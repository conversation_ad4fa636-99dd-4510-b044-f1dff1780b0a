@echo off
echo ========================================
echo        فحص متطلبات تشغيل ZET.IA
echo ========================================
echo.

echo فحص إصدار Windows...
ver
echo.

echo فحص الذاكرة المتاحة...
wmic OS get TotalVisibleMemorySize /value | find "TotalVisibleMemorySize"
echo.

echo فحص مساحة القرص...
dir "%~dp0" | find "bytes free"
echo.

echo فحص صلاحيات الكتابة...
echo test > "%~dp0test.tmp" 2>nul
if exist "%~dp0test.tmp" (
    echo ✅ صلاحيات الكتابة متاحة
    del "%~dp0test.tmp"
) else (
    echo ❌ لا توجد صلاحيات كتابة - شغل كمدير
)
echo.

echo فحص مجلد التطبيق...
if exist "%~dp0ZET.IA-Final\ZET.IA.exe" (
    echo ✅ ملف التطبيق موجود
) else (
    echo ❌ ملف التطبيق مفقود
)
echo.

echo ========================================
echo انتهى الفحص
echo ========================================
pause