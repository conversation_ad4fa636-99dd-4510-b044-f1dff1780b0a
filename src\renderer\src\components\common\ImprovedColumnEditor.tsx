import React, { useState, useCallback } from 'react'
import {
  <PERSON><PERSON>,
  Card,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Input,
  Select,
  Switch,
  InputNumber,
  Tooltip,
  Divider,
  Alert,
  Tag,
  List,
  Popconfirm,
  message,
  Tabs,
  Form
} from 'antd'
import {
  SettingOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  DragOutlined,
  InfoCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  CopyOutlined,
  ReloadOutlined
} from '@ant-design/icons'
// import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd' // مكتبة غير متوفرة

const { Title, Text } = Typography
const { Option } = Select
const { TabPane } = Tabs

export interface ColumnConfig {
  key: string
  title: string
  dataIndex: string
  type?: 'text' | 'number' | 'currency' | 'date' | 'boolean' | 'tag'
  visible: boolean
  width?: number | string
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  fixed?: 'left' | 'right' | false
  render?: any
}

interface ImprovedColumnEditorProps {
  visible: boolean
  onClose: () => void
  columns: ColumnConfig[]
  onColumnsChange: (columns: ColumnConfig[]) => void
  title?: string
}

const ImprovedColumnEditor: React.FC<ImprovedColumnEditorProps> = ({
  visible,
  onClose,
  columns,
  onColumnsChange,
  title = 'إدارة أعمدة الجدول'
}) => {
  const [activeTab, setActiveTab] = useState('manage')
  const [editingColumn, setEditingColumn] = useState<ColumnConfig | null>(null)
  const [newColumnForm] = Form.useForm()
  const [editColumnForm] = Form.useForm()

  // إضافة عمود جديد
  const handleAddColumn = useCallback((values: any) => {
    const newColumn: ColumnConfig = {
      key: values.key,
      title: values.title,
      dataIndex: values.dataIndex || values.key,
      type: values.type || 'text',
      visible: true,
      width: values.width || 'auto',
      align: values.align || 'right',
      sortable: values.sortable || false,
      filterable: values.filterable || false,
      fixed: values.fixed || false
    }

    // التحقق من عدم تكرار المفتاح
    if (columns.some(col => col.key === newColumn.key)) {
      message.error('مفتاح العمود موجود بالفعل')
      return
    }

    const updatedColumns = [...columns, newColumn]
    onColumnsChange(updatedColumns)
    newColumnForm.resetFields()
    message.success('تم إضافة العمود بنجاح')
  }, [columns, onColumnsChange, newColumnForm])

  // تحديث عمود موجود
  const handleUpdateColumn = useCallback((values: any) => {
    if (!editingColumn) return

    const updatedColumns = columns.map(col => 
      col.key === editingColumn.key 
        ? { ...col, ...values }
        : col
    )
    onColumnsChange(updatedColumns)
    setEditingColumn(null)
    editColumnForm.resetFields()
    message.success('تم تحديث العمود بنجاح')
  }, [columns, onColumnsChange, editingColumn, editColumnForm])

  // حذف عمود
  const handleDeleteColumn = useCallback((key: string) => {
    const updatedColumns = columns.filter(col => col.key !== key)
    onColumnsChange(updatedColumns)
    message.success('تم حذف العمود بنجاح')
  }, [columns, onColumnsChange])

  // تبديل رؤية العمود
  const handleToggleVisibility = useCallback((key: string) => {
    const updatedColumns = columns.map(col => 
      col.key === key ? { ...col, visible: !col.visible } : col
    )
    onColumnsChange(updatedColumns)
  }, [columns, onColumnsChange])

  // إعادة ترتيب الأعمدة
  const _handleDragEnd = useCallback((result: any) => {
    if (!result.destination) return

    const items = Array.from(columns)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    onColumnsChange(items)
    message.success('تم إعادة ترتيب الأعمدة')
  }, [columns, onColumnsChange])

  // نسخ إعدادات عمود
  const handleCopyColumn = useCallback((column: ColumnConfig) => {
    const newColumn = {
      ...column,
      key: `${column.key}_copy`,
      title: `${column.title} (نسخة)`
    }
    const updatedColumns = [...columns, newColumn]
    onColumnsChange(updatedColumns)
    message.success('تم نسخ العمود بنجاح')
  }, [columns, onColumnsChange])

  // إعادة تعيين الأعمدة
  const handleResetColumns = useCallback(() => {
    const resetColumns = columns.map(col => ({
      ...col,
      visible: true,
      width: 'auto',
      align: 'right' as const,
      sortable: false,
      filterable: false,
      fixed: false as const
    }))
    onColumnsChange(resetColumns)
    message.success('تم إعادة تعيين إعدادات الأعمدة')
  }, [columns, onColumnsChange])

  // بدء تعديل عمود
  const startEditColumn = useCallback((column: ColumnConfig) => {
    setEditingColumn(column)
    editColumnForm.setFieldsValue(column)
    setActiveTab('edit')
  }, [editColumnForm])

  // إحصائيات الأعمدة
  const columnStats = {
    total: columns.length,
    visible: columns.filter(col => col.visible).length,
    hidden: columns.filter(col => !col.visible).length,
    sortable: columns.filter(col => col.sortable).length,
    filterable: columns.filter(col => col.filterable).length
  }

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          {title}
          <Tag color="blue">{columnStats.total} عمود</Tag>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={900}
      footer={
        <Space>
          <Button onClick={onClose}>إغلاق</Button>
          <Popconfirm
            title="هل أنت متأكد من إعادة تعيين جميع الإعدادات؟"
            onConfirm={handleResetColumns}
            okText="نعم"
            cancelText="لا"
          >
            <Button icon={<ReloadOutlined />}>
              إعادة تعيين
            </Button>
          </Popconfirm>
        </Space>
      }
      destroyOnClose
    >
      {/* إحصائيات سريعة */}
      <Row gutter={[8, 8]} style={{ marginBottom: 16 }}>
        <Col span={4}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Text strong style={{ color: '#1890ff' }}>{columnStats.visible}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>مرئية</Text>
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Text strong style={{ color: '#ff4d4f' }}>{columnStats.hidden}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>مخفية</Text>
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Text strong style={{ color: '#52c41a' }}>{columnStats.sortable}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>قابلة للترتيب</Text>
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Text strong style={{ color: '#faad14' }}>{columnStats.filterable}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>قابلة للتصفية</Text>
          </Card>
        </Col>
      </Row>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {/* تبويب إدارة الأعمدة */}
        <TabPane 
          tab={
            <Space>
              <SettingOutlined />
              إدارة الأعمدة
            </Space>
          } 
          key="manage"
        >
          <Alert
            message="نصيحة"
            description="يمكنك سحب وإفلات الأعمدة لإعادة ترتيبها، أو النقر على الأيقونات للتحكم في الرؤية والإعدادات."
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          {/* تم تعطيل drag and drop مؤقتاً - مكتبة react-beautiful-dnd غير متوفرة */}
          <div>
            {columns.map((column, index) => (
              <Card
                key={column.key}
                size="small"
                style={{
                  marginBottom: 8,
                  border: column.visible ? '1px solid #d9d9d9' : '1px dashed #d9d9d9',
                  opacity: column.visible ? 1 : 0.6
                }}
              >
                <Row align="middle" gutter={[8, 0]}>
                  <Col flex="none">
                    <div>
                      <DragOutlined style={{ color: '#999', cursor: 'grab' }} />
                    </div>
                  </Col>
                  <Col flex="auto">
                    <Space direction="vertical" size={0}>
                      <Text strong>{column.title}</Text>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {column.key} • {column.type || 'text'}
                        {column.width && ` • عرض: ${column.width}`}
                      </Text>
                    </Space>
                  </Col>
                  <Col flex="none">
                    <Space>
                      <Tooltip title={column.visible ? 'إخفاء العمود' : 'إظهار العمود'}>
                        <Button
                          size="small"
                          type={column.visible ? 'default' : 'dashed'}
                          icon={column.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                          onClick={() => handleToggleVisibility(column.key)}
                        />
                      </Tooltip>
                      <Tooltip title="تعديل العمود">
                        <Button
                          size="small"
                          icon={<EditOutlined />}
                          onClick={() => startEditColumn(column)}
                        />
                      </Tooltip>
                      <Tooltip title="نسخ العمود">
                        <Button
                          size="small"
                          icon={<CopyOutlined />}
                          onClick={() => handleCopyColumn(column)}
                        />
                      </Tooltip>
                      <Tooltip title="حذف العمود">
                        <Popconfirm
                          title="هل أنت متأكد من حذف هذا العمود؟"
                          onConfirm={() => handleDeleteColumn(column.key)}
                          okText="نعم"
                          cancelText="لا"
                        >
                          <Button
                            size="small"
                            danger
                            icon={<DeleteOutlined />}
                          />
                        </Popconfirm>
                      </Tooltip>
                    </Space>
                  </Col>
                </Row>
              </Card>
            ))}
          </div>
        </TabPane>

        {/* تبويب إضافة عمود جديد */}
        <TabPane 
          tab={
            <Space>
              <PlusOutlined />
              إضافة عمود
            </Space>
          } 
          key="add"
        >
          <Form
            form={newColumnForm}
            layout="vertical"
            onFinish={handleAddColumn}
          >
            <Row gutter={[16, 0]}>
              <Col span={12}>
                <Form.Item
                  label="مفتاح العمود"
                  name="key"
                  rules={[{ required: true, message: 'مطلوب' }]}
                >
                  <Input placeholder="مثال: customer_name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="عنوان العمود"
                  name="title"
                  rules={[{ required: true, message: 'مطلوب' }]}
                >
                  <Input placeholder="مثال: اسم العميل" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item label="نوع البيانات" name="type">
                  <Select placeholder="اختر النوع">
                    <Option value="text">نص</Option>
                    <Option value="number">رقم</Option>
                    <Option value="currency">عملة</Option>
                    <Option value="date">تاريخ</Option>
                    <Option value="boolean">منطقي</Option>
                    <Option value="tag">علامة</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="العرض" name="width">
                  <InputNumber
                    placeholder="تلقائي"
                    min={50}
                    max={500}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="المحاذاة" name="align">
                  <Select placeholder="يمين">
                    <Option value="right">يمين</Option>
                    <Option value="center">وسط</Option>
                    <Option value="left">يسار</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 0]}>
              <Col span={8}>
                <Form.Item name="sortable" valuePropName="checked">
                  <Switch checkedChildren="قابل للترتيب" unCheckedChildren="غير قابل للترتيب" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="filterable" valuePropName="checked">
                  <Switch checkedChildren="قابل للتصفية" unCheckedChildren="غير قابل للتصفية" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="تثبيت" name="fixed">
                  <Select placeholder="بدون تثبيت" allowClear>
                    <Option value="left">يسار</Option>
                    <Option value="right">يمين</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item>
              <Button type="primary" htmlType="submit" icon={<PlusOutlined />}>
                إضافة العمود
              </Button>
            </Form.Item>
          </Form>
        </TabPane>

        {/* تبويب تعديل عمود */}
        {editingColumn && (
          <TabPane 
            tab={
              <Space>
                <EditOutlined />
                تعديل: {editingColumn.title}
              </Space>
            } 
            key="edit"
          >
            <Form
              form={editColumnForm}
              layout="vertical"
              onFinish={handleUpdateColumn}
              initialValues={editingColumn}
            >
              <Row gutter={[16, 0]}>
                <Col span={12}>
                  <Form.Item
                    label="عنوان العمود"
                    name="title"
                    rules={[{ required: true, message: 'مطلوب' }]}
                  >
                    <Input />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="نوع البيانات" name="type">
                    <Select>
                      <Option value="text">نص</Option>
                      <Option value="number">رقم</Option>
                      <Option value="currency">عملة</Option>
                      <Option value="date">تاريخ</Option>
                      <Option value="boolean">منطقي</Option>
                      <Option value="tag">علامة</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 0]}>
                <Col span={8}>
                  <Form.Item label="العرض" name="width">
                    <InputNumber
                      min={50}
                      max={500}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="المحاذاة" name="align">
                    <Select>
                      <Option value="right">يمين</Option>
                      <Option value="center">وسط</Option>
                      <Option value="left">يسار</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="visible" valuePropName="checked">
                    <Switch checkedChildren="مرئي" unCheckedChildren="مخفي" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 0]}>
                <Col span={8}>
                  <Form.Item name="sortable" valuePropName="checked">
                    <Switch checkedChildren="قابل للترتيب" unCheckedChildren="غير قابل للترتيب" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="filterable" valuePropName="checked">
                    <Switch checkedChildren="قابل للتصفية" unCheckedChildren="غير قابل للتصفية" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="تثبيت" name="fixed">
                    <Select placeholder="بدون تثبيت" allowClear>
                      <Option value="left">يسار</Option>
                      <Option value="right">يمين</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<CheckOutlined />}>
                    حفظ التغييرات
                  </Button>
                  <Button 
                    onClick={() => {
                      setEditingColumn(null)
                      setActiveTab('manage')
                    }}
                    icon={<CloseOutlined />}
                  >
                    إلغاء
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </TabPane>
        )}
      </Tabs>
    </Modal>
  )
}

export default ImprovedColumnEditor
