import React, { useState, useEffect, useMemo } from 'react'
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Tag,
  Tooltip,
  Select,
  DatePicker,
  Progress,
  Divider,
  Alert
} from 'antd'
import {
  CalendarOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import type { Dayjs } from 'dayjs'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

interface ProductionGanttChartProps {
  orders: any[]
  onBack?: () => void
}

interface GanttItem {
  id: number
  order_number: string
  product_name: string
  start_date: string
  end_date: string
  status: string
  priority: string
  progress: number
  department_name: string
  customer_name?: string
  estimated_hours: number
  actual_hours: number
}

interface TimelineSettings {
  viewMode: 'day' | 'week' | 'month'
  dateRange: [Dayjs, Dayjs]
  showWeekends: boolean
  showCompleted: boolean
}

const ProductionGanttChart: React.FC<ProductionGanttChartProps> = ({ orders, onBack }) => {
  const [settings, setSettings] = useState<TimelineSettings>({
    viewMode: 'week',
    dateRange: [dayjs().subtract(1, 'month'), dayjs().add(2, 'months')],
    showWeekends: true,
    showCompleted: true
  })

  const [ganttData, setGanttData] = useState<GanttItem[]>([])

  useEffect(() => {
    prepareGanttData()
  }, [orders, settings])

  const prepareGanttData = () => {
    const data: GanttItem[] = orders
      .filter(order => {
        // فلتر الأوامر المكتملة إذا كان مطلوباً
        if (!settings.showCompleted && order.status === 'completed') {
          return false
        }
        return true
      })
      .map(order => {
        // حساب نسبة التقدم
        let progress = 0
        switch (order.status) {
          case 'pending':
            progress = 0
            break
          case 'in_progress':
            progress = order.actual_hours > 0 && order.estimated_hours > 0 
              ? Math.min((order.actual_hours / order.estimated_hours) * 100, 90)
              : 25
            break
          case 'completed':
            progress = 100
            break
          case 'cancelled':
            progress = 0
            break
          case 'on_hold':
            progress = order.actual_hours > 0 && order.estimated_hours > 0 
              ? Math.min((order.actual_hours / order.estimated_hours) * 100, 50)
              : 10
            break
          default:
            progress = 0
        }

        return {
          id: order.id,
          order_number: order.order_number,
          product_name: order.item_name || order.product_name,
          start_date: order.start_date || order.order_date,
          end_date: order.expected_completion_date || dayjs(order.start_date || order.order_date).add(7, 'days').format('YYYY-MM-DD'),
          status: order.status,
          priority: order.priority,
          progress,
          department_name: order.department_name,
          customer_name: order.customer_name,
          estimated_hours: order.estimated_hours || 0,
          actual_hours: order.actual_hours || 0
        }
      })
      .sort((a, b) => {
        // ترتيب حسب الأولوية ثم التاريخ
        const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 }
        const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 1
        const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 1
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority
        }
        
        return dayjs(a.start_date).isBefore(dayjs(b.start_date)) ? -1 : 1
      })

    setGanttData(data)
  }

  // حساب الأيام في النطاق الزمني
  const timelineDays = useMemo(() => {
    const days: Dayjs[] = []
    let current = settings.dateRange[0].clone()
    const end = settings.dateRange[1]

    while (current.isBefore(end) || current.isSame(end, 'day')) {
      if (settings.showWeekends || (current.day() !== 0 && current.day() !== 6)) {
        days.push(current.clone())
      }
      current = current.add(1, 'day')
    }

    return days
  }, [settings.dateRange, settings.showWeekends])

  // حساب عرض الشريط
  const calculateBarWidth = (startDate: string, endDate: string) => {
    const start = dayjs(startDate)
    const end = dayjs(endDate)
    const duration = end.diff(start, 'day') + 1
    const totalDays = timelineDays.length
    return Math.max((duration / totalDays) * 100, 2) // حد أدنى 2%
  }

  // حساب موضع الشريط
  const calculateBarPosition = (startDate: string) => {
    const start = dayjs(startDate)
    const timelineStart = settings.dateRange[0]
    const daysPassed = start.diff(timelineStart, 'day')
    const totalDays = timelineDays.length
    return Math.max((daysPassed / totalDays) * 100, 0)
  }

  // ألوان الحالات
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#fa8c16'
      case 'in_progress': return '#1890ff'
      case 'completed': return '#52c41a'
      case 'cancelled': return '#f5222d'
      case 'on_hold': return '#722ed1'
      default: return '#d9d9d9'
    }
  }

  // ألوان الأولوية
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return '#ff4d4f'
      case 'high': return '#fa8c16'
      case 'normal': return '#1890ff'
      case 'low': return '#52c41a'
      default: return '#d9d9d9'
    }
  }

  const handleSettingsChange = (key: keyof TimelineSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Title level={2}>
            <CalendarOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
            الجدول الزمني لأوامر الإنتاج (Gantt Chart)
          </Title>
        </Col>
        {onBack && (
          <Col>
            <Button onClick={onBack}>العودة</Button>
          </Col>
        )}
      </Row>

      {/* إعدادات العرض */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Text strong>نطاق التاريخ:</Text>
            <RangePicker
              value={settings.dateRange}
              onChange={(dates) => dates && handleSettingsChange('dateRange', dates)}
              style={{ width: '100%', marginTop: '4px' }}
            />
          </Col>
          <Col span={4}>
            <Text strong>وضع العرض:</Text>
            <Select
              value={settings.viewMode}
              onChange={(value) => handleSettingsChange('viewMode', value)}
              style={{ width: '100%', marginTop: '4px' }}
            >
              <Option value="day">يومي</Option>
              <Option value="week">أسبوعي</Option>
              <Option value="month">شهري</Option>
            </Select>
          </Col>
          <Col span={6}>
            <Space>
              <Button
                size="small"
                icon={<ZoomInOutlined />}
                onClick={() => {
                  const range = settings.dateRange[1].diff(settings.dateRange[0], 'day')
                  const newRange = Math.max(range / 2, 7)
                  const center = settings.dateRange[0].add(range / 2, 'day')
                  handleSettingsChange('dateRange', [
                    center.subtract(newRange / 2, 'day'),
                    center.add(newRange / 2, 'day')
                  ])
                }}
              >
                تكبير
              </Button>
              <Button
                size="small"
                icon={<ZoomOutOutlined />}
                onClick={() => {
                  const range = settings.dateRange[1].diff(settings.dateRange[0], 'day')
                  const newRange = Math.min(range * 2, 365)
                  const center = settings.dateRange[0].add(range / 2, 'day')
                  handleSettingsChange('dateRange', [
                    center.subtract(newRange / 2, 'day'),
                    center.add(newRange / 2, 'day')
                  ])
                }}
              >
                تصغير
              </Button>
              <Button
                size="small"
                icon={<ReloadOutlined />}
                onClick={() => handleSettingsChange('dateRange', [
                  dayjs().subtract(1, 'month'),
                  dayjs().add(2, 'months')
                ])}
              >
                إعادة تعيين
              </Button>
            </Space>
          </Col>
          <Col span={8}>
            <Space>
              <Text>عرض عطل الأسبوع:</Text>
              <Button
                size="small"
                type={settings.showWeekends ? 'primary' : 'default'}
                onClick={() => handleSettingsChange('showWeekends', !settings.showWeekends)}
              >
                {settings.showWeekends ? 'مفعل' : 'معطل'}
              </Button>
              <Text>عرض المكتملة:</Text>
              <Button
                size="small"
                type={settings.showCompleted ? 'primary' : 'default'}
                onClick={() => handleSettingsChange('showCompleted', !settings.showCompleted)}
              >
                {settings.showCompleted ? 'مفعل' : 'معطل'}
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card size="small">
            <Text strong>إجمالي الأوامر: </Text>
            <Tag color="blue">{ganttData.length}</Tag>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Text strong>قيد التنفيذ: </Text>
            <Tag color="orange">{ganttData.filter(item => item.status === 'in_progress').length}</Tag>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Text strong>متأخرة: </Text>
            <Tag color="red">
              {ganttData.filter(item => 
                dayjs(item.end_date).isBefore(dayjs()) && item.status !== 'completed'
              ).length}
            </Tag>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Text strong>مكتملة: </Text>
            <Tag color="green">{ganttData.filter(item => item.status === 'completed').length}</Tag>
          </Card>
        </Col>
      </Row>

      {/* مخطط الجانت */}
      <Card title="المخطط الزمني">
        {ganttData.length === 0 ? (
          <Alert
            message="لا توجد أوامر إنتاج لعرضها"
            description="يرجى التأكد من وجود أوامر إنتاج في النطاق الزمني المحدد"
            type="info"
            showIcon
          />
        ) : (
          <div style={{ overflowX: 'auto', minHeight: '400px' }}>
            {/* رأس الجدول الزمني */}
            <div style={{ display: 'flex', marginBottom: '16px', borderBottom: '2px solid #f0f0f0', paddingBottom: '8px' }}>
              <div style={{ width: '300px', fontWeight: 'bold', padding: '8px' }}>
                أمر الإنتاج
              </div>
              <div style={{ flex: 1, position: 'relative', minWidth: '800px' }}>
                <div style={{ display: 'flex', height: '40px' }}>
                  {timelineDays.map((day, index) => (
                    <div
                      key={day.format('YYYY-MM-DD')}
                      style={{
                        flex: 1,
                        borderRight: '1px solid #f0f0f0',
                        padding: '4px',
                        fontSize: '12px',
                        textAlign: 'center',
                        backgroundColor: day.day() === 0 || day.day() === 6 ? '#fafafa' : 'white'
                      }}
                    >
                      <div style={{ fontWeight: 'bold' }}>
                        {day.format('DD')}
                      </div>
                      <div style={{ fontSize: '10px', color: '#666' }}>
                        {day.format('MMM')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* صفوف أوامر الإنتاج */}
            {ganttData.map((item, index) => (
              <div
                key={item.id}
                style={{
                  display: 'flex',
                  marginBottom: '8px',
                  borderBottom: '1px solid #f0f0f0',
                  paddingBottom: '8px'
                }}
              >
                {/* معلومات الأمر */}
                <div style={{ width: '300px', padding: '8px' }}>
                  <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                    {item.order_number}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
                    {item.product_name}
                  </div>
                  <Space size="small">
                    <Tag color={getPriorityColor(item.priority)}>
                      {item.priority}
                    </Tag>
                    <Tag color={getStatusColor(item.status)}>
                      {item.status}
                    </Tag>
                  </Space>
                  {item.customer_name && (
                    <div style={{ fontSize: '11px', color: '#999', marginTop: '2px' }}>
                      {item.customer_name}
                    </div>
                  )}
                </div>

                {/* شريط الجانت */}
                <div style={{ flex: 1, position: 'relative', minWidth: '800px', height: '60px' }}>
                  <div style={{ position: 'relative', height: '100%', paddingTop: '16px' }}>
                    <Tooltip
                      title={
                        <div>
                          <div><strong>{item.order_number}</strong></div>
                          <div>{item.product_name}</div>
                          <div>من: {dayjs(item.start_date).format('YYYY-MM-DD')}</div>
                          <div>إلى: {dayjs(item.end_date).format('YYYY-MM-DD')}</div>
                          <div>التقدم: {item.progress}%</div>
                          <div>القسم: {item.department_name}</div>
                          {item.customer_name && <div>العميل: {item.customer_name}</div>}
                        </div>
                      }
                    >
                      <div
                        style={{
                          position: 'absolute',
                          left: `${calculateBarPosition(item.start_date)}%`,
                          width: `${calculateBarWidth(item.start_date, item.end_date)}%`,
                          height: '24px',
                          backgroundColor: getStatusColor(item.status),
                          borderRadius: '4px',
                          border: `2px solid ${getPriorityColor(item.priority)}`,
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          fontSize: '11px',
                          fontWeight: 'bold',
                          minWidth: '40px'
                        }}
                      >
                        {item.progress}%
                      </div>
                    </Tooltip>
                    
                    {/* خط التقدم */}
                    {item.progress > 0 && item.progress < 100 && (
                      <div
                        style={{
                          position: 'absolute',
                          left: `${calculateBarPosition(item.start_date)}%`,
                          width: `${(calculateBarWidth(item.start_date, item.end_date) * item.progress) / 100}%`,
                          height: '24px',
                          backgroundColor: '#52c41a',
                          borderRadius: '4px 0 0 4px',
                          opacity: 0.8
                        }}
                      />
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  )
}

export default ProductionGanttChart
