import React, { useState, useEffect } from 'react'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  Card, Form, Input, Switch, Button, Alert, Radio,
  Space, Badge, Typography, Divider, Tooltip, Tag, App, Modal, Tabs
} from 'antd'
import {
  SyncOutlined, FolderOpenOutlined, CheckCircleOutlined,
  WarningOutlined, InfoCircleOutlined, ReloadOutlined,
  WifiOutlined, BugOutlined, SettingOutlined
} from '@ant-design/icons'
import DeviceLinkingDiagnostics from './DeviceLinkingDiagnostics'

const { Text } = Typography
const { TabPane } = Tabs

interface SyncStatus {
  enabled: boolean
  deviceRole: string
  isActive: boolean
  lastSync: Date
  sharedFolder: string
  isConnected: boolean
  connectedDevices?: any[]
}

export const SimpleSyncSettings: React.FC = () => {
  const { message } = App.useApp()
  const [form] = Form.useForm()
  const [syncEnabled, setSyncEnabled] = useState(false)
  const [deviceRole, setDeviceRole] = useState<'main' | 'branch'>('branch')
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null)
  const [testing, setTesting] = useState(false)
  const [syncing, setSyncing] = useState(false)
  const [sharedFolderPath, setSharedFolderPath] = useState('')
  const [creatingSmartFolder, setCreatingSmartFolder] = useState(false)
  const [networkInfo, setNetworkInfo] = useState<any>(null)
  const [connectedDevices, setConnectedDevices] = useState<{ ip: string; hostname?: string; mac?: string; isReachable: boolean; hasSharedFolder?: boolean }[]>([])
  const [searchingDevices, setSearchingDevices] = useState(false)
  const [lastSearchTime, setLastSearchTime] = useState<Date | null>(null)
  const [searchError, setSearchError] = useState<string | null>(null)

  useEffect(() => {
    loadSyncSettings()
    loadNetworkInfo()
    const interval = setInterval(updateSyncStatus, 10000) // كل 10 ثواني

    // معالج لإعادة تحميل الإعدادات عند تبديل قاعدة البيانات
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _handleDatabaseSwitch = () => {
      setTimeout(() => {
        loadSyncSettings()
        updateSyncStatus()
      }, 2000) // انتظار قليل للتأكد من اكتمال التبديل
    }

    // الاستماع لأحداث تبديل قاعدة البيانات (معطل مؤقتاً)
    // window.electronAPI?.onDatabaseSwitched?.(handleDatabaseSwitch)

    return () => {
      clearInterval(interval)
      // إزالة معالج الأحداث (معطل مؤقتاً)
      // window.electronAPI?.removeDatabaseSwitchListener?.(handleDatabaseSwitch)
    }
  }, [])

  const loadSyncSettings = async () => {
    try {
      const settings = await window.electronAPI.getSyncSettings()
      if (settings.success) {
        const data = settings.data
        form.setFieldsValue(data)
        setSyncEnabled(data.enabled || false)
        setDeviceRole(data.deviceRole || 'branch')
        setSharedFolderPath(data.sharedFolder || '')
      }
    } catch (error) {
      Logger.error('SimpleSyncSettings', 'خطأ في تحميل إعدادات المزامنة:', error)
    }
  }

  const updateSyncStatus = async () => {
    try {
      const status = await window.electronAPI.getSyncStatus()
      setSyncStatus(status)
    } catch (error) {
      Logger.error('SimpleSyncSettings', 'خطأ في تحديث حالة المزامنة:', error)
    }
  }

  const handleSave = async (values: any) => {
    try {
      const settings = {
        enabled: syncEnabled,
        deviceRole: deviceRole,
        sharedFolder: values.sharedFolder,
        syncInterval: parseInt(values.syncInterval) || 5
      }

      const result = await window.electronAPI.updateSyncSettings(settings)
      if (result.success) {
        message.success('تم حفظ إعدادات المزامنة بنجاح')

        // إعادة تحميل الإعدادات للتأكد من حفظها بشكل صحيح
        setTimeout(async () => {
          await loadSyncSettings()
          updateSyncStatus()
        }, 1000)
      } else {
        message.error(result.message || 'خطأ في حفظ الإعدادات')
      }
    } catch (error) {
      Logger.error('SimpleSyncSettings', 'خطأ في حفظ الإعدادات:', error)
      message.error('خطأ في حفظ الإعدادات')
    }
  }

  const testConnection = async () => {
    setTesting(true)
    try {
      const values = form.getFieldsValue()
      if (!values.sharedFolder) {
        message.error('يرجى إدخال مسار المجلد المشترك أولاً')
        return
      }

      const result = await window.electronAPI.testSyncConnection(values.sharedFolder)
      if (result.success) {
        message.success('✅ يمكن الوصول للمجلد المشترك بنجاح!')
      } else {
        message.error(`❌ ${result.message}`)
      }
    } catch (error) {
      Logger.error('SimpleSyncSettings', 'خطأ في اختبار الاتصال:', error)
      message.error('خطأ في اختبار الاتصال')
    } finally {
      setTesting(false)
    }
  }

  const forceSync = async () => {
    setSyncing(true)
    try {
      const result = await window.electronAPI.forceSyncNow()
      if (result.success) {
        message.success('تم تنفيذ المزامنة بنجاح')
        updateSyncStatus()
      } else {
        message.error(result.message || 'خطأ في المزامنة')
      }
    } catch {
      message.error('خطأ في تنفيذ المزامنة')
    } finally {
      setSyncing(false)
    }
  }

  const selectFolder = async () => {
    try {
      message.loading('جاري فتح نافذة اختيار المجلد...', 1)

      const result = await window.electronAPI.selectFolder()

      if (result.success && result.path) {
        // تحديث القيمة في النموذج والحالة
        form.setFieldValue('sharedFolder', result.path)
        setSharedFolderPath(result.path)

        // إجبار إعادة رسم المكون
        form.validateFields(['sharedFolder'])

        message.success(`تم اختيار المجلد: ${result.path}`)

        // اختبار الاتصال تلقائياً بعد الاختيار
        setTimeout(() => {
          testConnection()
        }, 500)
      } else if (result.message) {
        message.warning(result.message)
      } else {
        message.info('لم يتم اختيار أي مجلد')
      }
    } catch (error) {
      console.error('خطأ في اختيار المجلد:', error)
      message.error('خطأ في اختيار المجلد')
    }
  }

  const loadNetworkInfo = async () => {
    try {
      const result = await window.electronAPI.getNetworkInfo()
      if (result.success) {
        setNetworkInfo(result.data)
      }
    } catch (error) {
      Logger.error('SimpleSyncSettings', 'خطأ في تحميل معلومات الشبكة:', error)
    }
  }

  const createSmartSharedFolder = async () => {
    setCreatingSmartFolder(true)
    try {
      // التحقق من وجود مجلد مشترك مسبقاً
      if (sharedFolderPath) {
        const confirmed = await new Promise((resolve) => {
          Modal.confirm({
            title: '⚠️ تحذير: مجلد مشترك موجود',
            content: (
              <div>
                <p>يوجد مجلد مشترك محدد مسبقاً:</p>
                <p style={{ fontWeight: 'bold', color: '#1890ff' }}>{sharedFolderPath}</p>
                <p style={{ color: '#ff4d4f' }}>
                  إنشاء مجلد جديد قد يؤدي إلى استبدال قاعدة البيانات المشتركة الحالية.
                </p>
                <p>هل تريد المتابعة؟</p>
              </div>
            ),
            okText: 'نعم، أنشئ مجلد جديد',
            cancelText: 'إلغاء',
            okType: 'danger',
            onOk: () => resolve(true),
            onCancel: () => resolve(false)
          })
        })

        if (!confirmed) {
          setCreatingSmartFolder(false)
          return
        }
      }

      message.loading('جاري إنشاء المجلد المشترك الذكي...', 0)

      const result = await window.electronAPI.createSmartSharedFolder()
      message.destroy()

      if (result.success && result.path) {
        form.setFieldValue('sharedFolder', result.path)
        setSharedFolderPath(result.path)
        message.success(`✅ ${result.message}`)

        // اختبار الاتصال تلقائياً
        setTimeout(() => {
          testConnection()
        }, 1000)
      } else {
        message.error(`❌ ${result.message}`)
      }
    } catch (error) {
      message.destroy()
      Logger.error('SimpleSyncSettings', 'خطأ في إنشاء المجلد المشترك الذكي:', error)
      message.error('خطأ في إنشاء المجلد المشترك الذكي')
    } finally {
      setCreatingSmartFolder(false)
    }
  }

  const discoverDevices = async () => {
    setSearchingDevices(true)
    setSearchError(null)

    try {
      // رسالة تحميل محسنة
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const _loadingMessage = message.loading('🔍 البحث السريع عن الأجهزة المتصلة...', 0)

      const result = await window.electronAPI.discoverConnectedDevices()
      message.destroy()

      if (result.success) {
        const reachableDevices = result.devices.filter(device => device.isReachable)
        setConnectedDevices(reachableDevices)
        setLastSearchTime(new Date())

        if (reachableDevices.length > 0) {
          const devicesWithSharedFolder = reachableDevices.filter(device => (device as any).hasSharedFolder)

          if (devicesWithSharedFolder.length > 0) {
            message.success({
              content: `✅ تم العثور على ${reachableDevices.length} جهاز متاح (${devicesWithSharedFolder.length} يحتوي على قاعدة بيانات مشتركة)`,
              duration: 4
            })
          } else {
            message.warning({
              content: `⚠️ تم العثور على ${reachableDevices.length} جهاز لكن لا يحتوي أي منها على قاعدة بيانات مشتركة`,
              duration: 5
            })
          }
        } else {
          message.warning({
            content: '⚠️ لم يتم العثور على أجهزة متاحة في الشبكة المحلية',
            duration: 4
          })
        }
      } else {
        const errorMsg = result.message || 'فشل في البحث عن الأجهزة'
        setSearchError(errorMsg)
        message.error(`❌ ${errorMsg}`)
      }
    } catch (error: any) {
      message.destroy()
      const errorMsg = error?.message || 'خطأ غير متوقع في البحث عن الأجهزة'
      setSearchError(errorMsg)
      message.error('❌ خطأ في البحث عن الأجهزة')
      Logger.error('SimpleSyncSettings', 'خطأ في البحث عن الأجهزة:', error)
    } finally {
      setSearchingDevices(false)
    }
  }

  const selectDevice = async (device: { ip: string; hostname?: string; mac?: string; hasSharedFolder?: boolean }) => {
    try {
      const deviceName = device.hostname || device.ip
      message.loading(`جاري الاتصال بالجهاز ${deviceName}...`, 0)

      const result = await window.electronAPI.selectDeviceForSync(device)
      message.destroy()

      if (result.success && result.path) {
        form.setFieldValue('sharedFolder', result.path)
        setSharedFolderPath(result.path)

        message.success({
          content: `✅ ${result.message}`,
          duration: 4
        })

        // اختبار الاتصال تلقائياً
        setTimeout(() => {
          testConnection()
        }, 1000)
      } else {
        const errorMsg = result.message || `فشل في الاتصال بالجهاز ${deviceName}`
        message.error({
          content: `❌ ${errorMsg}`,
          duration: 5
        })
      }
    } catch (error: any) {
      message.destroy()
      const errorMsg = error?.message || 'خطأ غير متوقع في الاتصال بالجهاز'
      Logger.error('SimpleSyncSettings', 'خطأ في الاتصال بالجهاز:', error)
      message.error({
        content: `❌ ${errorMsg}`,
        duration: 5
      })
    }
  }

  const autoDetectDeviceRole = async () => {
    try {
      message.loading('🤖 جاري تحديد دور الجهاز تلقائياً...', 0)

      const result = await window.electronAPI.determineDeviceRole()
      message.destroy()

      if (result.success) {
        // تحويل من host/client إلى main/branch
        const newRole = result.role === 'host' ? 'main' : 'branch'
        setDeviceRole(newRole)
        form.setFieldValue('deviceRole', newRole)

        message.success({
          content: `✅ ${result.message}`,
          duration: 4
        })
      } else {
        message.error(`❌ ${result.message}`)
      }
    } catch (error) {
      message.destroy()
      message.error('❌ خطأ في تحديد دور الجهاز')
    }
  }

  const getStatusBadge = () => {
    if (!syncEnabled) {
      return <Badge status="default" text="معطل" />
    }

    if (!syncStatus) {
      return <Badge status="processing" text="جاري التحميل..." />
    }

    if (!syncStatus.isConnected) {
      return <Badge status="error" text="لا يمكن الوصول للمجلد المشترك" />
    }

    if (syncStatus.isActive) {
      return <Badge status="success" text="نشط ومتصل" />
    }

    return <Badge status="warning" text="متوقف" />
  }

  const getLastSyncText = () => {
    if (!syncStatus || !syncStatus.lastSync) {
      return 'لم تتم مزامنة بعد'
    }

    const lastSyncDate = new Date(syncStatus.lastSync)

    // التحقق من صحة التاريخ
    if (isNaN(lastSyncDate.getTime()) || lastSyncDate.getFullYear() < 2020) {
      return 'لم تتم مزامنة بعد'
    }

    const now = new Date()
    const diffMs = now.getTime() - lastSyncDate.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMinutes / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMinutes < 1) {
      return 'منذ أقل من دقيقة'
    } else if (diffMinutes < 60) {
      return `منذ ${diffMinutes} دقيقة`
    } else if (diffHours < 24) {
      return `منذ ${diffHours} ساعة`
    } else if (diffDays < 7) {
      return `منذ ${diffDays} يوم`
    } else {
      return lastSyncDate.toLocaleDateString('ar')
    }
  }

  return (
    <Card
      title={
        <Space>
          <SyncOutlined />
          <span>🔄 ربط الأجهزة</span>
          {getStatusBadge()}
        </Space>
      }
      extra={
        syncEnabled && syncStatus?.isConnected && (
          <Button
            icon={<SyncOutlined spin={syncing} />}
            onClick={forceSync}
            loading={syncing}
            type="primary"
            ghost
            size="small"
          >
            مزامنة الآن
          </Button>
        )
      }
    >
      <Tabs defaultActiveKey="settings" type="card">
        <TabPane
          tab={
            <Space>
              <SettingOutlined />
              <span>الإعدادات</span>
            </Space>
          }
          key="settings"
        >
      {/* شرح مبسط */}
      <Alert
        message="كيفية ربط جهازين"
        description={
          <div>
            <p><strong>1.</strong> أنشئ مجلد مشترك على الشبكة (مثل: مجلد على جهاز آخر أو NAS)</p>
            <p><strong>2.</strong> اختر دور الجهاز: <strong>فرعي</strong> (أكثر إدخالاً) أو <strong>مركزي</strong> (مراقبة + إدخال)</p>
            <p><strong>3.</strong> فعّل المزامنة وستتم تلقائياً كل بضع دقائق</p>
            <p><strong>ملاحّة:</strong> كلا الجهازين يمكنهما الإدخال والتعديل، والمزامنة ذكية حسب آخر تحديث</p>
          </div>
        }
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* حالة المزامنة */}
      {syncStatus && syncEnabled && (
        <Alert
          message={
            <Space>
              {syncStatus.isConnected ? <CheckCircleOutlined /> : <WarningOutlined />}
              <span>
                {syncStatus.isConnected ? 'المزامنة تعمل بنجاح' : 'لا يمكن الوصول للمجلد المشترك'}
              </span>
              {syncStatus.lastSync && (
                <Text type="secondary">
                  آخر مزامنة: {new Date(syncStatus.lastSync).toLocaleString('ar')}
                </Text>
              )}
            </Space>
          }
          type={syncStatus.isConnected ? 'success' : 'warning'}
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Form form={form} layout="vertical" onFinish={handleSave}>
        {/* تفعيل المزامنة */}
        <Form.Item>
          <Space>
            <Switch 
              checked={syncEnabled}
              onChange={setSyncEnabled}
              size="default"
            />
            <Text strong>تفعيل ربط الأجهزة</Text>
            <Tooltip title="عند التفعيل، ستتم مزامنة البيانات تلقائياً مع الأجهزة الأخرى">
              <InfoCircleOutlined style={{ color: '#1890ff' }} />
            </Tooltip>
          </Space>
        </Form.Item>

        {syncEnabled && (
          <>
            <Divider />
            
            {/* دور الجهاز */}
            <Form.Item label="دور هذا الجهاز">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Radio.Group
                  value={deviceRole}
                  onChange={(e) => setDeviceRole(e.target.value)}
                  size="large"
                  style={{ width: '100%' }}
                >
                  <Radio.Button value="branch" style={{ width: '48%' }}>
                    <Space>
                      📱 فرعي (عميل)
                    </Space>
                  </Radio.Button>
                  <Radio.Button value="main" style={{ width: '48%' }}>
                    <Space>
                      🏠 رئيسي (مضيف)
                    </Space>
                  </Radio.Button>
                </Radio.Group>

                <Button
                  icon={<span>🤖</span>}
                  onClick={autoDetectDeviceRole}
                  size="small"
                  type="dashed"
                  style={{ width: '100%' }}
                >
                  تحديد تلقائي للدور
                </Button>

                <div style={{ color: '#666', fontSize: '12px' }}>
                  <Text type="secondary">
                    {deviceRole === 'branch'
                      ? '📱 جهاز فرعي: يبحث عن الجهاز الرئيسي ويتصل به (مناسب للكاشير والفروع)'
                      : '🏠 جهاز رئيسي: ينشئ مجلد مشترك ويشاركه مع الأجهزة الأخرى (مناسب للخادم الرئيسي)'
                    }
                  </Text>
                </div>
              </Space>
            </Form.Item>

            {/* مسار المجلد المشترك */}
            <Form.Item
              name="sharedFolder"
              label={deviceRole === 'branch' ? "البحث عن الجهاز الرئيسي" : "مسار المجلد المشترك"}
              rules={[{ required: true, message: deviceRole === 'branch' ? 'يرجى البحث عن الجهاز الرئيسي والاتصال به' : 'يرجى إدخال مسار المجلد المشترك' }]}
            >
              {deviceRole === 'branch' ? (
                // واجهة الجهاز الفرعي - البحث عن الأجهزة
                <div>
                  <Alert
                    message="🔍 ابحث عن الجهاز الرئيسي"
                    description="هذا جهاز فرعي. ابحث عن الجهاز الرئيسي في الشبكة واتصل به."
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />
                  <Space.Compact style={{ width: '100%' }}>
                    <Input
                      style={{ width: 'calc(100% - 120px)' }}
                      placeholder="سيتم ملء المسار تلقائياً عند الاتصال بالجهاز الرئيسي"
                      dir="ltr"
                      value={sharedFolderPath}
                      onChange={(e) => {
                        setSharedFolderPath(e.target.value)
                        form.setFieldValue('sharedFolder', e.target.value)
                      }}
                      disabled={!sharedFolderPath}
                    />
                    <Button
                      icon={<FolderOpenOutlined />}
                      onClick={selectFolder}
                      style={{ width: 120 }}
                      type="default"
                    >
                      اختيار مجلد
                    </Button>
                  </Space.Compact>
                </div>
              ) : (
                // واجهة الجهاز الرئيسي - إنشاء أو اختيار مجلد
                <Space.Compact style={{ width: '100%' }}>
                  <Input
                    style={{ width: 'calc(100% - 240px)' }}
                    placeholder="\\الجهاز-الآخر\مجلد-مشترك أو C:\مجلد-مشترك"
                    dir="ltr"
                    value={sharedFolderPath}
                    onChange={(e) => {
                      setSharedFolderPath(e.target.value)
                      form.setFieldValue('sharedFolder', e.target.value)
                    }}
                  />
                  <Button
                    icon={<FolderOpenOutlined />}
                    onClick={selectFolder}
                    style={{ width: 120 }}
                    type="default"
                  >
                    اختيار مجلد
                  </Button>
                  <Button
                    icon={<span>🤖</span>}
                    onClick={createSmartSharedFolder}
                    loading={creatingSmartFolder}
                    style={{ width: 120 }}
                    type="primary"
                    ghost
                  >
                    إنشاء ذكي
                  </Button>
                </Space.Compact>
              )}
            </Form.Item>

            {/* معلومات الشبكة */}
            {networkInfo && (
              <Card
                size="small"
                title="🌐 معلومات الشبكة"
                style={{ marginBottom: 16 }}
                extra={
                  <Space>
                    <Button
                      size="small"
                      onClick={discoverDevices}
                      loading={searchingDevices}
                      icon={searchingDevices ? <ReloadOutlined spin /> : <WifiOutlined />}
                      type={connectedDevices.length > 0 ? "primary" : "default"}
                    >
                      {searchingDevices ? 'جاري البحث...' : 'البحث عن أجهزة'}
                    </Button>
                    {lastSearchTime && (
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        آخر بحث: {lastSearchTime.toLocaleTimeString('ar')}
                      </Text>
                    )}
                  </Space>
                }
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text>
                      <strong>اسم الجهاز:</strong> {networkInfo.hostname}
                    </Text>
                    <Tag color="blue" icon={<span>💻</span>}>
                      {deviceRole === 'main' ? 'رئيسي' : 'فرعي'}
                    </Tag>
                  </div>
                  <Text>
                    <strong>عنوان IP:</strong> {networkInfo.ip}
                  </Text>

                  {/* رسالة خطأ البحث */}
                  {searchError && (
                    <Alert
                      message="خطأ في البحث عن الأجهزة"
                      description={searchError}
                      type="error"
                      showIcon
                      closable
                      onClose={() => setSearchError(null)}
                      style={{ marginTop: 8 }}
                      action={
                        <Button size="small" onClick={discoverDevices} type="primary" ghost>
                          إعادة المحاولة
                        </Button>
                      }
                    />
                  )}
                  {connectedDevices.length > 0 && (
                    <div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                        <Text strong>الأجهزة المكتشفة ({connectedDevices.length}):</Text>
                        <div>
                          {connectedDevices.filter(d => d.hasSharedFolder).length > 0 && (
                            <Tag color="green">
                              {connectedDevices.filter(d => (d as any).hasSharedFolder).length} رئيسي
                            </Tag>
                          )}
                          {connectedDevices.filter(d => !(d as any).hasSharedFolder).length > 0 && (
                            <Tag color="blue">
                              {connectedDevices.filter(d => !(d as any).hasSharedFolder).length} عادي
                            </Tag>
                          )}
                        </div>
                      </div>
                      <div style={{ marginTop: 8, maxHeight: '200px', overflowY: 'auto' }}>
                        {connectedDevices.map((device, index) => {
                          const isMainDevice = device.hasSharedFolder
                          const cardStyle = {
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            padding: '12px 16px',
                            margin: '6px 0',
                            border: isMainDevice ? '2px solid #52c41a' : '1px solid #d9d9d9',
                            borderRadius: '8px',
                            backgroundColor: isMainDevice ? '#f6ffed' : '#fafafa',
                            boxShadow: isMainDevice ? '0 2px 8px rgba(82, 196, 26, 0.15)' : 'none',
                            transition: 'all 0.3s ease'
                          }

                          return (
                            <div key={index} style={cardStyle}>
                              <div style={{ flex: 1 }}>
                                <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 4 }}>
                                  <span style={{ fontSize: '16px' }}>
                                    {isMainDevice ? '🏠' : '💻'}
                                  </span>
                                  <Text strong style={{ color: isMainDevice ? '#52c41a' : 'inherit' }}>
                                    {device.hostname || 'جهاز غير معروف'}
                                  </Text>
                                  {isMainDevice && (
                                    <Tag color="green">جهاز رئيسي</Tag>
                                  )}
                                </div>
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                  📡 {device.ip}
                                  {device.mac && (
                                    <>
                                      <br />
                                      🔗 {device.mac}
                                    </>
                                  )}
                                  {device.hasSharedFolder && (
                                    <>
                                      <br />
                                      <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>
                                        ✅ يحتوي على قاعدة بيانات مشتركة
                                      </Text>
                                    </>
                                  )}
                                </Text>
                              </div>
                              <Button
                                size="small"
                                type={isMainDevice ? "primary" : "default"}
                                ghost={!isMainDevice}
                                onClick={() => selectDevice(device)}
                                icon={<span>{isMainDevice ? '🔗' : '📡'}</span>}
                                style={{
                                  borderColor: isMainDevice ? '#52c41a' : undefined,
                                  color: isMainDevice ? '#52c41a' : undefined
                                }}
                              >
                                {isMainDevice ? 'اتصال' : 'فحص'}
                              </Button>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )}

                  {/* رسالة تحذيرية للجهاز الفرعي عند عدم العثور على أجهزة رئيسية */}
                  {deviceRole === 'branch' && connectedDevices.length > 0 && !connectedDevices.some(device => device.hasSharedFolder) && (
                    <Alert
                      message="⚠️ لم يتم العثور على أجهزة رئيسية"
                      description="لم يتم العثور على أي جهاز رئيسي يحتوي على قاعدة بيانات مشتركة. تأكد من أن الجهاز الرئيسي متصل بالشبكة وأنه تم إنشاء المجلد المشترك عليه."
                      type="warning"
                      showIcon
                      style={{ marginTop: 16 }}
                      action={
                        <Button size="small" onClick={discoverDevices} loading={searchingDevices}>
                          إعادة البحث
                        </Button>
                      }
                    />
                  )}

                  {/* رسالة عدم وجود أجهزة للجهاز الفرعي */}
                  {deviceRole === 'branch' && connectedDevices.length === 0 && !searchingDevices && (
                    <Alert
                      message="🔍 لم يتم العثور على أجهزة"
                      description="لم يتم العثور على أي أجهزة في الشبكة. تأكد من أن الجهاز الرئيسي متصل بنفس الشبكة وجرب البحث مرة أخرى."
                      type="info"
                      showIcon
                      style={{ marginTop: 16 }}
                      action={
                        <Button size="small" onClick={discoverDevices} type="primary">
                          بحث الآن
                        </Button>
                      }
                    />
                  )}
                </Space>
              </Card>
            )}

            {/* فترة المزامنة */}
            <Form.Item 
              name="syncInterval" 
              label="فترة المزامنة (بالدقائق)"
              initialValue={5}
            >
              <Input 
                type="number" 
                min={1} 
                max={60}
                placeholder="5"
                addonAfter="دقيقة"
              />
            </Form.Item>

            {/* أزرار التحكم */}
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit" size="large">
                  حفظ الإعدادات
                </Button>
                
                <Button
                  onClick={testConnection}
                  loading={testing}
                  size="large"
                >
                  اختبار الاتصال
                </Button>

                {syncEnabled && (
                  <Button
                    onClick={forceSync}
                    loading={syncing}
                    size="large"
                    icon={<SyncOutlined />}
                  >
                    مزامنة فورية
                  </Button>
                )}
              </Space>
            </Form.Item>
          </>
        )}
      </Form>

      {/* معلومات المزامنة */}
      {syncEnabled && syncStatus && (
        <Card
          size="small"
          title="📊 معلومات المزامنة"
          style={{ marginTop: 16 }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text>الحالة:</Text>
              {getStatusBadge()}
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text>آخر مزامنة:</Text>
              <Text type="secondary">{getLastSyncText()}</Text>
            </div>
            {syncStatus.sharedFolder && (
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>المجلد المشترك:</Text>
                <Text type="secondary" style={{ fontSize: '12px', maxWidth: '200px', wordBreak: 'break-all' }}>
                  {syncStatus.sharedFolder}
                </Text>
              </div>
            )}
          </Space>
        </Card>
      )}

      {/* قائمة الأجهزة المتصلة */}
      {syncEnabled && syncStatus?.connectedDevices && syncStatus.connectedDevices.length > 0 && (
        <Card
          title="🖥️ الأجهزة المتصلة"
          size="small"
          style={{ marginTop: 16 }}
        >
          {syncStatus.connectedDevices.map((device: any, index: number) => (
            <div key={device.id} style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '8px 0',
              borderBottom: index < syncStatus.connectedDevices.length - 1 ? '1px solid #f0f0f0' : 'none'
            }}>
              <div>
                <Text strong>{device.name}</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {device.role === 'main' ? '📊 مركزي' : '📝 فرعي'} •
                  آخر مزامنة: {new Date(device.lastSync).toLocaleString('ar')}
                </Text>
              </div>
              <Badge
                status="success"
                text="متصل"
                style={{ fontSize: '12px' }}
              />
            </div>
          ))}
        </Card>
      )}
        </TabPane>

        <TabPane
          tab={
            <Space>
              <BugOutlined />
              <span>التشخيص</span>
            </Space>
          }
          key="diagnostics"
        >
          <DeviceLinkingDiagnostics />
        </TabPane>
      </Tabs>
    </Card>
  )
}

export default SimpleSyncSettings
