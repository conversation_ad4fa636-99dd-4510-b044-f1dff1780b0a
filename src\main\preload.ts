import { contextBridge, ipc<PERSON>ender<PERSON> } from 'electron'

// تعريف API للواجهة الأمامية
const electronAPI = {
  // المصادقة والأمان
  login: (username: string, password: string) => ipcRenderer.invoke('login', username, password),
  logout: (token: string) => ipcRenderer.invoke('logout', token),
  verifySession: (token: string) => ipcRenderer.invoke('verify-session', token),
  getCurrentUserInfo: () => ipcRenderer.invoke('get-current-user'),
  getActiveUsers: () => ipcRenderer.invoke('get-active-users'),
  getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
  getRecentLoginAttempts: () => ipcRenderer.invoke('get-recent-login-attempts'),
  resetPasswordRequest: (email: string) => ipc<PERSON>enderer.invoke('reset-password-request', email),

  // المستخدمين
  getUsers: () => ipc<PERSON>enderer.invoke('get-users'),
  createUser: (userData: any) => ipc<PERSON><PERSON>er.invoke('create-user', userData),
  updateUser: (userId: number, userData: any) => ipcRenderer.invoke('update-user', userId, userData),
  deleteUser: (userId: number) => ipcRenderer.invoke('delete-user', userId),
  getUserActivities: (userId: number) => ipcRenderer.invoke('get-user-activities', userId),
  getAllUserActivities: (filters?: any) => ipcRenderer.invoke('get-all-user-activities', filters),
  cleanupOldActivities: (daysToKeep?: number) => ipcRenderer.invoke('cleanup-old-activities', daysToKeep),
  resetUserPassword: (userId: number) => ipcRenderer.invoke('reset-user-password', userId),
  toggleUserStatus: (userId: number, isActive: boolean) => ipcRenderer.invoke('toggle-user-status', userId, isActive),
  generateUserCode: () => ipcRenderer.invoke('generate-user-code'),
  exportUsers: (format: 'excel' | 'csv') => ipcRenderer.invoke('export-users', format),
  importUsers: (usersData: any[]) => ipcRenderer.invoke('import-users', usersData),
  copyUserPermissions: (sourceUserId: number, targetUserIds: number[]) => ipcRenderer.invoke('copy-user-permissions', sourceUserId, targetUserIds),

  // الأدوار والصلاحيات
  getRoles: () => ipcRenderer.invoke('get-roles'),
  getRolesWithUsers: () => ipcRenderer.invoke('get-roles-with-users'),
  getPermissions: () => ipcRenderer.invoke('get-permissions'),
  getUserPermissions: (userId: number) => ipcRenderer.invoke('get-user-permissions', userId),
  createRole: (roleData: any) => ipcRenderer.invoke('create-role', roleData),
  updateRole: (roleId: number, roleData: any) => ipcRenderer.invoke('update-role', roleId, roleData),
  deleteRole: (roleId: number) => ipcRenderer.invoke('delete-role', roleId),

  // الإعدادات
  getSettings: () => ipcRenderer.invoke('get-settings'),
  updateSetting: (key: string, value: string) => ipcRenderer.invoke('update-setting', key, value),
  updateSettings: (settings: any) => ipcRenderer.invoke('update-settings', settings),
  getSetting: (key: string) => ipcRenderer.invoke('get-setting', key),

  // رفع وحفظ الشعار
  uploadCompanyLogo: () => ipcRenderer.invoke('upload-company-logo'),
  getCompanyLogo: () => ipcRenderer.invoke('get-company-logo'),

  // قراءة الملفات كـ base64 للطباعة
  readFileAsBase64: (filePath: string) => ipcRenderer.invoke('read-file-as-base64', filePath),

  // النسخ الاحتياطي المحسن
  getBackups: () => ipcRenderer.invoke('get-backups'),
  createBackup: (options?: { description?: string; includeImages?: boolean; compress?: boolean }) => ipcRenderer.invoke('create-backup', options),
  restoreBackup: (backupPath: string) => ipcRenderer.invoke('restore-backup', backupPath),
  deleteBackup: (backupFilename: string) => ipcRenderer.invoke('delete-backup', backupFilename),
  getBackupSettings: () => ipcRenderer.invoke('get-backup-settings'),
  updateBackupSettings: (settings: any) => ipcRenderer.invoke('update-backup-settings', settings),
  exportBackup: (backupFilename: string, exportPath: string) => ipcRenderer.invoke('export-backup', backupFilename, exportPath),
  importBackup: (importPath: string) => ipcRenderer.invoke('import-backup', importPath),
  verifyBackup: (backupFilename: string) => ipcRenderer.invoke('verify-backup', backupFilename),

  // العملات
  getCurrencies: () => ipcRenderer.invoke('get-currencies'),
  updateCurrencyRate: (currencyId: number, exchangeRate: number) => ipcRenderer.invoke('update-currency-rate', currencyId, exchangeRate),

  // طرق الدفع
  getPaymentMethods: () => ipcRenderer.invoke('get-payment-methods'),

  // المخازن
  getWarehouses: () => ipcRenderer.invoke('get-warehouses'),
  createWarehouse: (warehouseData: any) => ipcRenderer.invoke('create-warehouse', warehouseData),
  updateWarehouse: (warehouseId: number, warehouseData: any) => ipcRenderer.invoke('update-warehouse', warehouseId, warehouseData),
  deleteWarehouse: (warehouseId: number) => ipcRenderer.invoke('delete-warehouse', warehouseId),
  generateWarehouseCode: () => ipcRenderer.invoke('generate-warehouse-code'),

  // الفئات
  getCategories: () => ipcRenderer.invoke('get-categories'),
  createCategory: (categoryData: any) => ipcRenderer.invoke('create-category', categoryData),
  updateCategory: (categoryId: number, categoryData: any) => ipcRenderer.invoke('update-category', categoryId, categoryData),
  deleteCategory: (categoryId: number) => ipcRenderer.invoke('delete-category', categoryId),
  renameCategory: (categoryId: number, newName: string) => ipcRenderer.invoke('rename-category', categoryId, newName),
  generateCategoryCode: () => ipcRenderer.invoke('generate-category-code'),

  // الأصناف
  getItems: () => ipcRenderer.invoke('get-items'),
  getItemsByWarehouse: (warehouseId: number) => ipcRenderer.invoke('get-items-by-warehouse', warehouseId),
  getItemsByType: (type: string) => ipcRenderer.invoke('get-items-by-type', type),
  getItemTypes: () => ipcRenderer.invoke('get-item-types'),
  createItem: (itemData: any) => ipcRenderer.invoke('create-item', itemData),
  updateItem: (itemId: number, itemData: any) => ipcRenderer.invoke('update-item', itemId, itemData),
  deleteItem: (itemId: number) => ipcRenderer.invoke('delete-item', itemId),
  generateItemCode: (categoryId?: number) => ipcRenderer.invoke('generate-item-code', categoryId),
  findItemByBarcode: (barcode: string) => ipcRenderer.invoke('find-item-by-barcode', barcode),
  checkCodeUniqueness: (tableName: string, codeColumn: string, code: string, excludeId?: number) =>
    ipcRenderer.invoke('check-code-uniqueness', tableName, codeColumn, code, excludeId),
  exportItems: (format: 'excel' | 'csv') => ipcRenderer.invoke('export-items', format),
  importItems: (itemsData: any[]) => ipcRenderer.invoke('import-items', itemsData),

  // التحقق من تفرد الأرقام
  checkInvoiceNumberUniqueness: (tableName: string, invoiceNumber: string, excludeId?: number) =>
    ipcRenderer.invoke('check-invoice-number-uniqueness', tableName, invoiceNumber, excludeId),
  checkOrderNumberUniqueness: (tableName: string, orderNumber: string, excludeId?: number) =>
    ipcRenderer.invoke('check-order-number-uniqueness', tableName, orderNumber, excludeId),

  // الباركود
  getItemBarcodes: (itemId: number) => ipcRenderer.invoke('get-item-barcodes', itemId),
  addItemBarcode: (barcodeData: any) => ipcRenderer.invoke('add-item-barcode', barcodeData),
  deleteItemBarcode: (barcodeId: number) => ipcRenderer.invoke('delete-item-barcode', barcodeId),

  // المخزون
  getInventory: () => ipcRenderer.invoke('get-inventory'),
  getItemsWithInventory: () => ipcRenderer.invoke('get-items-with-inventory'),
  updateInventory: (inventoryData: any) => ipcRenderer.invoke('update-inventory', inventoryData),
  checkItemAvailability: (itemId: number, warehouseId: number, requestedQuantity: number) =>
    ipcRenderer.invoke('check-item-availability', itemId, warehouseId, requestedQuantity),

  // حركات المخزون
  getInventoryMovements: (filters?: any) => ipcRenderer.invoke('get-inventory-movements', filters),
  createInventoryMovement: (movementData: any) => ipcRenderer.invoke('create-inventory-movement', movementData),
  getItemWarehouseQuantity: (itemId: number, warehouseId: number) => ipcRenderer.invoke('get-item-warehouse-quantity', itemId, warehouseId),

  // التقارير - المخزون
  getInventoryReport: (filters?: any) => ipcRenderer.invoke('get-inventory-report', filters),
  getInventoryMovementsReport: (filters?: any) => ipcRenderer.invoke('get-inventory-movements-report', filters),
  getInventoryAuditReport: (filters?: any) => ipcRenderer.invoke('get-inventory-audit-report', filters),
  getMaterialConsumptionReport: (filters?: any) => ipcRenderer.invoke('get-material-consumption-report', filters),
  getLowStockReport: (filters?: any) => ipcRenderer.invoke('get-low-stock-report', filters),
  getWarehouse: (warehouseId: number) => ipcRenderer.invoke('get-warehouse', warehouseId),

  // التنبيهات والإحصائيات
  getInventoryAlerts: () => ipcRenderer.invoke('get-inventory-alerts'),
  getInventoryStatistics: () => ipcRenderer.invoke('get-inventory-statistics'),

  // إدارة صور الأصناف
  getItemImages: (itemId: number) => ipcRenderer.invoke('get-item-images', itemId),
  uploadItemImage: (imageData: any) => ipcRenderer.invoke('upload-item-image', imageData),
  getItemImage: (imagePath: string) => ipcRenderer.invoke('get-item-image', imagePath),
  setItemPrimaryImage: (itemId: number, imageId: number) => ipcRenderer.invoke('set-item-primary-image', itemId, imageId),
  deleteItemImage: (imageId: number) => ipcRenderer.invoke('delete-item-image', imageId),

  // تحليل ABC
  getABCAnalysisReport: (filters?: any) => ipcRenderer.invoke('get-abc-analysis-report', filters),



  // التقارير - المبيعات
  getSalesByCustomerReport: (filters?: any) => ipcRenderer.invoke('get-sales-by-customer-report', filters),
  getSalesByProductReport: (filters?: any) => ipcRenderer.invoke('get-sales-by-product-report', filters),
  getMonthlySalesReport: (filters?: any) => ipcRenderer.invoke('get-monthly-sales-report', filters),
  getSalesProfitabilityReport: (filters?: any) => ipcRenderer.invoke('get-sales-profitability-report', filters),

  // التقارير - المشتريات
  getPurchasesBySupplierReport: (filters?: any) => ipcRenderer.invoke('get-purchases-by-supplier-report', filters),
  getPurchasesByItemReport: (filters?: any) => ipcRenderer.invoke('get-purchases-by-item-report', filters),
  getSupplierPayablesReport: (filters?: any) => ipcRenderer.invoke('get-supplier-payables-report', filters),
  getPurchaseAnalysisReport: (filters?: any) => ipcRenderer.invoke('get-purchase-analysis-report', filters),
  getCostAnalysisReport: (filters?: any) => ipcRenderer.invoke('get-cost-analysis-report', filters),
  addSampleSuppliersData: () => ipcRenderer.invoke('add-sample-suppliers-data'),
  addSamplePurchaseOrdersData: () => ipcRenderer.invoke('add-sample-purchase-orders-data'),
  addSamplePurchaseInvoicesData: () => ipcRenderer.invoke('add-sample-purchase-invoices-data'),
  addSampleSupplierPaymentsData: () => ipcRenderer.invoke('add-sample-supplier-payments-data'),

  // المشتريات - الموردين
  getSuppliers: () => ipcRenderer.invoke('get-suppliers'),
  createSupplier: (supplierData: any) => ipcRenderer.invoke('create-supplier', supplierData),
  updateSupplier: (supplierId: number, supplierData: any) => ipcRenderer.invoke('update-supplier', supplierId, supplierData),
  deleteSupplier: (supplierId: number) => ipcRenderer.invoke('delete-supplier', supplierId),
  generateSupplierCode: () => ipcRenderer.invoke('generate-supplier-code'),

  // المشتريات - أوامر الشراء
  getPurchaseOrders: () => ipcRenderer.invoke('get-purchase-orders'),
  createPurchaseOrder: (orderData: any) => ipcRenderer.invoke('create-purchase-order', orderData),
  updatePurchaseOrder: (orderId: number, orderData: any) => ipcRenderer.invoke('update-purchase-order', orderId, orderData),
  deletePurchaseOrder: (orderId: number) => ipcRenderer.invoke('delete-purchase-order', orderId),
  getPurchaseOrderItems: (orderId: number) => ipcRenderer.invoke('get-purchase-order-items', orderId),
  updatePurchaseOrderStatus: (orderId: number, status: string) => ipcRenderer.invoke('update-purchase-order-status', orderId, status),
  convertOrderToInvoice: (orderId: number) => ipcRenderer.invoke('convert-order-to-invoice', orderId),
  generatePurchaseOrderNumber: () => ipcRenderer.invoke('generate-purchase-order-number'),

  // المشتريات - فواتير الشراء
  getPurchaseInvoices: () => ipcRenderer.invoke('get-purchase-invoices'),
  createPurchaseInvoice: (invoiceData: any) => ipcRenderer.invoke('create-purchase-invoice', invoiceData),
  updatePurchaseInvoice: (invoiceId: number, invoiceData: any) => ipcRenderer.invoke('update-purchase-invoice', invoiceId, invoiceData),
  updatePurchaseInvoiceStatus: (invoiceId: number, status: string) => ipcRenderer.invoke('update-purchase-invoice-status', invoiceId, status),
  deletePurchaseInvoice: (invoiceId: number) => ipcRenderer.invoke('delete-purchase-invoice', invoiceId),
  generatePurchaseInvoiceNumber: () => ipcRenderer.invoke('generate-purchase-invoice-number'),
  getPurchaseInvoiceItems: (invoiceId: number) => ipcRenderer.invoke('get-purchase-invoice-items', invoiceId),

  // المشتريات - مدفوعات الموردين
  getSupplierPayments: () => ipcRenderer.invoke('get-supplier-payments'),
  createSupplierPayment: (paymentData: any) => ipcRenderer.invoke('create-supplier-payment', paymentData),
  generatePaymentNumber: () => ipcRenderer.invoke('generate-payment-number'),

  // المدفوعات العامة
  createCustomerPayment: (paymentData: any) => ipcRenderer.invoke('create-customer-payment', paymentData),
  emergencyCreateCustomerPayment: (paymentData: any) => ipcRenderer.invoke('emergency-create-customer-payment', paymentData),
  getInvoicePayments: (invoiceId: number, invoiceType: string) => ipcRenderer.invoke('get-invoice-payments', invoiceId, invoiceType),

  // المبيعات - العملاء
  getCustomers: () => ipcRenderer.invoke('get-customers'),
  createCustomer: (customerData: any) => ipcRenderer.invoke('create-customer', customerData),
  updateCustomer: (customerId: number, customerData: any) => ipcRenderer.invoke('update-customer', customerId, customerData),
  deleteCustomer: (customerId: number) => ipcRenderer.invoke('delete-customer', customerId),
  generateCustomerCode: () => ipcRenderer.invoke('generate-customer-code'),
  addSampleCustomersData: () => ipcRenderer.invoke('add-sample-customers-data'),
  addSampleSalesInvoicesData: () => ipcRenderer.invoke('add-sample-sales-invoices-data'),
  addSampleSalesOrdersData: () => ipcRenderer.invoke('add-sample-sales-orders-data'),

  // المبيعات - أوامر البيع
  getSalesOrders: () => ipcRenderer.invoke('get-sales-orders'),
  getAvailableSalesOrders: (customerId?: number) => ipcRenderer.invoke('get-available-sales-orders', customerId),
  createSalesOrder: (orderData: any) => ipcRenderer.invoke('create-sales-order', orderData),
  updateSalesOrder: (orderId: number, orderData: any) => ipcRenderer.invoke('update-sales-order', orderId, orderData),
  deleteSalesOrder: (orderId: number) => ipcRenderer.invoke('delete-sales-order', orderId),
  generateSalesOrderNumber: () => ipcRenderer.invoke('generate-sales-order-number'),
  getSalesOrderItems: (orderId: number) => ipcRenderer.invoke('get-sales-order-items', orderId),

  // المبيعات - فواتير البيع
  getSalesInvoices: () => ipcRenderer.invoke('get-sales-invoices'),
  createSalesInvoice: (invoiceData: any) => ipcRenderer.invoke('create-sales-invoice', invoiceData),
  updateSalesInvoice: (invoiceId: number, invoiceData: any) => ipcRenderer.invoke('update-sales-invoice', invoiceId, invoiceData),
  deleteSalesInvoice: (invoiceId: number) => ipcRenderer.invoke('delete-sales-invoice', invoiceId),
  updateSalesInvoiceStatus: (invoiceId: number, status: string) => ipcRenderer.invoke('update-sales-invoice-status', invoiceId, status),
  generateSalesInvoiceNumber: () => ipcRenderer.invoke('generate-sales-invoice-number'),
  getSalesInvoiceItems: (invoiceId: number) => ipcRenderer.invoke('get-sales-invoice-items', invoiceId),

  // الدهان - أنواع الدهانات
  getPaintTypes: () => ipcRenderer.invoke('get-paint-types'),
  createPaintType: (paintTypeData: any) => ipcRenderer.invoke('create-paint-type', paintTypeData),
  updatePaintType: (paintTypeId: number, paintTypeData: any) => ipcRenderer.invoke('update-paint-type', paintTypeId, paintTypeData),
  deletePaintType: (paintTypeId: number) => ipcRenderer.invoke('delete-paint-type', paintTypeId),
  generatePaintTypeCode: () => ipcRenderer.invoke('generate-paint-type-code'),
  addSamplePaintTypesData: () => ipcRenderer.invoke('add-sample-paint-types-data'),

  // الدهان - أوامر الدهان
  getPaintOrders: () => ipcRenderer.invoke('get-paint-orders'),
  createPaintOrder: (orderData: any) => ipcRenderer.invoke('create-paint-order', orderData),
  updatePaintOrderStatus: (orderId: number, status: string) => ipcRenderer.invoke('update-paint-order-status', orderId, status),
  generatePaintOrderNumber: () => ipcRenderer.invoke('generate-paint-order-number'),

  // الدهان - فواتير الدهان
  getPaintInvoices: () => ipcRenderer.invoke('get-paint-invoices'),
  createPaintInvoice: (invoiceData: any) => ipcRenderer.invoke('create-paint-invoice', invoiceData),
  updatePaintInvoiceStatus: (invoiceId: number, status: string) => ipcRenderer.invoke('update-paint-invoice-status', invoiceId, status),
  generatePaintInvoiceNumber: () => ipcRenderer.invoke('generate-paint-invoice-number'),

  // الإنتاج - أقسام الإنتاج
  getProductionDepartments: () => ipcRenderer.invoke('get-production-departments'),
  createProductionDepartment: (departmentData: any) => ipcRenderer.invoke('create-production-department', departmentData),
  updateProductionDepartment: (departmentId: number, departmentData: any) => ipcRenderer.invoke('update-production-department', departmentId, departmentData),
  deleteProductionDepartment: (departmentId: number) => ipcRenderer.invoke('delete-production-department', departmentId),
  generateProductionDepartmentCode: () => ipcRenderer.invoke('generate-production-department-code'),

  // الإنتاج - أوامر الإنتاج
  getProductionOrders: () => ipcRenderer.invoke('get-production-orders'),
  createProductionOrder: (orderData: any) => ipcRenderer.invoke('create-production-order', orderData),
  updateProductionOrder: (orderId: number, orderData: any) => ipcRenderer.invoke('update-production-order', orderId, orderData),
  deleteProductionOrder: (orderId: number) => ipcRenderer.invoke('delete-production-order', orderId),
  generateProductionOrderNumber: () => ipcRenderer.invoke('generate-production-order-number'),
  generateProductionOrderCode: () => ipcRenderer.invoke('generate-production-order-code'),
  getProductionOrderDetails: (orderId: number) => ipcRenderer.invoke('get-production-order-details', orderId),
  createProductionOrderWithMultipleRecipes: (orderData: any) => ipcRenderer.invoke('create-production-order-with-multiple-recipes', orderData),
  checkMultipleRecipesMaterialsAvailability: (recipes: any[]) => ipcRenderer.invoke('check-multiple-recipes-materials-availability', recipes),
  getProductionOrderWithRecipes: (orderId: number) => ipcRenderer.invoke('get-production-order-with-recipes', orderId),
  addProductionOrderRecipes: (orderId: number, recipes: any[]) => ipcRenderer.invoke('add-production-order-recipes', orderId, recipes),

  // الإنتاج - تفاصيل أوامر الإنتاج للطباعة والعرض
  getProductionOrderItems: (orderId: number) => ipcRenderer.invoke('get-production-order-items', orderId),
  getProductionOrderMaterials: (orderId: number) => ipcRenderer.invoke('get-production-order-materials', orderId),
  getProductionOrderStagesDetails: (orderId: number) => ipcRenderer.invoke('get-production-order-stages', orderId),
  getProductionOrderImages: (orderId: number) => ipcRenderer.invoke('get-production-order-images', orderId),
  uploadProductionOrderImage: (imageData: any) => ipcRenderer.invoke('upload-production-order-image', imageData),
  createSampleProductionOrderImages: (orderId: number) => ipcRenderer.invoke('create-sample-production-order-images', orderId),

  // نقل الصور من localStorage إلى قاعدة البيانات
  migrateImagesToDatabase: (localStorageData: { [key: string]: string }) =>
    ipcRenderer.invoke('migrate-images-to-database', localStorageData),
  cleanupLocalStorageAfterMigration: (migrationResult: any) => ipcRenderer.invoke('cleanup-localstorage-after-migration', migrationResult),

  // الإنتاج - وصفات الإنتاج
  getProductionRecipes: () => ipcRenderer.invoke('get-production-recipes'),
  createProductionRecipe: (recipeData: any) => ipcRenderer.invoke('create-production-recipe', recipeData),
  updateProductionRecipe: (recipeId: number, recipeData: any) => ipcRenderer.invoke('update-production-recipe', recipeId, recipeData),
  deleteProductionRecipe: (recipeId: number) => ipcRenderer.invoke('delete-production-recipe', recipeId),
  generateRecipeCode: () => ipcRenderer.invoke('generate-recipe-code'),
  getRecipeMaterials: (recipeId: number) => ipcRenderer.invoke('get-recipe-materials', recipeId),
  checkRecipeMaterialsAvailability: (recipeId: number, productionQuantity?: number) => ipcRenderer.invoke('check-recipe-materials-availability', recipeId, productionQuantity),
  executeProduction: (productionData: any) => ipcRenderer.invoke('execute-production', productionData),
  executeProductionEnhanced: (productionData: any) => ipcRenderer.invoke('execute-production-enhanced', productionData),
  completeProduction: (productionOrderId: number, completionData?: any) => ipcRenderer.invoke('complete-production', productionOrderId, completionData),
  updateProductCostFromProduction: (productId: number) => ipcRenderer.invoke('update-product-cost-from-production', productId),
  updateProductCostFromRecipes: (productId: number) => ipcRenderer.invoke('update-product-cost-from-recipes', productId),
  getProductionCostAnalysis: (filters?: any) => ipcRenderer.invoke('get-production-cost-analysis', filters),

  // إعدادات الإنتاج
  getProductionSettings: () => ipcRenderer.invoke('get-production-settings'),
  saveProductionSettings: (settings: any) => ipcRenderer.invoke('save-production-settings', settings),

  // أوامر صرف المواد
  createMaterialIssueOrder: (orderData: any) => ipcRenderer.invoke('create-material-issue-order', orderData),
  getMaterialIssueOrders: (filters?: any) => ipcRenderer.invoke('get-material-issue-orders', filters),
  getMaterialIssueOrderDetails: (issueOrderId: number) => ipcRenderer.invoke('get-material-issue-order-details', issueOrderId),

  // تتبع ساعات العمل
  startLaborTimeTracking: (trackingData: any) => ipcRenderer.invoke('start-labor-time-tracking', trackingData),
  endLaborTimeTracking: (trackingId: number, notes?: string) => ipcRenderer.invoke('end-labor-time-tracking', trackingId, notes),
  getLaborTimeTracking: (filters?: any) => ipcRenderer.invoke('get-labor-time-tracking', filters),

  // الإنتاج - مراحل الإنتاج
  getProductionStages: () => ipcRenderer.invoke('get-production-stages'),
  createProductionStage: (stageData: any) => ipcRenderer.invoke('create-production-stage', stageData),
  updateProductionStage: (stageId: number, stageData: any) => ipcRenderer.invoke('update-production-stage', stageId, stageData),
  deleteProductionStage: (stageId: number) => ipcRenderer.invoke('delete-production-stage', stageId),
  generateProductionStageCode: () => ipcRenderer.invoke('generate-production-stage-code'),
  getProductionOrderStages: () => ipcRenderer.invoke('get-production-order-stages'),
  getAllProductionOrderStages: () => ipcRenderer.invoke('get-all-production-order-stages'),
  startProductionStage: (orderStageId: number) => ipcRenderer.invoke('start-production-stage', orderStageId),
  completeProductionStage: (orderStageData: any) => ipcRenderer.invoke('complete-production-stage', orderStageData),

  // الإنتاج - ربط بالمخزون
  startProductionOrder: (orderId: number) => ipcRenderer.invoke('start-production-order', orderId),
  completeProductionOrder: (orderId: number) => ipcRenderer.invoke('complete-production-order', orderId),

  // الإنتاج - التقارير
  getProductionOrdersReport: (filters: any) => ipcRenderer.invoke('get-production-orders-report', filters),
  getProductionEfficiencyReport: (filters: any) => ipcRenderer.invoke('get-production-efficiency-report', filters),
  getProductionCostsReport: (filters: any) => ipcRenderer.invoke('get-production-costs-report', filters),
  getProductionScheduleReport: (filters: any) => ipcRenderer.invoke('get-production-schedule-report', filters),

  // المحاسبة - دليل الحسابات
  getChartOfAccounts: () => ipcRenderer.invoke('get-chart-of-accounts'),
  createAccount: (accountData: any) => ipcRenderer.invoke('create-account', accountData),
  updateAccount: (accountId: number, accountData: any) => ipcRenderer.invoke('update-account', accountId, accountData),
  deleteAccount: (accountId: number) => ipcRenderer.invoke('delete-account', accountId),
  getAccountBalance: (accountId: number) => ipcRenderer.invoke('get-account-balance', accountId),
  getAccountTransactions: (accountId: number, filters?: any) => ipcRenderer.invoke('get-account-transactions', accountId, filters),

  // المحاسبة - القيود المحاسبية
  getJournalEntries: (filters?: any) => ipcRenderer.invoke('get-journal-entries', filters),
  createJournalEntry: (entryData: any) => ipcRenderer.invoke('create-journal-entry', entryData),
  updateJournalEntry: (entryId: number, entryData: any) => ipcRenderer.invoke('update-journal-entry', entryId, entryData),
  deleteJournalEntry: (entryId: number) => ipcRenderer.invoke('delete-journal-entry', entryId),
  postJournalEntry: (entryId: number, userId?: number) => ipcRenderer.invoke('post-journal-entry', entryId, userId),
  generateJournalEntryNumber: () => ipcRenderer.invoke('generate-journal-entry-number'),

  // المحاسبة - التقارير المحاسبية
  getTrialBalance: (filters?: any) => ipcRenderer.invoke('get-trial-balance', filters),
  getBalanceSheet: (filters?: any) => ipcRenderer.invoke('get-balance-sheet', filters),
  getIncomeStatement: (filters?: any) => ipcRenderer.invoke('get-income-statement', filters),
  getGeneralLedger: (filters?: any) => ipcRenderer.invoke('get-general-ledger', filters),
  getCashFlowStatement: (filters?: any) => ipcRenderer.invoke('get-cash-flow-statement', filters),

  // المحاسبة - الضرائب والزكاة
  calculateVAT: (amount: number, vatRate?: number) => ipcRenderer.invoke('calculate-vat', amount, vatRate),
  calculateZakat: (filters?: any) => ipcRenderer.invoke('calculate-zakat', filters),
  getTaxReport: (filters?: any) => ipcRenderer.invoke('get-tax-report', filters),
  getZakatReport: (filters?: any) => ipcRenderer.invoke('get-zakat-report', filters),

  // المحاسبة - إعدادات المحاسبة
  getFiscalYearSettings: () => ipcRenderer.invoke('get-fiscal-year-settings'),
  updateFiscalYearSettings: (settings: any) => ipcRenderer.invoke('update-fiscal-year-settings', settings),
  closeFiscalPeriod: (periodData: any) => ipcRenderer.invoke('close-fiscal-period', periodData),
  getAccountingSettings: () => ipcRenderer.invoke('get-accounting-settings'),
  updateAccountingSettings: (settings: any) => ipcRenderer.invoke('update-accounting-settings', settings),

  // المالية - البنوك والحسابات المصرفية
  getBankAccounts: () => ipcRenderer.invoke('get-bank-accounts'),
  createBankAccount: (accountData: any) => ipcRenderer.invoke('create-bank-account', accountData),
  updateBankAccount: (accountId: number, accountData: any) => ipcRenderer.invoke('update-bank-account', accountId, accountData),
  deleteBankAccount: (accountId: number) => ipcRenderer.invoke('delete-bank-account', accountId),

  // المالية - المعاملات المصرفية
  getBankTransactions: (accountId?: number) => ipcRenderer.invoke('get-bank-transactions', accountId),
  createBankTransaction: (transactionData: any) => ipcRenderer.invoke('create-bank-transaction', transactionData),

  // المالية - الشيكات
  getChecks: () => ipcRenderer.invoke('get-checks'),
  createCheck: (checkData: any) => ipcRenderer.invoke('create-check', checkData),
  updateCheckStatus: (checkId: number, status: string) => ipcRenderer.invoke('update-check-status', checkId, status),
  generateCheckNumber: () => ipcRenderer.invoke('generate-check-number'),
  exportChecks: (format: 'excel' | 'csv') => ipcRenderer.invoke('export-checks', format),
  importChecks: (checksData: any[]) => ipcRenderer.invoke('import-checks', checksData),

  // المالية - سندات الدفع
  getPaymentVouchers: () => ipcRenderer.invoke('get-payment-vouchers'),
  createPaymentVoucher: (voucherData: any) => ipcRenderer.invoke('create-payment-voucher', voucherData),
  updatePaymentVoucherStatus: (voucherId: number, status: string) => ipcRenderer.invoke('update-payment-voucher-status', voucherId, status),
  generatePaymentVoucherNumber: () => ipcRenderer.invoke('generate-payment-voucher-number'),

  // المالية - سندات القبض
  getReceiptVouchers: () => ipcRenderer.invoke('get-receipt-vouchers'),
  createReceiptVoucher: (voucherData: any) => ipcRenderer.invoke('create-receipt-voucher', voucherData),
  generateReceiptVoucherNumber: () => ipcRenderer.invoke('generate-receipt-voucher-number'),

  // المالية - الكمبيالات
  getPromissoryNotes: () => ipcRenderer.invoke('get-promissory-notes'),
  getPromissoryNoteById: (noteId: number) => ipcRenderer.invoke('get-promissory-note-by-id', noteId),
  createPromissoryNote: (noteData: any) => ipcRenderer.invoke('create-promissory-note', noteData),
  updatePromissoryNote: (noteId: number, noteData: any) => ipcRenderer.invoke('update-promissory-note', noteId, noteData),
  deletePromissoryNote: (noteId: number) => ipcRenderer.invoke('delete-promissory-note', noteId),
  transferPromissoryNote: (transferData: any) => ipcRenderer.invoke('transfer-promissory-note', transferData),
  updatePromissoryNoteStatus: (noteId: number, status: string) => ipcRenderer.invoke('update-promissory-note-status', noteId, status),
  generatePromissoryNoteNumber: () => ipcRenderer.invoke('generate-promissory-note-number'),

  // المالية - التقارير المتقدمة
  getFinancialSummary: (params?: any) => ipcRenderer.invoke('get-financial-summary', params),
  getAgingReport: (params?: any) => ipcRenderer.invoke('get-aging-report', params),
  getCashPositionReport: (params?: any) => ipcRenderer.invoke('get-cash-position-report', params),
  getProfitabilityAnalysis: (params?: any) => ipcRenderer.invoke('get-profitability-analysis', params),
  getBudgetVarianceReport: (params?: any) => ipcRenderer.invoke('get-budget-variance-report', params),

  // المالية - التصدير والطباعة
  exportFinancialReport: (reportType: string, params?: any) => ipcRenderer.invoke('export-financial-report', reportType, params),
  printFinancialReport: (reportType: string, params?: any) => ipcRenderer.invoke('print-financial-report', reportType, params),
  generateFinancialDashboard: (params?: any) => ipcRenderer.invoke('generate-financial-dashboard', params),

  // المالية - بيانات تجريبية
  addSampleFinanceData: () => ipcRenderer.invoke('add-sample-finance-data'),

  // المالية - ربط مع الأقسام الأخرى
  linkInvoiceToPayment: (linkData: any) => ipcRenderer.invoke('link-invoice-to-payment', linkData),
  getUnpaidInvoices: (entityType?: string, entityId?: number) => ipcRenderer.invoke('get-unpaid-invoices', entityType, entityId),
  getEntitiesForTransfer: () => ipcRenderer.invoke('get-entities-for-transfer'),

  // إدارة قاعدة البيانات
  checkDatabaseHealth: () => ipcRenderer.invoke('check-database-health'),
  reinitializeDatabase: () => ipcRenderer.invoke('reinitialize-database'),

  // المالية - تحويل الشيكات
  transferCheck: (transferData: any) => ipcRenderer.invoke('transfer-check', transferData),
  getCheckTransfers: (checkId?: number) => ipcRenderer.invoke('get-check-transfers', checkId),
  getTransferableChecks: () => ipcRenderer.invoke('get-transferable-checks'),
  getCheckHistory: (checkId: number) => ipcRenderer.invoke('get-check-history', checkId),

  // المالية - تكاليف الإنتاج
  getProductionCosts: () => ipcRenderer.invoke('get-production-costs'),
  updateProductionCostStatus: (costId: number, status: string) => ipcRenderer.invoke('update-production-cost-status', costId, status),

  // المالية - إدارة شيكات الشركة (تم دمجها مع الشيكات الموجودة)
  // المالية - التقارير الشاملة (تم دمجها مع الموجودة)

  // دالة عامة للاستدعاءات
  invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args),

  // ===== APIs إدارة الموظفين =====

  // إدارة أقسام الموظفين
  getEmployeeDepartments: () => ipcRenderer.invoke('get-employee-departments'),
  createEmployeeDepartment: (departmentData: any) => ipcRenderer.invoke('create-employee-department', departmentData),
  updateEmployeeDepartment: (departmentId: number, departmentData: any) => ipcRenderer.invoke('update-employee-department', departmentId, departmentData),
  deleteEmployeeDepartment: (departmentId: number) => ipcRenderer.invoke('delete-employee-department', departmentId),

  // إدارة أجهزة البصمة
  getFingerprintDevices: () => ipcRenderer.invoke('get-fingerprint-devices'),
  createFingerprintDevice: (deviceData: any) => ipcRenderer.invoke('create-fingerprint-device', deviceData),
  updateFingerprintDevice: (deviceId: number, deviceData: any) => ipcRenderer.invoke('update-fingerprint-device', deviceId, deviceData),
  deleteFingerprintDevice: (deviceId: number) => ipcRenderer.invoke('delete-fingerprint-device', deviceId),
  syncFingerprintDevice: (deviceId: number) => ipcRenderer.invoke('sync-fingerprint-device', deviceId),

  // إدارة الموظفين
  getEmployees: () => ipcRenderer.invoke('get-employees'),
  createEmployee: (employeeData: any) => ipcRenderer.invoke('create-employee', employeeData),
  updateEmployee: (employeeId: number, employeeData: any) => ipcRenderer.invoke('update-employee', employeeId, employeeData),
  deleteEmployee: (employeeId: number) => ipcRenderer.invoke('delete-employee', employeeId),
  terminateEmployee: (employeeId: number, terminationData: any) => ipcRenderer.invoke('terminate-employee', employeeId, terminationData),
  generateEmployeeCode: () => ipcRenderer.invoke('generate-employee-code'),
  generateDepartmentCode: () => ipcRenderer.invoke('generate-department-code'),

  // إدارة الحضور والانصراف
  getEmployeeAttendance: (filters?: any) => ipcRenderer.invoke('get-employee-attendance', filters),
  recordAttendance: (attendanceData: any) => ipcRenderer.invoke('record-attendance', attendanceData),

  // إدارة الإجازات
  getEmployeeLeaves: (filters?: any) => ipcRenderer.invoke('get-employee-leaves', filters),
  createLeaveRequest: (leaveData: any) => ipcRenderer.invoke('create-leave-request', leaveData),
  updateLeaveRequest: (leaveId: number, leaveData: any) => ipcRenderer.invoke('update-leave-request', leaveId, leaveData),
  approveLeaveRequest: (leaveId: number, approvalData: any) => ipcRenderer.invoke('approve-leave-request', leaveId, approvalData),
  deleteLeaveRequest: (leaveId: number) => ipcRenderer.invoke('delete-leave-request', leaveId),

  // إدارة الرواتب
  getEmployeePayroll: (filters?: any) => ipcRenderer.invoke('get-employee-payroll', filters),
  calculatePayroll: (payrollData: any) => ipcRenderer.invoke('calculate-payroll', payrollData),
  updatePayroll: (payrollId: number, payrollData: any) => ipcRenderer.invoke('update-payroll', payrollId, payrollData),
  deletePayroll: (payrollId: number) => ipcRenderer.invoke('delete-payroll', payrollId),

  // تقارير الموظفين
  getEmployeeAttendanceReport: (filters?: any) => ipcRenderer.invoke('get-employee-attendance-report', filters),
  getEmployeePayrollReport: (filters?: any) => ipcRenderer.invoke('get-employee-payroll-report', filters),
  getEmployeeLeavesReport: (filters?: any) => ipcRenderer.invoke('get-employee-leaves-report', filters),
  getEmployeePerformanceReport: (filters?: any) => ipcRenderer.invoke('get-employee-performance-report', filters),
  getEmployeeOvertimeReport: (filters?: any) => ipcRenderer.invoke('get-employee-overtime-report', filters),

  // ربط الموظفين بالأقسام الأخرى
  getEmployeesByDepartment: (departmentId: number) => ipcRenderer.invoke('get-employees-by-department', departmentId),
  getEmployeeSalesPerformance: (employeeId: number, filters?: any) => ipcRenderer.invoke('get-employee-sales-performance', employeeId, filters),
  getEmployeeProductionAssignments: (employeeId: number) => ipcRenderer.invoke('get-employee-production-assignments', employeeId),
  assignEmployeeToProduction: (assignmentData: any) => ipcRenderer.invoke('assign-employee-to-production', assignmentData),
  getEmployeeFinancialTransactions: (employeeId: number, filters?: any) => ipcRenderer.invoke('get-employee-financial-transactions', employeeId, filters),

  // البيانات التجريبية
  addSampleEmployeesData: () => ipcRenderer.invoke('add-sample-employees-data'),
  addSampleInventoryData: () => ipcRenderer.invoke('add-sample-inventory-data'),

  // إصلاح المخزون
  updateInventoryCostPrices: () => ipcRenderer.invoke('update-inventory-cost-prices'),

  // تحديث الفواتير المتأخرة
  updateOverdueInvoices: () => ipcRenderer.invoke('update-overdue-invoices'),

  // تقارير الدهان
  getPaintByCustomerReport: (filters: any) => ipcRenderer.invoke('get-paint-by-customer-report', filters),
  getPaintByTypeReport: (filters: any) => ipcRenderer.invoke('get-paint-by-type-report', filters),
  getMonthlyPaintReport: (filters: any) => ipcRenderer.invoke('get-monthly-paint-report', filters),
  getPaintProfitabilityReport: (filters: any) => ipcRenderer.invoke('get-paint-profitability-report', filters),
  getPaintPerformanceReport: (filters: any) => ipcRenderer.invoke('get-paint-performance-report', filters),
  getPaintQualityReport: (filters: any) => ipcRenderer.invoke('get-paint-quality-report', filters),

  // ===== نظام إدارة الصور =====

  // صور الأصناف - تم نقلها للأعلى لتجنب التكرار
  setPrimaryImage: (data: { imageId: number; itemId: number }) => ipcRenderer.invoke('set-primary-image', data),
  updateImageOrder: (data: { imageId: number; sortOrder: number }) => ipcRenderer.invoke('update-image-order', data),
  updateImageDescription: (data: { imageId: number; description: string }) => ipcRenderer.invoke('update-image-description', data),

  // صور الشيكات
  uploadCheckImage: (imageData: any) => ipcRenderer.invoke('upload-check-image', imageData),
  getCheckImages: (checkId: number) => ipcRenderer.invoke('get-check-images', checkId),
  deleteCheckImage: (imageId: number) => ipcRenderer.invoke('delete-check-image', imageId),
  updateCheckImageNotes: (data: { imageId: number; notes: string }) => ipcRenderer.invoke('update-check-image-notes', data),

  // إعدادات الصور
  getImageSettings: () => ipcRenderer.invoke('get-image-settings'),
  updateImageSetting: (data: { settingKey: string; settingValue: string }) => ipcRenderer.invoke('update-image-setting', data),
  getImageSetting: (settingKey: string) => ipcRenderer.invoke('get-image-setting', settingKey),

  // معالجات إضافية للصور
  selectAndUploadCheckImage: (checkId: number, imageSide?: string) => ipcRenderer.invoke('select-and-upload-check-image', checkId, imageSide),

  // الإشعارات
  getNotifications: (filters?: any) => ipcRenderer.invoke('get-notifications', filters),
  createNotification: (notificationData: any) => ipcRenderer.invoke('create-notification', notificationData),
  markNotificationRead: (notificationId: number, isRead?: boolean) => ipcRenderer.invoke('mark-notification-read', notificationId, isRead),
  markAllNotificationsRead: (userId?: number) => ipcRenderer.invoke('mark-all-notifications-read', userId),
  deleteNotification: (notificationId: number) => ipcRenderer.invoke('delete-notification', notificationId),
  getUnreadNotificationsCount: (userId?: number) => ipcRenderer.invoke('get-unread-notifications-count', userId),

  // الإشعارات الذكية
  getLowStockItems: (threshold?: number) => ipcRenderer.invoke('get-low-stock-items', threshold),
  getDueInvoices: (reminderDays?: number) => ipcRenderer.invoke('get-due-invoices', reminderDays),
  getLastBackupInfo: () => ipcRenderer.invoke('get-last-backup-info'),
  getSuspiciousActivities: () => ipcRenderer.invoke('get-suspicious-activities'),

  // معلومات النظام
  getPlatform: () => ipcRenderer.invoke('get-platform'),
  getElectronVersion: () => ipcRenderer.invoke('get-electron-version'),
  getResourcePath: (relativePath: string) => ipcRenderer.invoke('get-resource-path', relativePath),



  // الطباعة والتصدير المتقدم (إضافية)
  saveReportAsPDF: (reportType: string, filePath: string, params?: any) => ipcRenderer.invoke('save-report-as-pdf', reportType, filePath, params),
  previewReport: (reportType: string, params?: any) => ipcRenderer.invoke('preview-report', reportType, params),
  exportReportToExcel: (reportType: string, params?: any) => ipcRenderer.invoke('export-report-to-excel', reportType, params),
  exportReportToCSV: (reportType: string, params?: any) => ipcRenderer.invoke('export-report-to-csv', reportType, params),

  // المعالجات المفقودة من main.ts
  resetUserLoginStatus: (userId: number) => ipcRenderer.invoke('reset-user-login-status', userId),
  getCurrentUser: () => ipcRenderer.invoke('getCurrentUser'),
  generateMovementNumber: () => ipcRenderer.invoke('generate-movement-number'),
  cleanupExpiredSessions: () => ipcRenderer.invoke('cleanup-expired-sessions'),
  getDepartments: () => ipcRenderer.invoke('get-departments'),
  createDepartment: (departmentData: any) => ipcRenderer.invoke('create-department', departmentData),
  recordCheckout: (attendanceData: any) => ipcRenderer.invoke('record-checkout', attendanceData),
  getAttendanceRecords: (filters?: any) => ipcRenderer.invoke('get-attendance-records', filters),
  getVouchers: (filters?: any) => ipcRenderer.invoke('get-vouchers', filters),
  createVoucher: (voucherData: any) => ipcRenderer.invoke('create-voucher', voucherData),
  generateVoucherNumber: () => ipcRenderer.invoke('generate-voucher-number'),

  // المزامنة وربط الأجهزة
  getSyncSettings: () => ipcRenderer.invoke('get-sync-settings'),
  updateSyncSettings: (settings: any) => ipcRenderer.invoke('update-sync-settings', settings),
  testSyncConnection: (sharedFolder: string) => ipcRenderer.invoke('test-sync-connection', sharedFolder),
  getSyncStatus: () => ipcRenderer.invoke('get-sync-status'),
  forceSyncNow: () => ipcRenderer.invoke('force-sync-now'),
  selectFolder: () => ipcRenderer.invoke('select-folder'),

  // المزامنة الذكية
  createSmartSharedFolder: () => ipcRenderer.invoke('create-smart-shared-folder'),
  discoverConnectedDevices: () => ipcRenderer.invoke('discover-connected-devices'),
  getNetworkInfo: () => ipcRenderer.invoke('get-network-info'),
  selectDeviceForSync: (deviceInfo: { ip: string; hostname?: string; mac?: string }) => ipcRenderer.invoke('select-device-for-sync', deviceInfo),
  determineDeviceRole: () => ipcRenderer.invoke('determine-device-role'),

  // وظائف التصدير المحسنة
  saveExcelFile: (buffer: ArrayBuffer, fileName: string) => ipcRenderer.invoke('save-excel-file', buffer, fileName),

  // نظام التفعيل
  activateLicense: (activationCode: string) => ipcRenderer.invoke('activate-license', activationCode),
  checkActivationStatus: () => ipcRenderer.invoke('check-activation-status'),
  getLicenseInfo: () => ipcRenderer.invoke('get-license-info'),
  deactivateLicense: () => ipcRenderer.invoke('deactivate-license'),
  getHardwareId: () => ipcRenderer.invoke('get-hardware-id'),
  validateActivationCode: (activationCode: string) => ipcRenderer.invoke('validate-activation-code', activationCode),
  startActivationCheck: () => ipcRenderer.invoke('start-activation-check'),
  stopActivationCheck: () => ipcRenderer.invoke('stop-activation-check'),

  // تقارير مقارنة الأسعار
  getSupplierPriceComparison: (params: any) => ipcRenderer.invoke('get-supplier-price-comparison', params),

  // تقارير المبيعات المتقدمة
  getSalesByRegion: (params: any) => ipcRenderer.invoke('get-sales-by-region', params),

  // تقارير التدفق النقدي
  getCashFlow: (params: any) => ipcRenderer.invoke('get-cash-flow', params),

  // تقارير تحليل الموظفين
  getEmployeeAnalysis: (params: any) => ipcRenderer.invoke('get-employee-analysis', params),

  // تقارير المرتجعات
  getSalesReturnsReport: (params: any) => ipcRenderer.invoke('get-sales-returns-report', params),
  getTopProfitableCustomersReport: (params: any) => ipcRenderer.invoke('get-top-profitable-customers-report', params),

  // تقارير الأداء
  getPurchasePerformanceReport: (params: any) => ipcRenderer.invoke('get-purchase-performance-report', params),

  // تقارير إقفال السنة المالية
  getFiscalClosingReport: (params: any) => ipcRenderer.invoke('getFiscalClosingReport', params),

  // ==================== Print Settings APIs ====================
  // جلب إعدادات الطباعة المركزية
  'get-print-settings': () => ipcRenderer.invoke('get-print-settings'),

  // حفظ إعدادات الطباعة المركزية
  'save-print-settings': (settings: any) => ipcRenderer.invoke('save-print-settings', settings),

  // إعادة تعيين إعدادات الطباعة للافتراضية
  'reset-print-settings': () => ipcRenderer.invoke('reset-print-settings'),

  // حالة قاعدة البيانات
  getDatabaseStatus: () => ipcRenderer.invoke('get-database-status'),

  // تنبيهات المزامنة
  onSyncNotification: (callback: (data: any) => void) => {
    ipcRenderer.on('sync-notification', (_, data) => callback(data))
  },
  onSyncConflict: (callback: (data: any) => void) => {
    ipcRenderer.on('sync-conflict-detected', (_, data) => callback(data))
  },
  onDatabaseSwitched: (callback: (data: any) => void) => {
    ipcRenderer.on('database-switched', (_, data) => callback(data))
  },
  onDatabaseSwitchError: (callback: (data: any) => void) => {
    ipcRenderer.on('database-switch-error', (_, data) => callback(data))
  },

  // دعم الأحداث
  on: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.on(channel, callback)
  },
  removeListener: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, callback)
  },

  // فتح نوافذ إدارة منفصلة
  openItemsManagement: () => ipcRenderer.invoke('open-items-management'),
  openMaterialsManagement: () => ipcRenderer.invoke('open-materials-management'),
  openRecipesManagement: () => ipcRenderer.invoke('open-recipes-management'),

}




// تصدير API للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

// تعريف الأنواع للـ TypeScript
declare global {
  interface Window {
    electronAPI: typeof electronAPI
  }
}
