// أكواد الأخطاء الموحدة لنظام الإنتاج
export const ProductionErrorCodes = {
  // أخطاء أوامر الإنتاج
  PRODUCTION_ORDER: {
    INVALID_PRODUCT_ID: 'PROD_001',
    INVALID_QUANTITY: 'PROD_002', 
    INVALID_DEPARTMENT: 'PROD_003',
    INVALID_CUSTOMER: 'PROD_004',
    INVALID_DATE_RANGE: 'PROD_005',
    ORDER_NOT_FOUND: 'PROD_006',
    ORDER_ALREADY_STARTED: 'PROD_007',
    ORDER_ALREADY_COMPLETED: 'PROD_008',
    DUPLICATE_ORDER_NUMBER: 'PROD_009',
    INSUFFICIENT_MATERIALS: 'PROD_010',
    NO_RECIPE_FOUND: 'PROD_011',
    INVALID_STATUS_TRANSITION: 'PROD_012'
  },

  // أخطاء وصفات الإنتاج
  PRODUCTION_RECIPE: {
    INVALID_CODE: 'RCP_001',
    INVALID_NAME: 'RCP_002',
    INVALID_PRODUCT_ID: 'RCP_003',
    INVALID_DEPARTMENT: 'RCP_004',
    INVALID_TIME: 'RCP_005',
    INVALID_DIFFICULTY: 'RCP_006',
    INVALID_VERSION: 'RCP_007',
    NO_MATERIALS: 'RCP_008',
    DUPLICATE_CODE: 'RCP_009',
    RECIPE_NOT_FOUND: 'RCP_010',
    RECIPE_IN_USE: 'RCP_011'
  },

  // أخطاء مواد الوصفة
  RECIPE_MATERIALS: {
    INVALID_MATERIAL_ID: 'MAT_001',
    INVALID_QUANTITY: 'MAT_002',
    INVALID_UNIT: 'MAT_003',
    INVALID_COST: 'MAT_004',
    DUPLICATE_MATERIAL: 'MAT_005',
    MATERIAL_NOT_FOUND: 'MAT_006',
    INSUFFICIENT_STOCK: 'MAT_007'
  },

  // أخطاء عامة
  GENERAL: {
    DATABASE_ERROR: 'GEN_001',
    VALIDATION_ERROR: 'GEN_002',
    PERMISSION_DENIED: 'GEN_003',
    NETWORK_ERROR: 'GEN_004',
    TIMEOUT_ERROR: 'GEN_005',
    UNKNOWN_ERROR: 'GEN_006'
  }
} as const

// رسائل الأخطاء المترجمة
export const ErrorMessages = {
  [ProductionErrorCodes.PRODUCTION_ORDER.INVALID_PRODUCT_ID]: 'معرف المنتج غير صحيح أو غير موجود',
  [ProductionErrorCodes.PRODUCTION_ORDER.INVALID_QUANTITY]: 'الكمية يجب أن تكون رقم موجب أكبر من الصفر',
  [ProductionErrorCodes.PRODUCTION_ORDER.INVALID_DEPARTMENT]: 'قسم الإنتاج غير صحيح أو غير نشط',
  [ProductionErrorCodes.PRODUCTION_ORDER.INVALID_CUSTOMER]: 'العميل غير صحيح أو غير نشط',
  [ProductionErrorCodes.PRODUCTION_ORDER.INVALID_DATE_RANGE]: 'نطاق التاريخ غير صحيح',
  [ProductionErrorCodes.PRODUCTION_ORDER.ORDER_NOT_FOUND]: 'أمر الإنتاج غير موجود',
  [ProductionErrorCodes.PRODUCTION_ORDER.ORDER_ALREADY_STARTED]: 'أمر الإنتاج تم بدؤه مسبقاً',
  [ProductionErrorCodes.PRODUCTION_ORDER.ORDER_ALREADY_COMPLETED]: 'أمر الإنتاج مكتمل بالفعل',
  [ProductionErrorCodes.PRODUCTION_ORDER.DUPLICATE_ORDER_NUMBER]: 'رقم أمر الإنتاج موجود مسبقاً',
  [ProductionErrorCodes.PRODUCTION_ORDER.INSUFFICIENT_MATERIALS]: 'المواد غير كافية في المخزون',
  [ProductionErrorCodes.PRODUCTION_ORDER.NO_RECIPE_FOUND]: 'لا توجد وصفة إنتاج لهذا المنتج',
  [ProductionErrorCodes.PRODUCTION_ORDER.INVALID_STATUS_TRANSITION]: 'تغيير حالة الأمر غير مسموح',

  [ProductionErrorCodes.PRODUCTION_RECIPE.INVALID_CODE]: 'كود الوصفة غير صحيح (3-20 حرف، أحرف وأرقام فقط)',
  [ProductionErrorCodes.PRODUCTION_RECIPE.INVALID_NAME]: 'اسم الوصفة غير صحيح (2-100 حرف)',
  [ProductionErrorCodes.PRODUCTION_RECIPE.INVALID_PRODUCT_ID]: 'المنتج النهائي غير صحيح أو غير موجود',
  [ProductionErrorCodes.PRODUCTION_RECIPE.INVALID_DEPARTMENT]: 'قسم الإنتاج غير صحيح أو غير نشط',
  [ProductionErrorCodes.PRODUCTION_RECIPE.INVALID_TIME]: 'الوقت المقدر يجب أن يكون رقم موجب',
  [ProductionErrorCodes.PRODUCTION_RECIPE.INVALID_DIFFICULTY]: 'مستوى الصعوبة غير صحيح',
  [ProductionErrorCodes.PRODUCTION_RECIPE.INVALID_VERSION]: 'رقم الإصدار يجب أن يكون بين 1 و 999',
  [ProductionErrorCodes.PRODUCTION_RECIPE.NO_MATERIALS]: 'يجب إضافة مادة واحدة على الأقل للوصفة',
  [ProductionErrorCodes.PRODUCTION_RECIPE.DUPLICATE_CODE]: 'كود الوصفة موجود مسبقاً',
  [ProductionErrorCodes.PRODUCTION_RECIPE.RECIPE_NOT_FOUND]: 'الوصفة غير موجودة',
  [ProductionErrorCodes.PRODUCTION_RECIPE.RECIPE_IN_USE]: 'لا يمكن حذف الوصفة لأنها مستخدمة في أوامر إنتاج',

  [ProductionErrorCodes.RECIPE_MATERIALS.INVALID_MATERIAL_ID]: 'يجب اختيار المادة',
  [ProductionErrorCodes.RECIPE_MATERIALS.INVALID_QUANTITY]: 'كمية المادة يجب أن تكون رقم موجب',
  [ProductionErrorCodes.RECIPE_MATERIALS.INVALID_UNIT]: 'وحدة القياس مطلوبة',
  [ProductionErrorCodes.RECIPE_MATERIALS.INVALID_COST]: 'تكلفة المادة يجب أن تكون رقم موجب أو صفر',
  [ProductionErrorCodes.RECIPE_MATERIALS.DUPLICATE_MATERIAL]: 'المادة مضافة مسبقاً في الوصفة',
  [ProductionErrorCodes.RECIPE_MATERIALS.MATERIAL_NOT_FOUND]: 'المادة غير موجودة',
  [ProductionErrorCodes.RECIPE_MATERIALS.INSUFFICIENT_STOCK]: 'المادة غير متوفرة بالكمية المطلوبة',

  [ProductionErrorCodes.GENERAL.DATABASE_ERROR]: 'خطأ في قاعدة البيانات',
  [ProductionErrorCodes.GENERAL.VALIDATION_ERROR]: 'خطأ في التحقق من صحة البيانات',
  [ProductionErrorCodes.GENERAL.PERMISSION_DENIED]: 'ليس لديك صلاحية لتنفيذ هذه العملية',
  [ProductionErrorCodes.GENERAL.NETWORK_ERROR]: 'خطأ في الشبكة',
  [ProductionErrorCodes.GENERAL.TIMEOUT_ERROR]: 'انتهت مهلة الاتصال',
  [ProductionErrorCodes.GENERAL.UNKNOWN_ERROR]: 'حدث خطأ غير متوقع'
} as const

// نوع البيانات للاستجابة مع كود الخطأ
export interface ApiErrorResponse {
  success: false
  message: string
  errorCode?: string
  details?: any
  suggestions?: string[]
}

export interface ApiSuccessResponse<T = any> {
  success: true
  data: T
  message?: string
}

export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse

// دالة مساعدة لإنشاء استجابة خطأ
export function createErrorResponse(
  errorCode: string, 
  details?: any, 
  suggestions?: string[]
): ApiErrorResponse {
  return {
    success: false,
    message: ErrorMessages[errorCode as keyof typeof ErrorMessages] || ErrorMessages[ProductionErrorCodes.GENERAL.UNKNOWN_ERROR],
    errorCode,
    details,
    suggestions
  }
}

// دالة مساعدة لإنشاء استجابة نجاح
export function createSuccessResponse<T>(data: T, message?: string): ApiSuccessResponse<T> {
  return {
    success: true,
    data,
    message
  }
}
