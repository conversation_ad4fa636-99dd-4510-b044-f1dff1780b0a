import React from 'react'
import { Skeleton, Card, Row, Col, Space } from 'antd'
import styled, { keyframes } from 'styled-components'

// الحركات المتحركة
const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`

const pulse = keyframes`
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
`

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`

// الأنماط المخصصة
const SkeletonWrapper = styled.div`
  .skeleton-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: ${shimmer} 2s infinite;
  }
  
  .skeleton-pulse {
    animation: ${pulse} 1.5s ease-in-out infinite;
  }
  
  .fade-in {
    animation: ${fadeIn} 0.5s ease-out;
  }
  
  .skeleton-card {
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    overflow: hidden;
  }
  
  .skeleton-table-row {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:nth-child(even) {
      background-color: #fafafa;
    }
  }
  
  .skeleton-table-cell {
    flex: 1;
    margin-left: 16px;
    
    &:last-child {
      margin-left: 0;
    }
  }
  
  .skeleton-chart {
    display: flex;
    align-items: end;
    justify-content: space-between;
    height: 200px;
    padding: 20px;
    background: #fafafa;
    border-radius: 6px;
  }
  
  .skeleton-bar {
    background: #e0e0e0;
    border-radius: 4px;
    width: 20px;
    animation: ${pulse} 1.5s ease-in-out infinite;
    
    &:nth-child(1) { height: 60%; animation-delay: 0s; }
    &:nth-child(2) { height: 80%; animation-delay: 0.2s; }
    &:nth-child(3) { height: 40%; animation-delay: 0.4s; }
    &:nth-child(4) { height: 90%; animation-delay: 0.6s; }
    &:nth-child(5) { height: 70%; animation-delay: 0.8s; }
    &:nth-child(6) { height: 50%; animation-delay: 1s; }
  }
`

// مكون Skeleton للجداول
export const TableSkeleton: React.FC<{
  rows?: number
  columns?: number
  showHeader?: boolean
}> = ({ rows = 5, columns = 4, showHeader = true }) => {
  return (
    <SkeletonWrapper>
      <Card className="skeleton-card">
        {/* رأس الجدول */}
        {showHeader && (
          <div className="skeleton-table-row" style={{ background: '#fafafa', fontWeight: 'bold' }}>
            {Array.from({ length: columns }).map((_, index) => (
              <div key={index} className="skeleton-table-cell">
                <Skeleton.Input 
                  active 
                  size="small" 
                  style={{ width: `${60 + Math.random() * 40}%` }} 
                />
              </div>
            ))}
          </div>
        )}
        
        {/* صفوف البيانات */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="skeleton-table-row">
            {Array.from({ length: columns }).map((_, colIndex) => (
              <div key={colIndex} className="skeleton-table-cell">
                <Skeleton.Input 
                  active 
                  size="small" 
                  style={{ 
                    width: `${40 + Math.random() * 50}%`,
                    animationDelay: `${(rowIndex * columns + colIndex) * 0.1}s`
                  }} 
                />
              </div>
            ))}
          </div>
        ))}
      </Card>
    </SkeletonWrapper>
  )
}

// مكون Skeleton للبطاقات
export const CardSkeleton: React.FC<{
  count?: number
  hasAvatar?: boolean
  hasActions?: boolean
}> = ({ count = 3, hasAvatar = false, hasActions = true }) => {
  return (
    <SkeletonWrapper>
      <Row gutter={[16, 16]}>
        {Array.from({ length: count }).map((_, index) => (
          <Col key={index} xs={24} sm={12} lg={8}>
            <Card className="skeleton-card fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
              <Skeleton
                loading={true}
                avatar={hasAvatar}
                active
                paragraph={{ rows: 3 }}
              >
                {hasActions && (
                  <Space style={{ marginTop: 16 }}>
                    <Skeleton.Button active size="small" />
                    <Skeleton.Button active size="small" />
                  </Space>
                )}
              </Skeleton>
            </Card>
          </Col>
        ))}
      </Row>
    </SkeletonWrapper>
  )
}

// مكون Skeleton للنماذج
export const FormSkeleton: React.FC<{
  fields?: number
  columns?: 1 | 2 | 3
  hasSubmitButton?: boolean
}> = ({ fields = 6, columns = 2, hasSubmitButton = true }) => {
  const fieldsPerRow = columns
  const rows = Math.ceil(fields / fieldsPerRow)
  
  return (
    <SkeletonWrapper>
      <Card className="skeleton-card">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <Row key={rowIndex} gutter={16} style={{ marginBottom: 24 }}>
            {Array.from({ length: Math.min(fieldsPerRow, fields - rowIndex * fieldsPerRow) }).map((_, colIndex) => (
              <Col key={colIndex} span={24 / fieldsPerRow}>
                <div style={{ marginBottom: 8 }}>
                  <Skeleton.Input 
                    active 
                    size="small" 
                    style={{ width: '40%', marginBottom: 8 }} 
                  />
                </div>
                <Skeleton.Input 
                  active 
                  style={{ 
                    width: '100%',
                    animationDelay: `${(rowIndex * fieldsPerRow + colIndex) * 0.1}s`
                  }} 
                />
              </Col>
            ))}
          </Row>
        ))}
        
        {hasSubmitButton && (
          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Space>
              <Skeleton.Button active />
              <Skeleton.Button active />
            </Space>
          </div>
        )}
      </Card>
    </SkeletonWrapper>
  )
}

// مكون Skeleton للإحصائيات
export const StatsSkeleton: React.FC<{
  count?: number
}> = ({ count = 4 }) => {
  return (
    <SkeletonWrapper>
      <Row gutter={16}>
        {Array.from({ length: count }).map((_, index) => (
          <Col key={index} xs={24} sm={12} lg={6}>
            <Card className="skeleton-card fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
              <div style={{ textAlign: 'center' }}>
                <Skeleton.Avatar 
                  active 
                  size="large" 
                  shape="circle" 
                  style={{ marginBottom: 16 }}
                />
                <div>
                  <Skeleton.Input 
                    active 
                    size="large" 
                    style={{ width: '80%', marginBottom: 8 }} 
                  />
                  <Skeleton.Input 
                    active 
                    size="small" 
                    style={{ width: '60%' }} 
                  />
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </SkeletonWrapper>
  )
}

// مكون Skeleton للرسوم البيانية
export const ChartSkeleton: React.FC<{
  type?: 'bar' | 'line' | 'pie'
  height?: number
}> = ({ type = 'bar', height = 200 }) => {
  return (
    <SkeletonWrapper>
      <Card className="skeleton-card">
        <div style={{ marginBottom: 16 }}>
          <Skeleton.Input active style={{ width: '30%' }} />
        </div>
        
        {type === 'bar' && (
          <div className="skeleton-chart" style={{ height }}>
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="skeleton-bar" />
            ))}
          </div>
        )}
        
        {type === 'line' && (
          <div style={{ height, background: '#fafafa', borderRadius: 6, position: 'relative' }}>
            <svg width="100%" height="100%" style={{ position: 'absolute' }}>
              <path
                d="M 20 150 Q 80 100 140 120 T 260 80 T 380 110"
                stroke="#e0e0e0"
                strokeWidth="3"
                fill="none"
                className="skeleton-pulse"
              />
              {Array.from({ length: 5 }).map((_, index) => (
                <circle
                  key={index}
                  cx={20 + index * 80}
                  cy={150 - Math.random() * 70}
                  r="4"
                  fill="#e0e0e0"
                  className="skeleton-pulse"
                  style={{ animationDelay: `${index * 0.2}s` }}
                />
              ))}
            </svg>
          </div>
        )}
        
        {type === 'pie' && (
          <div style={{ 
            height, 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center',
            background: '#fafafa',
            borderRadius: 6
          }}>
            <div style={{
              width: height * 0.7,
              height: height * 0.7,
              borderRadius: '50%',
              background: 'conic-gradient(#e0e0e0 0deg 120deg, #f0f0f0 120deg 240deg, #e8e8e8 240deg 360deg)',
              animation: `${pulse} 2s ease-in-out infinite`
            }} />
          </div>
        )}
      </Card>
    </SkeletonWrapper>
  )
}

// مكون Skeleton للقوائم
export const ListSkeleton: React.FC<{
  items?: number
  hasAvatar?: boolean
  hasActions?: boolean
}> = ({ items = 5, hasAvatar = true, hasActions = true }) => {
  return (
    <SkeletonWrapper>
      <Card className="skeleton-card">
        {Array.from({ length: items }).map((_, index) => (
          <div 
            key={index} 
            className="skeleton-table-row fade-in"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            {hasAvatar && (
              <Skeleton.Avatar active size="default" style={{ marginLeft: 16 }} />
            )}
            
            <div style={{ flex: 1 }}>
              <Skeleton.Input 
                active 
                style={{ width: '60%', marginBottom: 8 }} 
              />
              <Skeleton.Input 
                active 
                size="small" 
                style={{ width: '40%' }} 
              />
            </div>
            
            {hasActions && (
              <Space>
                <Skeleton.Button active size="small" />
                <Skeleton.Button active size="small" />
              </Space>
            )}
          </div>
        ))}
      </Card>
    </SkeletonWrapper>
  )
}

// مكون Skeleton للصفحة الكاملة
export const PageSkeleton: React.FC<{
  hasHeader?: boolean
  hasStats?: boolean
  hasChart?: boolean
  hasTable?: boolean
}> = ({ 
  hasHeader = true, 
  hasStats = true, 
  hasChart = true, 
  hasTable = true 
}) => {
  return (
    <SkeletonWrapper>
      <div style={{ padding: 24 }}>
        {/* رأس الصفحة */}
        {hasHeader && (
          <div style={{ marginBottom: 24 }}>
            <Skeleton.Input 
              active 
              size="large" 
              style={{ width: '30%', marginBottom: 8 }} 
            />
            <Skeleton.Input 
              active 
              style={{ width: '50%' }} 
            />
          </div>
        )}
        
        {/* الإحصائيات */}
        {hasStats && (
          <div style={{ marginBottom: 24 }}>
            <StatsSkeleton count={4} />
          </div>
        )}
        
        {/* الرسم البياني */}
        {hasChart && (
          <div style={{ marginBottom: 24 }}>
            <ChartSkeleton type="bar" height={300} />
          </div>
        )}
        
        {/* الجدول */}
        {hasTable && (
          <TableSkeleton rows={8} columns={5} />
        )}
      </div>
    </SkeletonWrapper>
  )
}

// مكون Skeleton مخصص
export const CustomSkeleton: React.FC<{
  width?: string | number
  height?: string | number
  shape?: 'default' | 'circle' | 'square'
  animation?: 'pulse' | 'shimmer' | 'none'
  delay?: number
}> = ({ 
  width = '100%', 
  height = 20, 
  shape = 'default', 
  animation = 'pulse',
  delay = 0
}) => {
  const getShapeStyles = () => {
    const baseStyles = {
      width,
      height,
      background: '#f0f0f0',
      animationDelay: `${delay}s`
    }
    
    switch (shape) {
      case 'circle':
        return { ...baseStyles, borderRadius: '50%' }
      case 'square':
        return { ...baseStyles, borderRadius: 4 }
      default:
        return { ...baseStyles, borderRadius: 6 }
    }
  }
  
  const getAnimationClass = () => {
    switch (animation) {
      case 'shimmer':
        return 'skeleton-shimmer'
      case 'pulse':
        return 'skeleton-pulse'
      default:
        return ''
    }
  }
  
  return (
    <SkeletonWrapper>
      <div 
        className={getAnimationClass()}
        style={getShapeStyles()}
      />
    </SkeletonWrapper>
  )
}

export default {
  Table: TableSkeleton,
  Card: CardSkeleton,
  Form: FormSkeleton,
  Stats: StatsSkeleton,
  Chart: ChartSkeleton,
  List: ListSkeleton,
  Page: PageSkeleton,
  Custom: CustomSkeleton
}
