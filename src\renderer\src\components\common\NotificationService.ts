import { notification } from 'antd'
import dayjs from 'dayjs'
import { SafeLogger as Logger } from '../../utils/logger'

export interface NotificationData {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  date: string
  time: string
  status: 'pending' | 'sent' | 'dismissed'
  category: 'paint' | 'sales' | 'inventory' | 'general'
  reference_id?: number
  reference_type?: string
  created_at: string
  scheduled_at: string
}

export class NotificationService {
  private static readonly STORAGE_KEY = 'app_notifications'
  private static readonly SETTINGS_KEY = 'notification_settings'

  // إعدادات التنبيهات الافتراضية
  private static defaultSettings = {
    enabled: true,
    sound: true,
    desktop: true,
    email: false,
    categories: {
      paint: true,
      sales: true,
      inventory: true,
      general: true
    },
    timing: {
      delivery_reminder: 1, // يوم واحد قبل التسليم
      followup_reminder: 3, // 3 أيام بعد التسليم
      overdue_reminder: 1 // يوم واحد بعد التأخير
    }
  }

  // حفظ تنبيه جديد
  static saveNotification(notificationData: Omit<NotificationData, 'id' | 'created_at'>): NotificationData {
    const newNotification: NotificationData = {
      ...notificationData,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      created_at: dayjs().format('YYYY-MM-DD HH:mm:ss')
    }

    const notifications = this.getAllNotifications()
    notifications.push(newNotification)
    
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(notifications))
    
    // جدولة التنبيه إذا كان في المستقبل
    this.scheduleNotification(newNotification)
    
    return newNotification
  }

  // جلب جميع التنبيهات
  static getAllNotifications(): NotificationData[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في جلب التنبيهات:', error)
      return []
    }
  }

  // جلب التنبيهات المعلقة
  static getPendingNotifications(): NotificationData[] {
    return this.getAllNotifications().filter(n => n.status === 'pending')
  }

  // جلب التنبيهات حسب الفئة
  static getNotificationsByCategory(category: string): NotificationData[] {
    return this.getAllNotifications().filter(n => n.category === category)
  }

  // تحديث حالة التنبيه
  static updateNotificationStatus(id: string, status: NotificationData['status']): void {
    const notifications = this.getAllNotifications()
    const index = notifications.findIndex(n => n.id === id)
    
    if (index !== -1) {
      notifications[index].status = status
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(notifications))
    }
  }

  // حذف تنبيه
  static deleteNotification(id: string): void {
    const notifications = this.getAllNotifications().filter(n => n.id !== id)
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(notifications))
  }

  // جدولة التنبيه
  static scheduleNotification(notificationData: NotificationData): void {
    const scheduledTime = dayjs(`${notificationData.date} ${notificationData.time}`)
    const now = dayjs()
    
    if (scheduledTime.isAfter(now)) {
      const delay = scheduledTime.diff(now)
      
      setTimeout(() => {
        this.showNotification(notificationData)
        this.updateNotificationStatus(notificationData.id, 'sent')
      }, delay)
    }
  }

  // عرض التنبيه
  static showNotification(notificationData: NotificationData): void {
    const settings = this.getSettings()
    
    if (!settings.enabled || !settings.categories[notificationData.category]) {
      return
    }

    // عرض التنبيه في التطبيق
    notification[notificationData.type]({
      message: notificationData.title,
      description: notificationData.message,
      placement: 'topRight',
      duration: 6,
      onClick: () => {
        // يمكن إضافة منطق للانتقال إلى الصفحة المرتبطة
        Logger.info('NotificationService', 'تم النقر على التنبيه:', notificationData)
      }
    })

    // تنبيه سطح المكتب إذا كان مفعلاً
    if (settings.desktop && 'Notification' in window) {
      this.showDesktopNotification(notificationData)
    }

    // تشغيل الصوت إذا كان مفعلاً
    if (settings.sound) {
      this.playNotificationSound()
    }
  }

  // تنبيه سطح المكتب
  static showDesktopNotification(notificationData: NotificationData): void {
    if (Notification.permission === 'granted') {
      new Notification(notificationData.title, {
        body: notificationData.message,
        icon: '/icon.png', // مسار أيقونة التطبيق
        tag: notificationData.id
      })
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          this.showDesktopNotification(notificationData)
        }
      })
    }
  }

  // تشغيل صوت التنبيه
  static playNotificationSound(): void {
    try {
      const audio = new Audio('/notification-sound.mp3') // مسار ملف الصوت
      audio.volume = 0.5
      audio.play().catch(error => {
        Logger.info('NotificationService', 'لا يمكن تشغيل صوت التنبيه:', error)
      })
    } catch (error) {
      Logger.info('NotificationService', 'خطأ في تشغيل الصوت:', error)
    }
  }

  // جلب الإعدادات
  static getSettings() {
    try {
      const stored = localStorage.getItem(this.SETTINGS_KEY)
      return stored ? { ...this.defaultSettings, ...JSON.parse(stored) } : this.defaultSettings
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في جلب إعدادات التنبيهات:', error)
      return this.defaultSettings
    }
  }

  // حفظ الإعدادات
  static saveSettings(settings: any): void {
    try {
      localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings))
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في حفظ إعدادات التنبيهات:', error)
    }
  }

  // فحص التنبيهات المجدولة
  static checkScheduledNotifications(): void {
    const notifications = this.getPendingNotifications()
    const now = dayjs()

    notifications.forEach(notification => {
      const scheduledTime = dayjs(`${notification.date} ${notification.time}`)
      
      if (scheduledTime.isSameOrBefore(now)) {
        this.showNotification(notification)
        this.updateNotificationStatus(notification.id, 'sent')
      }
    })
  }

  // تنبيهات تلقائية لأوامر الدهان
  static createPaintOrderNotifications(order: any): void {
    const settings = this.getSettings()
    
    if (order.expected_completion_date) {
      const deliveryDate = dayjs(order.expected_completion_date)
      
      // تنبيه قبل التسليم
      const reminderDate = deliveryDate.subtract(settings.timing.delivery_reminder, 'day')
      if (reminderDate.isAfter(dayjs())) {
        this.saveNotification({
          title: 'تنبيه تسليم أمر دهان',
          message: `موعد تسليم أمر الدهان ${order.order_number} للعميل ${order.customer_name} خلال ${settings.timing.delivery_reminder} يوم`,
          type: 'warning',
          date: reminderDate.format('YYYY-MM-DD'),
          time: '09:00',
          status: 'pending',
          category: 'paint',
          reference_id: order.id,
          reference_type: 'paint_order',
          scheduled_at: reminderDate.format('YYYY-MM-DD HH:mm:ss')
        })
      }

      // تنبيه متابعة بعد التسليم
      const followupDate = deliveryDate.add(settings.timing.followup_reminder, 'day')
      this.saveNotification({
        title: 'متابعة أمر دهان',
        message: `متابعة مع العميل ${order.customer_name} حول أمر الدهان ${order.order_number}`,
        type: 'info',
        date: followupDate.format('YYYY-MM-DD'),
        time: '10:00',
        status: 'pending',
        category: 'paint',
        reference_id: order.id,
        reference_type: 'paint_order',
        scheduled_at: followupDate.format('YYYY-MM-DD HH:mm:ss')
      })
    }
  }

  // بدء خدمة التنبيهات
  static startNotificationService(): void {
    // فحص التنبيهات كل دقيقة
    setInterval(() => {
      this.checkScheduledNotifications()
    }, 60000)

    // طلب إذن التنبيهات
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }

  // تنظيف التنبيهات القديمة
  static cleanupOldNotifications(daysOld: number = 30): void {
    const cutoffDate = dayjs().subtract(daysOld, 'day')
    const notifications = this.getAllNotifications().filter(n => 
      dayjs(n.created_at).isAfter(cutoffDate)
    )
    
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(notifications))
  }
}

// بدء خدمة التنبيهات عند تحميل الملف
if (typeof window !== 'undefined') {
  NotificationService.startNotificationService()
}
