import React from 'react';
import { Tag, Typography, Progress } from 'antd';
import { CalendarOutlined, RiseOutlined } from '@ant-design/icons';
import UniversalReport from './UniversalReport';
import { DateUtils } from '../../utils/dateConfig';
import type { ReportData, ReportType } from '../../types/reports';
import { createDefaultFilters } from '../../types/reports';

const { Text } = Typography;

const MonthlySalesReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير المبيعات الشهرية...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getMonthlySalesReport(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const monthlySalesData = response.data;

      // معالجة البيانات
      const processedData = monthlySalesData.map((item: any, index: number) => ({
        ...item,
        key: item.month || index,
        month_name: getMonthName(item.month, item.year),
        avg_invoice_value: item.total_invoices > 0 ? item.total_sales / item.total_invoices : 0,
        growth_rate: item.growth_rate || 0,
        target_achievement: item.target_amount > 0 ? (item.total_sales / item.target_amount) * 100 : 0
      }));

      // حساب الإحصائيات
      const totalMonths = processedData.length;
      const totalSales = processedData.reduce((sum: number, item: any) => sum + item.total_sales, 0);
      const totalInvoices = processedData.reduce((sum: number, item: any) => sum + item.total_invoices, 0);
      const totalCustomers = processedData.reduce((sum: number, item: any) => sum + item.unique_customers, 0);
      const avgMonthlySales = totalMonths > 0 ? totalSales / totalMonths : 0;
      const avgGrowthRate = totalMonths > 0 ? 
        processedData.reduce((sum: number, item: any) => sum + item.growth_rate, 0) / totalMonths : 0;

      const bestMonth = processedData.length > 0 ?
        processedData.reduce((prev: any, current: any) => (prev.total_sales > current.total_sales) ? prev : current) : null;

      const summary = {
        totalMonths,
        totalSales: Math.round(totalSales * 100) / 100,
        totalInvoices,
        totalCustomers,
        avgMonthlySales: Math.round(avgMonthlySales * 100) / 100,
        avgGrowthRate: Math.round(avgGrowthRate * 100) / 100,
        bestMonth: bestMonth?.month_name || 'غير محدد',
        bestMonthSales: bestMonth?.total_sales || 0
      };

      console.log('✅ تم إنشاء تقرير المبيعات الشهرية بنجاح');

      return {
        title: 'تقرير المبيعات الشهرية',
        data: processedData,
        columns,
        summary,
        metadata: {
          reportType: 'monthly_sales' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: processedData.length
        }
      };
    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير المبيعات الشهرية:', error);
      throw error;
    }
  };

  // دالة للحصول على اسم الشهر
  const getMonthName = (month: number, year: number): string => {
    const monthNames = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return `${monthNames[month - 1]} ${year}`;
  };

  // إعداد الأعمدة
  const columns = [
    {
      title: 'الشهر',
      dataIndex: 'month_name',
      key: 'month_name',
      width: 150,
      render: (name: string) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <CalendarOutlined style={{ marginLeft: '8px', color: '#1890ff' }} />
          <Text strong>{name}</Text>
        </div>
      )
    },
    {
      title: 'إجمالي المبيعات (₪)',
      dataIndex: 'total_sales',
      key: 'total_sales',
      width: 150,
      align: 'right' as const,
      render: (sales: number) => (
        <Text strong style={{ color: '#52c41a' }}>
          {sales.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'عدد الفواتير',
      dataIndex: 'total_invoices',
      key: 'total_invoices',
      width: 120,
      align: 'center' as const,
      render: (count: number) => (
        <Tag color="blue">{count}</Tag>
      )
    },
    {
      title: 'عدد العملاء',
      dataIndex: 'unique_customers',
      key: 'unique_customers',
      width: 120,
      align: 'center' as const,
      render: (count: number) => (
        <Tag color="green">{count}</Tag>
      )
    },
    {
      title: 'متوسط قيمة الفاتورة (₪)',
      dataIndex: 'avg_invoice_value',
      key: 'avg_invoice_value',
      width: 150,
      align: 'right' as const,
      render: (value: number) => (
        <Text style={{ color: '#fa8c16' }}>
          {value.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'معدل النمو',
      dataIndex: 'growth_rate',
      key: 'growth_rate',
      width: 120,
      align: 'center' as const,
      render: (rate: number) => (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <RiseOutlined
            style={{ 
              marginLeft: '4px', 
              color: rate >= 0 ? '#52c41a' : '#ff4d4f' 
            }} 
          />
          <Tag color={rate >= 0 ? 'green' : 'red'}>
            {rate >= 0 ? '+' : ''}{rate.toFixed(1)}%
          </Tag>
        </div>
      )
    },
    {
      title: 'تحقيق الهدف',
      dataIndex: 'target_achievement',
      key: 'target_achievement',
      width: 150,
      align: 'center' as const,
      render: (achievement: number) => (
        <Progress
          percent={Math.round(achievement)}
          size="small"
          strokeColor={achievement >= 100 ? '#52c41a' : achievement >= 80 ? '#fa8c16' : '#ff4d4f'}
          format={() => `${achievement.toFixed(1)}%`}
        />
      )
    }
  ];

  return (
    <UniversalReport
      reportType={'monthly_sales' as ReportType}
      title="تقرير المبيعات الشهرية"
      description="تقرير شامل للمبيعات الشهرية مع تحليل النمو وتحقيق الأهداف"
      onGenerateReport={generateReport}
      defaultFilters={createDefaultFilters('monthly_sales')}
      showDateRange={true}
      showCustomerFilter={false}
      showAmountRangeFilter={true}
      showAdvancedSearch={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default MonthlySalesReport;
