// Global type declarations for ZET.IA

declare module 'sql.js' {
  interface Database {
    run(sql: string, params?: any[]): void;
    exec(sql: string): any[];
    prepare(sql: string): Statement;
    close(): void;
    export(): Uint8Array;
  }

  interface Statement {
    run(params?: any[]): void;
    get(params?: any[]): any;
    all(params?: any[]): any[];
    bind(params?: any[]): void;
    step(): boolean;
    getColumnNames(): string[];
    free(): void;
  }

  interface SqlJsStatic {
    Database: {
      new (data?: ArrayLike<number> | Buffer | null): Database;
    };
  }

  function initSqlJs(config?: {
    locateFile?: (filename: string) => string;
  }): Promise<SqlJsStatic>;

  export = initSqlJs;
}

declare module 'electron' {
  export * from 'electron/main';
}

// Window interface extensions
declare global {
  interface Window {
    electronAPI: {
      // Database operations
      getDatabaseStatus: () => Promise<any>;
      checkHealth: () => Promise<any>;
      
      // Event listeners
      on: (channel: string, callback: (...args: any[]) => void) => void;
      removeListener: (channel: string, callback: (...args: any[]) => void) => void;
      
      // Other APIs
      [key: string]: any;
    };
  }
}

export {};
