import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Select,
  Input,
  Space,
  Row,
  Col,
  Statistic,
  DatePicker,
  Tag,
  Descriptions,
  Timeline,
  Divider
, App} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import { useCurrentUser } from '../../hooks/useCurrentUser'
import {
  SwapOutlined,
  HistoryOutlined,
  ArrowLeftOutlined,
  CreditCardOutlined,
  UserOutlined,
  BankOutlined,
  CalendarOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

interface CheckTransferManagementProps {
  onBack: () => void
}

const CheckTransferManagement: React.FC<CheckTransferManagementProps> = ({ onBack }) => {
  const { message: messageApi } = App.useApp()
  const { userId } = useCurrentUser()
  const [transferableChecks, setTransferableChecks] = useState([])
  const [transfers, setTransfers] = useState([])
  const [entities, setEntities] = useState<any>({ customers: [], suppliers: [], all: [] })
  const [loading, setLoading] = useState(false)
  const [transferModalVisible, setTransferModalVisible] = useState(false)
  const [historyModalVisible, setHistoryModalVisible] = useState(false)
  const [selectedCheck, setSelectedCheck] = useState<any>(null)
  const [checkHistory, setCheckHistory] = useState<any>({ check: null, transfers: [] })
  const [form] = Form.useForm()

  useEffect(() => {
    loadTransferableChecks()
    loadTransfers()
    loadEntities()
  }, [])

  const loadTransferableChecks = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.getTransferableChecks()
      if (response.success) {
        setTransferableChecks(response.data)
      } else {
        messageApi.error('فشل في تحميل الشيكات القابلة للتحويل')
      }
    } catch (error) {
      messageApi.error('خطأ في تحميل الشيكات القابلة للتحويل')
    }
    setLoading(false)
  }

  const loadTransfers = async () => {
    try {
      const response = await window.electronAPI.getCheckTransfers()
      if (response.success) {
        setTransfers(response.data)
      }
    } catch (error) {
      Logger.error('CheckTransferManagement', 'خطأ في تحميل التحويلات:', error)
    }
  }

  const loadEntities = async () => {
    try {
      const response = await window.electronAPI.getEntitiesForTransfer()
      if (response.success) {
        setEntities(response.data)
      }
    } catch (error) {
      Logger.error('CheckTransferManagement', 'خطأ في تحميل الكيانات:', error)
    }
  }

  const handleTransfer = async (values: any) => {
    try {
      const transferData = {
        check_id: selectedCheck.id,
        from_entity_type: selectedCheck.entity_type || 'other',
        from_entity_id: selectedCheck.entity_id || null,
        from_entity_name: selectedCheck.current_holder || selectedCheck.original_payer || selectedCheck.payee_name || 'غير محدد',
        to_entity_type: values.to_entity_type,
        to_entity_id: values.to_entity_id,
        to_entity_name: values.to_entity_name,
        transfer_date: values.transfer_date.format('YYYY-MM-DD'),
        transfer_reason: values.transfer_reason,
        notes: values.notes,
        created_by: userId || 1
      }

      const response = await window.electronAPI.transferCheck(transferData)
      if (response.success) {
        messageApi.success('تم تحويل الشيك بنجاح')
        setTransferModalVisible(false)
        form.resetFields()
        loadTransferableChecks()
        loadTransfers()
      } else {
        messageApi.error(response.message || 'فشل في تحويل الشيك')
      }
    } catch (error) {
      messageApi.error('خطأ في تحويل الشيك')
    }
  }

  const showTransferModal = (check: any) => {
    setSelectedCheck(check)
    setTransferModalVisible(true)
    form.setFieldsValue({
      transfer_date: dayjs()
    })
  }

  const showCheckHistory = async (check: any) => {
    try {
      const response = await window.electronAPI.getCheckHistory(check.id)
      if (response.success) {
        setCheckHistory(response.data)
        setHistoryModalVisible(true)
      } else {
        messageApi.error('فشل في جلب تاريخ الشيك')
      }
    } catch (error) {
      messageApi.error('خطأ في جلب تاريخ الشيك')
    }
  }

  const handleEntityChange = (entityId: number) => {
    const entity = entities.all.find((e: any) => e.id === entityId)
    if (entity) {
      form.setFieldsValue({
        to_entity_type: entity.type,
        to_entity_name: entity.name
      })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'issued': return 'blue'
      case 'transferred': return 'orange'
      case 'cashed': return 'green'
      case 'bounced': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'issued': return 'مُصدر'
      case 'transferred': return 'محول'
      case 'cashed': return 'محصل'
      case 'bounced': return 'مرتد'
      default: return status
    }
  }

  const checksColumns = [
    {
      title: 'رقم الشيك',
      dataIndex: 'check_number',
      key: 'check_number',
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ₪ {amount?.toLocaleString() || 0}
        </span>
      ),
    },
    {
      title: 'الدافع الأصلي',
      dataIndex: 'original_payer',
      key: 'original_payer',
      render: (payer: string) => payer || 'غير محدد'
    },
    {
      title: 'الحامل الحالي',
      dataIndex: 'current_holder',
      key: 'current_holder',
      render: (holder: string, record: any) => holder || record.original_payer || 'غير محدد'
    },
    {
      title: 'تاريخ الاستحقاق',
      dataIndex: 'due_date',
      key: 'due_date',
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: any) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<SwapOutlined />}
            onClick={() => showTransferModal(record)}
          >
            تحويل
          </Button>
          <Button
            size="small"
            icon={<HistoryOutlined />}
            onClick={() => showCheckHistory(record)}
          >
            التاريخ
          </Button>
        </Space>
      ),
    },
  ]

  const transfersColumns = [
    {
      title: 'رقم الشيك',
      dataIndex: 'check_number',
      key: 'check_number',
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ₪ {amount?.toLocaleString() || 0}
        </span>
      ),
    },
    {
      title: 'من',
      dataIndex: 'from_entity_name',
      key: 'from_entity_name',
    },
    {
      title: 'إلى',
      dataIndex: 'to_entity_name',
      key: 'to_entity_name',
    },
    {
      title: 'تاريخ التحويل',
      dataIndex: 'transfer_date',
      key: 'transfer_date',
    },
    {
      title: 'السبب',
      dataIndex: 'transfer_reason',
      key: 'transfer_reason',
    },
    {
      title: 'المنشئ',
      dataIndex: 'created_by_name',
      key: 'created_by_name',
    },
  ]

  const stats = {
    totalTransferable: transferableChecks.length,
    totalTransfers: transfers.length,
    totalAmount: transferableChecks.reduce((sum: number, check: any) => sum + (check.amount || 0), 0)
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>🔄 إدارة تحويل الشيكات</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            تحويل الشيكات بين العملاء والموردين مع تتبع كامل للعمليات
          </p>
        </div>
        <Button 
          type="default" 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
        >
          العودة
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="الشيكات القابلة للتحويل"
              value={stats.totalTransferable}
              valueStyle={{ color: '#1890ff' }}
              prefix={<CreditCardOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="إجمالي التحويلات"
              value={stats.totalTransfers}
              valueStyle={{ color: '#52c41a' }}
              prefix={<SwapOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="إجمالي المبالغ"
              value={stats.totalAmount}
              valueStyle={{ color: '#13c2c2' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      {/* الشيكات القابلة للتحويل */}
      <Card 
        title="الشيكات القابلة للتحويل"
        style={{ marginBottom: '24px' }}
      >
        <Table
          columns={checksColumns}
          dataSource={transferableChecks}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* سجل التحويلات */}
      <Card title="سجل التحويلات">
        <Table
          columns={transfersColumns}
          dataSource={transfers}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* نموذج تحويل الشيك */}
      <Modal
        title="تحويل شيك"
        open={transferModalVisible}
        onCancel={() => {
          setTransferModalVisible(false)
          setSelectedCheck(null)
          form.resetFields()
        }}
        footer={null}
        width={700}
      >
        {selectedCheck && (
          <>
            <Descriptions title="بيانات الشيك" bordered size="small" style={{ marginBottom: '24px' }}>
              <Descriptions.Item label="رقم الشيك">{selectedCheck.check_number}</Descriptions.Item>
              <Descriptions.Item label="المبلغ">
                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                  ₪ {selectedCheck.amount?.toLocaleString() || 0}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="البنك">{selectedCheck.bank_name}</Descriptions.Item>
              <Descriptions.Item label="الدافع الأصلي">{selectedCheck.original_payer || 'غير محدد'}</Descriptions.Item>
              <Descriptions.Item label="الحامل الحالي">{selectedCheck.current_holder || selectedCheck.original_payer || 'غير محدد'}</Descriptions.Item>
              <Descriptions.Item label="تاريخ الاستحقاق">{selectedCheck.due_date}</Descriptions.Item>
            </Descriptions>

            <Form
              form={form}
              layout="vertical"
              onFinish={handleTransfer}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="to_entity_id"
                    label="المحول إليه"
                    rules={[{ required: true, message: 'يرجى اختيار المحول إليه' }]}
                  >
                    <Select
                      placeholder="اختر العميل أو المورد"
                      onChange={handleEntityChange}
                      showSearch
                      filterOption={(input, option) =>
                        (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                      }
                    >
                      <Select.OptGroup label="العملاء">
                        {entities.customers.map((customer: any) => (
                          <Option key={`customer-${customer.id}`} value={customer.id}>
                            <UserOutlined /> {customer.name}
                          </Option>
                        ))}
                      </Select.OptGroup>
                      <Select.OptGroup label="الموردين">
                        {entities.suppliers.map((supplier: any) => (
                          <Option key={`supplier-${supplier.id}`} value={supplier.id}>
                            <BankOutlined /> {supplier.name}
                          </Option>
                        ))}
                      </Select.OptGroup>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="transfer_date"
                    label="تاريخ التحويل"
                    rules={[{ required: true, message: 'يرجى اختيار تاريخ التحويل' }]}
                  >
                    <DatePicker
                      style={{ width: '100%' }}
                      placeholder="اختر تاريخ التحويل"
                      format="YYYY-MM-DD"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item name="to_entity_type" style={{ display: 'none' }}>
                <Input />
              </Form.Item>

              <Form.Item name="to_entity_name" style={{ display: 'none' }}>
                <Input />
              </Form.Item>

              <Form.Item
                name="transfer_reason"
                label="سبب التحويل"
                rules={[{ required: true, message: 'يرجى إدخال سبب التحويل' }]}
              >
                <Select placeholder="اختر سبب التحويل">
                  <Option value="payment_to_supplier">دفع للمورد</Option>
                  <Option value="debt_settlement">تسوية دين</Option>
                  <Option value="business_transaction">معاملة تجارية</Option>
                  <Option value="other">أخرى</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="notes"
                label="ملاحّات"
              >
                <TextArea
                  placeholder="ملاحّات إضافية حول التحويل"
                  rows={3}
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    تحويل الشيك
                  </Button>
                  <Button onClick={() => {
                    setTransferModalVisible(false)
                    setSelectedCheck(null)
                    form.resetFields()
                  }}>
                    إلغاء
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </>
        )}
      </Modal>

      {/* نموذج تاريخ الشيك */}
      <Modal
        title="تاريخ الشيك"
        open={historyModalVisible}
        onCancel={() => setHistoryModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setHistoryModalVisible(false)}>
            إغلاق
          </Button>
        ]}
        width={800}
      >
        {checkHistory.check && (
          <>
            <Descriptions title="بيانات الشيك" bordered size="small" style={{ marginBottom: '24px' }}>
              <Descriptions.Item label="رقم الشيك">{checkHistory.check.check_number}</Descriptions.Item>
              <Descriptions.Item label="المبلغ">
                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                  ₪ {checkHistory.check.amount?.toLocaleString() || 0}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="البنك">{checkHistory.check.bank_name}</Descriptions.Item>
              <Descriptions.Item label="تاريخ الإصدار">{checkHistory.check.issue_date}</Descriptions.Item>
              <Descriptions.Item label="تاريخ الاستحقاق">{checkHistory.check.due_date}</Descriptions.Item>
              <Descriptions.Item label="الحالة">
                <Tag color={getStatusColor(checkHistory.check.status)}>
                  {getStatusText(checkHistory.check.status)}
                </Tag>
              </Descriptions.Item>
            </Descriptions>

            <Divider>تاريخ التحويلات</Divider>

            {checkHistory.transfers && checkHistory.transfers.length > 0 ? (
              <Timeline>
                <Timeline.Item color="blue" dot={<CalendarOutlined />}>
                  <strong>إصدار الشيك</strong>
                  <br />
                  <span style={{ color: '#666' }}>
                    تاريخ الإصدار: {checkHistory.check.issue_date}
                  </span>
                  <br />
                  <span style={{ color: '#666' }}>
                    الدافع الأصلي: {checkHistory.check.original_payer || 'غير محدد'}
                  </span>
                </Timeline.Item>

                {checkHistory.transfers.map((transfer: any, index: number) => (
                  <Timeline.Item
                    key={transfer.id}
                    color="orange"
                    dot={<SwapOutlined />}
                  >
                    <strong>تحويل #{index + 1}</strong>
                    <br />
                    <span style={{ color: '#666' }}>
                      من: {transfer.from_entity_name} → إلى: {transfer.to_entity_name}
                    </span>
                    <br />
                    <span style={{ color: '#666' }}>
                      تاريخ التحويل: {transfer.transfer_date}
                    </span>
                    <br />
                    <span style={{ color: '#666' }}>
                      السبب: {transfer.transfer_reason}
                    </span>
                    {transfer.notes && (
                      <>
                        <br />
                        <span style={{ color: '#666' }}>
                          ملاحّات: {transfer.notes}
                        </span>
                      </>
                    )}
                    <br />
                    <span style={{ color: '#999', fontSize: '12px' }}>
                      بواسطة: {transfer.created_by_name}
                    </span>
                  </Timeline.Item>
                ))}
              </Timeline>
            ) : (
              <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                لا توجد تحويلات لهذا الشيك
              </div>
            )}
          </>
        )}
      </Modal>
    </div>
  )
}

export default CheckTransferManagement
