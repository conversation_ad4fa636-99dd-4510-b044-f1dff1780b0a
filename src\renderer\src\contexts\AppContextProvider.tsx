import React, { useState, useEffect, ReactNode } from 'react'
import { AppContext, AppContextType } from '../hooks/useAppContext'
import { SafeLogger as Logger } from '../utils/logger'

interface AppContextProviderProps {
  children: ReactNode
}

export const AppContextProvider: React.FC<AppContextProviderProps> = ({ children }) => {
  // State management
  const [currentUser, setCurrentUser] = useState<any | null>(null)
  const [theme, setTheme] = useState<'light' | 'dark'>('light')
  const [language, setLanguage] = useState<'ar' | 'en'>('ar')
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [permissions, _setPermissions] = useState<string[]>([])
  const [settings, setSettings] = useState<Record<string, any>>({})

  // Initialize app context
  useEffect(() => {
    // Load user preferences from localStorage or API
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' || 'light'
    const savedLanguage = localStorage.getItem('language') as 'ar' | 'en' || 'ar'
    
    setTheme(savedTheme)
    setLanguage(savedLanguage)
    
    // Load user data if available
    const savedUser = localStorage.getItem('currentUser')
    if (savedUser) {
      try {
        setCurrentUser(JSON.parse(savedUser))
      } catch (error) {
        Logger.error('AppContextProvider', 'Error parsing saved user data:', error)
      }
    }
  }, [])

  // Save preferences when they change
  useEffect(() => {
    localStorage.setItem('theme', theme)
  }, [theme])

  useEffect(() => {
    localStorage.setItem('language', language)
  }, [language])

  useEffect(() => {
    if (currentUser) {
      localStorage.setItem('currentUser', JSON.stringify(currentUser))
    } else {
      localStorage.removeItem('currentUser')
    }
  }, [currentUser])

  // Helper functions
  const refreshData = () => {
    setIsLoading(true)
    // Implement data refresh logic here
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }

  const hasPermission = (permission: string): boolean => {
    return permissions.includes(permission) || permissions.includes('admin')
  }

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  // Context value
  const contextValue: AppContextType = {
    currentUser,
    setCurrentUser,
    theme,
    setTheme,
    language,
    setLanguage,
    isLoading,
    setIsLoading,
    error,
    setError,
    refreshData,
    permissions,
    hasPermission,
    settings,
    updateSetting
  }

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  )
}

export default AppContextProvider
