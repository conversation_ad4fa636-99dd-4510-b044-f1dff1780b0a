import * as React from 'react'
import { useEffect, useCallback, useReducer, useMemo } from 'react'
import { Table, Button, Modal, Form, Input, Space, message, Popconfirm, Card, Statistic, Row, Col, Switch, InputNumber, App } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, UserOutlined, PhoneOutlined, MailOutlined, BarcodeOutlined, FileTextOutlined } from '@ant-design/icons'
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig'
import useErrorHandler from '../../hooks/useErrorHandler'
import LoadingWrapper from '../common/LoadingWrapper'
import { getCurrencySymbol } from '../../utils/settings'
import { SafeLogger as Logger } from '../../utils/logger'

import * as XLSX from 'xlsx'

interface Supplier {
  id: number
  code: string
  name: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  tax_number?: string
  payment_terms: number
  credit_limit: number
  is_active: boolean
  created_at: string
}

interface SupplierManagementProps {
  onBack: () => void
}

// تعريف حالة المكون
interface SupplierState {
  suppliers: Supplier[]
  loading: boolean
  error: string | null
  modalVisible: boolean
  editingSupplier: Supplier | null
}

// تعريف الإجراءات
type SupplierAction =
  | { type: 'SET_SUPPLIERS'; payload: Supplier[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_MODAL_VISIBLE'; payload: boolean }
  | { type: 'SET_EDITING_SUPPLIER'; payload: Supplier | null }

// دالة reducer لإدارة الحالة
const supplierReducer = (state: SupplierState, action: SupplierAction): SupplierState => {
  switch (action.type) {
    case 'SET_SUPPLIERS':
      return { ...state, suppliers: action.payload }
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    case 'SET_ERROR':
      return { ...state, error: action.payload }
    case 'SET_MODAL_VISIBLE':
      return { ...state, modalVisible: action.payload }
    case 'SET_EDITING_SUPPLIER':
      return { ...state, editingSupplier: action.payload }
    default:
      return state
  }
}

const SupplierManagement: React.FC<SupplierManagementProps> = ({ onBack }) => {
  const { handleDataLoadError, handleApiError } = useErrorHandler()

  // استخدام useReducer لإدارة الحالة بشكل أفضل
  const [state, dispatch] = useReducer(supplierReducer, {
    suppliers: [],
    loading: false,
    error: null,
    modalVisible: false,
    editingSupplier: null
  })

  const [form] = Form.useForm()

  // دالة معالجة الأخطاء المحسنة
  const handleError = useCallback((error: unknown, operation: string) => {
    const errorObj = error instanceof Error ? error : new Error(String(error))
    Logger.error('SupplierManagement', `Error during ${operation}:`, errorObj)
    message.error(`حدث خطأ أثناء ${operation}`)
    dispatch({ type: 'SET_ERROR', payload: errorObj.message })
  }, [])

  const loadSuppliers = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: true })
    dispatch({ type: 'SET_ERROR', payload: null })

    try {
      if (!window.electronAPI) {
        throw new Error('لا يمكن الوصول إلى قاعدة البيانات')
      }

      const response = await window.electronAPI.getSuppliers()
      Logger.info('SupplierManagement', '🔍 استجابة تحميل الموردين:', response)

      if (response && response.success) {
        dispatch({ type: 'SET_SUPPLIERS', payload: response.data || [] })
        Logger.info('SupplierManagement', '✅ تم تحميل الموردين بنجاح:', response.data?.length || 0)
      } else {
        const errorMessage = response?.message || 'فشل في تحميل الموردين'
        dispatch({ type: 'SET_ERROR', payload: errorMessage })
        handleApiError(response, 'تحميل الموردين')
      }
    } catch (_error) {
      handleError(_error, 'تحميل الموردين')
      handleDataLoadError(_error, 'الموردين')
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [handleError, handleApiError, handleDataLoadError])

  useEffect(() => {
    Logger.info('SupplierManagement', '🚀 تم تحميل مكون إدارة الموردين')
    Logger.info('SupplierManagement', '🔍 فحص window.electronAPI:', !!window.electronAPI)
    if (window.electronAPI) {
      Logger.info('SupplierManagement', '✅ window.electronAPI متوفر')
      Logger.info('SupplierManagement', '🔍 فحص دوال الموردين:', {
        getSuppliers: typeof window.electronAPI.getSuppliers,
        createSupplier: typeof window.electronAPI.createSupplier,
        updateSupplier: typeof window.electronAPI.updateSupplier,
        deleteSupplier: typeof window.electronAPI.deleteSupplier
      })
    } else {
      Logger.error('SupplierManagement', '❌ window.electronAPI غير متوفر')
    }
    loadSuppliers()
  }, [loadSuppliers])

  const generateCode = useCallback(async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generateSupplierCode()
        if (response.success && response.data) {
          form.setFieldsValue({ code: response.data.code })
          message.success('تم إنشاء الكود تلقائياً')
        } else {
          message.error(response.message || 'فشل في إنشاء الكود')
        }
      }
    } catch (error) {
      Logger.error('SupplierManagement', 'خطأ في إنشاء الكود:', error)
      message.error('فشل في إنشاء الكود')
    }
  }, [form])

  // دالة للتحقق من صحة البريد الإلكتروني
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // دالة للتحقق من صحة رقم الهاتف
  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^[+]?[0-9\-()s]{10,}$/
    return phoneRegex.test(phone)
  }

  const handleSubmit = async (values: any) => {
    try {
      Logger.info('SupplierManagement', '🔄 محاولة حفّ المورد:', values)

      // التحقق من صحة البريد الإلكتروني
      if (values.email && values.email.trim() !== '') {
        if (!validateEmail(values.email)) {
          message.error('البريد الإلكتروني غير صحيح')
          return
        }
      }

      // التحقق من صحة رقم الهاتف
      if (values.phone && values.phone.trim() !== '') {
        if (!validatePhone(values.phone)) {
          message.error('رقم الهاتف غير صحيح. يجب أن يحتوي على 10 أرقام على الأقل')
          return
        }
      }

      // التحقق من الحد الائتماني
      if (values.credit_limit && values.credit_limit < 0) {
        message.error('الحد الائتماني لا يمكن أن يكون سالباً')
        return
      }

      // التحقق من شروط الدفع
      if (values.payment_terms && values.payment_terms < 0) {
        message.error('شروط الدفع لا يمكن أن تكون سالبة')
        return
      }

      if (window.electronAPI) {
        // التحقق من تفرد الكود قبل الحفّ
        if (!state.editingSupplier) {
          const codeCheckResponse = await window.electronAPI.checkCodeUniqueness('suppliers', 'code', values.code)
          if (codeCheckResponse.success && !codeCheckResponse.data.isUnique) {
            message.error('هذا الكود مستخدم بالفعل، يرجى اختيار كود آخر')
            return
          }
        }

        let response: { success: boolean; message?: string; data?: any }
        if (state.editingSupplier) {
          Logger.info('SupplierManagement', '🔄 تحديث المورد: ' + state.editingSupplier.id)
          response = await window.electronAPI.updateSupplier(state.editingSupplier.id, values)
        } else {
          Logger.info('SupplierManagement', '🔄 إنشاء مورد جديد:', values)
          response = await window.electronAPI.createSupplier(values)
        }

        Logger.info('SupplierManagement', '📋 استجابة حفّ المورد:', response)

        if (response && response.success) {
          message.success(state.editingSupplier ? 'تم تحديث المورد بنجاح' : 'تم إضافة المورد بنجاح')
          dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })
          form.resetFields()
          dispatch({ type: 'SET_EDITING_SUPPLIER', payload: null })
          loadSuppliers()
        } else {
          Logger.error('SupplierManagement', '❌ فشل في حفّ المورد:', response)
          message.error(response?.message || 'فشل في حفّ المورد')
        }
      } else {
        Logger.error('SupplierManagement', '❌ window.electronAPI غير متوفر')
        message.error('خطأ في الاتصال بالنّام')
      }
    } catch (error) {
      Logger.error('SupplierManagement', '❌ خطأ في حفّ المورد:', error)
      message.error('حدث خطأ أثناء حفّ المورد')
    }
  }

  const handleEdit = useCallback((supplier: Supplier) => {
    dispatch({ type: 'SET_EDITING_SUPPLIER', payload: supplier })
    form.setFieldsValue(supplier)
    dispatch({ type: 'SET_MODAL_VISIBLE', payload: true })
  }, [form])

  const handleDelete = useCallback(async (supplierId: number) => {
    try {
      Logger.info('SupplierManagement', '🗑️ محاولة حذف المورد:', supplierId)

      if (window.electronAPI) {
        const response = await window.electronAPI.deleteSupplier(supplierId)
        Logger.info('SupplierManagement', '📋 استجابة حذف المورد:', response)

        if (response && response.success) {
          message.success('تم حذف المورد بنجاح')
          loadSuppliers()
        } else {
          Logger.error('SupplierManagement', '❌ فشل في حذف المورد:', response)
          message.error(response?.message || 'فشل في حذف المورد')
        }
      } else {
        Logger.error('SupplierManagement', '❌ window.electronAPI غير متوفر')
        message.error('خطأ في الاتصال بالنّام')
      }
    } catch (error) {
      handleError(error, 'حذف المورد')
    }
  }, [loadSuppliers, handleError])

  const handleAdd = useCallback(() => {
    dispatch({ type: 'SET_EDITING_SUPPLIER', payload: null })
    form.resetFields()
    form.setFieldsValue({
      payment_terms: 30,
      credit_limit: 0,
      is_active: true
    })
    generateCode()
    dispatch({ type: 'SET_MODAL_VISIBLE', payload: true })
  }, [form, generateCode])

  // إحصائيات الموردين محسنة بـ useMemo
  const supplierStats = useMemo(() => {
    const total = state.suppliers.length
    const active = state.suppliers.filter(s => s.is_active).length
    const inactive = total - active
    const totalCreditLimit = state.suppliers.reduce((sum, s) => sum + s.credit_limit, 0)

    return { total, active, inactive, totalCreditLimit }
  }, [state.suppliers])

  // دالة تصدير Excel محسنة
  const handleExportExcel = useCallback(() => {
    try {
      if (state.suppliers.length === 0) {
        message.warning('لا توجد بيانات موردين للتصدير')
        return
      }

      // تحضير البيانات للتصدير
      const exportData = state.suppliers.map(supplier => ({
        'كود المورد': supplier.code,
        'اسم المورد': supplier.name,
        'الشخص المسؤول': supplier.contact_person || '',
        'رقم الهاتف': supplier.phone || '',
        'البريد الإلكتروني': supplier.email || '',
        'العنوان': supplier.address || '',
        'الرقم الضريبي': supplier.tax_number || '',
        'شروط الدفع (يوم)': supplier.payment_terms,
        'الحد الائتماني': supplier.credit_limit,
        'الحالة': supplier.is_active ? 'نشط' : 'معطل',
        'تاريخ الإنشاء': new Date(supplier.created_at).toLocaleDateString('ar-EG')
      }))

      // إنشاء ورقة العمل
      const worksheet = XLSX.utils.json_to_sheet(exportData)

      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 15 }, // كود المورد
        { wch: 25 }, // اسم المورد
        { wch: 20 }, // الشخص المسؤول
        { wch: 15 }, // رقم الهاتف
        { wch: 25 }, // البريد الإلكتروني
        { wch: 30 }, // العنوان
        { wch: 15 }, // الرقم الضريبي
        { wch: 15 }, // شروط الدفع
        { wch: 15 }, // الحد الائتماني
        { wch: 10 }, // الحالة
        { wch: 15 }  // تاريخ الإنشاء
      ]
      worksheet['!cols'] = columnWidths

      // إنشاء المصنف
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'الموردين')

      // إضافة ورقة معلومات الشركة
      const companyInfo = [
        ['تقرير الموردين'],
        [''],
        ['إجمالي الموردين:', state.suppliers.length],
        ['الموردين النشطين:', supplierStats.active],
        ['الموردين المعطلين:', supplierStats.inactive],
        ['إجمالي الحد الائتماني:', supplierStats.totalCreditLimit.toLocaleString()],
        [''],
        ['تاريخ التصدير:', new Date().toLocaleDateString('ar-EG')],
        ['وقت التصدير:', new Date().toLocaleTimeString('ar-EG')]
      ]

      const infoWorksheet = XLSX.utils.aoa_to_sheet(companyInfo)
      XLSX.utils.book_append_sheet(workbook, infoWorksheet, 'معلومات التقرير')

      // تحديد اسم الملف
      const timestamp = new Date().toISOString().split('T')[0]
      const fileName = 'تقرير_الموردين_' + timestamp + '.xlsx'

      // تحميل الملف
      XLSX.writeFile(workbook, fileName)

      message.success('تم تصدير ' + state.suppliers.length + ' مورد بنجاح إلى ملف Excel')
    } catch (error) {
      handleError(error, 'تصدير البيانات')
    }
  }, [state.suppliers, supplierStats, handleError])

  const columns = [
    {
      title: 'الكود',
      dataIndex: 'code',
      key: 'code',
      width: 100,
      render: (code: string) => <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{code}</span>
    },
    {
      title: 'اسم المورد',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Supplier) => (
        <Space>
          <UserOutlined style={{ color: record.is_active ? '#52c41a' : '#ff4d4f' }} />
          <span style={{ fontWeight: 'bold' }}>{name}</span>
        </Space>
      )
    },
    {
      title: 'الشخص المسؤول',
      dataIndex: 'contact_person',
      key: 'contact_person',
      render: (contact: string) => contact || '-'
    },
    {
      title: 'الهاتف',
      dataIndex: 'phone',
      key: 'phone',
      render: (phone: string) => phone ? (
        <Space>
          <PhoneOutlined style={{ color: '#1890ff' }} />
          {phone}
        </Space>
      ) : '-'
    },
    {
      title: 'البريد الإلكتروني',
      dataIndex: 'email',
      key: 'email',
      render: (email: string) => email ? (
        <Space>
          <MailOutlined style={{ color: '#1890ff' }} />
          {email}
        </Space>
      ) : '-'
    },
    {
      title: 'شروط الدفع (يوم)',
      dataIndex: 'payment_terms',
      key: 'payment_terms',
      width: 120,
      render: (terms: number) => terms + ' يوم'
    },
    {
      title: `الحد الائتماني (${getCurrencySymbol()})`,
      dataIndex: 'credit_limit',
      key: 'credit_limit',
      width: 140,
      render: (limit: number) => limit.toLocaleString()
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (isActive: boolean) => (
        <span style={{ 
          color: isActive ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {isActive ? 'نشط' : 'معطل'}
        </span>
      )
    },
    {
      title: 'تاريخ الإنشاء (ميلادي)',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 140,
      render: (date: string) => DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE)
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 120,
      render: (_: any, record: Supplier) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
          />
          <Popconfirm
            title="هل أنت متأكد من حذف هذا المورد؟"
            onConfirm={() => handleDelete(record.id)}
            okText="نعم"
            cancelText="لا"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
            />
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>إدارة الموردين</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>إدارة بيانات الموردين وشروط التعامل</p>
        </div>
        <Space>
          <Button onClick={onBack}>رجوع</Button>

          <Button
            icon={<FileTextOutlined />}
            onClick={handleExportExcel}
            style={{ marginLeft: '8px' }}
          >
            تصدير Excel
          </Button>

          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            إضافة مورد جديد
          </Button>
        </Space>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الموردين"
              value={supplierStats.total}
              valueStyle={{ color: '#1890ff' }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="الموردين النشطين"
              value={supplierStats.active}
              valueStyle={{ color: '#52c41a' }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="الموردين المعطلين"
              value={supplierStats.inactive}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الحدود الائتمانية"
              value={supplierStats.totalCreditLimit}
              valueStyle={{ color: '#fa8c16' }}
              prefix={getCurrencySymbol()}
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      <LoadingWrapper
        loading={state.loading}
        error={state.error}
        onRetry={loadSuppliers}
        isEmpty={!state.loading && !state.error && state.suppliers.length === 0}
        emptyText="لا توجد موردين مسجلين"
      >
        <Table
          columns={columns}
          dataSource={state.suppliers}
          rowKey="id"
          pagination={{
            total: state.suppliers.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => range[0] + '-' + range[1] + ' من ' + total + ' مورد'
          }}
          scroll={{ x: 1200 }}
        />
      </LoadingWrapper>

      <Modal
        title={state.editingSupplier ? 'تعديل المورد' : 'إضافة مورد جديد'}
        open={state.modalVisible}
        onCancel={() => {
          dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })
          form.resetFields()
          dispatch({ type: 'SET_EDITING_SUPPLIER', payload: null })
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ payment_terms: 30, credit_limit: 0, is_active: true }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="كود المورد"
                name="code"
                rules={[{ required: true, message: 'يرجى إدخال كود المورد' }]}
              >
                <Input
                  placeholder="أدخل كود المورد"
                  addonAfter={
                    !state.editingSupplier && (
                      <Button
                        size="small"
                        onClick={generateCode}
                        icon={<BarcodeOutlined />}
                      >
                        إنشاء تلقائي
                      </Button>
                    )
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="اسم المورد"
                name="name"
                rules={[{ required: true, message: 'يرجى إدخال اسم المورد' }]}
              >
                <Input placeholder="اسم المورد" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="الشخص المسؤول" name="contact_person">
                <Input placeholder="اسم الشخص المسؤول" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="رقم الهاتف"
                name="phone"
                rules={[
                  {
                    pattern: /^[+]?[0-9\-()s]{10,}$/,
                    message: 'رقم الهاتف غير صحيح. يجب أن يحتوي على 10 أرقام على الأقل'
                  }
                ]}
              >
                <Input placeholder="رقم الهاتف" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="البريد الإلكتروني"
                name="email"
                rules={[
                  {
                    type: 'email',
                    message: 'البريد الإلكتروني غير صحيح'
                  }
                ]}
              >
                <Input placeholder="البريد الإلكتروني" type="email" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="الرقم الضريبي" name="tax_number">
                <Input placeholder="الرقم الضريبي" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="العنوان" name="address">
            <Input.TextArea placeholder="العنوان الكامل" rows={2} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="شروط الدفع (بالأيام)"
                name="payment_terms"
                rules={[
                  { required: true, message: 'يرجى إدخال شروط الدفع' },
                  { type: 'number', min: 0, message: 'شروط الدفع لا يمكن أن تكون سالبة' }
                ]}
              >
                <InputNumber
                  placeholder="عدد الأيام"
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={'الحد الائتماني (' + getCurrencySymbol() + ')'}
                name="credit_limit"
                rules={[
                  { required: true, message: 'يرجى إدخال الحد الائتماني' },
                  { type: 'number', min: 0, message: 'الحد الائتماني لا يمكن أن يكون سالباً' }
                ]}
              >
                <InputNumber
                  placeholder="الحد الائتماني"
                  min={0}
                  style={{ width: '100%' }}
                  formatter={value => String(value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="الحالة" name="is_active" valuePropName="checked">
            <Switch checkedChildren="نشط" unCheckedChildren="معطل" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {state.editingSupplier ? 'تحديث' : 'إضافة'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

const SupplierManagementWithApp: React.FC<SupplierManagementProps> = (props: SupplierManagementProps) => {
  return (
    <App>
      <SupplierManagement {...props} />
    </App>
  )
}

export default SupplierManagementWithApp
