/**
 * نظام نقل ومزامنة البيانات بين وضع المطور والإنتاج
 * Data Migration and Synchronization System
 */

import * as fs from 'fs'
import * as path from 'path'
import { app } from 'electron'
import { Logger } from './logger'

export class DataMigrationManager {
  private static instance: DataMigrationManager
  private devDbPath: string
  private prodDbPath: string

  private constructor() {
    // تحديد مسارات قواعد البيانات
    const userDataPath = app.getPath('userData')
    this.prodDbPath = path.join(userDataPath, 'database.db')
    
    // مسار قاعدة بيانات المطور (Electron)
    const electronDataPath = path.join(app.getPath('appData'), 'Electron')
    this.devDbPath = path.join(electronDataPath, 'database.db')
    
    Logger.info('DataMigration', '📂 مسار قاعدة بيانات المطور:', this.devDbPath)
    Logger.info('DataMigration', '📂 مسار قاعدة بيانات الإنتاج:', this.prodDbPath)
  }

  public static getInstance(): DataMigrationManager {
    if (!DataMigrationManager.instance) {
      DataMigrationManager.instance = new DataMigrationManager()
    }
    return DataMigrationManager.instance
  }

  /**
   * فحص وجود قواعد البيانات
   */
  public checkDatabasesExistence(): {
    devExists: boolean
    prodExists: boolean
    devPath: string
    prodPath: string
    devSize?: number
    prodSize?: number
    devModified?: Date
    prodModified?: Date
  } {
    const devExists = fs.existsSync(this.devDbPath)
    const prodExists = fs.existsSync(this.prodDbPath)

    const result: any = {
      devExists,
      prodExists,
      devPath: this.devDbPath,
      prodPath: this.prodDbPath
    }

    if (devExists) {
      const devStats = fs.statSync(this.devDbPath)
      result.devSize = devStats.size
      result.devModified = devStats.mtime
    }

    if (prodExists) {
      const prodStats = fs.statSync(this.prodDbPath)
      result.prodSize = prodStats.size
      result.prodModified = prodStats.mtime
    }

    return result
  }

  /**
   * نقل البيانات من المطور إلى الإنتاج
   */
  public async migrateFromDevToProduction(): Promise<{
    success: boolean
    message: string
    backupPath?: string
  }> {
    try {
      Logger.info('DataMigration', '🔄 بدء نقل البيانات من المطور إلى الإنتاج...')

      // التحقق من وجود قاعدة بيانات المطور
      if (!fs.existsSync(this.devDbPath)) {
        return {
          success: false,
          message: 'قاعدة بيانات المطور غير موجودة'
        }
      }

      // إنشاء مجلد الإنتاج إذا لم يكن موجوداً
      const prodDir = path.dirname(this.prodDbPath)
      if (!fs.existsSync(prodDir)) {
        fs.mkdirSync(prodDir, { recursive: true })
        Logger.info('DataMigration', '📁 تم إنشاء مجلد الإنتاج')
      }

      // إنشاء نسخة احتياطية من قاعدة بيانات الإنتاج إذا كانت موجودة
      let backupPath: string | undefined
      if (fs.existsSync(this.prodDbPath)) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
        backupPath = `${this.prodDbPath}.backup.${timestamp}`
        fs.copyFileSync(this.prodDbPath, backupPath)
        Logger.info('DataMigration', '💾 تم إنشاء نسخة احتياطية:', backupPath)
      }

      // نسخ قاعدة بيانات المطور إلى الإنتاج
      fs.copyFileSync(this.devDbPath, this.prodDbPath)

      const stats = fs.statSync(this.prodDbPath)
      Logger.info('DataMigration', '✅ تم نقل البيانات بنجاح')
      Logger.info('DataMigration', `📊 حجم الملف: ${(stats.size / 1024).toFixed(2)} KB`)

      return {
        success: true,
        message: 'تم نقل البيانات بنجاح',
        backupPath
      }

    } catch (error) {
      Logger.error('DataMigration', '❌ خطأ في نقل البيانات:', error)
      return {
        success: false,
        message: `خطأ في نقل البيانات: ${error}`
      }
    }
  }

  /**
   * نقل البيانات من الإنتاج إلى المطور
   */
  public async migrateFromProductionToDev(): Promise<{
    success: boolean
    message: string
    backupPath?: string
  }> {
    try {
      Logger.info('DataMigration', '🔄 بدء نقل البيانات من الإنتاج إلى المطور...')

      // التحقق من وجود قاعدة بيانات الإنتاج
      if (!fs.existsSync(this.prodDbPath)) {
        return {
          success: false,
          message: 'قاعدة بيانات الإنتاج غير موجودة'
        }
      }

      // إنشاء مجلد المطور إذا لم يكن موجوداً
      const devDir = path.dirname(this.devDbPath)
      if (!fs.existsSync(devDir)) {
        fs.mkdirSync(devDir, { recursive: true })
        Logger.info('DataMigration', '📁 تم إنشاء مجلد المطور')
      }

      // إنشاء نسخة احتياطية من قاعدة بيانات المطور إذا كانت موجودة
      let backupPath: string | undefined
      if (fs.existsSync(this.devDbPath)) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
        backupPath = `${this.devDbPath}.backup.${timestamp}`
        fs.copyFileSync(this.devDbPath, backupPath)
        Logger.info('DataMigration', '💾 تم إنشاء نسخة احتياطية:', backupPath)
      }

      // نسخ قاعدة بيانات الإنتاج إلى المطور
      fs.copyFileSync(this.prodDbPath, this.devDbPath)

      const stats = fs.statSync(this.devDbPath)
      Logger.info('DataMigration', '✅ تم نقل البيانات بنجاح')
      Logger.info('DataMigration', `📊 حجم الملف: ${(stats.size / 1024).toFixed(2)} KB`)

      return {
        success: true,
        message: 'تم نقل البيانات بنجاح',
        backupPath
      }

    } catch (error) {
      Logger.error('DataMigration', '❌ خطأ في نقل البيانات:', error)
      return {
        success: false,
        message: `خطأ في نقل البيانات: ${error}`
      }
    }
  }

  /**
   * البحث عن قواعد بيانات في مواقع مختلفة
   */
  public findDatabaseFiles(): Array<{
    path: string
    exists: boolean
    size?: number
    modified?: Date
  }> {
    const possiblePaths = [
      this.devDbPath,
      this.prodDbPath,
      path.join(process.cwd(), 'database.db'),
      path.join(process.cwd(), 'dist', 'database.db'),
      path.join(process.cwd(), 'src', 'database.db'),
      path.join(process.cwd(), 'dist', 'main', 'database.db')
    ]

    return possiblePaths.map(dbPath => {
      const exists = fs.existsSync(dbPath)
      const result: any = { path: dbPath, exists }

      if (exists) {
        const stats = fs.statSync(dbPath)
        result.size = stats.size
        result.modified = stats.mtime
      }

      return result
    })
  }

  /**
   * تنظيف النسخ الاحتياطية القديمة
   */
  public cleanupOldBackups(maxAge: number = 7): void {
    try {
      const directories = [
        path.dirname(this.devDbPath),
        path.dirname(this.prodDbPath)
      ]

      directories.forEach(dir => {
        if (fs.existsSync(dir)) {
          const files = fs.readdirSync(dir)
          const backupFiles = files.filter(file => file.includes('.backup.'))

          backupFiles.forEach(file => {
            const filePath = path.join(dir, file)
            const stats = fs.statSync(filePath)
            const ageInDays = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24)

            if (ageInDays > maxAge) {
              fs.unlinkSync(filePath)
              Logger.info('DataMigration', `🗑️ تم حذف النسخة الاحتياطية القديمة: ${file}`)
            }
          })
        }
      })

    } catch (error) {
      Logger.error('DataMigration', '❌ خطأ في تنظيف النسخ الاحتياطية:', error)
    }
  }
}

// تصدير مثيل واحد
export const dataMigrationManager = DataMigrationManager.getInstance()
