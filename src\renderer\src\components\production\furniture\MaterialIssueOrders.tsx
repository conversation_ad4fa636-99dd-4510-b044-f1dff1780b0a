import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Space,
  Tag,
  Card,
  Row,
  Col,
  Statistic,
  Descriptions,
  Tooltip,
  Divider
} from 'antd'
import {
  PlusOutlined,
  EyeOutlined,
  FileTextOutlined,
  InboxOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../../utils/logger'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

interface MaterialIssueOrder {
  id: number
  order_number: string
  production_order_id: number
  production_order_number: string
  warehouse_id: number
  warehouse_name: string
  status: 'pending' | 'approved' | 'issued' | 'cancelled'
  requested_by_name?: string
  approved_by_name?: string
  issued_by_name?: string
  request_date: string
  approval_date?: string
  issue_date?: string
  notes?: string
  items?: MaterialIssueItem[]
}

interface MaterialIssueItem {
  id: number
  material_id: number
  material_name: string
  material_code: string
  unit: string
  requested_quantity: number
  approved_quantity?: number
  issued_quantity?: number
  unit_price: number
  total_cost: number
}

interface MaterialIssueOrdersProps {
  productionOrderId?: number
}

const MaterialIssueOrdersComponent: React.FC<MaterialIssueOrdersProps> = ({ productionOrderId }) => {
  const [orders, setOrders] = useState<MaterialIssueOrder[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [detailsModalVisible, setDetailsModalVisible] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<MaterialIssueOrder | null>(null)
  const [form] = Form.useForm()
  const [productionOrders, setProductionOrders] = useState<any[]>([])
  const [warehouses, setWarehouses] = useState<any[]>([])
  const [materials, setMaterials] = useState<any[]>([])
  const [selectedMaterials, setSelectedMaterials] = useState<any[]>([])
  const [stats, setStats] = useState({
    totalOrders: 0,
    pendingOrders: 0,
    issuedOrders: 0,
    totalValue: 0
  })

  useEffect(() => {
    loadData()
  }, [productionOrderId])

  const loadData = async () => {
    await Promise.all([
      loadOrders(),
      loadProductionOrders(),
      loadWarehouses(),
      loadMaterials()
    ])
  }

  const loadOrders = async () => {
    setLoading(true)
    try {
      if (!window.electronAPI) {
        Logger.error('MaterialIssueOrders', 'window.electronAPI غير متوفر')
        return
      }

      const filters = productionOrderId ? { production_order_id: productionOrderId } : {}
      const result = await window.electronAPI.getMaterialIssueOrders(filters)
      
      if (result.success) {
        setOrders(result.data)
        calculateStats(result.data)
        Logger.info('MaterialIssueOrders', 'تم تحميل أوامر صرف المواد بنجاح')
      } else {
        message.error('فشل في تحميل أوامر صرف المواد')
      }
    } catch (error) {
      Logger.error('MaterialIssueOrders', 'خطأ في تحميل أوامر صرف المواد:', error)
      message.error('حدث خطأ في تحميل أوامر صرف المواد')
    }
    setLoading(false)
  }

  const loadProductionOrders = async () => {
    try {
      if (!window.electronAPI) return

      const result = await window.electronAPI.getProductionOrders()
      if (result.success) {
        setProductionOrders(result.data)
      }
    } catch (error) {
      Logger.error('MaterialIssueOrders', 'خطأ في تحميل أوامر الإنتاج:', error)
    }
  }

  const loadWarehouses = async () => {
    try {
      if (!window.electronAPI) return

      const result = await window.electronAPI.getWarehouses()
      // getWarehouses returns Warehouse[] directly, but handlers wrap it in ApiResponse
      if (Array.isArray(result)) {
        setWarehouses(result)
      } else if (result && typeof result === 'object' && 'success' in result) {
        const apiResult = result as any
        if (apiResult.success) {
          setWarehouses(apiResult.data || [])
        }
      }
    } catch (error) {
      Logger.error('MaterialIssueOrders', 'خطأ في تحميل المخازن:', error)
    }
  }

  const loadMaterials = async () => {
    try {
      if (!window.electronAPI) return

      const result = await window.electronAPI.getItems()
      // getItems returns Item[] directly, but handlers wrap it in ApiResponse
      let items: any[] = []
      if (Array.isArray(result)) {
        items = result
      } else if (result && typeof result === 'object' && 'success' in result && (result as any).success) {
        items = (result as any).data || []
      }

      const rawMaterials = items.filter((item: any) =>
        item.category_name === 'مواد خام' || item.type === 'raw_material'
      )
      setMaterials(rawMaterials)
    } catch (error) {
      Logger.error('MaterialIssueOrders', 'خطأ في تحميل المواد:', error)
    }
  }

  const calculateStats = (data: MaterialIssueOrder[]) => {
    const totalOrders = data.length
    const pendingOrders = data.filter(o => o.status === 'pending').length
    const issuedOrders = data.filter(o => o.status === 'issued').length
    const totalValue = data.reduce((sum, order) => {
      if (order.items) {
        return sum + order.items.reduce((itemSum, item) => itemSum + item.total_cost, 0)
      }
      return sum
    }, 0)

    setStats({
      totalOrders,
      pendingOrders,
      issuedOrders,
      totalValue: Math.round(totalValue * 100) / 100
    })
  }

  const handleCreateOrder = async (values: any) => {
    try {
      if (!window.electronAPI) {
        message.error('النظام غير متوفر')
        return
      }

      if (selectedMaterials.length === 0) {
        message.error('يجب إضافة مادة واحدة على الأقل')
        return
      }

      const orderData = {
        production_order_id: values.production_order_id,
        warehouse_id: values.warehouse_id,
        materials: selectedMaterials.map(material => ({
          material_id: material.material_id,
          requested_quantity: material.quantity,
          unit_price: material.unit_price
        })),
        notes: values.notes
      }

      const result = await window.electronAPI.createMaterialIssueOrder(orderData)
      
      if (result.success) {
        message.success('تم إنشاء أمر صرف المواد بنجاح')
        setModalVisible(false)
        form.resetFields()
        setSelectedMaterials([])
        await loadOrders()
      } else {
        message.error(result.message || 'فشل في إنشاء أمر صرف المواد')
      }
    } catch (error) {
      Logger.error('MaterialIssueOrders', 'خطأ في إنشاء أمر صرف المواد:', error)
      message.error('حدث خطأ في إنشاء أمر صرف المواد')
    }
  }

  const handleViewDetails = async (order: MaterialIssueOrder) => {
    try {
      if (!window.electronAPI) return

      const result = await window.electronAPI.getMaterialIssueOrderDetails(order.id)
      if (result.success) {
        setSelectedOrder(result.data)
        setDetailsModalVisible(true)
      }
    } catch (error) {
      Logger.error('MaterialIssueOrders', 'خطأ في جلب تفاصيل الأمر:', error)
      message.error('حدث خطأ في جلب تفاصيل الأمر')
    }
  }

  const addMaterial = () => {
    setSelectedMaterials([...selectedMaterials, {
      id: Date.now(),
      material_id: null,
      material_name: '',
      quantity: 1,
      unit_price: 0,
      total_cost: 0
    }])
  }

  const updateMaterial = (index: number, field: string, value: any) => {
    const updatedMaterials = [...selectedMaterials]
    updatedMaterials[index][field] = value

    if (field === 'material_id') {
      const selectedMaterial = materials.find(m => m.id === value)
      if (selectedMaterial) {
        updatedMaterials[index].material_name = selectedMaterial.name
        updatedMaterials[index].unit_price = selectedMaterial.cost_price || selectedMaterial.sale_price || 0
      }
    }

    if (field === 'quantity' || field === 'unit_price' || field === 'material_id') {
      updatedMaterials[index].total_cost = updatedMaterials[index].quantity * updatedMaterials[index].unit_price
    }

    setSelectedMaterials(updatedMaterials)
  }

  const removeMaterial = (index: number) => {
    setSelectedMaterials(selectedMaterials.filter((_, i) => i !== index))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'approved': return 'blue'
      case 'issued': return 'green'
      case 'cancelled': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار'
      case 'approved': return 'معتمد'
      case 'issued': return 'مصروف'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  const columns = [
    {
      title: 'رقم الأمر',
      dataIndex: 'order_number',
      key: 'order_number',
      width: 120,
    },
    {
      title: 'أمر الإنتاج',
      dataIndex: 'production_order_number',
      key: 'production_order_number',
      width: 120,
    },
    {
      title: 'المخزن',
      dataIndex: 'warehouse_name',
      key: 'warehouse_name',
      width: 120,
    },
    {
      title: 'تاريخ الطلب',
      dataIndex: 'request_date',
      key: 'request_date',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'طلب بواسطة',
      dataIndex: 'requested_by_name',
      key: 'requested_by_name',
      width: 120,
      render: (name?: string) => name || '-'
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 120,
      render: (record: MaterialIssueOrder) => (
        <Space>
          <Tooltip title="عرض التفاصيل">
            <Button
              type="default"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* الإحصائيات */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الأوامر"
              value={stats.totalOrders}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="في الانتظار"
              value={stats.pendingOrders}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="مصروف"
              value={stats.issuedOrders}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي القيمة"
              value={stats.totalValue}
              precision={2}
              prefix="₪"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* شريط الأدوات */}
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            form.resetFields()
            setSelectedMaterials([])
            if (productionOrderId) {
              form.setFieldsValue({ production_order_id: productionOrderId })
            }
            setModalVisible(true)
          }}
        >
          أمر صرف جديد
        </Button>
        <Button onClick={loadOrders} loading={loading}>
          تحديث البيانات
        </Button>
      </div>

      {/* جدول الأوامر */}
      <Table
        columns={columns}
        dataSource={orders}
        rowKey="id"
        loading={loading}
        scroll={{ x: 800 }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `إجمالي ${total} أمر`
        }}
      />

      {/* نافذة إنشاء أمر صرف جديد */}
      <Modal
        title="إنشاء أمر صرف مواد جديد"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateOrder}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="production_order_id"
                label="أمر الإنتاج"
                rules={[{ required: true, message: 'يرجى اختيار أمر الإنتاج' }]}
              >
                <Select
                  placeholder="اختر أمر الإنتاج"
                  showSearch
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                  }
                  disabled={!!productionOrderId}
                >
                  {productionOrders.map(order => (
                    <Option key={order.id} value={order.id}>
                      {order.order_number} - {order.item_name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="warehouse_id"
                label="المخزن"
                rules={[{ required: true, message: 'يرجى اختيار المخزن' }]}
              >
                <Select placeholder="اختر المخزن">
                  {warehouses.map(warehouse => (
                    <Option key={warehouse.id} value={warehouse.id}>
                      {warehouse.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Divider>المواد المطلوبة</Divider>

          {selectedMaterials.map((material, index) => (
            <Row key={material.id} gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={8}>
                <Select
                  placeholder="اختر المادة"
                  value={material.material_id}
                  onChange={(value) => updateMaterial(index, 'material_id', value)}
                  showSearch
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                  }
                  style={{ width: '100%' }}
                >
                  {materials.map(mat => (
                    <Option key={mat.id} value={mat.id}>
                      {mat.name} ({mat.code})
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={4}>
                <InputNumber
                  placeholder="الكمية"
                  value={material.quantity}
                  onChange={(value) => updateMaterial(index, 'quantity', value || 0)}
                  min={0}
                  step={0.1}
                  style={{ width: '100%' }}
                />
              </Col>
              <Col span={4}>
                <InputNumber
                  placeholder="السعر"
                  value={material.unit_price}
                  onChange={(value) => updateMaterial(index, 'unit_price', value || 0)}
                  min={0}
                  step={0.01}
                  style={{ width: '100%' }}
                />
              </Col>
              <Col span={4}>
                <Input
                  value={`₪${material.total_cost.toFixed(2)}`}
                  disabled
                  style={{ width: '100%' }}
                />
              </Col>
              <Col span={4}>
                <Button
                  type="text"
                  danger
                  onClick={() => removeMaterial(index)}
                >
                  حذف
                </Button>
              </Col>
            </Row>
          ))}

          <Button
            type="dashed"
            onClick={addMaterial}
            style={{ width: '100%', marginBottom: '16px' }}
            icon={<PlusOutlined />}
          >
            إضافة مادة
          </Button>

          <Form.Item
            name="notes"
            label="ملاحظات"
          >
            <TextArea rows={3} placeholder="ملاحظات إضافية..." />
          </Form.Item>

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                إنشاء الأمر
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* نافذة تفاصيل الأمر */}
      <Modal
        title="تفاصيل أمر صرف المواد"
        open={detailsModalVisible}
        onCancel={() => setDetailsModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailsModalVisible(false)}>
            إغلاق
          </Button>
        ]}
        width={800}
      >
        {selectedOrder && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="رقم الأمر">
                {selectedOrder.order_number}
              </Descriptions.Item>
              <Descriptions.Item label="أمر الإنتاج">
                {selectedOrder.production_order_number}
              </Descriptions.Item>
              <Descriptions.Item label="المخزن">
                {selectedOrder.warehouse_name}
              </Descriptions.Item>
              <Descriptions.Item label="الحالة">
                <Tag color={getStatusColor(selectedOrder.status)}>
                  {getStatusText(selectedOrder.status)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="تاريخ الطلب">
                {dayjs(selectedOrder.request_date).format('YYYY-MM-DD HH:mm')}
              </Descriptions.Item>
              <Descriptions.Item label="طلب بواسطة">
                {selectedOrder.requested_by_name || '-'}
              </Descriptions.Item>
              {selectedOrder.approval_date && (
                <Descriptions.Item label="تاريخ الاعتماد">
                  {dayjs(selectedOrder.approval_date).format('YYYY-MM-DD HH:mm')}
                </Descriptions.Item>
              )}
              {selectedOrder.approved_by_name && (
                <Descriptions.Item label="اعتمد بواسطة">
                  {selectedOrder.approved_by_name}
                </Descriptions.Item>
              )}
              {selectedOrder.issue_date && (
                <Descriptions.Item label="تاريخ الصرف">
                  {dayjs(selectedOrder.issue_date).format('YYYY-MM-DD HH:mm')}
                </Descriptions.Item>
              )}
              {selectedOrder.issued_by_name && (
                <Descriptions.Item label="صرف بواسطة">
                  {selectedOrder.issued_by_name}
                </Descriptions.Item>
              )}
              {selectedOrder.notes && (
                <Descriptions.Item label="ملاحظات" span={2}>
                  {selectedOrder.notes}
                </Descriptions.Item>
              )}
            </Descriptions>

            {selectedOrder.items && selectedOrder.items.length > 0 && (
              <div style={{ marginTop: '24px' }}>
                <h4>تفاصيل المواد</h4>
                <Table
                  dataSource={selectedOrder.items}
                  rowKey="id"
                  pagination={false}
                  size="small"
                  columns={[
                    {
                      title: 'المادة',
                      dataIndex: 'material_name',
                      key: 'material_name',
                    },
                    {
                      title: 'الكود',
                      dataIndex: 'material_code',
                      key: 'material_code',
                    },
                    {
                      title: 'الكمية المطلوبة',
                      dataIndex: 'requested_quantity',
                      key: 'requested_quantity',
                      render: (qty: number, record: MaterialIssueItem) =>
                        `${qty} ${record.unit || ''}`
                    },
                    {
                      title: 'الكمية المصروفة',
                      dataIndex: 'issued_quantity',
                      key: 'issued_quantity',
                      render: (qty: number | undefined, record: MaterialIssueItem) =>
                        qty ? `${qty} ${record.unit || ''}` : '-'
                    },
                    {
                      title: 'سعر الوحدة',
                      dataIndex: 'unit_price',
                      key: 'unit_price',
                      render: (price: number) => `₪${price.toFixed(2)}`
                    },
                    {
                      title: 'إجمالي التكلفة',
                      dataIndex: 'total_cost',
                      key: 'total_cost',
                      render: (cost: number) => `₪${cost.toFixed(2)}`
                    }
                  ]}
                />
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default MaterialIssueOrdersComponent
