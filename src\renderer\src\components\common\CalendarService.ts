import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import { SafeLogger as Logger } from '../../utils/logger'

dayjs.extend(isBetween)
import { NotificationData } from './NotificationService'

export interface CalendarEvent {
  id: string
  title: string
  description?: string
  date: string
  time?: string
  type: 'order' | 'delivery' | 'meeting' | 'reminder' | 'notification'
  category: 'paint' | 'sales' | 'inventory' | 'general'
  status: 'pending' | 'completed' | 'cancelled'
  reference_id?: number
  reference_type?: string
  color?: string
  created_at: string
}

export interface CalendarDayData {
  date: string
  events: CalendarEvent[]
  notifications: NotificationData[]
  hasEvents: boolean
  eventCount: number
  notificationCount: number
}

export class CalendarService {
  private static readonly STORAGE_KEY = 'calendar_events'

  // ألوان الأحداث حسب النوع
  private static eventColors = {
    order: '#1890ff',
    delivery: '#52c41a',
    meeting: '#722ed1',
    reminder: '#faad14',
    notification: '#ff7875'
  }

  // حفظ حدث جديد
  static saveEvent(eventData: Omit<CalendarEvent, 'id' | 'created_at'>): CalendarEvent {
    const newEvent: CalendarEvent = {
      ...eventData,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      color: eventData.color || this.eventColors[eventData.type]
    }

    const events = this.getAllEvents()
    events.push(newEvent)
    
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(events))
    
    return newEvent
  }

  // جلب جميع الأحداث
  static getAllEvents(): CalendarEvent[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      Logger.error('CalendarService', 'خطأ في جلب أحداث التقويم:', error)
      return []
    }
  }

  // جلب أحداث يوم محدد
  static getEventsForDate(date: string): CalendarEvent[] {
    return this.getAllEvents().filter(event => event.date === date)
  }

  // جلب أحداث شهر محدد
  static getEventsForMonth(year: number, month: number): CalendarEvent[] {
    const startDate = dayjs().year(year).month(month).startOf('month')
    const endDate = dayjs().year(year).month(month).endOf('month')
    
    return this.getAllEvents().filter(event => {
      const eventDate = dayjs(event.date)
      return eventDate.isBetween(startDate, endDate, 'day', '[]')
    })
  }

  // جلب أحداث فترة محددة
  static getEventsForRange(startDate: string, endDate: string): CalendarEvent[] {
    const start = dayjs(startDate)
    const end = dayjs(endDate)
    
    return this.getAllEvents().filter(event => {
      const eventDate = dayjs(event.date)
      return eventDate.isBetween(start, end, 'day', '[]')
    })
  }

  // جلب أحداث حسب الفئة
  static getEventsByCategory(category: string): CalendarEvent[] {
    return this.getAllEvents().filter(event => event.category === category)
  }

  // جلب أحداث حسب النوع
  static getEventsByType(type: string): CalendarEvent[] {
    return this.getAllEvents().filter(event => event.type === type)
  }

  // تحديث حدث
  static updateEvent(id: string, updates: Partial<CalendarEvent>): boolean {
    const events = this.getAllEvents()
    const index = events.findIndex(event => event.id === id)
    
    if (index !== -1) {
      events[index] = { ...events[index], ...updates }
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(events))
      return true
    }
    
    return false
  }

  // حذف حدث
  static deleteEvent(id: string): boolean {
    const events = this.getAllEvents().filter(event => event.id !== id)
    const originalLength = this.getAllEvents().length
    
    if (events.length < originalLength) {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(events))
      return true
    }
    
    return false
  }

  // إنشاء حدث من أمر دهان
  static createPaintOrderEvent(order: any): CalendarEvent {
    return this.saveEvent({
      title: `أمر دهان: ${order.order_number}`,
      description: `العميل: ${order.customer_name}\nالمساحة: ${order.total_area}م²\nالمبلغ: ₪${order.total_amount}`,
      date: order.order_date,
      time: '09:00',
      type: 'order',
      category: 'paint',
      status: 'pending',
      reference_id: order.id,
      reference_type: 'paint_order'
    })
  }

  // إنشاء حدث تسليم
  static createDeliveryEvent(order: any): CalendarEvent {
    return this.saveEvent({
      title: `تسليم: ${order.order_number}`,
      description: `تسليم أمر الدهان للعميل ${order.customer_name}`,
      date: order.expected_completion_date,
      time: '14:00',
      type: 'delivery',
      category: 'paint',
      status: 'pending',
      reference_id: order.id,
      reference_type: 'paint_order'
    })
  }

  // جلب بيانات يوم للتقويم
  static getCalendarDayData(date: dayjs.Dayjs, notifications: NotificationData[] = []): CalendarDayData {
    const dateStr = date.format('YYYY-MM-DD')
    const events = this.getEventsForDate(dateStr)
    const dayNotifications = notifications.filter(n => n.date === dateStr)
    
    return {
      date: dateStr,
      events,
      notifications: dayNotifications,
      hasEvents: events.length > 0 || dayNotifications.length > 0,
      eventCount: events.length,
      notificationCount: dayNotifications.length
    }
  }

  // جلب إحصائيات التقويم
  static getCalendarStats(year?: number, month?: number) {
    let events: CalendarEvent[]
    
    if (year !== undefined && month !== undefined) {
      events = this.getEventsForMonth(year, month)
    } else {
      events = this.getAllEvents()
    }

    return {
      total: events.length,
      byType: {
        order: events.filter(e => e.type === 'order').length,
        delivery: events.filter(e => e.type === 'delivery').length,
        meeting: events.filter(e => e.type === 'meeting').length,
        reminder: events.filter(e => e.type === 'reminder').length,
        notification: events.filter(e => e.type === 'notification').length
      },
      byCategory: {
        paint: events.filter(e => e.category === 'paint').length,
        sales: events.filter(e => e.category === 'sales').length,
        inventory: events.filter(e => e.category === 'inventory').length,
        general: events.filter(e => e.category === 'general').length
      },
      byStatus: {
        pending: events.filter(e => e.status === 'pending').length,
        completed: events.filter(e => e.status === 'completed').length,
        cancelled: events.filter(e => e.status === 'cancelled').length
      }
    }
  }

  // البحث في الأحداث
  static searchEvents(query: string): CalendarEvent[] {
    const lowerQuery = query.toLowerCase()
    
    return this.getAllEvents().filter(event =>
      event.title.toLowerCase().includes(lowerQuery) ||
      event.description?.toLowerCase().includes(lowerQuery)
    )
  }

  // جلب الأحداث القادمة
  static getUpcomingEvents(days: number = 7): CalendarEvent[] {
    const today = dayjs()
    const endDate = today.add(days, 'day')
    
    return this.getAllEvents()
      .filter(event => {
        const eventDate = dayjs(event.date)
        return eventDate.isBetween(today, endDate, 'day', '[]') && event.status === 'pending'
      })
      .sort((a, b) => dayjs(a.date).diff(dayjs(b.date)))
  }

  // جلب الأحداث المتأخرة
  static getOverdueEvents(): CalendarEvent[] {
    const today = dayjs()
    
    return this.getAllEvents()
      .filter(event => {
        const eventDate = dayjs(event.date)
        return eventDate.isBefore(today, 'day') && event.status === 'pending'
      })
      .sort((a, b) => dayjs(b.date).diff(dayjs(a.date)))
  }

  // تصدير الأحداث
  static exportEvents(format: 'json' | 'csv' = 'json'): string {
    const events = this.getAllEvents()
    
    if (format === 'csv') {
      const headers = ['التاريخ', 'العنوان', 'الوصف', 'النوع', 'الفئة', 'الحالة', 'الوقت']
      const rows = events.map(event => [
        event.date,
        event.title,
        event.description || '',
        event.type,
        event.category,
        event.status,
        event.time || ''
      ])
      
      return [headers, ...rows].map(row => row.join(',')).join('\n')
    }
    
    return JSON.stringify(events, null, 2)
  }

  // استيراد الأحداث
  static importEvents(data: string, format: 'json' | 'csv' = 'json'): number {
    try {
      let importedEvents: CalendarEvent[] = []
      
      if (format === 'json') {
        importedEvents = JSON.parse(data)
      } else {
        // معالجة CSV (مبسطة)
        const lines = data.split('\n')
        const _headers = lines[0].split(',')
        
        importedEvents = lines.slice(1).map(line => {
          const values = line.split(',')
          return {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            title: values[1] || '',
            description: values[2] || '',
            date: values[0] || '',
            time: values[6] || '',
            type: values[3] as any || 'reminder',
            category: values[4] as any || 'general',
            status: values[5] as any || 'pending',
            created_at: dayjs().format('YYYY-MM-DD HH:mm:ss')
          }
        }).filter(event => event.title && event.date)
      }
      
      const currentEvents = this.getAllEvents()
      const allEvents = [...currentEvents, ...importedEvents]
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(allEvents))
      
      return importedEvents.length
    } catch (error) {
      Logger.error('CalendarService', 'خطأ في استيراد الأحداث:', error)
      return 0
    }
  }

  // تنظيف الأحداث القديمة
  static cleanupOldEvents(daysOld: number = 90): number {
    const cutoffDate = dayjs().subtract(daysOld, 'day')
    const currentEvents = this.getAllEvents()
    const filteredEvents = currentEvents.filter(event => 
      dayjs(event.date).isAfter(cutoffDate) || event.status === 'pending'
    )
    
    const removedCount = currentEvents.length - filteredEvents.length
    
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredEvents))
    
    return removedCount
  }
}
