import React, { useState } from 'react';
import { Tag, Typography, Tabs, Card, Space } from 'antd';
import {
  ShoppingOutlined,
  TrophyOutlined,
  BarChartOutlined,
  DollarOutlined,
  AlertOutlined
} from '@ant-design/icons';
import UniversalReport from './UniversalReport';
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig';
import type { ReportData, ReportType } from '../../types/reports';

const { Text } = Typography;

const PurchasesByItemReport: React.FC = () => {
  const [activeTab, setActiveTab] = useState('all');

  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير المشتريات حسب الصنف...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getPurchasesByItemReport(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const purchasesByItemData = response.data;

      // معالجة البيانات
      const processedData = purchasesByItemData.map((item: any, index: number) => ({
        ...item,
        key: item.item_id || index,
        avg_price: item.total_quantity > 0 ? item.total_amount / item.total_quantity : 0
      }));

      // حساب الإحصائيات
      const totalItems = processedData.length;
      const totalQuantity = processedData.reduce((sum: number, item: any) => sum + item.total_quantity, 0);
      const totalAmount = processedData.reduce((sum: number, item: any) => sum + item.total_amount, 0);
      const totalInvoices = processedData.reduce((sum: number, item: any) => sum + item.invoice_count, 0);
      const avgPrice = totalQuantity > 0 ? totalAmount / totalQuantity : 0;

      const topItems = [...processedData]
        .sort((a: any, b: any) => b.total_amount - a.total_amount)
        .slice(0, 5)
        .map((item: any) => ({
          name: item.item_name,
          amount: item.total_amount
        }));

      const summary = {
        totalItems,
        totalQuantity: Math.round(totalQuantity * 100) / 100,
        totalAmount: Math.round(totalAmount * 100) / 100,
        totalInvoices,
        avgPrice: Math.round(avgPrice * 100) / 100,
        topItems
      };

      console.log('✅ تم إنشاء تقرير المشتريات حسب الصنف بنجاح');

      return {
        title: 'تقرير المشتريات حسب الصنف',
        data: processedData,
        columns,
        summary,
        metadata: {
          reportType: 'purchases_by_item' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: processedData.length
        }
      };
    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير المشتريات حسب الصنف:', error);
      throw error;
    }
  };
  // إعداد الأعمدة
  const columns = [
    {
      title: 'كود الصنف',
      dataIndex: 'item_code',
      key: 'item_code',
      width: 120,
      render: (code: string) => (
        <Text strong style={{ color: '#1890ff' }}>{code}</Text>
      )
    },
    {
      title: 'اسم الصنف',
      dataIndex: 'item_name',
      key: 'item_name',
      width: 200,
      render: (name: string) => (
        <Text strong>{name}</Text>
      )
    },
    {
      title: 'الفئة',
      dataIndex: 'category_name',
      key: 'category_name',
      width: 120,
      render: (category: string) => (
        <Tag color="purple">{category}</Tag>
      )
    },
    {
      title: 'إجمالي الكمية',
      dataIndex: 'total_quantity',
      key: 'total_quantity',
      width: 120,
      align: 'center' as const,
      render: (quantity: number) => (
        <Text strong style={{ color: '#52c41a' }}>
          {quantity.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'إجمالي المبلغ (₪)',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 150,
      align: 'right' as const,
      render: (amount: number) => (
        <Text strong style={{ color: '#1890ff' }}>
          {amount.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'متوسط السعر (₪)',
      dataIndex: 'avg_price',
      key: 'avg_price',
      width: 120,
      align: 'right' as const,
      render: (price: number) => (
        <Text style={{ color: '#fa8c16' }}>
          {price.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'عدد الفواتير',
      dataIndex: 'invoice_count',
      key: 'invoice_count',
      width: 120,
      align: 'center' as const,
      render: (count: number) => (
        <Tag color="blue">{count}</Tag>
      )
    },
    {
      title: 'عدد الموردين',
      dataIndex: 'supplier_count',
      key: 'supplier_count',
      width: 120,
      align: 'center' as const,
      render: (count: number) => (
        <Tag color="green">{count}</Tag>
      )
    },
    {
      title: 'آخر شراء',
      dataIndex: 'last_purchase_date',
      key: 'last_purchase_date',
      width: 140,
      render: (date: string) => date ? DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE) : '-'
    }
  ];
  return (
    <Card>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'all',
            label: (
              <Space>
                <ShoppingOutlined />
                جميع الأصناف
              </Space>
            ),
            children: (
              <UniversalReport
                reportType={'purchases_by_item' as ReportType}
                title="تقرير المشتريات حسب الصنف"
                description="تقرير تفصيلي للمشتريات مجمعة حسب الصنف مع الإحصائيات"
                onGenerateReport={generateReport}
                showDateRange={true}
                showSupplierFilter={true}
                showWarehouseFilter={true}
                showCategoryFilter={true}
                showItemFilter={true}
                showAmountRangeFilter={true}
                showPrintOptions={true}
                showExportOptions={true}
                defaultFilters={{
                  dateRange: null,
                  sortBy: 'total_amount',
                  sortOrder: 'desc'
                }}
              />
            )
          },
          {
            key: 'top',
            label: (
              <Space>
                <TrophyOutlined />
                الأصناف الأكثر شراءً
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <TrophyOutlined style={{ fontSize: '48px', color: '#faad14' }} />
                    <h3>أكثر الأصناف شراءً</h3>
                    <p>الأصناف الأعلى من حيث كمية ومبلغ المشتريات</p>
                  </div>
                </Space>
              </Card>
            )
          },
          {
            key: 'cost',
            label: (
              <Space>
                <DollarOutlined />
                تحليل التكاليف
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <DollarOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                    <h3>تحليل تكاليف الأصناف</h3>
                    <p>تحليل تطور أسعار الأصناف عبر الوقت</p>
                  </div>
                </Space>
              </Card>
            )
          },
          {
            key: 'trends',
            label: (
              <Space>
                <BarChartOutlined />
                الاتجاهات
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <BarChartOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                    <h3>اتجاهات المشتريات</h3>
                    <p>تحليل اتجاهات شراء الأصناف عبر الفترات</p>
                  </div>
                </Space>
              </Card>
            )
          },
          {
            key: 'alerts',
            label: (
              <Space>
                <AlertOutlined />
                التنبيهات
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <AlertOutlined style={{ fontSize: '48px', color: '#ff4d4f' }} />
                    <h3>تنبيهات المشتريات</h3>
                    <p>الأصناف التي تحتاج إعادة طلب أو مراجعة</p>
                  </div>
                </Space>
              </Card>
            )
          }
        ]}
      />
    </Card>
  );
};

export default PurchasesByItemReport;
