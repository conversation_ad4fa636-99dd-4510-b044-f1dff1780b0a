import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Alert,
  Progress,
  Space,
  Typography,
  List,
  Spin,
  Result,
  Divider,
  Row,
  Col,
  Tag,
  Statistic,
  notification
} from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  LockOutlined,
  FileProtectOutlined,
  Bar<PERSON><PERSON>Outlined,
  AuditOutlined
} from '@ant-design/icons';
import { FiscalPeriod, ClosingProgress, ValidationResult } from '../../types/fiscalPeriod';
import { fiscalPeriodApi } from '../../services/fiscalPeriodApi';

const { Step } = Steps;
const { Title, Text } = Typography;

interface ClosingWizardProps {
  period: FiscalPeriod;
  open: boolean;
  onClose: () => void;
  onComplete: () => void;
}

interface ClosingStep {
  title: string;
  description: string;
  status: 'wait' | 'process' | 'finish' | 'error';
  progress: number;
  details?: string[];
  errors?: string[];
}

const ClosingWizard: React.FC<ClosingWizardProps> = ({
  period,
  open,
  onClose,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [closingProgress, setClosingProgress] = useState<ClosingProgress | null>(null);
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [completed, setCompleted] = useState(false);

  const [steps, setSteps] = useState<ClosingStep[]>([
    {
      title: 'التحقق من صحة البيانات',
      description: 'فحص الميزان التجريبي والقيود المحاسبية',
      status: 'wait',
      progress: 0
    },
    {
      title: 'إنشاء نسخة احتياطية',
      description: 'حفظ نسخة احتياطية من البيانات قبل الإقفال',
      status: 'wait',
      progress: 0
    },
    {
      title: 'إقفال الحسابات',
      description: 'إقفال حسابات الإيرادات والمصروفات',
      status: 'wait',
      progress: 0
    },
    {
      title: 'ترحيل الأرصدة',
      description: 'ترحيل الأرصدة للفترة التالية',
      status: 'wait',
      progress: 0
    },
    {
      title: 'إنهاء الإقفال',
      description: 'تأكيد الإقفال وإنشاء التقارير النهائية',
      status: 'wait',
      progress: 0
    }
  ]);

  useEffect(() => {
    if (open && period) {
      resetWizard();
    }
  }, [open, period]);

  const resetWizard = () => {
    setCurrentStep(0);
    setLoading(false);
    setClosingProgress(null);
    setValidationResults([]);
    setError(null);
    setCompleted(false);
    setSteps(steps.map(step => ({ ...step, status: 'wait', progress: 0 })));
  };

  const updateStepStatus = (stepIndex: number, status: ClosingStep['status'], progress: number = 0, details?: string[], errors?: string[]) => {
    setSteps(prevSteps => 
      prevSteps.map((step, index) => 
        index === stepIndex 
          ? { ...step, status, progress, details, errors }
          : step
      )
    );
  };

  const startClosing = async () => {
    setLoading(true);
    setError(null);

    try {
      // الخطوة 1: التحقق من صحة البيانات
      await performValidation();
      
      // الخطوة 2: إنشاء نسخة احتياطية
      await createBackup();
      
      // الخطوة 3: إقفال الحسابات
      await performClosing();
      
      // الخطوة 4: ترحيل الأرصدة
      await carryForwardBalances();
      
      // الخطوة 5: إنهاء الإقفال
      await finalizeClosing();

      setCompleted(true);
      notification.success({
        message: 'تم الإقفال بنجاح',
        description: `تم إقفال الفترة المحاسبية "${period.period_name}" بنجاح`
      });

    } catch (error: any) {
      setError(error.message || 'حدث خطأ أثناء عملية الإقفال');
      notification.error({
        message: 'فشل في الإقفال',
        description: error.message || 'حدث خطأ أثناء عملية الإقفال'
      });
    } finally {
      setLoading(false);
    }
  };

  const performValidation = async () => {
    setCurrentStep(0);
    updateStepStatus(0, 'process', 10);

    const result = await fiscalPeriodApi.validatePeriodForClosing(period.id.toString());

    if (!result || result.length === 0) {
      updateStepStatus(0, 'error', 0, [], ['فشل في التحقق']);
      throw new Error('فشل في التحقق من صحة البيانات');
    }

    setValidationResults(result || []);

    const canClose = result.every(v => v.type !== 'error');
    if (!canClose) {
      const errors = result
        ?.filter(v => v.type === 'error')
        ?.map(v => v.message) || [];
      
      updateStepStatus(0, 'error', 0, [], errors);
      throw new Error('يوجد أخطاء يجب حلها قبل الإقفال');
    }

    updateStepStatus(0, 'finish', 100, ['تم التحقق من صحة البيانات بنجاح']);
  };

  const createBackup = async () => {
    setCurrentStep(1);
    updateStepStatus(1, 'process', 20);

    try {
      await fiscalPeriodApi.createClosingBackupSimple(period.id.toString());
    } catch (error: any) {
      updateStepStatus(1, 'error', 0, [], [error.message || 'فشل في إنشاء النسخة الاحتياطية']);
      throw new Error(error.message || 'فشل في إنشاء النسخة الاحتياطية');
    }

    updateStepStatus(1, 'finish', 100, [
      'تم إنشاء النسخة الاحتياطية بنجاح',
      'حجم الملف: غير محدد',
      'المسار: غير محدد'
    ]);
  };

  const performClosing = async () => {
    setCurrentStep(2);
    updateStepStatus(2, 'process', 30);

    const result = await fiscalPeriodApi.performPeriodClosing(period.id);
    
    if (!result.success) {
      updateStepStatus(2, 'error', 0, [], [result.message || 'فشل في إقفال الحسابات']);
      throw new Error(result.message || 'فشل في إقفال الحسابات');
    }

    setClosingProgress(result.data);
    
    updateStepStatus(2, 'finish', 100, [
      `تم إقفال ${result.data?.entriesCreated || 0} قيد محاسبي`,
      `إجمالي الإيرادات: ${result.data?.totalRevenue || 0}`,
      `إجمالي المصروفات: ${result.data?.totalExpenses || 0}`
    ]);
  };

  const carryForwardBalances = async () => {
    setCurrentStep(3);
    updateStepStatus(3, 'process', 70);

    const result = await fiscalPeriodApi.carryForwardBalances(period.id);
    
    if (!result.success) {
      updateStepStatus(3, 'error', 0, [], [result.message || 'فشل في ترحيل الأرصدة']);
      throw new Error(result.message || 'فشل في ترحيل الأرصدة');
    }

    updateStepStatus(3, 'finish', 100, [
      `تم ترحيل ${result.data?.accountsCarriedForward || 0} حساب`,
      'تم إنشاء الفترة المحاسبية التالية'
    ]);
  };

  const finalizeClosing = async () => {
    setCurrentStep(4);
    updateStepStatus(4, 'process', 90);

    const result = await fiscalPeriodApi.finalizePeriodClosing(period.id);
    
    if (!result.success) {
      updateStepStatus(4, 'error', 0, [], [result.message || 'فشل في إنهاء الإقفال']);
      throw new Error(result.message || 'فشل في إنهاء الإقفال');
    }

    updateStepStatus(4, 'finish', 100, [
      'تم إقفال الفترة المحاسبية نهائياً',
      'تم إنشاء تقارير الإقفال',
      'تم حفظ سجل المراجعة'
    ]);
  };

  const handleClose = () => {
    if (completed) {
      onComplete();
    } else {
      onClose();
    }
  };

  const renderStepContent = () => {
    const step = steps[currentStep];
    
    if (completed) {
      return (
        <Result
          status="success"
          title="تم إقفال الفترة المحاسبية بنجاح"
          subTitle={`تم إقفال الفترة "${period.period_name}" وإنشاء جميع التقارير المطلوبة`}
          extra={[
            <Button type="primary" key="reports" icon={<BarChartOutlined />}>
              عرض التقارير
            </Button>,
            <Button key="close" onClick={handleClose}>
              إغلاق
            </Button>
          ]}
        />
      );
    }

    return (
      <Card>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Title level={4}>{step.title}</Title>
          <Text type="secondary">{step.description}</Text>
          
          {step.status === 'process' && (
            <Progress percent={step.progress} status="active" />
          )}
          
          {step.details && step.details.length > 0 && (
            <List
              size="small"
              dataSource={step.details}
              renderItem={item => (
                <List.Item>
                  <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                  {item}
                </List.Item>
              )}
            />
          )}
          
          {step.errors && step.errors.length > 0 && (
            <Alert
              message="أخطاء يجب حلها"
              description={
                <List
                  size="small"
                  dataSource={step.errors}
                  renderItem={item => <List.Item>{item}</List.Item>}
                />
              }
              type="error"
              showIcon
            />
          )}
        </Space>
      </Card>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <LockOutlined />
          <span>معالج إقفال الفترة المحاسبية</span>
        </Space>
      }
      open={open}
      onCancel={handleClose}
      width={800}
      footer={
        <Space>
          <Button onClick={handleClose}>
            {completed ? 'إغلاق' : 'إلغاء'}
          </Button>
          {!completed && !loading && currentStep === 0 && (
            <Button type="primary" onClick={startClosing} icon={<LockOutlined />}>
              بدء الإقفال
            </Button>
          )}
        </Space>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* معلومات الفترة */}
        <Card size="small">
          <Row gutter={16}>
            <Col span={12}>
              <Statistic title="اسم الفترة" value={period.period_name} />
            </Col>
            <Col span={12}>
              <Statistic
                title="الحالة"
                value={period.status === 'open' ? 'مفتوحة' : period.status === 'closed' ? 'مغلقة' : 'مقفلة'}
                valueStyle={{ color: period.status === 'open' ? '#1890ff' : '#52c41a' }}
              />
            </Col>
          </Row>
        </Card>

        {/* خطوات الإقفال */}
        <Steps current={currentStep} direction="vertical" size="small">
          {steps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              status={step.status}
              icon={
                step.status === 'process' ? <LoadingOutlined /> :
                step.status === 'finish' ? <CheckCircleOutlined /> :
                step.status === 'error' ? <ExclamationCircleOutlined /> : undefined
              }
            />
          ))}
        </Steps>

        {/* محتوى الخطوة الحالية */}
        {renderStepContent()}

        {/* رسائل الخطأ العامة */}
        {error && (
          <Alert
            message="خطأ في عملية الإقفال"
            description={error}
            type="error"
            showIcon
            closable
            onClose={() => setError(null)}
          />
        )}
      </Space>
    </Modal>
  );
};

export default ClosingWizard; // Fixed syntax error
