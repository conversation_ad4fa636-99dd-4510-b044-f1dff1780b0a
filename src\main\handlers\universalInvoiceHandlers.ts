import { ipcMain } from 'electron'
import { UniversalInvoiceService, InvoiceType, CreateUniversalInvoiceData, UniversalInvoiceFilters } from '../services/UniversalInvoiceService'
import { RecurringInvoiceService, CreateRecurringTemplateData } from '../services/RecurringInvoiceService'
import { InvoiceAnalyticsService, InvoiceAnalyticsFilters } from '../services/InvoiceAnalyticsService'
import { InvoicePerformanceService } from '../services/InvoicePerformanceService'
import { Logger } from '../utils/logger'

let universalInvoiceService: UniversalInvoiceService
let recurringInvoiceService: RecurringInvoiceService
let invoiceAnalyticsService: InvoiceAnalyticsService
let invoicePerformanceService: InvoicePerformanceService

export function setUniversalInvoiceService(service: UniversalInvoiceService) {
  universalInvoiceService = service
}

/**
 * تسجيل معالجات IPC الموحدة للفواتير
 */
export function registerUniversalInvoiceHandlers(): void {
  universalInvoiceService = UniversalInvoiceService.getInstance()
  recurringInvoiceService = RecurringInvoiceService.getInstance()
  invoiceAnalyticsService = InvoiceAnalyticsService.getInstance()
  invoicePerformanceService = InvoicePerformanceService.getInstance()

  // ===== العمليات الأساسية =====

  /**
   * إنشاء فاتورة موحدة
   */
  ipcMain.handle('create-universal-invoice', async (_, data: CreateUniversalInvoiceData) => {
    try {
      Logger.info('UniversalInvoiceHandlers', `إنشاء فاتورة ${data.invoice_type}`)
      const result = await universalInvoiceService.createInvoice(data)
      Logger.info('UniversalInvoiceHandlers', `نتيجة إنشاء الفاتورة: ${result.success}`)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في إنشاء الفاتورة:', error)
      return {
        success: false,
        message: 'حدث خطأ في إنشاء الفاتورة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * تحديث فاتورة موحدة
   */
  ipcMain.handle('update-universal-invoice', async (_, invoiceType: InvoiceType, invoiceId: number, data: Partial<CreateUniversalInvoiceData>) => {
    try {
      Logger.info('UniversalInvoiceHandlers', `تحديث فاتورة ${invoiceType} رقم ${invoiceId}`)
      const result = await universalInvoiceService.updateInvoice(invoiceType, invoiceId, data)
      Logger.info('UniversalInvoiceHandlers', `نتيجة تحديث الفاتورة: ${result.success}`)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تحديث الفاتورة:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحديث الفاتورة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * حذف فاتورة موحدة
   */
  ipcMain.handle('delete-universal-invoice', async (_, invoiceType: InvoiceType, invoiceId: number) => {
    try {
      Logger.info('UniversalInvoiceHandlers', `حذف فاتورة ${invoiceType} رقم ${invoiceId}`)
      const result = await universalInvoiceService.deleteInvoice(invoiceType, invoiceId)
      Logger.info('UniversalInvoiceHandlers', `نتيجة حذف الفاتورة: ${result.success}`)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في حذف الفاتورة:', error)
      return {
        success: false,
        message: 'حدث خطأ في حذف الفاتورة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * جلب الفواتير مع الفلترة والصفحات
   */
  ipcMain.handle('get-universal-invoices', async (_, filters: UniversalInvoiceFilters = {}) => {
    try {
      Logger.info('UniversalInvoiceHandlers', 'جلب الفواتير مع الفلترة')
      const result = await universalInvoiceService.getInvoices(filters)
      Logger.info('UniversalInvoiceHandlers', `تم جلب ${result.data?.invoices?.length || 0} فاتورة`)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في جلب الفواتير:', error)
      return {
        success: false,
        message: 'حدث خطأ في جلب الفواتير',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * جلب إحصائيات الفواتير
   */
  ipcMain.handle('get-universal-invoice-statistics', async (_, filters: UniversalInvoiceFilters = {}) => {
    try {
      Logger.info('UniversalInvoiceHandlers', 'جلب إحصائيات الفواتير')
      const result = await universalInvoiceService.getStatistics(filters)
      Logger.info('UniversalInvoiceHandlers', `نتيجة جلب الإحصائيات: ${result.success}`)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في جلب الإحصائيات:', error)
      return {
        success: false,
        message: 'حدث خطأ في جلب الإحصائيات',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * توليد رقم فاتورة جديد
   */
  ipcMain.handle('generate-universal-invoice-number', async (_, invoiceType: InvoiceType) => {
    try {
      Logger.info('UniversalInvoiceHandlers', `توليد رقم فاتورة ${invoiceType}`)
      const invoiceNumber = await universalInvoiceService.generateInvoiceNumber(invoiceType)
      Logger.info('UniversalInvoiceHandlers', `تم توليد رقم الفاتورة: ${invoiceNumber}`)
      return {
        success: true,
        data: { invoiceNumber }
      }
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في توليد رقم الفاتورة:', error)
      return {
        success: false,
        message: 'حدث خطأ في توليد رقم الفاتورة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  // ===== عمليات متقدمة =====

  /**
   * تحويل فاتورة من نوع لآخر
   */
  ipcMain.handle('convert-invoice-type', async (_, fromType: InvoiceType, toType: InvoiceType, _invoiceId: number) => {
    try {
      Logger.info('UniversalInvoiceHandlers', `تحويل فاتورة من ${fromType} إلى ${toType}`)
      
      // جلب الفاتورة الأصلية
      const originalResult = await universalInvoiceService.getInvoices({
        invoice_type: fromType,
        page: 1,
        page_size: 1
      })

      if (!originalResult.success || !originalResult.data.invoices.length) {
        return { success: false, message: 'الفاتورة الأصلية غير موجودة' }
      }

      const originalInvoice = originalResult.data.invoices[0]

      // إنشاء فاتورة جديدة من النوع المطلوب
      const newInvoiceData: CreateUniversalInvoiceData = {
        invoice_type: toType,
        entity_id: originalInvoice.entity_id,
        invoice_date: new Date().toISOString().split('T')[0],
        notes: `تم التحويل من فاتورة ${fromType} رقم ${originalInvoice.invoice_number}`,
        items: originalInvoice.items?.map((item: any) => ({
          item_id: item.item_id,
          warehouse_id: item.warehouse_id,
          quantity: item.quantity,
          unit_price: item.unit_price
        })) || []
      }

      const result = await universalInvoiceService.createInvoice(newInvoiceData)
      Logger.info('UniversalInvoiceHandlers', `نتيجة تحويل الفاتورة: ${result.success}`)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تحويل الفاتورة:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحويل الفاتورة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * نسخ فاتورة
   */
  ipcMain.handle('duplicate-invoice', async (_, invoiceType: InvoiceType, invoiceId: number) => {
    try {
      Logger.info('UniversalInvoiceHandlers', `نسخ فاتورة ${invoiceType} رقم ${invoiceId}`)
      
      // جلب الفاتورة الأصلية
      const originalResult = await universalInvoiceService.getInvoices({
        invoice_type: invoiceType,
        page: 1,
        page_size: 1
      })

      if (!originalResult.success || !originalResult.data.invoices.length) {
        return { success: false, message: 'الفاتورة الأصلية غير موجودة' }
      }

      const originalInvoice = originalResult.data.invoices[0]

      // إنشاء نسخة جديدة
      const duplicateData: CreateUniversalInvoiceData = {
        invoice_type: invoiceType,
        entity_id: originalInvoice.entity_id,
        invoice_date: new Date().toISOString().split('T')[0],
        notes: `نسخة من فاتورة رقم ${originalInvoice.invoice_number}`,
        items: originalInvoice.items?.map((item: any) => ({
          item_id: item.item_id,
          warehouse_id: item.warehouse_id,
          quantity: item.quantity,
          unit_price: item.unit_price
        })) || []
      }

      const result = await universalInvoiceService.createInvoice(duplicateData)
      Logger.info('UniversalInvoiceHandlers', `نتيجة نسخ الفاتورة: ${result.success}`)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في نسخ الفاتورة:', error)
      return {
        success: false,
        message: 'حدث خطأ في نسخ الفاتورة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * تحديث حالة فاتورة
   */
  ipcMain.handle('update-invoice-status', async (_, invoiceType: InvoiceType, invoiceId: number, newStatus: string) => {
    try {
      Logger.info('UniversalInvoiceHandlers', `تحديث حالة فاتورة ${invoiceType} رقم ${invoiceId} إلى ${newStatus}`)
      
      const result = await universalInvoiceService.updateInvoice(invoiceType, invoiceId, {
        // status: newStatus as any // سيتم تحديث هذا حسب التنفيذ
      })
      
      Logger.info('UniversalInvoiceHandlers', `نتيجة تحديث الحالة: ${result.success}`)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تحديث حالة الفاتورة:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحديث حالة الفاتورة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * البحث في الفواتير
   */
  ipcMain.handle('search-invoices', async (_, searchTerm: string, invoiceTypes?: InvoiceType[]) => {
    try {
      Logger.info('UniversalInvoiceHandlers', `البحث في الفواتير: ${searchTerm}`)
      
      const filters: UniversalInvoiceFilters = {
        search_text: searchTerm,
        page: 1,
        page_size: 100
      }

      if (invoiceTypes && invoiceTypes.length === 1) {
        filters.invoice_type = invoiceTypes[0]
      }

      const result = await universalInvoiceService.getInvoices(filters)
      Logger.info('UniversalInvoiceHandlers', `تم العثور على ${result.data?.invoices?.length || 0} فاتورة`)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في البحث:', error)
      return {
        success: false,
        message: 'حدث خطأ في البحث',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * تصدير الفواتير
   */
  ipcMain.handle('export-invoices', async (_, filters: UniversalInvoiceFilters, format: 'excel' | 'pdf' | 'csv') => {
    try {
      Logger.info('UniversalInvoiceHandlers', `تصدير الفواتير بصيغة ${format}`)
      
      // جلب جميع الفواتير المطلوبة
      const result = await universalInvoiceService.getInvoices({
        ...filters,
        page: 1,
        page_size: 10000 // جلب جميع الفواتير
      })

      if (!result.success) {
        return result
      }

      // هنا يمكن إضافة منطق التصدير الفعلي
      // مؤقتاً نرجع البيانات فقط
      return {
        success: true,
        data: {
          invoices: result.data.invoices,
          format,
          exported_at: new Date().toISOString()
        },
        message: `تم تصدير ${result.data.invoices.length} فاتورة بصيغة ${format}`
      }
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في التصدير:', error)
      return {
        success: false,
        message: 'حدث خطأ في التصدير',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  // ===== معالجات الفواتير المتكررة =====

  /**
   * إنشاء قالب فاتورة متكررة
   */
  ipcMain.handle('create-recurring-template', async (_, data: CreateRecurringTemplateData, userId: number) => {
    try {
      Logger.info('UniversalInvoiceHandlers', 'إنشاء قالب فاتورة متكررة')
      const result = await recurringInvoiceService.createTemplate(data, userId)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في إنشاء القالب:', error)
      return {
        success: false,
        message: 'حدث خطأ في إنشاء قالب الفاتورة المتكررة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * جلب قوالب الفواتير المتكررة
   */
  ipcMain.handle('get-recurring-templates', async () => {
    try {
      const result = await recurringInvoiceService.getTemplates()
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في جلب القوالب:', error)
      return {
        success: false,
        message: 'حدث خطأ في جلب قوالب الفواتير المتكررة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * تحديث قالب فاتورة متكررة
   */
  ipcMain.handle('update-recurring-template', async (_, templateId: number, data: Partial<CreateRecurringTemplateData>) => {
    try {
      const result = await recurringInvoiceService.updateTemplate(templateId, data)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تحديث القالب:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحديث قالب الفاتورة المتكررة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * حذف قالب فاتورة متكررة
   */
  ipcMain.handle('delete-recurring-template', async (_, templateId: number) => {
    try {
      const result = await recurringInvoiceService.deleteTemplate(templateId)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في حذف القالب:', error)
      return {
        success: false,
        message: 'حدث خطأ في حذف قالب الفاتورة المتكررة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * تفعيل/إلغاء تفعيل قالب
   */
  ipcMain.handle('toggle-recurring-template', async (_, templateId: number, isActive: boolean) => {
    try {
      const result = await recurringInvoiceService.toggleTemplate(templateId, isActive)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تغيير حالة القالب:', error)
      return {
        success: false,
        message: 'حدث خطأ في تغيير حالة القالب',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * معالجة الفواتير المتكررة يدوياً
   */
  ipcMain.handle('process-recurring-invoices', async () => {
    try {
      await recurringInvoiceService.processRecurringInvoices()
      return {
        success: true,
        message: 'تم معالجة الفواتير المتكررة بنجاح'
      }
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في معالجة الفواتير المتكررة:', error)
      return {
        success: false,
        message: 'حدث خطأ في معالجة الفواتير المتكررة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * جلب سجل توليد الفواتير المتكررة
   */
  ipcMain.handle('get-recurring-generation-logs', async (_, templateId?: number) => {
    try {
      const result = await recurringInvoiceService.getGenerationLogs(templateId)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في جلب سجل التوليد:', error)
      return {
        success: false,
        message: 'حدث خطأ في جلب سجل توليد الفواتير',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  // ===== معالجات التحليلات =====

  /**
   * تحليل الإيرادات
   */
  ipcMain.handle('get-revenue-analytics', async (_, filters: InvoiceAnalyticsFilters) => {
    try {
      const result = await invoiceAnalyticsService.getRevenueAnalytics(filters)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تحليل الإيرادات:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل الإيرادات',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * تحليل طرق الدفع
   */
  ipcMain.handle('get-payment-analytics', async (_, filters: InvoiceAnalyticsFilters) => {
    try {
      const result = await invoiceAnalyticsService.getPaymentAnalytics(filters)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تحليل طرق الدفع:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل طرق الدفع',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * تحليل الفواتير المتأخرة
   */
  ipcMain.handle('get-overdue-analytics', async (_, filters: InvoiceAnalyticsFilters) => {
    try {
      const result = await invoiceAnalyticsService.getOverdueAnalytics(filters)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تحليل الفواتير المتأخرة:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل الفواتير المتأخرة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * تحليل العملاء
   */
  ipcMain.handle('get-customer-analytics', async (_, filters: InvoiceAnalyticsFilters) => {
    try {
      const result = await invoiceAnalyticsService.getCustomerAnalytics(filters)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تحليل العملاء:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل العملاء',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * تحليل الأصناف
   */
  ipcMain.handle('get-item-analytics', async (_, filters: InvoiceAnalyticsFilters) => {
    try {
      const result = await invoiceAnalyticsService.getItemAnalytics(filters)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تحليل الأصناف:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل الأصناف',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * تحليل الاتجاهات
   */
  ipcMain.handle('get-trend-analytics', async (_, filters: InvoiceAnalyticsFilters) => {
    try {
      const result = await invoiceAnalyticsService.getTrendAnalytics(filters)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تحليل الاتجاهات:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل الاتجاهات',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  // ===== معالجات الأداء =====

  /**
   * تحليل أداء الاستعلامات
   */
  ipcMain.handle('analyze-query-performance', async () => {
    try {
      const result = await invoicePerformanceService.analyzeQueryPerformance()
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تحليل الأداء:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل أداء الاستعلامات',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * تحسين قاعدة البيانات
   */
  ipcMain.handle('optimize-database', async () => {
    try {
      const result = await invoicePerformanceService.optimizeDatabase()
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تحسين قاعدة البيانات:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحسين قاعدة البيانات',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * مراقبة استخدام الذاكرة
   */
  ipcMain.handle('monitor-memory-usage', async () => {
    try {
      const result = await invoicePerformanceService.monitorMemoryUsage()
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في مراقبة الذاكرة:', error)
      return {
        success: false,
        message: 'حدث خطأ في مراقبة استخدام الذاكرة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  /**
   * تنظيف البيانات القديمة
   */
  ipcMain.handle('cleanup-old-data', async (_, daysToKeep: number = 365) => {
    try {
      const result = await invoicePerformanceService.cleanupOldData(daysToKeep)
      return result
    } catch (error) {
      Logger.error('UniversalInvoiceHandlers', 'خطأ في تنظيف البيانات:', error)
      return {
        success: false,
        message: 'حدث خطأ في تنظيف البيانات القديمة',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  Logger.info('UniversalInvoiceHandlers', '✅ تم تسجيل جميع معالجات الفواتير الموحدة والمتقدمة')
}
