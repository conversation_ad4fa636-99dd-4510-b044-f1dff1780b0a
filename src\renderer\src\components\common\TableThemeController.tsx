import React, { useState } from 'react'
import { 
  Card, 
  Select, 
  Checkbox, 
  Space, 
  Typography, 
  Button, 
  Tooltip,
  Dropdown,
  Slider,
  ColorPicker
} from 'antd'
import {
  SettingOutlined,
  TableOutlined,
  BgColorsOutlined,
  FontSizeOutlined,
  BorderOutlined,
  EyeOutlined
} from '@ant-design/icons'

const { Text } = Typography
const { Option } = Select

export interface TableThemeConfig {
  theme: 'default' | 'compact' | 'comfortable' | 'spacious'
  showRowNumbers: boolean
  stickyHeader: boolean
  fontSize: number
  rowHeight: 'auto' | 'small' | 'medium' | 'large'
  borderStyle: 'none' | 'horizontal' | 'vertical' | 'all'
  stripedRows: boolean
  hoverEffect: boolean
  primaryColor: string
  headerBackground: string
}

interface TableThemeControllerProps {
  config: TableThemeConfig
  onChange: (config: TableThemeConfig) => void
  showAdvanced?: boolean
}

const TableThemeController: React.FC<TableThemeControllerProps> = ({
  config,
  onChange,
  showAdvanced = false
}) => {
  const [showDropdown, setShowDropdown] = useState(false)

  const updateConfig = (updates: Partial<TableThemeConfig>) => {
    onChange({ ...config, ...updates })
  }

  const basicControls = (
    <Space direction="vertical" style={{ width: '100%' }}>
      <div>
        <Text strong style={{ fontSize: 12, marginBottom: 8, display: 'block' }}>
          <TableOutlined style={{ marginLeft: 6 }} />
          نمط الجدول
        </Text>
        <Select
          value={config.theme}
          style={{ width: '100%' }}
          onChange={(value) => updateConfig({ theme: value })}
        >
          <Option value="default">افتراضي</Option>
          <Option value="compact">مضغوط</Option>
          <Option value="comfortable">مريح</Option>
          <Option value="spacious">واسع</Option>
        </Select>
      </div>

      <Space direction="vertical" style={{ width: '100%' }}>
        <Checkbox 
          checked={config.showRowNumbers}
          onChange={(e) => updateConfig({ showRowNumbers: e.target.checked })}
        >
          إظهار أرقام الصفوف
        </Checkbox>
        
        <Checkbox 
          checked={config.stickyHeader}
          onChange={(e) => updateConfig({ stickyHeader: e.target.checked })}
        >
          رأس ثابت
        </Checkbox>
        
        <Checkbox 
          checked={config.stripedRows}
          onChange={(e) => updateConfig({ stripedRows: e.target.checked })}
        >
          صفوف مخططة
        </Checkbox>
        
        <Checkbox 
          checked={config.hoverEffect}
          onChange={(e) => updateConfig({ hoverEffect: e.target.checked })}
        >
          تأثير التمرير
        </Checkbox>
      </Space>
    </Space>
  )

  const advancedControls = (
    <Space direction="vertical" style={{ width: '100%' }}>
      <div>
        <Text strong style={{ fontSize: 12, marginBottom: 8, display: 'block' }}>
          <FontSizeOutlined style={{ marginLeft: 6 }} />
          حجم الخط: {config.fontSize}px
        </Text>
        <Slider
          min={10}
          max={18}
          value={config.fontSize}
          onChange={(value) => updateConfig({ fontSize: value })}
          marks={{
            10: '10px',
            14: '14px',
            18: '18px'
          }}
        />
      </div>

      <div>
        <Text strong style={{ fontSize: 12, marginBottom: 8, display: 'block' }}>
          <BorderOutlined style={{ marginLeft: 6 }} />
          نمط الحدود
        </Text>
        <Select
          value={config.borderStyle}
          style={{ width: '100%' }}
          onChange={(value) => updateConfig({ borderStyle: value })}
        >
          <Option value="none">بدون حدود</Option>
          <Option value="horizontal">أفقية فقط</Option>
          <Option value="vertical">عمودية فقط</Option>
          <Option value="all">جميع الحدود</Option>
        </Select>
      </div>

      <div>
        <Text strong style={{ fontSize: 12, marginBottom: 8, display: 'block' }}>
          ارتفاع الصفوف
        </Text>
        <Select
          value={config.rowHeight}
          style={{ width: '100%' }}
          onChange={(value) => updateConfig({ rowHeight: value })}
        >
          <Option value="auto">تلقائي</Option>
          <Option value="small">صغير</Option>
          <Option value="medium">متوسط</Option>
          <Option value="large">كبير</Option>
        </Select>
      </div>

      <div>
        <Text strong style={{ fontSize: 12, marginBottom: 8, display: 'block' }}>
          <BgColorsOutlined style={{ marginLeft: 6 }} />
          الألوان
        </Text>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Text style={{ fontSize: 11 }}>اللون الأساسي:</Text>
            <ColorPicker
              value={config.primaryColor}
              onChange={(color) => updateConfig({ primaryColor: color.toHexString() })}
              size="small"
            />
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Text style={{ fontSize: 11 }}>خلفية الرأس:</Text>
            <ColorPicker
              value={config.headerBackground}
              onChange={(color) => updateConfig({ headerBackground: color.toHexString() })}
              size="small"
            />
          </div>
        </Space>
      </div>
    </Space>
  )

  const dropdownContent = (
    <Card 
      size="small" 
      style={{ width: 280, maxHeight: 400, overflow: 'auto' }}
      title={
        <Space>
          <SettingOutlined />
          <span>إعدادات الجدول</span>
        </Space>
      }
    >
      {basicControls}
      {showAdvanced && (
        <>
          <div style={{ margin: '12px 0', borderTop: '1px solid #f0f0f0' }} />
          {advancedControls}
        </>
      )}
    </Card>
  )

  return (
    <Dropdown
      open={showDropdown}
      onOpenChange={setShowDropdown}
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="bottomRight"
    >
      <Tooltip title="إعدادات مظهر الجدول">
        <Button 
          icon={<SettingOutlined />}
          size="small"
          type={showDropdown ? 'primary' : 'default'}
        >
          مظهر الجدول
        </Button>
      </Tooltip>
    </Dropdown>
  )
}

export default TableThemeController
