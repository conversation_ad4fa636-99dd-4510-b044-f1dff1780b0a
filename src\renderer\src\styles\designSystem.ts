// نظام التصميم الموحد المستوحى من شاشة التفعيل المحسنة
export const designSystem = {
  // الألوان الأساسية المستوحاة من شاشة التفعيل
  colors: {
    // التدرجات الأساسية
    gradients: {
      primary: 'linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%)',
      secondary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      success: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
      warning: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      danger: 'linear-gradient(135deg, #fc466b 0%, #3f5efb 100%)',
      background: 'linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%)',
      text: 'linear-gradient(135deg, #FF6B6B, #4ECDC4)',
      shimmer: 'linear-gradient(90deg, #FF6B6B, #4ECDC4, #45B7D1, #FF6B6B)',
      animated: 'linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%)'
    },
    
    // الألوان الصلبة
    primary: '#FF6B6B',
    secondary: '#4ECDC4', 
    accent: '#45B7D1',
    success: '#fff3cd',
    warning: '#faad14',
    danger: '#ff4d4f',
    info: '#1890ff',
    
    // ألوان الخلفية
    background: {
      primary: '#ffffff',
      secondary: '#f0f2f5',
      dark: '#001529',
      glass: 'rgba(255, 255, 255, 0.95)',
      overlay: 'rgba(0, 0, 0, 0.3)',
      card: 'rgba(255, 255, 255, 0.95)'
    },
    
    // ألوان النص
    text: {
      primary: '#262626',
      secondary: '#666666',
      disabled: '#bfbfbf',
      light: '#ffffff',
      gradient: 'transparent'
    },
    
    // ألوان الحدود
    border: {
      light: '#f0f0f0',
      base: '#e1e5e9',
      dark: '#434343',
      focus: '#FF6B6B',
      glass: 'rgba(255, 255, 255, 0.3)'
    }
  },
  
  // أحجام الخط
  fontSize: {
    xs: '11px',
    sm: '12px',
    base: '14px',
    md: '16px',
    lg: '18px',
    xl: '20px',
    '2xl': '24px',
    '3xl': '28px',
    '4xl': '36px',
    '5xl': '48px'
  },
  
  // أوزان الخط
  fontWeight: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900
  },
  
  // المسافات
  spacing: {
    xs: '4px',
    sm: '8px',
    base: '16px',
    md: '20px',
    lg: '24px',
    xl: '32px',
    '2xl': '40px',
    '3xl': '48px',
    '4xl': '64px',
    '5xl': '80px'
  },
  
  // نصف قطر الحواف
  borderRadius: {
    none: '0',
    sm: '6px',
    base: '8px',
    md: '12px',
    lg: '16px',
    xl: '20px',
    '2xl': '24px',
    full: '50%'
  },
  
  // الظلال المحسنة
  shadows: {
    sm: '0 2px 4px rgba(0, 0, 0, 0.1)',
    base: '0 4px 12px rgba(0, 0, 0, 0.1)',
    md: '0 8px 25px rgba(0, 0, 0, 0.15)',
    lg: '0 15px 35px rgba(255, 107, 107, 0.4), 0 5px 15px rgba(0, 0, 0, 0.1)',
    xl: '0 25px 80px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2)',
    glow: '0 0 0 3px rgba(255, 107, 107, 0.1)',
    focus: '0 0 0 3px rgba(255, 107, 107, 0.2)',
    button: '0 8px 25px rgba(255, 107, 107, 0.4)',
    card: '0 25px 80px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.2)'
  },
  
  // التحولات والانتقالات
  transitions: {
    fast: '0.15s ease',
    base: '0.3s ease',
    slow: '0.6s ease',
    bounce: '0.3s cubic-bezier(0.16, 1, 0.3, 1)',
    elastic: '0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    smooth: 'all 0.3s ease'
  },
  
  // الأبعاد
  dimensions: {
    input: {
      height: '60px',
      heightSm: '40px',
      heightLg: '60px'
    },
    button: {
      height: '60px',
      heightSm: '40px', 
      heightLg: '60px'
    },
    logo: {
      size: '100px',
      sizeSm: '60px',
      sizeLg: '120px'
    },
    card: {
      width: '500px',
      maxWidth: '90vw'
    }
  },
  
  // الخطوط
  fonts: {
    primary: "'Segoe UI', Tahoma, Arial, sans-serif",
    secondary: "'Cairo', 'Segoe UI', sans-serif"
  }
}

// الأنيميشن والحركات
export const animations = {
  // أنيميشن الظهور
  fadeIn: `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
  `,
  
  // أنيميشن الانزلاق للأعلى
  slideUp: `
    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(80px) scale(0.8) rotateX(10deg);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1) rotateX(0deg);
      }
    }
  `,
  
  // أنيميشن التدرج المتحرك
  gradientShift: `
    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }
  `,
  
  // أنيميشن البريق
  shimmer: `
    @keyframes shimmer {
      0% { background-position: -200% 0; }
      100% { background-position: 200% 0; }
    }
  `,
  
  // أنيميشن الطفو
  logoFloat: `
    @keyframes logoFloat {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }
  `,
  
  // أنيميشن النبض
  pulse: `
    @keyframes pulse {
      0%, 100% {
        transform: scale(1);
        opacity: 0.3;
      }
      50% {
        transform: scale(1.1);
        opacity: 0.6;
      }
    }
  `,
  
  // أنيميشن النبض للزر
  buttonPulse: `
    @keyframes buttonPulse {
      0% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); }
      100% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); }
    }
  `
}

// تصدير كل شيء
export default designSystem
