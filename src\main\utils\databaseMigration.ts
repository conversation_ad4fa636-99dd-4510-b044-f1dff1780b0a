import { Logger } from './logger'

/**
 * خدمة Migration قاعدة البيانات
 * تتعامل مع إصلاح مشاكل قاعدة البيانات والتحديثات التدريجية
 * متوافقة مع sql.js
 */
export class DatabaseMigration {
  private db: any
  private currentVersion: number = 1

  constructor(database: any) {
    this.db = database
  }

  /**
   * تشغيل جميع migrations المطلوبة
   */
  public async runMigrations(): Promise<void> {
    try {
      Logger.info('DatabaseMigration', '🔄 بدء تشغيل Database Migrations...')

      // إنشاء جدول إصدارات قاعدة البيانات
      this.createVersionTable()

      // الحصول على الإصدار الحالي
      const currentVersion = this.getCurrentVersion()
      Logger.info('DatabaseMigration', `📊 الإصدار الحالي لقاعدة البيانات: ${currentVersion}`)

      // تشغيل migrations حسب الإصدار
      if (currentVersion < 1) {
        await this.migration_v1_fixInvoicePayments()
        this.updateVersion(1)
      }

      if (currentVersion < 2) {
        await this.migration_v2_fixProductionOrderCode()
        this.updateVersion(2)
      }

      if (currentVersion < 3) {
        await this.migration_v3_addMissingIndexes()
        this.updateVersion(3)
      }

      Logger.info('DatabaseMigration', '✅ تم إكمال جميع Database Migrations بنجاح')
    } catch (error) {
      Logger.error('DatabaseMigration', '❌ خطأ في تشغيل Database Migrations:', error)
      throw error
    }
  }

  /**
   * إنشاء جدول إصدارات قاعدة البيانات
   */
  private createVersionTable(): void {
    try {
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS database_version (
          id INTEGER PRIMARY KEY,
          version INTEGER NOT NULL,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // إدراج الإصدار الافتراضي إذا لم يكن موجوداً
      const versionExists = this.db.prepare('SELECT COUNT(*) as count FROM database_version').get() as any
      if (!versionExists || versionExists.count === 0) {
        this.db.prepare('INSERT INTO database_version (id, version) VALUES (1, 0)').run()
      }
    } catch (error) {
      Logger.warn('DatabaseMigration', 'تحذير في إنشاء جدول الإصدارات:', error)
    }
  }

  /**
   * الحصول على الإصدار الحالي لقاعدة البيانات
   */
  private getCurrentVersion(): number {
    try {
      const result = this.db.prepare('SELECT version FROM database_version WHERE id = 1').get() as any
      return result ? result.version : 0
    } catch (error) {
      Logger.warn('DatabaseMigration', 'تحذير في قراءة إصدار قاعدة البيانات:', error)
      return 0
    }
  }

  /**
   * تحديث إصدار قاعدة البيانات
   */
  private updateVersion(version: number): void {
    try {
      this.db.prepare('UPDATE database_version SET version = ?, updated_at = CURRENT_TIMESTAMP WHERE id = 1').run(version)
      Logger.info('DatabaseMigration', `📈 تم تحديث إصدار قاعدة البيانات إلى: ${version}`)
    } catch (error) {
      Logger.error('DatabaseMigration', 'خطأ في تحديث إصدار قاعدة البيانات:', error)
    }
  }

  /**
   * Migration v1: إصلاح جدول invoice_payments
   */
  private async migration_v1_fixInvoicePayments(): Promise<void> {
    try {
      Logger.info('DatabaseMigration', '🔧 Migration v1: إصلاح جدول invoice_payments...')

      // التحقق من وجود الجدول
      const tableExists = this.db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='invoice_payments'
      `).get()

      if (tableExists) {
        // فحص الأعمدة الموجودة
        const columns = this.db.prepare('PRAGMA table_info(invoice_payments)').all() as any[]
        const columnNames = columns.map(col => col.name)

        // إضافة payment_date إذا لم يكن موجوداً
        if (!columnNames.includes('payment_date')) {
          this.db.exec('ALTER TABLE invoice_payments ADD COLUMN payment_date DATE')
          Logger.info('DatabaseMigration', '✅ تم إضافة عمود payment_date')
        }

        // إضافة payment_method إذا لم يكن موجوداً
        if (!columnNames.includes('payment_method')) {
          this.db.exec('ALTER TABLE invoice_payments ADD COLUMN payment_method TEXT')
          Logger.info('DatabaseMigration', '✅ تم إضافة عمود payment_method')
        }
      }

      Logger.info('DatabaseMigration', '✅ تم إكمال Migration v1 بنجاح')
    } catch (error) {
      Logger.error('DatabaseMigration', '❌ خطأ في Migration v1:', error)
      throw error
    }
  }

  /**
   * Migration v2: إصلاح order_code في production_orders
   */
  private async migration_v2_fixProductionOrderCode(): Promise<void> {
    try {
      Logger.info('DatabaseMigration', '🔧 Migration v2: إصلاح order_code في production_orders...')

      // التحقق من وجود الجدول
      const tableExists = this.db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='production_orders'
      `).get()

      if (tableExists) {
        // فحص الأعمدة الموجودة
        const columns = this.db.prepare('PRAGMA table_info(production_orders)').all() as any[]
        const columnNames = columns.map(col => col.name)

        // إضافة order_code إذا لم يكن موجوداً
        if (!columnNames.includes('order_code')) {
          this.db.exec('ALTER TABLE production_orders ADD COLUMN order_code TEXT')
          Logger.info('DatabaseMigration', '✅ تم إضافة عمود order_code')

          // تحديث السجلات الموجودة بقيم فريدة
          const existingOrders = this.db.prepare('SELECT id FROM production_orders WHERE order_code IS NULL').all()
          for (let i = 0; i < existingOrders.length; i++) {
            const order = existingOrders[i] as any
            const orderCode = 'PO-' + (i + 1).toString().padStart(6, '0')
            this.db.prepare('UPDATE production_orders SET order_code = ? WHERE id = ?').run(orderCode, order.id)
          }
          Logger.info('DatabaseMigration', `✅ تم تحديث ${existingOrders.length} سجل بأكواد فريدة`)

          // إنشاء فهرس فريد
          try {
            this.db.exec('CREATE UNIQUE INDEX IF NOT EXISTS idx_production_orders_code ON production_orders(order_code)')
            Logger.info('DatabaseMigration', '✅ تم إنشاء فهرس فريد لـ order_code')
          } catch (indexError) {
            Logger.warn('DatabaseMigration', 'تحذير في إنشاء فهرس order_code:', indexError)
          }
        }
      }

      Logger.info('DatabaseMigration', '✅ تم إكمال Migration v2 بنجاح')
    } catch (error) {
      Logger.error('DatabaseMigration', '❌ خطأ في Migration v2:', error)
      throw error
    }
  }

  /**
   * Migration v3: إضافة الفهارس المفقودة
   */
  private async migration_v3_addMissingIndexes(): Promise<void> {
    try {
      Logger.info('DatabaseMigration', '🔧 Migration v3: إضافة الفهارس المفقودة...')

      // فهارس آمنة لا تسبب أخطاء
      const safeIndexes = [
        'CREATE INDEX IF NOT EXISTS idx_sales_invoices_date ON sales_invoices(invoice_date)',
        'CREATE INDEX IF NOT EXISTS idx_sales_invoices_customer ON sales_invoices(customer_id)',
        'CREATE INDEX IF NOT EXISTS idx_sales_invoices_status ON sales_invoices(status)',
        'CREATE INDEX IF NOT EXISTS idx_purchase_invoices_date ON purchase_invoices(invoice_date)',
        'CREATE INDEX IF NOT EXISTS idx_purchase_invoices_supplier ON purchase_invoices(supplier_id)',
        'CREATE INDEX IF NOT EXISTS idx_purchase_invoices_status ON purchase_invoices(status)',
        'CREATE INDEX IF NOT EXISTS idx_invoice_payments_invoice ON invoice_payments(invoice_id)',
        'CREATE INDEX IF NOT EXISTS idx_invoice_payments_type ON invoice_payments(invoice_type)'
      ]

      let successCount = 0
      for (const indexQuery of safeIndexes) {
        try {
          this.db.exec(indexQuery)
          successCount++
        } catch (error) {
          Logger.warn('DatabaseMigration', `تحذير في إنشاء فهرس: ${error}`)
        }
      }

      Logger.info('DatabaseMigration', `✅ تم إنشاء ${successCount} فهرس بنجاح`)
      Logger.info('DatabaseMigration', '✅ تم إكمال Migration v3 بنجاح')
    } catch (error) {
      Logger.error('DatabaseMigration', '❌ خطأ في Migration v3:', error)
      throw error
    }
  }

  /**
   * فحص سلامة قاعدة البيانات
   */
  public async checkDatabaseIntegrity(): Promise<boolean> {
    try {
      Logger.info('DatabaseMigration', '🔍 فحص سلامة قاعدة البيانات...')

      // فحص سلامة SQLite
      const integrityResult = this.db.prepare('PRAGMA integrity_check').get() as any
      if (integrityResult.integrity_check !== 'ok') {
        Logger.error('DatabaseMigration', '❌ فشل فحص سلامة قاعدة البيانات:', integrityResult)
        return false
      }

      // فحص الجداول المهمة
      const requiredTables = [
        'users', 'items', 'warehouses', 'customers', 'suppliers',
        'sales_invoices', 'purchase_invoices', 'invoice_payments'
      ]

      for (const table of requiredTables) {
        const tableExists = this.db.prepare(`
          SELECT name FROM sqlite_master 
          WHERE type='table' AND name=?
        `).get(table)

        if (!tableExists) {
          Logger.error('DatabaseMigration', `❌ الجدول المطلوب غير موجود: ${table}`)
          return false
        }
      }

      Logger.info('DatabaseMigration', '✅ فحص سلامة قاعدة البيانات مكتمل بنجاح')
      return true
    } catch (error) {
      Logger.error('DatabaseMigration', '❌ خطأ في فحص سلامة قاعدة البيانات:', error)
      return false
    }
  }
}
