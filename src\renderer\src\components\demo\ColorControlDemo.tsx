import React, { useState } from 'react'
import { Card, Row, Col, Button, Input, Space, Alert, Typography, Divider } from 'antd'
import { BgColorsOutlined, PrinterOutlined, EyeOutlined } from '@ant-design/icons'
import { usePrintSettings, usePrintColors } from '../../hooks/usePrintSettings'
import UnifiedPrintButton from '../common/UnifiedPrintButton'

const { Title, Text } = Typography

/**
 * مكون توضيحي لإظهار كيفية عمل التحكم في الألوان مع النظام المركزي
 */
const ColorControlDemo: React.FC = () => {
  const { updateSettings, isReady } = usePrintSettings()
  const colors = usePrintColors()
  const [previewColors, setPreviewColors] = useState({
    primaryColor: colors.primaryColor,
    secondaryColor: colors.secondaryColor,
    borderColor: colors.borderColor,
    backgroundColor: colors.backgroundColor,
    textColor: colors.textColor
  })

  // بيانات تجريبية للطباعة
  const samplePrintData = {
    id: 'DEMO-001',
    title: 'فاتورة تجريبية',
    number: 'INV-2024-001',
    date: new Date().toISOString().split('T')[0],
    customer: {
      name: 'عميل تجريبي',
      phone: '123456789'
    },
    items: [
      {
        id: 1,
        name: 'منتج تجريبي 1',
        quantity: 2,
        unitPrice: 100,
        total: 200
      },
      {
        id: 2,
        name: 'منتج تجريبي 2',
        quantity: 1,
        unitPrice: 150,
        total: 150
      }
    ],
    total: 350,
    notes: 'هذه فاتورة تجريبية لإظهار تأثير الألوان'
  }

  // تطبيق الألوان على النظام المركزي
  const applyColors = async () => {
    if (updateSettings) {
      await updateSettings(previewColors)
    }
  }

  // إعادة تعيين الألوان للافتراضية
  const resetColors = async () => {
    const defaultColors = {
      primaryColor: '#1890ff',
      secondaryColor: '#fff3cd',
      borderColor: '#d9d9d9',
      backgroundColor: '#ffffff',
      textColor: '#000000'
    }
    
    setPreviewColors(defaultColors)
    
    if (updateSettings) {
      await updateSettings(defaultColors)
    }
  }

  // معاينة الألوان
  const ColorPreview: React.FC<{ color: string; label: string }> = ({ color, label }) => (
    <div style={{ textAlign: 'center' }}>
      <div
        style={{
          width: 60,
          height: 60,
          backgroundColor: color,
          border: '2px solid #d9d9d9',
          borderRadius: 8,
          margin: '0 auto 8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: color === '#ffffff' ? '#000' : '#fff',
          fontSize: 12,
          fontWeight: 'bold'
        }}
      >
        {color}
      </div>
      <Text style={{ fontSize: 12 }}>{label}</Text>
    </div>
  )

  if (!isReady) {
    return (
      <Card>
        <Alert message="جاري تحميل النظام المركزي..." type="info" />
      </Card>
    )
  }

  return (
    <div style={{ padding: 24 }}>
      <Title level={3}>🎨 عرض توضيحي للتحكم في الألوان</Title>
      
      <Alert
        message="النظام المركزي للألوان"
        description="هذا المثال يوضح كيف يعمل النظام المركزي الجديد. تغيير الألوان هنا سيؤثر على جميع نماذج الطباعة فوراً!"
        type="success"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Row gutter={[24, 24]}>
        {/* قسم التحكم في الألوان */}
        <Col xs={24} lg={12}>
          <Card title="🎨 التحكم في الألوان" size="small">
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              
              {/* اللون الأساسي */}
              <div>
                <Text strong>اللون الأساسي:</Text>
                <div style={{ display: 'flex', gap: 8, alignItems: 'center', marginTop: 8 }}>
                  <Input
                    type="color"
                    value={previewColors.primaryColor}
                    onChange={(e) => setPreviewColors(prev => ({ ...prev, primaryColor: e.target.value }))}
                    style={{ width: 60, height: 32 }}
                  />
                  <Input
                    value={previewColors.primaryColor}
                    onChange={(e) => setPreviewColors(prev => ({ ...prev, primaryColor: e.target.value }))}
                    placeholder="#1890ff"
                  />
                </div>
              </div>

              {/* اللون الثانوي */}
              <div>
                <Text strong>اللون الثانوي:</Text>
                <div style={{ display: 'flex', gap: 8, alignItems: 'center', marginTop: 8 }}>
                  <Input
                    type="color"
                    value={previewColors.secondaryColor}
                    onChange={(e) => setPreviewColors(prev => ({ ...prev, secondaryColor: e.target.value }))}
                    style={{ width: 60, height: 32 }}
                  />
                  <Input
                    value={previewColors.secondaryColor}
                    onChange={(e) => setPreviewColors(prev => ({ ...prev, secondaryColor: e.target.value }))}
                    placeholder="#fff3cd"
                  />
                </div>
              </div>

              {/* لون الحدود */}
              <div>
                <Text strong>لون الحدود:</Text>
                <div style={{ display: 'flex', gap: 8, alignItems: 'center', marginTop: 8 }}>
                  <Input
                    type="color"
                    value={previewColors.borderColor}
                    onChange={(e) => setPreviewColors(prev => ({ ...prev, borderColor: e.target.value }))}
                    style={{ width: 60, height: 32 }}
                  />
                  <Input
                    value={previewColors.borderColor}
                    onChange={(e) => setPreviewColors(prev => ({ ...prev, borderColor: e.target.value }))}
                    placeholder="#d9d9d9"
                  />
                </div>
              </div>

              <Divider />

              {/* أزرار التحكم */}
              <Space>
                <Button 
                  type="primary" 
                  icon={<BgColorsOutlined />}
                  onClick={applyColors}
                >
                  تطبيق الألوان
                </Button>
                <Button 
                  onClick={resetColors}
                >
                  إعادة تعيين
                </Button>
              </Space>
            </Space>
          </Card>
        </Col>

        {/* قسم المعاينة */}
        <Col xs={24} lg={12}>
          <Card title="👁️ معاينة الألوان الحالية" size="small">
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <ColorPreview color={colors.primaryColor} label="أساسي" />
              </Col>
              <Col span={8}>
                <ColorPreview color={colors.secondaryColor} label="ثانوي" />
              </Col>
              <Col span={8}>
                <ColorPreview color={colors.borderColor} label="حدود" />
              </Col>
              <Col span={8}>
                <ColorPreview color={colors.backgroundColor} label="خلفية" />
              </Col>
              <Col span={8}>
                <ColorPreview color={colors.textColor} label="نص" />
              </Col>
            </Row>

            <Divider />

            {/* معاينة تطبيق الألوان */}
            <div
              style={{
                padding: 16,
                border: `2px solid ${colors.borderColor}`,
                backgroundColor: colors.backgroundColor,
                color: colors.textColor,
                borderRadius: 8
              }}
            >
              <div
                style={{
                  padding: 8,
                  backgroundColor: colors.primaryColor,
                  color: '#fff',
                  borderRadius: 4,
                  marginBottom: 8,
                  textAlign: 'center'
                }}
              >
                عنوان بالألوان الحالية
              </div>
              <div
                style={{
                  padding: 8,
                  backgroundColor: colors.secondaryColor,
                  color: colors.textColor,
                  borderRadius: 4,
                  textAlign: 'center'
                }}
              >
                محتوى بالألوان الحالية
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* قسم اختبار الطباعة */}
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="🖨️ اختبار الطباعة بالألوان الجديدة" size="small">
            <Alert
              message="اختبار مباشر"
              description="استخدم الزر أدناه لطباعة فاتورة تجريبية بالألوان الحالية من النظام المركزي"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <UnifiedPrintButton
              data={samplePrintData}
              buttonText="طباعة فاتورة تجريبية"
              size="large"
              type="invoice"
              buttonType="primary"
              icon={<PrinterOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* معلومات تقنية */}
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="ℹ️ معلومات تقنية" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text>
                <strong>حالة النظام:</strong> {isReady ? '✅ جاهز' : '⏳ جاري التحميل'}
              </Text>
              <Text>
                <strong>الألوان المطبقة حالياً:</strong>
              </Text>
              <div style={{ fontFamily: 'monospace', fontSize: 12, background: '#f5f5f5', padding: 8, borderRadius: 4 }}>
                primaryColor: {colors.primaryColor}<br/>
                secondaryColor: {colors.secondaryColor}<br/>
                borderColor: {colors.borderColor}<br/>
                backgroundColor: {colors.backgroundColor}<br/>
                textColor: {colors.textColor}
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default ColorControlDemo
