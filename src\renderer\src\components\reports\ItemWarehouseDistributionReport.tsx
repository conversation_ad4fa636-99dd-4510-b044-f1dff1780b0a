/**
 * تقرير توزيع الأصناف على المخازن
 * يعرض تفصيلي لتوزيع الأصناف والكميات في جميع المخازن
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'
import { SafeLogger as Logger } from '../../utils/logger'

interface ItemWarehouseDistributionReportProps {
  onBack?: () => void
}

interface ItemWarehouseData {
  item_id: number
  item_code: string
  item_name: string
  category_name: string
  unit: string
  warehouse_id: number
  warehouse_name: string
  quantity: number
  reserved_quantity: number
  available_quantity: number
  cost_price: number
  sale_price: number
  inventory_value: number
  min_quantity: number
  max_quantity: number
  last_movement_date: string
  status: 'normal' | 'low' | 'high' | 'out'
}

const ItemWarehouseDistributionReport: React.FC<ItemWarehouseDistributionReportProps> = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any) => {
    try {
      Logger.info('ItemWarehouseDistributionReport', 'بدء إنشاء تقرير توزيع الأصناف:', filters)

      // استدعاء API للحصول على بيانات توزيع الأصناف
      const response = await window.electronAPI?.invoke('get-item-warehouse-distribution', {
        warehouseId: filters.warehouseId,
        categoryId: filters.categoryId,
        searchText: filters.searchText
      })

      if (!response?.success) {
        throw new Error(response?.message || 'فشل في تحميل بيانات التقرير')
      }

      const data = response.data || []

      // حساب الإحصائيات
      const totalItems = data.length
      const totalValue = data.reduce((sum: number, item: ItemWarehouseData) => sum + item.inventory_value, 0)
      const lowStockItems = data.filter((item: ItemWarehouseData) => item.status === 'low').length
      const outOfStockItems = data.filter((item: ItemWarehouseData) => item.status === 'out').length
      const normalStockItems = data.filter((item: ItemWarehouseData) => item.status === 'normal').length
      const highStockItems = data.filter((item: ItemWarehouseData) => item.status === 'high').length

      const totalQuantity = data.reduce((sum: number, item: ItemWarehouseData) => sum + item.quantity, 0)
      const totalReserved = data.reduce((sum: number, item: ItemWarehouseData) => sum + item.reserved_quantity, 0)
      const totalAvailable = data.reduce((sum: number, item: ItemWarehouseData) => sum + item.available_quantity, 0)

      // تحديد الأعمدة
      const columns = [
        {
          key: 'item_code',
          title: 'كود الصنف',
          align: 'center' as const,
          format: 'text' as const,
          width: '120px'
        },
        {
          key: 'item_name',
          title: 'اسم الصنف',
          align: 'right' as const,
          format: 'text' as const,
          width: '200px'
        },
        {
          key: 'category_name',
          title: 'الفئة',
          align: 'center' as const,
          format: 'text' as const,
          width: '120px'
        },
        {
          key: 'warehouse_name',
          title: 'المخزن',
          align: 'center' as const,
          format: 'text' as const,
          width: '120px'
        },
        {
          key: 'quantity',
          title: 'الكمية الإجمالية',
          align: 'center' as const,
          format: 'number' as const,
          width: '120px'
        },
        {
          key: 'reserved_quantity',
          title: 'الكمية المحجوزة',
          align: 'center' as const,
          format: 'number' as const,
          width: '120px'
        },
        {
          key: 'available_quantity',
          title: 'الكمية المتاحة',
          align: 'center' as const,
          format: 'number' as const,
          width: '120px'
        },
        {
          key: 'unit',
          title: 'الوحدة',
          align: 'center' as const,
          format: 'text' as const,
          width: '80px'
        },
        {
          key: 'cost_price',
          title: 'سعر التكلفة',
          align: 'center' as const,
          format: 'currency' as const,
          width: '120px'
        },
        {
          key: 'sale_price',
          title: 'سعر البيع',
          align: 'center' as const,
          format: 'currency' as const,
          width: '120px'
        },
        {
          key: 'inventory_value',
          title: 'قيمة المخزون',
          align: 'center' as const,
          format: 'currency' as const,
          width: '140px'
        },
        {
          key: 'status',
          title: 'الحالة',
          align: 'center' as const,
          format: 'text' as const,
          width: '100px'
        }
      ]

      return {
        title: 'تقرير توزيع الأصناف على المخازن',
        data,
        columns,
        summary: {
          totalItems,
          totalValue,
          totalQuantity,
          totalReserved,
          totalAvailable,
          lowStockItems,
          outOfStockItems,
          normalStockItems,
          highStockItems,
          averageValue: totalItems > 0 ? Number((totalValue / totalItems).toFixed(2)) : 0
        },
        metadata: {
          reportType: 'item-warehouse-distribution' as ReportType,
          totalRecords: data.length,
          warehouse: filters.warehouseId ? 'مخزن محدد' : 'جميع المخازن',
          category: filters.categoryId ? 'فئة محددة' : 'جميع الفئات',
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام توزيع المخزون'
        }
      }
    } catch (error) {
      Logger.error('ItemWarehouseDistributionReport', 'خطأ في إنشاء التقرير:', error)
      throw error
    }
  }

  return (
    <UniversalReport
      reportType={'item-warehouse-distribution' as ReportType}
      title="تقرير توزيع الأصناف على المخازن"
      description="عرض تفصيلي لتوزيع الأصناف والكميات في جميع المخازن مع حالة المخزون"
      onGenerateReport={generateReport}
      showDateRange={false}
      showWarehouseFilter={true}
      showCategoryFilter={true}
      showItemFilter={false}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="item_warehouse_distribution_report"
      defaultFilters={{
        sortBy: 'item_name',
        sortOrder: 'asc'
      }}
    />
  )
}

export default ItemWarehouseDistributionReport
