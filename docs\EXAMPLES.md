# 🎯 **أمثلة عملية - النظام المتكامل لتعديل القوالب والأعمدة**

---

## 📋 **نظرة عامة**

هذا المستند يحتوي على أمثلة عملية وحالات استخدام حقيقية للنظام المتكامل لتعديل القوالب والأعمدة.

---

## 🏢 **حالات الاستخدام الشائعة**

### **1. شركة تجارية - تخصيص تقارير المبيعات**

#### **المتطلبات:**
- تقرير مبيعات شهري بألوان الشركة
- إظهار شعار الشركة في الرأس
- تنسيق خاص للأرقام (عملة سعودية)
- إخفاء بعض الأعمدة الحساسة

#### **التطبيق:**

```typescript
// إنشاء قالب مخصص للمبيعات
const salesTemplate = createTemplate({
  metadata: {
    name: 'قالب المبيعات الشهري',
    description: 'تقرير مبيعات مخصص للشركة التجارية',
    type: 'report',
    category: 'sales',
    isDefault: true
  },
  inheritance: {
    inheritsFromGlobal: false,
    customSettings: {
      // ألوان الشركة
      colors: {
        primary: '#2E8B57',      // أخضر الشركة
        secondary: '#F5F5DC',    // بيج فاتح
        headers: '#2E8B57',      // أخضر للعناوين
        background: '#FFFFFF',   // خلفية بيضاء
        text: '#2F4F4F',        // رمادي داكن للنص
        borders: '#2E8B57'       // حدود خضراء
      },
      // إعدادات الصفحة
      page: {
        size: 'A4',
        orientation: 'landscape',
        margins: { top: 20, bottom: 20, left: 15, right: 15 }
      },
      // الخطوط
      fonts: {
        primary: { family: 'Arial', size: 12, weight: 'normal' },
        headers: { family: 'Arial', size: 16, weight: 'bold' },
        tables: { family: 'Arial', size: 11, weight: 'normal' }
      },
      // التخطيط
      layout: {
        logoPosition: 'top-center',
        logoSize: 'medium',
        showBorders: true,
        borderWidth: 1
      },
      // المحتوى
      content: {
        headerText: 'شركة التجارة المتقدمة - تقرير المبيعات الشهري',
        footerText: 'تم إنشاء التقرير في: {date} | الصفحة {page} من {total}',
        watermark: true,
        watermarkText: 'سري',
        watermarkOpacity: 0.1
      }
    }
  },
  // تخصيص الأعمدة
  columns: [
    {
      key: 'date',
      title: 'التاريخ',
      dataIndex: 'date',
      display: { visible: true, width: 100, align: 'center' },
      print: { visible: true, width: '12%', align: 'center' },
      format: { type: 'date', dateFormat: 'DD/MM/YYYY' }
    },
    {
      key: 'customer',
      title: 'العميل',
      dataIndex: 'customerName',
      display: { visible: true, width: 200, align: 'right' },
      print: { visible: true, width: '25%', align: 'right' }
    },
    {
      key: 'product',
      title: 'المنتج',
      dataIndex: 'productName',
      display: { visible: true, width: 180, align: 'right' },
      print: { visible: true, width: '20%', align: 'right' }
    },
    {
      key: 'quantity',
      title: 'الكمية',
      dataIndex: 'quantity',
      display: { visible: true, width: 80, align: 'center' },
      print: { visible: true, width: '10%', align: 'center' },
      format: { type: 'number', precision: 0 }
    },
    {
      key: 'unitPrice',
      title: 'سعر الوحدة',
      dataIndex: 'unitPrice',
      display: { visible: true, width: 100, align: 'right' },
      print: { visible: true, width: '13%', align: 'right' },
      format: { type: 'currency', precision: 2, suffix: ' ريال' }
    },
    {
      key: 'total',
      title: 'الإجمالي',
      dataIndex: 'total',
      display: { visible: true, width: 120, align: 'right' },
      print: { 
        visible: true, 
        width: '15%', 
        align: 'right',
        fontWeight: 'bold',
        color: '#2E8B57'
      },
      format: { type: 'currency', precision: 2, suffix: ' ريال' }
    },
    {
      key: 'profit',
      title: 'الربح',
      dataIndex: 'profit',
      display: { visible: false, width: 100, align: 'right' }, // مخفي في العرض
      print: { visible: false }, // مخفي في الطباعة أيضاً
      format: { type: 'currency', precision: 2, suffix: ' ريال' }
    }
  ]
})

// حفظ القالب
await saveTemplate(salesTemplate)
```

---

### **2. مصنع - تقرير الإنتاج اليومي**

#### **المتطلبات:**
- تقرير إنتاج يومي مضغوط
- ألوان صناعية (أزرق وأسود)
- تركيز على الكفاءة والأرقام
- طباعة على ورق A5

#### **التطبيق:**

```typescript
const productionTemplate = createTemplate({
  metadata: {
    name: 'تقرير الإنتاج اليومي',
    description: 'تقرير مضغوط للإنتاج اليومي',
    type: 'report',
    category: 'production'
  },
  inheritance: {
    inheritsFromGlobal: false,
    customSettings: {
      colors: {
        primary: '#1E3A8A',      // أزرق داكن
        secondary: '#3B82F6',    // أزرق فاتح
        headers: '#1E3A8A',      // أزرق داكن للعناوين
        background: '#F8FAFC',   // خلفية رمادية فاتحة
        text: '#1F2937',        // رمادي داكن
        borders: '#6B7280'       // رمادي متوسط
      },
      page: {
        size: 'A5',
        orientation: 'portrait',
        margins: { top: 15, bottom: 15, left: 10, right: 10 }
      },
      fonts: {
        primary: { family: 'Arial', size: 10, weight: 'normal' },
        headers: { family: 'Arial', size: 14, weight: 'bold' },
        tables: { family: 'Arial', size: 9, weight: 'normal' }
      },
      layout: {
        logoPosition: 'top-left',
        logoSize: 'small',
        showBorders: true,
        borderWidth: 1,
        spacing: 5
      },
      content: {
        headerText: 'مصنع الإنتاج المتقدم - تقرير يومي',
        footerText: 'تاريخ الطباعة: {date}'
      }
    }
  },
  columns: [
    {
      key: 'shift',
      title: 'الوردية',
      dataIndex: 'shift',
      display: { visible: true, width: 80, align: 'center' },
      print: { visible: true, width: '15%', align: 'center' }
    },
    {
      key: 'line',
      title: 'الخط',
      dataIndex: 'productionLine',
      display: { visible: true, width: 100, align: 'center' },
      print: { visible: true, width: '20%', align: 'center' }
    },
    {
      key: 'product',
      title: 'المنتج',
      dataIndex: 'productCode',
      display: { visible: true, width: 120, align: 'center' },
      print: { visible: true, width: '25%', align: 'center' }
    },
    {
      key: 'target',
      title: 'المستهدف',
      dataIndex: 'targetQuantity',
      display: { visible: true, width: 80, align: 'center' },
      print: { visible: true, width: '15%', align: 'center' },
      format: { type: 'number', precision: 0 }
    },
    {
      key: 'actual',
      title: 'الفعلي',
      dataIndex: 'actualQuantity',
      display: { visible: true, width: 80, align: 'center' },
      print: { 
        visible: true, 
        width: '15%', 
        align: 'center',
        fontWeight: 'bold'
      },
      format: { type: 'number', precision: 0 }
    },
    {
      key: 'efficiency',
      title: 'الكفاءة %',
      dataIndex: 'efficiency',
      display: { visible: true, width: 80, align: 'center' },
      print: { 
        visible: true, 
        width: '10%', 
        align: 'center',
        fontWeight: 'bold',
        color: '#1E3A8A'
      },
      format: { type: 'percentage', precision: 1 }
    }
  ]
})
```

---

### **3. مستشفى - تقرير المرضى**

#### **المتطلبات:**
- تقرير طبي بألوان هادئة
- خصوصية عالية (إخفاء معلومات حساسة)
- تنسيق طبي احترافي
- طباعة عالية الجودة

#### **التطبيق:**

```typescript
const medicalTemplate = createTemplate({
  metadata: {
    name: 'تقرير المرضى اليومي',
    description: 'تقرير طبي للمرضى مع حماية الخصوصية',
    type: 'report',
    category: 'medical'
  },
  inheritance: {
    inheritsFromGlobal: false,
    customSettings: {
      colors: {
        primary: '#059669',      // أخضر طبي
        secondary: '#D1FAE5',    // أخضر فاتح
        headers: '#047857',      // أخضر داكن
        background: '#FFFFFF',   // أبيض نظيف
        text: '#374151',        // رمادي داكن
        borders: '#9CA3AF'       // رمادي فاتح
      },
      page: {
        size: 'A4',
        orientation: 'portrait',
        margins: { top: 25, bottom: 25, left: 20, right: 20 }
      },
      fonts: {
        primary: { family: 'Arial', size: 11, weight: 'normal' },
        headers: { family: 'Arial', size: 16, weight: 'bold' },
        tables: { family: 'Arial', size: 10, weight: 'normal' }
      },
      layout: {
        logoPosition: 'top-center',
        logoSize: 'large',
        showBorders: true,
        borderWidth: 1,
        spacing: 8
      },
      content: {
        headerText: 'مستشفى الرعاية المتقدمة - تقرير المرضى',
        footerText: 'سري ومحمي طبياً | {date}',
        watermark: true,
        watermarkText: 'سري',
        watermarkOpacity: 0.05
      },
      quality: {
        quality: 'high',
        copies: 1
      }
    }
  },
  columns: [
    {
      key: 'patientId',
      title: 'رقم المريض',
      dataIndex: 'patientId',
      display: { visible: true, width: 100, align: 'center' },
      print: { visible: true, width: '15%', align: 'center' }
    },
    {
      key: 'name',
      title: 'الاسم',
      dataIndex: 'patientName',
      display: { visible: true, width: 150, align: 'right' },
      print: { visible: false }, // مخفي للخصوصية
      advanced: {
        conditional: true,
        tooltip: 'مخفي في الطباعة للخصوصية'
      }
    },
    {
      key: 'age',
      title: 'العمر',
      dataIndex: 'age',
      display: { visible: true, width: 60, align: 'center' },
      print: { visible: true, width: '8%', align: 'center' }
    },
    {
      key: 'department',
      title: 'القسم',
      dataIndex: 'department',
      display: { visible: true, width: 120, align: 'right' },
      print: { visible: true, width: '20%', align: 'right' }
    },
    {
      key: 'doctor',
      title: 'الطبيب المعالج',
      dataIndex: 'doctorName',
      display: { visible: true, width: 150, align: 'right' },
      print: { visible: true, width: '25%', align: 'right' }
    },
    {
      key: 'status',
      title: 'الحالة',
      dataIndex: 'status',
      display: { visible: true, width: 100, align: 'center' },
      print: { 
        visible: true, 
        width: '15%', 
        align: 'center',
        fontWeight: 'bold'
      }
    },
    {
      key: 'admissionDate',
      title: 'تاريخ الدخول',
      dataIndex: 'admissionDate',
      display: { visible: true, width: 100, align: 'center' },
      print: { visible: true, width: '17%', align: 'center' },
      format: { type: 'date', dateFormat: 'DD/MM/YYYY' }
    }
  ]
})
```

---

## 🔧 **تخصيصات متقدمة**

### **1. قالب مع شروط ديناميكية**

```typescript
// قالب يغير الألوان حسب حالة الطلب
const conditionalTemplate = createTemplate({
  // ... إعدادات أساسية
  columns: [
    {
      key: 'status',
      title: 'الحالة',
      dataIndex: 'orderStatus',
      advanced: {
        conditional: true,
        conditions: [
          {
            condition: 'value === "مكتمل"',
            style: { color: '#52c41a', fontWeight: 'bold' }
          },
          {
            condition: 'value === "معلق"',
            style: { color: '#faad14', fontWeight: 'bold' }
          },
          {
            condition: 'value === "ملغي"',
            style: { color: '#ff4d4f', fontWeight: 'bold' }
          }
        ]
      }
    }
  ]
})
```

### **2. قالب مع تنسيق مخصص**

```typescript
// قالب مع تنسيق خاص للعملات والنسب
const customFormatTemplate = createTemplate({
  // ... إعدادات أساسية
  columns: [
    {
      key: 'amount',
      title: 'المبلغ',
      dataIndex: 'amount',
      format: {
        type: 'currency',
        precision: 2,
        prefix: 'ر.س ',
        locale: 'ar-SA'
      }
    },
    {
      key: 'discount',
      title: 'الخصم',
      dataIndex: 'discountRate',
      format: {
        type: 'percentage',
        precision: 1,
        suffix: '%'
      }
    }
  ]
})
```

---

## 🎯 **نصائح للاستخدام الأمثل**

### **1. تحسين الأداء**
```typescript
// استخدم Virtual Scrolling للبيانات الكبيرة
<UniversalReport
  data={largeDataset}
  virtual={largeDataset.length > 1000}
  template={selectedTemplate}
/>
```

### **2. التحقق من صحة القوالب**
```typescript
const { validateTemplate } = useUnifiedSettings()

const validation = validateTemplate(myTemplate)
if (!validation.isValid) {
  console.error('أخطاء في القالب:', validation.errors)
}
```

### **3. نسخ احتياطية للقوالب**
```typescript
// تصدير القالب
const exportedTemplate = JSON.stringify(myTemplate, null, 2)

// استيراد القالب
const importedTemplate = JSON.parse(exportedData)
await saveTemplate(importedTemplate)
```

---

**هذه الأمثلة تغطي معظم حالات الاستخدام الشائعة. يمكنك تخصيصها حسب احتياجاتك المحددة!** 🚀
