import React, { useState } from 'react'
import { Card, Row, Col, Typo<PERSON>, Button } from 'antd'
import {
  ArrowLeftOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  InboxOutlined,
  DollarOutlined,
  CalendarOutlined,
  BarChartOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import {
  ProductionOrdersReport,
  ProductionEfficiencyReport,
  MaterialConsumptionReport,
  ProductionCostsReport,
  ProductionScheduleReport
} from '../../reports'
import ProductionInventoryReport from '../reports/ProductionInventoryReport'

const { Title, Text } = Typography

const ReportCard = styled(Card)`
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  }
  
  .ant-card-body {
    padding: 24px;
  }
  
  .report-icon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
  }
  
  .report-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #1890ff;
  }
  
  .report-description {
    color: #666;
    line-height: 1.6;
    font-size: 14px;
  }
`

const ProductionOrdersReportCard = styled(ReportCard)`
  .report-icon { color: #1890ff; }
  &:hover { border-color: #1890ff; }
`

const EfficiencyReportCard = styled(ReportCard)`
  .report-icon { color: #52c41a; }
  &:hover { border-color: #52c41a; }
`

const MaterialConsumptionReportCard = styled(ReportCard)`
  .report-icon { color: #fa8c16; }
  &:hover { border-color: #fa8c16; }
`

const CostReportCard = styled(ReportCard)`
  .report-icon { color: #f5222d; }
  &:hover { border-color: #f5222d; }
`

const ScheduleReportCard = styled(ReportCard)`
  .report-icon { color: #722ed1; }
  &:hover { border-color: #722ed1; }
`

interface ProductionReportsProps {
  onBack?: () => void
}

type ReportType = 'production-orders' | 'production-efficiency' | 'material-consumption' | 'production-costs' | 'production-schedule' | 'production-inventory' | null

const ProductionReports: React.FC<ProductionReportsProps> = ({ onBack }) => {
  const [selectedReport, setSelectedReport] = useState<ReportType>(null)

  const handleReportClick = (reportType: ReportType) => {
    setSelectedReport(reportType)
  }

  const renderReport = () => {
    switch (selectedReport) {
      case 'production-orders':
        return <ProductionOrdersReport />
      case 'production-efficiency':
        return <ProductionEfficiencyReport />
      case 'material-consumption':
        return <MaterialConsumptionReport />
      case 'production-costs':
        return <ProductionCostsReport />
      case 'production-schedule':
        return <ProductionScheduleReport />
      case 'production-inventory':
        return <ProductionInventoryReport onBack={() => setSelectedReport(null)} />
      default:
        return null
    }
  }

  const reports = [
    {
      id: 'production-orders',
      title: 'تقرير أوامر الإنتاج',
      description: 'تقرير شامل لجميع أوامر الإنتاج مع تفاصيل الحالة والتقدم والتكاليف',
      icon: <FileTextOutlined className="report-icon" />,
      component: ProductionOrdersReportCard,
      features: [
        'عرض جميع أوامر الإنتاج مع الحالات',
        'تفاصيل التكاليف المقدرة والفعلية',
        'تواريخ البدء والإنجاز المتوقعة',
        'معلومات العملاء والأصناف',
        'إمكانية التصدير والطباعة'
      ]
    },
    {
      id: 'production-efficiency',
      title: 'تقرير كفاءة الإنتاج',
      description: 'تحليل كفاءة الإنتاج ومقارنة الأوقات المقدرة مع الفعلية',
      icon: <ClockCircleOutlined className="report-icon" />,
      component: EfficiencyReportCard,
      features: [
        'مقارنة الأوقات المقدرة مع الفعلية',
        'نسب الكفاءة لكل قسم ومرحلة',
        'تحليل الانحرافات الزمنية',
        'مؤشرات الأداء الرئيسية',
        'اتجاهات التحسن عبر الزمن'
      ]
    },
    {
      id: 'material-consumption',
      title: 'تقرير استهلاك المواد',
      description: 'تقرير تفصيلي لاستهلاك المواد الخام في عمليات الإنتاج',
      icon: <InboxOutlined className="report-icon" />,
      component: MaterialConsumptionReportCard,
      features: [
        'استهلاك المواد لكل أمر إنتاج',
        'مقارنة الاستهلاك المقدر مع الفعلي',
        'تكاليف المواد المستهلكة',
        'تحليل الهدر والفاقد',
        'توقعات احتياجات المواد'
      ]
    },
    {
      id: 'production-costs',
      title: 'تقرير تكاليف الإنتاج',
      description: 'تحليل شامل لتكاليف الإنتاج (مواد، عمالة، مصاريف عامة)',
      icon: <DollarOutlined className="report-icon" />,
      component: CostReportCard,
      features: [
        'تفصيل تكاليف المواد والعمالة',
        'المصاريف العامة والإضافية',
        'مقارنة التكاليف المقدرة مع الفعلية',
        'تحليل الربحية لكل أمر',
        'اتجاهات التكاليف عبر الزمن'
      ]
    },
    {
      id: 'production-schedule',
      title: 'تقرير جدولة الإنتاج',
      description: 'تقرير المواعيد المتوقعة والفعلية لإنجاز أوامر الإنتاج',
      icon: <CalendarOutlined className="report-icon" />,
      component: ScheduleReportCard,
      features: [
        'جدولة الأوامر والمواعيد المتوقعة',
        'تتبع التأخيرات والانحرافات',
        'تحليل أسباب التأخير',
        'توقعات إنجاز الأوامر الجارية',
        'تحسين جدولة الإنتاج'
      ]
    },
    {
      id: 'production-inventory',
      title: 'تقرير الإنتاج والمخزون الشامل',
      description: 'تقرير شامل يربط بين أوامر الإنتاج وحالة المخزون واستهلاك المواد',
      icon: <BarChartOutlined className="report-icon" />,
      component: ReportCard,
      features: [
        'ربط أوامر الإنتاج بحالة المخزون',
        'تتبع استهلاك المواد الخام',
        'تنبيهات المخزون المنخفض',
        'تحليل التكاليف الشامل',
        'إحصائيات الإنتاج والمخزون',
        'تصدير تقارير Excel شاملة'
      ]
    }
  ]

  if (selectedReport) {
    return (
      <div>
        <div style={{ marginBottom: '16px', display: 'flex', gap: '8px' }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => setSelectedReport(null)}
          >
            العودة للتقارير
          </Button>
          {onBack && (
            <Button
              onClick={onBack}
            >
              العودة للإنتاج
            </Button>
          )}
        </div>
        {renderReport()}
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* شريط العنوان والعودة */}
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          {onBack && (
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={onBack}
              style={{ marginLeft: '16px' }}
            >
              العودة
            </Button>
          )}
          <Title level={2} style={{ margin: 0, display: 'inline' }}>
            تقارير الإنتاج
          </Title>
        </div>
      </div>

      {/* وصف القسم */}
      <Card style={{ marginBottom: '24px', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}>
        <div style={{ color: 'white', textAlign: 'center' }}>
          <BarChartOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
          <Title level={3} style={{ color: 'white', marginBottom: '8px' }}>
            تقارير الإنتاج الشاملة
          </Title>
          <Text style={{ color: 'rgba(255,255,255,0.9)', fontSize: '16px' }}>
            مجموعة شاملة من التقارير لتحليل ومراقبة عمليات الإنتاج والأداء
          </Text>
        </div>
      </Card>

      {/* بطاقات التقارير */}
      <Row gutter={[24, 24]}>
        {reports.map((report) => {
          const ReportComponent = report.component
          return (
            <Col xs={24} sm={12} lg={8} key={report.id}>
              <ReportComponent onClick={() => handleReportClick(report.id as any)}>
                <div style={{ textAlign: 'center' }}>
                  {report.icon}
                  <div className="report-title">{report.title}</div>
                  <div className="report-description">
                    {report.description}
                  </div>
                  
                  <div style={{ marginTop: '16px', textAlign: 'right' }}>
                    <Text strong style={{ color: '#1890ff', fontSize: '14px' }}>المميزات:</Text>
                    <ul style={{ marginTop: '8px', paddingRight: '16px', textAlign: 'right' }}>
                      {report.features.slice(0, 3).map((feature, index) => (
                        <li key={index} style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </ReportComponent>
            </Col>
          )
        })}
      </Row>

      {/* معلومات إضافية */}
      <Card style={{ marginTop: '24px' }}>
        <Row gutter={16}>
          <Col span={12}>
            <Title level={4}>📊 ميزات التقارير</Title>
            <ul>
              <li>تقارير تفاعلية مع إمكانية التصفية</li>
              <li>تصدير البيانات بصيغ مختلفة (PDF, Excel)</li>
              <li>رسوم بيانية وإحصائيات مرئية</li>
              <li>تحديث البيانات في الوقت الفعلي</li>
              <li>إمكانية الطباعة المباشرة</li>
            </ul>
          </Col>
          <Col span={12}>
            <Title level={4}>🎯 فوائد التقارير</Title>
            <ul>
              <li>مراقبة الأداء وتحسين الكفاءة</li>
              <li>تحليل التكاليف وتحسين الربحية</li>
              <li>تخطيط أفضل للموارد والمواد</li>
              <li>اتخاذ قرارات مدروسة ومبنية على البيانات</li>
              <li>تحديد نقاط القوة والضعف في الإنتاج</li>
            </ul>
          </Col>
        </Row>
      </Card>

      {/* ملاحّة حول حالة التطوير */}
      <Card style={{ marginTop: '16px', background: '#f6ffed', border: '1px solid #b7eb8f' }}>
        <div style={{ textAlign: 'center' }}>
          <Title level={4} style={{ color: '#52c41a', marginBottom: '8px' }}>
            ✅ حالة التطوير
          </Title>
          <Text style={{ color: '#389e0d' }}>
            جميع التقارير متوفرة في النّام الخلفي مع APIs كاملة. 
            يتم حالياً تطوير واجهات العرض التفاعلية لكل تقرير.
          </Text>
        </div>
      </Card>
    </div>
  )
}

export default ProductionReports
