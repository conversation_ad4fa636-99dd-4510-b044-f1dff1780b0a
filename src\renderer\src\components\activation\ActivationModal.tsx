import React, { useState, useEffect } from 'react'
import { Input, Button, Typography, Space, Alert, Di<PERSON>r, Card } from 'antd'
import { KeyOutlined, SafetyOutlined, PhoneOutlined, MailOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import { SafeLogger as Logger } from '../../utils/logger'

const { Title, Text, Paragraph } = Typography

interface ActivationModalProps {
  visible: boolean
  onActivationSuccess: () => void
  licenseInfo?: any
}

interface ActivationAttempt {
  count: number
  lastAttempt: Date
}

const ActivationOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #0078D4 0%, #106EBE 100%);
  z-index: 999999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.6s ease-out;
  direction: rtl;

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
`

const ActivationContainer = styled(Card)`
  width: 450px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 1;
  animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);

  .ant-card-body {
    padding: 48px;
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(60px) scale(0.9);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
`

const LogoContainer = styled.div`
  text-align: center;
  margin-bottom: 40px;

  .company-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 16px;
    background: linear-gradient(135deg, #0078D4 0%, #106EBE 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
    box-shadow: 0 8px 24px rgba(0, 120, 212, 0.3);
    font-weight: bold;
  }
  }

  @keyframes gradientShift {
    0%, 100% {
      background: linear-gradient(90deg,
        #667eea 0%, #764ba2 15%, #f093fb 30%, #f5576c 45%,
        #4facfe 60%, #00f2fe 75%, #43e97b 90%, #38f9d7 100%
      )
    }
    50% {
      background: linear-gradient(90deg,
        #38f9d7 0%, #43e97b 15%, #00f2fe 30%, #4facfe 45%,
        #f5576c 60%, #f093fb 75%, #764ba2 90%, #667eea 100%
      )
    }
  }

  &::after {
    content: ''
    position: absolute
    top: -4px
    left: -4px
    right: -4px
    bottom: -4px
    background: linear-gradient(145deg,
      rgba(102, 126, 234, 0.1) 0%,
      rgba(118, 75, 162, 0.1) 25%,
      rgba(240, 147, 251, 0.1) 50%,
      rgba(245, 87, 108, 0.1) 75%,
      rgba(79, 172, 254, 0.1) 100%
    )
    border-radius: 36px
    z-index: -1
    filter: blur(20px)
    opacity: 0.7
  }
`

const LogoSection = styled.div`
  margin-bottom: 60px
  position: relative

  .app-logo {
    font-size: 84px
    font-weight: 900
    background: linear-gradient(135deg,
      #667eea 0%,
      #764ba2 25%,
      #f093fb 50%,
      #f5576c 75%,
      #4facfe 100%
    )
    -webkit-background-clip: text
    -webkit-text-fill-color: transparent
    background-clip: text
    margin-bottom: 25px
    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.15)
    letter-spacing: 6px
    position: relative
    display: inline-block
    animation: logoGlow 3s ease-in-out infinite alternate

    @keyframes logoGlow {
      from {
        filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.3))
      }
      to {
        filter: drop-shadow(0 0 40px rgba(102, 126, 234, 0.6))
      }
    }

    &::after {
      content: ''
      position: absolute
      bottom: -12px
      left: 50%
      transform: translateX(-50%)
      width: 120px
      height: 6px
      background: linear-gradient(90deg,
        #667eea 0%,
        #764ba2 25%,
        #f093fb 50%,
        #f5576c 75%,
        #4facfe 100%
      )
      border-radius: 3px
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4)
      animation: underlineShimmer 2s ease-in-out infinite
    }

    @keyframes underlineShimmer {
      0%, 100% { opacity: 0.7; transform: translateX(-50%) scaleX(1) }
      50% { opacity: 1; transform: translateX(-50%) scaleX(1.1) }
    }
  }

  .app-subtitle {
    color: #334155
    font-size: 26px
    font-weight: 700
    margin-bottom: 18px
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.08)
    background: linear-gradient(135deg, #334155 0%, #475569 100%)
    -webkit-background-clip: text
    -webkit-text-fill-color: transparent
    background-clip: text
  }

  .app-description {
    color: #64748b
    font-size: 18px
    font-style: italic
    font-weight: 500
    opacity: 0.95
    line-height: 1.6
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05)
  }
`

const ActivationForm = styled.div`
  margin: 50px 0
  width: 100%
  max-width: 500px

  .activation-input {
    margin: 30px 0
    text-align: center
    position: relative

    .ant-input-affix-wrapper {
      border-radius: 16px
      border: 3px solid #e8e8e8
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1)
      background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)
      backdrop-filter: blur(10px)
      height: 70px
      box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9)

      &:hover {
        border-color: #1890ff
        box-shadow:
          0 12px 40px rgba(24, 144, 255, 0.15),
          0 4px 16px rgba(24, 144, 255, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.9)
        transform: translateY(-2px)
      }

      &:focus-within {
        border-color: #1890ff
        box-shadow:
          0 16px 48px rgba(24, 144, 255, 0.25),
          0 8px 24px rgba(24, 144, 255, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.9)
        transform: translateY(-3px)
      }
    }

    input {
      text-align: center
      font-size: 22px
      font-weight: bold
      letter-spacing: 4px
      text-transform: uppercase
      background: transparent
      border: none
      padding: 20px 25px
      color: #1890ff

      &::placeholder {
        color: #bbb
        font-weight: 500
        letter-spacing: 2px
        font-size: 18px
      }

      &:focus {
        box-shadow: none
        outline: none
      }
    }

    .ant-input-prefix {
      color: #1890ff
      font-size: 24px
      margin-left: 20px
    }

    .ant-input-clear-icon {
      color: #999
      font-size: 18px

      &:hover {
        color: #1890ff
      }
    }
  }

  .activation-button {
    margin-top: 30px

    .ant-btn {
      border-radius: 16px
      height: 65px
      font-size: 20px
      font-weight: bold
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 50%, #0050b3 100%)
      border: none
      box-shadow:
        0 12px 32px rgba(24, 144, 255, 0.3),
        0 6px 16px rgba(24, 144, 255, 0.2)
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1)
      position: relative
      overflow: hidden

      &::before {
        content: ''
        position: absolute
        top: 0
        left: -100%
        width: 100%
        height: 100%
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)
        transition: left 0.6s ease
      }

      &:hover {
        transform: translateY(-3px)
        box-shadow:
          0 16px 40px rgba(24, 144, 255, 0.4),
          0 8px 20px rgba(24, 144, 255, 0.25)
        background: linear-gradient(135deg, #096dd9 0%, #0050b3 50%, #003a8c 100%)

        &::before {
          left: 100%
        }
      }

      &:active {
        transform: translateY(-1px)
      }

      &:disabled {
        background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%)
        color: #bbb
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1)
        transform: none

        &::before {
          display: none
        }
      }
    }
  }
`

const ContactInfo = styled.div`
  margin-top: 45px
  padding: 35px 30px
  background: linear-gradient(135deg,
    rgba(240, 249, 255, 0.9) 0%,
    rgba(230, 247, 255, 0.8) 50%,
    rgba(219, 234, 254, 0.9) 100%
  )
  border-radius: 20px
  border: 2px solid rgba(59, 130, 246, 0.15)
  box-shadow:
    0 10px 25px rgba(59, 130, 246, 0.1),
    0 4px 10px rgba(59, 130, 246, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.9)
  backdrop-filter: blur(10px)

  .contact-title {
    font-size: 22px
    font-weight: 800
    color: #1e40af
    margin-bottom: 25px
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1)
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)
    -webkit-background-clip: text
    -webkit-text-fill-color: transparent
    background-clip: text
  }

  .contact-item {
    display: flex
    align-items: center
    justify-content: center
    gap: 15px
    margin: 15px 0
    font-size: 17px
    font-weight: 600
    padding: 15px 20px
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(248, 250, 252, 0.8) 100%
    )
    border-radius: 12px
    border: 1px solid rgba(59, 130, 246, 0.1)
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1)
    cursor: pointer

    &:hover {
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 1) 0%,
        rgba(240, 249, 255, 0.95) 100%
      )
      transform: translateY(-3px) scale(1.02)
      box-shadow:
        0 8px 20px rgba(59, 130, 246, 0.15),
        0 4px 10px rgba(59, 130, 246, 0.1)
      border-color: rgba(59, 130, 246, 0.2)
    }

    .anticon {
      font-size: 20px
      color: #3b82f6
    }
  }

  .developer-info {
    margin-top: 25px
    padding: 20px
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.7) 0%,
      rgba(248, 250, 252, 0.6) 100%
    )
    border-radius: 12px
    border: 1px solid rgba(59, 130, 246, 0.08)
    font-size: 16px
    color: #475569
    font-weight: 600
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05)
  }
`

const ErrorAlert = styled(Alert)`
  margin: 25px 0
  text-align: right
  border-radius: 12px
  border: none
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1)

  &.warning-level-1 {
    background: linear-gradient(135deg, #fff7e6 0%, #fef9f0 100%)
    border-left: 4px solid #faad14
  }

  &.warning-level-2 {
    background: linear-gradient(135deg, #fff2e8 0%, #fef6f0 100%)
    border-left: 4px solid #fa8c16
  }

  &.warning-level-3 {
    background: linear-gradient(135deg, #fff1f0 0%, #fef1f0 100%)
    border-left: 4px solid #f5222d
  }

  .ant-alert-icon {
    font-size: 18px
  }

  .ant-alert-message {
    font-weight: 600
    font-size: 16px
  }
`

const ActivationModal: React.FC<ActivationModalProps> = ({
  visible,
  onActivationSuccess,
  licenseInfo
}) => {
  const [activationCode, setActivationCode] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [attempts, setAttempts] = useState<ActivationAttempt>({ count: 0, lastAttempt: new Date() })
  const [showCode, setShowCode] = useState(false)

  // تحميل محاولات التفعيل السابقة من localStorage
  useEffect(() => {
    const storedAttempts = localStorage.getItem('activation_attempts')
    if (storedAttempts) {
      try {
        const parsed = JSON.parse(storedAttempts)
        setAttempts({
          count: parsed.count || 0,
          lastAttempt: new Date(parsed.lastAttempt || new Date())
        })
      } catch (error) {
        Logger.error('ActivationModal', 'خطأ في تحميل محاولات التفعيل:', error)
      }
    }
  }, [])

  // حفظ محاولات التفعيل في localStorage
  const saveAttempts = (newAttempts: ActivationAttempt) => {
    localStorage.setItem('activation_attempts', JSON.stringify(newAttempts))
    setAttempts(newAttempts)
  }

  // الحصول على رسالة الخطأ حسب عدد المحاولات
  const getErrorMessage = (attemptCount: number): { message: string; level: number } => {
    switch (attemptCount) {
      case 1:
        return {
          message: 'رقم التفعيل غير صحيح، يرجى المحاولة مرة أخرى',
          level: 1
        }
      case 2:
        return {
          message: 'تحذير: رقم التفعيل غير صحيح، تأكد من الرقم المدخل',
          level: 2
        }
      case 3:
        return {
          message: 'تحذير نهائي: استخدام أرقام خاطئة قد يؤدي لحظر البرنامج',
          level: 3
        }
      default:
        return {
          message: 'تحذير: تم تجاوز عدد المحاولات المسموح. تواصل مع الدعم الفني',
          level: 3
        }
    }
  }

  // معالجة التفعيل
  const handleActivation = async () => {
    if (!activationCode.trim()) {
      setError('يرجى إدخال رقم التفعيل')
      return
    }

    setLoading(true)
    setError('')

    try {
      // استدعاء API التفعيل
      const result = await window.electronAPI?.activateLicense(activationCode.trim())
      
      if (result?.success) {
        // نجح التفعيل - إعادة تعيين المحاولات
        localStorage.removeItem('activation_attempts')
        onActivationSuccess()
      } else {
        // فشل التفعيل - زيادة عدد المحاولات
        const newAttemptCount = attempts.count + 1
        const newAttempts: ActivationAttempt = {
          count: newAttemptCount,
          lastAttempt: new Date()
        }
        saveAttempts(newAttempts)

        const errorInfo = getErrorMessage(newAttemptCount)
        setError(errorInfo.message)
      }
    } catch (error) {
      Logger.error('ActivationModal', 'خطأ في التفعيل:', error)
      setError('حدث خطأ أثناء التفعيل. حاول مرة أخرى.')
    } finally {
      setLoading(false)
    }
  }



  if (!visible) return null

  const errorInfo = attempts.count > 0 ? getErrorMessage(attempts.count) : null

  return (
    <ActivationOverlay>
      <ActivationContainer>
        <LogoSection>
          <div className="app-logo">ZET.IA</div>
          <div className="app-subtitle">نظام إدارة شامل للمحاسبة والإنتاج</div>
          <div className="app-description">الحل الأمثل لإدارة أعمالك بكفاءة واحترافية</div>
        </LogoSection>

        <Title level={2} style={{
          color: '#1890ff',
          marginBottom: 25,
          fontSize: '28px',
          fontWeight: 'bold'
        }}>
          <SafetyOutlined style={{ marginLeft: 10 }} /> تفعيل البرنامج
        </Title>

        {/* رسائل خاصة لحالة الترخيص */}
        {licenseInfo?.daysRemaining !== undefined && licenseInfo?.daysRemaining < 0 ? (
          <Alert
            message="انتهت صلاحية الترخيص"
            description={`لقد انتهت فترة الترخيص منذ ${Math.abs(licenseInfo.daysRemaining)} يوم. يرجى إدخال رقم تفعيل جديد للمتابعة.`}
            type="error"
            showIcon
            style={{ marginBottom: 20, textAlign: 'right' }}
          />
        ) : licenseInfo?.daysRemaining !== undefined && licenseInfo?.daysRemaining <= 7 && licenseInfo?.daysRemaining > 0 ? (
          <Alert
            message="تحذير: الترخيص قريب من الانتهاء"
            description={`سينتهي الترخيص خلال ${licenseInfo.daysRemaining} أيام. يرجى تجديد الترخيص قريباً.`}
            type="warning"
            showIcon
            style={{ marginBottom: 20, textAlign: 'right' }}
          />
        ) : (
          <Paragraph style={{
            fontSize: 18,
            color: '#666',
            marginBottom: 30,
            lineHeight: 1.6
          }}>
            يرجى إدخال رقم التفعيل الخاص بك للمتابعة واستخدام البرنامج
          </Paragraph>
        )}

        <ActivationForm>
          <div className="activation-input">
            <Input.Password
              size="large"
              placeholder="أدخل رقم التفعيل هنا"
              value={activationCode}
              onChange={(e) => {
                const value = e.target.value.toUpperCase().trim()
                setActivationCode(value)
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleActivation()
                }
              }}
              onPaste={(e) => {
                // السماح باللصق وتنظيف النص
                e.preventDefault()
                const pastedText = e.clipboardData.getData('text').toUpperCase().trim()
                setActivationCode(pastedText)
              }}
              prefix={<KeyOutlined style={{ color: '#1890ff' }} />}
              maxLength={50}
              autoComplete="off"
              spellCheck={false}
              autoFocus
              visibilityToggle={{
                visible: showCode,
                onVisibleChange: setShowCode,
              }}
              style={{
                fontSize: 20,
                direction: 'ltr',
                textAlign: 'center',
                fontWeight: '600',
                letterSpacing: showCode ? '2px' : '4px',
                height: '60px',
                borderRadius: '12px',
                border: '2px solid #d9d9d9',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.3s ease'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = '#1890ff'
                e.target.style.boxShadow = '0 4px 16px rgba(24, 144, 255, 0.3)'
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d9d9d9'
                e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)'
              }}
            />
            <div style={{
              fontSize: 15,
              color: '#52c41a',
              marginTop: 15,
              textAlign: 'center',
              fontWeight: '600',
              background: 'linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(82, 196, 26, 0.05))',
              padding: '12px 20px',
              borderRadius: '8px',
              border: '1px solid rgba(82, 196, 26, 0.2)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px'
            }}>
              <KeyOutlined style={{ color: '#52c41a' }} />
              يمكنك نسخ ولصق رقم التفعيل مباشرة (Ctrl+V)
            </div>

            <div style={{
              fontSize: 14,
              color: '#1890ff',
              marginTop: 10,
              textAlign: 'center',
              fontWeight: '500',
              background: 'linear-gradient(135deg, rgba(24, 144, 255, 0.08), rgba(24, 144, 255, 0.04))',
              padding: '10px 16px',
              borderRadius: '6px',
              border: '1px solid rgba(24, 144, 255, 0.15)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '6px'
            }}>
              🔒 رقم التفعيل محمي بنقاط للأمان - اضغط على أيقونة العين لإظهاره
            </div>
          </div>

          <div className="activation-button">
            <Button
              type="primary"
              size="large"
              loading={loading}
              onClick={handleActivation}
              disabled={!activationCode.trim()}
              style={{
                width: '320px',
                height: '75px',
                fontSize: '22px',
                fontWeight: 'bold',
                borderRadius: '16px',
                background: activationCode.trim()
                  ? 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)'
                  : 'linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%)',
                border: 'none',
                boxShadow: activationCode.trim()
                  ? '0 12px 30px rgba(102, 126, 234, 0.4), 0 6px 15px rgba(118, 75, 162, 0.3)'
                  : '0 6px 15px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                transform: loading ? 'scale(0.98)' : 'scale(1)',
                position: 'relative',
                overflow: 'hidden'
              }}
              onMouseEnter={(e) => {
                if (activationCode.trim() && !loading) {
                  e.currentTarget.style.transform = 'scale(1.08) translateY(-2px)'
                  e.currentTarget.style.boxShadow = '0 20px 40px rgba(102, 126, 234, 0.5), 0 10px 20px rgba(118, 75, 162, 0.4)'
                }
              }}
              onMouseLeave={(e) => {
                if (!loading) {
                  e.currentTarget.style.transform = 'scale(1) translateY(0)'
                  e.currentTarget.style.boxShadow = activationCode.trim()
                    ? '0 12px 30px rgba(102, 126, 234, 0.4), 0 6px 15px rgba(118, 75, 162, 0.3)'
                    : '0 6px 15px rgba(0, 0, 0, 0.1)'
                }
              }}
            >
              {loading ? (
                <Space>
                  <span>جاري التفعيل</span>
                  <span style={{
                    animation: 'pulse 1.5s infinite',
                    display: 'inline-block',
                    fontSize: '24px'
                  }}>⚡</span>
                </Space>
              ) : (
                <Space>
                  <SafetyOutlined style={{ fontSize: '24px' }} />
                  <span>تفعيل البرنامج</span>
                </Space>
              )}
            </Button>
          </div>
        </ActivationForm>

        {error && (
          <ErrorAlert
            message={error}
            type={errorInfo?.level === 3 ? 'error' : errorInfo?.level === 2 ? 'warning' : 'info'}
            showIcon
            className={`warning-level-${errorInfo?.level || 1}`}
          />
        )}

        <Divider />

        <ContactInfo>
          <div className="contact-title">للحصول على رقم التفعيل أو الدعم الفني:</div>

          <div className="contact-item">
            <PhoneOutlined style={{ color: '#1890ff' }} />
            <Text strong copyable={{ text: '0569329925' }}>0569329925</Text>
          </div>

          <div className="contact-item">
            <MailOutlined style={{ color: '#1890ff' }} />
            <Text strong copyable={{ text: '<EMAIL>' }}>
              <EMAIL>
            </Text>
          </div>

          <div className="developer-info">
            <Text strong>المطور: FARESNAWAF</Text>
            <br />
            <Text type="secondary">متاح للدعم الفني والاستشارات</Text>
          </div>
        </ContactInfo>

        {attempts.count > 0 && (
          <Card size="small" style={{ marginTop: 20, background: '#fff7e6' }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              عدد محاولات التفعيل: {attempts.count}
            </Text>
          </Card>
        )}
      </ActivationContainer>
    </ActivationOverlay>
  )
}

export default ActivationModal
