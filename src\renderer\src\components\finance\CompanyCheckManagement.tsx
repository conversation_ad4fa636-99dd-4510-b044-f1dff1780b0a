import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Select,
  Input,
  Space,
  message,
  Row,
  Col,
  Statistic,
  DatePicker,
  Tag,
  Descriptions,
  InputNumber,
  Alert,
  Tooltip
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  CreditCardOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
  CheckOutlined,
  BankOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  EyeOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

interface CompanyCheckManagementProps {
  onBack: () => void
}

const CompanyCheckManagement: React.FC<CompanyCheckManagementProps> = ({ onBack }) => {
  const [companyChecks, setCompanyChecks] = useState([])
  const [bankAccounts, setBankAccounts] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [detailsModalVisible, setDetailsModalVisible] = useState(false)
  const [selectedCheck, setSelectedCheck] = useState<any>(null)
  const [form] = Form.useForm()

  useEffect(() => {
    loadCompanyChecks()
    loadBankAccounts()
  }, [])

  const loadCompanyChecks = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.getChecks()
      if (response.success) {
        // فلترة الشيكات الصادرة من الشركة فقط
        const companyChecksData = response.data.filter((check: any) => check.is_company_check === 1)
        setCompanyChecks(companyChecksData)
      } else {
        message.error('فشل في تحميل شيكات الشركة')
      }
    } catch (error) {
      message.error('خطأ في تحميل شيكات الشركة')
    }
    setLoading(false)
  }

  const loadBankAccounts = async () => {
    try {
      const response = await window.electronAPI.getBankAccounts()
      if (response.success) {
        setBankAccounts(response.data)
      }
    } catch (error) {
      Logger.error('CompanyCheckManagement', 'خطأ في تحميل الحسابات المصرفية:', error)
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      const checkData = {
        check_number: values.check_number,
        bank_account_id: values.bank_account_id,
        amount: values.amount,
        issue_date: values.issue_date.format('YYYY-MM-DD'),
        due_date: values.due_date ? values.due_date.format('YYYY-MM-DD') : values.issue_date.format('YYYY-MM-DD'),
        payee_name: values.payee_name,
        notes: values.notes,
        check_type: 'issued',
        original_payer: 'الشركة',
        current_holder: values.payee_name,
        is_company_check: 1,
        reference_type: values.reference_type,
        reference_id: values.reference_id,
        created_by: 1
      }

      const response = await window.electronAPI.createCheck(checkData)
      if (response.success) {
        message.success('تم إصدار شيك الشركة بنجاح')
        setModalVisible(false)
        form.resetFields()
        loadCompanyChecks()
      } else {
        message.error('فشل في إصدار شيك الشركة')
      }
    } catch (error) {
      message.error('خطأ في إصدار شيك الشركة')
    }
  }

  const generateCheckNumber = async () => {
    try {
      const response = await window.electronAPI.generateCheckNumber()
      if (response.success) {
        form.setFieldsValue({ check_number: response.data.checkNumber })
      }
    } catch (error) {
      message.error('خطأ في إنشاء رقم الشيك')
    }
  }

  const updateCheckStatus = async (checkId: number, status: string) => {
    try {
      const response = await window.electronAPI.updateCheckStatus(checkId, status)
      if (response.success) {
        message.success('تم تحديث حالة الشيك بنجاح')
        loadCompanyChecks()
      } else {
        message.error('فشل في تحديث حالة الشيك')
      }
    } catch (error) {
      message.error('خطأ في تحديث حالة الشيك')
    }
  }

  const showCheckDetails = (check: any) => {
    setSelectedCheck(check)
    setDetailsModalVisible(true)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'issued': return 'blue'
      case 'cashed': return 'green'
      case 'bounced': return 'red'
      case 'cancelled': return 'orange'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'issued': return 'مُصدر'
      case 'cashed': return 'محصل'
      case 'bounced': return 'مرتد'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'issued': return <ClockCircleOutlined />
      case 'cashed': return <CheckOutlined />
      case 'bounced': return <ExclamationCircleOutlined />
      case 'cancelled': return <WarningOutlined />
      default: return null
    }
  }

  const isCheckOverdue = (dueDate: string) => {
    return dayjs().isAfter(dayjs(dueDate))
  }

  const getDaysUntilDue = (dueDate: string) => {
    return dayjs(dueDate).diff(dayjs(), 'days')
  }

  const columns = [
    {
      title: 'رقم الشيك',
      dataIndex: 'check_number',
      key: 'check_number',
      render: (number: string) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{number}</span>
      )
    },
    {
      title: 'المستفيد',
      dataIndex: 'payee_name',
      key: 'payee_name',
      render: (name: string) => (
        <Space>
          <BankOutlined style={{ color: '#1890ff' }} />
          <span>{name}</span>
        </Space>
      )
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ₪ {amount?.toLocaleString() || 0}
        </span>
      )
    },
    {
      title: 'البنك',
      dataIndex: 'bank_name',
      key: 'bank_name',
    },
    {
      title: 'تاريخ الإصدار',
      dataIndex: 'issue_date',
      key: 'issue_date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'تاريخ الاستحقاق',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (date: string, record: any) => {
        const isOverdue = isCheckOverdue(date)
        const daysUntilDue = getDaysUntilDue(date)
        
        return (
          <div>
            <div style={{ color: isOverdue ? '#ff4d4f' : '#666' }}>
              {dayjs(date).format('YYYY-MM-DD')}
            </div>
            {record.status === 'issued' && (
              <div style={{ fontSize: '12px', color: isOverdue ? '#ff4d4f' : '#fa8c16' }}>
                {isOverdue ? `متأخر ${Math.abs(daysUntilDue)} يوم` : `باقي ${daysUntilDue} يوم`}
              </div>
            )}
          </div>
        )
      }
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: any) => (
        <Space>
          <Tooltip title="عرض التفاصيل">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => showCheckDetails(record)}
            />
          </Tooltip>
          {record.status === 'issued' && (
            <>
              <Tooltip title="تحديد كمحصل">
                <Button
                  type="primary"
                  size="small"
                  icon={<CheckOutlined />}
                  style={{ backgroundColor: '#52c41a' }}
                  onClick={() => updateCheckStatus(record.id, 'cashed')}
                />
              </Tooltip>
              <Tooltip title="تحديد كمرتد">
                <Button
                  danger
                  size="small"
                  icon={<ExclamationCircleOutlined />}
                  onClick={() => updateCheckStatus(record.id, 'bounced')}
                />
              </Tooltip>
              <Tooltip title="إلغاء">
                <Button
                  size="small"
                  icon={<WarningOutlined />}
                  onClick={() => updateCheckStatus(record.id, 'cancelled')}
                />
              </Tooltip>
            </>
          )}
        </Space>
      )
    }
  ]

  const stats = {
    totalChecks: companyChecks.length,
    issuedChecks: companyChecks.filter((check: any) => check.status === 'issued').length,
    cashedChecks: companyChecks.filter((check: any) => check.status === 'cashed').length,
    bouncedChecks: companyChecks.filter((check: any) => check.status === 'bounced').length,
    totalAmount: companyChecks.reduce((sum: number, check: any) => sum + (check.amount || 0), 0),
    overdueChecks: companyChecks.filter((check: any) => 
      check.status === 'issued' && isCheckOverdue(check.due_date)
    ).length
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>🏦 إدارة شيكات الشركة</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            إصدار وإدارة الشيكات الصادرة من الشركة
          </p>
        </div>
        <Button 
          type="default" 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
        >
          العودة
        </Button>
      </div>

      {/* تنبيهات الشيكات المتأخرة */}
      {stats.overdueChecks > 0 && (
        <Alert
          message={`تحذير: يوجد ${stats.overdueChecks} شيك متأخر الاستحقاق`}
          description="يرجى متابعة الشيكات المتأخرة مع المستفيدين"
          type="warning"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي الشيكات"
              value={stats.totalChecks}
              valueStyle={{ color: '#1890ff' }}
              prefix={<CreditCardOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="شيكات مُصدرة"
              value={stats.issuedChecks}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="شيكات محصلة"
              value={stats.cashedChecks}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="شيكات مرتدة"
              value={stats.bouncedChecks}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="شيكات متأخرة"
              value={stats.overdueChecks}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي المبالغ"
              value={stats.totalAmount}
              valueStyle={{ color: '#13c2c2' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      {/* شريط الأدوات */}
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            form.resetFields()
            form.setFieldsValue({
              issue_date: dayjs(),
              due_date: dayjs().add(30, 'days')
            })
            generateCheckNumber()
            setModalVisible(true)
          }}
        >
          إصدار شيك جديد
        </Button>
        <Button onClick={loadCompanyChecks}>
          تحديث
        </Button>
      </div>

      {/* جدول شيكات الشركة */}
      <Card title="شيكات الشركة">
        <Table
          columns={columns}
          dataSource={companyChecks}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          rowClassName={(record: any) =>
            record.status === 'issued' && isCheckOverdue(record.due_date) ? 'overdue-row' : ''
          }
        />
      </Card>

      {/* نموذج إصدار شيك جديد */}
      <Modal
        title="إصدار شيك جديد من الشركة"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="check_number"
                label="رقم الشيك"
                rules={[{ required: true, message: 'يرجى إدخال رقم الشيك' }]}
              >
                <Input
                  placeholder="CHK000001"
                  addonAfter={
                    <Button
                      type="link"
                      size="small"
                      onClick={generateCheckNumber}
                    >
                      توليد
                    </Button>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="bank_account_id"
                label="الحساب المصرفي"
                rules={[{ required: true, message: 'يرجى اختيار الحساب المصرفي' }]}
              >
                <Select placeholder="اختر الحساب المصرفي">
                  {bankAccounts.map((account: any) => (
                    <Option key={account.id} value={account.id}>
                      {account.bank_name} - {account.account_name} ({account.account_number})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="payee_name"
                label="اسم المستفيد"
                rules={[{ required: true, message: 'يرجى إدخال اسم المستفيد' }]}
              >
                <Input placeholder="اسم الشخص أو الشركة المستفيدة" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="amount"
                label="المبلغ"
                rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  formatter={value => `₪ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value: string | undefined) => {
                    if (!value) return 0;
                    const parsed = parseFloat(value.replace(/₪\s?|(,*)/g, ''));
                    return isNaN(parsed) ? 0 : parsed;
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="issue_date"
                label="تاريخ الإصدار"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الإصدار' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="اختر تاريخ الإصدار"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="due_date"
                label="تاريخ الاستحقاق"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الاستحقاق' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="اختر تاريخ الاستحقاق"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="reference_type"
                label="نوع المرجع (اختياري)"
              >
                <Select placeholder="اختر نوع المرجع">
                  <Option value="purchase_invoice">فاتورة مشتريات</Option>
                  <Option value="production_cost">تكلفة إنتاج</Option>
                  <Option value="expense">مصروف</Option>
                  <Option value="other">أخرى</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="reference_id"
                label="رقم المرجع (اختياري)"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="رقم الفاتورة أو المرجع"
                  min={1}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="ملاحّات"
          >
            <TextArea
              placeholder="ملاحّات إضافية حول الشيك"
              rows={3}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<CheckOutlined />}>
                إصدار الشيك
              </Button>
              <Button onClick={() => {
                setModalVisible(false)
                form.resetFields()
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نموذج تفاصيل الشيك */}
      <Modal
        title="تفاصيل الشيك"
        open={detailsModalVisible}
        onCancel={() => setDetailsModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailsModalVisible(false)}>
            إغلاق
          </Button>
        ]}
        width={600}
      >
        {selectedCheck && (
          <Descriptions title="بيانات الشيك" bordered size="small">
            <Descriptions.Item label="رقم الشيك">{selectedCheck.check_number}</Descriptions.Item>
            <Descriptions.Item label="المستفيد">{selectedCheck.payee_name}</Descriptions.Item>
            <Descriptions.Item label="المبلغ">
              <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                ₪ {selectedCheck.amount?.toLocaleString() || 0}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="البنك">{selectedCheck.bank_name}</Descriptions.Item>
            <Descriptions.Item label="رقم الحساب">{selectedCheck.account_number}</Descriptions.Item>
            <Descriptions.Item label="تاريخ الإصدار">{selectedCheck.issue_date}</Descriptions.Item>
            <Descriptions.Item label="تاريخ الاستحقاق">{selectedCheck.due_date}</Descriptions.Item>
            <Descriptions.Item label="الحالة">
              <Tag color={getStatusColor(selectedCheck.status)} icon={getStatusIcon(selectedCheck.status)}>
                {getStatusText(selectedCheck.status)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="نوع المرجع">{selectedCheck.reference_type || 'غير محدد'}</Descriptions.Item>
            <Descriptions.Item label="رقم المرجع">{selectedCheck.reference_id || 'غير محدد'}</Descriptions.Item>
            <Descriptions.Item label="ملاحّات" span={3}>
              {selectedCheck.notes || 'لا توجد ملاحّات'}
            </Descriptions.Item>
            <Descriptions.Item label="تاريخ الإنشاء">{dayjs(selectedCheck.created_at).format('YYYY-MM-DD HH:mm')}</Descriptions.Item>
            <Descriptions.Item label="آخر تحديث">{dayjs(selectedCheck.updated_at).format('YYYY-MM-DD HH:mm')}</Descriptions.Item>
          </Descriptions>
        )}
      </Modal>

      <style dangerouslySetInnerHTML={{
        __html: `
          .overdue-row {
            background-color: #fff2f0 !important;
          }
          .overdue-row:hover {
            background-color: #ffebe6 !important;
          }
        `
      }} />
    </div>
  )
}

export default CompanyCheckManagement
