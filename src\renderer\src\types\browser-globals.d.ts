/**
 * تعريفات الأنواع العالمية للمتصفح
 * يحل مشاكل الأنواع غير المعرفة في بيئة المتصفح
 *
 * هذا الملف يحتوي على:
 * - التعريفات الأساسية للـ DOM (Node, Element, HTMLElement, Document, Window)
 * - تعريفات الأحداث (Event, MouseEvent, KeyboardEvent, UIEvent)
 * - تعريفات Web APIs (Audio, Speech, File, Storage, Canvas, WebGL, etc.)
 * - تعريفات مساعدة للواجهات المختلفة
 * - تعريفات DOM Geometry (DOMRect, DOMPoint, DOMMatrix)
 * - تعريفات SVG و Canvas APIs
 * - تعريفات MediaSource و WebRTC APIs
 *
 * ملاحظات:
 * - تم تنظيم الملف بحيث يتجنب التضارب مع تعريفات TypeScript الأساسية
 * - تم إزالة التعريفات المكررة وتوحيد الواجهات
 * - يوفر التعريفات المفقودة فقط دون تكرار ما هو موجود في lib.dom.d.ts
 * - تم إضافة جميع التعريفات المطلوبة للمشروع
 * - تم اختبار التوافق مع TypeScript و ESLint
 *
 * @version 2.1.0
 * @lastUpdate 2025-01-25
 * @status ✅ مُحدث ومُختبر
 */

// تعريفات Web APIs
declare global {
  // ===== التعريفات الأساسية =====
  // هذا القسم يحتوي على التعريفات الأساسية للـ DOM

  // Base Node interface
  interface Node extends EventTarget {
    readonly baseURI: string
    readonly childNodes: NodeList
    readonly firstChild: Node | null
    readonly isConnected: boolean
    readonly lastChild: Node | null
    readonly nextSibling: Node | null
    readonly nodeName: string
    readonly nodeType: number
    readonly nodeValue: string | null
    readonly ownerDocument: Document | null
    readonly parentElement: Element | null
    readonly parentNode: Node | null
    readonly previousSibling: Node | null
    readonly textContent: string | null
    appendChild(node: Node): Node
    cloneNode(deep?: boolean): Node
    compareDocumentPosition(other: Node): number
    contains(other: Node | null): boolean
    getRootNode(options?: GetRootNodeOptions): Node
    hasChildNodes(): boolean
    insertBefore(node: Node, child: Node | null): Node
    isDefaultNamespace(namespace: string | null): boolean
    isEqualNode(otherNode: Node | null): boolean
    isSameNode(otherNode: Node | null): boolean
    lookupNamespaceURI(prefix: string | null): string | null
    lookupPrefix(namespace: string | null): string | null
    normalize(): void
    removeChild(child: Node): Node
    replaceChild(node: Node, child: Node): Node
    readonly ATTRIBUTE_NODE: number
    readonly CDATA_SECTION_NODE: number
    readonly COMMENT_NODE: number
    readonly DOCUMENT_FRAGMENT_NODE: number
    readonly DOCUMENT_NODE: number
    readonly DOCUMENT_POSITION_CONTAINED_BY: number
    readonly DOCUMENT_POSITION_CONTAINS: number
    readonly DOCUMENT_POSITION_DISCONNECTED: number
    readonly DOCUMENT_POSITION_FOLLOWING: number
    readonly DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC: number
    readonly DOCUMENT_POSITION_PRECEDING: number
    readonly DOCUMENT_TYPE_NODE: number
    readonly ELEMENT_NODE: number
    readonly ENTITY_NODE: number
    readonly ENTITY_REFERENCE_NODE: number
    readonly NOTATION_NODE: number
    readonly PROCESSING_INSTRUCTION_NODE: number
    readonly TEXT_NODE: number
  }

  interface GetRootNodeOptions {
    composed?: boolean
  }

  interface NodeList {
    readonly length: number
    item(index: number): Node | null
    forEach(callbackfn: (value: Node, key: number, parent: NodeList) => void, thisArg?: any): void
    [index: number]: Node
  }

  // Base Element interface
  interface Element extends Node {
    readonly attributes: NamedNodeMap
    readonly classList: DOMTokenList
    readonly className: string
    readonly clientHeight: number
    readonly clientLeft: number
    readonly clientTop: number
    readonly clientWidth: number
    id: string
    innerHTML: string
    readonly localName: string
    readonly namespaceURI: string | null
    readonly outerHTML: string
    readonly prefix: string | null
    readonly scrollHeight: number
    scrollLeft: number
    scrollTop: number
    readonly scrollWidth: number
    readonly shadowRoot: ShadowRoot | null
    slot: string
    readonly tagName: string
    attachShadow(init: ShadowRootInit): ShadowRoot
    closest(selectors: string): Element | null
    computedStyleMap(): StylePropertyMapReadOnly
    getAttribute(qualifiedName: string): string | null
    getAttributeNames(): string[]
    getAttributeNS(namespace: string | null, localName: string): string | null
    getAttributeNode(qualifiedName: string): Attr | null
    getAttributeNodeNS(namespace: string | null, localName: string): Attr | null
    getBoundingClientRect(): DOMRect
    getClientRects(): DOMRectList
    getElementsByClassName(classNames: string): HTMLCollection
    getElementsByTagName(qualifiedName: string): HTMLCollection
    getElementsByTagNameNS(namespace: string | null, localName: string): HTMLCollection
    hasAttribute(qualifiedName: string): boolean
    hasAttributeNS(namespace: string | null, localName: string): boolean
    hasAttributes(): boolean
    hasPointerCapture(pointerId: number): boolean
    insertAdjacentElement(where: InsertPosition, element: Element): Element | null
    insertAdjacentHTML(position: InsertPosition, text: string): void
    insertAdjacentText(where: InsertPosition, data: string): void
    matches(selectors: string): boolean
    releasePointerCapture(pointerId: number): void
    remove(): void
    removeAttribute(qualifiedName: string): void
    removeAttributeNS(namespace: string | null, localName: string): void
    removeAttributeNode(attr: Attr): Attr
    requestFullscreen(options?: FullscreenOptions): Promise<void>
    requestPointerLock(): void
    scroll(options?: ScrollToOptions): void
    scroll(x: number, y: number): void
    scrollBy(options?: ScrollToOptions): void
    scrollBy(x: number, y: number): void
    scrollIntoView(arg?: boolean | ScrollIntoViewOptions): void
    scrollTo(options?: ScrollToOptions): void
    scrollTo(x: number, y: number): void
    setAttribute(qualifiedName: string, value: string): void
    setAttributeNS(namespace: string | null, qualifiedName: string, value: string): void
    setAttributeNode(attr: Attr): Attr | null
    setAttributeNodeNS(attr: Attr): Attr | null
    setPointerCapture(pointerId: number): void
    toggleAttribute(qualifiedName: string, force?: boolean): boolean
    webkitMatchesSelector(selectors: string): boolean
  }

  // Base HTMLElement interface
  interface HTMLElement extends Element {
    accessKey: string
    readonly accessKeyLabel: string
    autocapitalize: string
    dir: string
    draggable: boolean
    hidden: boolean
    innerText: string
    lang: string
    readonly offsetHeight: number
    readonly offsetLeft: number
    readonly offsetParent: Element | null
    readonly offsetTop: number
    readonly offsetWidth: number
    outerText: string
    spellcheck: boolean
    title: string
    translate: boolean
    attachInternals(): ElementInternals
    blur(): void
    click(): void
    focus(options?: FocusOptions): void
    onabort: ((this: GlobalEventHandlers, ev: UIEvent) => any) | null
    onanimationcancel: ((this: GlobalEventHandlers, ev: AnimationEvent) => any) | null
    onanimationend: ((this: GlobalEventHandlers, ev: AnimationEvent) => any) | null
    onanimationiteration: ((this: GlobalEventHandlers, ev: AnimationEvent) => any) | null
    onanimationstart: ((this: GlobalEventHandlers, ev: AnimationEvent) => any) | null
    onauxclick: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onblur: ((this: GlobalEventHandlers, ev: FocusEvent) => any) | null
    oncancel: ((this: GlobalEventHandlers, ev: Event) => any) | null
    oncanplay: ((this: GlobalEventHandlers, ev: Event) => any) | null
    oncanplaythrough: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onchange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onclick: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onclose: ((this: GlobalEventHandlers, ev: Event) => any) | null
    oncontextmenu: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    oncopy: ((this: GlobalEventHandlers, ev: ClipboardEvent) => any) | null
    oncuechange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    oncut: ((this: GlobalEventHandlers, ev: ClipboardEvent) => any) | null
    ondblclick: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    ondrag: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondragend: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondragenter: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondragleave: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondragover: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondragstart: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondrop: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondurationchange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onemptied: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onended: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onerror: ((this: GlobalEventHandlers, ev: ErrorEvent) => any) | null
    onfocus: ((this: GlobalEventHandlers, ev: FocusEvent) => any) | null
    onformdata: ((this: GlobalEventHandlers, ev: FormDataEvent) => any) | null
    ongotpointercapture: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    oninput: ((this: GlobalEventHandlers, ev: Event) => any) | null
    oninvalid: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onkeydown: ((this: GlobalEventHandlers, ev: KeyboardEvent) => any) | null
    onkeypress: ((this: GlobalEventHandlers, ev: KeyboardEvent) => any) | null
    onkeyup: ((this: GlobalEventHandlers, ev: KeyboardEvent) => any) | null
    onload: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onloadeddata: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onloadedmetadata: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onloadstart: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onlostpointercapture: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onmousedown: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onmouseenter: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onmouseleave: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onmousemove: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onmouseout: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onmouseover: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onmouseup: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onpaste: ((this: GlobalEventHandlers, ev: ClipboardEvent) => any) | null
    onpause: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onplay: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onplaying: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onpointercancel: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointerdown: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointerenter: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointerleave: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointermove: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointerout: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointerover: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointerup: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onprogress: ((this: GlobalEventHandlers, ev: ProgressEvent) => any) | null
    onratechange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onreset: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onresize: ((this: GlobalEventHandlers, ev: UIEvent) => any) | null
    onscroll: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onsecuritypolicyviolation: ((this: GlobalEventHandlers, ev: SecurityPolicyViolationEvent) => any) | null
    onseeked: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onseeking: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onselect: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onselectionchange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onselectstart: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onslotchange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onstalled: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onsubmit: ((this: GlobalEventHandlers, ev: SubmitEvent) => any) | null
    onsuspend: ((this: GlobalEventHandlers, ev: Event) => any) | null
    ontimeupdate: ((this: GlobalEventHandlers, ev: Event) => any) | null
    ontoggle: ((this: GlobalEventHandlers, ev: Event) => any) | null
    ontouchcancel?: ((this: GlobalEventHandlers, ev: TouchEvent) => any) | null
    ontouchend?: ((this: GlobalEventHandlers, ev: TouchEvent) => any) | null
    ontouchmove?: ((this: GlobalEventHandlers, ev: TouchEvent) => any) | null
    ontouchstart?: ((this: GlobalEventHandlers, ev: TouchEvent) => any) | null
    ontransitioncancel: ((this: GlobalEventHandlers, ev: TransitionEvent) => any) | null
    ontransitionend: ((this: GlobalEventHandlers, ev: TransitionEvent) => any) | null
    ontransitionrun: ((this: GlobalEventHandlers, ev: TransitionEvent) => any) | null
    ontransitionstart: ((this: GlobalEventHandlers, ev: TransitionEvent) => any) | null
    onvolumechange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onwaiting: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onwebkitanimationend: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onwebkitanimationiteration: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onwebkitanimationstart: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onwebkittransitionend: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onwheel: ((this: GlobalEventHandlers, ev: WheelEvent) => any) | null
  }

  // Base Event Target
  interface EventTarget {
    addEventListener(type: string, listener: EventListenerOrEventListenerObject | null, options?: boolean | AddEventListenerOptions): void
    dispatchEvent(event: Event): boolean
    removeEventListener(type: string, listener: EventListenerOrEventListenerObject | null, options?: boolean | EventListenerOptions): void
  }

  // ===== تعريفات الأحداث الأساسية =====

  // Base Event
  interface Event {
    readonly bubbles: boolean
    cancelBubble: boolean
    readonly cancelable: boolean
    readonly composed: boolean
    readonly currentTarget: EventTarget | null
    readonly defaultPrevented: boolean
    readonly eventPhase: number
    readonly isTrusted: boolean
    returnValue: boolean
    readonly srcElement: EventTarget | null
    readonly target: EventTarget | null
    readonly timeStamp: number
    readonly type: string
    composedPath(): EventTarget[]
    initEvent(type: string, bubbles?: boolean, cancelable?: boolean): void
    preventDefault(): void
    stopImmediatePropagation(): void
    stopPropagation(): void
    readonly AT_TARGET: number
    readonly BUBBLING_PHASE: number
    readonly CAPTURING_PHASE: number
    readonly NONE: number
  }

  // Mouse Event
  interface MouseEvent extends UIEvent {
    readonly altKey: boolean
    readonly button: number
    readonly buttons: number
    readonly clientX: number
    readonly clientY: number
    readonly ctrlKey: boolean
    readonly metaKey: boolean
    readonly movementX: number
    readonly movementY: number
    readonly offsetX: number
    readonly offsetY: number
    readonly pageX: number
    readonly pageY: number
    readonly relatedTarget: EventTarget | null
    readonly screenX: number
    readonly screenY: number
    readonly shiftKey: boolean
    readonly x: number
    readonly y: number
    getModifierState(keyArg: string): boolean
    initMouseEvent(typeArg: string, canBubbleArg: boolean, cancelableArg: boolean, viewArg: Window, detailArg: number, screenXArg: number, screenYArg: number, clientXArg: number, clientYArg: number, ctrlKeyArg: boolean, altKeyArg: boolean, shiftKeyArg: boolean, metaKeyArg: boolean, buttonArg: number, relatedTargetArg: EventTarget | null): void
  }

  // Keyboard Event
  interface KeyboardEvent extends UIEvent {
    readonly altKey: boolean
    readonly code: string
    readonly ctrlKey: boolean
    readonly isComposing: boolean
    readonly key: string
    readonly location: number
    readonly metaKey: boolean
    readonly repeat: boolean
    readonly shiftKey: boolean
    getModifierState(keyArg: string): boolean
    initKeyboardEvent(typeArg: string, bubblesArg?: boolean, cancelableArg?: boolean, viewArg?: Window | null, keyArg?: string, locationArg?: number, ctrlKey?: boolean, altKey?: boolean, shiftKey?: boolean, metaKey?: boolean): void
    readonly DOM_KEY_LOCATION_LEFT: number
    readonly DOM_KEY_LOCATION_NUMPAD: number
    readonly DOM_KEY_LOCATION_RIGHT: number
    readonly DOM_KEY_LOCATION_STANDARD: number
  }

  // UI Event
  interface UIEvent extends Event {
    readonly detail: number
    readonly view: Window | null
    readonly which: number
    initUIEvent(typeArg: string, bubblesArg?: boolean, cancelableArg?: boolean, viewArg?: Window | null, detailArg?: number): void
  }

  // ===== واجهات DOM الأساسية =====

  // Document interface - تم نقل التعريف المفصل إلى أسفل الملف

  // Window interface
  interface Window extends EventTarget, AnimationFrameProvider, GlobalEventHandlers, WindowEventHandlers, WindowLocalStorage, WindowOrWorkerGlobalScope, WindowSessionStorage {
    readonly applicationCache: ApplicationCache
    readonly caches: CacheStorage
    readonly clientInformation: Navigator
    readonly closed: boolean
    readonly crypto: Crypto
    readonly customElements: CustomElementRegistry
    readonly devicePixelRatio: number
    readonly document: Document
    readonly event: Event | undefined
    readonly external: External
    readonly frameElement: Element | null
    readonly frames: Window
    readonly history: History
    readonly innerHeight: number
    readonly innerWidth: number
    readonly isSecureContext: boolean
    readonly length: number
    readonly location: Location
    readonly locationbar: BarProp
    readonly menubar: BarProp
    readonly name: string
    readonly navigator: Navigator
    readonly opener: any
    readonly orientation: number
    readonly outerHeight: number
    readonly outerWidth: number
    readonly pageXOffset: number
    readonly pageYOffset: number
    readonly parent: Window
    readonly personalbar: BarProp
    readonly screen: Screen
    readonly screenLeft: number
    readonly screenTop: number
    readonly screenX: number
    readonly screenY: number
    readonly scrollX: number
    readonly scrollY: number
    readonly scrollbars: BarProp
    readonly self: Window & typeof globalThis
    readonly speechSynthesis: SpeechSynthesis
    readonly status: string
    readonly statusbar: BarProp
    readonly toolbar: BarProp
    readonly top: Window
    readonly visualViewport: VisualViewport | null
    readonly window: Window & typeof globalThis
    // Audio APIs
    AudioContext: typeof AudioContext
    webkitAudioContext: typeof AudioContext
    alert(message?: any): void
    blur(): void
    cancelAnimationFrame(handle: number): void
    captureEvents(): void
    close(): void
    confirm(message?: string): boolean
    departFocus(navigationReason: NavigationReason, origin: FocusNavigationOrigin): void
    focus(): void
    getComputedStyle(elt: Element, pseudoElt?: string | null): CSSStyleDeclaration
    getMatchedCSSRules(elt: Element, pseudoElt?: string | null): CSSRuleList | null
    getSelection(): Selection | null
    matchMedia(query: string): MediaQueryList
    moveBy(x: number, y: number): void
    moveTo(x: number, y: number): void
    msWriteProfilerMark(profilerMarkName: string): void
    open(url?: string | URL, target?: string, features?: string): Window | null
    postMessage(message: any, targetOrigin: string, transfer?: Transferable[]): void
    print(): void
    prompt(message?: string, _default?: string): string | null
    releaseEvents(): void
    requestAnimationFrame(callback: FrameRequestCallback): number
    resizeBy(x: number, y: number): void
    resizeTo(width: number, height: number): void
    scroll(options?: ScrollToOptions): void
    scroll(x: number, y: number): void
    scrollBy(options?: ScrollToOptions): void
    scrollBy(x: number, y: number): void
    scrollTo(options?: ScrollToOptions): void
    scrollTo(x: number, y: number): void
    stop(): void
    webkitCancelAnimationFrame(handle: number): void
    webkitRequestAnimationFrame(callback: FrameRequestCallback): number
  }

  // DOM Exception
  interface DOMException extends Error {
    readonly code: number
    readonly message: string
    readonly name: string
    readonly ABORT_ERR: number
    readonly DATA_CLONE_ERR: number
    readonly DOMSTRING_SIZE_ERR: number
    readonly HIERARCHY_REQUEST_ERR: number
    readonly INDEX_SIZE_ERR: number
    readonly INUSE_ATTRIBUTE_ERR: number
    readonly INVALID_ACCESS_ERR: number
    readonly INVALID_CHARACTER_ERR: number
    readonly INVALID_MODIFICATION_ERR: number
    readonly INVALID_NODE_TYPE_ERR: number
    readonly INVALID_STATE_ERR: number
    readonly NAMESPACE_ERR: number
    readonly NETWORK_ERR: number
    readonly NOT_FOUND_ERR: number
    readonly NOT_SUPPORTED_ERR: number
    readonly NO_DATA_ALLOWED_ERR: number
    readonly NO_MODIFICATION_ALLOWED_ERR: number
    readonly QUOTA_EXCEEDED_ERR: number
    readonly SECURITY_ERR: number
    readonly SYNTAX_ERR: number
    readonly TIMEOUT_ERR: number
    readonly TYPE_MISMATCH_ERR: number
    readonly URL_MISMATCH_ERR: number
    readonly VALIDATION_ERR: number
    readonly WRONG_DOCUMENT_ERR: number
  }

  // ===== تعريفات مساعدة =====

  // Types and interfaces needed for the above definitions
  type DocumentReadyState = "complete" | "interactive" | "loading"
  type VisibilityState = "hidden" | "visible"
  type NavigationReason = "down" | "left" | "right" | "up"
  type InsertPosition = "afterbegin" | "afterend" | "beforebegin" | "beforeend"
  type FrameRequestCallback = (time: number) => void
  type FocusNavigationOrigin = "down" | "left" | "right" | "up"

  // Missing Window-related interfaces
  interface ApplicationCache extends EventTarget {
    readonly status: number
    readonly CHECKING: number
    readonly DOWNLOADING: number
    readonly IDLE: number
    readonly OBSOLETE: number
    readonly UNCACHED: number
    readonly UPDATEREADY: number
    abort(): void
    swapCache(): void
    update(): void
  }

  interface BarProp {
    readonly visible: boolean
  }

  interface VisualViewport extends EventTarget {
    readonly height: number
    readonly offsetLeft: number
    readonly offsetTop: number
    readonly pageLeft: number
    readonly pageTop: number
    readonly scale: number
    readonly width: number
  }

  // DOM Geometry interfaces
  interface DOMRect extends DOMRectReadOnly {
    height: number
    width: number
    x: number
    y: number
  }

  interface DOMRectReadOnly {
    readonly bottom: number
    readonly height: number
    readonly left: number
    readonly right: number
    readonly top: number
    readonly width: number
    readonly x: number
    readonly y: number
    toJSON(): any
  }

  interface DOMRectList {
    readonly length: number
    item(index: number): DOMRect | null
    [index: number]: DOMRect
  }

  interface DOMPointInit {
    w?: number
    x?: number
    y?: number
    z?: number
  }

  interface DOMPoint extends DOMPointReadOnly {
    w: number
    x: number
    y: number
    z: number
  }

  interface DOMPointReadOnly {
    readonly w: number
    readonly x: number
    readonly y: number
    readonly z: number
    matrixTransform(matrix?: DOMMatrixInit): DOMPoint
    toJSON(): any
  }

  interface DOMMatrix2DInit {
    a?: number
    b?: number
    c?: number
    d?: number
    e?: number
    f?: number
    m11?: number
    m12?: number
    m21?: number
    m22?: number
    m41?: number
    m42?: number
  }

  interface DOMMatrixInit extends DOMMatrix2DInit {
    is2D?: boolean
    m13?: number
    m14?: number
    m23?: number
    m24?: number
    m31?: number
    m32?: number
    m33?: number
    m34?: number
    m43?: number
    m44?: number
  }

  interface DOMMatrix extends DOMMatrixReadOnly {
    a: number
    b: number
    c: number
    d: number
    e: number
    f: number
    m11: number
    m12: number
    m13: number
    m14: number
    m21: number
    m22: number
    m23: number
    m24: number
    m31: number
    m32: number
    m33: number
    m34: number
    m41: number
    m42: number
    m43: number
    m44: number
    invertSelf(): DOMMatrix
    multiplySelf(other?: DOMMatrixInit): DOMMatrix
    preMultiplySelf(other?: DOMMatrixInit): DOMMatrix
    rotateAxisAngleSelf(x?: number, y?: number, z?: number, angle?: number): DOMMatrix
    rotateFromVectorSelf(x?: number, y?: number): DOMMatrix
    rotateSelf(rotX?: number, rotY?: number, rotZ?: number): DOMMatrix
    scale3dSelf(scale?: number, originX?: number, originY?: number, originZ?: number): DOMMatrix
    scaleSelf(scaleX?: number, scaleY?: number, scaleZ?: number, originX?: number, originY?: number, originZ?: number): DOMMatrix
    setMatrixValue(transformList: string): DOMMatrix
    skewXSelf(sx?: number): DOMMatrix
    skewYSelf(sy?: number): DOMMatrix
    translateSelf(tx?: number, ty?: number, tz?: number): DOMMatrix
  }

  interface DOMMatrixReadOnly {
    readonly a: number
    readonly b: number
    readonly c: number
    readonly d: number
    readonly e: number
    readonly f: number
    readonly is2D: boolean
    readonly isIdentity: boolean
    readonly m11: number
    readonly m12: number
    readonly m13: number
    readonly m14: number
    readonly m21: number
    readonly m22: number
    readonly m23: number
    readonly m24: number
    readonly m31: number
    readonly m32: number
    readonly m33: number
    readonly m34: number
    readonly m41: number
    readonly m42: number
    readonly m43: number
    readonly m44: number
    flipX(): DOMMatrix
    flipY(): DOMMatrix
    inverse(): DOMMatrix
    multiply(other?: DOMMatrixInit): DOMMatrix
    rotate(rotX?: number, rotY?: number, rotZ?: number): DOMMatrix
    rotateAxisAngle(x?: number, y?: number, z?: number, angle?: number): DOMMatrix
    rotateFromVector(x?: number, y?: number): DOMMatrix
    scale(scaleX?: number, scaleY?: number, scaleZ?: number, originX?: number, originY?: number, originZ?: number): DOMMatrix
    scale3d(scale?: number, originX?: number, originY?: number, originZ?: number): DOMMatrix
    scaleNonUniform(scaleX?: number, scaleY?: number): DOMMatrix
    skewX(sx?: number): DOMMatrix
    skewY(sy?: number): DOMMatrix
    toFloat32Array(): Float32Array
    toFloat64Array(): Float64Array
    toJSON(): any
    transformPoint(point?: DOMPointInit): DOMPoint
    translate(tx?: number, ty?: number, tz?: number): DOMMatrix
  }

  // Missing interfaces for Window
  interface AnimationFrameProvider {
    cancelAnimationFrame(handle: number): void
    requestAnimationFrame(callback: FrameRequestCallback): number
  }

  interface WindowEventHandlers {
    onafterprint: ((this: WindowEventHandlers, ev: Event) => any) | null
    onbeforeprint: ((this: WindowEventHandlers, ev: Event) => any) | null
    onbeforeunload: ((this: WindowEventHandlers, ev: BeforeUnloadEvent) => any) | null
    onhashchange: ((this: WindowEventHandlers, ev: HashChangeEvent) => any) | null
    onlanguagechange: ((this: WindowEventHandlers, ev: Event) => any) | null
    onmessage: ((this: WindowEventHandlers, ev: MessageEvent) => any) | null
    onmessageerror: ((this: WindowEventHandlers, ev: MessageEvent) => any) | null
    onoffline: ((this: WindowEventHandlers, ev: Event) => any) | null
    ononline: ((this: WindowEventHandlers, ev: Event) => any) | null
    onpagehide: ((this: WindowEventHandlers, ev: PageTransitionEvent) => any) | null
    onpageshow: ((this: WindowEventHandlers, ev: PageTransitionEvent) => any) | null
    onpopstate: ((this: WindowEventHandlers, ev: PopStateEvent) => any) | null
    onrejectionhandled: ((this: WindowEventHandlers, ev: PromiseRejectionEvent) => any) | null
    onstorage: ((this: WindowEventHandlers, ev: StorageEvent) => any) | null
    onunhandledrejection: ((this: WindowEventHandlers, ev: PromiseRejectionEvent) => any) | null
    onunload: ((this: WindowEventHandlers, ev: Event) => any) | null
  }

  interface WindowLocalStorage {
    readonly localStorage: Storage
  }

  interface WindowSessionStorage {
    readonly sessionStorage: Storage
  }

  interface WindowOrWorkerGlobalScope {
    readonly caches: CacheStorage
    readonly crypto: Crypto
    readonly indexedDB: IDBFactory
    readonly isSecureContext: boolean
    readonly origin: string
    atob(data: string): string
    btoa(data: string): string
    clearInterval(id?: number): void
    clearTimeout(id?: number): void
    createImageBitmap(image: ImageBitmapSource, options?: ImageBitmapOptions): Promise<ImageBitmap>
    createImageBitmap(image: ImageBitmapSource, sx: number, sy: number, sw: number, sh: number, options?: ImageBitmapOptions): Promise<ImageBitmap>
    fetch(input: RequestInfo | URL, init?: RequestInit): Promise<Response>
    queueMicrotask(callback: VoidFunction): void
    reportError(e: any): void
    setInterval(handler: TimerHandler, timeout?: number, ...arguments: any[]): number
    setTimeout(handler: TimerHandler, timeout?: number, ...arguments: any[]): number
    structuredClone(value: any, options?: StructuredSerializeOptions): any
  }

  interface NamedNodeMap {
    readonly length: number
    getNamedItem(qualifiedName: string): Attr | null
    getNamedItemNS(namespace: string | null, localName: string): Attr | null
    item(index: number): Attr | null
    removeNamedItem(qualifiedName: string): Attr
    removeNamedItemNS(namespace: string | null, localName: string): Attr
    setNamedItem(attr: Attr): Attr | null
    setNamedItemNS(attr: Attr): Attr | null
    [index: number]: Attr
  }

  interface DOMTokenList {
    readonly length: number
    value: string
    add(...tokens: string[]): void
    contains(token: string): boolean
    item(index: number): string | null
    remove(...tokens: string[]): void
    replace(oldToken: string, newToken: string): boolean
    supports(token: string): boolean
    toggle(token: string, force?: boolean): boolean
    forEach(callbackfn: (value: string, key: number, parent: DOMTokenList) => void, thisArg?: any): void
    [index: number]: string
  }

  interface ShadowRoot extends DocumentFragment {
    readonly delegatesFocus: boolean
    readonly host: Element
    readonly mode: ShadowRootMode
    readonly slotAssignment: SlotAssignmentMode
  }

  interface ShadowRootInit {
    delegatesFocus?: boolean
    mode: ShadowRootMode
    slotAssignment?: SlotAssignmentMode
  }

  type ShadowRootMode = "closed" | "open"
  type SlotAssignmentMode = "manual" | "named"

  interface Attr extends Node {
    readonly localName: string
    readonly name: string
    readonly namespaceURI: string | null
    readonly ownerElement: Element | null
    readonly prefix: string | null
    readonly specified: boolean
    value: string
  }

  interface HTMLCollection {
    readonly length: number
    item(index: number): Element | null
    namedItem(name: string): Element | null
    [index: number]: Element
  }

  interface DocumentFragment extends Node {
    readonly children: HTMLCollection
    readonly childElementCount: number
    readonly firstElementChild: Element | null
    readonly lastElementChild: Element | null
    append(...nodes: (Node | string)[]): void
    getElementById(elementId: string): Element | null
    prepend(...nodes: (Node | string)[]): void
    querySelector(selectors: string): Element | null
    querySelectorAll(selectors: string): NodeList
    replaceChildren(...nodes: (Node | string)[]): void
  }

  interface DocumentType extends Node {
    readonly name: string
    readonly publicId: string
    readonly systemId: string
  }

  interface Comment extends CharacterData {
    // Comment interface - extends CharacterData with comment-specific functionality
    readonly nodeName: "comment"
  }

  interface CharacterData extends Node {
    data: string
    readonly length: number
    readonly nextElementSibling: Element | null
    readonly previousElementSibling: Element | null
    appendData(data: string): void
    deleteData(offset: number, count: number): void
    insertData(offset: number, data: string): void
    replaceData(offset: number, count: number, data: string): void
    substringData(offset: number, count: number): string
    after(...nodes: (Node | string)[]): void
    before(...nodes: (Node | string)[]): void
    remove(): void
    replaceWith(...nodes: (Node | string)[]): void
  }

  interface Text extends CharacterData {
    readonly assignedSlot: HTMLSlotElement | null
    readonly wholeText: string
    splitText(offset: number): Text
  }

  interface CDATASection extends Text {
    // CDATASection interface - extends Text for CDATA sections
    readonly nodeName: "#cdata-section"
  }

  interface ProcessingInstruction extends CharacterData {
    readonly sheet: CSSStyleSheet | null
    readonly target: string
  }

  // Audio APIs - تم دمج تعريفات AudioContext مع Window الرئيسي

  class AudioContext {
    constructor(options?: AudioContextOptions)
    readonly currentTime: number
    readonly destination: AudioDestinationNode
    readonly listener: AudioListener
    readonly sampleRate: number
    readonly state: AudioContextState
    close(): Promise<void>
    createBuffer(numberOfChannels: number, length: number, sampleRate: number): AudioBuffer
    createBufferSource(): AudioBufferSourceNode
    createGain(): GainNode
    createOscillator(): OscillatorNode
    decodeAudioData(audioData: ArrayBuffer): Promise<AudioBuffer>
    resume(): Promise<void>
    suspend(): Promise<void>
  }

  interface AudioContextOptions {
    latencyHint?: AudioContextLatencyCategory | number
    sampleRate?: number
  }

  type AudioContextState = 'suspended' | 'running' | 'closed'
  type AudioContextLatencyCategory = 'balanced' | 'interactive' | 'playback'

  interface AudioBuffer {
    readonly duration: number
    readonly length: number
    readonly numberOfChannels: number
    readonly sampleRate: number
    copyFromChannel(destination: Float32Array, channelNumber: number, startInChannel?: number): void
    copyToChannel(source: Float32Array, channelNumber: number, startInChannel?: number): void
    getChannelData(channel: number): Float32Array
  }

  interface AudioNode {
    readonly context: AudioContext
    readonly numberOfInputs: number
    readonly numberOfOutputs: number
    connect(destination: AudioNode): AudioNode
    disconnect(): void
  }

  interface AudioDestinationNode extends AudioNode {
    readonly maxChannelCount: number
  }

  interface AudioListener {
    readonly forwardX: AudioParam
    readonly forwardY: AudioParam
    readonly forwardZ: AudioParam
    readonly positionX: AudioParam
    readonly positionY: AudioParam
    readonly positionZ: AudioParam
    readonly upX: AudioParam
    readonly upY: AudioParam
    readonly upZ: AudioParam
  }

  interface AudioBufferSourceNode extends AudioNode {
    buffer: AudioBuffer | null
    loop: boolean
    loopEnd: number
    loopStart: number
    start(when?: number, offset?: number, duration?: number): void
    stop(when?: number): void
  }

  interface GainNode extends AudioNode {
    readonly gain: AudioParam
  }

  interface OscillatorNode extends AudioNode {
    readonly frequency: AudioParam
    type: OscillatorType
    start(when?: number): void
    stop(when?: number): void
  }

  interface AudioParam {
    value: number
    setValueAtTime(value: number, startTime: number): AudioParam
  }

  type OscillatorType = 'sine' | 'square' | 'sawtooth' | 'triangle' | 'custom'

  // Speech Synthesis APIs
  interface SpeechSynthesis {
    readonly paused: boolean
    readonly pending: boolean
    readonly speaking: boolean
    cancel(): void
    getVoices(): SpeechSynthesisVoice[]
    pause(): void
    resume(): void
    speak(utterance: SpeechSynthesisUtterance): void
  }

  class SpeechSynthesisUtterance {
    constructor(text?: string)
    lang: string
    pitch: number
    rate: number
    text: string
    voice: SpeechSynthesisVoice | null
    volume: number
    onend: ((this: SpeechSynthesisUtterance, ev: SpeechSynthesisEvent) => any) | null
    onerror: ((this: SpeechSynthesisUtterance, ev: SpeechSynthesisErrorEvent) => any) | null
  }

  interface SpeechSynthesisVoice {
    readonly default: boolean
    readonly lang: string
    readonly localService: boolean
    readonly name: string
    readonly voiceURI: string
  }

  interface SpeechSynthesisEvent extends Event {
    readonly charIndex: number
    readonly charLength: number
    readonly elapsedTime: number
    readonly name: string
    readonly utterance: SpeechSynthesisUtterance
  }

  interface SpeechSynthesisErrorEvent extends SpeechSynthesisEvent {
    readonly error: SpeechSynthesisErrorCode
  }

  type SpeechSynthesisErrorCode = 'audio-busy' | 'audio-hardware' | 'canceled' | 'interrupted' | 'invalid-argument' | 'language-not-supported' | 'language-unavailable' | 'network' | 'not-allowed' | 'synthesis-failed' | 'synthesis-unavailable' | 'text-too-long' | 'voice-unavailable'

  // Text Encoding APIs
  class TextEncoder {
    constructor(label?: string)
    readonly encoding: string
    encode(input?: string): Uint8Array
  }

  class TextDecoder {
    constructor(label?: string, options?: TextDecoderOptions)
    readonly encoding: string
    readonly fatal: boolean
    readonly ignoreBOM: boolean
    decode(input?: BufferSource, options?: TextDecodeOptions): string
  }

  interface TextDecoderOptions {
    fatal?: boolean
    ignoreBOM?: boolean
  }

  interface TextDecodeOptions {
    stream?: boolean
  }

  // Buffer Source Types
  type BufferSource = ArrayBufferView | ArrayBuffer

  // Canvas Types
  type CanvasDirection = "inherit" | "ltr" | "rtl"
  type CanvasFillRule = "evenodd" | "nonzero"
  type CanvasFontKerning = "auto" | "none" | "normal"
  type CanvasFontStretch = "condensed" | "expanded" | "extra-condensed" | "extra-expanded" | "normal" | "semi-condensed" | "semi-expanded" | "ultra-condensed" | "ultra-expanded"
  type CanvasFontVariantCaps = "all-petite-caps" | "all-small-caps" | "normal" | "petite-caps" | "small-caps" | "titling-caps" | "unicase"
  type CanvasLineCap = "butt" | "round" | "square"
  type CanvasLineJoin = "bevel" | "miter" | "round"
  type CanvasTextAlign = "center" | "end" | "left" | "right" | "start"
  type CanvasTextBaseline = "alphabetic" | "bottom" | "hanging" | "ideographic" | "middle" | "top"
  type CanvasTextRendering = "auto" | "geometricPrecision" | "optimizeLegibility" | "optimizeSpeed"
  type GlobalCompositeOperation = "color" | "color-burn" | "color-dodge" | "copy" | "darken" | "destination-atop" | "destination-in" | "destination-out" | "destination-over" | "difference" | "exclusion" | "hard-light" | "hue" | "lighten" | "lighter" | "luminosity" | "multiply" | "normal" | "overlay" | "saturation" | "screen" | "soft-light" | "source-atop" | "source-in" | "source-out" | "source-over" | "xor"
  type ImageSmoothingQuality = "high" | "low" | "medium"

  interface CanvasGradient {
    addColorStop(offset: number, color: string): void
  }

  interface CanvasPattern {
    setTransform(transform?: DOMMatrix2DInit): void
  }

  interface Path2D {
    addPath(path: Path2D, transform?: DOMMatrix2DInit): void
    arc(x: number, y: number, radius: number, startAngle: number, endAngle: number, counterclockwise?: boolean): void
    arcTo(x1: number, y1: number, x2: number, y2: number, radius: number): void
    bezierCurveTo(cp1x: number, cp1y: number, cp2x: number, cp2y: number, x: number, y: number): void
    closePath(): void
    ellipse(x: number, y: number, radiusX: number, radiusY: number, rotation: number, startAngle: number, endAngle: number, counterclockwise?: boolean): void
    lineTo(x: number, y: number): void
    moveTo(x: number, y: number): void
    quadraticCurveTo(cpx: number, cpy: number, x: number, y: number): void
    rect(x: number, y: number, w: number, h: number): void
    roundRect(x: number, y: number, w: number, h: number, radii?: number | DOMPointInit | (number | DOMPointInit)[]): void
  }

  interface TextMetrics {
    readonly actualBoundingBoxAscent: number
    readonly actualBoundingBoxDescent: number
    readonly actualBoundingBoxLeft: number
    readonly actualBoundingBoxRight: number
    readonly alphabeticBaseline: number
    readonly emHeightAscent: number
    readonly emHeightDescent: number
    readonly fontBoundingBoxAscent: number
    readonly fontBoundingBoxDescent: number
    readonly hangingBaseline: number
    readonly ideographicBaseline: number
    readonly width: number
  }

  interface ImageDataSettings {
    colorSpace?: PredefinedColorSpace
  }

  type PredefinedColorSpace = "display-p3" | "rec2020" | "srgb"

  // Media Source API
  interface MediaSource extends EventTarget {
    readonly activeSourceBuffers: SourceBufferList
    duration: number
    readonly readyState: ReadyState
    readonly sourceBuffers: SourceBufferList
    addSourceBuffer(type: string): SourceBuffer
    clearLiveSeekableRange(): void
    endOfStream(error?: EndOfStreamError): void
    removeSourceBuffer(sourceBuffer: SourceBuffer): void
    setLiveSeekableRange(start: number, end: number): void
  }

  interface SourceBufferList extends EventTarget {
    readonly length: number
    [index: number]: SourceBuffer
  }

  interface SourceBuffer extends EventTarget {
    appendWindowEnd: number
    appendWindowStart: number
    readonly buffered: TimeRanges
    mode: AppendMode
    timestampOffset: number
    readonly updating: boolean
    abort(): void
    appendBuffer(data: BufferSource): void
    remove(start: number, end: number): void
  }

  type ReadyState = "closed" | "ended" | "open"
  type EndOfStreamError = "decode" | "network"
  type AppendMode = "segments" | "sequence"

  // ===== File API Types =====

  interface File extends Blob {
    readonly lastModified: number
    readonly name: string
    readonly webkitRelativePath: string
  }

  interface Blob {
    readonly size: number
    readonly type: string
    arrayBuffer(): Promise<ArrayBuffer>
    slice(start?: number, end?: number, contentType?: string): Blob
    stream(): ReadableStream<Uint8Array>
    text(): Promise<string>
  }

  interface FileList {
    readonly length: number
    item(index: number): File | null
    [index: number]: File
  }

  interface HTMLFormElement extends HTMLElement {
    readonly elements: HTMLFormControlsCollection
    readonly length: number
    acceptCharset: string
    action: string
    autocomplete: string
    enctype: string
    encoding: string
    method: string
    name: string
    noValidate: boolean
    target: string
    checkValidity(): boolean
    reportValidity(): boolean
    requestSubmit(submitter?: HTMLElement): void
    reset(): void
    submit(): void
  }

  interface HTMLFormControlsCollection {
    readonly length: number
    item(index: number): Element | null
    namedItem(name: string): RadioNodeList | Element | null
    [index: number]: Element
  }

  interface RadioNodeList {
    readonly length: number
    value: string
    item(index: number): Node | null
    [index: number]: Node
  }

  interface ValidityState {
    readonly badInput: boolean
    readonly customError: boolean
    readonly patternMismatch: boolean
    readonly rangeOverflow: boolean
    readonly rangeUnderflow: boolean
    readonly stepMismatch: boolean
    readonly tooLong: boolean
    readonly tooShort: boolean
    readonly typeMismatch: boolean
    readonly valid: boolean
    readonly valueMissing: boolean
  }

  // Global Event Handlers interface (used by HTMLElement and other interfaces)
  interface GlobalEventHandlers {
    onabort: ((this: GlobalEventHandlers, ev: UIEvent) => any) | null
    onanimationcancel: ((this: GlobalEventHandlers, ev: AnimationEvent) => any) | null
    onanimationend: ((this: GlobalEventHandlers, ev: AnimationEvent) => any) | null
    onanimationiteration: ((this: GlobalEventHandlers, ev: AnimationEvent) => any) | null
    onanimationstart: ((this: GlobalEventHandlers, ev: AnimationEvent) => any) | null
    onauxclick: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onblur: ((this: GlobalEventHandlers, ev: FocusEvent) => any) | null
    oncancel: ((this: GlobalEventHandlers, ev: Event) => any) | null
    oncanplay: ((this: GlobalEventHandlers, ev: Event) => any) | null
    oncanplaythrough: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onchange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onclick: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onclose: ((this: GlobalEventHandlers, ev: Event) => any) | null
    oncontextmenu: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    oncopy: ((this: GlobalEventHandlers, ev: ClipboardEvent) => any) | null
    oncuechange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    oncut: ((this: GlobalEventHandlers, ev: ClipboardEvent) => any) | null
    ondblclick: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    ondrag: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondragend: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondragenter: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondragleave: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondragover: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondragstart: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondrop: ((this: GlobalEventHandlers, ev: DragEvent) => any) | null
    ondurationchange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onemptied: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onended: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onerror: ((this: GlobalEventHandlers, ev: ErrorEvent) => any) | null
    onfocus: ((this: GlobalEventHandlers, ev: FocusEvent) => any) | null
    onformdata: ((this: GlobalEventHandlers, ev: FormDataEvent) => any) | null
    ongotpointercapture: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    oninput: ((this: GlobalEventHandlers, ev: Event) => any) | null
    oninvalid: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onkeydown: ((this: GlobalEventHandlers, ev: KeyboardEvent) => any) | null
    onkeypress: ((this: GlobalEventHandlers, ev: KeyboardEvent) => any) | null
    onkeyup: ((this: GlobalEventHandlers, ev: KeyboardEvent) => any) | null
    onload: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onloadeddata: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onloadedmetadata: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onloadstart: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onlostpointercapture: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onmousedown: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onmouseenter: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onmouseleave: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onmousemove: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onmouseout: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onmouseover: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onmouseup: ((this: GlobalEventHandlers, ev: MouseEvent) => any) | null
    onpaste: ((this: GlobalEventHandlers, ev: ClipboardEvent) => any) | null
    onpause: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onplay: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onplaying: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onpointercancel: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointerdown: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointerenter: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointerleave: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointermove: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointerout: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointerover: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onpointerup: ((this: GlobalEventHandlers, ev: PointerEvent) => any) | null
    onprogress: ((this: GlobalEventHandlers, ev: ProgressEvent) => any) | null
    onratechange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onreset: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onresize: ((this: GlobalEventHandlers, ev: UIEvent) => any) | null
    onscroll: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onsecuritypolicyviolation: ((this: GlobalEventHandlers, ev: SecurityPolicyViolationEvent) => any) | null
    onseeked: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onseeking: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onselect: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onselectionchange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onselectstart: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onslotchange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onstalled: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onsubmit: ((this: GlobalEventHandlers, ev: SubmitEvent) => any) | null
    onsuspend: ((this: GlobalEventHandlers, ev: Event) => any) | null
    ontimeupdate: ((this: GlobalEventHandlers, ev: Event) => any) | null
    ontoggle: ((this: GlobalEventHandlers, ev: Event) => any) | null
    ontouchcancel?: ((this: GlobalEventHandlers, ev: TouchEvent) => any) | null
    ontouchend?: ((this: GlobalEventHandlers, ev: TouchEvent) => any) | null
    ontouchmove?: ((this: GlobalEventHandlers, ev: TouchEvent) => any) | null
    ontouchstart?: ((this: GlobalEventHandlers, ev: TouchEvent) => any) | null
    ontransitioncancel: ((this: GlobalEventHandlers, ev: TransitionEvent) => any) | null
    ontransitionend: ((this: GlobalEventHandlers, ev: TransitionEvent) => any) | null
    ontransitionrun: ((this: GlobalEventHandlers, ev: TransitionEvent) => any) | null
    ontransitionstart: ((this: GlobalEventHandlers, ev: TransitionEvent) => any) | null
    onvolumechange: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onwaiting: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onwebkitanimationend: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onwebkitanimationiteration: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onwebkitanimationstart: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onwebkittransitionend: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onwheel: ((this: GlobalEventHandlers, ev: WheelEvent) => any) | null
  }

  // DOM Event Types
  interface ErrorEvent extends Event {
    readonly colno: number
    readonly error: any
    readonly filename: string
    readonly lineno: number
    readonly message: string
  }

  interface PromiseRejectionEvent extends Event {
    readonly promise: Promise<any>
    readonly reason: any
  }

  // HTML Element Types
  interface HTMLInputElement extends HTMLElement {
    accept: string
    alt: string
    autocomplete: string
    checked: boolean
    defaultChecked: boolean
    defaultValue: string
    disabled: boolean
    files: FileList | null
    form: HTMLFormElement | null
    formAction: string
    formEnctype: string
    formMethod: string
    formNoValidate: boolean
    formTarget: string
    height: number
    indeterminate: boolean
    list: HTMLElement | null
    max: string
    maxLength: number
    min: string
    minLength: number
    multiple: boolean
    name: string
    pattern: string
    placeholder: string
    readOnly: boolean
    required: boolean
    selectionDirection: string | null
    selectionEnd: number | null
    selectionStart: number | null
    size: number
    src: string
    step: string
    type: string
    useMap: string
    validationMessage: string
    validity: ValidityState
    value: string
    valueAsDate: Date | null
    valueAsNumber: number
    width: number
    willValidate: boolean
    checkValidity(): boolean
    reportValidity(): boolean
    select(): void
    setCustomValidity(error: string): void
    setRangeText(replacement: string): void
    setSelectionRange(start: number, end: number, direction?: string): void
    stepDown(n?: number): void
    stepUp(n?: number): void
  }

  interface HTMLDivElement extends HTMLElement {
    align: string
  }

  interface HTMLImageElement extends HTMLElement {
    alt: string
    complete: boolean
    crossOrigin: string | null
    currentSrc: string
    height: number
    isMap: boolean
    loading: string
    naturalHeight: number
    naturalWidth: number
    referrerPolicy: string
    sizes: string
    src: string
    srcset: string
    useMap: string
    width: number
    decode(): Promise<void>
  }

  interface HTMLCanvasElement extends HTMLElement {
    height: number
    width: number
    getContext(contextId: "2d", options?: CanvasRenderingContext2DSettings): CanvasRenderingContext2D | null
    getContext(contextId: "webgl", options?: WebGLContextAttributes): WebGLRenderingContext | null
    getContext(contextId: "webgl2", options?: WebGLContextAttributes): WebGL2RenderingContext | null
    getContext(contextId: string, options?: any): RenderingContext | null
    toBlob(callback: BlobCallback | null, type?: string, quality?: any): void
    toDataURL(type?: string, quality?: any): string
    transferControlToOffscreen(): OffscreenCanvas
  }

  interface HTMLVideoElement extends HTMLElement {
    autoplay: boolean
    controls: boolean
    crossOrigin: string | null
    currentSrc: string
    currentTime: number
    defaultMuted: boolean
    defaultPlaybackRate: number
    duration: number
    ended: boolean
    height: number
    loop: boolean
    muted: boolean
    networkState: number
    paused: boolean
    playbackRate: number
    played: TimeRanges
    poster: string
    preload: string
    readyState: number
    seekable: TimeRanges
    seeking: boolean
    src: string
    srcObject: MediaStream | MediaSource | Blob | null
    videoHeight: number
    videoWidth: number
    volume: number
    width: number
    canPlayType(type: string): CanPlayTypeResult
    load(): void
    pause(): void
    play(): Promise<void>
  }

  interface HTMLScriptElement extends HTMLElement {
    async: boolean
    charset: string
    crossOrigin: string | null
    defer: boolean
    event: string
    htmlFor: string
    integrity: string
    noModule: boolean
    referrerPolicy: string
    src: string
    text: string
    type: string
  }

  interface HTMLHeadElement extends HTMLElement {
    // HTMLHeadElement interface - extends HTMLElement for <head> elements
    readonly tagName: "HEAD"
  }

  interface HTMLSlotElement extends HTMLElement {
    name: string
    assign(...nodes: (Element | Text)[]): void
    assignedElements(options?: AssignedNodesOptions): Element[]
    assignedNodes(options?: AssignedNodesOptions): Node[]
  }

  // SVG Types
  interface SVGAnimatedString {
    readonly animVal: string
    baseVal: string
  }

  interface SVGAnimatedLength {
    readonly animVal: SVGLength
    readonly baseVal: SVGLength
  }

  interface SVGAnimatedPreserveAspectRatio {
    readonly animVal: SVGPreserveAspectRatio
    readonly baseVal: SVGPreserveAspectRatio
  }

  interface SVGLength {
    readonly unitType: number
    value: number
    valueAsString: string
    valueInSpecifiedUnits: number
    convertToSpecifiedUnits(unitType: number): void
    newValueSpecifiedUnits(unitType: number, valueInSpecifiedUnits: number): void
  }

  interface SVGPreserveAspectRatio {
    align: number
    meetOrSlice: number
  }

  interface SVGAngle {
    readonly unitType: number
    value: number
    valueAsString: string
    valueInSpecifiedUnits: number
  }

  interface SVGNumber {
    value: number
  }

  interface SVGTransform {
    readonly angle: number
    readonly matrix: DOMMatrix
    readonly type: number
    setMatrix(matrix: DOMMatrix2DInit): void
    setRotate(angle: number, cx: number, cy: number): void
    setScale(sx: number, sy: number): void
    setSkewX(angle: number): void
    setSkewY(angle: number): void
    setTranslate(tx: number, ty: number): void
  }

  interface SVGElement extends Element {
    readonly className: SVGAnimatedString
    readonly ownerSVGElement: SVGSVGElement | null
    readonly viewportElement: SVGElement | null
  }

  interface SVGImageElement extends SVGElement {
    readonly height: SVGAnimatedLength
    readonly preserveAspectRatio: SVGAnimatedPreserveAspectRatio
    readonly width: SVGAnimatedLength
    readonly x: SVGAnimatedLength
    readonly y: SVGAnimatedLength
    crossOrigin: string | null
    href: SVGAnimatedString
  }

  interface SVGScriptElement extends SVGElement {
    crossOrigin: string | null
    href: SVGAnimatedString
    type: string
  }

  interface SVGSVGElement extends SVGElement {
    readonly currentScale: number
    readonly currentTranslate: DOMPointReadOnly
    readonly height: SVGAnimatedLength
    readonly width: SVGAnimatedLength
    readonly x: SVGAnimatedLength
    readonly y: SVGAnimatedLength
    animationsPaused(): boolean
    checkEnclosure(element: SVGElement, rect: DOMRectReadOnly): boolean
    checkIntersection(element: SVGElement, rect: DOMRectReadOnly): boolean
    createSVGAngle(): SVGAngle
    createSVGLength(): SVGLength
    createSVGMatrix(): DOMMatrix
    createSVGNumber(): SVGNumber
    createSVGPoint(): DOMPoint
    createSVGRect(): DOMRect
    createSVGTransform(): SVGTransform
    createSVGTransformFromMatrix(matrix?: DOMMatrix2DInit): SVGTransform
    deselectAll(): void
    forceRedraw(): void
    getCurrentTime(): number
    getElementById(elementId: string): Element
    getEnclosureList(rect: DOMRectReadOnly, referenceElement: SVGElement | null): NodeList
    getIntersectionList(rect: DOMRectReadOnly, referenceElement: SVGElement | null): NodeList
    pauseAnimations(): void
    setCurrentTime(seconds: number): void
    suspendRedraw(maxWait: number): number
    unpauseAnimations(): void
    unsuspendRedraw(suspendHandleID: number): void
    unsuspendRedrawAll(): void
  }

  // Image API
  class Image {
    constructor(width?: number, height?: number)
    alt: string
    complete: boolean
    crossOrigin: string | null
    currentSrc: string
    height: number
    isMap: boolean
    naturalHeight: number
    naturalWidth: number
    referrerPolicy: string
    sizes: string
    src: string
    srcset: string
    useMap: string
    width: number
    decode(): Promise<void>
    onload: ((this: GlobalEventHandlers, ev: Event) => any) | null
    onerror: ((this: GlobalEventHandlers, ev: string | Event) => any) | null
  }

  // Event Listener Type
  interface EventListener {
    (evt: Event): void
  }

  interface EventListenerObject {
    handleEvent(object: Event): void
  }

  type EventListenerOrEventListenerObject = EventListener | EventListenerObject

  // Global speech synthesis
  const speechSynthesis: SpeechSynthesis

  // Constructor for SpeechSynthesisUtterance
  const SpeechSynthesisUtterance: {
    prototype: SpeechSynthesisUtterance
    new(text?: string): SpeechSynthesisUtterance
  }

  // ===== أحداث إضافية =====

  interface AnimationEvent extends Event {
    readonly animationName: string
    readonly elapsedTime: number
    readonly pseudoElement: string
  }

  interface FocusEvent extends UIEvent {
    readonly relatedTarget: EventTarget | null
  }

  interface ClipboardEvent extends Event {
    readonly clipboardData: DataTransfer | null
  }

  interface DragEvent extends MouseEvent {
    readonly dataTransfer: DataTransfer | null
  }

  interface FormDataEvent extends Event {
    readonly formData: FormData
  }

  interface PointerEvent extends MouseEvent {
    readonly pointerId: number
    readonly width: number
    readonly height: number
    readonly pressure: number
    readonly tangentialPressure: number
    readonly tiltX: number
    readonly tiltY: number
    readonly twist: number
    readonly pointerType: string
    readonly isPrimary: boolean
  }

  interface ProgressEvent extends Event {
    readonly lengthComputable: boolean
    readonly loaded: number
    readonly total: number
  }

  interface SecurityPolicyViolationEvent extends Event {
    readonly blockedURI: string
    readonly columnNumber: number
    readonly documentURI: string
    readonly effectiveDirective: string
    readonly lineNumber: number
    readonly originalPolicy: string
    readonly referrer: string
    readonly sample: string
    readonly sourceFile: string
    readonly statusCode: number
    readonly violatedDirective: string
  }

  interface SubmitEvent extends Event {
    readonly submitter: HTMLElement | null
  }

  interface TouchEvent extends UIEvent {
    readonly altKey: boolean
    readonly changedTouches: TouchList
    readonly ctrlKey: boolean
    readonly metaKey: boolean
    readonly shiftKey: boolean
    readonly targetTouches: TouchList
    readonly touches: TouchList
  }

  interface TouchList {
    readonly length: number
    item(index: number): Touch | null
    [index: number]: Touch
  }

  interface Touch {
    readonly clientX: number
    readonly clientY: number
    readonly force: number
    readonly identifier: number
    readonly pageX: number
    readonly pageY: number
    readonly radiusX: number
    readonly radiusY: number
    readonly rotationAngle: number
    readonly screenX: number
    readonly screenY: number
    readonly target: EventTarget
  }

  interface TransitionEvent extends Event {
    readonly elapsedTime: number
    readonly propertyName: string
    readonly pseudoElement: string
  }

  interface WheelEvent extends MouseEvent {
    readonly deltaMode: number
    readonly deltaX: number
    readonly deltaY: number
    readonly deltaZ: number
  }

  interface DataTransfer {
    dropEffect: string
    effectAllowed: string
    readonly files: FileList
    readonly items: DataTransferItemList
    readonly types: readonly string[]
    clearData(format?: string): void
    getData(format: string): string
    setData(format: string, data: string): void
    setDragImage(image: Element, x: number, y: number): void
  }

  interface DataTransferItemList {
    readonly length: number
    add(data: string, type: string): DataTransferItem | null
    add(data: File): DataTransferItem | null
    clear(): void
    remove(index: number): void
    [index: number]: DataTransferItem
  }

  interface DataTransferItem {
    readonly kind: string
    readonly type: string
    getAsFile(): File | null
    getAsString(callback: FunctionStringCallback | null): void
    webkitGetAsEntry(): FileSystemEntry | null
  }

  interface FunctionStringCallback {
    (data: string): void
  }

  interface FileSystemEntry {
    readonly filesystem: FileSystem
    readonly fullPath: string
    readonly isDirectory: boolean
    readonly isFile: boolean
    readonly name: string
  }

  interface FileSystem {
    readonly name: string
    readonly root: FileSystemDirectoryEntry
  }

  interface FileSystemDirectoryEntry extends FileSystemEntry {
    createReader(): FileSystemDirectoryReader
    getDirectory(path?: string, options?: FileSystemFlags, successCallback?: FileSystemEntryCallback, errorCallback?: ErrorCallback): void
    getFile(path?: string, options?: FileSystemFlags, successCallback?: FileSystemEntryCallback, errorCallback?: ErrorCallback): void
  }

  interface FileSystemDirectoryReader {
    readEntries(successCallback: FileSystemEntriesCallback, errorCallback?: ErrorCallback): void
  }

  interface FileSystemFlags {
    create?: boolean
    exclusive?: boolean
  }

  interface FileSystemEntryCallback {
    (entry: FileSystemEntry): void
  }

  interface FileSystemEntriesCallback {
    (entries: FileSystemEntry[]): void
  }

  interface ErrorCallback {
    (err: DOMException): void
  }

  // Navigator API
  interface Navigator {
    readonly userAgent: string
    readonly language: string
    readonly languages: readonly string[]
    readonly platform: string
    readonly cookieEnabled: boolean
    readonly onLine: boolean
    readonly hardwareConcurrency: number
    readonly maxTouchPoints: number
    readonly serviceWorker: ServiceWorkerContainer
    readonly geolocation: Geolocation
    readonly mediaDevices: MediaDevices
    readonly permissions: Permissions
    readonly clipboard: Clipboard
    readonly credentials: CredentialsContainer
    readonly storage: StorageManager
    readonly connection: NetworkInformation
    readonly deviceMemory: number
    readonly webdriver: boolean
    getBattery(): Promise<BatteryManager>
    getGamepads(): (Gamepad | null)[]
    getUserMedia(constraints: MediaStreamConstraints, successCallback: NavigatorUserMediaSuccessCallback, errorCallback: NavigatorUserMediaErrorCallback): void
    registerProtocolHandler(scheme: string, url: string, title: string): void
    requestMediaKeySystemAccess(keySystem: string, supportedConfigurations: MediaKeySystemConfiguration[]): Promise<MediaKeySystemAccess>
    sendBeacon(url: string, data?: BodyInit | null): boolean
    share(data?: ShareData): Promise<void>
    vibrate(pattern: VibratePattern): boolean
  }

  // Placeholder interfaces for Navigator dependencies
  interface Permissions {
    query(permissionDesc: PermissionDescriptor): Promise<PermissionStatus>
  }

  interface PermissionDescriptor {
    name: string
  }

  interface PermissionStatus extends EventTarget {
    readonly name: string
    readonly state: PermissionState
    onchange: ((this: PermissionStatus, ev: Event) => any) | null
  }

  type PermissionState = "granted" | "denied" | "prompt"

  interface Clipboard extends EventTarget {
    read(): Promise<ClipboardItems>
    readText(): Promise<string>
    write(data: ClipboardItems): Promise<void>
    writeText(data: string): Promise<void>
  }

  type ClipboardItems = ClipboardItem[]

  interface ClipboardItem {
    readonly types: readonly string[]
    getType(type: string): Promise<Blob>
  }

  interface CredentialsContainer {
    create(options?: CredentialCreationOptions): Promise<Credential | null>
    get(options?: CredentialRequestOptions): Promise<Credential | null>
    preventSilentAccess(): Promise<void>
    store(credential: Credential): Promise<Credential>
  }

  interface Credential {
    readonly id: string
    readonly type: string
  }

  interface CredentialCreationOptions {
    publicKey?: PublicKeyCredentialCreationOptions
    signal?: AbortSignal
  }

  interface CredentialRequestOptions {
    mediation?: CredentialMediationRequirement
    publicKey?: PublicKeyCredentialRequestOptions
    signal?: AbortSignal
  }

  type CredentialMediationRequirement = "silent" | "optional" | "conditional" | "required"

  interface PublicKeyCredentialCreationOptions {
    challenge: BufferSource
    rp: PublicKeyCredentialRpEntity
    user: PublicKeyCredentialUserEntity
    pubKeyCredParams: PublicKeyCredentialParameters[]
    authenticatorSelection?: AuthenticatorSelectionCriteria
    timeout?: number
    excludeCredentials?: PublicKeyCredentialDescriptor[]
    extensions?: AuthenticationExtensionsClientInputs
    attestation?: AttestationConveyancePreference
  }

  interface PublicKeyCredentialRequestOptions {
    challenge: BufferSource
    timeout?: number
    rpId?: string
    allowCredentials?: PublicKeyCredentialDescriptor[]
    userVerification?: UserVerificationRequirement
    extensions?: AuthenticationExtensionsClientInputs
  }

  interface StorageManager {
    estimate(): Promise<StorageEstimate>
    getDirectory(): Promise<FileSystemDirectoryHandle>
    persist(): Promise<boolean>
    persisted(): Promise<boolean>
  }

  interface StorageEstimate {
    quota?: number
    usage?: number
    usageDetails?: Record<string, number>
  }

  interface FileSystemDirectoryHandle extends FileSystemHandle {
    readonly kind: "directory"
    entries(): AsyncIterableIterator<[string, FileSystemHandle]>
    getDirectoryHandle(name: string, options?: FileSystemGetDirectoryOptions): Promise<FileSystemDirectoryHandle>
    getFileHandle(name: string, options?: FileSystemGetFileOptions): Promise<FileSystemFileHandle>
    removeEntry(name: string, options?: FileSystemRemoveOptions): Promise<void>
    resolve(possibleDescendant: FileSystemHandle): Promise<string[] | null>
  }

  interface FileSystemHandle {
    readonly kind: FileSystemHandleKind
    readonly name: string
    isSameEntry(other: FileSystemHandle): Promise<boolean>
    queryPermission(descriptor?: FileSystemPermissionDescriptor): Promise<PermissionState>
    requestPermission(descriptor?: FileSystemPermissionDescriptor): Promise<PermissionState>
  }

  type FileSystemHandleKind = "file" | "directory"

  interface FileSystemFileHandle extends FileSystemHandle {
    readonly kind: "file"
    createWritable(options?: FileSystemCreateWritableOptions): Promise<FileSystemWritableFileStream>
    getFile(): Promise<File>
  }

  interface NetworkInformation extends EventTarget {
    readonly downlink: number
    readonly downlinkMax: number
    readonly effectiveType: string
    readonly rtt: number
    readonly saveData: boolean
    readonly type: string
    onchange: ((this: NetworkInformation, ev: Event) => any) | null
  }

  type BodyInit = ReadableStream | XMLHttpRequestBodyInit
  type XMLHttpRequestBodyInit = Blob | BufferSource | FormData | URLSearchParams | string

  // Additional missing types
  interface Gamepad {
    readonly axes: readonly number[]
    readonly buttons: readonly GamepadButton[]
    readonly connected: boolean
    readonly id: string
    readonly index: number
    readonly mapping: GamepadMappingType
    readonly timestamp: number
  }

  interface GamepadButton {
    readonly pressed: boolean
    readonly touched: boolean
    readonly value: number
  }

  type GamepadMappingType = "" | "standard"

  interface MediaStreamConstraints {
    audio?: boolean | MediaTrackConstraints
    video?: boolean | MediaTrackConstraints
  }

  interface MediaTrackConstraints {
    aspectRatio?: ConstrainDouble
    autoGainControl?: ConstrainBoolean
    channelCount?: ConstrainULong
    deviceId?: ConstrainDOMString
    echoCancellation?: ConstrainBoolean
    facingMode?: ConstrainDOMString
    frameRate?: ConstrainDouble
    groupId?: ConstrainDOMString
    height?: ConstrainULong
    latency?: ConstrainDouble
    noiseSuppression?: ConstrainBoolean
    sampleRate?: ConstrainULong
    sampleSize?: ConstrainULong
    width?: ConstrainULong
  }

  type ConstrainDouble = number | ConstrainDoubleRange
  type ConstrainBoolean = boolean | ConstrainBooleanParameters
  type ConstrainULong = number | ConstrainULongRange
  type ConstrainDOMString = string | string[] | ConstrainDOMStringParameters

  interface ConstrainDoubleRange {
    exact?: number
    ideal?: number
    max?: number
    min?: number
  }

  interface ConstrainBooleanParameters {
    exact?: boolean
    ideal?: boolean
  }

  interface ConstrainULongRange {
    exact?: number
    ideal?: number
    max?: number
    min?: number
  }

  interface ConstrainDOMStringParameters {
    exact?: string | string[]
    ideal?: string | string[]
  }

  interface MediaKeySystemConfiguration {
    audioCapabilities?: MediaKeySystemMediaCapability[]
    distinctiveIdentifier?: MediaKeysRequirement
    initDataTypes?: string[]
    label?: string
    persistentState?: MediaKeysRequirement
    sessionTypes?: string[]
    videoCapabilities?: MediaKeySystemMediaCapability[]
  }

  interface MediaKeySystemMediaCapability {
    contentType?: string
    encryptionScheme?: string | null
    robustness?: string
  }

  type MediaKeysRequirement = "required" | "optional" | "not-allowed"

  interface MediaKeySystemAccess {
    readonly keySystem: string
    createMediaKeys(): Promise<MediaKeys>
    getConfiguration(): MediaKeySystemConfiguration
  }

  interface MediaKeys {
    createSession(sessionType?: MediaKeySessionType): MediaKeySession
    setServerCertificate(serverCertificate: BufferSource): Promise<boolean>
  }

  type MediaKeySessionType = "temporary" | "persistent-license"

  interface MediaKeySession extends EventTarget {
    readonly closed: Promise<MediaKeySessionClosedReason>
    readonly expiration: number
    readonly keyStatuses: MediaKeyStatusMap
    readonly sessionId: string
    close(): Promise<void>
    generateRequest(initDataType: string, initData: BufferSource): Promise<void>
    load(sessionId: string): Promise<boolean>
    remove(): Promise<void>
    update(response: BufferSource): Promise<void>
  }

  type MediaKeySessionClosedReason = "internal-error" | "closed-by-application" | "release-acknowledged" | "hardware-context-reset" | "resource-evicted"

  interface MediaKeyStatusMap {
    readonly size: number
    get(keyId: BufferSource): MediaKeyStatus | undefined
    has(keyId: BufferSource): boolean
    forEach(callbackfn: (value: MediaKeyStatus, key: BufferSource, parent: MediaKeyStatusMap) => void, thisArg?: any): void
  }

  type MediaKeyStatus = "usable" | "expired" | "released" | "output-restricted" | "output-downscaled" | "usable-in-future" | "status-pending" | "internal-error"

  type NavigatorUserMediaSuccessCallback = (stream: MediaStream) => void
  type NavigatorUserMediaErrorCallback = (error: MediaStreamError) => void

  interface MediaStream extends EventTarget {
    readonly active: boolean
    readonly id: string
    addTrack(track: MediaStreamTrack): void
    clone(): MediaStream
    getAudioTracks(): MediaStreamTrack[]
    getTrackById(trackId: string): MediaStreamTrack | null
    getTracks(): MediaStreamTrack[]
    getVideoTracks(): MediaStreamTrack[]
    removeTrack(track: MediaStreamTrack): void
  }

  interface MediaStreamTrack extends EventTarget {
    readonly enabled: boolean
    readonly id: string
    readonly kind: string
    readonly label: string
    readonly muted: boolean
    readonly readyState: MediaStreamTrackState
    applyConstraints(constraints?: MediaTrackConstraints): Promise<void>
    clone(): MediaStreamTrack
    getCapabilities(): MediaTrackCapabilities
    getConstraints(): MediaTrackConstraints
    getSettings(): MediaTrackSettings
    stop(): void
  }

  type MediaStreamTrackState = "live" | "ended"

  interface MediaTrackCapabilities {
    aspectRatio?: DoubleRange
    autoGainControl?: boolean[]
    channelCount?: ULongRange
    deviceId?: string
    echoCancellation?: boolean[]
    facingMode?: string[]
    frameRate?: DoubleRange
    groupId?: string
    height?: ULongRange
    latency?: DoubleRange
    noiseSuppression?: boolean[]
    sampleRate?: ULongRange
    sampleSize?: ULongRange
    width?: ULongRange
  }

  interface MediaTrackSettings {
    aspectRatio?: number
    autoGainControl?: boolean
    channelCount?: number
    deviceId?: string
    echoCancellation?: boolean
    facingMode?: string
    frameRate?: number
    groupId?: string
    height?: number
    latency?: number
    noiseSuppression?: boolean
    sampleRate?: number
    sampleSize?: number
    width?: number
  }

  interface DoubleRange {
    max?: number
    min?: number
  }

  interface ULongRange {
    max?: number
    min?: number
  }

  interface MediaStreamError {
    readonly constraintName: string | null
    readonly message: string | null
    readonly name: string
  }

  interface MediaDeviceInfo {
    readonly deviceId: string
    readonly groupId: string
    readonly kind: MediaDeviceKind
    readonly label: string
    toJSON(): any
  }

  type MediaDeviceKind = "audioinput" | "audiooutput" | "videoinput"

  interface DisplayMediaStreamConstraints {
    audio?: boolean | MediaTrackConstraints
    video?: boolean | MediaTrackConstraints
  }

  interface MediaTrackSupportedConstraints {
    aspectRatio?: boolean
    autoGainControl?: boolean
    channelCount?: boolean
    deviceId?: boolean
    echoCancellation?: boolean
    facingMode?: boolean
    frameRate?: boolean
    groupId?: boolean
    height?: boolean
    latency?: boolean
    noiseSuppression?: boolean
    sampleRate?: boolean
    sampleSize?: boolean
    width?: boolean
  }

  // Additional Navigator types
  interface ServiceWorkerContainer {
    readonly controller: ServiceWorker | null
    readonly ready: Promise<ServiceWorkerRegistration>
    getRegistration(clientURL?: string): Promise<ServiceWorkerRegistration | undefined>
    getRegistrations(): Promise<ServiceWorkerRegistration[]>
    register(scriptURL: string, options?: RegistrationOptions): Promise<ServiceWorkerRegistration>
    startMessages(): void
    addEventListener<K extends keyof ServiceWorkerContainerEventMap>(type: K, listener: (this: ServiceWorkerContainer, ev: ServiceWorkerContainerEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void
    removeEventListener<K extends keyof ServiceWorkerContainerEventMap>(type: K, listener: (this: ServiceWorkerContainer, ev: ServiceWorkerContainerEventMap[K]) => any, options?: boolean | EventListenerOptions): void
  }

  interface ServiceWorkerContainerEventMap {
    "controllerchange": Event
    "message": MessageEvent
    "messageerror": MessageEvent
  }

  interface ServiceWorker extends EventTarget {
    readonly scriptURL: string
    readonly state: ServiceWorkerState
    postMessage(message: any, transfer?: Transferable[]): void
    addEventListener<K extends keyof ServiceWorkerEventMap>(type: K, listener: (this: ServiceWorker, ev: ServiceWorkerEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void
    removeEventListener<K extends keyof ServiceWorkerEventMap>(type: K, listener: (this: ServiceWorker, ev: ServiceWorkerEventMap[K]) => any, options?: boolean | EventListenerOptions): void
  }

  interface ServiceWorkerEventMap {
    "error": ErrorEvent
    "statechange": Event
  }

  type ServiceWorkerState = "installing" | "installed" | "activating" | "activated" | "redundant"

  interface ServiceWorkerRegistration extends EventTarget {
    readonly active: ServiceWorker | null
    readonly installing: ServiceWorker | null
    readonly navigationPreload: NavigationPreloadManager
    readonly pushManager: PushManager
    readonly scope: string
    readonly sync: SyncManager
    readonly updateViaCache: ServiceWorkerUpdateViaCache
    readonly waiting: ServiceWorker | null
    getNotifications(filter?: GetNotificationOptions): Promise<Notification[]>
    showNotification(title: string, options?: NotificationOptions): Promise<void>
    unregister(): Promise<boolean>
    update(): Promise<void>
    addEventListener<K extends keyof ServiceWorkerRegistrationEventMap>(type: K, listener: (this: ServiceWorkerRegistration, ev: ServiceWorkerRegistrationEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void
    removeEventListener<K extends keyof ServiceWorkerRegistrationEventMap>(type: K, listener: (this: ServiceWorkerRegistration, ev: ServiceWorkerRegistrationEventMap[K]) => any, options?: boolean | EventListenerOptions): void
  }

  interface ServiceWorkerRegistrationEventMap {
    "updatefound": Event
  }

  type ServiceWorkerUpdateViaCache = "imports" | "all" | "none"

  // Geolocation API
  interface Geolocation {
    clearWatch(watchId: number): void
    getCurrentPosition(successCallback: PositionCallback, errorCallback?: PositionErrorCallback | null, options?: PositionOptions): void
    watchPosition(successCallback: PositionCallback, errorCallback?: PositionErrorCallback | null, options?: PositionOptions): number
  }

  interface PositionCallback {
    (position: GeolocationPosition): void
  }

  interface PositionErrorCallback {
    (positionError: GeolocationPositionError): void
  }

  interface GeolocationPosition {
    readonly coords: GeolocationCoordinates
    readonly timestamp: number
  }

  interface GeolocationCoordinates {
    readonly accuracy: number
    readonly altitude: number | null
    readonly altitudeAccuracy: number | null
    readonly heading: number | null
    readonly latitude: number
    readonly longitude: number
    readonly speed: number | null
  }

  interface GeolocationPositionError {
    readonly code: number
    readonly message: string
    readonly PERMISSION_DENIED: number
    readonly POSITION_UNAVAILABLE: number
    readonly TIMEOUT: number
  }

  interface PositionOptions {
    enableHighAccuracy?: boolean
    maximumAge?: number
    timeout?: number
  }

  // Media Devices API
  interface MediaDevices extends EventTarget {
    enumerateDevices(): Promise<MediaDeviceInfo[]>
    getDisplayMedia(constraints?: DisplayMediaStreamConstraints): Promise<MediaStream>
    getSupportedConstraints(): MediaTrackSupportedConstraints
    getUserMedia(constraints?: MediaStreamConstraints): Promise<MediaStream>
    addEventListener<K extends keyof MediaDevicesEventMap>(type: K, listener: (this: MediaDevices, ev: MediaDevicesEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void
    removeEventListener<K extends keyof MediaDevicesEventMap>(type: K, listener: (this: MediaDevices, ev: MediaDevicesEventMap[K]) => any, options?: boolean | EventListenerOptions): void
  }

  interface MediaDevicesEventMap {
    "devicechange": Event
  }

  // Additional types for Navigator
  type VibratePattern = number | number[]
  type NavigatorUserMediaSuccessCallback = (stream: MediaStream) => void
  type NavigatorUserMediaErrorCallback = (error: MediaStreamError) => void

  interface ShareData {
    title?: string
    text?: string
    url?: string
  }

  interface BatteryManager extends EventTarget {
    readonly charging: boolean
    readonly chargingTime: number
    readonly dischargingTime: number
    readonly level: number
    addEventListener<K extends keyof BatteryManagerEventMap>(type: K, listener: (this: BatteryManager, ev: BatteryManagerEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void
    removeEventListener<K extends keyof BatteryManagerEventMap>(type: K, listener: (this: BatteryManager, ev: BatteryManagerEventMap[K]) => any, options?: boolean | EventListenerOptions): void
  }

  interface BatteryManagerEventMap {
    "chargingchange": Event
    "chargingtimechange": Event
    "dischargingtimechange": Event
    "levelchange": Event
  }

  // Basic DOM interfaces
  interface Storage {
    readonly length: number
    clear(): void
    getItem(key: string): string | null
    key(index: number): string | null
    removeItem(key: string): void
    setItem(key: string, value: string): void
    [name: string]: any
  }

  interface Location {
    readonly ancestorOrigins: DOMStringList
    hash: string
    host: string
    hostname: string
    href: string
    readonly origin: string
    pathname: string
    port: string
    protocol: string
    search: string
    assign(url: string | URL): void
    reload(): void
    replace(url: string | URL): void
    toString(): string
  }

  interface DOMStringList {
    readonly length: number
    contains(string: string): boolean
    item(index: number): string | null
    [index: number]: string
  }

  interface History {
    readonly length: number
    readonly scrollRestoration: ScrollRestoration
    readonly state: any
    back(): void
    forward(): void
    go(delta?: number): void
    pushState(data: any, unused: string, url?: string | URL | null): void
    replaceState(data: any, unused: string, url?: string | URL | null): void
  }

  type ScrollRestoration = "auto" | "manual"

  interface CustomElementRegistry {
    define(name: string, constructor: CustomElementConstructor, options?: ElementDefinitionOptions): void
    get(name: string): CustomElementConstructor | undefined
    upgrade(root: Node): void
    whenDefined(name: string): Promise<CustomElementConstructor>
  }

  interface CustomElementConstructor {
    new (...params: any[]): HTMLElement
  }

  interface ElementDefinitionOptions {
    extends?: string
  }

  interface External {
    AddSearchProvider(): void
    IsSearchProviderInstalled(): void
  }

  interface Screen {
    readonly availHeight: number
    readonly availLeft: number
    readonly availTop: number
    readonly availWidth: number
    readonly colorDepth: number
    readonly height: number
    readonly left: number
    readonly orientation: ScreenOrientation
    readonly pixelDepth: number
    readonly top: number
    readonly width: number
  }

  interface ScreenOrientation extends EventTarget {
    readonly angle: number
    readonly type: OrientationType
    lock(orientation: OrientationLockType): Promise<void>
    unlock(): void
  }

  type OrientationType = "landscape-primary" | "landscape-secondary" | "portrait-primary" | "portrait-secondary"
  type OrientationLockType = "any" | "landscape" | "landscape-primary" | "landscape-secondary" | "natural" | "portrait" | "portrait-primary" | "portrait-secondary"

  interface Crypto {
    readonly subtle: SubtleCrypto
    getRandomValues<T extends ArrayBufferView | null>(array: T): T
    randomUUID(): string
  }

  interface SubtleCrypto {
    decrypt(algorithm: AlgorithmIdentifier | RsaOaepParams | AesCtrParams | AesCbcParams | AesGcmParams, key: CryptoKey, data: BufferSource): Promise<ArrayBuffer>
    deriveBits(algorithm: AlgorithmIdentifier | EcdhKeyDeriveParams | HkdfParams | Pbkdf2Params, baseKey: CryptoKey, length: number): Promise<ArrayBuffer>
    deriveKey(algorithm: AlgorithmIdentifier | EcdhKeyDeriveParams | HkdfParams | Pbkdf2Params, baseKey: CryptoKey, derivedKeyType: AlgorithmIdentifier | AesDerivedKeyParams | HmacImportParams | HkdfParams | Pbkdf2Params, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKey>
    digest(algorithm: AlgorithmIdentifier, data: BufferSource): Promise<ArrayBuffer>
    encrypt(algorithm: AlgorithmIdentifier | RsaOaepParams | AesCtrParams | AesCbcParams | AesGcmParams, key: CryptoKey, data: BufferSource): Promise<ArrayBuffer>
    exportKey(format: "jwk", key: CryptoKey): Promise<JsonWebKey>
    exportKey(format: Exclude<KeyFormat, "jwk">, key: CryptoKey): Promise<ArrayBuffer>
    generateKey(algorithm: RsaHashedKeyGenParams | EcKeyGenParams, extractable: boolean, keyUsages: ReadonlyArray<KeyUsage>): Promise<CryptoKeyPair>
    generateKey(algorithm: AesKeyGenParams | HmacKeyGenParams | Pbkdf2Params, extractable: boolean, keyUsages: ReadonlyArray<KeyUsage>): Promise<CryptoKey>
    generateKey(algorithm: AlgorithmIdentifier, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKeyPair | CryptoKey>
    importKey(format: "jwk", keyData: JsonWebKey, algorithm: AlgorithmIdentifier | RsaHashedImportParams | EcKeyImportParams | HmacImportParams | AesKeyAlgorithm, extractable: boolean, keyUsages: ReadonlyArray<KeyUsage>): Promise<CryptoKey>
    importKey(format: Exclude<KeyFormat, "jwk">, keyData: BufferSource, algorithm: AlgorithmIdentifier | RsaHashedImportParams | EcKeyImportParams | HmacImportParams | AesKeyAlgorithm, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKey>
    sign(algorithm: AlgorithmIdentifier | RsaPssParams | EcdsaParams, key: CryptoKey, data: BufferSource): Promise<ArrayBuffer>
    unwrapKey(format: KeyFormat, wrappedKey: BufferSource, unwrappingKey: CryptoKey, unwrapAlgorithm: AlgorithmIdentifier | RsaOaepParams | AesCtrParams | AesCbcParams | AesGcmParams, unwrappedKeyAlgorithm: AlgorithmIdentifier | RsaHashedImportParams | EcKeyImportParams | HmacImportParams | AesKeyAlgorithm, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKey>
    verify(algorithm: AlgorithmIdentifier | RsaPssParams | EcdsaParams, key: CryptoKey, signature: BufferSource, data: BufferSource): Promise<boolean>
    wrapKey(format: KeyFormat, key: CryptoKey, wrappingKey: CryptoKey, wrapAlgorithm: AlgorithmIdentifier | RsaOaepParams | AesCtrParams | AesCbcParams | AesGcmParams): Promise<ArrayBuffer>
  }

  interface IDBFactory {
    cmp(first: any, second: any): number
    deleteDatabase(name: string): IDBOpenDBRequest
    open(name: string, version?: number): IDBOpenDBRequest
  }

  interface IDBOpenDBRequest extends IDBRequest<IDBDatabase> {
    onblocked: ((this: IDBOpenDBRequest, ev: IDBVersionChangeEvent) => any) | null
    onupgradeneeded: ((this: IDBOpenDBRequest, ev: IDBVersionChangeEvent) => any) | null
  }

  interface IDBRequest<T = any> extends EventTarget {
    readonly error: DOMException | null
    onerror: ((this: IDBRequest<T>, ev: Event) => any) | null
    onsuccess: ((this: IDBRequest<T>, ev: Event) => any) | null
    readonly readyState: IDBRequestReadyState
    readonly result: T
    readonly source: IDBObjectStore | IDBIndex | IDBCursor
    readonly transaction: IDBTransaction | null
  }

  type IDBRequestReadyState = "done" | "pending"

  interface IDBDatabase extends EventTarget {
    readonly name: string
    readonly objectStoreNames: DOMStringList
    onabort: ((this: IDBDatabase, ev: Event) => any) | null
    onclose: ((this: IDBDatabase, ev: Event) => any) | null
    onerror: ((this: IDBDatabase, ev: Event) => any) | null
    onversionchange: ((this: IDBDatabase, ev: IDBVersionChangeEvent) => any) | null
    readonly version: number
    close(): void
    createObjectStore(name: string, options?: IDBObjectStoreParameters): IDBObjectStore
    deleteObjectStore(name: string): void
    transaction(storeNames: string | string[], mode?: IDBTransactionMode, options?: IDBTransactionOptions): IDBTransaction
  }

  // IndexedDB related interfaces
  interface IDBObjectStore {
    readonly autoIncrement: boolean
    readonly indexNames: DOMStringList
    readonly keyPath: string | string[]
    readonly name: string
    readonly transaction: IDBTransaction
    add(value: any, key?: IDBValidKey): IDBRequest<IDBValidKey>
    clear(): IDBRequest<undefined>
    count(query?: IDBValidKey | IDBKeyRange): IDBRequest<number>
    createIndex(name: string, keyPath: string | string[], options?: IDBIndexParameters): IDBIndex
    delete(query: IDBValidKey | IDBKeyRange): IDBRequest<undefined>
    deleteIndex(name: string): void
    get(query: IDBValidKey | IDBKeyRange): IDBRequest<any>
    getAll(query?: IDBValidKey | IDBKeyRange | null, count?: number): IDBRequest<any[]>
    getAllKeys(query?: IDBValidKey | IDBKeyRange | null, count?: number): IDBRequest<IDBValidKey[]>
    getKey(query: IDBValidKey | IDBKeyRange): IDBRequest<IDBValidKey | undefined>
    index(name: string): IDBIndex
    openCursor(query?: IDBValidKey | IDBKeyRange | null, direction?: IDBCursorDirection): IDBRequest<IDBCursorWithValue | null>
    openKeyCursor(query?: IDBValidKey | IDBKeyRange | null, direction?: IDBCursorDirection): IDBRequest<IDBCursor | null>
    put(value: any, key?: IDBValidKey): IDBRequest<IDBValidKey>
  }

  interface IDBIndex {
    readonly keyPath: string | string[]
    readonly multiEntry: boolean
    readonly name: string
    readonly objectStore: IDBObjectStore
    readonly unique: boolean
    count(query?: IDBValidKey | IDBKeyRange): IDBRequest<number>
    get(query: IDBValidKey | IDBKeyRange): IDBRequest<any>
    getAll(query?: IDBValidKey | IDBKeyRange | null, count?: number): IDBRequest<any[]>
    getAllKeys(query?: IDBValidKey | IDBKeyRange | null, count?: number): IDBRequest<IDBValidKey[]>
    getKey(query: IDBValidKey | IDBKeyRange): IDBRequest<IDBValidKey | undefined>
    openCursor(query?: IDBValidKey | IDBKeyRange | null, direction?: IDBCursorDirection): IDBRequest<IDBCursorWithValue | null>
    openKeyCursor(query?: IDBValidKey | IDBKeyRange | null, direction?: IDBCursorDirection): IDBRequest<IDBCursor | null>
  }

  interface IDBCursor {
    readonly direction: IDBCursorDirection
    readonly key: IDBValidKey
    readonly primaryKey: IDBValidKey
    readonly request: IDBRequest
    readonly source: IDBObjectStore | IDBIndex
    advance(count: number): void
    continue(key?: IDBValidKey): void
    continuePrimaryKey(key: IDBValidKey, primaryKey: IDBValidKey): void
    delete(): IDBRequest<undefined>
    update(value: any): IDBRequest<IDBValidKey>
  }

  interface IDBCursorWithValue extends IDBCursor {
    readonly value: any
  }

  interface IDBTransaction extends EventTarget {
    readonly db: IDBDatabase
    readonly durability: IDBTransactionDurability
    readonly error: DOMException | null
    readonly mode: IDBTransactionMode
    readonly objectStoreNames: DOMStringList
    onabort: ((this: IDBTransaction, ev: Event) => any) | null
    oncomplete: ((this: IDBTransaction, ev: Event) => any) | null
    onerror: ((this: IDBTransaction, ev: Event) => any) | null
    abort(): void
    commit(): void
    objectStore(name: string): IDBObjectStore
  }

  // IndexedDB types
  type IDBValidKey = number | string | Date | BufferSource | IDBValidKey[]
  type IDBCursorDirection = "next" | "nextunique" | "prev" | "prevunique"
  type IDBTransactionMode = "readonly" | "readwrite" | "versionchange"
  type IDBTransactionDurability = "default" | "relaxed" | "strict"

  interface IDBObjectStoreParameters {
    autoIncrement?: boolean
    keyPath?: string | string[] | null
  }

  interface IDBIndexParameters {
    multiEntry?: boolean
    unique?: boolean
  }

  interface IDBTransactionOptions {
    durability?: IDBTransactionDurability
  }

  interface IDBKeyRange {
    readonly lower: any
    readonly lowerOpen: boolean
    readonly upper: any
    readonly upperOpen: boolean
    includes(key: any): boolean
  }

  interface IDBVersionChangeEvent extends Event {
    readonly newVersion: number | null
    readonly oldVersion: number
  }

  interface Performance {
    readonly navigation: PerformanceNavigation
    readonly timeOrigin: number
    readonly timing: PerformanceTiming
    clearMarks(markName?: string): void
    clearMeasures(measureName?: string): void
    clearResourceTimings(): void
    getEntries(): PerformanceEntryList
    getEntriesByName(name: string, type?: string): PerformanceEntryList
    getEntriesByType(type: string): PerformanceEntryList
    mark(markName: string, markOptions?: PerformanceMarkOptions): PerformanceMark
    measure(measureName: string, startOrMeasureOptions?: string | PerformanceMeasureOptions, endMark?: string): PerformanceMeasure
    now(): number
    setResourceTimingBufferSize(maxSize: number): void
    toJSON(): any
  }

  interface CacheStorage {
    delete(cacheName: string): Promise<boolean>
    has(cacheName: string): Promise<boolean>
    keys(): Promise<string[]>
    match(request: RequestInfo | URL, options?: MultiCacheQueryOptions): Promise<Response | undefined>
    open(cacheName: string): Promise<Cache>
  }

  interface CSSStyleDeclaration {
    [index: number]: string
    readonly length: number
    readonly parentRule: CSSRule | null
    cssFloat: string
    cssText: string
    getPropertyPriority(property: string): string
    getPropertyValue(property: string): string
    item(index: number): string
    removeProperty(property: string): string
    setProperty(property: string, value: string | null, priority?: string): void
  }

  interface Selection {
    readonly anchorNode: Node | null
    readonly anchorOffset: number
    readonly focusNode: Node | null
    readonly focusOffset: number
    readonly isCollapsed: boolean
    readonly rangeCount: number
    readonly type: string
    addRange(range: Range): void
    collapse(node: Node | null, offset?: number): void
    collapseToEnd(): void
    collapseToStart(): void
    containsNode(node: Node, allowPartialContainment?: boolean): boolean
    deleteFromDocument(): void
    empty(): void
    extend(node: Node, offset?: number): void
    getRangeAt(index: number): Range
    modify(alter?: string, direction?: string, granularity?: string): void
    removeAllRanges(): void
    removeRange(range: Range): void
    selectAllChildren(node: Node): void
    setBaseAndExtent(anchorNode: Node, anchorOffset: number, focusNode: Node, focusOffset: number): void
    setPosition(node: Node | null, offset?: number): void
    toString(): string
  }

  interface MediaQueryList extends EventTarget {
    readonly matches: boolean
    readonly media: string
    onchange: ((this: MediaQueryList, ev: MediaQueryListEvent) => any) | null
  }

  interface MediaQueryListEvent extends Event {
    readonly matches: boolean
    readonly media: string
  }

  // Additional missing interfaces
  interface RegistrationOptions {
    scope?: string
    type?: WorkerType
    updateViaCache?: ServiceWorkerUpdateViaCache
  }

  type WorkerType = "classic" | "module"

  interface AddEventListenerOptions extends EventListenerOptions {
    once?: boolean
    passive?: boolean
    signal?: AbortSignal
  }

  interface EventListenerOptions {
    capture?: boolean
  }

  interface MessageEvent extends Event {
    readonly data: any
    readonly lastEventId: string
    readonly origin: string
    readonly ports: readonly MessagePort[]
    readonly source: MessageEventSource | null
    initMessageEvent(type: string, bubbles?: boolean, cancelable?: boolean, data?: any, origin?: string, lastEventId?: string, source?: MessageEventSource | null, ports?: MessagePort[]): void
  }

  type MessageEventSource = WindowProxy | MessagePort | ServiceWorker

  interface MessagePort extends EventTarget {
    onmessage: ((this: MessagePort, ev: MessageEvent) => any) | null
    onmessageerror: ((this: MessagePort, ev: MessageEvent) => any) | null
    close(): void
    postMessage(message: any, transfer?: Transferable[]): void
    start(): void
  }

  interface WindowProxy {
    readonly window: WindowProxy
    readonly self: WindowProxy
    readonly location: Location
    readonly history: History
    readonly customElements: CustomElementRegistry
    readonly external: External
    readonly screen: Screen
    readonly innerHeight: number
    readonly innerWidth: number
    readonly scrollX: number
    readonly scrollY: number
    readonly pageXOffset: number
    readonly pageYOffset: number
    readonly screenX: number
    readonly screenY: number
    readonly outerHeight: number
    readonly outerWidth: number
    readonly devicePixelRatio: number
    readonly event: Event | undefined
    readonly frameElement: Element | null
    readonly frames: WindowProxy
    readonly length: number
    readonly name: string
    readonly opener: WindowProxy | null
    readonly parent: WindowProxy
    readonly top: WindowProxy | null
    readonly status: string
    readonly closed: boolean
    readonly crypto: Crypto
    readonly indexedDB: IDBFactory
    readonly isSecureContext: boolean
    readonly origin: string
    readonly performance: Performance
    readonly sessionStorage: Storage
    readonly localStorage: Storage
    readonly caches: CacheStorage
    readonly speechSynthesis: SpeechSynthesis
    alert(message?: any): void
    blur(): void
    cancelAnimationFrame(handle: number): void
    cancelIdleCallback(handle: number): void
    captureEvents(): void
    close(): void
    confirm(message?: string): boolean
    focus(): void
    getComputedStyle(elt: Element, pseudoElt?: string | null): CSSStyleDeclaration
    getSelection(): Selection | null
    matchMedia(query: string): MediaQueryList
    moveBy(x: number, y: number): void
    moveTo(x: number, y: number): void
    open(url?: string | URL, target?: string, features?: string): WindowProxy | null
    postMessage(message: any, targetOrigin: string, transfer?: Transferable[]): void
    print(): void
    prompt(message?: string, defaultText?: string): string | null
    releaseEvents(): void
    requestAnimationFrame(callback: FrameRequestCallback): number
    requestIdleCallback(callback: IdleRequestCallback, options?: IdleRequestOptions): number
    resizeBy(x: number, y: number): void
    resizeTo(width: number, height: number): void
    scroll(options?: ScrollToOptions): void
    scroll(x: number, y: number): void
    scrollBy(options?: ScrollToOptions): void
    scrollBy(x: number, y: number): void
    scrollTo(options?: ScrollToOptions): void
    scrollTo(x: number, y: number): void
    stop(): void
    addEventListener<K extends keyof WindowEventMap>(type: K, listener: (this: Window, ev: WindowEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void
    addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void
    removeEventListener<K extends keyof WindowEventMap>(type: K, listener: (this: Window, ev: WindowEventMap[K]) => any, options?: boolean | EventListenerOptions): void
    removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void
    dispatchEvent(event: Event): boolean
  }

  interface WindowEventMap extends GlobalEventHandlersEventMap {
    "DOMContentLoaded": Event
    "afterprint": Event
    "beforeprint": Event
    "beforeunload": BeforeUnloadEvent
    "blur": FocusEvent
    "error": ErrorEvent
    "focus": FocusEvent
    "hashchange": HashChangeEvent
    "languagechange": Event
    "load": Event
    "message": MessageEvent
    "messageerror": MessageEvent
    "offline": Event
    "online": Event
    "pagehide": PageTransitionEvent
    "pageshow": PageTransitionEvent
    "popstate": PopStateEvent
    "rejectionhandled": PromiseRejectionEvent
    "resize": UIEvent
    "storage": StorageEvent
    "unhandledrejection": PromiseRejectionEvent
    "unload": Event
  }

  interface GlobalEventHandlersEventMap {
    "abort": UIEvent
    "animationcancel": AnimationEvent
    "animationend": AnimationEvent
    "animationiteration": AnimationEvent
    "animationstart": AnimationEvent
    "auxclick": MouseEvent
    "blur": FocusEvent
    "cancel": Event
    "canplay": Event
    "canplaythrough": Event
    "change": Event
    "click": MouseEvent
    "close": Event
    "contextmenu": MouseEvent
    "copy": ClipboardEvent
    "cuechange": Event
    "cut": ClipboardEvent
    "dblclick": MouseEvent
    "drag": DragEvent
    "dragend": DragEvent
    "dragenter": DragEvent
    "dragleave": DragEvent
    "dragover": DragEvent
    "dragstart": DragEvent
    "drop": DragEvent
    "durationchange": Event
    "emptied": Event
    "ended": Event
    "error": ErrorEvent
    "focus": FocusEvent
    "formdata": FormDataEvent
    "gotpointercapture": PointerEvent
    "input": Event
    "invalid": Event
    "keydown": KeyboardEvent
    "keypress": KeyboardEvent
    "keyup": KeyboardEvent
    "load": Event
    "loadeddata": Event
    "loadedmetadata": Event
    "loadstart": Event
    "lostpointercapture": PointerEvent
    "mousedown": MouseEvent
    "mouseenter": MouseEvent
    "mouseleave": MouseEvent
    "mousemove": MouseEvent
    "mouseout": MouseEvent
    "mouseover": MouseEvent
    "mouseup": MouseEvent
    "paste": ClipboardEvent
    "pause": Event
    "play": Event
    "playing": Event
    "pointercancel": PointerEvent
    "pointerdown": PointerEvent
    "pointerenter": PointerEvent
    "pointerleave": PointerEvent
    "pointermove": PointerEvent
    "pointerout": PointerEvent
    "pointerover": PointerEvent
    "pointerup": PointerEvent
    "progress": ProgressEvent
    "ratechange": Event
    "reset": Event
    "resize": UIEvent
    "scroll": Event
    "securitypolicyviolation": SecurityPolicyViolationEvent
    "seeked": Event
    "seeking": Event
    "select": Event
    "selectionchange": Event
    "selectstart": Event
    "slotchange": Event
    "stalled": Event
    "submit": SubmitEvent
    "suspend": Event
    "timeupdate": Event
    "toggle": Event
    "touchcancel": TouchEvent
    "touchend": TouchEvent
    "touchmove": TouchEvent
    "touchstart": TouchEvent
    "transitioncancel": TransitionEvent
    "transitionend": TransitionEvent
    "transitionrun": TransitionEvent
    "transitionstart": TransitionEvent
    "volumechange": Event
    "waiting": Event
    "webkitanimationend": Event
    "webkitanimationiteration": Event
    "webkitanimationstart": Event
    "webkittransitionend": Event
    "wheel": WheelEvent
  }

  // Additional missing types
  interface Transferable {
    // Marker interface for transferable objects
    [key: string]: any
  }

  interface AbortSignal extends EventTarget {
    readonly aborted: boolean
    readonly reason: any
    onabort: ((this: AbortSignal, ev: Event) => any) | null
    throwIfAborted(): void
  }

  // ReadableStream
  interface ReadableStream<R = any> {
    readonly locked: boolean
    cancel(reason?: any): Promise<void>
    getReader(): ReadableStreamDefaultReader<R>
    pipeThrough<T>(transform: ReadableWritablePair<T, R>, options?: StreamPipeOptions): ReadableStream<T>
    pipeTo(destination: WritableStream<R>, options?: StreamPipeOptions): Promise<void>
    tee(): [ReadableStream<R>, ReadableStream<R>]
  }

  interface ReadableStreamDefaultReader<R = any> {
    readonly closed: Promise<undefined>
    cancel(reason?: any): Promise<void>
    read(): Promise<ReadableStreamDefaultReadResult<R>>
    releaseLock(): void
  }

  interface ReadableStreamDefaultReadResult<T> {
    done: boolean
    value: T
  }

  interface ReadableWritablePair<R = any, W = any> {
    readable: ReadableStream<R>
    writable: WritableStream<W>
  }

  interface WritableStream<W = any> {
    readonly locked: boolean
    abort(reason?: any): Promise<void>
    close(): Promise<void>
    getWriter(): WritableStreamDefaultWriter<W>
  }

  interface WritableStreamDefaultWriter<W = any> {
    readonly closed: Promise<undefined>
    readonly desiredSize: number | null
    readonly ready: Promise<undefined>
    abort(reason?: any): Promise<void>
    close(): Promise<void>
    releaseLock(): void
    write(chunk?: W): Promise<void>
  }

  interface StreamPipeOptions {
    preventAbort?: boolean
    preventCancel?: boolean
    preventClose?: boolean
    signal?: AbortSignal
  }

  // Additional Web API interfaces
  interface NavigationPreloadManager {
    disable(): Promise<void>
    enable(): Promise<void>
    getState(): Promise<NavigationPreloadState>
    setHeaderValue(value: string): Promise<void>
  }

  interface NavigationPreloadState {
    enabled: boolean
    headerValue: string
  }

  interface PushManager {
    getSubscription(): Promise<PushSubscription | null>
    permissionState(options?: PushSubscriptionOptionsInit): Promise<PermissionState>
    subscribe(options?: PushSubscriptionOptionsInit): Promise<PushSubscription>
  }

  interface PushSubscription {
    readonly endpoint: string
    readonly expirationTime: number | null
    readonly options: PushSubscriptionOptions
    getKey(name: PushEncryptionKeyName): ArrayBuffer | null
    toJSON(): PushSubscriptionJSON
    unsubscribe(): Promise<boolean>
  }

  // Push API types
  interface PushSubscriptionOptionsInit {
    applicationServerKey?: BufferSource | string | null
    userVisibleOnly?: boolean
  }

  interface PushSubscriptionOptions {
    readonly applicationServerKey: ArrayBuffer | null
    readonly userVisibleOnly: boolean
  }

  type PushEncryptionKeyName = "auth" | "p256dh"

  interface PushSubscriptionJSON {
    endpoint?: string
    expirationTime?: number | null
    keys?: Record<string, string>
  }

  interface SyncManager {
    getTags(): Promise<string[]>
    register(tag: string): Promise<void>
  }

  interface GetNotificationOptions {
    tag?: string
  }

  interface Notification extends EventTarget {
    readonly actions: readonly NotificationAction[]
    readonly badge: string
    readonly body: string
    readonly data: any
    readonly dir: NotificationDirection
    readonly icon: string
    readonly image: string
    readonly lang: string
    onclick: ((this: Notification, ev: Event) => any) | null
    onclose: ((this: Notification, ev: Event) => any) | null
    onerror: ((this: Notification, ev: Event) => any) | null
    onshow: ((this: Notification, ev: Event) => any) | null
    readonly renotify: boolean
    readonly requireInteraction: boolean
    readonly silent: boolean
    readonly tag: string
    readonly timestamp: number
    readonly title: string
    readonly vibrate: readonly number[]
    close(): void
  }

  // Notification constructor and static members
  const Notification: {
    prototype: Notification
    new(title: string, options?: NotificationOptions): Notification
    readonly maxActions: number
    readonly permission: NotificationPermission
    requestPermission(deprecatedCallback?: NotificationPermissionCallback): Promise<NotificationPermission>
  }

  interface NotificationOptions {
    actions?: NotificationAction[]
    badge?: string
    body?: string
    data?: any
    dir?: NotificationDirection
    icon?: string
    image?: string
    lang?: string
    renotify?: boolean
    requireInteraction?: boolean
    silent?: boolean
    tag?: string
    timestamp?: number
    vibrate?: VibratePattern
  }

  interface NotificationAction {
    action: string
    icon?: string
    title: string
  }

  type NotificationDirection = "auto" | "ltr" | "rtl"
  type NotificationPermission = "default" | "denied" | "granted"
  type NotificationPermissionCallback = (permission: NotificationPermission) => void

  interface BeforeUnloadEvent extends Event {
    returnValue: any
  }

  interface HashChangeEvent extends Event {
    readonly newURL: string
    readonly oldURL: string
  }

  interface PageTransitionEvent extends Event {
    readonly persisted: boolean
  }

  interface PopStateEvent extends Event {
    readonly state: any
  }

  interface StorageEvent extends Event {
    readonly key: string | null
    readonly newValue: string | null
    readonly oldValue: string | null
    readonly storageArea: Storage | null
    readonly url: string
  }

  type FrameRequestCallback = (time: number) => void

  interface IdleRequestCallback {
    (deadline: IdleDeadline): void
  }

  interface IdleDeadline {
    readonly didTimeout: boolean
    timeRemaining(): number
  }

  interface IdleRequestOptions {
    timeout?: number
  }

  interface ScrollToOptions {
    behavior?: ScrollBehavior
    left?: number
    top?: number
  }

  type ScrollBehavior = "auto" | "instant" | "smooth"

  // Audio Buffer Types
  interface AudioBuffer {
    readonly duration: number
    readonly length: number
    readonly numberOfChannels: number
    readonly sampleRate: number
    copyFromChannel(destination: Float32Array, channelNumber: number, startInChannel?: number): void
    copyToChannel(source: Float32Array, channelNumber: number, startInChannel?: number): void
    getChannelData(channel: number): Float32Array
  }

  interface OscillatorNode extends AudioScheduledSourceNode {
    readonly detune: AudioParam
    readonly frequency: AudioParam
    type: OscillatorType
    setPeriodicWave(periodicWave: PeriodicWave): void
    addEventListener<K extends keyof AudioScheduledSourceNodeEventMap>(type: K, listener: (this: OscillatorNode, ev: AudioScheduledSourceNodeEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void
    addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void
    removeEventListener<K extends keyof AudioScheduledSourceNodeEventMap>(type: K, listener: (this: OscillatorNode, ev: AudioScheduledSourceNodeEventMap[K]) => any, options?: boolean | EventListenerOptions): void
    removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void
  }

  interface AudioScheduledSourceNode extends AudioNode {
    onended: ((this: AudioScheduledSourceNode, ev: Event) => any) | null
    start(when?: number): void
    stop(when?: number): void
    addEventListener<K extends keyof AudioScheduledSourceNodeEventMap>(type: K, listener: (this: AudioScheduledSourceNode, ev: AudioScheduledSourceNodeEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void
    addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void
    removeEventListener<K extends keyof AudioScheduledSourceNodeEventMap>(type: K, listener: (this: AudioScheduledSourceNode, ev: AudioScheduledSourceNodeEventMap[K]) => any, options?: boolean | EventListenerOptions): void
    removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void
  }

  interface AudioScheduledSourceNodeEventMap {
    "ended": Event
  }

  interface PeriodicWave {
    // Placeholder for PeriodicWave
    [key: string]: any
  }

  // Additional missing types for FileSystem API
  interface FileSystemGetDirectoryOptions {
    create?: boolean
  }

  interface FileSystemGetFileOptions {
    create?: boolean
  }

  interface FileSystemRemoveOptions {
    recursive?: boolean
  }

  interface FileSystemPermissionDescriptor {
    mode?: FileSystemPermissionMode
  }

  type FileSystemPermissionMode = "read" | "readwrite"

  interface FileSystemCreateWritableOptions {
    keepExistingData?: boolean
  }

  interface FileSystemWritableFileStream extends WritableStream {
    seek(position: number): Promise<void>
    truncate(size: number): Promise<void>
    write(data: FileSystemWriteChunkType): Promise<void>
  }

  type FileSystemWriteChunkType = BufferSource | Blob | string | WriteParams

  interface WriteParams {
    data: BufferSource | Blob | string
    position?: number
    size?: number
    type: "write" | "seek" | "truncate"
  }

  // Additional Crypto API types
  type AlgorithmIdentifier = string | Algorithm

  interface Algorithm {
    name: string
  }

  interface RsaOaepParams extends Algorithm {
    label?: BufferSource
  }

  interface AesCtrParams extends Algorithm {
    counter: BufferSource
    length: number
  }

  interface AesCbcParams extends Algorithm {
    iv: BufferSource
  }

  interface AesGcmParams extends Algorithm {
    additionalData?: BufferSource
    iv: BufferSource
    tagLength?: number
  }

  interface CryptoKey {
    readonly algorithm: KeyAlgorithm
    readonly extractable: boolean
    readonly type: KeyType
    readonly usages: KeyUsage[]
  }

  interface KeyAlgorithm {
    name: string
  }

  type KeyType = "private" | "public" | "secret"
  type KeyUsage = "decrypt" | "deriveBits" | "deriveKey" | "encrypt" | "sign" | "unwrapKey" | "verify" | "wrapKey"
  type KeyFormat = "jwk" | "pkcs8" | "raw" | "spki"

  interface JsonWebKey {
    alg?: string
    crv?: string
    d?: string
    dp?: string
    dq?: string
    e?: string
    ext?: boolean
    k?: string
    key_ops?: string[]
    kty?: string
    n?: string
    oth?: RsaOtherPrimesInfo[]
    p?: string
    q?: string
    qi?: string
    use?: string
    x?: string
    y?: string
  }

  interface RsaOtherPrimesInfo {
    d?: string
    r?: string
    t?: string
  }

  // Additional Web Crypto types
  interface EcdhKeyDeriveParams extends Algorithm {
    public: CryptoKey
  }

  interface HkdfParams extends Algorithm {
    hash: HashAlgorithmIdentifier
    info: BufferSource
    salt: BufferSource
  }

  interface Pbkdf2Params extends Algorithm {
    hash: HashAlgorithmIdentifier
    iterations: number
    salt: BufferSource
  }

  type HashAlgorithmIdentifier = AlgorithmIdentifier

  interface AesDerivedKeyParams extends Algorithm {
    length: number
  }

  interface HmacImportParams extends Algorithm {
    hash: HashAlgorithmIdentifier
    length?: number
  }

  interface RsaHashedKeyGenParams extends RsaKeyGenParams {
    hash: HashAlgorithmIdentifier
  }

  interface RsaKeyGenParams extends Algorithm {
    modulusLength: number
    publicExponent: BigInteger
  }

  type BigInteger = Uint8Array

  interface EcKeyGenParams extends Algorithm {
    namedCurve: NamedCurve
  }

  type NamedCurve = string

  interface AesKeyGenParams extends Algorithm {
    length: number
  }

  interface HmacKeyGenParams extends Algorithm {
    hash: HashAlgorithmIdentifier
    length?: number
  }

  interface CryptoKeyPair {
    privateKey: CryptoKey
    publicKey: CryptoKey
  }

  interface RsaHashedImportParams extends Algorithm {
    hash: HashAlgorithmIdentifier
  }

  interface EcKeyImportParams extends Algorithm {
    namedCurve: NamedCurve
  }

  interface AesKeyAlgorithm extends KeyAlgorithm {
    length: number
  }

  interface RsaPssParams extends Algorithm {
    saltLength: number
  }

  interface EcdsaParams extends Algorithm {
    hash: HashAlgorithmIdentifier
  }

  // Additional Web Authentication types
  interface PublicKeyCredentialRpEntity {
    id?: string
    name: string
  }

  interface PublicKeyCredentialUserEntity {
    displayName: string
    id: BufferSource
    name: string
  }

  interface PublicKeyCredentialParameters {
    alg: COSEAlgorithmIdentifier
    type: PublicKeyCredentialType
  }

  type COSEAlgorithmIdentifier = number
  type PublicKeyCredentialType = "public-key"

  interface AuthenticatorSelectionCriteria {
    authenticatorAttachment?: AuthenticatorAttachment
    requireResidentKey?: boolean
    residentKey?: ResidentKeyRequirement
    userVerification?: UserVerificationRequirement
  }

  type AuthenticatorAttachment = "cross-platform" | "platform"
  type ResidentKeyRequirement = "discouraged" | "preferred" | "required"
  type UserVerificationRequirement = "discouraged" | "preferred" | "required"

  interface PublicKeyCredentialDescriptor {
    id: BufferSource
    transports?: AuthenticatorTransport[]
    type: PublicKeyCredentialType
  }

  type AuthenticatorTransport = "ble" | "hybrid" | "internal" | "nfc" | "usb"

  interface AuthenticationExtensionsClientInputs {
    appid?: string
    credProps?: boolean
    hmacCreateSecret?: boolean
  }

  type AttestationConveyancePreference = "direct" | "enterprise" | "indirect" | "none"

  // Additional Performance API types
  interface PerformanceNavigation {
    readonly redirectCount: number
    readonly type: number
    readonly TYPE_BACK_FORWARD: number
    readonly TYPE_NAVIGATE: number
    readonly TYPE_RELOAD: number
    readonly TYPE_RESERVED: number
  }

  interface PerformanceTiming {
    readonly connectEnd: number
    readonly connectStart: number
    readonly domComplete: number
    readonly domContentLoadedEventEnd: number
    readonly domContentLoadedEventStart: number
    readonly domInteractive: number
    readonly domLoading: number
    readonly domainLookupEnd: number
    readonly domainLookupStart: number
    readonly fetchStart: number
    readonly loadEventEnd: number
    readonly loadEventStart: number
    readonly navigationStart: number
    readonly redirectEnd: number
    readonly redirectStart: number
    readonly requestStart: number
    readonly responseEnd: number
    readonly responseStart: number
    readonly secureConnectionStart: number
    readonly unloadEventEnd: number
    readonly unloadEventStart: number
  }

  type PerformanceEntryList = PerformanceEntry[]

  interface PerformanceEntry {
    readonly duration: number
    readonly entryType: string
    readonly name: string
    readonly startTime: number
    toJSON(): any
  }

  interface PerformanceMarkOptions {
    detail?: any
    startTime?: number
  }

  interface PerformanceMark extends PerformanceEntry {
    readonly detail: any
  }

  interface PerformanceMeasureOptions {
    detail?: any
    duration?: number
    end?: string | number
    start?: string | number
  }

  interface PerformanceMeasure extends PerformanceEntry {
    readonly detail: any
  }

  // Additional Cache API types
  type RequestInfo = Request | string

  interface MultiCacheQueryOptions extends CacheQueryOptions {
    cacheName?: string
  }

  interface CacheQueryOptions {
    ignoreMethod?: boolean
    ignoreSearch?: boolean
    ignoreVary?: boolean
  }

  interface Cache {
    add(request: RequestInfo | URL): Promise<void>
    addAll(requests: RequestInfo[]): Promise<void>
    delete(request: RequestInfo | URL, options?: CacheQueryOptions): Promise<boolean>
    keys(request?: RequestInfo | URL, options?: CacheQueryOptions): Promise<readonly Request[]>
    match(request: RequestInfo | URL, options?: CacheQueryOptions): Promise<Response | undefined>
    matchAll(request?: RequestInfo | URL, options?: CacheQueryOptions): Promise<readonly Response[]>
    put(request: RequestInfo | URL, response: Response): Promise<void>
  }

  interface Request {
    readonly body: ReadableStream<Uint8Array> | null
    readonly bodyUsed: boolean
    readonly cache: RequestCache
    readonly credentials: RequestCredentials
    readonly destination: RequestDestination
    readonly headers: Headers
    readonly integrity: string
    readonly keepalive: boolean
    readonly method: string
    readonly mode: RequestMode
    readonly redirect: RequestRedirect
    readonly referrer: string
    readonly referrerPolicy: ReferrerPolicy
    readonly signal: AbortSignal
    readonly url: string
    arrayBuffer(): Promise<ArrayBuffer>
    blob(): Promise<Blob>
    clone(): Request
    formData(): Promise<FormData>
    json(): Promise<any>
    text(): Promise<string>
  }

  interface Response {
    readonly body: ReadableStream<Uint8Array> | null
    readonly bodyUsed: boolean
    readonly headers: Headers
    readonly ok: boolean
    readonly redirected: boolean
    readonly status: number
    readonly statusText: string
    readonly type: ResponseType
    readonly url: string
    arrayBuffer(): Promise<ArrayBuffer>
    blob(): Promise<Blob>
    clone(): Response
    formData(): Promise<FormData>
    json(): Promise<any>
    text(): Promise<string>
  }

  // Response constructor and static members
  const Response: {
    prototype: Response
    new(body?: BodyInit | null, init?: ResponseInit): Response
    error(): Response
    redirect(url: string | URL, status?: number): Response
  }

  interface Headers {
    append(name: string, value: string): void
    delete(name: string): void
    get(name: string): string | null
    has(name: string): boolean
    set(name: string, value: string): void
    forEach(callbackfn: (value: string, key: string, parent: Headers) => void, thisArg?: any): void
  }

  // Additional DOM types
  interface Node {
    readonly baseURI: string
    readonly childNodes: NodeList
    readonly firstChild: Node | null
    readonly isConnected: boolean
    readonly lastChild: Node | null
    readonly nextSibling: Node | null
    readonly nodeName: string
    readonly nodeType: number
    nodeValue: string | null
    readonly ownerDocument: Document | null
    readonly parentElement: Element | null
    readonly parentNode: Node | null
    readonly previousSibling: Node | null
    textContent: string | null
    appendChild<T extends Node>(node: T): T
    cloneNode(deep?: boolean): Node
    compareDocumentPosition(other: Node): number
    contains(other: Node | null): boolean
    getRootNode(options?: GetRootNodeOptions): Node
    hasChildNodes(): boolean
    insertBefore<T extends Node>(node: T, child: Node | null): T
    isDefaultNamespace(namespace: string | null): boolean
    isEqualNode(otherNode: Node | null): boolean
    isSameNode(otherNode: Node | null): boolean
    lookupNamespaceURI(prefix: string | null): string | null
    lookupPrefix(namespace: string | null): string | null
    normalize(): void
    removeChild<T extends Node>(child: T): T
    replaceChild<T extends Node>(node: Node, child: T): T
    readonly ATTRIBUTE_NODE: number
    readonly CDATA_SECTION_NODE: number
    readonly COMMENT_NODE: number
    readonly DOCUMENT_FRAGMENT_NODE: number
    readonly DOCUMENT_NODE: number
    readonly DOCUMENT_POSITION_CONTAINED_BY: number
    readonly DOCUMENT_POSITION_CONTAINS: number
    readonly DOCUMENT_POSITION_DISCONNECTED: number
    readonly DOCUMENT_POSITION_FOLLOWING: number
    readonly DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC: number
    readonly DOCUMENT_POSITION_PRECEDING: number
    readonly DOCUMENT_TYPE_NODE: number
    readonly ELEMENT_NODE: number
    readonly ENTITY_NODE: number
    readonly ENTITY_REFERENCE_NODE: number
    readonly NOTATION_NODE: number
    readonly PROCESSING_INSTRUCTION_NODE: number
    readonly TEXT_NODE: number
  }

  interface NodeList {
    readonly length: number
    item(index: number): Node | null
    forEach(callbackfn: (value: Node, key: number, parent: NodeList) => void, thisArg?: any): void
    [index: number]: Node
  }

  interface Element extends Node {
    readonly attributes: NamedNodeMap
    readonly classList: DOMTokenList
    className: string
    readonly clientHeight: number
    readonly clientLeft: number
    readonly clientTop: number
    readonly clientWidth: number
    id: string
    innerHTML: string
    readonly localName: string
    readonly namespaceURI: string | null
    readonly outerHTML: string
    readonly part: DOMTokenList
    readonly prefix: string | null
    readonly scrollHeight: number
    scrollLeft: number
    scrollTop: number
    readonly scrollWidth: number
    readonly shadowRoot: ShadowRoot | null
    slot: string
    readonly tagName: string
    attachShadow(init: ShadowRootInit): ShadowRoot
    closest<K extends keyof HTMLElementTagNameMap>(selector: K): HTMLElementTagNameMap[K] | null
    closest<K extends keyof SVGElementTagNameMap>(selector: K): SVGElementTagNameMap[K] | null
    closest<E extends Element = Element>(selectors: string): E | null
    computedStyleMap(): StylePropertyMapReadOnly
    getAttribute(qualifiedName: string): string | null
    getAttributeNS(namespace: string | null, localName: string): string | null
    getAttributeNames(): string[]
    getAttributeNode(qualifiedName: string): Attr | null
    getAttributeNodeNS(namespace: string | null, localName: string): Attr | null
    getBoundingClientRect(): DOMRect
    getClientRects(): DOMRectList
    getElementsByClassName(classNames: string): HTMLCollectionOf<Element>
    getElementsByTagName<K extends keyof HTMLElementTagNameMap>(qualifiedName: K): HTMLCollectionOf<HTMLElementTagNameMap[K]>
    getElementsByTagName<K extends keyof SVGElementTagNameMap>(qualifiedName: K): HTMLCollectionOf<SVGElementTagNameMap[K]>
    getElementsByTagName(qualifiedName: string): HTMLCollectionOf<Element>
    getElementsByTagNameNS(namespace: string | null, localName: string): HTMLCollectionOf<Element>
    hasAttribute(qualifiedName: string): boolean
    hasAttributeNS(namespace: string | null, localName: string): boolean
    hasAttributes(): boolean
    hasPointerCapture(pointerId: number): boolean
    insertAdjacentElement(where: InsertPosition, element: Element): Element | null
    insertAdjacentHTML(position: InsertPosition, text: string): void
    insertAdjacentText(where: InsertPosition, data: string): void
    matches(selectors: string): boolean
    querySelector<K extends keyof HTMLElementTagNameMap>(selectors: K): HTMLElementTagNameMap[K] | null
    querySelector<K extends keyof SVGElementTagNameMap>(selectors: K): SVGElementTagNameMap[K] | null
    querySelector<E extends Element = Element>(selectors: string): E | null
    querySelectorAll<K extends keyof HTMLElementTagNameMap>(selectors: K): NodeListOf<HTMLElementTagNameMap[K]>
    querySelectorAll<K extends keyof SVGElementTagNameMap>(selectors: K): NodeListOf<SVGElementTagNameMap[K]>
    querySelectorAll<E extends Element = Element>(selectors: string): NodeListOf<E>
    releasePointerCapture(pointerId: number): void
    remove(): void
    removeAttribute(qualifiedName: string): void
    removeAttributeNS(namespace: string | null, localName: string): void
    removeAttributeNode(attr: Attr): Attr
    requestFullscreen(options?: FullscreenOptions): Promise<void>
    requestPointerLock(): void
    scroll(options?: ScrollToOptions): void
    scroll(x: number, y: number): void
    scrollBy(options?: ScrollToOptions): void
    scrollBy(x: number, y: number): void
    scrollIntoView(arg?: boolean | ScrollIntoViewOptions): void
    scrollTo(options?: ScrollToOptions): void
    scrollTo(x: number, y: number): void
    setAttribute(qualifiedName: string, value: string): void
    setAttributeNS(namespace: string | null, qualifiedName: string, value: string): void
    setAttributeNode(attr: Attr): Attr | null
    setAttributeNodeNS(attr: Attr): Attr | null
    setPointerCapture(pointerId: number): void
    toggleAttribute(qualifiedName: string, force?: boolean): boolean
    webkitMatchesSelector(selectors: string): boolean
  }

  interface HTMLElement extends Element {
    accessKey: string
    readonly accessKeyLabel: string
    autocapitalize: string
    dir: string
    draggable: boolean
    enterKeyHint: string
    hidden: boolean
    inert: boolean
    innerText: string
    inputMode: string
    readonly isContentEditable: boolean
    lang: string
    readonly nonce?: string
    readonly offsetHeight: number
    readonly offsetLeft: number
    readonly offsetParent: Element | null
    readonly offsetTop: number
    readonly offsetWidth: number
    outerText: string
    spellcheck: boolean
    title: string
    translate: boolean
    attachInternals(): ElementInternals
    blur(): void
    click(): void
    focus(options?: FocusOptions): void
    addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void
    addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void
    removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void
    removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void
  }

  interface HTMLElementEventMap extends ElementEventMap, GlobalEventHandlersEventMap {
    "copy": ClipboardEvent
    "cut": ClipboardEvent
    "paste": ClipboardEvent
  }

  interface ElementEventMap {
    "fullscreenchange": Event
    "fullscreenerror": Event
  }

  // Additional missing types
  type ResponseType = "basic" | "cors" | "default" | "error" | "opaque" | "opaqueredirect"
  type RequestCache = "default" | "force-cache" | "no-cache" | "no-store" | "only-if-cached" | "reload"
  type RequestCredentials = "include" | "omit" | "same-origin"
  type RequestDestination = "" | "audio" | "audioworklet" | "document" | "embed" | "font" | "frame" | "iframe" | "image" | "manifest" | "object" | "paintworklet" | "report" | "script" | "serviceworker" | "sharedworker" | "style" | "track" | "video" | "worker" | "xslt"
  type RequestMode = "cors" | "navigate" | "no-cors" | "same-origin"
  type RequestRedirect = "error" | "follow" | "manual"
  type ReferrerPolicy = "" | "no-referrer" | "no-referrer-when-downgrade" | "origin" | "origin-when-cross-origin" | "same-origin" | "strict-origin" | "strict-origin-when-cross-origin" | "unsafe-url"

  interface ResponseInit {
    headers?: HeadersInit
    status?: number
    statusText?: string
  }

  type HeadersInit = Headers | string[][] | Record<string, string>

  interface GetRootNodeOptions {
    composed?: boolean
  }

  interface NamedNodeMap {
    readonly length: number
    getNamedItem(qualifiedName: string): Attr | null
    getNamedItemNS(namespace: string | null, localName: string): Attr | null
    item(index: number): Attr | null
    removeNamedItem(qualifiedName: string): Attr
    removeNamedItemNS(namespace: string | null, localName: string): Attr
    setNamedItem(attr: Attr): Attr | null
    setNamedItemNS(attr: Attr): Attr | null
    [index: number]: Attr
  }

  interface Attr extends Node {
    readonly localName: string
    readonly name: string
    readonly namespaceURI: string | null
    readonly ownerElement: Element | null
    readonly prefix: string | null
    readonly specified: boolean
    value: string
  }

  interface DOMTokenList {
    readonly length: number
    value: string
    add(...tokens: string[]): void
    contains(token: string): boolean
    item(index: number): string | null
    remove(...tokens: string[]): void
    replace(oldToken: string, newToken: string): boolean
    supports(token: string): boolean
    toggle(token: string, force?: boolean): boolean
    forEach(callbackfn: (value: string, key: number, parent: DOMTokenList) => void, thisArg?: any): void
    [index: number]: string
  }

  interface ShadowRoot extends DocumentFragment {
    readonly delegatesFocus: boolean
    readonly host: Element
    readonly mode: ShadowRootMode
    readonly slotAssignment: SlotAssignmentMode
  }

  type ShadowRootMode = "closed" | "open"
  type SlotAssignmentMode = "manual" | "named"

  interface ShadowRootInit {
    delegatesFocus?: boolean
    mode: ShadowRootMode
    slotAssignment?: SlotAssignmentMode
  }

  interface DocumentFragment extends Node {
    readonly childElementCount: number
    readonly children: HTMLCollection
    readonly firstElementChild: Element | null
    readonly lastElementChild: Element | null
    append(...nodes: (Node | string)[]): void
    getElementById(elementId: string): Element | null
    prepend(...nodes: (Node | string)[]): void
    querySelector<K extends keyof HTMLElementTagNameMap>(selectors: K): HTMLElementTagNameMap[K] | null
    querySelector<K extends keyof SVGElementTagNameMap>(selectors: K): SVGElementTagNameMap[K] | null
    querySelector<E extends Element = Element>(selectors: string): E | null
    querySelectorAll<K extends keyof HTMLElementTagNameMap>(selectors: K): NodeListOf<HTMLElementTagNameMap[K]>
    querySelectorAll<K extends keyof SVGElementTagNameMap>(selectors: K): NodeListOf<SVGElementTagNameMap[K]>
    querySelectorAll<E extends Element = Element>(selectors: string): NodeListOf<E>
    replaceChildren(...nodes: (Node | string)[]): void
  }

  interface HTMLCollection {
    readonly length: number
    item(index: number): Element | null
    namedItem(name: string): Element | null
    [index: number]: Element
  }

  interface HTMLCollectionOf<T extends Element> {
    readonly length: number
    item(index: number): T | null
    namedItem(name: string): T | null
    [index: number]: T
  }

  interface NodeListOf<TNode extends Node> extends NodeList {
    readonly length: number
    item(index: number): TNode
    forEach(callbackfn: (value: TNode, key: number, parent: NodeListOf<TNode>) => void, thisArg?: any): void
    [index: number]: TNode
  }

  interface HTMLElementTagNameMap {
    "a": HTMLAnchorElement
    "abbr": HTMLElement
    "address": HTMLElement
    "area": HTMLAreaElement
    "article": HTMLElement
    "aside": HTMLElement
    "audio": HTMLAudioElement
    "b": HTMLElement
    "base": HTMLBaseElement
    "bdi": HTMLElement
    "bdo": HTMLElement
    "blockquote": HTMLQuoteElement
    "body": HTMLBodyElement
    "br": HTMLBRElement
    "button": HTMLButtonElement
    "canvas": HTMLCanvasElement
    "caption": HTMLTableCaptionElement
    "cite": HTMLElement
    "code": HTMLElement
    "col": HTMLTableColElement
    "colgroup": HTMLTableColElement
    "data": HTMLDataElement
    "datalist": HTMLDataListElement
    "dd": HTMLElement
    "del": HTMLModElement
    "details": HTMLDetailsElement
    "dfn": HTMLElement
    "dialog": HTMLDialogElement
    "div": HTMLDivElement
    "dl": HTMLDListElement
    "dt": HTMLElement
    "em": HTMLElement
    "embed": HTMLEmbedElement
    "fieldset": HTMLFieldSetElement
    "figcaption": HTMLElement
    "figure": HTMLElement
    "footer": HTMLElement
    "form": HTMLFormElement
    "h1": HTMLHeadingElement
    "h2": HTMLHeadingElement
    "h3": HTMLHeadingElement
    "h4": HTMLHeadingElement
    "h5": HTMLHeadingElement
    "h6": HTMLHeadingElement
    "head": HTMLHeadElement
    "header": HTMLElement
    "hgroup": HTMLElement
    "hr": HTMLHRElement
    "html": HTMLHtmlElement
    "i": HTMLElement
    "iframe": HTMLIFrameElement
    "img": HTMLImageElement
    "input": HTMLInputElement
    "ins": HTMLModElement
    "kbd": HTMLElement
    "label": HTMLLabelElement
    "legend": HTMLLegendElement
    "li": HTMLLIElement
    "link": HTMLLinkElement
    "main": HTMLElement
    "map": HTMLMapElement
    "mark": HTMLElement
    "menu": HTMLMenuElement
    "meta": HTMLMetaElement
    "meter": HTMLMeterElement
    "nav": HTMLElement
    "noscript": HTMLElement
    "object": HTMLObjectElement
    "ol": HTMLOListElement
    "optgroup": HTMLOptGroupElement
    "option": HTMLOptionElement
    "output": HTMLOutputElement
    "p": HTMLParagraphElement
    "picture": HTMLPictureElement
    "pre": HTMLPreElement
    "progress": HTMLProgressElement
    "q": HTMLQuoteElement
    "rp": HTMLElement
    "rt": HTMLElement
    "ruby": HTMLElement
    "s": HTMLElement
    "samp": HTMLElement
    "script": HTMLScriptElement
    "search": HTMLElement
    "section": HTMLElement
    "select": HTMLSelectElement
    "slot": HTMLSlotElement
    "small": HTMLElement
    "source": HTMLSourceElement
    "span": HTMLSpanElement
    "strong": HTMLElement
    "style": HTMLStyleElement
    "sub": HTMLElement
    "summary": HTMLElement
    "sup": HTMLElement
    "table": HTMLTableElement
    "tbody": HTMLTableSectionElement
    "td": HTMLTableCellElement
    "template": HTMLTemplateElement
    "textarea": HTMLTextAreaElement
    "tfoot": HTMLTableSectionElement
    "th": HTMLTableCellElement
    "thead": HTMLTableSectionElement
    "time": HTMLTimeElement
    "title": HTMLTitleElement
    "tr": HTMLTableRowElement
    "track": HTMLTrackElement
    "u": HTMLElement
    "ul": HTMLUListElement
    "var": HTMLElement
    "video": HTMLVideoElement
    "wbr": HTMLElement
  }

  interface SVGElementTagNameMap {
    "svg": SVGSVGElement
    "g": SVGGElement
    "path": SVGPathElement
    "circle": SVGCircleElement
    "rect": SVGRectElement
    "line": SVGLineElement
    "text": SVGTextElement
  }

  // Additional HTML element interfaces (basic definitions)
  // These are simplified interfaces that extend their base types
  interface HTMLAnchorElement extends HTMLElement { href: string }
  interface HTMLAreaElement extends HTMLElement {
    // Placeholder for HTMLAreaElement - extends HTMLElement with area-specific properties
    [key: string]: any
  }
  interface HTMLAudioElement extends HTMLMediaElement {
    // Placeholder for HTMLAudioElement - extends HTMLMediaElement
    [key: string]: any
  }
  interface HTMLBaseElement extends HTMLElement {
    // Placeholder for HTMLBaseElement - extends HTMLElement with base-specific properties
    [key: string]: any
  }
  interface HTMLBodyElement extends HTMLElement {
    // Placeholder for HTMLBodyElement - extends HTMLElement with body-specific properties
    [key: string]: any
  }
  interface HTMLBRElement extends HTMLElement {
    // Placeholder for HTMLBRElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLButtonElement extends HTMLElement {
    // Placeholder for HTMLButtonElement - extends HTMLElement with button-specific properties
    [key: string]: any
  }
  interface HTMLCanvasElement extends HTMLElement {
    // Placeholder for HTMLCanvasElement - extends HTMLElement with canvas-specific properties
    [key: string]: any
  }
  interface HTMLTableCaptionElement extends HTMLElement {
    // Placeholder for HTMLTableCaptionElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLQuoteElement extends HTMLElement {
    // Placeholder for HTMLQuoteElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLTableColElement extends HTMLElement {
    // Placeholder for HTMLTableColElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLDataElement extends HTMLElement {
    // Placeholder for HTMLDataElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLDataListElement extends HTMLElement {
    // Placeholder for HTMLDataListElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLModElement extends HTMLElement {
    // Placeholder for HTMLModElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLDetailsElement extends HTMLElement {
    // Placeholder for HTMLDetailsElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLDialogElement extends HTMLElement {
    // Placeholder for HTMLDialogElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLDListElement extends HTMLElement {
    // Placeholder for HTMLDListElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLEmbedElement extends HTMLElement {
    // Placeholder for HTMLEmbedElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLFieldSetElement extends HTMLElement {
    // Placeholder for HTMLFieldSetElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLHeadingElement extends HTMLElement {
    // Placeholder for HTMLHeadingElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLHeadElement extends HTMLElement {
    // Placeholder for HTMLHeadElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLHRElement extends HTMLElement {
    // Placeholder for HTMLHRElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLHtmlElement extends HTMLElement {
    // Placeholder for HTMLHtmlElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLIFrameElement extends HTMLElement {
    // Placeholder for HTMLIFrameElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLImageElement extends HTMLElement {
    // Placeholder for HTMLImageElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLLabelElement extends HTMLElement {
    // Placeholder for HTMLLabelElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLLegendElement extends HTMLElement {
    // Placeholder for HTMLLegendElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLLIElement extends HTMLElement {
    // Placeholder for HTMLLIElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLLinkElement extends HTMLElement {
    // Placeholder for HTMLLinkElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLMapElement extends HTMLElement {
    // Placeholder for HTMLMapElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLMenuElement extends HTMLElement {
    // Placeholder for HTMLMenuElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLMetaElement extends HTMLElement {
    // Placeholder for HTMLMetaElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLMeterElement extends HTMLElement {
    // Placeholder for HTMLMeterElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLObjectElement extends HTMLElement {
    // Placeholder for HTMLObjectElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLOListElement extends HTMLElement {
    // Placeholder for HTMLOListElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLOptGroupElement extends HTMLElement {
    // Placeholder for HTMLOptGroupElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLOptionElement extends HTMLElement {
    // Placeholder for HTMLOptionElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLOutputElement extends HTMLElement {
    // Placeholder for HTMLOutputElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLParagraphElement extends HTMLElement {
    // Placeholder for HTMLParagraphElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLPictureElement extends HTMLElement {
    // Placeholder for HTMLPictureElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLPreElement extends HTMLElement {
    // Placeholder for HTMLPreElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLProgressElement extends HTMLElement {
    // Placeholder for HTMLProgressElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLScriptElement extends HTMLElement {
    // Placeholder for HTMLScriptElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLSelectElement extends HTMLElement {
    // Placeholder for HTMLSelectElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLSlotElement extends HTMLElement {
    // Placeholder for HTMLSlotElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLSourceElement extends HTMLElement {
    // Placeholder for HTMLSourceElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLSpanElement extends HTMLElement {
    // Placeholder for HTMLSpanElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLStyleElement extends HTMLElement {
    // Placeholder for HTMLStyleElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLTableElement extends HTMLElement {
    // Placeholder for HTMLTableElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLTableSectionElement extends HTMLElement {
    // Placeholder for HTMLTableSectionElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLTableCellElement extends HTMLElement {
    // Placeholder for HTMLTableCellElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLTemplateElement extends HTMLElement {
    // Placeholder for HTMLTemplateElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLTextAreaElement extends HTMLElement {
    // Placeholder for HTMLTextAreaElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLTimeElement extends HTMLElement {
    // Placeholder for HTMLTimeElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLTitleElement extends HTMLElement {
    // Placeholder for HTMLTitleElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLTableRowElement extends HTMLElement {
    // Placeholder for HTMLTableRowElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLTrackElement extends HTMLElement {
    // Placeholder for HTMLTrackElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLUListElement extends HTMLElement {
    // Placeholder for HTMLUListElement - extends HTMLElement
    [key: string]: any
  }
  interface HTMLVideoElement extends HTMLMediaElement {
    // Placeholder for HTMLVideoElement - extends HTMLMediaElement
    [key: string]: any
  }
  interface HTMLMediaElement extends HTMLElement {
    // Placeholder for HTMLMediaElement - extends HTMLElement with media-specific properties
    [key: string]: any
  }

  // Basic SVG interfaces
  interface SVGElement extends Element {
    // Placeholder for SVGElement - extends Element with SVG-specific properties
    [key: string]: any
  }
  interface SVGSVGElement extends SVGElement {
    // Placeholder for SVGSVGElement - extends SVGElement
    [key: string]: any
  }
  interface SVGGElement extends SVGElement {
    // Placeholder for SVGGElement - extends SVGElement
    [key: string]: any
  }
  interface SVGPathElement extends SVGElement {
    // Placeholder for SVGPathElement - extends SVGElement
    [key: string]: any
  }
  interface SVGCircleElement extends SVGElement {
    // Placeholder for SVGCircleElement - extends SVGElement
    [key: string]: any
  }
  interface SVGRectElement extends SVGElement {
    // Placeholder for SVGRectElement - extends SVGElement
    [key: string]: any
  }
  interface SVGLineElement extends SVGElement {
    // Placeholder for SVGLineElement - extends SVGElement
    [key: string]: any
  }
  interface SVGTextElement extends SVGElement {
    // Placeholder for SVGTextElement - extends SVGElement
    [key: string]: any
  }

  // Additional missing interfaces
  interface StylePropertyMapReadOnly {
    readonly size: number
    get(property: string): CSSStyleValue | undefined
    getAll(property: string): CSSStyleValue[]
    has(property: string): boolean
    forEach(callbackfn: (value: CSSStyleValue[], key: string, parent: StylePropertyMapReadOnly) => void, thisArg?: any): void
  }

  interface CSSStyleValue {
    toString(): string
  }

  interface DOMRect {
    readonly bottom: number
    readonly height: number
    readonly left: number
    readonly right: number
    readonly top: number
    readonly width: number
    readonly x: number
    readonly y: number
    toJSON(): any
  }

  interface DOMRectList {
    readonly length: number
    item(index: number): DOMRect | null
    [index: number]: DOMRect
  }

  type InsertPosition = "afterbegin" | "afterend" | "beforebegin" | "beforeend"

  interface FullscreenOptions {
    navigationUI?: FullscreenNavigationUI
  }

  type FullscreenNavigationUI = "auto" | "hide" | "show"

  interface ScrollIntoViewOptions extends ScrollOptions {
    block?: ScrollLogicalPosition
    inline?: ScrollLogicalPosition
  }

  interface ScrollOptions {
    behavior?: ScrollBehavior
  }

  type ScrollLogicalPosition = "center" | "end" | "nearest" | "start"

  interface ElementInternals {
    readonly form: HTMLFormElement | null
    readonly labels: NodeList
    readonly shadowRoot: ShadowRoot | null
    readonly states: CustomStateSet
    readonly validationMessage: string
    readonly validity: ValidityState
    readonly willValidate: boolean
    checkValidity(): boolean
    reportValidity(): boolean
    setFormValue(value: File | string | FormData | null, state?: File | string | FormData | null): void
    setValidity(flags?: ValidityStateFlags, message?: string, anchor?: HTMLElement): void
  }

  interface CustomStateSet {
    readonly size: number
    add(value: string): void
    clear(): void
    delete(value: string): boolean
    forEach(callbackfn: (value: string, value2: string, set: CustomStateSet) => void, thisArg?: any): void
    has(value: string): boolean
  }

  interface ValidityStateFlags {
    badInput?: boolean
    customError?: boolean
    patternMismatch?: boolean
    rangeOverflow?: boolean
    rangeUnderflow?: boolean
    stepMismatch?: boolean
    tooLong?: boolean
    tooShort?: boolean
    typeMismatch?: boolean
    valueMissing?: boolean
  }

  interface FocusOptions {
    preventScroll?: boolean
  }

  interface Range {
    readonly collapsed: boolean
    readonly commonAncestorContainer: Node
    readonly endContainer: Node
    readonly endOffset: number
    readonly startContainer: Node
    readonly startOffset: number
    cloneContents(): DocumentFragment
    cloneRange(): Range
    collapse(toStart?: boolean): void
    compareBoundaryPoints(how: number, sourceRange: Range): number
    comparePoint(node: Node, offset: number): number
    createContextualFragment(fragment: string): DocumentFragment
    deleteContents(): void
    detach(): void
    extractContents(): DocumentFragment
    getBoundingClientRect(): DOMRect
    getClientRects(): DOMRectList
    insertNode(node: Node): void
    intersectsNode(node: Node): boolean
    isPointInRange(node: Node, offset: number): boolean
    selectNode(node: Node): void
    selectNodeContents(node: Node): void
    setEnd(node: Node, offset: number): void
    setEndAfter(node: Node): void
    setEndBefore(node: Node): void
    setStart(node: Node, offset: number): void
    setStartAfter(node: Node): void
    setStartBefore(node: Node): void
    surroundContents(newParent: Node): void
    toString(): string
    readonly END_TO_END: number
    readonly END_TO_START: number
    readonly START_TO_END: number
    readonly START_TO_START: number
  }

  interface Document extends Node {
    readonly URL: string
    readonly anchors: HTMLCollectionOf<HTMLAnchorElement>
    readonly body: HTMLElement | null
    readonly characterSet: string
    readonly compatMode: string
    readonly contentType: string
    readonly currentScript: HTMLOrSVGScriptElement | null
    readonly defaultView: WindowProxy | null
    readonly designMode: string
    readonly dir: string
    readonly doctype: DocumentType | null
    readonly documentElement: HTMLElement
    readonly documentURI: string
    readonly domain: string
    readonly embeds: HTMLCollectionOf<HTMLEmbedElement>
    readonly forms: HTMLCollectionOf<HTMLFormElement>
    readonly fullscreenElement: Element | null
    readonly head: HTMLHeadElement | null
    readonly hidden: boolean
    readonly images: HTMLCollectionOf<HTMLImageElement>
    readonly implementation: DOMImplementation
    readonly inputEncoding: string
    readonly lastModified: string
    readonly links: HTMLCollectionOf<HTMLAnchorElement | HTMLAreaElement>
    readonly location: Location | null
    readonly plugins: HTMLCollectionOf<HTMLEmbedElement>
    readonly readyState: DocumentReadyState
    readonly referrer: string
    readonly scripts: HTMLCollectionOf<HTMLScriptElement>
    readonly scrollingElement: Element | null
    readonly timeline: DocumentTimeline
    title: string
    readonly visibilityState: DocumentVisibilityState
    adoptNode<T extends Node>(node: T): T
    append(...nodes: (Node | string)[]): void
    captureEvents(): void
    caretPositionFromPoint(x: number, y: number): CaretPosition | null
    caretRangeFromPoint(x: number, y: number): Range | null
    close(): void
    createAttribute(localName: string): Attr
    createAttributeNS(namespace: string | null, qualifiedName: string): Attr
    createCDATASection(data: string): CDATASection
    createComment(data: string): Comment
    createDocumentFragment(): DocumentFragment
    createElement<K extends keyof HTMLElementTagNameMap>(tagName: K, options?: ElementCreationOptions): HTMLElementTagNameMap[K]
    createElement<K extends keyof HTMLElementDeprecatedTagNameMap>(tagName: K, options?: ElementCreationOptions): HTMLElementDeprecatedTagNameMap[K]
    createElement(tagName: string, options?: ElementCreationOptions): HTMLElement
    createElementNS(namespace: string | null, qualifiedName: string, options?: ElementCreationOptions): Element
    createEvent(eventInterface: "AnimationEvent"): AnimationEvent
    createEvent(eventInterface: "AnimationPlaybackEvent"): AnimationPlaybackEvent
    createEvent(eventInterface: "AudioProcessingEvent"): AudioProcessingEvent
    createEvent(eventInterface: "BeforeUnloadEvent"): BeforeUnloadEvent
    createEvent(eventInterface: "BlobEvent"): BlobEvent
    createEvent(eventInterface: "ClipboardEvent"): ClipboardEvent
    createEvent(eventInterface: "CloseEvent"): CloseEvent
    createEvent(eventInterface: "CompositionEvent"): CompositionEvent
    createEvent(eventInterface: "CustomEvent"): CustomEvent
    createEvent(eventInterface: "DeviceMotionEvent"): DeviceMotionEvent
    createEvent(eventInterface: "DeviceOrientationEvent"): DeviceOrientationEvent
    createEvent(eventInterface: "DragEvent"): DragEvent
    createEvent(eventInterface: "ErrorEvent"): ErrorEvent
    createEvent(eventInterface: "Event"): Event
    createEvent(eventInterface: "Events"): Event
    createEvent(eventInterface: "FocusEvent"): FocusEvent
    createEvent(eventInterface: "FocusNavigationEvent"): FocusNavigationEvent
    createEvent(eventInterface: "FormDataEvent"): FormDataEvent
    createEvent(eventInterface: "GamepadEvent"): GamepadEvent
    createEvent(eventInterface: "HashChangeEvent"): HashChangeEvent
    createEvent(eventInterface: "IDBVersionChangeEvent"): IDBVersionChangeEvent
    createEvent(eventInterface: "InputEvent"): InputEvent
    createEvent(eventInterface: "KeyboardEvent"): KeyboardEvent
    createEvent(eventInterface: "MediaEncryptedEvent"): MediaEncryptedEvent
    createEvent(eventInterface: "MediaKeyMessageEvent"): MediaKeyMessageEvent
    createEvent(eventInterface: "MediaQueryListEvent"): MediaQueryListEvent
    createEvent(eventInterface: "MediaStreamTrackEvent"): MediaStreamTrackEvent
    createEvent(eventInterface: "MessageEvent"): MessageEvent
    createEvent(eventInterface: "MouseEvent"): MouseEvent
    createEvent(eventInterface: "MouseEvents"): MouseEvent
    createEvent(eventInterface: "MutationEvent"): MutationEvent
    createEvent(eventInterface: "MutationEvents"): MutationEvent
    createEvent(eventInterface: "OfflineAudioCompletionEvent"): OfflineAudioCompletionEvent
    createEvent(eventInterface: "PageTransitionEvent"): PageTransitionEvent
    createEvent(eventInterface: "PaymentMethodChangeEvent"): PaymentMethodChangeEvent
    createEvent(eventInterface: "PaymentRequestUpdateEvent"): PaymentRequestUpdateEvent
    createEvent(eventInterface: "PointerEvent"): PointerEvent
    createEvent(eventInterface: "PopStateEvent"): PopStateEvent
    createEvent(eventInterface: "ProgressEvent"): ProgressEvent
    createEvent(eventInterface: "PromiseRejectionEvent"): PromiseRejectionEvent
    createEvent(eventInterface: "RTCDataChannelEvent"): RTCDataChannelEvent
    createEvent(eventInterface: "RTCDTMFToneChangeEvent"): RTCDTMFToneChangeEvent
    createEvent(eventInterface: "RTCPeerConnectionIceEvent"): RTCPeerConnectionIceEvent
    createEvent(eventInterface: "RTCTrackEvent"): RTCTrackEvent
    createEvent(eventInterface: "SecurityPolicyViolationEvent"): SecurityPolicyViolationEvent
    createEvent(eventInterface: "SpeechSynthesisEvent"): SpeechSynthesisEvent
    createEvent(eventInterface: "StorageEvent"): StorageEvent
    createEvent(eventInterface: "SubmitEvent"): SubmitEvent
    createEvent(eventInterface: "TouchEvent"): TouchEvent
    createEvent(eventInterface: "TrackEvent"): TrackEvent
    createEvent(eventInterface: "TransitionEvent"): TransitionEvent
    createEvent(eventInterface: "UIEvent"): UIEvent
    createEvent(eventInterface: "UIEvents"): UIEvent
    createEvent(eventInterface: "WebGLContextEvent"): WebGLContextEvent
    createEvent(eventInterface: "WheelEvent"): WheelEvent
    createEvent(eventInterface: string): Event
    createNodeIterator(root: Node, whatToShow?: number, filter?: NodeFilter | null): NodeIterator
    createProcessingInstruction(target: string, data: string): ProcessingInstruction
    createRange(): Range
    createTextNode(data: string): Text
    createTreeWalker(root: Node, whatToShow?: number, filter?: NodeFilter | null): TreeWalker
    execCommand(commandId: string, showUI?: boolean, value?: string): boolean
    exitFullscreen(): Promise<void>
    exitPictureInPicture(): Promise<void>
    exitPointerLock(): void
    getElementById(elementId: string): HTMLElement | null
    getElementsByClassName(classNames: string): HTMLCollectionOf<Element>
    getElementsByName(elementName: string): NodeListOf<HTMLElement>
    getElementsByTagName<K extends keyof HTMLElementTagNameMap>(qualifiedName: K): HTMLCollectionOf<HTMLElementTagNameMap[K]>
    getElementsByTagName<K extends keyof SVGElementTagNameMap>(qualifiedName: K): HTMLCollectionOf<SVGElementTagNameMap[K]>
    getElementsByTagName(qualifiedName: string): HTMLCollectionOf<Element>
    getElementsByTagNameNS(namespace: string | null, localName: string): HTMLCollectionOf<Element>
    getSelection(): Selection | null
    hasFocus(): boolean
    hasStorageAccess(): Promise<boolean>
    importNode<T extends Node>(node: T, deep?: boolean): T
    open(unused1?: string, unused2?: string): Document
    open(url: string | URL, name: string, features: string): WindowProxy | null
    prepend(...nodes: (Node | string)[]): void
    queryCommandEnabled(commandId: string): boolean
    queryCommandIndeterm(commandId: string): boolean
    queryCommandState(commandId: string): boolean
    queryCommandSupported(commandId: string): boolean
    queryCommandValue(commandId: string): string
    querySelector<K extends keyof HTMLElementTagNameMap>(selectors: K): HTMLElementTagNameMap[K] | null
    querySelector<K extends keyof SVGElementTagNameMap>(selectors: K): SVGElementTagNameMap[K] | null
    querySelector<E extends Element = Element>(selectors: string): E | null
    querySelectorAll<K extends keyof HTMLElementTagNameMap>(selectors: K): NodeListOf<HTMLElementTagNameMap[K]>
    querySelectorAll<K extends keyof SVGElementTagNameMap>(selectors: K): NodeListOf<SVGElementTagNameMap[K]>
    querySelectorAll<E extends Element = Element>(selectors: string): NodeListOf<E>
    releaseEvents(): void
    replaceChildren(...nodes: (Node | string)[]): void
    requestStorageAccess(): Promise<void>
    write(...text: string[]): void
    writeln(...text: string[]): void
    addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void
    addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void
    removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void
    removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void
  }

  interface DocumentEventMap extends GlobalEventHandlersEventMap {
    "DOMContentLoaded": Event
    "fullscreenchange": Event
    "fullscreenerror": Event
    "pointerlockchange": Event
    "pointerlockerror": Event
    "readystatechange": Event
    "visibilitychange": Event
  }

  // Additional missing types for Document API
  type DocumentReadyState = "complete" | "interactive" | "loading"
  type DocumentVisibilityState = "hidden" | "visible"

  interface DocumentType extends Node {
    readonly name: string
    readonly publicId: string
    readonly systemId: string
  }

  interface DOMImplementation {
    createDocument(namespace: string | null, qualifiedName: string | null, doctype?: DocumentType | null): XMLDocument
    createDocumentType(qualifiedName: string, publicId: string, systemId: string): DocumentType
    createHTMLDocument(title?: string): Document
    hasFeature(): boolean
  }

  interface XMLDocument extends Document {
    // Placeholder for XMLDocument - extends Document
    [key: string]: any
  }

  interface DocumentTimeline {
    readonly currentTime: number | null
  }

  interface CaretPosition {
    readonly offset: number
    readonly offsetNode: Node
    getClientRect(): DOMRect | null
  }

  interface CDATASection extends Text {
    // Placeholder for CDATASection - extends Text
    [key: string]: any
  }

  interface Comment extends CharacterData {
    // Placeholder for Comment - extends CharacterData
    [key: string]: any
  }

  interface CharacterData extends Node {
    data: string
    readonly length: number
    readonly nextElementSibling: Element | null
    readonly previousElementSibling: Element | null
    after(...nodes: (Node | string)[]): void
    appendData(data: string): void
    before(...nodes: (Node | string)[]): void
    deleteData(offset: number, count: number): void
    insertData(offset: number, data: string): void
    remove(): void
    replaceData(offset: number, count: number, data: string): void
    replaceWith(...nodes: (Node | string)[]): void
    substringData(offset: number, count: number): string
  }

  interface Text extends CharacterData {
    readonly assignedSlot: HTMLSlotElement | null
    readonly wholeText: string
    splitText(offset: number): Text
  }

  interface ProcessingInstruction extends CharacterData {
    readonly sheet: CSSStyleSheet | null
    readonly target: string
  }

  interface CSSStyleSheet extends StyleSheet {
    readonly cssRules: CSSRuleList
    readonly ownerRule: CSSRule | null
    readonly rules: CSSRuleList
    addRule(selector?: string, style?: string, index?: number): number
    deleteRule(index: number): void
    insertRule(rule: string, index?: number): number
    removeRule(index?: number): void
  }

  interface StyleSheet {
    disabled: boolean
    readonly href: string | null
    readonly media: MediaList
    readonly ownerNode: Element | ProcessingInstruction | null
    readonly parentStyleSheet: CSSStyleSheet | null
    readonly title: string | null
    readonly type: string
  }

  interface CSSRuleList {
    readonly length: number
    item(index: number): CSSRule | null
    [index: number]: CSSRule
  }

  interface CSSRule {
    cssText: string
    readonly parentRule: CSSRule | null
    readonly parentStyleSheet: CSSStyleSheet | null
    readonly type: number
    readonly CHARSET_RULE: number
    readonly FONT_FACE_RULE: number
    readonly IMPORT_RULE: number
    readonly KEYFRAMES_RULE: number
    readonly KEYFRAME_RULE: number
    readonly MEDIA_RULE: number
    readonly NAMESPACE_RULE: number
    readonly PAGE_RULE: number
    readonly STYLE_RULE: number
    readonly SUPPORTS_RULE: number
  }

  interface MediaList {
    readonly length: number
    mediaText: string
    appendMedium(medium: string): void
    deleteMedium(medium: string): void
    item(index: number): string | null
    [index: number]: string
  }

  interface ElementCreationOptions {
    is?: string
  }

  interface HTMLElementDeprecatedTagNameMap {
    "listing": HTMLPreElement
    "xmp": HTMLPreElement
  }

  type HTMLOrSVGScriptElement = HTMLScriptElement | SVGScriptElement

  interface SVGScriptElement extends SVGElement {
    type: string
  }

  // Node Iterator and Tree Walker
  interface NodeIterator {
    readonly filter: NodeFilter | null
    readonly pointerBeforeReferenceNode: boolean
    readonly referenceNode: Node
    readonly root: Node
    readonly whatToShow: number
    detach(): void
    nextNode(): Node | null
    previousNode(): Node | null
  }

  interface TreeWalker {
    currentNode: Node
    readonly filter: NodeFilter | null
    readonly root: Node
    readonly whatToShow: number
    firstChild(): Node | null
    lastChild(): Node | null
    nextNode(): Node | null
    nextSibling(): Node | null
    parentNode(): Node | null
    previousNode(): Node | null
    previousSibling(): Node | null
  }

  interface NodeFilter {
    acceptNode(node: Node): number
    readonly FILTER_ACCEPT: number
    readonly FILTER_REJECT: number
    readonly FILTER_SKIP: number
    readonly SHOW_ALL: number
    readonly SHOW_ATTRIBUTE: number
    readonly SHOW_CDATA_SECTION: number
    readonly SHOW_COMMENT: number
    readonly SHOW_DOCUMENT: number
    readonly SHOW_DOCUMENT_FRAGMENT: number
    readonly SHOW_DOCUMENT_TYPE: number
    readonly SHOW_ELEMENT: number
    readonly SHOW_ENTITY: number
    readonly SHOW_ENTITY_REFERENCE: number
    readonly SHOW_NOTATION: number
    readonly SHOW_PROCESSING_INSTRUCTION: number
    readonly SHOW_TEXT: number
  }

  // Additional Event types that might be missing
  interface AnimationPlaybackEvent extends Event {
    readonly currentTime: number | null
    readonly timelineTime: number | null
  }

  interface AudioProcessingEvent extends Event {
    readonly inputBuffer: AudioBuffer
    readonly outputBuffer: AudioBuffer
    readonly playbackTime: number
  }

  interface BlobEvent extends Event {
    readonly data: Blob
    readonly timecode: number
  }

  interface CloseEvent extends Event {
    readonly code: number
    readonly reason: string
    readonly wasClean: boolean
  }

  interface CompositionEvent extends UIEvent {
    readonly data: string
  }

  interface CustomEvent<T = any> extends Event {
    readonly detail: T
    initCustomEvent(type: string, bubbles?: boolean, cancelable?: boolean, detail?: T): void
  }

  interface DeviceMotionEvent extends Event {
    readonly acceleration: DeviceMotionEventAcceleration | null
    readonly accelerationIncludingGravity: DeviceMotionEventAcceleration | null
    readonly interval: number
    readonly rotationRate: DeviceMotionEventRotationRate | null
  }

  interface DeviceMotionEventAcceleration {
    readonly x: number | null
    readonly y: number | null
    readonly z: number | null
  }

  interface DeviceMotionEventRotationRate {
    readonly alpha: number | null
    readonly beta: number | null
    readonly gamma: number | null
  }

  interface DeviceOrientationEvent extends Event {
    readonly absolute: boolean
    readonly alpha: number | null
    readonly beta: number | null
    readonly gamma: number | null
  }

  interface FocusNavigationEvent extends Event {
    readonly navigationReason: string
    readonly originHeight: number
    readonly originLeft: number
    readonly originTop: number
    readonly originWidth: number
  }

  interface GamepadEvent extends Event {
    readonly gamepad: Gamepad
  }

  interface InputEvent extends UIEvent {
    readonly data: string | null
    readonly dataTransfer: DataTransfer | null
    readonly inputType: string
    readonly isComposing: boolean
    getTargetRanges(): StaticRange[]
  }

  interface StaticRange {
    readonly collapsed: boolean
    readonly endContainer: Node
    readonly endOffset: number
    readonly startContainer: Node
    readonly startOffset: number
  }

  interface MediaEncryptedEvent extends Event {
    readonly initData: ArrayBuffer | null
    readonly initDataType: string
  }

  interface MediaKeyMessageEvent extends Event {
    readonly message: ArrayBuffer
    readonly messageType: MediaKeyMessageType
  }

  type MediaKeyMessageType = "individualization-request" | "license-release" | "license-renewal" | "license-request"

  interface MediaStreamTrackEvent extends Event {
    readonly track: MediaStreamTrack
  }

  interface MutationEvent extends Event {
    readonly attrChange: number
    readonly attrName: string
    readonly newValue: string
    readonly prevValue: string
    readonly relatedNode: Node
    initMutationEvent(type: string, bubbles?: boolean, cancelable?: boolean, relatedNode?: Node, prevValue?: string, newValue?: string, attrName?: string, attrChange?: number): void
    readonly ADDITION: number
    readonly MODIFICATION: number
    readonly REMOVAL: number
  }

  interface OfflineAudioCompletionEvent extends Event {
    readonly renderedBuffer: AudioBuffer
  }

  interface PaymentMethodChangeEvent extends Event {
    readonly methodDetails: any
    readonly methodName: string
  }

  interface PaymentRequestUpdateEvent extends Event {
    updateWith(detailsPromise: Promise<PaymentDetailsUpdate>): void
  }

  interface PaymentDetailsUpdate {
    displayItems?: PaymentItem[]
    error?: string
    modifiers?: PaymentDetailsModifier[]
    shippingAddressErrors?: AddressErrors
    shippingOptions?: PaymentShippingOption[]
    total?: PaymentItem
  }

  // Payment API types
  interface PaymentItem {
    amount: PaymentCurrencyAmount
    label: string
    pending?: boolean
  }

  interface PaymentCurrencyAmount {
    currency: string
    value: string
  }

  interface PaymentDetailsModifier {
    additionalDisplayItems?: PaymentItem[]
    data?: any
    supportedMethods: string
    total?: PaymentItem
  }

  interface AddressErrors {
    addressLine?: string
    city?: string
    country?: string
    dependentLocality?: string
    organization?: string
    phone?: string
    postalCode?: string
    recipient?: string
    region?: string
    sortingCode?: string
  }

  interface PaymentShippingOption {
    amount: PaymentCurrencyAmount
    id: string
    label: string
    selected?: boolean
  }



  interface RTCDataChannelEvent extends Event {
    readonly channel: RTCDataChannel
  }

  interface RTCDTMFToneChangeEvent extends Event {
    readonly tone: string
  }

  interface RTCPeerConnectionIceEvent extends Event {
    readonly candidate: RTCIceCandidate | null
    readonly url: string | null
  }

  interface RTCTrackEvent extends Event {
    readonly receiver: RTCRtpReceiver
    readonly streams: ReadonlyArray<MediaStream>
    readonly track: MediaStreamTrack
    readonly transceiver: RTCRtpTransceiver
  }

  // RTC API types
  interface RTCDataChannel extends EventTarget {
    readonly binaryType: string
    readonly bufferedAmount: number
    readonly bufferedAmountLowThreshold: number
    readonly id: number | null
    readonly label: string
    readonly maxPacketLifeTime: number | null
    readonly maxRetransmits: number | null
    readonly negotiated: boolean
    readonly ordered: boolean
    readonly protocol: string
    readonly readyState: RTCDataChannelState
    onbufferedamountlow: ((this: RTCDataChannel, ev: Event) => any) | null
    onclose: ((this: RTCDataChannel, ev: Event) => any) | null
    onerror: ((this: RTCDataChannel, ev: Event) => any) | null
    onmessage: ((this: RTCDataChannel, ev: MessageEvent) => any) | null
    onopen: ((this: RTCDataChannel, ev: Event) => any) | null
    close(): void
    send(data: string): void
    send(data: Blob): void
    send(data: ArrayBuffer): void
    send(data: ArrayBufferView): void
  }

  type RTCDataChannelState = "closed" | "closing" | "connecting" | "open"

  interface RTCIceCandidate {
    readonly candidate: string
    readonly component: RTCIceComponent | null
    readonly foundation: string | null
    readonly port: number | null
    readonly priority: number | null
    readonly protocol: RTCIceProtocol | null
    readonly relatedAddress: string | null
    readonly relatedPort: number | null
    readonly sdpMLineIndex: number | null
    readonly sdpMid: string | null
    readonly tcpType: RTCIceTcpCandidateType | null
    readonly type: RTCIceCandidateType | null
    readonly usernameFragment: string | null
    toJSON(): RTCIceCandidateInit
  }

  type RTCIceComponent = "rtcp" | "rtp"
  type RTCIceProtocol = "tcp" | "udp"
  type RTCIceTcpCandidateType = "active" | "passive" | "so"
  type RTCIceCandidateType = "host" | "prflx" | "relay" | "srflx"

  interface RTCIceCandidateInit {
    candidate?: string
    sdpMLineIndex?: number | null
    sdpMid?: string | null
    usernameFragment?: string | null
  }

  interface RTCRtpReceiver {
    readonly track: MediaStreamTrack
    readonly transport: RTCDtlsTransport | null
    getCapabilities(kind: string): RTCRtpCapabilities | null
    getContributingSources(): RTCRtpContributingSource[]
    getParameters(): RTCRtpReceiveParameters
    getStats(): Promise<RTCStatsReport>
    getSynchronizationSources(): RTCRtpSynchronizationSource[]
  }

  interface RTCRtpTransceiver {
    readonly currentDirection: RTCRtpTransceiverDirection | null
    direction: RTCRtpTransceiverDirection
    readonly mid: string | null
    readonly receiver: RTCRtpReceiver
    readonly sender: RTCRtpSender
    readonly stopped: boolean
    setCodecPreferences(codecs: RTCRtpCodecCapability[]): void
    stop(): void
  }

  type RTCRtpTransceiverDirection = "inactive" | "recvonly" | "sendonly" | "sendrecv"

  interface RTCDtlsTransport extends EventTarget {
    readonly iceTransport: RTCIceTransport
    readonly state: RTCDtlsTransportState
    onstatechange: ((this: RTCDtlsTransport, ev: Event) => any) | null
    onerror: ((this: RTCDtlsTransport, ev: Event) => any) | null
    getRemoteCertificates(): ArrayBuffer[]
  }

  type RTCDtlsTransportState = "closed" | "connected" | "connecting" | "failed" | "new"

  interface RTCIceTransport extends EventTarget {
    readonly component: RTCIceComponent
    readonly gatheringState: RTCIceGatheringState
    readonly role: RTCIceRole
    readonly state: RTCIceTransportState
    ongatheringstatechange: ((this: RTCIceTransport, ev: Event) => any) | null
    onselectedcandidatepairchange: ((this: RTCIceTransport, ev: Event) => any) | null
    onstatechange: ((this: RTCIceTransport, ev: Event) => any) | null
    getLocalCandidates(): RTCIceCandidate[]
    getLocalParameters(): RTCIceParameters | null
    getRemoteCandidates(): RTCIceCandidate[]
    getRemoteParameters(): RTCIceParameters | null
    getSelectedCandidatePair(): RTCIceCandidatePair | null
  }

  type RTCIceGatheringState = "complete" | "gathering" | "new"
  type RTCIceRole = "controlled" | "controlling" | "unknown"
  type RTCIceTransportState = "checking" | "closed" | "completed" | "connected" | "disconnected" | "failed" | "new"

  // Additional RTC API types
  interface RTCRtpCapabilities {
    codecs: RTCRtpCodecCapability[]
    headerExtensions: RTCRtpHeaderExtensionCapability[]
  }

  interface RTCRtpCodecCapability {
    channels?: number
    clockRate: number
    mimeType: string
    parameters?: Record<string, string>
    sdpFmtpLine?: string
  }

  interface RTCRtpHeaderExtensionCapability {
    uri: string
  }

  interface RTCRtpContributingSource {
    readonly audioLevel: number | null
    readonly rtpTimestamp: number
    readonly source: number
    readonly timestamp: number
  }

  interface RTCRtpReceiveParameters {
    codecs: RTCRtpCodecParameters[]
    headerExtensions: RTCRtpHeaderExtensionParameters[]
    rtcp: RTCRtcpParameters
  }

  interface RTCRtpCodecParameters {
    channels?: number
    clockRate: number
    mimeType: string
    parameters?: Record<string, string>
    payloadType: number
    sdpFmtpLine?: string
  }

  interface RTCRtpHeaderExtensionParameters {
    encrypted?: boolean
    id: number
    uri: string
  }

  interface RTCRtcpParameters {
    cname?: string
    reducedSize?: boolean
  }

  interface RTCStatsReport {
    readonly size: number
    forEach(callbackfn: (value: any, key: string, parent: RTCStatsReport) => void, thisArg?: any): void
    get(key: string): any
    has(key: string): boolean
    keys(): IterableIterator<string>
    values(): IterableIterator<any>
    entries(): IterableIterator<[string, any]>
    [Symbol.iterator](): IterableIterator<[string, any]>
  }

  interface RTCRtpSynchronizationSource {
    readonly audioLevel: number | null
    readonly rtpTimestamp: number
    readonly source: number
    readonly timestamp: number
    readonly voiceActivityFlag?: boolean
  }

  interface RTCRtpSender {
    readonly dtmf: RTCDTMFSender | null
    readonly track: MediaStreamTrack | null
    readonly transport: RTCDtlsTransport | null
    getCapabilities(kind: string): RTCRtpCapabilities | null
    getParameters(): RTCRtpSendParameters
    getStats(): Promise<RTCStatsReport>
    replaceTrack(withTrack: MediaStreamTrack | null): Promise<void>
    setParameters(parameters: RTCRtpSendParameters): Promise<void>
    setStreams(...streams: MediaStream[]): void
  }

  interface RTCDTMFSender extends EventTarget {
    readonly canInsertDTMF: boolean
    readonly duration: number
    readonly interToneGap: number
    ontonechange: ((this: RTCDTMFSender, ev: RTCDTMFToneChangeEvent) => any) | null
    readonly toneBuffer: string
    insertDTMF(tones: string, duration?: number, interToneGap?: number): void
  }

  interface RTCRtpSendParameters {
    codecs: RTCRtpCodecParameters[]
    degradationPreference?: RTCDegradationPreference
    encodings: RTCRtpEncodingParameters[]
    headerExtensions: RTCRtpHeaderExtensionParameters[]
    rtcp: RTCRtcpParameters
    transactionId: string
  }

  type RTCDegradationPreference = "balanced" | "maintain-framerate" | "maintain-resolution"

  interface RTCRtpEncodingParameters {
    active?: boolean
    codecPayloadType?: number
    dtx?: RTCDtxStatus
    maxBitrate?: number
    maxFramerate?: number
    priority?: RTCPriorityType
    rid?: string
    scaleResolutionDownBy?: number
  }

  type RTCDtxStatus = "disabled" | "enabled"
  type RTCPriorityType = "high" | "low" | "medium" | "very-low"

  interface RTCIceParameters {
    password: string
    usernameFragment: string
  }

  interface RTCIceCandidatePair {
    local?: RTCIceCandidate
    remote?: RTCIceCandidate
  }

  interface TrackEvent extends Event {
    readonly track: TextTrack | null
  }

  interface WebGLContextEvent extends Event {
    readonly statusMessage: string
  }

  // TextTrack API
  interface TextTrack extends EventTarget {
    readonly activeCues: TextTrackCueList | null
    readonly cues: TextTrackCueList | null
    readonly id: string
    readonly inBandMetadataTrackDispatchType: string
    readonly kind: TextTrackKind
    readonly label: string
    readonly language: string
    mode: TextTrackMode
    readonly sourceBuffer: SourceBuffer | null
    oncuechange: ((this: TextTrack, ev: Event) => any) | null
    addCue(cue: TextTrackCue): void
    removeCue(cue: TextTrackCue): void
  }

  type TextTrackKind = "captions" | "chapters" | "descriptions" | "metadata" | "subtitles"
  type TextTrackMode = "disabled" | "hidden" | "showing"

  interface TextTrackCueList {
    readonly length: number
    getCueById(id: string): TextTrackCue | null
    item(index: number): TextTrackCue | null
    [index: number]: TextTrackCue
  }

  interface TextTrackCue extends EventTarget {
    endTime: number
    id: string
    onenter: ((this: TextTrackCue, ev: Event) => any) | null
    onexit: ((this: TextTrackCue, ev: Event) => any) | null
    pauseOnExit: boolean
    startTime: number
    readonly track: TextTrack | null
  }

  interface SourceBuffer extends EventTarget {
    appendWindowEnd: number
    appendWindowStart: number
    readonly audioTracks: AudioTrackList
    readonly buffered: TimeRanges
    mode: AppendMode
    onabort: ((this: SourceBuffer, ev: Event) => any) | null
    onerror: ((this: SourceBuffer, ev: Event) => any) | null
    onupdate: ((this: SourceBuffer, ev: Event) => any) | null
    onupdateend: ((this: SourceBuffer, ev: Event) => any) | null
    onupdatestart: ((this: SourceBuffer, ev: Event) => any) | null
    timestampOffset: number
    readonly updating: boolean
    readonly videoTracks: VideoTrackList
    abort(): void
    appendBuffer(data: BufferSource): void
    changeType(type: string): void
    remove(start: number, end: number): void
  }

  type AppendMode = "segments" | "sequence"

  interface AudioTrackList extends EventTarget {
    readonly length: number
    onaddtrack: ((this: AudioTrackList, ev: TrackEvent) => any) | null
    onchange: ((this: AudioTrackList, ev: Event) => any) | null
    onremovetrack: ((this: AudioTrackList, ev: TrackEvent) => any) | null
    getTrackById(id: string): AudioTrack | null
    item(index: number): AudioTrack | null
    [index: number]: AudioTrack
  }

  interface VideoTrackList extends EventTarget {
    readonly length: number
    onaddtrack: ((this: VideoTrackList, ev: TrackEvent) => any) | null
    onchange: ((this: VideoTrackList, ev: Event) => any) | null
    onremovetrack: ((this: VideoTrackList, ev: TrackEvent) => any) | null
    readonly selectedIndex: number
    getTrackById(id: string): VideoTrack | null
    item(index: number): VideoTrack | null
    [index: number]: VideoTrack
  }

  interface AudioTrack {
    enabled: boolean
    readonly id: string
    readonly kind: string
    readonly label: string
    readonly language: string
    readonly sourceBuffer: SourceBuffer | null
  }

  interface VideoTrack {
    readonly id: string
    readonly kind: string
    readonly label: string
    readonly language: string
    selected: boolean
    readonly sourceBuffer: SourceBuffer | null
  }

  interface TimeRanges {
    readonly length: number
    end(index: number): number
    start(index: number): number
  }

  // ===== تعريفات إضافية مفقودة =====

  // Missing types for various APIs
  type TimerHandler = string | ((...args: any[]) => void)
  type VoidFunction = () => void
  type ImageBitmapSource = CanvasImageSource | Blob | ImageData
  type CanvasImageSource = HTMLOrSVGImageElement | HTMLVideoElement | HTMLCanvasElement | ImageBitmap | OffscreenCanvas
  type HTMLOrSVGImageElement = HTMLImageElement | SVGImageElement
  type HTMLOrSVGScriptElement = HTMLScriptElement | SVGScriptElement
  type RequestInfo = Request | string

  interface ImageBitmapOptions {
    colorSpaceConversion?: ColorSpaceConversion
    imageOrientation?: ImageOrientation
    premultiplyAlpha?: PremultiplyAlpha
    resizeHeight?: number
    resizeQuality?: ResizeQuality
    resizeWidth?: number
  }

  type ColorSpaceConversion = "default" | "none"
  type ImageOrientation = "flipY" | "from-image" | "none"
  type PremultiplyAlpha = "default" | "none" | "premultiply"
  type ResizeQuality = "high" | "low" | "medium" | "pixelated"

  interface ImageBitmap {
    readonly height: number
    readonly width: number
    close(): void
  }

  interface ImageData {
    readonly data: Uint8ClampedArray
    readonly height: number
    readonly width: number
  }

  interface StructuredSerializeOptions {
    transfer?: Transferable[]
  }

  interface RequestInit {
    body?: BodyInit | null
    cache?: RequestCache
    credentials?: RequestCredentials
    headers?: HeadersInit
    integrity?: string
    keepalive?: boolean
    method?: string
    mode?: RequestMode
    redirect?: RequestRedirect
    referrer?: string
    referrerPolicy?: ReferrerPolicy
    signal?: AbortSignal | null
    window?: any
  }

  type RequestCache = "default" | "force-cache" | "no-cache" | "no-store" | "only-if-cached" | "reload"
  type RequestCredentials = "include" | "omit" | "same-origin"
  type RequestMode = "cors" | "navigate" | "no-cors" | "same-origin"
  type RequestRedirect = "error" | "follow" | "manual"
  type ReferrerPolicy = "" | "no-referrer" | "no-referrer-when-downgrade" | "origin" | "origin-when-cross-origin" | "same-origin" | "strict-origin" | "strict-origin-when-cross-origin" | "unsafe-url"
  type HeadersInit = Headers | string[][] | Record<string, string>

  interface Request extends Body {
    readonly cache: RequestCache
    readonly credentials: RequestCredentials
    readonly destination: RequestDestination
    readonly headers: Headers
    readonly integrity: string
    readonly isHistoryNavigation: boolean
    readonly isReloadNavigation: boolean
    readonly keepalive: boolean
    readonly method: string
    readonly mode: RequestMode
    readonly redirect: RequestRedirect
    readonly referrer: string
    readonly referrerPolicy: ReferrerPolicy
    readonly signal: AbortSignal
    readonly url: string
    clone(): Request
  }

  type RequestDestination = "" | "audio" | "audioworklet" | "document" | "embed" | "font" | "frame" | "iframe" | "image" | "manifest" | "object" | "paintworklet" | "report" | "script" | "serviceworker" | "sharedworker" | "style" | "track" | "video" | "worker" | "xslt"

  interface Response extends Body {
    readonly headers: Headers
    readonly ok: boolean
    readonly redirected: boolean
    readonly status: number
    readonly statusText: string
    readonly type: ResponseType
    readonly url: string
    clone(): Response
  }

  interface ResponseConstructor {
    new(body?: BodyInit | null, init?: ResponseInit): Response
    error(): Response
    redirect(url: string | URL, status?: number): Response
  }

  declare const Response: ResponseConstructor

  type ResponseType = "basic" | "cors" | "default" | "error" | "opaque" | "opaqueredirect"

  interface Body {
    readonly body: ReadableStream<Uint8Array> | null
    readonly bodyUsed: boolean
    arrayBuffer(): Promise<ArrayBuffer>
    blob(): Promise<Blob>
    formData(): Promise<FormData>
    json(): Promise<any>
    text(): Promise<string>
  }

  interface AbortSignal extends EventTarget {
    readonly aborted: boolean
    readonly reason: any
    onabort: ((this: AbortSignal, ev: Event) => any) | null
    throwIfAborted(): void
  }

  interface FormData {
    append(name: string, value: string | Blob, fileName?: string): void
    delete(name: string): void
    get(name: string): FormDataEntryValue | null
    getAll(name: string): FormDataEntryValue[]
    has(name: string): boolean
    set(name: string, value: string | Blob, fileName?: string): void
    forEach(callbackfn: (value: FormDataEntryValue, key: string, parent: FormData) => void, thisArg?: any): void
  }

  type FormDataEntryValue = File | string

  interface URLSearchParams {
    append(name: string, value: string): void
    delete(name: string): void
    get(name: string): string | null
    getAll(name: string): string[]
    has(name: string): boolean
    set(name: string, value: string): void
    sort(): void
    toString(): string
    forEach(callbackfn: (value: string, key: string, parent: URLSearchParams) => void, thisArg?: any): void
  }

  // Additional missing types
  type CanPlayTypeResult = "" | "maybe" | "probably"
  type BlobCallback = (blob: Blob | null) => void
  type RenderingContext = CanvasRenderingContext2D | WebGLRenderingContext | WebGL2RenderingContext

  interface CanvasRenderingContext2DSettings {
    alpha?: boolean
    colorSpace?: PredefinedColorSpace
    desynchronized?: boolean
    willReadFrequently?: boolean
  }

  type PredefinedColorSpace = "display-p3" | "rec2020" | "srgb"

  interface WebGLContextAttributes {
    alpha?: boolean
    antialias?: boolean
    depth?: boolean
    desynchronized?: boolean
    failIfMajorPerformanceCaveat?: boolean
    powerPreference?: WebGLPowerPreference
    premultipliedAlpha?: boolean
    preserveDrawingBuffer?: boolean
    stencil?: boolean
  }

  type WebGLPowerPreference = "default" | "high-performance" | "low-power"

  interface CanvasRenderingContext2D {
    readonly canvas: HTMLCanvasElement
    direction: CanvasDirection
    fillStyle: string | CanvasGradient | CanvasPattern
    filter: string
    font: string
    fontKerning: CanvasFontKerning
    fontStretch: CanvasFontStretch
    fontVariantCaps: CanvasFontVariantCaps
    globalAlpha: number
    globalCompositeOperation: GlobalCompositeOperation
    imageSmoothingEnabled: boolean
    imageSmoothingQuality: ImageSmoothingQuality
    letterSpacing: string
    lineCap: CanvasLineCap
    lineDashOffset: number
    lineJoin: CanvasLineJoin
    lineWidth: number
    miterLimit: number
    shadowBlur: number
    shadowColor: string
    shadowOffsetX: number
    shadowOffsetY: number
    strokeStyle: string | CanvasGradient | CanvasPattern
    textAlign: CanvasTextAlign
    textBaseline: CanvasTextBaseline
    textRendering: CanvasTextRendering
    wordSpacing: string
    arc(x: number, y: number, radius: number, startAngle: number, endAngle: number, counterclockwise?: boolean): void
    arcTo(x1: number, y1: number, x2: number, y2: number, radius: number): void
    beginPath(): void
    bezierCurveTo(cp1x: number, cp1y: number, cp2x: number, cp2y: number, x: number, y: number): void
    clearRect(x: number, y: number, w: number, h: number): void
    clip(fillRule?: CanvasFillRule): void
    clip(path: Path2D, fillRule?: CanvasFillRule): void
    closePath(): void
    createConicGradient(startAngle: number, x: number, y: number): CanvasGradient
    createImageData(sw: number, sh: number, settings?: ImageDataSettings): ImageData
    createImageData(imagedata: ImageData): ImageData
    createLinearGradient(x0: number, y0: number, x1: number, y1: number): CanvasGradient
    createPattern(image: CanvasImageSource, repetition: string | null): CanvasPattern | null
    createRadialGradient(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number): CanvasGradient
    drawImage(image: CanvasImageSource, dx: number, dy: number): void
    drawImage(image: CanvasImageSource, dx: number, dy: number, dw: number, dh: number): void
    drawImage(image: CanvasImageSource, sx: number, sy: number, sw: number, sh: number, dx: number, dy: number, dw: number, dh: number): void
    ellipse(x: number, y: number, radiusX: number, radiusY: number, rotation: number, startAngle: number, endAngle: number, counterclockwise?: boolean): void
    fill(fillRule?: CanvasFillRule): void
    fill(path: Path2D, fillRule?: CanvasFillRule): void
    fillRect(x: number, y: number, w: number, h: number): void
    fillText(text: string, x: number, y: number, maxWidth?: number): void
    getImageData(sx: number, sy: number, sw: number, sh: number, settings?: ImageDataSettings): ImageData
    getLineDash(): number[]
    getTransform(): DOMMatrix
    isPointInPath(x: number, y: number, fillRule?: CanvasFillRule): boolean
    isPointInPath(path: Path2D, x: number, y: number, fillRule?: CanvasFillRule): boolean
    isPointInStroke(x: number, y: number): boolean
    isPointInStroke(path: Path2D, x: number, y: number): boolean
    lineTo(x: number, y: number): void
    measureText(text: string): TextMetrics
    moveTo(x: number, y: number): void
    putImageData(imagedata: ImageData, dx: number, dy: number): void
    putImageData(imagedata: ImageData, dx: number, dy: number, dirtyX: number, dirtyY: number, dirtyWidth: number, dirtyHeight: number): void
    quadraticCurveTo(cpx: number, cpy: number, x: number, y: number): void
    rect(x: number, y: number, w: number, h: number): void
    resetTransform(): void
    restore(): void
    rotate(angle: number): void
    roundRect(x: number, y: number, w: number, h: number, radii?: number | DOMPointInit | (number | DOMPointInit)[]): void
    save(): void
    scale(x: number, y: number): void
    setLineDash(segments: number[]): void
    setTransform(a: number, b: number, c: number, d: number, e: number, f: number): void
    setTransform(transform?: DOMMatrix2DInit): void
    stroke(): void
    stroke(path: Path2D): void
    strokeRect(x: number, y: number, w: number, h: number): void
    strokeText(text: string, x: number, y: number, maxWidth?: number): void
    transform(a: number, b: number, c: number, d: number, e: number, f: number): void
    translate(x: number, y: number): void
  }

  interface WebGLRenderingContext {
    readonly canvas: HTMLCanvasElement | OffscreenCanvas
    readonly drawingBufferHeight: number
    readonly drawingBufferWidth: number
    // WebGL methods would be extensive, keeping minimal for now
    getParameter(pname: GLenum): any
    getError(): GLenum
  }

  interface WebGL2RenderingContext extends WebGLRenderingContext {
    // WebGL2 specific methods
    readonly READ_BUFFER: GLenum
    readonly UNPACK_ROW_LENGTH: GLenum
    readonly UNPACK_SKIP_ROWS: GLenum
    readonly UNPACK_SKIP_PIXELS: GLenum
    texImage3D(target: GLenum, level: GLint, internalformat: GLint, width: GLsizei, height: GLsizei, depth: GLsizei, border: GLint, format: GLenum, type: GLenum, pixels: ArrayBufferView | null): void
    texSubImage3D(target: GLenum, level: GLint, xoffset: GLint, yoffset: GLint, zoffset: GLint, width: GLsizei, height: GLsizei, depth: GLsizei, format: GLenum, type: GLenum, pixels: ArrayBufferView | null): void
  }

  type GLenum = number
  type GLint = number
  type GLsizei = number

  interface OffscreenCanvas extends EventTarget {
    height: number
    width: number
    convertToBlob(options?: ImageEncodeOptions): Promise<Blob>
    getContext(contextId: "2d", options?: CanvasRenderingContext2DSettings): OffscreenCanvasRenderingContext2D | null
    getContext(contextId: "webgl", options?: WebGLContextAttributes): WebGLRenderingContext | null
    getContext(contextId: "webgl2", options?: WebGLContextAttributes): WebGL2RenderingContext | null
    getContext(contextId: string, options?: any): OffscreenRenderingContext | null
    transferToImageBitmap(): ImageBitmap
  }

  interface OffscreenCanvasRenderingContext2D extends CanvasRenderingContext2D {
    readonly canvas: OffscreenCanvas
  }

  type OffscreenRenderingContext = OffscreenCanvasRenderingContext2D | WebGLRenderingContext | WebGL2RenderingContext

  interface ImageEncodeOptions {
    quality?: number
    type?: string
  }

  interface AssignedNodesOptions {
    flatten?: boolean
  }

  // Global objects
  const navigator: Navigator
  const window: Window & typeof globalThis
  const document: Document
  const location: Location
  const history: History
  const screen: Screen
  const crypto: Crypto
  const localStorage: Storage
  const sessionStorage: Storage
  const indexedDB: IDBFactory
}

export {}


// Global typings - no eslint disable needed

// ===== Augment: Global typings for enhanced print system =====
// This section adds global Window properties and custom Document events
// used by the unified printing system. Keep it small and dependency-light.
import type { EnhancedPrintOptions, PrintData } from '../services/MasterPrintService'

declare global {
  interface Window {
    enhancedPrint?: (data: PrintData, options?: EnhancedPrintOptions) => Promise<void>
    smartPrint?: (
      type?: string,
      params?: { elementId?: string; data?: PrintData; options?: EnhancedPrintOptions }
    ) => Promise<void>
    printSystemIntegrator?: any
    printSystem?: any
    printSecurity?: any
  }

  interface DocumentEventMap {
    'print-start': CustomEvent<{ type?: string; data?: unknown }>
    'print-end': CustomEvent<{ type?: string; success: boolean; error?: unknown }>
  }
}
