/**
 * تقرير أداء العمال في الإنتاج
 * تقرير شامل لأداء العمال في قسم الإنتاج
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const ProductionWorkersPerformanceReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'production_workers_performance' as ReportType}
      title="تقرير أداء العمال في الإنتاج"
      description="تقرير مفصل لأداء العمال في قسم الإنتاج مع تحليل الإنتاجية والكفاءة والجودة"
      showDateRange={true}
      showDepartmentFilter={true}
      showEmployeeFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="production_workers_performance_report"
      defaultFilters={{
        sortBy: 'performance_score',
        sortOrder: 'desc'
      }}
    />
  )
}

export default ProductionWorkersPerformanceReport
