import React, { useState, useEffect } from 'react'
import { Button, Space, App, Row, Col, Card, Typography, Switch, Select, InputNumber } from 'antd'
import {
  ReloadOutlined,
  DownloadOutlined,
  PrinterOutlined,
  SettingOutlined,
  TableOutlined,
  Bar<PERSON>hartOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'
import EnhancedTable, { type EnhancedColumnType } from './EnhancedTable'
import VirtualTable, { useVirtualTable } from './VirtualTable'
import LoadingSkeletons from './LoadingSkeletons'
import AnimatedComponents from './AnimatedComponents'
import ThemeToggle from './ThemeToggle'
import { enhancedExportManager } from '../../utils/enhancedExportManager'

const { Title, Text } = Typography
const { Option } = Select

// بيانات تجريبية
interface SampleData {
  id: number
  name: string
  email: string
  phone: string
  department: string
  salary: number
  joinDate: string
  status: 'active' | 'inactive'
  performance: number
}

// إنشاء بيانات تجريبية
const generateSampleData = (count: number): SampleData[] => {
  const departments = ['المبيعات', 'المحاسبة', 'الإنتاج', 'الموارد البشرية', 'التسويق']
  const statuses: ('active' | 'inactive')[] = ['active', 'inactive']
  
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    name: `موظف ${index + 1}`,
    email: `employee${index + 1}@company.com`,
    phone: `05${Math.floor(Math.random() * *********).toString().padStart(8, '0')}`,
    department: departments[Math.floor(Math.random() * departments.length)],
    salary: Math.floor(Math.random() * 10000) + 3000,
    joinDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    performance: Math.floor(Math.random() * 100) + 1
  }))
}

// تعريف الأعمدة
const columns: EnhancedColumnType<SampleData>[] = [
  {
    key: 'id',
    title: 'الرقم',
    dataIndex: 'id',
    width: 80,
    sortable: true,
    searchable: true,
    exportable: true,
    type: 'number'
  },
  {
    key: 'name',
    title: 'الاسم',
    dataIndex: 'name',
    width: 150,
    sortable: true,
    searchable: true,
    exportable: true,
    type: 'text'
  },
  {
    key: 'email',
    title: 'البريد الإلكتروني',
    dataIndex: 'email',
    width: 200,
    searchable: true,
    exportable: true,
    type: 'text'
  },
  {
    key: 'phone',
    title: 'الهاتف',
    dataIndex: 'phone',
    width: 120,
    searchable: true,
    exportable: true,
    type: 'text'
  },
  {
    key: 'department',
    title: 'القسم',
    dataIndex: 'department',
    width: 120,
    filterable: true,
    searchable: true,
    exportable: true,
    type: 'text'
  },
  {
    key: 'salary',
    title: 'الراتب',
    dataIndex: 'salary',
    width: 120,
    sortable: true,
    exportable: true,
    type: 'currency'
  },
  {
    key: 'joinDate',
    title: 'تاريخ الانضمام',
    dataIndex: 'joinDate',
    width: 120,
    sortable: true,
    exportable: true,
    type: 'date'
  },
  {
    key: 'status',
    title: 'الحالة',
    dataIndex: 'status',
    width: 100,
    filterable: true,
    exportable: true,
    type: 'text',
    render: (status: string) => (
      <span style={{ 
        color: status === 'active' ? '#52c41a' : '#ff4d4f',
        fontWeight: 'bold'
      }}>
        {status === 'active' ? 'نشط' : 'غير نشط'}
      </span>
    )
  },
  {
    key: 'performance',
    title: 'الأداء',
    dataIndex: 'performance',
    width: 120,
    sortable: true,
    exportable: true,
    type: 'number',
    render: (performance: number) => (
      <AnimatedComponents.Progress
        percent={performance}
        strokeColor={performance >= 80 ? '#52c41a' : performance >= 60 ? '#faad14' : '#ff4d4f'}
        showInfo={true}
        strokeWidth={6}
      />
    )
  }
]

// مكون العرض التوضيحي
const EnhancedTableDemo: React.FC = () => {
  const { message } = App.useApp()
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<SampleData[]>([])
  const [dataCount, setDataCount] = useState(100)
  const [useVirtual, setUseVirtual] = useState(false)

  // تعيين message API لـ enhancedExportManager
  React.useEffect(() => {
    enhancedExportManager.setMessageApi(message)
  }, [message])
  const [showSkeletons, setShowSkeletons] = useState(false)

  // Hook للجدول الافتراضي
  const {
    data: _virtualData,
    totalCount: _totalCount,
    searchText: _searchText,
    setSearchText: _setSearchText,
    shouldUseVirtualization
  } = useVirtualTable(data, {
    searchFields: ['name', 'email', 'department'],
    enableVirtualization: useVirtual,
    threshold: 50
  })

  // تحميل البيانات
  const loadData = async () => {
    setLoading(true)
    setShowSkeletons(true)
    
    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const newData = generateSampleData(dataCount)
    setData(newData)
    setLoading(false)
    setShowSkeletons(false)
    
    message.success(`تم تحميل ${dataCount} سجل بنجاح`)
  }

  // تحميل البيانات عند بدء التشغيل
  useEffect(() => {
    loadData()
  }, [dataCount])

  // معالجة التصدير
  const handleExport = async (format: 'excel' | 'pdf') => {
    try {
      const exportColumns = columns.map(col => ({
        key: String(col.key),
        title: col.title as string,
        dataIndex: col.dataIndex as string,
        width: typeof col.width === 'number' ? col.width : (col.width ? parseInt(String(col.width)) : undefined),
        type: col.type
      }))

      if (format === 'excel') {
        await enhancedExportManager.exportToExcel(data, exportColumns, {
          filename: 'بيانات_الموظفين',
          sheetName: 'الموظفين'
        })
      } else {
        await enhancedExportManager.exportToPDF(data, exportColumns, {
          filename: 'بيانات_الموظفين',
          title: 'تقرير بيانات الموظفين',
          subtitle: `إجمالي ${data.length} موظف`
        })
      }
    } catch (error) {
      Logger.error('EnhancedTableDemo', 'خطأ في التصدير:', error)
    }
  }

  // معالجة الطباعة
  const handlePrint = async () => {
    try {
      await enhancedExportManager.printTable('enhanced-table-demo', {
        title: 'تقرير بيانات الموظفين',
        subtitle: `إجمالي ${data.length} موظف`,
        showDate: true,
        showPageNumbers: true
      })
    } catch (error) {
      Logger.error('EnhancedTableDemo', 'خطأ في الطباعة:', error)
    }
  }

  // حساب الإحصائيات
  const stats = {
    'إجمالي الموظفين': data.length,
    'الموظفين النشطين': data.filter(item => item.status === 'active').length,
    'متوسط الراتب': Math.round(data.reduce((sum, item) => sum + item.salary, 0) / data.length || 0),
    'متوسط الأداء': Math.round(data.reduce((sum, item) => sum + item.performance, 0) / data.length || 0)
  }

  if (showSkeletons) {
    return (
      <div style={{ padding: 24 }}>
        <LoadingSkeletons.Page 
          hasHeader={true}
          hasStats={true}
          hasChart={false}
          hasTable={true}
        />
      </div>
    )
  }

  return (
    <div style={{ padding: 24 }}>
      {/* رأس الصفحة */}
      <AnimatedComponents.Group animation="fadeIn" stagger={0.1}>
        <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
          <Col>
            <AnimatedComponents.Text level={2}>
              عرض توضيحي للجداول المحسنة
            </AnimatedComponents.Text>
            <AnimatedComponents.Text type="secondary">
              مثال على الجداول المحسنة مع الميزات الجديدة
            </AnimatedComponents.Text>
          </Col>
          <Col>
            <Space>
              <ThemeToggle size="middle" showText={true} />
              <AnimatedComponents.Button
                icon={<ReloadOutlined />}
                onClick={loadData}
                loading={loading}
                animation="pulse"
                hover={true}
              >
                تحديث البيانات
              </AnimatedComponents.Button>
            </Space>
          </Col>
        </Row>

        {/* الإحصائيات */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          {Object.entries(stats).map(([key, value], index) => (
            <Col key={key} xs={24} sm={12} lg={6}>
              <AnimatedComponents.Card
                animation="scaleIn"
                delay={index * 0.1}
                hover={true}
              >
                <div style={{ textAlign: 'center' }}>
                  <AnimatedComponents.Counter
                    to={value}
                    duration={2}
                    delay={0.5 + index * 0.1}
                    suffix={key.includes('راتب') || key.includes('أداء') ? (key.includes('راتب') ? ' ₪' : '%') : ''}
                    separator=","
                  />
                  <div style={{ marginTop: 8, color: '#666', fontSize: 12 }}>
                    {key}
                  </div>
                </div>
              </AnimatedComponents.Card>
            </Col>
          ))}
        </Row>

        {/* إعدادات الجدول */}
        <Card style={{ marginBottom: 24 }}>
          <Row gutter={16} align="middle">
            <Col>
              <Text strong>إعدادات الجدول:</Text>
            </Col>
            <Col>
              <Space>
                <Text>عدد السجلات:</Text>
                <Select
                  value={dataCount}
                  onChange={setDataCount}
                  style={{ width: 120 }}
                >
                  <Option value={50}>50</Option>
                  <Option value={100}>100</Option>
                  <Option value={500}>500</Option>
                  <Option value={1000}>1000</Option>
                  <Option value={5000}>5000</Option>
                </Select>
              </Space>
            </Col>
            <Col>
              <Space>
                <Text>التمرير الافتراضي:</Text>
                <Switch
                  checked={useVirtual}
                  onChange={setUseVirtual}
                  checkedChildren="مفعل"
                  unCheckedChildren="معطل"
                />
              </Space>
            </Col>
            <Col>
              <Space>
                <AnimatedComponents.Button
                  icon={<DownloadOutlined />}
                  onClick={() => handleExport('excel')}
                  type="primary"
                  animation="bounce"
                  hover={true}
                >
                  تصدير Excel
                </AnimatedComponents.Button>
                <AnimatedComponents.Button
                  icon={<DownloadOutlined />}
                  onClick={() => handleExport('pdf')}
                  animation="bounce"
                  hover={true}
                >
                  تصدير PDF
                </AnimatedComponents.Button>
                <AnimatedComponents.Button
                  icon={<PrinterOutlined />}
                  onClick={handlePrint}
                  animation="bounce"
                  hover={true}
                >
                  طباعة
                </AnimatedComponents.Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* الجدول */}
        <div id="enhanced-table-demo">
          {useVirtual ? (
            <VirtualTable
              data={data}
              columns={columns}
              loading={loading}
              height={600}
              enableVirtualization={true}
              threshold={50}
              emptyText="لا توجد بيانات للعرض"
              rowKey="id"
            />
          ) : (
            <EnhancedTable
              columns={columns}
              data={data}
              title="بيانات الموظفين"
              subtitle={`إجمالي ${data.length} موظف`}
              loading={loading}
              searchable={true}
              _filterable={true}
              exportable={true}
              printable={true}
              resizable={true}
              virtualScroll={shouldUseVirtualization}
              maxHeight={600}
              rowKey="id"
              onExport={(_exportData) => handleExport('excel')}
              onPrint={handlePrint}
              onRefresh={loadData}
              showSummary={true}
              summaryData={stats}
              emptyText="لا توجد بيانات للعرض"
            />
          )}
        </div>
      </AnimatedComponents.Group>
    </div>
  )
}

export default EnhancedTableDemo
