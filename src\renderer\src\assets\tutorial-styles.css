/* أنماط النظام التعليمي */

/* تمييز العناصر أثناء التعليم */
.tutorial-highlight {
  position: relative !important;
  z-index: 9997 !important;
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.6) !important;
  border-radius: 4px !important;
  background-color: rgba(24, 144, 255, 0.1) !important;
  transition: all 0.3s ease !important;
}

.tutorial-highlight::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border: 2px solid #1890ff;
  border-radius: 8px;
  animation: tutorialPulse 2s infinite;
  pointer-events: none;
  z-index: 9998;
}

@keyframes tutorialPulse {
  0% { 
    transform: scale(1); 
    opacity: 1; 
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }
  50% { 
    transform: scale(1.05); 
    opacity: 0.7; 
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0.3);
  }
  100% { 
    transform: scale(1); 
    opacity: 1; 
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

/* أنماط مركز التعليم */
.tutorial-center-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.tutorial-center-modal .ant-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px 16px 0 0;
}

.tutorial-center-modal .ant-modal-title {
  color: white;
  font-weight: bold;
}

/* أنماط البطاقات التعليمية */
.tutorial-module-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.tutorial-module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.tutorial-module-card.completed .ant-card-head {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.tutorial-module-card.production .ant-card-head {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.tutorial-module-card .ant-card-head {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 12px 12px 0 0;
}

.tutorial-module-card .ant-card-head-title {
  color: white;
  font-weight: bold;
}

/* أنماط التصنيفات */
.tutorial-category-tag {
  border-radius: 20px;
  padding: 4px 12px;
  font-weight: bold;
}

.tutorial-category-tag.basic {
  background: #e6f7ff;
  color: #1890ff;
  border-color: #91d5ff;
}

.tutorial-category-tag.intermediate {
  background: #fff7e6;
  color: #fa8c16;
  border-color: #ffd591;
}

.tutorial-category-tag.advanced {
  background: #fff1f0;
  color: #f5222d;
  border-color: #ffa39e;
}

.tutorial-category-tag.production {
  background: #f6ffed;
  color: #52c41a;
  border-color: #b7eb8f;
}

/* أنماط التعليم التفاعلي */
.interactive-tutorial-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9998;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: rtl;
}

.interactive-tutorial-card {
  max-width: 600px;
  width: 90%;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.interactive-tutorial-card .ant-card-head {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px 16px 0 0;
}

.interactive-tutorial-card .ant-card-head-title {
  color: white;
  font-weight: bold;
}

/* أنماط المساعد الذكي */
.smart-assistant-modal .ant-modal-content {
  direction: rtl;
  border-radius: 16px;
}

.smart-assistant-modal .ant-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px 16px 0 0;
}

.smart-assistant-modal .ant-modal-title {
  color: white;
  font-weight: bold;
}

.chat-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 16px 0;
}

.message-bubble {
  display: flex;
  margin-bottom: 12px;
}

.message-bubble.user {
  justify-content: flex-start;
}

.message-bubble.assistant {
  justify-content: flex-end;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-bubble.user .message-content {
  background: #1890ff;
  color: white;
}

.message-bubble.assistant .message-content {
  background: #f0f0f0;
  color: #333;
}

.message-avatar {
  margin: 0 8px;
}

/* أنماط دليل الدورة المستندية */
.production-guide-container {
  direction: rtl;
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.production-phase-card {
  margin-bottom: 16px;
  border-left: 4px solid #1890ff;
}

.production-phase-card.planning {
  border-left-color: #722ed1;
}

.production-phase-card.execution {
  border-left-color: #13c2c2;
}

.production-phase-card.control {
  border-left-color: #fa8c16;
}

.production-phase-card.completion {
  border-left-color: #52c41a;
}

.process-flow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: white;
  border-radius: 8px;
  margin: 16px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.process-step {
  text-align: center;
  flex: 1;
}

.step-icon {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 8px;
}

.step-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.step-desc {
  color: #666;
  font-size: 12px;
}

.arrow {
  font-size: 24px;
  color: #1890ff;
  margin: 0 16px;
}

/* أنماط الأزرار العائمة */
.tutorial-float-buttons {
  position: fixed;
  left: 24px;
  bottom: 24px;
  z-index: 1000;
}

/* أنماط شريط التقدم */
.tutorial-progress {
  margin-bottom: 20px;
}

.tutorial-progress .ant-progress-line {
  margin-bottom: 8px;
}

/* أنماط الخطوات */
.tutorial-step-content {
  padding: 20px 0;
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.step-number {
  background: #1890ff;
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-left: 12px;
}

.step-title {
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
}

.step-description {
  margin-bottom: 16px;
  line-height: 1.6;
  color: #666;
}

.step-tips {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;
}

.tip-icon {
  color: #52c41a;
  margin-left: 8px;
}

/* أنماط لوحة التحكم */
.tutorial-control-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.control-buttons {
  display: flex;
  gap: 8px;
}

/* أنماط الإجراءات السريعة */
.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 16px 0;
}

.action-button {
  border-radius: 20px;
  font-size: 12px;
}

/* تحسينات للجوال */
@media (max-width: 768px) {
  .interactive-tutorial-card {
    width: 95%;
    margin: 10px;
  }
  
  .process-flow {
    flex-direction: column;
    gap: 16px;
  }
  
  .arrow {
    transform: rotate(90deg);
  }
  
  .tutorial-float-buttons {
    left: 16px;
    bottom: 16px;
  }
}

/* تحسينات للطباعة */
@media print {
  .tutorial-highlight,
  .tutorial-highlight::before,
  .interactive-tutorial-overlay,
  .tutorial-float-buttons {
    display: none !important;
  }
}
