import React from 'react';
import { Tag, Typography, Progress } from 'antd';
import UniversalReport from './UniversalReport';
// import { DateUtils } from '../../utils/dateConfig';
import type { ReportData, ReportType } from '../../types/reports';
import { createDefaultFilters } from '../../types/reports';

const { Text } = Typography;

const PurchaseAnalysisReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير تحليل المشتريات...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getPurchaseAnalysisReport(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const purchaseAnalysisData = response.data;

      // معالجة البيانات
      const processedData = purchaseAnalysisData.map((item: any, index: number) => ({
        ...item,
        key: item.period || index,
        avg_invoice_value: item.invoice_count > 0 ? item.total_purchases / item.invoice_count : 0,
        growth_rate: index > 0 && purchaseAnalysisData[index - 1] ?
          ((item.total_purchases - purchaseAnalysisData[index - 1].total_purchases) / purchaseAnalysisData[index - 1].total_purchases) * 100 : 0
      }));

      // حساب الإحصائيات
      const totalPurchases = processedData.reduce((sum: number, item: any) => sum + item.total_purchases, 0);
      const totalInvoices = processedData.reduce((sum: number, item: any) => sum + item.invoice_count, 0);
      const totalSavings = processedData.reduce((sum: number, item: any) => sum + (item.cost_savings || 0), 0);

      const avgInvoiceValue = totalInvoices > 0 ? totalPurchases / totalInvoices : 0;
      const avgPaymentRate = processedData.length > 0 ?
        processedData.reduce((sum: number, item: any) => sum + item.payment_rate, 0) / processedData.length : 0;

      const bestPeriods = [...processedData]
        .sort((a: any, b: any) => b.total_purchases - a.total_purchases)
        .slice(0, 3)
        .map((item: any) => ({
          period: item.period,
          amount: item.total_purchases
        }));

      const summary = {
        totalPurchases: Math.round(totalPurchases * 100) / 100,
        totalInvoices,
        totalSavings: Math.round(totalSavings * 100) / 100,
        avgInvoiceValue: Math.round(avgInvoiceValue * 100) / 100,
        avgPaymentRate: Math.round(avgPaymentRate * 100) / 100,
        bestPeriods
      };

      console.log('✅ تم إنشاء تقرير تحليل المشتريات بنجاح');

      return {
        title: 'تقرير تحليل المشتريات',
        data: processedData,
        columns,
        summary,
        metadata: {
          reportType: 'purchase_analysis' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: processedData.length
        }
      };
    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير تحليل المشتريات:', error);
      throw error;
    }
  };
  // إعداد الأعمدة
  const columns = [
    {
      title: 'الفترة',
      dataIndex: 'period',
      key: 'period',
      width: 120,
      render: (period: string) => (
        <Text strong style={{ color: '#1890ff' }}>{period}</Text>
      )
    },
    {
      title: 'إجمالي المشتريات (₪)',
      dataIndex: 'total_purchases',
      key: 'total_purchases',
      width: 150,
      align: 'right' as const,
      render: (amount: number) => (
        <Text strong style={{ color: '#52c41a' }}>
          {amount.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'عدد الفواتير',
      dataIndex: 'invoice_count',
      key: 'invoice_count',
      width: 120,
      align: 'center' as const,
      render: (count: number) => (
        <Tag color="blue">{count}</Tag>
      )
    },
    {
      title: 'متوسط قيمة الفاتورة (₪)',
      dataIndex: 'avg_invoice_value',
      key: 'avg_invoice_value',
      width: 150,
      align: 'right' as const,
      render: (value: number) => (
        <Text style={{ color: '#fa8c16' }}>
          {value.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'عدد الموردين',
      dataIndex: 'supplier_count',
      key: 'supplier_count',
      width: 120,
      align: 'center' as const,
      render: (count: number) => (
        <Tag color="green">{count}</Tag>
      )
    },
    {
      title: 'نمو المشتريات',
      dataIndex: 'growth_rate',
      key: 'growth_rate',
      width: 150,
      align: 'center' as const,
      render: (rate: number) => (
        <Progress
          percent={Math.abs(rate)}
          size="small"
          strokeColor={rate >= 0 ? '#52c41a' : '#ff4d4f'}
          format={() => `${rate >= 0 ? '+' : ''}${rate.toFixed(1)}%`}
        />
      )
    },
    {
      title: 'معدل الدفع',
      dataIndex: 'payment_rate',
      key: 'payment_rate',
      width: 120,
      align: 'center' as const,
      render: (rate: number) => (
        <Progress
          percent={Math.round(rate)}
          size="small"
          strokeColor={rate >= 90 ? '#52c41a' : rate >= 70 ? '#fa8c16' : '#ff4d4f'}
        />
      )
    },
    {
      title: 'التوفير المحقق (₪)',
      dataIndex: 'cost_savings',
      key: 'cost_savings',
      width: 150,
      align: 'right' as const,
      render: (savings: number) => (
        <Text strong style={{ color: savings > 0 ? '#52c41a' : '#666' }}>
          {savings > 0 ? `+${savings.toLocaleString()}` : '-'}
        </Text>
      )
    }
  ];
  return (
    <UniversalReport
      reportType={'purchase_analysis' as ReportType}
      title="تقرير تحليل المشتريات"
      description="تقرير شامل لتحليل المشتريات مع الاتجاهات والإحصائيات"
      onGenerateReport={generateReport}
      defaultFilters={createDefaultFilters('purchase_analysis')}
      showDateRange={true}
      showSupplierFilter={true}
      showWarehouseFilter={true}
      showCategoryFilter={true}
      showAmountRangeFilter={true}
      showAdvancedSearch={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default PurchaseAnalysisReport;
