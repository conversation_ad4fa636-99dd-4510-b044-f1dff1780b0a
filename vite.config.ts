import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  root: 'src/renderer',
  base: './',

  // معالجة الأصول
  assetsInclude: ['**/*.svg'],

  build: {
    outDir: '../../dist/renderer',
    emptyOutDir: true,
    chunkSizeWarningLimit: 2000, // ✅ زيادة الحد لتقليل التحذيرات
    target: 'es2020',
    minify: 'terser',
    sourcemap: false, // تعطيل source maps في الإنتاج لتوفير المساحة

    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // ✅ تقسيم ذكي للمكتبات الكبيرة
          if (id.includes('node_modules')) {
            // React و المكتبات المرتبطة
            if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
              return 'react-vendor'
            }
            // Ant Design Core
            if (id.includes('antd') && !id.includes('@ant-design/icons')) {
              return 'antd-core'
            }
            // Ant Design Icons
            if (id.includes('@ant-design/icons')) {
              return 'antd-icons'
            }
            // Office Libraries (Excel, PDF)
            if (id.includes('xlsx') || id.includes('jspdf') || id.includes('html2canvas')) {
              return 'office'
            }
            // Utilities
            if (id.includes('dayjs') || id.includes('styled-components') || id.includes('lucide-react')) {
              return 'utils'
            }
            // Database
            if (id.includes('sql.js')) {
              return 'database'
            }
            // Crypto
            if (id.includes('bcryptjs')) {
              return 'crypto'
            }
            // باقي المكتبات
            return 'vendor'
          }
        },
        // تحسين أسماء الملفات
        chunkFileNames: 'js/[name]-[hash:8].js',
        entryFileNames: 'js/[name]-[hash:8].js',
        assetFileNames: (assetInfo) => {
          // الشعار يبقى في المجلد الجذر بدون hash
          if (assetInfo.names && assetInfo.names.includes('default-logo.svg')) {
            return '[name].[ext]'
          }
          return 'assets/[name]-[hash:8].[ext]'
        }
      },
      // تحسين الاستيراد الخارجي
      external: ['electron']
    },

    // تحسينات إضافية
    terserOptions: {
      compress: {
        drop_console: false, // ✅ الاحتفاظ بـ console.log للتشخيص
        drop_debugger: true,
        pure_funcs: ['console.debug'], // إزالة console.debug فقط
        passes: 2, // تمريرتان للضغط
        ecma: 2020 // ✅ استخدام ميزات ES2020
      },
      mangle: {
        safari10: true // دعم Safari 10
      },
      format: {
        comments: false // ✅ إزالة التعليقات
      }
    }
  },

  // تحسينات الأداء
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'antd',
      '@ant-design/icons',
      'dayjs',
      'styled-components',
      'file-saver',
      'react-to-print'
    ],
    exclude: ['sql.js', 'electron'] // استبعاد المكتبات الثقيلة والخاصة بـ Electron
  },

  // إعدادات التطوير
  server: {
    port: 3000,
    strictPort: true,
    hmr: {
      overlay: false // تعطيل overlay للأخطاء
    }
  },

  // حل المسارات
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src/renderer/src'),
      '@components': resolve(__dirname, 'src/renderer/src/components'),
      '@utils': resolve(__dirname, 'src/renderer/src/utils'),
      '@types': resolve(__dirname, 'src/renderer/src/types')
    }
  },

  // متغيرات البيئة
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString())
  }
})
