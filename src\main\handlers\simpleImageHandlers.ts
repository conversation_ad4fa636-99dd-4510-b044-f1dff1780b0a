/**
 * ═══════════════════════════════════════════════════════════════════════════
 * 🖼️ Simple Image Handlers - معالجات IPC للصور الموحدة
 * ═══════════════════════════════════════════════════════════════════════════
 * 
 * الهدف: توفير معالجات IPC بسيطة وواضحة لجميع عمليات الصور
 * 
 * المعالجات:
 * ✅ upload-unified-image - رفع صورة
 * ✅ get-unified-images - جلب الصور
 * ✅ get-unified-image-by-id - جلب صورة بالمعرف
 * ✅ delete-unified-image - حذف صورة
 * ✅ set-primary-unified-image - تعيين صورة رئيسية
 * ✅ update-unified-image - تحديث بيانات صورة
 * 
 * @version 1.0.0
 * @created 2025-09-30
 * ═══════════════════════════════════════════════════════════════════════════
 */

import { ipcMain } from 'electron'
import { SimpleDatabaseService } from '../services/SimpleDatabaseService'
import { Logger } from '../utils/logger'
import * as fs from 'fs'
import * as path from 'path'
import * as crypto from 'crypto'
import { app } from 'electron'

// ═══════════════════════════════════════════════════════════════════════════
// 🔧 الدوال المساعدة
// ═══════════════════════════════════════════════════════════════════════════

/**
 * الحصول على مسار مجلد الصور
 */
function getImagesDirectory(): string {
  const userDataPath = app.getPath('userData')
  const imagesPath = path.join(userDataPath, 'images')
  
  // إنشاء المجلد إذا لم يكن موجوداً
  if (!fs.existsSync(imagesPath)) {
    fs.mkdirSync(imagesPath, { recursive: true })
  }
  
  return imagesPath
}

/**
 * حفظ ملف صورة من Base64
 */
function saveImageFile(base64Data: string, fileName: string, category: string): string {
  const imagesDir = getImagesDirectory()
  const categoryDir = path.join(imagesDir, category)
  
  // إنشاء مجلد الفئة إذا لم يكن موجوداً
  if (!fs.existsSync(categoryDir)) {
    fs.mkdirSync(categoryDir, { recursive: true })
  }
  
  // إزالة البادئة من Base64 (data:image/png;base64,...)
  const base64Image = base64Data.replace(/^data:image\/\w+;base64,/, '')
  const buffer = Buffer.from(base64Image, 'base64')
  
  // إنشاء اسم ملف فريد
  const uniqueFileName = `${Date.now()}_${fileName}`
  const filePath = path.join(categoryDir, uniqueFileName)
  
  // حفظ الملف
  fs.writeFileSync(filePath, buffer)
  
  return filePath
}

/**
 * حذف ملف صورة
 */
function deleteImageFile(filePath: string): void {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath)
    }
  } catch (error) {
    Logger.warn('SimpleImageHandlers', `فشل حذف الملف: ${filePath}`, error)
  }
}

// ═══════════════════════════════════════════════════════════════════════════
// 📋 تسجيل المعالجات
// ═══════════════════════════════════════════════════════════════════════════

export function registerSimpleImageHandlers(): void {
  const dbService = SimpleDatabaseService.getInstance()

  Logger.info('SimpleImageHandlers', '🔧 تسجيل معالجات الصور الموحدة...')

  // ═══════════════════════════════════════════════════════════════════════════
  // 📤 رفع صورة
  // ═══════════════════════════════════════════════════════════════════════════

  ipcMain.handle('upload-unified-image', async (_event, data: {
    file: { name: string; size: number; type: string; data: string }
    category: string
    contextId: number
    description?: string
    isPrimary?: boolean
    metadata?: Record<string, any>
  }) => {
    try {
      Logger.info('SimpleImageHandlers', `📤 رفع صورة: ${data.file.name} [${data.category}:${data.contextId}]`)

      const db = dbService.getDatabase()

      // حفظ الملف
      const filePath = saveImageFile(data.file.data, data.file.name, data.category)

      // إنشاء معرف فريد باستخدام crypto (مثل باقي المشروع)
      const imageId = `img_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`

      // إدراج في قاعدة البيانات
      const query = `
        INSERT INTO unified_images (
          id, name, path, size, type, category, context_id,
          description, is_primary, sort_order, metadata,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `

      const params = [
        imageId,
        data.file.name,
        filePath,
        data.file.size,
        data.file.type,
        data.category,
        data.contextId,
        data.description || '',
        data.isPrimary ? 1 : 0,
        0, // sort_order
        JSON.stringify(data.metadata || {})
      ]

      db.run(query, ...params)

      // إذا كانت صورة رئيسية، إلغاء الصور الرئيسية الأخرى
      if (data.isPrimary) {
        db.run(
          `UPDATE unified_images
           SET is_primary = 0
           WHERE category = ? AND context_id = ? AND id != ?`,
          data.category, data.contextId, imageId
        )
      }

      // جلب الصورة المحفوظة
      const savedImage = db.prepare('SELECT * FROM unified_images WHERE id = ?').get(imageId)

      Logger.info('SimpleImageHandlers', `✅ تم رفع الصورة بنجاح: ${imageId}`)

      return {
        success: true,
        data: savedImage
      }
    } catch (error: any) {
      Logger.error('SimpleImageHandlers', '❌ خطأ في رفع الصورة:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء رفع الصورة'
      }
    }
  })

  // ═══════════════════════════════════════════════════════════════════════════
  // 📥 جلب الصور
  // ═══════════════════════════════════════════════════════════════════════════

  ipcMain.handle('get-unified-images', async (_event, data: {
    category: string
    contextId: number
  }) => {
    try {
      Logger.debug('SimpleImageHandlers', `🔍 جلب صور [${data.category}:${data.contextId}]`)

      const db = dbService.getDatabase()

      const query = `
        SELECT * FROM unified_images
        WHERE category = ? AND context_id = ? AND deleted_at IS NULL
        ORDER BY is_primary DESC, sort_order ASC, created_at ASC
      `

      const images = db.prepare(query).all(data.category, data.contextId)

      Logger.debug('SimpleImageHandlers', `✅ تم جلب ${images.length} صورة`)

      return {
        success: true,
        data: images
      }
    } catch (error: any) {
      Logger.error('SimpleImageHandlers', '❌ خطأ في جلب الصور:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء جلب الصور',
        data: []
      }
    }
  })

  // ═══════════════════════════════════════════════════════════════════════════
  // 🔍 جلب صورة بالمعرف
  // ═══════════════════════════════════════════════════════════════════════════

  ipcMain.handle('get-unified-image-by-id', async (_event, data: { imageId: string }) => {
    try {
      const db = dbService.getDatabase()

      const image = db.prepare('SELECT * FROM unified_images WHERE id = ? AND deleted_at IS NULL').get(data.imageId)

      if (!image) {
        throw new Error('الصورة غير موجودة')
      }

      return {
        success: true,
        data: image
      }
    } catch (error: any) {
      Logger.error('SimpleImageHandlers', '❌ خطأ في جلب الصورة:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء جلب الصورة'
      }
    }
  })

  // ═══════════════════════════════════════════════════════════════════════════
  // 🗑️ حذف صورة
  // ═══════════════════════════════════════════════════════════════════════════

  ipcMain.handle('delete-unified-image', async (_event, data: { imageId: string }) => {
    try {
      Logger.info('SimpleImageHandlers', `🗑️ حذف صورة: ${data.imageId}`)

      const db = dbService.getDatabase()

      // جلب معلومات الصورة أولاً
      const image = db.prepare('SELECT * FROM unified_images WHERE id = ?').get(data.imageId)

      if (!image) {
        throw new Error('الصورة غير موجودة')
      }

      // حذف ناعم (soft delete)
      db.run('UPDATE unified_images SET deleted_at = CURRENT_TIMESTAMP WHERE id = ?', data.imageId)

      // حذف الملف الفعلي
      deleteImageFile(image.path)

      Logger.info('SimpleImageHandlers', `✅ تم حذف الصورة بنجاح: ${data.imageId}`)

      return { success: true }
    } catch (error: any) {
      Logger.error('SimpleImageHandlers', '❌ خطأ في حذف الصورة:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء حذف الصورة'
      }
    }
  })

  // ═══════════════════════════════════════════════════════════════════════════
  // ⭐ تعيين صورة رئيسية
  // ═══════════════════════════════════════════════════════════════════════════

  ipcMain.handle('set-primary-unified-image', async (_event, data: {
    imageId: string
    category: string
    contextId: number
  }) => {
    try {
      Logger.info('SimpleImageHandlers', `⭐ تعيين صورة رئيسية: ${data.imageId}`)

      const db = dbService.getDatabase()

      // إلغاء جميع الصور الرئيسية الأخرى
      db.run(
        `UPDATE unified_images
         SET is_primary = 0
         WHERE category = ? AND context_id = ?`,
        data.category, data.contextId
      )

      // تعيين الصورة الحالية كرئيسية
      db.run('UPDATE unified_images SET is_primary = 1 WHERE id = ?', data.imageId)

      Logger.info('SimpleImageHandlers', `✅ تم تعيين الصورة الرئيسية بنجاح`)

      return { success: true }
    } catch (error: any) {
      Logger.error('SimpleImageHandlers', '❌ خطأ في تعيين الصورة الرئيسية:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء تعيين الصورة الرئيسية'
      }
    }
  })

  // ═══════════════════════════════════════════════════════════════════════════
  // ✏️ تحديث بيانات صورة
  // ═══════════════════════════════════════════════════════════════════════════

  ipcMain.handle('update-unified-image', async (_event, data: {
    imageId: string
    updates: {
      description?: string
      metadata?: Record<string, any>
      sort_order?: number
    }
  }) => {
    try {
      Logger.info('SimpleImageHandlers', `📝 تحديث صورة: ${data.imageId}`)

      const db = dbService.getDatabase()

      const updates: string[] = []
      const params: any[] = []

      if (data.updates.description !== undefined) {
        updates.push('description = ?')
        params.push(data.updates.description)
      }

      if (data.updates.metadata !== undefined) {
        updates.push('metadata = ?')
        params.push(JSON.stringify(data.updates.metadata))
      }

      if (data.updates.sort_order !== undefined) {
        updates.push('sort_order = ?')
        params.push(data.updates.sort_order)
      }

      if (updates.length === 0) {
        return { success: true }
      }

      updates.push('updated_at = CURRENT_TIMESTAMP')
      params.push(data.imageId)

      const query = `UPDATE unified_images SET ${updates.join(', ')} WHERE id = ?`
      db.run(query, ...params)

      Logger.info('SimpleImageHandlers', `✅ تم تحديث الصورة بنجاح`)

      return { success: true }
    } catch (error: any) {
      Logger.error('SimpleImageHandlers', '❌ خطأ في تحديث الصورة:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء تحديث الصورة'
      }
    }
  })

  Logger.info('SimpleImageHandlers', '✅ تم تسجيل معالجات الصور الموحدة بنجاح')
}

