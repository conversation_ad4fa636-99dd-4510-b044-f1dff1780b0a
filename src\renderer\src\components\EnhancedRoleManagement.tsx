import React, { useState, useEffect, useCallback, useReducer, useMemo } from 'react'
import {
  Card, Table, Button, Space, Modal, Form, Input, Popconfirm, Typography, Row, Col, Statistic,
  Alert, Tooltip, Badge, Checkbox, Divider,
  Avatar, Descriptions, App
} from 'antd'

import { SafeLogger as Logger } from '../utils/logger'
import {
  SafetyOutlined, PlusOutlined, EditOutlined, DeleteOutlined,
  KeyOutlined, UserOutlined, CheckCircleOutlined,
  ReloadOutlined, CopyOutlined, EyeOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { Role, Permission, User } from '../types/global'
import dayjs from 'dayjs'

const { Text } = Typography

const StyledCard = styled(Card)`
  .ant-card-head {
    background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
    border-bottom: none;
    
    .ant-card-head-title {
      color: white;
      font-weight: bold;
    }
  }
`

const StatCard = styled(Card)`
  text-align: center;
  border-radius: 8px;
  
  .ant-statistic-title {
    color: #666;
    font-size: 14px;
  }
  
  .ant-statistic-content {
    color: #722ed1;
  }
`

/*
const PermissionCard = styled(Card)`
  border-radius: 8px;
  margin-bottom: 8px;

  &:hover {
    box-shadow: 0 2px 8px rgba(114, 46, 209, 0.1);
    border-color: #722ed1;
  }
`
*/

interface EnhancedRoleManagementProps {
  currentUser: User
}

interface RoleWithUsers extends Role {
  user_count?: number
  users?: User[]
}

// تعريف حالة المكون
interface RoleState {
  roles: RoleWithUsers[]
  permissions: Permission[]
  loading: boolean
  selectedRole: RoleWithUsers | null
  modalVisible: boolean
  editingRole: RoleWithUsers | null
  roleDetailsVisible: boolean
}

// تعريف الإجراءات
type RoleAction =
  | { type: 'SET_ROLES'; payload: RoleWithUsers[] }
  | { type: 'SET_PERMISSIONS'; payload: Permission[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_SELECTED_ROLE'; payload: RoleWithUsers | null }
  | { type: 'SET_MODAL_VISIBLE'; payload: boolean }
  | { type: 'SET_EDITING_ROLE'; payload: RoleWithUsers | null }
  | { type: 'SET_ROLE_DETAILS_VISIBLE'; payload: boolean }

// دالة reducer لإدارة الحالة
const roleReducer = (state: RoleState, action: RoleAction): RoleState => {
  switch (action.type) {
    case 'SET_ROLES':
      return { ...state, roles: action.payload }
    case 'SET_PERMISSIONS':
      return { ...state, permissions: action.payload }
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    case 'SET_SELECTED_ROLE':
      return { ...state, selectedRole: action.payload }
    case 'SET_MODAL_VISIBLE':
      return { ...state, modalVisible: action.payload }
    case 'SET_EDITING_ROLE':
      return { ...state, editingRole: action.payload }
    case 'SET_ROLE_DETAILS_VISIBLE':
      return { ...state, roleDetailsVisible: action.payload }
    default:
      return state
  }
}

const EnhancedRoleManagement: React.FC<EnhancedRoleManagementProps> = () => {
  const { message } = App.useApp() // استخدام App context لحل تحذير Antd

  // استخدام useReducer لإدارة الحالة بشكل أفضل
  const [state, dispatch] = useReducer(roleReducer, {
    roles: [],
    permissions: [],
    loading: false,
    selectedRole: null,
    modalVisible: false,
    editingRole: null,
    roleDetailsVisible: false
  })

  const [, setUsers] = useState<User[]>([])
  const [form] = Form.useForm()

  // دالة معالجة الأخطاء المحسنة
  const handleError = useCallback((error: unknown, operation: string) => {
    Logger.error('EnhancedRoleManagement', `Error during ${operation}:`, error)
    message.error(`حدث خطأ أثناء ${operation}`)
  }, [message])

  const loadRoles = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: true })
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getRolesWithUsers()
        if (response.success) {
          dispatch({ type: 'SET_ROLES', payload: response.data })
        } else {
          message.error('فشل في تحميل الأدوار')
        }
      } else {
        // بيانات وهمية للتطوير
        const mockRoles: RoleWithUsers[] = [
          {
            id: 1,
            name: 'admin',
            display_name: 'مدير النّام',
            description: 'صلاحيات كاملة لإدارة النّام',
            permissions: JSON.stringify(['users.create', 'users.read', 'users.update', 'users.delete', 'roles.manage']),
            user_count: 2,
            created_at: '2024-01-01T00:00:00Z'
          },
          {
            id: 2,
            name: 'manager',
            display_name: 'مدير',
            description: 'صلاحيات إدارية محدودة',
            permissions: JSON.stringify(['users.read', 'reports.view', 'inventory.manage']),
            user_count: 3,
            created_at: '2024-02-01T00:00:00Z'
          },
          {
            id: 3,
            name: 'employee',
            display_name: 'موّف',
            description: 'صلاحيات أساسية للموّفين',
            permissions: JSON.stringify(['profile.view', 'attendance.mark']),
            user_count: 15,
            created_at: '2024-03-01T00:00:00Z'
          }
        ]
        dispatch({ type: 'SET_ROLES', payload: mockRoles })
      }
    } catch (error) {
      handleError(error, 'تحميل الأدوار')
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [message, handleError])

  const loadPermissions = useCallback(async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getPermissions()
        if (response.success) {
          dispatch({ type: 'SET_PERMISSIONS', payload: response.data })
        } else {
          message.error('فشل في تحميل الصلاحيات')
        }
      } else {
        // صلاحيات وهمية
        const mockPermissions: Permission[] = [
          // صلاحيات المستخدمين
          { id: 1, name: 'users.view', description: 'عرض المستخدمين', module: 'users', action: 'view', created_at: '2024-01-01T00:00:00Z' },
          { id: 2, name: 'users.create', description: 'إضافة مستخدمين', module: 'users', action: 'create', created_at: '2024-01-01T00:00:00Z' },
          { id: 3, name: 'users.edit', description: 'تعديل المستخدمين', module: 'users', action: 'edit', created_at: '2024-01-01T00:00:00Z' },
          { id: 4, name: 'users.delete', description: 'حذف المستخدمين', module: 'users', action: 'delete', created_at: '2024-01-01T00:00:00Z' },

          // صلاحيات المخزون
          { id: 5, name: 'inventory.view', description: 'عرض المخزون', module: 'inventory', action: 'view', created_at: '2024-01-01T00:00:00Z' },
          { id: 6, name: 'inventory.create', description: 'إضافة للمخزون', module: 'inventory', action: 'create', created_at: '2024-01-01T00:00:00Z' },
          { id: 7, name: 'inventory.edit', description: 'تعديل المخزون', module: 'inventory', action: 'edit', created_at: '2024-01-01T00:00:00Z' },

          // صلاحيات المبيعات
          { id: 8, name: 'sales.view', description: 'عرض المبيعات', module: 'sales', action: 'view', created_at: '2024-01-01T00:00:00Z' },
          { id: 9, name: 'sales.create', description: 'إنشاء فواتير بيع', module: 'sales', action: 'create', created_at: '2024-01-01T00:00:00Z' },

          // صلاحيات التقارير
          { id: 10, name: 'reports.view', description: 'عرض التقارير', module: 'reports', action: 'view', created_at: '2024-01-01T00:00:00Z' },
          { id: 11, name: 'reports.export', description: 'تصدير التقارير', module: 'reports', action: 'export', created_at: '2024-01-01T00:00:00Z' },

          // صلاحيات لوحة التحكم
          { id: 12, name: 'dashboard.view', description: 'عرض لوحة التحكم', module: 'dashboard', action: 'view', created_at: '2024-01-01T00:00:00Z' }
        ]
        dispatch({ type: 'SET_PERMISSIONS', payload: mockPermissions })
      }
    } catch (error) {
      handleError(error, 'تحميل الصلاحيات')
    }
  }, [message, handleError])

  const loadUsers = useCallback(async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getUsers()
        if (response.success && Array.isArray(response.data)) {
          setUsers(response.data)
        } else {
          Logger.error('EnhancedRoleManagement', 'خطأ في استجابة المستخدمين:', response)
          setUsers([])
        }
      } else {
        // بيانات وهمية
        const mockUsers: User[] = [
          {
            id: 1,
            username: 'admin',
            full_name: 'مدير النّام',
            email: '<EMAIL>',
            role: 'admin',
            is_active: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-06-25T10:30:00Z'
          }
        ]
        setUsers(mockUsers)
      }
    } catch (error) {
      Logger.error('EnhancedRoleManagement', 'Error loading users:', error)
    }
  }, [])

  useEffect(() => {
    loadRoles()
    loadPermissions()
    loadUsers()
  }, [loadRoles, loadPermissions, loadUsers])

  const handleCreateRole = useCallback(async (values: any) => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.createRole(values)
        if (result.success) {
          message.success('تم إنشاء الدور بنجاح')
          dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })
          form.resetFields()
          loadRoles()
        } else {
          message.error(result.message || 'فشل في إنشاء الدور')
        }
      } else {
        // محاكاة النجاح
        const newRole: RoleWithUsers = {
          id: Date.now(),
          ...values,
          user_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        dispatch({ type: 'SET_ROLES', payload: [...state.roles, newRole] })
        message.success('تم إنشاء الدور بنجاح')
        dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })
        form.resetFields()
      }
    } catch (error) {
      handleError(error, 'إنشاء الدور')
    }
  }, [message, form, loadRoles, state.roles, handleError])

  const handleUpdateRole = useCallback(async (values: any) => {
    try {
      if (state.editingRole && window.electronAPI) {
        const result = await window.electronAPI.updateRole(state.editingRole.id, values)
        if (result.success) {
          message.success('تم تحديث الدور بنجاح')
          dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })
          form.resetFields()
          dispatch({ type: 'SET_EDITING_ROLE', payload: null })
          loadRoles()
        } else {
          message.error(result.message || 'فشل في تحديث الدور')
        }
      } else if (state.editingRole) {
        // محاكاة النجاح
        const updatedRoles = state.roles.map(role =>
          role.id === state.editingRole?.id ? { ...role, ...values, updated_at: new Date().toISOString() } : role
        )
        dispatch({ type: 'SET_ROLES', payload: updatedRoles })
        message.success('تم تحديث الدور بنجاح')
        dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })
        form.resetFields()
        dispatch({ type: 'SET_EDITING_ROLE', payload: null })
      }
    } catch (error) {
      handleError(error, 'تحديث الدور')
    }
  }, [state.editingRole, state.roles, message, form, loadRoles, handleError])

  const handleDeleteRole = useCallback(async (roleId: number) => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.deleteRole(roleId)
        if (result.success) {
          message.success('تم حذف الدور بنجاح')
          loadRoles()
        } else {
          message.error(result.message || 'فشل في حذف الدور')
        }
      } else {
        // محاكاة النجاح
        const filteredRoles = state.roles.filter(role => role.id !== roleId)
        dispatch({ type: 'SET_ROLES', payload: filteredRoles })
        message.success('تم حذف الدور بنجاح')
      }
    } catch (error) {
      handleError(error, 'حذف الدور')
    }
  }, [message, loadRoles, state.roles, handleError])

  const duplicateRole = useCallback(async (role: RoleWithUsers) => {
    try {
      const duplicatedRole = {
        name: `${role.name}_copy`,
        display_name: `${role.display_name} (نسخة)`,
        description: `نسخة من ${role.description}`,
        permissions: role.permissions
      }

      if (window.electronAPI) {
        const result = await window.electronAPI.createRole(duplicatedRole)
        if (result.success) {
          message.success('تم نسخ الدور بنجاح')
          loadRoles()
        } else {
          message.error('فشل في نسخ الدور')
        }
      } else {
        // محاكاة النجاح
        const newRole: RoleWithUsers = {
          id: Date.now(),
          ...duplicatedRole,
          user_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        dispatch({ type: 'SET_ROLES', payload: [...state.roles, newRole] })
        message.success('تم نسخ الدور بنجاح')
      }
    } catch (error) {
      handleError(error, 'نسخ الدور')
    }
  }, [message, loadRoles, state.roles, handleError])

  const viewRoleDetails = useCallback((role: RoleWithUsers) => {
    dispatch({ type: 'SET_SELECTED_ROLE', payload: role })
    dispatch({ type: 'SET_ROLE_DETAILS_VISIBLE', payload: true })
  }, [])

  /*
  const getPermissionsByCategory = () => {
    const categories: { [key: string]: Permission[] } = {}
    permissions.forEach(permission => {
      const category = permission.module || 'other'
      if (!categories[category]) {
        categories[category] = []
      }
      categories[category].push(permission)
    })
    return categories
  }
  */

  const getRoleColor = useCallback((roleName: string) => {
    switch (roleName) {
      case 'admin': return 'red'
      case 'manager': return 'blue'
      case 'employee': return 'green'
      default: return 'purple'
    }
  }, [])

  // دالة لترجمة أسماء الصلاحيات
  const getPermissionDisplayName = useCallback((permissionName: string) => {
    const permission = state.permissions.find(p => p.name === permissionName)
    return permission ? permission.description : permissionName
  }, [state.permissions])

  // دالة لتجميع الصلاحيات حسب الوحدة
  const getPermissionsByModule = useCallback(() => {
    const modules: { [key: string]: Permission[] } = {}
    state.permissions.forEach(permission => {
      const module = permission.module || 'general'
      if (!modules[module]) {
        modules[module] = []
      }
      modules[module].push(permission)
    })
    return modules
  }, [state.permissions])

  // دالة لترجمة أسماء الوحدات
  const getModuleDisplayName = useCallback((module: string) => {
    const moduleNames: { [key: string]: string } = {
      'dashboard': 'لوحة التحكم',
      'profile': 'الملف الشخصي',
      'users': 'إدارة المستخدمين',
      'inventory': 'إدارة المخزون',
      'sales': 'إدارة المبيعات',
      'purchases': 'إدارة المشتريات',
      'reports': 'التقارير',
      'banking': 'إدارة البنوك',
      'general': 'عام'
    }
    return moduleNames[module] || module
  }, [])

  // إحصائيات الأدوار محسنة بـ useMemo
  const roleStats = useMemo(() => ({
    totalRoles: state.roles.length,
    totalPermissions: state.permissions.length,
    assignedUsers: state.roles.reduce((sum, role) => sum + (role.user_count || 0), 0),
    activeRoles: state.roles.filter(role => (role.user_count || 0) > 0).length
  }), [state.roles, state.permissions])

  // دالة معالجة إرسال النموذج محسنة
  const handleFormSubmit = useCallback(async (values: any) => {
    try {
      if (state.editingRole) {
        await handleUpdateRole(values)
      } else {
        await handleCreateRole(values)
      }
      dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })
      form.resetFields()
    } catch (error) {
      handleError(error, state.editingRole ? 'تحديث الدور' : 'إنشاء دور جديد')
    }
  }, [state.editingRole, handleUpdateRole, handleCreateRole, form, handleError])

  // أعمدة الجدول محسنة بـ useMemo
  const columns = useMemo(() => [
    {
      title: 'الدور',
      key: 'role',
      render: (record: RoleWithUsers) => (
        <Space>
          <Avatar
            size={40}
            icon={<SafetyOutlined />}
            style={{ backgroundColor: getRoleColor(record.name) === 'red' ? '#ff4d4f' : '#722ed1' }}
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.name}</div>
            <Text type="secondary">{record.description || 'لا يوجد وصف'}</Text>
          </div>
        </Space>
      )
    },
    {
      title: 'الوصف',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'عدد الصلاحيات',
      key: 'permissions_count',
      render: (record: RoleWithUsers) => (
        <Badge count={record.permissions?.length || 0} showZero color="#722ed1" />
      )
    },
    {
      title: 'عدد المستخدمين',
      dataIndex: 'user_count',
      key: 'user_count',
      render: (count: number) => (
        <Badge count={count} showZero color="#1890ff" />
      )
    },
    {
      title: 'تاريخ الإنشاء',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY')
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (record: RoleWithUsers) => (
        <Space>
          <Tooltip title="عرض التفاصيل">
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => viewRoleDetails(record)}
            />
          </Tooltip>
          <Tooltip title="تعديل">
            <Button
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                dispatch({ type: 'SET_EDITING_ROLE', payload: record })
                form.setFieldsValue(record)
                dispatch({ type: 'SET_MODAL_VISIBLE', payload: true })
              }}
            />
          </Tooltip>
          <Tooltip title="نسخ">
            <Button
              size="small"
              icon={<CopyOutlined />}
              onClick={() => duplicateRole(record)}
            />
          </Tooltip>
          {record.name !== 'admin' && record.user_count === 0 && (
            <Tooltip title="حذف">
              <Popconfirm
                title="هل تريد حذف هذا الدور؟"
                description="هذا الإجراء لا يمكن التراجع عنه"
                onConfirm={() => handleDeleteRole(record.id)}
                okText="حذف"
                cancelText="إلغاء"
                okType="danger"
              >
                <Button
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>
      )
    }
  ], [getRoleColor, viewRoleDetails, duplicateRole, handleDeleteRole, form])

  return (
    <div>
      <StyledCard
        title={
          <Space>
            <SafetyOutlined />
            إدارة الأدوار والصلاحيات
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadRoles}
              loading={state.loading}
            >
              تحديث
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                dispatch({ type: 'SET_EDITING_ROLE', payload: null })
                dispatch({ type: 'SET_MODAL_VISIBLE', payload: true })
                form.resetFields()
              }}
            >
              إضافة دور
            </Button>
          </Space>
        }
      >
        {/* إحصائيات سريعة */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6}>
            <StatCard>
              <Statistic
                title="إجمالي الأدوار"
                value={roleStats.totalRoles}
                prefix={<SafetyOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </StatCard>
          </Col>
          <Col xs={24} sm={6}>
            <StatCard>
              <Statistic
                title="إجمالي الصلاحيات"
                value={roleStats.totalPermissions}
                prefix={<KeyOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </StatCard>
          </Col>
          <Col xs={24} sm={6}>
            <StatCard>
              <Statistic
                title="المستخدمون المعينون"
                value={roleStats.assignedUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </StatCard>
          </Col>
          <Col xs={24} sm={6}>
            <StatCard>
              <Statistic
                title="الأدوار النشطة"
                value={roleStats.activeRoles}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </StatCard>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={state.roles}
          rowKey="id"
          loading={state.loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} دور`
          }}
        />
      </StyledCard>

      {/* نافذة إضافة/تعديل الدور */}
      <Modal
        title={state.editingRole ? 'تعديل الدور' : 'إضافة دور جديد'}
        open={state.modalVisible}
        onCancel={() => {
          dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })
          dispatch({ type: 'SET_EDITING_ROLE', payload: null })
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
        >
          <Form.Item
            name="name"
            label="اسم الدور"
            rules={[
              { required: true, message: 'يرجى إدخال اسم الدور' },
              { min: 2, message: 'اسم الدور يجب أن يكون حرفين على الأقل' }
            ]}
          >
            <Input placeholder="مثال: manager" disabled={!!state.editingRole} />
          </Form.Item>

          <Form.Item
            name="description"
            label="الوصف"
          >
            <Input.TextArea
              placeholder="وصف مختصر للدور وصلاحياته"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="permissions"
            label="الصلاحيات"
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                {Object.entries(getPermissionsByModule()).map(([module, modulePermissions]) => (
                  <div key={module} style={{ marginBottom: '16px' }}>
                    <Divider orientation="left" style={{ margin: '8px 0' }}>
                      <Text strong style={{ color: '#1890ff' }}>
                        {getModuleDisplayName(module)}
                      </Text>
                    </Divider>
                    <Row gutter={[16, 8]}>
                      {modulePermissions.map(permission => (
                        <Col span={12} key={permission.id}>
                          <Checkbox value={permission.name}>
                            <div>
                              <strong style={{ fontSize: '13px' }}>{permission.name}</strong>
                              <br />
                              <Text type="secondary" style={{ fontSize: '11px' }}>
                                {permission.description}
                              </Text>
                            </div>
                          </Checkbox>
                        </Col>
                      ))}
                    </Row>
                  </div>
                ))}
              </div>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {state.editingRole ? 'تحديث' : 'إضافة'}
              </Button>
              <Button onClick={() => {
                dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })
                dispatch({ type: 'SET_EDITING_ROLE', payload: null })
                form.resetFields()
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نافذة تفاصيل الدور */}
      <Modal
        title="تفاصيل الدور"
        open={state.roleDetailsVisible}
        onCancel={() => {
          dispatch({ type: 'SET_ROLE_DETAILS_VISIBLE', payload: false })
          dispatch({ type: 'SET_SELECTED_ROLE', payload: null })
        }}
        footer={[
          <Button key="close" onClick={() => {
            dispatch({ type: 'SET_ROLE_DETAILS_VISIBLE', payload: false })
            dispatch({ type: 'SET_SELECTED_ROLE', payload: null })
          }}>
            إغلاق
          </Button>
        ]}
        width={700}
      >
        {state.selectedRole && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="اسم الدور">{state.selectedRole.name}</Descriptions.Item>
              <Descriptions.Item label="عدد المستخدمين">{state.selectedRole.user_count || 0}</Descriptions.Item>
              <Descriptions.Item label="تاريخ الإنشاء">
                {dayjs(state.selectedRole.created_at).format('DD/MM/YYYY HH:mm')}
              </Descriptions.Item>
              <Descriptions.Item label="الوصف" span={2}>
                {state.selectedRole.description || 'لا يوجد وصف'}
              </Descriptions.Item>
            </Descriptions>

            <Divider>الصلاحيات</Divider>
            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              {state.selectedRole.permissions && state.selectedRole.permissions.length > 0 ? (
                <Row gutter={[16, 8]}>
                  {(typeof state.selectedRole.permissions === 'string' ? JSON.parse(state.selectedRole.permissions) : state.selectedRole.permissions).map((permission: string, index: number) => (
                    <Col span={24} key={index} style={{ marginBottom: 8 }}>
                      <div style={{
                        padding: '8px 12px',
                        border: '1px solid #d9d9d9',
                        borderRadius: '6px',
                        backgroundColor: '#f6f6f6'
                      }}>
                        <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
                          {permission}
                        </div>
                        <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                          {getPermissionDisplayName(permission)}
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              ) : (
                <Alert message="لا توجد صلاحيات محددة لهذا الدور" type="info" />
              )}
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default EnhancedRoleManagement
