# 🔧 إصلاح مشكلة عدم ظهور الصور في طباعة أوامر الإنتاج

## 📋 المشكلة

كانت الصور لا تظهر في طباعة أوامر الإنتاج بسبب عدم وجود النظام الأساسي لإدارة صور أوامر الإنتاج.

## 🔍 التشخيص

تم اكتشاف أن المشكلة الجذرية هي:

1. **عدم وجود handler**: `get-production-order-images` غير موجود في النظام
2. **عدم وجود جدول قاعدة البيانات**: لا يوجد جدول لحفظ صور أوامر الإنتاج
3. **عدم وجود طرق في الخدمات**: لا توجد طرق لجلب ومعالجة صور أوامر الإنتاج

## ✅ الحل المطبق

### 1. إنشاء جدول قاعدة البيانات

```sql
CREATE TABLE IF NOT EXISTS production_order_images (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  order_id INTEGER NOT NULL,
  image_name TEXT NOT NULL,
  image_path TEXT NOT NULL,
  file_size INTEGER DEFAULT 0,
  file_type TEXT,
  description TEXT,
  category TEXT DEFAULT 'general',
  is_primary BOOLEAN DEFAULT 0,
  tags TEXT,
  notes TEXT,
  uploaded_by INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (order_id) REFERENCES production_orders (id) ON DELETE CASCADE,
  FOREIGN KEY (uploaded_by) REFERENCES users (id) ON DELETE SET NULL
)
```

### 2. إضافة طرق في ProductionService

- `getProductionOrderImages(orderId: number)`: جلب صور أمر الإنتاج
- `addProductionOrderImage(imageData)`: إضافة صورة جديدة
- `getMimeType(extension)`: تحديد نوع MIME للصورة

### 3. إضافة Handlers في productionHandlers.ts

- `get-production-order-images`: جلب صور أمر الإنتاج
- `upload-production-order-image`: رفع صورة جديدة

### 4. إضافة طرق في preload.ts

```typescript
getProductionOrderImages: (orderId: number) => ipcRenderer.invoke('get-production-order-images', orderId),
uploadProductionOrderImage: (imageData: any) => ipcRenderer.invoke('upload-production-order-image', imageData),
```

### 5. تحسين معالجة الصور في MasterPrintService

- تحويل الصور إلى base64 تلقائياً
- معالجة أنواع مختلفة من مسارات الصور
- إضافة خصائص CSS للطباعة الصحيحة للصور

### 6. إنشاء أدوات الاختبار

- `ProductionOrderImagesTestHelper`: أداة لإنشاء صور تجريبية واختبار النظام
- `ProductionOrderImagesPrintTest`: مكون React لاختبار النظام

## 🚀 كيفية الاستخدام

### الاختبار السريع

```typescript
// في console المتصفح
window.testProductionOrderImages.quickTest()
```

### إنشاء صور تجريبية

```typescript
// إنشاء صور تجريبية لأمر إنتاج معين
await window.testProductionOrderImages.createTestImages(orderId)
```

### اختبار الطباعة

```typescript
// اختبار طباعة أمر إنتاج مع الصور
await window.testProductionOrderImages.testPrintProductionOrderWithImages(orderId)
```

## 📁 الملفات المُحدثة

### Backend (Main Process)
- `src/main/services/ProductionService.ts` - إضافة طرق إدارة الصور
- `src/main/handlers/productionHandlers.ts` - إضافة handlers
- `src/main/preload.ts` - إضافة طرق IPC

### Frontend (Renderer Process)
- `src/renderer/src/services/MasterPrintService.ts` - تحسين معالجة الصور
- `src/renderer/src/types/global.d.ts` - إضافة أنواع TypeScript

### أدوات الاختبار
- `src/renderer/src/utils/testProductionOrderImages.ts` - أداة الاختبار
- `src/renderer/src/components/test/ProductionOrderImagesPrintTest.tsx` - مكون الاختبار

## 🔧 الميزات الجديدة

1. **دعم أنواع مختلفة من الصور**: JPEG, PNG, GIF, SVG, BMP, WebP
2. **تصنيف الصور**: عام، مواد، عملية، جودة، نهائي
3. **تحويل تلقائي إلى base64**: للطباعة الصحيحة
4. **معالجة الأخطاء**: التعامل مع الصور المفقودة أو التالفة
5. **أدوات اختبار شاملة**: لضمان عمل النظام بشكل صحيح

## 📊 النتائج المتوقعة

بعد تطبيق هذا الحل:

- ✅ ستظهر الصور في طباعة أوامر الإنتاج
- ✅ يمكن رفع وإدارة صور أوامر الإنتاج
- ✅ تحويل تلقائي للصور للطباعة
- ✅ معالجة صحيحة للأخطاء
- ✅ أدوات اختبار للتحقق من النظام

## 🧪 خطوات الاختبار

1. تشغيل التطبيق
2. فتح console المتصفح
3. تشغيل: `window.testProductionOrderImages.quickTest()`
4. التحقق من ظهور الصور في معاينة الطباعة

## 📝 ملاحظات

- الصور التجريبية يتم إنشاؤها باستخدام Canvas API
- يتم حفظ الصور كـ base64 في قاعدة البيانات للاختبار
- في الإنتاج الفعلي، يُفضل حفظ الصور كملفات منفصلة
- النظام يدعم جميع أنواع الصور الشائعة
