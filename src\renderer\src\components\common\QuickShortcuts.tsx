import React, { useState, useEffect } from 'react'
import { Avatar } from 'antd'
import {
  InboxOutlined,
  UserOutlined,
  FileTextOutlined,
  TeamOutlined,
  ShoppingCartOutlined,
  ToolOutlined,
  BankOutlined,
  DollarOutlined,
  SettingOutlined,
  BookOutlined,
  PictureOutlined,
  CalendarOutlined,
  <PERSON><PERSON>hartOutlined,
  SafetyOutlined,
  ClockCircleOutlined,
  LoginOutlined,
  LogoutOutlined,
  DashboardOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { CompanyConfigManager, CompanyInfo } from '../../config/companyConfig'
import { SafeLogger as Logger } from '../../utils/logger'

const FloatingContainer = styled.div`
  padding: 10px;
  background: transparent;
  min-height: 100vh;

  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: scale(0.9) translateY(30px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* التأكد من ظهور العناصر حتى لو لم تعمل الـ animation */
  .tile-item {
    opacity: 1 !important;
    animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }
`

const TileItem = styled.div<{
  background: string;
  gridArea?: string;
}>`
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  background: ${props => props.background};
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 8px;
  overflow: hidden;
  z-index: 5;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  ${props => props.gridArea && `grid-area: ${props.gridArea};`}

  /* تأثير الإضاءة الداخلية */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  /* تأثير الموجة عند النقر */
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
  }

  &:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
    filter: brightness(1.1) saturate(1.1);

    &::before {
      opacity: 1;
    }
  }

  &:active {
    transform: translateY(-1px) scale(1.02);
    transition: all 0.1s ease;

    &::after {
      width: 100%;
      height: 100%;
    }
  }

  .tile-icon {
    font-size: 26px;
    color: white;
    margin-bottom: 6px;
    display: block;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    position: relative;
    z-index: 2;

    /* تكيف حجم الأيقونات مع الشاشات */
    @media (max-width: 1400px) {
      font-size: 22px;
      margin-bottom: 5px;
    }

    @media (max-width: 1200px) {
      font-size: 18px;
      margin-bottom: 4px;
    }

    @media (max-width: 1000px) {
      font-size: 16px;
      margin-bottom: 3px;
    }

    @media (max-width: 800px) {
      font-size: 14px;
      margin-bottom: 2px;
    }
  }

  .tile-label {
    font-size: 11px;
    font-weight: 600;
    color: white;
    line-height: 1.2;
    text-align: center;
    word-break: break-word;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    max-width: 100%;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;

    /* تكيف حجم النصوص مع الشاشات */
    @media (max-width: 1400px) {
      font-size: 10px;
      line-height: 1.1;
    }

    @media (max-width: 1200px) {
      font-size: 9px;
      line-height: 1.0;
    }

    @media (max-width: 1000px) {
      font-size: 8px;
      line-height: 1.0;
    }

    @media (max-width: 800px) {
      font-size: 7px;
      line-height: 0.9;
    }
  }

  &:hover .tile-icon {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));
  }

  &:hover .tile-label {
    transform: translateY(-1px);
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.6);
  }

  /* تأثير الإضاءة عند التمرير */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0);
    transition: background 0.15s ease;
    pointer-events: none;
  }

  &:hover::before {
    background: rgba(255, 255, 255, 0.1);
  }
`

interface QuickShortcutsProps {
  onShortcutClick: (key: string) => void
  onPlaySound: (type: 'click' | 'success' | 'error' | 'warning') => void
}



const TileGroup = styled.div`
  margin-bottom: 15px;

  .group-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    /* تكيف حجم عناوين المجموعات */
    @media (max-width: 1400px) {
      font-size: 15px;
      margin-bottom: 10px;
      gap: 6px;
    }

    @media (max-width: 1200px) {
      font-size: 14px;
      margin-bottom: 8px;
      gap: 5px;
    }

    @media (max-width: 1000px) {
      font-size: 13px;
      margin-bottom: 6px;
      gap: 4px;
    }

    @media (max-width: 800px) {
      font-size: 12px;
      margin-bottom: 5px;
      gap: 3px;
    }
  }
`

const TilesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 85px);
  grid-template-rows: repeat(4, 85px);
  gap: 10px;
  justify-content: center;
  margin: 0 auto;
  padding: 0;

  /* تكيف مع الشاشات المتوسطة */
  @media (max-width: 1400px) {
    grid-template-columns: repeat(4, 75px);
    grid-template-rows: repeat(4, 75px);
    gap: 8px;
  }

  /* تكيف مع الشاشات الصغيرة */
  @media (max-width: 1200px) {
    grid-template-columns: repeat(4, 65px);
    grid-template-rows: repeat(4, 65px);
    gap: 6px;
  }

  /* تكيف مع الشاشات الأصغر */
  @media (max-width: 1000px) {
    grid-template-columns: repeat(4, 55px);
    grid-template-rows: repeat(4, 55px);
    gap: 5px;
  }

  /* تكيف مع الشاشات الصغيرة جداً */
  @media (max-width: 800px) {
    grid-template-columns: repeat(4, 50px);
    grid-template-rows: repeat(4, 50px);
    gap: 4px;
  }
`

const GroupsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 20px;
  max-width: 1900px;
  margin: 0 auto;
  padding: 0 5px;

  /* تكيف مع الشاشات الكبيرة */
  @media (max-width: 1700px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    max-width: 1400px;
  }

  /* تكيف مع الشاشات المتوسطة */
  @media (max-width: 1400px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    max-width: 1200px;
  }

  /* تكيف مع الشاشات الأصغر - صفين */
  @media (max-width: 1100px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    max-width: 800px;
  }

  /* تكيف مع الشاشات الصغيرة - عمود واحد */
  @media (max-width: 700px) {
    grid-template-columns: 1fr;
    gap: 12px;
    max-width: 400px;
  }

  /* تكيف مع الشاشات الصغيرة جداً */
  @media (max-width: 500px) {
    gap: 8px;
    padding: 0 10px;
    max-width: 350px;
  }
`

// مكون شعار الشركة - بدون إطار أو خلفية
const CompanyFooter = styled.div`
  margin-top: 40px;
  text-align: center;
  max-width: 800px;
  margin: 40px auto 0;
  padding: 20px 0;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }

  .company-logo {
    margin-bottom: 12px;

    .ant-avatar {
      width: 100px !important;
      height: 100px !important;
      border-radius: 12px !important;

      /* تكيف حجم الشعار مع الشاشات */
      @media (max-width: 1200px) {
        width: 80px !important;
        height: 80px !important;
      }

      @media (max-width: 800px) {
        width: 60px !important;
        height: 60px !important;
      }

      @media (max-width: 500px) {
        width: 50px !important;
        height: 50px !important;
      }
    }
  }

  .company-name {
    font-size: 24px;
    font-weight: 700;
    color: #1890ff;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

    @media (max-width: 1200px) {
      font-size: 20px;
    }

    @media (max-width: 800px) {
      font-size: 18px;
    }

    @media (max-width: 500px) {
      font-size: 16px;
    }
  }

  .company-name-en {
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin-bottom: 12px;
    font-style: italic;

    @media (max-width: 1200px) {
      font-size: 14px;
    }

    @media (max-width: 800px) {
      font-size: 12px;
    }

    @media (max-width: 500px) {
      font-size: 11px;
    }
  }

  .company-details {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    margin-bottom: 12px;

    .detail-item {
      display: flex;
      align-items: center;
      gap: 6px;
      color: #555;
      font-size: 14px;

      .anticon {
        color: #1890ff;
      }
    }
  }

  .company-slogan {
    font-size: 14px;
    color: #888;
    font-style: italic;
    padding-top: 12px;
    margin-top: 12px;
  }

  @media (max-width: 768px) {
    padding: 16px 20px;
    margin: 30px auto 0;

    .company-name {
      font-size: 20px;
    }

    .company-details {
      flex-direction: column;
      gap: 8px;

      .detail-item {
        justify-content: center;
      }
    }
  }
`

const QuickShortcuts: React.FC<QuickShortcutsProps> = ({ onShortcutClick, onPlaySound }) => {
  console.log('🚀🚀🚀 QuickShortcuts component is rendering! - NEW FLOATING DESIGN WITH EFFECTS 🚀🚀🚀')
  console.log('📱 Current timestamp:', new Date().toISOString())

  // حالة معلومات الشركة
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null)
  const [companyLogo, setCompanyLogo] = useState<string>('')

  // تحميل معلومات الشركة
  useEffect(() => {
    const loadCompanyInfo = async () => {
      try {
        // جلب معلومات الشركة
        const info = await CompanyConfigManager.getCompanyInfo()
        setCompanyInfo(info)
        Logger.info('QuickShortcuts', 'تم تحميل معلومات الشركة:', info)

        // جلب شعار الشركة
        if (window.electronAPI?.getCompanyLogo) {
          const logoResult = await window.electronAPI.getCompanyLogo()
          if (logoResult?.success && logoResult.logoPath) {
            setCompanyLogo(logoResult.logoPath)
            Logger.info('QuickShortcuts', 'تم تحميل شعار الشركة:', logoResult.logoPath)
          }
        }
      } catch (error) {
        Logger.error('QuickShortcuts', 'خطأ في تحميل معلومات الشركة:', error as Error)
      }
    }

    loadCompanyInfo()
  }, [])

  // تنظيم الاختصارات في 4 مجموعات، كل مجموعة 4×4 - تصميم عائم جديد
  const tileGroups = [
    {
      title: 'البيانات الأساسية',
      color: '#1890ff',
      tiles: [
        // الصف الأول
        { key: 'items', icon: <InboxOutlined />, label: 'الأصناف', background: '#2196f3', gridArea: '1 / 1 / 2 / 3' }, // عرض 2
        { key: 'categories', icon: <BookOutlined />, label: 'الفئات', background: '#03a9f4', gridArea: '1 / 3 / 2 / 4' },
        { key: 'warehouses', icon: <BankOutlined />, label: 'المخازن', background: '#1976d2', gridArea: '1 / 4 / 2 / 5' },

        // الصف الثاني
        { key: 'customers', icon: <UserOutlined />, label: 'العملاء', background: '#00bcd4', gridArea: '2 / 1 / 3 / 2' },
        { key: 'suppliers', icon: <ShoppingCartOutlined />, label: 'الموردين', background: '#0097a7', gridArea: '2 / 2 / 3 / 3' },
        { key: 'units', icon: <ToolOutlined />, label: 'الوحدات', background: '#0288d1', gridArea: '2 / 3 / 3 / 5' }, // عرض 2

        // الصف الثالث
        { key: 'currencies', icon: <DollarOutlined />, label: 'العملات', background: '#0277bd', gridArea: '3 / 1 / 4 / 2' },
        { key: 'taxes', icon: <SafetyOutlined />, label: 'الضرائب', background: '#01579b', gridArea: '3 / 2 / 4 / 3' },
        { key: 'payment-methods', icon: <BankOutlined />, label: 'طرق الدفع', background: '#1565c0', gridArea: '3 / 3 / 4 / 4' },
        { key: 'price-lists', icon: <FileTextOutlined />, label: 'قوائم الأسعار', background: '#1976d2', gridArea: '3 / 4 / 4 / 5' },

        // الصف الرابع
        { key: 'locations', icon: <BankOutlined />, label: 'المواقع', background: '#1e88e5', gridArea: '4 / 1 / 5 / 3' }, // عرض 2
        { key: 'brands', icon: <BookOutlined />, label: 'العلامات', background: '#2196f3', gridArea: '4 / 3 / 5 / 4' },
        { key: 'models', icon: <ToolOutlined />, label: 'الموديلات', background: '#42a5f5', gridArea: '4 / 4 / 5 / 5' }
      ]
    },

    {
      title: 'المبيعات والمشتريات',
      color: '#52c41a',
      tiles: [
        // الصف الأول
        { key: 'sales-invoices', icon: <FileTextOutlined />, label: 'فاتورة بيع', background: '#52c41a', gridArea: '1 / 1 / 3 / 3' }, // مربع كبير 2×2
        { key: 'purchase-invoices', icon: <FileTextOutlined />, label: 'فاتورة شراء', background: '#73d13d', gridArea: '1 / 3 / 2 / 4' },
        { key: 'quotations', icon: <FileTextOutlined />, label: 'عروض الأسعار', background: '#95de64', gridArea: '1 / 4 / 2 / 5' },

        // الصف الثاني (جزء من المربع الكبير + عناصر جديدة)
        { key: 'purchase-orders', icon: <ShoppingCartOutlined />, label: 'أوامر الشراء', background: '#b7eb8f', gridArea: '2 / 3 / 3 / 4' },
        { key: 'sales-orders', icon: <UserOutlined />, label: 'أوامر البيع', background: '#d9f7be', gridArea: '2 / 4 / 3 / 5' },

        // الصف الثالث
        { key: 'returns-sales', icon: <FileTextOutlined />, label: 'مرتجعات البيع', background: '#389e0d', gridArea: '3 / 1 / 4 / 2' },
        { key: 'returns-purchase', icon: <FileTextOutlined />, label: 'مرتجعات الشراء', background: '#52c41a', gridArea: '3 / 2 / 4 / 3' },
        { key: 'inventory-movements', icon: <ToolOutlined />, label: 'حركة المخزون', background: '#73d13d', gridArea: '3 / 3 / 4 / 5' }, // عرض 2

        // الصف الرابع
        { key: 'vouchers', icon: <DollarOutlined />, label: 'السندات', background: '#95de64', gridArea: '4 / 1 / 5 / 2' },
        { key: 'banks', icon: <BankOutlined />, label: 'البنوك', background: '#b7eb8f', gridArea: '4 / 2 / 5 / 3' },
        { key: 'cash-flow', icon: <DollarOutlined />, label: 'التدفق النقدي', background: '#d9f7be', gridArea: '4 / 3 / 5 / 4' },
        { key: 'accounts', icon: <BookOutlined />, label: 'الحسابات', background: '#f6ffed', gridArea: '4 / 4 / 5 / 5' }
      ]
    },

    {
      title: 'الإنتاج والتصنيع',
      color: '#fa8c16',
      tiles: [
        // الصف الأول
        { key: 'furniture-production', icon: <ToolOutlined />, label: 'إنتاج الأثاث', background: '#fa8c16', gridArea: '1 / 1 / 2 / 4' }, // عرض 3
        { key: 'production-orders', icon: <FileTextOutlined />, label: 'أوامر الإنتاج', background: '#ffa940', gridArea: '1 / 4 / 2 / 5' },

        // الصف الثاني
        { key: 'paint-production', icon: <PictureOutlined />, label: 'دهان الأثاث', background: '#ffbb96', gridArea: '2 / 1 / 3 / 2' },
        { key: 'material-issue', icon: <InboxOutlined />, label: 'صرف المواد', background: '#ffd591', gridArea: '2 / 2 / 3 / 3' },
        { key: 'quality-control', icon: <SafetyOutlined />, label: 'مراقبة الجودة', background: '#ffe7ba', gridArea: '2 / 3 / 3 / 4' },
        { key: 'production-recipes', icon: <BookOutlined />, label: 'وصفات الإنتاج', background: '#fff2e8', gridArea: '2 / 4 / 3 / 5' },

        // الصف الثالث
        { key: 'labor-tracking', icon: <ClockCircleOutlined />, label: 'تتبع العمالة', background: '#fa8c16', gridArea: '3 / 1 / 4 / 3' }, // عرض 2
        { key: 'production-costs', icon: <DollarOutlined />, label: 'تكاليف الإنتاج', background: '#ffa940', gridArea: '3 / 3 / 4 / 4' },
        { key: 'maintenance', icon: <ToolOutlined />, label: 'الصيانة', background: '#ffbb96', gridArea: '3 / 4 / 4 / 5' },

        // الصف الرابع
        { key: 'production-reports', icon: <BarChartOutlined />, label: 'تقارير الإنتاج', background: '#ffd591', gridArea: '4 / 1 / 5 / 2' },
        { key: 'efficiency', icon: <DashboardOutlined />, label: 'الكفاءة', background: '#ffe7ba', gridArea: '4 / 2 / 5 / 3' },
        { key: 'production-planning', icon: <CalendarOutlined />, label: 'تخطيط الإنتاج', background: '#fff2e8', gridArea: '4 / 3 / 5 / 4' },
        { key: 'production-settings', icon: <SettingOutlined />, label: 'إعدادات الإنتاج', background: '#fff7e6', gridArea: '4 / 4 / 5 / 5' }
      ]
    },

    {
      title: 'الموظفين والتقارير',
      color: '#722ed1',
      tiles: [
        // الصف الأول
        { key: 'employees', icon: <TeamOutlined />, label: 'الموظفين', background: '#722ed1', gridArea: '1 / 1 / 2 / 2' },
        { key: 'attendance', icon: <ClockCircleOutlined />, label: 'الحضور والانصراف', background: '#9254de', gridArea: '1 / 2 / 2 / 4' }, // عرض 2
        { key: 'quick-checkin', icon: <LoginOutlined />, label: 'حضور سريع', background: '#b37feb', gridArea: '1 / 4 / 2 / 5' },

        // الصف الثاني
        { key: 'quick-checkout', icon: <LogoutOutlined />, label: 'انصراف سريع', background: '#d3adf7', gridArea: '2 / 1 / 2 / 2' },
        { key: 'payroll', icon: <CalendarOutlined />, label: 'الرواتب', background: '#efdbff', gridArea: '2 / 2 / 2 / 3' },
        { key: 'leaves', icon: <CalendarOutlined />, label: 'الإجازات', background: '#f9f0ff', gridArea: '2 / 3 / 2 / 4' },
        { key: 'overtime', icon: <ClockCircleOutlined />, label: 'الإضافي', background: '#722ed1', gridArea: '2 / 4 / 2 / 5' },

        // الصف الثالث
        { key: 'reports', icon: <BarChartOutlined />, label: 'التقارير', background: '#9254de', gridArea: '3 / 1 / 5 / 3' }, // مربع كبير 2×2
        { key: 'daily-summary', icon: <DashboardOutlined />, label: 'ملخص اليوم', background: '#b37feb', gridArea: '3 / 3 / 4 / 4' },
        { key: 'analytics', icon: <BarChartOutlined />, label: 'التحليلات', background: '#d3adf7', gridArea: '3 / 4 / 4 / 5' },

        // الصف الرابع (جزء من المربع الكبير + عناصر جديدة)
        { key: 'settings', icon: <SettingOutlined />, label: 'الإعدادات', background: '#efdbff', gridArea: '4 / 3 / 5 / 4' },
        { key: 'backup', icon: <SafetyOutlined />, label: 'النسخ الاحتياطي', background: '#f9f0ff', gridArea: '4 / 4 / 5 / 5' }
      ]
    }
  ]

  const handleShortcutClick = (key: string) => {
    onPlaySound('click')
    onShortcutClick(key)
  }

  // حساب العدد الإجمالي للبلاطات
  const totalTiles = tileGroups.reduce((total, group) => total + group.tiles.length, 0)

  console.log(`📊 Total tiles: ${totalTiles}, Groups: ${tileGroups.length}`)
  console.log('🎯 Tile groups:', tileGroups)

  return (
    <FloatingContainer>
      <GroupsContainer>
        {tileGroups.map((group, groupIndex) => (
          <TileGroup key={groupIndex}>
            <div className="group-title" style={{ color: group.color }}>
              <span style={{ fontSize: '20px', marginLeft: '8px' }}>
                {groupIndex === 0 && '🗂️'}
                {groupIndex === 1 && '💰'}
                {groupIndex === 2 && '🏭'}
                {groupIndex === 3 && '👥'}
              </span>
              {group.title}
            </div>
            <TilesGrid>
              {group.tiles.map((tile, index) => (
                <TileItem
                  key={tile.key}
                  background={tile.background}
                  gridArea={tile.gridArea}
                  className="tile-item"
                  onClick={() => handleShortcutClick(tile.key)}
                  style={{
                    animationDelay: `${(groupIndex * group.tiles.length + index) * 0.02}s`,
                    animation: 'fadeInScale 0.3s ease forwards',
                    opacity: 1 // fallback في حالة عدم عمل الـ animation
                  }}
                  title={`انقر للانتقال إلى ${tile.label}`}
                >
                  <div className="tile-icon">
                    {tile.icon}
                  </div>
                  <div className="tile-label">
                    {tile.label}
                  </div>
                </TileItem>
              ))}
            </TilesGrid>
          </TileGroup>
        ))}
      </GroupsContainer>

      {/* شعار ومعلومات الشركة */}
      <CompanyFooter>
        {companyLogo && (
          <div className="company-logo">
            <Avatar
              size={100}
              src={companyLogo}
              icon={<BankOutlined />}
              style={{
                border: 'none',
                boxShadow: 'none',
                borderRadius: '12px'
              }}
            />
          </div>
        )}

        {companyInfo && (
          <>
            <div className="company-name">{companyInfo.name}</div>
            {companyInfo.nameEn && (
              <div className="company-name-en">{companyInfo.nameEn}</div>
            )}

            <div className="company-details">
              {companyInfo.address && (
                <div className="detail-item">
                  <BankOutlined />
                  <span>{companyInfo.address}</span>
                </div>
              )}
              {companyInfo.phone && (
                <div className="detail-item">
                  <span>📞</span>
                  <span>{companyInfo.phone}</span>
                </div>
              )}
              {companyInfo.email && (
                <div className="detail-item">
                  <span>📧</span>
                  <span>{companyInfo.email}</span>
                </div>
              )}
              {companyInfo.website && (
                <div className="detail-item">
                  <span>🌐</span>
                  <span>{companyInfo.website}</span>
                </div>
              )}
            </div>

            <div className="company-slogan">
              ✨ نظام محاسبة وإنتاج متكامل - {totalTiles} اختصار في {tileGroups.length} مجموعات
            </div>
          </>
        )}
      </CompanyFooter>
    </FloatingContainer>
  )
}

export default QuickShortcuts
