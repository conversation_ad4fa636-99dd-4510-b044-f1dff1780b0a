/**
 * Hook مخصص لإدارة الصور
 * يوفر واجهة سهلة الاستخدام لجميع عمليات الصور
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  ImageCoreService, 
  type UnifiedImageData, 
  type ImageCategory, 
  type ImageContextType,
  type ImageUploadOptions,
  type ImageQueryOptions 
} from '../../services/images/ImageCoreService'
import { SafeLogger as Logger } from '../../utils/logger'

export interface UseImageManagerOptions {
  category: ImageCategory
  contextType: ImageContextType
  contextId: number
  autoLoad?: boolean
  uploadOptions?: ImageUploadOptions
}

export interface UseImageManagerReturn {
  // البيانات
  images: UnifiedImageData[]
  primaryImage: UnifiedImageData | null
  loading: boolean
  uploading: boolean
  error: string | null

  // الوظائف
  loadImages: () => Promise<void>
  uploadImage: (file: File) => Promise<boolean>
  uploadMultipleImages: (files: File[]) => Promise<boolean>
  deleteImage: (imageId: string) => Promise<boolean>
  setPrimaryImage: (imageId: string) => Promise<boolean>
  updateImage: (imageId: string, updates: Partial<UnifiedImageData>) => Promise<boolean>
  clearError: () => void
  refresh: () => Promise<void>

  // إحصائيات
  totalImages: number
  totalSize: number
  hasImages: boolean
  canUploadMore: (maxImages?: number) => boolean
}

/**
 * Hook لإدارة الصور
 */
export const useImageManager = (options: UseImageManagerOptions): UseImageManagerReturn => {
  const {
    category,
    contextType,
    contextId,
    autoLoad = true,
    uploadOptions = {}
  } = options

  // الحالات
  const [images, setImages] = useState<UnifiedImageData[]>([])
  const [primaryImage, setPrimaryImage] = useState<UnifiedImageData | null>(null)
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // المراجع
  const imageService = useRef(ImageCoreService.getInstance())
  const isInitialized = useRef(false)

  // تهيئة الخدمة
  useEffect(() => {
    const initializeService = async () => {
      if (isInitialized.current) return

      try {
        await imageService.current.initialize()
        isInitialized.current = true
        
        if (autoLoad) {
          await loadImages()
        }
      } catch (error) {
        Logger.error('useImageManager', 'فشل في تهيئة خدمة الصور:', error)
        setError('فشل في تهيئة نظام الصور')
      }
    }

    initializeService()
  }, [autoLoad])

  // إعادة تحميل الصور عند تغيير المعايير
  useEffect(() => {
    if (isInitialized.current && autoLoad) {
      loadImages()
    }
  }, [category, contextType, contextId, autoLoad])

  /**
   * تحميل الصور
   */
  const loadImages = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const queryOptions: ImageQueryOptions = {
        category,
        contextType,
        contextId,
        sortBy: 'sortOrder',
        sortOrder: 'asc'
      }

      const result = await imageService.current.getImages(queryOptions)

      if (result.success && result.data) {
        const imageList = result.data as UnifiedImageData[]
        setImages(imageList)
        
        // العثور على الصورة الرئيسية
        const primary = imageList.find(img => img.isPrimary) || null
        setPrimaryImage(primary)

        Logger.debug('useImageManager', `تم تحميل ${imageList.length} صورة`)
      } else {
        throw new Error(result.error || 'فشل في تحميل الصور')
      }
    } catch (error) {
      Logger.error('useImageManager', 'خطأ في تحميل الصور:', error)
      setError(error instanceof Error ? error.message : 'حدث خطأ في تحميل الصور')
    } finally {
      setLoading(false)
    }
  }, [category, contextType, contextId])

  /**
   * رفع صورة واحدة
   */
  const uploadImage = useCallback(async (file: File): Promise<boolean> => {
    try {
      setUploading(true)
      setError(null)

      const result = await imageService.current.uploadImage(
        file,
        category,
        contextType,
        contextId,
        uploadOptions
      )

      if (result.success) {
        await loadImages()
        Logger.info('useImageManager', `تم رفع الصورة: ${file.name}`)
        return true
      } else {
        throw new Error(result.error || 'فشل في رفع الصورة')
      }
    } catch (error) {
      Logger.error('useImageManager', 'خطأ في رفع الصورة:', error)
      setError(error instanceof Error ? error.message : 'حدث خطأ في رفع الصورة')
      return false
    } finally {
      setUploading(false)
    }
  }, [category, contextType, contextId, uploadOptions, loadImages])

  /**
   * رفع عدة صور
   */
  const uploadMultipleImages = useCallback(async (files: File[]): Promise<boolean> => {
    try {
      setUploading(true)
      setError(null)

      const result = await imageService.current.uploadMultipleImages(
        files,
        category,
        contextType,
        contextId,
        uploadOptions
      )

      if (result.success) {
        await loadImages()
        Logger.info('useImageManager', `تم رفع ${files.length} صورة`)
        return true
      } else {
        throw new Error(result.error || 'فشل في رفع الصور')
      }
    } catch (error) {
      Logger.error('useImageManager', 'خطأ في رفع الصور:', error)
      setError(error instanceof Error ? error.message : 'حدث خطأ في رفع الصور')
      return false
    } finally {
      setUploading(false)
    }
  }, [category, contextType, contextId, uploadOptions, loadImages])

  /**
   * حذف صورة
   */
  const deleteImage = useCallback(async (imageId: string): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const result = await imageService.current.deleteImage(imageId)

      if (result.success) {
        await loadImages()
        Logger.info('useImageManager', `تم حذف الصورة: ${imageId}`)
        return true
      } else {
        throw new Error(result.error || 'فشل في حذف الصورة')
      }
    } catch (error) {
      Logger.error('useImageManager', 'خطأ في حذف الصورة:', error)
      setError(error instanceof Error ? error.message : 'حدث خطأ في حذف الصورة')
      return false
    } finally {
      setLoading(false)
    }
  }, [loadImages])

  /**
   * تعيين صورة رئيسية
   */
  const setPrimaryImageHandler = useCallback(async (imageId: string): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const result = await imageService.current.setPrimaryImage(imageId, contextType, contextId)

      if (result.success) {
        await loadImages()
        Logger.info('useImageManager', `تم تعيين الصورة الرئيسية: ${imageId}`)
        return true
      } else {
        throw new Error(result.error || 'فشل في تعيين الصورة الرئيسية')
      }
    } catch (error) {
      Logger.error('useImageManager', 'خطأ في تعيين الصورة الرئيسية:', error)
      setError(error instanceof Error ? error.message : 'حدث خطأ في تعيين الصورة الرئيسية')
      return false
    } finally {
      setLoading(false)
    }
  }, [contextType, contextId, loadImages])

  /**
   * تحديث بيانات الصورة
   */
  const updateImage = useCallback(async (
    imageId: string, 
    updates: Partial<UnifiedImageData>
  ): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const result = await imageService.current.updateImage(imageId, updates)

      if (result.success) {
        await loadImages()
        Logger.info('useImageManager', `تم تحديث الصورة: ${imageId}`)
        return true
      } else {
        throw new Error(result.error || 'فشل في تحديث الصورة')
      }
    } catch (error) {
      Logger.error('useImageManager', 'خطأ في تحديث الصورة:', error)
      setError(error instanceof Error ? error.message : 'حدث خطأ في تحديث الصورة')
      return false
    } finally {
      setLoading(false)
    }
  }, [loadImages])

  /**
   * مسح رسالة الخطأ
   */
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  /**
   * تحديث البيانات
   */
  const refresh = useCallback(async () => {
    await loadImages()
  }, [loadImages])

  /**
   * التحقق من إمكانية رفع المزيد من الصور
   */
  const canUploadMore = useCallback((maxImages: number = 10): boolean => {
    return images.length < maxImages
  }, [images.length])

  // الإحصائيات المحسوبة
  const totalImages = images.length
  const totalSize = images.reduce((sum, img) => sum + img.size, 0)
  const hasImages = images.length > 0

  return {
    // البيانات
    images,
    primaryImage,
    loading,
    uploading,
    error,

    // الوظائف
    loadImages,
    uploadImage,
    uploadMultipleImages,
    deleteImage,
    setPrimaryImage: setPrimaryImageHandler,
    updateImage,
    clearError,
    refresh,

    // الإحصائيات
    totalImages,
    totalSize,
    hasImages,
    canUploadMore
  }
}

/**
 * Hook مبسط لجلب الصور فقط
 */
export const useImages = (
  category: ImageCategory,
  contextType: ImageContextType,
  contextId: number,
  queryOptions?: Partial<ImageQueryOptions>
) => {
  const [images, setImages] = useState<UnifiedImageData[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const imageService = useRef(ImageCoreService.getInstance())

  const loadImages = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      await imageService.current.initialize()

      const result = await imageService.current.getImages({
        category,
        contextType,
        contextId,
        ...queryOptions
      })

      if (result.success && result.data) {
        setImages(result.data as UnifiedImageData[])
      } else {
        throw new Error(result.error || 'فشل في تحميل الصور')
      }
    } catch (error) {
      Logger.error('useImages', 'خطأ في تحميل الصور:', error)
      setError(error instanceof Error ? error.message : 'حدث خطأ في تحميل الصور')
    } finally {
      setLoading(false)
    }
  }, [category, contextType, contextId, queryOptions])

  useEffect(() => {
    loadImages()
  }, [loadImages])

  return {
    images,
    loading,
    error,
    refresh: loadImages
  }
}
