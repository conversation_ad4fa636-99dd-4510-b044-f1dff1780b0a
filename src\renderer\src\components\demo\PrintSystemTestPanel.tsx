/**
 * لوحة اختبار نظام الطباعة - ZET.IA
 * تعرض نتائج الاختبار الشامل مع واجهة تفاعلية
 */

import React, { useState } from 'react'
import {
  Card,
  Button,
  Table,
  Tag,
  Space,
  Typography,
  Progress,
  Alert,
  Collapse,
  Spin,
  Row,
  Col,
  Statistic,
  Divider
} from 'antd'
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { runPrintSystemTest, runQuickTest, TestResult } from '../../utils/printSystemTest'

const { Title, Text, Paragraph } = Typography
const { Panel } = Collapse

const PrintSystemTestPanel: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [quickTestLoading, setQuickTestLoading] = useState(false)
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [quickTestResult, setQuickTestResult] = useState<boolean | null>(null)

  // تشغيل الاختبار الشامل
  const handleRunFullTest = async () => {
    setLoading(true)
    try {
      const results = await runPrintSystemTest()
      setTestResults(results)
    } catch (error) {
      console.error('خطأ في تشغيل الاختبار:', error)
    } finally {
      setLoading(false)
    }
  }

  // تشغيل الاختبار السريع
  const handleRunQuickTest = async () => {
    setQuickTestLoading(true)
    try {
      const result = await runQuickTest()
      setQuickTestResult(result)
    } catch (error) {
      console.error('خطأ في الاختبار السريع:', error)
      setQuickTestResult(false)
    } finally {
      setQuickTestLoading(false)
    }
  }

  // حساب الإحصائيات
  const getStatistics = () => {
    if (testResults.length === 0) return null

    const total = testResults.length
    const passed = testResults.filter(r => r.success).length
    const failed = total - passed
    const successRate = Math.round((passed / total) * 100)
    const totalDuration = testResults.reduce((sum, r) => sum + r.duration, 0)

    return { total, passed, failed, successRate, totalDuration }
  }

  const stats = getStatistics()

  // أعمدة الجدول
  const columns = [
    {
      title: 'اسم الاختبار',
      dataIndex: 'testName',
      key: 'testName',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: 'النتيجة',
      dataIndex: 'success',
      key: 'success',
      render: (success: boolean) => (
        <Tag color={success ? 'success' : 'error'} icon={success ? <CheckCircleOutlined /> : <CloseCircleOutlined />}>
          {success ? 'نجح' : 'فشل'}
        </Tag>
      )
    },
    {
      title: 'الرسالة',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true
    },
    {
      title: 'المدة (ms)',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration: number) => <Text type="secondary">{duration}</Text>,
      sorter: (a: TestResult, b: TestResult) => a.duration - b.duration
    }
  ]

  // تحميل التقرير
  const handleDownloadReport = () => {
    if (testResults.length === 0) return

    const report = generateDetailedReport()
    const blob = new Blob([report], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `print-system-test-report-${new Date().toISOString().split('T')[0]}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // إنشاء التقرير المفصل
  const generateDetailedReport = (): string => {
    if (!stats) return ''

    let report = `# تقرير اختبار نظام الطباعة - ZET.IA\n\n`
    report += `**التاريخ**: ${new Date().toLocaleString('ar-SA')}\n`
    report += `**إجمالي الاختبارات**: ${stats.total}\n`
    report += `**النجح**: ${stats.passed}\n`
    report += `**الفاشل**: ${stats.failed}\n`
    report += `**معدل النجاح**: ${stats.successRate}%\n`
    report += `**إجمالي الوقت**: ${stats.totalDuration}ms\n\n`

    report += `## تفاصيل الاختبارات\n\n`

    testResults.forEach(result => {
      const status = result.success ? '✅' : '❌'
      report += `### ${status} ${result.testName}\n`
      report += `- **النتيجة**: ${result.message}\n`
      report += `- **المدة**: ${result.duration}ms\n`
      if (result.details) {
        report += `- **التفاصيل**: \`${JSON.stringify(result.details, null, 2)}\`\n`
      }
      report += `\n`
    })

    return report
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={2}>
          <InfoCircleOutlined style={{ marginLeft: 8 }} />
          اختبار نظام الطباعة
        </Title>
        
        <Paragraph>
          هذه اللوحة تتيح لك اختبار جميع مكونات نظام الطباعة المحدث للتأكد من عمله بشكل صحيح.
        </Paragraph>

        <Space size="large" style={{ marginBottom: 24 }}>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            loading={loading}
            onClick={handleRunFullTest}
            size="large"
          >
            تشغيل الاختبار الشامل
          </Button>

          <Button
            icon={<ReloadOutlined />}
            loading={quickTestLoading}
            onClick={handleRunQuickTest}
          >
            اختبار سريع
          </Button>

          {testResults.length > 0 && (
            <Button
              icon={<DownloadOutlined />}
              onClick={handleDownloadReport}
            >
              تحميل التقرير
            </Button>
          )}
        </Space>

        {/* نتيجة الاختبار السريع */}
        {quickTestResult !== null && (
          <Alert
            message={quickTestResult ? 'الاختبار السريع نجح' : 'الاختبار السريع فشل'}
            type={quickTestResult ? 'success' : 'error'}
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {/* الإحصائيات */}
        {stats && (
          <Card style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="إجمالي الاختبارات"
                  value={stats.total}
                  prefix={<InfoCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="نجح"
                  value={stats.passed}
                  valueStyle={{ color: '#3f8600' }}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="فشل"
                  value={stats.failed}
                  valueStyle={{ color: '#cf1322' }}
                  prefix={<CloseCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="معدل النجاح"
                  value={stats.successRate}
                  suffix="%"
                  valueStyle={{ color: stats.successRate >= 80 ? '#3f8600' : '#cf1322' }}
                />
              </Col>
            </Row>
            
            <Divider />
            
            <Progress
              percent={stats.successRate}
              status={stats.successRate >= 80 ? 'success' : 'exception'}
              strokeColor={stats.successRate >= 80 ? '#52c41a' : '#ff4d4f'}
            />
          </Card>
        )}

        {/* جدول النتائج */}
        {loading ? (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              <Text>جاري تشغيل الاختبارات...</Text>
            </div>
          </div>
        ) : testResults.length > 0 ? (
          <>
            <Title level={3}>نتائج الاختبار</Title>
            <Table
              columns={columns}
              dataSource={testResults.map((result, index) => ({ ...result, key: index }))}
              pagination={false}
              size="middle"
              expandable={{
                expandedRowRender: (record: TestResult) => (
                  <div style={{ padding: '16px', backgroundColor: '#fafafa' }}>
                    <Text strong>التفاصيل:</Text>
                    <pre style={{ marginTop: 8, fontSize: '12px' }}>
                      {JSON.stringify(record.details, null, 2)}
                    </pre>
                  </div>
                ),
                rowExpandable: (record: TestResult) => !!record.details
              }}
            />

            {/* تفاصيل إضافية */}
            <Collapse style={{ marginTop: 16 }}>
              <Panel header="معلومات إضافية" key="1">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text>
                    <strong>إجمالي وقت التنفيذ:</strong> {stats?.totalDuration}ms
                  </Text>
                  <Text>
                    <strong>متوسط وقت الاختبار:</strong> {stats ? Math.round(stats.totalDuration / stats.total) : 0}ms
                  </Text>
                  <Text>
                    <strong>تاريخ التشغيل:</strong> {new Date().toLocaleString('ar-SA')}
                  </Text>
                </Space>
              </Panel>
            </Collapse>
          </>
        ) : (
          <Alert
            message="لم يتم تشغيل أي اختبارات بعد"
            description="اضغط على 'تشغيل الاختبار الشامل' لبدء اختبار النظام"
            type="info"
            showIcon
          />
        )}
      </Card>
    </div>
  )
}

export default PrintSystemTestPanel
