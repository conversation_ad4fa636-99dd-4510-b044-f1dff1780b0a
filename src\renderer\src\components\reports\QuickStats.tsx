import React from 'react'
import { Card, Row, Col, Statistic, Progress, Typography, Space, Tooltip } from 'antd'
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  DollarOutlined,
  UserOutlined,
  ShopOutlined,
  FileTextOutlined,
  TrophyOutlined,
  WarningOutlined
} from '@ant-design/icons'

const { Text } = Typography

export interface StatItem {
  title: string
  value: number | string
  prefix?: React.ReactNode
  suffix?: string
  precision?: number
  trend?: {
    value: number
    isPositive: boolean
    label?: string
  }
  color?: string
  background?: string
  icon?: React.ReactNode
  description?: string
  progress?: {
    percent: number
    status?: 'success' | 'active' | 'exception'
  }
}

interface QuickStatsProps {
  stats: StatItem[]
  loading?: boolean
  columns?: number
}

const QuickStats: React.FC<QuickStatsProps> = ({ 
  stats, 
  loading = false, 
  columns = 4 
}) => {
  const getColSpan = () => {
    return 24 / columns
  }

  const getDefaultBackground = (index: number) => {
    const backgrounds = [
      'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
      'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
    ]
    return backgrounds[index % backgrounds.length]
  }

  const renderTrend = (trend: StatItem['trend']) => {
    if (!trend) return null

    const { value, isPositive, label } = trend
    const color = isPositive ? '#52c41a' : '#ff4d4f'
    const icon = isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />

    return (
      <div style={{ fontSize: '12px', color, marginTop: '4px' }}>
        {icon} {Math.abs(value).toFixed(1)}% {label || (isPositive ? 'زيادة' : 'نقص')}
      </div>
    )
  }

  const renderProgress = (progress: StatItem['progress']) => {
    if (!progress) return null

    return (
      <Progress
        percent={progress.percent}
        size="small"
        status={progress.status || 'active'}
        style={{ marginTop: '8px' }}
        strokeColor={progress.status === 'success' ? '#52c41a' : undefined}
      />
    )
  }

  return (
    <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
      {stats.map((stat, index) => (
        <Col span={getColSpan()} key={index}>
          <Card
            hoverable
            loading={loading}
            style={{
              background: stat.background || getDefaultBackground(index),
              color: '#fff',
              border: 'none',
              borderRadius: '12px',
              overflow: 'hidden'
            }}
            styles={{ body: { padding: '20px' } }}
          >
            <div style={{ position: 'relative' }}>
              {/* الأيقونة */}
              {stat.icon && (
                <div style={{
                  position: 'absolute',
                  top: '-10px',
                  left: '-10px',
                  fontSize: '48px',
                  opacity: 0.2
                }}>
                  {stat.icon}
                </div>
              )}

              {/* المحتوى الرئيسي */}
              <div style={{ position: 'relative', zIndex: 1 }}>
                <Statistic
                  title={
                    <div style={{ color: '#fff', opacity: 0.9, fontSize: '14px', marginBottom: '8px' }}>
                      {stat.title}
                    </div>
                  }
                  value={stat.value}
                  valueStyle={{
                    color: '#fff',
                    fontSize: '28px',
                    fontWeight: 'bold',
                    lineHeight: '1.2'
                  }}
                  prefix={stat.prefix}
                  suffix={stat.suffix}
                  precision={stat.precision}
                />

                {/* الوصف */}
                {stat.description && (
                  <div style={{
                    color: '#fff',
                    opacity: 0.8,
                    fontSize: '12px',
                    marginTop: '8px',
                    lineHeight: '1.4'
                  }}>
                    {stat.description}
                  </div>
                )}

                {/* الاتجاه */}
                {renderTrend(stat.trend)}

                {/* شريط التقدم */}
                {renderProgress(stat.progress)}
              </div>
            </div>
          </Card>
        </Col>
      ))}
    </Row>
  )
}

// مكونات إحصائيات جاهزة للاستخدام
export const SalesStats: React.FC<{ data: any; loading?: boolean }> = ({ data, loading }) => {
  const stats: StatItem[] = [
    {
      title: 'إجمالي المبيعات',
      value: data?.totalSales || 0,
      prefix: '₪',
      precision: 0,
      icon: <DollarOutlined />,
      trend: {
        value: 15.2,
        isPositive: true,
        label: 'من الشهر الماضي'
      },
      description: `${data?.totalInvoices || 0} فاتورة`
    },
    {
      title: 'العملاء النشطين',
      value: data?.activeCustomers || 0,
      icon: <UserOutlined />,
      trend: {
        value: 8.5,
        isPositive: true,
        label: 'عملاء جدد'
      },
      description: `${data?.newCustomers || 0} عميل جديد`
    },
    {
      title: 'المنتجات المباعة',
      value: data?.productsSold || 0,
      icon: <ShopOutlined />,
      trend: {
        value: 12.3,
        isPositive: true,
        label: 'زيادة في المبيعات'
      },
      description: `${data?.topProducts || 0} منتج رائج`
    },
    {
      title: 'معدل الربح',
      value: data?.profitMargin || 0,
      suffix: '%',
      precision: 1,
      icon: <TrophyOutlined />,
      progress: {
        percent: data?.profitMargin || 0,
        status: (data?.profitMargin || 0) >= 30 ? 'success' : 'active'
      },
      description: 'هدف: 35%'
    }
  ]

  return <QuickStats stats={stats} loading={loading} />
}

export const CustomerStats: React.FC<{ data: any; loading?: boolean }> = ({ data, loading }) => {
  const stats: StatItem[] = [
    {
      title: 'إجمالي العملاء',
      value: data?.totalCustomers || 0,
      icon: <UserOutlined />,
      description: `VIP: ${data?.vipCustomers || 0} | جديد: ${data?.newCustomers || 0}`
    },
    {
      title: 'إجمالي المبيعات',
      value: data?.totalSales || 0,
      prefix: '₪',
      precision: 0,
      icon: <DollarOutlined />,
      description: `متوسط: ₪${Math.round((data?.totalSales || 0) / (data?.totalCustomers || 1)).toLocaleString()}`
    },
    {
      title: 'معدل الدفع',
      value: data?.paymentRate || 0,
      suffix: '%',
      precision: 1,
      icon: <FileTextOutlined />,
      progress: {
        percent: data?.paymentRate || 0,
        status: (data?.paymentRate || 0) >= 90 ? 'success' : 'active'
      }
    },
    {
      title: 'عملاء عالي المخاطر',
      value: data?.highRiskCustomers || 0,
      icon: <WarningOutlined />,
      description: 'تجاوزوا الحد الائتماني'
    }
  ]

  return <QuickStats stats={stats} loading={loading} />
}

export const ProductStats: React.FC<{ data: any; loading?: boolean }> = ({ data, loading }) => {
  const stats: StatItem[] = [
    {
      title: 'إجمالي المنتجات',
      value: data?.totalProducts || 0,
      icon: <ShopOutlined />,
      description: `ممتاز: ${data?.excellentProducts || 0} | منخفض المخزون: ${data?.lowStockProducts || 0}`
    },
    {
      title: 'إجمالي المبيعات',
      value: data?.totalSales || 0,
      prefix: '₪',
      precision: 0,
      icon: <DollarOutlined />,
      description: `الكمية: ${(data?.totalQuantity || 0).toLocaleString()} وحدة`
    },
    {
      title: 'إجمالي الربح',
      value: data?.totalProfit || 0,
      prefix: '₪',
      precision: 0,
      icon: <TrophyOutlined />,
      description: `هامش الربح: ${(data?.avgProfitMargin || 0).toFixed(1)}%`
    },
    {
      title: 'متوسط السعر',
      value: data?.avgPrice || 0,
      prefix: '₪',
      precision: 0,
      icon: <FileTextOutlined />,
      description: `${data?.totalInvoices || 0} فاتورة`
    }
  ]

  return <QuickStats stats={stats} loading={loading} />
}

export default QuickStats
