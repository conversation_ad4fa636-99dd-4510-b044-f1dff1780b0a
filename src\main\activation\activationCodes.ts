/**
 * أنواع التراخيص المختلفة
 */
export enum LicenseType {
  MONTHLY = 'MONTHLY',
  LIFETIME = 'LIFETIME',
  BLOCK = 'BLOCK'
}

/**
 * معلومات الترخيص
 */
export interface LicenseInfo {
  type: LicenseType
  duration: number // بالأيام، -1 للمدى الحياة، 0 للحظر
  months: number // عدد الأشهر، -1 للمدى الحياة، 0 للحظر
  description: string
}

/**
 * أرقام التفعيل المحددة مسبقاً
 */
export class ActivationCodes {
  // كلمة التفعيل الأساسية الموحدة
  private static readonly BASE_ACTIVATION_CODE = 'ZET-2024-FARES'

  // رقم الحظر/الإلغاء
  private static readonly BLOCK_CODE = 'ZET-BL-2024-FARES'

  // رقم مدى الحياة
  private static readonly LIFETIME_CODE = 'ZET-LT-2024-FARES'

  /**
   * استخراج عدد الأشهر من كلمة التفعيل
   */
  private static extractMonthsFromCode(code: string): number | null {
    const normalizedCode = code.trim().toUpperCase()

    // التحقق من رقم الحظر
    if (normalizedCode === this.BLOCK_CODE) {
      return 0
    }

    // التحقق من رقم مدى الحياة
    if (normalizedCode === this.LIFETIME_CODE) {
      return -1
    }

    // التحقق من النمط الجديد: ZET-2024-FARES-mX
    const monthPattern = new RegExp(`^${this.BASE_ACTIVATION_CODE}-m(\\d+)$`, 'i')
    const match = normalizedCode.match(monthPattern)

    if (match) {
      const months = parseInt(match[1], 10)
      // التحقق من أن عدد الأشهر صحيح (بين 1 و 120 شهر = 10 سنوات)
      if (months >= 1 && months <= 120) {
        return months
      }
    }

    return null
  }

  /**
   * التحقق من صحة رقم التفعيل
   */
  public static validateActivationCode(code: string): LicenseInfo | null {
    const months = this.extractMonthsFromCode(code)

    if (months === null) {
      return null
    }

    // رقم الحظر
    if (months === 0) {
      return {
        type: LicenseType.BLOCK,
        duration: 0,
        months: 0,
        description: 'إلغاء التفعيل'
      }
    }

    // رقم مدى الحياة
    if (months === -1) {
      return {
        type: LicenseType.LIFETIME,
        duration: -1,
        months: -1,
        description: 'ترخيص مدى الحياة'
      }
    }

    // ترخيص شهري
    const days = months * 30 // تقريب 30 يوم لكل شهر
    return {
      type: LicenseType.MONTHLY,
      duration: days,
      months: months,
      description: `ترخيص لمدة ${months} ${months === 1 ? 'شهر' : 'شهر'}`
    }
  }

  /**
   * الحصول على جميع أرقام التفعيل المتاحة (للمطور فقط)
   */
  public static getAllAvailableCodes(): string[] {
    const codes = [
      this.BLOCK_CODE,
      this.LIFETIME_CODE
    ]

    // إضافة أمثلة على الأكواد الشهرية
    const commonMonths = [1, 2, 6, 12, 24, 48]
    commonMonths.forEach(months => {
      codes.push(`${this.BASE_ACTIVATION_CODE}-m${months}`)
    })

    return codes
  }

  /**
   * إنشاء رقم تفعيل لعدد أشهر محدد
   */
  public static generateCodeForMonths(months: number): string {
    if (months <= 0) {
      throw new Error('عدد الأشهر يجب أن يكون أكبر من صفر')
    }
    if (months > 120) {
      throw new Error('عدد الأشهر لا يمكن أن يتجاوز 120 شهر')
    }

    return `${this.BASE_ACTIVATION_CODE}-m${months}`
  }



  /**
   * التحقق من أن الرقم هو رقم حظر
   */
  public static isBlockCode(code: string): boolean {
    const licenseInfo = this.validateActivationCode(code)
    return licenseInfo?.type === LicenseType.BLOCK
  }

  /**
   * حساب تاريخ انتهاء الصلاحية
   */
  public static calculateExpiryDate(licenseInfo: LicenseInfo): Date | null {
    if (licenseInfo.type === LicenseType.LIFETIME) {
      return null // لا ينتهي
    }
    
    if (licenseInfo.type === LicenseType.BLOCK) {
      return new Date() // ينتهي فوراً
    }

    const now = new Date()
    const expiryDate = new Date(now.getTime() + (licenseInfo.duration * 24 * 60 * 60 * 1000))
    return expiryDate
  }

  /**
   * التحقق من انتهاء صلاحية الترخيص
   */
  public static isLicenseExpired(expiryDate: Date | null): boolean {
    if (!expiryDate) {
      return false // ترخيص مدى الحياة
    }
    
    return new Date() > expiryDate
  }

  /**
   * حساب الأيام المتبقية
   */
  public static getDaysRemaining(expiryDate: Date | null): number {
    if (!expiryDate) {
      return -1 // مدى الحياة
    }

    const now = new Date()
    const diffTime = expiryDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return Math.max(0, diffDays)
  }

  /**
   * تنسيق معلومات الترخيص للعرض
   */
  public static formatLicenseInfo(licenseInfo: LicenseInfo, expiryDate: Date | null): string {
    switch (licenseInfo.type) {
      case LicenseType.LIFETIME:
        return 'ترخيص مدى الحياة'
      case LicenseType.BLOCK:
        return 'تم إلغاء التفعيل'
      case LicenseType.MONTHLY:
        if (expiryDate) {
          const daysRemaining = this.getDaysRemaining(expiryDate)
          if (daysRemaining > 0) {
            return `${licenseInfo.description} - متبقي ${daysRemaining} يوم`
          } else {
            return 'انتهت صلاحية الترخيص'
          }
        }
        return licenseInfo.description
      default:
        return licenseInfo.description
    }
  }
}
