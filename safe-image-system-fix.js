const fs = require('fs');
const path = require('path');
const initSqlJs = require('sql.js');

async function safeImageSystemFix() {
  try {
    console.log('🔧 بدء الإصلاح الآمن لنظام الصور...\n');
    
    // مسار قاعدة البيانات الأساسية فقط
    const dbPath = path.join(process.env.APPDATA, 'accounting-production-app', 'accounting.db');
    
    if (!fs.existsSync(dbPath)) {
      console.log('❌ قاعدة البيانات غير موجودة:', dbPath);
      return;
    }
    
    console.log(`📍 فحص قاعدة البيانات: ${dbPath}`);
    
    // تهيئة SQL.js
    const SQL = await initSqlJs({
      locateFile: file => path.join(__dirname, 'node_modules', 'sql.js', 'dist', file)
    });
    
    const filebuffer = fs.readFileSync(dbPath);
    const db = new SQL.Database(filebuffer);
    
    // 1. فحص هيكل قاعدة البيانات الموجود
    console.log('🔍 فحص هيكل قاعدة البيانات...');
    
    // فحص جدول production_orders
    let productionOrdersExists = false;
    let productionOrdersColumns = [];
    
    try {
      const tableCheck = db.exec("SELECT name FROM sqlite_master WHERE type='table' AND name='production_orders'");
      if (tableCheck[0]?.values?.length > 0) {
        productionOrdersExists = true;
        const columnsResult = db.exec("PRAGMA table_info(production_orders)");
        productionOrdersColumns = columnsResult[0]?.values?.map(row => row[1]) || [];
        console.log(`   ✅ جدول production_orders موجود مع ${productionOrdersColumns.length} عمود`);
        console.log(`   📋 الأعمدة: ${productionOrdersColumns.join(', ')}`);
      } else {
        console.log('   ❌ جدول production_orders غير موجود');
      }
    } catch (error) {
      console.log('   ❌ خطأ في فحص جدول production_orders:', error.message);
    }
    
    // فحص جدول users
    let usersExists = false;
    let defaultUserId = null;
    
    try {
      const usersCheck = db.exec("SELECT name FROM sqlite_master WHERE type='table' AND name='users'");
      if (usersCheck[0]?.values?.length > 0) {
        usersExists = true;
        const usersResult = db.exec("SELECT id FROM users LIMIT 1");
        defaultUserId = usersResult[0]?.values[0]?.[0] || null;
        console.log(`   ✅ جدول users موجود، أول مستخدم ID: ${defaultUserId}`);
      } else {
        console.log('   ❌ جدول users غير موجود');
      }
    } catch (error) {
      console.log('   ❌ خطأ في فحص جدول users:', error.message);
    }
    
    // 2. إنشاء جدول صور أوامر الإنتاج (بدون FOREIGN KEY)
    console.log('\n📋 إنشاء جدول صور أوامر الإنتاج...');
    
    try {
      db.run(`
        CREATE TABLE IF NOT EXISTS production_order_images (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          order_id INTEGER NOT NULL,
          image_name TEXT NOT NULL,
          image_path TEXT NOT NULL,
          file_size INTEGER DEFAULT 0,
          file_type TEXT DEFAULT 'image/jpeg',
          description TEXT,
          category TEXT DEFAULT 'general',
          is_primary INTEGER DEFAULT 0,
          tags TEXT,
          notes TEXT,
          uploaded_by INTEGER,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('   ✅ تم إنشاء جدول production_order_images بنجاح');
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('table') && error.message.includes('exists')) {
        console.log('   ℹ️ جدول production_order_images موجود مسبقاً');
      } else {
        console.log('   ❌ خطأ في إنشاء جدول production_order_images:', error.message);
        throw error;
      }
    }
    
    // 3. إنشاء جدول الصور الموحد (بدون FOREIGN KEY)
    console.log('📋 إنشاء جدول الصور الموحد...');
    
    try {
      db.run(`
        CREATE TABLE IF NOT EXISTS unified_images (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          original_name TEXT NOT NULL,
          path TEXT NOT NULL,
          thumbnail_path TEXT,
          size INTEGER NOT NULL,
          width INTEGER,
          height INTEGER,
          type TEXT NOT NULL,
          category TEXT NOT NULL,
          context_type TEXT NOT NULL,
          context_id INTEGER NOT NULL,
          description TEXT DEFAULT '',
          tags TEXT DEFAULT '[]',
          is_active INTEGER DEFAULT 1,
          is_primary INTEGER DEFAULT 0,
          sort_order INTEGER DEFAULT 0,
          uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          uploaded_by INTEGER,
          metadata TEXT DEFAULT '{}',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('   ✅ تم إنشاء جدول unified_images بنجاح');
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('table') && error.message.includes('exists')) {
        console.log('   ℹ️ جدول unified_images موجود مسبقاً');
      } else {
        console.log('   ❌ خطأ في إنشاء جدول unified_images:', error.message);
        throw error;
      }
    }
    
    // 4. إنشاء الفهارس
    console.log('🔍 إنشاء الفهارس...');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_production_order_images_order_id ON production_order_images(order_id)',
      'CREATE INDEX IF NOT EXISTS idx_production_order_images_category ON production_order_images(category)',
      'CREATE INDEX IF NOT EXISTS idx_unified_images_context ON unified_images(context_type, context_id)',
      'CREATE INDEX IF NOT EXISTS idx_unified_images_category ON unified_images(category)',
      'CREATE INDEX IF NOT EXISTS idx_unified_images_active ON unified_images(is_active)'
    ];
    
    let indexCount = 0;
    indexes.forEach(indexSql => {
      try {
        db.run(indexSql);
        indexCount++;
      } catch (error) {
        // تجاهل أخطاء الفهارس الموجودة
      }
    });
    console.log(`   ✅ تم إنشاء ${indexCount} فهرس`);
    
    // 5. إنشاء أوامر إنتاج تجريبية (فقط إذا كان الجدول موجود وفارغ)
    if (productionOrdersExists) {
      console.log('\n📋 فحص أوامر الإنتاج...');
      
      const ordersResult = db.exec('SELECT COUNT(*) FROM production_orders');
      const ordersCount = ordersResult[0]?.values[0]?.[0] || 0;
      
      if (ordersCount === 0) {
        console.log('📝 إنشاء أوامر إنتاج تجريبية...');
        
        // تحديد الأعمدة المتاحة
        const hasProductId = productionOrdersColumns.includes('product_id');
        const hasItemId = productionOrdersColumns.includes('item_id');
        const hasCustomerId = productionOrdersColumns.includes('customer_id');
        const hasCustomerName = productionOrdersColumns.includes('customer_name');
        const hasStartDate = productionOrdersColumns.includes('start_date');
        const hasOrderDate = productionOrdersColumns.includes('order_date');
        
        // بناء SQL بناءً على الأعمدة المتاحة
        let insertColumns = ['order_number', 'quantity', 'status', 'priority'];
        let insertValues = ['?', '?', '?', '?'];
        let insertData = [];
        
        if (hasProductId) {
          insertColumns.push('product_id');
          insertValues.push('?');
        } else if (hasItemId) {
          insertColumns.push('item_id');
          insertValues.push('?');
        }
        
        if (hasCustomerId) {
          insertColumns.push('customer_id');
          insertValues.push('?');
        } else if (hasCustomerName) {
          insertColumns.push('customer_name');
          insertValues.push('?');
        }
        
        if (hasStartDate) {
          insertColumns.push('start_date');
          insertValues.push('?');
        } else if (hasOrderDate) {
          insertColumns.push('order_date');
          insertValues.push('?');
        }
        
        const sampleOrders = [
          { order_number: 'TEST-001', quantity: 5, status: 'pending', priority: 'normal' },
          { order_number: 'TEST-002', quantity: 10, status: 'in_progress', priority: 'high' },
          { order_number: 'TEST-003', quantity: 3, status: 'completed', priority: 'low' }
        ];
        
        for (const order of sampleOrders) {
          try {
            let values = [order.order_number, order.quantity, order.status, order.priority];
            
            if (hasProductId || hasItemId) {
              values.push(1); // ID افتراضي
            }
            
            if (hasCustomerId) {
              values.push(1); // ID عميل افتراضي
            } else if (hasCustomerName) {
              values.push('عميل تجريبي');
            }
            
            if (hasStartDate || hasOrderDate) {
              values.push(new Date().toISOString().split('T')[0]); // تاريخ اليوم
            }
            
            const sql = `INSERT INTO production_orders (${insertColumns.join(', ')}) VALUES (${insertValues.join(', ')})`;
            db.run(sql, values);
            
            console.log(`   ✅ أمر إنتاج: ${order.order_number}`);
          } catch (error) {
            console.log(`   ⚠️ تخطي أمر ${order.order_number}: ${error.message}`);
          }
        }
      } else {
        console.log(`   ℹ️ يوجد ${ordersCount} أمر إنتاج مسبقاً`);
      }
      
      // 6. إنشاء صور تجريبية بسيطة
      console.log('\n🖼️ إنشاء صور تجريبية...');
      
      const allOrdersResult = db.exec('SELECT id, order_number FROM production_orders LIMIT 3');
      
      if (allOrdersResult[0]?.values) {
        for (const orderRow of allOrdersResult[0].values) {
          const orderId = orderRow[0];
          const orderNumber = orderRow[1];
          
          console.log(`   📸 إنشاء صور لأمر ${orderNumber}...`);
          
          // إنشاء صورة SVG بسيطة
          const svgContent = `<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="150" fill="#4ECDC4"/><text x="100" y="75" text-anchor="middle" fill="white" font-size="16">صورة تجريبية</text><text x="100" y="100" text-anchor="middle" fill="white" font-size="12">${orderNumber}</text></svg>`;
          
          const dataUrl = `data:image/svg+xml;base64,${Buffer.from(svgContent).toString('base64')}`;
          
          try {
            db.run(`
              INSERT INTO production_order_images (
                order_id, image_name, image_path, file_size, file_type,
                description, category, is_primary, uploaded_by
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              orderId,
              `صورة تجريبية - ${orderNumber}`,
              dataUrl,
              svgContent.length,
              'image/svg+xml',
              'صورة تجريبية للاختبار',
              'general',
              1,
              defaultUserId
            ]);
            
            console.log(`     ✅ صورة تجريبية`);
          } catch (error) {
            console.log(`     ❌ خطأ في إنشاء الصورة: ${error.message}`);
          }
        }
      }
    } else {
      console.log('\n⚠️ تخطي إنشاء أوامر الإنتاج - الجدول غير موجود');
    }
    
    // 7. حفظ قاعدة البيانات
    console.log('\n💾 حفظ التغييرات...');
    const data = db.export();
    fs.writeFileSync(dbPath, data);
    
    // 8. إحصائيات النتائج
    console.log('\n📊 إحصائيات النتائج:');
    
    try {
      if (productionOrdersExists) {
        const ordersCount = db.exec('SELECT COUNT(*) FROM production_orders')[0]?.values[0]?.[0] || 0;
        console.log(`   📋 أوامر الإنتاج: ${ordersCount}`);
      }
      
      const imagesCount = db.exec('SELECT COUNT(*) FROM production_order_images')[0]?.values[0]?.[0] || 0;
      console.log(`   🖼️ صور أوامر الإنتاج: ${imagesCount}`);
      
      const unifiedCount = db.exec('SELECT COUNT(*) FROM unified_images')[0]?.values[0]?.[0] || 0;
      console.log(`   🎯 الصور الموحدة: ${unifiedCount}`);
    } catch (error) {
      console.log('   ❌ خطأ في قراءة الإحصائيات:', error.message);
    }
    
    db.close();
    
    console.log('\n' + '='.repeat(60));
    console.log('✅ تم الإصلاح الآمن لنظام الصور بنجاح!');
    console.log('='.repeat(60));
    
    console.log('\n📝 الخطوات التالية:');
    console.log('   1. إعادة تشغيل التطبيق');
    console.log('   2. فتح قسم أوامر الإنتاج');
    console.log('   3. اختبار عرض الصور');
    console.log('   4. اختبار طباعة أمر إنتاج');
    
  } catch (error) {
    console.error('❌ خطأ في الإصلاح الآمن:', error);
  }
}

// تشغيل الإصلاح الآمن
safeImageSystemFix();
