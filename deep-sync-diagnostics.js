const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

async function deepSyncDiagnostics() {
  console.log('🔍 فحص عميق لقسم ربط الأجهزة والمزامنة\n');
  console.log('=' .repeat(60));
  
  const results = {
    networkConnectivity: null,
    arpTable: null,
    networkInterfaces: null,
    sharedFolders: null,
    permissions: null,
    services: null,
    firewall: null,
    syncSettings: null
  };
  
  // 1. فحص الاتصال بالشبكة
  console.log('\n📡 1. فحص الاتصال بالشبكة:');
  try {
    const { stdout: ipconfig } = await execAsync('ipconfig /all');
    const networkInfo = parseNetworkInfo(ipconfig);
    results.networkConnectivity = networkInfo;
    
    console.log(`   🌐 عنوان IP: ${networkInfo.ip || 'غير محدد'}`);
    console.log(`   🔗 Gateway: ${networkInfo.gateway || 'غير محدد'}`);
    console.log(`   📶 DNS: ${networkInfo.dns || 'غير محدد'}`);
    console.log(`   🏷️  اسم الجهاز: ${networkInfo.hostname || 'غير محدد'}`);
  } catch (error) {
    console.log(`   ❌ خطأ في فحص الشبكة: ${error.message}`);
    results.networkConnectivity = { error: error.message };
  }
  
  // 2. فحص ARP Table
  console.log('\n🔍 2. فحص ARP Table (الأجهزة المكتشفة):');
  try {
    const { stdout: arpOutput } = await execAsync('arp -a');
    const devices = parseArpTable(arpOutput);
    results.arpTable = devices;
    
    console.log(`   📊 عدد الأجهزة المكتشفة: ${devices.length}`);
    devices.slice(0, 5).forEach((device, index) => {
      console.log(`   ${index + 1}. ${device.ip} → ${device.mac} (${device.type})`);
    });
    if (devices.length > 5) {
      console.log(`   ... و ${devices.length - 5} جهاز آخر`);
    }
  } catch (error) {
    console.log(`   ❌ خطأ في فحص ARP: ${error.message}`);
    results.arpTable = { error: error.message };
  }
  
  // 3. فحص المجلدات المشتركة
  console.log('\n📁 3. فحص المجلدات المشتركة:');
  try {
    const { stdout: netShare } = await execAsync('net share');
    const shares = parseNetShares(netShare);
    results.sharedFolders = shares;
    
    console.log(`   📊 عدد المجلدات المشتركة: ${shares.length}`);
    shares.forEach((share, index) => {
      console.log(`   ${index + 1}. ${share.name} → ${share.path}`);
    });
  } catch (error) {
    console.log(`   ❌ خطأ في فحص المجلدات المشتركة: ${error.message}`);
    results.sharedFolders = { error: error.message };
  }
  
  // 4. فحص الصلاحيات
  console.log('\n🔐 4. فحص الصلاحيات:');
  try {
    const isAdmin = await checkAdminRights();
    results.permissions = { isAdmin };
    
    console.log(`   👑 صلاحيات إدارية: ${isAdmin ? '✅ متوفرة' : '❌ غير متوفرة'}`);
    
    if (!isAdmin) {
      console.log('   ⚠️  تحذير: قد تحتاج صلاحيات إدارية لإنشاء مجلدات مشتركة');
    }
  } catch (error) {
    console.log(`   ❌ خطأ في فحص الصلاحيات: ${error.message}`);
    results.permissions = { error: error.message };
  }
  
  // 5. فحص الخدمات المطلوبة
  console.log('\n⚙️ 5. فحص الخدمات المطلوبة:');
  try {
    const services = await checkRequiredServices();
    results.services = services;
    
    services.forEach(service => {
      const status = service.running ? '✅ يعمل' : '❌ متوقف';
      console.log(`   ${service.name}: ${status}`);
    });
  } catch (error) {
    console.log(`   ❌ خطأ في فحص الخدمات: ${error.message}`);
    results.services = { error: error.message };
  }
  
  // 6. فحص Firewall
  console.log('\n🛡️ 6. فحص Firewall:');
  try {
    const { stdout: firewallOutput } = await execAsync('netsh advfirewall show allprofiles state');
    const firewallStatus = parseFirewallStatus(firewallOutput);
    results.firewall = firewallStatus;
    
    console.log(`   🔥 Domain Profile: ${firewallStatus.domain ? '🔴 مفعل' : '🟢 معطل'}`);
    console.log(`   🏠 Private Profile: ${firewallStatus.private ? '🔴 مفعل' : '🟢 معطل'}`);
    console.log(`   🌐 Public Profile: ${firewallStatus.public ? '🔴 مفعل' : '🟢 معطل'}`);
  } catch (error) {
    console.log(`   ❌ خطأ في فحص Firewall: ${error.message}`);
    results.firewall = { error: error.message };
  }
  
  // 7. اختبار إنشاء مجلد مشترك تجريبي
  console.log('\n🧪 7. اختبار إنشاء مجلد مشترك تجريبي:');
  try {
    const testResult = await testSharedFolderCreation();
    console.log(`   📁 إنشاء المجلد: ${testResult.folderCreated ? '✅ نجح' : '❌ فشل'}`);
    console.log(`   🔗 مشاركة المجلد: ${testResult.shareCreated ? '✅ نجح' : '❌ فشل'}`);
    console.log(`   🗑️  حذف المجلد التجريبي: ${testResult.cleaned ? '✅ نجح' : '❌ فشل'}`);
    
    if (testResult.error) {
      console.log(`   ❌ خطأ: ${testResult.error}`);
    }
  } catch (error) {
    console.log(`   ❌ خطأ في الاختبار: ${error.message}`);
  }
  
  // 8. تحليل النتائج وتقديم التوصيات
  console.log('\n📋 8. تحليل النتائج والتوصيات:');
  analyzeResults(results);
  
  console.log('\n' + '='.repeat(60));
  console.log('✅ تم إنهاء الفحص العميق');
}

function parseNetworkInfo(ipconfig) {
  const info = {};
  
  // استخراج IP Address
  const ipMatch = ipconfig.match(/IPv4 Address[.\s]*:\s*([0-9.]+)/);
  if (ipMatch) info.ip = ipMatch[1];
  
  // استخراج Gateway
  const gatewayMatch = ipconfig.match(/Default Gateway[.\s]*:\s*([0-9.]+)/);
  if (gatewayMatch) info.gateway = gatewayMatch[1];
  
  // استخراج DNS
  const dnsMatch = ipconfig.match(/DNS Servers[.\s]*:\s*([0-9.]+)/);
  if (dnsMatch) info.dns = dnsMatch[1];
  
  // استخراج اسم الجهاز
  const hostnameMatch = ipconfig.match(/Host Name[.\s]*:\s*([^\r\n]+)/);
  if (hostnameMatch) info.hostname = hostnameMatch[1].trim();
  
  return info;
}

function parseArpTable(arpOutput) {
  const devices = [];
  const lines = arpOutput.split('\n');
  
  for (const line of lines) {
    const match = line.match(/\s*(\d+\.\d+\.\d+\.\d+)\s+([0-9a-f]{2}-[0-9a-f]{2}-[0-9a-f]{2}-[0-9a-f]{2}-[0-9a-f]{2}-[0-9a-f]{2})\s+(\w+)/i);
    if (match) {
      const [, ip, mac, type] = match;
      // تجاهل العناوين المحلية والبث
      if (!ip.startsWith('127.') && !ip.startsWith('224.') && !ip.startsWith('255.') && !ip.startsWith('239.')) {
        devices.push({ ip, mac: mac.toLowerCase(), type: type.toLowerCase() });
      }
    }
  }
  
  return devices;
}

function parseNetShares(netShareOutput) {
  const shares = [];
  const lines = netShareOutput.split('\n');
  
  for (const line of lines) {
    // تخطي الخطوط الفارغة والعناوين
    if (line.trim() && !line.includes('Share name') && !line.includes('---')) {
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 2) {
        const name = parts[0];
        const path = parts[1];
        if (name && path && !name.includes('$')) { // تجاهل المجلدات الإدارية
          shares.push({ name, path });
        }
      }
    }
  }
  
  return shares;
}

async function checkAdminRights() {
  try {
    await execAsync('net session', { timeout: 3000 });
    return true;
  } catch {
    return false;
  }
}

async function checkRequiredServices() {
  const requiredServices = [
    'Server',
    'Workstation',
    'LanmanServer',
    'LanmanWorkstation'
  ];
  
  const services = [];
  
  for (const serviceName of requiredServices) {
    try {
      const { stdout } = await execAsync(`sc query "${serviceName}"`);
      const running = stdout.includes('RUNNING');
      services.push({ name: serviceName, running });
    } catch {
      services.push({ name: serviceName, running: false });
    }
  }
  
  return services;
}

function parseFirewallStatus(firewallOutput) {
  return {
    domain: firewallOutput.includes('Domain Profile') && firewallOutput.includes('ON'),
    private: firewallOutput.includes('Private Profile') && firewallOutput.includes('ON'),
    public: firewallOutput.includes('Public Profile') && firewallOutput.includes('ON')
  };
}

async function testSharedFolderCreation() {
  const testFolderPath = path.join(process.env.TEMP, 'ZET-IA-Test-Share');
  const result = {
    folderCreated: false,
    shareCreated: false,
    cleaned: false,
    error: null
  };
  
  try {
    // إنشاء مجلد تجريبي
    if (!fs.existsSync(testFolderPath)) {
      fs.mkdirSync(testFolderPath, { recursive: true });
      result.folderCreated = true;
    }
    
    // محاولة مشاركة المجلد
    try {
      await execAsync(`net share ZET-IA-Test="${testFolderPath}" /grant:everyone,full`);
      result.shareCreated = true;
      
      // حذف المشاركة
      await execAsync('net share ZET-IA-Test /delete');
    } catch (shareError) {
      result.error = `فشل في مشاركة المجلد: ${shareError.message}`;
    }
    
    // حذف المجلد التجريبي
    if (fs.existsSync(testFolderPath)) {
      fs.rmSync(testFolderPath, { recursive: true, force: true });
      result.cleaned = true;
    }
    
  } catch (error) {
    result.error = error.message;
  }
  
  return result;
}

function analyzeResults(results) {
  const issues = [];
  const recommendations = [];
  
  // تحليل الاتصال بالشبكة
  if (!results.networkConnectivity || results.networkConnectivity.error) {
    issues.push('مشكلة في الاتصال بالشبكة');
    recommendations.push('تحقق من إعدادات الشبكة والاتصال بالإنترنت');
  } else if (!results.networkConnectivity.ip) {
    issues.push('لم يتم العثور على عنوان IP');
    recommendations.push('تحقق من إعدادات بطاقة الشبكة');
  }
  
  // تحليل ARP Table
  if (!results.arpTable || results.arpTable.error) {
    issues.push('فشل في فحص الأجهزة المتصلة');
    recommendations.push('تحقق من صلاحيات تشغيل أوامر الشبكة');
  } else if (results.arpTable.length === 0) {
    issues.push('لم يتم العثور على أجهزة أخرى في الشبكة');
    recommendations.push('تأكد من وجود أجهزة أخرى متصلة بنفس الشبكة');
  }
  
  // تحليل الصلاحيات
  if (results.permissions && !results.permissions.isAdmin) {
    issues.push('لا توجد صلاحيات إدارية');
    recommendations.push('شغل البرنامج كمدير (Run as Administrator) لإنشاء مجلدات مشتركة');
  }
  
  // تحليل الخدمات
  if (results.services && Array.isArray(results.services)) {
    const stoppedServices = results.services.filter(s => !s.running);
    if (stoppedServices.length > 0) {
      issues.push(`خدمات متوقفة: ${stoppedServices.map(s => s.name).join(', ')}`);
      recommendations.push('تفعيل خدمات مشاركة الملفات في Windows');
    }
  }
  
  // تحليل Firewall
  if (results.firewall && (results.firewall.domain || results.firewall.private || results.firewall.public)) {
    issues.push('Firewall مفعل قد يمنع مشاركة الملفات');
    recommendations.push('إضافة استثناء للبرنامج في Windows Firewall أو تعطيل Firewall مؤقتاً للاختبار');
  }
  
  // عرض النتائج
  if (issues.length === 0) {
    console.log('   ✅ لم يتم العثور على مشاكل واضحة');
    console.log('   💡 قسم ربط الأجهزة يجب أن يعمل بشكل طبيعي');
  } else {
    console.log('   ⚠️  المشاكل المكتشفة:');
    issues.forEach((issue, index) => {
      console.log(`      ${index + 1}. ${issue}`);
    });
    
    console.log('\n   💡 التوصيات:');
    recommendations.forEach((rec, index) => {
      console.log(`      ${index + 1}. ${rec}`);
    });
  }
}

// تشغيل الفحص
deepSyncDiagnostics().catch(console.error);
