import React, { useState, useEffect } from 'react'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  Card, Button, Alert, Space, Typography, Divider, 
  Descriptions, Tag, Progress, Collapse, App
} from 'antd'
import {
  CheckCircleOutlined, WarningOutlined, CloseCircleOutlined,
  ReloadOutlined, BugOutlined, WifiOutlined, DatabaseOutlined
} from '@ant-design/icons'

const { Text, Title } = Typography
const { Panel } = Collapse

interface DiagnosticResult {
  category: string
  name: string
  status: 'success' | 'warning' | 'error'
  message: string
  details?: string
  suggestion?: string
}

interface NetworkInfo {
  hostname: string
  ip: string
  isConnected: boolean
  gateway?: string
  dns?: string[]
}

export const DeviceLinkingDiagnostics: React.FC = () => {
  const { message } = App.useApp()
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([])
  const [networkInfo, setNetworkInfo] = useState<NetworkInfo | null>(null)
  const [running, setRunning] = useState(false)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    runDiagnostics()
  }, [])

  const runDiagnostics = async () => {
    setRunning(true)
    setProgress(0)
    setDiagnostics([])

    try {
      const results: DiagnosticResult[] = []
      
      // 1. فحص الشبكة
      setProgress(20)
      const networkResult = await checkNetworkConnectivity()
      results.push(networkResult)

      // 2. فحص قاعدة البيانات
      setProgress(40)
      const dbResult = await checkDatabaseStatus()
      results.push(dbResult)

      // 3. فحص المجلد المشترك
      setProgress(60)
      const folderResult = await checkSharedFolder()
      results.push(folderResult)

      // 4. فحص الأجهزة المتصلة
      setProgress(80)
      const devicesResult = await checkConnectedDevices()
      results.push(devicesResult)

      // 5. فحص إعدادات المزامنة
      setProgress(100)
      const syncResult = await checkSyncSettings()
      results.push(syncResult)

      setDiagnostics(results)
      
      // تحديث معلومات الشبكة
      await loadNetworkInfo()

    } catch (error: any) {
      Logger.error('DeviceLinkingDiagnostics', 'خطأ في تشغيل التشخيص:', error)
      message.error('خطأ في تشغيل التشخيص')
    } finally {
      setRunning(false)
    }
  }

  const checkNetworkConnectivity = async (): Promise<DiagnosticResult> => {
    try {
      const result = await window.electronAPI.getNetworkInfo()
      
      if (result.success && result.data) {
        return {
          category: 'network',
          name: 'اتصال الشبكة',
          status: 'success',
          message: `متصل بالشبكة - IP: ${result.data.ip}`,
          details: `اسم الجهاز: ${result.data.hostname}`
        }
      } else {
        return {
          category: 'network',
          name: 'اتصال الشبكة',
          status: 'error',
          message: 'لا يوجد اتصال بالشبكة',
          suggestion: 'تحقق من اتصال الإنترنت أو الشبكة المحلية'
        }
      }
    } catch (error) {
      return {
        category: 'network',
        name: 'اتصال الشبكة',
        status: 'error',
        message: 'خطأ في فحص الشبكة',
        suggestion: 'تحقق من إعدادات الشبكة'
      }
    }
  }

  const checkDatabaseStatus = async (): Promise<DiagnosticResult> => {
    try {
      // محاكاة فحص صحة قاعدة البيانات
      const result = { success: true, message: 'قاعدة البيانات تعمل بشكل طبيعي' }
      
      if (result.success) {
        return {
          category: 'database',
          name: 'قاعدة البيانات',
          status: 'success',
          message: 'قاعدة البيانات تعمل بشكل طبيعي',
          details: `نوع قاعدة البيانات: محلية`
        }
      } else {
        return {
          category: 'database',
          name: 'قاعدة البيانات',
          status: 'error',
          message: 'مشكلة في قاعدة البيانات',
          suggestion: 'أعد تشغيل التطبيق أو تحقق من ملف قاعدة البيانات'
        }
      }
    } catch (error) {
      return {
        category: 'database',
        name: 'قاعدة البيانات',
        status: 'error',
        message: 'خطأ في الاتصال بقاعدة البيانات',
        suggestion: 'أعد تشغيل التطبيق'
      }
    }
  }

  const checkSharedFolder = async (): Promise<DiagnosticResult> => {
    try {
      const settings = await window.electronAPI.getSyncSettings()
      
      if (!settings.success || !settings.data.enabled) {
        return {
          category: 'sync',
          name: 'المجلد المشترك',
          status: 'warning',
          message: 'المزامنة معطلة',
          suggestion: 'فعّل المزامنة من إعدادات ربط الأجهزة'
        }
      }

      if (!settings.data.sharedFolder) {
        return {
          category: 'sync',
          name: 'المجلد المشترك',
          status: 'warning',
          message: 'لم يتم تحديد مجلد مشترك',
          suggestion: 'حدد مجلد مشترك في إعدادات المزامنة'
        }
      }

      const testResult = await window.electronAPI.testSyncConnection(settings.data.sharedFolder)
      
      if (testResult.success) {
        return {
          category: 'sync',
          name: 'المجلد المشترك',
          status: 'success',
          message: 'يمكن الوصول للمجلد المشترك',
          details: `المسار: ${settings.data.sharedFolder}`
        }
      } else {
        return {
          category: 'sync',
          name: 'المجلد المشترك',
          status: 'error',
          message: 'لا يمكن الوصول للمجلد المشترك',
          suggestion: 'تحقق من صحة المسار وصلاحيات الوصول'
        }
      }
    } catch (error) {
      return {
        category: 'sync',
        name: 'المجلد المشترك',
        status: 'error',
        message: 'خطأ في فحص المجلد المشترك',
        suggestion: 'تحقق من إعدادات المزامنة'
      }
    }
  }

  const checkConnectedDevices = async (): Promise<DiagnosticResult> => {
    try {
      const result = await window.electronAPI.discoverConnectedDevices()
      
      if (result.success) {
        const reachableDevices = result.devices.filter(d => d.isReachable)
        const devicesWithSharedFolder = reachableDevices.filter(d => (d as any).hasSharedFolder)
        
        if (reachableDevices.length === 0) {
          return {
            category: 'devices',
            name: 'الأجهزة المتصلة',
            status: 'warning',
            message: 'لم يتم العثور على أجهزة في الشبكة',
            suggestion: 'تأكد من أن الأجهزة الأخرى متصلة بنفس الشبكة'
          }
        }

        if (devicesWithSharedFolder.length === 0) {
          return {
            category: 'devices',
            name: 'الأجهزة المتصلة',
            status: 'warning',
            message: `تم العثور على ${reachableDevices.length} جهاز لكن لا يحتوي أي منها على قاعدة بيانات مشتركة`,
            suggestion: 'تأكد من إعداد المجلد المشترك على الجهاز الرئيسي'
          }
        }

        return {
          category: 'devices',
          name: 'الأجهزة المتصلة',
          status: 'success',
          message: `تم العثور على ${reachableDevices.length} جهاز (${devicesWithSharedFolder.length} يحتوي على قاعدة بيانات مشتركة)`,
          details: `الأجهزة الرئيسية: ${devicesWithSharedFolder.map(d => d.hostname || d.ip).join(', ')}`
        }
      } else {
        return {
          category: 'devices',
          name: 'الأجهزة المتصلة',
          status: 'error',
          message: 'فشل في البحث عن الأجهزة',
          suggestion: 'تحقق من اتصال الشبكة وأعد المحاولة'
        }
      }
    } catch (error) {
      return {
        category: 'devices',
        name: 'الأجهزة المتصلة',
        status: 'error',
        message: 'خطأ في البحث عن الأجهزة',
        suggestion: 'تحقق من إعدادات الشبكة'
      }
    }
  }

  const checkSyncSettings = async (): Promise<DiagnosticResult> => {
    try {
      const settings = await window.electronAPI.getSyncSettings()
      
      if (!settings.success) {
        return {
          category: 'sync',
          name: 'إعدادات المزامنة',
          status: 'error',
          message: 'لا يمكن قراءة إعدادات المزامنة',
          suggestion: 'أعد تشغيل التطبيق'
        }
      }

      const data = settings.data
      
      if (!data.enabled) {
        return {
          category: 'sync',
          name: 'إعدادات المزامنة',
          status: 'warning',
          message: 'المزامنة معطلة',
          suggestion: 'فعّل المزامنة من الإعدادات'
        }
      }

      if (!data.deviceRole) {
        return {
          category: 'sync',
          name: 'إعدادات المزامنة',
          status: 'warning',
          message: 'لم يتم تحديد دور الجهاز',
          suggestion: 'حدد ما إذا كان الجهاز رئيسي أم فرعي'
        }
      }

      return {
        category: 'sync',
        name: 'إعدادات المزامنة',
        status: 'success',
        message: `المزامنة مفعلة - دور الجهاز: ${data.deviceRole === 'main' ? 'رئيسي' : 'فرعي'}`,
        details: `فترة المزامنة: ${data.syncInterval || 5} دقائق`
      }
    } catch (error) {
      return {
        category: 'sync',
        name: 'إعدادات المزامنة',
        status: 'error',
        message: 'خطأ في فحص إعدادات المزامنة',
        suggestion: 'تحقق من إعدادات التطبيق'
      }
    }
  }

  const loadNetworkInfo = async () => {
    try {
      const result = await window.electronAPI.getNetworkInfo()
      if (result.success) {
        setNetworkInfo({ ...result.data, isConnected: true })
      }
    } catch (error) {
      Logger.error('DeviceLinkingDiagnostics', 'خطأ في تحميل معلومات الشبكة:', error)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'warning':
        return <WarningOutlined style={{ color: '#faad14' }} />
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
      default:
        return null
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'success'
      case 'warning':
        return 'warning'
      case 'error':
        return 'error'
      default:
        return 'default'
    }
  }

  const groupedDiagnostics = diagnostics.reduce((acc, diagnostic) => {
    if (!acc[diagnostic.category]) {
      acc[diagnostic.category] = []
    }
    acc[diagnostic.category].push(diagnostic)
    return acc
  }, {} as Record<string, DiagnosticResult[]>)

  const getCategoryTitle = (category: string) => {
    switch (category) {
      case 'network':
        return '🌐 الشبكة'
      case 'database':
        return '💾 قاعدة البيانات'
      case 'sync':
        return '🔄 المزامنة'
      case 'devices':
        return '🖥️ الأجهزة'
      default:
        return category
    }
  }

  return (
    <Card 
      title={
        <Space>
          <BugOutlined />
          <span>🔍 تشخيص ربط الأجهزة</span>
        </Space>
      }
      extra={
        <Button 
          icon={<ReloadOutlined spin={running} />}
          onClick={runDiagnostics}
          loading={running}
          type="primary"
          ghost
        >
          إعادة التشخيص
        </Button>
      }
    >
      {running && (
        <div style={{ marginBottom: 16 }}>
          <Text>جاري تشغيل التشخيص...</Text>
          <Progress percent={progress} size="small" style={{ marginTop: 8 }} />
        </div>
      )}

      {diagnostics.length > 0 && (
        <Collapse defaultActiveKey={['network', 'database', 'sync', 'devices']}>
          {Object.entries(groupedDiagnostics).map(([category, results]) => (
            <Panel 
              key={category}
              header={
                <Space>
                  <span>{getCategoryTitle(category)}</span>
                  <Tag color={getStatusColor(results[0]?.status)}>
                    {results.filter(r => r.status === 'success').length}/{results.length} ✓
                  </Tag>
                </Space>
              }
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                {results.map((result, index) => (
                  <Alert
                    key={index}
                    message={
                      <Space>
                        {getStatusIcon(result.status)}
                        <Text strong>{result.name}</Text>
                      </Space>
                    }
                    description={
                      <div>
                        <Text>{result.message}</Text>
                        {result.details && (
                          <div style={{ marginTop: 4 }}>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {result.details}
                            </Text>
                          </div>
                        )}
                        {result.suggestion && (
                          <div style={{ marginTop: 8 }}>
                            <Text type="warning" style={{ fontSize: '12px' }}>
                              💡 {result.suggestion}
                            </Text>
                          </div>
                        )}
                      </div>
                    }
                    type={result.status === 'success' ? 'success' : result.status === 'warning' ? 'warning' : 'error'}
                    showIcon={false}
                    style={{ marginBottom: 8 }}
                  />
                ))}
              </Space>
            </Panel>
          ))}
        </Collapse>
      )}

      {networkInfo && (
        <>
          <Divider />
          <Title level={5}>📊 معلومات تفصيلية</Title>
          <Descriptions size="small" column={2} bordered>
            <Descriptions.Item label="اسم الجهاز">{networkInfo.hostname}</Descriptions.Item>
            <Descriptions.Item label="عنوان IP">{networkInfo.ip}</Descriptions.Item>
            <Descriptions.Item label="حالة الاتصال">
              <Tag color={networkInfo.isConnected ? 'green' : 'red'}>
                {networkInfo.isConnected ? 'متصل' : 'غير متصل'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="البوابة الافتراضية">{networkInfo.gateway || 'غير محدد'}</Descriptions.Item>
          </Descriptions>
        </>
      )}
    </Card>
  )
}

export default DeviceLinkingDiagnostics
