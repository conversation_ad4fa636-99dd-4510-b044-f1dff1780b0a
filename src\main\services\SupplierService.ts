import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { Logger } from '../utils/logger'

export interface Supplier {
  id: number
  code: string
  name: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  tax_number?: string
  balance: number
  credit_limit: number
  payment_terms: number
  is_active: boolean
  created_at: string
  updated_at?: string
}

export interface CreateSupplierData {
  code?: string
  name: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  tax_number?: string
  credit_limit?: number
  payment_terms?: number
  is_active?: boolean
}

export interface UpdateSupplierData extends CreateSupplierData {
  id: number
}

export class SupplierService {
  private static instance: SupplierService
  private db: any

  private constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
  }

  public static getInstance(): SupplierService {
    if (!SupplierService.instance) {
      SupplierService.instance = new SupplierService()
    }
    return SupplierService.instance
  }

  // إنشاء جداول الموردين
  public async createSupplierTables(): Promise<void> {
    const database = this.db

    // جدول الموردين
    database.exec(`
      CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        contact_person TEXT,
        phone TEXT,
        email TEXT,
        address TEXT,
        tax_number TEXT,
        balance DECIMAL(10,2) DEFAULT 0,
        credit_limit DECIMAL(10,2) DEFAULT 0,
        payment_terms INTEGER DEFAULT 30,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // جدول معاملات الموردين
    database.exec(`
      CREATE TABLE IF NOT EXISTS supplier_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        supplier_id INTEGER NOT NULL,
        transaction_type TEXT NOT NULL CHECK (transaction_type IN ('invoice', 'payment', 'credit', 'debit')),
        reference_type TEXT,
        reference_id INTEGER,
        amount DECIMAL(10,2) NOT NULL,
        description TEXT,
        transaction_date DATE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
      )
    `)

    // إنشاء الفهارس
    database.exec('CREATE INDEX IF NOT EXISTS idx_suppliers_code ON suppliers(code)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_suppliers_active ON suppliers(is_active)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_supplier_transactions_supplier ON supplier_transactions(supplier_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_supplier_transactions_date ON supplier_transactions(transaction_date)')

    // تشغيل Migration التلقائي
    await this.runSupplierMigrations()
  }

  // تشغيل Migration التلقائي للموردين
  private async runSupplierMigrations(): Promise<void> {
    try {
      Logger.info('SupplierService', '🔄 بدء Migration التلقائي للموردين...')

      // تحديث مخطط جدول الموردين
      await this.updateSupplierTableSchema()

      // إصلاح البيانات الموجودة
      await this.fixExistingSupplierData()

      Logger.info('SupplierService', '✅ تم إكمال Migration الموردين بنجاح')
    } catch (error) {
      Logger.error('SupplierService', '❌ خطأ في Migration الموردين:', error)
    }
  }

  // تحديث مخطط جدول الموردين
  private async updateSupplierTableSchema(): Promise<void> {
    try {
      Logger.info('SupplierService', '🔧 فحص وتحديث مخطط جدول الموردين...')

      // الحصول على معلومات الأعمدة الحالية
      const tableInfo = this.db.prepare("PRAGMA table_info(suppliers)").all()
      const existingColumns = tableInfo.map((col: any) => col.name)

      // قائمة الأعمدة الجديدة المطلوبة
      const newColumns = [
        { name: 'commercial_register', type: 'TEXT' },
        { name: 'website', type: 'TEXT' },
        { name: 'notes', type: 'TEXT' },
        { name: 'supplier_type', type: 'TEXT DEFAULT "company"' },
        { name: 'discount_percentage', type: 'DECIMAL(5,2) DEFAULT 0' },
        { name: 'preferred_payment_method', type: 'TEXT DEFAULT "bank_transfer"' },
        { name: 'credit_days', type: 'INTEGER DEFAULT 30' },
        { name: 'last_transaction_date', type: 'DATETIME' },
        { name: 'total_purchases', type: 'DECIMAL(15,2) DEFAULT 0' },
        { name: 'rating', type: 'INTEGER DEFAULT 5' },
        { name: 'created_by', type: 'INTEGER' }
      ]

      // إضافة الأعمدة المفقودة
      for (const column of newColumns) {
        if (!existingColumns.includes(column.name)) {
          try {
            this.db.exec(`ALTER TABLE suppliers ADD COLUMN ${column.name} ${column.type}`)
            Logger.info('SupplierService', '✅ تم إضافة العمود: ${column.name}')
          } catch (error) {
            Logger.info('SupplierService', '⚠️ فشل في إضافة العمود ${column.name}:', error)
          }
        }
      }

    } catch (error) {
      Logger.error('SupplierService', 'خطأ في تحديث مخطط جدول الموردين:', error)
    }
  }

  // إصلاح البيانات الموجودة للموردين
  private async fixExistingSupplierData(): Promise<void> {
    try {
      Logger.info('SupplierService', '🔧 بدء إصلاح البيانات الموجودة للموردين...')

      // 1. تحديث is_active للموردين الذين لا يحتوون على قيمة
      const updateActiveResult = this.db.prepare(`
        UPDATE suppliers
        SET is_active = 1
        WHERE is_active IS NULL OR is_active = 0
      `).run()

      if (updateActiveResult.changes > 0) {
        Logger.info('SupplierService', '✅ تم تحديث حالة النشاط لـ ${updateActiveResult.changes} مورد')
      }

      // 2. تحديث supplier_type للموردين الذين لا يحتوون على قيمة
      const updateTypeResult = this.db.prepare(`
        UPDATE suppliers
        SET supplier_type = 'company'
        WHERE supplier_type IS NULL OR supplier_type = ''
      `).run()

      if (updateTypeResult.changes > 0) {
        Logger.info('SupplierService', '✅ تم تحديث نوع المورد لـ ${updateTypeResult.changes} مورد')
      }

      // 3. تحديث preferred_payment_method للموردين الذين لا يحتوون على قيمة
      const updatePaymentResult = this.db.prepare(`
        UPDATE suppliers
        SET preferred_payment_method = 'bank_transfer'
        WHERE preferred_payment_method IS NULL OR preferred_payment_method = ''
      `).run()

      if (updatePaymentResult.changes > 0) {
        Logger.info('SupplierService', '✅ تم تحديث طريقة الدفع المفضلة لـ ${updatePaymentResult.changes} مورد')
      }

      // 4. تحديث credit_days للموردين الذين لا يحتوون على قيمة
      const updateCreditDaysResult = this.db.prepare(`
        UPDATE suppliers
        SET credit_days = payment_terms
        WHERE credit_days IS NULL AND payment_terms IS NOT NULL
      `).run()

      if (updateCreditDaysResult.changes > 0) {
        Logger.info('SupplierService', '✅ تم تحديث أيام الائتمان لـ ${updateCreditDaysResult.changes} مورد')
      }

      // 5. تحديث rating للموردين الذين لا يحتوون على قيمة
      const updateRatingResult = this.db.prepare(`
        UPDATE suppliers
        SET rating = 5
        WHERE rating IS NULL
      `).run()

      if (updateRatingResult.changes > 0) {
        Logger.info('SupplierService', '✅ تم تحديث التقييم لـ ${updateRatingResult.changes} مورد')
      }

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()
      Logger.info('SupplierService', '✅ تم إصلاح البيانات الموجودة بنجاح')

    } catch (error) {
      Logger.error('SupplierService', 'خطأ في إصلاح البيانات الموجودة:', error)
    }
  }

  // الحصول على جميع الموردين
  public async getSuppliers(): Promise<Supplier[]> {
    try {
      const suppliers = this.db.prepare(`
        SELECT * FROM suppliers 
        WHERE is_active = 1 
        ORDER BY name
      `).all() as Supplier[]

      return suppliers
    } catch (error) {
      Logger.error('SupplierService', 'خطأ في جلب الموردين:', error)
      return []
    }
  }

  // التحقق من صحة بيانات المورد
  private validateSupplierData(supplierData: CreateSupplierData): { isValid: boolean; message?: string } {
    // التحقق من الاسم
    if (!supplierData.name || supplierData.name.trim().length === 0) {
      return { isValid: false, message: 'اسم المورد مطلوب' }
    }

    if (supplierData.name.length > 100) {
      return { isValid: false, message: 'اسم المورد يجب أن يكون أقل من 100 حرف' }
    }

    // التحقق من الكود إذا تم توفيره
    if (supplierData.code && supplierData.code.length > 20) {
      return { isValid: false, message: 'كود المورد يجب أن يكون أقل من 20 حرف' }
    }

    // التحقق من البريد الإلكتروني
    if (supplierData.email && supplierData.email.trim().length > 0) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(supplierData.email)) {
        return { isValid: false, message: 'البريد الإلكتروني غير صحيح' }
      }
    }

    // التحقق من رقم الهاتف
    if (supplierData.phone && supplierData.phone.trim().length > 0) {
      const phoneRegex = /^[0-9+\-\s()]+$/
      if (!phoneRegex.test(supplierData.phone)) {
        return { isValid: false, message: 'رقم الهاتف غير صحيح' }
      }
    }

    // التحقق من حد الائتمان
    if (supplierData.credit_limit && supplierData.credit_limit < 0) {
      return { isValid: false, message: 'حد الائتمان لا يمكن أن يكون سالباً' }
    }

    // التحقق من شروط الدفع
    if (supplierData.payment_terms && supplierData.payment_terms < 0) {
      return { isValid: false, message: 'شروط الدفع لا يمكن أن تكون سالبة' }
    }

    return { isValid: true }
  }

  // إنشاء مورد جديد
  public async createSupplier(supplierData: CreateSupplierData): Promise<ApiResponse> {
    try {
      // التحقق من صحة البيانات
      const validation = this.validateSupplierData(supplierData)
      if (!validation.isValid) {
        return { success: false, message: validation.message || 'بيانات غير صحيحة' }
      }

      // توليد كود المورد إذا لم يتم توفيره
      if (!supplierData.code) {
        supplierData.code = await this.generateSupplierCode()
      }

      // التحقق من عدم تكرار الكود
      const existingSupplier = this.db.prepare('SELECT id FROM suppliers WHERE code = ?').get(supplierData.code.trim())
      if (existingSupplier) {
        return { success: false, message: 'كود المورد موجود مسبقاً' }
      }

      // التحقق من عدم تكرار البريد الإلكتروني
      if (supplierData.email && supplierData.email.trim().length > 0) {
        const existingEmail = this.db.prepare('SELECT id FROM suppliers WHERE email = ?').get(supplierData.email.trim())
        if (existingEmail) {
          return { success: false, message: 'البريد الإلكتروني موجود مسبقاً' }
        }
      }

      const result = this.db.prepare(`
        INSERT INTO suppliers (
          code, name, contact_person, phone, email, address, tax_number,
          balance, credit_limit, payment_terms, is_active,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `).run([
        supplierData.code,
        supplierData.name,
        supplierData.contact_person || null,
        supplierData.phone || null,
        supplierData.email || null,
        supplierData.address || null,
        supplierData.tax_number || null,
        0, // balance - الرصيد الافتراضي
        supplierData.credit_limit || 0,
        supplierData.payment_terms || 30,
        supplierData.is_active ? 1 : 0
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إنشاء المورد بنجاح' }
      } else {
        return { success: false, message: 'فشل في إنشاء المورد' }
      }
    } catch (error: any) {
      Logger.error('SupplierService', 'خطأ في إنشاء المورد:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود المورد موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في إنشاء المورد' }
    }
  }

  // تحديث مورد
  public async updateSupplier(supplierData: UpdateSupplierData): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        UPDATE suppliers SET
          code = ?, name = ?, contact_person = ?, phone = ?, email = ?, address = ?, tax_number = ?,
          credit_limit = ?, payment_terms = ?, is_active = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run([
        supplierData.code,
        supplierData.name,
        supplierData.contact_person || null,
        supplierData.phone || null,
        supplierData.email || null,
        supplierData.address || null,
        supplierData.tax_number || null,
        supplierData.credit_limit || 0,
        supplierData.payment_terms || 30,
        supplierData.is_active ? 1 : 0,
        supplierData.id
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث المورد بنجاح' }
      } else {
        return { success: false, message: 'لم يتم العثور على المورد' }
      }
    } catch (error: any) {
      Logger.error('SupplierService', 'خطأ في تحديث المورد:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود المورد موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في تحديث المورد' }
    }
  }

  // حذف مورد
  public async deleteSupplier(supplierId: number): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        UPDATE suppliers SET is_active = 0, updated_at = datetime('now')
        WHERE id = ?
      `).run([supplierId])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم حذف المورد بنجاح' }
      } else {
        return { success: false, message: 'لم يتم العثور على المورد' }
      }
    } catch (error) {
      Logger.error('SupplierService', 'خطأ في حذف المورد:', error)
      return { success: false, message: 'حدث خطأ في حذف المورد' }
    }
  }

  // توليد كود مورد جديد متسلسل
  public async generateSupplierCode(): Promise<string> {
    try {
      // الحصول على جميع الأكواد الموجودة مرتبة
      const existingCodes = this.db.prepare(`
        SELECT code FROM suppliers
        WHERE code LIKE 'SUPP%' AND code IS NOT NULL AND code != ''
        ORDER BY CAST(SUBSTR(code, 5) AS INTEGER) ASC
      `).all() as any[]

      // استخراج الأرقام من الأكواد
      const existingNumbers = existingCodes
        .map(row => {
          const codeNumber = row.code.substring(4)
          const number = parseInt(codeNumber)
          return isNaN(number) ? 0 : number
        })
        .filter(num => num > 0)
        .sort((a, b) => a - b)

      // البحث عن أول رقم متاح في التسلسل
      let nextNumber = 1
      for (const num of existingNumbers) {
        if (num === nextNumber) {
          nextNumber++
        } else if (num > nextNumber) {
          // وجدنا فجوة في التسلسل
          break
        }
      }

      // تكوين الكود الجديد
      const newCode = `SUPP${nextNumber.toString().padStart(3, '0')}`

      // التحقق النهائي من عدم وجود الكود (احتياط إضافي)
      const existingSupplier = this.db.prepare('SELECT id FROM suppliers WHERE code = ?').get(newCode)
      if (existingSupplier) {
        // في حالة وجود تضارب غير متوقع، ابحث عن أول رقم متاح بعد آخر رقم
        const maxNumber = Math.max(...existingNumbers, 0)
        return `SUPP${(maxNumber + 1).toString().padStart(3, '0')}`
      }

      return newCode
    } catch (error) {
      Logger.error('SupplierService', 'خطأ في توليد كود المورد:', error)
      return `SUPP${Date.now().toString().slice(-3)}`
    }
  }

  // الحصول على رصيد المورد
  public async getSupplierBalance(supplierId: number): Promise<number> {
    try {
      const supplier = this.db.prepare(`
        SELECT balance FROM suppliers WHERE id = ?
      `).get(supplierId) as any

      return supplier ? supplier.balance : 0
    } catch (error) {
      Logger.error('SupplierService', 'خطأ في جلب رصيد المورد:', error)
      return 0
    }
  }

  // تحديث رصيد المورد
  public async updateSupplierBalance(supplierId: number, amount: number, transactionType: string, description?: string): Promise<ApiResponse> {
    try {
      // تحديث رصيد المورد
      this.db.prepare(`
        UPDATE suppliers
        SET balance = balance + ?, updated_at = datetime('now')
        WHERE id = ?
      `).run([amount, supplierId])

      // إضافة معاملة
      this.db.prepare(`
        INSERT INTO supplier_transactions (
          supplier_id, transaction_type, amount, description, transaction_date
        ) VALUES (?, ?, ?, ?, date('now'))
      `).run([supplierId, transactionType, amount, description || null])

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()
      return { success: true, message: 'تم تحديث رصيد المورد بنجاح' }
    } catch (error) {
      Logger.error('SupplierService', 'خطأ في تحديث رصيد المورد:', error)
      return { success: false, message: 'حدث خطأ في تحديث رصيد المورد' }
    }
  }

  // الحصول على معاملات المورد
  public async getSupplierTransactions(supplierId: number): Promise<any[]> {
    try {
      const transactions = this.db.prepare(`
        SELECT * FROM supplier_transactions
        WHERE supplier_id = ?
        ORDER BY transaction_date DESC, created_at DESC
      `).all(supplierId)

      return transactions
    } catch (error) {
      Logger.error('SupplierService', 'خطأ في جلب معاملات المورد:', error)
      return []
    }
  }

  // تم إزالة جميع دوال البيانات التجريبية - يجب استخدام البيانات الحقيقية فقط
}
