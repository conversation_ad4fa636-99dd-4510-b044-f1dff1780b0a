import React, { useState, useEffect } from 'react'
import { Alert, Button, Space } from 'antd'
import { WarningOutlined, ClockCircleOutlined } from '@ant-design/icons'
import { useActivation } from './ActivationProvider'

interface LicenseWarningProps {
  style?: React.CSSProperties
}

const LicenseWarning: React.FC<LicenseWarningProps> = ({ style }) => {
  const { licenseInfo, showActivationModal } = useActivation()
  const [showWarning, setShowWarning] = useState(false)

  useEffect(() => {
    // إظهار التحذير إذا كان الترخيص قريب من الانتهاء (7 أيام أو أقل)
    if (licenseInfo?.daysRemaining !== undefined && 
        licenseInfo?.daysRemaining !== null && 
        licenseInfo?.daysRemaining <= 7 && 
        licenseInfo?.daysRemaining > 0) {
      setShowWarning(true)
    } else {
      setShowWarning(false)
    }
  }, [licenseInfo])

  if (!showWarning || !licenseInfo) {
    return null
  }

  const handleRenewClick = () => {
    showActivationModal()
  }

  const handleDismiss = () => {
    setShowWarning(false)
  }

  return (
    <Alert
      message={
        <Space>
          <ClockCircleOutlined />
          <span>تحذير: الترخيص قريب من الانتهاء</span>
        </Space>
      }
      description={
        <div style={{ textAlign: 'right' }}>
          <p>سينتهي ترخيص البرنامج خلال {licenseInfo.daysRemaining} أيام.</p>
          <p>يرجى تجديد الترخيص لتجنب انقطاع الخدمة.</p>
          <Space style={{ marginTop: 10 }}>
            <Button type="primary" size="small" onClick={handleRenewClick}>
              تجديد الترخيص
            </Button>
            <Button size="small" onClick={handleDismiss}>
              إخفاء التحذير
            </Button>
          </Space>
        </div>
      }
      type="warning"
      showIcon
      closable
      onClose={handleDismiss}
      style={{
        margin: '16px',
        borderRadius: '8px',
        ...style
      }}
    />
  )
}

export default LicenseWarning
