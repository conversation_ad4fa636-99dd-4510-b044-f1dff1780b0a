#!/usr/bin/env pwsh

Write-Host "🚀 Starting ZET.IA Accounting Application..." -ForegroundColor Green
Write-Host ""

# Check if the built application exists
$releasePath = "release-new\win-unpacked\ZET.IA.exe"
$devPath = "dist\main\main\main.js"

if (Test-Path $releasePath) {
    Write-Host "✅ Running from release build..." -ForegroundColor Yellow
    Start-Process $releasePath
    Write-Host "🎉 Application started successfully!" -ForegroundColor Green
} elseif (Test-Path $devPath) {
    Write-Host "⚠️  Running from development build..." -ForegroundColor Yellow
    npm run electron
} else {
    Write-Host "❌ Error: Application not found!" -ForegroundColor Red
    Write-Host "Please run the following commands:" -ForegroundColor Yellow
    Write-Host "  1. npm run build" -ForegroundColor Cyan
    Write-Host "  2. npm run dist" -ForegroundColor Cyan
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "You can close this window." -ForegroundColor Gray
Start-Sleep -Seconds 2
