import React, { useState } from 'react'
import {
  Modal, Upload, Button, message, Progress, Table, Alert, Space, Typography,
  Divider, Card, Row, Col, Statistic, Tag
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  UploadOutlined, DownloadOutlined, FileExcelOutlined,
  CheckCircleOutlined, ExclamationCircleOutlined, InfoCircleOutlined
} from '@ant-design/icons'
import * as XLSX from 'xlsx'
import { ApiResponse } from '../../types/global'
import { exportToExcel } from '../../utils/excelExportUtils'

const { Title, Text } = Typography
const { Dragger } = Upload

interface CheckImportExportProps {
  visible: boolean
  onClose: () => void
  onImportComplete?: () => void
}

interface ImportResult {
  successCount: number
  errorCount: number
  errors: string[]
}

const CheckImportExport: React.FC<CheckImportExportProps> = ({
  visible,
  onClose,
  onImportComplete
}) => {
  const [importing, setImporting] = useState(false)
  const [exporting, setExporting] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [previewData, setPreviewData] = useState<any[]>([])
  const [showPreview, setShowPreview] = useState(false)

  // تصدير الشيكات
  const handleExport = async (format: 'excel' | 'csv') => {
    try {
      setExporting(true)
      
      if (!window.electronAPI) {
        message.error('واجهة Electron غير متوفرة')
        return
      }

      const response: ApiResponse = await window.electronAPI.exportChecks(format)

      if (response.success && response.data) {
        if (response.data.length === 0) {
          message.warning('لا توجد شيكات للتصدير')
          return
        }

        // تصدير باستخدام دالة محسنة
        const result = await exportToExcel(response.data, {
          fileName: 'الشيكات',
          sheetName: 'الشيكات',
          includeTimestamp: true,
          customFormatting: true
        })

        if (!result.success) {
          message.error(result.message || 'فشل في تصدير الشيكات')
        }
      } else {
        message.error(response.message || 'فشل في تصدير الشيكات')
      }
    } catch (error) {
      Logger.error('CheckImportExport', 'خطأ في تصدير الشيكات:', error)
      message.error('حدث خطأ أثناء تصدير الشيكات')
    } finally {
      setExporting(false)
    }
  }

  // معاينة ملف الاستيراد
  const handleFilePreview = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)
        
        setPreviewData(jsonData.slice(0, 10)) // أول 10 صفوف للمعاينة
        setShowPreview(true)
        message.success(`تم تحميل ${jsonData.length} صف من الملف`)
      } catch (error) {
        message.error('فشل في قراءة الملف')
      }
    }
    reader.readAsArrayBuffer(file)
    return false // منع الرفع التلقائي
  }

  // استيراد الشيكات
  const handleImport = async (file: File) => {
    try {
      setImporting(true)
      setImportResult(null)

      if (!window.electronAPI) {
        message.error('واجهة Electron غير متوفرة')
        return
      }

      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer)
          const workbook = XLSX.read(data, { type: 'array' })
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]
          const jsonData = XLSX.utils.sheet_to_json(worksheet)

          // تحويل البيانات إلى التنسيق المطلوب
          const checksData = jsonData.map((row: any) => ({
            check_number: row['رقم الشيك'] || row['check_number'] || '',
            payee_name: row['اسم المستفيد'] || row['payee_name'] || '',
            amount: parseFloat(row['المبلغ'] || row['amount'] || '0'),
            check_date: row['تاريخ الشيك'] || row['check_date'] || new Date().toISOString(),
            due_date: row['تاريخ الاستحقاق'] || row['due_date'] || '',
            status: row['الحالة'] || row['status'] || 'pending',
            notes: row['ملاحّات'] || row['notes'] || ''
          }))

          const response: ApiResponse = await window.electronAPI.importChecks(checksData)
          
          if (response.success) {
            setImportResult(response.data)
            message.success(response.message)
            if (onImportComplete) {
              onImportComplete()
            }
          } else {
            message.error(response.message || 'فشل في استيراد الشيكات')
          }
        } catch (error) {
          Logger.error('CheckImportExport', 'خطأ في معالجة الملف:', error)
          message.error('حدث خطأ أثناء معالجة الملف')
        }
      }
      reader.readAsArrayBuffer(file)
    } catch (error) {
      Logger.error('CheckImportExport', 'خطأ في استيراد الشيكات:', error)
      message.error('حدث خطأ أثناء استيراد الشيكات')
    } finally {
      setImporting(false)
    }
  }

  // أعمدة جدول المعاينة
  const previewColumns = [
    { title: 'رقم الشيك', dataIndex: 'رقم الشيك', key: 'check_number' },
    { title: 'اسم المستفيد', dataIndex: 'اسم المستفيد', key: 'payee_name' },
    { title: 'المبلغ', dataIndex: 'المبلغ', key: 'amount' },
    { title: 'تاريخ الشيك', dataIndex: 'تاريخ الشيك', key: 'check_date' },
    { title: 'الحالة', dataIndex: 'الحالة', key: 'status' }
  ]

  return (
    <Modal
      title="استيراد وتصدير الشيكات"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnHidden
    >
      <div style={{ padding: '16px 0' }}>
        {/* قسم التصدير */}
        <Card title="تصدير الشيكات" style={{ marginBottom: 24 }}>
          <Text type="secondary">
            تصدير جميع الشيكات إلى ملف Excel أو CSV
          </Text>
          <div style={{ marginTop: 16 }}>
            <Space>
              <Button
                type="primary"
                icon={<FileExcelOutlined />}
                loading={exporting}
                onClick={() => handleExport('excel')}
              >
                تصدير Excel
              </Button>
              <Button
                icon={<DownloadOutlined />}
                loading={exporting}
                onClick={() => handleExport('csv')}
              >
                تصدير CSV
              </Button>
            </Space>
          </div>
        </Card>

        {/* قسم الاستيراد */}
        <Card title="استيراد الشيكات">
          <Alert
            message="تعليمات الاستيراد"
            description={
              <div>
                <p>• يجب أن يحتوي الملف على الأعمدة التالية: رقم الشيك، اسم المستفيد، المبلغ</p>
                <p>• الأعمدة الاختيارية: تاريخ الشيك، تاريخ الاستحقاق، الحالة، ملاحّات</p>
                <p>• يجب أن تكون أرقام الشيكات فريدة</p>
                <p>• الحالات المقبولة: pending, cleared, bounced, cancelled</p>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Dragger
            accept=".xlsx,.xls,.csv"
            beforeUpload={handleFilePreview}
            showUploadList={false}
            disabled={importing}
          >
            <p className="ant-upload-drag-icon">
              <UploadOutlined />
            </p>
            <p className="ant-upload-text">اضغط أو اسحب الملف هنا للمعاينة</p>
            <p className="ant-upload-hint">
              يدعم ملفات Excel (.xlsx, .xls) و CSV
            </p>
          </Dragger>

          {showPreview && previewData.length > 0 && (
            <div style={{ marginTop: 16 }}>
              <Title level={5}>معاينة البيانات (أول 10 صفوف)</Title>
              <Table
                dataSource={previewData}
                columns={previewColumns}
                pagination={false}
                size="small"
                scroll={{ x: true }}
                style={{ marginBottom: 16 }}
              />
              <Button
                type="primary"
                loading={importing}
                onClick={() => {
                  // إعادة قراءة الملف للاستيراد
                  const fileInput = document.createElement('input')
                  fileInput.type = 'file'
                  fileInput.accept = '.xlsx,.xls,.csv'
                  fileInput.onchange = (e) => {
                    const file = (e.target as HTMLInputElement).files?.[0]
                    if (file) {
                      handleImport(file)
                    }
                  }
                  fileInput.click()
                }}
              >
                تأكيد الاستيراد
              </Button>
            </div>
          )}

          {importing && (
            <div style={{ marginTop: 16 }}>
              <Progress percent={50} status="active" />
              <Text>جاري استيراد الشيكات...</Text>
            </div>
          )}

          {importResult && (
            <div style={{ marginTop: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="تم بنجاح"
                    value={importResult.successCount}
                    prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="فشل"
                    value={importResult.errorCount}
                    prefix={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
                    valueStyle={{ color: '#ff4d4f' }}
                  />
                </Col>
              </Row>

              {importResult.errors.length > 0 && (
                <div style={{ marginTop: 16 }}>
                  <Alert
                    message="أخطاء الاستيراد"
                    description={
                      <ul>
                        {importResult.errors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    }
                    type="warning"
                    showIcon
                  />
                </div>
              )}
            </div>
          )}
        </Card>
      </div>
    </Modal>
  )
}

export default CheckImportExport
