import { resourceManager } from './resourceManager'
import { SafeLogger as Logger } from './logger'

interface NotificationCheck {
  id: string
  name: string
  check: () => Promise<boolean>
  message: string
  type: 'info' | 'warning' | 'error' | 'success'
}

class SmartNotifications {
  private intervals: Set<NodeJS.Timeout> = new Set()
  private isRunning = false
  private checkInterval = 30 * 60 * 1000 // 30 دقيقة
  private notificationApi: any = null
  private eventListeners: Map<string, EventListener> = new Map()
  private cleanupCallbacks: Set<() => void> = new Set()
  private resourceId: string | null = null

  private checks: NotificationCheck[] = [
    {
      id: 'memory-usage',
      name: 'استخدام الذاكرة',
      check: this.checkMemoryUsage.bind(this),
      message: 'استخدام الذاكرة مرتفع - يُنصح بإعادة تشغيل التطبيق',
      type: 'warning'
    },
    {
      id: 'database-connection',
      name: 'اتصال قاعدة البيانات',
      check: this.checkDatabaseConnection.bind(this),
      message: 'مشكلة في الاتصال بقاعدة البيانات',
      type: 'error'
    },
    {
      id: 'session-expiry',
      name: 'انتهاء الجلسة',
      check: this.checkSessionExpiry.bind(this),
      message: 'جلستك ستنتهي قريباً',
      type: 'warning'
    },
    {
      id: 'backup-reminder',
      name: 'تذكير النسخ الاحتياطي',
      check: this.checkBackupReminder.bind(this),
      message: 'حان وقت إنشاء نسخة احتياطية',
      type: 'info'
    }
  ]

  constructor() {
    // تسجيل نّام الإشعارات في مدير الموارد
    this.resourceId = resourceManager.register({
      name: 'نّام الإشعارات الذكية',
      type: 'notification',
      priority: 20,
      cleanup: () => this.stop()
    })
  }

  // تعيين notification API من App context
  setNotificationApi(api: any) {
    this.notificationApi = api
  }

  // بدء المراقبة
  start() {
    if (this.isRunning) {
      Logger.warn('SmartNotifications', 'SmartNotifications قيد التشغيل بالفعل')
      return
    }

    this.isRunning = true
    Logger.info('SmartNotifications', '🔔 بدء مراقبة الإشعارات الذكية')

    // تشغيل فحص فوري
    this.runAllChecks()

    // جدولة الفحوصات الدورية
    const interval = setInterval(() => {
      this.runAllChecks()
    }, this.checkInterval)

    this.intervals.add(interval)

    // تنّيف عند إغلاق النافذة
    const beforeUnloadHandler = () => {
      this.stop()
    }
    const pageHideHandler = () => {
      this.stop()
    }

    window.addEventListener('beforeunload', beforeUnloadHandler)
    window.addEventListener('pagehide', pageHideHandler)

    // حفّ مراجع المستمعات للتنّيف لاحقاً
    this.eventListeners.set('beforeunload', beforeUnloadHandler)
    this.eventListeners.set('pagehide', pageHideHandler)
  }

  // إيقاف المراقبة
  stop() {
    if (!this.isRunning) {
      return
    }

    Logger.info('SmartNotifications', '🔕 إيقاف مراقبة الإشعارات الذكية')

    try {
      // تنّيف جميع الـ intervals
      this.intervals.forEach(interval => {
        try {
          clearInterval(interval)
        } catch (error) {
          Logger.warn('SmartNotifications', 'خطأ في تنّيف interval:', error)
        }
      })
      this.intervals.clear()

      // إزالة مستمعات الأحداث
      this.eventListeners.forEach((listener, eventType) => {
        try {
          window.removeEventListener(eventType, listener)
        } catch (error) {
          Logger.warn('SmartNotifications', 'خطأ في إزالة مستمع ${eventType}:', error)
        }
      })
      this.eventListeners.clear()

      // تنفيذ callbacks التنّيف
      this.cleanupCallbacks.forEach(callback => {
        try {
          callback()
        } catch (error) {
          Logger.warn('SmartNotifications', 'خطأ في تنفيذ callback التنّيف:', error)
        }
      })
      this.cleanupCallbacks.clear()

      // تنّيف مرجع notification API
      this.notificationApi = null

      // إلغاء تسجيل المورد من مدير الموارد
      if (this.resourceId) {
        resourceManager.unregister(this.resourceId)
        this.resourceId = null
      }

      this.isRunning = false
      Logger.info('SmartNotifications', '✅ تم تنّيف موارد الإشعارات الذكية بنجاح')
    } catch (error) {
      Logger.error('SmartNotifications', '❌ خطأ في تنّيف موارد الإشعارات الذكية:', error)
      this.isRunning = false
    }
  }

  // إضافة callback للتنّيف
  addCleanupCallback(callback: () => void) {
    this.cleanupCallbacks.add(callback)
  }

  // إزالة callback التنّيف
  removeCleanupCallback(callback: () => void) {
    this.cleanupCallbacks.delete(callback)
  }

  // تشغيل جميع الفحوصات
  private async runAllChecks() {
    if (!this.isRunning) {
      return
    }

    Logger.info('SmartNotifications', '🔍 تشغيل فحوصات الإشعارات الذكية...')

    const checkPromises = this.checks.map(async (check) => {
      try {
        // إضافة timeout لكل فحص
        const timeoutPromise = new Promise<boolean>((_, reject) => {
          setTimeout(() => reject(new Error(`انتهت مهلة فحص ${check.name}`)), 10000)
        })

        const shouldNotify = await Promise.race([check.check(), timeoutPromise])

        if (shouldNotify) {
          this.showNotification(check)
        }
      } catch (error) {
        Logger.error('SmartNotifications', 'خطأ في فحص ${check.name}:', error)
        // يمكن إضافة إشعار خطأ هنا إذا لزم الأمر
      }
    })

    // تنفيذ جميع الفحوصات بشكل متوازي
    await Promise.allSettled(checkPromises)
  }

  // عرض الإشعار
  private showNotification(check: NotificationCheck) {
    if (this.notificationApi) {
      this.notificationApi[check.type]({
        message: check.name,
        description: check.message,
        placement: 'topRight',
        duration: 10, // 10 ثواني
        key: check.id // لتجنب الإشعارات المكررة
      })
    } else {
      // fallback للطباعة في console إذا لم يكن API متاحاً
      Logger.warn('SmartNotifications', '[${check.type.toUpperCase()}] ${check.name}: ${check.message}')
    }
  }

  // فحص استخدام الذاكرة
  private async checkMemoryUsage(): Promise<boolean> {
    if (!(performance as any).memory) {
      return false
    }

    const memInfo = (performance as any).memory
    const usedMB = memInfo.usedJSHeapSize / 1024 / 1024
    const limitMB = memInfo.jsHeapSizeLimit / 1024 / 1024

    // تحذير إذا تم استخدام أكثر من 80% من الذاكرة المتاحة
    const usagePercentage = (usedMB / limitMB) * 100

    if (usagePercentage > 80) {
      Logger.warn('SmartNotifications', 'استخدام ذاكرة مرتفع: ${usedMB.toFixed(2)} MB (${usagePercentage.toFixed(1)}%)')
      return true
    }

    return false
  }

  // فحص اتصال قاعدة البيانات
  private async checkDatabaseConnection(): Promise<boolean> {
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.getSystemInfo()
        return !result.success
      }
      return true // إذا لم تكن API متاحة
    } catch (error) {
      Logger.error('SmartNotifications', 'خطأ في فحص قاعدة البيانات:', error)
      return true
    }
  }

  // فحص انتهاء الجلسة
  private async checkSessionExpiry(): Promise<boolean> {
    try {
      const token = localStorage.getItem('authToken')
      if (!token) {
        return false // لا توجد جلسة
      }

      // فحص صحة الرمز المميز
      if (typeof window !== 'undefined' && window.electronAPI) {
        const result = await window.electronAPI.verifySession(token)

        // تحذير إذا كانت الجلسة ستنتهي خلال 10 دقائق
        if (result.success && result.data?.expiresIn) {
          const expiresInMinutes = result.data.expiresIn / (1000 * 60)
          return expiresInMinutes <= 10
        }
      }

      return false
    } catch (error) {
      Logger.error('SmartNotifications', 'خطأ في فحص الجلسة:', error)
      return false
    }
  }

  // فحص تذكير النسخ الاحتياطي
  private async checkBackupReminder(): Promise<boolean> {
    const lastBackup = localStorage.getItem('lastBackupDate')

    if (!lastBackup) {
      return true // لم يتم إنشاء نسخة احتياطية من قبل
    }

    const lastBackupDate = new Date(lastBackup)
    const now = new Date()
    const daysSinceBackup = (now.getTime() - lastBackupDate.getTime()) / (1000 * 60 * 60 * 24)

    // تذكير كل 7 أيام
    return daysSinceBackup >= 7
  }

  // إضافة فحص مخصص
  addCustomCheck(check: NotificationCheck) {
    this.checks.push(check)
    Logger.info('SmartNotifications', 'تم إضافة فحص مخصص: ${check.name}')
  }

  // إزالة فحص
  removeCheck(checkId: string) {
    this.checks = this.checks.filter(check => check.id !== checkId)
    Logger.info('SmartNotifications', 'تم إزالة الفحص: ${checkId}')
  }

  // تغيير فترة الفحص
  setCheckInterval(minutes: number) {
    this.checkInterval = minutes * 60 * 1000

    if (this.isRunning) {
      // إعادة تشغيل مع الفترة الجديدة
      this.stop()
      this.start()
    }

    Logger.info('SmartNotifications', 'تم تغيير فترة الفحص إلى ${minutes} دقيقة')
  }

  // الحصول على حالة المراقبة
  getStatus() {
    return {
      isRunning: this.isRunning,
      activeIntervals: this.intervals.size,
      checksCount: this.checks.length,
      checkInterval: this.checkInterval / (1000 * 60) // بالدقائق
    }
  }
}

// إنشاء مثيل واحد
const smartNotifications = new SmartNotifications()

// تصدير المثيل والكلاس
export default smartNotifications
export { SmartNotifications }

// لا نبدأ المراقبة تلقائياً - سيتم بدؤها من App.tsx
// هذا يضمن أن notification API متاح قبل البدء
