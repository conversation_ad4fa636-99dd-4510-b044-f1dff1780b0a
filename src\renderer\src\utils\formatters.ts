import { SafeLogger as Logger } from './logger';
import { getCurrencySymbol } from './settings';
import dayjs from 'dayjs';

/**
 * دوال موحدة لتنسيق البيانات في التطبيق
 */

// تنسيق العملة
export const formatCurrency = (amount: number | string, showSymbol: boolean = true): string => {
  const numAmount = Number(amount) || 0;
  const symbol = showSymbol ? getCurrencySymbol() : '';

  // تنسيق الرقم مع فواصل الآلاف
  const formattedNumber = numAmount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });

  return showSymbol ? `${symbol} ${formattedNumber}` : formattedNumber;
};

// تنسيق العملة للعرض في الجداول
export const formatCurrencyForTable = (amount: number | string): string => {
  return formatCurrency(amount, true);
};

// تنسيق العملة للإدخال
export const formatCurrencyForInput = (value: string | number): string => {
  const numValue = Number(value) || 0;
  return formatCurrency(numValue, false);
};

// تحليل العملة من النص
export const parseCurrency = (value: string): number => {
  if (!value) return 0;

  // إزالة رمز العملة والفواصل
  const cleanValue = value
    .replace(new RegExp(getCurrencySymbol(), 'g'), '')
    .replace(/,/g, '')
    .trim();

  const parsed = parseFloat(cleanValue);
  return isNaN(parsed) ? 0 : parsed;
};

// تنسيق التواريخ
export const formatDate = (date: string | Date | dayjs.Dayjs, format: string = 'YYYY-MM-DD'): string => {
  if (!date) return '';

  try {
    return dayjs(date).format(format);
  } catch (_error) {
    Logger.error('Formatters', 'خطأ في تنسيق التاريخ:', _error);
    return '';
  }
};

// تنسيق التاريخ للعرض (ميلادي)
export const formatDateGregorian = (date: string | Date | dayjs.Dayjs): string => {
  if (!date) return '';

  try {
    return dayjs(date).format('DD/MM/YYYY');
  } catch (_error) {
    Logger.error('Formatters', 'خطأ في تنسيق التاريخ الميلادي:', _error);
    return '';
  }
};

// تنسيق التاريخ والوقت
export const formatDateTime = (date: string | Date | dayjs.Dayjs): string => {
  if (!date) return '';

  try {
    return dayjs(date).format('DD/MM/YYYY HH:mm');
  } catch (_error) {
    Logger.error('Formatters', 'خطأ في تنسيق التاريخ والوقت:', _error);
    return '';
  }
};

// تنسيق الأرقام
export const formatNumber = (number: number | string, decimals: number = 2): string => {
  const num = Number(number) || 0;
  return num.toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
};

// تنسيق النسب المئوية
export const formatPercentage = (value: number | string, decimals: number = 1): string => {
  const num = Number(value) || 0;
  return `${formatNumber(num, decimals)}%`;
};

// تنسيق الكميات
export const formatQuantity = (quantity: number | string, unit?: string): string => {
  const num = Number(quantity) || 0;
  const formattedQty = formatNumber(num, 3);
  return unit ? `${formattedQty} ${unit}` : formattedQty;
};

// تحقق من صحة التاريخ
export const isValidDate = (date: any): boolean => {
  return dayjs(date).isValid();
};

// تحقق من أن التاريخ ليس في المستقبل
export const isNotFutureDate = (date: any): boolean => {
  if (!isValidDate(date)) return false;
  return dayjs(date).isBefore(dayjs()) || dayjs(date).isSame(dayjs(), 'day');
};

// تحقق من أن التاريخ الثاني بعد الأول
export const isDateAfter = (date1: any, date2: any): boolean => {
  if (!isValidDate(date1) || !isValidDate(date2)) return false;
  return dayjs(date2).isAfter(dayjs(date1));
};

// حساب الفرق بين تاريخين بالأيام
export const daysDifference = (date1: any, date2: any): number => {
  if (!isValidDate(date1) || !isValidDate(date2)) return 0;
  return dayjs(date2).diff(dayjs(date1), 'day');
};

// تنسيق حالة الدفع
export const formatPaymentStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    'pending': 'في الانتظار',
    'paid': 'مدفوعة',
    'partial': 'مدفوعة جزئياً',
    'overdue': 'متأخرة',
    'cancelled': 'ملغية',
    'draft': 'مسودة',
    'sent': 'مرسلة'
  };

  return statusMap[status] || status;
};

// تنسيق طريقة الدفع
export const formatPaymentMethod = (method: string): string => {
  const methodMap: Record<string, string> = {
    'cash': 'نقدي',
    'credit': 'آجل',
    'partial': 'جزئي',
    'bank_transfer': 'تحويل بنكي',
    'check': 'شيك',
    'card': 'بطاقة'
  };

  return methodMap[method] || method;
};

// تنسيق حجم الملف
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// تنسيق رقم الهاتف
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';

  // إزالة جميع الأحرف غير الرقمية
  const cleaned = phone.replace(/\D/g, '');

  // تنسيق رقم فلسطيني
  if (cleaned.startsWith('970')) {
    return cleaned.replace(/(\d{3})(\d{2})(\d{3})(\d{4})/, '+$1 $2 $3 $4');
  }

  // تنسيق رقم محلي
  if (cleaned.startsWith('0')) {
    return cleaned.replace(/(\d{2})(\d{3})(\d{4})/, '$1 $2 $3');
  }

  return phone;
};

// تقصير النص
export const truncateText = (text: string, maxLength: number = 50): string => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// تنسيق الاسم (أول حرف كبير)
export const formatName = (name: string): string => {
  if (!name) return '';
  return name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
};

// تنسيق رقم الفاتورة
export const formatInvoiceNumber = (number: string, prefix?: string): string => {
  if (!number) return '';
  return prefix ? `${prefix}-${number}` : number;
};

export default formatCurrency;