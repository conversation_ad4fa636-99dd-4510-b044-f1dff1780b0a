import * as fs from 'fs'
import * as path from 'path'
import * as crypto from 'crypto'
import { app } from 'electron'
import { LicenseType } from './activationCodes'

/**
 * بيانات الترخيص المخزنة
 */
export interface StoredLicenseData {
  activationCode: string
  licenseType: LicenseType
  activationDate: string
  expiryDate: string | null
  hardwareId: string
  isActive: boolean
}

/**
 * فئة إدارة تخزين بيانات الترخيص
 */
export class LicenseStorage {
  private static instance: LicenseStorage
  private readonly licenseFilePath: string
  private readonly encryptionKey: string

  private constructor() {
    // مسار ملف الترخيص في مجلد البيانات
    const userDataPath = app.getPath('userData')
    this.licenseFilePath = path.join(userDataPath, '.zetia_license')
    
    // مفتاح التشفير (يجب أن يكون ثابت ومعقد)
    this.encryptionKey = 'ZET.IA-2024-FARES-NAWAF-LICENSE-KEY-SECURE'
  }

  public static getInstance(): LicenseStorage {
    if (!LicenseStorage.instance) {
      LicenseStorage.instance = new LicenseStorage()
    }
    return LicenseStorage.instance
  }

  /**
   * تشفير البيانات
   */
  private encrypt(data: string): string {
    try {
      const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey)
      let encrypted = cipher.update(data, 'utf8', 'hex')
      encrypted += cipher.final('hex')
      return encrypted
    } catch (error) {
      console.error('خطأ في تشفير البيانات:', error)
      throw new Error('فشل في تشفير بيانات الترخيص')
    }
  }

  /**
   * فك تشفير البيانات
   */
  private decrypt(encryptedData: string): string {
    try {
      const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey)
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8')
      decrypted += decipher.final('utf8')
      return decrypted
    } catch (error) {
      console.error('خطأ في فك تشفير البيانات:', error)
      throw new Error('فشل في فك تشفير بيانات الترخيص')
    }
  }

  /**
   * حفظ بيانات الترخيص
   */
  public async saveLicenseData(licenseData: StoredLicenseData): Promise<boolean> {
    try {
      const jsonData = JSON.stringify(licenseData, null, 2)
      const encryptedData = this.encrypt(jsonData)
      
      // إنشاء المجلد إذا لم يكن موجود
      const dir = path.dirname(this.licenseFilePath)
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }

      // كتابة البيانات المشفرة
      fs.writeFileSync(this.licenseFilePath, encryptedData, 'utf8')
      
      console.log('تم حفظ بيانات الترخيص بنجاح')
      return true
    } catch (error) {
      console.error('خطأ في حفظ بيانات الترخيص:', error)
      return false
    }
  }

  /**
   * قراءة بيانات الترخيص
   */
  public async loadLicenseData(): Promise<StoredLicenseData | null> {
    try {
      // التحقق من وجود الملف
      if (!fs.existsSync(this.licenseFilePath)) {
        console.log('ملف الترخيص غير موجود')
        return null
      }

      // قراءة البيانات المشفرة
      const encryptedData = fs.readFileSync(this.licenseFilePath, 'utf8')
      
      // فك التشفير
      const jsonData = this.decrypt(encryptedData)
      
      // تحويل إلى كائن
      const licenseData: StoredLicenseData = JSON.parse(jsonData)
      
      console.log('تم تحميل بيانات الترخيص بنجاح')
      return licenseData
    } catch (error) {
      console.error('خطأ في قراءة بيانات الترخيص:', error)
      return null
    }
  }

  /**
   * حذف بيانات الترخيص
   */
  public async deleteLicenseData(): Promise<boolean> {
    try {
      if (fs.existsSync(this.licenseFilePath)) {
        fs.unlinkSync(this.licenseFilePath)
        console.log('تم حذف بيانات الترخيص')
        return true
      }
      return true
    } catch (error) {
      console.error('خطأ في حذف بيانات الترخيص:', error)
      return false
    }
  }

  /**
   * التحقق من وجود ترخيص صالح
   */
  public async hasValidLicense(): Promise<boolean> {
    try {
      const licenseData = await this.loadLicenseData()
      
      if (!licenseData || !licenseData.isActive) {
        return false
      }

      // التحقق من انتهاء الصلاحية
      if (licenseData.expiryDate) {
        const expiryDate = new Date(licenseData.expiryDate)
        if (new Date() > expiryDate) {
          console.log('انتهت صلاحية الترخيص')
          return false
        }
      }

      return true
    } catch (error) {
      console.error('خطأ في التحقق من صحة الترخيص:', error)
      return false
    }
  }

  /**
   * تحديث حالة الترخيص
   */
  public async updateLicenseStatus(isActive: boolean): Promise<boolean> {
    try {
      const licenseData = await this.loadLicenseData()
      
      if (!licenseData) {
        return false
      }

      licenseData.isActive = isActive
      return await this.saveLicenseData(licenseData)
    } catch (error) {
      console.error('خطأ في تحديث حالة الترخيص:', error)
      return false
    }
  }

  /**
   * الحصول على معلومات الترخيص للعرض
   */
  public async getLicenseInfo(): Promise<{
    isActivated: boolean
    licenseType?: LicenseType
    activationDate?: Date
    expiryDate?: Date | null
    daysRemaining?: number
  }> {
    try {
      const licenseData = await this.loadLicenseData()
      
      if (!licenseData || !licenseData.isActive) {
        return { isActivated: false }
      }

      const activationDate = new Date(licenseData.activationDate)
      const expiryDate = licenseData.expiryDate ? new Date(licenseData.expiryDate) : null
      
      let daysRemaining = -1
      if (expiryDate) {
        const now = new Date()
        const diffTime = expiryDate.getTime() - now.getTime()
        daysRemaining = Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)))
      }

      return {
        isActivated: true,
        licenseType: licenseData.licenseType,
        activationDate,
        expiryDate,
        daysRemaining
      }
    } catch (error) {
      console.error('خطأ في الحصول على معلومات الترخيص:', error)
      return { isActivated: false }
    }
  }

  /**
   * إنشاء نسخة احتياطية من بيانات الترخيص
   */
  public async backupLicenseData(): Promise<string | null> {
    try {
      const licenseData = await this.loadLicenseData()
      if (!licenseData) {
        return null
      }

      const backupData = {
        ...licenseData,
        backupDate: new Date().toISOString()
      }

      return JSON.stringify(backupData, null, 2)
    } catch (error) {
      console.error('خطأ في إنشاء نسخة احتياطية:', error)
      return null
    }
  }
}
