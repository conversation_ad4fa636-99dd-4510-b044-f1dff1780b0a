import React, { useState, useEffect } from 'react'
import { Badge, Tooltip, Space } from 'antd'
import { SafetyOutlined, ClockCircleOutlined, WarningOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import { SafeLogger as Logger } from '../../utils/logger'

interface LicenseInfo {
  isActivated: boolean
  licenseType?: string
  activationDate?: string
  expiryDate?: string | null
  daysRemaining?: number
}

const LicenseContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  height: 20px;

  &:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
`

const StatusText = styled.span<{ $status: 'active' | 'warning' | 'expired' }>`
  font-size: 10px;
  font-weight: 600;
  color: ${props => {
    switch (props.$status) {
      case 'active': return '#52c41a'
      case 'warning': return '#faad14'
      case 'expired': return '#ff4d4f'
      default: return '#666'
    }
  }};
`

const DaysText = styled.span<{ $urgent: boolean }>`
  font-size: 9px;
  font-weight: 500;
  color: ${props => props.$urgent ? '#ff4d4f' : '#666'};
  animation: ${props => props.$urgent ? 'pulse 2s infinite' : 'none'};

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
  }
`

const LicenseStatusDisplay: React.FC = () => {
  const [licenseInfo, setLicenseInfo] = useState<LicenseInfo>({ isActivated: false })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadLicenseInfo()
    
    // تحديث المعلومات كل دقيقة
    const interval = setInterval(loadLicenseInfo, 60000)
    
    return () => clearInterval(interval)
  }, [])

  const loadLicenseInfo = async () => {
    try {
      const info = await window.electronAPI?.getLicenseInfo()
      if (info) {
        setLicenseInfo(info)
      }
    } catch (error) {
      Logger.error('LicenseStatusDisplay', 'خطأ في تحميل معلومات التفعيل:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatus = (): 'active' | 'warning' | 'expired' => {
    if (!licenseInfo.isActivated) return 'expired'
    if (licenseInfo.daysRemaining !== undefined && licenseInfo.daysRemaining < 30 && licenseInfo.daysRemaining >= 0) return 'warning'
    if (licenseInfo.daysRemaining !== undefined && licenseInfo.daysRemaining < 0) return 'expired'
    return 'active'
  }

  const getStatusIcon = () => {
    const status = getStatus()
    switch (status) {
      case 'active':
        return <SafetyOutlined style={{ color: '#52c41a' }} />
      case 'warning':
        return <WarningOutlined style={{ color: '#faad14' }} />
      case 'expired':
        return <WarningOutlined style={{ color: '#ff4d4f' }} />
      default:
        return <SafetyOutlined style={{ color: '#666' }} />
    }
  }

  const getStatusText = () => {
    const status = getStatus()
    switch (status) {
      case 'active':
        return licenseInfo.licenseType === 'LIFETIME' ? 'مفعل مدى الحياة' : 'مفعل'
      case 'warning':
        return 'ينتهي قريباً'
      case 'expired':
        return 'منتهي الصلاحية'
      default:
        return 'غير مفعل'
    }
  }

  const getTooltipContent = () => {
    if (!licenseInfo.isActivated) {
      return 'البرنامج غير مفعل - يرجى التفعيل للمتابعة'
    }

    let content = `حالة التفعيل: ${getStatusText()}\n`
    
    if (licenseInfo.activationDate) {
      content += `تاريخ التفعيل: ${new Date(licenseInfo.activationDate).toLocaleDateString('ar-EG')}\n`
    }
    
    if (licenseInfo.expiryDate) {
      content += `تاريخ الانتهاء: ${new Date(licenseInfo.expiryDate).toLocaleDateString('ar-EG')}\n`
    }
    
    if (licenseInfo.daysRemaining !== undefined && licenseInfo.daysRemaining >= 0) {
      content += `الأيام المتبقية: ${licenseInfo.daysRemaining} يوم`
    } else if (licenseInfo.daysRemaining === -1) {
      content += 'ترخيص مدى الحياة'
    }

    return content
  }

  if (loading) {
    return null
  }

  const status = getStatus()
  const isUrgent = status === 'warning' || status === 'expired'

  return (
    <Tooltip title={getTooltipContent()} placement="bottomLeft">
      <LicenseContainer>
        <Badge 
          status={status === 'active' ? 'success' : status === 'warning' ? 'warning' : 'error'} 
          dot 
        />
        {getStatusIcon()}
        <StatusText $status={status}>
          {getStatusText()}
        </StatusText>
        
        {licenseInfo.daysRemaining !== undefined && licenseInfo.daysRemaining >= 0 && (
          <Space size={2}>
            <ClockCircleOutlined style={{
              color: isUrgent ? '#ff4d4f' : '#666',
              fontSize: '9px'
            }} />
            <DaysText $urgent={isUrgent}>
              {licenseInfo.daysRemaining} يوم
            </DaysText>
          </Space>
        )}
      </LicenseContainer>
    </Tooltip>
  )
}

export default LicenseStatusDisplay
