import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Select, 
  Input, 
  Space, 
  message, 
  Row,
  Col,
  Statistic,
  DatePicker,
  Descriptions,
  InputNumber
} from 'antd'
import {
  DollarOutlined,
  ArrowLeftOutlined,
  CheckOutlined,
  UserOutlined,
  BgColorsOutlined,
  PrinterOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../../utils/logger'
import dayjs from 'dayjs'
import { getCurrencySymbol, formatCurrency } from '../../../utils/settings'
import { ReceiptPrintButton } from '../../common'

const { Option } = Select
const { TextArea } = Input

interface PaintPaymentManagementProps {
  onBack: () => void
}

const PaintPaymentManagement: React.FC<PaintPaymentManagementProps> = ({ onBack }) => {
  const [unpaidInvoices, setUnpaidInvoices] = useState([])
  const [bankAccounts, setBankAccounts] = useState([])
  const [loading, setLoading] = useState(false)
  const [paymentModalVisible, setPaymentModalVisible] = useState(false)
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null)
  const [paymentMethod, setPaymentMethod] = useState('receipt_voucher')
  const [form] = Form.useForm()

  // حالة لتخزين بيانات الإيصال المطبوع
  const [lastPaymentReceipt, setLastPaymentReceipt] = useState<any>(null)

  useEffect(() => {
    loadUnpaidInvoices()
    loadBankAccounts()
  }, [])

  const loadUnpaidInvoices = async () => {
    setLoading(true)
    try {
      Logger.info('PaintPaymentManagement', '🔄 جاري تحميل فواتير الدهان غير المدفوعة...')

      if (!window.electronAPI) {
        Logger.error('PaintPaymentManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('PaintPaymentManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية لفواتير الدهان غير المدفوعة
        const mockUnpaidInvoices = [
          {
            id: 1,
            invoice_number: 'PINV001',
            customer_id: 1,
            customer_name: 'أحمد محمد علي',
            total_amount: 375.0,
            final_amount: 431.25,
            type: 'paint',
            status: 'pending',
            invoice_date: '2024-06-20',
            due_date: '2024-07-20',
            payment_status: 'pending',
            notes: 'فاتورة دهان غرفة نوم'
          },
          {
            id: 2,
            invoice_number: 'PINV002',
            customer_id: 2,
            customer_name: 'فاطمة أحمد',
            total_amount: 420.0,
            final_amount: 463.0,
            type: 'paint',
            status: 'pending',
            invoice_date: '2024-06-18',
            due_date: '2024-07-18',
            payment_status: 'pending',
            notes: 'فاتورة دهان خزانة خشبية'
          }
        ]

        setUnpaidInvoices(mockUnpaidInvoices as any)
        Logger.info('PaintPaymentManagement', '✅ تم تحميل ${mockUnpaidInvoices.length} فاتورة دهان غير مدفوعة وهمية')
      } else {
        const response = await (window.electronAPI as any).getUnpaidInvoices('customer')
        if (response.success) {
          // فلترة فواتير الدهان فقط
          const paintInvoices = response.data.filter((invoice: any) => invoice.type === 'paint')
          setUnpaidInvoices(paintInvoices)
          Logger.info('PaintPaymentManagement', '✅ تم تحميل فواتير الدهان غير المدفوعة من قاعدة البيانات')
        } else {
          message.error('فشل في تحميل فواتير الدهان غير المدفوعة')
        }
      }
    } catch (error) {
      message.error('خطأ في تحميل فواتير الدهان غير المدفوعة')
    }
    setLoading(false)
  }

  const loadBankAccounts = async () => {
    try {
      Logger.info('PaintPaymentManagement', '🔄 جاري تحميل الحسابات المصرفية...')

      if (!window.electronAPI) {
        Logger.error('PaintPaymentManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('PaintPaymentManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للحسابات المصرفية
        const mockBankAccounts = [
          { id: 1, account_name: 'حساب بنكي رئيسي', account_number: '*********', balance: 50000, is_active: true },
          { id: 2, account_name: 'حساب بنكي فرعي', account_number: '*********', balance: 25000, is_active: true }
        ]

        setBankAccounts(mockBankAccounts as any)
        Logger.info('PaintPaymentManagement', '✅ تم تحميل ${mockBankAccounts.length} حساب مصرفي وهمي')
      } else {
        const response = await window.electronAPI.getBankAccounts()
        if (response.success) {
          setBankAccounts(response.data)
          Logger.info('PaintPaymentManagement', '✅ تم تحميل الحسابات المصرفية من قاعدة البيانات')
        }
      }
    } catch (error) {
      Logger.error('PaintPaymentManagement', 'خطأ في تحميل الحسابات المصرفية:', error)
    }
  }

  const handlePayment = async (values: any) => {
    if (!selectedInvoice) {
      message.error('لم يتم اختيار فاتورة للدفع')
      return
    }

    // التحقق من صحة بيانات الدفع
    if (!values.amount || values.amount <= 0) {
      message.error('مبلغ الدفعة يجب أن يكون أكبر من صفر')
      return
    }

    if (values.amount > selectedInvoice.remaining_amount) {
      message.error(`مبلغ الدفعة لا يمكن أن يكون أكبر من المبلغ المتبقي (${formatCurrency(selectedInvoice.remaining_amount)})`)
      return
    }

    if (!values.payment_date) {
      message.error('تاريخ الدفع مطلوب')
      return
    }

    // التحقق من البيانات المطلوبة حسب طريقة الدفع
    if (paymentMethod === 'check') {
      if (!values.check_number) {
        message.error('رقم الشيك مطلوب')
        return
      }
      if (!values.due_date) {
        message.error('تاريخ الاستحقاق مطلوب')
        return
      }
    } else if (paymentMethod === 'receipt_voucher') {
      if (!values.voucher_number) {
        message.error('رقم السند مطلوب')
        return
      }
    }

    try {
      let paymentResponse
      
      // إنشاء الدفعة حسب نوع الدفع
      if (paymentMethod === 'check') {
        // إنشاء شيك مستلم
        const checkData = {
          check_number: values.check_number,
          bank_account_id: values.bank_account_id,
          amount: values.amount,
          issue_date: values.payment_date.format('YYYY-MM-DD'),
          due_date: values.due_date ? values.due_date.format('YYYY-MM-DD') : values.payment_date.format('YYYY-MM-DD'),
          payee_name: 'الشركة',
          notes: values.notes,
          check_type: 'received',
          original_payer: selectedInvoice.customer_name || 'عميل',
          current_holder: 'الشركة',
          reference_type: 'paint_invoice',
          reference_id: selectedInvoice.id,
          created_by: 1
        }
        paymentResponse = await window.electronAPI.createCheck(checkData)
      } else if (paymentMethod === 'receipt_voucher') {
        // إنشاء سند قبض
        const voucherData = {
          voucher_number: values.voucher_number,
          amount: values.amount,
          receipt_date: values.payment_date.format('YYYY-MM-DD'),
          payer_name: selectedInvoice.customer_name || 'عميل',
          description: `تحصيل من فاتورة دهان رقم ${selectedInvoice.number}`,
          payment_method: values.voucher_payment_method || 'cash',
          bank_account_id: values.bank_account_id,
          reference_type: 'paint_invoice',
          reference_id: selectedInvoice.id,
          currency_id: 1,
          created_by: 1,
          notes: values.notes
        }
        paymentResponse = await window.electronAPI.createReceiptVoucher(voucherData)
      }

      if (paymentResponse && paymentResponse.success) {
        // ربط الدفعة بالفاتورة
        const linkData = {
          invoice_type: 'paint_invoice',
          invoice_id: selectedInvoice.id,
          payment_type: paymentMethod === 'check' ? 'check' : 'receipt_voucher',
          payment_id: paymentResponse.data?.checkId || paymentResponse.data?.voucherId,
          amount: values.amount
        }

        if (window.electronAPI) {
          const linkResponse = await window.electronAPI.linkInvoiceToPayment(linkData)
          if (linkResponse.success) {
            // إنشاء بيانات الإيصال للطباعة
            const receiptData = {
              id: paymentResponse.data?.checkId || paymentResponse.data?.voucherId || Date.now(),
              receiptNumber: values.voucher_number || values.check_number || `PAINT-REC-${Date.now()}`,
              receiptDate: values.payment_date.format('YYYY-MM-DD'),
              type: 'payment' as const,
              amount: values.amount,
              paymentMethod: paymentMethod === 'check' ? 'check' :
                            values.voucher_payment_method === 'bank' ? 'bank' : 'cash',
              customerName: selectedInvoice.customer_name || 'عميل',
              description: `تحصيل من فاتورة دهان رقم ${selectedInvoice.number}`,
              notes: values.notes,
              referenceNumber: selectedInvoice.number,
              bankName: paymentMethod === 'check' ?
                       bankAccounts.find(acc => acc.id === values.bank_account_id)?.bank_name : undefined,
              checkNumber: paymentMethod === 'check' ? values.check_number : undefined
            }

            setLastPaymentReceipt(receiptData)
            message.success('تم تسجيل الدفعة وربطها بفاتورة الدهان بنجاح')
            setPaymentModalVisible(false)
            form.resetFields()
            loadUnpaidInvoices()
          } else {
            message.error(`تم إنشاء الدفعة ولكن فشل في ربطها بالفاتورة: ${linkResponse.message || 'خطأ غير معروف'}`)
          }
        } else {
          message.success('تم تسجيل الدفعة بنجاح (وضع المتصفح)')
          setPaymentModalVisible(false)
          form.resetFields()
          loadUnpaidInvoices()
        }
      } else {
        message.error(`فشل في إنشاء الدفعة: ${paymentResponse?.message || 'خطأ غير معروف'}`)
      }
    } catch (error) {
      Logger.error('PaintPaymentManagement', 'خطأ في تسجيل الدفعة:', error)
      message.error('خطأ في تسجيل الدفعة')
    }
  }

  const showPaymentModal = (invoice: any) => {
    setSelectedInvoice(invoice)
    setPaymentModalVisible(true)
    form.setFieldsValue({
      payment_date: dayjs(),
      amount: invoice.remaining_amount
    })
  }

  const generateVoucherNumber = async () => {
    try {
      const response = await window.electronAPI.generateReceiptVoucherNumber()
      if (response.success) {
        form.setFieldsValue({ voucher_number: response.data.voucherNumber })
      }
    } catch (error) {
      message.error('خطأ في إنشاء رقم السند')
    }
  }

  const generateCheckNumber = async () => {
    try {
      const response = await window.electronAPI.generateCheckNumber()
      if (response.success) {
        form.setFieldsValue({ check_number: response.data.checkNumber })
      }
    } catch (error) {
      message.error('خطأ في إنشاء رقم الشيك')
    }
  }

  const columns = [
    {
      title: 'رقم الفاتورة',
      dataIndex: 'number',
      key: 'number',
      render: (number: string) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{number}</span>
      )
    },
    {
      title: 'العميل',
      dataIndex: 'customer_name',
      key: 'customer_name',
      render: (name: string, record: any) => (
        <Space>
          <UserOutlined style={{ color: '#1890ff' }} />
          <span>{name || `عميل ${record.entity_id}`}</span>
        </Space>
      )
    },
    {
      title: 'تاريخ الفاتورة',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'المساحة الإجمالية',
      dataIndex: 'total_area',
      key: 'total_area',
      render: (area: number) => (
        <span style={{ color: '#722ed1' }}>
          {area?.toFixed(2) || 0} م²
        </span>
      )
    },
    {
      title: 'المبلغ الإجمالي',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (amount: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          {formatCurrency(amount || 0)}
        </span>
      )
    },
    {
      title: 'المبلغ المدفوع',
      dataIndex: 'paid_amount',
      key: 'paid_amount',
      render: (amount: number) => (
        <span style={{ color: '#1890ff' }}>
          {formatCurrency(amount || 0)}
        </span>
      )
    },
    {
      title: 'المبلغ المتبقي',
      dataIndex: 'remaining_amount',
      key: 'remaining_amount',
      render: (amount: number) => (
        <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
          {formatCurrency(amount || 0)}
        </span>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: any) => (
        <Button
          type="primary"
          size="small"
          icon={<DollarOutlined />}
          onClick={() => showPaymentModal(record)}
        >
          تسجيل دفعة
        </Button>
      )
    }
  ]

  const stats = {
    totalInvoices: unpaidInvoices.length,
    totalAmount: unpaidInvoices.reduce((sum: number, invoice: any) => sum + (invoice.remaining_amount || 0), 0),
    totalArea: unpaidInvoices.reduce((sum: number, invoice: any) => sum + (invoice.total_area || 0), 0)
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>🎨 إدارة مدفوعات الدهان</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            تسجيل وربط المدفوعات بفواتير الدهان
          </p>
        </div>
        <Space>
          {lastPaymentReceipt && (
            <ReceiptPrintButton
              receiptData={lastPaymentReceipt}
              buttonText="طباعة آخر إيصال"
              size="middle"
              onPrintSuccess={() => message.success('تم طباعة الإيصال بنجاح')}
              onPrintError={() => message.error('فشل في طباعة الإيصال')}
            />
          )}
          <Button
            type="default"
            icon={<ArrowLeftOutlined />}
            onClick={onBack}
          >
            العودة
          </Button>
        </Space>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="فواتير دهان غير مدفوعة"
              value={stats.totalInvoices}
              valueStyle={{ color: '#1890ff' }}
              prefix={<BgColorsOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="إجمالي المبالغ المستحقة"
              value={stats.totalAmount}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={getCurrencySymbol()}
              precision={0}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="إجمالي المساحة"
              value={stats.totalArea}
              valueStyle={{ color: '#722ed1' }}
              suffix="م²"
              precision={2}
            />
          </Card>
        </Col>
      </Row>

      {/* جدول فواتير الدهان غير المدفوعة */}
      <Card title="فواتير الدهان غير المدفوعة">
        <Table
          columns={columns}
          dataSource={unpaidInvoices}
          rowKey={(record: any) => `paint-${record.id}`}
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* نموذج تسجيل الدفعة */}
      <Modal
        title="تسجيل دفعة لفاتورة دهان"
        open={paymentModalVisible}
        onCancel={() => {
          setPaymentModalVisible(false)
          setSelectedInvoice(null)
          form.resetFields()
        }}
        footer={null}
        width={800}
      >
        {selectedInvoice && (
          <>
            <Descriptions title="بيانات فاتورة الدهان" bordered size="small" style={{ marginBottom: '24px' }}>
              <Descriptions.Item label="رقم الفاتورة">{selectedInvoice.number}</Descriptions.Item>
              <Descriptions.Item label="العميل">{selectedInvoice.customer_name || `عميل ${selectedInvoice.entity_id}`}</Descriptions.Item>
              <Descriptions.Item label="تاريخ الفاتورة">{dayjs(selectedInvoice.date).format('YYYY-MM-DD')}</Descriptions.Item>
              <Descriptions.Item label="المساحة الإجمالية">
                <span style={{ color: '#722ed1' }}>
                  {selectedInvoice.total_area?.toFixed(2) || 0} م²
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="المبلغ الإجمالي">
                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                  {formatCurrency(selectedInvoice.total_amount || 0)}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="المبلغ المتبقي">
                <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
                  {formatCurrency(selectedInvoice.remaining_amount || 0)}
                </span>
              </Descriptions.Item>
            </Descriptions>

            <Form
              form={form}
              layout="vertical"
              onFinish={handlePayment}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="payment_method"
                    label="طريقة الدفع"
                    rules={[{ required: true, message: 'يرجى اختيار طريقة الدفع' }]}
                    initialValue="receipt_voucher"
                  >
                    <Select
                      placeholder="اختر طريقة الدفع"
                      onChange={(value) => setPaymentMethod(value)}
                    >
                      <Option value="receipt_voucher">سند قبض</Option>
                      <Option value="check">شيك</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="amount"
                    label="المبلغ"
                    rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="0"
                      min={0}
                      max={selectedInvoice.remaining_amount}
                      formatter={value => `${getCurrencySymbol()} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => (value || '').replace(new RegExp(`${getCurrencySymbol()}\\s?|(,*)`,'g'), '')}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="payment_date"
                    label="تاريخ الدفع"
                    rules={[{ required: true, message: 'يرجى اختيار تاريخ الدفع' }]}
                  >
                    <DatePicker
                      style={{ width: '100%' }}
                      placeholder="اختر تاريخ الدفع"
                      format="YYYY-MM-DD"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  {paymentMethod === 'receipt_voucher' ? (
                    <Form.Item
                      name="voucher_number"
                      label="رقم السند"
                      rules={[{ required: true, message: 'يرجى إدخال رقم السند' }]}
                    >
                      <Input
                        placeholder="RV000001"
                        addonAfter={
                          <Button
                            type="link"
                            size="small"
                            onClick={generateVoucherNumber}
                          >
                            توليد
                          </Button>
                        }
                      />
                    </Form.Item>
                  ) : (
                    <Form.Item
                      name="check_number"
                      label="رقم الشيك"
                      rules={[{ required: true, message: 'يرجى إدخال رقم الشيك' }]}
                    >
                      <Input
                        placeholder="CHK000001"
                        addonAfter={
                          <Button
                            type="link"
                            size="small"
                            onClick={generateCheckNumber}
                          >
                            توليد
                          </Button>
                        }
                      />
                    </Form.Item>
                  )}
                </Col>
              </Row>

              {paymentMethod === 'check' && (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="due_date"
                      label="تاريخ استحقاق الشيك"
                    >
                      <DatePicker
                        style={{ width: '100%' }}
                        placeholder="اختر تاريخ الاستحقاق"
                        format="YYYY-MM-DD"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="bank_account_id"
                      label="الحساب المصرفي"
                      rules={[{ required: paymentMethod === 'check', message: 'يرجى اختيار الحساب المصرفي' }]}
                    >
                      <Select placeholder="اختر الحساب المصرفي">
                        {bankAccounts.map((account: any) => (
                          <Option key={account.id} value={account.id}>
                            {account.bank_name} - {account.account_name} ({account.account_number})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              )}

              {paymentMethod === 'receipt_voucher' && (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="voucher_payment_method"
                      label="طريقة الدفع في السند"
                      initialValue="cash"
                    >
                      <Select placeholder="اختر طريقة الدفع">
                        <Option value="cash">نقدي</Option>
                        <Option value="bank_transfer">تحويل بنكي</Option>
                        <Option value="check">شيك</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="bank_account_id"
                      label="الحساب المصرفي (اختياري)"
                    >
                      <Select placeholder="اختر الحساب المصرفي">
                        {bankAccounts.map((account: any) => (
                          <Option key={account.id} value={account.id}>
                            {account.bank_name} - {account.account_name} ({account.account_number})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              )}

              <Form.Item
                name="notes"
                label="ملاحّات"
              >
                <TextArea
                  placeholder="ملاحّات إضافية حول الدفعة"
                  rows={3}
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<CheckOutlined />}>
                    تسجيل الدفعة
                  </Button>
                  <Button onClick={() => {
                    setPaymentModalVisible(false)
                    setSelectedInvoice(null)
                    form.resetFields()
                  }}>
                    إلغاء
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </>
        )}
      </Modal>
    </div>
  )
}

export default PaintPaymentManagement
