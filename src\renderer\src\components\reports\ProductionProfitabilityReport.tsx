/**
 * تقرير ربحية الإنتاج
 * تقرير شامل لربحية عمليات الإنتاج والمنتجات
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const ProductionProfitabilityReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'production_profitability' as ReportType}
      title="تقرير ربحية الإنتاج"
      description="تقرير مفصل لربحية عمليات الإنتاج والمنتجات مع تحليل التكاليف والعوائد"
      showDateRange={true}
      showDepartmentFilter={true}
      showCustomerFilter={true}
      showItemFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="production_profitability_report"
      defaultFilters={{
        sortBy: 'profit_margin',
        sortOrder: 'desc'
      }}
    />
  )
}

export default ProductionProfitabilityReport
