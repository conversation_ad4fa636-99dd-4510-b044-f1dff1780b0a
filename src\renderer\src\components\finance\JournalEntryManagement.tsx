import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Space,
  message,
  Tag,
  Row,
  Col,
  Statistic,
  Divider,
  <PERSON><PERSON>,
  Toolt<PERSON>,
  Popconfirm
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  PlusOutlined,
  ArrowLeftOutlined,
  FileTextOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckOutlined,
  ExclamationCircleOutlined,
  EyeOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { Option } = Select

interface JournalEntryManagementProps {
  onBack: () => void
}

interface JournalEntry {
  id: number
  entry_number: string
  entry_date: string
  description: string
  total_debit: number
  total_credit: number
  status: 'draft' | 'posted' | 'cancelled'
  created_at: string
  created_by_name?: string
  details?: JournalEntryDetail[]
}

interface JournalEntryDetail {
  id?: number
  account_id: number
  account_name?: string
  account_code?: string
  debit_amount: number
  credit_amount: number
  description?: string
}

interface Account {
  id: number
  account_code: string
  account_name: string
  account_type: string
  balance: number
}

const JournalEntryManagement: React.FC<JournalEntryManagementProps> = ({ onBack }) => {
  const [entries, setEntries] = useState<JournalEntry[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [viewModalVisible, setViewModalVisible] = useState(false)
  const [editingEntry, setEditingEntry] = useState<JournalEntry | null>(null)
  const [viewingEntry, setViewingEntry] = useState<JournalEntry | null>(null)
  const [form] = Form.useForm()
  const [entryDetails, setEntryDetails] = useState<JournalEntryDetail[]>([
    { account_id: 0, debit_amount: 0, credit_amount: 0, description: '' }
  ])

  const [stats, setStats] = useState({
    totalEntries: 0,
    draftEntries: 0,
    postedEntries: 0,
    totalDebit: 0,
    totalCredit: 0
  })

  const loadJournalEntries = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.getJournalEntries()
      if (Array.isArray(response)) {
        setEntries(response)
        calculateStats(response)
      } else {
        Logger.error('JournalEntryManagement', 'خطأ في جلب القيود اليومية:', response)
        message.error('فشل في جلب القيود اليومية')
      }
    } catch (error) {
      Logger.error('JournalEntryManagement', 'خطأ في جلب القيود اليومية:', error)
      message.error('حدث خطأ في جلب القيود اليومية')
    } finally {
      setLoading(false)
    }
  }

  const loadAccounts = async () => {
    try {
      const response = await window.electronAPI.getChartOfAccounts()
      if (response.success && response.data) {
        setAccounts(response.data)
      } else {
        message.error('فشل في جلب دليل الحسابات')
      }
    } catch (error) {
      Logger.error('JournalEntryManagement', 'خطأ في جلب دليل الحسابات:', error)
      message.error('حدث خطأ في جلب دليل الحسابات')
    }
  }

  useEffect(() => {
    loadJournalEntries()
    loadAccounts()
  }, [])

  const calculateStats = (entriesData: JournalEntry[]) => {
    const stats = {
      totalEntries: entriesData.length,
      draftEntries: entriesData.filter(e => e.status === 'draft').length,
      postedEntries: entriesData.filter(e => e.status === 'posted').length,
      totalDebit: entriesData.reduce((sum, e) => sum + (e.total_debit || 0), 0),
      totalCredit: entriesData.reduce((sum, e) => sum + (e.total_credit || 0), 0)
    }
    setStats(stats)
  }

  const validateBalance = () => {
    const totalDebit = entryDetails.reduce((sum, detail) => sum + (detail.debit_amount || 0), 0)
    const totalCredit = entryDetails.reduce((sum, detail) => sum + (detail.credit_amount || 0), 0)
    const difference = Math.abs(totalDebit - totalCredit)
    
    return {
      isBalanced: difference < 0.01,
      totalDebit,
      totalCredit,
      difference
    }
  }

  const handleCreateEntry = () => {
    setEditingEntry(null)
    setEntryDetails([
      { account_id: 0, debit_amount: 0, credit_amount: 0, description: '' },
      { account_id: 0, debit_amount: 0, credit_amount: 0, description: '' }
    ])
    form.resetFields()
    form.setFieldsValue({
      entry_date: dayjs(),
      description: ''
    })
    setModalVisible(true)
  }

  const handleEditEntry = (entry: JournalEntry) => {
    if (entry.status === 'posted') {
      message.warning('لا يمكن تعديل قيد مرحل')
      return
    }
    
    setEditingEntry(entry)
    setEntryDetails(entry.details || [])
    form.setFieldsValue({
      entry_date: dayjs(entry.entry_date),
      description: entry.description
    })
    setModalVisible(true)
  }

  const handleViewEntry = async (entry: JournalEntry) => {
    try {
      const response = await window.electronAPI.getJournalEntries({ entryId: entry.id })
      if (Array.isArray(response) && response.length > 0) {
        setViewingEntry(response[0])
        setViewModalVisible(true)
      }
    } catch (error) {
      Logger.error('JournalEntryManagement', 'خطأ في جلب تفاصيل القيد:', error)
      message.error('فشل في جلب تفاصيل القيد')
    }
  }

  const handleDeleteEntry = async (entryId: number) => {
    try {
      const response = await window.electronAPI.deleteJournalEntry(entryId)
      if (response.success) {
        message.success('تم حذف القيد بنجاح')
        loadJournalEntries()
      } else {
        message.error(response.message || 'فشل في حذف القيد')
      }
    } catch (error) {
      Logger.error('JournalEntryManagement', 'خطأ في حذف القيد:', error)
      message.error('حدث خطأ في حذف القيد')
    }
  }

  const handlePostEntry = async (entryId: number) => {
    try {
      const response = await window.electronAPI.postJournalEntry(entryId, 1)
      if (response.success) {
        message.success('تم ترحيل القيد بنجاح')
        loadJournalEntries()
      } else {
        message.error(response.message || 'فشل في ترحيل القيد')
      }
    } catch (error) {
      Logger.error('JournalEntryManagement', 'خطأ في ترحيل القيد:', error)
      message.error('حدث خطأ في ترحيل القيد')
    }
  }

  const addDetailRow = () => {
    setEntryDetails([
      ...entryDetails,
      { account_id: 0, debit_amount: 0, credit_amount: 0, description: '' }
    ])
  }

  const removeDetailRow = (index: number) => {
    if (entryDetails.length > 1) {
      const newDetails = entryDetails.filter((_, i) => i !== index)
      setEntryDetails(newDetails)
    }
  }

  const updateDetailRow = (index: number, field: keyof JournalEntryDetail, value: any) => {
    const newDetails = [...entryDetails]
    newDetails[index] = { ...newDetails[index], [field]: value }
    setEntryDetails(newDetails)
  }

  const handleSubmit = async (values: any) => {
    // التحقق من صحة التاريخ
    if (values.entry_date && values.entry_date.isAfter(dayjs())) {
      message.error('لا يمكن أن يكون تاريخ القيد في المستقبل')
      return
    }

    // التحقق من وجود وصف
    if (!values.description || values.description.trim().length < 5) {
      message.error('يجب إدخال وصف للقيد (على الأقل 5 أحرف)')
      return
    }

    const balance = validateBalance()

    if (!balance.isBalanced) {
      message.error('القيد غير متوازن! الفرق: ' + balance.difference.toFixed(2))
      return
    }

    if (entryDetails.length < 2) {
      message.error('يجب إدخال سطرين على الأقل')
      return
    }

    if (entryDetails.some(detail => detail.account_id === 0)) {
      message.error('يجب اختيار حساب لجميع السطور')
      return
    }

    // التحقق من أن كل سطر له مبلغ صحيح
    if (entryDetails.some(detail => detail.debit_amount === 0 && detail.credit_amount === 0)) {
      message.error('يجب إدخال مبلغ لجميع السطور')
      return
    }

    // التحقق من عدم وجود مبالغ سالبة
    if (entryDetails.some(detail => detail.debit_amount < 0 || detail.credit_amount < 0)) {
      message.error('لا يمكن أن تكون المبالغ سالبة')
      return
    }

    try {
      const entryData = {
        entry_date: values.entry_date.format('YYYY-MM-DD'),
        description: values.description,
        details: entryDetails.filter(detail => 
          detail.account_id > 0 && (detail.debit_amount > 0 || detail.credit_amount > 0)
        )
      }

      let response
      if (editingEntry) {
        response = await window.electronAPI.updateJournalEntry(editingEntry.id, entryData)
      } else {
        response = await window.electronAPI.createJournalEntry(entryData, 1)
      }

      if (response.success) {
        message.success(editingEntry ? 'تم تحديث القيد بنجاح' : 'تم إنشاء القيد بنجاح')
        setModalVisible(false)
        loadJournalEntries()
        form.resetFields()
        setEntryDetails([{ account_id: 0, debit_amount: 0, credit_amount: 0, description: '' }])
      } else {
        message.error(response.message || 'فشل في حفّ القيد')
      }
    } catch (error) {
      Logger.error('JournalEntryManagement', 'خطأ في حفّ القيد:', error)
      message.error('حدث خطأ في حفّ القيد')
    }
  }

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>📋 إدارة القيود اليومية</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            إدخال وإدارة القيود المحاسبية اليومية
          </p>
        </div>
        <Button 
          type="default" 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
          size="large"
        >
          العودة
        </Button>
      </div>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي القيود"
              value={stats.totalEntries}
              valueStyle={{ color: '#1890ff' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="قيود مسودة"
              value={stats.draftEntries}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<EditOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="قيود مرحلة"
              value={stats.postedEntries}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي المدين"
              value={stats.totalDebit}
              valueStyle={{ color: '#722ed1' }}
              prefix="₪"
              precision={2}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الدائن"
              value={stats.totalCredit}
              valueStyle={{ color: '#13c2c2' }}
              prefix="₪"
              precision={2}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Table */}
      <Card
        title={
          <Space>
            <FileTextOutlined />
            <span>القيود اليومية</span>
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateEntry}
          >
            قيد جديد
          </Button>
        }
      >
        <Table
          columns={[
            {
              title: 'رقم القيد',
              dataIndex: 'entry_number',
              key: 'entry_number',
              width: 120
            },
            {
              title: 'التاريخ',
              dataIndex: 'entry_date',
              key: 'entry_date',
              width: 120,
              render: (date: string) => dayjs(date).format('YYYY-MM-DD')
            },
            {
              title: 'البيان',
              dataIndex: 'description',
              key: 'description',
              ellipsis: true
            },
            {
              title: 'المدين',
              dataIndex: 'total_debit',
              key: 'total_debit',
              width: 120,
              render: (amount: number) => '₪' + (amount?.toFixed(2) || '0.00')
            },
            {
              title: 'الدائن',
              dataIndex: 'total_credit',
              key: 'total_credit',
              width: 120,
              render: (amount: number) => '₪' + (amount?.toFixed(2) || '0.00')
            },
            {
              title: 'الحالة',
              dataIndex: 'status',
              key: 'status',
              width: 100,
              render: (status: string) => {
                const statusConfig = {
                  draft: { color: 'orange', text: 'مسودة' },
                  posted: { color: 'green', text: 'مرحل' },
                  cancelled: { color: 'red', text: 'ملغي' }
                }
                const config = statusConfig[status as keyof typeof statusConfig]
                return <Tag color={config?.color}>{config?.text}</Tag>
              }
            },
            {
              title: 'الإجراءات',
              key: 'actions',
              width: 200,
              render: (_, record: JournalEntry) => (
                <Space>
                  <Tooltip title="عرض">
                    <Button
                      type="text"
                      icon={<EyeOutlined />}
                      onClick={() => handleViewEntry(record)}
                    />
                  </Tooltip>
                  {record.status === 'draft' && (
                    <>
                      <Tooltip title="تعديل">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={() => handleEditEntry(record)}
                        />
                      </Tooltip>
                      <Tooltip title="ترحيل">
                        <Popconfirm
                          title="هل أنت متأكد من ترحيل هذا القيد؟"
                          onConfirm={() => handlePostEntry(record.id)}
                          okText="نعم"
                          cancelText="لا"
                        >
                          <Button
                            type="text"
                            icon={<CheckOutlined />}
                            style={{ color: '#52c41a' }}
                          />
                        </Popconfirm>
                      </Tooltip>
                      <Tooltip title="حذف">
                        <Popconfirm
                          title="هل أنت متأكد من حذف هذا القيد؟"
                          onConfirm={() => handleDeleteEntry(record.id)}
                          okText="نعم"
                          cancelText="لا"
                        >
                          <Button
                            type="text"
                            icon={<DeleteOutlined />}
                            danger
                          />
                        </Popconfirm>
                      </Tooltip>
                    </>
                  )}
                </Space>
              )
            }
          ]}
          dataSource={entries}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => 'إجمالي ' + total + ' قيد'
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={editingEntry ? 'تعديل القيد اليومي' : 'قيد يومي جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
          setEntryDetails([{ account_id: 0, debit_amount: 0, credit_amount: 0, description: '' }])
        }}
        footer={null}
        width={1000}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="entry_date"
                label="تاريخ القيد"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ القيد' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="description"
                label="بيان القيد"
                rules={[{ required: true, message: 'يرجى إدخال بيان القيد' }]}
              >
                <Input placeholder="بيان القيد" />
              </Form.Item>
            </Col>
          </Row>

          <Divider>تفاصيل القيد</Divider>

          {/* Balance Check */}
          {(() => {
            const balance = validateBalance()
            return (
              <Alert
                message={
                  <Row gutter={16}>
                    <Col span={6}>
                      <Statistic
                        title="إجمالي المدين"
                        value={balance.totalDebit}
                        precision={2}
                        prefix="₪"
                        valueStyle={{ fontSize: '14px' }}
                      />
                    </Col>
                    <Col span={6}>
                      <Statistic
                        title="إجمالي الدائن"
                        value={balance.totalCredit}
                        precision={2}
                        prefix="₪"
                        valueStyle={{ fontSize: '14px' }}
                      />
                    </Col>
                    <Col span={6}>
                      <Statistic
                        title="الفرق"
                        value={balance.difference}
                        precision={2}
                        prefix="₪"
                        valueStyle={{
                          fontSize: '14px',
                          color: balance.isBalanced ? '#52c41a' : '#ff4d4f'
                        }}
                      />
                    </Col>
                    <Col span={6}>
                      <div style={{ textAlign: 'center', paddingTop: '8px' }}>
                        {balance.isBalanced ? (
                          <Tag color="success" icon={<CheckOutlined />}>متوازن</Tag>
                        ) : (
                          <Tag color="error" icon={<ExclamationCircleOutlined />}>غير متوازن</Tag>
                        )}
                      </div>
                    </Col>
                  </Row>
                }
                type={balance.isBalanced ? 'success' : 'warning'}
                style={{ marginBottom: '16px' }}
              />
            )
          })()}

          {/* Entry Details */}
          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {entryDetails.map((detail, index) => (
              <Card
                key={index}
                size="small"
                style={{ marginBottom: '8px' }}
                extra={
                  entryDetails.length > 1 && (
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removeDetailRow(index)}
                    />
                  )
                }
              >
                <Row gutter={8}>
                  <Col span={8}>
                    <Select
                      placeholder="اختر الحساب"
                      value={detail.account_id || undefined}
                      onChange={(value) => updateDetailRow(index, 'account_id', value)}
                      style={{ width: '100%' }}
                      showSearch
                      filterOption={(input, option) =>
                        (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                      }
                    >
                      {accounts.map(account => (
                        <Option key={account.id} value={account.id}>
                          {account.account_code} - {account.account_name}
                        </Option>
                      ))}
                    </Select>
                  </Col>
                  <Col span={4}>
                    <InputNumber
                      placeholder="مدين"
                      value={detail.debit_amount}
                      onChange={(value) => updateDetailRow(index, 'debit_amount', value || 0)}
                      style={{ width: '100%' }}
                      min={0}
                      precision={2}
                    />
                  </Col>
                  <Col span={4}>
                    <InputNumber
                      placeholder="دائن"
                      value={detail.credit_amount}
                      onChange={(value) => updateDetailRow(index, 'credit_amount', value || 0)}
                      style={{ width: '100%' }}
                      min={0}
                      precision={2}
                    />
                  </Col>
                  <Col span={8}>
                    <Input
                      placeholder="البيان"
                      value={detail.description}
                      onChange={(e) => updateDetailRow(index, 'description', e.target.value)}
                    />
                  </Col>
                </Row>
              </Card>
            ))}
          </div>

          <Button
            type="dashed"
            onClick={addDetailRow}
            style={{ width: '100%', marginTop: '8px' }}
            icon={<PlusOutlined />}
          >
            إضافة سطر
          </Button>

          <div style={{ textAlign: 'center', marginTop: '24px' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                disabled={!validateBalance().isBalanced}
                icon={<CheckOutlined />}
              >
                {editingEntry ? 'تحديث القيد' : 'حفّ القيد'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* View Modal */}
      <Modal
        title={'عرض القيد: ' + (viewingEntry?.entry_number || '')}
        open={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setViewModalVisible(false)}>
            إغلاق
          </Button>
        ]}
        width={800}
      >
        {viewingEntry && (
          <div>
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={12}>
                <strong>تاريخ القيد:</strong> {dayjs(viewingEntry.entry_date).format('YYYY-MM-DD')}
              </Col>
              <Col span={12}>
                <strong>الحالة:</strong>
                <Tag color={viewingEntry.status === 'posted' ? 'green' : 'orange'} style={{ marginLeft: '8px' }}>
                  {viewingEntry.status === 'posted' ? 'مرحل' : 'مسودة'}
                </Tag>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={24}>
                <strong>البيان:</strong> {viewingEntry.description}
              </Col>
            </Row>

            <Divider>تفاصيل القيد</Divider>

            <Table
              columns={[
                {
                  title: 'الحساب',
                  dataIndex: 'account_name',
                  key: 'account_name',
                  render: (name: string, record: any) => record.account_code + ' - ' + name
                },
                {
                  title: 'البيان',
                  dataIndex: 'description',
                  key: 'description'
                },
                {
                  title: 'مدين',
                  dataIndex: 'debit_amount',
                  key: 'debit_amount',
                  render: (amount: number) => amount > 0 ? '₪' + amount.toFixed(2) : '-'
                },
                {
                  title: 'دائن',
                  dataIndex: 'credit_amount',
                  key: 'credit_amount',
                  render: (amount: number) => amount > 0 ? '₪' + amount.toFixed(2) : '-'
                }
              ]}
              dataSource={viewingEntry.details || []}
              rowKey="id"
              pagination={false}
              size="small"
              summary={() => (
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={2}>
                    <strong>الإجمالي</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={2}>
                    <strong>₪{viewingEntry.total_debit?.toFixed(2)}</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={3}>
                    <strong>₪{viewingEntry.total_credit?.toFixed(2)}</strong>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              )}
            />
          </div>
        )}
      </Modal>
    </div>
  )
}

export default JournalEntryManagement
