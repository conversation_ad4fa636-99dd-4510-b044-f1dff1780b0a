import React from 'react';
import { Tag, Typography, Progress, Tooltip } from 'antd';
import UniversalReport from './UniversalReport';
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger';
import type { ReportData, ReportType } from '../../types/reports';

const { Text } = Typography;

const SupplierPriceComparisonReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير مقارنة أسعار الموردين...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getSupplierPriceComparisonReport(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const priceComparisonData = response.data;

      // معالجة البيانات
      const processedData = priceComparisonData.map((item: any, index: number) => ({
        ...item,
        key: item.item_id || index,
        price_variance_percentage: item.avg_price > 0 ? ((item.price_variance / item.avg_price) * 100) : 0,
        savings_potential: item.price_variance * 100, // افتراض كمية شراء 100 وحدة
        price_stability: item.price_variance <= 5 ? 'مستقر' : item.price_variance <= 15 ? 'متوسط' : 'متقلب'
      }));

      // حساب الإحصائيات
      const totalItems = processedData.length;
      const avgPriceVariance = processedData.reduce((sum, item) => sum + item.price_variance, 0) / totalItems;
      const totalSavingsPotential = processedData.reduce((sum, item) => sum + item.savings_potential, 0);
      const highVarianceItems = processedData.filter(item => item.price_variance_percentage > 20).length;

      // تحديد الأعمدة
      const columns = [
        {
          title: 'كود الصنف',
          dataIndex: 'item_code',
          key: 'item_code',
          width: 120,
          align: 'center' as const
        },
        {
          title: 'اسم الصنف',
          dataIndex: 'item_name',
          key: 'item_name',
          width: 200,
          ellipsis: true
        },
        {
          title: 'الفئة',
          dataIndex: 'category_name',
          key: 'category_name',
          width: 120,
          align: 'center' as const
        },
        {
          title: 'أقل سعر',
          dataIndex: 'min_price',
          key: 'min_price',
          width: 120,
          align: 'center' as const,
          render: (price: number) => (
            <Text strong style={{ color: '#52c41a' }}>
              {price.toFixed(2)} ر.س
            </Text>
          )
        },
        {
          title: 'أعلى سعر',
          dataIndex: 'max_price',
          key: 'max_price',
          width: 120,
          align: 'center' as const,
          render: (price: number) => (
            <Text strong style={{ color: '#ff4d4f' }}>
              {price.toFixed(2)} ر.س
            </Text>
          )
        },
        {
          title: 'متوسط السعر',
          dataIndex: 'avg_price',
          key: 'avg_price',
          width: 120,
          align: 'center' as const,
          render: (price: number) => (
            <Text strong style={{ color: '#1890ff' }}>
              {price.toFixed(2)} ر.س
            </Text>
          )
        },
        {
          title: 'فرق السعر',
          dataIndex: 'price_variance',
          key: 'price_variance',
          width: 120,
          align: 'center' as const,
          render: (variance: number) => (
            <Text strong style={{ color: variance > 15 ? '#ff4d4f' : variance > 5 ? '#fa8c16' : '#52c41a' }}>
              {variance.toFixed(2)} ر.س
            </Text>
          )
        },
        {
          title: 'نسبة التباين',
          dataIndex: 'price_variance_percentage',
          key: 'price_variance_percentage',
          width: 150,
          align: 'center' as const,
          render: (percentage: number) => (
            <Progress
              percent={Math.min(Math.round(percentage), 100)}
              size="small"
              strokeColor={percentage >= 30 ? '#ff4d4f' : percentage >= 15 ? '#fa8c16' : '#52c41a'}
              format={(percent) => `${percentage.toFixed(1)}%`}
            />
          )
        },
        {
          title: 'عدد الموردين',
          dataIndex: 'supplier_count',
          key: 'supplier_count',
          width: 120,
          align: 'center' as const,
          render: (count: number) => (
            <Tag color={count >= 4 ? 'green' : count >= 2 ? 'blue' : 'orange'}>
              {count} مورد
            </Tag>
          )
        },
        {
          title: 'أفضل مورد (سعر)',
          dataIndex: 'min_price_supplier',
          key: 'min_price_supplier',
          width: 200,
          ellipsis: true,
          render: (supplier: string) => (
            <Tooltip title={supplier}>
              <Text style={{ color: '#52c41a' }}>{supplier}</Text>
            </Tooltip>
          )
        },
        {
          title: 'أعلى مورد (سعر)',
          dataIndex: 'max_price_supplier',
          key: 'max_price_supplier',
          width: 200,
          ellipsis: true,
          render: (supplier: string) => (
            <Tooltip title={supplier}>
              <Text style={{ color: '#ff4d4f' }}>{supplier}</Text>
            </Tooltip>
          )
        },
        {
          title: 'إمكانية التوفير',
          dataIndex: 'savings_potential',
          key: 'savings_potential',
          width: 140,
          align: 'center' as const,
          render: (savings: number) => (
            <Text strong style={{ color: '#52c41a' }}>
              {savings.toFixed(2)} ر.س
            </Text>
          )
        },
        {
          title: 'استقرار السعر',
          dataIndex: 'price_stability',
          key: 'price_stability',
          width: 120,
          align: 'center' as const,
          render: (stability: string) => {
            const stabilityConfig = {
              'مستقر': { color: 'green', text: 'مستقر' },
              'متوسط': { color: 'orange', text: 'متوسط' },
              'متقلب': { color: 'red', text: 'متقلب' }
            };
            const config = stabilityConfig[stability as keyof typeof stabilityConfig] || { color: 'default', text: stability };
            return <Tag color={config.color}>{config.text}</Tag>;
          }
        },
        {
          title: 'آخر تحديث (ميلادي)',
          dataIndex: 'last_updated',
          key: 'last_updated',
          width: 140,
          render: (date: string) => date ? DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE) : '-'
        }
      ];

      return {
        title: 'تقرير مقارنة أسعار الموردين',
        subtitle: `تم إنشاؤه في ${DateUtils.formatForDisplay(new Date().toISOString(), DATE_FORMATS.DISPLAY_DATE_TIME)}`,
        description: 'مقارنة شاملة لأسعار الأصناف عبر الموردين المختلفين مع تحليل الفروقات وإمكانيات التوفير',
        columns,
        data: processedData,
        statistics: [
          {
            title: 'إجمالي الأصناف',
            value: totalItems,
            color: '#1890ff',
            icon: 'ShoppingCartOutlined'
          },
          {
            title: 'متوسط فرق الأسعار',
            value: `${avgPriceVariance.toFixed(2)} ر.س`,
            color: '#fa8c16',
            icon: 'DollarOutlined'
          },
          {
            title: 'إجمالي التوفير المحتمل',
            value: `${totalSavingsPotential.toFixed(2)} ر.س`,
            color: '#52c41a',
            icon: 'SaveOutlined'
          },
          {
            title: 'أصناف عالية التباين',
            value: highVarianceItems,
            color: '#ff4d4f',
            icon: 'WarningOutlined'
          }
        ],
        summary: {
          'إجمالي الأصناف': totalItems,
          'متوسط فرق الأسعار': `${avgPriceVariance.toFixed(2)} ر.س`,
          'إجمالي التوفير المحتمل': `${totalSavingsPotential.toFixed(2)} ر.س`,
          'أصناف عالية التباين': highVarianceItems,
          'نسبة الأصناف المستقرة': `${((processedData.filter(item => item.price_stability === 'مستقر').length / totalItems) * 100).toFixed(1)}%`
        },
        metadata: {
          generatedAt: new Date().toISOString(),
          reportType: 'supplier_price_comparison' as ReportType,
          totalRecords: processedData.length,
          filters: filters,
          dateRange: filters.dateRange ? `${filters.dateRange[0]} إلى ${filters.dateRange[1]}` : 'جميع الفترات'
        }
      };

    } catch (error) {
      Logger.error('SupplierPriceComparisonReport', 'خطأ في إنشاء التقرير:', error);
      throw new Error('فشل في تحميل بيانات مقارنة أسعار الموردين');
    }
  };

  return (
    <UniversalReport
      reportType={'supplier_price_comparison' as ReportType}
      title="تقرير مقارنة أسعار الموردين"
      description="مقارنة شاملة لأسعار الأصناف عبر الموردين المختلفين مع تحليل الفروقات وإمكانيات التوفير"
      onGenerateReport={generateReport}
      showDateRange={true}
      showSupplierFilter={true}
      showCategoryFilter={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default SupplierPriceComparisonReport;
