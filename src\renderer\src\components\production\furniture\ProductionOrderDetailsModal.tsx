import React, { useState, useEffect } from 'react'
import {
  Mo<PERSON>,
  Card,
  Row,
  Col,
  Table,
  Tag,
  Descriptions,
  Button,
  Space,
  Divider,
  Typography,
  Statistic,
  Timeline,
  message,
  Spin
} from 'antd'
import { SafeLogger as Logger } from '../../../utils/logger'
import {
  PrinterOutlined,
  CloseOutlined,
  FileTextOutlined,
  ToolOutlined,
  ClockCircleOutlined,
  NumberOutlined,
  TagOutlined,
  BookOutlined
} from '@ant-design/icons'
import UnifiedPrintButton from '../../common/UnifiedPrintButton'
// تم حذف نظام الطباعة المعقد - استخدم window.print() للطباعة البسيطة
import dayjs from 'dayjs'

const { Text } = Typography

interface ProductionOrderDetailsModalProps {
  visible: boolean
  onClose: () => void
  orderId: number | null
}

interface OrderDetails {
  id: number
  order_number: string
  order_code: string
  item_id?: number
  item_name: string
  item_code: string
  department_name: string
  customer_name?: string
  order_date: string
  start_date?: string
  expected_completion_date?: string
  actual_completion_date?: string
  end_date?: string
  quantity: number
  unit: string
  status: string
  priority: string
  estimated_cost: number
  actual_cost: number
  estimated_hours: number
  actual_hours: number
  completion_percentage?: number
  labor_cost?: number
  overhead_cost?: number
  notes?: string
  created_by_name?: string
  updated_by_name?: string
  created_at: string
  updated_at?: string
  recipe_name?: string
  recipe_code?: string
  recipe_description?: string
}

interface OrderItem {
  item_name: string
  item_code: string
  quantity: number
  unit: string
  unit_cost?: number
  specifications?: string
  notes?: string
}

interface OrderMaterial {
  material_name: string
  material_code: string
  quantity: number
  required_quantity: number
  unit: string
  cost_per_unit: number
  unit_cost: number
  total_cost: number
  warehouse_name?: string
  available_quantity?: number
  notes?: string
}

interface OrderStage {
  stage_name: string
  stage_description: string
  sequence: number
  status: string
  start_date?: string
  end_date?: string
  assigned_to_name?: string
  actual_hours?: number
  description?: string
  estimated_duration?: number
  actual_duration?: number
  department_name?: string
  notes?: string
}

const ProductionOrderDetailsModal: React.FC<ProductionOrderDetailsModalProps> = ({
  visible,
  onClose,
  orderId
}) => {
  const [loading, setLoading] = useState(false)
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null)
  const [orderItems, setOrderItems] = useState<OrderItem[]>([])
  const [orderMaterials, setOrderMaterials] = useState<OrderMaterial[]>([])
  const [orderStages, setOrderStages] = useState<OrderStage[]>([])

  useEffect(() => {
    if (visible && orderId) {
      loadOrderDetails()
    }
  }, [visible, orderId])

  const loadOrderDetails = async () => {
    if (!orderId || !window.electronAPI) return

    setLoading(true)
    try {
      // جلب تفاصيل الأمر الأساسية
      const detailsResponse = await (window.electronAPI as any).getProductionOrderDetails(orderId)
      if (detailsResponse.success) {
        setOrderDetails(detailsResponse.data)
      }

      // جلب الأصناف
      const itemsResponse = await window.electronAPI.getProductionOrderItems(orderId)
      if (itemsResponse.success) {
        setOrderItems(itemsResponse.data)
      }

      // جلب المواد
      const materialsResponse = await window.electronAPI.getProductionOrderMaterials(orderId)
      if (materialsResponse.success) {
        setOrderMaterials(materialsResponse.data)
      }

      // جلب المراحل
      const stagesResponse = await window.electronAPI.getProductionOrderStagesDetails(orderId)
      if (stagesResponse.success) {
        setOrderStages(stagesResponse.data)
      }
    } catch (error) {
      Logger.error('ProductionOrderDetailsModal', 'خطأ في جلب تفاصيل أمر الإنتاج:', error)
      message.error('فشل في جلب تفاصيل أمر الإنتاج')
    } finally {
      setLoading(false)
    }
  }

  const handlePrint = async () => {
    if (!orderDetails) {
      message.error('لا توجد تفاصيل أمر إنتاج للطباعة')
      return null
    }

    try {
      // تحميل الصور المرفقة لأمر الإنتاج
      let orderImages: any[] = []
      try {
        if (window.electronAPI) {
          const imagesResult = await window.electronAPI.getProductionOrderImages?.(orderDetails.id)
          if (imagesResult?.success) {
            orderImages = imagesResult.data || []
          }
        }
      } catch (error) {
        Logger.warn('ProductionOrderDetailsModal', 'تعذر تحميل صور أمر الإنتاج:', error)
      }

      // تحميل صور الصنف المطلوب إنتاجه
      let itemImages: any[] = []
      try {
        if (window.electronAPI && orderDetails.item_id) {
          const itemImagesResult = await window.electronAPI.getItemImages?.(orderDetails.item_id)
          if (itemImagesResult?.success) {
            itemImages = itemImagesResult.data || []
          }
        }
      } catch (error) {
        Logger.warn('ProductionOrderDetailsModal', 'تعذر تحميل صور الصنف:', error)
      }

      // دمج جميع الصور
      const allImages = [
        ...orderImages.map((img: any) => ({
          ...img,
          category: 'صورة أمر الإنتاج',
          description: img.description || `صورة أمر الإنتاج ${orderDetails.order_number}`
        })),
        ...itemImages.map((img: any) => ({
          ...img,
          category: 'صورة المنتج',
          description: img.description || `صورة المنتج ${orderDetails.item_name}`
        }))
      ]

      // إنشاء بيانات الطباعة المحسنة
      const printData = {
        title: `أمر إنتاج رقم ${orderDetails.order_number}`,
        subtitle: `المنتج: ${orderDetails.item_name} - الكمية: ${orderDetails.quantity} - القسم: ${orderDetails.department_name}`,

        // إضافة بيانات العميل مع معلومات إضافية
        customer: {
          name: orderDetails.customer_name || 'إنتاج داخلي',
          department: orderDetails.department_name,
          orderDate: orderDetails.order_date ? new Date(orderDetails.order_date).toLocaleDateString('ar-SA') : '',
          deliveryDate: orderDetails.expected_completion_date ? new Date(orderDetails.expected_completion_date).toLocaleDateString('ar-SA') : '',
          type: 'عميل'
        },

        data: [orderDetails],

        // تفاصيل الأصناف المطلوبة
        items: (orderItems || []).map((item, index) => ({
          id: index + 1,
          name: item.item_name || 'غير محدد',
          code: item.item_code || '',
          description: item.specifications || item.notes || '',
          quantity: item.quantity || 0,
          unit: item.unit || 'قطعة',
          unitPrice: item.unit_cost || 0,
          discount: 0,
          tax: 0,
          total: (item.quantity || 0) * (item.unit_cost || 0)
        })),

        // تفاصيل المواد الخام المطلوبة
        materials: (orderMaterials || []).map((material, index) => ({
          id: index + 1,
          name: material.material_name || 'غير محدد',
          code: material.material_code || '',
          description: `${material.material_name} - ${material.warehouse_name || 'مخزن غير محدد'}`,
          quantity: material.required_quantity || 0,
          unit: material.unit || 'قطعة',
          unitPrice: material.unit_cost || 0,
          total: (material.required_quantity || 0) * (material.unit_cost || 0),
          warehouse: material.warehouse_name || 'غير محدد',
          available: material.available_quantity || 0,
          status: (material.available_quantity || 0) >= (material.required_quantity || 0) ? 'متوفر' : 'غير كافي'
        })),

        // مراحل الإنتاج
        stages: (orderStages || []).map((stage, index) => ({
          id: index + 1,
          name: stage.stage_name || 'غير محدد',
          description: stage.description || '',
          estimatedTime: stage.estimated_duration || 0,
          actualTime: stage.actual_duration || 0,
          status: stage.status || 'pending',
          department: stage.department_name || 'غير محدد',
          notes: stage.notes || ''
        })),

        // الصور المرفقة
        images: allImages,

        // الملخص المحسن
        summary: {
          orderNumber: orderDetails.order_number,
          orderDate: orderDetails.order_date,
          itemName: orderDetails.item_name,
          itemCode: orderDetails.item_code,
          totalQuantity: orderDetails.quantity,
          estimatedCost: orderDetails.estimated_cost || 0,
          actualCost: orderDetails.actual_cost || 0,
          status: orderDetails.status,
          priority: orderDetails.priority,
          department: orderDetails.department_name,
          startDate: orderDetails.start_date,
          endDate: orderDetails.end_date,
          completionPercentage: orderDetails.completion_percentage || 0,
          totalMaterials: orderMaterials?.length || 0,
          totalStages: orderStages?.length || 0,
          totalImages: allImages.length,
          // حساب التكاليف
          materialsCost: (orderMaterials || []).reduce((sum, m) => sum + ((m.required_quantity || 0) * (m.unit_cost || 0)), 0),
          laborCost: orderDetails.labor_cost || 0,
          overheadCost: orderDetails.overhead_cost || 0
        },

        metadata: {
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام الإنتاج',
          orderNumber: orderDetails.order_number,
          department: orderDetails.department_name,
          documentType: 'أمر إنتاج مفصل',
          version: '2.0',
          includesImages: allImages.length > 0,
          includesMaterials: (orderMaterials?.length || 0) > 0,
          includesStages: (orderStages?.length || 0) > 0
        }
      }

      Logger.info('ProductionOrderDetailsModal', '🖨️ بدء طباعة أمر الإنتاج المحسن:', {
        orderNumber: orderDetails.order_number,
        itemsCount: orderItems?.length || 0,
        materialsCount: orderMaterials?.length || 0,
        stagesCount: orderStages?.length || 0,
        imagesCount: allImages.length
      })

      return printData
    } catch (error) {
      Logger.error('ProductionOrderDetailsModal', 'خطأ في تحضير بيانات الطباعة:', error)
      message.error('حدث خطأ أثناء تحضير بيانات الطباعة')
      return null
    }
  }

  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      'pending': 'orange',
      'in_progress': 'blue',
      'completed': 'green',
      'cancelled': 'red',
      'on_hold': 'purple'
    }
    return colors[status] || 'default'
  }

  const getStatusText = (status: string) => {
    const texts: { [key: string]: string } = {
      'pending': 'في الانتّار',
      'in_progress': 'قيد التنفيذ',
      'completed': 'مكتمل',
      'cancelled': 'ملغي',
      'on_hold': 'معلق'
    }
    return texts[status] || status
  }

  const getPriorityColor = (priority: string) => {
    const colors: { [key: string]: string } = {
      'low': 'green',
      'normal': 'blue',
      'high': 'orange',
      'urgent': 'red'
    }
    return colors[priority] || 'default'
  }

  const getPriorityText = (priority: string) => {
    const texts: { [key: string]: string } = {
      'low': 'منخفضة',
      'normal': 'عادية',
      'high': 'عالية',
      'urgent': 'عاجلة'
    }
    return texts[priority] || priority
  }

  const itemsColumns = [
    {
      title: 'اسم الصنف',
      dataIndex: 'item_name',
      key: 'item_name',
    },
    {
      title: 'كود الصنف',
      dataIndex: 'item_code',
      key: 'item_code',
    },
    {
      title: 'الكمية',
      key: 'quantity',
      render: (record: OrderItem) => `${record.quantity} ${record.unit}`
    },
    {
      title: 'المواصفات',
      dataIndex: 'specifications',
      key: 'specifications',
      render: (text: string) => text || 'قياسية'
    },
    {
      title: 'ملاحّات',
      dataIndex: 'notes',
      key: 'notes',
      render: (text: string) => text || '-'
    }
  ]

  const materialsColumns = [
    {
      title: 'اسم المادة',
      dataIndex: 'material_name',
      key: 'material_name',
    },
    {
      title: 'كود المادة',
      dataIndex: 'material_code',
      key: 'material_code',
    },
    {
      title: 'الكمية المطلوبة',
      key: 'quantity',
      render: (record: OrderMaterial) => `${record.quantity} ${record.unit}`
    },
    {
      title: 'سعر الوحدة',
      dataIndex: 'cost_per_unit',
      key: 'cost_per_unit',
      render: (cost: number) => `₪${cost?.toFixed(2) || '0.00'}`
    },
    {
      title: 'التكلفة الإجمالية',
      dataIndex: 'total_cost',
      key: 'total_cost',
      render: (cost: number) => `₪${cost?.toFixed(2) || '0.00'}`
    },
    {
      title: 'ملاحّات',
      dataIndex: 'notes',
      key: 'notes',
      render: (text: string) => text || '-'
    }
  ]

  const totalMaterialsCost = orderMaterials.reduce((sum, material) => sum + (material.total_cost || 0), 0)

  return (
    <Modal
      title={
        <Space>
          <FileTextOutlined />
          <span>تفاصيل أمر الإنتاج</span>
          {orderDetails && (
            <Tag color="blue">{orderDetails.order_number}</Tag>
          )}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={[
        <Button key="close" icon={<CloseOutlined />} onClick={onClose}>
          إغلاق
        </Button>,
        orderDetails && (
          <UnifiedPrintButton
            key="print"
            data={handlePrint}
            type="order"
            subType="work"
            title="طباعة أمر الإنتاج مع التفاصيل الكاملة والصور"
            buttonText="طباعة الأمر"
            size="middle"
            buttonType="primary"
            showDropdown={true}
            showExportOptions={true}
            icon={<PrinterOutlined />}
            disabled={!orderDetails}
          />
        )
      ]}
    >
      <Spin spinning={loading}>
        {orderDetails && (
          <div>
            {/* معلومات الأمر والكود */}
            <Card
              title={
                <Space>
                  <NumberOutlined />
                  معلومات أمر الإنتاج
                </Space>
              }
              style={{ marginBottom: 16 }}
            >
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic
                    title="رقم الأمر"
                    value={orderDetails.order_number}
                    prefix={<NumberOutlined />}
                    valueStyle={{ color: '#1890ff', fontSize: '16px' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="كود الأمر"
                    value={orderDetails.order_code}
                    prefix={<TagOutlined />}
                    valueStyle={{ color: '#52c41a', fontSize: '16px' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="الحالة"
                    value={getStatusText(orderDetails.status)}
                    prefix={<Tag color={getStatusColor(orderDetails.status)} />}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="الأولوية"
                    value={getPriorityText(orderDetails.priority)}
                    prefix={<Tag color={getPriorityColor(orderDetails.priority)} />}
                  />
                </Col>
              </Row>
            </Card>

            {/* معلومات الصنف والوصفة */}
            <Card title="تفاصيل الصنف والوصفة" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="الصنف"
                    value={orderDetails.item_name}
                    prefix={<ToolOutlined />}
                  />
                  <Text type="secondary">{orderDetails.item_code}</Text>
                </Col>
                <Col span={8}>
                  <Statistic
                    title="الكمية المطلوبة"
                    value={`${orderDetails.quantity} ${orderDetails.unit}`}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="القسم"
                    value={orderDetails.department_name}
                  />
                </Col>
              </Row>

              {orderDetails.recipe_name && (
                <>
                  <Divider />
                  <Row gutter={16}>
                    <Col span={8}>
                      <Statistic
                        title="الوصفة المستخدمة"
                        value={orderDetails.recipe_name}
                        prefix={<BookOutlined />}
                      />
                      <Text type="secondary">{orderDetails.recipe_code}</Text>
                    </Col>
                    <Col span={16}>
                      <div>
                        <Text strong>وصف الوصفة:</Text>
                        <br />
                        <Text type="secondary">{orderDetails.recipe_description || 'لا يوجد وصف'}</Text>
                      </div>
                    </Col>
                  </Row>
                </>
              )}
            </Card>

            {/* معلومات التواريخ والتكاليف */}
            <Card title="التواريخ والتكاليف" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="العميل">
                      {orderDetails.customer_name || 'إنتاج داخلي'}
                    </Descriptions.Item>
                    <Descriptions.Item label="تاريخ الأمر">
                      {dayjs(orderDetails.order_date).format('YYYY-MM-DD')}
                    </Descriptions.Item>
                    <Descriptions.Item label="تاريخ البدء المتوقع">
                      {orderDetails.start_date ? dayjs(orderDetails.start_date).format('YYYY-MM-DD') : 'غير محدد'}
                    </Descriptions.Item>
                    <Descriptions.Item label="تاريخ الإنجاز المتوقع">
                      {orderDetails.expected_completion_date ? dayjs(orderDetails.expected_completion_date).format('YYYY-MM-DD') : 'غير محدد'}
                    </Descriptions.Item>
                  </Descriptions>
                </Col>
                <Col span={12}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="التكلفة المقدرة"
                        value={orderDetails.estimated_cost}
                        precision={2}
                        suffix="₪"
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="التكلفة الفعلية"
                        value={orderDetails.actual_cost}
                        precision={2}
                        suffix="₪"
                        valueStyle={{ color: orderDetails.actual_cost > orderDetails.estimated_cost ? '#ff4d4f' : '#52c41a' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="الساعات المقدرة"
                        value={orderDetails.estimated_hours}
                        suffix="ساعة"
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="الساعات الفعلية"
                        value={orderDetails.actual_hours}
                        suffix="ساعة"
                        valueStyle={{ color: orderDetails.actual_hours > orderDetails.estimated_hours ? '#ff4d4f' : '#52c41a' }}
                      />
                    </Col>
                  </Row>
                </Col>
              </Row>
            </Card>

            {/* الأصناف المطلوبة */}
            <Card title="الأصناف المطلوب إنتاجها" style={{ marginBottom: 16 }}>
              <Table
                columns={itemsColumns}
                dataSource={orderItems}
                rowKey="item_code"
                pagination={false}
                size="small"
              />
            </Card>

            {/* المواد المطلوبة */}
            <Card 
              title={
                <Space>
                  <span>المواد والأدوات المطلوبة</span>
                  <Tag color="green">إجمالي التكلفة: ₪{totalMaterialsCost.toFixed(2)}</Tag>
                </Space>
              } 
              style={{ marginBottom: 16 }}
            >
              <Table
                columns={materialsColumns}
                dataSource={orderMaterials}
                rowKey="material_code"
                pagination={false}
                size="small"
              />
            </Card>

            {/* التكاليف */}
            <Card title="ملخص التكاليف" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic
                    title="التكلفة المقدرة"
                    value={orderDetails.estimated_cost}
                    precision={2}
                    prefix="₪"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="التكلفة الفعلية"
                    value={orderDetails.actual_cost}
                    precision={2}
                    prefix="₪"
                    valueStyle={{ color: orderDetails.actual_cost > orderDetails.estimated_cost ? '#ff4d4f' : '#52c41a' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="الساعات المقدرة"
                    value={orderDetails.estimated_hours}
                    precision={1}
                    suffix="ساعة"
                    prefix={<ClockCircleOutlined />}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="الساعات الفعلية"
                    value={orderDetails.actual_hours}
                    precision={1}
                    suffix="ساعة"
                    prefix={<ClockCircleOutlined />}
                  />
                </Col>
              </Row>
            </Card>

            {/* مراحل الإنتاج */}
            {orderStages.length > 0 && (
              <Card title="مراحل الإنتاج" style={{ marginBottom: 16 }}>
                <Timeline
                  items={orderStages.map((stage, index) => ({
                    key: index,
                    color: stage.status === 'completed' ? 'green' : stage.status === 'in_progress' ? 'blue' : 'gray',
                    children: (
                      <div>
                        <Text strong>{stage.stage_name}</Text>
                        <br />
                        <Text type="secondary">{stage.stage_description}</Text>
                        {stage.assigned_to_name && (
                          <>
                            <br />
                            <Text>المكلف: {stage.assigned_to_name}</Text>
                          </>
                        )}
                        {stage.actual_hours && (
                          <>
                            <br />
                            <Text>الساعات المنجزة: {stage.actual_hours} ساعة</Text>
                          </>
                        )}
                      </div>
                    )
                  }))}
                />
              </Card>
            )}

            {/* ملاحّات */}
            {orderDetails.notes && (
              <Card title="ملاحّات خاصة">
                <Text>{orderDetails.notes}</Text>
              </Card>
            )}
          </div>
        )}
      </Spin>
    </Modal>
  )
}

export default ProductionOrderDetailsModal
