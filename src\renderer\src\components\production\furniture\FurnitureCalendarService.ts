import { SafeLogger as Logger } from '../../../utils/logger'
// خدمة التقويم لقسم إنتاج الأثاث
export interface FurnitureCalendarEvent {
  id: string
  title: string
  description?: string
  start: Date
  end: Date
  type: 'production_order' | 'deadline' | 'meeting' | 'maintenance' | 'custom'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
  orderId?: string
  orderNumber?: string
  departmentId?: string
  departmentName?: string
  assignedTo?: string[]
  location?: string
  color?: string
  isAllDay: boolean
  isRecurring: boolean
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'yearly'
    interval: number
    endDate?: Date
    daysOfWeek?: number[]
  }
  reminders?: {
    time: number // دقائق قبل الحدث
    method: 'notification' | 'email' | 'sms'
  }[]
  createdAt: Date
  updatedAt: Date
}

export interface FurnitureCalendarStats {
  totalEvents: number
  todayEvents: number
  thisWeekEvents: number
  thisMonthEvents: number
  upcomingDeadlines: number
  overdueEvents: number
  completedEvents: number
  eventsByType: Record<string, number>
  eventsByStatus: Record<string, number>
  eventsByPriority: Record<string, number>
}

class FurnitureCalendarService {
  private events: FurnitureCalendarEvent[] = []

  constructor() {
    this.loadEvents()
  }

  // تحميل الأحداث من التخزين المحلي
  private loadEvents(): void {
    try {
      const stored = localStorage.getItem('furniture_calendar_events')
      if (stored) {
        const parsed = JSON.parse(stored)
        this.events = parsed.map((event: any) => ({
          ...event,
          start: new Date(event.start),
          end: new Date(event.end),
          createdAt: new Date(event.createdAt),
          updatedAt: new Date(event.updatedAt),
          recurringPattern: event.recurringPattern ? {
            ...event.recurringPattern,
            endDate: event.recurringPattern.endDate ? new Date(event.recurringPattern.endDate) : undefined
          } : undefined
        }))
      }
    } catch (error) {
      Logger.error('FurnitureCalendarService', 'خطأ في تحميل أحداث التقويم:', error)
    }
  }

  // حفظ الأحداث في التخزين المحلي
  private saveEvents(): void {
    try {
      localStorage.setItem('furniture_calendar_events', JSON.stringify(this.events))
    } catch (error) {
      Logger.error('FurnitureCalendarService', 'خطأ في حفظ أحداث التقويم:', error)
    }
  }

  // إضافة حدث جديد
  addEvent(event: Omit<FurnitureCalendarEvent, 'id' | 'createdAt' | 'updatedAt'>): string {
    const id = `furniture_event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const newEvent: FurnitureCalendarEvent = {
      ...event,
      id,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    this.events.push(newEvent)
    this.saveEvents()
    return id
  }

  // إضافة حدث أمر إنتاج
  addProductionOrderEvent(
    orderId: string,
    orderNumber: string,
    title: string,
    startDate: Date,
    endDate: Date,
    departmentId?: string,
    departmentName?: string
  ): string {
    return this.addEvent({
      title: `أمر إنتاج: ${title}`,
      description: `أمر الإنتاج رقم ${orderNumber}`,
      start: startDate,
      end: endDate,
      type: 'production_order',
      priority: 'medium',
      status: 'scheduled',
      orderId,
      orderNumber,
      departmentId,
      departmentName,
      color: '#1890ff',
      isAllDay: false,
      isRecurring: false
    })
  }

  // إضافة حدث موعد نهائي
  addDeadlineEvent(
    orderId: string,
    orderNumber: string,
    deadline: Date,
    priority: 'low' | 'medium' | 'high' | 'urgent' = 'high'
  ): string {
    return this.addEvent({
      title: `موعد نهائي: ${orderNumber}`,
      description: `الموعد النهائي لأمر الإنتاج ${orderNumber}`,
      start: deadline,
      end: deadline,
      type: 'deadline',
      priority,
      status: 'scheduled',
      orderId,
      orderNumber,
      color: priority === 'urgent' ? '#ff4d4f' : '#fa8c16',
      isAllDay: true,
      isRecurring: false
    })
  }

  // تحديث حدث
  updateEvent(eventId: string, updates: Partial<FurnitureCalendarEvent>): boolean {
    const eventIndex = this.events.findIndex(e => e.id === eventId)
    if (eventIndex !== -1) {
      this.events[eventIndex] = {
        ...this.events[eventIndex],
        ...updates,
        updatedAt: new Date()
      }
      this.saveEvents()
      return true
    }
    return false
  }

  // حذف حدث
  deleteEvent(eventId: string): boolean {
    const initialLength = this.events.length
    this.events = this.events.filter(e => e.id !== eventId)
    if (this.events.length < initialLength) {
      this.saveEvents()
      return true
    }
    return false
  }

  // الحصول على جميع الأحداث
  getAllEvents(): FurnitureCalendarEvent[] {
    return [...this.events].sort((a, b) => a.start.getTime() - b.start.getTime())
  }

  // الحصول على أحداث فترة معينة
  getEventsByDateRange(startDate: Date, endDate: Date): FurnitureCalendarEvent[] {
    return this.events.filter(event => {
      return event.start <= endDate && event.end >= startDate
    })
  }

  // الحصول على أحداث يوم معين
  getEventsByDate(date: Date): FurnitureCalendarEvent[] {
    const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate())
    const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
    
    return this.getEventsByDateRange(startOfDay, endOfDay)
  }

  // الحصول على أحداث الأسبوع الحالي
  getThisWeekEvents(): FurnitureCalendarEvent[] {
    const now = new Date()
    const startOfWeek = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay())
    const endOfWeek = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay() + 6, 23, 59, 59)
    
    return this.getEventsByDateRange(startOfWeek, endOfWeek)
  }

  // الحصول على أحداث الشهر الحالي
  getThisMonthEvents(): FurnitureCalendarEvent[] {
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
    
    return this.getEventsByDateRange(startOfMonth, endOfMonth)
  }

  // الحصول على الأحداث القادمة
  getUpcomingEvents(days: number = 7): FurnitureCalendarEvent[] {
    const now = new Date()
    const futureDate = new Date(now.getTime() + (days * 24 * 60 * 60 * 1000))
    
    return this.getEventsByDateRange(now, futureDate)
  }

  // الحصول على الأحداث المتأخرة
  getOverdueEvents(): FurnitureCalendarEvent[] {
    const now = new Date()
    return this.events.filter(event => 
      event.end < now && 
      event.status !== 'completed' && 
      event.status !== 'cancelled'
    )
  }

  // الحصول على أحداث أمر إنتاج معين
  getEventsByOrderId(orderId: string): FurnitureCalendarEvent[] {
    return this.events.filter(event => event.orderId === orderId)
  }

  // الحصول على أحداث قسم معين
  getEventsByDepartment(departmentId: string): FurnitureCalendarEvent[] {
    return this.events.filter(event => event.departmentId === departmentId)
  }

  // الحصول على إحصائيات التقويم
  getCalendarStats(): FurnitureCalendarStats {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const _tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)

    const todayEvents = this.getEventsByDate(today)
    const thisWeekEvents = this.getThisWeekEvents()
    const thisMonthEvents = this.getThisMonthEvents()
    const upcomingDeadlines = this.events.filter(e => 
      e.type === 'deadline' && 
      e.start > now && 
      e.status !== 'completed'
    )
    const overdueEvents = this.getOverdueEvents()
    const completedEvents = this.events.filter(e => e.status === 'completed')

    // إحصائيات حسب النوع
    const eventsByType: Record<string, number> = {}
    this.events.forEach(event => {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1
    })

    // إحصائيات حسب الحالة
    const eventsByStatus: Record<string, number> = {}
    this.events.forEach(event => {
      eventsByStatus[event.status] = (eventsByStatus[event.status] || 0) + 1
    })

    // إحصائيات حسب الأولوية
    const eventsByPriority: Record<string, number> = {}
    this.events.forEach(event => {
      eventsByPriority[event.priority] = (eventsByPriority[event.priority] || 0) + 1
    })

    return {
      totalEvents: this.events.length,
      todayEvents: todayEvents.length,
      thisWeekEvents: thisWeekEvents.length,
      thisMonthEvents: thisMonthEvents.length,
      upcomingDeadlines: upcomingDeadlines.length,
      overdueEvents: overdueEvents.length,
      completedEvents: completedEvents.length,
      eventsByType,
      eventsByStatus,
      eventsByPriority
    }
  }

  // البحث في الأحداث
  searchEvents(query: string): FurnitureCalendarEvent[] {
    const lowerQuery = query.toLowerCase()
    return this.events.filter(event =>
      event.title.toLowerCase().includes(lowerQuery) ||
      event.description?.toLowerCase().includes(lowerQuery) ||
      event.orderNumber?.toLowerCase().includes(lowerQuery) ||
      event.departmentName?.toLowerCase().includes(lowerQuery)
    )
  }

  // تصدير الأحداث
  exportEvents(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['العنوان', 'الوصف', 'تاريخ البداية', 'تاريخ النهاية', 'النوع', 'الحالة', 'الأولوية']
      const rows = this.events.map(event => [
        event.title,
        event.description || '',
        event.start.toISOString(),
        event.end.toISOString(),
        event.type,
        event.status,
        event.priority
      ])
      
      return [headers, ...rows].map(row => row.join(',')).join('\n')
    }
    
    return JSON.stringify(this.events, null, 2)
  }

  // تنظيف الأحداث القديمة
  cleanup(daysOld: number = 90): void {
    const cutoffDate = new Date(Date.now() - (daysOld * 24 * 60 * 60 * 1000))
    this.events = this.events.filter(event => 
      event.createdAt > cutoffDate || 
      event.status !== 'completed'
    )
    this.saveEvents()
  }
}

export const furnitureCalendarService = new FurnitureCalendarService()
