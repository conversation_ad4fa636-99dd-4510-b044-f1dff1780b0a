import React, { Component, ReactNode } from 'react'
import { Result, Button, Typography, Card, Space, Alert } from 'antd'
import { ExclamationCircleOutlined, ReloadOutlined, HomeOutlined } from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'

const { Title, Paragraph } = Typography

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
}

/**
 * مكون Error Boundary محسن لمعالجة الأخطاء غير المتوقعة
 */
class ErrorBoundaryWrapper extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    }
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    }
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    Logger.error('ErrorBoundaryWrapper', `❌ Error Boundary caught an error: ${error.message}`)
    
    this.setState({
      error,
      errorInfo
    })

    // استدعاء callback إضافي إذا تم توفيره
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // إرسال تقرير الخطأ (يمكن إضافة خدمة تتبع الأخطاء هنا)
    this.reportError(error, errorInfo)
  }

  private reportError = (error: Error, errorInfo: React.ErrorInfo) => {
    // يمكن إضافة خدمة تتبع الأخطاء هنا مثل Sentry
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    Logger.error('ErrorBoundaryWrapper', '📊 Error Report:', errorReport)
    
    // حفّ التقرير في localStorage للمراجعة اللاحقة
    try {
      const existingReports = JSON.parse(localStorage.getItem('errorReports') || '[]')
      existingReports.push(errorReport)
      
      // الاحتفاّ بآخر 10 تقارير فقط
      if (existingReports.length > 10) {
        existingReports.splice(0, existingReports.length - 10)
      }
      
      localStorage.setItem('errorReports', JSON.stringify(existingReports))
    } catch (e) {
      Logger.error('ErrorBoundaryWrapper', 'فشل في حفّ تقرير الخطأ:', e)
    }
  }

  private handleReload = () => {
    window.location.reload()
  }

  private handleGoHome = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    })
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    })
  }

  override render() {
    if (this.state.hasError) {
      // إذا تم توفير fallback مخصص
      if (this.props.fallback) {
        return this.props.fallback
      }

      // عرض صفحة خطأ افتراضية
      return (
        <div style={{ padding: '50px', minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
          <Card style={{ maxWidth: 800, margin: '0 auto' }}>
            <Result
              status="error"
              icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
              title="حدث خطأ غير متوقع"
              subTitle="نعتذر، حدث خطأ في التطبيق. يرجى المحاولة مرة أخرى."
              extra={[
                <Space key="actions" size="middle">
                  <Button type="primary" icon={<ReloadOutlined />} onClick={this.handleRetry}>
                    إعادة المحاولة
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={this.handleReload}>
                    إعادة تحميل الصفحة
                  </Button>
                  <Button icon={<HomeOutlined />} onClick={this.handleGoHome}>
                    العودة للرئيسية
                  </Button>
                </Space>
              ]}
            />

            {/* تفاصيل الخطأ للمطورين */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <Alert
                message="تفاصيل الخطأ (وضع التطوير)"
                description={
                  <div style={{ marginTop: 16 }}>
                    <Title level={5}>رسالة الخطأ:</Title>
                    <Paragraph code copyable>
                      {this.state.error.message}
                    </Paragraph>
                    
                    <Title level={5}>Stack Trace:</Title>
                    <Paragraph code copyable style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                      {this.state.error.stack}
                    </Paragraph>
                    
                    {this.state.errorInfo && (
                      <>
                        <Title level={5}>Component Stack:</Title>
                        <Paragraph code copyable style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                          {this.state.errorInfo.componentStack}
                        </Paragraph>
                      </>
                    )}
                  </div>
                }
                type="warning"
                showIcon
                style={{ marginTop: 24, textAlign: 'left' }}
              />
            )}

            {/* نصائح للمستخدم */}
            <Alert
              message="نصائح لحل المشكلة"
              description={
                <ul style={{ margin: 0, paddingLeft: 20 }}>
                  <li>تأكد من اتصالك بالإنترنت</li>
                  <li>أغلق التطبيق وأعد فتحه</li>
                  <li>تأكد من أن لديك أحدث إصدار من التطبيق</li>
                  <li>إذا استمرت المشكلة، تواصل مع الدعم الفني</li>
                </ul>
              }
              type="info"
              showIcon
              style={{ marginTop: 16 }}
            />
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundaryWrapper
