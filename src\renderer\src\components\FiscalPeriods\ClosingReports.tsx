import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Table,
  Tabs,
  Space,
  Row,
  Col,
  Typography,
  Alert,
  Tag,
  Progress,
  Divider,
  Tooltip,
  message,
  Modal,
  Select,
  DatePicker,
  Statistic,
  Timeline,
  Badge,
  Descriptions,
  List,
  Avatar,
  Spin,
  Result,
  Drawer,
  Form,
  Input,
  Checkbox,
  Radio
} from 'antd';
import {
  PrinterOutlined,
  DownloadOutlined,
  MailOutlined,
  EyeOutlined,
  RiseOutlined,
  BarChartOutlined,
  BankOutlined,
  FileTextOutlined,
  CalendarOutlined,
  DollarOutlined,
  TrophyOutlined,
  LineChartOutlined,
  PieChartOutlined,
  FundOutlined,
  SafetyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
  ShareAltOutlined,
  CloudDownloadOutlined,
  FilePdfOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { FiscalPeriod, ClosingReport, PeriodComparison } from '../../types/fiscalPeriod';
import { fiscalPeriodApi } from '../../services/fiscalPeriodApi';
import FiscalClosingReport from '../reports/FiscalClosingReport';
import { UnifiedPrintButton } from '../common';

const { TabPane } = Tabs;
const { Title, Text } = Typography;
const { Option } = Select;

interface ClosingReportsProps {
  period: FiscalPeriod;
}

const ClosingReports: React.FC<ClosingReportsProps> = ({ period }) => {
  const [loading, setLoading] = useState(false);
  const [closingReport, setClosingReport] = useState<ClosingReport | null>(null);
  const [periodComparison, setPeriodComparison] = useState<PeriodComparison | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [exportDrawerVisible, setExportDrawerVisible] = useState(false);
  const [selectedReports, setSelectedReports] = useState<string[]>([]);
  const [exportFormat, setExportFormat] = useState('pdf');
  const [reportHistory, setReportHistory] = useState<any[]>([]);
  const [customReportVisible, setCustomReportVisible] = useState(false);
  const [printModalVisible, setPrintModalVisible] = useState(false);
  const [selectedReportType, setSelectedReportType] = useState('summary');

  useEffect(() => {
    if (period) {
      loadReports();
    }
  }, [period]);

  const loadReports = async () => {
    setLoading(true);
    setError(null);

    try {
      // تحميل تقرير الإقفال
      const reportResult = await fiscalPeriodApi.getClosingReport(period.id);
      if (reportResult.success) {
        setClosingReport(reportResult.data);
      }

      // تحميل مقارنة الفترات
      const comparisonResult = await fiscalPeriodApi.getPeriodComparison(period.id.toString());
      if (comparisonResult) {
        setPeriodComparison(comparisonResult);
      }

    } catch (error: any) {
      setError(error.message || 'فشل في تحميل التقارير');
    } finally {
      setLoading(false);
    }
  };

  // استخدام النظام الموحد للطباعة
  const handleUnifiedPrint = (reportType: string, reportData: any) => {
    return {
      title: `تقرير ${getReportTypeArabic(reportType)} - ${period.period_name}`,
      subtitle: `الفترة المالية: ${period.start_date} - ${period.end_date}`,
      data: reportData || [],
      summary: {
        periodName: period.period_name,
        periodType: period.period_type,
        status: period.status,
        generatedAt: new Date().toISOString()
      },
      metadata: {
        generatedAt: new Date().toISOString(),
        generatedBy: 'نظام إقفال السنة المالية',
        reportType: reportType,
        periodId: period.id
      }
    };
  };

  const getReportTypeArabic = (reportType: string): string => {
    const types = {
      'income_statement': 'قائمة الدخل',
      'balance_sheet': 'قائمة المركز المالي',
      'trial_balance': 'ميزان المراجعة',
      'cash_flow': 'قائمة التدفقات النقدية',
      'closing_summary': 'ملخص الإقفال',
      'closing_entries': 'قيود الإقفال',
      'audit_log': 'سجل التدقيق'
    };
    return types[reportType as keyof typeof types] || reportType;
  };

  const handlePrint = async (reportType: string) => {
    try {
      const result = await fiscalPeriodApi.getClosingReport(period.id);
      if (result.success) {
        message.success('تم إرسال التقرير للطباعة');
      } else {
        message.error(result.message || 'فشل في طباعة التقرير');
      }
    } catch (error: any) {
      message.error(error.message || 'حدث خطأ أثناء الطباعة');
    }
  };

  const handleExport = async (reportType: string, format: 'pdf' | 'excel') => {
    try {
      const result = await fiscalPeriodApi.getClosingReport(period.id);
      if (result.success) {
        message.success(`تم تصدير التقرير بصيغة ${format.toUpperCase()}`);
      } else {
        message.error(result.message || 'فشل في تصدير التقرير');
      }
    } catch (error: any) {
      message.error(error.message || 'حدث خطأ أثناء التصدير');
    }
  };

  // وظائف متقدمة جديدة
  const handleBulkExport = async () => {
    if (selectedReports.length === 0) {
      message.warning('يرجى اختيار التقارير المراد تصديرها');
      return;
    }

    setLoading(true);
    try {
      // محاكاة التصدير المجمع
      await new Promise(resolve => setTimeout(resolve, 3000));
      message.success(`تم تصدير ${selectedReports.length} تقرير بصيغة ${exportFormat.toUpperCase()} بنجاح`);
      setExportDrawerVisible(false);
      setSelectedReports([]);
    } catch (error) {
      message.error('فشل في التصدير المجمع');
    } finally {
      setLoading(false);
    }
  };

  const handleEmailReport = async (reportType: string) => {
    try {
      // محاكاة إرسال البريد الإلكتروني
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success(`تم إرسال ${reportType} بالبريد الإلكتروني بنجاح`);
    } catch (error) {
      message.error('فشل في إرسال البريد الإلكتروني');
    }
  };

  const generateCustomReport = async (config: any) => {
    setLoading(true);
    try {
      // محاكاة إنشاء تقرير مخصص
      await new Promise(resolve => setTimeout(resolve, 4000));
      message.success('تم إنشاء التقرير المخصص بنجاح');
      setCustomReportVisible(false);
    } catch (error) {
      message.error('فشل في إنشاء التقرير المخصص');
    } finally {
      setLoading(false);
    }
  };

  // مكون لوحة التحكم المتقدمة
  const renderDashboard = () => {
    if (!closingReport) return null;

    return (
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* إحصائيات سريعة */}
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Card>
              <Statistic
                title="إجمالي الإيرادات"
                value={closingReport.totalRevenue || 0}
                precision={2}
                prefix={<RiseOutlined style={{ color: '#52c41a' }} />}
                suffix="₪"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="إجمالي المصروفات"
                value={closingReport.totalExpenses || 0}
                precision={2}
                prefix={<BarChartOutlined style={{ color: '#ff4d4f' }} />}
                suffix="₪"
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="صافي الربح"
                value={closingReport.netIncome || 0}
                precision={2}
                prefix={<TrophyOutlined style={{ color: '#1890ff' }} />}
                suffix="₪"
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="إجمالي الأصول"
                value={closingReport.totalAssets || 0}
                precision={2}
                prefix={<BankOutlined style={{ color: '#722ed1' }} />}
                suffix="₪"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* مؤشرات الأداء */}
        {periodComparison && (
          <Card title="مؤشرات الأداء مقارنة بالفترة السابقة">
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Card size="small">
                  <Statistic
                    title="نمو الإيرادات"
                    value={periodComparison.revenueGrowth}
                    precision={1}
                    suffix="%"
                    prefix={periodComparison.revenueGrowth > 0 ?
                      <RiseOutlined style={{ color: '#52c41a' }} /> :
                      <BarChartOutlined style={{ color: '#ff4d4f' }} />}
                    valueStyle={{
                      color: periodComparison.revenueGrowth > 0 ? '#52c41a' : '#ff4d4f'
                    }}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Statistic
                    title="نمو الأرباح"
                    value={periodComparison.profitGrowth}
                    precision={1}
                    suffix="%"
                    prefix={periodComparison.profitGrowth > 0 ?
                      <RiseOutlined style={{ color: '#52c41a' }} /> :
                      <BarChartOutlined style={{ color: '#ff4d4f' }} />}
                    valueStyle={{
                      color: periodComparison.profitGrowth > 0 ? '#52c41a' : '#ff4d4f'
                    }}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Statistic
                    title="نمو الأصول"
                    value={periodComparison.assetGrowth}
                    precision={1}
                    suffix="%"
                    prefix={periodComparison.assetGrowth > 0 ?
                      <RiseOutlined style={{ color: '#52c41a' }} /> :
                      <BarChartOutlined style={{ color: '#ff4d4f' }} />}
                    valueStyle={{
                      color: periodComparison.assetGrowth > 0 ? '#52c41a' : '#ff4d4f'
                    }}
                  />
                </Card>
              </Col>
            </Row>
          </Card>
        )}

        {/* التقارير السريعة */}
        <Card title="التقارير السريعة">
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Card
                hoverable
                style={{ textAlign: 'center', position: 'relative' }}
              >
                <FileTextOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
                <div style={{ marginTop: 8, marginBottom: 12 }}>قائمة الدخل</div>
                <UnifiedPrintButton
                  data={handleUnifiedPrint('income_statement', closingReport?.period)}
                  type="report"
                  subType="fiscal_closing"
                  buttonText="طباعة"
                  size="small"
                  buttonType="primary"
                  showDropdown={true}

                />
              </Card>
            </Col>
            <Col span={6}>
              <Card
                hoverable
                style={{ textAlign: 'center', position: 'relative' }}
              >
                <BankOutlined style={{ fontSize: '32px', color: '#52c41a' }} />
                <div style={{ marginTop: 8, marginBottom: 12 }}>قائمة المركز المالي</div>
                <UnifiedPrintButton
                  data={handleUnifiedPrint('balance_sheet', closingReport?.period)}
                  type="report"
                  subType="fiscal_closing"
                  buttonText="طباعة"
                  size="small"
                  buttonType="primary"
                  showDropdown={true}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card
                hoverable
                style={{ textAlign: 'center', position: 'relative' }}
              >
                <BarChartOutlined style={{ fontSize: '32px', color: '#faad14' }} />
                <div style={{ marginTop: 8, marginBottom: 12 }}>ميزان المراجعة</div>
                <UnifiedPrintButton
                  data={handleUnifiedPrint('trial_balance', closingReport?.period)}
                  type="report"
                  subType="fiscal_closing"
                  buttonText="طباعة"
                  size="small"
                  buttonType="primary"
                  showDropdown={true}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card
                hoverable
                style={{ textAlign: 'center', position: 'relative' }}
              >
                <FundOutlined style={{ fontSize: '32px', color: '#722ed1' }} />
                <div style={{ marginTop: 8, marginBottom: 12 }}>قائمة التدفقات النقدية</div>
                <UnifiedPrintButton
                  data={handleUnifiedPrint('cash_flow', closingReport?.period)}
                  type="report"
                  subType="fiscal_closing"
                  buttonText="طباعة"
                  size="small"
                  buttonType="primary"
                  showDropdown={true}
                />
              </Card>
            </Col>
          </Row>
        </Card>
      </Space>
    );
  };

  const renderSummaryReport = () => {
    if (!closingReport) return null;

    return (
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* إحصائيات الإقفال */}
        <Card title="ملخص الإقفال">
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="إجمالي الإيرادات"
                value={closingReport.totalRevenue || 0}
                precision={2}
                prefix={<DollarOutlined />}
                suffix="ر.س"
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="إجمالي المصروفات"
                value={closingReport.totalExpenses || 0}
                precision={2}
                prefix={<DollarOutlined />}
                suffix="ر.س"
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="صافي الربح/الخسارة"
                value={(closingReport.totalRevenue || 0) - (closingReport.totalExpenses || 0)}
                precision={2}
                prefix={<RiseOutlined />}
                suffix="ر.س"
                valueStyle={{ 
                  color: ((closingReport.totalRevenue || 0) - (closingReport.totalExpenses || 0)) >= 0 ? '#3f8600' : '#cf1322' 
                }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="عدد القيود المنشأة"
                value={closingReport.entriesCreated || 0}
                prefix={<FileTextOutlined />}
              />
            </Col>
          </Row>
        </Card>

        {/* قيود الإقفال */}
        <Card title="قيود الإقفال">
          <Table
            dataSource={closingReport.closingEntries || []}
            columns={[
              {
                title: 'رقم القيد',
                dataIndex: 'entryNumber',
                key: 'entryNumber'
              },
              {
                title: 'التاريخ',
                dataIndex: 'date',
                key: 'date',
                render: (date) => new Date(date).toLocaleDateString('ar-SA')
              },
              {
                title: 'الوصف',
                dataIndex: 'description',
                key: 'description'
              },
              {
                title: 'المدين',
                dataIndex: 'debitAmount',
                key: 'debitAmount',
                render: (amount) => amount ? `${amount.toFixed(2)} ر.س` : '-'
              },
              {
                title: 'الدائن',
                dataIndex: 'creditAmount',
                key: 'creditAmount',
                render: (amount) => amount ? `${amount.toFixed(2)} ر.س` : '-'
              }
            ]}
            pagination={{ pageSize: 10 }}
            size="small"
          />
        </Card>

        {/* الأرصدة المرحلة */}
        <Card title="الأرصدة المرحلة للفترة التالية">
          <Table
            dataSource={closingReport.carriedForwardBalances || []}
            columns={[
              {
                title: 'رقم الحساب',
                dataIndex: 'accountCode',
                key: 'accountCode'
              },
              {
                title: 'اسم الحساب',
                dataIndex: 'accountName',
                key: 'accountName'
              },
              {
                title: 'الرصيد المرحل',
                dataIndex: 'balance',
                key: 'balance',
                render: (balance) => `${balance.toFixed(2)} ر.س`
              },
              {
                title: 'نوع الرصيد',
                dataIndex: 'balanceType',
                key: 'balanceType',
                render: (type) => (
                  <Tag color={type === 'debit' ? 'blue' : 'green'}>
                    {type === 'debit' ? 'مدين' : 'دائن'}
                  </Tag>
                )
              }
            ]}
            pagination={{ pageSize: 10 }}
            size="small"
          />
        </Card>
      </Space>
    );
  };

  const renderComparisonReport = () => {
    if (!periodComparison) return null;

    return (
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Card title="مقارنة الفترات المحاسبية">
          <Table
            dataSource={periodComparison.comparisonData || []}
            columns={[
              {
                title: 'البيان',
                dataIndex: 'item',
                key: 'item'
              },
              {
                title: 'الفترة الحالية',
                dataIndex: 'currentPeriod',
                key: 'currentPeriod',
                render: (value) => `${value.toFixed(2)} ر.س`
              },
              {
                title: 'الفترة السابقة',
                dataIndex: 'previousPeriod',
                key: 'previousPeriod',
                render: (value) => `${value.toFixed(2)} ر.س`
              },
              {
                title: 'الفرق',
                dataIndex: 'difference',
                key: 'difference',
                render: (value) => (
                  <span style={{ color: value >= 0 ? '#3f8600' : '#cf1322' }}>
                    {value >= 0 ? '+' : ''}{value.toFixed(2)} ر.س
                  </span>
                )
              },
              {
                title: 'نسبة التغيير',
                dataIndex: 'changePercentage',
                key: 'changePercentage',
                render: (percentage) => (
                  <Tag color={percentage >= 0 ? 'green' : 'red'}>
                    {percentage >= 0 ? '+' : ''}{percentage.toFixed(1)}%
                  </Tag>
                )
              }
            ]}
            pagination={false}
            size="small"
          />
        </Card>
      </Space>
    );
  };

  const renderAuditLog = () => {
    if (!closingReport?.auditLog) return null;

    return (
      <Card title="سجل المراجعة">
        <Table
          dataSource={closingReport.auditLog}
          columns={[
            {
              title: 'التاريخ والوقت',
              dataIndex: 'timestamp',
              key: 'timestamp',
              render: (timestamp) => new Date(timestamp).toLocaleString('ar-SA')
            },
            {
              title: 'المستخدم',
              dataIndex: 'userId',
              key: 'userId'
            },
            {
              title: 'العملية',
              dataIndex: 'action',
              key: 'action'
            },
            {
              title: 'التفاصيل',
              dataIndex: 'details',
              key: 'details'
            },
            {
              title: 'الحالة',
              dataIndex: 'status',
              key: 'status',
              render: (status) => (
                <Tag color={status === 'success' ? 'green' : 'red'}>
                  {status === 'success' ? 'نجح' : 'فشل'}
                </Tag>
              )
            }
          ]}
          pagination={{ pageSize: 10 }}
          size="small"
        />
      </Card>
    );
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }} size="large">
      {/* شريط الأدوات المتقدم */}
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              <FileTextOutlined style={{ marginRight: 8 }} />
              نظام التقارير المتقدم - {period.period_name}
            </Title>
            <Text type="secondary">
              تقارير شاملة ومتطورة لإقفال السنة المالية
            </Text>
          </Col>
          <Col>
            <Space size="large">
              <Button
                type="primary"
                icon={<CloudDownloadOutlined />}
                onClick={() => setExportDrawerVisible(true)}
                size="large"
              >
                تصدير متقدم
              </Button>
              <Button
                icon={<PrinterOutlined />}
                onClick={() => setPrintModalVisible(true)}
                size="large"
              >
                طباعة
              </Button>
              <Button
                icon={<ShareAltOutlined />}
                onClick={() => setCustomReportVisible(true)}
                size="large"
              >
                تقرير مخصص
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* رسائل الخطأ */}
      {error && (
        <Alert
          message="خطأ في تحميل التقارير"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
        />
      )}

      {/* التقارير */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <BarChartOutlined />
                لوحة التحكم
              </span>
            }
            key="dashboard"
          >
            {renderDashboard()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <FileTextOutlined />
                ملخص الإقفال
              </span>
            }
            key="summary"
          >
            {renderSummaryReport()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <LineChartOutlined />
                مقارنة الفترات
              </span>
            }
            key="comparison"
          >
            {renderComparisonReport()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <SafetyOutlined />
                سجل المراجعة
              </span>
            }
            key="audit"
          >
            {renderAuditLog()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <ClockCircleOutlined />
                سجل التقارير
              </span>
            }
            key="history"
          >
            <Card title="سجل التقارير المُنشأة">
              <Timeline>
                <Timeline.Item
                  dot={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                  color="green"
                >
                  <div>
                    <Text strong>تقرير الإقفال النهائي</Text>
                    <br />
                    <Text type="secondary">تم إنشاؤه في 15 يناير 2024 - 2.4 MB</Text>
                  </div>
                </Timeline.Item>
                <Timeline.Item
                  dot={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                  color="green"
                >
                  <div>
                    <Text strong>ميزان المراجعة</Text>
                    <br />
                    <Text type="secondary">تم إنشاؤه في 15 يناير 2024 - 1.8 MB</Text>
                  </div>
                </Timeline.Item>
                <Timeline.Item
                  dot={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                  color="green"
                >
                  <div>
                    <Text strong>قائمة الدخل</Text>
                    <br />
                    <Text type="secondary">تم إنشاؤه في 15 يناير 2024 - 1.2 MB</Text>
                  </div>
                </Timeline.Item>
              </Timeline>
            </Card>
          </TabPane>
        </Tabs>
      </Card>

      {/* نافذة الطباعة */}
      <Modal
        title="طباعة التقرير"
        open={printModalVisible}
        onCancel={() => setPrintModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setPrintModalVisible(false)}>
            إلغاء
          </Button>,
          <Button
            key="print"
            type="primary"
            icon={<PrinterOutlined />}
            onClick={() => {
              handlePrint(selectedReportType);
              setPrintModalVisible(false);
            }}
          >
            طباعة
          </Button>
        ]}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>نوع التقرير:</Text>
            <Select
              value={selectedReportType}
              onChange={setSelectedReportType}
              style={{ width: '100%', marginTop: 8 }}
            >
              <Option value="summary">ملخص الإقفال</Option>
              <Option value="comparison">مقارنة الفترات</Option>
              <Option value="audit">سجل المراجعة</Option>
              <Option value="complete">التقرير الكامل</Option>
            </Select>
          </div>
        </Space>
      </Modal>

      {/* مكون التصدير المتقدم */}
      <Drawer
        title="تصدير التقارير المتقدم"
        placement="right"
        width={400}
        open={exportDrawerVisible}
        onClose={() => setExportDrawerVisible(false)}
      >
        <Form layout="vertical">
          <Form.Item label="اختر التقارير">
            <Checkbox.Group
              value={selectedReports}
              onChange={setSelectedReports}
              style={{ width: '100%' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Checkbox value="income_statement">قائمة الدخل</Checkbox>
                <Checkbox value="balance_sheet">قائمة المركز المالي</Checkbox>
                <Checkbox value="trial_balance">ميزان المراجعة</Checkbox>
                <Checkbox value="cash_flow">قائمة التدفقات النقدية</Checkbox>
                <Checkbox value="closing_summary">ملخص الإقفال</Checkbox>
              </Space>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item label="صيغة التصدير">
            <Radio.Group value={exportFormat} onChange={(e) => setExportFormat(e.target.value)}>
              <Space direction="vertical">
                <Radio value="pdf">
                  <FilePdfOutlined style={{ color: '#ff4d4f' }} /> PDF
                </Radio>
                <Radio value="excel">
                  <FileExcelOutlined style={{ color: '#52c41a' }} /> Excel
                </Radio>
                <Radio value="word">
                  <FileWordOutlined style={{ color: '#1890ff' }} /> Word
                </Radio>
              </Space>
            </Radio.Group>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              block
              loading={loading}
              onClick={handleBulkExport}
              disabled={selectedReports.length === 0}
            >
              تصدير التقارير المحددة
            </Button>
          </Form.Item>
        </Form>
      </Drawer>

      {/* مودال التقرير المخصص */}
      <Modal
        title="إنشاء تقرير مخصص"
        open={customReportVisible}
        onCancel={() => setCustomReportVisible(false)}
        width={600}
        footer={[
          <Button key="cancel" onClick={() => setCustomReportVisible(false)}>
            إلغاء
          </Button>,
          <Button
            key="generate"
            type="primary"
            loading={loading}
            onClick={() => generateCustomReport({})}
          >
            إنشاء التقرير
          </Button>
        ]}
      >
        <Form layout="vertical">
          <Form.Item label="اسم التقرير">
            <Input placeholder="أدخل اسم التقرير المخصص" />
          </Form.Item>
          <Form.Item label="العناصر المطلوبة">
            <Checkbox.Group style={{ width: '100%' }}>
              <Row>
                <Col span={12}><Checkbox value="revenue">الإيرادات</Checkbox></Col>
                <Col span={12}><Checkbox value="expenses">المصروفات</Checkbox></Col>
                <Col span={12}><Checkbox value="assets">الأصول</Checkbox></Col>
                <Col span={12}><Checkbox value="liabilities">الخصوم</Checkbox></Col>
                <Col span={12}><Checkbox value="equity">حقوق الملكية</Checkbox></Col>
                <Col span={12}><Checkbox value="ratios">النسب المالية</Checkbox></Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  );
};

export default ClosingReports;
