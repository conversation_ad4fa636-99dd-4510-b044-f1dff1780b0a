import { DatabaseService } from './DatabaseService'
import { Logger } from '../utils/logger'

export interface StoredTemplate {
  id: string
  name: string
  description?: string
  type: 'invoice' | 'report' | 'document' | 'custom'
  category?: string
  is_default: boolean
  is_custom: boolean
  is_active: boolean
  template_data: string
  preview_image?: string
  created_at: string
  updated_at: string
  created_by?: number
}

export interface TemplateFilter {
  type?: string
  category?: string
  is_default?: boolean
  is_custom?: boolean
  is_active?: boolean
  search?: string
}

export class TemplateStorageService {
  private static instance: TemplateStorageService
  private db: DatabaseService
  private isInitialized = false

  private constructor() {
    this.db = DatabaseService.getInstance()
  }

  public static getInstance(): TemplateStorageService {
    if (!TemplateStorageService.instance) {
      TemplateStorageService.instance = new TemplateStorageService()
    }
    return TemplateStorageService.instance
  }

  /**
   * تهيئة الخدمة والتأكد من وجود الجدول
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      await this.ensureTableExists()
      this.isInitialized = true
      Logger.info('TemplateStorageService', '✅ تم تهيئة خدمة قوالب الطباعة بنجاح')
    } catch (error) {
      Logger.error('TemplateStorageService', '❌ خطأ في تهيئة خدمة قوالب الطباعة:', error)
      throw error
    }
  }

  /**
   * التأكد من وجود جدول قوالب الطباعة
   */
  private async ensureTableExists(): Promise<void> {
    try {
      const database = this.db.getDatabase()

      // التحقق من وجود الجدول
      const tableExists = database.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='print_templates'
      `).get()

      if (!tableExists) {
        Logger.warn('TemplateStorageService', 'جدول print_templates غير موجود، سيتم إنشاؤه...')

        // إنشاء الجدول إذا لم يكن موجوداً
        database.exec(`
          CREATE TABLE IF NOT EXISTS print_templates (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            type TEXT NOT NULL CHECK (type IN ('invoice', 'receipt', 'report', 'certificate', 'custom', 'image')),
            category TEXT,
            is_default INTEGER DEFAULT 0,
            is_custom INTEGER DEFAULT 1,
            is_active INTEGER DEFAULT 1,
            template_data TEXT NOT NULL,
            preview_image TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER
          )
        `)

        // إنشاء الفهارس
        const indexes = [
          'CREATE INDEX IF NOT EXISTS idx_print_templates_type ON print_templates(type)',
          'CREATE INDEX IF NOT EXISTS idx_print_templates_category ON print_templates(category)',
          'CREATE INDEX IF NOT EXISTS idx_print_templates_active ON print_templates(is_active)',
          'CREATE INDEX IF NOT EXISTS idx_print_templates_default ON print_templates(is_default)',
          'CREATE INDEX IF NOT EXISTS idx_print_templates_custom ON print_templates(is_custom)'
        ]

        indexes.forEach(indexSql => {
          try {
            database.exec(indexSql)
          } catch (error) {
            Logger.warn('TemplateStorageService', 'تحذير في إنشاء فهرس:', error)
          }
        })

        Logger.info('TemplateStorageService', '✅ تم إنشاء جدول print_templates والفهارس')
      }
    } catch (error) {
      Logger.error('TemplateStorageService', 'خطأ في التأكد من وجود الجدول:', error)
      throw error
    }
  }

  /**
   * حفظ قالب جديد أو تحديث موجود
   */
  public async saveTemplate(template: Omit<StoredTemplate, 'created_at' | 'updated_at'>): Promise<boolean> {
    try {
      // التأكد من التهيئة
      await this.initialize()

      const database = this.db.getDatabase()
      
      // التحقق من وجود القالب
      const existing = database.prepare('SELECT id FROM print_templates WHERE id = ?').get(template.id)
      
      if (existing) {
        // تحديث القالب الموجود
        const result = database.prepare(`
          UPDATE print_templates 
          SET name = ?, description = ?, type = ?, category = ?, 
              is_default = ?, is_custom = ?, is_active = ?, 
              template_data = ?, preview_image = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).run([
          template.name,
          template.description || null,
          template.type,
          template.category || null,
          template.is_default ? 1 : 0,
          template.is_custom ? 1 : 0,
          template.is_active ? 1 : 0,
          template.template_data,
          template.preview_image || null,
          template.id
        ])
        
        Logger.success('TemplateStorageService', `تم تحديث القالب: ${template.name}`)
        return result.changes > 0
      } else {
        // إنشاء قالب جديد
        const result = database.prepare(`
          INSERT INTO print_templates 
          (id, name, description, type, category, is_default, is_custom, is_active, template_data, preview_image, created_by)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run([
          template.id,
          template.name,
          template.description || null,
          template.type,
          template.category || null,
          template.is_default ? 1 : 0,
          template.is_custom ? 1 : 0,
          template.is_active ? 1 : 0,
          template.template_data,
          template.preview_image || null,
          template.created_by || null
        ])
        
        Logger.success('TemplateStorageService', `تم حفظ القالب الجديد: ${template.name}`)
        return result.changes > 0
      }
    } catch (error) {
      Logger.error('TemplateStorageService', 'خطأ في حفظ القالب:', error)
      return false
    }
  }

  /**
   * جلب قالب بواسطة المعرف
   */
  public async getTemplate(id: string): Promise<StoredTemplate | null> {
    try {
      // التأكد من التهيئة
      await this.initialize()

      const database = this.db.getDatabase()
      const template = database.prepare('SELECT * FROM print_templates WHERE id = ? AND is_active = 1').get(id)
      
      if (template) {
        return {
          ...template,
          is_default: Boolean(template.is_default),
          is_custom: Boolean(template.is_custom),
          is_active: Boolean(template.is_active)
        }
      }
      
      return null
    } catch (error) {
      Logger.error('TemplateStorageService', 'خطأ في جلب القالب:', error)
      return null
    }
  }

  /**
   * جلب جميع القوالب مع التصفية
   */
  public async getTemplates(filter?: TemplateFilter): Promise<StoredTemplate[]> {
    try {
      // التأكد من التهيئة
      await this.initialize()

      const database = this.db.getDatabase()
      let sql = 'SELECT * FROM print_templates WHERE 1=1'
      const params: any[] = []

      if (filter) {
        if (filter.type) {
          sql += ' AND type = ?'
          params.push(filter.type)
        }
        if (filter.category) {
          sql += ' AND category = ?'
          params.push(filter.category)
        }
        if (filter.is_default !== undefined) {
          sql += ' AND is_default = ?'
          params.push(filter.is_default ? 1 : 0)
        }
        if (filter.is_custom !== undefined) {
          sql += ' AND is_custom = ?'
          params.push(filter.is_custom ? 1 : 0)
        }
        if (filter.is_active !== undefined) {
          sql += ' AND is_active = ?'
          params.push(filter.is_active ? 1 : 0)
        }
        if (filter.search) {
          sql += ' AND (name LIKE ? OR description LIKE ?)'
          params.push(`%${filter.search}%`, `%${filter.search}%`)
        }
      }

      sql += ' ORDER BY is_default DESC, name ASC'

      const templates = database.prepare(sql).all(params)
      
      return templates.map((template: any) => ({
        ...template,
        is_default: Boolean(template.is_default),
        is_custom: Boolean(template.is_custom),
        is_active: Boolean(template.is_active)
      }))
    } catch (error) {
      Logger.error('TemplateStorageService', 'خطأ في جلب القوالب:', error)
      return []
    }
  }

  /**
   * حذف قالب
   */
  public async deleteTemplate(id: string): Promise<boolean> {
    try {
      const database = this.db.getDatabase()
      
      // التحقق من أن القالب ليس افتراضي
      const template = database.prepare('SELECT is_default FROM print_templates WHERE id = ?').get(id)
      if (template && template.is_default) {
        Logger.warn('TemplateStorageService', 'لا يمكن حذف القالب الافتراضي')
        return false
      }
      
      const result = database.prepare('DELETE FROM print_templates WHERE id = ? AND is_default = 0').run(id)
      
      Logger.success('TemplateStorageService', `تم حذف القالب: ${id}`)
      return result.changes > 0
    } catch (error) {
      Logger.error('TemplateStorageService', 'خطأ في حذف القالب:', error)
      return false
    }
  }

  /**
   * تعطيل/تفعيل قالب
   */
  public async toggleTemplate(id: string, isActive: boolean): Promise<boolean> {
    try {
      const database = this.db.getDatabase()
      const result = database.prepare('UPDATE print_templates SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?').run([
        isActive ? 1 : 0,
        id
      ])
      
      Logger.success('TemplateStorageService', `تم ${isActive ? 'تفعيل' : 'تعطيل'} القالب: ${id}`)
      return result.changes > 0
    } catch (error) {
      Logger.error('TemplateStorageService', 'خطأ في تغيير حالة القالب:', error)
      return false
    }
  }

  /**
   * تكرار قالب
   */
  public async duplicateTemplate(id: string, newName: string): Promise<string | null> {
    try {
      const template = await this.getTemplate(id)
      if (!template) {
        return null
      }

      const newId = `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const newTemplate: Omit<StoredTemplate, 'created_at' | 'updated_at'> = {
        ...template,
        id: newId,
        name: newName,
        is_default: false,
        is_custom: true
      }

      const success = await this.saveTemplate(newTemplate)
      return success ? newId : null
    } catch (error) {
      Logger.error('TemplateStorageService', 'خطأ في تكرار القالب:', error)
      return null
    }
  }

  /**
   * تصدير قالب إلى JSON
   */
  public async exportTemplate(id: string): Promise<string | null> {
    try {
      const template = await this.getTemplate(id)
      if (!template) {
        return null
      }

      return JSON.stringify(template, null, 2)
    } catch (error) {
      Logger.error('TemplateStorageService', 'خطأ في تصدير القالب:', error)
      return null
    }
  }

  /**
   * استيراد قالب من JSON
   */
  public async importTemplate(jsonData: string): Promise<string | null> {
    try {
      const template = JSON.parse(jsonData) as StoredTemplate
      
      // إنشاء معرف جديد
      const newId = `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      template.id = newId
      template.is_custom = true
      template.is_default = false

      const success = await this.saveTemplate(template)
      return success ? newId : null
    } catch (error) {
      Logger.error('TemplateStorageService', 'خطأ في استيراد القالب:', error)
      return null
    }
  }

  /**
   * إحصائيات القوالب
   */
  public async getTemplateStats(): Promise<{
    total: number
    default: number
    custom: number
    active: number
    inactive: number
  }> {
    try {
      const database = this.db.getDatabase()
      
      const total = database.prepare('SELECT COUNT(*) as count FROM print_templates').get()?.count || 0
      const defaultCount = database.prepare('SELECT COUNT(*) as count FROM print_templates WHERE is_default = 1').get()?.count || 0
      const custom = database.prepare('SELECT COUNT(*) as count FROM print_templates WHERE is_custom = 1').get()?.count || 0
      const active = database.prepare('SELECT COUNT(*) as count FROM print_templates WHERE is_active = 1').get()?.count || 0
      const inactive = database.prepare('SELECT COUNT(*) as count FROM print_templates WHERE is_active = 0').get()?.count || 0

      return {
        total,
        default: defaultCount,
        custom,
        active,
        inactive
      }
    } catch (error) {
      Logger.error('TemplateStorageService', 'خطأ في جلب إحصائيات القوالب:', error)
      return {
        total: 0,
        default: 0,
        custom: 0,
        active: 0,
        inactive: 0
      }
    }
  }
}
