import { ipcMain } from 'electron'
import { Logger } from '../utils/logger'

// معالجات طوارئ للدفعات
let financialService: any = null

export function setEmergencyFinancialService(service: any) {
  financialService = service
  Logger.info('EmergencyHandlers', '🚨 تم تعيين الخدمة المالية للمعالجات الطارئة')
}

export function registerEmergencyHandlers() {
  Logger.info('EmergencyHandlers', '🚨 تسجيل المعالجات الطارئة...')

  // معالج طوارئ لدفعات العملاء
  ipcMain.handle('emergency-create-customer-payment', async (_, paymentData: any) => {
    try {
      Logger.info('EmergencyHandlers', '🚨 استدعاء معالج الدفعة الطارئ')
      
      if (!financialService) {
        Logger.error('EmergencyHandlers', 'الخدمة المالية غير متاحة')
        return { success: false, message: 'الخدمة المالية غير متاحة' }
      }

      Logger.info('EmergencyHandlers', 'بيانات الدفعة:', paymentData)

      // إنشاء سند قبض
      const voucherData = {
        voucher_number: paymentData.payment_number,
        payer_name: paymentData.customer_name || 'عميل',
        amount: paymentData.amount,
        receipt_date: paymentData.payment_date,
        payment_method: paymentData.payment_method || 'cash',
        bank_account_id: paymentData.bank_account_id,
        check_number: paymentData.check_number,
        notes: paymentData.notes,
        created_by: paymentData.created_by || 1
      }

      Logger.info('EmergencyHandlers', 'بيانات السند:', voucherData)
      const voucherResult = await financialService.createReceiptVoucher(voucherData)
      Logger.info('EmergencyHandlers', 'نتيجة إنشاء السند:', voucherResult)

      if (voucherResult.success && paymentData.invoice_id) {
        // ربط الدفعة بالفاتورة
        const linkData = {
          invoice_type: paymentData.invoice_type || 'sales_invoice',
          invoice_id: paymentData.invoice_id,
          payment_type: 'receipt_voucher',
          payment_id: voucherResult.data.voucherId,
          amount: paymentData.amount
        }

        Logger.info('EmergencyHandlers', 'بيانات الربط:', linkData)
        const linkResult = await financialService.linkInvoiceToPayment(linkData)
        Logger.info('EmergencyHandlers', 'نتيجة الربط:', linkResult)

        if (linkResult.success) {
          Logger.info('EmergencyHandlers', 'تم إنشاء الدفعة وربطها بنجاح')
          return {
            success: true,
            message: 'تم إنشاء دفعة العميل وربطها بالفاتورة بنجاح',
            data: {
              voucherId: voucherResult.data.voucherId,
              linkData: linkResult.data
            }
          }
        } else {
          Logger.error('EmergencyHandlers', 'فشل في ربط الدفعة بالفاتورة:', linkResult.message)
          return {
            success: false,
            message: 'تم إنشاء الدفعة ولكن فشل في ربطها بالفاتورة: ' + linkResult.message
          }
        }
      }

      Logger.info('EmergencyHandlers', 'إرجاع نتيجة السند فقط')
      return voucherResult
    } catch (error) {
      Logger.error('EmergencyHandlers', 'خطأ في معالج الدفعة الطارئ:', error)
      return { 
        success: false, 
        message: 'حدث خطأ في إنشاء دفعة العميل: ' + (error instanceof Error ? error.message : String(error)) 
      }
    }
  })

  Logger.info('EmergencyHandlers', '✅ تم تسجيل المعالجات الطارئة بنجاح')
}
