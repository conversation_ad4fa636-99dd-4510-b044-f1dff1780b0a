import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Space,
  message,
  Popconfirm,
  Tag,
  Row,
  Col,
  Statistic,
  Typography,
  TreeSelect
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ArrowLeftOutlined,
  BankOutlined,
  BarcodeOutlined,
  AccountBookOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'

const { Title } = Typography
const { Option } = Select

interface Account {
  id: number
  account_code: string
  account_name: string
  account_type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense'
  parent_id?: number
  level: number
  is_active: boolean
  balance: number
  created_at: string
  updated_at: string
}

interface ChartOfAccountsManagementProps {
  onBack: () => void
}

const ChartOfAccountsManagement: React.FC<ChartOfAccountsManagementProps> = ({ onBack }) => {
  const [accounts, setAccounts] = useState<Account[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingAccount, setEditingAccount] = useState<Account | null>(null)
  const [form] = Form.useForm()

  useEffect(() => {
    loadAccounts()
  }, [])

  const loadAccounts = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.getChartOfAccounts()
      if (response.success && response.data) {
        setAccounts(response.data)
      } else {
        message.error('فشل في تحميل دليل الحسابات')
      }
    } catch (error) {
      Logger.error('ChartOfAccountsManagement', 'خطأ في تحميل دليل الحسابات:', error)
      message.error('خطأ في تحميل دليل الحسابات')
    }
    setLoading(false)
  }

  // دالة إنشاء الكود التلقائي
  const generateCode = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generateAccountCode()
        if (response.success && response.data) {
          form.setFieldsValue({ account_code: response.data.code })
          message.success('تم إنشاء كود الحساب تلقائياً')
        } else {
          message.error(response.message || 'فشل في إنشاء كود الحساب')
        }
      }
    } catch (error) {
      Logger.error('ChartOfAccountsManagement', 'خطأ في إنشاء كود الحساب:', error)
      message.error('فشل في إنشاء كود الحساب')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      let result
      if (editingAccount) {
        result = await window.electronAPI.updateAccount(editingAccount.id, values)
      } else {
        result = await window.electronAPI.createAccount(values)
      }

      if (result.success) {
        message.success(editingAccount ? 'تم تحديث الحساب بنجاح' : 'تم إضافة الحساب بنجاح')
        loadAccounts()
        setModalVisible(false)
        setEditingAccount(null)
        form.resetFields()
      } else {
        message.error(result.message || 'فشل في حفّ الحساب')
      }
    } catch (error) {
      Logger.error('ChartOfAccountsManagement', 'خطأ في حفّ الحساب:', error)
      message.error('خطأ في حفّ الحساب')
    }
  }

  const handleAdd = () => {
    setEditingAccount(null)
    form.resetFields()
    form.setFieldsValue({ 
      is_active: true,
      level: 1,
      account_type: 'asset'
    })
    setModalVisible(true)
  }

  const handleEdit = (account: Account) => {
    setEditingAccount(account)
    form.setFieldsValue(account)
    setModalVisible(true)
  }

  const handleDelete = async (accountId: number) => {
    try {
      const response = await window.electronAPI.deleteAccount(accountId)
      if (response.success) {
        message.success('تم حذف الحساب بنجاح')
        loadAccounts()
      } else {
        message.error(response.message || 'فشل في حذف الحساب')
      }
    } catch (error) {
      Logger.error('ChartOfAccountsManagement', 'خطأ في حذف الحساب:', error)
      message.error('خطأ في حذف الحساب')
    }
  }

  const getAccountTypeColor = (type: string) => {
    const colors = {
      asset: 'blue',
      liability: 'red',
      equity: 'green',
      revenue: 'orange',
      expense: 'purple'
    }
    return colors[type as keyof typeof colors] || 'default'
  }

  const getAccountTypeText = (type: string) => {
    const types = {
      asset: 'أصول',
      liability: 'خصوم',
      equity: 'حقوق الملكية',
      revenue: 'إيرادات',
      expense: 'مصروفات'
    }
    return types[type as keyof typeof types] || type
  }

  const getParentAccounts = () => {
    return accounts.filter(account => account.level === 1).map(account => ({
      value: account.id,
      label: account.account_code + ' - ' + account.account_name
    }))
  }

  const getAccountStats = () => {
    const total = accounts.length
    const active = accounts.filter(a => a.is_active).length
    const inactive = total - active
    const totalBalance = accounts.reduce((sum, a) => sum + (a.balance || 0), 0)

    const byType = {
      asset: accounts.filter(a => a.account_type === 'asset').length,
      liability: accounts.filter(a => a.account_type === 'liability').length,
      equity: accounts.filter(a => a.account_type === 'equity').length,
      revenue: accounts.filter(a => a.account_type === 'revenue').length,
      expense: accounts.filter(a => a.account_type === 'expense').length
    }

    return { total, active, inactive, totalBalance, byType }
  }

  const stats = getAccountStats()

  const columns: ColumnsType<Account> = [
    {
      title: 'كود الحساب',
      dataIndex: 'account_code',
      key: 'account_code',
      width: 120,
      fixed: 'left'
    },
    {
      title: 'اسم الحساب',
      dataIndex: 'account_name',
      key: 'account_name',
      width: 200
    },
    {
      title: 'نوع الحساب',
      dataIndex: 'account_type',
      key: 'account_type',
      width: 120,
      render: (type: string) => (
        <Tag color={getAccountTypeColor(type)}>
          {getAccountTypeText(type)}
        </Tag>
      )
    },
    {
      title: 'المستوى',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      render: (level: number) => (
        <Tag color="cyan">{level}</Tag>
      )
    },
    {
      title: 'الرصيد',
      dataIndex: 'balance',
      key: 'balance',
      width: 120,
      render: (balance: number) => (
        <span style={{ color: balance >= 0 ? '#52c41a' : '#ff4d4f' }}>
          {balance?.toLocaleString() || 0} ₪
        </span>
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'نشط' : 'غير نشط'}
        </Tag>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Popconfirm
            title="هل أنت متأكد من حذف هذا الحساب؟"
            onConfirm={() => handleDelete(record.id)}
            okText="نعم"
            cancelText="لا"
          >
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
            <AccountBookOutlined /> دليل الحسابات
          </Title>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            إدارة دليل الحسابات والهيكل المحاسبي
          </p>
        </div>
        <Button 
          type="default" 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
        >
          العودة
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي الحسابات"
              value={stats.total}
              valueStyle={{ color: '#1890ff' }}
              prefix={<AccountBookOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="الحسابات النشطة"
              value={stats.active}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي الأرصدة"
              value={stats.totalBalance}
              valueStyle={{ color: stats.totalBalance >= 0 ? '#52c41a' : '#ff4d4f' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card>
            <Statistic
              title="الأصول"
              value={stats.byType.asset}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card>
            <Statistic
              title="الخصوم"
              value={stats.byType.liability}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card>
            <Statistic
              title="الإيرادات"
              value={stats.byType.revenue}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleAdd}
              size="large"
              block
            >
              إضافة حساب
            </Button>
          </Card>
        </Col>
      </Row>

      <Card title="قائمة الحسابات">
        <Table
          columns={columns}
          dataSource={accounts}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 15 }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* نموذج إضافة/تعديل الحساب */}
      <Modal
        title={editingAccount ? 'تعديل الحساب' : 'إضافة حساب جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setEditingAccount(null)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="account_code"
                label="كود الحساب"
                rules={[{ required: true, message: 'يرجى إدخال كود الحساب' }]}
              >
                <Input
                  placeholder="أدخل كود الحساب"
                  addonAfter={
                    <Button
                      size="small"
                      onClick={generateCode}
                      icon={<BarcodeOutlined />}
                    >
                      إنشاء تلقائي
                    </Button>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="account_name"
                label="اسم الحساب"
                rules={[{ required: true, message: 'يرجى إدخال اسم الحساب' }]}
              >
                <Input placeholder="أدخل اسم الحساب" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="account_type"
                label="نوع الحساب"
                rules={[{ required: true, message: 'يرجى اختيار نوع الحساب' }]}
              >
                <Select placeholder="اختر نوع الحساب">
                  <Option value="asset">أصول</Option>
                  <Option value="liability">خصوم</Option>
                  <Option value="equity">حقوق الملكية</Option>
                  <Option value="revenue">إيرادات</Option>
                  <Option value="expense">مصروفات</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="level"
                label="المستوى"
                rules={[{ required: true, message: 'يرجى إدخال المستوى' }]}
              >
                <Select placeholder="اختر المستوى">
                  <Option value={1}>المستوى الأول (رئيسي)</Option>
                  <Option value={2}>المستوى الثاني</Option>
                  <Option value={3}>المستوى الثالث</Option>
                  <Option value={4}>المستوى الرابع</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="parent_id"
                label="الحساب الأب"
              >
                <TreeSelect
                  placeholder="اختر الحساب الأب (اختياري)"
                  allowClear
                  treeData={getParentAccounts()}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="is_active"
                label="الحالة"
                valuePropName="checked"
              >
                <Switch checkedChildren="نشط" unCheckedChildren="غير نشط" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => {
                setModalVisible(false)
                setEditingAccount(null)
                form.resetFields()
              }}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {editingAccount ? 'تحديث' : 'إضافة'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ChartOfAccountsManagement
