import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { SupplierService } from './SupplierService'
import { InventoryService } from './InventoryService'
import { Logger } from '../utils/logger'
import { sanitizeFormData, validateRequiredIds, logFormData } from '../utils/dataTypeUtils'

export interface PurchaseInvoice {
  id: number
  invoice_number: string
  supplier_id: number
  supplier_name?: string
  invoice_date: string
  due_date?: string
  status: 'pending' | 'paid' | 'partial' | 'overdue' | 'cancelled'
  total_amount: number
  discount: number
  tax: number
  final_amount: number
  paid_amount: number
  remaining_amount: number
  payment_method?: string
  notes?: string
  created_at: string
  updated_at?: string
  created_by?: number
  items?: PurchaseInvoiceItem[]
}

export interface PurchaseInvoiceItem {
  id: number
  invoice_id: number
  item_id: number
  item_name?: string
  warehouse_id: number
  warehouse_name?: string
  quantity: number
  unit_price: number
  total_price: number
  notes?: string
}

export interface PurchaseOrder {
  id: number
  order_number: string
  supplier_id: number
  supplier_name?: string
  order_date: string
  delivery_date?: string
  status: 'pending' | 'confirmed' | 'received' | 'cancelled'
  total_amount: number
  discount: number
  tax: number
  final_amount: number
  notes?: string
  created_at: string
  updated_at?: string
  created_by?: number
}

export interface CreatePurchaseInvoiceData {
  supplier_id: number
  invoice_date: string
  due_date?: string
  discount?: number
  tax?: number
  payment_method?: string
  notes?: string
  items: {
    item_id: number
    warehouse_id: number
    quantity: number
    unit_price: number
  }[]
}

export interface CreatePurchaseOrderData {
  supplier_id: number
  order_date: string
  expected_date?: string
  discount?: number
  tax?: number
  notes?: string
  items: {
    item_id: number
    warehouse_id: number
    quantity: number
    unit_price: number
  }[]
}

export class PurchaseService {
  private static instance: PurchaseService
  private db: any
  private supplierService: SupplierService
  private inventoryService: InventoryService

  private constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
    this.supplierService = SupplierService.getInstance()
    this.inventoryService = InventoryService.getInstance()
  }

  public static getInstance(): PurchaseService {
    if (!PurchaseService.instance) {
      PurchaseService.instance = new PurchaseService()
    }
    return PurchaseService.instance
  }

  // إنشاء جداول المشتريات
  public async createPurchaseTables(): Promise<void> {
    const database = this.db

    // جدول فواتير المشتريات
    database.exec(`
      CREATE TABLE IF NOT EXISTS purchase_invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT UNIQUE NOT NULL,
        supplier_id INTEGER NOT NULL,
        invoice_date DATE NOT NULL,
        due_date DATE,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'partial', 'overdue', 'cancelled')),
        total_amount DECIMAL(10,2) DEFAULT 0,
        discount DECIMAL(10,2) DEFAULT 0,
        tax DECIMAL(10,2) DEFAULT 0,
        final_amount DECIMAL(10,2) DEFAULT 0,
        paid_amount DECIMAL(10,2) DEFAULT 0,
        remaining_amount DECIMAL(10,2) DEFAULT 0,
        payment_method TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // جدول تفاصيل فواتير المشتريات
    database.exec(`
      CREATE TABLE IF NOT EXISTS purchase_invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        item_id INTEGER NOT NULL,
        warehouse_id INTEGER NOT NULL,
        quantity DECIMAL(10,2) NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (invoice_id) REFERENCES purchase_invoices (id) ON DELETE CASCADE,
        FOREIGN KEY (item_id) REFERENCES items (id),
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
      )
    `)

    // جدول أوامر الشراء
    database.exec(`
      CREATE TABLE IF NOT EXISTS purchase_orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_number TEXT UNIQUE NOT NULL,
        supplier_id INTEGER NOT NULL,
        order_date DATE NOT NULL,
        delivery_date DATE,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'received', 'cancelled')),
        total_amount DECIMAL(10,2) DEFAULT 0,
        discount DECIMAL(10,2) DEFAULT 0,
        tax DECIMAL(10,2) DEFAULT 0,
        final_amount DECIMAL(10,2) DEFAULT 0,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // جدول تفاصيل أوامر الشراء
    database.exec(`
      CREATE TABLE IF NOT EXISTS purchase_order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        item_id INTEGER NOT NULL,
        warehouse_id INTEGER NOT NULL,
        quantity DECIMAL(10,3) NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES purchase_orders (id) ON DELETE CASCADE,
        FOREIGN KEY (item_id) REFERENCES items (id),
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
      )
    `)

    // إنشاء الفهارس
    database.exec('CREATE INDEX IF NOT EXISTS idx_purchase_invoices_supplier ON purchase_invoices(supplier_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_purchase_invoices_date ON purchase_invoices(invoice_date)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_purchase_invoices_status ON purchase_invoices(status)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_purchase_invoice_items_invoice ON purchase_invoice_items(invoice_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_purchase_orders_date ON purchase_orders(order_date)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_purchase_order_items_order ON purchase_order_items(order_id)')
  }

  // الحصول على فواتير المشتريات
  public async getPurchaseInvoices(): Promise<PurchaseInvoice[]> {
    try {
      const invoices = this.db.prepare(`
        SELECT pi.*, s.name as supplier_name
        FROM purchase_invoices pi
        LEFT JOIN suppliers s ON pi.supplier_id = s.id
        ORDER BY pi.created_at DESC
      `).all() as PurchaseInvoice[]

      return invoices
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في جلب فواتير المشتريات:', error)
      return []
    }
  }

  // التحقق من صحة بيانات الفاتورة
  private validateInvoiceData(invoiceData: CreatePurchaseInvoiceData): { isValid: boolean; message?: string } {
    // التحقق من المورد
    if (!invoiceData.supplier_id || invoiceData.supplier_id <= 0) {
      return { isValid: false, message: 'يجب اختيار مورد صحيح' }
    }

    // التحقق من تاريخ الفاتورة
    if (!invoiceData.invoice_date) {
      return { isValid: false, message: 'تاريخ الفاتورة مطلوب' }
    }

    // التحقق من الأصناف
    if (!invoiceData.items || invoiceData.items.length === 0) {
      return { isValid: false, message: 'يجب إضافة صنف واحد على الأقل' }
    }

    // التحقق من صحة كل صنف
    for (let i = 0; i < invoiceData.items.length; i++) {
      const item = invoiceData.items[i]

      if (!item.item_id || item.item_id <= 0) {
        return { isValid: false, message: `يجب اختيار صنف صحيح في السطر ${i + 1}` }
      }

      if (!item.warehouse_id || item.warehouse_id <= 0) {
        return { isValid: false, message: `يجب اختيار مخزن صحيح في السطر ${i + 1}` }
      }

      if (!item.quantity || item.quantity <= 0) {
        return { isValid: false, message: `الكمية يجب أن تكون أكبر من صفر في السطر ${i + 1}` }
      }

      if (!item.unit_price || item.unit_price <= 0) {
        return { isValid: false, message: `سعر الوحدة يجب أن يكون أكبر من صفر في السطر ${i + 1}` }
      }
    }

    // التحقق من القيم الرقمية
    if (invoiceData.discount && invoiceData.discount < 0) {
      return { isValid: false, message: 'قيمة الخصم لا يمكن أن تكون سالبة' }
    }

    if (invoiceData.tax && invoiceData.tax < 0) {
      return { isValid: false, message: 'قيمة الضريبة لا يمكن أن تكون سالبة' }
    }

    return { isValid: true }
  }

  // إنشاء فاتورة مشتريات جديدة
  public async createPurchaseInvoice(invoiceData: CreatePurchaseInvoiceData, userId?: number): Promise<ApiResponse> {
    try {
      // تسجيل البيانات الأصلية للتشخيص
      logFormData('PurchaseService', 'إنشاء فاتورة الشراء', invoiceData)

      // تنّيف وتحويل البيانات
      const sanitizedData = sanitizeFormData(invoiceData)

      // تسجيل البيانات بعد التنّيف
      Logger.info('PurchaseService', '🧹 البيانات بعد التنّيف:', {
        supplier_id: String(sanitizedData.supplier_id) + ' (' + typeof sanitizedData.supplier_id + ')',
        items_count: sanitizedData.items?.length || 0,
        total_amount: String(sanitizedData.total_amount) + ' (' + typeof sanitizedData.total_amount + ')',
        payment_method: String(sanitizedData.payment_method) + ' (' + typeof sanitizedData.payment_method + ')'
      })

      // التحقق من المعرفات المطلوبة أولاً
      const idValidation = validateRequiredIds(sanitizedData, ['supplier_id'])
      if (!idValidation.isValid) {
        Logger.error('PurchaseService', '❌ فشل التحقق من المعرفات:', idValidation.message)
        return { success: false, message: idValidation.message || 'معرفات غير صحيحة' }
      }

      // التحقق من صحة البيانات بعد التنّيف
      const validation = this.validateInvoiceData(sanitizedData)
      if (!validation.isValid) {
        Logger.error('PurchaseService', '❌ فشل التحقق من البيانات:', validation.message)
        return { success: false, message: validation.message || 'بيانات غير صحيحة' }
      }

      const supplierId = sanitizedData.supplier_id

      Logger.info('PurchaseService', '🔍 البحث عن المورد:', {
        supplier_id: supplierId,
        supplier_id_type: typeof supplierId
      })

      // التحقق من وجود المورد
      const supplier = this.db.prepare('SELECT id, is_active FROM suppliers WHERE id = ?').get(supplierId) as any

      Logger.info('PurchaseService', '🔍 نتيجة البحث عن المورد:', {
        supplier_found: !!supplier,
        supplier_data: supplier,
        search_value: supplierId,
        search_type: typeof supplierId
      })

      if (!supplier) {
        Logger.error('PurchaseService', '❌ المورد غير موجود:', {
          supplier_id: supplierId,
          supplier_id_type: typeof supplierId,
          search_query: 'SELECT id, is_active FROM suppliers WHERE id = ?'
        })
        return { success: false, message: 'المورد غير موجود' }
      }
      if (!supplier.is_active) {
        Logger.error('PurchaseService', '❌ المورد غير نشط:', { supplier_id: supplierId })
        return { success: false, message: 'المورد غير نشط' }
      }

      // التحقق من وجود الأصناف والمخازن
      for (const item of sanitizedData.items) {
        const itemExists = this.db.prepare('SELECT id, is_active FROM items WHERE id = ?').get(item.item_id) as any
        if (!itemExists) {
          return { success: false, message: 'الصنف غير موجود (ID: ' + item.item_id + ')' }
        }
        if (!itemExists.is_active) {
          return { success: false, message: 'الصنف غير نشط (ID: ' + item.item_id + ')' }
        }

        const warehouseExists = this.db.prepare('SELECT id, is_active FROM warehouses WHERE id = ?').get(item.warehouse_id) as any
        if (!warehouseExists) {
          return { success: false, message: 'المخزن غير موجود (ID: ' + item.warehouse_id + ')' }
        }
        if (!warehouseExists.is_active) {
          return { success: false, message: 'المخزن غير نشط (ID: ' + item.warehouse_id + ')' }
        }
      }

      // توليد رقم الفاتورة
      const invoiceNumber = await this.generateInvoiceNumber()

      // حساب المجاميع
      let totalAmount = 0
      for (const item of sanitizedData.items) {
        totalAmount += item.quantity * item.unit_price
      }

      const discount = sanitizedData.discount || 0
      const tax = sanitizedData.tax || 0
      const finalAmount = totalAmount - discount + tax

      // حساب المبلغ المدفوع والمتبقي
      const paidAmount = parseFloat(String(sanitizedData.paid_amount || 0))
      const remainingAmount = finalAmount - paidAmount

      // إدراج الفاتورة
      const invoiceResult = this.db.prepare(`
        INSERT INTO purchase_invoices (
          invoice_number, supplier_id, invoice_date, due_date,
          total_amount, discount, tax, final_amount, paid_amount, remaining_amount,
          payment_method, notes, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        invoiceNumber,
        supplierId,
        sanitizedData.invoice_date,
        sanitizedData.due_date || null,
        totalAmount,
        discount,
        tax,
        finalAmount,
        paidAmount,
        remainingAmount,
        sanitizedData.payment_method || null,
        sanitizedData.notes || null,
        userId || null
      )

      const invoiceId = invoiceResult.lastInsertRowid

      // تحديد حالة الفاتورة بناءً على المبلغ المدفوع
      let invoiceStatus = 'pending'
      if (remainingAmount <= 0) {
        invoiceStatus = 'paid'
      } else if (paidAmount > 0) {
        invoiceStatus = 'partial'
      }

      // تحديث حالة الفاتورة
      this.db.prepare(`
        UPDATE purchase_invoices
        SET status = ?
        WHERE id = ?
      `).run(invoiceStatus, invoiceId)

      // إدراج تفاصيل الفاتورة وتحديث المخزون
      for (const item of sanitizedData.items) {
        const totalPrice = item.quantity * item.unit_price

        // إدراج تفاصيل الفاتورة
        this.db.prepare(`
          INSERT INTO purchase_invoice_items (
            invoice_id, item_id, warehouse_id, quantity, unit_price, total_price
          ) VALUES (?, ?, ?, ?, ?, ?)
        `).run(
          invoiceId,
          item.item_id,
          item.warehouse_id,
          item.quantity,
          item.unit_price,
          totalPrice
        )

        // تحديث المخزون (إضافة الكمية مع سعر التكلفة)
        this.inventoryService.updateInventoryQuantity(
          item.item_id,
          item.warehouse_id,
          'in',
          item.quantity,
          item.unit_price
        )

        // إنشاء حركة مخزون
        await this.inventoryService.createInventoryMovement({
          item_id: item.item_id,
          warehouse_id: item.warehouse_id,
          movement_type: 'in',
          quantity: item.quantity,
          reference_type: 'purchase_invoice',
          reference_id: invoiceId,
          notes: 'فاتورة مشتريات رقم ' + invoiceNumber,
          created_by: userId
        })

        // تحديث سعر التكلفة للصنف
        this.db.prepare(`
          UPDATE items 
          SET cost_price = ?, updated_at = datetime('now')
          WHERE id = ?
        `).run(item.unit_price, item.item_id)
      }

      // تحديث رصيد المورد
      await this.supplierService.updateSupplierBalance(
        invoiceData.supplier_id,
        finalAmount,
        'invoice',
        'فاتورة مشتريات رقم ' + invoiceNumber
      )

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم إنشاء فاتورة المشتريات بنجاح',
        data: { invoiceId, invoiceNumber }
      }
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في إنشاء فاتورة المشتريات:', error)
      return { success: false, message: 'حدث خطأ في إنشاء فاتورة المشتريات' }
    }
  }

  // توليد رقم فاتورة جديد
  public async generateInvoiceNumber(): Promise<string> {
    try {
      const lastInvoice = this.db.prepare(`
        SELECT invoice_number FROM purchase_invoices
        WHERE invoice_number LIKE 'PUR%' AND invoice_number IS NOT NULL AND invoice_number != ''
        ORDER BY CAST(SUBSTR(invoice_number, 4) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastInvoice && lastInvoice.invoice_number && lastInvoice.invoice_number.length >= 9) {
        const codeNumber = lastInvoice.invoice_number.substring(3)
        const lastNumber = parseInt(codeNumber)

        if (!isNaN(lastNumber)) {
          const newNumber = lastNumber + 1
          return 'PUR' + newNumber.toString().padStart(6, '0')
        }
      }

      return 'PUR000001'
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في توليد رقم الفاتورة:', error)
      return 'PUR' + Date.now().toString().slice(-6)
    }
  }

  // تحديث فاتورة مشتريات
  public async updatePurchaseInvoice(invoiceId: number, invoiceData: CreatePurchaseInvoiceData, _userId?: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود الفاتورة
      const existingInvoice = this.db.prepare('SELECT * FROM purchase_invoices WHERE id = ?').get(invoiceId) as any
      if (!existingInvoice) {
        return { success: false, message: 'لم يتم العثور على الفاتورة' }
      }

      // حساب المجاميع
      let totalAmount = 0
      for (const item of invoiceData.items) {
        totalAmount += item.quantity * item.unit_price
      }

      const discount = invoiceData.discount || 0
      const tax = invoiceData.tax || 0
      const finalAmount = totalAmount - discount + tax

      // تحديث الفاتورة
      const result = this.db.prepare(`
        UPDATE purchase_invoices SET
          supplier_id = ?, invoice_date = ?, due_date = ?,
          total_amount = ?, discount = ?, tax = ?, final_amount = ?,
          remaining_amount = ?, payment_method = ?, notes = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run(
        invoiceData.supplier_id,
        invoiceData.invoice_date,
        invoiceData.due_date || null,
        totalAmount,
        discount,
        tax,
        finalAmount,
        finalAmount - existingInvoice.paid_amount, // المبلغ المتبقي = المبلغ الجديد - المدفوع
        invoiceData.payment_method || null,
        invoiceData.notes || null,
        invoiceId
      )

      if (result.changes === 0) {
        return { success: false, message: 'فشل في تحديث الفاتورة' }
      }

      // حذف التفاصيل القديمة وإعادة المخزون
      const oldItems = this.db.prepare('SELECT * FROM purchase_invoice_items WHERE invoice_id = ?').all(invoiceId)
      for (const oldItem of oldItems) {
        // إعادة الكمية للمخزون (عكس العملية)
        this.inventoryService.updateInventoryQuantity(
          oldItem.item_id,
          oldItem.warehouse_id,
          'out',
          oldItem.quantity
        )
      }

      // حذف التفاصيل القديمة
      this.db.prepare('DELETE FROM purchase_invoice_items WHERE invoice_id = ?').run(invoiceId)

      // إدراج التفاصيل الجديدة وتحديث المخزون
      for (const item of invoiceData.items) {
        const totalPrice = item.quantity * item.unit_price

        // إدراج تفاصيل الفاتورة
        this.db.prepare(`
          INSERT INTO purchase_invoice_items (
            invoice_id, item_id, warehouse_id, quantity, unit_price, total_price
          ) VALUES (?, ?, ?, ?, ?, ?)
        `).run(
          invoiceId,
          item.item_id,
          item.warehouse_id,
          item.quantity,
          item.unit_price,
          totalPrice
        )

        // تحديث المخزون (إضافة الكمية مع سعر التكلفة)
        this.inventoryService.updateInventoryQuantity(
          item.item_id,
          item.warehouse_id,
          'in',
          item.quantity,
          item.unit_price
        )

        // تحديث سعر التكلفة للصنف
        this.db.prepare(`
          UPDATE items
          SET cost_price = ?, updated_at = datetime('now')
          WHERE id = ?
        `).run(item.unit_price, item.item_id)
      }

      return { success: true, message: 'تم تحديث فاتورة المشتريات بنجاح' }
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تحديث فاتورة المشتريات:', error)
      return { success: false, message: 'حدث خطأ في تحديث فاتورة المشتريات' }
    }
  }

  // حذف أو إلغاء تفعيل فاتورة مشتريات مع تحقق من الدور
  public async deletePurchaseInvoice(invoiceId: number, userRole?: string, softDelete?: boolean): Promise<ApiResponse> {
    try {
      if (userRole !== 'admin' && userRole !== 'purchase_manager') {
        return { success: false, message: 'ليس لديك صلاحية حذف فاتورة المشتريات. فقط المدير أو مدير المشتريات يمكنه ذلك.' }
      }
      const invoice = this.db.prepare('SELECT * FROM purchase_invoices WHERE id = ?').get(invoiceId) as any
      if (!invoice) {
        return { success: false, message: 'لم يتم العثور على الفاتورة' }
      }
      if (invoice.paid_amount > 0) {
        return { success: false, message: 'لا يمكن حذف فاتورة تم دفع جزء منها أو مدفوعة بالكامل. يمكنك إلغاء تفعيلها فقط.' }
      }
      if (softDelete) {
        const result = this.db.prepare('UPDATE purchase_invoices SET status = ? WHERE id = ?').run('cancelled', invoiceId)
        if (result.changes > 0) {
          return { success: true, message: 'تم إلغاء تفعيل الفاتورة بنجاح (تم وضعها كملغاة)' }
        } else {
          return { success: false, message: 'فشل في إلغاء تفعيل الفاتورة' }
        }
      } else {
        // إعادة الكميات للمخزون
        const items = this.db.prepare('SELECT * FROM purchase_invoice_items WHERE invoice_id = ?').all(invoiceId)
        for (const item of items) {
          this.inventoryService.updateInventoryQuantity(
            item.item_id,
            item.warehouse_id,
            'out',
            item.quantity
          )
        }
        this.db.prepare('DELETE FROM purchase_invoice_items WHERE invoice_id = ?').run(invoiceId)
        const result = this.db.prepare('DELETE FROM purchase_invoices WHERE id = ?').run(invoiceId)
        if (result.changes > 0) {
          await this.supplierService.updateSupplierBalance(
            invoice.supplier_id,
            -invoice.final_amount,
            'invoice_deleted',
            'حذف فاتورة مشتريات رقم ' + invoice.invoice_number
          )
          return { success: true, message: 'تم حذف فاتورة المشتريات نهائيًا' }
        } else {
          return { success: false, message: 'فشل في حذف الفاتورة نهائيًا' }
        }
      }
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في حذف فاتورة المشتريات:', error)
      return { success: false, message: 'حدث خطأ في حذف فاتورة المشتريات' }
    }
  }

  // الحصول على تفاصيل فاتورة
  public async getPurchaseInvoiceItems(invoiceId: number): Promise<PurchaseInvoiceItem[]> {
    try {
      const items = this.db.prepare(`
        SELECT pii.*, i.name as item_name, w.name as warehouse_name
        FROM purchase_invoice_items pii
        LEFT JOIN items i ON pii.item_id = i.id
        LEFT JOIN warehouses w ON pii.warehouse_id = w.id
        WHERE pii.invoice_id = ?
        ORDER BY pii.id
      `).all(invoiceId) as PurchaseInvoiceItem[]

      return items
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في جلب تفاصيل الفاتورة:', error)
      return []
    }
  }

  // التحقق من صحة بيانات أمر الشراء
  private validateOrderData(orderData: CreatePurchaseOrderData): { isValid: boolean; message?: string } {
    // التحقق من المورد
    if (!orderData.supplier_id || orderData.supplier_id <= 0) {
      return { isValid: false, message: 'يجب اختيار مورد صحيح' }
    }

    // التحقق من تاريخ الأمر
    if (!orderData.order_date) {
      return { isValid: false, message: 'تاريخ الأمر مطلوب' }
    }

    // التحقق من الأصناف
    if (!orderData.items || orderData.items.length === 0) {
      return { isValid: false, message: 'يجب إضافة صنف واحد على الأقل' }
    }

    // التحقق من صحة كل صنف
    for (let i = 0; i < orderData.items.length; i++) {
      const item = orderData.items[i]

      if (!item.item_id || item.item_id <= 0) {
        return { isValid: false, message: `يجب اختيار صنف صحيح في السطر ${i + 1}` }
      }

      if (!item.warehouse_id || item.warehouse_id <= 0) {
        return { isValid: false, message: `يجب اختيار مخزن صحيح في السطر ${i + 1}` }
      }

      if (!item.quantity || item.quantity <= 0) {
        return { isValid: false, message: `الكمية يجب أن تكون أكبر من صفر في السطر ${i + 1}` }
      }

      if (!item.unit_price || item.unit_price <= 0) {
        return { isValid: false, message: `سعر الوحدة يجب أن يكون أكبر من صفر في السطر ${i + 1}` }
      }
    }

    // التحقق من القيم الرقمية
    if (orderData.discount && orderData.discount < 0) {
      return { isValid: false, message: 'قيمة الخصم لا يمكن أن تكون سالبة' }
    }

    if (orderData.tax && orderData.tax < 0) {
      return { isValid: false, message: 'قيمة الضريبة لا يمكن أن تكون سالبة' }
    }

    return { isValid: true }
  }

  // إنشاء أمر شراء جديد
  public async createPurchaseOrder(orderData: CreatePurchaseOrderData, userId?: number): Promise<ApiResponse> {
    try {
      // تسجيل البيانات الأصلية للتشخيص
      logFormData('PurchaseService', 'إنشاء أمر الشراء', orderData)

      // تنّيف وتحويل البيانات
      const sanitizedData = sanitizeFormData(orderData)

      // تسجيل البيانات بعد التنّيف
      Logger.info('PurchaseService', '🧹 البيانات بعد التنّيف:', {
        supplier_id: String(sanitizedData.supplier_id) + ' (' + typeof sanitizedData.supplier_id + ')',
        items_count: sanitizedData.items?.length || 0,
        total_amount: String(sanitizedData.total_amount) + ' (' + typeof sanitizedData.total_amount + ')'
      })

      // التحقق من المعرفات المطلوبة أولاً
      const idValidation = validateRequiredIds(sanitizedData, ['supplier_id'])
      if (!idValidation.isValid) {
        Logger.error('PurchaseService', '❌ فشل التحقق من المعرفات:', idValidation.message)
        return { success: false, message: idValidation.message || 'معرفات غير صحيحة' }
      }

      // التحقق من صحة البيانات بعد التنّيف
      const validation = this.validateOrderData(sanitizedData)
      if (!validation.isValid) {
        Logger.error('PurchaseService', '❌ فشل التحقق من البيانات:', validation.message)
        return { success: false, message: validation.message || 'بيانات غير صحيحة' }
      }

      const supplierId = sanitizedData.supplier_id

      Logger.info('PurchaseService', '🔍 البحث عن المورد:', {
        supplier_id: supplierId,
        supplier_id_type: typeof supplierId
      })

      // التحقق من وجود المورد
      const supplier = this.db.prepare('SELECT id, is_active FROM suppliers WHERE id = ?').get(supplierId) as any

      Logger.info('PurchaseService', '🔍 نتيجة البحث عن المورد:', {
        supplier_found: !!supplier,
        supplier_data: supplier,
        search_value: supplierId,
        search_type: typeof supplierId
      })

      if (!supplier) {
        Logger.error('PurchaseService', '❌ المورد غير موجود:', {
          supplier_id: supplierId,
          supplier_id_type: typeof supplierId,
          search_query: 'SELECT id, is_active FROM suppliers WHERE id = ?'
        })
        return { success: false, message: 'المورد غير موجود' }
      }
      if (!supplier.is_active) {
        Logger.error('PurchaseService', '❌ المورد غير نشط:', { supplier_id: supplierId })
        return { success: false, message: 'المورد غير نشط' }
      }

      // التحقق من وجود الأصناف والمخازن
      for (const item of sanitizedData.items) {
        const itemExists = this.db.prepare('SELECT id, is_active FROM items WHERE id = ?').get(item.item_id) as any
        if (!itemExists) {
          return { success: false, message: 'الصنف غير موجود (ID: ' + item.item_id + ')' }
        }
        if (!itemExists.is_active) {
          return { success: false, message: 'الصنف غير نشط (ID: ' + item.item_id + ')' }
        }

        const warehouseExists = this.db.prepare('SELECT id, is_active FROM warehouses WHERE id = ?').get(item.warehouse_id) as any
        if (!warehouseExists) {
          return { success: false, message: 'المخزن غير موجود (ID: ' + item.warehouse_id + ')' }
        }
        if (!warehouseExists.is_active) {
          return { success: false, message: 'المخزن غير نشط (ID: ' + item.warehouse_id + ')' }
        }
      }

      // توليد رقم الأمر
      const orderNumber = await this.generateOrderNumber()

      // حساب المجاميع
      let totalAmount = 0
      for (const item of sanitizedData.items) {
        totalAmount += item.quantity * item.unit_price
      }

      const discount = sanitizedData.discount || 0
      const tax = sanitizedData.tax || 0
      const finalAmount = totalAmount - discount + tax

      // إدراج الأمر
      const orderResult = this.db.prepare(`
        INSERT INTO purchase_orders (
          order_number, supplier_id, order_date, delivery_date,
          total_amount, discount, tax, final_amount,
          notes, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        orderNumber,
        supplierId,
        sanitizedData.order_date,
        sanitizedData.expected_date || null,
        totalAmount,
        discount,
        tax,
        finalAmount,
        sanitizedData.notes || null,
        userId || null
      )

      const orderId = orderResult.lastInsertRowid

      // إدراج تفاصيل الأمر
      for (const item of sanitizedData.items) {
        const totalPrice = item.quantity * item.unit_price

        this.db.prepare(`
          INSERT INTO purchase_order_items (
            order_id, item_id, warehouse_id, quantity, unit_price, total_price
          ) VALUES (?, ?, ?, ?, ?, ?)
        `).run(
          orderId,
          item.item_id,
          item.warehouse_id,
          item.quantity,
          item.unit_price,
          totalPrice
        )
      }

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم إنشاء أمر الشراء بنجاح',
        data: { orderId, orderNumber }
      }
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في إنشاء أمر الشراء:', error)
      return { success: false, message: 'حدث خطأ في إنشاء أمر الشراء' }
    }
  }

  // الحصول على أوامر الشراء
  public async getPurchaseOrders(): Promise<PurchaseOrder[]> {
    try {
      const orders = this.db.prepare(`
        SELECT po.*, s.name as supplier_name
        FROM purchase_orders po
        LEFT JOIN suppliers s ON po.supplier_id = s.id
        ORDER BY po.created_at DESC
      `).all() as PurchaseOrder[]

      return orders
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في جلب أوامر الشراء:', error)
      return []
    }
  }

  // تحديث أمر شراء
  public async updatePurchaseOrder(orderId: number, orderData: CreatePurchaseOrderData, _userId?: number): Promise<ApiResponse> {
    try {
      // حساب المجاميع
      let totalAmount = 0
      for (const item of orderData.items) {
        totalAmount += item.quantity * item.unit_price
      }

      const discount = orderData.discount || 0
      const tax = orderData.tax || 0
      const finalAmount = totalAmount - discount + tax

      // تحديث الأمر
      const result = this.db.prepare(`
        UPDATE purchase_orders SET
          supplier_id = ?, order_date = ?, delivery_date = ?,
          total_amount = ?, discount = ?, tax = ?, final_amount = ?,
          notes = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run(
        orderData.supplier_id,
        orderData.order_date,
        orderData.expected_date || null,
        totalAmount,
        discount,
        tax,
        finalAmount,
        orderData.notes || null,
        orderId
      )

      if (result.changes === 0) {
        return { success: false, message: 'لم يتم العثور على أمر الشراء' }
      }

      // حذف التفاصيل القديمة
      this.db.prepare('DELETE FROM purchase_order_items WHERE order_id = ?').run(orderId)

      // إدراج التفاصيل الجديدة
      for (const item of orderData.items) {
        const totalPrice = item.quantity * item.unit_price

        this.db.prepare(`
          INSERT INTO purchase_order_items (
            order_id, item_id, warehouse_id, quantity, unit_price, total_price
          ) VALUES (?, ?, ?, ?, ?, ?)
        `).run(
          orderId,
          item.item_id,
          item.warehouse_id,
          item.quantity,
          item.unit_price,
          totalPrice
        )
      }

      return { success: true, message: 'تم تحديث أمر الشراء بنجاح' }
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تحديث أمر الشراء:', error)
      return { success: false, message: 'حدث خطأ في تحديث أمر الشراء' }
    }
  }

  // حذف أو إلغاء تفعيل أمر شراء مع تحقق من الدور
  public async deletePurchaseOrder(orderId: number, userRole?: string, softDelete?: boolean): Promise<ApiResponse> {
    try {
      if (userRole !== 'admin' && userRole !== 'purchase_manager') {
        return { success: false, message: 'ليس لديك صلاحية حذف أمر الشراء. فقط المدير أو مدير المشتريات يمكنه ذلك.' }
      }
      const order = this.db.prepare('SELECT * FROM purchase_orders WHERE id = ?').get(orderId) as any
      if (!order) {
        return { success: false, message: 'لم يتم العثور على أمر الشراء' }
      }
      if (softDelete) {
        const result = this.db.prepare('UPDATE purchase_orders SET status = ? WHERE id = ?').run('cancelled', orderId)
        if (result.changes > 0) {
          return { success: true, message: 'تم إلغاء تفعيل أمر الشراء بنجاح (تم وضعه كملغى)' }
        } else {
          return { success: false, message: 'فشل في إلغاء تفعيل أمر الشراء' }
        }
      } else {
        this.db.prepare('DELETE FROM purchase_order_items WHERE order_id = ?').run(orderId)
        const result = this.db.prepare('DELETE FROM purchase_orders WHERE id = ?').run(orderId)
        if (result.changes > 0) {
          return { success: true, message: 'تم حذف أمر الشراء نهائيًا' }
        } else {
          return { success: false, message: 'فشل في حذف أمر الشراء نهائيًا' }
        }
      }
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في حذف أمر الشراء:', error)
      return { success: false, message: 'حدث خطأ في حذف أمر الشراء' }
    }
  }

  // تحديث حالة أمر الشراء
  public async updatePurchaseOrderStatus(orderId: number, status: string, _userId?: number): Promise<ApiResponse> {
    try {
      // التحقق من صحة الحالة
      const validStatuses = ['pending', 'confirmed', 'received', 'cancelled']
      if (!validStatuses.includes(status)) {
        return { success: false, message: 'حالة غير صحيحة' }
      }

      // التحقق من وجود الأمر
      const order = this.db.prepare('SELECT * FROM purchase_orders WHERE id = ?').get(orderId) as any
      if (!order) {
        return { success: false, message: 'لم يتم العثور على أمر الشراء' }
      }

      // تحديث الحالة
      const result = this.db.prepare(`
        UPDATE purchase_orders
        SET status = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run(status, orderId)

      if (result.changes > 0) {
        // إذا تم استلام الأمر، يمكن تحويله لفاتورة تلقائياً
        if (status === 'received') {
          // يمكن إضافة منطق إضافي هنا
        }

        return {
          success: true,
          message: 'تم تحديث حالة أمر الشراء إلى ' + this.getStatusText(status)
        }
      } else {
        return { success: false, message: 'فشل في تحديث حالة أمر الشراء' }
      }
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تحديث حالة أمر الشراء:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة أمر الشراء' }
    }
  }

  // تحديث حالة فاتورة الشراء
  public async updatePurchaseInvoiceStatus(invoiceId: number, status: string, _userId?: number): Promise<ApiResponse> {
    try {
      // التحقق من صحة الحالة
      const validStatuses = ['pending', 'paid', 'partial', 'overdue', 'cancelled']
      if (!validStatuses.includes(status)) {
        return { success: false, message: 'حالة غير صحيحة' }
      }

      // التحقق من وجود الفاتورة
      const invoice = this.db.prepare('SELECT * FROM purchase_invoices WHERE id = ?').get(invoiceId) as any
      if (!invoice) {
        return { success: false, message: 'لم يتم العثور على الفاتورة' }
      }

      // تحديث حالة الفاتورة
      const result = this.db.prepare(`
        UPDATE purchase_invoices
        SET status = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run(status, invoiceId)

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return {
          success: true,
          message: 'تم تحديث حالة فاتورة الشراء إلى ' + this.getStatusText(status)
        }
      } else {
        return { success: false, message: 'فشل في تحديث حالة فاتورة الشراء' }
      }
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تحديث حالة فاتورة الشراء:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة فاتورة الشراء' }
    }
  }

  // تحويل أمر الشراء إلى فاتورة
  public async convertOrderToInvoice(orderId: number, userId?: number): Promise<ApiResponse> {
    try {
      // الحصول على بيانات الأمر
      const order = this.db.prepare(`
        SELECT po.*, s.name as supplier_name
        FROM purchase_orders po
        LEFT JOIN suppliers s ON po.supplier_id = s.id
        WHERE po.id = ?
      `).get(orderId) as any

      if (!order) {
        return { success: false, message: 'لم يتم العثور على أمر الشراء' }
      }

      if (order.status !== 'confirmed' && order.status !== 'received') {
        return { success: false, message: 'يجب أن يكون الأمر مؤكداً أو مستلماً لتحويله إلى فاتورة' }
      }

      // الحصول على تفاصيل الأمر
      const orderItems = await this.getPurchaseOrderItems(orderId)
      if (orderItems.length === 0) {
        return { success: false, message: 'لا توجد أصناف في أمر الشراء' }
      }

      // إنشاء بيانات الفاتورة
      const invoiceData: CreatePurchaseInvoiceData = {
        supplier_id: order.supplier_id,
        invoice_date: new Date().toISOString().split('T')[0],
        due_date: order.delivery_date,
        discount: order.discount || 0,
        tax: order.tax || 0,
        notes: 'تحويل من أمر الشراء رقم ' + order.order_number,
        items: orderItems.map(item => ({
          item_id: item.item_id,
          warehouse_id: item.warehouse_id,
          quantity: item.quantity,
          unit_price: item.unit_price
        }))
      }

      // إنشاء الفاتورة
      const invoiceResult = await this.createPurchaseInvoice(invoiceData, userId)

      if (invoiceResult.success) {
        // تحديث حالة الأمر إلى مستلم
        await this.updatePurchaseOrderStatus(orderId, 'received', userId)

        return {
          success: true,
          message: 'تم تحويل أمر الشراء إلى فاتورة بنجاح',
          data: invoiceResult.data
        }
      } else {
        return invoiceResult
      }
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تحويل أمر الشراء إلى فاتورة:', error)
      return { success: false, message: 'حدث خطأ في تحويل أمر الشراء إلى فاتورة' }
    }
  }

  // الحصول على نص الحالة بالعربية
  private getStatusText(status: string): string {
    const statusMap: { [key: string]: string } = {
      'pending': 'في الانتّار',
      'confirmed': 'مؤكد',
      'received': 'مستلم',
      'cancelled': 'ملغي'
    }
    return statusMap[status] || status
  }

  // الحصول على تفاصيل أمر الشراء
  public async getPurchaseOrderItems(orderId: number): Promise<any[]> {
    try {
      const items = this.db.prepare(`
        SELECT poi.*, i.name as item_name, w.name as warehouse_name
        FROM purchase_order_items poi
        LEFT JOIN items i ON poi.item_id = i.id
        LEFT JOIN warehouses w ON poi.warehouse_id = w.id
        WHERE poi.order_id = ?
        ORDER BY poi.id
      `).all(orderId)

      return items
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في جلب تفاصيل أمر الشراء:', error)
      return []
    }
  }

  // توليد رقم أمر شراء جديد
  public async generateOrderNumber(): Promise<string> {
    try {
      const lastOrder = this.db.prepare(`
        SELECT order_number FROM purchase_orders
        WHERE order_number LIKE 'PO%' AND order_number IS NOT NULL AND order_number != ''
        ORDER BY CAST(SUBSTR(order_number, 3) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastOrder && lastOrder.order_number && lastOrder.order_number.length >= 8) {
        const codeNumber = lastOrder.order_number.substring(2)
        const lastNumber = parseInt(codeNumber)

        if (!isNaN(lastNumber)) {
          const newNumber = lastNumber + 1
          return 'PO' + newNumber.toString().padStart(6, '0')
        }
      }

      return 'PO000001'
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في توليد رقم أمر الشراء:', error)
      return 'PO' + Date.now().toString().slice(-6)
    }
  }

  // تقرير المشتريات حسب المورد
  public async getPurchasesBySupplierReport(filters: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          s.id as supplier_id,
          s.code as supplier_code,
          s.name as supplier_name,
          COUNT(pi.id) as invoice_count,
          COALESCE(SUM(pi.final_amount), 0) as total_amount,
          COALESCE(SUM(pi.paid_amount), 0) as paid_amount,
          COALESCE(SUM(pi.remaining_amount), 0) as remaining_amount,
          COALESCE(AVG(pi.final_amount), 0) as average_amount
        FROM suppliers s
        LEFT JOIN purchase_invoices pi ON s.id = pi.supplier_id
        WHERE s.is_active = 1
      `

      const params: any[] = []

      if (filters.supplierId) {
        query += ` AND s.id = ?`
        params.push(filters.supplierId)
      }

      if (filters.dateRange && filters.dateRange.length === 2) {
        query += ` AND pi.invoice_date BETWEEN ? AND ?`
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      query += ` GROUP BY s.id, s.code, s.name ORDER BY total_amount DESC`

      const result = this.db.prepare(query).all(params)
      return result
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تقرير المشتريات حسب المورد:', error)
      return []
    }
  }

  // تقرير المشتريات حسب الصنف
  public async getPurchasesByItemReport(filters: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          i.id as item_id,
          i.code as item_code,
          i.name as item_name,
          i.unit,
          COUNT(DISTINCT pi.id) as invoice_count,
          COALESCE(SUM(pii.quantity), 0) as total_quantity,
          COALESCE(SUM(pii.total_price), 0) as total_amount,
          COALESCE(AVG(pii.unit_price), 0) as average_price,
          MIN(pii.unit_price) as min_price,
          MAX(pii.unit_price) as max_price
        FROM items i
        LEFT JOIN purchase_invoice_items pii ON i.id = pii.item_id
        LEFT JOIN purchase_invoices pi ON pii.invoice_id = pi.id
        WHERE i.is_active = 1
      `

      const params: any[] = []

      if (filters.itemId) {
        query += ` AND i.id = ?`
        params.push(filters.itemId)
      }

      if (filters.categoryId) {
        query += ` AND i.category_id = ?`
        params.push(filters.categoryId)
      }

      if (filters.dateRange && filters.dateRange.length === 2) {
        query += ` AND pi.invoice_date BETWEEN ? AND ?`
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      query += ` GROUP BY i.id, i.code, i.name, i.unit ORDER BY total_amount DESC`

      const result = this.db.prepare(query).all(params)
      return result
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تقرير المشتريات حسب الصنف:', error)
      return []
    }
  }

  // تقرير مديونيات الموردين
  public async getSupplierPayablesReport(filters: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          s.id as supplier_id,
          s.code as supplier_code,
          s.name as supplier_name,
          s.phone,
          s.email,
          s.credit_limit,
          s.payment_terms,
          COALESCE(SUM(pi.final_amount), 0) as total_invoices_amount,
          COALESCE(SUM(pi.paid_amount), 0) as total_paid_amount,
          COALESCE(SUM(pi.remaining_amount), 0) as total_remaining_amount,
          COUNT(pi.id) as invoice_count,
          MAX(pi.invoice_date) as last_invoice_date
        FROM suppliers s
        LEFT JOIN purchase_invoices pi ON s.id = pi.supplier_id
        WHERE s.is_active = 1
      `

      const params: any[] = []

      if (filters.supplierId) {
        query += ` AND s.id = ?`
        params.push(filters.supplierId)
      }

      if (filters.dateRange && filters.dateRange.length === 2) {
        query += ` AND pi.invoice_date BETWEEN ? AND ?`
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      query += ` GROUP BY s.id, s.code, s.name, s.phone, s.email, s.credit_limit, s.payment_terms`
      query += ` HAVING total_remaining_amount > 0`
      query += ` ORDER BY total_remaining_amount DESC`

      const result = this.db.prepare(query).all(params)
      return result
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تقرير مديونيات الموردين:', error)
      return []
    }
  }

  // تقرير تحليل أداء المشتريات
  public async getPurchaseAnalysisReport(filters: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          strftime('%Y-%m', pi.invoice_date) as period,
          COUNT(pi.id) as invoice_count,
          COALESCE(SUM(pi.final_amount), 0) as total_amount,
          COALESCE(AVG(pi.final_amount), 0) as average_amount,
          COUNT(DISTINCT pi.supplier_id) as supplier_count,
          COALESCE(SUM(pii.quantity), 0) as total_quantity
        FROM purchase_invoices pi
        LEFT JOIN purchase_invoice_items pii ON pi.id = pii.invoice_id
        WHERE 1=1
      `

      const params: any[] = []

      if (filters.supplierId) {
        query += ` AND pi.supplier_id = ?`
        params.push(filters.supplierId)
      }

      if (filters.categoryId) {
        query += ` AND EXISTS (
          SELECT 1 FROM purchase_invoice_items pii2
          JOIN items i ON pii2.item_id = i.id
          WHERE pii2.invoice_id = pi.id AND i.category_id = ?
        )`
        params.push(filters.categoryId)
      }

      if (filters.dateRange && filters.dateRange.length === 2) {
        query += ` AND pi.invoice_date BETWEEN ? AND ?`
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      query += ` GROUP BY strftime('%Y-%m', pi.invoice_date) ORDER BY period DESC`

      const result = this.db.prepare(query).all(params)
      return result
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تقرير تحليل أداء المشتريات:', error)
      return []
    }
  }

  // تقرير تحليل التكاليف
  public async getCostAnalysisReport(filters: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          i.id as item_id,
          i.code as item_code,
          i.name as item_name,
          i.unit,
          c.name as category_name,
          COUNT(DISTINCT pi.id) as purchase_count,
          COALESCE(SUM(pii.quantity), 0) as total_quantity,
          COALESCE(SUM(pii.total_price), 0) as total_cost,
          COALESCE(AVG(pii.unit_price), 0) as average_cost,
          MIN(pii.unit_price) as min_cost,
          MAX(pii.unit_price) as max_cost,
          i.cost_price as current_cost,
          i.sale_price as current_sale_price,
          CASE
            WHEN i.cost_price > 0 THEN ((i.sale_price - i.cost_price) / i.cost_price * 100)
            ELSE 0
          END as profit_margin
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        LEFT JOIN purchase_invoice_items pii ON i.id = pii.item_id
        LEFT JOIN purchase_invoices pi ON pii.invoice_id = pi.id
        WHERE i.is_active = 1
      `

      const params: any[] = []

      if (filters.itemId) {
        query += ` AND i.id = ?`
        params.push(filters.itemId)
      }

      if (filters.categoryId) {
        query += ` AND i.category_id = ?`
        params.push(filters.categoryId)
      }

      if (filters.dateRange && filters.dateRange.length === 2) {
        query += ` AND pi.invoice_date BETWEEN ? AND ?`
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      query += ` GROUP BY i.id, i.code, i.name, i.unit, c.name, i.cost_price, i.sale_price`
      query += ` ORDER BY total_cost DESC`

      const result = this.db.prepare(query).all(params)
      return result
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تقرير تحليل التكاليف:', error)
      return []
    }
  }

  // إضافة بيانات تجريبية لأوامر الشراء
  public async addSamplePurchaseOrdersData(): Promise<ApiResponse> {
    try {
      // التأكد من وجود موردين
      const suppliers = this.db.prepare('SELECT id FROM suppliers LIMIT 3').all()
      if (suppliers.length === 0) {
        return { success: false, message: 'يجب إضافة موردين أولاً' }
      }

      // التأكد من وجود أصناف
      const items = this.db.prepare('SELECT id FROM items LIMIT 5').all()
      if (items.length === 0) {
        return { success: false, message: 'يجب إضافة أصناف أولاً' }
      }

      // التأكد من وجود مخازن
      const warehouses = this.db.prepare('SELECT id FROM warehouses LIMIT 1').all()
      if (warehouses.length === 0) {
        return { success: false, message: 'يجب إضافة مخازن أولاً' }
      }

      const sampleOrders = [
        {
          supplier_id: suppliers[0].id,
          order_date: new Date().toISOString().split('T')[0],
          delivery_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          notes: 'أمر شراء تجريبي - مواد غذائية',
          items: [
            { item_id: items[0]?.id || 1, warehouse_id: warehouses[0].id, quantity: 50, unit_price: 25.50 },
            { item_id: items[1]?.id || 2, warehouse_id: warehouses[0].id, quantity: 30, unit_price: 45.00 }
          ]
        },
        {
          supplier_id: suppliers[1]?.id || suppliers[0].id,
          order_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          delivery_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          notes: 'أمر شراء تجريبي - مواد تنّيف',
          items: [
            { item_id: items[2]?.id || 1, warehouse_id: warehouses[0].id, quantity: 100, unit_price: 12.75 },
            { item_id: items[3]?.id || 2, warehouse_id: warehouses[0].id, quantity: 25, unit_price: 85.00 }
          ]
        }
      ]

      let addedCount = 0
      for (const orderData of sampleOrders) {
        const result = await this.createPurchaseOrder(orderData, 1)
        if (result.success) {
          addedCount++
        }
      }

      return {
        success: true,
        message: 'تم إضافة ' + addedCount + ' أمر شراء تجريبي بنجاح'
      }
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في إضافة البيانات التجريبية لأوامر الشراء:', error)
      return { success: false, message: 'حدث خطأ في إضافة البيانات التجريبية' }
    }
  }

  // إضافة بيانات تجريبية لفواتير الشراء
  public async addSamplePurchaseInvoicesData(): Promise<ApiResponse> {
    try {
      // التأكد من وجود موردين
      const suppliers = this.db.prepare('SELECT id FROM suppliers LIMIT 3').all()
      if (suppliers.length === 0) {
        return { success: false, message: 'يجب إضافة موردين أولاً' }
      }

      // التأكد من وجود أصناف
      const items = this.db.prepare('SELECT id FROM items LIMIT 5').all()
      if (items.length === 0) {
        return { success: false, message: 'يجب إضافة أصناف أولاً' }
      }

      // التأكد من وجود مخازن
      const warehouses = this.db.prepare('SELECT id FROM warehouses LIMIT 1').all()
      if (warehouses.length === 0) {
        return { success: false, message: 'يجب إضافة مخازن أولاً' }
      }

      const sampleInvoices = [
        {
          supplier_id: suppliers[0].id,
          invoice_date: new Date().toISOString().split('T')[0],
          due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          discount: 50,
          tax: 120,
          notes: 'فاتورة شراء تجريبية - دفعة أولى',
          items: [
            { item_id: items[0]?.id || 1, warehouse_id: warehouses[0].id, quantity: 20, unit_price: 35.00 },
            { item_id: items[1]?.id || 2, warehouse_id: warehouses[0].id, quantity: 15, unit_price: 55.50 }
          ]
        },
        {
          supplier_id: suppliers[1]?.id || suppliers[0].id,
          invoice_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          due_date: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          discount: 25,
          tax: 85,
          notes: 'فاتورة شراء تجريبية - مواد خام',
          items: [
            { item_id: items[2]?.id || 1, warehouse_id: warehouses[0].id, quantity: 75, unit_price: 18.25 },
            { item_id: items[3]?.id || 2, warehouse_id: warehouses[0].id, quantity: 10, unit_price: 125.00 }
          ]
        }
      ]

      let addedCount = 0
      for (const invoiceData of sampleInvoices) {
        const result = await this.createPurchaseInvoice(invoiceData, 1)
        if (result.success) {
          addedCount++
        }
      }

      return {
        success: true,
        message: 'تم إضافة ' + addedCount + ' فاتورة شراء تجريبية بنجاح'
      }
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في إضافة البيانات التجريبية لفواتير الشراء:', error)
      return { success: false, message: 'حدث خطأ في إضافة البيانات التجريبية' }
    }
  }

  // تقرير تحليل الموردين
  public async getSupplierAnalysisReport(filters: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          s.id as supplier_id,
          s.code as supplier_code,
          s.name as supplier_name,
          COALESCE(AVG(sq.quality_rating), 3.5) as quality_rating,
          COALESCE(AVG(sq.on_time_delivery_rate), 85.0) as on_time_delivery_rate,
          COALESCE(AVG(sq.avg_delivery_days), 7) as avg_delivery_days,
          COALESCE(AVG(sq.price_competitiveness), 75.0) as price_competitiveness,
          COALESCE(SUM(pi.total_amount), 0) as total_purchases,
          COUNT(DISTINCT po.id) as order_count,
          COALESCE(AVG(sq.return_rate), 2.0) as return_rate,
          MAX(po.order_date) as last_order_date
        FROM suppliers s
        LEFT JOIN purchase_orders po ON s.id = po.supplier_id
        LEFT JOIN purchase_invoices pi ON s.id = pi.supplier_id
        LEFT JOIN supplier_quality sq ON s.id = sq.supplier_id
        WHERE 1=1
      `;

      const params: any[] = [];

      if (filters.supplierId) {
        query += ` AND s.id = ?`;
        params.push(filters.supplierId);
      }

      if (filters.dateRange && filters.dateRange.length === 2) {
        query += ` AND po.order_date BETWEEN ? AND ?`;
        params.push(filters.dateRange[0], filters.dateRange[1]);
      }

      query += ` GROUP BY s.id, s.code, s.name ORDER BY total_purchases DESC`;

      const stmt = this.db.prepare(query);
      return stmt.all(...params);
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تقرير تحليل الموردين:', error);
      throw error;
    }
  }

  // تقرير جودة الموردين
  public async getSupplierQualityReport(filters: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          s.id as supplier_id,
          s.code as supplier_code,
          s.name as supplier_name,
          COALESCE(sq.quality_score, 75.0) as quality_score,
          COALESCE(sq.return_rate, 2.0) as return_rate,
          COALESCE(sq.defect_rate, 1.5) as defect_rate,
          COALESCE(sq.specification_compliance, 90.0) as specification_compliance,
          COALESCE(sq.complaint_count, 0) as complaint_count,
          COALESCE(sq.avg_complaint_resolution_days, 5) as avg_complaint_resolution_days,
          COALESCE(sq.quality_certifications, '[]') as quality_certifications,
          sq.last_evaluation_date
        FROM suppliers s
        LEFT JOIN supplier_quality sq ON s.id = sq.supplier_id
        WHERE 1=1
      `;

      const params: any[] = [];

      if (filters.supplierId) {
        query += ` AND s.id = ?`;
        params.push(filters.supplierId);
      }

      if (filters.dateRange && filters.dateRange.length === 2) {
        query += ` AND sq.last_evaluation_date BETWEEN ? AND ?`;
        params.push(filters.dateRange[0], filters.dateRange[1]);
      }

      query += ` ORDER BY quality_score DESC`;

      const stmt = this.db.prepare(query);
      const results = stmt.all(...params);

      // معالجة شهادات الجودة
      return results.map((row: any) => ({
        ...row,
        quality_certifications: row.quality_certifications ? JSON.parse(row.quality_certifications) : []
      }));
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تقرير جودة الموردين:', error);
      throw error;
    }
  }

  // تقرير مقارنة أسعار الموردين
  public async getSupplierPriceComparison(filters: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          i.id as item_id,
          i.code as item_code,
          i.name as item_name,
          c.name as category_name,
          MIN(pii.unit_price) as min_price,
          MAX(pii.unit_price) as max_price,
          AVG(pii.unit_price) as avg_price,
          COUNT(DISTINCT pi.supplier_id) as supplier_count,
          (SELECT s.name FROM suppliers s
           JOIN purchase_invoices pi2 ON s.id = pi2.supplier_id
           JOIN purchase_invoice_items pii2 ON pi2.id = pii2.invoice_id
           WHERE pii2.item_id = i.id AND pii2.unit_price = MIN(pii.unit_price)
           LIMIT 1) as min_price_supplier,
          (SELECT s.name FROM suppliers s
           JOIN purchase_invoices pi2 ON s.id = pi2.supplier_id
           JOIN purchase_invoice_items pii2 ON pi2.id = pii2.invoice_id
           WHERE pii2.item_id = i.id AND pii2.unit_price = MAX(pii.unit_price)
           LIMIT 1) as max_price_supplier,
          MAX(pi.invoice_date) as last_updated
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        JOIN purchase_invoice_items pii ON i.id = pii.item_id
        JOIN purchase_invoices pi ON pii.invoice_id = pi.id
        WHERE 1=1
      `;

      const params: any[] = [];

      if (filters.categoryId) {
        query += ` AND i.category_id = ?`;
        params.push(filters.categoryId);
      }

      if (filters.itemId) {
        query += ` AND i.id = ?`;
        params.push(filters.itemId);
      }

      if (filters.dateRange && filters.dateRange.length === 2) {
        query += ` AND pi.invoice_date BETWEEN ? AND ?`;
        params.push(filters.dateRange[0], filters.dateRange[1]);
      }

      query += ` GROUP BY i.id, i.code, i.name, c.name
                 HAVING supplier_count > 1
                 ORDER BY (MAX(pii.unit_price) - MIN(pii.unit_price)) DESC`;

      const stmt = this.db.prepare(query);
      return stmt.all(...params);
    } catch (error) {
      Logger.error('PurchaseService', 'خطأ في تقرير مقارنة أسعار الموردين:', error);
      throw error;
    }
  }
}
