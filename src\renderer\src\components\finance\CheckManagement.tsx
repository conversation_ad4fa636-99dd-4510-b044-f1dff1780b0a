import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Space,
  message,
  Popconfirm,
  Tag,
  Row,
  Col,
  Statistic,
  DatePicker
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  PlusOutlined,
  DeleteOutlined,
  <PERSON><PERSON>eftOutlined,
  CreditCardOutlined,
  CheckOutlined,
  CloseOutlined,
  ExclamationOutlined,
  ScanOutlined,
  PictureOutlined,
  FileExcelOutlined,
  PrinterOutlined,
  ImportOutlined,
  ExportOutlined
} from '@ant-design/icons'
import CheckImportExport from './CheckImportExport'
import dayjs from 'dayjs'
import CheckImageManager from '../common/CheckImageManager'
import CheckScanner from '../common/CheckScanner'
import * as XLSX from 'xlsx'
import { useCurrentUser } from '../../hooks/useCurrentUser'
import { UnifiedPrintButton } from '../common'

interface CheckManagementProps {
  onBack: () => void
}

const CheckManagement: React.FC<CheckManagementProps> = ({ onBack }) => {
  const [checks, setChecks] = useState([])
  const [bankAccounts, setBankAccounts] = useState([])
  const [customers, setCustomers] = useState([])
  const [suppliers, setSuppliers] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const { userId } = useCurrentUser()
  const [editingCheck, setEditingCheck] = useState<any>(null)
  const [form] = Form.useForm()

  // حالات إدارة الصور
  const [imageManagerVisible, setImageManagerVisible] = useState(false)
  const [scannerVisible, setScannerVisible] = useState(false)
  const [selectedCheckForImages, setSelectedCheckForImages] = useState<any>(null)

  // حالة الاستيراد والتصدير
  const [importExportVisible, setImportExportVisible] = useState(false)

  useEffect(() => {
    loadChecks()
    loadBankAccounts()
    loadCustomers()
    loadSuppliers()
  }, [])

  const loadChecks = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.getChecks()
      if (response.success) {
        setChecks(response.data)
      } else {
        message.error('فشل في تحميل الشيكات')
      }
    } catch (error) {
      message.error('خطأ في تحميل الشيكات')
    }
    setLoading(false)
  }

  const loadBankAccounts = async () => {
    try {
      const response = await window.electronAPI.getBankAccounts()
      if (response.success) {
        setBankAccounts(response.data)
      }
    } catch (error) {
      Logger.error('CheckManagement', 'خطأ في تحميل الحسابات المصرفية:', error)
      message.error('فشل في تحميل الحسابات المصرفية')
    }
  }

  const loadCustomers = async () => {
    try {
      const response = await window.electronAPI.getCustomers()
      if (response.success) {
        setCustomers(response.data || [])
      }
    } catch (error) {
      Logger.error('CheckManagement', 'خطأ في تحميل العملاء:', error)
      message.error('فشل في تحميل العملاء')
    }
  }

  const loadSuppliers = async () => {
    try {
      const response = await window.electronAPI.getSuppliers()
      if (response.success) {
        setSuppliers(response.data || [])
      }
    } catch (error) {
      Logger.error('CheckManagement', 'خطأ في تحميل الموردين:', error)
      message.error('فشل في تحميل الموردين')
    }
  }

  const generateCheckNumber = async () => {
    try {
      const response = await window.electronAPI.generateCheckNumber()
      if (response.success) {
        
        setTimeout(() => {
          try {
            if (form && form.setFieldsValue) {
              form.setFieldsValue({ check_number: response.data.checkNumber })
            }
          } catch (error) {
            Logger.warn('CheckManagement', 'خطأ في تحديث النموذج:', error)
          }
        }, 100)
        message.success('تم إنشاء رقم الشيك')
      }
    } catch (error) {
      message.error('خطأ في إنشاء رقم الشيك')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      // التحقق من صحة رقم الشيك
      if (!values.check_number || values.check_number.trim().length < 3) {
        message.error('رقم الشيك يجب أن يكون على الأقل 3 أرقام')
        return
      }

      // التحقق من صحة المبلغ
      const amount = parseFloat(values.amount) || 0
      if (amount <= 0) {
        message.error('يجب أن يكون مبلغ الشيك أكبر من الصفر')
        return
      }

      if (amount > 1000000) {
        message.error('مبلغ الشيك كبير جداً، يرجى التحقق من القيمة المدخلة')
        return
      }

      // التحقق من صحة التواريخ
      if (values.due_date && values.issue_date && values.due_date.isBefore(values.issue_date)) {
        message.error('تاريخ الاستحقاق لا يمكن أن يكون قبل تاريخ الإصدار')
        return
      }

      // التحقق من أن تاريخ الإصدار ليس في المستقبل البعيد
      if (values.issue_date && values.issue_date.isAfter(dayjs().add(1, 'year'))) {
        message.error('تاريخ الإصدار لا يمكن أن يكون في المستقبل البعيد')
        return
      }

      // تحديد بيانات العميل/المورد بناءً على نوع الاختيار
      let entityType = null
      let entityId = null
      let entityName = null

      if (values.payee_selection_type === 'customer') {
        entityType = 'customer'
        entityId = values.entity_id
        entityName = values.payee_name
      } else if (values.payee_selection_type === 'supplier') {
        entityType = 'supplier'
        entityId = values.entity_id
        entityName = values.payee_name
      } else if (values.payee_selection_type === 'manual') {
        entityType = 'other'
        entityId = null
        entityName = values.payee_name
      }

      const checkData = {
        ...values,
        check_number: values.check_number.trim(),
        amount: amount,
        issue_date: values.issue_date.format('YYYY-MM-DD'),
        check_date: values.issue_date.format('YYYY-MM-DD'), // للتوافق مع قاعدة البيانات
        due_date: values.due_date.format('YYYY-MM-DD'),
        check_type: values.check_type || 'received',
        original_payer: values.check_type === 'issued' ? 'الشركة' : values.original_payer,
        current_holder: values.check_type === 'issued' ? values.payee_name : values.current_holder,
        is_company_check: values.check_type === 'issued' ? 1 : 0,
        // إضافة بيانات العميل/المورد
        entity_type: entityType,
        entity_id: entityId,
        entity_name: entityName,
        created_by: userId
      }

      if (editingCheck) {
        // تحديث الشيك (إذا كان مطلوباً)
        message.info('تحديث الشيكات غير متاح حالياً')
      } else {
        const response = await window.electronAPI.createCheck(checkData)
        if (response.success) {
          const successMessage = values.check_type === 'issued'
            ? 'تم إصدار شيك الشركة بنجاح'
            : 'تم إضافة الشيك بنجاح'
          message.success(successMessage)
          loadChecks()
          setModalVisible(false)
          form.resetFields()
        } else {
          message.error('فشل في حفّ الشيك')
        }
      }
    } catch (error) {
      message.error('خطأ في حفّ الشيك')
    }
  }

  const handleStatusChange = async (checkId: number, status: string) => {
    try {
      const response = await window.electronAPI.updateCheckStatus(checkId, status)
      if (response.success) {
        message.success('تم تحديث حالة الشيك بنجاح')
        loadChecks()
      } else {
        message.error('فشل في تحديث حالة الشيك')
      }
    } catch (error) {
      message.error('خطأ في تحديث حالة الشيك')
    }
  }

  // دوال إدارة الصور
  const handleManageImages = (check: any) => {
    setSelectedCheckForImages(check)
    setImageManagerVisible(true)
  }

  const handleScanCheck = (check: any) => {
    setSelectedCheckForImages(check)
    setScannerVisible(true)
  }

  const closeImageManager = () => {
    setImageManagerVisible(false)
    setSelectedCheckForImages(null)
  }

  const closeScanner = () => {
    setScannerVisible(false)
    setSelectedCheckForImages(null)
  }

  const handleScanComplete = (images: any[]) => {
    message.success('تم حفّ ' + images.length + ' صورة للشيك بنجاح')
    // يمكن إضافة منطق إضافي هنا
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'issued': return 'blue'
      case 'cashed': return 'green'
      case 'bounced': return 'red'
      case 'cancelled': return 'gray'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'issued': return 'مُصدر'
      case 'cashed': return 'محصل'
      case 'bounced': return 'مرتد'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  const isOverdue = (dueDate: string) => {
    return dayjs(dueDate).isBefore(dayjs(), 'day')
  }

  // دالة تصدير Excel للشيكات
  const handleExportExcel = () => {
    try {
      // تحضير البيانات للتصدير
      const exportData = checks.map(check => ({
        'رقم الشيك': check.check_number,
        'المبلغ': check.amount || 0,
        'اسم المستفيد': check.payee_name || '',
        'نوع الجهة': check.entity_type === 'customer' ? 'عميل' :
                    check.entity_type === 'supplier' ? 'مورد' :
                    check.entity_type === 'other' ? 'أخرى' : '-',
        'اسم العميل/المورد': check.entity_name || '-',
        'تاريخ الإصدار': dayjs(check.check_date).format('YYYY-MM-DD'),
        'تاريخ الاستحقاق': dayjs(check.due_date).format('YYYY-MM-DD'),
        'نوع الشيك': check.check_type === 'issued' ? 'صادر' : 'وارد',
        'الحالة': getStatusText(check.status),
        'البنك': check.bank_name || '',
        'الحساب البنكي': check.bank_account_name || '',
        'الدافع الأصلي': check.original_payer || '',
        'الحامل الحالي': check.current_holder || '',
        'ملاحّات': check.notes || '',
        'تاريخ الإنشاء': dayjs(check.created_at).format('YYYY-MM-DD HH:mm')
      }))

      // إنشاء ورقة العمل
      const worksheet = XLSX.utils.json_to_sheet(exportData)

      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 15 }, // رقم الشيك
        { wch: 15 }, // المبلغ
        { wch: 20 }, // اسم المستفيد
        { wch: 12 }, // نوع الجهة
        { wch: 20 }, // اسم العميل/المورد
        { wch: 12 }, // تاريخ الإصدار
        { wch: 12 }, // تاريخ الاستحقاق
        { wch: 10 }, // نوع الشيك
        { wch: 10 }, // الحالة
        { wch: 20 }, // البنك
        { wch: 20 }, // الحساب البنكي
        { wch: 20 }, // الدافع الأصلي
        { wch: 20 }, // الحامل الحالي
        { wch: 25 }, // ملاحّات
        { wch: 18 }  // تاريخ الإنشاء
      ]
      worksheet['!cols'] = columnWidths

      // إنشاء المصنف
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'الشيكات')

      // إضافة ورقة إحصائيات
      const totalAmount = checks.reduce((sum, check) => sum + (check.amount || 0), 0)
      const stats = [
        { 'البيان': 'إجمالي عدد الشيكات', 'القيمة': checks.length },
        { 'البيان': 'إجمالي المبالغ', 'القيمة': totalAmount },
        { 'البيان': 'الشيكات الصادرة', 'القيمة': checks.filter(c => c.check_type === 'issued').length },
        { 'البيان': 'الشيكات الواردة', 'القيمة': checks.filter(c => c.check_type === 'received').length },
        { 'البيان': 'الشيكات المحصلة', 'القيمة': checks.filter(c => c.status === 'cashed').length },
        { 'البيان': 'الشيكات المرتدة', 'القيمة': checks.filter(c => c.status === 'bounced').length },
        { 'البيان': 'الشيكات المتأخرة', 'القيمة': checks.filter(c => isOverdue(c.due_date) && c.status === 'issued').length }
      ]

      const statsWorksheet = XLSX.utils.json_to_sheet(stats)
      statsWorksheet['!cols'] = [{ wch: 25 }, { wch: 15 }]
      XLSX.utils.book_append_sheet(workbook, statsWorksheet, 'الإحصائيات')

      // حفّ الملف
      const fileName = 'الشيكات_' + dayjs().format('YYYY-MM-DD_HH-mm') + '.xlsx'
      XLSX.writeFile(workbook, fileName)

      message.success('تم تصدير البيانات بنجاح')
    } catch (error) {
      Logger.error('CheckManagement', 'خطأ في تصدير Excel:', error)
      message.error('فشل في تصدير البيانات')
    }
  }



  const columns = [
    {
      title: 'رقم الشيك',
      dataIndex: 'check_number',
      key: 'check_number',
    },
    {
      title: 'البنك',
      dataIndex: 'bank_name',
      key: 'bank_name',
    },
    {
      title: 'رقم الحساب',
      dataIndex: 'account_number',
      key: 'account_number',
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ₪ {amount?.toLocaleString() || 0}
        </span>
      ),
    },
    {
      title: 'المستفيد',
      dataIndex: 'payee_name',
      key: 'payee_name',
    },
    {
      title: 'العميل/المورد',
      key: 'entity_info',
      render: (record: any) => {
        if (!record.entity_type || record.entity_type === 'other') {
          return <span style={{ color: '#999' }}>-</span>
        }

        const entityTypeText = record.entity_type === 'customer' ? 'عميل' : 'مورد'
        const color = record.entity_type === 'customer' ? '#1890ff' : '#52c41a'

        return (
          <div>
            <Tag color={color} style={{ marginBottom: '2px' }}>
              {entityTypeText}
            </Tag>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.entity_name}
            </div>
          </div>
        )
      },
    },
    {
      title: 'تاريخ الإصدار',
      dataIndex: 'issue_date',
      key: 'issue_date',
    },
    {
      title: 'تاريخ الاستحقاق',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (dueDate: string) => (
        <span style={{ 
          color: isOverdue(dueDate) ? '#ff4d4f' : '#000',
          fontWeight: isOverdue(dueDate) ? 'bold' : 'normal'
        }}>
          {dueDate}
          {isOverdue(dueDate) && <ExclamationOutlined style={{ marginLeft: '4px', color: '#ff4d4f' }} />}
        </span>
      ),
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: any) => (
        <Space wrap>
          {record.status === 'issued' && (
            <>
              <Button
                type="primary"
                size="small"
                icon={<CheckOutlined />}
                onClick={() => handleStatusChange(record.id, 'cashed')}
              >
                تحصيل
              </Button>
              <Button
                danger
                size="small"
                icon={<CloseOutlined />}
                onClick={() => handleStatusChange(record.id, 'bounced')}
              >
                ارتداد
              </Button>
              <Popconfirm
                title="هل أنت متأكد من إلغاء هذا الشيك؟"
                onConfirm={() => handleStatusChange(record.id, 'cancelled')}
                okText="نعم"
                cancelText="لا"
              >
                <Button
                  size="small"
                  icon={<DeleteOutlined />}
                >
                  إلغاء
                </Button>
              </Popconfirm>
            </>
          )}

          {/* زر الطباعة الموحد */}
          <UnifiedPrintButton
            data={{
              id: record.id,
              title: 'شيك',
              subtitle: `رقم: ${record.check_number}`,
              date: record.issue_date,
              customer: {
                name: record.payee_name || 'غير محدد',
                phone: record.payee_phone || '',
                address: record.payee_address || ''
              },
              items: [{
                id: 1,
                name: 'مبلغ الشيك',
                description: `شيك ${record.check_type === 'issued' ? 'صادر' : 'وارد'} - ${record.bank_name || ''}`,
                quantity: 1,
                unit: 'مبلغ',
                unitPrice: record.amount || 0,
                total: record.amount || 0
              }],
              total: record.amount || 0,
              notes: `تاريخ الاستحقاق: ${record.due_date}\nالحالة: ${getStatusText(record.status)}\nرقم الحساب: ${record.account_number || 'غير محدد'}`
            }}
            type="receipt"
            subType="payment"
            buttonText="طباعة"
            size="small"
            showDropdown={true}
            _documentId={`check_${record.id}`}
            onAfterPrint={() => message.success('تم طباعة الشيك بنجاح')}
            onError={() => message.error('فشل في طباعة الشيك')}
          />

          {/* أزرار إدارة الصور */}
          <Button
            size="small"
            icon={<ScanOutlined />}
            onClick={() => handleScanCheck(record)}
            title="مسح ضوئي"
          >
            مسح
          </Button>
          <Button
            size="small"
            icon={<PictureOutlined />}
            onClick={() => handleManageImages(record)}
            title="إدارة الصور"
          >
            صور
          </Button>
        </Space>
      ),
    },
  ]

  const stats = {
    total: checks.length,
    issued: checks.filter((check: any) => check.status === 'issued').length,
    cashed: checks.filter((check: any) => check.status === 'cashed').length,
    bounced: checks.filter((check: any) => check.status === 'bounced').length,
    overdue: checks.filter((check: any) => check.status === 'issued' && isOverdue(check.due_date)).length,
    totalAmount: checks.reduce((sum: number, check: any) => sum + (check.amount || 0), 0)
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>💳 إدارة الشيكات</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            إصدار ومتابعة الشيكات وحالاتها
          </p>
        </div>
        <Button 
          type="default" 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
        >
          العودة
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي الشيكات"
              value={stats.total}
              valueStyle={{ color: '#1890ff' }}
              prefix={<CreditCardOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="شيكات مُصدرة"
              value={stats.issued}
              valueStyle={{ color: '#722ed1' }}
              prefix={<CreditCardOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="شيكات محصلة"
              value={stats.cashed}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="شيكات مرتدة"
              value={stats.bounced}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<CloseOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="شيكات متأخرة"
              value={stats.overdue}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<ExclamationOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي المبالغ"
              value={stats.totalAmount}
              valueStyle={{ color: '#13c2c2' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      <Card
        title="قائمة الشيكات"
        extra={
          <Space>
            <Button
              type="default"
              icon={<FileExcelOutlined />}
              onClick={handleExportExcel}
              style={{ color: '#52c41a', borderColor: '#52c41a' }}
            >
              تصدير Excel
            </Button>
            <UnifiedPrintButton
              data={{
                title: 'تقرير الشيكات',
                subtitle: `إجمالي: ${stats.total} شيك`,
                date: new Date().toLocaleDateString('ar-SA'),
                items: checks.map((check: any, index: number) => ({
                  id: check.id,
                  name: `شيك رقم ${check.check_number}`,
                  description: `${check.payee_name} - ${check.bank_name} - ${getStatusText(check.status)}`,
                  quantity: 1,
                  unit: 'شيك',
                  unitPrice: check.amount || 0,
                  total: check.amount || 0
                })),
                total: stats.totalAmount,
                notes: `إحصائيات الشيكات:\n- المجموع: ${stats.total}\n- صادرة: ${stats.issued}\n- محصلة: ${stats.cashed}\n- مرتدة: ${stats.bounced}\n- متأخرة: ${stats.overdue}`
              }}
              type="report"
              subType="financial"
              buttonText="طباعة التقرير"
              size="middle"
              showDropdown={true}
              _documentId="checks_report"
              onAfterPrint={() => message.success('تم طباعة تقرير الشيكات بنجاح')}
              onError={() => message.error('فشل في طباعة التقرير')}
            />
            <Button
              icon={<ImportOutlined />}
              onClick={() => setImportExportVisible(true)}
              style={{ color: '#1890ff', borderColor: '#1890ff' }}
            >
              استيراد/تصدير
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setModalVisible(true)}
            >
              إصدار شيك جديد
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={checks}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* نموذج إصدار شيك جديد */}
      <Modal
        title="إدارة الشيكات"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setEditingCheck(null)
          form.resetFields()
        }}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ check_type: 'received' }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="check_type"
                label="نوع الشيك"
                rules={[{ required: true, message: 'يرجى اختيار نوع الشيك' }]}
              >
                <Select placeholder="اختر نوع الشيك">
                  <Select.Option value="received">شيك مستلم</Select.Option>
                  <Select.Option value="issued">شيك صادر من الشركة</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="check_number"
                label="رقم الشيك"
                rules={[{ required: true, message: 'يرجى إدخال رقم الشيك' }]}
              >
                <Input
                  placeholder="CHK000001"
                  addonAfter={
                    <Button
                      type="link"
                      size="small"
                      onClick={generateCheckNumber}
                    >
                      توليد
                    </Button>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="bank_account_id"
                label="الحساب المصرفي"
                rules={[{ required: true, message: 'يرجى اختيار الحساب المصرفي' }]}
              >
                <Select placeholder="اختر الحساب المصرفي">
                  {bankAccounts.map((account: any) => (
                    <Select.Option key={account.id} value={account.id}>
                      {account.bank_name} - {account.account_name} ({account.account_number})
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="amount"
                label="المبلغ"
                rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  formatter={value => ('₪ ' + value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value: string | undefined) => {
                    if (!value) return 0;
                    const parsed = parseFloat(value.replace(/₪\s?|(,*)/g, ''));
                    return isNaN(parsed) ? 0 : parsed;
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="payee_selection_type"
                label="نوع المستفيد"
                rules={[{ required: true, message: 'يرجى اختيار نوع المستفيد' }]}
              >
                <Select
                  placeholder="اختر نوع المستفيد"
                  onChange={(_value) => {
                    // إعادة تعيين الحقول عند تغيير النوع
                    form.setFieldsValue({
                      payee_name: undefined,
                      entity_id: undefined,
                      entity_type: undefined,
                      entity_name: undefined
                    })
                  }}
                >
                  <Select.Option value="customer">عميل</Select.Option>
                  <Select.Option value="supplier">مورد</Select.Option>
                  <Select.Option value="manual">إدخال يدوي</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.payee_selection_type !== currentValues.payee_selection_type
                }
              >
                {({ getFieldValue }) => {
                  const selectionType = getFieldValue('payee_selection_type')

                  if (selectionType === 'manual') {
                    return (
                      <Form.Item
                        name="payee_name"
                        label="اسم المستفيد"
                        rules={[{ required: true, message: 'يرجى إدخال اسم المستفيد' }]}
                      >
                        <Input placeholder="اسم الشخص أو الشركة" />
                      </Form.Item>
                    )
                  }

                  if (selectionType === 'customer' || selectionType === 'supplier') {
                    const options = selectionType === 'customer' ? customers : suppliers
                    const label = selectionType === 'customer' ? 'العميل' : 'المورد'

                    return (
                      <Form.Item
                        name="entity_id"
                        label={`اختر ${label}`}
                        rules={[{ required: true, message: `يرجى اختيار ${label}` }]}
                      >
                        <Select
                          placeholder={`اختر ${label}`}
                          showSearch
                          filterOption={(input, option) =>
                            (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                          }
                          onChange={(value, _option) => {
                            const selectedEntity = options.find((item: any) => item.id === value)
                            if (selectedEntity) {
                              form.setFieldsValue({
                                payee_name: selectedEntity.name,
                                entity_type: selectionType,
                                entity_name: selectedEntity.name
                              })
                            }
                          }}
                        >
                          {options.map((item: any) => (
                            <Select.Option key={item.id} value={item.id}>
                              {item.name} ({item.code})
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    )
                  }

                  return null
                }}
              </Form.Item>
            </Col>
          </Row>

          {/* ربط الشيك بالعملاء والموردين */}
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="entity_type"
                label="نوع الجهة"
              >
                <Select
                  placeholder="اختر نوع الجهة"
                  allowClear
                  onChange={(_value) => {
                    form.setFieldsValue({ entity_id: undefined, entity_name: undefined })
                  }}
                >
                  <Select.Option value="customer">عميل</Select.Option>
                  <Select.Option value="supplier">مورد</Select.Option>
                  <Select.Option value="other">أخرى</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.entity_type !== currentValues.entity_type
                }
              >
                {({ getFieldValue }) => {
                  const entityType = getFieldValue('entity_type')
                  if (!entityType || entityType === 'other') return null

                  const options = entityType === 'customer' ? customers : suppliers
                  return (
                    <Form.Item
                      name="entity_id"
                      label={entityType === 'customer' ? 'العميل' : 'المورد'}
                    >
                      <Select
                        placeholder={`اختر ${entityType === 'customer' ? 'العميل' : 'المورد'}`}
                        allowClear
                        showSearch
                        filterOption={(input, option) =>
                          (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                        }
                        onChange={(value, _option) => {
                          const selectedEntity = options.find((item: any) => item.id === value)
                          if (selectedEntity) {
                            form.setFieldsValue({ entity_name: selectedEntity.name })
                          }
                        }}
                      >
                        {options.map((item: any) => (
                          <Select.Option key={item.id} value={item.id}>
                            {item.name} ({item.code})
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                  )
                }}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="entity_name"
                label="اسم الجهة"
              >
                <Input placeholder="اسم الجهة (يتم ملؤه تلقائياً)" readOnly />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.check_type !== currentValues.check_type
                }
              >
                {({ getFieldValue }) => {
                  const checkType = getFieldValue('check_type')
                  return checkType === 'received' ? (
                    <Form.Item
                      name="original_payer"
                      label="الدافع الأصلي"
                      rules={[{ required: true, message: 'يرجى إدخال اسم الدافع الأصلي' }]}
                    >
                      <Input placeholder="اسم الشخص أو الشركة الدافعة" />
                    </Form.Item>
                  ) : null
                }}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.check_type !== currentValues.check_type
                }
              >
                {({ getFieldValue }) => {
                  const checkType = getFieldValue('check_type')
                  return checkType === 'received' ? (
                    <Form.Item
                      name="current_holder"
                      label="الحامل الحالي"
                    >
                      <Input placeholder="الحامل الحالي للشيك (اختياري)" />
                    </Form.Item>
                  ) : null
                }}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="issue_date"
                label="تاريخ الإصدار"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الإصدار' }]}
              >
                <DatePicker 
                  style={{ width: '100%' }}
                  placeholder="اختر تاريخ الإصدار"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="due_date"
                label="تاريخ الاستحقاق"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الاستحقاق' }]}
              >
                <DatePicker 
                  style={{ width: '100%' }}
                  placeholder="اختر تاريخ الاستحقاق"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="ملاحّات"
          >
            <Input.TextArea 
              placeholder="ملاحّات إضافية حول الشيك"
              rows={3}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.check_type !== currentValues.check_type
                }
              >
                {({ getFieldValue }) => {
                  const checkType = getFieldValue('check_type')
                  return (
                    <Button type="primary" htmlType="submit">
                      {checkType === 'issued' ? 'إصدار شيك الشركة' : 'إضافة الشيك'}
                    </Button>
                  )
                }}
              </Form.Item>
              <Button onClick={() => {
                setModalVisible(false)
                setEditingCheck(null)
                form.resetFields()
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* مكون إدارة صور الشيك */}
      {selectedCheckForImages && (
        <CheckImageManager
          visible={imageManagerVisible}
          onClose={closeImageManager}
          checkId={selectedCheckForImages.id}
          checkNumber={selectedCheckForImages.check_number}
        />
      )}

      {/* مكون المسح الضوئي للشيك */}
      {selectedCheckForImages && (
        <CheckScanner
          visible={scannerVisible}
          onClose={closeScanner}
          checkId={selectedCheckForImages.id}
          onScanComplete={handleScanComplete}
        />
      )}

      {/* مكون الاستيراد والتصدير */}
      <CheckImportExport
        visible={importExportVisible}
        onClose={() => setImportExportVisible(false)}
        onImportComplete={() => {
          loadChecks() // إعادة تحميل الشيكات بعد الاستيراد
          setImportExportVisible(false)
        }}
      />
    </div>
  )
}

export default CheckManagement
