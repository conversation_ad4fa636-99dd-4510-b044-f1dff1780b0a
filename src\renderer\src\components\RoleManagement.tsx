import React, { useState, useEffect } from 'react'
import {
  Card, Table, Button, Space, Modal,
  message, Tag, Typography, Row, Col, Statistic, Descriptions,
  Collapse, Alert
} from 'antd'
import { SafeLogger as Logger } from '../utils/logger'
import {
  SafetyOutlined, PlusOutlined, EditOutlined, EyeOutlined,
  LockOutlined, SettingOutlined
} from '@ant-design/icons'
import { Role, Permission, User } from '../types/global'
import { DateUtils, DATE_FORMATS } from '../utils/dateConfig'

const { Title, Text } = Typography

interface RoleManagementProps {
  currentUser: User
}

const RoleManagement: React.FC<RoleManagementProps> = ({ currentUser }) => {
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(false)
  const [viewModalVisible, setViewModalVisible] = useState(false)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)

  useEffect(() => {
    loadRoles()
    loadPermissions()
  }, [])

  const loadRoles = async () => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getRoles()
        if (response.success && Array.isArray(response.data)) {
          setRoles(response.data)
        } else {
          Logger.error('RoleManagement', 'خطأ في استجابة الأدوار:', response)
          setRoles([])
        }
      }
    } catch (error) {
      message.error('فشل في تحميل الأدوار')
      Logger.error('RoleManagement', 'Error loading roles:', error)
      setRoles([])
    } finally {
      setLoading(false)
    }
  }

  const loadPermissions = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getPermissions()
        if (response.success && Array.isArray(response.data)) {
          setPermissions(response.data)
        } else {
          Logger.error('RoleManagement', 'خطأ في استجابة الصلاحيات:', response)
          setPermissions([])
        }
      }
    } catch (error) {
      message.error('فشل في تحميل الصلاحيات')
      Logger.error('RoleManagement', 'Error loading permissions:', error)
      setPermissions([])
    }
  }

  const handleViewRole = (role: Role) => {
    setSelectedRole(role)
    setViewModalVisible(true)
  }

  const getRoleColor = (roleName: string) => {
    switch (roleName) {
      case 'admin': return 'red'
      case 'manager': return 'blue'
      case 'accountant': return 'green'
      case 'warehouse': return 'orange'
      default: return 'default'
    }
  }

  const getRoleLabel = (roleName: string) => {
    switch (roleName) {
      case 'admin': return 'مدير النّام'
      case 'manager': return 'مدير'
      case 'accountant': return 'محاسب'
      case 'warehouse': return 'أمين مخزن'
      case 'user': return 'مستخدم'
      default: return roleName
    }
  }

  const parsePermissions = (permissionsStr: string) => {
    try {
      return JSON.parse(permissionsStr)
    } catch {
      return []
    }
  }

  const groupPermissionsByModule = () => {
    const grouped: { [key: string]: Permission[] } = {}
    permissions.forEach(permission => {
      if (!grouped[permission.module]) {
        grouped[permission.module] = []
      }
      grouped[permission.module].push(permission)
    })
    return grouped
  }

  const getModuleLabel = (module: string) => {
    const labels: { [key: string]: string } = {
      'dashboard': 'لوحة التحكم',
      'users': 'المستخدمين',
      'inventory': 'المخزون',
      'sales': 'المبيعات',
      'purchases': 'المشتريات',
      'reports': 'التقارير',
      'banking': 'البنوك',
      'profile': 'الملف الشخصي'
    }
    return labels[module] || module
  }

  const columns = [
    {
      title: 'الدور',
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => (
        <Space>
          <SafetyOutlined />
          <Tag color={getRoleColor(name)}>{getRoleLabel(name)}</Tag>
        </Space>
      )
    },
    {
      title: 'الوصف',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'عدد الصلاحيات',
      dataIndex: 'permissions',
      key: 'permissions',
      render: (permissions: string) => {
        const perms = parsePermissions(permissions)
        return (
          <Tag color="blue">
            {perms.includes('*') ? 'جميع الصلاحيات' : `${perms.length} صلاحية`}
          </Tag>
        )
      }
    },
    {
      title: 'تاريخ الإنشاء (ميلادي)',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 140,
      render: (date: string) => DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE)
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (record: Role) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewRole(record)}
          >
            عرض
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            disabled={record.name === 'admin' && currentUser.role !== 'admin'}
          >
            تعديل
          </Button>
        </Space>
      )
    }
  ]

  const stats = [
    {
      title: 'إجمالي الأدوار',
      value: roles.length,
      icon: <SafetyOutlined />
    },
    {
      title: 'إجمالي الصلاحيات',
      value: permissions.length,
      icon: <LockOutlined />
    },
    {
      title: 'الوحدات',
      value: Object.keys(groupPermissionsByModule()).length,
      icon: <SettingOutlined />
    }
  ]

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      {/* إحصائيات سريعة */}
      <Row gutter={[16, 16]}>
        {stats.map((stat, index) => (
          <Col xs={24} sm={8} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* جدول الأدوار */}
      <Card
        title={
          <Space>
            <SafetyOutlined />
            <Title level={4} style={{ margin: 0 }}>إدارة الأدوار والصلاحيات</Title>
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            disabled={currentUser.role !== 'admin'}
          >
            إضافة دور
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={roles}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showTotal: (total) => `إجمالي ${total} دور`
          }}
        />
      </Card>

      {/* الصلاحيات المتاحة */}
      <Card
        title={
          <Space>
            <LockOutlined />
            <Title level={4} style={{ margin: 0 }}>الصلاحيات المتاحة</Title>
          </Space>
        }
      >
        <Collapse
          items={Object.entries(groupPermissionsByModule()).map(([module, modulePermissions]) => ({
            key: module,
            label: (
              <Space>
                <SettingOutlined />
                <Text strong>{getModuleLabel(module)}</Text>
                <Tag color="blue">{modulePermissions.length} صلاحية</Tag>
              </Space>
            ),
            children: (
              <Row gutter={[16, 16]}>
                {modulePermissions.map(permission => (
                  <Col xs={24} sm={12} lg={8} key={permission.id}>
                    <Card size="small">
                      <Space direction="vertical" size="small">
                        <Text strong>{permission.name}</Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {permission.description}
                        </Text>
                        <Tag>{permission.action}</Tag>
                      </Space>
                    </Card>
                  </Col>
                ))}
              </Row>
            )
          }))}
        />
      </Card>

      {/* نافذة عرض تفاصيل الدور */}
      <Modal
        title={
          <Space>
            <SafetyOutlined />
            تفاصيل الدور: {selectedRole && getRoleLabel(selectedRole.name)}
          </Space>
        }
        open={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setViewModalVisible(false)}>
            إغلاق
          </Button>
        ]}
        width={800}
      >
        {selectedRole && (
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="اسم الدور">
                <Tag color={getRoleColor(selectedRole.name)}>
                  {getRoleLabel(selectedRole.name)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="تاريخ الإنشاء (ميلادي)">
                {DateUtils.formatForDisplay(selectedRole.created_at, DATE_FORMATS.DISPLAY_DATE)}
              </Descriptions.Item>
              <Descriptions.Item label="الوصف" span={2}>
                {selectedRole.description}
              </Descriptions.Item>
            </Descriptions>

            <div>
              <Title level={5}>الصلاحيات المخصصة:</Title>
              {(() => {
                const rolePermissions = parsePermissions(selectedRole.permissions)
                
                if (rolePermissions.includes('*')) {
                  return (
                    <Alert
                      message="جميع الصلاحيات"
                      description="هذا الدور يملك جميع الصلاحيات في النّام"
                      type="success"
                      showIcon
                    />
                  )
                }

                return (
                  <Space wrap>
                    {rolePermissions.map((perm: string, index: number) => (
                      <Tag key={index} color="blue">{perm}</Tag>
                    ))}
                  </Space>
                )
              })()}
            </div>
          </Space>
        )}
      </Modal>
    </Space>
  )
}

export default RoleManagement
