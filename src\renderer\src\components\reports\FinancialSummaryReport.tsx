/**
 * تقرير الملخص المالي - تقرير اختبار للقوالب الموحدة
 * تقرير مالي بسيط لاختبار نظام القوالب الموحدة الجديد
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const FinancialSummaryReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'financial_summary' as ReportType}
      title="تقرير الملخص المالي"
      description="ملخص مالي شامل يعرض أهم المؤشرات المالية والإحصائيات"
      showDateRange={true}
      showDepartmentFilter={false}
      showCategoryFilter={false}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="financial_summary_report"
      defaultFilters={{
        sortBy: 'date',
        sortOrder: 'desc'
      }}
    />
  )
}

export default FinancialSummaryReport
