import React, { useState } from 'react'
import { Card, Button, Row, Col, Typography } from 'antd'
import {
  ArrowLeftOutlined,
  BgColorsOutlined,
  FileTextOutlined,
  DollarOutlined,
  BarChartOutlined,
  SettingOutlined,
  CreditCardOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import PaintTypesManagement from './PaintTypesManagement'
import PaintOrdersManagement from './PaintOrdersManagement'
import PaintInvoicesManagement from './PaintInvoicesManagement'
import PaintPaymentManagement from './PaintPaymentManagement'
import PaintReports from './PaintReports'
import PaintEnhancedFeatures from './PaintEnhancedFeatures'

const { Title, Text } = Typography

const ModuleCard = styled(Card)`
  height: 180px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }
  
  .ant-card-body {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  }
  
  .module-icon {
    font-size: 42px;
    margin-bottom: 12px;
    color: #ff7875;
  }
  
  .module-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 6px;
    color: #262626;
  }
  
  .module-description {
    color: #666;
    font-size: 13px;
    line-height: 1.4;
  }
`

const TypesCard = styled(ModuleCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }
  
  .module-icon {
    color: #52c41a;
  }
`

const OrdersCard = styled(ModuleCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  }
  
  .module-icon {
    color: #1890ff;
  }
`

const InvoicesCard = styled(ModuleCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
  }
  
  .module-icon {
    color: #722ed1;
  }
`

const PaymentsCard = styled(ModuleCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }

  .module-icon {
    color: #13c2c2;
  }
`

const ReportsCard = styled(ModuleCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
  }

  .module-icon {
    color: #faad14;
  }
`

interface PaintManagementProps {
  onBack: () => void
  initialView?: string
}

const PaintManagement: React.FC<PaintManagementProps> = ({ onBack, initialView = 'main' }) => {
  const [activeView, setActiveView] = useState(initialView)

  // مراقبة تغيير initialView
  React.useEffect(() => {
    setActiveView(initialView)
  }, [initialView])

  const renderContent = () => {
    switch (activeView) {
      case 'types':
        return <PaintTypesManagement onBack={() => setActiveView('main')} />
      case 'orders':
        return <PaintOrdersManagement onBack={() => setActiveView('main')} />
      case 'invoices':
        return <PaintInvoicesManagement onBack={() => setActiveView('main')} />
      case 'payments':
        return <PaintPaymentManagement onBack={() => setActiveView('main')} />
      case 'reports':
        return <PaintReports onBack={() => setActiveView('main')} />
      case 'enhanced':
        return <PaintEnhancedFeatures onBack={() => setActiveView('main')} />
      default:
        return (
          <div style={{ padding: '24px' }}>
            <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <h1 style={{ margin: 0, color: '#ff7875', fontSize: '28px' }}>
                  <BgColorsOutlined style={{ marginLeft: '12px' }} />
                  إدارة قسم الدهان
                </h1>
                <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '16px' }}>
                  إدارة شاملة لأوامر الدهان والأصناف غير المخزنية
                </p>
              </div>
              <Button 
                icon={<ArrowLeftOutlined />} 
                onClick={onBack}
                size="large"
                style={{ borderRadius: '8px' }}
              >
                العودة للإنتاج
              </Button>
            </div>

            <Row gutter={[24, 24]}>
              <Col xs={24} sm={12} lg={6}>
                <TypesCard onClick={() => setActiveView('types')}>
                  <div className="ant-card-body">
                    <SettingOutlined className="module-icon" />
                    <div className="module-title">أنواع الدهانات</div>
                    <div className="module-description">
                      إدارة أنواع الدهانات والأسعار
                      <br />
                      تحديد السعر لكل متر مربع
                    </div>
                  </div>
                </TypesCard>
              </Col>

              <Col xs={24} sm={12} lg={6}>
                <OrdersCard onClick={() => setActiveView('orders')}>
                  <div className="ant-card-body">
                    <FileTextOutlined className="module-icon" />
                    <div className="module-title">أوامر الدهان</div>
                    <div className="module-description">
                      إنشاء وإدارة أوامر الدهان
                      <br />
                      حساب المساحة والتكلفة
                    </div>
                  </div>
                </OrdersCard>
              </Col>

              <Col xs={24} sm={12} lg={6}>
                <InvoicesCard onClick={() => setActiveView('invoices')}>
                  <div className="ant-card-body">
                    <DollarOutlined className="module-icon" />
                    <div className="module-title">فواتير الدهان</div>
                    <div className="module-description">
                      إنشاء فواتير من أوامر الدهان
                      <br />
                      ربط بالنّام المالي
                    </div>
                  </div>
                </InvoicesCard>
              </Col>

              <Col xs={24} sm={12} lg={6}>
                <PaymentsCard onClick={() => setActiveView('payments')}>
                  <div className="ant-card-body">
                    <CreditCardOutlined className="module-icon" />
                    <div className="module-title">مدفوعات الدهان</div>
                    <div className="module-description">
                      ربط فواتير الدهان بالنّام المالي
                      <br />
                      تسجيل الشيكات والسندات
                    </div>
                  </div>
                </PaymentsCard>
              </Col>

              <Col xs={24} sm={12} lg={6}>
                <ReportsCard onClick={() => setActiveView('reports')}>
                  <div className="ant-card-body">
                    <BarChartOutlined className="module-icon" />
                    <div className="module-title">تقارير الدهان</div>
                    <div className="module-description">
                      تقارير شاملة لعمليات الدهان
                      <br />
                      مشابهة لتقارير المبيعات
                    </div>
                  </div>
                </ReportsCard>
              </Col>

              <Col xs={24} sm={12} lg={6}>
                <ModuleCard onClick={() => setActiveView('enhanced')} style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
                  <div className="ant-card-body">
                    <div className="module-icon" style={{ color: '#fff' }}>⭐</div>
                    <div className="module-title" style={{ color: '#fff' }}>المميزات المحسنة</div>
                    <div className="module-description" style={{ color: '#f0f0f0' }}>
                      التنبيهات، التقويم، والصور
                      <br />
                      مميزات جديدة ومتطورة
                    </div>
                  </div>
                </ModuleCard>
              </Col>
            </Row>

            <div style={{ marginTop: '32px', padding: '24px', background: '#f5f5f5', borderRadius: '12px' }}>
              <Title level={4} style={{ color: '#ff7875', marginBottom: '16px' }}>
                <BgColorsOutlined style={{ marginLeft: '8px' }} />
                مميزات قسم الدهان
              </Title>
              
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <div style={{ padding: '16px', background: 'white', borderRadius: '8px', marginBottom: '12px' }}>
                    <Text strong style={{ color: '#52c41a' }}>✓ أصناف غير مخزنية:</Text>
                    <br />
                    <Text type="secondary">
                      العميل يأتي بقطع الأثاث للدهان، لا تحتاج لإدارة مخزون
                    </Text>
                  </div>
                </Col>
                
                <Col xs={24} md={12}>
                  <div style={{ padding: '16px', background: 'white', borderRadius: '8px', marginBottom: '12px' }}>
                    <Text strong style={{ color: '#1890ff' }}>✓ حساب تلقائي للتكلفة:</Text>
                    <br />
                    <Text type="secondary">
                      الطول × العرض × العدد × سعر نوع الدهان
                    </Text>
                  </div>
                </Col>
                
                <Col xs={24} md={12}>
                  <div style={{ padding: '16px', background: 'white', borderRadius: '8px', marginBottom: '12px' }}>
                    <Text strong style={{ color: '#722ed1' }}>✓ ربط بالفواتير:</Text>
                    <br />
                    <Text type="secondary">
                      تحويل أوامر الدهان إلى فواتير مالية تلقائياً
                    </Text>
                  </div>
                </Col>
                
                <Col xs={24} md={12}>
                  <div style={{ padding: '16px', background: 'white', borderRadius: '8px', marginBottom: '12px' }}>
                    <Text strong style={{ color: '#faad14' }}>✓ تقارير شاملة:</Text>
                    <br />
                    <Text type="secondary">
                      تقارير مفصلة مشابهة لتقارير المبيعات
                    </Text>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
        )
    }
  }

  return (
    <div>
      {renderContent()}
    </div>
  )
}

export default PaintManagement
