import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Alert,
  <PERSON>,
  But<PERSON>,
  Tag,
  Tooltip,
  Badge,
  message,
  Modal,
  Divider,
  Switch,
  Space,
  Row,
  Col,
  Form,
  Input,
  Select,
  Flex,
  Spin
} from 'antd';
import {
  BellOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { FiscalPeriod, ClosingNotification, NotificationSettings } from '../../src/types/fiscalPeriod';
import { fiscalPeriodApi } from '../../src/services/fiscalPeriodApi';

const { Title, Text } = Typography;

interface ClosingNotificationsProps {
  period: FiscalPeriod;
}

const ClosingNotifications: React.FC<ClosingNotificationsProps> = ({ period }) => {
  const [notifications, setNotifications] = useState<ClosingNotification[]>([]);
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [settingsModalOpen, setSettingsModalOpen] = useState(false);

  useEffect(() => {
    loadNotifications();
    loadSettings();
  }, [period.id]);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      const data = await fiscalPeriodApi.getClosingNotifications(period.id.toString());
      setNotifications(data);
    } catch (err) {
      setError('فشل في تحميل الإشعارات');
      message.error('فشل في تحميل الإشعارات');
      console.error('Error loading notifications:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadSettings = async () => {
    try {
      const data = await fiscalPeriodApi.getNotificationSettings();
      setSettings(data);
    } catch (err) {
      console.error('Error loading settings:', err);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await fiscalPeriodApi.markNotificationAsRead(notificationId);
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
    } catch (err) {
      console.error('Mark as read error:', err);
    }
  };

  const dismissNotification = async (notificationId: string) => {
    try {
      await fiscalPeriodApi.dismissNotification(notificationId);
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      message.success('تم إخفاء الإشعار');
    } catch (err) {
      console.error('Dismiss notification error:', err);
    }
  };

  const updateSettings = async (newSettings: NotificationSettings) => {
    try {
      await fiscalPeriodApi.updateNotificationSettings(newSettings);
      setSettings(newSettings);
      setSettingsModalOpen(false);
      message.success('تم حفظ إعدادات الإشعارات');
    } catch (err) {
      console.error('Update settings error:', err);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'error': return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'warning': return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'info': return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      default: return <BellOutlined />;
    }
  };

  const getPriorityTag = (priority: string) => {
    const colors = {
      high: 'error',
      medium: 'warning',
      low: 'success'
    } as const;

    const labels = {
      high: 'عالية',
      medium: 'متوسطة',
      low: 'منخفضة'
    };

    return (
      <Tag color={colors[priority as keyof typeof colors]}>
        {labels[priority as keyof typeof labels] || priority}
      </Tag>
    );
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleString('ar-SA');
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>جاري تحميل الإشعارات...</div>
      </div>
    );
  }

  return (
    <div>
      <Card>
        <div style={{ padding: '24px' }}>
          <Flex justify="space-between" align="center" style={{ marginBottom: 24 }}>
            <Title level={3}>
              <Badge count={unreadCount} offset={[10, 0]}>
                إشعارات الإقفال - {period.period_name}
              </Badge>
            </Title>
            <Button
              icon={<SettingOutlined />}
              onClick={() => setSettingsModalOpen(true)}
            >
              إعدادات الإشعارات
            </Button>
          </Flex>

          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              closable
              onClose={() => setError(null)}
              style={{ marginBottom: 16 }}
            />
          )}

          {notifications.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <BellOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
              <div style={{ marginTop: 16, color: '#999' }}>
                لا توجد إشعارات حالياً
              </div>
            </div>
          ) : (
            <List
              dataSource={notifications}
              renderItem={(notification) => (
                <List.Item
                  key={notification.id}
                  style={{
                    backgroundColor: notification.read ? 'transparent' : '#f6f6f6',
                    borderRadius: 8,
                    marginBottom: 8,
                    border: '1px solid #d9d9d9',
                    padding: 16
                  }}
                  actions={[
                    !notification.read && (
                      <Tooltip key="mark-read" title="تحديد كمقروء">
                        <Button
                          type="text"
                          size="small"
                          icon={<CheckCircleOutlined />}
                          onClick={() => markAsRead(notification.id)}
                        />
                      </Tooltip>
                    ),
                    <Tooltip key="dismiss" title="إخفاء الإشعار">
                      <Button
                        type="text"
                        size="small"
                        icon={<CloseOutlined />}
                        onClick={() => dismissNotification(notification.id)}
                        danger
                      />
                    </Tooltip>
                  ].filter(Boolean)}
                >
                  <List.Item.Meta
                    avatar={getNotificationIcon(notification.type)}
                    title={
                      <Flex align="center" gap="small">
                        <Text strong={!notification.read}>
                          {notification.title}
                        </Text>
                        {getPriorityTag((notification as any).priority || 'medium')}
                      </Flex>
                    }
                    description={
                      <div>
                        <Text type="secondary">{notification.message}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {formatDate((notification as any).createdAt || new Date().toISOString())}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </div>
      </Card>

      {/* Settings Modal */}
      <Modal
        title="إعدادات الإشعارات"
        open={settingsModalOpen}
        onCancel={() => setSettingsModalOpen(false)}
        footer={null}
        width={600}
      >
        <Form
          layout="vertical"
          initialValues={settings}
          onFinish={updateSettings}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="emailEnabled" valuePropName="checked">
                <Switch checkedChildren="تفعيل" unCheckedChildren="إيقاف" />
                <span style={{ marginLeft: 8 }}>إشعارات البريد الإلكتروني</span>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="smsEnabled" valuePropName="checked">
                <Switch checkedChildren="تفعيل" unCheckedChildren="إيقاف" />
                <span style={{ marginLeft: 8 }}>إشعارات الرسائل النصية</span>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Flex justify="end" gap="small">
              <Button onClick={() => setSettingsModalOpen(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                حفظ
              </Button>
            </Flex>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ClosingNotifications;
