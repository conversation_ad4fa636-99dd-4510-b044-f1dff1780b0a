<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ZET.IA - برنامج المحاسبة والإنتاج</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        direction: rtl;
        text-align: right;
      }
      
      * {
        box-sizing: border-box;
      }
    </style>
    <script>
      // تعريف المتغيرات المطلوبة لتجنب أخطاء undefined
      window.global = window.globalThis = window;

      // تعريف process مع جميع الخصائص المطلوبة
      if (typeof process === 'undefined') {
        window.process = {};
      }

      // التأكد من وجود جميع الخصائص المطلوبة
      window.process = Object.assign(window.process || {}, {
        env: { NODE_ENV: 'production' },
        version: 'v18.0.0',
        platform: 'win32',
        nextTick: function(callback) { setTimeout(callback, 0); },
        cwd: function() { return '/'; },
        argv: [],
        pid: 1,
        ppid: 0,
        title: 'browser',
        arch: 'x64',
        versions: {
          node: '18.0.0',
          v8: '10.0.0',
          uv: '1.0.0',
          zlib: '1.0.0',
          brotli: '1.0.0',
          ares: '1.0.0',
          modules: '108',
          nghttp2: '1.0.0',
          napi: '8',
          llhttp: '6.0.0',
          openssl: '3.0.0',
          cldr: '41.0',
          icu: '71.1',
          tz: '2022a',
          unicode: '14.0',
          electron: '28.3.3'
        }
      });

      // تعريف global process أيضاً
      window.global.process = window.process;

      // تعريف Buffer إذا لم يكن موجوداً
      if (typeof Buffer === 'undefined') {
        window.Buffer = {
          from: function(data) { return new Uint8Array(data); },
          alloc: function(size) { return new Uint8Array(size); },
          isBuffer: function() { return false; }
        };
      }

      // تعريف require إذا لم يكن موجوداً
      if (typeof require === 'undefined') {
        window.require = function(module) {
          console.warn('require() is not available in browser:', module);
          return {};
        };
      }
    </script>

    <script>
      // إضافة معالج أخطاء شامل لتجنب أخطاء undefined
      window.addEventListener('error', function(event) {
        console.error('خطأ في التطبيق:', event.error);
        console.error('المصدر:', event.filename, 'السطر:', event.lineno);

        // معالجة خاصة لأخطاء version
        if (event.error && event.error.message && event.error.message.includes('version')) {
          console.warn('🔧 محاولة إصلاح مشكلة الإصدار...');

          // التأكد من تعريف process.versions بشكل صحيح
          if (!window.process || !window.process.versions) {
            window.process = window.process || {};
            window.process.versions = {
              node: '18.0.0',
              v8: '10.0.0',
              electron: '28.3.3',
              chrome: '120.0.0',
              modules: '108'
            };
            console.log('✅ تم إصلاح تعريف process.versions');
          }
        }

        // منع إيقاف التطبيق بسبب أخطاء JavaScript
        event.preventDefault();
        return true;
      });

      window.addEventListener('unhandledrejection', function(event) {
        console.error('Promise مرفوض:', event.reason);
        event.preventDefault();
      });

      // فحص دوري للتأكد من سلامة التعريفات
      setInterval(function() {
        if (!window.process || !window.process.versions) {
          console.warn('🔧 إعادة تعريف process.versions...');
          window.process = window.process || {};
          window.process.versions = window.process.versions || {
            node: '18.0.0',
            v8: '10.0.0',
            electron: '28.3.3',
            chrome: '120.0.0',
            modules: '108'
          };
        }
      }, 1000);
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="./src/main.tsx"></script>
  </body>
</html>
