import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  DatePicker,
  Select,
  Button,
  Space,
  Typography,
  Divider,
  Tag,
  Tooltip,
  Popover,
  InputNumber,
  Switch,
  message
} from 'antd'
import { SafeLogger as Logger } from '../../../utils/logger'
import {
  FilterOutlined,
  ClearOutlined,
  SaveOutlined,
  HistoryOutlined,
  SearchOutlined,
  SettingOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import type { Dayjs } from 'dayjs'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

interface AdvancedFiltersProps {
  onFiltersChange: (filters: ProductionFilters) => void
  onReset: () => void
  departments: any[]
  customers: any[]
  items: any[]
}

export interface ProductionFilters {
  dateRange?: [Dayjs, Dayjs] | null
  status?: string[]
  priority?: string[]
  department_id?: number[]
  customer_id?: number[]
  item_id?: number[]
  estimated_cost_range?: [number, number] | null
  estimated_hours_range?: [number, number] | null
  search_text?: string
  show_completed?: boolean
  show_cancelled?: boolean
}

interface SavedFilter {
  id: string
  name: string
  filters: ProductionFilters
  created_at: string
}

const ProductionAdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  onFiltersChange,
  onReset,
  departments,
  customers,
  items
}) => {
  const [filters, setFilters] = useState<ProductionFilters>({
    show_completed: true,
    show_cancelled: false
  })
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([])
  const [filterName, setFilterName] = useState('')
  const [showAdvanced, setShowAdvanced] = useState(false)

  useEffect(() => {
    loadSavedFilters()
  }, [])

  const loadSavedFilters = () => {
    try {
      const saved = localStorage.getItem('production_saved_filters')
      if (saved) {
        setSavedFilters(JSON.parse(saved))
      }
    } catch (error) {
      Logger.error('ProductionAdvancedFilters', 'خطأ في تحميل الفلاتر المحفوّة:', error)
    }
  }

  const saveFilter = () => {
    if (!filterName.trim()) {
      message.warning('يرجى إدخال اسم للفلتر')
      return
    }

    const newFilter: SavedFilter = {
      id: Date.now().toString(),
      name: filterName.trim(),
      filters: { ...filters },
      created_at: new Date().toISOString()
    }

    const updatedFilters = [...savedFilters, newFilter]
    setSavedFilters(updatedFilters)
    localStorage.setItem('production_saved_filters', JSON.stringify(updatedFilters))
    setFilterName('')
    message.success('تم حفّ الفلتر بنجاح')
  }

  const loadFilter = (savedFilter: SavedFilter) => {
    setFilters(savedFilter.filters)
    onFiltersChange(savedFilter.filters)
    message.success(`تم تطبيق فلتر: ${savedFilter.name}`)
  }

  const deleteFilter = (filterId: string) => {
    const updatedFilters = savedFilters.filter(f => f.id !== filterId)
    setSavedFilters(updatedFilters)
    localStorage.setItem('production_saved_filters', JSON.stringify(updatedFilters))
    message.success('تم حذف الفلتر')
  }

  const handleFilterChange = (key: keyof ProductionFilters, value: any) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    onFiltersChange(newFilters)
  }

  const handleReset = () => {
    const resetFilters: ProductionFilters = {
      show_completed: true,
      show_cancelled: false
    }
    setFilters(resetFilters)
    onReset()
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.dateRange) count++
    if (filters.status?.length) count++
    if (filters.priority?.length) count++
    if (filters.department_id?.length) count++
    if (filters.customer_id?.length) count++
    if (filters.item_id?.length) count++
    if (filters.estimated_cost_range) count++
    if (filters.estimated_hours_range) count++
    if (filters.search_text) count++
    return count
  }

  const statusOptions = [
    { value: 'pending', label: 'في الانتّار', color: 'orange' },
    { value: 'in_progress', label: 'قيد التنفيذ', color: 'blue' },
    { value: 'completed', label: 'مكتمل', color: 'green' },
    { value: 'cancelled', label: 'ملغي', color: 'red' },
    { value: 'on_hold', label: 'معلق', color: 'purple' }
  ]

  const priorityOptions = [
    { value: 'low', label: 'منخفضة', color: 'green' },
    { value: 'normal', label: 'عادية', color: 'blue' },
    { value: 'high', label: 'عالية', color: 'orange' },
    { value: 'urgent', label: 'عاجلة', color: 'red' }
  ]

  return (
    <Card 
      title={
        <Space>
          <FilterOutlined />
          <span>الفلاتر المتقدمة</span>
          {getActiveFiltersCount() > 0 && (
            <Tag color="blue">{getActiveFiltersCount()} فلتر نشط</Tag>
          )}
        </Space>
      }
      extra={
        <Space>
          <Button
            size="small"
            onClick={() => setShowAdvanced(!showAdvanced)}
            icon={<SettingOutlined />}
          >
            {showAdvanced ? 'إخفاء المتقدم' : 'عرض المتقدم'}
          </Button>
          <Button
            size="small"
            onClick={handleReset}
            icon={<ClearOutlined />}
          >
            إعادة تعيين
          </Button>
        </Space>
      }
      style={{ marginBottom: '16px' }}
    >
      {/* الفلاتر الأساسية */}
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col span={6}>
          <Text strong>فترة التاريخ:</Text>
          <RangePicker
            value={filters.dateRange}
            onChange={(dates) => handleFilterChange('dateRange', dates)}
            style={{ width: '100%', marginTop: '4px' }}
            placeholder={['من تاريخ', 'إلى تاريخ']}
          />
        </Col>
        <Col span={6}>
          <Text strong>الحالة:</Text>
          <Select
            mode="multiple"
            value={filters.status}
            onChange={(value) => handleFilterChange('status', value)}
            style={{ width: '100%', marginTop: '4px' }}
            placeholder="اختر الحالات"
            allowClear
          >
            {statusOptions.map(option => (
              <Option key={option.value} value={option.value}>
                <Tag color={option.color} style={{ margin: 0 }}>
                  {option.label}
                </Tag>
              </Option>
            ))}
          </Select>
        </Col>
        <Col span={6}>
          <Text strong>الأولوية:</Text>
          <Select
            mode="multiple"
            value={filters.priority}
            onChange={(value) => handleFilterChange('priority', value)}
            style={{ width: '100%', marginTop: '4px' }}
            placeholder="اختر الأولويات"
            allowClear
          >
            {priorityOptions.map(option => (
              <Option key={option.value} value={option.value}>
                <Tag color={option.color} style={{ margin: 0 }}>
                  {option.label}
                </Tag>
              </Option>
            ))}
          </Select>
        </Col>
        <Col span={6}>
          <Text strong>القسم:</Text>
          <Select
            mode="multiple"
            value={filters.department_id}
            onChange={(value) => handleFilterChange('department_id', value)}
            style={{ width: '100%', marginTop: '4px' }}
            placeholder="اختر الأقسام"
            allowClear
          >
            {departments.map(dept => (
              <Option key={dept.id} value={dept.id}>
                {dept.name}
              </Option>
            ))}
          </Select>
        </Col>
      </Row>

      {/* الفلاتر المتقدمة */}
      {showAdvanced && (
        <>
          <Divider orientation="left">فلاتر متقدمة</Divider>
          <Row gutter={16} style={{ marginBottom: '16px' }}>
            <Col span={6}>
              <Text strong>العميل:</Text>
              <Select
                mode="multiple"
                value={filters.customer_id}
                onChange={(value) => handleFilterChange('customer_id', value)}
                style={{ width: '100%', marginTop: '4px' }}
                placeholder="اختر العملاء"
                allowClear
              >
                {customers.map(customer => (
                  <Option key={customer.id} value={customer.id}>
                    {customer.name}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Text strong>الصنف:</Text>
              <Select
                mode="multiple"
                value={filters.item_id}
                onChange={(value) => handleFilterChange('item_id', value)}
                style={{ width: '100%', marginTop: '4px' }}
                placeholder="اختر الأصناف"
                allowClear
                showSearch
                optionFilterProp="children"
                filterOption={(input, option) =>
                  option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                }
              >
                {items.map(item => (
                  <Option key={item.id} value={item.id}>
                    {item.name} ({item.code})
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Text strong>نطاق التكلفة (₪):</Text>
              <Space.Compact style={{ width: '100%', marginTop: '4px' }}>
                <InputNumber
                  placeholder="من"
                  value={filters.estimated_cost_range?.[0]}
                  onChange={(value) => {
                    const range = filters.estimated_cost_range || [0, 0]
                    handleFilterChange('estimated_cost_range', [value || 0, range[1]])
                  }}
                  style={{ width: '50%' }}
                />
                <InputNumber
                  placeholder="إلى"
                  value={filters.estimated_cost_range?.[1]}
                  onChange={(value) => {
                    const range = filters.estimated_cost_range || [0, 0]
                    handleFilterChange('estimated_cost_range', [range[0], value || 0])
                  }}
                  style={{ width: '50%' }}
                />
              </Space.Compact>
            </Col>
            <Col span={6}>
              <Text strong>نطاق الساعات:</Text>
              <Space.Compact style={{ width: '100%', marginTop: '4px' }}>
                <InputNumber
                  placeholder="من"
                  value={filters.estimated_hours_range?.[0]}
                  onChange={(value) => {
                    const range = filters.estimated_hours_range || [0, 0]
                    handleFilterChange('estimated_hours_range', [value || 0, range[1]])
                  }}
                  style={{ width: '50%' }}
                />
                <InputNumber
                  placeholder="إلى"
                  value={filters.estimated_hours_range?.[1]}
                  onChange={(value) => {
                    const range = filters.estimated_hours_range || [0, 0]
                    handleFilterChange('estimated_hours_range', [range[0], value || 0])
                  }}
                  style={{ width: '50%' }}
                />
              </Space.Compact>
            </Col>
          </Row>

          <Row gutter={16} style={{ marginBottom: '16px' }}>
            <Col span={12}>
              <Space>
                <Text strong>عرض المكتملة:</Text>
                <Switch
                  checked={filters.show_completed}
                  onChange={(checked) => handleFilterChange('show_completed', checked)}
                />
                <Text strong>عرض الملغية:</Text>
                <Switch
                  checked={filters.show_cancelled}
                  onChange={(checked) => handleFilterChange('show_cancelled', checked)}
                />
              </Space>
            </Col>
          </Row>
        </>
      )}

      {/* حفّ وتحميل الفلاتر */}
      <Divider orientation="left">إدارة الفلاتر</Divider>
      <Row gutter={16}>
        <Col span={12}>
          <Space.Compact style={{ width: '100%' }}>
            <Select
              placeholder="اسم الفلتر الجديد"
              value={filterName}
              onChange={setFilterName}
              style={{ width: '70%' }}
              showSearch
              allowClear
            />
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveFilter}
              disabled={!filterName.trim()}
            >
              حفّ
            </Button>
          </Space.Compact>
        </Col>
        <Col span={12}>
          {savedFilters.length > 0 && (
            <Popover
              title="الفلاتر المحفوّة"
              content={
                <div style={{ maxWidth: '300px' }}>
                  {savedFilters.map(filter => (
                    <div key={filter.id} style={{ marginBottom: '8px' }}>
                      <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                        <Button
                          type="link"
                          size="small"
                          onClick={() => loadFilter(filter)}
                          style={{ padding: 0 }}
                        >
                          {filter.name}
                        </Button>
                        <Button
                          type="text"
                          size="small"
                          danger
                          onClick={() => deleteFilter(filter.id)}
                        >
                          حذف
                        </Button>
                      </Space>
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {dayjs(filter.created_at).format('YYYY-MM-DD HH:mm')}
                      </Text>
                    </div>
                  ))}
                </div>
              }
              trigger="click"
            >
              <Button icon={<HistoryOutlined />}>
                الفلاتر المحفوّة ({savedFilters.length})
              </Button>
            </Popover>
          )}
        </Col>
      </Row>
    </Card>
  )
}

export default ProductionAdvancedFilters
