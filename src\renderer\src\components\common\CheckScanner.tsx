import React, { useState, useRef, useCallback } from 'react'
import {
  Mo<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Row,
  Col,
  message,
  Space,
  Select,
  Input,
  Progress,
  Alert,
  Typography,
  Divider,
  Image,
  Tag
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  ScanOutlined, FileImageOutlined,
  SaveOutlined,
  ReloadOutlined, CheckCircleOutlined} from '@ant-design/icons'
import type { CheckImage } from '../../types/global'

interface CheckScannerProps {
  visible: boolean
  onClose: () => void
  checkId?: number
  receiptId?: number
  onScanComplete?: (images: CheckImage[]) => void
}

const { Text } = Typography

const CheckScanner: React.FC<CheckScannerProps> = ({
  visible,
  onClose,
  checkId,
  receiptId,
  onScanComplete
}) => {
  const [scanning, setScanning] = useState(false)
  const [scannedImages, setScannedImages] = useState<any[]>([])
  const [currentSide, setCurrentSide] = useState<'front' | 'back' | 'deposit' | 'receipt'>('front')
  const [scanQuality, setScanQuality] = useState<'low' | 'medium' | 'high'>('high')
  const [scanProgress, setScanProgress] = useState(0)
  const [notes, setNotes] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  // خيارات جهة الشيك
  const sideOptions = [
    { value: 'front', label: 'وجه الشيك', color: 'blue' },
    { value: 'back', label: 'ّهر الشيك', color: 'green' },
    { value: 'deposit', label: 'إيصال الإيداع', color: 'orange' },
    { value: 'receipt', label: 'إقرار البنك', color: 'purple' }
  ]

  // خيارات جودة المسح
  const qualityOptions = [
    { value: 'low', label: 'منخفضة (150 DPI)', description: 'مناسبة للمعاينة السريعة' },
    { value: 'medium', label: 'متوسطة (300 DPI)', description: 'مناسبة للاستخدام العادي' },
    { value: 'high', label: 'عالية (600 DPI)', description: 'مناسبة للأرشفة والطباعة' }
  ]

  // محاكاة المسح الضوئي
  const simulateScan = useCallback(async () => {
    setScanning(true)
    setScanProgress(0)

    try {
      // محاكاة تقدم المسح
      for (let i = 0; i <= 100; i += 10) {
        setScanProgress(i)
        await new Promise(resolve => setTimeout(resolve, 200))
      }

      // إنشاء صورة وهمية للمسح
      const scannedImage = {
        id: Date.now(),
        name: `scan_${currentSide}_${Date.now()}.jpg`,
        path: `/scanned/checks/${checkId}/${currentSide}_${Date.now()}.jpg`,
        side: currentSide,
        quality: scanQuality,
        size: Math.floor(Math.random() * 2000000) + 500000, // حجم عشوائي
        timestamp: new Date().toISOString(),
        notes: notes
      }

      setScannedImages(prev => [...prev, scannedImage])
      message.success(`تم مسح ${getSideLabel(currentSide)} بنجاح`)
      
      // الانتقال للجهة التالية تلقائياً
      const currentIndex = sideOptions.findIndex(opt => opt.value === currentSide)
      if (currentIndex < sideOptions.length - 1) {
        setCurrentSide(sideOptions[currentIndex + 1].value as any)
      }
      
    } catch (error) {
      Logger.error('CheckScanner', 'خطأ في المسح الضوئي:', error)
      message.error('فشل في المسح الضوئي')
    } finally {
      setScanning(false)
      setScanProgress(0)
    }
  }, [currentSide, scanQuality, notes, checkId])

  // رفع صورة من الملفات
  const handleFile = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    Array.from(files).forEach((file: File) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        const uploadedImage = {
          id: Date.now() + Math.random(),
          name: file.name,
          path: e.target?.result as string,
          side: currentSide,
          quality: 'uploaded',
          size: file.size,
          timestamp: new Date().toISOString(),
          notes: notes,
          file: file
        }

        setScannedImages(prev => [...prev, uploadedImage])
        message.success(`تم رفع ${file.name} بنجاح`)
      }
      reader.readAsDataURL(file)
    })

    // إعادة تعيين input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // حذف صورة ممسوحة
  const removeScannedImage = (imageId: number | string) => {
    setScannedImages(prev => prev.filter(img => img.id !== imageId))
    message.success('تم حذف الصورة')
  }

  // الحصول على تسمية الجهة
  const getSideLabel = (side: string) => {
    const option = sideOptions.find(opt => opt.value === side)
    return option?.label || side
  }

  // الحصول على لون الجهة
  const getSideColor = (side: string) => {
    const option = sideOptions.find(opt => opt.value === side)
    return option?.color || 'default'
  }

  // حفّ جميع الصور الممسوحة
  const handleSaveAll = async () => {
    if (scannedImages.length === 0) {
      message.warning('لا توجد صور للحفّ')
      return
    }

    try {
      const savedImages: CheckImage[] = []

      for (const image of scannedImages) {
        const uploadData = {
          checkId: checkId,
          receiptId: receiptId,
          fileName: image.name,
          fileSize: image.size,
          fileType: image.file?.type || 'image/jpeg',
          fileData: image.path, // هذا base64 بالفعل
          imageSide: image.side,
          scanQuality: image.quality,
          notes: image.notes,
          image_name: image.name,
          image_path: ''
        }

        const response = await window.electronAPI?.uploadCheckImage(uploadData)
        if (response?.success) {
          savedImages.push(response.data)
        } else {
          throw new Error(`فشل في حفّ ${image.name}`)
        }
      }

      message.success(`تم حفّ ${savedImages.length} صورة بنجاح`)
      
      if (onScanComplete) {
        onScanComplete(savedImages)
      }
      
      // إعادة تعيين البيانات
      setScannedImages([])
      setNotes('')
      onClose()
      
    } catch (error) {
      Logger.error('CheckScanner', 'خطأ في حفّ الصور:', error)
      message.error('فشل في حفّ بعض الصور')
    }
  }

  // إعادة تعيين
  const handleReset = () => {
    setScannedImages([])
    setNotes('')
    setScanProgress(0)
    setCurrentSide('front')
  }

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <ScanOutlined />
          <span>مسح ضوئي للشيك</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={[
        <Button key="reset" onClick={handleReset} icon={<ReloadOutlined />}>
          إعادة تعيين
        </Button>,
        <Button key="cancel" onClick={onClose}>
          إلغاء
        </Button>,
        <Button 
          key="save" 
          type="primary" 
          onClick={handleSaveAll}
          disabled={scannedImages.length === 0}
          icon={<SaveOutlined />}
        >
          حفّ جميع الصور ({scannedImages.length})
        </Button>
      ]}
    >
      <Row gutter={[16, 16]}>
        {/* لوحة التحكم */}
        <Col xs={24} lg={8}>
          <Card title="إعدادات المسح" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>جهة الشيك:</Text>
                <Select
                  value={currentSide}
                  onChange={setCurrentSide}
                  style={{ width: '100%', marginTop: 4 }}
                >
                  {sideOptions.map(option => (
                    <Select.Option key={option.value} value={option.value}>
                      <Tag color={option.color}>{option.label}</Tag>
                    </Select.Option>
                  ))}
                </Select>
              </div>

              <div>
                <Text strong>جودة المسح:</Text>
                <Select
                  value={scanQuality}
                  onChange={setScanQuality}
                  style={{ width: '100%', marginTop: 4 }}
                >
                  {qualityOptions.map(option => (
                    <Select.Option key={option.value} value={option.value}>
                      <div>
                        <div>{option.label}</div>
                        <div style={{ fontSize: 11, color: '#666' }}>
                          {option.description}
                        </div>
                      </div>
                    </Select.Option>
                  ))}
                </Select>
              </div>

              <div>
                <Text strong>ملاحّات:</Text>
                <Input.TextArea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="ملاحّات اختيارية..."
                  rows={3}
                  style={{ marginTop: 4 }}
                />
              </div>

              <Divider />

              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  type="primary"
                  icon={<ScanOutlined />}
                  onClick={simulateScan}
                  loading={scanning}
                  block
                  size="large"
                >
                  {scanning ? 'جاري المسح...' : 'بدء المسح الضوئي'}
                </Button>

                <Button
                  icon={<FileImageOutlined />}
                  onClick={() => fileInputRef.current?.click()}
                  block
                >
                  رفع صورة من الملفات
                </Button>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  style={{ display: 'none' }}
                  onChange={handleFile }
                />
              </Space>

              {scanning && (
                <div>
                  <Text>تقدم المسح:</Text>
                  <Progress 
                    percent={scanProgress} 
                    size="small" 
                    status={scanProgress === 100 ? 'success' : 'active'}
                  />
                </div>
              )}
            </Space>
          </Card>
        </Col>

        {/* عرض الصور الممسوحة */}
        <Col xs={24} lg={16}>
          <Card 
            title={`الصور الممسوحة (${scannedImages.length})`}
            size="small"
            extra={
              scannedImages.length > 0 && (
                <Tag color="green">
                  <CheckCircleOutlined /> جاهز للحفّ
                </Tag>
              )
            }
          >
            {scannedImages.length === 0 ? (
              <div style={{ 
                textAlign: 'center', 
                padding: '40px 0',
                color: '#999'
              }}>
                <ScanOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <div>لا توجد صور ممسوحة بعد</div>
                <div style={{ fontSize: 12 }}>ابدأ بالمسح الضوئي أو رفع الصور</div>
              </div>
            ) : (
              <Row gutter={[16, 16]}>
                {scannedImages.map((image) => (
                  <Col xs={24} sm={12} md={8} key={image.id}>
                    <Card
                      size="small"
                      cover={
                        <div style={{ position: 'relative', height: 120 }}>
                          <Image
                            src={image.path}
                            alt={image.name}
                            style={{ 
                              width: '100%', 
                              height: '100%', 
                              objectFit: 'cover' 
                            }}
                            preview={true}
                          />
                          <Tag 
                            color={getSideColor(image.side)}
                            style={{ 
                              position: 'absolute', 
                              top: 4, 
                              right: 4,
                              fontSize: 10
                            }}
                          >
                            {getSideLabel(image.side)}
                          </Tag>
                        </div>
                      }
                      actions={[
                        <Button
                          key="delete"
                          type="text" 
                          danger 
                          size="small"
                          onClick={() => removeScannedImage(image.id)}
                        >
                          حذف
                        </Button>
                      ]}
                    >
                      <Card.Meta
                        title={
                          <div style={{ fontSize: 11, wordBreak: 'break-all' }}>
                            {image.name}
                          </div>
                        }
                        description={
                          <div style={{ fontSize: 10 }}>
                            <div>الجودة: {image.quality}</div>
                            <div>الحجم: {(image.size / 1024).toFixed(1)} KB</div>
                          </div>
                        }
                      />
                    </Card>
                  </Col>
                ))}
              </Row>
            )}
          </Card>
        </Col>
      </Row>

      {scannedImages.length > 0 && (
        <Alert
          message="نصيحة"
          description="تأكد من مسح جميع جهات الشيك المطلوبة قبل الحفّ. يمكنك إضافة ملاحّات لكل صورة."
          type="info"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </Modal>
  )
}

export default CheckScanner
