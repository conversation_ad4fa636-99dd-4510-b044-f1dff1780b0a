-- ═══════════════════════════════════════════════════════════════
-- س<PERSON><PERSON><PERSON><PERSON><PERSON> SQL لحذف جميع جداول نظام الصور
-- ═══════════════════════════════════════════════════════════════
-- 
-- تحذير: هذا السكريبت سيحذف جميع جداول الصور وبياناتها
-- تأكد من عمل نسخة احتياطية قبل التنفيذ!
--
-- ═══════════════════════════════════════════════════════════════

-- ═══════════════════════════════════════════════════════════════
-- المرحلة 1: حذف الجدول الموحد للصور
-- ═══════════════════════════════════════════════════════════════

-- حذف الفهارس أولاً (إن وجدت)
DROP INDEX IF EXISTS idx_unified_images_category;
DROP INDEX IF EXISTS idx_unified_images_context;
DROP INDEX IF EXISTS idx_unified_images_context_id;
DROP INDEX IF EXISTS idx_unified_images_is_primary;
DROP INDEX IF EXISTS idx_unified_images_uploaded_at;

-- حذف الجدول
DROP TABLE IF EXISTS unified_images;

-- ═══════════════════════════════════════════════════════════════
-- المرحلة 2: حذف جدول صور الأصناف
-- ═══════════════════════════════════════════════════════════════

-- حذ<PERSON> الفهارس أولاً (إن وجدت)
DROP INDEX IF EXISTS idx_item_images_item_id;
DROP INDEX IF EXISTS idx_item_images_is_primary;

-- حذف الجدول
DROP TABLE IF EXISTS item_images;

-- ═══════════════════════════════════════════════════════════════
-- المرحلة 3: حذف جدول صور أوامر الإنتاج
-- ═══════════════════════════════════════════════════════════════

-- حذف الفهارس أولاً (إن وجدت)
DROP INDEX IF EXISTS idx_production_order_images_order_id;
DROP INDEX IF EXISTS idx_production_order_images_category;
DROP INDEX IF EXISTS idx_production_order_images_is_primary;

-- حذف الجدول
DROP TABLE IF EXISTS production_order_images;

-- ═══════════════════════════════════════════════════════════════
-- المرحلة 4: حذف جدول صور العملاء
-- ═══════════════════════════════════════════════════════════════

-- حذف الفهارس أولاً (إن وجدت)
DROP INDEX IF EXISTS idx_customer_images_customer_id;
DROP INDEX IF EXISTS idx_customer_images_category;
DROP INDEX IF EXISTS idx_customer_images_is_primary;

-- حذف الجدول
DROP TABLE IF EXISTS customer_images;

-- ═══════════════════════════════════════════════════════════════
-- المرحلة 5: حذف جدول صور الشيكات
-- ═══════════════════════════════════════════════════════════════

-- حذف الفهارس أولاً (إن وجدت)
DROP INDEX IF EXISTS idx_check_images_check_id;
DROP INDEX IF EXISTS idx_check_images_receipt_id;
DROP INDEX IF EXISTS idx_check_images_image_side;

-- حذف الجدول
DROP TABLE IF EXISTS check_images;

-- ═══════════════════════════════════════════════════════════════
-- المرحلة 6: حذف أي جداول إضافية متعلقة بالصور (إن وجدت)
-- ═══════════════════════════════════════════════════════════════

-- جدول صور الفواتير (إن وجد)
DROP TABLE IF EXISTS invoice_images;

-- جدول صور الموردين (إن وجد)
DROP TABLE IF EXISTS supplier_images;

-- جدول صور المنتجات (إن وجد)
DROP TABLE IF EXISTS product_images;

-- جدول صور المواد الخام (إن وجد)
DROP TABLE IF EXISTS material_images;

-- جدول صور الجودة (إن وجد)
DROP TABLE IF EXISTS quality_images;

-- ═══════════════════════════════════════════════════════════════
-- التحقق من نجاح العملية
-- ═══════════════════════════════════════════════════════════════

-- عرض جميع الجداول المتبقية للتأكد من حذف جداول الصور
SELECT name FROM sqlite_master 
WHERE type='table' 
AND name LIKE '%image%'
ORDER BY name;

-- إذا كانت النتيجة فارغة، فقد تم حذف جميع جداول الصور بنجاح

-- ═══════════════════════════════════════════════════════════════
-- ملاحظات
-- ═══════════════════════════════════════════════════════════════

-- 1. هذا السكريبت يحذف الجداول فقط، لا يحذف ملفات الصور من نظام الملفات
-- 2. تأكد من حذف مجلد userData/images/ يدوياً
-- 3. قد تحتاج إلى إعادة تشغيل التطبيق بعد تنفيذ هذا السكريبت
-- 4. تأكد من عدم وجود Foreign Keys تشير إلى هذه الجداول

-- ═══════════════════════════════════════════════════════════════
-- نهاية السكريبت
-- ═══════════════════════════════════════════════════════════════

