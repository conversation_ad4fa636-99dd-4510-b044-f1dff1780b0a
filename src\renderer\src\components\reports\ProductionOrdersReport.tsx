/**
 * تقرير أوامر الإنتاج المحسن
 * تقرير شامل لأوامر الإنتاج باستخدام النظام الموحد
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const ProductionOrdersReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'production_orders' as ReportType}
      title="تقرير أوامر الإنتاج"
      description="تقرير شامل لجميع أوامر الإنتاج مع الحالات والأولويات والتكاليف"
      showDateRange={true}
      showDepartmentFilter={true}
      showStatusFilter={true}
      showCustomerFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="production_orders_report"
      defaultFilters={{
        sortBy: 'created_at',
        sortOrder: 'desc'
      }}
    />
  )
}

export default ProductionOrdersReport;
