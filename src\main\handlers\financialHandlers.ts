import { ipcMain } from 'electron'
import { FinancialValidator } from '../utils/validation'
import { Logger } from '../utils/logger'

// متغيرات الخدمات
let financialService: any = null

// دالة تعيين خدمة المالية
export function setFinancialService(service: any) {
  financialService = service
  Logger.info('FinancialHandlers', '🔧 تم تعيين الخدمة المالية:', service ? 'متاحة' : 'غير متاحة')
}

// تسجيل معالجات المالية
export function registerFinancialHandlers() {
  Logger.info('FinancialHandlers', '🔧 بدء تسجيل المعالجات المالية...')

  // الحسابات البنكية
  ipcMain.handle('get-bank-accounts', async () => {
    try {
      const accounts = await financialService.getBankAccounts()
      return { success: true, data: accounts }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب الحسابات البنكية:', error)
      return { success: false, message: 'حدث خطأ في جلب الحسابات البنكية' }
    }
  })

  ipcMain.handle('create-bank-account', async (_, accountData: any) => {
    try {
      // التحقق من صحة البيانات
      const validation = FinancialValidator.validateBankAccount(accountData)
      if (!validation.isValid) {
        Logger.info('FinancialHandlers', '🔍 فشل التحقق من صحة البيانات:', validation)
        const errorMessage = validation.errors && validation.errors.length > 0
          ? validation.errors.join(', ')
          : 'بيانات غير صحيحة'

        return {
          success: false,
          message: errorMessage,
          errors: validation.errors,
          warnings: validation.warnings
        }
      }

      const result = await financialService.createBankAccount(accountData)

      // إضافة التحذيرات إلى النتيجة إن وجدت
      if (validation.warnings && validation.warnings.length > 0) {
        result.warnings = validation.warnings
      }

      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء الحساب البنكي:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الحساب البنكي' }
    }
  })

  ipcMain.handle('update-bank-account', async (_, accountId: number, accountData: any) => {
    try {
      return await financialService.updateBankAccount(accountId, accountData)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تحديث الحساب البنكي:', error)
      return { success: false, message: 'حدث خطأ في تحديث الحساب البنكي' }
    }
  })

  ipcMain.handle('delete-bank-account', async (_, accountId: number) => {
    try {
      return await financialService.deleteBankAccount(accountId)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في حذف الحساب البنكي:', error)
      return { success: false, message: 'حدث خطأ في حذف الحساب البنكي' }
    }
  })

  // السندات
  ipcMain.handle('get-vouchers', async (_, voucherType?: 'receipt' | 'payment') => {
    try {
      const vouchers = await financialService.getVouchers(voucherType)
      return { success: true, data: vouchers }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب السندات:', error)
      return { success: false, message: 'حدث خطأ في جلب السندات' }
    }
  })

  ipcMain.handle('get-payment-vouchers', async () => {
    try {
      const vouchers = await financialService.getVouchers('payment')
      return { success: true, data: vouchers }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب سندات الدفع:', error)
      return { success: false, message: 'حدث خطأ في جلب سندات الدفع' }
    }
  })

  ipcMain.handle('get-receipt-vouchers', async () => {
    try {
      const vouchers = await financialService.getVouchers('receipt')
      return { success: true, data: vouchers }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب سندات القبض:', error)
      return { success: false, message: 'حدث خطأ في جلب سندات القبض' }
    }
  })

  ipcMain.handle('create-voucher', async (_, voucherData: any) => {
    try {
      // التحقق من صحة البيانات
      const validation = FinancialValidator.validateVoucher(voucherData)
      if (!validation.isValid) {
        return {
          success: false,
          message: 'بيانات غير صحيحة',
          errors: validation.errors,
          warnings: validation.warnings
        }
      }

      const result = await financialService.createVoucher(voucherData)

      // إضافة التحذيرات إلى النتيجة إن وجدت
      if (validation.warnings && validation.warnings.length > 0) {
        result.warnings = validation.warnings
      }

      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء السند:', error)
      return { success: false, message: 'حدث خطأ في إنشاء السند' }
    }
  })

  ipcMain.handle('generate-voucher-number', async (_, voucherType: 'receipt' | 'payment') => {
    try {
      const voucherNumber = await financialService.generateVoucherNumber(voucherType)
      return { success: true, data: { voucherNumber } }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في توليد رقم السند:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم السند' }
    }
  })

  // توليد رقم سند دفع
  ipcMain.handle('generate-payment-voucher-number', async () => {
    try {
      const result = await financialService.generatePaymentVoucherNumber()
      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في توليد رقم سند الدفع:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم سند الدفع' }
    }
  })

  // توليد رقم سند قبض
  ipcMain.handle('generate-receipt-voucher-number', async () => {
    try {
      const result = await financialService.generateReceiptVoucherNumber()
      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في توليد رقم سند القبض:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم سند القبض' }
    }
  })

  // إنشاء سند دفع
  ipcMain.handle('create-payment-voucher', async (_, voucherData: any) => {
    try {
      Logger.info('FinancialHandlers', 'إنشاء سند دفع:', voucherData)
      const result = await financialService.createPaymentVoucher(voucherData)
      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء سند الدفع:', error)
      return { success: false, message: 'حدث خطأ في إنشاء سند الدفع' }
    }
  })

  // إنشاء سند قبض
  ipcMain.handle('create-receipt-voucher', async (_, voucherData: any) => {
    try {
      Logger.info('FinancialHandlers', 'إنشاء سند قبض:', voucherData)
      const result = await financialService.createReceiptVoucher(voucherData)
      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء سند القبض:', error)
      return { success: false, message: 'حدث خطأ في إنشاء سند القبض' }
    }
  })

  // الشيكات
  ipcMain.handle('get-checks', async () => {
    try {
      const checks = await financialService.getChecks()
      return { success: true, data: checks }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب الشيكات:', error)
      return { success: false, message: 'حدث خطأ في جلب الشيكات' }
    }
  })

  ipcMain.handle('get-company-checks', async () => {
    try {
      const checks = await financialService.getChecks()
      return { success: true, data: checks.data || [] }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب شيكات الشركة:', error)
      return { success: false, message: 'حدث خطأ في جلب شيكات الشركة' }
    }
  })

  ipcMain.handle('create-check', async (_, checkData: any) => {
    try {
      // التحقق من صحة البيانات
      const validation = FinancialValidator.validateCheck(checkData)
      if (!validation.isValid) {
        return {
          success: false,
          message: 'بيانات غير صحيحة',
          errors: validation.errors,
          warnings: validation.warnings
        }
      }

      const result = await financialService.createCheck(checkData)

      // إضافة التحذيرات إلى النتيجة إن وجدت
      if (validation.warnings && validation.warnings.length > 0) {
        result.warnings = validation.warnings
      }

      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء الشيك:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الشيك' }
    }
  })

  ipcMain.handle('create-company-check', async (_event, checkData: any) => {
    try {
      return await financialService.createCheck(checkData)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء شيك الشركة:', error)
      return { success: false, message: 'حدث خطأ في إنشاء شيك الشركة' }
    }
  })

  // تحويلات الشيكات
  ipcMain.handle('get-transferable-checks', async () => {
    try {
      const checks = await financialService.getTransferableChecks()
      return { success: true, data: checks }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب الشيكات القابلة للتحويل:', error)
      return { success: false, message: 'حدث خطأ في جلب الشيكات القابلة للتحويل' }
    }
  })

  ipcMain.handle('get-check-transfers', async () => {
    try {
      const transfers = await financialService.getCheckTransfers()
      return { success: true, data: transfers }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب تحويلات الشيكات:', error)
      return { success: false, message: 'حدث خطأ في جلب تحويلات الشيكات' }
    }
  })

  ipcMain.handle('transfer-check', async (_, transferData: any) => {
    try {
      return await financialService.transferCheck(transferData)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تحويل الشيك:', error)
      return { success: false, message: 'حدث خطأ في تحويل الشيك' }
    }
  })

  // تم حذف المعالج المكرر get-entities-for-transfer - موجود في commonHandlers

  ipcMain.handle('get-check-history', async (_, checkId: number) => {
    try {
      const history = await financialService.getCheckHistory(checkId)
      return { success: true, data: history }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب تاريخ الشيك:', error)
      return { success: false, message: 'حدث خطأ في جلب تاريخ الشيك' }
    }
  })

  ipcMain.handle('create-check-transfer', async (_, transferData: any) => {
    try {
      // التحقق من صحة البيانات
      const validation = FinancialValidator.validateCheckTransfer(transferData)
      if (!validation.isValid) {
        return {
          success: false,
          message: 'بيانات غير صحيحة',
          errors: validation.errors,
          warnings: validation.warnings
        }
      }

      // استخدام دالة منفصلة لإنشاء تحويل الشيك مع معلومات إضافية
      const result = await financialService.createCheckTransfer(transferData)

      // إضافة التحذيرات إلى النتيجة إن وجدت
      if (validation.warnings && validation.warnings.length > 0) {
        result.warnings = validation.warnings
      }

      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء تحويل الشيك:', error)
      return { success: false, message: 'حدث خطأ في إنشاء تحويل الشيك' }
    }
  })

  // دليل الحسابات
  ipcMain.handle('get-chart-of-accounts', async () => {
    try {
      const accounts = await financialService.getChartOfAccounts()
      return { success: true, data: accounts }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب دليل الحسابات:', error)
      return { success: false, message: 'حدث خطأ في جلب دليل الحسابات' }
    }
  })

  ipcMain.handle('generate-account-code', async () => {
    try {
      const code = await financialService.generateAccountCode()
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في توليد كود الحساب:', error)
      return { success: false, message: 'حدث خطأ في توليد كود الحساب' }
    }
  })

  ipcMain.handle('create-account', async (_event, accountData: any) => {
    try {
      return await financialService.createAccount(accountData)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء الحساب:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الحساب' }
    }
  })

  ipcMain.handle('update-account', async (_event, accountId: number, accountData: any) => {
    try {
      return await financialService.updateAccount(accountId, accountData)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تحديث الحساب:', error)
      return { success: false, message: 'حدث خطأ في تحديث الحساب' }
    }
  })

  ipcMain.handle('delete-account', async (_event, accountId: number) => {
    try {
      return await financialService.deleteAccount(accountId)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في حذف الحساب:', error)
      return { success: false, message: 'حدث خطأ في حذف الحساب' }
    }
  })

  // إدارة صور الشيكات
  ipcMain.handle('get-check-images', async (_event, checkId: number) => {
    try {
      const images = await financialService.getCheckImages(checkId)
      return { success: true, data: images }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب صور الشيك:', error)
      return { success: false, message: 'حدث خطأ في جلب صور الشيك' }
    }
  })

  ipcMain.handle('upload-check-image', async (_event, imageData: any) => {
    try {
      const imageId = await financialService.addCheckImage(imageData)
      return { success: true, data: { id: imageId } }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في رفع صورة الشيك:', error)
      return { success: false, message: 'حدث خطأ في رفع صورة الشيك' }
    }
  })

  ipcMain.handle('delete-check-image', async (_event, imageId: number) => {
    try {
      await financialService.deleteCheckImage(imageId)
      return { success: true, message: 'تم حذف صورة الشيك بنجاح' }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في حذف صورة الشيك:', error)
      return { success: false, message: 'حدث خطأ في حذف صورة الشيك' }
    }
  })

  ipcMain.handle('update-check-image-notes', async (_event, data: { imageId: number; notes: string }) => {
    try {
      await financialService.updateCheckImageNotes(data.imageId, data.notes)
      return { success: true, message: 'تم تحديث ملاحّات الصورة بنجاح' }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تحديث ملاحّات صورة الشيك:', error)
      return { success: false, message: 'حدث خطأ في تحديث ملاحّات الصورة' }
    }
  })

  ipcMain.handle('generate-check-number', async () => {
    try {
      const checkNumber = await financialService.generateCheckNumber()
      return { success: true, data: { checkNumber } }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في توليد رقم الشيك:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم الشيك' }
    }
  })

  // التقارير المالية
  ipcMain.handle('get-trial-balance', async (_event, params?: any) => {
    try {
      const trialBalance = await financialService.getTrialBalance(params)
      return { success: true, data: trialBalance }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب ميزان المراجعة:', error)
      return { success: false, message: 'حدث خطأ في جلب ميزان المراجعة' }
    }
  })

  ipcMain.handle('get-balance-sheet', async (_event, params?: any) => {
    try {
      const balanceSheet = await financialService.getBalanceSheet(params)
      return { success: true, data: balanceSheet }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب الميزانية العمومية:', error)
      return { success: false, message: 'حدث خطأ في جلب الميزانية العمومية' }
    }
  })

  ipcMain.handle('get-income-statement', async (_event, params?: any) => {
    try {
      const incomeStatement = await financialService.getIncomeStatement(params)
      return { success: true, data: incomeStatement }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب قائمة الدخل:', error)
      return { success: false, message: 'حدث خطأ في جلب قائمة الدخل' }
    }
  })

  ipcMain.handle('get-cash-flow-statement', async (_, params?: any) => {
    try {
      const cashFlowStatement = await financialService.getCashFlowStatement(params)
      return { success: true, data: cashFlowStatement }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب قائمة التدفق النقدي:', error)
      return { success: false, message: 'حدث خطأ في جلب قائمة التدفق النقدي' }
    }
  })

  // تقارير مالية متقدمة
  ipcMain.handle('get-financial-summary', async (_, params?: any) => {
    try {
      const summary = await financialService.getFinancialSummary(params)
      return { success: true, data: summary }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب الملخص المالي:', error)
      return { success: false, message: 'حدث خطأ في جلب الملخص المالي' }
    }
  })

  ipcMain.handle('get-aging-report', async (_, params?: any) => {
    try {
      const agingReport = await financialService.getAgingReport(params)
      return { success: true, data: agingReport }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب تقرير الأعمار:', error)
      return { success: false, message: 'حدث خطأ في جلب تقرير الأعمار' }
    }
  })

  ipcMain.handle('get-cash-position-report', async (_, params?: any) => {
    try {
      const cashPosition = await financialService.getCashPositionReport(params)
      return { success: true, data: cashPosition }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب تقرير الوضع النقدي:', error)
      return { success: false, message: 'حدث خطأ في جلب تقرير الوضع النقدي' }
    }
  })

  ipcMain.handle('get-profitability-analysis', async (_, params?: any) => {
    try {
      const profitability = await financialService.getProfitabilityAnalysis(params)
      return { success: true, data: profitability }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب تحليل الربحية:', error)
      return { success: false, message: 'حدث خطأ في جلب تحليل الربحية' }
    }
  })

  ipcMain.handle('get-budget-variance-report', async (_, params?: any) => {
    try {
      const budgetVariance = await financialService.getBudgetVarianceReport(params)
      return { success: true, data: budgetVariance }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب تقرير انحراف الميزانية:', error)
      return { success: false, message: 'حدث خطأ في جلب تقرير انحراف الميزانية' }
    }
  })

  // معالجات الطباعة والتصدير المحسنة
  ipcMain.handle('export-financial-report', async (_, reportType: string, params?: any) => {
    try {
      const exportResult = await financialService.exportFinancialReport(reportType, params)
      return { success: true, data: exportResult }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تصدير التقرير المالي:', error)
      return { success: false, message: 'حدث خطأ في تصدير التقرير المالي' }
    }
  })

  ipcMain.handle('print-financial-report', async (_, reportType: string, params?: any) => {
    try {
      const printResult = await financialService.printFinancialReport(reportType, params)
      return { success: true, data: printResult }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في طباعة التقرير المالي:', error)
      return { success: false, message: 'حدث خطأ في طباعة التقرير المالي' }
    }
  })

  // حفّ التقرير كـ PDF
  ipcMain.handle('save-report-as-pdf', async (_, reportType: string, filePath: string, params?: any) => {
    try {
      const saveResult = await financialService.saveReportAsPDF(reportType, filePath, params)
      return { success: true, data: saveResult }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في حفّ التقرير كـ PDF:', error)
      return { success: false, message: 'حدث خطأ في حفّ التقرير كـ PDF' }
    }
  })

  // معاينة التقرير
  ipcMain.handle('preview-report', async (_, reportType: string, params?: any) => {
    try {
      const previewResult = await financialService.previewReport(reportType, params)
      return { success: true, data: previewResult }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في معاينة التقرير:', error)
      return { success: false, message: 'حدث خطأ في معاينة التقرير' }
    }
  })

  // تصدير التقرير إلى Excel
  ipcMain.handle('export-report-to-excel', async (_, reportType: string, params?: any) => {
    try {
      const { ExportService } = await import('../services/ExportService')
      const exportService = ExportService.getInstance()

      // الحصول على بيانات التقرير
      const reportData = await financialService.exportFinancialReport(reportType, params)

      // فحص البيانات الفارغة قبل التصدير
      const dataArray = Array.isArray(reportData.data) ? reportData.data : [reportData.data]
      if (!dataArray || dataArray.length === 0) {
        Logger.warn('FinancialHandlers', 'لا توجد بيانات للتصدير')
        return { success: false, message: 'لا توجد بيانات للتصدير إلى Excel' }
      }

      // تحضير البيانات للتصدير
      const exportData = {
        title: reportData.reportTitle,
        subtitle: reportData.reportSubtitle,
        data: dataArray,
        metadata: reportData.metadata
      }

      const exportResult = await exportService.exportToExcel(exportData, {
        format: 'excel',
        sheetName: reportData.reportTitle,
        includeHeaders: true
      })

      return { success: true, data: exportResult }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تصدير التقرير إلى Excel:', error)
      return { success: false, message: 'حدث خطأ في تصدير التقرير إلى Excel' }
    }
  })

  // تصدير التقرير إلى CSV
  ipcMain.handle('export-report-to-csv', async (_, reportType: string, params?: any) => {
    try {
      const { ExportService } = await import('../services/ExportService')
      const exportService = ExportService.getInstance()

      // الحصول على بيانات التقرير
      const reportData = await financialService.exportFinancialReport(reportType, params)

      // تحضير البيانات للتصدير
      const exportData = {
        title: reportData.reportTitle,
        subtitle: reportData.reportSubtitle,
        data: Array.isArray(reportData.data) ? reportData.data : [reportData.data],
        metadata: reportData.metadata
      }

      const exportResult = await exportService.exportToCSV(exportData, {
        format: 'csv',
        includeHeaders: true
      })

      return { success: true, data: exportResult }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تصدير التقرير إلى CSV:', error)
      return { success: false, message: 'حدث خطأ في تصدير التقرير إلى CSV' }
    }
  })

  ipcMain.handle('generate-financial-dashboard', async (_, params?: any) => {
    try {
      const dashboard = await financialService.generateFinancialDashboard(params)
      return { success: true, data: dashboard }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في توليد لوحة المعلومات المالية:', error)
      return { success: false, message: 'حدث خطأ في توليد لوحة المعلومات المالية' }
    }
  })

  // المعاملات المصرفية
  ipcMain.handle('get-bank-transactions', async () => {
    try {
      const transactions = await financialService.getBankTransactions()
      return { success: true, data: transactions }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب المعاملات المصرفية:', error)
      return { success: false, message: 'حدث خطأ في جلب المعاملات المصرفية' }
    }
  })

  ipcMain.handle('create-bank-transaction', async (_event, transactionData: any) => {
    try {
      return await financialService.createBankTransaction(transactionData)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء المعاملة المصرفية:', error)
      return { success: false, message: 'حدث خطأ في إنشاء المعاملة المصرفية' }
    }
  })

  // السندات الإذنية
  ipcMain.handle('get-promissory-notes', async () => {
    try {
      const notes = await financialService.getPromissoryNotes()
      return { success: true, data: notes }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب السندات الإذنية:', error)
      return { success: false, message: 'حدث خطأ في جلب السندات الإذنية' }
    }
  })

  ipcMain.handle('get-promissory-note-by-id', async (_, noteId: number) => {
    try {
      const note = await financialService.getPromissoryNoteById(noteId)
      return { success: true, data: note }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب السند الإذني:', error)
      return { success: false, message: 'حدث خطأ في جلب السند الإذني' }
    }
  })

  ipcMain.handle('create-promissory-note', async (_, noteData: any) => {
    try {
      // التحقق من صحة البيانات
      const validation = FinancialValidator.validatePromissoryNote(noteData)
      if (!validation.isValid) {
        return {
          success: false,
          message: 'بيانات غير صحيحة',
          errors: validation.errors,
          warnings: validation.warnings
        }
      }

      const result = await financialService.createPromissoryNote(noteData)

      // إضافة التحذيرات إلى النتيجة إن وجدت
      if (validation.warnings && validation.warnings.length > 0) {
        result.warnings = validation.warnings
      }

      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء السند الإذني:', error)
      return { success: false, message: 'حدث خطأ في إنشاء السند الإذني' }
    }
  })

  ipcMain.handle('update-promissory-note', async (_, noteId: number, noteData: any) => {
    try {
      return await financialService.updatePromissoryNote(noteId, noteData)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تحديث السند الإذني:', error)
      return { success: false, message: 'حدث خطأ في تحديث السند الإذني' }
    }
  })

  ipcMain.handle('delete-promissory-note', async (_, noteId: number) => {
    try {
      return await financialService.deletePromissoryNote(noteId)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في حذف السند الإذني:', error)
      return { success: false, message: 'حدث خطأ في حذف السند الإذني' }
    }
  })

  ipcMain.handle('transfer-promissory-note', async (_, transferData: any) => {
    try {
      return await financialService.transferPromissoryNote(transferData)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تحويل السند الإذني:', error)
      return { success: false, message: 'حدث خطأ في تحويل السند الإذني' }
    }
  })

  ipcMain.handle('generate-promissory-note-number', async () => {
    try {
      const noteNumber = await financialService.generatePromissoryNoteNumber()
      return { success: true, data: { noteNumber } }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في توليد رقم السند الإذني:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم السند الإذني' }
    }
  })

  // تم إزالة معالجات البيانات التجريبية - يجب استخدام البيانات الحقيقية فقط

  // المعالجات المالية الأساسية المفقودة
  ipcMain.handle('get-accounts', async (_, params?: any) => {
    try {
      const accounts = await financialService.getAccounts(params)
      return { success: true, data: accounts }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب الحسابات:', error)
      return { success: false, message: 'حدث خطأ في جلب الحسابات' }
    }
  })





  ipcMain.handle('get-transactions', async (_, params?: any) => {
    try {
      const transactions = await financialService.getTransactions(params)
      return { success: true, data: transactions }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب المعاملات:', error)
      return { success: false, message: 'حدث خطأ في جلب المعاملات' }
    }
  })

  ipcMain.handle('create-transaction', async (_, transactionData: any) => {
    try {
      return await financialService.createTransaction(transactionData)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء المعاملة:', error)
      return { success: false, message: 'حدث خطأ في إنشاء المعاملة' }
    }
  })

  ipcMain.handle('update-transaction', async (_, transactionData: any) => {
    try {
      return await financialService.updateTransaction(transactionData)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تحديث المعاملة:', error)
      return { success: false, message: 'حدث خطأ في تحديث المعاملة' }
    }
  })

  // المعالجات المفقودة للشيكات والكمبيالات
  ipcMain.handle('update-check', async (_, checkId: number, checkData: any) => {
    try {
      return await financialService.updateCheckStatus(checkId, checkData.status)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تحديث الشيك:', error)
      return { success: false, message: 'حدث خطأ في تحديث الشيك' }
    }
  })

  // معالج إضافي لتحديث حالة الشيك (للتوافق مع الواجهة الأمامية)
  ipcMain.handle('update-check-status', async (_, checkId: number, status: string) => {
    try {
      return await financialService.updateCheckStatus(checkId, status)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تحديث حالة الشيك:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة الشيك' }
    }
  })

  // تحديث حالة الكمبيالة مع تحديد الحساب البنكي
  ipcMain.handle('update-promissory-note-status', async (_, noteId: number, status: string, bankAccountId?: number) => {
    try {
      return await financialService.updatePromissoryNoteStatus(noteId, status, bankAccountId)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تحديث حالة الكمبيالة:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة الكمبيالة' }
    }
  })

  // تحصيل كمبيالة
  ipcMain.handle('collect-promissory-note', async (_, noteId: number, bankAccountId: number) => {
    try {
      return await financialService.collectPromissoryNote(noteId, bankAccountId)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تحصيل الكمبيالة:', error)
      return { success: false, message: 'حدث خطأ في تحصيل الكمبيالة' }
    }
  })

  // ملاحّة: دالة حذف الشيك غير متوفرة في FinancialService حالياً
  // يمكن إضافتها لاحقاً إذا لزم الأمر
  // تم حذف المعالج المكرر لـ update-promissory-note-status

  // طباعة الفواتير وأوامر الإنتاج
  ipcMain.handle('print-sales-invoice', async (_, invoiceId: number, params?: any) => {
    try {
      // ملاحّة: يجب إضافة هذه الدوال إلى FinancialService
      const printResult = await financialService.printFinancialReport('sales-invoice', {
        ...params,
        invoiceId
      })

      return { success: true, data: printResult }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في طباعة فاتورة المبيعات:', error)
      return { success: false, message: 'حدث خطأ في طباعة فاتورة المبيعات' }
    }
  })

  ipcMain.handle('print-purchase-invoice', async (_, invoiceId: number, params?: any) => {
    try {
      const printResult = await financialService.printFinancialReport('purchase-invoice', {
        ...params,
        invoiceId
      })

      return { success: true, data: printResult }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في طباعة فاتورة المشتريات:', error)
      return { success: false, message: 'حدث خطأ في طباعة فاتورة المشتريات' }
    }
  })

  ipcMain.handle('print-production-order', async (_, orderId: number, params?: any) => {
    try {
      const printResult = await financialService.printFinancialReport('production-order', {
        ...params,
        orderId
      })

      return { success: true, data: printResult }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في طباعة أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في طباعة أمر الإنتاج' }
    }
  })

  ipcMain.handle('print-paint-order', async (_, orderId: number, params?: any) => {
    try {
      const printResult = await financialService.printFinancialReport('paint-order', {
        ...params,
        orderId
      })

      return { success: true, data: printResult }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في طباعة أمر الدهان:', error)
      return { success: false, message: 'حدث خطأ في طباعة أمر الدهان' }
    }
  })

  // ==================== معالجات القيود اليومية ====================

  // إنشاء قيد يومي جديد
  ipcMain.handle('create-journal-entry', async (_event, entryData: any, userId?: number) => {
    try {
      return await financialService.createJournalEntry(entryData, userId)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في معالج إنشاء القيد اليومي:', error)
      return { success: false, message: 'حدث خطأ في إنشاء القيد اليومي' }
    }
  })

  // الحصول على القيود اليومية
  ipcMain.handle('get-journal-entries', async (_event, filters?: any) => {
    try {
      return await financialService.getJournalEntries(filters)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في معالج جلب القيود اليومية:', error)
      return []
    }
  })

  // تحديث قيد يومي
  ipcMain.handle('update-journal-entry', async (_event, entryId: number, entryData: any) => {
    try {
      return await financialService.updateJournalEntry(entryId, entryData)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في معالج تحديث القيد اليومي:', error)
      return { success: false, message: 'حدث خطأ في تحديث القيد اليومي' }
    }
  })

  // حذف قيد يومي
  ipcMain.handle('delete-journal-entry', async (_event, entryId: number) => {
    try {
      return await financialService.deleteJournalEntry(entryId)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في معالج حذف القيد اليومي:', error)
      return { success: false, message: 'حدث خطأ في حذف القيد اليومي' }
    }
  })

  // ترحيل قيد يومي
  ipcMain.handle('post-journal-entry', async (_event, entryId: number, userId?: number) => {
    try {
      return await financialService.postJournalEntry(entryId, userId)
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في معالج ترحيل القيد اليومي:', error)
      return { success: false, message: 'حدث خطأ في ترحيل القيد اليومي' }
    }
  })

  // توليد رقم قيد يومي
  ipcMain.handle('generate-journal-entry-number', async (_event) => {
    try {
      return await financialService.generateJournalEntryNumber()
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في معالج توليد رقم القيد:', error)
      return `JE${Date.now()}`
    }
  })

  // تصدير واستيراد الشيكات
  ipcMain.handle('export-checks', async (_, format: 'excel' | 'csv') => {
    try {
      const result = await financialService.exportChecks(format)
      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تصدير الشيكات:', error)
      return { success: false, message: 'حدث خطأ في تصدير الشيكات' }
    }
  })

  ipcMain.handle('import-checks', async (_, checksData: any[]) => {
    try {
      const result = await financialService.importChecks(checksData)
      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في استيراد الشيكات:', error)
      return { success: false, message: 'حدث خطأ في استيراد الشيكات' }
    }
  })

  // ==================== ربط الدفعات بالفواتير ====================

  // ربط دفعة بفاتورة
  ipcMain.handle('link-invoice-to-payment', async (_, linkData: any) => {
    try {
      const result = await financialService.linkInvoiceToPayment(linkData)
      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في ربط الدفعة بالفاتورة:', error)
      return { success: false, message: 'حدث خطأ في ربط الدفعة بالفاتورة' }
    }
  })

  // ==================== التقارير المالية المتقدمة ====================

  // تقرير أعمار ديون العملاء
  ipcMain.handle('get-customer-aging-report', async (_, params: any) => {
    try {
      const result = await financialService.getCustomerAgingReport(params)
      return { success: true, data: result }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب تقرير أعمار ديون العملاء:', error)
      return { success: false, message: 'حدث خطأ في جلب تقرير أعمار ديون العملاء' }
    }
  })

  // جلب الفواتير غير المدفوعة
  ipcMain.handle('get-unpaid-invoices', async (_, entityType?: string, entityId?: number) => {
    try {
      const result = await financialService.getUnpaidInvoices(entityType, entityId)
      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب الفواتير غير المدفوعة:', error)
      return { success: false, message: 'حدث خطأ في جلب الفواتير غير المدفوعة' }
    }
  })

  // جلب دفعات فاتورة محددة
  ipcMain.handle('get-invoice-payments', async (_, invoiceId: number, invoiceType: string) => {
    try {
      const result = await financialService.getInvoicePayments(invoiceId, invoiceType)
      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في جلب دفعات الفاتورة:', error)
      return { success: false, message: 'حدث خطأ في جلب دفعات الفاتورة' }
    }
  })

  // إنشاء دفعة للمورد
  ipcMain.handle('create-supplier-payment', async (_, paymentData: any) => {
    try {
      // إنشاء سند دفع
      const voucherData = {
        voucher_number: paymentData.payment_number,
        payee_name: paymentData.supplier_name || 'مورد',
        amount: paymentData.amount,
        payment_date: paymentData.payment_date,
        payment_method: paymentData.payment_method || 'cash',
        bank_account_id: paymentData.bank_account_id,
        check_number: paymentData.check_number,
        notes: paymentData.notes,
        reference_type: 'purchase_invoice',
        reference_id: paymentData.invoice_id,
        created_by: paymentData.created_by || 1
      }

      const voucherResult = await financialService.createPaymentVoucher(voucherData)

      if (voucherResult.success && paymentData.invoice_id) {
        // ربط الدفعة بالفاتورة
        const linkData = {
          invoice_type: 'purchase_invoice',
          invoice_id: paymentData.invoice_id,
          payment_type: 'payment_voucher',
          payment_id: voucherResult.data.voucherId,
          amount: paymentData.amount
        }

        const linkResult = await financialService.linkInvoiceToPayment(linkData)

        if (linkResult.success) {
          return {
            success: true,
            message: 'تم إنشاء دفعة المورد وربطها بالفاتورة بنجاح',
            data: {
              voucherId: voucherResult.data.voucherId,
              linkData: linkResult.data
            }
          }
        } else {
          return {
            success: false,
            message: 'تم إنشاء الدفعة ولكن فشل في ربطها بالفاتورة: ' + linkResult.message
          }
        }
      }

      return voucherResult
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء دفعة المورد:', error)
      return { success: false, message: 'حدث خطأ في إنشاء دفعة المورد' }
    }
  })

  // إنشاء دفعة للعميل
  ipcMain.handle('create-customer-payment', async (_, paymentData: any) => {
    try {
      // التحقق من وجود الخدمة المالية
      if (!financialService) {
        Logger.error('FinancialHandlers', 'الخدمة المالية غير متاحة')
        return { success: false, message: 'الخدمة المالية غير متاحة' }
      }

      Logger.info('FinancialHandlers', 'بدء إنشاء دفعة العميل:', paymentData)

      // إنشاء سند قبض
      const voucherData = {
        voucher_number: paymentData.payment_number,
        payer_name: paymentData.customer_name || 'عميل',
        amount: paymentData.amount,
        receipt_date: paymentData.payment_date,
        payment_method: paymentData.payment_method || 'cash',
        bank_account_id: paymentData.bank_account_id,
        check_number: paymentData.check_number,
        notes: paymentData.notes,
        reference_type: paymentData.invoice_type || 'sales_invoice',
        reference_id: paymentData.invoice_id,
        created_by: paymentData.created_by || 1
      }

      Logger.info('FinancialHandlers', 'بيانات السند:', voucherData)
      const voucherResult = await financialService.createReceiptVoucher(voucherData)
      Logger.info('FinancialHandlers', 'نتيجة إنشاء السند:', voucherResult)

      if (voucherResult.success && paymentData.invoice_id) {
        // ربط الدفعة بالفاتورة
        const linkData = {
          invoice_type: paymentData.invoice_type || 'sales_invoice',
          invoice_id: paymentData.invoice_id,
          payment_type: 'receipt_voucher',
          payment_id: voucherResult.data.voucherId,
          amount: paymentData.amount
        }

        Logger.info('FinancialHandlers', 'بيانات الربط:', linkData)
        const linkResult = await financialService.linkInvoiceToPayment(linkData)
        Logger.info('FinancialHandlers', 'نتيجة الربط:', linkResult)

        if (linkResult.success) {
          Logger.info('FinancialHandlers', 'تم إنشاء الدفعة وربطها بنجاح')
          return {
            success: true,
            message: 'تم إنشاء دفعة العميل وربطها بالفاتورة بنجاح',
            data: {
              voucherId: voucherResult.data.voucherId,
              linkData: linkResult.data
            }
          }
        } else {
          Logger.error('FinancialHandlers', 'فشل في ربط الدفعة بالفاتورة:', linkResult.message)
          return {
            success: false,
            message: 'تم إنشاء الدفعة ولكن فشل في ربطها بالفاتورة: ' + linkResult.message
          }
        }
      }

      Logger.info('FinancialHandlers', 'إرجاع نتيجة السند فقط')
      return voucherResult
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في إنشاء دفعة العميل:', error)
      return { success: false, message: 'حدث خطأ في إنشاء دفعة العميل: ' + (error instanceof Error ? error.message : String(error)) }
    }
  })

  // توليد رقم دفعة
  ipcMain.handle('generate-payment-number', async () => {
    try {
      const paymentNumber = await financialService.generatePaymentVoucherNumber()
      return {
        success: true,
        data: { paymentNumber: paymentNumber.data?.voucherNumber }
      }
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في توليد رقم الدفعة:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم الدفعة' }
    }
  })

  // تحديث الفواتير المتأخرة
  ipcMain.handle('update-overdue-invoices', async () => {
    try {
      const result = await financialService.updateOverdueInvoices()
      return result
    } catch (error) {
      Logger.error('FinancialHandlers', 'خطأ في تحديث الفواتير المتأخرة:', error)
      return { success: false, message: 'حدث خطأ في تحديث الفواتير المتأخرة' }
    }
  })

  Logger.info('FinancialHandlers', '✅ تم تسجيل جميع المعالجات المالية بنجاح، بما في ذلك create-customer-payment')
}
