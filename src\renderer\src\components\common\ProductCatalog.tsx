import React, { useState, useEffect } from 'react'
import {
  Modal,
  Card,
  Row,
  Col, Select,
  Button,
  Image,
  Tag,
  Space,
  Spin,
  Empty,
  Input,
  Divider,
  Tooltip,
  Badge} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import { ShoppingCartOutlined,
  EyeOutlined,
  AppstoreOutlined
} from '@ant-design/icons'
import type { Item, ItemImage } from '../../types/global'
import EnhancedProductCatalogPrint from './EnhancedProductCatalogPrint'

interface ProductCatalogProps {
  visible: boolean
  onClose: () => void
  onSelectProduct?: (item: Item) => void
  selectionMode?: boolean
  title?: string
}

// const { Title, Text } = Typography
const { Search } = Input

const ProductCatalog: React.FC<ProductCatalogProps> = ({
  visible,
  onClose,
  onSelectProduct,
  selectionMode = false,
  title = 'كتالوج المنتجات'
}) => {
  const [items, setItems] = useState<Item[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null)
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'category'>('name')
  // const [, setViewMode] = useState<'grid' | 'list'>('grid')
  const [itemImages, setItemImages] = useState<{ [key: number]: ItemImage[] }>({})

  // تحميل البيانات
  const loadData = async () => {
    setLoading(true)
    try {
      // تحميل الأصناف
      const itemsResponse = await window.electronAPI?.getItems()
      if (itemsResponse && (itemsResponse as any).success) {
        const itemsData = (itemsResponse as any).data || []
        const activeItems = itemsData.filter((item: Item) => item.is_active !== false)
        setItems(activeItems)

        // تحميل صور كل صنف
        const imagesMap: { [key: number]: ItemImage[] } = {}
        for (const item of activeItems) {
          try {
            const imagesResponse = await window.electronAPI?.getItemImages(item.id)
            if (imagesResponse?.success) {
              imagesMap[item.id] = imagesResponse.data || []
            }
          } catch (error) {
            Logger.error('ProductCatalog', 'خطأ في تحميل صور الصنف ${item.id}:', error)
          }
        }
        setItemImages(imagesMap)
      } else if (itemsResponse && Array.isArray(itemsResponse)) {
        // للتوافق مع الإصدارات القديمة
        const activeItems = itemsResponse.filter((item: Item) => item.is_active)
        setItems(activeItems)

        // تحميل صور كل صنف
        const imagesMap: { [key: number]: ItemImage[] } = {}
        for (const item of activeItems) {
          try {
            const imagesResponse = await window.electronAPI?.getItemImages(item.id)
            if (imagesResponse?.success) {
              imagesMap[item.id] = imagesResponse.data || []
            }
          } catch (error) {
            Logger.error('ProductCatalog', 'خطأ في تحميل صور الصنف ${item.id}:', error)
          }
        }
        setItemImages(imagesMap)
      }

      // تحميل الفئات
      const categoriesResponse = await window.electronAPI?.getCategories()
      if (categoriesResponse && Array.isArray(categoriesResponse)) {
        setCategories(categoriesResponse || [])
      }
    } catch (error) {
      Logger.error('ProductCatalog', 'خطأ في تحميل البيانات:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (visible) {
      loadData()
    }
  }, [visible])

  // تصفية وترتيب المنتجات
  const filteredAndSortedItems = React.useMemo(() => {
    const filtered = items.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))
      
      const matchesCategory = !selectedCategory || item.category_id === selectedCategory
      
      return matchesSearch && matchesCategory
    })

    switch (sortBy) {
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name))
        break
      case 'price':
        filtered.sort((a, b) => (b.sale_price || 0) - (a.sale_price || 0))
        break
      case 'category':
        filtered.sort((a, b) => (a.category_name || '').localeCompare(b.category_name || ''))
        break
    }

    return filtered
  }, [items, searchTerm, selectedCategory, sortBy])

  // الحصول على الصورة الرئيسية للصنف
  const getPrimaryImage = (itemId: number): ItemImage | null => {
    const images = itemImages[itemId] || []
    return images.find(img => img.is_primary) || images[0] || null
  }

  // عدد الصور للصنف
  const getImageCount = (itemId: number): number => {
    return itemImages[itemId]?.length || 0
  }

  // اختيار منتج
  const handleSelectProduct = (item: Item) => {
    if (selectionMode && onSelectProduct) {
      onSelectProduct(item)
      onClose()
    }
  }

  // معالج نجاح الطباعة
  const handlePrintSuccess = () => {
    // يمكن إضافة منطق إضافي هنا
  }

  // معالج خطأ الطباعة
  const handlePrintError = (error: string) => {
    Logger.error('ProductCatalog', 'خطأ في الطباعة:', error)
  }

  // عرض الشبكة
  const renderGridView = () => (
    <Row gutter={[16, 16]}>
      {filteredAndSortedItems.map((item) => {
        const primaryImage = getPrimaryImage(item.id)
        const imageCount = getImageCount(item.id)
        
        return (
          <Col xs={24} sm={12} md={8} lg={6} key={item.id}>
            <Card
              hoverable
              cover={
                <div style={{ position: 'relative', height: 200 }}>
                  {primaryImage ? (
                    <Image
                      src={primaryImage.image_path}
                      alt={item.name}
                      style={{ 
                        width: '100%', 
                        height: '100%', 
                        objectFit: 'cover' 
                      }}
                      preview={false}
                    />
                  ) : (
                    <div style={{ 
                      width: '100%', 
                      height: '100%', 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'center',
                      backgroundColor: '#f5f5f5',
                      color: '#999'
                    }}>
                      <AppstoreOutlined style={{ fontSize: 48 }} />
                    </div>
                  )}
                  
                  {imageCount > 0 && (
                    <Badge 
                      count={imageCount} 
                      style={{ 
                        position: 'absolute', 
                        top: 8, 
                        right: 8 
                      }} 
                    />
                  )}
                  
                  <Tag 
                    color="blue" 
                    style={{ 
                      position: 'absolute', 
                      bottom: 8, 
                      left: 8 
                    }}
                  >
                    {item.code}
                  </Tag>
                </div>
              }
              actions={selectionMode ? [
                <Tooltip key="select" title="اختيار المنتج">
                  <ShoppingCartOutlined
                    onClick={() => handleSelectProduct(item)}
                    style={{ color: '#1890ff' }}
                  />
                </Tooltip>
              ] : [
                <Tooltip key="view" title="عرض التفاصيل">
                  <EyeOutlined />
                </Tooltip>
              ]}
            >
              <Card.Meta
                title={
                  <div style={{ fontSize: 14 }}>
                    {item.name}
                  </div>
                }
                description={
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <div style={{ color: '#666', fontSize: 12 }}>
                      {item.category_name || 'بدون فئة'}
                    </div>
                    <div style={{ 
                      color: '#1890ff', 
                      fontSize: 16, 
                      fontWeight: 'bold' 
                    }}>
                      {item.sale_price?.toFixed(2) || 0} ج.م
                    </div>
                    {item.description && (
                      <div style={{ 
                        color: '#999', 
                        fontSize: 11,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {item.description}
                      </div>
                    )}
                  </Space>
                }
              />
            </Card>
          </Col>
        )
      })}
    </Row>
  )

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <AppstoreOutlined />
            <span>{title}</span>
            <Tag color="blue">{filteredAndSortedItems.length} منتج</Tag>
          </div>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width="90%"
      style={{ top: 20 }}
      footer={[
        <EnhancedProductCatalogPrint
          key="print"
          items={filteredAndSortedItems}
          itemImages={Object.values(itemImages).flat()}
          title="كتالوج المنتجات"
          onPrintSuccess={handlePrintSuccess}
          onPrintError={handlePrintError}
        />,
        <Button key="close" onClick={onClose}>
          إغلاق
        </Button>
      ]}
    >
      {/* شريط البحث والتصفية */}
      <div style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8}>
            <Search
              placeholder="البحث في المنتجات..."
              value={searchTerm}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={24} sm={6}>
            <Select
              placeholder="اختر الفئة"
              value={selectedCategory}
              onChange={setSelectedCategory}
              allowClear
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
              }
              style={{ width: '100%' }}
            >
              {categories.map(category => (
                <Select.Option key={category.id} value={category.id}>
                  {category.name}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <Select
              value={sortBy}
              onChange={setSortBy}
              style={{ width: '100%' }}
            >
              <Select.Option value="name">ترتيب بالاسم</Select.Option>
              <Select.Option value="price">ترتيب بالسعر</Select.Option>
              <Select.Option value="category">ترتيب بالفئة</Select.Option>
            </Select>
          </Col>
        </Row>
      </div>

      <Divider />

      {/* عرض المنتجات */}
      <Spin spinning={loading}>
        {filteredAndSortedItems.length === 0 ? (
          <Empty 
            description={searchTerm || selectedCategory ? 'لا توجد منتجات تطابق البحث' : 'لا توجد منتجات'}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          renderGridView()
        )}
      </Spin>
    </Modal>
  )
}

export default ProductCatalog
