import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Alert,
  message,
  Tooltip,
  Spin,
  Typography,
  Divider,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  LockOutlined,
  UnlockOutlined,
  EyeOutlined,
  DownloadOutlined,
  WarningOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { FiscalPeriod, CreateFiscalPeriodData, UpdateFiscalPeriodData } from '../../../renderer/src/types/fiscalPeriod';
import { fiscalPeriodApi } from '../../../renderer/src/services/fiscalPeriodApi';

const { Title } = Typography;
const { Option } = Select;

interface FiscalPeriodManagerProps {
  onPeriodSelect?: (period: FiscalPeriod) => void;
}

const FiscalPeriodManager: React.FC<FiscalPeriodManagerProps> = ({ onPeriodSelect }) => {
  const [periods, setPeriods] = useState<FiscalPeriod[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingPeriod, setEditingPeriod] = useState<FiscalPeriod | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadPeriods();
  }, []);

  const loadPeriods = async () => {
    try {
      setLoading(true);
      const response = await fiscalPeriodApi.getFiscalPeriods();
      if (response.success && response.data) {
        setPeriods(response.data);
      }
    } catch (err) {
      message.error('فشل في تحميل الفترات المالية');
      console.error('Error loading periods:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePeriod = () => {
    setEditingPeriod(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditPeriod = (period: FiscalPeriod) => {
    setEditingPeriod(period);
    form.setFieldsValue({
      period_name: period.period_name,
      period_type: period.period_type,
      start_date: dayjs(period.start_date),
      end_date: dayjs(period.end_date),
      notes: period.notes || ''
    });
    setIsModalVisible(true);
  };

  const handleSavePeriod = async (values: any) => {
    try {
      const periodData: CreateFiscalPeriodData = {
        period_name: values.period_name,
        period_type: values.period_type || 'annual',
        start_date: values.start_date.format('YYYY-MM-DD'),
        end_date: values.end_date.format('YYYY-MM-DD'),
        notes: values.notes
      };

      if (editingPeriod) {
        await fiscalPeriodApi.updateFiscalPeriod(editingPeriod.id, periodData);
        message.success('تم تحديث الفترة المالية بنجاح');
      } else {
        await fiscalPeriodApi.createFiscalPeriod(periodData);
        message.success('تم إنشاء الفترة المالية بنجاح');
      }
      
      setIsModalVisible(false);
      form.resetFields();
      loadPeriods();
    } catch (err) {
      message.error('فشل في حفظ الفترة المالية');
      console.error('Error saving period:', err);
    }
  };

  const handleClosePeriod = async (periodId: number) => {
    try {
      await fiscalPeriodApi.closeFiscalPeriod(periodId, 1); // userId = 1 for demo
      message.success('تم إقفال الفترة المالية بنجاح');
      loadPeriods();
    } catch (err) {
      message.error('فشل في إقفال الفترة المالية');
      console.error('Error closing period:', err);
    }
  };

  const handleReopenPeriod = async (periodId: number) => {
    try {
      await fiscalPeriodApi.reopenFiscalPeriod(periodId, 1); // userId = 1 for demo
      message.success('تم إعادة فتح الفترة المالية بنجاح');
      loadPeriods();
    } catch (err) {
      message.error('فشل في إعادة فتح الفترة المالية');
      console.error('Error reopening period:', err);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'success';
      case 'closed': return 'warning';
      case 'locked': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'open': return 'مفتوحة';
      case 'closed': return 'مقفلة';
      case 'locked': return 'مؤمنة';
      default: return status;
    }
  };

  const formatDate = (date: string) => {
    return dayjs(date).format('DD/MM/YYYY');
  };

  const formatPeriodType = (type: string) => {
    const types = {
      monthly: 'شهري',
      quarterly: 'ربع سنوي',
      semi_annual: 'نصف سنوي',
      annual: 'سنوي'
    };
    return types[type as keyof typeof types] || type;
  };

  const columns = [
    {
      title: 'اسم الفترة',
      dataIndex: 'period_name',
      key: 'period_name',
      render: (text: string) => <strong>{text}</strong>
    },
    {
      title: 'نوع الفترة',
      dataIndex: 'period_type',
      key: 'period_type',
      render: (type: string) => formatPeriodType(type)
    },
    {
      title: 'تاريخ البداية',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'تاريخ النهاية',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_, record: FiscalPeriod) => (
        <Space size="small">
          <Tooltip title="عرض التفاصيل">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => onPeriodSelect?.(record)}
            />
          </Tooltip>
          
          {record.status === 'open' && (
            <>
              <Tooltip title="تعديل">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => handleEditPeriod(record)}
                />
              </Tooltip>
              
              <Tooltip title="إقفال الفترة">
                <Popconfirm
                  title="هل أنت متأكد من إقفال هذه الفترة؟"
                  onConfirm={() => handleClosePeriod(record.id)}
                  okText="نعم"
                  cancelText="لا"
                >
                  <Button
                    type="text"
                    icon={<LockOutlined />}
                    danger
                  />
                </Popconfirm>
              </Tooltip>
            </>
          )}
          
          {record.status === 'closed' && (
            <Tooltip title="إعادة فتح">
              <Popconfirm
                title="هل أنت متأكد من إعادة فتح هذه الفترة؟"
                onConfirm={() => handleReopenPeriod(record.id)}
                okText="نعم"
                cancelText="لا"
              >
                <Button
                  type="text"
                  icon={<UnlockOutlined />}
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3}>إدارة الفترات المالية</Title>
            </Col>
            <Col>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreatePeriod}
              >
                إضافة فترة مالية جديدة
              </Button>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={periods}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} من ${total} فترة مالية`
          }}
        />
      </Card>

      <Modal
        title={editingPeriod ? 'تعديل الفترة المالية' : 'إضافة فترة مالية جديدة'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSavePeriod}
        >
          <Form.Item
            name="period_name"
            label="اسم الفترة"
            rules={[{ required: true, message: 'يرجى إدخال اسم الفترة' }]}
          >
            <Input placeholder="مثال: السنة المالية 2024" />
          </Form.Item>

          <Form.Item
            name="period_type"
            label="نوع الفترة"
            rules={[{ required: true, message: 'يرجى اختيار نوع الفترة' }]}
          >
            <Select placeholder="اختر نوع الفترة">
              <Option value="monthly">شهري</Option>
              <Option value="quarterly">ربع سنوي</Option>
              <Option value="semi_annual">نصف سنوي</Option>
              <Option value="annual">سنوي</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="start_date"
                label="تاريخ البداية"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ البداية' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="end_date"
                label="تاريخ النهاية"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ النهاية' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="ملاحظات"
          >
            <Input.TextArea rows={3} placeholder="ملاحظات إضافية (اختياري)" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingPeriod ? 'تحديث' : 'إضافة'}
              </Button>
              <Button onClick={() => {
                setIsModalVisible(false);
                form.resetFields();
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FiscalPeriodManager;
