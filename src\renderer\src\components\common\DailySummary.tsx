import React, { useState, useEffect } from 'react'
import { Modal, Card, Row, Col, Statistic, Typography, Space, Spin, Tag, Divider } from 'antd'
import {
  DashboardOutlined,
  TeamOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  WarningOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import { SafeLogger as Logger } from '../../utils/logger'

const { Text } = Typography

interface DailySummaryData {
  totalEmployees: number
  presentEmployees: number
  absentEmployees: number
  totalSales: number
  totalPurchases: number
  salesInvoices: number
  purchaseInvoices: number
  lowStockItems: number
  pendingTasks: number
}

interface DailySummaryProps {
  visible: boolean
  onClose: () => void
}

const DailySummary: React.FC<DailySummaryProps> = ({ visible, onClose }) => {
  const [data, setData] = useState<DailySummaryData | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (visible) {
      fetchDailySummary()
    }
  }, [visible])

  const fetchDailySummary = async () => {
    setLoading(true)
    try {
      const today = dayjs().format('YYYY-MM-DD')
      
      // جلب بيانات متعددة بشكل متوازي
      const [
        employeesResult,
        attendanceResult,
        salesResult,
        purchasesResult,
        inventoryResult
      ] = await Promise.all([
        window.electronAPI.getEmployees(),
        window.electronAPI.getAttendanceRecords(undefined, today, today),
        window.electronAPI.getSalesInvoices(),
        window.electronAPI.getPurchaseInvoices(),
        window.electronAPI.getItems()
      ])

      const totalEmployees = employeesResult.success ? employeesResult.data.length : 0
      const attendanceRecords = attendanceResult.success ? attendanceResult.data : []
      const presentEmployees = attendanceRecords.filter((record: any) => record.status === 'present').length
      const absentEmployees = totalEmployees - presentEmployees

      const allSalesInvoices = salesResult.success ? salesResult.data : []
      const allPurchaseInvoices = purchasesResult.success ? purchasesResult.data : []

      // فلترة الفواتير حسب تاريخ اليوم
      const salesInvoices = allSalesInvoices.filter((invoice: any) =>
        invoice.invoice_date && invoice.invoice_date.startsWith(today)
      )
      const purchaseInvoices = allPurchaseInvoices.filter((invoice: any) =>
        invoice.invoice_date && invoice.invoice_date.startsWith(today)
      )

      const totalSales = salesInvoices.reduce((sum: number, invoice: any) => sum + (invoice.total_amount || 0), 0)
      const totalPurchases = purchaseInvoices.reduce((sum: number, invoice: any) => sum + (invoice.total_amount || 0), 0)

      const items = Array.isArray(inventoryResult) ? inventoryResult : ((inventoryResult as any)?.success ? (inventoryResult as any).data : [])
      const lowStockItems = items.filter((item: any) => item.quantity <= (item.min_quantity || 0)).length

      setData({
        totalEmployees,
        presentEmployees,
        absentEmployees,
        totalSales,
        totalPurchases,
        salesInvoices: salesInvoices.length,
        purchaseInvoices: purchaseInvoices.length,
        lowStockItems,
        pendingTasks: 0 // يمكن إضافة منطق للمهام المعلقة لاحقاً
      })
    } catch (error) {
      Logger.error('DailySummary', 'خطأ في جلب ملخص اليوم:', error)
    } finally {
      setLoading(false)
    }
  }

  const getAttendanceStatus = () => {
    if (!data) return 'default'
    const attendanceRate = (data.presentEmployees / data.totalEmployees) * 100
    if (attendanceRate >= 90) return 'success'
    if (attendanceRate >= 70) return 'warning'
    return 'error'
  }

  const getSalesStatus = () => {
    if (!data) return 'default'
    if (data.totalSales >= 10000) return 'success'
    if (data.totalSales >= 5000) return 'warning'
    return 'default'
  }

  return (
    <Modal
      title={
        <Space>
          <DashboardOutlined />
          ملخص اليوم - {dayjs().format('YYYY-MM-DD')}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>جاري تحميل ملخص اليوم...</Text>
          </div>
        </div>
      ) : data ? (
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {/* إحصائيات الموّفين */}
          <Card title={<Space><TeamOutlined /> حضور الموّفين</Space>} size="small">
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="إجمالي الموّفين"
                  value={data.totalEmployees}
                  prefix={<TeamOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="الحاضرون"
                  value={data.presentEmployees}
                  prefix={<ClockCircleOutlined />}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="الغائبون"
                  value={data.absentEmployees}
                  prefix={<WarningOutlined />}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
            </Row>
            <Divider />
            <div style={{ textAlign: 'center' }}>
              <Tag color={getAttendanceStatus()}>
                معدل الحضور: {data.totalEmployees > 0 ? Math.round((data.presentEmployees / data.totalEmployees) * 100) : 0}%
              </Tag>
            </div>
          </Card>

          {/* إحصائيات المبيعات والمشتريات */}
          <Row gutter={16}>
            <Col span={12}>
              <Card title={<Space><ShoppingCartOutlined /> المبيعات</Space>} size="small">
                <Statistic
                  title="إجمالي المبيعات"
                  value={data.totalSales}
                  precision={2}
                  prefix={<DollarOutlined />}
                  suffix="ر.س"
                  valueStyle={{ color: getSalesStatus() === 'success' ? '#3f8600' : '#1890ff' }}
                />
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">عدد الفواتير: {data.salesInvoices}</Text>
                </div>
              </Card>
            </Col>
            <Col span={12}>
              <Card title={<Space><FileTextOutlined /> المشتريات</Space>} size="small">
                <Statistic
                  title="إجمالي المشتريات"
                  value={data.totalPurchases}
                  precision={2}
                  prefix={<DollarOutlined />}
                  suffix="ر.س"
                  valueStyle={{ color: '#722ed1' }}
                />
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">عدد الفواتير: {data.purchaseInvoices}</Text>
                </div>
              </Card>
            </Col>
          </Row>

          {/* تنبيهات مهمة */}
          <Card title={<Space><WarningOutlined /> تنبيهات مهمة</Space>} size="small">
            <Row gutter={16}>
              <Col span={12}>
                <Space>
                  <Text strong>أصناف منخفضة المخزون:</Text>
                  <Tag color={data.lowStockItems > 0 ? 'red' : 'green'}>
                    {data.lowStockItems} صنف
                  </Tag>
                </Space>
              </Col>
              <Col span={12}>
                <Space>
                  <Text strong>المهام المعلقة:</Text>
                  <Tag color="orange">{data.pendingTasks} مهمة</Tag>
                </Space>
              </Col>
            </Row>
          </Card>

          {/* ملخص الأداء */}
          <Card title={<Space><TrophyOutlined /> تقييم الأداء اليومي</Space>} size="small">
            <div style={{ textAlign: 'center' }}>
              {data.totalSales >= 10000 && data.presentEmployees / data.totalEmployees >= 0.9 ? (
                <Tag color="gold" style={{ fontSize: '16px', padding: '8px 16px' }}>
                  🏆 أداء ممتاز اليوم!
                </Tag>
              ) : data.totalSales >= 5000 && data.presentEmployees / data.totalEmployees >= 0.7 ? (
                <Tag color="green" style={{ fontSize: '16px', padding: '8px 16px' }}>
                  ✅ أداء جيد اليوم
                </Tag>
              ) : (
                <Tag color="orange" style={{ fontSize: '16px', padding: '8px 16px' }}>
                  📈 يمكن تحسين الأداء
                </Tag>
              )}
            </div>
          </Card>
        </Space>
      ) : (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Text type="secondary">لا توجد بيانات متاحة لليوم الحالي</Text>
          <div style={{ marginTop: 16 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              تأكد من وجود بيانات في النّام أو جرب تاريخاً آخر
            </Text>
          </div>
        </div>
      )}
    </Modal>
  )
}

export default DailySummary
