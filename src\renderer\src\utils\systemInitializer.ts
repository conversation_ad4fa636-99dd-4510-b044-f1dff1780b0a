/**
 * مهيئ النّام الشامل
 * يدير تهيئة وتنّيف جميع الأنّمة بشكل منسق
 */

import { resourceManager } from './resourceManager'
import { performanceOptimizer } from './performanceOptimizer'
import { securityCleaner } from './securityCleaner'
import { comprehensiveAudioSystem } from './comprehensiveAudioSystem'
import smartNotifications from './smartNotifications'
// import { SimplePrintService } from '../services/SimplePrintService' // تم حذف نظام الطباعة
// import templateInitializationService from '../services/TemplateInitializationService' // تم حذف نظام الطباعة
import { SimpleLogger as Logger } from './logger'

interface SystemStatus {
  initialized: boolean
  systems: {
    resourceManager: boolean
    performanceOptimizer: boolean
    securityCleaner: boolean
    audioSystem: boolean
    notifications: boolean
    printService: boolean
    templateService: boolean
  }
  errors: string[]
  initTime: number
}

class SystemInitializer {
  private status: SystemStatus = {
    initialized: false,
    systems: {
      resourceManager: false,
      performanceOptimizer: false,
      securityCleaner: false,
      audioSystem: false,
      notifications: false,
      printService: false,
      templateService: false
    },
    errors: [],
    initTime: 0
  }

  // تهيئة شاملة للنّام
  async initializeAll(): Promise<SystemStatus> {
    Logger.info('SystemInitializer', '🚀 بدء تهيئة النّام الشامل...')
    const startTime = performance.now()
    
    this.status.errors = []
    
    try {
      // 1. تهيئة مدير الموارد (أولوية عالية)
      await this.initializeResourceManager()
      
      // 2. تهيئة محسن الأداء
      await this.initializePerformanceOptimizer()
      
      // 3. تهيئة منّف الأمان
      await this.initializeSecurityCleaner()
      
      // 4. تهيئة النّام الصوتي
      await this.initializeAudioSystem()
      
      // 5. تهيئة نّام الإشعارات
      await this.initializeNotifications()

      // 6. تهيئة خدمة الطباعة وتحميل معلومات الشركة
      await this.initializePrintService()

      // 7. تهيئة خدمة القوالب
      await this.initializeTemplateService()

      // 8. تسجيل معالجات التنّيف العامة
      this.registerGlobalCleanupHandlers()
      
      this.status.initialized = true
      this.status.initTime = performance.now() - startTime
      
      Logger.info('SystemInitializer', '✅ تم تهيئة النّام بنجاح في ${this.status.initTime.toFixed(2)}ms')
      
      // تشغيل فحص صحة النّام
      this.performHealthCheck()
      
    } catch (error) {
      Logger.error('SystemInitializer', '❌ فشل في تهيئة النّام:', error)
      this.status.errors.push(`خطأ عام: ${error}`)
    }
    
    return this.status
  }

  // تهيئة مدير الموارد
  private async initializeResourceManager(): Promise<void> {
    try {
      // مدير الموارد يتم تهيئته تلقائياً عند الاستيراد
      this.status.systems.resourceManager = true
      Logger.info('SystemInitializer', '✅ تم تهيئة مدير الموارد')
    } catch (error) {
      this.status.errors.push(`مدير الموارد: ${error}`)
      Logger.error('SystemInitializer', '❌ فشل في تهيئة مدير الموارد:', error)
    }
  }

  // تهيئة محسن الأداء
  private async initializePerformanceOptimizer(): Promise<void> {
    try {
      // محسن الأداء يتم تهيئته تلقائياً عند الاستيراد
      this.status.systems.performanceOptimizer = true
      Logger.info('SystemInitializer', '✅ تم تهيئة محسن الأداء')
    } catch (error) {
      this.status.errors.push(`محسن الأداء: ${error}`)
      Logger.error('SystemInitializer', '❌ فشل في تهيئة محسن الأداء:', error)
    }
  }

  // تهيئة منّف الأمان
  private async initializeSecurityCleaner(): Promise<void> {
    try {
      // تنّيف البيانات الحساسة الموجودة
      securityCleaner.cleanLocalStorage()
      securityCleaner.cleanSessionStorage()
      
      this.status.systems.securityCleaner = true
      Logger.info('SystemInitializer', '✅ تم تهيئة منّف الأمان')
    } catch (error) {
      this.status.errors.push(`منّف الأمان: ${error}`)
      Logger.error('SystemInitializer', '❌ فشل في تهيئة منّف الأمان:', error)
    }
  }

  // تهيئة النّام الصوتي
  private async initializeAudioSystem(): Promise<void> {
    try {
      // تهيئة النّام الصوتي
      comprehensiveAudioSystem.attachToElements()
      
      this.status.systems.audioSystem = true
      Logger.info('SystemInitializer', '✅ تم تهيئة النّام الصوتي')
    } catch (error) {
      this.status.errors.push(`النّام الصوتي: ${error}`)
      Logger.error('SystemInitializer', '❌ فشل في تهيئة النّام الصوتي:', error)
    }
  }

  // تهيئة نّام الإشعارات
  private async initializeNotifications(): Promise<void> {
    try {
      // بدء نّام الإشعارات الذكية
      smartNotifications.start()

      this.status.systems.notifications = true
      Logger.info('SystemInitializer', '✅ تم تهيئة نّام الإشعارات')
    } catch (error) {
      this.status.errors.push(`نّام الإشعارات: ${error}`)
      Logger.error('SystemInitializer', '❌ فشل في تهيئة نّام الإشعارات:', error)
    }
  }

  // تهيئة خدمة الطباعة وتحميل معلومات الشركة
  private async initializePrintService(): Promise<void> {
    try {
      Logger.info('SystemInitializer', '🖨️ تم حذف نظام الطباعة - تخطي التهيئة...')

      // تم حذف نظام الطباعة
      // await SimplePrintService.loadCompanyInfo()

      this.status.systems.printService = true
      Logger.info('SystemInitializer', '✅ تم تخطي تهيئة نظام الطباعة المحذوف')
    } catch (error) {
      this.status.errors.push(`خدمة الطباعة: ${error}`)
      Logger.error('SystemInitializer', '❌ فشل في تهيئة خدمة الطباعة:', error)
    }
  }

  // تهيئة خدمة القوالب
  private async initializeTemplateService(): Promise<void> {
    try {
      Logger.info('SystemInitializer', '🎨 تم حذف نظام القوالب - تخطي التهيئة...')

      // تم حذف نظام القوالب
      // const templateStatus = await templateInitializationService.initializeAllTemplates()

      this.status.systems.templateService = true
      Logger.success('SystemInitializer', '✅ تم تخطي تهيئة نظام القوالب المحذوف')
    } catch (error) {
      this.status.errors.push(`خدمة القوالب: ${error}`)
      Logger.error('SystemInitializer', '❌ فشل في تهيئة خدمة القوالب:', error)
    }
  }

  // تسجيل معالجات التنّيف العامة
  private registerGlobalCleanupHandlers(): void {
    try {
      // معالج تنّيف عند إغلاق النافذة
      const beforeUnloadHandler = () => {
        Logger.info('SystemInitializer', '🧹 بدء تنّيف النّام قبل الإغلاق...')
        this.cleanup()
      }

      // معالج تنّيف عند إخفاء الصفحة
      const pageHideHandler = () => {
        Logger.info('SystemInitializer', '🧹 بدء تنّيف النّام عند إخفاء الصفحة...')
        this.cleanup()
      }

      window.addEventListener('beforeunload', beforeUnloadHandler)
      window.addEventListener('pagehide', pageHideHandler)

      Logger.info('SystemInitializer', '✅ تم تسجيل معالجات التنّيف العامة')
    } catch (error) {
      this.status.errors.push(`معالجات التنّيف: ${error}`)
      Logger.error('SystemInitializer', '❌ فشل في تسجيل معالجات التنّيف:', error)
    }
  }

  // فحص صحة النّام
  private performHealthCheck(): void {
    try {
      Logger.info('SystemInitializer', '🔍 فحص صحة النّام...')
      
      // فحص مدير الموارد
      const resourceHealth = resourceManager.healthCheck()
      if (!resourceHealth.healthy) {
        Logger.warn('SystemInitializer', '⚠️ مشاكل في مدير الموارد:', resourceHealth.issues)
      }

      // فحص محسن الأداء
      const perfStats = performanceOptimizer.getStats()
      Logger.info('SystemInitializer', '📊 إحصائيات الأداء:', perfStats)

      // فحص منّف الأمان
      const securityConfig = securityCleaner.getConfig()
      Logger.info('SystemInitializer', '🔒 إعدادات الأمان:', securityConfig)

      Logger.info('SystemInitializer', '✅ تم فحص صحة النّام')
    } catch (error) {
      Logger.error('SystemInitializer', '❌ خطأ في فحص صحة النّام:', error)
    }
  }

  // تنّيف شامل للنّام
  async cleanup(): Promise<void> {
    try {
      Logger.info('SystemInitializer', '🧹 بدء تنّيف شامل للنّام...')
      
      // تنّيف جميع الموارد المسجلة
      await resourceManager.cleanupAll()
      
      // استعادة console الأصلي
      securityCleaner.restoreOriginalConsole()
      
      Logger.info('SystemInitializer', '✅ تم تنّيف النّام بنجاح')
    } catch (error) {
      Logger.error('SystemInitializer', '❌ خطأ في تنّيف النّام:', error)
    }
  }

  // الحصول على حالة النّام
  getStatus(): SystemStatus {
    return { ...this.status }
  }

  // إعادة تهيئة النّام
  async reinitialize(): Promise<SystemStatus> {
    Logger.info('SystemInitializer', '🔄 إعادة تهيئة النّام...')
    
    // تنّيف أولاً
    await this.cleanup()
    
    // إعادة تعيين الحالة
    this.status = {
      initialized: false,
      systems: {
        resourceManager: false,
        performanceOptimizer: false,
        securityCleaner: false,
        audioSystem: false,
        notifications: false,
        printService: false,
        templateService: false
      },
      errors: [],
      initTime: 0
    }
    
    // تهيئة جديدة
    return await this.initializeAll()
  }
}

// إنشاء مثيل واحد
export const systemInitializer = new SystemInitializer()

// إتاحة النّام عالمياً
if (typeof window !== 'undefined') {
  ;(window as any).systemInitializer = systemInitializer
}

export default SystemInitializer
