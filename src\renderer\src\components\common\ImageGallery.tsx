import React, { useState, useEffect } from 'react'
import {
  Modal,
  Image,
  Button,
  Row,
  Col,
  Card,
  Spin,
  Empty,
  Tag, Space,
  Input,
  Select
} from 'antd'
import { logger as Logger } from './../../utils/logger'
import {
  LeftOutlined,
  RightOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  DownloadOutlined, PictureOutlined,
  SearchOutlined
} from '@ant-design/icons'
import type { ItemImage } from '../../types/global'

interface ImageGalleryProps {
  visible: boolean
  onClose: () => void
  images: ItemImage[]
  initialIndex?: number
  title?: string
}

const ImageGallery: React.FC<ImageGalleryProps> = ({
  visible,
  onClose,
  images,
  initialIndex = 0,
  title = 'معرض الصور'
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex)
  const [loading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'primary'>('primary')
  const [viewMode, setViewMode] = useState<'grid' | 'carousel'>('carousel')

  // تصفية وترتيب الصور
  const filteredAndSortedImages = React.useMemo(() => {
    const filtered = images.filter(image => 
      image.image_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (image.description && image.description.toLowerCase().includes(searchTerm.toLowerCase()))
    )

    switch (sortBy) {
      case 'name':
        filtered.sort((a, b) => a.image_name.localeCompare(b.image_name))
        break
      case 'date':
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        break
      case 'primary':
        filtered.sort((a, b) => {
          if (a.is_primary && !b.is_primary) return -1
          if (!a.is_primary && b.is_primary) return 1
          return a.sort_order - b.sort_order
        })
        break
    }

    return filtered
  }, [images, searchTerm, sortBy])

  const currentImage = filteredAndSortedImages[currentIndex]

  useEffect(() => {
    if (visible) {
      setCurrentIndex(Math.min(initialIndex, filteredAndSortedImages.length - 1))
    }
  }, [visible, initialIndex, filteredAndSortedImages.length])

  // التنقل للصورة التالية
  const nextImage = () => {
    setCurrentIndex((prev) => 
      prev < filteredAndSortedImages.length - 1 ? prev + 1 : 0
    )
  }

  // التنقل للصورة السابقة
  const prevImage = () => {
    setCurrentIndex((prev) => 
      prev > 0 ? prev - 1 : filteredAndSortedImages.length - 1
    )
  }

  // الانتقال لصورة محددة
  const goToImage = (index: number) => {
    setCurrentIndex(index)
  }

  // تحميل الصورة
  const downloadImage = (image: ItemImage) => {
    // هنا يجب إضافة منطق تحميل الصورة
    Logger.info('ImageGallery', 'تحميل الصورة:', image.image_name)
  }

  // عرض الشبكة
  const renderGridView = () => (
    <Row gutter={[16, 16]} style={{ maxHeight: '60vh', overflow: 'auto' }}>
      {filteredAndSortedImages.map((image, index) => (
        <Col xs={12} sm={8} md={6} lg={4} key={image.id}>
          <Card
            hoverable
            cover={
              <div style={{ position: 'relative', height: 120 }}>
                <Image
                  src={image.image_path}
                  alt={image.image_name}
                  style={{ 
                    width: '100%', 
                    height: '100%', 
                    objectFit: 'cover',
                    cursor: 'pointer'
                  }}
                  preview={false}
                  onClick={() => {
                    setCurrentIndex(index)
                    setViewMode('carousel')
                  }}
                />
                {image.is_primary && (
                  <Tag 
                    color="gold" 
                    style={{ 
                      position: 'absolute', 
                      top: 4, 
                      right: 4,
                      fontSize: 10
                    }}
                  >
                    رئيسية
                  </Tag>
                )}
              </div>
            }
            size="small"
          >
            <Card.Meta
              title={
                <div style={{ fontSize: 10, wordBreak: 'break-all' }}>
                  {image.image_name}
                </div>
              }
              description={
                <div style={{ fontSize: 9, color: '#666' }}>
                  {image.description || 'لا يوجد وصف'}
                </div>
              }
            />
          </Card>
        </Col>
      ))}
    </Row>
  )

  // عرض الكاروسيل
  const renderCarouselView = () => (
    <div style={{ textAlign: 'center' }}>
      {currentImage && (
        <>
          <div style={{ marginBottom: 16 }}>
            <Image
              src={currentImage.image_path}
              alt={currentImage.image_name}
              style={{ maxHeight: '60vh', maxWidth: '100%' }}
              preview={{
                toolbarRender: (
                  _,
                  {
                    transform: { scale },
                    actions: { onRotateLeft, onRotateRight, onZoomOut, onZoomIn }
                  }
                ) => (
                  <Space size={12} className="toolbar-wrapper">
                    <LeftOutlined onClick={prevImage} />
                    <ZoomOutOutlined disabled={scale === 1} onClick={onZoomOut} />
                    <ZoomInOutlined disabled={scale === 50} onClick={onZoomIn} />
                    <RotateLeftOutlined onClick={onRotateLeft} />
                    <RotateRightOutlined onClick={onRotateRight} />
                    <DownloadOutlined onClick={() => downloadImage(currentImage)} />
                    <RightOutlined onClick={nextImage} />
                  </Space>
                )
              }}
            />
          </div>

          <div style={{ marginBottom: 16 }}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 8 }}>
                <h4 style={{ margin: 0 }}>{currentImage.image_name}</h4>
                {currentImage.is_primary && (
                  <Tag color="gold">صورة رئيسية</Tag>
                )}
              </div>
              {currentImage.description && (
                <div style={{ color: '#666', fontSize: 14 }}>
                  {currentImage.description}
                </div>
              )}
              <div style={{ color: '#999', fontSize: 12 }}>
                الصورة {currentIndex + 1} من {filteredAndSortedImages.length}
              </div>
            </Space>
          </div>

          {/* شريط الصور المصغرة */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            gap: 8, 
            overflowX: 'auto',
            padding: '8px 0',
            maxWidth: '100%'
          }}>
            {filteredAndSortedImages.map((image, index) => (
              <div
                key={image.id}
                style={{
                  width: 60,
                  height: 40,
                  border: index === currentIndex ? '2px solid #1890ff' : '1px solid #d9d9d9',
                  borderRadius: 4,
                  overflow: 'hidden',
                  cursor: 'pointer',
                  flexShrink: 0
                }}
                onClick={() => goToImage(index)}
              >
                <Image
                  src={image.image_path}
                  alt={image.image_name}
                  style={{ 
                    width: '100%', 
                    height: '100%', 
                    objectFit: 'cover' 
                  }}
                  preview={false}
                />
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  )

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <PictureOutlined />
            <span>{title}</span>
            <Tag color="blue">{filteredAndSortedImages.length} صورة</Tag>
          </div>
          <Space>
            <Input
              placeholder="البحث في الصور..."
              prefix={<SearchOutlined />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{ width: 200 }}
              size="small"
            />
            <Select
              value={sortBy}
              onChange={setSortBy}
              size="small"
              style={{ width: 120 }}
            >
              <Select.Option value="primary">الرئيسية أولاً</Select.Option>
              <Select.Option value="name">الاسم</Select.Option>
              <Select.Option value="date">التاريخ</Select.Option>
            </Select>
            <Button.Group size="small">
              <Button 
                type={viewMode === 'carousel' ? 'primary' : 'default'}
                onClick={() => setViewMode('carousel')}
              >
                عرض مفرد
              </Button>
              <Button 
                type={viewMode === 'grid' ? 'primary' : 'default'}
                onClick={() => setViewMode('grid')}
              >
                شبكة
              </Button>
            </Button.Group>
          </Space>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width="90%"
      style={{ top: 20 }}
      footer={[
        <Button key="close" onClick={onClose}>
          إغلاق
        </Button>
      ]}
    >
      <Spin spinning={loading}>
        {filteredAndSortedImages.length === 0 ? (
          <Empty 
            description={searchTerm ? 'لا توجد صور تطابق البحث' : 'لا توجد صور'}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <>
            {viewMode === 'grid' ? renderGridView() : renderCarouselView()}
          </>
        )}
      </Spin>
    </Modal>
  )
}

export default ImageGallery
