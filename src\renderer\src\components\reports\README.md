# 📊 نظام التقارير - برنامج المحاسبة والإنتاج

## نظرة عامة

نظام التقارير المطور يوفر مجموعة شاملة من التقارير للمخزون مع واجهات متقدمة وإمكانيات تصدير متعددة.

## التقارير المتاحة

### 1. تقرير المخزون التفصيلي (InventoryDetailedReport)
- **الوصف**: تقرير شامل يعرض جميع الأصناف مع الكميات والقيم في كل مخزن
- **المميزات**:
  - عرض الكميات المتاحة والمحجوزة
  - حساب قيمة المخزون
  - مؤشرات الحالة (متاح/منخفض/غير متاح)
  - فلترة حسب المخزن والفئة والصنف

### 2. تقرير حركة المخزون (InventoryMovementsReport)
- **الوصف**: تقرير يعرض جميع حركات المخزون (إدخال/إخراج/تحويل) خلال فترة محددة
- **المميزات**:
  - تصنيف الحركات حسب النوع
  - عرض المراجع والمستخدمين
  - فلترة حسب الفترة الزمنية ونوع الحركة
  - إحصائيات شاملة للحركات

### 3. تقرير الأصناف المنخفضة (LowStockReport)
- **الوصف**: تقرير ينبه للأصناف التي وصلت للحد الأدنى أو أقل
- **المميزات**:
  - مؤشرات مستوى الخطر
  - حساب الكميات والقيم المطلوبة
  - نسب المخزون المتبقي
  - تصنيف حسب مستوى الأولوية

### 4. تقرير استهلاك المواد (MaterialConsumptionReport)
- **الوصف**: تقرير يعرض معدل استهلاك المواد والأصناف خلال فترة زمنية
- **المميزات**:
  - تحليل أنماط الاستهلاك
  - متوسط الاستهلاك اليومي
  - مستويات النشاط
  - مؤشرات الأداء

### 5. تقرير الجرد والمطابقة (InventoryAuditReport)
- **الوصف**: تقرير لمطابقة الكميات الفعلية مع الكميات المسجلة في النظام
- **المميزات**:
  - إمكانية تعديل الكميات الفعلية
  - حساب الفروقات والنسب
  - حالات الجرد (مطابق/زيادة/نقص)
  - إحصائيات دقة المخزون

## البنية التقنية

### ReportBase Component
المكون الأساسي الذي يوفر:
- واجهة موحدة لجميع التقارير
- نظام فلترة متقدم
- إمكانيات التصدير (PDF, Excel)
- الطباعة
- التنسيق المتجاوب

### الواجهات (Interfaces)

```typescript
interface ReportData {
  title: string;
  subtitle?: string;
  columns: any[];
  data: any[];
  summary?: {
    totalItems?: number;
    totalValue?: number;
    totalQuantity?: number;
    [key: string]: any;
  };
}

interface ReportFilters {
  dateRange?: [dayjs.Dayjs, dayjs.Dayjs];
  warehouseId?: number;
  categoryId?: number;
  itemId?: number;
  [key: string]: any;
}
```

## كيفية إضافة تقرير جديد

1. **إنشاء مكون التقرير**:
```typescript
import React from 'react';
import ReportBase, { ReportData, ReportFilters } from './ReportBase';

const NewReport: React.FC = () => {
  const generateReport = async (filters: ReportFilters): Promise<ReportData> => {
    // منطق إنشاء التقرير
    return {
      title: 'عنوان التقرير',
      columns: [...],
      data: [...],
      summary: {...}
    };
  };

  return (
    <ReportBase
      type="new_report"
      title="التقرير الجديد"
      description="وصف التقرير"
      onGenerateReport={generateReport}
      showDateRange={true}
      showWarehouseFilter={true}
    />
  );
};
```

2. **إضافة معالج في main.ts**:
```typescript
ipcMain.handle('get-new-report', (event, filters: any = {}) => {
  // منطق استعلام قاعدة البيانات
});
```

3. **إضافة الوظيفة في preload.ts**:
```typescript
getNewReport: (filters?: any) => ipcRenderer.invoke('get-new-report', filters),
```

4. **إضافة التقرير في Dashboard.tsx**:
```typescript
case 'new-report':
  return <NewReport />
```

## المميزات المتقدمة

### التصدير
- **PDF**: تصدير مع تنسيق احترافي
- **Excel**: جداول بيانات قابلة للتحرير
- **الطباعة**: تنسيق محسن للطباعة

### الفلترة
- فلاتر ديناميكية حسب نوع التقرير
- حفظ إعدادات الفلترة
- فلاتر مخصصة لكل تقرير

### الأداء
- تحميل البيانات بشكل تدريجي
- تحسين استعلامات قاعدة البيانات
- ذاكرة تخزين مؤقت للبيانات

## التحسينات المستقبلية

1. **تقارير مجدولة**: تشغيل تلقائي وإرسال بالبريد الإلكتروني
2. **تقارير مخصصة**: منشئ تقارير بصري
3. **لوحات معلومات**: عرض مؤشرات سريعة
4. **تصدير متقدم**: تنسيقات إضافية ومخصصة
5. **مشاركة التقارير**: روابط آمنة للمشاركة

## الاستخدام

```typescript
// استيراد التقارير
import {
  InventoryDetailedReport,
  InventoryMovementsReport,
  LowStockReport
} from './components/reports';

// استخدام في المكونات
<InventoryDetailedReport />
```

## الدعم والصيانة

- **المطور**: faresnawf
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: 0569329925

---

تم تطوير نظام التقارير كجزء من برنامج المحاسبة والإنتاج الشامل.
