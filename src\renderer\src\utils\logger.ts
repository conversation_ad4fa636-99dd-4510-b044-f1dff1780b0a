/**
 * نظام Logger موحد وآمن - ZET.IA Enhanced System
 * يدعم جميع أنواع التسجيل مع الحماية من circular dependencies
 */

// دالة مساعدة لتحويل unknown إلى Error
function toError(error: unknown): Error {
  if (error instanceof Error) {
    return error
  }
  if (typeof error === 'string') {
    return new Error(error)
  }
  if (typeof error === 'object' && error !== null) {
    return new Error(JSON.stringify(error))
  }
  return new Error('Unknown error')
}

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

export interface LogEntry {
  timestamp: Date
  level: LogLevel
  module: string
  message: string
  data?: any
  error?: Error
  userId?: string
  sessionId?: string
}

class LoggerClass {
  private static instance: LoggerClass
  private logs: LogEntry[] = []
  private maxLogs = 2000 // زيادة الحد الأقصى للسجلات
  private currentLevel = process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.WARN
  private isDevelopment = process.env.NODE_ENV === 'development'
  private isProduction = process.env.NODE_ENV === 'production'
  private sessionId = this.generateSessionId()

  private constructor() {
    // تنّيف السجلات القديمة كل 5 دقائق
    setInterval(() => this.cleanupOldLogs(), 5 * 60 * 1000)
  }

  public static getInstance(): LoggerClass {
    if (!LoggerClass.instance) {
      LoggerClass.instance = new LoggerClass()
    }
    return LoggerClass.instance
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  public setLevel(level: LogLevel): void {
    this.currentLevel = level
  }

  // تنّيف السجلات القديمة (أكثر من ساعة)
  private cleanupOldLogs(): void {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    this.logs = this.logs.filter(log => log.timestamp > oneHourAgo)
  }

  // تنسيق البيانات الحساسة
  private sanitizeData(data: any): any {
    if (!data) return data

    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth', 'credential', 'pin']

    if (typeof data === 'object') {
      const sanitized = { ...data }
      for (const key in sanitized) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
          sanitized[key] = '[REDACTED]'
        }
      }
      return sanitized
    }

    return data
  }

  private log(level: LogLevel, module: string, message: string, data?: any, error?: Error): void {
    if (level < this.currentLevel) {
      return
    }

    const sanitizedData = this.sanitizeData(data)
    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      module,
      message,
      data: sanitizedData,
      error,
      sessionId: this.sessionId
    }

    this.logs.push(entry)

    // إزالة السجلات القديمة إذا تجاوزت الحد الأقصى
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs)
    }

    // طباعة في وحدة التحكم
    this.printToConsole(entry)
  }

  private printToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toLocaleString('ar-SA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    const levelName = LogLevel[entry.level]
    const prefix = `[${timestamp}] [${levelName}] [${entry.module}]`

    // إضافة رموز تعبيرية للمستويات
    const levelIcons = {
      [LogLevel.DEBUG]: '🔍',
      [LogLevel.INFO]: 'ℹ️',
      [LogLevel.WARN]: '⚠️',
      [LogLevel.ERROR]: '❌'
    }

    const icon = levelIcons[entry.level] || ''
    const formattedPrefix = `${icon} ${prefix}`

    switch (entry.level) {
      case LogLevel.DEBUG:
        if (this.isDevelopment) {
          console.debug(formattedPrefix, entry.message, entry.data || '')
        }
        break
      case LogLevel.INFO:
        console.info(formattedPrefix, entry.message, entry.data || '')
        break
      case LogLevel.WARN:
        console.warn(formattedPrefix, entry.message, entry.data || '')
        break
      case LogLevel.ERROR:
        console.error(formattedPrefix, entry.message, entry.data || '', entry.error || '')

        // في الإنتاج، يمكن إرسال الأخطاء لخدمة مراقبة
        if (this.isProduction) {
          this.sendErrorToMonitoring(entry)
        }
        break
    }
  }

  // إرسال الأخطاء لخدمة المراقبة (للإنتاج)
  private sendErrorToMonitoring(entry: LogEntry): void {
    // TODO: تطبيق إرسال الأخطاء لخدمة مراقبة خارجية
    // مثل Sentry أو LogRocket
    try {
      // يمكن إضافة إرسال للخادم هنا
      if (window.electronAPI) {
        (window.electronAPI as any).logError({
          timestamp: entry.timestamp,
          level: entry.level,
          module: entry.module,
          message: entry.message,
          error: entry.error?.message,
          stack: entry.error?.stack,
          data: entry.data,
          sessionId: entry.sessionId
        })
      }
    } catch {
      // تجاهل أخطاء الإرسال لتجنب التكرار اللانهائي
    }
  }

  public debug(module: string, message: string, data?: any): void {
    this.log(LogLevel.DEBUG, module, message, data)
  }

  public info(module: string, message: string, data?: any): void {
    this.log(LogLevel.INFO, module, message, data)
  }

  public warn(module: string, message: string, data?: any): void {
    this.log(LogLevel.WARN, module, message, data)
  }

  public error(module: string, message: string, error?: Error, data?: any): void {
    this.log(LogLevel.ERROR, module, message, data, error)
  }

  public success(module: string, message: string, data?: any): void {
    this.log(LogLevel.INFO, module, `✅ ${message}`, data)
  }

  public performance(module: string, message: string, startTime?: number): void {
    if (this.isDevelopment) {
      let perfMessage = message
      if (startTime) {
        const duration = Date.now() - startTime
        perfMessage = `${message} (${duration}ms)`
      }
      this.log(LogLevel.DEBUG, module, `⚡ ${perfMessage}`)
    }
  }

  public getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter(log => log.level >= level)
    }
    return [...this.logs]
  }

  public getErrorLogs(): LogEntry[] {
    return this.logs.filter(log => log.level === LogLevel.ERROR)
  }

  public clearLogs(): void {
    this.logs = []
  }

  public exportLogs(): string {
    const exportData = {
      exportDate: new Date().toISOString(),
      sessionId: this.sessionId,
      environment: process.env.NODE_ENV,
      totalEntries: this.logs.length,
      logs: this.logs
    }
    return JSON.stringify(exportData, null, 2)
  }

  public getStats(): { total: number; byLevel: Record<string, number> } {
    const stats = {
      total: this.logs.length,
      byLevel: {
        DEBUG: 0,
        INFO: 0,
        WARN: 0,
        ERROR: 0
      }
    }

    this.logs.forEach(log => {
      const levelName = LogLevel[log.level]
      stats.byLevel[levelName]++
    })

    return stats
  }
}

// إنشاء مثيل واحد للاستخدام العام
export const logger = LoggerClass.getInstance()

// إنشاء كائن Logger للاستخدام المباشر
export const Logger = {
  error: (module: string, message: string, error?: unknown) => {
    const errorObj = error ? toError(error) : new Error(message)
    logger.error(module, message, errorObj)
  },
  warn: (module: string, message: string, data?: any) => {
    logger.warn(module, message, data)
  },
  info: (module: string, message: string, data?: any) => {
    logger.info(module, message, data)
  },
  debug: (module: string, message: string, data?: any) => {
    logger.debug(module, message, data)
  }
}

// دوال مساعدة للاستخدام السريع
export const logDebug = (module: string, message: string, data?: any) => 
  logger.debug(module, message, data)

export const logInfo = (module: string, message: string, data?: any) => 
  logger.info(module, message, data)

export const logWarn = (module: string, message: string, data?: any) => 
  logger.warn(module, message, data)

export const logError = (module: string, message: string, error?: unknown, data?: any) =>
  logger.error(module, message, error ? toError(error) : undefined, data)

// دالة لمعالجة الأخطاء بشكل موحد
export const handleError = (module: string, operation: string, error: unknown, showMessage = true) => {
  const errorObj = toError(error)
  const fullMessage = `خطأ في ${operation}: ${errorObj.message}`

  logError(module, fullMessage, errorObj)
  
  if (showMessage && typeof window !== 'undefined' && window.electronAPI) {
    // يمكن إضافة عرض رسالة للمستخدم هنا
    // استخدام console مقبول هنا كـ fallback
    console.error(`❌ ${module}: ${fullMessage}`)
  }
  
  return {
    success: false,
    message: fullMessage,
    error: errorObj.message
  }
}

// دالة لتسجيل العمليات الناجحة
export const logSuccess = (module: string, operation: string, data?: any) => {
  const message = `✅ تم ${operation} بنجاح`
  logInfo(module, message, data)
  return {
    success: true,
    message
  }
}

// ===== واجهات متوافقة مع الملفات الأخرى =====

// واجهة SimpleLogger للتوافق مع simpleLogger.ts
export const SimpleLogger = {
  error: (module: string, message: string, error?: unknown) => {
    const timestamp = new Date().toISOString()
    console.error(`[${timestamp}] [ERROR] [${module}] ${message}`, error || '')

    // إرسال للنظام الرئيسي أيضاً
    try {
      logError(module, message, error)
    } catch (e) {
      // تجاهل أخطاء النظام الرئيسي
    }
  },

  warn: (module: string, message: string, data?: any) => {
    const timestamp = new Date().toISOString()
    console.warn(`[${timestamp}] [WARN] [${module}] ${message}`, data || '')
    try {
      logWarn(module, message, data)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  },

  info: (module: string, message: string, data?: any) => {
    const timestamp = new Date().toISOString()
    console.info(`[${timestamp}] [INFO] [${module}] ${message}`, data || '')
    try {
      logInfo(module, message, data)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  },

  debug: (module: string, message: string, data?: any) => {
    const timestamp = new Date().toISOString()
    console.debug(`[${timestamp}] [DEBUG] [${module}] ${message}`, data || '')
    try {
      logDebug(module, message, data)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  },

  success: (module: string, message: string, data?: any) => {
    const timestamp = new Date().toISOString()
    console.log(`[${timestamp}] [SUCCESS] [${module}] ✅ ${message}`, data || '')
    try {
      logSuccess(module, message, data)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  }
}

// واجهة SafeLogger للتوافق مع loggerFix.ts
export const SafeLogger = {
  error: (module: string, message: string, error?: unknown) => {
    console.error(`[${module}] ${message}`, error)
    try {
      logError(module, message, error)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  },
  warn: (module: string, message: string, data?: any) => {
    console.warn(`[${module}] ${message}`, data)
    try {
      logWarn(module, message, data)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  },
  info: (module: string, message: string, data?: any) => {
    console.info(`[${module}] ${message}`, data)
    try {
      logInfo(module, message, data)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  },
  debug: (module: string, message: string, data?: any) => {
    console.debug(`[${module}] ${message}`, data)
    try {
      logDebug(module, message, data)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  },
  success: (module: string, message: string, data?: any) => {
    console.log(`[${module}] ✅ ${message}`, data)
    try {
      logSuccess(module, message, data)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  }
}

// واجهة GlobalLogger للتوافق مع globalLogger.ts
export const GlobalLogger = {
  error: (module: string, message: string, error?: unknown) => {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] [ERROR] [${module}] ${message}`
    console.error(logMessage, error || '')

    // محاولة إرسال للخادم إذا كان متاحاً
    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        (window.electronAPI as any).logError?.({
          timestamp: new Date(),
          level: 'ERROR',
          module,
          message,
          error: error instanceof Error ? error.message : String(error || ''),
          stack: error instanceof Error ? error.stack : undefined
        })
      }
      logError(module, message, error)
    } catch (_e) {
      // تجاهل أخطاء الإرسال
    }
  },

  warn: (module: string, message: string, data?: any) => {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] [WARN] [${module}] ${message}`
    console.warn(logMessage, data || '')
    try {
      logWarn(module, message, data)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  },

  info: (module: string, message: string, data?: any) => {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] [INFO] [${module}] ${message}`
    console.info(logMessage, data || '')
    try {
      logInfo(module, message, data)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  },

  debug: (module: string, message: string, data?: any) => {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] [DEBUG] [${module}] ${message}`
    console.debug(logMessage, data || '')
    try {
      logDebug(module, message, data)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  },

  success: (module: string, message: string, data?: any) => {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] [SUCCESS] [${module}] ${message}`
    console.log(logMessage, data || '')
    try {
      logSuccess(module, message, data)
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل التطبيق
    }
  }
}

// تعيين Logger عالمياً للتوافق
if (typeof window !== 'undefined') {
  (window as any).Logger = GlobalLogger
}

if (typeof global !== 'undefined') {
  (global as any).Logger = GlobalLogger
}

if (typeof globalThis !== 'undefined') {
  (globalThis as any).Logger = GlobalLogger
}

// التصديرات الافتراضية
export default SimpleLogger
