import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Row,
  Col,
  Statistic,
  DatePicker,
  Space,
  message,
  Tabs,
  Progress,
  Tag,
  Alert,
  Divider,
  Typography
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'

import {
  ArrowLeftOutlined,
  Bar<PERSON>hartOutlined,
  DollarOutlined,
  BankOutlined,
  CreditCardOutlined,
  FileTextOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  RiseOutlined,
  PieChartOutlined,
  LineChartOutlined,
  PrinterOutlined,
  // DownloadOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import { UnifiedPrintButton } from '../common'

const { RangePicker } = DatePicker
const { Title, Text } = Typography

interface ComprehensiveFinanceReportsProps {
  onBack: () => void
}

const ComprehensiveFinanceReports: React.FC<ComprehensiveFinanceReportsProps> = ({ onBack }) => {
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  // const [showPrintExport, setShowPrintExport] = useState(false) // تم حذف نظام الطباعة
  const [dateRange, setDateRange] = useState<any>([
    dayjs().subtract(30, 'day'),
    dayjs()
  ])
  const [reportData, setReportData] = useState({
    // البيانات المالية الأساسية
    bankAccounts: [],
    checks: [],
    paymentVouchers: [],
    receiptVouchers: [],
    
    // بيانات المبيعات والمشتريات
    salesInvoices: [],
    purchaseInvoices: [],
    
    // بيانات الإنتاج والدهان
    productionCosts: [],
    paintInvoices: [],
    
    // المعاملات والتحويلات
    transactions: [],
    checkTransfers: []
  })

  useEffect(() => {
    loadComprehensiveReportData()
  }, [dateRange])

  const loadComprehensiveReportData = async () => {
    setLoading(true)
    try {
      Logger.info('ComprehensiveFinanceReports', '🔄 جاري تحميل بيانات التقرير المالي الشامل...')



      if (!window.electronAPI) {
        Logger.error('ComprehensiveFinanceReports', '❌ window.electronAPI غير متوفر')
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      }

      const [
        bankAccountsResponse,
        checksResponse,
        paymentVouchersResponse,
        receiptVouchersResponse,
        salesInvoicesResponse,
        purchaseInvoicesResponse,
        productionCostsResponse,
        paintInvoicesResponse,
        transactionsResponse,
        checkTransfersResponse
      ] = await Promise.all([
        window.electronAPI.getBankAccounts(),
        window.electronAPI.getChecks(),
        window.electronAPI.getPaymentVouchers(),
        window.electronAPI.getReceiptVouchers(),
        window.electronAPI.getSalesInvoices(),
        window.electronAPI.getPurchaseInvoices(),
        window.electronAPI.getProductionCosts(),
        window.electronAPI.getPaintInvoices(),
        window.electronAPI.getBankTransactions(),
        window.electronAPI.getCheckTransfers()
      ])

      Logger.info('ComprehensiveFinanceReports', '✅ تم تحميل البيانات الحقيقية للتقرير المالي الشامل')

      setReportData({
        bankAccounts: bankAccountsResponse.success ? bankAccountsResponse.data : [],
        checks: checksResponse.success ? checksResponse.data : [],
        paymentVouchers: paymentVouchersResponse.success ? paymentVouchersResponse.data : [],
        receiptVouchers: receiptVouchersResponse.success ? receiptVouchersResponse.data : [],
        salesInvoices: salesInvoicesResponse.success ? salesInvoicesResponse.data : [],
        purchaseInvoices: purchaseInvoicesResponse.success ? purchaseInvoicesResponse.data : [],
        productionCosts: productionCostsResponse.success ? productionCostsResponse.data : [],
        paintInvoices: paintInvoicesResponse.success ? paintInvoicesResponse.data : [],
        transactions: transactionsResponse.success ? transactionsResponse.data : [],
        checkTransfers: checkTransfersResponse.success ? checkTransfersResponse.data : []
      })
    } catch (_error) {
      message.error('خطأ في تحميل بيانات التقارير الشاملة')
    }
    setLoading(false)
  }

  // حساب المؤشرات المالية الشاملة
  const calculateComprehensiveStats = () => {
    const {
      bankAccounts,
      checks,
      paymentVouchers,
      receiptVouchers,
      salesInvoices,
      purchaseInvoices,
      productionCosts,
      paintInvoices,
      transactions
    } = reportData

    // الأرصدة والسيولة
    const totalBankBalance = bankAccounts.reduce((sum: number, account: any) => sum + (account.balance || 0), 0)
    const totalCash = totalBankBalance // يمكن إضافة النقد في الصندوق لاحقاً

    // الإيرادات
    const salesRevenue = salesInvoices.reduce((sum: number, invoice: any) => sum + (invoice.total_amount || 0), 0)
    const paintRevenue = paintInvoices.reduce((sum: number, invoice: any) => sum + (invoice.total_amount || 0), 0)
    const totalRevenue = salesRevenue + paintRevenue

    // التكاليف والمصروفات
    const purchaseCosts = purchaseInvoices.reduce((sum: number, invoice: any) => sum + (invoice.total_amount || 0), 0)
    const productionCostsTotal = productionCosts.reduce((sum: number, cost: any) => sum + (cost.amount || 0), 0)
    const operationalExpenses = paymentVouchers.reduce((sum: number, voucher: any) => sum + (voucher.amount || 0), 0)
    const totalCosts = purchaseCosts + productionCostsTotal + operationalExpenses

    // الأرباح
    const grossProfit = totalRevenue - purchaseCosts
    const netProfit = totalRevenue - totalCosts
    const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0

    // المديونيات
    const salesReceivables = salesInvoices
      .filter((invoice: any) => invoice.payment_status !== 'paid')
      .reduce((sum: number, invoice: any) => sum + (invoice.remaining_amount || invoice.total_amount || 0), 0)
    
    const paintReceivables = paintInvoices
      .filter((invoice: any) => invoice.payment_status !== 'paid')
      .reduce((sum: number, invoice: any) => sum + (invoice.remaining_amount || invoice.total_amount || 0), 0)
    
    const totalReceivables = salesReceivables + paintReceivables

    const purchasePayables = purchaseInvoices
      .filter((invoice: any) => invoice.payment_status !== 'paid')
      .reduce((sum: number, invoice: any) => sum + (invoice.remaining_amount || invoice.total_amount || 0), 0)

    // الشيكات
    const pendingChecksValue = checks
      .filter((check: any) => check.status === 'issued')
      .reduce((sum: number, check: any) => sum + (check.amount || 0), 0)

    // التدفق النقدي
    const cashInflow = receiptVouchers.reduce((sum: number, voucher: any) => sum + (voucher.amount || 0), 0)
    const cashOutflow = paymentVouchers.reduce((sum: number, voucher: any) => sum + (voucher.amount || 0), 0)
    const netCashFlow = cashInflow - cashOutflow

    // نسب مالية مهمة
    const currentRatio = totalCash / (purchasePayables || 1) // نسبة السيولة
    const debtToEquity = purchasePayables / (totalCash + totalReceivables || 1) // نسبة الدين إلى حقوق الملكية
    const assetTurnover = totalRevenue / (totalCash + totalReceivables || 1) // معدل دوران الأصول

    return {
      // السيولة والأرصدة
      totalBankBalance,
      totalCash,
      
      // الإيرادات والأرباح
      salesRevenue,
      paintRevenue,
      totalRevenue,
      grossProfit,
      netProfit,
      profitMargin,
      
      // التكاليف
      purchaseCosts,
      productionCostsTotal,
      operationalExpenses,
      totalCosts,
      
      // المديونيات
      salesReceivables,
      paintReceivables,
      totalReceivables,
      purchasePayables,
      
      // الشيكات والتدفق النقدي
      pendingChecksValue,
      cashInflow,
      cashOutflow,
      netCashFlow,
      
      // النسب المالية
      currentRatio,
      debtToEquity,
      assetTurnover,
      
      // إحصائيات إضافية
      totalInvoices: salesInvoices.length + purchaseInvoices.length + paintInvoices.length,
      totalTransactions: transactions.length,
      totalChecks: checks.length
    }
  }

  const stats = calculateComprehensiveStats()

  // تقرير الأداء المالي العام
  const renderOverviewReport = () => (
    <div>
      <Alert
        message="ملخص الأداء المالي"
        description={'صافي الربح: ₪' + stats.netProfit.toLocaleString() + ' | هامش الربح: ' + stats.profitMargin.toFixed(1) + '% | نسبة السيولة: ' + stats.currentRatio.toFixed(2)}
        type={stats.netProfit >= 0 ? 'success' : 'warning'}
        showIcon
        style={{ marginBottom: '24px' }}
      />

      {/* المؤشرات الرئيسية */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الإيرادات"
              value={stats.totalRevenue}
              valueStyle={{ color: '#52c41a' }}
              prefix={<RiseOutlined />}
              suffix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="صافي الربح"
              value={stats.netProfit}
              valueStyle={{ color: stats.netProfit >= 0 ? '#52c41a' : '#ff4d4f' }}
              prefix={stats.netProfit >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
              suffix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="هامش الربح"
              value={stats.profitMargin}
              valueStyle={{ color: stats.profitMargin >= 0 ? '#52c41a' : '#ff4d4f' }}
              prefix={<PieChartOutlined />}
              suffix="%"
              precision={1}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="السيولة النقدية"
              value={stats.totalCash}
              valueStyle={{ color: '#1890ff' }}
              prefix={<BankOutlined />}
              suffix="₪"
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      {/* تحليل الإيرادات */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={12}>
          <Card title="تحليل الإيرادات" extra={<PieChartOutlined />}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="إيرادات المبيعات"
                  value={stats.salesRevenue}
                  valueStyle={{ color: '#1890ff' }}
                  suffix="₪"
                  precision={0}
                />
                <Progress 
                  percent={stats.totalRevenue > 0 ? (stats.salesRevenue / stats.totalRevenue) * 100 : 0} 
                  strokeColor="#1890ff"
                  size="small"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="إيرادات الدهان"
                  value={stats.paintRevenue}
                  valueStyle={{ color: '#722ed1' }}
                  suffix="₪"
                  precision={0}
                />
                <Progress 
                  percent={stats.totalRevenue > 0 ? (stats.paintRevenue / stats.totalRevenue) * 100 : 0} 
                  strokeColor="#722ed1"
                  size="small"
                />
              </Col>
            </Row>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="تحليل التكاليف" extra={<BarChartOutlined />}>
            <Row gutter={16}>
              <Col span={8}>
                <Text type="secondary">تكاليف المشتريات</Text>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#ff4d4f' }}>
                  ₪{stats.purchaseCosts.toLocaleString()}
                </div>
                <Progress 
                  percent={stats.totalCosts > 0 ? (stats.purchaseCosts / stats.totalCosts) * 100 : 0} 
                  strokeColor="#ff4d4f"
                  size="small"
                />
              </Col>
              <Col span={8}>
                <Text type="secondary">تكاليف الإنتاج</Text>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#fa8c16' }}>
                  ₪{stats.productionCostsTotal.toLocaleString()}
                </div>
                <Progress 
                  percent={stats.totalCosts > 0 ? (stats.productionCostsTotal / stats.totalCosts) * 100 : 0} 
                  strokeColor="#fa8c16"
                  size="small"
                />
              </Col>
              <Col span={8}>
                <Text type="secondary">المصروفات التشغيلية</Text>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#13c2c2' }}>
                  ₪{stats.operationalExpenses.toLocaleString()}
                </div>
                <Progress 
                  percent={stats.totalCosts > 0 ? (stats.operationalExpenses / stats.totalCosts) * 100 : 0} 
                  strokeColor="#13c2c2"
                  size="small"
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* المديونيات والالتزامات */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={8}>
          <Card title="المديونيات (المستحقات)" extra={<CreditCardOutlined style={{ color: '#52c41a' }} />}>
            <Statistic
              title="مديونيات المبيعات"
              value={stats.salesReceivables}
              valueStyle={{ color: '#52c41a' }}
              suffix="₪"
              precision={0}
            />
            <Statistic
              title="مديونيات الدهان"
              value={stats.paintReceivables}
              valueStyle={{ color: '#722ed1' }}
              suffix="₪"
              precision={0}
            />
            <Divider />
            <Statistic
              title="إجمالي المديونيات"
              value={stats.totalReceivables}
              valueStyle={{ color: '#1890ff', fontWeight: 'bold' }}
              suffix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="الالتزامات (المستحقات)" extra={<FileTextOutlined style={{ color: '#ff4d4f' }} />}>
            <Statistic
              title="مستحقات المشتريات"
              value={stats.purchasePayables}
              valueStyle={{ color: '#ff4d4f' }}
              suffix="₪"
              precision={0}
            />
            <Statistic
              title="شيكات معلقة"
              value={stats.pendingChecksValue}
              valueStyle={{ color: '#fa8c16' }}
              suffix="₪"
              precision={0}
            />
            <Divider />
            <Statistic
              title="إجمالي الالتزامات"
              value={stats.purchasePayables + stats.pendingChecksValue}
              valueStyle={{ color: '#ff4d4f', fontWeight: 'bold' }}
              suffix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="النسب المالية" extra={<LineChartOutlined style={{ color: '#13c2c2' }} />}>
            <div style={{ marginBottom: '16px' }}>
              <Text type="secondary">نسبة السيولة</Text>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: stats.currentRatio >= 1 ? '#52c41a' : '#ff4d4f' }}>
                {stats.currentRatio.toFixed(2)}
              </div>
              <Progress 
                percent={Math.min(stats.currentRatio * 50, 100)} 
                strokeColor={stats.currentRatio >= 1 ? '#52c41a' : '#ff4d4f'}
                size="small"
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <Text type="secondary">نسبة الدين إلى حقوق الملكية</Text>
              <div style={{ fontSize: '18px', fontWeight: 'bold', color: stats.debtToEquity <= 1 ? '#52c41a' : '#fa8c16' }}>
                {stats.debtToEquity.toFixed(2)}
              </div>
            </div>
            <div>
              <Text type="secondary">معدل دوران الأصول</Text>
              <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#1890ff' }}>
                {stats.assetTurnover.toFixed(2)}
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )

  // تقرير التدفق النقدي
  const renderCashFlowReport = () => (
    <div>
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="التدفق النقدي الداخل"
              value={stats.cashInflow}
              valueStyle={{ color: '#52c41a' }}
              prefix={<ArrowUpOutlined />}
              suffix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="التدفق النقدي الخارج"
              value={stats.cashOutflow}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<ArrowDownOutlined />}
              suffix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="صافي التدفق النقدي"
              value={stats.netCashFlow}
              valueStyle={{ color: stats.netCashFlow >= 0 ? '#52c41a' : '#ff4d4f' }}
              prefix={stats.netCashFlow >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
              suffix="₪"
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      <Card title="تفاصيل التدفق النقدي">
        <Row gutter={16}>
          <Col span={12}>
            <Title level={4} style={{ color: '#52c41a' }}>التدفقات الداخلة</Title>
            <div style={{ marginBottom: '16px' }}>
              <Text>سندات القبض: </Text>
              <Text strong style={{ color: '#52c41a' }}>₪{stats.cashInflow.toLocaleString()}</Text>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <Text>مديونيات محصلة: </Text>
              <Text strong style={{ color: '#52c41a' }}>₪{(stats.totalRevenue - stats.totalReceivables).toLocaleString()}</Text>
            </div>
          </Col>
          <Col span={12}>
            <Title level={4} style={{ color: '#ff4d4f' }}>التدفقات الخارجة</Title>
            <div style={{ marginBottom: '16px' }}>
              <Text>سندات الدفع: </Text>
              <Text strong style={{ color: '#ff4d4f' }}>₪{stats.cashOutflow.toLocaleString()}</Text>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <Text>مدفوعات المشتريات: </Text>
              <Text strong style={{ color: '#ff4d4f' }}>₪{(stats.purchaseCosts - stats.purchasePayables).toLocaleString()}</Text>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  )

  // تقرير المديونيات التفصيلي
  const renderReceivablesReport = () => {
    const receivablesData = [
      {
        key: '1',
        department: 'المبيعات',
        amount: stats.salesReceivables,
        percentage: stats.totalReceivables > 0 ? (stats.salesReceivables / stats.totalReceivables) * 100 : 0,
        color: '#1890ff'
      },
      {
        key: '2',
        department: 'الدهان',
        amount: stats.paintReceivables,
        percentage: stats.totalReceivables > 0 ? (stats.paintReceivables / stats.totalReceivables) * 100 : 0,
        color: '#722ed1'
      }
    ]

    const receivablesColumns = [
      {
        title: 'القسم',
        dataIndex: 'department',
        key: 'department',
        render: (text: string, record: any) => (
          <Tag color={record.color}>{text}</Tag>
        )
      },
      {
        title: 'المبلغ المستحق',
        dataIndex: 'amount',
        key: 'amount',
        render: (amount: number) => (
          <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
            ₪{amount.toLocaleString()}
          </span>
        )
      },
      {
        title: 'النسبة',
        dataIndex: 'percentage',
        key: 'percentage',
        render: (percentage: number) => (
          <div>
            <span>{percentage.toFixed(1)}%</span>
            <Progress percent={percentage} size="small" />
          </div>
        )
      }
    ]

    return (
      <div>
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={8}>
            <Card>
              <Statistic
                title="إجمالي المديونيات"
                value={stats.totalReceivables}
                valueStyle={{ color: '#1890ff' }}
                prefix={<CreditCardOutlined />}
                suffix="₪"
                precision={0}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="نسبة التحصيل"
                value={stats.totalRevenue > 0 ? ((stats.totalRevenue - stats.totalReceivables) / stats.totalRevenue) * 100 : 0}
                valueStyle={{ color: '#52c41a' }}
                prefix={<RiseOutlined />}
                suffix="%"
                precision={1}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="متوسط فترة التحصيل"
                value={30} // يمكن حسابها بناءً على البيانات الفعلية
                valueStyle={{ color: '#fa8c16' }}
                suffix="يوم"
              />
            </Card>
          </Col>
        </Row>

        <Card title="تفاصيل المديونيات حسب القسم">
          <Table
            columns={receivablesColumns}
            dataSource={receivablesData}
            pagination={false}
            size="small"
          />
        </Card>
      </div>
    )
  }

  // تقرير تحليل الربحية
  const renderProfitabilityReport = () => (
    <div>
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="الربح الإجمالي"
              value={stats.grossProfit}
              valueStyle={{ color: stats.grossProfit >= 0 ? '#52c41a' : '#ff4d4f' }}
              prefix={<DollarOutlined />}
              suffix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="هامش الربح الإجمالي"
              value={stats.totalRevenue > 0 ? (stats.grossProfit / stats.totalRevenue) * 100 : 0}
              valueStyle={{ color: '#1890ff' }}
              prefix={<PieChartOutlined />}
              suffix="%"
              precision={1}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="الربح الصافي"
              value={stats.netProfit}
              valueStyle={{ color: stats.netProfit >= 0 ? '#52c41a' : '#ff4d4f' }}
              prefix={<RiseOutlined />}
              suffix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="هامش الربح الصافي"
              value={stats.profitMargin}
              valueStyle={{ color: stats.profitMargin >= 0 ? '#52c41a' : '#ff4d4f' }}
              prefix={<LineChartOutlined />}
              suffix="%"
              precision={1}
            />
          </Card>
        </Col>
      </Row>

      <Card title="تحليل الربحية التفصيلي">
        <Row gutter={16}>
          <Col span={12}>
            <div style={{ marginBottom: '24px' }}>
              <Title level={4}>هيكل الإيرادات</Title>
              <div style={{ marginBottom: '8px' }}>
                <Text>إيرادات المبيعات: </Text>
                <Text strong style={{ color: '#1890ff' }}>₪{stats.salesRevenue.toLocaleString()}</Text>
                <Progress
                  percent={stats.totalRevenue > 0 ? (stats.salesRevenue / stats.totalRevenue) * 100 : 0}
                  strokeColor="#1890ff"
                  size="small"
                />
              </div>
              <div style={{ marginBottom: '8px' }}>
                <Text>إيرادات الدهان: </Text>
                <Text strong style={{ color: '#722ed1' }}>₪{stats.paintRevenue.toLocaleString()}</Text>
                <Progress
                  percent={stats.totalRevenue > 0 ? (stats.paintRevenue / stats.totalRevenue) * 100 : 0}
                  strokeColor="#722ed1"
                  size="small"
                />
              </div>
            </div>
          </Col>
          <Col span={12}>
            <div style={{ marginBottom: '24px' }}>
              <Title level={4}>هيكل التكاليف</Title>
              <div style={{ marginBottom: '8px' }}>
                <Text>تكلفة البضاعة المباعة: </Text>
                <Text strong style={{ color: '#ff4d4f' }}>₪{stats.purchaseCosts.toLocaleString()}</Text>
                <Progress
                  percent={stats.totalRevenue > 0 ? (stats.purchaseCosts / stats.totalRevenue) * 100 : 0}
                  strokeColor="#ff4d4f"
                  size="small"
                />
              </div>
              <div style={{ marginBottom: '8px' }}>
                <Text>تكاليف الإنتاج: </Text>
                <Text strong style={{ color: '#fa8c16' }}>₪{stats.productionCostsTotal.toLocaleString()}</Text>
                <Progress
                  percent={stats.totalRevenue > 0 ? (stats.productionCostsTotal / stats.totalRevenue) * 100 : 0}
                  strokeColor="#fa8c16"
                  size="small"
                />
              </div>
              <div style={{ marginBottom: '8px' }}>
                <Text>المصروفات التشغيلية: </Text>
                <Text strong style={{ color: '#13c2c2' }}>₪{stats.operationalExpenses.toLocaleString()}</Text>
                <Progress
                  percent={stats.totalRevenue > 0 ? (stats.operationalExpenses / stats.totalRevenue) * 100 : 0}
                  strokeColor="#13c2c2"
                  size="small"
                />
              </div>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  )

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>📊 التقارير المالية الشاملة</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            تقارير متقدمة تشمل جميع الأقسام مع تحليل شامل للأداء المالي
          </p>
        </div>
        <Space>
          <UnifiedPrintButton
            data={{
              title: 'التقرير المالي الشامل',
              subtitle: `من ${dateRange?.[0]?.format('YYYY-MM-DD')} إلى ${dateRange?.[1]?.format('YYYY-MM-DD')}`,
              date: new Date().toLocaleDateString('ar-SA'),
              items: [
                {
                  id: 1,
                  name: 'سندات القبض',
                  description: 'مجموع سندات القبض للفترة',
                  quantity: reportData.receiptVouchers?.length || 0,
                  unit: 'سند',
                  unitPrice: reportData.receiptVouchers?.reduce((sum: number, v: any) => sum + (v.amount || 0), 0) || 0,
                  total: reportData.receiptVouchers?.reduce((sum: number, v: any) => sum + (v.amount || 0), 0) || 0
                },
                {
                  id: 2,
                  name: 'سندات الدفع',
                  description: 'مجموع سندات الدفع للفترة',
                  quantity: reportData.paymentVouchers?.length || 0,
                  unit: 'سند',
                  unitPrice: reportData.paymentVouchers?.reduce((sum: number, v: any) => sum + (v.amount || 0), 0) || 0,
                  total: reportData.paymentVouchers?.reduce((sum: number, v: any) => sum + (v.amount || 0), 0) || 0
                },
                {
                  id: 3,
                  name: 'الشيكات',
                  description: 'مجموع الشيكات للفترة',
                  quantity: reportData.checks?.length || 0,
                  unit: 'شيك',
                  unitPrice: reportData.checks?.reduce((sum: number, c: any) => sum + (c.amount || 0), 0) || 0,
                  total: reportData.checks?.reduce((sum: number, c: any) => sum + (c.amount || 0), 0) || 0
                }
              ],
              total: (reportData.receiptVouchers?.reduce((sum: number, v: any) => sum + (v.amount || 0), 0) || 0) - (reportData.paymentVouchers?.reduce((sum: number, v: any) => sum + (v.amount || 0), 0) || 0),
              notes: `عدد سندات القبض: ${reportData.receiptVouchers?.length || 0}\nعدد سندات الدفع: ${reportData.paymentVouchers?.length || 0}\nعدد الشيكات: ${reportData.checks?.length || 0}\nعدد الحسابات البنكية: ${reportData.bankAccounts?.length || 0}`
            }}
            type="report"
            subType="financial"
            buttonText="طباعة التقرير الشامل"
            size="middle"
            showDropdown={true}
            _documentId="comprehensive_finance_report"
            onAfterPrint={() => message.success('تم طباعة التقرير المالي الشامل بنجاح')}
            onError={() => message.error('فشل في طباعة التقرير المالي الشامل')}
          />
          <Button
            type="default"
            icon={<ArrowLeftOutlined />}
            onClick={onBack}
          >
            العودة
          </Button>
        </Space>
      </div>

      {/* فلاتر التقارير */}
      <Card style={{ marginBottom: '24px' }}>
        <Space>
          <span>فترة التقرير:</span>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            format="YYYY-MM-DD"
          />
          <Button type="primary" onClick={loadComprehensiveReportData} loading={loading}>
            تحديث التقرير
          </Button>
        </Space>
      </Card>

      {/* التقارير المتقدمة */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'overview',
            label: 'نّرة عامة',
            children: renderOverviewReport()
          },
          {
            key: 'cashflow',
            label: 'التدفق النقدي',
            children: renderCashFlowReport()
          },
          {
            key: 'receivables',
            label: 'المديونيات',
            children: renderReceivablesReport()
          },
          {
            key: 'profitability',
            label: 'تحليل الربحية',
            children: renderProfitabilityReport()
          }
        ]}
      />

      {/* تم حذف مكون الطباعة والتصدير المتقدم */}
      {/* <AdvancedPrintExport
        reportType="financial-summary"
        reportTitle="التقرير المالي الشامل"
        visible={showPrintExport}
        onClose={() => setShowPrintExport(false)}
        onAction={(action, options) => {
          Logger.info('ComprehensiveFinanceReports', 'Print/Export action: ' + action)
          if (options.success) {
            setShowPrintExport(false)
          }
        }}
      /> */}
    </div>
  )
}

export default ComprehensiveFinanceReports
