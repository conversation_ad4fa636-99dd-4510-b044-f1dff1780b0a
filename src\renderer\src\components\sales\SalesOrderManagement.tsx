import React, { useState, useEffect } from 'react'
import { SafeLogger as Logger } from '../../utils/logger'
import * as XLSX from 'xlsx'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Space,
  Popconfirm,
  // message,
  Card,
  Row,
  Col,
  Statistic,
  Tag,
  Select,
  DatePicker,
  InputNumber,
  Divider,
  App
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ShopOutlined,
  UserOutlined,
  CalendarOutlined,
  DollarOutlined,
  ArrowLeftOutlined,
  SearchOutlined,
  FileTextOutlined,
  DownloadOutlined,
  PrinterOutlined
} from '@ant-design/icons'
import UnifiedPrintButton from '../common/UnifiedPrintButton'
import dayjs from 'dayjs'
import { useCurrentUser } from '../../utils/permissions'

const { Option } = Select
const { TextArea } = Input

interface SalesOrder {
  id: number
  order_number: string
  customer_id: number
  customer_name: string
  order_date: string
  expected_date?: string
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled'
  total_amount: number
  notes?: string
  created_by: number
  created_at: string
  updated_at: string
}

interface SalesOrderItem {
  id?: number
  item_id: number
  item_name: string
  warehouse_id: number
  warehouse_name?: string
  quantity: number
  unit_price: number
  total_price: number
}

interface Customer {
  id: number
  name: string
  code: string
}

interface Item {
  id: number
  name: string
  code: string
  sale_price: number
}

interface SalesOrderManagementProps {
  onBack: () => void
}

const SalesOrderManagement: React.FC<SalesOrderManagementProps> = ({ onBack }) => {
  const { message: messageApi } = App.useApp()
  const { id: userId } = useCurrentUser()
  const [orders, setOrders] = useState<SalesOrder[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [items, setItems] = useState<Item[]>([])
  const [warehouses, setWarehouses] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingOrder, setEditingOrder] = useState<SalesOrder | null>(null)
  const [orderItems, setOrderItems] = useState<SalesOrderItem[]>([])
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState('')

  useEffect(() => {
    loadOrders()
    loadCustomers()
    loadItems()
    loadWarehouses()
  }, [])

  const loadOrders = async () => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getSalesOrders()
        if (response.success) {
          const ordersData = response.data || []
          setOrders(ordersData)

          // تشخيص البيانات
          console.log('🔍 تشخيص أوامر البيع:', {
            totalOrders: ordersData.length,
            sampleOrder: ordersData[0] || null,
            loading: false
          })
        } else {
          messageApi.error('فشل في تحميل أوامر البيع')
        }
      }
    } catch (error) {
      messageApi.error('حدث خطأ أثناء تحميل أوامر البيع')
    } finally {
      setLoading(false)
    }
  }

  const loadCustomers = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getCustomers()
        if (response.success) {
          setCustomers(response.data || [])
        }
      }
    } catch (error) {
      Logger.error('SalesOrderManagement', 'خطأ في تحميل العملاء:', error)
    }
  }

  const loadItems = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getItems()
        if (Array.isArray(response)) {
          setItems(response.filter(item => item.is_active !== false) || [])
        }
      }
    } catch (error) {
      Logger.error('SalesOrderManagement', 'خطأ في تحميل الأصناف:', error)
    }
  }

  const loadWarehouses = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getWarehouses()
        if (response && typeof response === 'object' && 'success' in response && (response as any).success) {
          const warehousesData = (response as any).data || []
          setWarehouses(warehousesData.filter((w: any) => w.is_active !== false))
          Logger.info('SalesOrderManagement', `✅ تم تحميل ${warehousesData.length} مخزن بنجاح`)
        } else if (Array.isArray(response)) {
          setWarehouses(response.filter((w: any) => w.is_active !== false))
          Logger.info('SalesOrderManagement', `✅ تم تحميل ${response.length} مخزن بنجاح`)
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل المخازن'
          Logger.error('SalesOrderManagement', '❌ خطأ في استجابة المخازن:', errorMessage)
          setWarehouses([])
        }
      } else {
        Logger.warn('SalesOrderManagement', '⚠️ window.electronAPI غير متوفر')
        setWarehouses([])
      }
    } catch (error) {
      Logger.error('SalesOrderManagement', 'خطأ في تحميل المخازن:', error as Error)
      setWarehouses([])
    }
  }

  // دالة للتحقق من تفرد رقم الأمر
  const checkOrderNumberUniqueness = async (orderNumber: string): Promise<boolean> => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getSalesOrders()
        if (response.success && response.data) {
          const existingOrder = response.data.find((order: SalesOrder) =>
            order.order_number.toLowerCase() === orderNumber.toLowerCase() &&
            (!editingOrder || order.id !== editingOrder.id)
          )
          return !existingOrder
        }
      }
      return true
    } catch (error) {
      Logger.error('SalesOrderManagement', 'خطأ في التحقق من تفرد رقم الأمر:', error)
      return true // في حالة الخطأ، نسمح بالمتابعة
    }
  }

  const generateOrderNumber = async () => {
    try {
      let attempts = 0
      let orderNumber = ''
      let isUnique = false

      // محاولة إنشاء رقم فريد (حتى 5 محاولات)
      while (!isUnique && attempts < 5) {
        const timestamp = Date.now().toString().slice(-6)
        const randomSuffix = Math.floor(Math.random() * 100).toString().padStart(2, '0')
        orderNumber = `SO${timestamp}${randomSuffix}`

        isUnique = await checkOrderNumberUniqueness(orderNumber)
        attempts++

        if (!isUnique) {
          // انتّار قصير قبل المحاولة التالية
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }

      form.setFieldsValue({ order_number: orderNumber })
      if (isUnique) {
        messageApi.success('تم إنشاء رقم أمر فريد')
      } else {
        messageApi.warning('تم إنشاء رقم الأمر - يرجى التحقق من عدم تكراره')
      }
    } catch (error) {
      Logger.error('SalesOrderManagement', 'خطأ في إنشاء رقم الأمر:', error)
      // fallback بسيط
      const timestamp = Date.now().toString().slice(-6)
      const orderNumber = `SO${timestamp}`
      form.setFieldsValue({ order_number: orderNumber })
    }
  }

  // دالة للتحقق من توفر الكميات (تحذيرية فقط للأوامر)
  const checkItemsAvailability = async (items: SalesOrderItem[]): Promise<{success: boolean, warnings: string[]}> => {
    const warnings: string[] = []

    try {
      if (window.electronAPI) {
        for (let i = 0; i < items.length; i++) {
          const item = items[i]

          // محاولة الحصول على معلومات المخزون (إذا كان متوفراً)
          try {
            const inventoryResponse = await window.electronAPI.getItemInventory(item.item_id)

            if (inventoryResponse.success && inventoryResponse.data) {
              const totalAvailable = inventoryResponse.data.total_quantity || 0

              if (totalAvailable < item.quantity) {
                warnings.push(`تحذير: الكمية المطلوبة (${item.quantity}) من "${item.item_name}" قد تكون غير متوفرة. الكمية المتوفرة: ${totalAvailable}`)
              }
            }
          } catch (error) {
            // تجاهل أخطاء المخزون في الأوامر
            Logger.warn('SalesOrderManagement', `تعذر التحقق من مخزون "${item.item_name}"`, error)
          }
        }
      }
    } catch (error) {
      Logger.error('SalesOrderManagement', 'خطأ في التحقق من المخزون:', error)
    }

    return { success: true, warnings }
  }

  const handleSubmit = async (values: any) => {
    if (orderItems.length === 0) {
      messageApi.error('يجب إضافة صنف واحد على الأقل')
      return
    }

    // التحقق من صحة التواريخ
    if (values.expected_date && values.order_date) {
      const orderDate = dayjs(values.order_date)
      const expectedDate = dayjs(values.expected_date)

      if (expectedDate.isBefore(orderDate)) {
        messageApi.error('تاريخ التسليم المتوقع لا يمكن أن يكون قبل تاريخ الأمر')
        return
      }

      if (expectedDate.isBefore(dayjs(), 'day')) {
        messageApi.warning('تاريخ التسليم المتوقع في الماضي. هل تريد المتابعة؟')
      }
    }

    // التحقق من صحة الكميات والأسعار
    for (let i = 0; i < orderItems.length; i++) {
      const item = orderItems[i]
      if (item.quantity <= 0) {
        messageApi.error(`الكمية يجب أن تكون أكبر من صفر في السطر ${i + 1}`)
        return
      }
      if (item.unit_price <= 0) {
        messageApi.error(`سعر الوحدة يجب أن يكون أكبر من صفر في السطر ${i + 1}`)
        return
      }
      // التحقق من الكميات العشرية المنطقية
      if (item.quantity % 0.001 !== 0) {
        messageApi.error(`الكمية في السطر ${i + 1} تحتوي على دقة عشرية غير مناسبة`)
        return
      }
    }

    // التحقق من تفرد رقم الأمر
    const isOrderNumberUnique = await checkOrderNumberUniqueness(values.order_number)
    if (!isOrderNumberUnique) {
      messageApi.error(`رقم الأمر "${values.order_number}" موجود مسبقاً. يرجى استخدام رقم آخر.`)
      form.setFields([{
        name: 'order_number',
        errors: ['هذا الرقم موجود مسبقاً']
      }])
      return
    }

    // التحقق من توفر الكميات (تحذيرات فقط)
    const availabilityCheck = await checkItemsAvailability(orderItems)
    if (availabilityCheck.warnings.length > 0) {
      // عرض التحذيرات للمستخدم
      availabilityCheck.warnings.forEach(warning => {
        messageApi.warning(warning, 5) // عرض لمدة 5 ثوان
      })
    }

    try {
      if (window.electronAPI) {
        const orderData = {
          ...values,
          customer_id: parseInt(String(values.customer_id)), // تحويل معرف العميل إلى رقم صحيح
          expected_date: values.expected_date ? values.expected_date.format('YYYY-MM-DD') : null,
          items: orderItems,
          created_by: userId
        }

        let response
        if (editingOrder) {
          response = await window.electronAPI.updateSalesOrder(editingOrder.id, orderData)
        } else {
          response = await window.electronAPI.createSalesOrder(orderData)
        }

        if (response.success) {
          messageApi.success(editingOrder ? 'تم تحديث أمر البيع بنجاح' : 'تم إنشاء أمر البيع بنجاح')
          setModalVisible(false)
          form.resetFields()
          setEditingOrder(null)
          setOrderItems([])
          loadOrders()
        } else {
          messageApi.error(response.message || 'فشل في حفّ أمر البيع')
        }
      }
    } catch (error) {
      messageApi.error('حدث خطأ أثناء حفّ أمر البيع')
    }
  }

  const handleEdit = async (order: SalesOrder) => {
    setEditingOrder(order)
    form.setFieldsValue({
      ...order,
      order_date: dayjs(order.order_date),
      expected_date: order.expected_date ? dayjs(order.expected_date) : null
    })

    // تحميل عناصر الأمر
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getSalesOrderItems(order.id)
        if (response.success) {
          setOrderItems(response.data || [])
        }
      }
    } catch (error) {
      Logger.error('SalesOrderManagement', 'خطأ في تحميل عناصر الأمر:', error)
    }

    setModalVisible(true)
  }

  const handleDelete = async (orderId: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.deleteSalesOrder(orderId)
        if (response.success) {
          messageApi.success('تم حذف أمر البيع بنجاح')
          loadOrders()
        } else {
          messageApi.error(response.message || 'فشل في حذف أمر البيع')
        }
      }
    } catch (error) {
      messageApi.error('حدث خطأ أثناء حذف أمر البيع')
    }
  }

  const handleAdd = () => {
    setEditingOrder(null)
    form.resetFields()
    form.setFieldsValue({ 
      order_date: dayjs(),
      status: 'pending'
    })
    generateOrderNumber()
    setOrderItems([])
    setModalVisible(true)
  }

  const addOrderItem = () => {
    const newItem: SalesOrderItem = {
      item_id: 0,
      item_name: '',
      warehouse_id: 0,
      quantity: 1,
      unit_price: 0,
      total_price: 0
    }
    setOrderItems([...orderItems, newItem])
  }

  const updateOrderItem = (index: number, field: keyof SalesOrderItem, value: any) => {
    const updatedItems = [...orderItems]
    updatedItems[index] = { ...updatedItems[index], [field]: value }

    if (field === 'item_id') {
      const selectedItem = items.find(item => item.id === value)
      if (selectedItem) {
        updatedItems[index].item_name = selectedItem.name
        updatedItems[index].unit_price = selectedItem.sale_price
        updatedItems[index].total_price = updatedItems[index].quantity * selectedItem.sale_price
      }
    }

    if (field === 'quantity' || field === 'unit_price') {
      updatedItems[index].total_price = updatedItems[index].quantity * updatedItems[index].unit_price
    }

    setOrderItems(updatedItems)
  }

  const removeOrderItem = (index: number) => {
    const updatedItems = orderItems.filter((_, i) => i !== index)
    setOrderItems(updatedItems)
  }

  const filteredOrders = orders.filter(order =>
    order.order_number.toLowerCase().includes(searchText.toLowerCase()) ||
    order.customer_name.toLowerCase().includes(searchText.toLowerCase())
  )

  const stats = {
    totalOrders: orders.length,
    pendingOrders: orders.filter(o => o.status === 'pending').length,
    confirmedOrders: orders.filter(o => o.status === 'confirmed').length,
    totalAmount: orders.reduce((sum, o) => sum + o.total_amount, 0)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'confirmed': return 'blue'
      case 'shipped': return 'purple'
      case 'delivered': return 'green'
      case 'cancelled': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتّار'
      case 'confirmed': return 'مؤكد'
      case 'shipped': return 'تم الشحن'
      case 'delivered': return 'تم التسليم'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  // دالة تصدير Excel محسنة ومصححة
  const handleExportExcel = async () => {
    try {
      if (orders.length === 0) {
        messageApi.warning('لا توجد أوامر مبيعات للتصدير')
        return
      }

      // تحضير البيانات للتصدير مع تنظيف وتنسيق صحيح
      const exportData = orders.map(order => ({
        'Order_Number': order.order_number || '',
        'Customer_Name': order.customer_name || '',
        'Order_Date': order.order_date ? new Date(order.order_date).toISOString().split('T')[0] : '',
        'Expected_Date': order.expected_date ? new Date(order.expected_date).toISOString().split('T')[0] : '',
        'Status': order.status || '',
        'Total_Amount': order.total_amount || 0,
        'Notes': order.notes || '',
        'Created_By': (order as any).created_by_name || order.created_by || '',
        'Creation_Date': order.created_at ? new Date(order.created_at).toISOString().split('T')[0] : ''
      }))

      // إنشاء workbook جديد مع إعدادات محسنة
      const workbook = XLSX.utils.book_new()

      // تعيين خصائص الـ workbook
      workbook.Props = {
        Title: 'Sales Orders Report',
        Subject: 'Sales Order Management System Export',
        Author: 'ZET.IA Accounting System',
        CreatedDate: new Date()
      }

      // إنشاء ورقة العمل مع تنسيق محسن
      const worksheet = XLSX.utils.json_to_sheet(exportData, {
        header: [
          'Order_Number',
          'Customer_Name',
          'Order_Date',
          'Expected_Date',
          'Status',
          'Total_Amount',
          'Notes',
          'Created_By',
          'Creation_Date'
        ]
      })

      // تعيين عرض الأعمدة
      worksheet['!cols'] = [
        { wch: 15 }, // Order_Number
        { wch: 25 }, // Customer_Name
        { wch: 12 }, // Order_Date
        { wch: 12 }, // Expected_Date
        { wch: 12 }, // Status
        { wch: 15 }, // Total_Amount
        { wch: 30 }, // Notes
        { wch: 20 }, // Created_By
        { wch: 15 }  // Creation_Date
      ]

      // إضافة الورقة إلى workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sales_Orders')

      // إضافة ورقة معلومات التقرير بتنسيق محسن
      const reportData = [
        ['Sales Orders Report Summary'],
        [''],
        ['Report Date', new Date().toISOString().split('T')[0]],
        ['Total Orders', orders.length],
        ['Pending Orders', orders.filter(o => o.status === 'pending').length],
        ['Confirmed Orders', orders.filter(o => o.status === 'confirmed').length],
        ['Delivered Orders', orders.filter(o => o.status === 'delivered').length],
        ['Total Orders Value', orders.reduce((sum, o) => sum + (o.total_amount || 0), 0)]
      ]

      const summaryWorksheet = XLSX.utils.aoa_to_sheet(reportData)
      summaryWorksheet['!cols'] = [{ wch: 25 }, { wch: 20 }]
      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'Summary')

      // تحديد اسم الملف بتنسيق آمن
      const timestamp = new Date().toISOString().split('T')[0]
      const fileName = `Sales_Orders_Report_${timestamp}.xlsx`

      // استخدام Electron API لحفظ الملف
      if (window.electronAPI && window.electronAPI.saveExcelFile) {
        // تحويل workbook إلى buffer مع إعدادات محسنة
        const buffer = XLSX.write(workbook, {
          type: 'buffer',
          bookType: 'xlsx',
          compression: true,
          Props: {
            Title: 'Sales Orders Report',
            Subject: 'Export from ZET.IA System'
          }
        })

        const result = await window.electronAPI.saveExcelFile(buffer, fileName)

        if (result.success) {
          messageApi.success(`تم تصدير ${orders.length} أمر مبيعات بنجاح إلى ملف Excel`)
        } else {
          messageApi.error(result.message || 'فشل في حفظ الملف')
        }
      } else {
        // fallback للمتصفح العادي
        XLSX.writeFile(workbook, fileName, {
          compression: true,
          Props: {
            Title: 'Sales Orders Report',
            Subject: 'Export from ZET.IA System'
          }
        })
        messageApi.success(`تم تصدير ${orders.length} أمر مبيعات بنجاح إلى ملف Excel`)
      }
    } catch (error) {
      Logger.error('SalesOrderManagement', 'خطأ في تصدير Excel:', error)
      messageApi.error('حدث خطأ أثناء تصدير البيانات: ' + (error as Error).message)
    }
  }

  const columns = [
    {
      title: 'رقم الأمر',
      dataIndex: 'order_number',
      key: 'order_number',
      width: 120,
      render: (orderNumber: string) => <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{orderNumber}</span>
    },
    {
      title: 'العميل',
      dataIndex: 'customer_name',
      key: 'customer_name',
      render: (name: string) => (
        <Space>
          <UserOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontWeight: 'bold' }}>{name}</span>
        </Space>
      )
    },
    {
      title: 'تاريخ الأمر',
      dataIndex: 'order_date',
      key: 'order_date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'التاريخ المتوقع',
      dataIndex: 'expected_date',
      key: 'expected_date',
      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : '-'
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'المبلغ الإجمالي',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (amount: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ₪{amount.toLocaleString()}
        </span>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 200,
      render: (_, record: SalesOrder) => (
        <Space>
          <UnifiedPrintButton
            data={{
              title: `أمر بيع رقم ${record.order_number}`,
              subtitle: `العميل: ${record.customer_name}`,
              number: record.order_number,
              date: record.order_date,
              customer: {
                name: record.customer_name,
                address: '',
                phone: '',
                email: ''
              },
              items: [], // سيتم تحميلها عند الطباعة
              total: record.total_amount,
              notes: record.notes,
              metadata: {
                generatedAt: new Date().toISOString(),
                generatedBy: 'نظام المبيعات',
                orderNumber: record.order_number,
                status: record.status,
                expectedDate: record.expected_date
              }
            }}
            type="order"
            subType="sales"
            buttonText="طباعة"
            size="small"
            showDropdown={true}
            _documentId={`sales_order_${record.id}`}
            onAfterPrint={() => messageApi.success('تم طباعة أمر البيع بنجاح')}
            onError={(error) => messageApi.error(`فشل في الطباعة: ${error}`)}
          />
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Popconfirm
            title="هل أنت متأكد من حذف هذا الأمر؟"
            onConfirm={() => handleDelete(record.id)}
            okText="نعم"
            cancelText="لا"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h1 style={{ margin: 0, color: '#1890ff', fontSize: '28px' }}>
            <ShopOutlined style={{ marginLeft: '12px' }} />
            إدارة أوامر البيع
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '16px' }}>
            إدارة شاملة لأوامر البيع ومتابعة حالتها
          </p>
        </div>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
          size="large"
        >
          رجوع
        </Button>
      </div>

      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الأوامر"
              value={stats.totalOrders}
              valueStyle={{ color: '#1890ff' }}
              prefix={<ShopOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="أوامر في الانتّار"
              value={stats.pendingOrders}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="أوامر مؤكدة"
              value={stats.confirmedOrders}
              valueStyle={{ color: '#52c41a' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي المبيعات"
              value={stats.totalAmount}
              valueStyle={{ color: '#722ed1' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              size="large"
            >
              أمر بيع جديد
            </Button>
            <Button
              type="default"
              icon={<DownloadOutlined />}
              onClick={handleExportExcel}
              size="large"
            >
              تصدير Excel
            </Button>
            <UnifiedPrintButton
              data={{
                title: 'تقرير أوامر البيع',
                subtitle: `إجمالي الأوامر: ${orders.length}`,
                items: orders.map(order => ({
                  id: order.id,
                  name: `أمر رقم ${order.order_number}`,
                  description: `العميل: ${order.customer_name}`,
                  quantity: 1,
                  unit: 'أمر',
                  unitPrice: order.total_amount,
                  total: order.total_amount
                })),
                total: stats.totalAmount,
                notes: `تقرير شامل لأوامر البيع - تم إنشاؤه في ${new Date().toLocaleDateString('ar-SA')}`,
                metadata: {
                  generatedAt: new Date().toISOString(),
                  generatedBy: 'نظام المبيعات',
                  reportType: 'أوامر البيع',
                  totalOrders: stats.totalOrders,
                  pendingOrders: stats.pendingOrders,
                  confirmedOrders: stats.confirmedOrders,
                  totalAmount: stats.totalAmount
                }
              }}
              type="report"
              subType="sales"
              buttonText="طباعة التقرير"
              size="large"
              buttonType="default"
              icon={<PrinterOutlined />}
              _documentId="sales_orders_report"
              onAfterPrint={() => messageApi.success('تم طباعة تقرير أوامر البيع بنجاح')}
              onError={(error) => messageApi.error(`فشل في طباعة التقرير: ${error}`)}
            />

            <Input
              placeholder="البحث في الأوامر..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 300 }}
            />
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredOrders}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredOrders.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} من ${total} أمر`
          }}
          scroll={{ x: 1400 }}
          locale={{
            emptyText: filteredOrders.length === 0 ? 'لا توجد أوامر بيع' : 'لا توجد بيانات'
          }}
        />
      </Card>

      <Modal
        title={editingOrder ? 'تعديل أمر البيع' : 'أمر بيع جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
          setEditingOrder(null)
          setOrderItems([])
        }}
        footer={null}
        width={1000}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="order_number"
                label="رقم الأمر"
                rules={[{ required: true, message: 'يرجى إدخال رقم الأمر' }]}
              >
                <Input
                  placeholder="رقم الأمر"
                  addonAfter={
                    <Button
                      type="link"
                      size="small"
                      onClick={generateOrderNumber}
                      style={{ padding: 0 }}
                    >
                      توليد
                    </Button>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="customer_id"
                label="العميل"
                rules={[{ required: true, message: 'يرجى اختيار العميل' }]}
              >
                <Select placeholder="اختر العميل" showSearch>
                  {customers.map(customer => (
                    <Option key={customer.id} value={customer.id}>
                      {customer.name} ({customer.code})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="status"
                label="الحالة"
                rules={[{ required: true, message: 'يرجى اختيار الحالة' }]}
              >
                <Select placeholder="اختر الحالة">
                  <Option value="pending">في الانتّار</Option>
                  <Option value="confirmed">مؤكد</Option>
                  <Option value="shipped">تم الشحن</Option>
                  <Option value="delivered">تم التسليم</Option>
                  <Option value="cancelled">ملغي</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="order_date"
                label="تاريخ الأمر"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الأمر' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="expected_date"
                label="التاريخ المتوقع للتسليم"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          {/* جدول الأصناف */}
          <div style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
              <h4>أصناف الأمر</h4>
              <Button type="dashed" onClick={addOrderItem} icon={<PlusOutlined />}>
                إضافة صنف
              </Button>
            </div>

            <Table
              dataSource={orderItems}
              pagination={false}
              size="small"
              columns={[
                {
                  title: 'الصنف',
                  key: 'item',
                  render: (_, record, index) => (
                    <Select
                      placeholder="اختر الصنف"
                      style={{ width: '100%' }}
                      value={record.item_id || undefined}
                      onChange={(value) => {
                        const selectedItem = items.find(item => item.id === value)
                        updateOrderItem(index, 'item_id', value)
                        if (selectedItem) {
                          updateOrderItem(index, 'item_name', selectedItem.name)
                          updateOrderItem(index, 'unit_price', selectedItem.sale_price || 0)
                        }
                      }}
                    >
                      {items.map(item => (
                        <Select.Option key={item.id} value={item.id}>
                          {item.name} ({item.code})
                        </Select.Option>
                      ))}
                    </Select>
                  )
                },
                {
                  title: 'المخزن',
                  key: 'warehouse',
                  width: 150,
                  render: (_, record, index) => (
                    <Select
                      placeholder="اختر المخزن"
                      style={{ width: '100%' }}
                      value={record.warehouse_id || undefined}
                      onChange={(value) => updateOrderItem(index, 'warehouse_id', value)}
                    >
                      {warehouses.map(warehouse => (
                        <Select.Option key={warehouse.id} value={warehouse.id}>
                          {warehouse.name} ({warehouse.code})
                        </Select.Option>
                      ))}
                    </Select>
                  )
                },
                {
                  title: 'الكمية',
                  key: 'quantity',
                  width: 100,
                  render: (_, record, index) => (
                    <InputNumber
                      min={0}
                      value={record.quantity}
                      onChange={(value) => updateOrderItem(index, 'quantity', value || 0)}
                      style={{ width: '100%' }}
                    />
                  )
                },
                {
                  title: 'سعر الوحدة (₪)',
                  key: 'unit_price',
                  width: 120,
                  render: (_, record, index) => (
                    <InputNumber
                      min={0}
                      value={record.unit_price}
                      onChange={(value) => updateOrderItem(index, 'unit_price', value || 0)}
                      style={{ width: '100%' }}
                    />
                  )
                },
                {
                  title: 'الإجمالي (₪)',
                  key: 'total_price',
                  width: 120,
                  render: (_, record) => record.total_price.toLocaleString()
                },
                {
                  title: 'إجراءات',
                  key: 'actions',
                  width: 80,
                  render: (_, record, index) => (
                    <Button
                      type="link"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removeOrderItem(index)}
                      size="small"
                    />
                  )
                }
              ]}
            />
          </div>

          <Form.Item
            name="notes"
            label="ملاحّات"
          >
            <TextArea rows={3} placeholder="ملاحّات إضافية..." />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

const SalesOrderManagementWithApp: React.FC<SalesOrderManagementProps> = (props) => {
  return (
    <App>
      <SalesOrderManagement {...props} />
    </App>
  )
}

export default SalesOrderManagementWithApp
