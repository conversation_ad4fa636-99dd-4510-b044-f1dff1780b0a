// نصوص المساعدة لجميع أقسام التطبيق

export const PaintHelpTexts = {
  customer: "اختر العميل الذي سيتم دهان أثاثه. يمكنك البحث بالاسم أو الكود",
  paintType: "اختر نوع الدهان المناسب للقطعة. السعر محسوب لكل متر مربع",
  itemDescription: "أدخل وصف القطعة المراد دهانها (مثال: خزانة، طاولة، كرسي)",
  length: "أدخل الطول بالسنتيمتر (مثال: 150 سم)",
  width: "أدخل العرض بالسنتيمتر (مثال: 80 سم)",
  quantity: "عدد القطع المراد دهانها",
  expectedDate: "حدد التاريخ المتوقع لإنجاز أعمال الدهان",
  notes: "أضف أي ملاحظات خاصة أو تعليمات للإنتاج",
  area: "المساحة الإجمالية محسوبة تلقائياً: الطول × العرض × العدد",
  calculation: {
    title: "تفاصيل الحساب",
    content: `
      <p><strong>طريقة الحساب:</strong></p>
      <p>1. المساحة بالسنتيمتر المربع = الطول × العرض × العدد</p>
      <p>2. المساحة بالمتر المربع = المساحة بالسم² ÷ 10000</p>
      <p>3. التكلفة = المساحة بالم² × سعر المتر المربع</p>
    `
  }
}

export const ProductionHelpTexts = {
  item: "اختر الصنف المراد إنتاجه. يمكنك البحث بالاسم أو الكود",
  department: "اختر القسم المسؤول عن إنتاج هذا الصنف",
  customer: "اختر العميل إذا كان الإنتاج لعميل محدد، أو اتركه فارغاً للإنتاج الداخلي",
  orderDate: "تاريخ إنشاء أمر الإنتاج",
  quantity: "أدخل الكمية المطلوب إنتاجها",
  unit: "حدد وحدة القياس (قطعة، متر، كيلو، إلخ)",
  priority: {
    title: "مستويات الأولوية",
    content: `
      <p><strong>🟢 منخفضة:</strong> إنتاج عادي بدون استعجال</p>
      <p><strong>🔵 عادية:</strong> إنتاج ضمن الجدولة العادية</p>
      <p><strong>🟠 عالية:</strong> إنتاج مستعجل</p>
      <p><strong>🔴 عاجلة:</strong> إنتاج فوري - أولوية قصوى</p>
    `
  },
  estimatedCost: "التكلفة المتوقعة لإنتاج هذا الأمر (شاملة المواد والعمالة)",
  estimatedHours: "عدد ساعات العمل المتوقعة لإنجاز هذا الأمر",
  expectedDate: "التاريخ المتوقع لإنجاز أمر الإنتاج",
  notes: "أضف أي ملاحظات أو تعليمات خاصة لفريق الإنتاج"
}

export const InventoryHelpTexts = {
  itemCode: "كود الصنف الفريد. سيتم إنشاؤه تلقائياً إذا تُرك فارغاً",
  itemName: "اسم الصنف الواضح والمفهوم",
  category: "فئة الصنف لتسهيل التصنيف والبحث",
  unit: "وحدة قياس الصنف (قطعة، متر، كيلو، إلخ)",
  costPrice: "سعر شراء أو تكلفة الصنف",
  salePrice: "سعر بيع الصنف للعملاء",
  reorderPoint: "الحد الأدنى للمخزون - سيتم التنبيه عند الوصول إليه",
  maxStock: "الحد الأقصى للمخزون المسموح",
  warehouse: "المخزن الذي يحتوي على الصنف"
}

export const SalesHelpTexts = {
  customer: "اختر العميل من القائمة أو أضف عميل جديد",
  item: "اختر الصنف المراد بيعه",
  quantity: "الكمية المطلوب بيعها",
  price: "سعر البيع للوحدة الواحدة",
  discount: "نسبة أو مبلغ الخصم المطبق",
  tax: "نسبة الضريبة المضافة",
  paymentTerms: "شروط الدفع (نقداً، آجل، إلخ)",
  deliveryDate: "تاريخ التسليم المتوقع"
}

export const PurchaseHelpTexts = {
  supplier: "اختر المورد من القائمة أو أضف مورد جديد",
  item: "اختر الصنف المراد شراؤه",
  quantity: "الكمية المطلوب شراؤها",
  price: "سعر الشراء للوحدة الواحدة",
  discount: "نسبة أو مبلغ الخصم المحصل عليه",
  tax: "نسبة الضريبة المضافة",
  paymentTerms: "شروط الدفع مع المورد",
  deliveryDate: "تاريخ التسليم المتوقع من المورد"
}

export const CommonHelpTexts = {
  code: "الكود الفريد للعنصر. سيتم إنشاؤه تلقائياً إذا تُرك فارغاً",
  name: "الاسم الواضح والمفهوم",
  description: "وصف تفصيلي للعنصر",
  status: "حالة العنصر الحالية",
  date: "التاريخ المحدد",
  amount: "المبلغ بالعملة المحلية",
  percentage: "النسبة المئوية",
  phone: "رقم الهاتف مع رمز البلد",
  email: "عنوان البريد الإلكتروني",
  address: "العنوان الكامل",
  notes: "ملاحظات إضافية أو تعليمات خاصة"
}
