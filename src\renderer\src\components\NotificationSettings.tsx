import React, { useState, useEffect, useCallback } from 'react'
import {
  Modal, Form, Switch, Select, InputNumber, Space, Button,
  Card, Row, Col, Typography, Alert, message, Tabs, List, Tag
} from 'antd'
import { SafeLogger as Logger } from '../utils/logger'
import {
  BellOutlined, SoundOutlined, ClockCircleOutlined, SettingOutlined,
  NotificationOutlined, WarningOutlined, InfoCircleOutlined,
  CheckCircleOutlined, CloseCircleOutlined, ThunderboltOutlined
} from '@ant-design/icons'
import styled from 'styled-components'

const { Text } = Typography
const { TabPane } = Tabs

const StyledModal = styled(Modal)`
  .ant-modal-header {
    background: linear-gradient(135deg, #0078D4 0%, #106EBE 100%);
    border-bottom: none;
    
    .ant-modal-title {
      color: white;
      font-weight: bold;
    }
  }
  
  .ant-modal-body {
    padding: 24px;
  }
`

const SettingCard = styled(Card)`
  margin-bottom: 16px;
  border-radius: 8px;
  
  .ant-card-head {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }
  
  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .setting-label {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .setting-description {
      color: #666;
      font-size: 12px;
      margin-top: 4px;
    }
  }
`

interface NotificationSettingsProps {
  visible: boolean
  onClose: () => void
  currentUser: any
}

interface NotificationPreferences {
  enabled: boolean
  sound: boolean
  duration: number
  categories: {
    [key: string]: {
      enabled: boolean
      sound: boolean
      priority: 'low' | 'medium' | 'high'
    }
  }
  smartNotifications: {
    lowStock: boolean
    dueInvoices: boolean
    backupReminders: boolean
    securityAlerts: boolean
    systemUpdates: boolean
  }
  schedule: {
    enabled: boolean
    startTime: string
    endTime: string
    weekends: boolean
  }
}

const defaultPreferences: NotificationPreferences = {
  enabled: true,
  sound: true,
  duration: 4500,
  categories: {
    general: { enabled: true, sound: true, priority: 'medium' },
    inventory: { enabled: true, sound: true, priority: 'high' },
    sales: { enabled: true, sound: false, priority: 'medium' },
    purchases: { enabled: true, sound: false, priority: 'medium' },
    finance: { enabled: true, sound: true, priority: 'high' },
    production: { enabled: true, sound: false, priority: 'low' },
    employees: { enabled: true, sound: false, priority: 'low' }
  },
  smartNotifications: {
    lowStock: true,
    dueInvoices: true,
    backupReminders: true,
    securityAlerts: true,
    systemUpdates: true
  },
  schedule: {
    enabled: false,
    startTime: '08:00',
    endTime: '18:00',
    weekends: false
  }
}

const NotificationSettings: React.FC<NotificationSettingsProps> = ({
  visible,
  onClose,
  currentUser
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [preferences, setPreferences] = useState<NotificationPreferences>(defaultPreferences)

  const loadPreferences = useCallback(async () => {
    setLoading(true)
    try {
      // تحميل الإعدادات من localStorage أو قاعدة البيانات
      const savedPrefs = localStorage.getItem('notification-prefs-' + currentUser.id)
      if (savedPrefs) {
        const parsed = JSON.parse(savedPrefs)
        setPreferences({ ...defaultPreferences, ...parsed })
        form.setFieldsValue(parsed)
      } else {
        form.setFieldsValue(defaultPreferences)
      }
    } catch (error) {
      Logger.error('NotificationSettings', 'خطأ في تحميل إعدادات الإشعارات:', error)
      message.error('فشل في تحميل الإعدادات')
    } finally {
      setLoading(false)
    }
  }, [currentUser.id, form])

  useEffect(() => {
    if (visible) {
      loadPreferences()
    }
  }, [visible, loadPreferences])

  const handleSave = async (values: any) => {
    setLoading(true)
    try {
      const newPreferences = { ...preferences, ...values }
      
      // حفظ في localStorage
      localStorage.setItem('notification-prefs-' + currentUser.id, JSON.stringify(newPreferences))
      
      // تحديث الحالة
      setPreferences(newPreferences)
      
      // تحديث خدمة الإشعارات الذكية
      if ((window as any).smartNotificationService) {
        (window as any).smartNotificationService.updateSettings(newPreferences)
      }
      
      message.success('تم حفظ إعدادات الإشعارات بنجاح')
      onClose()
    } catch (error) {
      Logger.error('NotificationSettings', 'خطأ في حفظ إعدادات الإشعارات:', error)
      message.error('فشل في حفظ الإعدادات')
    } finally {
      setLoading(false)
    }
  }

  const getCategoryIcon = (category: string) => {
    const icons: { [key: string]: React.ReactNode } = {
      general: <InfoCircleOutlined />,
      inventory: <WarningOutlined />,
      sales: <CheckCircleOutlined />,
      purchases: <NotificationOutlined />,
      finance: <BellOutlined />,
      production: <SettingOutlined />,
      employees: <CloseCircleOutlined />
    }
    return icons[category] || <BellOutlined />
  }

  const getCategoryName = (category: string) => {
    const names: { [key: string]: string } = {
      general: 'عام',
      inventory: 'المخزون',
      sales: 'المبيعات',
      purchases: 'المشتريات',
      finance: 'المالية',
      production: 'الإنتاج',
      employees: 'الموظفين'
    }
    return names[category] || category
  }

  // const getPriorityColor = (priority: string) => {
  //   const colors: { [key: string]: string } = {
  //     low: '#52c41a',
  //     medium: '#faad14',
  //     high: '#ff4d4f'
  //   }
  //   return colors[priority] || '#1890ff'
  // }

  // const getPriorityName = (priority: string) => {
  //   const names: { [key: string]: string } = {
  //     low: 'منخفضة',
  //     medium: 'متوسطة',
  //     high: 'عالية'
  //   }
  //   return names[priority] || priority
  // }

  return (
    <StyledModal
      title={
        <Space>
          <SettingOutlined />
          إعدادات الإشعارات الشخصية
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={onClose}>
          إلغاء
        </Button>,
        <Button
          key="save"
          type="primary"
          loading={loading}
          onClick={() => form.submit()}
        >
          حفظ الإعدادات
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        initialValues={preferences}
      >
        <Tabs defaultActiveKey="general">
          <TabPane
            tab={
              <Space>
                <BellOutlined />
                الإعدادات العامة
              </Space>
            }
            key="general"
          >
            <SettingCard title="الإعدادات الأساسية">
              <div className="setting-item">
                <div className="setting-label">
                  <BellOutlined />
                  <div>
                    <div>تفعيل الإشعارات</div>
                    <div className="setting-description">
                      تفعيل أو إلغاء تفعيل جميع الإشعارات
                    </div>
                  </div>
                </div>
                <Form.Item name="enabled" valuePropName="checked" style={{ margin: 0 }}>
                  <Switch />
                </Form.Item>
              </div>

              <div className="setting-item">
                <div className="setting-label">
                  <SoundOutlined />
                  <div>
                    <div>تفعيل الصوت</div>
                    <div className="setting-description">
                      تشغيل صوت عند وصول إشعار جديد
                    </div>
                  </div>
                </div>
                <Form.Item name="sound" valuePropName="checked" style={{ margin: 0 }}>
                  <Switch />
                </Form.Item>
              </div>

              <div className="setting-item">
                <div className="setting-label">
                  <ClockCircleOutlined />
                  <div>
                    <div>مدة العرض</div>
                    <div className="setting-description">
                      مدة عرض الإشعار بالميلي ثانية
                    </div>
                  </div>
                </div>
                <Form.Item name="duration" style={{ margin: 0 }}>
                  <InputNumber
                    min={1000}
                    max={10000}
                    step={500}
                    style={{ width: 120 }}
                  />
                </Form.Item>
              </div>
            </SettingCard>
          </TabPane>

          <TabPane
            tab={
              <Space>
                <NotificationOutlined />
                فئات الإشعارات
              </Space>
            }
            key="categories"
          >
            <Alert
              message="تخصيص إعدادات الإشعارات حسب الفئة"
              description="يمكنك تفعيل أو إلغاء تفعيل الإشعارات لكل فئة على حدة وتحديد أولويتها"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <List
              dataSource={Object.entries(preferences.categories)}
              renderItem={([category, _settings]) => (
                <List.Item>
                  <Card style={{ width: '100%' }}>
                    <Row gutter={16} align="middle">
                      <Col span={6}>
                        <Space>
                          {getCategoryIcon(category)}
                          <Text strong>{getCategoryName(category)}</Text>
                        </Space>
                      </Col>
                      <Col span={4}>
                        <Form.Item
                          name={['categories', category, 'enabled']}
                          valuePropName="checked"
                          style={{ margin: 0 }}
                        >
                          <Switch size="small" />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item
                          name={['categories', category, 'sound']}
                          valuePropName="checked"
                          style={{ margin: 0 }}
                        >
                          <Switch size="small" />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          name={['categories', category, 'priority']}
                          style={{ margin: 0 }}
                        >
                          <Select size="small" style={{ width: '100%' }}>
                            <Select.Option value="low">
                              <Tag color="green">منخفضة</Tag>
                            </Select.Option>
                            <Select.Option value="medium">
                              <Tag color="orange">متوسطة</Tag>
                            </Select.Option>
                            <Select.Option value="high">
                              <Tag color="red">عالية</Tag>
                            </Select.Option>
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                </List.Item>
              )}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <ThunderboltOutlined />
                الإشعارات الذكية
              </Space>
            }
            key="smart"
          >
            <SettingCard title="الإشعارات التلقائية">
              <div className="setting-item">
                <div className="setting-label">
                  <WarningOutlined />
                  <div>
                    <div>تنبيهات المخزون المنخفض</div>
                    <div className="setting-description">
                      إشعارات عند انخفاض كمية الأصناف عن الحد الأدنى
                    </div>
                  </div>
                </div>
                <Form.Item
                  name={['smartNotifications', 'lowStock']}
                  valuePropName="checked"
                  style={{ margin: 0 }}
                >
                  <Switch />
                </Form.Item>
              </div>

              <div className="setting-item">
                <div className="setting-label">
                  <ClockCircleOutlined />
                  <div>
                    <div>تذكير الفواتير المستحقة</div>
                    <div className="setting-description">
                      إشعارات قبل استحقاق الفواتير بأيام محددة
                    </div>
                  </div>
                </div>
                <Form.Item
                  name={['smartNotifications', 'dueInvoices']}
                  valuePropName="checked"
                  style={{ margin: 0 }}
                >
                  <Switch />
                </Form.Item>
              </div>

              <div className="setting-item">
                <div className="setting-label">
                  <SettingOutlined />
                  <div>
                    <div>تذكير النسخ الاحتياطي</div>
                    <div className="setting-description">
                      إشعارات عند الحاجة لإنشاء نسخة احتياطية
                    </div>
                  </div>
                </div>
                <Form.Item
                  name={['smartNotifications', 'backupReminders']}
                  valuePropName="checked"
                  style={{ margin: 0 }}
                >
                  <Switch />
                </Form.Item>
              </div>

              <div className="setting-item">
                <div className="setting-label">
                  <CloseCircleOutlined />
                  <div>
                    <div>تنبيهات الأمان</div>
                    <div className="setting-description">
                      إشعارات عند رصد أنشطة مشبوهة أو مشاكل أمنية
                    </div>
                  </div>
                </div>
                <Form.Item
                  name={['smartNotifications', 'securityAlerts']}
                  valuePropName="checked"
                  style={{ margin: 0 }}
                >
                  <Switch />
                </Form.Item>
              </div>

              <div className="setting-item">
                <div className="setting-label">
                  <InfoCircleOutlined />
                  <div>
                    <div>تحديثات النظام</div>
                    <div className="setting-description">
                      إشعارات عند توفر تحديثات جديدة للنظام
                    </div>
                  </div>
                </div>
                <Form.Item
                  name={['smartNotifications', 'systemUpdates']}
                  valuePropName="checked"
                  style={{ margin: 0 }}
                >
                  <Switch />
                </Form.Item>
              </div>
            </SettingCard>
          </TabPane>
        </Tabs>
      </Form>
    </StyledModal>
  )
}

export default NotificationSettings
