import React, { useState } from 'react'
import { Tabs, Card, Space } from 'antd'
import {
  ClockCircleOutlined,
  CalendarOutlined,
  BarChartOutlined,
  TeamOutlined,
  AlertOutlined
} from '@ant-design/icons'
import UniversalReport from './UniversalReport'
import type { ReportData, ReportType } from '../../types/reports'

const EmployeeAttendanceReport: React.FC = () => {
  const [activeTab, setActiveTab] = useState('attendance');
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير الحضور والانصراف...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getEmployeeAttendance(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const attendanceData = response.data;

      // إعداد الأعمدة
      const columns = [
        {
          key: 'employee_name',
          title: 'اسم الموظف',
          align: 'right' as const,
          width: '200px'
        },
        {
          key: 'department',
          title: 'القسم',
          align: 'center' as const,
          width: '150px'
        },
        {
          key: 'attendance_date',
          title: 'التاريخ',
          align: 'center' as const,
          format: 'date' as const,
          width: '120px'
        },
        {
          key: 'check_in_time',
          title: 'وقت الحضور',
          align: 'center' as const,
          width: '120px'
        },
        {
          key: 'check_out_time',
          title: 'وقت الانصراف',
          align: 'center' as const,
          width: '120px'
        },
        {
          key: 'total_hours',
          title: 'إجمالي الساعات',
          align: 'center' as const,
          format: 'number' as const,
          width: '120px'
        },
        {
          key: 'overtime_hours',
          title: 'ساعات إضافية',
          align: 'center' as const,
          format: 'number' as const,
          width: '120px'
        },
        {
          key: 'status',
          title: 'الحالة',
          align: 'center' as const,
          width: '100px'
        }
      ];

      // إضافة مفاتيح فريدة للبيانات
      const dataWithKeys = attendanceData.map((item: any, index: number) => ({
        key: `attendance-${index}`,
        ...item
      }));

      // حساب الإحصائيات السريعة
      const totalRecords = attendanceData.length;
      const presentDays = attendanceData.filter((item: any) => item.status === 'حاضر').length;
      const absentDays = attendanceData.filter((item: any) => item.status === 'غائب').length;
      const totalOvertimeHours = attendanceData.reduce((sum: number, item: any) => sum + (item.overtime_hours || 0), 0);

      const quickStats = [
        {
          title: 'إجمالي السجلات',
          value: totalRecords,
          format: 'number' as const,
          color: '#1890ff'
        },
        {
          title: 'أيام الحضور',
          value: presentDays,
          format: 'number' as const,
          color: '#52c41a'
        },
        {
          title: 'أيام الغياب',
          value: absentDays,
          format: 'number' as const,
          color: '#ff4d4f'
        },
        {
          title: 'إجمالي الساعات الإضافية',
          value: totalOvertimeHours,
          format: 'number' as const,
          color: '#722ed1'
        }
      ];

      console.log('✅ تم إنشاء تقرير الحضور والانصراف بنجاح');

      return {
        title: 'تقرير الحضور والانصراف',
        data: dataWithKeys,
        columns,
        summary: {
          totalRecords: dataWithKeys.length,
          quickStats
        }
      };

    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير الحضور والانصراف:', error);
      throw error;
    }
  };

  // إعدادات التقرير
  const reportConfig = {
    reportType: 'employee-attendance' as ReportType,
    title: 'تقرير الحضور والانصراف',
    generateReport,
    defaultFilters: {
      dateRange: null, // كل المدة افتراضياً
    },
    enabledFilters: {
      showDateFilter: true,
      showStatusFilter: true,
      showAdvancedSearch: true,
    },
    features: {
      enableExport: true,
      enablePrint: true,
      enableRefresh: true,
      showQuickStats: true,
    }
  };

  return (
    <Card>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'attendance',
            label: (
              <Space>
                <ClockCircleOutlined />
                الحضور والانصراف
              </Space>
            ),
            children: <UniversalReport {...reportConfig} />
          },
          {
            key: 'monthly',
            label: (
              <Space>
                <CalendarOutlined />
                التقرير الشهري
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <CalendarOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                    <h3>التقرير الشهري للحضور</h3>
                    <p>ملخص شهري لحضور وانصراف الموظفين</p>
                  </div>
                </Space>
              </Card>
            )
          },
          {
            key: 'departments',
            label: (
              <Space>
                <TeamOutlined />
                حسب الأقسام
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <TeamOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                    <h3>الحضور حسب الأقسام</h3>
                    <p>تقرير الحضور مجمع حسب الأقسام</p>
                  </div>
                </Space>
              </Card>
            )
          },
          {
            key: 'violations',
            label: (
              <Space>
                <AlertOutlined />
                المخالفات
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <AlertOutlined style={{ fontSize: '48px', color: '#ff4d4f' }} />
                    <h3>مخالفات الحضور</h3>
                    <p>التأخير والغياب والانصراف المبكر</p>
                  </div>
                </Space>
              </Card>
            )
          },
          {
            key: 'analysis',
            label: (
              <Space>
                <BarChartOutlined />
                التحليل
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <BarChartOutlined style={{ fontSize: '48px', color: '#722ed1' }} />
                    <h3>تحليل أنماط الحضور</h3>
                    <p>رسوم بيانية وتحليلات متقدمة</p>
                  </div>
                </Space>
              </Card>
            )
          }
        ]}
      />
    </Card>
  );
};

export default EmployeeAttendanceReport;