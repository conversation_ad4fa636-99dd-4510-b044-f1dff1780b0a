export interface ReportTemplate {
  type: string
  title: string
  subtitle?: string
  data: any
  options?: any
}

export class ReportTemplateGenerator {
  private static instance: ReportTemplateGenerator

  private constructor() {}

  public static getInstance(): ReportTemplateGenerator {
    if (!ReportTemplateGenerator.instance) {
      ReportTemplateGenerator.instance = new ReportTemplateGenerator()
    }
    return ReportTemplateGenerator.instance
  }

  // إنشاء قالب ميزان المراجعة
  public generateTrialBalanceTemplate(data: any[]): string {
    let totalDebit = 0
    let totalCredit = 0

    const tableRows = data.map(account => {
      const debit = account.debit_balance || 0
      const credit = account.credit_balance || 0
      totalDebit += debit
      totalCredit += credit

      return `
        <tr>
          <td>${account.account_code || ''}</td>
          <td>${account.account_name || ''}</td>
          <td class="text-info">${debit.toLocaleString('ar-EG')} ₪</td>
          <td class="text-success">${credit.toLocaleString('ar-EG')} ₪</td>
        </tr>
      `
    }).join('')

    return `
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-value text-info">${totalDebit.toLocaleString('ar-EG')} ₪</div>
          <div class="stat-label">إجمالي المدين</div>
        </div>
        <div class="stat-card">
          <div class="stat-value text-success">${totalCredit.toLocaleString('ar-EG')} ₪</div>
          <div class="stat-label">إجمالي الدائن</div>
        </div>
        <div class="stat-card">
          <div class="stat-value ${totalDebit === totalCredit ? 'text-success' : 'text-danger'}">
            ${totalDebit === totalCredit ? 'متوازن' : 'غير متوازن'}
          </div>
          <div class="stat-label">حالة الميزان</div>
        </div>
      </div>

      <table class="report-table">
        <thead>
          <tr>
            <th>رمز الحساب</th>
            <th>اسم الحساب</th>
            <th>المدين</th>
            <th>الدائن</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
        <tfoot>
          <tr style="background: #f0f9ff; font-weight: bold;">
            <td colspan="2">الإجمالي</td>
            <td class="text-info">${totalDebit.toLocaleString('ar-EG')} ₪</td>
            <td class="text-success">${totalCredit.toLocaleString('ar-EG')} ₪</td>
          </tr>
        </tfoot>
      </table>
    `
  }

  // إنشاء قالب الميزانية العمومية
  public generateBalanceSheetTemplate(data: any): string {
    const { assets = [], liabilities = [], equity = [], totalAssets = 0, totalLiabilities = 0, totalEquity = 0 } = data

    const assetsRows = assets.map((asset: any) => `
      <tr>
        <td>${asset.account_name}</td>
        <td class="text-info">${(asset.balance || 0).toLocaleString('ar-EG')} ₪</td>
      </tr>
    `).join('')

    const liabilitiesRows = liabilities.map((liability: any) => `
      <tr>
        <td>${liability.account_name}</td>
        <td class="text-warning">${(liability.balance || 0).toLocaleString('ar-EG')} ₪</td>
      </tr>
    `).join('')

    const equityRows = equity.map((eq: any) => `
      <tr>
        <td>${eq.account_name}</td>
        <td class="text-success">${(eq.balance || 0).toLocaleString('ar-EG')} ₪</td>
      </tr>
    `).join('')

    return `
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-value text-info">${totalAssets.toLocaleString('ar-EG')} ₪</div>
          <div class="stat-label">إجمالي الأصول</div>
        </div>
        <div class="stat-card">
          <div class="stat-value text-warning">${totalLiabilities.toLocaleString('ar-EG')} ₪</div>
          <div class="stat-label">إجمالي الخصوم</div>
        </div>
        <div class="stat-card">
          <div class="stat-value text-success">${totalEquity.toLocaleString('ar-EG')} ₪</div>
          <div class="stat-label">حقوق الملكية</div>
        </div>
      </div>

      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
        <div>
          <h3 style="color: #1890ff; margin-bottom: 15px; text-align: center;">الأصول</h3>
          <table class="report-table">
            <thead>
              <tr>
                <th>اسم الحساب</th>
                <th>المبلغ</th>
              </tr>
            </thead>
            <tbody>
              ${assetsRows}
            </tbody>
            <tfoot>
              <tr style="background: #e6f7ff; font-weight: bold;">
                <td>إجمالي الأصول</td>
                <td class="text-info">${totalAssets.toLocaleString('ar-EG')} ₪</td>
              </tr>
            </tfoot>
          </table>
        </div>

        <div>
          <h3 style="color: #fa8c16; margin-bottom: 15px; text-align: center;">الخصوم وحقوق الملكية</h3>
          <table class="report-table">
            <thead>
              <tr>
                <th>اسم الحساب</th>
                <th>المبلغ</th>
              </tr>
            </thead>
            <tbody>
              ${liabilitiesRows}
              ${equityRows}
            </tbody>
            <tfoot>
              <tr style="background: #fff7e6; font-weight: bold;">
                <td>الإجمالي</td>
                <td class="text-warning">${(totalLiabilities + totalEquity).toLocaleString('ar-EG')} ₪</td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    `
  }

  // إنشاء قالب قائمة الدخل
  public generateIncomeStatementTemplate(data: any): string {
    const { revenues = [], expenses = [], totalRevenue = 0, totalExpenses = 0, netIncome = 0 } = data

    const revenuesRows = revenues.map((revenue: any) => `
      <tr>
        <td>${revenue.account_name}</td>
        <td class="text-success">${(revenue.amount || 0).toLocaleString('ar-EG')} ₪</td>
      </tr>
    `).join('')

    const expensesRows = expenses.map((expense: any) => `
      <tr>
        <td>${expense.account_name}</td>
        <td class="text-danger">${(expense.amount || 0).toLocaleString('ar-EG')} ₪</td>
      </tr>
    `).join('')

    return `
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-value text-success">${totalRevenue.toLocaleString('ar-EG')} ₪</div>
          <div class="stat-label">إجمالي الإيرادات</div>
        </div>
        <div class="stat-card">
          <div class="stat-value text-danger">${totalExpenses.toLocaleString('ar-EG')} ₪</div>
          <div class="stat-label">إجمالي المصروفات</div>
        </div>
        <div class="stat-card">
          <div class="stat-value ${netIncome >= 0 ? 'text-success' : 'text-danger'}">
            ${netIncome.toLocaleString('ar-EG')} ₪
          </div>
          <div class="stat-label">${netIncome >= 0 ? 'صافي الربح' : 'صافي الخسارة'}</div>
        </div>
      </div>

      <table class="report-table">
        <thead>
          <tr>
            <th colspan="2" style="background: #52c41a;">الإيرادات</th>
          </tr>
          <tr>
            <th>اسم الحساب</th>
            <th>المبلغ</th>
          </tr>
        </thead>
        <tbody>
          ${revenuesRows}
        </tbody>
        <tfoot>
          <tr style="background: #f6ffed; font-weight: bold;">
            <td>إجمالي الإيرادات</td>
            <td class="text-success">${totalRevenue.toLocaleString('ar-EG')} ₪</td>
          </tr>
        </tfoot>
      </table>

      <table class="report-table" style="margin-top: 20px;">
        <thead>
          <tr>
            <th colspan="2" style="background: #ff4d4f;">المصروفات</th>
          </tr>
          <tr>
            <th>اسم الحساب</th>
            <th>المبلغ</th>
          </tr>
        </thead>
        <tbody>
          ${expensesRows}
        </tbody>
        <tfoot>
          <tr style="background: #fff2f0; font-weight: bold;">
            <td>إجمالي المصروفات</td>
            <td class="text-danger">${totalExpenses.toLocaleString('ar-EG')} ₪</td>
          </tr>
        </tfoot>
      </table>

      <div style="margin-top: 30px; text-align: center; padding: 20px; background: ${netIncome >= 0 ? '#f6ffed' : '#fff2f0'}; border-radius: 8px;">
        <h3 style="color: ${netIncome >= 0 ? '#52c41a' : '#ff4d4f'}; margin: 0;">
          ${netIncome >= 0 ? 'صافي الربح' : 'صافي الخسارة'}: ${Math.abs(netIncome).toLocaleString('ar-EG')} ₪
        </h3>
      </div>
    `
  }

  // إنشاء قالب تقرير الأعمار
  public generateAgingReportTemplate(data: any): string {
    const { checksAging = [], promissoryNotesAging = [] } = data

    const checksRows = checksAging.map((check: any) => {
      const daysOld = Math.floor(check.days_old || 0)
      const statusClass = daysOld > 30 ? 'text-danger' : daysOld > 15 ? 'text-warning' : 'text-success'
      
      return `
        <tr>
          <td>${check.check_number}</td>
          <td>${check.payee_name}</td>
          <td>${(check.amount || 0).toLocaleString('ar-EG')} ₪</td>
          <td>${new Date(check.issue_date).toLocaleDateString('ar-EG')}</td>
          <td class="${statusClass}">${daysOld} يوم</td>
          <td><span class="status-badge status-${check.status}">${this.translateStatus(check.status)}</span></td>
        </tr>
      `
    }).join('')

    const notesRows = promissoryNotesAging.map((note: any) => {
      const daysOverdue = Math.floor(note.days_overdue || 0)
      const statusClass = daysOverdue > 0 ? 'text-danger' : 'text-success'
      
      return `
        <tr>
          <td>${note.note_number}</td>
          <td>${note.payee_name}</td>
          <td>${(note.amount || 0).toLocaleString('ar-EG')} ₪</td>
          <td>${new Date(note.due_date).toLocaleDateString('ar-EG')}</td>
          <td class="${statusClass}">${daysOverdue > 0 ? `${daysOverdue} يوم متأخر` : 'في الموعد'}</td>
          <td><span class="status-badge status-${note.status}">${this.translateStatus(note.status)}</span></td>
        </tr>
      `
    }).join('')

    return `
      <style>
        .status-badge {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
        }
        .status-issued { background: #e6f7ff; color: #1890ff; }
        .status-pending { background: #fff7e6; color: #fa8c16; }
        .status-cashed { background: #f6ffed; color: #52c41a; }
        .status-cancelled { background: #fff2f0; color: #ff4d4f; }
      </style>

      <h3 style="color: #1890ff; margin-bottom: 20px;">الشيكات المعلقة</h3>
      <table class="report-table">
        <thead>
          <tr>
            <th>رقم الشيك</th>
            <th>اسم المستفيد</th>
            <th>المبلغ</th>
            <th>تاريخ الإصدار</th>
            <th>العمر</th>
            <th>الحالة</th>
          </tr>
        </thead>
        <tbody>
          ${checksRows}
        </tbody>
      </table>

      <h3 style="color: #722ed1; margin: 40px 0 20px 0;">السندات الإذنية</h3>
      <table class="report-table">
        <thead>
          <tr>
            <th>رقم السند</th>
            <th>اسم المستفيد</th>
            <th>المبلغ</th>
            <th>تاريخ الاستحقاق</th>
            <th>حالة الاستحقاق</th>
            <th>الحالة</th>
          </tr>
        </thead>
        <tbody>
          ${notesRows}
        </tbody>
      </table>
    `
  }

  // إنشاء قالب فاتورة المبيعات
  public generateSalesInvoiceTemplate(invoice: any, items: any[]): string {
    const totalAmount = items.reduce((sum, item) => sum + (item.total_price || 0), 0)
    const discount = invoice.discount || 0
    const tax = invoice.tax || 0
    const finalAmount = totalAmount - discount + tax

    const itemsRows = items.map(item => `
      <tr>
        <td>${item.item_name}</td>
        <td>${item.item_code || '-'}</td>
        <td class="text-center">${item.quantity}</td>
        <td>${item.unit || 'قطعة'}</td>
        <td class="text-info">${(item.unit_price || 0).toLocaleString('ar-EG')} ₪</td>
        <td class="text-success">${(item.total_price || 0).toLocaleString('ar-EG')} ₪</td>
      </tr>
    `).join('')

    return `
      <div class="invoice-header">
        <div class="invoice-info">
          <h2 style="color: #52c41a; margin-bottom: 10px;">فاتورة مبيعات</h2>
          <div class="invoice-details">
            <p><strong>رقم الفاتورة:</strong> ${invoice.invoice_number}</p>
            <p><strong>تاريخ الفاتورة:</strong> ${new Date(invoice.invoice_date).toLocaleDateString('ar-EG')}</p>
            <p><strong>العميل:</strong> ${invoice.customer_name}</p>
            ${invoice.due_date ? `<p><strong>تاريخ الاستحقاق:</strong> ${new Date(invoice.due_date).toLocaleDateString('ar-EG')}</p>` : ''}
          </div>
        </div>
        <div class="invoice-status">
          <span class="status-badge status-${invoice.status}">${this.translateInvoiceStatus(invoice.status)}</span>
        </div>
      </div>

      <table class="report-table">
        <thead>
          <tr>
            <th>اسم الصنف</th>
            <th>كود الصنف</th>
            <th>الكمية</th>
            <th>الوحدة</th>
            <th>سعر الوحدة</th>
            <th>الإجمالي</th>
          </tr>
        </thead>
        <tbody>
          ${itemsRows}
        </tbody>
      </table>

      <div class="invoice-summary">
        <div class="summary-row">
          <span>إجمالي الأصناف:</span>
          <span class="text-info">${totalAmount.toLocaleString('ar-EG')} ₪</span>
        </div>
        ${discount > 0 ? `
          <div class="summary-row">
            <span>الخصم:</span>
            <span class="text-warning">-${discount.toLocaleString('ar-EG')} ₪</span>
          </div>
        ` : ''}
        ${tax > 0 ? `
          <div class="summary-row">
            <span>الضريبة:</span>
            <span class="text-info">+${tax.toLocaleString('ar-EG')} ₪</span>
          </div>
        ` : ''}
        <div class="summary-row total-row">
          <span>المبلغ الإجمالي:</span>
          <span class="text-success">${finalAmount.toLocaleString('ar-EG')} ₪</span>
        </div>
        ${invoice.paid_amount > 0 ? `
          <div class="summary-row">
            <span>المبلغ المدفوع:</span>
            <span class="text-success">${invoice.paid_amount.toLocaleString('ar-EG')} ₪</span>
          </div>
          <div class="summary-row">
            <span>المبلغ المتبقي:</span>
            <span class="text-danger">${(finalAmount - invoice.paid_amount).toLocaleString('ar-EG')} ₪</span>
          </div>
        ` : ''}
      </div>

      ${invoice.notes ? `
        <div class="notes-section">
          <h4>ملاحّات:</h4>
          <p>${invoice.notes}</p>
        </div>
      ` : ''}

      <style>
        .invoice-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 30px;
          padding: 20px;
          background: #f9f9f9;
          border-radius: 8px;
        }

        .invoice-details p {
          margin: 5px 0;
          font-size: 14px;
        }

        .invoice-summary {
          margin-top: 30px;
          padding: 20px;
          background: #f0f9ff;
          border-radius: 8px;
          border-right: 4px solid #1890ff;
        }

        .summary-row {
          display: flex;
          justify-content: space-between;
          margin: 8px 0;
          font-size: 14px;
        }

        .total-row {
          border-top: 2px solid #1890ff;
          padding-top: 10px;
          margin-top: 15px;
          font-weight: bold;
          font-size: 16px;
        }

        .notes-section {
          margin-top: 30px;
          padding: 15px;
          background: #fffbe6;
          border-radius: 8px;
          border-right: 4px solid #faad14;
        }

        .status-badge {
          padding: 6px 12px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 600;
        }

        .status-pending { background: #fff7e6; color: #fa8c16; }
        .status-paid { background: #f6ffed; color: #52c41a; }
        .status-partial { background: #e6f7ff; color: #1890ff; }
        .status-overdue { background: #fff2f0; color: #ff4d4f; }
        .status-cancelled { background: #f5f5f5; color: #8c8c8c; }
      </style>
    `
  }

  // إنشاء قالب فاتورة المشتريات
  public generatePurchaseInvoiceTemplate(invoice: any, items: any[]): string {
    const totalAmount = items.reduce((sum, item) => sum + (item.total_price || 0), 0)
    const discount = invoice.discount || 0
    const tax = invoice.tax || 0
    const finalAmount = totalAmount - discount + tax

    const itemsRows = items.map(item => `
      <tr>
        <td>${item.item_name}</td>
        <td>${item.item_code || '-'}</td>
        <td class="text-center">${item.quantity}</td>
        <td>${item.unit || 'قطعة'}</td>
        <td class="text-info">${(item.unit_price || 0).toLocaleString('ar-EG')} ₪</td>
        <td class="text-success">${(item.total_price || 0).toLocaleString('ar-EG')} ₪</td>
      </tr>
    `).join('')

    return `
      <div class="invoice-header">
        <div class="invoice-info">
          <h2 style="color: #fa8c16; margin-bottom: 10px;">فاتورة مشتريات</h2>
          <div class="invoice-details">
            <p><strong>رقم الفاتورة:</strong> ${invoice.invoice_number}</p>
            <p><strong>تاريخ الفاتورة:</strong> ${new Date(invoice.invoice_date).toLocaleDateString('ar-EG')}</p>
            <p><strong>المورد:</strong> ${invoice.supplier_name}</p>
            ${invoice.due_date ? `<p><strong>تاريخ الاستحقاق:</strong> ${new Date(invoice.due_date).toLocaleDateString('ar-EG')}</p>` : ''}
          </div>
        </div>
        <div class="invoice-status">
          <span class="status-badge status-${invoice.status}">${this.translateInvoiceStatus(invoice.status)}</span>
        </div>
      </div>

      <table class="report-table">
        <thead>
          <tr>
            <th>اسم الصنف</th>
            <th>كود الصنف</th>
            <th>الكمية</th>
            <th>الوحدة</th>
            <th>سعر الوحدة</th>
            <th>الإجمالي</th>
          </tr>
        </thead>
        <tbody>
          ${itemsRows}
        </tbody>
      </table>

      <div class="invoice-summary" style="background: #fff7e6; border-right-color: #fa8c16;">
        <div class="summary-row">
          <span>إجمالي الأصناف:</span>
          <span class="text-info">${totalAmount.toLocaleString('ar-EG')} ₪</span>
        </div>
        ${discount > 0 ? `
          <div class="summary-row">
            <span>الخصم:</span>
            <span class="text-warning">-${discount.toLocaleString('ar-EG')} ₪</span>
          </div>
        ` : ''}
        ${tax > 0 ? `
          <div class="summary-row">
            <span>الضريبة:</span>
            <span class="text-info">+${tax.toLocaleString('ar-EG')} ₪</span>
          </div>
        ` : ''}
        <div class="summary-row total-row">
          <span>المبلغ الإجمالي:</span>
          <span class="text-warning">${finalAmount.toLocaleString('ar-EG')} ₪</span>
        </div>
        ${invoice.paid_amount > 0 ? `
          <div class="summary-row">
            <span>المبلغ المدفوع:</span>
            <span class="text-success">${invoice.paid_amount.toLocaleString('ar-EG')} ₪</span>
          </div>
          <div class="summary-row">
            <span>المبلغ المتبقي:</span>
            <span class="text-danger">${(finalAmount - invoice.paid_amount).toLocaleString('ar-EG')} ₪</span>
          </div>
        ` : ''}
      </div>

      ${invoice.notes ? `
        <div class="notes-section">
          <h4>ملاحّات:</h4>
          <p>${invoice.notes}</p>
        </div>
      ` : ''}
    `
  }



  // إنشاء قالب أمر الإنتاج
  public generateProductionOrderTemplate(order: any, items: any[]): string {
    const itemsRows = items.map(item => `
      <tr>
        <td>${item.item_name}</td>
        <td>${item.item_code || '-'}</td>
        <td class="text-center">${item.quantity}</td>
        <td>${item.unit || 'قطعة'}</td>
        <td>${item.specifications || '-'}</td>
        <td>${item.notes || '-'}</td>
      </tr>
    `).join('')

    return `
      <div class="production-order-header">
        <div class="order-info">
          <h2 style="color: #722ed1; margin-bottom: 10px;">أمر إنتاج</h2>
          <div class="order-details">
            <p><strong>رقم الأمر:</strong> ${order.order_number}</p>
            <p><strong>تاريخ الأمر:</strong> ${new Date(order.order_date).toLocaleDateString('ar-EG')}</p>
            <p><strong>تاريخ البدء المطلوب:</strong> ${new Date(order.start_date).toLocaleDateString('ar-EG')}</p>
            <p><strong>تاريخ الانتهاء المطلوب:</strong> ${new Date(order.end_date).toLocaleDateString('ar-EG')}</p>
            <p><strong>الأولوية:</strong> ${this.translatePriority(order.priority)}</p>
          </div>
        </div>
        <div class="order-status">
          <span class="status-badge status-${order.status}">${this.translateProductionStatus(order.status)}</span>
        </div>
      </div>

      <div class="production-info">
        <div class="info-section">
          <h4>معلومات الإنتاج</h4>
          <p><strong>قسم الإنتاج:</strong> ${order.department_name || '-'}</p>
          <p><strong>المسؤول:</strong> ${order.supervisor_name || '-'}</p>
          <p><strong>نوع الإنتاج:</strong> ${order.production_type || '-'}</p>
        </div>
      </div>

      <table class="report-table">
        <thead>
          <tr>
            <th>اسم الصنف</th>
            <th>كود الصنف</th>
            <th>الكمية المطلوبة</th>
            <th>الوحدة</th>
            <th>المواصفات</th>
            <th>ملاحّات</th>
          </tr>
        </thead>
        <tbody>
          ${itemsRows}
        </tbody>
      </table>

      ${order.notes ? `
        <div class="notes-section">
          <h4>ملاحّات الإنتاج:</h4>
          <p>${order.notes}</p>
        </div>
      ` : ''}

      <div class="instructions-section">
        <h4>تعليمات الإنتاج:</h4>
        <ul>
          <li>يجب التأكد من جودة المواد الخام قبل البدء</li>
          <li>اتباع معايير السلامة والجودة المحددة</li>
          <li>تسجيل أي ملاحّات أو مشاكل أثناء الإنتاج</li>
          <li>إبلاغ المشرف عند اكتمال كل مرحلة</li>
        </ul>
      </div>

      <div class="signatures-section">
        <div class="signature-box">
          <p>توقيع المشرف</p>
          <div class="signature-line"></div>
          <p>التاريخ: ___________</p>
        </div>
        <div class="signature-box">
          <p>توقيع مدير الإنتاج</p>
          <div class="signature-line"></div>
          <p>التاريخ: ___________</p>
        </div>
        <div class="signature-box">
          <p>توقيع مراقب الجودة</p>
          <div class="signature-line"></div>
          <p>التاريخ: ___________</p>
        </div>
      </div>

      <style>
        .production-order-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 30px;
          padding: 20px;
          background: #f9f0ff;
          border-radius: 8px;
          border-right: 4px solid #722ed1;
        }

        .order-details p {
          margin: 5px 0;
          font-size: 14px;
        }

        .production-info {
          margin-bottom: 30px;
          padding: 15px;
          background: #f0f9ff;
          border-radius: 8px;
        }

        .info-section h4 {
          color: #722ed1;
          margin-bottom: 10px;
        }

        .instructions-section {
          margin-top: 30px;
          padding: 20px;
          background: #fffbe6;
          border-radius: 8px;
          border-right: 4px solid #faad14;
        }

        .instructions-section h4 {
          color: #faad14;
          margin-bottom: 15px;
        }

        .instructions-section ul {
          margin-right: 20px;
        }

        .instructions-section li {
          margin: 8px 0;
          font-size: 14px;
        }

        .signatures-section {
          display: flex;
          justify-content: space-around;
          margin-top: 40px;
          padding: 30px 20px;
          border-top: 2px solid #722ed1;
        }

        .signature-box {
          text-align: center;
          min-width: 150px;
        }

        .signature-line {
          width: 150px;
          height: 1px;
          border-bottom: 1px solid #333;
          margin: 20px auto;
        }

        .status-draft { background: #f5f5f5; color: #8c8c8c; }
        .status-approved { background: #e6f7ff; color: #1890ff; }
        .status-in-progress { background: #fff7e6; color: #fa8c16; }
        .status-completed { background: #f6ffed; color: #52c41a; }
        .status-cancelled { background: #fff2f0; color: #ff4d4f; }
      </style>
    `
  }

  // إنشاء قالب أمر الدهان
  public generatePaintOrderTemplate(order: any, items: any[]): string {
    const totalArea = items.reduce((sum, item) => sum + (item.area || 0), 0)
    const totalAmount = items.reduce((sum, item) => sum + (item.total_price || 0), 0)

    const itemsRows = items.map(item => `
      <tr>
        <td>${item.surface_type || '-'}</td>
        <td class="text-center">${item.length || '-'}</td>
        <td class="text-center">${item.width || '-'}</td>
        <td class="text-center">${(item.area || 0).toFixed(2)}</td>
        <td>${item.paint_type_name || '-'}</td>
        <td>${item.color || '-'}</td>
        <td class="text-success">${(item.total_price || 0).toLocaleString('ar-EG')} ₪</td>
      </tr>
    `).join('')

    return `
      <div class="paint-order-header">
        <div class="order-info">
          <h2 style="color: #13c2c2; margin-bottom: 10px;">أمر دهان</h2>
          <div class="order-details">
            <p><strong>رقم الأمر:</strong> ${order.order_number}</p>
            <p><strong>تاريخ الأمر:</strong> ${new Date(order.order_date).toLocaleDateString('ar-EG')}</p>
            <p><strong>العميل:</strong> ${order.customer_name}</p>
            <p><strong>موقع العمل:</strong> ${order.work_location || '-'}</p>
            <p><strong>تاريخ البدء:</strong> ${new Date(order.start_date).toLocaleDateString('ar-EG')}</p>
          </div>
        </div>
        <div class="order-status">
          <span class="status-badge status-${order.status}">${this.translateProductionStatus(order.status)}</span>
        </div>
      </div>

      <div class="paint-summary">
        <div class="summary-card">
          <h4>إجمالي المساحة</h4>
          <p class="summary-value">${totalArea.toFixed(2)} م²</p>
        </div>
        <div class="summary-card">
          <h4>إجمالي التكلفة</h4>
          <p class="summary-value">${totalAmount.toLocaleString('ar-EG')} ₪</p>
        </div>
      </div>

      <table class="report-table">
        <thead>
          <tr>
            <th>نوع السطح</th>
            <th>الطول (م)</th>
            <th>العرض (م)</th>
            <th>المساحة (م²)</th>
            <th>نوع الدهان</th>
            <th>اللون</th>
            <th>التكلفة</th>
          </tr>
        </thead>
        <tbody>
          ${itemsRows}
        </tbody>
        <tfoot>
          <tr style="background: #e6fffb; font-weight: bold;">
            <td colspan="3">الإجمالي</td>
            <td class="text-info">${totalArea.toFixed(2)} م²</td>
            <td colspan="2"></td>
            <td class="text-success">${totalAmount.toLocaleString('ar-EG')} ₪</td>
          </tr>
        </tfoot>
      </table>

      ${order.notes ? `
        <div class="notes-section">
          <h4>ملاحّات خاصة:</h4>
          <p>${order.notes}</p>
        </div>
      ` : ''}

      <div class="paint-instructions">
        <h4>تعليمات الدهان:</h4>
        <ul>
          <li>تنّيف السطح جيداً قبل البدء</li>
          <li>استخدام البرايمر المناسب لنوع السطح</li>
          <li>تطبيق طبقتين من الدهان على الأقل</li>
          <li>التأكد من جفاف كل طبقة قبل تطبيق التالية</li>
          <li>حماية المناطق المجاورة من رذاذ الدهان</li>
        </ul>
      </div>

      <style>
        .paint-order-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 30px;
          padding: 20px;
          background: #e6fffb;
          border-radius: 8px;
          border-right: 4px solid #13c2c2;
        }

        .paint-summary {
          display: flex;
          justify-content: space-around;
          margin-bottom: 30px;
        }

        .summary-card {
          text-align: center;
          padding: 20px;
          background: #f0f9ff;
          border-radius: 8px;
          min-width: 150px;
        }

        .summary-card h4 {
          color: #13c2c2;
          margin-bottom: 10px;
        }

        .summary-value {
          font-size: 24px;
          font-weight: bold;
          color: #1890ff;
          margin: 0;
        }

        .paint-instructions {
          margin-top: 30px;
          padding: 20px;
          background: #e6fffb;
          border-radius: 8px;
          border-right: 4px solid #13c2c2;
        }

        .paint-instructions h4 {
          color: #13c2c2;
          margin-bottom: 15px;
        }

        .paint-instructions ul {
          margin-right: 20px;
        }

        .paint-instructions li {
          margin: 8px 0;
          font-size: 14px;
        }
      </style>
    `
  }

  // ترجمة حالات الفواتير
  private translateInvoiceStatus(status: string): string {
    const translations: { [key: string]: string } = {
      'pending': 'معلقة',
      'paid': 'مدفوعة',
      'partial': 'مدفوعة جزئياً',
      'overdue': 'متأخرة',
      'cancelled': 'ملغية'
    }

    return translations[status] || status
  }

  // ترجمة حالات الإنتاج
  private translateProductionStatus(status: string): string {
    const translations: { [key: string]: string } = {
      'draft': 'مسودة',
      'approved': 'معتمد',
      'in-progress': 'قيد التنفيذ',
      'completed': 'مكتمل',
      'cancelled': 'ملغي'
    }

    return translations[status] || status
  }

  // ترجمة الأولوية
  // إنشاء قالب أمر البيع
  public generateSalesOrderTemplate(order: any, items: any[]): string {
    const totalAmount = items.reduce((sum, item) => sum + (item.total_price || 0), 0)
    const discount = order.discount || 0
    const tax = order.tax || 0
    const finalAmount = totalAmount - discount + tax

    const itemsRows = items.map((item, index) => `
      <tr>
        <td class="text-center">${index + 1}</td>
        <td>${item.item_name}</td>
        <td>${item.item_code || '-'}</td>
        <td class="text-center">${item.quantity}</td>
        <td>${item.unit || 'قطعة'}</td>
        <td class="text-info">${(item.unit_price || 0).toLocaleString('ar-EG')} ₪</td>
        <td class="text-success">${(item.quantity * item.unit_price || 0).toLocaleString('ar-EG')} ₪</td>
        <td class="text-success">${(item.total_price || 0).toLocaleString('ar-EG')} ₪</td>
      </tr>
    `).join('')

    return `
      <div class="order-header">
        <h2 class="order-title">أمر بيع رقم: ${order.order_number}</h2>
        <div class="order-info">
          <div class="info-row">
            <span class="label">العميل:</span>
            <span class="value">${order.customer_name || 'غير محدد'}</span>
          </div>
          <div class="info-row">
            <span class="label">تاريخ الأمر:</span>
            <span class="value">${new Date(order.order_date).toLocaleDateString('ar-EG')}</span>
          </div>
          <div class="info-row">
            <span class="label">تاريخ التسليم المتوقع:</span>
            <span class="value">${order.delivery_date ? new Date(order.delivery_date).toLocaleDateString('ar-EG') : 'غير محدد'}</span>
          </div>
          <div class="info-row">
            <span class="label">الحالة:</span>
            <span class="value status-${order.status}">${this.translateStatus(order.status)}</span>
          </div>
        </div>
      </div>

      <div class="items-section">
        <h3>تفاصيل الأصناف</h3>
        <table class="items-table">
          <thead>
            <tr>
              <th>#</th>
              <th>اسم الصنف</th>
              <th>كود الصنف</th>
              <th>الكمية</th>
              <th>الوحدة</th>
              <th>سعر الوحدة</th>
              <th>الإجمالي الفرعي</th>
              <th>الإجمالي الكامل</th>
            </tr>
          </thead>
          <tbody>
            ${itemsRows}
          </tbody>
        </table>
      </div>

      <div class="totals-section">
        <div class="totals-grid">
          <div class="total-row">
            <span class="label">المجموع الفرعي:</span>
            <span class="value">${totalAmount.toLocaleString('ar-EG')} ₪</span>
          </div>
          <div class="total-row">
            <span class="label">الخصم:</span>
            <span class="value">-${discount.toLocaleString('ar-EG')} ₪</span>
          </div>
          <div class="total-row">
            <span class="label">الضريبة:</span>
            <span class="value">+${tax.toLocaleString('ar-EG')} ₪</span>
          </div>
          <div class="total-row final-total">
            <span class="label">المجموع النهائي:</span>
            <span class="value">${finalAmount.toLocaleString('ar-EG')} ₪</span>
          </div>
        </div>
      </div>

      ${order.notes ? `
        <div class="notes-section">
          <h3>ملاحظات</h3>
          <p>${order.notes}</p>
        </div>
      ` : ''}
    `
  }

  // إنشاء قالب أمر الشراء
  public generatePurchaseOrderTemplate(order: any, items: any[]): string {
    const totalAmount = items.reduce((sum, item) => sum + (item.total_price || 0), 0)
    const discount = order.discount || 0
    const tax = order.tax || 0
    const finalAmount = totalAmount - discount + tax

    const itemsRows = items.map((item, index) => `
      <tr>
        <td class="text-center">${index + 1}</td>
        <td>${item.item_name}</td>
        <td>${item.item_code || '-'}</td>
        <td class="text-center">${item.quantity}</td>
        <td>${item.unit || 'قطعة'}</td>
        <td class="text-info">${(item.unit_price || 0).toLocaleString('ar-EG')} ₪</td>
        <td class="text-success">${(item.quantity * item.unit_price || 0).toLocaleString('ar-EG')} ₪</td>
        <td class="text-success">${(item.total_price || 0).toLocaleString('ar-EG')} ₪</td>
      </tr>
    `).join('')

    return `
      <div class="order-header">
        <h2 class="order-title">أمر شراء رقم: ${order.order_number}</h2>
        <div class="order-info">
          <div class="info-row">
            <span class="label">المورد:</span>
            <span class="value">${order.supplier_name || 'غير محدد'}</span>
          </div>
          <div class="info-row">
            <span class="label">تاريخ الأمر:</span>
            <span class="value">${new Date(order.order_date).toLocaleDateString('ar-EG')}</span>
          </div>
          <div class="info-row">
            <span class="label">تاريخ التسليم المتوقع:</span>
            <span class="value">${order.delivery_date ? new Date(order.delivery_date).toLocaleDateString('ar-EG') : 'غير محدد'}</span>
          </div>
          <div class="info-row">
            <span class="label">الحالة:</span>
            <span class="value status-${order.status}">${this.translateStatus(order.status)}</span>
          </div>
        </div>
      </div>

      <div class="items-section">
        <h3>تفاصيل الأصناف</h3>
        <table class="items-table">
          <thead>
            <tr>
              <th>#</th>
              <th>اسم الصنف</th>
              <th>كود الصنف</th>
              <th>الكمية</th>
              <th>الوحدة</th>
              <th>سعر الوحدة</th>
              <th>الإجمالي الفرعي</th>
              <th>الإجمالي الكامل</th>
            </tr>
          </thead>
          <tbody>
            ${itemsRows}
          </tbody>
        </table>
      </div>

      <div class="totals-section">
        <div class="totals-grid">
          <div class="total-row">
            <span class="label">المجموع الفرعي:</span>
            <span class="value">${totalAmount.toLocaleString('ar-EG')} ₪</span>
          </div>
          <div class="total-row">
            <span class="label">الخصم:</span>
            <span class="value">-${discount.toLocaleString('ar-EG')} ₪</span>
          </div>
          <div class="total-row">
            <span class="label">الضريبة:</span>
            <span class="value">+${tax.toLocaleString('ar-EG')} ₪</span>
          </div>
          <div class="total-row final-total">
            <span class="label">المجموع النهائي:</span>
            <span class="value">${finalAmount.toLocaleString('ar-EG')} ₪</span>
          </div>
        </div>
      </div>

      ${order.notes ? `
        <div class="notes-section">
          <h3>ملاحظات</h3>
          <p>${order.notes}</p>
        </div>
      ` : ''}
    `
  }

  private translateStatus(status: string): string {
    const translations: { [key: string]: string } = {
      'pending': 'في الانتظار',
      'confirmed': 'مؤكد',
      'processing': 'قيد التنفيذ',
      'completed': 'مكتمل',
      'cancelled': 'ملغي'
    }

    return translations[status] || status
  }

  private translatePriority(priority: string): string {
    const translations: { [key: string]: string } = {
      'low': 'منخفضة',
      'normal': 'عادية',
      'high': 'عالية',
      'urgent': 'عاجلة'
    }

    return translations[priority] || priority
  }
}
