import React, { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON>, 
  Dropdown, 
  Space, 
  Typography, 
  Card, 
  Row, 
  Col, 
  Radio, 
  Tooltip,
  Badge,
  Divider
} from 'antd'
import {
  BulbOutlined,
  BulbFilled,
  SettingOutlined,
  SunOutlined,
  MoonOutlined,
  DesktopOutlined,
  BgColorsOutlined,
  CheckOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { themeManager, type ThemeMode, type ColorScheme } from '../../utils/themeManager'

const { Text } = Typography

// الأنماط المخصصة
const ThemeCard = styled(Card)`
  .ant-card-body {
    padding: 16px;
  }
  
  .theme-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    
    &:hover {
      background: rgba(24, 144, 255, 0.06);
    }
    
    &.active {
      background: rgba(24, 144, 255, 0.1);
      border-color: #1890ff;
    }
  }
  
  .color-scheme-option {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    position: relative;
    
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    &.active {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
    
    .check-icon {
      position: absolute;
      top: -4px;
      right: -4px;
      background: #1890ff;
      color: white;
      border-radius: 50%;
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
    }
  }
  
  .preview-section {
    margin-top: 16px;
    padding: 12px;
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.06);
  }
  
  .preview-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 0;
    
    .preview-color {
      width: 20px;
      height: 20px;
      border-radius: 4px;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }
  }
`

// معلومات أوضاع الثيم
const themeModes: Array<{
  key: ThemeMode
  label: string
  icon: React.ReactNode
  description: string
}> = [
  {
    key: 'light',
    label: 'فاتح',
    icon: <SunOutlined />,
    description: 'وضع فاتح مريح للعينين في الإضاءة الجيدة'
  },
  {
    key: 'dark',
    label: 'مظلم',
    icon: <MoonOutlined />,
    description: 'وضع مظلم مريح للعينين في الإضاءة المنخفضة'
  },
  {
    key: 'auto',
    label: 'تلقائي',
    icon: <DesktopOutlined />,
    description: 'يتبع إعدادات النظام تلقائياً'
  }
]

// معلومات أنظمة الألوان
const colorSchemeInfo: Record<ColorScheme, {
  name: string
  description: string
  gradient: string
}> = {
  blue: {
    name: 'أزرق',
    description: 'اللون الافتراضي الكلاسيكي',
    gradient: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)'
  },
  green: {
    name: 'أخضر',
    description: 'لون طبيعي ومريح',
    gradient: 'linear-gradient(135deg, #fff3cd 0%, #389e0d 100%)'
  },
  purple: {
    name: 'بنفسجي',
    description: 'لون أنيق ومميز',
    gradient: 'linear-gradient(135deg, #722ed1 0%, #531dab 100%)'
  },
  orange: {
    name: 'برتقالي',
    description: 'لون دافئ ونشيط',
    gradient: 'linear-gradient(135deg, #fa8c16 0%, #d46b08 100%)'
  },
  red: {
    name: 'أحمر',
    description: 'لون قوي وجذاب',
    gradient: 'linear-gradient(135deg, #f5222d 0%, #cf1322 100%)'
  }
}

// مكون تبديل الثيم
const ThemeToggle: React.FC<{
  size?: 'small' | 'middle' | 'large'
  showText?: boolean
  placement?: 'bottom' | 'bottomLeft' | 'bottomRight' | 'top' | 'topLeft' | 'topRight'
}> = ({ 
  size = 'middle', 
  showText = false,
  placement = 'bottomRight'
}) => {
  const [currentMode, setCurrentMode] = useState<ThemeMode>(themeManager.getMode())
  const [currentColorScheme, setCurrentColorScheme] = useState<ColorScheme>(themeManager.getColorScheme())
  const [effectiveMode, setEffectiveMode] = useState<'light' | 'dark'>(themeManager.getEffectiveThemeMode())
  const [dropdownVisible, setDropdownVisible] = useState(false)

  // تحديث الحالة عند تغيير الثيم
  useEffect(() => {
    const handleThemeChange = () => {
      setCurrentMode(themeManager.getMode())
      setCurrentColorScheme(themeManager.getColorScheme())
      setEffectiveMode(themeManager.getEffectiveThemeMode())
    }

    themeManager.addListener(handleThemeChange)

    return () => {
      themeManager.removeListener(handleThemeChange)
    }
  }, [])

  // تغيير وضع الثيم
  const handleModeChange = (mode: ThemeMode) => {
    themeManager.setMode(mode)
    setDropdownVisible(false)
  }

  // تغيير نظام الألوان
  const handleColorSchemeChange = (colorScheme: ColorScheme) => {
    themeManager.setColorScheme(colorScheme)
  }

  // الحصول على أيقونة الوضع الحالي
  const getCurrentIcon = () => {
    if (currentMode === 'auto') {
      return effectiveMode === 'dark' ? <MoonOutlined /> : <SunOutlined />
    }
    return currentMode === 'dark' ? <MoonOutlined /> : <SunOutlined />
  }

  // محتوى القائمة المنسدلة
  const dropdownContent = (
    <ThemeCard style={{ width: 320, maxHeight: 500, overflow: 'auto' }}>
      {/* اختيار وضع الثيم */}
      <div>
        <Text strong style={{ fontSize: 14, marginBottom: 12, display: 'block' }}>
          <BulbOutlined style={{ marginLeft: 8 }} />
          وضع الثيم
        </Text>
        
        <Space direction="vertical" style={{ width: '100%' }}>
          {themeModes.map(mode => (
            <div
              key={mode.key}
              className={`theme-option ${currentMode === mode.key ? 'active' : ''}`}
              onClick={() => handleModeChange(mode.key)}
            >
              <Space>
                {mode.icon}
                <div>
                  <Text strong>{mode.label}</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {mode.description}
                  </Text>
                </div>
              </Space>
              {currentMode === mode.key && <CheckOutlined style={{ color: '#1890ff' }} />}
            </div>
          ))}
        </Space>
      </div>

      <Divider />

      {/* اختيار نظام الألوان */}
      <div>
        <Text strong style={{ fontSize: 14, marginBottom: 12, display: 'block' }}>
          <BgColorsOutlined style={{ marginLeft: 8 }} />
          نظام الألوان
        </Text>
        
        <Row gutter={[8, 8]}>
          {Object.entries(colorSchemeInfo).map(([key, info]) => (
            <Col span={6} key={key}>
              <Tooltip title={`${info.name} - ${info.description}`}>
                <div
                  className={`color-scheme-option ${currentColorScheme === key ? 'active' : ''}`}
                  style={{ background: info.gradient }}
                  onClick={() => handleColorSchemeChange(key as ColorScheme)}
                >
                  {currentColorScheme === key && (
                    <div className="check-icon">
                      <CheckOutlined />
                    </div>
                  )}
                </div>
              </Tooltip>
            </Col>
          ))}
        </Row>
      </div>

      {/* معاينة الألوان */}
      <div className="preview-section">
        <Text strong style={{ fontSize: 12, marginBottom: 8, display: 'block' }}>
          معاينة الألوان:
        </Text>
        
        {(() => {
          const colors = themeManager.getColorSchemeInfo(currentColorScheme)
          return Object.entries(colors).map(([colorKey, colorValue]) => (
            <div key={colorKey} className="preview-item">
              <Text style={{ fontSize: 11 }}>
                {colorKey === 'primary' ? 'أساسي' :
                 colorKey === 'secondary' ? 'ثانوي' :
                 colorKey === 'success' ? 'نجاح' :
                 colorKey === 'warning' ? 'تحذير' :
                 colorKey === 'error' ? 'خطأ' :
                 colorKey === 'info' ? 'معلومات' : colorKey}
              </Text>
              <div 
                className="preview-color" 
                style={{ backgroundColor: colorValue as string }}
              />
            </div>
          ))
        })()}
      </div>
    </ThemeCard>
  )

  return (
    <Dropdown
      menu={{
        items: []
      }}
      popupRender={() => dropdownContent}
      trigger={['click']}
      placement={placement as any}
      open={dropdownVisible}
      onOpenChange={setDropdownVisible}
    >
      <Tooltip title="إعدادات الثيم والألوان">
        <Button
          size={size}
          icon={getCurrentIcon()}
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: size === 'small' ? '24px' : undefined,
            width: size === 'small' ? '24px' : undefined,
            minWidth: size === 'small' ? '24px' : undefined,
            padding: size === 'small' ? 0 : undefined,
            fontSize: size === 'small' ? '12px' : undefined
          }}
        >
          {showText && (
            <Space>
              <span>الثيم</span>
              {currentMode === 'auto' && (
                <Badge
                  size="small"
                  color="blue"
                  text="تلقائي"
                  style={{ fontSize: 10 }}
                />
              )}
            </Space>
          )}
        </Button>
      </Tooltip>
    </Dropdown>
  )
}

// مكون مبسط لتبديل سريع
export const QuickThemeToggle: React.FC<{
  size?: 'small' | 'middle' | 'large'
}> = ({ size = 'middle' }) => {
  const [effectiveMode, setEffectiveMode] = useState<'light' | 'dark'>(
    themeManager.getEffectiveThemeMode()
  )

  useEffect(() => {
    const unsubscribe = themeManager.addListener(() => {
      setEffectiveMode(themeManager.getEffectiveThemeMode())
    })
    return unsubscribe
  }, [])

  const toggleMode = () => {
    const newMode = effectiveMode === 'light' ? 'dark' : 'light'
    themeManager.setMode(newMode)
  }

  return (
    <Tooltip title={`تبديل إلى الوضع ${effectiveMode === 'light' ? 'المظلم' : 'الفاتح'}`}>
      <Button
        size={size}
        icon={effectiveMode === 'light' ? <MoonOutlined /> : <SunOutlined />}
        onClick={toggleMode}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      />
    </Tooltip>
  )
}

export default ThemeToggle
