{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": false, "noImplicitAny": false, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/renderer/**/*"], "exclude": ["node_modules", "dist", "src/main/**/*"]}