import React, { useState, useEffect } from 'react'
import {
  Card,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Row,
  Col,
  Divider,
  message,
  Space,
  Typography,
  Alert,
  Tooltip,
  Select
} from 'antd'
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  PercentageOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../../utils/logger'

const { Title, Text } = Typography
const { Option } = Select

interface ProductionSettings {
  // إعدادات التكلفة
  overhead_rate: number // نسبة التكاليف العامة
  default_hourly_rate: number // المعدل الافتراضي للساعة
  auto_update_product_cost: boolean // تحديث تكلفة المنتج تلقائياً
  cost_calculation_method: 'fifo' | 'lifo' | 'average' // طريقة حساب التكلفة

  // إعدادات الإنتاج
  auto_create_material_issue: boolean // إنشاء أوامر صرف المواد تلقائياً
  require_approval_for_issue: boolean // طلب موافقة على أوامر الصرف
  auto_start_labor_tracking: boolean // بدء تتبع العمالة تلقائياً
  default_production_priority: 'low' | 'normal' | 'high' | 'urgent'

  // إعدادات التنبيهات
  notify_low_materials: boolean // تنبيه عند نقص المواد
  notify_production_delays: boolean // تنبيه عند تأخير الإنتاج
  notify_cost_variance: boolean // تنبيه عند انحراف التكلفة
  cost_variance_threshold: number // حد انحراف التكلفة للتنبيه

  // إعدادات التقارير
  auto_generate_daily_reports: boolean // إنشاء تقارير يومية تلقائياً
  include_labor_details: boolean // تضمين تفاصيل العمالة في التقارير
  report_format: 'pdf' | 'excel' | 'both'
}

const ProductionSettingsComponent: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [settings, setSettings] = useState<ProductionSettings>({
    // القيم الافتراضية
    overhead_rate: 15,
    default_hourly_rate: 25,
    auto_update_product_cost: true,
    cost_calculation_method: 'average',
    auto_create_material_issue: true,
    require_approval_for_issue: false,
    auto_start_labor_tracking: false,
    default_production_priority: 'normal',
    notify_low_materials: true,
    notify_production_delays: true,
    notify_cost_variance: true,
    cost_variance_threshold: 10,
    auto_generate_daily_reports: false,
    include_labor_details: true,
    report_format: 'pdf'
  })

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    setLoading(true)
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionSettings', 'window.electronAPI غير متوفر')
        return
      }

      const result = await window.electronAPI.getProductionSettings()
      if (result.success) {
        const loadedSettings = {
          ...result.data,
          auto_update_product_cost: Boolean(result.data.auto_update_product_cost),
          auto_create_material_issue: Boolean(result.data.auto_create_material_issue),
          require_approval_for_issue: Boolean(result.data.require_approval_for_issue),
          auto_start_labor_tracking: Boolean(result.data.auto_start_labor_tracking),
          notify_low_materials: Boolean(result.data.notify_low_materials),
          notify_production_delays: Boolean(result.data.notify_production_delays),
          notify_cost_variance: Boolean(result.data.notify_cost_variance),
          auto_generate_daily_reports: Boolean(result.data.auto_generate_daily_reports),
          include_labor_details: Boolean(result.data.include_labor_details)
        }
        setSettings(loadedSettings)
        form.setFieldsValue(loadedSettings)
        Logger.info('ProductionSettings', 'تم تحميل إعدادات الإنتاج من قاعدة البيانات')
      } else {
        // استخدام الإعدادات الافتراضية
        form.setFieldsValue(settings)
        Logger.info('ProductionSettings', 'تم استخدام الإعدادات الافتراضية')
      }
    } catch (error) {
      Logger.error('ProductionSettings', 'خطأ في تحميل الإعدادات:', error)
      message.error('حدث خطأ في تحميل الإعدادات')
      // استخدام الإعدادات الافتراضية في حالة الخطأ
      form.setFieldsValue(settings)
    }
    setLoading(false)
  }

  const handleSave = async (values: ProductionSettings) => {
    setSaving(true)
    try {
      if (!window.electronAPI) {
        message.error('النظام غير متوفر')
        return
      }

      const result = await window.electronAPI.saveProductionSettings(values)
      if (result.success) {
        setSettings(values)
        message.success('تم حفظ الإعدادات بنجاح')
        Logger.info('ProductionSettings', 'تم حفظ إعدادات الإنتاج في قاعدة البيانات')
      } else {
        message.error(result.message || 'فشل في حفظ الإعدادات')
        Logger.error('ProductionSettings', 'فشل في حفظ الإعدادات:', result.message)
      }
    } catch (error) {
      Logger.error('ProductionSettings', 'خطأ في حفظ الإعدادات:', error)
      message.error('حدث خطأ في حفظ الإعدادات')
    }
    setSaving(false)
  }

  const handleReset = () => {
    form.setFieldsValue(settings)
    message.info('تم إعادة تعيين النموذج')
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={3}>
          <SettingOutlined /> إعدادات الإنتاج المتقدمة
        </Title>
        <Text type="secondary">
          تخصيص إعدادات نظام الإنتاج وحساب التكاليف
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        initialValues={settings}
      >
        {/* إعدادات التكلفة */}
        <Card title={
          <Space>
            <DollarOutlined />
            إعدادات التكلفة
          </Space>
        } style={{ marginBottom: '24px' }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="overhead_rate"
                label={
                  <Space>
                    نسبة التكاليف العامة (%)
                    <Tooltip title="النسبة المئوية للتكاليف العامة من إجمالي التكاليف المباشرة">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: 'يرجى إدخال نسبة التكاليف العامة' }]}
              >
                <InputNumber
                  min={0}
                  max={100}
                  step={0.5}
                  style={{ width: '100%' }}
                  addonAfter="%"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="default_hourly_rate"
                label={
                  <Space>
                    المعدل الافتراضي للساعة (₪)
                    <Tooltip title="المعدل الافتراضي لحساب تكلفة العمالة بالساعة">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: 'يرجى إدخال المعدل الافتراضي' }]}
              >
                <InputNumber
                  min={0}
                  step={0.5}
                  style={{ width: '100%' }}
                  addonBefore="₪"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="cost_calculation_method"
                label="طريقة حساب التكلفة"
              >
                <Select>
                  <Option value="fifo">الوارد أولاً صادر أولاً (FIFO)</Option>
                  <Option value="lifo">الوارد أخيراً صادر أولاً (LIFO)</Option>
                  <Option value="average">المتوسط المرجح</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="auto_update_product_cost"
                label="تحديث تكلفة المنتج تلقائياً"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* إعدادات الإنتاج */}
        <Card title={
          <Space>
            <SettingOutlined />
            إعدادات الإنتاج
          </Space>
        } style={{ marginBottom: '24px' }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="auto_create_material_issue"
                label="إنشاء أوامر صرف المواد تلقائياً"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="require_approval_for_issue"
                label="طلب موافقة على أوامر الصرف"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="auto_start_labor_tracking"
                label="بدء تتبع العمالة تلقائياً"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="default_production_priority"
                label="الأولوية الافتراضية للإنتاج"
              >
                <Select>
                  <Option value="low">منخفضة</Option>
                  <Option value="normal">عادية</Option>
                  <Option value="high">عالية</Option>
                  <Option value="urgent">عاجلة</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* إعدادات التنبيهات */}
        <Card title={
          <Space>
            <InfoCircleOutlined />
            إعدادات التنبيهات
          </Space>
        } style={{ marginBottom: '24px' }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="notify_low_materials"
                label="تنبيه عند نقص المواد"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="notify_production_delays"
                label="تنبيه عند تأخير الإنتاج"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="notify_cost_variance"
                label="تنبيه عند انحراف التكلفة"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="cost_variance_threshold"
                label={
                  <Space>
                    حد انحراف التكلفة للتنبيه (%)
                    <Tooltip title="النسبة المئوية للانحراف في التكلفة التي تستدعي إرسال تنبيه">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                <InputNumber
                  min={0}
                  max={100}
                  step={1}
                  style={{ width: '100%' }}
                  addonAfter="%"
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* إعدادات التقارير */}
        <Card title={
          <Space>
            <PercentageOutlined />
            إعدادات التقارير
          </Space>
        } style={{ marginBottom: '24px' }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="auto_generate_daily_reports"
                label="إنشاء تقارير يومية تلقائياً"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="include_labor_details"
                label="تضمين تفاصيل العمالة"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="report_format"
                label="تنسيق التقارير"
              >
                <Select>
                  <Option value="pdf">PDF</Option>
                  <Option value="excel">Excel</Option>
                  <Option value="both">كلاهما</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* أزرار الحفظ */}
        <Card>
          <div style={{ textAlign: 'center' }}>
            <Space size="large">
              <Button
                type="primary"
                htmlType="submit"
                icon={<SaveOutlined />}
                loading={saving}
                size="large"
              >
                حفظ الإعدادات
              </Button>
              <Button
                onClick={handleReset}
                icon={<ReloadOutlined />}
                size="large"
              >
                إعادة تعيين
              </Button>
            </Space>
          </div>

          <Divider />

          <Alert
            message="ملاحظة مهمة"
            description="تغيير هذه الإعدادات سيؤثر على جميع عمليات الإنتاج المستقبلية. تأكد من مراجعة الإعدادات قبل الحفظ."
            type="info"
            showIcon
          />
        </Card>
      </Form>
    </div>
  )
}

export default ProductionSettingsComponent
