import * as fs from 'fs'
import * as path from 'path'
import * as bcrypt from 'bcryptjs'
import { Logger } from '../utils/logger'
import { SqlJsPathResolver } from '../utils/SqlJsPathResolver'
import { app } from 'electron'
import initSqlJs from 'sql.js'

/**
 * خدمة قاعدة البيانات الموحدة باستخدام sql.js مع تحسينات الأداء والذاكرة
 */
export class SimpleDatabaseService {
  private static instance: SimpleDatabaseService
  private db: any = null
  private SQL: any = null
  private dbPath: string = ''
  private isShared: boolean = false

  private constructor() {
    const userDataPath = app.getPath('userData')
    this.dbPath = path.join(userDataPath, 'database.db')
  }

  public static getInstance(): SimpleDatabaseService {
    if (!SimpleDatabaseService.instance) {
      SimpleDatabaseService.instance = new SimpleDatabaseService()
    }
    return SimpleDatabaseService.instance
  }

  /**
   * تهيئة قاعدة البيانات
   */
  public async initialize(): Promise<void> {
    try {
      Logger.info('SimpleDatabaseService', '🔄 تهيئة قاعدة البيانات الموحدة (sql.js)...')

      // تهيئة sql.js باستخدام النظام الذكي لتحديد المسار
      const pathResolver = SqlJsPathResolver.getInstance()

      this.SQL = await initSqlJs({
        locateFile: (file: string) => {
          return pathResolver.locateFile(file)
        }
      })

      // تحميل قاعدة البيانات من الملف إذا كانت موجودة
      if (fs.existsSync(this.dbPath)) {
        const filebuffer = fs.readFileSync(this.dbPath)
        this.db = new this.SQL.Database(filebuffer)
      } else {
        this.db = new this.SQL.Database()
      }

      Logger.info('SimpleDatabaseService', '✅ تم إنشاء قاعدة البيانات بنجاح')

      // إنشاء الجداول
      await this.createTables()

      // إنشاء جداول الصور
      await this.createImageTables()

      // إدراج البيانات الافتراضية
      await this.insertDefaultData()

      // حفظ قاعدة البيانات
      this.saveDatabase()

      Logger.info('SimpleDatabaseService', '✅ تم تهيئة قاعدة البيانات بنجاح')
    } catch (error) {
      Logger.error('SimpleDatabaseService', '❌ خطأ في تهيئة قاعدة البيانات:', error)
      throw error
    }
  }

  /**
   * إنشاء الجداول الأساسية
   */
  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('قاعدة البيانات غير مهيأة')

    Logger.info('SimpleDatabaseService', '🔄 إنشاء الجداول...')

    const tables = [
      // جدول الإعدادات
      `CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // جدول المستخدمين
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_code TEXT UNIQUE,
        username TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        full_name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        role TEXT NOT NULL DEFAULT 'user',
        is_active BOOLEAN DEFAULT 1,
        last_login DATETIME,
        login_attempts INTEGER DEFAULT 0,
        locked_until DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        roles TEXT
      )`,

      // جدول جلسات المستخدمين
      `CREATE TABLE IF NOT EXISTS user_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        token TEXT UNIQUE NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // جدول العملاء
      `CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        city TEXT,
        country TEXT,
        tax_number TEXT,
        credit_limit REAL DEFAULT 0,
        balance REAL DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (created_by) REFERENCES users (id)
      )`,

      // جدول المنتجات
      `CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT,
        unit TEXT DEFAULT 'قطعة',
        cost_price REAL DEFAULT 0,
        selling_price REAL DEFAULT 0,
        stock_quantity REAL DEFAULT 0,
        min_stock_level REAL DEFAULT 0,
        barcode TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (created_by) REFERENCES users (id)
      )`,

      // جدول الفواتير
      `CREATE TABLE IF NOT EXISTS invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT UNIQUE NOT NULL,
        invoice_type TEXT NOT NULL CHECK (invoice_type IN ('sales', 'purchase', 'return_sales', 'return_purchase')),
        customer_id INTEGER,
        supplier_id INTEGER,
        invoice_date DATE NOT NULL,
        due_date DATE,
        subtotal REAL DEFAULT 0,
        tax_amount REAL DEFAULT 0,
        discount_amount REAL DEFAULT 0,
        total_amount REAL NOT NULL,
        paid_amount REAL DEFAULT 0,
        remaining_amount REAL DEFAULT 0,
        status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'pending', 'paid', 'partial', 'overdue', 'cancelled')),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )`,

      // جدول عناصر الفواتير
      `CREATE TABLE IF NOT EXISTS invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        product_id INTEGER,
        product_name TEXT NOT NULL,
        quantity REAL NOT NULL,
        unit_price REAL NOT NULL,
        discount_percentage REAL DEFAULT 0,
        discount_amount REAL DEFAULT 0,
        tax_percentage REAL DEFAULT 0,
        tax_amount REAL DEFAULT 0,
        total_amount REAL NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products (id)
      )`,

      // جدول مدفوعات الفواتير
      `CREATE TABLE IF NOT EXISTS invoice_payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        payment_date DATE NOT NULL,
        amount REAL NOT NULL,
        payment_method TEXT NOT NULL,
        reference_number TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users (id)
      )`,

      // جدول النسخ الاحتياطية
      `CREATE TABLE IF NOT EXISTS backups (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_size INTEGER DEFAULT 0,
        backup_type TEXT DEFAULT 'manual' CHECK (backup_type IN ('manual', 'automatic', 'scheduled')),
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        error_message TEXT,
        FOREIGN KEY (created_by) REFERENCES users (id)
      )`,

      // جدول قوالب الطباعة الموحدة
      `CREATE TABLE IF NOT EXISTS print_templates (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL CHECK (type IN ('invoice', 'receipt', 'report', 'certificate', 'custom', 'image')),
        category TEXT,
        is_default INTEGER DEFAULT 0,
        is_custom INTEGER DEFAULT 1,
        is_active INTEGER DEFAULT 1,
        template_data TEXT NOT NULL,
        preview_image TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (created_by) REFERENCES users (id)
      )`,

      // جدول الصور الموحد
      `CREATE TABLE IF NOT EXISTS unified_images (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        original_name TEXT NOT NULL,
        path TEXT NOT NULL,
        thumbnail_path TEXT,
        size INTEGER NOT NULL,
        width INTEGER,
        height INTEGER,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        context_type TEXT NOT NULL,
        context_id INTEGER NOT NULL,
        description TEXT DEFAULT '',
        tags TEXT DEFAULT '[]',
        is_active INTEGER DEFAULT 1,
        is_primary INTEGER DEFAULT 0,
        sort_order INTEGER DEFAULT 0,
        uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        uploaded_by INTEGER,
        metadata TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        deleted_at DATETIME
      )`,

      // جدول صور أوامر الإنتاج
      `CREATE TABLE IF NOT EXISTS production_order_images (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        image_name TEXT NOT NULL,
        image_path TEXT NOT NULL,
        file_size INTEGER DEFAULT 0,
        file_type TEXT,
        description TEXT,
        category TEXT DEFAULT 'general' CHECK (category IN ('general', 'material', 'process', 'quality', 'final')),
        is_primary BOOLEAN DEFAULT 0,
        tags TEXT,
        notes TEXT,
        uploaded_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
    ]

    for (const tableSQL of tables) {
      try {
        this.db.exec(tableSQL)
      } catch (error) {
        Logger.error('SimpleDatabaseService', 'خطأ في إنشاء جدول:', error)
      }
    }

    // إنشاء الفهارس
    this.createIndexes()

    Logger.info('SimpleDatabaseService', '✅ تم إنشاء جميع الجداول بنجاح')
  }

  /**
   * إنشاء جداول الصور المطلوبة
   */
  private async createImageTables(): Promise<void> {
    if (!this.db) throw new Error('قاعدة البيانات غير مهيأة')

    Logger.info('SimpleDatabaseService', '🔄 إنشاء جداول الصور...')

    try {
      // إنشاء جدول الصور الموحد
      const unifiedImagesTable = `
        CREATE TABLE IF NOT EXISTS unified_images (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          original_name TEXT NOT NULL,
          path TEXT NOT NULL,
          thumbnail_path TEXT,
          size INTEGER NOT NULL,
          width INTEGER,
          height INTEGER,
          type TEXT NOT NULL,
          category TEXT NOT NULL,
          context_type TEXT NOT NULL,
          context_id INTEGER NOT NULL,
          description TEXT DEFAULT '',
          tags TEXT DEFAULT '[]',
          is_active INTEGER DEFAULT 1,
          is_primary INTEGER DEFAULT 0,
          sort_order INTEGER DEFAULT 0,
          uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          uploaded_by INTEGER,
          metadata TEXT DEFAULT '{}',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          deleted_at DATETIME
        )
      `

      this.db.exec(unifiedImagesTable)
      Logger.info('SimpleDatabaseService', '✅ تم إنشاء جدول unified_images')

      // إنشاء فهارس جدول الصور
      const imageIndexes = [
        'CREATE INDEX IF NOT EXISTS idx_unified_images_category ON unified_images(category)',
        'CREATE INDEX IF NOT EXISTS idx_unified_images_context ON unified_images(context_type, context_id)',
        'CREATE INDEX IF NOT EXISTS idx_unified_images_active ON unified_images(is_active)',
        'CREATE INDEX IF NOT EXISTS idx_unified_images_primary ON unified_images(is_primary)',
        'CREATE INDEX IF NOT EXISTS idx_unified_images_uploaded_at ON unified_images(uploaded_at)',
        'CREATE INDEX IF NOT EXISTS idx_unified_images_sort_order ON unified_images(sort_order)'
      ]

      for (const indexSQL of imageIndexes) {
        try {
          this.db.exec(indexSQL)
        } catch (error) {
          Logger.warn('SimpleDatabaseService', `تحذير في إنشاء فهرس الصور: ${error}`)
        }
      }

      Logger.info('SimpleDatabaseService', '✅ تم إنشاء جداول الصور بنجاح')
    } catch (error) {
      Logger.error('SimpleDatabaseService', '❌ خطأ في إنشاء جداول الصور:', error)
      throw error
    }
  }

  /**
   * إنشاء الفهارس
   */
  private createIndexes(): void {
    if (!this.db) return

    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_customers_code ON customers(customer_code)',
      'CREATE INDEX IF NOT EXISTS idx_products_code ON products(product_code)',
      'CREATE INDEX IF NOT EXISTS idx_invoices_number ON invoices(invoice_number)',
      'CREATE INDEX IF NOT EXISTS idx_invoices_customer ON invoices(customer_id)',
      'CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice ON invoice_items(invoice_id)',
      'CREATE INDEX IF NOT EXISTS idx_invoice_payments_invoice ON invoice_payments(invoice_id)',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(token)',
      'CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key)',
      // فهارس جدول قوالب الطباعة الموحدة
      'CREATE INDEX IF NOT EXISTS idx_print_templates_type ON print_templates(type)',
      'CREATE INDEX IF NOT EXISTS idx_print_templates_category ON print_templates(category)',
      'CREATE INDEX IF NOT EXISTS idx_print_templates_active ON print_templates(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_print_templates_default ON print_templates(is_default)',
      'CREATE INDEX IF NOT EXISTS idx_print_templates_custom ON print_templates(is_custom)',

      // فهارس جدول الصور الموحد
      'CREATE INDEX IF NOT EXISTS idx_unified_images_category ON unified_images(category)',
      'CREATE INDEX IF NOT EXISTS idx_unified_images_context ON unified_images(context_type, context_id)',
      'CREATE INDEX IF NOT EXISTS idx_unified_images_active ON unified_images(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_unified_images_primary ON unified_images(is_primary)',
      'CREATE INDEX IF NOT EXISTS idx_unified_images_uploaded_at ON unified_images(uploaded_at)',
      'CREATE INDEX IF NOT EXISTS idx_unified_images_sort_order ON unified_images(sort_order)',

      // فهارس جدول صور أوامر الإنتاج
      'CREATE INDEX IF NOT EXISTS idx_production_order_images_order_id ON production_order_images(order_id)',
      'CREATE INDEX IF NOT EXISTS idx_production_order_images_category ON production_order_images(category)',
      'CREATE INDEX IF NOT EXISTS idx_production_order_images_primary ON production_order_images(is_primary)',
      'CREATE INDEX IF NOT EXISTS idx_production_order_images_created_at ON production_order_images(created_at)'
    ]

    indexes.forEach(indexSql => {
      try {
        this.db?.exec(indexSql)
      } catch (error) {
        Logger.warn('SimpleDatabaseService', 'تحذير في إنشاء فهرس:', error)
      }
    })
  }

  /**
   * إدراج البيانات الافتراضية
   */
  private async insertDefaultData(): Promise<void> {
    if (!this.db) return

    try {
      // التحقق من وجود مستخدم admin
      const adminResult = this.db.exec('SELECT id FROM users WHERE username = "admin"')

      if (!adminResult || adminResult.length === 0 || adminResult[0].values.length === 0) {
        Logger.info('SimpleDatabaseService', '🔄 إنشاء مستخدم admin افتراضي...')

        const hashedPassword = bcrypt.hashSync('admin123', 10)

        this.db.exec(`
          INSERT INTO users (username, password_hash, full_name, role, is_active)
          VALUES ('admin', '${hashedPassword.replace(/'/g, "''")}', 'مدير النظام', 'admin', 1)
        `)

        Logger.info('SimpleDatabaseService', '✅ تم إنشاء مستخدم admin')
      }

      // إدراج إعدادات افتراضية
      const settingsResult = this.db.exec('SELECT COUNT(*) as count FROM settings')
      const settingsCount = settingsResult && settingsResult.length > 0 && settingsResult[0].values.length > 0
        ? settingsResult[0].values[0][0] : 0
      
      if (settingsCount === 0) {
        Logger.info('SimpleDatabaseService', '🔄 إدراج الإعدادات الافتراضية...')
        
        const defaultSettings = [
          ['app_name', 'نظام المحاسبة', 'اسم التطبيق'],
          ['currency', 'ريال', 'العملة الافتراضية'],
          ['tax_rate', '15', 'معدل الضريبة الافتراضي'],
          ['sync_enabled', 'false', 'تفعيل المزامنة'],
          ['sync_interval', '300', 'فترة المزامنة بالثواني']
        ]

        for (const setting of defaultSettings) {
          this.db.exec(`INSERT INTO settings (key, value, description) VALUES ('${setting[0]}', '${setting[1]}', '${setting[2]}')`)
        }
        
        Logger.info('SimpleDatabaseService', '✅ تم إدراج الإعدادات الافتراضية')
      }

      // إدراج قوالب الطباعة الافتراضية
      const templatesResult = this.db.exec('SELECT COUNT(*) as count FROM print_templates')
      const templatesCount = templatesResult && templatesResult.length > 0 && templatesResult[0].values.length > 0
        ? templatesResult[0].values[0][0] : 0

      if (templatesCount === 0) {
        Logger.info('SimpleDatabaseService', '🔄 إدراج قوالب الطباعة الافتراضية...')

        const defaultTemplates = [
          {
            id: 'default-invoice',
            name: 'قالب الفاتورة الافتراضي',
            description: 'قالب افتراضي للفواتير',
            type: 'invoice',
            category: 'financial',
            is_default: 1,
            is_custom: 0,
            is_active: 1,
            template_data: JSON.stringify({
              pageSize: 'A4',
              orientation: 'portrait',
              fontSize: 12,
              fontFamily: 'Arial',
              showHeader: true,
              showFooter: true,
              showLogo: true,
              primaryColor: '#1890ff'
            })
          },
          {
            id: 'default-receipt',
            name: 'قالب السند الافتراضي',
            description: 'قالب افتراضي للسندات',
            type: 'receipt',
            category: 'financial',
            is_default: 1,
            is_custom: 0,
            is_active: 1,
            template_data: JSON.stringify({
              pageSize: 'A5',
              orientation: 'portrait',
              fontSize: 11,
              fontFamily: 'Arial',
              showHeader: true,
              showFooter: false,
              showLogo: true,
              primaryColor: '#52c41a'
            })
          },
          {
            id: 'default-image',
            name: 'قالب طباعة الصور الافتراضي',
            description: 'قالب افتراضي لطباعة الصور',
            type: 'image',
            category: 'media',
            is_default: 1,
            is_custom: 0,
            is_active: 1,
            template_data: JSON.stringify({
              pageSize: 'A4',
              orientation: 'portrait',
              layout: 'grid',
              imagesPerPage: 6,
              quality: 'high',
              showMetadata: true
            })
          }
        ]

        for (const template of defaultTemplates) {
          this.db.exec(`
            INSERT INTO print_templates (id, name, description, type, category, is_default, is_custom, is_active, template_data, created_at, updated_at)
            VALUES ('${template.id}', '${template.name}', '${template.description}', '${template.type}', '${template.category}', ${template.is_default}, ${template.is_custom}, ${template.is_active}, '${template.template_data}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          `)
        }

        Logger.info('SimpleDatabaseService', '✅ تم إدراج قوالب الطباعة الافتراضية')
      }

    } catch (error) {
      Logger.error('SimpleDatabaseService', '❌ خطأ في إدراج البيانات الافتراضية:', error)
    }
  }

  /**
   * الحصول على قاعدة البيانات
   */
  public getDatabase(): any {
    if (!this.db) {
      throw new Error('قاعدة البيانات غير مهيأة')
    }

    // إنشاء واجهة محسنة لـ sql.js مع دعم prepared statements
    const dbWrapper = {
      ...this.db,
      prepare: (sql: string) => {
        return {
          get: (...params: any[]) => {
            try {
              // تحضير الاستعلام مع المعاملات
              let preparedSql = sql
              if (params && params.length > 0) {
                // استبدال علامات الاستفهام بالقيم بشكل صحيح
                let paramIndex = 0
                preparedSql = sql.replace(/\?/g, () => {
                  if (paramIndex < params.length) {
                    const param = params[paramIndex++]
                    if (param === null || param === undefined) {
                      return 'NULL'
                    } else if (typeof param === 'string') {
                      return `'${param.replace(/'/g, "''")}'`
                    } else if (typeof param === 'boolean') {
                      return param ? '1' : '0'
                    } else {
                      return String(param)
                    }
                  }
                  return '?'
                })
              }

              const result = this.db.exec(preparedSql)
              if (result && result.length > 0 && result[0].values.length > 0) {
                const columns = result[0].columns
                const values = result[0].values[0]
                const row: any = {}
                columns.forEach((col: string, index: number) => {
                  row[col] = values[index]
                })
                return row
              }
              return null
            } catch (error) {
              Logger.error('SimpleDatabaseService', 'خطأ في تنفيذ get:', error)
              return null
            }
          },
          all: (...params: any[]) => {
            try {
              // تحضير الاستعلام مع المعاملات
              let preparedSql = sql
              if (params && params.length > 0) {
                // استبدال علامات الاستفهام بالقيم بشكل صحيح
                let paramIndex = 0
                preparedSql = sql.replace(/\?/g, () => {
                  if (paramIndex < params.length) {
                    const param = params[paramIndex++]
                    if (param === null || param === undefined) {
                      return 'NULL'
                    } else if (typeof param === 'string') {
                      return `'${param.replace(/'/g, "''")}'`
                    } else if (typeof param === 'boolean') {
                      return param ? '1' : '0'
                    } else {
                      return String(param)
                    }
                  }
                  return '?'
                })
              }

              const result = this.db.exec(preparedSql)
              if (result && result.length > 0) {
                const columns = result[0].columns
                const values = result[0].values
                return values.map((row: any[]) => {
                  const obj: any = {}
                  columns.forEach((col: string, index: number) => {
                    obj[col] = row[index]
                  })
                  return obj
                })
              }
              return []
            } catch (error) {
              Logger.error('SimpleDatabaseService', 'خطأ في تنفيذ all:', error)
              return []
            }
          },
          run: (...params: any[]) => {
            try {
              // تحضير الاستعلام مع المعاملات
              let preparedSql = sql
              if (params && params.length > 0) {
                // استبدال علامات الاستفهام بالقيم بشكل صحيح
                let paramIndex = 0
                preparedSql = sql.replace(/\?/g, () => {
                  if (paramIndex < params.length) {
                    const param = params[paramIndex++]
                    if (param === null || param === undefined) {
                      return 'NULL'
                    } else if (typeof param === 'string') {
                      return `'${param.replace(/'/g, "''")}'`
                    } else if (typeof param === 'boolean') {
                      return param ? '1' : '0'
                    } else {
                      return String(param)
                    }
                  }
                  return '?'
                })
              }

              Logger.debug('SimpleDatabaseService', 'تنفيذ SQL:', preparedSql)

              // تنفيذ الاستعلام باستخدام sql.js
              this.db.exec(preparedSql)

              // الحصول على آخر معرف مدرج
              let lastInsertRowid = 0
              try {
                const result = this.db.exec("SELECT last_insert_rowid() as id")
                if (result && result.length > 0 && result[0].values.length > 0) {
                  lastInsertRowid = result[0].values[0][0] as number
                }
              } catch (e) {
                // في حالة فشل الحصول على المعرف، نستخدم قيمة افتراضية
                lastInsertRowid = Date.now() % 1000000 // معرف مؤقت
              }

              this.saveDatabase()
              return { changes: 1, lastInsertRowid }
            } catch (error) {
              Logger.error('SimpleDatabaseService', 'خطأ في تنفيذ run:', error)
              return { changes: 0, lastInsertRowid: 0 }
            }
          }
        }
      },
      run: (sql: string, ...params: any[]) => {
        try {
          // تحضير الاستعلام مع المعاملات
          let preparedSql = sql
          if (params && params.length > 0) {
            // استبدال علامات الاستفهام بالقيم بشكل صحيح
            let paramIndex = 0
            preparedSql = sql.replace(/\?/g, () => {
              if (paramIndex < params.length) {
                const param = params[paramIndex++]
                if (param === null || param === undefined) {
                  return 'NULL'
                } else if (typeof param === 'string') {
                  return `'${param.replace(/'/g, "''")}'`
                } else if (typeof param === 'boolean') {
                  return param ? '1' : '0'
                } else {
                  return String(param)
                }
              }
              return '?'
            })
          }

          Logger.debug('SimpleDatabaseService', 'تنفيذ SQL مباشر:', preparedSql)

          // تنفيذ الاستعلام باستخدام sql.js
          this.db.exec(preparedSql)

          // الحصول على آخر معرف مدرج
          let lastInsertRowid = 0
          try {
            const result = this.db.exec("SELECT last_insert_rowid() as id")
            if (result && result.length > 0 && result[0].values.length > 0) {
              lastInsertRowid = result[0].values[0][0] as number
            }
          } catch (e) {
            // في حالة فشل الحصول على المعرف، نستخدم قيمة افتراضية
            lastInsertRowid = Date.now() % 1000000 // معرف مؤقت
          }

          this.saveDatabase()
          return { changes: 1, lastInsertRowid }
        } catch (error) {
          Logger.error('SimpleDatabaseService', 'خطأ في تنفيذ run مباشر:', error)
          return { changes: 0, lastInsertRowid: 0 }
        }
      },
      exec: (sql: string) => {
        try {
          return this.db.exec(sql)
        } catch (error) {
          Logger.error('SimpleDatabaseService', 'خطأ في تنفيذ exec:', error)
          return []
        }
      }
    }

    return dbWrapper
  }

  /**
   * حفظ قاعدة البيانات إلى الملف
   */
  public saveDatabase(): void {
    if (!this.db) {
      Logger.warn('SimpleDatabaseService', 'لا يمكن حفظ قاعدة البيانات - غير مهيأة')
      return
    }

    try {
      const data = this.db.export()
      fs.writeFileSync(this.dbPath, data)
      Logger.debug('SimpleDatabaseService', '💾 تم حفظ قاعدة البيانات')
    } catch (error) {
      Logger.error('SimpleDatabaseService', '❌ خطأ في حفظ قاعدة البيانات:', error)
    }
  }

  /**
   * إغلاق قاعدة البيانات
   */
  public close(): void {
    if (this.db) {
      // حفظ قاعدة البيانات قبل الإغلاق
      this.saveDatabase()
      this.db.close()
      this.db = null
      Logger.info('SimpleDatabaseService', '✅ تم إغلاق قاعدة البيانات')
    }
  }

  /**
   * الحصول على مسار قاعدة البيانات الحالي
   */
  public getDatabasePath(): string {
    return this.dbPath
  }

  /**
   * فحص نوع قاعدة البيانات المستخدمة حالياً
   */
  public getDatabaseType(): 'local' | 'shared' {
    return this.isShared ? 'shared' : 'local'
  }

  /**
   * الحصول على معلومات حالة قاعدة البيانات الحالية
   */
  public getDatabaseStatus(): any {
    try {
      const exists = fs.existsSync(this.dbPath)
      const stats = exists ? fs.statSync(this.dbPath) : null

      return {
        path: this.dbPath,
        isShared: this.isShared,
        exists: exists,
        size: stats ? stats.size : 0,
        sizeKB: stats ? Math.round(stats.size / 1024) : 0,
        lastModified: stats ? stats.mtime : null,
        isConnected: !!this.db,
        tableCount: this.db ? this.getTableCount() : 0,
        recordCount: this.db ? this.getRecordCount() : 0,
        isEmpty: this.db ? this.getRecordCount() === 0 : true,
        isNetworkPath: this.dbPath.startsWith('\\\\')
      }
    } catch (error) {
      Logger.error('SimpleDatabaseService', 'خطأ في الحصول على حالة قاعدة البيانات:', error)
      return {
        path: this.dbPath,
        isShared: this.isShared,
        exists: false,
        size: 0,
        sizeKB: 0,
        lastModified: null,
        isConnected: false,
        tableCount: 0,
        recordCount: 0,
        isEmpty: true,
        isNetworkPath: false
      }
    }
  }

  private getTableCount(): number {
    try {
      const result = this.db.exec("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
      return result && result.length > 0 && result[0].values.length > 0 ? result[0].values[0][0] : 0
    } catch {
      return 0
    }
  }

  private getRecordCount(): number {
    try {
      const result = this.db.exec("SELECT COUNT(*) FROM users")
      return result && result.length > 0 && result[0].values.length > 0 ? result[0].values[0][0] : 0
    } catch {
      return 0
    }
  }




  /**
   * تحديد مسار قاعدة البيانات المستخدمة (محلية أو مشتركة)
   */
  public async setDatabasePath(newPath: string): Promise<boolean> {
    try {
      Logger.info('SimpleDatabaseService', `🔄 تغيير مسار قاعدة البيانات إلى: ${newPath}`)

      // حفظ قاعدة البيانات الحالية
      if (this.db) {
        this.saveDatabase()
        this.close()
      }

      // تحديث المسار
      this.dbPath = newPath
      this.isShared = !newPath.includes(app.getPath('userData'))

      // إعادة تهيئة قاعدة البيانات
      await this.initialize()

      Logger.success('SimpleDatabaseService', '✅ تم تغيير مسار قاعدة البيانات بنجاح')
      return true
    } catch (error) {
      Logger.error('SimpleDatabaseService', '❌ خطأ في تغيير مسار قاعدة البيانات:', error)
      return false
    }
  }

  /**
   * التبديل إلى قاعدة البيانات المشتركة
   */
  public async switchToSharedDatabase(sharedDbPath: string): Promise<boolean> {
    return this.setDatabasePath(sharedDbPath)
  }

  /**
   * التبديل إلى قاعدة البيانات المحلية
   */
  public async switchToLocalDatabase(): Promise<boolean> {
    const userDataPath = app.getPath('userData')
    const localDbPath = path.join(userDataPath, 'database.db')
    return this.setDatabasePath(localDbPath)
  }

  /**
   * فحص وجود عمود في جدول
   */
  public columnExists(tableName: string, columnName: string): boolean {
    try {
      const result = this.db.exec(`PRAGMA table_info(${tableName})`)
      if (result && result.length > 0) {
        const columns = result[0].values
        return columns.some((row: any) => row[1] === columnName)
      }
      return false
    } catch {
      return false
    }
  }

  /**
   * إضافة عمود بأمان (فقط إذا لم يكن موجوداً)
   */
  public safeAddColumn(tableName: string, columnDefinition: string): void {
    try {
      const columnName = columnDefinition.split(' ')[0]
      if (!this.columnExists(tableName, columnName)) {
        this.db.exec(`ALTER TABLE ${tableName} ADD COLUMN ${columnDefinition}`)
        Logger.info('SimpleDatabaseService', `✅ تم إضافة العمود ${columnName} إلى جدول ${tableName}`)
      }
    } catch (error) {
      Logger.warn('SimpleDatabaseService', `تحذير في إضافة العمود:`, error)
    }
  }

  /**
   * إنشاء نسخة احتياطية
   */
  public async createBackup(fileName: string): Promise<void> {
    try {
      if (!this.db) {
        throw new Error('قاعدة البيانات غير مهيأة')
      }

      const backupDir = path.join(path.dirname(this.dbPath), 'backups')
      const backupPath = path.join(backupDir, fileName)

      // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true })
      }

      // تصدير قاعدة البيانات وحفظها كنسخة احتياطية
      const data = this.db.export()
      fs.writeFileSync(backupPath, data)

      // التحقق من نجاح الكتابة
      const backupStats = fs.statSync(backupPath)
      if (backupStats.size === 0) {
        throw new Error('النسخة الاحتياطية فارغة - فشل في التصدير')
      }

      Logger.info('SimpleDatabaseService', '✅ تم إنشاء النسخة الاحتياطية:', backupPath)
    } catch (error) {
      Logger.error('SimpleDatabaseService', '❌ خطأ في إنشاء النسخة الاحتياطية:', error)
      throw error
    }
  }

  /**
   * استعادة نسخة احتياطية
   */
  public async restoreBackup(backupPath: string): Promise<void> {
    try {
      if (!fs.existsSync(backupPath)) {
        throw new Error('ملف النسخة الاحتياطية غير موجود')
      }

      // إغلاق قاعدة البيانات الحالية
      this.close()

      // نسخ النسخة الاحتياطية إلى مسار قاعدة البيانات الرئيسية
      fs.copyFileSync(backupPath, this.dbPath)

      // إعادة تهيئة قاعدة البيانات
      await this.initialize()

      Logger.info('SimpleDatabaseService', '✅ تم استعادة النسخة الاحتياطية من:', backupPath)
    } catch (error) {
      Logger.error('SimpleDatabaseService', '❌ خطأ في استعادة النسخة الاحتياطية:', error)
      throw error
    }
  }

  /**
   * حذف ملف نسخة احتياطية
   */
  public async deleteBackupFile(backupPath: string): Promise<void> {
    try {

      if (fs.existsSync(backupPath)) {
        fs.unlinkSync(backupPath)
        Logger.info('SimpleDatabaseService', '✅ تم حذف ملف النسخة الاحتياطية:', backupPath)
      }
    } catch (error) {
      Logger.error('SimpleDatabaseService', '❌ خطأ في حذف ملف النسخة الاحتياطية:', error)
      // لا نرمي الخطأ هنا لأن حذف الملف اختياري
    }
  }

  /**
   * فحص صحة قاعدة البيانات
   */
  public checkHealth(): any {
    try {
      if (!this.db) {
        return {
          success: false,
          healthy: false,
          connected: false,
          error: 'قاعدة البيانات غير متصلة'
        }
      }

      // اختبار بسيط للاتصال
      const testQuery = this.db.exec('SELECT 1 as test')
      let result: any = null
      if (testQuery.length > 0 && testQuery[0].values.length > 0) {
        const columns = testQuery[0].columns
        const values = testQuery[0].values[0]
        result = {}
        columns.forEach((col: string, index: number) => {
          result[col] = values[index]
        })
      }

      return {
        success: true,
        healthy: true,
        connected: true,
        test: result?.test === 1,
        details: {
          path: this.dbPath,
          isShared: this.isShared,
          tableCount: this.getTableCount(),
          recordCount: this.getRecordCount()
        }
      }
    } catch (error) {
      Logger.error('SimpleDatabaseService', 'خطأ في فحص صحة قاعدة البيانات:', error)
      return {
        success: false,
        healthy: false,
        connected: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }


}
