import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  DatePicker,
  message,
  Tag,
  Tooltip,
  Space,
  Alert,
  Row,
  Col,
  Typography,
  Spin,
  Flex,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
  EyeOutlined,
  Bar<PERSON><PERSON>Outlined
} from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/ar';
import { FiscalPeriod } from '../../types/fiscalPeriod';
import { fiscalPeriodApi } from '../../services/fiscalPeriodApi';

interface FiscalPeriodManagerProps {
  onPeriodSelect?: (period: FiscalPeriod) => void;
}

const FiscalPeriodManager: React.FC<FiscalPeriodManagerProps> = ({ onPeriodSelect }) => {
  const [periods, setPeriods] = useState<FiscalPeriod[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingPeriod, setEditingPeriod] = useState<FiscalPeriod | null>(null);
  const [form] = Form.useForm();

  // Configure dayjs for Arabic locale
  dayjs.locale('ar');

  useEffect(() => {
    loadPeriods();
  }, []);

  const loadPeriods = async () => {
    try {
      setLoading(true);
      const data = await fiscalPeriodApi.getAllPeriods();
      setPeriods(data);
    } catch (err) {
      setError('فشل في تحميل الفترات المالية');
      message.error('فشل في تحميل الفترات المالية');
      console.error('Error loading periods:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePeriod = () => {
    setEditingPeriod(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditPeriod = (period: FiscalPeriod) => {
    setEditingPeriod(period);
    form.setFieldsValue({
      name: period.period_name,
      startDate: dayjs(period.start_date),
      endDate: dayjs(period.end_date),
      description: period.notes || ''
    });
    setIsModalVisible(true);
  };

  const handleSavePeriod = async () => {
    try {
      const values = await form.validateFields();
      const formData = {
        name: values.name,
        startDate: values.startDate.toDate(),
        endDate: values.endDate.toDate(),
        description: values.description || ''
      };

      if (editingPeriod) {
        await fiscalPeriodApi.updatePeriod(editingPeriod.id.toString(), formData);
        message.success('تم تحديث الفترة المالية بنجاح');
      } else {
        await fiscalPeriodApi.createPeriod(formData);
        message.success('تم إنشاء الفترة المالية بنجاح');
      }

      setIsModalVisible(false);
      form.resetFields();
      loadPeriods();
    } catch (err) {
      message.error('فشل في حفظ الفترة المالية');
      console.error('Error saving period:', err);
    }
  };

  const handleClosePeriod = async (periodId: string) => {
    try {
      await fiscalPeriodApi.closePeriod(periodId);
      message.success('تم إقفال الفترة المالية بنجاح');
      loadPeriods();
    } catch (err) {
      message.error('فشل في إقفال الفترة المالية');
      console.error('Error closing period:', err);
    }
  };

  const handleReopenPeriod = async (periodId: string) => {
    try {
      await fiscalPeriodApi.reopenPeriod(periodId);
      message.success('تم إعادة فتح الفترة المالية بنجاح');
      loadPeriods();
    } catch (err) {
      message.error('فشل في إعادة فتح الفترة المالية');
      console.error('Error reopening period:', err);
    }
  };

  const getStatusColor = (status: 'open' | 'closed' | 'locked'): 'success' | 'error' | 'warning' | 'default' => {
    switch (status) {
      case 'open': return 'success';
      case 'closed': return 'error';
      case 'locked': return 'warning';
      default: return 'default';
    }
  };

  const getStatusText = (status: 'open' | 'closed' | 'locked'): string => {
    switch (status) {
      case 'open': return 'مفتوحة';
      case 'closed': return 'مقفلة';
      case 'locked': return 'مؤمنة';
      default: return 'غير محدد';
    }
  };

  const formatDate = (date: string): string => {
    return dayjs(date).format('DD/MM/YYYY');
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <Spin size="large" />
        <Typography.Text style={{ marginLeft: 16 }}>جاري التحميل...</Typography.Text>
      </div>
    );
  }

  return (
    <div>
      <Card>
        <div style={{ padding: '24px' }}>
          <Flex justify="space-between" align="center" style={{ marginBottom: 24 }}>
            <Typography.Title level={3}>
              إدارة الفترات المالية
            </Typography.Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreatePeriod}
              size="large"
            >
              إضافة فترة مالية جديدة
            </Button>
          </Flex>

          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              closable
              onClose={() => setError(null)}
              style={{ marginBottom: 16 }}
            />
          )}

          <Table
            dataSource={periods}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `${range[0]}-${range[1]} من ${total} عنصر`,
            }}
            scroll={{ x: 800 }}
          >
            <Table.Column
              title="اسم الفترة"
              dataIndex="period_name"
              key="period_name"
              render={(text: string) => (
                <Typography.Text strong>{text}</Typography.Text>
              )}
            />
            <Table.Column
              title="تاريخ البداية"
              dataIndex="start_date"
              key="start_date"
              render={(date: string) => formatDate(date)}
            />
            <Table.Column
              title="تاريخ النهاية"
              dataIndex="end_date"
              key="end_date"
              render={(date: string) => formatDate(date)}
            />
            <Table.Column
              title="الحالة"
              dataIndex="status"
              key="status"
              render={(status: 'open' | 'closed' | 'locked') => (
                <Tag color={getStatusColor(status)}>
                  {getStatusText(status)}
                </Tag>
              )}
            />
            <Table.Column
              title="الوصف"
              dataIndex="notes"
              key="notes"
              render={(notes: string) => (
                <Typography.Text type="secondary">
                  {notes || '-'}
                </Typography.Text>
              )}
            />
            <Table.Column
              title="الإجراءات"
              key="actions"
              render={(_, record: FiscalPeriod) => (
                <Space size="small">
                  <Tooltip title="عرض التفاصيل">
                    <Button
                      type="text"
                      icon={<EyeOutlined />}
                      size="small"
                      onClick={() => onPeriodSelect?.(record)}
                    />
                  </Tooltip>

                  {record.status === 'open' && (
                    <>
                      <Tooltip title="تعديل">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          size="small"
                          onClick={() => handleEditPeriod(record)}
                        />
                      </Tooltip>
                      <Tooltip title="إقفال الفترة">
                        <Button
                          type="text"
                          icon={<LockOutlined />}
                          size="small"
                          danger
                          onClick={() => handleClosePeriod(record.id.toString())}
                        />
                      </Tooltip>
                    </>
                  )}

                  {record.status === 'closed' && (
                    <Tooltip title="إعادة فتح الفترة">
                      <Button
                        type="text"
                        icon={<UnlockOutlined />}
                        size="small"
                        onClick={() => handleReopenPeriod(record.id.toString())}
                      />
                    </Tooltip>
                  )}

                  <Tooltip title="التقارير">
                    <Button
                      type="text"
                      icon={<BarChartOutlined />}
                      size="small"
                    />
                  </Tooltip>
                </Space>
              )}
            />
          </Table>
        </div>
      </Card>

      {/* Modal for Create/Edit Period */}
      <Modal
        title={editingPeriod ? 'تعديل الفترة المالية' : 'إضافة فترة مالية جديدة'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSavePeriod}
          style={{ marginTop: 16 }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label="اسم الفترة"
                rules={[
                  { required: true, message: 'يرجى إدخال اسم الفترة' },
                  { min: 3, message: 'يجب أن يكون اسم الفترة 3 أحرف على الأقل' }
                ]}
              >
                <Input placeholder="أدخل اسم الفترة المالية" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="startDate"
                label="تاريخ البداية"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ البداية' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="اختر تاريخ البداية"
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="endDate"
                label="تاريخ النهاية"
                rules={[
                  { required: true, message: 'يرجى اختيار تاريخ النهاية' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const startDate = getFieldValue('startDate');
                      if (!value || !startDate || value.isAfter(startDate)) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('تاريخ النهاية يجب أن يكون بعد تاريخ البداية'));
                    },
                  }),
                ]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="اختر تاريخ النهاية"
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="description"
                label="الوصف"
              >
                <Input.TextArea
                  rows={3}
                  placeholder="أدخل وصف الفترة المالية (اختياري)"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Flex justify="end" gap="small">
                <Button onClick={() => {
                  setIsModalVisible(false);
                  form.resetFields();
                }}>
                  إلغاء
                </Button>
                <Button type="primary" htmlType="submit">
                  {editingPeriod ? 'تحديث' : 'إضافة'}
                </Button>
              </Flex>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default FiscalPeriodManager;
