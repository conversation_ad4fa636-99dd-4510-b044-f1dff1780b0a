# دليل ربط أجهزة البصمة - شامل ومفصل

## 📋 المحتويات
1. [متطلبات النظام](#متطلبات-النظام)
2. [الأجهزة المدعومة](#الأجهزة-المدعومة)
3. [إعداد الشبكة](#إعداد-الشبكة)
4. [خطوات الربط](#خطوات-الربط)
5. [اختبار الاتصال](#اختبار-الاتصال)
6. [حل المشاكل الشائعة](#حل-المشاكل-الشائعة)
7. [الصيانة والمتابعة](#الصيانة-والمتابعة)

---

## 🔧 متطلبات النظام

### متطلبات الشبكة
- **الشبكة المحلية**: يجب أن يكون الجهاز والحاسوب على نفس الشبكة
- **عنوان IP**: عنوان IP ثابت للجهاز (مفضل)
- **المنفذ**: المنفذ الافتراضي 4370 (قابل للتغيير)
- **البروتوكول**: TCP/IP
- **الجدار الناري**: السماح للمنفذ المحدد

### متطلبات الطاقة
- **الكهرباء**: تيار مستمر 12V أو حسب مواصفات الجهاز
- **الاستهلاك**: عادة أقل من 5 واط
- **النسخ الاحتياطي**: UPS مفضل لضمان عدم انقطاع الخدمة

---

## 📱 الأجهزة المدعومة

### أجهزة ZKTeco (الأكثر شيوعاً)
| الموديل | السعة | المنفذ الافتراضي | الملاحظات |
|---------|-------|-----------------|-----------|
| **F18** | 1,000 مستخدم | 4370 | مناسب للمكاتب الصغيرة |
| **K40** | 3,000 مستخدم | 4370 | مناسب للشركات المتوسطة |
| **K50** | 5,000 مستخدم | 4370 | مناسب للشركات الكبيرة |
| **iClock 360** | 3,000 مستخدم | 4370 | شاشة ملونة متقدمة |

### أجهزة Hikvision
| الموديل | السعة | المنفذ الافتراضي | الملاحظات |
|---------|-------|-----------------|-----------|
| **DS-K1T201** | 1,000 مستخدم | 8000 | دعم RFID |
| **DS-K1T321** | 3,000 مستخدم | 8000 | شاشة LCD |

### أجهزة أخرى مدعومة
- **Suprema**: BioStation series
- **Anviz**: سلسلة OA1000
- **eSSL**: سلسلة X990

---

## 🌐 إعداد الشبكة

### الخطوة 1: تحديد عنوان IP للجهاز

#### الطريقة الأولى: من إعدادات الجهاز
1. اضغط على **Menu** في الجهاز
2. اختر **System** أو **النظام**
3. اختر **Network** أو **الشبكة**
4. سجل عنوان IP المعروض

#### الطريقة الثانية: استخدام برنامج البحث
1. حمل برنامج **Device Network Search** من الشركة المصنعة
2. شغل البرنامج على نفس الشبكة
3. سيظهر جميع الأجهزة المتصلة مع عناوين IP

#### الطريقة الثالثة: فحص الشبكة
```bash
# استخدم أمر ping لفحص النطاق
ping ***********
ping *************
ping *************
# ... وهكذا
```

### الخطوة 2: تعيين IP ثابت (مفضل)
1. ادخل إعدادات الجهاز
2. اختر **Network Settings**
3. غير من **DHCP** إلى **Static IP**
4. أدخل:
   - **IP Address**: مثل *************
   - **Subnet Mask**: عادة *************
   - **Gateway**: عنوان الراوتر (مثل ***********)
   - **DNS**: ******* أو عنوان DNS المحلي

---

## 🔗 خطوات الربط

### الخطوة 1: التحضير
1. **تأكد من الاتصال**:
   - الجهاز متصل بالكهرباء ويعمل
   - الجهاز متصل بالشبكة (كابل إيثرنت أو WiFi)
   - الحاسوب على نفس الشبكة

2. **جمع المعلومات المطلوبة**:
   - اسم الجهاز (اختياري)
   - موديل الجهاز
   - عنوان IP
   - رقم المنفذ (عادة 4370)
   - موقع الجهاز
   - السعة القصوى

### الخطوة 2: إضافة الجهاز في النظام
1. افتح **إدارة الموظفين** → **أجهزة البصمة**
2. اضغط **إضافة جهاز**
3. املأ البيانات:

#### البيانات الأساسية
- **اسم الجهاز**: مثل "جهاز البصمة الرئيسي"
- **موديل الجهاز**: مثل "ZKTeco F18"
- **معرف الجهاز**: رقم فريد (1, 2, 3...)

#### إعدادات الشبكة
- **عنوان IP**: العنوان الذي حصلت عليه
- **المنفذ**: 4370 (أو حسب الجهاز)

#### معلومات إضافية
- **الموقع**: مثل "المدخل الرئيسي"
- **الحالة**: نشط
- **السعة القصوى**: حسب موديل الجهاز
- **إصدار البرنامج**: إذا كان معروفاً

### الخطوة 3: حفظ واختبار
1. اضغط **حفظ**
2. اضغط **اختبار الاتصال** للتأكد من الربط
3. إذا نجح الاختبار، اضغط **مزامنة** لتحديث البيانات

---

## ✅ اختبار الاتصال

### اختبار أساسي
1. من قائمة الأجهزة، اضغط زر **اختبار الاتصال**
2. انتظر النتيجة:
   - ✅ **نجح**: الجهاز متصل ويعمل
   - ❌ **فشل**: راجع خطوات حل المشاكل

### اختبار متقدم
```bash
# اختبار ping من سطر الأوامر
ping [IP_ADDRESS]

# مثال
ping *************

# اختبار المنفذ
telnet [IP_ADDRESS] [PORT]

# مثال
telnet ************* 4370
```

### اختبار الوظائف
1. **تسجيل بصمة تجريبية**: جرب تسجيل بصمة جديدة
2. **قراءة البصمة**: جرب قراءة بصمة موجودة
3. **مزامنة البيانات**: تأكد من نقل البيانات للنظام
4. **تسجيل الحضور**: جرب تسجيل حضور وانصراف

---

## 🔧 حل المشاكل الشائعة

### المشكلة 1: لا يمكن الوصول للجهاز
**الأعراض**: فشل في اختبار الاتصال، رسالة "لا يمكن الوصول للجهاز"

**الحلول**:
1. **تحقق من الشبكة**:
   ```bash
   ping [IP_ADDRESS]
   ```
2. **تحقق من الكابل**: تأكد من سلامة كابل الشبكة
3. **تحقق من الطاقة**: تأكد من وصول الكهرباء للجهاز
4. **إعادة تشغيل**: أعد تشغيل الجهاز والراوتر
5. **تحقق من IP**: تأكد من صحة عنوان IP

### المشكلة 2: الاتصال بطيء أو متقطع
**الأعراض**: استجابة بطيئة، انقطاع متكرر في الاتصال

**الحلول**:
1. **تحقق من جودة الشبكة**: استخدم كابل Cat5e أو أفضل
2. **تقليل المسافة**: قرب الجهاز من الراوتر
3. **تحديث البرنامج**: حدث firmware الجهاز
4. **تغيير المنفذ**: جرب منفذ مختلف في الراوتر

### المشكلة 3: فشل في المزامنة
**الأعراض**: الاتصال يعمل لكن البيانات لا تنتقل

**الحلول**:
1. **تحقق من الصلاحيات**: تأكد من صلاحيات الوصول للجهاز
2. **تحقق من الإعدادات**: راجع إعدادات الجهاز
3. **مسح الذاكرة**: امسح البيانات القديمة من الجهاز
4. **إعادة التكوين**: أعد تكوين الجهاز من البداية

### المشكلة 4: عدم قراءة البصمات
**الأعراض**: الجهاز متصل لكن لا يقرأ البصمات

**الحلول**:
1. **تنظيف الحساس**: نظف سطح قراءة البصمة
2. **إعادة تسجيل**: أعد تسجيل البصمات المتأثرة
3. **تحديث قاعدة البيانات**: حدث قاعدة بيانات البصمات
4. **فحص الحساس**: تأكد من سلامة حساس البصمة

---

## 🛠️ الصيانة والمتابعة

### الصيانة اليومية
- **تنظيف الحساس**: امسح سطح قراءة البصمة يومياً
- **فحص الاتصال**: تأكد من استقرار الاتصال
- **مراجعة السجلات**: راجع سجلات الحضور والانصراف

### الصيانة الأسبوعية
- **نسخ احتياطي**: اعمل نسخة احتياطية من البيانات
- **تحديث الوقت**: تأكد من دقة الوقت والتاريخ
- **فحص الذاكرة**: تحقق من مساحة الذاكرة المتاحة

### الصيانة الشهرية
- **تحديث البرنامج**: حدث firmware إذا توفر إصدار جديد
- **فحص شامل**: اختبر جميع الوظائف
- **تنظيف عميق**: نظف الجهاز من الداخل والخارج
- **فحص الكوابل**: تحقق من سلامة جميع التوصيلات

### المراقبة المستمرة
- **تنبيهات النظام**: فعل تنبيهات انقطاع الاتصال
- **تقارير الأداء**: راجع تقارير أداء الأجهزة
- **سجلات الأخطاء**: تابع سجلات الأخطاء والمشاكل

---

## 📞 الدعم الفني

### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX
- **ساعات العمل**: الأحد - الخميس، 8:00 ص - 5:00 م

### قبل الاتصال بالدعم
1. جرب الحلول المذكورة في هذا الدليل
2. اجمع المعلومات التالية:
   - موديل الجهاز
   - رقم الإصدار
   - وصف المشكلة
   - الخطوات المجربة
   - رسائل الخطأ (إن وجدت)

---

## 📚 مراجع إضافية

### أدلة الشركات المصنعة
- [ZKTeco User Manual](https://www.zkteco.com)
- [Hikvision Documentation](https://www.hikvision.com)
- [Suprema Support](https://www.supremainc.com)

### أدوات مفيدة
- **Advanced IP Scanner**: لفحص الشبكة
- **Wireshark**: لتحليل حركة الشبكة
- **PuTTY**: للاتصال عبر Telnet/SSH

---

*آخر تحديث: يناير 2024*
*الإصدار: 1.0*
