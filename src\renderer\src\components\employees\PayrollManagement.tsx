﻿import React, { useState, useEffect } from 'react'
import * as XLSX from 'xlsx'
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form, Select, Row,
  Col,
  Typography,
  Statistic,
  Tag,
  Tooltip, Divider, message} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  DollarOutlined, EditOutlined,
  CheckCircleOutlined, CalculatorOutlined,
  BankOutlined,
  PrinterOutlined,
  FileExcelOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select

interface PayrollRecord {
  id: number
  employee_id: number
  employee_code: string
  employee_name: string
  position_name: string
  department_name: string
  pay_period_start: string
  pay_period_end: string
  month: number
  year: number
  basic_salary: number
  housing_allowance: number
  transport_allowance: number
  other_allowances: number
  overtime_amount: number
  allowances: number
  bonuses: number
  commissions: number
  gross_salary: number
  social_insurance: number
  income_tax: number
  tax_deduction: number
  insurance_deduction: number
  absence_deduction: number
  other_deductions: number
  total_deductions: number
  net_salary: number
  payment_date?: string
  payment_method: string
  payment_reference?: string
  status: string
  calculated_at: string
  paid_at: string | null
  calculated_by_name?: string
  approved_by_name?: string
  paid_by_name?: string
  created_at: string
  updated_at: string
}

interface Employee {
  id: number
  employee_code: string
  full_name: string
  department_name: string
  basic_salary: number
  is_active: number
  status?: string // للتوافق مع البيانات الوهمية
}

const PayrollManagement: React.FC = () => {
  const [payrollRecords, setPayrollRecords] = useState<PayrollRecord[]>([])
  const [employees, setEmployees] = useState<Employee[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [calculationModalVisible, setCalculationModalVisible] = useState(false)
  const [editingRecord, setEditingRecord] = useState<PayrollRecord | null>(null)
  const [filters, setFilters] = useState<any>({})
  const [calculationForm] = Form.useForm()

  useEffect(() => {
    fetchPayrollRecords()
    fetchEmployees()
  }, [])

  const fetchPayrollRecords = async (filterParams?: any) => {
    setLoading(true)
    try {
      if (!window.electronAPI) {
        Logger.error('PayrollManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('PayrollManagement', 'âڑ ï¸ڈ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للرواتب
        const mockPayrollRecords = [
          {
            id: 1,
            employee_id: 1,
            employee_name: 'أحمد محمد علٍ',
            employee_code: 'EMP001',
            department_name: 'قسم الموارد البشرية',
            basic_salary: 6000,
            total_salary: 6500,
            month: new Date().getMonth() + 1,
            year: new Date().getFullYear(),
            status: 'calculated'
          }
        ]

        setPayrollRecords(mockPayrollRecords as any[])
        Logger.info('PayrollManagement', '✅ تم تحميل ${mockPayrollRecords.length} سجل راتب وهمي للمتصفح')
        return
      }

      const result = await window.electronAPI.getEmployeePayroll(filterParams || filters)
      if (result.success) {
        setPayrollRecords(result.data)
      } else {
        message.error(result.message)
      }
    } catch (_error) {
      message.error('فشل في جلب بيانات الرواتب')
    } finally {
      setLoading(false)
    }
  }

  const fetchEmployees = async () => {
    try {
      Logger.info('PayrollManagement', 'ًں”„ جارٍ جلب بيانات الموظفيْ...')

      if (!window.electronAPI) {
        Logger.error('PayrollManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('PayrollManagement', 'âڑ ï¸ڈ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للموظفيْ
        const mockEmployees = [
          {
            id: 1,
            employee_code: 'EMP001',
            full_name: 'أحمد محمد علٍ',
            department_name: 'قسم الموارد البشرية',
            basic_salary: 6000,
            status: 'active'
          },
          {
            id: 2,
            employee_code: 'EMP002',
            full_name: 'فاطمة عبدالله الزهراٍْ',
            department_name: 'قسم المالية والمحاسبة',
            basic_salary: 7000,
            status: 'active'
          }
        ]

        setEmployees(mockEmployees as any[])
        Logger.info('PayrollManagement', '✅ تم تحميل ${mockEmployees.length} موظف وهمي للمتصفح')
        return
      }

      const result = await window.electronAPI.getEmployees()
      Logger.info('PayrollManagement', 'ًں“ٹ ْتٍجة جلب الموظفيْ:', result)

      if (result && result.success) {
        // تصحٍح اسم الخاصٍة من status إلى is_active
        const activeEmployees = result.data.filter((emp: any) => emp.is_active === 1)
        setEmployees(activeEmployees)
        Logger.info('PayrollManagement', '✅ تم جلب ${activeEmployees.length} موظف ْشط بنجاح')
      } else {
        Logger.error('PayrollManagement', '❌ فشل في جلب الموظفيْ:', result?.message)
        setEmployees([])
      }
    } catch (_error) {
      Logger.error('PayrollManagement', 'فشل في جلب الموظفين:', _error)
      setEmployees([])
    }
  }

  const handleCalculatePayroll = () => {
    calculationForm.resetFields()
    calculationForm.setFieldsValue({
      month: dayjs().month() + 1,
      year: dayjs().year()
    })
    setCalculationModalVisible(true)
  }

  const handleCalculationSubmit = async (values: any) => {
    try {
      if (!window.electronAPI) {
        Logger.error('PayrollManagement', '❌ window.electronAPI غير متوفر')
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن حساب الرواتب')
        return
      }

      const payrollData = {
        ...values,
        calculated_by: 1 // ٍجب الحصول على معرف المستخدم الحالٍ
      }

      const result = await window.electronAPI.calculateEmployeePayroll(payrollData)
      if (result.success) {
        message.success('تم حساب الراتب بنجاح')
        setCalculationModalVisible(false)
        calculationForm.resetFields()
        fetchPayrollRecords()
      } else {
        message.error(result.message)
      }
    } catch (_error) {
      message.error('فشل في حساب الراتب')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'default'
      case 'calculated': return 'processing'
      case 'approved': return 'warning'
      case 'paid': return 'success'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'draft': return 'مسودة'
      case 'calculated': return 'محسوب'
      case 'approved': return 'معتمد'
      case 'paid': return 'مدفوع'
      default: return status
    }
  }

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'bank_transfer': return 'تحوٍل بْكٍ'
      case 'cash': return 'ْقداً'
      case 'check': return 'شٍك'
      default: return method
    }
  }

  // دالة طباعة كشف راتب فردي
  const handlePrintPayroll = async (record: PayrollRecord) => {
    try {
      // استخدام طباعة مبسطة مع تصميم احترافي لكشف الراتب
      const printContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <title>كشف راتب - ${record.employee_name}</title>
          <style>
            @page { size: A4; margin: 1cm; }
            body {
              font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
              direction: rtl;
              margin: 0;
              padding: 20px;
              color: #333;
              line-height: 1.6;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 3px solid #1890ff;
              padding-bottom: 20px;
            }
            .header h1 {
              color: #1890ff;
              margin: 0;
              font-size: 28px;
              font-weight: bold;
            }
            .header .subtitle {
              color: #666;
              margin-top: 10px;
              font-size: 16px;
            }
            .employee-info {
              background: #f8f9fa;
              padding: 20px;
              border-radius: 8px;
              margin-bottom: 20px;
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 15px;
            }
            .info-item {
              display: flex;
              justify-content: space-between;
              padding: 8px 0;
              border-bottom: 1px solid #ddd;
            }
            .info-label {
              font-weight: bold;
              color: #666;
            }
            .info-value {
              color: #333;
            }
            .salary-details {
              margin: 20px 0;
            }
            .salary-section {
              background: white;
              border: 1px solid #ddd;
              border-radius: 8px;
              margin-bottom: 15px;
              overflow: hidden;
            }
            .section-header {
              background: #1890ff;
              color: white;
              padding: 12px 20px;
              font-weight: bold;
              font-size: 16px;
            }
            .section-content {
              padding: 15px 20px;
            }
            .salary-item {
              display: flex;
              justify-content: space-between;
              padding: 8px 0;
              border-bottom: 1px solid #f0f0f0;
            }
            .salary-item:last-child {
              border-bottom: none;
              font-weight: bold;
              font-size: 18px;
              color: #1890ff;
              border-top: 2px solid #1890ff;
              padding-top: 12px;
              margin-top: 8px;
            }
            .amount {
              font-weight: bold;
              color: #52c41a;
            }
            .deduction {
              color: #ff4d4f;
            }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 10px;
              color: #666;
              border-top: 1px solid #ddd;
              padding-top: 10px;
            }
            @media print {
              body { -webkit-print-color-adjust: exact; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>كشف راتب</h1>
            <div class="subtitle">
              الشهر: ${record.month}/${record.year} |
              تاريخ الإصدار: ${new Date().toLocaleDateString('ar-SA')}
            </div>
          </div>

          <div class="employee-info">
            <div class="info-item">
              <span class="info-label">اسم الموظف:</span>
              <span class="info-value">${record.employee_name}</span>
            </div>
            <div class="info-item">
              <span class="info-label">كود الموظف:</span>
              <span class="info-value">${record.employee_code}</span>
            </div>
            <div class="info-item">
              <span class="info-label">القسم:</span>
              <span class="info-value">${record.department_name || '-'}</span>
            </div>
            <div class="info-item">
              <span class="info-label">المنصب:</span>
              <span class="info-value">${record.position_name || '-'}</span>
            </div>
            <div class="info-item">
              <span class="info-label">الحالة:</span>
              <span class="info-value">${getStatusText(record.status)}</span>
            </div>
            <div class="info-item">
              <span class="info-label">طريقة الدفع:</span>
              <span class="info-value">${getPaymentMethodText(record.payment_method)}</span>
            </div>
          </div>

          <div class="salary-details">
            <div class="salary-section">
              <div class="section-header">الراتب الأساسي والبدلات</div>
              <div class="section-content">
                <div class="salary-item">
                  <span>الراتب الأساسي</span>
                  <span class="amount">${(record.basic_salary || 0).toLocaleString()} ريال</span>
                </div>
                <div class="salary-item">
                  <span>بدل السكن</span>
                  <span class="amount">${(record.housing_allowance || 0).toLocaleString()} ريال</span>
                </div>
                <div class="salary-item">
                  <span>بدل النقل</span>
                  <span class="amount">${(record.transport_allowance || 0).toLocaleString()} ريال</span>
                </div>
                <div class="salary-item">
                  <span>البدلات الأخرى</span>
                  <span class="amount">${(record.other_allowances || 0).toLocaleString()} ريال</span>
                </div>
                <div class="salary-item">
                  <span>الساعات الإضافية</span>
                  <span class="amount">${(record.overtime_amount || 0).toLocaleString()} ريال</span>
                </div>
              </div>
            </div>

            <div class="salary-section">
              <div class="section-header">الخصومات</div>
              <div class="section-content">
                <div class="salary-item">
                  <span>التأمينات الاجتماعية</span>
                  <span class="deduction">${(record.social_insurance || 0).toLocaleString()} ريال</span>
                </div>
                <div class="salary-item">
                  <span>ضريبة الدخل</span>
                  <span class="deduction">${(record.income_tax || 0).toLocaleString()} ريال</span>
                </div>
                <div class="salary-item">
                  <span>خصومات أخرى</span>
                  <span class="deduction">${(record.other_deductions || 0).toLocaleString()} ريال</span>
                </div>
                <div class="salary-item">
                  <span>خصم الغياب</span>
                  <span class="deduction">${(record.absence_deduction || 0).toLocaleString()} ريال</span>
                </div>
              </div>
            </div>

            <div class="salary-section">
              <div class="section-header">الإجمالي</div>
              <div class="section-content">
                <div class="salary-item">
                  <span>إجمالي الاستحقاقات</span>
                  <span class="amount">${(record.gross_salary || 0).toLocaleString()} ريال</span>
                </div>
                <div class="salary-item">
                  <span>إجمالي الخصومات</span>
                  <span class="deduction">${(record.total_deductions || 0).toLocaleString()} ريال</span>
                </div>
                <div class="salary-item">
                  <span>صافي الراتب</span>
                  <span class="amount">${(record.net_salary || 0).toLocaleString()} ريال</span>
                </div>
              </div>
            </div>
          </div>

          <div class="footer">
            تم إنشاء هذا الكشف بواسطة نظام ZET.IA للمحاسبة والإنتاج<br>
            هذا المستند سري ومخصص للموظف المذكور أعلاه فقط
          </div>
        </body>
        </html>
      `

      const printWindow = window.open('', '_blank')
      if (printWindow) {
        const printDoc = (printWindow as any).document
        if (printDoc) {
          printDoc.write(printContent)
          printDoc.close()
        }
        printWindow.print()
        message.success('تم إرسال كشف الراتب للطباعة')
      }
    } catch (_error) {
      Logger.error('PayrollManagement', 'خطأ في طباعة كشف الراتب:', _error)
      message.error('فشل في طباعة كشف الراتب')
    }
  }

  // دالة طباعة تقرير شامل للرواتب
  const handlePrintAllPayrolls = async () => {
    try {
      if (!payrollRecords || payrollRecords.length === 0) {
        message.warning('لا توجد سجلات رواتب للطباعة')
        return
      }

      const printContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <title>تقرير الرواتب الشامل</title>
          <style>
            @page { size: A4 landscape; margin: 1cm; }
            body {
              font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
              direction: rtl;
              margin: 0;
              padding: 20px;
              color: #333;
              line-height: 1.6;
              font-size: 12px;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 3px solid #1890ff;
              padding-bottom: 20px;
            }
            .header h1 {
              color: #1890ff;
              margin: 0;
              font-size: 24px;
              font-weight: bold;
            }
            .statistics {
              display: grid;
              grid-template-columns: repeat(4, 1fr);
              gap: 15px;
              margin-bottom: 30px;
              padding: 20px;
              background: #f8f9fa;
              border-radius: 8px;
            }
            .stat-item {
              text-align: center;
              padding: 10px;
              background: white;
              border-radius: 6px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .stat-value {
              font-size: 18px;
              font-weight: bold;
              color: #1890ff;
            }
            .stat-label {
              font-size: 10px;
              color: #666;
              margin-top: 5px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 20px;
              font-size: 10px;
              background: white;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 6px;
              text-align: center;
            }
            th {
              background: #1890ff;
              color: white;
              font-weight: bold;
            }
            tr:nth-child(even) {
              background: #f9f9f9;
            }
            .amount {
              font-weight: bold;
              color: #52c41a;
            }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 10px;
              color: #666;
              border-top: 1px solid #ddd;
              padding-top: 10px;
            }
            @media print {
              body { -webkit-print-color-adjust: exact; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>تقرير الرواتب الشامل</h1>
            <div class="subtitle">
              تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')} |
              إجمالي السجلات: ${payrollRecords.length}
            </div>
          </div>

          <div class="statistics">
            <div class="stat-item">
              <div class="stat-value">${payrollRecords.length}</div>
              <div class="stat-label">إجمالي السجلات</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">${payrollRecords.reduce((sum, r) => sum + (r.gross_salary || 0), 0).toLocaleString()}</div>
              <div class="stat-label">إجمالي الرواتب الإجمالية</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">${payrollRecords.reduce((sum, r) => sum + (r.total_deductions || 0), 0).toLocaleString()}</div>
              <div class="stat-label">إجمالي الخصومات</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">${payrollRecords.reduce((sum, r) => sum + (r.net_salary || 0), 0).toLocaleString()}</div>
              <div class="stat-label">إجمالي الرواتب الصافية</div>
            </div>
          </div>

          <table>
            <thead>
              <tr>
                <th>الموظف</th>
                <th>الشهر/السنة</th>
                <th>الراتب الأساسي</th>
                <th>البدلات</th>
                <th>الإجمالي</th>
                <th>الخصومات</th>
                <th>الصافي</th>
                <th>الحالة</th>
                <th>طريقة الدفع</th>
              </tr>
            </thead>
            <tbody>
              ${payrollRecords.map(record => `
                <tr>
                  <td>${record.employee_name}</td>
                  <td>${record.month}/${record.year}</td>
                  <td class="amount">${(record.basic_salary || 0).toLocaleString()}</td>
                  <td class="amount">${((record.housing_allowance || 0) + (record.transport_allowance || 0) + (record.other_allowances || 0)).toLocaleString()}</td>
                  <td class="amount">${(record.gross_salary || 0).toLocaleString()}</td>
                  <td style="color: #ff4d4f;">${(record.total_deductions || 0).toLocaleString()}</td>
                  <td class="amount">${(record.net_salary || 0).toLocaleString()}</td>
                  <td>${getStatusText(record.status)}</td>
                  <td>${getPaymentMethodText(record.payment_method)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="footer">
            تم إنشاء هذا التقرير بواسطة نظام ZET.IA للمحاسبة والإنتاج
          </div>
        </body>
        </html>
      `

      const printWindow = window.open('', '_blank')
      if (printWindow) {
        const printDoc = (printWindow as any).document
        if (printDoc) {
          printDoc.write(printContent)
          printDoc.close()
        }
        printWindow.print()
        message.success('تم إرسال التقرير للطباعة')
      }
    } catch (_error) {
      Logger.error('PayrollManagement', 'خطأ في طباعة التقرير:', _error)
      message.error('فشل في طباعة التقرير')
    }
  }

  // دالة تصدير Excel للرواتب
  const handleExportPayrolls = async () => {
    try {
      if (!payrollRecords || payrollRecords.length === 0) {
        message.warning('لا توجد سجلات رواتب للتصدير')
        return
      }

      const { MasterPrintService } = await import('../../services/MasterPrintService')

      const exportData = payrollRecords.map(record => ({
        'كود الموظف': record.employee_code,
        'اسم الموظف': record.employee_name,
        'القسم': record.department_name || '-',
        'المنصب': record.position_name || '-',
        'الشهر': record.month,
        'السنة': record.year,
        'الراتب الأساسي': record.basic_salary || 0,
        'بدل السكن': record.housing_allowance || 0,
        'بدل النقل': record.transport_allowance || 0,
        'البدلات الأخرى': record.other_allowances || 0,
        'الساعات الإضافية': record.overtime_amount || 0,
        'الراتب الإجمالي': record.gross_salary || 0,
        'التأمينات الاجتماعية': record.social_insurance || 0,
        'ضريبة الدخل': record.income_tax || 0,
        'خصومات أخرى': record.other_deductions || 0,
        'خصم الغياب': record.absence_deduction || 0,
        'إجمالي الخصومات': record.total_deductions || 0,
        'الراتب الصافي': record.net_salary || 0,
        'الحالة': getStatusText(record.status),
        'طريقة الدفع': getPaymentMethodText(record.payment_method),
        'تاريخ الحساب': record.calculated_at ? dayjs(record.calculated_at).format('YYYY-MM-DD') : '-',
        'تاريخ الدفع': record.paid_at ? dayjs(record.paid_at).format('YYYY-MM-DD') : '-'
      }))

      // تصدير إلى Excel باستخدام XLSX
      const ws = XLSX.utils.json_to_sheet(exportData)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, 'الرواتب')
      XLSX.writeFile(wb, 'تقرير_الرواتب_الشامل.xlsx')
      message.success(`تم تصدير ${exportData.length} سجل راتب بنجاح`)
    } catch (_error) {
      Logger.error('PayrollManagement', 'خطأ في تصدير الرواتب:', _error)
      message.error('فشل في تصدير البيانات')
    }
  }

  const columns: ColumnsType<PayrollRecord> = [
    {
      title: 'الموظف',
      key: 'employee',
      width: 200,
      fixed: 'left',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.employee_name}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.employee_code} - {record.department_name}
          </Text>
        </div>
      )
    },
    {
      title: 'الفترة',
      key: 'period',
      width: 120,
      render: (_, record) => (
        <div>
          <div>{record.month}/{record.year}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {dayjs(record.pay_period_start).format('DD/MM')} - {dayjs(record.pay_period_end).format('DD/MM')}
          </Text>
        </div>
      )
    },
    {
      title: 'الراتب الأساسي',
      dataIndex: 'basic_salary',
      key: 'basic_salary',
      width: 120,
      render: (value) => (
        <Space>
          <DollarOutlined />
          {value?.toLocaleString() || '0'} ₪
        </Space>
      )
    },
    {
      title: 'الساعات الإضافية',
      dataIndex: 'overtime_amount',
      key: 'overtime_amount',
      width: 120,
      render: (value) => value > 0 ? (
        <Tag color="orange">
          {value.toLocaleString()} ₪
        </Tag>
      ) : '-'
    },
    {
      title: 'البدلات',
      dataIndex: 'allowances',
      key: 'allowances',
      width: 100,
      render: (value) => value > 0 ? (
        <Tag color="blue">
          {value.toLocaleString()} ₪
        </Tag>
      ) : '-'
    },
    {
      title: 'الإجمالي',
      dataIndex: 'gross_salary',
      key: 'gross_salary',
      width: 120,
      render: (value) => (
        <Space>
          <strong>{value?.toLocaleString() || '0'} ₪</strong>
        </Space>
      )
    },
    {
      title: 'الخصومات',
      dataIndex: 'total_deductions',
      key: 'total_deductions',
      width: 120,
      render: (value) => value > 0 ? (
        <Tag color="red">
          -{value.toLocaleString()} ₪
        </Tag>
      ) : '-'
    },
    {
      title: 'الصافي',
      dataIndex: 'net_salary',
      key: 'net_salary',
      width: 120,
      render: (value) => (
        <Space>
          <strong style={{ color: '#52c41a' }}>
            {value?.toLocaleString() || '0'} ₪
          </strong>
        </Space>
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'طرٍقة الدفع',
      dataIndex: 'payment_method',
      key: 'payment_method',
      width: 120,
      render: (method, record) => record.status === 'paid' ? (
        <Space>
          <BankOutlined />
          {getPaymentMethodText(method)}
        </Space>
      ) : '-'
    },
    {
      title: 'الإجراط،ات',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Tooltip title="عرض التفاصٍل">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingRecord(record)
                setModalVisible(true)
              }}
            />
          </Tooltip>
          <Tooltip title="طباعة">
            <Button
              size="small"
              icon={<PrinterOutlined />}
              onClick={() => handlePrintPayroll(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const totalGrossSalary = payrollRecords.reduce((sum, record) => sum + record.gross_salary, 0)
  const totalNetSalary = payrollRecords.reduce((sum, record) => sum + record.net_salary, 0)
  const totalDeductions = payrollRecords.reduce((sum, record) => sum + record.total_deductions, 0)
  const paidRecords = payrollRecords.filter(record => record.status === 'paid').length

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <DollarOutlined /> إدارة الرواتب
      </Title>
      
      {/* إحصائيات سرٍعة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="إجمالي الرواتب الإجمالية"
              value={totalGrossSalary}
              prefix={<DollarOutlined />}
              suffix="₪"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="إجمالي الرواتب الصافية"
              value={totalNetSalary}
              prefix={<DollarOutlined />}
              suffix="₪"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="إجمالي الخصومات"
              value={totalDeductions}
              prefix={<DollarOutlined />}
              suffix="₪"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="الرواتب المدفوعة"
              value={paidRecords}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* فلاتر البحث */}
      <Card style={{ marginBottom: '16px' }}>
        <Form layout="inline" onFinish={(values) => {
          setFilters(values)
          fetchPayrollRecords(values)
        }}>
          <Form.Item name="employee_id" label="الموظف">
            <Select placeholder="اختر الموظف" style={{ width: 200 }} allowClear>
              {employees.map(emp => (
                <Option key={emp.id} value={emp.id}>
                  {emp.full_name} - {emp.employee_code}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="month" label="الشهر">
            <Select placeholder="اختر الشهر" style={{ width: 120 }} allowClear>
              {Array.from({ length: 12 }, (_, i) => (
                <Option key={i + 1} value={i + 1}>
                  {dayjs().month(i).format('MMMM')}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="year" label="السْة">
            <Select placeholder="اختر السْة" style={{ width: 100 }} allowClear>
              {Array.from({ length: 5 }, (_, i) => {
                const year = dayjs().year() - 2 + i
                return (
                  <Option key={year} value={year}>
                    {year}
                  </Option>
                )
              })}
            </Select>
          </Form.Item>

          <Form.Item name="status" label="الحالة">
            <Select placeholder="اختر الحالة" style={{ width: 120 }} allowClear>
              <Option value="draft">مسودة</Option>
              <Option value="calculated">محسوب</Option>
              <Option value="approved">معتمد</Option>
              <Option value="paid">مدفوع</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit">
              بحث
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* جدول الرواتب */}
      <Card
        title={
          <Space>
            <DollarOutlined />
            <span>سجلات الرواتب</span>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<FileExcelOutlined />}
              onClick={handleExportPayrolls}
              disabled={payrollRecords.length === 0}
            >
              تصدير Excel
            </Button>
            <Button
              icon={<PrinterOutlined />}
              onClick={handlePrintAllPayrolls}
              disabled={payrollRecords.length === 0}
            >
              طباعة التقرير
            </Button>
            <Button
              type="primary"
              icon={<CalculatorOutlined />}
              onClick={handleCalculatePayroll}
            >
              حساب راتب
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={payrollRecords}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} سجل`
          }}
        />
      </Card>

      {/* نافذة حساب الراتب */}
      <Modal
        title="حساب راتب موظف"
        open={calculationModalVisible}
        onCancel={() => {
          setCalculationModalVisible(false)
          calculationForm.resetFields()
        }}
        footer={null}
        width={600}
        destroyOnHidden
      >
        <Form
          form={calculationForm}
          layout="vertical"
          onFinish={handleCalculationSubmit}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="employee_id"
                label="الموظف"
                rules={[{ required: true, message: 'ٍرجى اختٍار الموظف' }]}
              >
                <Select placeholder="اختر الموظف" showSearch>
                  {employees.map(emp => (
                    <Option key={emp.id} value={emp.id}>
                      <div>
                        <div>{emp.full_name} - {emp.employee_code}</div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {emp.department_name} - راتب أساسٍ: {emp.basic_salary?.toLocaleString()} ₪
                        </Text>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="month"
                label="الشهر"
                rules={[{ required: true, message: 'ٍرجى اختٍار الشهر' }]}
              >
                <Select>
                  {Array.from({ length: 12 }, (_, i) => (
                    <Option key={i + 1} value={i + 1}>
                      {dayjs().month(i).format('MMMM')}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="year"
                label="السْة"
                rules={[{ required: true, message: 'ٍرجى اختٍار السْة' }]}
              >
                <Select>
                  {Array.from({ length: 5 }, (_, i) => {
                    const year = dayjs().year() - 2 + i
                    return (
                      <Option key={year} value={year}>
                        {year}
                      </Option>
                    )
                  })}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <Row justify="end">
            <Space>
              <Button onClick={() => setCalculationModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit" icon={<CalculatorOutlined />}>
                حساب الراتب
              </Button>
            </Space>
          </Row>
        </Form>
      </Modal>

      {/* نافذة عرض تفاصٍل الراتب */}
      <Modal
        title="تفاصٍل الراتب"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setEditingRecord(null)
        }}
        footer={null}
        width={800}
        destroyOnHidden
      >
        {editingRecord && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <Card title="معلومات الموظف" size="small">
                  <p><strong>الاسم:</strong> {editingRecord.employee_name}</p>
                  <p><strong>الكود:</strong> {editingRecord.employee_code}</p>
                  <p><strong>القسم:</strong> {editingRecord.department_name}</p>
                  <p><strong>الفترة:</strong> {editingRecord.month}/{editingRecord.year}</p>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="معلومات الدفع" size="small">
                  <p><strong>الحالة:</strong> <Tag color={getStatusColor(editingRecord.status)}>{getStatusText(editingRecord.status)}</Tag></p>
                  <p><strong>تارٍخ الدفع:</strong> {editingRecord.payment_date ? dayjs(editingRecord.payment_date).format('YYYY-MM-DD') : '-'}</p>
                  <p><strong>طرٍقة الدفع:</strong> {getPaymentMethodText(editingRecord.payment_method)}</p>
                  <p><strong>مرجع الدفع:</strong> {editingRecord.payment_reference || '-'}</p>
                </Card>
              </Col>
            </Row>

            <Divider />

            <Row gutter={16}>
              <Col span={12}>
                <Card title="الاستحقاقات" size="small">
                  <div style={{ marginBottom: '8px' }}>
                    <span>الراتب الأساسي: </span>
                    <strong>{editingRecord.basic_salary?.toLocaleString()} ₪</strong>
                  </div>
                  <div style={{ marginBottom: '8px' }}>
                    <span>الساعات الإضافية: </span>
                    <strong>{editingRecord.overtime_amount?.toLocaleString()} ₪</strong>
                  </div>
                  <div style={{ marginBottom: '8px' }}>
                    <span>البدلات: </span>
                    <strong>{editingRecord.allowances?.toLocaleString()} ₪</strong>
                  </div>
                  <div style={{ marginBottom: '8px' }}>
                    <span>المكافآت: </span>
                    <strong>{editingRecord.bonuses?.toLocaleString()} ₪</strong>
                  </div>
                  <div style={{ marginBottom: '8px' }}>
                    <span>العمولات: </span>
                    <strong>{editingRecord.commissions?.toLocaleString()} ₪</strong>
                  </div>
                  <Divider style={{ margin: '12px 0' }} />
                  <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                    <span>الإجمالي: </span>
                    <strong>{editingRecord.gross_salary?.toLocaleString()} ₪</strong>
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="الخصومات" size="small">
                  <div style={{ marginBottom: '8px' }}>
                    <span>الضرٍبة: </span>
                    <strong>{editingRecord.tax_deduction?.toLocaleString()} ₪</strong>
                  </div>
                  <div style={{ marginBottom: '8px' }}>
                    <span>التأمٍْ: </span>
                    <strong>{editingRecord.insurance_deduction?.toLocaleString()} ₪</strong>
                  </div>
                  <div style={{ marginBottom: '8px' }}>
                    <span>خصومات أخرى: </span>
                    <strong>{editingRecord.other_deductions?.toLocaleString()} ₪</strong>
                  </div>
                  <Divider style={{ margin: '12px 0' }} />
                  <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#ff4d4f' }}>
                    <span>إجمالي الخصومات: </span>
                    <strong>{editingRecord.total_deductions?.toLocaleString()} ₪</strong>
                  </div>
                  <Divider style={{ margin: '12px 0' }} />
                  <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#52c41a' }}>
                    <span>الصافي: </span>
                    <strong>{editingRecord.net_salary?.toLocaleString()} ₪</strong>
                  </div>
                </Card>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default PayrollManagement

