/**
 * ═══════════════════════════════════════════════════════════════════════════
 * 🖼️ SimpleImageManager - مكون موحد بسيط لإدارة جميع أنواع الصور
 * ═══════════════════════════════════════════════════════════════════════════
 * 
 * الهدف: استبدال جميع مكونات الصور المعقدة بمكون واحد بسيط وقابل لإعادة الاستخدام
 * 
 * الميزات:
 * ✅ رفع صور متعددة (Drag & Drop + Click)
 * ✅ عرض الصور في شبكة (Grid)
 * ✅ حذف الصور
 * ✅ تعيين صورة رئيسية
 * ✅ معاينة الصور (Gallery)
 * ✅ إضافة وصف للصور
 * ✅ دعم metadata مرن (للشيكات وغيرها)
 * 
 * الاستخدام:
 * ```tsx
 * // للأصناف
 * <SimpleImageManager category="item" contextId={itemId} maxImages={10} />
 * 
 * // لأوامر الإنتاج
 * <SimpleImageManager category="production" contextId={orderId} maxImages={5} />
 * 
 * // للشيكات (مع metadata)
 * <SimpleImageManager 
 *   category="check" 
 *   contextId={checkId}
 *   metadata={{ side: 'front', quality: 'high' }}
 * />
 * ```
 * 
 * @version 1.0.0
 * @created 2025-09-30
 * ═══════════════════════════════════════════════════════════════════════════
 */

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { Upload, Image as ImageIcon, X, Star, Eye, Trash2 } from 'lucide-react'
import { 
  simpleImageService, 
  ImageCategory, 
  UnifiedImage, 
  UploadImageOptions 
} from '../../services/SimpleImageService'
import { SafeLogger as Logger } from '../../utils/logger'

// ═══════════════════════════════════════════════════════════════════════════
// 📋 الواجهات
// ═══════════════════════════════════════════════════════════════════════════

export interface SimpleImageManagerProps {
  /** فئة الصورة */
  category: ImageCategory
  /** معرف السياق (item_id, order_id, check_id, etc.) */
  contextId: number
  /** الحد الأقصى للصور (افتراضي: 10) */
  maxImages?: number
  /** السماح بصور متعددة (افتراضي: true) */
  allowMultiple?: boolean
  /** إظهار حقل الوصف (افتراضي: true) */
  showDescription?: boolean
  /** إظهار زر الصورة الرئيسية (افتراضي: true) */
  showPrimaryButton?: boolean
  /** بيانات إضافية مرنة (للشيكات: {side: 'front', quality: 'high'}) */
  metadata?: Record<string, any>
  /** خيارات الرفع */
  uploadOptions?: UploadImageOptions
  /** callback عند تغيير الصور */
  onChange?: (images: UnifiedImage[]) => void
  /** CSS class إضافي */
  className?: string
}

// ═══════════════════════════════════════════════════════════════════════════
// 🎨 المكون الرئيسي
// ═══════════════════════════════════════════════════════════════════════════

export const SimpleImageManager: React.FC<SimpleImageManagerProps> = ({
  category,
  contextId,
  maxImages = 10,
  allowMultiple = true,
  showDescription = true,
  showPrimaryButton = true,
  metadata = {},
  uploadOptions = {},
  onChange,
  className = ''
}) => {
  // ═══════════════════════════════════════════════════════════════════════════
  // 🔧 الحالات (States)
  // ═══════════════════════════════════════════════════════════════════════════

  const [images, setImages] = useState<UnifiedImage[]>([])
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedImage, setSelectedImage] = useState<UnifiedImage | null>(null)
  const [isDragging, setIsDragging] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)

  // ═══════════════════════════════════════════════════════════════════════════
  // 📥 تحميل الصور
  // ═══════════════════════════════════════════════════════════════════════════

  const loadImages = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const result = await simpleImageService.getImages(category, contextId)

      if (result.success) {
        const imageList = (result.data || []) as UnifiedImage[]
        // ترتيب حسب sort_order ثم created_at
        imageList.sort((a, b) => {
          if (a.sort_order !== b.sort_order) {
            return a.sort_order - b.sort_order
          }
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        })
        
        setImages(imageList)
        onChange?.(imageList)
      } else {
        throw new Error(result.error || 'فشل في تحميل الصور')
      }
    } catch (error: any) {
      Logger.error('SimpleImageManager', 'خطأ في تحميل الصور:', error)
      setError(error.message || 'حدث خطأ في تحميل الصور')
    } finally {
      setLoading(false)
    }
  }, [category, contextId, onChange])

  useEffect(() => {
    if (contextId > 0) {
      loadImages()
    }
  }, [contextId, loadImages])

  // ═══════════════════════════════════════════════════════════════════════════
  // 📤 رفع الصور
  // ═══════════════════════════════════════════════════════════════════════════

  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length === 0) return

    await uploadFiles(files)

    // مسح اختيار الملف
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  const uploadFiles = useCallback(async (files: File[]) => {
    try {
      // التحقق من الحد الأقصى
      if (!allowMultiple && files.length > 1) {
        setError('يمكن رفع صورة واحدة فقط')
        return
      }

      if (images.length + files.length > maxImages) {
        setError(`الحد الأقصى للصور هو ${maxImages}`)
        return
      }

      setUploading(true)
      setError(null)

      // دمج metadata مع uploadOptions
      const options: UploadImageOptions = {
        ...uploadOptions,
        metadata: { ...metadata, ...uploadOptions.metadata }
      }

      // رفع الصور
      const result = await simpleImageService.uploadMultipleImages(
        files,
        category,
        contextId,
        options
      )

      if (result.success) {
        await loadImages()
      } else {
        setError(result.error || 'فشل في رفع الصور')
      }

      if (result.warnings && result.warnings.length > 0) {
        console.warn('تحذيرات الرفع:', result.warnings)
      }
    } catch (error: any) {
      Logger.error('SimpleImageManager', 'خطأ في رفع الصور:', error)
      setError(error.message || 'حدث خطأ في رفع الصور')
    } finally {
      setUploading(false)
    }
  }, [allowMultiple, images.length, maxImages, category, contextId, metadata, uploadOptions, loadImages])

  // ═══════════════════════════════════════════════════════════════════════════
  // 🗑️ حذف صورة
  // ═══════════════════════════════════════════════════════════════════════════

  const handleDeleteImage = useCallback(async (imageId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه الصورة؟')) return

    try {
      setLoading(true)
      const result = await simpleImageService.deleteImage(imageId)

      if (result.success) {
        await loadImages()
      } else {
        setError(result.error || 'فشل في حذف الصورة')
      }
    } catch (error: any) {
      Logger.error('SimpleImageManager', 'خطأ في حذف الصورة:', error)
      setError(error.message || 'حدث خطأ في حذف الصورة')
    } finally {
      setLoading(false)
    }
  }, [loadImages])

  // ═══════════════════════════════════════════════════════════════════════════
  // ⭐ تعيين صورة رئيسية
  // ═══════════════════════════════════════════════════════════════════════════

  const handleSetPrimary = useCallback(async (imageId: string) => {
    try {
      setLoading(true)
      const result = await simpleImageService.setPrimaryImage(imageId, category, contextId)

      if (result.success) {
        await loadImages()
      } else {
        setError(result.error || 'فشل في تعيين الصورة الرئيسية')
      }
    } catch (error: any) {
      Logger.error('SimpleImageManager', 'خطأ في تعيين الصورة الرئيسية:', error)
      setError(error.message || 'حدث خطأ في تعيين الصورة الرئيسية')
    } finally {
      setLoading(false)
    }
  }, [category, contextId, loadImages])

  // ═══════════════════════════════════════════════════════════════════════════
  // 🖱️ Drag & Drop
  // ═══════════════════════════════════════════════════════════════════════════

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const files = Array.from(e.dataTransfer.files).filter(file => 
      file.type.startsWith('image/')
    )

    if (files.length > 0) {
      await uploadFiles(files)
    }
  }, [uploadFiles])

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  // ═══════════════════════════════════════════════════════════════════════════
  // 🎨 العرض (Render)
  // ═══════════════════════════════════════════════════════════════════════════

  return (
    <div className={`simple-image-manager ${className}`}>
      {/* رسالة الخطأ */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* منطقة الرفع */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer mb-4 ${
          isDragging 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple={allowMultiple}
          onChange={handleFileSelect}
          className="hidden"
        />

        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-2" />
        <p className="text-sm text-gray-600">
          {uploading ? '⏳ جاري الرفع...' : 'اسحب الصور هنا أو انقر للاختيار'}
        </p>
        <p className="text-xs text-gray-500 mt-1">
          {allowMultiple 
            ? `الحد الأقصى: ${maxImages} صور` 
            : 'صورة واحدة فقط'}
        </p>
      </div>

      {/* عرض الصور */}
      {loading && images.length === 0 ? (
        <div className="text-center py-8 text-gray-500">⏳ جاري التحميل...</div>
      ) : images.length === 0 ? (
        <div className="text-center py-8 text-gray-400">
          <ImageIcon className="mx-auto h-16 w-16 mb-2" />
          <p>لا توجد صور</p>
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image) => (
            <div key={image.id} className="relative group">
              {/* الصورة */}
              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                <img
                  src={`file://${image.path}`}
                  alt={image.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7Zgdi02ZEg2YHZiiDYqtit2YXZitmEINin2YTYtdmI2LHYqTwvdGV4dD48L3N2Zz4='
                  }}
                />
              </div>

              {/* شارة الصورة الرئيسية */}
              {image.is_primary && (
                <div className="absolute top-2 right-2 bg-yellow-500 text-white rounded-full p-1">
                  <Star className="h-4 w-4" fill="currentColor" />
                </div>
              )}

              {/* أزرار التحكم */}
              <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                <div className="flex gap-2">
                  {/* معاينة */}
                  <button
                    onClick={() => setSelectedImage(image)}
                    className="p-2 bg-white text-gray-700 rounded-full hover:bg-gray-100"
                    title="معاينة"
                  >
                    <Eye className="h-4 w-4" />
                  </button>

                  {/* تعيين رئيسية */}
                  {showPrimaryButton && !image.is_primary && (
                    <button
                      onClick={() => handleSetPrimary(image.id)}
                      className="p-2 bg-white text-gray-700 rounded-full hover:bg-yellow-100"
                      title="تعيين كصورة رئيسية"
                    >
                      <Star className="h-4 w-4" />
                    </button>
                  )}

                  {/* حذف */}
                  <button
                    onClick={() => handleDeleteImage(image.id)}
                    className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600"
                    title="حذف"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* الوصف */}
              {showDescription && image.description && (
                <p className="text-xs text-gray-600 mt-1 truncate" title={image.description}>
                  {image.description}
                </p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* نافذة المعاينة */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl max-h-screen p-4">
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-6 right-6 bg-white text-gray-700 rounded-full p-2 hover:bg-gray-100"
            >
              <X className="h-6 w-6" />
            </button>
            <img
              src={`file://${selectedImage.path}`}
              alt={selectedImage.name}
              className="max-w-full max-h-screen object-contain"
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default SimpleImageManager

