import { SimpleLogger as Logger } from './logger'
// تم إزالة import message لتجنب static function warnings

// أنواع الأخطاء المختلفة
export enum ErrorType {
  VALIDATION = 'validation',
  NETWORK = 'network',
  DATABASE = 'database',
  PERMISSION = 'permission',
  BUSINESS_LOGIC = 'business_logic',
  UNKNOWN = 'unknown'
}

// واجهة للأخطاء المخصصة
export interface CustomError {
  type: ErrorType
  code?: string
  message: string
  details?: string
  field?: string
  suggestions?: string[]
}

// رسائل الأخطاء المحسنة
const ERROR_MESSAGES = {
  // أخطاء التحقق
  REQUIRED_FIELD: 'هذا الحقل مطلوب',
  INVALID_EMAIL: 'يرجى إدخال بريد إلكتروني صحيح',
  INVALID_PHONE: 'يرجى إدخال رقم هاتف صحيح',
  INVALID_NUMBER: 'يرجى إدخال رقم صحيح',
  DUPLICATE_CODE: 'هذا الكود موجود مسبقاً',
  DUPLICATE_NUMBER: 'هذا الرقم موجود مسبقاً',
  
  // أخطاء الشبكة
  NETWORK_ERROR: 'خطأ في الاتصال. يرجى التحقق من الاتصال بالإنترنت',
  SERVER_ERROR: 'خطأ في الخادم. يرجى المحاولة مرة أخرى',
  TIMEOUT_ERROR: 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى',
  
  // أخطاء قاعدة البيانات
  DATABASE_CONNECTION: 'لا يمكن الوصول إلى قاعدة البيانات',
  DATABASE_CONSTRAINT: 'خطأ في قيود قاعدة البيانات',
  DATABASE_TRANSACTION: 'فشل في تنفيذ العملية',
  
  // أخطاء الصلاحيات
  PERMISSION_DENIED: 'ليس لديك صلاحية لتنفيذ هذا الإجراء',
  LOGIN_REQUIRED: 'يجب تسجيل الدخول أولاً',
  
  // أخطاء منطق الأعمال
  INSUFFICIENT_INVENTORY: 'الكمية المطلوبة غير متوفرة في المخزن',
  INVALID_AMOUNT: 'المبلغ المدخل غير صحيح',
  INVALID_DATE: 'التاريخ المدخل غير صحيح',
  BUSINESS_RULE_VIOLATION: 'هذا الإجراء يخالف قواعد العمل'
}

// دالة لتحليل نوع الخطأ
export function analyzeError(error: any): CustomError {
  // إذا كان الخطأ من نوع CustomError مسبقاً
  if (error.type && error.message) {
    return error as CustomError
  }

  // تحليل رسالة الخطأ لتحديد النوع
  const errorMessage = error.message || error.toString() || 'خطأ غير معروف'

  // معالجة خاصة لأخطاء الإصدار
  if (errorMessage.includes('Cannot read properties of undefined (reading \'version\')') ||
      errorMessage.includes('version') && errorMessage.includes('undefined')) {
    // محاولة إصلاح مشكلة الإصدار
    try {
      if (typeof window !== 'undefined' && window.process) {
        if (!window.process.version) {
          (window.process as any).version = 'v18.0.0'
        }
        if (!window.process.versions) {
          (window.process as any).versions = {
            node: '18.0.0',
            v8: '10.0.0',
            uv: '1.0.0',
            zlib: '1.0.0',
            brotli: '1.0.0',
            ares: '1.0.0',
            modules: '108',
            nghttp2: '1.0.0',
            napi: '8',
            llhttp: '6.0.0',
            openssl: '3.0.0',
            cldr: '41.0',
            icu: '71.1',
            tz: '2022a',
            unicode: '14.0'
          }
        }
      }
    } catch (fixError) {
      Logger.error('ErrorHandler', 'ErrorHandler: فشل في إصلاح مشكلة الإصدار:', fixError)
    }

    return {
      type: ErrorType.UNKNOWN,
      code: 'VERSION_ERROR',
      message: 'تم إصلاح مشكلة الإصدار تلقائياً',
      details: errorMessage,
      suggestions: ['إذا استمر الخطأ، أعد تشغيل التطبيق']
    }
  }
  
  // أخطاء قاعدة البيانات
  if (errorMessage.includes('UNIQUE constraint failed') || errorMessage.includes('duplicate')) {
    return {
      type: ErrorType.DATABASE,
      code: 'DUPLICATE_ENTRY',
      message: 'البيانات المدخلة موجودة مسبقاً',
      details: errorMessage,
      suggestions: ['تحقق من البيانات المدخلة', 'استخدم قيم مختلفة']
    }
  }

  if (errorMessage.includes('FOREIGN KEY constraint failed')) {
    return {
      type: ErrorType.DATABASE,
      code: 'FOREIGN_KEY_ERROR',
      message: 'خطأ في ربط البيانات',
      details: errorMessage,
      suggestions: ['تأكد من صحة البيانات المرتبطة']
    }
  }

  // أخطاء الشبكة
  if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
    return {
      type: ErrorType.NETWORK,
      code: 'NETWORK_ERROR',
      message: ERROR_MESSAGES.NETWORK_ERROR,
      details: errorMessage,
      suggestions: ['تحقق من الاتصال بالإنترنت', 'أعد المحاولة']
    }
  }

  // أخطاء الصلاحيات
  if (errorMessage.includes('permission') || errorMessage.includes('unauthorized')) {
    return {
      type: ErrorType.PERMISSION,
      code: 'PERMISSION_DENIED',
      message: ERROR_MESSAGES.PERMISSION_DENIED,
      details: errorMessage,
      suggestions: ['تواصل مع المدير للحصول على الصلاحيات']
    }
  }

  // خطأ عام
  return {
    type: ErrorType.UNKNOWN,
    code: 'UNKNOWN_ERROR',
    message: errorMessage,
    details: errorMessage,
    suggestions: ['أعد المحاولة', 'تواصل مع الدعم الفني']
  }
}

// دالة لعرض الأخطاء بشكل محسن
export function displayError(error: any, context?: string) {
  const customError = analyzeError(error)
  
  let displayMessage = customError.message
  
  if (context) {
    displayMessage = `${context}: ${displayMessage}`
  }

  // طباعة الرسالة في الكونسول (سيتم عرضها بواسطة المكونات باستخدام App.useApp())
  Logger.warn('ErrorHandler', `${customError.type.toUpperCase()}: ${displayMessage}`)

  // طباعة تفاصيل الخطأ في وحدة التحكم للمطورين
  const errorDetails = {
    type: customError.type,
    code: customError.code,
    message: customError.message,
    details: customError.details,
    suggestions: customError.suggestions,
    context
  }
  Logger.error('ErrorHandler', 'Error Details:', new Error(JSON.stringify(errorDetails)))

  return customError
}

// دالة لإنشاء أخطاء مخصصة
export function createError(
  type: ErrorType,
  message: string,
  code?: string,
  field?: string,
  suggestions?: string[]
): CustomError {
  return {
    type,
    code,
    message,
    field,
    suggestions
  }
}

// دالة للتحقق من صحة البيانات مع رسائل محسنة
export function validateField(value: any, rules: any[], fieldName: string): CustomError | null {
  for (const rule of rules) {
    if (rule.required && (!value || value.toString().trim() === '')) {
      return createError(
        ErrorType.VALIDATION,
        `${fieldName} مطلوب`,
        'REQUIRED_FIELD',
        fieldName,
        [`يرجى إدخال ${fieldName}`]
      )
    }

    if (rule.type === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return createError(
        ErrorType.VALIDATION,
        ERROR_MESSAGES.INVALID_EMAIL,
        'INVALID_EMAIL',
        fieldName,
        ['تأكد من صيغة البريد الإلكتروني (<EMAIL>)']
      )
    }

    if (rule.type === 'number' && value && isNaN(Number(value))) {
      return createError(
        ErrorType.VALIDATION,
        ERROR_MESSAGES.INVALID_NUMBER,
        'INVALID_NUMBER',
        fieldName,
        ['يرجى إدخال أرقام فقط']
      )
    }

    if (rule.min && value && value.toString().length < rule.min) {
      return createError(
        ErrorType.VALIDATION,
        `${fieldName} يجب أن يكون ${rule.min} أحرف على الأقل`,
        'MIN_LENGTH',
        fieldName,
        [`أدخل ${rule.min} أحرف على الأقل`]
      )
    }

    if (rule.max && value && value.toString().length > rule.max) {
      return createError(
        ErrorType.VALIDATION,
        `${fieldName} يجب أن يكون ${rule.max} أحرف كحد أقصى`,
        'MAX_LENGTH',
        fieldName,
        [`أدخل ${rule.max} أحرف كحد أقصى`]
      )
    }
  }

  return null
}

// دالة لعرض رسائل النجاح المحسنة (سيتم عرضها بواسطة المكونات باستخدام App.useApp())
export function displaySuccess(successMessage: string, context?: string) {
  let displayMessage = successMessage

  if (context) {
    displayMessage = `${context}: ${displayMessage}`
  }

  Logger.info('ErrorHandler', 'SUCCESS: ${displayMessage}')
  return displayMessage
}

// دالة لعرض رسائل التحذير المحسنة (سيتم عرضها بواسطة المكونات باستخدام App.useApp())
export function displayWarning(warningMessage: string, context?: string) {
  let displayMessage = warningMessage

  if (context) {
    displayMessage = `${context}: ${displayMessage}`
  }

  Logger.warn('ErrorHandler', 'WARNING: ${displayMessage}')
  return displayMessage
}

export default {
  analyzeError,
  displayError,
  createError,
  validateField,
  displaySuccess,
  displayWarning,
  ErrorType,
  ERROR_MESSAGES
}
