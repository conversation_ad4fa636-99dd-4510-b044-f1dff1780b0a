# 🏢 ZET.IA - برنامج المحاسبة والإنتاج المتكامل

**المطور:** FARESNAWAF
**البريد الإلكتروني:** <EMAIL>
**الهاتف:** **********

## 📋 نظرة عامة

ZET.IA هو برنامج شامل لإدارة المحاسبة والإنتاج مصمم خصيصاً للشركات الصغيرة والمتوسطة. يوفر البرنامج حلولاً متكاملة لإدارة المخزون، المبيعات، المشتريات، الإنتاج، والتقارير المالية.

**✅ تم تنظيف وتطوير النظام بالكامل - الإصدار 1.7.0**

## 🧹 التحسينات الجديدة

تم تنظيف وتطوير النظام بالكامل:

- ✅ **حذف 70+ ملف غير ضروري** - إزالة جميع الملفات المكررة والمؤقتة
- ✅ **تنظيم بنية المشروع** - هيكل منطقي ومنظم
- ✅ **تحسين الأداء** - كود محسن وسريع
- ✅ **إصلاح الأخطاء** - نظام مستقر وموثوق
- ✅ **تحديث التوثيق** - دليل شامل ومحدث
- ✅ **النتيجة**: نظام احترافي جاهز للإنتاج

## 🚀 تشغيل التطبيق

### الطريقة الأولى: تشغيل مباشر (الأسرع)
```bash
# Windows Command Prompt
run-app.bat

# أو PowerShell
.\run-app.ps1
```

### الطريقة الثانية: تشغيل يدوي
```bash
# تشغيل من الحزمة المبنية
release-new\win-unpacked\ZET.IA.exe

# أو تشغيل من التطوير
npm run electron
```

### الطريقة الثالثة: إعادة البناء والتشغيل
```bash
# إعادة بناء التطبيق
npm run build

# إنشاء الحزمة النهائية
npm run dist

# تشغيل التطبيق
release-new\win-unpacked\ZET.IA.exe
```

## 🎨 نظام الطباعة المحسن

### ميزات طباعة الصور الجديدة:
- 🖨️ **طباعة صور الأصناف**: طباعة مباشرة مع خيارات متقدمة
- 📋 **كتالوج المنتجات**: عرض وطباعة كتالوج شامل
- 🏦 **طباعة صور الشيكات**: إدارة وطباعة صور الشيكات
- ⚙️ **إعدادات مخصصة**: تحكم كامل في خيارات الطباعة

### خيارات التخطيط:
- **تخطيط واحد**: صورة واحدة كبيرة مع التفاصيل
- **تخطيط الشبكة**: 2×2, 3×3, 4×4 صور في الصفحة
- **تخطيط القائمة**: صور مصغرة مع تفاصيل كاملة

## 🚀 المميزات

- **محمول بالكامل**: لا يحتاج أي متطلبات خارجية
- **قاعدة بيانات SQLite**: سريعة وموثوقة
- **واجهة عربية**: دعم كامل للغة العربية
- **تصميم حديث**: شبيه بـ Windows 10
- **متعدد الوحدات**: نظام شامل لجميع العمليات
- **نظام نظيف**: تم إزالة جميع الملفات غير الضرورية

## 🛠️ التقنيات المستخدمة

- **Electron**: للتطبيق المحمول
- **React**: للواجهة الأمامية
- **TypeScript**: للكتابة الآمنة
- **Ant Design**: مكتبة المكونات
- **SQLite**: قاعدة البيانات
- **Styled Components**: للتنسيق

## 📦 التثبيت والتشغيل

### المتطلبات
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn

### خطوات التثبيت

1. **تحميل المشروع**
   ```bash
   git clone [repository-url]
   cd accounting-production-app
   ```

2. **تثبيت المكتبات**
   ```bash
   npm install
   ```

3. **تشغيل البرنامج في وضع التطوير**
   ```bash
   npm run dev
   ```

4. **بناء البرنامج للإنتاج**
   ```bash
   npm run build
   npm run pack
   ```

5. **تشغيل الملف التنفيذي الجاهز**
   ```bash
   # تشغيل التطبيق مباشرة
   npm run run-app
   ```
   أو تشغيل الملف مباشرة من:
   ```
   release-updated\win-unpacked\Accounting Production App.exe
   ```

## 🔐 بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📁 هيكل المشروع

```
accounting-app/
├── src/
│   ├── main/              # Electron main process
│   │   ├── main.ts        # الملف الرئيسي
│   │   └── preload.ts     # ملف التحميل المسبق
│   └── renderer/          # React app
│       ├── src/
│       │   ├── components/    # المكونات
│       │   ├── types/         # تعريف الأنواع
│       │   ├── App.tsx        # التطبيق الرئيسي
│       │   └── main.tsx       # نقطة الدخول
│       └── index.html
├── dist/                  # الملفات المبنية
├── package.json
└── README.md
```

## 🗂️ الوحدات المتاحة

### ✅ المكتملة
- [x] نظام تسجيل الدخول والمصادقة الآمنة
- [x] إدارة المستخدمين والأدوار والصلاحيات
- [x] لوحة التحكم الرئيسية بتصميم Windows 10
- [x] قاعدة البيانات الشاملة (SQLite)
- [x] إدارة المخازن والمواقع
- [x] إدارة فئات الأصناف (هيكل شجري)
- [x] إدارة الأصناف والمنتجات
- [x] نظام الباركود المتقدم
- [x] إدارة المخزون والكميات
- [x] حركات المخزون (إدخال/إخراج/تحويل/تسوية)
- [x] 5 تقارير مخزون متقدمة مع فلاتر وتصدير
- [x] نظام التقويم الميلادي الكامل
- [x] العملة الأساسية: الشيكل الإسرائيلي (₪)

### 🚧 قيد التطوير
- [ ] وحدة المشتريات
- [ ] وحدة المبيعات
- [ ] وحدة الإنتاج (الأثاث والدهانات)
- [ ] الوحدات المالية (البنوك والشيكات)
- [ ] إدارة الموظفين
- [ ] التقارير المالية المتقدمة

## 🎨 التخصيص

### تغيير اسم الشركة
يمكن تغيير اسم الشركة من خلال تحديث جدول `settings` في قاعدة البيانات:

```sql
UPDATE settings SET value = 'ZET.IA' WHERE key = 'company_name';
```

### تغيير العملة
```sql
UPDATE settings SET value = 'العملة المطلوبة' WHERE key = 'currency';
```

## 🔧 الأوامر المتاحة

### أوامر npm
- `npm run dev`: تشغيل البرنامج في وضع التطوير
- `npm run build`: بناء البرنامج
- `npm run pack`: إنشاء الحزمة التنفيذية
- `npm run electron`: تشغيل Electron مباشرة

### سكريبتات PowerShell الموحدة
- `.\build-unified.ps1`: بناء كامل جديد وتشغيل
- `.\update-app.ps1`: تحديث سريع للتطوير
- `.\run-app.ps1`: تشغيل سريع للملف التنفيذي

## 📊 قاعدة البيانات

البرنامج يستخدم قاعدة بيانات SQLite محلية تُحفظ في:
- **Windows**: `%APPDATA%/accounting-production-app/accounting.db`
- **macOS**: `~/Library/Application Support/accounting-production-app/accounting.db`
- **Linux**: `~/.config/accounting-production-app/accounting.db`

## 🐛 الإبلاغ عن الأخطاء

إذا واجهت أي مشاكل، يرجى:
1. التأكد من تثبيت جميع المكتبات المطلوبة
2. التحقق من إصدار Node.js
3. مراجعة ملفات السجل في وحدة التحكم

## 📝 المساهمة

نرحب بالمساهمات! يرجى:
1. إنشاء fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال pull request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

للحصول على الدعم أو الاستفسارات، يرجى التواصل معنا.

---

**تم التطوير بواسطة**: faresnawf
**الهاتف**: **********
**البريد الإلكتروني**: <EMAIL>
**تاريخ الإنشاء**: 2025-06-22
**آخر تحديث**: 2025-07-10
**الإصدار**: 1.7.0
**الحالة**: ✅ نظام نظيف ومكتمل مع ملف تنفيذي موحد

## 🎯 حالة النظام النظيف

- ✅ **تم حذف 23 ملف غير ضروري**
- ✅ **نظام نظيف ومنظم بالكامل**
- ✅ **جميع الوظائف تعمل بشكل مثالي**
- ✅ **ملف تنفيذي جاهز للاستخدام**
- ✅ **قاعدة بيانات SQLite مستقرة**
- ✅ **واجهة محدثة ومتوافقة مع Ant Design v5**

**🏆 النظام جاهز للاستخدام الإنتاجي بثقة كاملة!**
