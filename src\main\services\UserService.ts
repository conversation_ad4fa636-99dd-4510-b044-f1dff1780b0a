import * as bcrypt from 'bcryptjs'
import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { Logger } from '../utils/logger'

export interface CreateUserData {
  user_code?: string
  username: string
  password: string
  full_name: string
  email?: string
  phone?: string
  role: string
  is_active: boolean
}

export interface UpdateUserData {
  user_code?: string
  username?: string
  password?: string
  full_name?: string
  email?: string
  phone?: string
  role?: string
  is_active?: boolean
}

export class UserService {
  private static instance: UserService
  private databaseService: DatabaseService

  private constructor() {
    this.databaseService = DatabaseService.getInstance()
  }

  private get db() {
    return this.databaseService.getDatabase()
  }

  public static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService()
    }
    return UserService.instance
  }

  // الحصول على جميع المستخدمين
  public async getUsers(): Promise<ApiResponse> {
    try {
      const users = this.db.prepare(`
        SELECT id, user_code, username, full_name, email, phone, role,
               is_active, last_login, login_attempts, locked_until,
               created_at, updated_at, roles
        FROM users
        ORDER BY created_at DESC
      `).all([])

      return { success: true, data: users }
    } catch (error) {
      Logger.error('UserService', 'خطأ في جلب المستخدمين:', error)
      return { success: false, message: 'حدث خطأ في جلب المستخدمين' }
    }
  }

  // الحصول على المستخدمين النشطين فقط
  public async getActiveUsers(): Promise<ApiResponse> {
    try {
      const users = this.db.prepare(`
        SELECT id, user_code, username, full_name, email, phone, role,
               is_active, last_login, login_attempts, locked_until,
               created_at, updated_at, roles
        FROM users
        WHERE is_active = 1
        ORDER BY full_name
      `).all([])

      return { success: true, data: users }
    } catch (error) {
      Logger.error('UserService', 'خطأ في جلب المستخدمين النشطين:', error)
      return { success: false, message: 'حدث خطأ في جلب المستخدمين النشطين' }
    }
  }

  // إنشاء مستخدم جديد
  public async createUser(userData: CreateUserData): Promise<ApiResponse> {
    try {
      // التحقق من عدم وجود اسم المستخدم
      const existingUser = this.db.prepare('SELECT id FROM users WHERE username = ?').get([userData.username])
      if (existingUser) {
        return { success: false, message: 'اسم المستخدم موجود بالفعل' }
      }

      // التحقق من عدم وجود البريد الإلكتروني
      if (userData.email) {
        const existingEmail = this.db.prepare('SELECT id FROM users WHERE email = ?').get([userData.email])
        if (existingEmail) {
          return { success: false, message: 'البريد الإلكتروني موجود بالفعل' }
        }
      }

      // تشفير كلمة المرور
      const hashedPassword = await bcrypt.hash(userData.password, 10)

      // إنشاء كود المستخدم إذا لم يتم توفيره
      let userCode = userData.user_code
      if (!userCode) {
        const generatedCode = await this.generateUserCode()
        userCode = generatedCode.code
      }

      // إدراج المستخدم الجديد
      const result = this.db.prepare(`
        INSERT INTO users (
          user_code, username, password_hash, full_name, email, phone, role, is_active,
          last_login, login_attempts, locked_until, created_at, updated_at, roles
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'), ?)
      `).run([
        userCode,
        userData.username,
        hashedPassword,
        userData.full_name,
        userData.email || null,
        userData.phone || null,
        userData.role,
        userData.is_active ? 1 : 0,
        null, // last_login
        0,    // login_attempts
        null, // locked_until
        userData.role // roles - نفس الدور الأساسي
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return {
          success: true,
          message: 'تم إنشاء المستخدم بنجاح',
          data: { id: result.lastInsertRowid, user_code: userCode }
        }
      } else {
        return { success: false, message: 'فشل في إنشاء المستخدم' }
      }

    } catch (error) {
      Logger.error('UserService', 'خطأ في إنشاء المستخدم:', error)
      return { success: false, message: 'حدث خطأ في إنشاء المستخدم' }
    }
  }

  // تحديث مستخدم
  public async updateUser(userId: number, userData: UpdateUserData, userRole?: string): Promise<ApiResponse> {
    try {
      if (userRole !== 'admin' && userRole !== 'user_manager') {
        return { success: false, message: 'ليس لديك صلاحية تحديث المستخدمين. فقط المدير أو مدير المستخدمين يمكنه ذلك.' }
      }
      // التحقق من وجود المستخدم
      const existingUser = this.db.prepare('SELECT * FROM users WHERE id = ?').get([userId]) as any
      if (!existingUser) {
        return { success: false, message: 'المستخدم غير موجود' }
      }

      // التحقق من عدم تكرار اسم المستخدم
      if (userData.username && userData.username !== existingUser.username) {
        const duplicateUsername = this.db.prepare('SELECT id FROM users WHERE username = ? AND id != ?').get([userData.username, userId])
        if (duplicateUsername) {
          return { success: false, message: 'اسم المستخدم موجود بالفعل' }
        }
      }

      // التحقق من عدم تكرار البريد الإلكتروني
      if (userData.email && userData.email !== existingUser.email) {
        const duplicateEmail = this.db.prepare('SELECT id FROM users WHERE email = ? AND id != ?').get([userData.email, userId])
        if (duplicateEmail) {
          return { success: false, message: 'البريد الإلكتروني موجود بالفعل' }
        }
      }

      // إعداد البيانات للتحديث
      const updateFields: string[] = []
      const updateValues: any[] = []

      if (userData.user_code !== undefined) {
        updateFields.push('user_code = ?')
        updateValues.push(userData.user_code)
      }
      if (userData.username !== undefined) {
        updateFields.push('username = ?')
        updateValues.push(userData.username)
      }
      if (userData.password !== undefined) {
        const hashedPassword = await bcrypt.hash(userData.password, 10)
        updateFields.push('password_hash = ?')
        updateValues.push(hashedPassword)
      }
      if (userData.full_name !== undefined) {
        updateFields.push('full_name = ?')
        updateValues.push(userData.full_name)
      }
      if (userData.email !== undefined) {
        updateFields.push('email = ?')
        updateValues.push(userData.email)
      }
      if (userData.phone !== undefined) {
        updateFields.push('phone = ?')
        updateValues.push(userData.phone)
      }
      if (userData.role !== undefined) {
        updateFields.push('role = ?')
        updateValues.push(userData.role)
      }
      if (userData.is_active !== undefined) {
        updateFields.push('is_active = ?')
        updateValues.push(userData.is_active ? 1 : 0)
      }

      if (updateFields.length === 0) {
        return { success: false, message: 'لا توجد بيانات للتحديث' }
      }

      // إضافة تاريخ التحديث
      updateFields.push('updated_at = datetime(\'now\')')
      updateValues.push(userId)

      // تنفيذ التحديث
      const result = this.db.prepare(`
        UPDATE users SET ${updateFields.join(', ')} WHERE id = ?
      `).run(...updateValues)

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث المستخدم بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث المستخدم' }
      }

    } catch (error) {
      Logger.error('UserService', 'خطأ في تحديث المستخدم:', error)
      return { success: false, message: 'حدث خطأ في تحديث المستخدم' }
    }
  }

  // حذف مستخدم
  public async deleteUser(userId: number, userRole?: string): Promise<ApiResponse> {
    try {
      if (userRole !== 'admin' && userRole !== 'user_manager') {
        return { success: false, message: 'ليس لديك صلاحية حذف المستخدمين. فقط المدير أو مدير المستخدمين يمكنه ذلك.' }
      }
      // التحقق من وجود المستخدم
      const user = this.db.prepare('SELECT * FROM users WHERE id = ?').get([userId]) as any
      if (!user) {
        return { success: false, message: 'المستخدم غير موجود' }
      }

      // منع حذف المدير الرئيسي
      if (user.username === 'admin') {
        return { success: false, message: 'لا يمكن حذف المدير الرئيسي' }
      }

      // حذف المستخدم
      const result = this.db.prepare('DELETE FROM users WHERE id = ?').run([userId])

      if (result.changes > 0) {
        return { success: true, message: 'تم حذف المستخدم بنجاح' }
      } else {
        return { success: false, message: 'فشل في حذف المستخدم' }
      }

    } catch (error) {
      Logger.error('UserService', 'خطأ في حذف المستخدم:', error)
      return { success: false, message: 'حدث خطأ في حذف المستخدم' }
    }
  }

  // تبديل حالة المستخدم (تفعيل/إلغاء تفعيل)
  public async toggleUserStatus(userId: number, isActive: boolean, userRole?: string): Promise<ApiResponse> {
    try {
      if (userRole !== 'admin' && userRole !== 'user_manager') {
        return { success: false, message: 'ليس لديك صلاحية تغيير حالة المستخدمين. فقط المدير أو مدير المستخدمين يمكنه ذلك.' }
      }
      const result = this.db.prepare(`
        UPDATE users
        SET is_active = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run([isActive ? 1 : 0, userId])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        const status = isActive ? 'تفعيل' : 'إلغاء تفعيل'
        return { success: true, message: `تم ${status} المستخدم بنجاح` }
      } else {
        return { success: false, message: 'فشل في تحديث حالة المستخدم' }
      }

    } catch (error) {
      Logger.error('UserService', 'خطأ في تبديل حالة المستخدم:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة المستخدم' }
    }
  }

  // إعادة تعيين كلمة مرور المستخدم
  public async resetUserPassword(userId: number): Promise<ApiResponse> {
    try {
      const defaultPassword = 'password123'
      const hashedPassword = await bcrypt.hash(defaultPassword, 10)

      const result = this.db.prepare(`
        UPDATE users
        SET password_hash = ?, login_attempts = 0, locked_until = NULL, updated_at = datetime('now')
        WHERE id = ?
      `).run([hashedPassword, userId])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return {
          success: true,
          message: 'تم إعادة تعيين كلمة المرور بنجاح',
          data: { newPassword: defaultPassword }
        }
      } else {
        return { success: false, message: 'فشل في إعادة تعيين كلمة المرور' }
      }

    } catch (error) {
      Logger.error('UserService', 'خطأ في إعادة تعيين كلمة المرور:', error)
      return { success: false, message: 'حدث خطأ في إعادة تعيين كلمة المرور' }
    }
  }

  // توليد كود مستخدم جديد متسلسل
  public async generateUserCode(): Promise<{ code: string }> {
    try {
      // الحصول على جميع الأكواد الموجودة مرتبة
      const existingCodes = this.db.prepare(`
        SELECT user_code FROM users
        WHERE user_code LIKE 'USER%' AND user_code IS NOT NULL AND user_code != ''
        ORDER BY CAST(SUBSTR(user_code, 5) AS INTEGER) ASC
      `).all() as any[]

      // استخراج الأرقام من الأكواد
      const existingNumbers = existingCodes
        .map(row => {
          const codeNumber = row.user_code.substring(4)
          const number = parseInt(codeNumber)
          return isNaN(number) ? 0 : number
        })
        .filter(num => num > 0)
        .sort((a, b) => a - b)

      // البحث عن أول رقم متاح في التسلسل
      let nextNumber = 1
      for (const num of existingNumbers) {
        if (num === nextNumber) {
          nextNumber++
        } else if (num > nextNumber) {
          // وجدنا فجوة في التسلسل
          break
        }
      }

      // تكوين الكود الجديد
      const newCode = `USER${nextNumber.toString().padStart(3, '0')}`

      // التحقق النهائي من عدم وجود الكود (احتياط إضافي)
      const existingUser = this.db.prepare('SELECT id FROM users WHERE user_code = ?').get([newCode])
      if (existingUser) {
        // في حالة وجود تضارب غير متوقع، ابحث عن أول رقم متاح بعد آخر رقم
        const maxNumber = Math.max(...existingNumbers, 0)
        return { code: `USER${(maxNumber + 1).toString().padStart(3, '0')}` }
      }

      return { code: newCode }
    } catch (error) {
      Logger.error('UserService', 'خطأ في توليد كود المستخدم:', error)
      return { code: `USER${Date.now().toString().slice(-3)}` }
    }
  }

  // الحصول على الأدوار
  public async getRoles(): Promise<ApiResponse> {
    try {
      const roles = this.db.prepare(`
        SELECT * FROM roles
        WHERE is_active = 1
        ORDER BY name
      `).all()

      return { success: true, data: roles }
    } catch (error) {
      Logger.error('UserService', 'خطأ في جلب الأدوار:', error)
      return { success: false, message: 'حدث خطأ في جلب الأدوار' }
    }
  }

  // الحصول على الأدوار مع عدد المستخدمين
  public async getRolesWithUsers(): Promise<ApiResponse> {
    try {
      const roles = this.db.prepare(`
        SELECT r.*,
               COUNT(u.id) as users_count
        FROM roles r
        LEFT JOIN users u ON u.role = r.name
        WHERE r.is_active = 1
        GROUP BY r.id
        ORDER BY r.name
      `).all()

      return { success: true, data: roles }
    } catch (error) {
      Logger.error('UserService', 'خطأ في جلب الأدوار مع المستخدمين:', error)
      return { success: false, message: 'حدث خطأ في جلب الأدوار مع المستخدمين' }
    }
  }

  // الحصول على الصلاحيات
  public async getPermissions(): Promise<ApiResponse> {
    try {
      const permissions = this.db.prepare(`
        SELECT * FROM permissions
        ORDER BY module, name
      `).all()

      return { success: true, data: permissions }
    } catch (error) {
      Logger.error('UserService', 'خطأ في جلب الصلاحيات:', error)
      return { success: false, message: 'حدث خطأ في جلب الصلاحيات' }
    }
  }

  // الحصول على صلاحيات مستخدم معين
  public async getUserPermissions(userId: number): Promise<ApiResponse> {
    try {
      // جلب دور المستخدم
      const user = this.db.prepare('SELECT role FROM users WHERE id = ?').get([userId]) as any

      if (!user) {
        return { success: false, message: 'المستخدم غير موجود' }
      }

      // جلب صلاحيات الدور
      const rolePermissions = this.db.prepare(`
        SELECT p.name
        FROM roles r
        JOIN role_permissions rp ON r.id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.id
        WHERE r.name = ? AND r.is_active = 1
      `).all(user.role)

      const permissions = rolePermissions.map((p: any) => p.name)

      return { success: true, data: permissions }
    } catch (error) {
      Logger.error('UserService', 'خطأ في جلب صلاحيات المستخدم:', error)
      return { success: false, message: 'حدث خطأ في جلب صلاحيات المستخدم' }
    }
  }

  // الحصول على أنشطة المستخدم
  public async getUserActivities(userId: number): Promise<ApiResponse> {
    try {
      const activities = this.db.prepare(`
        SELECT * FROM login_attempts
        WHERE username = (SELECT username FROM users WHERE id = ?)
        ORDER BY attempt_time DESC
        LIMIT 50
      `).all([userId])

      return { success: true, data: activities }
    } catch (error) {
      Logger.error('UserService', 'خطأ في جلب أنشطة المستخدم:', error)
      return { success: false, message: 'حدث خطأ في جلب أنشطة المستخدم' }
    }
  }

  // الحصول على جميع أنشطة المستخدمين
  public async getAllUserActivities(filters?: any): Promise<ApiResponse> {
    try {
      let sql = `
        SELECT la.*, u.full_name 
        FROM login_attempts la
        LEFT JOIN users u ON la.username = u.username
        ORDER BY la.attempt_time DESC
      `

      if (filters?.limit) {
        sql += ` LIMIT ${filters.limit}`
      } else {
        sql += ' LIMIT 100'
      }

      const activities = this.db.prepare(sql).all()

      return { success: true, data: activities }
    } catch (error) {
      Logger.error('UserService', 'خطأ في جلب أنشطة المستخدمين:', error)
      return { success: false, message: 'حدث خطأ في جلب أنشطة المستخدمين' }
    }
  }

  // تنّيف الأنشطة القديمة
  public async cleanupOldActivities(daysToKeep: number = 30): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        DELETE FROM login_attempts 
        WHERE attempt_time < datetime('now', '-${daysToKeep} days')
      `).run()

      return { 
        success: true, 
        message: `تم حذف ${result.changes} نشاط قديم`,
        data: { deletedCount: result.changes }
      }
    } catch (error) {
      Logger.error('UserService', 'خطأ في تنّيف الأنشطة القديمة:', error)
      return { success: false, message: 'حدث خطأ في تنّيف الأنشطة القديمة' }
    }
  }
}
