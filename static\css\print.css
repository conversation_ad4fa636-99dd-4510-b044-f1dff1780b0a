/* ===================================
   نظام طباعة احترافي متكامل
   Professional Print System
   =================================== */

/* إعدادات عامة للطباعة */
@media print {
    /* إخفاء العناصر غير المرغوبة */
    .no-print,
    .btn,
    .navbar,
    .sidebar,
    .footer,
    .breadcrumb,
    .alert,
    .modal,
    .dropdown,
    .pagination,
    .form-control,
    input[type="button"],
    input[type="submit"],
    button,
    .print-hide {
        display: none !important;
        visibility: hidden !important;
    }

    /* إعدادات الصفحة */
    @page {
        size: A4;
        margin: 1.5cm 1cm 1cm 1cm;
        orphans: 3;
        widows: 3;
    }

    @page :first {
        margin-top: 2cm;
    }

    @page :left {
        margin-left: 1.5cm;
        margin-right: 1cm;
    }

    @page :right {
        margin-left: 1cm;
        margin-right: 1.5cm;
    }

    /* إعدادات الجسم الأساسي */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    html {
        font-size: 12pt;
        direction: rtl;
    }

    body {
        font-family: 'Amiri', 'Times New Roman', 'DejaVu Sans', serif !important;
        font-size: 12pt !important;
        line-height: 1.6 !important;
        color: #000 !important;
        background: white !important;
        margin: 0 !important;
        padding: 0 !important;
        direction: rtl;
        text-align: right;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* إعدادات الحاويات */
    .container,
    .container-fluid {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
        direction: rtl;
    }

    .row {
        margin: 0 !important;
        padding: 0 !important;
    }

    .col-*,
    [class*="col-"] {
        padding: 0 !important;
        margin: 0 !important;
        float: none !important;
        width: 100% !important;
    }

    /* إعدادات النصوص العربية */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Amiri', 'Times New Roman', serif !important;
        font-weight: bold !important;
        color: #000 !important;
        text-align: center !important;
        margin: 0.5cm 0 0.3cm 0 !important;
        padding: 0 !important;
        page-break-after: avoid;
        direction: rtl;
    }

    h1 {
        font-size: 18pt !important;
        border-bottom: 2pt solid #000;
        padding-bottom: 0.2cm !important;
    }

    h2 {
        font-size: 16pt !important;
    }

    h3 {
        font-size: 14pt !important;
    }

    p {
        font-family: 'Amiri', 'Times New Roman', serif !important;
        font-size: 12pt !important;
        line-height: 1.6 !important;
        margin: 0.3cm 0 !important;
        text-align: justify !important;
        direction: rtl;
        orphans: 3;
        widows: 3;
    }

    /* إعدادات الجداول المحسنة */
    table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin: 0.5cm 0 !important;
        font-size: 11pt !important;
        direction: rtl;
        page-break-inside: avoid;
        table-layout: auto !important;
        min-width: 100% !important;
    }

    /* إعدادات خاصة لجداول الأصناف */
    .items-table,
    .print-table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin: 0.3cm 0 !important;
        font-size: 10pt !important;
        table-layout: fixed !important;
    }

    .items-table th,
    .items-table td,
    .print-table th,
    .print-table td {
        border: 1pt solid #000 !important;
        padding: 0.15cm 0.2cm !important;
        vertical-align: top !important;
        word-wrap: break-word !important;
        overflow: visible !important;
        white-space: normal !important;
    }

    /* تحديد عرض الأعمدة للجداول */
    .items-table th:nth-child(1),
    .items-table td:nth-child(1) { width: 8% !important; } /* رقم */
    .items-table th:nth-child(2),
    .items-table td:nth-child(2) { width: 35% !important; } /* الصنف */
    .items-table th:nth-child(3),
    .items-table td:nth-child(3) { width: 12% !important; } /* الكمية */
    .items-table th:nth-child(4),
    .items-table td:nth-child(4) { width: 10% !important; } /* الوحدة */
    .items-table th:nth-child(5),
    .items-table td:nth-child(5) { width: 15% !important; } /* السعر */
    .items-table th:nth-child(6),
    .items-table td:nth-child(6) { width: 20% !important; } /* المجموع */

    table thead {
        display: table-header-group !important;
    }

    table tbody {
        display: table-row-group !important;
    }

    table tfoot {
        display: table-footer-group !important;
    }

    th, td {
        border: 1pt solid #000 !important;
        padding: 0.2cm 0.3cm !important;
        text-align: right !important;
        vertical-align: top !important;
        font-family: 'Amiri', 'Times New Roman', serif !important;
        direction: rtl;
        page-break-inside: avoid;
        overflow: visible !important;
        word-wrap: break-word !important;
        white-space: normal !important;
        max-width: none !important;
    }

    th {
        background-color: #f0f0f0 !important;
        font-weight: bold !important;
        text-align: center !important;
        font-size: 11pt !important;
        padding: 0.3cm 0.2cm !important;
    }

    /* تحسين عرض النصوص في الخلايا */
    td {
        line-height: 1.4 !important;
        min-height: 0.8cm !important;
    }

    /* تحسين عرض أسماء الأصناف */
    .items-table td:nth-child(2) {
        text-align: right !important;
        direction: rtl !important;
        padding: 0.2cm !important;
    }

    /* تحسين عرض الأرقام في الجداول */
    .items-table td:nth-child(3),
    .items-table td:nth-child(5),
    .items-table td:nth-child(6) {
        text-align: left !important;
        direction: ltr !important;
        font-family: 'Times New Roman', monospace !important;
    }

    /* إعدادات الأرقام */
    .number,
    .amount,
    .price,
    .total {
        font-family: 'Times New Roman', monospace !important;
        text-align: left !important;
        direction: ltr !important;
        font-weight: bold !important;
    }

    /* إعدادات التواريخ */
    .date {
        font-family: 'Times New Roman', serif !important;
        direction: ltr !important;
        text-align: left !important;
    }

    /* إعدادات خاصة للفواتير */
    .invoice-header {
        text-align: center !important;
        margin-bottom: 1cm !important;
        border-bottom: 2pt solid #000 !important;
        padding-bottom: 0.5cm !important;
    }

    .invoice-details {
        margin: 0.5cm 0 !important;
    }

    .invoice-total {
        font-size: 14pt !important;
        font-weight: bold !important;
        text-align: center !important;
        margin-top: 1cm !important;
        border-top: 2pt solid #000 !important;
        padding-top: 0.5cm !important;
    }

    /* إعدادات كسر الصفحات */
    .page-break {
        page-break-before: always !important;
    }

    .no-page-break {
        page-break-inside: avoid !important;
    }

    /* إعدادات الصور */
    img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid !important;
        border: 1pt solid #ddd !important;
        border-radius: 4px !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }

    /* تخطيطات طباعة الصور */
    .images-section {
        margin: 20px 0 !important;
    }

    .single-image-page {
        page-break-after: always !important;
        text-align: center !important;
        min-height: 80vh !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
    }

    .image-container img {
        max-width: 90% !important;
        max-height: 70vh !important;
        object-fit: contain !important;
    }

    .image-metadata {
        background: #f8f9fa !important;
        padding: 15px !important;
        border-radius: 6px !important;
        margin-top: 20px !important;
        text-align: right !important;
        max-width: 600px !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }

    .images-grid {
        display: grid !important;
        gap: 15px !important;
        margin: 20px 0 !important;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    }

    .grid-image-item {
        background: white !important;
        border: 1pt solid #ddd !important;
        border-radius: 6px !important;
        padding: 10px !important;
        text-align: center !important;
        break-inside: avoid !important;
    }

    .grid-image-item img {
        width: 100% !important;
        height: 150px !important;
        object-fit: cover !important;
        border-radius: 4px !important;
    }

    .images-list {
        margin: 20px 0 !important;
    }

    .list-image-item {
        display: flex !important;
        margin-bottom: 15px !important;
        padding: 15px !important;
        border: 1pt solid #ddd !important;
        border-radius: 6px !important;
        background: white !important;
        break-inside: avoid !important;
    }

    .image-thumbnail {
        flex-shrink: 0 !important;
        margin-left: 15px !important;
    }

    .image-thumbnail img {
        width: 100px !important;
        height: 100px !important;
        object-fit: cover !important;
        border-radius: 4px !important;
    }

    .image-details {
        flex: 1 !important;
        text-align: right !important;
    }

    .image-details h4 {
        margin: 0 0 10px 0 !important;
        font-size: 14pt !important;
        color: #333 !important;
    }

    .image-details p {
        margin: 5px 0 !important;
        font-size: 11pt !important;
        line-height: 1.4 !important;
    }

    /* إعدادات الروابط */
    a {
        color: #000 !important;
        text-decoration: none !important;
    }

    a[href]:after {
        content: none !important;
    }

    /* إعدادات خاصة للتقارير */
    .report-header {
        text-align: center !important;
        margin-bottom: 1cm !important;
    }

    .report-footer {
        position: fixed;
        bottom: 1cm;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 10pt;
        border-top: 1pt solid #000;
        padding-top: 0.3cm;
    }

    /* إعدادات خاصة لمعاينة الطباعة */
    .print-preview-content {
        background: white !important;
        padding: 1cm !important;
        margin: 0 !important;
        box-shadow: none !important;
    }

    /* إعدادات خاصة للفواتير والسندات */
    .invoice-preview,
    .receipt-preview {
        background: white !important;
        padding: 1cm !important;
        margin: 0 !important;
        direction: rtl !important;
        font-family: 'Amiri', 'Times New Roman', serif !important;
    }

    .invoice-header,
    .receipt-header {
        text-align: center !important;
        margin-bottom: 1cm !important;
        border-bottom: 2pt solid #000 !important;
        padding-bottom: 0.5cm !important;
    }

    .invoice-details,
    .receipt-details {
        margin: 1cm 0 !important;
    }

    /* تحسين عرض الجداول في المعاينة */
    .print-preview-content table,
    .invoice-preview table,
    .receipt-preview table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin: 0.5cm 0 !important;
    }

    .print-preview-content th,
    .print-preview-content td,
    .invoice-preview th,
    .invoice-preview td,
    .receipt-preview th,
    .receipt-preview td {
        border: 1pt solid #000 !important;
        padding: 0.3cm !important;
        text-align: right !important;
        vertical-align: top !important;
    }

    /* إعدادات خاصة لرسائل عدم وجود بيانات */
    .no-items-message {
        text-align: center !important;
        padding: 1cm !important;
        border: 1pt solid #ccc !important;
        background-color: #f9f9f9 !important;
        margin: 0.5cm 0 !important;
        font-style: italic !important;
        color: #666 !important;
    }

    /* إعدادات الطباعة الملونة */
    .print-color {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* إعدادات خاصة للشركة - تم نقلها إلى MasterPrintService للتحكم الديناميكي */
    .company-logo {
        /* تم إزالة الإعدادات الثابتة لتجنب التعارض مع التنسيق التلقائي */
    }

    /* تنسيق عام للشعار في الطباعة */
    .logo-container {
        overflow: hidden !important;
        display: flex !important;
        align-items: center !important;
    }

    .logo {
        object-fit: contain !important;
        border: none !important;
        background: transparent !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    .company-info {
        text-align: center !important;
        margin-bottom: 1cm !important;
        font-size: 11pt !important;
    }

    /* إعدادات التوقيع */
    .signature-area {
        margin-top: 2cm !important;
        text-align: center !important;
        border-top: 1pt solid #000 !important;
        padding-top: 0.5cm !important;
    }

    /* إعدادات خاصة للعملة */
    .currency {
        font-family: 'Times New Roman', monospace !important;
        direction: ltr !important;
    }

    /* إعدادات الملاحظات */
    .notes {
        margin-top: 1cm !important;
        padding: 0.5cm !important;
        border: 1pt solid #000 !important;
        background-color: #f9f9f9 !important;
    }
}

/* إعدادات خاصة للطباعة الأبيض والأسود - معطلة لدعم الألوان */
/*
@media print and (monochrome) {
    * {
        color: #000 !important;
        background: white !important;
    }

    .bg-primary,
    .bg-secondary,
    .bg-success,
    .bg-danger,
    .bg-warning,
    .bg-info {
        background: #f0f0f0 !important;
        border: 1pt solid #000 !important;
    }
}
*/

/* إعدادات محسنة للطباعة الملونة */
@media print {
    /* التأكد من طباعة الألوان */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* الحفاظ على الألوان المخصصة */
    .print-color-preserve {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}

/* تحسينات للطباعة الشاملة */
@media print {
    .universal-image-context {
        background: #f0f0f0 !important;
        color: #000 !important;
        padding: 8px 12px !important;
        border: 1pt solid #000 !important;
        border-radius: 6px !important;
        font-size: 11pt !important;
        font-weight: bold !important;
        margin-bottom: 8px !important;
        display: inline-block !important;
    }

    .universal-image-metadata {
        background: #f8f9fa !important;
        border: 1pt solid #ddd !important;
        border-radius: 6px !important;
        padding: 12px !important;
        margin-top: 8px !important;
    }

    .universal-image-metadata .metadata-section {
        margin-bottom: 8px !important;
        padding-bottom: 8px !important;
        border-bottom: 1pt solid #ddd !important;
    }

    .universal-image-metadata .metadata-section:last-child {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
        border-bottom: none !important;
    }

    .universal-image-metadata .metadata-label {
        font-weight: bold !important;
        color: #000 !important;
        font-size: 11pt !important;
    }

    .universal-image-metadata .metadata-value {
        color: #333 !important;
        font-size: 10pt !important;
        margin-top: 2px !important;
    }

    /* تحسينات للألوان حسب السياق */
    .context-invoice {
        background: #e6f7ff !important;
        border-color: #1890ff !important;
    }

    .context-production-order {
        background: #f9f0ff !important;
        border-color: #722ed1 !important;
    }

    .context-customer {
        background: #e6fffb !important;
        border-color: #13c2c2 !important;
    }

    .context-supplier {
        background: #fff2e8 !important;
        border-color: #fa541c !important;
    }

    .context-item {
        background: #f6ffed !important;
        border-color: #52c41a !important;
    }

    .context-check {
        background: #fff0f6 !important;
        border-color: #eb2f96 !important;
    }

    /* إخفاء عناصر غير ضرورية في الطباعة */
    .no-print,
    .ant-btn,
    .ant-pagination,
    .ant-table-pagination,
    .ant-modal,
    .ant-drawer,
    .ant-tooltip,
    .ant-popover,
    .ant-dropdown,
    .ant-menu,
    .ant-notification,
    .ant-message,
    button,
    input,
    select,
    textarea {
        display: none !important;
    }
}
