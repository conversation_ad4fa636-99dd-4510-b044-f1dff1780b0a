import React, { useState } from 'react';
import { Tag, Typography, Tabs, Card, Space } from 'antd';
import {
  AppstoreOutlined,
  AlertOutlined,
  BarChartOutlined,
  DollarOutlined,
  TruckOutlined
} from '@ant-design/icons';
import UniversalReport from './UniversalReport';
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger'

const { Text } = Typography;

const InventoryDetailedReport: React.FC = () => {
  const [activeTab, setActiveTab] = useState('detailed');

  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<any> => {
    try {
      Logger.info('InventoryDetailedReport', '🔄 جاري إنشاء تقرير المخزون التفصيلي...')

      let inventoryData: any[]

      if (!window.electronAPI) {
        Logger.error('InventoryDetailedReport', '❌ window.electronAPI غير متوفر')
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      } else {
        // استعلام البيانات من قاعدة البيانات
        const response = await window.electronAPI.getInventoryReport({
          warehouseId: filters.warehouseId,
          categoryId: filters.categoryId,
          itemId: filters.itemId,
          dateRange: filters.dateRange
        });

        if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
          throw new Error('لا توجد بيانات متاحة');
        }

        inventoryData = response.data;
        Logger.info('InventoryDetailedReport', '✅ تم جلب بيانات المخزون من قاعدة البيانات')
      }

      // تحضير أعمدة الجدول
      const columns = [
        {
          title: 'كود الصنف',
          dataIndex: 'item_code',
          key: 'item_code',
          width: 120,
          fixed: 'left' as const,
          render: (text: string) => (
            <Text strong style={{ color: '#1890ff' }}>
              {text}
            </Text>
          )
        },
        {
          title: 'اسم الصنف',
          dataIndex: 'item_name',
          key: 'item_name',
          width: 200,
          fixed: 'left' as const,
          render: (text: string) => (
            <Text strong>{text}</Text>
          )
        },
        {
          title: 'الفئة',
          dataIndex: 'category_name',
          key: 'category_name',
          width: 150,
          render: (text: string) => (
            <Tag color="blue">{text}</Tag>
          )
        },
        {
          title: 'المخزن',
          dataIndex: 'warehouse_name',
          key: 'warehouse_name',
          width: 150,
          render: (text: string) => (
            <Tag color="green">{text}</Tag>
          )
        },
        {
          title: 'الوحدة',
          dataIndex: 'unit',
          key: 'unit',
          width: 80,
          align: 'center' as const
        },
        {
          title: 'الكمية المتاحة',
          dataIndex: 'quantity',
          key: 'quantity',
          width: 120,
          align: 'center' as const,
          render: (quantity: number) => (
            <Text strong style={{ 
              color: quantity > 0 ? '#52c41a' : quantity === 0 ? '#faad14' : '#f5222d' 
            }}>
              {quantity.toLocaleString()}
            </Text>
          )
        },
        {
          title: 'الكمية المحجوزة',
          dataIndex: 'reserved_quantity',
          key: 'reserved_quantity',
          width: 120,
          align: 'center' as const,
          render: (quantity: number) => (
            <Text style={{ color: '#faad14' }}>
              {quantity.toLocaleString()}
            </Text>
          )
        },
        {
          title: 'الكمية الفعلية',
          dataIndex: 'available_quantity',
          key: 'available_quantity',
          width: 120,
          align: 'center' as const,
          render: (quantity: number, record: any) => {
            const available = record.quantity - record.reserved_quantity;
            return (
              <Text strong style={{ 
                color: available > 0 ? '#52c41a' : available === 0 ? '#faad14' : '#f5222d' 
              }}>
                {available.toLocaleString()}
              </Text>
            );
          }
        },
        {
          title: 'سعر التكلفة (₪)',
          dataIndex: 'cost_price',
          key: 'cost_price',
          width: 120,
          align: 'center' as const,
          render: (price: number) => (
            <Text>{price ? price.toFixed(2) : '0.00'}</Text>
          )
        },
        {
          title: 'سعر البيع (₪)',
          dataIndex: 'sale_price',
          key: 'sale_price',
          width: 120,
          align: 'center' as const,
          render: (price: number) => (
            <Text strong style={{ color: '#1890ff' }}>
              {price ? price.toFixed(2) : '0.00'}
            </Text>
          )
        },
        {
          title: 'قيمة المخزون (₪)',
          dataIndex: 'inventory_value',
          key: 'inventory_value',
          width: 140,
          align: 'center' as const,
          render: (value: number, record: any) => {
            const totalValue = record.quantity * record.cost_price;
            return (
              <Text strong style={{ color: '#722ed1' }}>
                {totalValue.toFixed(2)}
              </Text>
            );
          }
        },
        {
          title: 'الحد الأدنى',
          dataIndex: 'min_quantity',
          key: 'min_quantity',
          width: 100,
          align: 'center' as const,
          render: (minQty: number, record: any) => {
            const isLow = record.quantity <= minQty;
            return (
              <Text style={{ color: isLow ? '#f5222d' : '#666' }}>
                {minQty || 0}
              </Text>
            );
          }
        },
        {
          title: 'الحد الأقصى',
          dataIndex: 'max_quantity',
          key: 'max_quantity',
          width: 100,
          align: 'center' as const,
          render: (maxQty: number) => (
            <Text style={{ color: '#666' }}>
              {maxQty || '-'}
            </Text>
          )
        },
        {
          title: 'الحالة',
          dataIndex: 'status',
          key: 'status',
          width: 100,
          align: 'center' as const,
          render: (status: string, record: any) => {
            const available = record.quantity - record.reserved_quantity;
            let statusText = 'متاح';
            let color = 'green';
            
            if (available <= 0) {
              statusText = 'غير متاح';
              color = 'red';
            } else if (record.quantity <= record.min_quantity) {
              statusText = 'منخفض';
              color = 'orange';
            }
            
            return <Tag color={color}>{statusText}</Tag>;
          }
        },
        {
          title: 'الموقع',
          dataIndex: 'location',
          key: 'location',
          width: 120,
          render: (location: string) => (
            <Text type="secondary">{location || '-'}</Text>
          )
        },
        {
          title: 'آخر تحديث (ميلادي)',
          dataIndex: 'last_updated',
          key: 'last_updated',
          width: 140,
          render: (date: string) => (
            <Text type="secondary">
              {date ? DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE_TIME) : '-'}
            </Text>
          )
        }
      ];

      // حساب الملخص
      const totalItems = inventoryData.length;
      const totalQuantity = inventoryData.reduce((sum: number, item: any) => sum + item.quantity, 0);
      const totalValue = inventoryData.reduce((sum: number, item: any) => 
        sum + (item.quantity * item.cost_price), 0
      );
      const lowStockItems = inventoryData.filter((item: any) => 
        item.quantity <= item.min_quantity
      ).length;

      // إضافة مفاتيح فريدة للبيانات
      const dataWithKeys = inventoryData.map((item: any, index: number) => ({
        ...item,
        key: `${item.item_id}-${item.warehouse_id}-${index}`,
        available_quantity: item.quantity - item.reserved_quantity
      }));

      // تحضير عنوان فرعي
      let subtitle = 'تقرير شامل لجميع الأصناف في المخازن';
      if (filters.warehouseId && window.electronAPI) {
        try {
          const warehouse = await window.electronAPI.getWarehouse(filters.warehouseId);
          subtitle += ` - المخزن: ${warehouse?.name}`;
        } catch (error) {
          Logger.error('InventoryDetailedReport', 'خطأ في جلب بيانات المخزن:', error);
        }
      }
      if (filters.dateRange) {
        // التعامل مع التواريخ كـ strings أو dayjs objects
        const startDate = typeof filters.dateRange[0] === 'string' ? filters.dateRange[0] : filters.dateRange[0].format('YYYY-MM-DD');
        const endDate = typeof filters.dateRange[1] === 'string' ? filters.dateRange[1] : filters.dateRange[1].format('YYYY-MM-DD');
        subtitle += ` - من ${startDate} إلى ${endDate} (ميلادي)`;
      }

      return {
        title: 'تقرير المخزون التفصيلي',
        subtitle,
        columns,
        data: dataWithKeys,
        summary: {
          totalItems,
          totalQuantity,
          totalValue: Math.round(totalValue * 100) / 100,
          lowStockItems
        }
      };
    } catch (error) {
      Logger.error('InventoryDetailedReport', 'خطأ في إنشاء تقرير المخزون التفصيلي:', error);
      throw new Error('فشل في إنشاء التقرير');
    }
  };

  return (
    <Card>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'detailed',
            label: (
              <Space>
                <AppstoreOutlined />
                المخزون التفصيلي
              </Space>
            ),
            children: (
              <UniversalReport
                reportType="inventory_detailed"
                title="تقرير المخزون التفصيلي"
                description="تقرير شامل يعرض جميع الأصناف مع الكميات والقيم في كل مخزن"
                onGenerateReport={generateReport}
                showDateRange={false}
                showWarehouseFilter={true}
                showCategoryFilter={true}
                showItemFilter={true}
                showStatistics={true}
                showSummary={true}
                showExportOptions={true}
                showPrintOptions={true}
                exportFileName="inventory_detailed_report"
                defaultFilters={{
                  sortBy: 'item_name',
                  sortOrder: 'asc'
                }}
              />
            )
          },
          {
            key: 'lowstock',
            label: (
              <Space>
                <AlertOutlined />
                الأصناف المنخفضة
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <AlertOutlined style={{ fontSize: '48px', color: '#ff4d4f' }} />
                    <h3>الأصناف المنخفضة</h3>
                    <p>الأصناف التي وصلت لحد الطلب أو أقل</p>
                  </div>
                </Space>
              </Card>
            )
          },
          {
            key: 'valuation',
            label: (
              <Space>
                <DollarOutlined />
                تقييم المخزون
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <DollarOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                    <h3>تقييم المخزون</h3>
                    <p>القيمة الإجمالية للمخزون وتوزيعها</p>
                  </div>
                </Space>
              </Card>
            )
          },
          {
            key: 'movements',
            label: (
              <Space>
                <TruckOutlined />
                الحركات
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <TruckOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                    <h3>حركات المخزون</h3>
                    <p>تقرير حركات الإدخال والإخراج</p>
                  </div>
                </Space>
              </Card>
            )
          },
          {
            key: 'analysis',
            label: (
              <Space>
                <BarChartOutlined />
                التحليل
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <BarChartOutlined style={{ fontSize: '48px', color: '#722ed1' }} />
                    <h3>تحليل المخزون</h3>
                    <p>رسوم بيانية وتحليلات متقدمة للمخزون</p>
                  </div>
                </Space>
              </Card>
            )
          }
        ]}
      />
    </Card>
  );
};

export default InventoryDetailedReport;
