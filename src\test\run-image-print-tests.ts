/**
 * تشغيل اختبارات نظام طباعة الصور المحسن - ZET.IA
 */

import { imagePrintSystemTest } from './image-print-system-test'
import { SafeLogger as Logger } from '../renderer/src/utils/logger'

/**
 * تشغيل جميع اختبارات النظام
 */
async function runImagePrintTests(): Promise<void> {
  Logger.info('RunImagePrintTests', '🚀 بدء تشغيل اختبارات نظام طباعة الصور المحسن')
  
  try {
    await imagePrintSystemTest.runAllTests()
    Logger.info('RunImagePrintTests', '✅ تم الانتهاء من جميع الاختبارات')
  } catch (error) {
    Logger.error('RunImagePrintTests', '❌ خطأ في تشغيل الاختبارات:', error)
  }
}

// تشغيل الاختبارات عند تحميل الملف
if (typeof window !== 'undefined') {
  // في بيئة المتصفح
  window.addEventListener('load', () => {
    runImagePrintTests()
  })
} else {
  // في بيئة Node.js
  runImagePrintTests()
}

export { runImagePrintTests }
