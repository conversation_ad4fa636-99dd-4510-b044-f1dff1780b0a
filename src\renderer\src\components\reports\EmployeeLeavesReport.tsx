import React from 'react';
import { Tag, Typography, Progress, Statistic, Tooltip } from 'antd';
import {
  CalendarOutlined,
  UserOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  HourglassOutlined,
  TrophyOutlined,
  WarningOutlined
} from '@ant-design/icons';
import UniversalReport from './UniversalReport';

import type { ReportData, ReportType } from '../../types/reports';
import { createDefaultFilters } from '../../types/reports';
import { SafeLogger as Logger } from '../../utils/logger';

const { Text } = Typography;

const EmployeeLeavesReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      Logger.info('EmployeeLeavesReport', '📅 بدء إنشاء تقرير إجازات الموظفين...');
      Logger.info('EmployeeLeavesReport', '🔍 الفلاتر المطبقة:', filters);

      let leavesData: any[] = [];

      if (!window.electronAPI || !window.electronAPI.getEmployeeLeavesReport) {
        Logger.info('EmployeeLeavesReport', '⚠️ التطبيق يعمل في وضع المتصفح أو الدالة غير متوفرة - استخدام بيانات وهمية');

        // بيانات وهمية للإجازات
        leavesData = [
          {
            employee_id: 1,
            employee_code: 'EMP001',
            employee_name: 'أحمد محمد علي',
            department_name: 'قسم المحاسبة',
            position: 'محاسب أول',
            total_leave_days: 25,
            used_leave_days: 18,
            remaining_leave_days: 7,
            sick_leave_days: 5,
            annual_leave_days: 13,
            emergency_leave_days: 0,
            leave_requests_count: 6,
            approved_requests: 5,
            pending_requests: 1,
            rejected_requests: 0,
            leave_utilization_rate: 72.0
          },
          {
            employee_id: 2,
            employee_code: 'EMP002',
            employee_name: 'فاطمة أحمد حسن',
            department_name: 'قسم المبيعات',
            position: 'مندوب مبيعات',
            total_leave_days: 30,
            used_leave_days: 22,
            remaining_leave_days: 8,
            sick_leave_days: 3,
            annual_leave_days: 19,
            emergency_leave_days: 0,
            leave_requests_count: 8,
            approved_requests: 7,
            pending_requests: 0,
            rejected_requests: 1,
            leave_utilization_rate: 73.33
          },
          {
            employee_id: 3,
            employee_code: 'EMP003',
            employee_name: 'محمد عبد الله',
            department_name: 'قسم الإنتاج',
            position: 'عامل إنتاج',
            total_leave_days: 20,
            used_leave_days: 12,
            remaining_leave_days: 8,
            sick_leave_days: 7,
            annual_leave_days: 5,
            emergency_leave_days: 0,
            leave_requests_count: 4,
            approved_requests: 4,
            pending_requests: 0,
            rejected_requests: 0,
            leave_utilization_rate: 60.0
          },
          {
            employee_id: 4,
            employee_code: 'EMP004',
            employee_name: 'سارة محمود',
            department_name: 'قسم الموارد البشرية',
            position: 'أخصائي موارد بشرية',
            total_leave_days: 28,
            used_leave_days: 15,
            remaining_leave_days: 13,
            sick_leave_days: 2,
            annual_leave_days: 13,
            emergency_leave_days: 0,
            leave_requests_count: 5,
            approved_requests: 5,
            pending_requests: 0,
            rejected_requests: 0,
            leave_utilization_rate: 53.57
          }
        ];
      } else {
        // استعلام البيانات من قاعدة البيانات
        const response = await window.electronAPI.getEmployeeLeavesReport({
          departmentId: filters.departmentId,
          employeeId: filters.employeeId,
          dateRange: filters.dateRange
        });

        if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
          throw new Error('لا توجد بيانات متاحة');
        }

        leavesData = response.data;
      }

      // تعريف الأعمدة
      const columns = [
        {
          title: 'الموظف',
          key: 'employee',
          render: (record: any) => (
            <div>
              <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
                <UserOutlined style={{ marginLeft: '8px' }} />
                {record.employee_name}
              </div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.employee_code} - {record.position}
              </Text>
            </div>
          ),
          width: 200
        },
        {
          title: 'القسم',
          key: 'department_name',
          format: 'text' as const,
          render: (record: any) => (
            <Tag color="blue" icon={<TeamOutlined />}>
              {record.department_name}
            </Tag>
          ),
          width: 150,
          align: 'center' as const
        },
        {
          title: 'إجمالي الإجازات',
          key: 'total_leave_days',
          format: 'number' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <CalendarOutlined style={{ color: '#1890ff', marginLeft: '4px' }} />
              <strong style={{ color: '#1890ff', fontSize: '16px' }}>
                {record.total_leave_days}
              </strong>
              <div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  يوم
                </Text>
              </div>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'المستخدم',
          key: 'used_leave_days',
          format: 'number' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: '4px' }} />
              <strong style={{ color: '#52c41a', fontSize: '16px' }}>
                {record.used_leave_days}
              </strong>
              <div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  يوم
                </Text>
              </div>
            </div>
          ),
          width: 100,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'المتبقي',
          key: 'remaining_leave_days',
          format: 'number' as const,
          render: (record: any) => {
            const remaining = record.remaining_leave_days;
            const color = remaining <= 5 ? '#ff4d4f' : remaining <= 10 ? '#fa8c16' : '#52c41a';
            return (
              <div style={{ textAlign: 'center' }}>
                <HourglassOutlined style={{ color, marginLeft: '4px' }} />
                <strong style={{ color, fontSize: '16px' }}>
                  {remaining}
                </strong>
                <div>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    يوم
                  </Text>
                </div>
              </div>
            );
          },
          width: 100,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'معدل الاستخدام',
          key: 'leave_utilization_rate',
          format: 'percentage' as const,
          render: (record: any) => {
            const rate = record.leave_utilization_rate;
            const color = rate >= 80 ? '#ff4d4f' :
                         rate >= 60 ? '#fa8c16' :
                         rate >= 40 ? '#1890ff' : '#52c41a';
            return (
              <div style={{ textAlign: 'center' }}>
                <div>
                  <TrophyOutlined style={{ color, marginLeft: '4px' }} />
                  <strong style={{ color, fontSize: '16px' }}>{rate.toFixed(1)}%</strong>
                </div>
                <Progress 
                  percent={rate} 
                  size="small" 
                  strokeColor={color}
                  showInfo={false}
                  style={{ width: '80px', margin: '4px auto' }}
                />
              </div>
            );
          },
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'طلبات الإجازة',
          key: 'leave_requests',
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <div style={{ marginBottom: '4px' }}>
                <Tag color="green" icon={<CheckCircleOutlined />}>
                  {record.approved_requests} موافق
                </Tag>
              </div>
              {record.pending_requests > 0 && (
                <div style={{ marginBottom: '4px' }}>
                  <Tag color="orange" icon={<ExclamationCircleOutlined />}>
                    {record.pending_requests} معلق
                  </Tag>
                </div>
              )}
              {record.rejected_requests > 0 && (
                <div>
                  <Tag color="red" icon={<CloseCircleOutlined />}>
                    {record.rejected_requests} مرفوض
                  </Tag>
                </div>
              )}
            </div>
          ),
          width: 140,
          align: 'center' as const
        }
      ];

      // حساب الإحصائيات
      const totalEmployees = leavesData.length;
      const totalLeaveDays = leavesData.reduce((sum, emp) => sum + (emp.total_leave_days || 0), 0);
      const totalUsedDays = leavesData.reduce((sum, emp) => sum + (emp.used_leave_days || 0), 0);
      const totalRemainingDays = leavesData.reduce((sum, emp) => sum + (emp.remaining_leave_days || 0), 0);
      const avgUtilizationRate = totalEmployees > 0 ? 
        leavesData.reduce((sum, emp) => sum + (emp.leave_utilization_rate || 0), 0) / totalEmployees : 0;
      const totalRequests = leavesData.reduce((sum, emp) => sum + (emp.leave_requests_count || 0), 0);
      const totalApproved = leavesData.reduce((sum, emp) => sum + (emp.approved_requests || 0), 0);

      Logger.info('EmployeeLeavesReport', `✅ تم إنشاء تقرير إجازات الموظفين بنجاح: ${totalEmployees} موظف`);

      return {
        title: 'تقرير إجازات الموظفين',
        data: leavesData,
        columns,
        summary: {
          totalEmployees,
          totalLeaveDays,
          totalUsedDays,
          totalRemainingDays,
          avgUtilizationRate: Math.round(avgUtilizationRate * 10) / 10,
          totalRequests,
          totalApproved,
          approvalRate: totalRequests > 0 ? Math.round((totalApproved / totalRequests) * 100 * 10) / 10 : 0
        },
        metadata: {
          reportType: 'employee_leaves' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: leavesData.length
        }
      };
    } catch (error) {
      Logger.error('EmployeeLeavesReport', 'خطأ في إنشاء تقرير إجازات الموظفين:', error);
      throw new Error('فشل في إنشاء تقرير إجازات الموظفين');
    }
  };

  return (
    <UniversalReport
      reportType={'employee_leaves' as ReportType}
      title="تقرير إجازات الموظفين"
      description="تقرير شامل لإجازات الموظفين مع معدلات الاستخدام وحالات الطلبات"
      onGenerateReport={generateReport}
      defaultFilters={createDefaultFilters('employee_leaves')}
      showDateRange={true}
      showEmployeeFilter={true}
      showDepartmentFilter={true}
      showAdvancedSearch={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default EmployeeLeavesReport;
