# 📸 نظام إدارة الصور المحسن

## 🌟 نظرة عامة

نظام إدارة الصور المحسن هو حل شامل ومتطور لإدارة الصور في تطبيق المحاسبة. يوفر النظام أداءً محسناً، إدارة ذكية للذاكرة، وواجهة موحدة لجميع عمليات الصور.

## ✨ الميزات الرئيسية

### 🚀 الأداء والكفاءة
- **ضغط ذكي للصور** مع الحفاظ على الجودة
- **نظام cache متقدم** مع LRU وإدارة ذكية للذاكرة
- **معالجة متوازية** للصور المتعددة
- **lazy loading** للصور الكبيرة
- **تحسين استهلاك الذاكرة** ومنع التسريبات

### 🔒 الأمان والموثوقية
- **فحص أمني شامل** للملفات المرفوعة
- **التحقق من صحة الملفات** ونوع المحتوى
- **حماية من الملفات الضارة** والكود المشبوه
- **معالجة أخطاء متقدمة** مع تسجيل مفصل

### 🎨 تجربة المستخدم المحسنة
- **واجهة موحدة** لجميع أنواع الصور
- **drag & drop** للرفع السهل
- **عارض صور متقدم** مع تكبير وتدوير
- **معاينة فورية** للصور المرفوعة
- **مؤشرات تقدم** للعمليات الطويلة

### 🗄️ إدارة البيانات المتقدمة
- **قاعدة بيانات موحدة** لجميع الصور
- **نظام علامات** للتصنيف والبحث
- **صور رئيسية** لكل سياق
- **إحصائيات شاملة** للاستخدام
- **نسخ احتياطي تلقائي** للبيانات

## 🏗️ البنية المعمارية

### الطبقة الأساسية (Core Layer)
```
src/renderer/src/services/images/
├── ImageCoreService.ts          # الخدمة الأساسية الموحدة
├── ImageProcessingService.ts    # معالجة وضغط الصور
├── ImageCacheService.ts         # إدارة cache ذكية
├── ImageValidationService.ts    # التحقق من صحة الصور
└── ImageStorageService.ts       # إدارة التخزين (DB + Files)
```

### طبقة المكونات (Components Layer)
```
src/renderer/src/components/images/
├── UnifiedImageManager.tsx      # مدير الصور الموحد
├── EnhancedImageViewer.tsx      # عارض الصور المتقدم
└── index.ts                     # فهرس المكونات
```

### طبقة التكامل (Integration Layer)
```
src/renderer/src/hooks/images/
├── useImageManager.ts           # Hook لإدارة الصور
└── index.ts                     # فهرس Hooks
```

## 🚀 البدء السريع

### 1. التثبيت والتهيئة

```typescript
import { initializeImageServices } from '../services/images'

// في بداية التطبيق
await initializeImageServices()
```

### 2. الاستخدام الأساسي

```typescript
import { UnifiedImageManager } from '../components/images'

const MyComponent = () => {
  return (
    <UnifiedImageManager
      category="inventory"
      contextType="item"
      contextId={123}
      maxImages={5}
      allowMultiple={true}
      onImagesChange={(images) => console.log('تم تحديث الصور:', images)}
    />
  )
}
```

### 3. استخدام Hook

```typescript
import { useImageManager } from '../hooks/images'

const MyComponent = () => {
  const {
    images,
    loading,
    uploadImage,
    deleteImage
  } = useImageManager({
    category: 'inventory',
    contextType: 'item',
    contextId: 123
  })

  // استخدام الوظائف...
}
```

## 📊 أنواع البيانات

### فئات الصور (ImageCategory)
- `inventory` - صور المخزون والأصناف
- `production` - صور أوامر الإنتاج
- `sales` - صور المبيعات والفواتير
- `checks` - صور الشيكات
- `general` - صور عامة

### أنواع السياق (ImageContextType)
- `item` - صنف في المخزون
- `production_order` - أمر إنتاج
- `invoice` - فاتورة مبيعات
- `check` - شيك
- `customer` - عميل
- `supplier` - مورد

### بيانات الصورة الموحدة (UnifiedImageData)
```typescript
interface UnifiedImageData {
  id: string                    // معرف فريد
  name: string                  // اسم الصورة
  originalName: string          // الاسم الأصلي
  path: string                  // مسار الملف
  thumbnailPath?: string        // مسار الصورة المصغرة
  size: number                  // حجم الملف
  width?: number                // العرض
  height?: number               // الارتفاع
  type: string                  // نوع الملف
  category: ImageCategory       // الفئة
  contextType: ImageContextType // نوع السياق
  contextId: number             // معرف السياق
  description?: string          // وصف
  tags: string[]                // علامات
  isActive: boolean             // نشط
  isPrimary: boolean            // رئيسي
  sortOrder: number             // ترتيب العرض
  uploadedAt: Date              // تاريخ الرفع
  uploadedBy?: number           // المستخدم الذي رفع
  metadata?: Record<string, any> // بيانات إضافية
}
```

## ⚙️ خيارات التكوين

### خيارات الرفع (ImageUploadOptions)
```typescript
interface ImageUploadOptions {
  maxSize?: number              // الحد الأقصى للحجم (بايت)
  allowedTypes?: string[]       // الأنواع المسموحة
  quality?: number              // جودة الضغط (0-1)
  maxWidth?: number             // العرض الأقصى
  maxHeight?: number            // الارتفاع الأقصى
  generateThumbnail?: boolean   // إنشاء صورة مصغرة
  thumbnailSize?: number        // حجم الصورة المصغرة
  autoResize?: boolean          // تغيير الحجم تلقائياً
  watermark?: boolean           // إضافة علامة مائية
  watermarkText?: string        // نص العلامة المائية
}
```

### خيارات الاستعلام (ImageQueryOptions)
```typescript
interface ImageQueryOptions {
  category?: ImageCategory      // تصفية حسب الفئة
  contextType?: ImageContextType // تصفية حسب نوع السياق
  contextId?: number            // تصفية حسب معرف السياق
  contextIds?: number[]         // تصفية حسب معرفات متعددة
  tags?: string[]               // تصفية حسب العلامات
  dateFrom?: Date               // من تاريخ
  dateTo?: Date                 // إلى تاريخ
  isPrimary?: boolean           // الصور الرئيسية فقط
  isActive?: boolean            // الصور النشطة فقط
  limit?: number                // عدد النتائج
  offset?: number               // إزاحة النتائج
  sortBy?: string               // ترتيب حسب
  sortOrder?: 'asc' | 'desc'    // اتجاه الترتيب
}
```

## 🔧 الصيانة والتحسين

### تنظيف الذاكرة
```typescript
import { cleanupImageServices } from '../services/images'

// عند إغلاق التطبيق
await cleanupImageServices()
```

### مراقبة الأداء
```typescript
import { ImageCacheService } from '../services/images'

const cacheService = ImageCacheService.getInstance()
const stats = cacheService.getCacheStats()

console.log('إحصائيات Cache:', {
  totalEntries: stats.totalEntries,
  totalSize: stats.totalSize,
  hitRate: stats.hitRate,
  missRate: stats.missRate
})
```

### إحصائيات النظام
```typescript
import { imageService } from '../services/images'

const statsResult = await imageService.getImageStatistics()
if (statsResult.success) {
  const stats = statsResult.data
  console.log('إحصائيات الصور:', {
    totalImages: stats.totalImages,
    totalSize: stats.totalSize,
    byCategory: stats.byCategory
  })
}
```

## 🐛 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### 1. بطء في تحميل الصور
```typescript
// تحقق من إعدادات cache
const cacheStats = cacheService.getCacheStats()
if (cacheStats.hitRate < 50) {
  // زيادة حجم cache
  // أو تحسين استراتيجية التحميل
}
```

#### 2. استهلاك عالي للذاكرة
```typescript
// تنظيف دوري للذاكرة
setInterval(async () => {
  await imageService.cleanup()
}, 5 * 60 * 1000) // كل 5 دقائق
```

#### 3. فشل في رفع الصور
```typescript
// التحقق من صحة الملف
const validation = await validationService.validateFile(file)
if (!validation.isValid) {
  console.error('خطأ في التحقق:', validation.error)
}
```

## 📈 مؤشرات الأداء

### المؤشرات المستهدفة
- **سرعة تحميل الصور**: تحسين بنسبة 50%
- **استهلاك الذاكرة**: تقليل بنسبة 40%
- **زمن الاستجابة**: تحسين بنسبة 60%
- **معدل الأخطاء**: تقليل بنسبة 80%

### مراقبة الأداء
```typescript
// قياس أداء العمليات
const startTime = performance.now()
await imageService.uploadImage(file, category, contextType, contextId)
const endTime = performance.now()
console.log(`وقت الرفع: ${endTime - startTime}ms`)
```

## 🔄 التحديثات والترقيات

### خطة التطوير المستقبلية
1. **دعم الذكاء الاصطناعي** لتصنيف الصور تلقائياً
2. **تكامل مع خدمات التخزين السحابي**
3. **ميزات تحرير الصور** المتقدمة
4. **نظام مشاركة الصور** بين المستخدمين
5. **تحليلات متقدمة** لاستخدام الصور

### إرشادات الترقية
عند ترقية النظام من الإصدار القديم:

1. **نسخ احتياطي** من البيانات الحالية
2. **تشغيل سكريبت الترحيل** للبيانات
3. **اختبار شامل** للوظائف الجديدة
4. **تدريب المستخدمين** على الميزات الجديدة

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

1. **مراجعة الوثائق** والأمثلة
2. **فحص سجلات الأخطاء** في وحدة التحكم
3. **استخدام أدوات التشخيص** المدمجة
4. **التواصل مع فريق التطوير** للمساعدة المتقدمة

---

**تم تطوير هذا النظام بعناية لتوفير أفضل تجربة ممكنة لإدارة الصور في تطبيق المحاسبة. نأمل أن يلبي احتياجاتكم ويحسن من كفاءة عملكم.**
