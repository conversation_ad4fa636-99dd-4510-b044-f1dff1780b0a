import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Button, Space, Statistic, Menu } from 'antd'
import {
  ShopOutlined,
  UserOutlined,
  FileTextOutlined,
  DollarOutlined,
  Bar<PERSON>hartOutlined,
  ArrowLeftOutlined,
  ShoppingOutlined,
  CreditCardOutlined
} from '@ant-design/icons'
import CustomerManagement from './CustomerManagement'
import SalesOrderManagement from './SalesOrderManagement'
import SalesInvoiceManagement from './SalesInvoiceManagement'
import InvoicePaymentManagement from './InvoicePaymentManagement'
import SalesReports from './SalesReports'

interface SalesManagementProps {
  onBack: () => void
  initialView?: ActiveView
}

type ActiveView = 'main' | 'customers' | 'orders' | 'invoices' | 'payments' | 'reports'

const SalesManagement: React.FC<SalesManagementProps> = ({ onBack, initialView = 'main' }) => {
  const [activeView, setActiveView] = useState<ActiveView>(initialView as ActiveView)

  // تحديث activeView عندما يتغير initialView من الخارج
  useEffect(() => {
    setActiveView(initialView as ActiveView)
  }, [initialView])

  const renderContent = () => {
    switch (activeView) {
      case 'customers':
        return <CustomerManagement onBack={() => setActiveView('main')} />
      case 'orders':
        return <SalesOrderManagement onBack={() => setActiveView('main')} />
      case 'invoices':
        return <SalesInvoiceManagement onBack={() => setActiveView('main')} />
      case 'payments':
        return <InvoicePaymentManagement onBack={() => setActiveView('main')} />
      case 'reports':
        return <SalesReports onBack={() => setActiveView('main')} />
      default:
        return (
          <div style={{ padding: '24px' }}>
            <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <h1 style={{ margin: 0, color: '#1890ff', fontSize: '28px' }}>
                  <ShopOutlined style={{ marginLeft: '12px' }} />
                  إدارة المبيعات
                </h1>
                <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '16px' }}>
                  نّام شامل لإدارة المبيعات والعملاء والفواتير
                </p>
              </div>
              <Button 
                icon={<ArrowLeftOutlined />} 
                onClick={onBack}
                size="large"
              >
                رجوع
              </Button>
            </div>

            <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
              <Col xs={24} sm={12} lg={6}>
                <Card>
                  <Statistic
                    title="إجمالي العملاء"
                    value={0}
                    valueStyle={{ color: '#1890ff' }}
                    prefix={<UserOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <Card>
                  <Statistic
                    title="أوامر البيع"
                    value={0}
                    valueStyle={{ color: '#52c41a' }}
                    prefix={<ShoppingOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <Card>
                  <Statistic
                    title="فواتير البيع"
                    value={0}
                    valueStyle={{ color: '#722ed1' }}
                    prefix={<FileTextOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} lg={6}>
                <Card>
                  <Statistic
                    title="إجمالي المبيعات"
                    value={0}
                    valueStyle={{ color: '#fa8c16' }}
                    prefix="₪"
                    precision={0}
                  />
                </Card>
              </Col>
            </Row>

            <Row gutter={[24, 24]}>
              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ 
                    height: '200px',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    border: 'none',
                    borderRadius: '12px'
                  }}
                  styles={{ body: {
                    padding: '24px',
                    color: 'white',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between'
                  } }}
                  onClick={() => setActiveView('customers')}
                >
                  <div>
                    <UserOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>إدارة العملاء</h3>
                    <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                      إضافة وإدارة بيانات العملاء والحدود الائتمانية
                    </p>
                  </div>
                  <Button 
                    type="primary" 
                    ghost 
                    style={{ alignSelf: 'flex-start' }}
                  >
                    إدارة العملاء
                  </Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ 
                    height: '200px',
                    background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                    border: 'none',
                    borderRadius: '12px'
                  }}
                  styles={{ body: {
                    padding: '24px',
                    color: 'white',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between'
                  } }}
                  onClick={() => setActiveView('orders')}
                >
                  <div>
                    <ShoppingOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>أوامر البيع</h3>
                    <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                      إنشاء ومتابعة أوامر البيع وحالتها
                    </p>
                  </div>
                  <Button 
                    type="primary" 
                    ghost 
                    style={{ alignSelf: 'flex-start' }}
                  >
                    إدارة الأوامر
                  </Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ 
                    height: '200px',
                    background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                    border: 'none',
                    borderRadius: '12px'
                  }}
                  styles={{ body: {
                    padding: '24px',
                    color: 'white',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between'
                  } }}
                  onClick={() => setActiveView('invoices')}
                >
                  <div>
                    <FileTextOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>فواتير البيع</h3>
                    <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                      إنشاء ومتابعة فواتير البيع والمدفوعات
                    </p>
                  </div>
                  <Button 
                    type="primary" 
                    ghost 
                    style={{ alignSelf: 'flex-start' }}
                  >
                    إدارة الفواتير
                  </Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{
                    height: '200px',
                    background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                    border: 'none',
                    borderRadius: '12px'
                  }}
                  styles={{ body: {
                    padding: '24px',
                    color: '#333',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between'
                  } }}
                  onClick={() => setActiveView('payments')}
                >
                  <div>
                    <CreditCardOutlined style={{ fontSize: '48px', marginBottom: '16px', color: '#1890ff' }} />
                    <h3 style={{ color: '#333', margin: 0, fontSize: '20px' }}>إدارة المدفوعات</h3>
                    <p style={{ color: '#666', margin: '8px 0 0 0' }}>
                      تسجيل وربط المدفوعات بالفواتير
                    </p>
                  </div>
                  <Button
                    type="primary"
                    style={{ alignSelf: 'flex-start' }}
                  >
                    إدارة المدفوعات
                  </Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{
                    height: '200px',
                    background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                    border: 'none',
                    borderRadius: '12px'
                  }}
                  styles={{ body: {
                    padding: '24px',
                    color: 'white',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between'
                  } }}
                  onClick={() => setActiveView('reports')}
                >
                  <div>
                    <BarChartOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>تقارير المبيعات</h3>
                    <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                      تقارير شاملة لأداء المبيعات والعملاء
                    </p>
                  </div>
                  <Button
                    type="primary"
                    ghost
                    style={{ alignSelf: 'flex-start' }}
                  >
                    عرض التقارير
                  </Button>
                </Card>
              </Col>
            </Row>
          </div>
        )
    }
  }

  return renderContent()
}

export default SalesManagement
