/**
 * نّام تحسين الأداء
 * يوفر debouncing وthrottling وتحسينات DOM
 */

import { resourceManager } from './resourceManager'
import { logger as Logger }  from './logger'

interface DebounceOptions {
  delay: number
  immediate?: boolean
}

interface ThrottleOptions {
  delay: number
  leading?: boolean
  trailing?: boolean
}

class PerformanceOptimizer {
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map()
  private throttleTimers: Map<string, { timer: NodeJS.Timeout | null; lastCall: number }> = new Map()
  private domCache: Map<string, Element[]> = new Map()
  private resourceId: string | null = null

  constructor() {
    // تسجيل النّام في مدير الموارد
    this.resourceId = resourceManager.register({
      name: 'محسن الأداء',
      type: 'other',
      priority: 30,
      cleanup: () => this.cleanup()
    })
  }

  // Debounce function - تأخير تنفيذ الدالة حتى توقف الاستدعاءات
  debounce<T extends (...args: any[]) => any>(
    func: T,
    options: DebounceOptions,
    key?: string
  ): (...args: Parameters<T>) => void {
    const debounceKey = key || func.name || 'anonymous'
    
    return (..._args: Parameters<T>) => {
      const existingTimer = this.debounceTimers.get(debounceKey)
      
      if (existingTimer) {
        clearTimeout(existingTimer)
      }

      if (options.immediate && !existingTimer) {
        func.apply(this, _args)
      }

      const timer = setTimeout(() => {
        this.debounceTimers.delete(debounceKey)
        if (!options.immediate) {
          func.apply(this, _args)
        }
      }, options.delay)

      this.debounceTimers.set(debounceKey, timer)
    }
  }

  // Throttle function - تحديد معدل تنفيذ الدالة
  throttle<T extends (...args: any[]) => any>(
    func: T,
    options: ThrottleOptions,
    key?: string
  ): (...args: Parameters<T>) => void {
    const throttleKey = key || func.name || 'anonymous'
    
    return (..._args: Parameters<T>) => {
      const now = Date.now()
      const throttleData = this.throttleTimers.get(throttleKey)
      
      if (!throttleData) {
        // أول استدعاء
        this.throttleTimers.set(throttleKey, { timer: null, lastCall: now })
        
        if (options.leading !== false) {
          func.apply(this, _args)
        }
        
        return
      }

      const timeSinceLastCall = now - throttleData.lastCall
      
      if (timeSinceLastCall >= options.delay) {
        // يمكن تنفيذ الدالة
        throttleData.lastCall = now
        func.apply(this, _args)
      } else if (options.trailing !== false && !throttleData.timer) {
        // جدولة تنفيذ متأخر
        const remainingTime = options.delay - timeSinceLastCall
        
        throttleData.timer = setTimeout(() => {
          const data = this.throttleTimers.get(throttleKey)
          if (data) {
            data.timer = null
            data.lastCall = Date.now()
          }
          func.apply(this, _args)
        }, remainingTime)
      }
    }
  }

  // تحسين DOM queries مع cache
  optimizedQuerySelector(selector: string, useCache = true): Element | null {
    if (useCache) {
      const cached = this.domCache.get(selector)
      if (cached && cached.length > 0) {
        // التحقق من أن العنصر ما زال في DOM
        if (document.contains(cached[0])) {
          return cached[0]
        } else {
          this.domCache.delete(selector)
        }
      }
    }

    const element = document.querySelector(selector)
    if (element && useCache) {
      this.domCache.set(selector, [element])
    }
    
    return element
  }

  // تحسين DOM queries متعددة مع cache
  optimizedQuerySelectorAll(selector: string, useCache = true): Element[] {
    if (useCache) {
      const cached = this.domCache.get(selector)
      if (cached) {
        // التحقق من أن العناصر ما زالت في DOM
        const validElements = cached.filter(el => document.contains(el))
        if (validElements.length === cached.length) {
          return validElements
        } else {
          this.domCache.delete(selector)
        }
      }
    }

    const elements = Array.from(document.querySelectorAll(selector))
    if (elements.length > 0 && useCache) {
      this.domCache.set(selector, elements)
    }
    
    return elements
  }

  // تنّيف cache DOM
  clearDOMCache(selector?: string): void {
    if (selector) {
      this.domCache.delete(selector)
    } else {
      this.domCache.clear()
    }
  }

  // تحسين معالج الأحداث مع debouncing
  createOptimizedEventHandler<T extends Event>(
    handler: (event: T) => void,
    options: DebounceOptions,
    key?: string
  ): (_event: T) => void {
    return this.debounce(handler, options, key)
  }

  // تحسين scroll handler
  createOptimizedScrollHandler(
    handler: (event: Event) => void,
    delay = 100
  ): (_event: Event) => void {
    return this.throttle(handler, { delay, leading: true, trailing: true }, 'scroll-handler')
  }

  // تحسين resize handler
  createOptimizedResizeHandler(
    handler: (event: Event) => void,
    delay = 250
  ): (_event: Event) => void {
    return this.debounce(handler, { delay }, 'resize-handler')
  }

  // تحسين input handler
  createOptimizedInputHandler(
    handler: (event: Event) => void,
    delay = 300
  ): (_event: Event) => void {
    return this.debounce(handler, { delay }, 'input-handler')
  }

  // تجميع تحديثات DOM
  batchDOMUpdates(updates: (() => void)[]): void {
    // استخدام requestAnimationFrame لتجميع التحديثات
    requestAnimationFrame(() => {
      updates.forEach(update => {
        try {
          update()
        } catch (error) {
          Logger.warn('PerformanceOptimizer', 'خطأ في تحديث DOM:', error)
        }
      })
    })
  }

  // قياس أداء دالة
  measurePerformance<T>(
    func: () => T,
    _label: string
  ): { result: T; duration: number } {
    const start = performance.now()
    const result = func()
    const end = performance.now()
    const duration = end - start
    
    if (duration > 100) {
      Logger.warn('PerformanceOptimizer', `⚠️ عملية بطيئة: ${_label} استغرقت ${duration.toFixed(2)}ms`)
    }
    
    return { result, duration }
  }

  // تنّيف الموارد
  cleanup(): void {
    Logger.info('PerformanceOptimizer', '🧹 تنّيف محسن الأداء...')
    
    // تنّيف debounce timers
    this.debounceTimers.forEach(timer => clearTimeout(timer))
    this.debounceTimers.clear()
    
    // تنّيف throttle timers
    this.throttleTimers.forEach(data => {
      if (data.timer) {
        clearTimeout(data.timer)
      }
    })
    this.throttleTimers.clear()
    
    // تنّيف DOM cache
    this.domCache.clear()
    
    // إلغاء تسجيل المورد
    if (this.resourceId) {
      resourceManager.unregister(this.resourceId)
      this.resourceId = null
    }
    
    Logger.info('PerformanceOptimizer', '✅ تم تنّيف محسن الأداء')
  }

  // الحصول على إحصائيات الأداء
  getStats() {
    return {
      debounceTimers: this.debounceTimers.size,
      throttleTimers: this.throttleTimers.size,
      cachedSelectors: this.domCache.size
    }
  }
}

// إنشاء مثيل واحد
export const performanceOptimizer = new PerformanceOptimizer()

// إتاحة النّام عالمياً
if (typeof window !== 'undefined') {
  ;(window as any).performanceOptimizer = performanceOptimizer
}

export default PerformanceOptimizer
