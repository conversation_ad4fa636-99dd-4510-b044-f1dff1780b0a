/**
 * أداة التحقق من صحة القوالب وإصلاح المشاكل
 * تضمن أن جميع القوالب تحتوي على إعدادات شاملة ومتسقة
 */

import { SafeLogger as Logger } from './logger';

export interface TemplateValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fixedTemplate?: any;
}

/**
 * التحقق من صحة قالب الطباعة
 */
export const validatePrintTemplate = (template: any): TemplateValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const fixedTemplate = { ...template };

  // التحقق من الحقول الأساسية
  if (!template.id) {
    errors.push('معرف القالب مفقود');
  }

  if (!template.name) {
    errors.push('اسم القالب مفقود');
  }

  if (!template.type) {
    errors.push('نوع القالب مفقود');
    fixedTemplate.type = 'custom';
  }

  // التحقق من الإعدادات
  if (!template.settings) {
    warnings.push('إعدادات القالب مفقودة، سيتم استخدام الإعدادات الافتراضية');
    fixedTemplate.settings = getDefaultSettings();
  } else {
    // إصلاح الإعدادات المفقودة
    const defaultSettings = getDefaultSettings();
    const currentSettings = template.settings;

    // التحقق من كل إعداد مطلوب
    Object.keys(defaultSettings).forEach(key => {
      if (currentSettings[key] === undefined || currentSettings[key] === null) {
        warnings.push(`إعداد ${key} مفقود، سيتم استخدام القيمة الافتراضية`);
        fixedTemplate.settings[key] = defaultSettings[key];
      }
    });

    // التحقق من صحة القيم
    if (currentSettings.fontSize && (currentSettings.fontSize < 8 || currentSettings.fontSize > 24)) {
      warnings.push('حجم الخط غير صحيح، سيتم تعديله');
      fixedTemplate.settings.fontSize = 12;
    }

    if (currentSettings.pageSize && !['A4', 'A5', 'A3', 'Letter'].includes(currentSettings.pageSize)) {
      warnings.push('حجم الورق غير صحيح، سيتم تعديله');
      fixedTemplate.settings.pageSize = 'A4';
    }

    if (currentSettings.orientation && !['portrait', 'landscape'].includes(currentSettings.orientation)) {
      warnings.push('اتجاه الصفحة غير صحيح، سيتم تعديله');
      fixedTemplate.settings.orientation = 'portrait';
    }
  }

  // التحقق من التواريخ
  if (!template.createdAt) {
    fixedTemplate.createdAt = new Date().toISOString();
  }

  if (!template.updatedAt) {
    fixedTemplate.updatedAt = new Date().toISOString();
  }

  // التحقق من الحالة
  if (template.isActive === undefined) {
    fixedTemplate.isActive = true;
  }

  if (template.isDefault === undefined) {
    fixedTemplate.isDefault = false;
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fixedTemplate: errors.length === 0 ? fixedTemplate : undefined
  };
};

/**
 * الحصول على الإعدادات الافتراضية الشاملة
 */
export const getDefaultSettings = () => ({
  pageSize: 'A4',
  orientation: 'portrait',
  fontSize: 12,
  fontFamily: 'Arial',
  showHeader: true,
  showFooter: true,
  showLogo: true,
  showSignature: false,
  showTerms: true,
  marginTop: 20,
  marginBottom: 20,
  marginLeft: 15,
  marginRight: 15,
  primaryColor: '#1890ff',
  secondaryColor: '#f0f2f5',
  borderColor: '#d9d9d9'
});

/**
 * إصلاح مجموعة من القوالب
 */
export const validateAndFixTemplates = (templates: any[]): {
  validTemplates: any[];
  errors: string[];
  warnings: string[];
} => {
  const validTemplates: any[] = [];
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  templates.forEach((template, index) => {
    const result = validatePrintTemplate(template);
    
    if (result.isValid && result.fixedTemplate) {
      validTemplates.push(result.fixedTemplate);
    } else {
      Logger.error('TemplateValidator', `قالب غير صحيح في الفهرس ${index}:`, result.errors);
    }

    // إضافة الأخطاء والتحذيرات مع رقم القالب
    result.errors.forEach(error => {
      allErrors.push(`قالب ${index + 1}: ${error}`);
    });

    result.warnings.forEach(warning => {
      allWarnings.push(`قالب ${index + 1}: ${warning}`);
    });
  });

  return {
    validTemplates,
    errors: allErrors,
    warnings: allWarnings
  };
};

/**
 * التحقق من تطابق القوالب مع المعايير
 */
export const checkTemplateConsistency = (templates: any[]): {
  consistent: boolean;
  issues: string[];
  recommendations: string[];
} => {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // التحقق من وجود قالب افتراضي لكل نوع
  const types = ['invoice', 'receipt', 'report'];
  types.forEach(type => {
    const defaultTemplate = templates.find(t => t.type === type && t.isDefault);
    if (!defaultTemplate) {
      issues.push(`لا يوجد قالب افتراضي لنوع ${type}`);
    }
  });

  // التحقق من تطابق الإعدادات الأساسية
  const reportTemplates = templates.filter(t => t.type === 'report');
  if (reportTemplates.length > 1) {
    const firstTemplate = reportTemplates[0];
    const inconsistentTemplates = reportTemplates.filter(t => 
      t.settings?.showLogo !== firstTemplate.settings?.showLogo ||
      t.settings?.showHeader !== firstTemplate.settings?.showHeader
    );

    if (inconsistentTemplates.length > 0) {
      recommendations.push('يُنصح بتوحيد إعدادات العرض (الشعار، الرأسية) لجميع قوالب التقارير');
    }
  }

  // التحقق من الألوان
  const uniqueColors = new Set(templates.map(t => t.settings?.primaryColor).filter(Boolean));
  if (uniqueColors.size > 5) {
    recommendations.push('يُنصح بتقليل عدد الألوان المستخدمة للحصول على تصميم متسق');
  }

  return {
    consistent: issues.length === 0,
    issues,
    recommendations
  };
};

/**
 * إنشاء تقرير شامل عن حالة القوالب
 */
export const generateTemplateReport = (templates: any[]) => {
  const validationResult = validateAndFixTemplates(templates);
  const consistencyResult = checkTemplateConsistency(validationResult.validTemplates);

  const report = {
    totalTemplates: templates.length,
    validTemplates: validationResult.validTemplates.length,
    invalidTemplates: templates.length - validationResult.validTemplates.length,
    errors: validationResult.errors,
    warnings: validationResult.warnings,
    consistencyIssues: consistencyResult.issues,
    recommendations: consistencyResult.recommendations,
    summary: {
      hasErrors: validationResult.errors.length > 0,
      hasWarnings: validationResult.warnings.length > 0,
      isConsistent: consistencyResult.consistent,
      overallHealth: getOverallHealth(validationResult, consistencyResult)
    }
  };

  Logger.info('TemplateValidator', 'تقرير حالة القوالب:', report);
  return report;
};

/**
 * تقييم الحالة العامة للقوالب
 */
const getOverallHealth = (validationResult: any, consistencyResult: any): 'excellent' | 'good' | 'fair' | 'poor' => {
  const errorCount = validationResult.errors.length;
  const warningCount = validationResult.warnings.length;
  const issueCount = consistencyResult.issues.length;

  if (errorCount === 0 && warningCount === 0 && issueCount === 0) {
    return 'excellent';
  } else if (errorCount === 0 && warningCount <= 2 && issueCount <= 1) {
    return 'good';
  } else if (errorCount <= 1 && warningCount <= 5 && issueCount <= 3) {
    return 'fair';
  } else {
    return 'poor';
  }
};
