// Main Dashboard Component
export { default as FiscalPeriodDashboard } from './FiscalPeriodDashboard';

// Individual Components
export { default as FiscalPeriodManager } from './FiscalPeriodManager';
export { default as ValidationDashboard } from './ValidationDashboard';
// export { default as ClosingWizard } from '../src/components/FiscalPeriods/ClosingWizard'; // TODO: Implement ClosingWizard
export { default as ClosingReports } from './ClosingReports';
export { default as ClosingNotifications } from './ClosingNotifications';

// Re-export types for convenience
export type {
  FiscalPeriod,
  FiscalPeriodStatus,
  ValidationResult,
  ValidationSummary,
  ClosingProgress,
  ClosingReport,
  PeriodComparison,
  ClosingNotification,
  NotificationSettings
} from '../../../renderer/src/types/fiscalPeriod';
