import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ton,
  Table,
  Tabs,
  Tooltip,
  Alert,
  Tag,
  Progress,
  Divider,
  Space,
  Row,
  Col,
  Statistic,
  Spin
} from 'antd';
import {
  PrinterOutlined,
  DownloadOutlined,
  MailOutlined
} from '@ant-design/icons';
import { FiscalPeriod, ClosingReport, PeriodComparison } from '../../src/types/fiscalPeriod';
import { fiscalPeriodApi } from '../../src/services/fiscalPeriodApi';

const { Title } = Typography;
const { TabPane } = Tabs;

interface ClosingReportsProps {
  period: FiscalPeriod;
}

const ClosingReports: React.FC<ClosingReportsProps> = ({ period }) => {
  const [activeTab, setActiveTab] = useState('summary');
  const [closingReport, setClosingReport] = useState<ClosingReport | null>(null);
  const [periodComparison, setPeriodComparison] = useState<PeriodComparison | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadReports();
  }, [period.id]);

  const loadReports = async () => {
    try {
      setLoading(true);
      const [report, comparison] = await Promise.all([
        fiscalPeriodApi.getClosingReport(period.id),
        fiscalPeriodApi.getPeriodComparison(period.id.toString())
      ]);

      setClosingReport((report as any).data || report);
      setPeriodComparison(comparison);
    } catch (err) {
      setError('فشل في تحميل التقارير');
      console.error('Error loading reports:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadReport = async (reportType: string) => {
    try {
      await fiscalPeriodApi.downloadReport(period.id.toString(), reportType);
    } catch (err) {
      console.error('Download error:', err);
    }
  };

  const handleEmailReport = async (reportType: string) => {
    try {
      await fiscalPeriodApi.emailReport(period.id.toString(), reportType);
    } catch (err) {
      console.error('Email error:', err);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>جاري تحميل التقارير...</div>
      </div>
    );
  }

  const closingEntriesColumns = [
    {
      title: 'رقم القيد',
      dataIndex: 'entry_number',
      key: 'entry_number',
    },
    {
      title: 'التاريخ',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => new Date(date).toLocaleDateString('ar-SA'),
    },
    {
      title: 'الوصف',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => formatCurrency(amount),
    },
  ];

  const trialBalanceColumns = [
    {
      title: 'رقم الحساب',
      dataIndex: 'account_code',
      key: 'account_code',
    },
    {
      title: 'اسم الحساب',
      dataIndex: 'account_name',
      key: 'account_name',
    },
    {
      title: 'مدين',
      dataIndex: 'debit',
      key: 'debit',
      render: (amount: number) => formatCurrency(amount),
    },
    {
      title: 'دائن',
      dataIndex: 'credit',
      key: 'credit',
      render: (amount: number) => formatCurrency(amount),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ padding: '24px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
            <Title level={3}>
              تقارير الإقفال - {period.period_name}
            </Title>
            <Space>
              <Button icon={<PrinterOutlined />} onClick={() => window.print()}>
                طباعة
              </Button>
              <Button icon={<DownloadOutlined />} onClick={() => handleDownloadReport('summary')}>
                تحميل
              </Button>
              <Button icon={<MailOutlined />} onClick={() => handleEmailReport('summary')}>
                إرسال بالبريد
              </Button>
            </Space>
          </div>

          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              closable
              onClose={() => setError(null)}
              style={{ marginBottom: 16 }}
            />
          )}

          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="ملخص الإقفال" key="summary">
              <Row gutter={[16, 16]}>
                <Col span={6}>
                  <Statistic
                    title="إجمالي الإيرادات"
                    value={closingReport?.totalRevenue || 0}
                    formatter={(value) => formatCurrency(Number(value))}
                    valueStyle={{ color: '#3f8600' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="إجمالي المصروفات"
                    value={closingReport?.totalExpenses || 0}
                    formatter={(value) => formatCurrency(Number(value))}
                    valueStyle={{ color: '#cf1322' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="صافي الربح"
                    value={closingReport?.netIncome || 0}
                    formatter={(value) => formatCurrency(Number(value))}
                    valueStyle={{ color: closingReport?.netIncome >= 0 ? '#3f8600' : '#cf1322' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="عدد القيود"
                    value={(closingReport as any)?.totalEntries || closingReport?.entriesCreated || 0}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
              </Row>

              <Divider />

              <Title level={4}>قيود الإقفال</Title>
              <Table
                dataSource={closingReport?.closingEntries || []}
                columns={closingEntriesColumns}
                pagination={{ pageSize: 10 }}
                scroll={{ x: 800 }}
              />
            </TabPane>

            <TabPane tab="ميزان المراجعة" key="trial-balance">
              <Table
                dataSource={(closingReport as any)?.trialBalance || []}
                columns={trialBalanceColumns}
                pagination={{ pageSize: 15 }}
                scroll={{ x: 800 }}
              />
            </TabPane>

            <TabPane tab="مقارنة الفترات" key="comparison">
              {periodComparison && (
                <div>
                  <Title level={4}>مقارنة مع الفترة السابقة</Title>
                  <Table
                    dataSource={[
                      {
                        key: 'revenue',
                        item: 'الإيرادات',
                        current: (periodComparison.currentPeriod as any).revenue || 0,
                        previous: (periodComparison.previousPeriod as any).revenue || 0,
                        change: (periodComparison as any).revenueChange || 0,
                        changePercent: (periodComparison as any).revenueChangePercent || 0,
                      },
                      {
                        key: 'expenses',
                        item: 'المصروفات',
                        current: (periodComparison.currentPeriod as any).expenses || 0,
                        previous: (periodComparison.previousPeriod as any).expenses || 0,
                        change: (periodComparison as any).expensesChange || 0,
                        changePercent: (periodComparison as any).expensesChangePercent || 0,
                      },
                      {
                        key: 'netIncome',
                        item: 'صافي الدخل',
                        current: (periodComparison.currentPeriod as any).netIncome || 0,
                        previous: (periodComparison.previousPeriod as any).netIncome || 0,
                        change: (periodComparison as any).netIncomeChange || 0,
                        changePercent: (periodComparison as any).netIncomeChangePercent || 0,
                      },
                    ]}
                    columns={[
                      {
                        title: 'البند',
                        dataIndex: 'item',
                        key: 'item',
                      },
                      {
                        title: 'الفترة الحالية',
                        dataIndex: 'current',
                        key: 'current',
                        render: (value: number) => formatCurrency(value),
                      },
                      {
                        title: 'الفترة السابقة',
                        dataIndex: 'previous',
                        key: 'previous',
                        render: (value: number) => formatCurrency(value),
                      },
                      {
                        title: 'التغيير',
                        dataIndex: 'change',
                        key: 'change',
                        render: (value: number) => (
                          <Tag color={value >= 0 ? 'green' : 'red'}>
                            {formatCurrency(value)}
                          </Tag>
                        ),
                      },
                      {
                        title: 'النسبة المئوية',
                        dataIndex: 'changePercent',
                        key: 'changePercent',
                        render: (value: number) => (
                          <Tag color={value >= 0 ? 'green' : 'red'}>
                            {formatPercentage(value)}
                          </Tag>
                        ),
                      },
                    ]}
                    pagination={false}
                  />
                </div>
              )}
            </TabPane>

            <TabPane tab="سجل المراجعة" key="audit">
              <Table
                dataSource={closingReport?.auditLog || (closingReport as any)?.auditTrail || []}
                columns={[
                  {
                    title: 'الوقت',
                    dataIndex: 'timestamp',
                    key: 'timestamp',
                    render: (timestamp: string) => new Date(timestamp).toLocaleString('ar-SA'),
                  },
                  {
                    title: 'المستخدم',
                    dataIndex: 'user',
                    key: 'user',
                  },
                  {
                    title: 'الإجراء',
                    dataIndex: 'action',
                    key: 'action',
                  },
                  {
                    title: 'التفاصيل',
                    dataIndex: 'details',
                    key: 'details',
                  },
                ]}
                pagination={{ pageSize: 10 }}
              />
            </TabPane>
          </Tabs>
        </div>
      </Card>
    </div>
  );
};

export default ClosingReports;
