/**
 * تقرير أداء الموظفين المحسن
 * تقرير شامل لتقييم أداء الموظفين باستخدام النظام الموحد
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const EmployeePerformanceReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'employee_performance' as ReportType}
      title="تقرير أداء الموظفين"
      description="تقييم شامل لأداء الموظفين مع مؤشرات الإنتاجية والجودة والكفاءة"
      showDateRange={true}
      showEmployeeFilter={true}
      showDepartmentFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="employee_performance_report"
      defaultFilters={{
        sortBy: 'avg_performance_score',
        sortOrder: 'desc'
      }}
    />
  )
}

export default EmployeePerformanceReport