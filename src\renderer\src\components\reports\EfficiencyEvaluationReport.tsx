/**
 * تقرير تقييم الكفاءة
 * تقرير شامل لتقييم كفاءة الموظفين والأقسام
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const EfficiencyEvaluationReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'efficiency_evaluation' as ReportType}
      title="تقرير تقييم الكفاءة"
      description="تقييم شامل لكفاءة الموظفين مع مؤشرات الأداء والإنتاجية"
      showDateRange={true}
      showEmployeeFilter={true}
      showDepartmentFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}

      exportFileName="efficiency_evaluation_report"
      defaultFilters={{
        sortBy: 'efficiency_score',
        sortOrder: 'desc'
      }}
    />
  )
}

export default EfficiencyEvaluationReport
