import React from 'react';
import { Tag, Typography,  Tooltip } from 'antd';
import { 
  DollarOutlined, 
  UserOutlined, 
  BankOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';
import { SafeLogger as Logger } from '../../utils/logger'
import UniversalReport from './UniversalReport';
import type { ReportData, ReportType } from '../../types/reports';
import { createDefaultFilters } from '../../types/reports';

const { Text } = Typography;

const EmployeePayrollReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      Logger.info('EmployeePayrollReport', '💰 بدء إنشاء تقرير الرواتب والمكافآت...');
      Logger.info('EmployeePayrollReport', '🔍 الفلاتر المطبقة:', filters);

      let payrollData: any[] = [];

      if (!window.electronAPI || !window.electronAPI.getEmployeePayrollReport) {
        Logger.info('EmployeePayrollReport', '⚠️ التطبيق يعمل في وضع المتصفح أو الدالة غير متوفرة - استخدام بيانات وهمية');

        // بيانات وهمية للرواتب
        payrollData = [
          {
            employee_id: 1,
            employee_name: 'أحمد محمد علي',
            employee_code: 'EMP001',
            department_name: 'قسم المحاسبة',
            basic_salary: 4500,
            overtime_amount: 650,
            allowances: 300,
            gross_salary: 5450,
            total_deductions: 654,
            net_salary: 4796,
            bonus: 200,
            commission: 150
          },
          {
            employee_id: 2,
            employee_name: 'فاطمة أحمد حسن',
            employee_code: 'EMP002',
            department_name: 'قسم المبيعات',
            basic_salary: 3800,
            overtime_amount: 420,
            allowances: 250,
            gross_salary: 4470,
            total_deductions: 447,
            net_salary: 4023,
            bonus: 300,
            commission: 200
          },
          {
            employee_id: 3,
            employee_name: 'محمد عبدالله سالم',
            employee_code: 'EMP003',
            department_name: 'قسم الإنتاج',
            basic_salary: 3200,
            overtime_amount: 380,
            allowances: 200,
            gross_salary: 3780,
            total_deductions: 378,
            net_salary: 3402,
            bonus: 100,
            commission: 80
          },
          {
            employee_id: 4,
            employee_name: 'سارة خالد محمود',
            employee_code: 'EMP004',
            department_name: 'قسم الموارد البشرية',
            basic_salary: 4200,
            overtime_amount: 0,
            allowances: 350,
            gross_salary: 4550,
            total_deductions: 455,
            net_salary: 4095,
            bonus: 250,
            commission: 0
          }
        ];
      } else {
        // استعلام البيانات من قاعدة البيانات
        const response = await window.electronAPI.getEmployeePayrollReport({
          departmentId: filters.departmentId,
          employeeId: filters.employeeId,
          month: filters.month,
          year: filters.year
        });

        if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
          throw new Error('لا توجد بيانات متاحة');
        }

        payrollData = response.data;
      }

      // تعريف الأعمدة
      const columns = [
        {
          title: 'الموظف',
          key: 'employee',
          render: (record: any) => (
            <div>
              <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
                <UserOutlined style={{ marginLeft: '8px' }} />
                {record.employee_name}
              </div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.employee_code}
              </Text>
            </div>
          ),
          width: 200
        },
        {
          title: 'القسم',
          key: 'department_name',
          format: 'text' as const,
          render: (record: any) => (
            <Tag color="blue">{record.department_name || 'غير محدد'}</Tag>
          ),
          width: 120,
          align: 'center' as const
        },
        {
          title: 'نوع التوظيف',
          key: 'employment_type',
          format: 'text' as const,
          render: (record: any) => {
            const type = record.employment_type;
            const _color = type === 'full_time' ? 'green' : type === 'part_time' ? 'orange' : 'purple';
            const text = type === 'full_time' ? 'دوام كامل' :
                        type === 'part_time' ? 'دوام جزئي' :
                        type === 'contract' ? 'عقد' : type;
            return <Tag color={_color}>{text}</Tag>;
          },
          width: 120,
          align: 'center' as const,
          filterable: true
        },
        {
          title: 'نوع الراتب',
          key: 'salary_type',
          format: 'text' as const,
          render: (record: any) => {
            const type = record.salary_type;
            const _color = type === 'monthly' ? 'blue' : 'cyan';
            const text = type === 'monthly' ? 'شهري' : type === 'hourly' ? 'بالساعة' : type;
            return <Tag color={_color}>{text}</Tag>;
          },
          width: 100,
          align: 'center' as const,
          filterable: true
        },
        {
          title: 'الراتب الأساسي',
          key: 'basic_salary',
          format: 'currency' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <BankOutlined style={{ color: '#1890ff', marginLeft: '4px' }} />
              <strong style={{ color: '#1890ff' }}>
                {record.basic_salary?.toLocaleString() || 0} ₪
              </strong>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'متوسط الإضافي',
          key: 'avg_overtime_amount',
          format: 'currency' as const,
          render: (record: any) => {
            const amount = record.avg_overtime_amount;
            if (!amount || amount === 0) return <Text type="secondary">-</Text>;
            return (
              <div style={{ textAlign: 'center' }}>
                <RiseOutlined style={{ color: '#52c41a', marginLeft: '4px' }} />
                <strong style={{ color: '#52c41a' }}>
                  {Math.round(amount).toLocaleString()} ₪
                </strong>
              </div>
            );
          },
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'متوسط البدلات',
          key: 'avg_allowances',
          format: 'currency' as const,
          render: (record: any) => {
            const amount = record.avg_allowances;
            if (!amount || amount === 0) return <Text type="secondary">-</Text>;
            return (
              <div style={{ textAlign: 'center' }}>
                <TrophyOutlined style={{ color: '#fa8c16', marginLeft: '4px' }} />
                <strong style={{ color: '#fa8c16' }}>
                  {Math.round(amount).toLocaleString()} ₪
                </strong>
              </div>
            );
          },
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'الراتب الإجمالي',
          key: 'gross_salary',
          format: 'currency' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <DollarOutlined style={{ color: '#722ed1', marginLeft: '4px' }} />
              <strong style={{ color: '#722ed1' }}>
                {Math.round(record.gross_salary || 0).toLocaleString()} ₪
              </strong>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'الخصومات',
          key: 'total_deductions',
          format: 'currency' as const,
          render: (record: any) => {
            const amount = record.total_deductions;
            if (!amount || amount === 0) return <Text type="secondary">-</Text>;
            return (
              <div style={{ textAlign: 'center' }}>
                <FallOutlined style={{ color: '#ff4d4f', marginLeft: '4px' }} />
                <strong style={{ color: '#ff4d4f' }}>
                  -{Math.round(amount).toLocaleString()} ₪
                </strong>
              </div>
            );
          },
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'الراتب الصافي',
          key: 'net_salary',
          format: 'currency' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <BankOutlined style={{ color: '#52c41a', marginLeft: '4px' }} />
              <strong style={{ color: '#52c41a', fontSize: '16px' }}>
                {Math.round(record.net_salary || 0).toLocaleString()} ₪
              </strong>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'إجمالي المدفوع',
          key: 'total_paid',
          format: 'currency' as const,
          render: (record: any) => (
            <Tooltip title="إجمالي المبلغ المدفوع للموظف">
              <div style={{ textAlign: 'center' }}>
                <strong style={{ color: '#1890ff', fontSize: '14px' }}>
                  {Math.round(record.total_paid || 0).toLocaleString()} ₪
                </strong>
              </div>
            </Tooltip>
          ),
          width: 140,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'عدد الرواتب',
          key: 'payroll_records_count',
          format: 'number' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <Tag color="purple">{record.payroll_records_count || 0} راتب</Tag>
            </div>
          ),
          width: 100,
          align: 'center' as const,
          sortable: true
        }
      ];

      // حساب الإحصائيات
      const totalEmployees = payrollData.length;
      const totalBasicSalaries = payrollData.reduce((sum, emp) => sum + (emp.basic_salary || 0), 0);
      const totalNetPaid = payrollData.reduce((sum, emp) => sum + (emp.net_salary || 0), 0);
      const totalOvertimePaid = payrollData.reduce((sum, emp) => sum + (emp.overtime_amount || 0), 0);
      const avgBasicSalary = totalEmployees > 0 ? totalBasicSalaries / totalEmployees : 0;
      const highestPaidEmployee = payrollData.reduce((max: any, emp: any) =>
        (emp.net_salary > (max?.net_salary || 0)) ? emp : max, payrollData[0] || null
      );
      const lowestPaidEmployee = payrollData.reduce((min: any, emp: any) =>
        (emp.net_salary < (min?.net_salary || Infinity)) ? emp : min, payrollData[0] || null
      );

      Logger.info('EmployeePayrollReport', '✅ تم إنشاء تقرير الرواتب بنجاح: ${totalEmployees} موظف');

      return {
        title: 'تقرير الرواتب والمكافآت',
        data: payrollData,
        columns,
        summary: {
          totalEmployees,
          totalBasicSalaries: Math.round(totalBasicSalaries),
          avgBasicSalary: Math.round(avgBasicSalary),
          totalNetPaid: Math.round(totalNetPaid),
          totalOvertimePaid: Math.round(totalOvertimePaid),
          highestPaidEmployee,
          lowestPaidEmployee,
          totalGrossSalaries: payrollData.reduce((sum, emp) => sum + (emp.gross_salary || 0), 0),
          totalDeductions: payrollData.reduce((sum, emp) => sum + (emp.total_deductions || 0), 0)
        },
        metadata: {
          reportType: 'employee_payroll' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: payrollData.length
        }
      };
    } catch (_error) {
      Logger.error('EmployeePayrollReport', 'خطأ في إنشاء تقرير الرواتب:', _error);
      throw new Error('فشل في إنشاء تقرير الرواتب');
    }
  };

  return (
    <UniversalReport
      reportType={'employee_payroll' as ReportType}
      title="تقرير الرواتب والمكافآت"
      description="تقرير شامل لرواتب الموظفين والمكافآت والخصومات مع إحصائيات مالية تفصيلية"
      onGenerateReport={generateReport}
      defaultFilters={createDefaultFilters('employee_payroll')}
      showDateRange={true}
      showEmployeeFilter={true}
      showDepartmentFilter={true}
      showAdvancedSearch={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default EmployeePayrollReport;
