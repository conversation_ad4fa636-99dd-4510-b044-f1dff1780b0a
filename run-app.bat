@echo off
echo Starting ZET.IA Accounting Application...
echo.

REM Check if the built application exists
if exist "release-new\win-unpacked\ZET.IA.exe" (
    echo Running from release-new directory...
    start "" "release-new\win-unpacked\ZET.IA.exe"
) else if exist "dist\main\main\main.js" (
    echo Running from development build...
    npm run electron
) else (
    echo Error: Application not found!
    echo Please run: npm run build
    echo Then run: npm run dist
    pause
    exit /b 1
)

echo Application started successfully!
echo You can close this window.
timeout /t 3 /nobreak >nul
