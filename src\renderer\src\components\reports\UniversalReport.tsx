/**
 * مكون التقرير الموحد
 * مكون شامل لعرض جميع أنواع التقارير بطريقة موحدة
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Spin,
  Row,
  Col,
  Statistic,
  Typography,
  Divider,
  DatePicker,
  Select,
  Input,
  Progress,
  Tooltip,
  App
} from 'antd'
import {
  ReloadOutlined,
  DownloadOutlined,
  PrinterOutlined,
  SearchOutlined,
  FilterOutlined,
  EyeOutlined,
  DatabaseOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import { 
  ReportType, 
  ReportData, 
  ReportFilters, 
  ReportComponentProps,
  formatValue
} from '../../types/reports'
import { UniversalReportService } from '../../services/UniversalReportService'
import { UnifiedPrintButton } from '../common'
import ExcelExportButton from './ExcelExportButton'
import PDFExportButton from './PDFExportButton'
import ReportTemplateSelector from './ReportTemplateSelector'
import { SafeLogger as Logger } from '../../utils/logger'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

// دالة لتحديد فئة التقرير
const getReportCategory = (reportType: ReportType): string => {
  const categoryMap: Record<string, string> = {
    // التقارير المالية
    'profitability': 'financial',
    'profit_loss': 'financial',
    'cash_flow': 'financial',
    'cash_flow_analysis': 'financial',
    'balance_sheet': 'financial',
    'income_statement': 'financial',
    'bank_reconciliation': 'financial',
    'customer_aging': 'financial',
    'customer_analysis': 'financial',
    'financial_summary': 'financial',

    // تقارير المخزون
    'inventory_detailed': 'inventory',
    'inventory_movements': 'inventory',
    'inventory_audit': 'inventory',
    'material_consumption': 'inventory',
    'low_stock': 'inventory',
    'advanced_inventory': 'inventory',
    'abc_analysis': 'inventory',
    'abc-analysis': 'inventory',
    'item_warehouse_distribution': 'inventory',
    'item-warehouse-distribution': 'inventory',

    // تقارير المبيعات
    'sales_by_customer': 'sales',
    'sales_by_product': 'sales',
    'sales_by_region': 'sales',
    'monthly_sales': 'sales',
    'sales_returns': 'sales',
    'top_profitable_customers': 'sales',

    // تقارير المشتريات
    'purchases_by_supplier': 'purchase',
    'purchases_by_item': 'purchase',
    'supplier_payables': 'purchase',
    'purchase_analysis': 'purchase',
    'cost_analysis': 'purchase',
    'supplier_price_comparison': 'purchase',
    'supplier_quality': 'purchase',
    'supplier_analysis': 'purchase',

    // تقارير الإنتاج
    'production_orders': 'production',
    'production_efficiency': 'production',
    'production_costs': 'production',
    'production_schedule': 'production',
    'production_quality': 'production',
    'production_workers_performance': 'production',
    'production_materials_consumption': 'production',
    'production_profitability': 'production',

    // تقارير الموظفين
    'employee_attendance': 'employees',
    'employee-attendance': 'employees',
    'employee_payroll': 'employees',
    'employee_leaves': 'employees',
    'employee_performance': 'employees',
    'employee_overtime': 'employees',
    'employee_analysis': 'employees',
    'employee-analysis': 'employees',
    'salary_comparison': 'employees',
    'efficiency_evaluation': 'employees',

    // تقارير الدهان
    'paint_by_customer': 'paint',
    'paint_by_type': 'paint',
    'monthly_paint': 'paint',
    'paint_profitability': 'paint',
    'paint_performance': 'paint',
    'paint_quality': 'paint'
  };

  return categoryMap[reportType] || 'financial';
}

interface UniversalReportProps extends ReportComponentProps {
  reportType: ReportType
}

const UniversalReport: React.FC<UniversalReportProps> = ({
  reportType,
  title,
  description,
  defaultFilters = {},
  showDateRange = true,
  showWarehouseFilter = false,
  showCategoryFilter = false,
  showItemFilter = false,
  showSupplierFilter = false,
  showCustomerFilter = false,
  showEmployeeFilter: _showEmployeeFilter = false,
  showDepartmentFilter: _showDepartmentFilter = false,
  showStatusFilter = false,
  showMovementTypeFilter: _showMovementTypeFilter = false,
  showAmountRangeFilter = false,
  showAdvancedSearch = true,
  showStatistics = true,
  showSummary = true,
  showExportOptions = true,
  showPrintOptions = true,
  customFilters,
  customActions,
  exportFileName,
  onDataLoad,
  onError,
  onExport
}) => {
  const [loading, setLoading] = useState(false)
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [filters, setFilters] = useState<ReportFilters>(defaultFilters)
  const [searchTerm, setSearchTerm] = useState('')
  const [loadProgress, setLoadProgress] = useState(0)
  const [cacheHit, setCacheHit] = useState(false)
  const [loadTime, setLoadTime] = useState(0)
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null)

  const { message } = App.useApp()
  const reportService = UniversalReportService.getInstance()

  // تحسين الأداء: تخزين مؤقت للبيانات المفلترة
  const filteredData = useMemo(() => {
    if (!reportData?.data || !searchTerm) return reportData?.data || []

    return reportData.data.filter((item: any) => {
      return Object.values(item).some((value: any) =>
        String(value).toLowerCase().includes(searchTerm.toLowerCase())
      )
    })
  }, [reportData?.data, searchTerm])

  // تحسين الأداء: تخزين مؤقت للإحصائيات
  const _memoizedStatistics = useMemo(() => {
    return reportData?.statistics || []
  }, [reportData?.statistics])

  // تحميل التقرير مع تحسينات الأداء
  const loadReport = useCallback(async () => {
    const startTime = performance.now()

    try {
      setLoading(true)
      setLoadProgress(0)
      setCacheHit(false)

      Logger.info('UniversalReport', `تحميل تقرير: ${reportType}`)

      // محاكاة تقدم التحميل
      const progressInterval = setInterval(() => {
        setLoadProgress(prev => Math.min(prev + 10, 90))
      }, 100)

      const result = await reportService.generateReport(reportType, filters, {
        cacheResults: true,
        cacheDuration: 300000, // 5 دقائق
        maxRecords: 10000
      })

      clearInterval(progressInterval)
      setLoadProgress(100)

      const endTime = performance.now()
      const duration = endTime - startTime
      setLoadTime(duration)

      if (result.success && result.data) {
        setReportData(result.data)
        setCacheHit(result.fromCache || false)
        onDataLoad?.(result.data)

        const recordCount = result.data.data?.length || 0

        // إشعارات محسنة حسب نوع التحميل
        if (result.fromCache) {
          message.success(`تم تحميل التقرير من الذاكرة المؤقتة (${recordCount} سجل)`)
        } else if (result.isLazyLoaded) {
          message.info(`تم تحميل الدفعة الأولى (${recordCount} من ${result.totalRecords} سجل)`)
        } else {
          message.success(`تم تحميل التقرير بنجاح (${recordCount} سجل)`)
        }

        // عرض معلومات الضغط إذا كانت متوفرة
        if (result.compressionRatio && result.compressionRatio > 1.5) {
          Logger.info('UniversalReport', `تم ضغط البيانات بنسبة ${result.compressionRatio.toFixed(1)}x`)
        }
      } else {
        const errorMsg = result.error || 'فشل في تحميل التقرير'
        message.error(errorMsg)
        onError?.(errorMsg)
      }
    } catch (error) {
      Logger.error('UniversalReport', 'خطأ في تحميل التقرير:', error)
      const errorMsg = error instanceof Error ? error.message : 'خطأ غير معروف'
      message.error(errorMsg)
      onError?.(errorMsg)
    } finally {
      setLoading(false)
    }
  }, [reportType, filters, reportService, onDataLoad, onError])

  // تحميل التقرير عند التحميل الأول أو تغيير الفلاتر
  useEffect(() => {
    loadReport()
  }, [loadReport])

  // تحديث الفلاتر
  const updateFilters = (newFilters: Partial<ReportFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  // فلترة البيانات محلياً (تم دمجها مع useMemo أعلاه)

  // إنشاء أعمدة الجدول
  const tableColumns = reportData?.columns.map(col => ({
    title: col.title,
    dataIndex: col.key,
    key: col.key,
    width: col.width,
    align: col.align,
    sorter: col.sortable !== false,
    render: (value: any, record: any, index: number) => {
      if (col.render) {
        return col.render(value, record, index)
      }
      return formatValue(value, col.format)
    }
  })) || []

  // معالج التصدير
  const _handleExport = (format: 'excel' | 'pdf' | 'csv') => {
    if (reportData) {
      onExport?.(format, reportData)
    }
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* رأس التقرير */}
      <Card style={{ marginBottom: '16px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              {title}
            </Title>
            {description && (
              <Text type="secondary">{description}</Text>
            )}
            {/* مؤشر الأداء */}
            {loading && (
              <div style={{ marginTop: '8px' }}>
                <Progress
                  percent={loadProgress}
                  size="small"
                  status="active"
                  format={() => `${loadProgress}%`}
                />
              </div>
            )}
            {!loading && loadTime > 0 && (
              <div style={{ marginTop: '4px' }}>
                <Space size="small">
                  <Tooltip title="وقت التحميل">
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      ⏱️ {(loadTime / 1000).toFixed(2)}ث
                    </Text>
                  </Tooltip>
                  {cacheHit && (
                    <Tooltip title="تم التحميل من التخزين المؤقت">
                      <Text type="success" style={{ fontSize: '12px' }}>
                        💾 مخزن مؤقتاً
                      </Text>
                    </Tooltip>
                  )}
                </Space>
              </div>
            )}
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadReport}
                loading={loading}
              >
                تحديث
              </Button>
              {customActions}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* فلاتر التقرير */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          {showDateRange && (
            <Col xs={24} sm={12} md={8}>
              <Text strong>الفترة الزمنية:</Text>
              <RangePicker
                style={{ width: '100%', marginTop: '4px' }}
                value={filters.dateRange as [dayjs.Dayjs, dayjs.Dayjs]}
                onChange={(dates) => updateFilters({ dateRange: dates })}
                format="YYYY-MM-DD"
                placeholder={['من تاريخ (اختياري)', 'إلى تاريخ (اختياري)']}
                allowClear
              />
              {!filters.dateRange && (
                <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginTop: '2px' }}>
                  عدم تحديد التواريخ يعني كل المدة
                </Text>
              )}
            </Col>
          )}
          
          {showWarehouseFilter && (
            <Col xs={24} sm={12} md={8}>
              <Text strong>المخزن:</Text>
              <Select
                style={{ width: '100%', marginTop: '4px' }}
                placeholder="اختر المخزن"
                allowClear
                value={filters.warehouseId}
                onChange={(value) => updateFilters({ warehouseId: value })}
              >
                <Option value={1}>المخزن الرئيسي</Option>
                <Option value={2}>مخزن فرعي</Option>
              </Select>
            </Col>
          )}

          {showCategoryFilter && (
            <Col xs={24} sm={12} md={8}>
              <Text strong>الفئة:</Text>
              <Select
                style={{ width: '100%', marginTop: '4px' }}
                placeholder="اختر الفئة"
                allowClear
                value={filters.categoryId}
                onChange={(value) => updateFilters({ categoryId: value })}
              >
                <Option value={1}>فئة أ</Option>
                <Option value={2}>فئة ب</Option>
              </Select>
            </Col>
          )}

          {showCustomerFilter && (
            <Col xs={24} sm={12} md={8}>
              <Text strong>العميل:</Text>
              <Select
                style={{ width: '100%', marginTop: '4px' }}
                placeholder="اختر العميل"
                allowClear
                showSearch
                value={filters.customerId}
                onChange={(value) => updateFilters({ customerId: value })}
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                <Option value={1}>عميل تجريبي 1</Option>
                <Option value={2}>عميل تجريبي 2</Option>
              </Select>
            </Col>
          )}

          {showSupplierFilter && (
            <Col xs={24} sm={12} md={8}>
              <Text strong>المورد:</Text>
              <Select
                style={{ width: '100%', marginTop: '4px' }}
                placeholder="اختر المورد"
                allowClear
                showSearch
                value={filters.supplierId}
                onChange={(value) => updateFilters({ supplierId: value })}
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                <Option value={1}>مورد تجريبي 1</Option>
                <Option value={2}>مورد تجريبي 2</Option>
              </Select>
            </Col>
          )}

          {showItemFilter && (
            <Col xs={24} sm={12} md={8}>
              <Text strong>الصنف:</Text>
              <Select
                style={{ width: '100%', marginTop: '4px' }}
                placeholder="اختر الصنف"
                allowClear
                showSearch
                value={filters.itemId}
                onChange={(value) => updateFilters({ itemId: value })}
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                <Option value={1}>صنف تجريبي 1</Option>
                <Option value={2}>صنف تجريبي 2</Option>
              </Select>
            </Col>
          )}

          {showStatusFilter && (
            <Col xs={24} sm={12} md={8}>
              <Text strong>الحالة:</Text>
              <Select
                style={{ width: '100%', marginTop: '4px' }}
                placeholder="اختر الحالة"
                allowClear
                value={filters.status}
                onChange={(value) => updateFilters({ status: value })}
              >
                <Option value="active">نشط</Option>
                <Option value="inactive">غير نشط</Option>
                <Option value="pending">في الانتظار</Option>
                <Option value="completed">مكتمل</Option>
              </Select>
            </Col>
          )}

          {showAmountRangeFilter && (
            <Col xs={24} sm={12} md={8}>
              <Text strong>نطاق المبلغ:</Text>
              <Space.Compact style={{ marginTop: '4px', width: '100%' }}>
                <Input
                  style={{ width: '50%' }}
                  placeholder="من"
                  type="number"
                  value={filters.amountFrom}
                  onChange={(e) => updateFilters({ amountFrom: e.target.value ? Number(e.target.value) : undefined })}
                />
                <Input
                  style={{ width: '50%' }}
                  placeholder="إلى"
                  type="number"
                  value={filters.amountTo}
                  onChange={(e) => updateFilters({ amountTo: e.target.value ? Number(e.target.value) : undefined })}
                />
              </Space.Compact>
            </Col>
          )}

          {showAdvancedSearch && (
            <Col xs={24} sm={12} md={8}>
              <Text strong>البحث:</Text>
              <Input
                style={{ marginTop: '4px' }}
                placeholder="البحث في البيانات..."
                prefix={<SearchOutlined />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                allowClear
              />
            </Col>
          )}

          {customFilters}
        </Row>
      </Card>

      {/* إحصائيات التقرير */}
      {showStatistics && reportData?.statistics && (
        <Card style={{ marginBottom: '16px' }}>
          <Row gutter={[16, 16]}>
            {reportData.statistics.map((stat, index) => (
              <Col xs={12} sm={8} md={6} key={index}>
                <Statistic
                  title={stat.title}
                  value={stat.value}
                  valueStyle={{ color: stat.color }}
                  prefix={stat.icon}
                />
              </Col>
            ))}
          </Row>
        </Card>
      )}

      {/* قالب الطباعة */}
      {showPrintOptions && reportData && (
        <ReportTemplateSelector
          reportType={reportType}
          category={getReportCategory(reportType)}
          autoSelectTemplate={true}
          onTemplateSelect={setSelectedTemplate}
          onPrintWithTemplate={(template) => {
            // استخدام القالب المحدد للطباعة
            const _printData = {
              title: title,
              subtitle: description,
              dateRange: reportData.metadata?.dateRange || 'جميع الفترات',
              data: filteredData,
              summary: reportData.summary || {},
              columns: (reportData.columns || []).map(col => ({
                ...col,
                width: typeof col.width === 'number' ? `${col.width}px` : col.width
              })),
              template: template
            };

            // يمكن إضافة منطق طباعة مخصص هنا
            message.success(`تم اختيار القالب: ${template.name}`);
          }}
          showPrintButton={false}
          size="small"
          style={{ marginBottom: 16 }}
        />
      )}

      {/* أدوات التصدير والطباعة */}
      {(showExportOptions || showPrintOptions) && reportData && (
        <Card style={{ marginBottom: '16px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Text strong>
                عرض {filteredData.length} من أصل {reportData.data.length} سجل
              </Text>
            </Col>
            <Col>
              <Space>
                {showPrintOptions && reportData && (
                  <>
                    <Button
                      type="default"
                      icon={<EyeOutlined />}
                      size="middle"
                      disabled={loading || filteredData.length === 0}
                      onClick={async () => {
                        // استخدام النظام المركزي للمعاينة
                        try {
                          const { MasterPrintService } = await import('../../services/MasterPrintService')
                          const printService = MasterPrintService.getInstance()

                          const printData = {
                            title: title,
                            subtitle: description,
                            dateRange: reportData.metadata?.dateRange || 'جميع الفترات',
                            data: filteredData,
                            summary: reportData.summary || {},
                            columns: (reportData.columns || []).map(col => ({
                              ...col,
                              width: typeof col.width === 'number' ? `${col.width}px` : col.width
                            }))
                          }

                          await printService.previewOnly(printData, {
                            type: 'report',
                            subType: 'financial',
                            showLogo: true,
                            showHeader: true,
                            showFooter: true,
                            orientation: 'landscape'
                          })
                        } catch (error) {
                          message.error('فشل في فتح معاينة التقرير')
                        }
                      }}
                    >
                      معاينة
                    </Button>

                    <UnifiedPrintButton
                      data={{
                        title: title,
                        subtitle: description,
                        dateRange: reportData.metadata?.dateRange || 'جميع الفترات',
                        data: filteredData,
                        summary: reportData.summary || {},
                        columns: (reportData.columns || []).map(col => ({
                          ...col,
                          width: typeof col.width === 'number' ? `${col.width}px` : col.width
                        })),
                        // إضافة القالب المحدد
                        template: selectedTemplate
                      }}
                      type="report"
                      subType={getReportCategory(reportType) as any}
                      buttonText="طباعة"
                      size="middle"
                      showDropdown={true}
                      _documentId={`universal_report_${reportType}`}
                      onAfterPrint={() => message.success('تم طباعة التقرير بنجاح')}
                      onError={() => message.error('فشل في طباعة التقرير')}
                      disabled={loading || filteredData.length === 0}
                    />
                  </>
                )}

                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  size="middle"
                  loading={loading}
                  onClick={loadReport}
                  title="تحديث التقرير"
                >
                  تحديث
                </Button>

                {showExportOptions && (
                  <>
                    <ExcelExportButton
                      data={filteredData}
                      filename={exportFileName || `${reportType}_report`}
                      sheetName={reportData.title}
                      title="تصدير Excel"
                      size="middle"
                      disabled={loading || filteredData.length === 0}
                    />
                    
                    <PDFExportButton
                      reportData={{
                        title: reportData.title,
                        subtitle: reportData.subtitle,
                        data: filteredData,
                        summary: reportData.summary,
                        columns: reportData.columns.map(col => ({
                          key: col.key,
                          title: col.title,
                          align: col.align,
                          format: col.format === 'percentage' ? 'number' : col.format
                        }))
                      }}
                      filename={`${exportFileName || reportType}_report.pdf`}
                      title="تصدير PDF"
                      size="middle"
                      disabled={loading || filteredData.length === 0}
                    />
                  </>
                )}
              </Space>
            </Col>
          </Row>
        </Card>
      )}

      {/* جدول البيانات */}
      <Card>
        <Spin spinning={loading}>
          <Table
            columns={tableColumns}
            dataSource={filteredData}
            rowKey={(record) => record.id || record.key || `row-${Math.random().toString(36).substr(2, 9)}`}
            pagination={{
              total: filteredData.length,
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => (
                <span>
                  عرض {range[0]}-{range[1]} من أصل {total} سجل
                  {loadTime > 0 && (
                    <Text type="secondary" style={{ marginLeft: '8px' }}>
                      • تم التحميل في {(loadTime / 1000).toFixed(2)} ثانية
                    </Text>
                  )}
                </span>
              ),
              pageSizeOptions: ['10', '20', '50', '100'],
              defaultPageSize: 20
            }}
            scroll={{ x: 'max-content', y: 600 }}
            size="middle"
            bordered
            loading={loading}
            locale={{
              emptyText: loading ? 'جاري التحميل...' : 'لا توجد بيانات'
            }}
            // تحسين الأداء: تفعيل التحميل الافتراضي للصفوف
            virtual={filteredData.length > 1000}
          />
        </Spin>

        {/* ملخص التقرير */}
        {showSummary && reportData?.summary && (
          <>
            <Divider />
            <Card size="small" style={{ backgroundColor: '#f8f9fa' }}>
              <Title level={5}>ملخص التقرير</Title>
              <Row gutter={[16, 8]}>
                {Object.entries(reportData.summary).map(([key, value]) => (
                  <Col xs={12} sm={8} md={6} key={key}>
                    <Text strong>{key}: </Text>
                    <Text>{formatValue(value)}</Text>
                  </Col>
                ))}
              </Row>
            </Card>
          </>
        )}
      </Card>
    </div>
  )
}

export default UniversalReport
