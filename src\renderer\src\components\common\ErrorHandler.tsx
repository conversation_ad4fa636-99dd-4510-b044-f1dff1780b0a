import React from 'react'
import { message, notification, Modal } from 'antd'
import { ExclamationCircleOutlined, WarningOutlined, InfoCircleOutlined } from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'

// أنواع الأخطاء
export enum ErrorType {
  VALIDATION = 'validation',
  NETWORK = 'network',
  DATABASE = 'database',
  PERMISSION = 'permission',
  BUSINESS_LOGIC = 'business_logic',
  SYSTEM = 'system'
}

// مستويات الخطورة
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// واجهة معلومات الخطأ
export interface ErrorInfo {
  type: ErrorType
  severity: ErrorSeverity
  message: string
  details?: string
  code?: string
  field?: string
  suggestions?: string[]
  timestamp?: Date
}

// فئة معالج الأخطاء المحسنة
export class ErrorHandler {
  // عرض رسالة خطأ بسيطة
  static showError(error: string | ErrorInfo): void {
    if (typeof error === 'string') {
      message.error(error)
      return
    }

    const { severity, message: errorMessage } = error

    switch (severity) {
      case ErrorSeverity.LOW:
        message.info(errorMessage)
        break
      case ErrorSeverity.MEDIUM:
        message.warning(errorMessage)
        break
      case ErrorSeverity.HIGH:
        message.error(errorMessage)
        break
      case ErrorSeverity.CRITICAL:
        this.showCriticalError(error)
        break
      default:
        message.error(errorMessage)
    }
  }

  // معالجة أخطاء API المحسنة
  static handleApiError(error: any, context: string = 'العملية'): void {
    Logger.error('ErrorHandler', '❌ خطأ في ${context}:', error)

    if (!window.electronAPI) {
      message.error('لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.')
      return
    }

    if (error?.response?.status === 404) {
      message.warning(`${context}: الخدمة غير متاحة حالياً`)
      return
    }

    if (error?.response?.status === 500) {
      message.error(`${context}: خطأ في الخادم`)
      return
    }

    if (error?.message?.includes('timeout')) {
      message.warning(`${context}: انتهت مهلة الاتصال`)
      return
    }

    if (error?.message?.includes('network')) {
      message.error(`${context}: مشكلة في الشبكة`)
      return
    }

    // رسالة خطأ عامة
    const errorMessage = error?.message || error?.toString() || 'حدث خطأ غير متوقع'
    message.error(`${context}: ${errorMessage}`)
  }

  // معالجة أخطاء تحميل البيانات
  static handleDataLoadError(error: any, dataType: string): void {
    Logger.error('ErrorHandler', '❌ خطأ في تحميل ${dataType}:', error)

    if (!window.electronAPI) {
      message.error(`لا يمكن تحميل ${dataType}. يرجى إعادة تشغيل التطبيق.`)
      return
    }

    if (error?.code === 'ENOENT') {
      message.warning(`${dataType}: الملف غير موجود`)
      return
    }

    if (error?.code === 'EACCES') {
      message.error(`${dataType}: ليس لديك صلاحية للوصول`)
      return
    }

    message.error(`فشل في تحميل ${dataType}. يرجى المحاولة مرة أخرى.`)
  }

  // معالجة أخطاء المعالجات المفقودة
  static handleMissingHandler(handlerName: string): void {
    Logger.warn('ErrorHandler', '⚠️ المعالج ${handlerName} غير متاح')
    message.warning(`الوّيفة "${handlerName}" غير متاحة حالياً. سيتم تطويرها قريباً.`)
  }

  // معالجة أخطاء الشبكة
  static handleNetworkError(error: any, operation: string): void {
    Logger.error('ErrorHandler', '❌ خطأ شبكة في ${operation}:', error)

    if (error?.code === 'NETWORK_ERROR') {
      message.error(`${operation}: مشكلة في الاتصال بالشبكة`)
      return
    }

    if (error?.code === 'TIMEOUT') {
      message.warning(`${operation}: انتهت مهلة الاتصال`)
      return
    }

    message.error(`${operation}: فشل في الاتصال`)
  }

  // معالجة أخطاء قاعدة البيانات
  static handleDatabaseError(error: any, operation: string): void {
    Logger.error('ErrorHandler', '❌ خطأ قاعدة بيانات في ${operation}:', error)

    if (error?.code === 'SQLITE_BUSY') {
      message.warning(`${operation}: قاعدة البيانات مشغولة، يرجى المحاولة مرة أخرى`)
      return
    }

    if (error?.code === 'SQLITE_LOCKED') {
      message.error(`${operation}: قاعدة البيانات مقفلة`)
      return
    }

    if (error?.code === 'SQLITE_CORRUPT') {
      message.error(`${operation}: قاعدة البيانات تالفة`)
      return
    }

    message.error(`${operation}: خطأ في قاعدة البيانات`)
  }

  // معالجة أخطاء التحقق من الصحة
  static handleValidationError(errors: string[], context: string = 'البيانات'): void {
    Logger.warn('ErrorHandler', '⚠️ أخطاء تحقق في ${context}:', errors)

    if (errors.length === 1) {
      message.error(`${context}: ${errors[0]}`)
      return
    }

    Modal.warning({
      title: `أخطاء في ${context}`,
      content: (
        <div>
          <p>يرجى تصحيح الأخطاء التالية:</p>
          <ul>
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      ),
      okText: 'موافق'
    })
  }

  // معالجة أخطاء الصلاحيات
  static handlePermissionError(action: string): void {
    Logger.warn('ErrorHandler', '⚠️ ليس لديك صلاحية لـ: ${action}')
    message.warning(`ليس لديك صلاحية لتنفيذ هذا الإجراء: ${action}`)
  }

  // عرض خطأ حرج مع تفاصيل
  static showCriticalError(error: ErrorInfo): void {
    Modal.error({
      title: 'خطأ حرج في النّام',
      content: (
        <div>
          <p><strong>الرسالة:</strong> {error.message}</p>
          {error.details && <p><strong>التفاصيل:</strong> {error.details}</p>}
          {error.code && <p><strong>رمز الخطأ:</strong> {error.code}</p>}
          {error.suggestions && (
            <div>
              <strong>الحلول المقترحة:</strong>
              <ul>
                {error.suggestions.map((suggestion, index) => (
                  <li key={index}>{suggestion}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      ),
      icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
      okText: 'موافق'
    })
  }

  // عرض إشعار مفصل
  static showNotification(error: ErrorInfo): void {
    const { type, severity, message: errorMessage, details, suggestions } = error
    
    let icon = <InfoCircleOutlined />
    let duration = 4.5

    switch (severity) {
      case ErrorSeverity.LOW:
        icon = <InfoCircleOutlined style={{ color: '#1890ff' }} />
        duration = 3
        break
      case ErrorSeverity.MEDIUM:
        icon = <WarningOutlined style={{ color: '#faad14' }} />
        duration = 6
        break
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        icon = <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
        duration = 0 // لا يختفي تلقائياً
        break
    }

    notification.open({
      message: this.getErrorTypeTitle(type),
      description: (
        <div>
          <p>{errorMessage}</p>
          {details && <p style={{ fontSize: '12px', color: '#666' }}>{details}</p>}
          {suggestions && suggestions.length > 0 && (
            <div style={{ marginTop: '8px' }}>
              <strong>الحلول المقترحة:</strong>
              <ul style={{ margin: '4px 0', paddingLeft: '16px' }}>
                {suggestions.map((suggestion, index) => (
                  <li key={index} style={{ fontSize: '12px' }}>{suggestion}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      ),
      icon,
      duration,
      placement: 'topRight'
    })
  }

  // الحصول على عنوان نوع الخطأ
  static getErrorTypeTitle(type: ErrorType): string {
    switch (type) {
      case ErrorType.VALIDATION:
        return 'خطأ في التحقق من البيانات'
      case ErrorType.NETWORK:
        return 'خطأ في الشبكة'
      case ErrorType.DATABASE:
        return 'خطأ في قاعدة البيانات'
      case ErrorType.PERMISSION:
        return 'خطأ في الصلاحيات'
      case ErrorType.BUSINESS_LOGIC:
        return 'خطأ في منطق العمل'
      case ErrorType.SYSTEM:
        return 'خطأ في النّام'
      default:
        return 'خطأ غير معروف'
    }
  }

  // تسجيل الخطأ
  static logError(error: ErrorInfo): void {
    const logEntry = {
      ...error,
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    Logger.error('ErrorHandler', 'Error logged:', logEntry)
    
    // يمكن إضافة إرسال الخطأ لخادم التسجيل هنا
    // this.sendErrorToServer(logEntry)
  }

  // معالجة أخطاء الفواتير
  static handleInvoiceError(error: any, context: string): void {
    let errorInfo: ErrorInfo

    if (error.message?.includes('UNIQUE constraint failed')) {
      errorInfo = {
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        message: 'رقم الفاتورة موجود مسبقاً',
        details: 'يرجى استخدام رقم فاتورة مختلف',
        suggestions: [
          'تحقق من رقم الفاتورة المدخل',
          'استخدم زر "توليد رقم تلقائي"',
          'راجع الفواتير الموجودة'
        ]
      }
    } else if (error.message?.includes('FOREIGN KEY constraint failed')) {
      errorInfo = {
        type: ErrorType.DATABASE,
        severity: ErrorSeverity.HIGH,
        message: 'خطأ في ربط البيانات',
        details: 'فشل في ربط الفاتورة بالعميل أو المورد',
        suggestions: [
          'تأكد من وجود العميل/المورد المحدد',
          'أعد تحميل البيانات',
          'تواصل مع الدعم الفني'
        ]
      }
    } else if (error.message?.includes('insufficient inventory')) {
      errorInfo = {
        type: ErrorType.BUSINESS_LOGIC,
        severity: ErrorSeverity.HIGH,
        message: 'الكمية المطلوبة غير متوفرة في المخزون',
        details: error.message,
        suggestions: [
          'تحقق من الكمية المتاحة في المخزون',
          'قم بتقليل الكمية المطلوبة',
          'أضف مخزون جديد أولاً'
        ]
      }
    } else if (error.message?.includes('network')) {
      errorInfo = {
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: 'خطأ في الاتصال',
        details: 'فشل في الاتصال بقاعدة البيانات',
        suggestions: [
          'تحقق من اتصال الإنترنت',
          'أعد تشغيل التطبيق',
          'تواصل مع الدعم الفني'
        ]
      }
    } else {
      errorInfo = {
        type: ErrorType.SYSTEM,
        severity: ErrorSeverity.HIGH,
        message: `خطأ في ${context}`,
        details: error.message || 'خطأ غير معروف',
        code: error.code,
        suggestions: [
          'أعد المحاولة',
          'أعد تشغيل التطبيق',
          'تواصل مع الدعم الفني'
        ]
      }
    }

    this.logError(errorInfo)
    this.showNotification(errorInfo)
  }

  // معالجة أخطاء المدفوعات
  static handlePaymentError(error: any, paymentMethod: string): void {
    let errorInfo: ErrorInfo

    if (error.message?.includes('amount exceeds remaining')) {
      errorInfo = {
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        message: 'المبلغ المدخل أكبر من المبلغ المتبقي',
        details: 'لا يمكن دفع مبلغ أكبر من المبلغ المستحق',
        suggestions: [
          'تحقق من المبلغ المتبقي',
          'أدخل مبلغاً أقل أو مساوياً للمبلغ المتبقي'
        ]
      }
    } else if (error.message?.includes('invalid payment method')) {
      errorInfo = {
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        message: 'طريقة الدفع غير صحيحة',
        details: `طريقة الدفع "${paymentMethod}" غير مدعومة`,
        suggestions: [
          'اختر طريقة دفع صحيحة',
          'تحقق من الطرق المتاحة'
        ]
      }
    } else {
      errorInfo = {
        type: ErrorType.SYSTEM,
        severity: ErrorSeverity.HIGH,
        message: 'خطأ في معالجة الدفعة',
        details: error.message || 'خطأ غير معروف في نّام المدفوعات',
        suggestions: [
          'أعد المحاولة',
          'تحقق من بيانات الدفع',
          'تواصل مع الدعم الفني'
        ]
      }
    }

    this.logError(errorInfo)
    this.showNotification(errorInfo)
  }

  // معالجة أخطاء الطباعة
  static handlePrintError(error: any): void {
    const errorInfo: ErrorInfo = {
      type: ErrorType.SYSTEM,
      severity: ErrorSeverity.MEDIUM,
      message: 'خطأ في الطباعة',
      details: error.message || 'فشل في طباعة الفاتورة',
      suggestions: [
        'تحقق من اتصال الطابعة',
        'تأكد من وجود ورق في الطابعة',
        'أعد المحاولة',
        'جرب طباعة سريعة'
      ]
    }

    this.logError(errorInfo)
    this.showNotification(errorInfo)
  }

  // التحقق من صحة البيانات
  static validateInvoiceData(invoiceData: any): ErrorInfo[] {
    const errors: ErrorInfo[] = []

    // التحقق من رقم الفاتورة
    if (!invoiceData.invoice_number || invoiceData.invoice_number.trim() === '') {
      errors.push({
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.HIGH,
        message: 'رقم الفاتورة مطلوب',
        field: 'invoice_number',
        suggestions: ['أدخل رقم فاتورة صحيح']
      })
    }

    // التحقق من العميل/المورد
    if (!invoiceData.customer_id && !invoiceData.supplier_id) {
      errors.push({
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.HIGH,
        message: 'يجب اختيار عميل أو مورد',
        field: 'customer_id',
        suggestions: ['اختر عميلاً أو مورداً من القائمة']
      })
    }

    // التحقق من تاريخ الفاتورة
    if (!invoiceData.invoice_date) {
      errors.push({
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        message: 'تاريخ الفاتورة مطلوب',
        field: 'invoice_date',
        suggestions: ['أدخل تاريخ الفاتورة']
      })
    }

    // التحقق من الأصناف
    if (!invoiceData.items || invoiceData.items.length === 0) {
      errors.push({
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.HIGH,
        message: 'يجب إضافة صنف واحد على الأقل',
        field: 'items',
        suggestions: ['أضف أصنافاً للفاتورة']
      })
    }

    // التحقق من المبالغ
    if (invoiceData.total_amount <= 0) {
      errors.push({
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.HIGH,
        message: 'إجمالي الفاتورة يجب أن يكون أكبر من صفر',
        field: 'total_amount',
        suggestions: ['تحقق من أسعار وكميات الأصناف']
      })
    }

    return errors
  }

  // التحقق من صحة بيانات الدفع
  static validatePaymentData(paymentData: any, remainingAmount: number): ErrorInfo[] {
    const errors: ErrorInfo[] = []

    // التحقق من المبلغ
    if (!paymentData.amount || paymentData.amount <= 0) {
      errors.push({
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.HIGH,
        message: 'مبلغ الدفع يجب أن يكون أكبر من صفر',
        field: 'amount',
        suggestions: ['أدخل مبلغاً صحيحاً']
      })
    }

    if (paymentData.amount > remainingAmount) {
      errors.push({
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.HIGH,
        message: 'مبلغ الدفع أكبر من المبلغ المتبقي',
        field: 'amount',
        suggestions: [`المبلغ المتبقي هو ${remainingAmount}`]
      })
    }

    // التحقق من طريقة الدفع
    if (!paymentData.payment_method) {
      errors.push({
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.HIGH,
        message: 'يجب اختيار طريقة الدفع',
        field: 'payment_method',
        suggestions: ['اختر طريقة دفع من القائمة']
      })
    }

    // التحقق من تاريخ الدفع
    if (!paymentData.payment_date) {
      errors.push({
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        message: 'تاريخ الدفع مطلوب',
        field: 'payment_date',
        suggestions: ['أدخل تاريخ الدفع']
      })
    }

    return errors
  }
}

// مكون عرض الأخطاء
interface ErrorDisplayProps {
  errors: ErrorInfo[]
  onClose?: () => void
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ errors }) => {
  if (errors.length === 0) return null

  return (
    <div style={{ marginBottom: '16px' }}>
      {errors.map((error, index) => {
        if (error.severity === ErrorSeverity.CRITICAL) {
          ErrorHandler.showCriticalError(error)
        } else {
          ErrorHandler.showNotification(error)
        }
        return (
          <div key={index} style={{ marginBottom: '8px' }}>
            {/* Error displayed via notification */}
          </div>
        )
      })}
    </div>
  )
}

export default ErrorHandler
