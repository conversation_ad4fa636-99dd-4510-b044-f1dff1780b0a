import React, { useState, useEffect } from 'react'
import {
  Modal,
  Card,
  Descriptions,
  Table,
  Tag,
  Space,
  Button,
  Divider,
  Row,
  Col,
  Statistic,
  Typography,
  Alert,
  Collapse,
  Tooltip
} from 'antd'
import {
  EyeOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  ToolOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { Logger } from '../../../utils/logger'

const { Text, Title } = Typography
const { Panel } = Collapse

interface ProductionOrderDetailsWithRecipesProps {
  visible: boolean
  onClose: () => void
  orderId: number | null
}

const ProductionOrderDetailsWithRecipes: React.FC<ProductionOrderDetailsWithRecipesProps> = ({
  visible,
  onClose,
  orderId
}) => {
  const [orderDetails, setOrderDetails] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (visible && orderId) {
      loadOrderDetails()
    }
  }, [visible, orderId])

  const loadOrderDetails = async () => {
    try {
      setLoading(true)
      if (!window.electronAPI || !orderId) return

      const result = await window.electronAPI.getProductionOrderItems(orderId)
      if (result.success) {
        setOrderDetails(result.data)
      } else {
        Logger.error('ProductionOrderDetailsWithRecipes', 'فشل في تحميل تفاصيل الأمر:', result.message)
      }
    } catch (error) {
      Logger.error('ProductionOrderDetailsWithRecipes', 'خطأ في تحميل تفاصيل الأمر:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'in_progress': return 'blue'
      case 'completed': return 'green'
      case 'cancelled': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'معلق'
      case 'in_progress': return 'قيد التنفيذ'
      case 'completed': return 'مكتمل'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'green'
      case 'normal': return 'blue'
      case 'high': return 'orange'
      case 'urgent': return 'red'
      default: return 'default'
    }
  }

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'low': return 'منخفضة'
      case 'normal': return 'عادية'
      case 'high': return 'عالية'
      case 'urgent': return 'عاجلة'
      default: return priority
    }
  }

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'easy': return 'green'
      case 'medium': return 'blue'
      case 'hard': return 'orange'
      case 'expert': return 'red'
      default: return 'default'
    }
  }

  const getDifficultyText = (level: string) => {
    switch (level) {
      case 'easy': return 'سهل'
      case 'medium': return 'متوسط'
      case 'hard': return 'صعب'
      case 'expert': return 'خبير'
      default: return level
    }
  }

  // أعمدة جدول الوصفات
  const recipesColumns = [
    {
      title: 'الوصفة',
      key: 'recipe',
      render: (record: any) => (
        <div>
          <Text strong>{record.recipe_name || record.item_name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.recipe_code} - {record.item_code}
          </Text>
          <br />
          <Space size="small">
            <Tag color={getDifficultyColor(record.difficulty_level)}>
              {getDifficultyText(record.difficulty_level)}
            </Tag>
            <Text type="secondary" style={{ fontSize: '11px' }}>
              {record.estimated_time}س
            </Text>
          </Space>
        </div>
      )
    },
    {
      title: 'الكمية',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number, record: any) => (
        <Text>{quantity} {record.item_unit || 'قطعة'}</Text>
      )
    },
    {
      title: 'المواد المطلوبة',
      key: 'materials',
      render: (record: any) => (
        <Text>{record.materials?.length || 0} مادة</Text>
      )
    },
    {
      title: 'ملاحظات',
      dataIndex: 'notes',
      key: 'notes',
      render: (notes: string) => notes || '-'
    }
  ]

  // أعمدة جدول المواد
  const materialsColumns = [
    {
      title: 'المادة',
      key: 'material',
      render: (record: any) => (
        <div>
          <Text strong>{record.material_name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.material_code}
          </Text>
        </div>
      )
    },
    {
      title: 'الكمية المطلوبة',
      key: 'required_quantity',
      render: (record: any) => (
        <Text>{record.required_quantity} {record.material_unit}</Text>
      )
    },
    {
      title: 'المتوفر',
      key: 'available',
      render: (record: any) => (
        <Text style={{ 
          color: record.available_quantity >= record.required_quantity ? '#52c41a' : '#ff4d4f' 
        }}>
          {record.available_quantity} {record.material_unit}
        </Text>
      )
    },
    {
      title: 'التكلفة',
      key: 'cost',
      render: (record: any) => (
        <Text>₪{record.total_cost?.toFixed(2) || '0.00'}</Text>
      )
    },
    {
      title: 'المخزن',
      dataIndex: 'warehouse_name',
      key: 'warehouse_name',
      render: (warehouse: string) => warehouse || '-'
    }
  ]

  if (!orderDetails) {
    return (
      <Modal
        title="تفاصيل أمر الإنتاج"
        open={visible}
        onCancel={onClose}
        footer={[
          <Button key="close" onClick={onClose}>
            إغلاق
          </Button>
        ]}
        width="90%"
        style={{ top: 20 }}
      >
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Text>جاري تحميل التفاصيل...</Text>
        </div>
      </Modal>
    )
  }

  const { order, recipes, summary } = orderDetails

  return (
    <Modal
      title={
        <Space>
          <EyeOutlined />
          تفاصيل أمر الإنتاج - {order.order_number}
          <Tag color={getStatusColor(order.status)}>
            {getStatusText(order.status)}
          </Tag>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          إغلاق
        </Button>
      ]}
      width="95%"
      style={{ top: 20 }}
    >
      <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
        {/* معلومات أمر الإنتاج الأساسية */}
        <Card title="معلومات الأمر" size="small" style={{ marginBottom: 16 }}>
          <Descriptions column={3} size="small">
            <Descriptions.Item label="رقم الأمر">{order.order_number}</Descriptions.Item>
            <Descriptions.Item label="كود الأمر">{order.order_code || '-'}</Descriptions.Item>
            <Descriptions.Item label="الحالة">
              <Tag color={getStatusColor(order.status)}>
                {getStatusText(order.status)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="الأولوية">
              <Tag color={getPriorityColor(order.priority)}>
                {getPriorityText(order.priority)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="القسم">{order.department_name || '-'}</Descriptions.Item>
            <Descriptions.Item label="العميل">{order.customer_name || 'إنتاج داخلي'}</Descriptions.Item>
            <Descriptions.Item label="المخزن">{order.warehouse_name || '-'}</Descriptions.Item>
            <Descriptions.Item label="تاريخ البدء">{order.start_date || '-'}</Descriptions.Item>
            <Descriptions.Item label="تاريخ الانتهاء">{order.end_date || '-'}</Descriptions.Item>
            <Descriptions.Item label="أنشأ بواسطة">{order.created_by_name || '-'}</Descriptions.Item>
            <Descriptions.Item label="تاريخ الإنشاء">
              {new Date(order.created_at).toLocaleDateString('ar-EG')}
            </Descriptions.Item>
            <Descriptions.Item label="ملاحظات" span={2}>
              {order.notes || '-'}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* الإحصائيات */}
        <Card title="ملخص الأمر" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="عدد الوصفات"
                value={summary.total_recipes}
                prefix={<ToolOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="إجمالي القطع"
                value={summary.total_items}
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="التكلفة المقدرة"
                value={summary.estimated_total_cost}
                precision={2}
                prefix="₪"
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="الوقت المقدر"
                value={summary.estimated_total_hours}
                precision={1}
                suffix="ساعة"
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Col>
          </Row>
        </Card>

        {/* الوصفات والمواد */}
        <Card title="الوصفات والمواد المطلوبة" size="small">
          <Collapse defaultActiveKey={['recipes']} size="small">
            <Panel 
              header={
                <Space>
                  <ToolOutlined />
                  الوصفات ({recipes.length})
                </Space>
              } 
              key="recipes"
            >
              <Table
                columns={recipesColumns}
                dataSource={recipes}
                rowKey="id"
                pagination={false}
                size="small"
                expandable={{
                  expandedRowRender: (record) => (
                    <div style={{ margin: 0 }}>
                      <Title level={5}>المواد المطلوبة لهذه الوصفة:</Title>
                      {record.materials && record.materials.length > 0 ? (
                        <Table
                          columns={materialsColumns}
                          dataSource={record.materials}
                          rowKey="id"
                          pagination={false}
                          size="small"
                          style={{ marginTop: 8 }}
                        />
                      ) : (
                        <Alert
                          message="لا توجد مواد محددة لهذه الوصفة"
                          type="info"
                          showIcon

                        />
                      )}
                    </div>
                  ),
                  rowExpandable: (record) => record.materials && record.materials.length > 0
                }}
              />
            </Panel>
          </Collapse>
        </Card>
      </div>
    </Modal>
  )
}

export default ProductionOrderDetailsWithRecipes
