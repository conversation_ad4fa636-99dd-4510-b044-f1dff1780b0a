import React from 'react';

const SimpleTest: React.FC = () => {
  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column',
      fontFamily: 'Arial, sans-serif',
      background: '#f0f2f5'
    }}>
      <div style={{
        background: 'white',
        padding: '40px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        textAlign: 'center'
      }}>
        <h1 style={{ color: '#1890ff', marginBottom: '20px' }}>
          ✅ React يعمل بشكل صحيح!
        </h1>
        <p style={{ color: '#666', marginBottom: '20px' }}>
          إذا كنت ترى هذه الرسالة، فإن React يتم تحميله وعرضه بنجاح.
        </p>
        <div style={{
          background: '#f6ffed',
          border: '1px solid #b7eb8f',
          padding: '10px',
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <strong>الوقت الحالي:</strong> {new Date().toLocaleString('ar-EG')}
        </div>
        <button
          onClick={() => window.alert('React يعمل!')}
          style={{
            background: '#1890ff',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          اختبار التفاعل
        </button>
      </div>
    </div>
  );
};

export default SimpleTest;
