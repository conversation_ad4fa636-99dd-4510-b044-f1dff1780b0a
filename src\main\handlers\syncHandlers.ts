
import { ipc<PERSON>ain, dialog, IpcMainInvokeEvent, BrowserWindow } from 'electron'
import { DatabaseService, SimpleDatabaseService } from '../services'
import { FileSyncService, SyncConfig } from '../services/FileSyncService'
import { SmartSyncService } from '../services/SmartSyncService'
import { Logger } from '../utils/logger'
import * as fs from 'fs'
import * as path from 'path'
import * as os from 'os'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

let databaseService: DatabaseService
let fileSyncService: FileSyncService | null = null
let smartSyncService: SmartSyncService | null = null

export const setSyncDatabaseService = (service: DatabaseService) => {
  databaseService = service
  smartSyncService = new SmartSyncService(service)
}

// دالة مساعدة لحفظ الإعدادات في قاعدة البيانات
const saveSettingsToDatabase = async (db: any, settingsToUpdate: { key: string; value: string }[]) => {
  try {
    for (const setting of settingsToUpdate) {
      // التحقق من وجود الإعداد
      const checkStmt = db.prepare(`SELECT id FROM settings WHERE key = ?`)
      const existing = checkStmt.get(setting.key)

      if (existing) {
        // تحديث الإعداد الموجود
        const updateStmt = db.prepare(`UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?`)
        updateStmt.run(setting.value, setting.key)
      } else {
        // إنشاء إعداد جديد
        const insertStmt = db.prepare(`INSERT INTO settings (key, value, description, created_at, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`)
        insertStmt.run(setting.key, setting.value, `إعداد ${setting.key}`)
      }
    }

    // حفظ التغييرات
    if (databaseService instanceof SimpleDatabaseService) {
      databaseService.saveDatabase()
    }

    Logger.info('SyncHandlers', 'تم حفظ الإعدادات في قاعدة البيانات بنجاح')
  } catch (error: any) {
    Logger.error('SyncHandlers', 'خطأ في حفظ الإعدادات في قاعدة البيانات:', error)
    throw error
  }
}

// دالة مساعدة لاستعادة الإعدادات في قاعدة البيانات الجديدة
const restoreSettingsToNewDatabase = async (tempSettingsPath: string) => {
  try {
    if (fs.existsSync(tempSettingsPath)) {
      const settingsData = JSON.parse(fs.readFileSync(tempSettingsPath, 'utf8'))

      const settingsToUpdate = [
        { key: 'sync_enabled', value: settingsData.enabled.toString() },
        { key: 'sync_device_role', value: settingsData.deviceRole },
        { key: 'sync_shared_folder', value: settingsData.sharedFolder },
        { key: 'sync_interval', value: settingsData.syncInterval.toString() }
      ]

      const db = databaseService.getDatabase()
      await saveSettingsToDatabase(db, settingsToUpdate)

      // حذف الملف المؤقت
      fs.unlinkSync(tempSettingsPath)

      Logger.info('SyncHandlers', 'تم استعادة إعدادات المزامنة في قاعدة البيانات الجديدة')
    }
  } catch (error: any) {
    Logger.error('SyncHandlers', 'خطأ في استعادة إعدادات المزامنة:', error)
  }
}

// دالة للتحقق من حفظ الإعدادات بشكل صحيح
const verifySettingsSaved = async (settings: SyncConfig): Promise<boolean> => {
  try {
    const db = databaseService.getDatabase()
    const settingsQuery = db.exec('SELECT * FROM settings WHERE key LIKE "sync_%"')

    const savedSettings: any[] = []
    if (settingsQuery.length > 0 && settingsQuery[0].values.length > 0) {
      const columns = settingsQuery[0].columns
      settingsQuery[0].values.forEach((values: any[]) => {
        const setting: any = {}
        columns.forEach((col: string, index: number) => {
          setting[col] = values[index]
        })
        savedSettings.push(setting)
      })
    }

    const settingsMap: { [key: string]: string } = {}
    savedSettings.forEach((row: any) => {
      settingsMap[row.key] = row.value
    })

    // التحقق من أن الإعدادات محفوظة بشكل صحيح
    const isCorrect =
      settingsMap['sync_enabled'] === settings.enabled.toString() &&
      settingsMap['sync_device_role'] === settings.deviceRole &&
      settingsMap['sync_shared_folder'] === settings.sharedFolder &&
      settingsMap['sync_interval'] === settings.syncInterval.toString()

    Logger.info('SyncHandlers', 'التحقق من حفظ الإعدادات:', {
      expected: settings,
      saved: settingsMap,
      isCorrect
    })

    return isCorrect
  } catch (error: any) {
    Logger.error('SyncHandlers', 'خطأ في التحقق من حفظ الإعدادات:', error)
    return false
  }
}

// دالة حفظ الجلسات الحالية قبل التبديل
const saveCurrentSessions = async (): Promise<any[]> => {
  try {
    Logger.info('SyncHandlers', '🔄 حفظ الجلسات الحالية...')
    const db = databaseService.getDatabase()
    const sessionsQuery = db.exec(`
      SELECT s.*, u.username, u.full_name, u.role
      FROM user_sessions s
      JOIN users u ON s.user_id = u.id
      WHERE s.expires_at > datetime('now')
    `)

    const sessions: any[] = []
    if (sessionsQuery.length > 0 && sessionsQuery[0].values.length > 0) {
      const columns = sessionsQuery[0].columns
      sessionsQuery[0].values.forEach((values: any[]) => {
        const session: any = {}
        columns.forEach((col: string, index: number) => {
          session[col] = values[index]
        })
        sessions.push(session)
      })
    }

    Logger.info('SyncHandlers', `✅ تم حفظ ${sessions.length} جلسة نشطة`)
    return sessions
  } catch (error) {
    Logger.error('SyncHandlers', '❌ خطأ في حفظ الجلسات:', error)
    return []
  }
}



// دالة استعادة الجلسات في قاعدة البيانات الجديدة
const restoreSessionsToNewDatabase = async (sessions: any[]): Promise<void> => {
  try {
    Logger.info('SyncHandlers', '🔄 استعادة الجلسات في قاعدة البيانات الجديدة...')

    if (!sessions || sessions.length === 0) {
      Logger.info('SyncHandlers', 'لا توجد جلسات لاستعادتها')
      return
    }

    const db = databaseService.getDatabase()
    for (const session of sessions) {
      try {
        // التحقق من وجود المستخدم
        const userStmt = db.prepare('SELECT id FROM users WHERE id = ?')
        const user = userStmt.get(session.user_id)

        if (user) {
          // إدراج الجلسة
          const sessionStmt = db.prepare(`
            INSERT OR REPLACE INTO user_sessions (user_id, token, expires_at, created_at)
            VALUES (?, ?, ?, ?)
          `)
          sessionStmt.run(session.user_id, session.token, session.expires_at, session.created_at)
        }
      } catch (sessionError) {
        Logger.warn('SyncHandlers', `خطأ في استعادة جلسة ${session.token}:`, sessionError)
      }
    }

    Logger.info('SyncHandlers', `✅ تم استعادة ${sessions.length} جلسة في قاعدة البيانات الجديدة`)
  } catch (error) {
    Logger.error('SyncHandlers', '❌ خطأ في استعادة الجلسات:', error)
  }
}

export const registerSyncHandlers = () => {
  // جلب إعدادات المزامنة
  ipcMain.handle('get-sync-settings', async () => {
    try {
      const db = databaseService.getDatabase()
      const settingsQuery = db.exec('SELECT * FROM settings WHERE key LIKE "sync_%"')

      const settings: any[] = []
      if (settingsQuery.length > 0 && settingsQuery[0].values.length > 0) {
        const columns = settingsQuery[0].columns
        settingsQuery[0].values.forEach((values: any[]) => {
          const setting: any = {}
          columns.forEach((col: string, index: number) => {
            setting[col] = values[index]
          })
          settings.push(setting)
        })
      }

      const settingsMap: { [key: string]: string } = {}
      if (settings && Array.isArray(settings)) {
        settings.forEach((row: any) => {
          settingsMap[row.key] = row.value
        })
      }

      const syncSettings = {
        enabled: settingsMap['sync_enabled'] === 'true',
        deviceRole: settingsMap['sync_device_role'] || 'branch',
        sharedFolder: settingsMap['sync_shared_folder'] || '',
        syncInterval: parseInt(settingsMap['sync_interval'] || '5')
      }

      return { success: true, data: syncSettings }
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في جلب إعدادات المزامنة:', error)
      return { success: false, message: error.message }
    }
  })

  // تحديث إعدادات المزامنة
  ipcMain.handle('update-sync-settings', async (_event: IpcMainInvokeEvent, settings: SyncConfig) => {
    try {
      Logger.info('SyncHandlers', 'تحديث إعدادات المزامنة:', settings)

      const db = databaseService.getDatabase()

      // حفّ الإعدادات في قاعدة البيانات
      const settingsToUpdate = [
        { key: 'sync_enabled', value: settings.enabled.toString() },
        { key: 'sync_device_role', value: settings.deviceRole },
        { key: 'sync_shared_folder', value: settings.sharedFolder },
        { key: 'sync_interval', value: settings.syncInterval.toString() }
      ]

      // حفظ الإعدادات في قاعدة البيانات الحالية أولاً
      await saveSettingsToDatabase(db, settingsToUpdate)

      // حفظ الإعدادات في ملف مؤقت للاستخدام بعد تبديل قاعدة البيانات
      const tempSettingsPath = path.join(os.tmpdir(), 'sync_settings_temp.json')
      fs.writeFileSync(tempSettingsPath, JSON.stringify(settings))

      // إيقاف المزامنة الحالية إن وجدت
      if (fileSyncService) {
        await fileSyncService.stopSync()
        fileSyncService = null
      }

      // التحقق من الحاجة لتبديل قاعدة البيانات
      let currentDbPath: string
      let currentDbType: string
      let needsDatabaseSwitch = false

      try {
        if (databaseService.getDatabaseStatus) {
          const currentDbStatus = databaseService.getDatabaseStatus()
          currentDbPath = currentDbStatus.path
        } else if ((databaseService as any).dbPath) {
          currentDbPath = (databaseService as any).dbPath
        } else {
          currentDbPath = path.join(process.cwd(), 'database.db')
        }

        if (databaseService.getDatabaseType) {
          currentDbType = databaseService.getDatabaseType()
        } else if ((databaseService as any).isShared !== undefined) {
          currentDbType = (databaseService as any).isShared ? 'shared' : 'local'
        } else {
          currentDbType = 'local'
        }
      } catch (error) {
        Logger.warn('SyncHandlers', 'خطأ في الحصول على معلومات قاعدة البيانات:', error)
        currentDbPath = path.join(process.cwd(), 'database.db')
        currentDbType = 'local'
      }

      if (settings.enabled && settings.sharedFolder) {
        const sharedDbPath = path.join(settings.sharedFolder, 'database.db')

        // التحقق من أن قاعدة البيانات الحالية ليست هي المشتركة المطلوبة
        needsDatabaseSwitch = currentDbPath !== sharedDbPath && fs.existsSync(sharedDbPath)
      } else {
        // التحقق من أن قاعدة البيانات الحالية ليست المحلية
        needsDatabaseSwitch = currentDbType !== 'local'
      }

      // تبديل قاعدة البيانات فقط عند الحاجة
      if (needsDatabaseSwitch) {
        if (settings.enabled && settings.sharedFolder) {
          const sharedDbPath = path.join(settings.sharedFolder, 'database.db')

          if (fs.existsSync(sharedDbPath)) {
            Logger.info('SyncHandlers', '🔄 التبديل إلى قاعدة البيانات المشتركة...')

            // حفظ معلومات الجلسة الحالية قبل التبديل
            const currentSessions = await saveCurrentSessions()

            // التبديل إلى قاعدة البيانات المشتركة
            Logger.info('SyncHandlers', `🔄 التبديل إلى قاعدة البيانات المشتركة: ${sharedDbPath}`)

            // إغلاق قاعدة البيانات الحالية
            databaseService.close()

            // إعادة تهيئة خدمة قاعدة البيانات للقاعدة المشتركة
            await databaseService.setDatabasePath(sharedDbPath)

            // تم تحديث قاعدة البيانات بنجاح

            const switchSuccess = true

            if (switchSuccess) {
              Logger.success('SyncHandlers', '✅ تم التبديل إلى قاعدة البيانات المشتركة بنجاح')

              // استعادة الجلسات في قاعدة البيانات الجديدة
              await restoreSessionsToNewDatabase(currentSessions)

              // استعادة إعدادات المزامنة في قاعدة البيانات الجديدة
              await restoreSettingsToNewDatabase(tempSettingsPath)

              // بدء المزامنة الجديدة
              fileSyncService = new FileSyncService(settings, databaseService)
              await fileSyncService.startSync()

              // إرسال إشعار لواجهة المستخدم لإعادة التحميل مع معلومات الجلسة
              const mainWindow = BrowserWindow.getAllWindows()[0]
              if (mainWindow) {
                mainWindow.webContents.send('database-switched', {
                  type: 'shared',
                  path: sharedDbPath,
                  message: 'تم التبديل إلى قاعدة البيانات المشتركة',
                  preserveSession: true,
                  sessionsRestored: currentSessions.length
                })
              }
            } else {
              Logger.error('SyncHandlers', '❌ فشل في التبديل إلى قاعدة البيانات المشتركة')
              return { success: false, message: 'فشل في التبديل إلى قاعدة البيانات المشتركة' }
            }
          } else {
            Logger.warn('SyncHandlers', '⚠️ قاعدة البيانات المشتركة غير موجودة، سيتم استخدام المحلية')

            // بدء المزامنة مع قاعدة البيانات المحلية
            fileSyncService = new FileSyncService(settings, databaseService)
            await fileSyncService.startSync()
          }
        } else {
          // التبديل إلى قاعدة البيانات المحلية عند إيقاف المزامنة
          Logger.info('SyncHandlers', '🔄 التبديل إلى قاعدة البيانات المحلية...')

          // حفظ الجلسات الحالية قبل التبديل
          const currentSessions = await saveCurrentSessions()

          // التبديل إلى قاعدة البيانات المحلية
          Logger.info('SyncHandlers', '🔄 التبديل إلى قاعدة البيانات المحلية')

          // إغلاق قاعدة البيانات الحالية
          databaseService.close()

          // إعادة تهيئة خدمة قاعدة البيانات للقاعدة المحلية
          let switchSuccess = false
          try {
            if (databaseService.switchToLocalDatabase) {
              await databaseService.switchToLocalDatabase()
              switchSuccess = true
            } else {
              // استخدام طريقة بديلة
              const localDbPath = path.join(process.cwd(), 'database.db')
              await databaseService.setDatabasePath(localDbPath)
              switchSuccess = true
            }
          } catch (switchError) {
            Logger.error('SyncHandlers', 'خطأ في التبديل إلى قاعدة البيانات المحلية:', switchError)
            switchSuccess = false
          }

          if (switchSuccess) {
            // استعادة الجلسات في قاعدة البيانات المحلية
            await restoreSessionsToNewDatabase(currentSessions)

            // استعادة إعدادات المزامنة في قاعدة البيانات المحلية
            await restoreSettingsToNewDatabase(tempSettingsPath)

            // إرسال إشعار لواجهة المستخدم لإعادة التحميل مع الحفاظ على الجلسة
            const mainWindow = BrowserWindow.getAllWindows()[0]
            if (mainWindow) {
              mainWindow.webContents.send('database-switched', {
                type: 'local',
                message: 'تم التبديل إلى قاعدة البيانات المحلية',
                preserveSession: true,
                sessionsRestored: currentSessions.length
              })
            }
          }
        }
      } else {
        // لا حاجة لتبديل قاعدة البيانات، فقط تحديث إعدادات المزامنة
        Logger.info('SyncHandlers', '📝 تحديث إعدادات المزامنة بدون تبديل قاعدة البيانات')

        if (settings.enabled && settings.sharedFolder) {
          // بدء أو إعادة تشغيل المزامنة
          fileSyncService = new FileSyncService(settings, databaseService)
          await fileSyncService.startSync()
        }

        // تنظيف الملف المؤقت لأنه لم يعد مطلوباً
        try {
          const tempSettingsPath = path.join(os.tmpdir(), 'sync_settings_temp.json')
          if (fs.existsSync(tempSettingsPath)) {
            fs.unlinkSync(tempSettingsPath)
          }
        } catch (cleanupError) {
          Logger.warn('SyncHandlers', 'خطأ في تنظيف الملف المؤقت:', cleanupError)
        }
      }

      // التحقق من حفظ الإعدادات بشكل صحيح
      const settingsSaved = await verifySettingsSaved(settings)
      if (!settingsSaved) {
        Logger.warn('SyncHandlers', '⚠️ قد لا تكون الإعدادات محفوظة بشكل صحيح')
        // محاولة حفظ الإعدادات مرة أخرى
        const db = databaseService.getDatabase()
        const settingsToUpdate = [
          { key: 'sync_enabled', value: settings.enabled.toString() },
          { key: 'sync_device_role', value: settings.deviceRole },
          { key: 'sync_shared_folder', value: settings.sharedFolder },
          { key: 'sync_interval', value: settings.syncInterval.toString() }
        ]
        await saveSettingsToDatabase(db, settingsToUpdate)
      }

      return { success: true, message: 'تم تحديث إعدادات المزامنة بنجاح' }
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في تحديث إعدادات المزامنة:', error)

      // تنظيف الملف المؤقت في حالة الخطأ
      try {
        const tempSettingsPath = path.join(os.tmpdir(), 'sync_settings_temp.json')
        if (fs.existsSync(tempSettingsPath)) {
          fs.unlinkSync(tempSettingsPath)
        }
      } catch (cleanupError) {
        Logger.warn('SyncHandlers', 'خطأ في تنظيف الملف المؤقت:', cleanupError)
      }

      return { success: false, message: error.message }
    }
  })

  // اختبار الاتصال بالمجلد المشترك
  ipcMain.handle('test-sync-connection', async (_event: IpcMainInvokeEvent, sharedFolder: string) => {
    try {
      if (!sharedFolder) {
        return { success: false, message: 'لم يتم تحديد مسار المجلد' }
      }

      // فحص وجود المجلد
      if (!fs.existsSync(sharedFolder)) {
        return { success: false, message: 'المجلد غير موجود أو لا يمكن الوصول إليه' }
      }

      // فحص صلاحيات الكتابة
      const testFile = path.join(sharedFolder, 'test-write.tmp')
      try {
        fs.writeFileSync(testFile, 'test')
        fs.unlinkSync(testFile)
      } catch {
        return { success: false, message: 'لا توجد صلاحيات كتابة في المجلد' }
      }

      return { success: true, message: 'تم الاتصال بنجاح' }
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في اختبار الاتصال:', error)
      return { success: false, message: error.message }
    }
  })

  // جلب حالة المزامنة
  ipcMain.handle('get-sync-status', async () => {
    try {
      // جلب الإعدادات من قاعدة البيانات
      const db = databaseService.getDatabase()
      const settingsQuery = db.exec('SELECT * FROM settings WHERE key LIKE "sync_%"')

      const settings: any[] = []
      if (settingsQuery.length > 0 && settingsQuery[0].values.length > 0) {
        const columns = settingsQuery[0].columns
        settingsQuery[0].values.forEach((values: any[]) => {
          const setting: any = {}
          columns.forEach((col: string, index: number) => {
            setting[col] = values[index]
          })
          settings.push(setting)
        })
      }

      const settingsMap: { [key: string]: string } = {}
      if (settings && Array.isArray(settings)) {
        settings.forEach((row: any) => {
          settingsMap[row.key] = row.value
        })
      }

      const syncEnabled = settingsMap['sync_enabled'] === 'true'
      const sharedFolder = settingsMap['sync_shared_folder'] || ''
      const deviceRole = settingsMap['sync_device_role'] || 'branch'

      // فحص الاتصال بالمجلد المشترك
      let isConnected = false
      let lastSync = new Date(0)
      let connectedDevices: any[] = []

      if (syncEnabled && sharedFolder) {
        try {
          // فحص وجود المجلد
          isConnected = fs.existsSync(sharedFolder)

          if (isConnected) {
            // جلب آخر وقت مزامنة من ملف السجل
            const logFile = path.join(sharedFolder, 'sync-log.txt')
            if (fs.existsSync(logFile)) {
              const logContent = fs.readFileSync(logFile, 'utf8')
              const lines = logContent.split('\n').filter((line: string) => line.trim())
              if (lines.length > 0) {
                const lastLine = lines[lines.length - 1]
                const timeMatch = lastLine.match(/\[(.*?)\]/)
                if (timeMatch) {
                  lastSync = new Date(timeMatch[1])
                }
              }
            }

            // جلب قائمة الأجهزة المتصلة
            const devicesFile = path.join(sharedFolder, 'devices.json')
            if (fs.existsSync(devicesFile)) {
              try {
                const devicesData = fs.readFileSync(devicesFile, 'utf8')
                const devices = JSON.parse(devicesData)

                // فلترة الأجهزة النشطة (آخر مزامنة خلال آخر ساعة)
                const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
                connectedDevices = devices.filter((device: any) => {
                  const deviceLastSync = new Date(device.lastSync)
                  return deviceLastSync > oneHourAgo
                })
              } catch (parseError) {
                Logger.warn('SyncHandlers', 'خطأ في قراءة ملف الأجهزة:', parseError)
              }
            }
          }
        } catch (error) {
          Logger.warn('SyncHandlers', 'خطأ في فحص المجلد المشترك:', error)
          isConnected = false
        }
      }

      // إذا كان هناك خدمة مزامنة نشطة، استخدم بياناتها
      if (fileSyncService) {
        try {
          const serviceStatus = await fileSyncService.getSyncStatus()
          if (serviceStatus.lastSync > lastSync) {
            lastSync = serviceStatus.lastSync
          }
        } catch (error) {
          Logger.warn('SyncHandlers', 'خطأ في جلب حالة الخدمة:', error)
        }
      }

      return {
        enabled: syncEnabled,
        deviceRole: deviceRole,
        isActive: !!fileSyncService,
        lastSync: lastSync,
        sharedFolder: sharedFolder,
        isConnected: isConnected,
        connectedDevices: connectedDevices
      }

    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في جلب حالة المزامنة:', error)
      return {
        enabled: false,
        deviceRole: 'branch',
        isActive: false,
        lastSync: new Date(),
        sharedFolder: '',
        isConnected: false,
        connectedDevices: []
      }
    }
  })

  // تنفيذ مزامنة فورية
  ipcMain.handle('force-sync-now', async () => {
    try {
      if (!fileSyncService) {
        return { success: false, message: 'المزامنة غير مفعلة' }
      }

      await fileSyncService.forceSyncNow()
      return { success: true, message: 'تم تنفيذ المزامنة بنجاح' }
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في المزامنة الفورية:', error)
      return { success: false, message: error.message }
    }
  })

  // اختيار مجلد
  ipcMain.handle('select-folder', async () => {
    try {
      Logger.info('SyncHandlers', 'فتح نافذة اختيار المجلد المشترك...')

      const result = dialog.showOpenDialogSync({
        properties: ['openDirectory', 'createDirectory'],
        title: 'اختر المجلد المشترك للمزامنة',
        buttonLabel: 'اختيار هذا المجلد'
      })

      if (!result || result.length === 0) {
        Logger.info('SyncHandlers', 'تم إلغاء اختيار المجلد')
        return { success: false, message: 'تم إلغاء اختيار المجلد' }
      }

      const selectedPath = result[0]
      Logger.success('SyncHandlers', `تم اختيار المجلد: ${selectedPath}`)

      // فحص إمكانية الوصول للمجلد
      if (!fs.existsSync(selectedPath)) {
        return { success: false, message: 'المجلد المحدد غير موجود' }
      }

      // فحص صلاحيات الكتابة
      try {
        const testFile = path.join(selectedPath, 'test-write-permission.tmp')
        fs.writeFileSync(testFile, 'test')
        fs.unlinkSync(testFile)
        Logger.info('SyncHandlers', 'تم التحقق من صلاحيات الكتابة بنجاح')
      } catch (writeError) {
        Logger.error('SyncHandlers', 'لا توجد صلاحيات كتابة في المجلد:', writeError)
        return { success: false, message: 'لا توجد صلاحيات كتابة في المجلد المحدد' }
      }

      return { success: true, path: selectedPath }
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في اختيار المجلد:', error)
      return { success: false, message: `خطأ في اختيار المجلد: ${error.message}` }
    }
  })

  // إنشاء مجلد مشترك ذكي تلقائياً
  ipcMain.handle('create-smart-shared-folder', async () => {
    try {
      if (!smartSyncService) {
        return { success: false, message: 'خدمة المزامنة الذكية غير متاحة' }
      }

      Logger.info('SyncHandlers', 'بدء إنشاء المجلد المشترك الذكي...')
      const result = await smartSyncService.createSmartSharedFolder()

      if (result.success) {
        Logger.success('SyncHandlers', `تم إنشاء المجلد المشترك: ${result.path}`)
      }

      return result
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في إنشاء المجلد المشترك الذكي:', error)
      return { success: false, message: `خطأ في إنشاء المجلد المشترك: ${error.message}` }
    }
  })

  // البحث عن الأجهزة المتصلة
  ipcMain.handle('discover-connected-devices', async () => {
    try {
      if (!smartSyncService) {
        Logger.warn('SyncHandlers', 'خدمة المزامنة الذكية غير متاحة')
        return {
          success: false,
          devices: [],
          message: 'خدمة المزامنة الذكية غير متاحة'
        }
      }

      Logger.info('SyncHandlers', 'بدء البحث عن الأجهزة المتصلة...')
      const devices = await smartSyncService.discoverConnectedDevices()

      Logger.success('SyncHandlers', `تم العثور على ${devices.length} جهاز متاح`)
      return { success: true, devices }
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في البحث عن الأجهزة:', error)
      return {
        success: false,
        devices: [],
        message: error.message || 'خطأ غير معروف في البحث عن الأجهزة'
      }
    }
  })

  // اختيار جهاز من القائمة المكتشفة
  ipcMain.handle('select-device-for-sync', async (_event: IpcMainInvokeEvent, deviceInfo: { ip: string; hostname?: string }) => {
    try {
      if (!smartSyncService) {
        return { success: false, message: 'خدمة المزامنة الذكية غير متاحة' }
      }

      const deviceName = deviceInfo.hostname || deviceInfo.ip.replace(/\./g, '-')
      Logger.info('SyncHandlers', `البحث عن مجلد مشترك في الجهاز: ${deviceName} (${deviceInfo.ip})`)

      // قائمة أسماء المجلدات المشتركة المحتملة
      const possibleSharedNames = [
        'ZET.IA-Shared',
        'ZETIA-Shared',
        'ZETIAShared',
        'Accounting-Shared',
        'AccountingShared',
        'Shared',
        'Share'
      ]

      // البحث عن المجلدات المشتركة المتاحة
      let foundSharedPath: string | null = null
      const availableShares: string[] = []

      try {
        // أولاً: محاولة الحصول على قائمة المشاركات المتاحة

        try {
          const { stdout } = await execAsync(`net view \\\\${deviceInfo.ip}`, { timeout: 5000 })

          // استخراج أسماء المشاركات من الناتج
          const lines = stdout.split('\n')
          for (const line of lines) {
            const trimmedLine = line.trim()
            if (trimmedLine && !trimmedLine.startsWith('\\\\') && !trimmedLine.includes('Command completed')) {
              const parts = trimmedLine.split(/\s+/)
              if (parts.length > 0 && parts[0]) {
                availableShares.push(parts[0])
              }
            }
          }

          Logger.info('SyncHandlers', `المشاركات المتاحة في ${deviceName}:`, availableShares)
        } catch (netViewError) {
          Logger.warn('SyncHandlers', `لا يمكن الحصول على قائمة المشاركات من ${deviceInfo.ip}:`, netViewError)
        }

        // ثانياً: البحث عن المجلدات المشتركة المحتملة
        for (const shareName of possibleSharedNames) {
          const sharedPath = `\\\\${deviceInfo.ip}\\${shareName}`

          try {
            if (fs.existsSync(sharedPath)) {
              // التحقق من وجود ملفات قاعدة البيانات
              const dbFiles = fs.readdirSync(sharedPath).filter((file: string) =>
                file.endsWith('.db') || file.endsWith('.sqlite') || file.endsWith('.sqlite3')
              )

              if (dbFiles.length > 0) {
                foundSharedPath = sharedPath
                Logger.success('SyncHandlers', `تم العثور على مجلد مشترك مع قاعدة بيانات: ${sharedPath}`)
                break
              } else {
                Logger.info('SyncHandlers', `تم العثور على مجلد مشترك لكن بدون قاعدة بيانات: ${sharedPath}`)
              }
            }
          } catch (accessError) {
            Logger.debug('SyncHandlers', `لا يمكن الوصول للمسار ${sharedPath}:`, accessError)
          }
        }

        // إذا لم نجد مجلد مشترك، نبحث في المشاركات المتاحة
        if (!foundSharedPath && availableShares.length > 0) {
          for (const shareName of availableShares) {
            const sharedPath = `\\\\${deviceInfo.ip}\\${shareName}`

            try {
              if (fs.existsSync(sharedPath)) {
                const files = fs.readdirSync(sharedPath)
                const dbFiles = files.filter((file: string) =>
                  file.endsWith('.db') || file.endsWith('.sqlite') || file.endsWith('.sqlite3')
                )

                if (dbFiles.length > 0) {
                  foundSharedPath = sharedPath
                  Logger.success('SyncHandlers', `تم العثور على مجلد مشترك في المشاركة ${shareName}: ${sharedPath}`)
                  break
                }
              }
            } catch (accessError) {
              Logger.debug('SyncHandlers', `لا يمكن الوصول للمشاركة ${shareName}:`, accessError)
            }
          }
        }

        if (foundSharedPath) {
          return {
            success: true,
            path: foundSharedPath,
            message: `تم العثور على مجلد مشترك على الجهاز: ${deviceName}`
          }
        } else {
          // لم نجد مجلد مشترك مناسب
          const availableSharesText = availableShares.length > 0
            ? `\nالمشاركات المتاحة: ${availableShares.join(', ')}`
            : ''

          return {
            success: false,
            message: `لم يتم العثور على مجلد مشترك مناسب في الجهاز ${deviceName}.${availableSharesText}\nتأكد من أن الجهاز الرئيسي قام بإنشاء مجلد مشترك وأنه يحتوي على قاعدة بيانات.`
          }
        }

      } catch (error) {
        Logger.warn('SyncHandlers', `خطأ في البحث عن المجلد المشترك في ${deviceInfo.ip}:`, error)
        return {
          success: false,
          message: `لا يمكن الوصول للجهاز ${deviceName}. تأكد من تفعيل مشاركة الملفات على الجهاز المستهدف وأن الجهازين متصلين بنفس الشبكة.`
        }
      }

    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في اختيار الجهاز:', error)
      return { success: false, message: `خطأ في اختيار الجهاز: ${error.message}` }
    }
  })

  // الحصول على معلومات الشبكة
  ipcMain.handle('get-network-info', async () => {
    try {
      if (!smartSyncService) {
        return { success: false, message: 'خدمة المزامنة الذكية غير متاحة' }
      }

      const networkInfo = await smartSyncService.getNetworkInfo()
      return { success: true, data: networkInfo }
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في الحصول على معلومات الشبكة:', error)
      return { success: false, message: error.message }
    }
  })

  // تحديد دور الجهاز تلقائياً (مضيف أم عميل)
  ipcMain.handle('determine-device-role', async () => {
    try {
      if (!smartSyncService) {
        return { success: false, message: 'خدمة المزامنة الذكية غير متاحة' }
      }

      Logger.info('SyncHandlers', 'تحديد دور الجهاز تلقائياً...')
      const role = await smartSyncService.determineDeviceRole()

      Logger.success('SyncHandlers', `تم تحديد دور الجهاز: ${role}`)
      return {
        success: true,
        role,
        message: role === 'host'
          ? 'تم تحديد هذا الجهاز كمضيف (سيحتوي على الملف المشترك)'
          : 'تم تحديد هذا الجهاز كعميل (سيصل للملف من جهاز آخر)'
      }
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في تحديد دور الجهاز:', error)
      return {
        success: false,
        message: error.message || 'خطأ غير معروف في تحديد دور الجهاز',
        role: 'host' // افتراضي
      }
    }
  })

  // فحص نوع قاعدة البيانات المستخدمة حالياً
  ipcMain.handle('get-database-type', async () => {
    try {
      const dbType = databaseService.getDatabaseType()
      const dbPath = databaseService.getDatabasePath()

      return {
        success: true,
        type: dbType,
        path: dbPath,
        message: dbType === 'local'
          ? 'يتم استخدام قاعدة البيانات المحلية'
          : 'يتم استخدام قاعدة البيانات المشتركة'
      }
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في فحص نوع قاعدة البيانات:', error)
      return {
        success: false,
        message: error.message || 'خطأ غير معروف في فحص نوع قاعدة البيانات'
      }
    }
  })

  // إجبار التبديل إلى قاعدة البيانات المشتركة
  ipcMain.handle('force-switch-to-shared-database', async (_event: IpcMainInvokeEvent, sharedDbPath: string) => {
    try {
      Logger.info('SyncHandlers', 'إجبار التبديل إلى قاعدة البيانات المشتركة:', sharedDbPath)

      const success = await databaseService.switchToSharedDatabase(sharedDbPath)

      if (success) {
        // إرسال إشعار لواجهة المستخدم لإعادة التحميل
        const mainWindow = BrowserWindow.getAllWindows()[0]
        if (mainWindow) {
          mainWindow.webContents.send('database-switched', {
            type: 'shared',
            path: sharedDbPath,
            message: 'تم التبديل إلى قاعدة البيانات المشتركة بنجاح'
          })
        }

        return {
          success: true,
          message: 'تم التبديل إلى قاعدة البيانات المشتركة بنجاح'
        }
      } else {
        return {
          success: false,
          message: 'فشل في التبديل إلى قاعدة البيانات المشتركة'
        }
      }
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في إجبار التبديل إلى قاعدة البيانات المشتركة:', error)
      return {
        success: false,
        message: error.message || 'خطأ غير معروف في التبديل'
      }
    }
  })

  // إجبار التبديل إلى قاعدة البيانات المحلية
  ipcMain.handle('force-switch-to-local-database', async () => {
    try {
      Logger.info('SyncHandlers', 'إجبار التبديل إلى قاعدة البيانات المحلية')

      const success = await databaseService.switchToLocalDatabase()

      if (success) {
        // إرسال إشعار لواجهة المستخدم لإعادة التحميل مع الحفاظ على الجلسة
        const mainWindow = BrowserWindow.getAllWindows()[0]
        if (mainWindow) {
          mainWindow.webContents.send('database-switched', {
            type: 'local',
            message: 'تم التبديل إلى قاعدة البيانات المحلية بنجاح',
            preserveSession: true
          })
        }

        return {
          success: true,
          message: 'تم التبديل إلى قاعدة البيانات المحلية بنجاح'
        }
      } else {
        return {
          success: false,
          message: 'فشل في التبديل إلى قاعدة البيانات المحلية'
        }
      }
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في إجبار التبديل إلى قاعدة البيانات المحلية:', error)
      return {
        success: false,
        message: error.message || 'خطأ غير معروف في التبديل'
      }
    }
  })

  // الحصول على حالة قاعدة البيانات المفصلة
  ipcMain.handle('get-database-status', async () => {
    try {
      const databaseService = DatabaseService.getInstance()

      // الحصول على معلومات مفصلة عن قاعدة البيانات
      const status = databaseService.getDatabaseStatus()

      return {
        success: true,
        data: {
          currentPath: status.path,
          isShared: status.isShared,
          type: status.isShared ? 'shared' : 'local',
          exists: status.exists,
          size: status.size,
          sizeKB: status.sizeKB,
          lastModified: status.lastModified,
          isConnected: status.isConnected,
          tableCount: status.tableCount,
          recordCount: status.recordCount,
          isEmpty: status.isEmpty,
          isNetworkPath: status.isNetworkPath
        }
      }
    } catch (error: any) {
      Logger.error('SyncHandlers', 'خطأ في الحصول على حالة قاعدة البيانات:', error)
      return {
        success: false,
        message: error.message || 'خطأ غير معروف'
      }
    }
  })
}

// تهيئة المزامنة عند بدء التطبيق
export const initializeSyncService = async () => {
  try {
    const db = databaseService.getDatabase()
    const settingsQuery = db.exec('SELECT * FROM settings WHERE key LIKE "sync_%"')

    const settings: any[] = []
    if (settingsQuery.length > 0 && settingsQuery[0].values.length > 0) {
      const columns = settingsQuery[0].columns
      settingsQuery[0].values.forEach((values: any[]) => {
        const setting: any = {}
        columns.forEach((col: string, index: number) => {
          setting[col] = values[index]
        })
        settings.push(setting)
      })
    }

    const settingsMap: { [key: string]: string } = {}
    if (settings && Array.isArray(settings)) {
      settings.forEach((row: any) => {
        settingsMap[row.key] = row.value
      })
    }

    const syncEnabled = settingsMap['sync_enabled'] === 'true'

    if (syncEnabled) {
      const syncConfig: SyncConfig = {
        enabled: true,
        deviceRole: settingsMap['sync_device_role'] as 'main' | 'branch' || 'branch',
        sharedFolder: settingsMap['sync_shared_folder'] || '',
        syncInterval: parseInt(settingsMap['sync_interval'] || '5')
      }

      if (syncConfig.sharedFolder) {
        fileSyncService = new FileSyncService(syncConfig, databaseService)
        await fileSyncService.startSync()
        Logger.success('SyncHandlers', 'تم تهيئة خدمة المزامنة تلقائياً')
      }
    }
  } catch (error: any) {
    Logger.error('SyncHandlers', 'خطأ في تهيئة خدمة المزامنة:', error)
  }
}
