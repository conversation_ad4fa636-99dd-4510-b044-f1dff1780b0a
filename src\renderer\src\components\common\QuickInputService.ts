import { SafeLogger as Logger } from '../../utils/logger'
// خدمة تسريع الإدخالات وحفّ البيانات السابقة

export interface QuickInputData {
  [key: string]: any
}

export interface RecentData {
  id: string
  label: string
  data: QuickInputData
  timestamp: number
  frequency: number
}

export class QuickInputService {
  private static readonly STORAGE_KEY = 'quickInputData'
  private static readonly MAX_RECENT_ITEMS = 10

  // حفّ بيانات الإدخال الأخيرة
  static saveRecentInput(formType: string, data: QuickInputData, label?: string): void {
    try {
      const recentData = this.getRecentData(formType)
      const id = this.generateId(data)
      const displayLabel = label || this.generateLabel(data)
      
      // البحث عن البيانات الموجودة
      const existingIndex = recentData.findIndex(item => item.id === id)
      
      if (existingIndex >= 0) {
        // تحديث البيانات الموجودة
        recentData[existingIndex] = {
          ...recentData[existingIndex],
          data,
          timestamp: Date.now(),
          frequency: recentData[existingIndex].frequency + 1
        }
      } else {
        // إضافة بيانات جديدة
        recentData.unshift({
          id,
          label: displayLabel,
          data,
          timestamp: Date.now(),
          frequency: 1
        })
      }

      // الاحتفاّ بأحدث العناصر فقط
      const sortedData = recentData
        .sort((a, b) => b.frequency - a.frequency || b.timestamp - a.timestamp)
        .slice(0, this.MAX_RECENT_ITEMS)

      localStorage.setItem(`${this.STORAGE_KEY}_${formType}`, JSON.stringify(sortedData))
    } catch (error) {
      Logger.error('QuickInputService', 'خطأ في حفّ البيانات السريعة:', error)
    }
  }

  // استرجاع البيانات السابقة
  static getRecentData(formType: string): RecentData[] {
    try {
      const stored = localStorage.getItem(`${this.STORAGE_KEY}_${formType}`)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      Logger.error('QuickInputService', 'خطأ في استرجاع البيانات السريعة:', error)
      return []
    }
  }

  // الحصول على أكثر البيانات استخداماً
  static getMostUsedData(formType: string): RecentData | null {
    const recentData = this.getRecentData(formType)
    return recentData.length > 0 ? recentData[0] : null
  }

  // الحصول على آخر البيانات المدخلة
  static getLastInputData(formType: string): RecentData | null {
    const recentData = this.getRecentData(formType)
    const sortedByTime = recentData.sort((a, b) => b.timestamp - a.timestamp)
    return sortedByTime.length > 0 ? sortedByTime[0] : null
  }

  // حذف بيانات معينة
  static removeRecentData(formType: string, id: string): void {
    try {
      const recentData = this.getRecentData(formType)
      const filteredData = recentData.filter(item => item.id !== id)
      localStorage.setItem(`${this.STORAGE_KEY}_${formType}`, JSON.stringify(filteredData))
    } catch (error) {
      Logger.error('QuickInputService', 'خطأ في حذف البيانات السريعة:', error)
    }
  }

  // مسح جميع البيانات السابقة
  static clearRecentData(formType: string): void {
    try {
      localStorage.removeItem(`${this.STORAGE_KEY}_${formType}`)
    } catch (error) {
      Logger.error('QuickInputService', 'خطأ في مسح البيانات السريعة:', error)
    }
  }

  // إنشاء معرف فريد للبيانات
  private static generateId(data: QuickInputData): string {
    const key = Object.keys(data)
      .sort()
      .map(k => `${k}:${data[k]}`)
      .join('|')

    // استخدام طريقة آمنة لتحويل النص العربي إلى base64
    try {
      // استخدام Buffer إذا كان متاحاً (في Node.js/Electron)
      if (typeof Buffer !== 'undefined') {
        return Buffer.from(key, 'utf8').toString('base64').substring(0, 16)
      }

      // استخدام طريقة آمنة مع btoa
      const encoded = encodeURIComponent(key)
      return btoa(encoded).substring(0, 16)
    } catch (error) {
      // في حالة فشل التشفير، استخدم hash بسيط
      Logger.warn('QuickInputService', 'فشل في تشفير المعرف، استخدام hash بديل:', error)
      return this.simpleHash(key).substring(0, 16)
    }
  }

  // دالة hash بسيطة كبديل
  private static simpleHash(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // تحويل إلى 32bit integer
    }
    return Math.abs(hash).toString(36).padStart(16, '0')
  }

  // إنشاء تسمية للبيانات
  private static generateLabel(data: QuickInputData): string {
    const importantFields = ['customer_name', 'item_name', 'department_name', 'supplier_name']
    
    for (const field of importantFields) {
      if (data[field]) {
        return `${data[field]} - ${new Date().toLocaleDateString('ar-EG')}`
      }
    }

    return `إدخال سريع - ${new Date().toLocaleDateString('ar-EG')}`
  }

  // الحصول على اقتراحات للإكمال التلقائي
  static getAutoCompleteOptions(formType: string, fieldName: string): string[] {
    try {
      const recentData = this.getRecentData(formType)
      const values = recentData
        .map(item => item.data[fieldName])
        .filter(value => value && typeof value === 'string')
        .filter((value, index, array) => array.indexOf(value) === index) // إزالة التكرار
        .slice(0, 5) // أول 5 قيم

      return values
    } catch (error) {
      Logger.error('QuickInputService', 'خطأ في الحصول على اقتراحات الإكمال التلقائي:', error)
      return []
    }
  }

  // حفّ إعدادات المستخدم المفضلة
  static saveUserPreferences(formType: string, preferences: any): void {
    try {
      localStorage.setItem(`${this.STORAGE_KEY}_preferences_${formType}`, JSON.stringify(preferences))
    } catch (error) {
      Logger.error('QuickInputService', 'خطأ في حفّ إعدادات المستخدم:', error)
    }
  }

  // استرجاع إعدادات المستخدم المفضلة
  static getUserPreferences(formType: string): any {
    try {
      const stored = localStorage.getItem(`${this.STORAGE_KEY}_preferences_${formType}`)
      return stored ? JSON.parse(stored) : {}
    } catch (error) {
      Logger.error('QuickInputService', 'خطأ في استرجاع إعدادات المستخدم:', error)
      return {}
    }
  }
}

// مكون للإدخال السريع
export interface QuickInputProps {
  formType: string
  onSelectData: (data: QuickInputData) => void
  onClearData?: () => void
}

export const QuickInputHelper = {
  // إنشاء خيارات الإدخال السريع
  createQuickInputOptions: (formType: string): RecentData[] => {
    return QuickInputService.getRecentData(formType)
  },

  // تطبيق البيانات على النموذج
  applyDataToForm: (form: any, data: QuickInputData): void => {
    try {
      // تطبيق البيانات مع تجاهل الحقول الفارغة
      const cleanData = Object.keys(data).reduce((acc, key) => {
        if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
          acc[key] = data[key]
        }
        return acc
      }, {} as any)

      form.setFieldsValue(cleanData)
    } catch (error) {
      Logger.error('QuickInputService', 'خطأ في تطبيق البيانات على النموذج:', error)
    }
  },

  // إنشاء قائمة منسدلة للإدخال السريع
  createQuickSelectOptions: (recentData: RecentData[]) => {
    return recentData.map(item => ({
      label: `${item.label} (استخدم ${item.frequency} مرة)`,
      value: item.id,
      data: item.data
    }))
  }
}
