import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Select, 
  Input, 
  Space, 
  message, 
  Row,
  Col,
  Statistic,
  DatePicker,
  Tag,
  Descriptions,
  InputNumber,
  Divider
} from 'antd'
import {
  DollarOutlined,
  CreditCardOutlined,
  FileTextOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
  CheckOutlined,
  BankOutlined,
  UserOutlined,
  PrinterOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'
import { useCurrentUser } from '../../hooks/useCurrentUser'
import dayjs from 'dayjs'
import { getCurrencySymbol } from '../../utils/settings'
import { ReceiptPrintButton } from '../common'

const { Option } = Select
const { TextArea } = Input

interface PurchasePaymentManagementProps {
  onBack: () => void
}

const PurchasePaymentManagement: React.FC<PurchasePaymentManagementProps> = ({ onBack }) => {
  const { userId } = useCurrentUser()
  const [unpaidInvoices, setUnpaidInvoices] = useState([])
  const [bankAccounts, setBankAccounts] = useState([])
  const [loading, setLoading] = useState(false)
  const [paymentModalVisible, setPaymentModalVisible] = useState(false)
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null)
  const [paymentMethod, setPaymentMethod] = useState('payment_voucher')
  const [form] = Form.useForm()

  // حالة لتخزين بيانات الإيصال المطبوع
  const [lastPaymentReceipt, setLastPaymentReceipt] = useState<any>(null)

  useEffect(() => {
    loadUnpaidInvoices()
    loadBankAccounts()
  }, [])

  const loadUnpaidInvoices = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.getUnpaidInvoices('supplier')
      if (response.success) {
        setUnpaidInvoices(response.data)
      } else {
        message.error('فشل في تحميل فواتير الموردين غير المدفوعة')
      }
    } catch (error) {
      message.error('خطأ في تحميل فواتير الموردين غير المدفوعة')
    }
    setLoading(false)
  }

  const loadBankAccounts = async () => {
    try {
      const response = await window.electronAPI.getBankAccounts()
      if (response.success) {
        setBankAccounts(response.data)
      }
    } catch (error) {
      Logger.error('PurchasePaymentManagement', 'خطأ في تحميل الحسابات المصرفية:', error)
    }
  }

  const handlePayment = async (values: any) => {
    try {
      let paymentResponse
      
      // إنشاء الدفعة حسب نوع الدفع
      if (paymentMethod === 'check') {
        // إنشاء شيك صادر من الشركة
        const checkData = {
          check_number: values.check_number,
          bank_account_id: values.bank_account_id,
          amount: values.amount,
          issue_date: values.payment_date.format('YYYY-MM-DD'),
          due_date: values.due_date ? values.due_date.format('YYYY-MM-DD') : values.payment_date.format('YYYY-MM-DD'),
          payee_name: selectedInvoice.supplier_name || 'مورد',
          notes: values.notes,
          check_type: 'issued',
          original_payer: 'الشركة',
          current_holder: selectedInvoice.supplier_name || 'مورد',
          is_company_check: 1,
          reference_type: 'purchase_invoice',
          reference_id: selectedInvoice.id,
          created_by: userId || 1
        }
        paymentResponse = await window.electronAPI.createCheck(checkData)
      } else if (paymentMethod === 'payment_voucher') {
        // إنشاء سند دفع
        const voucherData = {
          voucher_number: values.voucher_number,
          amount: values.amount,
          payment_date: values.payment_date.format('YYYY-MM-DD'),
          payee_name: selectedInvoice.supplier_name || 'مورد',
          description: `دفع لفاتورة مشتريات رقم ${selectedInvoice.number}`,
          payment_method: values.voucher_payment_method || 'cash',
          bank_account_id: values.bank_account_id,
          reference_type: 'purchase_invoice',
          reference_id: selectedInvoice.id,
          currency_id: 1,
          created_by: userId || 1,
          notes: values.notes
        }
        paymentResponse = await window.electronAPI.createPaymentVoucher(voucherData)
      }

      if (paymentResponse && paymentResponse.success) {
        // ربط الدفعة بالفاتورة
        const linkData = {
          invoice_type: 'purchase_invoice',
          invoice_id: selectedInvoice.id,
          payment_type: paymentMethod === 'check' ? 'check' : 'payment_voucher',
          payment_id: paymentResponse.data?.checkId || paymentResponse.data?.voucherId,
          amount: values.amount
        }

        const linkResponse = await window.electronAPI.linkInvoiceToPayment(linkData)
        if (linkResponse.success) {
          // إنشاء بيانات الإيصال للطباعة
          const receiptData = {
            id: paymentResponse.data?.checkId || paymentResponse.data?.voucherId || Date.now(),
            receiptNumber: values.voucher_number || values.check_number || `PAY-${Date.now()}`,
            receiptDate: values.payment_date.format('YYYY-MM-DD'),
            type: 'receipt' as const, // سند صرف للموردين
            amount: values.amount,
            paymentMethod: paymentMethod === 'check' ? 'check' :
                          values.voucher_payment_method === 'bank' ? 'bank' : 'cash',
            customerName: selectedInvoice.supplier_name || 'مورد',
            description: `دفع لفاتورة مشتريات رقم ${selectedInvoice.number}`,
            notes: values.notes,
            referenceNumber: selectedInvoice.number,
            bankName: paymentMethod === 'check' ?
                     bankAccounts.find(acc => acc.id === values.bank_account_id)?.bank_name : undefined,
            checkNumber: paymentMethod === 'check' ? values.check_number : undefined
          }

          setLastPaymentReceipt(receiptData)
          message.success('تم تسجيل الدفعة وربطها بالفاتورة بنجاح')
          setPaymentModalVisible(false)
          form.resetFields()
          loadUnpaidInvoices()
        } else {
          message.error('تم إنشاء الدفعة ولكن فشل في ربطها بالفاتورة')
        }
      } else {
        message.error('فشل في إنشاء الدفعة')
      }
    } catch (error) {
      message.error('خطأ في تسجيل الدفعة')
    }
  }

  const showPaymentModal = (invoice: any) => {
    setSelectedInvoice(invoice)
    setPaymentModalVisible(true)
    form.setFieldsValue({
      payment_date: dayjs(),
      amount: invoice.remaining_amount
    })
  }

  const generateVoucherNumber = async () => {
    try {
      const response = await window.electronAPI.generatePaymentVoucherNumber()
      if (response.success) {
        form.setFieldsValue({ voucher_number: response.data.voucherNumber })
      }
    } catch (error) {
      message.error('خطأ في إنشاء رقم السند')
    }
  }

  const generateCheckNumber = async () => {
    try {
      const response = await window.electronAPI.generateCheckNumber()
      if (response.success) {
        form.setFieldsValue({ check_number: response.data.checkNumber })
      }
    } catch (error) {
      message.error('خطأ في إنشاء رقم الشيك')
    }
  }

  const columns = [
    {
      title: 'رقم الفاتورة',
      dataIndex: 'number',
      key: 'number',
      render: (number: string) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{number}</span>
      )
    },
    {
      title: 'المورد',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      render: (name: string, record: any) => (
        <Space>
          <BankOutlined style={{ color: '#1890ff' }} />
          <span>{name || `مورد ${record.entity_id}`}</span>
        </Space>
      )
    },
    {
      title: 'تاريخ الفاتورة',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'المبلغ الإجمالي',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (amount: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ₪ {amount?.toLocaleString() || 0}
        </span>
      )
    },
    {
      title: 'المبلغ المدفوع',
      dataIndex: 'paid_amount',
      key: 'paid_amount',
      render: (amount: number) => (
        <span style={{ color: '#1890ff' }}>
          {getCurrencySymbol()} {amount?.toLocaleString() || 0}
        </span>
      )
    },
    {
      title: 'المبلغ المتبقي',
      dataIndex: 'remaining_amount',
      key: 'remaining_amount',
      render: (amount: number) => (
        <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
          {getCurrencySymbol()} {amount?.toLocaleString() || 0}
        </span>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_, record: any) => (
        <Button
          type="primary"
          size="small"
          icon={<DollarOutlined />}
          onClick={() => showPaymentModal(record)}
        >
          تسجيل دفعة
        </Button>
      )
    }
  ]

  const stats = {
    totalInvoices: unpaidInvoices.length,
    totalAmount: unpaidInvoices.reduce((sum: number, invoice: any) => sum + (invoice.remaining_amount || 0), 0)
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>💳 إدارة مدفوعات الموردين</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            تسجيل وربط المدفوعات بفواتير المشتريات
          </p>
        </div>
        <Space>
          {lastPaymentReceipt && (
            <ReceiptPrintButton
              receiptData={lastPaymentReceipt}
              buttonText="طباعة آخر إيصال"
              size="middle"
              onPrintSuccess={() => message.success('تم طباعة الإيصال بنجاح')}
              onPrintError={() => message.error('فشل في طباعة الإيصال')}
            />
          )}
          <Button
            type="default"
            icon={<ArrowLeftOutlined />}
            onClick={onBack}
          >
            العودة
          </Button>
        </Space>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="فواتير غير مدفوعة"
              value={stats.totalInvoices}
              valueStyle={{ color: '#1890ff' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="إجمالي المبالغ المستحقة"
              value={stats.totalAmount}
              valueStyle={{ color: '#ff4d4f' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="متوسط قيمة الفاتورة"
              value={stats.totalInvoices > 0 ? stats.totalAmount / stats.totalInvoices : 0}
              valueStyle={{ color: '#52c41a' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      {/* جدول فواتير الموردين غير المدفوعة */}
      <Card title="فواتير الموردين غير المدفوعة">
        <Table
          columns={columns}
          dataSource={unpaidInvoices}
          rowKey={(record: any) => `purchase-${record.id}`}
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* نموذج تسجيل الدفعة */}
      <Modal
        title="تسجيل دفعة للمورد"
        open={paymentModalVisible}
        onCancel={() => {
          setPaymentModalVisible(false)
          setSelectedInvoice(null)
          form.resetFields()
        }}
        footer={null}
        width={800}
      >
        {selectedInvoice && (
          <>
            <Descriptions title="بيانات الفاتورة" bordered size="small" style={{ marginBottom: '24px' }}>
              <Descriptions.Item label="رقم الفاتورة">{selectedInvoice.number}</Descriptions.Item>
              <Descriptions.Item label="المورد">{selectedInvoice.supplier_name || `مورد ${selectedInvoice.entity_id}`}</Descriptions.Item>
              <Descriptions.Item label="تاريخ الفاتورة">{dayjs(selectedInvoice.date).format('YYYY-MM-DD')}</Descriptions.Item>
              <Descriptions.Item label="المبلغ الإجمالي">
                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                  ₪ {selectedInvoice.total_amount?.toLocaleString() || 0}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="المبلغ المدفوع">
                <span style={{ color: '#1890ff' }}>
                  ₪ {selectedInvoice.paid_amount?.toLocaleString() || 0}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="المبلغ المتبقي">
                <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
                  ₪ {selectedInvoice.remaining_amount?.toLocaleString() || 0}
                </span>
              </Descriptions.Item>
            </Descriptions>

            <Form
              form={form}
              layout="vertical"
              onFinish={handlePayment}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="payment_method"
                    label="طريقة الدفع"
                    rules={[{ required: true, message: 'يرجى اختيار طريقة الدفع' }]}
                    initialValue="payment_voucher"
                  >
                    <Select
                      placeholder="اختر طريقة الدفع"
                      onChange={(value) => setPaymentMethod(value)}
                    >
                      <Option value="payment_voucher">سند دفع</Option>
                      <Option value="check">شيك الشركة</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="amount"
                    label="المبلغ"
                    rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="0"
                      min={0}
                      max={selectedInvoice.remaining_amount}
                      formatter={value => `${getCurrencySymbol()} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => {
                        const symbol = getCurrencySymbol()
                        const escapedSymbol = symbol.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
                        return (value || '').replace(new RegExp(`${escapedSymbol}\\s?|(,*)`, 'g'), '')
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="payment_date"
                    label="تاريخ الدفع"
                    rules={[{ required: true, message: 'يرجى اختيار تاريخ الدفع' }]}
                  >
                    <DatePicker
                      style={{ width: '100%' }}
                      placeholder="اختر تاريخ الدفع"
                      format="YYYY-MM-DD"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  {paymentMethod === 'payment_voucher' ? (
                    <Form.Item
                      name="voucher_number"
                      label="رقم السند"
                      rules={[{ required: true, message: 'يرجى إدخال رقم السند' }]}
                    >
                      <Input
                        placeholder="PV000001"
                        addonAfter={
                          <Button
                            type="link"
                            size="small"
                            onClick={generateVoucherNumber}
                          >
                            توليد
                          </Button>
                        }
                      />
                    </Form.Item>
                  ) : (
                    <Form.Item
                      name="check_number"
                      label="رقم الشيك"
                      rules={[{ required: true, message: 'يرجى إدخال رقم الشيك' }]}
                    >
                      <Input
                        placeholder="CHK000001"
                        addonAfter={
                          <Button
                            type="link"
                            size="small"
                            onClick={generateCheckNumber}
                          >
                            توليد
                          </Button>
                        }
                      />
                    </Form.Item>
                  )}
                </Col>
              </Row>

              {paymentMethod === 'check' && (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="due_date"
                      label="تاريخ استحقاق الشيك"
                    >
                      <DatePicker
                        style={{ width: '100%' }}
                        placeholder="اختر تاريخ الاستحقاق"
                        format="YYYY-MM-DD"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="bank_account_id"
                      label="الحساب المصرفي"
                      rules={[{ required: paymentMethod === 'check', message: 'يرجى اختيار الحساب المصرفي' }]}
                    >
                      <Select placeholder="اختر الحساب المصرفي">
                        {bankAccounts.map((account: any) => (
                          <Option key={account.id} value={account.id}>
                            {account.bank_name} - {account.account_name} ({account.account_number})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              )}

              {paymentMethod === 'payment_voucher' && (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="voucher_payment_method"
                      label="طريقة الدفع في السند"
                      initialValue="cash"
                    >
                      <Select placeholder="اختر طريقة الدفع">
                        <Option value="cash">نقدي</Option>
                        <Option value="bank_transfer">تحويل بنكي</Option>
                        <Option value="check">شيك</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="bank_account_id"
                      label="الحساب المصرفي (اختياري)"
                    >
                      <Select placeholder="اختر الحساب المصرفي">
                        {bankAccounts.map((account: any) => (
                          <Option key={account.id} value={account.id}>
                            {account.bank_name} - {account.account_name} ({account.account_number})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              )}

              <Form.Item
                name="notes"
                label="ملاحّات"
              >
                <TextArea
                  placeholder="ملاحّات إضافية حول الدفعة"
                  rows={3}
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<CheckOutlined />}>
                    تسجيل الدفعة
                  </Button>
                  <Button onClick={() => {
                    setPaymentModalVisible(false)
                    setSelectedInvoice(null)
                    form.resetFields()
                  }}>
                    إلغاء
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </>
        )}
      </Modal>
    </div>
  )
}

export default PurchasePaymentManagement
