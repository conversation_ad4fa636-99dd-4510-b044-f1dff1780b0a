import React, { useState, useEffect } from 'react'
import {
  Card, Table, Button, Modal, Form, Input, Select, Switch, Space,
  Popconfirm, Typography, Row, Col, Statistic, Tag, App} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  PlusOutlined, EditOutlined, DeleteOutlined, HomeOutlined,
  UserOutlined, CheckCircleOutlined, StopOutlined, EyeOutlined,
  BarcodeOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { Warehouse, User, ApiResponse } from '../../types/global'
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig'
import WarehouseContents from './WarehouseContents'

const { Title } = Typography
const { Option } = Select

const StyledCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
`

const ActionButton = styled(Button)`
  margin-left: 8px;
`

interface WarehouseManagementProps {
  onBack?: () => void
}

const WarehouseManagement: React.FC<WarehouseManagementProps> = ({ onBack: _onBack }) => {
  Logger.info('WarehouseManagement', '🚀 WarehouseManagement component loaded')

  const { message: messageApi } = App.useApp()
  const [warehouses, setWarehouses] = useState<Warehouse[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingWarehouse, setEditingWarehouse] = useState<Warehouse | null>(null)
  const [form] = Form.useForm()
  const [contentsModalVisible, setContentsModalVisible] = useState(false)
  const [selectedWarehouse, setSelectedWarehouse] = useState<Warehouse | null>(null)

  useEffect(() => {
    Logger.info('WarehouseManagement', '🔄 WarehouseManagement useEffect triggered')
    loadWarehouses()
    loadUsers()
  }, [])

  const loadWarehouses = async () => {
    Logger.info('WarehouseManagement', '🔄 بدء تحميل المخازن...')
    setLoading(true)
    try {
      Logger.info('WarehouseManagement', '🔍 فحص window.electronAPI:', !!window.electronAPI)
      if (window.electronAPI) {
        Logger.info('WarehouseManagement', '🔍 فحص دالة getWarehouses:', typeof window.electronAPI.getWarehouses)
        const response = await window.electronAPI.getWarehouses()
        Logger.info('WarehouseManagement', '📥 استجابة getWarehouses:', response)

        if (response && (response as any).success) {
          const warehousesData = (response as any).data || []
          const activeWarehouses = warehousesData.filter((warehouse: Warehouse) => warehouse.is_active)
          setWarehouses(activeWarehouses)
          Logger.info('WarehouseManagement', '✅ تم تحميل ${warehousesData.length} مخزن بنجاح (${activeWarehouses.length} نشط)')
          Logger.info('WarehouseManagement', '📋 المخازن المحملة:', activeWarehouses)
        } else if (Array.isArray(response)) {
          // للتوافق مع الإصدارات القديمة
          const activeWarehouses = response.filter((warehouse: Warehouse) => warehouse.is_active)
          setWarehouses(activeWarehouses)
          Logger.info('WarehouseManagement', '✅ تم تحميل ${response.length} مخزن بنجاح (تنسيق قديم) (${activeWarehouses.length} نشط)')
          Logger.info('WarehouseManagement', '📋 المخازن المحملة:', activeWarehouses)
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل المخازن'
          Logger.error('WarehouseManagement', '❌ خطأ في تحميل المخازن:', errorMessage)
          Logger.error('WarehouseManagement', '❌ الاستجابة الكاملة:', response)
          messageApi.error(errorMessage)
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('WarehouseManagement', '❌ window.electronAPI غير متوفر')
        messageApi.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل المخازن'
      Logger.error('WarehouseManagement', '❌ خطأ في تحميل المخازن:', error)
      messageApi.error(`خطأ في تحميل المخازن: ${errorMessage}`)
    } finally {
      setLoading(false)
      Logger.info('WarehouseManagement', '🏁 انتهاء تحميل المخازن')
    }
  }

  const loadUsers = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getUsers()
        if (response && response.success) {
          const usersData = response.data || []
          setUsers(usersData.filter((user: User) => user.is_active))
          Logger.info('WarehouseManagement', '✅ تم تحميل ${usersData.length} مستخدم بنجاح')
        } else if (Array.isArray(response)) {
          // للتوافق مع الإصدارات القديمة
          setUsers(response.filter((user: User) => user.is_active))
          Logger.info('WarehouseManagement', '✅ تم تحميل ${response.length} مستخدم بنجاح')
        } else {
          const errorMessage = response?.message || 'فشل في تحميل المستخدمين'
          Logger.error('WarehouseManagement', '❌ خطأ في تحميل المستخدمين:', errorMessage)
          messageApi.error(errorMessage)
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('WarehouseManagement', '❌ window.electronAPI غير متوفر')
        messageApi.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل المستخدمين'
      Logger.error('WarehouseManagement', '❌ خطأ في تحميل المستخدمين:', error)
      messageApi.error(`خطأ في تحميل المستخدمين: ${errorMessage}`)
    }
  }

  const handleAdd = () => {
    setEditingWarehouse(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = (warehouse: Warehouse) => {
    setEditingWarehouse(warehouse)
    form.setFieldsValue({
      code: (warehouse as any).code,
      name: warehouse.name,
      location: warehouse.location,
      manager_id: warehouse.manager_id,
      is_active: warehouse.is_active
    })
    setModalVisible(true)
  }

  const handleDelete = async (warehouseId: number) => {
    try {
      if (window.electronAPI) {
        const response: ApiResponse = await window.electronAPI.deleteWarehouse(warehouseId)
        if (response.success) {
          messageApi.success('تم حذف المخزن بنجاح')
          loadWarehouses()
        } else {
          messageApi.error(response.message || 'فشل في حذف المخزن')
        }
      }
    } catch (error) {
      Logger.error('WarehouseManagement', 'خطأ في حذف المخزن:', error)
      messageApi.error('فشل في حذف المخزن')
    }
  }

  const handleViewContents = (warehouse: Warehouse) => {
    setSelectedWarehouse(warehouse)
    setContentsModalVisible(true)
  }

  const generateWarehouseCode = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generateWarehouseCode()
        if (response.success && response.data?.code) {
          form.setFieldsValue({ code: response.data.code })
          messageApi.success('تم إنشاء كود المخزن تلقائياً')
        } else {
          messageApi.error('فشل في إنشاء كود المخزن')
        }
      }
    } catch (error) {
      Logger.error('WarehouseManagement', 'خطأ في إنشاء كود المخزن:', error)
      messageApi.error('فشل في إنشاء كود المخزن')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      if (window.electronAPI) {
        // التحقق من تفرد الكود قبل الحفّ (إذا كان موجوداً)
        if (values.code) {
          const codeCheckResponse = await window.electronAPI.checkCodeUniqueness('warehouses', 'code', values.code, editingWarehouse?.id)
          if (codeCheckResponse.success && !codeCheckResponse.data.isUnique) {
            messageApi.error('هذا الكود مستخدم بالفعل، يرجى اختيار كود آخر')
            form.setFields([{
              name: 'code',
              errors: ['هذا الكود مستخدم بالفعل']
            }])
            return
          }
        }

        let response: ApiResponse

        if (editingWarehouse) {
          response = await window.electronAPI.updateWarehouse(editingWarehouse.id, values)
        } else {
          response = await window.electronAPI.createWarehouse(values)
        }

        if (response.success) {
          messageApi.success(editingWarehouse ? 'تم تحديث المخزن بنجاح' : 'تم إضافة المخزن بنجاح')
          setModalVisible(false)
          form.resetFields()
          setEditingWarehouse(null)
          loadWarehouses()
        } else {
          messageApi.error(response.message || 'فشل في حفّ المخزن')
        }
      } else {
        messageApi.error('لا يمكن الوصول إلى قاعدة البيانات')
      }
    } catch (error) {
      Logger.error('WarehouseManagement', 'خطأ في حفّ المخزن:', error)
      messageApi.error('فشل في حفّ المخزن')
    }
  }

  const columns = [
    {
      title: 'اسم المخزن',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Warehouse) => (
        <Space>
          <HomeOutlined />
          <span style={{ fontWeight: 'bold' }}>{text}</span>
          {!record.is_active && <Tag color="red">معطل</Tag>}
        </Space>
      )
    },
    {
      title: 'الموقع',
      dataIndex: 'location',
      key: 'location',
      render: (text: string) => text || '-'
    },
    {
      title: 'المسؤول',
      dataIndex: 'manager_name',
      key: 'manager_name',
      render: (text: string) => (
        <Space>
          <UserOutlined />
          {text || 'غير محدد'}
        </Space>
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'} icon={isActive ? <CheckCircleOutlined /> : <StopOutlined />}>
          {isActive ? 'نشط' : 'معطل'}
        </Tag>
      )
    },
    {
      title: 'تاريخ الإنشاء (ميلادي)',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 140,
      render: (date: string) => DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE)
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: Warehouse) => (
        <Space>
          <ActionButton
            type="default"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewContents(record)}
          >
            عرض المحتويات
          </ActionButton>
          <ActionButton
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            تعديل
          </ActionButton>
          <Popconfirm
            title="هل أنت متأكد من حذف هذا المخزن؟"
            onConfirm={() => handleDelete(record.id)}
            okText="نعم"
            cancelText="لا"
          >
            <ActionButton
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              حذف
            </ActionButton>
          </Popconfirm>
        </Space>
      )
    }
  ]

  const activeWarehouses = warehouses.filter(w => w.is_active).length
  const totalWarehouses = warehouses.length

  Logger.info('WarehouseManagement', '🎨 عرض WarehouseManagement:', {
    totalWarehouses,
    activeWarehouses,
    loading,
    modalVisible,
    warehousesData: warehouses.slice(0, 3) // أول 3 مخازن للفحص
  })

  return (
    <div>
      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <StyledCard>
            <Statistic
              title="إجمالي المخازن"
              value={totalWarehouses}
              prefix={<HomeOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </StyledCard>
        </Col>
        <Col span={8}>
          <StyledCard>
            <Statistic
              title="المخازن النشطة"
              value={activeWarehouses}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </StyledCard>
        </Col>
        <Col span={8}>
          <StyledCard>
            <Statistic
              title="المخازن المعطلة"
              value={totalWarehouses - activeWarehouses}
              prefix={<StopOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </StyledCard>
        </Col>
      </Row>

      {/* جدول المخازن */}
      <StyledCard
        title={
          <Space>
            <HomeOutlined />
            <Title level={4} style={{ margin: 0 }}>إدارة المخازن</Title>
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            إضافة مخزن جديد
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={warehouses}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} مخزن`
          }}
        />
      </StyledCard>

      {/* نموذج إضافة/تعديل المخزن */}
      <Modal
        title={editingWarehouse ? 'تعديل المخزن' : 'إضافة مخزن جديد'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ is_active: true }}
        >
          <Form.Item
            name="code"
            label="كود المخزن"
            rules={[{ required: true, message: 'يرجى إدخال كود المخزن' }]}
          >
            <Input
              placeholder="أدخل كود المخزن"
              addonAfter={
                <Button
                  size="small"
                  onClick={generateWarehouseCode}
                  icon={<BarcodeOutlined />}
                >
                  إنشاء تلقائي
                </Button>
              }
            />
          </Form.Item>

          <Form.Item
            name="name"
            label="اسم المخزن"
            rules={[{ required: true, message: 'يرجى إدخال اسم المخزن' }]}
          >
            <Input placeholder="أدخل اسم المخزن" />
          </Form.Item>

          <Form.Item
            name="location"
            label="الموقع"
          >
            <Input placeholder="أدخل موقع المخزن (اختياري)" />
          </Form.Item>

          <Form.Item
            name="manager_id"
            label="المسؤول"
          >
            <Select placeholder="اختر المسؤول عن المخزن" allowClear>
              {users.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.full_name} ({user.username})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="is_active"
            label="الحالة"
            valuePropName="checked"
          >
            <Switch checkedChildren="نشط" unCheckedChildren="معطل" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {editingWarehouse ? 'تحديث' : 'إضافة'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* مودال عرض محتويات المخزن */}
      {selectedWarehouse && (
        <WarehouseContents
          warehouse={selectedWarehouse}
          visible={contentsModalVisible}
          onClose={() => {
            setContentsModalVisible(false)
            setSelectedWarehouse(null)
          }}
        />
      )}
    </div>
  )
}

const WarehouseManagementWithApp: React.FC<WarehouseManagementProps> = (props) => {
  return (
    <App>
      <WarehouseManagement {...props} />
    </App>
  )
}

export default WarehouseManagementWithApp
