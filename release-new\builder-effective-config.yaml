directories:
  output: release-new
  buildResources: build
appId: com.faresnawaf.zetia
productName: ZET.IA
files:
  - filter:
      - dist/**/*
      - node_modules/**/*
      - '!node_modules/.cache'
      - '!node_modules/.vite'
      - '!node_modules/**/test/**'
      - '!node_modules/**/tests/**'
      - '!node_modules/**/*.md'
      - '!node_modules/**/.git*'
      - '!node_modules/**/docs/**'
      - '!node_modules/**/examples/**'
      - '!node_modules/**/*.map'
      - '!node_modules/**/LICENSE*'
      - '!node_modules/**/CHANGELOG*'
      - '!node_modules/**/README*'
      - '!node_modules/**/.bin/**'
      - '!node_modules/**/man/**'
      - '!node_modules/**/*.d.ts'
asar: false
nodeGypRebuild: false
buildDependenciesFromSource: false
npmRebuild: false
win:
  target: nsis
  requestedExecutionLevel: asInvoker
  signAndEditExecutable: false
  verifyUpdateCodeSignature: false
  artifactName: ${productName}-Setup-${version}-${arch}.${ext}
  extraResources:
    - from: build/
      to: build/
      filter:
        - '**/*'
    - from: resources/sql-backup/
      to: sql-backup/
      filter:
        - '**/*'
    - from: node_modules/sql.js/dist/
      to: sql.js/dist/
      filter:
        - '**/*'
mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
    - target: zip
      arch:
        - x64
        - arm64
  icon: assets/icon.png
  hardenedRuntime: false
  gatekeeperAssess: false
  artifactName: ${productName}-${version}-${arch}.${ext}
linux:
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
    - target: rpm
      arch:
        - x64
  icon: assets/icon.png
  artifactName: ${productName}-${version}-${arch}.${ext}
  category: Office
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  allowElevation: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  deleteAppDataOnUninstall: false
  runAfterFinish: true
  menuCategory: ZET.IA
  perMachine: false
  differentialPackage: false
  warningsAsErrors: false
forceCodeSigning: false
electronDownload:
  cache: ./cache
compression: maximum
includeSubNodeModules: true
extraMetadata:
  main: dist/main/main/main.js
afterPack: ./scripts/after-pack.js
publish: null
electronVersion: 28.3.3
