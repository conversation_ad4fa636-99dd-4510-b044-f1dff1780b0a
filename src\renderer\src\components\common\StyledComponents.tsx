import styled from 'styled-components'
import { Card, Button, Input, Form } from 'antd'
import { designSystem, animations } from '../../styles/designSystem'

// الحاوي الرئيسي مع الخلفية المتحركة
export const AnimatedBackground = styled.div`
  ${animations.fadeIn}
  ${animations.gradientShift}
  
  background: ${designSystem.colors.gradients.background};
  background-size: 400% 400%;
  animation: gradientShift 4s ease infinite, fadeIn 0.8s ease-out;
  min-height: 100vh;
  direction: rtl;
`

// البطاقة المحسنة مع تأثير الزجاج
export const GlassCard = styled(Card)<{ $animated?: boolean }>`
  ${animations.slideUp}
  
  box-shadow: ${designSystem.shadows.card};
  border-radius: ${designSystem.borderRadius['2xl']};
  backdrop-filter: blur(20px);
  background: ${designSystem.colors.background.glass};
  border: 2px solid ${designSystem.colors.border.glass};
  position: relative;
  z-index: 1;
  animation: ${props => props.$animated ? 'slideUp 1s cubic-bezier(0.16, 1, 0.3, 1)' : 'none'};
  overflow: hidden;
  transition: ${designSystem.transitions.smooth};

  /* إضافة تأثير الضوء العلوي */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: ${designSystem.colors.gradients.shimmer};
    background-size: 200% 100%;
    animation: shimmer 2s linear infinite;
  }

  .ant-card-body {
    padding: ${designSystem.spacing['3xl']};
    background: transparent;
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: ${designSystem.shadows.xl};
  }

  ${animations.shimmer}
`

// الزر المحسن مع التأثيرات
export const EnhancedButton = styled(Button)<{ $variant?: 'primary' | 'secondary' | 'danger' }>`
  height: ${designSystem.dimensions.button.height};
  border-radius: ${designSystem.borderRadius.lg};
  font-weight: ${designSystem.fontWeight.bold};
  font-size: ${designSystem.fontSize.lg};
  transition: ${designSystem.transitions.smooth};
  position: relative;
  overflow: hidden;
  border: none;

  background: ${props => {
    switch (props.$variant) {
      case 'primary':
        return designSystem.colors.gradients.primary
      case 'secondary':
        return designSystem.colors.gradients.secondary
      case 'danger':
        return designSystem.colors.gradients.danger
      default:
        return designSystem.colors.gradients.primary
    }
  }};

  box-shadow: ${props => {
    switch (props.$variant) {
      case 'primary':
        return designSystem.shadows.button
      case 'secondary':
        return designSystem.shadows.md
      case 'danger':
        return designSystem.shadows.md
      default:
        return designSystem.shadows.button
    }
  }};

  &:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: ${designSystem.shadows.lg};
  }

  &:active:not(:disabled) {
    transform: translateY(-1px);
  }

  &:disabled {
    background: ${designSystem.colors.background.secondary} !important;
    color: ${designSystem.colors.text.disabled} !important;
    box-shadow: none !important;
    transform: none !important;
  }

  /* تأثير الموجة عند النقر */
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: width 0.6s, height 0.6s;
    transform: translate(-50%, -50%);
    z-index: 0;
  }

  &:active::before {
    width: 300px;
    height: 300px;
  }

  span {
    position: relative;
    z-index: 1;
  }
`

// حقل الإدخال المحسن
export const EnhancedInput = styled(Input)<{ $size?: 'sm' | 'md' | 'lg' }>`
  height: ${props => {
    switch (props.$size) {
      case 'sm': return designSystem.dimensions.input.heightSm
      case 'lg': return designSystem.dimensions.input.heightLg
      default: return designSystem.dimensions.input.height
    }
  }};
  border-radius: ${designSystem.borderRadius.lg};
  font-size: ${designSystem.fontSize.lg};
  border: 2px solid ${designSystem.colors.border.base};
  box-shadow: ${designSystem.shadows.base};
  transition: ${designSystem.transitions.smooth};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${designSystem.shadows.md};
    border-color: ${designSystem.colors.border.focus};
  }

  &:focus {
    border-color: ${designSystem.colors.border.focus};
    box-shadow: ${designSystem.shadows.focus};
    transform: translateY(-2px);
  }

  &.ant-input-affix-wrapper {
    height: ${props => {
      switch (props.$size) {
        case 'sm': return designSystem.dimensions.input.heightSm
        case 'lg': return designSystem.dimensions.input.heightLg
        default: return designSystem.dimensions.input.height
      }
    }};
    border-radius: ${designSystem.borderRadius.lg};
    border: 2px solid ${designSystem.colors.border.base};
    box-shadow: ${designSystem.shadows.base};
    transition: ${designSystem.transitions.smooth};

    &:hover {
      transform: translateY(-2px);
      box-shadow: ${designSystem.shadows.md};
      border-color: ${designSystem.colors.border.focus};
    }

    &:focus-within {
      border-color: ${designSystem.colors.border.focus};
      box-shadow: ${designSystem.shadows.focus};
      transform: translateY(-2px);
    }
  }
`

// حقل كلمة المرور المحسن
export const EnhancedPasswordInput = styled(Input.Password)<{ $size?: 'sm' | 'md' | 'lg' }>`
  .ant-input-affix-wrapper {
    height: ${props => {
      switch (props.$size) {
        case 'sm': return designSystem.dimensions.input.heightSm
        case 'lg': return designSystem.dimensions.input.heightLg
        default: return designSystem.dimensions.input.height
      }
    }};
    border-radius: ${designSystem.borderRadius.lg};
    font-size: ${designSystem.fontSize.lg};
    border: 2px solid ${designSystem.colors.border.base};
    box-shadow: ${designSystem.shadows.base};
    transition: ${designSystem.transitions.smooth};

    &:hover {
      transform: translateY(-2px);
      box-shadow: ${designSystem.shadows.md};
      border-color: ${designSystem.colors.border.focus};
    }

    &:focus-within {
      border-color: ${designSystem.colors.border.focus};
      box-shadow: ${designSystem.shadows.focus};
      transform: translateY(-2px);
    }
  }
`

// النموذج المحسن
export const EnhancedForm = styled(Form)`
  .ant-form-item {
    margin-bottom: ${designSystem.spacing.lg};
  }

  .ant-form-item-label > label {
    font-weight: ${designSystem.fontWeight.semibold};
    color: ${designSystem.colors.text.primary};
    font-size: ${designSystem.fontSize.base};
  }

  .ant-form-item-explain-error {
    font-size: ${designSystem.fontSize.sm};
    margin-top: ${designSystem.spacing.sm};
  }
`

// الحاوي المرن
export const FlexContainer = styled.div<{
  $direction?: 'row' | 'column'
  $align?: 'start' | 'center' | 'end' | 'stretch'
  $justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly'
  $gap?: string
  $wrap?: boolean
}>`
  display: flex;
  flex-direction: ${props => props.$direction || 'row'};
  align-items: ${props => {
    switch (props.$align) {
      case 'start': return 'flex-start'
      case 'center': return 'center'
      case 'end': return 'flex-end'
      case 'stretch': return 'stretch'
      default: return 'flex-start'
    }
  }};
  justify-content: ${props => {
    switch (props.$justify) {
      case 'start': return 'flex-start'
      case 'center': return 'center'
      case 'end': return 'flex-end'
      case 'between': return 'space-between'
      case 'around': return 'space-around'
      case 'evenly': return 'space-evenly'
      default: return 'flex-start'
    }
  }};
  gap: ${props => props.$gap || designSystem.spacing.base};
  flex-wrap: ${props => props.$wrap ? 'wrap' : 'nowrap'};
`

// النص المتدرج
export const GradientText = styled.span<{ $gradient?: string }>`
  background: ${props => props.$gradient || designSystem.colors.gradients.text};
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: ${designSystem.fontWeight.bold};
`
