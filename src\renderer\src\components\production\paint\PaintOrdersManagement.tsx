import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  message,
  Space,
  Tag,
  Row,
  Col,
  Statistic,
  Divider,
  Tooltip,
  Popover,
  Upload,
  Image,
  Calendar,
  Badge,
  Alert,
  notification
} from 'antd'
import { SafeLogger as Logger } from '../../../utils/logger'
import {
  ArrowLeftOutlined,
  PlusOutlined,
  DeleteOutlined,
  FileTextOutlined,
  CalculatorOutlined,
  QuestionCircleOutlined,
  InfoCircleOutlined,
  PrinterOutlined,
  CameraOutlined,
  CalendarOutlined,
  BellOutlined,
  ClockCircleOutlined,
  UploadOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { DateUtils } from '../../../utils/dateUtils'
import UnifiedPrintButton from '../../common/UnifiedPrintButton'
import { QuickInputService, QuickInputHelper, RecentData } from '../../common/QuickInputService'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

const StyledCard = styled(Card)`
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .ant-card-head {
    background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
    border-radius: 8px 8px 0 0;
    
    .ant-card-head-title {
      color: white;
      font-weight: 600;
    }
  }
`

const StatsCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .ant-statistic-title {
    color: #666;
    font-size: 14px;
  }
  
  .ant-statistic-content {
    color: #1890ff;
  }
`

const ItemCard = styled(Card)`
  margin-bottom: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  
  .ant-card-body {
    padding: 16px;
  }
`

interface PaintOrder {
  id: number
  order_number: string
  customer_id: number
  customer_name: string
  order_date: string
  expected_completion_date: string
  actual_completion_date: string
  status: string
  total_area: number
  total_amount: number
  notes: string
  created_by: number
  created_by_name: string
  created_at: string
  // حقول إضافية للطباعة
  item_description?: string
  paint_type_name?: string
  length?: number
  width?: number
  quantity?: number
  price_per_sqm?: number
}

interface PaintType {
  id: number
  code: string
  name: string
  price_per_sqm: number
  color: string
  finish_type: string
}

interface Customer {
  id: number
  code: string
  name: string
}

interface OrderItem {
  item_description: string
  paint_type_id: number
  length: number // بالسنتيمتر
  width: number // بالسنتيمتر
  quantity: number
  area: number // بالمتر المربع
  area_cm: number // بالسنتيمتر المربع للعرض
  unit_price: number
  total_price: number
  notes: string
  color?: string // إضافة خاصية اللون
  images?: string[] // مصفوفة روابط الصور
}

interface OrderNotification {
  id: string
  order_id: number
  order_number: string
  customer_name: string
  type: 'delivery' | 'followup' | 'reminder'
  message: string
  date: string
  time: string
  status: 'pending' | 'sent' | 'dismissed'
  created_at: string
}

interface PaintOrdersManagementProps {
  onBack: () => void
}

const PaintOrdersManagement: React.FC<PaintOrdersManagementProps> = ({ onBack }) => {
  const [orders, setOrders] = useState<PaintOrder[]>([])
  const [paintTypes, setPaintTypes] = useState<PaintType[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingOrder, setEditingOrder] = useState<PaintOrder | null>(null)
  const [form] = Form.useForm()
  const [orderItems, setOrderItems] = useState<OrderItem[]>([])
  const [recentInputs, setRecentInputs] = useState<RecentData[]>([])
  const [showQuickInput, setShowQuickInput] = useState(false)

  // حالات جديدة للتنبيهات والتقويم والصور
  const [notifications, setNotifications] = useState<OrderNotification[]>([])
  const [calendarVisible, setCalendarVisible] = useState(false)
  const [notificationModalVisible, setNotificationModalVisible] = useState(false)
  const [selectedOrderForNotification, setSelectedOrderForNotification] = useState<PaintOrder | null>(null)
  const [notificationForm] = Form.useForm()
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false)
  const [previewImage, setPreviewImage] = useState('')
  const [_fileList, _setFileList] = useState<any[]>([])
  const [_currentItemIndex, _setCurrentItemIndex] = useState<number>(-1)
  const [_stats, _setStats] = useState({
    totalOrders: 0,
    totalAmount: 0,
    totalArea: 0
  })

  useEffect(() => {
    loadOrders()
    loadPaintTypes()
    loadCustomers()
    loadRecentInputs()
    loadNotifications()
    checkUpcomingDeliveries()
  }, [])

  // تحميل التنبيهات من localStorage
  const loadNotifications = () => {
    try {
      const savedNotifications = localStorage.getItem('paint_notifications')
      if (savedNotifications) {
        setNotifications(JSON.parse(savedNotifications))
      }
    } catch (error) {
      Logger.error('PaintOrdersManagement', 'خطأ في تحميل التنبيهات:', error)
    }
  }

  // حفظ التنبيهات في localStorage
  const saveNotifications = (newNotifications: OrderNotification[]) => {
    try {
      localStorage.setItem('paint_notifications', JSON.stringify(newNotifications))
      setNotifications(newNotifications)
    } catch (error) {
      Logger.error('PaintOrdersManagement', 'خطأ في حفظ التنبيهات:', error)
    }
  }

  // فحص المواعيد القريبة وإرسال تنبيهات
  const checkUpcomingDeliveries = () => {
    const today = dayjs()
    const _tomorrow = today.add(1, 'day')

    orders.forEach(order => {
      if (order.expected_completion_date) {
        const deliveryDate = dayjs(order.expected_completion_date)
        const daysDiff = deliveryDate.diff(today, 'day')

        // تنبيه قبل يوم من التسليم
        if (daysDiff === 1) {
          const existingNotification = notifications.find(
            n => n.order_id === order.id && n.type === 'delivery'
          )

          if (!existingNotification) {
            createNotification({
              order_id: order.id,
              order_number: order.order_number,
              customer_name: order.customer_name,
              type: 'delivery',
              message: `موعد تسليم أمر الدهان ${order.order_number} للعميل ${order.customer_name} غداً`,
              date: deliveryDate.format('YYYY-MM-DD'),
              time: '09:00'
            })
          }
        }

        // تنبيه للأوامر المتأخرة
        if (daysDiff < 0 && order.status !== 'completed') {
          const existingNotification = notifications.find(
            n => n.order_id === order.id && n.type === 'reminder'
          )

          if (!existingNotification) {
            createNotification({
              order_id: order.id,
              order_number: order.order_number,
              customer_name: order.customer_name,
              type: 'reminder',
              message: `أمر الدهان ${order.order_number} متأخر عن الموعد المحدد`,
              date: today.format('YYYY-MM-DD'),
              time: '10:00'
            })
          }
        }
      }
    })
  }

  const loadRecentInputs = () => {
    const recent = QuickInputService.getRecentData('paint_order')
    setRecentInputs(recent)
  }

  // إنشاء تنبيه جديد
  const createNotification = (notificationData: Omit<OrderNotification, 'id' | 'status' | 'created_at'>) => {
    const newNotification: OrderNotification = {
      ...notificationData,
      id: Date.now().toString(),
      status: 'pending',
      created_at: dayjs().format('YYYY-MM-DD HH:mm:ss')
    }

    const updatedNotifications = [...notifications, newNotification]
    saveNotifications(updatedNotifications)

    // عرض التنبيه للمستخدم
    notification.info({
      message: 'تنبيه جديد',
      description: newNotification.message,
      placement: 'topRight',
      duration: 5
    })
  }

  // إضافة تنبيه مخصص
  const showNotificationModal = (order: PaintOrder) => {
    setSelectedOrderForNotification(order)
    notificationForm.setFieldsValue({
      type: 'followup',
      date: dayjs().add(1, 'day'),
      time: dayjs().format('HH:mm')
    })
    setNotificationModalVisible(true)
  }

  // حفظ التنبيه المخصص
  const handleNotificationSubmit = async (values: any) => {
    if (!selectedOrderForNotification) return

    createNotification({
      order_id: selectedOrderForNotification.id,
      order_number: selectedOrderForNotification.order_number,
      customer_name: selectedOrderForNotification.customer_name,
      type: values.type,
      message: values.message || `متابعة أمر الدهان ${selectedOrderForNotification.order_number}`,
      date: values.date.format('YYYY-MM-DD'),
      time: values.time
    })

    setNotificationModalVisible(false)
    notificationForm.resetFields()
    setSelectedOrderForNotification(null)
    message.success('تم إضافة التنبيه بنجاح')
  }

  // رفض التنبيه
  const _dismissNotification = (notificationId: string) => {
    const updatedNotifications = notifications.map(n =>
      n.id === notificationId ? { ...n, status: 'dismissed' as const } : n
    )
    saveNotifications(updatedNotifications)
  }

  // معالجة رفع الصور
  const handleImageUpload = (info: any, itemIndex: number) => {
    const { fileList: newFileList } = info

    // تحديث قائمة الملفات للعنصر المحدد
    const updatedItems = [...orderItems]
    if (updatedItems[itemIndex]) {
      updatedItems[itemIndex].images = newFileList.map((file: any) => {
        if (file.response) {
          return file.response.url
        }
        return file.url || file.thumbUrl
      }).filter(Boolean)
      setOrderItems(updatedItems)
    }
  }

  // معاينة الصورة
  const handlePreview = (file: any) => {
    setPreviewImage(file.url || file.thumbUrl)
    setImagePreviewVisible(true)
  }

  // إزالة الصورة
  const handleRemoveImage = (file: any, itemIndex: number) => {
    const updatedItems = [...orderItems]
    if (updatedItems[itemIndex] && updatedItems[itemIndex].images) {
      updatedItems[itemIndex].images = (updatedItems[itemIndex].images || []).filter(
        img => img !== (file.url || file.thumbUrl)
      )
      setOrderItems(updatedItems)
    }
  }

  // الحصول على بيانات التقويم
  const getCalendarData = (date: dayjs.Dayjs) => {
    const dateStr = date.format('YYYY-MM-DD')
    const dayOrders = orders.filter(order =>
      order.expected_completion_date === dateStr ||
      order.order_date === dateStr
    )

    const dayNotifications = notifications.filter(notification =>
      notification.date === dateStr && notification.status === 'pending'
    )

    return {
      orders: dayOrders,
      notifications: dayNotifications,
      hasEvents: dayOrders.length > 0 || dayNotifications.length > 0
    }
  }

  // عرض محتوى التقويم لكل يوم
  const dateCellRender = (date: dayjs.Dayjs) => {
    const { orders: dayOrders, notifications: dayNotifications, hasEvents } = getCalendarData(date)

    if (!hasEvents) return null

    return (
      <div style={{ fontSize: '12px' }}>
        {dayOrders.map(order => (
          <div key={order.id} style={{ marginBottom: '2px' }}>
            <Badge
              status={order.status === 'completed' ? 'success' : 'processing'}
              text={`${order.order_number.slice(-4)}`}
            />
          </div>
        ))}
        {dayNotifications.map(notification => (
          <div key={notification.id} style={{ marginBottom: '2px' }}>
            <Badge
              status="warning"
              text={notification.type === 'delivery' ? '🚚' : '⏰'}
            />
          </div>
        ))}
      </div>
    )
  }

  // عرض محتوى الشهر
  const monthCellRender = (date: dayjs.Dayjs) => {
    const monthOrders = orders.filter(order => {
      const orderDate = dayjs(order.expected_completion_date)
      return orderDate.month() === date.month() && orderDate.year() === date.year()
    })

    if (monthOrders.length === 0) return null

    return (
      <div style={{ fontSize: '12px', textAlign: 'center' }}>
        <Badge count={monthOrders.length} style={{ backgroundColor: '#52c41a' }} />
      </div>
    )
  }

  const loadOrders = async () => {
    setLoading(true)
    try {
      Logger.info('PaintOrdersManagement', '🔄 جاري تحميل أوامر الدهان...')

      if (!window.electronAPI) {
        Logger.error('PaintOrdersManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('PaintOrdersManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية لأوامر الدهان
        const mockOrders = [
          {
            id: 1,
            order_number: 'PAINT001',
            customer_id: 1,
            customer_name: 'أحمد محمد علي',
            paint_type_id: 1,
            paint_type_name: 'دهان أكريليك',
            length: 3.0,
            width: 2.5,
            quantity: 2,
            total_area: 15.0,
            price_per_sqm: 25.0,
            total_amount: 375.0,
            status: 'pending',
            order_date: '2024-06-20',
            expected_completion_date: '2024-06-25',
            notes: 'دهان غرفة نوم بلون أبيض',
            created_by: 1,
            created_by_name: 'مدير الدهان'
          },
          {
            id: 2,
            order_number: 'PAINT002',
            customer_id: 2,
            customer_name: 'فاطمة أحمد',
            paint_type_id: 2,
            paint_type_name: 'دهان زيتي',
            length: 4.0,
            width: 3.0,
            quantity: 1,
            total_area: 12.0,
            price_per_sqm: 35.0,
            total_amount: 420.0,
            status: 'in_progress',
            order_date: '2024-06-18',
            expected_completion_date: '2024-06-23',
            notes: 'دهان خزانة خشبية بلون بني',
            created_by: 1,
            created_by_name: 'مدير الدهان'
          }
        ]

        setOrders(mockOrders as any)
        Logger.info('PaintOrdersManagement', '✅ تم تحميل ' + mockOrders.length + ' أمر دهان وهمي')
      } else {
        const response = await window.electronAPI.getPaintOrders()
        if (response.success) {
          setOrders(response.data)
          Logger.info('PaintOrdersManagement', '✅ تم تحميل أوامر الدهان من قاعدة البيانات')
        } else {
          message.error('فشل في تحميل أوامر الدهان')
        }
      }
    } catch (error) {
      Logger.error('PaintOrdersManagement', 'خطأ في تحميل أوامر الدهان:', error)
      message.error('حدث خطأ في تحميل أوامر الدهان')
    } finally {
      setLoading(false)
    }
  }

  const loadPaintTypes = async () => {
    try {
      Logger.info('PaintOrdersManagement', '🔄 جاري تحميل أنواع الدهانات...')

      if (!window.electronAPI) {
        Logger.error('PaintOrdersManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('PaintOrdersManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية لأنواع الدهانات
        const mockPaintTypes = [
          { id: 1, name: 'دهان أكريليك', code: 'PAINT001', price_per_sqm: 25.0, is_active: true },
          { id: 2, name: 'دهان زيتي', code: 'PAINT002', price_per_sqm: 35.0, is_active: true },
          { id: 3, name: 'دهان لامع', code: 'PAINT003', price_per_sqm: 30.0, is_active: true }
        ]

        setPaintTypes(mockPaintTypes as any)
        Logger.info('PaintOrdersManagement', '✅ تم تحميل ' + mockPaintTypes.length + ' نوع دهان وهمي')
      } else {
        const response = await window.electronAPI.getPaintTypes()
        if (response.success) {
          setPaintTypes(response.data)
          Logger.info('PaintOrdersManagement', '✅ تم تحميل أنواع الدهانات من قاعدة البيانات')
        }
      }
    } catch (error) {
      Logger.error('PaintOrdersManagement', 'خطأ في تحميل أنواع الدهانات:', error)
    }
  }

  const loadCustomers = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getCustomers()
        if (response.success) {
          setCustomers(response.data)
        }
      }
    } catch (error) {
      Logger.error('PaintOrdersManagement', 'خطأ في تحميل العملاء:', error)
    }
  }

  const generateOrderNumber = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generatePaintOrderNumber()
        if (response.success) {
          form.setFieldsValue({ order_number: response.data.order_number })
        }
      }
    } catch (error) {
      Logger.error('PaintOrdersManagement', 'خطأ في إنشاء رقم الأمر:', error)
    }
  }

  const addOrderItem = () => {
    const newItem: OrderItem = {
      item_description: '',
      paint_type_id: 0,
      length: 0, // بالسنتيمتر
      width: 0, // بالسنتيمتر
      quantity: 1,
      area: 0, // بالمتر المربع
      area_cm: 0, // بالسنتيمتر المربع
      unit_price: 0,
      total_price: 0,
      notes: '',
      images: [] // إضافة مصفوفة فارغة للصور
    }
    setOrderItems([...orderItems, newItem])
  }

  const removeOrderItem = (index: number) => {
    const newItems = orderItems.filter((_, i) => i !== index)
    setOrderItems(newItems)
  }

  const updateOrderItem = (index: number, field: string, value: any) => {
    const newItems = [...orderItems]
    newItems[index] = { ...newItems[index], [field]: value }

    // حساب المساحة والسعر تلقائياً
    if (['length', 'width', 'quantity', 'paint_type_id'].includes(field)) {
      const item = newItems[index]
      // تحويل من سنتيمتر إلى متر مربع: (الطول بالسم × العرض بالسم) ÷ 10000 × العدد
      const areaInSqM = (item.length * item.width / 10000) * item.quantity
      const paintType = paintTypes.find(p => p.id === item.paint_type_id)
      const unitPrice = paintType ? paintType.price_per_sqm : 0
      const totalPrice = areaInSqM * unitPrice

      newItems[index] = {
        ...newItems[index],
        area: areaInSqM, // المساحة بالمتر المربع
        area_cm: item.length * item.width * item.quantity, // المساحة بالسنتيمتر المربع للعرض
        unit_price: unitPrice,
        total_price: totalPrice
      }
    }

    setOrderItems(newItems)
  }

  const handleSubmit = async (values: any) => {
    // التحقق من صحة البيانات الأساسية
    if (!values.customer_id) {
      message.error('يجب اختيار العميل')
      return
    }

    if (orderItems.length === 0) {
      message.error('يجب إضافة صنف واحد على الأقل')
      return
    }

    // التحقق من صحة بيانات الأصناف
    for (const item of orderItems) {
      if (!item.paint_type_id) {
        message.error('يجب اختيار نوع الدهان لجميع الأصناف')
        return
      }

      if (!item.length || item.length <= 0) {
        message.error('يجب أن يكون الطول أكبر من الصفر')
        return
      }

      if (!item.width || item.width <= 0) {
        message.error('يجب أن يكون العرض أكبر من الصفر')
        return
      }

      if (!item.quantity || item.quantity <= 0) {
        message.error('يجب أن تكون الكمية أكبر من الصفر')
        return
      }

      if (item.length > 1000) {
        message.error('الطول كبير جداً، يرجى التحقق من القيمة')
        return
      }

      if (item.width > 1000) {
        message.error('العرض كبير جداً، يرجى التحقق من القيمة')
        return
      }

      if (item.quantity > 10000) {
        message.error('الكمية كبيرة جداً، يرجى التحقق من القيمة')
        return
      }

      if (!item.color || item.color.trim().length === 0) {
        message.error('يجب تحديد اللون لجميع الأصناف')
        return
      }
    }

    // التحقق من صحة التواريخ
    if (values.order_date && values.expected_completion_date) {
      if (values.order_date.isAfter(values.expected_completion_date)) {
        message.error('تاريخ الأمر يجب أن يكون قبل تاريخ الإنجاز المتوقع')
        return
      }
    }

    if (values.expected_completion_date && values.expected_completion_date.isBefore(dayjs())) {
      message.error('تاريخ الإنجاز المتوقع لا يمكن أن يكون في الماضي')
      return
    }

    try {
      const orderData = {
        ...values,
        customer_id: parseInt(String(values.customer_id)), // تحويل معرف العميل إلى رقم صحيح
        order_date: values.order_date.format('YYYY-MM-DD'),
        expected_completion_date: values.expected_completion_date?.format('YYYY-MM-DD'),
        items: orderItems,
        created_by: 1 // TODO: استخدام معرف المستخدم الحالي
      }

      if (window.electronAPI) {
        const response = await window.electronAPI.createPaintOrder(orderData)
        if (response.success) {
          message.success('تم إنشاء أمر الدهان بنجاح')

          // حفّ البيانات للإدخال السريع
          if (!editingOrder) {
            const quickInputData = {
              ...values,
              orderItems: orderItems,
              customer_name: customers.find(c => c.id === values.customer_id)?.name
            }

            const label = (quickInputData.customer_name || 'عميل') + ' - دهان ' + orderItems.length + ' قطعة'
            QuickInputService.saveRecentInput('paint_order', quickInputData, label)
            loadRecentInputs()
          }

          setModalVisible(false)
          setEditingOrder(null)
          form.resetFields()
          setOrderItems([])
          loadOrders()
        } else {
          message.error('فشل في إنشاء أمر الدهان')
        }
      }
    } catch (error) {
      Logger.error('PaintOrdersManagement', 'خطأ في إنشاء أمر الدهان:', error)
      message.error('حدث خطأ في إنشاء أمر الدهان')
    }
  }

  const updateOrderStatus = async (orderId: number, status: string) => {
    try {
      if (!window.electronAPI) {
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن تحديث حالة الأوامر')
        return
      }

      const response = await window.electronAPI.updatePaintOrderStatus(orderId, status)
      if (response.success) {
        message.success('تم تحديث حالة الأمر بنجاح')
        loadOrders()
      } else {
        message.error(response.message || 'فشل في تحديث حالة الأمر')
      }
    } catch (error) {
      Logger.error('PaintOrdersManagement', 'خطأ في تحديث حالة الأمر:', error)
      message.error('حدث خطأ في تحديث حالة الأمر')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'in_progress': return 'blue'
      case 'completed': return 'green'
      case 'invoiced': return 'purple'
      case 'cancelled': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتّار'
      case 'in_progress': return 'قيد التنفيذ'
      case 'completed': return 'مكتمل'
      case 'invoiced': return 'مفوتر'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  const _printPaintOrder = (order: PaintOrder) => {
    try {
      // إنشاء بيانات الطباعة
      const _printData = {
        title: `أمر دهان رقم ${order.order_number}`,
        subtitle: `العميل: ${order.customer_name}`,
        data: [order],
        summary: {
          totalArea: order.total_area || 0,
          totalCost: order.total_amount || 0,
          status: order.status,
          paintType: order.paint_type_name
        },
        metadata: {
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام الدهان',
          orderNumber: order.order_number
        }
      }

      Logger.info('PaintOrdersManagement', '🖨️ بدء طباعة أمر الدهان:', order.order_number)
      message.success('تم إرسال أمر الدهان للطباعة بنجاح')
    } catch (error) {
      Logger.error('PaintOrdersManagement', 'خطأ في طباعة أمر الدهان:', error)
      message.error('فشل في طباعة أمر الدهان')
    }
  }

  const handleQuickInputPaint = (recentData: RecentData) => {
    QuickInputHelper.applyDataToForm(form, recentData.data)

    // تطبيق بيانات الأصناف إذا كانت متوفرة
    if (recentData.data.orderItems && Array.isArray(recentData.data.orderItems)) {
      setOrderItems(recentData.data.orderItems)
    }

    // تحديث التواريخ للوقت الحالي
    form.setFieldsValue({
      order_date: dayjs(),
      expected_completion_date: recentData.data.expected_completion_date ?
        dayjs().add(3, 'days') : undefined
    })

    message.success('تم تطبيق البيانات: ' + recentData.label)
  }

  const handleSaveAsPaintPreference = () => {
    const currentValues = form.getFieldsValue()
    const preferences = {
      customer_id: currentValues.customer_id,
      paint_type_preferences: orderItems.map(item => ({
        paint_type_id: item.paint_type_id,
        item_description: item.item_description
      }))
    }

    QuickInputService.saveUserPreferences('paint_order', preferences)
    message.success('تم حفّ الإعدادات المفضلة للدهان')
  }

  const columns = [
    {
      title: 'رقم الأمر',
      dataIndex: 'order_number',
      key: 'order_number',
      width: 120,
    },
    {
      title: 'العميل',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 200,
    },
    {
      title: 'تاريخ الأمر (ميلادي)',
      dataIndex: 'order_date',
      key: 'order_date',
      width: 130,
      render: (date: string) => DateUtils.formatDate(date),
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: 'المساحة الإجمالية (م²)',
      dataIndex: 'total_area',
      key: 'total_area',
      width: 150,
      render: (area: number) => area.toFixed(2) + ' م²',
    },
    {
      title: 'المبلغ الإجمالي (₪)',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 150,
      render: (amount: number) => '₪' + amount.toFixed(2),
    },
    {
      title: 'تاريخ الإنشاء (ميلادي)',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => DateUtils.formatDate(date),
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 350,
      render: (_: any, record: PaintOrder) => (
        <Space>
          <Select
            size="small"
            value={record.status}
            onChange={(status) => updateOrderStatus(record.id, status)}
            style={{ width: 120 }}
          >
            <Option value="pending">في الانتّار</Option>
            <Option value="in_progress">قيد التنفيذ</Option>
            <Option value="completed">مكتمل</Option>
            <Option value="cancelled">ملغي</Option>
          </Select>
          <Tooltip title="إضافة تنبيه مخصص">
            <Button
              size="small"
              icon={<BellOutlined />}
              onClick={() => showNotificationModal(record)}
              style={{ borderRadius: '4px' }}
            >
              تنبيه
            </Button>
          </Tooltip>
          <UnifiedPrintButton
            data={{
              title: `أمر دهان رقم ${record.order_number}`,
              subtitle: `العميل: ${record.customer_name}`,
              data: [record],
              summary: {
                totalArea: record.total_area || 0,
                totalCost: record.total_amount || 0,
                status: record.status,
                paintType: record.paint_type_name
              },
              metadata: {
                generatedAt: new Date().toISOString(),
                generatedBy: 'نظام الدهان',
                orderNumber: record.order_number
              }
            }}
            type="order"
            subType="work"
            title="طباعة أمر الدهان"
            buttonText="طباعة"
            size="small"
            buttonType="default"
            showDropdown={true}
            showExportOptions={true}
            icon={<PrinterOutlined />}
          />
        </Space>
      ),
    },
  ]

  const orderStats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'pending').length,
    inProgress: orders.filter(o => o.status === 'in_progress').length,
    completed: orders.filter(o => o.status === 'completed').length,
    totalAmount: orders.reduce((sum, o) => sum + o.total_amount, 0),
    totalArea: orders.reduce((sum, o) => sum + o.total_area, 0),
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h1 style={{ margin: 0, color: '#1890ff', fontSize: '28px' }}>
            <FileTextOutlined style={{ marginLeft: '12px' }} />
            إدارة أوامر الدهان
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '16px' }}>
            إنشاء وإدارة أوامر الدهان مع حساب التكلفة التلقائي
          </p>
        </div>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
          size="large"
          style={{ borderRadius: '8px' }}
        >
          العودة
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="إجمالي الأوامر"
              value={orderStats.total}
              prefix={<FileTextOutlined />}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="في الانتّار"
              value={orderStats.pending}
              valueStyle={{ color: '#faad14' }}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="قيد التنفيذ"
              value={orderStats.inProgress}
              valueStyle={{ color: '#1890ff' }}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="مكتملة"
              value={orderStats.completed}
              valueStyle={{ color: '#52c41a' }}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="إجمالي المبلغ"
              value={orderStats.totalAmount}
              precision={2}
              suffix="₪"
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="إجمالي المساحة"
              value={orderStats.totalArea}
              precision={2}
              suffix="م²"
            />
          </StatsCard>
        </Col>
      </Row>

      <StyledCard
        title="قائمة أوامر الدهان"
        extra={
          <Space>
            <Button
              icon={<CalendarOutlined />}
              onClick={() => setCalendarVisible(true)}
              style={{ borderRadius: '6px' }}
            >
              التقويم
            </Button>
            <Button
              icon={<BellOutlined />}
              onClick={() => {
                const pendingNotifications = notifications.filter(n => n.status === 'pending')
                if (pendingNotifications.length > 0) {
                  Modal.info({
                    title: 'التنبيهات المعلقة',
                    content: (
                      <div>
                        {pendingNotifications.map(notification => (
                          <div key={notification.id} style={{ marginBottom: '8px', padding: '8px', background: '#f5f5f5', borderRadius: '4px' }}>
                            <div><strong>{notification.message}</strong></div>
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              {notification.date} في {notification.time}
                            </div>
                          </div>
                        ))}
                      </div>
                    ),
                    width: 500
                  })
                } else {
                  message.info('لا توجد تنبيهات معلقة')
                }
              }}
              style={{ borderRadius: '6px' }}
            >
              التنبيهات ({notifications.filter(n => n.status === 'pending').length})
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingOrder(null)
                form.resetFields()
                setOrderItems([])
                generateOrderNumber()
                setModalVisible(true)
              }}
              style={{ borderRadius: '6px' }}
            >
              إضافة أمر دهان
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => 'إجمالي ' + total + ' أمر',
          }}
          scroll={{ x: 1200 }}
        />
      </StyledCard>

      <Modal
        title="إضافة أمر دهان جديد"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setEditingOrder(null)
          form.resetFields()
          setOrderItems([])
        }}
        footer={null}
        width={900}
        style={{ top: 20 }}
      >
        {/* قسم الإدخال السريع للدهان */}
        {!editingOrder && recentInputs.length > 0 && (
          <Card
            size="small"
            title="🎨 إدخال سريع للدهان"
            style={{ marginBottom: 16, backgroundColor: '#fff7e6' }}
            extra={
              <Button
                size="small"
                type="link"
                onClick={() => setShowQuickInput(!showQuickInput)}
              >
                {showQuickInput ? 'إخفاء' : 'عرض'}
              </Button>
            }
          >
            {showQuickInput && (
              <Space wrap>
                {recentInputs.slice(0, 3).map((recent) => (
                  <Button
                    key={recent.id}
                    size="small"
                    onClick={() => handleQuickInputPaint(recent)}
                    style={{
                      backgroundColor: '#fff7e6',
                      borderColor: '#ffd591',
                      color: '#d46b08'
                    }}
                  >
                    🎨 {recent.label}
                  </Button>
                ))}
                <Button
                  size="small"
                  type="dashed"
                  onClick={handleSaveAsPaintPreference}
                  style={{ color: '#52c41a' }}
                >
                  💾 حفّ كإعداد مفضل
                </Button>
              </Space>
            )}
          </Card>
        )}

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{ marginTop: '20px' }}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="order_number"
                label="رقم الأمر"
                rules={[{ required: true, message: 'يرجى إدخال رقم الأمر' }]}
              >
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="customer_id"
                label={
                  <Space>
                    العميل
                    <Tooltip title="اختر العميل الذي سيتم دهان أثاثه. يمكنك البحث بالاسم أو الكود">
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: 'يرجى اختيار العميل' }]}
              >
                <Select
                  placeholder="اختر العميل"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false) as boolean
                  }
                >
                  {customers.map(customer => (
                    <Option key={customer.id} value={customer.id}>
                      {customer.name} ({customer.code || ('CUS' + customer.id.toString().padStart(3, '0'))})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="order_date"
                label="تاريخ الأمر"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الأمر' }]}
                initialValue={dayjs()}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="expected_completion_date"
                label={
                  <Space>
                    تاريخ الإنجاز المتوقع
                    <Tooltip title="حدد التاريخ المتوقع لإنجاز أعمال الدهان">
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="اختر تاريخ الإنجاز"
                  disabledDate={(current) => current && current < dayjs().startOf('day')}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="notes"
                label={
                  <Space>
                    ملاحّات
                    <Tooltip title="أضف أي ملاحّات خاصة أو تعليمات للإنتاج">
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
              >
                <TextArea
                  rows={2}
                  placeholder="ملاحّات إضافية أو تعليمات خاصة للإنتاج..."
                  maxLength={500}
                  showCount
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider>أصناف الدهان</Divider>

          {orderItems.map((item, index) => (
            <ItemCard key={index}>
              <Row gutter={8} align="middle">
                <Col span={6}>
                  <Tooltip title="أدخل وصف القطعة المراد دهانها (مثال: خزانة، طاولة، كرسي)">
                    <Input
                      placeholder="وصف القطعة (مثال: خزانة)"
                      value={item.item_description}
                      onChange={(e) => updateOrderItem(index, 'item_description', e.target.value)}
                    />
                  </Tooltip>
                </Col>
                <Col span={5}>
                  <Tooltip title="اختر نوع الدهان المناسب للقطعة. السعر محسوب لكل متر مربع">
                    <Select
                      placeholder="نوع الدهان"
                      value={item.paint_type_id || undefined}
                      onChange={(value) => updateOrderItem(index, 'paint_type_id', value)}
                      style={{ width: '100%' }}
                      showSearch
                      optionFilterProp="children"
                    >
                      {paintTypes.map(type => (
                        <Option key={type.id} value={type.id}>
                          {type.name} (₪{type.price_per_sqm}/م²)
                        </Option>
                      ))}
                    </Select>
                  </Tooltip>
                </Col>
                <Col span={3}>
                  <Tooltip title="أدخل الطول بالسنتيمتر (مثال: 150 سم)">
                    <InputNumber
                      placeholder="الطول (سم)"
                      min={0}
                      precision={1}
                      value={item.length}
                      onChange={(value) => updateOrderItem(index, 'length', value || 0)}
                      style={{ width: '100%' }}
                      addonAfter="سم"
                    />
                  </Tooltip>
                </Col>
                <Col span={3}>
                  <Tooltip title="أدخل العرض بالسنتيمتر (مثال: 80 سم)">
                    <InputNumber
                      placeholder="العرض (سم)"
                      min={0}
                      precision={1}
                      value={item.width}
                      onChange={(value) => updateOrderItem(index, 'width', value || 0)}
                      style={{ width: '100%' }}
                      addonAfter="سم"
                    />
                  </Tooltip>
                </Col>
                <Col span={2}>
                  <Tooltip title="عدد القطع المراد دهانها">
                    <InputNumber
                      placeholder="العدد"
                      min={1}
                      value={item.quantity}
                      onChange={(value) => updateOrderItem(index, 'quantity', value || 1)}
                      style={{ width: '100%' }}
                    />
                  </Tooltip>
                </Col>
                <Col span={3}>
                  <Tooltip title={'المساحة الإجمالية: ' + (item.area_cm || 0).toLocaleString() + ' سم² = ' + item.area.toFixed(2) + ' م²'}>
                    <Input
                      value={item.area.toFixed(2) + ' م²'}
                      disabled
                      style={{ textAlign: 'center', backgroundColor: '#f0f8ff' }}
                    />
                  </Tooltip>
                </Col>
                <Col span={2}>
                  <Button
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => removeOrderItem(index)}
                  />
                </Col>
              </Row>
              <Row style={{ marginTop: '8px' }}>
                <Col span={12}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Popover
                      content={
                        <div style={{ maxWidth: '300px' }}>
                          <p><strong>تفاصيل الحساب:</strong></p>
                          <p>الطول: {item.length} سم</p>
                          <p>العرض: {item.width} سم</p>
                          <p>العدد: {item.quantity} قطعة</p>
                          <p>المساحة: {item.length} × {item.width} × {item.quantity} = {(item.area_cm || 0).toLocaleString()} سم²</p>
                          <p>المساحة بالمتر المربع: {item.area.toFixed(4)} م²</p>
                          <p>سعر المتر المربع: ₪{item.unit_price.toFixed(2)}</p>
                          <p><strong>المجموع: {item.area.toFixed(4)} × ₪{item.unit_price.toFixed(2)} = ₪{item.total_price.toFixed(2)}</strong></p>
                        </div>
                      }
                      title="تفاصيل الحساب"
                      trigger="hover"
                    >
                      <span style={{ cursor: 'pointer', color: '#1890ff' }}>
                        <CalculatorOutlined style={{ marginLeft: '4px' }} />
                        الحساب: {item.length}سم × {item.width}سم × {item.quantity} = {item.area.toFixed(2)}م² × ₪{item.unit_price.toFixed(2)}
                      </span>
                    </Popover>
                    <strong style={{ color: '#1890ff', fontSize: '16px' }}>
                      ₪{item.total_price.toFixed(2)}
                    </strong>
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ textAlign: 'right' }}>
                    <Upload
                      listType="picture-card"
                      fileList={item.images?.map((img, imgIndex) => ({
                        uid: `${index}-${imgIndex}`,
                        name: `صورة ${imgIndex + 1}`,
                        status: 'done',
                        url: img,
                        thumbUrl: img
                      })) || []}
                      onPreview={handlePreview}
                      onChange={(info) => handleImageUpload(info, index)}
                      onRemove={(file) => handleRemoveImage(file, index)}
                      beforeUpload={() => false} // منع الرفع التلقائي
                      accept="image/*"
                      multiple
                    >
                      {(item.images?.length || 0) < 3 && (
                        <div>
                          <CameraOutlined />
                          <div style={{ marginTop: 8, fontSize: '12px' }}>صور القطعة</div>
                        </div>
                      )}
                    </Upload>
                  </div>
                </Col>
              </Row>
              {item.images && item.images.length > 0 && (
                <Row style={{ marginTop: '8px' }}>
                  <Col span={24}>
                    <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                      {item.images.map((img, imgIndex) => (
                        <Image
                          key={imgIndex}
                          width={60}
                          height={60}
                          src={img}
                          style={{ borderRadius: '4px', objectFit: 'cover' }}
                          preview={{
                            mask: <div style={{ fontSize: '12px' }}>معاينة</div>
                          }}
                        />
                      ))}
                    </div>
                  </Col>
                </Row>
              )}
            </ItemCard>
          ))}

          <Button
            type="dashed"
            icon={<PlusOutlined />}
            onClick={addOrderItem}
            style={{ width: '100%', marginBottom: '16px' }}
          >
            إضافة صنف
          </Button>

          {orderItems.length > 0 && (
            <div style={{
              background: 'linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%)',
              padding: '16px',
              borderRadius: '8px',
              marginBottom: '16px',
              border: '1px solid #d9d9d9'
            }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title={
                      <Space>
                        إجمالي المساحة (سم²)
                        <Tooltip title="المساحة الإجمالية بالسنتيمتر المربع">
                          <InfoCircleOutlined />
                        </Tooltip>
                      </Space>
                    }
                    value={orderItems.reduce((sum, item) => sum + (item.area_cm || 0), 0)}
                    precision={0}
                    suffix="سم²"
                    formatter={(value) => value.toLocaleString()}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title={
                      <Space>
                        إجمالي المساحة (م²)
                        <Tooltip title="المساحة الإجمالية بالمتر المربع">
                          <InfoCircleOutlined />
                        </Tooltip>
                      </Space>
                    }
                    value={orderItems.reduce((sum, item) => sum + item.area, 0)}
                    precision={2}
                    suffix="م²"
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="إجمالي المبلغ"
                    value={orderItems.reduce((sum, item) => sum + item.total_price, 0)}
                    precision={2}
                    suffix="₪"
                    valueStyle={{ color: '#1890ff', fontSize: '20px', fontWeight: 'bold' }}
                  />
                </Col>
              </Row>
            </div>
          )}

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => {
                setModalVisible(false)
                setEditingOrder(null)
                form.resetFields()
                setOrderItems([])
              }}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                إنشاء الأمر
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* مودال التقويم */}
      <Modal
        title="تقويم أوامر الدهان"
        open={calendarVisible}
        onCancel={() => setCalendarVisible(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        <Calendar
          dateCellRender={dateCellRender}
          monthCellRender={monthCellRender}
          onSelect={(date) => {
            const { orders: dayOrders, notifications: dayNotifications } = getCalendarData(date)
            if (dayOrders.length > 0 || dayNotifications.length > 0) {
              Modal.info({
                title: `أحداث يوم ${date.format('YYYY-MM-DD')}`,
                content: (
                  <div>
                    {dayOrders.length > 0 && (
                      <div style={{ marginBottom: '16px' }}>
                        <h4>أوامر الدهان:</h4>
                        {dayOrders.map(order => (
                          <div key={order.id} style={{ marginBottom: '8px', padding: '8px', background: '#f0f8ff', borderRadius: '4px' }}>
                            <div><strong>{order.order_number}</strong> - {order.customer_name}</div>
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              المساحة: {order.total_area}م² - المبلغ: ₪{order.total_amount}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    {dayNotifications.length > 0 && (
                      <div>
                        <h4>التنبيهات:</h4>
                        {dayNotifications.map(notification => (
                          <div key={notification.id} style={{ marginBottom: '8px', padding: '8px', background: '#fff7e6', borderRadius: '4px' }}>
                            <div><strong>{notification.message}</strong></div>
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              الوقت: {notification.time}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ),
                width: 600
              })
            }
          }}
        />
      </Modal>

      {/* مودال إضافة التنبيهات */}
      <Modal
        title="إضافة تنبيه مخصص"
        open={notificationModalVisible}
        onCancel={() => {
          setNotificationModalVisible(false)
          notificationForm.resetFields()
          setSelectedOrderForNotification(null)
        }}
        onOk={() => notificationForm.submit()}
        okText="إضافة التنبيه"
        cancelText="إلغاء"
      >
        <Form
          form={notificationForm}
          layout="vertical"
          onFinish={handleNotificationSubmit}
        >
          <Form.Item
            label="نوع التنبيه"
            name="type"
            rules={[{ required: true, message: 'يرجى اختيار نوع التنبيه' }]}
          >
            <Select placeholder="اختر نوع التنبيه">
              <Option value="delivery">تنبيه تسليم</Option>
              <Option value="followup">تنبيه متابعة</Option>
              <Option value="reminder">تنبيه تذكير</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="التاريخ"
            name="date"
            rules={[{ required: true, message: 'يرجى اختيار التاريخ' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            label="الوقت"
            name="time"
            rules={[{ required: true, message: 'يرجى اختيار الوقت' }]}
          >
            <Input placeholder="مثال: 09:00" />
          </Form.Item>

          <Form.Item
            label="رسالة التنبيه (اختيارية)"
            name="message"
          >
            <Input.TextArea
              placeholder="أدخل رسالة مخصصة للتنبيه..."
              rows={3}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* مودال معاينة الصور */}
      <Modal
        open={imagePreviewVisible}
        title="معاينة الصورة"
        footer={null}
        onCancel={() => setImagePreviewVisible(false)}
        width={800}
      >
        <img alt="معاينة" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>
  )
}

export default PaintOrdersManagement
