/**
 * ===================================
 * نظام أمان الطباعة
 * Print Security System
 * ===================================
 */

class PrintSecurity {
    constructor() {
        this.sensitiveSelectors = [
            '.sensitive-data',
            '.confidential',
            '.internal-only',
            '.password',
            '.secret',
            '.private-info',
            '[data-sensitive="true"]'
        ];
        
        this.userPermissions = this.getUserPermissions();
        this.init();
    }

    /**
     * تهيئة نظام الأمان
     */
    init() {
        this.setupPrintProtection();
        this.setupDataEncryption();
        this.setupAccessControl();
        this.setupAuditLogging();
    }

    /**
     * إعداد حماية الطباعة
     */
    setupPrintProtection() {
        // منع الطباعة للمستخدمين غير المخولين
        if (!this.userPermissions.canPrint) {
            document.querySelectorAll('.print-btn, .btn-print').forEach(btn => {
                btn.disabled = true;
                btn.title = 'ليس لديك صلاحية للطباعة';
                btn.classList.add('disabled');
            });
            return;
        }

        // إخفاء البيانات الحساسة قبل الطباعة
        window.addEventListener('beforeprint', () => {
            this.hideSensitiveData();
            this.logPrintAttempt();
        });

        // إظهار البيانات بعد الطباعة
        window.addEventListener('afterprint', () => {
            this.showSensitiveData();
        });

        // حماية من طباعة الشاشة
        this.preventScreenCapture();
    }

    /**
     * إخفاء البيانات الحساسة
     */
    hideSensitiveData() {
        this.sensitiveElements = [];
        
        this.sensitiveSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                // حفظ المحتوى الأصلي
                this.sensitiveElements.push({
                    element: element,
                    originalContent: element.innerHTML,
                    originalStyle: element.style.cssText
                });
                
                // إخفاء أو تشويش المحتوى
                if (element.dataset.hideOnPrint === 'true') {
                    element.style.display = 'none';
                } else {
                    element.innerHTML = '***';
                    element.style.color = '#ccc';
                }
            });
        });

        // إخفاء معلومات حساسة محددة
        this.hideSpecificSensitiveInfo();
    }

    /**
     * إظهار البيانات الحساسة
     */
    showSensitiveData() {
        if (this.sensitiveElements) {
            this.sensitiveElements.forEach(item => {
                item.element.innerHTML = item.originalContent;
                item.element.style.cssText = item.originalStyle;
            });
            this.sensitiveElements = [];
        }
    }

    /**
     * إخفاء معلومات حساسة محددة
     */
    hideSpecificSensitiveInfo() {
        // إخفاء أرقام الحسابات البنكية
        document.querySelectorAll('.bank-account, .account-number').forEach(el => {
            if (el.textContent.length > 4) {
                const visible = el.textContent.slice(-4);
                el.textContent = '****' + visible;
            }
        });

        // إخفاء أرقام الهوية
        document.querySelectorAll('.id-number, .national-id').forEach(el => {
            if (el.textContent.length > 4) {
                const visible = el.textContent.slice(-4);
                el.textContent = '****' + visible;
            }
        });

        // إخفاء معلومات شخصية حساسة
        if (!this.userPermissions.canViewPersonalInfo) {
            document.querySelectorAll('.personal-info').forEach(el => {
                el.style.display = 'none';
            });
        }
    }

    /**
     * إعداد تشفير البيانات
     */
    setupDataEncryption() {
        // تشفير البيانات الحساسة في localStorage
        this.encryptSensitiveStorage();
        
        // تشفير البيانات المرسلة للطباعة
        this.setupEncryptedPrintData();
    }

    /**
     * تشفير البيانات في التخزين المحلي
     */
    encryptSensitiveStorage() {
        const sensitiveKeys = ['user_data', 'print_history', 'sensitive_settings'];
        
        sensitiveKeys.forEach(key => {
            const data = localStorage.getItem(key);
            if (data && !this.isEncrypted(data)) {
                const encrypted = this.encrypt(data);
                localStorage.setItem(key, encrypted);
            }
        });
    }

    /**
     * إعداد تحكم الوصول
     */
    setupAccessControl() {
        // فحص صلاحيات المستخدم
        if (!this.userPermissions.canAccessPrintSystem) {
            this.blockPrintAccess();
            return;
        }

        // تطبيق قيود الطباعة حسب الدور
        this.applyRoleBasedRestrictions();
        
        // تطبيق قيود الوقت
        this.applyTimeBasedRestrictions();
    }

    /**
     * حظر الوصول للطباعة
     */
    blockPrintAccess() {
        document.querySelectorAll('.print-btn, .btn-print').forEach(btn => {
            btn.remove();
        });
        
        // منع اختصارات الطباعة
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                alert('ليس لديك صلاحية للطباعة');
            }
        });
    }

    /**
     * تطبيق قيود حسب الدور
     */
    applyRoleBasedRestrictions() {
        const userRole = this.userPermissions.role;
        
        switch (userRole) {
            case 'viewer':
                // المشاهد: طباعة محدودة فقط
                this.limitPrintOptions(['basic']);
                break;
                
            case 'user':
                // المستخدم: طباعة عادية
                this.limitPrintOptions(['basic', 'detailed']);
                break;
                
            case 'admin':
                // المدير: طباعة كاملة
                // لا توجد قيود
                break;
                
            default:
                this.blockPrintAccess();
        }
    }

    /**
     * تحديد خيارات الطباعة
     */
    limitPrintOptions(allowedOptions) {
        document.querySelectorAll('.print-option').forEach(option => {
            if (!allowedOptions.includes(option.dataset.option)) {
                option.style.display = 'none';
            }
        });
    }

    /**
     * تطبيق قيود الوقت
     */
    applyTimeBasedRestrictions() {
        const now = new Date();
        const hour = now.getHours();
        
        // منع الطباعة خارج ساعات العمل (إذا كان مطلوباً)
        if (this.userPermissions.restrictWorkingHours) {
            if (hour < 8 || hour > 18) {
                this.blockPrintAccess();
                console.log('الطباعة محظورة خارج ساعات العمل');
            }
        }
    }

    /**
     * إعداد تسجيل العمليات
     */
    setupAuditLogging() {
        this.auditLog = [];
        
        // تسجيل محاولات الطباعة
        document.addEventListener('print-attempt', (e) => {
            this.logEvent('print_attempt', e.detail);
        });
        
        // تسجيل الوصول للبيانات الحساسة
        this.logSensitiveDataAccess();
    }

    /**
     * تسجيل محاولة الطباعة
     */
    logPrintAttempt() {
        const logEntry = {
            timestamp: new Date().toISOString(),
            user: this.getCurrentUser(),
            action: 'print_attempt',
            document: this.getCurrentDocument(),
            ip: this.getUserIP(),
            userAgent: navigator.userAgent
        };
        
        this.auditLog.push(logEntry);
        this.sendAuditLog(logEntry);
        
        // إرسال حدث مخصص
        document.dispatchEvent(new CustomEvent('print-attempt', {
            detail: logEntry
        }));
    }

    /**
     * تسجيل الوصول للبيانات الحساسة
     */
    logSensitiveDataAccess() {
        this.sensitiveSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                element.addEventListener('click', () => {
                    this.logEvent('sensitive_data_access', {
                        element: selector,
                        timestamp: new Date().toISOString()
                    });
                });
            });
        });
    }

    /**
     * تسجيل حدث
     */
    logEvent(eventType, data) {
        const logEntry = {
            type: eventType,
            timestamp: new Date().toISOString(),
            user: this.getCurrentUser(),
            data: data
        };
        
        this.auditLog.push(logEntry);
        this.sendAuditLog(logEntry);
    }

    /**
     * منع التقاط الشاشة
     */
    preventScreenCapture() {
        // منع PrintScreen
        document.addEventListener('keydown', (e) => {
            if (e.key === 'PrintScreen') {
                e.preventDefault();
                alert('التقاط الشاشة محظور لأسباب أمنية');
            }
        });

        // منع النقر بالزر الأيمن على البيانات الحساسة
        this.sensitiveSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                element.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                });
            });
        });
    }

    /**
     * تشفير البيانات
     */
    encrypt(data) {
        // تشفير بسيط (يجب استخدام تشفير أقوى في الإنتاج)
        return btoa(encodeURIComponent(data));
    }

    /**
     * فك التشفير
     */
    decrypt(encryptedData) {
        try {
            return decodeURIComponent(atob(encryptedData));
        } catch (e) {
            return null;
        }
    }

    /**
     * فحص ما إذا كانت البيانات مشفرة
     */
    isEncrypted(data) {
        try {
            atob(data);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * جلب صلاحيات المستخدم
     */
    getUserPermissions() {
        // هذه البيانات يجب أن تأتي من الخادم
        return {
            canPrint: true,
            canAccessPrintSystem: true,
            canViewPersonalInfo: false,
            role: 'user',
            restrictWorkingHours: false
        };
    }

    /**
     * جلب المستخدم الحالي
     */
    getCurrentUser() {
        return {
            id: 'user123',
            name: 'مستخدم تجريبي',
            role: 'user'
        };
    }

    /**
     * جلب المستند الحالي
     */
    getCurrentDocument() {
        return {
            type: 'invoice',
            id: document.querySelector('[data-document-id]')?.dataset.documentId || 'unknown',
            title: document.title
        };
    }

    /**
     * جلب IP المستخدم
     */
    getUserIP() {
        // يجب الحصول على IP من الخادم
        return 'unknown';
    }

    /**
     * إرسال سجل التدقيق
     */
    sendAuditLog(logEntry) {
        // إرسال السجل للخادم
        fetch('/api/audit-log', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(logEntry)
        }).catch(error => {
            console.error('فشل في إرسال سجل التدقيق:', error);
        });
    }

    /**
     * إعداد تشفير بيانات الطباعة
     */
    setupEncryptedPrintData() {
        // تشفير البيانات قبل إرسالها للطباعة
        const originalPrint = window.print;
        window.print = () => {
            this.encryptPrintData();
            originalPrint.call(window);
            this.decryptPrintData();
        };
    }

    /**
     * تشفير بيانات الطباعة
     */
    encryptPrintData() {
        // تشفير البيانات الحساسة قبل الطباعة
        document.querySelectorAll('[data-encrypt-on-print="true"]').forEach(element => {
            const originalContent = element.textContent;
            element.dataset.originalContent = originalContent;
            element.textContent = this.encrypt(originalContent);
        });
    }

    /**
     * فك تشفير بيانات الطباعة
     */
    decryptPrintData() {
        // إعادة البيانات الأصلية بعد الطباعة
        document.querySelectorAll('[data-encrypt-on-print="true"]').forEach(element => {
            if (element.dataset.originalContent) {
                element.textContent = element.dataset.originalContent;
                delete element.dataset.originalContent;
            }
        });
    }
}

// إنشاء مثيل نظام الأمان
const printSecurity = new PrintSecurity();

// تصدير للاستخدام العام
window.printSecurity = printSecurity;
