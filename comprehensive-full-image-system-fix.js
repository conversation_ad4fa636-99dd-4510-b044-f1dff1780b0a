const fs = require('fs');
const path = require('path');
const initSqlJs = require('sql.js');

async function comprehensiveFullImageSystemFix() {
  try {
    console.log('🔧 بدء الإصلاح الشامل لكامل نظام الصور...\n');
    
    const dbPath = path.join(process.env.APPDATA, 'accounting-production-app', 'accounting.db');
    
    if (!fs.existsSync(dbPath)) {
      console.log('❌ قاعدة البيانات غير موجودة:', dbPath);
      return;
    }
    
    console.log(`📍 فحص قاعدة البيانات: ${dbPath}`);
    
    const SQL = await initSqlJs({
      locateFile: file => path.join(__dirname, 'node_modules', 'sql.js', 'dist', file)
    });
    
    const filebuffer = fs.readFileSync(dbPath);
    const db = new SQL.Database(filebuffer);
    
    // 1. إنشاء جميع جداول الصور
    console.log('🖼️ إنشاء جداول الصور الشاملة...\n');
    
    // جدول صور أوامر الإنتاج
    console.log('📋 إنشاء جدول صور أوامر الإنتاج...');
    try {
      db.run(`
        CREATE TABLE IF NOT EXISTS production_order_images (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          order_id INTEGER NOT NULL,
          image_name TEXT NOT NULL,
          image_path TEXT NOT NULL,
          file_size INTEGER DEFAULT 0,
          file_type TEXT DEFAULT 'image/jpeg',
          description TEXT,
          category TEXT DEFAULT 'general',
          is_primary INTEGER DEFAULT 0,
          tags TEXT,
          notes TEXT,
          uploaded_by INTEGER,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('   ✅ جدول production_order_images');
    } catch (error) {
      console.log('   ℹ️ جدول production_order_images موجود مسبقاً');
    }
    
    // جدول الصور الموحد
    console.log('📋 إنشاء جدول الصور الموحد...');
    try {
      db.run(`
        CREATE TABLE IF NOT EXISTS unified_images (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          original_name TEXT NOT NULL,
          path TEXT NOT NULL,
          thumbnail_path TEXT,
          size INTEGER NOT NULL,
          width INTEGER,
          height INTEGER,
          type TEXT NOT NULL,
          category TEXT NOT NULL,
          context_type TEXT NOT NULL,
          context_id INTEGER NOT NULL,
          description TEXT DEFAULT '',
          tags TEXT DEFAULT '[]',
          is_active INTEGER DEFAULT 1,
          is_primary INTEGER DEFAULT 0,
          sort_order INTEGER DEFAULT 0,
          uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          uploaded_by INTEGER,
          metadata TEXT DEFAULT '{}',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('   ✅ جدول unified_images');
    } catch (error) {
      console.log('   ℹ️ جدول unified_images موجود مسبقاً');
    }
    
    // جدول صور الأصناف
    console.log('📋 إنشاء جدول صور الأصناف...');
    try {
      db.run(`
        CREATE TABLE IF NOT EXISTS item_images (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_id INTEGER NOT NULL,
          image_name TEXT NOT NULL,
          image_path TEXT NOT NULL,
          file_size INTEGER DEFAULT 0,
          file_type TEXT DEFAULT 'image/jpeg',
          description TEXT,
          is_primary INTEGER DEFAULT 0,
          sort_order INTEGER DEFAULT 0,
          uploaded_by INTEGER,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('   ✅ جدول item_images');
    } catch (error) {
      console.log('   ℹ️ جدول item_images موجود مسبقاً');
    }
    
    // جدول صور العملاء
    console.log('📋 إنشاء جدول صور العملاء...');
    try {
      db.run(`
        CREATE TABLE IF NOT EXISTS customer_images (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          customer_id INTEGER NOT NULL,
          image_name TEXT NOT NULL,
          image_path TEXT NOT NULL,
          file_size INTEGER DEFAULT 0,
          file_type TEXT DEFAULT 'image/jpeg',
          description TEXT,
          category TEXT DEFAULT 'profile',
          is_primary INTEGER DEFAULT 0,
          uploaded_by INTEGER,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('   ✅ جدول customer_images');
    } catch (error) {
      console.log('   ℹ️ جدول customer_images موجود مسبقاً');
    }
    
    // جدول صور الموردين
    console.log('📋 إنشاء جدول صور الموردين...');
    try {
      db.run(`
        CREATE TABLE IF NOT EXISTS supplier_images (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          supplier_id INTEGER NOT NULL,
          image_name TEXT NOT NULL,
          image_path TEXT NOT NULL,
          file_size INTEGER DEFAULT 0,
          file_type TEXT DEFAULT 'image/jpeg',
          description TEXT,
          category TEXT DEFAULT 'profile',
          is_primary INTEGER DEFAULT 0,
          uploaded_by INTEGER,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('   ✅ جدول supplier_images');
    } catch (error) {
      console.log('   ℹ️ جدول supplier_images موجود مسبقاً');
    }
    
    // جدول إعدادات الصور
    console.log('📋 إنشاء جدول إعدادات الصور...');
    try {
      db.run(`
        CREATE TABLE IF NOT EXISTS image_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          setting_key TEXT UNIQUE NOT NULL,
          setting_value TEXT NOT NULL,
          description TEXT,
          category TEXT DEFAULT 'general',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('   ✅ جدول image_settings');
    } catch (error) {
      console.log('   ℹ️ جدول image_settings موجود مسبقاً');
    }
    
    // 2. إنشاء جميع الفهارس
    console.log('\n🔍 إنشاء الفهارس الشاملة...');
    
    const allIndexes = [
      // فهارس صور أوامر الإنتاج
      'CREATE INDEX IF NOT EXISTS idx_production_order_images_order_id ON production_order_images(order_id)',
      'CREATE INDEX IF NOT EXISTS idx_production_order_images_category ON production_order_images(category)',
      'CREATE INDEX IF NOT EXISTS idx_production_order_images_primary ON production_order_images(is_primary)',
      
      // فهارس الصور الموحدة
      'CREATE INDEX IF NOT EXISTS idx_unified_images_context ON unified_images(context_type, context_id)',
      'CREATE INDEX IF NOT EXISTS idx_unified_images_category ON unified_images(category)',
      'CREATE INDEX IF NOT EXISTS idx_unified_images_active ON unified_images(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_unified_images_primary ON unified_images(is_primary)',
      
      // فهارس صور الأصناف
      'CREATE INDEX IF NOT EXISTS idx_item_images_item_id ON item_images(item_id)',
      'CREATE INDEX IF NOT EXISTS idx_item_images_primary ON item_images(is_primary)',
      
      // فهارس صور العملاء
      'CREATE INDEX IF NOT EXISTS idx_customer_images_customer_id ON customer_images(customer_id)',
      'CREATE INDEX IF NOT EXISTS idx_customer_images_category ON customer_images(category)',
      
      // فهارس صور الموردين
      'CREATE INDEX IF NOT EXISTS idx_supplier_images_supplier_id ON supplier_images(supplier_id)',
      'CREATE INDEX IF NOT EXISTS idx_supplier_images_category ON supplier_images(category)',
      
      // فهارس إعدادات الصور
      'CREATE INDEX IF NOT EXISTS idx_image_settings_key ON image_settings(setting_key)',
      'CREATE INDEX IF NOT EXISTS idx_image_settings_category ON image_settings(category)'
    ];
    
    let indexCount = 0;
    allIndexes.forEach(indexSql => {
      try {
        db.run(indexSql);
        indexCount++;
      } catch (error) {
        // تجاهل أخطاء الفهارس الموجودة
      }
    });
    console.log(`   ✅ تم إنشاء ${indexCount} فهرس`);
    
    // 3. إدراج إعدادات الصور الافتراضية
    console.log('\n⚙️ إدراج إعدادات الصور الافتراضية...');
    
    const defaultSettings = [
      { key: 'max_file_size', value: '10485760', description: 'الحد الأقصى لحجم الملف بالبايت (10MB)', category: 'upload' },
      { key: 'allowed_types', value: 'image/jpeg,image/png,image/gif,image/webp,image/svg+xml', description: 'أنواع الملفات المسموحة', category: 'upload' },
      { key: 'thumbnail_width', value: '150', description: 'عرض الصورة المصغرة', category: 'thumbnail' },
      { key: 'thumbnail_height', value: '150', description: 'ارتفاع الصورة المصغرة', category: 'thumbnail' },
      { key: 'image_quality', value: '85', description: 'جودة ضغط الصور (1-100)', category: 'compression' },
      { key: 'storage_path', value: 'images', description: 'مسار حفظ الصور', category: 'storage' },
      { key: 'enable_watermark', value: 'false', description: 'تفعيل العلامة المائية', category: 'watermark' },
      { key: 'auto_backup', value: 'true', description: 'النسخ الاحتياطي التلقائي للصور', category: 'backup' }
    ];
    
    for (const setting of defaultSettings) {
      try {
        db.run(`
          INSERT OR IGNORE INTO image_settings (setting_key, setting_value, description, category)
          VALUES (?, ?, ?, ?)
        `, [setting.key, setting.value, setting.description, setting.category]);
      } catch (error) {
        // تجاهل الأخطاء للإعدادات الموجودة
      }
    }
    console.log(`   ✅ تم إدراج ${defaultSettings.length} إعداد افتراضي`);
    
    // 4. إنشاء بيانات تجريبية شاملة (إذا كانت الجداول فارغة)
    console.log('\n🧪 فحص وإنشاء البيانات التجريبية...');
    
    // فحص أوامر الإنتاج وإنشاء صور تجريبية
    try {
      const ordersResult = db.exec('SELECT id, order_number FROM production_orders LIMIT 3');
      if (ordersResult[0]?.values && ordersResult[0].values.length > 0) {
        console.log('📸 إنشاء صور تجريبية لأوامر الإنتاج...');
        
        for (const orderRow of ordersResult[0].values) {
          const orderId = orderRow[0];
          const orderNumber = orderRow[1];
          
          // فحص إذا كانت توجد صور مسبقاً
          const existingImages = db.exec(`SELECT COUNT(*) FROM production_order_images WHERE order_id = ${orderId}`);
          const imageCount = existingImages[0]?.values[0]?.[0] || 0;
          
          if (imageCount === 0) {
            const svgContent = `<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="150" fill="#4ECDC4"/><text x="100" y="75" text-anchor="middle" fill="white" font-size="16">صورة تجريبية</text><text x="100" y="100" text-anchor="middle" fill="white" font-size="12">${orderNumber}</text></svg>`;
            const dataUrl = `data:image/svg+xml;charset=utf-8;base64,${Buffer.from(svgContent, 'utf8').toString('base64')}`;
            
            try {
              db.run(`
                INSERT INTO production_order_images (
                  order_id, image_name, image_path, file_size, file_type,
                  description, category, is_primary
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                orderId,
                `صورة تجريبية - ${orderNumber}`,
                dataUrl,
                svgContent.length,
                'image/svg+xml',
                'صورة تجريبية للاختبار',
                'general',
                1
              ]);
              console.log(`   ✅ صورة لأمر ${orderNumber}`);
            } catch (error) {
              console.log(`   ⚠️ تخطي صورة أمر ${orderNumber}: ${error.message}`);
            }
          }
        }
      }
    } catch (error) {
      console.log('   ℹ️ لا توجد أوامر إنتاج لإنشاء صور لها');
    }
    
    // فحص الأصناف وإنشاء صور تجريبية
    try {
      const itemsResult = db.exec('SELECT id, name FROM items LIMIT 3');
      if (itemsResult[0]?.values && itemsResult[0].values.length > 0) {
        console.log('📦 إنشاء صور تجريبية للأصناف...');
        
        for (const itemRow of itemsResult[0].values) {
          const itemId = itemRow[0];
          const itemName = itemRow[1];
          
          const existingImages = db.exec(`SELECT COUNT(*) FROM item_images WHERE item_id = ${itemId}`);
          const imageCount = existingImages[0]?.values[0]?.[0] || 0;
          
          if (imageCount === 0) {
            const svgContent = `<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="150" fill="#FF6B6B"/><text x="100" y="75" text-anchor="middle" fill="white" font-size="14">صنف</text><text x="100" y="100" text-anchor="middle" fill="white" font-size="12">${itemName}</text></svg>`;
            const dataUrl = `data:image/svg+xml;charset=utf-8;base64,${Buffer.from(svgContent, 'utf8').toString('base64')}`;
            
            try {
              db.run(`
                INSERT INTO item_images (
                  item_id, image_name, image_path, file_size, file_type,
                  description, is_primary
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
              `, [
                itemId,
                `صورة ${itemName}`,
                dataUrl,
                svgContent.length,
                'image/svg+xml',
                'صورة تجريبية للصنف',
                1
              ]);
              console.log(`   ✅ صورة للصنف ${itemName}`);
            } catch (error) {
              console.log(`   ⚠️ تخطي صورة صنف ${itemName}: ${error.message}`);
            }
          }
        }
      }
    } catch (error) {
      console.log('   ℹ️ لا توجد أصناف لإنشاء صور لها');
    }
    
    // 5. حفظ قاعدة البيانات
    console.log('\n💾 حفظ التغييرات...');
    const data = db.export();
    fs.writeFileSync(dbPath, data);
    
    // 6. إحصائيات شاملة
    console.log('\n📊 إحصائيات النظام الشامل:');
    
    const stats = {};
    const imageTables = [
      'production_order_images',
      'unified_images', 
      'item_images',
      'customer_images',
      'supplier_images',
      'image_settings'
    ];
    
    for (const table of imageTables) {
      try {
        const result = db.exec(`SELECT COUNT(*) FROM ${table}`);
        stats[table] = result[0]?.values[0]?.[0] || 0;
        console.log(`   📋 ${table}: ${stats[table]} سجل`);
      } catch (error) {
        console.log(`   ❌ خطأ في قراءة ${table}: ${error.message}`);
      }
    }
    
    db.close();
    
    console.log('\n' + '='.repeat(80));
    console.log('✅ تم الإصلاح الشامل لكامل نظام الصور بنجاح!');
    console.log('='.repeat(80));
    
    console.log('\n🎯 ما تم إصلاحه:');
    console.log('   ✅ جداول صور أوامر الإنتاج');
    console.log('   ✅ جداول الصور الموحدة');
    console.log('   ✅ جداول صور الأصناف');
    console.log('   ✅ جداول صور العملاء والموردين');
    console.log('   ✅ إعدادات النظام');
    console.log('   ✅ الفهارس والأداء');
    console.log('   ✅ البيانات التجريبية');
    
    console.log('\n📝 الخطوات التالية:');
    console.log('   1. إعادة تشغيل التطبيق');
    console.log('   2. اختبار جميع أنواع الصور');
    console.log('   3. اختبار الطباعة مع الصور');
    console.log('   4. اختبار ربط الأجهزة');
    
  } catch (error) {
    console.error('❌ خطأ في الإصلاح الشامل:', error);
  }
}

// تشغيل الإصلاح الشامل
comprehensiveFullImageSystemFix();
