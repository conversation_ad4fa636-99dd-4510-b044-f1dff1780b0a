import { SafeLogger as Logger } from './logger'
/**
 * نّام إدارة الموارد الشامل
 * يدير تنّيف جميع الموارد بشكل مركزي لمنع تسريبات الذاكرة
 */

interface Resource {
  id: string
  name: string
  cleanup: () => Promise<void> | void
  priority: number // أولوية التنّيف (أقل رقم = أولوية أعلى)
  type: 'audio' | 'notification' | 'event' | 'timer' | 'observer' | 'other'
}

interface ResourceStats {
  totalResources: number
  resourcesByType: Record<string, number>
  cleanupErrors: number
  lastCleanupTime: Date | null
}

class ResourceManager {
  private resources: Map<string, Resource> = new Map()
  private isCleaningUp = false
  private cleanupPromise: Promise<void> | null = null
  private stats: ResourceStats = {
    totalResources: 0,
    resourcesByType: {},
    cleanupErrors: 0,
    lastCleanupTime: null
  }

  // تسجيل مورد جديد
  register(resource: Omit<Resource, 'id'> & { id?: string }): string {
    const id = resource.id || this.generateId()
    
    const fullResource: Resource = {
      id,
      name: resource.name,
      cleanup: resource.cleanup,
      priority: resource.priority || 100,
      type: resource.type
    }

    this.resources.set(id, fullResource)
    this.updateStats()
    
    Logger.info('ResourceManager', `📝 تم تسجيل مورد: ${resource.name} (${id})`)
    return id
  }

  // إلغاء تسجيل مورد
  unregister(id: string): boolean {
    const resource = this.resources.get(id)
    if (resource) {
      this.resources.delete(id)
      this.updateStats()
      Logger.info('ResourceManager', `🗑️ تم إلغاء تسجيل مورد: ${resource.name} (${id})`)
      return true
    }
    return false
  }

  // تنّيف مورد محدد
  async cleanupResource(id: string): Promise<boolean> {
    const resource = this.resources.get(id)
    if (!resource) {
      Logger.warn('ResourceManager', `⚠️ مورد غير موجود: ${id}`)
      return false
    }

    try {
      Logger.info('ResourceManager', `🧹 تنّيف مورد: ${resource.name} (${id})`)
      await resource.cleanup()
      this.unregister(id)
      return true
    } catch (error) {
      Logger.error('ResourceManager', `❌ خطأ في تنّيف مورد ${resource.name}:`, error)
      this.stats.cleanupErrors++
      return false
    }
  }

  // تنّيف جميع الموارد
  async cleanupAll(): Promise<void> {
    if (this.isCleaningUp) {
      Logger.info('ResourceManager', '⏳ تنّيف قيد التنفيذ بالفعل، انتّار...')
      return this.cleanupPromise || Promise.resolve()
    }

    this.isCleaningUp = true
    this.cleanupPromise = this.performCleanup()
    
    try {
      await this.cleanupPromise
    } finally {
      this.isCleaningUp = false
      this.cleanupPromise = null
    }
  }

  // تنفيذ التنّيف الفعلي
  private async performCleanup(): Promise<void> {
    Logger.info('ResourceManager', '🧹 بدء تنّيف شامل للموارد...')
    const startTime = Date.now()

    // ترتيب الموارد حسب الأولوية
    const sortedResources = Array.from(this.resources.values())
      .sort((a, b) => a.priority - b.priority)

    const cleanupPromises = sortedResources.map(async (resource) => {
      try {
        Logger.info('ResourceManager', `🧹 تنّيف: ${resource.name} (${resource.type})`)
        await resource.cleanup()
      } catch (error) {
        Logger.error('ResourceManager', `❌ خطأ في تنّيف ${resource.name}:`, error)
        this.stats.cleanupErrors++
      }
    })

    // تنفيذ التنّيف بشكل متوازي مع timeout
    const timeoutPromise = new Promise<void>((_, reject) => {
      setTimeout(() => reject(new Error('انتهت مهلة التنّيف')), 30000) // 30 ثانية
    })

    try {
      await Promise.race([
        Promise.allSettled(cleanupPromises),
        timeoutPromise
      ])
    } catch (error) {
      Logger.error('ResourceManager', '❌ انتهت مهلة تنّيف الموارد:', error)
    }

    // تنّيف القائمة
    this.resources.clear()
    this.stats.lastCleanupTime = new Date()
    this.updateStats()

    const duration = Date.now() - startTime
    Logger.info('ResourceManager', `✅ تم تنّيف الموارد في ${duration}ms`)
  }

  // تنّيف موارد من نوع معين
  async cleanupByType(type: Resource['type']): Promise<void> {
    Logger.info('ResourceManager', `🧹 تنّيف موارد من نوع: ${type}`)
    
    const resourcesOfType = Array.from(this.resources.values())
      .filter(resource => resource.type === type)
      .sort((a, b) => a.priority - b.priority)

    for (const resource of resourcesOfType) {
      await this.cleanupResource(resource.id)
    }
  }

  // الحصول على إحصائيات الموارد
  getStats(): ResourceStats {
    return { ...this.stats }
  }

  // الحصول على قائمة الموارد
  getResources(): Resource[] {
    return Array.from(this.resources.values())
  }

  // فحص صحة الموارد
  healthCheck(): { healthy: boolean; issues: string[] } {
    const issues: string[] = []
    
    if (this.resources.size > 100) {
      issues.push(`عدد كبير من الموارد: ${this.resources.size}`)
    }
    
    if (this.stats.cleanupErrors > 10) {
      issues.push(`أخطاء تنّيف كثيرة: ${this.stats.cleanupErrors}`)
    }

    const audioResources = Array.from(this.resources.values())
      .filter(r => r.type === 'audio').length
    if (audioResources > 20) {
      issues.push(`موارد صوتية كثيرة: ${audioResources}`)
    }

    return {
      healthy: issues.length === 0,
      issues
    }
  }

  // توليد معرف فريد
  private generateId(): string {
    return `resource_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // تحديث الإحصائيات
  private updateStats(): void {
    this.stats.totalResources = this.resources.size
    this.stats.resourcesByType = {}
    
    for (const resource of this.resources.values()) {
      this.stats.resourcesByType[resource.type] = 
        (this.stats.resourcesByType[resource.type] || 0) + 1
    }
  }
}

// إنشاء مثيل واحد للنّام
export const resourceManager = new ResourceManager()

// إتاحة النّام عالمياً
if (typeof window !== 'undefined') {
  ;(window as any).resourceManager = resourceManager
  
  // تنّيف تلقائي عند إغلاق النافذة
  window.addEventListener('beforeunload', () => {
    resourceManager.cleanupAll()
  })
  
  window.addEventListener('pagehide', () => {
    resourceManager.cleanupAll()
  })
}

export default ResourceManager
