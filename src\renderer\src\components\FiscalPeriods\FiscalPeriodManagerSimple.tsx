import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  DatePicker,
  message,
  Tag,
  Space,
  Alert,
  Row,
  Col,
  Typography,
  Popconfirm,
  Tabs,
  Statistic,
  Progress,
  Divider,
  Tooltip,
  Badge,
  Drawer
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
  FileTextOutlined,
  DashboardOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  BarChartOutlined,
  SafetyOutlined,
  SettingOutlined,
  PrinterOutlined,
  DownloadOutlined,
  ReloadOutlined,
  CalendarOutlined,
  BankOutlined,
  AuditOutlined,
  UserOutlined
} from '@ant-design/icons';
import { FiscalPeriod, CreateFiscalPeriodData } from '../../types/fiscalPeriod';
import { fiscalPeriodApi } from '../../services/fiscalPeriodApi';
import dayjs from 'dayjs';
import ValidationDashboard from './ValidationDashboard';
import ClosingWizard from './ClosingWizardSimple';
import ClosingReports from './ClosingReports';
import SecurityManager from './SecurityManager';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface FiscalPeriodManagerProps {
  onPeriodSelect?: (period: FiscalPeriod) => void;
}

const FiscalPeriodManager: React.FC<FiscalPeriodManagerProps> = () => {
  // حالات البيانات الأساسية
  const [periods, setPeriods] = useState<FiscalPeriod[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingPeriod, setEditingPeriod] = useState<FiscalPeriod | null>(null);
  const [form] = Form.useForm();

  // حالات الواجهة المتقدمة
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedPeriod, setSelectedPeriod] = useState<FiscalPeriod | null>(null);
  const [validationDrawerVisible, setValidationDrawerVisible] = useState(false);
  const [closingWizardVisible, setClosingWizardVisible] = useState(false);
  const [reportsDrawerVisible, setReportsDrawerVisible] = useState(false);
  const [securityDrawerVisible, setSecurityDrawerVisible] = useState(false);


  // إحصائيات الفترات
  const [statistics, setStatistics] = useState({
    totalPeriods: 0,
    openPeriods: 0,
    closedPeriods: 0,
    lockedPeriods: 0,
    currentPeriod: null as FiscalPeriod | null
  });

  useEffect(() => {
    loadPeriods();
    loadStatistics();
  }, []);

  const loadPeriods = async () => {
    try {
      setLoading(true);
      const data = await fiscalPeriodApi.getAllPeriods();
      setPeriods(data);

      // تحديث الإحصائيات
      const stats = {
        totalPeriods: data.length,
        openPeriods: data.filter(p => p.status === 'open').length,
        closedPeriods: data.filter(p => p.status === 'closed').length,
        lockedPeriods: data.filter(p => p.status === 'locked').length,
        currentPeriod: data.find(p => p.is_current) || null
      };
      setStatistics(stats);

    } catch (err) {
      setError('فشل في تحميل الفترات المالية');
      console.error('Error loading periods:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      // يمكن إضافة استدعاء API منفصل للإحصائيات المتقدمة
      // const stats = await fiscalPeriodApi.getStatistics();
    } catch (err) {
      console.error('Error loading statistics:', err);
    }
  };

  const handleCreatePeriod = () => {
    setEditingPeriod(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditPeriod = (period: FiscalPeriod) => {
    setEditingPeriod(period);
    form.setFieldsValue({
      period_name: period.period_name,
      start_date: dayjs(period.start_date),
      end_date: dayjs(period.end_date),
      notes: period.notes
    });
    setIsModalVisible(true);
  };

  // وظائف التحقق والإقفال المتقدمة
  const handleValidatePeriod = async (period: FiscalPeriod) => {
    try {
      setSelectedPeriod(period);
      setValidationDrawerVisible(true);

      // تشغيل التحقق
      await fiscalPeriodApi.validatePeriodForClosing(period.id.toString());

    } catch (err) {
      message.error('فشل في تشغيل التحقق');
      console.error('Validation error:', err);
    }
  };

  const handleStartClosing = (period: FiscalPeriod) => {
    setSelectedPeriod(period);
    setClosingWizardVisible(true);
  };

  const handleViewReports = (period: FiscalPeriod) => {
    setSelectedPeriod(period);
    setReportsDrawerVisible(true);
  };

  const handleReopenPeriod = async (period: FiscalPeriod) => {
    try {
      await fiscalPeriodApi.reopenFiscalPeriod(period.id, 1); // userId = 1 مؤقتاً
      message.success('تم إعادة فتح الفترة المالية بنجاح');
      loadPeriods();
    } catch (err) {
      message.error('فشل في إعادة فتح الفترة المالية');
      console.error('Reopen error:', err);
    }
  };



  const handleSavePeriod = async (values: any) => {
    try {
      const periodData: CreateFiscalPeriodData = {
        period_name: values.period_name,
        period_type: values.period_type || 'annual',
        start_date: values.start_date.format('YYYY-MM-DD'),
        end_date: values.end_date.format('YYYY-MM-DD'),
        notes: values.notes
      };

      if (editingPeriod) {
        const response = await fiscalPeriodApi.updateFiscalPeriod(editingPeriod.id, periodData);
        if (response.success) {
          message.success('تم تحديث الفترة المالية بنجاح');
        } else {
          message.error(response.message || 'فشل في تحديث الفترة المالية');
          return;
        }
      } else {
        const response = await fiscalPeriodApi.createFiscalPeriod(periodData);
        if (response.success) {
          message.success('تم إنشاء الفترة المالية بنجاح');
        } else {
          message.error(response.message || 'فشل في إنشاء الفترة المالية');
          return;
        }
      }
      
      setIsModalVisible(false);
      form.resetFields();
      loadPeriods();
    } catch (err) {
      message.error('فشل في حفظ الفترة المالية');
      console.error('Error saving period:', err);
    }
  };

  // مكونات العرض المتقدمة
  const renderDashboard = () => (
    <Space direction="vertical" style={{ width: '100%' }} size="large">
      {/* إحصائيات سريعة محسنة */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <div style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '12px',
            padding: '24px',
            color: 'white',
            textAlign: 'center',
            transition: 'all 0.3s ease',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', marginBottom: '8px' }}>
              {statistics.totalPeriods}
            </div>
            <div style={{ fontSize: '1rem', opacity: 0.9 }}>
              <CalendarOutlined style={{ marginLeft: 8 }} />
              إجمالي الفترات
            </div>
          </div>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <div style={{
            background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
            borderRadius: '12px',
            padding: '24px',
            color: 'white',
            textAlign: 'center',
            transition: 'all 0.3s ease',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', marginBottom: '8px' }}>
              {statistics.openPeriods}
            </div>
            <div style={{ fontSize: '1rem', opacity: 0.9 }}>
              <UnlockOutlined style={{ marginLeft: 8 }} />
              الفترات المفتوحة
            </div>
          </div>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <div style={{
            background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
            borderRadius: '12px',
            padding: '24px',
            color: 'white',
            textAlign: 'center',
            transition: 'all 0.3s ease',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', marginBottom: '8px' }}>
              {statistics.closedPeriods}
            </div>
            <div style={{ fontSize: '1rem', opacity: 0.9 }}>
              <LockOutlined style={{ marginLeft: 8 }} />
              الفترات المقفلة
            </div>
          </div>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <div style={{
            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            borderRadius: '12px',
            padding: '24px',
            color: 'white',
            textAlign: 'center',
            transition: 'all 0.3s ease',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{ fontSize: '2.5rem', fontWeight: 'bold', marginBottom: '8px' }}>
              {statistics.lockedPeriods}
            </div>
            <div style={{ fontSize: '1rem', opacity: 0.9 }}>
              <SafetyOutlined style={{ marginLeft: 8 }} />
              الفترات المؤمنة
            </div>
          </div>
        </Col>
      </Row>

      {/* الفترة الحالية */}
      {statistics.currentPeriod && (
        <Card
          title={
            <Space>
              <BankOutlined />
              <span>الفترة المالية الحالية</span>
              <Badge status="processing" text="نشطة" />
            </Space>
          }
          extra={
            <Space>
              <Button
                type="primary"
                icon={<CheckCircleOutlined />}
                onClick={() => handleValidatePeriod(statistics.currentPeriod!)}
                size="large"
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none',
                  borderRadius: '8px',
                  fontWeight: '600',
                  boxShadow: '0 4px 15px rgba(102, 126, 234, 0.3)'
                }}
              >
                فحص البيانات
              </Button>
              <Button
                icon={<LockOutlined />}
                onClick={() => handleStartClosing(statistics.currentPeriod!)}
                disabled={statistics.currentPeriod.status !== 'open'}
                size="large"
                style={{
                  background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                  border: 'none',
                  borderRadius: '8px',
                  color: '#8b4513',
                  fontWeight: '500'
                }}
              >
                بدء الإقفال
              </Button>
            </Space>
          }
        >
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Text strong>اسم الفترة: </Text>
              <Text>{statistics.currentPeriod.period_name}</Text>
            </Col>
            <Col span={12}>
              <Text strong>الحالة: </Text>
              <Tag color={
                statistics.currentPeriod.status === 'open' ? 'green' :
                statistics.currentPeriod.status === 'closed' ? 'orange' : 'red'
              }>
                {statistics.currentPeriod.status === 'open' ? 'مفتوحة' :
                 statistics.currentPeriod.status === 'closed' ? 'مقفلة' : 'مؤمنة'}
              </Tag>
            </Col>
            <Col span={12}>
              <Text strong>تاريخ البداية: </Text>
              <Text>{dayjs(statistics.currentPeriod.start_date).format('DD/MM/YYYY')}</Text>
            </Col>
            <Col span={12}>
              <Text strong>تاريخ النهاية: </Text>
              <Text>{dayjs(statistics.currentPeriod.end_date).format('DD/MM/YYYY')}</Text>
            </Col>
          </Row>
        </Card>
      )}

      {/* إجراءات سريعة محسنة */}
      <Card
        title="الإجراءات السريعة"
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          border: '1px solid #f0f0f0'
        }}
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <Button
              type="primary"
              block
              icon={<PlusOutlined />}
              onClick={handleCreatePeriod}
              size="large"
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                boxShadow: '0 4px 15px rgba(102, 126, 234, 0.3)'
              }}
            >
              إنشاء فترة مالية جديدة
            </Button>
          </Col>
          <Col xs={24} sm={8}>
            <Button
              block
              icon={<ReloadOutlined />}
              onClick={loadPeriods}
              size="large"
              style={{
                background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                border: 'none',
                borderRadius: '8px',
                color: '#8b4513',
                fontWeight: '500'
              }}
            >
              تحديث البيانات
            </Button>
          </Col>
          <Col xs={24} sm={8}>
            <Button
              block
              icon={<BarChartOutlined />}
              onClick={() => setActiveTab('reports')}
              size="large"
              style={{
                background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                border: 'none',
                borderRadius: '8px',
                color: '#8b4513',
                fontWeight: '500'
              }}
            >
              عرض التقارير
            </Button>
          </Col>
        </Row>
      </Card>
    </Space>
  );



  const columns = [
    {
      title: 'اسم الفترة',
      dataIndex: 'period_name',
      key: 'period_name',
      sorter: true,
      render: (text: string, record: FiscalPeriod) => (
        <Space>
          <Text strong>{text}</Text>
          {record.is_current && <Badge status="processing" text="حالية" />}
        </Space>
      )
    },
    {
      title: 'نوع الفترة',
      dataIndex: 'period_type',
      key: 'period_type',
      render: (type: string) => {
        const typeMap = {
          'monthly': 'شهرية',
          'quarterly': 'ربع سنوية',
          'semi_annual': 'نصف سنوية',
          'annual': 'سنوية'
        };
        return (
          <Tag color="blue">
            {typeMap[type as keyof typeof typeMap] || type}
          </Tag>
        );
      }
    },
    {
      title: 'الفترة الزمنية',
      key: 'period_range',
      render: (_: any, record: FiscalPeriod) => (
        <Space direction="vertical" size="small">
          <Text type="secondary">
            من: {dayjs(record.start_date).format('DD/MM/YYYY')}
          </Text>
          <Text type="secondary">
            إلى: {dayjs(record.end_date).format('DD/MM/YYYY')}
          </Text>
        </Space>
      ),
      sorter: (a: FiscalPeriod, b: FiscalPeriod) =>
        dayjs(a.start_date).unix() - dayjs(b.start_date).unix(),
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: FiscalPeriod) => {
        const statusConfig = {
          'open': {
            color: 'green',
            text: 'مفتوحة',
            icon: <UnlockOutlined />
          },
          'closed': {
            color: 'orange',
            text: 'مقفلة',
            icon: <LockOutlined />
          },
          'locked': {
            color: 'red',
            text: 'مؤمنة',
            icon: <SafetyOutlined />
          }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        const getStatusStyle = (status: string) => {
          const styles = {
            open: {
              background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
              color: '#2e7d32',
              borderRadius: '20px',
              padding: '4px 12px',
              fontWeight: '500',
              border: 'none'
            },
            closed: {
              background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
              color: '#f57c00',
              borderRadius: '20px',
              padding: '4px 12px',
              fontWeight: '500',
              border: 'none'
            },
            locked: {
              background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
              color: '#d32f2f',
              borderRadius: '20px',
              padding: '4px 12px',
              fontWeight: '500',
              border: 'none'
            }
          };
          return styles[status] || {};
        };

        return (
          <Space>
            {config?.icon}
            <Tag style={getStatusStyle(status)}>
              {config?.text}
            </Tag>
            {record.closing_date && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {dayjs(record.closing_date).format('DD/MM/YYYY')}
              </Text>
            )}
          </Space>
        );
      },
      filters: [
        { text: 'مفتوحة', value: 'open' },
        { text: 'مقفلة', value: 'closed' },
        { text: 'مؤمنة', value: 'locked' }
      ],
      onFilter: (value: any, record: FiscalPeriod) => record.status === value,
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 250,
      render: (_: any, record: FiscalPeriod) => (
        <Space size="small" wrap>
          <Tooltip title="فحص البيانات">
            <Button
              type="text"
              icon={<CheckCircleOutlined />}
              onClick={() => handleValidatePeriod(record)}
              style={{ color: '#52c41a' }}
            />
          </Tooltip>

          {record.status === 'open' && (
            <>
              <Tooltip title="تعديل">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => handleEditPeriod(record)}
                />
              </Tooltip>

              <Tooltip title="بدء الإقفال">
                <Button
                  type="text"
                  icon={<LockOutlined />}
                  onClick={() => handleStartClosing(record)}
                  style={{ color: '#fa8c16' }}
                />
              </Tooltip>
            </>
          )}

          {record.status === 'closed' && (
            <Tooltip title="إعادة فتح">
              <Popconfirm
                title="هل أنت متأكد من إعادة فتح هذه الفترة؟"
                onConfirm={() => handleReopenPeriod(record)}
                okText="نعم"
                cancelText="لا"
              >
                <Button
                  type="text"
                  icon={<UnlockOutlined />}
                  style={{ color: '#1890ff' }}
                />
              </Popconfirm>
            </Tooltip>
          )}

          <Tooltip title="التقارير">
            <Button
              type="text"
              icon={<FileTextOutlined />}
              onClick={() => handleViewReports(record)}
              style={{ color: '#722ed1' }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <span>جاري التحميل...</span>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', minHeight: '100vh', background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)', direction: 'rtl' }}>
      {/* العنوان الرئيسي المحسن */}
      <Card style={{
        marginBottom: 24,
        borderRadius: '12px',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
        animation: 'fadeInUp 0.6s ease-out'
      }}>
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          padding: '20px',
          borderRadius: '12px 12px 0 0',
          marginBottom: '20px'
        }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space direction="vertical" size="small">
                <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                  <div style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '60px',
                    height: '60px',
                    borderRadius: '50%',
                    background: 'rgba(255,255,255,0.2)',
                    color: 'white',
                    fontSize: '24px'
                  }}>
                    <BankOutlined />
                  </div>
                  <div>
                    <Title level={2} style={{
                      margin: 0,
                      color: 'white',
                      background: 'linear-gradient(135deg, #fff 0%, #f0f0f0 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text'
                    }}>
                      🔒 نظام إقفال السنة المالية
                    </Title>
                    <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '1.1rem' }}>
                      إدارة شاملة ومتطورة للفترات المالية مع نظام إقفال آمن ومتقدم
                    </Text>
                  </div>
                </div>
              </Space>
            </Col>
            <Col>
              <Space size="large">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreatePeriod}
                  size="large"
                  style={{
                    background: 'white',
                    color: '#667eea',
                    border: 'none',
                    borderRadius: '8px',
                    fontWeight: '600',
                    boxShadow: '0 4px 15px rgba(255, 255, 255, 0.3)'
                  }}
                >
                  إنشاء فترة جديدة
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadPeriods}
                  size="large"
                  style={{
                    background: 'rgba(255,255,255,0.2)',
                    color: 'white',
                    border: '1px solid rgba(255,255,255,0.3)',
                    borderRadius: '8px'
                  }}
                >
                  تحديث
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      </Card>

      {/* رسائل الخطأ */}
      {error && (
        <Alert
          message={error}
          type="error"
          style={{ marginBottom: 16 }}
          closable
          onClose={() => setError(null)}
        />
      )}

      {/* التبويبات الرئيسية المحسنة */}
      <Card style={{
        borderRadius: '12px',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
        border: '1px solid #f0f0f0',
        animation: 'fadeInUp 0.6s ease-out'
      }}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          size="large"
          tabBarStyle={{
            marginBottom: 24,
            borderBottom: 'none'
          }}
        >
          <TabPane
            tab={
              <Space>
                <DashboardOutlined />
                <span>لوحة التحكم</span>
              </Space>
            }
            key="dashboard"
          >
            {renderDashboard()}
          </TabPane>

          <TabPane
            tab={
              <Space>
                <CalendarOutlined />
                <span>إدارة الفترات</span>
                <Badge count={periods.length} showZero />
              </Space>
            }
            key="periods"
          >
            <Table
              columns={columns}
              dataSource={periods}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} من ${total} فترة مالية`,
                style: { direction: 'rtl' }
              }}
              scroll={{ x: 1200 }}
              style={{
                direction: 'rtl',
                borderRadius: '12px',
                overflow: 'hidden',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <BarChartOutlined />
                <span>التقارير والإحصائيات</span>
              </Space>
            }
            key="reports"
          >
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Alert
                  message="قسم التقارير والإحصائيات"
                  description="هنا يمكنك عرض تقارير مفصلة عن جميع الفترات المالية والإحصائيات المتقدمة"
                  type="info"
                  showIcon
                />
              </Col>
            </Row>
          </TabPane>

          <TabPane
            tab={
              <Space>
                <SafetyOutlined />
                <span>الأمان والصلاحيات</span>
              </Space>
            }
            key="security"
          >
            <Card>
              <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
                <Col>
                  <Title level={4}>نظام الأمان والصلاحيات</Title>
                  <Text type="secondary">إدارة المستخدمين والصلاحيات مع نظام موافقات متقدم</Text>
                </Col>
                <Col>
                  <Button
                    type="primary"
                    icon={<SafetyOutlined />}
                    onClick={() => setSecurityDrawerVisible(true)}
                    size="large"
                  >
                    فتح نظام الأمان
                  </Button>
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Card>
                    <Statistic
                      title="المستخدمون النشطون"
                      value={12}
                      prefix={<UserOutlined style={{ color: '#52c41a' }} />}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card>
                    <Statistic
                      title="طلبات الموافقة المعلقة"
                      value={3}
                      prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card>
                    <Statistic
                      title="الصلاحيات المُعرَّفة"
                      value={15}
                      prefix={<LockOutlined style={{ color: '#1890ff' }} />}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
              </Row>
            </Card>
          </TabPane>

          <TabPane
            tab={
              <Space>
                <SettingOutlined />
                <span>الإعدادات</span>
              </Space>
            }
            key="settings"
          >
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Alert
                  message="إعدادات النظام"
                  description="هنا يمكنك تكوين إعدادات الإقفال والأمان والصلاحيات"
                  type="info"
                  showIcon
                />
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>

      <Modal
        title={editingPeriod ? 'تعديل الفترة المالية' : 'إضافة فترة مالية جديدة'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
        style={{
          borderRadius: '12px'
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSavePeriod}
        >
          <Form.Item
            name="period_name"
            label="اسم الفترة المالية"
            rules={[{ required: true, message: 'يرجى إدخال اسم الفترة المالية' }]}
          >
            <Input placeholder="مثال: السنة المالية 2024" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="start_date"
                label="تاريخ البداية"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ البداية' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="end_date"
                label="تاريخ النهاية"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ النهاية' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="ملاحظات"
          >
            <Input.TextArea rows={3} placeholder="ملاحظات إضافية (اختياري)" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingPeriod ? 'تحديث' : 'إضافة'}
              </Button>
              <Button onClick={() => {
                setIsModalVisible(false);
                form.resetFields();
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* درج التحقق من البيانات المحسن */}
      <Drawer
        title={
          <Space>
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
            <span>فحص صحة البيانات</span>
            {selectedPeriod && (
              <Tag color="blue">{selectedPeriod.period_name}</Tag>
            )}
          </Space>
        }
        width={800}
        open={validationDrawerVisible}
        onClose={() => setValidationDrawerVisible(false)}
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => selectedPeriod && handleValidatePeriod(selectedPeriod)}
            >
              إعادة الفحص
            </Button>
            <Button
              type="primary"
              icon={<LockOutlined />}
              onClick={() => {
                if (selectedPeriod) {
                  setValidationDrawerVisible(false);
                  handleStartClosing(selectedPeriod);
                }
              }}
            >
              بدء الإقفال
            </Button>
          </Space>
        }
      >
        {selectedPeriod && (
          <ValidationDashboard
            period={selectedPeriod}
            onValidationComplete={(canClose) => {
              console.log('Validation complete:', canClose);
            }}
          />
        )}
      </Drawer>

      {/* معالج الإقفال */}
      {selectedPeriod && (
        <ClosingWizard
          period={selectedPeriod}
          open={closingWizardVisible}
          onClose={() => setClosingWizardVisible(false)}
          onComplete={() => {
            setClosingWizardVisible(false);
            loadPeriods();
          }}
        />
      )}

      {/* درج التقارير المحسن */}
      <Drawer
        title={
          <Space>
            <FileTextOutlined style={{ color: '#722ed1' }} />
            <span>تقارير الإقفال</span>
            {selectedPeriod && (
              <Tag color="purple">{selectedPeriod.period_name}</Tag>
            )}
          </Space>
        }
        width={1000}
        open={reportsDrawerVisible}
        onClose={() => setReportsDrawerVisible(false)}
        extra={
          <Space>
            <Button icon={<PrinterOutlined />}>
              طباعة
            </Button>
            <Button icon={<DownloadOutlined />}>
              تصدير
            </Button>
          </Space>
        }
      >
        {selectedPeriod && (
          <ClosingReports period={selectedPeriod} />
        )}
      </Drawer>

      {/* درج الأمان والصلاحيات المحسن */}
      <Drawer
        title={
          <Space>
            <SafetyOutlined style={{ color: '#ff4d4f' }} />
            <span>نظام الأمان والصلاحيات</span>
          </Space>
        }
        width={1200}
        open={securityDrawerVisible}
        onClose={() => setSecurityDrawerVisible(false)}
        extra={
          <Space>
            <Button icon={<AuditOutlined />}>
              سجل الأمان
            </Button>
            <Button icon={<SettingOutlined />}>
              إعدادات متقدمة
            </Button>
          </Space>
        }
      >
        {selectedPeriod && (
          <SecurityManager period={selectedPeriod} />
        )}
      </Drawer>
    </div>
  );
};

export default FiscalPeriodManager;
