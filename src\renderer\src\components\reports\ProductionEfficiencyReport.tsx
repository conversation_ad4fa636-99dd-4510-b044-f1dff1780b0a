/**
 * تقرير كفاءة الإنتاج المحسن
 * تقرير شامل لكفاءة الإنتاج باستخدام النظام الموحد
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const ProductionEfficiencyReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'production_efficiency' as ReportType}
      title="تقرير كفاءة الإنتاج"
      description="تقرير مفصل لكفاءة الإنتاج مع تحليل الأداء والإنتاجية والجودة"
      showDateRange={true}
      showDepartmentFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="production_efficiency_report"
      defaultFilters={{
        sortBy: 'efficiency_score',
        sortOrder: 'desc'
      }}
    />
  )
}

export default ProductionEfficiencyReport
