import React, { useState, useEffect, useCallback } from 'react'
import { Form, Button, Typography, Space, App, Spin, Alert, Select } from 'antd'
import { UserOutlined, LockOutlined, SafetyOutlined, TeamOutlined, ReloadOutlined, CrownOutlined, SettingOutlined, ShopOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import { LoginResponse } from '../types/global'
import { logger as Logger } from './../utils/logger'
import AnimatedLogo from './common/AnimatedLogo'
import {
  AnimatedBackground,
  GlassCard,
  EnhancedButton,
  EnhancedPasswordInput,
  EnhancedForm
} from './common/StyledComponents'

const { Title, Text } = Typography





const LogoContainer = styled.div`
  text-align: center;
  margin-bottom: 32px;
`





interface LoginFormProps {
  onLogin: (user: any, token: string) => void
}

const LoginForm: React.FC<LoginFormProps> = ({ onLogin }) => {
  const { message } = App.useApp()
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()
  const [loginAttempts, setLoginAttempts] = useState(0)
  const [isBlocked, setIsBlocked] = useState(false)
  const [blockTimeLeft, setBlockTimeLeft] = useState(0)
  const [activeUsers, setActiveUsers] = useState<any[]>([])
  const [loadingUsers, setLoadingUsers] = useState(false)


  // جلب المستخدمين النشطين
  const loadActiveUsers = useCallback(async () => {
    try {
      setLoadingUsers(true)
      if (window.electronAPI) {
        const response = await window.electronAPI.getActiveUsers()
        if (response.success && response.data) {
          setActiveUsers(response.data)
          Logger.info('LoginForm', `تم جلب ${response.data.length} مستخدم نشط`)
        } else {
          Logger.error('LoginForm', 'فشل في جلب المستخدمين النشطين:', new Error(response.message || 'خطأ غير معروف'))
        }
      }
    } catch (error) {
      Logger.error('LoginForm', 'خطأ في جلب المستخدمين النشطين:', error as Error)
    } finally {
      setLoadingUsers(false)
    }
  }, [])

  const checkExistingSession = useCallback(async () => {
    try {
      const token = localStorage.getItem('authToken')
      if (token && window.electronAPI) {
        const response = await window.electronAPI.verifySession(token)
        if (response.success && (response as any).user) {
          onLogin((response as any).user, token)
        } else {
          localStorage.removeItem('authToken')
        }
      }
    } catch (error) {
      Logger.error('LoginForm', 'خطأ في فحص الجلسة:', error as Error)
      localStorage.removeItem('authToken')
    }
  }, [onLogin])

  // فحص الجلسة الموجودة عند تحميل المكون
  useEffect(() => {
    checkExistingSession()
    loadActiveUsers()
    // تعبئة البيانات الافتراضية
    form.setFieldsValue({
      username: 'admin',
      password: 'admin123'
    })
  }, [checkExistingSession, loadActiveUsers, form])



  // عداد الحّر
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isBlocked && blockTimeLeft > 0) {
      interval = setInterval(() => {
        setBlockTimeLeft(prev => {
          if (prev <= 1) {
            setIsBlocked(false)
            setLoginAttempts(0)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isBlocked, blockTimeLeft])

  const handleSubmit = async (values: { username: string; password: string }) => {
    if (isBlocked) {
      message.warning(`تم حّر تسجيل الدخول لمدة ${blockTimeLeft} ثانية`)
      return
    }

    setLoading(true)

    try {
      if (window.electronAPI) {
        Logger.info('LoginForm', '🔄 محاولة تسجيل الدخول...', values.username)
        const response: LoginResponse = await window.electronAPI.login(values.username, values.password)
        Logger.info('LoginForm', '📨 استجابة تسجيل الدخول:', response)

        if (response.success && response.user && response.token) {
          // حفّ الرمز المميز
          localStorage.setItem('authToken', response.token)

          // إعادة تعيين محاولات تسجيل الدخول
          setLoginAttempts(0)

          Logger.info('LoginForm', '✅ تسجيل دخول ناجح:', response.user.full_name)
          // message.success(`مرحباً ${response.user.full_name}`)
          onLogin(response.user, response.token)
        } else {
          // زيادة عدد المحاولات الفاشلة
          const newAttempts = loginAttempts + 1
          setLoginAttempts(newAttempts)

          if (newAttempts >= 5) {
            setIsBlocked(true)
            setBlockTimeLeft(300) // 5 دقائق
            message.error('تم حّر تسجيل الدخول لمدة 5 دقائق بسبب المحاولات المتكررة')
          } else {
            message.error(response.message || 'اسم المستخدم أو كلمة المرور غير صحيحة')
            message.warning(`المحاولة ${newAttempts} من 5`)
          }

          // مسح كلمة المرور
          form.setFieldsValue({ password: '' })
        }
      } else {
        // وضع التطوير - تسجيل دخول تجريبي
        if (values.username === 'admin' && values.password === 'admin123') {
          const mockToken = 'dev-token-' + Date.now()
          localStorage.setItem('authToken', mockToken)

          onLogin({
            id: 1,
            username: 'admin',
            full_name: 'المدير العام',
            role: 'admin'
          }, mockToken)
        } else {
          const newAttempts = loginAttempts + 1
          setLoginAttempts(newAttempts)

          if (newAttempts >= 5) {
            setIsBlocked(true)
            setBlockTimeLeft(300)
            message.error('تم حّر تسجيل الدخول لمدة 5 دقائق')
          } else {
            message.error('اسم المستخدم أو كلمة المرور غير صحيحة')
            message.warning(`المحاولة ${newAttempts} من 5`)
          }

          form.setFieldsValue({ password: '' })
        }
      }
    } catch (error) {
      message.error('حدث خطأ أثناء تسجيل الدخول')
      Logger.error('LoginForm', 'Login error:', error as Error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <AnimatedBackground style={{ height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '20px' }}>
      <GlassCard $animated style={{ width: '450px', maxWidth: '90vw' }}>
        <LogoContainer>
          <AnimatedLogo
            size="lg"
            showText={true}
            showSubtitle={true}
            showDeveloper={true}
            variant="full"
          />
        </LogoContainer>

        <Title level={3} style={{
          textAlign: 'center',
          marginBottom: 24,
          background: 'linear-gradient(135deg, #FF6B6B, #4ECDC4)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          fontSize: '28px',
          fontWeight: 'bold'
        }}>
          <SafetyOutlined style={{ marginLeft: 10, color: '#FF6B6B' }} />
          تسجيل الدخول
        </Title>

        {/* تحذير الحّر */}
        {isBlocked && (
          <Alert
            message="تم حّر تسجيل الدخول"
            description={`تم حّر تسجيل الدخول لمدة ${Math.floor(blockTimeLeft / 60)}:${(blockTimeLeft % 60).toString().padStart(2, '0')} بسبب المحاولات المتكررة`}
            type="error"
            showIcon
            style={{ marginBottom: 20 }}
          />
        )}

        {/* تحذير المحاولات */}
        {loginAttempts > 0 && loginAttempts < 5 && !isBlocked && (
          <Alert
            message={`تحذير: ${loginAttempts} محاولات فاشلة من أصل 5`}
            description="سيتم حّر تسجيل الدخول بعد 5 محاولات فاشلة"
            type="warning"
            showIcon
            style={{ marginBottom: 20 }}
          />
        )}

        <EnhancedForm
          form={form}
          name="login"
          onFinish={handleSubmit as any}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            initialValue="admin"
            rules={[
              { required: true, message: 'يرجى اختيار اسم المستخدم' }
            ]}
            extra={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '4px' }}>
                <Button
                  type="link"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={loadActiveUsers}
                  loading={loadingUsers}
                  style={{ padding: 0, height: 'auto', fontSize: '12px' }}
                >
                  تحديث القائمة
                </Button>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {activeUsers.length > 0 ? `${activeUsers.length} مستخدم متاح` : 'لا توجد مستخدمين'}
                </Text>
              </div>
            }
          >
            <Select
              placeholder="اختر اسم المستخدم"
              disabled={isBlocked || loadingUsers}
              loading={loadingUsers}
              showSearch
              size="large"
              filterOption={(input, option) => {
                const searchTerm = input.toLowerCase()
                const username = option?.value?.toString().toLowerCase() || ''

                // البحث في قائمة المستخدمين الأصلية للحصول على الاسم الكامل
                const user = activeUsers.find(u => u.username === option?.value)
                const fullName = user?.full_name?.toLowerCase() || ''
                const role = user?.role?.toLowerCase() || ''

                return username.includes(searchTerm) ||
                       fullName.includes(searchTerm) ||
                       role.includes(searchTerm)
              }}
              suffixIcon={<UserOutlined />}
              options={activeUsers.map(user => ({
                value: user.username,
                label: (
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', direction: 'rtl' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      {user.role === 'admin' ? (
                        <CrownOutlined style={{ color: '#ff4d4f' }} />
                      ) : user.role === 'manager' ? (
                        <SettingOutlined style={{ color: '#1890ff' }} />
                      ) : user.role === 'accountant' ? (
                        <TeamOutlined style={{ color: '#52c41a' }} />
                      ) : user.role === 'warehouse' ? (
                        <ShopOutlined style={{ color: '#fa8c16' }} />
                      ) : (
                        <UserOutlined style={{ color: '#666' }} />
                      )}
                      <span style={{ fontWeight: 'bold' }}>{user.full_name}</span>
                      <span style={{ color: '#666', fontSize: '12px' }}>({user.username})</span>
                    </div>
                    <span style={{
                      fontSize: '10px',
                      color: '#999',
                      backgroundColor: user.role === 'admin' ? '#fff2f0' : user.role === 'manager' ? '#f0f8ff' : '#f6ffed',
                      padding: '2px 6px',
                      borderRadius: '4px',
                      border: `1px solid ${user.role === 'admin' ? '#ffccc7' : user.role === 'manager' ? '#bae7ff' : '#d9f7be'}`
                    }}>
                      {user.role === 'admin' ? 'مدير' : user.role === 'manager' ? 'مشرف' : user.role === 'accountant' ? 'محاسب' : user.role === 'warehouse' ? 'مخزن' : 'مستخدم'}
                    </span>
                  </div>
                )
              }))}
              notFoundContent={
                loadingUsers ? (
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Spin size="small" />
                    <div style={{ marginTop: '8px' }}>جاري تحميل المستخدمين...</div>
                  </div>
                ) : (
                  <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                    <UserOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                    <div>لا توجد مستخدمين نشطين</div>
                    <Button
                      type="link"
                      size="small"
                      onClick={loadActiveUsers}
                      style={{ marginTop: '8px' }}
                    >
                      إعادة المحاولة
                    </Button>
                  </div>
                )
              }
            />
          </Form.Item>

          <Form.Item
            name="password"
            initialValue="admin123"
            rules={[
              { required: true, message: 'يرجى إدخال كلمة المرور' },
              { min: 6, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' }
            ]}
          >
            <EnhancedPasswordInput
              prefix={<LockOutlined style={{ color: '#FF6B6B' }} />}
              placeholder="كلمة المرور"
              autoComplete="current-password"
              disabled={isBlocked}
              maxLength={100}
              $size="lg"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0 }}>
            <EnhancedButton
              type="primary"
              htmlType="submit"
              loading={loading}
              disabled={isBlocked}
              block
              $variant="primary"
            >
              {loading ? (
                <Space>
                  <span>جاري التحقق</span>
                  <span style={{
                    animation: 'pulse 1.5s infinite',
                    display: 'inline-block',
                    fontSize: '20px'
                  }}>⚡</span>
                </Space>
              ) : isBlocked ? (
                `محّور (${Math.floor(blockTimeLeft / 60)}:${(blockTimeLeft % 60).toString().padStart(2, '0')})`
              ) : (
                <Space>
                  <SafetyOutlined style={{ fontSize: '20px' }} />
                  <span>تسجيل الدخول</span>
                </Space>
              )}
            </EnhancedButton>
          </Form.Item>
        </EnhancedForm>

        {/* معلومات إضافية */}
        <div style={{ marginTop: 24, textAlign: 'center' }}>
          <Space direction="vertical" size="small">
            <Alert
              message="بيانات تسجيل الدخول"
              description="اسم المستخدم الافتراضي: admin | كلمة المرور الافتراضية: admin123"
              type="info"
              showIcon
              style={{ marginBottom: 12, fontSize: 12 }}
            />
            <Text type="secondary" style={{ fontSize: 12 }}>
              <SafetyOutlined /> نّام آمن مع تشفير البيانات
            </Text>
          </Space>
        </div>
      </GlassCard>
    </AnimatedBackground>
  )
}

export default LoginForm
