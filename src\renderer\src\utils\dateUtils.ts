import dayjs from 'dayjs'
import 'dayjs/locale/en'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import { SafeLogger as Logger } from './logger'

// إعداد dayjs
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(customParseFormat)
dayjs.locale('en')

// تنسيقات التاريخ الميلادي
export const DATE_FORMATS = {
  DISPLAY_DATE: 'YYYY-MM-DD',
  DISPLAY_DATETIME: 'YYYY-MM-DD HH:mm',
  DISPLAY_TIME: 'HH:mm',
  ISO_DATE: 'YYYY-MM-DD',
  ISO_DATETIME: 'YYYY-MM-DDTHH:mm:ss',
  READABLE_DATE: 'DD/MM/YYYY',
  READABLE_DATETIME: 'DD/MM/YYYY HH:mm'
}

export class DateUtils {
  /**
   * تنسيق التاريخ للعرض بالتقويم الميلادي
   */
  static formatForDisplay(date: string | Date | dayjs.Dayjs, format: string = DATE_FORMATS.DISPLAY_DATE): string {
    if (!date) return ''

    try {
      const dayjsDate = dayjs(date)
      if (!dayjsDate.isValid()) return ''

      return dayjsDate.format(format)
    } catch (error) {
      Logger.error('DateUtils', 'خطأ في تنسيق التاريخ:', error)
      return ''
    }
  }

  /**
   * تنسيق التاريخ (اختصار لـ formatForDisplay)
   */
  static formatDate(date: string | Date | dayjs.Dayjs, format: string = DATE_FORMATS.DISPLAY_DATE): string {
    return DateUtils.formatForDisplay(date, format)
  }

  /**
   * تحويل التاريخ إلى تنسيق ISO
   */
  static toISOString(date: string | Date | dayjs.Dayjs): string {
    if (!date) return ''
    
    try {
      const dayjsDate = dayjs(date)
      if (!dayjsDate.isValid()) return ''
      
      return dayjsDate.toISOString()
    } catch (error) {
      Logger.error('DateUtils', 'خطأ في تحويل التاريخ إلى ISO:', error)
      return ''
    }
  }

  /**
   * الحصول على التاريخ الحالي
   */
  static now(): dayjs.Dayjs {
    return dayjs()
  }

  /**
   * الحصول على بداية اليوم
   */
  static startOfDay(date?: string | Date | dayjs.Dayjs): dayjs.Dayjs {
    return dayjs(date).startOf('day')
  }

  /**
   * الحصول على نهاية اليوم
   */
  static endOfDay(date?: string | Date | dayjs.Dayjs): dayjs.Dayjs {
    return dayjs(date).endOf('day')
  }

  /**
   * إضافة فترة زمنية للتاريخ
   */
  static add(date: string | Date | dayjs.Dayjs, amount: number, unit: dayjs.ManipulateType): dayjs.Dayjs {
    return dayjs(date).add(amount, unit)
  }

  /**
   * طرح فترة زمنية من التاريخ
   */
  static subtract(date: string | Date | dayjs.Dayjs, amount: number, unit: dayjs.ManipulateType): dayjs.Dayjs {
    return dayjs(date).subtract(amount, unit)
  }

  /**
   * مقارنة تاريخين
   */
  static isBefore(date1: string | Date | dayjs.Dayjs, date2: string | Date | dayjs.Dayjs): boolean {
    return dayjs(date1).isBefore(dayjs(date2))
  }

  /**
   * مقارنة تاريخين
   */
  static isAfter(date1: string | Date | dayjs.Dayjs, date2: string | Date | dayjs.Dayjs): boolean {
    return dayjs(date1).isAfter(dayjs(date2))
  }

  /**
   * التحقق من صحة التاريخ
   */
  static isValid(date: string | Date | dayjs.Dayjs): boolean {
    return dayjs(date).isValid()
  }

  /**
   * حساب الفرق بين تاريخين
   */
  static diff(date1: string | Date | dayjs.Dayjs, date2: string | Date | dayjs.Dayjs, unit: dayjs.QUnitType = 'day'): number {
    return dayjs(date1).diff(dayjs(date2), unit)
  }

  /**
   * تنسيق التاريخ للقراءة السهلة
   */
  static formatReadable(date: string | Date | dayjs.Dayjs): string {
    return DateUtils.formatForDisplay(date, DATE_FORMATS.READABLE_DATE)
  }

  /**
   * تنسيق التاريخ والوقت للقراءة السهلة
   */
  static formatReadableDateTime(date: string | Date | dayjs.Dayjs): string {
    return DateUtils.formatForDisplay(date, DATE_FORMATS.READABLE_DATETIME)
  }

  /**
   * الحصول على اسم الشهر بالعربية
   */
  static getMonthName(date: string | Date | dayjs.Dayjs): string {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ]
    
    const monthIndex = dayjs(date).month()
    return months[monthIndex] || ''
  }

  /**
   * الحصول على اسم اليوم بالعربية
   */
  static getDayName(date: string | Date | dayjs.Dayjs): string {
    const days = [
      'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'
    ]
    
    const dayIndex = dayjs(date).day()
    return days[dayIndex] || ''
  }

  /**
   * تحويل التاريخ إلى نص وصفي
   */
  static toRelativeTime(date: string | Date | dayjs.Dayjs): string {
    const now = dayjs()
    const targetDate = dayjs(date)
    const diffDays = now.diff(targetDate, 'day')

    if (diffDays === 0) {
      return 'اليوم'
    } else if (diffDays === 1) {
      return 'أمس'
    } else if (diffDays === -1) {
      return 'غداً'
    } else if (diffDays > 1 && diffDays <= 7) {
      return `منذ ${diffDays} أيام`
    } else if (diffDays < -1 && diffDays >= -7) {
      return `خلال ${Math.abs(diffDays)} أيام`
    } else {
      return DateUtils.formatReadable(date)
    }
  }

  /**
   * الحصول على نطاق تاريخ (من - إلى)
   */
  static getDateRange(startDate: string | Date | dayjs.Dayjs, endDate: string | Date | dayjs.Dayjs): string {
    const start = DateUtils.formatReadable(startDate)
    const end = DateUtils.formatReadable(endDate)
    return `${start} - ${end}`
  }

  /**
   * التحقق من أن التاريخ في المستقبل
   */
  static isFuture(date: string | Date | dayjs.Dayjs): boolean {
    return dayjs(date).isAfter(dayjs())
  }

  /**
   * التحقق من أن التاريخ في الماضي
   */
  static isPast(date: string | Date | dayjs.Dayjs): boolean {
    return dayjs(date).isBefore(dayjs())
  }

  /**
   * الحصول على بداية الشهر
   */
  static startOfMonth(date?: string | Date | dayjs.Dayjs): dayjs.Dayjs {
    return dayjs(date).startOf('month')
  }

  /**
   * الحصول على نهاية الشهر
   */
  static endOfMonth(date?: string | Date | dayjs.Dayjs): dayjs.Dayjs {
    return dayjs(date).endOf('month')
  }

  /**
   * الحصول على بداية السنة
   */
  static startOfYear(date?: string | Date | dayjs.Dayjs): dayjs.Dayjs {
    return dayjs(date).startOf('year')
  }

  /**
   * الحصول على نهاية السنة
   */
  static endOfYear(date?: string | Date | dayjs.Dayjs): dayjs.Dayjs {
    return dayjs(date).endOf('year')
  }
}

export default DateUtils
