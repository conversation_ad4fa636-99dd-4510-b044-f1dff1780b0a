import React from 'react';
import { Tag, Typography, Progress } from 'antd';
import UniversalReport from './UniversalReport';
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger';
import type { ReportData, ReportType } from '../../types/reports';

const { Text } = Typography;

const PurchasesBySupplierReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير المشتريات حسب المورد...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getPurchasesBySupplierReport(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const purchasesBySupplierData = response.data;

      // معالجة البيانات
      const processedData = purchasesBySupplierData.map((item: any, index: number) => ({
        ...item,
        key: item.supplier_id || index,
        average_invoice: item.total_amount / (item.invoice_count || 1),
        payment_percentage: item.total_amount > 0 ? (item.paid_amount / item.total_amount) * 100 : 0,
        outstanding_amount: item.total_amount - item.paid_amount
      }));

      // حساب الإحصائيات
      const totalSuppliers = processedData.length;
      const totalInvoices = processedData.reduce((sum: number, item: any) => sum + item.invoice_count, 0);
      const totalAmount = processedData.reduce((sum: number, item: any) => sum + item.total_amount, 0);
      const totalPaid = processedData.reduce((sum: number, item: any) => sum + item.paid_amount, 0);
      const totalOutstanding = totalAmount - totalPaid;
      const avgInvoiceValue = totalInvoices > 0 ? totalAmount / totalInvoices : 0;
      const overallPaymentPercentage = totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0;

      const topSuppliers = [...processedData]
        .sort((a: any, b: any) => b.total_amount - a.total_amount)
        .slice(0, 5)
        .map((item: any) => item.supplier_name);

      const highOutstandingSuppliers = processedData
        .filter((item: any) => item.outstanding_amount > 0)
        .sort((a: any, b: any) => b.outstanding_amount - a.outstanding_amount)
        .slice(0, 5)
        .map((item: any) => item.supplier_name);

      const summary = {
        totalSuppliers,
        totalInvoices,
        totalAmount: Math.round(totalAmount * 100) / 100,
        totalPaid: Math.round(totalPaid * 100) / 100,
        totalOutstanding: Math.round(totalOutstanding * 100) / 100,
        avgInvoiceValue: Math.round(avgInvoiceValue * 100) / 100,
        overallPaymentPercentage: Math.round(overallPaymentPercentage * 100) / 100,
        topSuppliers,
        highOutstandingSuppliers
      };

      console.log('✅ تم إنشاء تقرير المشتريات حسب المورد بنجاح');

      return {
        title: 'تقرير المشتريات حسب المورد',
        data: processedData,
        columns,
        summary,
        metadata: {
          reportType: 'purchases_by_supplier' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: processedData.length
        }
      };
    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير المشتريات حسب المورد:', error);
      throw error;
    }
  };

  // إعداد الأعمدة
  const columns = [
    {
      title: 'كود المورد',
      dataIndex: 'supplier_code',
      key: 'supplier_code',
      width: 120,
      render: (code: string) => (
        <Text strong style={{ color: '#1890ff' }}>{code}</Text>
      )
    },
    {
      title: 'اسم المورد',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      width: 200,
      render: (name: string) => (
        <Text strong>{name}</Text>
      )
    },
    {
      title: 'عدد الفواتير',
      dataIndex: 'invoice_count',
      key: 'invoice_count',
      width: 120,
      align: 'center' as const,
      render: (count: number) => (
        <Tag color="blue">{count}</Tag>
      )
    },
    {
      title: 'إجمالي المبلغ (₪)',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 150,
      align: 'right' as const,
      render: (amount: number) => (
        <Text strong style={{ color: '#52c41a' }}>
          {amount.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'متوسط الفاتورة (₪)',
      dataIndex: 'average_invoice',
      key: 'average_invoice',
      width: 150,
      align: 'right' as const,
      render: (avg: number) => (
        <Text>{avg.toLocaleString()}</Text>
      )
    },
    {
      title: 'المبلغ المدفوع (₪)',
      dataIndex: 'paid_amount',
      key: 'paid_amount',
      width: 150,
      align: 'right' as const,
      render: (paid: number) => (
        <Text style={{ color: '#52c41a' }}>
          {paid.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'المبلغ المستحق (₪)',
      dataIndex: 'outstanding_amount',
      key: 'outstanding_amount',
      width: 150,
      align: 'right' as const,
      render: (outstanding: number) => (
        <Text style={{ color: outstanding > 0 ? '#ff4d4f' : '#52c41a' }}>
          {outstanding.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'نسبة السداد',
      dataIndex: 'payment_percentage',
      key: 'payment_percentage',
      width: 150,
      align: 'center' as const,
      render: (percentage: number) => (
        <Progress
          percent={Math.round(percentage)}
          size="small"
          strokeColor={percentage >= 80 ? '#52c41a' : percentage >= 50 ? '#fa8c16' : '#ff4d4f'}
        />
      )
    },
    {
      title: 'آخر فاتورة (ميلادي)',
      dataIndex: 'last_invoice_date',
      key: 'last_invoice_date',
      width: 140,
      render: (date: string) => date ? DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE) : '-'
    }
  ];

  return (
    <UniversalReport
      reportType={'purchases_by_supplier' as ReportType}
      title="تقرير المشتريات حسب المورد"
      description="تقرير تفصيلي للمشتريات مجمعة حسب المورد مع الإحصائيات"
      onGenerateReport={generateReport}
      showDateRange={true}
      showSupplierFilter={true}
      showAmountRangeFilter={true}
      showPrintOptions={true}
      showExportOptions={true}
      defaultFilters={{
        dateRange: null, // كل المدة افتراضياً
        sortBy: 'total_amount',
        sortOrder: 'desc'
      }}
    />
  );

};

export default PurchasesBySupplierReport;
