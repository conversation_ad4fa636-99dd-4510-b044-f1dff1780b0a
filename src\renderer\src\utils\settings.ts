import { SafeLogger as Logger } from './logger'
// نّام إعدادات التطبيق

export interface AppSettings {
  // إعدادات الضرائب
  taxRate: number // معدل الضريبة (كنسبة مئوية)
  taxEnabled: boolean // تفعيل الضريبة
  
  // إعدادات العملة
  currency: string
  currencySymbol: string
  
  // إعدادات الفواتير
  invoicePrefix: string
  orderPrefix: string
  
  // إعدادات عامة
  companyName: string
  companyAddress: string
  companyPhone: string
  companyEmail: string
}

// الإعدادات الافتراضية
const defaultSettings: AppSettings = {
  taxRate: 16, // 16% ضريبة القيمة المضافة
  taxEnabled: true,
  currency: 'ILS',
  currencySymbol: '₪',
  invoicePrefix: 'SAL',
  orderPrefix: 'SO',
  companyName: 'ZET.IA',
  companyAddress: '',
  companyPhone: '',
  companyEmail: ''
}

class SettingsManager {
  private static instance: SettingsManager
  private settings: AppSettings
  private storageKey = 'app_settings'

  private constructor() {
    this.settings = this.loadSettings()
  }

  public static getInstance(): SettingsManager {
    if (!SettingsManager.instance) {
      SettingsManager.instance = new SettingsManager()
    }
    return SettingsManager.instance
  }

  private loadSettings(): AppSettings {
    try {
      // محاولة تحميل الإعدادات من قاعدة البيانات أولاً
      if (window.electronAPI) {
        this.loadFromDatabase()
      } else {
        // في وضع التطوير، استخدم localStorage
        const stored = localStorage.getItem(this.storageKey)
        if (stored) {
          const parsed = JSON.parse(stored)
          // دمج الإعدادات المحفوّة مع الافتراضية لضمان وجود جميع الخصائص
          return { ...defaultSettings, ...parsed }
        }
      }
    } catch (error) {
      Logger.error('Settings', 'خطأ في تحميل الإعدادات:', error instanceof Error ? error : Object.assign(new Error(String(error)), error && typeof error === 'object' ? error : {}))
    }
    return { ...defaultSettings }
  }

  // تحميل الإعدادات من قاعدة البيانات
  private async loadFromDatabase(): Promise<void> {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getSettings()
        if (response.success && Array.isArray(response.data)) {
          const settingsMap: any = {}
          response.data.forEach((setting: any) => {
            settingsMap[setting.key] = setting.value
          })

          // تحديث الإعدادات من قاعدة البيانات
          this.settings = {
            taxRate: parseFloat(settingsMap.tax_rate || '16'),
            taxEnabled: settingsMap.tax_enabled === 'true',
            currency: settingsMap.currency || 'ILS',
            currencySymbol: settingsMap.currency_symbol || '₪',
            invoicePrefix: settingsMap.invoice_prefix || 'SAL',
            orderPrefix: settingsMap.order_prefix || 'SO',
            companyName: settingsMap.company_name || 'ZET.IA',
            companyAddress: settingsMap.company_address || '',
            companyPhone: settingsMap.company_phone || '',
            companyEmail: settingsMap.company_email || ''
          }

          Logger.info('Settings', '✅ تم تحميل الإعدادات من قاعدة البيانات:', this.settings)
        }
      }
    } catch (error) {
      Logger.error('Settings', '❌ خطأ في تحميل الإعدادات من قاعدة البيانات:', error instanceof Error ? error : Object.assign(new Error(String(error)), error && typeof error === 'object' ? error : {}))
    }
  }

  private saveSettings(): void {
    try {
      // حفّ في قاعدة البيانات إذا كانت متاحة
      if (window.electronAPI) {
        this.saveToDatabase()
      } else {
        // في وضع التطوير، استخدم localStorage
        localStorage.setItem(this.storageKey, JSON.stringify(this.settings))
      }
    } catch (error) {
      Logger.error('Settings', 'خطأ في حفّ الإعدادات:', error instanceof Error ? error : Object.assign(new Error(String(error)), error && typeof error === 'object' ? error : {}))
    }
  }

  // حفّ الإعدادات في قاعدة البيانات
  private async saveToDatabase(): Promise<void> {
    try {
      if (window.electronAPI) {
        const settingsToSave = {
          tax_rate: this.settings.taxRate.toString(),
          tax_enabled: this.settings.taxEnabled.toString(),
          currency: this.settings.currency,
          currency_symbol: this.settings.currencySymbol,
          invoice_prefix: this.settings.invoicePrefix,
          order_prefix: this.settings.orderPrefix,
          company_name: this.settings.companyName,
          company_address: this.settings.companyAddress,
          company_phone: this.settings.companyPhone,
          company_email: this.settings.companyEmail
        }

        const result = await window.electronAPI.updateSettings(settingsToSave)
        if (result.success) {
          Logger.info('Settings', '✅ تم حفّ الإعدادات في قاعدة البيانات')
        } else {
          Logger.error('Settings', '❌ فشل في حفّ الإعدادات:', new Error(String(result.message)))
        }
      }
    } catch (error) {
      Logger.error('Settings', '❌ خطأ في حفّ الإعدادات في قاعدة البيانات:', error instanceof Error ? error : Object.assign(new Error(String(error)), error && typeof error === 'object' ? error : {}))
    }
  }

  public getSettings(): AppSettings {
    return { ...this.settings }
  }

  public getSetting<K extends keyof AppSettings>(key: K): AppSettings[K] {
    return this.settings[key]
  }

  // تحقق من صحة القيم
  private validateSetting<K extends keyof AppSettings>(key: K, value: AppSettings[K]): string | null {
    if (key === 'taxRate' && (typeof value !== 'number' || value < 0 || value > 100)) {
      return 'معدل الضريبة يجب أن يكون رقمًا بين 0 و100.'
    }
    if (key === 'currencySymbol' && (typeof value !== 'string' || value.trim() === '')) {
      return 'رمز العملة لا يمكن أن يكون فارغًا.'
    }
    if (key === 'companyEmail' && value && typeof value === 'string' && !/^\S+@\S+\.\S+$/.test(value)) {
      return 'البريد الإلكتروني غير صالح.'
    }
    return null
  }

  public setSetting<K extends keyof AppSettings>(key: K, value: AppSettings[K], userRole?: string): void {
    if (userRole && userRole !== 'admin' && userRole !== 'settings_manager') {
      throw new Error('ليس لديك صلاحية تعديل الإعدادات.')
    }
    const validationError = this.validateSetting(key, value)
    if (validationError) {
      throw new Error(validationError)
    }
    this.settings[key] = value
    this.saveSettings()
  }

  public updateSettings(updates: Partial<AppSettings>, userRole?: string): void {
    if (userRole && userRole !== 'admin' && userRole !== 'settings_manager') {
      throw new Error('ليس لديك صلاحية تعديل الإعدادات.')
    }
    for (const key in updates) {
      const validationError = this.validateSetting(key as keyof AppSettings, updates[key as keyof AppSettings] as any)
      if (validationError) {
        throw new Error(validationError)
      }
    }
    this.settings = { ...this.settings, ...updates }
    this.saveSettings()
  }

  public resetSettings(userRole?: string): void {
    if (userRole && userRole !== 'admin' && userRole !== 'settings_manager') {
      throw new Error('ليس لديك صلاحية إعادة تعيين الإعدادات.')
    }
    this.settings = { ...defaultSettings }
    this.saveSettings()
  }

  // إعادة تحميل الإعدادات من قاعدة البيانات
  public async reloadFromDatabase(): Promise<void> {
    await this.loadFromDatabase()
  }

  // تحديث إعداد واحد وحفّه في قاعدة البيانات مع تحقق من الصلاحية
  public async updateSettingInDatabase<K extends keyof AppSettings>(key: K, value: AppSettings[K], userRole?: string): Promise<boolean> {
    try {
      if (userRole && userRole !== 'admin' && userRole !== 'settings_manager') {
        throw new Error('ليس لديك صلاحية تعديل الإ��دادات.')
      }
      const validationError = this.validateSetting(key, value)
      if (validationError) {
        throw new Error(validationError)
      }
      this.settings[key] = value
      await this.saveToDatabase()
      return true
    } catch (error) {
      Logger.error('Settings', 'خطأ في تحدي�� الإعداد:', error instanceof Error ? error : Object.assign(new Error(String(error)), error && typeof error === 'object' ? error : {}))
      return false
    }
  }

  // دوال مساعدة للإعدادات الشائعة
  public getTaxRate(): number {
    return this.settings.taxRate / 100 // تحويل إلى عدد عشري
  }

  public isTaxEnabled(): boolean {
    return this.settings.taxEnabled
  }

  public getCurrencySymbol(): string {
    return this.settings.currencySymbol
  }

  public formatCurrency(amount: number): string {
    return `${this.settings.currencySymbol}${amount.toLocaleString()}`
  }

  public calculateTax(amount: number): number {
    if (!this.settings.taxEnabled) {
      return 0
    }
    return amount * this.getTaxRate()
  }

  public calculateTotal(subtotal: number, discount: number = 0): number {
    const afterDiscount = subtotal - discount
    const tax = this.calculateTax(afterDiscount)
    return afterDiscount + tax
  }
}

// إنشاء مثيل واحد للاستخدام العام
export const settingsManager = SettingsManager.getInstance()

// دوال مساعدة للاستخدام السريع
export const getTaxRate = () => settingsManager.getTaxRate()
export const isTaxEnabled = () => settingsManager.isTaxEnabled()
export const getCurrencySymbol = () => settingsManager.getCurrencySymbol()
export const formatCurrency = (amount: number) => settingsManager.formatCurrency(amount)
export const calculateTax = (amount: number) => settingsManager.calculateTax(amount)
export const calculateTotal = (subtotal: number, discount: number = 0) =>
  settingsManager.calculateTotal(subtotal, discount)

// دوال لإعادة تحميل الإعدادات
export const reloadSettingsFromDatabase = () => settingsManager.reloadFromDatabase()
export const updateSettingInDatabase = <K extends keyof AppSettings>(key: K, value: AppSettings[K]) =>
  settingsManager.updateSettingInDatabase(key, value)

// دالة لحساب المجاميع مع التفاصيل
export const calculateInvoiceTotals = (subtotal: number, discountAmount: number = 0) => {
  const afterDiscount = subtotal - discountAmount
  const taxAmount = calculateTax(afterDiscount)
  const totalAmount = afterDiscount + taxAmount

  return {
    subtotal: Math.round(subtotal * 100) / 100,
    discountAmount: Math.round(discountAmount * 100) / 100,
    taxAmount: Math.round(taxAmount * 100) / 100,
    totalAmount: Math.round(totalAmount * 100) / 100,
    taxRate: settingsManager.getSetting('taxRate')
  }
}

// دالة لحساب مجموع الأصناف
export const calculateItemsSubtotal = (items: any[]) => {
  if (!Array.isArray(items)) {
    return 0
  }

  return items.reduce((sum, item) => {
    const quantity = Number(item.quantity) || 0
    const unitPrice = Number(item.unit_price) || 0
    return sum + (quantity * unitPrice)
  }, 0)
}

// دالة للتحقق من صحة البيانات المالية
export const validateInvoiceFinancials = (data: {
  subtotal: number
  discountAmount?: number
  taxAmount?: number
  totalAmount: number
  paidAmount?: number
}) => {
  const errors: string[] = []

  // التحقق من القيم الأساسية
  if (data.subtotal < 0) {
    errors.push('المجموع الفرعي لا يمكن أن يكون سالباً')
  }

  if (data.discountAmount && data.discountAmount < 0) {
    errors.push('مبلغ الخصم لا يمكن أن يكون سالباً')
  }

  if (data.discountAmount && data.discountAmount > data.subtotal) {
    errors.push('مبلغ الخصم لا يمكن أن يكون أكبر من المجموع الفرعي')
  }

  if (data.taxAmount && data.taxAmount < 0) {
    errors.push('مبلغ الضريبة لا يمكن أن يكون سالباً')
  }

  if (data.totalAmount < 0) {
    errors.push('المجموع الإجمالي لا يمكن أن يكون سالباً')
  }

  if (data.paidAmount && data.paidAmount < 0) {
    errors.push('المبلغ المدفوع لا يمكن أن يكون سالباً')
  }

  if (data.paidAmount && data.paidAmount > data.totalAmount) {
    errors.push('المبلغ المدفوع لا يمكن أن يكون أكبر من المجموع الإجمالي')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
