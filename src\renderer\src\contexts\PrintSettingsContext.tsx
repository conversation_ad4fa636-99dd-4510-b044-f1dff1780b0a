import React, { createContext, useContext, useEffect, useState } from 'react'
import { CentralizedPrintSettings } from '../types/print'
import { MasterPrintService } from '../services/MasterPrintService'
import { Logger } from '../utils/logger'

// نوع السياق
interface PrintSettingsContextType {
  settings: CentralizedPrintSettings | null
  loading: boolean
  error: string | null
  updateSettings: (newSettings: Partial<CentralizedPrintSettings>) => Promise<void>
  resetSettings: () => Promise<void>
  refreshSettings: () => Promise<void>
  isReady: boolean
}

// إنشاء السياق
export const PrintSettingsContext = createContext<PrintSettingsContextType | undefined>(undefined)

// Hook للوصول للسياق
export const usePrintSettingsContext = () => {
  const context = useContext(PrintSettingsContext)
  if (context === undefined) {
    throw new Error('usePrintSettingsContext must be used within a PrintSettingsProvider')
  }
  return context
}

// مزود السياق
interface PrintSettingsProviderProps {
  children: React.ReactNode
}

export const PrintSettingsProvider: React.FC<PrintSettingsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<CentralizedPrintSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isReady, setIsReady] = useState(false)

  const printService = MasterPrintService.getInstance()

  // تحميل الإعدادات من قاعدة البيانات
  const loadSettings = async () => {
    try {
      setLoading(true)
      setError(null)

      // الحصول على الإعدادات من قاعدة البيانات
      const savedSettings = await window.electronAPI?.invoke('get-print-settings')
      
      if (savedSettings) {
        const centralizedSettings: CentralizedPrintSettings = {
          ...printService.getDefaultOptions(),
          ...savedSettings,
          autoSync: true,
          lastUpdated: new Date().toISOString(),
          version: '1.0.0'
        }
        
        setSettings(centralizedSettings)
        
        // تطبيق الإعدادات على خدمة الطباعة
        printService.updateSettings(centralizedSettings)
        
        // تطبيق الألوان على متغيرات CSS
        applyColorsToCSS(centralizedSettings)
        
        // إشعار جميع المكونات بالتحديث
        broadcastSettingsUpdate(centralizedSettings)
        
        Logger.info('PrintSettingsProvider', 'تم تحميل الإعدادات المركزية:', centralizedSettings)
      } else {
        // استخدام الإعدادات الافتراضية
        const defaultSettings: CentralizedPrintSettings = {
          ...printService.getDefaultOptions(),
          autoSync: true,
          lastUpdated: new Date().toISOString(),
          version: '1.0.0'
        }
        
        setSettings(defaultSettings)
        await saveSettings(defaultSettings)
      }
      
      setIsReady(true)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'خطأ في تحميل إعدادات الطباعة'
      setError(errorMessage)
      Logger.error('PrintSettingsProvider', 'خطأ في تحميل الإعدادات:', err)
    } finally {
      setLoading(false)
    }
  }

  // حفظ الإعدادات في قاعدة البيانات
  const saveSettings = async (newSettings: CentralizedPrintSettings) => {
    try {
      await window.electronAPI?.invoke('save-print-settings', newSettings)
      Logger.info('PrintSettingsProvider', 'تم حفظ الإعدادات:', newSettings)
    } catch (err) {
      Logger.error('PrintSettingsProvider', 'خطأ في حفظ الإعدادات:', err)
      throw err
    }
  }

  // تطبيق الألوان على متغيرات CSS
  const applyColorsToCSS = (settings: CentralizedPrintSettings) => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement
      
      if (settings.primaryColor) {
        root.style.setProperty('--print-primary-color', settings.primaryColor)
      }
      if (settings.secondaryColor) {
        root.style.setProperty('--print-secondary-color', settings.secondaryColor)
      }
      if (settings.borderColor) {
        root.style.setProperty('--print-border-color', settings.borderColor)
      }
      if (settings.backgroundColor) {
        root.style.setProperty('--print-background-color', settings.backgroundColor)
      }
      if (settings.textColor) {
        root.style.setProperty('--print-text-color', settings.textColor)
      }
      
      Logger.info('PrintSettingsProvider', 'تم تطبيق الألوان على CSS')
    }
  }

  // إشعار جميع المكونات بالتحديث
  const broadcastSettingsUpdate = (newSettings: CentralizedPrintSettings) => {
    // إرسال حدث مخصص لإشعار جميع المكونات
    const event = new CustomEvent('printSettingsUpdated', {
      detail: newSettings
    })
    window.dispatchEvent(event)
  }

  // تحديث الإعدادات
  const updateSettings = async (newSettings: Partial<CentralizedPrintSettings>) => {
    if (!settings) return

    try {
      setLoading(true)
      setError(null)

      const updatedSettings: CentralizedPrintSettings = {
        ...settings,
        ...newSettings,
        lastUpdated: new Date().toISOString()
      }

      setSettings(updatedSettings)
      
      // حفظ في قاعدة البيانات
      await saveSettings(updatedSettings)
      
      // تطبيق على خدمة الطباعة
      printService.updateSettings(updatedSettings)
      
      // تطبيق الألوان على CSS
      applyColorsToCSS(updatedSettings)
      
      // إشعار جميع المكونات
      broadcastSettingsUpdate(updatedSettings)
      
      Logger.info('PrintSettingsProvider', 'تم تحديث الإعدادات:', updatedSettings)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'خطأ في تحديث الإعدادات'
      setError(errorMessage)
      Logger.error('PrintSettingsProvider', 'خطأ في تحديث الإعدادات:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // إعادة تعيين الإعدادات للافتراضية
  const resetSettings = async () => {
    try {
      setLoading(true)
      setError(null)

      const defaultSettings: CentralizedPrintSettings = {
        ...printService.getDefaultOptions(),
        autoSync: true,
        lastUpdated: new Date().toISOString(),
        version: '1.0.0'
      }

      setSettings(defaultSettings)
      await saveSettings(defaultSettings)
      printService.updateSettings(defaultSettings)
      applyColorsToCSS(defaultSettings)
      broadcastSettingsUpdate(defaultSettings)
      
      Logger.info('PrintSettingsProvider', 'تم إعادة تعيين الإعدادات للافتراضية')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'خطأ في إعادة تعيين الإعدادات'
      setError(errorMessage)
      Logger.error('PrintSettingsProvider', 'خطأ في إعادة تعيين الإعدادات:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // تحديث الإعدادات من المصدر
  const refreshSettings = async () => {
    await loadSettings()
  }

  // تحميل الإعدادات عند بدء التشغيل
  useEffect(() => {
    loadSettings()
  }, [])

  const value: PrintSettingsContextType = {
    settings,
    loading,
    error,
    updateSettings,
    resetSettings,
    refreshSettings,
    isReady
  }

  return (
    <PrintSettingsContext.Provider value={value}>
      {children}
    </PrintSettingsContext.Provider>
  )
}

// Hook مبسط للاستماع لتحديثات الإعدادات
export const usePrintSettingsListener = () => {
  const [settings, setSettings] = useState<CentralizedPrintSettings | null>(null)

  useEffect(() => {
    const handleSettingsUpdate = (event: CustomEvent<CentralizedPrintSettings>) => {
      setSettings(event.detail)
    }

    window.addEventListener('printSettingsUpdated', handleSettingsUpdate as EventListener)
    
    return () => {
      window.removeEventListener('printSettingsUpdated', handleSettingsUpdate as EventListener)
    }
  }, [])

  return settings
}
