import { ipcMain } from 'electron'
import { UserService, AuthService } from '../services'
import { Logger } from '../utils/logger'

let userService: UserService

export function setUserService(service: UserService) {
  userService = service
}

export function registerUserHandlers(): void {
  // الحصول على جميع المستخدمين
  ipcMain.handle('get-users', async () => {
    try {
      return await userService.getUsers()
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في جلب المستخدمين:', error)
      return { success: false, message: 'حدث خطأ في جلب المستخدمين' }
    }
  })

  // الحصول على المستخدمين النشطين فقط
  ipcMain.handle('get-active-users', async () => {
    try {
      return await userService.getActiveUsers()
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في جلب المستخدمين النشطين:', error)
      return { success: false, message: 'حدث خطأ في جلب المستخدمين النشطين' }
    }
  })

  // إنشاء مستخدم جديد
  ipcMain.handle('create-user', async (event, userData: any) => {
    try {
      return await userService.createUser(userData)
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في إنشاء المستخدم:', error)
      return { success: false, message: 'حدث خطأ في إنشاء المستخدم' }
    }
  })

  // تحديث مستخدم
  ipcMain.handle('update-user', async (event, userId: number, userData: any) => {
    try {
      // الحصول على دور المستخدم الحالي
      const userRole = AuthService.getCurrentUserRole()

      return await userService.updateUser(userId, userData, userRole || undefined)
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في تحديث المستخدم:', error)
      return { success: false, message: 'حدث خطأ في تحديث المستخدم' }
    }
  })

  // حذف مستخدم
  ipcMain.handle('delete-user', async (event, userId: number) => {
    try {
      // الحصول على دور المستخدم الحالي
      const userRole = AuthService.getCurrentUserRole()

      return await userService.deleteUser(userId, userRole || undefined)
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في حذف المستخدم:', error)
      return { success: false, message: 'حدث خطأ في حذف المستخدم' }
    }
  })

  // تبديل حالة المستخدم
  ipcMain.handle('toggle-user-status', async (event, userId: number, isActive: boolean) => {
    try {
      // الحصول على دور المستخدم الحالي
      const userRole = AuthService.getCurrentUserRole()

      return await userService.toggleUserStatus(userId, isActive, userRole || undefined)
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في تبديل حالة المستخدم:', error)
      return { success: false, message: 'حدث خطأ في تبديل حالة المستخدم' }
    }
  })

  // إعادة تعيين كلمة المرور
  ipcMain.handle('reset-user-password', async (event, userId: number) => {
    try {
      return await userService.resetUserPassword(userId)
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في إعادة تعيين كلمة المرور:', error)
      return { success: false, message: 'حدث خطأ في إعادة تعيين كلمة المرور' }
    }
  })

  // توليد كود مستخدم
  ipcMain.handle('generate-user-code', async () => {
    try {
      const code = await userService.generateUserCode()
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في توليد كود المستخدم:', error)
      return { success: false, message: 'حدث خطأ في توليد كود المستخدم' }
    }
  })

  // الحصول على أنشطة المستخدم
  ipcMain.handle('get-user-activities', async (event, userId: number) => {
    try {
      return await userService.getUserActivities(userId)
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في جلب أنشطة المستخدم:', error)
      return { success: false, message: 'حدث خطأ في جلب أنشطة المستخدم' }
    }
  })

  // الحصول على جميع أنشطة المستخدمين
  ipcMain.handle('get-all-user-activities', async (event, filters?: any) => {
    try {
      return await userService.getAllUserActivities(filters)
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في جلب أنشطة المستخدمين:', error)
      return { success: false, message: 'حدث خطأ في جلب أنشطة المستخدمين' }
    }
  })

  // تنّيف الأنشطة القديمة
  ipcMain.handle('cleanup-old-activities', async (event, daysToKeep?: number) => {
    try {
      return await userService.cleanupOldActivities(daysToKeep)
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في تنّيف الأنشطة القديمة:', error)
      return { success: false, message: 'حدث خطأ في تنّيف الأنشطة القديمة' }
    }
  })

  // الأدوار والصلاحيات
  ipcMain.handle('get-roles', async () => {
    try {
      return await userService.getRoles()
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في جلب الأدوار:', error)
      return { success: false, message: 'حدث خطأ في جلب الأدوار' }
    }
  })

  ipcMain.handle('get-roles-with-users', async () => {
    try {
      return await userService.getRolesWithUsers()
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في جلب الأدوار مع المستخدمين:', error)
      return { success: false, message: 'حدث خطأ في جلب الأدوار مع المستخدمين' }
    }
  })

  ipcMain.handle('get-permissions', async () => {
    try {
      return await userService.getPermissions()
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في جلب الصلاحيات:', error)
      return { success: false, message: 'حدث خطأ في جلب الصلاحيات' }
    }
  })

  ipcMain.handle('get-user-permissions', async (_, userId: number) => {
    try {
      return await userService.getUserPermissions(userId)
    } catch (error) {
      Logger.error('UserHandlers', 'خطأ في جلب صلاحيات المستخدم:', error)
      return { success: false, message: 'حدث خطأ في جلب صلاحيات المستخدم' }
    }
  })
}
