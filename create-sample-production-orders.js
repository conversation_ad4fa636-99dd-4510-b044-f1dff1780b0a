const fs = require('fs');
const path = require('path');
const initSqlJs = require('sql.js');

async function createSampleProductionOrders() {
  try {
    console.log('🏭 إنشاء أوامر إنتاج تجريبية لاختبار نظام الصور...\n');
    
    const dbPath = path.join(process.env.APPDATA, 'accounting-production-app', 'accounting.db');
    
    if (!fs.existsSync(dbPath)) {
      console.log('❌ قاعدة البيانات غير موجودة:', dbPath);
      return;
    }
    
    console.log(`📍 فحص قاعدة البيانات: ${dbPath}`);
    
    const SQL = await initSqlJs({
      locateFile: file => path.join(__dirname, 'node_modules', 'sql.js', 'dist', file)
    });
    
    const filebuffer = fs.readFileSync(dbPath);
    const db = new SQL.Database(filebuffer);
    
    // فحص الأصناف الموجودة
    console.log('📦 فحص الأصناف الموجودة...');
    const itemsResult = db.exec('SELECT id, name FROM items LIMIT 5');
    
    if (!itemsResult[0]?.values || itemsResult[0].values.length === 0) {
      console.log('❌ لا توجد أصناف في قاعدة البيانات');
      return;
    }
    
    console.log('✅ تم العثور على الأصناف:');
    itemsResult[0].values.forEach(row => {
      console.log(`   📋 ${row[0]}: ${row[1]}`);
    });
    
    // فحص أوامر الإنتاج الموجودة
    console.log('\n🏭 فحص أوامر الإنتاج الموجودة...');
    const existingOrders = db.exec('SELECT COUNT(*) FROM production_orders');
    const orderCount = existingOrders[0]?.values[0]?.[0] || 0;
    console.log(`📊 عدد أوامر الإنتاج الحالية: ${orderCount}`);
    
    // إنشاء أوامر إنتاج تجريبية
    console.log('\n🏭 إنشاء أوامر إنتاج تجريبية...');
    
    const sampleOrders = [
      {
        order_number: 'PROD-001-2025',
        product_id: itemsResult[0].values[0][0],
        quantity: 10,
        status: 'in_progress',
        notes: 'أمر إنتاج تجريبي للاختبار'
      },
      {
        order_number: 'PROD-002-2025',
        product_id: itemsResult[0].values[1] ? itemsResult[0].values[1][0] : itemsResult[0].values[0][0],
        quantity: 5,
        status: 'pending',
        notes: 'أمر إنتاج تجريبي ثاني'
      },
      {
        order_number: 'PROD-003-2025',
        product_id: itemsResult[0].values[2] ? itemsResult[0].values[2][0] : itemsResult[0].values[0][0],
        quantity: 15,
        status: 'completed',
        notes: 'أمر إنتاج تجريبي مكتمل'
      }
    ];
    
    let createdOrders = 0;
    
    for (const order of sampleOrders) {
      try {
        // فحص إذا كان الأمر موجود مسبقاً
        const existingOrder = db.exec('SELECT id FROM production_orders WHERE order_number = ?', [order.order_number]);
        
        if (existingOrder[0]?.values && existingOrder[0].values.length > 0) {
          console.log(`   ⚠️ أمر ${order.order_number} موجود مسبقاً`);
          continue;
        }
        
        // إنشاء الأمر
        db.run(`
          INSERT INTO production_orders (
            order_number, product_id, quantity, status, notes,
            start_date, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          order.order_number,
          order.product_id,
          order.quantity,
          order.status,
          order.notes,
          new Date().toISOString(),
          new Date().toISOString(),
          new Date().toISOString()
        ]);
        
        createdOrders++;
        console.log(`   ✅ تم إنشاء أمر ${order.order_number}`);
        
      } catch (error) {
        console.log(`   ❌ خطأ في إنشاء أمر ${order.order_number}: ${error.message}`);
      }
    }
    
    // إنشاء صور تجريبية لأوامر الإنتاج
    console.log('\n📸 إنشاء صور تجريبية لأوامر الإنتاج...');
    
    const ordersForImages = db.exec('SELECT id, order_number FROM production_orders LIMIT 3');
    
    if (ordersForImages[0]?.values) {
      for (const orderRow of ordersForImages[0].values) {
        const orderId = orderRow[0];
        const orderNumber = orderRow[1];
        
        // فحص الصور الموجودة
        const existingImages = db.exec('SELECT COUNT(*) FROM production_order_images WHERE order_id = ?', [orderId]);
        const imageCount = existingImages[0]?.values[0]?.[0] || 0;
        
        if (imageCount === 0) {
          // إنشاء صورة تجريبية SVG
          const svgContent = `
            <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
              <rect width="200" height="150" fill="#f0f0f0" stroke="#ccc" stroke-width="2"/>
              <text x="100" y="75" text-anchor="middle" font-family="Arial" font-size="14" fill="#333">
                صورة تجريبية
              </text>
              <text x="100" y="95" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
                ${orderNumber}
              </text>
              <circle cx="50" cy="50" r="20" fill="#4CAF50" opacity="0.7"/>
              <rect x="130" y="30" width="40" height="40" fill="#2196F3" opacity="0.7"/>
            </svg>
          `;
          
          const dataUrl = `data:image/svg+xml;base64,${Buffer.from(svgContent).toString('base64')}`;
          
          try {
            db.run(`
              INSERT INTO production_order_images (
                order_id, image_name, image_path, file_size, file_type,
                description, category, is_primary, created_at
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              orderId,
              `صورة تجريبية - ${orderNumber}`,
              dataUrl,
              svgContent.length,
              'image/svg+xml',
              'صورة تجريبية للاختبار',
              'general',
              1,
              new Date().toISOString()
            ]);
            
            console.log(`   ✅ صورة لأمر ${orderNumber}`);
          } catch (error) {
            console.log(`   ❌ خطأ في إنشاء صورة لأمر ${orderNumber}: ${error.message}`);
          }
        } else {
          console.log(`   ⚠️ أمر ${orderNumber} يحتوي على ${imageCount} صورة مسبقاً`);
        }
      }
    }
    
    // حفظ قاعدة البيانات
    console.log('\n💾 حفظ التغييرات...');
    const data = db.export();
    fs.writeFileSync(dbPath, data);
    
    // إحصائيات نهائية
    console.log('\n📊 إحصائيات نهائية:');
    const finalOrderCount = db.exec('SELECT COUNT(*) FROM production_orders');
    const finalImageCount = db.exec('SELECT COUNT(*) FROM production_order_images');
    
    console.log(`   📋 إجمالي أوامر الإنتاج: ${finalOrderCount[0]?.values[0]?.[0] || 0}`);
    console.log(`   📸 إجمالي صور أوامر الإنتاج: ${finalImageCount[0]?.values[0]?.[0] || 0}`);
    console.log(`   ✅ تم إنشاء ${createdOrders} أمر إنتاج جديد`);
    
    db.close();
    
    console.log('\n' + '='.repeat(80));
    console.log('✅ تم إنشاء أوامر الإنتاج والصور التجريبية بنجاح!');
    console.log('='.repeat(80));
    
    console.log('\n📝 الخطوات التالية:');
    console.log('   1. افتح التطبيق');
    console.log('   2. اذهب إلى قسم "إدارة أوامر الإنتاج"');
    console.log('   3. اختبر عرض الصور');
    console.log('   4. اختبر طباعة أوامر الإنتاج مع الصور');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء أوامر الإنتاج:', error.message);
  }
}

createSampleProductionOrders();
