import React, { useState, useEffect, useRef } from 'react'
import { Card, Button, Space, Typography } from 'antd'
import styled, { keyframes, css } from 'styled-components'

const { Title, Text } = Typography

// الحركات المتحركة
const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`

const slideInRight = keyframes`
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`

const slideInLeft = keyframes`
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
`

const scaleIn = keyframes`
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
`

const bounce = keyframes`
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
`

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`

const shake = keyframes`
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
`

const rotate = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`

const glow = keyframes`
  0%, 100% {
    box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(24, 144, 255, 0.8);
  }
`

// أنواع الحركات
export type AnimationType = 
  | 'fadeIn' 
  | 'slideInRight' 
  | 'slideInLeft' 
  | 'scaleIn' 
  | 'bounce' 
  | 'pulse' 
  | 'shake' 
  | 'rotate' 
  | 'glow'

// خصائص الحركة
interface AnimationProps {
  animation?: AnimationType
  duration?: number
  delay?: number
  infinite?: boolean
  hover?: boolean
}

// مكون الحاوي المتحرك
const AnimatedWrapper = styled.div.withConfig({
  shouldForwardProp: (prop) => !['animation', 'duration', 'delay', 'infinite', 'hover'].includes(prop)
})<AnimationProps>`
  ${({ animation, duration = 0.5, delay = 0, infinite, hover }) => {
    const getAnimation = () => {
      switch (animation) {
        case 'fadeIn': return fadeIn
        case 'slideInRight': return slideInRight
        case 'slideInLeft': return slideInLeft
        case 'scaleIn': return scaleIn
        case 'bounce': return bounce
        case 'pulse': return pulse
        case 'shake': return shake
        case 'rotate': return rotate
        case 'glow': return glow
        default: return fadeIn
      }
    }

    if (!animation) return ''

    const animationCSS = css`
      animation: ${getAnimation()} ${duration}s ease-out ${delay}s ${infinite ? 'infinite' : '1'} both;
    `

    const hoverAnimationCSS = css`
      &:hover {
        animation: ${getAnimation()} ${duration}s ease-out ${infinite ? 'infinite' : '1'};
      }
    `

    return hover ? hoverAnimationCSS : animationCSS
  }}
`

// مكون البطاقة المتحركة
export const AnimatedCard: React.FC<{
  children: React.ReactNode
  animation?: AnimationType
  duration?: number
  delay?: number
  hover?: boolean
  className?: string
  style?: React.CSSProperties
  onClick?: () => void
}> = ({ 
  children, 
  animation = 'fadeIn', 
  duration = 0.5, 
  delay = 0, 
  hover = false,
  className,
  style,
  onClick
}) => {
  return (
    <AnimatedWrapper
      animation={animation}
      duration={duration}
      delay={delay}
      hover={hover}
      className={className}
      style={style}
      onClick={onClick}
    >
      <Card
        hoverable={!!onClick}
        style={{
          transition: 'all 0.3s ease',
          cursor: onClick ? 'pointer' : 'default'
        }}
      >
        {children}
      </Card>
    </AnimatedWrapper>
  )
}

// مكون الزر المتحرك
export const AnimatedButton: React.FC<{
  children: React.ReactNode
  animation?: AnimationType
  duration?: number
  delay?: number
  hover?: boolean
  type?: 'primary' | 'default' | 'dashed' | 'link' | 'text'
  size?: 'small' | 'middle' | 'large'
  icon?: React.ReactNode
  loading?: boolean
  disabled?: boolean
  onClick?: () => void
}> = ({ 
  children, 
  animation = 'pulse', 
  duration = 0.3, 
  delay = 0, 
  hover = true,
  ...buttonProps
}) => {
  return (
    <AnimatedWrapper
      animation={hover ? undefined : animation}
      duration={duration}
      delay={delay}
      hover={hover}
    >
      <Button
        {...buttonProps}
        style={{
          transition: 'all 0.3s ease'
        }}
      >
        {children}
      </Button>
    </AnimatedWrapper>
  )
}

// مكون النص المتحرك
export const AnimatedText: React.FC<{
  children: React.ReactNode
  animation?: AnimationType
  duration?: number
  delay?: number
  level?: 1 | 2 | 3 | 4 | 5
  type?: 'secondary' | 'success' | 'warning' | 'danger'
  className?: string
}> = ({ 
  children, 
  animation = 'fadeIn', 
  duration = 0.5, 
  delay = 0,
  level,
  type,
  className
}) => {
  const TextComponent = level ? Title : Text

  return (
    <AnimatedWrapper
      animation={animation}
      duration={duration}
      delay={delay}
      className={className}
    >
      <TextComponent level={level} type={type}>
        {children}
      </TextComponent>
    </AnimatedWrapper>
  )
}

// مكون المجموعة المتحركة
export const AnimatedGroup: React.FC<{
  children: React.ReactNode[]
  animation?: AnimationType
  duration?: number
  stagger?: number
  direction?: 'horizontal' | 'vertical'
  className?: string
}> = ({ 
  children, 
  animation = 'fadeIn', 
  duration = 0.5, 
  stagger = 0.1,
  direction = 'vertical' as 'vertical' | 'horizontal',
  className
}) => {
  return (
    <Space 
      direction={direction} 
      size="middle" 
      className={className}
      style={{ width: '100%' }}
    >
      {React.Children.map(children, (child, index) => (
        <AnimatedWrapper
          key={index}
          animation={animation}
          duration={duration}
          delay={index * stagger}
        >
          {child}
        </AnimatedWrapper>
      ))}
    </Space>
  )
}

// Hook للحركة عند الظهور في الشاشة
export const useInViewAnimation = (
  animation: AnimationType = 'fadeIn',
  options: IntersectionObserverInit = {}
) => {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      {
        threshold: 0.1,
        ...options
      }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [])

  return { ref, isVisible, animation: isVisible ? animation : undefined }
}

// مكون الحركة عند الظهور
export const InViewAnimation: React.FC<{
  children: React.ReactNode
  animation?: AnimationType
  duration?: number
  threshold?: number
  className?: string
}> = ({ 
  children, 
  animation = 'fadeIn', 
  duration = 0.5, 
  threshold = 0.1,
  className
}) => {
  const { ref, isVisible } = useInViewAnimation(animation as AnimationType, { threshold })

  return (
    <div ref={ref} className={className}>
      <AnimatedWrapper
        animation={isVisible ? animation : undefined}
        duration={duration}
      >
        {children}
      </AnimatedWrapper>
    </div>
  )
}

// مكون العداد المتحرك
export const AnimatedCounter: React.FC<{
  from?: number
  to: number
  duration?: number
  delay?: number
  suffix?: string
  prefix?: string
  separator?: string
  decimals?: number
}> = ({ 
  from = 0, 
  to, 
  duration = 2, 
  delay = 0, 
  suffix = '', 
  prefix = '',
  separator = ',',
  decimals = 0
}) => {
  const [count, setCount] = useState(from)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsAnimating(true)
      const increment = (to - from) / (duration * 60) // 60 FPS
      let current = from

      const counter = setInterval(() => {
        current += increment
        if (current >= to) {
          setCount(to)
          clearInterval(counter)
          setIsAnimating(false)
        } else {
          setCount(current)
        }
      }, 1000 / 60)

      return () => clearInterval(counter)
    }, delay * 1000)

    return () => clearTimeout(timer)
  }, [from, to, duration, delay])

  const formatNumber = (num: number) => {
    const rounded = Number(num.toFixed(decimals))
    const parts = rounded.toString().split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator)
    return parts.join('.')
  }

  return (
    <AnimatedWrapper animation="fadeIn" duration={0.5}>
      <span style={{ 
        fontWeight: 'bold',
        fontSize: '1.2em',
        color: isAnimating ? '#1890ff' : 'inherit',
        transition: 'color 0.3s ease'
      }}>
        {prefix}{formatNumber(count)}{suffix}
      </span>
    </AnimatedWrapper>
  )
}

// مكون شريط التقدم المتحرك
export const AnimatedProgress: React.FC<{
  percent: number
  duration?: number
  delay?: number
  showInfo?: boolean
  strokeColor?: string
  trailColor?: string
  strokeWidth?: number
}> = ({ 
  percent, 
  duration = 1, 
  delay = 0, 
  showInfo = true,
  strokeColor = '#1890ff',
  trailColor = '#f0f0f0',
  strokeWidth = 8
}) => {
  const [currentPercent, setCurrentPercent] = useState(0)

  useEffect(() => {
    const timer = setTimeout(() => {
      const increment = percent / (duration * 60)
      let current = 0

      const progress = setInterval(() => {
        current += increment
        if (current >= percent) {
          setCurrentPercent(percent)
          clearInterval(progress)
        } else {
          setCurrentPercent(current)
        }
      }, 1000 / 60)

      return () => clearInterval(progress)
    }, delay * 1000)

    return () => clearTimeout(timer)
  }, [percent, duration, delay])

  return (
    <AnimatedWrapper animation="fadeIn" duration={0.5}>
      <div style={{ width: '100%', position: 'relative' }}>
        <div
          style={{
            width: '100%',
            height: strokeWidth,
            backgroundColor: trailColor,
            borderRadius: strokeWidth / 2,
            overflow: 'hidden'
          }}
        >
          <div
            style={{
              width: `${currentPercent}%`,
              height: '100%',
              backgroundColor: strokeColor,
              borderRadius: strokeWidth / 2,
              transition: 'width 0.1s ease',
              background: `linear-gradient(90deg, ${strokeColor}, ${strokeColor}dd)`
            }}
          />
        </div>
        {showInfo && (
          <div
            style={{
              position: 'absolute',
              top: '50%',
              right: 8,
              transform: 'translateY(-50%)',
              fontSize: 12,
              fontWeight: 'bold',
              color: '#666'
            }}
          >
            {Math.round(currentPercent)}%
          </div>
        )}
      </div>
    </AnimatedWrapper>
  )
}

export default {
  Card: AnimatedCard,
  Button: AnimatedButton,
  Text: AnimatedText,
  Group: AnimatedGroup,
  InView: InViewAnimation,
  Counter: AnimatedCounter,
  Progress: AnimatedProgress,
  useInViewAnimation
}
