/**
 * أنواع البيانات لنظام الإقفال المحاسبي
 */

export type FiscalPeriodStatus = 'OPEN' | 'CLOSED' | 'LOCKED';

export interface FiscalPeriod {
  id: number
  period_name: string
  period_type: 'monthly' | 'quarterly' | 'semi_annual' | 'annual'
  start_date: string
  end_date: string
  status: 'open' | 'closed' | 'locked'
  closing_date?: string
  closed_by?: number
  closed_by_name?: string
  reopening_date?: string
  reopened_by?: number
  reopened_by_name?: string
  is_current: boolean
  notes?: string
  created_at: string
  updated_at?: string
}

export interface ClosingEntry {
  id: number
  fiscal_period_id: number
  entry_type: 'revenue_closing' | 'expense_closing' | 'profit_transfer'
  journal_entry_id: number
  amount: number
  entry_number?: string
  description?: string
  entry_date?: string
  created_at: string
}

export interface CarriedForwardBalance {
  id: number
  fiscal_period_id: number
  account_id: number
  account_code?: string
  account_name?: string
  opening_balance: number
  closing_balance: number
  carried_forward_amount: number
  balance_type: 'debit' | 'credit'
  created_at: string
}

export interface PeriodClosingAudit {
  id: number
  fiscal_period_id: number
  action_type: 'close_attempt' | 'close_success' | 'reopen' | 'modify_attempt'
  user_id: number
  user_name?: string
  action_details?: string
  ip_address?: string
  user_agent?: string
  success: boolean
  error_message?: string
  created_at: string
}

export interface ClosingValidation {
  success: boolean
  message?: string
  data?: {
    errors: string[]
    trialBalance: {
      balanced: boolean
      totalDebits: number
      totalCredits: number
      difference: number
    }
    canClose: boolean
  }
}

export interface ClosingReport {
  period: FiscalPeriod
  closingEntries: ClosingEntry[]
  carriedForwardBalances: CarriedForwardBalance[]
  auditLog: PeriodClosingAudit[]
  totalRevenue?: number
  totalExpenses?: number
  netIncome?: number
  totalAssets?: number
  entriesCreated?: number
}

export interface ClosingStatistics {
  totalPeriods: number
  openPeriods: number
  closedPeriods: number
  lockedPeriods: number
  currentPeriod: FiscalPeriod | null
}

export interface CreateFiscalPeriodData {
  period_name: string
  period_type: 'monthly' | 'quarterly' | 'semi_annual' | 'annual'
  start_date: string
  end_date: string
  is_current?: boolean
  notes?: string
}

export interface UpdateFiscalPeriodData {
  period_name?: string
  period_type?: 'monthly' | 'quarterly' | 'semi_annual' | 'annual'
  start_date?: string
  end_date?: string
  is_current?: boolean
  notes?: string
}

// أنواع الاستجابات من API
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
}

// خيارات الفلترة للفترات المالية
export interface FiscalPeriodFilters {
  period_type?: 'monthly' | 'quarterly' | 'semi_annual' | 'annual'
  status?: 'open' | 'closed' | 'locked'
  start_date?: string
  end_date?: string
  is_current?: boolean
}

// خيارات الترتيب
export interface SortOptions {
  field: keyof FiscalPeriod
  direction: 'asc' | 'desc'
}

// حالات التحميل
export interface LoadingStates {
  periods: boolean
  creating: boolean
  updating: boolean
  closing: boolean
  reopening: boolean
  deleting: boolean
  validating: boolean
}

// أنواع الأذونات للإقفال المحاسبي
export interface ClosingPermissions {
  canCreatePeriod: boolean
  canUpdatePeriod: boolean
  canClosePeriod: boolean
  canReopenPeriod: boolean
  canDeletePeriod: boolean
  canViewClosedData: boolean
  canModifyClosedData: boolean
  requiresApproval: boolean
  approvalLevel: 'supervisor' | 'manager' | 'admin'
}

// إعدادات الإقفال
export interface ClosingSettings {
  autoBackupBeforeClosing: boolean
  requireApprovalForClosing: boolean
  requireApprovalForReopening: boolean
  allowModificationAfterClosing: boolean
  lockPeriodAfterDays: number
  notifyUsersBeforeClosing: boolean
  notificationDays: number
}

// بيانات التحقق قبل الإقفال
export interface PreClosingChecks {
  allEntriesPosted: boolean
  trialBalanceBalanced: boolean
  noUnreconciledTransactions: boolean
  backupCompleted: boolean
  approvalObtained: boolean
  noOpenInvoices: boolean
  inventoryReconciled: boolean
}

// ملخص التحقق (تم نقله للأسفل لتجنب التكرار)

// تقدم عملية الإقفال
export interface ClosingProgress {
  currentStep: string;
  percentage: number;
  estimatedTimeRemaining?: number;
}

// نتائج عملية الإقفال
export interface ClosingResults {
  revenueClosing: number
  expenseClosing: number
  profitTransfer: number
  entriesCreated: number[]
  accountsCarriedForward: number
  backupCreated: boolean
  processingTime: number
}

// خيارات طباعة تقارير الإقفال
export interface ClosingReportPrintOptions {
  includeAuditLog: boolean
  includeCarriedForwardBalances: boolean
  includeClosingEntries: boolean
  showDetailedEntries: boolean
  watermark: boolean
  signatureRequired: boolean
}

// أنواع التقارير المتاحة للإقفال
export type ClosingReportType = 
  | 'closing_summary'
  | 'closing_entries'
  | 'carried_forward_balances'
  | 'audit_log'
  | 'period_comparison'
  | 'closing_checklist'

// بيانات مقارنة الفترات
export interface PeriodComparison {
  currentPeriod: FiscalPeriod
  previousPeriod?: FiscalPeriod
  revenueComparison: {
    current: number
    previous: number
    change: number
    changePercent: number
  }
  expenseComparison: {
    current: number
    previous: number
    change: number
    changePercent: number
  }
  profitComparison: {
    current: number
    previous: number
    change: number
    changePercent: number
  }
  revenueGrowth?: number
  profitGrowth?: number
  assetGrowth?: number
  comparisonData?: any[]
}

// حالات الإشعارات
export interface ClosingNotification {
  id: string
  type: 'warning' | 'error' | 'success' | 'info'
  title: string
  message: string
  periodId?: number
  actionRequired?: boolean
  timestamp: string
  read: boolean
}

// خيارات التصدير
export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv'
  includeCharts: boolean
  includeDetails: boolean
  dateRange?: {
    start: string
    end: string
  }
}

// أنواع إضافية للواجهات الجديدة

export interface ValidationResult {
  id: string;
  section: string;
  type: 'error' | 'warning' | 'info' | 'success';
  message: string;
  details?: string;
  actionRequired?: boolean;
}

export interface ValidationSummary {
  totalErrors: number;
  totalWarnings: number;
  totalInfo: number;
  totalSuccess: number;
  canClose: boolean;
}

// تم دمج ClosingProgress مع التعريف السابق في السطر 189

export interface TrialBalanceAccount {
  accountId: string;
  accountCode: string;
  accountName: string;
  debitBalance: number;
  creditBalance: number;
  balance: number;
}

export interface AuditTrailEntry {
  id: string;
  timestamp: string;
  userName: string;
  action: string;
  details: string;
}

export interface PeriodData {
  revenue: number;
  expenses: number;
  netIncome: number;
}

export interface NotificationSettings {
  closingReminders: boolean;
  validationAlerts: boolean;
  backupNotifications: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  inAppNotifications: boolean;
}

// تصدير جميع الأنواع للاستخدام في الواجهات
// لا نحتاج إلى تصدير افتراضي للأنواع
