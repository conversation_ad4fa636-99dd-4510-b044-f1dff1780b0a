import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Select, 
  Input, 
  Space, 
  message, 
  Row,
  Col,
  Statistic,
  DatePicker,
  Tag,
  Descriptions,
  InputNumber,
  Tabs
} from 'antd'
// import type { TabsProps } from 'antd'
import {
  DollarOutlined,
  FileTextOutlined,
  ArrowLeftOutlined,
  CheckOutlined,
  ToolOutlined,
  ShoppingOutlined,
  TeamOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../../utils/logger'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

interface ProductionFinanceManagementProps {
  onBack: () => void
}

const ProductionFinanceManagement: React.FC<ProductionFinanceManagementProps> = ({ onBack }) => {
  const [productionCosts, setProductionCosts] = useState([])
  const [bankAccounts, setBankAccounts] = useState([])
  const [loading, setLoading] = useState(false)
  const [paymentModalVisible, setPaymentModalVisible] = useState(false)
  const [selectedCost, setSelectedCost] = useState<any>(null)
  const [paymentMethod, setPaymentMethod] = useState('payment_voucher')
  const [activeTab, setActiveTab] = useState('material_costs')
  const [form] = Form.useForm()

  useEffect(() => {
    loadProductionCosts()
    loadBankAccounts()
  }, [])

  const loadProductionCosts = async () => {
    setLoading(true)
    try {
      Logger.info('ProductionFinanceManagement', '🔄 جاري تحميل تكاليف الإنتاج...')

      if (!window.electronAPI) {
        Logger.error('ProductionFinanceManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('ProductionFinanceManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية لتكاليف الإنتاج
        const mockProductionCosts = [
          {
            id: 1,
            cost_type: 'مواد خام',
            description: 'تكلفة الخشب والمسامير',
            amount: 5000.0,
            order_id: 1,
            order_number: 'PO001',
            department_id: 1,
            department_name: 'قسم النجارة',
            cost_date: '2024-06-20',
            status: 'approved',
            created_by: 1,
            created_by_name: 'مدير الإنتاج'
          },
          {
            id: 2,
            cost_type: 'عمالة',
            description: 'أجور العمال لأمر الإنتاج',
            amount: 2000.0,
            order_id: 1,
            order_number: 'PO001',
            department_id: 1,
            department_name: 'قسم النجارة',
            cost_date: '2024-06-19',
            status: 'pending',
            created_by: 1,
            created_by_name: 'مدير الإنتاج'
          }
        ]

        setProductionCosts(mockProductionCosts as any)
        Logger.info('ProductionFinanceManagement', '✅ تم تحميل ${mockProductionCosts.length} تكلفة إنتاج وهمية')
      } else {
        const response = await window.electronAPI.getProductionCosts()
        if (response.success) {
          setProductionCosts(response.data)
          Logger.info('ProductionFinanceManagement', '✅ تم تحميل تكاليف الإنتاج من قاعدة البيانات')
        } else {
          message.error('فشل في تحميل تكاليف الإنتاج')
        }
      }
    } catch (error) {
      message.error('خطأ في تحميل تكاليف الإنتاج')
    }
    setLoading(false)
  }

  const loadBankAccounts = async () => {
    try {
      const response = await window.electronAPI.getBankAccounts()
      if (response.success) {
        setBankAccounts(response.data)
      }
    } catch (error) {
      Logger.error('ProductionFinanceManagement', 'خطأ في تحميل الحسابات المصرفية:', error)
    }
  }

  const handlePayment = async (values: any) => {
    try {
      let paymentResponse
      
      // إنشاء الدفعة حسب نوع الدفع
      if (paymentMethod === 'check') {
        // إنشاء شيك صادر من الشركة
        const checkData = {
          check_number: values.check_number,
          bank_account_id: values.bank_account_id,
          amount: values.amount,
          issue_date: values.payment_date.format('YYYY-MM-DD'),
          due_date: values.due_date ? values.due_date.format('YYYY-MM-DD') : values.payment_date.format('YYYY-MM-DD'),
          payee_name: selectedCost.supplier_name || selectedCost.description || 'مصاريف إنتاج',
          notes: values.notes,
          check_type: 'issued',
          original_payer: 'الشركة',
          current_holder: selectedCost.supplier_name || 'مورد',
          is_company_check: 1,
          reference_type: 'production_cost',
          reference_id: selectedCost.id,
          created_by: 1
        }
        paymentResponse = await window.electronAPI.createCheck(checkData)
      } else if (paymentMethod === 'payment_voucher') {
        // إنشاء سند دفع
        const voucherData = {
          voucher_number: values.voucher_number,
          amount: values.amount,
          payment_date: values.payment_date.format('YYYY-MM-DD'),
          payee_name: selectedCost.supplier_name || selectedCost.description || 'مصاريف إنتاج',
          description: `دفع تكلفة إنتاج: ${selectedCost.description}`,
          payment_method: values.voucher_payment_method || 'cash',
          bank_account_id: values.bank_account_id,
          reference_type: 'production_cost',
          reference_id: selectedCost.id,
          currency_id: 1,
          created_by: 1,
          notes: values.notes
        }
        paymentResponse = await window.electronAPI.createPaymentVoucher(voucherData)
      }

      if (paymentResponse && paymentResponse.success) {
        // تحديث حالة التكلفة إلى مدفوعة
        const updateResponse = await (window.electronAPI as any).updateProductionCostStatus(selectedCost.id, 'paid')
        if (updateResponse.success) {
          message.success('تم تسجيل الدفعة وتحديث حالة التكلفة بنجاح')
          setPaymentModalVisible(false)
          form.resetFields()
          loadProductionCosts()
        } else {
          message.error('تم إنشاء الدفعة ولكن فشل في تحديث حالة التكلفة')
        }
      } else {
        message.error('فشل في إنشاء الدفعة')
      }
    } catch (error) {
      message.error('خطأ في تسجيل الدفعة')
    }
  }

  const showPaymentModal = (cost: any) => {
    setSelectedCost(cost)
    setPaymentModalVisible(true)
    form.setFieldsValue({
      payment_date: dayjs(),
      amount: cost.amount
    })
  }

  const generateVoucherNumber = async () => {
    try {
      const response = await window.electronAPI.generatePaymentVoucherNumber()
      if (response.success) {
        form.setFieldsValue({ voucher_number: response.data.voucherNumber })
      }
    } catch (error) {
      message.error('خطأ في إنشاء رقم السند')
    }
  }

  const generateCheckNumber = async () => {
    try {
      const response = await window.electronAPI.generateCheckNumber()
      if (response.success) {
        form.setFieldsValue({ check_number: response.data.checkNumber })
      }
    } catch (error) {
      message.error('خطأ في إنشاء رقم الشيك')
    }
  }

  const getCostTypeIcon = (type: string) => {
    switch (type) {
      case 'material': return <ShoppingOutlined style={{ color: '#1890ff' }} />
      case 'labor': return <TeamOutlined style={{ color: '#52c41a' }} />
      case 'overhead': return <ToolOutlined style={{ color: '#fa8c16' }} />
      default: return <DollarOutlined style={{ color: '#722ed1' }} />
    }
  }

  const getCostTypeText = (type: string) => {
    switch (type) {
      case 'material': return 'مواد خام'
      case 'labor': return 'عمالة'
      case 'overhead': return 'تكاليف عامة'
      default: return type
    }
  }

  const getCostCategoryText = (category: string) => {
    switch (category) {
      case 'direct': return 'مباشرة'
      case 'indirect': return 'غير مباشرة'
      default: return category
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'paid': return 'green'
      case 'cancelled': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتّار'
      case 'paid': return 'مدفوعة'
      case 'cancelled': return 'ملغية'
      default: return status
    }
  }

  const columns = [
    {
      title: 'نوع التكلفة',
      dataIndex: 'cost_type',
      key: 'cost_type',
      render: (type: string) => (
        <Space>
          {getCostTypeIcon(type)}
          <span>{getCostTypeText(type)}</span>
        </Space>
      )
    },
    {
      title: 'التصنيف',
      dataIndex: 'cost_category',
      key: 'cost_category',
      render: (category: string) => (
        <Tag color={category === 'direct' ? 'blue' : 'orange'}>
          {getCostCategoryText(category)}
        </Tag>
      )
    },
    {
      title: 'الوصف',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'أمر الإنتاج',
      dataIndex: 'order_number',
      key: 'order_number',
      render: (number: string) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{number}</span>
      )
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ₪ {amount?.toLocaleString() || 0}
        </span>
      )
    },
    {
      title: 'التاريخ',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: any) => (
        record.status === 'pending' && (
          <Button
            type="primary"
            size="small"
            icon={<DollarOutlined />}
            onClick={() => showPaymentModal(record)}
          >
            تسجيل دفعة
          </Button>
        )
      )
    }
  ]

  // فلترة التكاليف حسب النوع
  const filteredCosts = productionCosts.filter((cost: any) => {
    switch (activeTab) {
      case 'material_costs':
        return cost.cost_type === 'material'
      case 'labor_costs':
        return cost.cost_type === 'labor'
      case 'overhead_costs':
        return cost.cost_type === 'overhead'
      default:
        return true
    }
  })

  const stats = {
    totalCosts: productionCosts.length,
    pendingCosts: productionCosts.filter((cost: any) => cost.status === 'pending').length,
    totalAmount: productionCosts.reduce((sum: number, cost: any) => sum + (cost.amount || 0), 0),
    pendingAmount: productionCosts.filter((cost: any) => cost.status === 'pending').reduce((sum: number, cost: any) => sum + (cost.amount || 0), 0)
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>🏭 إدارة مالية الإنتاج</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            تسجيل وربط تكاليف الإنتاج بالنّام المالي
          </p>
        </div>
        <Button 
          type="default" 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
        >
          العودة
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي التكاليف"
              value={stats.totalCosts}
              valueStyle={{ color: '#1890ff' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="تكاليف معلقة"
              value={stats.pendingCosts}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي المبالغ"
              value={stats.totalAmount}
              valueStyle={{ color: '#52c41a' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="مبالغ معلقة"
              value={stats.pendingAmount}
              valueStyle={{ color: '#ff4d4f' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      {/* جدول تكاليف الإنتاج */}
      <Card title="تكاليف الإنتاج">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            { key: 'material_costs', label: 'تكاليف المواد' },
            { key: 'labor_costs', label: 'تكاليف العمالة' },
            { key: 'overhead_costs', label: 'التكاليف العامة' },
            { key: 'all_costs', label: 'جميع التكاليف' }
          ]}
        />

        <Table
          columns={columns}
          dataSource={filteredCosts}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* نموذج تسجيل الدفعة */}
      <Modal
        title="تسجيل دفعة لتكلفة إنتاج"
        open={paymentModalVisible}
        onCancel={() => {
          setPaymentModalVisible(false)
          setSelectedCost(null)
          form.resetFields()
        }}
        footer={null}
        width={800}
      >
        {selectedCost && (
          <>
            <Descriptions title="بيانات التكلفة" bordered size="small" style={{ marginBottom: '24px' }}>
              <Descriptions.Item label="نوع التكلفة">
                <Space>
                  {getCostTypeIcon(selectedCost.cost_type)}
                  <span>{getCostTypeText(selectedCost.cost_type)}</span>
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="التصنيف">
                <Tag color={selectedCost.cost_category === 'direct' ? 'blue' : 'orange'}>
                  {getCostCategoryText(selectedCost.cost_category)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="الوصف">{selectedCost.description}</Descriptions.Item>
              <Descriptions.Item label="أمر الإنتاج">{selectedCost.order_number}</Descriptions.Item>
              <Descriptions.Item label="المبلغ">
                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                  ₪ {selectedCost.amount?.toLocaleString() || 0}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="التاريخ">{dayjs(selectedCost.created_at).format('YYYY-MM-DD')}</Descriptions.Item>
            </Descriptions>

            <Form
              form={form}
              layout="vertical"
              onFinish={handlePayment}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="payment_method"
                    label="طريقة الدفع"
                    rules={[{ required: true, message: 'يرجى اختيار طريقة الدفع' }]}
                    initialValue="payment_voucher"
                  >
                    <Select
                      placeholder="اختر طريقة الدفع"
                      onChange={(value) => setPaymentMethod(value)}
                    >
                      <Option value="payment_voucher">سند دفع</Option>
                      <Option value="check">شيك الشركة</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="amount"
                    label="المبلغ"
                    rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="0"
                      min={0}
                      formatter={value => `₪ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => (parseFloat((value || '').replace(/₪\s?|(,*)/g, '')) || 0) as any}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="payment_date"
                    label="تاريخ الدفع"
                    rules={[{ required: true, message: 'يرجى اختيار تاريخ الدفع' }]}
                  >
                    <DatePicker
                      style={{ width: '100%' }}
                      placeholder="اختر تاريخ الدفع"
                      format="YYYY-MM-DD"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  {paymentMethod === 'payment_voucher' ? (
                    <Form.Item
                      name="voucher_number"
                      label="رقم السند"
                      rules={[{ required: true, message: 'يرجى إدخال رقم السند' }]}
                    >
                      <Input
                        placeholder="PV000001"
                        addonAfter={
                          <Button
                            type="link"
                            size="small"
                            onClick={generateVoucherNumber}
                          >
                            توليد
                          </Button>
                        }
                      />
                    </Form.Item>
                  ) : (
                    <Form.Item
                      name="check_number"
                      label="رقم الشيك"
                      rules={[{ required: true, message: 'يرجى إدخال رقم الشيك' }]}
                    >
                      <Input
                        placeholder="CHK000001"
                        addonAfter={
                          <Button
                            type="link"
                            size="small"
                            onClick={generateCheckNumber}
                          >
                            توليد
                          </Button>
                        }
                      />
                    </Form.Item>
                  )}
                </Col>
              </Row>

              {paymentMethod === 'check' && (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="due_date"
                      label="تاريخ استحقاق الشيك"
                    >
                      <DatePicker
                        style={{ width: '100%' }}
                        placeholder="اختر تاريخ الاستحقاق"
                        format="YYYY-MM-DD"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="bank_account_id"
                      label="الحساب المصرفي"
                      rules={[{ required: paymentMethod === 'check', message: 'يرجى اختيار الحساب المصرفي' }]}
                    >
                      <Select placeholder="اختر الحساب المصرفي">
                        {bankAccounts.map((account: any) => (
                          <Option key={account.id} value={account.id}>
                            {account.bank_name} - {account.account_name} ({account.account_number})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              )}

              {paymentMethod === 'payment_voucher' && (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="voucher_payment_method"
                      label="طريقة الدفع في السند"
                      initialValue="cash"
                    >
                      <Select placeholder="اختر طريقة الدفع">
                        <Option value="cash">نقدي</Option>
                        <Option value="bank_transfer">تحويل بنكي</Option>
                        <Option value="check">شيك</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="bank_account_id"
                      label="الحساب المصرفي (اختياري)"
                    >
                      <Select placeholder="اختر الحساب المصرفي">
                        {bankAccounts.map((account: any) => (
                          <Option key={account.id} value={account.id}>
                            {account.bank_name} - {account.account_name} ({account.account_number})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              )}

              <Form.Item
                name="notes"
                label="ملاحّات"
              >
                <TextArea
                  placeholder="ملاحّات إضافية حول الدفعة"
                  rows={3}
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<CheckOutlined />}>
                    تسجيل الدفعة
                  </Button>
                  <Button onClick={() => {
                    setPaymentModalVisible(false)
                    setSelectedCost(null)
                    form.resetFields()
                  }}>
                    إلغاء
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </>
        )}
      </Modal>
    </div>
  )
}

export default ProductionFinanceManagement
