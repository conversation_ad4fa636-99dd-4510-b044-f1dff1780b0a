# 🏗️ هيكل خدمات الطباعة - ZET.IA

## 📋 نظرة عامة

تم تصميم نظام الطباعة في ZET.IA ليعمل بطريقة موزعة بين عمليتين:

- **Renderer Process**: إنشاء المحتوى والقوالب
- **Main Process**: الطباعة الفعلية والحفظ

## 🔧 الخدمات المتاحة

### 1. MasterPrintService (Renderer Process)

**الموقع**: `src/renderer/src/services/MasterPrintService.ts`

**المسؤوليات**:
- إنشاء HTML للطباعة
- إدارة القوالب والتنسيقات
- معالجة البيانات وتحويلها
- إدارة الإعدادات والألوان
- المعاينة في المتصفح
- دعم أنواع مختلفة من المستندات

**الوظائف الرئيسية**:
```typescript
// الطباعة الأساسية
await masterPrintService.print(data, options)

// المعاينة
await masterPrintService.preview(data, options)

// الطباعة السريعة
await masterPrintService.quickPrint(data, 'invoice')

// إنشاء HTML
const html = await masterPrintService.generateHTML(data, options)
```

**أنواع المستندات المدعومة**:
- الفواتير (invoice)
- الإيصالات (receipt)
- التقارير (report)
- الشهادات (certificate)
- البيانات (statement)
- الصور (image)
- الكتالوجات (catalog)

### 2. PrintService (Main Process)

**الموقع**: `src/main/services/PrintService.ts`

**المسؤوليات**:
- الطباعة الفعلية باستخدام Electron APIs
- حفظ المستندات كـ PDF
- إدارة إعدادات الطابعة
- التحكم في جودة الطباعة
- معالجة أخطاء الطباعة

**الوظائف الرئيسية**:
```typescript
// الطباعة المحسنة
await printService.printWithUnifiedOptions(html, options)

// حفظ كـ PDF
await printService.saveAsPDFWithUnifiedOptions(html, filePath, options)

// الطباعة التقليدية (للتوافق)
await printService.printInvoice(invoiceData, options)
```

## 🔄 تدفق العمل

### 1. الطباعة العادية
```mermaid
graph TD
    A[مكون الطباعة] --> B[MasterPrintService]
    B --> C[إنشاء HTML]
    C --> D[إرسال للـ Main Process]
    D --> E[PrintService]
    E --> F[الطباعة الفعلية]
```

### 2. المعاينة
```mermaid
graph TD
    A[طلب المعاينة] --> B[MasterPrintService]
    B --> C[إنشاء HTML]
    C --> D[عرض في Modal]
    D --> E[خيار الطباعة]
    E --> F[إرسال للـ Main Process]
```

## 📊 مقارنة الخدمات

| الخاصية | MasterPrintService | PrintService |
|---------|-------------------|--------------|
| **البيئة** | Renderer Process | Main Process |
| **الحجم** | 2,912 سطر | 687 سطر |
| **التعقيد** | عالي | متوسط |
| **الوظائف** | إنشاء المحتوى | الطباعة الفعلية |
| **الأنواع** | UnifiedPrintOptions | MainProcessPrintOptions |
| **التبعيات** | React, Antd | Electron APIs |

## 🎯 الأنواع الموحدة

### UnifiedPrintOptions (Renderer)
```typescript
interface UnifiedPrintOptions {
  pageSize?: 'A4' | 'A5' | 'A3' | 'Letter' | 'Legal'
  orientation?: 'portrait' | 'landscape'
  margins?: PrintMargins
  fontSize?: number
  fontFamily?: string
  primaryColor?: string
  showLogo?: boolean
  // ... المزيد
}
```

### MainProcessPrintOptions (Main)
```typescript
interface MainProcessPrintOptions {
  pageSize?: 'A4' | 'A5' | 'A3' | 'Letter' | 'Legal'
  orientation?: 'portrait' | 'landscape'
  margins?: PrintMargins
  quality?: 'draft' | 'normal' | 'high'
  copies?: number
  silent?: boolean
  printBackground?: boolean
  color?: boolean
}
```

## 🔧 الاستخدام العملي

### 1. في المكونات
```typescript
import { MasterPrintService } from '../services/MasterPrintService'
import { usePrintSettings } from '../hooks/usePrintSettings'

const MyComponent = () => {
  const { settings } = usePrintSettings()
  const printService = MasterPrintService.getInstance()
  
  const handlePrint = async () => {
    await printService.print(data, {
      type: 'invoice',
      ...settings
    })
  }
}
```

### 2. في الـ Main Process
```typescript
import { PrintService } from '../services/PrintService'

// في systemHandlers.ts
ipcMain.handle('print-document', async (_, html, options) => {
  const printService = PrintService.getInstance()
  return await printService.printWithUnifiedOptions(html, options)
})
```

## 🚀 المزايا

### 1. فصل الاهتمامات
- **MasterPrintService**: يركز على المحتوى والتنسيق
- **PrintService**: يركز على الطباعة والحفظ

### 2. الأداء
- معالجة HTML في Renderer Process
- الطباعة في Main Process بدون تأثير على UI

### 3. المرونة
- دعم أنواع مختلفة من المستندات
- إعدادات قابلة للتخصيص
- توافق مع الأنظمة القديمة

### 4. الصيانة
- كود منظم ومفصول
- أنواع موحدة
- سهولة الاختبار

## 🔮 التطوير المستقبلي

### 1. تحسينات مخطط لها
- دعم الطباعة السحابية
- قوالب ديناميكية
- طباعة مجمعة
- تحسين الأداء

### 2. ميزات جديدة
- طباعة الباركود
- دعم اللغات المتعددة
- تصدير لصيغ مختلفة
- طباعة تلقائية

## 📝 ملاحظات مهمة

1. **استخدم MasterPrintService** لجميع عمليات إنشاء المحتوى
2. **استخدم PrintService** فقط في Main Process
3. **الأنواع الموحدة** تضمن التوافق بين الخدمتين
4. **الإعدادات المركزية** تعمل مع كلا الخدمتين
5. **التوافق مع القديم** محفوظ للتطبيقات الموجودة

---

*تم إنشاء هذه الوثيقة كجزء من مشروع توحيد نظام الطباعة في ZET.IA*
