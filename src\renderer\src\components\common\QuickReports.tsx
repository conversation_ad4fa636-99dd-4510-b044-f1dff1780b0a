import React, { useState } from 'react'
import { Mo<PERSON>, Card, Row, Col, Button, Space, Typography, Divider } from 'antd'
import {
  ClockCircleOutlined,
  FileTextOutlined,
  ShoppingCartOutlined,
  TeamOutlined,
  DollarOutlined,
  Bar<PERSON>hartOutlined,
  PrinterOutlined,
  DownloadOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { Title, Text } = Typography

interface QuickReportsProps {
  visible: boolean
  onClose: () => void
  onNavigateToReport: (reportKey: string) => void
}

const QuickReports: React.FC<QuickReportsProps> = ({ visible, onClose, onNavigateToReport }) => {
  const [loading, setLoading] = useState<string | null>(null)

  const handleReportClick = (reportKey: string) => {
    setLoading(reportKey)
    setTimeout(() => {
      onNavigateToReport(reportKey)
      onClose()
      setLoading(null)
    }, 500)
  }

  const quickReports = [
    {
      key: 'attendance-report',
      title: 'تقرير الحضور اليومي',
      description: 'حضور وانصراف الموظفين لليوم الحالي',
      icon: <ClockCircleOutlined />,
      color: '#52c41a',
      category: 'employees'
    },
    {
      key: 'sales-invoices',
      title: 'فواتير المبيعات',
      description: 'جميع فواتير المبيعات',
      icon: <ShoppingCartOutlined />,
      color: '#1890ff',
      category: 'sales'
    },
    {
      key: 'purchase-invoices',
      title: 'فواتير المشتريات',
      description: 'جميع فواتير المشتريات',
      icon: <FileTextOutlined />,
      color: '#722ed1',
      category: 'purchases'
    },
    {
      key: 'payroll-report',
      title: 'تقرير الرواتب',
      description: 'ملخص رواتب الموظفين',
      icon: <TeamOutlined />,
      color: '#fa8c16',
      category: 'employees'
    },
    {
      key: 'inventory-detailed-report',
      title: 'تقرير المخزون التفصيلي',
      description: 'حالة المخزون والأصناف منخفضة الكمية',
      icon: <BarChartOutlined />,
      color: '#13c2c2',
      category: 'inventory'
    },
    {
      key: 'monthly-sales-report',
      title: 'تقرير المبيعات الشهري',
      description: 'ملخص المبيعات الشهرية',
      icon: <DollarOutlined />,
      color: '#eb2f96',
      category: 'sales'
    }
  ]

  const reportCategories = [
    {
      key: 'employees-reports',
      title: 'تقارير الموظفين',
      icon: <TeamOutlined />,
      color: '#52c41a'
    },
    {
      key: 'sales-reports',
      title: 'تقارير المبيعات',
      icon: <ShoppingCartOutlined />,
      color: '#1890ff'
    },
    {
      key: 'purchases-reports',
      title: 'تقارير المشتريات',
      icon: <FileTextOutlined />,
      color: '#722ed1'
    },
    {
      key: 'finance-reports',
      title: 'التقارير المالية',
      icon: <DollarOutlined />,
      color: '#eb2f96'
    },
    {
      key: 'inventory-reports',
      title: 'تقارير المخزون',
      icon: <BarChartOutlined />,
      color: '#13c2c2'
    }
  ]

  return (
    <Modal
      title={
        <Space>
          <ClockCircleOutlined />
          التقارير السريعة - {dayjs().format('YYYY-MM-DD')}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={900}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* مقدمة */}
        <Card size="small" style={{ background: '#f6ffed', border: '1px solid #b7eb8f' }}>
          <Text>
            💡 اختر من التقارير السريعة أدناه للحصول على معلومات فورية عن أداء عملك اليوم
          </Text>
        </Card>

        {/* التقارير السريعة */}
        <div>
          <Title level={4}>📊 التقارير السريعة</Title>
          <Row gutter={[16, 16]}>
            {quickReports.map(report => (
              <Col xs={24} sm={12} md={8} key={report.key}>
                <Card
                  size="small"
                  hoverable
                  style={{
                    borderColor: report.color,
                    borderWidth: 2,
                    height: '120px',
                    cursor: 'pointer'
                  }}
                  onClick={() => handleReportClick(report.key)}
                >
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', color: report.color, marginBottom: 8 }}>
                      {report.icon}
                    </div>
                    <Text strong style={{ fontSize: '12px', display: 'block', marginBottom: 4 }}>
                      {report.title}
                    </Text>
                    <Text type="secondary" style={{ fontSize: '10px' }}>
                      {report.description}
                    </Text>
                  </div>
                  {loading === report.key && (
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: 'rgba(255,255,255,0.8)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: '6px'
                    }}>
                      <Text>جاري التحميل...</Text>
                    </div>
                  )}
                </Card>
              </Col>
            ))}
          </Row>
        </div>

        <Divider />

        {/* فئات التقارير */}
        <div>
          <Title level={4}>📁 فئات التقارير</Title>
          <Row gutter={[16, 16]}>
            {reportCategories.map(category => (
              <Col xs={24} sm={12} md={8} lg={6} key={category.key}>
                <Button
                  block
                  size="large"
                  style={{
                    height: '60px',
                    borderColor: category.color,
                    color: category.color
                  }}
                  onClick={() => handleReportClick('reports')}
                >
                  <Space direction="vertical" size={0}>
                    <div style={{ fontSize: '18px' }}>{category.icon}</div>
                    <Text style={{ fontSize: '12px', color: category.color }}>
                      {category.title}
                    </Text>
                  </Space>
                </Button>
              </Col>
            ))}
          </Row>
        </div>

        <Divider />

        {/* إجراءات سريعة */}
        <div>
          <Title level={5}>⚡ إجراءات سريعة</Title>
          <Space wrap>
            <Button
              icon={<PrinterOutlined />}
              onClick={() => handleReportClick('inventory-detailed-report')}
            >
              طباعة تقرير شامل
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => handleReportClick('monthly-sales-report')}
            >
              تصدير البيانات
            </Button>
            <Button
              type="dashed"
              onClick={() => handleReportClick('reports')}
            >
              جميع التقارير
            </Button>
          </Space>
        </div>

        {/* نصائح */}
        <Card size="small" style={{ background: '#fff7e6', border: '1px solid #ffd591' }}>
          <Text type="warning">
            💡 نصيحة: يمكنك الوصول لجميع التقارير من القائمة الجانبية ← التقارير
          </Text>
        </Card>
      </Space>
    </Modal>
  )
}

export default QuickReports
