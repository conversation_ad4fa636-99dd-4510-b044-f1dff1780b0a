import { Logger } from './logger'

/**
 * دالة مساعدة لتحويل القيم إلى أرقام صحيحة
 * تتعامل مع القيم المرسلة من النماذج التي قد تكون strings
 */
export function safeParseInt(value: any, fieldName?: string): number {
  Logger.info('DataTypeUtils', `🔢 محاولة تحويل ${fieldName || 'unknown'}: ${value} (${typeof value})`)

  if (value === null || value === undefined || value === '') {
    Logger.info('DataTypeUtils', `🔢 قيمة فارغة، إرجاع 0`)
    return 0
  }

  if (typeof value === 'number') {
    const result = Math.floor(value)
    Logger.info('DataTypeUtils', `🔢 رقم صحيح: ${value} → ${result}`)
    return result
  }

  if (typeof value === 'string') {
    const parsed = parseInt(value, 10)
    if (isNaN(parsed)) {
      Logger.warn('DataTypeUtils', `❌ فشل في تحويل القيمة إلى رقم: ${fieldName || 'unknown'} = ${value}`)
      return 0
    }
    Logger.info('DataTypeUtils', `🔢 تحويل نص إلى رقم: "${value}" → ${parsed}`)
    return parsed
  }

  Logger.warn('DataTypeUtils', `❌ نوع بيانات غير متوقع: ${fieldName || 'unknown'} = ${value} (${typeof value})`)
  return 0
}

/**
 * دالة مساعدة لتحويل القيم إلى أرقام عشرية
 */
export function safeParseFloat(value: any, fieldName?: string): number {
  if (value === null || value === undefined || value === '') {
    return 0
  }

  if (typeof value === 'number') {
    return value
  }

  if (typeof value === 'string') {
    const parsed = parseFloat(value)
    if (isNaN(parsed)) {
      Logger.warn('DataTypeUtils', `فشل في تحويل القيمة إلى رقم عشري: ${fieldName || 'unknown'} = ${value}`)
      return 0
    }
    return parsed
  }

  Logger.warn('DataTypeUtils', `نوع بيانات غير متوقع: ${fieldName || 'unknown'} = ${value} (${typeof value})`)
  return 0
}

/**
 * دالة لتحويل بيانات الفاتورة/الأمر وضمان صحة أنواع البيانات
 */
export function sanitizeFormData(data: any): any {
  Logger.info('DataTypeUtils', '🔄 بدء تنظيف البيانات:', {
    supplier_id: data.supplier_id ? `${data.supplier_id} (${typeof data.supplier_id})` : 'غير محدد',
    customer_id: data.customer_id ? `${data.customer_id} (${typeof data.customer_id})` : 'غير محدد'
  })

  const sanitized = { ...data }

  // تحويل المعرفات الأساسية
  if (sanitized.customer_id !== undefined) {
    const originalValue = sanitized.customer_id
    sanitized.customer_id = safeParseInt(sanitized.customer_id, 'customer_id')
    Logger.info('DataTypeUtils', `🔄 تحويل customer_id: ${originalValue} (${typeof originalValue}) → ${sanitized.customer_id} (${typeof sanitized.customer_id})`)
  }

  if (sanitized.supplier_id !== undefined) {
    const originalValue = sanitized.supplier_id
    sanitized.supplier_id = safeParseInt(sanitized.supplier_id, 'supplier_id')
    Logger.info('DataTypeUtils', `🔄 تحويل supplier_id: ${originalValue} (${typeof originalValue}) → ${sanitized.supplier_id} (${typeof sanitized.supplier_id})`)
  }

  if (sanitized.order_id !== undefined) {
    const originalValue = sanitized.order_id
    sanitized.order_id = safeParseInt(sanitized.order_id, 'order_id')
    Logger.info('DataTypeUtils', `🔄 تحويل order_id: ${originalValue} (${typeof originalValue}) → ${sanitized.order_id} (${typeof sanitized.order_id})`)
  }

  // تحويل المبالغ المالية
  if (sanitized.total_amount !== undefined) {
    sanitized.total_amount = safeParseFloat(sanitized.total_amount, 'total_amount')
  }

  if (sanitized.discount !== undefined) {
    sanitized.discount = safeParseFloat(sanitized.discount, 'discount')
  }

  if (sanitized.tax !== undefined) {
    sanitized.tax = safeParseFloat(sanitized.tax, 'tax')
  }

  if (sanitized.discount_amount !== undefined) {
    sanitized.discount_amount = safeParseFloat(sanitized.discount_amount, 'discount_amount')
  }

  if (sanitized.tax_amount !== undefined) {
    sanitized.tax_amount = safeParseFloat(sanitized.tax_amount, 'tax_amount')
  }

  if (sanitized.subtotal !== undefined) {
    sanitized.subtotal = safeParseFloat(sanitized.subtotal, 'subtotal')
  }

  if (sanitized.final_amount !== undefined) {
    sanitized.final_amount = safeParseFloat(sanitized.final_amount, 'final_amount')
  }

  if (sanitized.paid_amount !== undefined) {
    sanitized.paid_amount = safeParseFloat(sanitized.paid_amount, 'paid_amount')
  }

  // تحويل بيانات الأصناف
  if (sanitized.items && Array.isArray(sanitized.items)) {
    sanitized.items = sanitized.items.map((item: any, index: number) => ({
      ...item,
      item_id: safeParseInt(item.item_id, `items[${index}].item_id`),
      warehouse_id: safeParseInt(item.warehouse_id, `items[${index}].warehouse_id`),
      quantity: safeParseFloat(item.quantity, `items[${index}].quantity`),
      unit_price: safeParseFloat(item.unit_price, `items[${index}].unit_price`),
      total_price: safeParseFloat(item.total_price, `items[${index}].total_price`)
    }))
  }

  Logger.info('DataTypeUtils', '✅ انتهاء تنظيف البيانات:', {
    supplier_id: sanitized.supplier_id ? `${sanitized.supplier_id} (${typeof sanitized.supplier_id})` : 'غير محدد',
    customer_id: sanitized.customer_id ? `${sanitized.customer_id} (${typeof sanitized.customer_id})` : 'غير محدد',
    items_count: sanitized.items?.length || 0
  })

  return sanitized
}

/**
 * دالة للتحقق من صحة المعرفات المطلوبة
 */
export function validateRequiredIds(data: any, requiredFields: string[]): { isValid: boolean; message?: string } {
  for (const field of requiredFields) {
    const value = data[field]
    const parsedValue = safeParseInt(value, field)
    
    if (parsedValue <= 0) {
      return {
        isValid: false,
        message: `${field} مطلوب ويجب أن يكون رقماً صحيحاً أكبر من صفر`
      }
    }
  }

  return { isValid: true }
}

/**
 * دالة لتسجيل بيانات النموذج للتشخيص
 */
export function logFormData(serviceName: string, operation: string, data: any): void {
  Logger.info(serviceName, `🔍 ${operation} - بيانات النموذج:`, {
    customer_id: data.customer_id ? `${data.customer_id} (${typeof data.customer_id})` : 'غير محدد',
    supplier_id: data.supplier_id ? `${data.supplier_id} (${typeof data.supplier_id})` : 'غير محدد',
    order_id: data.order_id ? `${data.order_id} (${typeof data.order_id})` : 'غير محدد',
    items_count: data.items?.length || 0,
    total_amount: data.total_amount ? `${data.total_amount} (${typeof data.total_amount})` : 'غير محدد'
  })
}
