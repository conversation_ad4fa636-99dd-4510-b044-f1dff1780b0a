import React, { useState, useEffect, useRef } from 'react'
import {
  Card,
  Upload,
  Button,
  Space,
  Image,
  Slider,
  Select,
  Row,
  Col,
  Typography,
  message,
  Modal,
  Tooltip,
  Progress,
  Badge,
  Divider,
  Switch,
  InputNumber,
  Radio,
  ColorPicker
} from 'antd'
import {
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  CopyOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  PictureOutlined,
  SettingOutlined,
  SaveOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { SafeLogger as Logger } from '../../utils/logger'

const { Title, Text } = Typography

// الواجهات
export interface LogoSettings {
  enabled: boolean
  url: string
  size: {
    width: number
    height: number
    maintainAspectRatio: boolean
  }
  position: {
    horizontal: 'left' | 'center' | 'right'
    vertical: 'top' | 'middle' | 'bottom'
    offsetX: number
    offsetY: number
  }
  style: {
    borderRadius: number
    opacity: number
    shadow: boolean
    shadowColor: string
    shadowBlur: number
    border: boolean
    borderColor: string
    borderWidth: number
  }
  background: {
    enabled: boolean
    color: string
    padding: number
  }
  rotation: number
  flip: {
    horizontal: boolean
    vertical: boolean
  }
}

// الإعدادات الافتراضية
const DEFAULT_LOGO_SETTINGS: LogoSettings = {
  enabled: true,
  url: '',
  size: {
    width: 80,
    height: 80,
    maintainAspectRatio: true
  },
  position: {
    horizontal: 'right',
    vertical: 'top',
    offsetX: 0,
    offsetY: 0
  },
  style: {
    borderRadius: 8,
    opacity: 100,
    shadow: false,
    shadowColor: '#000000',
    shadowBlur: 4,
    border: false,
    borderColor: '#d9d9d9',
    borderWidth: 1
  },
  background: {
    enabled: false,
    color: '#ffffff',
    padding: 8
  },
  rotation: 0,
  flip: {
    horizontal: false,
    vertical: false
  }
}

// التصميم
const StyledCard = styled(Card)`
  .ant-card-head {
    background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    border-bottom: none;
    
    .ant-card-head-title {
      color: white;
      font-weight: bold;
    }
  }
`

const LogoPreviewContainer = styled.div`
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  margin: 16px 0;
  background: #fafafa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
`

const LogoPreview = styled.div<{ settings: LogoSettings }>`
  width: ${props => props.settings.size.width}px;
  height: ${props => props.settings.size.height}px;
  border-radius: ${props => props.settings.style.borderRadius}px;
  opacity: ${props => props.settings.style.opacity / 100};
  transform: rotate(${props => props.settings.rotation}deg) 
             scaleX(${props => props.settings.flip.horizontal ? -1 : 1}) 
             scaleY(${props => props.settings.flip.vertical ? -1 : 1});
  border: ${props => props.settings.style.border ? 
    `${props.settings.style.borderWidth}px solid ${props.settings.style.borderColor}` : 'none'};
  box-shadow: ${props => props.settings.style.shadow ? 
    `0 0 ${props.settings.style.shadowBlur}px ${props.settings.style.shadowColor}` : 'none'};
  background-color: ${props => props.settings.background.enabled ? props.settings.background.color : 'transparent'};
  padding: ${props => props.settings.background.enabled ? `${props.settings.background.padding}px` : '0'};
  position: relative;
  transition: all 0.3s ease;
  
  &:hover {
    transform: rotate(${props => props.settings.rotation}deg) 
               scaleX(${props => props.settings.flip.horizontal ? -1.05 : 1.05}) 
               scaleY(${props => props.settings.flip.vertical ? -1.05 : 1.05});
  }
`

const ControlPanel = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
`

interface AdvancedLogoManagerProps {
  onLogoChange?: (settings: LogoSettings) => void
  initialSettings?: Partial<LogoSettings>
}

const AdvancedLogoManager: React.FC<AdvancedLogoManagerProps> = ({
  onLogoChange,
  initialSettings
}) => {
  const [settings, setSettings] = useState<LogoSettings>(DEFAULT_LOGO_SETTINGS)
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [previewVisible, setPreviewVisible] = useState(false)
  const [originalImageSize, setOriginalImageSize] = useState({ width: 0, height: 0 })
  const _fileInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    loadLogoSettings()
  }, [])

  useEffect(() => {
    if (initialSettings) {
      const mergedSettings = { ...DEFAULT_LOGO_SETTINGS, ...initialSettings }
      setSettings(mergedSettings)
    }
  }, [initialSettings])

  // تحميل إعدادات اللوجو
  const loadLogoSettings = async () => {
    setLoading(true)
    try {
      if (window.electronAPI?.getCompanyLogo) {
        const result = await window.electronAPI.getCompanyLogo()
        if (result.success && result.logoPath) {
          setSettings(prev => ({ ...prev, url: result.logoPath }))
        }
      }

      // تحميل الإعدادات المحفوظة
      if (window.electronAPI?.getSettings) {
        const response = await window.electronAPI.getSettings()
        if (response.success && response.data?.logoSettings) {
          const savedSettings = response.data.logoSettings
          setSettings(prev => ({ ...prev, ...savedSettings }))
        }
      }
    } catch (error) {
      Logger.error('AdvancedLogoManager', 'خطأ في تحميل إعدادات اللوجو:', error as Error)
    } finally {
      setLoading(false)
    }
  }

  // حفظ الإعدادات
  const saveSettings = async () => {
    setLoading(true)
    try {
      if (window.electronAPI?.updateSettings) {
        const response = await window.electronAPI.updateSettings({
          logoSettings: settings
        })
        
        if (response.success) {
          onLogoChange?.(settings)
          message.success('تم حفظ إعدادات اللوجو بنجاح')
        } else {
          message.error('فشل في حفظ الإعدادات')
        }
      } else {
        // حفظ محلي كبديل
        localStorage.setItem('logoSettings', JSON.stringify(settings))
        onLogoChange?.(settings)
        message.success('تم حفظ الإعدادات محلياً')
      }
    } catch (error) {
      Logger.error('AdvancedLogoManager', 'خطأ في حفظ الإعدادات:', error as Error)
      message.error('فشل في حفظ الإعدادات')
    } finally {
      setLoading(false)
    }
  }

  // رفع اللوجو
  const handleUpload = async (file: File) => {
    setUploading(true)
    setUploadProgress(0)

    try {
      // محاكاة تقدم الرفع
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 100)

      if (window.electronAPI?.uploadCompanyLogo) {
        const result = await window.electronAPI.uploadCompanyLogo()
        
        clearInterval(progressInterval)
        setUploadProgress(100)
        
        if (result.success) {
          // تحديث URL اللوجو
          setSettings(prev => ({ ...prev, url: result.logoPath }))
          
          // الحصول على أبعاد الصورة الأصلية
          const img = document.createElement('img')
          img.onload = () => {
            setOriginalImageSize({ width: img.width, height: img.height })
            
            // تحديث الحجم بناءً على الصورة الأصلية
            if (settings.size.maintainAspectRatio) {
              const aspectRatio = img.width / img.height
              setSettings(prev => ({
                ...prev,
                size: {
                  ...prev.size,
                  height: Math.round(prev.size.width / aspectRatio)
                }
              }))
            }
          }
          img.src = result.logoPath
          
          message.success('تم رفع اللوجو بنجاح')
        } else {
          message.error('فشل في رفع اللوجو')
        }
      } else {
        // معالجة محلية للملف
        const reader = new FileReader()
        reader.onload = (e) => {
          const dataUrl = e.target?.result as string
          setSettings(prev => ({ ...prev, url: dataUrl }))
          message.success('تم تحميل اللوجو محلياً')
        }
        reader.readAsDataURL(file)
        
        clearInterval(progressInterval)
        setUploadProgress(100)
      }
    } catch (error) {
      Logger.error('AdvancedLogoManager', 'خطأ في رفع اللوجو:', error as Error)
      message.error('حدث خطأ أثناء رفع اللوجو')
    } finally {
      setUploading(false)
      setTimeout(() => setUploadProgress(0), 1000)
    }

    return false // منع الرفع التلقائي
  }

  // حذف اللوجو
  const handleDelete = async () => {
    try {
      if (window.electronAPI?.deleteCompanyLogo) {
        const result = await window.electronAPI.deleteCompanyLogo()
        if (result.success) {
          setSettings(prev => ({ ...prev, url: '' }))
          message.success('تم حذف اللوجو بنجاح')
        }
      } else {
        setSettings(prev => ({ ...prev, url: '' }))
        message.success('تم حذف اللوجو')
      }
    } catch (error) {
      Logger.error('AdvancedLogoManager', 'خطأ في حذف اللوجو:', error as Error)
      message.error('فشل في حذف اللوجو')
    }
  }

  // تحديث الحجم مع الحفاظ على النسبة
  const updateSize = (dimension: 'width' | 'height', value: number) => {
    if (settings.size.maintainAspectRatio && originalImageSize.width && originalImageSize.height) {
      const aspectRatio = originalImageSize.width / originalImageSize.height
      
      if (dimension === 'width') {
        setSettings(prev => ({
          ...prev,
          size: {
            ...prev.size,
            width: value,
            height: Math.round(value / aspectRatio)
          }
        }))
      } else {
        setSettings(prev => ({
          ...prev,
          size: {
            ...prev.size,
            width: Math.round(value * aspectRatio),
            height: value
          }
        }))
      }
    } else {
      setSettings(prev => ({
        ...prev,
        size: {
          ...prev.size,
          [dimension]: value
        }
      }))
    }
  }

  // إعادة تعيين الإعدادات
  const resetSettings = () => {
    setSettings({ ...DEFAULT_LOGO_SETTINGS, url: settings.url })
    message.success('تم إعادة تعيين إعدادات اللوجو')
  }

  return (
    <StyledCard
      title={
        <Space>
          <PictureOutlined />
          إدارة اللوجو المتقدمة
          {settings.enabled && <Badge status="success" text="مفعل" />}
        </Space>
      }
      extra={
        <Space>
          <Tooltip title="معاينة كاملة">
            <Button
              icon={<EyeOutlined />}
              onClick={() => setPreviewVisible(true)}
              disabled={!settings.url}
            />
          </Tooltip>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={loading}
            onClick={saveSettings}
          >
            حفظ
          </Button>
        </Space>
      }
      loading={loading}
    >
      <Row gutter={24}>
        <Col span={12}>
          {/* تحكم في تفعيل اللوجو */}
          <div style={{ marginBottom: 16 }}>
            <Switch
              checked={settings.enabled}
              onChange={(enabled) => setSettings(prev => ({ ...prev, enabled }))}
              checkedChildren="مفعل"
              unCheckedChildren="معطل"
            />
            <Text style={{ marginLeft: 8 }}>تفعيل اللوجو</Text>
          </div>

          {settings.enabled && (
            <>
              {/* رفع اللوجو */}
              <div style={{ marginBottom: 16 }}>
                <Upload
                  accept="image/*"
                  showUploadList={false}
                  beforeUpload={handleUpload}
                  disabled={uploading}
                >
                  <Button
                    icon={<UploadOutlined />}
                    loading={uploading}
                    block
                  >
                    {uploading ? 'جاري الرفع...' : 'رفع لوجو جديد'}
                  </Button>
                </Upload>
                
                {uploadProgress > 0 && (
                  <Progress
                    percent={uploadProgress}
                    size="small"
                    style={{ marginTop: 8 }}
                  />
                )}
              </div>

              {settings.url && (
                <>
                  {/* أزرار التحكم السريع */}
                  <Space wrap style={{ marginBottom: 16 }}>
                    <Button
                      size="small"
                      icon={<RotateLeftOutlined />}
                      onClick={() => setSettings(prev => ({ 
                        ...prev, 
                        rotation: prev.rotation - 90 
                      }))}
                    >
                      دوران يسار
                    </Button>
                    <Button
                      size="small"
                      icon={<RotateRightOutlined />}
                      onClick={() => setSettings(prev => ({ 
                        ...prev, 
                        rotation: prev.rotation + 90 
                      }))}
                    >
                      دوران يمين
                    </Button>
                    <Button
                      size="small"
                      onClick={() => setSettings(prev => ({ 
                        ...prev, 
                        flip: { ...prev.flip, horizontal: !prev.flip.horizontal }
                      }))}
                    >
                      قلب أفقي
                    </Button>
                    <Button
                      size="small"
                      onClick={() => setSettings(prev => ({ 
                        ...prev, 
                        flip: { ...prev.flip, vertical: !prev.flip.vertical }
                      }))}
                    >
                      قلب عمودي
                    </Button>
                    <Button
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={handleDelete}
                    >
                      حذف
                    </Button>
                  </Space>

                  <ControlPanel>
                    {/* الحجم */}
                    <Title level={5}>الحجم والأبعاد</Title>
                    <div style={{ marginBottom: 16 }}>
                      <Switch
                        checked={settings.size.maintainAspectRatio}
                        onChange={(maintainAspectRatio) => 
                          setSettings(prev => ({ 
                            ...prev, 
                            size: { ...prev.size, maintainAspectRatio }
                          }))
                        }
                        size="small"
                      />
                      <Text style={{ marginLeft: 8, fontSize: '12px' }}>
                        الحفاظ على النسبة
                      </Text>
                    </div>
                    
                    <Row gutter={16}>
                      <Col span={12}>
                        <Text>العرض:</Text>
                        <InputNumber
                          min={20}
                          max={300}
                          value={settings.size.width}
                          onChange={(value) => updateSize('width', value || 80)}
                          style={{ width: '100%' }}
                        />
                      </Col>
                      <Col span={12}>
                        <Text>الارتفاع:</Text>
                        <InputNumber
                          min={20}
                          max={300}
                          value={settings.size.height}
                          onChange={(value) => updateSize('height', value || 80)}
                          style={{ width: '100%' }}
                          disabled={settings.size.maintainAspectRatio}
                        />
                      </Col>
                    </Row>

                    <Divider />

                    {/* الموضع */}
                    <Title level={5}>الموضع</Title>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Text>أفقي:</Text>
                        <Select
                          value={settings.position.horizontal}
                          onChange={(horizontal) => 
                            setSettings(prev => ({ 
                              ...prev, 
                              position: { ...prev.position, horizontal }
                            }))
                          }
                          style={{ width: '100%' }}
                        >
                          <Select.Option value="left">يسار</Select.Option>
                          <Select.Option value="center">وسط</Select.Option>
                          <Select.Option value="right">يمين</Select.Option>
                        </Select>
                      </Col>
                      <Col span={12}>
                        <Text>عمودي:</Text>
                        <Select
                          value={settings.position.vertical}
                          onChange={(vertical) => 
                            setSettings(prev => ({ 
                              ...prev, 
                              position: { ...prev.position, vertical }
                            }))
                          }
                          style={{ width: '100%' }}
                        >
                          <Select.Option value="top">أعلى</Select.Option>
                          <Select.Option value="middle">وسط</Select.Option>
                          <Select.Option value="bottom">أسفل</Select.Option>
                        </Select>
                      </Col>
                    </Row>

                    <Divider />

                    {/* التأثيرات */}
                    <Title level={5}>التأثيرات</Title>
                    
                    <div style={{ marginBottom: 12 }}>
                      <Text>الشفافية: {settings.style.opacity}%</Text>
                      <Slider
                        min={10}
                        max={100}
                        value={settings.style.opacity}
                        onChange={(opacity) => 
                          setSettings(prev => ({ 
                            ...prev, 
                            style: { ...prev.style, opacity }
                          }))
                        }
                      />
                    </div>

                    <div style={{ marginBottom: 12 }}>
                      <Text>الدوران: {settings.rotation}°</Text>
                      <Slider
                        min={-180}
                        max={180}
                        value={settings.rotation}
                        onChange={(rotation) => setSettings(prev => ({ ...prev, rotation }))}
                      />
                    </div>

                    <div style={{ marginBottom: 12 }}>
                      <Text>انحناء الزوايا: {settings.style.borderRadius}px</Text>
                      <Slider
                        min={0}
                        max={50}
                        value={settings.style.borderRadius}
                        onChange={(borderRadius) => 
                          setSettings(prev => ({ 
                            ...prev, 
                            style: { ...prev.style, borderRadius }
                          }))
                        }
                      />
                    </div>

                    {/* الحدود والظلال */}
                    <div style={{ marginBottom: 12 }}>
                      <Switch
                        checked={settings.style.border}
                        onChange={(border) => 
                          setSettings(prev => ({ 
                            ...prev, 
                            style: { ...prev.style, border }
                          }))
                        }
                        size="small"
                      />
                      <Text style={{ marginLeft: 8 }}>إضافة حد</Text>
                    </div>

                    <div style={{ marginBottom: 12 }}>
                      <Switch
                        checked={settings.style.shadow}
                        onChange={(shadow) => 
                          setSettings(prev => ({ 
                            ...prev, 
                            style: { ...prev.style, shadow }
                          }))
                        }
                        size="small"
                      />
                      <Text style={{ marginLeft: 8 }}>إضافة ظل</Text>
                    </div>

                    <div style={{ marginBottom: 12 }}>
                      <Switch
                        checked={settings.background.enabled}
                        onChange={(enabled) => 
                          setSettings(prev => ({ 
                            ...prev, 
                            background: { ...prev.background, enabled }
                          }))
                        }
                        size="small"
                      />
                      <Text style={{ marginLeft: 8 }}>خلفية ملونة</Text>
                    </div>

                    <Button
                      block
                      onClick={resetSettings}
                      style={{ marginTop: 16 }}
                    >
                      إعادة تعيين
                    </Button>
                  </ControlPanel>
                </>
              )}
            </>
          )}
        </Col>

        <Col span={12}>
          <Title level={4}>معاينة مباشرة</Title>
          <LogoPreviewContainer>
            {settings.enabled && settings.url ? (
              <LogoPreview settings={settings}>
                <Image
                  src={settings.url}
                  alt="لوجو الشركة"
                  width="100%"
                  height="100%"
                  style={{ objectFit: 'contain' }}
                  preview={false}
                />
              </LogoPreview>
            ) : (
              <div style={{ 
                textAlign: 'center', 
                color: '#999',
                fontSize: '16px'
              }}>
                <PictureOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <div>
                  {settings.enabled ? 'لم يتم رفع لوجو بعد' : 'اللوجو معطل'}
                </div>
              </div>
            )}
          </LogoPreviewContainer>
        </Col>
      </Row>

      {/* نافذة المعاينة الكاملة */}
      <Modal
        title="معاينة اللوجو"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={800}
      >
        <div style={{ 
          textAlign: 'center', 
          padding: '40px',
          background: '#f5f5f5',
          borderRadius: '8px'
        }}>
          {settings.url && (
            <LogoPreview settings={settings}>
              <Image
                src={settings.url}
                alt="لوجو الشركة"
                width="100%"
                height="100%"
                style={{ objectFit: 'contain' }}
              />
            </LogoPreview>
          )}
        </div>
      </Modal>
    </StyledCard>
  )
}

export default AdvancedLogoManager
