import React from 'react'
import { Tag } from 'antd'
import dayjs from 'dayjs'
import ReportBase, { ReportData, ReportFilters } from './ReportBase'
import { logger as Logger } from '../../utils/logger'

const CustomerAgingReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: ReportFilters): Promise<ReportData> => {
    try {
      Logger.info('CustomerAgingReport', 'بدء إنشاء تقرير أعمار ديون العملاء')

      // استعلام البيانات من قاعدة البيانات
      const response = await (window as any).electronAPI.getCustomerAgingReport({
        customerId: filters.customerId,
        asOfDate: filters.dateRange?.[1] && typeof filters.dateRange[1] === 'object' && 'format' in filters.dateRange[1]
          ? (filters.dateRange[1] as any).format('YYYY-MM-DD')
          : dayjs().format('YYYY-MM-DD')
      })

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة')
      }

      const agingData = response.data

      // تعريف الأعمدة
      const columns = [
        {
          title: 'العميل',
          format: 'text',
          filterable: true,
          key: 'customer_name',
          width: 200,
          fixed: 'left' as const,
          render: (text: string, record: any) => (
            <div>
              <div style={{ fontWeight: 'bold', color: '#1890ff' }}>{text}</div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                كود: {record.customer_code || 'غير محدد'}
              </div>
            </div>
          )
        },
        {
          title: 'إجمالي المديونية',
          format: 'currency',
          sortable: true,
          key: 'total_outstanding',
          width: 120,
          align: 'center' as const,
          render: (record: any) => (
            <span style={{ fontWeight: 'bold', color: record.total_outstanding > 0 ? '#f5222d' : '#52c41a' }}>
              {(record.total_outstanding || 0).toLocaleString('ar-EG')} ج.م
            </span>
          )
        },
        {
          title: 'جاري (0-30 يوم)',
          format: 'currency',
          sortable: true,
          key: 'current_0_30',
          width: 120,
          align: 'center' as const,
          render: (record: any) => (
            <span style={{ color: '#52c41a' }}>
              {(record.current_0_30 || 0).toLocaleString('ar-EG')} ج.م
            </span>
          )
        },
        {
          title: '31-60 يوم',
          format: 'currency',
          sortable: true,
          key: 'days_31_60',
          width: 120,
          align: 'center' as const,
          render: (record: any) => (
            <span style={{ color: '#faad14' }}>
              {(record.days_31_60 || 0).toLocaleString('ar-EG')} ج.م
            </span>
          )
        },
        {
          title: '61-90 يوم',
          format: 'currency',
          sortable: true,
          key: 'days_61_90',
          width: 120,
          align: 'center' as const,
          render: (record: any) => (
            <span style={{ color: '#fa8c16' }}>
              {(record.days_61_90 || 0).toLocaleString('ar-EG')} ج.م
            </span>
          )
        },
        {
          title: '91-120 يوم',
          format: 'currency',
          sortable: true,
          key: 'days_91_120',
          width: 120,
          align: 'center' as const,
          render: (record: any) => (
            <span style={{ color: '#f5222d' }}>
              {(record.days_91_120 || 0).toLocaleString('ar-EG')} ج.م
            </span>
          )
        },
        {
          title: 'أكثر من 120 يوم',
          format: 'currency',
          sortable: true,
          key: 'over_120',
          width: 130,
          align: 'center' as const,
          render: (record: any) => (
            <span style={{ color: '#a8071a', fontWeight: 'bold' }}>
              {(record.over_120 || 0).toLocaleString('ar-EG')} ج.م
            </span>
          )
        },
        {
          title: 'أقدم فاتورة',
          format: 'date',
          sortable: true,
          key: 'oldest_invoice_date',
          width: 120,
          align: 'center' as const,
          render: (record: any) => {
            const date = record.oldest_invoice_date;
            if (!date) return '-'
            const invoiceDate = dayjs(date)
            const daysOld = dayjs().diff(invoiceDate, 'day')
            return (
              <div>
                <div>{invoiceDate.format('YYYY-MM-DD')}</div>
                <Tag color={daysOld > 90 ? 'red' : daysOld > 60 ? 'orange' : daysOld > 30 ? 'yellow' : 'green'}>
                  {daysOld} يوم
                </Tag>
              </div>
            )
          }
        },
        {
          title: 'الحد الائتماني',
          format: 'currency',
          sortable: true,
          key: 'credit_limit',
          width: 120,
          align: 'center' as const,
          render: (record: any) => {
            const value = record.credit_limit;
            const utilizationPercent = value > 0 ? (record.total_outstanding / value) * 100 : 0
            return (
              <div>
                <div>{(value || 0).toLocaleString('ar-EG')} ج.م</div>
                <Tag color={utilizationPercent > 90 ? 'red' : utilizationPercent > 70 ? 'orange' : 'green'}>
                  {utilizationPercent.toFixed(1)}%
                </Tag>
              </div>
            )
          }
        },
        {
          title: 'حالة الائتمان',
          key: 'credit_status',
          width: 120,
          align: 'center' as const,
          render: (_: any, record: any) => {
            const utilizationPercent = record.credit_limit > 0 ? (record.total_outstanding / record.credit_limit) * 100 : 0
            const hasOverdue = record.over_120 > 0 || record.days_91_120 > 0
            
            if (hasOverdue) {
              return <Tag color="red">متأخر</Tag>
            } else if (utilizationPercent > 90) {
              return <Tag color="orange">تحذير</Tag>
            } else if (utilizationPercent > 70) {
              return <Tag color="yellow">مراقبة</Tag>
            } else {
              return <Tag color="green">جيد</Tag>
            }
          }
        }
      ]

      // إعداد البيانات للجدول
      const tableData = agingData.map((item: any, index: number) => ({
        ...item,
        key: item.customer_id || index
      }))

      // حساب الإحصائيات
      const totalCustomers = agingData.length
      const totalOutstanding = agingData.reduce((sum: number, item: any) => sum + (item.total_outstanding || 0), 0)
      const totalCurrent = agingData.reduce((sum: number, item: any) => sum + (item.current_0_30 || 0), 0)
      const _total31_60 = agingData.reduce((sum: number, item: any) => sum + (item.days_31_60 || 0), 0)
      const total61_90 = agingData.reduce((sum: number, item: any) => sum + (item.days_61_90 || 0), 0)
      const total91_120 = agingData.reduce((sum: number, item: any) => sum + (item.days_91_120 || 0), 0)
      const totalOver120 = agingData.reduce((sum: number, item: any) => sum + (item.over_120 || 0), 0)
      
      const customersWithOverdue = agingData.filter((item: any) => 
        (item.over_120 || 0) > 0 || (item.days_91_120 || 0) > 0 || (item.days_61_90 || 0) > 0
      ).length

      const statistics = [
        {
          title: 'إجمالي العملاء',
          value: totalCustomers.toLocaleString('ar-EG'),
          color: '#1890ff',
          icon: '👥'
        },
        {
          title: 'إجمالي المديونيات',
          value: `${totalOutstanding.toLocaleString('ar-EG')} ج.م`,
          color: '#f5222d',
          icon: '💰'
        },
        {
          title: 'المديونيات الجارية (0-30)',
          value: `${totalCurrent.toLocaleString('ar-EG')} ج.م`,
          color: '#52c41a',
          icon: '✅'
        },
        {
          title: 'المديونيات المتأخرة (+60)',
          value: `${(total61_90 + total91_120 + totalOver120).toLocaleString('ar-EG')} ج.م`,
          color: '#f5222d',
          icon: '⚠️'
        },
        {
          title: 'عملاء بمديونيات متأخرة',
          value: customersWithOverdue.toLocaleString('ar-EG'),
          color: '#fa8c16',
          icon: '🚨'
        },
        {
          title: 'نسبة التحصيل',
          value: `${totalOutstanding > 0 ? ((totalCurrent / totalOutstanding) * 100).toFixed(1) : 0}%`,
          color: '#722ed1',
          icon: '📊'
        }
      ]

      return {
        title: 'تقرير أعمار ديون العملاء',
        columns,
        data: tableData,
        statistics,
        summary: {
          totalRecords: totalCustomers,
          totalOutstanding: totalOutstanding,
          totalCurrent: totalCurrent,
          totalOverdue: total61_90 + total91_120 + totalOver120
        }
      }

    } catch (error) {
      Logger.error('CustomerAgingReport', 'خطأ في إنشاء تقرير أعمار ديون العملاء:', error as Error)
      throw new Error('فشل في تحميل بيانات التقرير')
    }
  }

  return (
    <ReportBase
      title="تقرير أعمار ديون العملاء"
      generateReport={generateReport}
      showDateRange={true}
    />
  )
}

export default CustomerAgingReport
