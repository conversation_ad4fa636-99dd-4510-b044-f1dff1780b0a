/**
 * تقرير استهلاك المواد في الإنتاج
 * تقرير شامل لاستهلاك المواد والخامات في عمليات الإنتاج
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const ProductionMaterialsConsumptionReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'production_materials_consumption' as ReportType}
      title="تقرير استهلاك المواد في الإنتاج"
      description="تقرير مفصل لاستهلاك المواد والخامات في عمليات الإنتاج مع تحليل التكاليف والكفاءة"
      showDateRange={true}
      showDepartmentFilter={true}
      showItemFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="production_materials_consumption_report"
      defaultFilters={{
        sortBy: 'consumption_amount',
        sortOrder: 'desc'
      }}
    />
  )
}

export default ProductionMaterialsConsumptionReport
