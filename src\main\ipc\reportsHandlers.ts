import { ipcMain } from 'electron'
import { FinancialService } from '../services/FinancialService'
import { ReportsService } from '../services/ReportsService'

/**
 * تسجيل معالجات IPC للتقارير المتقدمة
 */
export function registerReportsHandlers(): void {
  const financialService = FinancialService.getInstance()
  const reportsService = new ReportsService()

  // ===== التقارير المالية =====

  /**
   * إنشاء الميزانية العمومية
   */
  ipcMain.handle('generate-balance-sheet', async (event, asOfDate: string) => {
    try {
      console.log('طلب إنشاء الميزانية العمومية:', asOfDate)
      const result = financialService.generateBalanceSheet(asOfDate)
      console.log('نتيجة الميزانية العمومية:', result.success)
      return result
    } catch (error) {
      console.error('خطأ في معالج الميزانية العمومية:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء إنشاء الميزانية العمومية'
      }
    }
  })

  /**
   * إنشاء قائمة الدخل
   */
  ipcMain.handle('generate-income-statement', async (event, params: { fromDate: string, toDate: string }) => {
    try {
      console.log('طلب إنشاء قائمة الدخل:', params)
      const result = financialService.generateIncomeStatement(params.fromDate, params.toDate)
      console.log('نتيجة قائمة الدخل:', result.success)
      return result
    } catch (error) {
      console.error('خطأ في معالج قائمة الدخل:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء إنشاء قائمة الدخل'
      }
    }
  })

  /**
   * إنشاء قائمة التدفق النقدي
   */
  ipcMain.handle('generate-cash-flow-statement', async (event, params: { fromDate: string, toDate: string, method: 'direct' | 'indirect' }) => {
    try {
      console.log('طلب إنشاء قائمة التدفق النقدي:', params)
      const result = financialService.generateCashFlowStatement(params.fromDate, params.toDate, params.method)
      console.log('نتيجة قائمة التدفق النقدي:', result.success)
      return result
    } catch (error) {
      console.error('خطأ في معالج قائمة التدفق النقدي:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء إنشاء قائمة التدفق النقدي'
      }
    }
  })

  /**
   * إنشاء مطابقة البنك
   */
  ipcMain.handle('generate-bank-reconciliation', async (event, params: { bankAccountId: number, asOfDate: string }) => {
    try {
      console.log('طلب إنشاء مطابقة البنك:', params)
      const result = financialService.generateBankReconciliation(params.bankAccountId, params.asOfDate)
      console.log('نتيجة مطابقة البنك:', result.success)
      return result
    } catch (error) {
      console.error('خطأ في معالج مطابقة البنك:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء إنشاء مطابقة البنك'
      }
    }
  })

  /**
   * الحصول على فئات المنتجات
   */
  ipcMain.handle('get-product-categories', async () => {
    try {
      console.log('طلب الحصول على فئات المنتجات')
      const result = await financialService.getProductCategories()
      console.log('نتيجة فئات المنتجات:', result.length, 'فئة')
      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('خطأ في معالج فئات المنتجات:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء جلب فئات المنتجات'
      }
    }
  })

  // ===== تقارير المبيعات =====

  /**
   * تقرير المرتجعات والخصومات
   */
  ipcMain.handle('get-sales-returns-and-discounts', async (_, params: { fromDate: string, toDate: string, reportType: string }) => {
    try {
      console.log('طلب تقرير المرتجعات والخصومات:', params)
      const result = reportsService.getSalesReturnsAndDiscounts(params)
      console.log('نتيجة تقرير المرتجعات والخصومات:', result.success)
      return result
    } catch (error) {
      console.error('خطأ في معالج تقرير المرتجعات والخصومات:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء تحميل تقرير المرتجعات والخصومات'
      }
    }
  })

  /**
   * تقرير المبيعات حسب المنطقة
   */
  ipcMain.handle('get-sales-by-region', async (_, params: { fromDate: string, toDate: string, sortBy: string }) => {
    try {
      console.log('طلب تقرير المبيعات حسب المنطقة:', params)
      const result = reportsService.getSalesByRegion(params)
      console.log('نتيجة تقرير المبيعات حسب المنطقة:', result.success)
      return result
    } catch (error) {
      console.error('خطأ في معالج تقرير المبيعات حسب المنطقة:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء تحميل تقرير المبيعات حسب المنطقة'
      }
    }
  })

  /**
   * تقرير العملاء الأكثر ربحية
   */
  ipcMain.handle('get-top-profitable-customers', async (_, params: { fromDate: string, toDate: string, analysisType: string, topCount: number }) => {
    try {
      console.log('طلب تقرير العملاء الأكثر ربحية:', params)
      const result = reportsService.getTopProfitableCustomers(params)
      console.log('نتيجة تقرير العملاء الأكثر ربحية:', result.success)
      return result
    } catch (error) {
      console.error('خطأ في معالج تقرير العملاء الأكثر ربحية:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء تحميل تقرير العملاء الأكثر ربحية'
      }
    }
  })

  // ===== تقارير المشتريات =====

  /**
   * تقرير مقارنة أسعار الموردين
   */
  ipcMain.handle('get-supplier-price-comparison', async (_, params: { fromDate: string, toDate: string, category: string }) => {
    try {
      console.log('طلب تقرير مقارنة أسعار الموردين:', params)
      const result = reportsService.getSupplierPriceComparison(params)
      console.log('نتيجة تقرير مقارنة أسعار الموردين:', result.success)
      return result
    } catch (error) {
      console.error('خطأ في معالج تقرير مقارنة أسعار الموردين:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء تحميل تقرير مقارنة أسعار الموردين'
      }
    }
  })

  /**
   * تقرير جودة الموردين
   */
  ipcMain.handle('get-supplier-quality-report', async (_, params: { fromDate: string, toDate: string, sortBy: string, riskFilter: string }) => {
    try {
      console.log('طلب تقرير جودة الموردين:', params)
      const result = reportsService.getSupplierQualityReport(params)
      console.log('نتيجة تقرير جودة الموردين:', result.success)
      return result
    } catch (error) {
      console.error('خطأ في معالج تقرير جودة الموردين:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء تحميل تقرير جودة الموردين'
      }
    }
  })

  console.log('تم تسجيل معالجات التقارير المتقدمة بنجاح')
}
