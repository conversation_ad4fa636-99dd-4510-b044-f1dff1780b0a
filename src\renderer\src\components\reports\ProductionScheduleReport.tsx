/**
 * تقرير جدولة الإنتاج المحسن
 * تقرير شامل لجدولة الإنتاج باستخدام النظام الموحد
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const ProductionScheduleReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'production_schedule' as ReportType}
      title="تقرير جدولة الإنتاج"
      description="تقرير مفصل لجدولة الإنتاج مع متابعة المواعيد والتقدم والأولويات"
      showDateRange={true}
      showDepartmentFilter={true}
      showStatusFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="production_schedule_report"
      defaultFilters={{
        sortBy: 'expected_completion_date',
        sortOrder: 'asc'
      }}
    />
  )
}

export default ProductionScheduleReport
