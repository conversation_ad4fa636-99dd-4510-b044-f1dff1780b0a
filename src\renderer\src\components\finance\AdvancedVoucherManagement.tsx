import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Select, 
  Input, 
  Space, 
  message, 
  Row,
  Col,
  Statistic,
  DatePicker,
  Tag,
  Descriptions,
  InputNumber,
  Divider,
  Tabs,
  Alert,
  Tooltip,
  Badge
} from 'antd'
import {
  ArrowLeftOutlined,
  PlusOutlined,
  CheckOutlined,
  <PERSON>UpOutlined,
  ArrowDownOutlined,
  LinkOutlined,
  EyeOutlined,
  PrinterOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'
import dayjs from 'dayjs'
import { UnifiedPrintButton } from '../common'

const { Option } = Select
const { TextArea } = Input

interface AdvancedVoucherManagementProps {
  onBack: () => void
}

const AdvancedVoucherManagement: React.FC<AdvancedVoucherManagementProps> = ({ onBack }) => {
  const [paymentVouchers, setPaymentVouchers] = useState([])
  const [receiptVouchers, setReceiptVouchers] = useState([])
  const [bankAccounts, setBankAccounts] = useState([])
  const [unpaidInvoices, setUnpaidInvoices] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [detailsModalVisible, setDetailsModalVisible] = useState(false)
  const [selectedVoucher, setSelectedVoucher] = useState<any>(null)
  const [voucherType, setVoucherType] = useState<'payment' | 'receipt'>('payment')
  const [activeTab, setActiveTab] = useState('payment')
  const [form] = Form.useForm()

  useEffect(() => {
    loadVouchers()
    loadBankAccounts()
    loadUnpaidInvoices()
  }, [])

  const loadVouchers = async () => {
    setLoading(true)
    try {
      const [paymentResponse, receiptResponse] = await Promise.all([
        window.electronAPI.getPaymentVouchers(),
        window.electronAPI.getReceiptVouchers()
      ])

      if (paymentResponse.success) {
        setPaymentVouchers(paymentResponse.data)
      }
      if (receiptResponse.success) {
        setReceiptVouchers(receiptResponse.data)
      }
    } catch (error) {
      message.error('خطأ في تحميل السندات')
    }
    setLoading(false)
  }

  const loadBankAccounts = async () => {
    try {
      const response = await window.electronAPI.getBankAccounts()
      if (response.success) {
        setBankAccounts(response.data)
      }
    } catch (error) {
      Logger.error('AdvancedVoucherManagement', 'خطأ في تحميل الحسابات المصرفية:', error)
    }
  }

  const loadUnpaidInvoices = async () => {
    try {
      const response = await window.electronAPI.getUnpaidInvoices()
      if (response.success) {
        setUnpaidInvoices(response.data)
      }
    } catch (error) {
      Logger.error('AdvancedVoucherManagement', 'خطأ في تحميل الفواتير غير المدفوعة:', error)
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      const voucherData = {
        ...values,
        payment_date: values.payment_date?.format('YYYY-MM-DD'),
        receipt_date: values.receipt_date?.format('YYYY-MM-DD'),
        created_by: 1
      }

      const response = voucherType === 'payment'
        ? await window.electronAPI.createPaymentVoucher(voucherData)
        : await window.electronAPI.createReceiptVoucher(voucherData)

      if (response.success) {
        // ربط السند بالفاتورة إذا تم تحديدها
        if (values.reference_invoice_id) {
          await window.electronAPI.linkInvoiceToPayment(response.voucherId)
        }

        message.success(`تم إضافة ${voucherType === 'payment' ? 'سند الدفع' : 'سند القبض'} بنجاح`)
        loadVouchers()
        loadUnpaidInvoices()
        setModalVisible(false)
        form.resetFields()
      } else {
        message.error(`فشل في إضافة ${voucherType === 'payment' ? 'سند الدفع' : 'سند القبض'}`)
      }
    } catch (error) {
      message.error('خطأ في حفّ السند')
    }
  }

  const generateVoucherNumber = async (type: 'payment' | 'receipt') => {
    try {
      const response = type === 'payment' 
        ? await window.electronAPI.generatePaymentVoucherNumber()
        : await window.electronAPI.generateReceiptVoucherNumber()
      
      if (response.success) {
        form.setFieldsValue({ voucher_number: response.data.voucherNumber })
      }
    } catch (error) {
      message.error('خطأ في إنشاء رقم السند')
    }
  }

  const showVoucherDetails = (voucher: any) => {
    setSelectedVoucher(voucher)
    setDetailsModalVisible(true)
  }

  const showModal = (type: 'payment' | 'receipt') => {
    setVoucherType(type)
    setModalVisible(true)
    form.resetFields()
    form.setFieldsValue({
      [type === 'payment' ? 'payment_date' : 'receipt_date']: dayjs()
    })
    generateVoucherNumber(type)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'paid': return 'green'
      case 'received': return 'green'
      case 'cancelled': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتّار'
      case 'paid': return 'مدفوع'
      case 'received': return 'مستلم'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return 'نقداً'
      case 'check': return 'شيك'
      case 'bank_transfer': return 'تحويل بنكي'
      default: return method
    }
  }

  const getReferenceTypeText = (type: string) => {
    switch (type) {
      case 'sales_invoice': return 'فاتورة مبيعات'
      case 'purchase_invoice': return 'فاتورة مشتريات'
      case 'paint_invoice': return 'فاتورة دهان'
      case 'production_cost': return 'تكلفة إنتاج'
      default: return type || 'غير محدد'
    }
  }

  const paymentColumns = [
    {
      title: 'رقم السند',
      dataIndex: 'voucher_number',
      key: 'voucher_number',
      render: (number: string) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{number}</span>
      )
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
          ₪ {amount?.toLocaleString() || 0}
        </span>
      )
    },
    {
      title: 'المستفيد',
      dataIndex: 'payee_name',
      key: 'payee_name',
    },
    {
      title: 'تاريخ الدفع',
      dataIndex: 'payment_date',
      key: 'payment_date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'طريقة الدفع',
      dataIndex: 'payment_method',
      key: 'payment_method',
      render: (method: string) => (
        <Tag color="blue">{getPaymentMethodText(method)}</Tag>
      )
    },
    {
      title: 'المرجع',
      dataIndex: 'reference_type',
      key: 'reference_type',
      render: (type: string, record: any) => (
        type ? (
          <Tooltip title={`رقم المرجع: ${record.reference_id}`}>
            <Tag color="purple" icon={<LinkOutlined />}>
              {getReferenceTypeText(type)}
            </Tag>
          </Tooltip>
        ) : (
          <Tag color="default">غير مربوط</Tag>
        )
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: any) => (
        <Space>
          <Tooltip title="عرض التفاصيل">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => showVoucherDetails(record)}
            />
          </Tooltip>
          <UnifiedPrintButton
            data={{
              id: record.id,
              title: record.voucher_type === 'payment' ? 'سند دفع متقدم' : 'سند قبض متقدم',
              subtitle: `رقم: ${record.voucher_number}`,
              date: record.voucher_date,
              customer: {
                name: record.payee_name || 'غير محدد'
              },
              items: [{
                id: 1,
                name: record.voucher_type === 'payment' ? 'مبلغ مدفوع' : 'مبلغ مقبوض',
                description: record.description || '',
                quantity: 1,
                unit: 'مبلغ',
                unitPrice: record.amount || 0,
                total: record.amount || 0
              }],
              total: record.amount || 0,
              notes: record.notes || ''
            }}
            type="receipt"
            subType={record.voucher_type}
            buttonText=""
            size="small"
            showDropdown={true}
            _documentId={`advanced_voucher_${record.id}`}
            onAfterPrint={() => message.success('تم طباعة السند بنجاح')}
            onError={() => message.error('فشل في طباعة السند')}
          />
        </Space>
      )
    }
  ]

  const receiptColumns = [
    {
      title: 'رقم السند',
      dataIndex: 'voucher_number',
      key: 'voucher_number',
      render: (number: string) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{number}</span>
      )
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ₪ {amount?.toLocaleString() || 0}
        </span>
      )
    },
    {
      title: 'الدافع',
      dataIndex: 'payer_name',
      key: 'payer_name',
    },
    {
      title: 'تاريخ القبض',
      dataIndex: 'receipt_date',
      key: 'receipt_date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'طريقة الدفع',
      dataIndex: 'payment_method',
      key: 'payment_method',
      render: (method: string) => (
        <Tag color="green">{getPaymentMethodText(method)}</Tag>
      )
    },
    {
      title: 'المرجع',
      dataIndex: 'reference_type',
      key: 'reference_type',
      render: (type: string, record: any) => (
        type ? (
          <Tooltip title={`رقم المرجع: ${record.reference_id}`}>
            <Tag color="purple" icon={<LinkOutlined />}>
              {getReferenceTypeText(type)}
            </Tag>
          </Tooltip>
        ) : (
          <Tag color="default">غير مربوط</Tag>
        )
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: any) => (
        <Space>
          <Tooltip title="عرض التفاصيل">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => showVoucherDetails(record)}
            />
          </Tooltip>
          <UnifiedPrintButton
            data={{
              id: record.id,
              title: record.voucher_type === 'payment' ? 'سند دفع متقدم' : 'سند قبض متقدم',
              subtitle: `رقم: ${record.voucher_number}`,
              date: record.voucher_date,
              customer: {
                name: record.payee_name || 'غير محدد'
              },
              items: [{
                id: 1,
                name: record.voucher_type === 'payment' ? 'مبلغ مدفوع' : 'مبلغ مقبوض',
                description: record.description || '',
                quantity: 1,
                unit: 'مبلغ',
                unitPrice: record.amount || 0,
                total: record.amount || 0
              }],
              total: record.amount || 0,
              notes: record.notes || ''
            }}
            type="receipt"
            subType={record.voucher_type}
            buttonText=""
            size="small"
            showDropdown={true}
            _documentId={`advanced_voucher_${record.id}`}
            onAfterPrint={() => message.success('تم طباعة السند بنجاح')}
            onError={() => message.error('فشل في طباعة السند')}
          />
        </Space>
      )
    }
  ]

  const paymentStats = {
    total: paymentVouchers.length,
    pending: paymentVouchers.filter((v: any) => v.status === 'pending').length,
    paid: paymentVouchers.filter((v: any) => v.status === 'paid').length,
    totalAmount: paymentVouchers.reduce((sum: number, v: any) => sum + (v.amount || 0), 0),
    linkedCount: paymentVouchers.filter((v: any) => v.reference_type).length
  }

  const receiptStats = {
    total: receiptVouchers.length,
    pending: receiptVouchers.filter((v: any) => v.status === 'pending').length,
    received: receiptVouchers.filter((v: any) => v.status === 'received').length,
    totalAmount: receiptVouchers.reduce((sum: number, v: any) => sum + (v.amount || 0), 0),
    linkedCount: receiptVouchers.filter((v: any) => v.reference_type).length
  }

  const unlinkedVouchersCount = paymentVouchers.filter((v: any) => !v.reference_type).length + 
                                receiptVouchers.filter((v: any) => !v.reference_type).length

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>📋 نّام السندات المتقدم</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            إدارة متقدمة لسندات الدفع والقبض مع الربط التلقائي بالفواتير
          </p>
        </div>
        <Button 
          type="default" 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
        >
          العودة
        </Button>
      </div>

      {/* تنبيه السندات غير المربوطة */}
      {unlinkedVouchersCount > 0 && (
        <Alert
          message={`تنبيه: يوجد ${unlinkedVouchersCount} سند غير مربوط بفاتورة`}
          description="يُنصح بربط السندات بالفواتير المقابلة لها لضمان دقة التقارير المالية"
          type="warning"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="سندات الدفع"
              value={paymentStats.total}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<ArrowUpOutlined />}
              suffix={
                <Badge 
                  count={paymentStats.linkedCount} 
                  style={{ backgroundColor: '#52c41a' }}
                  title="السندات المربوطة"
                />
              }
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="سندات القبض"
              value={receiptStats.total}
              valueStyle={{ color: '#52c41a' }}
              prefix={<ArrowDownOutlined />}
              suffix={
                <Badge 
                  count={receiptStats.linkedCount} 
                  style={{ backgroundColor: '#52c41a' }}
                  title="السندات المربوطة"
                />
              }
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي المدفوعات"
              value={paymentStats.totalAmount}
              valueStyle={{ color: '#ff4d4f' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي المقبوضات"
              value={receiptStats.totalAmount}
              valueStyle={{ color: '#52c41a' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="صافي التدفق"
              value={receiptStats.totalAmount - paymentStats.totalAmount}
              valueStyle={{ 
                color: receiptStats.totalAmount >= paymentStats.totalAmount ? '#52c41a' : '#ff4d4f' 
              }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="فواتير غير مدفوعة"
              value={unpaidInvoices.length}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* جداول السندات */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'payment',
              label: (
                <span>
                  <ArrowUpOutlined style={{ color: '#ff4d4f' }} />
                  سندات الدفع ({paymentStats.total})
                </span>
              ),
              children: (
                <div>
                  <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => showModal('payment')}
                    >
                      إضافة سند دفع
                    </Button>
                    <Space>
                      <Button onClick={loadVouchers}>تحديث</Button>
                    </Space>
                  </div>
                  <Table
                    columns={paymentColumns}
                    dataSource={paymentVouchers}
                    rowKey="id"
                    loading={loading}
                    pagination={{ pageSize: 10 }}
                    locale={{
                      emptyText: paymentVouchers.length === 0 ? 'لا توجد سندات دفع' : 'لا توجد بيانات'
                    }}
                  />
                </div>
              )
            },
            {
              key: 'receipt',
              label: (
                <span>
                  <ArrowDownOutlined style={{ color: '#52c41a' }} />
                  سندات القبض ({receiptStats.total})
                </span>
              ),
              children: (
                <div>
                  <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => showModal('receipt')}
                    >
                      إضافة سند قبض
                    </Button>
                    <Space>
                      <Button onClick={loadVouchers}>تحديث</Button>
                    </Space>
                  </div>
                  <Table
                    columns={receiptColumns}
                    dataSource={receiptVouchers}
                    rowKey="id"
                    loading={loading}
                    pagination={{ pageSize: 10 }}
                    locale={{
                      emptyText: receiptVouchers.length === 0 ? 'لا توجد سندات قبض' : 'لا توجد بيانات'
                    }}
                  />
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* نموذج إضافة سند */}
      <Modal
        title={`${voucherType === 'payment' ? 'إضافة سند دفع جديد' : 'إضافة سند قبض جديد'}`}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="voucher_number"
                label="رقم السند"
                rules={[{ required: true, message: 'يرجى إدخال رقم السند' }]}
              >
                <Input
                  placeholder={voucherType === 'payment' ? 'PV000001' : 'RV000001'}
                  disabled
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="amount"
                label="المبلغ"
                rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  formatter={value => `₪ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value: string | undefined) => {
                    if (!value) return 0;
                    const parsed = parseFloat(value.replace(/₪\s?|(,*)/g, ''));
                    return isNaN(parsed) ? 0 : parsed;
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={voucherType === 'payment' ? 'payee_name' : 'payer_name'}
                label={voucherType === 'payment' ? 'اسم المستفيد' : 'اسم الدافع'}
                rules={[{ required: true, message: `يرجى إدخال ${voucherType === 'payment' ? 'اسم المستفيد' : 'اسم الدافع'}` }]}
              >
                <Input placeholder={voucherType === 'payment' ? 'اسم الشخص أو الشركة المستفيدة' : 'اسم الشخص أو الشركة الدافعة'} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={voucherType === 'payment' ? 'payment_date' : 'receipt_date'}
                label={voucherType === 'payment' ? 'تاريخ الدفع' : 'تاريخ القبض'}
                rules={[{ required: true, message: `يرجى اختيار ${voucherType === 'payment' ? 'تاريخ الدفع' : 'تاريخ القبض'}` }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder={`اختر ${voucherType === 'payment' ? 'تاريخ الدفع' : 'تاريخ القبض'}`}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="payment_method"
                label="طريقة الدفع"
                rules={[{ required: true, message: 'يرجى اختيار طريقة الدفع' }]}
              >
                <Select placeholder="اختر طريقة الدفع">
                  <Option value="cash">نقداً</Option>
                  <Option value="check">شيك</Option>
                  <Option value="bank_transfer">تحويل بنكي</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="bank_account_id"
                label="الحساب المصرفي (اختياري)"
              >
                <Select placeholder="اختر الحساب المصرفي" allowClear>
                  {bankAccounts.map((account: any) => (
                    <Option key={account.id} value={account.id}>
                      {account.bank_name} - {account.account_name} ({account.account_number})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Divider>ربط بفاتورة (اختياري)</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="reference_invoice_type"
                label="نوع الفاتورة"
              >
                <Select placeholder="اختر نوع الفاتورة" allowClear>
                  {voucherType === 'receipt' ? (
                    <>
                      <Option value="sales_invoice">فاتورة مبيعات</Option>
                      <Option value="paint_invoice">فاتورة دهان</Option>
                    </>
                  ) : (
                    <>
                      <Option value="purchase_invoice">فاتورة مشتريات</Option>
                      <Option value="production_cost">تكلفة إنتاج</Option>
                    </>
                  )}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="reference_invoice_id"
                label="رقم الفاتورة"
              >
                <Select
                  placeholder="اختر الفاتورة"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {unpaidInvoices
                    .filter((invoice: any) => {
                      const invoiceType = form.getFieldValue('reference_invoice_type')
                      if (!invoiceType) return false
                      return invoice.type === invoiceType.replace('_invoice', '')
                    })
                    .map((invoice: any) => (
                      <Option key={invoice.id} value={invoice.id}>
                        {invoice.number} - ₪{invoice.remaining_amount?.toLocaleString()}
                      </Option>
                    ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="الوصف"
            rules={[{ required: true, message: 'يرجى إدخال وصف السند' }]}
          >
            <TextArea
              placeholder={`وصف ${voucherType === 'payment' ? 'سند الدفع' : 'سند القبض'}`}
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="notes"
            label="ملاحّات إضافية"
          >
            <TextArea
              placeholder="ملاحّات إضافية (اختياري)"
              rows={2}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<CheckOutlined />}>
                {voucherType === 'payment' ? 'إضافة سند الدفع' : 'إضافة سند القبض'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false)
                form.resetFields()
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نموذج تفاصيل السند */}
      <Modal
        title="تفاصيل السند"
        open={detailsModalVisible}
        onCancel={() => setDetailsModalVisible(false)}
        footer={[
          selectedVoucher && (
            <UnifiedPrintButton
              key="print"
              data={{
                id: selectedVoucher.id,
                title: selectedVoucher.voucher_type === 'payment' ? 'سند دفع متقدم' : 'سند قبض متقدم',
                subtitle: `رقم: ${selectedVoucher.voucher_number}`,
                date: selectedVoucher.voucher_date,
                customer: {
                  name: selectedVoucher.payee_name || 'غير محدد'
                },
                items: [{
                  id: 1,
                  name: selectedVoucher.voucher_type === 'payment' ? 'مبلغ مدفوع' : 'مبلغ مقبوض',
                  description: selectedVoucher.description || '',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: selectedVoucher.amount || 0,
                  total: selectedVoucher.amount || 0
                }],
                total: selectedVoucher.amount || 0,
                notes: selectedVoucher.notes || ''
              }}
              type="receipt"
              subType={selectedVoucher.voucher_type}
              buttonText="طباعة"
              size="middle"
              showDropdown={true}
              _documentId={`advanced_voucher_details_${selectedVoucher.id}`}
              onAfterPrint={() => message.success('تم طباعة السند بنجاح')}
              onError={() => message.error('فشل في طباعة السند')}
            />
          ),
          <Button key="close" onClick={() => setDetailsModalVisible(false)}>
            إغلاق
          </Button>
        ]}
        width={700}
      >
        {selectedVoucher && (
          <Descriptions title="بيانات السند" bordered size="small">
            <Descriptions.Item label="رقم السند">{selectedVoucher.voucher_number}</Descriptions.Item>
            <Descriptions.Item label="النوع">
              <Tag color={selectedVoucher.payee_name ? 'red' : 'green'}>
                {selectedVoucher.payee_name ? 'سند دفع' : 'سند قبض'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="المبلغ">
              <span style={{
                color: selectedVoucher.payee_name ? '#ff4d4f' : '#52c41a',
                fontWeight: 'bold'
              }}>
                ₪ {selectedVoucher.amount?.toLocaleString() || 0}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label={selectedVoucher.payee_name ? 'المستفيد' : 'الدافع'}>
              {selectedVoucher.payee_name || selectedVoucher.payer_name}
            </Descriptions.Item>
            <Descriptions.Item label="التاريخ">
              {selectedVoucher.payment_date || selectedVoucher.receipt_date}
            </Descriptions.Item>
            <Descriptions.Item label="طريقة الدفع">
              <Tag color="blue">{getPaymentMethodText(selectedVoucher.payment_method)}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="الحساب المصرفي">
              {selectedVoucher.bank_name ?
                `${selectedVoucher.bank_name} - ${selectedVoucher.account_name}` :
                'غير محدد'
              }
            </Descriptions.Item>
            <Descriptions.Item label="الحالة">
              <Tag color={getStatusColor(selectedVoucher.status)}>
                {getStatusText(selectedVoucher.status)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="المرجع">
              {selectedVoucher.reference_type ? (
                <Tag color="purple" icon={<LinkOutlined />}>
                  {getReferenceTypeText(selectedVoucher.reference_type)} #{selectedVoucher.reference_id}
                </Tag>
              ) : (
                <Tag color="default">غير مربوط</Tag>
              )}
            </Descriptions.Item>
            <Descriptions.Item label="الوصف" span={3}>
              {selectedVoucher.description || 'لا يوجد وصف'}
            </Descriptions.Item>
            <Descriptions.Item label="ملاحّات" span={3}>
              {selectedVoucher.notes || 'لا توجد ملاحّات'}
            </Descriptions.Item>
            <Descriptions.Item label="تاريخ الإنشاء">
              {dayjs(selectedVoucher.created_at).format('YYYY-MM-DD HH:mm')}
            </Descriptions.Item>
            <Descriptions.Item label="آخر تحديث">
              {dayjs(selectedVoucher.updated_at).format('YYYY-MM-DD HH:mm')}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  )
}

export default AdvancedVoucherManagement
