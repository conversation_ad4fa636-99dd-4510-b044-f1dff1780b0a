/* ===================================
   أنماط معاينة الطباعة المحسنة
   Enhanced Print Preview Styles
   =================================== */

/* إعدادات أساسية للمعاينة */
.print-preview-container {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.print-preview-content {
    background: white;
    padding: 20px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    direction: rtl;
    font-family: Arial, sans-serif;
    
    /* التأكد من عرض الألوان في المعاينة */
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
}

/* تطبيق الألوان المخصصة في المعاينة */
.print-preview-content .header {
    border-bottom: 2px solid var(--print-primary-color, #1890ff);
    margin-bottom: 20px;
    padding-bottom: 15px;
}

.print-preview-content .company-name {
    color: var(--print-primary-color, #1890ff);
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 5px;
}

.print-preview-content .document-title {
    color: var(--print-primary-color, #1890ff);
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 10px;
}

.print-preview-content .section-title {
    color: var(--print-primary-color, #1890ff);
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 14px;
}

.print-preview-content .items-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
}

.print-preview-content .items-table th {
    background-color: var(--print-primary-color, #1890ff) !important;
    color: white !important;
    padding: 8px;
    text-align: center;
    border: 1px solid var(--print-border-color, #d9d9d9);
    font-weight: bold;
}

.print-preview-content .items-table td {
    padding: 6px 8px;
    border: 1px solid var(--print-border-color, #d9d9d9);
    text-align: center;
}

.print-preview-content .items-table tr:nth-child(even) {
    background-color: #fafafa;
}

.print-preview-content .totals-table {
    margin-top: 20px;
    float: left;
    min-width: 300px;
}

.print-preview-content .totals-table .label {
    background-color: var(--print-secondary-color, #52c41a) !important;
    color: white;
    font-weight: bold;
    padding: 8px 15px;
    text-align: right;
    border: 1px solid var(--print-border-color, #d9d9d9);
}

.print-preview-content .totals-table .value {
    padding: 8px 15px;
    text-align: left;
    border: 1px solid var(--print-border-color, #d9d9d9);
    font-family: monospace;
}

.print-preview-content .totals-table .total-row {
    background-color: var(--print-primary-color, #1890ff) !important;
    color: white !important;
    font-weight: bold;
    font-size: 16px;
}

.print-preview-content .footer {
    margin-top: 30px;
    padding-top: 15px;
    border-top: 1px solid var(--print-border-color, #d9d9d9);
    text-align: center;
    font-size: 12px;
    color: #666;
}

.print-preview-content .watermark {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    font-size: 48px;
    color: var(--print-primary-color, #1890ff);
    opacity: 0.1;
    z-index: -1;
    pointer-events: none;
    font-weight: bold;
    white-space: nowrap;
}

/* تحسينات للعناصر الخاصة */
.print-preview-content .customer-section {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    margin: 15px 0;
    border: 1px solid var(--print-border-color, #d9d9d9);
}

.print-preview-content .signature-box {
    width: 200px;
    height: 80px;
    border: 1px solid var(--print-border-color, #d9d9d9);
    text-align: center;
    padding-top: 60px;
    font-size: 12px;
    margin: 20px auto;
}

/* تحسينات للنصوص المخصصة */
.print-preview-content .custom-header-text {
    text-align: center;
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 4px;
    font-weight: bold;
    color: var(--print-primary-color, #1890ff);
}

.print-preview-content .custom-footer-text {
    text-align: center;
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 4px;
    font-weight: bold;
    color: var(--print-primary-color, #1890ff);
}

/* تحسينات للشعار */
.print-preview-content .logo {
    max-width: 100px;
    max-height: 60px;
    object-fit: contain;
}

/* تحسينات للجداول المتقدمة */
.print-preview-content .enhanced-table {
    border: 2px solid var(--print-primary-color, #1890ff);
    border-radius: 6px;
    overflow: hidden;
}

.print-preview-content .enhanced-table th {
    background: linear-gradient(135deg, var(--print-primary-color, #1890ff), var(--print-secondary-color, #52c41a));
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* تحسينات للوضع الداكن في المعاينة */
[data-theme="dark"] .print-preview-content {
    background: #1f1f1f;
    color: #ffffff;
}

[data-theme="dark"] .print-preview-content .items-table td {
    background-color: #2a2a2a;
    color: #ffffff;
}

[data-theme="dark"] .print-preview-content .items-table tr:nth-child(even) {
    background-color: #333333;
}

/* تحسينات للطباعة الفعلية */
@media print {
    .print-preview-container {
        background: none;
        padding: 0;
        box-shadow: none;
    }
    
    .print-preview-content {
        box-shadow: none;
        border-radius: 0;
    }
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
    .print-preview-content {
        padding: 15px;
        font-size: 14px;
    }
    
    .print-preview-content .items-table th,
    .print-preview-content .items-table td {
        padding: 4px;
        font-size: 12px;
    }
    
    .print-preview-content .totals-table {
        width: 100%;
        float: none;
    }
}

/* تأثيرات التحميل */
.print-preview-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
    background: #f8f9fa;
    border-radius: 6px;
    color: #666;
    font-size: 16px;
}

.print-preview-loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-top: 2px solid var(--print-primary-color, #1890ff);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات للوصولية */
.print-preview-content:focus {
    outline: 2px solid var(--print-primary-color, #1890ff);
    outline-offset: 2px;
}

/* تحسينات للطباعة عالية الجودة */
.print-high-quality .print-preview-content {
    font-size: 12pt;
    line-height: 1.4;
}

.print-high-quality .print-preview-content .items-table {
    font-size: 10pt;
}

.print-high-quality .print-preview-content .footer {
    font-size: 9pt;
}

/* ===================================
   تنسيق تلقائي للشعار
   Auto Logo Layout
   =================================== */

/* تنسيق تلقائي للشعار في المعاينة */
.logo-container {
    display: flex !important;
    align-items: center !important;
    justify-content: var(--print-logo-position, center) !important;
    min-height: var(--print-logo-height, 60px) !important;
    overflow: hidden !important;
    margin-bottom: 10px !important;
    transition: all 0.3s ease !important;
}

.logo-container.top-left {
    justify-content: flex-start !important;
}

.logo-container.top-center {
    justify-content: center !important;
}

.logo-container.top-right {
    justify-content: flex-end !important;
}

.logo {
    max-width: var(--print-logo-width, 90px) !important;
    max-height: var(--print-logo-height, 60px) !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
    display: var(--print-show-logo, block) !important;
    margin: 0 !important;
    border: none !important;
    background: transparent !important;
    transition: all 0.3s ease !important;
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
}

/* تأثيرات تفاعلية للشعار */
.logo:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

/* تنسيق متجاوب للشعار */
@media (max-width: 768px) {
    .logo {
        max-width: calc(var(--print-logo-width, 90px) * 0.8) !important;
        max-height: calc(var(--print-logo-height, 60px) * 0.8) !important;
    }

    .logo-container {
        min-height: calc(var(--print-logo-height, 60px) * 0.8) !important;
    }
}

@media (max-width: 480px) {
    .logo {
        max-width: calc(var(--print-logo-width, 90px) * 0.6) !important;
        max-height: calc(var(--print-logo-height, 60px) * 0.6) !important;
    }

    .logo-container {
        min-height: calc(var(--print-logo-height, 60px) * 0.6) !important;
    }
}

/* إصلاح مشاكل الشعار في الطباعة */
@media print {
    .logo-container {
        page-break-inside: avoid !important;
        break-inside: avoid !important;
    }

    .logo {
        max-width: var(--print-logo-width, 90px) !important;
        max-height: var(--print-logo-height, 60px) !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}
