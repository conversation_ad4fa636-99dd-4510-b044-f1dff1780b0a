/**
 * خدمة التقارير الموحدة
 * تدير جميع عمليات التقارير بطريقة موحدة ومحسنة
 */

import {
  ReportType,
  ReportData,
  ReportFilters,
  ReportResult,
  ReportSettings,
  createDefaultFilters,
  createDefaultColumns
} from '../types/reports'
import { ReportPerformanceService } from './ReportPerformanceService'
import { SafeLogger as Logger } from '../utils/logger'

export class UniversalReportService {
  private static instance: UniversalReportService
  private cache: Map<string, { data: ReportData; timestamp: number; size: number }> = new Map()
  private performanceService: ReportPerformanceService
  private loadingQueue: Map<string, Promise<ReportResult>> = new Map()
  private compressionCache: Map<string, { compressed: string; timestamp: number }> = new Map()
  private defaultSettings: ReportSettings = {
    autoRefresh: false,
    refreshInterval: 300000, // 5 دقائق
    cacheResults: true,
    cacheDuration: 600000, // 10 دقائق
    maxRecords: 10000,
    defaultPageSize: 50,
    defaultSortBy: 'date',
    defaultSortOrder: 'desc'
  }

  // إعدادات التحسين المتقدمة
  private optimizationSettings = {
    maxCacheSize: 50 * 1024 * 1024, // 50MB
    compressionThreshold: 1024 * 1024, // 1MB
    lazyLoadThreshold: 1000, // عدد السجلات
    batchSize: 500, // حجم الدفعة للتحميل التدريجي
    preloadNextBatch: true, // تحميل الدفعة التالية مسبقاً
    enableCompression: true, // تفعيل الضغط
    enableVirtualization: true, // تفعيل العرض الافتراضي
    cacheCleanupInterval: 300000 // 5 دقائق
  }

  private constructor() {
    this.performanceService = ReportPerformanceService.getInstance()
    this.startCacheCleanupTimer()
    this.initializeOptimizations()
  }

  /**
   * تهيئة التحسينات المتقدمة
   */
  private initializeOptimizations(): void {
    // تفعيل تنظيف الكاش التلقائي
    this.startAutoCacheCleanup()

    // تحسين إعدادات الذاكرة
    if (typeof window !== 'undefined' && 'performance' in window) {
      // مراقبة استخدام الذاكرة
      this.monitorMemoryUsage()
    }

    Logger.info('UniversalReportService', 'تم تهيئة التحسينات المتقدمة')
  }

  /**
   * مراقبة استخدام الذاكرة وتحسينها
   */
  private monitorMemoryUsage(): void {
    setInterval(() => {
      if ('memory' in performance) {
        const memInfo = (performance as any).memory
        const usedMB = memInfo.usedJSHeapSize / 1024 / 1024

        // إذا تجاوز الاستخدام 100MB، قم بتنظيف الكاش
        if (usedMB > 100) {
          this.aggressiveCacheCleanup()
        }
      }
    }, 60000) // كل دقيقة
  }

  /**
   * تنظيف الكاش بقوة عند الحاجة
   */
  private aggressiveCacheCleanup(): void {
    const currentTime = Date.now()
    let cleanedSize = 0

    // حذف العناصر الأقدم من 5 دقائق
    for (const [key, value] of this.cache.entries()) {
      if (currentTime - value.timestamp > 300000) {
        cleanedSize += value.size || 0
        this.cache.delete(key)
      }
    }

    // تنظيف كاش الضغط أيضاً
    for (const [key, value] of this.compressionCache.entries()) {
      if (currentTime - value.timestamp > 300000) {
        this.compressionCache.delete(key)
      }
    }

    if (cleanedSize > 0) {
      Logger.info('UniversalReportService', `تم تنظيف ${(cleanedSize / 1024 / 1024).toFixed(2)}MB من الكاش`)
    }
  }

  /**
   * بدء مؤقت تنظيف الكاش
   */
  private startCacheCleanupTimer(): void {
    setInterval(() => {
      this.cleanExpiredCache()
    }, this.optimizationSettings.cacheCleanupInterval)
  }

  public static getInstance(): UniversalReportService {
    if (!UniversalReportService.instance) {
      UniversalReportService.instance = new UniversalReportService()
    }
    return UniversalReportService.instance
  }

  /**
   * إنشاء تقرير موحد مع تحسينات الأداء
   */
  public async generateReport(
    type: ReportType,
    filters: ReportFilters = {},
    settings: Partial<ReportSettings> = {}
  ): Promise<ReportResult> {
    const startTime = Date.now()

    try {
      Logger.info('UniversalReportService', `إنشاء تقرير: ${type}`)

      // دمج الإعدادات
      const mergedSettings = { ...this.defaultSettings, ...settings }

      // دمج الفلاتر مع الافتراضية
      const mergedFilters = { ...createDefaultFilters(type), ...filters }

      // فحص الكاش مع تحسينات الأداء
      const cacheKey = this.generateCacheKey(type, mergedFilters)

      // التحقق من وجود طلب مماثل قيد التنفيذ
      if (this.loadingQueue.has(cacheKey)) {
        Logger.info('UniversalReportService', 'انتظار طلب مماثل قيد التنفيذ')
        return await this.loadingQueue.get(cacheKey)!
      }

      if (mergedSettings.cacheResults) {
        const cachedResult = this.getCachedResult(cacheKey, mergedSettings.cacheDuration || 300000)
        if (cachedResult) {
          Logger.info('UniversalReportService', 'استخدام النتيجة المحفوظة')

          return {
            success: true,
            data: cachedResult,
            processingTime: Date.now() - startTime,
            totalRecords: cachedResult.data.length,
            fromCache: true
          }
        }
      }

      // إنشاء promise للطلب الجديد
      const reportPromise = this.executeReportGeneration(type, mergedFilters, mergedSettings, cacheKey, startTime)
      this.loadingQueue.set(cacheKey, reportPromise)

      try {
        const result = await reportPromise
        return result
      } finally {
        // إزالة من قائمة الانتظار
        this.loadingQueue.delete(cacheKey)
      }

    } catch (error) {
      Logger.error('UniversalReportService', 'خطأ في إنشاء التقرير:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        processingTime: Date.now() - startTime
      }
    }
  }

  /**
   * تنفيذ إنشاء التقرير الفعلي
   */
  private async executeReportGeneration(
    type: ReportType,
    filters: ReportFilters,
    settings: ReportSettings,
    cacheKey: string,
    startTime: number
  ): Promise<ReportResult> {
    // تحديد ما إذا كان يجب استخدام التحميل التدريجي
    const shouldUseLazyLoading = await this.shouldUseLazyLoading(type, filters)

    if (shouldUseLazyLoading) {
      return await this.generateLargeReport(type, filters, settings, cacheKey, startTime)
    } else {
      return await this.generateStandardReport(type, filters, settings, cacheKey, startTime)
    }
  }

  /**
   * تحديد ما إذا كان يجب استخدام التحميل التدريجي
   */
  private async shouldUseLazyLoading(type: ReportType, filters: ReportFilters): Promise<boolean> {
    try {
      // محاولة تقدير حجم البيانات
      const estimatedSize = await this.estimateDataSize(type, filters)
      return estimatedSize > this.optimizationSettings.lazyLoadThreshold
    } catch {
      // في حالة الخطأ، استخدم التحميل العادي
      return false
    }
  }

  /**
   * تقدير حجم البيانات
   */
  private async estimateDataSize(type: ReportType, filters: ReportFilters): Promise<number> {
    try {
      // استدعاء API لتقدير الحجم
      const response = await window.electronAPI?.estimateReportSize?.(type, filters)
      return (response as any)?.estimatedRecords || 0
    } catch {
      return 0
    }
  }

  /**
   * إنشاء تقرير كبير مع التحميل التدريجي
   */
  private async generateLargeReport(
    type: ReportType,
    filters: ReportFilters,
    settings: ReportSettings,
    cacheKey: string,
    startTime: number
  ): Promise<ReportResult> {
    Logger.info('UniversalReportService', 'استخدام التحميل التدريجي للتقرير الكبير')

    // تحميل الدفعة الأولى
    const firstBatch = await this.loadReportBatch(type, filters, 0, this.optimizationSettings.batchSize)

    if (!firstBatch.success || !firstBatch.data) {
      throw new Error(firstBatch.error || 'فشل في تحميل البيانات')
    }

    // إنشاء البيانات الأولية
    const reportData: ReportData = {
      title: firstBatch.data.title,
      subtitle: firstBatch.data.subtitle,
      columns: firstBatch.data.columns,
      data: firstBatch.data.data,
      summary: firstBatch.data.summary,
      metadata: {
        ...firstBatch.data.metadata,
        isLazyLoaded: true,
        totalEstimated: firstBatch.totalRecords,
        currentlyLoaded: firstBatch.data.data.length,
        batchSize: this.optimizationSettings.batchSize
      }
    }

    // ضغط البيانات إذا لزم الأمر
    const optimizedData = await this.optimizeReportDataInternal(reportData)

    // حفظ في الكاش
    if (settings.cacheResults) {
      this.setCachedResult(cacheKey, optimizedData)
    }

    // تحميل الدفعة التالية في الخلفية إذا كان مفعلاً
    if (this.optimizationSettings.preloadNextBatch && firstBatch.hasMore) {
      this.preloadNextBatch(type, filters, 1)
    }

    const processingTime = Date.now() - startTime
    Logger.info('UniversalReportService', `تم إنشاء التقرير الكبير في ${processingTime}ms`)

    return {
      success: true,
      data: optimizedData,
      processingTime,
      totalRecords: firstBatch.totalRecords || optimizedData.data.length,
      hasMore: firstBatch.hasMore,
      isLazyLoaded: true
    }
  }

  /**
   * إنشاء تقرير عادي
   */
  private async generateStandardReport(
    type: ReportType,
    filters: ReportFilters,
    settings: ReportSettings,
    cacheKey: string,
    startTime: number
  ): Promise<ReportResult> {
    // إنشاء التقرير
    const reportData = await this.createReport(type, filters, settings)

    // تحسين البيانات للأداء
    const optimizedData = await this.optimizeReportDataInternal(reportData)

    // حفظ في الكاش
    if (settings.cacheResults) {
      this.setCachedResult(cacheKey, optimizedData)
    }

    const processingTime = Date.now() - startTime
    Logger.info('UniversalReportService', `تم إنشاء التقرير في ${processingTime}ms`)

    return {
      success: true,
      data: optimizedData,
      processingTime,
      totalRecords: optimizedData.data.length
    }
  }

  /**
   * إنشاء التقرير الفعلي
   */
  private async createReport(
    type: ReportType, 
    filters: ReportFilters,
    _settings: ReportSettings
  ): Promise<ReportData> {
    
    // تحديد معالج التقرير حسب النوع
    switch (type) {
      case 'inventory_detailed':
        return this.generateInventoryDetailedReport(filters)
      case 'inventory_movements':
        return this.generateInventoryMovementsReport(filters)
      case 'sales_by_customer':
        return this.generateSalesByCustomerReport(filters)
      case 'sales_by_product':
        return this.generateSalesByProductReport(filters)
      case 'purchases_by_supplier':
        return this.generatePurchasesBySupplierReport(filters)
      case 'employee_attendance':
        return this.generateEmployeeAttendanceReport(filters)
      default:
        return this.generateGenericReport(type, filters)
    }
  }

  /**
   * تقرير المخزون التفصيلي
   */
  private async generateInventoryDetailedReport(filters: ReportFilters): Promise<ReportData> {
    try {
      // استدعاء API للحصول على البيانات
      const response = await window.electronAPI?.getInventoryReport?.(filters)
      
      if (!response?.success) {
        throw new Error(response?.message || 'فشل في تحميل بيانات المخزون')
      }

      return {
        title: 'تقرير المخزون التفصيلي',
        subtitle: this.generateSubtitle(filters),
        columns: [
          { key: 'item_code', title: 'كود الصنف', align: 'center', width: 120 },
          { key: 'item_name', title: 'اسم الصنف', align: 'right', width: 200 },
          { key: 'category_name', title: 'الفئة', align: 'center', width: 150 },
          { key: 'warehouse_name', title: 'المخزن', align: 'center', width: 150 },
          { key: 'current_quantity', title: 'الكمية الحالية', align: 'center', format: 'number', width: 120 },
          { key: 'unit_price', title: 'سعر الوحدة', align: 'center', format: 'currency', width: 120 },
          { key: 'total_value', title: 'القيمة الإجمالية', align: 'center', format: 'currency', width: 150 }
        ],
        data: response.data || [],
        summary: this.calculateInventorySummary(response.data || []),
        metadata: {
          generatedAt: new Date().toISOString(),
          reportType: 'inventory_detailed',
          filters,
          totalRecords: response.data?.length || 0
        }
      }
    } catch (error) {
      Logger.error('UniversalReportService', 'خطأ في تقرير المخزون التفصيلي:', error)
      throw error
    }
  }

  /**
   * تقرير حركات المخزون
   */
  private async generateInventoryMovementsReport(filters: ReportFilters): Promise<ReportData> {
    try {
      const response = await window.electronAPI?.getInventoryMovementsReport?.(filters)
      
      if (!response?.success) {
        throw new Error(response?.message || 'فشل في تحميل حركات المخزون')
      }

      return {
        title: 'تقرير حركات المخزون',
        subtitle: this.generateSubtitle(filters),
        columns: [
          { key: 'movement_date', title: 'التاريخ', align: 'center', format: 'date', width: 120 },
          { key: 'item_name', title: 'الصنف', align: 'right', width: 200 },
          { key: 'movement_type', title: 'نوع الحركة', align: 'center', width: 120 },
          { key: 'quantity', title: 'الكمية', align: 'center', format: 'number', width: 100 },
          { key: 'reference', title: 'المرجع', align: 'center', width: 150 },
          { key: 'notes', title: 'ملاحظات', align: 'right', width: 200 }
        ],
        data: response.data || [],
        summary: this.calculateMovementsSummary(response.data || []),
        metadata: {
          generatedAt: new Date().toISOString(),
          reportType: 'inventory_movements',
          filters,
          totalRecords: response.data?.length || 0
        }
      }
    } catch (error) {
      Logger.error('UniversalReportService', 'خطأ في تقرير حركات المخزون:', error)
      throw error
    }
  }

  /**
   * تقرير المبيعات حسب العميل
   */
  private async generateSalesByCustomerReport(filters: ReportFilters): Promise<ReportData> {
    try {
      // استدعاء تقرير المبيعات الحقيقي بدلاً من تقرير الدهان
      const response = await window.electronAPI?.getSalesByCustomerReport?.(filters)

      if (!response?.success) {
        throw new Error(response?.message || 'فشل في تحميل بيانات المبيعات')
      }

      // التأكد من أن البيانات في الشكل الصحيح
      const reportData = Array.isArray(response.data) ? response.data : []

      return {
        title: 'تقرير المبيعات حسب العميل',
        subtitle: this.generateSubtitle(filters),
        columns: [
          { key: 'customer_name', title: 'اسم العميل', align: 'right', width: 200 },
          { key: 'total_invoices', title: 'عدد الفواتير', align: 'center', format: 'number', width: 120 },
          { key: 'total_amount', title: 'إجمالي المبيعات', align: 'center', format: 'currency', width: 150 },
          { key: 'paid_amount', title: 'المدفوع', align: 'center', format: 'currency', width: 150 },
          { key: 'outstanding_amount', title: 'المتبقي', align: 'center', format: 'currency', width: 150 },
          { key: 'avg_invoice_amount', title: 'متوسط الفاتورة', align: 'center', format: 'currency', width: 150 },
          { key: 'payment_percentage', title: 'نسبة الدفع %', align: 'center', format: 'percentage', width: 120 },
          { key: 'last_purchase_date', title: 'آخر شراء', align: 'center', format: 'date', width: 120 }
        ],
        data: reportData,
        summary: this.calculateSalesSummary(reportData),
        metadata: {
          generatedAt: new Date().toISOString(),
          reportType: 'sales_by_customer',
          filters,
          totalRecords: reportData.length
        }
      }
    } catch (error) {
      Logger.error('UniversalReportService', 'خطأ في تقرير المبيعات حسب العميل:', error)
      throw error
    }
  }

  /**
   * تقرير المبيعات حسب المنتج
   */
  private async generateSalesByProductReport(filters: ReportFilters): Promise<ReportData> {
    try {
      const response = await window.electronAPI?.getSalesByProductReport?.(filters)

      if (!response?.success) {
        throw new Error(response?.message || 'فشل في تحميل بيانات المبيعات حسب المنتج')
      }

      return {
        title: 'تقرير المبيعات حسب المنتج',
        subtitle: this.generateSubtitle(filters),
        columns: [
          { key: 'item_code', title: 'كود المنتج', align: 'center', width: 120 },
          { key: 'item_name', title: 'اسم المنتج', align: 'right', width: 200 },
          { key: 'category_name', title: 'الفئة', align: 'center', width: 150 },
          { key: 'total_quantity', title: 'الكمية المباعة', align: 'center', format: 'number', width: 120 },
          { key: 'total_value', title: 'قيمة المبيعات', align: 'center', format: 'currency', width: 150 },
          { key: 'profit_margin', title: 'هامش الربح %', align: 'center', format: 'percentage', width: 120 }
        ],
        data: response.data || [],
        summary: this.calculateProductSalesSummary(response.data || []),
        metadata: {
          generatedAt: new Date().toISOString(),
          reportType: 'sales_by_product',
          filters,
          totalRecords: response.data?.length || 0
        }
      }
    } catch (error) {
      Logger.error('UniversalReportService', 'خطأ في تقرير المبيعات حسب المنتج:', error)
      throw error
    }
  }

  /**
   * تقرير المشتريات حسب المورد
   */
  private async generatePurchasesBySupplierReport(filters: ReportFilters): Promise<ReportData> {
    try {
      const response = await window.electronAPI?.getPurchasesBySupplierReport?.(filters)

      if (!response?.success) {
        throw new Error(response?.message || 'فشل في تحميل بيانات المشتريات حسب المورد')
      }

      return {
        title: 'تقرير المشتريات حسب المورد',
        subtitle: this.generateSubtitle(filters),
        columns: [
          { key: 'supplier_name', title: 'اسم المورد', align: 'right', width: 200 },
          { key: 'total_orders', title: 'عدد الطلبات', align: 'center', format: 'number', width: 120 },
          { key: 'total_amount', title: 'إجمالي المشتريات', align: 'center', format: 'currency', width: 150 },
          { key: 'avg_order_amount', title: 'متوسط الطلب', align: 'center', format: 'currency', width: 150 },
          { key: 'last_order_date', title: 'آخر طلب', align: 'center', format: 'date', width: 120 }
        ],
        data: response.data || [],
        summary: this.calculatePurchasesSummary(response.data || []),
        metadata: {
          generatedAt: new Date().toISOString(),
          reportType: 'purchases_by_supplier',
          filters,
          totalRecords: response.data?.length || 0
        }
      }
    } catch (error) {
      Logger.error('UniversalReportService', 'خطأ في تقرير المشتريات حسب المورد:', error)
      throw error
    }
  }

  /**
   * تقرير حضور الموظفين
   */
  private async generateEmployeeAttendanceReport(filters: ReportFilters): Promise<ReportData> {
    try {
      const response = await window.electronAPI?.getEmployeeAttendanceReport?.(filters)

      if (!response?.success) {
        throw new Error(response?.message || 'فشل في تحميل بيانات حضور الموظفين')
      }

      return {
        title: 'تقرير حضور الموظفين',
        subtitle: this.generateSubtitle(filters),
        columns: [
          { key: 'employee_name', title: 'اسم الموظف', align: 'right', width: 200 },
          { key: 'department', title: 'القسم', align: 'center', width: 150 },
          { key: 'total_days', title: 'أيام العمل', align: 'center', format: 'number', width: 120 },
          { key: 'present_days', title: 'أيام الحضور', align: 'center', format: 'number', width: 120 },
          { key: 'absent_days', title: 'أيام الغياب', align: 'center', format: 'number', width: 120 },
          { key: 'attendance_rate', title: 'معدل الحضور %', align: 'center', format: 'percentage', width: 120 }
        ],
        data: response.data || [],
        summary: this.calculateAttendanceSummary(response.data || []),
        metadata: {
          generatedAt: new Date().toISOString(),
          reportType: 'employee_attendance',
          filters,
          totalRecords: response.data?.length || 0
        }
      }
    } catch (error) {
      Logger.error('UniversalReportService', 'خطأ في تقرير حضور الموظفين:', error)
      throw error
    }
  }

  /**
   * إنشاء تقرير عام
   */
  private async generateGenericReport(type: ReportType, filters: ReportFilters): Promise<ReportData> {
    return {
      title: this.getReportTitle(type),
      subtitle: this.generateSubtitle(filters),
      columns: createDefaultColumns(type),
      data: [],
      summary: {},
      metadata: {
        generatedAt: new Date().toISOString(),
        reportType: type,
        filters,
        totalRecords: 0
      }
    }
  }

  /**
   * دوال مساعدة
   */
  private generateCacheKey(type: ReportType, filters: ReportFilters): string {
    return `${type}_${JSON.stringify(filters)}`
  }

  private getCachedResult(key: string, duration: number): ReportData | null {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < duration) {
      return cached.data
    }
    return null
  }

  private setCachedResult(key: string, data: ReportData): void {
    const size = this.calculateDataSize(data)
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      size
    })

    // تنظيف الكاش إذا تجاوز الحد المسموح
    if (this.getCurrentCacheSize() > this.optimizationSettings.maxCacheSize) {
      this.optimizeCacheSize()
    }
  }

  private generateSubtitle(filters: ReportFilters): string {
    if (filters.dateRange && Array.isArray(filters.dateRange) && filters.dateRange[0] && filters.dateRange[1]) {
      return `الفترة: ${filters.dateRange[0]} إلى ${filters.dateRange[1]}`
    }
    return `كل المدة - تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-EG')}`
  }

  private getReportTitle(type: ReportType): string {
    const titles: Record<ReportType, string> = {
      inventory_detailed: 'تقرير المخزون التفصيلي',
      inventory_movements: 'تقرير حركات المخزون',
      inventory_audit: 'تقرير جرد المخزون',
      material_consumption: 'تقرير استهلاك المواد',
      closing_summary: 'ملخص الإقفال',
      closing_entries: 'قيود الإقفال',
      carried_forward_balances: 'الأرصدة المرحلة',
      audit_log: 'سجل المراجعة',
      period_comparison: 'مقارنة الفترات',
      closing_checklist: 'قائمة مراجعة الإقفال',
      low_stock: 'تقرير الأصناف المنخفضة',
      item_warehouse_distribution: 'تقرير توزيع الأصناف',
      abc_analysis: 'تحليل ABC',
      purchases_by_supplier: 'تقرير المشتريات حسب المورد',
      purchases_by_item: 'تقرير المشتريات حسب الصنف',
      supplier_payables: 'تقرير مستحقات الموردين',
      purchase_analysis: 'تحليل المشتريات',
      cost_analysis: 'تحليل التكاليف',
      supplier_price_comparison: 'مقارنة أسعار الموردين',
      supplier_quality: 'تقرير جودة الموردين',
      supplier_analysis: 'تحليل الموردين',
      purchase_performance: 'أداء المشتريات',
      sales_by_customer: 'تقرير المبيعات حسب العميل',
      sales_by_product: 'تقرير المبيعات حسب المنتج',
      sales_by_region: 'تقرير المبيعات حسب المنطقة',
      monthly_sales: 'تقرير المبيعات الشهرية',
      sales_returns: 'تقرير مرتجعات المبيعات',
      top_profitable_customers: 'تقرير أفضل العملاء ربحية',
      profitability: 'تقرير الربحية',
      employee_attendance: 'تقرير حضور الموظفين',
      employee_payroll: 'تقرير رواتب الموظفين',
      employee_leaves: 'تقرير إجازات الموظفين',
      employee_performance: 'تقرير أداء الموظفين',
      employee_overtime: 'تقرير ساعات إضافية',
      employee_analysis: 'تحليل الموظفين',
      salary_comparison: 'مقارنة الرواتب',
      efficiency_evaluation: 'تقييم الكفاءة',
      production_orders: 'تقرير أوامر الإنتاج',
      production_efficiency: 'تقرير كفاءة الإنتاج',
      production_costs: 'تقرير تكاليف الإنتاج',
      production_schedule: 'تقرير جدولة الإنتاج',
      production_quality: 'تقرير جودة الإنتاج',
      production_workers_performance: 'تقرير أداء عمال الإنتاج',
      production_materials_consumption: 'تقرير استهلاك مواد الإنتاج',
      production_profitability: 'تقرير ربحية الإنتاج',
      paint_by_customer: 'تقرير الدهان حسب العميل',
      paint_by_type: 'تقرير الدهان حسب النوع',
      monthly_paint: 'تقرير الدهان الشهري',
      paint_profitability: 'تقرير ربحية الدهان',
      paint_performance: 'تقرير أداء الدهان',
      paint_quality: 'تقرير جودة الدهان',
      balance_sheet: 'الميزانية العمومية',
      income_statement: 'قائمة الدخل',
      cash_flow: 'قائمة التدفقات النقدية',
      cash_flow_analysis: 'تحليل التدفق النقدي',
      profit_loss: 'قائمة الأرباح والخسائر',
      bank_reconciliation: 'تسوية البنك',
      customer_aging: 'تقرير أعمار الذمم',
      departments_integration: 'تقرير تكامل الأقسام'
    }
    return titles[type] || 'تقرير'
  }

  private calculateInventorySummary(data: any[]): any {
    return {
      totalItems: data.length,
      totalValue: data.reduce((sum, item) => sum + (item.total_value || 0), 0),
      totalQuantity: data.reduce((sum, item) => sum + (item.current_quantity || 0), 0)
    }
  }

  private calculateMovementsSummary(data: any[]): any {
    return {
      totalMovements: data.length,
      incomingMovements: data.filter(item => item.movement_type === 'in').length,
      outgoingMovements: data.filter(item => item.movement_type === 'out').length
    }
  }

  private calculateSalesSummary(data: any[]): any {
    return {
      totalCustomers: data.length,
      totalSales: data.reduce((sum, item) => sum + (item.total_amount || 0), 0),
      totalInvoices: data.reduce((sum, item) => sum + (item.total_invoices || 0), 0)
    }
  }

  private calculateProductSalesSummary(data: any[]): any {
    return {
      totalProducts: data.length,
      totalQuantity: data.reduce((sum, item) => sum + (item.total_quantity || 0), 0),
      totalValue: data.reduce((sum, item) => sum + (item.total_value || 0), 0),
      avgProfitMargin: data.length > 0 ? data.reduce((sum, item) => sum + (item.profit_margin || 0), 0) / data.length : 0
    }
  }

  private calculatePurchasesSummary(data: any[]): any {
    return {
      totalSuppliers: data.length,
      totalPurchases: data.reduce((sum, item) => sum + (item.total_amount || 0), 0),
      totalOrders: data.reduce((sum, item) => sum + (item.total_orders || 0), 0)
    }
  }

  private calculateAttendanceSummary(data: any[]): any {
    const totalEmployees = data.length
    const avgAttendanceRate = totalEmployees > 0 ?
      data.reduce((sum, item) => sum + (item.attendance_rate || 0), 0) / totalEmployees : 0

    return {
      totalEmployees,
      avgAttendanceRate,
      totalPresentDays: data.reduce((sum, item) => sum + (item.present_days || 0), 0),
      totalAbsentDays: data.reduce((sum, item) => sum + (item.absent_days || 0), 0)
    }
  }

  /**
   * تحميل تدريجي للتقرير
   */
  public async loadReportProgressively(
    type: ReportType,
    filters: ReportFilters = {},
    pageSize: number = 50
  ): Promise<{ data: any[], hasMore: boolean, nextPage: number }> {
    try {
      Logger.info('UniversalReportService', `تحميل تدريجي لتقرير: ${type}`)

      const mergedFilters = { ...createDefaultFilters(type), ...filters }

      return await this.performanceService.loadDataProgressively(type, mergedFilters, pageSize)
    } catch (error) {
      Logger.error('UniversalReportService', 'خطأ في التحميل التدريجي:', error)
      throw error
    }
  }

  /**
   * الحصول على مقاييس الأداء
   */
  public getPerformanceMetrics(reportType?: ReportType) {
    return this.performanceService.getPerformanceMetrics(reportType)
  }

  /**
   * تحديث إعدادات الأداء
   */
  public updatePerformanceSettings(settings: {
    maxCacheSize?: number
    cacheTimeout?: number
    compressionEnabled?: boolean
    lazyLoadingEnabled?: boolean
  }): void {
    this.performanceService.updateSettings(settings)
  }

  /**
   * تنظيف الكاش
   */
  public clearCache(): void {
    this.cache.clear()
    this.performanceService.clearCache()
    Logger.info('UniversalReportService', 'تم تنظيف كاش التقارير')
  }

  /**
   * تنظيف الكاش المنتهي الصلاحية تلقائياً
   */
  public cleanExpiredCache(): void {
    const now = Date.now()
    let cleanedCount = 0

    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > (this.defaultSettings.cacheDuration || 300000)) {
        this.cache.delete(key)
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
      Logger.info('UniversalReportService', `تم تنظيف ${cleanedCount} عنصر منتهي الصلاحية من الكاش`)
    }
  }

  /**
   * بدء تنظيف الكاش التلقائي
   */
  public startAutoCacheCleanup(): void {
    // تنظيف كل 5 دقائق
    setInterval(() => {
      this.cleanExpiredCache()
    }, 300000)

    Logger.info('UniversalReportService', 'تم بدء تنظيف الكاش التلقائي')
  }

  /**
   * تحديث الإعدادات الافتراضية
   */
  public updateDefaultSettings(settings: Partial<ReportSettings>): void {
    this.defaultSettings = { ...this.defaultSettings, ...settings }
    Logger.info('UniversalReportService', 'تم تحديث إعدادات التقارير')
  }

  /**
   * تحسين بيانات المشتريات للعرض
   */
  private optimizePurchasesData(data: any[]): any[] {
    return data.map(item => ({
      ...item,
      // تحسين عرض المبالغ
      total_amount: typeof item.total_amount === 'number' ?
        Math.round(item.total_amount * 100) / 100 : item.total_amount,
      paid_amount: typeof item.paid_amount === 'number' ?
        Math.round(item.paid_amount * 100) / 100 : item.paid_amount,
      outstanding_amount: typeof item.outstanding_amount === 'number' ?
        Math.round(item.outstanding_amount * 100) / 100 : item.outstanding_amount,

      // تحسين عرض النسب المئوية
      payment_percentage: typeof item.payment_percentage === 'number' ?
        Math.round(item.payment_percentage * 100) / 100 : item.payment_percentage,

      // تحسين عرض التواريخ
      order_date: item.order_date ? new Date(item.order_date).toLocaleDateString('ar-EG') : '',
      invoice_date: item.invoice_date ? new Date(item.invoice_date).toLocaleDateString('ar-EG') : '',
      last_purchase_date: item.last_purchase_date ? new Date(item.last_purchase_date).toLocaleDateString('ar-EG') : ''
    }));
  }

  /**
   * تحسين بيانات الموردين للعرض
   */
  private optimizeSuppliersData(data: any[]): any[] {
    return data.map(item => ({
      ...item,
      // تحسين تقييم الجودة
      quality_rating: typeof item.quality_rating === 'number' ?
        Math.round(item.quality_rating * 10) / 10 : item.quality_rating,
      quality_score: typeof item.quality_score === 'number' ?
        Math.round(item.quality_score * 10) / 10 : item.quality_score,

      // تحسين معدلات الأداء
      on_time_delivery_rate: typeof item.on_time_delivery_rate === 'number' ?
        Math.round(item.on_time_delivery_rate * 100) / 100 : item.on_time_delivery_rate,
      return_rate: typeof item.return_rate === 'number' ?
        Math.round(item.return_rate * 100) / 100 : item.return_rate,
      defect_rate: typeof item.defect_rate === 'number' ?
        Math.round(item.defect_rate * 100) / 100 : item.defect_rate,

      // تحسين المبالغ
      total_purchases: typeof item.total_purchases === 'number' ?
        Math.round(item.total_purchases * 100) / 100 : item.total_purchases,

      // معالجة شهادات الجودة
      quality_certifications: Array.isArray(item.quality_certifications) ?
        item.quality_certifications :
        (item.quality_certifications ? JSON.parse(item.quality_certifications) : [])
    }));
  }

  /**
   * حساب مقاييس أداء المشتريات
   */
  public async getPurchasePerformanceMetrics(_filters: ReportFilters = {}): Promise<any> {
    try {
      const startTime = performance.now();

      // تحميل البيانات الحقيقية من قاعدة البيانات
      if (!window.electronAPI) {
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      }

      const purchasesResponse = await window.electronAPI.getPurchaseAnalysisReport({})
      const suppliersResponse = await window.electronAPI.getSupplierAnalysisReport({})
      const ordersResponse = await window.electronAPI.getPurchaseOrders()

      const purchasesData = purchasesResponse?.data || []
      const suppliersData = suppliersResponse?.data || []
      const ordersData = ordersResponse?.data || []

      const metrics = {
        totalPurchases: purchasesData.reduce((sum: number, item: any) => sum + (item.total_amount || 0), 0),
        totalSuppliers: suppliersData.length,
        avgOrderValue: purchasesData.length > 0 ?
          purchasesData.reduce((sum: number, item: any) => sum + (item.total_amount || 0), 0) / purchasesData.length : 0,
        onTimeDeliveryRate: suppliersData.length > 0 ?
          suppliersData.reduce((sum: number, item: any) => sum + (item.on_time_delivery_rate || 0), 0) / suppliersData.length : 0,
        qualityScore: suppliersData.length > 0 ?
          suppliersData.reduce((sum: number, item: any) => sum + (item.quality_score || 0), 0) / suppliersData.length : 0,
        pendingOrders: ordersData.filter((order: any) => order.status === 'pending').length,
        overdueOrders: ordersData.filter((order: any) => order.status === 'overdue').length,
        costSavings: this.calculateCostSavings(purchasesData)
      };

      const endTime = performance.now();
      Logger.info('UniversalReportService', `تم حساب مقاييس المشتريات في ${endTime - startTime}ms`);

      return { success: true, data: metrics };
    } catch (error) {
      Logger.error('UniversalReportService', 'خطأ في حساب مقاييس أداء المشتريات:', error);
      return { success: false, message: 'حدث خطأ في حساب المقاييس' };
    }
  }

  /**
   * حساب التوفير في التكاليف
   */
  private calculateCostSavings(purchasesData: any[]): number {
    // حساب التوفير بناءً على مقارنة الأسعار والخصومات
    return purchasesData.reduce((savings: number, item: any) => {
      const discount = item.discount_amount || 0;
      const negotiatedSavings = item.negotiated_savings || 0;
      return savings + discount + negotiatedSavings;
    }, 0);
  }

  /**
   * تحميل دفعة من البيانات
   */
  private async loadReportBatch(
    type: ReportType,
    filters: ReportFilters,
    page: number,
    pageSize: number
  ): Promise<{
    success: boolean
    data?: ReportData
    totalRecords?: number
    hasMore?: boolean
    error?: string
  }> {
    try {
      const response = await window.electronAPI?.generateReportBatch?.(type, filters, page, pageSize)

      if (!response?.success) {
        throw new Error((response as any)?.message || 'فشل في تحميل الدفعة')
      }

      return {
        success: true,
        data: response.data,
        totalRecords: (response as any).totalRecords || 0,
        hasMore: (response as any).hasMore || false
      }
    } catch (error) {
      Logger.error('UniversalReportService', 'خطأ في تحميل الدفعة:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      }
    }
  }

  /**
   * تحميل الدفعة التالية في الخلفية
   */
  private async preloadNextBatch(type: ReportType, filters: ReportFilters, page: number): Promise<void> {
    try {
      const nextBatch = await this.loadReportBatch(type, filters, page, this.optimizationSettings.batchSize)

      if (nextBatch.success && nextBatch.data) {
        // حفظ الدفعة في كاش منفصل للدفعات المحملة مسبقاً
        const preloadKey = `preload_${this.generateCacheKey(type, filters)}_${page}`
        this.setCachedResult(preloadKey, nextBatch.data)

        Logger.info('UniversalReportService', `تم تحميل الدفعة ${page} مسبقاً`)
      }
    } catch (error) {
      Logger.warn('UniversalReportService', 'فشل في التحميل المسبق:', error)
    }
  }

  /**
   * تحسين بيانات التقرير
   */
  private async optimizeReportDataInternal(data: ReportData): Promise<ReportData> {
    // إذا كانت البيانات صغيرة، لا نحتاج لتحسين
    if (data.data.length < this.optimizationSettings.compressionThreshold) {
      return data
    }

    // تطبيق ضغط البيانات
    return await this.compressReportData(data)
  }

  /**
   * ضغط بيانات التقرير
   */
  private async compressReportData(data: ReportData): Promise<ReportData> {
    try {
      // ضغط البيانات الكبيرة فقط
      const compressedData = { ...data }

      if (data.data && data.data.length > 100) {
        // ضغط مصفوفة البيانات
        const dataString = JSON.stringify(data.data)
        const compressed = await this.compressString(dataString)

        compressedData.metadata = {
          ...data.metadata,
          isCompressed: true,
          originalSize: dataString.length,
          compressedSize: compressed.length
        }

        // حفظ البيانات المضغوطة في كاش منفصل
        const compressionKey = `compressed_${Date.now()}`
        this.compressionCache.set(compressionKey, {
          compressed,
          timestamp: Date.now()
        })

        compressedData.metadata.compressionKey = compressionKey

        // الاحتفاظ بعينة صغيرة للعرض
        compressedData.data = data.data.slice(0, 50)
      }

      return compressedData
    } catch (error) {
      Logger.warn('UniversalReportService', 'فشل في ضغط البيانات:', error)
      return data
    }
  }

  /**
   * ضغط النص
   */
  private async compressString(text: string): Promise<string> {
    try {
      // استخدام ضغط بسيط (يمكن تحسينه لاحقاً)
      return btoa(encodeURIComponent(text))
    } catch {
      return text
    }
  }

  /**
   * إلغاء ضغط النص
   */
  private async decompressString(compressed: string): Promise<string> {
    try {
      return decodeURIComponent(atob(compressed))
    } catch {
      return compressed
    }
  }

  /**
   * حساب حجم البيانات
   */
  private calculateDataSize(data: ReportData): number {
    try {
      return JSON.stringify(data).length
    } catch {
      return 0
    }
  }

  /**
   * تحسين حجم الكاش
   */
  private optimizeCacheSize(): void {
    const currentSize = this.getCurrentCacheSize()

    if (currentSize > this.optimizationSettings.maxCacheSize) {
      // حذف العناصر الأقدم حتى نصل للحد المسموح
      const sortedEntries = Array.from(this.cache.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp)

      let removedSize = 0
      for (const [key, value] of sortedEntries) {
        if (currentSize - removedSize <= this.optimizationSettings.maxCacheSize * 0.8) {
          break
        }

        removedSize += value.size || 0
        this.cache.delete(key)
      }

      Logger.info('UniversalReportService', `تم تحسين الكاش: حذف ${(removedSize / 1024 / 1024).toFixed(2)}MB`)
    }
  }

  /**
   * حساب الحجم الحالي للكاش
   */
  private getCurrentCacheSize(): number {
    let totalSize = 0
    for (const value of this.cache.values()) {
      totalSize += value.size || 0
    }
    return totalSize
  }
}
