import React, { useState, useEffect, useRef } from 'react'
import {
  Card,
  Table,
  DatePicker,
  Button,
  Space,
  Row,
  Col,
  Statistic,
  Select,
  message,
  Spin,
  Progress,
  Tag,
  Tooltip,
  Typography
} from 'antd'
import {
  DollarOutlined,
  ArrowUpOutlined,
  Pie<PERSON>hartOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  ReloadOutlined,
  <PERSON>Outlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import PrintableReport from './PrintableReport'
import { UnifiedPrintButton } from '../common'
import ExcelExportButton from './ExcelExportButton'

const { RangePicker } = DatePicker
const { Option } = Select
const { Title, Text } = Typography

interface ProfitabilityData {
  period: string
  revenue: number
  cost_of_goods: number
  gross_profit: number
  gross_margin: number
  operating_expenses: number
  operating_profit: number
  operating_margin: number
  net_profit: number
  net_margin: number
  roi: number
  profit_trend: 'up' | 'down' | 'stable'
}

const ProfitabilityReport: React.FC = () => {
  const [data, setData] = useState<ProfitabilityData[]>([])
  const [loading, setLoading] = useState(false)
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(6, 'months'),
    dayjs()
  ])
  const [periodType, setPeriodType] = useState<'monthly' | 'quarterly'>('monthly')
  const printRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    loadData()
  }, [periodType])

  const loadData = async () => {
    setLoading(true)
    try {
      if (!window.electronAPI) {
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      }

      // جلب بيانات الربحية الحقيقية من قاعدة البيانات
      const response = await window.electronAPI.getProfitabilityReport({
        dateRange: [
          dateRange[0].format('YYYY-MM-DD'),
          dateRange[1].format('YYYY-MM-DD')
        ],
        groupBy: periodType === 'monthly' ? 'month' : 'quarter'
      })

      if (!response || !response.success) {
        throw new Error('فشل في جلب تقرير الربحية من قاعدة البيانات')
      }

      const profitabilityData = response.data || []

      setData(profitabilityData)
    } catch (error) {
      message.error('فشل في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const columns = [
    {
      key: 'period',
      title: 'الفترة',
      width: '150px',
      align: 'center' as const,
      render: (period: string, record: ProfitabilityData) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{period}</div>
          <div style={{ fontSize: '12px' }}>
            {record.profit_trend === 'up' && <Tag color="green"><RiseOutlined /> صاعد</Tag>}
            {record.profit_trend === 'down' && <Tag color="red"><FallOutlined /> هابط</Tag>}
            {record.profit_trend === 'stable' && <Tag color="blue">ثابت</Tag>}
          </div>
        </div>
      )
    },
    {
      key: 'revenue',
      title: 'الإيرادات والتكاليف',
      width: '200px',
      align: 'center' as const,
      format: 'currency' as const,
      render: (revenue: number, record: ProfitabilityData) => (
        <div>
          <div style={{ color: '#1890ff', fontWeight: 'bold', marginBottom: '4px' }}>
            ₪{revenue.toLocaleString()}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            التكلفة: ₪{record.cost_of_goods.toLocaleString()}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            المصروفات: ₪{record.operating_expenses.toLocaleString()}
          </div>
        </div>
      )
    },
    {
      key: 'gross_profit',
      title: 'الربح الإجمالي',
      width: '180px',
      align: 'center' as const,
      format: 'currency' as const,
      render: (profit: number, record: ProfitabilityData) => (
        <div style={{ textAlign: 'center' }}>
          <div style={{ color: '#52c41a', fontWeight: 'bold', fontSize: '16px' }}>
            ₪{profit.toLocaleString()}
          </div>
          <Progress
            percent={record.gross_margin}
            size="small"
            status="active"
            style={{ marginTop: '4px' }}
          />
          <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
            هامش: {record.gross_margin.toFixed(1)}%
          </div>
        </div>
      )
    },
    {
      key: 'operating_profit',
      title: 'الربح التشغيلي',
      width: '180px',
      align: 'center' as const,
      format: 'currency' as const,
      render: (profit: number, record: ProfitabilityData) => (
        <div style={{ textAlign: 'center' }}>
          <div style={{ color: '#722ed1', fontWeight: 'bold', fontSize: '16px' }}>
            ₪{profit.toLocaleString()}
          </div>
          <Progress
            percent={record.operating_margin}
            size="small"
            status="active"
            style={{ marginTop: '4px' }}
          />
          <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
            هامش: {record.operating_margin.toFixed(1)}%
          </div>
        </div>
      )
    },
    {
      key: 'net_profit',
      title: 'صافي الربح',
      width: '180px',
      align: 'center' as const,
      format: 'currency' as const,
      render: (profit: number, record: ProfitabilityData) => (
        <div style={{ textAlign: 'center' }}>
          <div style={{ color: '#fa8c16', fontWeight: 'bold', fontSize: '16px' }}>
            ₪{profit.toLocaleString()}
          </div>
          <Progress
            percent={record.net_margin}
            size="small"
            status={record.net_margin >= 15 ? 'success' : 'active'}
            style={{ marginTop: '4px' }}
          />
          <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
            هامش: {record.net_margin.toFixed(1)}%
          </div>
        </div>
      )
    },
    {
      key: 'roi',
      title: 'العائد على الاستثمار',
      width: '150px',
      align: 'center' as const,
      format: 'percentage' as const,
      render: (roi: number) => (
        <div style={{ textAlign: 'center' }}>
          <div style={{
            color: roi >= 20 ? '#52c41a' : roi >= 15 ? '#fa8c16' : '#ff4d4f',
            fontWeight: 'bold',
            fontSize: '18px'
          }}>
            {roi.toFixed(1)}%
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>ROI</div>
        </div>
      )
    },
    {
      title: 'إجراءات',
      key: 'actions',
      width: 100,
      align: 'center' as const,
      render: (_, record: ProfitabilityData) => (
        <Space>
          <Tooltip title="عرض التفاصيل">
            <Button 
              type="text" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => message.info(`عرض تفاصيل ${record.period}`)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const stats = {
    totalRevenue: data.reduce((sum, item) => sum + item.revenue, 0),
    totalCosts: data.reduce((sum, item) => sum + item.cost_of_goods + item.operating_expenses, 0),
    totalGrossProfit: data.reduce((sum, item) => sum + item.gross_profit, 0),
    totalNetProfit: data.reduce((sum, item) => sum + item.net_profit, 0),
    avgGrossMargin: data.length > 0 ? data.reduce((sum, item) => sum + item.gross_margin, 0) / data.length : 0,
    avgNetMargin: data.length > 0 ? data.reduce((sum, item) => sum + item.net_margin, 0) / data.length : 0,
    avgROI: data.length > 0 ? data.reduce((sum, item) => sum + item.roi, 0) / data.length : 0,
    bestPeriod: data.length > 0 ? data.reduce((prev, current) => (prev.net_profit > current.net_profit) ? prev : current) : null
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* النسخة القابلة للطباعة */}
      <div style={{ display: 'none' }}>
        <PrintableReport
          ref={printRef}
          title="تقرير الربحية"
          subtitle="تحليل شامل للأرباح والهوامش والعائد على الاستثمار"
          reportDate={`${dateRange[0].format('YYYY-MM-DD')} إلى ${dateRange[1].format('YYYY-MM-DD')}`}
        >
          <Table
            columns={columns.filter(col => col.key !== 'actions')}
            dataSource={data}
            rowKey="period"
            pagination={false}
            size="small"
          />
        </PrintableReport>
      </div>

      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
            <PieChartOutlined style={{ marginLeft: '12px' }} />
            تقرير الربحية
          </Title>
          <Text style={{ color: '#666', fontSize: '16px' }}>
            تحليل شامل للأرباح والهوامش والعائد على الاستثمار مع مؤشرات الأداء المالي
          </Text>
        </div>

        {/* الإحصائيات الرئيسية */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card hoverable style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: '#fff' }}>
              <Statistic
                title={<span style={{ color: '#fff' }}>إجمالي الإيرادات</span>}
                value={stats.totalRevenue}
                valueStyle={{ color: '#fff', fontSize: '28px' }}
                prefix="₪"
                precision={0}
              />
              <div style={{ marginTop: '8px', fontSize: '12px', opacity: 0.8 }}>
                التكاليف: ₪{stats.totalCosts.toLocaleString()}
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card hoverable style={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: '#fff' }}>
              <Statistic
                title={<span style={{ color: '#fff' }}>إجمالي الربح</span>}
                value={stats.totalNetProfit}
                valueStyle={{ color: '#fff', fontSize: '28px' }}
                prefix="₪"
                precision={0}
              />
              <div style={{ marginTop: '8px', fontSize: '12px', opacity: 0.8 }}>
                هامش صافي: {stats.avgNetMargin.toFixed(1)}%
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card hoverable style={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: '#fff' }}>
              <Statistic
                title={<span style={{ color: '#fff' }}>هامش الربح الإجمالي</span>}
                value={stats.avgGrossMargin}
                valueStyle={{ color: '#fff', fontSize: '28px' }}
                suffix="%"
                precision={1}
              />
              <div style={{ marginTop: '8px', fontSize: '12px', opacity: 0.8 }}>
                الربح الإجمالي: ₪{stats.totalGrossProfit.toLocaleString()}
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card hoverable style={{ background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', color: '#fff' }}>
              <Statistic
                title={<span style={{ color: '#fff' }}>العائد على الاستثمار</span>}
                value={stats.avgROI}
                valueStyle={{ color: '#fff', fontSize: '28px' }}
                suffix="%"
                precision={1}
              />
              <div style={{ marginTop: '8px', fontSize: '12px', opacity: 0.8 }}>
                أفضل فترة: {stats.bestPeriod?.period || 'لا يوجد'}
              </div>
            </Card>
          </Col>
        </Row>

        {/* شريط الأدوات والفلاتر */}
        <Card style={{ marginBottom: '16px', background: '#fafafa' }}>
          <Row gutter={[16, 16]} align="middle">
            <Col span={8}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text strong>فترة التقرير:</Text>
                <RangePicker
                  value={dateRange}
                  onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
                  format="YYYY-MM-DD"
                  style={{ width: '100%' }}
                />
              </Space>
            </Col>
            <Col span={6}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text strong>نوع الفترة:</Text>
                <Select
                  value={periodType}
                  onChange={setPeriodType}
                  style={{ width: '100%' }}
                >
                  <Option value="monthly">شهرية</Option>
                  <Option value="quarterly">ربع سنوية</Option>
                </Select>
              </Space>
            </Col>
            <Col span={10}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text strong>الإجراءات:</Text>
                <Space>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={loadData}
                    loading={loading}
                    title="تحديث البيانات"
                  />
                  <UnifiedPrintButton
                    data={{
                      title: 'تقرير الربحية',
                      subtitle: 'تحليل شامل للأرباح والهوامش والعائد على الاستثمار',
                      dateRange: `${dateRange[0].format('YYYY-MM-DD')} إلى ${dateRange[1].format('YYYY-MM-DD')}`,
                      data: data,
                      summary: {
                        totalRevenue: data.reduce((sum, item) => sum + item.revenue, 0),
                        totalCosts: data.reduce((sum, item) => sum + (item.cost_of_goods + item.operating_expenses), 0),
                        totalProfit: data.reduce((sum, item) => sum + item.net_profit, 0),
                        avgProfitMargin: data.length > 0 ? data.reduce((sum, item) => sum + item.net_margin, 0) / data.length : 0
                      },
                      columns: [
                        { title: 'الفترة', key: 'period' },
                        { title: 'الإيرادات', key: 'revenue' },
                        { title: 'التكاليف', key: 'total_costs' },
                        { title: 'صافي الربح', key: 'net_profit' },
                        { title: 'هامش الربح %', key: 'profit_margin' },
                        { title: 'العائد على الاستثمار %', key: 'roi' }
                      ]
                    }}
                    type="report"
                    subType="financial"
                    buttonText="طباعة التقرير"
                    size="middle"
                    showDropdown={true}
                    _documentId="profitability_report"
                    onAfterPrint={() => message.success('تم طباعة التقرير بنجاح')}
                    onError={() => message.error('فشل في طباعة التقرير')}
                    disabled={loading || data.length === 0}
                  />
                  <ExcelExportButton
                    data={data}
                    filename="تقرير_الربحية"
                    sheetName="تقرير الربحية"
                    headers={{
                      period: 'الفترة',
                      revenue: 'الإيرادات',
                      cost_of_goods: 'تكلفة البضاعة',
                      gross_profit: 'الربح الإجمالي',
                      gross_margin: 'هامش الربح الإجمالي',
                      operating_expenses: 'المصروفات التشغيلية',
                      operating_profit: 'الربح التشغيلي',
                      operating_margin: 'هامش الربح التشغيلي',
                      net_profit: 'صافي الربح',
                      net_margin: 'هامش صافي الربح',
                      roi: 'العائد على الاستثمار',
                      profit_trend: 'اتجاه الربح'
                    }}
                    excludeColumns={[]}
                  />
                </Space>
              </Space>
            </Col>
          </Row>
        </Card>

        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={data}
            rowKey="period"
            pagination={false}
            scroll={{ x: 1400 }}
            size="middle"
            bordered
            summary={() => (
              <Table.Summary.Row style={{ backgroundColor: '#f0f2f5', fontWeight: 'bold' }}>
                <Table.Summary.Cell index={0}>
                  <strong style={{ fontSize: '14px' }}>الإجمالي ({data.length} فترة)</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>
                  <div>
                    <div style={{ color: '#1890ff', fontWeight: 'bold' }}>₪{stats.totalRevenue.toLocaleString()}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>إيرادات</div>
                  </div>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={2} align="center">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ color: '#52c41a', fontWeight: 'bold' }}>₪{stats.totalGrossProfit.toLocaleString()}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>هامش: {stats.avgGrossMargin.toFixed(1)}%</div>
                  </div>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={3} align="center">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ color: '#722ed1', fontWeight: 'bold' }}>₪{(stats.totalGrossProfit - (stats.totalRevenue - stats.totalGrossProfit - stats.totalCosts)).toLocaleString()}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>تشغيلي</div>
                  </div>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={4} align="center">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ color: '#fa8c16', fontWeight: 'bold' }}>₪{stats.totalNetProfit.toLocaleString()}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>صافي: {stats.avgNetMargin.toFixed(1)}%</div>
                  </div>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={5} align="center">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{
                      color: stats.avgROI >= 20 ? '#52c41a' : stats.avgROI >= 15 ? '#fa8c16' : '#ff4d4f',
                      fontWeight: 'bold'
                    }}>
                      {stats.avgROI.toFixed(1)}%
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>متوسط ROI</div>
                  </div>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={6}></Table.Summary.Cell>
              </Table.Summary.Row>
            )}
          />
        </Spin>
      </Card>
    </div>
  )
}

export default ProfitabilityReport
