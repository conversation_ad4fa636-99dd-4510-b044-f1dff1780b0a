import React from 'react'
import { Tooltip, Popover, Space } from 'antd'
import { QuestionCircleOutlined, InfoCircleOutlined } from '@ant-design/icons'

interface HelpTooltipProps {
  title: string
  content: string
  type?: 'tooltip' | 'popover'
  icon?: 'question' | 'info'
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight'
  color?: string
}

const HelpTooltip: React.FC<HelpTooltipProps> = ({
  title,
  content,
  type = 'tooltip',
  icon = 'question',
  placement = 'top',
  color = '#1890ff'
}) => {
  const IconComponent = icon === 'question' ? QuestionCircleOutlined : InfoCircleOutlined

  if (type === 'popover') {
    return (
      <Popover
        content={<div style={{ maxWidth: '300px' }}>{content}</div>}
        title={title}
        trigger="hover"
        placement={placement as any}
      >
        <IconComponent style={{ color, cursor: 'pointer' }} />
      </Popover>
    )
  }

  return (
    <Tooltip title={content} placement={placement as any}>
      <IconComponent style={{ color, cursor: 'pointer' }} />
    </Tooltip>
  )
}

interface LabelWithHelpProps {
  label: string
  helpContent: string
  helpTitle?: string
  helpType?: 'tooltip' | 'popover'
  helpIcon?: 'question' | 'info'
}

export const LabelWithHelp: React.FC<LabelWithHelpProps> = ({
  label,
  helpContent,
  helpTitle,
  helpType = 'tooltip',
  helpIcon = 'question'
}) => {
  return (
    <Space>
      {label}
      <HelpTooltip
        title={helpTitle || label}
        content={helpContent}
        type={helpType}
        icon={helpIcon}
      />
    </Space>
  )
}

export default HelpTooltip
