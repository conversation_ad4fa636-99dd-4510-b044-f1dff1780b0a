/**
 * تقرير تحليل العملاء
 * تقرير شامل لتحليل سلوك وأداء العملاء
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const CustomerAnalysisReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'sales_by_customer' as ReportType}
      title="تقرير تحليل العملاء"
      description="تحليل شامل لسلوك العملاء وأنماط الشراء والربحية"
      showDateRange={true}
      showCustomerFilter={true}
      showStatusFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="customer_analysis_report"
      defaultFilters={{
        sortBy: 'total_amount',
        sortOrder: 'desc'
      }}
    />
  )
}

export default CustomerAnalysisReport
