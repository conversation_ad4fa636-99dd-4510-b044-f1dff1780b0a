import * as XLSX from 'xlsx'
import jsPDF from 'jspdf'
// import html2canvas from 'html2canvas' // غير مستخدم حالياً
// import { saveAs } from 'file-saver' // غير مستخدم حالياً
import { App } from 'antd'
import { settingsManager } from './settings'
// استخدام الدوال الأساسية من excelExportUtils لتجنب التكرار
import { ExportableData, ExcelExportOptions } from './excelExportUtils'
import { SafeLogger as Logger } from './logger'

// أنواع البيانات للتصدير
export interface ExportColumn {
  key: string
  title: string
  dataIndex: string
  width?: number
  type?: 'text' | 'number' | 'currency' | 'date' | 'boolean'
  format?: (value: any) => string
  exportable?: boolean
}

export interface ExportOptions {
  filename?: string
  sheetName?: string
  includeHeaders?: boolean
  includeFooter?: boolean
  customFooter?: string
  dateFormat?: string
  numberFormat?: string
  currencySymbol?: string
  orientation?: 'portrait' | 'landscape'
  pageSize?: 'A4' | 'A3' | 'Letter'
  margins?: {
    top: number
    right: number
    bottom: number
    left: number
  }
}

export interface PrintOptions {
  title?: string
  subtitle?: string
  showDate?: boolean
  showPageNumbers?: boolean
  customHeader?: string
  customFooter?: string
  orientation?: 'portrait' | 'landscape'
  pageSize?: 'A4' | 'A3' | 'Letter'
  margins?: {
    top: number
    right: number
    bottom: number
    left: number
  }
  scale?: number
  quality?: number
}

// فئة إدارة التصدير المحسنة
class EnhancedExportManager {
  private messageApi: any = null

  // تعيين message API
  setMessageApi(messageApi: any) {
    this.messageApi = messageApi
  }

  // دالة مساعدة للرسائل
  private showMessage(type: 'success' | 'error' | 'warning', content: string) {
    if (this.messageApi) {
      this.messageApi[type](content)
    } else {
      console.log(`${type.toUpperCase()}: ${content}`)
    }
  }
  private getDefaultOptions(): ExportOptions {
    // استخدام العملة من الإعدادات
    const settings = settingsManager.getSettings()
    const currencySymbol = settings?.currencySymbol || '₪'

    return {
      filename: 'تقرير',
      sheetName: 'البيانات',
      includeHeaders: true,
      includeFooter: true,
      dateFormat: 'YYYY-MM-DD',
      numberFormat: '#,##0.00',
      currencySymbol: currencySymbol,
      orientation: 'landscape',
      pageSize: 'A4',
      margins: {
        top: 20,
        right: 20,
        bottom: 20,
        left: 20
      }
    }
  }

  // تصدير إلى Excel مع تحسينات
  async exportToExcel<T extends Record<string, any>>(
    data: T[],
    columns: ExportColumn[],
    options: Partial<ExportOptions> = {}
  ): Promise<void> {
    try {
      const opts = { ...this.getDefaultOptions(), ...options }
      
      // تصفية الأعمدة القابلة للتصدير
      const exportableColumns = columns.filter(col => col.exportable !== false)
      
      // إعداد البيانات للتصدير
      const exportData = data.map(row => {
        const exportRow: Record<string, any> = {}
        
        exportableColumns.forEach(col => {
          let value = row[col.dataIndex]
          
          // تطبيق التنسيق المخصص
          if (col.format) {
            value = col.format(value)
          } else {
            // تنسيق تلقائي حسب النوع
            value = this.formatValue(value, col.type, opts)
          }
          
          exportRow[col.title] = value
        })
        
        return exportRow
      })

      // إنشاء ورقة العمل
      const worksheet = XLSX.utils.json_to_sheet(exportData)
      
      // تطبيق عرض الأعمدة
      const columnWidths = exportableColumns.map(col => ({
        wch: col.width ? Math.max(col.width / 8, 10) : 15
      }))
      worksheet['!cols'] = columnWidths
      
      // إضافة تنسيق للرؤوس
      const headerRange = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
      for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col })
        if (worksheet[cellAddress]) {
          worksheet[cellAddress].s = {
            font: { bold: true, color: { rgb: 'FFFFFF' } },
            fill: { fgColor: { rgb: '1890FF' } },
            alignment: { horizontal: 'center', vertical: 'center' }
          }
        }
      }
      
      // إضافة تذييل إذا كان مطلوباً
      if (opts.includeFooter) {
        const _footerRow = exportableColumns.length
        const footerText = opts.customFooter || 
          `تم إنشاء التقرير في: ${new Date().toLocaleDateString('ar-EG')}`
        
        const footerCell = XLSX.utils.encode_cell({ r: data.length + 1, c: 0 })
        worksheet[footerCell] = {
          v: footerText,
          t: 's',
          s: {
            font: { italic: true, color: { rgb: '666666' } },
            alignment: { horizontal: 'left' }
          }
        }
        
        // دمج خلايا التذييل
        if (!worksheet['!merges']) worksheet['!merges'] = []
        worksheet['!merges'].push({
          s: { r: data.length + 1, c: 0 },
          e: { r: data.length + 1, c: exportableColumns.length - 1 }
        })
      }

      // إنشاء المصنف
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, opts.sheetName)
      
      // إعداد خصائص المصنف
      workbook.Props = {
        Title: opts.filename,
        Subject: 'تقرير من نظام المحاسبة',
        Author: 'نظام فارس للمحاسبة',
        CreatedDate: new Date()
      }

      // حفظ الملف
      const fileName = `${opts.filename}_${new Date().toISOString().split('T')[0]}.xlsx`
      XLSX.writeFile(workbook, fileName)
      
      this.showMessage('success', `تم تصدير ${data.length} سجل إلى ملف Excel بنجاح`)

    } catch (error) {
      console.error('خطأ في تصدير Excel:', error)
      this.showMessage('error', 'فشل في تصدير البيانات إلى Excel')
      throw error
    }
  }

  // تصدير إلى PDF مع تحسينات
  async exportToPDF<T extends Record<string, any>>(
    data: T[],
    columns: ExportColumn[],
    options: Partial<ExportOptions & PrintOptions> = {}
  ): Promise<void> {
    try {
      const opts = { ...this.getDefaultOptions(), ...options }
      
      // إنشاء مستند PDF
      const pdf = new jsPDF({
        orientation: opts.orientation,
        unit: 'mm',
        format: opts.pageSize?.toLowerCase() as any
      })
      
      // إعداد الخط العربي
      pdf.setFont('helvetica')
      pdf.setFontSize(12)
      
      // إضافة العنوان
      if (options.title) {
        pdf.setFontSize(16)
        pdf.text(options.title, pdf.internal.pageSize.width / 2, 20, { align: 'center' })
      }
      
      // إضافة العنوان الفرعي
      if (options.subtitle) {
        pdf.setFontSize(12)
        pdf.text(options.subtitle, pdf.internal.pageSize.width / 2, 30, { align: 'center' })
      }
      
      // إضافة التاريخ
      if (options.showDate !== false) {
        pdf.setFontSize(10)
        pdf.text(
          `تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}`,
          opts.margins?.left || 20,
          40
        )
      }
      
      // إعداد الجدول
      const exportableColumns = columns.filter(col => col.exportable !== false)
      const headers = exportableColumns.map(col => col.title)
      
      const tableData = data.map(row => 
        exportableColumns.map(col => {
          const value = row[col.dataIndex]
          return col.format ? col.format(value) : this.formatValue(value, col.type, opts)
        })
      )
      
      // إضافة الجدول باستخدام autoTable (يتطلب تثبيت jspdf-autotable)
      // هذا مثال مبسط - في التطبيق الحقيقي نحتاج مكتبة إضافية
      let yPosition = 50
      const rowHeight = 8
      const leftMargin = opts.margins?.left || 20
      const rightMargin = opts.margins?.right || 20
      const colWidth = (pdf.internal.pageSize.width - leftMargin - rightMargin) / headers.length

      // رسم الرؤوس
      pdf.setFillColor(24, 144, 255)
      pdf.setTextColor(255, 255, 255)
      pdf.rect(leftMargin, yPosition, pdf.internal.pageSize.width - leftMargin - rightMargin, rowHeight, 'F')

      headers.forEach((header, index) => {
        pdf.text(
          header,
          leftMargin + (index * colWidth) + (colWidth / 2),
          yPosition + 5,
          { align: 'center' }
        )
      })
      
      yPosition += rowHeight
      pdf.setTextColor(0, 0, 0)
      
      // رسم البيانات
      tableData.forEach((row, rowIndex) => {
        if (yPosition > pdf.internal.pageSize.height - 30) {
          pdf.addPage()
          yPosition = 20
        }
        
        // خلفية متناوبة للصفوف
        if (rowIndex % 2 === 0) {
          pdf.setFillColor(248, 249, 250)
          pdf.rect(leftMargin, yPosition, pdf.internal.pageSize.width - leftMargin - rightMargin, rowHeight, 'F')
        }

        row.forEach((cell, cellIndex) => {
          pdf.text(
            String(cell || ''),
            leftMargin + (cellIndex * colWidth) + 2,
            yPosition + 5
          )
        })
        
        yPosition += rowHeight
      })
      
      // إضافة تذييل
      if (opts.includeFooter) {
        const footerText = opts.customFooter || 
          `تم إنشاء التقرير بواسطة نظام فارس للمحاسبة - ${new Date().toLocaleDateString('ar-EG')}`
        
        pdf.setFontSize(8)
        pdf.text(
          footerText,
          pdf.internal.pageSize.width / 2,
          pdf.internal.pageSize.height - 10,
          { align: 'center' }
        )
      }
      
      // إضافة أرقام الصفحات
      if (options.showPageNumbers !== false) {
        const pageCount = pdf.getNumberOfPages()
        for (let i = 1; i <= pageCount; i++) {
          pdf.setPage(i)
          pdf.setFontSize(8)
          pdf.text(
            `صفحة ${i} من ${pageCount}`,
            pdf.internal.pageSize.width - rightMargin,
            pdf.internal.pageSize.height - 10,
            { align: 'right' }
          )
        }
      }
      
      // حفظ الملف
      const fileName = `${opts.filename}_${new Date().toISOString().split('T')[0]}.pdf`
      pdf.save(fileName)
      
      this.showMessage('success', `تم تصدير ${data.length} سجل إلى ملف PDF بنجاح`)

    } catch (error) {
      console.error('خطأ في تصدير PDF:', error)
      this.showMessage('error', 'فشل في تصدير البيانات إلى PDF')
      throw error
    }
  }

  // طباعة محسنة للجداول باستخدام النظام المركزي
  async printTable(
    elementId: string,
    options: Partial<PrintOptions> = {}
  ): Promise<void> {
    try {
      const element = document.getElementById(elementId)
      if (!element) {
        throw new Error('العنصر المطلوب طباعته غير موجود')
      }

      // استخدام النظام المركزي للطباعة
      const { MasterPrintService } = await import('../services/MasterPrintService')
      const printService = MasterPrintService.getInstance()

      // تحضير بيانات الطباعة من العنصر
      const printData = {
        title: options.title || 'جدول بيانات',
        subtitle: options.subtitle,
        date: new Date().toLocaleDateString('ar-SA'),
        content: element.outerHTML,
        metadata: {
          generatedAt: new Date().toISOString(),
          elementId: elementId,
          printType: 'table'
        }
      }

      await printService.print(printData, {
        type: 'report',
        subType: 'financial',
        showLogo: true,
        showHeader: true,
        showFooter: true,
        orientation: options.orientation || 'portrait',
        pageSize: options.pageSize || 'A4'
      })

      this.showMessage('success', 'تم إرسال المحتوى للطباعة')
      
    } catch (error) {
      console.error('خطأ في الطباعة:', error)
      this.showMessage('error', 'فشل في طباعة المحتوى')
      throw error
    }
  }

  // تنسيق القيم حسب النوع
  private formatValue(value: any, type?: string, options?: Partial<ExportOptions>): string {
    if (value === null || value === undefined) return ''
    
    switch (type) {
      case 'currency': {
        const num = parseFloat(value)
        if (isNaN(num)) return value

        // استخدام العملة من الإعدادات إذا لم تكن محددة في الخيارات
        const settings = settingsManager.getSettings()
        const currencySymbol = options?.currencySymbol || settings?.currencySymbol || '₪'

        return `${num.toLocaleString('ar-EG', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })} ${currencySymbol}`
      }

      case 'number': {
        const number = parseFloat(value)
        return isNaN(number) ? value : number.toLocaleString('ar-EG')
      }
        
      case 'date':
        if (value instanceof Date) {
          return value.toLocaleDateString('ar-EG')
        } else if (typeof value === 'string') {
          const date = new Date(value)
          return isNaN(date.getTime()) ? value : date.toLocaleDateString('ar-EG')
        }
        return value
        
      case 'boolean':
        return value ? 'نعم' : 'لا'
        
      default:
        return String(value)
    }
  }

  // إنشاء HTML للطباعة
  private generatePrintHTML(element: HTMLElement, options: Partial<PrintOptions>): string {
    const styles = `
      <style>
        @page {
          size: ${options.pageSize || 'A4'} ${options.orientation || 'portrait'};
          margin: ${options.margins?.top || 20}mm ${options.margins?.right || 20}mm 
                  ${options.margins?.bottom || 20}mm ${options.margins?.left || 20}mm;
        }
        
        body {
          font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
          font-size: 12px;
          line-height: 1.4;
          color: #333;
          direction: rtl;
          text-align: right;
        }
        
        .print-header {
          text-align: center;
          margin-bottom: 20px;
          border-bottom: 2px solid #1890ff;
          padding-bottom: 10px;
        }
        
        .print-title {
          font-size: 18px;
          font-weight: bold;
          color: #1890ff;
          margin-bottom: 5px;
        }
        
        .print-subtitle {
          font-size: 14px;
          color: #666;
          margin-bottom: 5px;
        }
        
        .print-date {
          font-size: 10px;
          color: #999;
        }
        
        .print-content {
          margin: 20px 0;
        }
        
        .print-footer {
          text-align: center;
          margin-top: 20px;
          padding-top: 10px;
          border-top: 1px solid #ddd;
          font-size: 10px;
          color: #666;
        }
        
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 10px 0;
        }
        
        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: right;
        }
        
        th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        
        tr:nth-child(even) {
          background-color: #fafafa;
        }
        
        @media print {
          .ant-btn, .ant-pagination, .no-print {
            display: none !important;
          }
          
          .print-break {
            page-break-before: always;
          }
        }
      </style>
    `

    const header = `
      <div class="print-header">
        ${options.title ? `<div class="print-title">${options.title}</div>` : ''}
        ${options.subtitle ? `<div class="print-subtitle">${options.subtitle}</div>` : ''}
        ${options.showDate !== false ? `<div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</div>` : ''}
        ${options.customHeader ? `<div>${options.customHeader}</div>` : ''}
      </div>
    `

    const footer = `
      <div class="print-footer">
        ${options.customFooter || 'تم إنشاء هذا التقرير بواسطة نظام فارس للمحاسبة'}
      </div>
    `

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <title>${options.title || 'طباعة'}</title>
          ${styles}
        </head>
        <body>
          ${header}
          <div class="print-content">
            ${element.innerHTML}
          </div>
          ${footer}
        </body>
      </html>
    `
  }
}

// إنشاء مثيل واحد
export const enhancedExportManager = new EnhancedExportManager()
