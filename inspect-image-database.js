const fs = require('fs');
const path = require('path');
const initSqlJs = require('sql.js');

async function inspectImageDatabase() {
  try {
    console.log('🔍 بدء فحص قاعدة البيانات للصور...\n');
    
    // مسار قاعدة البيانات
    const dbPath = path.join(process.env.APPDATA, 'accounting-production-app-final', 'accounting.db');
    console.log(`📍 مسار قاعدة البيانات: ${dbPath}`);
    
    // التحقق من وجود الملف
    if (!fs.existsSync(dbPath)) {
      console.log('❌ ملف قاعدة البيانات غير موجود!');
      return;
    }
    
    // قراءة حجم الملف
    const stats = fs.statSync(dbPath);
    console.log(`📊 حجم قاعدة البيانات: ${(stats.size / 1024).toFixed(2)} KB`);
    console.log(`📅 آخر تعديل: ${stats.mtime}`);
    
    // تهيئة SQL.js
    const SQL = await initSqlJs({
      locateFile: file => path.join(__dirname, 'node_modules', 'sql.js', 'dist', file)
    });
    
    // قراءة قاعدة البيانات
    const filebuffer = fs.readFileSync(dbPath);
    const db = new SQL.Database(filebuffer);
    
    console.log('\n✅ تم تحميل قاعدة البيانات بنجاح\n');
    
    // فحص الجداول الموجودة
    console.log('📋 الجداول الموجودة:');
    const tables = db.exec("SELECT name FROM sqlite_master WHERE type='table'");
    
    if (tables.length === 0 || !tables[0].values) {
      console.log('❌ لا توجد جداول في قاعدة البيانات!');
      return;
    }
    
    const tableNames = tables[0].values.map(row => row[0]);
    console.log(`   العدد الإجمالي: ${tableNames.length}`);
    tableNames.forEach((table, index) => {
      console.log(`   ${index + 1}. ${table}`);
    });
    
    // البحث عن جداول الصور
    const imageTables = tableNames.filter(table => 
      table.includes('image') || table.includes('unified_images') || table.includes('item_images')
    );
    
    console.log('\n🖼️ جداول الصور الموجودة:');
    if (imageTables.length === 0) {
      console.log('   ❌ لا توجد جداول صور!');
    } else {
      imageTables.forEach(table => {
        console.log(`   📸 ${table}`);
      });
    }
    
    // فحص جدول unified_images إذا كان موجوداً
    if (imageTables.includes('unified_images')) {
      console.log('\n📊 فحص جدول unified_images:');
      
      // عدد الصور
      const countResult = db.exec("SELECT COUNT(*) as count FROM unified_images");
      const totalImages = countResult[0]?.values[0]?.[0] || 0;
      console.log(`   📈 إجمالي الصور: ${totalImages}`);
      
      if (totalImages > 0) {
        // الصور النشطة
        const activeResult = db.exec("SELECT COUNT(*) as count FROM unified_images WHERE is_active = 1");
        const activeImages = activeResult[0]?.values[0]?.[0] || 0;
        console.log(`   ✅ الصور النشطة: ${activeImages}`);
        
        // الصور حسب الفئة
        console.log('\n   📂 الصور حسب الفئة:');
        const categoryResult = db.exec(`
          SELECT category, COUNT(*) as count 
          FROM unified_images 
          WHERE is_active = 1 
          GROUP BY category
        `);
        
        if (categoryResult[0]?.values) {
          categoryResult[0].values.forEach(row => {
            console.log(`      ${row[0]}: ${row[1]} صورة`);
          });
        }
        
        // الصور حسب نوع السياق
        console.log('\n   🏷️ الصور حسب نوع السياق:');
        const contextResult = db.exec(`
          SELECT context_type, COUNT(*) as count 
          FROM unified_images 
          WHERE is_active = 1 
          GROUP BY context_type
        `);
        
        if (contextResult[0]?.values) {
          contextResult[0].values.forEach(row => {
            console.log(`      ${row[0]}: ${row[1]} صورة`);
          });
        }
        
        // عينة من الصور
        console.log('\n   📋 عينة من الصور (أول 5):');
        const sampleResult = db.exec(`
          SELECT id, name, path, category, context_type, context_id, size, uploaded_at
          FROM unified_images 
          WHERE is_active = 1 
          ORDER BY uploaded_at DESC 
          LIMIT 5
        `);
        
        if (sampleResult[0]?.values) {
          sampleResult[0].values.forEach((row, index) => {
            console.log(`      ${index + 1}. ${row[1]} (${row[3]}/${row[4]})`);
            console.log(`         المسار: ${row[2]}`);
            console.log(`         الحجم: ${(row[6] / 1024).toFixed(2)} KB`);
            console.log(`         التاريخ: ${row[7]}`);
            console.log('');
          });
        }
        
        // فحص مسارات الصور
        console.log('\n   🔍 فحص مسارات الصور:');
        const pathsResult = db.exec(`
          SELECT DISTINCT SUBSTR(path, 1, INSTR(path, '/') - 1) as root_folder, COUNT(*) as count
          FROM unified_images 
          WHERE is_active = 1 AND path LIKE '%/%'
          GROUP BY root_folder
        `);
        
        if (pathsResult[0]?.values) {
          pathsResult[0].values.forEach(row => {
            console.log(`      ${row[0]}/: ${row[1]} صورة`);
          });
        }
      }
    }
    
    // فحص جدول item_images إذا كان موجوداً
    if (imageTables.includes('item_images')) {
      console.log('\n📊 فحص جدول item_images (القديم):');
      
      const oldCountResult = db.exec("SELECT COUNT(*) as count FROM item_images");
      const oldTotalImages = oldCountResult[0]?.values[0]?.[0] || 0;
      console.log(`   📈 إجمالي الصور: ${oldTotalImages}`);
      
      if (oldTotalImages > 0) {
        // عينة من الصور القديمة
        console.log('\n   📋 عينة من الصور القديمة (أول 3):');
        const oldSampleResult = db.exec(`
          SELECT id, item_id, image_url, image_data, created_at
          FROM item_images 
          ORDER BY created_at DESC 
          LIMIT 3
        `);
        
        if (oldSampleResult[0]?.values) {
          oldSampleResult[0].values.forEach((row, index) => {
            console.log(`      ${index + 1}. صورة للعنصر ${row[1]}`);
            console.log(`         URL: ${row[2] ? row[2].substring(0, 50) + '...' : 'غير محدد'}`);
            console.log(`         البيانات: ${row[3] ? 'موجودة' : 'غير موجودة'}`);
            console.log(`         التاريخ: ${row[4]}`);
            console.log('');
          });
        }
      }
    }
    
    // فحص جداول الإنتاج
    const productionTables = tableNames.filter(table => 
      table.includes('production') || table.includes('furniture')
    );
    
    if (productionTables.length > 0) {
      console.log('\n🏭 جداول الإنتاج الموجودة:');
      productionTables.forEach(table => {
        console.log(`   🔧 ${table}`);
        
        // عدد السجلات
        try {
          const countResult = db.exec(`SELECT COUNT(*) as count FROM ${table}`);
          const count = countResult[0]?.values[0]?.[0] || 0;
          console.log(`      📊 عدد السجلات: ${count}`);
        } catch (error) {
          console.log(`      ❌ خطأ في قراءة الجدول: ${error.message}`);
        }
      });
    }
    
    db.close();
    console.log('\n✅ تم إنهاء فحص قاعدة البيانات');
    
  } catch (error) {
    console.error('❌ خطأ في فحص قاعدة البيانات:', error);
  }
}

// تشغيل الفحص
inspectImageDatabase();
