import React, { useState, useEffect } from 'react'
import {
  Card, Table, Button, Modal, Form, Input, Select, InputNumber, Space,
  Typography, Row, Col, Statistic, Tag, App} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  ArrowUpOutlined, ArrowDownOutlined, SwapOutlined,
  HistoryOutlined, InboxOutlined, SearchOutlined, PrinterOutlined,
  FileExcelOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { InventoryMovement, Item, Warehouse, ApiResponse } from '../../types/global'
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig'
import * as XLSX from 'xlsx'

const { Title } = Typography
const { Option } = Select

const StyledCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
`

const MovementTypeTag = styled(Tag).withConfig({
  shouldForwardProp: (prop) => prop !== 'movementType'
})<{ movementType: string }>`
  font-weight: bold;
`

interface InventoryMovementsProps {
  onBack?: () => void
}

const InventoryMovements: React.FC<InventoryMovementsProps> = () => {
  const { message: messageApi } = App.useApp()
  const [movements, setMovements] = useState<InventoryMovement[]>([])
  const [items, setItems] = useState<Item[]>([])
  const [warehouses, setWarehouses] = useState<Warehouse[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [/* activeTab */, /* setActiveTab */] = useState('in')
  const [filters, setFilters] = useState<any>({})
  const [form] = Form.useForm()

  useEffect(() => {
    loadMovements()
    loadItems()
    loadWarehouses()
  }, [])

  const loadMovements = async (filterParams?: any) => {
    setLoading(true)
    try {
      Logger.info('InventoryMovements', '🔄 جاري تحميل حركات المخزون...')

      if (window.electronAPI) {
        const response = await window.electronAPI.getInventoryMovements(filterParams)
        if (response && (response as any).success) {
          setMovements((response as any).data || [])
          Logger.info('InventoryMovements', '✅ تم تحميل ${(response as any).data?.length || 0} حركة مخزون من قاعدة البيانات')
        } else {
          Logger.error('InventoryMovements', '❌ خطأ في تحميل حركات المخزون:', (response as any)?.message)
          messageApi.error((response as any)?.message || 'فشل في تحميل حركات المخزون')
          setMovements([])
        }
      } else {
        Logger.error('InventoryMovements', '❌ window.electronAPI غير متوفر')
        messageApi.error('لا يمكن الوصول إلى قاعدة البيانات')
        setMovements([])
      }
    } catch (error) {
      Logger.error('InventoryMovements', 'خطأ في تحميل حركات المخزون:', error)
      messageApi.error('فشل في تحميل حركات المخزون')
    } finally {
      setLoading(false)
    }
  }

  const loadItems = async () => {
    try {
      Logger.info('InventoryMovements', '🔄 جاري تحميل الأصناف...')

      if (window.electronAPI) {
        const response = await window.electronAPI.getItems()
        if (response && (response as any).success) {
          const itemsData = (response as any).data || []
          setItems(itemsData.filter((item: Item) => item.is_active))
          Logger.info('InventoryMovements', '✅ تم تحميل ${itemsData.length} صنف من قاعدة البيانات')
        } else if (Array.isArray(response)) {
          // للتوافق مع الإصدارات القديمة
          setItems(response.filter((item: Item) => item.is_active))
          Logger.info('InventoryMovements', '✅ تم تحميل ${response.length} صنف من قاعدة البيانات')
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل الأصناف'
          Logger.error('InventoryMovements', '❌ خطأ في تحميل الأصناف:', errorMessage)
          messageApi.error(errorMessage)
          setItems([])
        }
      } else {
        Logger.error('InventoryMovements', '❌ window.electronAPI غير متوفر')
        messageApi.error('لا يمكن الوصول إلى قاعدة البيانات')
        setItems([])
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل الأصناف'
      Logger.error('InventoryMovements', '❌ خطأ في تحميل الأصناف:', error)
      messageApi.error(`خطأ في تحميل الأصناف: ${errorMessage}`)
      setItems([])
    }
  }

  const loadWarehouses = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getWarehouses()
        if ((response as any).success && Array.isArray((response as any).data)) {
          setWarehouses((response as any).data.filter((w: Warehouse) => w.is_active))
        } else {
          Logger.error('InventoryMovements', 'خطأ في استجابة المخازن:', response)
          setWarehouses([])
        }
      }
    } catch (error) {
      Logger.error('InventoryMovements', 'خطأ في تحميل المخازن:', error)
    }
  }

  const handleAddMovement = (movementType: string) => {
    Logger.info('InventoryMovements', '🆕 إضافة حركة مخزون جديدة - النوع: ${movementType}')
    form.resetFields()
    form.setFieldsValue({
      movement_type: movementType,
      reference_type: 'manual' // تعيين افتراضي
    })

    // تعيين قيم افتراضية حسب نوع الحركة
    if (movementType === 'in') {
      Logger.info('InventoryMovements', '📥 حركة إدخال مخزون')
    } else if (movementType === 'out') {
      Logger.info('InventoryMovements', '📤 حركة إخراج مخزون')
    } else if (movementType === 'transfer') {
      Logger.info('InventoryMovements', '🔄 حركة تحويل مخزون')
    } else if (movementType === 'adjustment') {
      Logger.info('InventoryMovements', '⚖️ حركة تسوية مخزون')
    }

    setModalVisible(true)
  }

  const checkItemAvailability = async (itemId: number, warehouseId: number, requestedQuantity: number): Promise<{ available: boolean, currentQuantity: number, availableQuantity: number }> => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getItemWarehouseQuantity(itemId, warehouseId)
        if (response.success && response.data) {
          const { quantity, reserved_quantity } = response.data
          const availableQuantity = quantity - (reserved_quantity || 0)
          return {
            available: availableQuantity >= requestedQuantity,
            currentQuantity: quantity || 0,
            availableQuantity: availableQuantity || 0
          }
        }
      }
      return { available: false, currentQuantity: 0, availableQuantity: 0 }
    } catch (error) {
      Logger.error('InventoryMovements', 'خطأ في التحقق من توفر الكمية:', error)
      return { available: false, currentQuantity: 0, availableQuantity: 0 }
    }
  }

  const handleSubmit = async (values: any) => {
    Logger.info('InventoryMovements', '🔄 بدء إرسال حركة المخزون:', values)

    try {
      if (!window.electronAPI) {
        Logger.error('InventoryMovements', '❌ window.electronAPI غير متوفر')
        messageApi.error('لا يمكن الوصول إلى قاعدة البيانات')
        return
      }

      // التحقق من صحة البيانات الأساسية أولاً
      if (!values.item_id) {
        Logger.error('InventoryMovements', '❌ معرف الصنف مفقود')
        messageApi.error('يجب اختيار الصنف')
        return
      }

      if (!values.warehouse_id) {
        Logger.error('InventoryMovements', '❌ معرف المخزن مفقود')
        messageApi.error('يجب اختيار المخزن')
        return
      }

      if (!values.quantity || values.quantity <= 0) {
        Logger.error('InventoryMovements', '❌ الكمية غير صحيحة:', values.quantity)
        messageApi.error('يجب إدخال كمية صحيحة')
        return
      }

      if (!values.movement_type) {
        Logger.error('InventoryMovements', '❌ نوع الحركة مفقود')
        messageApi.error('نوع الحركة مطلوب')
        return
      }

      Logger.info('InventoryMovements', '✅ جميع البيانات الأساسية صحيحة')

      // التحقق من توفر الكمية للحركات الصادرة فقط
      if (values.movement_type === 'out') {
        Logger.info('InventoryMovements', '🔍 التحقق من توفر الكمية للحركة الصادرة...')
        const availabilityCheck = await checkItemAvailability(values.item_id, values.warehouse_id, values.quantity)

        if (!availabilityCheck.available) {
          const selectedItem = items.find(item => item.id === values.item_id)
          const selectedWarehouse = warehouses.find(wh => wh.id === values.warehouse_id)

          const errorMessage = `الكمية المطلوبة (${values.quantity}) غير متوفرة في المخزن. ` +
            `الكمية المتاحة: ${availabilityCheck.availableQuantity} من ${selectedItem?.name || 'الصنف'} ` +
            `في مخزن ${selectedWarehouse?.name || 'المخزن'}`

          Logger.error('InventoryMovements', '❌ الكمية غير متوفرة:', errorMessage)
          messageApi.error(errorMessage)

          form.setFields([{
            name: 'quantity',
            errors: [`الكمية المتاحة: ${availabilityCheck.availableQuantity} فقط`]
          }])
          return
        }
        Logger.info('InventoryMovements', '✅ الكمية متوفرة للحركة الصادرة')
      }

      // إرسال البيانات لإنشاء الحركة
      Logger.info('InventoryMovements', '📤 إرسال البيانات لإنشاء حركة المخزون...')
      const response: ApiResponse = await window.electronAPI.createInventoryMovement(values)

      Logger.info('InventoryMovements', '📥 استجابة الخادم:', response)

      if (response.success) {
        Logger.info('InventoryMovements', '✅ تم إنشاء حركة المخزون بنجاح')
        messageApi.success('تم إضافة حركة المخزون بنجاح')
        setModalVisible(false)
        form.resetFields()
        await loadMovements(filters) // انتّار إعادة التحميل
      } else {
        const errorMessage = response.message || 'فشل في إضافة حركة المخزون'
        Logger.error('InventoryMovements', '❌ فشل في إنشاء حركة المخزون:', errorMessage)
        messageApi.error(errorMessage)
      }
    } catch (error) {
      Logger.error('InventoryMovements', '❌ خطأ في إضافة حركة المخزون:', error)
      const errorMessage = error instanceof Error ? error.message : 'فشل في إضافة حركة المخزون'
      messageApi.error(errorMessage)
    }
  }

  const handleFilter = (filterValues: any) => {
    const newFilters = { ...filterValues }
    setFilters(newFilters)
    loadMovements(newFilters)
  }

  const getMovementTypeColor = (type: string) => {
    switch (type) {
      case 'in': return 'green'
      case 'out': return 'red'
      case 'transfer': return 'blue'
      case 'adjustment': return 'orange'
      default: return 'default'
    }
  }

  const getMovementTypeIcon = (type: string) => {
    switch (type) {
      case 'in': return <ArrowUpOutlined />
      case 'out': return <ArrowDownOutlined />
      case 'transfer': return <SwapOutlined />
      case 'adjustment': return <InboxOutlined />
      default: return <HistoryOutlined />
    }
  }

  const getMovementTypeText = (type: string) => {
    switch (type) {
      case 'in': return 'إدخال'
      case 'out': return 'إخراج'
      case 'transfer': return 'تحويل'
      case 'adjustment': return 'تسوية'
      default: return type
    }
  }

  // وّيفة الطباعة الموحدة
  const handlePrint = async () => {
    try {
      const { MasterPrintService } = await import('../../services/MasterPrintService')
      const printService = MasterPrintService.getInstance()

      const printData = {
        title: 'تقرير حركات المخزون',
        subtitle: `إجمالي الحركات: ${movements.length}`,
        date: new Date().toLocaleDateString('ar-SA'),
        items: movements.map(movement => ({
          name: movement.item_name,
          description: `${getMovementTypeText(movement.movement_type)} - ${movement.reference_number || ''}`,
          quantity: movement.quantity,
          unit: movement.unit || 'قطعة',
          unitPrice: movement.unit_cost || 0,
          total: movement.quantity * (movement.unit_cost || 0)
        })),
        total: movements.reduce((sum, movement) => sum + (movement.quantity * (movement.unit_cost || 0)), 0),
        notes: 'تقرير حركات المخزون'
      }

      await printService.print(printData, {
        type: 'report',
        subType: 'inventory',
        showLogo: true,
        showHeader: true,
        showFooter: true
      })

      messageApi.success('تم إرسال التقرير للطباعة')
    } catch (error) {
      Logger.error('InventoryMovements', 'خطأ في الطباعة:', error)
      messageApi.error('فشل في طباعة التقرير')
    }
  }

  // وّيفة التصدير إلى Excel
  const handleExportToExcel = () => {
    try {
      // فحص البيانات قبل التصدير
      if (!movements || movements.length === 0) {
        messageApi.warning('لا توجد حركات مخزون للتصدير')
        return
      }

      const exportData = movements.map((movement, index) => ({
        'الرقم': index + 1,
        'التاريخ': DateUtils.formatForDisplay((movement as any).movement_date || movement.created_at, DATE_FORMATS.DISPLAY_DATE),
        'نوع الحركة': getMovementTypeText(movement.movement_type),
        'الصنف': movement.item_name,
        'كود الصنف': movement.item_code,
        'المخزن': movement.warehouse_name,
        'الكمية': movement.quantity,
        'سعر الوحدة': (movement as any).unit_cost || 0,
        'القيمة الإجمالية': (movement as any).total_value || 0,
        'الرصيد قبل': (movement as any).balance_before || 0,
        'الرصيد بعد': (movement as any).balance_after || 0,
        'نوع المرجع': movement.reference_type || '-',
        'رقم المرجع': (movement as any).reference_number || '-',
        'ملاحّات': movement.notes || '-'
      }))

      const worksheet = XLSX.utils.json_to_sheet(exportData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'حركات المخزون')

      const fileName = `حركات_المخزون_${DateUtils.formatForDisplay(new Date().toISOString(), DATE_FORMATS.DISPLAY_DATE)}.xlsx`
      XLSX.writeFile(workbook, fileName)

      messageApi.success(`تم تصدير ${exportData.length} حركة مخزون بنجاح`)
    } catch (error) {
      Logger.error('InventoryMovements', 'خطأ في التصدير:', error)
      messageApi.error('حدث خطأ أثناء التصدير')
    }
  }

  // تم حذف generatePrintContent - نستخدم الآن MasterPrintService الموحد
  const _generatePrintContent_removed = () => {
    const currentDate = DateUtils.formatForDisplay(new Date().toISOString(), DATE_FORMATS.DISPLAY_DATE_TIME)

    // حساب الإحصائيات
    const totalIn = movements.filter(m => m.movement_type === 'in').reduce((sum, m) => sum + ((m as any).total_value || 0), 0)
    const totalOut = movements.filter(m => m.movement_type === 'out').reduce((sum, m) => sum + ((m as any).total_value || 0), 0)
    const totalTransfer = movements.filter(m => m.movement_type === 'transfer').length
    const totalAdjustment = movements.filter(m => m.movement_type === 'adjustment').length

    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>تقرير حركات المخزون</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 20px;
          }
          .header h1 {
            color: #1890ff;
            margin: 0;
            font-size: 24px;
          }
          .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
          }
          .stat-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #d9d9d9;
            text-align: center;
          }
          .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #1890ff;
          }
          .stat-label {
            color: #666;
            font-size: 14px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 12px;
          }
          th, td {
            border: 1px solid #d9d9d9;
            padding: 6px;
            text-align: center;
          }
          th {
            background-color: #f5f5f5;
            font-weight: bold;
          }
          .movement-in { color: #52c41a; font-weight: bold; }
          .movement-out { color: #ff4d4f; font-weight: bold; }
          .movement-transfer { color: #1890ff; font-weight: bold; }
          .movement-adjustment { color: #fa8c16; font-weight: bold; }
          .footer {
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 12px;
          }
          @media print {
            body { margin: 0; }
            .no-print { display: none; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>تقرير حركات المخزون</h1>
          <p>تاريخ الطباعة: ${currentDate}</p>
        </div>

        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value">${movements.length}</div>
            <div class="stat-label">إجمالي الحركات</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">${totalIn.toFixed(2)} ₪</div>
            <div class="stat-label">قيمة الوارد</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">${totalOut.toFixed(2)} ₪</div>
            <div class="stat-label">قيمة الصادر</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">${totalTransfer + totalAdjustment}</div>
            <div class="stat-label">نقل وتسوية</div>
          </div>
        </div>

        <table>
          <thead>
            <tr>
              <th>الرقم</th>
              <th>التاريخ</th>
              <th>النوع</th>
              <th>الصنف</th>
              <th>المخزن</th>
              <th>الكمية</th>
              <th>سعر الوحدة</th>
              <th>القيمة</th>
              <th>الرصيد قبل</th>
              <th>الرصيد بعد</th>
              <th>المرجع</th>
            </tr>
          </thead>
          <tbody>
            ${movements.map((movement, index) => `
              <tr>
                <td>${index + 1}</td>
                <td>${DateUtils.formatForDisplay((movement as any).movement_date || movement.created_at, DATE_FORMATS.DISPLAY_DATE)}</td>
                <td class="movement-${movement.movement_type}">${getMovementTypeText(movement.movement_type)}</td>
                <td>${movement.item_name}</td>
                <td>${movement.warehouse_name}</td>
                <td>${movement.quantity}</td>
                <td>${((movement as any).unit_cost || 0).toFixed(2)} ₪</td>
                <td>${((movement as any).total_value || 0).toFixed(2)} ₪</td>
                <td>${(movement as any).balance_before || 0}</td>
                <td>${(movement as any).balance_after || 0}</td>
                <td>${(movement as any).reference_number || '-'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="footer">
          <p>تم إنشاء هذا التقرير بواسطة نّام إدارة المخزون - ${currentDate}</p>
        </div>
      </body>
      </html>
    `
  }

  const columns = [
    {
      title: 'التاريخ (ميلادي)',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 140,
      render: (date: string) => (
        <div>
          <div>{DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE)}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {DateUtils.formatForDisplay(date, 'HH:mm')}
          </div>
        </div>
      )
    },
    {
      title: 'نوع الحركة',
      dataIndex: 'movement_type',
      key: 'movement_type',
      width: 120,
      render: (type: string) => (
        <MovementTypeTag 
          color={getMovementTypeColor(type)} 
          icon={getMovementTypeIcon(type)}
          movementType={type}
        >
          {getMovementTypeText(type)}
        </MovementTypeTag>
      )
    },
    {
      title: 'الصنف',
      key: 'item',
      render: (_: any, record: InventoryMovement) => (
        <Space direction="vertical" size={0}>
          <span style={{ fontWeight: 'bold' }}>{record.item_name}</span>
          <Tag color="blue" style={{ fontFamily: 'monospace' }}>
            {record.item_code}
          </Tag>
        </Space>
      )
    },
    {
      title: 'المخزن',
      dataIndex: 'warehouse_name',
      key: 'warehouse_name',
      render: (text: string) => text
    },
    {
      title: 'الكمية',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number, record: InventoryMovement) => (
        <span style={{ 
          fontWeight: 'bold',
          color: record.movement_type === 'in' ? '#52c41a' : 
                 record.movement_type === 'out' ? '#ff4d4f' : '#1890ff'
        }}>
          {record.movement_type === 'out' ? '-' : '+'}{quantity}
        </span>
      )
    },
    {
      title: 'نوع المرجع',
      dataIndex: 'reference_type',
      key: 'reference_type',
      render: (type: string) => type || '-'
    },
    {
      title: 'رقم المرجع',
      dataIndex: 'reference_id',
      key: 'reference_id',
      render: (id: number) => id || '-'
    },
    {
      title: 'المستخدم',
      dataIndex: 'created_by_name',
      key: 'created_by_name',
      render: (name: string) => name || 'النّام'
    },
    {
      title: 'ملاحّات',
      dataIndex: 'notes',
      key: 'notes',
      render: (notes: string) => notes || '-'
    }
  ]

  // إحصائيات الحركات
  const totalMovements = movements.length
  const inMovements = movements.filter(m => m.movement_type === 'in').length
  const outMovements = movements.filter(m => m.movement_type === 'out').length
  const transferMovements = movements.filter(m => m.movement_type === 'transfer').length

  return (
    <div>
      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="إجمالي الحركات"
              value={totalMovements}
              prefix={<HistoryOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </StyledCard>
        </Col>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="حركات الإدخال"
              value={inMovements}
              prefix={<ArrowUpOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </StyledCard>
        </Col>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="حركات الإخراج"
              value={outMovements}
              prefix={<ArrowDownOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </StyledCard>
        </Col>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="حركات التحويل"
              value={transferMovements}
              prefix={<SwapOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </StyledCard>
        </Col>
      </Row>

      {/* أزرار إضافة الحركات */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Button
            type="primary"
            block
            size="large"
            icon={<ArrowUpOutlined />}
            onClick={() => handleAddMovement('in')}
            style={{ background: '#52c41a', borderColor: '#52c41a' }}
          >
            إدخال مخزون
          </Button>
        </Col>
        <Col span={6}>
          <Button
            type="primary"
            block
            size="large"
            icon={<ArrowDownOutlined />}
            onClick={() => handleAddMovement('out')}
            style={{ background: '#ff4d4f', borderColor: '#ff4d4f' }}
          >
            إخراج مخزون
          </Button>
        </Col>
        <Col span={6}>
          <Button
            type="primary"
            block
            size="large"
            icon={<SwapOutlined />}
            onClick={() => handleAddMovement('transfer')}
            style={{ background: '#722ed1', borderColor: '#722ed1' }}
          >
            تحويل مخزون
          </Button>
        </Col>
        <Col span={6}>
          <Button
            type="primary"
            block
            size="large"
            icon={<InboxOutlined />}
            onClick={() => handleAddMovement('adjustment')}
            style={{ background: '#fa8c16', borderColor: '#fa8c16' }}
          >
            تسوية مخزون
          </Button>
        </Col>
      </Row>

      {/* فلاتر البحث */}
      <StyledCard title="فلاتر البحث" size="small">
        <Form layout="inline" onFinish={handleFilter}>
          <Form.Item name="item_id">
            <Select
              placeholder="اختر الصنف"
              style={{ width: 200 }}
              allowClear
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
              }
            >
              {items.map(item => (
                <Option key={item.id} value={item.id}>
                  {item.name} ({item.code})
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item name="warehouse_id">
            <Select
              placeholder="اختر المخزن"
              style={{ width: 150 }}
              allowClear
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
              }
            >
              {warehouses.map(warehouse => (
                <Option key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item name="movement_type">
            <Select placeholder="نوع الحركة" style={{ width: 120 }} allowClear>
              <Option value="in">إدخال</Option>
              <Option value="out">إخراج</Option>
              <Option value="transfer">تحويل</Option>
              <Option value="adjustment">تسوية</Option>
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              بحث
            </Button>
          </Form.Item>
          
          <Form.Item>
            <Button onClick={() => {
              setFilters({})
              loadMovements()
            }}>
              إعادة تعيين
            </Button>
          </Form.Item>
        </Form>
      </StyledCard>

      {/* جدول الحركات */}
      <StyledCard
        title={
          <Space>
            <HistoryOutlined />
            <Title level={4} style={{ margin: 0 }}>حركات المخزون</Title>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<PrinterOutlined />}
              onClick={handlePrint}
              disabled={movements.length === 0}
            >
              طباعة
            </Button>
            <Button
              icon={<FileExcelOutlined />}
              onClick={handleExportToExcel}
              disabled={movements.length === 0}
              style={{ color: '#52c41a', borderColor: '#52c41a' }}
            >
              تصدير Excel
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={movements}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            pageSize: 15,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} حركة`
          }}
        />
      </StyledCard>

      {/* نموذج إضافة حركة */}
      <Modal
        title="إضافة حركة مخزون"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item name="movement_type" hidden>
            <Input />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="item_id"
                label="الصنف"
                rules={[{ required: true, message: 'يرجى اختيار الصنف' }]}
              >
                <Select
                  placeholder="اختر الصنف"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                  }
                >
                  {items.map(item => (
                    <Option key={item.id} value={item.id}>
                      {item.name} ({item.code})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="warehouse_id"
                label="المخزن"
                rules={[{ required: true, message: 'يرجى اختيار المخزن' }]}
              >
                <Select
                  placeholder="اختر المخزن"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                  }
                >
                  {warehouses.map(warehouse => (
                    <Option key={warehouse.id} value={warehouse.id}>
                      {warehouse.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="quantity"
                label="الكمية"
                rules={[{ required: true, message: 'يرجى إدخال الكمية' }]}
              >
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                  placeholder="الكمية"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="reference_type"
                label="نوع المرجع"
              >
                <Select placeholder="اختر نوع المرجع" allowClear>
                  <Option value="purchase">فاتورة شراء</Option>
                  <Option value="sale">فاتورة بيع</Option>
                  <Option value="production">أمر إنتاج</Option>
                  <Option value="manual">يدوي</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="reference_id"
            label="رقم المرجع"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="رقم المرجع (اختياري)"
            />
          </Form.Item>

          <Form.Item
            name="notes"
            label="ملاحّات"
          >
            <Input.TextArea placeholder="ملاحّات إضافية" rows={3} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                إضافة
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default InventoryMovements
