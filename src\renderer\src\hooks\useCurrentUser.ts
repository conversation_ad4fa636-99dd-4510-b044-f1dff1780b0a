import { useState, useEffect } from 'react'
import { SafeLogger as Logger } from '../utils/logger'

interface User {
  id: number
  username: string
  full_name: string
  email?: string
  role: string
}

export const useCurrentUser = () => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        if (window.electronAPI) {
          const response = await window.electronAPI.getCurrentUserInfo()
          if (response.success && response.data) {
            setUser(response.data)
          }
        }
      } catch (error) {
        Logger.error('UseCurrentUser', 'خطأ في جلب بيانات المستخدم الحالي:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchCurrentUser()
  }, [])

  return { user, loading, userId: user?.id || 1 }
}
