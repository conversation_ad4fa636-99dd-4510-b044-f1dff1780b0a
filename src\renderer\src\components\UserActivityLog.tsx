import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Space,
  Button,
  DatePicker,
  Select,
  Tag,
  Typography,
  Tooltip,
  message,
  Modal,
  Descriptions,
  Form
} from 'antd'
import { SafeLogger as Logger } from '../utils/logger'
import { exportToExcel } from '../utils/excelExportUtils'
import {
  HistoryOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  DeleteOutlined,
  EyeOutlined,
  UserOutlined,
  ClockCircleOutlined,
  GlobalOutlined,
  DesktopOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

interface UserActivity {
  id: string
  user_id: number
  action: string
  resource?: string
  resource_id?: number
  details?: any
  ip_address?: string
  user_agent?: string
  success?: boolean
  created_at: string
  username?: string
  full_name?: string
}

interface ActivityFilters {
  userId?: number
  action?: string
  dateFrom?: string
  dateTo?: string
  limit?: number
}

interface UserActivityLogProps {
  userId?: number // إذا تم تمرير userId، سيعرض أنشطة مستخدم معين فقط
  showUserColumn?: boolean // إّهار عمود المستخدم أم لا
}

const UserActivityLog: React.FC<UserActivityLogProps> = ({ 
  userId, 
  showUserColumn = true 
}) => {
  const [activities, setActivities] = useState<UserActivity[]>([])
  const [users, setUsers] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedActivity, setSelectedActivity] = useState<UserActivity | null>(null)
  const [detailsModalVisible, setDetailsModalVisible] = useState(false)
  const [filters, setFilters] = useState<ActivityFilters>({
    userId: userId,
    limit: 100
  })
  const [form] = Form.useForm()

  // تحميل البيانات عند بدء التشغيل
  useEffect(() => {
    loadActivities()
    if (showUserColumn) {
      loadUsers()
    }
  }, [userId, showUserColumn])

  // تحديث الفلاتر عند تغيير userId
  useEffect(() => {
    setFilters(prev => ({ ...prev, userId: userId }))
  }, [userId])

  // إعادة تحميل الأنشطة عند تغيير الفلاتر
  useEffect(() => {
    if (!userId) {
      loadActivities()
    }
  }, [filters])

  // تحميل الأنشطة
  const loadActivities = async () => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        let result
        if (userId) {
          // جلب أنشطة مستخدم معين
          result = await window.electronAPI.getUserActivities(userId)
        } else {
          // جلب جميع الأنشطة مع الفلاتر
          result = await window.electronAPI.getAllUserActivities(filters)
        }
        
        if (result.success) {
          setActivities(result.data || [])
        } else {
          message.error(result.message || 'فشل في تحميل سجل الأنشطة')
        }
      } else {
        // بيانات وهمية للاختبار
        const mockActivities: UserActivity[] = [
          {
            id: '1',
            user_id: 1,
            action: 'login_success',
            resource: 'authentication',
            details: { ip_address: '*************' },
            ip_address: '*************',
            created_at: dayjs().subtract(1, 'hour').toISOString(),
            username: 'admin',
            full_name: 'مدير النّام'
          },
          {
            id: '2',
            user_id: 1,
            action: 'create_user',
            resource: 'users',
            resource_id: 5,
            details: { username: 'new_user' },
            ip_address: '*************',
            created_at: dayjs().subtract(2, 'hours').toISOString(),
            username: 'admin',
            full_name: 'مدير النّام'
          }
        ]
        setActivities(mockActivities)
      }
    } catch (error) {
      Logger.error('UserActivityLog', 'Error loading activities:', error)
      message.error('حدث خطأ في تحميل سجل الأنشطة')
    } finally {
      setLoading(false)
    }
  }

  // تحميل المستخدمين للفلتر
  const loadUsers = async () => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.getUsers()
        if (result.success) {
          setUsers(result.data || [])
        }
      }
    } catch (error) {
      Logger.error('UserActivityLog', 'Error loading users:', error)
    }
  }

  // تطبيق الفلاتر
  const handleFilter = (values: any) => {
    const newFilters: ActivityFilters = {
      userId: values.userId,
      action: values.action,
      dateFrom: values.dateRange?.[0]?.toISOString(),
      dateTo: values.dateRange?.[1]?.toISOString(),
      limit: values.limit || 100
    }
    setFilters(newFilters)
    
    // إعادة تحميل الأنشطة مع الفلاتر الجديدة (سيتم تحديثها تلقائياً عبر useEffect)
  }

  // إعادة تعيين الفلاتر
  const handleResetFilters = () => {
    form.resetFields()
    setFilters({ userId: userId, limit: 100 })
    // سيتم إعادة التحميل تلقائياً عبر useEffect
  }

  // عرض تفاصيل النشاط
  const showActivityDetails = (activity: UserActivity) => {
    setSelectedActivity(activity)
    setDetailsModalVisible(true)
  }

  // تنّيف الأنشطة القديمة
  const handleCleanupOldActivities = () => {
    Modal.confirm({
      title: 'تنّيف سجل الأنشطة القديمة',
      content: 'هل تريد حذف الأنشطة الأقدم من 90 يوماً؟',
      onOk: async () => {
        try {
          if (window.electronAPI) {
            const result = await window.electronAPI.cleanupOldActivities(90)
            if (result.success) {
              message.success(result.message)
              loadActivities()
            } else {
              message.error(result.message)
            }
          }
        } catch {
          message.error('فشل في تنّيف سجل الأنشطة')
        }
      }
    })
  }

  // تصدير سجل الأنشطة
  const handleExportActivities = async () => {
    try {
      if (activities.length === 0) {
        message.warning('لا توجد أنشطة للتصدير')
        return
      }

      // تحضير البيانات للتصدير
      const exportData = activities.map(activity => ({
        'المستخدم': activity.username || activity.user_id,
        'النشاط': activity.action,
        'التفاصيل': activity.details || '',
        'عنوان IP': activity.ip_address || '',
        'المتصفح': activity.user_agent || '',
        'التاريخ والوقت': new Date(activity.created_at).toLocaleString('ar-EG'),
        'الحالة': activity.success ? 'نجح' : 'فشل'
      }))

      // تصدير باستخدام دالة التصدير المحسنة
      const result = await exportToExcel(exportData, {
        fileName: 'سجل_الأنشطة',
        sheetName: 'سجل الأنشطة',
        includeTimestamp: true,
        customFormatting: true
      })

      if (result.success) {
        message.success(`تم تصدير ${activities.length} نشاط بنجاح إلى ملف Excel`)
      } else {
        message.error(result.message || 'فشل في تصدير سجل الأنشطة')
      }
    } catch (error) {
      Logger.error('UserActivityLog', 'خطأ في تصدير سجل الأنشطة:', error)
      message.error('حدث خطأ أثناء تصدير سجل الأنشطة')
    }
  }

  // الحصول على لون التاغ حسب نوع النشاط
  const getActivityColor = (action: string): string => {
    const colorMap: Record<string, string> = {
      'login_success': 'green',
      'login_failed': 'red',
      'logout': 'blue',
      'create_user': 'cyan',
      'update_user': 'orange',
      'delete_user': 'red',
      'reset_password': 'purple',
      'toggle_status': 'gold'
    }
    return colorMap[action] || 'default'
  }

  // الحصول على وصف النشاط
  const getActivityDescription = (activity: UserActivity): string => {
    const descriptions: Record<string, string> = {
      'login_success': 'تسجيل دخول ناجح',
      'login_failed': 'محاولة دخول فاشلة',
      'logout': 'تسجيل خروج',
      'create_user': 'إنشاء مستخدم جديد',
      'update_user': 'تحديث بيانات مستخدم',
      'delete_user': 'حذف مستخدم',
      'reset_password': 'إعادة تعيين كلمة المرور',
      'toggle_status': 'تغيير حالة المستخدم'
    }
    return descriptions[activity.action] || activity.action
  }

  // أعمدة الجدول
  const columns: ColumnsType<UserActivity> = [
    {
      title: 'التاريخ والوقت',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date: string) => (
        <Tooltip title={dayjs(date).format('DD/MM/YYYY HH:mm:ss')}>
          <Text>{dayjs(date).format('DD/MM HH:mm')}</Text>
        </Tooltip>
      ),
      sorter: (a, b) => dayjs(a.created_at).unix() - dayjs(b.created_at).unix(),
      defaultSortOrder: 'descend'
    },
    ...(showUserColumn ? [{
      title: 'المستخدم',
      key: 'user',
      width: 150,
      render: (record: UserActivity) => (
        <Space>
          <UserOutlined />
          <div>
            <Text strong>{record.full_name || record.username}</Text>
            {record.username && record.full_name && (
              <br />
            )}
            {record.username && record.full_name && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                @{record.username}
              </Text>
            )}
          </div>
        </Space>
      )
    }] : []),
    {
      title: 'النشاط',
      dataIndex: 'action',
      key: 'action',
      width: 200,
      render: (action: string, record: UserActivity) => (
        <Space direction="vertical" size="small">
          <Tag color={getActivityColor(action)}>
            {getActivityDescription(record)}
          </Tag>
          {record.resource && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.resource}
              {record.resource_id && ` #${record.resource_id}`}
            </Text>
          )}
        </Space>
      )
    },
    {
      title: 'عنوان IP',
      dataIndex: 'ip_address',
      key: 'ip_address',
      width: 130,
      render: (ip: string) => ip ? (
        <Space>
          <GlobalOutlined />
          <Text code>{ip}</Text>
        </Space>
      ) : '-'
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 100,
      render: (record: UserActivity) => (
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => showActivityDetails(record)}
          size="small"
        >
          تفاصيل
        </Button>
      )
    }
  ]

  return (
    <div>
      {/* فلاتر البحث */}
      {!userId && (
        <Card size="small" style={{ marginBottom: 16 }}>
          <Form
            form={form}
            layout="inline"
            onFinish={handleFilter}
            style={{ gap: 8 }}
          >
            <Form.Item name="userId" label="المستخدم">
              <Select
                placeholder="اختر المستخدم"
                style={{ width: 150 }}
                allowClear
              >
                {users.map(user => (
                  <Option key={user.id} value={user.id}>
                    {user.full_name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item name="action" label="نوع النشاط">
              <Select
                placeholder="اختر النشاط"
                style={{ width: 150 }}
                allowClear
              >
                <Option value="login_success">تسجيل دخول</Option>
                <Option value="logout">تسجيل خروج</Option>
                <Option value="create_user">إنشاء مستخدم</Option>
                <Option value="update_user">تحديث مستخدم</Option>
                <Option value="delete_user">حذف مستخدم</Option>
              </Select>
            </Form.Item>

            <Form.Item name="dateRange" label="الفترة الزمنية">
              <RangePicker
                style={{ width: 250 }}
                placeholder={['من تاريخ', 'إلى تاريخ']}
              />
            </Form.Item>

            <Form.Item name="limit" label="عدد السجلات">
              <Select defaultValue={100} style={{ width: 100 }}>
                <Option value={50}>50</Option>
                <Option value={100}>100</Option>
                <Option value={200}>200</Option>
                <Option value={500}>500</Option>
              </Select>
            </Form.Item>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  htmlType="submit"
                >
                  بحث
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleResetFilters}
                >
                  إعادة تعيين
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      )}

      {/* جدول الأنشطة */}
      <Card
        title={
          <Space>
            <HistoryOutlined />
            <Title level={4} style={{ margin: 0 }}>
              {userId ? 'سجل أنشطة المستخدم' : 'سجل أنشطة النّام'}
            </Title>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ExportOutlined />}
              onClick={handleExportActivities}
            >
              تصدير
            </Button>
            {!userId && (
              <Button
                icon={<DeleteOutlined />}
                onClick={handleCleanupOldActivities}
                danger
              >
                تنّيف القديم
              </Button>
            )}
            <Button
              icon={<ReloadOutlined />}
              onClick={loadActivities}
              loading={loading}
            >
              تحديث
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={activities}
          rowKey="id"
          loading={loading}
          scroll={{ x: 800 }}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} نشاط`
          }}
        />
      </Card>

      {/* نافذة تفاصيل النشاط */}
      <Modal
        title="تفاصيل النشاط"
        open={detailsModalVisible}
        onCancel={() => setDetailsModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailsModalVisible(false)}>
            إغلاق
          </Button>
        ]}
        width={600}
      >
        {selectedActivity && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="المستخدم">
              <Space>
                <UserOutlined />
                {selectedActivity.full_name || selectedActivity.username}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="النشاط">
              <Tag color={getActivityColor(selectedActivity.action)}>
                {getActivityDescription(selectedActivity)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="المورد">
              {selectedActivity.resource || '-'}
              {selectedActivity.resource_id && ` #${selectedActivity.resource_id}`}
            </Descriptions.Item>
            <Descriptions.Item label="التاريخ والوقت">
              <Space>
                <ClockCircleOutlined />
                {dayjs(selectedActivity.created_at).format('DD/MM/YYYY HH:mm:ss')}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="عنوان IP">
              <Space>
                <GlobalOutlined />
                <Text code>{selectedActivity.ip_address || '-'}</Text>
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="متصفح المستخدم">
              <Space>
                <DesktopOutlined />
                <Text style={{ fontSize: '12px' }}>
                  {selectedActivity.user_agent || '-'}
                </Text>
              </Space>
            </Descriptions.Item>
            {selectedActivity.details && Object.keys(selectedActivity.details).length > 0 && (
              <Descriptions.Item label="تفاصيل إضافية">
                <pre style={{ fontSize: '12px', background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                  {JSON.stringify(selectedActivity.details, null, 2)}
                </pre>
              </Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>
    </div>
  )
}

export default UserActivityLog
