const fs = require('fs');
const path = require('path');
const initSqlJs = require('sql.js');

async function createTestDataForImagesSafe() {
  try {
    console.log('🧪 إنشاء بيانات تجريبية آمنة لاختبار نظام الصور...\n');
    
    const dbPath = path.join(process.env.APPDATA, 'accounting-production-app', 'accounting.db');
    
    if (!fs.existsSync(dbPath)) {
      console.log('❌ قاعدة البيانات غير موجودة:', dbPath);
      return;
    }
    
    const SQL = await initSqlJs({
      locateFile: file => path.join(__dirname, 'node_modules', 'sql.js', 'dist', file)
    });
    
    const filebuffer = fs.readFileSync(dbPath);
    const db = new SQL.Database(filebuffer);
    
    // فحص هيكل الجداول
    console.log('🔍 فحص هيكل الجداول...');
    
    let productionOrdersColumns = [];
    let itemsColumns = [];
    let hasProductsTable = false;
    let hasCategoriesTable = false;
    
    try {
      const productionTableCheck = db.exec("PRAGMA table_info(production_orders)");
      productionOrdersColumns = productionTableCheck[0]?.values?.map(row => row[1]) || [];
      console.log(`   📋 production_orders: ${productionOrdersColumns.join(', ')}`);
    } catch (error) {
      console.log('   ❌ جدول production_orders غير موجود');
      return;
    }
    
    try {
      const itemsTableCheck = db.exec("PRAGMA table_info(items)");
      itemsColumns = itemsTableCheck[0]?.values?.map(row => row[1]) || [];
      console.log(`   📋 items: ${itemsColumns.join(', ')}`);
    } catch (error) {
      console.log('   ❌ جدول items غير موجود');
      return;
    }
    
    // فحص وجود جداول المراجع
    try {
      const productsCheck = db.exec("SELECT name FROM sqlite_master WHERE type='table' AND name='products'");
      hasProductsTable = productsCheck[0]?.values?.length > 0;
      console.log(`   📋 products: ${hasProductsTable ? 'موجود' : 'غير موجود'}`);
    } catch (error) {
      console.log('   📋 products: غير موجود');
    }
    
    try {
      const categoriesCheck = db.exec("SELECT name FROM sqlite_master WHERE type='table' AND name='categories'");
      hasCategoriesTable = categoriesCheck[0]?.values?.length > 0;
      console.log(`   📋 categories: ${hasCategoriesTable ? 'موجود' : 'غير موجود'}`);
    } catch (error) {
      console.log('   📋 categories: غير موجود');
    }
    
    // الحصول على IDs صالحة
    let validProductId = null;
    let validCategoryId = null;
    
    if (hasProductsTable) {
      try {
        const productResult = db.exec("SELECT id FROM products LIMIT 1");
        validProductId = productResult[0]?.values[0]?.[0] || null;
        console.log(`   🔍 أول product_id صالح: ${validProductId}`);
      } catch (error) {
        console.log('   ⚠️ لا توجد منتجات');
      }
    }
    
    if (hasCategoriesTable) {
      try {
        const categoryResult = db.exec("SELECT id FROM categories LIMIT 1");
        validCategoryId = categoryResult[0]?.values[0]?.[0] || null;
        console.log(`   🔍 أول category_id صالح: ${validCategoryId}`);
      } catch (error) {
        console.log('   ⚠️ لا توجد فئات');
      }
    }
    
    // إنشاء منتج وفئة إذا لم تكن موجودة
    if (!validProductId && hasProductsTable) {
      try {
        db.run("INSERT INTO products (name, description, created_at) VALUES (?, ?, ?)", 
               ['منتج تجريبي', 'منتج للاختبار', new Date().toISOString()]);
        const newProductResult = db.exec("SELECT last_insert_rowid()");
        validProductId = newProductResult[0]?.values[0]?.[0];
        console.log(`   ✅ تم إنشاء منتج تجريبي بـ ID: ${validProductId}`);
      } catch (error) {
        console.log('   ⚠️ فشل إنشاء منتج تجريبي');
      }
    }
    
    if (!validCategoryId && hasCategoriesTable) {
      try {
        db.run("INSERT INTO categories (name, description, created_at) VALUES (?, ?, ?)", 
               ['فئة تجريبية', 'فئة للاختبار', new Date().toISOString()]);
        const newCategoryResult = db.exec("SELECT last_insert_rowid()");
        validCategoryId = newCategoryResult[0]?.values[0]?.[0];
        console.log(`   ✅ تم إنشاء فئة تجريبية بـ ID: ${validCategoryId}`);
      } catch (error) {
        console.log('   ⚠️ فشل إنشاء فئة تجريبية');
      }
    }
    
    // بناء SQL ديناميكي لأوامر الإنتاج
    console.log('\n📋 إنشاء أوامر إنتاج تجريبية...');
    
    const hasOrderNumber = productionOrdersColumns.includes('order_number');
    const hasProductId = productionOrdersColumns.includes('product_id');
    const hasQuantity = productionOrdersColumns.includes('quantity');
    const hasStatus = productionOrdersColumns.includes('status');
    const hasStartDate = productionOrdersColumns.includes('start_date');
    const hasCreatedAt = productionOrdersColumns.includes('created_at');
    const hasUpdatedAt = productionOrdersColumns.includes('updated_at');
    
    if (!hasOrderNumber) {
      console.log('   ❌ عمود order_number غير موجود');
      return;
    }
    
    // فحص البيانات الموجودة
    const existingOrdersResult = db.exec('SELECT COUNT(*) FROM production_orders WHERE order_number LIKE "TEST-%"');
    const existingOrdersCount = existingOrdersResult[0]?.values[0]?.[0] || 0;
    
    if (existingOrdersCount > 0) {
      console.log(`   ℹ️ يوجد ${existingOrdersCount} أوامر تجريبية مسبقاً`);
    } else {
      const testOrders = [
        { order_number: 'TEST-001', quantity: 5, status: 'pending' },
        { order_number: 'TEST-002', quantity: 10, status: 'in_progress' },
        { order_number: 'TEST-003', quantity: 3, status: 'completed' }
      ];
      
      let createdOrders = 0;
      for (const order of testOrders) {
        try {
          let columns = ['order_number'];
          let placeholders = ['?'];
          let values = [order.order_number];
          
          if (hasProductId && validProductId) {
            columns.push('product_id');
            placeholders.push('?');
            values.push(validProductId);
          }
          
          if (hasQuantity) {
            columns.push('quantity');
            placeholders.push('?');
            values.push(order.quantity);
          }
          
          if (hasStatus) {
            columns.push('status');
            placeholders.push('?');
            values.push(order.status);
          }
          
          if (hasStartDate) {
            columns.push('start_date');
            placeholders.push('?');
            values.push(new Date().toISOString().split('T')[0]);
          }
          
          if (hasCreatedAt) {
            columns.push('created_at');
            placeholders.push('?');
            values.push(new Date().toISOString());
          }
          
          if (hasUpdatedAt) {
            columns.push('updated_at');
            placeholders.push('?');
            values.push(new Date().toISOString());
          }
          
          const sql = `INSERT INTO production_orders (${columns.join(', ')}) VALUES (${placeholders.join(', ')})`;
          db.run(sql, values);
          createdOrders++;
          console.log(`   ✅ أمر إنتاج: ${order.order_number}`);
        } catch (error) {
          console.log(`   ⚠️ تخطي أمر ${order.order_number}: ${error.message}`);
        }
      }
    }
    
    // بناء SQL ديناميكي للأصناف
    console.log('\n📦 إنشاء أصناف تجريبية...');
    
    const hasCode = itemsColumns.includes('code');
    const hasName = itemsColumns.includes('name');
    const hasCategoryId = itemsColumns.includes('category_id');
    const hasUnit = itemsColumns.includes('unit');
    const hasCostPrice = itemsColumns.includes('cost_price');
    const hasSalePrice = itemsColumns.includes('sale_price');
    const hasItemQuantity = itemsColumns.includes('quantity');
    const hasIsActive = itemsColumns.includes('is_active');
    const hasItemCreatedAt = itemsColumns.includes('created_at');
    
    if (!hasCode || !hasName) {
      console.log('   ❌ أعمدة code أو name غير موجودة');
    } else {
      // فحص البيانات الموجودة
      const existingItemsResult = db.exec('SELECT COUNT(*) FROM items WHERE code LIKE "ITEM-%"');
      const existingItemsCount = existingItemsResult[0]?.values[0]?.[0] || 0;
      
      if (existingItemsCount > 0) {
        console.log(`   ℹ️ يوجد ${existingItemsCount} أصناف تجريبية مسبقاً`);
      } else {
        const testItems = [
          { code: 'ITEM-001', name: 'كرسي خشبي', unit: 'قطعة', cost_price: 100, sale_price: 150 },
          { code: 'ITEM-002', name: 'طاولة مكتب', unit: 'قطعة', cost_price: 200, sale_price: 300 },
          { code: 'ITEM-003', name: 'خزانة ملابس', unit: 'قطعة', cost_price: 500, sale_price: 750 }
        ];
        
        let createdItems = 0;
        for (const item of testItems) {
          try {
            let columns = ['code', 'name'];
            let placeholders = ['?', '?'];
            let values = [item.code, item.name];
            
            if (hasCategoryId && validCategoryId) {
              columns.push('category_id');
              placeholders.push('?');
              values.push(validCategoryId);
            }
            
            if (hasUnit) {
              columns.push('unit');
              placeholders.push('?');
              values.push(item.unit);
            }
            
            if (hasCostPrice) {
              columns.push('cost_price');
              placeholders.push('?');
              values.push(item.cost_price);
            }
            
            if (hasSalePrice) {
              columns.push('sale_price');
              placeholders.push('?');
              values.push(item.sale_price);
            }
            
            if (hasItemQuantity) {
              columns.push('quantity');
              placeholders.push('?');
              values.push(0);
            }
            
            if (hasIsActive) {
              columns.push('is_active');
              placeholders.push('?');
              values.push(1);
            }
            
            if (hasItemCreatedAt) {
              columns.push('created_at');
              placeholders.push('?');
              values.push(new Date().toISOString());
            }
            
            const sql = `INSERT INTO items (${columns.join(', ')}) VALUES (${placeholders.join(', ')})`;
            db.run(sql, values);
            createdItems++;
            console.log(`   ✅ صنف: ${item.name}`);
          } catch (error) {
            console.log(`   ⚠️ تخطي صنف ${item.name}: ${error.message}`);
          }
        }
      }
    }
    
    // إنشاء صور لأوامر الإنتاج
    console.log('\n🖼️ إنشاء صور تجريبية لأوامر الإنتاج...');
    
    try {
      const ordersResult = db.exec('SELECT id, order_number FROM production_orders WHERE order_number LIKE "TEST-%"');
      if (ordersResult[0]?.values && ordersResult[0].values.length > 0) {
        let createdOrderImages = 0;
        for (const orderRow of ordersResult[0].values) {
          const orderId = orderRow[0];
          const orderNumber = orderRow[1];
          
          // فحص الصور الموجودة
          const existingImagesResult = db.exec('SELECT COUNT(*) FROM production_order_images WHERE order_id = ?', [orderId]);
          const existingImagesCount = existingImagesResult[0]?.values[0]?.[0] || 0;
          
          if (existingImagesCount === 0) {
            const svgContent = `<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="150" fill="#4ECDC4"/><text x="100" y="75" text-anchor="middle" fill="white" font-size="16">صورة تجريبية</text><text x="100" y="100" text-anchor="middle" fill="white" font-size="12">${orderNumber}</text></svg>`;
            const dataUrl = `data:image/svg+xml;charset=utf-8;base64,${Buffer.from(svgContent, 'utf8').toString('base64')}`;
            
            try {
              db.run(`
                INSERT INTO production_order_images (
                  order_id, image_name, image_path, file_size, file_type,
                  description, category, is_primary, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                orderId,
                `صورة تجريبية - ${orderNumber}`,
                dataUrl,
                svgContent.length,
                'image/svg+xml',
                'صورة تجريبية للاختبار',
                'general',
                1,
                new Date().toISOString()
              ]);
              createdOrderImages++;
              console.log(`   ✅ صورة لأمر ${orderNumber}`);
            } catch (error) {
              console.log(`   ⚠️ تخطي صورة أمر ${orderNumber}: ${error.message}`);
            }
          } else {
            console.log(`   ℹ️ أمر ${orderNumber} له ${existingImagesCount} صورة مسبقاً`);
          }
        }
      } else {
        console.log('   ℹ️ لا توجد أوامر إنتاج تجريبية');
      }
    } catch (error) {
      console.log('   ❌ خطأ في إنشاء صور أوامر الإنتاج:', error.message);
    }
    
    // إنشاء صور للأصناف
    console.log('\n🖼️ إنشاء صور تجريبية للأصناف...');
    
    try {
      const itemsResult = db.exec('SELECT id, name FROM items WHERE code LIKE "ITEM-%"');
      if (itemsResult[0]?.values && itemsResult[0].values.length > 0) {
        let createdItemImages = 0;
        for (const itemRow of itemsResult[0].values) {
          const itemId = itemRow[0];
          const itemName = itemRow[1];
          
          // فحص الصور الموجودة
          const existingImagesResult = db.exec('SELECT COUNT(*) FROM item_images WHERE item_id = ?', [itemId]);
          const existingImagesCount = existingImagesResult[0]?.values[0]?.[0] || 0;
          
          if (existingImagesCount === 0) {
            const svgContent = `<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="150" fill="#FF6B6B"/><text x="100" y="75" text-anchor="middle" fill="white" font-size="14">صنف</text><text x="100" y="100" text-anchor="middle" fill="white" font-size="12">${itemName}</text></svg>`;
            const dataUrl = `data:image/svg+xml;charset=utf-8;base64,${Buffer.from(svgContent, 'utf8').toString('base64')}`;
            
            try {
              db.run(`
                INSERT INTO item_images (
                  item_id, image_name, image_path, file_size, file_type,
                  description, is_primary, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                itemId,
                `صورة ${itemName}`,
                dataUrl,
                svgContent.length,
                'image/svg+xml',
                'صورة تجريبية للصنف',
                1,
                new Date().toISOString()
              ]);
              createdItemImages++;
              console.log(`   ✅ صورة للصنف ${itemName}`);
            } catch (error) {
              console.log(`   ⚠️ تخطي صورة صنف ${itemName}: ${error.message}`);
            }
          } else {
            console.log(`   ℹ️ صنف ${itemName} له ${existingImagesCount} صورة مسبقاً`);
          }
        }
      } else {
        console.log('   ℹ️ لا توجد أصناف تجريبية');
      }
    } catch (error) {
      console.log('   ❌ خطأ في إنشاء صور الأصناف:', error.message);
    }
    
    // تحديث إعدادات الصور
    console.log('\n⚙️ تحديث إعدادات الصور...');
    
    const settings = [
      { key: 'max_file_size', value: '10485760', description: 'الحد الأقصى لحجم الملف (10MB)', category: 'upload' },
      { key: 'allowed_types', value: 'image/jpeg,image/png,image/gif,image/webp,image/svg+xml', description: 'أنواع الملفات المسموحة', category: 'upload' },
      { key: 'thumbnail_width', value: '150', description: 'عرض الصورة المصغرة', category: 'thumbnail' },
      { key: 'thumbnail_height', value: '150', description: 'ارتفاع الصورة المصغرة', category: 'thumbnail' },
      { key: 'image_quality', value: '85', description: 'جودة ضغط الصور', category: 'compression' },
      { key: 'storage_path', value: 'images', description: 'مسار حفظ الصور', category: 'storage' },
      { key: 'enable_watermark', value: 'false', description: 'تفعيل العلامة المائية', category: 'watermark' },
      { key: 'auto_backup', value: 'true', description: 'النسخ الاحتياطي التلقائي', category: 'backup' }
    ];
    
    let updatedSettings = 0;
    for (const setting of settings) {
      try {
        db.run(`
          INSERT OR REPLACE INTO image_settings (setting_key, setting_value, description, category, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [setting.key, setting.value, setting.description, setting.category, new Date().toISOString(), new Date().toISOString()]);
        updatedSettings++;
      } catch (error) {
        console.log(`   ⚠️ تخطي إعداد ${setting.key}: ${error.message}`);
      }
    }
    console.log(`   ✅ تم تحديث ${updatedSettings} إعداد`);
    
    // حفظ قاعدة البيانات
    console.log('\n💾 حفظ التغييرات...');
    const data = db.export();
    fs.writeFileSync(dbPath, data);
    
    // إحصائيات نهائية
    console.log('\n📊 إحصائيات البيانات التجريبية:');
    
    const tables = [
      'production_orders',
      'items',
      'production_order_images',
      'item_images',
      'unified_images',
      'image_settings'
    ];
    
    for (const table of tables) {
      try {
        const result = db.exec(`SELECT COUNT(*) FROM ${table}`);
        const count = result[0]?.values[0]?.[0] || 0;
        console.log(`   📋 ${table}: ${count} سجل`);
      } catch (error) {
        console.log(`   ❌ خطأ في قراءة ${table}: ${error.message}`);
      }
    }
    
    db.close();
    
    console.log('\n' + '='.repeat(80));
    console.log('✅ تم إنشاء البيانات التجريبية الآمنة بنجاح!');
    console.log('='.repeat(80));
    
    console.log('\n📝 الآن يمكنك:');
    console.log('   1. إعادة تشغيل التطبيق');
    console.log('   2. اختبار عرض الصور في أوامر الإنتاج');
    console.log('   3. اختبار عرض الصور في الأصناف');
    console.log('   4. اختبار طباعة الصور');
    console.log('   5. اختبار رفع صور جديدة');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات التجريبية الآمنة:', error);
  }
}

// تشغيل إنشاء البيانات التجريبية الآمنة
createTestDataForImagesSafe();
