/**
 * مكون إدارة أعمدة التقارير
 * يوفر واجهة سهلة لتخصيص أعمدة التقارير
 */

import React, { useState } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Typography, 
  Modal,
  Table,
  Switch,
  InputNumber,
  Select,
  Input,
  message,
  Tooltip,
  Tag
} from 'antd';
import {
  SettingOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  EditOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { ColumnConfig } from '../common/SimpleTableManager';

const { Text, Title } = Typography;
const { Option } = Select;

interface ReportColumnManagerProps {
  columns: ColumnConfig[];
  onColumnsChange: (columns: ColumnConfig[]) => void;
  reportTitle?: string;
}

const ReportColumnManager: React.FC<ReportColumnManagerProps> = ({
  columns,
  onColumnsChange,
  reportTitle = 'التقرير'
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [editingColumns, setEditingColumns] = useState<ColumnConfig[]>([]);

  // فتح مودال الإدارة
  const handleOpenManager = () => {
    setEditingColumns([...columns]);
    setModalVisible(true);
  };

  // حفظ التغييرات
  const handleSaveChanges = () => {
    onColumnsChange(editingColumns);
    setModalVisible(false);
    message.success('تم حفظ إعدادات الأعمدة بنجاح');
  };

  // تبديل رؤية العمود
  const toggleColumnVisibility = (index: number) => {
    const newColumns = [...editingColumns];
    newColumns[index].visible = !newColumns[index].visible;
    setEditingColumns(newColumns);
  };

  // تحديث عرض العمود
  const updateColumnWidth = (index: number, width: number) => {
    const newColumns = [...editingColumns];
    newColumns[index].width = width;
    setEditingColumns(newColumns);
  };

  // تحديث محاذاة العمود
  const updateColumnAlign = (index: number, align: 'left' | 'center' | 'right') => {
    const newColumns = [...editingColumns];
    newColumns[index].align = align;
    setEditingColumns(newColumns);
  };

  // تحديث عنوان العمود
  const updateColumnTitle = (index: number, title: string) => {
    const newColumns = [...editingColumns];
    newColumns[index].title = title;
    setEditingColumns(newColumns);
  };

  // إعادة تعيين الأعمدة
  const resetColumns = () => {
    setEditingColumns([...columns]);
    message.info('تم إعادة تعيين الأعمدة');
  };

  // أعمدة جدول إدارة الأعمدة
  const managerColumns = [
    {
      title: 'العمود',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (title: string, record: ColumnConfig, index: number) => (
        <Input
          value={title}
          onChange={(e) => updateColumnTitle(index, e.target.value)}
          size="small"
        />
      )
    },
    {
      title: 'النوع',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      )
    },
    {
      title: 'العرض',
      dataIndex: 'width',
      key: 'width',
      width: 100,
      render: (width: number, record: ColumnConfig, index: number) => (
        <InputNumber
          value={width}
          onChange={(value) => updateColumnWidth(index, value || 100)}
          size="small"
          min={50}
          max={500}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'المحاذاة',
      dataIndex: 'align',
      key: 'align',
      width: 120,
      render: (align: string, record: ColumnConfig, index: number) => (
        <Select
          value={align}
          onChange={(value) => updateColumnAlign(index, value as "left" | "right" | "center")}
          size="small"
          style={{ width: '100%' }}
        >
          <Option value="right">يمين</Option>
          <Option value="center">وسط</Option>
          <Option value="left">يسار</Option>
        </Select>
      )
    },
    {
      title: 'مرئي',
      dataIndex: 'visible',
      key: 'visible',
      width: 80,
      align: 'center' as const,
      render: (visible: boolean, record: ColumnConfig, index: number) => (
        <Tooltip title={visible ? 'إخفاء العمود' : 'إظهار العمود'}>
          <Button
            type="text"
            icon={visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
            onClick={() => toggleColumnVisibility(index)}
            style={{ color: visible ? '#52c41a' : '#ff4d4f' }}
          />
        </Tooltip>
      )
    }
  ];

  const visibleColumnsCount = columns.filter(col => col.visible).length;
  const totalColumnsCount = columns.length;

  return (
    <>
      <Card size="small" style={{ backgroundColor: '#f8f9fa', marginBottom: 16 }}>
        <Space split={<span style={{ color: '#d9d9d9' }}>|</span>}>
          <Button
            type="primary"
            icon={<SettingOutlined />}
            size="small"
            onClick={handleOpenManager}
          >
            تخصيص الأعمدة
          </Button>
          
          <Text type="secondary">
            <strong>{visibleColumnsCount}</strong> من أصل <strong>{totalColumnsCount}</strong> عمود مرئي
          </Text>
          
          <Text type="secondary">
            يمكنك تخصيص الأعمدة المعروضة وترتيبها حسب احتياجاتك
          </Text>
        </Space>
      </Card>

      <Modal
        title={
          <Space>
            <SettingOutlined />
            <span>إدارة أعمدة {reportTitle}</span>
          </Space>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={800}
        footer={
          <Space>
            <Button onClick={() => setModalVisible(false)}>
              إلغاء
            </Button>
            <Button icon={<ReloadOutlined />} onClick={resetColumns}>
              إعادة تعيين
            </Button>
            <Button type="primary" icon={<SaveOutlined />} onClick={handleSaveChanges}>
              حفظ التغييرات
            </Button>
          </Space>
        }
      >
        <div style={{ marginBottom: 16 }}>
          <Title level={5}>تخصيص أعمدة التقرير</Title>
          <Text type="secondary">
            يمكنك تعديل عنوان العمود، عرضه، محاذاته، وإظهاره أو إخفاؤه
          </Text>
        </div>

        <Table
          columns={managerColumns}
          dataSource={editingColumns}
          rowKey="key"
          pagination={false}
          size="small"
          scroll={{ y: 400 }}
          bordered
        />

        <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f0f2f5', borderRadius: 6 }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            💡 <strong>نصائح:</strong> يمكنك تعديل عرض الأعمدة لتناسب المحتوى، 
            واختيار المحاذاة المناسبة لنوع البيانات (أرقام: وسط، نصوص: يمين)
          </Text>
        </div>
      </Modal>
    </>
  );
};

export default ReportColumnManager;
