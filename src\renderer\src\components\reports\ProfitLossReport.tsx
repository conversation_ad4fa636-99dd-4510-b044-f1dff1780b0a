/**
 * تقرير الأرباح والخسائر
 * تقرير مالي شامل يعرض الإيرادات والمصروفات والأرباح
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const ProfitLossReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'profit_loss' as ReportType}
      title="تقرير الأرباح والخسائر"
      description="تقرير مالي شامل يعرض الإيرادات والمصروفات وصافي الربح خلال فترة محددة"
      showDateRange={true}
      showDepartmentFilter={true}
      showCategoryFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="profit_loss_report"
      defaultFilters={{
        sortBy: 'date',
        sortOrder: 'desc'
      }}
    />
  )
}

export default ProfitLossReport
