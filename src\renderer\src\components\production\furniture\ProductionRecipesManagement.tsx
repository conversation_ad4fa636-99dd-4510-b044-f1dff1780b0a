import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Space,
  Tag,
  Tooltip,
  Card,
  Row,
  Col,
  Statistic,
  Tabs,
  List,
  Divider,
  Popconfirm,
  Alert
} from 'antd'
import { SafeLogger as Logger } from '../../../utils/logger'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExperimentOutlined,
  ClockCircleOutlined,
  // ToolOutlined,
  InboxOutlined,
  BarcodeOutlined,
  PrinterOutlined,
  FilePdfOutlined
} from '@ant-design/icons'
import UnifiedPrintButton from '../../common/UnifiedPrintButton'

const { Option } = Select
const { TextArea } = Input


interface ProductionRecipe {
  id: number
  code: string
  name: string
  item_id: number
  item_name: string
  item_code: string
  department_id: number
  department_name: string
  description?: string
  instructions?: string
  estimated_time: number
  difficulty_level: 'easy' | 'medium' | 'hard' | 'expert'
  version: number
  is_active: boolean
  created_at: string
  materials?: RecipeMaterial[]
}

interface RecipeMaterial {
  id: number
  recipe_id: number
  material_id: number
  material_name: string
  material_code: string
  quantity: number
  unit: string
  cost_per_unit: number
  total_cost: number
  is_optional: boolean
  warehouse_id?: number  // إضافة معرف المخزن للمادة
  warehouse_name?: string  // إضافة اسم المخزن للعرض
}

interface ProductionRecipesManagementProps {
  onBack?: () => void
}

const ProductionRecipesManagement: React.FC<ProductionRecipesManagementProps> = ({ onBack: _onBack }) => {
  const [recipes, setRecipes] = useState<ProductionRecipe[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [viewModalVisible, setViewModalVisible] = useState(false)
  const [editingRecipe, setEditingRecipe] = useState<ProductionRecipe | null>(null)
  const [viewingRecipe, setViewingRecipe] = useState<ProductionRecipe | null>(null)
  const [form] = Form.useForm()
  const [items, setItems] = useState<any[]>([])
  const [departments, setDepartments] = useState<any[]>([])
  const [materials, setMaterials] = useState<any[]>([])
  const [recipeMaterials, setRecipeMaterials] = useState<RecipeMaterial[]>([])
  const [materialAvailability, setMaterialAvailability] = useState<any[]>([])

  // نموذج إنشاء منتج جديد
  const [newProductModalVisible, setNewProductModalVisible] = useState(false)
  const [newProductForm] = Form.useForm()
  const [categories, setCategories] = useState<any[]>([])
  const [warehouses, setWarehouses] = useState<any[]>([])

  // نموذج تنفيذ الإنتاج
  const [executionForm] = Form.useForm()

  // نموذج إضافة مادة جديدة - مبسط
  const [selectedWarehouseForMaterial, setSelectedWarehouseForMaterial] = useState<number | null>(null)
  const [availableMaterialsInWarehouse, setAvailableMaterialsInWarehouse] = useState<any[]>([])
  const [selectedMaterialId, setSelectedMaterialId] = useState<number | null>(null)
  const [materialQuantity, setMaterialQuantity] = useState<number>(1)

  // إحصائيات سريعة
  const [stats, setStats] = useState({
    total: 0,
    easy: 0,
    medium: 0,
    hard: 0,
    expert: 0,
    avg_time: 0
  })

  // حالة التحميل والتخزين المؤقت
  const [_dataLoaded, setDataLoaded] = useState({
    recipes: false,
    items: false,
    departments: false,
    materials: false,
    categories: false,
    warehouses: false
  })
  const [lastDataLoad, setLastDataLoad] = useState<number>(0)
  const DATA_CACHE_DURATION = 5 * 60 * 1000 // 5 دقائق

  // تحميل البيانات مع التخزين المؤقت
  const loadAllData = async (forceReload: boolean = false) => {
    const now = Date.now()
    const shouldReload = forceReload || (now - lastDataLoad) > DATA_CACHE_DURATION

    if (shouldReload) {
      setLoading(true)
      try {
        await Promise.all([
          loadRecipes(),
          loadItems(),
          loadDepartments(),
          loadMaterials(),
          loadCategories(),
          loadWarehouses()
        ])
        setLastDataLoad(now)
        setDataLoaded({
          recipes: true,
          items: true,
          departments: true,
          materials: true,
          categories: true,
          warehouses: true
        })
      } catch (error) {
        Logger.error('ProductionRecipesManagement', 'خطأ في تحميل البيانات:', error)
        message.error('فشل في تحميل بعض البيانات')
      } finally {
        setLoading(false)
      }
    }
  }

  useEffect(() => {
    loadAllData()
  }, [])

  const loadRecipes = async () => {
    setLoading(true)
    try {
      Logger.info('ProductionRecipesManagement', '🔄 جاري تحميل وصفات الإنتاج...')

      if (!window.electronAPI) {
        Logger.error('ProductionRecipesManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('ProductionRecipesManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية لوصفات الإنتاج
        const mockRecipes = [
          {
            id: 1,
            recipe_name: 'وصفة طاولة خشبية',
            item_id: 1,
            item_name: 'طاولة خشبية',
            difficulty_level: 'medium',
            estimated_time: 480, // 8 ساعات
            description: 'وصفة لصنع طاولة خشبية فاخرة',
            instructions: 'قطع الخشب، تجميع القطع، صنفرة، دهان',
            department_id: 1,
            department_name: 'قسم النجارة',
            is_active: true,
            created_at: '2024-06-20'
          },
          {
            id: 2,
            recipe_name: 'وصفة كرسي مكتب',
            item_id: 2,
            item_name: 'كرسي مكتب',
            difficulty_level: 'easy',
            estimated_time: 240, // 4 ساعات
            description: 'وصفة لصنع كرسي مكتب مريح',
            instructions: 'تجميع الهيكل، تركيب المقعد، إضافة العجلات',
            department_id: 1,
            department_name: 'قسم النجارة',
            is_active: true,
            created_at: '2024-06-18'
          },
          {
            id: 3,
            recipe_name: 'وصفة خزانة ملابس',
            item_id: 3,
            item_name: 'خزانة ملابس',
            difficulty_level: 'hard',
            estimated_time: 960, // 16 ساعة
            description: 'وصفة لصنع خزانة ملابس بأبواب منزلقة',
            instructions: 'بناء الهيكل، تركيب الأرفف، تركيب الأبواب المنزلقة',
            department_id: 1,
            department_name: 'قسم النجارة',
            is_active: true,
            created_at: '2024-06-15'
          }
        ]

        setRecipes(mockRecipes as any[])
        calculateStats(mockRecipes as any[])
        Logger.info('ProductionRecipesManagement', '✅ تم تحميل ' + mockRecipes.length + ' وصفة إنتاج وهمية')
      } else {
        const result = await window.electronAPI.getProductionRecipes()
        if (result.success) {
          setRecipes(result.data)
          calculateStats(result.data)
          Logger.info('ProductionRecipesManagement', '✅ تم تحميل وصفات الإنتاج من قاعدة البيانات')
        } else {
          message.error('فشل في تحميل وصفات الإنتاج')
        }
      }
    } catch (error) {
      message.error('خطأ في تحميل وصفات الإنتاج')
    }
    setLoading(false)
  }

  const loadItems = async () => {
    try {
      Logger.info('ProductionRecipesManagement', '🔄 جاري تحميل الأصناف...')

      if (!window.electronAPI) {
        Logger.error('ProductionRecipesManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('ProductionRecipesManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للأصناف
        const mockItems = [
          { id: 1, name: 'طاولة خشبية', code: 'ITEM001', type: 'finished_product' },
          { id: 2, name: 'كرسي مكتب', code: 'ITEM002', type: 'finished_product' },
          { id: 3, name: 'خزانة ملابس', code: 'ITEM003', type: 'finished_product' }
        ]

        setItems(mockItems)
        Logger.info('ProductionRecipesManagement', '✅ تم تحميل ' + mockItems.length + ' صنف وهمي')
      } else {
        const result: any = await window.electronAPI.getItemsByType('finished_product')
        if (result && result.success) {
          setItems(result.data)
          Logger.info('ProductionRecipesManagement', '✅ تم تحميل المنتجات النهائية من قاعدة البيانات')
        }
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحميل الأصناف:', error)
    }
  }

  const loadDepartments = async () => {
    try {
      Logger.info('ProductionRecipesManagement', '🔄 جاري تحميل أقسام الإنتاج...')

      if (!window.electronAPI) {
        Logger.error('ProductionRecipesManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('ProductionRecipesManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية لأقسام الإنتاج
        const mockDepartments = [
          { id: 1, name: 'قسم النجارة', code: 'WOOD001', manager_name: 'أحمد النجار', is_active: true },
          { id: 2, name: 'قسم التجميع', code: 'ASSM001', manager_name: 'محمد المجمع', is_active: true },
          { id: 3, name: 'قسم التشطيب', code: 'FINI001', manager_name: 'علي المشطب', is_active: true }
        ]

        setDepartments(mockDepartments)
        Logger.info('ProductionRecipesManagement', '✅ تم تحميل ' + mockDepartments.length + ' قسم إنتاج وهمي')
      } else {
        const result = await window.electronAPI.getProductionDepartments()
        if (result.success) {
          setDepartments(result.data)
          Logger.info('ProductionRecipesManagement', '✅ تم تحميل أقسام الإنتاج من قاعدة البيانات')
        }
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحميل الأقسام:', error)
    }
  }

  const loadMaterials = async () => {
    try {
      Logger.info('ProductionRecipesManagement', '🔄 جاري تحميل المواد الخام...')

      if (!window.electronAPI) {
        Logger.error('ProductionRecipesManagement', '❌ window.electronAPI غير متوفر')
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      }

      // جلب المواد الخام مع معلومات المخزون
      const response = await window.electronAPI.getItemsWithInventory()

      if (response && response.success) {
        const allItems = response.data || []
        const rawMaterials = allItems.filter((item: any) =>
          item.category_name === 'مواد خام' || item.type === 'raw_material'
        )
        setMaterials(rawMaterials)
        Logger.info('ProductionRecipesManagement', `✅ تم تحميل ${rawMaterials.length} مادة خام مع معلومات المخزون`)

        // طباعة عينة من البيانات للتشخيص
        if (rawMaterials.length > 0) {
          Logger.info('ProductionRecipesManagement', 'عينة من بيانات المواد:', JSON.stringify(rawMaterials[0], null, 2))
        }
      } else {
        // fallback للطريقة القديمة
        Logger.warn('ProductionRecipesManagement', 'فشل في تحميل المواد مع المخزون، استخدام الطريقة القديمة...')
        const fallbackResponse = await window.electronAPI.getItems()
        if (Array.isArray(fallbackResponse)) {
          const rawMaterials = fallbackResponse.filter((item: any) => item.category_name === 'مواد خام' || item.type === 'raw_material')
          setMaterials(rawMaterials)
          Logger.info('ProductionRecipesManagement', `✅ تم تحميل ${rawMaterials.length} مادة خام (الطريقة القديمة)`)
        } else if (fallbackResponse && (fallbackResponse as any).success) {
          const materials = (fallbackResponse as any).data || []
          const rawMaterials = materials.filter((item: any) => item.category_name === 'مواد خام' || item.type === 'raw_material')
          setMaterials(rawMaterials)
          Logger.info('ProductionRecipesManagement', `✅ تم تحميل ${rawMaterials.length} مادة خام (الطريقة القديمة)`)
        }
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحميل المواد:', error)
    }
  }

  // تحديث المواد المتاحة عند اختيار مخزن
  const handleWarehouseChange = (warehouseId: number) => {
    setSelectedWarehouseForMaterial(warehouseId)

    // تصفية المواد المتوفرة في هذا المخزن
    const materialsInWarehouse = materials.filter((material: any) => {
      if (material.inventory && Array.isArray(material.inventory)) {
        return material.inventory.some((inv: any) =>
          inv.warehouse_id === warehouseId && (inv.quantity || 0) > 0
        )
      }
      return false
    })

    setAvailableMaterialsInWarehouse(materialsInWarehouse)
    setSelectedMaterialId(null) // إعادة تعيين المادة المختارة
  }

  // الحصول على معلومات المخزون لمادة محددة في مخزن محدد
  const getMaterialInventoryInfo = (materialId: number, warehouseId: number) => {
    const material = materials.find((m: any) => m.id === materialId)
    if (!material || !material.inventory) return null

    return material.inventory.find((inv: any) => inv.warehouse_id === warehouseId)
  }

  const loadCategories = async () => {
    try {
      if (window.electronAPI) {
        const result: any = await window.electronAPI.getCategories()
        if (result && result.success) {
          setCategories(result.data)
          Logger.info('ProductionRecipesManagement', '✅ تم تحميل الفئات من قاعدة البيانات')
        }
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحميل الفئات:', error)
    }
  }

  const loadWarehouses = async () => {
    try {
      if (window.electronAPI) {
        const result: any = await window.electronAPI.getWarehouses()
        if (result && result.success) {
          setWarehouses(result.data)
          Logger.info('ProductionRecipesManagement', '✅ تم تحميل المخازن من قاعدة البيانات')
        }
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحميل المخازن:', error)
    }
  }

  const calculateStats = (recipesData: ProductionRecipe[]) => {
    const stats = {
      total: recipesData.length,
      easy: recipesData.filter(r => r.difficulty_level === 'easy').length,
      medium: recipesData.filter(r => r.difficulty_level === 'medium').length,
      hard: recipesData.filter(r => r.difficulty_level === 'hard').length,
      expert: recipesData.filter(r => r.difficulty_level === 'expert').length,
      avg_time: recipesData.length > 0 ? recipesData.reduce((sum, r) => sum + r.estimated_time, 0) / recipesData.length : 0
    }
    setStats(stats)
  }

  // دالة إنشاء الكود التلقائي للوصفة
  const generateCode = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generateRecipeCode()
        if (response.success && response.data) {
          form.setFieldsValue({ code: response.data.code })
          message.success('تم إنشاء الكود تلقائياً')
        } else {
          message.error(response.message || 'فشل في إنشاء الكود')
        }
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في إنشاء الكود:', error)
      message.error('فشل في إنشاء الكود')
    }
  }

  // دالة إنشاء الكود التلقائي للمنتج الجديد
  const generateProductCode = async (categoryId?: number) => {
    try {
      if (!window.electronAPI) {
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن إنشاء الكود')
        return
      }

      const response: any = await window.electronAPI.generateItemCode(categoryId)
      if (response.success && response.data) {
        setTimeout(() => {
          try {
            if (newProductForm && newProductForm.setFieldsValue) {
              newProductForm.setFieldsValue({ code: response.data.code })
            }
          } catch (error) {
            Logger.warn('ProductionRecipesManagement', 'خطأ في تحديث النموذج:', error)
          }
        }, 100)
        message.success('تم إنشاء الكود تلقائياً')
      } else {
        message.error(response.message || 'فشل في إنشاء الكود')
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في إنشاء كود المنتج:', error)
      message.error('فشل في إنشاء الكود')
    }
  }

  // دالة إنشاء منتج جديد
  const handleCreateNewProduct = async (values: any) => {
    try {
      const productData = {
        ...values,
        type: 'finished_product', // تحديد نوع الصنف كمنتج نهائي
        category_id: parseInt(String(values.category_id)), // تحويل معرف الفئة إلى رقم صحيح
        warehouse_id: parseInt(String(values.warehouse_id)), // تحويل معرف المخزن إلى رقم صحيح
        cost_price: 0, // سيتم حسابها تلقائياً من الوصفات
        sale_price: 0  // سيتم حسابها تلقائياً بناءً على التكلفة + هامش الربح
      }

      if (window.electronAPI) {
        const response: any = await window.electronAPI.createItem(productData)
        if (response.success) {
          message.success('تم إنشاء المنتج بنجاح')
          setNewProductModalVisible(false)
          newProductForm.resetFields()

          // إعادة تحميل قائمة المنتجات
          await loadItems()

          // تحديد المنتج الجديد في النموذج
          form.setFieldsValue({ item_id: response.data.id })

          return response.data
        } else {
          message.error(response.message || 'فشل في إنشاء المنتج')
        }
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في إنشاء المنتج:', error)
      message.error('فشل في إنشاء المنتج')
    }
    return null
  }

  // دالة تنفيذ الإنتاج - نافذة واحدة مدمجة
  const handleExecuteProduction = (recipe: ProductionRecipe) => {
    // إعادة تعيين النموذج
    executionForm.resetFields()

    // إعداد القيم الافتراضية
    executionForm.setFieldsValue({
      quantity: 1,
      warehouse_id: warehouses[0]?.id
    })

    Modal.confirm({
      title: (
        <div style={{ textAlign: 'center', marginBottom: '20px' }}>
          <h3 style={{ color: '#1890ff', margin: 0 }}>تنفيذ إنتاج</h3>
          <p style={{ margin: '8px 0', fontSize: '16px', fontWeight: 'bold' }}>
            {recipe.name}
          </p>
          <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>
            كود الوصفة: {recipe.code}
          </p>
        </div>
      ),
      width: 600,
      content: (
        <div>
          {/* معلومات العملية */}
          <div style={{
            background: '#f0f8ff',
            padding: '12px',
            borderRadius: '6px',
            marginBottom: '20px',
            border: '1px solid #d9d9d9'
          }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#1890ff' }}>ما سيتم تنفيذه:</h4>
            <ul style={{ margin: 0, paddingRight: '20px' }}>
              <li>خصم المواد الخام من المخزون</li>
              <li>إضافة المنتج النهائي إلى المخزون</li>
              <li>تسجيل عملية الإنتاج وحساب التكاليف</li>
              <li>إنشاء أمر إنتاج جديد</li>
            </ul>
          </div>

          {/* نموذج البيانات */}
          <Form
            form={executionForm}
            layout="vertical"
            style={{ marginTop: '16px' }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="quantity"
                  label="الكمية المطلوب إنتاجها"
                  rules={[
                    { required: true, message: 'يرجى إدخال الكمية' },
                    { type: 'number', min: 1, message: 'الكمية يجب أن تكون أكبر من صفر' }
                  ]}
                >
                  <InputNumber
                    min={1}
                    max={1000}
                    precision={0}
                    style={{ width: '100%' }}
                    placeholder="أدخل الكمية"
                    addonAfter="قطعة"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="warehouse_id"
                  label="مخزن المنتج النهائي"
                  rules={[{ required: true, message: 'يرجى اختيار المخزن' }]}
                >
                  <Select
                    placeholder="اختر المخزن"
                    showSearch
                    optionFilterProp="children"
                  >
                    {warehouses.map(warehouse => (
                      <Option key={warehouse.id} value={warehouse.id}>
                        {warehouse.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="notes"
              label="ملاحظات الإنتاج"
            >
              <TextArea
                rows={3}
                placeholder="ملاحظات إضافية حول عملية الإنتاج (اختياري)"
                maxLength={500}
                showCount
              />
            </Form.Item>
          </Form>
        </div>
      ),
      onOk: async () => {
        try {
          // التحقق من صحة البيانات
          const values = await executionForm.validateFields()

          // التحقق من توفر المواد قبل التنفيذ
          if (window.electronAPI) {
            const availabilityCheck = await window.electronAPI.checkRecipeMaterialsAvailability(
              recipe.id,
              values.quantity
            )

            if (availabilityCheck && !availabilityCheck.success) {
              message.error('فشل في التحقق من توفر المواد')
              return Promise.reject('فشل في التحقق من توفر المواد')
            }

            if (availabilityCheck && availabilityCheck.data && !availabilityCheck.data.available) {
              const insufficientMaterials = availabilityCheck.data.insufficientMaterials || []
              const materialsList = insufficientMaterials.map((m: any) =>
                `${m.material_name}: مطلوب ${m.required_quantity} ${m.unit}, متوفر ${m.available_quantity}`
              ).join('\n')

              message.error({
                content: `المواد التالية غير كافية:\n${materialsList}`,
                duration: 8
              })
              return Promise.reject('مواد غير كافية')
            }
          }

          // إعداد بيانات الإنتاج
          const productionData = {
            recipe_id: recipe.id,
            quantity: values.quantity,
            warehouse_id: values.warehouse_id,
            notes: values.notes || '',
            recipe_name: recipe.name,
            recipe_code: recipe.code
          }

          // عرض مؤشر التحميل
          const loadingMessage = message.loading('جاري تنفيذ الإنتاج...', 0)

          try {
            if (window.electronAPI) {
              const result: any = await window.electronAPI.executeProductionEnhanced(productionData)

              loadingMessage() // إخفاء مؤشر التحميل

              if (result && result.success) {
                // رسالة نجاح مفصلة
                Modal.success({
                  title: 'تم تنفيذ الإنتاج بنجاح! ✅',
                  content: (
                    <div style={{ textAlign: 'right' }}>
                      <p><strong>رقم أمر الإنتاج:</strong> {result.data.order_number}</p>
                      <p><strong>الكمية المنتجة:</strong> {values.quantity} قطعة</p>
                      <p><strong>تكلفة المواد:</strong> ₪{result.data.material_cost?.toFixed(2) || '0.00'}</p>
                      <p><strong>التكلفة الإجمالية:</strong> ₪{result.data.total_cost?.toFixed(2) || '0.00'}</p>
                      {result.data.production_time && (
                        <p><strong>وقت الإنتاج المقدر:</strong> {result.data.production_time} ساعة</p>
                      )}
                    </div>
                  ),
                  width: 500
                })

                // إعادة تحميل البيانات
                await loadRecipes()
              } else {
                message.error(result?.message || 'فشل في تنفيذ الإنتاج')
                return Promise.reject(result?.message || 'فشل في تنفيذ الإنتاج')
              }
            } else {
              loadingMessage()
              message.error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
              return Promise.reject('النظام غير متوفر')
            }
          } catch (apiError) {
            loadingMessage() // إخفاء مؤشر التحميل في حالة الخطأ
            throw apiError
          }

        } catch (error: any) {
          Logger.error('ProductionRecipesManagement', 'خطأ في تنفيذ الإنتاج:', error)

          // رسائل خطأ مفصلة
          if (error.message?.includes('insufficient')) {
            message.error('المواد الخام غير كافية لتنفيذ هذا الإنتاج')
          } else if (error.message?.includes('warehouse')) {
            message.error('خطأ في المخزن المحدد')
          } else if (error.message?.includes('recipe')) {
            message.error('خطأ في بيانات الوصفة')
          } else if (typeof error === 'string' && error !== 'فشل في التحقق من توفر المواد' && error !== 'مواد غير كافية') {
            message.error(`خطأ في تنفيذ الإنتاج: ${error}`)
          } else if (error.message && error !== 'فشل في التحقق من توفر المواد' && error !== 'مواد غير كافية') {
            message.error(`حدث خطأ: ${error.message}`)
          }

          return Promise.reject(error)
        }
      },
      okText: 'تنفيذ الإنتاج',
      cancelText: 'إلغاء',
      okButtonProps: {
        size: 'large',
        style: { minWidth: '120px' }
      },
      cancelButtonProps: {
        size: 'large'
      }
    })
  }

  // وّائف التحقق من صحة البيانات
  const validateCode = (code: string): { isValid: boolean; message?: string } => {
    if (!code || code.trim().length === 0) {
      return { isValid: false, message: 'كود الوصفة مطلوب' }
    }
    if (code.length < 3 || code.length > 20) {
      return { isValid: false, message: 'كود الوصفة يجب أن يكون بين 3 و 20 حرف' }
    }
    // السماح بالأحرف الإنجليزية (كبيرة وصغيرة) والأرقام
    if (!/^[A-Za-z0-9]+$/.test(code)) {
      return { isValid: false, message: 'كود الوصفة يجب أن يحتوي على أحرف إنجليزية وأرقام فقط' }
    }
    return { isValid: true }
  }

  const validateName = (name: string): { isValid: boolean; message?: string } => {
    if (!name || name.trim().length === 0) {
      return { isValid: false, message: 'اسم الوصفة مطلوب' }
    }
    if (name.length < 2) {
      return { isValid: false, message: 'اسم الوصفة يجب أن يكون أكثر من حرفين' }
    }
    if (name.length > 100) {
      return { isValid: false, message: 'اسم الوصفة طويل جداً (الحد الأقصى 100 حرف)' }
    }
    if (/[<>"'&]/.test(name)) {
      return { isValid: false, message: 'اسم الوصفة يحتوي على أحرف غير مسموحة' }
    }
    return { isValid: true }
  }

  const validateEstimatedTime = (time: number): { isValid: boolean; message?: string } => {
    if (!time || time <= 0) {
      return { isValid: false, message: 'يجب أن يكون الوقت المقدر أكبر من الصفر' }
    }
    if (time > 1000) {
      return { isValid: false, message: 'الوقت المقدر كبير جداً (الحد الأقصى 1000 ساعة)' }
    }
    // السماح بأي رقم عشري موجب (إزالة قيد نصف ساعة)
    if (time < 0.1) {
      return { isValid: false, message: 'الوقت المقدر يجب أن يكون على الأقل 0.1 ساعة' }
    }
    return { isValid: true }
  }

  const calculateTotalCost = (quantity: number, costPerUnit: number): number => {
    return Math.round((quantity * costPerUnit) * 100) / 100
  }

  const handleSubmit = async (values: any) => {
    try {
      // التحقق من صحة الكود
      if (!editingRecipe) {
        const codeValidation = validateCode(values.code)
        if (!codeValidation.isValid) {
          message.error(codeValidation.message)
          return
        }
      }

      // التحقق من صحة الاسم
      const nameValidation = validateName(values.name)
      if (!nameValidation.isValid) {
        message.error(nameValidation.message)
        return
      }

      // التحقق من اختيار المنتج
      if (!values.item_id) {
        message.error('يجب اختيار المنتج النهائي')
        return
      }

      // التحقق من اختيار القسم
      if (!values.department_id) {
        message.error('يجب اختيار قسم الإنتاج')
        return
      }

      // التحقق من اختيار مخزن المنتج التام
      if (!values.product_warehouse_id) {
        message.error('يجب اختيار مخزن المنتج التام')
        return
      }

      // التحقق من صحة الوقت المقدر
      const timeValidation = validateEstimatedTime(values.estimated_time)
      if (!timeValidation.isValid) {
        message.error(timeValidation.message)
        return
      }

      // التحقق من صحة مستوى الصعوبة
      const validDifficultyLevels = ['easy', 'medium', 'hard', 'expert']
      if (!validDifficultyLevels.includes(values.difficulty_level)) {
        message.error('مستوى الصعوبة غير صحيح')
        return
      }

      // التحقق من صحة رقم الإصدار
      if (!values.version || values.version < 1 || values.version > 999) {
        message.error('رقم الإصدار يجب أن يكون بين 1 و 999')
        return
      }

      // التحقق من وجود مواد في الوصفة
      if (!recipeMaterials || recipeMaterials.length === 0) {
        message.error('يجب إضافة مادة واحدة على الأقل للوصفة')
        return
      }

      // التحقق من صحة بيانات المواد باستخدام الدالة المحسنة
      for (let i = 0; i < recipeMaterials.length; i++) {
        const material = recipeMaterials[i]

        // التأكد من أن المادة لديها البيانات الأساسية
        if (!material) {
          message.error(`بيانات المادة رقم ${i + 1} مفقودة`)
          return
        }

        const validation = validateMaterialData(material, i)
        if (!validation.isValid) {
          message.error(validation.message)
          return
        }
      }

      // التحقق من تفرد الكود قبل الحفّ
      if (window.electronAPI && !editingRecipe) {
        try {
          const codeCheckResponse = await window.electronAPI.checkCodeUniqueness('production_recipes', 'code', values.code)
          if (codeCheckResponse.success && !codeCheckResponse.data.isUnique) {
            message.error('هذا الكود مستخدم بالفعل، يرجى اختيار كود آخر')
            return
          }
        } catch (error) {
          Logger.error('ProductionRecipesManagement', 'خطأ في التحقق من تفرد الكود:', error)
          message.warning('لا يمكن التحقق من تفرد الكود، سيتم المتابعة')
        }
      }

      // التحقق من توفر API
      if (!window.electronAPI) {
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن حفّ الوصفات')
        return
      }

      // التأكد من وجود item_id قبل الإرسال
      if (!values.item_id) {
        message.error('معرف المنتج مطلوب - يرجى اختيار منتج نهائي')
        return
      }

      // حساب التكلفة الإجمالية للوصفة
      const totalCost = recipeMaterials.reduce((sum, material) => {
        const materialCost = (material.cost_per_unit || 0) * (material.quantity || 0)
        return sum + materialCost
      }, 0)

      // إعداد بيانات الوصفة مع جميع الحقول المطلوبة
      const recipeData = {
        name: values.name,
        code: values.code,
        product_id: values.item_id, // توحيد استخدام product_id
        department_id: values.department_id,
        product_warehouse_id: values.product_warehouse_id,
        difficulty_level: values.difficulty_level,
        estimated_time: values.estimated_time,
        version: values.version || 1,
        description: values.description || '',
        instructions: values.instructions || '',
        notes: values.notes || '',
        total_cost: totalCost,
        is_active: values.is_active !== false, // افتراضياً true
        materials: recipeMaterials.map(material => ({
          material_id: material.material_id,
          quantity: material.quantity,
          unit: material.unit,
          cost_per_unit: material.cost_per_unit || 0,
          total_cost: material.total_cost || 0,
          is_optional: material.is_optional || false,
          warehouse_id: material.warehouse_id, // إضافة معرف المخزن
          warehouse_name: material.warehouse_name // إضافة اسم المخزن للمرجع
        }))
      }

      // تسجيل البيانات للتشخيص
      Logger.info('ProductionRecipesManagement', 'بيانات الوصفة المرسلة:', {
        ...recipeData,
        materials_count: recipeData.materials.length,
        total_cost: totalCost
      })

      // عرض مؤشر التحميل
      const loadingMessage = message.loading(
        editingRecipe ? 'جاري تحديث الوصفة...' : 'جاري إنشاء الوصفة...',
        0
      )

      let result: any
      try {
        if (editingRecipe) {
          result = await window.electronAPI.updateProductionRecipe(editingRecipe.id, recipeData)
        } else {
          result = await window.electronAPI.createProductionRecipe(recipeData)
        }
      } finally {
        loadingMessage() // إخفاء مؤشر التحميل
      }

      if (result && result.success) {
        const successMessage = editingRecipe
          ? `تم تحديث الوصفة "${values.name}" بنجاح`
          : `تم إنشاء الوصفة "${values.name}" بنجاح`

        message.success(successMessage)

        // إغلاق النافذة وإعادة تعيين البيانات
        setModalVisible(false)
        setEditingRecipe(null)
        setRecipeMaterials([])
        form.resetFields()

        // إعادة تحميل البيانات
        await loadRecipes()

        // تسجيل العملية الناجحة
        Logger.info('ProductionRecipesManagement', `✅ ${editingRecipe ? 'تحديث' : 'إنشاء'} الوصفة بنجاح:`, {
          recipe_id: result.data?.id,
          recipe_name: values.name,
          recipe_code: values.code,
          materials_count: recipeData.materials.length,
          total_cost: totalCost
        })

      } else {
        const errorMessage = result?.message || 'فشل في حفّ الوصفة'
        message.error(errorMessage)
        Logger.error('ProductionRecipesManagement', 'فشل في حفّ الوصفة:', result)
      }
    } catch (error: any) {
      Logger.error('ProductionRecipesManagement', 'خطأ في حفّ الوصفة:', error)

      // رسائل خطأ مفصلة حسب نوع الخطأ
      if (error.message?.includes('UNIQUE constraint')) {
        if (error.message.includes('code')) {
          message.error(`كود الوصفة "${values.code}" موجود مسبقاً، يرجى اختيار كود آخر`)
        } else if (error.message.includes('name')) {
          message.error(`اسم الوصفة "${values.name}" موجود مسبقاً، يرجى اختيار اسم آخر`)
        } else {
          message.error('بيانات الوصفة موجودة مسبقاً، يرجى التحقق من الكود والاسم')
        }
      } else if (error.message?.includes('FOREIGN KEY')) {
        message.error('خطأ في ربط البيانات - تحقق من المنتج والقسم والمواد المحددة')
      } else if (error.message?.includes('NOT NULL')) {
        message.error('بعض البيانات المطلوبة مفقودة - تحقق من جميع الحقول الإجبارية')
      } else if (error.message?.includes('timeout')) {
        message.error('انتهت مهلة الاتصال - يرجى المحاولة مرة أخرى')
      } else if (error.message?.includes('network') || error.message?.includes('connection')) {
        message.error('خطأ في الاتصال - تحقق من الشبكة وحاول مرة أخرى')
      } else if (error.message?.includes('materials')) {
        message.error('خطأ في بيانات المواد - تحقق من جميع المواد المضافة')
      } else {
        message.error(`خطأ في حفّ الوصفة: ${error.message || 'خطأ غير معروف'}`)
      }
    }
  }

  const handleEdit = async (recipe: ProductionRecipe) => {
    try {
      setEditingRecipe(recipe)

      // تحميل مواد الوصفة أولاً
      await loadRecipeMaterials(recipe.id)

      // ثم تعيين قيم النموذج
      form.setFieldsValue(recipe)

      setModalVisible(true)
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحميل بيانات التعديل:', error)
      message.error('فشل في تحميل بيانات الوصفة للتعديل')
    }
  }

  const handleView = async (recipe: ProductionRecipe) => {
    setViewingRecipe(recipe)
    await loadRecipeMaterials(recipe.id)
    setViewModalVisible(true)
  }

  const handleDelete = async (recipeId: number) => {
    try {
      if (!window.electronAPI) {
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن حذف الوصفات')
        return
      }

      const result = await window.electronAPI.deleteProductionRecipe(recipeId)
      if (result.success) {
        message.success('تم حذف الوصفة بنجاح')
        loadRecipes()
      } else {
        message.error(result.message || 'فشل في حذف الوصفة')
      }
    } catch (error) {
      message.error('خطأ في حذف الوصفة')
    }
  }

  const loadRecipeMaterials = async (recipeId: number) => {
    try {
      const result = await window.electronAPI.getRecipeMaterials(recipeId)
      if (result.success) {
        setRecipeMaterials(result.data)
        // التحقق من توفر المواد في المخزون
        await checkMaterialsAvailability(recipeId)
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحميل مواد الوصفة:', error)
    }
  }

  const checkMaterialsAvailability = async (recipeId: number, productionQuantity: number = 1) => {
    try {
      const result = await window.electronAPI.checkRecipeMaterialsAvailability(recipeId, productionQuantity)
      if (result.success) {
        setMaterialAvailability(result.data.materials || [])
        return result.data
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في التحقق من توفر المواد:', error)
    }
    return null
  }

  // إضافة مادة جديدة - مبسط
  const addNewMaterial = () => {
    if (!selectedWarehouseForMaterial) {
      message.warning('يرجى اختيار المخزن أولاً')
      return
    }

    if (!selectedMaterialId) {
      message.warning('يرجى اختيار المادة')
      return
    }

    if (materialQuantity <= 0) {
      message.warning('يرجى إدخال كمية صحيحة')
      return
    }

    // البحث عن المادة المختارة
    const selectedMaterial = materials.find(m => m.id === selectedMaterialId)
    if (!selectedMaterial) {
      message.error('المادة المختارة غير موجودة')
      return
    }

    // التحقق من عدم تكرار المادة
    const existingMaterial = recipeMaterials.find(m => m.material_id === selectedMaterialId)
    if (existingMaterial) {
      message.warning('هذه المادة مضافة مسبقاً')
      return
    }



    const newMaterial: RecipeMaterial = {
      id: Date.now(),
      recipe_id: 0,
      material_id: selectedMaterialId,
      material_name: selectedMaterial.name || '',
      material_code: selectedMaterial.code || '',
      quantity: materialQuantity,
      unit: selectedMaterial.unit || 'قطعة',
      cost_per_unit: selectedMaterial.cost_price || selectedMaterial.sale_price || 0,
      total_cost: materialQuantity * (selectedMaterial.cost_price || selectedMaterial.sale_price || 0),
      is_optional: false,
      warehouse_id: selectedWarehouseForMaterial,
      warehouse_name: warehouses.find(w => w.id === selectedWarehouseForMaterial)?.name || ''
    }

    setRecipeMaterials([...recipeMaterials, newMaterial])

    // إعادة تعيين النموذج
    setSelectedMaterialId(null)
    setMaterialQuantity(1)

    message.success('تم إضافة المادة بنجاح')
  }

  const validateMaterialData = (material: RecipeMaterial, index: number): { isValid: boolean; message?: string; errorCode?: string } => {
    const position = `العنصر رقم ${index + 1}`

    // التحقق من اختيار المادة
    if (!material.material_id) {
      return {
        isValid: false,
        message: `يجب اختيار المادة للـ${position}`,
        errorCode: 'MAT_001'
      }
    }

    // التحقق من صحة الكمية
    if (!material.quantity || material.quantity <= 0) {
      return {
        isValid: false,
        message: `كمية المادة يجب أن تكون أكبر من الصفر للـ${position}`,
        errorCode: 'MAT_002'
      }
    }

    if (material.quantity > 10000) {
      return {
        isValid: false,
        message: `كمية المادة كبيرة جداً للـ${position} (الحد الأقصى 10,000)`,
        errorCode: 'MAT_002'
      }
    }

    // التحقق من وحدة القياس
    if (!material.unit || material.unit.trim().length === 0) {
      return {
        isValid: false,
        message: `وحدة القياس مطلوبة للـ${position}`,
        errorCode: 'MAT_003'
      }
    }

    // التحقق من صحة وحدة القياس
    const validUnits = ['قطعة', 'كيلو', 'جرام', 'لتر', 'متر', 'متر مربع', 'متر مكعب', 'طن', 'علبة', 'كرتونة']
    if (!validUnits.includes(material.unit)) {
      return {
        isValid: false,
        message: `وحدة القياس غير صحيحة للـ${position}. الوحدات المسموحة: ${validUnits.join(', ')}`,
        errorCode: 'MAT_003'
      }
    }

    // التحقق من التكلفة
    if (material.cost_per_unit < 0) {
      return {
        isValid: false,
        message: `سعر الوحدة لا يمكن أن يكون سالباً للـ${position}`,
        errorCode: 'MAT_004'
      }
    }

    if (material.cost_per_unit > 100000) {
      return {
        isValid: false,
        message: `سعر الوحدة مرتفع جداً للـ${position} (الحد الأقصى 100,000)`,
        errorCode: 'MAT_004'
      }
    }

    // التحقق من تكرار المواد
    const duplicateIndex = recipeMaterials.findIndex((m, i) =>
      i !== index && m.material_id === material.material_id
    )

    if (duplicateIndex !== -1) {
      return {
        isValid: false,
        message: `المادة مضافة مسبقاً في العنصر رقم ${duplicateIndex + 1}`,
        errorCode: 'MAT_005'
      }
    }

    return { isValid: true }
  }

  const updateMaterial = (_index: number, field: string, value: any) => {
    try {
      const updatedMaterials = [...recipeMaterials]
      updatedMaterials[_index] = { ...updatedMaterials[_index], [field]: value }

      // إذا تم تغيير المادة، تحديث البيانات المرتبطة
      if (field === 'material_id') {
        if (value && value > 0) {
          const selectedMaterial = materials.find(m => m.id === value)
          if (selectedMaterial) {
            updatedMaterials[_index].material_name = selectedMaterial.name || ''
            updatedMaterials[_index].material_code = selectedMaterial.code || ''
            updatedMaterials[_index].unit = selectedMaterial.unit || 'قطعة'
            // استخدام سعر التكلفة إذا كان متوفراً، وإلا استخدام سعر البيع
            updatedMaterials[_index].cost_per_unit = selectedMaterial.cost_price || selectedMaterial.sale_price || 0

            // إذا لم يكن هناك مخزن محدد للمادة، استخدم المخزن المختار افتراضياً
            if (!updatedMaterials[_index].warehouse_id && selectedWarehouseForMaterial) {
              updatedMaterials[_index].warehouse_id = selectedWarehouseForMaterial
              updatedMaterials[_index].warehouse_name = warehouses.find(w => w.id === selectedWarehouseForMaterial)?.name || ''
            }
          } else {
            // إذا لم يتم العثور على المادة، إعادة تعيين البيانات
            updatedMaterials[_index].material_name = ''
            updatedMaterials[_index].material_code = ''
            updatedMaterials[_index].unit = 'قطعة'
            updatedMaterials[_index].cost_per_unit = 0
          }
        } else {
          // إذا لم يتم اختيار مادة، إعادة تعيين البيانات
          updatedMaterials[_index].material_name = ''
          updatedMaterials[_index].material_code = ''
          updatedMaterials[_index].unit = 'قطعة'
          updatedMaterials[_index].cost_per_unit = 0
        }
      }

      // التحقق من صحة القيم المدخلة
      if (field === 'quantity' && value !== null && value !== undefined) {
        if (value < 0) {
          message.warning('الكمية لا يمكن أن تكون سالبة')
          return
        }
        if (value > 10000) {
          message.warning('الكمية كبيرة جداً')
          return
        }
      }

      if (field === 'cost_per_unit' && value !== null && value !== undefined) {
        if (value < 0) {
          message.warning('سعر الوحدة لا يمكن أن يكون سالباً')
          return
        }
        if (value > 100000) {
          message.warning('سعر الوحدة مرتفع جداً')
          return
        }
      }

      // حساب التكلفة الإجمالية مع التقريب الصحيح
      if (field === 'quantity' || field === 'cost_per_unit' || field === 'material_id') {
        const quantity = updatedMaterials[_index].quantity || 0
        const costPerUnit = updatedMaterials[_index].cost_per_unit || 0
        updatedMaterials[_index].total_cost = calculateTotalCost(quantity, costPerUnit)
      }

      setRecipeMaterials(updatedMaterials)
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحديث المادة:', error)
      message.error('حدث خطأ في تحديث بيانات المادة')
    }
  }

  const removeMaterial = (_index: number) => {
    const updatedMaterials = recipeMaterials.filter((_, i) => i !== _index)
    setRecipeMaterials(updatedMaterials)
  }

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'easy': return 'green'
      case 'medium': return 'blue'
      case 'hard': return 'orange'
      case 'expert': return 'red'
      default: return 'default'
    }
  }

  const getDifficultyText = (level: string) => {
    switch (level) {
      case 'easy': return 'سهل'
      case 'medium': return 'متوسط'
      case 'hard': return 'صعب'
      case 'expert': return 'خبير'
      default: return level
    }
  }

  // دالة تحضير بيانات الطباعة المفصلة للوصفة (محسنة ومطورة)
  const prepareDetailedRecipePrintData = async (recipe: ProductionRecipe) => {
    try {
      // تحميل مواد الوصفة مع معلومات المخزون
      const materialsResult = await window.electronAPI?.getRecipeMaterials(recipe.id)
      const materials = materialsResult?.success ? materialsResult.data : []

      // تحميل معلومات توفر المواد
      const availabilityResult = await window.electronAPI?.checkRecipeMaterialsAvailability(recipe.id, 1)
      const availability = availabilityResult?.success ? availabilityResult.data : null

      // حساب التكاليف والإحصائيات
      const totalCost = materials.reduce((sum: number, m: any) => sum + (m.total_cost || 0), 0)
      const totalMaterials = materials.length
      const optionalMaterials = materials.filter((m: any) => m.is_optional).length
      const requiredMaterials = totalMaterials - optionalMaterials

      // تحضير بيانات المواد مع معلومات التوفر
      const materialsData = materials.map((material: any, index: number) => {
        const materialAvailability = availability?.materials?.find((m: any) => m.material_id === material.material_id)

        return {
          id: index + 1,
          material_code: material.material_code || '',
          material_name: material.material_name || 'غير محدد',
          warehouse_name: material.warehouse_name || 'غير محدد',
          quantity: material.quantity || 0,
          unit: material.unit || 'قطعة',
          cost_per_unit: material.cost_per_unit || 0,
          total_cost: material.total_cost || 0,
          is_optional: material.is_optional ? 'اختياري' : 'إجباري',
          available_quantity: materialAvailability?.available_quantity || 0,
          is_sufficient: materialAvailability?.is_sufficient ? 'متوفر' : 'غير كافي',
          shortage: materialAvailability?.shortage || 0,
          status_color: materialAvailability?.is_sufficient ? '#52c41a' : '#ff4d4f'
        }
      })

      // تحضير معلومات الوصفة الأساسية
      const recipeInfo = {
        code: recipe.code,
        name: recipe.name,
        item_name: recipe.item_name || 'غير محدد',
        item_code: recipe.item_code || '',
        department_name: recipe.department_name || 'غير محدد',
        estimated_time: recipe.estimated_time || 0,
        difficulty_level: getDifficultyText(recipe.difficulty_level),
        version: recipe.version || 1,
        is_active: recipe.is_active ? 'نشطة' : 'غير نشطة',
        description: recipe.description || 'لا يوجد وصف',
        instructions: recipe.instructions || 'لا توجد تعليمات',
        created_at: recipe.created_at ? new Date(recipe.created_at).toLocaleDateString('ar-SA') : 'غير محدد'
      }

      return {
        title: `وصفة إنتاج مفصلة: ${recipe.name}`,
        subtitle: `الكود: ${recipe.code} | المنتج: ${recipe.item_name} | التكلفة: ${totalCost.toFixed(2)} ج.م | المواد: ${totalMaterials}`,

        // بيانات الوصفة الأساسية
        data: [recipeInfo],

        // بيانات المواد المفصلة
        materials: materialsData,

        // أعمدة جدول الوصفة
        columns: [
          { key: 'code', title: 'كود الوصفة', align: 'center' as const, width: '12%' },
          { key: 'name', title: 'اسم الوصفة', align: 'right' as const, width: '20%' },
          { key: 'item_name', title: 'المنتج النهائي', align: 'right' as const, width: '18%' },
          { key: 'department_name', title: 'القسم', align: 'center' as const, width: '12%' },
          { key: 'estimated_time', title: 'الوقت (ساعة)', align: 'center' as const, format: 'number' as const, width: '10%' },
          { key: 'difficulty_level', title: 'الصعوبة', align: 'center' as const, width: '10%' },
          { key: 'version', title: 'الإصدار', align: 'center' as const, width: '8%' },
          { key: 'is_active', title: 'الحالة', align: 'center' as const, width: '10%' }
        ],

        // أعمدة جدول المواد
        materialsColumns: [
          { key: 'id', title: '#', align: 'center' as const, width: '5%' },
          { key: 'material_code', title: 'كود المادة', align: 'center' as const, width: '12%' },
          { key: 'material_name', title: 'اسم المادة', align: 'right' as const, width: '20%' },
          { key: 'warehouse_name', title: 'المخزن', align: 'center' as const, width: '12%' },
          { key: 'quantity', title: 'الكمية', align: 'center' as const, format: 'number' as const, width: '8%' },
          { key: 'unit', title: 'الوحدة', align: 'center' as const, width: '8%' },
          { key: 'cost_per_unit', title: 'سعر الوحدة', align: 'center' as const, format: 'currency' as const, width: '10%' },
          { key: 'total_cost', title: 'التكلفة الإجمالية', align: 'center' as const, format: 'currency' as const, width: '12%' },
          { key: 'is_optional', title: 'النوع', align: 'center' as const, width: '8%' },
          { key: 'is_sufficient', title: 'التوفر', align: 'center' as const, width: '8%' }
        ],

        // ملخص شامل ومفصل
        summary: {
          compact: false,
          showTotals: true,
          data: [
            { label: 'إجمالي المواد', value: totalMaterials, format: 'number', icon: '📦' },
            { label: 'المواد الإجبارية', value: requiredMaterials, format: 'number', icon: '⚡' },
            { label: 'المواد الاختيارية', value: optionalMaterials, format: 'number', icon: '🔧' },
            { label: 'التكلفة الإجمالية', value: totalCost, format: 'currency', icon: '💰' },
            { label: 'متوسط تكلفة المادة', value: totalMaterials > 0 ? totalCost / totalMaterials : 0, format: 'currency', icon: '📊' },
            { label: 'الوقت المقدر', value: recipe.estimated_time || 0, format: 'number', unit: 'ساعة', icon: '⏱️' },
            { label: 'مستوى الصعوبة', value: getDifficultyText(recipe.difficulty_level), icon: '🎯' },
            { label: 'القسم المسؤول', value: recipe.department_name || 'غير محدد', icon: '🏭' },
            { label: 'إصدار الوصفة', value: `v${recipe.version || 1}`, icon: '📋' },
            { label: 'حالة الوصفة', value: recipe.is_active ? 'نشطة ✅' : 'غير نشطة ❌', icon: '🔄' },
            { label: 'تاريخ الإنشاء', value: recipe.created_at ? new Date(recipe.created_at).toLocaleDateString('ar-SA') : 'غير محدد', icon: '📅' },
            { label: 'حالة المواد', value: availability?.available ? 'جميع المواد متوفرة ✅' : 'بعض المواد غير متوفرة ⚠️', icon: '📋' }
          ]
        },

        // معلومات إضافية للتقرير
        additionalInfo: {
          description: recipe.description,
          instructions: recipe.instructions,
          notes: `هذه الوصفة تحتوي على ${totalMaterials} مادة (${requiredMaterials} إجبارية، ${optionalMaterials} اختيارية) بتكلفة إجمالية ${totalCost.toFixed(2)} ج.م ووقت إنتاج مقدر ${recipe.estimated_time || 0} ساعة.`,
          warnings: availability && !availability.available ?
            `تحذير: بعض المواد غير متوفرة بالكمية المطلوبة. يرجى مراجعة المخزون قبل بدء الإنتاج.` : null
        },

        // بيانات وصفية محسنة
        metadata: {
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام إدارة الإنتاج - ZET.IA',
          recipeCode: recipe.code,
          recipeName: recipe.name,
          documentType: 'وصفة إنتاج مفصلة مع تحليل المواد والتكاليف',
          documentVersion: '2.0',
          totalMaterials: totalMaterials,
          totalCost: totalCost,
          estimatedTime: recipe.estimated_time || 0,
          difficultyLevel: recipe.difficulty_level,
          department: recipe.department_name,
          isActive: recipe.is_active,
          materialsAvailable: availability?.available || false,
          reportId: `RECIPE_${recipe.code}_${Date.now()}`,
          printSettings: {
            orientation: 'portrait',
            pageSize: 'A4',
            showLogo: true,
            showHeader: true,
            showFooter: true,
            showSignature: true,
            primaryColor: '#722ed1',
            secondaryColor: '#f0f2f5'
          }
        }
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحضير بيانات الطباعة المفصلة:', error)
      throw new Error(`فشل في تحضير بيانات الطباعة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    }
  }

  // دالة تحضير بيانات طباعة التقرير الشامل لجميع الوصفات
  const prepareComprehensiveRecipesReport = async () => {
    try {
      // حساب الإحصائيات الشاملة
      const totalRecipes = recipes.length
      const activeRecipes = recipes.filter(r => r.is_active).length
      const inactiveRecipes = totalRecipes - activeRecipes

      // إحصائيات مستوى الصعوبة
      const difficultyStats = {
        easy: recipes.filter(r => r.difficulty_level === 'easy').length,
        medium: recipes.filter(r => r.difficulty_level === 'medium').length,
        hard: recipes.filter(r => r.difficulty_level === 'hard').length,
        expert: recipes.filter(r => r.difficulty_level === 'expert').length
      }

      // إحصائيات الأقسام
      const departmentStats = departments.reduce((acc: any, dept) => {
        acc[dept.name] = recipes.filter(r => r.department_id === dept.id).length
        return acc
      }, {})

      // حساب متوسط الأوقات
      const totalTime = recipes.reduce((sum, r) => sum + (r.estimated_time || 0), 0)
      const averageTime = totalRecipes > 0 ? totalTime / totalRecipes : 0

      // تحضير بيانات الوصفات للطباعة
      const recipesData = recipes.map((recipe, index) => ({
        id: index + 1,
        code: recipe.code,
        name: recipe.name,
        item_name: recipe.item_name || 'غير محدد',
        item_code: recipe.item_code || '',
        department_name: recipe.department_name || 'غير محدد',
        estimated_time: recipe.estimated_time || 0,
        difficulty_level: getDifficultyText(recipe.difficulty_level),
        version: recipe.version || 1,
        is_active: recipe.is_active ? 'نشطة' : 'غير نشطة',
        created_at: recipe.created_at ? new Date(recipe.created_at).toLocaleDateString('ar-SA') : 'غير محدد',
        status_icon: recipe.is_active ? '✅' : '❌',
        difficulty_icon: recipe.difficulty_level === 'easy' ? '🟢' :
                        recipe.difficulty_level === 'medium' ? '🟡' :
                        recipe.difficulty_level === 'hard' ? '🟠' : '🔴'
      }))

      return {
        title: 'تقرير شامل - وصفات الإنتاج',
        subtitle: `إجمالي الوصفات: ${totalRecipes} | النشطة: ${activeRecipes} | غير النشطة: ${inactiveRecipes} | تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}`,

        data: recipesData,

        columns: [
          { key: 'id', title: '#', align: 'center' as const, width: '5%' },
          { key: 'code', title: 'كود الوصفة', align: 'center' as const, width: '12%' },
          { key: 'name', title: 'اسم الوصفة', align: 'right' as const, width: '20%' },
          { key: 'item_name', title: 'المنتج النهائي', align: 'right' as const, width: '18%' },
          { key: 'department_name', title: 'القسم', align: 'center' as const, width: '12%' },
          { key: 'estimated_time', title: 'الوقت (ساعة)', align: 'center' as const, format: 'number' as const, width: '10%' },
          { key: 'difficulty_level', title: 'الصعوبة', align: 'center' as const, width: '10%' },
          { key: 'version', title: 'الإصدار', align: 'center' as const, width: '8%' },
          { key: 'is_active', title: 'الحالة', align: 'center' as const, width: '8%' },
          { key: 'created_at', title: 'تاريخ الإنشاء', align: 'center' as const, width: '12%' }
        ],

        summary: {
          compact: false,
          showTotals: true,
          showCharts: true,
          data: [
            { label: 'إجمالي الوصفات', value: totalRecipes, format: 'number', icon: '📋', color: '#1890ff' },
            { label: 'الوصفات النشطة', value: activeRecipes, format: 'number', icon: '✅', color: '#52c41a' },
            { label: 'الوصفات غير النشطة', value: inactiveRecipes, format: 'number', icon: '❌', color: '#ff4d4f' },
            { label: 'متوسط وقت الإنتاج', value: averageTime, format: 'number', unit: 'ساعة', icon: '⏱️', color: '#722ed1' },
            { label: 'إجمالي ساعات الإنتاج', value: totalTime, format: 'number', unit: 'ساعة', icon: '🕐', color: '#fa8c16' },
            { label: 'الوصفات السهلة', value: difficultyStats.easy, format: 'number', icon: '🟢', color: '#52c41a' },
            { label: 'الوصفات المتوسطة', value: difficultyStats.medium, format: 'number', icon: '🟡', color: '#faad14' },
            { label: 'الوصفات الصعبة', value: difficultyStats.hard, format: 'number', icon: '🟠', color: '#fa8c16' },
            { label: 'وصفات الخبراء', value: difficultyStats.expert, format: 'number', icon: '🔴', color: '#ff4d4f' },
            { label: 'عدد الأقسام المشاركة', value: Object.keys(departmentStats).length, format: 'number', icon: '🏭', color: '#13c2c2' }
          ]
        },

        // إحصائيات تفصيلية
        detailedStats: {
          difficultyDistribution: difficultyStats,
          departmentDistribution: departmentStats,
          timeAnalysis: {
            total: totalTime,
            average: averageTime,
            min: Math.min(...recipes.map(r => r.estimated_time || 0)),
            max: Math.max(...recipes.map(r => r.estimated_time || 0))
          },
          statusAnalysis: {
            active: activeRecipes,
            inactive: inactiveRecipes,
            activePercentage: totalRecipes > 0 ? (activeRecipes / totalRecipes * 100).toFixed(1) : '0'
          }
        },

        // معلومات إضافية
        additionalInfo: {
          generationNotes: `تم إنشاء هذا التقرير في ${new Date().toLocaleString('ar-SA')} ويحتوي على تحليل شامل لجميع وصفات الإنتاج المسجلة في النظام.`,
          recommendations: [
            totalRecipes === 0 ? 'لا توجد وصفات مسجلة في النظام. يُنصح بإضافة وصفات الإنتاج.' : null,
            inactiveRecipes > activeRecipes ? 'عدد الوصفات غير النشطة أكبر من النشطة. يُنصح بمراجعة الوصفات غير النشطة.' : null,
            difficultyStats.expert > totalRecipes * 0.5 ? 'نسبة عالية من وصفات الخبراء. قد تحتاج لتدريب إضافي للعمال.' : null,
            averageTime > 10 ? 'متوسط وقت الإنتاج مرتفع. يُنصح بمراجعة كفاءة العمليات.' : null
          ].filter(Boolean),
          warnings: [
            totalRecipes === 0 ? '⚠️ لا توجد وصفات في النظام' : null,
            activeRecipes === 0 ? '⚠️ لا توجد وصفات نشطة' : null,
            Object.keys(departmentStats).length === 0 ? '⚠️ لا توجد أقسام مرتبطة بالوصفات' : null
          ].filter(Boolean)
        },

        metadata: {
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام إدارة الإنتاج - ZET.IA',
          documentType: 'تقرير شامل لوصفات الإنتاج مع التحليلات والإحصائيات',
          documentVersion: '2.0',
          totalRecords: totalRecipes,
          reportScope: 'جميع وصفات الإنتاج',
          reportId: `RECIPES_REPORT_${Date.now()}`,
          dataSource: 'قاعدة بيانات الإنتاج',
          reportType: 'comprehensive_analysis',
          printSettings: {
            orientation: 'landscape',
            pageSize: 'A4',
            showLogo: true,
            showHeader: true,
            showFooter: true,
            showSignature: true,
            showPageNumbers: true,
            primaryColor: '#722ed1',
            secondaryColor: '#f0f2f5',
            fontSize: 11
          },
          statistics: {
            totalRecipes,
            activeRecipes,
            inactiveRecipes,
            averageTime,
            totalTime,
            difficultyStats,
            departmentStats
          }
        }
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحضير تقرير الوصفات الشامل:', error)
      throw new Error(`فشل في تحضير التقرير الشامل: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    }
  }

  // دالة تحضير بيانات طباعة الوصفة مع باركود QR
  const prepareRecipeWithQRCode = async (recipe: ProductionRecipe) => {
    try {
      // إنشاء بيانات QR Code تحتوي على معلومات الوصفة
      const qrData = {
        type: 'production_recipe',
        code: recipe.code,
        name: recipe.name,
        id: recipe.id,
        version: recipe.version,
        timestamp: new Date().toISOString()
      }

      // تحميل مواد الوصفة
      const materialsResult = await window.electronAPI?.getRecipeMaterials(recipe.id)
      const materials = materialsResult?.success ? materialsResult.data : []
      const totalCost = materials.reduce((sum: number, m: any) => sum + (m.total_cost || 0), 0)

      return {
        title: `وصفة إنتاج مع باركود: ${recipe.name}`,
        subtitle: `الكود: ${recipe.code} | المنتج: ${recipe.item_name} | التكلفة: ${totalCost.toFixed(2)} ج.م`,

        data: [{
          code: recipe.code,
          name: recipe.name,
          item_name: recipe.item_name || 'غير محدد',
          department_name: recipe.department_name || 'غير محدد',
          estimated_time: recipe.estimated_time || 0,
          difficulty_level: getDifficultyText(recipe.difficulty_level),
          version: recipe.version || 1,
          is_active: recipe.is_active ? 'نشطة ✅' : 'غير نشطة ❌',
          total_cost: totalCost,
          materials_count: materials.length,
          created_at: recipe.created_at ? new Date(recipe.created_at).toLocaleDateString('ar-SA') : 'غير محدد'
        }],

        columns: [
          { key: 'code', title: 'كود الوصفة', align: 'center' as const, width: '15%' },
          { key: 'name', title: 'اسم الوصفة', align: 'right' as const, width: '25%' },
          { key: 'item_name', title: 'المنتج النهائي', align: 'right' as const, width: '20%' },
          { key: 'department_name', title: 'القسم', align: 'center' as const, width: '15%' },
          { key: 'estimated_time', title: 'الوقت (ساعة)', align: 'center' as const, format: 'number' as const, width: '10%' },
          { key: 'difficulty_level', title: 'الصعوبة', align: 'center' as const, width: '10%' },
          { key: 'is_active', title: 'الحالة', align: 'center' as const, width: '10%' }
        ],

        // إضافة بيانات QR Code
        qrCode: JSON.stringify(qrData),
        qrCodeTitle: 'باركود الوصفة',
        qrCodeDescription: `امسح هذا الرمز للوصول السريع لوصفة ${recipe.name}`,

        summary: {
          compact: true,
          data: [
            { label: 'كود الوصفة', value: recipe.code, icon: '🏷️' },
            { label: 'عدد المواد', value: materials.length, format: 'number', icon: '📦' },
            { label: 'التكلفة الإجمالية', value: totalCost, format: 'currency', icon: '💰' },
            { label: 'الوقت المقدر', value: recipe.estimated_time || 0, format: 'number', unit: 'ساعة', icon: '⏱️' },
            { label: 'مستوى الصعوبة', value: getDifficultyText(recipe.difficulty_level), icon: '🎯' },
            { label: 'القسم المسؤول', value: recipe.department_name || 'غير محدد', icon: '🏭' },
            { label: 'إصدار الوصفة', value: `v${recipe.version || 1}`, icon: '📋' },
            { label: 'حالة الوصفة', value: recipe.is_active ? 'نشطة ✅' : 'غير نشطة ❌', icon: '🔄' }
          ]
        },

        additionalInfo: {
          qrInstructions: 'استخدم تطبيق قارئ الباركود لمسح الرمز والوصول السريع لمعلومات الوصفة',
          printInstructions: 'يمكن طباعة هذه الوصفة وإرفاقها بمنطقة الإنتاج للمرجعية السريعة',
          notes: `وصفة إنتاج ${recipe.name} - الكود ${recipe.code} - تم إنشاؤها في ${new Date().toLocaleDateString('ar-SA')}`
        },

        metadata: {
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام إدارة الإنتاج - ZET.IA',
          recipeCode: recipe.code,
          recipeName: recipe.name,
          documentType: 'وصفة إنتاج مع باركود QR',
          documentVersion: '2.0',
          qrCodeData: qrData,
          printSettings: {
            orientation: 'portrait',
            pageSize: 'A4',
            showLogo: true,
            showHeader: true,
            showFooter: true,
            showQRCode: true,
            primaryColor: '#722ed1'
          }
        }
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحضير بيانات الطباعة مع QR:', error)
      throw new Error(`فشل في تحضير بيانات الطباعة مع QR: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    }
  }

  // دالة تحضير بيانات طباعة ملصقات المواد
  const prepareMaterialLabels = async (recipe: ProductionRecipe) => {
    try {
      // تحميل مواد الوصفة
      const materialsResult = await window.electronAPI?.getRecipeMaterials(recipe.id)
      const materials = materialsResult?.success ? materialsResult.data : []

      if (materials.length === 0) {
        throw new Error('لا توجد مواد في هذه الوصفة لطباعة الملصقات')
      }

      // تحضير بيانات الملصقات
      const labelsData = materials.map((material: any, index: number) => ({
        id: index + 1,
        material_code: material.material_code || '',
        material_name: material.material_name || 'غير محدد',
        warehouse_name: material.warehouse_name || 'غير محدد',
        quantity: material.quantity || 0,
        unit: material.unit || 'قطعة',
        recipe_code: recipe.code,
        recipe_name: recipe.name,
        department_name: recipe.department_name || 'غير محدد',
        is_optional: material.is_optional ? 'اختياري' : 'إجباري',
        cost_per_unit: material.cost_per_unit || 0,
        total_cost: material.total_cost || 0,
        qr_data: JSON.stringify({
          type: 'material_for_recipe',
          material_id: material.material_id,
          material_code: material.material_code,
          recipe_id: recipe.id,
          recipe_code: recipe.code,
          quantity: material.quantity,
          unit: material.unit,
          timestamp: new Date().toISOString()
        })
      }))

      return {
        title: `ملصقات المواد - وصفة: ${recipe.name}`,
        subtitle: `الكود: ${recipe.code} | عدد المواد: ${materials.length} | القسم: ${recipe.department_name}`,

        data: labelsData,

        columns: [
          { key: 'id', title: '#', align: 'center' as const, width: '5%' },
          { key: 'material_code', title: 'كود المادة', align: 'center' as const, width: '15%' },
          { key: 'material_name', title: 'اسم المادة', align: 'right' as const, width: '25%' },
          { key: 'warehouse_name', title: 'المخزن', align: 'center' as const, width: '15%' },
          { key: 'quantity', title: 'الكمية', align: 'center' as const, format: 'number' as const, width: '10%' },
          { key: 'unit', title: 'الوحدة', align: 'center' as const, width: '10%' },
          { key: 'is_optional', title: 'النوع', align: 'center' as const, width: '10%' },
          { key: 'total_cost', title: 'التكلفة', align: 'center' as const, format: 'currency' as const, width: '10%' }
        ],

        // إعدادات خاصة بطباعة الملصقات
        labelSettings: {
          layout: 'grid',
          labelsPerPage: 12,
          labelSize: 'medium',
          showQRCode: true,
          showBarcode: true,
          includeInstructions: true
        },

        summary: {
          compact: true,
          data: [
            { label: 'إجمالي المواد', value: materials.length, format: 'number', icon: '📦' },
            { label: 'المواد الإجبارية', value: materials.filter((m: any) => !m.is_optional).length, format: 'number', icon: '⚡' },
            { label: 'المواد الاختيارية', value: materials.filter((m: any) => m.is_optional).length, format: 'number', icon: '🔧' },
            { label: 'إجمالي التكلفة', value: materials.reduce((sum: number, m: any) => sum + (m.total_cost || 0), 0), format: 'currency', icon: '💰' },
            { label: 'وصفة الإنتاج', value: recipe.name, icon: '📋' },
            { label: 'كود الوصفة', value: recipe.code, icon: '🏷️' },
            { label: 'القسم المسؤول', value: recipe.department_name || 'غير محدد', icon: '🏭' }
          ]
        },

        additionalInfo: {
          instructions: [
            'قم بطباعة هذه الملصقات وإرفاقها بالمواد المطلوبة',
            'تأكد من توفر جميع المواد قبل بدء الإنتاج',
            'استخدم قارئ الباركود للتحقق من صحة المواد',
            'احتفظ بنسخة من الملصقات للمرجعية'
          ],
          notes: `ملصقات المواد لوصفة ${recipe.name} - تم إنشاؤها في ${new Date().toLocaleDateString('ar-SA')}`,
          warnings: materials.length > 20 ? ['عدد كبير من المواد - تأكد من التنظيم الجيد'] : []
        },

        metadata: {
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام إدارة الإنتاج - ZET.IA',
          recipeCode: recipe.code,
          recipeName: recipe.name,
          documentType: 'ملصقات المواد للإنتاج',
          documentVersion: '2.0',
          totalMaterials: materials.length,
          printSettings: {
            orientation: 'portrait',
            pageSize: 'A4',
            showLogo: true,
            showHeader: true,
            showFooter: false,
            primaryColor: '#722ed1',
            labelLayout: true,
            labelsPerPage: 12
          }
        }
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحضير ملصقات المواد:', error)
      throw new Error(`فشل في تحضير ملصقات المواد: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    }
  }

  // دالة تحضير تقرير تحليل التكاليف للوصفة
  const prepareCostAnalysisReport = async (recipe: ProductionRecipe) => {
    try {
      // تحميل مواد الوصفة مع معلومات التكلفة
      const materialsResult = await window.electronAPI?.getRecipeMaterials(recipe.id)
      const materials = materialsResult?.success ? materialsResult.data : []

      if (materials.length === 0) {
        throw new Error('لا توجد مواد في هذه الوصفة لتحليل التكاليف')
      }

      // حساب التكاليف والإحصائيات
      const totalMaterialCost = materials.reduce((sum: number, m: any) => sum + (m.total_cost || 0), 0)
      const averageCostPerMaterial = totalMaterialCost / materials.length
      const mostExpensiveMaterial = materials.reduce((max: any, m: any) =>
        (m.total_cost || 0) > (max.total_cost || 0) ? m : max, materials[0])
      const leastExpensiveMaterial = materials.reduce((min: any, m: any) =>
        (m.total_cost || 0) < (min.total_cost || 0) ? m : min, materials[0])

      // تصنيف المواد حسب التكلفة
      const highCostMaterials = materials.filter((m: any) => (m.total_cost || 0) > averageCostPerMaterial * 1.5)
      const mediumCostMaterials = materials.filter((m: any) =>
        (m.total_cost || 0) <= averageCostPerMaterial * 1.5 && (m.total_cost || 0) >= averageCostPerMaterial * 0.5)
      const lowCostMaterials = materials.filter((m: any) => (m.total_cost || 0) < averageCostPerMaterial * 0.5)

      // تحضير بيانات التحليل
      const analysisData = materials.map((material: any, index: number) => {
        const costPercentage = totalMaterialCost > 0 ? ((material.total_cost || 0) / totalMaterialCost * 100) : 0
        const costCategory = (material.total_cost || 0) > averageCostPerMaterial * 1.5 ? 'مرتفع' :
                           (material.total_cost || 0) >= averageCostPerMaterial * 0.5 ? 'متوسط' : 'منخفض'

        return {
          id: index + 1,
          material_code: material.material_code || '',
          material_name: material.material_name || 'غير محدد',
          warehouse_name: material.warehouse_name || 'غير محدد',
          quantity: material.quantity || 0,
          unit: material.unit || 'قطعة',
          cost_per_unit: material.cost_per_unit || 0,
          total_cost: material.total_cost || 0,
          cost_percentage: costPercentage,
          cost_category: costCategory,
          is_optional: material.is_optional ? 'اختياري' : 'إجباري',
          cost_impact: costPercentage > 20 ? 'عالي' : costPercentage > 10 ? 'متوسط' : 'منخفض'
        }
      }).sort((a: any, b: any) => b.total_cost - a.total_cost) // ترتيب حسب التكلفة تنازلياً

      return {
        title: `تحليل التكاليف - وصفة: ${recipe.name}`,
        subtitle: `الكود: ${recipe.code} | إجمالي التكلفة: ${totalMaterialCost.toFixed(2)} ج.م | عدد المواد: ${materials.length}`,

        data: analysisData,

        columns: [
          { key: 'id', title: '#', align: 'center' as const, width: '5%' },
          { key: 'material_code', title: 'كود المادة', align: 'center' as const, width: '12%' },
          { key: 'material_name', title: 'اسم المادة', align: 'right' as const, width: '20%' },
          { key: 'quantity', title: 'الكمية', align: 'center' as const, format: 'number' as const, width: '8%' },
          { key: 'unit', title: 'الوحدة', align: 'center' as const, width: '8%' },
          { key: 'cost_per_unit', title: 'سعر الوحدة', align: 'center' as const, format: 'currency' as const, width: '10%' },
          { key: 'total_cost', title: 'التكلفة الإجمالية', align: 'center' as const, format: 'currency' as const, width: '12%' },
          { key: 'cost_percentage', title: 'النسبة %', align: 'center' as const, format: 'percentage' as const, width: '8%' },
          { key: 'cost_category', title: 'فئة التكلفة', align: 'center' as const, width: '10%' },
          { key: 'cost_impact', title: 'التأثير', align: 'center' as const, width: '8%' }
        ],

        summary: {
          compact: false,
          showTotals: true,
          data: [
            { label: 'إجمالي تكلفة المواد', value: totalMaterialCost, format: 'currency', icon: '💰', color: '#1890ff' },
            { label: 'متوسط تكلفة المادة', value: averageCostPerMaterial, format: 'currency', icon: '📊', color: '#722ed1' },
            { label: 'أغلى مادة', value: `${mostExpensiveMaterial?.material_name} (${(mostExpensiveMaterial?.total_cost || 0).toFixed(2)} ج.م)`, icon: '🔺', color: '#ff4d4f' },
            { label: 'أرخص مادة', value: `${leastExpensiveMaterial?.material_name} (${(leastExpensiveMaterial?.total_cost || 0).toFixed(2)} ج.م)`, icon: '🔻', color: '#52c41a' },
            { label: 'مواد عالية التكلفة', value: highCostMaterials.length, format: 'number', icon: '🔴', color: '#ff4d4f' },
            { label: 'مواد متوسطة التكلفة', value: mediumCostMaterials.length, format: 'number', icon: '🟡', color: '#faad14' },
            { label: 'مواد منخفضة التكلفة', value: lowCostMaterials.length, format: 'number', icon: '🟢', color: '#52c41a' },
            { label: 'الوقت المقدر للإنتاج', value: recipe.estimated_time || 0, format: 'number', unit: 'ساعة', icon: '⏱️', color: '#13c2c2' },
            { label: 'تكلفة الساعة الواحدة', value: (recipe.estimated_time || 0) > 0 ? totalMaterialCost / (recipe.estimated_time || 1) : 0, format: 'currency', unit: 'ج.م/ساعة', icon: '💸', color: '#fa8c16' }
          ]
        },

        // تحليل مفصل للتكاليف
        costBreakdown: {
          highCostMaterials: highCostMaterials.map((m: any) => ({
            name: m.material_name,
            cost: m.total_cost,
            percentage: totalMaterialCost > 0 ? (m.total_cost / totalMaterialCost * 100).toFixed(1) : '0'
          })),
          mediumCostMaterials: mediumCostMaterials.map((m: any) => ({
            name: m.material_name,
            cost: m.total_cost,
            percentage: totalMaterialCost > 0 ? (m.total_cost / totalMaterialCost * 100).toFixed(1) : '0'
          })),
          lowCostMaterials: lowCostMaterials.map((m: any) => ({
            name: m.material_name,
            cost: m.total_cost,
            percentage: totalMaterialCost > 0 ? (m.total_cost / totalMaterialCost * 100).toFixed(1) : '0'
          }))
        },

        additionalInfo: {
          recommendations: [
            highCostMaterials.length > materials.length * 0.3 ? 'نسبة عالية من المواد مرتفعة التكلفة - يُنصح بمراجعة الموردين' : null,
            totalMaterialCost > 1000 ? 'تكلفة مرتفعة للوصفة - قد تحتاج لمراجعة التسعير' : null,
            (recipe.estimated_time || 0) > 0 && totalMaterialCost / (recipe.estimated_time || 1) > 100 ? 'تكلفة عالية للساعة الواحدة - مراجعة الكفاءة مطلوبة' : null
          ].filter(Boolean),
          costOptimization: [
            'مراجعة أسعار الموردين للمواد عالية التكلفة',
            'البحث عن بدائل أقل تكلفة للمواد الاختيارية',
            'تحسين كميات المواد لتقليل الهدر',
            'مراجعة عمليات الإنتاج لتقليل الوقت المطلوب'
          ],
          notes: `تحليل التكاليف لوصفة ${recipe.name} - تم إنشاؤه في ${new Date().toLocaleDateString('ar-SA')}`
        },

        metadata: {
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام إدارة الإنتاج - ZET.IA',
          recipeCode: recipe.code,
          recipeName: recipe.name,
          documentType: 'تقرير تحليل التكاليف للوصفة',
          documentVersion: '2.0',
          totalMaterials: materials.length,
          totalCost: totalMaterialCost,
          analysisType: 'cost_breakdown_analysis',
          printSettings: {
            orientation: 'landscape',
            pageSize: 'A4',
            showLogo: true,
            showHeader: true,
            showFooter: true,
            showCharts: true,
            primaryColor: '#722ed1',
            secondaryColor: '#f0f2f5'
          }
        }
      }
    } catch (error) {
      Logger.error('ProductionRecipesManagement', 'خطأ في تحضير تقرير تحليل التكاليف:', error)
      throw new Error(`فشل في تحضير تقرير تحليل التكاليف: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    }
  }

  const columns = [
    {
      title: 'كود الوصفة',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: 'اسم الوصفة',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: 'الصنف',
      key: 'item',
      width: 200,
      render: (record: ProductionRecipe) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.item_name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.item_code}</div>
        </div>
      )
    },
    {
      title: 'القسم',
      dataIndex: 'department_name',
      key: 'department_name',
      width: 150,
    },
    {
      title: 'المخازن',
      key: 'warehouses',
      width: 200,
      render: (record: ProductionRecipe) => (
        <div>
          <div style={{ fontSize: '12px', marginBottom: '4px' }}>
            <strong>المواد:</strong> {(record as any).material_warehouse_name || 'غير محدد'}
          </div>
          <div style={{ fontSize: '12px' }}>
            <strong>المنتج:</strong> {(record as any).product_warehouse_name || 'غير محدد'}
          </div>
        </div>
      )
    },
    {
      title: 'مستوى الصعوبة',
      dataIndex: 'difficulty_level',
      key: 'difficulty_level',
      width: 120,
      render: (level: string) => (
        <Tag color={getDifficultyColor(level)}>
          {getDifficultyText(level)}
        </Tag>
      )
    },
    {
      title: 'الوقت المقدر',
      dataIndex: 'estimated_time',
      key: 'estimated_time',
      width: 120,
      render: (time: number) => time + ' ساعة'
    },
    {
      title: 'الإصدار',
      dataIndex: 'version',
      key: 'version',
      width: 80,
      render: (version: number) => 'v' + version
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 220,
      render: (record: ProductionRecipe) => (
        <Space wrap>
          {/* زر الطباعة السريعة للوصفة */}
          <Tooltip title="طباعة سريعة للوصفة">
            <UnifiedPrintButton
              data={{
                title: `وصفة إنتاج: ${record.name}`,
                subtitle: `الكود: ${record.code} | المنتج: ${record.item_name} | القسم: ${record.department_name}`,

                // إضافة معلومات الوصفة
                customer: {
                  name: `وصفة: ${record.name}`,
                  department: record.department_name || 'غير محدد',
                  orderDate: new Date().toLocaleDateString('ar-SA'),
                  deliveryDate: '',
                  type: 'وصفة إنتاج'
                },

                data: [{
                  code: record.code,
                  name: record.name,
                  item_name: record.item_name || 'غير محدد',
                  item_code: record.item_code || '',
                  department_name: record.department_name || 'غير محدد',
                  estimated_time: record.estimated_time || 0,
                  difficulty_level: getDifficultyText(record.difficulty_level),
                  version: record.version || 1,
                  is_active: record.is_active ? 'نشطة ✅' : 'غير نشطة ❌',
                  created_at: record.created_at ? new Date(record.created_at).toLocaleDateString('ar-SA') : 'غير محدد',
                  description: record.description || 'لا يوجد وصف',
                  instructions: record.instructions || 'لا توجد تعليمات'
                }],
                columns: [
                  { key: 'code', title: 'كود الوصفة', align: 'center' as const, width: '15%' },
                  { key: 'name', title: 'اسم الوصفة', align: 'right' as const, width: '25%' },
                  { key: 'item_name', title: 'المنتج النهائي', align: 'right' as const, width: '20%' },
                  { key: 'department_name', title: 'القسم', align: 'center' as const, width: '15%' },
                  { key: 'estimated_time', title: 'الوقت (ساعة)', align: 'center' as const, format: 'number' as const, width: '10%' },
                  { key: 'difficulty_level', title: 'الصعوبة', align: 'center' as const, width: '10%' },
                  { key: 'is_active', title: 'الحالة', align: 'center' as const, width: '10%' }
                ],
                summary: {
                  compact: true,
                  data: [
                    { label: 'الوقت المقدر', value: record.estimated_time || 0, format: 'number', unit: 'ساعة', icon: '⏱️' },
                    { label: 'مستوى الصعوبة', value: getDifficultyText(record.difficulty_level), icon: '🎯' },
                    { label: 'القسم المسؤول', value: record.department_name || 'غير محدد', icon: '🏭' },
                    { label: 'إصدار الوصفة', value: `v${record.version || 1}`, icon: '📋' },
                    { label: 'حالة الوصفة', value: record.is_active ? 'نشطة ✅' : 'غير نشطة ❌', icon: '🔄' }
                  ]
                },
                metadata: {
                  generatedAt: new Date().toISOString(),
                  generatedBy: 'نظام إدارة الإنتاج - ZET.IA',
                  recipeCode: record.code,
                  documentType: 'وصفة إنتاج - طباعة سريعة',
                  printSettings: {
                    orientation: 'portrait',
                    pageSize: 'A4',
                    showLogo: true,
                    showHeader: true,
                    showFooter: true,
                    primaryColor: '#722ed1'
                  }
                }
              }}
              type="report"
              subType="production"
              title="طباعة سريعة للوصفة"
              buttonText="طباعة"
              size="small"
              buttonType="default"
              showDropdown={true}
              showExportOptions={true}
              showSettings={true}
              icon={<PrinterOutlined />}
              customSettings={{
                showLogo: true,
                showHeader: true,
                showFooter: true,
                primaryColor: '#722ed1',
                orientation: 'portrait'
              }}
            />
          </Tooltip>

          {/* زر الطباعة المفصلة مع المواد */}
          <Tooltip title="طباعة مفصلة مع المواد والتكاليف">
            <UnifiedPrintButton
              data={async () => {
                try {
                  const printData = await prepareDetailedRecipePrintData(record)
                  if (!printData) {
                    throw new Error('فشل في تحضير بيانات الطباعة المفصلة')
                  }
                  return printData
                } catch (error) {
                  Logger.error('ProductionRecipesManagement', 'خطأ في تحضير البيانات المفصلة:', error)
                  throw error
                }
              }}
              type="report"
              subType="production"
              title="طباعة الوصفة المفصلة مع المواد والتكاليف"
              buttonText=""
              size="small"
              buttonType="primary"
              showDropdown={true}
              showExportOptions={true}
              showSettings={true}
              icon={<FilePdfOutlined />}
              customSettings={{
                showLogo: true,
                showHeader: true,
                showFooter: true,
                showSignature: true,
                showTerms: false,
                primaryColor: '#722ed1',
                secondaryColor: '#f0f2f5',
                orientation: 'portrait',
                pageSize: 'A4',
                fontSize: 11
              }}
              onBeforePrint={() => {
                Logger.info('ProductionRecipesManagement', `بدء طباعة الوصفة المفصلة: ${record.code}`)
              }}
              onAfterPrint={() => {
                Logger.info('ProductionRecipesManagement', `تم إكمال طباعة الوصفة المفصلة: ${record.code}`)
              }}
              onError={(error) => {
                Logger.error('ProductionRecipesManagement', `خطأ في طباعة الوصفة ${record.code}:`, error)
              }}
            />
          </Tooltip>

          {/* زر طباعة مع باركود QR */}
          <Tooltip title="طباعة مع باركود QR">
            <UnifiedPrintButton
              data={async () => {
                try {
                  const printData = await prepareRecipeWithQRCode(record)
                  if (!printData) {
                    throw new Error('فشل في تحضير بيانات الطباعة مع QR')
                  }
                  return printData
                } catch (error) {
                  Logger.error('ProductionRecipesManagement', 'خطأ في تحضير البيانات مع QR:', error)
                  throw error
                }
              }}
              type="report"
              subType="production"
              title="طباعة الوصفة مع باركود QR"
              buttonText=""
              size="small"
              buttonType="default"
              showDropdown={true}
              showExportOptions={true}
              showSettings={true}
              icon={<BarcodeOutlined />}
              customSettings={{
                showLogo: true,
                showHeader: true,
                showFooter: true,
                primaryColor: '#722ed1',
                orientation: 'portrait',
                pageSize: 'A4'
              }}
              onBeforePrint={() => {
                Logger.info('ProductionRecipesManagement', `بدء طباعة الوصفة مع QR: ${record.code}`)
              }}
              onAfterPrint={() => {
                Logger.info('ProductionRecipesManagement', `تم إكمال طباعة الوصفة مع QR: ${record.code}`)
              }}
              onError={(error) => {
                Logger.error('ProductionRecipesManagement', `خطأ في طباعة الوصفة مع QR ${record.code}:`, error)
              }}
            />
          </Tooltip>

          {/* زر طباعة ملصقات المواد */}
          <Tooltip title="طباعة ملصقات المواد">
            <UnifiedPrintButton
              data={async () => {
                try {
                  const printData = await prepareMaterialLabels(record)
                  if (!printData) {
                    throw new Error('فشل في تحضير ملصقات المواد')
                  }
                  return printData
                } catch (error) {
                  Logger.error('ProductionRecipesManagement', 'خطأ في تحضير ملصقات المواد:', error)
                  throw error
                }
              }}
              type="report"
              subType="production"
              title="طباعة ملصقات المواد للوصفة"
              buttonText=""
              size="small"
              buttonType="default"
              showDropdown={true}
              showExportOptions={true}
              showSettings={true}
              icon={<FilePdfOutlined style={{ color: '#fa8c16' }} />}
              customSettings={{
                showLogo: false,
                showHeader: true,
                showFooter: false,
                primaryColor: '#fa8c16',
                orientation: 'portrait',
                pageSize: 'A4'
              }}
              onBeforePrint={() => {
                Logger.info('ProductionRecipesManagement', `بدء طباعة ملصقات المواد: ${record.code}`)
              }}
              onAfterPrint={() => {
                Logger.info('ProductionRecipesManagement', `تم إكمال طباعة ملصقات المواد: ${record.code}`)
              }}
              onError={(error) => {
                Logger.error('ProductionRecipesManagement', `خطأ في طباعة ملصقات المواد ${record.code}:`, error)
              }}
            />
          </Tooltip>

          <Tooltip title="فحص توفر المواد">
            <Button
              type="default"
              size="small"
              icon={<InboxOutlined />}
              onClick={async () => {
                const availability = await checkMaterialsAvailability(record.id)
                if (availability) {
                  if (availability.available) {
                    message.success('جميع المواد متوفرة (' + availability.availableMaterials + '/' + availability.totalMaterials + ')')
                  } else {
                    message.warning(availability.insufficientMaterials.length + ' مادة غير كافية من أصل ' + availability.totalMaterials)
                  }
                }
              }}
            />
          </Tooltip>

          {/* زر تحليل التكاليف */}
          <Tooltip title="تحليل التكاليف">
            <UnifiedPrintButton
              data={async () => {
                try {
                  const printData = await prepareCostAnalysisReport(record)
                  if (!printData) {
                    throw new Error('فشل في تحضير تقرير تحليل التكاليف')
                  }
                  return printData
                } catch (error) {
                  Logger.error('ProductionRecipesManagement', 'خطأ في تحضير تحليل التكاليف:', error)
                  throw error
                }
              }}
              type="report"
              subType="production"
              title="تقرير تحليل التكاليف للوصفة"
              buttonText=""
              size="small"
              buttonType="default"
              showDropdown={true}
              showExportOptions={true}
              showSettings={true}
              icon={<FilePdfOutlined style={{ color: '#13c2c2' }} />}
              customSettings={{
                showLogo: true,
                showHeader: true,
                showFooter: true,
                primaryColor: '#13c2c2',
                orientation: 'landscape',
                pageSize: 'A4'
              }}
              onBeforePrint={() => {
                Logger.info('ProductionRecipesManagement', `بدء طباعة تحليل التكاليف: ${record.code}`)
              }}
              onAfterPrint={() => {
                Logger.info('ProductionRecipesManagement', `تم إكمال طباعة تحليل التكاليف: ${record.code}`)
              }}
              onError={(error) => {
                Logger.error('ProductionRecipesManagement', `خطأ في طباعة تحليل التكاليف ${record.code}:`, error)
              }}
            />
          </Tooltip>

          <Tooltip title="تنفيذ الإنتاج">
            <Button
              type="default"
              size="small"
              icon={<ExperimentOutlined />}
              style={{ color: '#52c41a', borderColor: '#52c41a' }}
              onClick={() => handleExecuteProduction(record)}
            />
          </Tooltip>
          <Tooltip title="عرض التفاصيل">
            <Button
              type="default"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="تعديل">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="حذف">
            <Popconfirm
              title="هل أنت متأكد من حذف هذه الوصفة؟"
              description="سيتم حذف الوصفة وجميع موادها نهائياً"
              onConfirm={() => handleDelete(record.id)}
              okText="نعم"
              cancelText="لا"
            >
              <Button
                danger
                size="small"
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* الإحصائيات السريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي الوصفات"
              value={stats.total}
              prefix={<ExperimentOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="سهل"
              value={stats.easy}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="متوسط"
              value={stats.medium}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="صعب"
              value={stats.hard}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="خبير"
              value={stats.expert}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="متوسط الوقت"
              value={stats.avg_time}
              precision={1}
              suffix="ساعة"
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* شريط الأدوات */}
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingRecipe(null)
            setRecipeMaterials([])
            form.resetFields()
            generateCode() // إنشاء كود تلقائي عند إضافة وصفة جديدة
            setModalVisible(true)
          }}
        >
          وصفة إنتاج جديدة
        </Button>
        <Button
          onClick={() => loadAllData(true)}
          loading={loading}
        >
          تحديث البيانات
        </Button>
        <Space>
          {/* زر طباعة التقرير الشامل المحسن */}
          <UnifiedPrintButton
            data={async () => {
              try {
                const reportData = await prepareComprehensiveRecipesReport()
                if (!reportData) {
                  throw new Error('فشل في تحضير بيانات التقرير الشامل')
                }
                return reportData
              } catch (error) {
                Logger.error('ProductionRecipesManagement', 'خطأ في تحضير التقرير الشامل:', error)
                throw error
              }
            }}
            type="report"
            subType="production"
            title="طباعة التقرير الشامل لوصفات الإنتاج مع التحليلات"
            buttonText="التقرير الشامل"
            size="middle"
            buttonType="primary"
            showDropdown={true}
            showExportOptions={true}
            showSettings={true}
            icon={<PrinterOutlined />}
            customSettings={{
              showLogo: true,
              showHeader: true,
              showFooter: true,
              showSignature: true,
              primaryColor: '#722ed1',
              orientation: 'landscape',
              pageSize: 'A4',
              fontSize: 11
            }}
            onBeforePrint={() => {
              Logger.info('ProductionRecipesManagement', 'بدء طباعة التقرير الشامل لوصفات الإنتاج')
            }}
            onAfterPrint={() => {
              Logger.info('ProductionRecipesManagement', 'تم إكمال طباعة التقرير الشامل لوصفات الإنتاج')
            }}
            onError={(error) => {
              Logger.error('ProductionRecipesManagement', 'خطأ في طباعة التقرير الشامل:', error)
            }}
          />

          {/* زر طباعة سريعة للقائمة الحالية */}
          <UnifiedPrintButton
            data={{
              title: 'قائمة وصفات الإنتاج',
              subtitle: `إجمالي الوصفات: ${recipes.length} | النشطة: ${recipes.filter(r => r.is_active).length} | تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}`,

              // إضافة معلومات عامة للتقرير
              customer: {
                name: 'تقرير وصفات الإنتاج',
                department: 'جميع الأقسام',
                orderDate: new Date().toLocaleDateString('ar-SA'),
                deliveryDate: '',
                type: 'تقرير'
              },

              data: recipes.map((recipe, index) => ({
                id: index + 1,
                code: recipe.code,
                name: recipe.name,
                item_name: recipe.item_name || 'غير محدد',
                item_code: recipe.item_code || '',
                department_name: recipe.department_name || 'غير محدد',
                estimated_time: recipe.estimated_time || 0,
                difficulty_level: getDifficultyText(recipe.difficulty_level),
                version: recipe.version || 1,
                is_active: recipe.is_active ? 'نشطة ✅' : 'غير نشطة ❌',
                created_at: recipe.created_at ? new Date(recipe.created_at).toLocaleDateString('ar-SA') : 'غير محدد'
              })),
              columns: [
                { key: 'id', title: '#', align: 'center' as const, width: '5%' },
                { key: 'code', title: 'كود الوصفة', align: 'center' as const, width: '12%' },
                { key: 'name', title: 'اسم الوصفة', align: 'right' as const, width: '20%' },
                { key: 'item_name', title: 'المنتج النهائي', align: 'right' as const, width: '18%' },
                { key: 'department_name', title: 'القسم', align: 'center' as const, width: '12%' },
                { key: 'estimated_time', title: 'الوقت (ساعة)', align: 'center' as const, format: 'number' as const, width: '10%' },
                { key: 'difficulty_level', title: 'الصعوبة', align: 'center' as const, width: '10%' },
                { key: 'version', title: 'الإصدار', align: 'center' as const, width: '8%' },
                { key: 'is_active', title: 'الحالة', align: 'center' as const, width: '8%' },
                { key: 'created_at', title: 'تاريخ الإنشاء', align: 'center' as const, width: '12%' }
              ],
              summary: {
                compact: true,
                data: [
                  { label: 'إجمالي الوصفات', value: recipes.length, format: 'number', icon: '📋' },
                  { label: 'الوصفات النشطة', value: recipes.filter(r => r.is_active).length, format: 'number', icon: '✅' },
                  { label: 'الوصفات السهلة', value: recipes.filter(r => r.difficulty_level === 'easy').length, format: 'number', icon: '🟢' },
                  { label: 'الوصفات المتوسطة', value: recipes.filter(r => r.difficulty_level === 'medium').length, format: 'number', icon: '🟡' },
                  { label: 'الوصفات الصعبة', value: recipes.filter(r => r.difficulty_level === 'hard').length, format: 'number', icon: '🟠' },
                  { label: 'وصفات الخبراء', value: recipes.filter(r => r.difficulty_level === 'expert').length, format: 'number', icon: '🔴' },
                  { label: 'إجمالي ساعات الإنتاج', value: recipes.reduce((sum, r) => sum + (r.estimated_time || 0), 0), format: 'number', unit: 'ساعة', icon: '⏱️' }
                ]
              },
              metadata: {
                generatedAt: new Date().toISOString(),
                generatedBy: 'نظام إدارة الإنتاج - ZET.IA',
                reportType: 'قائمة وصفات الإنتاج',
                documentType: 'قائمة سريعة لوصفات الإنتاج',
                totalRecords: recipes.length,
                printSettings: {
                  orientation: 'landscape',
                  pageSize: 'A4',
                  showLogo: true,
                  showHeader: true,
                  showFooter: true,
                  primaryColor: '#722ed1'
                }
              }
            }}
            type="report"
            subType="production"
            title="طباعة قائمة وصفات الإنتاج"
            buttonText="قائمة سريعة"
            size="middle"
            buttonType="default"
            showDropdown={true}
            showExportOptions={true}
            showSettings={true}
            icon={<BarcodeOutlined />}
            customSettings={{
              showLogo: true,
              showHeader: true,
              showFooter: true,
              primaryColor: '#722ed1',
              orientation: 'landscape',
              pageSize: 'A4'
            }}
          />
        </Space>
      </div>

      {/* جدول الوصفات */}
      <Table
        columns={columns}
        dataSource={recipes}
        rowKey="id"
        loading={loading}
        scroll={{ x: 1400 }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => 'إجمالي ' + total + ' وصفة'
        }}
      />

      {/* نافذة إضافة/تعديل وصفة الإنتاج */}
      <Modal
        title={editingRecipe ? 'تعديل وصفة الإنتاج' : 'وصفة إنتاج جديدة'}
        open={modalVisible}
        onCancel={() => {
          // تأكيد الإلغاء إذا كانت هناك تغييرات
          const hasChanges = recipeMaterials.length > 0 || form.isFieldsTouched()

          if (hasChanges) {
            Modal.confirm({
              title: 'تأكيد الإلغاء',
              content: 'هل أنت متأكد من إلغاء التغييرات؟ سيتم فقدان جميع البيانات المدخلة.',
              okText: 'نعم، إلغاء',
              cancelText: 'لا، متابعة التعديل',
              onOk: () => {
                setModalVisible(false)
                setEditingRecipe(null)
                setRecipeMaterials([])
                form.resetFields()
              }
            })
          } else {
            setModalVisible(false)
            setEditingRecipe(null)
            setRecipeMaterials([])
            form.resetFields()
          }
        }}
        footer={null}
        width={1000}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            difficulty_level: 'medium',
            version: 1,
            estimated_time: 1
          }}
        >
          <Tabs
            defaultActiveKey="1"
            items={[
              {
                key: '1',
                label: 'معلومات أساسية',
                children: (
                  <div>
                    <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="code"
                    label="كود الوصفة"
                    rules={[{ required: true, message: 'يرجى إدخال كود الوصفة' }]}
                  >
                    <Input
                      placeholder="أدخل كود الوصفة"
                      addonAfter={
                        !editingRecipe && (
                          <Button
                            size="small"
                            onClick={generateCode}
                            icon={<BarcodeOutlined />}
                          >
                            إنشاء تلقائي
                          </Button>
                        )
                      }
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="name"
                    label="اسم الوصفة"
                    rules={[{ required: true, message: 'يرجى إدخال اسم الوصفة' }]}
                  >
                    <Input placeholder="اسم الوصفة" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="item_id"
                    label={
                      <Space>
                        <span>المنتج النهائي</span>
                        <Button
                          type="link"
                          size="small"
                          icon={<PlusOutlined />}
                          onClick={() => {
                            // فتح نافذة إدارة الأصناف في تبويب جديد
                            if (window.electronAPI) {
                              window.electronAPI.openItemsManagement()
                            } else {
                              message.info('يرجى الانتقال إلى قسم إدارة الأصناف لإنشاء منتج جديد')
                            }
                          }}
                          style={{ padding: 0, height: 'auto' }}
                        >
                          إنشاء منتج جديد
                        </Button>
                      </Space>
                    }
                    rules={[{ required: true, message: 'يرجى اختيار المنتج النهائي' }]}
                  >
                    <Select
                      placeholder="اختر المنتج النهائي أو أنشئ منتج جديد"
                      showSearch
                      filterOption={(input, option) =>
                        (option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false) as boolean
                      }
                    >
                      {items.map(item => (
                        <Option key={item.id} value={item.id}>
                          {item.name} ({item.code})
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="department_id"
                    label="قسم الإنتاج"
                    rules={[{ required: true, message: 'يرجى اختيار قسم الإنتاج' }]}
                  >
                    <Select
                      placeholder="اختر قسم الإنتاج"
                      showSearch
                      optionFilterProp="children"
                      filterOption={(input, option) =>
                        option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                      }
                    >
                      {departments.map(dept => (
                        <Option key={dept.id} value={dept.id}>
                          {dept.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    name="product_warehouse_id"
                    label="مخزن المنتج التام"
                    rules={[{ required: true, message: 'يرجى اختيار مخزن المنتج التام' }]}
                  >
                    <Select
                      placeholder="اختر مخزن المنتج التام"
                      showSearch
                      optionFilterProp="children"
                      filterOption={(input, option) =>
                        option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                      }
                      onChange={(value) => {
                        form.setFieldsValue({ product_warehouse_id: value })
                      }}
                    >
                      {warehouses.map(warehouse => (
                        <Option key={warehouse.id} value={warehouse.id}>
                          {warehouse.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="difficulty_level"
                    label="مستوى الصعوبة"
                    initialValue="medium"
                  >
                    <Select>
                      <Option value="easy">سهل</Option>
                      <Option value="medium">متوسط</Option>
                      <Option value="hard">صعب</Option>
                      <Option value="expert">خبير</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="estimated_time"
                    label="الوقت المقدر (ساعة)"
                    rules={[{ required: true, message: 'الوقت المقدر مطلوب' }]}
                  >
                    <InputNumber
                      min={0.1}
                      max={1000}
                      step={0.5}
                      style={{ width: '100%' }}
                      placeholder="1"
                      addonAfter="ساعة"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="version"
                    label="رقم الإصدار"
                    initialValue={1}
                    rules={[{ required: true, message: 'رقم الإصدار مطلوب' }]}
                  >
                    <InputNumber min={1} max={999} style={{ width: '100%' }} placeholder="1" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="description"
                label="وصف الوصفة"
              >
                <TextArea rows={3} placeholder="وصف مختصر للوصفة..." />
              </Form.Item>

              <Form.Item
                name="instructions"
                label="تعليمات الإنتاج"
              >
                <TextArea rows={5} placeholder="تعليمات تفصيلية لعملية الإنتاج..." />
              </Form.Item>
                    </div>
                )
              },
              {
                key: '2',
                label: 'المواد المطلوبة',
                children: (
                  <div>
              {/* نموذج إضافة مادة جديدة - مبسط */}
              <Card
                title="إضافة مادة جديدة"
                size="small"
                style={{ marginBottom: '16px', backgroundColor: '#f8f9fa' }}
              >
                <Row gutter={[16, 16]}>
                  <Col span={8}>
                    <Form.Item label="المخزن" style={{ marginBottom: 8 }}>
                      <Select
                        placeholder="اختر المخزن"
                        value={selectedWarehouseForMaterial}
                        onChange={handleWarehouseChange}
                        showSearch
                        optionFilterProp="children"
                      >
                        {warehouses.map(warehouse => (
                          <Option key={warehouse.id} value={warehouse.id}>
                            {warehouse.name}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>

                  <Col span={8}>
                    <Form.Item label="المادة" style={{ marginBottom: 8 }}>
                      <Select
                        placeholder="اختر المادة"
                        value={selectedMaterialId}
                        onChange={setSelectedMaterialId}
                        disabled={!selectedWarehouseForMaterial}
                        showSearch
                        optionFilterProp="children"
                      >
                        {availableMaterialsInWarehouse.map(material => {
                          const inventoryInfo = material.inventory?.find((inv: any) =>
                            inv.warehouse_id === selectedWarehouseForMaterial
                          )
                          return (
                            <Option key={material.id} value={material.id}>
                              {material.name} - متوفر: {inventoryInfo?.quantity || 0} {material.unit}
                            </Option>
                          )
                        })}
                      </Select>
                    </Form.Item>
                  </Col>

                  <Col span={4}>
                    <Form.Item label="الكمية" style={{ marginBottom: 8 }}>
                      <InputNumber
                        min={0.1}
                        max={10000}
                        step={0.1}
                        value={materialQuantity}
                        onChange={(value) => setMaterialQuantity(value || 1)}
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>

                  <Col span={4}>
                    <Form.Item label=" " style={{ marginBottom: 8 }}>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={addNewMaterial}
                        disabled={!selectedWarehouseForMaterial || !selectedMaterialId}
                        style={{ width: '100%' }}
                      >
                        إضافة
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </Card>

              {materials.length === 0 && (
                <Alert
                  message="🚨 لا توجد مواد خام متاحة"
                  description={
                    <div>
                      <p><strong>لإنشاء وصفات الإنتاج، تحتاج إلى إضافة مواد خام أولاً.</strong></p>
                      <p>المواد الخام هي المكونات الأساسية التي تدخل في تصنيع المنتجات النهائية مثل الأخشاب، الدهانات، والاكسسوارات.</p>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={() => {
                          if (window.electronAPI) {
                            window.electronAPI.openMaterialsManagement()
                          } else {
                            message.info('يرجى الانتقال إلى قسم إدارة المواد لإنشاء مواد خام جديدة')
                          }
                        }}
                        style={{ marginTop: 8 }}
                      >
                        🔧 انتقل إلى إدارة المواد
                      </Button>
                    </div>
                  }
                  type="warning"
                  showIcon
                  style={{ marginBottom: '16px' }}
                />
              )}

              {/* زر سريع لإضافة مادة خام جديدة */}
              {materials.length > 0 && (
                <div style={{ marginBottom: '16px', textAlign: 'center' }}>
                  <Button
                    type="dashed"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      if (window.electronAPI) {
                        window.electronAPI.openMaterialsManagement()
                      } else {
                        message.info('يرجى الانتقال إلى قسم إدارة المواد لإنشاء مواد خام جديدة')
                      }
                    }}
                  >
                    🧱 إضافة مادة خام جديدة
                  </Button>
                  <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                    لا تجد المادة المطلوبة؟ أضفها من قسم إدارة المواد
                  </div>
                </div>
              )}

              {/* رسالة تحذيرية عند عدم وجود مواد */}
              {recipeMaterials.length === 0 && (
                <div style={{
                  textAlign: 'center',
                  padding: '20px',
                  backgroundColor: '#fff7e6',
                  border: '1px dashed #ffa940',
                  borderRadius: '6px',
                  marginBottom: '16px'
                }}>
                  <div style={{ color: '#fa8c16', marginBottom: '8px' }}>
                    ⚠️ لم يتم إضافة أي مواد بعد
                  </div>
                  <div style={{ color: '#666', fontSize: '14px' }}>
                    يجب إضافة مادة واحدة على الأقل لإكمال إنشاء الوصفة
                  </div>
                </div>
              )}

              {/* عناوين الأعمدة */}
              {recipeMaterials.length > 0 && (
                <Row gutter={8} style={{ marginBottom: '8px', padding: '8px', backgroundColor: '#fafafa', borderRadius: '4px' }}>
                  <Col span={5}><strong>المادة</strong></Col>
                  <Col span={4}><strong>المخزن</strong></Col>
                  <Col span={3}><strong>الكمية</strong></Col>
                  <Col span={2}><strong>الوحدة</strong></Col>
                  <Col span={3}><strong>سعر الوحدة</strong></Col>
                  <Col span={3}><strong>التكلفة الإجمالية</strong></Col>
                  <Col span={2}><strong>النوع</strong></Col>
                  <Col span={2}><strong>حذف</strong></Col>
                </Row>
              )}

              {recipeMaterials.map((material, index) => (
                <Card key={material.id} size="small" style={{ marginBottom: '8px' }}>
                  <Row gutter={8} align="middle">
                    <Col span={5}>
                      <Select
                        placeholder="اختر المادة"
                        value={material.material_id || undefined}
                        onChange={(value) => updateMaterial(index, 'material_id', value)}
                        style={{ width: '100%' }}
                        showSearch
                        filterOption={(input, option) =>
                          (option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false) as boolean
                        }
                      >
                        {materials.map(mat => (
                          <Option key={mat.id} value={mat.id}>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <div>
                                <div>{mat.name} ({mat.code})</div>
                              </div>
                              <span style={{ color: '#52c41a', fontWeight: 'bold', fontSize: '11px' }}>
                                ₪{(mat.cost_price || mat.sale_price || 0).toFixed(2)}
                              </span>
                            </div>
                          </Option>
                        ))}
                      </Select>
                    </Col>
                    <Col span={4}>
                      <Select
                        placeholder="اختر المخزن"
                        value={material.warehouse_id || undefined}
                        onChange={(value) => {
                          updateMaterial(index, 'warehouse_id', value)
                          // تحديث اسم المخزن أيضاً
                          const warehouse = warehouses.find(w => w.id === value)
                          updateMaterial(index, 'warehouse_name', warehouse?.name || '')
                        }}
                        style={{ width: '100%' }}
                        showSearch
                        filterOption={(input, option) =>
                          (option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false) as boolean
                        }
                      >
                        {warehouses.map(warehouse => {
                          // التحقق من توفر المادة في هذا المخزن
                          const materialInventory = material.material_id ?
                            getMaterialInventoryInfo(material.material_id, warehouse.id) : null
                          const availableQty = materialInventory ?
                            (materialInventory.quantity || 0) - (materialInventory.reserved_quantity || 0) : 0

                          return (
                            <Option key={warehouse.id} value={warehouse.id} disabled={material.material_id && availableQty <= 0}>
                              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <span>{warehouse.name}</span>
                                {material.material_id && materialInventory && (
                                  <span style={{
                                    fontSize: '10px',
                                    color: availableQty > 0 ? '#52c41a' : '#ff4d4f'
                                  }}>
                                    {availableQty.toFixed(2)}
                                  </span>
                                )}
                              </div>
                            </Option>
                          )
                        })}
                      </Select>
                    </Col>
                    <Col span={3}>
                      <InputNumber
                        placeholder="الكمية"
                        value={material.quantity}
                        onChange={(value) => updateMaterial(index, 'quantity', value || 0)}
                        min={0}
                        step={0.1}
                        style={{ width: '100%' }}
                      />
                    </Col>
                    <Col span={2}>
                      <Input
                        placeholder="الوحدة"
                        value={material.unit}
                        onChange={(e) => updateMaterial(index, 'unit', e.target.value)}
                        disabled
                        style={{ backgroundColor: '#f5f5f5' }}
                      />
                    </Col>
                    <Col span={3}>
                      <InputNumber
                        placeholder="سعر الوحدة"
                        value={material.cost_per_unit}
                        onChange={(value) => updateMaterial(index, 'cost_per_unit', value || 0)}
                        min={0}
                        step={0.01}
                        style={{ width: '100%' }}
                        formatter={value => `₪ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => parseFloat((value || '').replace(/₪\s?|(,*)/g, '')) || 0}
                      />
                    </Col>
                    <Col span={3}>
                      <InputNumber
                        placeholder="التكلفة الإجمالية"
                        value={material.total_cost}
                        disabled
                        style={{ width: '100%', backgroundColor: '#f0f8ff' }}
                        formatter={value => `₪ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      />
                    </Col>
                    <Col span={2}>
                      <Select
                        value={material.is_optional}
                        onChange={(value) => updateMaterial(index, 'is_optional', value)}
                        style={{ width: '100%' }}
                      >
                        <Option value={false}>أساسي</Option>
                        <Option value={true}>اختياري</Option>
                      </Select>
                    </Col>
                    <Col span={2}>
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => removeMaterial(index)}
                        style={{ width: '100%' }}
                      />
                    </Col>
                  </Row>
                  {material.material_name && (
                    <div style={{ marginTop: '4px', fontSize: '12px', color: '#666' }}>
                      <strong>{material.material_name}</strong> - {material.material_code} - {material.unit}
                      {material.warehouse_name && (
                        <span style={{ marginLeft: '8px', color: '#1890ff' }}>
                          📦 {material.warehouse_name}
                        </span>
                      )}
                      {material.warehouse_id && material.material_id && (
                        (() => {
                          const inventoryInfo = getMaterialInventoryInfo(material.material_id, material.warehouse_id)
                          const availableQty = inventoryInfo ?
                            (inventoryInfo.quantity || 0) - (inventoryInfo.reserved_quantity || 0) : 0
                          return inventoryInfo && (
                            <span style={{
                              marginLeft: '8px',
                              color: availableQty > 0 ? '#52c41a' : '#ff4d4f',
                              fontWeight: 'bold'
                            }}>
                              متوفر: {availableQty.toFixed(2)}
                            </span>
                          )
                        })()
                      )}
                    </div>
                  )}
                </Card>
              ))}

              {recipeMaterials.length > 0 && (
                <div style={{ marginTop: '16px', textAlign: 'right' }}>
                  <strong>
                    إجمالي تكلفة المواد: ₪{recipeMaterials.reduce((sum, m) => sum + m.total_cost, 0).toFixed(2)}
                  </strong>
                </div>
              )}
                  </div>
                )
              }
            ]}
          />

          <Divider />

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingRecipe ? 'تحديث' : 'إنشاء'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false)
                setEditingRecipe(null)
                setRecipeMaterials([])
                form.resetFields()
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نافذة عرض تفاصيل الوصفة */}
      <Modal
        title="تفاصيل وصفة الإنتاج"
        open={viewModalVisible}
        onCancel={() => {
          setViewModalVisible(false)
          setViewingRecipe(null)
        }}
        footer={[
          <Space key="actions">
            <Button key="close" onClick={() => {
              setViewModalVisible(false)
              setViewingRecipe(null)
            }}>
              إغلاق
            </Button>
          </Space>
        ]}
        width={800}
      >
        {viewingRecipe && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <p><strong>اسم الوصفة:</strong> {viewingRecipe.name}</p>
                <p><strong>كود الوصفة:</strong> {viewingRecipe.code}</p>
                <p><strong>الصنف:</strong> {viewingRecipe.item_name} ({viewingRecipe.item_code})</p>
                <p><strong>القسم:</strong> {viewingRecipe.department_name}</p>
              </Col>
              <Col span={12}>
                <p><strong>مستوى الصعوبة:</strong> 
                  <Tag color={getDifficultyColor(viewingRecipe.difficulty_level)} style={{ marginLeft: '8px' }}>
                    {getDifficultyText(viewingRecipe.difficulty_level)}
                  </Tag>
                </p>
                <p><strong>الوقت المقدر:</strong> {viewingRecipe.estimated_time} ساعة</p>
                <p><strong>الإصدار:</strong> v{viewingRecipe.version}</p>
              </Col>
            </Row>

            {viewingRecipe.description && (
              <div>
                <h4>الوصف:</h4>
                <p>{viewingRecipe.description}</p>
              </div>
            )}

            {viewingRecipe.instructions && (
              <div>
                <h4>تعليمات الإنتاج:</h4>
                <p style={{ whiteSpace: 'pre-wrap' }}>{viewingRecipe.instructions}</p>
              </div>
            )}

            <h4>المواد المطلوبة:</h4>
            {recipeMaterials.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                لا توجد مواد مضافة لهذه الوصفة
              </div>
            ) : (
              <List
                dataSource={recipeMaterials}
                renderItem={(material, _index) => {
                  const availability = materialAvailability.find(m => m.material_id === material.material_id)
                  return (
                    <List.Item style={{ border: '1px solid #f0f0f0', borderRadius: '6px', marginBottom: '8px', padding: '12px' }}>
                      <List.Item.Meta
                        title={
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <div>
                              <strong>{material.material_name}</strong> ({material.material_code})
                              {material.is_optional && <Tag color="orange" style={{ marginLeft: '8px' }}>اختياري</Tag>}
                            </div>
                            {availability && (
                              <Tag color={availability.is_sufficient ? 'green' : 'red'}>
                                {availability.is_sufficient ? 'متوفر' : 'غير كافي'}
                              </Tag>
                            )}
                          </div>
                        }
                        description={
                          <div>
                            <Row gutter={16} style={{ marginTop: '8px' }}>
                              <Col span={6}>
                                <div><strong>الكمية:</strong> {material.quantity} {material.unit}</div>
                              </Col>
                              <Col span={6}>
                                <div><strong>سعر الوحدة:</strong> ₪{material.cost_per_unit.toFixed(2)}</div>
                              </Col>
                              <Col span={6}>
                                <div><strong>التكلفة الإجمالية:</strong> ₪{material.total_cost.toFixed(2)}</div>
                              </Col>
                              <Col span={6}>
                                {availability && (
                                  <div style={{ color: availability.is_sufficient ? '#52c41a' : '#ff4d4f' }}>
                                    <strong>المتوفر:</strong> {availability.available_quantity} {material.unit}
                                    {!availability.is_sufficient && (
                                      <div style={{ color: '#ff4d4f' }}>
                                        <strong>النقص:</strong> {availability.shortage} {material.unit}
                                      </div>
                                    )}
                                  </div>
                                )}
                              </Col>
                            </Row>
                          </div>
                        }
                      />
                    </List.Item>
                  )
                }}
              />
            )}
            
            {recipeMaterials.length > 0 && (
              <div style={{ marginTop: '16px', textAlign: 'right', fontSize: '16px' }}>
                <strong>
                  إجمالي تكلفة المواد: ₪{recipeMaterials.reduce((sum, m) => sum + m.total_cost, 0).toFixed(2)}
                </strong>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* نموذج إنشاء منتج جديد */}
      <Modal
        title="إنشاء منتج نهائي جديد"
        open={newProductModalVisible}
        onCancel={() => {
          setNewProductModalVisible(false)
          newProductForm.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={newProductForm}
          layout="vertical"
          onFinish={handleCreateNewProduct}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="كود المنتج"
                rules={[{ required: true, message: 'يرجى إدخال كود المنتج' }]}
              >
                <Input
                  placeholder="أدخل كود المنتج"
                  addonAfter={
                    <Button
                      size="small"
                      onClick={() => generateProductCode(newProductForm.getFieldValue('category_id'))}
                      icon={<BarcodeOutlined />}
                      title="إنشاء كود تلقائي"
                    >
                      تلقائي
                    </Button>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="اسم المنتج"
                rules={[{ required: true, message: 'يرجى إدخال اسم المنتج' }]}
              >
                <Input placeholder="أدخل اسم المنتج" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category_id"
                label="الفئة"
                rules={[{ required: true, message: 'يرجى اختيار الفئة' }]}
              >
                <Select placeholder="اختر الفئة">
                  {categories.map(cat => (
                    <Option key={cat.id} value={cat.id}>
                      {cat.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="warehouse_id"
                label="المخزن"
                rules={[{ required: true, message: 'يرجى اختيار المخزن' }]}
              >
                <Select placeholder="اختر المخزن">
                  {warehouses.map(warehouse => (
                    <Option key={warehouse.id} value={warehouse.id}>
                      {warehouse.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="unit"
                label="وحدة القياس"
                rules={[{ required: true, message: 'يرجى إدخال وحدة القياس' }]}
              >
                <Input placeholder="قطعة" />
              </Form.Item>
            </Col>
            <Col span={16}>
              <Alert
                message="ملاحظة مهمة"
                description="سعر التكلفة وسعر البيع للمنتجات النهائية سيتم حسابهما تلقائياً بناءً على تكلفة المواد والوصفات. لا تحتاج لإدخالهما يدوياً."
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="الوصف"
          >
            <TextArea
              placeholder="وصف المنتج (اختياري)"
              rows={3}
            />
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => {
                setNewProductModalVisible(false)
                newProductForm.resetFields()
              }}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                إنشاء المنتج
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ProductionRecipesManagement
