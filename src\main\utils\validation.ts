// نظام التحقق من البيانات للنظام المحاسبي
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings?: string[]
}

export class FinancialValidator {
  // التحقق من بيانات الحساب البنكي
  static validateBankAccount(accountData: any): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // التحقق من الحقول المطلوبة
    if (!accountData.account_name || accountData.account_name.trim() === '') {
      errors.push('اسم الحساب مطلوب')
    }

    if (!accountData.account_number || accountData.account_number.trim() === '') {
      errors.push('رقم الحساب مطلوب')
    }

    if (!accountData.bank_name || accountData.bank_name.trim() === '') {
      errors.push('اسم البنك مطلوب')
    }

    if (!accountData.account_type || accountData.account_type.trim() === '') {
      errors.push('نوع الحساب مطلوب')
    }

    // التحقق من الرصيد (يمكن أن يكون balance أو initial_balance)
    const balanceValue = accountData.balance !== undefined ? accountData.balance : accountData.initial_balance
    if (balanceValue !== undefined && balanceValue !== null && balanceValue !== '') {
      const balance = parseFloat(balanceValue)
      if (isNaN(balance)) {
        errors.push('الرصيد يجب أن يكون رقماً صحيحاً')
      } else if (balance < 0) {
        warnings.push('الرصيد سالب - تأكد من صحة البيانات')
      }
    }

    // التحقق من طول رقم الحساب
    if (accountData.account_number && accountData.account_number.length < 5) {
      warnings.push('رقم الحساب قصير جداً - تأكد من صحته')
    }

    // التحقق من صحة IBAN إذا تم توفيره
    if (accountData.iban && accountData.iban.trim() !== '') {
      const iban = accountData.iban.replace(/\s/g, '').toUpperCase()
      if (iban.length < 15 || iban.length > 34) {
        warnings.push('طول رقم IBAN غير صحيح - تأكد من صحته')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // التحقق من بيانات الشيك
  static validateCheck(checkData: any): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // التحقق من الحقول المطلوبة
    if (!checkData.check_number || checkData.check_number.trim() === '') {
      errors.push('رقم الشيك مطلوب')
    }

    if (!checkData.payee_name || checkData.payee_name.trim() === '') {
      errors.push('اسم المستفيد مطلوب')
    }

    if (!checkData.amount) {
      errors.push('مبلغ الشيك مطلوب')
    } else {
      const amount = parseFloat(checkData.amount)
      if (isNaN(amount) || amount <= 0) {
        errors.push('مبلغ الشيك يجب أن يكون رقماً موجباً')
      } else if (amount > 1000000) {
        warnings.push('مبلغ الشيك كبير جداً - تأكد من صحة المبلغ')
      }
    }

    // التحقق من تاريخ الإصدار (قبول كلا من issue_date و check_date)
    const issueDate = checkData.issue_date || checkData.check_date
    if (!issueDate) {
      errors.push('تاريخ الإصدار مطلوب')
    } else {
      const date = new Date(issueDate)
      const today = new Date()
      if (isNaN(date.getTime())) {
        errors.push('تاريخ الإصدار غير صحيح')
      } else if (date > today) {
        warnings.push('تاريخ الإصدار في المستقبل')
      }
    }

    // التحقق من تاريخ الاستحقاق
    if (checkData.due_date) {
      const dueDate = new Date(checkData.due_date)
      if (isNaN(dueDate.getTime())) {
        errors.push('تاريخ الاستحقاق غير صحيح')
      } else if (issueDate) {
        const issueDateObj = new Date(issueDate)
        if (dueDate < issueDateObj) {
          errors.push('تاريخ الاستحقاق يجب أن يكون بعد تاريخ الإصدار')
        }
      }
    }

    if (!checkData.bank_account_id) {
      errors.push('الحساب البنكي مطلوب')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // التحقق من بيانات السند الإذني
  static validatePromissoryNote(noteData: any): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // التحقق من الحقول المطلوبة
    if (!noteData.note_number || noteData.note_number.trim() === '') {
      errors.push('رقم السند مطلوب')
    }

    if (!noteData.drawer_name || noteData.drawer_name.trim() === '') {
      errors.push('اسم الساحب مطلوب')
    }

    if (!noteData.payee_name || noteData.payee_name.trim() === '') {
      errors.push('اسم المستفيد مطلوب')
    }

    if (!noteData.amount) {
      errors.push('مبلغ السند مطلوب')
    } else {
      const amount = parseFloat(noteData.amount)
      if (isNaN(amount) || amount <= 0) {
        errors.push('مبلغ السند يجب أن يكون رقماً موجباً')
      }
    }

    if (!noteData.issue_date) {
      errors.push('تاريخ الإصدار مطلوب')
    }

    if (!noteData.due_date) {
      errors.push('تاريخ الاستحقاق مطلوب')
    } else if (noteData.issue_date) {
      const issueDate = new Date(noteData.issue_date)
      const dueDate = new Date(noteData.due_date)
      
      if (dueDate <= issueDate) {
        errors.push('تاريخ الاستحقاق يجب أن يكون بعد تاريخ الإصدار')
      }

      const daysDiff = Math.ceil((dueDate.getTime() - issueDate.getTime()) / (1000 * 3600 * 24))
      if (daysDiff > 365) {
        warnings.push('فترة السند طويلة جداً (أكثر من سنة)')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // التحقق من بيانات السند (قبض/دفع)
  static validateVoucher(voucherData: any): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // التحقق من الحقول المطلوبة
    if (!voucherData.voucher_number || voucherData.voucher_number.trim() === '') {
      errors.push('رقم السند مطلوب')
    }

    if (!voucherData.voucher_type || !['receipt', 'payment'].includes(voucherData.voucher_type)) {
      errors.push('نوع السند يجب أن يكون قبض أو دفع')
    }

    if (!voucherData.amount) {
      errors.push('مبلغ السند مطلوب')
    } else {
      const amount = parseFloat(voucherData.amount)
      if (isNaN(amount) || amount <= 0) {
        errors.push('مبلغ السند يجب أن يكون رقماً موجباً')
      }
    }

    if (!voucherData.description || voucherData.description.trim() === '') {
      errors.push('وصف السند مطلوب')
    }

    if (!voucherData.voucher_date) {
      errors.push('تاريخ السند مطلوب')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // التحقق من بيانات المعاملة المصرفية
  static validateBankTransaction(transactionData: any): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // التحقق من الحقول المطلوبة
    if (!transactionData.account_id) {
      errors.push('الحساب البنكي مطلوب')
    }

    if (!transactionData.transaction_type || !['deposit', 'withdrawal'].includes(transactionData.transaction_type)) {
      errors.push('نوع المعاملة يجب أن يكون إيداع أو سحب')
    }

    if (!transactionData.amount) {
      errors.push('مبلغ المعاملة مطلوب')
    } else {
      const amount = parseFloat(transactionData.amount)
      if (isNaN(amount) || amount <= 0) {
        errors.push('مبلغ المعاملة يجب أن يكون رقماً موجباً')
      }
    }

    if (!transactionData.description || transactionData.description.trim() === '') {
      errors.push('وصف المعاملة مطلوب')
    }

    if (!transactionData.transaction_date) {
      errors.push('تاريخ المعاملة مطلوب')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // التحقق من بيانات تحويل الشيك
  static validateCheckTransfer(transferData: any): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // التحقق من الحقول المطلوبة
    if (!transferData.check_id) {
      errors.push('معرف الشيك مطلوب')
    }

    if (!transferData.from_entity_name || transferData.from_entity_name.trim() === '') {
      errors.push('اسم الجهة المحولة مطلوب')
    }

    if (!transferData.to_entity_name || transferData.to_entity_name.trim() === '') {
      errors.push('اسم الجهة المحول إليها مطلوب')
    }

    if (!transferData.transfer_date) {
      errors.push('تاريخ التحويل مطلوب')
    }

    if (transferData.from_entity_name === transferData.to_entity_name) {
      errors.push('لا يمكن تحويل الشيك لنفس الجهة')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // دالة مساعدة للتحقق من التاريخ
  static isValidDate(dateString: string): boolean {
    const date = new Date(dateString)
    return date instanceof Date && !isNaN(date.getTime())
  }

  // دالة مساعدة للتحقق من الرقم
  static isValidNumber(value: any): boolean {
    return !isNaN(parseFloat(value)) && isFinite(value)
  }

  // دالة مساعدة للتحقق من النص
  static isValidText(text: string, minLength: number = 1): boolean {
    return typeof text === 'string' && text.trim().length >= minLength
  }
}
