import React, { useState, useEffect } from 'react'
import {
  Card, Table, Button, Modal, Space, Typography, Row, Col,
  Statistic, Tag, Input, Select, Tooltip, Spin, App
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  InboxOutlined, SearchOutlined, AppstoreOutlined,
  WarningOutlined, ExclamationCircleOutlined, CheckCircleOutlined,
  FilterOutlined, ReloadOutlined, PrinterOutlined,
  FileExcelOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { Warehouse, Item } from '../../types/global'
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig'
import * as XLSX from 'xlsx'

const { Text } = Typography
const { Option } = Select

const StyledCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
`

// const ActionButton = styled(Button)`
//   margin-left: 8px;
// `

interface WarehouseContentsProps {
  warehouse: Warehouse
  visible: boolean
  onClose: () => void
}

interface WarehouseItem extends Item {
  item_name: string
  item_code: string
  quantity: number
  reserved_quantity: number
  available_quantity: number
  location: string
  stock_status: 'normal' | 'low_stock' | 'out_of_stock' | 'high_stock'
  category_name?: string
}

const WarehouseContents: React.FC<WarehouseContentsProps> = ({
  warehouse,
  visible,
  onClose
}) => {
  const { message } = App.useApp()
  const [items, setItems] = useState<WarehouseItem[]>([])
  const [filteredItems, setFilteredItems] = useState<WarehouseItem[]>([])
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [categoryFilter, setCategoryFilter] = useState<string>('')
  const [categories, setCategories] = useState<any[]>([])
  // const printRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (visible && warehouse.id) {
      loadWarehouseContents()
      loadCategories()
    }
  }, [visible, warehouse.id])

  useEffect(() => {
    filterItems()
  }, [items, searchText, statusFilter, categoryFilter])

  const loadWarehouseContents = async () => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getItemsByWarehouse(warehouse.id)
        if (response.success) {
          const itemsData = response.data || []
          setItems(itemsData)
          Logger.info('WarehouseContents', '✅ تم تحميل ${itemsData.length} صنف للمخزن ${warehouse.name}')
        } else {
          const errorMessage = response.message || 'فشل في تحميل محتويات المخزن'
          Logger.error('WarehouseContents', '❌ خطأ في تحميل محتويات المخزن:', errorMessage)
          message.error(errorMessage)
        }
      } else {
        Logger.error('WarehouseContents', '❌ window.electronAPI غير متوفر')
        message.error('لا يمكن الوصول إلى قاعدة البيانات')
      }
    } catch (error) {
      Logger.error('WarehouseContents', '❌ خطأ في تحميل محتويات المخزن:', error)
      message.error('حدث خطأ أثناء تحميل محتويات المخزن')
    } finally {
      setLoading(false)
    }
  }

  const loadCategories = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getCategories()
        if (Array.isArray(response)) {
          setCategories(response)
        } else if (response && typeof response === 'object' && 'success' in response && (response as any).success) {
          setCategories((response as any).data || [])
        }
      }
    } catch (error) {
      Logger.error('WarehouseContents', 'خطأ في تحميل الفئات:', error)
    }
  }

  const filterItems = () => {
    let filtered = [...items]

    // فلترة بالنص
    if (searchText) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchText.toLowerCase()) ||
        item.code.toLowerCase().includes(searchText.toLowerCase()) ||
        (item.category_name && item.category_name.toLowerCase().includes(searchText.toLowerCase()))
      )
    }

    // فلترة بالحالة
    if (statusFilter) {
      filtered = filtered.filter(item => item.stock_status === statusFilter)
    }

    // فلترة بالفئة
    if (categoryFilter) {
      filtered = filtered.filter(item => item.category_id?.toString() === categoryFilter)
    }

    setFilteredItems(filtered)
  }

  const getStockStatusTag = (status: string, _quantity: number) => {
    switch (status) {
      case 'out_of_stock':
        return <Tag color="red" icon={<ExclamationCircleOutlined />}>نفد المخزون</Tag>
      case 'low_stock':
        return <Tag color="orange" icon={<WarningOutlined />}>مخزون منخفض</Tag>
      case 'high_stock':
        return <Tag color="blue" icon={<InboxOutlined />}>مخزون عالي</Tag>
      default:
        return <Tag color="green" icon={<CheckCircleOutlined />}>متوفر</Tag>
    }
  }

  // const getQuantityColor = (available: number, total: number) => {
  //   if (available === 0) return '#ff4d4f'
  //   if (available < total * 0.2) return '#fa8c16'
  //   return '#52c41a'
  // }

  // وّيفة الطباعة الموحدة
  const handlePrint = async () => {
    try {
      const { MasterPrintService } = await import('../../services/MasterPrintService')
      const printService = MasterPrintService.getInstance()

      const printData = {
        title: `محتويات المخزن - ${warehouse.name}`,
        subtitle: `إجمالي الأصناف: ${filteredItems.length}`,
        date: new Date().toLocaleDateString('en-GB'),
        items: filteredItems.map(item => ({
          name: item.item_name,
          description: item.item_code,
          quantity: item.available_quantity,
          unit: item.unit || 'قطعة',
          unitPrice: item.cost_price || 0,
          total: (item.available_quantity * (item.cost_price || 0))
        })),
        total: filteredItems.reduce((sum, item) => sum + (item.available_quantity * (item.cost_price || 0)), 0),
        notes: `تقرير محتويات المخزن: ${warehouse.name}`
      }

      await printService.print(printData, {
        type: 'report',
        subType: 'inventory',
        showLogo: true,
        showHeader: true,
        showFooter: true
      })

      message.success('تم إرسال التقرير للطباعة')
    } catch (error) {
      console.error('خطأ في الطباعة:', error)
      message.error('فشل في طباعة التقرير')
    }
  }

  // وّيفة التصدير إلى Excel
  const handleExportToExcel = () => {
    try {
      const exportData = filteredItems.map((item, index) => ({
        'الرقم': index + 1,
        'كود الصنف': item.code,
        'اسم الصنف': item.name,
        'الفئة': item.category_name || '-',
        'الوحدة': item.unit,
        'الكمية الإجمالية': item.quantity || 0,
        'الكمية المحجوزة': item.reserved_quantity || 0,
        'الكمية المتاحة': item.available_quantity || 0,
        'الموقع': item.location || '-',
        'الحالة': getStatusText(item.stock_status),
        'السعر': item.cost_price || 0,
        'القيمة الإجمالية': (item.quantity || 0) * (item.cost_price || 0)
      }))

      const worksheet = XLSX.utils.json_to_sheet(exportData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'محتويات المخزن')

      const fileName = `محتويات_المخزن_${warehouse.name}_${DateUtils.formatForDisplay(new Date().toISOString(), DATE_FORMATS.DISPLAY_DATE)}.xlsx`
      XLSX.writeFile(workbook, fileName)

      message.success('تم تصدير البيانات بنجاح')
    } catch (error) {
      Logger.error('WarehouseContents', 'خطأ في التصدير:', error)
      message.error('حدث خطأ أثناء التصدير')
    }
  }

  // الحصول على نص الحالة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'out_of_stock': return 'نفد المخزون'
      case 'low_stock': return 'مخزون منخفض'
      case 'high_stock': return 'مخزون عالي'
      default: return 'متوفر'
    }
  }



  const columns = [
    {
      title: 'كود الصنف',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      render: (text: string) => (
        <Text code style={{ fontSize: '12px' }}>{text}</Text>
      )
    },
    {
      title: 'اسم الصنف',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: WarehouseItem) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          {record.category_name && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.category_name}
            </Text>
          )}
        </div>
      )
    },
    {
      title: 'الكمية الإجمالية',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 120,
      align: 'center' as const,
      render: (quantity: number, record: WarehouseItem) => (
        <div>
          <div style={{ fontWeight: 'bold', fontSize: '16px' }}>
            {quantity || 0}
          </div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.unit}
          </Text>
        </div>
      )
    },
    {
      title: 'محجوزة',
      dataIndex: 'reserved_quantity',
      key: 'reserved_quantity',
      width: 100,
      align: 'center' as const,
      render: (quantity: number, record: WarehouseItem) => (
        <div>
          <Text type="warning" style={{ fontWeight: 'bold' }}>
            {quantity || 0}
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.unit}
          </Text>
        </div>
      )
    },
    {
      title: 'متاحة للبيع',
      dataIndex: 'available_quantity',
      key: 'available_quantity',
      width: 120,
      align: 'center' as const,
      render: (quantity: number, record: WarehouseItem) => (
        <div>
          <Text 
            style={{ 
              fontWeight: 'bold', 
              fontSize: '16px',
              color: quantity > 0 ? '#52c41a' : '#ff4d4f'
            }}
          >
            {quantity || 0}
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.unit}
          </Text>
        </div>
      )
    },
    {
      title: 'الموقع',
      dataIndex: 'location',
      key: 'location',
      width: 120,
      render: (location: string) => location || '-'
    },
    {
      title: 'الحالة',
      dataIndex: 'stock_status',
      key: 'stock_status',
      width: 120,
      align: 'center' as const,
      render: (status: string, record: WarehouseItem) => 
        getStockStatusTag(status, record.quantity)
    }
  ]

  // حساب الإحصائيات
  const totalItems = filteredItems.length
  const totalQuantity = filteredItems.reduce((sum, item) => sum + (item.quantity || 0), 0)
  // const totalReserved = filteredItems.reduce((sum, item) => sum + (item.reserved_quantity || 0), 0)
  // const totalAvailable = filteredItems.reduce((sum, item) => sum + (item.available_quantity || 0), 0)
  const outOfStockItems = filteredItems.filter(item => item.stock_status === 'out_of_stock').length
  const lowStockItems = filteredItems.filter(item => item.stock_status === 'low_stock').length

  return (
    <Modal
      title={
        <Space>
          <InboxOutlined style={{ color: '#1890ff' }} />
          <span>محتويات المخزن: {warehouse.name}</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width="90%"
      style={{ top: 20 }}
      styles={{ body: { padding: '24px' } }}
    >
      <Spin spinning={loading}>
        {/* إحصائيات سريعة */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <StyledCard>
              <Statistic
                title="إجمالي الأصناف"
                value={totalItems}
                prefix={<AppstoreOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </StyledCard>
          </Col>
          <Col span={6}>
            <StyledCard>
              <Statistic
                title="إجمالي الكميات"
                value={totalQuantity}
                prefix={<InboxOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </StyledCard>
          </Col>
          <Col span={6}>
            <StyledCard>
              <Statistic
                title="أصناف منخفضة"
                value={lowStockItems}
                prefix={<WarningOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </StyledCard>
          </Col>
          <Col span={6}>
            <StyledCard>
              <Statistic
                title="أصناف نفدت"
                value={outOfStockItems}
                prefix={<ExclamationCircleOutlined />}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </StyledCard>
          </Col>
        </Row>

        {/* أدوات البحث والفلترة */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={8}>
              <Input
                placeholder="البحث في الأصناف..."
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
              />
            </Col>
            <Col span={6}>
              <Select
                placeholder="فلترة بالحالة"
                style={{ width: '100%' }}
                allowClear
                value={statusFilter}
                onChange={setStatusFilter}
              >
                <Option value="normal">متوفر</Option>
                <Option value="low_stock">مخزون منخفض</Option>
                <Option value="out_of_stock">نفد المخزون</Option>
                <Option value="high_stock">مخزون عالي</Option>
              </Select>
            </Col>
            <Col span={6}>
              <Select
                placeholder="فلترة بالفئة"
                style={{ width: '100%' }}
                allowClear
                showSearch
                optionFilterProp="children"
                filterOption={(input, option) =>
                  option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                }
                value={categoryFilter}
                onChange={setCategoryFilter}
              >
                {categories.map(category => (
                  <Option key={category.id} value={category.id.toString()}>
                    {category.name}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={4}>
              <Space>
                <Tooltip title="إعادة تحميل">
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={loadWarehouseContents}
                    loading={loading}
                  />
                </Tooltip>
                <Button
                  icon={<FilterOutlined />}
                  onClick={() => {
                    setSearchText('')
                    setStatusFilter('')
                    setCategoryFilter('')
                  }}
                >
                  مسح الفلاتر
                </Button>
              </Space>
            </Col>
          </Row>

          {/* أزرار الطباعة والتصدير */}
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={24}>
              <Space style={{ float: 'left' }}>
                <Button
                  type="primary"
                  icon={<PrinterOutlined />}
                  onClick={handlePrint}
                  disabled={filteredItems.length === 0}
                >
                  طباعة التقرير
                </Button>
                <Button
                  icon={<FileExcelOutlined />}
                  onClick={handleExportToExcel}
                  disabled={filteredItems.length === 0}
                  style={{ color: '#52c41a', borderColor: '#52c41a' }}
                >
                  تصدير إلى Excel
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* جدول الأصناف */}
        <Card>
          <Table
            columns={columns}
            dataSource={filteredItems}
            rowKey={(record) => `${record.id}-${warehouse.id}`}
            loading={loading}
            scroll={{ x: 1000, y: 400 }}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} من ${total} صنف`,
              size: 'small'
            }}
            size="small"
          />
        </Card>
      </Spin>
    </Modal>
  )
}

export default WarehouseContents
