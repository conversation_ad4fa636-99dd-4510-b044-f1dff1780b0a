// تعريف الأنواع العامة للتطبيق

export interface User {
  id: number
  user_code?: string
  username: string
  full_name: string
  email?: string
  phone?: string
  role: 'admin' | 'manager' | 'accountant' | 'warehouse' | 'user'
  is_active: boolean
  last_login?: string | null
  login_attempts?: number
  locked_until?: string | null
  created_at: string
  updated_at?: string
  roles?: string // أدوار إضافية مفصولة بفاصلة
}

export interface Role {
  id: number
  name: string
  display_name: string
  description?: string
  permissions: string // JSON string
  is_system?: number
  is_active?: number
  created_at: string
  updated_at?: string
}

export interface Permission {
  id: number
  name: string
  description: string
  module: string
  action: string
  category?: string
  created_at: string
}

export interface RoleWithUsers extends Role {
  users?: User[]
  user_count?: number
}

export interface LoginAttempt {
  id: number
  username: string
  ip_address: string
  user_agent: string
  success: boolean
  attempt_time: string
  failure_reason?: string
}

export interface UserSession {
  id: number
  user_id: number
  token: string
  expires_at: string
  created_at: string
}

export interface Setting {
  id: number
  key: string
  value: string
  description?: string
}

export interface LoginResponse {
  success: boolean
  message?: string
  user?: User
  token?: string
  expiresAt?: string
}

export interface ApiResponse {
  success: boolean
  message?: string
  data?: any
  voucherId?: number
  id?: number
  user?: User
  available?: boolean
  availableQuantity?: number
  invoiceNumber?: string
  logoPath?: string
  fileName?: string
  name?: string
  length?: number
  filter?: (callback: (item: any) => boolean) => any[]
  reduce?: (callback: (acc: any, item: any) => any, initial: any) => any
  map?: (callback: (item: any) => any) => any[]
  code?: string
  details?: string
}

export interface CreateUserData {
  user_code?: string
  username: string
  password: string
  full_name: string
  email?: string
  phone?: string
  role: string
  is_active: boolean
}

export interface UpdateUserData {
  user_code?: string
  username?: string
  password?: string
  full_name?: string
  email?: string
  phone?: string
  role?: string
  is_active?: boolean
}

export interface Currency {
  id: number
  code: string
  name: string
  symbol: string
  exchange_rate: number
  is_default: boolean
  created_at: string
}

export interface Warehouse {
  id: number
  name: string
  location?: string
  manager_id?: number
  manager_name?: string
  is_active: boolean
  created_at: string
}

export interface Supplier {
  id: number
  code: string
  name: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  tax_number?: string
  balance: number
  credit_limit: number
  payment_terms: number
  is_active: boolean
  created_at: string
  updated_at?: string
}

export interface Category {
  id: number
  code?: string
  name: string
  description?: string
  parent_id?: number
  level: number
  path: string
  is_active: boolean
  created_at: string
}

export type ItemType = 'raw_material' | 'finished_product' | 'component' | 'tool' | 'consumable'

export const ITEM_TYPES: Record<ItemType, string> = {
  raw_material: 'مواد خام',
  finished_product: 'منتجات نهائية',
  component: 'مكونات',
  tool: 'أدوات',
  consumable: 'مواد استهلاكية'
}

export const ITEM_TYPE_OPTIONS = Object.entries(ITEM_TYPES).map(([value, label]) => ({
  value: value as ItemType,
  label
}))

export interface Item {
  id: number
  code: string
  name: string
  description?: string
  category_id?: number
  category_name?: string
  category_path?: string
  warehouse_id?: number
  warehouse_name?: string
  type: ItemType
  unit: string
  cost_price: number
  sale_price: number
  min_quantity: number
  max_quantity: number
  is_active?: boolean
  available_quantity?: number
  created_at: string
}

export interface ItemBarcode {
  id: number
  item_id: number
  barcode: string
  type: string
  is_primary: boolean
  created_at: string
}

export interface Inventory {
  id: number
  item_id: number
  item_code: string
  item_name: string
  warehouse_id: number
  warehouse_name: string
  category_name?: string
  unit: string
  quantity: number
  reserved_quantity: number
  cost_price: number
  sale_price: number
  min_quantity: number
  max_quantity: number
  cost_price?: number
  location?: string
  notes?: string
  last_updated: string
}

export interface InventoryMovement {
  id: number
  item_id: number
  item_code: string
  item_name: string
  warehouse_id: number
  warehouse_name: string
  movement_type: 'in' | 'out' | 'transfer' | 'adjustment'
  quantity: number
  reference_number?: string
  reference_type?: string
  reference_id?: number
  unit: string
  unit_cost: number
  notes?: string
  created_by?: number
  created_by_name?: string
  created_at: string
}

export interface OpeningStock {
  id?: number
  item_id: number
  item_name?: string
  item_code?: string
  warehouse_id: number
  warehouse_name?: string
  opening_quantity: number
  unit_cost: number
  opening_value: number
  opening_date: string
  notes?: string
  is_processed?: boolean
  created_at?: string
  updated_at?: string
}

// ===== أنواع نظام إدارة الصور =====

export interface ItemImage {
  id: number
  item_id: number
  image_name: string
  image_path: string
  image_size?: number
  image_width?: number
  image_height?: number
  image_type?: string
  is_primary: boolean
  description?: string
  sort_order: number
  created_at: string
  updated_at: string
  created_by?: number
}

export interface CheckImage {
  id: number
  check_id?: number
  receipt_id?: number
  image_name: string
  image_path: string
  image_type?: string
  image_side: 'front' | 'back' | 'deposit' | 'receipt'
  image_size?: number
  scan_quality?: string
  notes?: string
  created_at: string
  created_by?: number
}

export interface ImageSetting {
  id: number
  setting_key: string
  setting_value: string
  description?: string
  updated_at: string
  updated_by?: number
}

export interface ImageUploadData {
  item_id?: number
  check_id?: number
  receipt_id?: number
  image_name: string
  image_path: string
  image_size?: number
  image_width?: number
  image_height?: number
  image_type?: string
  image_side?: 'front' | 'back' | 'deposit' | 'receipt'
  description?: string
  is_primary?: boolean
  scan_quality?: string
  notes?: string
}

export interface ImageData {
  id: number
  name: string
  path: string
  size?: number
  type?: string
  check_id?: number
  created_at?: string
}

export interface ElectronAPI {
  // المصادقة والأمان
  login: (username: string, password: string) => Promise<LoginResponse>
  logout: (token: string) => Promise<ApiResponse>
  verifySession: (token: string) => Promise<ApiResponse>

  // المستخدمين
  getUsers: () => Promise<ApiResponse>
  getActiveUsers: () => Promise<ApiResponse>
  createUser: (userData: CreateUserData) => Promise<ApiResponse>
  updateUser: (userId: number, userData: UpdateUserData) => Promise<ApiResponse>
  deleteUser: (userId: number) => Promise<ApiResponse>
  getUserActivities: (userId: number) => Promise<ApiResponse>
  getAllUserActivities: (filters?: any) => Promise<ApiResponse>
  cleanupOldActivities: (daysToKeep?: number) => Promise<ApiResponse>
  resetUserPassword: (userId: number) => Promise<ApiResponse>
  toggleUserStatus: (userId: number, isActive: boolean) => Promise<ApiResponse>
  generateUserCode: () => Promise<ApiResponse>
  exportUsers: (format: 'excel' | 'csv') => Promise<ApiResponse>
  importUsers: (usersData: any[]) => Promise<ApiResponse>
  copyUserPermissions: (sourceUserId: number, targetUserIds: number[]) => Promise<ApiResponse>

  // الأدوار والصلاحيات
  getRoles: () => Promise<ApiResponse>
  getRolesWithUsers: () => Promise<ApiResponse>
  createRole: (roleData: any) => Promise<ApiResponse>
  updateRole: (roleId: number, roleData: any) => Promise<ApiResponse>
  deleteRole: (roleId: number) => Promise<ApiResponse>
  getPermissions: () => Promise<ApiResponse>
  getUserPermissions: (userId: number) => Promise<ApiResponse>

  // الإعدادات
  getSettings: () => Promise<ApiResponse>
  updateSetting: (key: string, value: string) => Promise<ApiResponse>
  updateSettings: (settings: any) => Promise<ApiResponse>
  getSetting: (key: string) => Promise<ApiResponse>
  getCompanyLogo: () => Promise<ApiResponse>
  uploadCompanyLogo: () => Promise<ApiResponse>
  deleteCompanyLogo: () => Promise<ApiResponse>

  // قراءة الملفات كـ base64 للطباعة
  readFileAsBase64: (filePath: string) => Promise<string | null>

  // Additional User Functions
  getCurrentUser: () => Promise<ApiResponse>
  getCurrentUserInfo: () => Promise<ApiResponse>
  getUserActivityLog: () => Promise<ApiResponse>

  // Additional Invoice Functions
  getSalesInvoices: () => Promise<ApiResponse>
  getPurchaseInvoices: () => Promise<ApiResponse>
  generatePurchaseInvoiceNumber: () => Promise<ApiResponse>
  generatePurchaseOrderNumber: () => Promise<ApiResponse>
  createPurchaseInvoice: (invoice: any) => Promise<ApiResponse>
  getSalesOrders: () => Promise<ApiResponse>
  getPurchaseOrders: () => Promise<ApiResponse>
  createSalesOrder: (orderData: any) => Promise<ApiResponse>
  createPurchaseOrder: (orderData: any) => Promise<ApiResponse>
  getUnpaidInvoices: (type?: string) => Promise<ApiResponse>
  updatePurchaseInvoiceStatus: (invoiceId: number, status: string) => Promise<ApiResponse>

  // Sales Invoice Functions
  generateSalesInvoiceNumber: () => Promise<ApiResponse>
  createSalesInvoice: (invoiceData: any) => Promise<ApiResponse>
  updateSalesInvoice: (invoiceId: number, invoiceData: any) => Promise<ApiResponse>
  updateSalesInvoiceStatus: (invoiceId: number, status: string) => Promise<ApiResponse>
  deleteSalesInvoice: (invoiceId: number) => Promise<ApiResponse>
  getSalesInvoiceItems: (invoiceId: number) => Promise<ApiResponse>
  checkInvoiceNumberUniqueness: (invoiceNumber: string, invoiceType: string, excludeId?: number) => Promise<ApiResponse>

  // Sales Order Functions
  updateSalesOrder: (orderId: number, orderData: any) => Promise<ApiResponse>
  deleteSalesOrder: (orderId: number) => Promise<ApiResponse>
  getSalesOrderItems: (orderId: number) => Promise<ApiResponse>
  getAvailableSalesOrders: (customerId?: number) => Promise<ApiResponse>

  // Purchase Invoice Functions
  updatePurchaseInvoice: (invoiceId: number, invoiceData: any) => Promise<ApiResponse>
  deletePurchaseInvoice: (invoiceId: number) => Promise<ApiResponse>
  getPurchaseInvoiceItems: (invoiceId: number) => Promise<ApiResponse>

  // Purchase Order Functions
  updatePurchaseOrder: (orderId: number, orderData: any) => Promise<ApiResponse>
  deletePurchaseOrder: (orderId: number) => Promise<ApiResponse>
  getPurchaseOrderItems: (orderId: number) => Promise<ApiResponse>

  // Universal Invoice System Functions
  getUniversalInvoices: (filters?: any) => Promise<ApiResponse>
  getUniversalInvoiceStatistics: (filters?: any) => Promise<ApiResponse>
  createUniversalInvoice: (invoiceData: any) => Promise<ApiResponse>
  updateUniversalInvoice: (invoiceType: string, invoiceId: number, invoiceData: any) => Promise<ApiResponse>
  deleteUniversalInvoice: (invoiceId: number, invoiceType: string) => Promise<ApiResponse>
  duplicateInvoice: (invoiceId: number, invoiceType: string) => Promise<ApiResponse>
  exportInvoices: (filters: any, format: string) => Promise<ApiResponse>

  // Database Functions
  checkDatabaseHealth: () => Promise<ApiResponse>
  reinitializeDatabase: () => Promise<ApiResponse>

  // System Functions
  getSystemInfo: () => Promise<ApiResponse>
  getRecentLoginAttempts: () => Promise<ApiResponse>
  resetPasswordRequest: (identifier: string) => Promise<ApiResponse>



  // Production Functions
  getProductionOrder: (orderId: number) => Promise<ApiResponse>
  getProductionOrderItems: (orderId: number) => Promise<ApiResponse>
  getProductionOrderMaterials: (orderId: number) => Promise<ApiResponse>
  getProductionOrderStagesDetails: (orderId: number) => Promise<ApiResponse>
  getProductionOrderImages: (orderId: number) => Promise<ApiResponse>
  uploadProductionOrderImage: (imageData: any) => Promise<ApiResponse>
  createSampleProductionOrderImages: (orderId: number) => Promise<ApiResponse>

  // نقل الصور من localStorage إلى قاعدة البيانات
  migrateImagesToDatabase: (localStorageData: { [key: string]: string }) => Promise<{
    success: boolean
    totalFound: number
    migrated: number
    skipped: number
    errors: string[]
    details: {
      localStorageKeys: string[]
      migratedImages: Array<{
        id: string
        orderId: string
        name: string
        size: number
        status: 'migrated' | 'skipped' | 'error'
        reason?: string
      }>
    }
  }>
  cleanupLocalStorageAfterMigration: (migrationResult: any) => Promise<{ success: boolean; message: string }>

  // Employee Functions
  syncFingerprintDevice: (deviceId: number) => Promise<ApiResponse>
  applyEmployeeLeave: (leaveData: any) => Promise<ApiResponse>
  approveEmployeeLeave: (leaveId: number, approvalData: any) => Promise<ApiResponse>
  calculateEmployeePayroll: (payrollData: any) => Promise<ApiResponse>

  // العملات
  getCurrencies: () => Promise<Currency[]>
  updateCurrencyRate: (currencyId: number, exchangeRate: number) => Promise<ApiResponse>

  // المخازن
  getWarehouses: () => Promise<Warehouse[]>
  getWarehouse: (warehouseId: number) => Promise<ApiResponse>
  createWarehouse: (warehouseData: any) => Promise<ApiResponse>
  updateWarehouse: (warehouseId: number, warehouseData: any) => Promise<ApiResponse>
  deleteWarehouse: (warehouseId: number) => Promise<ApiResponse>

  // الفئات
  getCategories: () => Promise<Category[]>
  createCategory: (categoryData: any) => Promise<ApiResponse>
  updateCategory: (categoryId: number, categoryData: any) => Promise<ApiResponse>
  deleteCategory: (categoryId: number) => Promise<ApiResponse>
  generateCategoryCode: () => Promise<ApiResponse>

  // الأصناف
  getItems: () => Promise<Item[]>
  getItemsByWarehouse: (warehouseId: number) => Promise<ApiResponse>
  getItemsByType: (type: string) => Promise<ApiResponse>
  getItemTypes: () => Promise<ApiResponse>
  createItem: (itemData: any) => Promise<ApiResponse>
  updateItem: (itemId: number, itemData: any) => Promise<ApiResponse>
  deleteItem: (itemId: number) => Promise<ApiResponse>
  generateItemCode: (categoryId?: number) => Promise<ApiResponse>
  generateItemCodeSafe: (categoryId?: number) => Promise<ApiResponse>
  findItemByBarcode: (barcode: string) => Promise<Item | null>
  getItemInventory: (itemId: number) => Promise<ApiResponse>
  getItemsWithInventory: () => Promise<ApiResponse>
  exportItems: (format: 'excel' | 'csv') => Promise<ApiResponse>
  importItems: (itemsData: any[]) => Promise<ApiResponse>

  // الباركود
  getItemBarcodes: (itemId: number) => Promise<ItemBarcode[]>
  addItemBarcode: (barcodeData: any) => Promise<ApiResponse>
  deleteItemBarcode: (barcodeId: number) => Promise<ApiResponse>

  // المخزون
  getInventory: () => Promise<Inventory[]>
  updateInventory: (inventoryData: any) => Promise<ApiResponse>
  checkItemAvailability: (itemId: number, warehouseId: number, requestedQuantity: number) => Promise<ApiResponse>
  getInventoryReport: (filters?: any) => Promise<ApiResponse>

  // حركات المخزون
  getInventoryMovements: (filters?: any) => Promise<InventoryMovement[]>
  getInventoryMovementsReport: (filters?: any) => Promise<ApiResponse>
  createInventoryMovement: (movementData: any) => Promise<ApiResponse>
  getItemWarehouseQuantity: (itemId: number, warehouseId: number) => Promise<ApiResponse>
  getLowStockReport: (filters?: any) => Promise<ApiResponse>

  // التنبيهات والإحصائيات
  getInventoryAlerts: () => Promise<ApiResponse>
  getInventoryStatistics: () => Promise<ApiResponse>

  // إدارة صور الأصناف
  getItemImages: (itemId: number) => Promise<ApiResponse>
  uploadItemImage: (imageData: any) => Promise<ApiResponse>
  downloadItemImage: (imageId: number) => Promise<ApiResponse>
  setItemPrimaryImage: (itemId: number, imageId: number) => Promise<ApiResponse>
  deleteItemImage: (imageId: number) => Promise<ApiResponse>

  // تحليل ABC
  getABCAnalysisReport: (filters?: any) => Promise<ApiResponse>

  // مخزون أول المدة
  getOpeningStock: () => Promise<ApiResponse<OpeningStock[]>>
  createOpeningStock: (openingStockData: OpeningStock) => Promise<ApiResponse>
  updateOpeningStock: (openingStockData: OpeningStock) => Promise<ApiResponse>
  deleteOpeningStock: (id: number) => Promise<ApiResponse>
  processOpeningStock: () => Promise<ApiResponse>

  // الدهان - أنواع الدهانات
  getPaintTypes: () => Promise<ApiResponse>
  createPaintType: (paintTypeData: any) => Promise<ApiResponse>
  updatePaintType: (paintTypeId: number, paintTypeData: any) => Promise<ApiResponse>
  deletePaintType: (paintTypeId: number) => Promise<ApiResponse>
  generatePaintTypeCode: () => Promise<ApiResponse>
  addSamplePaintTypesData: () => Promise<ApiResponse>

  // الدهان - أوامر الدهان
  getPaintOrders: () => Promise<ApiResponse>
  createPaintOrder: (orderData: any) => Promise<ApiResponse>
  updatePaintOrderStatus: (orderId: number, status: string) => Promise<ApiResponse>
  generatePaintOrderNumber: () => Promise<ApiResponse>

  // الدهان - فواتير الدهان
  getPaintInvoices: () => Promise<ApiResponse>
  createPaintInvoice: (invoiceData: any) => Promise<ApiResponse>
  updatePaintInvoiceStatus: (invoiceId: number, status: string) => Promise<ApiResponse>
  generatePaintInvoiceNumber: () => Promise<ApiResponse>

  // الإنتاج - أقسام الإنتاج
  getProductionDepartments: () => Promise<ApiResponse>
  createProductionDepartment: (departmentData: any) => Promise<ApiResponse>
  updateProductionDepartment: (departmentId: number, departmentData: any) => Promise<ApiResponse>
  deleteProductionDepartment: (departmentId: number) => Promise<ApiResponse>

  // الإنتاج - أوامر الإنتاج
  getProductionOrders: () => Promise<ApiResponse>
  createProductionOrder: (orderData: any) => Promise<ApiResponse>
  updateProductionOrder: (orderId: number, orderData: any) => Promise<ApiResponse>
  deleteProductionOrder: (orderId: number) => Promise<ApiResponse>
  generateProductionOrderNumber: () => Promise<ApiResponse>

  // الإنتاج - وصفات الإنتاج
  getProductionRecipes: () => Promise<ApiResponse>
  createProductionRecipe: (recipeData: any) => Promise<ApiResponse>
  updateProductionRecipe: (recipeId: number, recipeData: any) => Promise<ApiResponse>
  deleteProductionRecipe: (recipeId: number) => Promise<ApiResponse>
  generateRecipeCode: () => Promise<ApiResponse>
  getRecipeMaterials: (recipeId: number) => Promise<ApiResponse>
  checkRecipeMaterialsAvailability: (recipeId: number, productionQuantity?: number) => Promise<ApiResponse>
  checkMultipleRecipesMaterialsAvailability: (recipes: any[]) => Promise<ApiResponse>
  executeProduction: (productionData: any) => Promise<ApiResponse>
  executeProductionEnhanced: (productionData: any) => Promise<ApiResponse>

  // الإنتاج - مراحل الإنتاج
  getProductionStages: () => Promise<ApiResponse>
  createProductionStage: (stageData: any) => Promise<ApiResponse>
  updateProductionStage: (stageId: number, stageData: any) => Promise<ApiResponse>
  deleteProductionStage: (stageId: number) => Promise<ApiResponse>
  getProductionOrderStages: () => Promise<ApiResponse>
  getAllProductionOrderStages: () => Promise<ApiResponse>
  startProductionStage: (orderStageId: number) => Promise<ApiResponse>
  completeProductionStage: (orderStageData: any) => Promise<ApiResponse>

  // الإنتاج - ربط بالمخزون
  startProductionOrder: (orderId: number) => Promise<ApiResponse>
  completeProductionOrder: (orderId: number) => Promise<ApiResponse>

  // الإنتاج - التقارير
  getProductionOrdersReport: (filters: any) => Promise<ApiResponse>
  getProductionEfficiencyReport: (filters: any) => Promise<ApiResponse>
  getProductionCostsReport: (filters: any) => Promise<ApiResponse>
  getProductionScheduleReport: (filters: any) => Promise<ApiResponse>
  getProductionQualityReport: (filters: any) => Promise<ApiResponse>
  getProductionWorkersPerformanceReport: (filters: any) => Promise<ApiResponse>
  getProductionMaterialsConsumptionReport: (filters: any) => Promise<ApiResponse>
  getProductionProfitabilityReport: (filters: any) => Promise<ApiResponse>

  // الإنتاج - الإعدادات
  getProductionSettings: () => Promise<ApiResponse>
  saveProductionSettings: (settings: any) => Promise<ApiResponse>

  // الإنتاج - أوامر صرف المواد
  createMaterialIssueOrder: (orderData: any) => Promise<ApiResponse>
  getMaterialIssueOrders: (filters?: any) => Promise<ApiResponse>
  getMaterialIssueOrderDetails: (issueOrderId: number) => Promise<ApiResponse>

  // الإنتاج - تتبع ساعات العمل
  startLaborTimeTracking: (trackingData: any) => Promise<ApiResponse>
  endLaborTimeTracking: (trackingId: number, notes?: string) => Promise<ApiResponse>
  getLaborTimeTracking: (filters?: any) => Promise<ApiResponse>

  // العملاء
  getCustomers: () => Promise<ApiResponse>
  getCustomersPaginated: (options: any) => Promise<ApiResponse>
  createCustomer: (customerData: any) => Promise<ApiResponse>
  updateCustomer: (customerId: number, customerData: any) => Promise<ApiResponse>
  deleteCustomer: (customerId: number) => Promise<ApiResponse>
  generateCustomerCode: () => Promise<ApiResponse>
  searchCustomers: (searchTerm: string) => Promise<ApiResponse>
  exportCustomersData: () => Promise<ApiResponse>

  // الموردين
  getSuppliers: () => Promise<ApiResponse>
  createSupplier: (supplierData: any) => Promise<ApiResponse>
  updateSupplier: (supplierId: number, supplierData: any) => Promise<ApiResponse>
  deleteSupplier: (supplierId: number) => Promise<ApiResponse>
  generateSupplierCode: () => Promise<ApiResponse>
  getSupplierPayments: () => Promise<ApiResponse>
  generatePaymentNumber: () => Promise<ApiResponse>

  // البنوك والحسابات المصرفية
  getBankAccounts: () => Promise<ApiResponse>
  createBankAccount: (accountData: any) => Promise<ApiResponse>
  updateBankAccount: (accountId: number, accountData: any) => Promise<ApiResponse>
  deleteBankAccount: (accountId: number) => Promise<ApiResponse>

  // السندات المالية
  getPaymentVouchers: () => Promise<ApiResponse>
  getReceiptVouchers: () => Promise<ApiResponse>
  createPaymentVoucher: (voucherData: any) => Promise<ApiResponse>
  createReceiptVoucher: (voucherData: any) => Promise<ApiResponse>
  generatePaymentVoucherNumber: () => Promise<ApiResponse>
  generateReceiptVoucherNumber: () => Promise<ApiResponse>
  linkInvoiceToPayment: (linkData: any) => Promise<ApiResponse>

  // Customer and Supplier Payments
  createCustomerPayment: (paymentData: any) => Promise<ApiResponse>
  createSupplierPayment: (paymentData: any) => Promise<ApiResponse>
  getInvoicePayments: (invoiceId: number, type: string) => Promise<ApiResponse>

  // الشيكات
  getChecks: () => Promise<ApiResponse>
  createCheck: (checkData: any) => Promise<ApiResponse>
  updateCheckStatus: (checkId: number, status: string) => Promise<ApiResponse>
  generateCheckNumber: () => Promise<ApiResponse>
  getTransferableChecks: () => Promise<ApiResponse>
  getCheckTransfers: () => Promise<ApiResponse>
  getEntitiesForTransfer: () => Promise<ApiResponse>
  exportChecks: (format: 'excel' | 'csv') => Promise<ApiResponse>
  importChecks: (checksData: any[]) => Promise<ApiResponse>
  exportChecks: (format: 'excel' | 'csv') => Promise<ApiResponse>
  importChecks: (checksData: any[]) => Promise<ApiResponse>
  transferCheck: (transferData: any) => Promise<ApiResponse>
  getCheckHistory: (checkId: number) => Promise<ApiResponse>

  // المعاملات المصرفية
  getBankTransactions: () => Promise<ApiResponse>
  createBankTransaction: (transactionData: any) => Promise<ApiResponse>

  // السندات الإذنية
  getPromissoryNotes: () => Promise<ApiResponse>
  createPromissoryNote: (noteData: any) => Promise<ApiResponse>
  updatePromissoryNoteStatus: (noteId: number, status: string) => Promise<ApiResponse>
  generatePromissoryNoteNumber: () => Promise<ApiResponse>

  // تكاليف الإنتاج
  getProductionCosts: () => Promise<ApiResponse>

  // نظام الأكواد المحسن
  checkCodeUniqueness: (tableName: string, codeColumn: string, code: string, excludeId?: number) => Promise<ApiResponse>
  generatePaintTypeCode: () => Promise<ApiResponse>
  generateCostCenterCode: () => Promise<ApiResponse>
  generateWarehouseCode: () => Promise<ApiResponse>
  generateProductionDepartmentCode: () => Promise<ApiResponse>
  generateProductionStageCode: () => Promise<ApiResponse>
  generateRecipeCode: () => Promise<ApiResponse>

  // إدارة الموظفين
  getEmployees: () => Promise<ApiResponse>
  createEmployee: (employeeData: any) => Promise<ApiResponse>
  updateEmployee: (employeeId: number, employeeData: any) => Promise<ApiResponse>
  deleteEmployee: (employeeId: number) => Promise<ApiResponse>
  terminateEmployee: (employeeId: number, terminationData: any) => Promise<ApiResponse>
  generateEmployeeCode: () => Promise<ApiResponse>
  generateDepartmentCode: () => Promise<ApiResponse>

  // أقسام الموظفين
  getEmployeeDepartments: () => Promise<ApiResponse>
  createEmployeeDepartment: (departmentData: any) => Promise<ApiResponse>
  updateEmployeeDepartment: (departmentId: number, departmentData: any) => Promise<ApiResponse>
  deleteEmployeeDepartment: (departmentId: number) => Promise<ApiResponse>
  generateEmployeeDepartmentCode: () => Promise<ApiResponse>

  // أجهزة البصمة
  getFingerprintDevices: () => Promise<ApiResponse>
  createFingerprintDevice: (deviceData: any) => Promise<ApiResponse>
  updateFingerprintDevice: (deviceId: number, deviceData: any) => Promise<ApiResponse>
  deleteFingerprintDevice: (deviceId: number) => Promise<ApiResponse>

  // الحضور والانصراف
  getEmployeeAttendance: (filters?: any) => Promise<ApiResponse>
  recordAttendance: (attendanceData: any) => Promise<ApiResponse>
  updateAttendance: (attendanceId: number, attendanceData: any) => Promise<ApiResponse>
  deleteAttendance: (attendanceId: number) => Promise<ApiResponse>

  // الرواتب
  getEmployeePayroll: (filters?: any) => Promise<ApiResponse>
  calculatePayroll: (payrollData: any) => Promise<ApiResponse>
  updatePayroll: (payrollId: number, payrollData: any) => Promise<ApiResponse>
  deletePayroll: (payrollId: number) => Promise<ApiResponse>

  // الإجازات
  getEmployeeLeaves: (filters?: any) => Promise<ApiResponse>
  createLeaveRequest: (leaveData: any) => Promise<ApiResponse>
  updateLeaveRequest: (leaveId: number, leaveData: any) => Promise<ApiResponse>
  approveLeaveRequest: (leaveId: number, approvalData: any) => Promise<ApiResponse>
  deleteLeaveRequest: (leaveId: number) => Promise<ApiResponse>

  // ربط الموظفين بالأقسام الأخرى
  getEmployeesByDepartment: (departmentId: number) => Promise<ApiResponse>
  getEmployeeSalesPerformance: (employeeId: number, filters?: any) => Promise<ApiResponse>
  getEmployeeProductionAssignments: (employeeId: number) => Promise<ApiResponse>
  assignEmployeeToProduction: (assignmentData: any) => Promise<ApiResponse>
  getEmployeeFinancialTransactions: (employeeId: number, filters?: any) => Promise<ApiResponse>

  // تقارير الموظفين المحسنة
  getEmployeeAttendanceReport: (filters?: any) => Promise<ApiResponse>
  getEmployeePayrollReport: (filters?: any) => Promise<ApiResponse>
  getEmployeeLeavesReport: (filters?: any) => Promise<ApiResponse>
  getEmployeePerformanceReport: (filters?: any) => Promise<ApiResponse>

  // البيانات التجريبية
  addSampleEmployeesData: () => Promise<ApiResponse>
  addSamplePurchaseInvoicesData: () => Promise<ApiResponse>
  addSamplePurchaseOrdersData: () => Promise<ApiResponse>
  addSampleSuppliersData: () => Promise<ApiResponse>
  addSampleSupplierPaymentsData: () => Promise<ApiResponse>
  addSampleInventoryData: () => Promise<ApiResponse>
  addSampleFinanceData: () => Promise<ApiResponse>
  addSampleCustomersData: () => Promise<ApiResponse>
  addSampleSalesInvoicesData: () => Promise<ApiResponse>
  addSampleSalesOrdersData: () => Promise<ApiResponse>

  // تقارير تكامل الأقسام
  getCustomerComprehensiveInfo: (customerId: number) => Promise<ApiResponse>
  getItemComprehensiveInfo: (itemId: number) => Promise<ApiResponse>
  getDepartmentsIntegrationReport: () => Promise<ApiResponse>
  validateDepartmentsIntegrity: () => Promise<ApiResponse>

  // تقارير المبيعات
  getSalesByCustomerReport: (filters?: any) => Promise<ApiResponse>
  getSalesByProductReport: (filters?: any) => Promise<ApiResponse>
  getMonthlySalesReport: (filters?: any) => Promise<ApiResponse>
  getProfitabilityReport: (filters?: any) => Promise<ApiResponse>

  // تقارير الدهان
  getPaintByCustomerReport: (filters: any) => Promise<ApiResponse>
  getPaintByTypeReport: (filters: any) => Promise<ApiResponse>
  getMonthlyPaintReport: (filters: any) => Promise<ApiResponse>
  getPaintProfitabilityReport: (filters: any) => Promise<ApiResponse>
  getPaintPerformanceReport: (filters: any) => Promise<ApiResponse>
  getPaintQualityReport: (filters: any) => Promise<ApiResponse>

  // تقارير الإنتاج
  getProductionOrdersReport: (filters: any) => Promise<ApiResponse>
  getProductionEfficiencyReport: (filters: any) => Promise<ApiResponse>
  getMaterialConsumptionReport: (filters: any) => Promise<ApiResponse>
  getProductionCostsReport: (filters: any) => Promise<ApiResponse>
  getProductionScheduleReport: (filters: any) => Promise<ApiResponse>
  getProductionQualityReport: (filters: any) => Promise<ApiResponse>
  getProductionWorkersPerformanceReport: (filters: any) => Promise<ApiResponse>
  getProductionMaterialsConsumptionReport: (filters: any) => Promise<ApiResponse>
  getProductionProfitabilityReport: (filters: any) => Promise<ApiResponse>

  // تقارير متقدمة للإنتاج
  'get-production-efficiency-report': (filters: any) => Promise<ApiResponse>
  getProductionEfficiencyReport: (filters: any) => Promise<ApiResponse>

  // تقارير أخرى
  getLowStockReport: (filters: any) => Promise<ApiResponse>
  getCostAnalysisReport: (filters: any) => Promise<ApiResponse>
  getEmployeeOvertimeReport: (filters: any) => Promise<ApiResponse>
  getInventoryAuditReport: (filters: any) => Promise<ApiResponse>
  getPurchaseAnalysisReport: (filters: any) => Promise<ApiResponse>
  getPurchasesByItemReport: (filters: any) => Promise<ApiResponse>
  getPurchasesBySupplierReport: (filters: any) => Promise<ApiResponse>
  getSupplierPayablesReport: (filters: any) => Promise<ApiResponse>
  getSupplierAnalysisReport: (filters: any) => Promise<ApiResponse>
  getSupplierQualityReport: (filters: any) => Promise<ApiResponse>
  getSupplierPriceComparisonReport: (filters: any) => Promise<ApiResponse>
  getPurchaseMetrics: (filters: any) => Promise<ApiResponse>
  getSalesByProductReport: (filters: any) => Promise<ApiResponse>

  // ===== النسخ الاحتياطي المحسن =====
  getBackups: () => Promise<ApiResponse>
  createBackup: (options?: { description?: string; includeImages?: boolean; compress?: boolean }) => Promise<ApiResponse>
  restoreBackup: (backupPath: string) => Promise<ApiResponse>
  deleteBackup: (backupFilename: string) => Promise<ApiResponse>
  getBackupSettings: () => Promise<ApiResponse>
  updateBackupSettings: (settings: any) => Promise<ApiResponse>
  exportBackup: (backupFilename: string, exportPath?: string) => Promise<ApiResponse>
  importBackup: (importPath: string) => Promise<ApiResponse>
  verifyBackup: (backupFilename: string) => Promise<ApiResponse>

  // ===== نظام إدارة الصور =====

  // صور الأصناف
  uploadItemImage: (imageData: ImageUploadData) => Promise<ApiResponse>
  getItemImages: (itemId: number) => Promise<ApiResponse>
  deleteItemImage: (imageId: number) => Promise<ApiResponse>
  setPrimaryImage: (data: { imageId: number; itemId: number }) => Promise<ApiResponse>
  updateImageOrder: (data: { imageId: number; sortOrder: number }) => Promise<ApiResponse>
  updateImageDescription: (data: { imageId: number; description: string }) => Promise<ApiResponse>

  // صور الشيكات
  uploadCheckImage: (imageData: ImageUploadData) => Promise<ApiResponse>
  getCheckImages: (checkId: number) => Promise<ApiResponse>
  deleteCheckImage: (imageId: number) => Promise<ApiResponse>
  updateCheckImageNotes: (data: { imageId: number; notes: string }) => Promise<ApiResponse>

  // إعدادات الصور
  getImageSettings: () => Promise<ApiResponse>
  updateImageSetting: (data: { settingKey: string; settingValue: string }) => Promise<ApiResponse>
  getImageSetting: (settingKey: string) => Promise<ApiResponse>

  // معالجات إضافية للصور
  selectAndUploadItemImage: (itemId: number) => Promise<ApiResponse>
  selectAndUploadCheckImage: (checkId: number, imageSide?: string) => Promise<ApiResponse>

  // الإشعارات
  getNotifications: (filters?: any) => Promise<ApiResponse>
  createNotification: (notificationData: any) => Promise<ApiResponse>
  markNotificationRead: (notificationId: number, isRead?: boolean) => Promise<ApiResponse>
  markAllNotificationsRead: (userId?: number) => Promise<ApiResponse>
  deleteNotification: (notificationId: number) => Promise<ApiResponse>
  getUnreadNotificationsCount: (userId?: number) => Promise<ApiResponse>

  // الإشعارات الذكية
  getLowStockItems: (threshold?: number) => Promise<ApiResponse>
  getDueInvoices: (reminderDays?: number) => Promise<ApiResponse>
  getLastBackupInfo: () => Promise<ApiResponse>
  getSuspiciousActivities: () => Promise<ApiResponse>

  // العملاء
  getCustomers: () => Promise<ApiResponse>
  createCustomer: (customerData: any) => Promise<ApiResponse>
  updateCustomer: (customerData: any) => Promise<ApiResponse>
  deleteCustomer: (customerId: number) => Promise<ApiResponse>
  generateCustomerCode: () => Promise<ApiResponse>

  // الموردين
  getSuppliers: () => Promise<ApiResponse>
  createSupplier: (supplierData: any) => Promise<ApiResponse>
  updateSupplier: (supplierData: any) => Promise<ApiResponse>
  deleteSupplier: (supplierId: number) => Promise<ApiResponse>
  generateSupplierCode: () => Promise<ApiResponse>

  // فواتير المبيعات (تم نقلها إلى القسم الرئيسي أعلاه)
  // getSalesInvoices: () => Promise<ApiResponse> - مكررة
  // createSalesInvoice: (invoiceData: any) => Promise<ApiResponse> - مكررة
  // getSalesInvoiceItems: (invoiceId: number) => Promise<ApiResponse> - مكررة
  // generateSalesInvoiceNumber: () => Promise<ApiResponse> - مكررة

  // أوامر البيع
  getSalesOrders: () => Promise<ApiResponse>
  generateSalesOrderNumber: () => Promise<ApiResponse>

  // فواتير المشتريات
  getPurchaseInvoices: () => Promise<ApiResponse>
  createPurchaseInvoice: (invoiceData: any) => Promise<ApiResponse>
  getPurchaseInvoiceItems: (invoiceId: number) => Promise<ApiResponse>
  generatePurchaseInvoiceNumber: () => Promise<ApiResponse>

  // أوامر الشراء
  getPurchaseOrders: () => Promise<ApiResponse>
  createPurchaseOrder: (orderData: any) => Promise<ApiResponse>
  updatePurchaseOrder: (orderId: number, orderData: any) => Promise<ApiResponse>
  deletePurchaseOrder: (orderId: number) => Promise<ApiResponse>
  getPurchaseOrderItems: (orderId: number) => Promise<ApiResponse>
  generatePurchaseOrderNumber: () => Promise<ApiResponse>

  // الموظفين
  getEmployees: () => Promise<ApiResponse>
  createEmployee: (employeeData: any) => Promise<ApiResponse>
  generateEmployeeCode: () => Promise<ApiResponse>
  getEmployeeAttendance: (filters?: any) => Promise<ApiResponse>

  // الأقسام
  getDepartments: () => Promise<ApiResponse>
  createDepartment: (departmentData: any) => Promise<ApiResponse>

  // الحضور والانصراف
  recordAttendance: (employeeId: number, checkIn: string, date?: string) => Promise<ApiResponse>
  recordCheckout: (employeeId: number, checkOut: string, date?: string) => Promise<ApiResponse>
  getAttendanceRecords: (employeeId?: number, startDate?: string, endDate?: string) => Promise<ApiResponse>

  // الحسابات البنكية
  getBankAccounts: () => Promise<ApiResponse>
  createBankAccount: (accountData: any) => Promise<ApiResponse>

  // السندات
  getVouchers: (voucherType?: 'receipt' | 'payment') => Promise<ApiResponse>
  createVoucher: (voucherData: any) => Promise<ApiResponse>
  generateVoucherNumber: (voucherType: 'receipt' | 'payment') => Promise<ApiResponse>

  // الشيكات
  getChecks: () => Promise<ApiResponse>
  createCheck: (checkData: any) => Promise<ApiResponse>

  // دليل الحسابات
  getChartOfAccounts: () => Promise<ApiResponse>
  generateAccountCode: () => Promise<ApiResponse>
  createAccount: (accountData: any) => Promise<ApiResponse>
  updateAccount: (accountId: number, accountData: any) => Promise<ApiResponse>
  deleteAccount: (accountId: number) => Promise<ApiResponse>
  getAccountBalance: (accountId: number) => Promise<ApiResponse>
  getAccountTransactions: (accountId: number, filters?: any) => Promise<ApiResponse>

  // القيود اليومية
  getJournalEntries: (filters?: any) => Promise<ApiResponse>
  createJournalEntry: (entryData: any, userId?: number) => Promise<ApiResponse>
  updateJournalEntry: (entryId: number, entryData: any) => Promise<ApiResponse>
  deleteJournalEntry: (entryId: number) => Promise<ApiResponse>
  postJournalEntry: (entryId: number, userId?: number) => Promise<ApiResponse>
  generateJournalEntryNumber: () => Promise<ApiResponse>

  // التقارير المحاسبية
  getTrialBalance: (filters?: any) => Promise<ApiResponse>
  getBalanceSheet: (filters?: any) => Promise<ApiResponse>
  getIncomeStatement: (filters?: any) => Promise<ApiResponse>
  getCashFlowStatement: (filters?: any) => Promise<ApiResponse>
  getFinancialSummary: (filters?: any) => Promise<ApiResponse>
  getAgingReport: (filters?: any) => Promise<ApiResponse>
  getCashPositionReport: (filters?: any) => Promise<ApiResponse>

  // أقسام الإنتاج
  getProductionDepartments: () => Promise<ApiResponse>
  createProductionDepartment: (departmentData: any) => Promise<ApiResponse>
  generateProductionDepartmentCode: () => Promise<ApiResponse>

  // أوامر الإنتاج
  getProductionOrders: () => Promise<ApiResponse>
  createProductionOrder: (orderData: any) => Promise<ApiResponse>
  generateProductionOrderNumber: () => Promise<ApiResponse>
  generateProductionOrderCode: () => Promise<ApiResponse>
  getProductionOrderDetails: (orderId: number) => Promise<ApiResponse>

  // وصفات الإنتاج
  getProductionRecipes: () => Promise<ApiResponse>
  createProductionRecipe: (recipeData: any) => Promise<ApiResponse>
  generateRecipeCode: () => Promise<ApiResponse>

  // مراحل الإنتاج
  getProductionStages: () => Promise<ApiResponse>

  // الطباعة والتصدير المتقدم
  printFinancialReport: (reportType: string, params?: any) => Promise<ApiResponse>
  saveReportAsPDF: (reportType: string, filePath: string, params?: any) => Promise<ApiResponse>
  previewReport: (reportType: string, params?: any) => Promise<ApiResponse>
  exportReportToExcel: (reportType: string, params?: any) => Promise<ApiResponse>
  exportReportToCSV: (reportType: string, params?: any) => Promise<ApiResponse>
  exportFinancialReport: (reportType: string, params?: any) => Promise<ApiResponse>

  // طباعة الفواتير وأوامر الإنتاج
  printSalesInvoice: (invoiceId: number, params?: any) => Promise<ApiResponse>
  printPurchaseInvoice: (invoiceId: number, params?: any) => Promise<ApiResponse>
  printProductionOrder: (orderId: number, params?: any) => Promise<ApiResponse>
  printPaintOrder: (orderId: number, params?: any) => Promise<ApiResponse>


	  // اختبارات مؤقتة (وضع التطوير فقط)
	  runSelfTests: () => Promise<{ success: boolean; steps: any[]; durationMs: number }>

  // المزامنة وربط الأجهزة
  getSyncSettings: () => Promise<{ success: boolean; data: SyncConfig }>
  updateSyncSettings: (settings: SyncConfig) => Promise<{ success: boolean; message?: string }>
  testSyncConnection: (sharedFolder: string) => Promise<{ success: boolean; message: string }>
  getSyncStatus: () => Promise<SyncStatus>
  forceSyncNow: () => Promise<{ success: boolean; message?: string }>
  selectFolder: () => Promise<{ success: boolean; path?: string; message?: string }>

  // المزامنة الذكية
  createSmartSharedFolder: () => Promise<{ success: boolean; path?: string; message: string }>
  discoverConnectedDevices: () => Promise<{ success: boolean; devices: { ip: string; hostname?: string; mac?: string; isReachable: boolean }[]; message?: string }>
  getNetworkInfo: () => Promise<{ success: boolean; data?: { hostname: string; ip: string; mac?: string; workgroup?: string }; message?: string }>
  selectDeviceForSync: (deviceInfo: { ip: string; hostname?: string; mac?: string }) => Promise<{ success: boolean; path?: string; message: string; requiresSetup?: boolean; targetDevice?: any }>
  determineDeviceRole: () => Promise<{ success: boolean; role?: 'host' | 'client'; message?: string }>

  // دالة عامة للاستدعاءات
  invoke: (channel: string, ...args: any[]) => Promise<any>

  // وظائف التصدير المحسنة
  saveExcelFile: (buffer: ArrayBuffer, fileName: string) => Promise<{ success: boolean; message?: string; filePath?: string }>

  // معلومات النظام
  platform: string
  version: string

  // نظام التفعيل
  activateLicense: (activationCode: string) => Promise<{
    success: boolean
    message: string
    licenseInfo?: any
    expiryDate?: Date | null
    daysRemaining?: number
  }>
  checkActivationStatus: () => Promise<{
    isActivated: boolean
    licenseType?: string
    activationDate?: Date
    expiryDate?: Date | null
    daysRemaining?: number
    hardwareId?: string
  }>
  getLicenseInfo: () => Promise<{
    isActivated: boolean
    activationCode?: string
    licenseType?: string
    activationDate?: string
    expiryDate?: string | null
    daysRemaining?: number
    hardwareId?: string
    formattedInfo?: string
  }>
  deactivateLicense: () => Promise<{ success: boolean }>
  getHardwareId: () => Promise<{ success: boolean; hardwareId: string | null }>
  validateActivationCode: (activationCode: string) => Promise<{
    success: boolean
    isValid: boolean
    licenseInfo: any
  }>
  startActivationCheck: () => Promise<{ success: boolean }>
  stopActivationCheck: () => Promise<{ success: boolean }>

  // تقارير مقارنة الأسعار
  getSupplierPriceComparison: (params: any) => Promise<ApiResponse>

  // Template Storage API
  saveTemplate: (template: any) => Promise<ApiResponse>
  getTemplate: (id: string) => Promise<ApiResponse>
  getTemplates: (filter?: any) => Promise<ApiResponse>
  deleteTemplate: (id: string) => Promise<ApiResponse>
  toggleTemplate: (id: string, isActive: boolean) => Promise<ApiResponse>
  duplicateTemplate: (id: string, newName: string) => Promise<ApiResponse>
  exportTemplate: (id: string) => Promise<ApiResponse>
  importTemplate: (jsonData: any) => Promise<ApiResponse>
  getTemplateStats: () => Promise<ApiResponse>

  // Bank Reconciliation
  generateBankReconciliation: (params: any) => Promise<ApiResponse>

  // Sales Reports
  getSalesByRegion: (params: any) => Promise<ApiResponse>
  getSalesReturnsAndDiscounts: (params: any) => Promise<ApiResponse>
  getSalesReturnsReport: (params: any) => Promise<ApiResponse>
  getSupplierQualityReport: (params: any) => Promise<ApiResponse>
  getTopProfitableCustomers: (params: any) => Promise<ApiResponse>
  getTopProfitableCustomersReport: (params: any) => Promise<ApiResponse>
  getMonthlySalesReport: (params: any) => Promise<ApiResponse>

  // Cash Flow Reports
  getCashFlow: (params: any) => Promise<ApiResponse>

  // Employee Reports
  getEmployeeAnalysis: (params: any) => Promise<ApiResponse>
  getEmployeeLeavesReport: (params: any) => Promise<ApiResponse>

  // Purchase Performance Reports
  getPurchasePerformanceReport: (params: any) => Promise<ApiResponse>

  // Fiscal Closing Reports
  getFiscalClosingReport: (params: any) => Promise<ApiResponse>

  // Report optimization functions
  estimateReportSize: (type: string, filters: any) => Promise<ApiResponse>
  generateReportBatch: (type: string, filters: any, page: number, pageSize: number) => Promise<ApiResponse>

  // Navigation functions
  openItemsManagement: () => Promise<ApiResponse>
  openMaterialsManagement: () => Promise<ApiResponse>
  openRecipesManagement: () => Promise<ApiResponse>

  // Event listeners
  on: (channel: string, callback: (...args: any[]) => void) => void
  removeListener: (channel: string, callback: (...args: any[]) => void) => void
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
    electron?: {
      ipcRenderer?: {
        invoke: (channel: string, ...args: any[]) => Promise<any>
        on: (channel: string, func: (...args: any[]) => void) => void
        removeAllListeners: (channel: string) => void
      }
    }
    themeManager?: {
      loadThemeFromDatabase: () => Promise<void>
      getAntdThemeConfig: () => any
      applyTheme: (theme: any) => Promise<void>
      getCurrentTheme: () => any
    }
    smartNotificationService?: {
      updateSettings: (settings: any) => void
    }
    comprehensiveAudioSystem?: {
      playSound: (type: 'click' | 'hover' | 'success' | 'error' | 'warning' | 'notification') => void
      speakText: (elementId: string, customText?: string) => void
      addVoiceContent: (elementId: string, text: string) => void
      attachToNewElements: (container?: HTMLElement) => void
      updateSettings: (settings: any) => void
      testSystem: () => void
    }
  }
}

// أنواع المزامنة
export interface SyncConfig {
  enabled: boolean
  deviceRole: 'main' | 'branch'
  sharedFolder: string
  syncInterval: number
}

export interface SyncStatus {
  enabled: boolean
  deviceRole: string
  isActive: boolean
  lastSync: Date
  sharedFolder: string
  isConnected: boolean
}
