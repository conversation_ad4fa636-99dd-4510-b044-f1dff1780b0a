import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { Logger } from '../utils/logger'
import { ProductionErrorCodes, createErrorResponse } from '../constants/ErrorCodes'

export interface ProductionDepartment {
  id: number
  code?: string
  name: string
  description?: string
  supervisor_id?: number
  supervisor_name?: string
  is_active: boolean
  created_at: string
}

export interface ProductionOrder {
  id: number
  order_number: string
  product_id: number
  recipe_id?: number
  product_name?: string
  product_code?: string
  product_unit?: string
  quantity: number
  department_id?: number
  department_name?: string
  customer_id?: number
  customer_name?: string
  start_date?: string
  end_date?: string
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority: 'low' | 'normal' | 'high' | 'urgent'
  estimated_cost: number
  actual_cost: number
  estimated_hours: number
  actual_hours: number
  notes?: string
  created_at: string
  created_by?: number
}

export interface ProductionRecipe {
  id: number
  code?: string
  name: string
  description?: string
  product_id?: number
  product_name?: string
  product_code?: string
  department_id?: number
  department_name?: string
  estimated_time: number
  estimated_cost: number
  difficulty_level: 'easy' | 'medium' | 'hard' | 'expert'
  instructions?: string
  version: number
  is_active: boolean
  created_at: string
  materials?: RecipeMaterial[]
}

export interface RecipeMaterial {
  id: number
  recipe_id: number
  material_id: number
  material_name?: string
  material_code?: string
  quantity: number
  unit?: string
  cost_per_unit: number
  total_cost: number
  is_optional: boolean
  notes?: string
}

export interface CompositeProduct {
  id: number
  composite_product_id: number
  composite_product_name?: string
  component_product_id: number
  component_product_name?: string
  quantity: number
  unit: string
  is_optional: boolean
  notes?: string
  created_at: string
}

export interface ProductionStage {
  id: number
  code?: string
  name: string
  description?: string
  sequence_order: number
  estimated_duration?: number
  is_active: boolean
  created_at: string
}

export class ProductionService {
  private static instance: ProductionService
  private db: any

  private constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
  }

  public static getInstance(): ProductionService {
    if (!ProductionService.instance) {
      ProductionService.instance = new ProductionService()
    }
    return ProductionService.instance
  }

  // إنشاء جداول الإنتاج
  public async createProductionTables(): Promise<void> {
    const database = this.db

    // جدول أقسام الإنتاج
    database.exec(`
      CREATE TABLE IF NOT EXISTS production_departments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        supervisor_id INTEGER,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (supervisor_id) REFERENCES employees (id)
      )
    `)

    // جدول أوامر الإنتاج - التعريف الموحد والمحسن
    database.exec(`
      CREATE TABLE IF NOT EXISTS production_orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_number TEXT UNIQUE NOT NULL,
        order_code TEXT,
        product_id INTEGER NOT NULL,
        recipe_id INTEGER,
        quantity DECIMAL(10,2) NOT NULL CHECK (quantity > 0),
        department_id INTEGER,
        customer_id INTEGER,
        warehouse_id INTEGER,
        start_date DATE,
        end_date DATE,
        completion_date DATETIME,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
        priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
        estimated_cost DECIMAL(10,2) DEFAULT 0 CHECK (estimated_cost >= 0),
        actual_cost DECIMAL(10,2) DEFAULT 0 CHECK (actual_cost >= 0),
        estimated_hours INTEGER DEFAULT 0 CHECK (estimated_hours >= 0),
        actual_hours INTEGER DEFAULT 0 CHECK (actual_hours >= 0),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (product_id) REFERENCES items (id) ON DELETE RESTRICT,
        FOREIGN KEY (recipe_id) REFERENCES production_recipes (id) ON DELETE SET NULL,
        FOREIGN KEY (department_id) REFERENCES production_departments (id) ON DELETE SET NULL,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE SET NULL,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users (id) ON DELETE SET NULL
      )
    `)

    // التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
    try {
      const tableInfo = database.exec(`PRAGMA table_info(production_orders)`)[0]
      const columns = tableInfo ? tableInfo.values.map((row: any) => row[1]) : []

      // إضافة الأعمدة الجديدة للتكلفة الفعلية
      const newColumns = [
        { name: 'recipe_id', type: 'INTEGER' },
        { name: 'actual_quantity', type: 'DECIMAL(10,2)' },
        { name: 'material_cost', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'labor_cost', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'overhead_cost', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'unit_cost', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'labor_hours', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'quality_notes', type: 'TEXT' },
        { name: 'completion_notes', type: 'TEXT' }
      ]

      for (const column of newColumns) {
        if (!columns.includes(column.name)) {
          Logger.info('ProductionService', `إضافة عمود ${column.name} إلى جدول production_orders`)
          database.exec(`ALTER TABLE production_orders ADD COLUMN ${column.name} ${column.type}`)
        }
      }



      if (!columns.includes('order_number')) {
        Logger.info('ProductionService', 'إضافة عمود order_number إلى جدول production_orders')
        database.exec(`ALTER TABLE production_orders ADD COLUMN order_number TEXT`)
        // تحديث السجلات الموجودة برقم أمر افتراضي
        database.exec(`UPDATE production_orders SET order_number = 'PROD-' || id WHERE order_number IS NULL`)
      }

      if (!columns.includes('completion_date')) {
        Logger.info('ProductionService', 'إضافة عمود completion_date إلى جدول production_orders')
        database.exec(`ALTER TABLE production_orders ADD COLUMN completion_date DATETIME`)
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في التحقق من أعمدة جدول production_orders:', error)
    }

    // إنشاء فهارس الأداء لجدول أوامر الإنتاج
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_orders_number ON production_orders (order_number)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_orders_status ON production_orders (status)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_orders_product ON production_orders (product_id)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_orders_recipe ON production_orders (recipe_id)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_orders_created ON production_orders (created_at)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_orders_department ON production_orders (department_id)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_orders_customer ON production_orders (customer_id)`)

    // إنشاء trigger لتحديث updated_at تلقائياً
    database.exec(`
      CREATE TRIGGER IF NOT EXISTS update_production_orders_timestamp
      AFTER UPDATE ON production_orders
      BEGIN
        UPDATE production_orders SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END
    `)

    // فهارس إضافية لتحسين الأداء
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_orders_status_created ON production_orders (status, created_at)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_orders_product_status ON production_orders (product_id, status)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_orders_department_status ON production_orders (department_id, status)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_orders_priority_status ON production_orders (priority, status)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_orders_dates ON production_orders (start_date, end_date)`)

    // فهارس لجدول وصفات الإنتاج
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_recipes_code ON production_recipes (code)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_recipes_product ON production_recipes (product_id)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_recipes_department ON production_recipes (department_id)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_recipes_active ON production_recipes (is_active)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_recipes_product_active ON production_recipes (product_id, is_active)`)

    // فهارس لجدول مواد الوصفة
    database.exec(`CREATE INDEX IF NOT EXISTS idx_recipe_materials_recipe ON recipe_materials (recipe_id)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_recipe_materials_material ON recipe_materials (material_id)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_recipe_materials_recipe_material ON recipe_materials (recipe_id, material_id)`)

    // فهارس لجدول أقسام الإنتاج
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_departments_code ON production_departments (code)`)
    database.exec(`CREATE INDEX IF NOT EXISTS idx_production_departments_active ON production_departments (is_active)`)

    // جدول عناصر أمر الإنتاج
    database.exec(`
      CREATE TABLE IF NOT EXISTS production_order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        item_id INTEGER NOT NULL,
        quantity DECIMAL(10,2) NOT NULL CHECK (quantity > 0),
        unit TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES production_orders (id) ON DELETE CASCADE,
        FOREIGN KEY (item_id) REFERENCES items (id) ON DELETE RESTRICT
      )
    `)

    // جدول مواد أمر الإنتاج
    database.exec(`
      CREATE TABLE IF NOT EXISTS production_order_materials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        material_id INTEGER NOT NULL,
        quantity DECIMAL(10,2) NOT NULL CHECK (quantity > 0),
        unit TEXT,
        cost_per_unit DECIMAL(10,2) DEFAULT 0,
        total_cost DECIMAL(10,2) DEFAULT 0,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES production_orders (id) ON DELETE CASCADE,
        FOREIGN KEY (material_id) REFERENCES items (id) ON DELETE RESTRICT
      )
    `)

    // جدول مراحل أمر الإنتاج
    database.exec(`
      CREATE TABLE IF NOT EXISTS production_order_stages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        stage_id INTEGER,
        stage_name TEXT NOT NULL,
        sequence_number INTEGER NOT NULL DEFAULT 1,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'skipped')),
        start_time DATETIME,
        end_time DATETIME,
        estimated_duration INTEGER DEFAULT 0,
        actual_duration INTEGER DEFAULT 0,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES production_orders (id) ON DELETE CASCADE,
        FOREIGN KEY (stage_id) REFERENCES production_stages (id) ON DELETE SET NULL
      )
    `)

    // جدول صور أوامر الإنتاج
    database.exec(`
      CREATE TABLE IF NOT EXISTS production_order_images (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        image_name TEXT NOT NULL,
        image_path TEXT NOT NULL,
        file_size INTEGER DEFAULT 0,
        file_type TEXT,
        description TEXT,
        category TEXT DEFAULT 'general' CHECK (category IN ('general', 'material', 'process', 'quality', 'final')),
        is_primary BOOLEAN DEFAULT 0,
        tags TEXT,
        notes TEXT,
        uploaded_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES production_orders (id) ON DELETE CASCADE,
        FOREIGN KEY (uploaded_by) REFERENCES users (id) ON DELETE SET NULL
      )
    `)

    // إنشاء view للاستعلامات المعقدة
    database.exec(`
      CREATE VIEW IF NOT EXISTS production_orders_detailed AS
      SELECT
        po.*,
        i.name as product_name,
        i.code as product_code,
        i.unit as product_unit,
        pd.name as department_name,
        pd.code as department_code,
        c.name as customer_name,
        c.code as customer_code,
        pr.name as recipe_name,
        pr.code as recipe_code,
        w.name as warehouse_name
      FROM production_orders po
      LEFT JOIN items i ON po.product_id = i.id
      LEFT JOIN production_departments pd ON po.department_id = pd.id
      LEFT JOIN customers c ON po.customer_id = c.id
      LEFT JOIN production_recipes pr ON po.recipe_id = pr.id
      LEFT JOIN warehouses w ON po.warehouse_id = w.id
    `)

    // إضافة حقل order_code إذا لم يكن موجوداً
    try {
      // أولاً، إضافة العمود بدون قيد UNIQUE
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('production_orders', 'order_code TEXT')
      Logger.info('ProductionService', 'تم إضافة حقل order_code إلى جدول production_orders')

      // تحديث السجلات الموجودة بقيم فريدة
      const existingOrders = database.prepare('SELECT id FROM production_orders WHERE order_code IS NULL').all()
      for (let i = 0; i < existingOrders.length; i++) {
        const order = existingOrders[i] as any
        const orderCode = 'PO-' + (i + 1).toString().padStart(6, '0')
        database.prepare('UPDATE production_orders SET order_code = ? WHERE id = ?').run(orderCode, order.id)
      }

      // إنشاء فهرس فريد بعد تحديث البيانات
      try {
        database.exec('CREATE UNIQUE INDEX IF NOT EXISTS idx_production_orders_code ON production_orders(order_code)')
        Logger.info('ProductionService', 'تم إنشاء فهرس فريد لحقل order_code')
      } catch (indexError: any) {
        Logger.warn('ProductionService', 'تحذير في إنشاء فهرس order_code:', indexError.message)
      }

    } catch (error: any) {
      if (!error.message.includes('duplicate column name')) {
        Logger.warn('ProductionService', 'خطأ في إضافة حقل order_code:', error.message)
      }
    }

    // جدول وصفات الإنتاج
    database.exec(`
      CREATE TABLE IF NOT EXISTS production_recipes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        product_id INTEGER,
        department_id INTEGER,
        material_warehouse_id INTEGER,
        product_warehouse_id INTEGER,
        estimated_time INTEGER DEFAULT 0,
        estimated_cost DECIMAL(10,2) DEFAULT 0,
        difficulty_level TEXT DEFAULT 'medium' CHECK (difficulty_level IN ('easy', 'medium', 'hard', 'expert')),
        instructions TEXT,
        version INTEGER DEFAULT 1,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES items (id),
        FOREIGN KEY (department_id) REFERENCES production_departments (id),
        FOREIGN KEY (material_warehouse_id) REFERENCES warehouses (id),
        FOREIGN KEY (product_warehouse_id) REFERENCES warehouses (id)
      )
    `)

    // إضافة حقول المخازن للجداول الموجودة (إذا لم تكن موجودة)
    // ملاحظة: هذه الحقول موجودة بالفعل في إنشاء الجدول أعلاه
    // لذلك لا نحتاج لإضافتها مرة أخرى

    // إضافة حقل التكلفة المقدرة للجداول الموجودة (إذا لم يكن موجوداً)
    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('production_recipes', 'estimated_cost DECIMAL(10,2) DEFAULT 0')
    } catch (error) {
      // الحقل موجود بالفعل
    }

    // إضافة حقول المخازن المنفصلة لجدول أوامر الإنتاج (إذا لم تكن موجودة)
    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('production_orders', 'material_warehouse_id INTEGER')
      dbService.safeAddColumn('production_orders', 'product_warehouse_id INTEGER')
    } catch (error) {
      // الحقول موجودة بالفعل
    }

    // جدول المنتجات المركبة (للمنتجات التامة مثل غرفة نوم كلاسيك)
    database.exec(`
      CREATE TABLE IF NOT EXISTS composite_products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        composite_product_id INTEGER NOT NULL,
        component_product_id INTEGER NOT NULL,
        quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
        unit TEXT DEFAULT 'قطعة',
        is_optional BOOLEAN DEFAULT 0,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (composite_product_id) REFERENCES items (id) ON DELETE CASCADE,
        FOREIGN KEY (component_product_id) REFERENCES items (id) ON DELETE CASCADE,
        UNIQUE(composite_product_id, component_product_id)
      )
    `)

    // جدول مواد الوصفة
    database.exec(`
      CREATE TABLE IF NOT EXISTS recipe_materials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        recipe_id INTEGER NOT NULL,
        material_id INTEGER NOT NULL,
        warehouse_id INTEGER,
        quantity DECIMAL(10,2) NOT NULL,
        unit TEXT,
        cost_per_unit DECIMAL(10,2) DEFAULT 0,
        total_cost DECIMAL(10,2) DEFAULT 0,
        is_optional BOOLEAN DEFAULT 0,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (recipe_id) REFERENCES production_recipes (id) ON DELETE CASCADE,
        FOREIGN KEY (material_id) REFERENCES items (id),
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
      )
    `)

    // ملاحظة: warehouse_id موجود بالفعل في إنشاء الجدول أعلاه
    // لذلك لا نحتاج لإضافته مرة أخرى

    // جدول أوامر صرف المواد
    database.exec(`
      CREATE TABLE IF NOT EXISTS material_issue_orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_number TEXT UNIQUE NOT NULL,
        production_order_id INTEGER NOT NULL,
        warehouse_id INTEGER NOT NULL,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'issued', 'cancelled')),
        requested_by INTEGER,
        approved_by INTEGER,
        issued_by INTEGER,
        request_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        approval_date DATETIME,
        issue_date DATETIME,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (production_order_id) REFERENCES production_orders (id) ON DELETE CASCADE,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id),
        FOREIGN KEY (requested_by) REFERENCES users (id),
        FOREIGN KEY (approved_by) REFERENCES users (id),
        FOREIGN KEY (issued_by) REFERENCES users (id)
      )
    `)

    // جدول تفاصيل أوامر صرف المواد
    database.exec(`
      CREATE TABLE IF NOT EXISTS material_issue_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        issue_order_id INTEGER NOT NULL,
        material_id INTEGER NOT NULL,
        requested_quantity DECIMAL(10,2) NOT NULL,
        approved_quantity DECIMAL(10,2),
        issued_quantity DECIMAL(10,2),
        unit_price DECIMAL(10,2) NOT NULL,
        total_cost DECIMAL(10,2) NOT NULL,
        batch_number TEXT,
        expiry_date DATE,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (issue_order_id) REFERENCES material_issue_orders (id) ON DELETE CASCADE,
        FOREIGN KEY (material_id) REFERENCES items (id)
      )
    `)

    // جدول تتبع ساعات العمل
    database.exec(`
      CREATE TABLE IF NOT EXISTS labor_time_tracking (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        production_order_id INTEGER NOT NULL,
        worker_id INTEGER,
        worker_name TEXT NOT NULL,
        department_id INTEGER,
        start_time DATETIME NOT NULL,
        end_time DATETIME,
        actual_hours DECIMAL(10,2),
        hourly_rate DECIMAL(10,2) NOT NULL DEFAULT 0,
        labor_cost DECIMAL(10,2) DEFAULT 0,
        task_description TEXT,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'paused', 'completed', 'cancelled')),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (production_order_id) REFERENCES production_orders (id) ON DELETE CASCADE,
        FOREIGN KEY (worker_id) REFERENCES users (id),
        FOREIGN KEY (department_id) REFERENCES production_departments (id)
      )
    `)

    // جدول إعدادات الإنتاج
    database.exec(`
      CREATE TABLE IF NOT EXISTS production_settings (
        id INTEGER PRIMARY KEY DEFAULT 1,
        overhead_rate REAL DEFAULT 15,
        default_hourly_rate REAL DEFAULT 25,
        auto_update_product_cost INTEGER DEFAULT 1,
        cost_calculation_method TEXT DEFAULT 'average' CHECK (cost_calculation_method IN ('fifo', 'lifo', 'average')),
        auto_create_material_issue INTEGER DEFAULT 1,
        require_approval_for_issue INTEGER DEFAULT 0,
        auto_start_labor_tracking INTEGER DEFAULT 0,
        default_production_priority TEXT DEFAULT 'normal' CHECK (default_production_priority IN ('low', 'normal', 'high', 'urgent')),
        notify_low_materials INTEGER DEFAULT 1,
        notify_production_delays INTEGER DEFAULT 1,
        notify_cost_variance INTEGER DEFAULT 1,
        cost_variance_threshold REAL DEFAULT 10,
        auto_generate_daily_reports INTEGER DEFAULT 0,
        include_labor_details INTEGER DEFAULT 1,
        report_format TEXT DEFAULT 'pdf' CHECK (report_format IN ('pdf', 'excel', 'both')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // جدول مراحل الإنتاج
    database.exec(`
      CREATE TABLE IF NOT EXISTS production_stages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        department_id INTEGER,
        sequence INTEGER NOT NULL DEFAULT 1,
        estimated_hours DECIMAL(10,2) DEFAULT 0,
        labor_cost_per_hour DECIMAL(10,2) DEFAULT 0,
        required_skills TEXT,
        equipment_needed TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (department_id) REFERENCES production_departments (id)
      )
    `)

    // إضافة الأعمدة الجديدة إذا لم تكن موجودة
    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('production_stages', 'department_id INTEGER')
    } catch {
      // العمود موجود بالفعل
    }

    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('production_stages', 'estimated_hours DECIMAL(10,2) DEFAULT 0')
    } catch {
      // العمود موجود بالفعل
    }

    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('production_stages', 'labor_cost_per_hour DECIMAL(10,2) DEFAULT 0')
    } catch {
      // العمود موجود بالفعل
    }

    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('production_stages', 'required_skills TEXT')
    } catch {
      // العمود موجود بالفعل
    }

    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('production_stages', 'equipment_needed TEXT')
    } catch {
      // العمود موجود بالفعل
    }

    // تحديث اسم العمود sequence_order إلى sequence إذا لزم الأمر
    try {
      const dbService = DatabaseService.getInstance()
      if (dbService.columnExists('production_stages', 'sequence_order') && !dbService.columnExists('production_stages', 'sequence')) {
        database.exec(`ALTER TABLE production_stages RENAME COLUMN sequence_order TO sequence`)
      }
    } catch {
      // العمود تم تحديثه بالفعل أو لا يحتاج تحديث
    }

    // جدول مراحل أوامر الإنتاج
    database.exec(`
      CREATE TABLE IF NOT EXISTS production_order_stages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        stage_id INTEGER NOT NULL,
        start_time DATETIME,
        end_time DATETIME,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'skipped')),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES production_orders (id) ON DELETE CASCADE,
        FOREIGN KEY (stage_id) REFERENCES production_stages (id)
      )
    `)

    // جدول أنواع الدهانات
    database.exec(`
      CREATE TABLE IF NOT EXISTS paint_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        price_per_sqm DECIMAL(10,2) NOT NULL,
        cost_per_sqm DECIMAL(10,2) DEFAULT 0,
        color_options TEXT,
        coverage_per_liter DECIMAL(10,2),
        drying_time INTEGER,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // جدول أوامر الدهان
    database.exec(`
      CREATE TABLE IF NOT EXISTS paint_orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_number TEXT UNIQUE NOT NULL,
        paint_type_id INTEGER NOT NULL,
        customer_id INTEGER,
        area_sqm DECIMAL(10,2) NOT NULL,
        color TEXT NOT NULL,
        quantity_liters DECIMAL(10,2),
        unit_price DECIMAL(10,2),
        total_price DECIMAL(10,2),
        delivery_date DATE,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (paint_type_id) REFERENCES paint_types (id),
        FOREIGN KEY (customer_id) REFERENCES customers (id)
      )
    `)

    // جدول فواتير الدهان
    database.exec(`
      CREATE TABLE IF NOT EXISTS paint_invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT UNIQUE NOT NULL,
        order_id INTEGER,
        customer_id INTEGER NOT NULL,
        invoice_date DATE NOT NULL,
        due_date DATE,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'partial', 'overdue', 'cancelled')),
        total_amount DECIMAL(10,2) DEFAULT 0,
        discount DECIMAL(10,2) DEFAULT 0,
        tax DECIMAL(10,2) DEFAULT 0,
        final_amount DECIMAL(10,2) DEFAULT 0,
        paid_amount DECIMAL(10,2) DEFAULT 0,
        remaining_amount DECIMAL(10,2) DEFAULT 0,
        payment_method TEXT,
        payment_date DATE,
        total_area DECIMAL(10,2) DEFAULT 0,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (order_id) REFERENCES paint_orders (id),
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // جدول تفاصيل فواتير الدهان
    database.exec(`
      CREATE TABLE IF NOT EXISTS paint_invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        paint_order_id INTEGER NOT NULL,
        area_sqm DECIMAL(10,2) NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        color TEXT,
        paint_type_id INTEGER,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (invoice_id) REFERENCES paint_invoices (id) ON DELETE CASCADE,
        FOREIGN KEY (paint_order_id) REFERENCES paint_orders (id),
        FOREIGN KEY (paint_type_id) REFERENCES paint_types (id)
      )
    `)

    // إنشاء الفهارس
    database.exec('CREATE INDEX IF NOT EXISTS idx_production_departments_active ON production_departments(is_active)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_production_orders_status ON production_orders(status)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_production_orders_product ON production_orders(product_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_production_orders_customer ON production_orders(customer_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_production_orders_priority ON production_orders(priority)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_production_orders_estimated_cost ON production_orders(estimated_cost)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_production_recipes_active ON production_recipes(is_active)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_production_recipes_department ON production_recipes(department_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_production_recipes_difficulty ON production_recipes(difficulty_level)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_production_recipes_time ON production_recipes(estimated_time)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_recipe_materials_recipe ON recipe_materials(recipe_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_production_stages_active ON production_stages(is_active)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_production_stages_order ON production_stages(sequence_order)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_paint_types_code ON paint_types(code)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_paint_types_active ON paint_types(is_active)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_paint_orders_paint_type ON paint_orders(paint_type_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_paint_orders_customer ON paint_orders(customer_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_paint_orders_status ON paint_orders(status)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_paint_invoices_customer ON paint_invoices(customer_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_paint_invoices_date ON paint_invoices(invoice_date)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_paint_invoices_status ON paint_invoices(status)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_paint_invoice_items_invoice ON paint_invoice_items(invoice_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_paint_invoice_items_order ON paint_invoice_items(paint_order_id)')

    // إضافة الأعمدة الجديدة إذا لم تكن موجودة
    this.addMissingColumns()
  }

  // إضافة الأعمدة المفقودة
  private addMissingColumns(): void {
    try {
      // إضافة cost_per_sqm إلى paint_types إذا لم يكن موجوداً
      try {
        const dbService = DatabaseService.getInstance()
        dbService.safeAddColumn('paint_types', 'cost_per_sqm DECIMAL(10,2) DEFAULT 0')
        Logger.info('ProductionService', 'تم إضافة عمود cost_per_sqm إلى جدول paint_types')
      } catch (error: any) {
        Logger.warn('ProductionService', 'خطأ في إضافة عمود cost_per_sqm:', error.message)
      }

      // إضافة payment_date إلى paint_invoices إذا لم يكن موجوداً
      try {
        const dbService = DatabaseService.getInstance()
        dbService.safeAddColumn('paint_invoices', 'payment_date DATE')
        Logger.info('ProductionService', 'تم إضافة عمود payment_date إلى جدول paint_invoices')
      } catch (error: any) {
        Logger.warn('ProductionService', 'خطأ في إضافة عمود payment_date:', error.message)
      }

      // إضافة total_area إلى paint_invoices إذا لم يكن موجوداً
      try {
        const dbService = DatabaseService.getInstance()
        dbService.safeAddColumn('paint_invoices', 'total_area DECIMAL(10,2) DEFAULT 0')
        Logger.info('ProductionService', 'تم إضافة عمود total_area إلى جدول paint_invoices')
      } catch (error: any) {
        Logger.warn('ProductionService', 'خطأ في إضافة عمود total_area:', error.message)
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في إضافة الأعمدة المفقودة:', error)
    }
  }

  // الحصول على أقسام الإنتاج
  public async getProductionDepartments(): Promise<ProductionDepartment[]> {
    try {
      const departments = this.db.prepare(`
        SELECT pd.*, e.name as supervisor_name
        FROM production_departments pd
        LEFT JOIN employees e ON pd.supervisor_id = e.id
        WHERE pd.is_active = 1
        ORDER BY pd.name
      `).all() as ProductionDepartment[]

      return departments
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب أقسام الإنتاج:', error)
      return []
    }
  }

  // إنشاء قسم إنتاج جديد مع تحقق من الدور
  public async createProductionDepartment(departmentData: {
    code?: string
    name: string
    description?: string
    supervisor_id?: number
  }, userRole?: string): Promise<ApiResponse> {
    try {
      if (userRole !== 'admin' && userRole !== 'production_manager') {
        return { success: false, message: 'ليس لديك صلاحية إنشاء قسم إنتاج. فقط المدير أو مدير الإنتاج يمكنه ذلك.' }
      }
      if (!departmentData.name || departmentData.name.trim().length < 2) {
        return { success: false, message: 'اسم القسم مطلوب ويجب أن يكون أكثر من حرفين.' }
      }
      // توليد كود القسم إذا لم يتم توفيره
      if (!departmentData.code) {
        departmentData.code = await this.generateDepartmentCode()
      }
      const result = this.db.prepare(`
        INSERT INTO production_departments (code, name, description, supervisor_id)
        VALUES (?, ?, ?, ?)
      `).run(
        departmentData.code,
        departmentData.name,
        departmentData.description || null,
        departmentData.supervisor_id || null
      )
      if (result.changes > 0) {
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إنشاء قسم الإنتاج بنجاح' }
      } else {
        return { success: false, message: 'فشل في إنشاء قسم الإنتاج' }
      }
    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في إنشاء قسم الإنتاج:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'اسم أو كود القسم موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في إنشاء قسم الإنتاج' }
    }
  }

  // إعادة تفعيل قسم إنتاج
  public async reactivateProductionDepartment(departmentId: number, userRole?: string): Promise<ApiResponse> {
    try {
      if (userRole !== 'admin' && userRole !== 'production_manager') {
        return { success: false, message: 'ليس لديك ��لاحية إعادة تفعيل قسم إنتاج.' }
      }
      const result = this.db.prepare(`
        UPDATE production_departments SET is_active = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?
      `).run(departmentId)
      if (result.changes > 0) {
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إعادة تفعيل قسم الإنتاج بنجاح' }
      } else {
        return { success: false, message: 'فشل في إعادة تفعيل قسم الإنتاج' }
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في إعادة تفعيل قسم الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في إعادة تفعيل قسم الإنتاج' }
    }
  }

  // حذف قسم إنتاج مع خيار الحذف الفعلي
  public async deleteProductionDepartment(departmentId: number, userRole?: string, forceDelete?: boolean): Promise<ApiResponse> {
    try {
      if (userRole !== 'admin' && userRole !== 'production_manager') {
        return { success: false, message: 'ليس لديك صلاحية حذف قسم إنتاج.' }
      }
      const existingDepartment = this.db.prepare(`
        SELECT * FROM production_departments WHERE id = ?
      `).get(departmentId) as any
      if (!existingDepartment) {
        return { success: false, message: 'قسم الإنتاج غير موجود' }
      }
      const ordersCount = this.db.prepare(`
        SELECT COUNT(*) as count FROM production_orders
        WHERE department_id = ? AND status IN ('pending', 'in_progress')
      `).get(departmentId) as any
      if (ordersCount.count > 0) {
        return {
          success: false,
          message: 'لا يمكن حذف القسم لوجود أوامر إنتاج نشطة مرتبطة به. يمكنك إلغاء تفعيله بدلاً من ذلك.'
        }
      }
      const recipesCount = this.db.prepare(`
        SELECT COUNT(*) as count FROM production_recipes
        WHERE department_id = ? AND is_active = 1
      `).get(departmentId) as any
      if (recipesCount.count > 0) {
        return {
          success: false,
          message: 'لا يمكن حذف القسم لوجود وصفات إنتاج نشطة مرتبطة به. يمكنك إلغاء تفعيله بدلاً من ذلك.'
        }
      }
      if (forceDelete) {
        const result = this.db.prepare(`
          DELETE FROM production_departments WHERE id = ?
        `).run(departmentId)
        if (result.changes > 0) {
          DatabaseService.getInstance().saveDatabase()
          return { success: true, message: 'تم حذف قسم الإنتاج نهائيًا' }
        } else {
          return { success: false, message: 'فشل في حذف قسم الإنتاج نهائيًا' }
        }
      } else {
        const result = this.db.prepare(`
          UPDATE production_departments
          SET is_active = 0, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).run(departmentId)
        if (result.changes > 0) {
          DatabaseService.getInstance().saveDatabase()
          return { success: true, message: 'تم إلغاء تفعيل قسم الإنتاج بنجاح' }
        } else {
          return { success: false, message: 'فشل في حذف قسم الإنتاج' }
        }
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في حذف قسم الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في حذف قسم الإنتاج' }
    }
  }

  // تحديث قسم إنتاج
  public async updateProductionDepartment(departmentId: number, departmentData: {
    code?: string
    name?: string
    description?: string
    supervisor_id?: number
    is_active?: boolean
  }, userRole?: string): Promise<ApiResponse> {
    try {
      if (userRole !== 'admin' && userRole !== 'production_manager') {
        return { success: false, message: 'ليس لديك صلاحية تحديث قسم إنتاج. فقط المدير أو مدير الإنتاج يمكنه ذلك.' }
      }
      // التحقق من وجود القسم
      const existingDepartment = this.db.prepare(`
        SELECT * FROM production_departments WHERE id = ?
      `).get(departmentId) as any

      if (!existingDepartment) {
        return { success: false, message: 'قسم الإنتاج غير موجود' }
      }

      // بناء استعلام التحديث الديناميكي
      const updateFields: string[] = []
      const values: any[] = []

      if (departmentData.code !== undefined) {
        // التحقق من عدم تكرار الكود (باستثناء القسم الحالي)
        const existingCode = this.db.prepare(`
          SELECT id FROM production_departments WHERE code = ? AND id != ?
        `).get(departmentData.code, departmentId)

        if (existingCode) {
          return { success: false, message: 'كود القسم موجود مسبقاً' }
        }

        updateFields.push('code = ?')
        values.push(departmentData.code)
      }

      if (departmentData.name !== undefined) {
        // التحقق من عدم تكرار الاسم (باستثناء القسم الحالي)
        const existingName = this.db.prepare(`
          SELECT id FROM production_departments WHERE name = ? AND id != ?
        `).get(departmentData.name, departmentId)

        if (existingName) {
          return { success: false, message: 'اسم القسم موجود مسبقاً' }
        }

        updateFields.push('name = ?')
        values.push(departmentData.name)
      }

      if (departmentData.description !== undefined) {
        updateFields.push('description = ?')
        values.push(departmentData.description || null)
      }

      if (departmentData.supervisor_id !== undefined) {
        updateFields.push('supervisor_id = ?')
        values.push(departmentData.supervisor_id || null)
      }

      if (departmentData.is_active !== undefined) {
        updateFields.push('is_active = ?')
        values.push(departmentData.is_active ? 1 : 0)
      }

      if (updateFields.length === 0) {
        return { success: false, message: 'لا توجد بيانات للتحديث' }
      }

      // إضافة تاريخ التحديث
      updateFields.push('updated_at = CURRENT_TIMESTAMP')
      values.push(departmentId)

      // تنفيذ التحديث
      const result = this.db.prepare(
        'UPDATE production_departments SET ' + updateFields.join(', ') + ' WHERE id = ?'
      ).run(...values)

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث قسم الإنتاج بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث قسم الإنتاج' }
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تحديث قسم الإنتاج:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'اسم أو كود القسم موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في تحديث قسم الإنتاج' }
    }
  }

  // (تم حذف التعريف المكرر لدالة deleteProductionDepartment)

  // جلب تفاصيل أمر إنتاج واحد
  public async getProductionOrderDetails(orderId: number): Promise<any> {
    try {
      const order = this.db.prepare(`
        SELECT
          po.*,
          i.name as item_name,
          i.code as item_code,
          i.unit as unit,
          pd.name as department_name,
          c.name as customer_name,
          pr.name as recipe_name,
          pr.code as recipe_code,
          pr.description as recipe_description
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        LEFT JOIN production_departments pd ON po.department_id = pd.id
        LEFT JOIN customers c ON po.customer_id = c.id
        LEFT JOIN production_recipes pr ON po.recipe_id = pr.id
        WHERE po.id = ?
      `).get(orderId)

      if (!order) {
        throw new Error('أمر الإنتاج غير موجود')
      }

      return order
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب تفاصيل أمر الإنتاج:', error)
      throw error
    }
  }

  // الحصول على أوامر الإنتاج - محسن مع أسماء الحقول المتطابقة
  public async getProductionOrders(): Promise<any[]> {
    try {
      const orders = this.db.prepare(`
        SELECT
          po.id,
          po.order_number,
          po.product_id as item_id,
          i.name as item_name,
          i.code as item_code,
          i.unit,
          po.quantity,
          po.department_id,
          pd.name as department_name,
          po.customer_id,
          c.name as customer_name,
          po.start_date,
          po.end_date,
          po.completion_date as actual_completion_date,
          po.start_date as order_date,
          po.end_date as expected_completion_date,
          po.status,
          po.priority,
          po.estimated_cost,
          po.actual_cost,
          po.estimated_hours,
          po.actual_hours,
          po.notes,
          po.created_at,
          'مدير النّام' as created_by_name
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        LEFT JOIN production_departments pd ON po.department_id = pd.id
        LEFT JOIN customers c ON po.customer_id = c.id
        ORDER BY po.created_at DESC
      `).all()

      Logger.info('ProductionService', `✅ تم جلب ${orders.length} أمر إنتاج من قاعدة البيانات`)
      return orders
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب أوامر الإنتاج:', error)
      return []
    }
  }

  // إنشاء أمر إنتاج جديد - محسن مع أكواد الأخطاء
  public async createProductionOrder(orderData: {
    order_code?: string
    product_id: number
    recipe_id?: number
    quantity: number
    department_id?: number
    customer_id?: number
    // تم إزالة warehouse_id - سيتم تحديد المخازن من الوصفة
    start_date?: string
    end_date?: string
    priority?: string
    estimated_cost?: number
    estimated_hours?: number
    notes?: string
  }, userId?: number): Promise<ApiResponse> {
    try {
      // التحقق من صحة البيانات الأساسية
      if (!orderData.product_id || orderData.product_id <= 0) {
        return createErrorResponse(
          ProductionErrorCodes.PRODUCTION_ORDER.INVALID_PRODUCT_ID,
          { product_id: orderData.product_id },
          ['تأكد من اختيار منتج صحيح من القائمة']
        )
      }

      if (!orderData.quantity || orderData.quantity <= 0) {
        return createErrorResponse(
          ProductionErrorCodes.PRODUCTION_ORDER.INVALID_QUANTITY,
          { quantity: orderData.quantity },
          ['أدخل كمية صحيحة أكبر من الصفر']
        )
      }

      // التحقق من الحد الأقصى للكمية
      if (orderData.quantity > 1000000) {
        return createErrorResponse(
          ProductionErrorCodes.PRODUCTION_ORDER.INVALID_QUANTITY,
          { quantity: orderData.quantity, max_allowed: 1000000 },
          ['قلل الكمية أو قسم الأمر إلى أوامر متعددة']
        )
      }

      // التحقق من وجود المنتج
      const product = this.db.prepare('SELECT id, is_active, name FROM items WHERE id = ?').get(orderData.product_id) as any
      if (!product) {
        return createErrorResponse(
          ProductionErrorCodes.PRODUCTION_ORDER.INVALID_PRODUCT_ID,
          { product_id: orderData.product_id },
          ['تأكد من اختيار منتج موجود في النّام']
        )
      }
      if (!product.is_active) {
        return createErrorResponse(
          ProductionErrorCodes.PRODUCTION_ORDER.INVALID_PRODUCT_ID,
          { product_id: orderData.product_id, product_name: product.name },
          ['اختر منتج نشط أو قم بتفعيل هذا المنتج أولاً']
        )
      }

      // التحقق من وجود القسم إذا تم تحديده
      if (orderData.department_id) {
        const department = this.db.prepare('SELECT id, is_active FROM production_departments WHERE id = ?').get(orderData.department_id) as any
        if (!department) {
          return { success: false, message: 'قسم الإنتاج غير موجود' }
        }
        if (!department.is_active) {
          return { success: false, message: 'قسم الإنتاج غير نشط' }
        }
      }

      // التحقق من وجود العميل إذا تم تحديده
      if (orderData.customer_id) {
        const customer = this.db.prepare('SELECT id, is_active FROM customers WHERE id = ?').get(orderData.customer_id) as any
        if (!customer) {
          return { success: false, message: 'العميل غير موجود' }
        }
        if (!customer.is_active) {
          return { success: false, message: 'العميل غير نشط' }
        }
      }

      // الحصول على معلومات الوصفة إذا تم تحديدها
      let materialWarehouseId = null
      let productWarehouseId = null

      if (orderData.recipe_id) {
        const recipe = this.db.prepare(`
          SELECT material_warehouse_id, product_warehouse_id
          FROM production_recipes
          WHERE id = ? AND is_active = 1
        `).get(orderData.recipe_id) as any

        if (recipe) {
          materialWarehouseId = recipe.material_warehouse_id
          productWarehouseId = recipe.product_warehouse_id
        }
      }

      // توليد رقم أمر الإنتاج
      const orderNumber = await this.generateOrderNumber()

      // توليد كود أمر الإنتاج إذا لم يتم توفيره
      const orderCode = orderData.order_code || await this.generateOrderCode()

      const result = this.db.prepare(`
        INSERT INTO production_orders (
          order_number, order_code, product_id, recipe_id, quantity, department_id, customer_id,
          material_warehouse_id, product_warehouse_id, start_date, end_date, priority,
          estimated_cost, estimated_hours, notes, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        orderNumber,
        orderCode,
        orderData.product_id,
        orderData.recipe_id || null,
        orderData.quantity,
        orderData.department_id || null,
        orderData.customer_id || null,
        materialWarehouseId,
        productWarehouseId,
        orderData.start_date || null,
        orderData.end_date || null,
        orderData.priority || 'normal',
        orderData.estimated_cost || 0,
        orderData.estimated_hours || 0,
        orderData.notes || null,
        userId || null
      )

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return {
          success: true,
          message: 'تم إنشاء أمر الإنتاج بنجاح',
          data: { orderNumber }
        }
      } else {
        return { success: false, message: 'فشل في إنشاء أمر الإنتاج' }
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في إنشاء أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في إنشاء أمر الإنتاج' }
    }
  }

  // الحصول على وصفات الإنتاج
  public async getProductionRecipes(): Promise<ProductionRecipe[]> {
    try {
      const recipes = this.db.prepare(`
        SELECT pr.*,
               i.name as item_name,
               i.code as item_code,
               i.id as item_id,
               pd.name as department_name,
               mw.name as material_warehouse_name,
               pw.name as product_warehouse_name
        FROM production_recipes pr
        LEFT JOIN items i ON pr.product_id = i.id
        LEFT JOIN production_departments pd ON pr.department_id = pd.id
        LEFT JOIN warehouses mw ON pr.material_warehouse_id = mw.id
        LEFT JOIN warehouses pw ON pr.product_warehouse_id = pw.id
        WHERE pr.is_active = 1
        ORDER BY pr.name
      `).all() as ProductionRecipe[]

      return recipes
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب وصفات الإنتاج:', error)
      return []
    }
  }

  // إنشاء وصفة إنتاج جديدة
  public async createProductionRecipe(recipeData: {
    code?: string
    name: string
    description?: string
    product_id?: number
    item_id?: number // إضافة دعم لـ item_id
    department_id?: number
    material_warehouse_id?: number
    product_warehouse_id?: number
    estimated_time?: number
    difficulty_level?: string
    version?: number
    instructions?: string
    materials?: { material_id: number; warehouse_id?: number; quantity: number; unit?: string; cost_per_unit?: number; is_optional?: boolean; notes?: string }[]
  }): Promise<ApiResponse> {
    try {
      // تسجيل البيانات المستلمة للتشخيص
      Logger.info('ProductionService', 'بيانات الوصفة المستلمة:', JSON.stringify(recipeData, null, 2))

      // التحقق من صحة البيانات الأساسية
      if (!recipeData.name || recipeData.name.trim().length === 0) {
        return { success: false, message: 'اسم الوصفة مطلوب' }
      }

      // التحقق من وجود معرف المنتج (item_id أو product_id)
      const productId = recipeData.item_id || recipeData.product_id
      Logger.info('ProductionService', 'معرف المنتج المستخرج:', productId)
      Logger.info('ProductionService', 'item_id:', recipeData.item_id)
      Logger.info('ProductionService', 'product_id:', recipeData.product_id)

      if (!productId) {
        Logger.error('ProductionService', 'معرف المنتج مفقود في البيانات')
        return { success: false, message: 'معرف المنتج مطلوب' }
      }

      // التحقق من وجود المنتج في قاعدة البيانات
      const product = this.db.prepare('SELECT id, is_active, name FROM items WHERE id = ?').get(productId) as any
      if (!product) {
        return { success: false, message: 'المنتج المحدد غير موجود' }
      }
      if (!product.is_active) {
        return { success: false, message: 'المنتج المحدد غير نشط' }
      }

      // التحقق من وجود القسم إذا تم تحديده
      if (recipeData.department_id) {
        const department = this.db.prepare('SELECT id, is_active FROM production_departments WHERE id = ?').get(recipeData.department_id) as any
        if (!department) {
          return { success: false, message: 'قسم الإنتاج غير موجود' }
        }
        if (!department.is_active) {
          return { success: false, message: 'قسم الإنتاج غير نشط' }
        }
      }



      // التحقق من وجود مخزن المنتج التام إذا تم تحديده
      if (recipeData.product_warehouse_id) {
        const warehouse = this.db.prepare('SELECT id, is_active FROM warehouses WHERE id = ?').get(recipeData.product_warehouse_id) as any
        if (!warehouse) {
          return { success: false, message: 'مخزن المنتج التام غير موجود' }
        }
        if (!warehouse.is_active) {
          return { success: false, message: 'مخزن المنتج التام غير نشط' }
        }
      }

      // توليد كود الوصفة إذا لم يتم توفيره
      if (!recipeData.code) {
        recipeData.code = await this.generateRecipeCode()
      }

      const result = this.db.prepare(`
        INSERT INTO production_recipes (
          code, name, description, product_id, department_id,
          product_warehouse_id,
          estimated_time, difficulty_level, version, instructions
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        recipeData.code,
        recipeData.name,
        recipeData.description || null,
        productId, // استخدام المعرف المحدد
        recipeData.department_id || null,
        recipeData.product_warehouse_id || null,
        recipeData.estimated_time || 0,
        recipeData.difficulty_level || 'medium',
        recipeData.version || 1,
        recipeData.instructions || null
      )

      const recipeId = result.lastInsertRowid

      // حساب التكلفة المقدرة من المواد
      let estimatedCost = 0

      // إضافة المواد إذا كانت موجودة
      if (recipeData.materials && recipeData.materials.length > 0) {
        for (const material of recipeData.materials) {
          const totalCost = (material.cost_per_unit || 0) * material.quantity
          estimatedCost += totalCost

          this.db.prepare(`
            INSERT INTO recipe_materials (
              recipe_id, material_id, warehouse_id, quantity, unit, cost_per_unit,
              total_cost, is_optional, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `).run(
            recipeId,
            material.material_id,
            material.warehouse_id || null,
            material.quantity,
            material.unit || null,
            material.cost_per_unit || 0,
            totalCost,
            material.is_optional || false,
            material.notes || null
          )
        }
      }

      // تحديث التكلفة المقدرة في الوصفة
      this.db.prepare(`
        UPDATE production_recipes
        SET estimated_cost = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).run(estimatedCost, recipeId)

      // تحديث تكلفة المنتج النهائي تلقائياً
      const recipe = this.db.prepare('SELECT product_id FROM production_recipes WHERE id = ?').get(recipeId) as any
      if (recipe?.product_id) {
        await this.updateProductCostFromRecipes(recipe.product_id)
      }

      // تحديث تكلفة المنتج النهائي تلقائياً
      if (productId) {
        await this.updateProductCostFromRecipes(productId)
      }

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إنشاء وصفة الإنتاج بنجاح' }
      } else {
        return { success: false, message: 'فشل في إنشاء وصفة الإنتاج' }
      }
    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في إنشاء وصفة الإنتاج:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود الوصفة موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في إنشاء وصفة الإنتاج' }
    }
  }

  // الحصول على مراحل الإنتاج
  public async getProductionStages(): Promise<ProductionStage[]> {
    try {
      const stages = this.db.prepare(`
        SELECT ps.*, pd.name as department_name
        FROM production_stages ps
        LEFT JOIN production_departments pd ON ps.department_id = pd.id
        WHERE ps.is_active = 1
        ORDER BY ps.sequence, ps.name
      `).all() as ProductionStage[]

      return stages
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب مراحل الإنتاج:', error)
      return []
    }
  }

  // إنشاء مرحلة إنتاج جديدة
  public async createProductionStage(stageData: any): Promise<ApiResponse> {
    try {
      // التحقق من عدم تكرار الكود
      const existingStage = this.db.prepare('SELECT id FROM production_stages WHERE code = ?').get([stageData.code])
      if (existingStage) {
        return { success: false, message: 'كود مرحلة الإنتاج موجود بالفعل' }
      }

      const result = this.db.prepare(`
        INSERT INTO production_stages (
          code, name, description, department_id, sequence,
          estimated_hours, labor_cost_per_hour, required_skills,
          equipment_needed, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run([
        stageData.code,
        stageData.name,
        stageData.description || null,
        stageData.department_id || null,
        stageData.sequence || 1,
        stageData.estimated_hours || 0,
        stageData.labor_cost_per_hour || 0,
        stageData.required_skills || null,
        stageData.equipment_needed || null,
        stageData.is_active ? 1 : 0
      ])

      if (result.changes > 0) {
        DatabaseService.getInstance().saveDatabase()
        return {
          success: true,
          message: 'تم إنشاء مرحلة الإنتاج بنجاح',
          data: { id: result.lastInsertRowid }
        }
      } else {
        return { success: false, message: 'فشل في إنشاء مرحلة الإنتاج' }
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في إنشاء مرحلة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في إنشاء مرحلة الإنتاج' }
    }
  }

  // تحديث مرحلة إنتاج
  public async updateProductionStage(stageId: number, stageData: any): Promise<ApiResponse> {
    try {
      // التحقق من عدم تكرار الكود (باستثناء المرحلة الحالية)
      const existingStage = this.db.prepare('SELECT id FROM production_stages WHERE code = ? AND id != ?').get([stageData.code, stageId])
      if (existingStage) {
        return { success: false, message: 'كود مرحلة الإنتاج موجود بالفعل' }
      }

      const result = this.db.prepare(`
        UPDATE production_stages SET
          code = ?, name = ?, description = ?, department_id = ?,
          sequence = ?, estimated_hours = ?, labor_cost_per_hour = ?,
          required_skills = ?, equipment_needed = ?, is_active = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).run([
        stageData.code,
        stageData.name,
        stageData.description || null,
        stageData.department_id || null,
        stageData.sequence || 1,
        stageData.estimated_hours || 0,
        stageData.labor_cost_per_hour || 0,
        stageData.required_skills || null,
        stageData.equipment_needed || null,
        stageData.is_active ? 1 : 0,
        stageId
      ])

      if (result.changes > 0) {
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث مرحلة الإنتاج بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث مرحلة الإنتاج' }
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في تحديث مرحلة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تحديث مرحلة الإنتاج' }
    }
  }

  // توليد كود قسم إنتاج جديد
  public async generateDepartmentCode(): Promise<string> {
    try {
      const lastDepartment = this.db.prepare(`
        SELECT code FROM production_departments
        WHERE code LIKE 'DEPT%' AND code IS NOT NULL AND code != ''
        ORDER BY CAST(SUBSTR(code, 5) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastDepartment && lastDepartment.code && lastDepartment.code.length >= 8) {
        const codeNumber = lastDepartment.code.substring(4)
        const lastNumber = parseInt(codeNumber)

        if (!isNaN(lastNumber)) {
          const newNumber = lastNumber + 1
          return 'DEPT' + newNumber.toString().padStart(3, '0')
        }
      }

      return 'DEPT001'
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في توليد كود القسم:', error)
      return 'DEPT' + Date.now().toString().slice(-3)
    }
  }

  // جلب أصناف أمر الإنتاج للطباعة
  public async getProductionOrderItems(orderId: number): Promise<any[]> {
    try {
      const items = this.db.prepare(`
        SELECT poi.*, i.name as item_name, i.code as item_code, i.unit
        FROM production_order_items poi
        LEFT JOIN items i ON poi.item_id = i.id
        WHERE poi.order_id = ?
        ORDER BY poi.id
      `).all(orderId) as any[]

      return items
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب أصناف أمر الإنتاج:', error)
      return []
    }
  }

  // جلب صور أمر الإنتاج
  public async getProductionOrderImages(orderId: number): Promise<any[]> {
    try {
      // استخدام DatabaseService بدلاً من SQLite المباشر
      const DatabaseService = require('./DatabaseService').DatabaseService
      const databaseService = DatabaseService.getInstance()
      const db = databaseService.getDatabase()

      if (!db) {
        Logger.warn('ProductionService', 'قاعدة البيانات غير متاحة')
        return []
      }

      const query = `
        SELECT * FROM production_order_images
        WHERE order_id = ?
        ORDER BY is_primary DESC, created_at DESC
      `

      const result = db.exec(query, [orderId])
      let images: any[] = []

      if (result && result.length > 0) {
        const columns = result[0].columns
        const values = result[0].values
        images = values.map((row: any[]) => {
          const obj: any = {}
          columns.forEach((col: string, index: number) => {
            obj[col] = row[index]
          })
          return obj
        })
      }

      // تحويل الصور إلى base64 للعرض
      const fs = require('fs')
      const processedImages = images.map((image: any) => {
        try {
          if (!image.image_path) {
            return {
              ...image,
              image_path: null,
              original_path: image.image_path,
              error: 'مسار الصورة غير محدد'
            }
          }

          if (fs.existsSync(image.image_path)) {
            const imageBuffer = fs.readFileSync(image.image_path)
            const mimeType = this.getMimeType(require('path').extname(image.image_path))

            if (!mimeType) {
              Logger.warn('ProductionService', `نوع ملف غير مدعوم: ${image.image_path}`)
              return {
                ...image,
                image_path: null,
                original_path: image.image_path,
                error: 'نوع الملف غير مدعوم'
              }
            }

            const base64Data = imageBuffer.toString('base64')
            const dataUrl = `data:${mimeType};base64,${base64Data}`

            Logger.info('ProductionService', `تم تحويل صورة أمر الإنتاج بنجاح: ${image.image_name}`)

            return {
              ...image,
              image_path: dataUrl,
              original_path: image.image_path,
              file_size: imageBuffer.length
            }
          } else {
            Logger.warn('ProductionService', `صورة أمر الإنتاج غير موجودة: ${image.image_path}`)
            return {
              ...image,
              image_path: null,
              original_path: image.image_path,
              error: 'الصورة غير موجودة'
            }
          }
        } catch (error) {
          Logger.error('ProductionService', `خطأ في معالجة صورة أمر الإنتاج ${image.image_name}:`, error)
          return {
            ...image,
            image_path: null,
            original_path: image.image_path,
            error: 'خطأ في معالجة الصورة'
          }
        }
      })

      Logger.info('ProductionService', `تم جلب ${processedImages.length} صورة لأمر الإنتاج ${orderId}`)
      return processedImages

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب صور أمر الإنتاج:', error)
      return []
    }
  }

  // دالة مساعدة لتحديد نوع MIME للصورة
  private getMimeType(extension: string): string | null {
    const mimeTypes: { [key: string]: string } = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml'
    }

    return mimeTypes[extension.toLowerCase()] || null
  }

  // إضافة صورة لأمر الإنتاج
  public async addProductionOrderImage(imageData: {
    order_id: number
    image_name: string
    image_path: string
    file_size?: number
    file_type?: string
    description?: string
    category?: string
    is_primary?: boolean
    tags?: string
    notes?: string
    uploaded_by?: number
  }): Promise<number> {
    try {
      // استخدام DatabaseService بدلاً من SQLite المباشر
      const DatabaseService = require('./DatabaseService').DatabaseService
      const databaseService = DatabaseService.getInstance()
      const db = databaseService.getDatabase()

      if (!db) {
        throw new Error('قاعدة البيانات غير متاحة')
      }

      // التأكد من وجود جدول production_order_images
      try {
        const { SimpleDatabaseService } = await import('./SimpleDatabaseService')
        const simpleDatabaseService = SimpleDatabaseService.getInstance()
        const simpleDb = simpleDatabaseService.getDatabase()
        simpleDb.run(`
          CREATE TABLE IF NOT EXISTS production_order_images (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            image_name TEXT NOT NULL,
            image_path TEXT NOT NULL,
            file_size INTEGER DEFAULT 0,
            file_type TEXT,
            description TEXT,
            category TEXT DEFAULT 'general' CHECK (category IN ('general', 'material', 'process', 'quality', 'final')),
            is_primary BOOLEAN DEFAULT 0,
            tags TEXT,
            notes TEXT,
            uploaded_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `)
      } catch (createError) {
        Logger.warn('ProductionService', 'تحذير في إنشاء جدول الصور:', createError)
      }

      // إدراج الصورة
      const query = `
        INSERT INTO production_order_images (
          order_id, image_name, image_path, file_size, file_type,
          description, category, is_primary, tags, notes, uploaded_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `

      const params = [
        imageData.order_id,
        imageData.image_name,
        imageData.image_path,
        imageData.file_size || 0,
        imageData.file_type || 'image/jpeg',
        imageData.description || null,
        imageData.category || 'general',
        imageData.is_primary ? 1 : 0,
        imageData.tags || null,
        imageData.notes || null,
        imageData.uploaded_by || null
      ]

      // استخدام SimpleDatabaseService للتنفيذ
      const { SimpleDatabaseService } = await import('./SimpleDatabaseService')
      const simpleDatabaseService = SimpleDatabaseService.getInstance()
      const simpleDb = simpleDatabaseService.getDatabase()
      simpleDb.run(query, ...params)

      // الحصول على آخر معرف مدرج
      let lastInsertRowid = Date.now() % 1000000 // قيمة افتراضية
      try {
        const lastIdResult = db.exec("SELECT last_insert_rowid() as id")
        if (lastIdResult && lastIdResult.length > 0 && lastIdResult[0].values.length > 0) {
          lastInsertRowid = lastIdResult[0].values[0][0] as number
        }
      } catch (e) {
        Logger.warn('ProductionService', 'تحذير في الحصول على آخر معرف:', e)
      }

      Logger.info('ProductionService', `تم إضافة صورة لأمر الإنتاج ${imageData.order_id}: ${imageData.image_name} (ID: ${lastInsertRowid})`)
      return lastInsertRowid

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في إضافة صورة أمر الإنتاج:', error)
      throw error
    }
  }

  // إنشاء صور تجريبية لأمر إنتاج (للاختبار)
  public async createSampleProductionOrderImages(orderId: number): Promise<boolean> {
    try {
      Logger.info('ProductionService', `إنشاء صور تجريبية لأمر الإنتاج ${orderId}`)

      // التحقق من وجود أمر الإنتاج
      const order = this.db.prepare('SELECT id FROM production_orders WHERE id = ?').get(orderId)
      if (!order) {
        Logger.warn('ProductionService', `أمر الإنتاج ${orderId} غير موجود`)
        return false
      }

      // التحقق من وجود صور مسبقاً
      const existingImages = this.db.prepare('SELECT COUNT(*) as count FROM production_order_images WHERE order_id = ?').get(orderId) as any
      if (existingImages.count > 0) {
        Logger.info('ProductionService', `توجد صور مسبقاً لأمر الإنتاج ${orderId}`)
        return true
      }

      // إنشاء صور تجريبية باستخدام data URLs
      const sampleImages = [
        {
          name: 'صورة المواد الخام',
          category: 'material',
          description: 'صورة توضح المواد الخام المستخدمة في الإنتاج',
          color: '#FF6B6B'
        },
        {
          name: 'صورة العملية الإنتاجية',
          category: 'process',
          description: 'صورة توضح مراحل العملية الإنتاجية',
          color: '#4ECDC4'
        },
        {
          name: 'صورة المنتج النهائي',
          category: 'final',
          description: 'صورة توضح المنتج النهائي بعد الانتهاء من الإنتاج',
          color: '#45B7D1'
        },
        {
          name: 'صورة فحص الجودة',
          category: 'quality',
          description: 'صورة توضح عملية فحص الجودة للمنتج',
          color: '#96CEB4'
        }
      ]

      for (let i = 0; i < sampleImages.length; i++) {
        const imageInfo = sampleImages[i]

        // إنشاء صورة SVG بسيطة
        const svgContent = `
          <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
            <rect width="400" height="300" fill="${imageInfo.color}"/>
            <text x="200" y="120" text-anchor="middle" fill="white" font-size="24" font-family="Arial">${imageInfo.name}</text>
            <text x="200" y="160" text-anchor="middle" fill="white" font-size="16" font-family="Arial">أمر إنتاج رقم: ${orderId}</text>
            <text x="200" y="200" text-anchor="middle" fill="white" font-size="14" font-family="Arial">${imageInfo.description}</text>
          </svg>
        `

        // تحويل SVG إلى data URL
        const dataUrl = `data:image/svg+xml;base64,${Buffer.from(svgContent).toString('base64')}`

        // إضافة الصورة إلى قاعدة البيانات
        await this.addProductionOrderImage({
          order_id: orderId,
          image_name: imageInfo.name,
          image_path: dataUrl,
          file_size: svgContent.length,
          file_type: 'image/svg+xml',
          description: imageInfo.description,
          category: imageInfo.category,
          is_primary: i === 0, // الصورة الأولى تكون أساسية
          tags: `اختبار,${imageInfo.category}`,
          notes: 'صورة تجريبية للاختبار',
          uploaded_by: 1
        })
      }

      Logger.info('ProductionService', `تم إنشاء ${sampleImages.length} صورة تجريبية لأمر الإنتاج ${orderId}`)
      return true

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في إنشاء الصور التجريبية:', error)
      return false
    }
  }

  // إضافة وصفات متعددة لأمر الإنتاج
  public async addProductionOrderRecipes(orderId: number, recipes: {
    recipe_id: number
    quantity: number
    notes?: string
  }[]): Promise<ApiResponse> {
    try {
      if (!recipes || recipes.length === 0) {
        return { success: false, message: 'لا توجد وصفات لإضافتها' }
      }

      // التحقق من وجود أمر الإنتاج
      const order = this.db.prepare(`
        SELECT id FROM production_orders WHERE id = ?
      `).get(orderId) as any

      if (!order) {
        return { success: false, message: 'أمر الإنتاج غير موجود' }
      }

      // التحقق من صحة الوصفات
      for (const recipe of recipes) {
        if (!recipe.recipe_id || recipe.quantity <= 0) {
          return { success: false, message: 'بيانات الوصفة غير صحيحة' }
        }

        // التحقق من وجود الوصفة
        const recipeExists = this.db.prepare(`
          SELECT id FROM production_recipes WHERE id = ? AND is_active = 1
        `).get(recipe.recipe_id) as any

        if (!recipeExists) {
          return { success: false, message: `الوصفة رقم ${recipe.recipe_id} غير موجودة أو غير نشطة` }
        }
      }

      // إضافة الوصفات
      const transaction = this.db.transaction(() => {
        for (const recipe of recipes) {
          // الحصول على معلومات الوصفة
          const recipeInfo = this.db.prepare(`
            SELECT pr.*, i.name as product_name, i.id as product_id
            FROM production_recipes pr
            LEFT JOIN items i ON pr.product_id = i.id OR pr.item_id = i.id
            WHERE pr.id = ?
          `).get(recipe.recipe_id) as any

          if (recipeInfo) {
            // إضافة الصنف لأمر الإنتاج
            this.db.prepare(`
              INSERT INTO production_order_items (
                order_id, item_id, quantity, unit, notes
              ) VALUES (?, ?, ?, ?, ?)
            `).run(
              orderId,
              recipeInfo.product_id,
              recipe.quantity,
              'قطعة', // يمكن تخصيصها لاحقاً
              recipe.notes || null
            )
          }
        }
      })

      transaction()

      return {
        success: true,
        message: `تم إضافة ${recipes.length} وصفة لأمر الإنتاج بنجاح`,
        data: { added_recipes: recipes.length }
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في إضافة وصفات أمر الإنتاج:', error)
      return { success: false, message: error.message || 'حدث خطأ في إضافة الوصفات' }
    }
  }

  // التحقق من توفر المواد لعدة وصفات
  public async checkMultipleRecipesMaterialsAvailability(recipes: {
    recipe_id: number
    quantity: number
  }[]): Promise<ApiResponse> {
    try {
      if (!recipes || recipes.length === 0) {
        return { success: false, message: 'لا توجد وصفات للتحقق منها' }
      }

      const allMaterials: { [key: number]: {
        material_id: number
        material_name: string
        total_required: number
        available_quantity: number
        unit: string
        warehouse_id: number
      } } = {}

      // جمع جميع المواد المطلوبة من كل الوصفات
      for (const recipe of recipes) {
        const recipeMaterials = this.db.prepare(`
          SELECT rm.*, i.name as material_name, i.unit,
                 COALESCE(inv.quantity, 0) as available_quantity
          FROM recipe_materials rm
          LEFT JOIN items i ON rm.material_id = i.id
          LEFT JOIN inventory inv ON rm.material_id = inv.item_id AND rm.warehouse_id = inv.warehouse_id
          WHERE rm.recipe_id = ?
        `).all(recipe.recipe_id) as any[]

        for (const material of recipeMaterials) {
          const requiredQuantity = material.quantity * recipe.quantity
          const materialKey = material.material_id

          if (allMaterials[materialKey]) {
            // إضافة الكمية المطلوبة للمادة الموجودة
            allMaterials[materialKey].total_required += requiredQuantity
          } else {
            // إضافة مادة جديدة
            allMaterials[materialKey] = {
              material_id: material.material_id,
              material_name: material.material_name,
              total_required: requiredQuantity,
              available_quantity: material.available_quantity,
              unit: material.unit,
              warehouse_id: material.warehouse_id
            }
          }
        }
      }

      // التحقق من توفر المواد
      const insufficientMaterials: any[] = []
      let allAvailable = true

      for (const materialKey in allMaterials) {
        const material = allMaterials[materialKey]
        if (material.available_quantity < material.total_required) {
          allAvailable = false
          insufficientMaterials.push({
            material_id: material.material_id,
            material_name: material.material_name,
            required_quantity: material.total_required,
            available_quantity: material.available_quantity,
            shortage: material.total_required - material.available_quantity,
            unit: material.unit,
            warehouse_id: material.warehouse_id
          })
        }
      }

      return {
        success: true,
        data: {
          available: allAvailable,
          total_materials: Object.keys(allMaterials).length,
          insufficient_materials: insufficientMaterials,
          materials_summary: Object.values(allMaterials)
        }
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في التحقق من توفر المواد:', error)
      return { success: false, message: error.message || 'حدث خطأ في التحقق من توفر المواد' }
    }
  }

  // إنشاء أمر إنتاج مع عدة وصفات (للمنتجات المركبة)
  public async createProductionOrderWithMultipleRecipes(orderData: {
    composite_product_id: number // المنتج التام (غرفة نوم كلاسيك)
    quantity: number // كمية المنتج التام
    order_code?: string
    department_id?: number
    customer_id?: number
    // تم إزالة warehouse_id - سيتم تحديد المخازن من الوصفات
    start_date?: string
    end_date?: string
    priority?: string
    notes?: string
    recipes: {
      recipe_id: number
      quantity: number
      notes?: string
    }[]
  }, userId?: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود المنتج التام
      if (!orderData.composite_product_id) {
        return { success: false, message: 'يجب تحديد المنتج التام المراد إنتاجه' }
      }

      // التحقق من كمية المنتج التام
      if (!orderData.quantity || orderData.quantity <= 0) {
        return { success: false, message: 'يجب تحديد كمية صحيحة للمنتج التام' }
      }

      // التحقق من وجود المنتج التام في قاعدة البيانات
      const compositeProduct = this.db.prepare('SELECT id, name, is_active FROM items WHERE id = ?').get(orderData.composite_product_id) as any
      if (!compositeProduct) {
        return { success: false, message: 'المنتج التام المحدد غير موجود' }
      }
      if (!compositeProduct.is_active) {
        return { success: false, message: 'المنتج التام المحدد غير نشط' }
      }

      // التحقق من وجود وصفات
      if (!orderData.recipes || orderData.recipes.length === 0) {
        return { success: false, message: 'يجب إضافة وصفة واحدة على الأقل' }
      }

      // التحقق من صحة الوصفات
      for (const recipe of orderData.recipes) {
        if (!recipe.recipe_id || recipe.quantity <= 0) {
          return { success: false, message: 'بيانات الوصفة غير صحيحة' }
        }
      }

      // التحقق من توفر المواد لجميع الوصفات
      const availabilityCheck = await this.checkMultipleRecipesMaterialsAvailability(orderData.recipes)
      if (!availabilityCheck.success) {
        return availabilityCheck
      }

      // توليد رقم أمر تلقائي
      const orderNumber = this.generateProductionOrderNumberSync()

      // توليد كود أمر تلقائي
      const orderCode = orderData.order_code || this.generateProductionOrderCodeSync()

      // حساب التكلفة الإجمالية المقدرة
      let totalEstimatedCost = 0
      let totalEstimatedHours = 0

      for (const recipe of orderData.recipes) {
        const recipeInfo = this.db.prepare(`
          SELECT estimated_cost, estimated_time FROM production_recipes WHERE id = ?
        `).get(recipe.recipe_id) as any

        if (recipeInfo) {
          totalEstimatedCost += (recipeInfo.estimated_cost || 0) * recipe.quantity
          totalEstimatedHours += (recipeInfo.estimated_time || 0) * recipe.quantity
        }
      }

      // بدء المعاملة
      const transaction = this.db.transaction(() => {
        // 1. إنشاء أمر الإنتاج الرئيسي للمنتج التام
        const result = this.db.prepare(`
          INSERT INTO production_orders (
            order_number, order_code, product_id, recipe_id, quantity, department_id, customer_id,
            start_date, end_date, priority, estimated_cost, estimated_hours,
            notes, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          orderNumber,
          orderCode,
          orderData.composite_product_id, // المنتج التام (غرفة نوم كلاسيك)
          null, // recipe_id - لا يوجد وصفة واحدة للمنتج التام
          orderData.quantity, // كمية المنتج التام
          orderData.department_id || null,
          orderData.customer_id || null,
          orderData.start_date || null,
          orderData.end_date || null,
          orderData.priority || 'normal',
          totalEstimatedCost * orderData.quantity, // التكلفة الإجمالية للمنتج التام
          totalEstimatedHours * orderData.quantity, // الوقت الإجمالي للمنتج التام
          orderData.notes || null,
          userId || null
        )

        const orderId = result.lastInsertRowid

        // 2. إضافة تفاصيل الوصفات (الأصناف المكونة للمنتج التام)
        for (const recipe of orderData.recipes) {
          // الحصول على معلومات الوصفة والمنتج المكون
          const recipeInfo = this.db.prepare(`
            SELECT pr.*, i.name as product_name, i.id as product_id
            FROM production_recipes pr
            LEFT JOIN items i ON pr.product_id = i.id
            WHERE pr.id = ?
          `).get(recipe.recipe_id) as any

          if (recipeInfo) {
            // إضافة الصنف المكون لأمر الإنتاج مع الكمية المطلوبة للمنتج التام
            const totalQuantityNeeded = recipe.quantity * orderData.quantity

            this.db.prepare(`
              INSERT INTO production_order_items (
                order_id, item_id, quantity, unit, notes
              ) VALUES (?, ?, ?, ?, ?)
            `).run(
              orderId,
              recipeInfo.product_id, // الصنف المكون (دولاب، سرير، تسريحة)
              totalQuantityNeeded, // الكمية الإجمالية المطلوبة
              'قطعة',
              `${recipe.notes || ''} - مكون من المنتج التام: ${compositeProduct.name}`.trim()
            )

            // إضافة ربط في جدول المنتجات المركبة إذا لم يكن موجوداً
            try {
              this.db.prepare(`
                INSERT OR IGNORE INTO composite_products (
                  composite_product_id, component_product_id, quantity, unit, notes
                ) VALUES (?, ?, ?, ?, ?)
              `).run(
                orderData.composite_product_id,
                recipeInfo.product_id,
                recipe.quantity,
                'قطعة',
                `مكون من ${compositeProduct.name}`
              )
            } catch (error) {
              // تجاهل الخطأ إذا كان الربط موجوداً بالفعل
            }
          }
        }

        return orderId
      })

      const orderId = transaction()

      // حفظ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: `تم إنشاء أمر إنتاج المنتج التام بنجاح - رقم الأمر: ${orderNumber} - المنتج: ${compositeProduct.name}`,
        data: {
          order_id: orderId,
          order_number: orderNumber,
          order_code: orderCode,
          composite_product_id: orderData.composite_product_id,
          composite_product_name: compositeProduct.name,
          composite_quantity: orderData.quantity,
          total_components: orderData.recipes.length,
          total_component_quantity: orderData.recipes.reduce((sum, r) => sum + r.quantity * orderData.quantity, 0),
          estimated_cost: totalEstimatedCost * orderData.quantity,
          estimated_hours: totalEstimatedHours * orderData.quantity,
          materials_availability: availabilityCheck.data
        }
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في إنشاء أمر الإنتاج مع عدة وصفات:', error)
      return { success: false, message: error.message || 'حدث خطأ في إنشاء أمر الإنتاج' }
    }
  }

  // الحصول على تفاصيل أمر الإنتاج مع الوصفات المتعددة
  public async getProductionOrderWithRecipes(orderId: number): Promise<ApiResponse> {
    try {
      // الحصول على معلومات أمر الإنتاج الأساسية
      const order = this.db.prepare(`
        SELECT po.*,
               d.name as department_name,
               c.name as customer_name,
               w.name as warehouse_name,
               u.name as created_by_name
        FROM production_orders po
        LEFT JOIN production_departments d ON po.department_id = d.id
        LEFT JOIN customers c ON po.customer_id = c.id
        LEFT JOIN warehouses w ON po.warehouse_id = w.id
        LEFT JOIN users u ON po.created_by = u.id
        WHERE po.id = ?
      `).get(orderId) as any

      if (!order) {
        return { success: false, message: 'أمر الإنتاج غير موجود' }
      }

      // الحصول على الوصفات والأصناف المرتبطة بالأمر
      const orderItems = this.db.prepare(`
        SELECT poi.*,
               i.name as item_name,
               i.code as item_code,
               i.unit as item_unit,
               pr.id as recipe_id,
               pr.name as recipe_name,
               pr.code as recipe_code,
               pr.estimated_time,
               pr.difficulty_level
        FROM production_order_items poi
        LEFT JOIN items i ON poi.item_id = i.id
        LEFT JOIN production_recipes pr ON (pr.product_id = poi.item_id OR pr.item_id = poi.item_id)
        WHERE poi.order_id = ?
        ORDER BY poi.id
      `).all(orderId) as any[]

      // الحصول على المواد المطلوبة لكل وصفة
      const recipesWithMaterials = []
      for (const item of orderItems) {
        if (item.recipe_id) {
          const materials = this.db.prepare(`
            SELECT rm.*,
                   i.name as material_name,
                   i.code as material_code,
                   i.unit as material_unit,
                   w.name as warehouse_name,
                   COALESCE(inv.quantity, 0) as available_quantity
            FROM recipe_materials rm
            LEFT JOIN items i ON rm.material_id = i.id
            LEFT JOIN warehouses w ON rm.warehouse_id = w.id
            LEFT JOIN inventory inv ON rm.material_id = inv.item_id AND rm.warehouse_id = inv.warehouse_id
            WHERE rm.recipe_id = ?
            ORDER BY rm.id
          `).all(item.recipe_id) as any[]

          recipesWithMaterials.push({
            ...item,
            materials: materials.map(material => ({
              ...material,
              required_quantity: material.quantity * item.quantity,
              total_cost: material.cost_per_unit * material.quantity * item.quantity
            }))
          })
        } else {
          recipesWithMaterials.push({
            ...item,
            materials: []
          })
        }
      }

      // حساب الإحصائيات
      const totalMaterials = recipesWithMaterials.reduce((sum, recipe) => sum + recipe.materials.length, 0)
      const totalMaterialCost = recipesWithMaterials.reduce((sum, recipe) =>
        sum + recipe.materials.reduce((matSum: number, mat: any) => matSum + mat.total_cost, 0), 0
      )

      return {
        success: true,
        data: {
          order: order,
          recipes: recipesWithMaterials,
          summary: {
            total_recipes: recipesWithMaterials.length,
            total_items: recipesWithMaterials.reduce((sum, recipe) => sum + recipe.quantity, 0),
            total_materials: totalMaterials,
            total_material_cost: totalMaterialCost,
            estimated_total_cost: order.estimated_cost || 0,
            estimated_total_hours: order.estimated_hours || 0
          }
        }
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في جلب تفاصيل أمر الإنتاج:', error)
      return { success: false, message: error.message || 'حدث خطأ في جلب تفاصيل أمر الإنتاج' }
    }
  }

  // جلب مواد أمر الإنتاج للطباعة
  public async getProductionOrderMaterials(orderId: number): Promise<any[]> {
    try {
      const materials = this.db.prepare(`
        SELECT pom.*, m.name as material_name, m.code as material_code, m.unit
        FROM production_order_materials pom
        LEFT JOIN materials m ON pom.material_id = m.id
        WHERE pom.order_id = ?
        ORDER BY pom.id
      `).all(orderId) as any[]

      return materials
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب مواد أمر الإنتاج:', error)
      return []
    }
  }

  // جلب مراحل أمر الإنتاج للطباعة
  public async getProductionOrderStages(orderId: number): Promise<any[]> {
    try {
      const stages = this.db.prepare(`
        SELECT pos.*, ps.name as stage_name, ps.description as stage_description
        FROM production_order_stages pos
        LEFT JOIN production_stages ps ON pos.stage_id = ps.id
        WHERE pos.order_id = ?
        ORDER BY pos.sequence_number
      `).all(orderId) as any[]

      return stages
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب مراحل أمر الإنتاج:', error)
      return []
    }
  }

  // جلب جميع مراحل أوامر الإنتاج
  public async getAllProductionOrderStages(): Promise<any[]> {
    try {
      const stages = this.db.prepare(`
        SELECT pos.*, ps.name as stage_name, ps.description as stage_description,
               po.order_number, po.order_code, po.status as order_status
        FROM production_order_stages pos
        LEFT JOIN production_stages ps ON pos.stage_id = ps.id
        LEFT JOIN production_orders po ON pos.order_id = po.id
        ORDER BY po.created_at DESC, pos.sequence_number
      `).all() as any[]

      return stages
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب جميع مراحل أوامر الإنتاج:', error)
      return []
    }
  }

  // توليد كود أمر إنتاج جديد - محسن وآمن
  public async generateOrderCode(): Promise<string> {
    const maxRetries = 5
    let attempt = 0

    while (attempt < maxRetries) {
      try {
        const transaction = this.db.transaction(() => {
          // جلب آخر كود أمر إنتاج
          const lastOrder = this.db.prepare(`
            SELECT order_code FROM production_orders
            WHERE order_code LIKE 'PO-%'
            ORDER BY CAST(SUBSTR(order_code, 4) AS INTEGER) DESC
            LIMIT 1
          `).get() as any

          let newNumber = 1
          if (lastOrder && lastOrder.order_code) {
            const currentNumber = parseInt(lastOrder.order_code.substring(3))
            if (!isNaN(currentNumber)) {
              newNumber = currentNumber + 1
            }
          }

          const orderCode = 'PO-' + newNumber.toString().padStart(6, '0')

          // التحقق من عدم وجود الكود مسبقاً
          const existingOrder = this.db.prepare(`
            SELECT id FROM production_orders WHERE order_code = ?
          `).get(orderCode)

          if (existingOrder) {
            throw new Error(`Order code ${orderCode} already exists`)
          }

          return orderCode
        })

        const orderCode = transaction()

        Logger.info('ProductionService', `تم توليد كود أمر إنتاج جديد: ${orderCode}`)
        return orderCode

      } catch (error: any) {
        attempt++
        Logger.warn('ProductionService', `محاولة ${attempt} فشلت في توليد كود الأمر:`, error.message)

        if (attempt >= maxRetries) {
          // fallback آمن باستخدام timestamp
          const timestamp = Date.now().toString()
          const fallbackCode = 'PO-' + timestamp.slice(-6)

          Logger.error('ProductionService', 'فشل في توليد كود أمر منتظم، استخدام fallback:', fallbackCode)
          return fallbackCode
        }

        // انتظار قصير قبل المحاولة التالية
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }

    // هذا السطر لن يتم الوصول إليه، لكنه مطلوب للـ TypeScript
    throw new Error('فشل في توليد كود أمر الإنتاج بعد عدة محاولات')
  }

  // توليد رقم أمر إنتاج جديد - محسن وآمن
  public async generateOrderNumber(): Promise<string> {
    const maxRetries = 5
    let attempt = 0

    while (attempt < maxRetries) {
      try {
        // استخدام transaction للأمان
        const transaction = this.db.transaction(() => {
          // الحصول على آخر رقم أمر بطريقة آمنة
          const lastOrder = this.db.prepare(`
            SELECT order_number FROM production_orders
            WHERE order_number LIKE 'PROD%'
            AND order_number IS NOT NULL
            AND order_number != ''
            AND LENGTH(order_number) = 10
            ORDER BY CAST(SUBSTR(order_number, 5) AS INTEGER) DESC
            LIMIT 1
          `).get() as any

          let newNumber = 1

          if (lastOrder?.order_number) {
            const codeNumber = lastOrder.order_number.substring(4)
            const lastNumber = parseInt(codeNumber, 10)

            if (!isNaN(lastNumber) && lastNumber > 0 && lastNumber < 999999) {
              newNumber = lastNumber + 1
            }
          }

          const orderNumber = 'PROD' + newNumber.toString().padStart(6, '0')

          // التحقق من عدم وجود الرقم مسبقاً
          const existingOrder = this.db.prepare(`
            SELECT id FROM production_orders WHERE order_number = ?
          `).get(orderNumber)

          if (existingOrder) {
            throw new Error(`Order number ${orderNumber} already exists`)
          }

          return orderNumber
        })

        const orderNumber = transaction()

        Logger.info('ProductionService', `تم توليد رقم أمر إنتاج جديد: ${orderNumber}`)
        return orderNumber

      } catch (error: any) {
        attempt++
        Logger.warn('ProductionService', `محاولة ${attempt} فشلت في توليد رقم الأمر:`, error.message)

        if (attempt >= maxRetries) {
          // fallback آمن باستخدام timestamp
          const timestamp = Date.now().toString()
          const fallbackNumber = 'PROD' + timestamp.slice(-6)

          Logger.error('ProductionService', 'فشل في توليد رقم أمر منتّم، استخدام fallback:', fallbackNumber)
          return fallbackNumber
        }

        // انتّار قصير قبل المحاولة التالية
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }

    // هذا السطر لن يتم الوصول إليه، لكنه مطلوب للـ TypeScript
    throw new Error('فشل في توليد رقم أمر الإنتاج بعد عدة محاولات')
  }

  // توليد كود وصفة إنتاج جديد - محسن وآمن
  public async generateRecipeCode(): Promise<string> {
    const maxRetries = 5
    let attempt = 0

    while (attempt < maxRetries) {
      try {
        const transaction = this.db.transaction(() => {
          // الحصول على آخر كود وصفة
          const lastRecipe = this.db.prepare(`
            SELECT code FROM production_recipes
            WHERE code LIKE 'RCP%'
            AND code IS NOT NULL
            AND code != ''
            AND LENGTH(code) = 9
            ORDER BY CAST(SUBSTR(code, 4) AS INTEGER) DESC
            LIMIT 1
          `).get() as any

          let newNumber = 1

          if (lastRecipe?.code) {
            const codeNumber = lastRecipe.code.substring(3)
            const lastNumber = parseInt(codeNumber, 10)

            if (!isNaN(lastNumber) && lastNumber > 0 && lastNumber < 999999) {
              newNumber = lastNumber + 1
            }
          }

          const recipeCode = 'RCP' + newNumber.toString().padStart(6, '0')

          // التحقق من عدم وجود الكود مسبقاً
          const existingRecipe = this.db.prepare(`
            SELECT id FROM production_recipes WHERE code = ?
          `).get(recipeCode)

          if (existingRecipe) {
            throw new Error(`Recipe code ${recipeCode} already exists`)
          }

          return recipeCode
        })

        const recipeCode = transaction()

        Logger.info('ProductionService', `تم توليد كود وصفة جديد: ${recipeCode}`)
        return recipeCode

      } catch (error: any) {
        attempt++
        Logger.warn('ProductionService', `محاولة ${attempt} فشلت في توليد كود الوصفة:`, error.message)

        if (attempt >= maxRetries) {
          const timestamp = Date.now().toString()
          const fallbackCode = 'RCP' + timestamp.slice(-6)

          Logger.error('ProductionService', 'فشل في توليد كود وصفة منتّم، استخدام fallback:', fallbackCode)
          return fallbackCode
        }

        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }

    throw new Error('فشل في توليد كود الوصفة بعد عدة محاولات')
  }

  // التحقق من صلاحيات المستخدم
  private async checkUserPermission(userId: number | undefined, action: string): Promise<boolean> {
    try {
      // إذا لم يتم تمرير معرف المستخدم، نسمح بالعملية (للتوافق مع النّام الحالي)
      if (!userId) {
        Logger.warn('ProductionService', `تم تنفيذ العملية ${action} بدون معرف مستخدم`)
        return true
      }

      // التحقق من وجود المستخدم
      const user = this.db.prepare('SELECT id, is_active, role FROM users WHERE id = ?').get(userId) as any
      if (!user) {
        Logger.error('ProductionService', `مستخدم غير موجود: ${userId}`)
        return false
      }

      if (!user.is_active) {
        Logger.error('ProductionService', `مستخدم غير نشط: ${userId}`)
        return false
      }

      // التحقق من الصلاحيات حسب الدور
      const adminActions = ['delete_production_order', 'delete_recipe', 'modify_completed_order']
      const managerActions = ['create_production_order', 'update_production_order', 'start_production', 'complete_production']

      if (adminActions.includes(action) && user.role !== 'admin') {
        Logger.warn('ProductionService', `المستخدم ${userId} ليس لديه صلاحية ${action}`)
        return false
      }

      if (managerActions.includes(action) && !['admin', 'manager', 'production_manager'].includes(user.role)) {
        Logger.warn('ProductionService', `المستخدم ${userId} ليس لديه صلاحية ${action}`)
        return false
      }

      return true
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في التحقق من الصلاحيات:', error)
      return false
    }
  }

  // تسجيل العمليات الحساسة
  private async logSensitiveOperation(
    userId: number | undefined,
    action: string,
    details: any
  ): Promise<void> {
    try {
      const logEntry = {
        user_id: userId || null,
        action,
        details: JSON.stringify(details),
        timestamp: new Date().toISOString(),
        ip_address: 'localhost', // سيتم تحديثه لاحقاً
        user_agent: 'Electron App'
      }

      // حفّ السجل في جدول العمليات (سيتم إنشاؤه لاحقاً)
      Logger.info('ProductionService', `عملية حساسة: ${action}`, logEntry)
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في تسجيل العملية:', error)
    }
  }



  // توليد كود مرحلة إنتاج جديد
  public async generateProductionStageCode(): Promise<string> {
    try {
      const lastStage = this.db.prepare(`
        SELECT code FROM production_stages
        WHERE code LIKE 'STAGE%' AND code IS NOT NULL AND code != ''
        ORDER BY CAST(SUBSTR(code, 6) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastStage && lastStage.code && lastStage.code.length >= 8) {
        const codeNumber = lastStage.code.substring(5)
        const lastNumber = parseInt(codeNumber)

        if (!isNaN(lastNumber)) {
          const newNumber = lastNumber + 1
          return 'STAGE' + newNumber.toString().padStart(3, '0')
        }
      }

      return 'STAGE001'
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في توليد كود مرحلة الإنتاج:', error)
      return 'STAGE' + Date.now().toString().slice(-3)
    }
  }

  // بدء أمر الإنتاج - استهلاك المواد الخام من المخزون
  public async startProductionOrder(orderId: number): Promise<ApiResponse> {
    try {
      // الحصول على تفاصيل أمر الإنتاج
      const order = this.db.prepare(`
        SELECT po.*, i.name as product_name
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        WHERE po.id = ? AND po.status = 'pending'
      `).get(orderId) as any

      if (!order) {
        return { success: false, message: 'أمر الإنتاج غير موجود أو تم بدؤه مسبقاً' }
      }

      // البحث عن وصفة الإنتاج للمنتج
      const recipe = this.db.prepare(`
        SELECT * FROM production_recipes
        WHERE product_id = ? AND is_active = 1
        LIMIT 1
      `).get(order.product_id) as any

      if (!recipe) {
        return { success: false, message: 'لا توجد وصفة إنتاج لهذا المنتج' }
      }

      // الحصول على مواد الوصفة
      const recipeMaterials = this.db.prepare(`
        SELECT rm.*, i.name as material_name, i.unit
        FROM recipe_materials rm
        LEFT JOIN items i ON rm.material_id = i.id
        WHERE rm.recipe_id = ?
      `).all(recipe.id) as any[]

      if (recipeMaterials.length === 0) {
        return { success: false, message: 'لا توجد مواد في وصفة الإنتاج' }
      }

      // التحقق من توفر المواد في المخزون
      const insufficientMaterials: Array<{
        material_name: string
        required: number
        available: number
        shortage: number
      }> = []

      for (const material of recipeMaterials) {
        const requiredQuantity = material.quantity * order.quantity

        // الحصول على الكمية المتاحة في مخزن المواد المحدد في أمر الإنتاج
        const materialWarehouseId = order.material_warehouse_id || recipe.material_warehouse_id
        const inventory = this.db.prepare(`
          SELECT SUM(quantity) as total_quantity
          FROM inventory
          WHERE item_id = ? AND warehouse_id = ?
        `).get(material.material_id, materialWarehouseId) as any

        const availableQuantity = inventory?.total_quantity || 0

        if (availableQuantity < requiredQuantity) {
          insufficientMaterials.push({
            material_name: material.material_name,
            required: requiredQuantity,
            available: availableQuantity,
            shortage: requiredQuantity - availableQuantity
          })
        }
      }

      // إذا كانت هناك مواد غير كافية، إرجاع تحذير
      if (insufficientMaterials.length > 0) {
        return {
          success: false,
          message: 'بعض المواد غير كافية في المخزون',
          data: { insufficientMaterials }
        }
      }

      // بدء معاملة قاعدة البيانات
      const transaction = this.db.transaction(() => {
        // تحديث حالة أمر الإنتاج
        this.db.prepare(`
          UPDATE production_orders
          SET status = 'in_progress', start_date = datetime('now'), updated_at = datetime('now')
          WHERE id = ?
        `).run(orderId)

        // استهلاك المواد من المخزون
        for (const material of recipeMaterials) {
          const requiredQuantity = material.quantity * order.quantity

          // تحديث المخزون (خصم الكمية)
          this.db.prepare(`
            UPDATE inventory
            SET quantity = quantity - ?, last_updated = datetime('now')
            WHERE item_id = ? AND quantity >= ?
          `).run(requiredQuantity, material.material_id, requiredQuantity)

          // إنشاء حركة مخزون صادرة
          this.db.prepare(`
            INSERT INTO inventory_movements (
              item_id, movement_type, quantity, reference_type, reference_id,
              notes, created_at
            ) VALUES (?, 'out', ?, 'production_order', ?, ?, datetime('now'))
          `).run(
            material.material_id,
            requiredQuantity,
            orderId,
            'استهلاك مواد لأمر إنتاج ' + order.order_number + ' - ' + material.material_name
          )
        }
      })

      transaction()

      return {
        success: true,
        message: 'تم بدء أمر الإنتاج بنجاح وتم استهلاك المواد من المخزون'
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في بدء أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في بدء أمر الإنتاج' }
    }
  }

  // إكمال أمر الإنتاج - إضافة المنتج النهائي للمخزون
  public async completeProductionOrder(orderId: number): Promise<ApiResponse> {
    try {
      // الحصول على تفاصيل أمر الإنتاج
      const order = this.db.prepare(`
        SELECT po.*, i.name as product_name, i.unit
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        WHERE po.id = ? AND po.status = 'in_progress'
      `).get(orderId) as any

      if (!order) {
        return { success: false, message: 'أمر الإنتاج غير موجود أو لم يتم بدؤه بعد' }
      }

      // تحديد مخزن المنتج التام من أمر الإنتاج أو الوصفة
      let targetWarehouseId = order.product_warehouse_id

      if (!targetWarehouseId) {
        // البحث عن مخزن المنتج في الوصفة
        const recipe = this.db.prepare(`
          SELECT product_warehouse_id FROM production_recipes
          WHERE product_id = ? AND is_active = 1 LIMIT 1
        `).get(order.product_id) as any

        if (recipe && recipe.product_warehouse_id) {
          targetWarehouseId = recipe.product_warehouse_id
        } else {
          // الحصول على المخزن الافتراضي إذا لم يكن محدد
          const defaultWarehouse = this.db.prepare(`
            SELECT id FROM warehouses WHERE is_active = 1 LIMIT 1
          `).get() as any

          if (!defaultWarehouse) {
            return { success: false, message: 'لا يوجد مخزن محدد للمنتج التام في الأمر أو الوصفة ولا يوجد مخزن افتراضي نشط' }
          }
          targetWarehouseId = defaultWarehouse.id
        }
      }

      // بدء معاملة قاعدة البيانات
      const transaction = this.db.transaction(() => {
        // تحديث حالة أمر الإنتاج
        this.db.prepare(`
          UPDATE production_orders
          SET status = 'completed', end_date = datetime('now'), updated_at = datetime('now')
          WHERE id = ?
        `).run(orderId)

        // التحقق من وجود سجل المخزون للمنتج التام
        // ملاحظة: يتم إدخال المنتج التام (مثل غرفة نوم كلاسيك) وليس الأصناف المكونة له
        const existingInventory = this.db.prepare(`
          SELECT id, quantity FROM inventory
          WHERE item_id = ? AND warehouse_id = ?
        `).get(order.product_id, targetWarehouseId) as any

        if (existingInventory) {
          // تحديث الكمية الموجودة
          this.db.prepare(`
            UPDATE inventory
            SET quantity = quantity + ?, last_updated = datetime('now')
            WHERE id = ?
          `).run(order.quantity, existingInventory.id)
        } else {
          // إنشاء سجل مخزون جديد
          this.db.prepare(`
            INSERT INTO inventory (item_id, warehouse_id, quantity, last_updated)
            VALUES (?, ?, ?, datetime('now'))
          `).run(order.product_id, targetWarehouseId, order.quantity)
        }

        // إنشاء حركة مخزون واردة للمنتج التام
        // ملاحظة: يتم تسجيل دخول المنتج التام فقط (مثل غرفة نوم كلاسيك)
        this.db.prepare(`
          INSERT INTO inventory_movements (
            item_id, warehouse_id, movement_type, quantity, reference_type, reference_id,
            notes, created_at
          ) VALUES (?, ?, 'in', ?, 'production_order', ?, ?, datetime('now'))
        `).run(
          order.product_id, // المنتج التام
          targetWarehouseId,
          order.quantity, // كمية المنتج التام
          orderId,
          'إنتاج مكتمل لأمر ' + order.order_number + ' - ' + order.product_name + ' (منتج تام)'
        )

        // تحديث الكمية الإجمالية في جدول الأصناف (للتوافق مع النّام الحالي)
        this.db.prepare(`
          UPDATE items
          SET quantity = quantity + ?, updated_at = datetime('now')
          WHERE id = ?
        `).run(order.quantity, order.product_id)
      })

      transaction()

      return {
        success: true,
        message: 'تم إكمال أمر الإنتاج بنجاح وتم إضافة المنتج للمخزون'
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في إكمال أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في إكمال أمر الإنتاج' }
    }
  }

  // جلب مواد الوصفة
  public async getRecipeMaterials(recipeId: number): Promise<ApiResponse> {
    try {
      const recipeMaterials = this.db.prepare(`
        SELECT rm.*,
               i.name as material_name,
               i.code as material_code,
               i.unit,
               i.cost_price as cost_per_unit,
               i.sale_price,
               i.type as material_type,
               c.name as category_name,
               w.name as warehouse_name,
               w.code as warehouse_code
        FROM recipe_materials rm
        LEFT JOIN items i ON rm.material_id = i.id
        LEFT JOIN categories c ON i.category_id = c.id
        LEFT JOIN warehouses w ON rm.warehouse_id = w.id
        WHERE rm.recipe_id = ?
        ORDER BY rm.id
      `).all(recipeId) as any[]

      return {
        success: true,
        data: recipeMaterials
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب مواد الوصفة:', error)
      return { success: false, message: 'حدث خطأ في جلب مواد الوصفة' }
    }
  }

  // التحقق من توفر مواد الوصفة
  public async checkRecipeMaterialsAvailability(recipeId: number, quantity: number): Promise<ApiResponse> {
    try {
      // الحصول على مواد الوصفة
      const recipeMaterials = this.db.prepare(`
        SELECT rm.*,
               i.name as material_name,
               i.code as material_code,
               i.unit,
               i.cost_price as cost_per_unit
        FROM recipe_materials rm
        LEFT JOIN items i ON rm.material_id = i.id
        WHERE rm.recipe_id = ?
      `).all(recipeId) as any[]

      if (recipeMaterials.length === 0) {
        return {
          success: true,
          data: {
            available: true,
            insufficientMaterials: [],
            materials: [],
            totalMaterials: 0,
            availableMaterials: 0
          }
        }
      }

      const insufficientMaterials: Array<{
        material_id: number
        material_name: string
        material_unit: string
        required_quantity: number
        available_quantity: number
        shortage: number
      }> = []
      let allAvailable = true

      for (const material of recipeMaterials) {
        const requiredQuantity = material.quantity * quantity

        // الحصول على الكمية المتاحة في المخزون
        const inventory = this.db.prepare(`
          SELECT SUM(quantity) as total_quantity
          FROM inventory
          WHERE item_id = ?
        `).get(material.material_id) as any

        const availableQuantity = inventory?.total_quantity || 0

        if (availableQuantity < requiredQuantity) {
          allAvailable = false
          insufficientMaterials.push({
            material_id: material.material_id,
            material_name: material.material_name,
            material_unit: material.unit,
            required_quantity: requiredQuantity,
            available_quantity: availableQuantity,
            shortage: requiredQuantity - availableQuantity
          })
        }
      }

      return {
        success: true,
        data: {
          available: allAvailable,
          insufficientMaterials
        }
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في التحقق من توفر المواد:', error)
      return { success: false, message: 'حدث خطأ في التحقق من توفر المواد' }
    }
  }

  // تحديث أمر الإنتاج
  public async updateProductionOrder(orderId: number, orderData: {
    product_id?: number
    quantity?: number
    department_id?: number
    customer_id?: number
    start_date?: string
    end_date?: string
    priority?: string
    estimated_cost?: number
    estimated_hours?: number
    notes?: string
    status?: string
  }): Promise<ApiResponse> {
    try {
      // التحقق من وجود الأمر
      const existingOrder = this.db.prepare(`
        SELECT * FROM production_orders WHERE id = ?
      `).get(orderId) as any

      if (!existingOrder) {
        return { success: false, message: 'أمر الإنتاج غير موجود' }
      }

      // التحقق من إمكانية التحديث (لا يمكن تحديث الأوامر المكتملة)
      if (existingOrder.status === 'completed') {
        return { success: false, message: 'لا يمكن تحديث أمر إنتاج مكتمل' }
      }

      // بناء استعلام التحديث
      const updateFields: string[] = []
      const updateValues: any[] = []

      Object.keys(orderData).forEach(key => {
        if (orderData[key as keyof typeof orderData] !== undefined) {
          updateFields.push(key + ' = ?')
          updateValues.push(orderData[key as keyof typeof orderData])
        }
      })

      if (updateFields.length === 0) {
        return { success: false, message: 'لا توجد بيانات للتحديث' }
      }

      updateFields.push('updated_at = datetime(\'now\')')
      updateValues.push(orderId)

      const updateQuery = 'UPDATE production_orders SET ' + updateFields.join(', ') + ' WHERE id = ?'

      const result = this.db.prepare(updateQuery).run(...updateValues)

      if (result.changes > 0) {
        return {
          success: true,
          message: 'تم تحديث أمر الإنتاج بنجاح',
          data: { id: orderId }
        }
      } else {
        return { success: false, message: 'فشل في تحديث أمر الإنتاج' }
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في تحديث أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تحديث أمر الإنتاج' }
    }
  }

  // حذف أمر الإنتاج
  public async deleteProductionOrder(orderId: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود الأمر
      const existingOrder = this.db.prepare(`
        SELECT * FROM production_orders WHERE id = ?
      `).get(orderId) as any

      if (!existingOrder) {
        return { success: false, message: 'أمر الإنتاج غير موجود' }
      }

      // التحقق من إمكانية الحذف (لا يمكن حذف الأوامر قيد التنفيذ أو المكتملة)
      if (existingOrder.status === 'in_progress') {
        return { success: false, message: 'لا يمكن حذف أمر إنتاج قيد التنفيذ' }
      }

      if (existingOrder.status === 'completed') {
        return { success: false, message: 'لا يمكن حذف أمر إنتاج مكتمل' }
      }

      const result = this.db.prepare(`
        DELETE FROM production_orders WHERE id = ?
      `).run(orderId)

      if (result.changes > 0) {
        return {
          success: true,
          message: 'تم حذف أمر الإنتاج بنجاح'
        }
      } else {
        return { success: false, message: 'فشل في حذف أمر الإنتاج' }
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في حذف أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في حذف أمر الإنتاج' }
    }
  }

  // تحديث وصفة الإنتاج
  public async updateProductionRecipe(recipeId: number, recipeData: {
    code?: string
    name?: string
    description?: string
    item_id?: number
    department_id?: number
    material_warehouse_id?: number
    product_warehouse_id?: number
    estimated_time?: number
    difficulty_level?: string
    instructions?: string
    is_active?: boolean
    materials?: any[]
  }): Promise<ApiResponse> {
    try {
      // التحقق من وجود الوصفة
      const existingRecipe = this.db.prepare(`
        SELECT * FROM production_recipes WHERE id = ?
      `).get(recipeId) as any

      if (!existingRecipe) {
        return { success: false, message: 'وصفة الإنتاج غير موجودة' }
      }

      // بدء معاملة قاعدة البيانات
      const transaction = this.db.transaction(() => {
        // بناء استعلام التحديث للوصفة
        const updateFields: string[] = []
        const updateValues: any[] = []

        // تحديث الحقول الأساسية
        const basicFields = ['code', 'name', 'description', 'department_id', 'material_warehouse_id', 'product_warehouse_id', 'estimated_time', 'difficulty_level', 'instructions', 'is_active']
        basicFields.forEach(key => {
          if (recipeData[key as keyof typeof recipeData] !== undefined) {
            updateFields.push(key + ' = ?')
            updateValues.push(recipeData[key as keyof typeof recipeData])
          }
        })

        // معالجة خاصة لحقل المنتج (دعم كلا من item_id و product_id)
        if (recipeData.item_id !== undefined) {
          updateFields.push('product_id = ?')
          updateValues.push(recipeData.item_id)
        }

        if (updateFields.length > 0) {
          updateFields.push('updated_at = datetime(\'now\')')
          updateFields.push('version = version + 1')
          updateValues.push(recipeId)

          const updateQuery = 'UPDATE production_recipes SET ' + updateFields.join(', ') + ' WHERE id = ?'
          this.db.prepare(updateQuery).run(...updateValues)
        }

        // تحديث المواد إذا تم توفيرها
        let estimatedCost = 0
        if (recipeData.materials && Array.isArray(recipeData.materials)) {
          // حذف المواد الحالية
          this.db.prepare(`
            DELETE FROM recipe_materials WHERE recipe_id = ?
          `).run(recipeId)

          // إضافة المواد الجديدة وحساب التكلفة المقدرة
          for (const material of recipeData.materials) {
            if (material.material_id && material.quantity > 0) {
              const totalCost = (material.cost_per_unit || 0) * material.quantity
              estimatedCost += totalCost

              this.db.prepare(`
                INSERT INTO recipe_materials (
                  recipe_id, material_id, warehouse_id, quantity, unit, cost_per_unit,
                  total_cost, is_optional, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
              `).run(
                recipeId,
                material.material_id,
                material.warehouse_id || null,
                material.quantity,
                material.unit || null,
                material.cost_per_unit || 0,
                totalCost,
                material.is_optional || false,
                material.notes || null
              )
            }
          }

          // تحديث التكلفة المقدرة في الوصفة
          this.db.prepare(`
            UPDATE production_recipes
            SET estimated_cost = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `).run(estimatedCost, recipeId)
        }
      })

      transaction()

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم تحديث وصفة الإنتاج بنجاح',
        data: { id: recipeId }
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في تحديث وصفة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تحديث وصفة الإنتاج' }
    }
  }

  // حذف وصفة الإنتاج
  public async deleteProductionRecipe(recipeId: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود الوصفة
      const existingRecipe = this.db.prepare(`
        SELECT * FROM production_recipes WHERE id = ?
      `).get(recipeId) as any

      if (!existingRecipe) {
        return { success: false, message: 'وصفة الإنتاج غير موجودة' }
      }

      // التحقق من عدم استخدام الوصفة في أوامر إنتاج نشطة
      const activeOrders = this.db.prepare(`
        SELECT COUNT(*) as count FROM production_orders po
        INNER JOIN production_recipes pr ON po.product_id = pr.product_id
        WHERE pr.id = ? AND po.status IN ('pending', 'in_progress')
      `).get(recipeId) as any

      if (activeOrders.count > 0) {
        return { 
          success: false, 
          message: 'لا يمكن حذف الوصفة لأنها مستخدمة في أوامر إنتاج نشطة' 
        }
      }

      // بدء معاملة لحذف الوصفة ومواد الوصفة
      const transaction = this.db.transaction(() => {
        // حذف مواد الوصفة أولاً
        this.db.prepare(`
          DELETE FROM recipe_materials WHERE recipe_id = ?
        `).run(recipeId)

        // حذف الوصفة
        this.db.prepare(`
          DELETE FROM production_recipes WHERE id = ?
        `).run(recipeId)
      })

      transaction()

      return {
        success: true,
        message: 'تم حذف وصفة الإنتاج ومواد الوصفة بنجاح'
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في حذف وصفة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في حذف وصفة الإنتاج' }
    }
  }

  // ===== دوال الدهان =====

  // جلب أنواع الدهانات
  public async getPaintTypes(): Promise<any[]> {
    try {
      const paintTypes = this.db.prepare(`
        SELECT * FROM paint_types
        WHERE is_active = 1
        ORDER BY created_at DESC
      `).all()
      return paintTypes
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب أنواع الدهانات:', error)
      return []
    }
  }

  // إنشاء نوع دهان جديد
  public async createPaintType(paintTypeData: {
    code?: string
    name: string
    description?: string
    price_per_sqm: number
    color_options?: string
    coverage_per_liter?: number
    drying_time?: number
  }): Promise<ApiResponse> {
    try {
      // توليد كود نوع الدهان إذا لم يتم توفيره
      if (!paintTypeData.code) {
        paintTypeData.code = await this.generatePaintTypeCode()
      }

      const result = this.db.prepare(`
        INSERT INTO paint_types (
          code, name, description, price_per_sqm, color_options,
          coverage_per_liter, drying_time
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        paintTypeData.code,
        paintTypeData.name,
        paintTypeData.description || null,
        paintTypeData.price_per_sqm,
        paintTypeData.color_options || null,
        paintTypeData.coverage_per_liter || null,
        paintTypeData.drying_time || null
      )

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إنشاء نوع الدهان بنجاح' }
      } else {
        return { success: false, message: 'فشل في إنشاء نوع الدهان' }
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في إنشاء نوع الدهان:', error)
      if (error instanceof Error && error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود نوع الدهان موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في إنشاء نوع الدهان' }
    }
  }

  // تحديث نوع دهان
  public async updatePaintType(paintTypeId: number, paintTypeData: any): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        UPDATE paint_types SET
          name = ?, description = ?, price_per_sqm = ?, color_options = ?,
          coverage_per_liter = ?, drying_time = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).run(
        paintTypeData.name,
        paintTypeData.description || null,
        paintTypeData.price_per_sqm,
        paintTypeData.color_options || null,
        paintTypeData.coverage_per_liter || null,
        paintTypeData.drying_time || null,
        paintTypeId
      )

      if (result.changes > 0) {
        return { success: true, message: 'تم تحديث نوع الدهان بنجاح' }
      } else {
        return { success: false, message: 'نوع الدهان غير موجود' }
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في تحديث نوع الدهان:', error)
      return { success: false, message: 'حدث خطأ في تحديث نوع الدهان' }
    }
  }

  // حذف نوع دهان
  public async deletePaintType(paintTypeId: number): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        UPDATE paint_types SET is_active = 0 WHERE id = ?
      `).run(paintTypeId)

      if (result.changes > 0) {
        return { success: true, message: 'تم حذف نوع الدهان بنجاح' }
      } else {
        return { success: false, message: 'نوع الدهان غير موجود' }
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في حذف نوع الدهان:', error)
      return { success: false, message: 'حدث خطأ في حذف نوع الدهان' }
    }
  }

  // توليد كود نوع دهان جديد
  public async generatePaintTypeCode(): Promise<string> {
    try {
      const lastPaintType = this.db.prepare(`
        SELECT code FROM paint_types
        WHERE code LIKE 'PAINT%' AND code IS NOT NULL AND code != ''
        ORDER BY CAST(SUBSTR(code, 6) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastPaintType && lastPaintType.code && lastPaintType.code.length >= 8) {
        const codeNumber = lastPaintType.code.substring(5)
        const lastNumber = parseInt(codeNumber)

        if (!isNaN(lastNumber)) {
          const newNumber = lastNumber + 1
          return 'PAINT' + newNumber.toString().padStart(3, '0')
        }
      }

      return 'PAINT001'
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في توليد كود نوع الدهان:', error)
      return 'PAINT' + Date.now().toString().slice(-3)
    }
  }

  // تم إزالة دالة إضافة البيانات التجريبية - يجب استخدام البيانات الحقيقية فقط

  // جلب أوامر الدهان
  public async getPaintOrders(): Promise<any[]> {
    try {
      const orders = this.db.prepare(`
        SELECT po.*, pt.name as paint_type_name, pt.code as paint_type_code,
               c.name as customer_name
        FROM paint_orders po
        LEFT JOIN paint_types pt ON po.paint_type_id = pt.id
        LEFT JOIN customers c ON po.customer_id = c.id
        ORDER BY po.created_at DESC
      `).all()
      return orders
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب أوامر الدهان:', error)
      return []
    }
  }

  // إنشاء أمر دهان جديد
  public async createPaintOrder(orderData: {
    order_number?: string
    paint_type_id: number
    customer_id?: number
    area_sqm: number
    color: string
    quantity_liters?: number
    unit_price?: number
    total_price?: number
    delivery_date?: string
    notes?: string
  }): Promise<ApiResponse> {
    try {
      // توليد رقم أمر الدهان إذا لم يتم توفيره
      if (!orderData.order_number) {
        orderData.order_number = await this.generatePaintOrderNumber()
      }

      // حساب الكمية والسعر إذا لم يتم توفيرهما
      if (!orderData.quantity_liters || !orderData.total_price) {
        const paintType = this.db.prepare(`
          SELECT * FROM paint_types WHERE id = ?
        `).get(orderData.paint_type_id) as any

        if (paintType) {
          if (!orderData.quantity_liters && paintType.coverage_per_liter) {
            orderData.quantity_liters = Math.ceil(orderData.area_sqm / paintType.coverage_per_liter)
          }
          if (!orderData.unit_price) {
            orderData.unit_price = paintType.price_per_sqm
          }
          if (!orderData.total_price) {
            orderData.total_price = orderData.area_sqm * paintType.price_per_sqm
          }
        }
      }

      const result = this.db.prepare(`
        INSERT INTO paint_orders (
          order_number, paint_type_id, customer_id, area_sqm, color,
          quantity_liters, unit_price, total_price, delivery_date, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        orderData.order_number,
        orderData.paint_type_id,
        orderData.customer_id || null,
        orderData.area_sqm,
        orderData.color,
        orderData.quantity_liters || null,
        orderData.unit_price || null,
        orderData.total_price || null,
        orderData.delivery_date || null,
        orderData.notes || null
      )

      if (result.changes > 0) {
        return { success: true, message: 'تم إنشاء أمر الدهان بنجاح' }
      } else {
        return { success: false, message: 'فشل في إنشاء أمر الدهان' }
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في إنشاء أمر الدهان:', error)
      return { success: false, message: 'حدث خطأ في إنشاء أمر الدهان' }
    }
  }

  // تحديث حالة أمر الدهان
  public async updatePaintOrderStatus(orderId: number, status: string): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        UPDATE paint_orders
        SET status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).run(status, orderId)

      if (result.changes > 0) {
        return { success: true, message: 'تم تحديث حالة أمر الدهان بنجاح' }
      } else {
        return { success: false, message: 'أمر الدهان غير موجود' }
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في تحديث حالة أمر الدهان:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة أمر الدهان' }
    }
  }

  // توليد رقم أمر دهان جديد
  public async generatePaintOrderNumber(): Promise<string> {
    try {
      const lastOrder = this.db.prepare(`
        SELECT order_number FROM paint_orders
        WHERE order_number LIKE 'PAINT-ORD%' AND order_number IS NOT NULL AND order_number != ''
        ORDER BY CAST(SUBSTR(order_number, 10) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastOrder && lastOrder.order_number && lastOrder.order_number.length >= 13) {
        const codeNumber = lastOrder.order_number.substring(9)
        const lastNumber = parseInt(codeNumber)

        if (!isNaN(lastNumber)) {
          const newNumber = lastNumber + 1
          return 'PAINT-ORD' + newNumber.toString().padStart(4, '0')
        }
      }

      return 'PAINT-ORD0001'
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في توليد رقم أمر الدهان:', error)
      return 'PAINT-ORD' + Date.now().toString().slice(-4)
    }
  }

  // تقرير كفاءة الإنتاج
  public async getProductionEfficiencyReport(filters: any): Promise<ApiResponse> {
    try {
      const whereConditions = ['po.status = "completed"']
      const params: any[] = []

      if (filters.startDate) {
        whereConditions.push('DATE(po.end_date) >= ?')
        params.push(filters.startDate)
      }

      if (filters.endDate) {
        whereConditions.push('DATE(po.end_date) <= ?')
        params.push(filters.endDate)
      }

      if (filters.department_id) {
        whereConditions.push('po.department_id = ?')
        params.push(filters.department_id)
      }

      const query = `
        SELECT
          po.id as order_id,
          po.order_number,
          i.name as product_name,
          po.estimated_hours,
          po.actual_hours,
          po.estimated_cost,
          po.actual_cost,
          po.status,
          po.end_date as completion_date,
          CASE
            WHEN po.actual_hours > 0 THEN
              ROUND((CAST(po.estimated_hours AS FLOAT) / po.actual_hours) * 100, 2)
            ELSE 0
          END as efficiency_percentage,
          (po.actual_cost - po.estimated_cost) as cost_variance,
          (po.actual_hours - po.estimated_hours) as time_variance
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        WHERE ${whereConditions.join(' AND ')}
        ORDER BY po.end_date DESC
      `

      const results = this.db.prepare(query).all(...params) as any[]

      return {
        success: true,
        data: results
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في تقرير كفاءة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تقرير كفاءة الإنتاج' }
    }
  }

  // ===== إدارة فواتير الدهان =====

  // إنشاء فاتورة دهان جديدة
  public async createPaintInvoice(invoiceData: {
    invoice_number?: string
    order_id?: number
    customer_id: number
    invoice_date: string
    due_date?: string
    total_amount: number
    discount?: number
    tax?: number
    final_amount: number
    payment_method?: string
    notes?: string
    created_by?: number
  }): Promise<ApiResponse> {
    try {
      // توليد رقم فاتورة إذا لم يتم توفيره
      if (!invoiceData.invoice_number) {
        invoiceData.invoice_number = await this.generatePaintInvoiceNumber()
      }

      // حساب المبالغ
      const totalAmount = invoiceData.total_amount || 0
      const discount = invoiceData.discount || 0
      const tax = invoiceData.tax || 0
      const finalAmount = totalAmount - discount + tax

      // حساب المبلغ المدفوع والمتبقي
      const paidAmount = parseFloat(String((invoiceData as any).paid_amount || 0))
      const remainingAmount = finalAmount - paidAmount

      // حساب المساحة الإجمالية من أمر الدهان إذا كان موجوداً
      let totalArea = 0
      if (invoiceData.order_id) {
        const order = this.db.prepare('SELECT area_sqm FROM paint_orders WHERE id = ?').get(invoiceData.order_id) as any
        totalArea = order?.area_sqm || 0
      }

      const result = this.db.prepare(`
        INSERT INTO paint_invoices (
          invoice_number, order_id, customer_id, invoice_date, due_date,
          total_amount, discount, tax, final_amount, paid_amount, remaining_amount,
          payment_method, payment_date, total_area, notes, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        invoiceData.invoice_number,
        invoiceData.order_id || null,
        invoiceData.customer_id,
        invoiceData.invoice_date,
        invoiceData.due_date || null,
        totalAmount,
        discount,
        tax,
        finalAmount,
        paidAmount,
        remainingAmount,
        invoiceData.payment_method || null,
        paidAmount > 0 ? invoiceData.invoice_date : null, // payment_date
        totalArea,
        invoiceData.notes || null,
        invoiceData.created_by || null
      )

      if (result.changes > 0) {
        const invoiceId = result.lastInsertRowid

        // تحديد حالة الفاتورة بناءً على المبلغ المدفوع
        let invoiceStatus = 'pending'
        if (remainingAmount <= 0) {
          invoiceStatus = 'paid'
        } else if (paidAmount > 0) {
          invoiceStatus = 'partial'
        }

        // تحديث حالة الفاتورة
        this.db.prepare(`
          UPDATE paint_invoices
          SET status = ?
          WHERE id = ?
        `).run(invoiceStatus, invoiceId)

        // إدراج تفاصيل فاتورة الدهان إذا كان مرتبطاً بأمر
        if (invoiceData.order_id) {
          const order = this.db.prepare(`
            SELECT po.*, pt.name as paint_type_name
            FROM paint_orders po
            LEFT JOIN paint_types pt ON po.paint_type_id = pt.id
            WHERE po.id = ?
          `).get(invoiceData.order_id) as any

          if (order) {
            this.db.prepare(`
              INSERT INTO paint_invoice_items (
                invoice_id, paint_order_id, area_sqm, unit_price, total_price,
                color, paint_type_id, notes
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `).run(
              invoiceId,
              invoiceData.order_id,
              order.area_sqm,
              order.price_per_sqm || 0,
              order.total_amount || 0,
              order.color,
              order.paint_type_id,
              `أمر دهان رقم ${order.order_number}`
            )
          }

          // تحديث حالة أمر الدهان إلى مفوتر
          this.db.prepare(`
            UPDATE paint_orders
            SET status = 'completed', updated_at = datetime('now')
            WHERE id = ?
          `).run(invoiceData.order_id)
        }

        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()

        return {
          success: true,
          message: 'تم إنشاء فاتورة الدهان بنجاح',
          data: { id: result.lastInsertRowid, invoice_number: invoiceData.invoice_number }
        }
      } else {
        return { success: false, message: 'فشل في إنشاء فاتورة الدهان' }
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في إنشاء فاتورة الدهان:', error)
      return { success: false, message: 'حدث خطأ في إنشاء فاتورة الدهان' }
    }
  }

  // جلب جميع فواتير الدهان
  public async getPaintInvoices(): Promise<ApiResponse> {
    try {
      const invoices = this.db.prepare(`
        SELECT
          pi.*,
          c.name as customer_name,
          c.code as customer_code,
          po.order_number,
          po.area_sqm,
          po.color,
          pt.name as paint_type_name
        FROM paint_invoices pi
        LEFT JOIN customers c ON pi.customer_id = c.id
        LEFT JOIN paint_orders po ON pi.order_id = po.id
        LEFT JOIN paint_types pt ON po.paint_type_id = pt.id
        ORDER BY pi.created_at DESC
      `).all()

      return { success: true, data: invoices }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب فواتير الدهان:', error)
      return { success: false, message: 'حدث خطأ في جلب فواتير الدهان' }
    }
  }

  // تحديث حالة فاتورة الدهان
  public async updatePaintInvoiceStatus(invoiceId: number, status: string): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        UPDATE paint_invoices
        SET status = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run(status, invoiceId)

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث حالة فاتورة الدهان بنجاح' }
      } else {
        return { success: false, message: 'فاتورة الدهان غير موجودة' }
      }
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في تحديث حالة فاتورة الدهان:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة فاتورة الدهان' }
    }
  }

  // توليد رقم فاتورة دهان جديد
  public async generatePaintInvoiceNumber(): Promise<string> {
    try {
      const lastInvoice = this.db.prepare(`
        SELECT invoice_number FROM paint_invoices
        WHERE invoice_number LIKE 'PAINT-INV%'
        ORDER BY CAST(SUBSTR(invoice_number, 10) AS INTEGER) DESC
        LIMIT 1
      `).get()

      let nextNumber = 1
      if (lastInvoice && lastInvoice.invoice_number) {
        const currentNumber = parseInt(lastInvoice.invoice_number.substring(9))
        nextNumber = currentNumber + 1
      }

      return 'PAINT-INV' + nextNumber.toString().padStart(4, '0')
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في توليد رقم فاتورة الدهان:', error)
      return 'PAINT-INV' + Date.now().toString().slice(-4)
    }
  }

  // توليد رقم أمر إنتاج جديد (متزامن)
  private generateProductionOrderNumberSync(): string {
    try {
      const lastOrder = this.db.prepare(`
        SELECT order_number FROM production_orders
        WHERE order_number LIKE 'PROD-%'
        ORDER BY CAST(SUBSTR(order_number, 6) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      let nextNumber = 1
      if (lastOrder && lastOrder.order_number) {
        const currentNumber = parseInt(lastOrder.order_number.substring(5))
        nextNumber = currentNumber + 1
      }

      return 'PROD-' + nextNumber.toString().padStart(4, '0')
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في توليد رقم أمر الإنتاج:', error)
      return 'PROD-' + Date.now().toString().slice(-4)
    }
  }

  // توليد كود أمر إنتاج جديد (متزامن)
  private generateProductionOrderCodeSync(): string {
    try {
      const lastOrder = this.db.prepare(`
        SELECT order_code FROM production_orders
        WHERE order_code LIKE 'PO-%'
        ORDER BY CAST(SUBSTR(order_code, 4) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      let nextNumber = 1
      if (lastOrder && lastOrder.order_code) {
        const currentNumber = parseInt(lastOrder.order_code.substring(3))
        nextNumber = currentNumber + 1
      }

      return 'PO-' + nextNumber.toString().padStart(6, '0')
    } catch (error) {
      Logger.error('ProductionService', 'خطأ في توليد كود أمر الإنتاج:', error)
      return 'PO-' + Date.now().toString().slice(-6)
    }
  }

  // ===== تنفيذ الإنتاج وتحويل المواد =====

  public async executeProduction(productionData: {
    recipe_id: number
    quantity: number
    warehouse_id: number
    notes?: string
  }): Promise<ApiResponse> {
    try {
      // الحصول على تفاصيل الوصفة
      const recipe = this.db.prepare(`
        SELECT pr.*, i.name as product_name, i.code as product_code, i.type as product_type
        FROM production_recipes pr
        LEFT JOIN items i ON pr.product_id = i.id
        WHERE pr.id = ? AND pr.is_active = 1
      `).get(productionData.recipe_id) as any

      if (!recipe) {
        return { success: false, message: 'الوصفة غير موجودة أو غير نشطة' }
      }

      if (!recipe.product_id) {
        return { success: false, message: 'الوصفة لا تحتوي على منتج مرتبط' }
      }

      // الحصول على مواد الوصفة
      const recipeMaterials = this.db.prepare(`
        SELECT rm.*, i.name as material_name, i.code as material_code, i.type as material_type
        FROM recipe_materials rm
        LEFT JOIN items i ON rm.material_id = i.id
        WHERE rm.recipe_id = ?
      `).all(productionData.recipe_id) as any[]

      // التحقق من توفر المواد
      for (const material of recipeMaterials) {
        const requiredQuantity = material.quantity * productionData.quantity

        // التحقق من المخزون
        const inventory = this.db.prepare(`
          SELECT quantity FROM inventory
          WHERE item_id = ? AND warehouse_id = ?
        `).get(material.material_id, productionData.warehouse_id) as any

        const availableQuantity = inventory?.quantity || 0

        if (availableQuantity < requiredQuantity) {
          return {
            success: false,
            message: `كمية غير كافية من المادة: ${material.material_name}. المطلوب: ${requiredQuantity}, المتوفر: ${availableQuantity}`
          }
        }
      }

      // توليد رقم أمر إنتاج جديد
      const orderNumber = this.generateProductionOrderNumberSync()
      Logger.info('ProductionService', `توليد رقم أمر إنتاج: ${orderNumber}`)

      // بدء المعاملة
      const transaction = this.db.transaction(() => {
        // خصم المواد الخام من المخزون
        for (const material of recipeMaterials) {
          const requiredQuantity = material.quantity * productionData.quantity

          // تحديث المخزون
          this.db.prepare(`
            UPDATE inventory
            SET quantity = quantity - ?,
                last_updated = datetime('now')
            WHERE item_id = ? AND warehouse_id = ?
          `).run(requiredQuantity, material.material_id, productionData.warehouse_id)

          // تسجيل حركة المخزون (خروج)
          this.db.prepare(`
            INSERT INTO inventory_movements (
              item_id, warehouse_id, movement_type, quantity,
              reference_type, reference_id, notes, created_at
            ) VALUES (?, ?, 'out', ?, 'production', ?, ?, datetime('now'))
          `).run(
            material.material_id,
            productionData.warehouse_id,
            requiredQuantity,
            productionData.recipe_id,
            `استهلاك في إنتاج: ${recipe.product_name} - ${productionData.notes || ''}`
          )
        }

        // إضافة المنتج النهائي إلى المخزون
        if (recipe.product_id) {
          // التحقق من وجود سجل مخزون للمنتج
          const existingInventory = this.db.prepare(`
            SELECT id FROM inventory
            WHERE item_id = ? AND warehouse_id = ?
          `).get(recipe.product_id, productionData.warehouse_id)

          if (existingInventory) {
            // تحديث الكمية الموجودة
            this.db.prepare(`
              UPDATE inventory
              SET quantity = quantity + ?,
                  last_updated = datetime('now')
              WHERE item_id = ? AND warehouse_id = ?
            `).run(productionData.quantity, recipe.product_id, productionData.warehouse_id)
          } else {
            // إنشاء سجل مخزون جديد
            this.db.prepare(`
              INSERT INTO inventory (
                item_id, warehouse_id, quantity, last_updated
              ) VALUES (?, ?, ?, datetime('now'))
            `).run(recipe.product_id, productionData.warehouse_id, productionData.quantity)
          }

          // تسجيل حركة المخزون (دخول)
          this.db.prepare(`
            INSERT INTO inventory_movements (
              item_id, warehouse_id, movement_type, quantity,
              reference_type, reference_id, notes, created_at
            ) VALUES (?, ?, 'in', ?, 'production', ?, ?, datetime('now'))
          `).run(
            recipe.product_id,
            productionData.warehouse_id,
            productionData.quantity,
            productionData.recipe_id,
            `إنتاج: ${recipe.product_name} - ${productionData.notes || ''}`
          )
        }

        // تسجيل عملية الإنتاج
        const productionResult = this.db.prepare(`
          INSERT INTO production_orders (
            order_number, product_id, recipe_id, quantity, warehouse_id, status,
            completion_date, notes, created_at
          ) VALUES (?, ?, ?, ?, ?, 'completed', datetime('now'), ?, datetime('now'))
        `).run(
          orderNumber,
          recipe.product_id,
          productionData.recipe_id,
          productionData.quantity,
          productionData.warehouse_id,
          productionData.notes || null
        )

        Logger.info('ProductionService', `تم إنشاء أمر الإنتاج برقم: ${orderNumber} (ID: ${productionResult.lastInsertRowid})`)

        return productionResult.lastInsertRowid
      })

      const productionOrderId = transaction()

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: `تم تنفيذ الإنتاج بنجاح - رقم الأمر: ${orderNumber}`,
        data: {
          production_order_id: productionOrderId,
          order_number: orderNumber,
          produced_quantity: productionData.quantity,
          product_name: recipe.name
        }
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تنفيذ الإنتاج:', error)
      return { success: false, message: error.message || 'حدث خطأ في تنفيذ الإنتاج' }
    }
  }

  // تقرير تكاليف الإنتاج
  public async getProductionCostsReport(filters: any): Promise<ApiResponse> {
    try {
      const whereConditions = ['1=1']
      const params: any[] = []

      if (filters.dateRange && filters.dateRange[0]) {
        whereConditions.push('DATE(po.created_at) >= ?')
        params.push(filters.dateRange[0])
      }

      if (filters.dateRange && filters.dateRange[1]) {
        whereConditions.push('DATE(po.created_at) <= ?')
        params.push(filters.dateRange[1])
      }

      if (filters.department) {
        whereConditions.push('po.department_id = ?')
        params.push(filters.department)
      }

      if (filters.orderId) {
        whereConditions.push('po.id = ?')
        params.push(filters.orderId)
      }

      const query = `
        SELECT
          po.id as order_id,
          po.order_number,
          i.name as item_name,
          i.code as item_code,
          i.unit,
          pd.name as department_name,
          po.quantity,
          po.estimated_cost,
          po.actual_cost,
          COALESCE(material_costs.total, 0) as material_costs,
          COALESCE(labor_costs.total, 0) as labor_costs,
          COALESCE(overhead_costs.total, 0) as overhead_costs,
          (COALESCE(material_costs.total, 0) + COALESCE(labor_costs.total, 0) + COALESCE(overhead_costs.total, 0)) as total_costs,
          po.status,
          po.created_at
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        LEFT JOIN production_departments pd ON po.department_id = pd.id
        LEFT JOIN (
          SELECT
            po.id,
            SUM(rm.quantity * rm.cost_per_unit * po.quantity) as total
          FROM production_orders po
          JOIN production_recipes pr ON po.recipe_id = pr.id
          JOIN recipe_materials rm ON pr.id = rm.recipe_id
          GROUP BY po.id
        ) material_costs ON po.id = material_costs.id
        LEFT JOIN (
          SELECT
            po.id,
            SUM(ps.estimated_hours * ps.labor_cost_per_hour) as total
          FROM production_orders po
          JOIN production_stages ps ON po.department_id = ps.department_id
          GROUP BY po.id
        ) labor_costs ON po.id = labor_costs.id
        LEFT JOIN (
          SELECT
            po.id,
            (po.estimated_cost * COALESCE(ps.overhead_rate, 15) / 100) as total
          FROM production_orders po
          LEFT JOIN production_settings ps ON ps.id = 1
        ) overhead_costs ON po.id = overhead_costs.id
        WHERE ${whereConditions.join(' AND ')}
        ORDER BY po.created_at DESC
      `

      const data = this.db.prepare(query).all(params) as any[]

      return {
        success: true,
        data: { data },
        message: 'تم جلب تقرير تكاليف الإنتاج بنجاح'
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تقرير تكاليف الإنتاج:', error)
      return { success: false, message: error.message || 'حدث خطأ في تقرير تكاليف الإنتاج' }
    }
  }

  // تقرير جدولة الإنتاج
  public async getProductionScheduleReport(filters: any): Promise<ApiResponse> {
    try {
      const whereConditions = ['1=1']
      const params: any[] = []

      if (filters.dateRange && filters.dateRange[0]) {
        whereConditions.push('DATE(po.start_date) >= ?')
        params.push(filters.dateRange[0])
      }

      if (filters.dateRange && filters.dateRange[1]) {
        whereConditions.push('DATE(po.expected_completion_date) <= ?')
        params.push(filters.dateRange[1])
      }

      if (filters.department) {
        whereConditions.push('po.department_id = ?')
        params.push(filters.department)
      }

      if (filters.status) {
        whereConditions.push('po.status = ?')
        params.push(filters.status)
      }

      const query = `
        SELECT
          po.id as order_id,
          po.order_number,
          i.name as item_name,
          i.code as item_code,
          pd.name as department_name,
          c.name as customer_name,
          po.quantity,
          po.start_date,
          po.expected_completion_date,
          po.actual_completion_date,
          po.status,
          po.priority,
          CASE
            WHEN po.status = 'completed' AND po.actual_completion_date <= po.expected_completion_date THEN 'on_time'
            WHEN po.status = 'completed' AND po.actual_completion_date > po.expected_completion_date THEN 'delayed'
            WHEN po.status != 'completed' AND DATE('now') > po.expected_completion_date THEN 'delayed'
            WHEN po.status != 'completed' AND DATE('now') <= po.expected_completion_date THEN 'on_time'
            ELSE 'pending'
          END as schedule_status,
          CASE
            WHEN po.status = 'completed' THEN 100
            WHEN po.status = 'in_progress' THEN 50
            WHEN po.status = 'pending' THEN 0
            ELSE 0
          END as progress_percentage,
          po.estimated_hours,
          po.actual_hours,
          po.created_at
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        LEFT JOIN production_departments pd ON po.department_id = pd.id
        LEFT JOIN customers c ON po.customer_id = c.id
        WHERE ${whereConditions.join(' AND ')}
        ORDER BY po.expected_completion_date ASC, po.priority DESC
      `

      const data = this.db.prepare(query).all(params) as any[]

      return {
        success: true,
        data: { data },
        message: 'تم جلب تقرير جدولة الإنتاج بنجاح'
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تقرير جدولة الإنتاج:', error)
      return { success: false, message: error.message || 'حدث خطأ في تقرير جدولة الإنتاج' }
    }
  }

  // تقرير جودة الإنتاج
  public async getProductionQualityReport(filters: any): Promise<ApiResponse> {
    try {
      const whereConditions = ['1=1']
      const params: any[] = []

      if (filters.dateRange && filters.dateRange[0]) {
        whereConditions.push('po.created_at >= ?')
        params.push(filters.dateRange[0])
      }

      if (filters.dateRange && filters.dateRange[1]) {
        whereConditions.push('po.created_at <= ?')
        params.push(filters.dateRange[1])
      }

      if (filters.department) {
        whereConditions.push('d.id = ?')
        params.push(filters.department)
      }

      const query = `
        SELECT
          po.id as order_id,
          po.order_number,
          i.name as item_name,
          d.name as department_name,
          c.name as customer_name,
          po.quantity,
          po.status,
          po.quality_score,
          po.defect_count,
          po.rework_count,
          po.inspection_notes,
          po.quality_rating,
          po.created_at,
          po.completed_at
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        LEFT JOIN departments d ON po.department_id = d.id
        LEFT JOIN customers c ON po.customer_id = c.id
        WHERE ${whereConditions.join(' AND ')}
        ORDER BY po.created_at DESC
      `

      const data = this.db.prepare(query).all(...params)

      return { success: true, data: { data }, message: 'تم جلب تقرير جودة الإنتاج بنجاح' }
    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تقرير جودة الإنتاج:', error)
      return { success: false, message: error.message || 'حدث خطأ في تقرير جودة الإنتاج' }
    }
  }

  // تقرير أداء العمال في الإنتاج
  public async getProductionWorkersPerformanceReport(filters: any): Promise<ApiResponse> {
    try {
      const whereConditions = ['1=1']
      const params: any[] = []

      if (filters.dateRange && filters.dateRange[0]) {
        whereConditions.push('po.created_at >= ?')
        params.push(filters.dateRange[0])
      }

      if (filters.dateRange && filters.dateRange[1]) {
        whereConditions.push('po.created_at <= ?')
        params.push(filters.dateRange[1])
      }

      if (filters.department) {
        whereConditions.push('d.id = ?')
        params.push(filters.department)
      }

      if (filters.employee) {
        whereConditions.push('e.id = ?')
        params.push(filters.employee)
      }

      const query = `
        SELECT
          e.id as employee_id,
          e.name as employee_name,
          d.name as department_name,
          COUNT(po.id) as total_orders,
          COUNT(CASE WHEN po.status = 'completed' THEN 1 END) as completed_orders,
          AVG(po.quality_score) as avg_quality_score,
          AVG(po.efficiency_score) as avg_efficiency_score,
          SUM(po.quantity) as total_quantity_produced,
          AVG(JULIANDAY(po.completed_at) - JULIANDAY(po.start_date)) as avg_completion_days
        FROM production_orders po
        LEFT JOIN employees e ON po.assigned_employee_id = e.id
        LEFT JOIN departments d ON po.department_id = d.id
        WHERE ${whereConditions.join(' AND ')} AND e.id IS NOT NULL
        GROUP BY e.id, e.name, d.name
        ORDER BY avg_efficiency_score DESC
      `

      const data = this.db.prepare(query).all(...params)

      return { success: true, data: { data }, message: 'تم جلب تقرير أداء العمال بنجاح' }
    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تقرير أداء العمال:', error)
      return { success: false, message: error.message || 'حدث خطأ في تقرير أداء العمال' }
    }
  }

  // تقرير استهلاك المواد في الإنتاج
  public async getProductionMaterialsConsumptionReport(filters: any): Promise<ApiResponse> {
    try {
      const whereConditions = ['1=1']
      const params: any[] = []

      if (filters.dateRange && filters.dateRange[0]) {
        whereConditions.push('pom.created_at >= ?')
        params.push(filters.dateRange[0])
      }

      if (filters.dateRange && filters.dateRange[1]) {
        whereConditions.push('pom.created_at <= ?')
        params.push(filters.dateRange[1])
      }

      if (filters.department) {
        whereConditions.push('d.id = ?')
        params.push(filters.department)
      }

      if (filters.item) {
        whereConditions.push('i.id = ?')
        params.push(filters.item)
      }

      const query = `
        SELECT
          i.id as material_id,
          i.name as material_name,
          i.code as material_code,
          d.name as department_name,
          SUM(pom.quantity_used) as total_consumed,
          AVG(pom.quantity_used) as avg_consumption,
          COUNT(DISTINCT po.id) as orders_count,
          SUM(pom.quantity_used * i.cost_price) as total_cost,
          AVG(pom.quantity_used * i.cost_price) as avg_cost_per_order
        FROM production_order_materials pom
        LEFT JOIN items i ON pom.material_id = i.id
        LEFT JOIN production_orders po ON pom.production_order_id = po.id
        LEFT JOIN departments d ON po.department_id = d.id
        WHERE ${whereConditions.join(' AND ')}
        GROUP BY i.id, i.name, i.code, d.name
        ORDER BY total_consumed DESC
      `

      const data = this.db.prepare(query).all(...params)

      return { success: true, data: { data }, message: 'تم جلب تقرير استهلاك المواد بنجاح' }
    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تقرير استهلاك المواد:', error)
      return { success: false, message: error.message || 'حدث خطأ في تقرير استهلاك المواد' }
    }
  }

  // تقرير ربحية الإنتاج
  public async getProductionProfitabilityReport(filters: any): Promise<ApiResponse> {
    try {
      const whereConditions = ['po.status = "completed"']
      const params: any[] = []

      if (filters.dateRange && filters.dateRange[0]) {
        whereConditions.push('po.completed_at >= ?')
        params.push(filters.dateRange[0])
      }

      if (filters.dateRange && filters.dateRange[1]) {
        whereConditions.push('po.completed_at <= ?')
        params.push(filters.dateRange[1])
      }

      if (filters.department) {
        whereConditions.push('d.id = ?')
        params.push(filters.department)
      }

      if (filters.customer) {
        whereConditions.push('c.id = ?')
        params.push(filters.customer)
      }

      if (filters.item) {
        whereConditions.push('i.id = ?')
        params.push(filters.item)
      }

      const query = `
        SELECT
          po.id as order_id,
          po.order_number,
          i.name as item_name,
          c.name as customer_name,
          d.name as department_name,
          po.quantity,
          po.selling_price,
          po.total_cost,
          (po.selling_price - po.total_cost) as profit,
          CASE
            WHEN po.selling_price > 0 THEN ((po.selling_price - po.total_cost) / po.selling_price * 100)
            ELSE 0
          END as profit_margin,
          po.completed_at
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        LEFT JOIN customers c ON po.customer_id = c.id
        LEFT JOIN departments d ON po.department_id = d.id
        WHERE ${whereConditions.join(' AND ')}
        ORDER BY profit_margin DESC
      `

      const data = this.db.prepare(query).all(...params)

      return { success: true, data: { data }, message: 'تم جلب تقرير ربحية الإنتاج بنجاح' }
    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تقرير ربحية الإنتاج:', error)
      return { success: false, message: error.message || 'حدث خطأ في تقرير ربحية الإنتاج' }
    }
  }

  /**
   * تقرير الدهان حسب العميل
   */
  public async getPaintByCustomerReport(filters: {
    customerId?: number
    dateRange?: [string, string]
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}): Promise<ApiResponse> {
    try {
      Logger.info('ProductionService', 'إنشاء تقرير الدهان حسب العميل', filters)

      const whereConditions = ['1=1']
      const params: any[] = []

      // فلترة حسب العميل
      if (filters.customerId) {
        whereConditions.push('po.customer_id = ?')
        params.push(filters.customerId)
      }

      // فلترة حسب التاريخ
      if (filters.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
        whereConditions.push('po.created_at BETWEEN ? AND ?')
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      // ترتيب النتائج
      const sortBy = filters.sortBy || 'total_amount'
      const sortOrder = filters.sortOrder || 'desc'

      const query = `
        SELECT
          c.id as customer_id,
          c.name as customer_name,
          c.code as customer_code,
          c.phone as customer_phone,
          c.email as customer_email,
          COUNT(DISTINCT po.id) as total_orders,
          COUNT(DISTINCT pi.id) as total_invoices,
          COALESCE(SUM(po.total_price), 0) as total_amount,
          COALESCE(SUM(pi.paid_amount), 0) as paid_amount,
          COALESCE(SUM(pi.final_amount - pi.paid_amount), 0) as outstanding_amount,
          COALESCE(AVG(po.total_price), 0) as avg_order_amount,
          COALESCE(SUM(po.area_sqm), 0) as total_area,
          MAX(po.created_at) as last_order_date,
          MIN(po.created_at) as first_order_date,
          COUNT(CASE WHEN po.status = 'completed' THEN 1 END) as completed_orders,
          COUNT(CASE WHEN po.status = 'pending' THEN 1 END) as pending_orders,
          COUNT(CASE WHEN po.status = 'in_progress' THEN 1 END) as in_progress_orders
        FROM customers c
        LEFT JOIN paint_orders po ON c.id = po.customer_id
        LEFT JOIN paint_invoices pi ON po.id = pi.order_id
        WHERE ${whereConditions.join(' AND ')}
        GROUP BY c.id, c.name, c.code, c.phone, c.email
        HAVING COUNT(po.id) > 0
        ORDER BY ${sortBy} ${sortOrder}
      `

      const results = this.db.prepare(query).all(...params) as any[]

      // تحسين البيانات للعرض
      const processedData = results.map(row => ({
        ...row,
        total_amount: Math.round(row.total_amount * 100) / 100,
        paid_amount: Math.round(row.paid_amount * 100) / 100,
        outstanding_amount: Math.round(row.outstanding_amount * 100) / 100,
        avg_order_amount: Math.round(row.avg_order_amount * 100) / 100,
        total_area: Math.round(row.total_area * 100) / 100,
        payment_percentage: row.total_amount > 0 ?
          Math.round((row.paid_amount / row.total_amount) * 100 * 100) / 100 : 0,
        last_order_date: row.last_order_date || '',
        first_order_date: row.first_order_date || ''
      }))

      Logger.info('ProductionService', `تم إنشاء تقرير الدهان حسب العميل: ${processedData.length} عميل`)

      return {
        success: true,
        data: { data: processedData },
        message: `تم جلب تقرير الدهان لـ ${processedData.length} عميل`
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تقرير الدهان حسب العميل:', error)
      return {
        success: false,
        message: error.message || 'حدث خطأ في إنشاء تقرير الدهان حسب العميل'
      }
    }
  }

  /**
   * تقرير الدهان حسب النوع
   */
  public async getPaintByTypeReport(filters: {
    paintTypeId?: number
    dateRange?: [string, string]
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}): Promise<ApiResponse> {
    try {
      Logger.info('ProductionService', 'إنشاء تقرير الدهان حسب النوع', filters)

      const whereConditions = ['1=1']
      const params: any[] = []

      // فلترة حسب نوع الدهان
      if (filters.paintTypeId) {
        whereConditions.push('po.paint_type_id = ?')
        params.push(filters.paintTypeId)
      }

      // فلترة حسب التاريخ
      if (filters.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
        whereConditions.push('po.created_at BETWEEN ? AND ?')
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      // ترتيب النتائج
      const sortBy = filters.sortBy || 'total_amount'
      const sortOrder = filters.sortOrder || 'desc'

      const query = `
        SELECT
          pt.id as paint_type_id,
          pt.name as paint_type_name,
          pt.code as paint_type_code,
          pt.price_per_sqm,
          COUNT(DISTINCT po.id) as total_orders,
          COALESCE(SUM(po.total_price), 0) as total_amount,
          COALESCE(SUM(po.area_sqm), 0) as total_area,
          COALESCE(AVG(po.total_price), 0) as avg_order_amount,
          COUNT(DISTINCT po.customer_id) as unique_customers,
          MAX(po.created_at) as last_order_date,
          MIN(po.created_at) as first_order_date,
          COUNT(CASE WHEN po.status = 'completed' THEN 1 END) as completed_orders,
          COUNT(CASE WHEN po.status = 'pending' THEN 1 END) as pending_orders
        FROM paint_types pt
        LEFT JOIN paint_orders po ON pt.id = po.paint_type_id
        WHERE ${whereConditions.join(' AND ')}
        GROUP BY pt.id, pt.name, pt.code, pt.price_per_sqm
        HAVING COUNT(po.id) > 0
        ORDER BY ${sortBy} ${sortOrder}
      `

      const results = this.db.prepare(query).all(...params) as any[]

      // تحسين البيانات للعرض
      const processedData = results.map(row => ({
        ...row,
        total_amount: Math.round(row.total_amount * 100) / 100,
        avg_order_amount: Math.round(row.avg_order_amount * 100) / 100,
        total_area: Math.round(row.total_area * 100) / 100,
        price_per_sqm: Math.round(row.price_per_sqm * 100) / 100,
        last_order_date: row.last_order_date || '',
        first_order_date: row.first_order_date || ''
      }))

      Logger.info('ProductionService', `تم إنشاء تقرير الدهان حسب النوع: ${processedData.length} نوع`)

      return {
        success: true,
        data: { data: processedData },
        message: `تم جلب تقرير الدهان لـ ${processedData.length} نوع`
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تقرير الدهان حسب النوع:', error)
      return {
        success: false,
        message: error.message || 'حدث خطأ في إنشاء تقرير الدهان حسب النوع'
      }
    }
  }

  /**
   * تقرير الدهان الشهري
   */
  public async getMonthlyPaintReport(filters: {
    year?: number
    month?: number
    dateRange?: [string, string]
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}): Promise<ApiResponse> {
    try {
      Logger.info('ProductionService', 'إنشاء تقرير الدهان الشهري', filters)

      const whereConditions = ['1=1']
      const params: any[] = []

      // فلترة حسب السنة والشهر
      if (filters.year && filters.month) {
        whereConditions.push("strftime('%Y', po.order_date) = ? AND strftime('%m', po.order_date) = ?")
        params.push(filters.year.toString(), filters.month.toString().padStart(2, '0'))
      } else if (filters.year) {
        whereConditions.push("strftime('%Y', po.order_date) = ?")
        params.push(filters.year.toString())
      }

      // فلترة حسب التاريخ
      if (filters.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
        whereConditions.push('po.order_date BETWEEN ? AND ?')
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      // ترتيب النتائج
      const sortBy = filters.sortBy || 'month_year'
      const sortOrder = filters.sortOrder || 'desc'

      const query = `
        SELECT
          strftime('%Y-%m', po.order_date) as month_year,
          strftime('%Y', po.order_date) as year,
          strftime('%m', po.order_date) as month,
          COUNT(DISTINCT po.id) as total_orders,
          COUNT(DISTINCT po.customer_id) as unique_customers,
          COALESCE(SUM(po.area_sqm), 0) as total_area,
          COALESCE(SUM(po.total_amount), 0) as total_revenue,
          COALESCE(AVG(po.total_amount), 0) as avg_order_amount,
          COALESCE(AVG(po.area_sqm), 0) as avg_area_per_order,
          COUNT(CASE WHEN po.status = 'completed' THEN 1 END) as completed_orders,
          COUNT(CASE WHEN po.status = 'pending' THEN 1 END) as pending_orders,
          COUNT(CASE WHEN po.status = 'in_progress' THEN 1 END) as in_progress_orders,
          ROUND(
            CASE
              WHEN COUNT(po.id) > 0 THEN
                (COUNT(CASE WHEN po.status = 'completed' THEN 1 END) * 100.0 / COUNT(po.id))
              ELSE 0
            END, 2
          ) as completion_rate
        FROM paint_orders po
        WHERE ${whereConditions.join(' AND ')}
        GROUP BY strftime('%Y-%m', po.order_date)
        ORDER BY ${sortBy} ${sortOrder}
      `

      const results = this.db.prepare(query).all(...params) as any[]

      // تحسين البيانات للعرض
      const processedData = results.map(row => ({
        ...row,
        total_area: Math.round(row.total_area * 100) / 100,
        total_revenue: Math.round(row.total_revenue * 100) / 100,
        avg_order_amount: Math.round(row.avg_order_amount * 100) / 100,
        avg_area_per_order: Math.round(row.avg_area_per_order * 100) / 100,
        month_name: this.getMonthName(parseInt(row.month)),
        growth_rate: 0 // سيتم حسابه لاحقاً
      }))

      // حساب معدل النمو الشهري
      for (let i = 1; i < processedData.length; i++) {
        const current = processedData[i].total_revenue
        const previous = processedData[i - 1].total_revenue
        if (previous > 0) {
          processedData[i].growth_rate = Math.round(((current - previous) / previous) * 100 * 100) / 100
        }
      }

      Logger.info('ProductionService', `تم إنشاء تقرير الدهان الشهري: ${processedData.length} شهر`)

      return {
        success: true,
        data: processedData,
        message: `تم جلب تقرير الدهان الشهري لـ ${processedData.length} شهر`
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تقرير الدهان الشهري:', error)
      return {
        success: false,
        message: error.message || 'حدث خطأ في إنشاء تقرير الدهان الشهري'
      }
    }
  }

  /**
   * تقرير ربحية الدهان
   */
  public async getPaintProfitabilityReport(filters: {
    paintTypeId?: number
    customerId?: number
    dateRange?: [string, string]
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}): Promise<ApiResponse> {
    try {
      Logger.info('ProductionService', 'إنشاء تقرير ربحية الدهان', filters)

      const whereConditions = ['1=1']
      const params: any[] = []

      // فلترة حسب نوع الدهان
      if (filters.paintTypeId) {
        whereConditions.push('po.paint_type_id = ?')
        params.push(filters.paintTypeId)
      }

      // فلترة حسب العميل
      if (filters.customerId) {
        whereConditions.push('po.customer_id = ?')
        params.push(filters.customerId)
      }

      // فلترة حسب التاريخ
      if (filters.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
        whereConditions.push('po.order_date BETWEEN ? AND ?')
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      // ترتيب النتائج
      const sortBy = filters.sortBy || 'total_profit'
      const sortOrder = filters.sortOrder || 'desc'

      const query = `
        SELECT
          pt.id as paint_type_id,
          pt.code as paint_type_code,
          pt.name as paint_type_name,
          pt.price_per_sqm as standard_price,
          COUNT(DISTINCT po.id) as total_orders,
          COUNT(DISTINCT po.customer_id) as unique_customers,
          COALESCE(SUM(po.area_sqm), 0) as total_area,
          COALESCE(SUM(po.total_amount), 0) as total_revenue,
          COALESCE(SUM(po.area_sqm * COALESCE(pt.cost_per_sqm, 0)), 0) as total_cost,
          COALESCE(SUM(po.total_amount) - SUM(po.area_sqm * COALESCE(pt.cost_per_sqm, 0)), 0) as total_profit,
          COALESCE(AVG(po.price_per_sqm), 0) as avg_selling_price,
          COALESCE(pt.cost_per_sqm, 0) as cost_per_sqm,
          CASE
            WHEN COALESCE(SUM(po.total_amount), 0) > 0 THEN
              ROUND(((COALESCE(SUM(po.total_amount) - SUM(po.area_sqm * COALESCE(pt.cost_per_sqm, 0)), 0)) / COALESCE(SUM(po.total_amount), 1)) * 100, 2)
            ELSE 0
          END as profit_margin_percentage,
          CASE
            WHEN COALESCE(pt.cost_per_sqm, 0) > 0 THEN
              ROUND(((COALESCE(AVG(po.price_per_sqm), 0) - COALESCE(pt.cost_per_sqm, 0)) / COALESCE(pt.cost_per_sqm, 1)) * 100, 2)
            ELSE 0
          END as markup_percentage
        FROM paint_types pt
        LEFT JOIN paint_orders po ON pt.id = po.paint_type_id
        WHERE ${whereConditions.join(' AND ')}
        GROUP BY pt.id, pt.code, pt.name, pt.price_per_sqm, pt.cost_per_sqm
        HAVING COUNT(po.id) > 0
        ORDER BY ${sortBy} ${sortOrder}
      `

      const results = this.db.prepare(query).all(...params) as any[]

      // تحسين البيانات للعرض
      const processedData = results.map(row => ({
        ...row,
        total_area: Math.round(row.total_area * 100) / 100,
        total_revenue: Math.round(row.total_revenue * 100) / 100,
        total_cost: Math.round(row.total_cost * 100) / 100,
        total_profit: Math.round(row.total_profit * 100) / 100,
        avg_selling_price: Math.round(row.avg_selling_price * 100) / 100,
        cost_per_sqm: Math.round(row.cost_per_sqm * 100) / 100,
        profit_per_sqm: Math.round((row.avg_selling_price - row.cost_per_sqm) * 100) / 100,
        roi_percentage: row.cost_per_sqm > 0 ?
          Math.round(((row.avg_selling_price - row.cost_per_sqm) / row.cost_per_sqm) * 100 * 100) / 100 : 0
      }))

      Logger.info('ProductionService', `تم إنشاء تقرير ربحية الدهان: ${processedData.length} نوع دهان`)

      return {
        success: true,
        data: processedData,
        message: `تم جلب تقرير ربحية الدهان لـ ${processedData.length} نوع دهان`
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تقرير ربحية الدهان:', error)
      return {
        success: false,
        message: error.message || 'حدث خطأ في إنشاء تقرير ربحية الدهان'
      }
    }
  }

  /**
   * دالة مساعدة للحصول على اسم الشهر
   */
  private getMonthName(month: number): string {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ]
    return months[month - 1] || ''
  }

  // ===== إدارة أوامر صرف المواد =====

  // إنشاء أمر صرف مواد جديد
  public async createMaterialIssueOrder(orderData: {
    production_order_id: number
    warehouse_id: number
    materials: {
      material_id: number
      requested_quantity: number
      unit_price: number
    }[]
    notes?: string
    requested_by?: number
  }): Promise<ApiResponse> {
    try {
      // التحقق من وجود أمر الإنتاج
      const productionOrder = this.db.prepare('SELECT * FROM production_orders WHERE id = ?').get(orderData.production_order_id) as any
      if (!productionOrder) {
        return { success: false, message: 'أمر الإنتاج غير موجود' }
      }

      // التحقق من وجود المخزن
      const warehouse = this.db.prepare('SELECT * FROM warehouses WHERE id = ? AND is_active = 1').get(orderData.warehouse_id) as any
      if (!warehouse) {
        return { success: false, message: 'المخزن غير موجود أو غير نشط' }
      }

      // إنشاء رقم أمر الصرف
      const orderNumber = await this.generateMaterialIssueOrderNumber()

      // بدء معاملة قاعدة البيانات
      const transaction = this.db.transaction(() => {
        // إدراج أمر الصرف
        const issueOrderResult = this.db.prepare(`
          INSERT INTO material_issue_orders (
            order_number, production_order_id, warehouse_id,
            requested_by, notes
          ) VALUES (?, ?, ?, ?, ?)
        `).run(
          orderNumber,
          orderData.production_order_id,
          orderData.warehouse_id,
          orderData.requested_by || null,
          orderData.notes || null
        )

        const issueOrderId = issueOrderResult.lastInsertRowid

        // إدراج تفاصيل المواد
        for (const material of orderData.materials) {
          const totalCost = material.requested_quantity * material.unit_price

          this.db.prepare(`
            INSERT INTO material_issue_items (
              issue_order_id, material_id, requested_quantity,
              unit_price, total_cost
            ) VALUES (?, ?, ?, ?, ?)
          `).run(
            issueOrderId,
            material.material_id,
            material.requested_quantity,
            material.unit_price,
            totalCost
          )
        }

        return issueOrderId
      })

      const issueOrderId = transaction()

      // حفظ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم إنشاء أمر صرف المواد بنجاح',
        data: { id: issueOrderId, order_number: orderNumber }
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في إنشاء أمر صرف المواد:', error)
      return { success: false, message: 'حدث خطأ في إنشاء أمر صرف المواد' }
    }
  }

  // توليد رقم أمر صرف المواد
  private async generateMaterialIssueOrderNumber(): Promise<string> {
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')

    // البحث عن آخر رقم في نفس الشهر
    const lastOrder = this.db.prepare(`
      SELECT order_number FROM material_issue_orders
      WHERE order_number LIKE ?
      ORDER BY id DESC LIMIT 1
    `).get(`MI-${year}${month}-%`) as any

    let sequence = 1
    if (lastOrder) {
      const lastSequence = parseInt(lastOrder.order_number.split('-')[2])
      sequence = lastSequence + 1
    }

    return `MI-${year}${month}-${String(sequence).padStart(4, '0')}`
  }

  // الحصول على أوامر صرف المواد
  public async getMaterialIssueOrders(filters: {
    production_order_id?: number
    warehouse_id?: number
    status?: string
  } = {}): Promise<ApiResponse> {
    try {
      const whereConditions = ['1=1']
      const params: any[] = []

      if (filters.production_order_id) {
        whereConditions.push('mio.production_order_id = ?')
        params.push(filters.production_order_id)
      }

      if (filters.warehouse_id) {
        whereConditions.push('mio.warehouse_id = ?')
        params.push(filters.warehouse_id)
      }

      if (filters.status) {
        whereConditions.push('mio.status = ?')
        params.push(filters.status)
      }

      const orders = this.db.prepare(`
        SELECT
          mio.*,
          po.order_number as production_order_number,
          w.name as warehouse_name,
          u1.full_name as requested_by_name,
          u2.full_name as approved_by_name,
          u3.full_name as issued_by_name
        FROM material_issue_orders mio
        LEFT JOIN production_orders po ON mio.production_order_id = po.id
        LEFT JOIN warehouses w ON mio.warehouse_id = w.id
        LEFT JOIN users u1 ON mio.requested_by = u1.id
        LEFT JOIN users u2 ON mio.approved_by = u2.id
        LEFT JOIN users u3 ON mio.issued_by = u3.id
        WHERE ${whereConditions.join(' AND ')}
        ORDER BY mio.created_at DESC
      `).all(params) as any[]

      return {
        success: true,
        data: orders
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب أوامر صرف المواد:', error)
      return { success: false, message: 'حدث خطأ في جلب أوامر صرف المواد' }
    }
  }

  // الحصول على تفاصيل أمر صرف المواد
  public async getMaterialIssueOrderDetails(issueOrderId: number): Promise<ApiResponse> {
    try {
      const orderDetails = this.db.prepare(`
        SELECT
          mio.*,
          po.order_number as production_order_number,
          w.name as warehouse_name
        FROM material_issue_orders mio
        LEFT JOIN production_orders po ON mio.production_order_id = po.id
        LEFT JOIN warehouses w ON mio.warehouse_id = w.id
        WHERE mio.id = ?
      `).get(issueOrderId) as any

      if (!orderDetails) {
        return { success: false, message: 'أمر صرف المواد غير موجود' }
      }

      const items = this.db.prepare(`
        SELECT
          mii.*,
          i.name as material_name,
          i.code as material_code,
          i.unit
        FROM material_issue_items mii
        LEFT JOIN items i ON mii.material_id = i.id
        WHERE mii.issue_order_id = ?
        ORDER BY mii.id
      `).all(issueOrderId) as any[]

      return {
        success: true,
        data: {
          ...orderDetails,
          items
        }
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب تفاصيل أمر صرف المواد:', error)
      return { success: false, message: 'حدث خطأ في جلب تفاصيل أمر صرف المواد' }
    }
  }

  // ===== إدارة تتبع ساعات العمل =====

  // بدء تسجيل ساعات العمل
  public async startLaborTimeTracking(trackingData: {
    production_order_id: number
    worker_id?: number
    worker_name: string
    department_id?: number
    hourly_rate: number
    task_description?: string
  }): Promise<ApiResponse> {
    try {
      // التحقق من وجود أمر الإنتاج
      const productionOrder = this.db.prepare('SELECT * FROM production_orders WHERE id = ?').get(trackingData.production_order_id) as any
      if (!productionOrder) {
        return { success: false, message: 'أمر الإنتاج غير موجود' }
      }

      // التحقق من عدم وجود تسجيل نشط للعامل في نفس أمر الإنتاج
      const activeTracking = this.db.prepare(`
        SELECT * FROM labor_time_tracking
        WHERE production_order_id = ? AND worker_name = ? AND status = 'active'
      `).get(trackingData.production_order_id, trackingData.worker_name) as any

      if (activeTracking) {
        return { success: false, message: 'يوجد تسجيل نشط للعامل في هذا الأمر' }
      }

      const result = this.db.prepare(`
        INSERT INTO labor_time_tracking (
          production_order_id, worker_id, worker_name, department_id,
          start_time, hourly_rate, task_description, status
        ) VALUES (?, ?, ?, ?, datetime('now'), ?, ?, 'active')
      `).run(
        trackingData.production_order_id,
        trackingData.worker_id || null,
        trackingData.worker_name,
        trackingData.department_id || null,
        trackingData.hourly_rate,
        trackingData.task_description || null
      )

      // حفظ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم بدء تسجيل ساعات العمل بنجاح',
        data: { id: result.lastInsertRowid }
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في بدء تسجيل ساعات العمل:', error)
      return { success: false, message: 'حدث خطأ في بدء تسجيل ساعات العمل' }
    }
  }

  // إنهاء تسجيل ساعات العمل
  public async endLaborTimeTracking(trackingId: number, notes?: string): Promise<ApiResponse> {
    try {
      // الحصول على التسجيل
      const tracking = this.db.prepare('SELECT * FROM labor_time_tracking WHERE id = ?').get(trackingId) as any
      if (!tracking) {
        return { success: false, message: 'تسجيل ساعات العمل غير موجود' }
      }

      if (tracking.status !== 'active') {
        return { success: false, message: 'تسجيل ساعات العمل غير نشط' }
      }

      // حساب الساعات الفعلية
      const startTime = new Date(tracking.start_time)
      const endTime = new Date()
      const actualHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60)
      const laborCost = actualHours * tracking.hourly_rate

      // تحديث التسجيل
      this.db.prepare(`
        UPDATE labor_time_tracking
        SET end_time = datetime('now'),
            actual_hours = ?,
            labor_cost = ?,
            status = 'completed',
            notes = ?,
            updated_at = datetime('now')
        WHERE id = ?
      `).run(actualHours, laborCost, notes || null, trackingId)

      // حفظ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم إنهاء تسجيل ساعات العمل بنجاح',
        data: { actual_hours: actualHours, labor_cost: laborCost }
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في إنهاء تسجيل ساعات العمل:', error)
      return { success: false, message: 'حدث خطأ في إنهاء تسجيل ساعات العمل' }
    }
  }

  // الحصول على تسجيلات ساعات العمل
  public async getLaborTimeTracking(filters: {
    production_order_id?: number
    worker_id?: number
    department_id?: number
    status?: string
  } = {}): Promise<ApiResponse> {
    try {
      const whereConditions = ['1=1']
      const params: any[] = []

      if (filters.production_order_id) {
        whereConditions.push('ltt.production_order_id = ?')
        params.push(filters.production_order_id)
      }

      if (filters.worker_id) {
        whereConditions.push('ltt.worker_id = ?')
        params.push(filters.worker_id)
      }

      if (filters.department_id) {
        whereConditions.push('ltt.department_id = ?')
        params.push(filters.department_id)
      }

      if (filters.status) {
        whereConditions.push('ltt.status = ?')
        params.push(filters.status)
      }

      const trackings = this.db.prepare(`
        SELECT
          ltt.*,
          po.order_number as production_order_number,
          pd.name as department_name
        FROM labor_time_tracking ltt
        LEFT JOIN production_orders po ON ltt.production_order_id = po.id
        LEFT JOIN production_departments pd ON ltt.department_id = pd.id
        WHERE ${whereConditions.join(' AND ')}
        ORDER BY ltt.start_time DESC
      `).all(params) as any[]

      return {
        success: true,
        data: trackings
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في جلب تسجيلات ساعات العمل:', error)
      return { success: false, message: 'حدث خطأ في جلب تسجيلات ساعات العمل' }
    }
  }

  // ===== تنفيذ الإنتاج المحسن مع التكلفة الفعلية =====

  // تنفيذ إنتاج محسن مع إنشاء أوامر صرف المواد وحساب التكلفة الفعلية
  public async executeProductionEnhanced(productionData: {
    recipe_id: number
    quantity: number
    warehouse_id: number
    notes?: string
    requested_by?: number
  }): Promise<ApiResponse> {
    try {
      // الحصول على تفاصيل الوصفة مع معلومات المخازن
      const recipe = this.db.prepare(`
        SELECT pr.*,
               i.name as product_name,
               i.code as product_code,
               mw.name as material_warehouse_name,
               pw.name as product_warehouse_name
        FROM production_recipes pr
        LEFT JOIN items i ON pr.product_id = i.id
        LEFT JOIN warehouses mw ON pr.material_warehouse_id = mw.id
        LEFT JOIN warehouses pw ON pr.product_warehouse_id = pw.id
        WHERE pr.id = ? AND pr.is_active = 1
      `).get(productionData.recipe_id) as any

      if (!recipe) {
        return { success: false, message: 'الوصفة غير موجودة أو غير نشطة' }
      }

      // استخدام مخازن الوصفة (مطلوب وجودها)
      const materialWarehouseId = recipe.material_warehouse_id
      const productWarehouseId = recipe.product_warehouse_id

      if (!materialWarehouseId) {
        return { success: false, message: 'مخزن المواد الخام غير محدد في الوصفة' }
      }

      if (!productWarehouseId) {
        return { success: false, message: 'مخزن المنتج التام غير محدد في الوصفة' }
      }

      // الحصول على مواد الوصفة مع الأسعار الفعلية
      const recipeMaterials = this.db.prepare(`
        SELECT rm.*,
               i.name as material_name,
               i.code as material_code,
               inv.quantity as available_quantity,
               COALESCE(i.cost_price, i.sale_price, rm.cost_per_unit) as actual_unit_price
        FROM recipe_materials rm
        LEFT JOIN items i ON rm.material_id = i.id
        LEFT JOIN inventory inv ON i.id = inv.item_id AND inv.warehouse_id = ?
        WHERE rm.recipe_id = ?
      `).all(materialWarehouseId, productionData.recipe_id) as any[]

      if (recipeMaterials.length === 0) {
        return { success: false, message: 'لا توجد مواد في الوصفة' }
      }

      // التحقق من توفر المواد
      for (const material of recipeMaterials) {
        const requiredQuantity = material.quantity * productionData.quantity
        const availableQuantity = material.available_quantity || 0

        if (availableQuantity < requiredQuantity) {
          return {
            success: false,
            message: `كمية غير كافية من المادة: ${material.material_name}. المطلوب: ${requiredQuantity}, المتوفر: ${availableQuantity}`
          }
        }
      }

      // توليد رقم أمر إنتاج جديد
      const orderNumber = this.generateProductionOrderNumberSync()

      // بدء المعاملة
      const transaction = this.db.transaction(() => {
        // 1. إنشاء أمر الإنتاج
        const productionResult = this.db.prepare(`
          INSERT INTO production_orders (
            order_number, product_id, recipe_id, quantity,
            material_warehouse_id, product_warehouse_id, status, start_date, notes, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, 'in_progress', datetime('now'), ?, datetime('now'))
        `).run(
          orderNumber,
          recipe.product_id,
          productionData.recipe_id,
          productionData.quantity,
          materialWarehouseId,
          productWarehouseId,
          productionData.notes || null
        )

        const productionOrderId = productionResult.lastInsertRowid

        // 2. إنشاء أمر صرف المواد
        const issueOrderNumber = `MI-${orderNumber.replace('PO-', '')}`
        const issueOrderResult = this.db.prepare(`
          INSERT INTO material_issue_orders (
            order_number, production_order_id, warehouse_id,
            requested_by, status, request_date
          ) VALUES (?, ?, ?, ?, 'approved', datetime('now'))
        `).run(
          issueOrderNumber,
          productionOrderId,
          materialWarehouseId,
          productionData.requested_by || null
        )

        const issueOrderId = issueOrderResult.lastInsertRowid

        // 3. إضافة تفاصيل المواد المصروفة وحساب التكلفة الفعلية
        let totalMaterialCost = 0

        for (const material of recipeMaterials) {
          const requiredQuantity = material.quantity * productionData.quantity
          const actualUnitPrice = material.actual_unit_price || 0
          const totalCost = requiredQuantity * actualUnitPrice
          totalMaterialCost += totalCost

          // إضافة تفاصيل أمر الصرف
          this.db.prepare(`
            INSERT INTO material_issue_items (
              issue_order_id, material_id, requested_quantity,
              approved_quantity, issued_quantity, unit_price, total_cost
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
          `).run(
            issueOrderId,
            material.material_id,
            requiredQuantity,
            requiredQuantity,
            requiredQuantity,
            actualUnitPrice,
            totalCost
          )

          // خصم المواد من المخزون
          this.db.prepare(`
            UPDATE inventory
            SET quantity = quantity - ?,
                last_updated = datetime('now')
            WHERE item_id = ? AND warehouse_id = ?
          `).run(requiredQuantity, material.material_id, materialWarehouseId)

          // تسجيل حركة المخزون
          this.db.prepare(`
            INSERT INTO inventory_movements (
              item_id, warehouse_id, movement_type, quantity,
              reference_type, reference_id, notes, created_at
            ) VALUES (?, ?, 'out', ?, 'material_issue', ?, ?, datetime('now'))
          `).run(
            material.material_id,
            materialWarehouseId,
            requiredQuantity,
            issueOrderId,
            `صرف للإنتاج - أمر رقم: ${orderNumber}`
          )
        }

        // 4. تحديث حالة أمر الصرف
        this.db.prepare(`
          UPDATE material_issue_orders
          SET status = 'issued',
              issue_date = datetime('now'),
              issued_by = ?
          WHERE id = ?
        `).run(productionData.requested_by || null, issueOrderId)

        // 5. حساب التكلفة الفعلية (المواد + العمالة + التكاليف العامة)
        const laborCost = 0 // سيتم حسابها من تسجيلات ساعات العمل
        const overheadRate = this.getOverheadRate() / 100 // تحويل النسبة المئوية إلى عشرية
        const overheadCost = totalMaterialCost * overheadRate
        const actualTotalCost = totalMaterialCost + laborCost + overheadCost

        // 6. تحديث أمر الإنتاج بالتكلفة الفعلية
        this.db.prepare(`
          UPDATE production_orders
          SET actual_cost = ?
          WHERE id = ?
        `).run(actualTotalCost, productionOrderId)

        return {
          productionOrderId,
          issueOrderId,
          totalMaterialCost,
          actualTotalCost
        }
      })

      const result = transaction()

      // حفظ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: `تم إنشاء أمر الإنتاج بنجاح - رقم الأمر: ${orderNumber}`,
        data: {
          production_order_id: result.productionOrderId,
          issue_order_id: result.issueOrderId,
          order_number: orderNumber,
          quantity: productionData.quantity,
          product_name: recipe.product_name,
          material_cost: result.totalMaterialCost,
          total_cost: result.actualTotalCost
        }
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تنفيذ الإنتاج المحسن:', error)
      return { success: false, message: error.message || 'حدث خطأ في تنفيذ الإنتاج' }
    }
  }

  // إكمال الإنتاج وحساب التكلفة الفعلية النهائية
  public async completeProduction(productionOrderId: number, completionData?: {
    actual_quantity?: number
    quality_notes?: string
    completion_notes?: string
  }): Promise<ApiResponse> {
    try {
      // الحصول على تفاصيل أمر الإنتاج
      const productionOrder = this.db.prepare(`
        SELECT po.*,
               pr.name as recipe_name,
               pr.product_id,
               pr.product_warehouse_id,
               i.name as product_name
        FROM production_orders po
        LEFT JOIN production_recipes pr ON po.recipe_id = pr.id
        LEFT JOIN items i ON pr.product_id = i.id
        WHERE po.id = ? AND po.status = 'in_progress'
      `).get(productionOrderId) as any

      if (!productionOrder) {
        return { success: false, message: 'أمر الإنتاج غير موجود أو غير قيد التنفيذ' }
      }

      // حساب تكلفة العمالة الفعلية من تسجيلات ساعات العمل
      const laborCosts = this.db.prepare(`
        SELECT SUM(labor_cost) as total_labor_cost,
               SUM(actual_hours) as total_hours
        FROM labor_time_tracking
        WHERE production_order_id = ? AND status = 'completed'
      `).get(productionOrderId) as any

      const totalLaborCost = laborCosts?.total_labor_cost || 0
      const totalLaborHours = laborCosts?.total_hours || 0

      // حساب تكلفة المواد الفعلية من أوامر الصرف
      const materialCosts = this.db.prepare(`
        SELECT SUM(mii.total_cost) as total_material_cost
        FROM material_issue_orders mio
        JOIN material_issue_items mii ON mio.id = mii.issue_order_id
        WHERE mio.production_order_id = ? AND mio.status = 'issued'
      `).get(productionOrderId) as any

      const totalMaterialCost = materialCosts?.total_material_cost || 0

      // حساب التكاليف العامة من الإعدادات
      const directCosts = totalMaterialCost + totalLaborCost
      const overheadRate = this.getOverheadRate() / 100 // تحويل النسبة المئوية إلى عشرية
      const overheadCost = directCosts * overheadRate

      // التكلفة الإجمالية الفعلية
      const actualTotalCost = directCosts + overheadCost

      // الكمية المنتجة الفعلية
      const actualQuantity = completionData?.actual_quantity || productionOrder.quantity

      // تكلفة الوحدة
      const unitCost = actualQuantity > 0 ? actualTotalCost / actualQuantity : 0

      // بدء المعاملة
      const transaction = this.db.transaction(() => {
        // تحديث أمر الإنتاج
        this.db.prepare(`
          UPDATE production_orders
          SET status = 'completed',
              completion_date = datetime('now'),
              actual_quantity = ?,
              actual_cost = ?,
              material_cost = ?,
              labor_cost = ?,
              overhead_cost = ?,
              unit_cost = ?,
              labor_hours = ?,
              quality_notes = ?,
              completion_notes = ?
          WHERE id = ?
        `).run(
          actualQuantity,
          actualTotalCost,
          totalMaterialCost,
          totalLaborCost,
          overheadCost,
          unitCost,
          totalLaborHours,
          completionData?.quality_notes || null,
          completionData?.completion_notes || null,
          productionOrderId
        )

        // إضافة المنتج النهائي إلى المخزون
        const productWarehouseId = productionOrder.product_warehouse_id || productionOrder.warehouse_id

        // التحقق من وجود سجل مخزون للمنتج
        const existingInventory = this.db.prepare(`
          SELECT id FROM inventory
          WHERE item_id = ? AND warehouse_id = ?
        `).get(productionOrder.product_id, productWarehouseId)

        if (existingInventory) {
          // تحديث الكمية الموجودة
          this.db.prepare(`
            UPDATE inventory
            SET quantity = quantity + ?,
                last_updated = datetime('now')
            WHERE item_id = ? AND warehouse_id = ?
          `).run(actualQuantity, productionOrder.product_id, productWarehouseId)
        } else {
          // إنشاء سجل مخزون جديد
          this.db.prepare(`
            INSERT INTO inventory (
              item_id, warehouse_id, quantity, last_updated
            ) VALUES (?, ?, ?, datetime('now'))
          `).run(productionOrder.product_id, productWarehouseId, actualQuantity)
        }

        // تسجيل حركة المخزون (دخول)
        this.db.prepare(`
          INSERT INTO inventory_movements (
            item_id, warehouse_id, movement_type, quantity,
            reference_type, reference_id, notes, created_at
          ) VALUES (?, ?, 'in', ?, 'production_completion', ?, ?, datetime('now'))
        `).run(
          productionOrder.product_id,
          productWarehouseId,
          actualQuantity,
          productionOrderId,
          `إكمال إنتاج - أمر رقم: ${productionOrder.order_number}`
        )

        // تحديث تكلفة المنتج إذا كانت أقل من التكلفة الفعلية
        const currentProduct = this.db.prepare(`
          SELECT cost_price FROM items WHERE id = ?
        `).get(productionOrder.product_id) as any

        if (!currentProduct?.cost_price || currentProduct.cost_price < unitCost) {
          this.db.prepare(`
            UPDATE items
            SET cost_price = ?,
                last_updated = datetime('now')
            WHERE id = ?
          `).run(unitCost, productionOrder.product_id)
        }

        return {
          actualQuantity,
          actualTotalCost,
          totalMaterialCost,
          totalLaborCost,
          overheadCost,
          unitCost,
          totalLaborHours
        }
      })

      const result = transaction()

      // حفظ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: `تم إكمال الإنتاج بنجاح - أمر رقم: ${productionOrder.order_number}`,
        data: {
          production_order_id: productionOrderId,
          order_number: productionOrder.order_number,
          product_name: productionOrder.product_name,
          actual_quantity: result.actualQuantity,
          actual_total_cost: result.actualTotalCost,
          material_cost: result.totalMaterialCost,
          labor_cost: result.totalLaborCost,
          overhead_cost: result.overheadCost,
          unit_cost: result.unitCost,
          labor_hours: result.totalLaborHours
        }
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في إكمال الإنتاج:', error)
      return { success: false, message: error.message || 'حدث خطأ في إكمال الإنتاج' }
    }
  }

  // تحديث تكلفة المنتج بناءً على الوصفات
  public async updateProductCostFromRecipes(productId: number): Promise<ApiResponse> {
    try {
      // الحصول على جميع الوصفات النشطة للمنتج
      const recipes = this.db.prepare(`
        SELECT pr.*,
               SUM(rm.quantity * COALESCE(i.cost_price, 0)) as total_material_cost
        FROM production_recipes pr
        LEFT JOIN recipe_materials rm ON pr.id = rm.recipe_id
        LEFT JOIN items i ON rm.material_id = i.id
        WHERE pr.product_id = ? AND pr.is_active = 1
        GROUP BY pr.id
        ORDER BY pr.created_at DESC
      `).all(productId) as any[]

      if (recipes.length === 0) {
        return { success: false, message: 'لا توجد وصفات نشطة لهذا المنتج' }
      }

      // حساب متوسط التكلفة من جميع الوصفات
      let totalCost = 0
      let validRecipes = 0

      for (const recipe of recipes) {
        if (recipe.total_material_cost > 0) {
          // إضافة تكلفة العمالة والنفقات العامة
          const settings = await this.getProductionSettings()
          const overheadRate = settings.data?.overhead_rate || 15
          const hourlyRate = settings.data?.default_hourly_rate || 25

          const laborCost = (recipe.estimated_time || 0) * hourlyRate
          const overheadCost = recipe.total_material_cost * (overheadRate / 100)

          const recipeTotalCost = recipe.total_material_cost + laborCost + overheadCost
          totalCost += recipeTotalCost
          validRecipes++
        }
      }

      if (validRecipes === 0) {
        return { success: false, message: 'لا توجد وصفات صالحة لحساب التكلفة' }
      }

      const averageCost = totalCost / validRecipes

      // تحديث تكلفة المنتج
      this.db.prepare(`
        UPDATE items
        SET cost_price = ?,
            sale_price = CASE
              WHEN sale_price = 0 OR sale_price < ? * 1.2
              THEN ? * 1.3
              ELSE sale_price
            END,
            updated_at = datetime('now')
        WHERE id = ?
      `).run(averageCost, averageCost, averageCost, productId)

      // حفظ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم تحديث تكلفة المنتج بنجاح',
        data: {
          product_id: productId,
          new_cost_price: averageCost,
          new_sale_price: averageCost * 1.3,
          recipes_count: validRecipes
        }
      }

    } catch (error) {
      Logger.error('ProductionService', 'خطأ في تحديث تكلفة المنتج من الوصفات:', error)
      return { success: false, message: 'حدث خطأ في تحديث تكلفة المنتج' }
    }
  }

  // تحديث تكلفة المنتج بناءً على التكلفة الفعلية للإنتاج
  public async updateProductCostFromProduction(productId: number): Promise<ApiResponse> {
    try {
      // حساب متوسط التكلفة الفعلية من آخر 5 أوامر إنتاج مكتملة
      const recentOrders = this.db.prepare(`
        SELECT unit_cost, actual_quantity
        FROM production_orders
        WHERE product_id = ?
          AND status = 'completed'
          AND unit_cost IS NOT NULL
          AND unit_cost > 0
        ORDER BY completion_date DESC
        LIMIT 5
      `).all(productId) as any[]

      if (recentOrders.length === 0) {
        return { success: false, message: 'لا توجد أوامر إنتاج مكتملة لهذا المنتج' }
      }

      // حساب متوسط التكلفة مرجح بالكمية
      let totalCost = 0
      let totalQuantity = 0

      for (const order of recentOrders) {
        totalCost += order.unit_cost * order.actual_quantity
        totalQuantity += order.actual_quantity
      }

      const averageUnitCost = totalQuantity > 0 ? totalCost / totalQuantity : 0

      // تحديث تكلفة المنتج
      this.db.prepare(`
        UPDATE items
        SET cost_price = ?,
            last_updated = datetime('now')
        WHERE id = ?
      `).run(averageUnitCost, productId)

      // حفظ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم تحديث تكلفة المنتج بنجاح',
        data: {
          product_id: productId,
          new_cost_price: averageUnitCost,
          orders_count: recentOrders.length
        }
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تحديث تكلفة المنتج:', error)
      return { success: false, message: error.message || 'حدث خطأ في تحديث تكلفة المنتج' }
    }
  }

  // تقرير تحليل التكاليف الفعلية مقابل المقدرة
  public async getProductionCostAnalysis(filters?: {
    product_id?: number
    date_from?: string
    date_to?: string
    department_id?: number
  }): Promise<ApiResponse> {
    try {
      const whereConditions = ['po.status = "completed"']
      const params: any[] = []

      if (filters?.product_id) {
        whereConditions.push('po.product_id = ?')
        params.push(filters.product_id)
      }

      if (filters?.date_from) {
        whereConditions.push('DATE(po.completion_date) >= ?')
        params.push(filters.date_from)
      }

      if (filters?.date_to) {
        whereConditions.push('DATE(po.completion_date) <= ?')
        params.push(filters.date_to)
      }

      if (filters?.department_id) {
        whereConditions.push('po.department_id = ?')
        params.push(filters.department_id)
      }

      const analysis = this.db.prepare(`
        SELECT
          po.id,
          po.order_number,
          i.name as product_name,
          i.code as product_code,
          po.quantity,
          po.actual_quantity,
          po.estimated_cost,
          po.actual_cost,
          po.material_cost,
          po.labor_cost,
          po.overhead_cost,
          po.unit_cost,
          po.labor_hours,
          po.completion_date,
          d.name as department_name,
          ROUND((po.actual_cost - po.estimated_cost), 2) as cost_variance,
          ROUND(((po.actual_cost - po.estimated_cost) / po.estimated_cost * 100), 2) as cost_variance_percent
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        LEFT JOIN production_departments d ON po.department_id = d.id
        WHERE ${whereConditions.join(' AND ')}
        ORDER BY po.completion_date DESC
      `).all(...params) as any[]

      // حساب الإحصائيات الإجمالية
      const totalOrders = analysis.length
      const totalEstimatedCost = analysis.reduce((sum, order) => sum + (order.estimated_cost || 0), 0)
      const totalActualCost = analysis.reduce((sum, order) => sum + (order.actual_cost || 0), 0)
      const totalMaterialCost = analysis.reduce((sum, order) => sum + (order.material_cost || 0), 0)
      const totalLaborCost = analysis.reduce((sum, order) => sum + (order.labor_cost || 0), 0)
      const totalOverheadCost = analysis.reduce((sum, order) => sum + (order.overhead_cost || 0), 0)
      const totalLaborHours = analysis.reduce((sum, order) => sum + (order.labor_hours || 0), 0)

      const summary = {
        total_orders: totalOrders,
        total_estimated_cost: Math.round(totalEstimatedCost * 100) / 100,
        total_actual_cost: Math.round(totalActualCost * 100) / 100,
        total_material_cost: Math.round(totalMaterialCost * 100) / 100,
        total_labor_cost: Math.round(totalLaborCost * 100) / 100,
        total_overhead_cost: Math.round(totalOverheadCost * 100) / 100,
        total_labor_hours: Math.round(totalLaborHours * 100) / 100,
        cost_variance: Math.round((totalActualCost - totalEstimatedCost) * 100) / 100,
        cost_variance_percent: totalEstimatedCost > 0 ?
          Math.round(((totalActualCost - totalEstimatedCost) / totalEstimatedCost * 100) * 100) / 100 : 0,
        average_unit_cost: totalOrders > 0 ?
          Math.round((totalActualCost / analysis.reduce((sum, order) => sum + (order.actual_quantity || order.quantity), 0)) * 100) / 100 : 0
      }

      return {
        success: true,
        data: {
          analysis,
          summary
        }
      }

    } catch (error: any) {
      Logger.error('ProductionService', 'خطأ في تحليل التكاليف:', error)
      return { success: false, message: error.message || 'حدث خطأ في تحليل التكاليف' }
    }
  }

  // ===== إعدادات الإنتاج =====

  // جلب إعدادات الإنتاج
  public async getProductionSettings(): Promise<ApiResponse> {
    try {
      const settings = this.db.prepare(`
        SELECT * FROM production_settings WHERE id = 1
      `).get() as any

      if (!settings) {
        // إنشاء إعدادات افتراضية
        const defaultSettings = {
          overhead_rate: 15,
          default_hourly_rate: 25,
          auto_update_product_cost: 1,
          cost_calculation_method: 'average',
          auto_create_material_issue: 1,
          require_approval_for_issue: 0,
          auto_start_labor_tracking: 0,
          default_production_priority: 'normal',
          notify_low_materials: 1,
          notify_production_delays: 1,
          notify_cost_variance: 1,
          cost_variance_threshold: 10,
          auto_generate_daily_reports: 0,
          include_labor_details: 1,
          report_format: 'pdf'
        }

        this.db.prepare(`
          INSERT INTO production_settings (
            id, overhead_rate, default_hourly_rate, auto_update_product_cost,
            cost_calculation_method, auto_create_material_issue, require_approval_for_issue,
            auto_start_labor_tracking, default_production_priority, notify_low_materials,
            notify_production_delays, notify_cost_variance, cost_variance_threshold,
            auto_generate_daily_reports, include_labor_details, report_format
          ) VALUES (
            1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
          )
        `).run(
          defaultSettings.overhead_rate,
          defaultSettings.default_hourly_rate,
          defaultSettings.auto_update_product_cost,
          defaultSettings.cost_calculation_method,
          defaultSettings.auto_create_material_issue,
          defaultSettings.require_approval_for_issue,
          defaultSettings.auto_start_labor_tracking,
          defaultSettings.default_production_priority,
          defaultSettings.notify_low_materials,
          defaultSettings.notify_production_delays,
          defaultSettings.notify_cost_variance,
          defaultSettings.cost_variance_threshold,
          defaultSettings.auto_generate_daily_reports,
          defaultSettings.include_labor_details,
          defaultSettings.report_format
        )

        return { success: true, data: defaultSettings }
      }

      return { success: true, data: settings }
    } catch (error) {
      console.error('خطأ في جلب إعدادات الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب إعدادات الإنتاج' }
    }
  }

  // حفظ إعدادات الإنتاج
  public async saveProductionSettings(settings: any): Promise<ApiResponse> {
    try {
      this.db.prepare(`
        UPDATE production_settings SET
          overhead_rate = ?,
          default_hourly_rate = ?,
          auto_update_product_cost = ?,
          cost_calculation_method = ?,
          auto_create_material_issue = ?,
          require_approval_for_issue = ?,
          auto_start_labor_tracking = ?,
          default_production_priority = ?,
          notify_low_materials = ?,
          notify_production_delays = ?,
          notify_cost_variance = ?,
          cost_variance_threshold = ?,
          auto_generate_daily_reports = ?,
          include_labor_details = ?,
          report_format = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = 1
      `).run(
        settings.overhead_rate,
        settings.default_hourly_rate,
        settings.auto_update_product_cost ? 1 : 0,
        settings.cost_calculation_method,
        settings.auto_create_material_issue ? 1 : 0,
        settings.require_approval_for_issue ? 1 : 0,
        settings.auto_start_labor_tracking ? 1 : 0,
        settings.default_production_priority,
        settings.notify_low_materials ? 1 : 0,
        settings.notify_production_delays ? 1 : 0,
        settings.notify_cost_variance ? 1 : 0,
        settings.cost_variance_threshold,
        settings.auto_generate_daily_reports ? 1 : 0,
        settings.include_labor_details ? 1 : 0,
        settings.report_format
      )

      return { success: true, message: 'تم حفظ الإعدادات بنجاح' }
    } catch (error) {
      console.error('خطأ في حفظ إعدادات الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في حفظ إعدادات الإنتاج' }
    }
  }

  // جلب نسبة التكاليف العامة من الإعدادات (متزامن)
  private getOverheadRate(): number {
    try {
      const settings = this.db.prepare(`
        SELECT overhead_rate FROM production_settings WHERE id = 1
      `).get() as any

      return settings?.overhead_rate || 15 // القيمة الافتراضية 15%
    } catch (error) {
      console.error('خطأ في جلب نسبة التكاليف العامة:', error)
      return 15 // القيمة الافتراضية
    }
  }
}
