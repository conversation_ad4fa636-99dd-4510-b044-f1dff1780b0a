import { Logger } from './logger'

/**
 * أداة تحسين الذاكرة والأداء
 */
export class MemoryOptimizer {
  private static instance: MemoryOptimizer
  private cleanupInterval: NodeJS.Timeout | null = null
  private lastCleanup: number = 0
  private readonly CLEANUP_INTERVAL = 60000 // دقيقة واحدة
  private readonly FORCE_GC_THRESHOLD = 200 * 1024 * 1024 // 200MB

  private constructor() {}

  public static getInstance(): MemoryOptimizer {
    if (!MemoryOptimizer.instance) {
      MemoryOptimizer.instance = new MemoryOptimizer()
    }
    return MemoryOptimizer.instance
  }

  /**
   * بدء مراقبة الذاكرة والتنظيف التلقائي
   */
  public startMonitoring(): void {
    if (this.cleanupInterval) {
      return // المراقبة تعمل بالفعل
    }

    Logger.info('MemoryOptimizer', '🧹 بدء مراقبة الذاكرة والتنظيف التلقائي')

    this.cleanupInterval = setInterval(() => {
      this.performCleanup()
    }, this.CLEANUP_INTERVAL)

    // تنظيف فوري عند البدء
    this.performCleanup()
  }

  /**
   * إيقاف مراقبة الذاكرة
   */
  public stopMonitoring(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
      Logger.info('MemoryOptimizer', '⏹️ تم إيقاف مراقبة الذاكرة')
    }
  }

  /**
   * تنفيذ عملية تنظيف الذاكرة
   */
  public performCleanup(): void {
    try {
      const memoryUsage = process.memoryUsage()
      const currentTime = Date.now()

      Logger.debug('MemoryOptimizer', '📊 استهلاك الذاكرة الحالي:', {
        rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`,
        heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
        heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
        external: `${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`
      })

      // تنظيف cache إذا مر وقت كافي
      if (currentTime - this.lastCleanup > this.CLEANUP_INTERVAL) {
        this.clearCaches()
        this.lastCleanup = currentTime
      }

      // تشغيل garbage collection إذا تجاوز الاستهلاك الحد المسموح
      if (memoryUsage.rss > this.FORCE_GC_THRESHOLD && global.gc) {
        Logger.info('MemoryOptimizer', '🗑️ تشغيل garbage collection بسبب ارتفاع استهلاك الذاكرة')
        global.gc()
      }

    } catch (error) {
      Logger.error('MemoryOptimizer', 'خطأ في تنظيف الذاكرة:', error)
    }
  }

  /**
   * تنظيف جميع أنواع cache
   */
  private clearCaches(): void {
    try {
      // تنظيف require cache (باستثناء الملفات الأساسية)
      const moduleKeys = Object.keys(require.cache)
      let clearedCount = 0

      for (const key of moduleKeys) {
        // لا تحذف الملفات الأساسية
        if (!key.includes('node_modules') && 
            !key.includes('electron') && 
            !key.includes('main.js') &&
            !key.includes('DatabaseService') &&
            !key.includes('logger')) {
          delete require.cache[key]
          clearedCount++
        }
      }

      if (clearedCount > 0) {
        Logger.debug('MemoryOptimizer', `🧹 تم تنظيف ${clearedCount} ملف من require cache`)
      }

      // تنظيف timers منتهية الصلاحية
      this.clearExpiredTimers()

    } catch (error) {
      Logger.error('MemoryOptimizer', 'خطأ في تنظيف cache:', error)
    }
  }

  /**
   * تنظيف timers منتهية الصلاحية
   */
  private clearExpiredTimers(): void {
    try {
      // هذه دالة تجريبية لتنظيف timers
      // في الواقع، Node.js يدير هذا تلقائياً
      const activeHandles = (process as any)._getActiveHandles?.() || []
      const activeRequests = (process as any)._getActiveRequests?.() || []

      Logger.debug('MemoryOptimizer', `📊 العمليات النشطة: ${activeHandles.length} handles, ${activeRequests.length} requests`)

    } catch (error) {
      // تجاهل الأخطاء في هذه العملية التجريبية
    }
  }

  /**
   * فرض تشغيل garbage collection
   */
  public forceGarbageCollection(): boolean {
    try {
      if (global.gc) {
        const beforeMemory = process.memoryUsage()
        global.gc()
        const afterMemory = process.memoryUsage()

        const savedMemory = beforeMemory.heapUsed - afterMemory.heapUsed
        Logger.info('MemoryOptimizer', `🗑️ تم تشغيل garbage collection، تم توفير ${(savedMemory / 1024 / 1024).toFixed(2)} MB`)
        return true
      } else {
        Logger.warn('MemoryOptimizer', '⚠️ garbage collection غير متوفر (استخدم --expose-gc)')
        return false
      }
    } catch (error) {
      Logger.error('MemoryOptimizer', 'خطأ في تشغيل garbage collection:', error)
      return false
    }
  }

  /**
   * الحصول على إحصائيات الذاكرة
   */
  public getMemoryStats(): any {
    const memoryUsage = process.memoryUsage()
    return {
      rss: `${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`,
      heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
      heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
      external: `${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`,
      heapUtilization: `${((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100).toFixed(1)}%`
    }
  }

  /**
   * تحسين استعلام قاعدة البيانات للذاكرة
   */
  public optimizeQuery(sql: string, params?: any): { sql: string; params: any; options: any } {
    let optimizedSql = sql.trim()
    const options: any = {}

    // إضافة LIMIT إذا لم يكن موجوداً
    if (!optimizedSql.toLowerCase().includes('limit') && 
        optimizedSql.toLowerCase().startsWith('select')) {
      optimizedSql += ' LIMIT 1000'
      options.limit = 1000
    }

    // تحسين استعلامات COUNT
    if (optimizedSql.toLowerCase().includes('count(*)')) {
      // استخدام تقدير سريع للجداول الكبيرة
      optimizedSql = optimizedSql.replace(/count\(\*\)/gi, 'count(*)')
    }

    return {
      sql: optimizedSql,
      params,
      options
    }
  }
}

// تصدير instance واحد
export const memoryOptimizer = MemoryOptimizer.getInstance()
