import React from 'react';
import { Tag, Typography, Select } from 'antd';
import UniversalReport from './UniversalReport';
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger'

const { Text } = Typography;

const InventoryMovementsReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<any> => {
    try {
      Logger.info('InventoryMovementsReport', '🔄 جاري إنشاء تقرير حركة المخزون...')

      let movementsData: any[]

      if (!window.electronAPI) {
        Logger.error('InventoryMovementsReport', '❌ window.electronAPI غير متوفر')
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      } else {
        // استعلام البيانات من قاعدة البيانات
        const response = await window.electronAPI.getInventoryMovementsReport({
          warehouseId: filters.warehouseId,
          itemId: filters.itemId,
          movementType: filters.movementType,
          dateRange: filters.dateRange
        });

        if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
          throw new Error('لا توجد بيانات متاحة');
        }

        movementsData = response.data;
        Logger.info('InventoryMovementsReport', '✅ تم جلب بيانات حركة المخزون من قاعدة البيانات')
      }

      // تحضير أعمدة الجدول
      const columns = [
        {
          title: 'التاريخ والوقت (ميلادي)',
          dataIndex: 'created_at',
          key: 'created_at',
          width: 150,
          fixed: 'left' as const,
          render: (date: string) => (
            <Text strong style={{ color: '#1890ff' }}>
              {DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE_TIME)}
            </Text>
          )
        },
        {
          title: 'كود الصنف',
          dataIndex: 'item_code',
          key: 'item_code',
          width: 120,
          render: (text: string) => (
            <Text strong>{text}</Text>
          )
        },
        {
          title: 'اسم الصنف',
          dataIndex: 'item_name',
          key: 'item_name',
          width: 200,
          render: (text: string) => (
            <Text>{text}</Text>
          )
        },
        {
          title: 'المخزن',
          dataIndex: 'warehouse_name',
          key: 'warehouse_name',
          width: 150,
          render: (text: string) => (
            <Tag color="blue">{text}</Tag>
          )
        },
        {
          title: 'نوع الحركة',
          dataIndex: 'movement_type',
          key: 'movement_type',
          width: 120,
          align: 'center' as const,
          render: (type: string) => {
            const typeConfig = {
              'in': { text: 'إدخال', color: 'green' },
              'out': { text: 'إخراج', color: 'red' },
              'transfer': { text: 'تحويل', color: 'blue' },
              'adjustment': { text: 'تسوية', color: 'orange' }
            };
            const config = typeConfig[type as keyof typeof typeConfig] || { text: type, color: 'default' };
            return <Tag color={config.color}>{config.text}</Tag>;
          }
        },
        {
          title: 'الكمية',
          dataIndex: 'quantity',
          key: 'quantity',
          width: 100,
          align: 'center' as const,
          render: (quantity: number, record: any) => (
            <Text strong style={{ 
              color: record.movement_type === 'in' ? '#52c41a' : 
                     record.movement_type === 'out' ? '#f5222d' : '#1890ff'
            }}>
              {record.movement_type === 'out' ? '-' : '+'}{Math.abs(quantity).toLocaleString()}
            </Text>
          )
        },
        {
          title: 'الوحدة',
          dataIndex: 'unit',
          key: 'unit',
          width: 80,
          align: 'center' as const
        },
        {
          title: 'نوع المرجع',
          dataIndex: 'reference_type',
          key: 'reference_type',
          width: 120,
          render: (type: string) => {
            if (!type) return '-';
            const typeConfig = {
              'purchase': { text: 'فاتورة شراء', color: 'green' },
              'sale': { text: 'فاتورة بيع', color: 'blue' },
              'transfer': { text: 'تحويل', color: 'purple' },
              'adjustment': { text: 'تسوية', color: 'orange' },
              'production': { text: 'إنتاج', color: 'cyan' }
            };
            const config = typeConfig[type as keyof typeof typeConfig] || { text: type, color: 'default' };
            return <Tag color={config.color}>{config.text}</Tag>;
          }
        },
        {
          title: 'رقم المرجع',
          dataIndex: 'reference_id',
          key: 'reference_id',
          width: 100,
          align: 'center' as const,
          render: (id: number) => (
            <Text type="secondary">{id || '-'}</Text>
          )
        },
        {
          title: 'المستخدم',
          dataIndex: 'created_by_name',
          key: 'created_by_name',
          width: 150,
          render: (name: string) => (
            <Text>{name || 'غير محدد'}</Text>
          )
        },
        {
          title: 'ملاحّات',
          dataIndex: 'notes',
          key: 'notes',
          width: 200,
          render: (notes: string) => (
            <Text type="secondary" ellipsis={{ tooltip: notes }}>
              {notes || '-'}
            </Text>
          )
        }
      ];

      // حساب الملخص
      const totalMovements = movementsData.length;
      const inMovements = movementsData.filter((m: any) => m.movement_type === 'in').length;
      const outMovements = movementsData.filter((m: any) => m.movement_type === 'out').length;
      const transferMovements = movementsData.filter((m: any) => m.movement_type === 'transfer').length;
      const adjustmentMovements = movementsData.filter((m: any) => m.movement_type === 'adjustment').length;

      const totalInQuantity = movementsData
        .filter((m: any) => m.movement_type === 'in')
        .reduce((sum: number, m: any) => sum + m.quantity, 0);
      
      const totalOutQuantity = movementsData
        .filter((m: any) => m.movement_type === 'out')
        .reduce((sum: number, m: any) => sum + Math.abs(m.quantity), 0);

      // إضافة مفاتيح فريدة للبيانات
      const dataWithKeys = movementsData.map((movement: any, index: number) => ({
        ...movement,
        key: `${movement.id}-${index}`
      }));

      // تحضير عنوان فرعي
      let subtitle = 'تقرير شامل لجميع حركات المخزون';
      if (filters.warehouseId && window.electronAPI) {
        try {
          const warehouse = await window.electronAPI.getWarehouse(filters.warehouseId);
          subtitle += ` - المخزن: ${warehouse?.name}`;
        } catch (error) {
          Logger.error('InventoryMovementsReport', 'خطأ في جلب بيانات المخزن:', error);
        }
      }
      if (filters.dateRange) {
        const startDate = typeof filters.dateRange[0] === 'string' ? filters.dateRange[0] : filters.dateRange[0].format('YYYY-MM-DD');
        const endDate = typeof filters.dateRange[1] === 'string' ? filters.dateRange[1] : filters.dateRange[1].format('YYYY-MM-DD');
        subtitle += ` - من ${startDate} إلى ${endDate}`;
      }

      return {
        title: 'تقرير حركة المخزون',
        subtitle,
        columns,
        data: dataWithKeys,
        summary: {
          totalMovements,
          inMovements,
          outMovements,
          transferMovements,
          adjustmentMovements,
          totalInQuantity: Math.round(totalInQuantity * 100) / 100,
          totalOutQuantity: Math.round(totalOutQuantity * 100) / 100,
          netMovement: Math.round((totalInQuantity - totalOutQuantity) * 100) / 100
        }
      };
    } catch (error) {
      Logger.error('InventoryMovementsReport', 'خطأ في إنشاء تقرير حركة المخزون:', error);
      throw new Error('فشل في إنشاء التقرير');
    }
  };

  // فلتر نوع الحركة المخصص - سيتم تمريره كـ children في ReportBase
  const _movementTypeFilter = null; // سنستخدم customFilters بدلاً من ذلك

  return (
    <UniversalReport
      reportType="inventory_movements"
      title="تقرير حركة المخزون"
      description="تقرير يعرض جميع حركات المخزون (إدخال/إخراج/تحويل) خلال فترة محددة"
      onGenerateReport={generateReport}
      showDateRange={true}
      showWarehouseFilter={true}
      showCategoryFilter={false}
      showItemFilter={true}
      showMovementTypeFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="inventory_movements_report"
      defaultFilters={{
        dateRange: null, // كل المدة افتراضياً
        sortBy: 'movement_date',
        sortOrder: 'desc'
      }}
    />
  );
};

export default InventoryMovementsReport;
