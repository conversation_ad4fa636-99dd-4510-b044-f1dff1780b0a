import React from 'react';
import { Tag, Typography, Progress } from 'antd';
import UniversalReport from './UniversalReport';
import dayjs, { DATE_FORMATS } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger';
import type { ReportData, ReportType } from '../../types/reports';

const { Text } = Typography;

const PaintByCustomerReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getPaintByCustomerReport({
        customerId: filters.customerId,
        dateRange: filters.dateRange
      });

      if (!response || !response.success || !response.data) {
        throw new Error('لا توجد بيانات متاحة');
      }

      // التأكد من أن البيانات في الشكل الصحيح
      const paintData = response.data;
      const data = Array.isArray(paintData.data) ? paintData.data :
                   Array.isArray(paintData) ? paintData : [];

      // حساب الإحصائيات
      const totalCustomers = data.length;
      const totalOrders = data.reduce((sum: number, item: any) => sum + (item.total_orders || 0), 0);
      const totalInvoices = data.reduce((sum: number, item: any) => sum + (item.total_invoices || 0), 0);
      const totalArea = data.reduce((sum: number, item: any) => sum + (item.total_area || 0), 0);
      const totalRevenue = data.reduce((sum: number, item: any) => sum + (item.total_amount || 0), 0);
      const totalPaid = data.reduce((sum: number, item: any) => sum + (item.paid_amount || 0), 0);
      const totalOutstanding = data.reduce((sum, item) => sum + item.outstanding_amount, 0);

      // إعداد البيانات للجدول
      const tableData = data.map((item, index) => ({
        key: item.customer_id,
        index: index + 1,
        customer_code: item.customer_code,
        customer_name: item.customer_name,
        phone: item.phone || '-',
        email: item.email || '-',
        total_orders: item.total_orders,
        total_invoices: item.total_invoices,
        total_area: item.total_area,
        total_amount: item.total_amount,
        paid_amount: item.paid_amount,
        outstanding_amount: item.outstanding_amount,
        payment_percentage: item.total_amount > 0 ? (item.paid_amount / item.total_amount * 100) : 0,
        last_order_date: item.last_order_date ? dayjs(item.last_order_date).format(DATE_FORMATS.DISPLAY_DATE) : '-',
        last_invoice_date: item.last_invoice_date ? dayjs(item.last_invoice_date).format(DATE_FORMATS.DISPLAY_DATE) : '-'
      }));

      // تعريف الأعمدة
      const columns = [
        {
          title: '#',
          key: 'index',
          width: 50,
          align: 'center' as const
        },
        {
          title: 'كود العميل',
          key: 'customer_code',
          width: 100,
          filterable: true
        },
        {
          title: 'اسم العميل',
          key: 'customer_name',
          width: 150,
          filterable: true
        },
        {
          title: 'الهاتف',
          key: 'phone',
          width: 120
        },
        {
          title: 'البريد الإلكتروني',
          key: 'email',
          width: 150
        },
        {
          title: 'عدد الأوامر',
          key: 'total_orders',
          width: 100,
          align: 'center' as const,
          sortable: true,
          render: (record: any) => (
            <Tag color="blue">{record.total_orders}</Tag>
          )
        },
        {
          title: 'عدد الفواتير',
          key: 'total_invoices',
          width: 100,
          align: 'center' as const,
          sortable: true,
          render: (record: any) => (
            <Tag color="green">{record.total_invoices}</Tag>
          )
        },
        {
          title: 'إجمالي المساحة (م²)',
          key: 'total_area',
          width: 120,
          align: 'right' as const,
          sortable: true,
          render: (record: any) => record.total_area.toLocaleString('ar-EG', { minimumFractionDigits: 2 })
        },
        {
          title: 'إجمالي المبلغ',
          key: 'total_amount',
          width: 120,
          align: 'right' as const,
          sortable: true,
          render: (record: any) => (
            <Text strong style={{ color: '#1890ff' }}>
              {record.total_amount.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'المبلغ المدفوع',
          key: 'paid_amount',
          width: 120,
          align: 'right' as const,
          sortable: true,
          render: (record: any) => (
            <Text style={{ color: '#52c41a' }}>
              {record.paid_amount.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'المبلغ المستحق',
          key: 'outstanding_amount',
          width: 120,
          align: 'right' as const,
          sortable: true,
          render: (record: any) => (
            <Text style={{ color: record.outstanding_amount > 0 ? '#ff4d4f' : '#52c41a' }}>
              {record.outstanding_amount.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'نسبة السداد',
          key: 'payment_percentage',
          width: 120,
          align: 'center' as const,
          sortable: true,
          render: (record: any) => (
            <Progress
              percent={Math.round(record.payment_percentage)}
              size="small"
              status={record.payment_percentage === 100 ? 'success' : record.payment_percentage > 50 ? 'active' : 'exception'}
            />
          )
        },
        {
          title: 'آخر أمر',
          key: 'last_order_date',
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'آخر فاتورة',
          key: 'last_invoice_date',
          width: 120,
          align: 'center' as const,
          sortable: true
        }
      ];

      // إعداد الإحصائيات
      const statistics = [
        {
          title: 'إجمالي العملاء',
          value: totalCustomers,
          color: '#1890ff',
          icon: '👥'
        },
        {
          title: 'إجمالي الأوامر',
          value: totalOrders,
          color: '#52c41a',
          icon: '📋'
        },
        {
          title: 'إجمالي الفواتير',
          value: totalInvoices,
          color: '#722ed1',
          icon: '🧾'
        },
        {
          title: 'إجمالي المساحة (م²)',
          value: totalArea.toLocaleString('ar-EG', { minimumFractionDigits: 2 }),
          color: '#fa8c16',
          icon: '📐'
        },
        {
          title: 'إجمالي الإيرادات',
          value: `${totalRevenue.toLocaleString('ar-EG')} ج.م`,
          color: '#13c2c2',
          icon: '💰'
        },
        {
          title: 'إجمالي المدفوع',
          value: `${totalPaid.toLocaleString('ar-EG')} ج.م`,
          color: '#52c41a',
          icon: '✅'
        },
        {
          title: 'إجمالي المستحق',
          value: `${totalOutstanding.toLocaleString('ar-EG')} ج.م`,
          color: '#ff4d4f',
          icon: '⏰'
        },
        {
          title: 'متوسط قيمة العميل',
          value: totalCustomers > 0 ? `${Math.round(totalRevenue / totalCustomers).toLocaleString('ar-EG')} ج.م` : '0 ج.م',
          color: '#eb2f96',
          icon: '📊'
        }
      ];

      return {
        title: 'تقرير الدهان حسب العميل',
        columns,
        data: tableData,
        statistics,
        summary: {
          totalRecords: totalCustomers,
          totalRevenue: totalRevenue,
          totalPaid: totalPaid,
          totalOutstanding: totalOutstanding
        }
      };

    } catch (error) {
      Logger.error('PaintByCustomerReport', 'خطأ في إنشاء تقرير الدهان حسب العميل:', error);
      throw new Error('فشل في تحميل بيانات التقرير');
    }
  };

  return (
    <UniversalReport
      reportType={'paint_by_customer' as ReportType}
      title="تقرير الدهان حسب العميل"
      description="تقرير تفصيلي للدهان مجمع حسب العميل مع الإحصائيات"
      onGenerateReport={generateReport}
      showDateRange={true}
      showCustomerFilter={true}
    />
  );
};

export default PaintByCustomerReport;
