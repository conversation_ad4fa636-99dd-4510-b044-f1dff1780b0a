# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
release/
release-final/
release-updated/

# Cache directories
cache/
.cache/
.vite/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache/

# Next.js build output
.next/

# Nuxt.js build / generate output
.nuxt/

# Gatsby files
.cache/
public/

# Storybook build outputs
.out/
.storybook-out/

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Electron specific
out/

# Database backups
*.db.backup
*.sqlite.backup

# Test files and reports
test-*.js
debug-*.js
fix-*.js
check-*.js
*-report.json
*-report.md
eslint-*.json

# Temporary scripts (exclude essential run scripts)
# Keep these essential run scripts:
# run-dev.bat
# run-dev.ps1
# build-and-run.bat
# build-production.ps1
# quick-start.bat
# install-and-run.bat

# Arabic documentation files (temporary)
*.md
!README.md
!docs/*.md
!HOW_TO_RUN.md
!CLEANUP_SUMMARY.md

# HTML diagrams and flowcharts
*.html
!src/renderer/index.html

# Shortcuts and links
*.lnk
