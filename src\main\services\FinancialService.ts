import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { Logger } from '../utils/logger'

export interface BankAccount {
  id: number
  account_name: string
  bank_name: string
  account_number: string
  iban?: string
  balance: number
  currency: string
  is_active: boolean
  created_at: string
}

export interface Voucher {
  id: number
  voucher_number: string
  voucher_type: 'receipt' | 'payment'
  amount: number
  description: string
  reference_type?: string
  reference_id?: number
  bank_account_id?: number
  payment_method: 'cash' | 'bank_transfer' | 'check'
  voucher_date: string
  created_at: string
  created_by?: number
}

export interface Check {
  id: number
  check_number: string
  bank_account_id: number
  bank_name?: string
  amount: number
  payee_name: string
  check_date: string
  due_date: string
  status: 'issued' | 'cashed' | 'bounced' | 'cancelled'
  notes?: string
  // ربط الشيك بالعملاء والموردين
  entity_type?: 'customer' | 'supplier' | 'other'
  entity_id?: number
  entity_name?: string
  // معلومات إضافية للشيك
  check_type?: 'issued' | 'received'
  original_payer?: string
  current_holder?: string
  is_company_check?: boolean
  created_at: string
  created_by?: number
}

export interface Account {
  id: number
  account_code: string
  account_name: string
  account_type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense'
  parent_id?: number
  level: number
  is_active: boolean
  balance: number
  created_at: string
}

export interface BankTransaction {
  id: number
  account_id: number
  transaction_type: 'deposit' | 'withdrawal'
  amount: number
  description: string
  reference_type?: string
  reference_id?: number
  transaction_date: string
  created_at: string
  created_by?: number
}

export interface PromissoryNote {
  id: number
  note_number: string
  drawer_name: string
  payee_name: string
  amount: number
  issue_date: string
  due_date: string
  status: 'issued' | 'paid' | 'dishonored' | 'cancelled'
  notes?: string
  created_at: string
  created_by?: number
}

export interface CheckTransfer {
  id: number
  check_id: number
  from_entity_type: string
  from_entity_id?: number
  from_entity_name: string
  to_entity_type: string
  to_entity_id?: number
  to_entity_name: string
  transfer_date: string
  notes?: string
  created_at: string
  created_by?: number
}

export class FinancialService {
  private static instance: FinancialService
  private db: any

  private constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
  }

  public static getInstance(): FinancialService {
    if (!FinancialService.instance) {
      FinancialService.instance = new FinancialService()
    }
    return FinancialService.instance
  }

  // تحديث مخطط جدول الحسابات البنكية
  private async updateBankAccountsTableSchema(): Promise<void> {
    try {
      // التحقق من وجود الأعمدة الجديدة في جدول الحسابات البنكية
      const tableInfo = this.db.prepare("PRAGMA table_info(bank_accounts)").all()
      const existingColumns = tableInfo.map((col: any) => col.name)

      Logger.info('FinancialService', 'الأعمدة الموجودة في جدول bank_accounts:', existingColumns)

      const newColumns = [
        { name: 'account_type', type: 'TEXT DEFAULT "current"' },
        { name: 'branch', type: 'TEXT' },
        { name: 'contact_person', type: 'TEXT' },
        { name: 'phone', type: 'TEXT' }
      ]

      // إضافة الأعمدة المفقودة
      for (const column of newColumns) {
        if (!existingColumns.includes(column.name)) {
          try {
            this.db.exec(`ALTER TABLE bank_accounts ADD COLUMN ${column.name} ${column.type}`)
            Logger.info('FinancialService', '✅ تم إضافة العمود في جدول الحسابات البنكية: ' + column.name)
          } catch (error) {
            Logger.info('FinancialService', '⚠️ فشل في إضافة العمود ' + column.name + ' في جدول الحسابات البنكية:', error)
          }
        } else {
          Logger.info('FinancialService', '✅ العمود ' + column.name + ' موجود بالفعل في جدول الحسابات البنكية')
        }
      }

    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تحديث مخطط جدول الحسابات البنكية:', error)
    }
  }

  // تحديث مخطط جدول الشيكات
  private async updateChecksTableSchema(): Promise<void> {
    try {
      // التحقق من وجود الأعمدة الجديدة في جدول الشيكات
      const tableInfo = this.db.prepare("PRAGMA table_info(checks)").all()
      const existingColumns = tableInfo.map((col: any) => col.name)

      Logger.info('FinancialService', 'الأعمدة الموجودة في جدول checks:', existingColumns)

      const newColumns = [
        { name: 'entity_type', type: 'TEXT' },
        { name: 'entity_id', type: 'INTEGER' },
        { name: 'entity_name', type: 'TEXT' },
        { name: 'check_type', type: 'TEXT DEFAULT "received"' },
        { name: 'original_payer', type: 'TEXT' },
        { name: 'current_holder', type: 'TEXT' },
        { name: 'is_company_check', type: 'BOOLEAN DEFAULT 0' }
      ]

      // إضافة الأعمدة المفقودة
      for (const column of newColumns) {
        if (!existingColumns.includes(column.name)) {
          try {
            this.db.exec(`ALTER TABLE checks ADD COLUMN ${column.name} ${column.type}`)
            Logger.info('FinancialService', '✅ تم إضافة العمود في جدول الشيكات: ' + column.name)
          } catch (error) {
            Logger.info('FinancialService', '⚠️ فشل في إضافة العمود ' + column.name + ' في جدول الشيكات:', error)
          }
        } else {
          Logger.info('FinancialService', '✅ العمود ' + column.name + ' موجود بالفعل في جدول الشيكات')
        }
      }

    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تحديث مخطط جدول الشيكات:', error)
    }
  }

  // إنشاء جداول المالية
  public async createFinancialTables(): Promise<void> {
    const database = this.db

    // جدول الحسابات البنكية
    database.exec(`
      CREATE TABLE IF NOT EXISTS bank_accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_name TEXT NOT NULL,
        bank_name TEXT NOT NULL,
        account_number TEXT UNIQUE NOT NULL,
        account_type TEXT DEFAULT 'current',
        iban TEXT,
        branch TEXT,
        contact_person TEXT,
        phone TEXT,
        balance DECIMAL(15,2) DEFAULT 0,
        currency TEXT DEFAULT 'SAR',
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // تحديث مخطط جدول الحسابات البنكية إذا كان موجوداً
    await this.updateBankAccountsTableSchema()

    // تحديث مخطط جدول الشيكات إذا كان موجوداً
    await this.updateChecksTableSchema()

    // جدول السندات (القبض والدفع)
    database.exec(`
      CREATE TABLE IF NOT EXISTS vouchers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        voucher_number TEXT UNIQUE NOT NULL,
        voucher_type TEXT NOT NULL CHECK (voucher_type IN ('receipt', 'payment')),
        amount DECIMAL(15,2) NOT NULL,
        description TEXT NOT NULL,
        reference_type TEXT,
        reference_id INTEGER,
        bank_account_id INTEGER,
        payment_method TEXT DEFAULT 'cash' CHECK (payment_method IN ('cash', 'bank_transfer', 'check')),
        voucher_date DATE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (bank_account_id) REFERENCES bank_accounts (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // جدول الشيكات
    database.exec(`
      CREATE TABLE IF NOT EXISTS checks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        check_number TEXT UNIQUE NOT NULL,
        bank_account_id INTEGER NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        payee_name TEXT NOT NULL,
        check_date DATE NOT NULL,
        due_date DATE NOT NULL,
        status TEXT DEFAULT 'issued' CHECK (status IN ('issued', 'cashed', 'bounced', 'cancelled')),
        notes TEXT,
        entity_type TEXT CHECK (entity_type IN ('customer', 'supplier', 'other')),
        entity_id INTEGER,
        entity_name TEXT,
        check_type TEXT DEFAULT 'received' CHECK (check_type IN ('issued', 'received')),
        original_payer TEXT,
        current_holder TEXT,
        is_company_check BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (bank_account_id) REFERENCES bank_accounts (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // جدول دليل الحسابات
    database.exec(`
      CREATE TABLE IF NOT EXISTS chart_of_accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_code TEXT UNIQUE NOT NULL,
        account_name TEXT NOT NULL,
        account_type TEXT NOT NULL CHECK (account_type IN ('asset', 'liability', 'equity', 'revenue', 'expense')),
        parent_id INTEGER,
        level INTEGER NOT NULL DEFAULT 1,
        is_active BOOLEAN DEFAULT 1,
        balance DECIMAL(15,2) DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES chart_of_accounts (id)
      )
    `)

    // جدول القيود اليومية
    database.exec(`
      CREATE TABLE IF NOT EXISTS journal_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        entry_number TEXT UNIQUE NOT NULL,
        entry_date DATE NOT NULL,
        description TEXT NOT NULL,
        reference_type TEXT,
        reference_id INTEGER,
        total_debit DECIMAL(15,2) NOT NULL,
        total_credit DECIMAL(15,2) NOT NULL,
        status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'posted', 'cancelled')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // جدول تفاصيل القيود
    database.exec(`
      CREATE TABLE IF NOT EXISTS journal_entry_details (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        entry_id INTEGER NOT NULL,
        account_id INTEGER NOT NULL,
        debit_amount DECIMAL(15,2) DEFAULT 0,
        credit_amount DECIMAL(15,2) DEFAULT 0,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (entry_id) REFERENCES journal_entries (id) ON DELETE CASCADE,
        FOREIGN KEY (account_id) REFERENCES chart_of_accounts (id)
      )
    `)

    // جدول المعاملات المصرفية
    database.exec(`
      CREATE TABLE IF NOT EXISTS bank_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER NOT NULL,
        transaction_type TEXT NOT NULL CHECK (transaction_type IN ('deposit', 'withdrawal')),
        amount DECIMAL(15,2) NOT NULL,
        description TEXT NOT NULL,
        reference_type TEXT,
        reference_id INTEGER,
        transaction_date DATE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (account_id) REFERENCES bank_accounts (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // جدول الكمبيالات
    database.exec(`
      CREATE TABLE IF NOT EXISTS promissory_notes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        note_number TEXT UNIQUE NOT NULL,
        drawer_name TEXT NOT NULL,
        payee_name TEXT NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        issue_date DATE NOT NULL,
        due_date DATE NOT NULL,
        status TEXT DEFAULT 'issued' CHECK (status IN ('issued', 'paid', 'dishonored', 'cancelled')),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // جدول تحويلات الشيكات
    database.exec(`
      CREATE TABLE IF NOT EXISTS check_transfers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        check_id INTEGER NOT NULL,
        from_entity_type TEXT NOT NULL,
        from_entity_id INTEGER,
        from_entity_name TEXT NOT NULL,
        to_entity_type TEXT NOT NULL,
        to_entity_id INTEGER,
        to_entity_name TEXT NOT NULL,
        transfer_date DATE NOT NULL,
        transfer_amount DECIMAL(15,2),
        transfer_reason TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (check_id) REFERENCES checks (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // إضافة الحقول الجديدة إذا لم تكن موجودة (للتوافق مع قواعد البيانات الموجودة)
    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('check_transfers', 'transfer_amount DECIMAL(15,2)')
    } catch {
      // الحقل موجود بالفعل
    }

    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('check_transfers', 'transfer_reason TEXT')
    } catch {
      // الحقل موجود بالفعل
    }

    // جدول صور الشيكات
    database.exec(`
      CREATE TABLE IF NOT EXISTS check_images (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        check_id INTEGER NOT NULL,
        receipt_id INTEGER,
        image_name TEXT NOT NULL,
        image_path TEXT NOT NULL,
        image_type TEXT,
        image_side TEXT DEFAULT 'front' CHECK (image_side IN ('front', 'back', 'deposit', 'receipt')),
        image_size INTEGER,
        scan_quality TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (check_id) REFERENCES checks (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // إنشاء الفهارس
    database.exec('CREATE INDEX IF NOT EXISTS idx_bank_accounts_active ON bank_accounts(is_active)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_vouchers_type ON vouchers(voucher_type)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_vouchers_date ON vouchers(voucher_date)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_checks_status ON checks(status)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_checks_date ON checks(check_date)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_accounts_type ON chart_of_accounts(account_type)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_accounts_parent ON chart_of_accounts(parent_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON journal_entries(entry_date)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_journal_details_entry ON journal_entry_details(entry_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_bank_transactions_account ON bank_transactions(account_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_bank_transactions_date ON bank_transactions(transaction_date)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_promissory_notes_status ON promissory_notes(status)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_promissory_notes_due_date ON promissory_notes(due_date)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_check_transfers_check ON check_transfers(check_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_check_transfers_date ON check_transfers(transfer_date)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_check_images_check_id ON check_images(check_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_check_images_side ON check_images(image_side)')

    // إنشاء دليل الحسابات الأساسي
    await this.createDefaultChartOfAccounts()

    // تحديث الفواتير المتأخرة
    await this.updateOverdueInvoices()
  }

  // إنشاء دليل الحسابات الأساسي
  private async createDefaultChartOfAccounts(): Promise<void> {
    try {
      // التحقق من وجود حسابات مسبقاً
      const existingAccounts = this.db.prepare('SELECT COUNT(*) as count FROM chart_of_accounts').get() as any
      if (existingAccounts.count > 0) {
        Logger.info('FinancialService', '📊 دليل الحسابات موجود بالفعل')
        return
      }

      Logger.info('FinancialService', '📊 إنشاء دليل الحسابات الأساسي...')

      const defaultAccounts = [
        // الأصول (Assets)
        { code: '1000', name: 'الأصول', type: 'asset', parent: null, level: 1 },
        { code: '1100', name: 'الأصول المتداولة', type: 'asset', parent: '1000', level: 2 },
        { code: '1110', name: 'النقدية والبنوك', type: 'asset', parent: '1100', level: 3 },
        { code: '1111', name: 'الصندوق', type: 'asset', parent: '1110', level: 4 },
        { code: '1112', name: 'البنك الأهلي', type: 'asset', parent: '1110', level: 4 },
        { code: '1113', name: 'بنك الراجحي', type: 'asset', parent: '1110', level: 4 },
        { code: '1120', name: 'العملاء والذمم المدينة', type: 'asset', parent: '1100', level: 3 },
        { code: '1121', name: 'حسابات العملاء', type: 'asset', parent: '1120', level: 4 },
        { code: '1122', name: 'أوراق القبض', type: 'asset', parent: '1120', level: 4 },
        { code: '1130', name: 'المخزون', type: 'asset', parent: '1100', level: 3 },
        { code: '1131', name: 'مخزون المواد الخام', type: 'asset', parent: '1130', level: 4 },
        { code: '1132', name: 'مخزون الإنتاج التام', type: 'asset', parent: '1130', level: 4 },

        { code: '1200', name: 'الأصول الثابتة', type: 'asset', parent: '1000', level: 2 },
        { code: '1210', name: 'الأراضي والمباني', type: 'asset', parent: '1200', level: 3 },
        { code: '1220', name: 'المعدات والآلات', type: 'asset', parent: '1200', level: 3 },
        { code: '1230', name: 'الأثاث والتجهيزات', type: 'asset', parent: '1200', level: 3 },

        // الخصوم (Liabilities)
        { code: '2000', name: 'الخصوم', type: 'liability', parent: null, level: 1 },
        { code: '2100', name: 'الخصوم المتداولة', type: 'liability', parent: '2000', level: 2 },
        { code: '2110', name: 'الموردون والذمم الدائنة', type: 'liability', parent: '2100', level: 3 },
        { code: '2111', name: 'حسابات الموردين', type: 'liability', parent: '2110', level: 4 },
        { code: '2112', name: 'أوراق الدفع', type: 'liability', parent: '2110', level: 4 },
        { code: '2120', name: 'المصروفات المستحقة', type: 'liability', parent: '2100', level: 3 },
        { code: '2130', name: 'الضرائب المستحقة', type: 'liability', parent: '2100', level: 3 },

        // حقوق الملكية (Equity)
        { code: '3000', name: 'حقوق الملكية', type: 'equity', parent: null, level: 1 },
        { code: '3100', name: 'رأس المال', type: 'equity', parent: '3000', level: 2 },
        { code: '3200', name: 'الأرباح المحتجزة', type: 'equity', parent: '3000', level: 2 },
        { code: '3300', name: 'أرباح السنة الحالية', type: 'equity', parent: '3000', level: 2 },

        // الإيرادات (Revenue)
        { code: '4000', name: 'الإيرادات', type: 'revenue', parent: null, level: 1 },
        { code: '4100', name: 'إيرادات المبيعات', type: 'revenue', parent: '4000', level: 2 },
        { code: '4110', name: 'مبيعات المنتجات', type: 'revenue', parent: '4100', level: 3 },
        { code: '4120', name: 'مبيعات الخدمات', type: 'revenue', parent: '4100', level: 3 },
        { code: '4200', name: 'إيرادات أخرى', type: 'revenue', parent: '4000', level: 2 },

        // المصروفات (Expenses)
        { code: '5000', name: 'المصروفات', type: 'expense', parent: null, level: 1 },
        { code: '5100', name: 'تكلفة البضاعة المباعة', type: 'expense', parent: '5000', level: 2 },
        { code: '5200', name: 'المصروفات التشغيلية', type: 'expense', parent: '5000', level: 2 },
        { code: '5210', name: 'الرواتب والأجور', type: 'expense', parent: '5200', level: 3 },
        { code: '5220', name: 'الإيجارات', type: 'expense', parent: '5200', level: 3 },
        { code: '5230', name: 'الكهرباء والماء', type: 'expense', parent: '5200', level: 3 },
        { code: '5240', name: 'الاتصالات', type: 'expense', parent: '5200', level: 3 },
        { code: '5250', name: 'الصيانة والإصلاح', type: 'expense', parent: '5200', level: 3 },
        { code: '5300', name: 'المصروفات الإدارية', type: 'expense', parent: '5000', level: 2 },
        { code: '5400', name: 'المصروفات المالية', type: 'expense', parent: '5000', level: 2 }
      ]

      // إدراج الحسابات الأساسية أولاً
      for (const account of defaultAccounts) {
        let parentId = null
        if (account.parent) {
          const parent = this.db.prepare('SELECT id FROM chart_of_accounts WHERE account_code = ?').get(account.parent) as any
          if (parent) {
            parentId = parent.id
          }
        }

        this.db.prepare(`
          INSERT INTO chart_of_accounts (
            account_code, account_name, account_type, parent_id, level, is_active
          ) VALUES (?, ?, ?, ?, ?, 1)
        `).run([account.code, account.name, account.type, parentId, account.level])
      }

      Logger.info('FinancialService', '✅ تم إنشاء دليل الحسابات الأساسي بنجاح')
    } catch (error) {
      Logger.error('FinancialService', '❌ خطأ في إنشاء دليل الحسابات الأساسي:', error)
    }
  }

  // الحصول على الحسابات البنكية
  public async getBankAccounts(): Promise<BankAccount[]> {
    try {
      const accounts = this.db.prepare(`
        SELECT * FROM bank_accounts 
        WHERE is_active = 1 
        ORDER BY account_name
      `).all() as BankAccount[]

      return accounts
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب الحسابات البنكية:', error)
      return []
    }
  }

  // إنشاء حساب بنكي جديد
  public async createBankAccount(accountData: {
    account_name: string
    bank_name: string
    account_number: string
    account_type?: string
    iban?: string
    branch?: string
    contact_person?: string
    phone?: string
    balance?: number
    initial_balance?: number
    currency?: string
    is_active?: boolean
  }): Promise<ApiResponse> {
    try {
      Logger.info('FinancialService', '🔍 البيانات المستلمة لإنشاء الحساب البنكي:', accountData)

      // دعم كل من balance و initial_balance
      const balanceValue = accountData.balance !== undefined ? accountData.balance : (accountData.initial_balance || 0)

      Logger.info('FinancialService', '🔍 القيم التي سيتم إدراجها:')
      Logger.info('FinancialService', '- account_name:', accountData.account_name)
      Logger.info('FinancialService', '- bank_name:', accountData.bank_name)
      Logger.info('FinancialService', '- account_number:', accountData.account_number)
      Logger.info('FinancialService', '- account_type:', accountData.account_type || 'current')
      Logger.info('FinancialService', '- iban:', accountData.iban || null)
      Logger.info('FinancialService', '- branch:', accountData.branch || null)
      Logger.info('FinancialService', '- contact_person:', accountData.contact_person || null)
      Logger.info('FinancialService', '- phone:', accountData.phone || null)
      Logger.info('FinancialService', '- balance:', balanceValue)
      Logger.info('FinancialService', '- currency:', accountData.currency || 'SAR')
      Logger.info('FinancialService', '- is_active:', accountData.is_active !== undefined ? (accountData.is_active ? 1 : 0) : 1)

      const params = [
        accountData.account_name,
        accountData.bank_name,
        accountData.account_number,
        accountData.account_type || 'current',
        accountData.iban || null,
        accountData.branch || null,
        accountData.contact_person || null,
        accountData.phone || null,
        balanceValue,
        accountData.currency || 'SAR',
        accountData.is_active !== undefined ? (accountData.is_active ? 1 : 0) : 1
      ]

      Logger.info('FinancialService', '🔍 المعاملات النهائية:', params)

      const result = this.db.prepare(`
        INSERT INTO bank_accounts (
          account_name, bank_name, account_number, account_type, iban,
          branch, contact_person, phone, balance, currency, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(...params)

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إنشاء الحساب البنكي بنجاح', data: { id: result.lastInsertRowid } }
      } else {
        return { success: false, message: 'فشل في إنشاء الحساب البنكي' }
      }
    } catch (error: any) {
      Logger.error('FinancialService', 'خطأ في إنشاء الحساب البنكي:', error)
      Logger.error('FinancialService', 'تفاصيل الخطأ:', error.message)
      Logger.error('FinancialService', 'نوع الخطأ:', error.code)

      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'رقم الحساب موجود مسبقاً' }
      }
      if (error.message.includes('no such column')) {
        return { success: false, message: 'خطأ في بنية قاعدة البيانات - يرجى إعادة تشغيل التطبيق' }
      }
      return { success: false, message: 'حدث خطأ في إنشاء الحساب البنكي: ' + error.message }
    }
  }

  // الحصول على السندات
  public async getVouchers(voucherType?: 'receipt' | 'payment'): Promise<Voucher[]> {
    try {
      let query = `
        SELECT v.*, ba.account_name as bank_account_name
        FROM vouchers v
        LEFT JOIN bank_accounts ba ON v.bank_account_id = ba.id
      `
      const params: any[] = []

      if (voucherType) {
        query += ' WHERE v.voucher_type = ?'
        params.push(voucherType)
      }

      query += ' ORDER BY v.created_at DESC'

      const vouchers = this.db.prepare(query).all(...params) as Voucher[]
      return vouchers
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب السندات:', error)
      return []
    }
  }

  // إنشاء سند جديد
  public async createVoucher(voucherData: {
    voucher_type: 'receipt' | 'payment'
    amount: number
    description: string
    reference_type?: string
    reference_id?: number
    bank_account_id?: number
    payment_method?: 'cash' | 'bank_transfer' | 'check'
    voucher_date: string
    entity_type?: string
    entity_id?: number
  }, userId?: number): Promise<ApiResponse> {
    try {
      // توليد رقم السند أولاً
      const voucherNumber = await this.generateVoucherNumber(voucherData.voucher_type)

      const transaction = this.db.transaction(() => {
        // التحقق من صحة البيانات
        const validation = this.validateVoucher(voucherData)
        if (!validation.isValid) {
          throw new Error(validation.message)
        }

        // التحقق من وجود الحساب البنكي وحالته
        if (voucherData.bank_account_id) {
          const bankAccount = this.db.prepare(`
            SELECT id, balance, is_active FROM bank_accounts WHERE id = ?
          `).get(voucherData.bank_account_id)

          if (!bankAccount) {
            throw new Error('الحساب البنكي المحدد غير موجود')
          }

          if (!bankAccount.is_active) {
            throw new Error('الحساب البنكي غير نشط')
          }

          // التحقق من كفاية الرصيد لسندات الصرف
          if (voucherData.voucher_type === 'payment' && bankAccount.balance < voucherData.amount) {
            throw new Error('رصيد الحساب البنكي غير كافي')
          }
        }

        // التحقق من صحة الكيان إذا تم تحديده
        if (voucherData.entity_id && voucherData.entity_type) {
          const entityExists = this.validateEntity(voucherData.entity_type, voucherData.entity_id)
          if (!entityExists) {
            throw new Error('الكيان المحدد غير موجود')
          }
        }

        // رقم السند تم توليده مسبقاً

        const result = this.db.prepare(`
          INSERT INTO vouchers (
            voucher_number, voucher_type, amount, description, reference_type,
            reference_id, bank_account_id, payment_method, voucher_date,
            entity_type, entity_id, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          voucherNumber,
          voucherData.voucher_type,
          voucherData.amount,
          voucherData.description,
          voucherData.reference_type || null,
          voucherData.reference_id || null,
          voucherData.bank_account_id || null,
          voucherData.payment_method || 'cash',
          voucherData.voucher_date,
          voucherData.entity_type || null,
          voucherData.entity_id || null,
          userId || null
        )

        const voucherId = result.lastInsertRowid

        // تحديث رصيد الحساب البنكي إذا كان محدد
        if (voucherData.bank_account_id) {
          const balanceChange = voucherData.voucher_type === 'receipt' ? voucherData.amount : -voucherData.amount
          this.db.prepare(`
            UPDATE bank_accounts
            SET balance = balance + ?, updated_at = datetime('now')
            WHERE id = ?
          `).run(balanceChange, voucherData.bank_account_id)
        }

        // حفظ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()

        return {
          success: true,
          message: 'تم إنشاء السند بنجاح',
          data: { voucherNumber, voucherId }
        }

    })

      return transaction()
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في إنشاء السند:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : 'حدث خطأ في إنشاء السند'
      }
    }
  }

  // الحصول على الشيكات
  public async getChecks(): Promise<Check[]> {
    try {
      const checks = this.db.prepare(`
        SELECT c.*, ba.account_name as bank_name
        FROM checks c
        LEFT JOIN bank_accounts ba ON c.bank_account_id = ba.id
        ORDER BY c.created_at DESC
      `).all() as Check[]

      return checks
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب الشيكات:', error)
      return []
    }
  }

  // إنشاء شيك جديد
  public async createCheck(checkData: {
    check_number: string
    bank_account_id: number
    amount: number
    payee_name: string
    check_date: string
    due_date?: string
    check_type?: string
    notes?: string
    original_payer?: string
    current_holder?: string
    is_company_check?: number
    // الحقول الجديدة للربط بالعملاء والموردين
    entity_type?: 'customer' | 'supplier' | 'other'
    entity_id?: number
    entity_name?: string
  }, userId?: number): Promise<ApiResponse> {
    try {
      // استيراد نظام التحقق من البيانات
      const { FinancialValidator } = await import('../utils/validation')

      // التحقق من صحة البيانات
      const validation = FinancialValidator.validateCheck(checkData)
      if (!validation.isValid) {
        return {
          success: false,
          message: 'بيانات الشيك غير صحيحة',
          data: validation.errors
        }
      }

      // التحقق من وجود الحساب البنكي
      const bankAccount = this.db.prepare(`
        SELECT id, balance, account_name FROM bank_accounts WHERE id = ?
      `).get(checkData.bank_account_id) as any

      if (!bankAccount) {
        return {
          success: false,
          message: 'الحساب البنكي غير موجود'
        }
      }

      // التحقق من كفاية الرصيد للشيكات الصادرة
      const isIssuedCheck = checkData.check_type === 'issued' || checkData.is_company_check === 1
      if (isIssuedCheck && bankAccount.balance < checkData.amount) {
        return {
          success: false,
          message: `الرصيد غير كافي. الرصيد الحالي: ${bankAccount.balance}`
        }
      }

      // إدراج الشيك في قاعدة البيانات
      const result = this.db.prepare(`
        INSERT INTO checks (
          check_number, bank_account_id, amount, payee_name,
          check_date, due_date, check_type, notes, original_payer,
          current_holder, is_company_check, entity_type, entity_id,
          entity_name, created_by, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'issued')
      `).run(
        checkData.check_number,
        checkData.bank_account_id,
        checkData.amount,
        checkData.payee_name,
        checkData.check_date,
        checkData.due_date || null,
        checkData.check_type || 'received',
        checkData.notes || null,
        checkData.original_payer || null,
        checkData.current_holder || null,
        checkData.is_company_check || 0,
        checkData.entity_type || null,
        checkData.entity_id || null,
        checkData.entity_name || null,
        userId || null
      )

      if (result.changes > 0) {
        const checkId = result.lastInsertRowid

        // تحديث رصيد الحساب البنكي حسب نوع الشيك
        if (isIssuedCheck) {
          // خصم المبلغ للشيكات الصادرة
          this.db.prepare(`
            UPDATE bank_accounts
            SET balance = balance - ?, updated_at = datetime('now')
            WHERE id = ?
          `).run(checkData.amount, checkData.bank_account_id)
        } else {
          // إضافة المبلغ للشيكات المستلمة
          this.db.prepare(`
            UPDATE bank_accounts
            SET balance = balance + ?, updated_at = datetime('now')
            WHERE id = ?
          `).run(checkData.amount, checkData.bank_account_id)
        }

        // حفظ قاعدة البيانات
        this.db.exec('PRAGMA wal_checkpoint(FULL)')

        return {
          success: true,
          message: 'تم إنشاء الشيك بنجاح',
          data: {
            checkId,
            checkType: isIssuedCheck ? 'صادر' : 'مستلم',
            accountName: bankAccount.account_name
          }
        }
      } else {
        return { success: false, message: 'فشل في إنشاء الشيك' }
      }
    } catch (error: any) {
      Logger.error('FinancialService', 'خطأ في إنشاء الشيك:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'رقم الشيك موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في إنشاء الشيك: ' + error.message }
    }
  }

  // توليد رقم سند جديد
  public async generateVoucherNumber(voucherType: 'receipt' | 'payment'): Promise<string> {
    try {
      const prefix = voucherType === 'receipt' ? 'REC' : 'PAY'
      
      const lastVoucher = this.db.prepare(`
        SELECT voucher_number FROM vouchers 
        WHERE voucher_number LIKE '${prefix}%' 
        ORDER BY CAST(SUBSTR(voucher_number, ${prefix.length + 1}) AS INTEGER) DESC 
        LIMIT 1
      `).get() as any

      if (lastVoucher) {
        const lastNumber = parseInt(lastVoucher.voucher_number.substring(prefix.length))
        const newNumber = lastNumber + 1
        return prefix + newNumber.toString().padStart(6, '0')
      } else {
        return prefix + '000001'
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في توليد رقم السند:', error)
      const prefix = voucherType === 'receipt' ? 'REC' : 'PAY'
      return prefix + Date.now().toString().slice(-6)
    }
  }

  // الحصول على دليل الحسابات
  public async getChartOfAccounts(): Promise<Account[]> {
    try {
      const accounts = this.db.prepare(`
        SELECT * FROM chart_of_accounts 
        WHERE is_active = 1 
        ORDER BY account_code
      `).all() as Account[]

      return accounts
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب دليل الحسابات:', error)
      return []
    }
  }

  // توليد كود حساب جديد
  public async generateAccountCode(): Promise<string> {
    try {
      const lastAccount = this.db.prepare(`
        SELECT account_code FROM chart_of_accounts
        WHERE account_code LIKE 'ACC%' AND account_code IS NOT NULL AND account_code != ''
        ORDER BY CAST(SUBSTR(account_code, 4) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastAccount && lastAccount.account_code && lastAccount.account_code.length >= 7) {
        const codeNumber = lastAccount.account_code.substring(3)
        const lastNumber = parseInt(codeNumber)

        if (!isNaN(lastNumber)) {
          const newNumber = lastNumber + 1
          return 'ACC' + newNumber.toString().padStart(4, '0')
        }
      }

      return 'ACC0001'
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في توليد كود الحساب:', error)
      return 'ACC' + Date.now().toString().slice(-4)
    }
  }

  // إنشاء حساب جديد في دليل الحسابات
  public async createAccount(accountData: {
    account_code: string
    account_name: string
    account_type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense'
    parent_id?: number
    level?: number
  }): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, parent_id, level
        ) VALUES (?, ?, ?, ?, ?)
      `).run(
        accountData.account_code,
        accountData.account_name,
        accountData.account_type,
        accountData.parent_id || null,
        accountData.level || 1
      )

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إنشاء الحساب بنجاح' }
      } else {
        return { success: false, message: 'فشل في إنشاء الحساب' }
      }
    } catch (error: any) {
      Logger.error('FinancialService', 'خطأ في إنشاء الحساب:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود الحساب موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في إنشاء الحساب' }
    }
  }

  // تحديث حساب في دليل الحسابات
  public async updateAccount(accountId: number, accountData: {
    account_code: string
    account_name: string
    account_type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense'
    parent_id?: number
    level?: number
    is_active?: boolean
  }): Promise<ApiResponse> {
    try {
      // التحقق من عدم تكرار الكود (باستثناء الحساب الحالي)
      const existingAccount = this.db.prepare('SELECT id FROM chart_of_accounts WHERE account_code = ? AND id != ?').get([accountData.account_code, accountId])
      if (existingAccount) {
        return { success: false, message: 'كود الحساب موجود بالفعل' }
      }

      const result = this.db.prepare(`
        UPDATE chart_of_accounts SET
          account_code = ?, account_name = ?, account_type = ?,
          parent_id = ?, level = ?, is_active = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).run(
        accountData.account_code,
        accountData.account_name,
        accountData.account_type,
        accountData.parent_id || null,
        accountData.level || 1,
        accountData.is_active ? 1 : 0,
        accountId
      )

      if (result.changes > 0) {
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث الحساب بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث الحساب' }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تحديث الحساب:', error)
      return { success: false, message: 'حدث خطأ في تحديث الحساب' }
    }
  }

  // حذف حساب من دليل الحسابات
  public async deleteAccount(accountId: number): Promise<ApiResponse> {
    try {
      // التحقق من عدم وجود حسابات فرعية
      const childAccounts = this.db.prepare('SELECT COUNT(*) as count FROM chart_of_accounts WHERE parent_id = ?').get([accountId]) as any
      if (childAccounts.count > 0) {
        return { success: false, message: 'لا يمكن حذف الحساب لوجود حسابات فرعية' }
      }

      const result = this.db.prepare('DELETE FROM chart_of_accounts WHERE id = ?').run([accountId])

      if (result.changes > 0) {
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم حذف الحساب بنجاح' }
      } else {
        return { success: false, message: 'فشل في حذف الحساب' }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في حذف الحساب:', error)
      return { success: false, message: 'حدث خطأ في حذف الحساب' }
    }
  }

  // تحديث حساب بنكي
  public async updateBankAccount(accountId: number, accountData: {
    account_name?: string
    bank_name?: string
    account_number?: string
    iban?: string
    balance?: number
    currency?: string
    is_active?: boolean
  }): Promise<ApiResponse> {
    try {
      const updateFields: string[] = []
      const values: any[] = []

      if (accountData.account_name !== undefined) {
        updateFields.push('account_name = ?')
        values.push(accountData.account_name)
      }
      if (accountData.bank_name !== undefined) {
        updateFields.push('bank_name = ?')
        values.push(accountData.bank_name)
      }
      if (accountData.account_number !== undefined) {
        updateFields.push('account_number = ?')
        values.push(accountData.account_number)
      }
      if (accountData.iban !== undefined) {
        updateFields.push('iban = ?')
        values.push(accountData.iban)
      }
      if (accountData.balance !== undefined) {
        updateFields.push('balance = ?')
        values.push(accountData.balance)
      }
      if (accountData.currency !== undefined) {
        updateFields.push('currency = ?')
        values.push(accountData.currency)
      }
      if (accountData.is_active !== undefined) {
        updateFields.push('is_active = ?')
        values.push(accountData.is_active ? 1 : 0)
      }

      updateFields.push('updated_at = datetime(\'now\')')
      values.push(accountId)

      const result = this.db.prepare(`
        UPDATE bank_accounts
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `).run(...values)

      if (result.changes > 0) {
        return { success: true, message: 'تم تحديث الحساب البنكي بنجاح' }
      } else {
        return { success: false, message: 'لم يتم العثور على الحساب البنكي' }
      }
    } catch (error: any) {
      Logger.error('FinancialService', 'خطأ في تحديث الحساب البنكي:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'رقم الحساب موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في تحديث الحساب البنكي' }
    }
  }

  // حذف حساب بنكي
  public async deleteBankAccount(accountId: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود معاملات مرتبطة بالحساب
      const transactionsCount = this.db.prepare(`
        SELECT COUNT(*) as count FROM vouchers WHERE bank_account_id = ?
      `).get(accountId) as any

      const checksCount = this.db.prepare(`
        SELECT COUNT(*) as count FROM checks WHERE bank_account_id = ?
      `).get(accountId) as any

      if (transactionsCount.count > 0 || checksCount.count > 0) {
        return {
          success: false,
          message: 'لا يمكن حذف الحساب لوجود معاملات مرتبطة به. يمكنك إلغاء تفعيله بدلاً من ذلك.'
        }
      }

      const result = this.db.prepare(`
        DELETE FROM bank_accounts WHERE id = ?
      `).run(accountId)

      if (result.changes > 0) {
        return { success: true, message: 'تم حذف الحساب البنكي بنجاح' }
      } else {
        return { success: false, message: 'لم يتم العثور على الحساب البنكي' }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في حذف الحساب البنكي:', error)
      return { success: false, message: 'حدث خطأ في حذف الحساب البنكي' }
    }
  }

  // إنشاء معاملة مصرفية
  public async createBankTransaction(transactionData: {
    account_id: number
    transaction_type: 'deposit' | 'withdrawal'
    amount: number
    description: string
    reference_type?: string
    reference_id?: number
    transaction_date: string
  }, userId?: number): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        INSERT INTO bank_transactions (
          account_id, transaction_type, amount, description, reference_type,
          reference_id, transaction_date, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        transactionData.account_id,
        transactionData.transaction_type,
        transactionData.amount,
        transactionData.description,
        transactionData.reference_type || null,
        transactionData.reference_id || null,
        transactionData.transaction_date,
        userId || null
      )

      if (result.changes > 0) {
        // تحديث رصيد الحساب البنكي
        const balanceChange = transactionData.transaction_type === 'deposit'
          ? transactionData.amount
          : -transactionData.amount

        this.db.prepare(`
          UPDATE bank_accounts
          SET balance = balance + ?, updated_at = datetime('now')
          WHERE id = ?
        `).run(balanceChange, transactionData.account_id)

        return { success: true, message: 'تم إنشاء المعاملة المصرفية بنجاح' }
      } else {
        return { success: false, message: 'فشل في إنشاء المعاملة المصرفية' }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في إنشاء المعاملة المصرفية:', error)
      return { success: false, message: 'حدث خطأ في إنشاء المعاملة المصرفية' }
    }
  }

  // تحديث حالة الشيك
  public async updateCheckStatus(checkId: number, status: 'issued' | 'cashed' | 'bounced' | 'cancelled'): Promise<ApiResponse> {
    const transaction = this.db.transaction(() => {
        // جلب بيانات الشيك الحالية
        const check = this.db.prepare(`
          SELECT * FROM checks WHERE id = ?
        `).get(checkId) as any

        if (!check) {
          throw new Error('لم يتم العثور على الشيك')
        }

        const oldStatus = check.status
        const isIssuedCheck = check.check_type === 'issued' || check.is_company_check === 1

        // تحديث حالة الشيك
        const result = this.db.prepare(`
          UPDATE checks
          SET status = ?, updated_at = datetime('now')
          WHERE id = ?
        `).run(status, checkId)

        if (result.changes === 0) {
          throw new Error('فشل في تحديث حالة الشيك')
        }

        // تحديث رصيد الحساب البنكي حسب التغيير في الحالة
        if (oldStatus !== status && check.bank_account_id) {
          let balanceChange = 0

          // حساب التغيير في الرصيد
          if (oldStatus === 'issued' && status === 'cashed') {
            // تحصيل شيك - لا تغيير إضافي للشيكات الصادرة (تم خصمها عند الإنشاء)
            // للشيكات المستلمة - لا تغيير إضافي (تم إضافتها عند الإنشاء)
            balanceChange = 0
          } else if (oldStatus === 'cashed' && status === 'bounced') {
            // ارتداد شيك محصل - عكس التأثير
            balanceChange = isIssuedCheck ? check.amount : -check.amount
          } else if (oldStatus === 'issued' && status === 'bounced') {
            // ارتداد شيك مباشرة - للشيكات الصادرة: إرجاع المبلغ، للمستلمة: خصم المبلغ
            balanceChange = isIssuedCheck ? check.amount : -check.amount
          } else if (oldStatus === 'bounced' && status === 'cashed') {
            // تحصيل شيك مرتد - تطبيق التأثير مرة أخرى
            balanceChange = isIssuedCheck ? -check.amount : check.amount
          } else if (status === 'cancelled') {
            // إلغاء الشيك - عكس التأثير الأصلي
            if (oldStatus === 'cashed') {
              balanceChange = isIssuedCheck ? check.amount : -check.amount
            } else if (oldStatus === 'issued' && isIssuedCheck) {
              balanceChange = check.amount // إرجاع المبلغ المخصوم للشيكات الصادرة
            } else if (oldStatus === 'issued' && !isIssuedCheck) {
              balanceChange = -check.amount // خصم المبلغ المضاف للشيكات المستلمة
            }
          }

          // تطبيق التغيير على الرصيد
          if (balanceChange !== 0) {
            this.db.prepare(`
              UPDATE bank_accounts
              SET balance = balance + ?, updated_at = datetime('now')
              WHERE id = ?
            `).run(balanceChange, check.bank_account_id)
          }
        }

        return {
          success: true,
          message: 'تم تحديث حالة الشيك بنجاح',
          data: {
            oldStatus,
            newStatus: status,
            checkType: isIssuedCheck ? 'صادر' : 'مستلم'
          }
        }

    })

    try {
      return transaction()
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تحديث حالة الشيك:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : 'حدث خطأ في تحديث حالة الشيك'
      }
    }
  }

  // توليد رقم شيك جديد
  public async generateCheckNumber(): Promise<string> {
    try {
      const lastCheck = this.db.prepare(`
        SELECT check_number FROM checks
        WHERE check_number LIKE 'CHK%'
        ORDER BY CAST(SUBSTR(check_number, 4) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastCheck) {
        const lastNumber = parseInt(lastCheck.check_number.substring(3))
        const newNumber = lastNumber + 1
        return 'CHK' + newNumber.toString().padStart(6, '0')
      } else {
        return 'CHK000001'
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في توليد رقم الشيك:', error)
      return 'CHK' + Date.now().toString().slice(-6)
    }
  }

  // الحصول على الكمبيالات
  public async getPromissoryNotes(): Promise<any[]> {
    try {
      const notes = this.db.prepare(`
        SELECT * FROM promissory_notes
        ORDER BY created_at DESC
      `).all()

      return notes
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب الكمبيالات:', error)
      return []
    }
  }

  // إنشاء كمبيالة جديدة
  public async createPromissoryNote(noteData: {
    note_number: string
    drawer_name: string
    payee_name: string
    amount: number
    issue_date: string
    due_date: string
    drawer_entity_type?: string
    drawer_entity_id?: number
    payee_entity_type?: string
    payee_entity_id?: number
    notes?: string
  }, userId?: number): Promise<ApiResponse> {
    const transaction = this.db.transaction(() => {
        // التحقق من صحة البيانات
        const validation = this.validatePromissoryNote(noteData)
        if (!validation.isValid) {
          throw new Error(validation.message)
        }

        // التحقق من عدم تكرار رقم الكمبيالة
        const existingNote = this.db.prepare(`
          SELECT id FROM promissory_notes WHERE note_number = ?
        `).get(noteData.note_number)

        if (existingNote) {
          throw new Error('رقم الكمبيالة موجود مسبقاً')
        }

        // التحقق من صحة الكيانات إذا تم تحديدها
        if (noteData.drawer_entity_id && noteData.drawer_entity_type) {
          const drawerExists = this.validateEntity(noteData.drawer_entity_type, noteData.drawer_entity_id)
          if (!drawerExists) {
            throw new Error('الساحب المحدد غير موجود')
          }
        }

        if (noteData.payee_entity_id && noteData.payee_entity_type) {
          const payeeExists = this.validateEntity(noteData.payee_entity_type, noteData.payee_entity_id)
          if (!payeeExists) {
            throw new Error('المستفيد المحدد غير موجود')
          }
        }

        const result = this.db.prepare(`
          INSERT INTO promissory_notes (
            note_number, drawer_name, payee_name, amount,
            issue_date, due_date, drawer_entity_type, drawer_entity_id,
            payee_entity_type, payee_entity_id, notes, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          noteData.note_number,
          noteData.drawer_name,
          noteData.payee_name,
          noteData.amount,
          noteData.issue_date,
          noteData.due_date,
          noteData.drawer_entity_type || null,
          noteData.drawer_entity_id || null,
          noteData.payee_entity_type || null,
          noteData.payee_entity_id || null,
          noteData.notes || null,
          userId || null
        )

        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()

        return {
          success: true,
          message: 'تم إنشاء الكمبيالة بنجاح',
          data: { noteId: result.lastInsertRowid }
        }

    })

    try {
      return transaction()
    } catch (error: any) {
      Logger.error('FinancialService', 'خطأ في إنشاء الكمبيالة:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : 'حدث خطأ في إنشاء الكمبيالة'
      }
    }
  }

  // الحصول على كمبيالة بالمعرف
  public async getPromissoryNoteById(noteId: number): Promise<PromissoryNote | null> {
    try {
      const note = this.db.prepare(`
        SELECT * FROM promissory_notes WHERE id = ?
      `).get(noteId) as PromissoryNote

      return note || null
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب الكمبيالة:', error)
      return null
    }
  }

  // تحديث كمبيالة
  public async updatePromissoryNote(noteId: number, noteData: Partial<PromissoryNote>): Promise<ApiResponse> {
    try {
      const updateFields: string[] = []
      const values: any[] = []

      if (noteData.drawer_name) {
        updateFields.push('drawer_name = ?')
        values.push(noteData.drawer_name)
      }
      if (noteData.payee_name) {
        updateFields.push('payee_name = ?')
        values.push(noteData.payee_name)
      }
      if (noteData.amount) {
        updateFields.push('amount = ?')
        values.push(noteData.amount)
      }
      if (noteData.issue_date) {
        updateFields.push('issue_date = ?')
        values.push(noteData.issue_date)
      }
      if (noteData.due_date) {
        updateFields.push('due_date = ?')
        values.push(noteData.due_date)
      }
      if (noteData.status) {
        updateFields.push('status = ?')
        values.push(noteData.status)
      }
      if (noteData.notes !== undefined) {
        updateFields.push('notes = ?')
        values.push(noteData.notes)
      }

      if (updateFields.length === 0) {
        return { success: false, message: 'لا توجد بيانات للتحديث' }
      }

      updateFields.push('updated_at = datetime(\'now\')')
      values.push(noteId)

      const result = this.db.prepare(`
        UPDATE promissory_notes
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `).run(...values)

      if (result.changes > 0) {
        return { success: true, message: 'تم تحديث الكمبيالة بنجاح' }
      } else {
        return { success: false, message: 'لم يتم العثور على الكمبيالة' }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تحديث الكمبيالة:', error)
      return { success: false, message: 'حدث خطأ في تحديث الكمبيالة' }
    }
  }

  // حذف كمبيالة
  public async deletePromissoryNote(noteId: number): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        DELETE FROM promissory_notes WHERE id = ?
      `).run(noteId)

      if (result.changes > 0) {
        return { success: true, message: 'تم حذف الكمبيالة بنجاح' }
      } else {
        return { success: false, message: 'لم يتم العثور على الكمبيالة' }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في حذف الكمبيالة:', error)
      return { success: false, message: 'حدث خطأ في حذف الكمبيالة' }
    }
  }

  // تحويل كمبيالة
  public async transferPromissoryNote(transferData: {
    noteId: number
    newPayeeName: string
    transferDate: string
    notes?: string
  }): Promise<ApiResponse> {
    try {
      // التحقق من وجود الكمبيالة
      const note = await this.getPromissoryNoteById(transferData.noteId)
      if (!note) {
        return { success: false, message: 'لم يتم العثور على الكمبيالة' }
      }

      // تحديث اسم المستفيد
      const result = this.db.prepare(`
        UPDATE promissory_notes
        SET payee_name = ?, notes = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run(transferData.newPayeeName, transferData.notes || note.notes, transferData.noteId)

      if (result.changes > 0) {
        return { success: true, message: 'تم تحويل الكمبيالة بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحويل الكمبيالة' }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تحويل الكمبيالة:', error)
      return { success: false, message: 'حدث خطأ في تحويل الكمبيالة' }
    }
  }

  // تحديث حالة الكمبيالة
  public async updatePromissoryNoteStatus(noteId: number, status: 'issued' | 'paid' | 'dishonored' | 'cancelled', bankAccountId?: number): Promise<ApiResponse> {
    const transaction = this.db.transaction(() => {
        // جلب بيانات الكمبيالة الحالية
        const note = this.db.prepare(`
          SELECT * FROM promissory_notes WHERE id = ?
        `).get(noteId) as any

        if (!note) {
          throw new Error('لم يتم العثور على الكمبيالة')
        }

        const oldStatus = note.status

        // تحديث حالة الكمبيالة
        const result = this.db.prepare(`
          UPDATE promissory_notes
          SET status = ?, updated_at = datetime('now')
          WHERE id = ?
        `).run(status, noteId)

        if (result.changes === 0) {
          throw new Error('فشل في تحديث حالة الكمبيالة')
        }

        // تحديث رصيد الحساب البنكي عند التحصيل أو الإلغاء
        if (bankAccountId && oldStatus !== status) {
          let balanceChange = 0

          if (oldStatus === 'issued' && status === 'paid') {
            // تحصيل الكمبيالة - إضافة المبلغ للحساب
            balanceChange = note.amount
          } else if (oldStatus === 'paid' && status === 'dishonored') {
            // عدم دفع كمبيالة محصلة - خصم المبلغ
            balanceChange = -note.amount
          } else if (status === 'cancelled' && oldStatus === 'paid') {
            // إلغاء كمبيالة محصلة - خصم المبلغ
            balanceChange = -note.amount
          }

          // تطبيق التغيير على الرصيد
          if (balanceChange !== 0) {
            this.db.prepare(`
              UPDATE bank_accounts
              SET balance = balance + ?, updated_at = datetime('now')
              WHERE id = ?
            `).run(balanceChange, bankAccountId)
          }
        }

        return {
          success: true,
          message: 'تم تحديث حالة الكمبيالة بنجاح',
          data: {
            oldStatus,
            newStatus: status,
            amount: note.amount
          }
        }

    })

    try {
      return transaction()
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تحديث حالة الكمبيالة:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : 'حدث خطأ في تحديث حالة الكمبيالة'
      }
    }
  }

  // تحصيل كمبيالة مع تحديد الحساب البنكي
  public async collectPromissoryNote(noteId: number, bankAccountId: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود الكمبيالة
      const note = this.db.prepare(`
        SELECT * FROM promissory_notes WHERE id = ? AND status = 'issued'
      `).get(noteId) as any

      if (!note) {
        return { success: false, message: 'لم يتم العثور على الكمبيالة أو أنها غير قابلة للتحصيل' }
      }

      // التحقق من وجود الحساب البنكي
      const bankAccount = this.db.prepare(`
        SELECT * FROM bank_accounts WHERE id = ? AND is_active = 1
      `).get(bankAccountId) as any

      if (!bankAccount) {
        return { success: false, message: 'الحساب البنكي غير موجود أو غير نشط' }
      }

      // تحديث حالة الكمبيالة وإضافة المبلغ للحساب
      const result = await this.updatePromissoryNoteStatus(noteId, 'paid', bankAccountId)

      if (result.success) {
        // تم تحصيل الكمبيالة بنجاح
        // يمكن إضافة قيد يومي هنا إذا لزم الأمر

        return {
          success: true,
          message: 'تم تحصيل الكمبيالة بنجاح',
          data: {
            noteId,
            amount: note.amount,
            bankAccount: bankAccount.account_name
          }
        }
      } else {
        return result
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تحصيل الكمبيالة:', error)
      return { success: false, message: 'حدث خطأ في تحصيل الكمبيالة' }
    }
  }

  // ==================== القيود اليومية ====================

  // إنشاء قيد يومي جديد
  public async createJournalEntry(entryData: {
    entry_date: string
    description: string
    reference_type?: string
    reference_id?: number
    details: Array<{
      account_id: number
      debit_amount?: number
      credit_amount?: number
      description?: string
    }>
  }, userId?: number): Promise<ApiResponse> {
    try {
      // التحقق من توازن القيد
      const totalDebit = entryData.details.reduce((sum, detail) => sum + (detail.debit_amount || 0), 0)
      const totalCredit = entryData.details.reduce((sum, detail) => sum + (detail.credit_amount || 0), 0)

      if (Math.abs(totalDebit - totalCredit) > 0.01) {
        return {
          success: false,
          message: 'القيد غير متوازن - مجموع المدين يجب أن يساوي مجموع الدائن',
          details: { totalDebit, totalCredit, difference: totalDebit - totalCredit }
        }
      }

      // تنفيذ العملية مع معاملة آمنة
      const result = await this.db.executeInTransaction(async () => {
        // توليد رقم القيد
        const entryNumber = await this.generateJournalEntryNumber()

        // إدراج القيد الرئيسي
        const entryResult = this.db.prepare(`
          INSERT INTO journal_entries (
            entry_number, entry_date, description, reference_type, reference_id,
            total_debit, total_credit, status, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, 'draft', ?)
        `).run(
          entryNumber,
          entryData.entry_date,
          entryData.description,
          entryData.reference_type || null,
          entryData.reference_id || null,
          totalDebit,
          totalCredit,
          userId || null
        )

        if (entryResult.changes === 0) {
          throw new Error('فشل في إنشاء القيد اليومي')
        }

        const entryId = entryResult.lastInsertRowid

        // إدراج تفاصيل القيد
        for (const detail of entryData.details) {
          const detailResult = this.db.prepare(`
            INSERT INTO journal_entry_details (
              entry_id, account_id, debit_amount, credit_amount, description
            ) VALUES (?, ?, ?, ?, ?)
          `).run(
            entryId,
            detail.account_id,
            detail.debit_amount || 0,
            detail.credit_amount || 0,
            detail.description || null
          )

          if (detailResult.changes === 0) {
            throw new Error('فشل في إدراج تفاصيل القيد')
          }
        }

        return { entryId, entryNumber }
      })

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم إنشاء القيد اليومي بنجاح',
        data: result
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في إنشاء القيد اليومي:', error)
      return { success: false, message: 'حدث خطأ في إنشاء القيد اليومي' }
    }
  }

  // توليد رقم القيد اليومي
  public async generateJournalEntryNumber(): Promise<string> {
    try {
      const currentYear = new Date().getFullYear()
      const prefix = 'JE' + currentYear

      const lastEntry = this.db.prepare(`
        SELECT entry_number FROM journal_entries
        WHERE entry_number LIKE ?
        ORDER BY id DESC LIMIT 1
      `).get(prefix + '%')

      let nextNumber = 1
      if (lastEntry) {
        const lastNumber = parseInt(lastEntry.entry_number.replace(prefix, ''))
        nextNumber = lastNumber + 1
      }

      return prefix + nextNumber.toString().padStart(6, '0')
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في توليد رقم القيد:', error)
      const timestamp = Date.now()
      return 'JE' + timestamp
    }
  }

  // الحصول على القيود اليومية
  public async getJournalEntries(filters?: {
    startDate?: string
    endDate?: string
    status?: string
    accountId?: number
  }): Promise<any[]> {
    try {
      let sql = `
        SELECT je.*, u.full_name as created_by_name
        FROM journal_entries je
        LEFT JOIN users u ON je.created_by = u.id
        WHERE 1=1
      `
      const params: any[] = []

      if (filters?.startDate) {
        sql += ' AND je.entry_date >= ?'
        params.push(filters.startDate)
      }

      if (filters?.endDate) {
        sql += ' AND je.entry_date <= ?'
        params.push(filters.endDate)
      }

      if (filters?.status) {
        sql += ' AND je.status = ?'
        params.push(filters.status)
      }

      if (filters?.accountId) {
        sql += ` AND je.id IN (
          SELECT DISTINCT entry_id FROM journal_entry_details
          WHERE account_id = ?
        )`
        params.push(filters.accountId)
      }

      sql += ' ORDER BY je.entry_date DESC, je.id DESC'

      const entries = this.db.prepare(sql).all(params)

      // جلب تفاصيل كل قيد
      for (const entry of entries) {
        const details = this.db.prepare(`
          SELECT jed.*, coa.account_name, coa.account_code
          FROM journal_entry_details jed
          JOIN chart_of_accounts coa ON jed.account_id = coa.id
          WHERE jed.entry_id = ?
          ORDER BY jed.id
        `).all(entry.id)

        entry.details = details
      }

      return entries
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب القيود اليومية:', error)
      return []
    }
  }

  // تحديث قيد يومي
  public async updateJournalEntry(entryId: number, entryData: {
    entry_date?: string
    description?: string
    reference_type?: string
    reference_id?: number
    details?: Array<{
      account_id: number
      debit_amount?: number
      credit_amount?: number
      description?: string
    }>
  }): Promise<ApiResponse> {
    try {
      // التحقق من وجود القيد
      const existingEntry = this.db.prepare('SELECT * FROM journal_entries WHERE id = ?').get(entryId)
      if (!existingEntry) {
        return { success: false, message: 'لم يتم العثور على القيد' }
      }

      // التحقق من حالة القيد - لا يمكن تعديل القيود المرحلة
      if (existingEntry.status === 'posted') {
        return { success: false, message: 'لا يمكن تعديل قيد مرحل' }
      }

      // تحديث البيانات الأساسية للقيد
      const updateFields: string[] = []
      const updateValues: any[] = []

      if (entryData.entry_date) {
        updateFields.push('entry_date = ?')
        updateValues.push(entryData.entry_date)
      }

      if (entryData.description) {
        updateFields.push('description = ?')
        updateValues.push(entryData.description)
      }

      if (entryData.reference_type !== undefined) {
        updateFields.push('reference_type = ?')
        updateValues.push(entryData.reference_type)
      }

      if (entryData.reference_id !== undefined) {
        updateFields.push('reference_id = ?')
        updateValues.push(entryData.reference_id)
      }

      // تحديث التفاصيل إذا تم تمريرها
      if (entryData.details) {
        // التحقق من توازن القيد
        const totalDebit = entryData.details.reduce((sum, detail) => sum + (detail.debit_amount || 0), 0)
        const totalCredit = entryData.details.reduce((sum, detail) => sum + (detail.credit_amount || 0), 0)

        if (Math.abs(totalDebit - totalCredit) > 0.01) {
          return {
            success: false,
            message: 'القيد غير متوازن - مجموع المدين يجب أن يساوي مجموع الدائن',
            details: { totalDebit, totalCredit, difference: totalDebit - totalCredit }
          }
        }

        updateFields.push('total_debit = ?', 'total_credit = ?')
        updateValues.push(totalDebit, totalCredit)

        // تنفيذ تحديث التفاصيل مع معاملة آمنة
        await this.db.executeInTransaction(async () => {
          // حذف التفاصيل القديمة
          this.db.prepare('DELETE FROM journal_entry_details WHERE entry_id = ?').run(entryId)

          // إدراج التفاصيل الجديدة
          if (entryData.details) {
            for (const detail of entryData.details) {
            const detailResult = this.db.prepare(`
              INSERT INTO journal_entry_details (
                entry_id, account_id, debit_amount, credit_amount, description
              ) VALUES (?, ?, ?, ?, ?)
            `).run(
              entryId,
              detail.account_id,
              detail.debit_amount || 0,
              detail.credit_amount || 0,
              detail.description || null
            )

            if (detailResult.changes === 0) {
              throw new Error('فشل في تحديث تفاصيل القيد')
            }
          }
        }
        })
      }

      // تحديث القيد الرئيسي إذا كان هناك تحديثات
      if (updateFields.length > 0) {
        updateFields.push('updated_at = datetime(\'now\')')
        updateValues.push(entryId)

        const updateResult = this.db.prepare(`
          UPDATE journal_entries
          SET ${updateFields.join(', ')}
          WHERE id = ?
        `).run(...updateValues)

        if (updateResult.changes === 0) {
          return { success: false, message: 'فشل في تحديث القيد' }
        }
      }

      return { success: true, message: 'تم تحديث القيد اليومي بنجاح' }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تحديث القيد اليومي:', error)
      return { success: false, message: 'حدث خطأ في تحديث القيد اليومي' }
    }
  }

  // حذف قيد يومي
  public async deleteJournalEntry(entryId: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود القيد
      const existingEntry = this.db.prepare('SELECT * FROM journal_entries WHERE id = ?').get(entryId)
      if (!existingEntry) {
        return { success: false, message: 'لم يتم العثور على القيد' }
      }

      // التحقق من حالة القيد - لا يمكن حذف القيود المرحلة
      if (existingEntry.status === 'posted') {
        return { success: false, message: 'لا يمكن حذف قيد مرحل' }
      }

      // حذف التفاصيل أولاً (سيتم حذفها تلقائياً بسبب CASCADE)
      const deleteResult = this.db.prepare('DELETE FROM journal_entries WHERE id = ?').run(entryId)

      if (deleteResult.changes > 0) {
        return { success: true, message: 'تم حذف القيد اليومي بنجاح' }
      } else {
        return { success: false, message: 'فشل في حذف القيد' }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في حذف القيد اليومي:', error)
      return { success: false, message: 'حدث خطأ في حذف القيد اليومي' }
    }
  }

  // ترحيل قيد يومي
  public async postJournalEntry(entryId: number, _userId?: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود القيد
      const existingEntry = this.db.prepare('SELECT * FROM journal_entries WHERE id = ?').get(entryId)
      if (!existingEntry) {
        return { success: false, message: 'لم يتم العثور على القيد' }
      }

      // التحقق من حالة القيد
      if (existingEntry.status === 'posted') {
        return { success: false, message: 'القيد مرحل مسبقاً' }
      }

      if (existingEntry.status === 'cancelled') {
        return { success: false, message: 'لا يمكن ترحيل قيد ملغي' }
      }

      // التحقق من توازن القيد
      if (Math.abs(existingEntry.total_debit - existingEntry.total_credit) > 0.01) {
        return { success: false, message: 'القيد غير متوازن - لا يمكن ترحيله' }
      }

      // تنفيذ عملية الترحيل مع معاملة آمنة
      await this.db.executeInTransaction(async () => {
        // تحديث أرصدة الحسابات
        const details = this.db.prepare(`
          SELECT jed.*, coa.account_type
          FROM journal_entry_details jed
          JOIN chart_of_accounts coa ON jed.account_id = coa.id
          WHERE jed.entry_id = ?
        `).all(entryId)

        for (const detail of details) {
          let balanceChange = 0

          // حساب التغيير في الرصيد حسب نوع الحساب
          if (['asset', 'expense'].includes(detail.account_type)) {
            // الأصول والمصروفات: المدين يزيد الرصيد، الدائن ينقص
            balanceChange = (detail.debit_amount || 0) - (detail.credit_amount || 0)
          } else {
            // الخصوم والإيرادات وحقوق الملكية: الدائن يزيد الرصيد، المدين ينقص
            balanceChange = (detail.credit_amount || 0) - (detail.debit_amount || 0)
          }

          // تحديث رصيد الحساب
          const balanceUpdateResult = this.db.prepare(`
            UPDATE chart_of_accounts
            SET balance = balance + ?, updated_at = datetime('now')
            WHERE id = ?
          `).run(balanceChange, detail.account_id)

          if (balanceUpdateResult.changes === 0) {
            throw new Error('فشل في تحديث رصيد الحساب ' + detail.account_id)
          }
        }

        // تحديث حالة القيد إلى مرحل
        const updateResult = this.db.prepare(`
          UPDATE journal_entries
          SET status = 'posted', updated_at = datetime('now')
          WHERE id = ?
        `).run(entryId)

        if (updateResult.changes === 0) {
          throw new Error('فشل في تحديث حالة القيد')
        }
      })

      return { success: true, message: 'تم ترحيل القيد اليومي بنجاح' }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في ترحيل القيد اليومي:', error)
      return { success: false, message: 'حدث خطأ في ترحيل القيد اليومي' }
    }
  }

  // توليد رقم كمبيالة جديد
  public async generatePromissoryNoteNumber(): Promise<string> {
    try {
      const lastNote = this.db.prepare(`
        SELECT note_number FROM promissory_notes
        WHERE note_number LIKE 'PN%'
        ORDER BY CAST(SUBSTR(note_number, 3) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastNote) {
        const lastNumber = parseInt(lastNote.note_number.substring(2))
        const newNumber = lastNumber + 1
        return 'PN' + newNumber.toString().padStart(6, '0')
      } else {
        return 'PN000001'
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في توليد رقم الكمبيالة:', error)
      return 'PN' + Date.now().toString().slice(-6)
    }
  }

  // الحصول على المعاملات المصرفية
  public async getBankTransactions(accountId?: number): Promise<any[]> {
    try {
      let query = `
        SELECT bt.*, ba.account_name, ba.bank_name
        FROM bank_transactions bt
        LEFT JOIN bank_accounts ba ON bt.account_id = ba.id
      `
      const params: any[] = []

      if (accountId) {
        query += ' WHERE bt.account_id = ?'
        params.push(accountId)
      }

      query += ' ORDER BY bt.created_at DESC'

      const transactions = this.db.prepare(query).all(...params)
      return transactions
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب المعاملات المصرفية:', error)
      return []
    }
  }

  // الحصول على الشيكات القابلة للتحويل
  public async getTransferableChecks(): Promise<any[]> {
    try {
      const checks = this.db.prepare(`
        SELECT c.*, ba.account_name as bank_name
        FROM checks c
        LEFT JOIN bank_accounts ba ON c.bank_account_id = ba.id
        WHERE c.status IN ('issued', 'transferred')
        ORDER BY c.created_at DESC
      `).all()

      return checks
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب الشيكات القابلة للتحويل:', error)
      return []
    }
  }

  // الحصول على تحويلات الشيكات
  public async getCheckTransfers(): Promise<any[]> {
    try {
      const transfers = this.db.prepare(`
        SELECT ct.*, c.check_number, c.amount, u.username as created_by_name
        FROM check_transfers ct
        LEFT JOIN checks c ON ct.check_id = c.id
        LEFT JOIN users u ON ct.created_by = u.id
        ORDER BY ct.created_at DESC
      `).all()

      return transfers
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب تحويلات الشيكات:', error)
      return []
    }
  }

  // تحويل شيك
  public async transferCheck(transferData: {
    check_id: number
    from_entity_type: string
    from_entity_id?: number
    from_entity_name: string
    to_entity_type: string
    to_entity_id?: number
    to_entity_name: string
    transfer_date: string
    transfer_reason?: string
    notes?: string
    created_by?: number
  }): Promise<ApiResponse> {
    const transaction = this.db.transaction(() => {
        // التحقق من وجود الشيك وحالته
        const check = this.db.prepare(`
          SELECT * FROM checks WHERE id = ? AND status IN ('issued', 'transferred')
        `).get(transferData.check_id)

        if (!check) {
          throw new Error('لم يتم العثور على الشيك أو أن الشيك غير قابل للتحويل')
        }

        // التحقق من صحة الكيان المحول إليه
        if (transferData.to_entity_id) {
          let entityExists = false
          if (transferData.to_entity_type === 'customer') {
            const customer = this.db.prepare('SELECT id FROM customers WHERE id = ? AND is_active = 1').get(transferData.to_entity_id)
            entityExists = !!customer
          } else if (transferData.to_entity_type === 'supplier') {
            const supplier = this.db.prepare('SELECT id FROM suppliers WHERE id = ? AND is_active = 1').get(transferData.to_entity_id)
            entityExists = !!supplier
          }

          if (!entityExists) {
            throw new Error(`الكيان المحول إليه غير موجود`)
          }
        }

        // إنشاء سجل التحويل
        const result = this.db.prepare(`
          INSERT INTO check_transfers (
            check_id, from_entity_type, from_entity_id, from_entity_name,
            to_entity_type, to_entity_id, to_entity_name, transfer_date,
            transfer_reason, notes, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          transferData.check_id,
          transferData.from_entity_type,
          transferData.from_entity_id || null,
          transferData.from_entity_name,
          transferData.to_entity_type,
          transferData.to_entity_id || null,
          transferData.to_entity_name,
          transferData.transfer_date,
          transferData.transfer_reason || null,
          transferData.notes || null,
          transferData.created_by || null
        )

        // تحديث حامل الشيك الحالي ومعلومات الكيان
        this.db.prepare(`
          UPDATE checks
          SET current_holder = ?,
              entity_type = ?,
              entity_id = ?,
              entity_name = ?,
              status = 'transferred',
              updated_at = datetime('now')
          WHERE id = ?
        `).run(
          transferData.to_entity_name,
          transferData.to_entity_type,
          transferData.to_entity_id || null,
          transferData.to_entity_name,
          transferData.check_id
        )

        return {
          success: true,
          message: 'تم تحويل الشيك بنجاح',
          data: { transferId: result.lastInsertRowid }
        }

    })

    try {
      return transaction()
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تحويل الشيك:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : 'حدث خطأ في تحويل الشيك'
      }
    }
  }

  // إنشاء تحويل شيك مع معلومات إضافية
  public async createCheckTransfer(transferData: {
    check_id: number
    from_entity_type: string
    from_entity_id?: number
    from_entity_name: string
    to_entity_type: string
    to_entity_id?: number
    to_entity_name: string
    transfer_date: string
    transfer_amount?: number
    transfer_reason?: string
    notes?: string
  }, userId?: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود الشيك
      const check = this.db.prepare(`
        SELECT * FROM checks WHERE id = ?
      `).get(transferData.check_id)

      if (!check) {
        return { success: false, message: 'لم يتم العثور على الشيك' }
      }

      // إنشاء سجل التحويل مع معلومات إضافية
      const result = this.db.prepare(`
        INSERT INTO check_transfers (
          check_id, from_entity_type, from_entity_id, from_entity_name,
          to_entity_type, to_entity_id, to_entity_name, transfer_date,
          transfer_amount, transfer_reason, notes, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        transferData.check_id,
        transferData.from_entity_type,
        transferData.from_entity_id || null,
        transferData.from_entity_name,
        transferData.to_entity_type,
        transferData.to_entity_id || null,
        transferData.to_entity_name,
        transferData.transfer_date,
        transferData.transfer_amount || null,
        transferData.transfer_reason || null,
        transferData.notes || null,
        userId || null
      )

      if (result.changes > 0) {
        return {
          success: true,
          message: 'تم إنشاء تحويل الشيك بنجاح',
          data: { transferId: result.lastInsertRowid }
        }
      } else {
        return { success: false, message: 'فشل في إنشاء تحويل الشيك' }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في إنشاء تحويل الشيك:', error)
      return { success: false, message: 'حدث خطأ في إنشاء تحويل الشيك' }
    }
  }

  // الحصول على الكيانات القابلة للتحويل
  public async getEntitiesForTransfer(): Promise<any> {
    try {
      // جلب العملاء
      const customers = this.db.prepare(`
        SELECT id, customer_code as code, customer_name as name, 'customer' as type
        FROM customers
        WHERE is_active = 1
        ORDER BY customer_name
      `).all()

      // جلب الموردين
      const suppliers = this.db.prepare(`
        SELECT id, supplier_code as code, supplier_name as name, 'supplier' as type
        FROM suppliers
        WHERE is_active = 1
        ORDER BY supplier_name
      `).all()

      const all = [...customers, ...suppliers]

      return {
        customers,
        suppliers,
        all
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب الكيانات القابلة للتحويل:', error)
      return {
        customers: [],
        suppliers: [],
        all: []
      }
    }
  }

  // الحصول على تاريخ الشيك
  public async getCheckHistory(checkId: number): Promise<any> {
    try {
      // جلب بيانات الشيك
      const check = this.db.prepare(`
        SELECT c.*, ba.account_name as bank_name
        FROM checks c
        LEFT JOIN bank_accounts ba ON c.bank_account_id = ba.id
        WHERE c.id = ?
      `).get(checkId)

      if (!check) {
        return { check: null, transfers: [] }
      }

      // جلب تحويلات الشيك
      const transfers = this.db.prepare(`
        SELECT ct.*, u.username as created_by_name
        FROM check_transfers ct
        LEFT JOIN users u ON ct.created_by = u.id
        WHERE ct.check_id = ?
        ORDER BY ct.created_at ASC
      `).all(checkId)

      return {
        check,
        transfers
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب تاريخ الشيك:', error)
      return { check: null, transfers: [] }
    }
  }

  // ===== إدارة صور الشيكات =====

  // دالة مساعدة لتحديد نوع MIME للصورة
  private getMimeType(filePath: string): string {
    const path = require('path')
    const extension = path.extname(filePath).toLowerCase()

    const mimeTypes: { [key: string]: string } = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.bmp': 'image/bmp',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml',
      '.tiff': 'image/tiff',
      '.tif': 'image/tiff',
      '.avif': 'image/avif'
    }

    return mimeTypes[extension] || 'image/jpeg'
  }

  public async getCheckImages(checkId: number): Promise<any[]> {
    try {
      const fs = require('fs')
      // const path = require('path') // غير مستخدم حالياً

      const images = this.db.prepare(`
        SELECT * FROM check_images
        WHERE check_id = ?
        ORDER BY image_side, created_at DESC
      `).all([checkId]) as any[]

      // تحويل الصور إلى base64 للعرض
      const processedImages = images.map(image => {
        try {
          if (image.image_path && fs.existsSync(image.image_path)) {
            const imageBuffer = fs.readFileSync(image.image_path)
            const mimeType = this.getMimeType(image.image_path)
            const base64Data = imageBuffer.toString('base64')
            const dataUrl = 'data:' + mimeType + ';base64,' + base64Data

            return {
              ...image,
              image_path: dataUrl,
              original_path: image.image_path
            }
          } else {
            Logger.warn('FinancialService', 'صورة الشيك غير موجودة: ' + image.image_path)
            return {
              ...image,
              image_path: null,
              original_path: image.image_path,
              error: 'الصورة غير موجودة'
            }
          }
        } catch (error) {
          Logger.error('FinancialService', 'خطأ في معالجة صورة الشيك ' + image.image_path + ':', error)
          return {
            ...image,
            image_path: null,
            original_path: image.image_path,
            error: 'خطأ في تحميل الصورة'
          }
        }
      })

      return processedImages
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب صور الشيك:', error)
      return []
    }
  }

  public async addCheckImage(imageData: any): Promise<number> {
    try {
      const fs = require('fs')
      const path = require('path')
      const { app } = require('electron')

      let checkId: number
      let fileName: string
      let fileBuffer: Buffer
      let fileSize: number
      let receiptId: number | null = null
      let imageSide: string = 'front'
      let scanQuality: string | null = null
      let notes: string | null = null

      // التعامل مع البيانات الجديدة (base64)
      if (imageData.fileData && imageData.checkId) {
        checkId = imageData.checkId
        receiptId = imageData.receiptId || null
        fileName = imageData.fileName
        fileSize = imageData.fileSize || 0
        imageSide = imageData.imageSide || 'front'
        scanQuality = imageData.scanQuality || null
        notes = imageData.notes || null

        // تحويل base64 إلى Buffer
        const base64Data = imageData.fileData.split(',')[1] // إزالة data:image/...;base64,
        fileBuffer = Buffer.from(base64Data, 'base64')
      } else {
        // البيانات القديمة (للتوافق مع النسخة القديمة)
        checkId = imageData.check_id
        receiptId = imageData.receipt_id || null
        fileName = imageData.image_name
        fileSize = imageData.image_size || 0
        imageSide = imageData.image_side || 'front'
        scanQuality = imageData.scan_quality || null
        notes = imageData.notes || null

        // إذا كانت البيانات القديمة تحتوي على مسار فقط، فلا نحفّ الملف
        if (!imageData.file_data) {
          // حفّ البيانات في قاعدة البيانات فقط
          const stmt = this.db.prepare(`
            INSERT INTO check_images (
              check_id, receipt_id, image_name, image_path, image_type,
              image_side, image_size, scan_quality, notes, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `)

          const result = stmt.run([
            checkId,
            receiptId,
            fileName,
            imageData.image_path,
            imageData.image_type || null,
            imageSide,
            fileSize,
            scanQuality,
            notes,
            imageData.created_by || null
          ])

          return result.lastInsertRowid as number
        }

        fileBuffer = imageData.file_data
      }

      // التحقق من وجود البيانات المطلوبة
      if (!checkId || !fileName || !fileBuffer) {
        throw new Error('بيانات صورة الشيك مفقودة')
      }

      // التحقق من صيغة الصورة
      const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
      const fileExtension = path.extname(fileName).toLowerCase()

      if (!allowedExtensions.includes(fileExtension)) {
        throw new Error('صيغة الصورة غير مدعومة: ' + fileExtension + '. الصيغ المدعومة: ' + allowedExtensions.join(', '))
      }

      // إنشاء مجلد الصور إذا لم يكن موجوداً
      const imagesDir = path.join(app.getPath('userData'), 'images', 'checks', checkId.toString())
      if (!fs.existsSync(imagesDir)) {
        fs.mkdirSync(imagesDir, { recursive: true })
      }

      // إنشاء مسار الصورة الجديد
      const timestamp = Date.now()
      const newFileName = timestamp + fileExtension
      const newFilePath = path.join(imagesDir, newFileName)

      // حفّ الملف
      fs.writeFileSync(newFilePath, fileBuffer)

      // حفّ البيانات في قاعدة البيانات
      const stmt = this.db.prepare(`
        INSERT INTO check_images (
          check_id, receipt_id, image_name, image_path, image_type,
          image_side, image_size, scan_quality, notes, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      const result = stmt.run([
        checkId,
        receiptId,
        fileName,
        newFilePath,
        imageData.fileType || imageData.image_type || null,
        imageSide,
        fileSize,
        scanQuality,
        notes,
        imageData.created_by || null
      ])

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      Logger.info('FinancialService', 'تم رفع صورة جديدة للشيك ' + checkId + ': ' + newFileName)
      return result.lastInsertRowid as number
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في إضافة صورة الشيك:', error)
      throw error
    }
  }

  public async deleteCheckImage(imageId: number): Promise<void> {
    try {
      this.db.prepare('DELETE FROM check_images WHERE id = ?').run([imageId])
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في حذف صورة الشيك:', error)
      throw error
    }
  }

  public async updateCheckImageNotes(imageId: number, notes: string): Promise<void> {
    try {
      this.db.prepare(`
        UPDATE check_images
        SET notes = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).run([notes, imageId])
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تحديث ملاحّات صورة الشيك:', error)
      throw error
    }
  }

  // ===== التقارير المالية =====

  public async getTrialBalance(_params?: any): Promise<any[]> {
    try {
      // ميزان المراجعة - جلب جميع الحسابات مع أرصدتها
      const accounts = this.db.prepare(`
        SELECT
          account_code,
          account_name,
          account_type,
          SUM(CASE WHEN debit_amount > 0 THEN debit_amount ELSE 0 END) as total_debit,
          SUM(CASE WHEN credit_amount > 0 THEN credit_amount ELSE 0 END) as total_credit,
          (SUM(CASE WHEN debit_amount > 0 THEN debit_amount ELSE 0 END) -
           SUM(CASE WHEN credit_amount > 0 THEN credit_amount ELSE 0 END)) as balance
        FROM chart_of_accounts coa
        LEFT JOIN journal_entries je ON coa.id = je.account_id
        WHERE coa.is_active = 1
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        ORDER BY coa.account_code
      `).all()

      return accounts
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب ميزان المراجعة:', error)
      return []
    }
  }

  public async getBalanceSheet(_params?: any): Promise<any> {
    try {
      // الميزانية العمومية
      const assets = this.db.prepare(`
        SELECT account_name, SUM(debit_amount - credit_amount) as balance
        FROM chart_of_accounts coa
        LEFT JOIN journal_entries je ON coa.id = je.account_id
        WHERE coa.account_type IN ('asset', 'current_asset', 'fixed_asset')
        AND coa.is_active = 1
        GROUP BY coa.id, coa.account_name
        HAVING balance != 0
        ORDER BY coa.account_code
      `).all()

      const liabilities = this.db.prepare(`
        SELECT account_name, SUM(credit_amount - debit_amount) as balance
        FROM chart_of_accounts coa
        LEFT JOIN journal_entries je ON coa.id = je.account_id
        WHERE coa.account_type IN ('liability', 'current_liability', 'long_term_liability')
        AND coa.is_active = 1
        GROUP BY coa.id, coa.account_name
        HAVING balance != 0
        ORDER BY coa.account_code
      `).all()

      const equity = this.db.prepare(`
        SELECT account_name, SUM(credit_amount - debit_amount) as balance
        FROM chart_of_accounts coa
        LEFT JOIN journal_entries je ON coa.id = je.account_id
        WHERE coa.account_type IN ('equity', 'capital', 'retained_earnings')
        AND coa.is_active = 1
        GROUP BY coa.id, coa.account_name
        HAVING balance != 0
        ORDER BY coa.account_code
      `).all()

      return {
        assets,
        liabilities,
        equity,
        totalAssets: assets.reduce((sum: number, item: any) => sum + (item.balance || 0), 0),
        totalLiabilities: liabilities.reduce((sum: number, item: any) => sum + (item.balance || 0), 0),
        totalEquity: equity.reduce((sum: number, item: any) => sum + (item.balance || 0), 0)
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب الميزانية العمومية:', error)
      return { assets: [], liabilities: [], equity: [], totalAssets: 0, totalLiabilities: 0, totalEquity: 0 }
    }
  }

  public async getIncomeStatement(_params?: any): Promise<any> {
    try {
      // قائمة الدخل
      const revenues = this.db.prepare(`
        SELECT account_name, SUM(credit_amount - debit_amount) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entries je ON coa.id = je.account_id
        WHERE coa.account_type IN ('revenue', 'income', 'sales')
        AND coa.is_active = 1
        GROUP BY coa.id, coa.account_name
        HAVING amount != 0
        ORDER BY coa.account_code
      `).all()

      const expenses = this.db.prepare(`
        SELECT account_name, SUM(debit_amount - credit_amount) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entries je ON coa.id = je.account_id
        WHERE coa.account_type IN ('expense', 'cost', 'operating_expense')
        AND coa.is_active = 1
        GROUP BY coa.id, coa.account_name
        HAVING amount != 0
        ORDER BY coa.account_code
      `).all()

      const totalRevenue = revenues.reduce((sum: number, item: any) => sum + (item.amount || 0), 0)
      const totalExpenses = expenses.reduce((sum: number, item: any) => sum + (item.amount || 0), 0)
      const netIncome = totalRevenue - totalExpenses

      return {
        revenues,
        expenses,
        totalRevenue,
        totalExpenses,
        netIncome
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب قائمة الدخل:', error)
      return { revenues: [], expenses: [], totalRevenue: 0, totalExpenses: 0, netIncome: 0 }
    }
  }

  public async getCashFlowStatement(_params?: any): Promise<any> {
    try {
      // قائمة التدفق النقدي
      const operatingActivities = this.db.prepare(`
        SELECT
          'Operating Activities' as category,
          SUM(CASE WHEN v.voucher_type = 'receipt' THEN v.amount ELSE 0 END) as cash_inflow,
          SUM(CASE WHEN v.voucher_type = 'payment' THEN v.amount ELSE 0 END) as cash_outflow
        FROM vouchers v
        WHERE v.created_at >= date('now', '-1 month')
      `).get() as any

      const investingActivities = this.db.prepare(`
        SELECT
          'Investing Activities' as category,
          SUM(CASE WHEN bt.transaction_type = 'deposit' THEN bt.amount ELSE 0 END) as cash_inflow,
          SUM(CASE WHEN bt.transaction_type = 'withdrawal' THEN bt.amount ELSE 0 END) as cash_outflow
        FROM bank_transactions bt
        WHERE bt.created_at >= date('now', '-1 month')
        AND bt.description LIKE '%استثمار%'
      `).get() as any

      const financingActivities = this.db.prepare(`
        SELECT
          'Financing Activities' as category,
          0 as cash_inflow,
          SUM(amount) as cash_outflow
        FROM checks
        WHERE created_at >= date('now', '-1 month')
        AND status = 'cashed'
      `).get() as any

      const netCashFlow = (operatingActivities?.cash_inflow || 0) - (operatingActivities?.cash_outflow || 0) +
                         (investingActivities?.cash_inflow || 0) - (investingActivities?.cash_outflow || 0) +
                         (financingActivities?.cash_inflow || 0) - (financingActivities?.cash_outflow || 0)

      return {
        operatingActivities: operatingActivities || { category: 'Operating Activities', cash_inflow: 0, cash_outflow: 0 },
        investingActivities: investingActivities || { category: 'Investing Activities', cash_inflow: 0, cash_outflow: 0 },
        financingActivities: financingActivities || { category: 'Financing Activities', cash_inflow: 0, cash_outflow: 0 },
        netCashFlow
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب قائمة التدفق النقدي:', error)
      return {
        operatingActivities: { category: 'Operating Activities', cash_inflow: 0, cash_outflow: 0 },
        investingActivities: { category: 'Investing Activities', cash_inflow: 0, cash_outflow: 0 },
        financingActivities: { category: 'Financing Activities', cash_inflow: 0, cash_outflow: 0 },
        netCashFlow: 0
      }
    }
  }

  // ===== التقارير المالية المتقدمة =====

  // الملخص المالي
  public async getFinancialSummary(_params?: any): Promise<any> {
    try {
      const totalAssets = this.db.prepare(`
        SELECT COALESCE(SUM(balance), 0) as total FROM bank_accounts WHERE is_active = 1
      `).get() as any

      const totalChecks = this.db.prepare(`
        SELECT COALESCE(SUM(amount), 0) as total FROM checks WHERE status IN ('issued', 'received', 'pending')
      `).get() as any

      const totalPromissoryNotes = this.db.prepare(`
        SELECT COALESCE(SUM(amount), 0) as total FROM promissory_notes WHERE status IN ('issued', 'received', 'pending')
      `).get() as any

      // حساب الإيرادات من جميع المصادر
      const monthlyRevenue = this.db.prepare(`
        SELECT COALESCE(SUM(total), 0) as total FROM (
          SELECT SUM(amount) as total FROM vouchers
          WHERE voucher_type = 'receipt' AND DATE(voucher_date) >= DATE('now', '-30 days')
          UNION ALL
          SELECT SUM(final_amount) as total FROM sales_invoices
          WHERE status = 'paid' AND DATE(invoice_date) >= DATE('now', '-30 days')
          UNION ALL
          SELECT SUM(final_amount) as total FROM paint_invoices
          WHERE status = 'paid' AND DATE(invoice_date) >= DATE('now', '-30 days')
        )
      `).get() as any

      // حساب المصروفات من جميع المصادر
      const monthlyExpenses = this.db.prepare(`
        SELECT COALESCE(SUM(total), 0) as total FROM (
          SELECT SUM(amount) as total FROM vouchers
          WHERE voucher_type = 'payment' AND DATE(voucher_date) >= DATE('now', '-30 days')
          UNION ALL
          SELECT SUM(final_amount) as total FROM purchase_invoices
          WHERE status = 'paid' AND DATE(invoice_date) >= DATE('now', '-30 days')
        )
      `).get() as any

      // حساب المبيعات والمشتريات المعلقة
      const salesReceivables = this.db.prepare(`
        SELECT COALESCE(SUM(remaining_amount), 0) as total FROM sales_invoices
        WHERE remaining_amount > 0 AND status != 'cancelled'
      `).get() as any

      const paintReceivables = this.db.prepare(`
        SELECT COALESCE(SUM(remaining_amount), 0) as total FROM paint_invoices
        WHERE remaining_amount > 0 AND status != 'cancelled'
      `).get() as any

      const purchasePayables = this.db.prepare(`
        SELECT COALESCE(SUM(remaining_amount), 0) as total FROM purchase_invoices
        WHERE remaining_amount > 0 AND status != 'cancelled'
      `).get() as any

      // حساب قيمة المخزون
      const inventoryValue = this.db.prepare(`
        SELECT COALESCE(SUM(inv.quantity * COALESCE(inv.cost_price, i.cost_price, 0)), 0) as total
        FROM inventory inv
        JOIN items i ON inv.item_id = i.id
        WHERE i.is_active = 1 AND inv.quantity > 0
      `).get() as any

      const totalReceivables = salesReceivables.total + paintReceivables.total

      return {
        totalAssets: totalAssets.total + totalChecks.total + totalPromissoryNotes.total + inventoryValue.total + totalReceivables,
        totalBankBalance: totalAssets.total,
        totalChecksValue: totalChecks.total,
        totalPromissoryNotesValue: totalPromissoryNotes.total,
        totalInventoryValue: inventoryValue.total,
        totalReceivables: totalReceivables,
        salesReceivables: salesReceivables.total,
        paintReceivables: paintReceivables.total,
        totalPayables: purchasePayables.total,
        monthlyRevenue: monthlyRevenue.total,
        monthlyExpenses: monthlyExpenses.total,
        monthlyProfit: monthlyRevenue.total - monthlyExpenses.total,
        netWorth: (totalAssets.total + inventoryValue.total + totalReceivables) - purchasePayables.total,
        generatedAt: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب الملخص المالي:', error)
      return {
        totalAssets: 0,
        totalBankBalance: 0,
        totalChecksValue: 0,
        totalPromissoryNotesValue: 0,
        monthlyRevenue: 0,
        monthlyExpenses: 0,
        monthlyProfit: 0,
        generatedAt: new Date().toISOString()
      }
    }
  }

  // تقرير الأعمار (للشيكات والسندات الإذنية)
  public async getAgingReport(_params?: any): Promise<any> {
    try {
      const checksAging = this.db.prepare(`
        SELECT
          id,
          check_number,
          payee_name,
          amount,
          issue_date,
          JULIANDAY('now') - JULIANDAY(issue_date) as days_old,
          status
        FROM checks
        WHERE status IN ('issued', 'pending')
        ORDER BY issue_date ASC
      `).all()

      const promissoryNotesAging = this.db.prepare(`
        SELECT
          id,
          note_number,
          payee_name,
          amount,
          due_date,
          JULIANDAY('now') - JULIANDAY(due_date) as days_overdue,
          status
        FROM promissory_notes
        WHERE status = 'issued'
        ORDER BY due_date ASC
      `).all()

      // تصنيف حسب العمر
      const categorizeByAge = (items: any[], dateField: string) => {
        return {
          current: items.filter(item => item[dateField] <= 30),
          thirtyDays: items.filter(item => item[dateField] > 30 && item[dateField] <= 60),
          sixtyDays: items.filter(item => item[dateField] > 60 && item[dateField] <= 90),
          ninetyDaysPlus: items.filter(item => item[dateField] > 90)
        }
      }

      return {
        checks: categorizeByAge(checksAging, 'days_old'),
        promissoryNotes: categorizeByAge(promissoryNotesAging, 'days_overdue'),
        generatedAt: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب تقرير الأعمار:', error)
      return {
        checks: { current: [], thirtyDays: [], sixtyDays: [], ninetyDaysPlus: [] },
        promissoryNotes: { current: [], thirtyDays: [], sixtyDays: [], ninetyDaysPlus: [] },
        generatedAt: new Date().toISOString()
      }
    }
  }

  // تقرير أعمار ديون العملاء
  public async getCustomerAgingReport(params?: { customerId?: number; asOfDate?: string }): Promise<any[]> {
    try {
      const asOfDate = params?.asOfDate || new Date().toISOString().split('T')[0]

      let whereClause = ''
      const queryParams: any[] = [asOfDate, asOfDate, asOfDate, asOfDate, asOfDate]

      if (params?.customerId) {
        whereClause = 'WHERE c.id = ?'
        queryParams.push(params.customerId)
      }

      const agingData = this.db.prepare(`
        SELECT
          c.id as customer_id,
          c.name as customer_name,
          c.code as customer_code,
          c.credit_limit,
          COALESCE(SUM(
            CASE
              WHEN si.status = 'unpaid' OR (si.total_amount - COALESCE(payments.total_paid, 0)) > 0
              THEN si.total_amount - COALESCE(payments.total_paid, 0)
              ELSE 0
            END
          ), 0) as total_outstanding,

          COALESCE(SUM(
            CASE
              WHEN (si.status = 'unpaid' OR (si.total_amount - COALESCE(payments.total_paid, 0)) > 0)
                AND JULIANDAY(?) - JULIANDAY(si.invoice_date) <= 30
              THEN si.total_amount - COALESCE(payments.total_paid, 0)
              ELSE 0
            END
          ), 0) as current_0_30,

          COALESCE(SUM(
            CASE
              WHEN (si.status = 'unpaid' OR (si.total_amount - COALESCE(payments.total_paid, 0)) > 0)
                AND JULIANDAY(?) - JULIANDAY(si.invoice_date) BETWEEN 31 AND 60
              THEN si.total_amount - COALESCE(payments.total_paid, 0)
              ELSE 0
            END
          ), 0) as days_31_60,

          COALESCE(SUM(
            CASE
              WHEN (si.status = 'unpaid' OR (si.total_amount - COALESCE(payments.total_paid, 0)) > 0)
                AND JULIANDAY(?) - JULIANDAY(si.invoice_date) BETWEEN 61 AND 90
              THEN si.total_amount - COALESCE(payments.total_paid, 0)
              ELSE 0
            END
          ), 0) as days_61_90,

          COALESCE(SUM(
            CASE
              WHEN (si.status = 'unpaid' OR (si.total_amount - COALESCE(payments.total_paid, 0)) > 0)
                AND JULIANDAY(?) - JULIANDAY(si.invoice_date) BETWEEN 91 AND 120
              THEN si.total_amount - COALESCE(payments.total_paid, 0)
              ELSE 0
            END
          ), 0) as days_91_120,

          COALESCE(SUM(
            CASE
              WHEN (si.status = 'unpaid' OR (si.total_amount - COALESCE(payments.total_paid, 0)) > 0)
                AND JULIANDAY(?) - JULIANDAY(si.invoice_date) > 120
              THEN si.total_amount - COALESCE(payments.total_paid, 0)
              ELSE 0
            END
          ), 0) as over_120,

          MIN(
            CASE
              WHEN si.status = 'unpaid' OR (si.total_amount - COALESCE(payments.total_paid, 0)) > 0
              THEN si.invoice_date
              ELSE NULL
            END
          ) as oldest_invoice_date

        FROM customers c
        LEFT JOIN sales_invoices si ON c.id = si.customer_id
        LEFT JOIN (
          SELECT
            ip.invoice_id,
            SUM(ip.amount) as total_paid
          FROM invoice_payments ip
          WHERE ip.invoice_type = 'sales_invoice'
          GROUP BY ip.invoice_id
        ) payments ON si.id = payments.invoice_id
        ${whereClause}
        GROUP BY c.id, c.name, c.code, c.credit_limit
        HAVING total_outstanding > 0
        ORDER BY total_outstanding DESC
      `).all(...queryParams)

      return agingData
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب تقرير أعمار ديون العملاء:', error)
      return []
    }
  }

  // تقرير الوضع النقدي
  public async getCashPositionReport(_params?: any): Promise<any> {
    try {
      const bankBalances = this.db.prepare(`
        SELECT
          account_name,
          balance,
          account_number,
          bank_name
        FROM bank_accounts
        WHERE is_active = 1
        ORDER BY balance DESC
      `).all()

      const pendingChecks = this.db.prepare(`
        SELECT COALESCE(SUM(amount), 0) as total
        FROM checks
        WHERE status IN ('issued', 'pending')
      `).get() as any

      const receivablePromissoryNotes = this.db.prepare(`
        SELECT COALESCE(SUM(amount), 0) as total
        FROM promissory_notes
        WHERE status = 'issued' AND due_date <= DATE('now', '+30 days')
      `).get() as any

      const totalBankBalance = bankBalances.reduce((sum: number, account: any) => sum + account.balance, 0)

      return {
        bankAccounts: bankBalances,
        totalBankBalance,
        pendingChecksValue: pendingChecks.total,
        receivablePromissoryNotesValue: receivablePromissoryNotes.total,
        netCashPosition: totalBankBalance - pendingChecks.total + receivablePromissoryNotes.total,
        generatedAt: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب تقرير الوضع النقدي:', error)
      return {
        bankAccounts: [],
        totalBankBalance: 0,
        pendingChecksValue: 0,
        receivablePromissoryNotesValue: 0,
        netCashPosition: 0,
        generatedAt: new Date().toISOString()
      }
    }
  }

  // تحليل الربحية
  public async getProfitabilityAnalysis(_params?: any): Promise<any> {
    try {
      const monthlyData = this.db.prepare(`
        SELECT
          strftime('%Y-%m', voucher_date) as month,
          SUM(CASE WHEN voucher_type = 'receipt' THEN amount ELSE 0 END) as revenue,
          SUM(CASE WHEN voucher_type = 'payment' THEN amount ELSE 0 END) as expenses
        FROM vouchers
        WHERE voucher_date >= DATE('now', '-12 months')
        GROUP BY strftime('%Y-%m', voucher_date)
        ORDER BY month DESC
      `).all()

      const totalRevenue = monthlyData.reduce((sum: number, item: any) => sum + item.revenue, 0)
      const totalExpenses = monthlyData.reduce((sum: number, item: any) => sum + item.expenses, 0)
      const netProfit = totalRevenue - totalExpenses
      const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0

      return {
        monthlyData: monthlyData.map((item: any) => ({
          ...item,
          profit: item.revenue - item.expenses,
          profitMargin: item.revenue > 0 ? ((item.revenue - item.expenses) / item.revenue) * 100 : 0
        })),
        totalRevenue,
        totalExpenses,
        netProfit,
        profitMargin,
        generatedAt: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب تحليل الربحية:', error)
      return {
        monthlyData: [],
        totalRevenue: 0,
        totalExpenses: 0,
        netProfit: 0,
        profitMargin: 0,
        generatedAt: new Date().toISOString()
      }
    }
  }

  // تقرير انحراف الميزانية (مبسط)
  public async getBudgetVarianceReport(_params?: any): Promise<any> {
    try {
      // هذا تقرير مبسط - يمكن تطويره أكثر مع إضافة جداول الميزانية
      const actualRevenue = this.db.prepare(`
        SELECT COALESCE(SUM(amount), 0) as total
        FROM vouchers
        WHERE voucher_type = 'receipt'
        AND DATE(voucher_date) >= DATE('now', '-30 days')
      `).get() as any

      const actualExpenses = this.db.prepare(`
        SELECT COALESCE(SUM(amount), 0) as total
        FROM vouchers
        WHERE voucher_type = 'payment'
        AND DATE(voucher_date) >= DATE('now', '-30 days')
      `).get() as any

      // ميزانية افتراضية (يمكن تخزينها في جدول منفصل)
      const budgetedRevenue = 100000 // مثال
      const budgetedExpenses = 80000 // مثال

      return {
        revenue: {
          budgeted: budgetedRevenue,
          actual: actualRevenue.total,
          variance: actualRevenue.total - budgetedRevenue,
          variancePercent: budgetedRevenue > 0 ? ((actualRevenue.total - budgetedRevenue) / budgetedRevenue) * 100 : 0
        },
        expenses: {
          budgeted: budgetedExpenses,
          actual: actualExpenses.total,
          variance: actualExpenses.total - budgetedExpenses,
          variancePercent: budgetedExpenses > 0 ? ((actualExpenses.total - budgetedExpenses) / budgetedExpenses) * 100 : 0
        },
        generatedAt: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب تقرير انحراف الميزانية:', error)
      return {
        revenue: { budgeted: 0, actual: 0, variance: 0, variancePercent: 0 },
        expenses: { budgeted: 0, actual: 0, variance: 0, variancePercent: 0 },
        generatedAt: new Date().toISOString()
      }
    }
  }

  // تصدير التقارير المالية المحسن
  public async exportFinancialReport(reportType: string, params?: any): Promise<any> {
    try {
      let reportData: any = {}
      let reportTitle = ''
      let reportSubtitle = ''

      switch (reportType) {
        case 'trial-balance':
          reportData = await this.getTrialBalance(params)
          reportTitle = 'ميزان المراجعة'
          reportSubtitle = 'Trial Balance'
          break
        case 'balance-sheet':
          reportData = await this.getBalanceSheet(params)
          reportTitle = 'الميزانية العمومية'
          reportSubtitle = 'Balance Sheet'
          break
        case 'income-statement':
          reportData = await this.getIncomeStatement(params)
          reportTitle = 'قائمة الدخل'
          reportSubtitle = 'Income Statement'
          break
        case 'cash-flow':
          reportData = await this.getCashFlowStatement(params)
          reportTitle = 'قائمة التدفق النقدي'
          reportSubtitle = 'Cash Flow Statement'
          break
        case 'financial-summary':
          reportData = await this.getFinancialSummary(params)
          reportTitle = 'الملخص المالي'
          reportSubtitle = 'Financial Summary'
          break
        case 'aging-report':
          reportData = await this.getAgingReport(params)
          reportTitle = 'تقرير الأعمار'
          reportSubtitle = 'Aging Report'
          break
        case 'cash-position':
          reportData = await this.getCashPositionReport(params)
          reportTitle = 'تقرير الوضع النقدي'
          reportSubtitle = 'Cash Position Report'
          break
        case 'profitability-analysis':
          reportData = await this.getProfitabilityAnalysis(params)
          reportTitle = 'تحليل الربحية'
          reportSubtitle = 'Profitability Analysis'
          break
        default:
          throw new Error('نوع التقرير غير مدعوم: ' + reportType)
      }

      // إعداد بيانات التقرير المحسنة
      const enhancedReportData = {
        reportType,
        reportTitle,
        reportSubtitle,
        data: reportData,
        metadata: {
          generatedAt: new Date().toISOString(),
          generatedBy: params?.userId || 'النّام',
          dateRange: params?.dateRange || null,
          filters: params?.filters || {},
          companyInfo: {
            name: 'شركة المحاسبة والإنتاج',
            nameEn: 'Accounting & Production Company',
            address: 'العنوان الرئيسي للشركة',
            phone: '+970-XXX-XXXX',
            email: '<EMAIL>',
            website: 'www.company.com'
          }
        },
        format: params?.format || 'json'
      }

      return enhancedReportData
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تصدير التقرير:', error)
      throw error
    }
  }

  // طباعة التقارير المالية المحسنة
  public async printFinancialReport(reportType: string, params?: any): Promise<any> {
    try {
      const { PrintService } = await import('./PrintService')
      const { ReportTemplateGenerator } = await import('./ReportTemplateGenerator')

      const printService = PrintService.getInstance()
      const templateGenerator = ReportTemplateGenerator.getInstance()

      // الحصول على بيانات التقرير
      const reportData = await this.exportFinancialReport(reportType, params)

      // إنشاء محتوى HTML للتقرير
      let reportContent = ''

      switch (reportType) {
        case 'trial-balance':
          reportContent = templateGenerator.generateTrialBalanceTemplate(reportData.data)
          break
        case 'balance-sheet':
          reportContent = templateGenerator.generateBalanceSheetTemplate(reportData.data)
          break
        case 'income-statement':
          reportContent = templateGenerator.generateIncomeStatementTemplate(reportData.data)
          break
        case 'aging-report':
          reportContent = templateGenerator.generateAgingReportTemplate(reportData.data)
          break
        case 'sales-invoice':
          reportContent = templateGenerator.generateSalesInvoiceTemplate(reportData.data.invoice, reportData.data.items)
          break
        case 'purchase-invoice':
          reportContent = templateGenerator.generatePurchaseInvoiceTemplate(reportData.data.invoice, reportData.data.items)
          break
        case 'production-order':
          reportContent = templateGenerator.generateProductionOrderTemplate(reportData.data.order, reportData.data.items)
          break
        case 'paint-order':
          reportContent = templateGenerator.generatePaintOrderTemplate(reportData.data.order, reportData.data.items)
          break
        case 'sales-order':
          reportContent = templateGenerator.generateSalesOrderTemplate(reportData.data.order, reportData.data.items)
          break
        case 'purchase-order':
          reportContent = templateGenerator.generatePurchaseOrderTemplate(reportData.data.order, reportData.data.items)
          break
        default:
          // قالب عام للتقارير الأخرى
          reportContent = this.generateGenericReportContent(reportData.data)
      }

      // طباعة التقرير
      const printResult = await printService.printReport(
        reportData.reportTitle,
        reportContent,
        {
          format: params?.format || 'A4',
          orientation: params?.orientation || 'portrait',
          includeHeader: true,
          includeFooter: true,
          includeLogo: true
        }
      )

      return {
        ...reportData,
        printResult,
        printedAt: new Date().toISOString(),
        status: printResult.success ? 'printed' : 'print-failed'
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في طباعة التقرير:', error)
      throw error
    }
  }

  // إنشاء محتوى عام للتقارير
  private generateGenericReportContent(data: any): string {
    if (!data || (Array.isArray(data) && data.length === 0)) {
      return '<p style="text-align: center; color: #999; padding: 40px;">لا توجد بيانات لعرضها</p>'
    }

    if (Array.isArray(data)) {
      // إنشاء جدول للبيانات
      const headers = Object.keys(data[0] || {})
      const headerRow = headers.map(header => '<th>' + this.translateHeader(header) + '</th>').join('')

      const dataRows = data.map(row => {
        const cells = headers.map(header => {
          const value = row[header]
          return '<td>' + this.formatValue(value) + '</td>'
        }).join('')
        return '<tr>' + cells + '</tr>'
      }).join('')

      return `
        <table class="report-table">
          <thead>
            <tr>${headerRow}</tr>
          </thead>
          <tbody>
            ${dataRows}
          </tbody>
        </table>
      `
    } else {
      // عرض البيانات كإحصائيات
      const stats = Object.entries(data).map(([key, value]) => `
        <div class="stat-card">
          <div class="stat-value">${this.formatValue(value)}</div>
          <div class="stat-label">${this.translateHeader(key)}</div>
        </div>
      `).join('')

      return '<div class="stats-grid">' + stats + '</div>'
    }
  }

  // ترجمة العناوين
  private translateHeader(header: string): string {
    const translations: { [key: string]: string } = {
      'id': 'المعرف',
      'name': 'الاسم',
      'amount': 'المبلغ',
      'date': 'التاريخ',
      'status': 'الحالة',
      'description': 'الوصف',
      'account_name': 'اسم الحساب',
      'account_number': 'رقم الحساب',
      'balance': 'الرصيد',
      'check_number': 'رقم الشيك',
      'payee_name': 'اسم المستفيد',
      'drawer_name': 'اسم الساحب',
      'note_number': 'رقم السند',
      'voucher_number': 'رقم القسيمة',
      'created_at': 'تاريخ الإنشاء',
      'updated_at': 'تاريخ التحديث',
      'totalAssets': 'إجمالي الأصول',
      'totalBankBalance': 'إجمالي الأرصدة المصرفية',
      'totalChecksValue': 'إجمالي قيمة الشيكات',
      'totalPromissoryNotesValue': 'إجمالي قيمة السندات الإذنية',
      'monthlyRevenue': 'الإيرادات الشهرية',
      'monthlyExpenses': 'المصروفات الشهرية',
      'monthlyProfit': 'الربح الشهري'
    }

    return translations[header] || header
  }

  // تنسيق القيم
  private formatValue(value: any): string {
    if (value === null || value === undefined) {
      return '-'
    }

    if (typeof value === 'number') {
      return value.toLocaleString('ar-EG') + ' ₪'
    }

    if (value instanceof Date) {
      return value.toLocaleDateString('ar-EG')
    }

    if (typeof value === 'string' && value.includes('T')) {
      // محاولة تحويل التاريخ
      const date = new Date(value)
      if (!isNaN(date.getTime())) {
        return date.toLocaleDateString('ar-EG')
      }
    }

    return String(value)
  }

  // حفّ التقرير كـ PDF
  public async saveReportAsPDF(reportType: string, filePath: string, params?: any): Promise<any> {
    try {
      const { PrintService } = await import('./PrintService')
      const { ReportTemplateGenerator } = await import('./ReportTemplateGenerator')

      const printService = PrintService.getInstance()
      const templateGenerator = ReportTemplateGenerator.getInstance()

      // الحصول على بيانات التقرير
      const reportData = await this.exportFinancialReport(reportType, params)

      // إنشاء محتوى HTML للتقرير
      let reportContent = ''

      switch (reportType) {
        case 'trial-balance':
          reportContent = templateGenerator.generateTrialBalanceTemplate(reportData.data)
          break
        case 'balance-sheet':
          reportContent = templateGenerator.generateBalanceSheetTemplate(reportData.data)
          break
        case 'income-statement':
          reportContent = templateGenerator.generateIncomeStatementTemplate(reportData.data)
          break
        case 'aging-report':
          reportContent = templateGenerator.generateAgingReportTemplate(reportData.data)
          break
        case 'sales-invoice':
          reportContent = templateGenerator.generateSalesInvoiceTemplate(reportData.data.invoice, reportData.data.items)
          break
        case 'purchase-invoice':
          reportContent = templateGenerator.generatePurchaseInvoiceTemplate(reportData.data.invoice, reportData.data.items)
          break
        case 'production-order':
          reportContent = templateGenerator.generateProductionOrderTemplate(reportData.data.order, reportData.data.items)
          break
        case 'paint-order':
          reportContent = templateGenerator.generatePaintOrderTemplate(reportData.data.order, reportData.data.items)
          break
        case 'sales-order':
          reportContent = templateGenerator.generateSalesOrderTemplate(reportData.data.order, reportData.data.items)
          break
        case 'purchase-order':
          reportContent = templateGenerator.generatePurchaseOrderTemplate(reportData.data.order, reportData.data.items)
          break
        default:
          reportContent = this.generateGenericReportContent(reportData.data)
      }

      // حفّ التقرير كـ PDF
      const saveResult = await printService.saveReportAsPDF(
        reportData.reportTitle,
        reportContent,
        filePath,
        {
          format: params?.format || 'A4',
          orientation: params?.orientation || 'portrait',
          includeHeader: true,
          includeFooter: true,
          includeLogo: true
        }
      )

      return {
        ...reportData,
        saveResult,
        savedAt: new Date().toISOString(),
        filePath: saveResult.filePath
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في حفّ التقرير كـ PDF:', error)
      throw error
    }
  }

  // معاينة التقرير
  public async previewReport(reportType: string, params?: any): Promise<any> {
    try {
      const { PrintService } = await import('./PrintService')
      const { ReportTemplateGenerator } = await import('./ReportTemplateGenerator')

      const printService = PrintService.getInstance()
      const templateGenerator = ReportTemplateGenerator.getInstance()

      // الحصول على بيانات التقرير
      const reportData = await this.exportFinancialReport(reportType, params)

      // إنشاء محتوى HTML للتقرير
      let reportContent = ''

      switch (reportType) {
        case 'trial-balance':
          reportContent = templateGenerator.generateTrialBalanceTemplate(reportData.data)
          break
        case 'balance-sheet':
          reportContent = templateGenerator.generateBalanceSheetTemplate(reportData.data)
          break
        case 'income-statement':
          reportContent = templateGenerator.generateIncomeStatementTemplate(reportData.data)
          break
        case 'aging-report':
          reportContent = templateGenerator.generateAgingReportTemplate(reportData.data)
          break
        case 'sales-invoice':
          reportContent = templateGenerator.generateSalesInvoiceTemplate(reportData.data.invoice, reportData.data.items)
          break
        case 'purchase-invoice':
          reportContent = templateGenerator.generatePurchaseInvoiceTemplate(reportData.data.invoice, reportData.data.items)
          break
        case 'production-order':
          reportContent = templateGenerator.generateProductionOrderTemplate(reportData.data.order, reportData.data.items)
          break
        case 'paint-order':
          reportContent = templateGenerator.generatePaintOrderTemplate(reportData.data.order, reportData.data.items)
          break
        case 'sales-order':
          reportContent = templateGenerator.generateSalesOrderTemplate(reportData.data.order, reportData.data.items)
          break
        case 'purchase-order':
          reportContent = templateGenerator.generatePurchaseOrderTemplate(reportData.data.order, reportData.data.items)
          break
        default:
          reportContent = this.generateGenericReportContent(reportData.data)
      }

      // فتح نافذة المعاينة
      const previewWindow = await printService.previewReport(
        reportData.reportTitle,
        reportContent,
        {
          format: params?.format || 'A4',
          orientation: params?.orientation || 'portrait',
          includeHeader: true,
          includeFooter: true,
          includeLogo: true
        }
      )

      return {
        success: true,
        message: 'تم فتح معاينة التقرير',
        windowId: previewWindow.id
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في معاينة التقرير:', error)
      throw error
    }
  }

  // توليد لوحة المعلومات المالية
  public async generateFinancialDashboard(params?: any): Promise<any> {
    try {
      const [
        financialSummary,
        cashPosition,
        agingReport,
        profitabilityAnalysis
      ] = await Promise.all([
        this.getFinancialSummary(params),
        this.getCashPositionReport(params),
        this.getAgingReport(params),
        this.getProfitabilityAnalysis(params)
      ])

      return {
        summary: financialSummary,
        cashPosition,
        aging: agingReport,
        profitability: profitabilityAnalysis,
        generatedAt: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في توليد لوحة المعلومات المالية:', error)
      return {
        summary: {},
        cashPosition: {},
        aging: {},
        profitability: {},
        generatedAt: new Date().toISOString()
      }
    }
  }

  // تصدير الشيكات
  public async exportChecks(_format: 'excel' | 'csv'): Promise<ApiResponse> {
    try {
      const checks = this.db.prepare(`
        SELECT
          c.check_number,
          c.bank_name,
          c.amount,
          c.payee_name,
          c.check_date,
          c.due_date,
          c.status,
          c.notes,
          c.created_at,
          ba.account_name as bank_account_name
        FROM checks c
        LEFT JOIN bank_accounts ba ON c.bank_account_id = ba.id
        ORDER BY c.created_at DESC
      `).all()

      return { success: true, data: checks }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تصدير الشيكات:', error)
      return { success: false, message: 'حدث خطأ في تصدير الشيكات' }
    }
  }

  // استيراد الشيكات
  public async importChecks(checksData: any[]): Promise<ApiResponse> {
    try {
      let successCount = 0
      let errorCount = 0
      const errors: string[] = []

      for (const checkData of checksData) {
        try {
          // التحقق من البيانات الأساسية
          if (!checkData.check_number || !checkData.amount || !checkData.payee_name) {
            errors.push('الشيك "' + (checkData.check_number || 'غير محدد') + '" - رقم الشيك والمبلغ واسم المستفيد مطلوبة')
            errorCount++
            continue
          }

          // التحقق من عدم تكرار رقم الشيك
          const existingCheck = this.db.prepare('SELECT id FROM checks WHERE check_number = ?').get([checkData.check_number])
          if (existingCheck) {
            errors.push('الشيك رقم "' + checkData.check_number + '" موجود مسبقاً')
            errorCount++
            continue
          }

          // البحث عن الحساب البنكي إذا تم تحديده
          let bankAccountId = null
          if (checkData.bank_account_name) {
            const bankAccount = this.db.prepare('SELECT id FROM bank_accounts WHERE account_name = ? AND is_active = 1').get([checkData.bank_account_name])
            if (bankAccount) {
              bankAccountId = (bankAccount as any).id
            }
          }

          // إنشاء الشيك
          const result = this.db.prepare(`
            INSERT INTO checks (
              check_number, bank_account_id, bank_name, amount,
              payee_name, check_date, due_date, status, notes, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
          `).run([
            checkData.check_number,
            bankAccountId,
            checkData.bank_name || '',
            checkData.amount,
            checkData.payee_name,
            checkData.check_date || new Date().toISOString().split('T')[0],
            checkData.due_date || new Date().toISOString().split('T')[0],
            checkData.status || 'issued',
            checkData.notes || ''
          ])

          if (result.changes > 0) {
            successCount++
          } else {
            errors.push('فشل في إنشاء الشيك "' + checkData.check_number + '"')
            errorCount++
          }
        } catch (error: any) {
          errors.push('خطأ في الشيك "' + checkData.check_number + '": ' + error.message)
          errorCount++
        }
      }

      // حفّ قاعدة البيانات
      if (successCount > 0) {
        DatabaseService.getInstance().saveDatabase()
      }

      return {
        success: successCount > 0,
        message: 'تم استيراد ' + successCount + ' شيك بنجاح' + (errorCount > 0 ? ' مع ' + errorCount + ' خطأ' : ''),
        data: {
          successCount,
          errorCount,
          errorsList: errors.slice(0, 10) // أول 10 أخطاء فقط
        }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في استيراد الشيكات:', error)
      return { success: false, message: 'حدث خطأ في استيراد الشيكات' }
    }
  }

  // ==================== ربط الدفعات بالفواتير ====================

  // ربط دفعة بفاتورة
  public async linkInvoiceToPayment(linkData: {
    invoice_type: string
    invoice_id: number
    payment_type: string
    payment_id: number
    amount: number
  }): Promise<ApiResponse> {
    try {
      // التحقق من صحة البيانات
      if (!linkData.invoice_type || !linkData.invoice_id || !linkData.payment_type || !linkData.payment_id || !linkData.amount) {
        return { success: false, message: 'جميع بيانات الربط مطلوبة' }
      }

      // التحقق من وجود الفاتورة
      const invoiceTable = linkData.invoice_type === 'sales_invoice' ? 'sales_invoices' :
                          linkData.invoice_type === 'purchase_invoice' ? 'purchase_invoices' :
                          linkData.invoice_type === 'paint_invoice' ? 'paint_invoices' :
                          linkData.invoice_type === 'service_invoice' ? 'service_invoices' : null

      if (!invoiceTable) {
        return { success: false, message: 'نوع الفاتورة غير صحيح. الأنواع المدعومة: sales_invoice, purchase_invoice, paint_invoice, service_invoice' }
      }

      const invoice = this.db.prepare('SELECT * FROM ' + invoiceTable + ' WHERE id = ?').get(linkData.invoice_id)
      if (!invoice) {
        return { success: false, message: 'الفاتورة غير موجودة' }
      }

      // التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي
      if (linkData.amount > invoice.remaining_amount) {
        return { success: false, message: 'المبلغ المدفوع يتجاوز المبلغ المتبقي' }
      }

      // إنشاء جدول ربط الدفعات إذا لم يكن موجوداً
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS invoice_payments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          invoice_type TEXT NOT NULL,
          invoice_id INTEGER NOT NULL,
          payment_type TEXT NOT NULL,
          payment_id INTEGER NOT NULL,
          amount DECIMAL(15,2) NOT NULL,
          payment_date DATE,
          payment_method TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // إضافة الأعمدة المفقودة للجداول الموجودة
      try {
        this.db.exec('ALTER TABLE invoice_payments ADD COLUMN payment_date DATE')
      } catch {
        // العمود موجود بالفعل
      }

      try {
        this.db.exec('ALTER TABLE invoice_payments ADD COLUMN payment_method TEXT')
      } catch {
        // العمود موجود بالفعل
      }

      // إنشاء رابط الدفعة
      const linkResult = this.db.prepare(`
        INSERT INTO invoice_payments (
          invoice_type, invoice_id, payment_type, payment_id, amount, created_at
        ) VALUES (?, ?, ?, ?, ?, datetime('now'))
      `).run(
        linkData.invoice_type,
        linkData.invoice_id,
        linkData.payment_type,
        linkData.payment_id,
        linkData.amount
      )

      if (linkResult.changes === 0) {
        return { success: false, message: 'فشل في ربط الدفعة بالفاتورة' }
      }

      // تحديث المبلغ المدفوع والمتبقي في الفاتورة
      const newPaidAmount = (invoice.paid_amount || 0) + linkData.amount
      const newRemainingAmount = invoice.final_amount - newPaidAmount

      // تحديد حالة الفاتورة الجديدة
      let newStatus = 'pending'
      if (newRemainingAmount <= 0) {
        newStatus = 'paid'
      } else if (newPaidAmount > 0) {
        newStatus = 'partial'
      }

      const updateResult = this.db.prepare(`
        UPDATE ${invoiceTable}
        SET paid_amount = ?, remaining_amount = ?, status = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run(newPaidAmount, newRemainingAmount, newStatus, linkData.invoice_id)

      if (updateResult.changes === 0) {
        return { success: false, message: 'فشل في تحديث حالة الفاتورة' }
      }

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم ربط الدفعة بالفاتورة وتحديث حالتها بنجاح',
        data: {
          newPaidAmount,
          newRemainingAmount,
          newStatus
        }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في ربط الدفعة بالفاتورة:', error)
      return { success: false, message: 'حدث خطأ في ربط الدفعة بالفاتورة' }
    }
  }

  // جلب الفواتير غير المدفوعة
  public async getUnpaidInvoices(entityType?: string, entityId?: number): Promise<ApiResponse> {
    try {
      const invoices: any[] = []

      // جلب فواتير المبيعات غير المدفوعة
      if (!entityType || entityType === 'customer') {
        const salesInvoices = this.db.prepare(`
          SELECT
            si.id, si.invoice_number, si.invoice_date, si.due_date,
            si.final_amount, si.paid_amount, si.remaining_amount, si.status,
            c.name as customer_name, c.code as customer_code,
            'sales' as type, 'sales_invoice' as invoice_type
          FROM sales_invoices si
          LEFT JOIN customers c ON si.customer_id = c.id
          WHERE si.remaining_amount > 0 AND si.status != 'cancelled'
          ${entityId ? 'AND si.customer_id = ?' : ''}
          ORDER BY si.due_date ASC
        `).all(entityId ? [entityId] : [])

        invoices.push(...salesInvoices)
      }

      // جلب فواتير المشتريات غير المدفوعة
      if (!entityType || entityType === 'supplier') {
        const purchaseInvoices = this.db.prepare(`
          SELECT
            pi.id, pi.invoice_number, pi.invoice_date, pi.due_date,
            pi.final_amount, pi.paid_amount, pi.remaining_amount, pi.status,
            s.name as supplier_name, s.code as supplier_code,
            'purchase' as type, 'purchase_invoice' as invoice_type
          FROM purchase_invoices pi
          LEFT JOIN suppliers s ON pi.supplier_id = s.id
          WHERE pi.remaining_amount > 0 AND pi.status != 'cancelled'
          ${entityId ? 'AND pi.supplier_id = ?' : ''}
          ORDER BY pi.due_date ASC
        `).all(entityId ? [entityId] : [])

        invoices.push(...purchaseInvoices)
      }

      // جلب فواتير الدهان غير المدفوعة
      if (!entityType || entityType === 'customer') {
        const paintInvoices = this.db.prepare(`
          SELECT
            pi.id, pi.invoice_number, pi.invoice_date, pi.due_date,
            pi.final_amount, pi.paid_amount, pi.remaining_amount, pi.status,
            c.name as customer_name, c.code as customer_code,
            'paint' as type, 'paint_invoice' as invoice_type
          FROM paint_invoices pi
          LEFT JOIN customers c ON pi.customer_id = c.id
          WHERE pi.remaining_amount > 0 AND pi.status != 'cancelled'
          ${entityId ? 'AND pi.customer_id = ?' : ''}
          ORDER BY pi.due_date ASC
        `).all(entityId ? [entityId] : [])

        invoices.push(...paintInvoices)
      }

      return { success: true, data: invoices }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب الفواتير غير المدفوعة:', error)
      return { success: false, message: 'حدث خطأ في جلب الفواتير غير المدفوعة' }
    }
  }

  // جلب دفعات فاتورة محددة
  public async getInvoicePayments(invoiceId: number, invoiceType: string): Promise<ApiResponse> {
    try {
      const payments = this.db.prepare(`
        SELECT
          ip.*,
          CASE
            WHEN ip.payment_type = 'receipt_voucher' THEN v.voucher_number
            WHEN ip.payment_type = 'payment_voucher' THEN v.voucher_number
            WHEN ip.payment_type = 'check' THEN c.check_number
            ELSE 'غير محدد'
          END as reference_number,
          CASE
            WHEN ip.payment_type = 'receipt_voucher' THEN v.voucher_date
            WHEN ip.payment_type = 'payment_voucher' THEN v.voucher_date
            WHEN ip.payment_type = 'check' THEN c.issue_date
            ELSE ip.created_at
          END as payment_date
        FROM invoice_payments ip
        LEFT JOIN vouchers v ON ip.payment_type IN ('receipt_voucher', 'payment_voucher') AND ip.payment_id = v.id
        LEFT JOIN checks c ON ip.payment_type = 'check' AND ip.payment_id = c.id
        WHERE ip.invoice_id = ? AND ip.invoice_type = ?
        ORDER BY ip.created_at DESC
      `).all([invoiceId, invoiceType])

      return { success: true, data: payments }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في جلب دفعات الفاتورة:', error)
      return { success: false, message: 'حدث خطأ في جلب دفعات الفاتورة' }
    }
  }

  // تحديث حالات الفواتير المتأخرة
  public async updateOverdueInvoices(): Promise<ApiResponse> {
    try {
      const today = new Date().toISOString().split('T')[0]

      // تحديث فواتير المبيعات المتأخرة
      const salesResult = this.db.prepare(`
        UPDATE sales_invoices
        SET status = 'overdue', updated_at = datetime('now')
        WHERE due_date < ?
          AND status IN ('pending', 'partial')
          AND remaining_amount > 0
      `).run(today)

      // تحديث فواتير المشتريات المتأخرة
      const purchaseResult = this.db.prepare(`
        UPDATE purchase_invoices
        SET status = 'overdue', updated_at = datetime('now')
        WHERE due_date < ?
          AND status IN ('pending', 'partial')
          AND remaining_amount > 0
      `).run(today)

      // تحديث فواتير الدهان المتأخرة
      const paintResult = this.db.prepare(`
        UPDATE paint_invoices
        SET status = 'overdue', updated_at = datetime('now')
        WHERE due_date < ?
          AND status IN ('pending', 'partial')
          AND remaining_amount > 0
      `).run(today)

      const totalUpdated = salesResult.changes + purchaseResult.changes + paintResult.changes

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      Logger.info('FinancialService', 'تم تحديث ' + totalUpdated + ' فاتورة متأخرة')

      return {
        success: true,
        message: 'تم تحديث ' + totalUpdated + ' فاتورة متأخرة',
        data: {
          salesUpdated: salesResult.changes,
          purchaseUpdated: purchaseResult.changes,
          paintUpdated: paintResult.changes,
          totalUpdated
        }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في تحديث الفواتير المتأخرة:', error)
      return { success: false, message: 'حدث خطأ في تحديث الفواتير المتأخرة' }
    }
  }

  // توليد رقم سند دفع
  public async generatePaymentVoucherNumber(): Promise<ApiResponse> {
    try {
      const voucherNumber = await this.generateVoucherNumber('payment')
      return {
        success: true,
        data: { voucherNumber }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في توليد رقم سند الدفع:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم سند الدفع' }
    }
  }

  // توليد رقم سند قبض
  public async generateReceiptVoucherNumber(): Promise<ApiResponse> {
    try {
      const voucherNumber = await this.generateVoucherNumber('receipt')
      return {
        success: true,
        data: { voucherNumber }
      }
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في توليد رقم سند القبض:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم سند القبض' }
    }
  }

  // إنشاء سند دفع
  public async createPaymentVoucher(voucherData: {
    voucher_number: string
    payee_name: string
    amount: number
    payment_date: string
    payment_method?: string
    bank_account_id?: number
    check_number?: string
    notes?: string
    reference_type?: string
    reference_id?: number
    created_by?: number
  }): Promise<ApiResponse> {
    try {
      const result = await this.createVoucher({
        voucher_type: 'payment',
        amount: voucherData.amount,
        description: 'دفعة للمورد: ' + voucherData.payee_name,
        reference_type: voucherData.reference_type,
        reference_id: voucherData.reference_id,
        bank_account_id: voucherData.bank_account_id,
        payment_method: voucherData.payment_method as any || 'cash',
        voucher_date: voucherData.payment_date
      }, voucherData.created_by)

      if (result.success) {
        return {
          success: true,
          message: 'تم إنشاء سند الدفع بنجاح',
          data: { voucherId: result.data?.voucherId }
        }
      }

      return result
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في إنشاء سند الدفع:', error)
      return { success: false, message: 'حدث خطأ في إنشاء سند الدفع' }
    }
  }

  // إنشاء سند قبض
  public async createReceiptVoucher(voucherData: {
    voucher_number: string
    payer_name: string
    amount: number
    receipt_date: string
    payment_method?: string
    bank_account_id?: number
    check_number?: string
    notes?: string
    reference_type?: string
    reference_id?: number
    created_by?: number
  }): Promise<ApiResponse> {
    try {
      const result = await this.createVoucher({
        voucher_type: 'receipt',
        amount: voucherData.amount,
        description: 'قبض من العميل: ' + voucherData.payer_name,
        reference_type: voucherData.reference_type,
        reference_id: voucherData.reference_id,
        bank_account_id: voucherData.bank_account_id,
        payment_method: voucherData.payment_method as any || 'cash',
        voucher_date: voucherData.receipt_date
      }, voucherData.created_by)

      if (result.success) {
        return {
          success: true,
          message: 'تم إنشاء سند القبض بنجاح',
          data: { voucherId: result.data?.voucherId }
        }
      }

      return result
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في إنشاء سند القبض:', error)
      return { success: false, message: 'حدث خطأ في إنشاء سند القبض' }
    }
  }

  // دالة التحقق من وجود الكيان
  private validateEntity(entityType: string, entityId: number): boolean {
    try {
      let query = ''
      if (entityType === 'customer') {
        query = 'SELECT id FROM customers WHERE id = ? AND is_active = 1'
      } else if (entityType === 'supplier') {
        query = 'SELECT id FROM suppliers WHERE id = ? AND is_active = 1'
      } else {
        return false
      }

      const entity = this.db.prepare(query).get(entityId)
      return !!entity
    } catch (error) {
      Logger.error('FinancialService', 'خطأ في التحقق من الكيان:', error)
      return false
    }
  }

  // دالة التحقق من صحة بيانات الكمبيالة
  private validatePromissoryNote(noteData: any): { isValid: boolean; message: string } {
    // التحقق من رقم الكمبيالة
    if (!noteData.note_number || noteData.note_number.trim() === '') {
      return { isValid: false, message: 'رقم الكمبيالة مطلوب' }
    }

    // التحقق من اسم الساحب
    if (!noteData.drawer_name || noteData.drawer_name.trim() === '') {
      return { isValid: false, message: 'اسم الساحب مطلوب' }
    }

    // التحقق من اسم المستفيد
    if (!noteData.payee_name || noteData.payee_name.trim() === '') {
      return { isValid: false, message: 'اسم المستفيد مطلوب' }
    }

    // التحقق من المبلغ
    if (!noteData.amount || noteData.amount <= 0) {
      return { isValid: false, message: 'المبلغ يجب أن يكون أكبر من صفر' }
    }

    // التحقق من تاريخ الإصدار
    if (!noteData.issue_date) {
      return { isValid: false, message: 'تاريخ الإصدار مطلوب' }
    }

    // التحقق من تاريخ الاستحقاق
    if (!noteData.due_date) {
      return { isValid: false, message: 'تاريخ الاستحقاق مطلوب' }
    }

    // التحقق من أن تاريخ الاستحقاق بعد تاريخ الإصدار
    const issueDate = new Date(noteData.issue_date)
    const dueDate = new Date(noteData.due_date)

    if (dueDate <= issueDate) {
      return { isValid: false, message: 'تاريخ الاستحقاق يجب أن يكون بعد تاريخ الإصدار' }
    }

    return { isValid: true, message: '' }
  }

  // دالة التحقق من صحة بيانات السند
  private validateVoucher(voucherData: any): { isValid: boolean; message: string } {
    // التحقق من نوع السند
    if (!voucherData.voucher_type || !['receipt', 'payment'].includes(voucherData.voucher_type)) {
      return { isValid: false, message: 'نوع السند غير صحيح' }
    }

    // التحقق من المبلغ
    if (!voucherData.amount || voucherData.amount <= 0) {
      return { isValid: false, message: 'المبلغ يجب أن يكون أكبر من صفر' }
    }

    // التحقق من الوصف
    if (!voucherData.description || voucherData.description.trim() === '') {
      return { isValid: false, message: 'وصف السند مطلوب' }
    }

    // التحقق من تاريخ السند
    if (!voucherData.voucher_date) {
      return { isValid: false, message: 'تاريخ السند مطلوب' }
    }

    // التحقق من صحة تاريخ السند
    const voucherDate = new Date(voucherData.voucher_date)
    if (isNaN(voucherDate.getTime())) {
      return { isValid: false, message: 'تاريخ السند غير صحيح' }
    }

    // التحقق من طريقة الدفع
    if (voucherData.payment_method && !['cash', 'bank_transfer', 'check'].includes(voucherData.payment_method)) {
      return { isValid: false, message: 'طريقة الدفع غير صحيحة' }
    }

    return { isValid: true, message: '' }
  }

  // إنشاء الميزانية العمومية
  public generateBalanceSheet(asOfDate: string): any {
    try {
      // الأصول المتداولة
      const currentAssets = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END) -
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0
          ) as balance,
          0 as level
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id AND je.entry_date <= ?
        WHERE coa.account_type IN ('current_asset', 'cash', 'accounts_receivable', 'inventory')
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING balance != 0
        ORDER BY coa.account_code
      `).all(asOfDate)

      // الأصول الثابتة
      const fixedAssets = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END) -
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0
          ) as balance,
          0 as level
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id AND je.entry_date <= ?
        WHERE coa.account_type IN ('fixed_asset', 'property', 'equipment')
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING balance != 0
        ORDER BY coa.account_code
      `).all(asOfDate)

      // الخصوم المتداولة
      const currentLiabilities = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END) -
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0
          ) as balance,
          0 as level
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id AND je.entry_date <= ?
        WHERE coa.account_type IN ('current_liability', 'accounts_payable', 'accrued_expenses')
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING balance != 0
        ORDER BY coa.account_code
      `).all(asOfDate)

      // الخصوم طويلة الأجل
      const longTermLiabilities = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END) -
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0
          ) as balance,
          0 as level
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id AND je.entry_date <= ?
        WHERE coa.account_type IN ('long_term_liability', 'loan', 'mortgage')
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING balance != 0
        ORDER BY coa.account_code
      `).all(asOfDate)

      // حقوق الملكية
      const equityAccounts = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END) -
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0
          ) as balance,
          0 as level
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id AND je.entry_date <= ?
        WHERE coa.account_type IN ('equity', 'capital', 'retained_earnings')
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING balance != 0
        ORDER BY coa.account_code
      `).all(asOfDate)

      // حساب الإجماليات
      const totalCurrentAssets = currentAssets.reduce((sum: number, item: any) => sum + item.balance, 0)
      const totalFixedAssets = fixedAssets.reduce((sum: number, item: any) => sum + item.balance, 0)
      const totalAssets = totalCurrentAssets + totalFixedAssets

      const totalCurrentLiabilities = currentLiabilities.reduce((sum: number, item: any) => sum + item.balance, 0)
      const totalLongTermLiabilities = longTermLiabilities.reduce((sum: number, item: any) => sum + item.balance, 0)
      const totalLiabilities = totalCurrentLiabilities + totalLongTermLiabilities

      const totalEquity = equityAccounts.reduce((sum: number, item: any) => sum + item.balance, 0)

      return {
        success: true,
        data: {
          assets: {
            current_assets: currentAssets,
            fixed_assets: fixedAssets,
            total_assets: totalAssets
          },
          liabilities: {
            current_liabilities: currentLiabilities,
            long_term_liabilities: longTermLiabilities,
            total_liabilities: totalLiabilities
          },
          equity: {
            equity_accounts: equityAccounts,
            total_equity: totalEquity
          },
          total_liabilities_equity: totalLiabilities + totalEquity,
          as_of_date: asOfDate
        }
      }
    } catch (error) {
      console.error('خطأ في إنشاء الميزانية العمومية:', error)
      return {
        success: false,
        message: 'فشل في إنشاء الميزانية العمومية'
      }
    }
  }

  // إنشاء قائمة الدخل
  public generateIncomeStatement(fromDate: string, toDate: string): any {
    try {
      // إيرادات المبيعات
      const salesRevenue = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END) -
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('revenue', 'sales_revenue')
        AND je.entry_date BETWEEN ? AND ?
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING amount != 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      // الإيرادات الأخرى
      const otherRevenue = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END) -
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('other_income', 'interest_income')
        AND je.entry_date BETWEEN ? AND ?
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING amount != 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      // تكلفة البضاعة المباعة
      const costOfGoodsSold = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END) -
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('cost_of_goods_sold', 'cogs')
        AND je.entry_date BETWEEN ? AND ?
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING amount != 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      // مصروفات البيع
      const sellingExpenses = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END) -
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('selling_expense', 'marketing_expense')
        AND je.entry_date BETWEEN ? AND ?
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING amount != 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      // المصروفات الإدارية
      const administrativeExpenses = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END) -
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('administrative_expense', 'general_expense', 'expense')
        AND je.entry_date BETWEEN ? AND ?
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING amount != 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      // الإيرادات والمصروفات الأخرى
      const otherIncome = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END) -
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('other_income', 'gain')
        AND je.entry_date BETWEEN ? AND ?
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING amount != 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      const otherExpenses = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END) -
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('other_expense', 'loss')
        AND je.entry_date BETWEEN ? AND ?
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING amount != 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      // حساب الإجماليات والنسب
      const totalSalesRevenue = salesRevenue.reduce((sum: number, item: any) => sum + item.amount, 0)
      const totalOtherRevenue = otherRevenue.reduce((sum: number, item: any) => sum + item.amount, 0)
      const totalRevenue = totalSalesRevenue + totalOtherRevenue

      const totalCOGS = costOfGoodsSold.reduce((sum: number, item: any) => sum + item.amount, 0)
      const grossProfit = totalRevenue - totalCOGS

      const totalSellingExpenses = sellingExpenses.reduce((sum: number, item: any) => sum + item.amount, 0)
      const totalAdminExpenses = administrativeExpenses.reduce((sum: number, item: any) => sum + item.amount, 0)
      const totalOperatingExpenses = totalSellingExpenses + totalAdminExpenses

      const operatingIncome = grossProfit - totalOperatingExpenses

      const totalOtherIncome = otherIncome.reduce((sum: number, item: any) => sum + item.amount, 0)
      const totalOtherExpenses = otherExpenses.reduce((sum: number, item: any) => sum + item.amount, 0)
      const netOther = totalOtherIncome - totalOtherExpenses

      const netIncomeBeforeTax = operatingIncome + netOther
      const taxExpense = 0 // يمكن حسابها من حسابات الضرائب
      const netIncome = netIncomeBeforeTax - taxExpense

      // إضافة النسب المئوية
      const addPercentages = (items: any[], total: number) => {
        return items.map(item => ({
          ...item,
          percentage: total > 0 ? (item.amount / total) * 100 : 0
        }))
      }

      return {
        success: true,
        data: {
          revenue: {
            sales_revenue: addPercentages(salesRevenue, totalRevenue),
            other_revenue: addPercentages(otherRevenue, totalRevenue),
            total_revenue: totalRevenue
          },
          cost_of_goods_sold: {
            cogs_items: addPercentages(costOfGoodsSold, totalRevenue),
            total_cogs: totalCOGS
          },
          gross_profit: grossProfit,
          operating_expenses: {
            selling_expenses: addPercentages(sellingExpenses, totalRevenue),
            administrative_expenses: addPercentages(administrativeExpenses, totalRevenue),
            total_operating_expenses: totalOperatingExpenses
          },
          operating_income: operatingIncome,
          other_income_expenses: {
            other_income: addPercentages(otherIncome, totalRevenue),
            other_expenses: addPercentages(otherExpenses, totalRevenue),
            net_other: netOther
          },
          net_income_before_tax: netIncomeBeforeTax,
          tax_expense: taxExpense,
          net_income: netIncome,
          period: {
            from_date: fromDate,
            to_date: toDate
          }
        }
      }
    } catch (error) {
      console.error('خطأ في إنشاء قائمة الدخل:', error)
      return {
        success: false,
        message: 'فشل في إنشاء قائمة الدخل'
      }
    }
  }

  // إنشاء قائمة التدفق النقدي
  public generateCashFlowStatement(fromDate: string, toDate: string, _method: 'direct' | 'indirect' = 'direct'): any {
    try {
      // الأنشطة التشغيلية - المقبوضات النقدية
      const operatingReceipts = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          'sales_receipts' as category,
          COALESCE(
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('cash', 'bank')
        AND je.entry_date BETWEEN ? AND ?
        AND je.description LIKE '%مبيعات%'
        GROUP BY coa.id, coa.account_code, coa.account_name
        HAVING amount > 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      // الأنشطة التشغيلية - المدفوعات النقدية
      const operatingPayments = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          CASE
            WHEN je.description LIKE '%مورد%' THEN 'supplier_payments'
            WHEN je.description LIKE '%راتب%' OR je.description LIKE '%أجور%' THEN 'employee_payments'
            WHEN je.description LIKE '%ضريبة%' THEN 'tax_payments'
            ELSE 'operating_expenses'
          END as category,
          COALESCE(
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('cash', 'bank')
        AND je.entry_date BETWEEN ? AND ?
        AND jed.credit_amount > 0
        GROUP BY coa.id, coa.account_code, coa.account_name, category
        HAVING amount > 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      // الأنشطة الاستثمارية - المقبوضات
      const investingReceipts = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          'asset_sales' as category,
          COALESCE(
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('cash', 'bank')
        AND je.entry_date BETWEEN ? AND ?
        AND (je.description LIKE '%بيع أصل%' OR je.description LIKE '%استثمار%')
        GROUP BY coa.id, coa.account_code, coa.account_name
        HAVING amount > 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      // الأنشطة الاستثمارية - المدفوعات
      const investingPayments = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          'asset_purchases' as category,
          COALESCE(
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('cash', 'bank')
        AND je.entry_date BETWEEN ? AND ?
        AND (je.description LIKE '%شراء أصل%' OR je.description LIKE '%استثمار%')
        AND jed.credit_amount > 0
        GROUP BY coa.id, coa.account_code, coa.account_name
        HAVING amount > 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      // الأنشطة التمويلية - المقبوضات
      const financingReceipts = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          CASE
            WHEN je.description LIKE '%قرض%' THEN 'loan_proceeds'
            ELSE 'capital_contributions'
          END as category,
          COALESCE(
            SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('cash', 'bank')
        AND je.entry_date BETWEEN ? AND ?
        AND (je.description LIKE '%قرض%' OR je.description LIKE '%رأس مال%')
        GROUP BY coa.id, coa.account_code, coa.account_name, category
        HAVING amount > 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      // الأنشطة التمويلية - المدفوعات
      const financingPayments = this.db.prepare(`
        SELECT
          coa.account_code,
          coa.account_name,
          CASE
            WHEN je.description LIKE '%سداد قرض%' THEN 'loan_repayments'
            WHEN je.description LIKE '%توزيع%' THEN 'dividend_payments'
            ELSE 'capital_withdrawals'
          END as category,
          COALESCE(
            SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0
          ) as amount
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('cash', 'bank')
        AND je.entry_date BETWEEN ? AND ?
        AND (je.description LIKE '%سداد%' OR je.description LIKE '%توزيع%' OR je.description LIKE '%سحب%')
        AND jed.credit_amount > 0
        GROUP BY coa.id, coa.account_code, coa.account_name, category
        HAVING amount > 0
        ORDER BY coa.account_code
      `).all(fromDate, toDate)

      // حساب النقد في بداية ونهاية الفترة
      const beginningCash = this.db.prepare(`
        SELECT COALESCE(
          SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END) -
          SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0
        ) as balance
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('cash', 'bank')
        AND je.entry_date < ?
      `).get(fromDate)

      const endingCash = this.db.prepare(`
        SELECT COALESCE(
          SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END) -
          SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0
        ) as balance
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('cash', 'bank')
        AND je.entry_date <= ?
      `).get(toDate)

      // تجميع البيانات وحساب الإجماليات
      const groupByCategory = (items: any[]) => {
        const grouped: any = {}
        items.forEach(item => {
          if (!grouped[item.category]) {
            grouped[item.category] = []
          }
          grouped[item.category].push(item)
        })
        return grouped
      }

      const operatingReceiptsGrouped = groupByCategory(operatingReceipts)
      const operatingPaymentsGrouped = groupByCategory(operatingPayments)
      const investingReceiptsGrouped = groupByCategory(investingReceipts)
      const investingPaymentsGrouped = groupByCategory(investingPayments)
      const financingReceiptsGrouped = groupByCategory(financingReceipts)
      const financingPaymentsGrouped = groupByCategory(financingPayments)

      // حساب الإجماليات
      const totalOperatingReceipts = operatingReceipts.reduce((sum: number, item: any) => sum + item.amount, 0)
      const totalOperatingPayments = operatingPayments.reduce((sum: number, item: any) => sum + item.amount, 0)
      const netOperatingCashFlow = totalOperatingReceipts - totalOperatingPayments

      const totalInvestingReceipts = investingReceipts.reduce((sum: number, item: any) => sum + item.amount, 0)
      const totalInvestingPayments = investingPayments.reduce((sum: number, item: any) => sum + item.amount, 0)
      const netInvestingCashFlow = totalInvestingReceipts - totalInvestingPayments

      const totalFinancingReceipts = financingReceipts.reduce((sum: number, item: any) => sum + item.amount, 0)
      const totalFinancingPayments = financingPayments.reduce((sum: number, item: any) => sum + item.amount, 0)
      const netFinancingCashFlow = totalFinancingReceipts - totalFinancingPayments

      const netChangeInCash = netOperatingCashFlow + netInvestingCashFlow + netFinancingCashFlow
      const totalCashInflows = totalOperatingReceipts + totalInvestingReceipts + totalFinancingReceipts
      const totalCashOutflows = totalOperatingPayments + totalInvestingPayments + totalFinancingPayments

      // إضافة النسب المئوية
      const addPercentages = (items: any[], total: number) => {
        return items.map(item => ({
          ...item,
          percentage: total > 0 ? (item.amount / total) * 100 : 0
        }))
      }

      return {
        success: true,
        data: {
          period: {
            from_date: fromDate,
            to_date: toDate
          },
          operating_activities: {
            cash_receipts: {
              sales_receipts: addPercentages(operatingReceiptsGrouped.sales_receipts || [], totalCashInflows),
              other_receipts: addPercentages(operatingReceiptsGrouped.other_receipts || [], totalCashInflows),
              total_receipts: totalOperatingReceipts
            },
            cash_payments: {
              supplier_payments: addPercentages(operatingPaymentsGrouped.supplier_payments || [], totalCashOutflows),
              employee_payments: addPercentages(operatingPaymentsGrouped.employee_payments || [], totalCashOutflows),
              operating_expenses: addPercentages(operatingPaymentsGrouped.operating_expenses || [], totalCashOutflows),
              tax_payments: addPercentages(operatingPaymentsGrouped.tax_payments || [], totalCashOutflows),
              other_payments: addPercentages(operatingPaymentsGrouped.other_payments || [], totalCashOutflows),
              total_payments: totalOperatingPayments
            },
            net_operating_cash_flow: netOperatingCashFlow
          },
          investing_activities: {
            cash_receipts: {
              asset_sales: addPercentages(investingReceiptsGrouped.asset_sales || [], totalCashInflows),
              investment_income: addPercentages(investingReceiptsGrouped.investment_income || [], totalCashInflows),
              total_receipts: totalInvestingReceipts
            },
            cash_payments: {
              asset_purchases: addPercentages(investingPaymentsGrouped.asset_purchases || [], totalCashOutflows),
              investments: addPercentages(investingPaymentsGrouped.investments || [], totalCashOutflows),
              total_payments: totalInvestingPayments
            },
            net_investing_cash_flow: netInvestingCashFlow
          },
          financing_activities: {
            cash_receipts: {
              loan_proceeds: addPercentages(financingReceiptsGrouped.loan_proceeds || [], totalCashInflows),
              capital_contributions: addPercentages(financingReceiptsGrouped.capital_contributions || [], totalCashInflows),
              total_receipts: totalFinancingReceipts
            },
            cash_payments: {
              loan_repayments: addPercentages(financingPaymentsGrouped.loan_repayments || [], totalCashOutflows),
              dividend_payments: addPercentages(financingPaymentsGrouped.dividend_payments || [], totalCashOutflows),
              capital_withdrawals: addPercentages(financingPaymentsGrouped.capital_withdrawals || [], totalCashOutflows),
              total_payments: totalFinancingPayments
            },
            net_financing_cash_flow: netFinancingCashFlow
          },
          summary: {
            beginning_cash: beginningCash?.balance || 0,
            net_change_in_cash: netChangeInCash,
            ending_cash: endingCash?.balance || 0,
            total_cash_inflows: totalCashInflows,
            total_cash_outflows: totalCashOutflows
          }
        }
      }
    } catch (error) {
      console.error('خطأ في إنشاء قائمة التدفق النقدي:', error)
      return {
        success: false,
        message: 'فشل في إنشاء قائمة التدفق النقدي'
      }
    }
  }

  // إنشاء مطابقة البنك
  public generateBankReconciliation(bankAccountId: number, asOfDate: string): any {
    try {
      // معلومات الحساب البنكي
      const bankAccount = this.db.prepare(`
        SELECT id, account_name, account_number, bank_name
        FROM bank_accounts
        WHERE id = ?
      `).get(bankAccountId)

      if (!bankAccount) {
        return {
          success: false,
          message: 'الحساب البنكي غير موجود'
        }
      }

      // كشف البنك - المعاملات البنكية
      const bankTransactions = this.db.prepare(`
        SELECT
          id,
          transaction_date,
          description,
          debit_amount,
          credit_amount,
          balance,
          reference_number,
          is_reconciled,
          reconciled_date,
          reconciled_by
        FROM bank_transactions
        WHERE account_id = ? AND transaction_date <= ?
        ORDER BY transaction_date
      `).all(bankAccountId, asOfDate)

      // سجلات الدفاتر - قيود اليومية للحساب البنكي
      const bookTransactions = this.db.prepare(`
        SELECT
          jed.id,
          je.entry_date,
          je.description,
          jed.debit_amount,
          jed.credit_amount,
          je.reference_number,
          v.voucher_number,
          jed.is_reconciled,
          jed.reconciled_date
        FROM journal_entry_details jed
        JOIN journal_entries je ON jed.entry_id = je.id
        LEFT JOIN vouchers v ON je.voucher_id = v.id
        WHERE jed.account_id = (
          SELECT chart_account_id FROM bank_accounts WHERE id = ?
        )
        AND je.entry_date <= ?
        ORDER BY je.entry_date
      `).all(bankAccountId, asOfDate)

      // حساب الأرصدة
      const bankOpeningBalance = 0 // يمكن حسابه من المعاملات السابقة
      const bankClosingBalance = bankTransactions.reduce((balance: number, trans: any) => {
        return balance + (trans.credit_amount || 0) - (trans.debit_amount || 0)
      }, bankOpeningBalance)

      const bookOpeningBalance = 0 // يمكن حسابه من القيود السابقة
      const bookClosingBalance = bookTransactions.reduce((balance: number, trans: any) => {
        return balance + (trans.debit_amount || 0) - (trans.credit_amount || 0)
      }, bookOpeningBalance)

      // البنود غير المطابقة
      const unreconciledBankItems = bankTransactions.filter((trans: any) => !trans.is_reconciled)
      const unreconciledBookItems = bookTransactions.filter((trans: any) => !trans.is_reconciled)

      // الشيكات المعلقة (صادرة ولم تصرف بعد)
      const outstandingChecks = this.db.prepare(`
        SELECT
          bt.id,
          bt.transaction_date,
          bt.description,
          bt.debit_amount,
          bt.credit_amount,
          bt.reference_number
        FROM bank_transactions bt
        WHERE bt.account_id = ?
        AND bt.transaction_date <= ?
        AND bt.debit_amount > 0
        AND bt.is_reconciled = 0
        AND bt.description LIKE '%شيك%'
      `).all(bankAccountId, asOfDate)

      // الودائع في الطريق (مودعة ولم تظهر في كشف البنك بعد)
      const depositsInTransit = this.db.prepare(`
        SELECT
          jed.id,
          je.entry_date as transaction_date,
          je.description,
          jed.debit_amount,
          jed.credit_amount,
          je.reference_number
        FROM journal_entry_details jed
        JOIN journal_entries je ON jed.entry_id = je.id
        WHERE jed.account_id = (
          SELECT chart_account_id FROM bank_accounts WHERE id = ?
        )
        AND je.entry_date <= ?
        AND jed.debit_amount > 0
        AND jed.is_reconciled = 0
        AND je.description LIKE '%إيداع%'
      `).all(bankAccountId, asOfDate)

      // حساب الأرصدة المعدلة
      const outstandingChecksTotal = outstandingChecks.reduce((sum: number, check: any) => sum + (check.debit_amount || 0), 0)
      const depositsInTransitTotal = depositsInTransit.reduce((sum: number, deposit: any) => sum + (deposit.credit_amount || deposit.debit_amount || 0), 0)

      const adjustedBankBalance = bankClosingBalance - outstandingChecksTotal + depositsInTransitTotal
      const adjustedBookBalance = bookClosingBalance // قد تحتاج تعديلات إضافية

      const difference = adjustedBankBalance - adjustedBookBalance
      const reconciledItems = bankTransactions.filter((trans: any) => trans.is_reconciled).length

      return {
        success: true,
        data: {
          bank_account: bankAccount,
          period: {
            from_date: asOfDate,
            to_date: asOfDate
          },
          bank_statement: {
            opening_balance: bankOpeningBalance,
            closing_balance: bankClosingBalance,
            transactions: bankTransactions
          },
          book_records: {
            opening_balance: bookOpeningBalance,
            closing_balance: bookClosingBalance,
            transactions: bookTransactions
          },
          reconciliation: {
            reconciled_items: reconciledItems,
            unreconciled_bank_items: unreconciledBankItems,
            unreconciled_book_items: unreconciledBookItems,
            outstanding_checks: outstandingChecks,
            deposits_in_transit: depositsInTransit,
            bank_errors: [], // يمكن إضافة منطق للكشف عن الأخطاء
            book_errors: [], // يمكن إضافة منطق للكشف عن الأخطاء
            adjusted_bank_balance: adjustedBankBalance,
            adjusted_book_balance: adjustedBookBalance,
            difference: difference
          }
        }
      }
    } catch (error) {
      console.error('خطأ في إنشاء مطابقة البنك:', error)
      return {
        success: false,
        message: 'فشل في إنشاء مطابقة البنك'
      }
    }
  }



  // الحصول على فئات المنتجات
  public getProductCategories(): any {
    try {
      const categories = this.db.prepare(`
        SELECT DISTINCT category
        FROM products
        WHERE category IS NOT NULL AND category != ''
        ORDER BY category
      `).all()

      return {
        success: true,
        data: categories.map((cat: any) => cat.category)
      }
    } catch (error) {
      console.error('خطأ في جلب فئات المنتجات:', error)
      return {
        success: false,
        message: 'فشل في جلب فئات المنتجات'
      }
    }
  }

  // تم إزالة جميع دوال البيانات التجريبية - يجب استخدام البيانات الحقيقية فقط

}
