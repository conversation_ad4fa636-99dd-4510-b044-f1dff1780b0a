import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, Card, Alert, Spin, Space, Typography, Divider } from 'antd'
import { PrinterOutlined, EyeOutlined, BugOutlined, CheckCircleOutlined } from '@ant-design/icons'
import { MasterPrintService } from '../../services/MasterPrintService'
import { ImagePrintService } from '../../services/images/ImagePrintService'
import { Logger } from '../../utils/Logger'

const { Title, Text, Paragraph } = Typography

interface TestResult {
  test: string
  status: 'success' | 'error' | 'warning'
  message: string
  details?: any
}

export const ImagePrintTest: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [testResults, setTestResults] = useState<TestResult[]>([])

  const printService = MasterPrintService.getInstance()
  const imageService = ImagePrintService.getInstance()

  // اختبار تحميل الصور من قاعدة البيانات
  const testImageLoading = async (): Promise<TestResult> => {
    try {
      Logger.info('ImagePrintTest', '🧪 اختبار تحميل الصور من قاعدة البيانات')
      
      // محاولة جلب صور الأصناف
      const itemImages = await window.electronAPI?.getItemImages(1)
      
      if (itemImages?.success && itemImages.data?.length > 0) {
        const firstImage = itemImages.data[0]
        Logger.info('ImagePrintTest', 'أول صورة:', firstImage)
        
        return {
          test: 'تحميل الصور من قاعدة البيانات',
          status: 'success',
          message: `تم العثور على ${itemImages.data.length} صورة`,
          details: {
            firstImagePath: firstImage.image_path,
            firstImageName: firstImage.image_name,
            isBase64: firstImage.image_path?.startsWith('data:')
          }
        }
      } else {
        return {
          test: 'تحميل الصور من قاعدة البيانات',
          status: 'warning',
          message: 'لا توجد صور في قاعدة البيانات للاختبار'
        }
      }
    } catch (error) {
      return {
        test: 'تحميل الصور من قاعدة البيانات',
        status: 'error',
        message: `خطأ في تحميل الصور: ${error}`
      }
    }
  }

  // اختبار تحويل الصور إلى base64
  const testImageConversion = async (): Promise<TestResult> => {
    try {
      Logger.info('ImagePrintTest', '🧪 اختبار تحويل الصور إلى base64')
      
      // اختبار مسارات مختلفة
      const testPaths = [
        'C:\\test\\image.jpg',
        'images/test.png',
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
      ]

      const results = []
      for (const testPath of testPaths) {
        try {
          const result = await window.electronAPI?.readFileAsBase64(testPath)
          results.push({
            path: testPath,
            success: !!result,
            result: result ? 'تم التحويل بنجاح' : 'فشل التحويل'
          })
        } catch (error) {
          results.push({
            path: testPath,
            success: false,
            result: `خطأ: ${error}`
          })
        }
      }

      return {
        test: 'تحويل الصور إلى base64',
        status: 'success',
        message: 'تم اختبار تحويل الصور',
        details: results
      }
    } catch (error) {
      return {
        test: 'تحويل الصور إلى base64',
        status: 'error',
        message: `خطأ في اختبار التحويل: ${error}`
      }
    }
  }

  // اختبار طباعة صورة تجريبية
  const testImagePrint = async (): Promise<TestResult> => {
    try {
      Logger.info('ImagePrintTest', '🧪 اختبار طباعة صورة تجريبية')
      
      // إنشاء صورة تجريبية
      const testImage = {
        id: 'test-image-1',
        name: 'صورة تجريبية',
        path: 'data:image/svg+xml;base64,' + btoa(`
          <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#e3f2fd"/>
            <text x="50%" y="40%" text-anchor="middle" font-family="Arial" font-size="16" fill="#1976d2">
              صورة تجريبية
            </text>
            <text x="50%" y="60%" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
              للاختبار فقط
            </text>
            <circle cx="50%" cy="75%" r="20" fill="#4caf50"/>
          </svg>
        `),
        description: 'صورة تجريبية لاختبار النظام',
        category: 'test',
        size: 1024,
        uploadDate: new Date().toISOString()
      }

      // محاولة طباعة الصورة
      const printData = {
        type: 'images' as const,
        title: 'اختبار طباعة الصور',
        images: [testImage],
        metadata: {
          testMode: true,
          timestamp: new Date().toISOString()
        }
      }

      const printOptions = {
        layout: 'single' as const,
        quality: 'high' as const,
        showMetadata: true,
        preview: true
      }

      await printService.preview(printData, printOptions)

      return {
        test: 'طباعة صورة تجريبية',
        status: 'success',
        message: 'تم فتح معاينة الطباعة بنجاح'
      }
    } catch (error) {
      return {
        test: 'طباعة صورة تجريبية',
        status: 'error',
        message: `خطأ في طباعة الصورة التجريبية: ${error}`
      }
    }
  }

  // تشغيل جميع الاختبارات
  const runAllTests = async () => {
    setLoading(true)
    setTestResults([])

    try {
      const tests = [
        testImageLoading,
        testImageConversion,
        testImagePrint
      ]

      const results: TestResult[] = []
      for (const test of tests) {
        const result = await test()
        results.push(result)
        setTestResults([...results])
      }
    } catch (error) {
      Logger.error('ImagePrintTest', 'خطأ في تشغيل الاختبارات:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return '#52c41a'
      case 'warning': return '#faad14'
      case 'error': return '#ff4d4f'
      default: return '#d9d9d9'
    }
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'warning': return <EyeOutlined style={{ color: '#faad14' }} />
      case 'error': return <BugOutlined style={{ color: '#ff4d4f' }} />
      default: return null
    }
  }

  return (
    <Card title="اختبار نظام طباعة الصور" style={{ margin: 20 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Alert
          message="أداة تشخيص مشاكل طباعة الصور"
          description="هذه الأداة تساعد في تشخيص وحل مشاكل عدم ظهور الصور في الطباعة والمعاينة"
          type="info"
          showIcon
        />

        <Button
          type="primary"
          icon={<BugOutlined />}
          onClick={runAllTests}
          loading={loading}
          size="large"
        >
          تشغيل اختبارات التشخيص
        </Button>

        <Divider />

        {testResults.length > 0 && (
          <div>
            <Title level={4}>نتائج الاختبارات:</Title>
            {testResults.map((result, index) => (
              <Card
                key={index}
                size="small"
                style={{ 
                  marginBottom: 10,
                  borderLeft: `4px solid ${getStatusColor(result.status)}`
                }}
              >
                <Space>
                  {getStatusIcon(result.status)}
                  <Text strong>{result.test}</Text>
                </Space>
                <Paragraph style={{ margin: '8px 0 0 0' }}>
                  {result.message}
                </Paragraph>
                {result.details && (
                  <details style={{ marginTop: 8 }}>
                    <summary style={{ cursor: 'pointer', color: '#1890ff' }}>
                      عرض التفاصيل
                    </summary>
                    <pre style={{ 
                      background: '#f5f5f5', 
                      padding: 8, 
                      borderRadius: 4,
                      fontSize: 12,
                      marginTop: 8
                    }}>
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </Card>
            ))}
          </div>
        )}

        {loading && (
          <div style={{ textAlign: 'center', padding: 20 }}>
            <Spin size="large" />
            <div style={{ marginTop: 10 }}>جاري تشغيل الاختبارات...</div>
          </div>
        )}
      </Space>
    </Card>
  )
}

export default ImagePrintTest
