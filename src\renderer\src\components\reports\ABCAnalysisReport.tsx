/**
 * تقرير تحليل ABC للأصناف
 * يصنف الأصناف حسب قيمة المبيعات إلى فئات A, B, C
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'
import dayjs from 'dayjs'
import { SafeLogger as Logger } from '../../utils/logger'

interface ABCAnalysisReportProps {
  onBack?: () => void
}

interface ABCItem {
  item_id: number
  item_code: string
  item_name: string
  category_name: string
  warehouse_name: string
  total_sales: number
  total_quantity: number
  sales_percentage: number
  cumulative_percentage: number
  abc_category: 'A' | 'B' | 'C'
  rank: number
}

const ABCAnalysisReport: React.FC<ABCAnalysisReportProps> = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any) => {
    try {
      Logger.info('ABCAnalysisReport', 'بدء إنشاء تقرير تحليل ABC:', filters)

      // استدعاء API للحصول على بيانات تحليل ABC
      const response = await window.electronAPI?.getABCAnalysisReport({
        warehouseId: filters.warehouseId,
        dateFrom: filters.dateRange?.[0]?.format('YYYY-MM-DD'),
        dateTo: filters.dateRange?.[1]?.format('YYYY-MM-DD')
      })

      if (!response?.success) {
        throw new Error(response?.message || 'فشل في تحميل بيانات التقرير')
      }

      const data = response.data || []

      // حساب الإحصائيات
      const totalItems = data.length
      const categoryA = data.filter((item: ABCItem) => item.abc_category === 'A')
      const categoryB = data.filter((item: ABCItem) => item.abc_category === 'B')
      const categoryC = data.filter((item: ABCItem) => item.abc_category === 'C')

      const totalSales = data.reduce((sum: number, item: ABCItem) => sum + item.total_sales, 0)
      const categoryASales = categoryA.reduce((sum: number, item: ABCItem) => sum + item.total_sales, 0)
      const categoryBSales = categoryB.reduce((sum: number, item: ABCItem) => sum + item.total_sales, 0)
      const categoryCSales = categoryC.reduce((sum: number, item: ABCItem) => sum + item.total_sales, 0)

      // تحديد الأعمدة
      const columns = [
        {
          key: 'rank',
          title: 'الترتيب',
          align: 'center' as const,
          format: 'number' as const,
          width: '80px'
        },
        {
          key: 'item_code',
          title: 'كود الصنف',
          align: 'center' as const,
          format: 'text' as const,
          width: '120px'
        },
        {
          key: 'item_name',
          title: 'اسم الصنف',
          align: 'right' as const,
          format: 'text' as const,
          width: '200px'
        },
        {
          key: 'category_name',
          title: 'الفئة',
          align: 'center' as const,
          format: 'text' as const,
          width: '120px'
        },
        {
          key: 'warehouse_name',
          title: 'المخزن',
          align: 'center' as const,
          format: 'text' as const,
          width: '120px'
        },
        {
          key: 'total_sales',
          title: 'إجمالي المبيعات',
          align: 'center' as const,
          format: 'currency' as const,
          width: '140px'
        },
        {
          key: 'total_quantity',
          title: 'الكمية المباعة',
          align: 'center' as const,
          format: 'number' as const,
          width: '120px'
        },
        {
          key: 'sales_percentage',
          title: 'نسبة المبيعات (%)',
          align: 'center' as const,
          format: 'percentage' as const,
          width: '140px'
        },
        {
          key: 'cumulative_percentage',
          title: 'النسبة التراكمية (%)',
          align: 'center' as const,
          format: 'percentage' as const,
          width: '150px'
        },
        {
          key: 'abc_category',
          title: 'تصنيف ABC',
          align: 'center' as const,
          format: 'text' as const,
          width: '100px'
        }
      ]

      return {
        title: 'تقرير تحليل ABC للأصناف',
        data,
        columns,
        summary: {
          totalItems,
          categoryAItems: categoryA.length,
          categoryBItems: categoryB.length,
          categoryCItems: categoryC.length,
          totalSales,
          categoryASales,
          categoryBSales,
          categoryCSales,
          categoryAPercentage: totalSales > 0 ? ((categoryASales / totalSales) * 100).toFixed(1) : '0',
          categoryBPercentage: totalSales > 0 ? ((categoryBSales / totalSales) * 100).toFixed(1) : '0',
          categoryCPercentage: totalSales > 0 ? ((categoryCSales / totalSales) * 100).toFixed(1) : '0'
        },
        metadata: {
          reportType: 'abc-analysis' as ReportType,
          totalRecords: data.length,
          dateRange: filters.dateRange ?
            `${filters.dateRange[0]?.format('YYYY-MM-DD')} - ${filters.dateRange[1]?.format('YYYY-MM-DD')}` :
            'جميع الفترات',
          warehouse: filters.warehouseId ? 'مخزن محدد' : 'جميع المخازن',
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام تحليل ABC'
        }
      }
    } catch (error) {
      Logger.error('ABCAnalysisReport', 'خطأ في إنشاء التقرير:', error)
      throw error
    }
  }

  return (
    <UniversalReport
      reportType={'abc-analysis' as ReportType}
      title="تقرير تحليل ABC للأصناف"
      description="تصنيف الأصناف حسب قيمة المبيعات إلى فئات A, B, C لتحسين إدارة المخزون"
      onGenerateReport={generateReport}
      showDateRange={true}
      showWarehouseFilter={true}
      showCategoryFilter={false}
      showItemFilter={false}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="abc_analysis_report"
      defaultFilters={{
        dateRange: [dayjs().subtract(1, 'year'), dayjs()],
        sortBy: 'rank',
        sortOrder: 'asc'
      }}

    />
  )
}

export default ABCAnalysisReport
