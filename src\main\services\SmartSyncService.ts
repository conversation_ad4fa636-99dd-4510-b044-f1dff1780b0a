import * as fs from 'fs'
import * as path from 'path'
import * as os from 'os'
import { exec } from 'child_process'
import { promisify } from 'util'
import { Logger } from '../utils/logger'
import { SimpleDatabaseService } from './SimpleDatabaseService'

const execAsync = promisify(exec)

export interface SmartSyncConfig {
  autoCreateSharedFolder: boolean
  preferredLocation: 'network' | 'local' | 'auto'
  folderName: string
  enableNetworkSharing: boolean
  autoDiscoverDevices: boolean
}

export class SmartSyncService {
  private databaseService: SimpleDatabaseService | any
  private config: SmartSyncConfig

  constructor(databaseService: SimpleDatabaseService | any) {
    this.databaseService = databaseService
    this.config = {
      autoCreateSharedFolder: true,
      preferredLocation: 'auto',
      folderName: 'ZET.IA-Shared',
      enableNetworkSharing: true,
      autoDiscoverDevices: true
    }
  }

  /**
   * فحص ما إذا كان التطبيق يعمل بصلاحيات إدارية
   */
  private async isRunningAsAdmin(): Promise<boolean> {
    try {
      // محاولة تنفيذ أمر يتطلب صلاحيات إدارية
      await execAsync('net session', { timeout: 3000 })
      return true
    } catch {
      return false
    }
  }

  /**
   * تنفيذ أمر بصلاحيات إدارية عبر UAC (Windows يطلب الصلاحيات)
   */
  private async executeWithUAC(command: string): Promise<{ success: boolean; stdout?: string; stderr?: string; userCancelled?: boolean }> {
    try {
      Logger.info('SmartSync', `تنفيذ أمر بصلاحيات إدارية: ${command}`)

      // استخدام PowerShell مع UAC لطلب الصلاحيات من Windows
      const uacCommand = `powershell -Command "Start-Process cmd -ArgumentList '/c ${command}' -Verb RunAs -Wait -WindowStyle Hidden"`

      const { stdout, stderr } = await execAsync(uacCommand, { timeout: 30000 })

      Logger.success('SmartSync', 'تم تنفيذ الأمر بصلاحيات إدارية بنجاح')
      return { success: true, stdout, stderr }

    } catch (error: any) {
      Logger.warn('SmartSync', 'فشل في تنفيذ الأمر بصلاحيات إدارية:', error)

      // فحص إذا كان المستخدم ألغى UAC
      const userCancelled = error.message && (
        error.message.includes('cancelled') ||
        error.message.includes('Operation was canceled') ||
        error.code === 1223 // Windows error code for user cancellation
      )

      if (userCancelled) {
        Logger.info('SmartSync', 'المستخدم ألغى طلب الصلاحيات الإدارية من Windows')
        return { success: false, userCancelled: true, stderr: 'تم إلغاء طلب الصلاحيات من قبل المستخدم' }
      }

      return { success: false, userCancelled: false, stderr: error.message }
    }
  }

  /**
   * إنشاء مجلد مشترك ذكي تلقائياً
   */
  async createSmartSharedFolder(): Promise<{ success: boolean; path?: string; networkPath?: string; message: string }> {
    try {
      Logger.info('SmartSync', 'بدء إنشاء المجلد المشترك الذكي...')

      // 1. البحث عن أفضل موقع للمجلد المشترك
      const bestLocation = await this.findBestSharedLocation()
      
      if (!bestLocation.success) {
        return bestLocation
      }

      const sharedPath = bestLocation.path as string
      Logger.info('SmartSync', `تم اختيار الموقع: ${sharedPath}`)

      // 2. إنشاء المجلد إذا لم يكن موجوداً
      if (!fs.existsSync(sharedPath)) {
        fs.mkdirSync(sharedPath, { recursive: true })
        Logger.success('SmartSync', `تم إنشاء المجلد: ${sharedPath}`)
      }

      // 3. إعداد صلاحيات المشاركة
      await this.setupFolderSharing(sharedPath)

      // 4. إنشاء ملفات التكوين الأساسية
      await this.createConfigFiles(sharedPath)

      // 5. اختبار المجلد
      const testResult = await this.testSharedFolder(sharedPath)
      if (!testResult.success) {
        return testResult
      }

      // إنشاء رسالة نجاح مفصلة
      const networkPath = `\\\\${os.hostname()}\\${this.config.folderName}`
      const successMessage = `🎉 تم إنشاء المجلد المشترك بنجاح!

📁 المسار المحلي: ${sharedPath}
🌐 مسار الشبكة: ${networkPath}
💾 قاعدة البيانات: تم نسخها وإعدادها
🔐 الصلاحيات: تم تكوينها للمشاركة

يمكن للأجهزة الفرعية الآن الاتصال باستخدام المسار: ${networkPath}`

      Logger.success('SmartSync', '🎉 تم إنشاء المجلد المشترك بنجاح!')
      Logger.info('SmartSync', `📁 المسار المحلي: ${sharedPath}`)
      Logger.info('SmartSync', `🌐 مسار الشبكة: ${networkPath}`)

      return {
        success: true,
        path: sharedPath,
        networkPath: networkPath,
        message: successMessage
      }

    } catch (error: any) {
      Logger.error('SmartSync', 'خطأ في إنشاء المجلد المشترك:', error)
      return {
        success: false,
        message: `خطأ في إنشاء المجلد المشترك: ${error.message}`
      }
    }
  }

  /**
   * البحث عن أفضل موقع للمجلد المشترك
   */
  private async findBestSharedLocation(): Promise<{ success: boolean; path?: string; message: string }> {
    const locations = await this.getSuggestedLocations()

    for (const location of locations) {
      try {
        // فحص إمكانية الوصول
        if (fs.existsSync(location.basePath)) {
          const testPath = path.join(location.basePath, this.config.folderName)
          
          // محاولة إنشاء مجلد تجريبي
          if (!fs.existsSync(testPath)) {
            fs.mkdirSync(testPath, { recursive: true })
          }

          // اختبار الكتابة
          const testFile = path.join(testPath, 'test-write.tmp')
          fs.writeFileSync(testFile, 'test')
          fs.unlinkSync(testFile)

          Logger.success('SmartSync', `تم العثور على موقع مناسب: ${testPath} (${location.type})`)
          
          return {
            success: true,
            path: testPath,
            message: `تم اختيار الموقع: ${location.description}`
          }
        }
      } catch (error) {
        Logger.warn('SmartSync', `فشل في اختبار الموقع ${location.basePath}:`, error)
        continue
      }
    }

    return {
      success: false,
      message: 'لم يتم العثور على موقع مناسب للمجلد المشترك'
    }
  }

  /**
   * الحصول على قائمة المواقع المقترحة
   */
  private async getSuggestedLocations() {
    const locations = []
    const username = os.userInfo().username
    // const hostname = os.hostname() // غير مستخدم حالياً

    // 1. مجلد المستندات المشتركة (الأولوية الأولى)
    const publicDocuments = path.join('C:', 'Users', 'Public', 'Documents')
    locations.push({
      basePath: publicDocuments,
      type: 'public',
      priority: 1,
      description: 'مجلد المستندات المشتركة (يمكن الوصول إليه من جميع المستخدمين)'
    })

    // 2. مجلد المستخدم الحالي
    const userDocuments = path.join(os.homedir(), 'Documents')
    locations.push({
      basePath: userDocuments,
      type: 'user',
      priority: 2,
      description: `مجلد مستندات المستخدم (${username})`
    })

    // 3. محرك الأقراص الجذر
    locations.push({
      basePath: 'C:',
      type: 'root',
      priority: 3,
      description: 'محرك الأقراص الرئيسي'
    })

    // 4. البحث عن محركات أقراص الشبكة
    try {
      const networkDrives = await this.findNetworkDrives()
      networkDrives.forEach((drive) => {
        locations.push({
          basePath: drive,
          type: 'network',
          priority: 0, // أولوية عالية للشبكة
          description: `محرك أقراص الشبكة (${drive})`
        })
      })
    } catch (error) {
      Logger.warn('SmartSync', 'فشل في البحث عن محركات أقراص الشبكة:', error)
    }

    // ترتيب حسب الأولوية
    return locations.sort((a, b) => a.priority - b.priority)
  }

  /**
   * البحث عن محركات أقراص الشبكة
   */
  private async findNetworkDrives(): Promise<string[]> {
    try {
      const { stdout } = await execAsync('wmic logicaldisk get size,freespace,caption,drivetype')
      const lines = stdout.split('\n').filter((line: string) => line.trim())
      
      const networkDrives: string[] = []
      
      for (const line of lines) {
        const parts = line.trim().split(/\s+/)
        if (parts.length >= 4) {
          const driveType = parts[3]
          const caption = parts[0]
          
          // نوع 4 = محرك شبكة
          if (driveType === '4' && caption && caption.includes(':')) {
            networkDrives.push(caption)
          }
        }
      }

      return networkDrives
    } catch (error) {
      Logger.warn('SmartSync', 'خطأ في البحث عن محركات الشبكة:', error)
      return []
    }
  }

  /**
   * إعداد مشاركة المجلد
   */
  private async setupFolderSharing(folderPath: string): Promise<void> {
    try {
      if (!this.config.enableNetworkSharing) {
        Logger.info('SmartSync', 'مشاركة الشبكة معطلة في الإعدادات')
        return
      }

      Logger.info('SmartSync', 'إعداد مشاركة المجلد...')

      // إنشاء مشاركة شبكة للمجلد
      const shareName = this.config.folderName.replace(/[^a-zA-Z0-9]/g, '')

      // التحقق من وجود المشاركة
      try {
        const { stdout } = await execAsync(`net share ${shareName}`, { timeout: 5000 })
        if (stdout.includes(folderPath)) {
          Logger.info('SmartSync', `المشاركة ${shareName} موجودة بالفعل وتشير للمجلد الصحيح`)
          return
        }
      } catch {
        // المشاركة غير موجودة، سنحاول إنشاؤها
      }

      // فحص الصلاحيات الحالية
      const hasAdminRights = await this.isRunningAsAdmin()

      if (hasAdminRights) {
        // لدينا صلاحيات إدارية، ننشئ المشاركة مباشرة
        await this.createNetworkShareDirect(folderPath, shareName)
      } else {
        // لا توجد صلاحيات إدارية، نطلب من Windows عبر UAC
        await this.createNetworkShareWithUAC(folderPath, shareName)
      }

    } catch (error) {
      Logger.error('SmartSync', 'خطأ عام في إعداد مشاركة المجلد:', error)
    }
  }

  /**
   * إنشاء مشاركة الشبكة مباشرة (مع الصلاحيات الإدارية الموجودة)
   */
  private async createNetworkShareDirect(folderPath: string, shareName: string): Promise<void> {
    try {
      // إنشاء مشاركة جديدة
      await execAsync(`net share ${shareName}="${folderPath}" /grant:everyone,full`, { timeout: 10000 })
      Logger.success('SmartSync', `✅ تم إنشاء مشاركة الشبكة: \\\\${os.hostname()}\\${shareName}`)

      // إعداد صلاحيات المجلد
      await execAsync(`icacls "${folderPath}" /grant Everyone:F /T`, { timeout: 10000 })
      Logger.success('SmartSync', '✅ تم إعداد صلاحيات المجلد')

    } catch (error: any) {
      Logger.error('SmartSync', 'فشل في إنشاء مشاركة الشبكة:', error)
      throw error
    }
  }

  /**
   * إنشاء مشاركة الشبكة عبر UAC (Windows يطلب الصلاحيات)
   */
  private async createNetworkShareWithUAC(folderPath: string, shareName: string): Promise<void> {
    try {
      Logger.info('SmartSync', 'طلب الصلاحيات الإدارية من Windows لإنشاء مشاركة الشبكة...')

      // إنشاء أمر مشاركة الشبكة
      const shareCommand = `net share ${shareName}="${folderPath}" /grant:everyone,full`

      // تنفيذ الأمر عبر UAC
      const shareResult = await this.executeWithUAC(shareCommand)

      if (shareResult.success) {
        Logger.success('SmartSync', `✅ تم إنشاء مشاركة الشبكة عبر UAC: \\\\${os.hostname()}\\${shareName}`)

        // إعداد صلاحيات المجلد
        const permCommand = `icacls "${folderPath}" /grant Everyone:F /T`
        const permResult = await this.executeWithUAC(permCommand)

        if (permResult.success) {
          Logger.success('SmartSync', '✅ تم إعداد صلاحيات المجلد عبر UAC')
        } else {
          Logger.warn('SmartSync', 'تحذير: فشل في إعداد صلاحيات المجلد:', permResult.stderr)
        }

        // تسجيل نجاح العملية
        Logger.success('SmartSync', `🎉 تم تفعيل مشاركة الشبكة بنجاح! يمكن الوصول عبر: \\\\${os.hostname()}\\${shareName}`)

      } else if (shareResult.userCancelled) {
        Logger.info('SmartSync', 'المستخدم ألغى طلب الصلاحيات الإدارية')

        // تسجيل إلغاء العملية
        Logger.info('SmartSync', '⚠️ تم إلغاء طلب الصلاحيات الإدارية - يمكن المتابعة بدون مشاركة الشبكة')

      } else {
        Logger.error('SmartSync', 'فشل في إنشاء مشاركة الشبكة عبر UAC:', shareResult.stderr)

        // تسجيل فشل العملية
        Logger.error('SmartSync', `❌ فشل في إنشاء مشاركة الشبكة: ${shareResult.stderr}`)
      }

    } catch (error: any) {
      Logger.error('SmartSync', 'خطأ في إنشاء مشاركة الشبكة عبر UAC:', error)
      throw error
    }
  }

  /**
   * إنشاء ملفات التكوين الأساسية ونسخ قاعدة البيانات
   */
  private async createConfigFiles(folderPath: string): Promise<void> {
    try {
      Logger.info('SmartSync', 'إنشاء ملفات التكوين ونسخ قاعدة البيانات...')

      // 1. نسخ قاعدة البيانات الحالية إلى المجلد المشترك
      await this.copyDatabaseToSharedFolder(folderPath)

      // 1.1. التبديل إلى قاعدة البيانات المشتركة
      const sharedDbPath = path.join(folderPath, 'database.db')
      Logger.info('SmartSync', '🔄 التبديل إلى قاعدة البيانات المشتركة...')

      try {
        if (this.databaseService.switchToSharedDatabase) {
          const switchSuccess = await this.databaseService.switchToSharedDatabase(sharedDbPath)
          if (!switchSuccess) {
            Logger.error('SmartSync', '❌ فشل في التبديل إلى قاعدة البيانات المشتركة')
            throw new Error('فشل في التبديل إلى قاعدة البيانات المشتركة')
          }
        } else {
          // استخدام طريقة بديلة للتبديل
          await this.databaseService.setDatabasePath(sharedDbPath)
          Logger.success('SmartSync', '✅ تم التبديل إلى قاعدة البيانات المشتركة')
        }
      } catch (switchError) {
        Logger.error('SmartSync', '❌ خطأ في التبديل إلى قاعدة البيانات المشتركة:', switchError)
        throw new Error('فشل في التبديل إلى قاعدة البيانات المشتركة')
      }

      Logger.success('SmartSync', '✅ تم التبديل إلى قاعدة البيانات المشتركة بنجاح')

      // 2. ملف معلومات المجلد
      const infoFile = path.join(folderPath, 'folder-info.json')
      const folderInfo = {
        createdBy: os.hostname(),
        createdAt: new Date().toISOString(),
        purpose: 'ZET.IA Database Sync Folder',
        version: '1.0.0',
        deviceRole: 'main',
        networkPath: `\\\\${os.hostname()}\\${this.config.folderName}`,
        databaseFile: 'database.db'
      }
      fs.writeFileSync(infoFile, JSON.stringify(folderInfo, null, 2))
      Logger.success('SmartSync', '✅ تم إنشاء ملف معلومات المجلد')

      // 3. ملف تعليمات
      const readmeFile = path.join(folderPath, 'README.txt')
      const readmeContent = `
مجلد مزامنة ZET.IA
==================

هذا المجلد مخصص لمزامنة بيانات تطبيق ZET.IA بين الأجهزة المختلفة.

الملفات الموجودة:
- database.db: قاعدة البيانات المشتركة (الملف الرئيسي)
- folder-info.json: معلومات المجلد والجهاز المنشئ
- devices.json: قائمة الأجهزة المتصلة
- sync-log.txt: سجل عمليات المزامنة
- README.txt: هذا الملف

معلومات الشبكة:
- اسم الجهاز الرئيسي: ${os.hostname()}
- مسار الشبكة: \\\\${os.hostname()}\\${this.config.folderName}
- تاريخ الإنشاء: ${new Date().toLocaleString('ar')}

تعليمات للأجهزة الفرعية:
1. تأكد من أن جهازك متصل بنفس الشبكة
2. استخدم المسار: \\\\${os.hostname()}\\${this.config.folderName}
3. تأكد من تفعيل مشاركة الملفات في Windows

ملاحظات مهمة:
- لا تحذف أو تعدل ملف database.db يدوياً
- لا تنسخ ملفات أخرى إلى هذا المجلد
- تأكد من وجود مساحة كافية على القرص الصلب

تحذير: لا تقم بحذف أو تعديل الملفات في هذا المجلد يدوياً.

تم الإنشاء بواسطة: ${os.hostname()}
التاريخ: ${new Date().toLocaleString('ar')}
`
      fs.writeFileSync(readmeFile, readmeContent)

      // ملف سجل المزامنة
      const logFile = path.join(folderPath, 'sync-log.txt')
      const logEntry = `[${new Date().toISOString()}] تم إنشاء المجلد المشترك بواسطة ${os.hostname()}\n`
      fs.writeFileSync(logFile, logEntry)

      // 4. إنشاء ملف قائمة الأجهزة
      const devicesFile = path.join(folderPath, 'devices.json')
      const devicesInfo = {
        mainDevice: {
          hostname: os.hostname(),
          ip: await this.getLocalIP(),
          createdAt: new Date().toISOString(),
          role: 'main'
        },
        connectedDevices: [],
        lastUpdated: new Date().toISOString()
      }
      fs.writeFileSync(devicesFile, JSON.stringify(devicesInfo, null, 2))
      Logger.success('SmartSync', '✅ تم إنشاء ملف قائمة الأجهزة')

      // 5. إنشاء ملف سجل المزامنة
      const syncLogFile = path.join(folderPath, 'sync-log.txt')
      const initialLogEntry = `[${new Date().toLocaleString('ar')}] تم إنشاء المجلد المشترك بواسطة ${os.hostname()}\n`
      fs.writeFileSync(syncLogFile, initialLogEntry)
      Logger.success('SmartSync', '✅ تم إنشاء ملف سجل المزامنة')

      Logger.success('SmartSync', 'تم إنشاء ملفات التكوين الأساسية')

    } catch (error) {
      Logger.error('SmartSync', 'خطأ في إنشاء ملفات التكوين:', error)
      throw error
    }
  }

  /**
   * نسخ قاعدة البيانات الحالية إلى المجلد المشترك
   */
  private async copyDatabaseToSharedFolder(folderPath: string): Promise<void> {
    try {
      Logger.info('SmartSync', 'نسخ قاعدة البيانات إلى المجلد المشترك...')

      // الحصول على مسار قاعدة البيانات الحالية
      let currentDbPath: string

      try {
        if (this.databaseService.getDatabasePath) {
          currentDbPath = this.databaseService.getDatabasePath()
        } else if (this.databaseService.dbPath) {
          currentDbPath = this.databaseService.dbPath
        } else {
          // مسار افتراضي
          currentDbPath = path.join(process.cwd(), 'database.db')
        }
      } catch (error) {
        Logger.warn('SmartSync', 'لا يمكن الحصول على مسار قاعدة البيانات، استخدام المسار الافتراضي')
        currentDbPath = path.join(process.cwd(), 'database.db')
      }

      if (!fs.existsSync(currentDbPath)) {
        throw new Error(`قاعدة البيانات الحالية غير موجودة: ${currentDbPath}`)
      }

      // مسار قاعدة البيانات في المجلد المشترك
      const sharedDbPath = path.join(folderPath, 'database.db')

      // التحقق من وجود قاعدة بيانات مشتركة والتعامل معها بحذر
      if (fs.existsSync(sharedDbPath)) {
        Logger.warn('SmartSync', '⚠️ قاعدة بيانات مشتركة موجودة بالفعل!')

        // مقارنة أحجام الملفات لتحديد الأحدث
        const currentSize = fs.statSync(currentDbPath).size
        const sharedSize = fs.statSync(sharedDbPath).size
        const currentModified = fs.statSync(currentDbPath).mtime
        const sharedModified = fs.statSync(sharedDbPath).mtime

        Logger.info('SmartSync', `قاعدة البيانات المحلية: ${Math.round(currentSize/1024)} KB، آخر تعديل: ${currentModified.toLocaleString()}`)
        Logger.info('SmartSync', `قاعدة البيانات المشتركة: ${Math.round(sharedSize/1024)} KB، آخر تعديل: ${sharedModified.toLocaleString()}`)

        // إنشاء نسخة احتياطية من القاعدة المشتركة
        const backupPath = path.join(folderPath, `database-shared-backup-${Date.now()}.db`)
        fs.copyFileSync(sharedDbPath, backupPath)
        Logger.info('SmartSync', `تم إنشاء نسخة احتياطية من القاعدة المشتركة: ${backupPath}`)

        // إذا كانت القاعدة المشتركة أحدث، لا نستبدلها
        if (sharedModified > currentModified) {
          Logger.warn('SmartSync', '⚠️ قاعدة البيانات المشتركة أحدث من المحلية - سيتم استخدام المشتركة')
          return // لا نستبدل القاعدة المشتركة
        }

        Logger.info('SmartSync', '📝 سيتم استبدال قاعدة البيانات المشتركة بالمحلية الأحدث')
      }

      // حفظ قاعدة البيانات الحالية قبل النسخ لضمان أحدث البيانات
      try {
        if (this.databaseService.saveDatabase) {
          this.databaseService.saveDatabase()
          Logger.info('SmartSync', '💾 تم حفظ قاعدة البيانات الحالية قبل النسخ')
        } else {
          Logger.info('SmartSync', '💾 لا توجد حاجة لحفظ قاعدة البيانات (تحفظ تلقائياً)')
        }
      } catch (saveError) {
        Logger.warn('SmartSync', 'تحذير: فشل في حفظ قاعدة البيانات قبل النسخ:', saveError)
      }

      // نسخ قاعدة البيانات
      fs.copyFileSync(currentDbPath, sharedDbPath)
      Logger.success('SmartSync', `✅ تم نسخ قاعدة البيانات إلى: ${sharedDbPath}`)

      // التحقق من سلامة النسخة
      const originalSize = fs.statSync(currentDbPath).size
      const copiedSize = fs.statSync(sharedDbPath).size

      if (originalSize !== copiedSize) {
        throw new Error(`حجم الملف المنسوخ لا يطابق الأصل. الأصل: ${originalSize}, المنسوخ: ${copiedSize}`)
      }

      Logger.success('SmartSync', '✅ تم التحقق من سلامة نسخ قاعدة البيانات')
      Logger.info('SmartSync', `📊 حجم قاعدة البيانات المنسوخة: ${Math.round(copiedSize / 1024)} KB`)

      // إعداد صلاحيات الملف
      try {
        await execAsync(`icacls "${sharedDbPath}" /grant Everyone:F`, { timeout: 5000 })
        Logger.success('SmartSync', '✅ تم إعداد صلاحيات قاعدة البيانات المشتركة')
      } catch (permError) {
        Logger.warn('SmartSync', 'تحذير: فشل في إعداد صلاحيات قاعدة البيانات:', permError)
      }

    } catch (error) {
      Logger.error('SmartSync', 'خطأ في نسخ قاعدة البيانات:', error)
      throw error
    }
  }

  /**
   * اختبار المجلد المشترك والتحقق من قاعدة البيانات
   */
  private async testSharedFolder(folderPath: string): Promise<{ success: boolean; message: string }> {
    try {
      Logger.info('SmartSync', 'اختبار المجلد المشترك...')

      // 1. اختبار الكتابة والقراءة
      const testFile = path.join(folderPath, 'test-access.tmp')
      fs.writeFileSync(testFile, 'test-content')

      const content = fs.readFileSync(testFile, 'utf8')
      if (content !== 'test-content') {
        throw new Error('فشل في اختبار القراءة والكتابة')
      }

      // حذف الملف التجريبي
      fs.unlinkSync(testFile)
      Logger.success('SmartSync', '✅ اختبار الكتابة والقراءة نجح')

      // 2. التحقق من وجود قاعدة البيانات
      const dbPath = path.join(folderPath, 'database.db')
      if (!fs.existsSync(dbPath)) {
        throw new Error('قاعدة البيانات غير موجودة في المجلد المشترك')
      }

      // 3. التحقق من حجم قاعدة البيانات
      const dbStats = fs.statSync(dbPath)
      if (dbStats.size === 0) {
        throw new Error('قاعدة البيانات فارغة')
      }

      Logger.success('SmartSync', `✅ قاعدة البيانات موجودة وصالحة (${dbStats.size} بايت)`)

      // 4. التحقق من وجود ملفات التكوين الأساسية
      const requiredFiles = ['folder-info.json', 'README.txt', 'devices.json']
      for (const fileName of requiredFiles) {
        const filePath = path.join(folderPath, fileName)
        if (!fs.existsSync(filePath)) {
          Logger.warn('SmartSync', `تحذير: الملف ${fileName} غير موجود`)
        }
      }

      // 5. اختبار صلاحيات الشبكة (إذا كان مسار شبكة)
      if (folderPath.startsWith('\\\\')) {
        Logger.info('SmartSync', 'اختبار الوصول عبر الشبكة...')
        // محاولة إنشاء ملف تجريبي للتأكد من صلاحيات الشبكة
        const networkTestFile = path.join(folderPath, 'network-test.tmp')
        fs.writeFileSync(networkTestFile, 'network-test')
        fs.unlinkSync(networkTestFile)
        Logger.success('SmartSync', '✅ الوصول عبر الشبكة يعمل بشكل صحيح')
      }

      return {
        success: true,
        message: `تم اختبار المجلد المشترك بنجاح. قاعدة البيانات موجودة وصالحة (${Math.round(dbStats.size / 1024)} KB)`
      }

    } catch (error: any) {
      Logger.error('SmartSync', 'فشل في اختبار المجلد المشترك:', error)
      return {
        success: false,
        message: `فشل في اختبار المجلد المشترك: ${error.message}`
      }
    }
  }

  /**
   * البحث التلقائي عن الأجهزة المتصلة (محسن مع MAC Address)
   */
  async discoverConnectedDevices(): Promise<{ ip: string; hostname?: string; mac?: string; isReachable: boolean; hasSharedFolder?: boolean }[]> {
    try {
      Logger.info('SmartSync', 'البحث عن الأجهزة المتصلة (البحث محسن)...')

      // محاولة البحث مع إعادة المحاولة
      for (let attempt = 1; attempt <= 2; attempt++) {
        Logger.info('SmartSync', `محاولة البحث ${attempt}/2...`)

        // 1. البحث عبر ARP table أولاً (أسرع وأدق)
        const arpDevices = await this.discoverDevicesViaARP()

        if (arpDevices.length > 0) {
          Logger.success('SmartSync', `تم العثور على ${arpDevices.length} جهاز عبر ARP table`)

          // فحص إمكانية الوصول للأجهزة المكتشفة
          const reachableDevices = await this.verifyDevicesReachability(arpDevices)

          if (reachableDevices.length > 0) {
            // فحص الأجهزة للبحث عن ملفات مشتركة
            const devicesWithSharedInfo = await this.checkDevicesForSharedFolders(reachableDevices)
            if (devicesWithSharedInfo.length > 0) {
              return devicesWithSharedInfo
            }
          }
        }

        // 2. إذا فشل ARP، نستخدم الطريقة التقليدية
        Logger.info('SmartSync', 'البحث التقليدي عبر ping...')
        const pingDevices = await this.discoverDevicesViaPing()

        if (pingDevices.length > 0) {
          // فحص الأجهزة للبحث عن ملفات مشتركة
          const devicesWithSharedInfo = await this.checkDevicesForSharedFolders(pingDevices)
          if (devicesWithSharedInfo.length > 0) {
            return devicesWithSharedInfo
          }
        }

        // إذا لم نجد شيء في المحاولة الأولى، انتظر قليلاً
        if (attempt === 1) {
          Logger.info('SmartSync', 'لم يتم العثور على أجهزة، إعادة المحاولة خلال 3 ثواني...')
          await new Promise(resolve => setTimeout(resolve, 3000))
        }
      }

      Logger.warn('SmartSync', 'لم يتم العثور على أي أجهزة بعد محاولتين')
      return []

    } catch (error) {
      Logger.error('SmartSync', 'خطأ في البحث عن الأجهزة:', error)
      throw new Error(`فشل في البحث عن الأجهزة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    }
  }

  /**
   * فحص الأجهزة للبحث عن ملفات مشتركة
   */
  private async checkDevicesForSharedFolders(devices: { ip: string; hostname?: string; mac?: string; isReachable: boolean }[]): Promise<{ ip: string; hostname?: string; mac?: string; isReachable: boolean; hasSharedFolder?: boolean }[]> {
    const devicesWithSharedInfo = []

    for (const device of devices) {
      if (!device.isReachable) {
        devicesWithSharedInfo.push({ ...device, hasSharedFolder: false })
        continue
      }

      try {
        Logger.debug('SmartSync', `فحص الجهاز ${device.ip} للبحث عن ملفات مشتركة...`)

        // قائمة أسماء المجلدات المشتركة المحتملة
        const possibleSharedNames = [
          'ZET.IA-Shared',
          'ZETIA-Shared',
          'ZETIAShared',
          'Accounting-Shared',
          'AccountingShared'
        ]

        let hasSharedFolder = false

        // البحث عن المجلدات المشتركة
        for (const shareName of possibleSharedNames) {
          const sharedPath = `\\\\${device.ip}\\${shareName}`

          try {
            if (fs.existsSync(sharedPath)) {
              // التحقق من وجود ملفات قاعدة البيانات
              const files = fs.readdirSync(sharedPath)
              const dbFiles = files.filter(file =>
                file.endsWith('.db') || file.endsWith('.sqlite') || file.endsWith('.sqlite3')
              )

              if (dbFiles.length > 0) {
                hasSharedFolder = true
                Logger.success('SmartSync', `✅ تم العثور على مجلد مشترك مع قاعدة بيانات في ${device.ip}\\${shareName}`)
                break
              }
            }
          } catch (accessError) {
            // تجاهل أخطاء الوصول وتابع البحث
            Logger.debug('SmartSync', `لا يمكن الوصول للمسار ${sharedPath}`)
          }
        }

        devicesWithSharedInfo.push({ ...device, hasSharedFolder })

      } catch (error) {
        Logger.debug('SmartSync', `خطأ في فحص الجهاز ${device.ip}:`, error)
        devicesWithSharedInfo.push({ ...device, hasSharedFolder: false })
      }
    }

    // ترتيب الأجهزة: الأجهزة التي تحتوي على ملفات مشتركة أولاً
    devicesWithSharedInfo.sort((a, b) => {
      if (a.hasSharedFolder && !b.hasSharedFolder) return -1
      if (!a.hasSharedFolder && b.hasSharedFolder) return 1
      return 0
    })

    const sharedDevicesCount = devicesWithSharedInfo.filter(d => d.hasSharedFolder).length
    if (sharedDevicesCount > 0) {
      Logger.success('SmartSync', `🎉 تم العثور على ${sharedDevicesCount} جهاز يحتوي على ملفات مشتركة`)
    }

    return devicesWithSharedInfo
  }

  /**
   * اكتشاف الأجهزة عبر ARP table (أسرع وأدق)
   */
  private async discoverDevicesViaARP(): Promise<{ ip: string; hostname?: string; mac?: string; isReachable: boolean }[]> {
    try {
      Logger.info('SmartSync', 'البحث عبر ARP table...')

      const { stdout } = await execAsync('arp -a', { timeout: 10000 })
      const devices: { ip: string; hostname?: string; mac?: string; isReachable: boolean }[] = []

      const lines = stdout.split('\n')

      for (const line of lines) {
        // تحليل خط ARP: IP address MAC address Type
        // نمط Windows: "  ***********           9c-63-5b-f6-54-36     dynamic"
        const match = line.match(/\s*(\d+\.\d+\.\d+\.\d+)\s+([0-9a-f]{2}-[0-9a-f]{2}-[0-9a-f]{2}-[0-9a-f]{2}-[0-9a-f]{2}-[0-9a-f]{2})\s+(\w+)/i)

        if (match) {
          const ip = match[1]
          const mac = match[2].toLowerCase()
          const type = match[3].toLowerCase()

          // تجاهل العناوين المحلية والبث
          if (ip.startsWith('127.') || ip.startsWith('224.') || ip.startsWith('255.') || ip.startsWith('239.')) {
            continue
          }

          // إضافة الجهاز مع معلومات MAC
          devices.push({
            ip,
            mac,
            isReachable: type === 'dynamic', // الأجهزة النشطة عادة تكون dynamic
            hostname: undefined // سيتم الحصول عليه لاحقاً
          })

          Logger.info('SmartSync', `🔍 ARP: ${ip} → ${mac} (${type})`)
        }
      }

      Logger.success('SmartSync', `تم اكتشاف ${devices.length} جهاز عبر ARP`)
      return devices

    } catch (error) {
      Logger.warn('SmartSync', 'فشل في البحث عبر ARP:', error)
      return []
    }
  }

  /**
   * التحقق من إمكانية الوصول للأجهزة المكتشفة
   */
  private async verifyDevicesReachability(devices: { ip: string; hostname?: string; mac?: string; isReachable: boolean }[]): Promise<{ ip: string; hostname?: string; mac?: string; isReachable: boolean }[]> {
    Logger.info('SmartSync', `التحقق من إمكانية الوصول لـ ${devices.length} جهاز...`)

    const promises = devices.map(device => this.pingDeviceWithMAC(device.ip, device.mac))
    const results = await Promise.all(promises)

    const reachableDevices = results.filter(device => device.isReachable)
    Logger.success('SmartSync', `${reachableDevices.length} جهاز متاح من أصل ${devices.length}`)

    return reachableDevices
  }

  /**
   * البحث التقليدي عبر ping (fallback)
   */
  private async discoverDevicesViaPing(): Promise<{ ip: string; hostname?: string; mac?: string; isReachable: boolean }[]> {
    const devices: { ip: string; hostname?: string; mac?: string; isReachable: boolean }[] = []

    // الحصول على عنوان IP الحالي لتحديد نطاق الشبكة
    const networkInfo = await this.getNetworkInfo()
    const currentIP = networkInfo.ip

    if (currentIP === 'localhost' || !currentIP.includes('.')) {
      Logger.warn('SmartSync', 'لا يمكن تحديد نطاق الشبكة')
      return []
    }

    // تحديد نطاق الشبكة (مثل 192.168.1.x)
    const ipParts = currentIP.split('.')
    const networkBase = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}`

    Logger.info('SmartSync', `البحث السريع في نطاق الشبكة: ${networkBase}.x`)

    // البحث في النطاق المحدود (أول 10 عناوين + عناوين شائعة)
    const commonIPs = [1, 2, 3, 4, 5, 10, 20, 100, 101, 254] // عناوين IP شائعة
    const promises: Promise<{ ip: string; hostname?: string; mac?: string; isReachable: boolean }>[] = []

    for (const ip of commonIPs) {
      const targetIP = `${networkBase}.${ip}`

      // تخطي IP الحالي
      if (targetIP === currentIP) continue

      promises.push(this.pingDeviceWithMAC(targetIP))
    }

    Logger.info('SmartSync', `فحص ${promises.length} عنوان IP محتمل...`)

    // تنفيذ البحث بشكل متوازي (مجموعات من 5 للسرعة)
    const batchSize = 5
    let checkedCount = 0

    for (let i = 0; i < promises.length; i += batchSize) {
      const batch = promises.slice(i, i + batchSize)
      const results = await Promise.all(batch)

      // إضافة الأجهزة المتاحة فقط
      results.forEach(result => {
        checkedCount++
        if (result.isReachable) {
          devices.push(result)
          Logger.info('SmartSync', `✅ تم العثور على جهاز: ${result.ip} (${result.hostname || 'غير معروف'}) [${result.mac || 'MAC غير معروف'}]`)
        }
      })

      // توقف قصير بين المجموعات
      if (i + batchSize < promises.length) {
        await new Promise(resolve => setTimeout(resolve, 50))
      }
    }

    Logger.success('SmartSync', `تم فحص ${checkedCount} عنوان IP وتم العثور على ${devices.length} جهاز متاح`)

    // إذا لم نجد أجهزة، نحاول البحث الموسع
    if (devices.length === 0) {
      Logger.info('SmartSync', 'لم يتم العثور على أجهزة في البحث السريع، محاولة البحث الموسع...')
      return await this.extendedDeviceSearch(networkBase, currentIP)
    }

    return devices
  }

  /**
   * البحث الموسع عن الأجهزة (إذا فشل البحث السريع)
   */
  private async extendedDeviceSearch(networkBase: string, currentIP: string): Promise<{ ip: string; hostname?: string; mac?: string; isReachable: boolean }[]> {
    const devices: { ip: string; hostname?: string; mac?: string; isReachable: boolean }[] = []
    const promises: Promise<{ ip: string; hostname?: string; mac?: string; isReachable: boolean }>[] = []

    // البحث في نطاق أوسع (50 عنوان IP)
    for (let i = 1; i <= 50; i++) {
      const targetIP = `${networkBase}.${i}`
      if (targetIP === currentIP) continue
      promises.push(this.pingDeviceWithMAC(targetIP))
    }

    Logger.info('SmartSync', `البحث الموسع: فحص ${promises.length} عنوان IP إضافي...`)

    // تنفيذ البحث بمجموعات صغيرة
    const batchSize = 10
    for (let i = 0; i < promises.length; i += batchSize) {
      const batch = promises.slice(i, i + batchSize)
      const results = await Promise.all(batch)

      results.forEach(result => {
        if (result.isReachable) {
          devices.push(result)
          Logger.info('SmartSync', `✅ تم العثور على جهاز: ${result.ip} (${result.hostname || 'غير معروف'}) [${result.mac || 'MAC غير معروف'}]`)
        }
      })

      // توقف بين المجموعات
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    Logger.success('SmartSync', `البحث الموسع: تم العثور على ${devices.length} جهاز إضافي`)
    return devices
  }

  /**
   * فحص إمكانية الوصول لجهاز معين مع MAC Address (محسن للسرعة)
   */
  private async pingDeviceWithMAC(ip: string, knownMAC?: string): Promise<{ ip: string; hostname?: string; mac?: string; isReachable: boolean }> {
    try {
      // استخدام ping مع timeout محسن للموثوقية
      const { stdout } = await execAsync(`ping -n 2 -w 2000 ${ip}`, { timeout: 5000 })

      // فحص أكثر دقة لنتيجة ping
      const isReachable = stdout.includes('TTL=') ||
                         stdout.includes('time<') ||
                         stdout.includes('time=') ||
                         (stdout.includes('Reply from') && !stdout.includes('Destination host unreachable'))

      let hostname: string | undefined
      let mac: string | undefined = knownMAC

      if (isReachable) {
        // الحصول على اسم الجهاز
        try {
          // محاولة سريعة للحصول على اسم الجهاز
          const { stdout: hostnameOutput } = await execAsync(`nslookup ${ip}`, { timeout: 800 })

          // البحث عن اسم الجهاز في النتيجة
          const nameMatch = hostnameOutput.match(/Name:\s*([^\r\n]+)/)
          if (nameMatch) {
            hostname = nameMatch[1].trim()
          } else {
            // محاولة أخرى للعثور على الاسم
            const altMatch = hostnameOutput.match(/([a-zA-Z0-9-.]+)\s+/)
            if (altMatch && !altMatch[1].includes(ip)) {
              hostname = altMatch[1].trim()
            }
          }
        } catch {
          // في حالة فشل nslookup، نحاول طريقة أخرى
          try {
            const { stdout: netOutput } = await execAsync(`nbtstat -A ${ip}`, { timeout: 500 })
            const netMatch = netOutput.match(/([A-Z0-9-]+)\s+<00>\s+UNIQUE/)
            if (netMatch) {
              hostname = netMatch[1].trim()
            }
          } catch {
            // تجاهل أخطاء nbtstat أيضاً
          }
        }

        // الحصول على MAC Address إذا لم يكن معروفاً
        if (!mac) {
          try {
            const { stdout: arpOutput } = await execAsync(`arp -a`, { timeout: 500 })
            const lines = arpOutput.split('\n')

            for (const line of lines) {
              // البحث عن السطر الذي يحتوي على IP المطلوب
              if (line.includes(ip)) {
                // نمط Windows: "  ***********           9c-63-5b-f6-54-36     dynamic"
                const macMatch = line.match(/([0-9a-f]{2}-[0-9a-f]{2}-[0-9a-f]{2}-[0-9a-f]{2}-[0-9a-f]{2}-[0-9a-f]{2})/i)
                if (macMatch) {
                  mac = macMatch[1].toLowerCase()
                  Logger.info('SmartSync', `🔗 MAC لـ ${ip}: ${mac}`)
                  break
                }
              }
            }
          } catch (error) {
            Logger.warn('SmartSync', `خطأ في الحصول على MAC لـ ${ip}:`, error)
          }
        }
      }

      return { ip, hostname, mac, isReachable }

    } catch (error) {
      // في حالة timeout أو خطأ، نعتبر الجهاز غير متاح
      return { ip, mac: knownMAC, isReachable: false }
    }
  }



  /**
   * الحصول على معلومات الشبكة
   */
  async getNetworkInfo(): Promise<{ hostname: string; ip: string; mac?: string; workgroup?: string }> {
    try {
      const hostname = os.hostname()

      // الحصول على عنوان IP و MAC
      const networkInterfaces = os.networkInterfaces()
      let ip = 'localhost'
      let mac: string | undefined

      for (const interfaceName in networkInterfaces) {
        const addresses = networkInterfaces[interfaceName]
        if (addresses) {
          for (const addr of addresses) {
            if (addr.family === 'IPv4' && !addr.internal) {
              ip = addr.address
              mac = addr.mac
              break
            }
          }
        }
      }

      return { hostname, ip, mac }

    } catch (error) {
      Logger.error('SmartSync', 'خطأ في الحصول على معلومات الشبكة:', error)
      return { hostname: 'unknown', ip: 'localhost' }
    }
  }

  /**
   * تحديد نوع الجهاز في الشبكة (مضيف أم عميل) بذكاء
   */
  async determineDeviceRole(): Promise<'host' | 'client'> {
    try {
      Logger.info('SmartSync', 'تحديد دور الجهاز في الشبكة...')

      // 1. فحص وجود مجلد مشترك محلي نشط
      const localSharedFolder = await this.findLocalSharedFolder()
      if (localSharedFolder) {
        Logger.info('SmartSync', `✅ جهاز مضيف - يحتوي على مجلد مشترك: ${localSharedFolder}`)
        return 'host'
      }

      // 2. البحث عن مجلدات مشتركة في الشبكة
      const networkSharedFolders = await this.findSharedFoldersInNetwork()
      if (networkSharedFolders.length > 0) {
        Logger.info('SmartSync', `✅ جهاز عميل - تم العثور على ${networkSharedFolders.length} مجلد مشترك في الشبكة`)
        return 'client'
      }

      // 3. فحص الأولوية بناءً على خصائص الجهاز
      const devicePriority = await this.calculateDevicePriority()
      const networkDevices = await this.discoverConnectedDevices()

      if (networkDevices.length === 0) {
        Logger.info('SmartSync', '🏠 جهاز وحيد في الشبكة - سيكون مضيف')
        return 'host'
      }

      // 4. مقارنة الأولوية مع الأجهزة الأخرى
      let shouldBeHost = true
      for (const device of networkDevices) {
        if (device.isReachable) {
          const remotePriority = await this.getRemoteDevicePriority(device.ip)
          if (remotePriority > devicePriority) {
            shouldBeHost = false
            break
          }
        }
      }

      const role = shouldBeHost ? 'host' : 'client'
      Logger.info('SmartSync', `🎯 تم تحديد دور الجهاز: ${role} (أولوية: ${devicePriority})`)

      return role

    } catch (error) {
      Logger.error('SmartSync', 'خطأ في تحديد نوع الجهاز:', error)
      return 'host' // افتراضي - أفضل أن يكون مضيف في حالة الخطأ
    }
  }

  /**
   * البحث عن مجلد مشترك محلي نشط
   */
  private async findLocalSharedFolder(): Promise<string | null> {
    try {
      const locations = await this.getSuggestedLocations()

      for (const location of locations) {
        const sharedPath = path.join(location.basePath, this.config.folderName)

        if (fs.existsSync(sharedPath)) {
          // فحص ما إذا كان هذا المجلد مشارك بالفعل
          const isShared = await this.isFolderShared(sharedPath)

          if (isShared) {
            return sharedPath
          }
        }
      }

      return null
    } catch (error) {
      Logger.warn('SmartSync', 'خطأ في البحث عن مجلد مشترك محلي:', error)
      return null
    }
  }

  /**
   * حساب أولوية الجهاز (كلما زادت الأولوية، كلما كان أولى ليكون مضيف)
   */
  private async calculateDevicePriority(): Promise<number> {
    try {
      let priority = 0

      // 1. نوع نظام التشغيل (Windows Server أولوية أعلى)
      const platform = os.platform()
      if (platform === 'win32') {
        try {
          const { stdout } = await execAsync('wmic os get caption', { timeout: 3000 })
          if (stdout.toLowerCase().includes('server')) {
            priority += 100 // Windows Server
          } else {
            priority += 50 // Windows Desktop
          }
        } catch {
          priority += 50
        }
      } else {
        priority += 30 // Linux/Mac
      }

      // 2. مساحة القرص المتاحة
      try {
        const { stdout } = await execAsync('wmic logicaldisk get size,freespace,caption', { timeout: 3000 })
        const lines = stdout.split('\n')
        let maxFreeSpace = 0

        for (const line of lines) {
          const match = line.match(/(\w:)\s+(\d+)\s+(\d+)/)
          if (match) {
            const freeSpace = parseInt(match[2]) || 0
            maxFreeSpace = Math.max(maxFreeSpace, freeSpace)
          }
        }

        // إضافة نقاط بناءً على المساحة المتاحة (GB)
        priority += Math.min(50, Math.floor(maxFreeSpace / (1024 * 1024 * 1024)))
      } catch {
        priority += 10 // افتراضي
      }

      // 3. ذاكرة النظام
      const totalMemory = os.totalmem()
      priority += Math.min(30, Math.floor(totalMemory / (1024 * 1024 * 1024))) // GB

      // 4. عدد المعالجات
      const cpuCount = os.cpus().length
      priority += Math.min(20, cpuCount * 2)

      // 5. وقت تشغيل النظام (الأجهزة التي تعمل لفترة أطول أولوية أعلى)
      const uptime = os.uptime()
      if (uptime > 86400) { // أكثر من يوم
        priority += 15
      } else if (uptime > 3600) { // أكثر من ساعة
        priority += 5
      }

      Logger.info('SmartSync', `أولوية الجهاز المحسوبة: ${priority}`)
      return priority

    } catch (error) {
      Logger.warn('SmartSync', 'خطأ في حساب أولوية الجهاز:', error)
      return 50 // افتراضي
    }
  }

  /**
   * الحصول على أولوية جهاز بعيد (تقدير)
   */
  private async getRemoteDevicePriority(ip: string): Promise<number> {
    try {
      // محاولة الحصول على معلومات أساسية عن الجهاز البعيد
      let priority = 50 // افتراضي

      // فحص نوع نظام التشغيل عبر ping
      try {
        const { stdout } = await execAsync(`ping -n 1 ${ip}`, { timeout: 2000 })
        if (stdout.includes('TTL=128')) {
          priority += 50 // Windows
        } else if (stdout.includes('TTL=64')) {
          priority += 30 // Linux/Mac
        }
      } catch {
        // تجاهل أخطاء ping
      }

      // فحص وجود مشاركات (يدل على أنه server)
      try {
        const { stdout } = await execAsync(`net view \\\\${ip}`, { timeout: 3000 })
        if (stdout.includes('shared successfully')) {
          priority += 30 // لديه مشاركات نشطة
        }
      } catch {
        // تجاهل أخطاء net view
      }

      return priority

    } catch (error) {
      Logger.warn('SmartSync', `خطأ في تقدير أولوية الجهاز ${ip}:`, error)
      return 50 // افتراضي
    }
  }

  /**
   * فحص ما إذا كان المجلد مشارك
   */
  private async isFolderShared(folderPath: string): Promise<boolean> {
    try {
      const { stdout } = await execAsync('net share', { timeout: 5000 })
      return stdout.includes(folderPath)
    } catch {
      return false
    }
  }

  /**
   * البحث عن مجلدات مشتركة في الشبكة
   */
  private async findSharedFoldersInNetwork(): Promise<string[]> {
    try {
      const devices = await this.discoverConnectedDevices()
      const sharedFolders: string[] = []

      for (const device of devices) {
        if (device.isReachable) {
          try {
            // البحث عن مشاركات في الجهاز
            const { stdout } = await execAsync(`net view \\\\${device.ip}`, { timeout: 3000 })

            if (stdout.includes(this.config.folderName)) {
              const sharePath = `\\\\${device.ip}\\${this.config.folderName}`
              sharedFolders.push(sharePath)
              Logger.info('SmartSync', `تم العثور على مجلد مشترك: ${sharePath}`)
            }
          } catch {
            // تجاهل أخطاء الوصول للأجهزة
          }
        }
      }

      return sharedFolders
    } catch (error) {
      Logger.warn('SmartSync', 'خطأ في البحث عن مجلدات مشتركة:', error)
      return []
    }
  }

  /**
   * الحصول على عنوان IP المحلي
   */
  private async getLocalIP(): Promise<string> {
    try {
      const networkInterfaces = os.networkInterfaces()

      // البحث عن عنوان IP الصحيح (غير loopback وليس virtual)
      for (const interfaceName in networkInterfaces) {
        const interfaces = networkInterfaces[interfaceName]
        if (interfaces) {
          for (const iface of interfaces) {
            // تجاهل loopback و virtual interfaces
            if (iface.family === 'IPv4' &&
                !iface.internal &&
                !interfaceName.toLowerCase().includes('virtual') &&
                !interfaceName.toLowerCase().includes('vmware') &&
                !interfaceName.toLowerCase().includes('vbox')) {
              return iface.address
            }
          }
        }
      }

      // إذا لم نجد عنوان مناسب، نعيد localhost
      return '127.0.0.1'
    } catch (error) {
      Logger.warn('SmartSync', 'خطأ في الحصول على عنوان IP المحلي:', error)
      return '127.0.0.1'
    }
  }
}
