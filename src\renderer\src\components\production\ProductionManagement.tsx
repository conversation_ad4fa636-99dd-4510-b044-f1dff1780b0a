import React, { useState } from 'react'
import { Card, Button, Row, Col, Typography } from 'antd'
// import { ProductionOrderPrintButton as _ProductionOrderPrintButton } from '../common/ProductionOrderPrintButton'
import {
  ArrowLeftOutlined,
  ToolOutlined,
  BgColorsOutlined,
  FileTextOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  ExperimentOutlined,
  DashboardOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import PaintManagement from './paint/PaintManagement'
import FurnitureProduction from './furniture/FurnitureProduction'
import ProductionReports from './furniture/ProductionReports'
import ProductionDashboard from './furniture/ProductionDashboard'

// const { Title, Text } = Typography

// const StyledCard = styled(Card)`
//   margin-bottom: 16px;
//   border-radius: 8px;
//   box-shadow: 0 2px 8px rgba(0,0,0,0.1);
//
//   .ant-card-head {
//     background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
//     border-radius: 8px 8px 0 0;
//
//     .ant-card-head-title {
//       color: white;
//       font-weight: 600;
//     }
//   }
// `

const ModuleCard = styled(Card)`
  height: 200px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }
  
  .ant-card-body {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }
  
  .module-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #1890ff;
  }
  
  .module-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #262626;
  }
  
  .module-description {
    color: #666;
    font-size: 14px;
  }
`

const PaintCard = styled(ModuleCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  }
  
  .module-icon {
    color: #ff7875;
  }
`

const FurnitureCard = styled(ModuleCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }
  
  .module-icon {
    color: #52c41a;
  }
`

const ReportsCard = styled(ModuleCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
  }
  
  .module-icon {
    color: #722ed1;
  }
`

interface ProductionManagementProps {
  onBack: () => void
  initialView?: string
}

const ProductionManagement: React.FC<ProductionManagementProps> = ({ onBack, initialView = 'main' }) => {
  const [activeView, setActiveView] = useState(initialView)

  // مراقبة تغيير initialView
  React.useEffect(() => {
    setActiveView(initialView)
  }, [initialView])

  const renderContent = () => {
    switch (activeView) {
      case 'dashboard':
        return <ProductionDashboard />
      case 'paint':
        return <PaintManagement onBack={() => setActiveView('main')} />
      case 'furniture':
        return <FurnitureProduction onBack={() => setActiveView('main')} />
      case 'reports':
        return <ProductionReports onBack={() => setActiveView('main')} />
      default:
        return (
          <div style={{ padding: '24px' }}>
            <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <h1 style={{ margin: 0, color: '#1890ff', fontSize: '28px' }}>
                  <ToolOutlined style={{ marginLeft: '12px' }} />
                  إدارة الإنتاج
                </h1>
                <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '16px' }}>
                  إدارة شاملة لعمليات الإنتاج والتصنيع
                </p>
              </div>
              <Button 
                icon={<ArrowLeftOutlined />} 
                onClick={onBack}
                size="large"
                style={{ borderRadius: '8px' }}
              >
                العودة للرئيسية
              </Button>
            </div>

            <Row gutter={[24, 24]}>
              <Col xs={24} sm={12} lg={8}>
                <ModuleCard onClick={() => setActiveView('dashboard')}>
                  <div className="ant-card-body">
                    <DashboardOutlined className="module-icon" style={{ color: '#1890ff' }} />
                    <div className="module-title">لوحة التحكم</div>
                    <div className="module-description">
                      عرض شامل لحالة الإنتاج والمخزون
                      <br />
                      إحصائيات وتنبيهات فورية
                    </div>
                  </div>
                </ModuleCard>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <PaintCard onClick={() => setActiveView('paint')}>
                  <div className="ant-card-body">
                    <BgColorsOutlined className="module-icon" />
                    <div className="module-title">قسم الدهان</div>
                    <div className="module-description">
                      إدارة أوامر الدهان والأصناف غير المخزنية
                      <br />
                      حساب التكلفة: الطول × العرض × العدد × السعر
                    </div>
                  </div>
                </PaintCard>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <FurnitureCard onClick={() => setActiveView('furniture')}>
                  <div className="ant-card-body">
                    <ToolOutlined className="module-icon" />
                    <div className="module-title">إنتاج الأثاث</div>
                    <div className="module-description">
                      إدارة أوامر إنتاج الأثاث والمواد الخام
                      <br />
                      تتبع مراحل الإنتاج والتكاليف
                    </div>
                  </div>
                </FurnitureCard>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <ReportsCard onClick={() => setActiveView('reports')}>
                  <div className="ant-card-body">
                    <BarChartOutlined className="module-icon" />
                    <div className="module-title">تقارير الإنتاج</div>
                    <div className="module-description">
                      تقارير شاملة لجميع عمليات الإنتاج
                      <br />
                      (قيد التطوير)
                    </div>
                  </div>
                </ReportsCard>
              </Col>
            </Row>

            <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
              <Col xs={24} sm={12} lg={8}>
                <ModuleCard>
                  <div className="ant-card-body">
                    <ExperimentOutlined className="module-icon" />
                    <div className="module-title">مراقبة الجودة</div>
                    <div className="module-description">
                      فحص ومراقبة جودة المنتجات
                      <br />
                      (قيد التطوير)
                    </div>
                  </div>
                </ModuleCard>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <ModuleCard>
                  <div className="ant-card-body">
                    <SettingOutlined className="module-icon" />
                    <div className="module-title">إعدادات الإنتاج</div>
                    <div className="module-description">
                      إعدادات عامة لعمليات الإنتاج
                      <br />
                      (قيد التطوير)
                    </div>
                  </div>
                </ModuleCard>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <ModuleCard>
                  <div className="ant-card-body">
                    <FileTextOutlined className="module-icon" />
                    <div className="module-title">الوثائق والمواصفات</div>
                    <div className="module-description">
                      إدارة وثائق ومواصفات الإنتاج
                      <br />
                      (قيد التطوير)
                    </div>
                  </div>
                </ModuleCard>
              </Col>
            </Row>
          </div>
        )
    }
  }

  return (
    <div>
      {renderContent()}
    </div>
  )
}

export default ProductionManagement
