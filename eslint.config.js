const js = require('@eslint/js');
const tseslint = require('@typescript-eslint/eslint-plugin');
const tsparser = require('@typescript-eslint/parser');
const react = require('eslint-plugin-react');
const reactHooks = require('eslint-plugin-react-hooks');
const reactRefresh = require('eslint-plugin-react-refresh');

module.exports = [
  {
    ignores: [
      'dist/**',
      'release/**',
      'release-new/**',
      'node_modules/**',
      'cache/**',
      'logs/**',
      'tools/**',
      '*.js',
      '*.mjs',
      'vite.config.ts',
      '**/*.config.js',
      '**/*.config.ts',
      'src/renderer/public/**/*.js',
      'static/**/*.js',
      'src/renderer/public/**/*.js',
      'static/**/*.js',
    ],
  },
  js.configs.recommended,
  {
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      parser: tsparser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        // Node.js globals
        process: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        global: 'readonly',
        require: 'readonly',
        module: 'readonly',
        exports: 'readonly',
        // Jest globals
        describe: 'readonly',
        test: 'readonly',
        it: 'readonly',
        expect: 'readonly',
        beforeEach: 'readonly',
        afterEach: 'readonly',
        beforeAll: 'readonly',
        afterAll: 'readonly',
        jest: 'readonly',
        // Browser globals
        window: 'readonly',
        document: 'readonly',
        console: 'readonly',
        localStorage: 'readonly',
        sessionStorage: 'readonly',
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        setInterval: 'readonly',
        clearInterval: 'readonly',
        fetch: 'readonly',
        URL: 'readonly',
        URLSearchParams: 'readonly',
        HTMLElement: 'readonly',
        HTMLCanvasElement: 'readonly',
        HTMLFormElement: 'readonly',
        HTMLButtonElement: 'readonly',
        HTMLMediaElement: 'readonly',
        HTMLAudioElement: 'readonly',
        Notification: 'readonly',
        Audio: 'readonly',
        CustomEvent: 'readonly',
        IntersectionObserver: 'readonly',
        IntersectionObserverInit: 'readonly',
        IntersectionObserverEntry: 'readonly',
        // Electron globals
        electron: 'readonly',
        // TypeScript/Node.js types
        NodeJS: 'readonly',
        // Common globals
        Promise: 'readonly',
        Map: 'readonly',
        Set: 'readonly',
        WeakMap: 'readonly',
        WeakSet: 'readonly',
      },
    },
    plugins: {
      '@typescript-eslint': tseslint,
      'react': react,
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    rules: {
      ...tseslint.configs.recommended.rules,
      ...react.configs.recommended.rules,
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': 'off', // تعطيل تحذيرات Fast Refresh
      // قواعد محسنة لتقليل التحذيرات غير الضرورية
      '@typescript-eslint/no-unused-vars': ['warn', {
        argsIgnorePattern: '^_|^error$|^unused|^key$|^index$|^percent$|^type$|^encoding$|^values$',
        varsIgnorePattern: '^_|.*[Ee]rror.*|^unused|^React$|^[A-Z][a-zA-Z]*$',
        ignoreRestSiblings: true,
        destructuredArrayIgnorePattern: '^_',
        caughtErrors: 'none'
      }],
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-non-null-assertion': 'warn',
      '@typescript-eslint/no-require-imports': 'warn',
      '@typescript-eslint/no-var-requires': 'warn',
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',
      'react-hooks/exhaustive-deps': 'off', // تعطيل تحذيرات useEffect dependencies مؤقتاً // تخفيف من error إلى warn
      'no-console': 'off',
      'no-debugger': 'warn',
      'no-undef': 'error',
      'no-unreachable': 'warn',
      'no-unused-vars': 'off',
      'prefer-const': 'warn',
      'no-var': 'warn',
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  },

  {
    files: ['src/main/**/*.ts'],
    languageOptions: {
      globals: {
        // إضافة Node.js globals خاصة بـ Main Process
        process: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        global: 'readonly',
        require: 'readonly',
        module: 'readonly',
        exports: 'readonly',
        console: 'readonly',
        FormData: 'readonly',
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        setInterval: 'readonly',
        clearInterval: 'readonly',
        NodeJS: 'readonly',
      },
    },
    rules: {
      // قواعد خاصة بـ Electron Main Process
      'no-console': 'off',
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/no-require-imports': 'off',
      'no-undef': 'error',
    },
  },
  {
    files: ['src/renderer/**/*.{ts,tsx}'],
    languageOptions: {
      globals: {
        // Browser/Renderer globals
        window: 'readonly',
        document: 'readonly',
        console: 'readonly',
        localStorage: 'readonly',
        sessionStorage: 'readonly',
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        setInterval: 'readonly',
        clearInterval: 'readonly',
        fetch: 'readonly',
        URL: 'readonly',
        URLSearchParams: 'readonly',
        HTMLElement: 'readonly',
        HTMLInputElement: 'readonly',
        HTMLDivElement: 'readonly',
        Element: 'readonly',
        Event: 'readonly',
        ErrorEvent: 'readonly',
        PromiseRejectionEvent: 'readonly',
        MouseEvent: 'readonly',
        KeyboardEvent: 'readonly',
        EventListener: 'readonly',
        EventListenerObject: 'readonly',
        EventListenerOrEventListenerObject: 'readonly',
        File: 'readonly',
        FileReader: 'readonly',
        Blob: 'readonly',
        FormData: 'readonly',
        MutationObserver: 'readonly',
        Node: 'readonly',
        NodeList: 'readonly',
        ResizeObserver: 'readonly',
        IntersectionObserver: 'readonly',
        requestAnimationFrame: 'readonly',
        cancelAnimationFrame: 'readonly',
        performance: 'readonly',
        navigator: 'readonly',
        Navigator: 'readonly',
        AudioContext: 'readonly',
        webkitAudioContext: 'readonly',
        speechSynthesis: 'readonly',
        SpeechSynthesis: 'readonly',
        SpeechSynthesisUtterance: 'readonly',
        TextEncoder: 'readonly',
        TextDecoder: 'readonly',
        Image: 'readonly',
        btoa: 'readonly',
        atob: 'readonly',
        AudioBuffer: 'readonly',
        OscillatorNode: 'readonly',
        // Electron renderer globals
        electron: 'readonly',
        // React/JSX globals
        React: 'readonly',
        JSX: 'readonly',
        // Additional globals for common variables
        error: 'readonly',
        _error: 'readonly',
        uploadFile: 'readonly',
      },
    },
    rules: {
      // قواعد خاصة بـ Renderer Process
      'no-console': 'off', // السماح بـ console في Renderer للتطوير
      '@typescript-eslint/no-var-requires': 'error',
      '@typescript-eslint/no-require-imports': 'error',
      'no-undef': 'error',
    },
  },

  // إعدادات خاصة لملفات Node.js (scripts)
  {
    files: ['scripts/**/*.js'],
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
      globals: {
        console: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        process: 'readonly',
        Buffer: 'readonly',
        global: 'readonly',
        require: 'readonly',
        module: 'readonly',
        exports: 'readonly'
      }
    },
    rules: {
      'no-console': 'off',
      'no-undef': 'off'
    },
  },
];
