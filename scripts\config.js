/**
 * تكوين السكريپتات المحسنة - ZET.IA
 * إعدادات مركزية لجميع سكريپتات البناء
 */

module.exports = {
  // إعدادات البناء الأساسية
  build: {
    // مجلدات الإخراج
    outputDirs: {
      dist: 'dist',
      portable: 'dist-portable-final',
      installer: 'release-new'
    },
    
    // إعدادات TypeScript
    typescript: {
      skipLibCheck: true,
      noEmit: true,
      strict: false
    },
    
    // إعدادات Vite
    vite: {
      mode: 'production',
      logLevel: 'warn',
      minify: 'terser'
    }
  },
  
  // إعدادات النسخة المحمولة
  portable: {
    useSqlJs: true,
    skipNativeRebuild: true,
    optimizeSize: true,
    includeDebugInfo: false
  },
  
  // إعدادات المثبت
  installer: {
    platform: 'win32',
    arch: 'x64',
    compression: 'maximum',
    oneClick: false
  },
  
  // إعدادات التنظيف
  cleanup: {
    keepLatest: 3,
    cleanDirs: ['dist', 'release-new', 'dist-portable-*'],
    excludePatterns: ['*.log', '*.json']
  },
  
  // إعدادات التقارير
  reporting: {
    generateJson: true,
    includeTimestamps: true,
    detailedErrors: true
  }
};
