import React from 'react'
import { Card, Space, Typography } from 'antd'
import QuickShortcuts from '../common/QuickShortcuts'

const { Title, Text } = Typography

const QuickShortcutsTest: React.FC = () => {
  const handleShortcutClick = (key: string) => {
    console.log('🎯 Shortcut clicked:', key)
    window.alert(`تم النقر على الاختصار: ${key}`)
  }

  const handlePlaySound = (type: 'click' | 'success' | 'error' | 'warning') => {
    console.log('🔊 Sound played:', type)
  }

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card>
          <Title level={2}>اختبار مكون الاختصارات السريعة</Title>
          <Text>هذه صفحة اختبار للتأكد من أن مكون الاختصارات السريعة يعمل بشكل صحيح</Text>
        </Card>

        <QuickShortcuts
          onShortcutClick={handleShortcutClick}
          onPlaySound={handlePlaySound}
        />

        <Card>
          <Title level={4}>تعليمات الاختبار:</Title>
          <ul>
            <li>يجب أن تظهر الاختصارات السريعة أعلاه</li>
            <li>انقر على أي اختصار للتأكد من أنه يعمل</li>
            <li>تحقق من console للرسائل التشخيصية</li>
            <li>يجب أن تظهر رسالة تأكيد عند النقر على أي اختصار</li>
          </ul>
        </Card>
      </Space>
    </div>
  )
}

export default QuickShortcutsTest
