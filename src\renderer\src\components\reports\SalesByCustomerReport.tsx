import React, { useState } from 'react'
import { Tag, Typography, Progress, Tabs, Card, Space } from 'antd'
import {
  TrophyOutlined,
  Bar<PERSON>hartOutlined,
  DollarOutlined,
  TeamOutlined
} from '@ant-design/icons'
import UniversalReport from './UniversalReport'
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig'
import { SafeLogger as Logger } from '../../utils/logger'
import type { ReportData, ReportType } from '../../types/reports'

const { Text } = Typography

const SalesByCustomerReport: React.FC = () => {
  const [activeTab, setActiveTab] = useState('all');

  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      Logger.info('SalesByCustomerReport', 'بدء إنشاء تقرير المبيعات حسب العميل', filters)

      // استعلام البيانات من قاعدة البيانات
      const response = await (window as any).electronAPI.getSalesByCustomerReport(filters)

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة')
      }

      const salesByCustomerData = response.data

      // معالجة البيانات
      const processedData = salesByCustomerData.map((item: any, index: number) => ({
        ...item,
        key: item.customer_id || index,
        average_invoice: item.total_amount / (item.invoice_count || 1),
        payment_percentage: item.total_amount > 0 ? (item.paid_amount / item.total_amount) * 100 : 0,
        outstanding_amount: item.total_amount - item.paid_amount,
        profit_margin: item.total_amount > 0 ? ((item.total_amount - item.cost_amount) / item.total_amount) * 100 : 0,
        customer_rank: index + 1,
        is_vip: item.total_amount > 50000, // عميل VIP إذا كان إجمالي المبيعات أكثر من 50,000
        risk_level: item.outstanding_amount > 10000 ? 'high' : item.outstanding_amount > 5000 ? 'medium' : 'low'
      }))

      // ترتيب البيانات حسب إجمالي المبيعات
      processedData.sort((a: any, b: any) => b.total_amount - a.total_amount)

      // حساب الإحصائيات
      const totalCustomers = processedData.length
      const totalInvoices = processedData.reduce((sum: number, item: any) => sum + item.invoice_count, 0)
      const totalAmount = processedData.reduce((sum: number, item: any) => sum + item.total_amount, 0)
      const totalPaid = processedData.reduce((sum: number, item: any) => sum + item.paid_amount, 0)
      const totalOutstanding = totalAmount - totalPaid
      const totalCost = processedData.reduce((sum: number, item: any) => sum + (item.cost_amount || 0), 0)
      const totalProfit = totalAmount - totalCost
      const avgInvoiceValue = totalInvoices > 0 ? totalAmount / totalInvoices : 0
      const overallPaymentPercentage = totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0
      const overallProfitMargin = totalAmount > 0 ? (totalProfit / totalAmount) * 100 : 0

      const topCustomers = processedData.slice(0, 5).map((item: any) => item.customer_name)
      const vipCustomers = processedData.filter((item: any) => item.is_vip).length
      const highRiskCustomers = processedData.filter((item: any) => item.risk_level === 'high').length

      // تعريف الأعمدة
      const columns = [
        {
          title: 'الترتيب',
          dataIndex: 'customer_rank',
          key: 'customer_rank',
          width: 80,
          align: 'center' as const,
          render: (rank: number, _record: any) => (
            <span style={{ 
              fontWeight: 'bold',
              color: rank <= 3 ? '#faad14' : '#666'
            }}>
              #{rank}
            </span>
          )
        },
        {
          title: 'اسم العميل',
          dataIndex: 'customer_name',
          key: 'customer_name',
          width: 200,
          render: (name: string, record: any) => (
            <div>
              <Text strong>{name}</Text>
              {record.is_vip && (
                <Tag color="gold" style={{ marginLeft: 8 }}>VIP</Tag>
              )}
              {record.risk_level === 'high' && (
                <Tag color="red" style={{ marginLeft: 8 }}>مخاطر عالية</Tag>
              )}
            </div>
          )
        },
        {
          title: 'عدد الفواتير',
          dataIndex: 'invoice_count',
          key: 'invoice_count',
          width: 120,
          align: 'center' as const,
          sorter: (a: any, b: any) => a.invoice_count - b.invoice_count
        },
        {
          title: 'إجمالي المبيعات',
          dataIndex: 'total_amount',
          key: 'total_amount',
          width: 150,
          align: 'center' as const,
          render: (amount: number) => (
            <Text strong style={{ color: '#1890ff' }}>
              {amount?.toLocaleString()} ₪
            </Text>
          ),
          sorter: (a: any, b: any) => a.total_amount - b.total_amount
        },
        {
          title: 'متوسط الفاتورة',
          dataIndex: 'average_invoice',
          key: 'average_invoice',
          width: 130,
          align: 'center' as const,
          render: (avg: number) => `${avg?.toFixed(2)} ₪`,
          sorter: (a: any, b: any) => a.average_invoice - b.average_invoice
        },
        {
          title: 'المبلغ المدفوع',
          dataIndex: 'paid_amount',
          key: 'paid_amount',
          width: 130,
          align: 'center' as const,
          render: (amount: number) => (
            <Text style={{ color: '#52c41a' }}>
              {amount?.toLocaleString()} ₪
            </Text>
          )
        },
        {
          title: 'المبلغ المستحق',
          dataIndex: 'outstanding_amount',
          key: 'outstanding_amount',
          width: 130,
          align: 'center' as const,
          render: (amount: number) => (
            <Text style={{ color: amount > 0 ? '#ff4d4f' : '#52c41a' }}>
              {amount?.toLocaleString()} ₪
            </Text>
          )
        },
        {
          title: 'نسبة الدفع',
          dataIndex: 'payment_percentage',
          key: 'payment_percentage',
          width: 120,
          align: 'center' as const,
          render: (percentage: number) => (
            <Progress
              percent={Number(percentage.toFixed(1))}
              size="small"
              status={percentage >= 90 ? 'success' : percentage >= 70 ? 'active' : 'exception'}
              format={(percent) => `${percent}%`}
            />
          )
        },
        {
          title: 'هامش الربح',
          dataIndex: 'profit_margin',
          key: 'profit_margin',
          width: 120,
          align: 'center' as const,
          render: (margin: number) => (
            <Tag color={margin >= 20 ? 'green' : margin >= 10 ? 'orange' : 'red'}>
              {margin?.toFixed(1)}%
            </Tag>
          )
        },
        {
          title: 'آخر عملية شراء',
          dataIndex: 'last_purchase_date',
          key: 'last_purchase_date',
          width: 140,
          render: (date: string) => date ? DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE) : '-'
        }
      ]

      return {
        title: 'تقرير المبيعات حسب العميل',
        data: processedData,
        columns,
        summary: {
          totalCustomers,
          totalInvoices,
          totalAmount,
          totalPaid,
          totalOutstanding,
          totalProfit,
          avgInvoiceValue,
          overallPaymentPercentage,
          overallProfitMargin,
          vipCustomers,
          highRiskCustomers,
          topCustomers
        },
        metadata: {
          reportType: 'sales_by_customer' as ReportType,
          totalRecords: processedData.length,
          dateRange: filters.startDate && filters.endDate ? `${filters.startDate} - ${filters.endDate}` : 'جميع الفترات',
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام تقارير المبيعات'
        }
      }
    } catch (error) {
      Logger.error('SalesByCustomerReport', 'خطأ في إنشاء تقرير المبيعات حسب العميل:', error)
      throw new Error('فشل في إنشاء تقرير المبيعات حسب العميل')
    }
  }

  return (
    <Card>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'all',
            label: (
              <Space>
                <TeamOutlined />
                جميع العملاء
              </Space>
            ),
            children: (
              <UniversalReport
                reportType={'sales_by_customer' as ReportType}
                title="تقرير المبيعات حسب العميل"
                description="تقرير تفصيلي للمبيعات مجمعة حسب العميل مع الإحصائيات والتحليلات"
                onGenerateReport={generateReport}
                showDateRange={true}
                showCustomerFilter={true}
                showAmountRangeFilter={true}
                showStatusFilter={true}
                showPrintOptions={true}
                showExportOptions={true}
                defaultFilters={{
                  dateRange: null,
                  sortBy: 'total_amount',
                  sortOrder: 'desc'
                }}
              />
            )
          },
          {
            key: 'top',
            label: (
              <Space>
                <TrophyOutlined />
                أفضل العملاء
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <TrophyOutlined style={{ fontSize: '48px', color: '#faad14' }} />
                    <h3>أفضل 20 عميل</h3>
                    <p>العملاء الأكثر شراءً وربحية</p>
                  </div>
                </Space>
              </Card>
            )
          },
          {
            key: 'analysis',
            label: (
              <Space>
                <BarChartOutlined />
                التحليل
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <BarChartOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                    <h3>تحليل سلوك العملاء</h3>
                    <p>رسوم بيانية وتحليلات متقدمة لسلوك العملاء</p>
                  </div>
                </Space>
              </Card>
            )
          },
          {
            key: 'profitability',
            label: (
              <Space>
                <DollarOutlined />
                الربحية
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <DollarOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                    <h3>تحليل ربحية العملاء</h3>
                    <p>تحليل الهوامش الربحية لكل عميل</p>
                  </div>
                </Space>
              </Card>
            )
          }
        ]}
      />
    </Card>
  )
}

export default SalesByCustomerReport
