# 🔧 **توثيق واجهات البرمجة - النظام المتكامل لتعديل القوالب والأعمدة**

---

## 📋 **نظرة عامة**

هذا المستند يوثق جميع واجهات البرمجة والخدمات المطورة في النظام المتكامل لتعديل القوالب والأعمدة.

---

## 🏗️ **البنية العامة**

### **المكونات الرئيسية:**
- **UnifiedSettingsService**: خدمة إدارة الإعدادات الموحدة
- **EnhancedTemplateCreator**: المحرر المتكامل للقوالب
- **TemplatePreviewComponent**: مكون المعاينة المباشرة
- **ReportTemplateSelector**: محدد القوالب للتقارير
- **UniversalReport**: مكون التقارير الموحد

---

## 🔌 **واجهات البرمجة الأساسية**

### **1. واجهة UnifiedPrintSettings**

```typescript
interface UnifiedPrintSettings {
  // إعدادات الصفحة
  page: {
    size: 'A4' | 'A5' | 'Letter' | 'Legal'
    orientation: 'portrait' | 'landscape'
    margins: {
      top: number
      bottom: number
      left: number
      right: number
    }
  }
  
  // إعدادات الخطوط
  fonts: {
    primary: FontSettings
    secondary: FontSettings
    headers: FontSettings
    content: FontSettings
    tables: FontSettings
  }
  
  // إعدادات الألوان
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    text: string
    headers: string
    borders: string
  }
  
  // إعدادات التخطيط
  layout: {
    spacing: number
    borderWidth: number
    borderRadius: number
    showBorders: boolean
    showShadows: boolean
    logoPosition: 'top-left' | 'top-center' | 'top-right'
    logoSize: 'small' | 'medium' | 'large'
  }
  
  // إعدادات المحتوى
  content: {
    headerText: string
    footerText: string
    watermark: boolean
    watermarkText: string
    watermarkOpacity: number
  }
  
  // إعدادات الجودة
  quality: {
    quality: 'draft' | 'normal' | 'high'
    copies: number
    autoSave: boolean
  }
  
  // معلومات النسخة
  version: string
  lastUpdated: string
  autoSync: boolean
}
```

### **2. واجهة EnhancedTemplate**

```typescript
interface EnhancedTemplate {
  // معلومات القالب
  metadata: {
    id: string
    name: string
    description: string
    type: 'report' | 'invoice' | 'document'
    category?: string
    isDefault: boolean
    isActive: boolean
    createdAt: string
    updatedAt: string
  }
  
  // نظام الوراثة
  inheritance: {
    inheritsFromGlobal: boolean
    customSettings: Partial<UnifiedPrintSettings>
    overrides: string[]
  }
  
  // إعدادات الأعمدة
  columns?: EnhancedColumnConfig[]
  
  // إعدادات خاصة بنوع القالب
  typeSpecificSettings?: Record<string, any>
}
```

### **3. واجهة EnhancedColumnConfig**

```typescript
interface EnhancedColumnConfig {
  // معلومات العمود
  key: string
  title: string
  dataIndex: string
  
  // إعدادات العرض
  display: {
    visible: boolean
    width: number | string
    minWidth?: number
    maxWidth?: number
    align: 'left' | 'center' | 'right'
    ellipsis: boolean
    resizable: boolean
    sortable: boolean
    filterable: boolean
  }
  
  // إعدادات الطباعة
  print: {
    visible: boolean
    width: number | string
    align: 'left' | 'center' | 'right'
    fontSize: number
    fontWeight: 'normal' | 'bold'
    color: string
    backgroundColor: string
    borderStyle: 'none' | 'solid' | 'dashed'
    borderColor: string
    padding: number
  }
  
  // إعدادات التنسيق
  format: {
    type: 'text' | 'number' | 'currency' | 'date' | 'percentage'
    precision?: number
    prefix?: string
    suffix?: string
    dateFormat?: string
    locale?: string
  }
  
  // إعدادات متقدمة
  advanced: {
    conditional: boolean
    conditions?: ColumnCondition[]
    customRender?: string
    tooltip?: string
    helpText?: string
  }
}
```

---

## 🛠️ **خدمات النظام**

### **1. UnifiedSettingsService**

#### **الطرق الرئيسية:**

```typescript
class UnifiedSettingsService {
  // تحميل الإعدادات العامة
  async loadGlobalSettings(): Promise<UnifiedPrintSettings>
  
  // حفظ الإعدادات العامة
  async saveGlobalSettings(settings: UnifiedPrintSettings): Promise<boolean>
  
  // تحميل جميع القوالب
  async loadTemplates(): Promise<EnhancedTemplate[]>
  
  // حفظ قالب
  async saveTemplate(template: EnhancedTemplate): Promise<boolean>
  
  // حذف قالب
  async deleteTemplate(id: string): Promise<boolean>
  
  // الحصول على قالب بالمعرف
  getTemplate(id: string): EnhancedTemplate | null
  
  // الحصول على الإعدادات الفعالة للقالب
  getEffectiveSettings(template: EnhancedTemplate): UnifiedPrintSettings
  
  // التحقق من صحة الإعدادات
  validateSettings(settings: Partial<UnifiedPrintSettings>): SettingsValidation
  
  // التحقق من صحة القالب
  validateTemplate(template: EnhancedTemplate): SettingsValidation
}
```

### **2. useUnifiedSettings Hook**

#### **القيم المُرجعة:**

```typescript
interface UseUnifiedSettingsReturn {
  // الحالة
  globalSettings: UnifiedPrintSettings | null
  templates: EnhancedTemplate[]
  selectedTemplate: EnhancedTemplate | null
  loading: boolean
  error: string | null
  
  // دوال إدارة الإعدادات العامة
  loadGlobalSettings: () => Promise<void>
  saveGlobalSettings: (settings: UnifiedPrintSettings) => Promise<boolean>
  updateGlobalSettings: (updates: Partial<UnifiedPrintSettings>) => Promise<boolean>
  resetGlobalSettings: () => Promise<boolean>
  
  // دوال إدارة القوالب
  createTemplate: (template?: Partial<EnhancedTemplate>) => EnhancedTemplate
  saveTemplate: (template: EnhancedTemplate) => Promise<boolean>
  deleteTemplate: (id: string) => Promise<boolean>
  selectTemplate: (id: string) => void
  duplicateTemplate: (id: string) => EnhancedTemplate | null
  
  // دوال الوراثة والحساب
  getEffectiveSettings: (template: EnhancedTemplate) => UnifiedPrintSettings
  getTemplatesByCategory: (category: string) => EnhancedTemplate[]
  getTemplatesByType: (type: string) => EnhancedTemplate[]
  
  // دوال التحقق
  validateSettings: (settings: Partial<UnifiedPrintSettings>) => SettingsValidation
  validateTemplate: (template: EnhancedTemplate) => SettingsValidation
}
```

---

## 🎨 **مكونات واجهة المستخدم**

### **1. EnhancedTemplateCreator**

#### **الخصائص (Props):**

```typescript
interface EnhancedTemplateCreatorProps {
  visible: boolean
  onClose: () => void
  template?: EnhancedTemplate
  mode?: 'create' | 'edit' | 'duplicate'
  reportContext?: {
    reportType: string
    reportCategory: string
    reportTitle: string
  }
  previewData?: any[]
  previewLoading?: boolean
}
```

### **2. TemplatePreviewComponent**

#### **الخصائص (Props):**

```typescript
interface TemplatePreviewComponentProps {
  template: EnhancedTemplate
  effectiveSettings: UnifiedPrintSettings
  previewData?: any[]
  loading?: boolean
  onSettingsChange?: (settings: Partial<UnifiedPrintSettings>) => void
}
```

### **3. ReportTemplateSelector**

#### **الخصائص (Props):**

```typescript
interface ReportTemplateSelectorProps {
  reportType?: string
  category?: string
  onTemplateSelect?: (template: ReportTemplate) => void
  onPrintWithTemplate?: (template: ReportTemplate) => void
  showPrintButton?: boolean
  size?: 'small' | 'middle' | 'large'
  style?: React.CSSProperties
  autoSelectTemplate?: boolean
  reportData?: any[]
  reportLoading?: boolean
}
```

---

## 🔄 **تدفق البيانات**

### **1. تحميل القوالب:**
```
UniversalReport → ReportTemplateSelector → useReportTemplates → UnifiedSettingsService → Database
```

### **2. تعديل القوالب:**
```
ReportTemplateSelector → EnhancedTemplateCreator → useUnifiedSettings → UnifiedSettingsService → Database
```

### **3. المعاينة المباشرة:**
```
EnhancedTemplateCreator → TemplatePreviewComponent → Real Data → Live Preview
```

---

## 🎯 **أمثلة الاستخدام**

### **1. إنشاء قالب جديد:**

```typescript
const { createTemplate, saveTemplate } = useUnifiedSettings()

const newTemplate = createTemplate({
  metadata: {
    name: 'قالب مخصص',
    description: 'قالب للتقارير المالية',
    type: 'report',
    category: 'financial'
  },
  inheritance: {
    inheritsFromGlobal: true,
    customSettings: {
      colors: {
        primary: '#1890ff'
      }
    },
    overrides: ['colors.primary']
  }
})

await saveTemplate(newTemplate)
```

### **2. تحديث الإعدادات العامة:**

```typescript
const { updateGlobalSettings } = useUnifiedSettings()

await updateGlobalSettings({
  fonts: {
    primary: {
      family: 'Arial',
      size: 14,
      weight: 'normal'
    }
  }
})
```

### **3. استخدام المعاينة المباشرة:**

```typescript
<TemplatePreviewComponent
  template={selectedTemplate}
  effectiveSettings={getEffectiveSettings(selectedTemplate)}
  previewData={reportData}
  loading={reportLoading}
  onSettingsChange={(settings) => {
    // تحديث الإعدادات
  }}
/>
```

---

## ⚡ **تحسينات الأداء**

### **1. التخزين المؤقت:**
- القوالب محفوظة في الذاكرة المؤقتة
- الإعدادات العامة محفوظة محلياً
- البيانات المحسوبة مخزنة مؤقتاً

### **2. التحميل التدريجي:**
- تحميل القوالب عند الحاجة
- تحميل البيانات بشكل تدريجي
- معاينة محسنة للبيانات الكبيرة

### **3. التحديث الذكي:**
- تحديث جزئي للمكونات
- إعادة رسم محسنة
- تجنب التحديثات غير الضرورية

---

## 🔒 **الأمان والتحقق**

### **1. التحقق من البيانات:**
- التحقق من صحة الإعدادات
- التحقق من صحة القوالب
- التحقق من الأذونات

### **2. معالجة الأخطاء:**
- معالجة شاملة للأخطاء
- رسائل خطأ واضحة
- استرداد تلقائي من الأخطاء

---

**هذا التوثيق يغطي جميع جوانب النظام المطور. للمزيد من التفاصيل، راجع الكود المصدري والتعليقات المضمنة.** 🚀
