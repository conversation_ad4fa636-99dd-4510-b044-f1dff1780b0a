import * as fs from 'fs'
import * as path from 'path'
import { Logger } from '../utils/logger'
import { DatabaseService } from './DatabaseService'

export interface SyncConfig {
  enabled: boolean
  sharedFolder: string
  deviceRole: 'main' | 'branch' // مركزي أو فرعي
  syncInterval: number // بالدقائق
  deviceId?: string // معرف فريد للجهاز
  deviceName?: string // اسم الجهاز
}

export class FileSyncService {
  private config: SyncConfig
  private databaseService: DatabaseService
  private syncTimer?: NodeJS.Timeout
  private lastSyncTime: Date = new Date(0)
  private localDbPath: string
  private sharedDbPath: string

  constructor(config: SyncConfig, databaseService: DatabaseService) {
    this.config = config
    this.databaseService = databaseService
    this.localDbPath = path.join(require('electron').app.getPath('userData'), 'database.db')
    this.sharedDbPath = path.join(config.sharedFolder, 'database.db')

    // إنشاء معرف فريد للجهاز إذا لم يكن موجوداً
    if (!this.config.deviceId) {
      this.config.deviceId = this.generateDeviceId()
    }
    if (!this.config.deviceName) {
      this.config.deviceName = require('os').hostname()
    }
  }

  async startSync(): Promise<void> {
    try {
      if (!this.config.enabled) {
        Logger.info('FileSync', 'المزامنة معطلة')
        return
      }

      // فحص إمكانية الوصول للمجلد المشترك
      if (!fs.existsSync(this.config.sharedFolder)) {
        throw new Error(`لا يمكن الوصول للمجلد المشترك: ${this.config.sharedFolder}`)
      }

      // بدء المزامنة الدورية (تحويل الدقائق إلى ميلي ثانية)
      const intervalMs = this.config.syncInterval * 60 * 1000
      this.syncTimer = setInterval(() => {
        this.performSync()
      }, intervalMs)

      // مزامنة فورية
      await this.performSync()

      Logger.success('FileSync', `تم بدء المزامنة - الدور: ${this.config.deviceRole}`)
    } catch (error) {
      Logger.error('FileSync', 'خطأ في بدء المزامنة:', error)
      throw error
    }
  }

  async stopSync(): Promise<void> {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
      this.syncTimer = undefined
    }
    Logger.info('FileSync', 'تم إيقاف خدمة المزامنة')
  }

  private async performSync(): Promise<void> {
    try {
      const lockFile = `${this.sharedDbPath}.lock`

      // فحص وجود ملف القفل مع timeout
      if (fs.existsSync(lockFile)) {
        const isValidLock = await this.isLockValid(lockFile)
        if (isValidLock) {
          Logger.warn('FileSync', 'قاعدة البيانات مقفلة من جهاز آخر - سيتم إعادة المحاولة لاحقاً')
          return
        } else {
          // كسر القفل القديم
          Logger.info('FileSync', 'كسر قفل قديم معلق...')
          await this.breakStaleLock(lockFile)
        }
      }

      // إنشاء ملف قفل مؤقت
      await this.createLockFile(lockFile)

      try {
        const localExists = fs.existsSync(this.localDbPath)
        const sharedExists = fs.existsSync(this.sharedDbPath)

        if (!localExists && !sharedExists) {
          Logger.info('FileSync', 'لا توجد ملفات للمزامنة')
          return
        }

        if (!sharedExists && localExists) {
          // لا يوجد ملف مشترك، رفع المحلي
          await this.uploadToShared()
        } else if (!localExists && sharedExists) {
          // لا يوجد ملف محلي، تحميل المشترك
          await this.downloadFromShared()
        } else {
          // كلا الملفين موجود، مقارنة التواريخ
          await this.smartSync()
        }

        this.lastSyncTime = new Date()

        // تحديث سجل الأجهزة
        await this.updateDeviceRegistry()

        Logger.info('FileSync', `تمت المزامنة بنجاح - ${this.config.deviceRole}`)

        // إرسال تنبيه نجاح المزامنة
        await this.notifyUser('sync-success', 'تمت المزامنة بنجاح', {
          deviceRole: this.config.deviceRole,
          timestamp: new Date().toISOString()
        })
      } finally {
        // حذف ملف القفل
        await this.removeLockFile(lockFile)
      }
    } catch (error) {
      Logger.error('FileSync', 'خطأ في المزامنة:', error)

      // إرسال تنبيه خطأ المزامنة
      await this.notifyUser('sync-error', 'فشل في المزامنة', {
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      })
    }
  }

  private async createLockFile(lockPath: string): Promise<void> {
    const lockInfo = {
      clientId: process.env.CLIENT_ID || 'unknown',
      timestamp: new Date().toISOString(),
      pid: process.pid,
      hostname: require('os').hostname(),
      deviceId: this.config.deviceId
    }
    fs.writeFileSync(lockPath, JSON.stringify(lockInfo))
  }

  private async removeLockFile(lockPath: string): Promise<void> {
    if (fs.existsSync(lockPath)) {
      fs.unlinkSync(lockPath)
    }
  }

  private async isLockValid(lockPath: string): Promise<boolean> {
    try {
      if (!fs.existsSync(lockPath)) {
        return false
      }

      const lockData = fs.readFileSync(lockPath, 'utf8')
      const lockInfo = JSON.parse(lockData)

      // فحص عمر القفل (30 دقيقة كحد أقصى)
      const lockAge = Date.now() - new Date(lockInfo.timestamp).getTime()
      const maxLockAge = 30 * 60 * 1000 // 30 دقيقة

      if (lockAge > maxLockAge) {
        Logger.warn('FileSync', `قفل قديم تم العثور عليه (عمر: ${Math.round(lockAge / 60000)} دقيقة)`)
        return false
      }

      // فحص إذا كان القفل من نفس الجهاز
      const currentHostname = require('os').hostname()
      if (lockInfo.hostname === currentHostname && lockInfo.deviceId === this.config.deviceId) {
        Logger.info('FileSync', 'القفل من نفس الجهاز - سيتم كسره')
        return false
      }

      return true
    } catch (error) {
      Logger.error('FileSync', 'خطأ في فحص صحة القفل:', error)
      return false
    }
  }

  private async breakStaleLock(lockPath: string): Promise<void> {
    try {
      if (fs.existsSync(lockPath)) {
        // إنشاء نسخة احتياطية من معلومات القفل
        const lockData = fs.readFileSync(lockPath, 'utf8')
        const backupPath = `${lockPath}.broken-${Date.now()}`
        fs.writeFileSync(backupPath, lockData)

        // حذف القفل
        fs.unlinkSync(lockPath)
        Logger.info('FileSync', `تم كسر القفل القديم وحفظ نسخة احتياطية: ${backupPath}`)
      }
    } catch (error) {
      Logger.error('FileSync', 'خطأ في كسر القفل:', error)
    }
  }

  private async uploadToShared(): Promise<void> {
    Logger.info('FileSync', 'رفع قاعدة البيانات المحلية للمشاركة...')

    // إنشاء نسخة احتياطية محسنة من القاعدة المشتركة
    await this.createEnhancedBackup(this.sharedDbPath, 'shared', 'before-upload')

    // إنشاء نسخة احتياطية من القاعدة المحلية أيضاً
    await this.createEnhancedBackup(this.localDbPath, 'local', 'before-upload')

    // نسخ الملف مع التحقق
    try {
      fs.copyFileSync(this.localDbPath, this.sharedDbPath)

      // التحقق من نجاح النسخ
      const localSize = fs.statSync(this.localDbPath).size
      const sharedSize = fs.statSync(this.sharedDbPath).size

      if (localSize !== sharedSize) {
        throw new Error('فشل في التحقق من سلامة النسخ')
      }

      Logger.success('FileSync', `تم رفع قاعدة البيانات بنجاح (${Math.round(localSize/1024)} KB)`)

      // نسخ النسخ الاحتياطية إلى المجلد المشترك
      await this.syncBackupsToSharedFolder()

    } catch (error) {
      Logger.error('FileSync', 'خطأ في رفع قاعدة البيانات:', error)
      throw error
    }
  }

  private async downloadFromShared(): Promise<void> {
    Logger.info('FileSync', 'تحميل قاعدة البيانات المشتركة...')

    // إنشاء نسخة احتياطية محسنة
    await this.createEnhancedBackup(this.localDbPath, 'local', 'before-download')

    // نسخ الملف مع التحقق
    try {
      fs.copyFileSync(this.sharedDbPath, this.localDbPath)

      // التحقق من نجاح النسخ
      const sharedSize = fs.statSync(this.sharedDbPath).size
      const localSize = fs.statSync(this.localDbPath).size

      if (sharedSize !== localSize) {
        throw new Error('فشل في التحقق من سلامة النسخ')
      }

      Logger.success('FileSync', `تم تحميل قاعدة البيانات بنجاح (${Math.round(sharedSize/1024)} KB)`)
    } catch (error) {
      Logger.error('FileSync', 'خطأ في تحميل قاعدة البيانات:', error)
      throw error
    }
  }

  private async smartSync(): Promise<void> {
    const localStats = fs.statSync(this.localDbPath)
    const sharedStats = fs.statSync(this.sharedDbPath)

    const localModified = localStats.mtime
    const sharedModified = sharedStats.mtime

    Logger.info('FileSync', `مقارنة التواريخ - محلي: ${localModified.toISOString()}, مشترك: ${sharedModified.toISOString()}`)

    // فحص التعارض المحتمل (تعديل في نفس الوقت تقريباً)
    const timeDifference = Math.abs(localModified.getTime() - sharedModified.getTime())
    const conflictThreshold = 60000 // دقيقة واحدة

    if (timeDifference < conflictThreshold && timeDifference > 0) {
      Logger.warn('FileSync', '⚠️ تعارض محتمل: تم تعديل الملفين في نفس الوقت تقريباً!')

      // إنشاء نسخ احتياطية من كلا الملفين
      await this.createConflictBackups()

      // تنبيه المستخدم
      await this.notifyUserOfConflict(localModified, sharedModified)

      // اختيار الملف الأحدث مع تحذير
      if (localModified > sharedModified) {
        Logger.warn('FileSync', '⚠️ سيتم رفع الملف المحلي (أحدث بـ ' + Math.round(timeDifference/1000) + ' ثانية)')
        await this.uploadToShared()
      } else {
        Logger.warn('FileSync', '⚠️ سيتم تحميل الملف المشترك (أحدث بـ ' + Math.round(timeDifference/1000) + ' ثانية)')
        await this.downloadFromShared()
      }
      return
    }

    if (localModified > sharedModified) {
      // الملف المحلي أحدث - رفعه للمشترك
      Logger.info('FileSync', 'الملف المحلي أحدث، جاري الرفع...')
      await this.uploadToShared()
    } else if (sharedModified > localModified) {
      // الملف المشترك أحدث - تحميله محلياً
      Logger.info('FileSync', 'الملف المشترك أحدث، جاري التحميل...')
      await this.downloadFromShared()
    } else {
      // نفس التاريخ - لا حاجة للمزامنة
      Logger.info('FileSync', 'الملفات متطابقة، لا حاجة للمزامنة')
    }
  }

  async getSyncStatus(): Promise<{
    enabled: boolean
    deviceRole: string
    isActive: boolean
    lastSync: Date
    sharedFolder: string
    isConnected: boolean
  }> {
    return {
      enabled: this.config.enabled,
      deviceRole: this.config.deviceRole,
      isActive: !!this.syncTimer,
      lastSync: this.lastSyncTime,
      sharedFolder: this.config.sharedFolder,
      isConnected: fs.existsSync(this.config.sharedFolder)
    }
  }

  async forceSyncNow(): Promise<void> {
    Logger.info('FileSync', 'بدء مزامنة فورية...')
    await this.performSync()
  }

  private generateDeviceId(): string {
    const crypto = require('crypto')
    const os = require('os')

    // إنشاء معرف فريد بناءً على معلومات الجهاز
    const machineInfo = `${os.hostname()}-${os.platform()}-${os.arch()}`
    const hash = crypto.createHash('md5').update(machineInfo).digest('hex')
    return `device-${hash.substring(0, 8)}`
  }

  private async updateDeviceRegistry(): Promise<void> {
    try {
      const registryPath = path.join(this.config.sharedFolder, 'devices.json')
      let devices: any[] = []

      // قراءة سجل الأجهزة الموجود
      if (fs.existsSync(registryPath)) {
        try {
          const registryData = fs.readFileSync(registryPath, 'utf8')
          const parsedData = JSON.parse(registryData)
          // التأكد من أن البيانات هي array
          devices = Array.isArray(parsedData) ? parsedData : []
        } catch (parseError) {
          Logger.warn('FileSync', 'خطأ في قراءة سجل الأجهزة، سيتم إنشاء سجل جديد:', parseError)
          devices = []
        }
      }

      // تحديث معلومات هذا الجهاز
      const deviceInfo = {
        id: this.config.deviceId,
        name: this.config.deviceName,
        role: this.config.deviceRole,
        lastSync: new Date().toISOString(),
        hostname: require('os').hostname(),
        platform: require('os').platform()
      }

      // التأكد من أن devices هو array قبل المتابعة
      if (!Array.isArray(devices)) {
        Logger.warn('FileSync', 'devices ليس array، سيتم إنشاء array جديد')
        devices = []
      }

      // البحث عن الجهاز في السجل
      const existingIndex = devices.findIndex(d => d && d.id === this.config.deviceId)
      if (existingIndex >= 0) {
        devices[existingIndex] = deviceInfo
      } else {
        devices.push(deviceInfo)
      }

      // حفّ السجل المحدث
      fs.writeFileSync(registryPath, JSON.stringify(devices, null, 2))
      Logger.info('FileSync', `تم تحديث سجل الأجهزة - الأجهزة المتصلة: ${devices.length}`)
    } catch (error) {
      Logger.error('FileSync', 'خطأ في تحديث سجل الأجهزة:', error)
    }
  }

  async getConnectedDevices(): Promise<any[]> {
    try {
      const registryPath = path.join(this.config.sharedFolder, 'devices.json')

      if (!fs.existsSync(registryPath)) {
        return []
      }

      const registryData = fs.readFileSync(registryPath, 'utf8')
      const devices = JSON.parse(registryData)

      // فلترة الأجهزة النشطة (آخر مزامنة خلال آخر ساعة)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      const activeDevices = devices.filter((device: any) => {
        const lastSync = new Date(device.lastSync)
        return lastSync > oneHourAgo
      })

      return activeDevices
    } catch (error) {
      Logger.error('FileSync', 'خطأ في جلب قائمة الأجهزة:', error)
      return []
    }
  }

  private async createConflictBackups(): Promise<void> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')

      // نسخة احتياطية من الملف المحلي
      if (fs.existsSync(this.localDbPath)) {
        const localBackup = `${this.localDbPath}.conflict-local-${timestamp}`
        fs.copyFileSync(this.localDbPath, localBackup)
        Logger.info('FileSync', `تم إنشاء نسخة احتياطية محلية: ${localBackup}`)
      }

      // نسخة احتياطية من الملف المشترك
      if (fs.existsSync(this.sharedDbPath)) {
        const sharedBackup = `${this.sharedDbPath}.conflict-shared-${timestamp}`
        fs.copyFileSync(this.sharedDbPath, sharedBackup)
        Logger.info('FileSync', `تم إنشاء نسخة احتياطية مشتركة: ${sharedBackup}`)
      }
    } catch (error) {
      Logger.error('FileSync', 'خطأ في إنشاء النسخ الاحتياطية للتعارض:', error)
    }
  }

  private async notifyUserOfConflict(localTime: Date, sharedTime: Date): Promise<void> {
    try {
      const { BrowserWindow } = require('electron')
      const mainWindow = BrowserWindow.getAllWindows()[0]

      if (mainWindow) {
        mainWindow.webContents.send('sync-conflict-detected', {
          type: 'conflict',
          message: 'تم اكتشاف تعارض في المزامنة',
          details: {
            localModified: localTime.toISOString(),
            sharedModified: sharedTime.toISOString(),
            timeDifference: Math.abs(localTime.getTime() - sharedTime.getTime())
          },
          action: 'تم حل التعارض تلقائياً باختيار الملف الأحدث'
        })
      }
    } catch (error) {
      Logger.error('FileSync', 'خطأ في إرسال تنبيه التعارض:', error)
    }
  }

  private async createEnhancedBackup(filePath: string, type: 'local' | 'shared', reason: string): Promise<string | null> {
    try {
      if (!fs.existsSync(filePath)) {
        Logger.info('FileSync', `لا يوجد ملف للنسخ الاحتياطي: ${filePath}`)
        return null
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const fileStats = fs.statSync(filePath)
      const backupPath = `${filePath}.backup-${type}-${reason}-${timestamp}`

      // نسخ الملف
      fs.copyFileSync(filePath, backupPath)

      // التحقق من النسخ
      const backupStats = fs.statSync(backupPath)
      if (fileStats.size !== backupStats.size) {
        throw new Error('فشل في التحقق من سلامة النسخة الاحتياطية')
      }

      Logger.info('FileSync', `تم إنشاء نسخة احتياطية: ${backupPath} (${Math.round(fileStats.size/1024)} KB)`)

      // تنظيف النسخ القديمة (الاحتفاظ بآخر 10 نسخ)
      await this.cleanupOldBackups(filePath)

      return backupPath
    } catch (error) {
      Logger.error('FileSync', 'خطأ في إنشاء النسخة الاحتياطية:', error)
      return null
    }
  }

  /**
   * مزامنة النسخ الاحتياطية إلى المجلد المشترك
   */
  private async syncBackupsToSharedFolder(): Promise<void> {
    try {
      const sharedBackupDir = path.join(path.dirname(this.sharedDbPath), 'backups')

      // إنشاء مجلد النسخ الاحتياطية في المجلد المشترك
      if (!fs.existsSync(sharedBackupDir)) {
        fs.mkdirSync(sharedBackupDir, { recursive: true })
        Logger.info('FileSync', `تم إنشاء مجلد النسخ الاحتياطية المشتركة: ${sharedBackupDir}`)
      }

      // نسخ النسخ الاحتياطية المحلية الحديثة
      const localBackupDir = path.join(path.dirname(this.localDbPath), 'backups')
      if (fs.existsSync(localBackupDir)) {
        const backupFiles = fs.readdirSync(localBackupDir)
        const recentBackups = backupFiles
          .filter(file => file.endsWith('.db'))
          .map(file => ({
            name: file,
            path: path.join(localBackupDir, file),
            mtime: fs.statSync(path.join(localBackupDir, file)).mtime
          }))
          .sort((a, b) => b.mtime.getTime() - a.mtime.getTime())
          .slice(0, 5) // آخر 5 نسخ احتياطية

        for (const backup of recentBackups) {
          const sharedBackupPath = path.join(sharedBackupDir, backup.name)
          if (!fs.existsSync(sharedBackupPath)) {
            fs.copyFileSync(backup.path, sharedBackupPath)
            Logger.info('FileSync', `تم نسخ النسخة الاحتياطية إلى المجلد المشترك: ${backup.name}`)
          }
        }
      }
    } catch (error) {
      Logger.error('FileSync', 'خطأ في مزامنة النسخ الاحتياطية:', error)
    }
  }

  private async cleanupOldBackups(originalPath: string): Promise<void> {
    try {
      const dir = path.dirname(originalPath)
      const baseName = path.basename(originalPath)

      // البحث عن ملفات النسخ الاحتياطية
      const files = fs.readdirSync(dir)
      const backupFiles = files
        .filter(file => file.startsWith(`${baseName}.backup-`))
        .map(file => ({
          name: file,
          path: path.join(dir, file),
          stats: fs.statSync(path.join(dir, file))
        }))
        .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime())

      // حذف النسخ الزائدة (الاحتفاظ بآخر 15 نسخة)
      const maxBackups = 15
      if (backupFiles.length > maxBackups) {
        const filesToDelete = backupFiles.slice(maxBackups)
        for (const file of filesToDelete) {
          fs.unlinkSync(file.path)
          Logger.info('FileSync', `تم حذف نسخة احتياطية قديمة: ${file.name}`)
        }
      }
    } catch (error) {
      Logger.error('FileSync', 'خطأ في تنظيف النسخ الاحتياطية:', error)
    }
  }

  private async notifyUser(type: string, message: string, details?: any): Promise<void> {
    try {
      const { BrowserWindow } = require('electron')
      const mainWindow = BrowserWindow.getAllWindows()[0]

      if (mainWindow) {
        mainWindow.webContents.send('sync-notification', {
          type,
          message,
          details,
          timestamp: new Date().toISOString(),
          deviceRole: this.config.deviceRole,
          deviceName: this.config.deviceName
        })
      }
    } catch (error) {
      Logger.error('FileSync', 'خطأ في إرسال التنبيه:', error)
    }
  }
}
