import React from 'react';
import { Tag, Typography, Progress, Tooltip } from 'antd';
import { StarOutlined, TrophyOutlined, FireOutlined } from '@ant-design/icons';
import UniversalReport from './UniversalReport';
import { DateUtils } from '../../utils/dateConfig';
import type { ReportData, ReportType } from '../../types/reports';
import { createDefaultFilters } from '../../types/reports';

const { Text } = Typography;

const SalesByProductReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير مبيعات الأصناف...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getSalesByProductReport(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const salesByProductData = response.data;

      // معالجة البيانات
      const processedData = salesByProductData.map((item: any, index: number) => ({
        ...item,
        key: item.item_id || index,
        avg_price: item.total_quantity > 0 ? item.total_value / item.total_quantity : 0,
        profit_margin: item.total_value > 0 ? ((item.total_value - item.total_cost) / item.total_value) * 100 : 0,
        rank: index + 1
      }));

      // حساب الإحصائيات
      const totalProducts = processedData.length;
      const totalQuantity = processedData.reduce((sum: number, item: any) => sum + item.total_quantity, 0);
      const totalValue = processedData.reduce((sum: number, item: any) => sum + item.total_value, 0);
      const totalCost = processedData.reduce((sum: number, item: any) => sum + (item.total_cost || 0), 0);
      const totalProfit = totalValue - totalCost;

      const topProducts = [...processedData]
        .sort((a: any, b: any) => b.total_value - a.total_value)
        .slice(0, 5)
        .map((item: any) => ({
          name: item.item_name,
          value: item.total_value,
          quantity: item.total_quantity
        }));

      const summary = {
        totalProducts,
        totalQuantity,
        totalValue: Math.round(totalValue * 100) / 100,
        totalCost: Math.round(totalCost * 100) / 100,
        totalProfit: Math.round(totalProfit * 100) / 100,
        avgPrice: totalQuantity > 0 ? Math.round((totalValue / totalQuantity) * 100) / 100 : 0,
        profitMargin: totalValue > 0 ? Math.round((totalProfit / totalValue) * 100 * 100) / 100 : 0,
        topProducts
      };

      console.log('✅ تم إنشاء تقرير مبيعات الأصناف بنجاح');

      return {
        title: 'تقرير مبيعات الأصناف',
        data: processedData,
        columns,
        summary,
        metadata: {
          reportType: 'sales_by_product' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: processedData.length
        }
      };
    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير مبيعات الأصناف:', error);
      throw error;
    }
  };

  // إعداد الأعمدة
  const columns = [
    {
      title: 'الترتيب',
      dataIndex: 'rank',
      key: 'rank',
      width: 80,
      align: 'center' as const,
      render: (rank: number) => {
        if (rank <= 3) {
          const colors = ['#ffd700', '#c0c0c0', '#cd7f32'];
          return (
            <Tooltip title={`المركز ${rank}`}>
              <TrophyOutlined style={{ color: colors[rank - 1], fontSize: '16px' }} />
            </Tooltip>
          );
        }
        return <Text strong>{rank}</Text>;
      }
    },
    {
      title: 'كود الصنف',
      dataIndex: 'item_code',
      key: 'item_code',
      width: 120,
      render: (code: string) => (
        <Text strong style={{ color: '#1890ff' }}>{code}</Text>
      )
    },
    {
      title: 'اسم الصنف',
      dataIndex: 'item_name',
      key: 'item_name',
      width: 200,
      render: (name: string) => (
        <Text strong>{name}</Text>
      )
    },
    {
      title: 'الفئة',
      dataIndex: 'category_name',
      key: 'category_name',
      width: 120,
      render: (category: string) => (
        <Tag color="blue">{category}</Tag>
      )
    },
    {
      title: 'الكمية المباعة',
      dataIndex: 'total_quantity',
      key: 'total_quantity',
      width: 120,
      align: 'center' as const,
      render: (quantity: number, record: any) => (
        <div>
          <Text strong>{quantity.toLocaleString()}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>{record.unit}</Text>
        </div>
      )
    },
    {
      title: 'إجمالي المبيعات (₪)',
      dataIndex: 'total_value',
      key: 'total_value',
      width: 150,
      align: 'right' as const,
      render: (value: number) => (
        <Text strong style={{ color: '#52c41a' }}>
          {value.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'متوسط السعر (₪)',
      dataIndex: 'avg_price',
      key: 'avg_price',
      width: 120,
      align: 'right' as const,
      render: (price: number) => (
        <Text style={{ color: '#fa8c16' }}>
          {price.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'هامش الربح',
      dataIndex: 'profit_margin',
      key: 'profit_margin',
      width: 120,
      align: 'center' as const,
      render: (margin: number) => (
        <Progress
          percent={Math.round(margin)}
          size="small"
          strokeColor={margin >= 30 ? '#52c41a' : margin >= 15 ? '#fa8c16' : '#ff4d4f'}
          format={() => `${margin.toFixed(1)}%`}
        />
      )
    },
    {
      title: 'عدد الفواتير',
      dataIndex: 'invoice_count',
      key: 'invoice_count',
      width: 100,
      align: 'center' as const,
      render: (count: number) => (
        <Tag color="green">{count}</Tag>
      )
    },
    {
      title: 'الأداء',
      dataIndex: 'performance_rating',
      key: 'performance_rating',
      width: 100,
      align: 'center' as const,
      render: (rating: string) => {
        const ratingConfig = {
          'Excellent': { color: 'green', icon: <StarOutlined />, text: 'ممتاز' },
          'Good': { color: 'blue', icon: <FireOutlined />, text: 'جيد' },
          'Average': { color: 'orange', icon: null, text: 'متوسط' },
          'Poor': { color: 'red', icon: null, text: 'ضعيف' }
        };
        const config = ratingConfig[rating as keyof typeof ratingConfig] || { color: 'default', icon: null, text: rating };
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      }
    }
  ];

  return (
    <UniversalReport
      reportType={'sales_by_product' as ReportType}
      title="تقرير مبيعات الأصناف"
      description="تقرير شامل لمبيعات الأصناف مع تحليل الأداء والربحية"
      onGenerateReport={generateReport}
      defaultFilters={createDefaultFilters('sales_by_product')}
      showDateRange={true}
      showCustomerFilter={true}
      showItemFilter={true}
      showCategoryFilter={true}
      showAmountRangeFilter={true}
      showAdvancedSearch={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default SalesByProductReport;
