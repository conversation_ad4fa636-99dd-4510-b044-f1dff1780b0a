/**
 * تقرير مقارنة الرواتب
 * تقرير مقارنة الرواتب بين الأقسام والموظفين
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const SalaryComparisonReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'salary_comparison' as ReportType}
      title="تقرير مقارنة الرواتب"
      description="مقارنة شاملة للرواتب بين الأقسام والمناصب مع تحليل الفجوات والتوزيع"
      showDateRange={false}
      showEmployeeFilter={false}
      showDepartmentFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}

      exportFileName="salary_comparison_report"
      defaultFilters={{
        sortBy: 'avg_salary',
        sortOrder: 'desc'
      }}
    />
  )
}

export default SalaryComparisonReport
