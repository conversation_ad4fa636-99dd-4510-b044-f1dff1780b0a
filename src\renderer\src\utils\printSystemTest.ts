/**
 * اختبار شامل لنظام الطباعة المحدث - ZET.IA
 * يتحقق من جميع الوظائف والتكاملات
 */

import { MasterPrintService } from '../services/MasterPrintService'
import {
  UnifiedPrintOptions,
  createDefaultPrintOptions,
  createDefaultPrintData
} from '../types/print'
import { Logger } from './logger'

export interface TestResult {
  testName: string
  success: boolean
  message: string
  duration: number
  details?: any
}

export class PrintSystemTester {
  private results: TestResult[] = []
  private printService: MasterPrintService

  constructor() {
    this.printService = MasterPrintService.getInstance()
  }

  /**
   * تشغيل جميع الاختبارات
   */
  public async runAllTests(): Promise<TestResult[]> {
    this.results = []
    
    console.log('🧪 بدء الاختبار الشامل لنظام الطباعة...')
    
    // اختبارات الأنواع الموحدة
    await this.testUnifiedTypes()
    
    // اختبارات الخدمات
    await this.testMasterPrintService()
    
    // اختبارات الإعدادات
    await this.testPrintSettings()
    
    // اختبارات أنواع المستندات
    await this.testDocumentTypes()
    
    // اختبارات التوافق
    await this.testBackwardCompatibility()
    
    // اختبارات الأداء
    await this.testPerformance()
    
    // عرض النتائج
    this.displayResults()
    
    return this.results
  }

  /**
   * اختبار الأنواع الموحدة
   */
  private async testUnifiedTypes(): Promise<void> {
    const startTime = Date.now()
    
    try {
      // اختبار إنشاء الإعدادات الافتراضية
      const defaultOptions = createDefaultPrintOptions()
      const defaultData = createDefaultPrintData()
      
      // التحقق من الخصائص المطلوبة
      const requiredOptionProps = ['pageSize', 'orientation', 'margins', 'fontSize']
      const requiredDataProps = ['company', 'items', 'totals']
      
      const missingOptionProps = requiredOptionProps.filter(prop => !(prop in defaultOptions))
      const missingDataProps = requiredDataProps.filter(prop => !(prop in defaultData))
      
      if (missingOptionProps.length > 0 || missingDataProps.length > 0) {
        throw new Error(`خصائص مفقودة: ${[...missingOptionProps, ...missingDataProps].join(', ')}`)
      }
      
      this.addResult('testUnifiedTypes', true, 'تم اختبار الأنواع الموحدة بنجاح', Date.now() - startTime, {
        defaultOptions,
        defaultData
      })
      
    } catch (error) {
      this.addResult('testUnifiedTypes', false, `فشل اختبار الأنواع الموحدة: ${error}`, Date.now() - startTime)
    }
  }

  /**
   * اختبار MasterPrintService
   */
  private async testMasterPrintService(): Promise<void> {
    const startTime = Date.now()
    
    try {
      // اختبار إنشاء المثيل
      const service = MasterPrintService.getInstance()
      if (!service) {
        throw new Error('فشل في إنشاء مثيل MasterPrintService')
      }
      
      // اختبار الحصول على الإعدادات الافتراضية
      const defaultOptions = service.getDefaultOptions()
      if (!defaultOptions || typeof defaultOptions !== 'object') {
        throw new Error('فشل في الحصول على الإعدادات الافتراضية')
      }
      
      // اختبار تحديث الإعدادات
      service.updateSettings({ primaryColor: '#ff0000' })
      
      // اختبار إنشاء HTML
      const testData = createDefaultPrintData()
      const testOptions = createDefaultPrintOptions()
      
      const html = await service.generateHTML(testData, testOptions)
      if (!html || typeof html !== 'string' || html.length < 100) {
        throw new Error('فشل في إنشاء HTML صالح')
      }
      
      this.addResult('testMasterPrintService', true, 'تم اختبار MasterPrintService بنجاح', Date.now() - startTime, {
        htmlLength: html.length,
        defaultOptions
      })
      
    } catch (error) {
      this.addResult('testMasterPrintService', false, `فشل اختبار MasterPrintService: ${error}`, Date.now() - startTime)
    }
  }

  /**
   * اختبار إعدادات الطباعة
   */
  private async testPrintSettings(): Promise<void> {
    const startTime = Date.now()
    
    try {
      // اختبار الحصول على الإعدادات من قاعدة البيانات
      let savedSettings = null
      try {
        savedSettings = await window.electronAPI?.invoke('get-print-settings')
      } catch (error) {
        // قد لا تكون متاحة في بيئة الاختبار
        console.warn('لا يمكن الوصول لقاعدة البيانات في بيئة الاختبار')
      }
      
      // اختبار دمج الإعدادات
      const defaultOptions = createDefaultPrintOptions()
      const mergedSettings = { ...defaultOptions, primaryColor: '#00ff00' }
      
      if (!mergedSettings.primaryColor || mergedSettings.primaryColor !== '#00ff00') {
        throw new Error('فشل في دمج الإعدادات')
      }
      
      this.addResult('testPrintSettings', true, 'تم اختبار إعدادات الطباعة بنجاح', Date.now() - startTime, {
        savedSettings: savedSettings ? 'متاح' : 'غير متاح',
        mergedSettings
      })
      
    } catch (error) {
      this.addResult('testPrintSettings', false, `فشل اختبار إعدادات الطباعة: ${error}`, Date.now() - startTime)
    }
  }

  /**
   * اختبار أنواع المستندات المختلفة
   */
  private async testDocumentTypes(): Promise<void> {
    const startTime = Date.now()
    
    try {
      const documentTypes: Array<UnifiedPrintOptions['type']> = [
        'invoice', 'receipt', 'order', 'report', 'certificate', 'statement'
      ]
      
      const testData = createDefaultPrintData()
      const results: any = {}
      
      for (const type of documentTypes) {
        try {
          const options: UnifiedPrintOptions = {
            ...createDefaultPrintOptions(),
            type
          }
          
          const html = await this.printService.generateHTML(testData, options)
          results[type] = {
            success: true,
            htmlLength: html.length
          }
        } catch (error) {
          results[type] = {
            success: false,
            error: error instanceof Error ? error.message : 'خطأ غير معروف'
          }
        }
      }
      
      const successCount = Object.values(results).filter((r: any) => r.success).length
      const totalCount = documentTypes.length
      
      if (successCount < totalCount) {
        throw new Error(`فشل في ${totalCount - successCount} من ${totalCount} أنواع المستندات`)
      }
      
      this.addResult('testDocumentTypes', true, `تم اختبار ${successCount}/${totalCount} أنواع المستندات بنجاح`, Date.now() - startTime, results)
      
    } catch (error) {
      this.addResult('testDocumentTypes', false, `فشل اختبار أنواع المستندات: ${error}`, Date.now() - startTime)
    }
  }

  /**
   * اختبار التوافق مع الأنظمة القديمة
   */
  private async testBackwardCompatibility(): Promise<void> {
    const startTime = Date.now()
    
    try {
      // محاكاة الطباعة السريعة (بدون طباعة فعلية)
      const quickPrintTest = async () => {
        // لا نستدعي الطباعة الفعلية في الاختبار
        return true
      }
      
      const quickPrintResult = await quickPrintTest()
      
      this.addResult('testBackwardCompatibility', quickPrintResult, 'تم اختبار التوافق مع الأنظمة القديمة', Date.now() - startTime, {
        quickPrint: quickPrintResult
      })
      
    } catch (error) {
      this.addResult('testBackwardCompatibility', false, `فشل اختبار التوافق: ${error}`, Date.now() - startTime)
    }
  }

  /**
   * اختبار الأداء
   */
  private async testPerformance(): Promise<void> {
    const startTime = Date.now()
    
    try {
      const testData = createDefaultPrintData()
      const testOptions = createDefaultPrintOptions()
      
      // اختبار سرعة إنشاء HTML
      const htmlStartTime = Date.now()
      const html = await this.printService.generateHTML(testData, testOptions)
      const htmlDuration = Date.now() - htmlStartTime
      
      // اختبار الذاكرة (تقريبي)
      const htmlSize = new Blob([html]).size
      
      const performanceMetrics = {
        htmlGenerationTime: htmlDuration,
        htmlSize,
        isAcceptablePerformance: htmlDuration < 1000 && htmlSize < 1024 * 1024 // أقل من ثانية و 1MB
      }
      
      this.addResult('testPerformance', performanceMetrics.isAcceptablePerformance, 
        `اختبار الأداء: ${htmlDuration}ms، ${Math.round(htmlSize/1024)}KB`, 
        Date.now() - startTime, performanceMetrics)
      
    } catch (error) {
      this.addResult('testPerformance', false, `فشل اختبار الأداء: ${error}`, Date.now() - startTime)
    }
  }

  /**
   * إضافة نتيجة اختبار
   */
  private addResult(testName: string, success: boolean, message: string, duration: number, details?: any): void {
    this.results.push({
      testName,
      success,
      message,
      duration,
      details
    })
    
    const status = success ? '✅' : '❌'
    console.log(`${status} ${testName}: ${message} (${duration}ms)`)
  }

  /**
   * عرض النتائج النهائية
   */
  private displayResults(): void {
    const totalTests = this.results.length
    const passedTests = this.results.filter(r => r.success).length
    const failedTests = totalTests - passedTests
    
    console.log('\n📊 نتائج الاختبار الشامل:')
    console.log(`✅ نجح: ${passedTests}`)
    console.log(`❌ فشل: ${failedTests}`)
    console.log(`📈 معدل النجاح: ${Math.round((passedTests / totalTests) * 100)}%`)
    
    if (failedTests > 0) {
      console.log('\n❌ الاختبارات الفاشلة:')
      this.results.filter(r => !r.success).forEach(result => {
        console.log(`  - ${result.testName}: ${result.message}`)
      })
    }
    
    Logger.info('PrintSystemTester', 'تم إكمال الاختبار الشامل', {
      totalTests,
      passedTests,
      failedTests,
      successRate: Math.round((passedTests / totalTests) * 100)
    })
  }

  /**
   * الحصول على تقرير مفصل
   */
  public getDetailedReport(): string {
    const totalTests = this.results.length
    const passedTests = this.results.filter(r => r.success).length
    
    let report = `# تقرير الاختبار الشامل لنظام الطباعة\n\n`
    report += `**التاريخ**: ${new Date().toLocaleString('ar-SA')}\n`
    report += `**إجمالي الاختبارات**: ${totalTests}\n`
    report += `**النجح**: ${passedTests}\n`
    report += `**الفاشل**: ${totalTests - passedTests}\n`
    report += `**معدل النجاح**: ${Math.round((passedTests / totalTests) * 100)}%\n\n`
    
    report += `## تفاصيل الاختبارات\n\n`
    
    this.results.forEach(result => {
      const status = result.success ? '✅' : '❌'
      report += `### ${status} ${result.testName}\n`
      report += `- **النتيجة**: ${result.message}\n`
      report += `- **المدة**: ${result.duration}ms\n`
      if (result.details) {
        report += `- **التفاصيل**: \`${JSON.stringify(result.details, null, 2)}\`\n`
      }
      report += `\n`
    })
    
    return report
  }
}

/**
 * تشغيل الاختبار الشامل
 */
export const runPrintSystemTest = async (): Promise<TestResult[]> => {
  const tester = new PrintSystemTester()
  return await tester.runAllTests()
}

/**
 * تشغيل اختبار سريع
 */
export const runQuickTest = async (): Promise<boolean> => {
  try {
    const service = MasterPrintService.getInstance()
    const defaultOptions = service.getDefaultOptions()
    const testData = createDefaultPrintData()
    
    await service.generateHTML(testData, defaultOptions)
    
    console.log('✅ الاختبار السريع نجح')
    return true
  } catch (error) {
    console.error('❌ الاختبار السريع فشل:', error)
    return false
  }
}
