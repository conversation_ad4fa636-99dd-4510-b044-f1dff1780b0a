import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Timeline,
  Calendar,
  Badge,
  List,
  Avatar,
  Button,
  Modal,
  Typography,
  Space,
  Tag,
  Alert,
  Divider
} from 'antd'
import {
  BellOutlined,
  CalendarOutlined,
  CameraOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import { NotificationService, NotificationData } from '../../common/NotificationService'
import { CalendarService, CalendarEvent } from '../../common/CalendarService'
import dayjs from 'dayjs'

// TODO: استخدام SimpleImageService بدلاً من ImageService القديم
interface ImageData {
  id: string
  category: string
  size: number
}

const { Title, Text } = Typography

interface PaintEnhancedFeaturesProps {
  onBack: () => void
}

const PaintEnhancedFeatures: React.FC<PaintEnhancedFeaturesProps> = ({ onBack }) => {
  const [notifications, setNotifications] = useState<NotificationData[]>([])
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [images, setImages] = useState<ImageData[]>([])
  const [_selectedDate, setSelectedDate] = useState(dayjs())
  const [statsModalVisible, setStatsModalVisible] = useState(false)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = () => {
    // تحميل التنبيهات
    const allNotifications = NotificationService.getAllNotifications()
    const paintNotifications = allNotifications.filter(n => n.category === 'paint')
    setNotifications(paintNotifications)

    // تحميل الأحداث
    const allEvents = CalendarService.getAllEvents()
    const paintEvents = allEvents.filter(e => e.category === 'paint')
    setEvents(paintEvents)

    // تحميل الصور
    // TODO: استخدام SimpleImageService لجلب الصور
    const paintImages: ImageData[] = []
    setImages(paintImages)
  }

  // إحصائيات التنبيهات
  const notificationStats = {
    total: notifications.length,
    pending: notifications.filter(n => n.status === 'pending').length,
    sent: notifications.filter(n => n.status === 'sent').length,
    dismissed: notifications.filter(n => n.status === 'dismissed').length
  }

  // إحصائيات الأحداث
  const eventStats = {
    total: events.length,
    thisMonth: events.filter(e => dayjs(e.date).month() === dayjs().month()).length,
    upcoming: events.filter(e => dayjs(e.date).isAfter(dayjs()) && e.status === 'pending').length,
    overdue: events.filter(e => dayjs(e.date).isBefore(dayjs()) && e.status === 'pending').length
  }

  // إحصائيات الصور
  const imageStats = {
    total: images.length,
    totalSize: images.reduce((sum, img) => sum + img.size, 0),
    thisMonth: images.filter(img => dayjs(img.uploaded_at).month() === dayjs().month()).length,
    byType: images.reduce((acc, img) => {
      acc[img.reference_type || 'other'] = (acc[img.reference_type || 'other'] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }

  // عرض محتوى التقويم
  const dateCellRender = (date: dayjs.Dayjs) => {
    const dateStr = date.format('YYYY-MM-DD')
    const dayEvents = events.filter(e => e.date === dateStr)
    const dayNotifications = notifications.filter(n => n.date === dateStr)

    if (dayEvents.length === 0 && dayNotifications.length === 0) return null

    return (
      <div style={{ fontSize: '12px' }}>
        {dayEvents.map(event => (
          <div key={event.id} style={{ marginBottom: '2px' }}>
            <Badge 
              status={event.status === 'completed' ? 'success' : 'processing'} 
              text={event.title.slice(0, 10) + '...'}
            />
          </div>
        ))}
        {dayNotifications.map(notification => (
          <div key={notification.id} style={{ marginBottom: '2px' }}>
            <Badge 
              status="warning" 
              text="تنبيه"
            />
          </div>
        ))}
      </div>
    )
  }

  // التنبيهات الأخيرة
  const recentNotifications = notifications
    .sort((a, b) => dayjs(b.created_at).diff(dayjs(a.created_at)))
    .slice(0, 5)

  // الأحداث القادمة
  const upcomingEvents = events
    .filter(e => dayjs(e.date).isAfter(dayjs()) && e.status === 'pending')
    .sort((a, b) => dayjs(a.date).diff(dayjs(b.date)))
    .slice(0, 5)

  // الصور الأخيرة
  const recentImages = images
    .sort((a, b) => dayjs(b.uploaded_at).diff(dayjs(a.uploaded_at)))
    .slice(0, 6)

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2} style={{ margin: 0, color: '#ff7875' }}>
            المميزات المحسنة لقسم الدهان
          </Title>
          <Text type="secondary">
            نظام التنبيهات، التقويم، وإدارة الصور
          </Text>
        </div>
        <Space>
          <Button onClick={() => setStatsModalVisible(true)}>
            إحصائيات تفصيلية
          </Button>
          <Button onClick={onBack}>
            العودة
          </Button>
        </Space>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="التنبيهات المعلقة"
              value={notificationStats.pending}
              prefix={<BellOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="الأحداث القادمة"
              value={eventStats.upcoming}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="إجمالي الصور"
              value={imageStats.total}
              prefix={<CameraOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="الأحداث المتأخرة"
              value={eventStats.overdue}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* التقويم */}
        <Col xs={24} lg={12}>
          <Card title="التقويم" style={{ height: '500px' }}>
            <Calendar
              fullscreen={false}
              dateCellRender={dateCellRender}
              onSelect={(date) => setSelectedDate(date)}
            />
          </Card>
        </Col>

        {/* التنبيهات الأخيرة */}
        <Col xs={24} lg={12}>
          <Card title="التنبيهات الأخيرة" style={{ height: '500px', overflow: 'auto' }}>
            <List
              dataSource={recentNotifications}
              renderItem={(notification) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        icon={<BellOutlined />} 
                        style={{ 
                          backgroundColor: notification.status === 'pending' ? '#faad14' : '#52c41a' 
                        }}
                      />
                    }
                    title={notification.title}
                    description={
                      <div>
                        <div>{notification.message}</div>
                        <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                          <ClockCircleOutlined style={{ marginLeft: '4px' }} />
                          {notification.date} في {notification.time}
                          <Tag 
                            color={notification.status === 'pending' ? 'orange' : 'green'}
                            style={{ marginRight: '8px' }}
                          >
                            {notification.status === 'pending' ? 'معلق' : 'مرسل'}
                          </Tag>
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* الأحداث القادمة */}
        <Col xs={24} lg={12}>
          <Card title="الأحداث القادمة" style={{ height: '400px', overflow: 'auto' }}>
            <Timeline>
              {upcomingEvents.map((event) => (
                <Timeline.Item
                  key={event.id}
                  dot={<CalendarOutlined style={{ fontSize: '16px' }} />}
                  color={event.type === 'delivery' ? 'green' : 'blue'}
                >
                  <div>
                    <strong>{event.title}</strong>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {event.date} {event.time && `في ${event.time}`}
                    </div>
                    {event.description && (
                      <div style={{ fontSize: '12px', marginTop: '4px' }}>
                        {event.description}
                      </div>
                    )}
                  </div>
                </Timeline.Item>
              ))}
              {upcomingEvents.length === 0 && (
                <Timeline.Item dot={<InfoCircleOutlined />}>
                  <Text type="secondary">لا توجد أحداث قادمة</Text>
                </Timeline.Item>
              )}
            </Timeline>
          </Card>
        </Col>

        {/* الصور الأخيرة */}
        <Col xs={24} lg={12}>
          <Card title="الصور الأخيرة" style={{ height: '400px', overflow: 'auto' }}>
            <Row gutter={[8, 8]}>
              {recentImages.map((image) => (
                <Col span={8} key={image.id}>
                  <div style={{ textAlign: 'center' }}>
                    <img
                      src={image.thumbnailUrl || image.url}
                      alt={image.name}
                      style={{
                        width: '100%',
                        height: '80px',
                        objectFit: 'cover',
                        borderRadius: '4px',
                        border: '1px solid #d9d9d9'
                      }}
                    />
                    <div style={{ fontSize: '10px', marginTop: '4px', color: '#666' }}>
                      {image.name.length > 15 ? image.name.slice(0, 15) + '...' : image.name}
                    </div>
                  </div>
                </Col>
              ))}
              {recentImages.length === 0 && (
                <Col span={24}>
                  <div style={{ textAlign: 'center', padding: '40px', color: '#ccc' }}>
                    <CameraOutlined style={{ fontSize: '48px' }} />
                    <div style={{ marginTop: '16px' }}>لا توجد صور</div>
                  </div>
                </Col>
              )}
            </Row>
          </Card>
        </Col>
      </Row>

      {/* مودال الإحصائيات التفصيلية */}
      <Modal
        title="إحصائيات تفصيلية"
        open={statsModalVisible}
        onCancel={() => setStatsModalVisible(false)}
        footer={null}
        width={800}
      >
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card title="التنبيهات">
              <Statistic title="الإجمالي" value={notificationStats.total} />
              <Divider />
              <Statistic title="معلقة" value={notificationStats.pending} />
              <Statistic title="مرسلة" value={notificationStats.sent} />
              <Statistic title="مرفوضة" value={notificationStats.dismissed} />
            </Card>
          </Col>
          <Col span={8}>
            <Card title="الأحداث">
              <Statistic title="الإجمالي" value={eventStats.total} />
              <Divider />
              <Statistic title="هذا الشهر" value={eventStats.thisMonth} />
              <Statistic title="قادمة" value={eventStats.upcoming} />
              <Statistic title="متأخرة" value={eventStats.overdue} />
            </Card>
          </Col>
          <Col span={8}>
            <Card title="الصور">
              <Statistic title="الإجمالي" value={imageStats.total} />
              <Divider />
              <Statistic
                title="الحجم الإجمالي"
                value={`${(imageStats.totalSize / 1024 / 1024).toFixed(2)} MB`}
              />
              <Statistic title="هذا الشهر" value={imageStats.thisMonth} />
            </Card>
          </Col>
        </Row>
      </Modal>
    </div>
  )
}

export default PaintEnhancedFeatures
