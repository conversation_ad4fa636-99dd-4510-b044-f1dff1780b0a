import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Button, Space, Statistic, Menu } from 'antd'
import {
  ShoppingCartOutlined,
  UserOutlined,
  FileTextOutlined,
  DollarOutlined,
  Bar<PERSON>hartOutlined,
  ArrowLeftOutlined,
  CreditCardOutlined
} from '@ant-design/icons'
import SupplierManagement from './SupplierManagement'
import PurchaseOrderManagement from './PurchaseOrderManagement'
import PurchaseInvoiceManagement from './PurchaseInvoiceManagement'
import SupplierPaymentManagement from './SupplierPaymentManagement'
import PurchasePaymentManagement from './PurchasePaymentManagement'
import {
  PurchasesBySupplierReport,
  PurchasesByItemReport,
  SupplierPayablesReport,
  PurchaseAnalysisReport,
  CostAnalysisReport
} from '../reports'
import { SafeLogger as Logger } from '../../utils/logger'

interface PurchaseManagementProps {
  onBack: () => void
  initialView?: ActiveView
}

type ActiveView = 'main' | 'suppliers' | 'orders' | 'invoices' | 'payments' | 'finance-payments' | 'reports' |
  'purchases-by-supplier' | 'purchases-by-item' | 'supplier-payables' | 'purchase-analysis' | 'cost-analysis'

const PurchaseManagement: React.FC<PurchaseManagementProps> = ({ onBack, initialView = 'main' }) => {
  const [activeView, setActiveView] = useState<ActiveView>(initialView as ActiveView)

  // تحديث activeView عندما يتغير initialView من الخارج
  useEffect(() => {
    Logger.info('PurchaseManagement', '🚀 تم تحميل مكون إدارة المشتريات')
    Logger.info('PurchaseManagement', '📋 العرض الأولي:', initialView)
    setActiveView(initialView as ActiveView)
  }, [initialView])

  // تسجيل تغيير العرض النشط
  useEffect(() => {
    Logger.info('PurchaseManagement', '🔄 تم تغيير العرض النشط إلى:', activeView)
  }, [activeView])

  const renderContent = () => {
    Logger.info('PurchaseManagement', '🎨 عرض المحتوى للعرض:', activeView)

    try {
      switch (activeView) {
        case 'suppliers':
          Logger.info('PurchaseManagement', '👥 عرض إدارة الموردين')
          return <SupplierManagement onBack={() => setActiveView('main')} />
        case 'orders':
          Logger.info('PurchaseManagement', '📋 عرض إدارة أوامر الشراء')
          return <PurchaseOrderManagement onBack={() => setActiveView('main')} />
        case 'invoices':
          Logger.info('PurchaseManagement', '🧾 عرض إدارة فواتير الشراء')
          return <PurchaseInvoiceManagement onBack={() => setActiveView('main')} />
        case 'payments':
          Logger.info('PurchaseManagement', '💰 عرض إدارة مدفوعات الموردين')
          return <SupplierPaymentManagement onBack={() => setActiveView('main')} />
        case 'finance-payments':
          Logger.info('PurchaseManagement', '🏦 عرض إدارة المدفوعات المالية')
          return <PurchasePaymentManagement onBack={() => setActiveView('main')} />
      case 'reports':
        return (
          <div style={{ padding: '24px' }}>
            <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <h2 style={{ margin: 0, color: '#1890ff' }}>📊 تقارير المشتريات</h2>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => setActiveView('main')}
                size="large"
              >
                رجوع
              </Button>
            </div>

            <Row gutter={[24, 24]}>
              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ height: '180px' }}
                  styles={{ body: { padding: '20px', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' } }}
                  onClick={() => setActiveView('purchases-by-supplier')}
                >
                  <div>
                    <UserOutlined style={{ fontSize: '32px', color: '#1890ff', marginBottom: '12px' }} />
                    <h3 style={{ margin: 0, fontSize: '16px' }}>تقرير المشتريات حسب المورد</h3>
                    <p style={{ color: '#666', margin: '8px 0 0 0', fontSize: '14px' }}>
                      إجمالي المشتريات والمدفوعات لكل مورد
                    </p>
                  </div>
                  <Button type="primary" size="small">عرض التقرير</Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ height: '180px' }}
                  styles={{ body: { padding: '20px', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' } }}
                  onClick={() => setActiveView('purchases-by-item')}
                >
                  <div>
                    <ShoppingCartOutlined style={{ fontSize: '32px', color: '#52c41a', marginBottom: '12px' }} />
                    <h3 style={{ margin: 0, fontSize: '16px' }}>تقرير المشتريات حسب الصنف</h3>
                    <p style={{ color: '#666', margin: '8px 0 0 0', fontSize: '14px' }}>
                      تحليل المشتريات بالكميات والأسعار
                    </p>
                  </div>
                  <Button type="primary" size="small">عرض التقرير</Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ height: '180px' }}
                  styles={{ body: { padding: '20px', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' } }}
                  onClick={() => setActiveView('supplier-payables')}
                >
                  <div>
                    <DollarOutlined style={{ fontSize: '32px', color: '#fa8c16', marginBottom: '12px' }} />
                    <h3 style={{ margin: 0, fontSize: '16px' }}>تقرير مديونيات الموردين</h3>
                    <p style={{ color: '#666', margin: '8px 0 0 0', fontSize: '14px' }}>
                      المديونيات والحدود الائتمانية
                    </p>
                  </div>
                  <Button type="primary" size="small">عرض التقرير</Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ height: '180px' }}
                  styles={{ body: { padding: '20px', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' } }}
                  onClick={() => setActiveView('purchase-analysis')}
                >
                  <div>
                    <BarChartOutlined style={{ fontSize: '32px', color: '#722ed1', marginBottom: '12px' }} />
                    <h3 style={{ margin: 0, fontSize: '16px' }}>تحليل أداء المشتريات</h3>
                    <p style={{ color: '#666', margin: '8px 0 0 0', fontSize: '14px' }}>
                      مؤشرات الأداء والنمو
                    </p>
                  </div>
                  <Button type="primary" size="small">عرض التقرير</Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ height: '180px' }}
                  styles={{ body: { padding: '20px', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' } }}
                  onClick={() => setActiveView('cost-analysis')}
                >
                  <div>
                    <FileTextOutlined style={{ fontSize: '32px', color: '#eb2f96', marginBottom: '12px' }} />
                    <h3 style={{ margin: 0, fontSize: '16px' }}>تحليل التكاليف</h3>
                    <p style={{ color: '#666', margin: '8px 0 0 0', fontSize: '14px' }}>
                      التكاليف المباشرة وغير المباشرة
                    </p>
                  </div>
                  <Button type="primary" size="small">عرض التقرير</Button>
                </Card>
              </Col>
            </Row>
          </div>
        )
      case 'purchases-by-supplier':
        return <PurchasesBySupplierReport />
      case 'purchases-by-item':
        return <PurchasesByItemReport />
      case 'supplier-payables':
        return <SupplierPayablesReport />
      case 'purchase-analysis':
        return <PurchaseAnalysisReport />
      case 'cost-analysis':
        return <CostAnalysisReport />
      default:
        return (
          <div style={{ padding: '24px' }}>
            <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <h1 style={{ margin: 0, color: '#1890ff', fontSize: '28px' }}>
                  <ShoppingCartOutlined style={{ marginLeft: '12px' }} />
                  إدارة المشتريات
                </h1>
                <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '16px' }}>
                  نّام شامل لإدارة المشتريات والموردين والمدفوعات
                </p>
              </div>
              <Button 
                icon={<ArrowLeftOutlined />} 
                onClick={onBack}
                size="large"
              >
                رجوع للقائمة الرئيسية
              </Button>
            </div>

            {/* إحصائيات سريعة */}
            <Row gutter={[16, 16]} style={{ marginBottom: '32px' }}>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="إجمالي الموردين"
                    value={0}
                    valueStyle={{ color: '#1890ff' }}
                    prefix={<UserOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="أوامر الشراء النشطة"
                    value={0}
                    valueStyle={{ color: '#fa8c16' }}
                    prefix={<FileTextOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="فواتير معلقة"
                    value={0}
                    valueStyle={{ color: '#ff4d4f' }}
                    prefix={<FileTextOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card>
                  <Statistic
                    title="إجمالي المدفوعات (₪)"
                    value={0}
                    valueStyle={{ color: '#52c41a' }}
                    prefix={<DollarOutlined />}
                    precision={0}
                  />
                </Card>
              </Col>
            </Row>

            {/* بطاقات الوحدات الفرعية */}
            <Row gutter={[24, 24]}>
              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ 
                    height: '200px',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    border: 'none',
                    borderRadius: '12px'
                  }}
                  styles={{ body: {
                    padding: '24px',
                    color: 'white',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between'
                  } }}
                  onClick={() => setActiveView('suppliers')}
                >
                  <div>
                    <UserOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>إدارة الموردين</h3>
                    <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                      إضافة وتعديل بيانات الموردين وشروط التعامل
                    </p>
                  </div>
                  <Button 
                    type="primary" 
                    ghost 
                    style={{ alignSelf: 'flex-start' }}
                  >
                    إدارة الموردين
                  </Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ 
                    height: '200px',
                    background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                    border: 'none',
                    borderRadius: '12px'
                  }}
                  styles={{ body: {
                    padding: '24px',
                    color: 'white',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between'
                  } }}
                  onClick={() => setActiveView('orders')}
                >
                  <div>
                    <ShoppingCartOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>أوامر الشراء</h3>
                    <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                      إنشاء ومتابعة أوامر الشراء من الموردين
                    </p>
                  </div>
                  <Button 
                    type="primary" 
                    ghost 
                    style={{ alignSelf: 'flex-start' }}
                  >
                    إدارة الأوامر
                  </Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ 
                    height: '200px',
                    background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                    border: 'none',
                    borderRadius: '12px'
                  }}
                  styles={{ body: {
                    padding: '24px',
                    color: 'white',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between'
                  } }}
                  onClick={() => setActiveView('invoices')}
                >
                  <div>
                    <FileTextOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>فواتير الشراء</h3>
                    <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                      إنشاء ومتابعة فواتير الشراء وتحديث المخزون
                    </p>
                  </div>
                  <Button 
                    type="primary" 
                    ghost 
                    style={{ alignSelf: 'flex-start' }}
                  >
                    إدارة الفواتير
                  </Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ 
                    height: '200px',
                    background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                    border: 'none',
                    borderRadius: '12px'
                  }}
                  styles={{ body: {
                    padding: '24px',
                    color: 'white',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between'
                  } }}
                  onClick={() => setActiveView('payments')}
                >
                  <div>
                    <DollarOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>مدفوعات الموردين</h3>
                    <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                      تسجيل ومتابعة مدفوعات الموردين والفواتير
                    </p>
                  </div>
                  <Button 
                    type="primary" 
                    ghost 
                    style={{ alignSelf: 'flex-start' }}
                  >
                    إدارة المدفوعات
                  </Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ 
                    height: '200px',
                    background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                    border: 'none',
                    borderRadius: '12px'
                  }}
                  styles={{ body: {
                    padding: '24px',
                    color: 'white',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between'
                  } }}
                  onClick={() => setActiveView('reports')}
                >
                  <div>
                    <BarChartOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>تقارير المشتريات</h3>
                    <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                      تقارير شاملة عن المشتريات والموردين والمدفوعات
                    </p>
                  </div>
                  <Button 
                    type="primary" 
                    ghost 
                    style={{ alignSelf: 'flex-start' }}
                  >
                    عرض التقارير
                  </Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{
                    height: '200px',
                    background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                    border: 'none',
                    borderRadius: '12px'
                  }}
                  styles={{ body: {
                    padding: '24px',
                    color: '#333',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between'
                  } }}
                  onClick={() => setActiveView('finance-payments')}
                >
                  <div>
                    <CreditCardOutlined style={{ fontSize: '48px', marginBottom: '16px', color: '#1890ff' }} />
                    <h3 style={{ color: '#333', margin: 0, fontSize: '20px' }}>المدفوعات المالية</h3>
                    <p style={{ color: '#666', margin: '8px 0 0 0' }}>
                      ربط الفواتير بالنّام المالي والشيكات
                    </p>
                  </div>
                  <Button
                    type="primary"
                    style={{ alignSelf: 'flex-start' }}
                  >
                    إدارة المدفوعات المالية
                  </Button>
                </Card>
              </Col>
            </Row>

            {/* نصائح وإرشادات */}
            <Card 
              title="نصائح لإدارة المشتريات بفعالية" 
              style={{ marginTop: '32px' }}
              headStyle={{ background: '#f0f2f5', color: '#1890ff' }}
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <h4>🎯 أفضل الممارسات:</h4>
                  <ul style={{ paddingRight: '20px' }}>
                    <li>تحديث بيانات الموردين بانتّام</li>
                    <li>مراجعة أوامر الشراء قبل الإرسال</li>
                    <li>متابعة تواريخ استحقاق الفواتير</li>
                    <li>توثيق جميع المدفوعات بالمراجع</li>
                  </ul>
                </Col>
                <Col xs={24} md={12}>
                  <h4>📊 مؤشرات الأداء:</h4>
                  <ul style={{ paddingRight: '20px' }}>
                    <li>معدل تسليم الموردين في الوقت المحدد</li>
                    <li>متوسط فترة الدفع للموردين</li>
                    <li>نسبة الخصومات المحصلة</li>
                    <li>تكلفة الشراء مقارنة بالميزانية</li>
                  </ul>
                </Col>
              </Row>
            </Card>
          </div>
        )
    }
    } catch (error) {
      Logger.error('PurchaseManagement', '❌ خطأ في عرض المحتوى:', error instanceof Error ? error : new Error(String(error)))
      return (
        <div style={{ padding: '24px', textAlign: 'center' }}>
          <Card>
            <h3 style={{ color: '#ff4d4f' }}>حدث خطأ في تحميل قسم المشتريات</h3>
            <p>يرجى إعادة المحاولة أو الاتصال بالدعم الفني</p>
            <Button type="primary" onClick={() => setActiveView('main')}>
              العودة للقائمة الرئيسية
            </Button>
          </Card>
        </div>
      )
    }
  }

  return renderContent()
}

export default PurchaseManagement
