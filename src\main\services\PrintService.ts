import { BrowserWindow } from 'electron'
import * as fs from 'fs'
// import * as path from 'path' // غير مستخدم حالياً
import { Logger } from '../utils/logger'

// استخدام الأنواع الموحدة مع دعم للأنواع القديمة للتوافق
export interface PrintOptions {
  format?: 'A4' | 'A3' | 'Letter'
  orientation?: 'portrait' | 'landscape'
  margins?: {
    top?: number
    bottom?: number
    left?: number
    right?: number
  }
  includeHeader?: boolean
  includeFooter?: boolean
  includeLogo?: boolean
  customCSS?: string
}

// واجهة موحدة للطباعة في العملية الرئيسية
export interface MainProcessPrintOptions {
  pageSize?: 'A4' | 'A5' | 'A3' | 'Letter' | 'Legal'
  orientation?: 'portrait' | 'landscape'
  margins?: {
    top: number
    right: number
    bottom: number
    left: number
  }
  quality?: 'draft' | 'normal' | 'high'
  copies?: number
  silent?: boolean
  printBackground?: boolean
  color?: boolean
}

export interface CompanyInfo {
  name: string
  nameEn: string
  address: string
  phone: string
  email: string
  website: string
  logoPath?: string
}

export class PrintService {
  private static instance: PrintService
  private companyInfo: CompanyInfo

  private constructor() {
    this.companyInfo = {
      name: 'ZET.IA',
      nameEn: 'ZET.IA - Accounting & Production System',
      address: 'فلسطين - غزة',
      phone: '**********',
      email: '<EMAIL>',
      website: 'www.zetia.com',
      logoPath: 'assets/company-logo.png'
    }
  }

  public static getInstance(): PrintService {
    if (!PrintService.instance) {
      PrintService.instance = new PrintService()
    }
    return PrintService.instance
  }

  // تحديث معلومات الشركة
  public updateCompanyInfo(info: Partial<CompanyInfo>): void {
    this.companyInfo = { ...this.companyInfo, ...info }
  }

  // إنشاء ترويسة موحدة
  private generateHeader(reportTitle: string, reportSubtitle?: string): string {
    const currentDate = new Date().toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })

    return `
      <div class="report-header">
        <div class="header-content">
          <div class="company-logo">
            ${this.companyInfo.logoPath ? `<img src="${this.companyInfo.logoPath}" alt="شعار الشركة" />` : ''}
          </div>
          <div class="company-info">
            <h1 class="company-name">${this.companyInfo.name}</h1>
            <h2 class="company-name-en">${this.companyInfo.nameEn}</h2>
            <div class="company-details">
              <p><i class="icon-location"></i> ${this.companyInfo.address}</p>
              <p><i class="icon-phone"></i> ${this.companyInfo.phone} | <i class="icon-email"></i> ${this.companyInfo.email}</p>
              <p><i class="icon-web"></i> ${this.companyInfo.website}</p>
            </div>
          </div>
        </div>
        <div class="report-title-section">
          <h2 class="report-title">${reportTitle}</h2>
          ${reportSubtitle ? `<h3 class="report-subtitle">${reportSubtitle}</h3>` : ''}
          <div class="report-date">تاريخ التقرير: ${currentDate}</div>
        </div>
      </div>
    `
  }

  // إنشاء تذييل موحد
  private generateFooter(pageInfo?: string): string {
    const currentDateTime = new Date().toLocaleString('ar-EG')
    
    return `
      <div class="report-footer">
        <div class="footer-content">
          <div class="footer-left">
            <p>تم إنشاء هذا التقرير بواسطة نّام المحاسبة والإنتاج</p>
            <p>تاريخ ووقت الطباعة: ${currentDateTime}</p>
          </div>
          <div class="footer-center">
            <p>${this.companyInfo.name}</p>
            <p>${this.companyInfo.phone} | ${this.companyInfo.email}</p>
          </div>
          <div class="footer-right">
            ${pageInfo ? `<p>${pageInfo}</p>` : ''}
            <p>صفحة <span class="page-number"></span> من <span class="total-pages"></span></p>
          </div>
        </div>
      </div>
    `
  }

  // إنشاء CSS موحد للتقارير
  private generateReportCSS(): string {
    return `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Cairo', 'Arial', sans-serif;
          direction: rtl;
          background: white;
          color: #333;
          line-height: 1.6;
        }

        .report-container {
          max-width: 100%;
          margin: 0 auto;
          padding: 20px;
        }

        /* ترويسة التقرير */
        .report-header {
          border-bottom: 3px solid #1890ff;
          margin-bottom: 30px;
          padding-bottom: 20px;
        }

        .header-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;
        }

        .company-logo img {
          max-height: 80px;
          max-width: 120px;
          object-fit: contain;
        }

        .company-info {
          text-align: center;
          flex: 1;
        }

        .company-name {
          font-size: 28px;
          font-weight: 700;
          color: #1890ff;
          margin-bottom: 5px;
        }

        .company-name-en {
          font-size: 18px;
          font-weight: 400;
          color: #666;
          margin-bottom: 15px;
        }

        .company-details p {
          font-size: 14px;
          color: #666;
          margin: 3px 0;
        }

        .report-title-section {
          text-align: center;
          background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
          padding: 20px;
          border-radius: 8px;
        }

        .report-title {
          font-size: 24px;
          font-weight: 700;
          color: #1890ff;
          margin-bottom: 8px;
        }

        .report-subtitle {
          font-size: 16px;
          font-weight: 400;
          color: #666;
          margin-bottom: 10px;
        }

        .report-date {
          font-size: 14px;
          color: #999;
          font-weight: 600;
        }

        /* محتوى التقرير */
        .report-content {
          margin: 30px 0;
        }

        /* الجداول */
        .report-table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          border-radius: 8px;
          overflow: hidden;
        }

        .report-table th {
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          color: white;
          padding: 15px 12px;
          text-align: center;
          font-weight: 600;
          font-size: 14px;
        }

        .report-table td {
          padding: 12px;
          text-align: center;
          border-bottom: 1px solid #f0f0f0;
          font-size: 13px;
        }

        .report-table tbody tr:nth-child(even) {
          background-color: #fafafa;
        }

        .report-table tbody tr:hover {
          background-color: #e6f7ff;
        }

        /* الإحصائيات */
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin: 30px 0;
        }

        .stat-card {
          background: white;
          border: 1px solid #e8e8e8;
          border-radius: 8px;
          padding: 20px;
          text-align: center;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #1890ff;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }

        /* تذييل التقرير */
        .report-footer {
          border-top: 2px solid #1890ff;
          margin-top: 40px;
          padding-top: 20px;
          font-size: 12px;
          color: #666;
        }

        .footer-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .footer-left, .footer-center, .footer-right {
          flex: 1;
        }

        .footer-center {
          text-align: center;
        }

        .footer-right {
          text-align: left;
        }

        /* طباعة */
        @media print {
          body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          
          .report-container {
            padding: 0;
          }
          
          .report-header {
            page-break-inside: avoid;
          }
          
          .report-table {
            page-break-inside: auto;
          }
          
          .report-table tr {
            page-break-inside: avoid;
            page-break-after: auto;
          }
          
          .report-footer {
            position: fixed;
            bottom: 0;
            width: 100%;
          }
        }

        /* ألوان مخصصة */
        .text-success { color: #52c41a; }
        .text-warning { color: #faad14; }
        .text-danger { color: #ff4d4f; }
        .text-info { color: #1890ff; }
        
        .bg-success { background-color: #f6ffed; }
        .bg-warning { background-color: #fffbe6; }
        .bg-danger { background-color: #fff2f0; }
        .bg-info { background-color: #e6f7ff; }
      </style>
    `
  }

  // إنشاء HTML للتقرير
  public generateReportHTML(
    reportTitle: string,
    reportContent: string,
    options: PrintOptions = {},
    reportSubtitle?: string
  ): string {
    const css = this.generateReportCSS()
    const header = options.includeHeader !== false ? this.generateHeader(reportTitle, reportSubtitle) : ''
    const footer = options.includeFooter !== false ? this.generateFooter() : ''

    return `
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${reportTitle}</title>
        ${css}
        ${options.customCSS ? `<style>${options.customCSS}</style>` : ''}
      </head>
      <body>
        <div class="report-container">
          ${header}
          <div class="report-content">
            ${reportContent}
          </div>
          ${footer}
        </div>
      </body>
      </html>
    `
  }

  // طباعة التقرير
  public async printReport(
    reportTitle: string,
    reportContent: string,
    options: PrintOptions = {}
  ): Promise<{ success: boolean; message: string }> {
    try {
      const html = this.generateReportHTML(reportTitle, reportContent, options)

      // إنشاء نافذة مخفية للطباعة
      const printWindow = new BrowserWindow({
        width: 800,
        height: 600,
        show: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true
        }
      })

      // تحميل المحتوى
      await printWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(html)}`)

      // إعدادات الطباعة
      const printOptions = {
        silent: false,
        printBackground: true,
        color: true,
        margins: {
          marginType: 'custom' as const,
          top: options.margins?.top || 1,
          bottom: options.margins?.bottom || 1,
          left: options.margins?.left || 1,
          right: options.margins?.right || 1
        },
        landscape: options.orientation === 'landscape',
        scaleFactor: 100,
        pagesPerSheet: 1,
        collate: false,
        copies: 1,
        duplexMode: 'simplex' as const,
        dpi: {
          horizontal: 300,
          vertical: 300
        },
        header: '',
        footer: ''
      }

      // طباعة التقرير
      printWindow.webContents.print(printOptions, (success, failureReason) => {
        if (!success) {
          Logger.error('PrintService', 'فشل في الطباعة:', failureReason)
        }
        // إغلاق النافذة
        printWindow.close()
      })

      return {
        success: true,
        message: 'تم إرسال التقرير للطباعة بنجاح'
      }
    } catch (error) {
      Logger.error('PrintService', 'خطأ في طباعة التقرير:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء طباعة التقرير'
      }
    }
  }

  // حفّ التقرير كـ PDF
  public async saveReportAsPDF(
    reportTitle: string,
    reportContent: string,
    filePath: string,
    options: PrintOptions = {}
  ): Promise<{ success: boolean; message: string; filePath?: string }> {
    try {
      const html = this.generateReportHTML(reportTitle, reportContent, options)

      const printWindow = new BrowserWindow({
        width: 800,
        height: 600,
        show: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true
        }
      })

      await printWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(html)}`)

      const pdfOptions = {
        marginsType: 1,
        pageSize: options.format || 'A4',
        printBackground: true,
        printSelectionOnly: false,
        landscape: options.orientation === 'landscape'
      }

      const data = await printWindow.webContents.printToPDF(pdfOptions)
      fs.writeFileSync(filePath, data)

      printWindow.close()

      return {
        success: true,
        message: 'تم حفّ التقرير كـ PDF بنجاح',
        filePath
      }
    } catch (error) {
      Logger.error('PrintService', 'خطأ في حفّ PDF:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء حفّ التقرير كـ PDF'
      }
    }
  }

  // معاينة التقرير
  public async previewReport(
    reportTitle: string,
    reportContent: string,
    options: PrintOptions = {}
  ): Promise<BrowserWindow> {
    const html = this.generateReportHTML(reportTitle, reportContent, options)

    const previewWindow = new BrowserWindow({
      width: 1000,
      height: 800,
      title: `معاينة التقرير - ${reportTitle}`,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    })

    await previewWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(html)}`)

    return previewWindow
  }

  /**
   * طباعة محسنة باستخدام الأنواع الموحدة
   */
  public async printWithUnifiedOptions(
    html: string,
    options: MainProcessPrintOptions = {}
  ): Promise<{ success: boolean; message: string }> {
    try {
      const printWindow = new BrowserWindow({
        width: 800,
        height: 600,
        show: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true
        }
      })

      // تحميل المحتوى
      await printWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(html)}`)

      // إعدادات الطباعة المحسنة
      const printOptions = {
        silent: options.silent || false,
        printBackground: options.printBackground ?? true,
        color: options.color ?? true,
        margins: {
          marginType: 'custom' as const,
          top: options.margins?.top || 20,
          bottom: options.margins?.bottom || 20,
          left: options.margins?.left || 20,
          right: options.margins?.right || 20
        },
        landscape: options.orientation === 'landscape',
        scaleFactor: 100,
        pagesPerSheet: 1,
        collate: false,
        copies: options.copies || 1,
        duplexMode: 'simplex' as const,
        dpi: {
          horizontal: options.quality === 'high' ? 600 : options.quality === 'normal' ? 300 : 150,
          vertical: options.quality === 'high' ? 600 : options.quality === 'normal' ? 300 : 150
        },
        header: '',
        footer: ''
      }

      // تنفيذ الطباعة
      await printWindow.webContents.print(printOptions)

      // إغلاق النافذة
      printWindow.close()

      Logger.info('PrintService', 'تم إرسال المستند للطباعة بنجاح')
      return {
        success: true,
        message: 'تم إرسال المستند للطباعة بنجاح'
      }

    } catch (error) {
      Logger.error('PrintService', 'خطأ في الطباعة:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء الطباعة'
      }
    }
  }

  /**
   * حفظ كـ PDF باستخدام الأنواع الموحدة
   */
  public async saveAsPDFWithUnifiedOptions(
    html: string,
    filePath: string,
    options: MainProcessPrintOptions = {}
  ): Promise<{ success: boolean; message: string; filePath?: string }> {
    try {
      const printWindow = new BrowserWindow({
        width: 800,
        height: 600,
        show: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true
        }
      })

      await printWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(html)}`)

      const pdfOptions = {
        marginsType: 1,
        pageSize: options.pageSize || 'A4',
        printBackground: options.printBackground ?? true,
        printSelectionOnly: false,
        landscape: options.orientation === 'landscape'
      }

      const data = await printWindow.webContents.printToPDF(pdfOptions)
      fs.writeFileSync(filePath, data)

      printWindow.close()

      Logger.info('PrintService', `تم حفظ PDF في: ${filePath}`)
      return {
        success: true,
        message: 'تم حفظ المستند كـ PDF بنجاح',
        filePath
      }
    } catch (error) {
      Logger.error('PrintService', 'خطأ في حفظ PDF:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء حفظ المستند كـ PDF'
      }
    }
  }

  /**
   * تحويل الخيارات القديمة إلى الأنواع الموحدة
   */
  private convertLegacyOptions(options: PrintOptions): MainProcessPrintOptions {
    return {
      pageSize: options.format === 'A3' ? 'A3' : options.format === 'Letter' ? 'Letter' : 'A4',
      orientation: options.orientation || 'portrait',
      margins: {
        top: options.margins?.top || 20,
        right: 20,
        bottom: options.margins?.bottom || 20,
        left: options.margins?.left || 20
      },
      quality: 'normal',
      copies: 1,
      silent: false,
      printBackground: true,
      color: true
    }
  }
}
