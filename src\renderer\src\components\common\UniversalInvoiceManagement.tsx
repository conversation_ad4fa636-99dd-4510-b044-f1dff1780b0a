import React, { useState, useEffect, useCallback, useMemo } from 'react'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Space,
  Select,
  DatePicker,
  Card,
  Row,
  Col,
  Statistic,
  Tag,
  Popconfirm,
  Divider,
  Typography,
  App,
  Tabs,
  Badge
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FileTextOutlined,
  SearchOutlined,
  DownloadOutlined,
  CopyOutlined,
  SwapOutlined,
  PrinterOutlined,
  DollarOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import { useCurrentUser } from '../../utils/permissions'
import { formatCurrency } from '../../utils/settings'
// import InvoicePrintButton from './InvoicePrintButton' // تم تعطيل مؤقتاً لاختبار المشكلة

const { Option } = Select
const { Search } = Input
const { TabPane } = Tabs

// أنواع البيانات
export type InvoiceType = 'sales' | 'purchase' | 'paint' | 'service'
export type InvoiceStatus = 'draft' | 'pending' | 'sent' | 'paid' | 'partial' | 'overdue' | 'cancelled'

export interface UniversalInvoice {
  id: number
  invoice_number: string
  invoice_type: InvoiceType
  entity_id: number
  entity_name: string
  entity_type: 'customer' | 'supplier'
  invoice_date: string
  due_date?: string
  status: InvoiceStatus
  subtotal: number
  discount_amount: number
  tax_amount: number
  total_amount: number
  paid_amount: number
  remaining_amount: number
  payment_method?: string
  notes?: string
  created_by: number
  created_at: string
  items?: any[]
}

export interface UniversalInvoiceFilters {
  invoice_type?: InvoiceType
  entity_id?: number
  status?: InvoiceStatus[]
  date_from?: string
  date_to?: string
  search_text?: string
  page?: number
  page_size?: number
}

interface UniversalInvoiceManagementProps {
  defaultInvoiceType?: InvoiceType
  showTypeFilter?: boolean
  _showEntityFilter?: boolean
  allowCreate?: boolean
  allowEdit?: boolean
  allowDelete?: boolean
  onInvoiceSelect?: (invoice: UniversalInvoice) => void
  customActions?: (invoice: UniversalInvoice) => React.ReactNode
}

const UniversalInvoiceManagement: React.FC<UniversalInvoiceManagementProps> = ({
  defaultInvoiceType,
  showTypeFilter = true,
  _showEntityFilter = true,
  allowCreate = true,
  allowEdit = true,
  allowDelete = true,
  onInvoiceSelect,
  customActions
}) => {
  const { id: userId } = useCurrentUser()
  const { message: messageApi } = App.useApp()

  // الحالات الأساسية
  const [invoices, setInvoices] = useState<UniversalInvoice[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingInvoice, setEditingInvoice] = useState<UniversalInvoice | null>(null)
  const [form] = Form.useForm()

  // حالات الفلترة والبحث
  const [filters, setFilters] = useState<UniversalInvoiceFilters>({
    invoice_type: defaultInvoiceType,
    page: 1,
    page_size: 50
  })
  const [searchText, setSearchText] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<InvoiceStatus[]>([])

  // حالات الإحصائيات
  const [statistics, setStatistics] = useState<any>({
    total_invoices: 0,
    total_amount: 0,
    paid_amount: 0,
    remaining_amount: 0,
    by_status: {},
    by_type: {}
  })

  // تحميل البيانات
  const loadInvoices = useCallback(async () => {
    setLoading(true)
    try {
      const currentFilters = {
        ...filters,
        search_text: searchText || undefined,
        status: selectedStatus.length > 0 ? selectedStatus : undefined
      }

      if (window.electronAPI) {
        const response = await window.electronAPI.getUniversalInvoices(currentFilters)
        if (response.success) {
          setInvoices(response.data.invoices || [])
          
          // تحميل الإحصائيات
          const statsResponse = await window.electronAPI.getUniversalInvoiceStatistics(currentFilters)
          if (statsResponse.success) {
            setStatistics(statsResponse.data)
          }
        } else {
          messageApi.error(response.message || 'فشل في تحميل الفواتير')
        }
      }
    } catch (error) {
      messageApi.error('حدث خطأ في تحميل الفواتير')
    } finally {
      setLoading(false)
    }
  }, [filters, searchText, selectedStatus, messageApi])

  // تحميل البيانات عند التغيير
  useEffect(() => {
    loadInvoices()
  }, [loadInvoices])

  // إنشاء فاتورة جديدة
  const handleCreate = () => {
    setEditingInvoice(null)
    form.resetFields()
    setModalVisible(true)
  }

  // تعديل فاتورة
  const handleEdit = (invoice: UniversalInvoice) => {
    setEditingInvoice(invoice)
    form.setFieldsValue({
      ...invoice,
      invoice_date: dayjs(invoice.invoice_date),
      due_date: invoice.due_date ? dayjs(invoice.due_date) : undefined
    })
    setModalVisible(true)
  }

  // حذف فاتورة
  const handleDelete = async (invoice: UniversalInvoice) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.deleteUniversalInvoice(
          invoice.id,
          invoice.invoice_type
        )
        if (response.success) {
          messageApi.success('تم حذف الفاتورة بنجاح')
          loadInvoices()
        } else {
          messageApi.error(response.message || 'فشل في حذف الفاتورة')
        }
      }
    } catch (error) {
      messageApi.error('حدث خطأ في حذف الفاتورة')
    }
  }

  // نسخ فاتورة
  const handleDuplicate = async (invoice: UniversalInvoice) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.duplicateInvoice(
          invoice.id,
          invoice.invoice_type
        )
        if (response.success) {
          messageApi.success('تم نسخ الفاتورة بنجاح')
          loadInvoices()
        } else {
          messageApi.error(response.message || 'فشل في نسخ الفاتورة')
        }
      }
    } catch (error) {
      messageApi.error('حدث خطأ في نسخ الفاتورة')
    }
  }

  // حفظ الفاتورة
  const handleSave = async (values: any) => {
    try {
      const invoiceData = {
        ...values,
        invoice_date: values.invoice_date.format('YYYY-MM-DD'),
        due_date: values.due_date?.format('YYYY-MM-DD'),
        created_by: userId
      }

      if (window.electronAPI) {
        let response
        if (editingInvoice) {
          response = await window.electronAPI.updateUniversalInvoice(
            editingInvoice.invoice_type,
            editingInvoice.id,
            invoiceData
          )
        } else {
          response = await window.electronAPI.createUniversalInvoice(invoiceData)
        }

        if (response.success) {
          messageApi.success(editingInvoice ? 'تم تحديث الفاتورة بنجاح' : 'تم إنشاء الفاتورة بنجاح')
          setModalVisible(false)
          loadInvoices()
        } else {
          messageApi.error(response.message || 'فشل في حفظ الفاتورة')
        }
      }
    } catch (error) {
      messageApi.error('حدث خطأ في حفظ الفاتورة')
    }
  }

  // تصدير البيانات
  const handleExport = async (format: 'excel' | 'pdf' | 'csv') => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.exportInvoices(filters, format)
        if (response.success) {
          messageApi.success(`تم تصدير ${response.data.invoices.length} فاتورة بصيغة ${format}`)
        } else {
          messageApi.error(response.message || 'فشل في التصدير')
        }
      }
    } catch (error) {
      messageApi.error('حدث خطأ في التصدير')
    }
  }

  // الحصول على لون الحالة
  const getStatusColor = (status: InvoiceStatus): string => {
    const colors = {
      draft: 'default',
      pending: 'processing',
      sent: 'warning',
      paid: 'success',
      partial: 'orange',
      overdue: 'error',
      cancelled: 'default'
    }
    return colors[status] || 'default'
  }

  // الحصول على نص الحالة
  const getStatusText = (status: InvoiceStatus): string => {
    const texts = {
      draft: 'مسودة',
      pending: 'معلقة',
      sent: 'مرسلة',
      paid: 'مدفوعة',
      partial: 'مدفوعة جزئياً',
      overdue: 'متأخرة',
      cancelled: 'ملغاة'
    }
    return texts[status] || status
  }

  // الحصول على نص نوع الفاتورة
  const getInvoiceTypeText = (type: InvoiceType): string => {
    const texts = {
      sales: 'مبيعات',
      purchase: 'مشتريات',
      paint: 'دهان',
      service: 'خدمات'
    }
    return texts[type] || type
  }

  // أعمدة الجدول
  const columns = useMemo(() => [
    {
      title: 'رقم الفاتورة',
      dataIndex: 'invoice_number',
      key: 'invoice_number',
      width: 150,
      render: (text: string, record: UniversalInvoice) => (
        <Button
          type="link"
          onClick={() => onInvoiceSelect?.(record)}
          style={{ padding: 0, height: 'auto' }}
        >
          {text}
        </Button>
      )
    },
    {
      title: 'النوع',
      dataIndex: 'invoice_type',
      key: 'invoice_type',
      width: 100,
      render: (type: InvoiceType) => (
        <Tag color="blue">{getInvoiceTypeText(type)}</Tag>
      ),
      filters: [
        { text: 'مبيعات', value: 'sales' },
        { text: 'مشتريات', value: 'purchase' },
        { text: 'دهان', value: 'paint' },
        { text: 'خدمات', value: 'service' }
      ],
      onFilter: (value: any, record: UniversalInvoice) => record.invoice_type === value
    },
    {
      title: 'العميل/المورد',
      dataIndex: 'entity_name',
      key: 'entity_name',
      width: 200
    },
    {
      title: 'تاريخ الفاتورة',
      dataIndex: 'invoice_date',
      key: 'invoice_date',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
      sorter: (a: UniversalInvoice, b: UniversalInvoice) => 
        dayjs(a.invoice_date).unix() - dayjs(b.invoice_date).unix()
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: InvoiceStatus) => (
        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
      ),
      filters: [
        { text: 'مسودة', value: 'draft' },
        { text: 'معلقة', value: 'pending' },
        { text: 'مرسلة', value: 'sent' },
        { text: 'مدفوعة', value: 'paid' },
        { text: 'مدفوعة جزئياً', value: 'partial' },
        { text: 'متأخرة', value: 'overdue' },
        { text: 'ملغاة', value: 'cancelled' }
      ],
      onFilter: (value: any, record: UniversalInvoice) => record.status === value
    },
    {
      title: 'المبلغ الإجمالي',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 150,
      render: (amount: number) => formatCurrency(amount),
      sorter: (a: UniversalInvoice, b: UniversalInvoice) => a.total_amount - b.total_amount
    },
    {
      title: 'المبلغ المدفوع',
      dataIndex: 'paid_amount',
      key: 'paid_amount',
      width: 150,
      render: (amount: number) => formatCurrency(amount)
    },
    {
      title: 'المبلغ المتبقي',
      dataIndex: 'remaining_amount',
      key: 'remaining_amount',
      width: 150,
      render: (amount: number) => (
        <span style={{ color: amount > 0 ? '#ff4d4f' : '#52c41a' }}>
          {formatCurrency(amount)}
        </span>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 200,
      render: (_: any, record: UniversalInvoice) => (
        <Space size="small">
          <Button type="default" size="small">
            طباعة
          </Button>
          
          {allowEdit && (
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          )}
          
          <Button
            size="small"
            icon={<CopyOutlined />}
            onClick={() => handleDuplicate(record)}
            title="نسخ الفاتورة"
          />
          
          {allowDelete && (
            <Popconfirm
              title="هل أنت متأكد من حذف هذه الفاتورة؟"
              onConfirm={() => handleDelete(record)}
              okText="نعم"
              cancelText="لا"
            >
              <Button
                danger
                size="small"
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          )}
          
          {customActions?.(record)}
        </Space>
      )
    }
  ], [allowEdit, allowDelete, onInvoiceSelect, customActions])

  return (
    <div style={{ padding: '24px' }}>
      {/* الإحصائيات */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="إجمالي الفواتير"
              value={statistics.total_invoices}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="إجمالي المبلغ"
              value={statistics.total_amount}
              formatter={(value) => formatCurrency(Number(value))}
              prefix={<DollarOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="المبلغ المدفوع"
              value={statistics.paid_amount}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="المبلغ المتبقي"
              value={statistics.remaining_amount}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* شريط الأدوات */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Space>
              <Search
                placeholder="البحث في الفواتير..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onSearch={loadInvoices}
                style={{ width: 300 }}
                enterButton={<SearchOutlined />}
              />
              
              {showTypeFilter && (
                <Select
                  placeholder="نوع الفاتورة"
                  value={filters.invoice_type}
                  onChange={(value) => setFilters(prev => ({ ...prev, invoice_type: value }))}
                  allowClear
                  style={{ width: 150 }}
                >
                  <Option value="sales">مبيعات</Option>
                  <Option value="purchase">مشتريات</Option>
                  <Option value="paint">دهان</Option>
                  <Option value="service">خدمات</Option>
                </Select>
              )}
              
              <Select
                mode="multiple"
                placeholder="الحالة"
                value={selectedStatus}
                onChange={setSelectedStatus}
                style={{ width: 200 }}
              >
                <Option value="draft">مسودة</Option>
                <Option value="pending">معلقة</Option>
                <Option value="sent">مرسلة</Option>
                <Option value="paid">مدفوعة</Option>
                <Option value="partial">مدفوعة جزئياً</Option>
                <Option value="overdue">متأخرة</Option>
                <Option value="cancelled">ملغاة</Option>
              </Select>
            </Space>
          </Col>
          
          <Col>
            <Space>
              {allowCreate && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreate}
                >
                  فاتورة جديدة
                </Button>
              )}
              
              <Button
                icon={<DownloadOutlined />}
                onClick={() => handleExport('excel')}
              >
                تصدير Excel
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* جدول الفواتير */}
      <Card>
        <Table
          columns={columns}
          dataSource={invoices}
          rowKey="id"
          loading={loading}
          pagination={{
            current: filters.page,
            pageSize: filters.page_size,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} من ${total} فاتورة`,
            onChange: (page, pageSize) => 
              setFilters(prev => ({ ...prev, page, page_size: pageSize }))
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* نافذة إنشاء/تعديل الفاتورة */}
      <Modal
        title={editingInvoice ? 'تعديل الفاتورة' : 'فاتورة جديدة'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="invoice_type"
                label="نوع الفاتورة"
                rules={[{ required: true, message: 'نوع الفاتورة مطلوب' }]}
              >
                <Select placeholder="اختر نوع الفاتورة">
                  <Option value="sales">مبيعات</Option>
                  <Option value="purchase">مشتريات</Option>
                  <Option value="paint">دهان</Option>
                  <Option value="service">خدمات</Option>
                </Select>
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="entity_id"
                label="العميل/المورد"
                rules={[{ required: true, message: 'العميل أو المورد مطلوب' }]}
              >
                <Select placeholder="اختر العميل أو المورد">
                  {/* سيتم تحميل القائمة ديناميكياً */}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="invoice_date"
                label="تاريخ الفاتورة"
                rules={[{ required: true, message: 'تاريخ الفاتورة مطلوب' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="due_date"
                label="تاريخ الاستحقاق"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="notes"
            label="ملاحظات"
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default UniversalInvoiceManagement
