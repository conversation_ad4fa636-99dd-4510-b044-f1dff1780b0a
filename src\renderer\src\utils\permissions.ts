import { useState, useEffect } from 'react'
import { SafeLogger as Logger } from './logger'
// نّام الصلاحيات للتطبيق

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  SALES_USER = 'sales_user',
  ACCOUNTANT = 'accountant',
  VIEWER = 'viewer'
}

export enum Permission {
  // صلاحيات العملاء
  CUSTOMERS_VIEW = 'customers_view',
  CUSTOMERS_CREATE = 'customers_create',
  CUSTOMERS_EDIT = 'customers_edit',
  CUSTOMERS_DELETE = 'customers_delete',
  
  // صلاحيات المبيعات
  SALES_VIEW = 'sales_view',
  SALES_CREATE = 'sales_create',
  SALES_EDIT = 'sales_edit',
  SALES_DELETE = 'sales_delete',
  
  // صلاحيات الفواتير
  INVOICES_VIEW = 'invoices_view',
  INVOICES_CREATE = 'invoices_create',
  INVOICES_EDIT = 'invoices_edit',
  INVOICES_DELETE = 'invoices_delete',
  INVOICES_PRINT = 'invoices_print',
  
  // صلاحيات المدفوعات
  PAYMENTS_VIEW = 'payments_view',
  PAYMENTS_CREATE = 'payments_create',
  PAYMENTS_EDIT = 'payments_edit',
  PAYMENTS_DELETE = 'payments_delete',
  
  // صلاحيات التقارير
  REPORTS_VIEW = 'reports_view',
  REPORTS_EXPORT = 'reports_export',
  
  // صلاحيات النّام
  SYSTEM_SETTINGS = 'system_settings',
  USER_MANAGEMENT = 'user_management',
  BACKUP_RESTORE = 'backup_restore'
}

// تعريف الصلاحيات لكل دور
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    // المدير له جميع الصلاحيات
    ...Object.values(Permission)
  ],
  
  [UserRole.MANAGER]: [
    // المدير التنفيذي
    Permission.CUSTOMERS_VIEW,
    Permission.CUSTOMERS_CREATE,
    Permission.CUSTOMERS_EDIT,
    Permission.SALES_VIEW,
    Permission.SALES_CREATE,
    Permission.SALES_EDIT,
    Permission.INVOICES_VIEW,
    Permission.INVOICES_CREATE,
    Permission.INVOICES_EDIT,
    Permission.INVOICES_PRINT,
    Permission.PAYMENTS_VIEW,
    Permission.PAYMENTS_CREATE,
    Permission.PAYMENTS_EDIT,
    Permission.REPORTS_VIEW,
    Permission.REPORTS_EXPORT
  ],
  
  [UserRole.SALES_USER]: [
    // موّف المبيعات
    Permission.CUSTOMERS_VIEW,
    Permission.CUSTOMERS_CREATE,
    Permission.CUSTOMERS_EDIT,
    Permission.SALES_VIEW,
    Permission.SALES_CREATE,
    Permission.SALES_EDIT,
    Permission.INVOICES_VIEW,
    Permission.INVOICES_CREATE,
    Permission.INVOICES_EDIT,
    Permission.INVOICES_PRINT,
    Permission.REPORTS_VIEW
  ],
  
  [UserRole.ACCOUNTANT]: [
    // المحاسب
    Permission.CUSTOMERS_VIEW,
    Permission.SALES_VIEW,
    Permission.INVOICES_VIEW,
    Permission.INVOICES_EDIT,
    Permission.INVOICES_PRINT,
    Permission.PAYMENTS_VIEW,
    Permission.PAYMENTS_CREATE,
    Permission.PAYMENTS_EDIT,
    Permission.REPORTS_VIEW,
    Permission.REPORTS_EXPORT
  ],
  
  [UserRole.VIEWER]: [
    // مستخدم للعرض فقط
    Permission.CUSTOMERS_VIEW,
    Permission.SALES_VIEW,
    Permission.INVOICES_VIEW,
    Permission.PAYMENTS_VIEW,
    Permission.REPORTS_VIEW
  ]
}

// واجهة المستخدم
export interface User {
  id: number
  username: string
  name: string
  email?: string
  role: UserRole
  isActive: boolean
  permissions?: Permission[] // صلاحيات إضافية مخصصة
  createdAt: string
  lastLogin?: string
}

// المستخدم الحالي (يمكن تحديثه من localStorage أو API)
let currentUser: User | null = null

// دالة لتعيين المستخدم الحالي
export function setCurrentUser(user: User | null) {
  currentUser = user
  if (user) {
    localStorage.setItem('currentUser', JSON.stringify(user))
  } else {
    localStorage.removeItem('currentUser')
  }
}

// دالة للحصول على المستخدم الحالي
export function getCurrentUser(): User | null {
  if (!currentUser) {
    const stored = localStorage.getItem('currentUser')
    if (stored) {
      try {
        currentUser = JSON.parse(stored)
      } catch (error) {
        Logger.error('Permissions', 'خطأ في تحليل بيانات المستخدم:', error)
        localStorage.removeItem('currentUser')
      }
    }
  }
  return currentUser
}

// دالة للحصول على صلاحيات المستخدم
export function getUserPermissions(user: User): Permission[] {
  const rolePermissions = ROLE_PERMISSIONS[user.role] || []
  const customPermissions = user.permissions || []
  
  // دمج صلاحيات الدور مع الصلاحيات المخصصة
  return [...new Set([...rolePermissions, ...customPermissions])]
}

// دالة للتحقق من صلاحية معينة
export function hasPermission(permission: Permission, user?: User): boolean {
  const targetUser = user || getCurrentUser()
  
  if (!targetUser || !targetUser.isActive) {
    return false
  }
  
  // المدير له جميع الصلاحيات
  if (targetUser.role === UserRole.ADMIN) {
    return true
  }
  
  const userPermissions = getUserPermissions(targetUser)
  return userPermissions.includes(permission)
}

// دالة للتحقق من عدة صلاحيات (يجب أن تكون جميعها متوفرة)
export function hasAllPermissions(permissions: Permission[], user?: User): boolean {
  return permissions.every(permission => hasPermission(permission, user))
}

// دالة للتحقق من أي من الصلاحيات (يكفي واحدة منها)
export function hasAnyPermission(permissions: Permission[], user?: User): boolean {
  return permissions.some(permission => hasPermission(permission, user))
}

// دالة للتحقق من الدور
export function hasRole(role: UserRole, user?: User): boolean {
  const targetUser = user || getCurrentUser()
  return targetUser?.role === role
}

// دالة للتحقق من أي من الأدوار
export function hasAnyRole(roles: UserRole[], user?: User): boolean {
  const targetUser = user || getCurrentUser()
  return targetUser ? roles.includes(targetUser.role) : false
}

// دالة لتسجيل الخروج
export function logout() {
  setCurrentUser(null)
}

// دالة للتحقق من تسجيل الدخول
export function isLoggedIn(): boolean {
  const user = getCurrentUser()
  return user !== null && user.isActive
}

// React Hook للحصول على المستخدم الحالي
export function useCurrentUser() {
  const [user, setUser] = useState<User | null>(getCurrentUser())

  useEffect(() => {
    // تحديث المستخدم عند تغيير localStorage
    const handleStorageChange = () => {
      setUser(getCurrentUser())
    }

    window.addEventListener('storage', handleStorageChange)

    // تحديث دوري للتأكد من صحة البيانات
    const interval = setInterval(() => {
      const currentUser = getCurrentUser()
      setUser(currentUser)
    }, 5000)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      clearInterval(interval)
    }
  }, [])

  return user
}

// دالة لإنشاء مستخدم افتراضي للتطوير
export function createDefaultUser(): User {
  return {
    id: 1,
    username: 'admin',
    name: 'مدير النّام',
    email: '<EMAIL>',
    role: UserRole.ADMIN,
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: new Date().toISOString()
  }
}

// تهيئة المستخدم الافتراضي إذا لم يكن هناك مستخدم
export function initializeDefaultUser() {
  if (!getCurrentUser()) {
    const defaultUser = createDefaultUser()
    setCurrentUser(defaultUser)
    Logger.info('Permissions', 'تم إنشاء مستخدم افتراضي:', defaultUser.name)
  }
}

// أسماء الأدوار بالعربية
export const ROLE_NAMES: Record<UserRole, string> = {
  [UserRole.ADMIN]: 'مدير النّام',
  [UserRole.MANAGER]: 'مدير تنفيذي',
  [UserRole.SALES_USER]: 'موّف مبيعات',
  [UserRole.ACCOUNTANT]: 'محاسب',
  [UserRole.VIEWER]: 'مستخدم عرض'
}

// أسماء الصلاحيات بالعربية
export const PERMISSION_NAMES: Record<Permission, string> = {
  [Permission.CUSTOMERS_VIEW]: 'عرض العملاء',
  [Permission.CUSTOMERS_CREATE]: 'إضافة عملاء',
  [Permission.CUSTOMERS_EDIT]: 'تعديل العملاء',
  [Permission.CUSTOMERS_DELETE]: 'حذف العملاء',
  [Permission.SALES_VIEW]: 'عرض المبيعات',
  [Permission.SALES_CREATE]: 'إنشاء مبيعات',
  [Permission.SALES_EDIT]: 'تعديل المبيعات',
  [Permission.SALES_DELETE]: 'حذف المبيعات',
  [Permission.INVOICES_VIEW]: 'عرض الفواتير',
  [Permission.INVOICES_CREATE]: 'إنشاء فواتير',
  [Permission.INVOICES_EDIT]: 'تعديل الفواتير',
  [Permission.INVOICES_DELETE]: 'حذف الفواتير',
  [Permission.INVOICES_PRINT]: 'طباعة الفواتير',
  [Permission.PAYMENTS_VIEW]: 'عرض المدفوعات',
  [Permission.PAYMENTS_CREATE]: 'إنشاء مدفوعات',
  [Permission.PAYMENTS_EDIT]: 'تعديل المدفوعات',
  [Permission.PAYMENTS_DELETE]: 'حذف المدفوعات',
  [Permission.REPORTS_VIEW]: 'عرض التقارير',
  [Permission.REPORTS_EXPORT]: 'تصدير التقارير',
  [Permission.SYSTEM_SETTINGS]: 'إعدادات النّام',
  [Permission.USER_MANAGEMENT]: 'إدارة المستخدمين',
  [Permission.BACKUP_RESTORE]: 'النسخ الاحتياطي'
}

export default {
  UserRole,
  Permission,
  setCurrentUser,
  getCurrentUser,
  useCurrentUser,
  hasPermission,
  hasAllPermissions,
  hasAnyPermission,
  hasRole,
  hasAnyRole,
  isLoggedIn,
  logout,
  initializeDefaultUser,
  ROLE_NAMES,
  PERMISSION_NAMES
}
