import React, { useState, useEffect } from 'react'
import {
  Card,
  Button,
  Table,
  Space,
  Alert,
  Modal,
  Typography,
  Descriptions,
  Tag,
  message,
  Divider,
  Row,
  Col,
  Statistic
} from 'antd'
import {
  DatabaseOutlined,
  SwapOutlined,
  SearchOutlined,
  ClearOutlined,
  InfoCircleOutlined,
  WarningOutlined
} from '@ant-design/icons'

const { Title, Text, Paragraph } = Typography

interface DatabaseInfo {
  devExists: boolean
  prodExists: boolean
  devPath: string
  prodPath: string
  devSize?: number
  prodSize?: number
  devModified?: string
  prodModified?: string
}

interface DatabaseFile {
  path: string
  exists: boolean
  size?: number
  modified?: string
}

const DataMigration: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [databaseInfo, setDatabaseInfo] = useState<DatabaseInfo | null>(null)
  const [databaseFiles, setDatabaseFiles] = useState<DatabaseFile[]>([])
  const [migrationModalVisible, setMigrationModalVisible] = useState(false)
  const [migrationType, setMigrationType] = useState<'dev-to-prod' | 'prod-to-dev'>('dev-to-prod')

  // تحميل معلومات قواعد البيانات
  const loadDatabaseInfo = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.invoke('check-databases')
      if (response.success) {
        setDatabaseInfo(response.data)
      } else {
        message.error(response.message)
      }
    } catch (error) {
      message.error('خطأ في تحميل معلومات قواعد البيانات')
    } finally {
      setLoading(false)
    }
  }

  // البحث عن ملفات قواعد البيانات
  const findDatabaseFiles = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.invoke('find-database-files')
      if (response.success) {
        setDatabaseFiles(response.data)
      } else {
        message.error(response.message)
      }
    } catch (error) {
      message.error('خطأ في البحث عن ملفات قواعد البيانات')
    } finally {
      setLoading(false)
    }
  }

  // تنفيذ نقل البيانات
  const performMigration = async () => {
    setLoading(true)
    try {
      const endpoint = migrationType === 'dev-to-prod' ? 'migrate-dev-to-prod' : 'migrate-prod-to-dev'
      const response = await window.electronAPI.invoke(endpoint)
      
      if (response.success) {
        message.success(response.message)
        if (response.backupPath) {
          message.info(`تم إنشاء نسخة احتياطية: ${response.backupPath}`)
        }
        await loadDatabaseInfo()
      } else {
        message.error(response.message)
      }
    } catch (error) {
      message.error('خطأ في نقل البيانات')
    } finally {
      setLoading(false)
      setMigrationModalVisible(false)
    }
  }

  // تنّيف النسخ الاحتياطية
  const cleanupBackups = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.invoke('cleanup-old-backups', 7)
      if (response.success) {
        message.success(response.message)
      } else {
        message.error(response.message)
      }
    } catch (error) {
      message.error('خطأ في تنّيف النسخ الاحتياطية')
    } finally {
      setLoading(false)
    }
  }

  // تحميل البيانات عند بدء المكون
  useEffect(() => {
    loadDatabaseInfo()
    findDatabaseFiles()
  }, [])

  // تنسيق حجم الملف
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'غير معروف'
    const kb = bytes / 1024
    if (kb < 1024) return `${kb.toFixed(2)} KB`
    const mb = kb / 1024
    return `${mb.toFixed(2)} MB`
  }

  // تنسيق التاريخ
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'غير معروف'
    return new Date(dateString).toLocaleString('ar-EG')
  }

  // أعمدة جدول ملفات قواعد البيانات
  const columns = [
    {
      title: 'المسار',
      dataIndex: 'path',
      key: 'path',
      ellipsis: true,
    },
    {
      title: 'الحالة',
      dataIndex: 'exists',
      key: 'exists',
      render: (exists: boolean) => (
        <Tag color={exists ? 'green' : 'red'}>
          {exists ? 'موجود' : 'غير موجود'}
        </Tag>
      ),
    },
    {
      title: 'الحجم',
      dataIndex: 'size',
      key: 'size',
      render: (size?: number) => formatFileSize(size),
    },
    {
      title: 'تاريخ التعديل',
      dataIndex: 'modified',
      key: 'modified',
      render: (modified?: string) => formatDate(modified),
    },
  ]

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <DatabaseOutlined /> إدارة نقل البيانات
      </Title>
      
      <Paragraph>
        هذه الصفحة تساعدك في نقل البيانات بين وضع المطور والتطبيق المبني.
        يمكنك مزامنة البيانات والإعدادات بين البيئتين.
      </Paragraph>

      <Alert
        message="تحذير مهم"
        description="تأكد من إغلاق جميع نوافذ التطبيق الأخرى قبل نقل البيانات. سيتم إنشاء نسخة احتياطية تلقائياً."
        type="warning"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* معلومات قواعد البيانات الرئيسية */}
      <Card title="معلومات قواعد البيانات" style={{ marginBottom: 24 }}>
        {databaseInfo && (
          <Row gutter={16}>
            <Col span={12}>
              <Card size="small" title="قاعدة بيانات المطور">
                <Statistic
                  title="الحالة"
                  value={databaseInfo.devExists ? 'موجودة' : 'غير موجودة'}
                  valueStyle={{ color: databaseInfo.devExists ? '#3f8600' : '#cf1322' }}
                />
                {databaseInfo.devExists && (
                  <>
                    <Divider />
                    <Descriptions size="small" column={1}>
                      <Descriptions.Item label="الحجم">
                        {formatFileSize(databaseInfo.devSize)}
                      </Descriptions.Item>
                      <Descriptions.Item label="آخر تعديل">
                        {formatDate(databaseInfo.devModified)}
                      </Descriptions.Item>
                      <Descriptions.Item label="المسار">
                        <Text code copyable ellipsis>
                          {databaseInfo.devPath}
                        </Text>
                      </Descriptions.Item>
                    </Descriptions>
                  </>
                )}
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small" title="قاعدة بيانات الإنتاج">
                <Statistic
                  title="الحالة"
                  value={databaseInfo.prodExists ? 'موجودة' : 'غير موجودة'}
                  valueStyle={{ color: databaseInfo.prodExists ? '#3f8600' : '#cf1322' }}
                />
                {databaseInfo.prodExists && (
                  <>
                    <Divider />
                    <Descriptions size="small" column={1}>
                      <Descriptions.Item label="الحجم">
                        {formatFileSize(databaseInfo.prodSize)}
                      </Descriptions.Item>
                      <Descriptions.Item label="آخر تعديل">
                        {formatDate(databaseInfo.prodModified)}
                      </Descriptions.Item>
                      <Descriptions.Item label="المسار">
                        <Text code copyable ellipsis>
                          {databaseInfo.prodPath}
                        </Text>
                      </Descriptions.Item>
                    </Descriptions>
                  </>
                )}
              </Card>
            </Col>
          </Row>
        )}
        
        <Divider />
        
        <Space>
          <Button
            type="primary"
            icon={<SwapOutlined />}
            onClick={() => {
              setMigrationType('dev-to-prod')
              setMigrationModalVisible(true)
            }}
            disabled={!databaseInfo?.devExists}
          >
            نقل من المطور إلى الإنتاج
          </Button>
          
          <Button
            icon={<SwapOutlined />}
            onClick={() => {
              setMigrationType('prod-to-dev')
              setMigrationModalVisible(true)
            }}
            disabled={!databaseInfo?.prodExists}
          >
            نقل من الإنتاج إلى المطور
          </Button>
          
          <Button
            icon={<SearchOutlined />}
            onClick={loadDatabaseInfo}
            loading={loading}
          >
            تحديث المعلومات
          </Button>
          
          <Button
            icon={<ClearOutlined />}
            onClick={cleanupBackups}
            loading={loading}
          >
            تنّيف النسخ الاحتياطية
          </Button>
        </Space>
      </Card>

      {/* جدول جميع ملفات قواعد البيانات */}
      <Card title="جميع ملفات قواعد البيانات الموجودة">
        <Table
          columns={columns}
          dataSource={databaseFiles.map((file, index) => ({ ...file, key: index }))}
          loading={loading}
          size="small"
          pagination={false}
        />
        
        <Divider />
        
        <Button
          icon={<SearchOutlined />}
          onClick={findDatabaseFiles}
          loading={loading}
        >
          البحث عن ملفات قواعد البيانات
        </Button>
      </Card>

      {/* نافذة تأكيد النقل */}
      <Modal
        title={
          <Space>
            <WarningOutlined style={{ color: '#faad14' }} />
            تأكيد نقل البيانات
          </Space>
        }
        open={migrationModalVisible}
        onOk={performMigration}
        onCancel={() => setMigrationModalVisible(false)}
        confirmLoading={loading}
        okText="تأكيد النقل"
        cancelText="إلغاء"
      >
        <Alert
          message="تحذير"
          description={
            migrationType === 'dev-to-prod'
              ? 'سيتم نقل البيانات من وضع المطور إلى التطبيق المبني. هذا سيستبدل البيانات الحالية في التطبيق المبني.'
              : 'سيتم نقل البيانات من التطبيق المبني إلى وضع المطور. هذا سيستبدل البيانات الحالية في وضع المطور.'
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Paragraph>
          <InfoCircleOutlined /> سيتم إنشاء نسخة احتياطية تلقائياً من البيانات الحالية قبل النقل.
        </Paragraph>
        
        <Paragraph strong>
          هل أنت متأكد من أنك تريد المتابعة؟
        </Paragraph>
      </Modal>
    </div>
  )
}

export default DataMigration
