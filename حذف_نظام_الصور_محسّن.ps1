# ═══════════════════════════════════════════════════════════════
# سكريبت PowerShell المحسّن لحذف نظام الصور بالكامل
# ═══════════════════════════════════════════════════════════════
# الإصدار: 2.0
# التاريخ: 2025-09-30
# الوصف: حذف شامل ومفصل لجميع مكونات نظام الصور
# ═══════════════════════════════════════════════════════════════

# تعطيل رسائل التقدم لتحسين الأداء
$ProgressPreference = 'SilentlyContinue'

# متغيرات الإحصائيات
$deletedFolders = 0
$deletedFiles = 0
$failedDeletions = @()
$totalSize = 0

# دالة لعرض رسالة ملونة
function Write-ColorMessage {
    param(
        [string]$Message,
        [string]$Color = "White",
        [string]$Icon = ""
    )
    if ($Icon) {
        Write-Host "$Icon $Message" -ForegroundColor $Color
    } else {
        Write-Host $Message -ForegroundColor $Color
    }
}

# دالة لعرض خط فاصل
function Write-Separator {
    param([string]$Color = "Cyan")
    Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor $Color
}

# دالة لحذف مجلد
function Remove-FolderSafe {
    param([string]$Path)
    
    if (Test-Path $Path) {
        try {
            $size = (Get-ChildItem -Path $Path -Recurse -File | Measure-Object -Property Length -Sum).Sum
            Remove-Item -Path $Path -Recurse -Force -ErrorAction Stop
            $script:deletedFolders++
            $script:totalSize += $size
            Write-ColorMessage "  ✅ تم حذف: $Path" -Color Green
            return $true
        } catch {
            Write-ColorMessage "  ❌ فشل حذف: $Path - $($_.Exception.Message)" -Color Red
            $script:failedDeletions += $Path
            return $false
        }
    } else {
        Write-ColorMessage "  ⚠️  غير موجود: $Path" -Color Yellow
        return $false
    }
}

# دالة لحذف ملف
function Remove-FileSafe {
    param([string]$Path)
    
    if (Test-Path $Path) {
        try {
            $size = (Get-Item $Path).Length
            Remove-Item -Path $Path -Force -ErrorAction Stop
            $script:deletedFiles++
            $script:totalSize += $size
            Write-ColorMessage "  ✅ تم حذف: $Path" -Color Green
            return $true
        } catch {
            Write-ColorMessage "  ❌ فشل حذف: $Path - $($_.Exception.Message)" -Color Red
            $script:failedDeletions += $Path
            return $false
        }
    } else {
        Write-ColorMessage "  ⚠️  غير موجود: $Path" -Color Yellow
        return $false
    }
}

# دالة لتحويل الحجم إلى صيغة قابلة للقراءة
function Format-FileSize {
    param([long]$Size)
    
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    } elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    } elseif ($Size -gt 1KB) {
        return "{0:N2} KB" -f ($Size / 1KB)
    } else {
        return "$Size بايت"
    }
}

# عرض الترويسة
Clear-Host
Write-Separator -Color Red
Write-ColorMessage "  ⚠️  سكريبت حذف نظام الصور الشامل - الإصدار 2.0" -Color Yellow
Write-Separator -Color Red
Write-Host ""

# عرض الإحصائيات
Write-ColorMessage "📊 الإحصائيات المتوقعة:" -Color Cyan
Write-Host ""
Write-ColorMessage "  • المجلدات الكاملة: 3 مجلدات" -Color White -Icon "📁"
Write-ColorMessage "  • الملفات في common/: 13 ملف" -Color White -Icon "📄"
Write-ColorMessage "  • ملفات أخرى: 7 ملفات" -Color White -Icon "📄"
Write-ColorMessage "  • ملفات الاختبار: 2 ملف" -Color White -Icon "🧪"
Write-ColorMessage "  • ملفات التوثيق: 1 ملف" -Color White -Icon "📚"
Write-ColorMessage "  • الإجمالي: ~26 ملف + 3 مجلدات" -Color Yellow -Icon "📦"
Write-Host ""

# عرض التحذيرات
Write-Separator -Color Red
Write-ColorMessage "  ⚠️  تحذيرات مهمة" -Color Yellow
Write-Separator -Color Red
Write-Host ""
Write-ColorMessage "1. تأكد من عمل نسخة احتياطية كاملة" -Color Red -Icon "⚠️"
Write-ColorMessage "2. هذا الحذف نهائي ولا يمكن التراجع عنه" -Color Red -Icon "⚠️"
Write-ColorMessage "3. ستحتاج لتعديل ملفات أخرى يدوياً بعد الحذف" -Color Red -Icon "⚠️"
Write-ColorMessage "4. تأكد من إغلاق جميع المحررات والتطبيقات" -Color Red -Icon "⚠️"
Write-Host ""

# طلب التأكيد
Write-Separator -Color Yellow
$confirmation = Read-Host "هل أنت متأكد من المتابعة؟ (اكتب 'نعم' بالضبط للمتابعة)"

if ($confirmation -ne "نعم") {
    Write-Host ""
    Write-ColorMessage "✅ تم إلغاء العملية بنجاح" -Color Green
    Write-Host ""
    exit
}

Write-Host ""
Write-ColorMessage "🚀 بدء عملية الحذف..." -Color Green
Write-Host ""

# ═══════════════════════════════════════════════════════════════
# المرحلة 1: حذف المجلدات الكاملة
# ═══════════════════════════════════════════════════════════════

Write-Separator
Write-ColorMessage "  المرحلة 1: حذف المجلدات الكاملة (3 مجلدات)" -Color Green
Write-Separator
Write-Host ""

$folders = @(
    "src/renderer/src/components/images",
    "src/renderer/src/services/images",
    "src/renderer/src/hooks/images"
)

foreach ($folder in $folders) {
    Remove-FolderSafe -Path $folder
}

Write-Host ""

# ═══════════════════════════════════════════════════════════════
# المرحلة 2: حذف ملفات common/
# ═══════════════════════════════════════════════════════════════

Write-Separator
Write-ColorMessage "  المرحلة 2: حذف ملفات common/ (13 ملف)" -Color Green
Write-Separator
Write-Host ""

$commonFiles = @(
    "src/renderer/src/components/common/ImageGallery.tsx",
    "src/renderer/src/components/common/ImageService.ts",
    "src/renderer/src/components/common/ImageSettings.tsx",
    "src/renderer/src/components/common/ImageWithFallback.tsx",
    "src/renderer/src/components/common/CheckImageManager.tsx",
    "src/renderer/src/components/common/ImagePrintTest.tsx",
    "src/renderer/src/components/common/UniversalImagePrint.tsx",
    "src/renderer/src/components/common/UniversalImagePrintDemo.tsx",
    "src/renderer/src/components/common/InvoiceImagesPrintButton.tsx",
    "src/renderer/src/components/common/ProductionOrderImagesPrintButton.tsx",
    "src/renderer/src/components/common/CustomerImagesPrintButton.tsx",
    "src/renderer/src/components/common/SmartImagePrintButton.tsx",
    "src/renderer/src/components/common/README_IMAGE_PRINT.md"
)

foreach ($file in $commonFiles) {
    Remove-FileSafe -Path $file
}

Write-Host ""

# ═══════════════════════════════════════════════════════════════
# المرحلة 3: حذف ملفات أخرى
# ═══════════════════════════════════════════════════════════════

Write-Separator
Write-ColorMessage "  المرحلة 3: حذف ملفات أخرى (7 ملفات)" -Color Green
Write-Separator
Write-Host ""

$otherFiles = @(
    "src/renderer/src/components/examples/ImageSystemExample.tsx",
    "src/renderer/src/components/debug/ImageDebugger.tsx",
    "src/renderer/src/components/inventory/EnhancedItemImagePrint.tsx",
    "src/renderer/src/components/production/furniture/FurnitureImageService.ts",
    "src/renderer/src/services/UniversalImageService.ts",
    "src/renderer/src/utils/imageUtils.ts",
    "src/main/handlers/imageHandlers.ts"
)

foreach ($file in $otherFiles) {
    Remove-FileSafe -Path $file
}

Write-Host ""

# ═══════════════════════════════════════════════════════════════
# المرحلة 4: حذف ملفات الاختبار
# ═══════════════════════════════════════════════════════════════

Write-Separator
Write-ColorMessage "  المرحلة 4: حذف ملفات الاختبار (2 ملف)" -Color Green
Write-Separator
Write-Host ""

$testFiles = @(
    "src/test/image-print-system-test.ts",
    "src/test/run-image-print-test.ts"
)

foreach ($file in $testFiles) {
    Remove-FileSafe -Path $file
}

Write-Host ""

# ═══════════════════════════════════════════════════════════════
# المرحلة 5: حذف ملفات التوثيق
# ═══════════════════════════════════════════════════════════════

Write-Separator
Write-ColorMessage "  المرحلة 5: حذف ملفات التوثيق (5 ملفات)" -Color Green
Write-Separator
Write-Host ""

$docFiles = @(
    "docs/IMAGE_SYSTEM_DATABASE_INTEGRATION_COMPLETE.md",
    "docs/IMAGE_SYSTEM_README.md",
    "docs/IMAGE_SYSTEM_USAGE_EXAMPLES.md",
    "docs/IMAGE_SYSTEM_INTEGRATION_GUIDE.md",
    "docs/IMAGE_PRINT_FIX_SOLUTION.md"
)

foreach ($file in $docFiles) {
    Remove-FileSafe -Path $file
}

Write-Host ""

# ═══════════════════════════════════════════════════════════════
# عرض النتائج النهائية
# ═══════════════════════════════════════════════════════════════

Write-Separator -Color Green
Write-ColorMessage "  ✅ اكتملت عملية الحذف" -Color Green
Write-Separator -Color Green
Write-Host ""

Write-ColorMessage "📊 الإحصائيات النهائية:" -Color Cyan
Write-Host ""
Write-ColorMessage "  • المجلدات المحذوفة: $deletedFolders" -Color White -Icon "📁"
Write-ColorMessage "  • الملفات المحذوفة: $deletedFiles" -Color White -Icon "📄"
Write-ColorMessage "  • الحجم الإجمالي المحرر: $(Format-FileSize $totalSize)" -Color White -Icon "💾"

if ($failedDeletions.Count -gt 0) {
    Write-Host ""
    Write-ColorMessage "  ⚠️  فشل حذف $($failedDeletions.Count) عنصر:" -Color Yellow
    foreach ($failed in $failedDeletions) {
        Write-ColorMessage "    • $failed" -Color Red
    }
}

Write-Host ""
Write-Separator -Color Yellow
Write-ColorMessage "  ⚠️  الخطوات المتبقية (يدوياً)" -Color Yellow
Write-Separator -Color Yellow
Write-Host ""

Write-ColorMessage "1. تعديل src/renderer/src/components/inventory/ItemManagement.tsx" -Color White -Icon "📝"
Write-ColorMessage "   - حذف الاستيرادات (سطر 17، 19)" -Color Gray
Write-ColorMessage "   - حذف الحالات (سطر 60-64)" -Color Gray
Write-ColorMessage "   - حذف الدوال (سطر 226-270)" -Color Gray
Write-ColorMessage "   - حذف الأزرار (سطر 459-471)" -Color Gray
Write-ColorMessage "   - حذف Modal (سطر 936-972)" -Color Gray
Write-Host ""

Write-ColorMessage "2. تعديل src/renderer/src/components/Dashboard.tsx" -Color White -Icon "📝"
Write-ColorMessage "   - حذف الاستيرادات (سطر 85-92، 103-109)" -Color Gray
Write-ColorMessage "   - حذف القوائم (سطر 1077-1090، 1106-1114)" -Color Gray
Write-ColorMessage "   - حذف الحالات (سطر 1393-1450، 1727-1731)" -Color Gray
Write-Host ""

Write-ColorMessage "3. تعديل src/main/main.ts" -Color White -Icon "📝"
Write-ColorMessage "   - حذف الاستيراد (سطر 28)" -Color Gray
Write-ColorMessage "   - حذف إنشاء الجدول (سطر 334-374)" -Color Gray
Write-ColorMessage "   - حذف التسجيل (سطر 528)" -Color Gray
Write-Host ""

Write-ColorMessage "4. تعديل src/main/services/SimpleDatabaseService.ts" -Color White -Icon "📝"
Write-ColorMessage "   - حذف استدعاء createImageTables() (سطر 61)" -Color Gray
Write-ColorMessage "   - حذف الدالة createImageTables() (سطر 318-372)" -Color Gray
Write-Host ""

Write-ColorMessage "5. تعديل src/main/services/ProductionService.ts" -Color White -Icon "📝"
Write-ColorMessage "   - حذف جدول production_order_images (سطر 306-318)" -Color Gray
Write-ColorMessage "   - حذف الدوال (سطر 1594-1892)" -Color Gray
Write-Host ""

Write-ColorMessage "6. تعديل src/renderer/src/types/global.d.ts" -Color White -Icon "📝"
Write-ColorMessage "   - حذف الواجهات (سطر 277-341)" -Color Gray
Write-ColorMessage "   - حذف الدوال (سطر 456-458، 544-548، 837، 849-870)" -Color Gray
Write-Host ""

Write-ColorMessage "7. تعديل src/renderer/src/components/common/index.ts" -Color White -Icon "📝"
Write-ColorMessage "   - حذف التصديرات (سطر 21-42)" -Color Gray
Write-Host ""

Write-ColorMessage "8. تنفيذ سكريبت SQL لحذف الجداول" -Color White -Icon "🗄️"
Write-ColorMessage "   - استخدم ملف: حذف_جداول_الصور.sql" -Color Gray
Write-Host ""

Write-ColorMessage "9. اختبار التطبيق" -Color White -Icon "🧪"
Write-ColorMessage "   - تأكد من عدم وجود أخطاء" -Color Gray
Write-ColorMessage "   - تأكد من عمل جميع الوظائف الأخرى" -Color Gray
Write-Host ""

Write-Separator -Color Cyan
Write-ColorMessage "  ✅ انتهى السكريبت بنجاح" -Color Green
Write-Separator -Color Cyan
Write-Host ""

