import React from 'react'
import { Alert, Button } from 'antd'
import { LockOutlined, UserOutlined } from '@ant-design/icons'
import { Permission, UserRole, hasPermission, hasAnyPermission, hasRole, hasAnyRole, getCurrentUser, PERMISSION_NAMES, ROLE_NAMES } from '../../utils/permissions'

interface PermissionGuardProps {
  children: React.ReactNode
  permission?: Permission
  permissions?: Permission[]
  requireAll?: boolean // إذا كان true، يجب توفر جميع الصلاحيات. إذا كان false، يكفي واحدة
  role?: UserRole
  roles?: UserRole[]
  fallback?: React.ReactNode
  showMessage?: boolean
  disabled?: boolean // إذا كان true، يعرض المحتوى لكن معطل
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  permissions,
  requireAll = true,
  role,
  roles,
  fallback,
  showMessage = true,
  disabled = false
}) => {
  const currentUser = getCurrentUser()

  // التحقق من تسجيل الدخول
  if (!currentUser || !currentUser.isActive) {
    if (showMessage) {
      return (
        <Alert
          message="يجب تسجيل الدخول"
          description="يرجى تسجيل الدخول للوصول إلى هذه الميزة"
          type="warning"
          icon={<UserOutlined />}
          showIcon
          action={
            <Button size="small" type="primary">
              تسجيل الدخول
            </Button>
          }
        />
      )
    }
    return fallback || null
  }

  // التحقق من الأدوار
  let hasRequiredRole = true
  if (role) {
    hasRequiredRole = hasRole(role)
  } else if (roles && roles.length > 0) {
    hasRequiredRole = hasAnyRole(roles)
  }

  // التحقق من الصلاحيات
  let hasRequiredPermission = true
  if (permission) {
    hasRequiredPermission = hasPermission(permission)
  } else if (permissions && permissions.length > 0) {
    if (requireAll) {
      hasRequiredPermission = permissions.every(p => hasPermission(p))
    } else {
      hasRequiredPermission = hasAnyPermission(permissions)
    }
  }

  const hasAccess = hasRequiredRole && hasRequiredPermission

  // إذا لم يكن لديه صلاحية
  if (!hasAccess) {
    if (showMessage) {
      const missingPermissions: string[] = []
      const missingRoles: string[] = []

      // تحديد الصلاحيات المفقودة
      if (permission && !hasPermission(permission)) {
        missingPermissions.push(PERMISSION_NAMES[permission])
      }
      if (permissions) {
        permissions.forEach(p => {
          if (!hasPermission(p)) {
            missingPermissions.push(PERMISSION_NAMES[p])
          }
        })
      }

      // تحديد الأدوار المفقودة
      if (role && !hasRole(role)) {
        missingRoles.push(ROLE_NAMES[role])
      }
      if (roles) {
        roles.forEach(r => {
          if (!hasRole(r)) {
            missingRoles.push(ROLE_NAMES[r])
          }
        })
      }

      let description = 'ليس لديك الصلاحية اللازمة للوصول إلى هذه الميزة.'
      
      if (missingPermissions.length > 0) {
        description += ` الصلاحيات المطلوبة: ${missingPermissions.join(', ')}`
      }
      
      if (missingRoles.length > 0) {
        description += ` الأدوار المطلوبة: ${missingRoles.join(', ')}`
      }

      return (
        <Alert
          message="صلاحية غير كافية"
          description={description}
          type="error"
          icon={<LockOutlined />}
          showIcon
          style={{ margin: '16px 0' }}
        />
      )
    }
    return fallback || null
  }

  // إذا كان معطلاً، عرض المحتوى لكن معطل
  if (disabled) {
    return (
      <div style={{ opacity: 0.5, pointerEvents: 'none', position: 'relative' }}>
        {children}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10
          }}
        >
          <Alert
            message="معطل مؤقتاً"
            type="info"

            showIcon
          />
        </div>
      </div>
    )
  }

  // عرض المحتوى إذا كان لديه الصلاحية
  return <>{children}</>
}

// مكون مبسط للأزرار
interface PermissionButtonProps {
  permission?: Permission
  permissions?: Permission[]
  requireAll?: boolean
  role?: UserRole
  roles?: UserRole[]
  children: React.ReactNode
  [key: string]: any // للسماح بتمرير خصائص أخرى للزر
}

export const PermissionButton: React.FC<PermissionButtonProps> = ({
  permission,
  permissions,
  requireAll = true,
  role,
  roles,
  children,
  ...buttonProps
}) => {
  const currentUser = getCurrentUser()

  // التحقق من الصلاحيات
  let hasAccess = true

  if (!currentUser || !currentUser.isActive) {
    hasAccess = false
  } else {
    // التحقق من الأدوار
    if (role) {
      hasAccess = hasAccess && hasRole(role)
    } else if (roles && roles.length > 0) {
      hasAccess = hasAccess && hasAnyRole(roles)
    }

    // التحقق من الصلاحيات
    if (permission) {
      hasAccess = hasAccess && hasPermission(permission)
    } else if (permissions && permissions.length > 0) {
      if (requireAll) {
        hasAccess = hasAccess && permissions.every(p => hasPermission(p))
      } else {
        hasAccess = hasAccess && hasAnyPermission(permissions)
      }
    }
  }

  if (!hasAccess) {
    return null
  }

  return <Button {...buttonProps}>{children}</Button>
}

// Hook للتحقق من الصلاحيات
export const usePermissions = () => {
  const currentUser = getCurrentUser()

  return {
    user: currentUser,
    hasPermission: (permission: Permission) => hasPermission(permission),
    hasAnyPermission: (permissions: Permission[]) => hasAnyPermission(permissions),
    hasRole: (role: UserRole) => hasRole(role),
    hasAnyRole: (roles: UserRole[]) => hasAnyRole(roles),
    isLoggedIn: currentUser !== null && currentUser.isActive
  }
}

export default PermissionGuard
