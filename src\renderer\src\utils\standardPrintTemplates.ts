/**
 * قوالب الطباعة الموحدة والمعيارية - محدثة ومحسنة
 * قوالب ذكية موحدة تغطي جميع أنواع التقارير والمستندات
 */

export interface StandardPrintTemplate {
  id: string;
  name: string;
  description: string;
  type: 'invoice' | 'receipt' | 'report' | 'certificate' | 'custom';
  category?: string; // فئة فرعية للتقارير (financial, inventory, sales, etc.)
  isDefault: boolean;
  isActive: boolean;
  settings: {
    pageSize: string;
    orientation: string;
    fontSize: number;
    fontFamily: string;
    showHeader: boolean;
    showFooter: boolean;
    showLogo: boolean;
    showSignature: boolean;
    showTerms: boolean;
    marginTop: number;
    marginBottom: number;
    marginLeft: number;
    marginRight: number;
    primaryColor: string;
    secondaryColor: string;
    borderColor?: string;
    // إعدادات إضافية للتقارير
    headerSize?: number;
    lineSpacing?: number;
    tableWidth?: number;
    sectionSpacing?: number;
  };
  // أنواع التقارير المدعومة بهذا القالب
  supportedReportTypes?: string[];
  createdAt: string;
  updatedAt: string;
}

/**
 * القوالب المعيارية الموحدة والمحسنة
 * قوالب ذكية تغطي جميع أنواع المستندات والتقارير
 */
export const STANDARD_PRINT_TEMPLATES: StandardPrintTemplate[] = [
  // ==================== قوالب المستندات الأساسية ====================
  {
    id: 'default-invoice',
    name: 'قالب الفاتورة الشامل',
    description: 'قالب شامل للفواتير مع جميع العناصر والجداول',
    type: 'invoice',
    isDefault: true,
    isActive: true,
    settings: {
      pageSize: 'A4',
      orientation: 'portrait',
      fontSize: 12,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: true,
      showTerms: true,
      marginTop: 20,
      marginBottom: 20,
      marginLeft: 15,
      marginRight: 15,
      primaryColor: '#1890ff',
      secondaryColor: '#f0f2f5',
      borderColor: '#d9d9d9',
      headerSize: 18,
      lineSpacing: 1.5,
      tableWidth: 100,
      sectionSpacing: 15
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'default-receipt',
    name: 'قالب الإيصال الشامل',
    description: 'قالب شامل للإيصالات مع الشعار والجداول',
    type: 'receipt',
    isDefault: true,
    isActive: true,
    settings: {
      pageSize: 'A5',
      orientation: 'portrait',
      fontSize: 11,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: false,
      showTerms: false,
      marginTop: 15,
      marginBottom: 15,
      marginLeft: 10,
      marginRight: 10,
      primaryColor: '#52c41a',
      secondaryColor: '#f6ffed',
      borderColor: '#b7eb8f',
      headerSize: 16,
      lineSpacing: 1.3,
      tableWidth: 100,
      sectionSpacing: 12
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  // ==================== قوالب التقارير الموحدة ====================
  {
    id: 'report-financial-unified',
    name: 'قالب التقارير المالية الموحد',
    description: 'قالب موحد لجميع التقارير المالية (أرباح وخسائر، تدفق نقدي، ميزانية، إلخ)',
    type: 'report',
    category: 'financial',
    isDefault: true,
    isActive: true,
    settings: {
      pageSize: 'A4',
      orientation: 'landscape',
      fontSize: 10,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: false,
      showTerms: false,
      marginTop: 15,
      marginBottom: 15,
      marginLeft: 10,
      marginRight: 10,
      primaryColor: '#1890ff',
      secondaryColor: '#f0f2f5',
      borderColor: '#91d5ff',
      headerSize: 16,
      lineSpacing: 1.4,
      tableWidth: 100,
      sectionSpacing: 12
    },
    supportedReportTypes: [
      'profit_loss', 'cash_flow', 'cash_flow_analysis', 'balance_sheet',
      'income_statement', 'bank_reconciliation', 'customer_aging',
      'profitability', 'customer_analysis', 'financial_summary',
      'closing_summary', 'closing_entries', 'carried_forward_balances',
      'audit_log', 'period_comparison', 'trial_balance'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'report-inventory-unified',
    name: 'قالب تقارير المخزون الموحد',
    description: 'قالب موحد لجميع تقارير المخزون (تفصيلي، حركات، منخفض، استهلاك، إلخ)',
    type: 'report',
    category: 'inventory',
    isDefault: false,
    isActive: true,
    settings: {
      pageSize: 'A4',
      orientation: 'portrait',
      fontSize: 9,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: false,
      showTerms: false,
      marginTop: 15,
      marginBottom: 15,
      marginLeft: 10,
      marginRight: 10,
      primaryColor: '#52c41a',
      secondaryColor: '#f6ffed',
      borderColor: '#b7eb8f',
      headerSize: 14,
      lineSpacing: 1.2,
      tableWidth: 100,
      sectionSpacing: 10
    },
    supportedReportTypes: [
      'inventory_detailed', 'inventory_movements', 'inventory_audit',
      'material_consumption', 'low_stock', 'advanced_inventory',
      'abc_analysis', 'abc-analysis', 'item_warehouse_distribution', 'item-warehouse-distribution'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'report-sales-unified',
    name: 'قالب تقارير المبيعات الموحد',
    description: 'قالب موحد لجميع تقارير المبيعات (حسب العميل، المنتج، الشهرية، إلخ)',
    type: 'report',
    category: 'sales',
    isDefault: false,
    isActive: true,
    settings: {
      pageSize: 'A4',
      orientation: 'landscape',
      fontSize: 10,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: false,
      showTerms: false,
      marginTop: 15,
      marginBottom: 15,
      marginLeft: 10,
      marginRight: 10,
      primaryColor: '#13c2c2',
      secondaryColor: '#e6fffb',
      borderColor: '#87e8de',
      headerSize: 15,
      lineSpacing: 1.3,
      tableWidth: 100,
      sectionSpacing: 12
    },
    supportedReportTypes: [
      'sales_by_customer', 'sales_by_product', 'sales_by_region',
      'monthly_sales', 'sales_returns', 'top_profitable_customers'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'report-purchases-unified',
    name: 'قالب تقارير المشتريات الموحد',
    description: 'قالب موحد لجميع تقارير المشتريات (حسب المورد، الصنف، التحليل، إلخ)',
    type: 'report',
    category: 'purchases',
    isDefault: false,
    isActive: true,
    settings: {
      pageSize: 'A4',
      orientation: 'landscape',
      fontSize: 10,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: false,
      showTerms: false,
      marginTop: 15,
      marginBottom: 15,
      marginLeft: 10,
      marginRight: 10,
      primaryColor: '#fa541c',
      secondaryColor: '#fff2e8',
      borderColor: '#ffbb96',
      headerSize: 14,
      lineSpacing: 1.3,
      tableWidth: 100,
      sectionSpacing: 12
    },
    supportedReportTypes: [
      'purchases_by_supplier', 'purchases_by_item', 'supplier_payables',
      'purchase_analysis', 'cost_analysis', 'supplier_price_comparison',
      'supplier_quality', 'supplier_analysis'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'report-production-unified',
    name: 'قالب تقارير الإنتاج الموحد',
    description: 'قالب موحد لجميع تقارير الإنتاج (أوامر، كفاءة، تكاليف، جودة، إلخ)',
    type: 'report',
    category: 'production',
    isDefault: false,
    isActive: true,
    settings: {
      pageSize: 'A4',
      orientation: 'landscape',
      fontSize: 10,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: false,
      showTerms: false,
      marginTop: 15,
      marginBottom: 15,
      marginLeft: 10,
      marginRight: 10,
      primaryColor: '#fa8c16',
      secondaryColor: '#fff7e6',
      borderColor: '#ffd591',
      headerSize: 14,
      lineSpacing: 1.3,
      tableWidth: 100,
      sectionSpacing: 12
    },
    supportedReportTypes: [
      'production_orders', 'production_efficiency', 'production_costs',
      'production_schedule', 'production_quality', 'production_workers_performance',
      'production_materials_consumption', 'production_profitability'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'report-fiscal-closing-unified',
    name: 'قالب تقارير إقفال السنة المالية الموحد',
    description: 'قالب موحد لجميع تقارير إقفال السنة المالية (ملخص الإقفال، القيود، الترحيل، سجل التدقيق، إلخ)',
    type: 'report',
    category: 'fiscal_closing',
    isDefault: true,
    isActive: true,
    settings: {
      pageSize: 'A4',
      orientation: 'portrait',
      fontSize: 11,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: true,
      showTerms: false,
      marginTop: 20,
      marginBottom: 20,
      marginLeft: 15,
      marginRight: 15,
      primaryColor: '#722ed1',
      secondaryColor: '#f9f0ff',
      borderColor: '#d3adf7',
      headerSize: 16,
      lineSpacing: 1.5,
      tableWidth: 100,
      sectionSpacing: 15
    },
    supportedReportTypes: [
      'closing_summary', 'closing_entries', 'carried_forward_balances',
      'audit_log', 'period_comparison', 'closing_checklist',
      'trial_balance', 'income_statement', 'balance_sheet', 'cash_flow'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'report-employees-unified',
    name: 'قالب تقارير الموظفين الموحد',
    description: 'قالب موحد لجميع تقارير الموظفين (حضور، رواتب، إجازات، أداء، إلخ)',
    type: 'report',
    category: 'employees',
    isDefault: false,
    isActive: true,
    settings: {
      pageSize: 'A4',
      orientation: 'landscape',
      fontSize: 10,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: false,
      showTerms: false,
      marginTop: 15,
      marginBottom: 15,
      marginLeft: 10,
      marginRight: 10,
      primaryColor: '#722ed1',
      secondaryColor: '#f9f0ff',
      borderColor: '#d3adf7',
      headerSize: 14,
      lineSpacing: 1.3,
      tableWidth: 100,
      sectionSpacing: 12
    },
    supportedReportTypes: [
      'employee_attendance', 'employee-attendance', 'employee_payroll', 'employee_leaves',
      'employee_performance', 'employee_overtime', 'employee_analysis', 'employee-analysis',
      'salary_comparison', 'efficiency_evaluation'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'report-paint-unified',
    name: 'قالب تقارير الدهان الموحد',
    description: 'قالب موحد لجميع تقارير الدهان (حسب العميل، النوع، الشهرية، الجودة، إلخ)',
    type: 'report',
    category: 'paint',
    isDefault: false,
    isActive: true,
    settings: {
      pageSize: 'A4',
      orientation: 'landscape',
      fontSize: 10,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: false,
      showTerms: false,
      marginTop: 15,
      marginBottom: 15,
      marginLeft: 10,
      marginRight: 10,
      primaryColor: '#eb2f96',
      secondaryColor: '#fff0f6',
      borderColor: '#ffadd2',
      headerSize: 14,
      lineSpacing: 1.3,
      tableWidth: 100,
      sectionSpacing: 12
    },
    supportedReportTypes: [
      'paint_by_customer', 'paint_by_type', 'monthly_paint',
      'paint_profitability', 'paint_performance', 'paint_quality'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  // ==================== قوالب المستندات الخاصة ====================
  {
    id: 'certificate-template',
    name: 'قالب الشهادات الرسمي',
    description: 'قالب رسمي للشهادات والوثائق مع تصميم أنيق',
    type: 'certificate',
    isDefault: false,
    isActive: true,
    settings: {
      pageSize: 'A4',
      orientation: 'landscape',
      fontSize: 14,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: true,
      showTerms: false,
      marginTop: 30,
      marginBottom: 30,
      marginLeft: 25,
      marginRight: 25,
      primaryColor: '#13c2c2',
      secondaryColor: '#e6fffb',
      borderColor: '#87e8de',
      headerSize: 18,
      lineSpacing: 1.8,
      tableWidth: 90,
      sectionSpacing: 20
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'simple-list',
    name: 'قالب القوائم البسيط',
    description: 'قالب بسيط للقوائم والكشوفات مع تنسيق نظيف',
    type: 'custom',
    isDefault: false,
    isActive: true,
    settings: {
      pageSize: 'A4',
      orientation: 'portrait',
      fontSize: 11,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: false,
      showTerms: false,
      marginTop: 20,
      marginBottom: 20,
      marginLeft: 15,
      marginRight: 15,
      primaryColor: '#595959',
      secondaryColor: '#f5f5f5',
      borderColor: '#d9d9d9',
      headerSize: 14,
      lineSpacing: 1.4,
      tableWidth: 100,
      sectionSpacing: 15
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

/**
 * دالة للحصول على قالب حسب النوع
 */
export const getTemplateByType = (type: string): StandardPrintTemplate | undefined => {
  return STANDARD_PRINT_TEMPLATES.find(template =>
    template.type === type && template.isDefault
  );
};

/**
 * دالة للحصول على قالب حسب المعرف
 */
export const getTemplateById = (id: string): StandardPrintTemplate | undefined => {
  return STANDARD_PRINT_TEMPLATES.find(template => template.id === id);
};

/**
 * دالة للحصول على جميع القوالب النشطة
 */
export const getActiveTemplates = (): StandardPrintTemplate[] => {
  return STANDARD_PRINT_TEMPLATES.filter(template => template.isActive);
};

/**
 * دالة للحصول على القوالب حسب النوع
 */
export const getTemplatesByType = (type: string): StandardPrintTemplate[] => {
  return STANDARD_PRINT_TEMPLATES.filter(template =>
    template.type === type && template.isActive
  );
};

/**
 * دالة للحصول على القوالب حسب الفئة (للتقارير)
 */
export const getTemplatesByCategory = (category: string): StandardPrintTemplate[] => {
  return STANDARD_PRINT_TEMPLATES.filter(template =>
    template.category === category && template.isActive
  );
};

/**
 * دالة للحصول على القالب المناسب لنوع تقرير معين
 */
export const getTemplateForReportType = (reportType: string): StandardPrintTemplate | undefined => {
  // البحث عن قالب يدعم نوع التقرير المحدد
  const template = STANDARD_PRINT_TEMPLATES.find(template =>
    template.supportedReportTypes?.includes(reportType) && template.isActive
  );

  // إذا لم يوجد قالب محدد، استخدم القالب الافتراضي للتقارير
  if (!template) {
    return STANDARD_PRINT_TEMPLATES.find(template =>
      template.type === 'report' && template.isDefault
    );
  }

  return template;
};

/**
 * دالة للحصول على جميع قوالب التقارير مع تصنيفها
 */
export const getReportTemplatesGrouped = (): Record<string, StandardPrintTemplate[]> => {
  const reportTemplates = STANDARD_PRINT_TEMPLATES.filter(template =>
    template.type === 'report' && template.isActive
  );

  const grouped: Record<string, StandardPrintTemplate[]> = {};

  reportTemplates.forEach(template => {
    const category = template.category || 'general';
    if (!grouped[category]) {
      grouped[category] = [];
    }
    grouped[category].push(template);
  });

  return grouped;
};

/**
 * دالة للحصول على أسماء الفئات المتاحة
 */
export const getAvailableCategories = (): string[] => {
  const categories = new Set<string>();

  STANDARD_PRINT_TEMPLATES.forEach(template => {
    if (template.category && template.isActive) {
      categories.add(template.category);
    }
  });

  return Array.from(categories);
};

/**
 * دالة للحصول على إحصائيات القوالب
 */
export const getTemplateStats = () => {
  const total = STANDARD_PRINT_TEMPLATES.length;
  const active = STANDARD_PRINT_TEMPLATES.filter(t => t.isActive).length;
  const byType = STANDARD_PRINT_TEMPLATES.reduce((acc, template) => {
    acc[template.type] = (acc[template.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const byCategory = STANDARD_PRINT_TEMPLATES.reduce((acc, template) => {
    if (template.category) {
      acc[template.category] = (acc[template.category] || 0) + 1;
    }
    return acc;
  }, {} as Record<string, number>);

  return {
    total,
    active,
    inactive: total - active,
    byType,
    byCategory
  };
};
