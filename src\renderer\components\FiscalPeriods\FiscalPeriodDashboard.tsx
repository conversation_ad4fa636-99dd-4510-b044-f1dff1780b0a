import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Tabs,
  Alert,
  <PERSON>loat<PERSON>utton,
  Badge,
  Toolt<PERSON>,
  Typo<PERSON>,
  Space,
  Spin,
  message
} from 'antd';
import {
  DashboardOutlined,
  CheckCircleOutlined,
  LockOutlined,
  BellOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  PlusOutlined
} from '@ant-design/icons';
import FiscalPeriodManager from './FiscalPeriodManager';
import ValidationDashboard from './ValidationDashboard';
import ClosingReports from './ClosingReports';
import ClosingNotifications from './ClosingNotifications';
import { FiscalPeriod } from '../../src/types/fiscalPeriod';
import { fiscalPeriodApi } from '../../src/services/fiscalPeriodApi';

const { TabPane } = Tabs;

const FiscalPeriodDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedPeriod, setSelectedPeriod] = useState<FiscalPeriod | null>(null);
  const [periods, setPeriods] = useState<FiscalPeriod[]>([]);
  const [canClosePeriod, setCanClosePeriod] = useState(false);
  const [unreadNotifications, setUnreadNotifications] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPeriods();
    loadNotificationCount();
  }, []);

  useEffect(() => {
    if (selectedPeriod) {
      checkClosingEligibility();
    }
  }, [selectedPeriod]);

  const loadPeriods = async () => {
    try {
      setLoading(true);
      const data = await fiscalPeriodApi.getAllPeriods();
      setPeriods(data);

      // Select the first open period by default
      const openPeriod = data.find(p => p.status === 'open');
      if (openPeriod) {
        setSelectedPeriod(openPeriod);
      }
    } catch (err) {
      setError('فشل في تحميل الفترات المالية');
      message.error('فشل في تحميل الفترات المالية');
      console.error('Error loading periods:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadNotificationCount = async () => {
    try {
      const count = await fiscalPeriodApi.getUnreadNotificationCount();
      setUnreadNotifications(count);
    } catch (err) {
      console.error('Error loading notification count:', err);
    }
  };

  const checkClosingEligibility = async () => {
    if (!selectedPeriod || selectedPeriod.status !== 'open') {
      setCanClosePeriod(false);
      return;
    }

    try {
      const validationResults = await fiscalPeriodApi.validatePeriodForClosing(selectedPeriod.id.toString());
      const hasErrors = validationResults.some(r => r.type === 'error');
      setCanClosePeriod(!hasErrors);
    } catch (err) {
      console.error('Error checking closing eligibility:', err);
      setCanClosePeriod(false);
    }
  };

  const handlePeriodSelect = (period: FiscalPeriod) => {
    setSelectedPeriod(period);
    setActiveTab(1); // Switch to validation tab
  };

  const handleValidationComplete = (canClose: boolean) => {
    setCanClosePeriod(canClose);
  };

  const handleStartClosing = () => {
    if (selectedPeriod && canClosePeriod) {
      message.info('سيتم تطوير معالج الإقفال قريباً');
    }
  };

  const renderPeriodSelector = () => {
    if (!selectedPeriod) return null;

    return (
      <Card style={{ marginBottom: 24 }}>
        <Typography.Title level={4}>
          الفترة المالية المحددة
        </Typography.Title>
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <Typography.Text strong>
            {selectedPeriod.period_name}
          </Typography.Text>
          <Typography.Text type="secondary">
            من {new Date(selectedPeriod.start_date).toLocaleDateString('ar-SA')}
            إلى {new Date(selectedPeriod.end_date).toLocaleDateString('ar-SA')}
          </Typography.Text>
          {selectedPeriod.status === 'open' && canClosePeriod && (
            <Tooltip title="بدء عملية الإقفال">
              <FloatButton
                icon={<LockOutlined />}
                type="primary"
                onClick={handleStartClosing}
                style={{ position: 'relative', right: 0, bottom: 0 }}
              />
            </Tooltip>
          )}
        </Space>
      </Card>
    );
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <Typography.Text style={{ display: 'block', marginTop: 16 }}>
          جاري التحميل...
        </Typography.Text>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Typography.Title level={2} style={{ marginBottom: 24 }}>
        نظام الإقفال المحاسبي
      </Typography.Title>

      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      {renderPeriodSelector()}

      <Card>
        <Tabs
          activeKey={activeTab.toString()}
          onChange={(key) => setActiveTab(parseInt(key))}
          type="card"
        >
          <TabPane
            tab={
              <span>
                <DashboardOutlined />
                إدارة الفترات
              </span>
            }
            key="0"
          >
            <FiscalPeriodManager onPeriodSelect={handlePeriodSelect} />
          </TabPane>

          <TabPane
            tab={
              <span>
                <CheckCircleOutlined />
                فحص صحة البيانات
              </span>
            }
            key="1"
            disabled={!selectedPeriod}
          >
            {selectedPeriod && (
              <ValidationDashboard
                period={selectedPeriod}
                onValidationComplete={handleValidationComplete}
              />
            )}
          </TabPane>

          <TabPane
            tab={
              <Badge count={unreadNotifications} size="small">
                <span>
                  <BellOutlined />
                  الإشعارات
                </span>
              </Badge>
            }
            key="2"
            disabled={!selectedPeriod}
          >
            {selectedPeriod && (
              <ClosingNotifications period={selectedPeriod} />
            )}
          </TabPane>

          <TabPane
            tab={
              <span>
                <BarChartOutlined />
                التقارير
              </span>
            }
            key="3"
            disabled={!selectedPeriod || selectedPeriod.status === 'open'}
          >
            {selectedPeriod && selectedPeriod.status !== 'open' && (
              <ClosingReports period={selectedPeriod} />
            )}
          </TabPane>
        </Tabs>
      </Card>

      {/* Floating Action Button for Quick Actions */}
      {selectedPeriod?.status === 'open' && (
        <FloatButton
          icon={<LockOutlined />}
          type={canClosePeriod ? 'primary' : 'default'}
          tooltip={canClosePeriod ? 'بدء عملية الإقفال' : 'يجب حل الأخطاء أولاً'}
          onClick={handleStartClosing}
          style={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            backgroundColor: canClosePeriod ? '#52c41a' : '#d9d9d9'
          }}
        />
      )}
    </div>
  );
};

export default FiscalPeriodDashboard;
