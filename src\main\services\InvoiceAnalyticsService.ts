import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { Logger } from '../utils/logger'
import { InvoiceType, InvoiceStatus } from './UniversalInvoiceService'

// واجهات التحليلات
export interface RevenueAnalytics {
  period: string
  sales_amount: number
  purchase_amount: number
  net_revenue: number
  profit_margin: number
  invoice_count: number
}

export interface PaymentAnalytics {
  payment_method: string
  count: number
  amount: number
  percentage: number
  avg_amount: number
}

export interface OverdueAnalytics {
  total_overdue: number
  overdue_amount: number
  avg_days_overdue: number
  by_age: Array<{
    age_range: string
    count: number
    amount: number
    percentage: number
  }>
}

export interface CustomerAnalytics {
  customer_id: number
  customer_name: string
  total_invoices: number
  total_amount: number
  paid_amount: number
  remaining_amount: number
  avg_invoice_amount: number
  last_invoice_date: string
  payment_behavior: 'excellent' | 'good' | 'average' | 'poor'
}

export interface ItemAnalytics {
  item_id: number
  item_name: string
  total_quantity: number
  total_revenue: number
  total_cost: number
  profit_amount: number
  profit_margin: number
  invoice_count: number
  avg_price: number
}

export interface TrendAnalytics {
  period: string
  metric: string
  value: number
  change_percentage: number
  trend: 'up' | 'down' | 'stable'
}

export interface InvoiceAnalyticsFilters {
  date_from?: string
  date_to?: string
  invoice_type?: InvoiceType
  customer_id?: number
  supplier_id?: number
  status?: InvoiceStatus[]
  group_by?: 'day' | 'week' | 'month' | 'quarter' | 'year'
}

export class InvoiceAnalyticsService {
  private static instance: InvoiceAnalyticsService
  private db: any

  private constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
  }

  public static getInstance(): InvoiceAnalyticsService {
    if (!InvoiceAnalyticsService.instance) {
      InvoiceAnalyticsService.instance = new InvoiceAnalyticsService()
    }
    return InvoiceAnalyticsService.instance
  }

  // تحليل الإيرادات
  public async getRevenueAnalytics(filters: InvoiceAnalyticsFilters = {}): Promise<ApiResponse> {
    try {
      Logger.info('InvoiceAnalyticsService', 'تحليل الإيرادات')

      const groupBy = filters.group_by || 'month'
      const dateFormat = this.getDateFormat(groupBy)
      
      let whereClause = 'WHERE 1=1'
      const params: any[] = []

      if (filters.date_from) {
        whereClause += ' AND invoice_date >= ?'
        params.push(filters.date_from)
      }

      if (filters.date_to) {
        whereClause += ' AND invoice_date <= ?'
        params.push(filters.date_to)
      }

      if (filters.status && filters.status.length > 0) {
        whereClause += ` AND status IN (${filters.status.map(() => '?').join(',')})`
        params.push(...filters.status)
      }

      // تحليل المبيعات
      const salesQuery = `
        SELECT 
          strftime('${dateFormat}', invoice_date) as period,
          SUM(total_amount) as sales_amount,
          COUNT(*) as sales_count
        FROM sales_invoices 
        ${whereClause}
        GROUP BY strftime('${dateFormat}', invoice_date)
        ORDER BY period
      `

      // تحليل المشتريات
      const purchasesQuery = `
        SELECT 
          strftime('${dateFormat}', invoice_date) as period,
          SUM(total_amount) as purchase_amount,
          COUNT(*) as purchase_count
        FROM purchase_invoices 
        ${whereClause}
        GROUP BY strftime('${dateFormat}', invoice_date)
        ORDER BY period
      `

      const salesStmt = this.db.prepare(salesQuery)
      const purchasesStmt = this.db.prepare(purchasesQuery)

      const salesData = salesStmt.all(...params)
      const purchasesData = purchasesStmt.all(...params)

      // دمج البيانات
      const revenueMap = new Map<string, RevenueAnalytics>()

      salesData.forEach((row: any) => {
        revenueMap.set(row.period, {
          period: row.period,
          sales_amount: row.sales_amount || 0,
          purchase_amount: 0,
          net_revenue: row.sales_amount || 0,
          profit_margin: 0,
          invoice_count: row.sales_count || 0
        })
      })

      purchasesData.forEach((row: any) => {
        const existing = revenueMap.get(row.period)
        if (existing) {
          existing.purchase_amount = row.purchase_amount || 0
          existing.net_revenue = existing.sales_amount - existing.purchase_amount
          existing.profit_margin = existing.sales_amount > 0 
            ? (existing.net_revenue / existing.sales_amount) * 100 
            : 0
          existing.invoice_count += row.purchase_count || 0
        } else {
          revenueMap.set(row.period, {
            period: row.period,
            sales_amount: 0,
            purchase_amount: row.purchase_amount || 0,
            net_revenue: -(row.purchase_amount || 0),
            profit_margin: 0,
            invoice_count: row.purchase_count || 0
          })
        }
      })

      const analytics = Array.from(revenueMap.values()).sort((a, b) => a.period.localeCompare(b.period))

      return {
        success: true,
        data: analytics
      }
    } catch (error) {
      Logger.error('InvoiceAnalyticsService', 'خطأ في تحليل الإيرادات:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل الإيرادات'
      }
    }
  }

  // تحليل طرق الدفع
  public async getPaymentAnalytics(filters: InvoiceAnalyticsFilters = {}): Promise<ApiResponse> {
    try {
      Logger.info('InvoiceAnalyticsService', 'تحليل طرق الدفع')

      let whereClause = 'WHERE payment_method IS NOT NULL'
      const params: any[] = []

      if (filters.date_from) {
        whereClause += ' AND invoice_date >= ?'
        params.push(filters.date_from)
      }

      if (filters.date_to) {
        whereClause += ' AND invoice_date <= ?'
        params.push(filters.date_to)
      }

      if (filters.invoice_type) {
        // تحديد الجدول حسب نوع الفاتورة
        const tableName = this.getTableName(filters.invoice_type)
        
        const query = `
          SELECT 
            payment_method,
            COUNT(*) as count,
            SUM(paid_amount) as amount,
            AVG(paid_amount) as avg_amount
          FROM ${tableName}
          ${whereClause}
          GROUP BY payment_method
          ORDER BY amount DESC
        `

        const stmt = this.db.prepare(query)
        const results = stmt.all(...params)

        // حساب النسب المئوية
        const totalAmount = results.reduce((sum: number, row: any) => sum + (row.amount || 0), 0)
        
        const analytics: PaymentAnalytics[] = results.map((row: any) => ({
          payment_method: row.payment_method,
          count: row.count,
          amount: row.amount || 0,
          percentage: totalAmount > 0 ? ((row.amount || 0) / totalAmount) * 100 : 0,
          avg_amount: row.avg_amount || 0
        }))

        return {
          success: true,
          data: analytics
        }
      } else {
        // تحليل شامل لجميع أنواع الفواتير
        const tables = ['sales_invoices', 'purchase_invoices', 'paint_invoices']
        const allResults: any[] = []

        for (const table of tables) {
          const query = `
            SELECT 
              payment_method,
              COUNT(*) as count,
              SUM(paid_amount) as amount,
              AVG(paid_amount) as avg_amount
            FROM ${table}
            ${whereClause}
            GROUP BY payment_method
          `

          const stmt = this.db.prepare(query)
          const results = stmt.all(...params)
          allResults.push(...results)
        }

        // دمج النتائج
        const paymentMap = new Map<string, PaymentAnalytics>()
        
        allResults.forEach(row => {
          const existing = paymentMap.get(row.payment_method)
          if (existing) {
            existing.count += row.count
            existing.amount += row.amount || 0
            existing.avg_amount = existing.amount / existing.count
          } else {
            paymentMap.set(row.payment_method, {
              payment_method: row.payment_method,
              count: row.count,
              amount: row.amount || 0,
              percentage: 0,
              avg_amount: row.avg_amount || 0
            })
          }
        })

        const analytics = Array.from(paymentMap.values())
        const totalAmount = analytics.reduce((sum, item) => sum + item.amount, 0)
        
        // حساب النسب المئوية
        analytics.forEach(item => {
          item.percentage = totalAmount > 0 ? (item.amount / totalAmount) * 100 : 0
        })

        analytics.sort((a, b) => b.amount - a.amount)

        return {
          success: true,
          data: analytics
        }
      }
    } catch (error) {
      Logger.error('InvoiceAnalyticsService', 'خطأ في تحليل طرق الدفع:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل طرق الدفع'
      }
    }
  }

  // تحليل الفواتير المتأخرة
  public async getOverdueAnalytics(filters: InvoiceAnalyticsFilters = {}): Promise<ApiResponse> {
    try {
      Logger.info('InvoiceAnalyticsService', 'تحليل الفواتير المتأخرة')

      const today = new Date().toISOString().split('T')[0]
      
      let whereClause = `WHERE due_date < '${today}' AND status != 'paid' AND status != 'cancelled'`
      const params: any[] = []

      if (filters.date_from) {
        whereClause += ' AND invoice_date >= ?'
        params.push(filters.date_from)
      }

      if (filters.date_to) {
        whereClause += ' AND invoice_date <= ?'
        params.push(filters.date_to)
      }

      const tables = ['sales_invoices', 'purchase_invoices', 'paint_invoices']
      const allOverdueData: any[] = []

      for (const table of tables) {
        const query = `
          SELECT 
            id,
            invoice_number,
            due_date,
            remaining_amount,
            julianday('${today}') - julianday(due_date) as days_overdue
          FROM ${table}
          ${whereClause}
        `

        const stmt = this.db.prepare(query)
        const results = stmt.all(...params)
        allOverdueData.push(...results)
      }

      if (allOverdueData.length === 0) {
        return {
          success: true,
          data: {
            total_overdue: 0,
            overdue_amount: 0,
            avg_days_overdue: 0,
            by_age: []
          }
        }
      }

      // حساب الإحصائيات العامة
      const totalOverdue = allOverdueData.length
      const overdueAmount = allOverdueData.reduce((sum, item) => sum + (item.remaining_amount || 0), 0)
      const avgDaysOverdue = allOverdueData.reduce((sum, item) => sum + item.days_overdue, 0) / totalOverdue

      // تصنيف حسب العمر
      const ageRanges = [
        { range: '1-30 يوم', min: 1, max: 30 },
        { range: '31-60 يوم', min: 31, max: 60 },
        { range: '61-90 يوم', min: 61, max: 90 },
        { range: 'أكثر من 90 يوم', min: 91, max: Infinity }
      ]

      const byAge = ageRanges.map(range => {
        const items = allOverdueData.filter(item => 
          item.days_overdue >= range.min && item.days_overdue <= range.max
        )
        
        const amount = items.reduce((sum, item) => sum + (item.remaining_amount || 0), 0)
        
        return {
          age_range: range.range,
          count: items.length,
          amount,
          percentage: totalOverdue > 0 ? (items.length / totalOverdue) * 100 : 0
        }
      })

      const analytics: OverdueAnalytics = {
        total_overdue: totalOverdue,
        overdue_amount: overdueAmount,
        avg_days_overdue: Math.round(avgDaysOverdue),
        by_age: byAge
      }

      return {
        success: true,
        data: analytics
      }
    } catch (error) {
      Logger.error('InvoiceAnalyticsService', 'خطأ في تحليل الفواتير المتأخرة:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل الفواتير المتأخرة'
      }
    }
  }

  // تحليل العملاء
  public async getCustomerAnalytics(filters: InvoiceAnalyticsFilters = {}): Promise<ApiResponse> {
    try {
      Logger.info('InvoiceAnalyticsService', 'تحليل العملاء')

      let whereClause = 'WHERE 1=1'
      const params: any[] = []

      if (filters.date_from) {
        whereClause += ' AND invoice_date >= ?'
        params.push(filters.date_from)
      }

      if (filters.date_to) {
        whereClause += ' AND invoice_date <= ?'
        params.push(filters.date_to)
      }

      if (filters.customer_id) {
        whereClause += ' AND customer_id = ?'
        params.push(filters.customer_id)
      }

      const query = `
        SELECT 
          customer_id,
          customer_name,
          COUNT(*) as total_invoices,
          SUM(total_amount) as total_amount,
          SUM(paid_amount) as paid_amount,
          SUM(remaining_amount) as remaining_amount,
          AVG(total_amount) as avg_invoice_amount,
          MAX(invoice_date) as last_invoice_date,
          AVG(CASE 
            WHEN status = 'paid' THEN 100
            WHEN status = 'partial' THEN 50
            WHEN status = 'overdue' THEN 0
            ELSE 25
          END) as payment_score
        FROM sales_invoices 
        ${whereClause}
        GROUP BY customer_id, customer_name
        ORDER BY total_amount DESC
      `

      const stmt = this.db.prepare(query)
      const results = stmt.all(...params)

      const analytics: CustomerAnalytics[] = results.map((row: any) => {
        let paymentBehavior: 'excellent' | 'good' | 'average' | 'poor'
        
        if (row.payment_score >= 80) paymentBehavior = 'excellent'
        else if (row.payment_score >= 60) paymentBehavior = 'good'
        else if (row.payment_score >= 40) paymentBehavior = 'average'
        else paymentBehavior = 'poor'

        return {
          customer_id: row.customer_id,
          customer_name: row.customer_name,
          total_invoices: row.total_invoices,
          total_amount: row.total_amount || 0,
          paid_amount: row.paid_amount || 0,
          remaining_amount: row.remaining_amount || 0,
          avg_invoice_amount: row.avg_invoice_amount || 0,
          last_invoice_date: row.last_invoice_date,
          payment_behavior: paymentBehavior
        }
      })

      return {
        success: true,
        data: analytics
      }
    } catch (error) {
      Logger.error('InvoiceAnalyticsService', 'خطأ في تحليل العملاء:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل العملاء'
      }
    }
  }

  // تحليل الأصناف
  public async getItemAnalytics(filters: InvoiceAnalyticsFilters = {}): Promise<ApiResponse> {
    try {
      Logger.info('InvoiceAnalyticsService', 'تحليل الأصناف')

      let whereClause = 'WHERE 1=1'
      const params: any[] = []

      if (filters.date_from) {
        whereClause += ' AND si.invoice_date >= ?'
        params.push(filters.date_from)
      }

      if (filters.date_to) {
        whereClause += ' AND si.invoice_date <= ?'
        params.push(filters.date_to)
      }

      const query = `
        SELECT 
          sii.item_id,
          sii.item_name,
          SUM(sii.quantity) as total_quantity,
          SUM(sii.total_price) as total_revenue,
          COUNT(DISTINCT si.id) as invoice_count,
          AVG(sii.unit_price) as avg_price
        FROM sales_invoice_items sii
        JOIN sales_invoices si ON sii.sales_invoice_id = si.id
        ${whereClause}
        GROUP BY sii.item_id, sii.item_name
        ORDER BY total_revenue DESC
      `

      const stmt = this.db.prepare(query)
      const results = stmt.all(...params)

      // جلب تكلفة الأصناف من المخزون
      const analytics: ItemAnalytics[] = []
      
      for (const row of results) {
        // جلب متوسط تكلفة الصنف
        const costStmt = this.db.prepare(`
          SELECT AVG(cost_price) as avg_cost 
          FROM inventory_items 
          WHERE item_id = ?
        `)
        const costResult = costStmt.get(row.item_id)
        const avgCost = costResult?.avg_cost || 0
        
        const totalCost = avgCost * row.total_quantity
        const profitAmount = row.total_revenue - totalCost
        const profitMargin = row.total_revenue > 0 ? (profitAmount / row.total_revenue) * 100 : 0

        analytics.push({
          item_id: row.item_id,
          item_name: row.item_name,
          total_quantity: row.total_quantity,
          total_revenue: row.total_revenue || 0,
          total_cost: totalCost,
          profit_amount: profitAmount,
          profit_margin: profitMargin,
          invoice_count: row.invoice_count,
          avg_price: row.avg_price || 0
        })
      }

      return {
        success: true,
        data: analytics
      }
    } catch (error) {
      Logger.error('InvoiceAnalyticsService', 'خطأ في تحليل الأصناف:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل الأصناف'
      }
    }
  }

  // تحليل الاتجاهات
  public async getTrendAnalytics(filters: InvoiceAnalyticsFilters = {}): Promise<ApiResponse> {
    try {
      Logger.info('InvoiceAnalyticsService', 'تحليل الاتجاهات')

      const groupBy = filters.group_by || 'month'
      const _dateFormat = this.getDateFormat(groupBy) // غير مستخدم حالياً
      
      // جلب بيانات الفترة الحالية والسابقة
      const revenueResult = await this.getRevenueAnalytics(filters)
      if (!revenueResult.success) {
        return revenueResult
      }

      const revenueData = revenueResult.data as RevenueAnalytics[]
      const trends: TrendAnalytics[] = []

      for (let i = 1; i < revenueData.length; i++) {
        const current = revenueData[i]
        const previous = revenueData[i - 1]

        // اتجاه المبيعات
        const salesChange = previous.sales_amount > 0 
          ? ((current.sales_amount - previous.sales_amount) / previous.sales_amount) * 100 
          : 0

        trends.push({
          period: current.period,
          metric: 'sales',
          value: current.sales_amount,
          change_percentage: salesChange,
          trend: salesChange > 5 ? 'up' : salesChange < -5 ? 'down' : 'stable'
        })

        // اتجاه الربح
        const profitChange = previous.net_revenue > 0 
          ? ((current.net_revenue - previous.net_revenue) / previous.net_revenue) * 100 
          : 0

        trends.push({
          period: current.period,
          metric: 'profit',
          value: current.net_revenue,
          change_percentage: profitChange,
          trend: profitChange > 5 ? 'up' : profitChange < -5 ? 'down' : 'stable'
        })
      }

      return {
        success: true,
        data: trends
      }
    } catch (error) {
      Logger.error('InvoiceAnalyticsService', 'خطأ في تحليل الاتجاهات:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل الاتجاهات'
      }
    }
  }

  // دوال مساعدة
  private getDateFormat(groupBy: string): string {
    switch (groupBy) {
      case 'day': return '%Y-%m-%d'
      case 'week': return '%Y-W%W'
      case 'month': return '%Y-%m'
      case 'quarter': return '%Y-Q' + Math.ceil(new Date().getMonth() / 3)
      case 'year': return '%Y'
      default: return '%Y-%m'
    }
  }

  private getTableName(invoiceType: InvoiceType): string {
    switch (invoiceType) {
      case 'sales': return 'sales_invoices'
      case 'purchase': return 'purchase_invoices'
      case 'paint': return 'paint_invoices'
      case 'service': return 'service_invoices'
      default: return 'sales_invoices'
    }
  }
}
