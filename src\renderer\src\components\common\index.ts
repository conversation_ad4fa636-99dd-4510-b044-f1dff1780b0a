// تصدير جميع المكونات المشتركة - ZET.IA Enhanced System
export { default as QuickShortcuts } from './QuickShortcuts'
export { default as QuickAttendance } from './QuickAttendance'
export { default as DailySummary } from './DailySummary'
export { default as QuickReports } from './QuickReports'
export { default as ErrorBoundary } from './ErrorBoundary'
export { default as LoadingWrapper } from './LoadingWrapper'

// مكونات الطباعة المتكاملة
export { default as UnifiedPrintButton } from './UnifiedPrintButton'
export { default as InvoicePrintButton } from './InvoicePrintButton'
export { default as ReceiptPrintButton } from './ReceiptPrintButton'
export { default as ReportPrintButton } from './ReportPrintButton'


// خدمات الطباعة
export { MasterPrintService } from '../../services/MasterPrintService'
export type { PrintData, UnifiedPrintOptions } from '../../types/print'

// المكونات الموجودة مسبقاً للصور
export { default as CheckImageManager } from './CheckImageManager'
export { default as EnhancedProductCatalogPrint } from './EnhancedProductCatalogPrint'

// مكون الصور الموحد البسيط (النظام الجديد)
export { SimpleImageManager } from './SimpleImageManager'




// أنواع البيانات المشتركة
export interface ShortcutItem {
  key: string
  icon: React.ReactNode
  label: string
  background: string
  color: string
}

export interface QuickShortcutsProps {
  onShortcutClick: (key: string) => void
  onPlaySound: (type: 'click' | 'success' | 'error' | 'warning') => void
}
