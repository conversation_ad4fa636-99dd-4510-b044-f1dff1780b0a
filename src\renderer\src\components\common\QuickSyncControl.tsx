import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, message, Popover, Space, Typography } from 'antd'
import { SyncOutlined, CheckCircleOutlined, WarningOutlined, DisconnectOutlined } from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'

const { Text } = Typography

interface SyncStatus {
  enabled: boolean
  deviceRole: string
  isActive: boolean
  lastSync: Date
  sharedFolder: string
  isConnected: boolean
}

export const QuickSyncControl: React.FC = () => {
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null)
  const [syncing, setSyncing] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadSyncStatus()
    const interval = setInterval(loadSyncStatus, 30000) // كل 30 ثانية
    return () => clearInterval(interval)
  }, [])

  const loadSyncStatus = async () => {
    try {
      const status = await window.electronAPI.getSyncStatus()
      setSyncStatus(status)
    } catch (error) {
      Logger.error('QuickSyncControl', 'خطأ في تحميل حالة المزامنة:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSync = async () => {
    if (!syncStatus?.enabled) {
      message.info('المزامنة غير مفعلة. يمكنك تفعيلها من الإعدادات')
      return
    }

    setSyncing(true)
    try {
      const result = await window.electronAPI.forceSyncNow()
      if (result.success) {
        message.success('تم تنفيذ المزامنة بنجاح')
        loadSyncStatus()
      } else {
        message.error(result.message || 'خطأ في المزامنة')
      }
    } catch {
      message.error('خطأ في تنفيذ المزامنة')
    } finally {
      setSyncing(false)
    }
  }

  const getStatusIcon = () => {
    if (!syncStatus?.enabled) {
      return <DisconnectOutlined style={{ color: '#999' }} />
    }
    
    if (syncStatus.isConnected) {
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />
    }
    
    return <WarningOutlined style={{ color: '#faad14' }} />
  }

  const getStatusText = () => {
    if (!syncStatus?.enabled) {
      return 'المزامنة معطلة'
    }
    
    if (syncStatus.isConnected) {
      return 'متصل'
    }
    
    return 'غير متصل'
  }

  const getStatusColor = () => {
    if (!syncStatus?.enabled) {
      return 'default'
    }
    
    if (syncStatus.isConnected) {
      return 'success'
    }
    
    return 'warning'
  }

  const getPopoverContent = () => {
    if (!syncStatus) {
      return <Text>جاري التحميل...</Text>
    }

    return (
      <div style={{ minWidth: 200 }}>
        <Space direction="vertical" size="small">
          <div>
            <Text strong>الحالة: </Text>
            <Text>{getStatusText()}</Text>
          </div>
          
          {syncStatus.enabled && (
            <>
              <div>
                <Text strong>الدور: </Text>
                <Text>{syncStatus.deviceRole === 'branch' ? 'فرعي' : 'مركزي'}</Text>
              </div>
              
              {syncStatus.lastSync && (
                <div>
                  <Text strong>آخر مزامنة: </Text>
                  <Text type="secondary">
                    {new Date(syncStatus.lastSync).toLocaleString('ar')}
                  </Text>
                </div>
              )}
              
              {syncStatus.sharedFolder && (
                <div>
                  <Text strong>المجلد: </Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {syncStatus.sharedFolder}
                  </Text>
                </div>
              )}

              <div style={{ marginTop: 8, padding: 8, backgroundColor: '#f6ffed', borderRadius: 4 }}>
                <Text style={{ fontSize: '12px', color: '#52c41a' }}>
                  💡 مزامنة ذكية ثنائية الاتجاه - كلا الجهازين يمكنهما الإدخال
                </Text>
              </div>
            </>
          )}
          
          <Button 
            type="primary" 
            size="small" 
            block
            onClick={handleSync}
            loading={syncing}
            disabled={!syncStatus.enabled}
          >
            {syncStatus.enabled ? 'مزامنة الآن' : 'غير مفعل'}
          </Button>
        </Space>
      </div>
    )
  }

  if (loading) {
    return (
      <Button 
        icon={<SyncOutlined spin />} 
        size="small" 
        type="text"
        loading
      />
    )
  }

  return (
    <Popover 
      content={getPopoverContent()} 
      title="حالة المزامنة" 
      trigger="hover"
      placement="bottomRight"
    >
      <Badge status={getStatusColor() as any} dot>
        <Button
          icon={syncing ? <SyncOutlined spin /> : getStatusIcon()}
          size="small"
          type="text"
          onClick={handleSync}
          style={{
            border: 'none',
            boxShadow: 'none',
            height: '24px',
            width: '24px',
            minWidth: '24px',
            padding: 0,
            fontSize: '12px'
          }}
        />
      </Badge>
    </Popover>
  )
}

export default QuickSyncControl
