import { SafeLogger as Logger } from '../../../utils/logger'
// خدمة التنبيهات لقسم إنتاج الأثاث
export interface FurnitureNotification {
  id: string
  type: 'deadline' | 'reminder' | 'status_change' | 'custom'
  title: string
  message: string
  orderId?: string
  orderNumber?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  scheduledTime: Date
  isRead: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface FurnitureNotificationSettings {
  enableDesktopNotifications: boolean
  enableSoundAlerts: boolean
  deadlineWarningDays: number
  reminderIntervalHours: number
  autoMarkAsRead: boolean
}

class FurnitureNotificationService {
  private notifications: FurnitureNotification[] = []
  private settings: FurnitureNotificationSettings = {
    enableDesktopNotifications: true,
    enableSoundAlerts: true,
    deadlineWarningDays: 3,
    reminderIntervalHours: 24,
    autoMarkAsRead: false
  }
  private checkInterval: NodeJS.Timeout | null = null

  constructor() {
    this.loadNotifications()
    this.loadSettings()
    this.startPeriodicCheck()
  }

  // تحميل التنبيهات من التخزين المحلي
  private loadNotifications(): void {
    try {
      const stored = localStorage.getItem('furniture_notifications')
      if (stored) {
        const parsed = JSON.parse(stored)
        this.notifications = parsed.map((n: any) => ({
          ...n,
          scheduledTime: new Date(n.scheduledTime),
          createdAt: new Date(n.createdAt),
          updatedAt: new Date(n.updatedAt)
        }))
      }
    } catch (error) {
      Logger.error('FurnitureNotificationService', 'خطأ في تحميل التنبيهات:', error)
    }
  }

  // حفظ التنبيهات في التخزين المحلي
  private saveNotifications(): void {
    try {
      localStorage.setItem('furniture_notifications', JSON.stringify(this.notifications))
    } catch (error) {
      Logger.error('FurnitureNotificationService', 'خطأ في حفظ التنبيهات:', error)
    }
  }

  // تحميل الإعدادات
  private loadSettings(): void {
    try {
      const stored = localStorage.getItem('furniture_notification_settings')
      if (stored) {
        this.settings = { ...this.settings, ...JSON.parse(stored) }
      }
    } catch (error) {
      Logger.error('FurnitureNotificationService', 'خطأ في تحميل إعدادات التنبيهات:', error)
    }
  }

  // حفظ الإعدادات
  private saveSettings(): void {
    try {
      localStorage.setItem('furniture_notification_settings', JSON.stringify(this.settings))
    } catch (error) {
      Logger.error('FurnitureNotificationService', 'خطأ في حفظ إعدادات التنبيهات:', error)
    }
  }

  // بدء الفحص الدوري للتنبيهات
  private startPeriodicCheck(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
    }
    
    this.checkInterval = setInterval(() => {
      this.checkPendingNotifications()
    }, 60000) // فحص كل دقيقة
  }

  // فحص التنبيهات المعلقة
  private checkPendingNotifications(): void {
    const now = new Date()
    const pendingNotifications = this.notifications.filter(
      n => n.isActive && !n.isRead && n.scheduledTime <= now
    )

    pendingNotifications.forEach(notification => {
      this.showNotification(notification)
      if (this.settings.autoMarkAsRead) {
        this.markAsRead(notification.id)
      }
    })
  }

  // عرض التنبيه
  private showNotification(notification: FurnitureNotification): void {
    // تنبيه سطح المكتب
    if (this.settings.enableDesktopNotifications && typeof window !== 'undefined' && 'Notification' in window) {
      if (Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/icon.png',
          tag: notification.id
        })
      } else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            new Notification(notification.title, {
              body: notification.message,
              icon: '/icon.png',
              tag: notification.id
            })
          }
        })
      }
    }

    // تنبيه صوتي
    if (this.settings.enableSoundAlerts) {
      this.playNotificationSound(notification.priority)
    }
  }

  // تشغيل صوت التنبيه
  private playNotificationSound(_priority: string): void {
    try {
      if (typeof window === 'undefined' || typeof Audio === 'undefined') return;
      const audio = new Audio()
      switch (_priority) {
        case 'urgent':
          audio.src = '/sounds/urgent.mp3'
          break
        case 'high':
          audio.src = '/sounds/high.mp3'
          break
        default:
          audio.src = '/sounds/default.mp3'
      }
      audio.play().catch(() => {
        // تجاهل أخطاء تشغيل الصوت
      })
    } catch (error) {
      Logger.error('FurnitureNotificationService', 'خطأ في تشغيل صوت التنبيه:', error)
    }
  }

  // إضافة تنبيه جديد
  addNotification(notification: Omit<FurnitureNotification, 'id' | 'isRead' | 'createdAt' | 'updatedAt'>): string {
    const id = `furniture_notif_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    const newNotification: FurnitureNotification = {
      ...notification,
      id,
      isRead: false,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    this.notifications.push(newNotification)
    this.saveNotifications()
    return id
  }

  // إضافة تنبيه للموعد النهائي
  addDeadlineNotification(orderId: string, orderNumber: string, deadline: Date): string {
    const warningTime = new Date(deadline.getTime() - (this.settings.deadlineWarningDays * 24 * 60 * 60 * 1000))
    
    return this.addNotification({
      type: 'deadline',
      title: 'تنبيه موعد نهائي',
      message: `أمر الإنتاج ${orderNumber} يقترب من الموعد النهائي`,
      orderId,
      orderNumber,
      priority: 'high',
      scheduledTime: warningTime,
      isActive: true
    })
  }

  // إضافة تنبيه مخصص
  addCustomNotification(
    title: string,
    message: string,
    scheduledTime: Date,
    priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium',
    orderId?: string,
    orderNumber?: string
  ): string {
    return this.addNotification({
      type: 'custom',
      title,
      message,
      orderId,
      orderNumber,
      priority,
      scheduledTime,
      isActive: true
    })
  }

  // الحصول على جميع التنبيهات
  getAllNotifications(): FurnitureNotification[] {
    return [...this.notifications].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
  }

  // الحصول على التنبيهات غير المقروءة
  getUnreadNotifications(): FurnitureNotification[] {
    return this.notifications.filter(n => !n.isRead && n.isActive)
  }

  // الحصول على التنبيهات المعلقة
  getPendingNotifications(): FurnitureNotification[] {
    const now = new Date()
    return this.notifications.filter(n => n.isActive && n.scheduledTime <= now && !n.isRead)
  }

  // تحديد التنبيه كمقروء
  markAsRead(notificationId: string): void {
    const notification = this.notifications.find(n => n.id === notificationId)
    if (notification) {
      notification.isRead = true
      notification.updatedAt = new Date()
      this.saveNotifications()
    }
  }

  // تحديد جميع التنبيهات كمقروءة
  markAllAsRead(): void {
    this.notifications.forEach(n => {
      if (!n.isRead) {
        n.isRead = true
        n.updatedAt = new Date()
      }
    })
    this.saveNotifications()
  }

  // حذف تنبيه
  deleteNotification(notificationId: string): void {
    this.notifications = this.notifications.filter(n => n.id !== notificationId)
    this.saveNotifications()
  }

  // تحديث الإعدادات
  updateSettings(newSettings: Partial<FurnitureNotificationSettings>): void {
    this.settings = { ...this.settings, ...newSettings }
    this.saveSettings()
  }

  // الحصول على الإعدادات
  getSettings(): FurnitureNotificationSettings {
    return { ...this.settings }
  }

  // تنظيف التنبيهات القديمة
  cleanup(daysOld: number = 30): void {
    const cutoffDate = new Date(Date.now() - (daysOld * 24 * 60 * 60 * 1000))
    this.notifications = this.notifications.filter(n => n.createdAt > cutoffDate)
    this.saveNotifications()
  }

  // إيقاف الخدمة
  destroy(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }
}

export const furnitureNotificationService = new FurnitureNotificationService()
