/**
 * مكون معاينة القوالب المباشرة
 *
 * يوفر معاينة مباشرة للقوالب مع المميزات التالية:
 * - معاينة مع البيانات الحقيقية من قاعدة البيانات
 * - أدوات تحكم متقدمة (تكبير، هوامش، أحجام)
 * - تطبيق جميع إعدادات القالب فورياً
 * - تحسينات أداء للبيانات الكبيرة
 * - واجهة تفاعلية سهلة الاستخدام
 *
 * @version 1.0.0
 * <AUTHOR> المتكامل لتعديل القوالب والأعمدة
 */

import React, { useState, useEffect, useMemo } from 'react'
import {
  Card,
  Row,
  Col,
  Table,
  Typography,
  Space,
  Tag,
  Button,
  Select,
  Divider,
  Al<PERSON>,
  Spin,
  Switch,
  Tooltip
} from 'antd'
import {
  EyeOutlined,
  PrinterOutlined,
  ExpandOutlined,
  CompressOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons'

import {
  EnhancedTemplate,
  UnifiedPrintSettings,
  EnhancedColumnConfig
} from '../../types/enhancedTemplateTypes'
import { SafeLogger as Logger } from '../../utils/logger'

const { Title, Text } = Typography
const { Option } = Select

interface TemplatePreviewComponentProps {
  template: EnhancedTemplate
  effectiveSettings: UnifiedPrintSettings
  onSettingsChange?: (settings: Partial<UnifiedPrintSettings>) => void
  reportContext?: {
    reportType: string
    reportCategory: string
    reportTitle: string
  }
  // البيانات الحقيقية للمعاينة
  previewData?: any[]
  loading?: boolean
}

// أنواع أحجام الطباعة المدعومة
const PAGE_SIZES = {
  A4: { width: 210, height: 297, label: 'A4 (210×297mm)' },
  A5: { width: 148, height: 210, label: 'A5 (148×210mm)' },
  Letter: { width: 216, height: 279, label: 'Letter (8.5×11")' },
  Legal: { width: 216, height: 356, label: 'Legal (8.5×14")' }
}

const TemplatePreviewComponent: React.FC<TemplatePreviewComponentProps> = ({
  template,
  effectiveSettings,
  onSettingsChange,
  reportContext,
  previewData = [],
  loading: externalLoading = false
}) => {
  // الحالة المحلية
  const [previewSize, setPreviewSize] = useState<keyof typeof PAGE_SIZES>('A4')
  const [zoomLevel, setZoomLevel] = useState(100)
  const [_showRulers, _setShowRulers] = useState(false)
  const [showMargins, setShowMargins] = useState(true)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [loading, setLoading] = useState(false)

  // البيانات للمعاينة (حقيقية أو فارغة)
  const displayData = useMemo(() => {
    if (previewData && previewData.length > 0) {
      // استخدام البيانات الحقيقية المرسلة
      return previewData.slice(0, 10) // أول 10 صفوف للمعاينة
    } else {
      // إذا لم توجد بيانات، عرض رسالة
      return []
    }
  }, [previewData])

  // الأعمدة المرئية للطباعة
  const visibleColumns = useMemo(() => {
    if (!template.columns) return []
    return template.columns
      .filter(col => col.printVisible !== false)
      .sort((a, b) => (a.printOrder || 0) - (b.printOrder || 0))
  }, [template.columns])

  // حساب أبعاد الصفحة
  const pageSize = PAGE_SIZES[previewSize]
  const scaleFactor = zoomLevel / 100

  // تحديث المعاينة عند تغيير الإعدادات
  useEffect(() => {
    setLoading(true)
    const timer = setTimeout(() => {
      setLoading(false)
    }, 300)
    return () => clearTimeout(timer)
  }, [template, effectiveSettings, previewSize])

  // دمج حالة التحميل الداخلية والخارجية
  const isLoading = loading || externalLoading

  // تطبيق الأنماط على الجدول
  const getTableStyle = () => ({
    fontSize: `${effectiveSettings.font.fontSize}px`,
    fontFamily: effectiveSettings.font.fontFamily,
    color: effectiveSettings.colors.textColor,
    backgroundColor: effectiveSettings.colors.backgroundColor,
    border: `${effectiveSettings.layout.borderWidth}px solid ${effectiveSettings.colors.borderColor}`,
    borderRadius: '4px',
    width: '100%'
  })

  // تطبيق الأنماط على الرأس
  const getHeaderStyle = () => ({
    fontSize: `${effectiveSettings.font.headerSize}px`,
    fontFamily: effectiveSettings.font.fontFamily,
    color: effectiveSettings.colors.primaryColor,
    textAlign: 'center' as const,
    marginBottom: `${effectiveSettings.layout.sectionSpacing}px`,
    fontWeight: 'bold'
  })

  // تطبيق الأنماط على الأعمدة
  const getColumnStyle = (column: EnhancedColumnConfig) => ({
    textAlign: column.printAlign || 'right',
    fontWeight: column.printFormat?.bold ? 'bold' : 'normal',
    fontStyle: column.printFormat?.italic ? 'italic' : 'normal',
    textDecoration: column.printFormat?.underline ? 'underline' : 'none',
    color: column.printFormat?.color || effectiveSettings.colors.textColor,
    backgroundColor: column.printFormat?.backgroundColor || 'transparent',
    width: column.printWidth || 'auto'
  })

  // تحديث حجم المعاينة
  const handleSizeChange = (size: keyof typeof PAGE_SIZES) => {
    setPreviewSize(size)
    if (onSettingsChange) {
      onSettingsChange({
        page: {
          ...effectiveSettings.page,
          pageSize: size
        }
      })
    }
  }

  // تحديث مستوى التكبير
  const handleZoomChange = (zoom: number) => {
    setZoomLevel(zoom)
  }

  // تبديل وضع الشاشة الكاملة
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  // إعادة تحميل المعاينة
  const refreshPreview = () => {
    setLoading(true)
    // يمكن إضافة منطق إعادة تحميل البيانات هنا
    setTimeout(() => setLoading(false), 500)
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* شريط أدوات المعاينة */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 8]} align="middle">
          <Col xs={24} sm={6}>
            <Space>
              <Text strong>حجم الصفحة:</Text>
              <Select
                value={previewSize}
                onChange={handleSizeChange}
                style={{ width: 140 }}
                size="small"
              >
                {Object.entries(PAGE_SIZES).map(([key, size]) => (
                  <Option key={key} value={key}>
                    {size.label}
                  </Option>
                ))}
              </Select>
            </Space>
          </Col>

          <Col xs={24} sm={6}>
            <Space>
              <Text strong>التكبير:</Text>
              <Select
                value={zoomLevel}
                onChange={handleZoomChange}
                style={{ width: 100 }}
                size="small"
              >
                <Option value={50}>50%</Option>
                <Option value={75}>75%</Option>
                <Option value={100}>100%</Option>
                <Option value={125}>125%</Option>
                <Option value={150}>150%</Option>
                <Option value={200}>200%</Option>
              </Select>
            </Space>
          </Col>

          <Col xs={24} sm={6}>
            <Space>
              <Tooltip title="إظهار الهوامش">
                <Switch
                  checked={showMargins}
                  onChange={setShowMargins}
                  size="small"
                />
              </Tooltip>
              <Text>الهوامش</Text>
            </Space>
          </Col>

          <Col xs={24} sm={6}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={refreshPreview}
                size="small"
                loading={isLoading}
              >
                تحديث
              </Button>
              <Button
                icon={isFullscreen ? <CompressOutlined /> : <ExpandOutlined />}
                onClick={toggleFullscreen}
                size="small"
              >
                {isFullscreen ? 'تصغير' : 'شاشة كاملة'}
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* منطقة المعاينة */}
      <Card
        style={{
          flex: 1,
          overflow: 'auto',
          backgroundColor: '#f5f5f5',
          padding: '20px'
        }}
        bodyStyle={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'flex-start',
          minHeight: '100%'
        }}
      >
        <Spin spinning={isLoading} tip="جاري تحديث المعاينة...">
          <div
            style={{
              width: `${pageSize.width * scaleFactor}px`,
              minHeight: `${pageSize.height * scaleFactor}px`,
              backgroundColor: 'white',
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
              padding: `${effectiveSettings.page.margins.top * scaleFactor}px ${effectiveSettings.page.margins.right * scaleFactor}px ${effectiveSettings.page.margins.bottom * scaleFactor}px ${effectiveSettings.page.margins.left * scaleFactor}px`,
              position: 'relative',
              transform: `scale(${scaleFactor})`,
              transformOrigin: 'top center'
            }}
          >
            {/* إظهار الهوامش */}
            {showMargins && (
              <>
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: `${effectiveSettings.page.margins.top * scaleFactor}px`,
                  backgroundColor: 'rgba(255, 0, 0, 0.1)',
                  border: '1px dashed #ff4d4f'
                }} />
                <div style={{
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  height: `${effectiveSettings.page.margins.bottom * scaleFactor}px`,
                  backgroundColor: 'rgba(255, 0, 0, 0.1)',
                  border: '1px dashed #ff4d4f'
                }} />
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  bottom: 0,
                  width: `${effectiveSettings.page.margins.left * scaleFactor}px`,
                  backgroundColor: 'rgba(255, 0, 0, 0.1)',
                  border: '1px dashed #ff4d4f'
                }} />
                <div style={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  bottom: 0,
                  width: `${effectiveSettings.page.margins.right * scaleFactor}px`,
                  backgroundColor: 'rgba(255, 0, 0, 0.1)',
                  border: '1px dashed #ff4d4f'
                }} />
              </>
            )}

            {/* رأس التقرير */}
            {effectiveSettings.display.showHeader && (
              <div style={getHeaderStyle()}>
                {effectiveSettings.content.headerText || reportContext?.reportTitle || 'عنوان التقرير'}
              </div>
            )}

            {/* الشعار */}
            {effectiveSettings.display.showLogo && (
              <div style={{
                textAlign: effectiveSettings.layout.logoPosition.includes('left') ? 'left' : 
                          effectiveSettings.layout.logoPosition.includes('right') ? 'right' : 'center',
                marginBottom: `${effectiveSettings.layout.sectionSpacing}px`
              }}>
                <div style={{
                  width: `${effectiveSettings.layout.logoSize}px`,
                  height: `${effectiveSettings.layout.logoSize}px`,
                  backgroundColor: effectiveSettings.colors.secondaryColor,
                  border: `2px solid ${effectiveSettings.colors.borderColor}`,
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: '4px'
                }}>
                  <Text type="secondary">شعار</Text>
                </div>
              </div>
            )}

            {/* جدول البيانات */}
            <div style={{ marginTop: `${effectiveSettings.layout.sectionSpacing}px` }}>
              {displayData.length > 0 ? (
                <Table
                  dataSource={displayData}
                  columns={visibleColumns.map(col => ({
                    title: col.title,
                    dataIndex: col.dataIndex,
                    key: col.key,
                    align: col.printAlign || 'right',
                    width: col.printWidth,
                    render: (value: any) => (
                      <span style={getColumnStyle(col)}>
                        {formatCellValue(value, col)}
                      </span>
                    )
                  }))}
                  pagination={false}
                  size="small"
                  style={getTableStyle()}
                  bordered
                />
              ) : (
                <div style={{
                  textAlign: 'center',
                  padding: '40px',
                  border: `1px dashed ${effectiveSettings.colors.borderColor}`,
                  borderRadius: '4px',
                  backgroundColor: effectiveSettings.colors.secondaryColor
                }}>
                  <Text type="secondary">
                    لا توجد بيانات للمعاينة. يرجى تحديد تقرير يحتوي على بيانات لرؤية المعاينة.
                  </Text>
                </div>
              )}
            </div>

            {/* ذيل التقرير */}
            {effectiveSettings.display.showFooter && (
              <div style={{
                textAlign: 'center',
                marginTop: `${effectiveSettings.layout.sectionSpacing}px`,
                fontSize: `${effectiveSettings.font.fontSize - 2}px`,
                color: effectiveSettings.colors.textColor
              }}>
                {effectiveSettings.content.footerText || 'شكراً لتعاملكم معنا'}
              </div>
            )}

            {/* العلامة المائية */}
            {effectiveSettings.content.watermark && (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%) rotate(-45deg)',
                fontSize: '48px',
                color: effectiveSettings.colors.textColor,
                opacity: effectiveSettings.content.watermarkOpacity || 0.1,
                pointerEvents: 'none',
                zIndex: 1
              }}>
                {effectiveSettings.content.watermarkText || 'ZET.IA'}
              </div>
            )}
          </div>
        </Spin>
      </Card>

      {/* معلومات المعاينة */}
      <Alert
        message="معاينة مباشرة"
        description={`يتم عرض القالب "${template.metadata.name}" بحجم ${PAGE_SIZES[previewSize].label} وتكبير ${zoomLevel}%`}
        type="info"
        showIcon
        style={{ marginTop: 16 }}
      />
    </div>
  )
}



// دالة تنسيق قيم الخلايا
function formatCellValue(value: any, column: EnhancedColumnConfig) {
  if (value === null || value === undefined) return '-'

  switch (column.type) {
    case 'currency':
      return `${Number(value).toLocaleString('ar-SA', {
        minimumFractionDigits: column.numberFormat?.decimals || 2
      })} ${column.numberFormat?.currency || 'ر.س'}`
    
    case 'number':
      return Number(value).toLocaleString('ar-SA', {
        minimumFractionDigits: column.numberFormat?.decimals || 0,
        useGrouping: column.numberFormat?.thousandsSeparator !== false
      })
    
    case 'date':
      return new Date(value).toLocaleDateString('ar-SA')
    
    default:
      return String(value)
  }
}

export default TemplatePreviewComponent
