import { useState, useEffect, useCallback } from 'react';
import { calculateInvoiceTotals, calculateItemsSubtotal, validateInvoiceFinancials } from '../utils/settings';
import { SafeLogger as Logger } from '../utils/logger';

export interface InvoiceItem {
  item_id: number;
  quantity: number;
  unit_price: number;
  total_price: number;
  [key: string]: any;
}

export interface InvoiceTotals {
  subtotal: number;
  discountAmount: number;
  taxAmount: number;
  totalAmount: number;
  remainingAmount: number;
}

export interface UseInvoiceCalculationsProps {
  form: any;
  items: InvoiceItem[];
  onError?: (error: string) => void;
}

/**
 * Hook موحد لحساب مجاميع الفواتير
 */
export const useInvoiceCalculations = ({ form, items, onError }: UseInvoiceCalculationsProps) => {
  const [totals, setTotals] = useState<InvoiceTotals>({
    subtotal: 0,
    discountAmount: 0,
    taxAmount: 0,
    totalAmount: 0,
    remainingAmount: 0
  });

  const [isCalculating, setIsCalculating] = useState(false);

  // حساب المجاميع
  const calculateTotals = useCallback(async () => {
    if (isCalculating) return;

    setIsCalculating(true);
    try {
      // حساب المجموع الفرعي من الأصناف
      const subtotal = calculateItemsSubtotal(items);

      // الحصول على قيم الخصم والضريبة من النموذج
      const discountAmount = form.getFieldValue('discount_amount') || 0;

      // حساب المجاميع باستخدام الدالة الموحدة
      const calculatedTotals = calculateInvoiceTotals(subtotal, discountAmount);

      // حساب المبلغ المتبقي
      const paidAmount = form.getFieldValue('paid_amount') || 0;
      const remainingAmount = calculatedTotals.totalAmount - paidAmount;

      const newTotals: InvoiceTotals = {
        subtotal: calculatedTotals.subtotal,
        discountAmount: calculatedTotals.discountAmount,
        taxAmount: calculatedTotals.taxAmount,
        totalAmount: calculatedTotals.totalAmount,
        remainingAmount: Math.round((remainingAmount + Number.EPSILON) * 100) / 100
      };

      setTotals(newTotals);

      // تحديث قيم النموذج
      form.setFieldsValue({
        subtotal: newTotals.subtotal,
        tax_amount: newTotals.taxAmount,
        total_amount: newTotals.totalAmount,
        remaining_amount: newTotals.remainingAmount
      });

      Logger.info('useInvoiceCalculations', 'تم حساب المجاميع بنجاح:', newTotals);

    } catch (_error) {
      const errorMessage = _error instanceof Error ? _error.message : 'خطأ في حساب المجاميع';
      Logger.error('useInvoiceCalculations', 'خطأ في حساب المجاميع:', _error);

      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsCalculating(false);
    }
  }, [form, items, isCalculating, onError]);

  // حساب المجاميع عند تغيير الأصناف
  useEffect(() => {
    if (items.length > 0) {
      // استخدام timeout لتجنب الحسابات المتكررة
      const timeoutId = setTimeout(() => {
        calculateTotals();
      }, 100);

      return () => clearTimeout(timeoutId);
    } else {
      // إعادة تعيين المجاميع إذا لم تكن هناك أصناف
      const emptyTotals: InvoiceTotals = {
        subtotal: 0,
        discountAmount: 0,
        taxAmount: 0,
        totalAmount: 0,
        remainingAmount: 0
      };

      setTotals(emptyTotals);
      form.setFieldsValue({
        subtotal: 0,
        tax_amount: 0,
        total_amount: 0,
        remaining_amount: 0
      });
      return undefined;
    }
  }, [items, calculateTotals, form]);

  // التحقق من صحة البيانات المالية
  const validateFinancials = useCallback(() => {
    try {
      const validation = validateInvoiceFinancials({
        subtotal: totals.subtotal,
        discountAmount: totals.discountAmount,
        taxAmount: totals.taxAmount,
        totalAmount: totals.totalAmount,
        paidAmount: form.getFieldValue('paid_amount') || 0
      });

      return validation;
    } catch (_error) {
      Logger.error('useInvoiceCalculations', 'خطأ في التحقق من البيانات المالية:', _error);
      return {
        isValid: false,
        errors: ['خطأ في التحقق من البيانات المالية']
      };
    }
  }, [totals, form]);

  // حساب المبلغ المتبقي عند تغيير المبلغ المدفوع
  const updateRemainingAmount = useCallback((paidAmount: number) => {
    const remaining = totals.totalAmount - (paidAmount || 0);
    const roundedRemaining = Math.round((remaining + Number.EPSILON) * 100) / 100;

    setTotals(prev => ({
      ...prev,
      remainingAmount: roundedRemaining
    }));

    form.setFieldsValue({
      remaining_amount: roundedRemaining
    });
  }, [totals.totalAmount, form]);

  // إعادة حساب المجاميع عند تغيير الخصم أو الضريبة
  const recalculateOnFieldChange = useCallback((changedFields: any[]) => {
    const relevantFields = ['discount_amount', 'tax_amount', 'paid_amount'];
    const hasRelevantChange = changedFields.some(field =>
      relevantFields.includes(field.name?.[0])
    );

    if (hasRelevantChange) {
      // تأخير قصير لضمان تحديث القيم في النموذج
      setTimeout(() => {
        calculateTotals();
      }, 50);
    }
  }, [calculateTotals]);

  // دالة لتحديث total_price للأصناف
  const updateItemTotals = useCallback((updatedItems: InvoiceItem[]) => {
    return updatedItems.map(item => ({
      ...item,
      total_price: (item.quantity || 0) * (item.unit_price || 0)
    }));
  }, []);

  return {
    totals,
    isCalculating,
    calculateTotals,
    validateFinancials,
    updateRemainingAmount,
    recalculateOnFieldChange,
    updateItemTotals
  };
};

/**
 * Hook مبسط لحساب مجاميع الأصناف فقط
 */
export const useItemsCalculation = (items: InvoiceItem[]) => {
  const [subtotal, setSubtotal] = useState(0);

  useEffect(() => {
    try {
      const calculatedSubtotal = calculateItemsSubtotal(items);
      setSubtotal(calculatedSubtotal);
    } catch (_error) {
      Logger.error('useItemsCalculation', 'خطأ في حساب مجموع الأصناف:', _error);
      setSubtotal(0);
    }
  }, [items]);

  return subtotal;
};

/**
 * Hook للتحقق من صحة البيانات المالية
 */
export const useFinancialValidation = () => {
  const validateInvoiceData = useCallback((data: {
    subtotal: number;
    discountAmount?: number;
    taxAmount?: number;
    totalAmount: number;
    paidAmount?: number;
  }) => {
    try {
      return validateInvoiceFinancials(data);
    } catch (_error) {
      Logger.error('useFinancialValidation', 'خطأ في التحقق من البيانات:', _error);
      return {
        isValid: false,
        errors: ['خطأ في التحقق من البيانات المالية']
      };
    }
  }, []);

  return { validateInvoiceData };
};

export default useInvoiceCalculations;