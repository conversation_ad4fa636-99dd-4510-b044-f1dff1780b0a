/**
 * مكون اختيار قالب التقرير
 * يتيح للمستخدم اختيار القالب المناسب لطباعة التقرير
 */

import React, { useState } from 'react';
import {
  Select,
  Button,
  Space,
  Card,
  Typography,
  Tag,
  Tooltip,
  Modal,
  Row,
  Col,
  Divider,
  Alert
} from 'antd';
import {
  FileTextOutlined,
  EyeOutlined,
  SettingOutlined,
  PrinterOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useReportTemplates, ReportTemplate } from '../../hooks/useReportTemplates';
import EnhancedTemplateCreator from '../Settings/EnhancedTemplateCreator';
import { EnhancedTemplate } from '../../types/enhancedTemplateTypes';

const { Text, Title } = Typography;
const { Option } = Select;

interface ReportTemplateSelectorProps {
  onTemplateSelect?: (template: ReportTemplate) => void;
  onPrintWithTemplate?: (template: ReportTemplate) => void;
  showPrintButton?: boolean;
  size?: 'small' | 'middle' | 'large';
  style?: React.CSSProperties;
  // خصائص جديدة للقوالب الموحدة
  reportType?: string; // نوع التقرير لاختيار القالب المناسب تلقائياً
  category?: string; // فئة التقرير لفلترة القوالب
  autoSelectTemplate?: boolean; // اختيار القالب المناسب تلقائياً
}

const ReportTemplateSelector: React.FC<ReportTemplateSelectorProps> = ({
  onTemplateSelect,
  onPrintWithTemplate,
  showPrintButton = true,
  size = 'middle',
  style,
  reportType,
  category,
  autoSelectTemplate = false
}) => {
  const {
    reportTemplates,
    selectedTemplate,
    loading,
    selectTemplate,
    getTemplateSettings: _getTemplateSettings,
    // الدوال الجديدة للقوالب الموحدة
    getTemplateForReport: _getTemplateForReport,
    getTemplatesByCategory,
    selectTemplateForReport
  } = useReportTemplates();

  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState<ReportTemplate | null>(null);

  // حالة المحرر المتكامل
  const [editorVisible, setEditorVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<EnhancedTemplate | null>(null);

  // فلترة القوالب حسب الفئة إذا تم تحديدها
  const filteredTemplates = category
    ? getTemplatesByCategory(category)
    : reportTemplates;

  // اختيار القالب المناسب تلقائياً عند التحميل
  React.useEffect(() => {
    if (autoSelectTemplate && reportType && !selectedTemplate) {
      selectTemplateForReport(reportType);
    }
  }, [autoSelectTemplate, reportType, selectedTemplate, selectTemplateForReport]);

  // معاينة القالب
  const handlePreview = (template: ReportTemplate) => {
    setPreviewTemplate(template);
    setPreviewVisible(true);
  };

  // اختيار القالب
  const handleTemplateChange = (templateId: string) => {
    selectTemplate(templateId);
    const template = filteredTemplates.find(t => t.id === templateId);
    if (template && onTemplateSelect) {
      onTemplateSelect(template);
    }
  };

  // طباعة بالقالب المحدد
  const handlePrintWithTemplate = () => {
    if (selectedTemplate && onPrintWithTemplate) {
      onPrintWithTemplate(selectedTemplate);
    }
  };

  // الحصول على لون القالب
  const getTemplateColor = (template: ReportTemplate) => {
    return template.settings.primaryColor || '#1890ff';
  };

  // الحصول على وصف القالب
  const getTemplateDescription = (template: ReportTemplate) => {
    const settings = template.settings;
    return `${settings.pageSize || 'A4'} - ${settings.orientation === 'landscape' ? 'أفقي' : 'عمودي'} - خط ${settings.fontSize || 12}`;
  };

  // فتح محرر القوالب المتكامل
  const openTemplateEditor = () => {
    if (selectedTemplate) {
      // تحويل القالب القديم إلى القالب المحسن
      const enhancedTemplate: EnhancedTemplate = {
        metadata: {
          id: selectedTemplate.id,
          name: selectedTemplate.name,
          description: selectedTemplate.description || '',
          type: selectedTemplate.type,
          category: category || 'general',
          isDefault: selectedTemplate.isDefault,
          isActive: selectedTemplate.isActive,
          createdAt: selectedTemplate.createdAt,
          updatedAt: selectedTemplate.updatedAt
        },
        inheritance: {
          inheritsFromGlobal: true,
          customSettings: {
            page: {
              pageSize: selectedTemplate.settings.pageSize as any,
              orientation: selectedTemplate.settings.orientation as any,
              margins: {
                top: selectedTemplate.settings.marginTop || 20,
                bottom: selectedTemplate.settings.marginBottom || 20,
                left: selectedTemplate.settings.marginLeft || 20,
                right: selectedTemplate.settings.marginRight || 20
              }
            },
            font: {
              fontSize: selectedTemplate.settings.fontSize || 12,
              fontFamily: selectedTemplate.settings.fontFamily || 'Arial',
              headerSize: 18,
              lineSpacing: 1.5
            },
            colors: {
              primaryColor: selectedTemplate.settings.primaryColor || '#1890ff',
              secondaryColor: selectedTemplate.settings.secondaryColor || '#f0f2f5',
              borderColor: '#d9d9d9',
              backgroundColor: '#ffffff',
              textColor: '#000000'
            },
            display: {
              showHeader: selectedTemplate.settings.showHeader ?? true,
              showFooter: selectedTemplate.settings.showFooter ?? true,
              showLogo: selectedTemplate.settings.showLogo ?? true,
              showSignature: false,
              showTerms: true,
              showQR: false
            }
          },
          overrides: []
        }
      };

      setEditingTemplate(enhancedTemplate);
    }
    setEditorVisible(true);
  };

  // إغلاق محرر القوالب
  const closeTemplateEditor = () => {
    setEditorVisible(false);
    setEditingTemplate(null);
  };

  return (
    <div style={style}>
      <Card 
        size="small" 
        title={
          <Space>
            <FileTextOutlined />
            <span>قالب الطباعة</span>
          </Space>
        }
        extra={
          selectedTemplate && (
            <Space>
              <Tooltip title="معاينة القالب">
                <Button
                  type="text"
                  icon={<EyeOutlined />}
                  size="small"
                  onClick={() => handlePreview(selectedTemplate)}
                />
              </Tooltip>
              <Tooltip title="إعدادات القالب">
                <Button
                  type="text"
                  icon={<SettingOutlined />}
                  size="small"
                  onClick={openTemplateEditor}
                  disabled={!selectedTemplate}
                />
              </Tooltip>
            </Space>
          )
        }
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* معلومات إضافية عن الفلترة */}
          {(category || reportType) && (
            <Alert
              message={
                <Space>
                  <InfoCircleOutlined />
                  <Text style={{ fontSize: '12px' }}>
                    {category && `فئة: ${category}`}
                    {category && reportType && ' | '}
                    {reportType && `نوع التقرير: ${reportType}`}
                  </Text>
                </Space>
              }
              type="info"
              showIcon={false}
              style={{ padding: '4px 8px', fontSize: '12px' }}
            />
          )}

          {/* اختيار القالب */}
          <Select
            value={selectedTemplate?.id}
            onChange={handleTemplateChange}
            loading={loading}
            placeholder="اختر قالب الطباعة"
            style={{ width: '100%' }}
            size={size}
            optionLabelProp="label"
          >
            {filteredTemplates.map(template => (
              <Option 
                key={template.id} 
                value={template.id}
                label={template.name}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Space>
                    <Text strong>{template.name}</Text>
                    {template.isDefault && (
                      <Tag color="blue">افتراضي</Tag>
                    )}
                  </Space>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {getTemplateDescription(template)}
                  </Text>
                </div>
              </Option>
            ))}
          </Select>

          {/* معلومات القالب المحدد */}
          {selectedTemplate && (
            <div style={{ 
              padding: '8px 12px', 
              backgroundColor: '#f8f9fa', 
              borderRadius: '6px',
              border: `1px solid ${getTemplateColor(selectedTemplate)}20`
            }}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text strong style={{ color: getTemplateColor(selectedTemplate) }}>
                    {selectedTemplate.name}
                  </Text>
                  <Space>
                    <Button
                      type="text"
                      icon={<EyeOutlined />}
                      size="small"
                      onClick={() => handlePreview(selectedTemplate)}
                    >
                      معاينة
                    </Button>
                    {showPrintButton && (
                      <Button
                        type="primary"
                        icon={<PrinterOutlined />}
                        size="small"
                        onClick={handlePrintWithTemplate}
                      >
                        طباعة
                      </Button>
                    )}
                  </Space>
                </div>
                
                {selectedTemplate.description && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {selectedTemplate.description}
                  </Text>
                )}
                
                <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                  <Tag color="default" style={{ margin: 0 }}>
                    {selectedTemplate.settings.pageSize || 'A4'}
                  </Tag>
                  <Tag color="default" style={{ margin: 0 }}>
                    {selectedTemplate.settings.orientation === 'landscape' ? 'أفقي' : 'عمودي'}
                  </Tag>
                  <Tag color="default" style={{ margin: 0 }}>
                    خط {selectedTemplate.settings.fontSize || 12}
                  </Tag>
                </div>
              </Space>
            </div>
          )}

          {/* تنبيه إذا لم توجد قوالب */}
          {!loading && reportTemplates.length === 0 && (
            <Alert
              message="لا توجد قوالب متاحة"
              description="يمكنك إنشاء قوالب جديدة من إعدادات الطباعة"
              type="info"
              showIcon
              icon={<InfoCircleOutlined />}
            />
          )}
        </Space>
      </Card>

      {/* مودال معاينة القالب */}
      <Modal
        title={
          <Space>
            <EyeOutlined />
            <span>معاينة القالب: {previewTemplate?.name}</span>
          </Space>
        }
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width={600}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            إغلاق
          </Button>,
          previewTemplate && showPrintButton && (
            <Button
              key="print"
              type="primary"
              icon={<PrinterOutlined />}
              onClick={() => {
                if (previewTemplate && onPrintWithTemplate) {
                  onPrintWithTemplate(previewTemplate);
                  setPreviewVisible(false);
                }
              }}
            >
              طباعة بهذا القالب
            </Button>
          )
        ]}
      >
        {previewTemplate && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card size="small" title="إعدادات الصفحة">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>حجم الورق:</Text>
                      <Text strong>{previewTemplate.settings.pageSize || 'A4'}</Text>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>الاتجاه:</Text>
                      <Text strong>
                        {previewTemplate.settings.orientation === 'landscape' ? 'أفقي' : 'عمودي'}
                      </Text>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>حجم الخط:</Text>
                      <Text strong>{previewTemplate.settings.fontSize || 12}</Text>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>نوع الخط:</Text>
                      <Text strong>{previewTemplate.settings.fontFamily || 'Arial'}</Text>
                    </div>
                  </Space>
                </Card>
              </Col>
              
              <Col span={12}>
                <Card size="small" title="إعدادات العرض">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>الرأسية:</Text>
                      <Text strong>{previewTemplate.settings.showHeader ? 'نعم' : 'لا'}</Text>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>التذييل:</Text>
                      <Text strong>{previewTemplate.settings.showFooter ? 'نعم' : 'لا'}</Text>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>الشعار:</Text>
                      <Text strong>{previewTemplate.settings.showLogo ? 'نعم' : 'لا'}</Text>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>اللون الأساسي:</Text>
                      <div style={{ 
                        width: '20px', 
                        height: '20px', 
                        backgroundColor: previewTemplate.settings.primaryColor || '#1890ff',
                        borderRadius: '4px',
                        border: '1px solid #d9d9d9'
                      }} />
                    </div>
                  </Space>
                </Card>
              </Col>
            </Row>

            <Divider />

            <Alert
              message="معاينة القالب"
              description="هذه معاينة مبسطة لإعدادات القالب. للحصول على معاينة كاملة، استخدم خيار الطباعة مع المعاينة."
              type="info"
              showIcon
            />
          </div>
        )}
      </Modal>

      {/* المحرر المتكامل للقوالب */}
      <EnhancedTemplateCreator
        visible={editorVisible}
        onClose={closeTemplateEditor}
        template={editingTemplate || undefined}
        mode="edit"
        reportContext={reportType && category ? {
          reportType: reportType,
          reportCategory: category,
          reportTitle: `تقرير ${reportType}`
        } : undefined}
      />
    </div>
  );
};

export default ReportTemplateSelector;
