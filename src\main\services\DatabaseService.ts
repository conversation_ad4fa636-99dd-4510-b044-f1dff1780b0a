import { SimpleDatabaseService } from './SimpleDatabaseService'

/**
 * خدمة قاعدة البيانات الرئيسية - تستخدم SimpleDatabaseService في الخلفية
 */
export class DatabaseService {
  private static instance: DatabaseService
  private simpleDatabaseService: SimpleDatabaseService

  private constructor() {
    this.simpleDatabaseService = SimpleDatabaseService.getInstance()
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService()
    }
    return DatabaseService.instance
  }

  /**
   * تهيئة قاعدة البيانات
   */
  public async initialize(): Promise<void> {
    return this.simpleDatabaseService.initialize()
  }

  /**
   * الحصول على قاعدة البيانات
   */
  public getDatabase(): any {
    return this.simpleDatabaseService.getDatabase()
  }

  /**
   * إغلاق قاعدة البيانات
   */
  public close(): void {
    return this.simpleDatabaseService.close()
  }

  /**
   * الحصول على مسار قاعدة البيانات الحالي
   */
  public getDatabasePath(): string {
    return this.simpleDatabaseService.getDatabasePath()
  }

  /**
   * تحديد مسار قاعدة البيانات المستخدمة (محلية أو مشتركة)
   */
  public async setDatabasePath(newPath: string): Promise<boolean> {
    return this.simpleDatabaseService.setDatabasePath(newPath)
  }

  /**
   * التبديل إلى قاعدة البيانات المشتركة
   */
  public async switchToSharedDatabase(sharedDbPath: string): Promise<boolean> {
    return this.simpleDatabaseService.switchToSharedDatabase(sharedDbPath)
  }

  /**
   * التبديل إلى قاعدة البيانات المحلية
   */
  public async switchToLocalDatabase(): Promise<boolean> {
    return this.simpleDatabaseService.switchToLocalDatabase()
  }

  /**
   * الحصول على معلومات حالة قاعدة البيانات الحالية
   */
  public getDatabaseStatus(): any {
    return this.simpleDatabaseService.getDatabaseStatus()
  }

  /**
   * فحص نوع قاعدة البيانات المستخدمة حالياً
   */
  public getDatabaseType(): 'local' | 'shared' {
    return this.simpleDatabaseService.getDatabaseType()
  }

  /**
   * حفظ قاعدة البيانات
   */
  public saveDatabase(): void {
    return this.simpleDatabaseService.saveDatabase()
  }

  /**
   * إنشاء نسخة احتياطية
   */
  public async createBackup(fileName: string): Promise<void> {
    return this.simpleDatabaseService.createBackup(fileName)
  }

  /**
   * استعادة نسخة احتياطية
   */
  public async restoreBackup(backupPath: string): Promise<void> {
    return this.simpleDatabaseService.restoreBackup(backupPath)
  }

  /**
   * حذف ملف نسخة احتياطية
   */
  public async deleteBackupFile(backupPath: string): Promise<void> {
    return this.simpleDatabaseService.deleteBackupFile(backupPath)
  }

  /**
   * فحص وجود عمود في جدول
   */
  public columnExists(tableName: string, columnName: string): boolean {
    return this.simpleDatabaseService.columnExists(tableName, columnName)
  }

  /**
   * إضافة عمود بأمان (فقط إذا لم يكن موجوداً)
   */
  public safeAddColumn(tableName: string, columnDefinition: string): void {
    return this.simpleDatabaseService.safeAddColumn(tableName, columnDefinition)
  }

  /**
   * فحص صحة قاعدة البيانات
   */
  public checkHealth(): any {
    try {
      const db = this.getDatabase()
      if (!db) {
        return { healthy: false, error: 'قاعدة البيانات غير متصلة' }
      }

      // اختبار بسيط للاتصال
      const testQuery = db.exec('SELECT 1 as test')
      let result: any = null
      if (testQuery.length > 0 && testQuery[0].values.length > 0) {
        const columns = testQuery[0].columns
        const values = testQuery[0].values[0]
        result = {}
        columns.forEach((col: string, index: number) => {
          result[col] = values[index]
        })
      }

      return {
        healthy: true,
        connected: true,
        test: result?.test === 1
      }
    } catch (error) {
      return {
        healthy: false,
        connected: false,
        error: (error as Error).message
      }
    }
  }
}
