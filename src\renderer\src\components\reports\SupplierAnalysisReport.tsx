/**
 * تقرير تحليل الموردين
 * تقرير شامل لتحليل أداء وجودة الموردين
 */

import React from 'react';
import { Tag, Typography, Progress, Rate } from 'antd';
import UniversalReport from './UniversalReport';
import { DateUtils } from '../../utils/dateConfig';
import type { ReportData, ReportType } from '../../types/reports';
import { createDefaultFilters } from '../../types/reports';

const { Text } = Typography;

const SupplierAnalysisReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير تحليل الموردين...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getSupplierAnalysisReport(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const supplierAnalysisData = response.data;

      // معالجة البيانات
      const processedData = supplierAnalysisData.map((item: any, index: number) => ({
        ...item,
        key: item.supplier_id || index,
        avg_order_value: item.total_orders > 0 ? item.total_amount / item.total_orders : 0,
        overall_score: ((item.quality_score + item.price_competitiveness + (item.on_time_delivery_rate / 20)) / 3).toFixed(1)
      }));

      // حساب الإحصائيات
      const totalSuppliers = processedData.length;
      const totalOrders = processedData.reduce((sum: number, item: any) => sum + item.total_orders, 0);
      const totalAmount = processedData.reduce((sum: number, item: any) => sum + item.total_amount, 0);
      const avgDeliveryRate = processedData.reduce((sum: number, item: any) => sum + item.on_time_delivery_rate, 0) / totalSuppliers;
      const avgQualityScore = processedData.reduce((sum: number, item: any) => sum + item.quality_score, 0) / totalSuppliers;

      const excellentSuppliers = processedData.filter(item => item.status === 'excellent').length;
      const goodSuppliers = processedData.filter(item => item.status === 'good').length;

      const summary = {
        totalSuppliers,
        totalOrders,
        totalAmount: Math.round(totalAmount * 100) / 100,
        avgDeliveryRate: Math.round(avgDeliveryRate * 100) / 100,
        avgQualityScore: Math.round(avgQualityScore * 100) / 100,
        excellentSuppliers,
        goodSuppliers
      };

      console.log('✅ تم إنشاء تقرير تحليل الموردين بنجاح');

      return {
        title: 'تقرير تحليل الموردين',
        data: processedData,
        columns,
        summary,
        metadata: {
          reportType: 'supplier_analysis' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: processedData.length
        }
      };
    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير تحليل الموردين:', error);
      throw error;
    }
  };

  // إعداد الأعمدة
  const columns = [
    {
      title: 'كود المورد',
      dataIndex: 'supplier_code',
      key: 'supplier_code',
      width: 120,
      render: (code: string) => (
        <Text strong style={{ color: '#1890ff' }}>{code}</Text>
      )
    },
    {
      title: 'اسم المورد',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      width: 200,
      render: (name: string) => (
        <Text strong>{name}</Text>
      )
    },
    {
      title: 'عدد الطلبات',
      dataIndex: 'total_orders',
      key: 'total_orders',
      width: 120,
      align: 'center' as const,
      render: (count: number) => (
        <Tag color="blue">{count}</Tag>
      )
    },
    {
      title: 'إجمالي المبلغ (₪)',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 150,
      align: 'right' as const,
      render: (amount: number) => (
        <Text strong style={{ color: '#52c41a' }}>
          {amount.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'معدل التسليم في الوقت',
      dataIndex: 'on_time_delivery_rate',
      key: 'on_time_delivery_rate',
      width: 150,
      align: 'center' as const,
      render: (rate: number) => (
        <Progress
          percent={Math.round(rate)}
          size="small"
          strokeColor={rate >= 90 ? '#52c41a' : rate >= 75 ? '#fa8c16' : '#ff4d4f'}
        />
      )
    },
    {
      title: 'تقييم الجودة',
      dataIndex: 'quality_score',
      key: 'quality_score',
      width: 120,
      align: 'center' as const,
      render: (score: number) => (
        <Rate disabled value={score} allowHalf style={{ fontSize: '14px' }} />
      )
    },
    {
      title: 'تنافسية السعر',
      dataIndex: 'price_competitiveness',
      key: 'price_competitiveness',
      width: 120,
      align: 'center' as const,
      render: (score: number) => (
        <Rate disabled value={score} allowHalf style={{ fontSize: '14px' }} />
      )
    },
    {
      title: 'التقييم العام',
      dataIndex: 'overall_score',
      key: 'overall_score',
      width: 120,
      align: 'center' as const,
      render: (score: string) => (
        <Text strong style={{ color: '#fa8c16' }}>{score}</Text>
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center' as const,
      render: (status: string) => {
        const statusConfig = {
          excellent: { color: 'green', text: 'ممتاز' },
          good: { color: 'blue', text: 'جيد' },
          average: { color: 'orange', text: 'متوسط' },
          poor: { color: 'red', text: 'ضعيف' }
        };
        const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    }
  ];

  return (
    <UniversalReport
      reportType={'supplier_analysis' as ReportType}
      title="تقرير تحليل الموردين"
      description="تقرير شامل لتحليل أداء الموردين مع مؤشرات الجودة والأسعار"
      onGenerateReport={generateReport}
      defaultFilters={createDefaultFilters('supplier_analysis')}
      showDateRange={true}
      showSupplierFilter={true}
      showCategoryFilter={true}
      showStatusFilter={true}
      showAmountRangeFilter={true}
      showAdvancedSearch={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default SupplierAnalysisReport;
