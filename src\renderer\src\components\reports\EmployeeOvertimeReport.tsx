import React from 'react';
import { Tag, Typography, Progress, Statistic, Tooltip } from 'antd';
import {
  ClockCircleOutlined,
  UserOutlined,
  DollarOutlined,
  TeamOutlined,
  CalendarOutlined,
  TrophyOutlined,
  StarOutlined,
  RiseOutlined,
  FallOutlined,
  CheckCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import UniversalReport from './UniversalReport';

import type { ReportData, ReportType } from '../../types/reports';
import { createDefaultFilters } from '../../types/reports';
import { SafeLogger as Logger } from '../../utils/logger';

const { Text } = Typography;

const EmployeeOvertimeReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      Logger.info('EmployeeOvertimeReport', '⏰ بدء إنشاء تقرير الساعات الإضافية...');
      Logger.info('EmployeeOvertimeReport', '🔍 الفلاتر المطبقة:', filters);

      let overtimeData: any[] = [];

      if (!window.electronAPI || !window.electronAPI.getEmployeeOvertimeReport) {
        Logger.info('EmployeeOvertimeReport', '⚠️ التطبيق يعمل في وضع المتصفح أو الدالة غير متوفرة - استخدام بيانات وهمية');

        // بيانات وهمية للساعات الإضافية
        overtimeData = [
          {
            employee_id: 1,
            employee_code: 'EMP001',
            employee_name: 'أحمد محمد علي',
            department_name: 'قسم المحاسبة',
            position: 'محاسب أول',
            hourly_rate: 25.0,
            overtime_rate: 37.5,
            overtime_days: 12,
            total_overtime_hours: 48.5,
            avg_overtime_per_day: 4.0,
            max_overtime_day: 8.0,
            total_overtime_amount: 1818.75,
            avg_overtime_amount: 151.56,
            total_working_days: 22,
            overtime_frequency_rate: 54.55
          },
          {
            employee_id: 2,
            employee_code: 'EMP002',
            employee_name: 'فاطمة أحمد حسن',
            department_name: 'قسم المبيعات',
            position: 'مندوب مبيعات',
            hourly_rate: 22.0,
            overtime_rate: 33.0,
            overtime_days: 15,
            total_overtime_hours: 62.0,
            avg_overtime_per_day: 4.1,
            max_overtime_day: 6.5,
            total_overtime_amount: 2046.0,
            avg_overtime_amount: 136.4,
            total_working_days: 23,
            overtime_frequency_rate: 65.22
          },
          {
            employee_id: 3,
            employee_code: 'EMP003',
            employee_name: 'محمد عبد الله',
            department_name: 'قسم الإنتاج',
            position: 'عامل إنتاج',
            hourly_rate: 18.0,
            overtime_rate: 27.0,
            overtime_days: 8,
            total_overtime_hours: 28.0,
            avg_overtime_per_day: 3.5,
            max_overtime_day: 5.0,
            total_overtime_amount: 756.0,
            avg_overtime_amount: 94.5,
            total_working_days: 21,
            overtime_frequency_rate: 38.10
          },
          {
            employee_id: 4,
            employee_code: 'EMP004',
            employee_name: 'سارة محمود',
            department_name: 'قسم الموارد البشرية',
            position: 'أخصائي موارد بشرية',
            hourly_rate: 24.0,
            overtime_rate: 36.0,
            overtime_days: 6,
            total_overtime_hours: 18.5,
            avg_overtime_per_day: 3.1,
            max_overtime_day: 4.0,
            total_overtime_amount: 666.0,
            avg_overtime_amount: 111.0,
            total_working_days: 22,
            overtime_frequency_rate: 27.27
          }
        ];
      } else {
        // استعلام البيانات من قاعدة البيانات
        const response = await window.electronAPI.getEmployeeOvertimeReport({
          departmentId: filters.departmentId,
          employeeId: filters.employeeId,
          dateRange: filters.dateRange
        });

        if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
          throw new Error('لا توجد بيانات متاحة');
        }

        overtimeData = response.data;
      }

      // تعريف الأعمدة
      const columns = [
        {
          title: 'الموّف',
          key: 'employee',
          render: (record: any) => (
            <div>
              <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
                <UserOutlined style={{ marginLeft: '8px' }} />
                {record.employee_name}
              </div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.employee_code} - {record.position}
              </Text>
            </div>
          ),
          width: 200
        },
        {
          title: 'القسم',
          key: 'department_name',
          format: 'text' as const,
          render: (record: any) => (
            <Tag color="blue" icon={<TeamOutlined />}>
              {record.department_name}
            </Tag>
          ),
          width: 150,
          align: 'center' as const
        },
        {
          title: 'أيام الساعات الإضافية',
          key: 'overtime_days',
          format: 'number' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <CalendarOutlined style={{ color: '#52c41a', marginLeft: '4px' }} />
              <strong style={{ color: '#52c41a', fontSize: '16px' }}>
                {record.overtime_days}
              </strong>
              <div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  يوم
                </Text>
              </div>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'إجمالي الساعات الإضافية',
          key: 'total_overtime_hours',
          format: 'number' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <ClockCircleOutlined style={{ color: '#1890ff', marginLeft: '4px' }} />
              <strong style={{ color: '#1890ff', fontSize: '16px' }}>
                {record.total_overtime_hours.toFixed(1)}
              </strong>
              <div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  ساعة
                </Text>
              </div>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'متوسط الساعات/يوم',
          key: 'avg_overtime_per_day',
          format: 'number' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <StarOutlined style={{ color: '#fa8c16', marginLeft: '4px' }} />
              <strong style={{ color: '#fa8c16', fontSize: '16px' }}>
                {record.avg_overtime_per_day.toFixed(1)}
              </strong>
              <div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  ساعة/يوم
                </Text>
              </div>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'أقصى ساعات في يوم',
          key: 'max_overtime_day',
          format: 'number' as const,
          render: (record: any) => {
            const max = record.max_overtime_day;
            const color = max >= 8 ? '#ff4d4f' : max >= 6 ? '#fa8c16' : '#52c41a';
            return (
              <div style={{ textAlign: 'center' }}>
                <TrophyOutlined style={{ color, marginLeft: '4px' }} />
                <strong style={{ color, fontSize: '16px' }}>
                  {max.toFixed(1)}
                </strong>
                <div>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    ساعة
                  </Text>
                </div>
              </div>
            );
          },
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'إجمالي المبلغ',
          key: 'total_overtime_amount',
          format: 'currency' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <DollarOutlined style={{ color: '#722ed1', marginLeft: '4px' }} />
              <strong style={{ color: '#722ed1', fontSize: '16px' }}>
                {record.total_overtime_amount.toLocaleString('ar-EG', { minimumFractionDigits: 2 })}
              </strong>
              <div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  ج.م
                </Text>
              </div>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'معدل التكرار',
          key: 'overtime_frequency_rate',
          format: 'percentage' as const,
          render: (record: any) => {
            const rate = record.overtime_frequency_rate;
            const color = rate >= 70 ? '#ff4d4f' :
                         rate >= 50 ? '#fa8c16' :
                         rate >= 30 ? '#1890ff' : '#52c41a';
            return (
              <div style={{ textAlign: 'center' }}>
                <div>
                  <RiseOutlined style={{ color, marginLeft: '4px' }} />
                  <strong style={{ color, fontSize: '16px' }}>{rate.toFixed(1)}%</strong>
                </div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  تكرار
                </Text>
              </div>
            );
          },
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'تقييم الأداء',
          key: 'performance_rating',
          render: (record: any) => {
            let rating = 'عادي';
            let color = 'blue';
            let icon = <CheckCircleOutlined />;
            
            if (record.overtime_frequency_rate > 70) {
              rating = 'مرتفع جداً';
              color = 'red';
              icon = <WarningOutlined />;
            } else if (record.overtime_frequency_rate > 50) {
              rating = 'مرتفع';
              color = 'orange';
              icon = <RiseOutlined />;
            } else if (record.overtime_frequency_rate > 30) {
              rating = 'متوسط';
              color = 'blue';
              icon = <CheckCircleOutlined />;
            } else {
              rating = 'منخفض';
              color = 'green';
              icon = <CheckCircleOutlined />;
            }
            
            return (
              <div style={{ textAlign: 'center' }}>
                <Tag color={color} icon={icon}>
                  {rating}
                </Tag>
              </div>
            );
          },
          width: 120
        }
      ];

      // حساب الإحصائيات
      const totalEmployees = overtimeData.length;
      const totalOvertimeHours = overtimeData.reduce((sum, emp) => sum + (emp.total_overtime_hours || 0), 0);
      const totalOvertimeAmount = overtimeData.reduce((sum, emp) => sum + (emp.total_overtime_amount || 0), 0);
      const avgOvertimePerEmployee = totalEmployees > 0 ? totalOvertimeHours / totalEmployees : 0;
      const avgAmountPerEmployee = totalEmployees > 0 ? totalOvertimeAmount / totalEmployees : 0;
      const highOvertimeEmployees = overtimeData.filter(emp => emp.overtime_frequency_rate > 50).length;
      const maxOvertimeEmployee = overtimeData.reduce((max: any, emp: any) =>
        (emp.total_overtime_hours > (max?.total_overtime_hours || 0)) ? emp : max, overtimeData[0] || null
      );

      Logger.info('EmployeeOvertimeReport', '✅ تم إنشاء تقرير الساعات الإضافية بنجاح: ${totalEmployees} موّف');

      return {
        title: 'تقرير الساعات الإضافية للموظفين',
        data: overtimeData,
        columns,
        summary: {
          totalEmployees,
          totalOvertimeHours: Math.round(totalOvertimeHours * 10) / 10,
          totalOvertimeAmount: Math.round(totalOvertimeAmount * 100) / 100,
          avgOvertimePerEmployee: Math.round(avgOvertimePerEmployee * 10) / 10,
          avgAmountPerEmployee: Math.round(avgAmountPerEmployee * 100) / 100,
          highOvertimeEmployees,
          maxOvertimeEmployee
        },
        metadata: {
          reportType: 'employee_overtime' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: overtimeData.length
        }
      };
    } catch (error) {
      Logger.error('EmployeeOvertimeReport', 'خطأ في إنشاء تقرير الساعات الإضافية:', error);
      throw new Error('فشل في إنشاء تقرير الساعات الإضافية');
    }
  };

  return (
    <UniversalReport
      reportType={'employee_overtime' as ReportType}
      title="تقرير الساعات الإضافية للموظفين"
      description="تقرير شامل للساعات الإضافية مع التكاليف ومعدلات التكرار والتقييم"
      onGenerateReport={generateReport}
      defaultFilters={createDefaultFilters('employee_overtime')}
      showDateRange={true}
      showEmployeeFilter={true}
      showDepartmentFilter={true}
      showAdvancedSearch={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default EmployeeOvertimeReport;