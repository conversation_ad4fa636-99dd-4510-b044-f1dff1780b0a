import React from 'react';
import { Tag, Typography, Progress, Space } from 'antd';
import { StarOutlined } from '@ant-design/icons';
import UniversalReport from './UniversalReport';
// import { DateUtils } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger';
import type { ReportData, ReportType } from '../../types/reports';
import { createDefaultFilters } from '../../types/reports';

const { Text } = Typography;

const PurchasePerformanceDashboard: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء لوحة مراقبة أداء المشتريات...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getPurchasePerformanceReport(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const performanceData = response.data;

      // معالجة البيانات
      const processedData = performanceData.map((item: any, index: number) => ({
        ...item,
        key: item.supplier_id || index,
        avg_order_value: item.total_orders > 0 ? item.total_value / item.total_orders : 0,
        performance_score: ((item.on_time_rate + item.quality_score * 20 + item.price_competitiveness * 20) / 3).toFixed(1)
      }));

      // حساب الإحصائيات
      const totalSuppliers = processedData.length;
      const totalOrders = processedData.reduce((sum: number, item: any) => sum + item.total_orders, 0);
      const totalValue = processedData.reduce((sum: number, item: any) => sum + item.total_value, 0);
      const avgDeliveryRate = processedData.reduce((sum: number, item: any) => sum + item.on_time_rate, 0) / totalSuppliers;
      const avgQualityScore = processedData.reduce((sum: number, item: any) => sum + item.quality_score, 0) / totalSuppliers;

      const excellentSuppliers = processedData.filter(item => item.status === 'excellent').length;
      const goodSuppliers = processedData.filter(item => item.status === 'good').length;

      const summary = {
        totalSuppliers,
        totalOrders,
        totalValue: Math.round(totalValue * 100) / 100,
        avgDeliveryRate: Math.round(avgDeliveryRate * 100) / 100,
        avgQualityScore: Math.round(avgQualityScore * 100) / 100,
        excellentSuppliers,
        goodSuppliers,
        avgOrderValue: totalOrders > 0 ? Math.round((totalValue / totalOrders) * 100) / 100 : 0
      };

      console.log('✅ تم إنشاء لوحة مراقبة أداء المشتريات بنجاح');

      return {
        title: 'لوحة مراقبة أداء المشتريات',
        data: processedData,
        columns,
        summary,
        metadata: {
          reportType: 'purchase_performance' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: processedData.length
        }
      };
    } catch (error) {
      console.error('❌ خطأ في إنشاء لوحة مراقبة أداء المشتريات:', error);
      throw error;
    }
  };

  // إعداد الأعمدة
  const columns = [
    {
      title: 'كود المورد',
      dataIndex: 'supplier_code',
      key: 'supplier_code',
      width: 120,
      render: (code: string) => (
        <Text strong style={{ color: '#1890ff' }}>{code}</Text>
      )
    },
    {
      title: 'اسم المورد',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      width: 200,
      render: (name: string) => (
        <Text strong>{name}</Text>
      )
    },
    {
      title: 'عدد الطلبات',
      dataIndex: 'total_orders',
      key: 'total_orders',
      width: 120,
      align: 'center' as const,
      render: (count: number) => (
        <Tag color="blue">{count}</Tag>
      )
    },
    {
      title: 'إجمالي القيمة (₪)',
      dataIndex: 'total_value',
      key: 'total_value',
      width: 150,
      align: 'right' as const,
      render: (value: number) => (
        <Text strong style={{ color: '#52c41a' }}>
          {value.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'متوسط قيمة الطلب (₪)',
      dataIndex: 'avg_order_value',
      key: 'avg_order_value',
      width: 150,
      align: 'right' as const,
      render: (value: number) => (
        <Text style={{ color: '#fa8c16' }}>
          {value.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'معدل التسليم في الوقت',
      dataIndex: 'on_time_rate',
      key: 'on_time_rate',
      width: 150,
      align: 'center' as const,
      render: (rate: number) => (
        <Progress
          percent={Math.round(rate)}
          size="small"
          strokeColor={rate >= 90 ? '#52c41a' : rate >= 75 ? '#fa8c16' : '#ff4d4f'}
        />
      )
    },
    {
      title: 'تقييم الجودة',
      dataIndex: 'quality_score',
      key: 'quality_score',
      width: 120,
      align: 'center' as const,
      render: (score: number) => (
        <Space>
          <StarOutlined style={{ color: '#faad14' }} />
          <Text>{score.toFixed(1)}</Text>
        </Space>
      )
    },
    {
      title: 'نقاط الأداء',
      dataIndex: 'performance_score',
      key: 'performance_score',
      width: 120,
      align: 'center' as const,
      render: (score: string) => (
        <Text strong style={{ color: '#722ed1' }}>{score}</Text>
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center' as const,
      render: (status: string) => {
        const statusConfig = {
          excellent: { color: 'green', text: 'ممتاز' },
          good: { color: 'blue', text: 'جيد' },
          average: { color: 'orange', text: 'متوسط' },
          poor: { color: 'red', text: 'ضعيف' }
        };
        const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    }
  ];

  return (
    <UniversalReport
      reportType={'purchase_performance' as ReportType}
      title="لوحة مراقبة أداء المشتريات"
      description="تقرير شامل لمراقبة أداء المشتريات والموردين مع مؤشرات الجودة"
      onGenerateReport={generateReport}
      defaultFilters={createDefaultFilters('purchase_performance')}
      showDateRange={true}
      showSupplierFilter={true}
      showStatusFilter={true}
      showAmountRangeFilter={true}
      showAdvancedSearch={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default PurchasePerformanceDashboard;
