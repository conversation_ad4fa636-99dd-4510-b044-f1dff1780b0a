import React from 'react'
import { Select, Space, Form, InputNumber, Input, DatePicker, Row, Col } from 'antd'
import {
  WalletOutlined,
  BankOutlined,
  CreditCardOutlined,
  DollarOutlined,
  FileTextOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { Option } = Select

// أنواع طرق الدفع المتاحة
export const PAYMENT_METHODS = {
  CASH: 'cash',
  CHECK: 'check',
  BANK_TRANSFER: 'bank_transfer',
  CREDIT_CARD: 'credit_card',
  RECEIPT_VOUCHER: 'receipt_voucher',
  PAYMENT_VOUCHER: 'payment_voucher'
} as const

export type PaymentMethodType = typeof PAYMENT_METHODS[keyof typeof PAYMENT_METHODS]

// أيقونات طرق الدفع
export const getPaymentMethodIcon = (method: PaymentMethodType) => {
  switch (method) {
    case PAYMENT_METHODS.CASH:
      return <WalletOutlined style={{ color: '#52c41a' }} />
    case PAYMENT_METHODS.CHECK:
      return <BankOutlined style={{ color: '#1890ff' }} />
    case PAYMENT_METHODS.BANK_TRANSFER:
      return <BankOutlined style={{ color: '#722ed1' }} />
    case PAYMENT_METHODS.CREDIT_CARD:
      return <CreditCardOutlined style={{ color: '#fa8c16' }} />
    case PAYMENT_METHODS.RECEIPT_VOUCHER:
      return <FileTextOutlined style={{ color: '#13c2c2' }} />
    case PAYMENT_METHODS.PAYMENT_VOUCHER:
      return <FileTextOutlined style={{ color: '#eb2f96' }} />
    default:
      return <DollarOutlined />
  }
}

// نصوص طرق الدفع
export const getPaymentMethodText = (method: PaymentMethodType) => {
  switch (method) {
    case PAYMENT_METHODS.CASH:
      return 'نقدي'
    case PAYMENT_METHODS.CHECK:
      return 'شيك'
    case PAYMENT_METHODS.BANK_TRANSFER:
      return 'تحويل بنكي'
    case PAYMENT_METHODS.CREDIT_CARD:
      return 'بطاقة ائتمان'
    case PAYMENT_METHODS.RECEIPT_VOUCHER:
      return 'سند قبض'
    case PAYMENT_METHODS.PAYMENT_VOUCHER:
      return 'سند دفع'
    default:
      return method
  }
}

// خصائص مكون اختيار طريقة الدفع
interface PaymentMethodSelectorProps {
  value?: PaymentMethodType
  onChange?: (value: PaymentMethodType) => void
  placeholder?: string
  disabled?: boolean
  allowedMethods?: PaymentMethodType[]
  size?: 'small' | 'middle' | 'large'
}

// مكون اختيار طريقة الدفع المحسن
export const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  value,
  onChange,
  placeholder = 'اختر طريقة الدفع',
  disabled = false,
  allowedMethods,
  size = 'middle'
}) => {
  const methods = allowedMethods || Object.values(PAYMENT_METHODS)

  // التحقق من صحة البيانات
  if (!methods || methods.length === 0) {
    return (
      <Select
        placeholder="لا توجد طرق دفع متاحة"
        disabled={true}
        size={size as 'small' | 'middle' | 'large'}
        style={{ width: '100%' }}
        notFoundContent="لا توجد طرق دفع متاحة"
      />
    )
  }

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      disabled={disabled}
      size={size as 'small' | 'middle' | 'large'}
      style={{ width: '100%' }}
      showSearch
      optionFilterProp="children"
      filterOption={(input, option) =>
        option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
      }
      notFoundContent="لا توجد طريقة دفع مطابقة"
    >
      {methods.map(method => (
        <Option key={method} value={method}>
          <Space>
            {getPaymentMethodIcon(method)}
            {getPaymentMethodText(method)}
          </Space>
        </Option>
      ))}
    </Select>
  )
}

// خصائص نموذج الدفع
interface PaymentFormProps {
  paymentMethod: PaymentMethodType
  maxAmount?: number
  onFieldChange?: (field: string, value: any) => void
  showBankAccounts?: boolean
  bankAccounts?: Array<{ id: number; name: string; account_number: string }>
}

// مكون نموذج الدفع
export const PaymentForm: React.FC<PaymentFormProps> = ({
  paymentMethod,
  maxAmount,
  showBankAccounts = false,
  bankAccounts = []
}) => {
  const renderPaymentFields = () => {
    switch (paymentMethod) {
      case PAYMENT_METHODS.CASH:
        return (
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="amount"
                label="المبلغ النقدي"
                rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  max={maxAmount}
                  formatter={value => `₪ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => parseFloat((value || '').replace(/₪\s?|(,*)/g, '')) || 0}
                />
              </Form.Item>
            </Col>
          </Row>
        )

      case PAYMENT_METHODS.CHECK:
        return (
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="amount"
                label="مبلغ الشيك"
                rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  max={maxAmount}
                  formatter={value => `₪ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => parseFloat((value || '').replace(/₪\s?|(,*)/g, '')) || 0}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="check_number"
                label="رقم الشيك"
                rules={[{ required: true, message: 'يرجى إدخال رقم الشيك' }]}
              >
                <Input placeholder="رقم الشيك" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="bank_name"
                label="اسم البنك"
                rules={[{ required: true, message: 'يرجى إدخال اسم البنك' }]}
              >
                <Input placeholder="اسم البنك" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="due_date"
                label="تاريخ الاستحقاق"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الاستحقاق' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="تاريخ الاستحقاق"
                  disabledDate={(current) => current && current < dayjs().startOf('day')}
                />
              </Form.Item>
            </Col>
          </Row>
        )

      case PAYMENT_METHODS.BANK_TRANSFER:
        return (
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="amount"
                label="مبلغ التحويل"
                rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  max={maxAmount}
                  formatter={value => `₪ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => parseFloat((value || '').replace(/₪\s?|(,*)/g, '')) || 0}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="reference_number"
                label="رقم المرجع"
                rules={[{ required: true, message: 'يرجى إدخال رقم المرجع' }]}
              >
                <Input placeholder="رقم المرجع" />
              </Form.Item>
            </Col>
            {showBankAccounts && (
              <Col span={24}>
                <Form.Item
                  name="bank_account_id"
                  label="الحساب البنكي"
                  rules={[{ required: true, message: 'يرجى اختيار الحساب البنكي' }]}
                >
                  <Select placeholder="اختر الحساب البنكي">
                    {bankAccounts.map(account => (
                      <Option key={account.id} value={account.id}>
                        {account.name} - {account.account_number}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            )}
          </Row>
        )

      case PAYMENT_METHODS.RECEIPT_VOUCHER:
      case PAYMENT_METHODS.PAYMENT_VOUCHER:
        return (
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="amount"
                label="المبلغ"
                rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  max={maxAmount}
                  formatter={value => `₪ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => parseFloat((value || '').replace(/₪\s?|(,*)/g, '')) || 0}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="voucher_number"
                label="رقم السند"
                rules={[{ required: true, message: 'يرجى إدخال رقم السند' }]}
              >
                <Input placeholder="رقم السند" />
              </Form.Item>
            </Col>
          </Row>
        )

      default:
        return (
          <Form.Item
            name="amount"
            label="المبلغ"
            rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="0"
              min={0}
              max={maxAmount}
              formatter={value => `₪ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => parseFloat((value || '').replace(/₪\s?|(,*)/g, '')) || 0}
            />
          </Form.Item>
        )
    }
  }

  return (
    <>
      {renderPaymentFields()}
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name="payment_date"
            label="تاريخ الدفع"
            rules={[{ required: true, message: 'يرجى اختيار تاريخ الدفع' }]}
            initialValue={dayjs()}
          >
            <DatePicker
              style={{ width: '100%' }}
              placeholder="تاريخ الدفع"
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name="notes"
            label="ملاحّات"
          >
            <Input.TextArea
              placeholder="ملاحّات إضافية"
              rows={3}
            />
          </Form.Item>
        </Col>
      </Row>
    </>
  )
}
