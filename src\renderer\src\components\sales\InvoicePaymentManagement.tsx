import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Select, 
  Input, 
  Space, 
  message, 
  Row,
  Col,
  Statistic,
  DatePicker,
  Tag,
  Descriptions,
  InputNumber,
  Divider,
  List,
  Typography
} from 'antd'
import {
  DollarOutlined,
  CreditCardOutlined,
  FileTextOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
  CheckOutlined,
  BankOutlined,
  UserOutlined,
  PrinterOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'
import dayjs from 'dayjs'
import { getCurrencySymbol, formatCurrency } from '../../utils/settings'
import { ReceiptPrintButton } from '../common'

const { Option } = Select
const { TextArea } = Input
const { Text } = Typography

interface InvoicePaymentManagementProps {
  onBack: () => void
}

const InvoicePaymentManagement: React.FC<InvoicePaymentManagementProps> = ({ onBack }) => {
  const [unpaidInvoices, setUnpaidInvoices] = useState([])
  const [bankAccounts, setBankAccounts] = useState([])
  const [loading, setLoading] = useState(false)
  const [paymentModalVisible, setPaymentModalVisible] = useState(false)
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null)
  const [paymentMethod, setPaymentMethod] = useState('cash')
  const [form] = Form.useForm()

  // حالة لتخزين بيانات الإيصال المطبوع
  const [lastPaymentReceipt, setLastPaymentReceipt] = useState<any>(null)

  useEffect(() => {
    loadUnpaidInvoices()
    loadBankAccounts()
  }, [])

  const loadUnpaidInvoices = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.getUnpaidInvoices('customer')
      if (response.success) {
        setUnpaidInvoices(response.data)
      } else {
        message.error('فشل في تحميل الفواتير غير المدفوعة')
      }
    } catch (error) {
      message.error('خطأ في تحميل الفواتير غير المدفوعة')
    }
    setLoading(false)
  }

  const loadBankAccounts = async () => {
    try {
      const response = await window.electronAPI.getBankAccounts()
      if (response.success) {
        setBankAccounts(response.data)
      }
    } catch (error) {
      Logger.error('InvoicePaymentManagement', 'خطأ في تحميل الحسابات المصرفية:', error)
    }
  }

  const handlePayment = async (values: any) => {
    try {
      let paymentResponse
      
      // إنشاء الدفعة حسب نوع الدفع
      if (paymentMethod === 'check') {
        // إنشاء شيك
        const checkData = {
          check_number: values.check_number,
          bank_account_id: values.bank_account_id,
          amount: values.amount,
          issue_date: values.payment_date.format('YYYY-MM-DD'),
          due_date: values.due_date ? values.due_date.format('YYYY-MM-DD') : values.payment_date.format('YYYY-MM-DD'),
          payee_name: 'الشركة',
          notes: values.notes,
          check_type: 'received',
          original_payer: selectedInvoice.customer_name || 'عميل',
          current_holder: 'الشركة',
          reference_type: selectedInvoice.type + '_invoice',
          reference_id: selectedInvoice.id,
          created_by: 1
        }
        paymentResponse = await window.electronAPI.createCheck(checkData)
      } else if (paymentMethod === 'receipt_voucher') {
        // إنشاء سند قبض
        const voucherData = {
          voucher_number: values.voucher_number,
          amount: values.amount,
          receipt_date: values.payment_date.format('YYYY-MM-DD'),
          payer_name: selectedInvoice.customer_name || 'عميل',
          description: `تحصيل من فاتورة رقم ${selectedInvoice.number}`,
          payment_method: values.voucher_payment_method || 'cash',
          bank_account_id: values.bank_account_id,
          reference_type: selectedInvoice.type + '_invoice',
          reference_id: selectedInvoice.id,
          currency_id: 1,
          created_by: 1,
          notes: values.notes
        }
        paymentResponse = await window.electronAPI.createReceiptVoucher(voucherData)
      }

      if (paymentResponse && paymentResponse.success) {
        // ربط الدفعة بالفاتورة
        const linkData = {
          invoice_type: selectedInvoice.type + '_invoice',
          invoice_id: selectedInvoice.id,
          payment_type: paymentMethod === 'check' ? 'check' : 'receipt_voucher',
          payment_id: paymentResponse.data?.checkId || paymentResponse.data?.voucherId,
          amount: values.amount
        }

        const linkResponse = await window.electronAPI.linkInvoiceToPayment(linkData)
        if (linkResponse.success) {
          // إنشاء بيانات الإيصال للطباعة
          const receiptData = {
            id: paymentResponse.data?.checkId || paymentResponse.data?.voucherId || Date.now(),
            receiptNumber: values.voucher_number || values.check_number || `REC-${Date.now()}`,
            receiptDate: values.payment_date.format('YYYY-MM-DD'),
            type: 'payment' as const,
            amount: values.amount,
            paymentMethod: paymentMethod === 'check' ? 'check' :
                          values.voucher_payment_method === 'bank' ? 'bank' : 'cash',
            customerName: selectedInvoice.customer_name || 'عميل',
            description: `تحصيل من فاتورة رقم ${selectedInvoice.number}`,
            notes: values.notes,
            referenceNumber: selectedInvoice.number,
            bankName: paymentMethod === 'check' ?
                     bankAccounts.find(acc => acc.id === values.bank_account_id)?.bank_name : undefined,
            checkNumber: paymentMethod === 'check' ? values.check_number : undefined
          }

          setLastPaymentReceipt(receiptData)
          message.success('تم تسجيل الدفعة وربطها بالفاتورة بنجاح')
          setPaymentModalVisible(false)
          form.resetFields()
          loadUnpaidInvoices()
        } else {
          message.error('تم إنشاء الدفعة ولكن فشل في ربطها بالفاتورة')
        }
      } else {
        message.error('فشل في إنشاء الدفعة')
      }
    } catch (error) {
      Logger.error('InvoicePaymentManagement', 'خطأ في تسجيل الدفعة:', error)
      message.error('خطأ في تسجيل الدفعة')
    }
  }

  const showPaymentModal = (invoice: any) => {
    setSelectedInvoice(invoice)
    setPaymentModalVisible(true)
    form.setFieldsValue({
      payment_date: dayjs(),
      amount: invoice.remaining_amount
    })
  }

  const generateVoucherNumber = async () => {
    try {
      const response = await window.electronAPI.generateReceiptVoucherNumber()
      if (response.success) {
        form.setFieldsValue({ voucher_number: response.data.voucherNumber })
      }
    } catch (error) {
      message.error('خطأ في إنشاء رقم السند')
    }
  }

  const generateCheckNumber = async () => {
    try {
      const response = await window.electronAPI.generateCheckNumber()
      if (response.success) {
        form.setFieldsValue({ check_number: response.data.checkNumber })
      }
    } catch (error) {
      message.error('خطأ في إنشاء رقم الشيك')
    }
  }

  const getInvoiceTypeText = (type: string) => {
    switch (type) {
      case 'sales': return 'مبيعات'
      case 'paint': return 'دهان'
      default: return type
    }
  }

  const columns = [
    {
      title: 'نوع الفاتورة',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={type === 'sales' ? 'blue' : 'orange'}>
          {getInvoiceTypeText(type)}
        </Tag>
      )
    },
    {
      title: 'رقم الفاتورة',
      dataIndex: 'number',
      key: 'number',
      render: (number: string) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{number}</span>
      )
    },
    {
      title: 'العميل',
      dataIndex: 'customer_name',
      key: 'customer_name',
      render: (name: string, record: any) => {
        // البحث عن اسم العميل من entity_id
        return (
          <Space>
            <UserOutlined style={{ color: '#1890ff' }} />
            <span>{name || `عميل ${record.entity_id}`}</span>
          </Space>
        )
      }
    },
    {
      title: 'تاريخ الفاتورة',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'المبلغ الإجمالي',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (amount: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          {formatCurrency(amount || 0)}
        </span>
      )
    },
    {
      title: 'المبلغ المدفوع',
      dataIndex: 'paid_amount',
      key: 'paid_amount',
      render: (amount: number) => (
        <span style={{ color: '#1890ff' }}>
          {formatCurrency(amount || 0)}
        </span>
      )
    },
    {
      title: 'المبلغ المتبقي',
      dataIndex: 'remaining_amount',
      key: 'remaining_amount',
      render: (amount: number) => (
        <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
          {formatCurrency(amount || 0)}
        </span>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_, record: any) => (
        <Button
          type="primary"
          size="small"
          icon={<DollarOutlined />}
          onClick={() => showPaymentModal(record)}
        >
          تسجيل دفعة
        </Button>
      )
    }
  ]

  const stats = {
    totalInvoices: unpaidInvoices.length,
    totalAmount: unpaidInvoices.reduce((sum: number, invoice: any) => sum + (invoice.remaining_amount || 0), 0),
    salesInvoices: unpaidInvoices.filter((invoice: any) => invoice.type === 'sales').length,
    paintInvoices: unpaidInvoices.filter((invoice: any) => invoice.type === 'paint').length
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>💰 إدارة مدفوعات الفواتير</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            تسجيل وربط المدفوعات بالفواتير غير المدفوعة
          </p>
        </div>
        <Space>
          {lastPaymentReceipt && (
            <ReceiptPrintButton
              receiptData={lastPaymentReceipt}
              buttonText="طباعة آخر إيصال"
              size="middle"
              onPrintSuccess={() => message.success('تم طباعة الإيصال بنجاح')}
              onPrintError={() => message.error('فشل في طباعة الإيصال')}
            />
          )}
          <Button
            type="default"
            icon={<ArrowLeftOutlined />}
            onClick={onBack}
          >
            العودة
          </Button>
        </Space>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الفواتير"
              value={stats.totalInvoices}
              valueStyle={{ color: '#1890ff' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="فواتير المبيعات"
              value={stats.salesInvoices}
              valueStyle={{ color: '#52c41a' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="فواتير الدهان"
              value={stats.paintInvoices}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي المبالغ المستحقة"
              value={stats.totalAmount}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={getCurrencySymbol()}
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      {/* جدول الفواتير غير المدفوعة */}
      <Card title="الفواتير غير المدفوعة">
        <Table
          columns={columns}
          dataSource={unpaidInvoices}
          rowKey={(record: any) => `${record.type}-${record.id}`}
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* نموذج تسجيل الدفعة */}
      <Modal
        title="تسجيل دفعة"
        open={paymentModalVisible}
        onCancel={() => {
          setPaymentModalVisible(false)
          setSelectedInvoice(null)
          form.resetFields()
        }}
        footer={null}
        width={800}
      >
        {selectedInvoice && (
          <>
            <Descriptions title="بيانات الفاتورة" bordered size="small" style={{ marginBottom: '24px' }}>
              <Descriptions.Item label="نوع الفاتورة">
                <Tag color={selectedInvoice.type === 'sales' ? 'blue' : 'orange'}>
                  {getInvoiceTypeText(selectedInvoice.type)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="رقم الفاتورة">{selectedInvoice.number}</Descriptions.Item>
              <Descriptions.Item label="العميل">{selectedInvoice.customer_name || `عميل ${selectedInvoice.entity_id}`}</Descriptions.Item>
              <Descriptions.Item label="المبلغ الإجمالي">
                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                  {formatCurrency(selectedInvoice.total_amount || 0)}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="المبلغ المدفوع">
                <span style={{ color: '#1890ff' }}>
                  {formatCurrency(selectedInvoice.paid_amount || 0)}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="المبلغ المتبقي">
                <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
                  {formatCurrency(selectedInvoice.remaining_amount || 0)}
                </span>
              </Descriptions.Item>
            </Descriptions>

            <Form
              form={form}
              layout="vertical"
              onFinish={handlePayment}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="payment_method"
                    label="طريقة الدفع"
                    rules={[{ required: true, message: 'يرجى اختيار طريقة الدفع' }]}
                    initialValue="receipt_voucher"
                  >
                    <Select
                      placeholder="اختر طريقة الدفع"
                      onChange={(value) => setPaymentMethod(value)}
                    >
                      <Option value="receipt_voucher">سند قبض</Option>
                      <Option value="check">شيك</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="amount"
                    label="المبلغ"
                    rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="0"
                      min={0}
                      max={selectedInvoice.remaining_amount}
                      formatter={value => `${getCurrencySymbol()} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => parseFloat((value || '').replace(new RegExp(`${getCurrencySymbol()}\\s?|(,*)`,'g'), '')) || 0}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="payment_date"
                    label="تاريخ الدفع"
                    rules={[{ required: true, message: 'يرجى اختيار تاريخ الدفع' }]}
                  >
                    <DatePicker
                      style={{ width: '100%' }}
                      placeholder="اختر تاريخ الدفع"
                      format="YYYY-MM-DD"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  {paymentMethod === 'receipt_voucher' ? (
                    <Form.Item
                      name="voucher_number"
                      label="رقم السند"
                      rules={[{ required: true, message: 'يرجى إدخال رقم السند' }]}
                    >
                      <Input
                        placeholder="RV000001"
                        addonAfter={
                          <Button
                            type="link"
                            size="small"
                            onClick={generateVoucherNumber}
                          >
                            توليد
                          </Button>
                        }
                      />
                    </Form.Item>
                  ) : (
                    <Form.Item
                      name="check_number"
                      label="رقم الشيك"
                      rules={[{ required: true, message: 'يرجى إدخال رقم الشيك' }]}
                    >
                      <Input
                        placeholder="CHK000001"
                        addonAfter={
                          <Button
                            type="link"
                            size="small"
                            onClick={generateCheckNumber}
                          >
                            توليد
                          </Button>
                        }
                      />
                    </Form.Item>
                  )}
                </Col>
              </Row>

              {paymentMethod === 'check' && (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="due_date"
                      label="تاريخ استحقاق الشيك"
                    >
                      <DatePicker
                        style={{ width: '100%' }}
                        placeholder="اختر تاريخ الاستحقاق"
                        format="YYYY-MM-DD"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="bank_account_id"
                      label="الحساب المصرفي"
                      rules={[{ required: paymentMethod === 'check', message: 'يرجى اختيار الحساب المصرفي' }]}
                    >
                      <Select placeholder="اختر الحساب المصرفي">
                        {bankAccounts.map((account: any) => (
                          <Option key={account.id} value={account.id}>
                            {account.bank_name} - {account.account_name} ({account.account_number})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              )}

              {paymentMethod === 'receipt_voucher' && (
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="voucher_payment_method"
                      label="طريقة الدفع في السند"
                      initialValue="cash"
                    >
                      <Select placeholder="اختر طريقة الدفع">
                        <Option value="cash">نقدي</Option>
                        <Option value="bank_transfer">تحويل بنكي</Option>
                        <Option value="check">شيك</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="bank_account_id"
                      label="الحساب المصرفي (اختياري)"
                    >
                      <Select placeholder="اختر الحساب المصرفي">
                        {bankAccounts.map((account: any) => (
                          <Option key={account.id} value={account.id}>
                            {account.bank_name} - {account.account_name} ({account.account_number})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              )}

              <Form.Item
                name="notes"
                label="ملاحّات"
              >
                <TextArea
                  placeholder="ملاحّات إضافية حول الدفعة"
                  rows={3}
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<CheckOutlined />}>
                    تسجيل الدفعة
                  </Button>
                  <Button onClick={() => {
                    setPaymentModalVisible(false)
                    setSelectedInvoice(null)
                    form.resetFields()
                  }}>
                    إلغاء
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </>
        )}
      </Modal>
    </div>
  )
}

export default InvoicePaymentManagement
