import { SafeLogger as Logger } from '../utils/logger'
// Company configuration and default settings
export interface CompanyInfo {
  name: string
  nameEn?: string
  address?: string
  phone?: string
  email?: string
  website?: string
  taxNumber?: string
  registrationNumber?: string
  logo?: string
}

// Default company information
export const DEFAULT_COMPANY_INFO: CompanyInfo = {
  name: 'ZET.IA',
  nameEn: 'ZET.IA Company',
  address: 'العنوان الافتراضي',
  phone: '+970-XXX-XXXX',
  email: '<EMAIL>',
  website: 'www.zetia.com',
  taxNumber: '*********',
  registrationNumber: 'REG-123456',
  logo: ''
}

// Print configuration defaults
export const PRINT_DEFAULTS = {
  pageSize: 'A4' as const,
  orientation: 'portrait' as const,
  margins: {
    top: 20,
    bottom: 20,
    left: 20,
    right: 20
  },
  showSignatures: true,
  showInstructions: true,
  companyName: 'ZET.IA'
}

// Validation functions
export const validateCompanyInfo = (info: Partial<CompanyInfo>): string[] => {
  const errors: string[] = []
  
  if (info.email && !isValidEmail(info.email)) {
    errors.push('البريد الإلكتروني غير صحيح')
  }
  
  if (info.phone && !isValidPhone(info.phone)) {
    errors.push('رقم الهاتف غير صحيح')
  }
  
  if (info.website && !isValidWebsite(info.website)) {
    errors.push('رابط الموقع غير صحيح')
  }
  
  return errors
}

// Helper validation functions
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^[+]?[0-9\-\s()]{7,}$/
  return phoneRegex.test(phone)
}

const isValidWebsite = (website: string): boolean => {
  try {
    new URL(website.startsWith('http') ? website : `https://${website}`)
    return true
  } catch {
    return false
  }
}

// Company info management
export class CompanyConfigManager {
  private static readonly STORAGE_KEY = 'company_info'
  
  static async getCompanyInfo(): Promise<CompanyInfo> {
    try {
      // Try to get from electron API first
      if (window.electronAPI?.getSettings) {
        const response = await window.electronAPI.getSettings()
        if (response.success && response.data) {
          return { ...DEFAULT_COMPANY_INFO, ...response.data }
        }
      }
      
      // Fallback to localStorage
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        const parsed = JSON.parse(stored)
        return { ...DEFAULT_COMPANY_INFO, ...parsed }
      }
      
      return DEFAULT_COMPANY_INFO
    } catch (error) {
      Logger.warn('CompanyConfig', 'Failed to load company info, using defaults:', error)
      return DEFAULT_COMPANY_INFO
    }
  }
  
  static async updateCompanyInfo(info: Partial<CompanyInfo>): Promise<boolean> {
    try {
      // Validate the info
      const errors = validateCompanyInfo(info)
      if (errors.length > 0) {
        throw new Error(`Validation errors: ${errors.join(', ')}`)
      }
      
      // Try to save via electron API first
      if (window.electronAPI?.updateSettings) {
        const response = await window.electronAPI.updateSettings(info)
        if (response.success) {
          return true
        }
      }
      
      // Fallback to localStorage
      const current = await this.getCompanyInfo()
      const updated = { ...current, ...info }
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updated))
      
      return true
    } catch (error) {
      Logger.error('CompanyConfig', 'Failed to update company info:', error)
      return false
    }
  }
}
