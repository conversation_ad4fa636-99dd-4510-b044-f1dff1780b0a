import React from 'react';
import { Tag, Typography, Progress } from 'antd';
import UniversalReport from './UniversalReport';
import type { ReportData, ReportType } from '../../types/reports';
import { DateUtils } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger'

const { Text } = Typography;

const PaintPerformanceReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getPaintPerformanceReport({
        year: filters.year,
        dateRange: filters.dateRange
      });

      if (!response || !response.success || !response.data || !Array.isArray(response.data.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const paintData = response.data;

      const data = paintData.data;

      // حساب الإحصائيات
      const totalPeriods = data.length;
      const totalOrders = data.reduce((sum, item) => sum + item.total_orders, 0);
      const totalCompleted = data.reduce((sum, item) => sum + item.completed_orders, 0);
      const _totalInProgress = data.reduce((sum, item) => sum + item.in_progress_orders, 0);
      const _totalPending = data.reduce((sum, item) => sum + item.pending_orders, 0);
      const totalArea = data.reduce((sum, item) => sum + item.total_area_painted, 0);
      const totalRevenue = data.reduce((sum, item) => sum + item.total_revenue, 0);
      const totalCustomers = data.reduce((sum, item) => sum + item.unique_customers, 0);

      // إعداد البيانات للجدول
      const tableData = data.map((item, index) => {
        const [year, month] = item.period.split('-');
        const monthNames = [
          'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
          'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        const monthName = monthNames[parseInt(month) - 1];
        
        return {
          key: item.period,
          index: index + 1,
          period: item.period,
          period_name: `${monthName} ${year}`,
          total_orders: item.total_orders,
          completed_orders: item.completed_orders,
          in_progress_orders: item.in_progress_orders,
          pending_orders: item.pending_orders,
          completion_rate: item.completion_rate,
          total_area_painted: item.total_area_painted,
          avg_area_per_order: item.avg_area_per_order,
          unique_customers: item.unique_customers,
          total_revenue: item.total_revenue,
          avg_revenue_per_order: item.avg_revenue_per_order,
          efficiency_score: item.completion_rate // استخدام معدل الإنجاز كمؤشر كفاءة
        };
      });

      // تعريف الأعمدة
      const columns = [
        {
          title: '#',

          key: 'index',
          width: 50,
          align: 'center' as const
        },
        {
          title: 'الفترة',

          key: 'period_name',
          width: 120,
          align: 'center' as const
        },
        {
          title: 'إجمالي الأوامر',
          sortable: true,
          key: 'total_orders',
          width: 100,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="blue">{record.total_orders}</Tag>
          )
        },
        {
          title: 'الأوامر المكتملة',
          sortable: true,
          key: 'completed_orders',
          width: 120,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="green">{record.completed_orders}</Tag>
          )
        },
        {
          title: 'قيد التنفيذ',

          sortable: true,
          key: 'in_progress_orders',
          width: 100,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="orange">{record.in_progress_orders}</Tag>
          )
        },
        {
          title: 'معلقة',

          sortable: true,
          key: 'pending_orders',
          width: 80,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="red">{record.pending_orders}</Tag>
          )
        },
        {
          title: 'معدل الإنجاز',
          sortable: true,
          key: 'completion_rate',
          width: 120,
          align: 'center' as const,
          render: (record: any) => (
            <Progress
              percent={Math.round(record.completion_rate)}
              size="small"
              status={record.completion_rate >= 80 ? 'success' : record.completion_rate >= 60 ? 'active' : 'exception'}
            />
          )
        },
        {
          title: 'إجمالي المساحة (م²)',

          sortable: true,
          key: 'total_area_painted',
          width: 130,
          align: 'right' as const,
          render: (record: any) => record.total_area_painted.toLocaleString('ar-EG', { minimumFractionDigits: 2 })
        },
        {
          title: 'متوسط المساحة/أمر',

          sortable: true,
          key: 'avg_area_per_order',
          width: 130,
          align: 'right' as const,
          render: (record: any) => (
            <Text style={{ color: '#722ed1' }}>
              {record.avg_area_per_order.toFixed(2)} م²
            </Text>
          )
        },
        {
          title: 'عدد العملاء',

          sortable: true,
          key: 'unique_customers',
          width: 100,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="purple">{record.unique_customers}</Tag>
          )
        },
        {
          title: 'إجمالي الإيرادات',

          sortable: true,
          key: 'total_revenue',
          width: 130,
          align: 'right' as const,
          render: (record: any) => (
            <Text strong style={{ color: '#1890ff' }}>
              {record.total_revenue.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'متوسط الإيرادات/أمر',

          sortable: true,
          key: 'avg_revenue_per_order',
          width: 140,
          align: 'right' as const,
          render: (record: any) => (
            <Text style={{ color: '#13c2c2' }}>
              {record.avg_revenue_per_order.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'مؤشر الكفاءة',

          sortable: true,
          key: 'efficiency_score',
          width: 120,
          align: 'center' as const,
          render: (record: any) => {
            const value = record.efficiency_score;
            const color = value >= 80 ? 'green' : value >= 60 ? 'orange' : 'red';
            const label = value >= 80 ? 'ممتاز' : value >= 60 ? 'جيد' : 'يحتاج تحسين';
            return (
              <Tag color={color}>
                {label} ({value.toFixed(1)}%)
              </Tag>
            );
          }
        }
      ];

      // إعداد الإحصائيات
      const avgCompletionRate = totalPeriods > 0 ? (totalCompleted / totalOrders * 100) : 0;
      const avgAreaPerOrder = totalOrders > 0 ? (totalArea / totalOrders) : 0;
      const avgRevenuePerOrder = totalOrders > 0 ? (totalRevenue / totalOrders) : 0;
      const _avgCustomersPerPeriod = totalPeriods > 0 ? (totalCustomers / totalPeriods) : 0;

      const statistics = [
        {
          title: 'إجمالي الفترات',
          value: totalPeriods,
          color: '#1890ff',
          icon: '📅'
        },
        {
          title: 'إجمالي الأوامر',
          value: totalOrders,
          color: '#52c41a',
          icon: '📋'
        },
        {
          title: 'الأوامر المكتملة',
          value: totalCompleted,
          color: '#52c41a',
          icon: '✅'
        },
        {
          title: 'متوسط معدل الإنجاز',
          value: `${avgCompletionRate.toFixed(1)}%`,
          color: '#722ed1',
          icon: '📊'
        },
        {
          title: 'إجمالي المساحة (م²)',
          value: totalArea.toLocaleString('ar-EG', { minimumFractionDigits: 2 }),
          color: '#fa8c16',
          icon: '📐'
        },
        {
          title: 'متوسط المساحة/أمر',
          value: `${avgAreaPerOrder.toFixed(2)} م²`,
          color: '#eb2f96',
          icon: '📏'
        },
        {
          title: 'إجمالي الإيرادات',
          value: `${totalRevenue.toLocaleString('ar-EG')} ج.م`,
          color: '#13c2c2',
          icon: '💰'
        },
        {
          title: 'متوسط الإيرادات/أمر',
          value: `${avgRevenuePerOrder.toLocaleString('ar-EG')} ج.م`,
          color: '#f759ab',
          icon: '💵'
        }
      ];

      return {
        title: 'تقرير أداء الدهان',
        columns,
        data: tableData,
        statistics,
        summary: {
          totalRecords: totalPeriods,
          totalOrders: totalOrders,
          totalCompleted: totalCompleted,
          avgCompletionRate: avgCompletionRate,
          totalRevenue: totalRevenue
        }
      };

    } catch (error) {
      Logger.error('PaintPerformanceReport', 'خطأ في إنشاء تحليل أداء الدهان:', error);
      throw new Error('فشل في تحميل بيانات التقرير');
    }
  };

  return (
    <UniversalReport
      reportType={'paint_performance' as ReportType}
      title="تحليل أداء الدهان"
      description="تقرير تحليلي لأداء الدهان مع مؤشرات الكفاءة"
      onGenerateReport={generateReport}
      showDateRange={true}
    />
  );
};

export default PaintPerformanceReport;
