/**
 * ═══════════════════════════════════════════════════════════════════════════
 * 🖼️ SimpleImageService - خدمة موحدة بسيطة لإدارة جميع أنواع الصور
 * ═══════════════════════════════════════════════════════════════════════════
 * 
 * الهدف: استبدال جميع خدمات الصور المعقدة بخدمة واحدة بسيطة وواضحة
 * 
 * الميزات:
 * ✅ رفع صورة واحدة أو متعددة
 * ✅ جلب الصور حسب الفئة والسياق
 * ✅ حذف صورة
 * ✅ تعيين صورة رئيسية
 * ✅ تحديث بيانات الصورة
 * ✅ دعم metadata مرن (للشيكات وغيرها)
 * 
 * الاستخدام:
 * - الأصناف: category='item', contextId=item.id
 * - أوامر الإنتاج: category='production', contextId=order.id
 * - الشيكات: category='check', contextId=check.id, metadata={side:'front'}
 * - العملاء: category='customer', contextId=customer.id
 * 
 * @version 1.0.0
 * @created 2025-09-30
 * ═══════════════════════════════════════════════════════════════════════════
 */

import { SafeLogger as Logger } from '../utils/logger'

// ═══════════════════════════════════════════════════════════════════════════
// 📋 الواجهات والأنواع
// ═══════════════════════════════════════════════════════════════════════════

/**
 * فئات الصور المدعومة
 */
export type ImageCategory = 'item' | 'production' | 'check' | 'customer' | 'supplier' | 'general'

/**
 * بيانات الصورة الموحدة
 */
export interface UnifiedImage {
  id: string
  name: string
  path: string
  size: number
  type: string
  category: ImageCategory
  context_id: number
  description?: string
  is_primary: boolean
  sort_order: number
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
}

/**
 * خيارات رفع الصورة
 */
export interface UploadImageOptions {
  description?: string
  isPrimary?: boolean
  metadata?: Record<string, any>
  maxSize?: number // بالبايت (افتراضي: 5MB)
  allowedTypes?: string[] // افتراضي: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
}

/**
 * نتيجة العملية
 */
export interface ImageOperationResult {
  success: boolean
  data?: any
  error?: string
  warnings?: string[]
}

// ═══════════════════════════════════════════════════════════════════════════
// 🔧 الخدمة الرئيسية
// ═══════════════════════════════════════════════════════════════════════════

export class SimpleImageService {
  private static instance: SimpleImageService

  // الإعدادات الافتراضية
  private readonly DEFAULT_MAX_SIZE = 5 * 1024 * 1024 // 5MB
  private readonly DEFAULT_ALLOWED_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp'
  ]

  private constructor() {
    Logger.info('SimpleImageService', '🎨 تم تهيئة خدمة الصور الموحدة')
  }

  /**
   * الحصول على مثيل الخدمة (Singleton)
   */
  public static getInstance(): SimpleImageService {
    if (!SimpleImageService.instance) {
      SimpleImageService.instance = new SimpleImageService()
    }
    return SimpleImageService.instance
  }

  // ═══════════════════════════════════════════════════════════════════════════
  // 📤 رفع الصور
  // ═══════════════════════════════════════════════════════════════════════════

  /**
   * رفع صورة واحدة
   */
  public async uploadImage(
    file: File,
    category: ImageCategory,
    contextId: number,
    options: UploadImageOptions = {}
  ): Promise<ImageOperationResult> {
    try {
      Logger.info('SimpleImageService', `📤 رفع صورة: ${file.name} [${category}:${contextId}]`)

      // 1️⃣ التحقق من صحة الملف
      const validation = this.validateFile(file, options)
      if (!validation.success) {
        return validation
      }

      // 2️⃣ رفع الصورة عبر IPC
      const result = await window.electronAPI?.invoke('upload-unified-image', {
        file: {
          name: file.name,
          size: file.size,
          type: file.type,
          data: await this.fileToBase64(file)
        },
        category,
        contextId,
        description: options.description || '',
        isPrimary: options.isPrimary || false,
        metadata: options.metadata || {}
      })

      if (!result?.success) {
        throw new Error(result?.error || 'فشل رفع الصورة')
      }

      Logger.info('SimpleImageService', `✅ تم رفع الصورة بنجاح: ${file.name}`)
      return {
        success: true,
        data: result.data,
        warnings: validation.warnings
      }
    } catch (error: any) {
      Logger.error('SimpleImageService', '❌ خطأ في رفع الصورة:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء رفع الصورة'
      }
    }
  }

  /**
   * رفع صور متعددة
   */
  public async uploadMultipleImages(
    files: File[],
    category: ImageCategory,
    contextId: number,
    options: UploadImageOptions = {}
  ): Promise<ImageOperationResult> {
    try {
      Logger.info('SimpleImageService', `📤 رفع ${files.length} صورة [${category}:${contextId}]`)

      const results: UnifiedImage[] = []
      const errors: string[] = []
      const warnings: string[] = []

      for (const file of files) {
        const result = await this.uploadImage(file, category, contextId, options)
        
        if (result.success && result.data) {
          results.push(result.data)
        } else {
          errors.push(`${file.name}: ${result.error}`)
        }

        if (result.warnings) {
          warnings.push(...result.warnings)
        }
      }

      if (errors.length > 0) {
        Logger.warn('SimpleImageService', `⚠️ بعض الصور فشلت: ${errors.join(', ')}`)
      }

      return {
        success: results.length > 0,
        data: results,
        error: errors.length > 0 ? errors.join('\n') : undefined,
        warnings: warnings.length > 0 ? warnings : undefined
      }
    } catch (error: any) {
      Logger.error('SimpleImageService', '❌ خطأ في رفع الصور المتعددة:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء رفع الصور'
      }
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════
  // 📥 جلب الصور
  // ═══════════════════════════════════════════════════════════════════════════

  /**
   * جلب الصور حسب الفئة والسياق
   */
  public async getImages(
    category: ImageCategory,
    contextId: number
  ): Promise<ImageOperationResult> {
    try {
      Logger.debug('SimpleImageService', `🔍 جلب صور [${category}:${contextId}]`)

      const result = await window.electronAPI?.invoke('get-unified-images', {
        category,
        contextId
      })

      if (!result?.success) {
        throw new Error(result?.error || 'فشل جلب الصور')
      }

      Logger.debug('SimpleImageService', `✅ تم جلب ${result.data?.length || 0} صورة`)
      return {
        success: true,
        data: result.data || []
      }
    } catch (error: any) {
      Logger.error('SimpleImageService', '❌ خطأ في جلب الصور:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء جلب الصور',
        data: []
      }
    }
  }

  /**
   * جلب صورة واحدة بالمعرف
   */
  public async getImageById(imageId: string): Promise<ImageOperationResult> {
    try {
      const result = await window.electronAPI?.invoke('get-unified-image-by-id', { imageId })

      if (!result?.success) {
        throw new Error(result?.error || 'فشل جلب الصورة')
      }

      return {
        success: true,
        data: result.data
      }
    } catch (error: any) {
      Logger.error('SimpleImageService', '❌ خطأ في جلب الصورة:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء جلب الصورة'
      }
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════
  // ✏️ تحديث الصور
  // ═══════════════════════════════════════════════════════════════════════════

  /**
   * تحديث بيانات الصورة
   */
  public async updateImage(
    imageId: string,
    updates: {
      description?: string
      metadata?: Record<string, any>
      sort_order?: number
    }
  ): Promise<ImageOperationResult> {
    try {
      Logger.info('SimpleImageService', `📝 تحديث صورة: ${imageId}`)

      const result = await window.electronAPI?.invoke('update-unified-image', {
        imageId,
        updates
      })

      if (!result?.success) {
        throw new Error(result?.error || 'فشل تحديث الصورة')
      }

      Logger.info('SimpleImageService', `✅ تم تحديث الصورة بنجاح`)
      return { success: true, data: result.data }
    } catch (error: any) {
      Logger.error('SimpleImageService', '❌ خطأ في تحديث الصورة:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء تحديث الصورة'
      }
    }
  }

  /**
   * تعيين صورة رئيسية
   */
  public async setPrimaryImage(
    imageId: string,
    category: ImageCategory,
    contextId: number
  ): Promise<ImageOperationResult> {
    try {
      Logger.info('SimpleImageService', `⭐ تعيين صورة رئيسية: ${imageId}`)

      const result = await window.electronAPI?.invoke('set-primary-unified-image', {
        imageId,
        category,
        contextId
      })

      if (!result?.success) {
        throw new Error(result?.error || 'فشل تعيين الصورة الرئيسية')
      }

      Logger.info('SimpleImageService', `✅ تم تعيين الصورة الرئيسية بنجاح`)
      return { success: true }
    } catch (error: any) {
      Logger.error('SimpleImageService', '❌ خطأ في تعيين الصورة الرئيسية:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء تعيين الصورة الرئيسية'
      }
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════
  // 🗑️ حذف الصور
  // ═══════════════════════════════════════════════════════════════════════════

  /**
   * حذف صورة
   */
  public async deleteImage(imageId: string): Promise<ImageOperationResult> {
    try {
      Logger.info('SimpleImageService', `🗑️ حذف صورة: ${imageId}`)

      const result = await window.electronAPI?.invoke('delete-unified-image', { imageId })

      if (!result?.success) {
        throw new Error(result?.error || 'فشل حذف الصورة')
      }

      Logger.info('SimpleImageService', `✅ تم حذف الصورة بنجاح`)
      return { success: true }
    } catch (error: any) {
      Logger.error('SimpleImageService', '❌ خطأ في حذف الصورة:', error)
      return {
        success: false,
        error: error.message || 'حدث خطأ أثناء حذف الصورة'
      }
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════
  // 🔧 دوال مساعدة
  // ═══════════════════════════════════════════════════════════════════════════

  /**
   * التحقق من صحة الملف
   */
  private validateFile(file: File, options: UploadImageOptions): ImageOperationResult {
    const warnings: string[] = []
    const maxSize = options.maxSize || this.DEFAULT_MAX_SIZE
    const allowedTypes = options.allowedTypes || this.DEFAULT_ALLOWED_TYPES

    // التحقق من وجود الملف
    if (!file) {
      return { success: false, error: 'لم يتم تحديد ملف' }
    }

    // التحقق من نوع الملف
    if (!allowedTypes.includes(file.type)) {
      return {
        success: false,
        error: `نوع الملف غير مدعوم. الأنواع المدعومة: ${allowedTypes.join(', ')}`
      }
    }

    // التحقق من حجم الملف
    if (file.size > maxSize) {
      return {
        success: false,
        error: `حجم الملف كبير جداً. الحد الأقصى: ${this.formatFileSize(maxSize)}`
      }
    }

    // تحذير للملفات الكبيرة
    if (file.size > maxSize * 0.8) {
      warnings.push(`حجم الملف كبير (${this.formatFileSize(file.size)})`)
    }

    return { success: true, warnings: warnings.length > 0 ? warnings : undefined }
  }

  /**
   * تحويل ملف إلى Base64
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  /**
   * تنسيق حجم الملف
   */
  private formatFileSize(bytes: number): string {
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`
  }
}

// ═══════════════════════════════════════════════════════════════════════════
// 📤 تصدير مثيل الخدمة
// ═══════════════════════════════════════════════════════════════════════════

export const simpleImageService = SimpleImageService.getInstance()

