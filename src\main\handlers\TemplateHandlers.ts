import { ipcMain } from 'electron'
import { TemplateStorageService, StoredTemplate, TemplateFilter } from '../services/TemplateStorageService'
import { Logger } from '../utils/logger'

export class TemplateHandlers {
  private static templateService: TemplateStorageService

  public static registerHandlers(): void {
    this.templateService = TemplateStorageService.getInstance()

    // حفظ قالب
    ipcMain.handle('save-template', async (event, template: Omit<StoredTemplate, 'created_at' | 'updated_at'>) => {
      try {
        Logger.info('TemplateHandlers', `طلب حفظ القالب: ${template.name}`)
        const result = await this.templateService.saveTemplate(template)
        
        if (result) {
          Logger.success('TemplateHandlers', `تم حفظ القالب بنجاح: ${template.name}`)
          return { success: true, message: 'تم حفظ القالب بنجاح' }
        } else {
          Logger.error('TemplateHandlers', `فشل في حفظ القالب: ${template.name}`)
          return { success: false, message: 'فشل في حفظ القالب' }
        }
      } catch (error) {
        Logger.error('TemplateHandlers', 'خطأ في معالج حفظ القالب:', error)
        return { success: false, message: 'خطأ في حفظ القالب', error: error instanceof Error ? error.message : 'خطأ غير معروف' }
      }
    })

    // جلب قالب بواسطة المعرف
    ipcMain.handle('get-template', async (event, id: string) => {
      try {
        Logger.info('TemplateHandlers', `طلب جلب القالب: ${id}`)
        const template = await this.templateService.getTemplate(id)
        
        if (template) {
          Logger.success('TemplateHandlers', `تم جلب القالب بنجاح: ${template.name}`)
          return { success: true, data: template }
        } else {
          Logger.warn('TemplateHandlers', `لم يتم العثور على القالب: ${id}`)
          return { success: false, message: 'لم يتم العثور على القالب' }
        }
      } catch (error) {
        Logger.error('TemplateHandlers', 'خطأ في معالج جلب القالب:', error)
        return { success: false, message: 'خطأ في جلب القالب', error: error instanceof Error ? error.message : 'خطأ غير معروف' }
      }
    })

    // جلب جميع القوالب مع التصفية
    ipcMain.handle('get-templates', async (event, filter?: TemplateFilter) => {
      try {
        Logger.info('TemplateHandlers', 'طلب جلب القوالب مع التصفية:', filter)
        const templates = await this.templateService.getTemplates(filter)
        
        Logger.success('TemplateHandlers', `تم جلب ${templates.length} قالب`)
        return { success: true, data: templates }
      } catch (error) {
        Logger.error('TemplateHandlers', 'خطأ في معالج جلب القوالب:', error)
        return { success: false, message: 'خطأ في جلب القوالب', error: error instanceof Error ? error.message : 'خطأ غير معروف' }
      }
    })

    // حذف قالب
    ipcMain.handle('delete-template', async (event, id: string) => {
      try {
        Logger.info('TemplateHandlers', `طلب حذف القالب: ${id}`)
        const result = await this.templateService.deleteTemplate(id)
        
        if (result) {
          Logger.success('TemplateHandlers', `تم حذف القالب بنجاح: ${id}`)
          return { success: true, message: 'تم حذف القالب بنجاح' }
        } else {
          Logger.error('TemplateHandlers', `فشل في حذف القالب: ${id}`)
          return { success: false, message: 'فشل في حذف القالب أو أنه قالب افتراضي' }
        }
      } catch (error) {
        Logger.error('TemplateHandlers', 'خطأ في معالج حذف القالب:', error)
        return { success: false, message: 'خطأ في حذف القالب', error: error instanceof Error ? error.message : 'خطأ غير معروف' }
      }
    })

    // تفعيل/تعطيل قالب
    ipcMain.handle('toggle-template', async (event, id: string, isActive: boolean) => {
      try {
        Logger.info('TemplateHandlers', `طلب ${isActive ? 'تفعيل' : 'تعطيل'} القالب: ${id}`)
        const result = await this.templateService.toggleTemplate(id, isActive)
        
        if (result) {
          Logger.success('TemplateHandlers', `تم ${isActive ? 'تفعيل' : 'تعطيل'} القالب بنجاح: ${id}`)
          return { success: true, message: `تم ${isActive ? 'تفعيل' : 'تعطيل'} القالب بنجاح` }
        } else {
          Logger.error('TemplateHandlers', `فشل في ${isActive ? 'تفعيل' : 'تعطيل'} القالب: ${id}`)
          return { success: false, message: `فشل في ${isActive ? 'تفعيل' : 'تعطيل'} القالب` }
        }
      } catch (error) {
        Logger.error('TemplateHandlers', 'خطأ في معالج تغيير حالة القالب:', error)
        return { success: false, message: 'خطأ في تغيير حالة القالب', error: error instanceof Error ? error.message : 'خطأ غير معروف' }
      }
    })

    // تكرار قالب
    ipcMain.handle('duplicate-template', async (event, id: string, newName: string) => {
      try {
        Logger.info('TemplateHandlers', `طلب تكرار القالب: ${id} باسم: ${newName}`)
        const newId = await this.templateService.duplicateTemplate(id, newName)
        
        if (newId) {
          Logger.success('TemplateHandlers', `تم تكرار القالب بنجاح: ${newId}`)
          return { success: true, data: { id: newId }, message: 'تم تكرار القالب بنجاح' }
        } else {
          Logger.error('TemplateHandlers', `فشل في تكرار القالب: ${id}`)
          return { success: false, message: 'فشل في تكرار القالب' }
        }
      } catch (error) {
        Logger.error('TemplateHandlers', 'خطأ في معالج تكرار القالب:', error)
        return { success: false, message: 'خطأ في تكرار القالب', error: error instanceof Error ? error.message : 'خطأ غير معروف' }
      }
    })

    // تصدير قالب
    ipcMain.handle('export-template', async (event, id: string) => {
      try {
        Logger.info('TemplateHandlers', `طلب تصدير القالب: ${id}`)
        const jsonData = await this.templateService.exportTemplate(id)
        
        if (jsonData) {
          Logger.success('TemplateHandlers', `تم تصدير القالب بنجاح: ${id}`)
          return { success: true, data: jsonData, message: 'تم تصدير القالب بنجاح' }
        } else {
          Logger.error('TemplateHandlers', `فشل في تصدير القالب: ${id}`)
          return { success: false, message: 'فشل في تصدير القالب' }
        }
      } catch (error) {
        Logger.error('TemplateHandlers', 'خطأ في معالج تصدير القالب:', error)
        return { success: false, message: 'خطأ في تصدير القالب', error: error instanceof Error ? error.message : 'خطأ غير معروف' }
      }
    })

    // استيراد قالب
    ipcMain.handle('import-template', async (event, jsonData: string) => {
      try {
        Logger.info('TemplateHandlers', 'طلب استيراد قالب من JSON')
        const newId = await this.templateService.importTemplate(jsonData)
        
        if (newId) {
          Logger.success('TemplateHandlers', `تم استيراد القالب بنجاح: ${newId}`)
          return { success: true, data: { id: newId }, message: 'تم استيراد القالب بنجاح' }
        } else {
          Logger.error('TemplateHandlers', 'فشل في استيراد القالب')
          return { success: false, message: 'فشل في استيراد القالب' }
        }
      } catch (error) {
        Logger.error('TemplateHandlers', 'خطأ في معالج استيراد القالب:', error)
        return { success: false, message: 'خطأ في استيراد القالب', error: error instanceof Error ? error.message : 'خطأ غير معروف' }
      }
    })

    // جلب إحصائيات القوالب
    ipcMain.handle('get-template-stats', async (_event) => {
      try {
        Logger.info('TemplateHandlers', 'طلب جلب إحصائيات القوالب')
        const stats = await this.templateService.getTemplateStats()
        
        Logger.success('TemplateHandlers', 'تم جلب إحصائيات القوالب بنجاح')
        return { success: true, data: stats }
      } catch (error) {
        Logger.error('TemplateHandlers', 'خطأ في معالج إحصائيات القوالب:', error)
        return { success: false, message: 'خطأ في جلب إحصائيات القوالب', error: error instanceof Error ? error.message : 'خطأ غير معروف' }
      }
    })

    Logger.success('TemplateHandlers', '✅ تم تسجيل جميع معالجات القوالب بنجاح')
  }
}
