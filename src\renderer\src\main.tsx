// تحميل Early polyfill أولاً
import './utils/earlyPolyfill'

// تحميل React
import React from 'react'
import ReactDOM from 'react-dom/client'

// تحميل Antd
import { ConfigProvider, message, notification } from 'antd'
import arEG from 'antd/locale/ar_EG'
import 'antd/dist/reset.css'

// تحميل المكونات الأساسية
import MainApp from './App'
import { AppContextProvider } from './contexts/AppContextProvider'

// تحميل الأدوات المساعدة الأساسية أولاً
import './utils/dateConfig' // تحميل تكوين التاريخ الميلادي
import calendarConfig from './utils/calendarConfig' // تحميل تكوين التقويم الميلادي
import { SimpleLogger as Logger } from './utils/logger'
import { analyzeError, displayError } from './utils/errorHandler'
import { startProcessMonitor } from './utils/processPolyfill'

// تم حذف نظام الطباعة
// import './services/PrintSystemInitializer' // تم حذف نظام الطباعة

// تحميل Polyfills الإضافية بشكل آمن
const loadPolyfills = async () => {
  try {
    // تحميل polyfills بترتيب آمن لتجنب temporal dead zone
    await import('./utils/bufferPolyfill')
    // تم دمج logger polyfills في النظام الموحد

    Logger.info('Main', '✅ تم تحميل جميع polyfills بنجاح')
  } catch (error) {
    console.warn('فشل في تحميل بعض polyfills:', error)
  }
}

// بدء مراقب process قبل أي شيء آخر
startProcessMonitor()

// فحص الإصدار وإدارة الكاش بذكاء
const initializeApp = () => {
  try {
    const currentVersion = '1.7.0'
    const storedVersion = localStorage.getItem('app_version')

    Logger.info('Main', '🔄 فحص إصدار التطبيق...')
    Logger.info('Main', 'الإصدار الحالي:', currentVersion)
    Logger.info('Main', 'الإصدار المحفوظ:', storedVersion)

    // مسح الكاش فقط عند تغيير الإصدار
    if (storedVersion !== currentVersion) {
      Logger.info('Main', '🧹 إصدار جديد - مسح الكاش...')

      // مسح الكاش القديم فقط
      try {
        localStorage.removeItem('app_cache')
        sessionStorage.removeItem('app_cache')
      } catch (e) {
        Logger.warn('Main', 'تعذر مسح بعض الكاش:', e)
      }

      // حفظ الإصدار الجديد
      localStorage.setItem('app_version', currentVersion)
      localStorage.setItem('last_update', new Date().toISOString())

      Logger.info('Main', '✅ تم تحديث الإصدار بنجاح')
    } else {
      Logger.info('Main', '✅ نفس الإصدار - لا حاجة لمسح الكاش')
    }

    // إضافة معرف الإصدار للـ DOM
    document.documentElement.setAttribute('data-version', currentVersion)

  } catch (error) {
    Logger.error('Main', '❌ خطأ في تهيئة التطبيق:', error)
  }
}

// تشغيل تهيئة التطبيق
initializeApp()

// إضافة معالج أخطاء شامل محسن
window.addEventListener('error', (event) => {
  Logger.error('Main', '❌ خطأ JavaScript:', event.error)
  Logger.error('Main', `المصدر: ${event.filename} السطر: ${event.lineno}`)

  // معالجة خاصة لأخطاء temporal dead zone
  if (event.error && event.error.message &&
      (event.error.message.includes('Cannot access') && event.error.message.includes('before initialization'))) {
    Logger.warn('Main', '🔧 محاولة إصلاح مشكلة temporal dead zone...')

    // إعادة تحميل الصفحة بعد تأخير قصير
    setTimeout(() => {
      Logger.info('Main', '🔄 إعادة تحميل التطبيق لحل مشكلة التهيئة...')
      window.location.reload()
    }, 1000)

    event.preventDefault()
    return
  }

  // استخدام معالج الأخطاء المحسن
  const customError = analyzeError(event.error)
  displayError(customError, 'JavaScript Error')

  // منع إيقاف التطبيق
  event.preventDefault()
})

window.addEventListener('unhandledrejection', (event) => {
  Logger.error('Main', '❌ Promise مرفوض:', event.reason)

  // استخدام معالج الأخطاء المحسن
  const customError = analyzeError(event.reason)
  displayError(customError, 'Unhandled Promise Rejection')

  event.preventDefault() // منع إظهار الخطأ في الكونسول
})

// تطبيق تكوين التقويم الميلادي
try {
  calendarConfig.applyGregorianCalendarConfig()
  Logger.info('Main', '✅ تم تطبيق تكوين التقويم بنجاح')
} catch (error) {
  Logger.error('Main', '❌ خطأ في تكوين التقويم:', error)
}

// إعداد الرسائل العامة مع دعم RTL محسن
try {
  message.config({
    top: 100,
    duration: 3,
    maxCount: 3,
    rtl: true,
    prefixCls: 'ant-message-rtl',
  })

  notification.config({
    placement: 'topRight',
    top: 100,
    duration: 4.5,
    rtl: true,
    prefixCls: 'ant-notification-rtl',
  })

  Logger.info('Main', '✅ تم تكوين الرسائل والإشعارات بنجاح')
} catch (error) {
  Logger.error('Main', '❌ خطأ في تكوين الرسائل:', error)
}

// إعداد CSS مخصص للتطبيق
const customStyles = `
  /* متغيرات CSS للثيمات */
  :root {
    --primary-color: #1890ff;
    --sidebar-bg-color: #001529;
    --menu-selected-color: #1890ff;
    --menu-hover-color: #112545;
    --header-bg-color: #ffffff;
    --content-bg-color: #f0f2f5;
    --border-radius: 6px;
    --font-size: 14px;
    --font-family: 'Segoe UI, Tahoma, Arial, sans-serif';
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #ff4d4f;
    --info-color: #1890ff;
    --border-color: #f0f0f0;

    /* متغيرات خاصة بالطباعة */
    --print-primary-color: #1890ff;
    --print-secondary-color: #fff3cd;
    --print-border-color: #d9d9d9;
    --print-background-color: #ffffff;
    --print-text-color: #000000;

    /* متغيرات خاصة بالشعار */
    --print-logo-width: 90px;
    --print-logo-height: 60px;
    --print-logo-size: medium;
    --print-logo-position: top-left;
    --print-show-logo: block;
  }

  /* الوضع الداكن */
  [data-theme="dark"] {
    --sidebar-bg-color: #000c17;
    --header-bg-color: #141414;
    --content-bg-color: #000000;
    --border-color: #303030;
  }

  /* تحسينات RTL */
  .ant-layout-sider {
    box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
    background: var(--sidebar-bg-color) !important;
  }

  .ant-menu-dark {
    background: var(--sidebar-bg-color) !important;
  }

  .ant-menu-dark .ant-menu-item-selected {
    background-color: var(--menu-selected-color, var(--primary-color)) !important;
  }

  .ant-menu-dark .ant-menu-submenu-title:hover,
  .ant-menu-dark .ant-menu-item:hover {
    background-color: var(--menu-hover-color) !important;
  }

  /* تحسينات القوائم المنسدلة الجانبية */
  .ant-menu-vertical .ant-menu-submenu-popup {
    background: var(--sidebar-bg-color) !important;
    border-radius: var(--border-radius) !important;
    box-shadow: 4px 4px 20px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid var(--border-color) !important;
    margin-right: 8px !important;
  }

  .ant-menu-vertical .ant-menu-submenu-popup .ant-menu-item-selected {
    background-color: var(--menu-selected-color, var(--primary-color)) !important;
  }

  .ant-menu-vertical .ant-menu-submenu-popup .ant-menu-item:hover {
    background-color: var(--menu-hover-color) !important;
  }

  .ant-menu-vertical .ant-menu-submenu-popup .ant-menu {
    background: #1f2937 !important;
    border-radius: 8px !important;
  }

  .ant-menu-vertical .ant-menu-submenu-popup .ant-menu-item {
    background: #1f2937 !important;
    color: #e5e7eb !important;
    margin: 2px 4px !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
  }

  .ant-menu-vertical .ant-menu-submenu-popup .ant-menu-item:hover {
    background: #374151 !important;
    color: #ffffff !important;
    transform: translateX(-2px) !important;
  }

  .ant-menu-vertical .ant-menu-submenu-popup .ant-menu-item-selected {
    background: #3b82f6 !important;
    color: #ffffff !important;
  }

  .ant-menu-vertical .ant-menu-submenu-popup .ant-menu-item-selected:hover {
    background: #2563eb !important;
  }

  /* تحسين القوائم الفرعية المتداخلة */
  .ant-menu-vertical .ant-menu-submenu-popup .ant-menu-submenu-title {
    background: #1f2937 !important;
    color: #e5e7eb !important;
    margin: 2px 4px !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
  }

  .ant-menu-vertical .ant-menu-submenu-popup .ant-menu-submenu-title:hover {
    background: #374151 !important;
    color: #ffffff !important;
  }

  /* تحسين الأسهم في القوائم المنسدلة */
  .ant-menu-vertical .ant-menu-submenu-arrow {
    color: #9ca3af !important;
    transition: all 0.2s ease !important;
  }

  .ant-menu-vertical .ant-menu-submenu:hover .ant-menu-submenu-arrow {
    color: #ffffff !important;
  }

  /* تحسينات إضافية للقائمة الجانبية */
  .ant-menu-vertical .ant-menu-submenu-title {
    position: relative !important;
    overflow: visible !important;
  }

  /* ضمان ظهور القوائم المنسدلة بشكل صحيح */
  .ant-menu-vertical .ant-menu-submenu-popup {
    min-width: 200px !important;
    max-height: 400px !important;
    overflow-y: auto !important;
  }

  /* تحسين التمرير في القوائم المنسدلة الطويلة */
  .ant-menu-vertical .ant-menu-submenu-popup::-webkit-scrollbar {
    width: 6px;
  }

  .ant-menu-vertical .ant-menu-submenu-popup::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 3px;
  }

  .ant-menu-vertical .ant-menu-submenu-popup::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 3px;
  }

  .ant-menu-vertical .ant-menu-submenu-popup::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  /* تحسينات البطاقات */
  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
  }

  .ant-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  /* تحسينات الأزرار */
  .ant-btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .ant-btn-primary {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    border: none;
  }

  .ant-btn-primary:hover {
    background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  }

  /* تحسينات الجداول */
  .ant-table {
    border-radius: 8px;
    overflow: hidden;
  }

  .ant-table-thead > tr > th {
    background: #fafafa;
    font-weight: 600;
  }

  /* تحسينات النماذج */
  .ant-form-item-label > label {
    font-weight: 500;
  }

  .ant-input, .ant-select-selector {
    border-radius: 6px;
    transition: all 0.3s ease;
  }

  .ant-input:focus, .ant-select-focused .ant-select-selector {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  /* تحسينات الإحصائيات */
  .ant-statistic-title {
    font-weight: 500;
    color: #666;
  }

  .ant-statistic-content {
    font-weight: 600;
  }

  /* تحسينات القوائم المنسدلة */
  .ant-menu-submenu-arrow {
    transition: transform 0.3s ease;
  }

  .ant-menu-submenu-open > .ant-menu-submenu-title .ant-menu-submenu-arrow {
    transform: rotate(180deg);
  }













  /* تحسينات الرسائل */
  .ant-message-rtl {
    direction: rtl;
  }

  .ant-notification-rtl {
    direction: rtl;
  }

  /* تحسينات عامة */
  body {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .ant-layout {
    min-height: 100vh;
  }

  /* تحسينات التمرير */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
`

// إضافة الأنماط المخصصة
const styleElement = document.createElement('style')
styleElement.textContent = customStyles
document.head.appendChild(styleElement)

// دالة تحميل التطبيق الرئيسية
const loadApp = async () => {
  try {
    // تحميل polyfills أولاً
    await loadPolyfills()

    // انتظار قصير للتأكد من تهيئة جميع المتغيرات
    await new Promise(resolve => setTimeout(resolve, 100))

    const rootElement = document.getElementById('root')
    if (!rootElement) {
      throw new Error('لم يتم العثور على عنصر root في HTML')
    }

    Logger.info('Main', '🚀 بدء تحميل تطبيق React...')

    ReactDOM.createRoot(rootElement).render(
    <React.StrictMode>
      <AppContextProvider>
        <ConfigProvider
          locale={arEG}
          direction="rtl"
          theme={{
          token: {
            colorPrimary: '#1890ff',
            colorSuccess: '#52c41a',
            colorWarning: '#faad14',
            colorError: '#ff4d4f',
            colorInfo: '#1890ff',
            borderRadius: 6,
            fontSize: 14,
            fontFamily: 'Segoe UI, Tahoma, Arial, sans-serif',
            colorBgContainer: '#ffffff',
            colorBgElevated: '#ffffff',
            colorBgLayout: '#f0f2f5',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
            boxShadowSecondary: '0 4px 16px rgba(0, 0, 0, 0.12)',
            lineHeight: 1.5715,
            controlHeight: 32,
            controlHeightLG: 40,
            controlHeightSM: 24,
          },
          components: {
            Layout: {
              bodyBg: '#f0f2f5',
              headerBg: '#ffffff',
              headerHeight: 32,
              headerPadding: '0 12px',
              siderBg: '#001529',
              triggerBg: '#002140',
              triggerColor: '#ffffff',
              zeroTriggerWidth: 48,
              zeroTriggerHeight: 42,
            },
            Menu: {
              darkItemBg: '#001529',
              darkSubMenuItemBg: '#000c17',
              darkItemSelectedBg: '#1890ff',
              darkItemHoverBg: '#112545',
              darkItemColor: 'rgba(255, 255, 255, 0.85)',
              darkItemSelectedColor: '#ffffff',
              darkItemDisabledColor: 'rgba(255, 255, 255, 0.35)',
              itemMarginBlock: 4,
              itemMarginInline: 4,
              itemPaddingInline: 12,
              subMenuItemBorderRadius: 6,
              itemBorderRadius: 6,
            },
            Button: {
              controlHeight: 32,
              controlHeightLG: 40,
              controlHeightSM: 24,
              borderRadius: 6,
              borderRadiusLG: 8,
              borderRadiusSM: 4,
              fontWeight: 500,
              primaryShadow: '0 2px 0 rgba(5, 145, 255, 0.1)',
              dangerShadow: '0 2px 0 rgba(255, 38, 5, 0.06)',
            },
            Input: {
              controlHeight: 32,
              controlHeightLG: 40,
              controlHeightSM: 24,
              borderRadius: 6,
              borderRadiusLG: 8,
              borderRadiusSM: 4,
              paddingInline: 12,
              paddingInlineLG: 16,
              paddingInlineSM: 8,
            },
            Select: {
              controlHeight: 32,
              controlHeightLG: 40,
              controlHeightSM: 24,
              borderRadius: 6,
              borderRadiusLG: 8,
              borderRadiusSM: 4,
            },
            Table: {
              cellPaddingBlock: 12,
              cellPaddingInline: 16,
              cellPaddingBlockMD: 8,
              cellPaddingInlineMD: 12,
              cellPaddingBlockSM: 6,
              cellPaddingInlineSM: 8,
              borderRadius: 8,
              headerBg: '#fafafa',
              headerColor: 'rgba(0, 0, 0, 0.88)',
              headerSortActiveBg: '#f0f0f0',
              headerSortHoverBg: '#f5f5f5',
              bodySortBg: '#fafafa',
              rowHoverBg: '#f5f5f5',
              rowSelectedBg: '#e6f7ff',
              rowSelectedHoverBg: '#bae7ff',
            },
            Card: {
              borderRadius: 8,
              borderRadiusLG: 12,
              borderRadiusSM: 6,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
              headerBg: 'transparent',
              headerFontSize: 16,
              headerFontSizeSM: 14,
              headerHeight: 56,
              headerHeightSM: 48,
              actionsBg: '#fafafa',
            },
            Modal: {
              borderRadius: 8,
              borderRadiusLG: 12,
              borderRadiusSM: 6,
              headerBg: '#ffffff',
              contentBg: '#ffffff',
              footerBg: '#ffffff',
              titleFontSize: 16,
              titleLineHeight: 1.5,
            },
            DatePicker: {
              cellHeight: 32,
              cellWidth: 32,
              borderRadius: 6,
              borderRadiusLG: 8,
              borderRadiusSM: 4,
              controlHeight: 32,
              controlHeightLG: 40,
              controlHeightSM: 24,
            },
            Form: {
              itemMarginBottom: 16,
              verticalLabelPadding: '0 0 8px',
              labelFontSize: 14,
              labelColor: 'rgba(0, 0, 0, 0.85)',
              labelRequiredMarkColor: '#ff4d4f',
            },
            Statistic: {
              titleFontSize: 14,
              contentFontSize: 24,
            },
            Notification: {
              width: 384,
              borderRadius: 8,
              boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
            },
            Message: {
              borderRadius: 6,
              boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
            },
            Drawer: {
              borderRadius: 8,
              borderRadiusOuter: 0,
            },
            Progress: {
              defaultColor: '#1890ff',
              remainingColor: 'rgba(0, 0, 0, 0.06)',
              circleTextColor: 'rgba(0, 0, 0, 0.85)',
              lineBorderRadius: 100,
            },
            Tag: {
              borderRadiusSM: 4,
              fontSizeSM: 12,
              lineHeightSM: 1.5,
            },
            Spin: {
              dotSize: 20,
              dotSizeLG: 32,
              dotSizeSM: 14,
            }
          }
        }}
      >
        <MainApp />
      </ConfigProvider>
      </AppContextProvider>
    </React.StrictMode>,
  )

  Logger.info('Main', '✅ تم تحميل تطبيق React بنجاح')
  } catch (error) {
    Logger.error('Main', '❌ خطأ في تحميل تطبيق React:', error)

    // عرض رسالة خطأ في الصفحة
    const rootElement = document.getElementById('root')
    if (rootElement) {
      rootElement.innerHTML = `
        <div style="
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: 100vh;
          font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
          text-align: center;
          padding: 20px;
          background: #f0f2f5;
        ">
          <div style="
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            max-width: 500px;
          ">
            <h1 style="color: #ff4d4f; margin-bottom: 16px;">خطأ في تحميل التطبيق</h1>
            <p style="color: #666; margin-bottom: 20px;">حدث خطأ أثناء تحميل التطبيق. يرجى المحاولة مرة أخرى.</p>
            <button onclick="window.location.reload()" style="
              background: #1890ff;
              color: white;
              border: none;
              padding: 10px 20px;
              border-radius: 6px;
              cursor: pointer;
              font-size: 14px;
            ">إعادة تحميل</button>
            <details style="margin-top: 20px; text-align: left;">
              <summary style="cursor: pointer; color: #666;">تفاصيل الخطأ</summary>
              <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px; overflow: auto;">${error}</pre>
            </details>
          </div>
        </div>
      `
    }
  }
}

// تشغيل التطبيق
loadApp().catch(error => {
  console.error('خطأ في تحميل التطبيق:', error)
})
