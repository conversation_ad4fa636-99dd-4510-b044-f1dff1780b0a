import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { CodeGeneratorService } from './CodeGeneratorService'
import { Logger } from '../utils/logger'

export interface Customer {
  id: number
  code: string
  name: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  balance: number
  credit_limit: number
  payment_terms: number
  is_active: boolean
  created_at: string
  updated_at?: string
}

export interface CreateCustomerData {
  code?: string
  name: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  credit_limit?: number
  payment_terms?: number
  is_active?: boolean
}

export interface UpdateCustomerData extends CreateCustomerData {
  id: number
}

export class CustomerService {
  private static instance: CustomerService
  private db: any
  private codeGenerator: CodeGeneratorService

  private constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
    this.codeGenerator = new CodeGeneratorService(this.db)
  }

  public static getInstance(): CustomerService {
    if (!CustomerService.instance) {
      CustomerService.instance = new CustomerService()
    }
    return CustomerService.instance
  }

  // إنشاء جداول العملاء
  public async createCustomerTables(): Promise<void> {
    const database = this.db

    // جدول العملاء
    database.exec(`
      CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        contact_person TEXT,
        phone TEXT,
        email TEXT,
        address TEXT,
        balance DECIMAL(10,2) DEFAULT 0,
        credit_limit DECIMAL(10,2) DEFAULT 0,
        payment_terms INTEGER DEFAULT 30,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // جدول معاملات العملاء
    database.exec(`
      CREATE TABLE IF NOT EXISTS customer_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        transaction_type TEXT NOT NULL CHECK (transaction_type IN ('invoice', 'payment', 'credit', 'debit')),
        reference_type TEXT,
        reference_id INTEGER,
        amount DECIMAL(10,2) NOT NULL,
        description TEXT,
        transaction_date DATE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id)
      )
    `)

    // إنشاء الفهارس
    database.exec('CREATE INDEX IF NOT EXISTS idx_customers_code ON customers(code)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_customers_active ON customers(is_active)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_customer_transactions_customer ON customer_transactions(customer_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_customer_transactions_date ON customer_transactions(transaction_date)')

    // تشغيل Migration التلقائي
    await this.runCustomerMigrations()
  }

  // تشغيل Migration التلقائي للعملاء
  private async runCustomerMigrations(): Promise<void> {
    try {
      Logger.info('CustomerService', '🔄 بدء Migration التلقائي للعملاء...')

      // تحديث مخطط جدول العملاء
      await this.updateCustomerTableSchema()

      // إصلاح البيانات الموجودة
      await this.fixExistingCustomerData()

      Logger.info('CustomerService', '✅ تم إكمال Migration العملاء بنجاح')
    } catch (error) {
      Logger.error('CustomerService', '❌ خطأ في Migration العملاء:', error)
    }
  }

  // تحديث مخطط جدول العملاء
  private async updateCustomerTableSchema(): Promise<void> {
    try {
      Logger.info('CustomerService', '🔧 فحص وتحديث مخطط جدول العملاء...')

      // الحصول على معلومات الأعمدة الحالية
      const tableInfo = this.db.prepare("PRAGMA table_info(customers)").all()
      const existingColumns = tableInfo.map((col: any) => col.name)

      // قائمة الأعمدة الجديدة المطلوبة
      const newColumns = [
        { name: 'tax_number', type: 'TEXT' },
        { name: 'commercial_register', type: 'TEXT' },
        { name: 'website', type: 'TEXT' },
        { name: 'notes', type: 'TEXT' },
        { name: 'customer_type', type: 'TEXT DEFAULT "individual"' },
        { name: 'discount_percentage', type: 'DECIMAL(5,2) DEFAULT 0' },
        { name: 'preferred_payment_method', type: 'TEXT DEFAULT "cash"' },
        { name: 'credit_days', type: 'INTEGER DEFAULT 30' },
        { name: 'last_transaction_date', type: 'DATETIME' },
        { name: 'total_purchases', type: 'DECIMAL(15,2) DEFAULT 0' },
        { name: 'created_by', type: 'INTEGER' }
      ]

      // إضافة الأعمدة المفقودة
      for (const column of newColumns) {
        if (!existingColumns.includes(column.name)) {
          try {
            this.db.exec(`ALTER TABLE customers ADD COLUMN ${column.name} ${column.type}`)
            Logger.info('CustomerService', '✅ تم إضافة العمود: ${column.name}')
          } catch (error) {
            Logger.info('CustomerService', '⚠️ فشل في إضافة العمود ${column.name}:', error)
          }
        }
      }

    } catch (error) {
      Logger.error('CustomerService', 'خطأ في تحديث مخطط جدول العملاء:', error)
    }
  }

  // إصلاح البيانات الموجودة للعملاء
  private async fixExistingCustomerData(): Promise<void> {
    try {
      Logger.info('CustomerService', '🔧 بدء إصلاح البيانات الموجودة للعملاء...')

      // 1. تحديث is_active للعملاء الذين لا يحتوون على قيمة
      const updateActiveResult = this.db.prepare(`
        UPDATE customers
        SET is_active = 1
        WHERE is_active IS NULL OR is_active = 0
      `).run()

      if (updateActiveResult.changes > 0) {
        Logger.info('CustomerService', '✅ تم تحديث حالة النشاط لـ ${updateActiveResult.changes} عميل')
      }

      // 2. تحديث customer_type للعملاء الذين لا يحتوون على قيمة
      const updateTypeResult = this.db.prepare(`
        UPDATE customers
        SET customer_type = 'individual'
        WHERE customer_type IS NULL OR customer_type = ''
      `).run()

      if (updateTypeResult.changes > 0) {
        Logger.info('CustomerService', '✅ تم تحديث نوع العميل لـ ${updateTypeResult.changes} عميل')
      }

      // 3. تحديث preferred_payment_method للعملاء الذين لا يحتوون على قيمة
      const updatePaymentResult = this.db.prepare(`
        UPDATE customers
        SET preferred_payment_method = 'cash'
        WHERE preferred_payment_method IS NULL OR preferred_payment_method = ''
      `).run()

      if (updatePaymentResult.changes > 0) {
        Logger.info('CustomerService', '✅ تم تحديث طريقة الدفع المفضلة لـ ${updatePaymentResult.changes} عميل')
      }

      // 4. تحديث credit_days للعملاء الذين لا يحتوون على قيمة
      const updateCreditDaysResult = this.db.prepare(`
        UPDATE customers
        SET credit_days = payment_terms
        WHERE credit_days IS NULL AND payment_terms IS NOT NULL
      `).run()

      if (updateCreditDaysResult.changes > 0) {
        Logger.info('CustomerService', '✅ تم تحديث أيام الائتمان لـ ${updateCreditDaysResult.changes} عميل')
      }

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()
      Logger.info('CustomerService', '✅ تم إصلاح البيانات الموجودة بنجاح')

    } catch (error) {
      Logger.error('CustomerService', 'خطأ في إصلاح البيانات الموجودة:', error)
    }
  }

  // الحصول على جميع العملاء
  public async getCustomers(): Promise<Customer[]> {
    try {
      const customers = this.db.prepare(`
        SELECT * FROM customers 
        WHERE is_active = 1 
        ORDER BY name
      `).all() as Customer[]

      return customers
    } catch (error) {
      Logger.error('CustomerService', 'خطأ في جلب العملاء:', error)
      return []
    }
  }

  // إنشاء عميل جديد
  public async createCustomer(customerData: CreateCustomerData): Promise<ApiResponse> {
    try {
      // التحقق من صحة البيانات الأساسية
      const validation = this.validateCustomerData(customerData)
      if (!validation.isValid) {
        return { success: false, message: validation.message }
      }

      // توليد كود تلقائي إذا لم يتم توفيره
      if (!customerData.code || customerData.code.trim() === '') {
        customerData.code = await this.generateCustomerCode()
      }

      // التحقق من تفرد الكود
      const existingCustomer = this.db.prepare('SELECT id FROM customers WHERE code = ?').get(customerData.code.trim())
      if (existingCustomer) {
        return { success: false, message: 'كود العميل موجود مسبقاً' }
      }

      // التحقق من تفرد البريد الإلكتروني إذا تم توفيره
      if (customerData.email && customerData.email.trim() !== '') {
        const existingEmail = this.db.prepare('SELECT id FROM customers WHERE email = ? AND email != ""').get(customerData.email.trim())
        if (existingEmail) {
          return { success: false, message: 'البريد الإلكتروني مستخدم من قبل عميل آخر' }
        }
      }

      // التحقق من تفرد البريد الإلكتروني
      if (customerData.email && customerData.email.trim()) {
        const existingEmail = this.db.prepare('SELECT id FROM customers WHERE email = ?').get(customerData.email.trim())
        if (existingEmail) {
          return { success: false, message: 'البريد الإلكتروني موجود مسبقاً' }
        }
      }

      // إدراج العميل
      const result = this.db.prepare(`
        INSERT INTO customers (
          code, name, contact_person, phone, email, address,
          balance, credit_limit, payment_terms, is_active,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `).run([
        customerData.code?.trim() || '',
        customerData.name.trim(),
        customerData.contact_person?.trim() || null,
        customerData.phone?.trim() || null,
        customerData.email?.trim() || null,
        customerData.address?.trim() || null,
        0, // balance - الرصيد الافتراضي
        customerData.credit_limit || 0,
        customerData.payment_terms || 30,
        customerData.is_active ? 1 : 0
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إنشاء العميل بنجاح', data: { id: result.lastInsertRowid } }
      } else {
        return { success: false, message: 'فشل في إنشاء العميل' }
      }
    } catch (error: any) {
      Logger.error('CustomerService', 'خطأ في إنشاء العميل:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود العميل موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في إنشاء العميل' }
    }
  }

  // تحديث عميل
  public async updateCustomer(customerData: UpdateCustomerData): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        UPDATE customers SET
          code = ?, name = ?, contact_person = ?, phone = ?, email = ?, address = ?,
          credit_limit = ?, payment_terms = ?, is_active = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run(
        customerData.code,
        customerData.name,
        customerData.contact_person || null,
        customerData.phone || null,
        customerData.email || null,
        customerData.address || null,
        customerData.credit_limit || 0,
        customerData.payment_terms || 30,
        customerData.is_active ? 1 : 0,
        customerData.id
      )

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث العميل بنجاح' }
      } else {
        return { success: false, message: 'لم يتم العثور على العميل' }
      }
    } catch (error: any) {
      Logger.error('CustomerService', 'خطأ في تحديث العميل:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود العميل موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في تحديث العميل' }
    }
  }

  // حذف عميل
  public async deleteCustomer(customerId: number): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        UPDATE customers SET is_active = 0, updated_at = datetime('now')
        WHERE id = ?
      `).run(customerId)

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم حذف العميل بنجاح' }
      } else {
        return { success: false, message: 'لم يتم العثور على العميل' }
      }
    } catch (error) {
      Logger.error('CustomerService', 'خطأ في حذف العميل:', error)
      return { success: false, message: 'حدث خطأ في حذف العميل' }
    }
  }

  // التحقق من صحة بيانات العميل
  private validateCustomerData(customerData: CreateCustomerData): { isValid: boolean; message?: string } {
    // التحقق من الاسم
    if (!customerData.name || customerData.name.trim().length === 0) {
      return { isValid: false, message: 'اسم العميل مطلوب' }
    }

    if (customerData.name.length > 100) {
      return { isValid: false, message: 'اسم العميل يجب أن يكون أقل من 100 حرف' }
    }

    // التحقق من الكود إذا تم توفيره
    if (customerData.code && customerData.code.length > 20) {
      return { isValid: false, message: 'كود العميل يجب أن يكون أقل من 20 حرف' }
    }

    // التحقق من البريد الإلكتروني
    if (customerData.email && customerData.email.trim().length > 0) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(customerData.email)) {
        return { isValid: false, message: 'البريد الإلكتروني غير صحيح' }
      }
    }

    // التحقق من رقم الهاتف
    if (customerData.phone && customerData.phone.trim().length > 0) {
      const phoneRegex = /^[0-9+\-\s()]+$/
      if (!phoneRegex.test(customerData.phone)) {
        return { isValid: false, message: 'رقم الهاتف غير صحيح' }
      }
    }

    // التحقق من حد الائتمان
    if (customerData.credit_limit && customerData.credit_limit < 0) {
      return { isValid: false, message: 'حد الائتمان لا يمكن أن يكون سالباً' }
    }

    // التحقق من شروط الدفع
    if (customerData.payment_terms && customerData.payment_terms < 0) {
      return { isValid: false, message: 'شروط الدفع لا يمكن أن تكون سالبة' }
    }

    return { isValid: true }
  }

  // توليد كود عميل جديد متسلسل
  public async generateCustomerCode(): Promise<string> {
    try {
      // الحصول على جميع الأكواد الموجودة مرتبة
      const existingCodes = this.db.prepare(`
        SELECT code FROM customers
        WHERE code LIKE 'CUST%' AND code IS NOT NULL AND code != ''
        ORDER BY CAST(SUBSTR(code, 5) AS INTEGER) ASC
      `).all() as any[]

      // استخراج الأرقام من الأكواد
      const existingNumbers = existingCodes
        .map(row => {
          const codeNumber = row.code.substring(4)
          const number = parseInt(codeNumber)
          return isNaN(number) ? 0 : number
        })
        .filter(num => num > 0)
        .sort((a, b) => a - b)

      // البحث عن أول رقم متاح في التسلسل
      let nextNumber = 1
      for (const num of existingNumbers) {
        if (num === nextNumber) {
          nextNumber++
        } else if (num > nextNumber) {
          // وجدنا فجوة في التسلسل
          break
        }
      }

      // تكوين الكود الجديد
      const newCode = `CUST${nextNumber.toString().padStart(3, '0')}`

      // التحقق النهائي من عدم وجود الكود (احتياط إضافي)
      const existingCustomer = this.db.prepare('SELECT id FROM customers WHERE code = ?').get(newCode)
      if (existingCustomer) {
        // في حالة وجود تضارب غير متوقع، ابحث عن أول رقم متاح بعد آخر رقم
        const maxNumber = Math.max(...existingNumbers, 0)
        return `CUST${(maxNumber + 1).toString().padStart(3, '0')}`
      }

      return newCode
    } catch (error) {
      Logger.error('CustomerService', 'خطأ في توليد كود العميل:', error)
      // كود احتياطي باستخدام timestamp
      return `CUST${Date.now().toString().slice(-3)}`
    }
  }

  // توليد كود عميل جديد متسلسل (متزامن)
  public generateCustomerCodeSync(): string {
    try {
      // الحصول على جميع الأكواد الموجودة مرتبة
      const existingCodes = this.db.prepare(`
        SELECT code FROM customers
        WHERE code LIKE 'CUST%' AND code IS NOT NULL AND code != ''
        ORDER BY CAST(SUBSTR(code, 5) AS INTEGER) ASC
      `).all() as any[]

      // استخراج الأرقام من الأكواد
      const existingNumbers = existingCodes
        .map(row => {
          const codeNumber = row.code.substring(4)
          const number = parseInt(codeNumber)
          return isNaN(number) ? 0 : number
        })
        .filter(num => num > 0)
        .sort((a, b) => a - b)

      // البحث عن أول رقم متاح في التسلسل
      let nextNumber = 1
      for (const num of existingNumbers) {
        if (num === nextNumber) {
          nextNumber++
        } else if (num > nextNumber) {
          // وجدنا فجوة في التسلسل
          break
        }
      }

      // تكوين الكود الجديد
      const newCode = `CUST${nextNumber.toString().padStart(3, '0')}`

      // التحقق النهائي من عدم وجود الكود (احتياط إضافي)
      const existingCustomer = this.db.prepare('SELECT id FROM customers WHERE code = ?').get(newCode)
      if (existingCustomer) {
        // في حالة وجود تضارب غير متوقع، ابحث عن أول رقم متاح بعد آخر رقم
        const maxNumber = Math.max(...existingNumbers, 0)
        return `CUST${(maxNumber + 1).toString().padStart(3, '0')}`
      }

      return newCode
    } catch (error) {
      Logger.error('CustomerService', 'خطأ في توليد كود العميل:', error)
      return `CUST${Date.now().toString().slice(-3)}`
    }
  }

  // الحصول على رصيد العميل
  public async getCustomerBalance(customerId: number): Promise<number> {
    try {
      const customer = this.db.prepare(`
        SELECT balance FROM customers WHERE id = ?
      `).get(customerId) as any

      return customer ? customer.balance : 0
    } catch (error) {
      Logger.error('CustomerService', 'خطأ في جلب رصيد العميل:', error)
      return 0
    }
  }

  // تحديث رصيد العميل
  public async updateCustomerBalance(customerId: number, amount: number, transactionType: string, description?: string): Promise<ApiResponse> {
    try {
      // تحديث رصيد العميل
      this.db.prepare(`
        UPDATE customers 
        SET balance = balance + ?, updated_at = datetime('now')
        WHERE id = ?
      `).run(amount, customerId)

      // إضافة معاملة
      this.db.prepare(`
        INSERT INTO customer_transactions (
          customer_id, transaction_type, amount, description, transaction_date
        ) VALUES (?, ?, ?, ?, date('now'))
      `).run(customerId, transactionType, amount, description || null)

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()
      return { success: true, message: 'تم تحديث رصيد العميل بنجاح' }
    } catch (error) {
      Logger.error('CustomerService', 'خطأ في تحديث رصيد العميل:', error)
      return { success: false, message: 'حدث خطأ في تحديث رصيد العميل' }
    }
  }

  // الحصول على معاملات العميل
  public async getCustomerTransactions(customerId: number): Promise<any[]> {
    try {
      const transactions = this.db.prepare(`
        SELECT * FROM customer_transactions 
        WHERE customer_id = ? 
        ORDER BY transaction_date DESC, created_at DESC
      `).all(customerId)

      return transactions
    } catch (error) {
      Logger.error('CustomerService', 'خطأ في جلب معاملات العميل:', error)
      return []
    }
  }

  // تم إزالة جميع دوال البيانات التجريبية - يجب استخدام البيانات الحقيقية فقط
}
