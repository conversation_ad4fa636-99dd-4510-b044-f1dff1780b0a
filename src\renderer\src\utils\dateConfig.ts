import dayjs from 'dayjs';
import 'dayjs/locale/en';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';
import dayOfYear from 'dayjs/plugin/dayOfYear';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

// تحميل الإضافات المطلوبة
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
dayjs.extend(advancedFormat);
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);
dayjs.extend(dayOfYear);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

// تعيين اللغة الإنجليزية للتقويم الميلادي
dayjs.locale('en');

// تنسيقات التاريخ المستخدمة في التطبيق
export const DATE_FORMATS = {
  // تنسيقات عرض التاريخ
  DISPLAY_DATE: 'YYYY-MM-DD',
  DISPLAY_DATE_TIME: 'YYYY-MM-DD HH:mm',
  DISPLAY_DATE_TIME_SECONDS: 'YYYY-MM-DD HH:mm:ss',
  DISPLAY_DATE_ARABIC: 'D MMMM YYYY',
  DISPLAY_DATE_TIME_ARABIC: 'D MMMM YYYY HH:mm',
  
  // تنسيقات قاعدة البيانات
  DATABASE_DATE: 'YYYY-MM-DD',
  DATABASE_DATETIME: 'YYYY-MM-DD HH:mm:ss',
  
  // تنسيقات التقارير
  REPORT_DATE: 'YYYY-MM-DD',
  REPORT_MONTH: 'YYYY-MM',
  REPORT_YEAR: 'YYYY',
  
  // تنسيقات الإدخال
  INPUT_DATE: 'YYYY-MM-DD',
  INPUT_DATETIME: 'YYYY-MM-DD HH:mm'
};

// دوال مساعدة للتاريخ
export const DateUtils = {
  // تنسيق التاريخ للعرض
  formatForDisplay: (date: string | Date | dayjs.Dayjs, format: string = DATE_FORMATS.DISPLAY_DATE): string => {
    if (!date) return '';
    return dayjs(date).format(format);
  },

  // تنسيق التاريخ لقاعدة البيانات
  formatForDatabase: (date: string | Date | dayjs.Dayjs): string => {
    if (!date) return '';
    return dayjs(date).format(DATE_FORMATS.DATABASE_DATE);
  },

  // تنسيق التاريخ والوقت لقاعدة البيانات
  formatDateTimeForDatabase: (date: string | Date | dayjs.Dayjs): string => {
    if (!date) return '';
    return dayjs(date).format(DATE_FORMATS.DATABASE_DATETIME);
  },

  // الحصول على التاريخ الحالي
  getCurrentDate: (): dayjs.Dayjs => {
    return dayjs();
  },

  // الحصول على بداية اليوم
  getStartOfDay: (date?: string | Date | dayjs.Dayjs): dayjs.Dayjs => {
    return dayjs(date).startOf('day');
  },

  // الحصول على نهاية اليوم
  getEndOfDay: (date?: string | Date | dayjs.Dayjs): dayjs.Dayjs => {
    return dayjs(date).endOf('day');
  },

  // الحصول على بداية الشهر
  getStartOfMonth: (date?: string | Date | dayjs.Dayjs): dayjs.Dayjs => {
    return dayjs(date).startOf('month');
  },

  // الحصول على نهاية الشهر
  getEndOfMonth: (date?: string | Date | dayjs.Dayjs): dayjs.Dayjs => {
    return dayjs(date).endOf('month');
  },

  // الحصول على بداية السنة
  getStartOfYear: (date?: string | Date | dayjs.Dayjs): dayjs.Dayjs => {
    return dayjs(date).startOf('year');
  },

  // الحصول على نهاية السنة
  getEndOfYear: (date?: string | Date | dayjs.Dayjs): dayjs.Dayjs => {
    return dayjs(date).endOf('year');
  },

  // إضافة أيام
  addDays: (date: string | Date | dayjs.Dayjs, days: number): dayjs.Dayjs => {
    return dayjs(date).add(days, 'day');
  },

  // إضافة أشهر
  addMonths: (date: string | Date | dayjs.Dayjs, months: number): dayjs.Dayjs => {
    return dayjs(date).add(months, 'month');
  },

  // إضافة سنوات
  addYears: (date: string | Date | dayjs.Dayjs, years: number): dayjs.Dayjs => {
    return dayjs(date).add(years, 'year');
  },

  // التحقق من صحة التاريخ
  isValid: (date: string | Date | dayjs.Dayjs): boolean => {
    return dayjs(date).isValid();
  },

  // مقارنة التواريخ
  isBefore: (date1: string | Date | dayjs.Dayjs, date2: string | Date | dayjs.Dayjs): boolean => {
    return dayjs(date1).isBefore(dayjs(date2));
  },

  isAfter: (date1: string | Date | dayjs.Dayjs, date2: string | Date | dayjs.Dayjs): boolean => {
    return dayjs(date1).isAfter(dayjs(date2));
  },

  isSame: (date1: string | Date | dayjs.Dayjs, date2: string | Date | dayjs.Dayjs, unit?: dayjs.UnitType): boolean => {
    return dayjs(date1).isSame(dayjs(date2), unit);
  },

  // الحصول على الفرق بين تاريخين
  diff: (date1: string | Date | dayjs.Dayjs, date2: string | Date | dayjs.Dayjs, unit?: dayjs.UnitType): number => {
    return dayjs(date1).diff(dayjs(date2), unit);
  },

  // تحويل التاريخ إلى نص نسبي
  fromNow: (date: string | Date | dayjs.Dayjs): string => {
    return dayjs(date).fromNow();
  },

  // تحويل التاريخ إلى نص نسبي من تاريخ محدد
  from: (date: string | Date | dayjs.Dayjs, compareDate: string | Date | dayjs.Dayjs): string => {
    return dayjs(date).from(dayjs(compareDate));
  }
};

// تصدير dayjs المُكوَّن
export default dayjs;
