import { ipcMain, app, dialog } from 'electron'
import { DatabaseService, AuthService } from '../services'
import { EncryptionService } from '../services/EncryptionService'
import { PrintSettingsService } from '../services/PrintSettingsService'
import * as fs from 'fs'
import * as path from 'path'
import { Logger } from '../utils/logger'
import { dataMigrationManager } from '../utils/dataMigration'

let databaseService: DatabaseService
let authService: AuthService
let encryptionService: EncryptionService

// دالة مساعدة لتنفيذ استعلامات SELECT مع sql.js
function executeSelectQuery(db: any, sql: string, params?: any[]): any[] {
  try {
    // استخدام prepared statement إذا كانت هناك معاملات
    let result: any
    if (params && params.length > 0) {
      const stmt = db.prepare(sql)
      result = stmt.all(...params)
      return result || []
    } else {
      result = db.exec(sql)
      if (result && result.length > 0) {
        const columns = result[0].columns
        const values = result[0].values
        return values.map((row: any[]) => {
          const obj: any = {}
          columns.forEach((col: string, index: number) => {
            obj[col] = row[index]
          })
          return obj
        })
      }
    }
    return []
  } catch (error) {
    Logger.error('SystemHandlers', 'خطأ في تنفيذ استعلام SELECT:', error)
    return []
  }
}

// دالة مساعدة لتنفيذ استعلام SELECT واحد
function executeSelectOne(db: any, sql: string, params?: any[]): any {
  try {
    // استخدام prepared statement إذا كانت هناك معاملات
    if (params && params.length > 0) {
      const stmt = db.prepare(sql)
      return stmt.get(...params) || null
    } else {
      const result = db.exec(sql)
      if (result && result.length > 0 && result[0].values.length > 0) {
        const columns = result[0].columns
        const values = result[0].values[0]
        const obj: any = {}
        columns.forEach((col: string, index: number) => {
          obj[col] = values[index]
        })
        return obj
      }
    }
    return null
  } catch (error) {
    Logger.error('SystemHandlers', 'خطأ في تنفيذ استعلام SELECT واحد:', error)
    return null
  }
}

// تعيين المراجع للخدمات
export function setServiceReferences(dbService: DatabaseService, authSvc: AuthService) {
  databaseService = dbService
  authService = authSvc
  encryptionService = EncryptionService.getInstance()
}

export function registerSystemHandlers(): void {
  // فحص صحة قاعدة البيانات
  ipcMain.handle('check-database-health', async () => {
    try {
      return databaseService.checkHealth()
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في فحص قاعدة البيانات:', error)
      return { success: false, message: 'حدث خطأ في فحص قاعدة البيانات' }
    }
  })

  // تنّيف الجلسات المنتهية الصلاحية
  ipcMain.handle('cleanup-expired-sessions', async () => {
    try {
      await authService.cleanupExpiredSessions()
      return { success: true, message: 'تم تنّيف الجلسات المنتهية الصلاحية' }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في تنّيف الجلسات:', error)
      return { success: false, message: 'حدث خطأ في تنّيف الجلسات' }
    }
  })

  // إعادة تهيئة قاعدة البيانات
  ipcMain.handle('reinitialize-database', async () => {
    try {
      await databaseService.initialize()
      return { success: true, message: 'تم إعادة تهيئة قاعدة البيانات بنجاح' }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في إعادة تهيئة قاعدة البيانات:', error)
      return { success: false, message: 'حدث خطأ في إعادة تهيئة قاعدة البيانات' }
    }
  })

  // الحصول على الإعدادات (موحد مع دعم البادئة)
  ipcMain.handle('get-settings', async (_, prefix?: string) => {
    try {
      const db = databaseService.getDatabase()
      Logger.info('SystemHandlers', 'محاولة جلب الإعدادات من قاعدة البيانات...')

      let query = 'SELECT key, value FROM settings'
      if (prefix) {
        query += ` WHERE key LIKE '${prefix}%'`
      }

      const result = executeSelectQuery(db, query)
      Logger.info('SystemHandlers', 'نتيجة استعلام الإعدادات:', result)

      // إرجاع البيانات بصيغة مختلفة حسب وجود البادئة
      if (prefix) {
        // إرجاع كخريطة للإعدادات المفلترة
        const settingsMap: { [key: string]: string } = {}
        if (result && Array.isArray(result)) {
          result.forEach((row: any) => {
            settingsMap[row.key] = row.value
          })
        }
        return { success: true, data: settingsMap }
      } else {
        // إرجاع كمصفوفة لجميع الإعدادات
        const settingsArray: any[] = []
        if (result && Array.isArray(result)) {
          Logger.info('SystemHandlers', 'عدد الإعدادات المسترجعة:', result.length)

          result.forEach((row: any) => {
            // فك تشفير القيمة إذا كانت مشفرة
            if (encryptionService && row.key && row.value && encryptionService.isSensitiveSetting(row.key)) {
              row.value = encryptionService.decrypt(row.value)
              Logger.info('SystemHandlers', 'تم فك تشفير الإعداد الحساس: ' + row.key)
            }
            settingsArray.push(row)
          })

          Logger.info('SystemHandlers', 'تم جلب ' + settingsArray.length + ' إعداد من قاعدة البيانات')
          Logger.info('SystemHandlers', 'الإعدادات المجلبة:', settingsArray.map(s => s.key + '=' + s.value))
        } else {
          Logger.warn('SystemHandlers', 'لم يتم العثور على إعدادات في قاعدة البيانات')
        }

        return { success: true, data: settingsArray }
      }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في جلب الإعدادات:', error)
      return {
        success: false,
        message: 'حدث خطأ في جلب الإعدادات',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  })

  // تحديث إعداد واحد
  ipcMain.handle('update-setting', async (_, key: string, value: string) => {
    try {
      const db = databaseService.getDatabase()

      // التحقق من صحة المدخلات
      if (!key || typeof key !== 'string') {
        Logger.error('SystemHandlers', 'مفتاح الإعداد غير صحيح:', key)
        return { success: false, message: 'مفتاح الإعداد غير صحيح' }
      }

      // التحقق من أمان مفتاح الإعداد (منع SQL injection)
      const keyRegex = /^[a-zA-Z0-9_-]+$/
      if (!keyRegex.test(key)) {
        Logger.error('SystemHandlers', 'مفتاح الإعداد يحتوي على أحرف غير مسموحة:', key)
        return { success: false, message: 'مفتاح الإعداد يحتوي على أحرف غير مسموحة' }
      }

      // التحقق من طول القيمة
      if (value && value.length > 10000) {
        Logger.error('SystemHandlers', 'قيمة الإعداد طويلة جداً:', value.length)
        return { success: false, message: 'قيمة الإعداد طويلة جداً' }
      }

      Logger.info('SystemHandlers', 'محاولة تحديث الإعداد: ${key} = ${value}')

      // تحويل القيمة إلى نص وحمايتها من SQL injection
      let safeValue = value != null ? String(value).replace(/'/g, "''") : ''

      // تشفير القيمة إذا كانت حساسة
      if (encryptionService && encryptionService.isSensitiveSetting(key)) {
        safeValue = encryptionService.encrypt(safeValue)
        Logger.info('SystemHandlers', 'تم تشفير الإعداد الحساس: ' + key)
      }

      // التحقق من وجود الإعداد
      const existing = executeSelectOne(db, `SELECT id FROM settings WHERE key = ?`, [key])

      if (existing) {
        // تحديث الإعداد الموجود
        Logger.info('SystemHandlers', 'تحديث الإعداد الموجود: ' + key)
        const updateStmt = db.prepare(`UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?`)
        updateStmt.run(value, key)
      } else {
        // إنشاء إعداد جديد
        Logger.info('SystemHandlers', 'إنشاء إعداد جديد: ' + key)
        const insertStmt = db.prepare(`INSERT INTO settings (key, value, description, created_at, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`)
        insertStmt.run(key, value, 'إعداد ' + key)
      }

      // حفّ التغييرات
      databaseService.saveDatabase()

      // فحص سلامة قاعدة البيانات بعد الحفّ
      const healthCheck = databaseService.checkHealth()
      if (!healthCheck.success) {
        Logger.error('SystemHandlers', '❌ فشل في فحص سلامة قاعدة البيانات:', healthCheck.message)
        return { success: false, message: 'فشل في حفّ البيانات - مشكلة في قاعدة البيانات' }
      }

      // التحقق من حفّ البيانات
      const verificationResult = executeSelectOne(db, `SELECT value FROM settings WHERE key = ?`, [key])

      if (verificationResult) {
        const savedValue = verificationResult.value
        Logger.info('SystemHandlers', 'تم التحقق من حفّ الإعداد: ' + key + ' = ' + savedValue)

        if (savedValue === safeValue) {
          Logger.info('SystemHandlers', '✅ تم تحديث الإعداد بنجاح: ' + key + ' = ' + value)
          return { success: true, message: 'تم تحديث الإعداد بنجاح' }
        } else {
          Logger.error('SystemHandlers', '❌ فشل في حفّ الإعداد: ' + key + ', القيمة المحفوّة: ' + savedValue + ', القيمة المطلوبة: ' + safeValue)
          return { success: false, message: 'فشل في حفّ الإعداد' }
        }
      } else {
        Logger.error('SystemHandlers', '❌ لم يتم العثور على الإعداد بعد الحفّ: ' + key)
        return { success: false, message: 'فشل في حفّ الإعداد' }
      }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في تحديث الإعداد:', error)
      return { success: false, message: 'حدث خطأ في تحديث الإعداد' }
    }
  })

  // تحديث عدة إعدادات
  ipcMain.handle('update-settings', async (_, settings: any) => {
    try {
      const db = databaseService.getDatabase()

      // التحقق من صحة المدخلات
      if (!settings || typeof settings !== 'object') {
        Logger.error('SystemHandlers', 'بيانات الإعدادات غير صحيحة:', settings)
        return { success: false, message: 'بيانات الإعدادات غير صحيحة' }
      }

      Logger.info('SystemHandlers', 'محاولة تحديث عدة إعدادات:', settings)

      let updatedCount = 0
      for (const [key, value] of Object.entries(settings)) {
        // التحقق من صحة المفتاح
        if (!key || typeof key !== 'string') {
          Logger.warn('SystemHandlers', 'تخطي مفتاح غير صحيح: ${key}')
          continue // تخطي المفاتيح غير الصحيحة
        }

        // تحويل القيمة إلى نص وحمايتها من SQL injection
        let stringValue = value != null ? String(value).replace(/'/g, "''") : ''

        // تشفير القيمة إذا كانت حساسة
        if (encryptionService && encryptionService.isSensitiveSetting(key)) {
          stringValue = encryptionService.encrypt(stringValue)
          Logger.info('SystemHandlers', 'تم تشفير الإعداد الحساس: ' + key)
        }

        // التحقق من وجود الإعداد
        const existing = executeSelectOne(db, `SELECT id FROM settings WHERE key = ?`, [key])

        if (existing) {
          // تحديث الإعداد الموجود
          Logger.info('SystemHandlers', 'تحديث الإعداد الموجود: ' + key)
          const updateStmt = db.prepare(`UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?`)
          updateStmt.run(stringValue, key)
        } else {
          // إنشاء إعداد جديد
          Logger.info('SystemHandlers', 'إنشاء إعداد جديد: ' + key)
          const insertStmt = db.prepare(`INSERT INTO settings (key, value, description, created_at, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`)
          insertStmt.run(key, stringValue, 'إعداد ' + key)
        }
        updatedCount++
      }

      // حفّ التغييرات
      databaseService.saveDatabase()

      // فحص سلامة قاعدة البيانات بعد الحفّ
      const healthCheck = databaseService.checkHealth()
      if (!healthCheck.success) {
        Logger.error('SystemHandlers', '❌ فشل في فحص سلامة قاعدة البيانات:', healthCheck.message)
        return { success: false, message: 'فشل في حفّ البيانات - مشكلة في قاعدة البيانات' }
      }

      // التحقق من حفّ البيانات
      let verifiedCount = 0
      for (const [key, value] of Object.entries(settings)) {
        if (!key || typeof key !== 'string') continue

        let stringValue = value != null ? String(value).replace(/'/g, "''") : ''

        // تشفير القيمة إذا كانت حساسة للمقارنة
        if (encryptionService && encryptionService.isSensitiveSetting(key)) {
          stringValue = encryptionService.encrypt(stringValue)
        }

        const verificationResult = executeSelectOne(db, `SELECT value FROM settings WHERE key = ?`, [key])

        if (verificationResult) {
          const savedValue = verificationResult.value
          if (savedValue === stringValue) {
            verifiedCount++
          } else {
            Logger.error('SystemHandlers', '❌ فشل في حفّ الإعداد: ' + key + ', القيمة المحفوّة: ' + savedValue + ', القيمة المطلوبة: ' + stringValue)
          }
        }
      }

      Logger.info('SystemHandlers', 'تم التحقق من حفّ ' + verifiedCount + ' من ' + updatedCount + ' إعداد')
      Logger.info('SystemHandlers', '📊 تفاصيل فحص قاعدة البيانات:', healthCheck.details)

      if (verifiedCount === updatedCount) {
        Logger.info('SystemHandlers', '✅ تم تحديث جميع الإعدادات بنجاح')
        return { success: true, message: 'تم تحديث ' + updatedCount + ' إعداد بنجاح' }
      } else {
        Logger.error('SystemHandlers', '❌ فشل في حفّ بعض الإعدادات: ' + verifiedCount + '/' + updatedCount)
        return { success: false, message: 'فشل في حفّ بعض الإعدادات' }
      }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في تحديث الإعدادات:', error)
      return { success: false, message: 'حدث خطأ في تحديث الإعدادات' }
    }
  })

  // الحصول على إعداد واحد
  ipcMain.handle('get-setting', async (_, key: string) => {
    try {
      // التحقق من صحة مفتاح الإعداد
      if (!key || typeof key !== 'string') {
        return { success: false, message: 'مفتاح الإعداد غير صحيح' }
      }

      const keyRegex = /^[a-zA-Z0-9_-]+$/
      if (!keyRegex.test(key)) {
        return { success: false, message: 'مفتاح الإعداد يحتوي على أحرف غير مسموحة' }
      }

      const db = databaseService.getDatabase()
      const result = executeSelectOne(db, `SELECT value FROM settings WHERE key = ?`, [key])

      if (result) {
        let value = result.value

        // فك تشفير القيمة إذا كانت مشفرة
        if (encryptionService && encryptionService.isSensitiveSetting(key)) {
          value = encryptionService.decrypt(value)
          Logger.info('SystemHandlers', 'تم فك تشفير الإعداد الحساس: ' + key)
        }

        return { success: true, data: value }
      } else {
        return { success: false, message: 'الإعداد غير موجود' }
      }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في جلب الإعداد:', error)
      return { success: false, message: 'حدث خطأ في جلب الإعداد' }
    }
  })

  // الحصول على محاولات تسجيل الدخول الأخيرة
  ipcMain.handle('get-recent-login-attempts', async () => {
    try {
      const db = databaseService.getDatabase()
      const attempts = executeSelectQuery(db, `
        SELECT * FROM login_attempts
        ORDER BY attempt_time DESC
        LIMIT 10
      `)

      const loginAttempts: any[] = []
      if (attempts && Array.isArray(attempts)) {
        attempts.forEach((row: any) => {
          loginAttempts.push(row)
        })
      }

      return { success: true, data: loginAttempts }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في جلب محاولات تسجيل الدخول:', error)
      return { success: false, message: 'حدث خطأ في جلب محاولات تسجيل الدخول' }
    }
  })

  // شعار الشركة
  ipcMain.handle('get-company-logo', async () => {
    try {
      const db = databaseService.getDatabase()
      const result = executeSelectOne(db, "SELECT value FROM settings WHERE key = 'company_logo'")

      if (result && result.value) {
        const logoPath = result.value as string

        // التحقق من وجود الملف
        if (fs.existsSync(logoPath)) {
          try {
            // قراءة الملف وتحويله إلى base64
            const imageBuffer = fs.readFileSync(logoPath)
            const fileExtension = path.extname(logoPath).toLowerCase()

            // تحديد نوع MIME بناءً على امتداد الملف
            let mimeType = 'image/png' // افتراضي
            switch (fileExtension) {
              case '.jpg':
              case '.jpeg':
                mimeType = 'image/jpeg'
                break
              case '.png':
                mimeType = 'image/png'
                break
              case '.gif':
                mimeType = 'image/gif'
                break
              case '.bmp':
                mimeType = 'image/bmp'
                break
              case '.svg':
                mimeType = 'image/svg+xml'
                break
            }

            // تحويل إلى data URL
            const base64Data = imageBuffer.toString('base64')
            const dataUrl = 'data:' + mimeType + ';base64,' + base64Data

            return {
              success: true,
              logoPath: dataUrl,
              data: {
                id: 1,
                name: 'شعار الشركة',
                path: dataUrl,
                type: 'image'
              }
            }
          } catch (readError) {
            Logger.error('SystemHandlers', 'خطأ في قراءة ملف الشعار:', readError)
            return { success: false, message: 'فشل في قراءة ملف الشعار' }
          }
        } else {
          return { success: false, message: 'ملف الشعار غير موجود' }
        }
      } else {
        return { success: false, message: 'لم يتم العثور على شعار الشركة' }
      }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في جلب شعار الشركة:', error)
      return { success: false, message: 'حدث خطأ في جلب شعار الشركة' }
    }
  })

  // اختيار ورفع شعار الشركة
  ipcMain.handle('upload-company-logo', async () => {
    try {
      // فتح نافذة اختيار الملف
      const result = await dialog.showOpenDialog({
        title: 'اختر شعار الشركة',
        filters: [
          { name: 'صور', extensions: ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg'] },
          { name: 'جميع الملفات', extensions: ['*'] }
        ],
        properties: ['openFile']
      })

      if ((result as any).canceled || !(result as any).filePaths || (result as any).filePaths.length === 0) {
        return { success: false, message: 'تم إلغاء اختيار الملف' }
      }

      const selectedFilePath = (result as any).filePaths[0]
      const fileStats = fs.statSync(selectedFilePath)
      const fileExtension = path.extname(selectedFilePath).toLowerCase()

      // التحقق من نوع الملف
      const allowedExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg']
      if (!allowedExtensions.includes(fileExtension)) {
        return { success: false, message: 'نوع الملف غير مدعوم. يرجى اختيار صورة صالحة.' }
      }

      // التحقق من حجم الملف (أقل من 5 ميجابايت)
      const maxSize = 5 * 1024 * 1024 // 5MB
      if (fileStats.size > maxSize) {
        return { success: false, message: 'حجم الملف كبير جداً. يجب أن يكون أقل من 5 ميجابايت.' }
      }

      // إنشاء مجلد الشعارات إذا لم يكن موجوداً
      const logoDir = path.join(app.getPath('userData'), 'logos')
      if (!fs.existsSync(logoDir)) {
        fs.mkdirSync(logoDir, { recursive: true })
      }

      // نسخ الملف إلى مجلد التطبيق
      const newFileName = 'company_logo_' + Date.now() + fileExtension
      const newFilePath = path.join(logoDir, newFileName)
      fs.copyFileSync(selectedFilePath, newFilePath)

      // حفّ مسار الشعار في قاعدة البيانات
      const db = databaseService.getDatabase()
      const existing = executeSelectOne(db, "SELECT id FROM settings WHERE key = 'company_logo'")

      if (existing) {
        // تحديث الشعار الموجود
        const updateStmt = db.prepare('UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?')
        updateStmt.run(newFilePath, 'company_logo')
      } else {
        // إنشاء إعداد جديد للشعار
        const insertStmt = db.prepare('INSERT INTO settings (key, value, description, created_at, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)')
        insertStmt.run('company_logo', newFilePath, 'مسار شعار الشركة')
      }

      // حفّ التغييرات
      databaseService.saveDatabase()

      // تحويل الصورة المرفوعة إلى base64 للعرض
      try {
        const imageBuffer = fs.readFileSync(newFilePath)
        let mimeType = 'image/png' // افتراضي
        switch (fileExtension) {
          case '.jpg':
          case '.jpeg':
            mimeType = 'image/jpeg'
            break
          case '.png':
            mimeType = 'image/png'
            break
          case '.gif':
            mimeType = 'image/gif'
            break
          case '.bmp':
            mimeType = 'image/bmp'
            break
          case '.svg':
            mimeType = 'image/svg+xml'
            break
        }

        const base64Data = imageBuffer.toString('base64')
        const dataUrl = 'data:' + mimeType + ';base64,' + base64Data

        return {
          success: true,
          message: 'تم رفع الشعار بنجاح',
          fileName: newFileName,
          logoPath: dataUrl,
          size: fileStats.size,
          type: fileExtension
        }
      } catch (readError) {
        Logger.error('SystemHandlers', 'خطأ في قراءة الملف المرفوع:', readError)
        return {
          success: true,
          message: 'تم رفع الشعار بنجاح',
          fileName: newFileName,
          logoPath: newFilePath, // fallback to file path
          size: fileStats.size,
          type: fileExtension
        }
      }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في رفع شعار الشركة:', error)
      return { success: false, message: 'حدث خطأ أثناء رفع الشعار' }
    }
  })

  // حذف شعار الشركة
  ipcMain.handle('delete-company-logo', async () => {
    try {
      const db = databaseService.getDatabase()

      // جلب مسار الشعار الحالي
      const logoQuery = executeSelectOne(db, `SELECT value FROM settings WHERE key = 'company_logo'`)

      if (logoQuery && logoQuery.value) {
        const logoPath = logoQuery.value as string

        // حذف الملف من النّام إذا كان موجوداً
        if (logoPath && logoPath !== './default-logo.svg') {
          try {
            const fullPath = path.resolve(logoPath)
            if (fs.existsSync(fullPath)) {
              fs.unlinkSync(fullPath)
              Logger.info('SystemHandlers', '✅ تم حذف ملف الشعار:', fullPath)
            }
          } catch (fileError) {
            Logger.warn('SystemHandlers', 'تعذر حذف ملف الشعار:', fileError)
          }
        }

        // حذف السجل من قاعدة البيانات
        db.exec(`DELETE FROM settings WHERE key = 'company_logo'`)

        // حفّ التغييرات
        databaseService.saveDatabase()

        Logger.info('SystemHandlers', '✅ تم حذف شعار الشركة بنجاح')
        return {
          success: true,
          message: 'تم حذف شعار الشركة بنجاح'
        }
      } else {
        return {
          success: false,
          message: 'لا يوجد شعار لحذفه'
        }
      }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في حذف شعار الشركة:', error)
      return { success: false, message: 'حدث خطأ أثناء حذف الشعار' }
    }
  })

  // النسخ الاحتياطية
  ipcMain.handle('get-backups', async () => {
    try {
      const db = databaseService.getDatabase()
      const backups = db.exec(`
        SELECT * FROM backups
        ORDER BY created_at DESC
      `)

      const backupsArray: any[] = []
      if (backups.length > 0) {
        const columns = backups[0].columns
        backups[0].values.forEach((row: any) => {
          const backup: any = {}
          columns.forEach((col: string, index: number) => {
            backup[col] = row[index]
          })
          backupsArray.push(backup)
        })
      }

      return { success: true, data: backupsArray }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في جلب النسخ الاحتياطية:', error)
      return { success: false, message: 'حدث خطأ في جلب النسخ الاحتياطية' }
    }
  })

  // إعدادات النسخ الاحتياطي
  ipcMain.handle('get-backup-settings', async () => {
    try {
      const db = databaseService.getDatabase()

      // جلب إعدادات النسخ الاحتياطي من قاعدة البيانات
      const autoBackup = executeSelectOne(db, "SELECT value FROM settings WHERE key = 'auto_backup'")
      const backupInterval = executeSelectOne(db, "SELECT value FROM settings WHERE key = 'backup_interval'")
      const backupLocation = executeSelectOne(db, "SELECT value FROM settings WHERE key = 'backup_location'")
      const maxBackups = executeSelectOne(db, "SELECT value FROM settings WHERE key = 'max_backups'")

      const settings = {
        auto_backup: autoBackup && autoBackup.value === 'true',
        backup_interval: backupInterval ? backupInterval.value : 'daily',
        backup_location: backupLocation ? backupLocation.value : './backups',
        max_backups: maxBackups ? parseInt(maxBackups.value) : 10
      }

      return { success: true, data: settings }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في جلب إعدادات النسخ الاحتياطي:', error)
      return { success: false, message: 'حدث خطأ في جلب إعدادات النسخ الاحتياطي' }
    }
  })

  // إنشاء نسخة احتياطية
  ipcMain.handle('create-backup', async (_, options?: any) => {
    const path = require('path')
    const fs = require('fs')
    let backupId: any = null

    try {
      const db = databaseService.getDatabase()
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const fileName = options?.description ?
        `backup_${options.description}_${timestamp}.db` :
        `backup_${timestamp}.db`

      const backupDir = path.join(path.dirname(databaseService.getDatabasePath()), 'backups')
      const backupPath = path.join(backupDir, fileName)
      const filePath = './backups/' + fileName



      // 1. إنشاء سجل النسخة الاحتياطية بحالة "قيد التنفيذ"
      const insertStmt = db.prepare(`INSERT INTO backups (file_name, file_path, file_size, backup_type, status, description) VALUES (?, ?, 0, 'manual', 'in_progress', ?)`)
      insertStmt.run(fileName, filePath, options?.description || '')

      // جلب ID النسخة الاحتياطية المُدرجة حديثاً
      const lastIdResult = db.exec('SELECT last_insert_rowid() as id')
      backupId = lastIdResult && lastIdResult.length > 0 && lastIdResult[0].values.length > 0
        ? lastIdResult[0].values[0][0] : null

      try {
        // 2. إنشاء النسخة الاحتياطية فعلياً
        await databaseService.createBackup(fileName)

        // 3. التحقق من نجاح إنشاء الملف
        if (!fs.existsSync(backupPath)) {
          throw new Error('فشل في إنشاء ملف النسخة الاحتياطية')
        }

        const stats = fs.statSync(backupPath)
        if (stats.size === 0) {
          throw new Error('النسخة الاحتياطية فارغة')
        }

        // 4. تحديث حالة النسخة الاحتياطية إلى "مكتملة"
        const updateStmt = db.prepare(`UPDATE backups SET status = 'completed', file_size = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`)
        updateStmt.run(stats.size, backupId)

        return {
          success: true,
          message: 'تم إنشاء النسخة الاحتياطية بنجاح',
          data: {
            fileName: fileName,
            backupId: backupId,
            fileSize: stats.size,
            filePath: backupPath
          }
        }

      } catch (backupError) {
        // تحديث حالة النسخة الاحتياطية إلى "فاشلة" في حالة الخطأ
        const errorMsg = backupError instanceof Error ? backupError.message : String(backupError)
        if (backupId) {
          try {
            const failStmt = db.prepare(`UPDATE backups SET status = 'failed', error_message = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`)
            failStmt.run(errorMsg, backupId)
          } catch (updateError) {
            Logger.error('SystemHandlers', 'خطأ في تحديث حالة النسخة الاحتياطية:', updateError)
          }
        }
        throw backupError
      }

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error)
      Logger.error('SystemHandlers', 'خطأ في إنشاء النسخة الاحتياطية:', error)
      return { success: false, message: `حدث خطأ في إنشاء النسخة الاحتياطية: ${errorMsg}` }
    }
  })

  // استعادة نسخة احتياطية
  ipcMain.handle('restore-backup', async (_, backupId: number) => {
    try {
      const db = databaseService.getDatabase()

      // جلب معلومات النسخة الاحتياطية
      const backup = executeSelectOne(db, `SELECT * FROM backups WHERE id = ?`, [backupId])

      if (!backup) {
        return { success: false, message: 'النسخة الاحتياطية غير موجودة' }
      }

      // استعادة النسخة الاحتياطية
      await databaseService.restoreBackup(backup.file_path)

      return {
        success: true,
        message: 'تم استعادة النسخة الاحتياطية بنجاح'
      }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في استعادة النسخة الاحتياطية:', error)
      return { success: false, message: 'حدث خطأ في استعادة النسخة الاحتياطية' }
    }
  })

  // حذف نسخة احتياطية
  ipcMain.handle('delete-backup', async (_, backupId: number) => {
    try {
      const db = databaseService.getDatabase()

      // جلب معلومات النسخة الاحتياطية
      const backup = executeSelectOne(db, `SELECT * FROM backups WHERE id = ?`, [backupId])

      if (!backup) {
        return { success: false, message: 'النسخة الاحتياطية غير موجودة' }
      }

      // حذف النسخة الاحتياطية من قاعدة البيانات
      const deleteStmt = db.prepare(`DELETE FROM backups WHERE id = ?`)
      deleteStmt.run(backupId)

      // التحقق من نجاح الحذف
      const checkStmt = db.prepare(`SELECT COUNT(*) as count FROM backups WHERE id = ?`)
      const checkResult = checkStmt.get(backupId)
      const isDeleted = checkResult ? checkResult.count === 0 : false

      if (isDeleted) {
        // حذف الملف الفعلي (اختياري)
        try {
          if (backup.file_path && fs.existsSync(backup.file_path)) {
            fs.unlinkSync(backup.file_path)
          }
        } catch (fileError) {
          Logger.warn('SystemHandlers', 'تحذير: فشل في حذف ملف النسخة الاحتياطية:', fileError)
        }

        return {
          success: true,
          message: 'تم حذف النسخة الاحتياطية بنجاح'
        }
      } else {
        return { success: false, message: 'فشل في حذف النسخة الاحتياطية' }
      }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في حذف النسخة الاحتياطية:', error)
      return { success: false, message: 'حدث خطأ في حذف النسخة الاحتياطية' }
    }
  })

  // تحديث إعدادات النسخ الاحتياطي
  ipcMain.handle('update-backup-settings', async (_, settings: any) => {
    try {
      const db = databaseService.getDatabase()

      // التحقق من صحة المدخلات
      if (!settings || typeof settings !== 'object') {
        return { success: false, message: 'بيانات الإعدادات غير صحيحة' }
      }

      // تحديث كل إعداد على حدة
      for (const [key, value] of Object.entries(settings)) {
        const stringValue = value != null ? String(value).replace(/'/g, "''") : ''

        // التحقق من وجود الإعداد
        const existing = executeSelectOne(db, 'SELECT id FROM settings WHERE key = ?', ['backup_' + key])

        if (existing) {
          // تحديث الإعداد الموجود
          const updateStmt = db.prepare('UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?')
          updateStmt.run(stringValue, 'backup_' + key)
        } else {
          // إنشاء إعداد جديد
          const insertStmt = db.prepare('INSERT INTO settings (key, value, description, created_at, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)')
          insertStmt.run('backup_' + key, stringValue, 'إعداد النسخ الاحتياطي ' + key)
        }
      }

      // حفّ التغييرات
      databaseService.saveDatabase()

      return {
        success: true,
        message: 'تم تحديث إعدادات النسخ الاحتياطي بنجاح'
      }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في تحديث إعدادات النسخ الاحتياطي:', error)
      return { success: false, message: 'حدث خطأ في تحديث إعدادات النسخ الاحتياطي' }
    }
  })

  // العملات
  ipcMain.handle('get-currencies', async () => {
    try {
      // جلب العملات من قاعدة البيانات أو إرجاع قائمة افتراضية
      const currencies = [
        { id: 1, code: 'ILS', name: 'شيكل إسرائيلي', symbol: '₪', rate: 1.0, is_default: true },
        { id: 2, code: 'USD', name: 'دولار أمريكي', symbol: '$', rate: 3.7, is_default: false },
        { id: 3, code: 'EUR', name: 'يورو', symbol: '€', rate: 4.0, is_default: false },
        { id: 4, code: 'JOD', name: 'دينار أردني', symbol: 'د.أ', rate: 5.2, is_default: false }
      ]

      return { success: true, data: currencies }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في جلب العملات:', error)
      return { success: false, message: 'حدث خطأ في جلب العملات' }
    }
  })

  // تحديث سعر صرف العملة
  ipcMain.handle('update-currency-rate', async (_, currencyId: number, exchangeRate: number) => {
    try {
      // التحقق من صحة المعاملات
      if (!currencyId || typeof currencyId !== 'number' || currencyId <= 0) {
        return { success: false, message: 'معرف العملة غير صحيح' }
      }

      if (!exchangeRate || typeof exchangeRate !== 'number' || exchangeRate <= 0) {
        return { success: false, message: 'سعر الصرف غير صحيح' }
      }

      // حفّ سعر الصرف في الإعدادات
      const db = databaseService.getDatabase()
      const key = 'currency_rate_' + currencyId

      const existing = executeSelectOne(db, 'SELECT id FROM settings WHERE key = ?', [key])

      if (existing) {
        const updateStmt = db.prepare('UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?')
        updateStmt.run(exchangeRate, key)
      } else {
        const insertStmt = db.prepare('INSERT INTO settings (key, value, description, created_at, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)')
        insertStmt.run(key, exchangeRate, 'سعر صرف العملة ' + currencyId)
      }

      databaseService.saveDatabase()

      return { success: true, message: 'تم تحديث سعر الصرف بنجاح' }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في تحديث سعر الصرف:', error)
      return { success: false, message: 'حدث خطأ في تحديث سعر الصرف' }
    }
  })

  // طرق الدفع
  ipcMain.handle('get-payment-methods', async () => {
    try {
      const paymentMethods = [
        { id: 1, code: 'cash', name: 'نقدي', icon: 'wallet', is_active: true },
        { id: 2, code: 'check', name: 'شيك', icon: 'file-text', is_active: true },
        { id: 3, code: 'bank_transfer', name: 'تحويل بنكي', icon: 'bank', is_active: true },
        { id: 4, code: 'credit_card', name: 'بطاقة ائتمان', icon: 'credit-card', is_active: true },
        { id: 5, code: 'receipt_voucher', name: 'سند قبض', icon: 'receipt', is_active: true },
        { id: 6, code: 'payment_voucher', name: 'سند دفع', icon: 'payment', is_active: true }
      ]

      return { success: true, data: paymentMethods }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في جلب طرق الدفع:', error)
      return { success: false, message: 'حدث خطأ في جلب طرق الدفع' }
    }
  })

  // ===== معالجات نقل البيانات =====

  // فحص قواعد البيانات المتاحة
  ipcMain.handle('check-databases', async () => {
    try {
      const result = dataMigrationManager.checkDatabasesExistence()
      Logger.info('SystemHandlers', 'فحص قواعد البيانات:', result)
      return { success: true, data: result }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في فحص قواعد البيانات:', error)
      return { success: false, message: 'حدث خطأ في فحص قواعد البيانات' }
    }
  })

  // البحث عن ملفات قواعد البيانات
  ipcMain.handle('find-database-files', async () => {
    try {
      const files = dataMigrationManager.findDatabaseFiles()
      Logger.info('SystemHandlers', 'ملفات قواعد البيانات الموجودة:', files)
      return { success: true, data: files }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في البحث عن ملفات قواعد البيانات:', error)
      return { success: false, message: 'حدث خطأ في البحث عن ملفات قواعد البيانات' }
    }
  })



  // حفظ الإعدادات
  ipcMain.handle('save-settings', async (_, settings: Array<{ key: string; value: string; description?: string }>) => {
    try {
      const db = databaseService.getDatabase()

      settings.forEach(setting => {
        // التحقق من وجود الإعداد
        const existing = executeSelectOne(db, `SELECT id FROM settings WHERE key = ?`, [setting.key])

        if (!existing) {
          // إدراج إعداد جديد
          const insertStmt = db.prepare(`INSERT INTO settings (key, value, description, created_at, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`)
          insertStmt.run(setting.key, setting.value, setting.description || '')
        } else {
          // تحديث الإعداد الموجود
          const updateStmt = db.prepare(`UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?`)
          updateStmt.run(setting.value, setting.key)
        }
      })

      Logger.info('SystemHandlers', `تم حفظ ${settings.length} إعداد بنجاح`)
      return { success: true, message: 'تم حفظ الإعدادات بنجاح' }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في حفظ الإعدادات:', error)
      return { success: false, message: 'حدث خطأ في حفظ الإعدادات' }
    }
  })

  // جلب قوالب الطباعة
  ipcMain.handle('get-print-templates', async () => {
    try {
      const db = databaseService.getDatabase()
      const result = executeSelectQuery(db, 'SELECT * FROM print_templates WHERE is_active = 1 ORDER BY created_at DESC')

      const templates: any[] = []
      if (result && Array.isArray(result)) {
        result.forEach((template: any) => {
          // تحويل template_data من JSON
          if (template.template_data) {
            try {
              template.settings = JSON.parse(template.template_data)
            } catch {
              template.settings = {}
            }
          }

          templates.push(template)
        })
      }

      return { success: true, data: templates }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في جلب قوالب الطباعة:', error)
      return { success: false, message: 'حدث خطأ في جلب قوالب الطباعة' }
    }
  })

  // حفظ قالب طباعة
  ipcMain.handle('save-print-template', async (_, template: any) => {
    try {
      const db = databaseService.getDatabase()

      const templateData = JSON.stringify(template.settings || {})

      if (template.id && template.id.startsWith('template-')) {
        // قالب جديد
        const stmt = db.prepare(`
          INSERT INTO print_templates (id, name, description, type, category, is_default, is_custom, is_active, template_data, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `)
        stmt.run(
          template.id,
          template.name,
          template.description || '',
          template.type,
          template.category || 'custom',
          template.isDefault ? 1 : 0,
          1, // is_custom
          template.isActive ? 1 : 0,
          templateData
        )
      } else {
        // تحديث قالب موجود
        const stmt = db.prepare(`
          UPDATE print_templates
          SET name = ?, description = ?, type = ?, template_data = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `)
        stmt.run(
          template.name,
          template.description || '',
          template.type,
          templateData,
          template.id
        )
      }

      return { success: true, message: 'تم حفظ القالب بنجاح' }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في حفظ قالب الطباعة:', error)
      return { success: false, message: 'حدث خطأ في حفظ قالب الطباعة' }
    }
  })

  // حذف قالب طباعة
  ipcMain.handle('delete-print-template', async (_, templateId: string) => {
    try {
      const db = databaseService.getDatabase()

      // التحقق من أن القالب ليس افتراضي
      const checkResult = executeSelectOne(db, `SELECT is_default FROM print_templates WHERE id = ?`, [templateId])
      if (checkResult && checkResult.is_default === 1) {
        return { success: false, message: 'لا يمكن حذف القوالب الافتراضية' }
      }

      const stmt = db.prepare('DELETE FROM print_templates WHERE id = ? AND is_default = 0')
      stmt.run(templateId)

      return { success: true, message: 'تم حذف القالب بنجاح' }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في حذف قالب الطباعة:', error)
      return { success: false, message: 'حدث خطأ في حذف قالب الطباعة' }
    }
  })

  // جلب تقارير إقفال السنة المالية
  ipcMain.handle('getFiscalClosingReport', async (_, params: any) => {
    try {
      const { periodId, reportType, dateRange, includeDetails } = params

      // هنا يمكن إضافة منطق جلب البيانات من قاعدة البيانات
      // حالياً سنرجع بيانات تجريبية
      const mockData = {
        data: [
          {
            id: 1,
            description: 'إقفال حسابات الإيرادات',
            amount: 250000,
            date: new Date().toISOString(),
            type: reportType
          },
          {
            id: 2,
            description: 'إقفال حسابات المصروفات',
            amount: 180000,
            date: new Date().toISOString(),
            type: reportType
          }
        ],
        summary: {
          totalRevenue: 250000,
          totalExpenses: 180000,
          netProfit: 70000,
          periodId: periodId
        },
        columns: [
          { key: 'id', title: 'المعرف', align: 'center', width: 80 },
          { key: 'description', title: 'الوصف', align: 'right', width: 200 },
          { key: 'amount', title: 'المبلغ', align: 'center', format: 'currency', width: 120 },
          { key: 'date', title: 'التاريخ', align: 'center', format: 'date', width: 120 }
        ]
      }

      return { success: true, data: mockData }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في جلب تقرير إقفال السنة المالية:', error)
      return { success: false, message: 'حدث خطأ في جلب التقرير' }
    }
  })

  // نقل البيانات من المطور إلى الإنتاج
  ipcMain.handle('migrate-dev-to-prod', async () => {
    try {
      Logger.info('SystemHandlers', 'بدء نقل البيانات من المطور إلى الإنتاج')
      const result = await dataMigrationManager.migrateFromDevToProduction()
      Logger.info('SystemHandlers', 'نتيجة نقل البيانات:', result)
      return result
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في نقل البيانات:', error)
      return { success: false, message: 'حدث خطأ في نقل البيانات' }
    }
  })

  // نقل البيانات من الإنتاج إلى المطور
  ipcMain.handle('migrate-prod-to-dev', async () => {
    try {
      Logger.info('SystemHandlers', 'بدء نقل البيانات من الإنتاج إلى المطور')
      const result = await dataMigrationManager.migrateFromProductionToDev()
      Logger.info('SystemHandlers', 'نتيجة نقل البيانات:', result)
      return result
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في نقل البيانات:', error)
      return { success: false, message: 'حدث خطأ في نقل البيانات' }
    }
  })

  // تنّيف النسخ الاحتياطية القديمة
  ipcMain.handle('cleanup-old-backups', async (_, maxAge: number = 7) => {
    try {
      Logger.info('SystemHandlers', 'بدء تنّيف النسخ الاحتياطية القديمة')
      dataMigrationManager.cleanupOldBackups(maxAge)
      return { success: true, message: 'تم تنّيف النسخ الاحتياطية القديمة' }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في تنّيف النسخ الاحتياطية:', error)
      return { success: false, message: 'حدث خطأ في تنّيف النسخ الاحتياطية' }
    }
  })

  // قراءة ملف كـ base64 للشعارات والصور مع معالجة محسنة
  ipcMain.handle('read-file-as-base64', async (event, filePath: string) => {
    try {
      Logger.debug('SystemHandlers', `🔍 محاولة قراءة الملف: ${filePath}`)

      // إذا كان المسار يحتوي على base64 بالفعل، أرجعه كما هو
      if (filePath.startsWith('data:')) {
        Logger.info('SystemHandlers', 'الملف محول بالفعل إلى base64')
        return filePath.split(',')[1] // إرجاع الجزء base64 فقط
      }

      // محاولة المسارات المختلفة
      const possiblePaths = [
        filePath, // المسار الأصلي
        path.resolve(filePath), // المسار المطلق
        path.join(app.getPath('userData'), filePath), // مسار userData
        path.join(app.getPath('userData'), 'images', path.basename(filePath)), // مجلد الصور
        path.join(process.cwd(), filePath), // مجلد المشروع
        path.join(process.cwd(), 'images', path.basename(filePath)) // مجلد صور المشروع
      ]

      let actualPath = null
      for (const testPath of possiblePaths) {
        if (fs.existsSync(testPath)) {
          actualPath = testPath
          Logger.info('SystemHandlers', `✅ تم العثور على الملف في: ${testPath}`)
          break
        }
      }

      if (!actualPath) {
        Logger.warn('SystemHandlers', `❌ الملف غير موجود في أي من المسارات المحتملة: ${filePath}`)
        Logger.debug('SystemHandlers', 'المسارات المجربة:', possiblePaths)
        return null
      }

      // قراءة الملف وتحويله إلى base64
      const fileBuffer = fs.readFileSync(actualPath)
      const base64String = fileBuffer.toString('base64')

      Logger.info('SystemHandlers', `✅ تم تحميل الملف بنجاح: ${path.basename(actualPath)} (${fileBuffer.length} بايت)`)
      return base64String
    } catch (error) {
      Logger.error('SystemHandlers', `❌ خطأ في قراءة الملف ${filePath}:`, error)
      return null
    }
  })

  // حذف الإعدادات المكررة (company_logo_size و company_logo_position)
  ipcMain.handle('remove-duplicate-logo-settings', async () => {
    try {
      const db = databaseService.getDatabase()

      // حذف الإعدادات المكررة من معلومات الشركة
      const settingsToRemove = ['company_logo_size', 'company_logo_position']

      for (const setting of settingsToRemove) {
        const stmt = db.prepare('DELETE FROM settings WHERE key = ?')
        const result = stmt.run(setting)

        if (result.changes > 0) {
          Logger.info('SystemHandlers', `✅ تم حذف الإعداد المكرر: ${setting}`)
        } else {
          Logger.info('SystemHandlers', `ℹ️ الإعداد غير موجود: ${setting}`)
        }
      }

      // حفظ التغييرات
      databaseService.saveDatabase()

      return {
        success: true,
        message: 'تم حذف الإعدادات المكررة بنجاح',
        removedSettings: settingsToRemove
      }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في حذف الإعدادات المكررة:', error)
      return { success: false, message: 'حدث خطأ في حذف الإعدادات المكررة' }
    }
  })

  // ==================== Print Settings Handlers ====================
  // استخدام الخدمة المحسنة لإدارة إعدادات الطباعة

  // جلب إعدادات الطباعة المركزية
  ipcMain.handle('get-print-settings', async (_, userId?: number, documentType?: string) => {
    try {
      const printSettingsService = PrintSettingsService.getInstance()
      const result = await printSettingsService.getSettings(userId, documentType)

      if (result.success) {
        Logger.info('SystemHandlers', '✅ تم جلب إعدادات الطباعة بنجاح')
        return result.data
      } else {
        Logger.error('SystemHandlers', '❌ فشل في جلب إعدادات الطباعة:', result.error)
        throw new Error(result.error || 'فشل في جلب الإعدادات')
      }
    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في جلب إعدادات الطباعة:', error)
      throw error
    }
  })

  // حفظ إعدادات الطباعة المركزية
  ipcMain.handle('save-print-settings', async (_, settings: any, userId?: number, documentType?: string) => {
    try {
      const printSettingsService = PrintSettingsService.getInstance()
      const result = await printSettingsService.saveSettings(settings, userId, documentType)

      if (result.success) {
        Logger.info('SystemHandlers', '✅ تم حفظ إعدادات الطباعة بنجاح')
        return result
      } else {
        Logger.error('SystemHandlers', '❌ فشل في حفظ إعدادات الطباعة:', result.error)
        return result
      }

    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في حفظ إعدادات الطباعة:', error)
      return { success: false, message: 'حدث خطأ في حفظ إعدادات الطباعة' }
    }
  })

  // إعادة تعيين إعدادات الطباعة للافتراضية
  ipcMain.handle('reset-print-settings', async (_, userId?: number, documentType?: string) => {
    try {
      const printSettingsService = PrintSettingsService.getInstance()
      const result = await printSettingsService.resetSettings(userId, documentType)

      if (result.success) {
        Logger.info('SystemHandlers', '✅ تم إعادة تعيين إعدادات الطباعة للافتراضية')
        return result
      } else {
        Logger.error('SystemHandlers', '❌ فشل في إعادة تعيين إعدادات الطباعة:', result.error)
        return result
      }

    } catch (error) {
      Logger.error('SystemHandlers', 'خطأ في إعادة تعيين إعدادات الطباعة:', error)
      return { success: false, message: 'حدث خطأ في إعادة تعيين إعدادات الطباعة' }
    }
  })
}
