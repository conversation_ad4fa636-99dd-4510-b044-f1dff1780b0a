// تصدير جميع المعالجات
import { registerAuthHandlers, setAuthService } from './authHandlers'
import { registerUserHandlers, setUserService } from './userHandlers'
import { registerInventoryHandlers, setInventoryService } from './inventoryHandlers'
import { registerSalesHandlers, setSalesServices } from './salesHandlers'
import { registerPurchaseHandlers, setPurchaseServices } from './purchaseHandlers'
import { registerSystemHandlers, setServiceReferences } from './systemHandlers'
import { registerProductionHandlers, setProductionService } from './productionHandlers'
import { registerFinancialHandlers, setFinancialService } from './financialHandlers'
import { registerEmployeeHandlers, setEmployeeService } from './employeeHandlers'
import { registerNotificationHandlers, setNotificationService } from './notificationHandlers'
import { registerCommonHandlers, setCommonServices } from './commonHandlers'
import { registerUniversalInvoiceHandlers, setUniversalInvoiceService } from './universalInvoiceHandlers'
import { TemplateHandlers } from './TemplateHandlers'
import { registerFiscalPeriodHandlers } from './fiscalPeriodHandlers' // تحميل معالجات الإقفال المحاسبي
import { Logger } from '../utils/logger'

// تصدير الدوال
export {
  registerAuthHandlers,
  registerUserHandlers,
  registerInventoryHandlers,
  registerSalesHandlers,
  registerPurchaseHandlers,
  registerSystemHandlers,
  registerProductionHandlers,
  registerFinancialHandlers,
  registerEmployeeHandlers,
  registerNotificationHandlers,
  registerCommonHandlers,
  registerUniversalInvoiceHandlers,
  registerFiscalPeriodHandlers,
  TemplateHandlers
}

// متتبع التسجيل لتجنب التكرار
let handlersRegistered = false

// دالة تسجيل جميع المعالجات
export function registerAllHandlers() {
  if (handlersRegistered) {
    Logger.warn('HandlerRegistry', 'المعالجات مسجلة بالفعل، تخطي التسجيل المكرر')
    return
  }

  try {
    registerAuthHandlers()
    registerUserHandlers()
    registerInventoryHandlers()
    registerSalesHandlers()
    registerPurchaseHandlers()
    registerSystemHandlers()
    registerProductionHandlers()
    registerFinancialHandlers()
    registerEmployeeHandlers()
    registerNotificationHandlers()
    registerCommonHandlers()
    registerUniversalInvoiceHandlers()
    registerFiscalPeriodHandlers()
    TemplateHandlers.registerHandlers()

    handlersRegistered = true
    Logger.info('HandlerRegistry', 'تم تسجيل جميع المعالجات بنجاح')
  } catch (error) {
    Logger.error('HandlerRegistry', 'خطأ في تسجيل المعالجات:', error)
    throw error
  }
}

// دالة تعيين جميع الخدمات
export function setAllServices(services: any) {
  setAuthService(services.authService)
  setUserService(services.userService)
  setInventoryService(services.inventoryService)
  setSalesServices(services.salesService, services.customerService)
  setPurchaseServices(services.purchaseService, services.supplierService)
  setServiceReferences(services.databaseService, services.authService)
  setProductionService(services.productionService)
  setFinancialService(services.financialService)
  setEmployeeService(services.employeeService)
  setNotificationService(services.notificationService)
  setCommonServices(services.customerService, services.supplierService, services.codeGeneratorService)
  setUniversalInvoiceService(services.universalInvoiceService)
}
