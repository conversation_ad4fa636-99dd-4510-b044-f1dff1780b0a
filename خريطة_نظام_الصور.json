{"نظام_الصور": {"الوصف": "حصر شامل لجميع مكونات نظام الصور في التطبيق", "تاريخ_الإنشاء": "2025-09-30", "الحالة": "جاهز_للحذف", "المجلدات_الكاملة": {"العدد": 3, "القائمة": [{"المسار": "src/renderer/src/components/images/", "الوصف": "مكونات واجهة المستخدم للصور", "المحتويات": ["UnifiedImageManager.tsx", "EnhancedImageViewer.tsx", "index.ts"]}, {"المسار": "src/renderer/src/services/images/", "الوصف": "خدمات إدارة الصور", "المحتويات": ["ImageCoreService.ts", "ImageProcessingService.ts", "ImageCacheService.ts", "ImageValidationService.ts", "ImageStorageService.ts", "ImagePrintService.ts", "ImageErrorHandler.ts", "EnhancedImageValidator.ts", "ImagePerformanceOptimizer.ts", "test-integration.ts", "index.ts"]}, {"المسار": "src/renderer/src/hooks/images/", "الوصف": "React Hooks للصور", "المحتويات": ["useImageManager.ts", "index.ts"]}]}, "الملفات_الفردية": {"العدد": 23, "التصنيف": {"common": ["src/renderer/src/components/common/ImageGallery.tsx", "src/renderer/src/components/common/ImageService.ts", "src/renderer/src/components/common/ImageSettings.tsx", "src/renderer/src/components/common/ImageWithFallback.tsx", "src/renderer/src/components/common/CheckImageManager.tsx", "src/renderer/src/components/common/ImagePrintTest.tsx", "src/renderer/src/components/common/UniversalImagePrint.tsx", "src/renderer/src/components/common/UniversalImagePrintDemo.tsx", "src/renderer/src/components/common/InvoiceImagesPrintButton.tsx", "src/renderer/src/components/common/ProductionOrderImagesPrintButton.tsx", "src/renderer/src/components/common/CustomerImagesPrintButton.tsx", "src/renderer/src/components/common/SmartImagePrintButton.tsx", "src/renderer/src/components/common/README_IMAGE_PRINT.md"], "أخرى": ["src/renderer/src/components/examples/ImageSystemExample.tsx", "src/renderer/src/components/debug/ImageDebugger.tsx", "src/renderer/src/components/inventory/EnhancedItemImagePrint.tsx", "src/renderer/src/components/production/furniture/FurnitureImageService.ts", "src/renderer/src/services/UniversalImageService.ts", "src/renderer/src/utils/imageUtils.ts", "src/main/handlers/imageHandlers.ts"], "اختبار": ["src/test/image-print-system-test.ts", "src/test/run-image-print-test.ts"], "توثيق": ["docs/IMAGE_SYSTEM_DATABASE_INTEGRATION_COMPLETE.md"]}}, "جداول_قاعدة_البيانات": {"العدد": 5, "القائمة": [{"الاسم": "unified_images", "الوصف": "الجدول الموحد لجميع الصور", "الأعمدة_الرئيسية": ["id", "name", "path", "category", "context_type", "context_id", "is_primary"]}, {"الاسم": "item_images", "الوصف": "صور الأصناف", "الأعمدة_الرئيسية": ["id", "item_id", "image_path", "is_primary"]}, {"الاسم": "production_order_images", "الوصف": "صور أوامر الإنتاج", "الأعمدة_الرئيسية": ["id", "order_id", "image_path", "category"]}, {"الاسم": "customer_images", "الوصف": "صور العملاء", "الأعمدة_الرئيسية": ["id", "customer_id", "image_path"]}, {"الاسم": "check_images", "الوصف": "صور الشيكات", "الأعمدة_الرئيسية": ["id", "check_id", "image_path", "image_side"]}]}, "المكونات_الرئيسية": {"مكونات_العرض": ["UnifiedImageManager", "EnhancedImageViewer", "ImageGallery", "ImageWithFallback", "CheckImageManager"], "مكونات_الطباعة": ["UniversalImagePrint", "InvoiceImagesPrintButton", "ProductionOrderImagesPrintButton", "CustomerImagesPrintButton", "SmartImagePrintButton", "EnhancedItemImagePrint"], "مكونات_الإعدادات": ["ImageSettings"], "مكونات_الاختبار": ["ImageSystemExample", "UniversalImagePrintDemo", "ImagePrintTest", "ImageDebugger"]}, "الخدمات": {"خدمات_النواة": ["ImageCoreService", "ImageProcessingService", "ImageStorageService", "ImageCacheService", "ImageValidationService", "ImagePrintService"], "خدمات_محسنة": ["EnhancedImageValidator", "ImageErrorHandler", "ImagePerformanceOptimizer"], "خدمات_متخصصة": ["FurnitureImageService", "UniversalImageService", "ImageService"]}, "الـHooks": ["useImageManager"], "معالجات_IPC": ["database-query", "save-image-file", "delete-image-file", "create-unified-images-table", "create-image-indexes", "create-image-directories", "read-image-as-base64"], "الواجهات_والأنواع": {"في_global.d.ts": ["ItemImage", "CheckImage"], "في_ImageCoreService.ts": ["UnifiedImageData", "ImageCategory", "ImageContextType", "ImageUploadOptions", "ImageOperationResult"], "في_ImageService.ts": ["ImageData", "ImageUploadOptions"]}, "الأزرار": {"أزرار_الرفع_والإدارة": ["رفع صورة", "رفع صور متعددة", "حذ<PERSON> الصورة", "تعيين كصورة رئيسية", "م<PERSON><PERSON>"], "أزرار_العرض": ["فتح العارض", "معاينة", "<PERSON>ر<PERSON> مفرد", "شبكة"], "أزرار_الطباعة": ["طباعة صور الفواتير", "طباعة صور أوامر الإنتاج", "طباعة صور العملاء", "طباعة ذكية شاملة", "طباعة صور الأصناف"], "أزرار_التحكم": ["تكبير", "تصغير", "تدوير", "تحميل", "ملء الشاشة"]}, "التبويبات": ["المدير الموحد", "العمليات المتقدمة", "التكامل مع النظام"], "مجلدات_نظام_الملفات": {"المسار_الرئيسي": "userData/images/", "المجلدات_الفرعية": ["inventory", "production", "checks", "customers", "thumbnails", "temp"]}, "الاستخدامات_في_المكونات_الأخرى": [{"الملف": "ItemManagement.tsx", "الاستخدام": "Modal لإدارة صور الصنف"}, {"الملف": "Dashboard.tsx", "الاستخدام": "أقسام طباعة الصور"}, {"الملف": "ProductionRecipesManagement.tsx", "الاستخدام": "صور في الوصفات"}], "الملفات_المستثناة": {"الوصف": "ملفات الشعار - لا تحذف", "القائمة": ["AdvancedLogoManager.tsx", "AnimatedLogo.tsx", "LogoManager.tsx", "logo.svg", "default-logo.svg"]}, "الإحصائيات": {"عدد_الملفات": 40, "عدد_الخدمات": 12, "عدد_المكونات": 20, "عدد_الجداول": 5, "عدد_الأزرار": 20, "عدد_الواجهات": 15}, "خطوات_الحذف_الموصى_بها": ["1. عمل نسخة احتياطية", "2. إنشاء فرع جديد", "3. <PERSON><PERSON><PERSON> المجلدات الثلاثة الكاملة", "4. حذ<PERSON> الملفات الفردية", "5. <PERSON><PERSON><PERSON> ج<PERSON><PERSON><PERSON><PERSON> قاعدة البيانات", "6. <PERSON><PERSON><PERSON> م<PERSON><PERSON><PERSON> الصور من userData", "7. تعديل الملفات التي تستورد المكونات", "8. اخ<PERSON><PERSON><PERSON><PERSON> التطبيق", "9. الت<PERSON>ك<PERSON> من عدم وجود أخطاء"], "تقدير_الوقت": {"حذف_الملفات": "30 دقيقة", "تعديل_الملفات": "1-2 ساعة", "حذف_قاعدة_البيانات": "15 دقيقة", "الاختبار": "1-2 ساعة", "الإجمالي": "3-5 ساعات"}}}