import React, { useState, useEffect, useRef } from 'react'
import { Image, Typography } from 'antd'
import { PictureOutlined } from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'

const { Text } = Typography

interface ImageWithFallbackProps {
  src?: string | null
  alt?: string
  style?: React.CSSProperties
  fallbackText?: string
  preview?: boolean
  onClick?: () => void
  className?: string
}

const ImageWithFallback: React.FC<ImageWithFallbackProps> = ({
  src,
  alt = 'صورة',
  style,
  fallbackText = 'فشل في تحميل الصورة',
  preview = false,
  onClick,
  className
}) => {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // إعادة تعيين حالة الخطأ عند تغيير المصدر
  useEffect(() => {
    if (src) {
      setImageError(false)
      setImageLoading(true)

      // إلغاء timeout السابق إن وجد
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      // إضافة timeout لمنع التجمد (10 ثوان)
      timeoutRef.current = setTimeout(() => {
        Logger.warn('ImageWithFallback', 'انتهت مهلة تحميل الصورة:', src?.substring(0, 50))
        setImageError(true)
        setImageLoading(false)
      }, 10000)
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [src])

  // التحقق من صحة مصدر الصورة
  const isValidImageSrc = (src: string | null | undefined): boolean => {
    if (!src || src.trim() === '') return false

    // التحقق من base64
    if (src.startsWith('data:image/')) {
      // التحقق من أن البيانات ليست معطلة أو اختبارية
      if (src.includes('INVALID') || src.includes('invalid-base64-data')) {
        return false
      }
      return true
    }

    // التحقق من URL
    try {
      new URL(src)
      return true
    } catch {
      return false
    }
  }

  // إذا لم يكن هناك مصدر صحيح للصورة أو حدث خطأ، عرض الـ fallback
  if (!isValidImageSrc(src) || imageError) {
    return (
      <div
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f5f5f5',
          color: '#999',
          flexDirection: 'column',
          cursor: onClick ? 'pointer' : 'default',
          ...style
        }}
        onClick={onClick}
        className={className}
      >
        <PictureOutlined style={{ fontSize: 48, marginBottom: 8 }} />
        <Text type="secondary" style={{ textAlign: 'center', fontSize: 12 }}>
          {fallbackText}
        </Text>
      </div>
    )
  }

  return (
    <div style={{ position: 'relative', width: '100%', height: '100%' }}>
      {imageLoading && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#f5f5f5',
            zIndex: 1
          }}
        >
          <PictureOutlined style={{ fontSize: 24, color: '#ccc' }} />
        </div>
      )}
      <Image
        src={src}
        alt={alt}
        style={style}
        preview={preview}
        onClick={onClick}
        className={className}
        onLoad={() => {
          // إلغاء timeout عند نجاح التحميل
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
          }
          Logger.info('ImageWithFallback', 'تم تحميل الصورة بنجاح:', src?.substring(0, 50))
          setImageLoading(false)
        }}
        onError={(e) => {
          // إلغاء timeout عند حدوث خطأ
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
          }
          // تجنب تسجيل الأخطاء للصور الاختبارية المقصودة أو الصور الفارغة
          if (!src?.includes('INVALID') &&
              !src?.includes('/9j/4AAQSkZJRgABAQAAAQABAA') &&
              src &&
              src.trim() !== '') {
            Logger.error('ImageWithFallback', 'خطأ في تحميل الصورة:', { src: src?.substring(0, 100), error: e })
          } else {
            Logger.debug('ImageWithFallback', 'صورة اختبار أو فارغة (متوقع):', src?.substring(0, 50))
          }
          setImageError(true)
          setImageLoading(false)
        }}
        fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
      />
    </div>
  )
}

export default ImageWithFallback
