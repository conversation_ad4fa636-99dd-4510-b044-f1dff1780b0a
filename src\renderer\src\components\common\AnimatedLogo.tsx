import React from 'react'
import styled from 'styled-components'
import { designSystem, animations } from '../../styles/designSystem'

interface AnimatedLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  showText?: boolean
  showSubtitle?: boolean
  showDeveloper?: boolean
  variant?: 'default' | 'minimal' | 'full'
  className?: string
  style?: React.CSSProperties
}

const LogoContainer = styled.div<{ $size: string; $variant: string }>`
  text-align: center;
  
  ${animations.fadeIn}
  ${animations.logoFloat}
  ${animations.pulse}
  
  .company-logo {
    width: ${props => {
      switch (props.$size) {
        case 'sm': return '60px'
        case 'md': return '80px'
        case 'lg': return '100px'
        case 'xl': return '120px'
        default: return '100px'
      }
    }};
    height: ${props => {
      switch (props.$size) {
        case 'sm': return '60px'
        case 'md': return '80px'
        case 'lg': return '100px'
        case 'xl': return '120px'
        default: return '100px'
      }
    }};
    margin: 0 auto ${props => props.$variant === 'minimal' ? '0' : '20px'};
    background: ${designSystem.colors.gradients.primary};
    border-radius: ${designSystem.borderRadius.full};
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: ${props => {
      switch (props.$size) {
        case 'sm': return '20px'
        case 'md': return '28px'
        case 'lg': return '36px'
        case 'xl': return '44px'
        default: return '36px'
      }
    }};
    color: ${designSystem.colors.text.light};
    box-shadow: ${designSystem.shadows.lg};
    font-weight: ${designSystem.fontWeight.black};
    position: relative;
    animation: logoFloat 3s ease-in-out infinite;
    transition: ${designSystem.transitions.smooth};
    cursor: pointer;

    /* تأثير الهالة */
    &::before {
      content: '';
      position: absolute;
      top: -5px;
      left: -5px;
      right: -5px;
      bottom: -5px;
      background: ${designSystem.colors.gradients.primary};
      border-radius: ${designSystem.borderRadius.full};
      z-index: -1;
      opacity: 0.3;
      animation: pulse 2s ease-in-out infinite;
    }

    &:hover {
      transform: translateY(-5px) scale(1.05);
      box-shadow: ${designSystem.shadows.xl};
      
      &::before {
        opacity: 0.6;
        transform: scale(1.2);
      }
    }

    &:active {
      transform: translateY(-2px) scale(0.98);
    }
  }

  .logo-text {
    font-size: ${props => {
      switch (props.$size) {
        case 'sm': return designSystem.fontSize.lg
        case 'md': return designSystem.fontSize['2xl']
        case 'lg': return designSystem.fontSize['3xl']
        case 'xl': return designSystem.fontSize['4xl']
        default: return designSystem.fontSize['3xl']
      }
    }};
    font-weight: ${designSystem.fontWeight.black};
    background: ${designSystem.colors.gradients.text};
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: ${props => props.$variant === 'minimal' ? '0' : '8px'};
    font-family: ${designSystem.fonts.primary};
    letter-spacing: 2px;
  }

  .logo-subtitle {
    font-size: ${props => {
      switch (props.$size) {
        case 'sm': return designSystem.fontSize.sm
        case 'md': return designSystem.fontSize.base
        case 'lg': return designSystem.fontSize.md
        case 'xl': return designSystem.fontSize.lg
        default: return designSystem.fontSize.md
      }
    }};
    color: ${designSystem.colors.text.secondary};
    font-weight: ${designSystem.fontWeight.medium};
    margin-bottom: ${props => props.$variant === 'full' ? '12px' : '0'};
    font-family: ${designSystem.fonts.primary};
  }

  .developer-info {
    padding: ${props => {
      switch (props.$size) {
        case 'sm': return '6px 8px'
        case 'md': return '8px 12px'
        case 'lg': return '8px 12px'
        case 'xl': return '10px 16px'
        default: return '8px 12px'
      }
    }};
    background: rgba(0, 120, 212, 0.05);
    border-radius: ${designSystem.borderRadius.sm};
    border: 1px solid rgba(0, 120, 212, 0.1);
    margin-top: ${props => props.$variant === 'full' ? '12px' : '8px'};
    
    .developer-title {
      color: #0078D4;
      font-weight: ${designSystem.fontWeight.semibold};
      font-size: ${props => {
        switch (props.$size) {
          case 'sm': return designSystem.fontSize.xs
          case 'md': return designSystem.fontSize.sm
          case 'lg': return designSystem.fontSize.sm
          case 'xl': return designSystem.fontSize.base
          default: return designSystem.fontSize.sm
        }
      }};
      margin-bottom: 4px;
    }
    
    .developer-contact {
      color: ${designSystem.colors.text.secondary};
      font-size: ${props => {
        switch (props.$size) {
          case 'sm': return '10px'
          case 'md': return designSystem.fontSize.xs
          case 'lg': return designSystem.fontSize.xs
          case 'xl': return designSystem.fontSize.sm
          default: return designSystem.fontSize.xs
        }
      }};
      line-height: 1.4;
    }
  }
`

const AnimatedLogo: React.FC<AnimatedLogoProps> = ({
  size = 'md',
  showText = true,
  showSubtitle = true,
  showDeveloper = false,
  variant = 'default',
  className,
  style
}) => {
  return (
    <LogoContainer 
      $size={size} 
      $variant={variant}
      className={className}
      style={style}
    >
      <div className="company-logo">
        ZET
      </div>
      
      {showText && variant !== 'minimal' && (
        <div className="logo-text">
          ZET.IA
        </div>
      )}
      
      {showSubtitle && variant !== 'minimal' && (
        <div className="logo-subtitle">
          نظام إدارة شامل للمحاسبة والإنتاج
        </div>
      )}
      
      {showDeveloper && variant === 'full' && (
        <div className="developer-info">
          <div className="developer-title">المطور: FARESNAWAF</div>
          <div className="developer-contact">
            <EMAIL> | 0569329925
          </div>
        </div>
      )}
    </LogoContainer>
  )
}

export default AnimatedLogo
