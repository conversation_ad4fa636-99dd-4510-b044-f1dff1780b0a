import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Tabs,
  Button,
  Space,
  Typography,
  Alert,
  Progress,
  List,
  Tag,
  Tooltip
} from 'antd'
import {
  BarChartOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  UserOutlined,
  InboxOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../../utils/logger'
import dayjs from 'dayjs'
import ProductionOrdersManagement from './ProductionOrdersManagement'
import LaborTimeTrackingComponent from './LaborTimeTracking'
import MaterialIssueOrdersComponent from './MaterialIssueOrders'
import ProductionRecipesManagement from './ProductionRecipesManagement'
import ProductionCostAnalysis from './ProductionCostAnalysis'
import ProductionSettingsComponent from './ProductionSettings'

const { Title, Text } = Typography
const { TabPane } = Tabs

interface DashboardStats {
  production: {
    totalOrders: number
    inProgressOrders: number
    completedOrders: number
    totalValue: number
  }
  materials: {
    totalIssueOrders: number
    pendingOrders: number
    issuedOrders: number
    totalValue: number
  }
  labor: {
    activeTrackings: number
    totalHours: number
    totalCost: number
    averageHourlyRate: number
  }
}

interface RecentActivity {
  id: number
  type: 'production' | 'material_issue' | 'labor'
  title: string
  description: string
  status: string
  timestamp: string
  value?: number
}

// إضافة CSS للتأثيرات
const cardHoverStyle = `
  .shortcut-card:hover {
    transform: translateY(-4px) !important;
    box-shadow: 0 8px 24px rgba(0,0,0,0.15) !important;
  }
  .shortcut-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
`

const ProductionDashboard: React.FC = () => {
  console.log('🚀 ProductionDashboard المحسن يتم تحميله!')

  const [stats, setStats] = useState<DashboardStats>({
    production: { totalOrders: 0, inProgressOrders: 0, completedOrders: 0, totalValue: 0 },
    materials: { totalIssueOrders: 0, pendingOrders: 0, issuedOrders: 0, totalValue: 0 },
    labor: { activeTrackings: 0, totalHours: 0, totalCost: 0, averageHourlyRate: 0 }
  })
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('dashboard')

  useEffect(() => {
    if (activeTab === 'dashboard') {
      loadDashboardData()
    }
  }, [activeTab])

  const loadDashboardData = async () => {
    setLoading(true)
    try {
      await Promise.all([
        loadProductionStats(),
        loadMaterialStats(),
        loadLaborStats(),
        loadRecentActivities()
      ])
    } catch (error) {
      Logger.error('ProductionDashboard', 'خطأ في تحميل بيانات لوحة التحكم:', error)
    }
    setLoading(false)
  }

  const loadProductionStats = async () => {
    try {
      if (!window.electronAPI) return

      const result = await window.electronAPI.getProductionOrders()
      if (result.success) {
        const orders = result.data
        const totalOrders = orders.length
        const inProgressOrders = orders.filter((o: any) => o.status === 'in_progress').length
        const completedOrders = orders.filter((o: any) => o.status === 'completed').length
        const totalValue = orders.reduce((sum: number, order: any) => 
          sum + (order.actual_cost || order.estimated_cost || 0), 0)

        setStats(prev => ({
          ...prev,
          production: { totalOrders, inProgressOrders, completedOrders, totalValue }
        }))
      }
    } catch (error) {
      Logger.error('ProductionDashboard', 'خطأ في تحميل إحصائيات الإنتاج:', error)
    }
  }

  const loadMaterialStats = async () => {
    try {
      if (!window.electronAPI) return

      const result = await window.electronAPI.getMaterialIssueOrders()
      if (result.success) {
        const orders = result.data
        const totalIssueOrders = orders.length
        const pendingOrders = orders.filter((o: any) => o.status === 'pending').length
        const issuedOrders = orders.filter((o: any) => o.status === 'issued').length
        const totalValue = orders.reduce((sum: number, order: any) => {
          if (order.items) {
            return sum + order.items.reduce((itemSum: number, item: any) => itemSum + item.total_cost, 0)
          }
          return sum
        }, 0)

        setStats(prev => ({
          ...prev,
          materials: { totalIssueOrders, pendingOrders, issuedOrders, totalValue }
        }))
      }
    } catch (error) {
      Logger.error('ProductionDashboard', 'خطأ في تحميل إحصائيات المواد:', error)
    }
  }

  const loadLaborStats = async () => {
    try {
      if (!window.electronAPI) return

      const result = await window.electronAPI.getLaborTimeTracking()
      if (result.success) {
        const trackings = result.data
        const activeTrackings = trackings.filter((t: any) => t.status === 'active').length
        const completedTrackings = trackings.filter((t: any) => t.status === 'completed')
        const totalHours = completedTrackings.reduce((sum: number, t: any) => sum + (t.actual_hours || 0), 0)
        const totalCost = completedTrackings.reduce((sum: number, t: any) => sum + (t.labor_cost || 0), 0)
        const averageHourlyRate = completedTrackings.length > 0 
          ? completedTrackings.reduce((sum: number, t: any) => sum + t.hourly_rate, 0) / completedTrackings.length 
          : 0

        setStats(prev => ({
          ...prev,
          labor: { 
            activeTrackings, 
            totalHours: Math.round(totalHours * 100) / 100,
            totalCost: Math.round(totalCost * 100) / 100,
            averageHourlyRate: Math.round(averageHourlyRate * 100) / 100
          }
        }))
      }
    } catch (error) {
      Logger.error('ProductionDashboard', 'خطأ في تحميل إحصائيات العمالة:', error)
    }
  }

  const loadRecentActivities = async () => {
    try {
      // محاكاة الأنشطة الحديثة - يمكن تطويرها لاحقاً
      const activities: RecentActivity[] = [
        {
          id: 1,
          type: 'production',
          title: 'تم إكمال أمر إنتاج PO-2024-001',
          description: 'إنتاج 50 وحدة من كرسي خشبي',
          status: 'completed',
          timestamp: dayjs().subtract(1, 'hour').toISOString(),
          value: 2500
        },
        {
          id: 2,
          type: 'material_issue',
          title: 'تم صرف مواد MI-2024-015',
          description: 'صرف خشب وبراغي لأمر الإنتاج',
          status: 'issued',
          timestamp: dayjs().subtract(2, 'hours').toISOString(),
          value: 800
        },
        {
          id: 3,
          type: 'labor',
          title: 'بدء تسجيل ساعات عمل',
          description: 'أحمد محمد - قسم النجارة',
          status: 'active',
          timestamp: dayjs().subtract(3, 'hours').toISOString()
        }
      ]
      setRecentActivities(activities)
    } catch (error) {
      Logger.error('ProductionDashboard', 'خطأ في تحميل الأنشطة الحديثة:', error)
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'production': return <FileTextOutlined style={{ color: '#1890ff' }} />
      case 'material_issue': return <InboxOutlined style={{ color: '#52c41a' }} />
      case 'labor': return <UserOutlined style={{ color: '#722ed1' }} />
      default: return <ExclamationCircleOutlined />
    }
  }

  const getActivityStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success'
      case 'active': return 'processing'
      case 'issued': return 'success'
      default: return 'default'
    }
  }

  const renderDashboard = () => (
    <div>
      <style>{cardHoverStyle}</style>
      {/* أزرار الاختصارات السريعة */}
      <div style={{ marginBottom: '32px' }}>
        <Title level={4} style={{ marginBottom: '24px' }}>الاختصارات السريعة</Title>

        {/* مجموعة إدارة الإنتاج */}
        <div style={{
          marginBottom: '48px',
          padding: '24px',
          backgroundColor: '#f8f9fa',
          borderRadius: '16px',
          border: '1px solid #e8e8e8'
        }}>
          <Title level={5} style={{ marginBottom: '24px', color: '#1890ff', textAlign: 'center' }}>
            <FileTextOutlined /> إدارة الإنتاج
          </Title>
          <Row gutter={[32, 32]} justify="center">
            <Col xs={12} sm={8} md={6} lg={6} xl={4}>
              <Card
                hoverable
                className="shortcut-card"
                style={{
                  textAlign: 'center',
                  height: '140px',
                  width: '140px',
                  margin: '0 auto',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  borderRadius: '16px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                }}
                bodyStyle={{ padding: '16px' }}
                onClick={() => setActiveTab('orders')}
              >
                <FileTextOutlined style={{ fontSize: '36px', color: '#1890ff', marginBottom: '12px' }} />
                <Text strong style={{ fontSize: '13px' }}>أوامر الإنتاج</Text>
              </Card>
            </Col>
            <Col xs={12} sm={8} md={6} lg={6} xl={4}>
              <Card
                hoverable
                className="shortcut-card"
                style={{
                  textAlign: 'center',
                  height: '140px',
                  width: '140px',
                  margin: '0 auto',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  borderRadius: '16px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                }}
                bodyStyle={{ padding: '16px' }}
                onClick={() => setActiveTab('recipes')}
              >
                <BarChartOutlined style={{ fontSize: '36px', color: '#52c41a', marginBottom: '12px' }} />
                <Text strong style={{ fontSize: '13px' }}>وصفات الإنتاج</Text>
              </Card>
            </Col>
            <Col xs={12} sm={8} md={6} lg={6} xl={4}>
              <Card
                hoverable
                className="shortcut-card"
                style={{
                  textAlign: 'center',
                  height: '140px',
                  width: '140px',
                  margin: '0 auto',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  borderRadius: '16px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                }}
                bodyStyle={{ padding: '16px' }}
                onClick={() => setActiveTab('cost-analysis')}
              >
                <DollarOutlined style={{ fontSize: '36px', color: '#722ed1', marginBottom: '12px' }} />
                <Text strong style={{ fontSize: '13px' }}>تحليل التكاليف</Text>
              </Card>
            </Col>
          </Row>
        </div>

        {/* مجموعة إدارة المواد والعمالة */}
        <div style={{
          marginBottom: '48px',
          padding: '24px',
          backgroundColor: '#f0f9f0',
          borderRadius: '16px',
          border: '1px solid #d9f7be'
        }}>
          <Title level={5} style={{ marginBottom: '24px', color: '#52c41a', textAlign: 'center' }}>
            <InboxOutlined /> إدارة المواد والعمالة
          </Title>
          <Row gutter={[32, 32]} justify="center">
            <Col xs={12} sm={8} md={6} lg={6} xl={4}>
              <Card
                hoverable
                className="shortcut-card"
                style={{
                  textAlign: 'center',
                  height: '140px',
                  width: '140px',
                  margin: '0 auto',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  borderRadius: '16px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                }}
                bodyStyle={{ padding: '16px' }}
                onClick={() => setActiveTab('materials')}
              >
                <InboxOutlined style={{ fontSize: '36px', color: '#fa8c16', marginBottom: '12px' }} />
                <Text strong style={{ fontSize: '13px' }}>أوامر صرف المواد</Text>
              </Card>
            </Col>
            <Col xs={12} sm={8} md={6} lg={6} xl={4}>
              <Card
                hoverable
                className="shortcut-card"
                style={{
                  textAlign: 'center',
                  height: '140px',
                  width: '140px',
                  margin: '0 auto',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  borderRadius: '16px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                }}
                bodyStyle={{ padding: '16px' }}
                onClick={() => setActiveTab('labor')}
              >
                <ClockCircleOutlined style={{ fontSize: '36px', color: '#13c2c2', marginBottom: '12px' }} />
                <Text strong style={{ fontSize: '13px' }}>تتبع ساعات العمل</Text>
              </Card>
            </Col>
            <Col xs={12} sm={8} md={6} lg={6} xl={4}>
              <Card
                hoverable
                className="shortcut-card"
                style={{
                  textAlign: 'center',
                  height: '140px',
                  width: '140px',
                  margin: '0 auto',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  borderRadius: '16px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                }}
                bodyStyle={{ padding: '16px' }}
                onClick={() => setActiveTab('settings')}
              >
                <UserOutlined style={{ fontSize: '36px', color: '#eb2f96', marginBottom: '12px' }} />
                <Text strong style={{ fontSize: '13px' }}>الإعدادات</Text>
              </Card>
            </Col>
          </Row>
        </div>
      </div>

      {/* الإحصائيات الرئيسية */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Title level={4}>إحصائيات الإنتاج</Title>
        </Col>
        
        {/* إحصائيات أوامر الإنتاج */}
        <Col span={6}>
          <Card>
            <Statistic
              title="أوامر الإنتاج"
              value={stats.production.totalOrders}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="قيد التنفيذ"
              value={stats.production.inProgressOrders}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="مكتمل"
              value={stats.production.completedOrders}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="قيمة الإنتاج"
              value={stats.production.totalValue}
              precision={2}
              prefix="₪"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>

        {/* إحصائيات المواد */}
        <Col span={24}>
          <Title level={4}>إحصائيات المواد</Title>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="أوامر الصرف"
              value={stats.materials.totalIssueOrders}
              prefix={<InboxOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="في الانتظار"
              value={stats.materials.pendingOrders}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="مصروف"
              value={stats.materials.issuedOrders}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="قيمة المواد"
              value={stats.materials.totalValue}
              precision={2}
              prefix="₪"
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>

        {/* إحصائيات العمالة */}
        <Col span={24}>
          <Title level={4}>إحصائيات العمالة</Title>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="تسجيلات نشطة"
              value={stats.labor.activeTrackings}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الساعات"
              value={stats.labor.totalHours}
              precision={2}
              suffix="ساعة"
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="تكلفة العمالة"
              value={stats.labor.totalCost}
              precision={2}
              prefix="₪"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="متوسط المعدل"
              value={stats.labor.averageHourlyRate}
              precision={2}
              suffix="/ساعة"
              prefix="₪"
            />
          </Card>
        </Col>
      </Row>

      {/* الأنشطة الحديثة */}
      <Row gutter={16}>
        <Col span={24}>
          <Card title="الأنشطة الحديثة" extra={
            <Button onClick={loadDashboardData} loading={loading}>
              تحديث
            </Button>
          }>
            <List
              dataSource={recentActivities}
              renderItem={(activity) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={getActivityIcon(activity.type)}
                    title={
                      <Space>
                        {activity.title}
                        <Tag color={getActivityStatusColor(activity.status)}>
                          {activity.status}
                        </Tag>
                      </Space>
                    }
                    description={
                      <div>
                        <div>{activity.description}</div>
                        <Text type="secondary">
                          {dayjs(activity.timestamp).format('YYYY-MM-DD HH:mm')}
                        </Text>
                        {activity.value && (
                          <Text strong style={{ marginLeft: '8px' }}>
                            ₪{activity.value.toFixed(2)}
                          </Text>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <BarChartOutlined /> لوحة تحكم الإنتاج المحسنة ✅
        </Title>
        <Text type="secondary">
          إدارة شاملة لعمليات الإنتاج والتكاليف - النسخة المحسنة
        </Text>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="لوحة التحكم" key="dashboard">
          {renderDashboard()}
        </TabPane>
        <TabPane tab="أوامر الإنتاج" key="orders">
          <ProductionOrdersManagement />
        </TabPane>
        <TabPane tab="وصفات الإنتاج" key="recipes">
          <ProductionRecipesManagement />
        </TabPane>
        <TabPane tab="أوامر صرف المواد" key="materials">
          <MaterialIssueOrdersComponent />
        </TabPane>
        <TabPane tab="تتبع ساعات العمل" key="labor">
          <LaborTimeTrackingComponent />
        </TabPane>
        <TabPane tab="تحليل التكاليف" key="cost-analysis">
          <ProductionCostAnalysis />
        </TabPane>
        <TabPane tab="الإعدادات" key="settings">
          <ProductionSettingsComponent />
        </TabPane>
      </Tabs>
    </div>
  )
}

export default ProductionDashboard
