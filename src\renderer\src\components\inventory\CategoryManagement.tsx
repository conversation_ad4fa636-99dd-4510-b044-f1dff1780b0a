import React, { useState, useEffect } from 'react'
import {
  Card, Tree, Button, Modal, Form, Input, Select, Switch, Space,
  Popconfirm, Typography, Row, Col, Statistic, Tag, Tooltip, App,
  Result
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  PlusOutlined, EditOutlined, DeleteOutlined, FolderOutlined,
  FolderOpenOutlined, AppstoreOutlined, BranchesOutlined,
  PrinterOutlined, FileExcelOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { Category, ApiResponse } from '../../types/global'
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig'
import * as XLSX from 'xlsx'

const { Title, Text } = Typography
const { DirectoryTree } = Tree
const { Option } = Select

const StyledCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
`



const TreeContainer = styled.div`
  .ant-tree {
    background: #fafafa;
    border-radius: 6px;
    padding: 16px;
  }
  
  .ant-tree-node-content-wrapper {
    padding: 4px 8px;
    border-radius: 4px;
  }
  
  .ant-tree-node-content-wrapper:hover {
    background-color: #e6f7ff;
  }
  
  .ant-tree-node-selected {
    background-color: #bae7ff !important;
  }
`

interface CategoryManagementProps {
  onBack?: () => void
}

const CategoryManagement: React.FC<CategoryManagementProps> = ({ onBack: _onBack }) => {
  Logger.info('CategoryManagement', '🚀 CategoryManagement component loaded - START')
  Logger.info('CategoryManagement', '🔍 Props received:', { onBack: !!_onBack })

  const { message } = App.useApp()
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [form] = Form.useForm()

  Logger.info('CategoryManagement', '🔍 State initialized:', {
    categoriesLength: categories.length,
    loading,
    modalVisible
  })

  const loadCategories = async () => {
    Logger.info('CategoryManagement', '🔄 بدء تحميل فئات الأصناف...')
    setLoading(true)
    try {
      Logger.info('CategoryManagement', '🔍 فحص window.electronAPI:', !!window.electronAPI)
      if (window.electronAPI) {
        Logger.info('CategoryManagement', '🔍 فحص دالة getCategories:', typeof window.electronAPI.getCategories)

        // إضافة timeout للطلب
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('انتهت مهلة الطلب')), 10000)
        )

        const response = await Promise.race([
          window.electronAPI.getCategories(),
          timeoutPromise
        ])

        Logger.info('CategoryManagement', '📥 استجابة getCategories:', response)

        if (response && (response as any).success) {
          const categoriesData = (response as any).data || []
          const activeCategories = categoriesData.filter((cat: Category) => cat.is_active)
          setCategories(activeCategories)
          Logger.info('CategoryManagement', '✅ تم تحميل ${categoriesData.length} فئة بنجاح (${activeCategories.length} نشطة)')
          Logger.info('CategoryManagement', '📋 الفئات المحملة:', activeCategories.slice(0, 3)) // أول 3 فئات للفحص
        } else if (Array.isArray(response)) {
          // للتوافق مع الإصدارات القديمة
          const activeCategories = response.filter((cat: Category) => cat.is_active)
          setCategories(activeCategories)
          Logger.info('CategoryManagement', '✅ تم تحميل ${response.length} فئة بنجاح (تنسيق قديم) (${activeCategories.length} نشطة)')
          Logger.info('CategoryManagement', '📋 الفئات المحملة:', activeCategories.slice(0, 3))
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل فئات الأصناف'
          Logger.error('CategoryManagement', '❌ خطأ في تحميل الفئات:', errorMessage)
          Logger.error('CategoryManagement', '❌ الاستجابة الكاملة:', response)
          message.error(errorMessage)
          // تعيين مصفوفة فارغة في حالة الخطأ
          setCategories([])
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('CategoryManagement', '❌ window.electronAPI غير متوفر')
        message.error(errorMessage)
        // تعيين مصفوفة فارغة في حالة عدم توفر API
        setCategories([])
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل الفئات'
      Logger.error('CategoryManagement', '❌ خطأ في تحميل الفئات:', error)
      message.error(`خطأ في تحميل الفئات: ${errorMessage}`)
      // تعيين مصفوفة فارغة في حالة الخطأ
      setCategories([])
    } finally {
      setLoading(false)
      Logger.info('CategoryManagement', '🏁 انتهاء تحميل فئات الأصناف')
    }
  }

  useEffect(() => {
    Logger.info('CategoryManagement', '🔄 CategoryManagement useEffect triggered')
    try {
      loadCategories()
    } catch (error) {
      Logger.error('CategoryManagement', '❌ خطأ في useEffect:', error)
      message.error('حدث خطأ في تحميل الكومبوننت')
    }
  }, [])

  // تحويل الفئات إلى هيكل شجري
  const buildTreeData = (categories: Category[], parentId: number | null = null): any[] => {
    return categories
      .filter(cat => cat.parent_id === parentId)
      .map(cat => ({
        key: cat.id.toString(),
        title: (
          <Space>
            {cat.code && (
              <Tag color="purple" style={{ fontFamily: 'monospace', fontSize: '11px' }}>
                {cat.code}
              </Tag>
            )}
            <span style={{ fontWeight: cat.level === 1 ? 'bold' : 'normal' }}>
              {cat.name}
            </span>
            <Tag color={cat.is_active ? 'green' : 'red'}>
              {cat.is_active ? 'نشط' : 'معطل'}
            </Tag>
            <Tag color="blue">
              المستوى {cat.level}
            </Tag>
          </Space>
        ),
        icon: cat.level === 1 ? <FolderOutlined /> : <FolderOutlined />,
        children: buildTreeData(categories, cat.id),
        data: cat
      }))
  }

  const treeData = buildTreeData(categories)

  const handleAdd = (parentCategory?: Category) => {
    setEditingCategory(null)
    form.resetFields()
    if (parentCategory) {
      form.setFieldsValue({ parent_id: parentCategory.id })
    }
    setModalVisible(true)
  }

  const handleCancel = () => {
    setModalVisible(false)
    form.resetFields()
    setEditingCategory(null)
  }

  const handleEdit = (category: Category) => {
    Logger.info('CategoryManagement', '🔧 تعديل الفئة:', category)
    setEditingCategory(category)
    form.setFieldsValue({
      code: category.code,
      name: category.name,
      description: category.description,
      parent_id: category.parent_id || undefined, // تحويل null إلى undefined للـ Select
      is_active: category.is_active
    })
    setModalVisible(true)
  }

  const handleDelete = async (categoryId: number) => {
    try {
      if (window.electronAPI) {
        const response: ApiResponse = await window.electronAPI.deleteCategory(categoryId)
        if (response.success) {
          message.success('تم حذف الفئة بنجاح')
          loadCategories()
          setSelectedCategory(null)
        } else {
          message.error(response.message || 'فشل في حذف الفئة')
        }
      }
    } catch (error) {
      Logger.error('CategoryManagement', 'خطأ في حذف الفئة:', error)
      message.error('فشل في حذف الفئة')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      if (window.electronAPI) {
        let response: ApiResponse

        // إضافة تحقق إضافي من البيانات
        if (!values.name || values.name.trim() === '') {
          message.error('اسم الفئة مطلوب')
          return
        }

        if (editingCategory) {
          Logger.info('CategoryManagement', '🔄 تحديث الفئة:', { categoryId: editingCategory.id, values })
          response = await window.electronAPI.updateCategory(editingCategory.id, values)
        } else {
          Logger.info('CategoryManagement', '➕ إضافة فئة جديدة:', values)
          response = await window.electronAPI.createCategory(values)
        }

        if (response.success) {
          const successMessage = editingCategory
            ? `تم تحديث الفئة "${values.name}" بنجاح`
            : `تم إضافة الفئة "${values.name}" بنجاح`
          message.success(successMessage)
          setModalVisible(false)
          form.resetFields()
          setEditingCategory(null)
          loadCategories()
        } else {
          Logger.error('CategoryManagement', '❌ خطأ في الاستجابة:', response)
          message.error(response.message || 'فشل في حفّ الفئة')
        }
      }
    } catch (error) {
      Logger.error('CategoryManagement', '❌ خطأ في حفّ الفئة:', error)
      message.error('فشل في حفّ الفئة - تحقق من الاتصال بقاعدة البيانات')
    }
  }

  // توليد كود الفئة تلقائياً
  const generateCategoryCode = async () => {
    try {
      if (!window.electronAPI) {
        message.error('لا يمكن الوصول إلى قاعدة البيانات')
        return
      }

      const response = await window.electronAPI.generateCategoryCode()
      Logger.info('CategoryManagement', '🔢 استجابة توليد الكود:', response)

      if (response && response.success && response.data && response.data.code) {
        form.setFieldsValue({ code: response.data.code })
        message.success(`تم توليد كود الفئة: ${response.data.code}`)
      } else {
        const errorMessage = response?.message || 'فشل في توليد كود الفئة'
        Logger.error('CategoryManagement', '❌ خطأ في استجابة توليد الكود:', response)
        message.error(errorMessage)
      }
    } catch (error) {
      Logger.error('CategoryManagement', '❌ خطأ في توليد كود الفئة:', error)
      message.error(`فشل في توليد كود الفئة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    }
  }

  // وّيفة الطباعة الموحدة
  const handlePrint = async () => {
    try {
      const { MasterPrintService } = await import('../../services/MasterPrintService')
      const printService = MasterPrintService.getInstance()

      const printData = {
        title: 'شجرة فئات الأصناف',
        subtitle: `إجمالي الفئات: ${categories.length}`,
        date: new Date().toLocaleDateString('ar-SA'),
        items: categories.map(category => ({
          name: category.name,
          description: category.code,
          quantity: 1,
          unit: 'فئة',
          unitPrice: 0,
          total: 0
        })),
        total: 0,
        notes: 'تقرير شجرة فئات الأصناف'
      }

      await printService.print(printData, {
        type: 'report',
        subType: 'inventory',
        showLogo: true,
        showHeader: true,
        showFooter: true
      })

      message.success('تم إرسال التقرير للطباعة')
    } catch (error) {
      console.error('خطأ في الطباعة:', error)
      message.error('فشل في طباعة التقرير')
    }
  }

  // وّيفة التصدير إلى Excel
  const handleExportToExcel = () => {
    try {
      const exportData = categories.map((category, index) => ({
        'الرقم': index + 1,
        'اسم الفئة': category.name,
        'الوصف': category.description || '-',
        'الفئة الأب': categories.find(c => c.id === category.parent_id)?.name || '-',
        'المستوى': category.level,
        'المسار الكامل': category.path,
        'الحالة': category.is_active ? 'نشط' : 'معطل',
        'تاريخ الإنشاء': DateUtils.formatForDisplay(category.created_at, DATE_FORMATS.DISPLAY_DATE)
      }))

      const worksheet = XLSX.utils.json_to_sheet(exportData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'فئات الأصناف')

      const fileName = `فئات_الأصناف_${DateUtils.formatForDisplay(new Date().toISOString(), DATE_FORMATS.DISPLAY_DATE)}.xlsx`
      XLSX.writeFile(workbook, fileName)

      message.success('تم تصدير البيانات بنجاح')
    } catch (error) {
      Logger.error('CategoryManagement', 'خطأ في التصدير:', error)
      message.error('حدث خطأ أثناء التصدير')
    }
  }

  // تم حذف generatePrintContent - نستخدم الآن MasterPrintService الموحد
  const _generatePrintContent_removed = () => {
    const currentDate = DateUtils.formatForDisplay(new Date().toISOString(), DATE_FORMATS.DISPLAY_DATE_TIME)

    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>شجرة فئات الأصناف</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 20px;
          }
          .header h1 {
            color: #1890ff;
            margin: 0;
            font-size: 24px;
          }
          .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
          }
          .stat-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #d9d9d9;
            text-align: center;
          }
          .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
          }
          .stat-label {
            color: #666;
            font-size: 14px;
          }
          .tree-container {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
          }
          .category-item {
            margin: 8px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border-right: 4px solid #1890ff;
          }
          .category-level-1 { margin-right: 0px; font-weight: bold; }
          .category-level-2 { margin-right: 20px; }
          .category-level-3 { margin-right: 40px; }
          .category-level-4 { margin-right: 60px; }
          .status-active { color: #52c41a; }
          .status-inactive { color: #ff4d4f; }
          .footer {
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 12px;
          }
          @media print {
            body { margin: 0; }
            .no-print { display: none; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>شجرة فئات الأصناف</h1>
          <p>تاريخ الطباعة: ${currentDate}</p>
        </div>

        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value">${totalCategories}</div>
            <div class="stat-label">إجمالي الفئات</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">${activeCategories}</div>
            <div class="stat-label">الفئات النشطة</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">${maxLevel}</div>
            <div class="stat-label">أقصى مستوى</div>
          </div>
        </div>

        <div class="tree-container">
          <h3>الهيكل الشجري للفئات:</h3>
          ${categories
            .sort((a, b) => a.path.localeCompare(b.path))
            .map(category => `
              <div class="category-item category-level-${category.level}">
                <strong>${category.name}</strong>
                <span class="status-${category.is_active ? 'active' : 'inactive'}">
                  (${category.is_active ? 'نشط' : 'معطل'})
                </span>
                ${category.description ? `<br><small>${category.description}</small>` : ''}
                <br><small>المسار: ${category.path}</small>
              </div>
            `).join('')}
        </div>

        <div class="footer">
          <p>تم إنشاء هذا التقرير بواسطة نّام إدارة المخزون - ${currentDate}</p>
        </div>
      </body>
      </html>
    `
  }

  const onSelect = (selectedKeys: React.Key[], info: any) => {
    if (selectedKeys.length > 0 && info.node.data) {
      setSelectedCategory(info.node.data)
    } else {
      setSelectedCategory(null)
    }
  }

  // إحصائيات الفئات
  const totalCategories = categories.length
  const activeCategories = categories.filter(c => c.is_active).length
  const mainCategories = categories.filter(c => c.level === 1).length
  const subCategories = categories.filter(c => c.level > 1).length
  const maxLevel = categories.length > 0 ? Math.max(...categories.map(c => c.level)) : 0

  Logger.info('CategoryManagement', '🎨 عرض CategoryManagement:', {
    totalCategories,
    activeCategories,
    mainCategories,
    subCategories,
    loading,
    modalVisible,
    treeDataLength: treeData.length,
    categoriesData: categories.slice(0, 3) // أول 3 فئات للفحص
  })

  // إضافة معالجة أخطاء للعرض
  Logger.info('CategoryManagement', '🎨 CategoryManagement render started')
  try {
    Logger.info('CategoryManagement', '🎨 CategoryManagement render - inside try block')
    return (
      <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
        <Card style={{ marginBottom: '24px' }}>
          <Space>
            <AppstoreOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
            <div>
              <Title level={2} style={{ margin: 0 }}>إدارة فئات الأصناف</Title>
              <Text type="secondary">تنّيم وإدارة فئات الأصناف في النّام</Text>
            </div>
          </Space>
        </Card>

        {/* إحصائيات سريعة */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="إجمالي الفئات"
              value={totalCategories}
              prefix={<AppstoreOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </StyledCard>
        </Col>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="الفئات النشطة"
              value={activeCategories}
              prefix={<FolderOpenOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </StyledCard>
        </Col>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="الفئات الرئيسية"
              value={mainCategories}
              prefix={<FolderOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </StyledCard>
        </Col>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="الفئات الفرعية"
              value={subCategories}
              prefix={<BranchesOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </StyledCard>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* شجرة الفئات */}
        <Col span={16}>
          <StyledCard
            title={
              <Space>
                <AppstoreOutlined />
                <Title level={4} style={{ margin: 0 }}>شجرة فئات الأصناف</Title>
              </Space>
            }
            extra={
              <Space>
                <Button
                  icon={<PrinterOutlined />}
                  onClick={handlePrint}
                  disabled={categories.length === 0}
                >
                  طباعة
                </Button>
                <Button
                  icon={<FileExcelOutlined />}
                  onClick={handleExportToExcel}
                  disabled={categories.length === 0}
                  style={{ color: '#52c41a', borderColor: '#52c41a' }}
                >
                  تصدير Excel
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => handleAdd()}
                >
                  إضافة فئة رئيسية
                </Button>
              </Space>
            }
          >
            <TreeContainer>
              <DirectoryTree
                multiple={false}
                onSelect={onSelect}
                treeData={treeData}
                showIcon
                height={400}
              />
            </TreeContainer>
          </StyledCard>
        </Col>

        {/* تفاصيل الفئة المحددة */}
        <Col span={8}>
          <StyledCard
            title="تفاصيل الفئة"
            extra={
              selectedCategory && (
                <Space>
                  <Tooltip title="إضافة فئة فرعية">
                    <Button
                      size="small"
                      icon={<PlusOutlined />}
                      onClick={() => handleAdd(selectedCategory)}
                    />
                  </Tooltip>
                  <Tooltip title="تعديل الفئة">
                    <Button
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => handleEdit(selectedCategory)}
                    />
                  </Tooltip>
                  <Popconfirm
                    title="هل أنت متأكد من حذف هذه الفئة؟"
                    onConfirm={() => handleDelete(selectedCategory.id)}
                    okText="نعم"
                    cancelText="لا"
                  >
                    <Tooltip title="حذف الفئة">
                      <Button
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                      />
                    </Tooltip>
                  </Popconfirm>
                </Space>
              )
            }
          >
            {selectedCategory ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <strong>كود الفئة:</strong>
                  <div>
                    <Tag color="purple" style={{ fontFamily: 'monospace' }}>
                      {selectedCategory.code || 'لا يوجد كود'}
                    </Tag>
                  </div>
                </div>

                <div>
                  <strong>اسم الفئة:</strong>
                  <div>{selectedCategory.name}</div>
                </div>

                <div>
                  <strong>الوصف:</strong>
                  <div>{selectedCategory.description || 'لا يوجد وصف'}</div>
                </div>

                <div>
                  <strong>المستوى:</strong>
                  <div>
                    <Tag color="blue">المستوى {selectedCategory.level}</Tag>
                  </div>
                </div>

                <div>
                  <strong>المسار:</strong>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {selectedCategory.path}
                  </div>
                </div>

                <div>
                  <strong>الحالة:</strong>
                  <div>
                    <Tag color={selectedCategory.is_active ? 'green' : 'red'}>
                      {selectedCategory.is_active ? 'نشط' : 'معطل'}
                    </Tag>
                  </div>
                </div>

                <div>
                  <strong>تاريخ الإنشاء (ميلادي):</strong>
                  <div>{DateUtils.formatForDisplay(selectedCategory.created_at, DATE_FORMATS.DISPLAY_DATE)}</div>
                </div>
              </Space>
            ) : (
              <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                اختر فئة من الشجرة لعرض التفاصيل
              </div>
            )}
          </StyledCard>
        </Col>
      </Row>

      {/* نموذج إضافة/تعديل الفئة */}
      <Modal
        title={
          <Space>
            {editingCategory ? <EditOutlined /> : <PlusOutlined />}
            {editingCategory ? `تعديل الفئة: ${editingCategory.name}` : 'إضافة فئة جديدة'}
          </Space>
        }
        open={modalVisible}
        onCancel={handleCancel}
        footer={null}
        width={700}
      >
        {editingCategory && (
          <div style={{
            background: '#f0f2f5',
            padding: '12px',
            borderRadius: '6px',
            marginBottom: '16px',
            border: '1px solid #d9d9d9'
          }}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div><strong>الحالة الحالية:</strong></div>
              <div>📁 <strong>الاسم:</strong> {editingCategory.name}</div>
              <div>📊 <strong>المستوى:</strong> {editingCategory.level}</div>
              <div>🗂️ <strong>المسار:</strong> {editingCategory.path}</div>
              <div>👆 <strong>الفئة الأب الحالية:</strong> {
                editingCategory.parent_id
                  ? categories.find(c => c.id === editingCategory.parent_id)?.name || 'غير محدد'
                  : 'فئة رئيسية (لا يوجد أب)'
              }</div>
            </Space>
          </div>
        )}

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ is_active: true }}
        >
          <Form.Item
            name="code"
            label="كود الفئة"
            rules={[{ required: true, message: 'يرجى إدخال كود الفئة' }]}
          >
            <Space.Compact style={{ display: 'flex', width: '100%' }}>
              <Input
                style={{ flex: 1 }}
                placeholder="أدخل كود الفئة أو اضغط توليد"
              />
              <Button
                type="primary"
                onClick={generateCategoryCode}
                loading={loading}
              >
                توليد تلقائي
              </Button>
            </Space.Compact>
          </Form.Item>

          <Form.Item
            name="name"
            label="اسم الفئة"
            rules={[{ required: true, message: 'يرجى إدخال اسم الفئة' }]}
          >
            <Input placeholder="أدخل اسم الفئة" />
          </Form.Item>

          <Form.Item
            name="description"
            label="الوصف"
          >
            <Input.TextArea placeholder="أدخل وصف الفئة (اختياري)" rows={3} />
          </Form.Item>

          {editingCategory && (
            <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f6f6f6', borderRadius: 6 }}>
              <Text strong>الحالة الحالية:</Text>
              <br />
              <Text>الاسم: {editingCategory.name}</Text>
              <br />
              <Text>المستوى: {editingCategory.level}</Text>
              <br />
              <Text>المسار: {editingCategory.path || 'فئة رئيسية'}</Text>
              {editingCategory.parent_id && (
                <>
                  <br />
                  <Text>الفئة الأب الحالية: {categories.find(c => c.id === editingCategory.parent_id)?.name || 'غير محدد'}</Text>
                </>
              )}
            </div>
          )}

          <Form.Item
            name="parent_id"
            label="الفئة الأب"
            help={editingCategory ? "يمكنك تغيير الفئة الأب أو تركه فارغاً لجعل الفئة رئيسية" : "اتركه فارغاً لجعل الفئة رئيسية"}
          >
            <Select
              placeholder="اختر الفئة الأب (اختياري)"
              allowClear
              showSearch
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
              }
            >
              {categories
                .filter(cat => {
                  // منع اختيار نفس الفئة كأب لنفسها
                  if (editingCategory && cat.id === editingCategory.id) return false

                  // منع اختيار فئة فرعية كأب لفئة أعلى منها في الهيكل
                  // تحسين المنطق للتحقق من المسار بشكل أكثر دقة
                  if (editingCategory && cat.path) {
                    // التحقق من أن الفئة المحررة ليست في مسار الفئة المرشحة
                    const pathParts = cat.path.split(' > ')
                    if (pathParts.includes(editingCategory.name)) return false

                    // التحقق من أن الفئة المرشحة ليست فرعية للفئة المحررة
                    if (editingCategory.path) {
                      const editingPathParts = editingCategory.path.split(' > ')
                      if (editingPathParts.includes(cat.name)) return false
                    }
                  }

                  return true
                })
                .sort((a, b) => a.level - b.level || a.name.localeCompare(b.name))
                .map(cat => (
                  <Option key={cat.id} value={cat.id}>
                    {'  '.repeat(Math.max(0, cat.level - 1))}
                    {cat.name}
                    <span style={{ color: '#999', fontSize: '12px' }}>
                      {' '}(المستوى {cat.level})
                    </span>
                  </Option>
                ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="is_active"
            label="الحالة"
            valuePropName="checked"
          >
            <Switch checkedChildren="نشط" unCheckedChildren="معطل" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={handleCancel}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit" loading={false}>
                {editingCategory ? 'تحديث الفئة' : 'إضافة الفئة'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
      </div>
    )
  } catch (error) {
    Logger.error('CategoryManagement', '❌ خطأ في عرض CategoryManagement:', error)
    return (
      <div style={{ padding: '24px' }}>
        <Result
          status="error"
          title="خطأ في تحميل إدارة الفئات"
          subTitle="حدث خطأ أثناء تحميل واجهة إدارة فئات الأصناف"
          extra={
            <Button type="primary" onClick={() => window.location.reload()}>
              إعادة تحميل الصفحة
            </Button>
          }
        />
      </div>
    )
  }
}

const CategoryManagementWithApp: React.FC<CategoryManagementProps> = (props) => {
  return (
    <App>
      <CategoryManagement {...props} />
    </App>
  )
}

export default CategoryManagementWithApp
