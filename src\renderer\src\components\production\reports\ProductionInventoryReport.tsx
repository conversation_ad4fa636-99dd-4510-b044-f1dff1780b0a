import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  DatePicker,
  Select,
  Row,
  Col,
  Statistic,
  Tag,
  Space,
  Typography,
  Alert,
  message,
  // Progress,
  // Tooltip,
  // Divider
} from 'antd'
import { SafeLogger as Logger } from '../../../utils/logger'
import UnifiedPrintButton from '../../common/UnifiedPrintButton'
import {
  ArrowLeftOutlined,
  DownloadOutlined,
  PrinterOutlined,
  BuildOutlined,
  InboxOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DollarOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import * as XLSX from 'xlsx'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

interface ProductionInventoryReportProps {
  onBack?: () => void
}

interface ReportData {
  production_orders: any[]
  inventory_status: any[]
  material_consumption: any[]
  production_costs: any[]
}

const ProductionInventoryReport: React.FC<ProductionInventoryReportProps> = ({ onBack }) => {
  const [loading, setLoading] = useState(false)
  const [reportData, setReportData] = useState<ReportData>({
    production_orders: [],
    inventory_status: [],
    material_consumption: [],
    production_costs: []
  })
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs()
  ])
  const [selectedDepartment, setSelectedDepartment] = useState<number | null>(null)
  const [departments, setDepartments] = useState<any[]>([])

  // إحصائيات التقرير
  const [stats, setStats] = useState({
    total_orders: 0,
    completed_orders: 0,
    total_production_value: 0,
    total_material_cost: 0,
    low_stock_items: 0,
    out_of_stock_items: 0
  })

  useEffect(() => {
    loadDepartments()
    generateReport()
  }, [])

  useEffect(() => {
    generateReport()
  }, [dateRange, selectedDepartment])

  const loadDepartments = async () => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.invoke('get-production-departments')
        if (result.success) {
          setDepartments(result.data)
        }
      }
    } catch (error) {
      Logger.error('ProductionInventoryReport', 'خطأ في تحميل الأقسام:', error)
    }
  }

  const generateReport = async () => {
    setLoading(true)
    try {
      if (!window.electronAPI) {
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      }

      // إعداد فلاتر التقرير
      const _filters = {
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
        department_id: selectedDepartment
      }

      // جلب البيانات الحقيقية من قاعدة البيانات
      const [ordersResponse, inventoryResponse] = await Promise.all([
        window.electronAPI.getProductionOrders(),
        window.electronAPI.getItems()
      ])

      // تجميع البيانات من الاستجابات
      const reportData = {
        production_orders: ordersResponse?.success ? ordersResponse.data : [],
        inventory_status: Array.isArray(inventoryResponse) ? inventoryResponse : [],
        material_consumption: [],
        production_costs: []
      }

      setReportData(reportData)
      calculateStats(reportData)
    } catch (error) {
      Logger.error('ProductionInventoryReport', 'خطأ في إنشاء التقرير:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (data: ReportData) => {
    const stats = {
      total_orders: data.production_orders.length,
      completed_orders: data.production_orders.filter(o => o.status === 'completed').length,
      total_production_value: data.production_orders.reduce((sum, o) => sum + (o.estimated_cost || 0), 0),
      total_material_cost: data.material_consumption.reduce((sum, m) => sum + (m.total_cost || 0), 0),
      low_stock_items: data.inventory_status.filter(i => i.current_stock <= i.min_quantity && i.current_stock > 0).length,
      out_of_stock_items: data.inventory_status.filter(i => i.current_stock <= 0).length
    }
    setStats(stats)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'in_progress': return 'blue'
      case 'completed': return 'green'
      case 'cancelled': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتّار'
      case 'in_progress': return 'قيد التنفيذ'
      case 'completed': return 'مكتمل'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  const getStockStatus = (item: any) => {
    if (item.current_stock <= 0) {
      return { status: 'نفدت', color: 'red', icon: <ExclamationCircleOutlined /> }
    } else if (item.current_stock <= item.min_quantity) {
      return { status: 'منخفض', color: 'orange', icon: <WarningOutlined /> }
    } else {
      return { status: 'متوفر', color: 'green', icon: <CheckCircleOutlined /> }
    }
  }

  const exportToExcel = () => {
    try {
      if (!reportData.production_orders.length && !reportData.inventory_status.length) {
        message.warning('لا توجد بيانات للتصدير')
        return
      }

      const wb = XLSX.utils.book_new()

      // ورقة الإحصائيات العامة
      const statsData = [
        { 'المؤشر': 'إجمالي أوامر الإنتاج', 'القيمة': stats.total_orders },
        { 'المؤشر': 'أوامر مكتملة', 'القيمة': stats.completed_orders },
        { 'المؤشر': 'قيمة الإنتاج (₪)', 'القيمة': stats.total_production_value },
        { 'المؤشر': 'تكلفة المواد (₪)', 'القيمة': stats.total_material_cost },
        { 'المؤشر': 'أصناف منخفضة المخزون', 'القيمة': stats.low_stock_items },
        { 'المؤشر': 'أصناف نافدة من المخزون', 'القيمة': stats.out_of_stock_items }
      ]
      const statsWS = XLSX.utils.json_to_sheet(statsData)
      XLSX.utils.book_append_sheet(wb, statsWS, 'الإحصائيات')

      // ورقة أوامر الإنتاج
      if (reportData.production_orders.length > 0) {
        const ordersWS = XLSX.utils.json_to_sheet(reportData.production_orders)
        XLSX.utils.book_append_sheet(wb, ordersWS, 'أوامر الإنتاج')
      }

      // ورقة حالة المخزون
      if (reportData.inventory_status.length > 0) {
        const inventoryWS = XLSX.utils.json_to_sheet(reportData.inventory_status)
        XLSX.utils.book_append_sheet(wb, inventoryWS, 'حالة المخزون')
      }

      // ورقة استهلاك المواد
      if (reportData.material_consumption.length > 0) {
        const consumptionWS = XLSX.utils.json_to_sheet(reportData.material_consumption)
        XLSX.utils.book_append_sheet(wb, consumptionWS, 'استهلاك المواد')
      }

      const fileName = `تقرير_الإنتاج_والمخزون_${dayjs().format('YYYY-MM-DD_HH-mm')}.xlsx`
      XLSX.writeFile(wb, fileName)

      message.success('تم تصدير التقرير الشامل بنجاح')
    } catch (error) {
      Logger.error('ProductionInventoryReport', 'خطأ في تصدير التقرير:', error)
      message.error('فشل في تصدير التقرير - يرجى المحاولة مرة أخرى')
    }
  }

  const productionOrdersColumns = [
    {
      title: 'رقم الأمر',
      dataIndex: 'order_number',
      key: 'order_number',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>
    },
    {
      title: 'الصنف',
      dataIndex: 'item_name',
      key: 'item_name',
      width: 200
    },
    {
      title: 'القسم',
      dataIndex: 'department_name',
      key: 'department_name',
      width: 150
    },
    {
      title: 'الكمية',
      key: 'quantity',
      width: 100,
      render: (record: any) => `${record.quantity} ${record.unit}`
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'التكلفة المقدرة',
      dataIndex: 'estimated_cost',
      key: 'estimated_cost',
      width: 120,
      render: (cost: number) => `₪${cost?.toFixed(2) || '0.00'}`
    },
    {
      title: 'التكلفة الفعلية',
      dataIndex: 'actual_cost',
      key: 'actual_cost',
      width: 120,
      render: (cost: number) => `₪${cost?.toFixed(2) || '0.00'}`
    },
    {
      title: 'تاريخ الأمر',
      dataIndex: 'order_date',
      key: 'order_date',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    }
  ]

  const inventoryColumns = [
    {
      title: 'الصنف',
      dataIndex: 'item_name',
      key: 'item_name',
      width: 200
    },
    {
      title: 'الفئة',
      dataIndex: 'category_name',
      key: 'category_name',
      width: 120
    },
    {
      title: 'المخزون الحالي',
      key: 'current_stock',
      width: 150,
      render: (record: any) => {
        const stockStatus = getStockStatus(record)
        return (
          <Space>
            <span style={{ color: stockStatus.color }}>
              {stockStatus.icon}
            </span>
            <span>{record.current_stock} {record.unit}</span>
          </Space>
        )
      }
    },
    {
      title: 'الحد الأدنى',
      dataIndex: 'min_quantity',
      key: 'min_quantity',
      width: 100,
      render: (qty: number, record: any) => `${qty} ${record.unit}`
    },
    {
      title: 'القيمة الإجمالية',
      key: 'total_value',
      width: 120,
      render: (record: any) => `₪${(record.current_stock * record.cost_price).toFixed(2)}`
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* رأس التقرير */}
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
            <BuildOutlined style={{ marginRight: '12px' }} />
            تقرير الإنتاج والمخزون الشامل
          </Title>
          <Text type="secondary">
            تقرير شامل يربط بين أوامر الإنتاج وحالة المخزون واستهلاك المواد
          </Text>
        </div>
        <Space>
          <Button
            icon={<DownloadOutlined />}
            onClick={exportToExcel}
            type="primary"
          >
            تصدير Excel
          </Button>
          <UnifiedPrintButton
            data={{
              title: 'تقرير الإنتاج والمخزون الشامل',
              subtitle: `من ${dateRange[0].format('YYYY-MM-DD')} إلى ${dateRange[1].format('YYYY-MM-DD')}`,
              items: reportData.production_orders?.map((order: any, index: number) => ({
                id: order.id || index,
                name: `أمر إنتاج ${order.order_number}`,
                description: `المنتج: ${order.item_name} - الكمية: ${order.quantity}`,
                quantity: order.quantity || 0,
                unit: order.unit || 'قطعة',
                unitPrice: order.estimated_cost || 0,
                total: (order.quantity || 0) * (order.estimated_cost || 0)
              })) || [],
              total: stats.total_production_value,
              notes: `إحصائيات التقرير:\n- أوامر الإنتاج: ${stats.total_orders}\n- قيمة الإنتاج: ${stats.total_production_value.toLocaleString()} ₪\n- أصناف المخزون: ${reportData.inventory_status.length}\n- قيمة المخزون: ${reportData.inventory_status.reduce((sum, item) => sum + (item.current_stock * item.unit_cost), 0).toLocaleString()} ₪`,
              metadata: {
                generatedAt: new Date().toISOString(),
                generatedBy: 'نظام الإنتاج والمخزون',
                reportType: 'production_inventory',
                dateRange: `${dateRange[0].format('YYYY-MM-DD')} - ${dateRange[1].format('YYYY-MM-DD')}`,
                department: selectedDepartment || 'جميع الأقسام'
              }
            }}
            type="report"
            subType="inventory"
            buttonText="طباعة"
            size="middle"
            showDropdown={true}
            _documentId="production_inventory_report"
            onAfterPrint={() => message.success('تم طباعة التقرير بنجاح')}
            onError={(error) => message.error(`فشل في الطباعة: ${error}`)}
          />
          {onBack && (
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={onBack}
            >
              العودة
            </Button>
          )}
        </Space>
      </div>

      {/* فلاتر التقرير */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={8}>
            <div>
              <Text strong>فترة التقرير:</Text>
              <RangePicker
                value={dateRange}
                onChange={(dates) => dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
                style={{ width: '100%', marginTop: '8px' }}
                format="YYYY-MM-DD"
              />
            </div>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <div>
              <Text strong>القسم:</Text>
              <Select
                value={selectedDepartment}
                onChange={setSelectedDepartment}
                style={{ width: '100%', marginTop: '8px' }}
                placeholder="جميع الأقسام"
                allowClear
              >
                {departments.map(dept => (
                  <Option key={dept.id} value={dept.id}>
                    {dept.name}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <div style={{ marginTop: '32px' }}>
              <Button
                type="primary"
                onClick={generateReport}
                loading={loading}
                block
              >
                تحديث التقرير
              </Button>
            </div>
          </Col>
        </Row>
      </Card>

      {/* إحصائيات سريعة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={12} sm={8} lg={4}>
          <Card>
            <Statistic
              title="إجمالي الأوامر"
              value={stats.total_orders}
              prefix={<BuildOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <Card>
            <Statistic
              title="أوامر مكتملة"
              value={stats.completed_orders}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <Card>
            <Statistic
              title="قيمة الإنتاج"
              value={stats.total_production_value}
              prefix={<DollarOutlined />}
              suffix="₪"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <Card>
            <Statistic
              title="تكلفة المواد"
              value={stats.total_material_cost}
              prefix={<InboxOutlined />}
              suffix="₪"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <Card>
            <Statistic
              title="مخزون منخفض"
              value={stats.low_stock_items}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <Card>
            <Statistic
              title="مخزون نافد"
              value={stats.out_of_stock_items}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* تنبيهات */}
      {(stats.low_stock_items > 0 || stats.out_of_stock_items > 0) && (
        <Alert
          message="تنبيهات المخزون"
          description={`يوجد ${stats.low_stock_items} صنف منخفض المخزون و ${stats.out_of_stock_items} صنف نافد من المخزون`}
          type="warning"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* جدول أوامر الإنتاج */}
      <Card title="أوامر الإنتاج" style={{ marginBottom: '24px' }}>
        <Table
          columns={productionOrdersColumns}
          dataSource={reportData.production_orders}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1000 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} أمر إنتاج`
          }}
        />
      </Card>

      {/* جدول حالة المخزون */}
      <Card title="حالة المخزون">
        <Table
          columns={inventoryColumns}
          dataSource={reportData.inventory_status}
          rowKey="item_name"
          loading={loading}
          scroll={{ x: 800 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} صنف`
          }}
          rowClassName={(record) => {
            if (record.current_stock <= 0) return 'row-out-of-stock'
            if (record.current_stock <= record.min_quantity) return 'row-low-stock'
            return ''
          }}
        />
      </Card>
    </div>
  )
}

export default ProductionInventoryReport
