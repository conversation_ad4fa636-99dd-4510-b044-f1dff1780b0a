import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  Space,
  Tag,
  Row,
  Col,
  Statistic,
  Descriptions,
  Divider,
  Popconfirm,
  App
} from 'antd'
import { SafeLogger as Logger } from '../../../utils/logger'
import {
  ArrowLeftOutlined,
  PlusOutlined,
  DollarOutlined,
  FileTextOutlined,
  PrinterOutlined,
  EyeOutlined,
  CreditCardOutlined,
  // EditOutlined,
  DeleteOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { DateUtils } from '../../../utils/dateConfig'
import dayjs from 'dayjs'
import {
  PaymentMethodSelector,
  PaymentForm,
  PAYMENT_METHODS,
  PaymentMethodType
} from '../../common/PaymentComponents'
import {
  RemainingAmountDisplay,
  PaymentStatusTag,
  PaymentSummary
} from '../../common/RemainingAmountDisplay'
// import MasterPrintButton from '../../common/MasterPrintButton' // تم حذف نظام الطباعة
// import { PaymentData } from '../../common/InvoicePrintTemplate' // تم حذف نظام الطباعة
import { InvoicePrintButton } from '../../common'
import { MasterPrintService } from '../../../services/MasterPrintService'
import { useCurrentUser } from '../../../utils/permissions'

const { Option } = Select

const StyledCard = styled(Card)`
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .ant-card-head {
    background: linear-gradient(135deg, #722ed1 0%, #eb2f96 100%);
    border-radius: 8px 8px 0 0;
    
    .ant-card-head-title {
      color: white;
      font-weight: 600;
    }
  }
`

const StatsCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .ant-statistic-title {
    color: #666;
    font-size: 14px;
  }
  
  .ant-statistic-content {
    color: #722ed1;
  }
`

interface PaintInvoice {
  id: number
  invoice_number: string
  order_id: number
  order_number: string
  customer_id: number
  customer_name: string
  invoice_date: string
  due_date: string
  total_area: number
  subtotal: number
  tax_rate: number
  tax_amount: number
  discount_rate: number
  discount_amount: number
  total_amount: number
  paid_amount: number
  remaining_amount: number
  status: string
  notes: string
  created_by: number
  created_by_name: string
  created_at: string
}

interface PaintOrder {
  id: number
  order_number: string
  customer_id: number
  customer_name: string
  status: string
  total_area: number
  total_amount: number
}

interface PaintInvoicesManagementProps {
  onBack: () => void
}

const PaintInvoicesManagement: React.FC<PaintInvoicesManagementProps> = ({ onBack }) => {
  const currentUser = useCurrentUser()
  const userId = currentUser?.id || 1
  const [invoices, setInvoices] = useState<PaintInvoice[]>([])
  const [availableOrders, setAvailableOrders] = useState<PaintOrder[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [detailsModalVisible, setDetailsModalVisible] = useState(false)
  const [selectedInvoice, setSelectedInvoice] = useState<PaintInvoice | null>(null)
  const [form] = Form.useForm()

  // استخدام App context للرسائل
  const { message: messageApi } = App.useApp()

  // حالات جديدة للدفع والطباعة
  const [paymentModalVisible, setPaymentModalVisible] = useState(false)
  const [selectedInvoiceForPayment, setSelectedInvoiceForPayment] = useState<PaintInvoice | null>(null)
  const [paymentForm] = Form.useForm()
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethodType>(PAYMENT_METHODS.CASH)
  const [invoicePayments, setInvoicePayments] = useState<any[]>([])
  const [loadingPayments, setLoadingPayments] = useState(false)

  useEffect(() => {
    loadInvoices()
    loadAvailableOrders()
  }, [])

  const loadInvoices = async () => {
    setLoading(true)
    try {
      Logger.info('PaintInvoicesManagement', '🔄 جاري تحميل فواتير الدهان...')

      if (!window.electronAPI) {
        Logger.error('PaintInvoicesManagement', '❌ window.electronAPI غير متوفر')
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      } else {
        const response = await window.electronAPI.getPaintInvoices()
        if (response.success) {
          setInvoices(response.data)
          Logger.info('PaintInvoicesManagement', '✅ تم تحميل فواتير الدهان من قاعدة البيانات')
        } else {
          messageApi.error('فشل في تحميل فواتير الدهان')
        }
      }
    } catch (error) {
      Logger.error('PaintInvoicesManagement', 'خطأ في تحميل فواتير الدهان:', error)
      messageApi.error('حدث خطأ في تحميل فواتير الدهان')
    } finally {
      setLoading(false)
    }
  }

  const loadAvailableOrders = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getPaintOrders()
        if (response.success) {
          // فلترة الأوامر المكتملة وغير المفوترة
          const completedOrders = response.data.filter((order: PaintOrder) => 
            order.status === 'completed'
          )
          setAvailableOrders(completedOrders)
        }
      }
    } catch (error) {
      Logger.error('PaintInvoicesManagement', 'خطأ في تحميل الأوامر المتاحة:', error)
    }
  }

  const generateInvoiceNumber = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generatePaintInvoiceNumber()
        if (response.success) {
          form.setFieldsValue({ invoice_number: response.data.invoice_number })
        }
      }
    } catch (error) {
      Logger.error('PaintInvoicesManagement', 'خطأ في إنشاء رقم الفاتورة:', error)
    }
  }

  const handleSubmit = async (values: any) => {
    // التحقق من صحة البيانات الأساسية
    if (!values.invoice_number || values.invoice_number.trim() === '') {
      messageApi.error('رقم الفاتورة مطلوب')
      return { success: false, message: 'رقم الفاتورة مطلوب' }
    }

    if (!values.order_id) {
      messageApi.error('يجب اختيار أمر الدهان')
      return { success: false, message: 'يجب اختيار أمر الدهان' }
    }

    if (!values.invoice_date) {
      messageApi.error('تاريخ الفاتورة مطلوب')
      return { success: false, message: 'تاريخ الفاتورة مطلوب' }
    }

    // التحقق من نسب الضريبة والخصم
    const taxRate = values.tax_rate || 0
    const discountRate = values.discount_rate || 0

    if (taxRate < 0 || taxRate > 100) {
      messageApi.error('نسبة الضريبة يجب أن تكون بين 0 و 100')
      return { success: false, message: 'نسبة الضريبة يجب أن تكون بين 0 و 100' }
    }

    if (discountRate < 0 || discountRate > 100) {
      messageApi.error('نسبة الخصم يجب أن تكون بين 0 و 100')
      return { success: false, message: 'نسبة الخصم يجب أن تكون بين 0 و 100' }
    }

    try {
      const invoiceData = {
        ...values,
        order_id: parseInt(String(values.order_id)), // تحويل معرف أمر الدهان إلى رقم صحيح
        invoice_date: values.invoice_date.format('YYYY-MM-DD'),
        due_date: values.due_date?.format('YYYY-MM-DD'),
        created_by: userId
      }

      if (window.electronAPI) {
        const response = await window.electronAPI.createPaintInvoice(invoiceData)
        if (response.success) {
          messageApi.success('تم إنشاء فاتورة الدهان بنجاح')
          setModalVisible(false)
          form.resetFields()
          loadInvoices()
          loadAvailableOrders()
          return { success: true, data: response.data }
        } else {
          const errorMessage = response.message || 'فشل في إنشاء فاتورة الدهان'
          messageApi.error(errorMessage)
          Logger.error('PaintInvoicesManagement', 'خطأ من الخادم:', response)
          return { success: false, message: errorMessage }
        }
      }
      const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات'
      messageApi.error(errorMessage)
      return { success: false, message: errorMessage }
    } catch (error) {
      Logger.error('PaintInvoicesManagement', 'خطأ في إنشاء فاتورة الدهان:', error)
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع في إنشاء فاتورة الدهان'
      messageApi.error(errorMessage)
      return { success: false, message: errorMessage }
    }
  }

  const updateInvoiceStatus = async (invoiceId: number, status: string) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.updatePaintInvoiceStatus(invoiceId, status)
        if (response.success) {
          messageApi.success('تم تحديث حالة الفاتورة بنجاح')
          loadInvoices()
        } else {
          messageApi.error('فشل في تحديث حالة الفاتورة')
        }
      }
    } catch (error) {
      Logger.error('PaintInvoicesManagement', 'خطأ في تحديث حالة الفاتورة:', error)
      messageApi.error('حدث خطأ في تحديث حالة الفاتورة')
    }
  }

  const handleDeleteInvoice = async (invoiceId: number) => {
    try {
      if (window.electronAPI) {
        const response = await (window.electronAPI as any).deletePaintInvoice(invoiceId)
        if (response.success) {
          messageApi.success('تم حذف فاتورة الدهان بنجاح')
          loadInvoices()
          loadAvailableOrders()
        } else {
          messageApi.error(response.message || 'فشل في حذف فاتورة الدهان')
        }
      }
    } catch (error) {
      Logger.error('PaintInvoicesManagement', 'خطأ في حذف فاتورة الدهان:', error)
      messageApi.error('حدث خطأ أثناء حذف فاتورة الدهان')
    }
  }

  // تحميل مدفوعات الفاتورة
  const loadInvoicePayments = async (invoiceId: number) => {
    setLoadingPayments(true)
    try {
      if (window.electronAPI) {
        const response = await (window.electronAPI as any).getInvoicePayments(invoiceId, 'paint')
        if (response.success) {
          setInvoicePayments(response.data || [])
        }
      }
    } catch (error) {
      Logger.error('PaintInvoicesManagement', 'خطأ في تحميل المدفوعات:', error)
    } finally {
      setLoadingPayments(false)
    }
  }

  // فتح نافذة الدفع
  const showPaymentModal = (invoice: PaintInvoice) => {
    setSelectedInvoiceForPayment(invoice)
    setPaymentModalVisible(true)
    paymentForm.resetFields()
    paymentForm.setFieldsValue({
      payment_date: dayjs(),
      payment_method: PAYMENT_METHODS.CASH
    })
    setSelectedPaymentMethod(PAYMENT_METHODS.CASH)
    loadInvoicePayments(invoice.id)
  }

  // معالجة الدفع
  const handlePayment = async (values: any) => {
    if (!selectedInvoiceForPayment) return

    try {
      const paymentData = {
        invoice_id: selectedInvoiceForPayment.id,
        invoice_type: 'paint_invoice',
        customer_id: selectedInvoiceForPayment.customer_id,
        amount: values.amount,
        payment_method: selectedPaymentMethod,
        payment_date: values.payment_date.format('YYYY-MM-DD'),
        reference_number: values.reference_number || values.check_number || values.voucher_number,
        notes: values.notes,
        // بيانات إضافية حسب طريقة الدفع
        check_number: values.check_number,
        bank_name: values.bank_name,
        due_date: values.due_date ? values.due_date.format('YYYY-MM-DD') : null,
        bank_account_id: values.bank_account_id,
        created_by: userId
      }

      if (window.electronAPI) {
        const response = await (window.electronAPI as any).createCustomerPayment(paymentData)
        if (response.success) {
          messageApi.success('تم تسجيل الدفعة بنجاح')
          setPaymentModalVisible(false)
          paymentForm.resetFields()
          loadInvoices() // إعادة تحميل الفواتير لتحديث المبالغ
          loadInvoicePayments(selectedInvoiceForPayment.id) // تحديث المدفوعات
        } else {
          messageApi.error(response.message || 'فشل في تسجيل الدفعة')
        }
      }
    } catch (error) {
      Logger.error('PaintInvoicesManagement', 'خطأ في تسجيل الدفعة:', error)
      messageApi.error('حدث خطأ أثناء تسجيل الدفعة')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'paid': return 'green'
      case 'partial': return 'blue'
      case 'overdue': return 'red'
      case 'cancelled': return 'default'
      default: return 'blue'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'معلقة'
      case 'paid': return 'مدفوعة'
      case 'partial': return 'جزئية'
      case 'overdue': return 'متأخرة'
      case 'cancelled': return 'ملغية'
      default: return status
    }
  }

  const columns = [
    {
      title: 'رقم الفاتورة',
      dataIndex: 'invoice_number',
      key: 'invoice_number',
      width: 120,
    },
    {
      title: 'رقم الأمر',
      dataIndex: 'order_number',
      key: 'order_number',
      width: 120,
    },
    {
      title: 'العميل',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 200,
    },
    {
      title: 'تاريخ الفاتورة (ميلادي)',
      dataIndex: 'invoice_date',
      key: 'invoice_date',
      width: 130,
      render: (date: string) => DateUtils.formatForDisplay(date),
    },
    {
      title: 'المساحة الإجمالية (م²)',
      dataIndex: 'total_area',
      key: 'total_area',
      width: 150,
      render: (area: number) => area.toFixed(2) + ' م²',
    },
    {
      title: 'حالة الدفع',
      key: 'payment_status',
      width: 150,
      render: (_: any, record: PaintInvoice) => (
        <PaymentStatusTag
          totalAmount={record.total_amount || 0}
          paidAmount={record.paid_amount || 0}
          dueDate={record.due_date}
          size="small"
        />
      )
    },
    {
      title: 'المبالغ',
      key: 'amounts',
      width: 200,
      render: (_: any, record: PaintInvoice) => (
        <RemainingAmountDisplay
          totalAmount={record.total_amount || 0}
          paidAmount={record.paid_amount || 0}
          dueDate={record.due_date}
          size="small"
          showProgress={false}
        />
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 300,
      render: (_: any, record: PaintInvoice) => (
        <Space size="small">
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedInvoice(record)
              setDetailsModalVisible(true)
            }}
            title="عرض التفاصيل"
          />
          {record.remaining_amount > 0 && (
            <Button
              type="default"
              size="small"
              icon={<CreditCardOutlined />}
              onClick={() => showPaymentModal(record)}
              title="تسجيل دفعة"
              style={{ color: '#52c41a', borderColor: '#52c41a' }}
            />
          )}
          <InvoicePrintButton
            invoiceData={{
              id: record.id,
              invoiceNumber: record.invoice_number,
              invoiceDate: record.invoice_date,
              customerName: record.customer_name || '',
              customerAddress: '', // يمكن إضافة عنوان العميل لاحقاً
              items: [], // سيتم تحميلها ديناميكياً
              subtotal: record.subtotal || 0,
              discount: record.discount_amount || 0,
              tax: record.tax_amount || 0,
              total: record.total_amount || 0,
              paid: record.paid_amount || 0,
              remaining: record.remaining_amount || 0,
              notes: record.notes || ''
            }}
            invoiceType="sales" // استخدام نوع مدعوم
            size="small"
            buttonText="طباعة"
            loadInvoiceItems={async () => {
              // تحميل أصناف فاتورة الدهان
              try {
                if (window.electronAPI) {
                  // استخدام API موجود أو إنشاء بيانات من الفاتورة
                  const response = await window.electronAPI.getPaintInvoices?.()
                  if (response?.success && Array.isArray(response.data)) {
                    const invoice = response.data.find((inv: any) => inv.id === record.id)
                    if (invoice) {
                      return [{
                        id: 1,
                        name: 'خدمة دهان',
                        description: `دهان ${invoice.paint_type_name || ''} - ${invoice.color || ''}`,
                        quantity: invoice.area_sqm || 1,
                        unit: 'متر مربع',
                        unitPrice: (invoice.total_amount || 0) / (invoice.area_sqm || 1),
                        total: invoice.total_amount || 0
                      }]
                    }
                  }
                }
                // بيانات افتراضية إذا لم تتوفر API
                return [{
                  id: 1,
                  name: 'خدمة دهان',
                  description: `دهان للعميل ${record.customer_name}`,
                  quantity: record.total_area || 1,
                  unit: 'متر مربع',
                  unitPrice: (record.total_amount || 0) / (record.total_area || 1),
                  total: record.total_amount || 0
                }]
              } catch (error) {
                console.error('خطأ في تحميل أصناف فاتورة الدهان:', error)
                return []
              }
            }}
            loadSupplierData={async () => {
              // تحميل بيانات العميل
              try {
                if (window.electronAPI && record.customer_id) {
                  const response = await window.electronAPI.getCustomers?.()
                  if (response?.success && Array.isArray(response.data)) {
                    const customer = response.data.find((cust: any) => cust.id === record.customer_id)
                    if (customer) {
                      return {
                        name: customer.name || record.customer_name || '',
                        address: customer.address || '',
                        phone: customer.phone || '',
                        email: customer.email || ''
                      }
                    }
                  }
                }
                // بيانات افتراضية
                return {
                  name: record.customer_name || 'عميل',
                  address: '',
                  phone: '',
                  email: ''
                }
              } catch (error) {
                console.error('خطأ في تحميل بيانات العميل:', error)
                return {
                  name: record.customer_name || 'عميل',
                  address: '',
                  phone: '',
                  email: ''
                }
              }
            }}
            onPrintSuccess={() => messageApi.success('تم طباعة فاتورة الدهان بنجاح')}
            onPrintError={() => messageApi.error('فشل في طباعة فاتورة الدهان')}
          />
          <Select
            size="small"
            value={record.status}
            onChange={(status) => updateInvoiceStatus(record.id, status)}
            style={{ width: 100 }}
          >
            <Option value="pending">معلقة</Option>
            <Option value="paid">مدفوعة</Option>
            <Option value="partial">جزئية</Option>
            <Option value="overdue">متأخرة</Option>
            <Option value="cancelled">ملغية</Option>
          </Select>
          <Popconfirm
            title="هل أنت متأكد من حذف هذه الفاتورة؟"
            description="سيتم حذف الفاتورة نهائياً ولا يمكن التراجع عن هذا الإجراء"
            onConfirm={() => handleDeleteInvoice(record.id)}
            okText="نعم، احذف"
            cancelText="إلغاء"
            okType="danger"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
              title="حذف الفاتورة"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const stats = {
    total: invoices.length,
    pending: invoices.filter(i => i.status === 'pending').length,
    paid: invoices.filter(i => i.status === 'paid').length,
    overdue: invoices.filter(i => i.status === 'overdue').length,
    totalAmount: invoices.reduce((sum, i) => sum + i.total_amount, 0),
    paidAmount: invoices.reduce((sum, i) => sum + i.paid_amount, 0),
    remainingAmount: invoices.reduce((sum, i) => sum + i.remaining_amount, 0),
    totalArea: invoices.reduce((sum, i) => sum + i.total_area, 0),
    averageInvoiceAmount: invoices.length > 0 ? invoices.reduce((sum, i) => sum + i.total_amount, 0) / invoices.length : 0,
    paymentRate: invoices.length > 0 ? (invoices.reduce((sum, i) => sum + i.paid_amount, 0) / invoices.reduce((sum, i) => sum + i.total_amount, 0)) * 100 : 0
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h1 style={{ margin: 0, color: '#722ed1', fontSize: '28px' }}>
            <DollarOutlined style={{ marginLeft: '12px' }} />
            إدارة فواتير الدهان
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '16px' }}>
            إنشاء فواتير من أوامر الدهان المكتملة
          </p>
        </div>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
          size="large"
          style={{ borderRadius: '8px' }}
        >
          العودة
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="إجمالي الفواتير"
              value={stats.total}
              prefix={<DollarOutlined />}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="في الانتّار"
              value={stats.pending}
              valueStyle={{ color: '#faad14' }}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="مدفوعة"
              value={stats.paid}
              valueStyle={{ color: '#52c41a' }}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="متأخرة"
              value={stats.overdue}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="إجمالي المبلغ"
              value={stats.totalAmount}
              precision={2}
              suffix="₪"
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="المبلغ المدفوع"
              value={stats.paidAmount}
              precision={2}
              suffix="₪"
              valueStyle={{ color: '#52c41a' }}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="المبلغ المتبقي"
              value={stats.remainingAmount}
              precision={2}
              suffix="₪"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="إجمالي المساحة"
              value={stats.totalArea}
              precision={2}
              suffix="م²"
              valueStyle={{ color: '#722ed1' }}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="متوسط الفاتورة"
              value={stats.averageInvoiceAmount}
              precision={2}
              suffix="₪"
              valueStyle={{ color: '#1890ff' }}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={8} lg={4}>
          <StatsCard>
            <Statistic
              title="نسبة التحصيل"
              value={stats.paymentRate}
              precision={1}
              suffix="%"
              valueStyle={{ color: stats.paymentRate >= 80 ? '#52c41a' : stats.paymentRate >= 50 ? '#faad14' : '#ff4d4f' }}
            />
          </StatsCard>
        </Col>
      </Row>

      {/* ملخص المدفوعات الشامل */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <PaymentSummary
            invoices={invoices.map(invoice => ({
              totalAmount: invoice.total_amount,
              paidAmount: invoice.paid_amount,
              dueDate: invoice.due_date
            }))}
            title="ملخص فواتير الدهان"
            // showDetails={true}
          />
        </Col>
      </Row>

      <StyledCard
        title="قائمة فواتير الدهان"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              form.resetFields()
              generateInvoiceNumber()
              setModalVisible(true)
            }}
            style={{ borderRadius: '6px' }}
            disabled={availableOrders.length === 0}
          >
            إنشاء فاتورة
          </Button>
        }
      >
        {availableOrders.length === 0 && (
          <div style={{ 
            textAlign: 'center', 
            padding: '20px', 
            background: '#fff7e6', 
            border: '1px solid #ffd591',
            borderRadius: '6px',
            marginBottom: '16px'
          }}>
            <FileTextOutlined style={{ fontSize: '24px', color: '#faad14', marginBottom: '8px' }} />
            <p style={{ margin: 0, color: '#d48806' }}>
              لا توجد أوامر دهان مكتملة متاحة للفوترة
            </p>
          </div>
        )}
        
        <Table
          columns={columns}
          dataSource={invoices}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => 'إجمالي ' + total + ' فاتورة',
          }}
          scroll={{ x: 1500 }}
        />
      </StyledCard>

      {/* نموذج إنشاء فاتورة */}
      <Modal
        title="إنشاء فاتورة دهان جديدة"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{ marginTop: '20px' }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="invoice_number"
                label="رقم الفاتورة"
                rules={[{ required: true, message: 'يرجى إدخال رقم الفاتورة' }]}
              >
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="order_id"
                label="أمر الدهان"
                rules={[{ required: true, message: 'يرجى اختيار أمر الدهان' }]}
              >
                <Select placeholder="اختر أمر الدهان">
                  {availableOrders.map(order => (
                    <Option key={order.id} value={order.id}>
                      {order.order_number} - {order.customer_name} (₪{order.total_amount.toFixed(2)})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="invoice_date"
                label="تاريخ الفاتورة"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الفاتورة' }]}
                initialValue={dayjs()}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="due_date"
                label="تاريخ الاستحقاق"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="tax_rate"
                label="نسبة الضريبة (%)"
                initialValue={0}
              >
                <InputNumber
                  min={0}
                  max={100}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="discount_rate"
                label="نسبة الخصم (%)"
                initialValue={0}
              >
                <InputNumber
                  min={0}
                  max={100}
                  precision={2}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider>معلومات الدفع</Divider>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="payment_type"
                label="نوع الدفع"
                rules={[{ required: true, message: 'يرجى اختيار نوع الدفع' }]}
                initialValue="cash"
              >
                <Select placeholder="اختر نوع الدفع">
                  <Option value="cash">نقدي</Option>
                  <Option value="credit">آجل</Option>
                  <Option value="partial">جزئي</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="paid_amount" label="المبلغ المدفوع">
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  formatter={value => ('₪ ' + value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => (parseFloat((value || '').replace(/₪\s?|(,*)/g, '')) || 0) as any}
                  placeholder="المبلغ المدفوع"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="remaining_amount" label="المبلغ المتبقي">
                <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                  prevValues.paid_amount !== currentValues.paid_amount
                }>
                  {({ getFieldValue }) => {
                    const paidAmount = getFieldValue('paid_amount') || 0
                    // سيتم حساب المجموع من أمر الدهان
                    const remaining = 0 - paidAmount // سيتم تحديثه لاحقاً

                    return (
                      <InputNumber
                        style={{
                          width: '100%',
                          color: remaining > 0 ? '#ff4d4f' : '#52c41a'
                        }}
                        value={remaining}
                        disabled
                        formatter={value => ('₪ ' + value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      />
                    )
                  }}
                </Form.Item>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="payment_method" label="طريقة الدفع">
                <Select placeholder="اختر طريقة الدفع" allowClear>
                  <Option value="cash">نقدي</Option>
                  <Option value="check">شيك</Option>
                  <Option value="bank_transfer">تحويل بنكي</Option>
                  <Option value="credit_card">بطاقة ائتمان</Option>
                  <Option value="receipt_voucher">سند قبض</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="payment_reference" label="مرجع الدفع">
                <Input placeholder="رقم الشيك، رقم التحويل، إلخ..." />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="ملاحّات"
          >
            <Input.TextArea rows={3} placeholder="ملاحّات إضافية" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => {
                setModalVisible(false)
                form.resetFields()
              }}>
                إلغاء
              </Button>
              {/* تم حذف زر الطباعة - MasterPrintButton */}
              <Button
                icon={<PrinterOutlined />}
                onClick={async () => {
                  try {
                    const values = await form.validateFields()
                    const selectedOrder = availableOrders.find(o => o.id === values.order_id)

                    if (!selectedOrder) {
                      messageApi.error('يرجى اختيار أمر دهان أولاً')
                      return
                    }

                    // إنشاء بيانات الفاتورة للطباعة
                    const invoiceData = {
                      id: 'preview',
                      title: `فاتورة دهان - مسودة`,
                      subtitle: `العميل: ${selectedOrder.customer_name}`,
                      number: values.invoice_number || 'مسودة',
                      date: values.invoice_date.format('YYYY-MM-DD'),
                      dueDate: values.due_date ? values.due_date.format('YYYY-MM-DD') : '',
                      customer: {
                        name: selectedOrder.customer_name,
                        address: '',
                        phone: '',
                        email: ''
                      },
                      items: [{
                        id: 1,
                        name: 'خدمة دهان',
                        description: `دهان ${(selectedOrder as any).paint_type_name || 'نوع الدهان'} - ${(selectedOrder as any).color || 'اللون'}`,
                        quantity: selectedOrder.total_area,
                        unit: 'متر مربع',
                        unitPrice: selectedOrder.total_amount / selectedOrder.total_area,
                        total: selectedOrder.total_amount
                      }],
                      subtotal: selectedOrder.total_amount,
                      taxAmount: (selectedOrder.total_amount * (values.tax_rate || 0)) / 100,
                      discountAmount: (selectedOrder.total_amount * (values.discount_rate || 0)) / 100,
                      total: selectedOrder.total_amount,
                      paidAmount: values.paid_amount || 0,
                      remainingAmount: selectedOrder.total_amount - (values.paid_amount || 0),
                      notes: values.notes || '',
                      metadata: {
                        generatedAt: new Date().toISOString(),
                        generatedBy: 'نظام الدهان',
                        invoiceType: 'paint',
                        orderNumber: selectedOrder.order_number
                      }
                    }

                    const printService = MasterPrintService.getInstance()
                    await printService.print(invoiceData, {
                      type: 'invoice',
                      subType: 'sales',
                      preview: true,
                      onSuccess: () => {
                        messageApi.success('تم إرسال الفاتورة للطباعة')
                      },
                      onError: (error) => {
                        messageApi.error(`فشل في الطباعة: ${error}`)
                      }
                    })

                  } catch (error) {
                    messageApi.error('يرجى ملء جميع الحقول المطلوبة')
                  }
                }}
              >
                طباعة سريعة
              </Button>
              <Button type="primary" htmlType="submit">
                إنشاء الفاتورة
              </Button>
              <Button
                type="default"
                htmlType="submit"
                onClick={async () => {
                  try {
                    const values = await form.validateFields()
                    const result = await handleSubmit(values)

                    if (result?.success) {
                      messageApi.success('تم حفظ الفاتورة، جاري الطباعة...')

                      const selectedOrder = availableOrders.find(o => o.id === values.order_id)
                      if (selectedOrder && result.data) {
                        // إنشاء بيانات الفاتورة للطباعة
                        const invoiceData = {
                          id: result.data.id,
                          title: `فاتورة دهان رقم ${result.data.invoice_number}`,
                          subtitle: `العميل: ${selectedOrder.customer_name}`,
                          number: result.data.invoice_number,
                          date: values.invoice_date.format('YYYY-MM-DD'),
                          dueDate: values.due_date ? values.due_date.format('YYYY-MM-DD') : '',
                          customer: {
                            name: selectedOrder.customer_name,
                            address: '',
                            phone: '',
                            email: ''
                          },
                          items: [{
                            id: 1,
                            name: 'خدمة دهان',
                            description: `دهان ${(selectedOrder as any).paint_type_name || 'نوع الدهان'} - ${(selectedOrder as any).color || 'اللون'}`,
                            quantity: selectedOrder.total_area,
                            unit: 'متر مربع',
                            unitPrice: selectedOrder.total_amount / selectedOrder.total_area,
                            total: selectedOrder.total_amount
                          }],
                          subtotal: selectedOrder.total_amount,
                          taxAmount: (selectedOrder.total_amount * (values.tax_rate || 0)) / 100,
                          discountAmount: (selectedOrder.total_amount * (values.discount_rate || 0)) / 100,
                          total: selectedOrder.total_amount,
                          paidAmount: values.paid_amount || 0,
                          remainingAmount: selectedOrder.total_amount - (values.paid_amount || 0),
                          notes: values.notes || '',
                          metadata: {
                            generatedAt: new Date().toISOString(),
                            generatedBy: 'نظام الدهان',
                            invoiceType: 'paint',
                            orderNumber: selectedOrder.order_number
                          }
                        }

                        const printService = MasterPrintService.getInstance()
                        await printService.print(invoiceData, {
                          type: 'invoice',
                          subType: 'sales',
                          onSuccess: () => {
                            messageApi.success('تم طباعة الفاتورة بنجاح')
                          },
                          onError: (error) => {
                            messageApi.error(`فشل في الطباعة: ${error}`)
                          }
                        })
                      }
                    }
                  } catch (error) {
                    messageApi.error('فشل في حفظ الفاتورة')
                  }
                }}
              >
                حفظ وطباعة
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نموذج عرض تفاصيل الفاتورة */}
      <Modal
        title="تفاصيل فاتورة الدهان"
        open={detailsModalVisible}
        onCancel={() => {
          setDetailsModalVisible(false)
          setSelectedInvoice(null)
        }}
        footer={[
          <Button key="close" onClick={() => {
            setDetailsModalVisible(false)
            setSelectedInvoice(null)
          }}>
            إغلاق
          </Button>
          /* تم حذف زر الطباعة - MasterPrintButton */
        ]}
        width={700}
      >
        {selectedInvoice && (
          <Descriptions bordered column={2} style={{ marginTop: '20px' }}>
            <Descriptions.Item label="رقم الفاتورة">{selectedInvoice.invoice_number}</Descriptions.Item>
            <Descriptions.Item label="رقم الأمر">{selectedInvoice.order_number}</Descriptions.Item>
            <Descriptions.Item label="العميل">{selectedInvoice.customer_name}</Descriptions.Item>
            <Descriptions.Item label="تاريخ الفاتورة">{DateUtils.formatForDisplay(selectedInvoice.invoice_date)}</Descriptions.Item>
            <Descriptions.Item label="تاريخ الاستحقاق">
              {selectedInvoice.due_date ? DateUtils.formatForDisplay(selectedInvoice.due_date) : 'غير محدد'}
            </Descriptions.Item>
            <Descriptions.Item label="الحالة">
              <Tag color={getStatusColor(selectedInvoice.status)}>
                {getStatusText(selectedInvoice.status)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="المساحة الإجمالية">{selectedInvoice.total_area.toFixed(2)} م²</Descriptions.Item>
            <Descriptions.Item label="المبلغ الفرعي">₪{selectedInvoice.subtotal.toFixed(2)}</Descriptions.Item>
            <Descriptions.Item label="الضريبة ({selectedInvoice.tax_rate}%)">₪{selectedInvoice.tax_amount.toFixed(2)}</Descriptions.Item>
            <Descriptions.Item label="الخصم ({selectedInvoice.discount_rate}%)">₪{selectedInvoice.discount_amount.toFixed(2)}</Descriptions.Item>
            <Descriptions.Item label="المبلغ الإجمالي">
              <strong style={{ color: '#1890ff', fontSize: '16px' }}>₪{selectedInvoice.total_amount.toFixed(2)}</strong>
            </Descriptions.Item>
            <Descriptions.Item label="المبلغ المدفوع">₪{selectedInvoice.paid_amount.toFixed(2)}</Descriptions.Item>
            <Descriptions.Item label="المبلغ المتبقي" span={2}>
              <strong style={{ color: selectedInvoice.remaining_amount > 0 ? '#ff4d4f' : '#52c41a', fontSize: '16px' }}>
                ₪{selectedInvoice.remaining_amount.toFixed(2)}
              </strong>
            </Descriptions.Item>
            {selectedInvoice.notes && (
              <Descriptions.Item label="ملاحّات" span={2}>{selectedInvoice.notes}</Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>

      {/* نافذة تسجيل الدفعة */}
      <Modal
        title={'تسجيل دفعة - ' + (selectedInvoiceForPayment?.invoice_number || '')}
        open={paymentModalVisible}
        onCancel={() => {
          setPaymentModalVisible(false)
          setSelectedInvoiceForPayment(null)
          paymentForm.resetFields()
        }}
        footer={null}
        width={700}
      >
        {selectedInvoiceForPayment && (
          <div>
            {/* معلومات الفاتورة */}
            <Card size="small" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Space direction="vertical" size="small">
                    <span><strong>العميل:</strong> {selectedInvoiceForPayment.customer_name}</span>
                    <span><strong>رقم الأمر:</strong> {selectedInvoiceForPayment.order_number}</span>
                    <span><strong>المساحة:</strong> {selectedInvoiceForPayment.total_area} م²</span>
                  </Space>
                </Col>
                <Col span={12}>
                  <RemainingAmountDisplay
                    totalAmount={selectedInvoiceForPayment.total_amount}
                    paidAmount={selectedInvoiceForPayment.paid_amount}
                    dueDate={selectedInvoiceForPayment.due_date}
                    showDetails={true}
                    size="small"
                  />
                </Col>
              </Row>
            </Card>

            {/* نموذج الدفع */}
            <Form
              form={paymentForm}
              layout="vertical"
              onFinish={handlePayment}
            >
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    name="payment_method"
                    label="طريقة الدفع"
                    rules={[{ required: true, message: 'يرجى اختيار طريقة الدفع' }]}
                  >
                    <PaymentMethodSelector
                      value={selectedPaymentMethod}
                      onChange={setSelectedPaymentMethod}
                      allowedMethods={[
                        PAYMENT_METHODS.CASH,
                        PAYMENT_METHODS.CHECK,
                        PAYMENT_METHODS.BANK_TRANSFER,
                        PAYMENT_METHODS.RECEIPT_VOUCHER
                      ]}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <PaymentForm
                paymentMethod={selectedPaymentMethod}
                maxAmount={selectedInvoiceForPayment.remaining_amount}
              />

              <div style={{ textAlign: 'left', marginTop: 24 }}>
                <Space>
                  <Button onClick={() => setPaymentModalVisible(false)}>
                    إلغاء
                  </Button>
                  <Button type="primary" htmlType="submit">
                    تسجيل الدفعة
                  </Button>
                </Space>
              </div>
            </Form>

            {/* ملخص المدفوعات */}
            {invoicePayments.length > 0 && (
              <div style={{ marginTop: 24 }}>
                <Divider>ملخص المدفوعات</Divider>
                <PaymentSummary
                  {...{
                    payments: invoicePayments,
                    totalAmount: selectedInvoiceForPayment.total_amount,
                    showDetails: true
                  } as any}
                />
              </div>
            )}

            {/* سجل المدفوعات السابقة */}
            {invoicePayments.length > 0 && (
              <div style={{ marginTop: 24 }}>
                <Divider>المدفوعات السابقة</Divider>
                <Table
                  dataSource={invoicePayments}
                  size="small"
                  pagination={false}
                  loading={loadingPayments}
                  columns={[
                    {
                      title: 'التاريخ',
                      dataIndex: 'payment_date',
                      key: 'payment_date',
                      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
                    },
                    {
                      title: 'المبلغ',
                      dataIndex: 'amount',
                      key: 'amount',
                      render: (amount: number) => '₪ ' + amount.toLocaleString()
                    },
                    {
                      title: 'طريقة الدفع',
                      dataIndex: 'payment_method',
                      key: 'payment_method'
                    },
                    {
                      title: 'المرجع',
                      dataIndex: 'reference_number',
                      key: 'reference_number',
                      render: (ref: string) => ref || '-'
                    }
                  ]}
                />
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default PaintInvoicesManagement
