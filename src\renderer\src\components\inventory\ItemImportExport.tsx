import React, { useState } from 'react'
import {
  Modal, Upload, Button, message, Progress, Table, Alert, Space, Typography,
  Divider, Card, Row, Col, Statistic, Tag
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  UploadOutlined, DownloadOutlined, FileExcelOutlined, 
  CheckCircleOutlined, ExclamationCircleOutlined, InfoCircleOutlined
} from '@ant-design/icons'
import * as XLSX from 'xlsx'
import { ApiResponse } from '../../types/global'

const { Title, Text } = Typography
const { Dragger } = Upload

interface ItemImportExportProps {
  visible: boolean
  onClose: () => void
  onImportComplete?: () => void
}

interface ImportResult {
  successCount: number
  errorCount: number
  errors: string[]
}

const ItemImportExport: React.FC<ItemImportExportProps> = ({
  visible,
  onClose,
  onImportComplete
}) => {
  const [importing, setImporting] = useState(false)
  const [exporting, setExporting] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [previewData, setPreviewData] = useState<any[]>([])
  const [showPreview, setShowPreview] = useState(false)

  // تصدير الأصناف
  const handleExport = async (format: 'excel' | 'csv') => {
    try {
      setExporting(true)
      
      if (!window.electronAPI) {
        message.error('واجهة Electron غير متوفرة')
        return
      }

      const response: ApiResponse = await window.electronAPI.exportItems(format)

      if (response.success && response.data) {
        if (response.data.length === 0) {
          message.warning('لا توجد أصناف للتصدير')
          return
        }

        // إنشاء ملف Excel
        const worksheet = XLSX.utils.json_to_sheet(response.data)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'الأصناف')

        // تحديد اسم الملف
        const timestamp = new Date().toISOString().split('T')[0]
        const fileName = `الأصناف_${timestamp}.${format === 'excel' ? 'xlsx' : 'csv'}`

        // تحميل الملف
        XLSX.writeFile(workbook, fileName)

        message.success(`تم تصدير ${response.data.length} صنف بنجاح`)
      } else {
        message.error(response.message || 'فشل في تصدير الأصناف')
      }
    } catch (error) {
      Logger.error('ItemImportExport', 'خطأ في تصدير الأصناف:', error)
      message.error('حدث خطأ أثناء تصدير الأصناف')
    } finally {
      setExporting(false)
    }
  }

  // معاينة ملف الاستيراد
  const handleFilePreview = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)
        
        setPreviewData(jsonData.slice(0, 10)) // أول 10 صفوف للمعاينة
        setShowPreview(true)
        message.success(`تم تحميل ${jsonData.length} صف من الملف`)
      } catch (error) {
        message.error('فشل في قراءة الملف')
      }
    }
    reader.readAsArrayBuffer(file)
    return false // منع الرفع التلقائي
  }

  // استيراد الأصناف
  const handleImport = async (file: File) => {
    try {
      setImporting(true)
      setImportResult(null)

      if (!window.electronAPI) {
        message.error('واجهة Electron غير متوفرة')
        return
      }

      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer)
          const workbook = XLSX.read(data, { type: 'array' })
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]
          const jsonData = XLSX.utils.sheet_to_json(worksheet)

          // تحويل البيانات إلى التنسيق المطلوب
          const itemsData = jsonData.map((row: any) => ({
            code: row['كود الصنف'] || row['code'] || '',
            name: row['اسم الصنف'] || row['name'] || '',
            description: row['الوصف'] || row['description'] || '',
            unit: row['الوحدة'] || row['unit'] || 'قطعة',
            cost_price: parseFloat(row['سعر التكلفة'] || row['cost_price'] || '0'),
            sale_price: parseFloat(row['سعر البيع'] || row['sale_price'] || '0'),
            min_stock_level: parseFloat(row['الحد الأدنى'] || row['min_stock_level'] || '0'),
            max_stock_level: parseFloat(row['الحد الأقصى'] || row['max_stock_level'] || '0'),
            barcode: row['الباركود'] || row['barcode'] || '',
            is_active: (row['الحالة'] || row['is_active'] || 'نشط') === 'نشط'
          }))

          const response: ApiResponse = await window.electronAPI.importItems(itemsData)
          
          if (response.success) {
            setImportResult(response.data)
            message.success(response.message)
            if (onImportComplete) {
              onImportComplete()
            }
          } else {
            message.error(response.message || 'فشل في استيراد الأصناف')
          }
        } catch (error) {
          Logger.error('ItemImportExport', 'خطأ في معالجة الملف:', error)
          message.error('حدث خطأ أثناء معالجة الملف')
        }
      }
      reader.readAsArrayBuffer(file)
    } catch (error) {
      Logger.error('ItemImportExport', 'خطأ في استيراد الأصناف:', error)
      message.error('حدث خطأ أثناء استيراد الأصناف')
    } finally {
      setImporting(false)
    }
  }

  // أعمدة جدول المعاينة
  const previewColumns = [
    { title: 'كود الصنف', dataIndex: 'كود الصنف', key: 'code' },
    { title: 'اسم الصنف', dataIndex: 'اسم الصنف', key: 'name' },
    { title: 'الوصف', dataIndex: 'الوصف', key: 'description' },
    { title: 'الوحدة', dataIndex: 'الوحدة', key: 'unit' },
    { title: 'سعر التكلفة', dataIndex: 'سعر التكلفة', key: 'cost_price' },
    { title: 'سعر البيع', dataIndex: 'سعر البيع', key: 'sale_price' }
  ]

  return (
    <Modal
      title="استيراد وتصدير الأصناف"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnHidden
    >
      <div style={{ padding: '16px 0' }}>
        {/* قسم التصدير */}
        <Card title="تصدير الأصناف" style={{ marginBottom: 24 }}>
          <Text type="secondary">
            تصدير جميع الأصناف النشطة إلى ملف Excel أو CSV
          </Text>
          <div style={{ marginTop: 16 }}>
            <Space>
              <Button
                type="primary"
                icon={<FileExcelOutlined />}
                loading={exporting}
                onClick={() => handleExport('excel')}
              >
                تصدير Excel
              </Button>
              <Button
                icon={<DownloadOutlined />}
                loading={exporting}
                onClick={() => handleExport('csv')}
              >
                تصدير CSV
              </Button>
            </Space>
          </div>
        </Card>

        {/* قسم الاستيراد */}
        <Card title="استيراد الأصناف">
          <Alert
            message="تعليمات الاستيراد"
            description={
              <div>
                <p>• يجب أن يحتوي الملف على الأعمدة التالية: كود الصنف، اسم الصنف</p>
                <p>• الأعمدة الاختيارية: الوصف، الوحدة، سعر التكلفة، سعر البيع، الحد الأدنى، الحد الأقصى، الباركود</p>
                <p>• يجب أن تكون أكواد الأصناف فريدة</p>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Dragger
            accept=".xlsx,.xls,.csv"
            beforeUpload={handleFilePreview}
            showUploadList={false}
            disabled={importing}
          >
            <p className="ant-upload-drag-icon">
              <UploadOutlined />
            </p>
            <p className="ant-upload-text">اضغط أو اسحب الملف هنا للمعاينة</p>
            <p className="ant-upload-hint">
              يدعم ملفات Excel (.xlsx, .xls) و CSV
            </p>
          </Dragger>

          {showPreview && previewData.length > 0 && (
            <div style={{ marginTop: 16 }}>
              <Title level={5}>معاينة البيانات (أول 10 صفوف)</Title>
              <Table
                dataSource={previewData}
                columns={previewColumns}
                pagination={false}
                size="small"
                scroll={{ x: true }}
                style={{ marginBottom: 16 }}
              />
              <Button
                type="primary"
                loading={importing}
                onClick={() => {
                  // إعادة قراءة الملف للاستيراد
                  const fileInput = document.createElement('input')
                  fileInput.type = 'file'
                  fileInput.accept = '.xlsx,.xls,.csv'
                  fileInput.onchange = (e) => {
                    const file = (e.target as HTMLInputElement).files?.[0]
                    if (file) {
                      handleImport(file)
                    }
                  }
                  fileInput.click()
                }}
              >
                تأكيد الاستيراد
              </Button>
            </div>
          )}

          {importing && (
            <div style={{ marginTop: 16 }}>
              <Progress percent={50} status="active" />
              <Text>جاري استيراد الأصناف...</Text>
            </div>
          )}

          {importResult && (
            <div style={{ marginTop: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="تم بنجاح"
                    value={importResult.successCount}
                    prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="فشل"
                    value={importResult.errorCount}
                    prefix={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
                    valueStyle={{ color: '#ff4d4f' }}
                  />
                </Col>
              </Row>

              {importResult.errors.length > 0 && (
                <div style={{ marginTop: 16 }}>
                  <Alert
                    message="أخطاء الاستيراد"
                    description={
                      <ul>
                        {importResult.errors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    }
                    type="warning"
                    showIcon
                  />
                </div>
              )}
            </div>
          )}
        </Card>
      </div>
    </Modal>
  )
}

export default ItemImportExport
