import { useCallback } from 'react'
import { App } from 'antd'
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from '../components/common/ErrorHandler'
import { logger as Logger } from './../utils/logger'

export interface UseErrorHandlerReturn {
  handleApiError: (error: any, context?: string) => void
  handleDataLoadError: (error: any, dataType: string) => void
  handleMissingHandler: (handlerName: string) => void
  handleNetworkError: (error: any, operation: string) => void
  handleDatabaseError: (error: any, operation: string) => void
  handleValidationError: (errors: string[], context?: string) => void
  handlePermissionError: (action: string) => void
  handleGenericError: (error: any, fallbackMessage?: string) => void
}

/**
 * Hook مخصص لمعالجة الأخطاء بطريقة موحدة
 */
export const useErrorHandler = (): UseErrorHandlerReturn => {
  const { message, notification } = App.useApp()

  const handleApiError = useCallback((error: any, context: string = 'العملية') => {
    Logger.error('UseErrorHandler', '❌ خطأ في ${context}:', error)
    
    if (!window.electronAPI) {
      message.error('لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.')
      return
    }

    if (error?.response?.status === 404) {
      message.warning(`${context}: الخدمة غير متاحة حالياً`)
      return
    }

    if (error?.response?.status === 500) {
      message.error(`${context}: خطأ في الخادم`)
      return
    }

    if (error?.message?.includes('timeout')) {
      message.warning(`${context}: انتهت مهلة الاتصال`)
      return
    }

    if (error?.message?.includes('network')) {
      message.error(`${context}: مشكلة في الشبكة`)
      return
    }

    // رسالة خطأ عامة
    const errorMessage = error?.message || error?.toString() || 'حدث خطأ غير متوقع'
    message.error(`${context}: ${errorMessage}`)
  }, [message])

  const handleDataLoadError = useCallback((error: any, dataType: string) => {
    Logger.error('UseErrorHandler', '❌ خطأ في تحميل ${dataType}:', error)
    
    if (!window.electronAPI) {
      message.error(`لا يمكن تحميل ${dataType}. يرجى إعادة تشغيل التطبيق.`)
      return
    }

    if (error?.code === 'ENOENT') {
      message.warning(`${dataType}: الملف غير موجود`)
      return
    }

    if (error?.code === 'EACCES') {
      message.error(`${dataType}: ليس لديك صلاحية للوصول`)
      return
    }

    message.error(`فشل في تحميل ${dataType}. يرجى المحاولة مرة أخرى.`)
  }, [message])

  const handleMissingHandler = useCallback((handlerName: string) => {
    Logger.warn('UseErrorHandler', '⚠️ المعالج ${handlerName} غير متاح')
    message.warning(`الوظيفة "${handlerName}" غير متاحة حالياً. سيتم تطويرها قريباً.`)
  }, [message])

  const handleNetworkError = useCallback((error: any, operation: string) => {
    Logger.error('UseErrorHandler', '❌ خطأ شبكة في ${operation}:', error)
    
    if (error?.code === 'NETWORK_ERROR') {
      message.error(`${operation}: مشكلة في الاتصال بالشبكة`)
      return
    }

    if (error?.code === 'TIMEOUT') {
      message.warning(`${operation}: انتهت مهلة الاتصال`)
      return
    }

    message.error(`${operation}: فشل في الاتصال`)
  }, [message])

  const handleDatabaseError = useCallback((error: any, operation: string) => {
    Logger.error('UseErrorHandler', '❌ خطأ قاعدة بيانات في ${operation}:', error)
    
    if (error?.code === 'SQLITE_BUSY') {
      message.warning(`${operation}: قاعدة البيانات مشغولة، يرجى المحاولة مرة أخرى`)
      return
    }

    if (error?.code === 'SQLITE_LOCKED') {
      message.error(`${operation}: قاعدة البيانات مقفلة`)
      return
    }

    if (error?.code === 'SQLITE_CORRUPT') {
      message.error(`${operation}: قاعدة البيانات تالفة`)
      return
    }

    message.error(`${operation}: خطأ في قاعدة البيانات`)
  }, [message])

  const handleValidationError = useCallback((errors: string[], context: string = 'البيانات') => {
    Logger.warn('UseErrorHandler', '⚠️ أخطاء تحقق في ${context}:', errors)

    if (errors.length === 1) {
      message.error(`${context}: ${errors[0]}`)
      return
    }

    const errorList = errors.map((error, index) => `${index + 1}. ${error}`).join('\n')

    notification.warning({
      message: `أخطاء في ${context}`,
      description: `يرجى تصحيح الأخطاء التالية:\n${errorList}`,
      duration: 10
    })
  }, [message, notification])

  const handlePermissionError = useCallback((action: string) => {
    Logger.warn('UseErrorHandler', '⚠️ ليس لديك صلاحية لـ: ${action}')
    message.warning(`ليس لديك صلاحية لتنفيذ هذا الإجراء: ${action}`)
  }, [message])

  const handleGenericError = useCallback((error: any, fallbackMessage: string = 'حدث خطأ غير متوقع') => {
    Logger.error('UseErrorHandler', '❌ خطأ عام:', error)
    
    const errorMessage = error?.message || error?.toString() || fallbackMessage
    message.error(errorMessage)
  }, [message])

  return {
    handleApiError,
    handleDataLoadError,
    handleMissingHandler,
    handleNetworkError,
    handleDatabaseError,
    handleValidationError,
    handlePermissionError,
    handleGenericError
  }
}

export default useErrorHandler
