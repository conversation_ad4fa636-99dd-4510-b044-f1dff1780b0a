import React, { useState } from 'react'
import { Button, Dropdown, App } from 'antd'
import { PrinterOutlined, EyeOutlined } from '@ant-design/icons'
import { MasterPrintService } from '../../services/MasterPrintService'
import { PrintData } from '../../types/print'

interface InvoicePrintButtonProps {
  // بيانات الفاتورة
  invoiceData: {
    id: string | number
    invoiceNumber: string
    invoiceDate: string
    dueDate?: string
    customerName: string
    customerAddress?: string
    customerPhone?: string
    customerEmail?: string
    items: Array<{
      id?: string | number
      name: string
      description?: string
      quantity: number
      unit?: string
      unitPrice: number
      discount?: number
      tax?: number
      total: number
    }>
    subtotal: number
    discount?: number
    tax?: number
    total: number
    paid?: number
    remaining?: number
    notes?: string
    terms?: string
  }

  // نوع الفاتورة
  invoiceType?: 'sales' | 'purchase'

  // إعدادات المظهر
  buttonText?: string
  size?: 'small' | 'middle' | 'large'
  disabled?: boolean
  showDropdown?: boolean

  // دالة تحميل العناصر ديناميكياً (اختيارية)
  loadInvoiceItems?: () => Promise<Array<{
    id?: string | number
    name: string
    description?: string
    quantity: number
    unit?: string
    unitPrice: number
    discount?: number
    tax?: number
    total: number
  }>>

  // دالة تحميل بيانات العميل/المورد ديناميكياً (اختيارية)
  loadSupplierData?: () => Promise<{
    name: string
    address?: string
    phone?: string
    email?: string
  }>

  // callbacks
  onPrintSuccess?: () => void
  onPrintError?: (error: string) => void
}

const InvoicePrintButton: React.FC<InvoicePrintButtonProps> = ({
  invoiceData,
  invoiceType = 'sales',
  buttonText,
  size = 'middle',
  disabled = false,
  showDropdown = true,
  loadInvoiceItems,
  loadSupplierData,
  onPrintSuccess,
  onPrintError
}) => {
  const { message } = App.useApp()
  const [loading, setLoading] = useState(false)

  // دالة لإعداد بيانات الطباعة
  const preparePrintData = async (): Promise<PrintData> => {
    let items = invoiceData.items || []

    // إذا كانت العناصر فارغة ووجدت دالة تحميل، استخدمها
    if (items.length === 0 && loadInvoiceItems) {
      try {
        items = await loadInvoiceItems()
      } catch (error) {
        console.error('خطأ في تحميل عناصر الفاتورة:', error)
        onPrintError?.('فشل في تحميل عناصر الفاتورة')
        throw error
      }
    }

    // تحميل إعدادات الشركة من النظام
    let companyData = null
    try {
      if (window.electronAPI) {
        const settingsResponse = await window.electronAPI.getSettings()
        if (settingsResponse.success) {
          const settings = settingsResponse.data
          companyData = {
            name: settings.company_name || 'اسم الشركة',
            address: settings.company_address || '',
            phone: settings.company_phone || '',
            email: settings.company_email || '',
            website: settings.company_website || '',
            logo: settings.company_logo || null,
            taxNumber: settings.company_tax_number || ''
          }
        }
      }
    } catch (error) {
      console.warn('فشل في تحميل إعدادات الشركة:', error)
    }

    // تحسين تحويل بيانات الأصناف مع حساب المجاميع
    const processedItems = items.map(item => {
      const quantity = item.quantity || 0
      const unitPrice = item.unitPrice || 0
      const calculatedTotal = quantity * unitPrice

      return {
        id: item.id,
        name: item.name || 'غير محدد',
        description: item.description || '',
        quantity: quantity,
        unit: item.unit || 'قطعة',
        unitPrice: unitPrice,
        discount: item.discount || 0,
        tax: item.tax || 0,
        // التأكد من أن المجموع الفرعي محسوب بشكل صحيح (الكمية × سعر الوحدة)
        total: item.total || calculatedTotal
      }
    })

    // تحميل بيانات العميل/المورد إذا كانت الدالة متوفرة
    let customerData = {
      name: invoiceData.customerName,
      address: invoiceData.customerAddress,
      phone: invoiceData.customerPhone,
      email: invoiceData.customerEmail
    }

    if (loadSupplierData) {
      try {
        const supplierData = await loadSupplierData()
        customerData = {
          name: supplierData.name || invoiceData.customerName,
          address: supplierData.address,
          phone: supplierData.phone,
          email: supplierData.email
        }
      } catch (error) {
        console.warn('فشل في تحميل بيانات المورد/العميل:', error)
      }
    }

    // حساب المجموع الفرعي من العناصر الفعلية
    const calculatedSubtotal = processedItems.reduce((sum, item) => sum + (item.total || 0), 0)

    // استخدام المجموع المحسوب أو المجموع المحفوظ
    const subtotal = calculatedSubtotal > 0 ? calculatedSubtotal : (invoiceData.subtotal || 0)
    const discount = invoiceData.discount || 0
    const tax = invoiceData.tax || 0
    const total = invoiceData.total || (subtotal - discount + tax)
    const paid = invoiceData.paid || 0
    const remaining = invoiceData.remaining !== undefined ? invoiceData.remaining : (total - paid)

    return {
      id: invoiceData.id,
      title: invoiceType === 'sales' ? 'فاتورة مبيعات' : 'فاتورة مشتريات',
      number: invoiceData.invoiceNumber,
      date: invoiceData.invoiceDate,
      metadata: { dueDate: invoiceData.dueDate },
      company: companyData,
      customer: customerData,
      items: processedItems,
      subtotal: subtotal,
      discount: discount,
      tax: tax,
      total: total,
      paid: paid,
      remaining: remaining,
      notes: invoiceData.notes,
      terms: invoiceData.terms || 'شكراً لتعاملكم معنا. يرجى الدفع خلال 30 يوماً من تاريخ الفاتورة.'
    }
  }

  // تحميل إعدادات الطباعة من النظام
  const loadPrintSettings = async () => {
    try {
      if (window.electronAPI) {
        const settingsResponse = await window.electronAPI.getSettings()
        if (settingsResponse.success) {
          const settings = settingsResponse.data
          return {
            // إعدادات المحتوى
            showLogo: settings.print_show_logo !== 'false',
            logoSize: (settings.print_logo_size as 'small' | 'medium' | 'large' | 'extra-large') || 'medium',
            showHeader: settings.print_show_header !== 'false',
            showFooter: settings.print_show_footer !== 'false',
            showSignature: settings.print_show_signature === 'true',
            showTerms: settings.print_show_terms !== 'false',
            showQR: settings.print_show_qr === 'true',

            // إعدادات الصفحة
            pageSize: (settings.print_page_size as 'A4' | 'A5' | 'Letter' | 'A3') || 'A4',
            orientation: (settings.print_orientation as 'portrait' | 'landscape') || 'portrait',
            margins: {
              top: parseInt(settings.print_margin_top) || 20,
              right: parseInt(settings.print_margin_right) || 20,
              bottom: parseInt(settings.print_margin_bottom) || 20,
              left: parseInt(settings.print_margin_left) || 20
            },

            // إعدادات النص والتصميم
            fontSize: parseInt(settings.print_font_size) || 12,
            fontFamily: settings.print_font_family || 'Arial',
            primaryColor: settings.print_primary_color || (invoiceType === 'sales' ? '#fff3cd' : '#1890ff'),
            secondaryColor: settings.print_secondary_color || '#fff3cd',
            borderColor: settings.print_border_color || '#d9d9d9',
            backgroundColor: settings.print_background_color || '#ffffff',
            textColor: settings.print_text_color || '#000000',

            // إعدادات متقدمة
            copies: parseInt(settings.print_copies) || 1,
            quality: (settings.print_quality as 'draft' | 'normal' | 'high') || 'normal',
            autoSave: settings.print_auto_save === 'true',

            // إعدادات العلامة المائية
            watermark: settings.print_watermark === 'true',
            watermarkText: settings.print_watermark_text || 'ZET.IA',
            watermarkOpacity: parseFloat(settings.print_watermark_opacity) || 0.1,

            // نصوص مخصصة
            headerText: settings.print_header_text || '',
            footerText: settings.print_footer_text || '',
            logoPosition: (settings.print_logo_position as 'top-left' | 'top-center' | 'top-right') || 'top-left'
          }
        }
      }
    } catch (error) {
      console.warn('فشل في تحميل إعدادات الطباعة:', error)
    }

    // الإعدادات الافتراضية
    return {
      // إعدادات المحتوى
      showLogo: true,
      logoSize: 'medium' as const,
      showHeader: true,
      showFooter: true,
      showSignature: false,
      showTerms: true,
      showQR: false,

      // إعدادات الصفحة
      pageSize: 'A4' as const,
      orientation: 'portrait' as const,
      margins: { top: 20, right: 20, bottom: 20, left: 20 },

      // إعدادات النص والتصميم
      fontSize: 12,
      fontFamily: 'Arial',
      primaryColor: invoiceType === 'sales' ? '#52c41a' : '#1890ff',
      secondaryColor: '#52c41a',
      borderColor: '#d9d9d9',
      backgroundColor: '#ffffff',
      textColor: '#000000',

      // إعدادات متقدمة
      copies: 1,
      quality: 'normal' as const,
      autoSave: true,

      // إعدادات العلامة المائية
      watermark: false,
      watermarkText: 'ZET.IA',
      watermarkOpacity: 0.1,

      // نصوص مخصصة
      headerText: '',
      footerText: '',
      logoPosition: 'top-left' as const
    }
  }

  // دوال الطباعة
  const handleDirectPrint = async () => {
    try {
      setLoading(true)
      const printData = await preparePrintData()
      const printSettings = await loadPrintSettings()

      const printService = MasterPrintService.getInstance()
      await printService.print(printData, {
        type: 'invoice',
        subType: invoiceType,
        ...printSettings
      })

      message.success('تم إرسال الفاتورة للطباعة بنجاح')
      onPrintSuccess?.()
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في الطباعة'
      message.error(errorMessage)
      onPrintError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handlePreview = async () => {
    try {
      setLoading(true)
      const printData = await preparePrintData()
      const printSettings = await loadPrintSettings()

      const printService = MasterPrintService.getInstance()
      await printService.previewOnly(printData, {
        type: 'invoice',
        subType: invoiceType,
        ...printSettings
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في المعاينة'
      message.error(errorMessage)
      onPrintError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // قائمة الخيارات المنسدلة
  const dropdownItems = [
    {
      key: 'preview',
      label: 'معاينة فقط (بدون طباعة)',
      icon: <EyeOutlined />,
      onClick: handlePreview
    },
    {
      key: 'print',
      label: 'طباعة مباشرة',
      icon: <PrinterOutlined />,
      onClick: handleDirectPrint
    }
  ]

  // إذا لم تكن هناك قائمة منسدلة، اعرض زر بسيط
  if (!showDropdown) {
    return (
      <Button
        type="primary"
        size={size}
        icon={<PrinterOutlined />}
        loading={loading}
        disabled={disabled}
        onClick={handleDirectPrint}
      >
        {buttonText || 'طباعة'}
      </Button>
    )
  }

  // عرض الزر مع القائمة المنسدلة
  return (
    <Dropdown
      menu={{ items: dropdownItems }}
      trigger={['click']}
      disabled={disabled || loading}
    >
      <Button
        type="primary"
        size={size}
        icon={<PrinterOutlined />}
        loading={loading}
        disabled={disabled}
      >
        {buttonText || 'طباعة'}
      </Button>
    </Dropdown>
  )
}

export default InvoicePrintButton
