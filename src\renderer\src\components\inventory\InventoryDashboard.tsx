﻿import React, { useState, useEffect } from 'react'
import {
  Card, Row, Col, Statistic, Alert, List, Tag, Typography, Space, Button,
  Progress, Tooltip, Badge, Spin, Modal
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  InboxOutlined, WarningOutlined, CheckCircleOutlined, ExclamationCircleOutlined,
  TruckOutlined, AppstoreOutlined, DollarOutlined, HistoryOutlined,
  ReloadOutlined, BellOutlined, ToolOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { ApiResponse } from '../../types/global'
import InventoryValueFix from './InventoryValueFix'

const { Title, Text } = Typography

const StyledCard = styled(Card)`
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
`

const StatisticCard = styled(Card)`
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  
  .ant-statistic-title {
    color: rgba(255, 255, 255, 0.8);
  }
  
  .ant-statistic-content {
    color: white;
  }
`

const AlertCard = styled(Card)`
  border-radius: 12px;
  border-left: 4px solid #ff4d4f;
  
  &.warning {
    border-left-color: #faad14;
  }
  
  &.info {
    border-left-color: #1890ff;
  }
`

interface InventoryDashboardProps {
  onNavigate?: (view: string) => void
}

const InventoryDashboard: React.FC<InventoryDashboardProps> = ({ onNavigate }) => {
  const [statistics, setStatistics] = useState<any>({})
  const [alerts, setAlerts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [showValueFix, setShowValueFix] = useState(false)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    setLoading(true)
    try {
      const [statsResponse, alertsResponse] = await Promise.all([
        window.electronAPI?.getInventoryStatistics(),
        window.electronAPI?.getInventoryAlerts()
      ])

      if (statsResponse?.success) {
        setStatistics(statsResponse.data)
      }

      if (alertsResponse?.success) {
        setAlerts(alertsResponse.data || [])
      }
    } catch (_error) {
      Logger.error('InventoryDashboard', 'خطأ في تحميل بيانات لوحة المعلومات:', _error)
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadDashboardData()
    setRefreshing(false)
  }

  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case 'low_stock':
        return <WarningOutlined style={{ color: '#faad14' }} />
      case 'over_stock':
        return <ExclamationCircleOutlined style={{ color: '#1890ff' }} />
      case 'stale_stock':
        return <HistoryOutlined style={{ color: '#ff4d4f' }} />
      default:
        return <BellOutlined />
    }
  }

  const getAlertColor = (alertType: string) => {
    switch (alertType) {
      case 'low_stock':
        return 'warning'
      case 'over_stock':
        return 'info'
      case 'stale_stock':
        return 'error'
      default:
        return 'default'
    }
  }

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>جارٍ تحمٍل لوحة معلومات المخزوْ...</Text>
        </div>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '24px' 
      }}>
        <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
          <InboxOutlined style={{ marginRight: 8 }} />
          لوحة معلومات المخزوْ
        </Title>
        <Space>
          <Button
            icon={<ToolOutlined />}
            onClick={() => setShowValueFix(true)}
            type="default"
          >
            إصلاح قٍم المخزوْ
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={refreshing}
            type="primary"
          >
            تحدٍث
          </Button>
        </Space>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <StatisticCard>
            <Statistic
              title="إجمالٍ الأصْاف"
              value={statistics.totalItems || 0}
              prefix={<AppstoreOutlined />}
            />
          </StatisticCard>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <StatisticCard>
            <Statistic
              title="إجمالٍ المخازْ"
              value={statistics.totalWarehouses || 0}
              prefix={<TruckOutlined />}
            />
          </StatisticCard>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <StatisticCard>
            <Statistic
              title="قٍمة المخزوْ"
              value={statistics.totalValue || 0}
              precision={2}
              suffix="₪"
              prefix={<DollarOutlined />}
            />
          </StatisticCard>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <StatisticCard>
            <Statistic
              title="الحركات الحدٍثة"
              value={statistics.recentMovements || 0}
              prefix={<HistoryOutlined />}
            />
          </StatisticCard>
        </Col>
      </Row>

      {/* Alert Summary */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={8}>
          <StyledCard>
            <Statistic
              title="مخزوْ مْخفض"
              value={statistics.lowStockItems || 0}
              valueStyle={{ color: '#faad14' }}
              prefix={<WarningOutlined />}
            />
            <Progress 
              percent={statistics.totalItems > 0 ? 
                Math.round((statistics.lowStockItems / statistics.totalItems) * 100) : 0
              }
              strokeColor="#faad14"
              size="small"
              style={{ marginTop: 8 }}
            />
          </StyledCard>
        </Col>
        <Col xs={24} sm={8}>
          <StyledCard>
            <Statistic
              title="مخزوْ زائد"
              value={statistics.overStockItems || 0}
              valueStyle={{ color: '#1890ff' }}
              prefix={<ExclamationCircleOutlined />}
            />
            <Progress 
              percent={statistics.totalItems > 0 ? 
                Math.round((statistics.overStockItems / statistics.totalItems) * 100) : 0
              }
              strokeColor="#1890ff"
              size="small"
              style={{ marginTop: 8 }}
            />
          </StyledCard>
        </Col>
        <Col xs={24} sm={8}>
          <StyledCard>
            <Statistic
              title="ْافد المخزوْ"
              value={statistics.outOfStockItems || 0}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<ExclamationCircleOutlined />}
            />
            <Progress 
              percent={statistics.totalItems > 0 ? 
                Math.round((statistics.outOfStockItems / statistics.totalItems) * 100) : 0
              }
              strokeColor="#ff4d4f"
              size="small"
              style={{ marginTop: 8 }}
            />
          </StyledCard>
        </Col>
      </Row>

      {/* Alerts List */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <StyledCard
            title={
              <Space>
                <BellOutlined />
                <span>التْبٍهات الْشطة</span>
                <Badge count={alerts.length} />
              </Space>
            }
            extra={
              <Button 
                size="small" 
                onClick={() => onNavigate?.('low-stock-report')}
              >
                عرض التقرٍر
              </Button>
            }
          >
            {alerts.length > 0 ? (
              <List
                dataSource={alerts.slice(0, 10)}
                renderItem={(alert) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={getAlertIcon(alert.alert_type)}
                      title={
                        <Space>
                          <Text strong>{alert.name}</Text>
                          <Tag color={getAlertColor(alert.alert_type)}>
                            {alert.alert_message}
                          </Tag>
                        </Space>
                      }
                      description={
                        <Space>
                          <Text type="secondary">كود: {alert.code}</Text>
                          <Text type="secondary">المخزْ: {alert.warehouse_name}</Text>
                          <Text type="secondary">الكمٍة: {alert.current_quantity} {alert.unit}</Text>
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <CheckCircleOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">لا توجد تْبٍهات حالٍاً</Text>
                </div>
              </div>
            )}
          </StyledCard>
        </Col>
        
        <Col xs={24} lg={8}>
          <StyledCard title="إجراط،ات سرٍعة">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button 
                block 
                type="primary" 
                icon={<AppstoreOutlined />}
                onClick={() => onNavigate?.('items')}
              >
                إدارة الأصْاف
              </Button>
              <Button 
                block 
                icon={<HistoryOutlined />}
                onClick={() => onNavigate?.('movements')}
              >
                حركات المخزوْ
              </Button>
              <Button 
                block 
                icon={<TruckOutlined />}
                onClick={() => onNavigate?.('warehouses')}
              >
                إدارة المخازْ
              </Button>
              <Button 
                block 
                icon={<InboxOutlined />}
                onClick={() => onNavigate?.('inventory')}
              >
                عرض المخزوْ
              </Button>
            </Space>
          </StyledCard>
        </Col>
      </Row>

      {/* Modal إصلاح قٍم المخزوْ */}
      <Modal
        title="إصلاح قٍم المخزوْ"
        open={showValueFix}
        onCancel={() => setShowValueFix(false)}
        footer={null}
        width={600}
      >
        <InventoryValueFix onClose={() => setShowValueFix(false)} />
      </Modal>
    </div>
  )
}

export default InventoryDashboard

