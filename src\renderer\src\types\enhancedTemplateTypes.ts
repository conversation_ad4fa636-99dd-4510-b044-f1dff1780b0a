/**
 * أنواع البيانات الموحدة للنظام المتكامل لتعديل القوالب والأعمدة
 * يجمع جميع الإعدادات في هيكل موحد ومتسق
 */

import { ColumnConfig } from '../components/common/ImprovedColumnEditor'

// ===== تكوين الأعمدة المحسن =====

export interface EnhancedColumnConfig {
  // الخصائص الأساسية
  key: string
  title: string
  dataIndex: string
  type?: 'text' | 'number' | 'currency' | 'date' | 'boolean' | 'tag'

  // إعدادات العرض
  visible: boolean
  width?: number | string
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  fixed?: 'left' | 'right' | false
  render?: any

  // إعدادات الطباعة المحسنة
  printVisible?: boolean
  printWidth?: number | string
  printAlign?: 'left' | 'center' | 'right'
  printOrder?: number

  // تنسيق الطباعة
  printFormat?: {
    bold?: boolean
    italic?: boolean
    underline?: boolean
    color?: string
    backgroundColor?: string
  }

  // تنسيق الأرقام
  numberFormat?: {
    decimals?: number
    thousandsSeparator?: boolean
    currency?: string
  }

  // تنسيق التاريخ
  dateFormat?: string
}

// ===== الإعدادات الأساسية =====

export interface PageSettings {
  pageSize: 'A4' | 'A5' | 'Letter' | 'Legal'
  orientation: 'portrait' | 'landscape'
  margins: {
    top: number
    bottom: number
    left: number
    right: number
  }
}

export interface FontSettings {
  fontSize: number
  fontFamily: string
  headerSize: number
  lineSpacing: number
}

export interface ColorSettings {
  primaryColor: string
  secondaryColor: string
  borderColor: string
  backgroundColor: string
  textColor: string
}

export interface DisplaySettings {
  showHeader: boolean
  showFooter: boolean
  showLogo: boolean
  showSignature: boolean
  showTerms: boolean
  showQR: boolean
}

export interface LayoutSettings {
  logoPosition: 'top-left' | 'top-center' | 'top-right'
  logoSize: number // تغيير إلى رقم للتوافق مع Slider
  borderWidth: number
  sectionSpacing: number
  tableWidth: number
}

export interface ContentSettings {
  headerText: string
  footerText: string
  watermark: boolean
  watermarkText: string
  watermarkOpacity: number
}

export interface QualitySettings {
  quality: 'draft' | 'normal' | 'high'
  copies: number
  autoSave: boolean
}

// ===== الإعدادات الموحدة =====

export interface UnifiedPrintSettings {
  // الإعدادات الأساسية
  page: PageSettings
  font: FontSettings
  colors: ColorSettings
  display: DisplaySettings
  layout: LayoutSettings
  content: ContentSettings
  quality: QualitySettings
  
  // معلومات إضافية
  version?: string
  lastUpdated?: string
  autoSync?: boolean
}

// ===== إعدادات القوالب =====

export interface TemplateMetadata {
  id: string
  name: string
  description: string
  type: 'invoice' | 'receipt' | 'report' | 'certificate' | 'custom'
  category?: string // فئة التقرير (financial, inventory, sales, etc.)
  isDefault: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
  
  // أنواع التقارير المدعومة
  supportedReportTypes?: string[]
  
  // معاينة القالب
  preview?: string
  thumbnail?: string
}

export interface TemplateInheritance {
  // هل يرث من الإعدادات العامة؟
  inheritsFromGlobal: boolean
  
  // الإعدادات المخصصة (تتجاوز الإعدادات العامة)
  customSettings: Partial<UnifiedPrintSettings>
  
  // الإعدادات المحفوظة فقط (للتوفير في المساحة)
  overrides: string[] // قائمة بأسماء الإعدادات المخصصة
}

export interface EnhancedTemplate {
  // معلومات القالب
  metadata: TemplateMetadata
  
  // نظام الوراثة
  inheritance: TemplateInheritance
  
  // إعدادات الأعمدة المخصصة
  columns?: EnhancedColumnConfig[]
  
  // إعدادات خاصة بنوع القالب
  typeSpecificSettings?: Record<string, any>
}

// ===== إعدادات الأعمدة المحسنة =====
// تم دمج جميع خصائص الأعمدة في التعريف الأساسي أعلاه لتجنب التكرار

// ===== واجهة المحرر المتكامل =====

export interface EnhancedTemplateEditorProps {
  // القالب المراد تعديله (undefined للقالب الجديد)
  template?: EnhancedTemplate
  
  // الأعمدة المتاحة للتعديل
  availableColumns?: EnhancedColumnConfig[]
  
  // الإعدادات العامة (للوراثة)
  globalSettings: UnifiedPrintSettings
  
  // معلومات التقرير (إذا كان التعديل من تقرير)
  reportContext?: {
    reportType: string
    reportCategory: string
    reportTitle: string
  }
  
  // callbacks
  onSave: (template: EnhancedTemplate) => void
  onCancel: () => void
  onPreview?: (template: EnhancedTemplate) => void
  onTest?: (template: EnhancedTemplate) => void
}

// ===== أنواع مساعدة =====

export type SettingsSection =
  | 'page'
  | 'fonts'
  | 'colors'
  | 'layout'
  | 'columns'
  | 'advanced'
  | 'preview'

export type TemplateEditMode = 'create' | 'edit' | 'duplicate' | 'inherit'

export interface SettingsValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// ===== دوال مساعدة للأنواع =====

export const createDefaultUnifiedSettings = (): UnifiedPrintSettings => ({
  page: {
    pageSize: 'A4',
    orientation: 'portrait',
    margins: { top: 20, bottom: 20, left: 20, right: 20 }
  },
  font: {
    fontSize: 12,
    fontFamily: 'Arial',
    headerSize: 18,
    lineSpacing: 1.5
  },
  colors: {
    primaryColor: '#1890ff',
    secondaryColor: '#fff3cd',
    borderColor: '#d9d9d9',
    backgroundColor: '#ffffff',
    textColor: '#000000'
  },
  display: {
    showHeader: true,
    showFooter: true,
    showLogo: true,
    showSignature: false,
    showTerms: true,
    showQR: false
  },
  layout: {
    logoPosition: 'top-left',
    logoSize: 100, // تغيير إلى رقم
    borderWidth: 1,
    sectionSpacing: 15,
    tableWidth: 100
  },
  content: {
    headerText: 'شركة المحاسبة المتقدمة',
    footerText: 'شكراً لتعاملكم معنا',
    watermark: false,
    watermarkText: 'ZET.IA',
    watermarkOpacity: 0.1
  },
  quality: {
    quality: 'normal',
    copies: 1,
    autoSave: true
  },
  version: '1.0.0',
  lastUpdated: new Date().toISOString(),
  autoSync: true
})

export const createEmptyTemplate = (): EnhancedTemplate => ({
  metadata: {
    id: `template-${Date.now()}`,
    name: '',
    description: '',
    type: 'report',
    isDefault: false,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  inheritance: {
    inheritsFromGlobal: true,
    customSettings: {},
    overrides: []
  },
  columns: []
})
