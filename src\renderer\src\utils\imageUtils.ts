/**
 * أدوات مساعدة للتعامل مع الصور
 */

import { SafeLogger as Logger } from './logger'

// الصيغ المدعومة للصور
export const SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/bmp',
  'image/webp',
  'image/svg+xml',
  'image/tiff',
  'image/tif',
  'image/avif'
] as const

// الحد الأقصى لحجم الصورة (5 ميجابايت)
export const MAX_IMAGE_SIZE = 5 * 1024 * 1024

// الحد الأقصى لأبعاد الصورة
export const MAX_IMAGE_DIMENSIONS = {
  width: 4000,
  height: 4000
}

/**
 * التحقق من صحة ملف الصورة
 */
export interface ImageValidationResult {
  isValid: boolean
  error?: string
  warnings?: string[]
}

/**
 * تشخيص مشاكل الصور
 */
export const diagnoseImageIssues = (imageSrc: string | null | undefined): {
  isValid: boolean
  issues: string[]
  suggestions: string[]
} => {
  const issues: string[] = []
  const suggestions: string[] = []

  if (!imageSrc) {
    issues.push('مصدر الصورة غير محدد')
    suggestions.push('تأكد من وجود مسار صحيح للصورة')
    return { isValid: false, issues, suggestions }
  }

  // فحص base64
  if (imageSrc.startsWith('data:image/')) {
    const parts = imageSrc.split(',')
    if (parts.length !== 2) {
      issues.push('تنسيق base64 غير صحيح')
      suggestions.push('تأكد من تنسيق data:image/type;base64,data')
    }

    const base64Data = parts[1]
    if (!base64Data || base64Data.length < 100) {
      issues.push('بيانات base64 قصيرة أو فارغة')
      suggestions.push('تأكد من تحويل الصورة بشكل صحيح')
    }
  } else {
    // فحص URL
    try {
      new URL(imageSrc)
    } catch {
      issues.push('مسار الصورة غير صحيح')
      suggestions.push('تأكد من صحة مسار الصورة أو URL')
    }
  }

  return {
    isValid: issues.length === 0,
    issues,
    suggestions
  }
}

/**
 * اختبار تحميل الصورة مع timeout لتجنب التجمد
 */
export const testImageLoad = (imageSrc: string, timeoutMs: number = 10000): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image()
    let isResolved = false

    // دالة للحل مرة واحدة فقط
    const resolveOnce = (result: boolean) => {
      if (!isResolved) {
        isResolved = true
        resolve(result)
      }
    }

    // Timeout لتجنب التجمد
    const timeoutId = setTimeout(() => {
      Logger.warn('ImageUtils', '⏰ انتهت مهلة تحميل الصورة')
      resolveOnce(false)
    }, timeoutMs)

    img.onload = () => {
      clearTimeout(timeoutId)
      Logger.info('ImageUtils', '✅ تم تحميل الصورة بنجاح')
      resolveOnce(true)
    }

    img.onerror = (error) => {
      clearTimeout(timeoutId)
      Logger.error('ImageUtils', '❌ فشل في تحميل الصورة:', error)
      resolveOnce(false)
    }

    // التحقق من صحة المصدر قبل التحميل
    if (!imageSrc || imageSrc.trim() === '') {
      clearTimeout(timeoutId)
      Logger.warn('ImageUtils', '⚠️ مصدر الصورة فارغ')
      resolveOnce(false)
      return
    }

    try {
      img.src = imageSrc
    } catch (error) {
      clearTimeout(timeoutId)
      Logger.error('ImageUtils', '❌ خطأ في تعيين مصدر الصورة:', error)
      resolveOnce(false)
    }
  })
}

export const validateImageFile = async (file: File): Promise<ImageValidationResult> => {
  const result: ImageValidationResult = {
    isValid: true,
    warnings: []
  }

  // التحقق من نوع الملف
  if (!file.type.startsWith('image/')) {
    return {
      isValid: false,
      error: 'الملف ليس صورة'
    }
  }

  // التحقق من الصيغة المدعومة
  if (!SUPPORTED_IMAGE_FORMATS.includes(file.type as any)) {
    return {
      isValid: false,
      error: `صيغة الصورة غير مدعومة: ${file.type}. الصيغ المدعومة: ${SUPPORTED_IMAGE_FORMATS.join(', ')}`
    }
  }

  // التحقق من حجم الملف
  if (file.size > MAX_IMAGE_SIZE) {
    return {
      isValid: false,
      error: `حجم الصورة كبير جداً (${(file.size / 1024 / 1024).toFixed(2)} ميجابايت). الحد الأقصى: ${MAX_IMAGE_SIZE / 1024 / 1024} ميجابايت`
    }
  }

  // التحقق من أبعاد الصورة
  try {
    const dimensions = await getImageDimensions(file)
    if (dimensions.width > MAX_IMAGE_DIMENSIONS.width || dimensions.height > MAX_IMAGE_DIMENSIONS.height) {
      result.warnings?.push(`أبعاد الصورة كبيرة (${dimensions.width}x${dimensions.height}). قد يؤثر ذلك على الأداء`)
    }
  } catch {
    return {
      isValid: false,
      error: 'فشل في قراءة أبعاد الصورة. الصورة قد تكون تالفة'
    }
  }

  return result
}

/**
 * الحصول على أبعاد الصورة
 */
export const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = document.createElement('img')
    const url = URL.createObjectURL(file)
    
    img.onload = () => {
      URL.revokeObjectURL(url)
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      })
    }
    
    img.onerror = () => {
      URL.revokeObjectURL(url)
      reject(new Error('فشل في تحميل الصورة'))
    }
    
    img.src = url
  })
}

/**
 * تحويل ملف إلى base64
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = () => {
      resolve(reader.result as string)
    }
    
    reader.onerror = () => {
      reject(new Error('فشل في قراءة الملف'))
    }
    
    reader.readAsDataURL(file)
  })
}

/**
 * تنسيق حجم الملف
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * الحصول على امتداد الملف من نوع MIME
 */
export const getFileExtensionFromMimeType = (mimeType: string): string => {
  const mimeToExt: { [key: string]: string } = {
    'image/jpeg': '.jpg',
    'image/jpg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/bmp': '.bmp',
    'image/webp': '.webp',
    'image/svg+xml': '.svg',
    'image/tiff': '.tiff',
    'image/tif': '.tif',
    'image/avif': '.avif'
  }
  
  return mimeToExt[mimeType] || '.jpg'
}

/**
 * إنشاء صورة مصغرة (thumbnail)
 */
export const createThumbnail = (
  file: File, 
  maxWidth: number = 200, 
  maxHeight: number = 200,
  quality: number = 0.8
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = document.createElement('img')
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      reject(new Error('فشل في إنشاء canvas context'))
      return
    }
    
    img.onload = () => {
      // حساب الأبعاد الجديدة مع الحفاّ على النسبة
      const ratio = Math.min(maxWidth / img.width, maxHeight / img.height)
      const newWidth = img.width * ratio
      const newHeight = img.height * ratio
      
      canvas.width = newWidth
      canvas.height = newHeight
      
      // رسم الصورة المصغرة
      ctx.drawImage(img, 0, 0, newWidth, newHeight)
      
      // تحويل إلى base64
      const thumbnail = canvas.toDataURL('image/jpeg', quality)
      resolve(thumbnail)
    }
    
    img.onerror = () => {
      reject(new Error('فشل في تحميل الصورة'))
    }
    
    img.src = URL.createObjectURL(file)
  })
}
