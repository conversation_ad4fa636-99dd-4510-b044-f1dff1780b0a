/**
 * مكون طباعة كتالوج المنتجات المحسن
 * يستخدم النظام الموحد للطباعة مع تخطيط احترافي
 */

import React, { useState } from 'react'
import {
  Button,
  Modal,
  Form,
  Select,
  Switch,
  InputNumber,
  Space,
  Divider,
  App,
  Tooltip,
  Checkbox,
  Row,
  Col
} from 'antd'
import { 
  PrinterOutlined, 
  SettingOutlined, 
  EyeOutlined,
  ShoppingOutlined 
} from '@ant-design/icons'
import { MasterPrintService, type PrintData, type EnhancedPrintOptions } from '../../services/MasterPrintService'
import { SafeLogger as Logger } from '../../utils/logger'
import type { Item, ItemImage } from '../../types/global'

interface EnhancedProductCatalogPrintProps {
  items: Item[]
  itemImages: ItemImage[]
  title?: string
  buttonText?: string
  size?: 'small' | 'middle' | 'large'
  disabled?: boolean
  onPrintSuccess?: () => void
  onPrintError?: (error: string) => void
}

const EnhancedProductCatalogPrint: React.FC<EnhancedProductCatalogPrintProps> = ({
  items,
  itemImages,
  title = 'كتالوج المنتجات',
  buttonText = 'طباعة الكتالوج',
  size = 'middle',
  disabled = false,
  onPrintSuccess,
  onPrintError
}) => {
  const { message } = App.useApp()
  const [form] = Form.useForm()
  const [settingsVisible, setSettingsVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  
  // إعدادات افتراضية
  const [printOptions, setPrintOptions] = useState<EnhancedPrintOptions>({
    type: 'catalog',
    subType: 'product',
    pageSize: 'A4',
    orientation: 'portrait',
    showLogo: true,
    showHeader: true,
    showFooter: true,
    fontSize: 11,
    primaryColor: '#1890ff'
  })

  const [catalogSettings, setCatalogSettings] = useState({
    layout: 'catalog' as 'grid' | 'list' | 'catalog',
    itemsPerPage: 12,
    showImages: true,
    showPrices: true,
    showCodes: true,
    showDescriptions: true,
    imageSize: 'medium' as 'small' | 'medium' | 'large',
    sortBy: 'name' as 'name' | 'code' | 'price',
    groupByCategory: false,
    showBarcodes: false,
    showStock: false
  })

  // الحصول على الصورة الأساسية للصنف
  const getPrimaryImage = (itemId: number): ItemImage | undefined => {
    return itemImages.find(img => img.item_id === itemId && img.is_primary) ||
           itemImages.find(img => img.item_id === itemId)
  }

  // تحويل الأصناف إلى تنسيق النظام الموحد
  const convertItemsToPrintFormat = (): PrintData['images'] => {
    return items.map(item => {
      const primaryImage = getPrimaryImage(item.id)
      return {
        id: item.id,
        name: item.name,
        path: primaryImage?.image_path || '/images/no-image.png',
        description: catalogSettings.showDescriptions ? (item.description || '') : '',
        category: item.category_name || 'غير مصنف',
        size: primaryImage?.image_size,
        uploadDate: primaryImage?.created_at,
        notes: primaryImage?.description,
        metadata: {
          itemId: item.id,
          code: catalogSettings.showCodes ? item.code : '',
          price: catalogSettings.showPrices ? `${item.sale_price || 0}` : '',
          unit: item.unit || 'قطعة',
          category: item.category_name || 'غير مصنف',
          barcode: catalogSettings.showBarcodes ? '' : '', // item.barcode غير متوفر
          stock: catalogSettings.showStock ? '0' : '', // item.quantity غير متوفر
          hasImage: !!primaryImage,
          purchasePrice: item.cost_price || 0,
          isActive: item.is_active
        }
      }
    })
  }

  // ترتيب الأصناف
  const sortItems = (items: Item[]): Item[] => {
    const sorted = [...items]
    
    switch (catalogSettings.sortBy) {
      case 'name':
        return sorted.sort((a, b) => a.name.localeCompare(b.name, 'ar'))
      case 'code':
        return sorted.sort((a, b) => a.code.localeCompare(b.code))
      case 'price':
        return sorted.sort((a, b) => (b.sale_price || 0) - (a.sale_price || 0))
      default:
        return sorted
    }
  }

  // إعداد بيانات الطباعة
  const preparePrintData = (): PrintData => {
    const _sortedItems = sortItems(items)
    const printImages = convertItemsToPrintFormat()
    
    return {
      title: title,
      subtitle: `إجمالي المنتجات: ${items.length}`,
      date: new Date().toLocaleDateString('ar-SA'),
      images: printImages,
      imageSettings: {
        layout: catalogSettings.layout === 'catalog' ? 'grid' : catalogSettings.layout,
        imagesPerPage: catalogSettings.itemsPerPage,
        showMetadata: true,
        imageQuality: 'high'
      },
      notes: `
        إعدادات الكتالوج:
        - التخطيط: ${catalogSettings.layout === 'grid' ? 'شبكة' : 'قائمة'}
        - عدد المنتجات في الصفحة: ${catalogSettings.itemsPerPage}
        - إظهار الصور: ${catalogSettings.showImages ? 'نعم' : 'لا'}
        - إظهار الأسعار: ${catalogSettings.showPrices ? 'نعم' : 'لا'}
        - إظهار الأكواد: ${catalogSettings.showCodes ? 'نعم' : 'لا'}
        - الترتيب حسب: ${catalogSettings.sortBy === 'name' ? 'الاسم' : catalogSettings.sortBy === 'code' ? 'الكود' : 'السعر'}
      `,
      metadata: {
        catalogType: 'products',
        totalItems: items.length,
        printType: 'product-catalog',
        settings: catalogSettings
      }
    }
  }

  // طباعة مباشرة
  const handleDirectPrint = async () => {
    if (items.length === 0) {
      message.warning('لا توجد منتجات للطباعة')
      return
    }

    setLoading(true)
    try {
      const printService = MasterPrintService.getInstance()
      const printData = preparePrintData()
      
      await printService.print(printData, {
        ...printOptions,
        onSuccess: () => {
          message.success('تم إرسال الكتالوج للطباعة بنجاح')
          onPrintSuccess?.()
        },
        onError: (error) => {
          message.error(`فشل في الطباعة: ${error}`)
          onPrintError?.(error)
        }
      })
      
    } catch (error) {
      Logger.error('EnhancedProductCatalogPrint', 'خطأ في الطباعة:', error)
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
      message.error(`حدث خطأ أثناء الطباعة: ${errorMessage}`)
      onPrintError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // معاينة قبل الطباعة
  const handlePreview = async () => {
    if (items.length === 0) {
      message.warning('لا توجد منتجات للمعاينة')
      return
    }

    setLoading(true)
    try {
      const printService = MasterPrintService.getInstance()
      const printData = preparePrintData()

      await printService.previewOnly(printData, {
        ...printOptions,
        onSuccess: () => {
          message.success('تم فتح معاينة الطباعة')
        },
        onError: (error) => {
          message.error(`فشل في المعاينة: ${error}`)
        }
      })

    } catch (error) {
      Logger.error('EnhancedProductCatalogPrint', 'خطأ في المعاينة:', error)
      message.error('حدث خطأ أثناء المعاينة')
    } finally {
      setLoading(false)
    }
  }

  // حفظ الإعدادات
  const handleSaveSettings = () => {
    form.validateFields().then(values => {
      setPrintOptions(prev => ({ ...prev, ...values.printOptions }))
      setCatalogSettings(prev => ({ ...prev, ...values.catalogSettings }))
      setSettingsVisible(false)
      message.success('تم حفظ الإعدادات')
    })
  }

  return (
    <>
      <Space.Compact>
        <Button
          type="primary"
          icon={<PrinterOutlined />}
          size={size}
          loading={loading}
          disabled={disabled || items.length === 0}
          onClick={handleDirectPrint}
        >
          {buttonText}
        </Button>
        
        <Tooltip title="معاينة">
          <Button
            icon={<EyeOutlined />}
            size={size}
            disabled={disabled || items.length === 0}
            onClick={handlePreview}
          />
        </Tooltip>
        
        <Tooltip title="إعدادات الطباعة">
          <Button
            icon={<SettingOutlined />}
            size={size}
            onClick={() => setSettingsVisible(true)}
          />
        </Tooltip>
      </Space.Compact>

      {/* مودال إعدادات الطباعة */}
      <Modal
        title={
          <Space>
            <ShoppingOutlined />
            إعدادات طباعة كتالوج المنتجات
          </Space>
        }
        open={settingsVisible}
        onCancel={() => setSettingsVisible(false)}
        onOk={handleSaveSettings}
        width={700}
        okText="حفظ الإعدادات"
        cancelText="إلغاء"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            printOptions: printOptions,
            catalogSettings: catalogSettings
          }}
        >
          <Divider>إعدادات الطباعة العامة</Divider>
          
          <Form.Item name={['printOptions', 'pageSize']} label="حجم الورقة">
            <Select>
              <Select.Option value="A4">A4</Select.Option>
              <Select.Option value="A3">A3</Select.Option>
              <Select.Option value="Letter">Letter</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name={['printOptions', 'orientation']} label="اتجاه الطباعة">
            <Select>
              <Select.Option value="portrait">عمودي</Select.Option>
              <Select.Option value="landscape">أفقي</Select.Option>
            </Select>
          </Form.Item>

          <Divider>إعدادات الكتالوج</Divider>

          <Form.Item name={['catalogSettings', 'layout']} label="تخطيط الكتالوج">
            <Select>
              <Select.Option value="catalog">كتالوج احترافي (مستحسن)</Select.Option>
              <Select.Option value="grid">شبكة المنتجات</Select.Option>
              <Select.Option value="list">قائمة مع التفاصيل</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name={['catalogSettings', 'itemsPerPage']} label="عدد المنتجات في الصفحة">
            <InputNumber min={4} max={24} />
          </Form.Item>

          <Form.Item name={['catalogSettings', 'sortBy']} label="ترتيب المنتجات حسب">
            <Select>
              <Select.Option value="name">الاسم</Select.Option>
              <Select.Option value="code">الكود</Select.Option>
              <Select.Option value="price">السعر</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item label="عناصر العرض">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['catalogSettings', 'showImages']} valuePropName="checked" style={{ margin: 0 }}>
                    <Checkbox>إظهار الصور</Checkbox>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['catalogSettings', 'showPrices']} valuePropName="checked" style={{ margin: 0 }}>
                    <Checkbox>إظهار الأسعار</Checkbox>
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['catalogSettings', 'showCodes']} valuePropName="checked" style={{ margin: 0 }}>
                    <Checkbox>إظهار الأكواد</Checkbox>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['catalogSettings', 'showDescriptions']} valuePropName="checked" style={{ margin: 0 }}>
                    <Checkbox>إظهار الأوصاف</Checkbox>
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['catalogSettings', 'showBarcodes']} valuePropName="checked" style={{ margin: 0 }}>
                    <Checkbox>إظهار الباركود</Checkbox>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['catalogSettings', 'showStock']} valuePropName="checked" style={{ margin: 0 }}>
                    <Checkbox>إظهار المخزون</Checkbox>
                  </Form.Item>
                </Col>
              </Row>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default EnhancedProductCatalogPrint
