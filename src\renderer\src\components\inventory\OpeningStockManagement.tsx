import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Form,
  Input,
  Select,
  InputNumber,
  Modal,
  Space,
  Row,
  Col,
  Statistic,
  Tag,
  Tooltip,
  DatePicker,
  Typography, App} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  ArrowLeftOutlined,
  PlusOutlined,
  EditOutlined,
  SaveOutlined,
  ReloadOutlined,
  PrinterOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  WarningOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select

interface OpeningStockItem {
  id?: number
  item_id: number
  item_name: string
  item_code: string
  warehouse_id: number
  warehouse_name: string
  unit: string
  opening_quantity: number
  opening_value: number
  unit_cost: number
  opening_date: string
  notes?: string
  created_at?: string
  updated_at?: string
}

interface OpeningStockManagementProps {
  onBack: () => void
}

const OpeningStockManagement: React.FC<OpeningStockManagementProps> = ({ onBack }) => {
  const { message: messageApi } = App.useApp()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [openingStockData, setOpeningStockData] = useState<OpeningStockItem[]>([])
  const [items, setItems] = useState<any[]>([])
  const [warehouses, setWarehouses] = useState<any[]>([])
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingRecord, setEditingRecord] = useState<OpeningStockItem | null>(null)
  const [statistics, setStatistics] = useState({
    totalItems: 0,
    totalQuantity: 0,
    totalValue: 0,
    processedWarehouses: 0
  })

  // تحميل البيانات الأولية
  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    setLoading(true)
    try {
      await Promise.all([
        loadOpeningStock(),
        loadItems(),
        loadWarehouses()
      ])
    } catch (error) {
      Logger.error('OpeningStockManagement', 'خطأ في تحميل البيانات:', error)
      messageApi.error('فشل في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const loadOpeningStock = async () => {
    try {
      const response = await window.electronAPI.getOpeningStock()
      if (response.success) {
        setOpeningStockData(response.data || [])
        calculateStatistics(response.data || [])
      }
    } catch (error) {
      Logger.error('OpeningStockManagement', 'خطأ في تحميل مخزون أول المدة:', error)
    }
  }

  const loadItems = async () => {
    try {
      const response = await window.electronAPI.getItems()
      if ((response as any).success) {
        setItems((response as any).data || [])
      }
    } catch (error) {
      Logger.error('OpeningStockManagement', 'خطأ في تحميل الأصناف:', error)
    }
  }

  const loadWarehouses = async () => {
    try {
      const response = await window.electronAPI.getWarehouses()
      if ((response as any).success) {
        setWarehouses((response as any).data || [])
      }
    } catch (error) {
      Logger.error('OpeningStockManagement', 'خطأ في تحميل المخازن:', error)
    }
  }

  const calculateStatistics = (data: OpeningStockItem[]) => {
    const stats = {
      totalItems: data.length,
      totalQuantity: data.reduce((sum, item) => sum + (item.opening_quantity || 0), 0),
      totalValue: data.reduce((sum, item) => sum + (item.opening_value || 0), 0),
      processedWarehouses: new Set(data.map(item => item.warehouse_id)).size
    }
    setStatistics(stats)
  }

  const handleSubmit = async (values: any) => {
    setLoading(true)
    try {
      const openingStockData = {
        ...values,
        item_id: parseInt(String(values.item_id)), // تحويل معرف الصنف إلى رقم صحيح
        warehouse_id: parseInt(String(values.warehouse_id)), // تحويل معرف المخزن إلى رقم صحيح
        opening_value: values.opening_quantity * values.unit_cost,
        opening_date: values.opening_date ? values.opening_date.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        id: editingRecord?.id
      }

      const response = editingRecord
        ? await window.electronAPI.updateOpeningStock(openingStockData)
        : await window.electronAPI.createOpeningStock(openingStockData)

      if (response.success) {
        messageApi.success(editingRecord ? 'تم تحديث مخزون أول المدة بنجاح' : 'تم إضافة مخزون أول المدة بنجاح')
        setIsModalVisible(false)
        form.resetFields()
        setEditingRecord(null)
        await loadOpeningStock()
      } else {
        messageApi.error(response.message || 'فشل في حفّ البيانات')
      }
    } catch (error) {
      Logger.error('OpeningStockManagement', 'خطأ في حفّ مخزون أول المدة:', error)
      messageApi.error('فشل في حفّ البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (record: OpeningStockItem) => {
    setEditingRecord(record)
    form.setFieldsValue({
      ...record,
      opening_date: record.opening_date ? dayjs(record.opening_date) : dayjs()
    })
    setIsModalVisible(true)
  }

  const handleDelete = async (id: number) => {
    try {
      const response = await window.electronAPI.deleteOpeningStock(id)
      if (response.success) {
        messageApi.success('تم حذف السجل بنجاح')
        await loadOpeningStock()
      } else {
        messageApi.error(response.message || 'فشل في حذف السجل')
      }
    } catch (error) {
      Logger.error('OpeningStockManagement', 'خطأ في حذف السجل:', error)
      messageApi.error('فشل في حذف السجل')
    }
  }

  const processOpeningStock = async () => {
    Modal.confirm({
      title: 'معالجة مخزون أول المدة',
      content: 'هل أنت متأكد من معالجة جميع سجلات مخزون أول المدة؟ سيتم تحديث المخزون الحالي وإنشاء حركات مخزون.',
      okText: 'نعم، معالجة',
      cancelText: 'إلغاء',
      onOk: async () => {
        setLoading(true)
        try {
          const response = await window.electronAPI.processOpeningStock()
          if (response.success) {
            messageApi.success('تم معالجة مخزون أول المدة بنجاح')
            await loadOpeningStock()
          } else {
            messageApi.error(response.message || 'فشل في معالجة مخزون أول المدة')
          }
        } catch (error) {
          Logger.error('OpeningStockManagement', 'خطأ في معالجة مخزون أول المدة:', error)
          messageApi.error('فشل في معالجة مخزون أول المدة')
        } finally {
          setLoading(false)
        }
      }
    })
  }

  // وّيفة الطباعة الموحدة
  const handlePrint = async () => {
    try {
      const { MasterPrintService } = await import('../../services/MasterPrintService')
      const printService = MasterPrintService.getInstance()

      const printData = {
        title: 'تقرير مخزون أول المدة',
        subtitle: `إجمالي السجلات: ${openingStockData.length}`,
        date: new Date().toLocaleDateString('ar-SA'),
        items: openingStockData.map(record => ({
          name: record.item_name,
          description: `${record.item_code} - ${record.warehouse_name}`,
          quantity: record.opening_quantity,
          unit: record.unit || 'قطعة',
          unitPrice: record.unit_cost || 0,
          total: record.opening_value || 0
        })),
        total: openingStockData.reduce((sum, record) => sum + (record.opening_value || 0), 0),
        notes: 'تقرير مخزون أول المدة'
      }

      await printService.print(printData, {
        type: 'report',
        subType: 'inventory',
        showLogo: true,
        showHeader: true,
        showFooter: true
      })

      messageApi.success('تم إرسال التقرير للطباعة')
    } catch (error) {
      Logger.error('OpeningStockManagement', 'خطأ في الطباعة:', error)
      messageApi.error('فشل في طباعة التقرير')
    }
  }

  // وّيفة التصدير إلى Excel
  const handleExportToExcel = async () => {
    try {
      const XLSX = await import('xlsx')

      const exportData = openingStockData.map(record => ({
        'كود الصنف': record.item_code,
        'اسم الصنف': record.item_name,
        'المخزن': record.warehouse_name,
        'الكمية': record.opening_quantity,
        'الوحدة': record.unit,
        'سعر الوحدة': record.unit_cost,
        'القيمة الإجمالية': record.opening_value,
        'تاريخ الإدخال': record.opening_date
      }))

      const worksheet = XLSX.utils.json_to_sheet(exportData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'مخزون أول المدة')

      const fileName = `مخزون_أول_المدة_${new Date().toLocaleDateString('ar-SA').replace(/\//g, '-')}.xlsx`
      XLSX.writeFile(workbook, fileName)

      messageApi.success('تم تصدير البيانات بنجاح')
    } catch (error) {
      Logger.error('OpeningStockManagement', 'خطأ في التصدير:', error)
      messageApi.error('حدث خطأ أثناء التصدير')
    }
  }

  const columns = [
    {
      title: 'كود الصنف',
      dataIndex: 'item_code',
      key: 'item_code',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>
    },
    {
      title: 'اسم الصنف',
      dataIndex: 'item_name',
      key: 'item_name',
      ellipsis: true
    },
    {
      title: 'المخزن',
      dataIndex: 'warehouse_name',
      key: 'warehouse_name',
      width: 150
    },
    {
      title: 'الكمية',
      dataIndex: 'opening_quantity',
      key: 'opening_quantity',
      width: 100,
      align: 'center' as const,
      render: (value: number) => <Text strong>{value?.toLocaleString()}</Text>
    },
    {
      title: 'سعر الوحدة',
      dataIndex: 'unit_cost',
      key: 'unit_cost',
      width: 120,
      align: 'center' as const,
      render: (value: number) => `${value?.toFixed(2)} ₪`
    },
    {
      title: 'القيمة الإجمالية',
      dataIndex: 'opening_value',
      key: 'opening_value',
      width: 140,
      align: 'center' as const,
      render: (value: number) => <Text strong style={{ color: '#52c41a' }}>{value?.toFixed(2)} ₪</Text>
    },
    {
      title: 'تاريخ أول المدة',
      dataIndex: 'opening_date',
      key: 'opening_date',
      width: 120,
      render: (date: string) => dayjs(date).format('DD/MM/YYYY')
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 120,
      align: 'center' as const,
      render: (_: any, record: OpeningStockItem) => (
        <Space>
          <Tooltip title="تعديل">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="حذف">
            <Button
              danger
              size="small"
              icon={<WarningOutlined />}
              onClick={() => record.id && handleDelete(record.id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
          📦 إدارة مخزون أول المدة
        </Title>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={onBack}
          size="large"
        >
          رجوع
        </Button>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="إجمالي الأصناف"
              value={statistics.totalItems}
              prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="إجمالي الكمية"
              value={statistics.totalQuantity}
              precision={0}
              prefix={<PlusOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="إجمالي القيمة"
              value={statistics.totalValue}
              precision={2}
              suffix="₪"
              prefix={<SaveOutlined style={{ color: '#faad14' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="المخازن المعالجة"
              value={statistics.processedWarehouses}
              prefix={<WarningOutlined style={{ color: '#722ed1' }} />}
            />
          </Card>
        </Col>
      </Row>

      {/* Action Buttons */}
      <div style={{ marginBottom: '16px', display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingRecord(null)
            form.resetFields()
            setIsModalVisible(true)
          }}
        >
          إضافة مخزون أول المدة
        </Button>
        <Button
          icon={<ReloadOutlined />}
          onClick={loadOpeningStock}
          loading={loading}
        >
          تحديث
        </Button>
        <Button
          type="primary"
          danger
          icon={<SaveOutlined />}
          onClick={processOpeningStock}
          disabled={openingStockData.length === 0}
        >
          معالجة مخزون أول المدة
        </Button>
      </div>

      {/* Main Table */}
      <Card
        title="مخزون أول المدة"
        extra={
          <Space>
            <Button
              icon={<PrinterOutlined />}
              onClick={handlePrint}
              disabled={openingStockData.length === 0}
            >
              طباعة
            </Button>
            <Button
              icon={<FileExcelOutlined />}
              onClick={handleExportToExcel}
              disabled={openingStockData.length === 0}
              style={{ color: '#52c41a', borderColor: '#52c41a' }}
            >
              تصدير Excel
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={openingStockData}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} من ${total} سجل`
          }}
          scroll={{ x: 1000 }}
          locale={{
            emptyText: 'لا توجد بيانات مخزون أول المدة'
          }}
        />
      </Card>

      {/* Add/Edit Modal */}
      <Modal
        title={editingRecord ? 'تعديل مخزون أول المدة' : 'إضافة مخزون أول المدة'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false)
          form.resetFields()
          setEditingRecord(null)
        }}
        footer={null}
        width={600}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            opening_date: dayjs(),
            opening_quantity: 0,
            unit_cost: 0
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="item_id"
                label="الصنف"
                rules={[{ required: true, message: 'يرجى اختيار الصنف' }]}
              >
                <Select
                  placeholder="اختر الصنف"
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ||
                    option?.value?.toString().includes(input)) || false
                  }
                >
                  {items.map(item => (
                    <Option key={item.id} value={item.id}>
                      {item.code} - {item.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="warehouse_id"
                label="المخزن"
                rules={[{ required: true, message: 'يرجى اختيار المخزن' }]}
              >
                <Select
                  placeholder="اختر المخزن"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                  }
                >
                  {warehouses.map(warehouse => (
                    <Option key={warehouse.id} value={warehouse.id}>
                      {warehouse.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="opening_quantity"
                label="الكمية"
                rules={[
                  { required: true, message: 'يرجى إدخال الكمية' },
                  { type: 'number', min: 0, message: 'الكمية يجب أن تكون أكبر من أو تساوي صفر' }
                ]}
              >
                <InputNumber
                  placeholder="الكمية"
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="unit_cost"
                label="سعر الوحدة"
                rules={[
                  { required: true, message: 'يرجى إدخال سعر الوحدة' },
                  { type: 'number', min: 0, message: 'السعر يجب أن يكون أكبر من أو يساوي صفر' }
                ]}
              >
                <InputNumber
                  placeholder="سعر الوحدة"
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  addonAfter="₪"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="opening_date"
                label="تاريخ أول المدة"
                rules={[{ required: true, message: 'يرجى اختيار التاريخ' }]}
              >
                <DatePicker
                  placeholder="اختر التاريخ"
                  style={{ width: '100%' }}
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="ملاحّات"
          >
            <Input.TextArea
              placeholder="ملاحّات إضافية (اختياري)"
              rows={3}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<SaveOutlined />}
              >
                {editingRecord ? 'تحديث' : 'حفّ'}
              </Button>
              <Button
                onClick={() => {
                  setIsModalVisible(false)
                  form.resetFields()
                  setEditingRecord(null)
                }}
              >
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default OpeningStockManagement
