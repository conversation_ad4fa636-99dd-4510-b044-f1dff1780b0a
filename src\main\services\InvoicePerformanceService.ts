import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { Logger } from '../utils/logger'

export class InvoicePerformanceService {
  private static instance: InvoicePerformanceService
  private db: any

  private constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
    this.initializeIndexes()
  }

  public static getInstance(): InvoicePerformanceService {
    if (!InvoicePerformanceService.instance) {
      InvoicePerformanceService.instance = new InvoicePerformanceService()
    }
    return InvoicePerformanceService.instance
  }

  // إنشاء الفهارس لتحسين الأداء
  private initializeIndexes(): void {
    try {
      Logger.info('InvoicePerformanceService', 'إنشاء فهارس قاعدة البيانات...')

      // فهارس فواتير المبيعات
      const salesIndexes = [
        'CREATE INDEX IF NOT EXISTS idx_sales_invoices_date ON sales_invoices(invoice_date)',
        'CREATE INDEX IF NOT EXISTS idx_sales_invoices_customer ON sales_invoices(customer_id)',
        'CREATE INDEX IF NOT EXISTS idx_sales_invoices_status ON sales_invoices(status)',
        'CREATE INDEX IF NOT EXISTS idx_sales_invoices_number ON sales_invoices(invoice_number)',
        'CREATE INDEX IF NOT EXISTS idx_sales_invoices_created ON sales_invoices(created_at)',
        'CREATE INDEX IF NOT EXISTS idx_sales_invoices_amount ON sales_invoices(total_amount)',
        'CREATE INDEX IF NOT EXISTS idx_sales_invoices_remaining ON sales_invoices(remaining_amount)',
        'CREATE INDEX IF NOT EXISTS idx_sales_invoices_composite ON sales_invoices(customer_id, status, invoice_date)'
      ]

      // فهارس فواتير المشتريات
      const purchaseIndexes = [
        'CREATE INDEX IF NOT EXISTS idx_purchase_invoices_date ON purchase_invoices(invoice_date)',
        'CREATE INDEX IF NOT EXISTS idx_purchase_invoices_supplier ON purchase_invoices(supplier_id)',
        'CREATE INDEX IF NOT EXISTS idx_purchase_invoices_status ON purchase_invoices(status)',
        'CREATE INDEX IF NOT EXISTS idx_purchase_invoices_number ON purchase_invoices(invoice_number)',
        'CREATE INDEX IF NOT EXISTS idx_purchase_invoices_created ON purchase_invoices(created_at)',
        'CREATE INDEX IF NOT EXISTS idx_purchase_invoices_amount ON purchase_invoices(total_amount)',
        'CREATE INDEX IF NOT EXISTS idx_purchase_invoices_composite ON purchase_invoices(supplier_id, status, invoice_date)'
      ]

      // فهارس فواتير الدهان
      const paintIndexes = [
        'CREATE INDEX IF NOT EXISTS idx_paint_invoices_date ON paint_invoices(invoice_date)',
        'CREATE INDEX IF NOT EXISTS idx_paint_invoices_customer ON paint_invoices(customer_id)',
        'CREATE INDEX IF NOT EXISTS idx_paint_invoices_status ON paint_invoices(status)',
        'CREATE INDEX IF NOT EXISTS idx_paint_invoices_number ON paint_invoices(invoice_number)',
        'CREATE INDEX IF NOT EXISTS idx_paint_invoices_area ON paint_invoices(total_area)',
        'CREATE INDEX IF NOT EXISTS idx_paint_invoices_composite ON paint_invoices(customer_id, status, invoice_date)'
      ]

      // فهارس أصناف الفواتير
      const itemIndexes = [
        'CREATE INDEX IF NOT EXISTS idx_sales_invoice_items_invoice ON sales_invoice_items(sales_invoice_id)',
        'CREATE INDEX IF NOT EXISTS idx_sales_invoice_items_item ON sales_invoice_items(item_id)',
        'CREATE INDEX IF NOT EXISTS idx_sales_invoice_items_warehouse ON sales_invoice_items(warehouse_id)',
        'CREATE INDEX IF NOT EXISTS idx_purchase_invoice_items_invoice ON purchase_invoice_items(purchase_invoice_id)',
        'CREATE INDEX IF NOT EXISTS idx_purchase_invoice_items_item ON purchase_invoice_items(item_id)',
        'CREATE INDEX IF NOT EXISTS idx_paint_invoice_items_invoice ON paint_invoice_items(paint_invoice_id)',
        'CREATE INDEX IF NOT EXISTS idx_paint_invoice_items_paint_type ON paint_invoice_items(paint_type_id)'
      ]

      // فهارس المدفوعات
      const paymentIndexes = [
        'CREATE INDEX IF NOT EXISTS idx_invoice_payments_invoice ON invoice_payments(invoice_id)',
        'CREATE INDEX IF NOT EXISTS idx_invoice_payments_date ON invoice_payments(payment_date)',
        'CREATE INDEX IF NOT EXISTS idx_invoice_payments_method ON invoice_payments(payment_method)',
        'CREATE INDEX IF NOT EXISTS idx_invoice_payments_amount ON invoice_payments(amount)'
      ]

      // تنفيذ جميع الفهارس
      const allIndexes = [
        ...salesIndexes,
        ...purchaseIndexes,
        ...paintIndexes,
        ...itemIndexes,
        ...paymentIndexes
      ]

      allIndexes.forEach(indexQuery => {
        try {
          this.db.exec(indexQuery)
        } catch (error) {
          Logger.warn('InvoicePerformanceService', `تحذير في إنشاء الفهرس: ${error}`)
        }
      })

      Logger.info('InvoicePerformanceService', '✅ تم إنشاء جميع الفهارس بنجاح')
    } catch (error) {
      Logger.error('InvoicePerformanceService', 'خطأ في إنشاء الفهارس:', error)
    }
  }

  // تحليل أداء الاستعلامات
  public async analyzeQueryPerformance(): Promise<ApiResponse> {
    try {
      Logger.info('InvoicePerformanceService', 'تحليل أداء الاستعلامات')

      const queries = [
        {
          name: 'جلب فواتير المبيعات',
          query: 'SELECT COUNT(*) FROM sales_invoices WHERE invoice_date >= date("now", "-30 days")'
        },
        {
          name: 'جلب فواتير المشتريات',
          query: 'SELECT COUNT(*) FROM purchase_invoices WHERE invoice_date >= date("now", "-30 days")'
        },
        {
          name: 'البحث في الفواتير',
          query: 'SELECT COUNT(*) FROM sales_invoices WHERE customer_name LIKE "%test%"'
        },
        {
          name: 'إحصائيات العملاء',
          query: 'SELECT customer_id, COUNT(*), SUM(total_amount) FROM sales_invoices GROUP BY customer_id'
        },
        {
          name: 'الفواتير المتأخرة',
          query: 'SELECT COUNT(*) FROM sales_invoices WHERE due_date < date("now") AND status != "paid"'
        }
      ]

      const results = []

      for (const queryInfo of queries) {
        const startTime = Date.now()
        
        try {
          const stmt = this.db.prepare(`EXPLAIN QUERY PLAN ${queryInfo.query}`)
          const plan = stmt.all()
          
          // تنفيذ الاستعلام الفعلي لقياس الوقت
          const actualStmt = this.db.prepare(queryInfo.query)
          actualStmt.all()
          
          const endTime = Date.now()
          const executionTime = endTime - startTime

          results.push({
            name: queryInfo.name,
            execution_time_ms: executionTime,
            query_plan: plan,
            performance_rating: this.getPerformanceRating(executionTime)
          })
        } catch (error) {
          results.push({
            name: queryInfo.name,
            execution_time_ms: -1,
            error: error instanceof Error ? error.message : String(error),
            performance_rating: 'error'
          })
        }
      }

      return {
        success: true,
        data: {
          analysis_date: new Date().toISOString(),
          queries: results,
          recommendations: this.generatePerformanceRecommendations(results)
        }
      }
    } catch (error) {
      Logger.error('InvoicePerformanceService', 'خطأ في تحليل الأداء:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحليل أداء الاستعلامات'
      }
    }
  }

  // تحسين قاعدة البيانات
  public async optimizeDatabase(): Promise<ApiResponse> {
    try {
      Logger.info('InvoicePerformanceService', 'بدء تحسين قاعدة البيانات')

      const optimizations = []

      // تحليل الجداول
      const tableStats = await this.analyzeTableStats()
      optimizations.push({
        type: 'table_analysis',
        result: tableStats
      })

      // إعادة بناء الفهارس
      try {
        this.db.exec('REINDEX')
        optimizations.push({
          type: 'reindex',
          result: 'تم إعادة بناء جميع الفهارس بنجاح'
        })
      } catch (error) {
        optimizations.push({
          type: 'reindex',
          result: `خطأ في إعادة بناء الفهارس: ${error}`
        })
      }

      // تحليل وتحسين الجداول
      try {
        this.db.exec('ANALYZE')
        optimizations.push({
          type: 'analyze',
          result: 'تم تحليل إحصائيات الجداول بنجاح'
        })
      } catch (error) {
        optimizations.push({
          type: 'analyze',
          result: `خطأ في تحليل الجداول: ${error}`
        })
      }

      // تنظيف قاعدة البيانات
      try {
        this.db.exec('VACUUM')
        optimizations.push({
          type: 'vacuum',
          result: 'تم تنظيف قاعدة البيانات وضغطها بنجاح'
        })
      } catch (error) {
        optimizations.push({
          type: 'vacuum',
          result: `خطأ في تنظيف قاعدة البيانات: ${error}`
        })
      }

      // تحديث إحصائيات الفهارس
      try {
        this.db.exec('PRAGMA optimize')
        optimizations.push({
          type: 'optimize',
          result: 'تم تحديث إحصائيات الفهارس بنجاح'
        })
      } catch (error) {
        optimizations.push({
          type: 'optimize',
          result: `خطأ في تحديث الإحصائيات: ${error}`
        })
      }

      Logger.info('InvoicePerformanceService', '✅ تم تحسين قاعدة البيانات')

      return {
        success: true,
        data: {
          optimization_date: new Date().toISOString(),
          optimizations,
          message: 'تم تحسين قاعدة البيانات بنجاح'
        }
      }
    } catch (error) {
      Logger.error('InvoicePerformanceService', 'خطأ في تحسين قاعدة البيانات:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحسين قاعدة البيانات'
      }
    }
  }

  // تحليل إحصائيات الجداول
  private async analyzeTableStats(): Promise<any> {
    try {
      const tables = [
        'sales_invoices',
        'purchase_invoices', 
        'paint_invoices',
        'sales_invoice_items',
        'purchase_invoice_items',
        'paint_invoice_items',
        'invoice_payments'
      ]

      const stats = []

      for (const table of tables) {
        try {
          // عدد الصفوف
          const countStmt = this.db.prepare(`SELECT COUNT(*) as count FROM ${table}`)
          const countResult = countStmt.get()

          // حجم الجدول (تقريبي)
          const sizeStmt = this.db.prepare(`
            SELECT 
              name,
              (SELECT COUNT(*) FROM ${table}) as row_count,
              (SELECT COUNT(*) FROM pragma_table_info('${table}')) as column_count
            FROM sqlite_master 
            WHERE type='table' AND name='${table}'
          `)
          const sizeResult = sizeStmt.get()

          stats.push({
            table_name: table,
            row_count: countResult.count,
            column_count: sizeResult?.column_count || 0,
            estimated_size_kb: Math.round((countResult.count * (sizeResult?.column_count || 1) * 50) / 1024) // تقدير تقريبي
          })
        } catch (error) {
          stats.push({
            table_name: table,
            error: error instanceof Error ? error.message : String(error)
          })
        }
      }

      return stats
    } catch (error) {
      Logger.error('InvoicePerformanceService', 'خطأ في تحليل إحصائيات الجداول:', error)
      return { error: 'فشل في تحليل إحصائيات الجداول' }
    }
  }

  // تقييم أداء الاستعلام
  private getPerformanceRating(executionTimeMs: number): string {
    if (executionTimeMs < 10) return 'ممتاز'
    if (executionTimeMs < 50) return 'جيد'
    if (executionTimeMs < 200) return 'متوسط'
    if (executionTimeMs < 1000) return 'بطيء'
    return 'بطيء جداً'
  }

  // توليد توصيات الأداء
  private generatePerformanceRecommendations(queryResults: any[]): string[] {
    const recommendations = []

    // تحليل النتائج وتوليد التوصيات
    const slowQueries = queryResults.filter(q => q.execution_time_ms > 100)
    
    if (slowQueries.length > 0) {
      recommendations.push('يوجد استعلامات بطيئة تحتاج لتحسين')
      recommendations.push('فكر في إضافة فهارس جديدة للأعمدة المستخدمة في WHERE و JOIN')
    }

    const errorQueries = queryResults.filter(q => q.performance_rating === 'error')
    if (errorQueries.length > 0) {
      recommendations.push('يوجد استعلامات تحتوي على أخطاء تحتاج لمراجعة')
    }

    if (queryResults.every(q => q.execution_time_ms < 50)) {
      recommendations.push('أداء الاستعلامات ممتاز!')
    }

    // توصيات عامة
    recommendations.push('قم بتشغيل VACUUM بشكل دوري لتحسين الأداء')
    recommendations.push('استخدم LIMIT في الاستعلامات التي ترجع بيانات كثيرة')
    recommendations.push('فكر في أرشفة البيانات القديمة لتحسين الأداء')

    return recommendations
  }

  // مراقبة استخدام الذاكرة
  public async monitorMemoryUsage(): Promise<ApiResponse> {
    try {
      Logger.info('InvoicePerformanceService', 'مراقبة استخدام الذاكرة')

      // معلومات الذاكرة من SQLite
      const memoryInfo = {
        cache_size: this.db.pragma('cache_size'),
        page_size: this.db.pragma('page_size'),
        page_count: this.db.pragma('page_count'),
        freelist_count: this.db.pragma('freelist_count'),
        memory_used: this.db.pragma('cache_size') * this.db.pragma('page_size')
      }

      // حساب حجم قاعدة البيانات
      const dbSize = memoryInfo.page_count * memoryInfo.page_size
      const freeSpace = memoryInfo.freelist_count * memoryInfo.page_size

      return {
        success: true,
        data: {
          memory_info: memoryInfo,
          database_size_bytes: dbSize,
          database_size_mb: Math.round(dbSize / (1024 * 1024) * 100) / 100,
          free_space_bytes: freeSpace,
          free_space_mb: Math.round(freeSpace / (1024 * 1024) * 100) / 100,
          fragmentation_percentage: Math.round((freeSpace / dbSize) * 100 * 100) / 100,
          recommendations: this.generateMemoryRecommendations(memoryInfo, dbSize, freeSpace)
        }
      }
    } catch (error) {
      Logger.error('InvoicePerformanceService', 'خطأ في مراقبة الذاكرة:', error)
      return {
        success: false,
        message: 'حدث خطأ في مراقبة استخدام الذاكرة'
      }
    }
  }

  // توليد توصيات الذاكرة
  private generateMemoryRecommendations(memoryInfo: any, dbSize: number, freeSpace: number): string[] {
    const recommendations = []
    const fragmentationPercentage = (freeSpace / dbSize) * 100

    if (fragmentationPercentage > 10) {
      recommendations.push('نسبة التجزئة عالية، يُنصح بتشغيل VACUUM')
    }

    if (dbSize > 100 * 1024 * 1024) { // أكبر من 100 ميجا
      recommendations.push('حجم قاعدة البيانات كبير، فكر في أرشفة البيانات القديمة')
    }

    if (memoryInfo.cache_size < 2000) {
      recommendations.push('حجم الذاكرة المؤقتة صغير، فكر في زيادته لتحسين الأداء')
    }

    if (recommendations.length === 0) {
      recommendations.push('استخدام الذاكرة مُحسَّن بشكل جيد')
    }

    return recommendations
  }

  // تنظيف البيانات القديمة
  public async cleanupOldData(daysToKeep: number = 365): Promise<ApiResponse> {
    try {
      Logger.info('InvoicePerformanceService', `تنظيف البيانات الأقدم من ${daysToKeep} يوم`)

      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)
      const cutoffDateStr = cutoffDate.toISOString().split('T')[0]

      const cleanupResults = []

      // تنظيف سجلات الفواتير المتكررة القديمة
      try {
        const stmt = this.db.prepare(`
          DELETE FROM recurring_invoice_logs 
          WHERE created_at < ? AND status = 'success'
        `)
        const result = stmt.run(cutoffDateStr)
        cleanupResults.push({
          table: 'recurring_invoice_logs',
          deleted_rows: result.changes,
          status: 'success'
        })
      } catch (error) {
        cleanupResults.push({
          table: 'recurring_invoice_logs',
          error: error instanceof Error ? error.message : String(error),
          status: 'error'
        })
      }

      // يمكن إضافة المزيد من عمليات التنظيف هنا
      // مثل تنظيف الفواتير الملغاة القديمة، السجلات المؤقتة، إلخ

      return {
        success: true,
        data: {
          cleanup_date: new Date().toISOString(),
          cutoff_date: cutoffDateStr,
          results: cleanupResults,
          total_deleted: cleanupResults.reduce((sum, r) => sum + (r.deleted_rows || 0), 0)
        }
      }
    } catch (error) {
      Logger.error('InvoicePerformanceService', 'خطأ في تنظيف البيانات:', error)
      return {
        success: false,
        message: 'حدث خطأ في تنظيف البيانات القديمة'
      }
    }
  }

  // إعداد مراقبة الأداء التلقائية
  public setupPerformanceMonitoring(): void {
    try {
      Logger.info('InvoicePerformanceService', 'إعداد مراقبة الأداء التلقائية')

      // مراقبة يومية للأداء
      setInterval(async () => {
        try {
          const analysis = await this.analyzeQueryPerformance()
          if (analysis.success) {
            Logger.info('InvoicePerformanceService', 'تم تحليل الأداء التلقائي')
          }
        } catch (error) {
          Logger.error('InvoicePerformanceService', 'خطأ في المراقبة التلقائية:', error)
        }
      }, 24 * 60 * 60 * 1000) // كل 24 ساعة

      // تحسين أسبوعي لقاعدة البيانات
      setInterval(async () => {
        try {
          const optimization = await this.optimizeDatabase()
          if (optimization.success) {
            Logger.info('InvoicePerformanceService', 'تم التحسين التلقائي لقاعدة البيانات')
          }
        } catch (error) {
          Logger.error('InvoicePerformanceService', 'خطأ في التحسين التلقائي:', error)
        }
      }, 7 * 24 * 60 * 60 * 1000) // كل أسبوع

      Logger.info('InvoicePerformanceService', '✅ تم إعداد مراقبة الأداء التلقائية')
    } catch (error) {
      Logger.error('InvoicePerformanceService', 'خطأ في إعداد المراقبة:', error)
    }
  }
}
