/* ===================================
   أنماط نافذة خيارات الطباعة
   Print Modal Styles
   =================================== */

.print-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    direction: rtl;
}

.print-modal.show {
    opacity: 1;
    visibility: visible;
}

.print-modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.7);
    transition: transform 0.3s ease;
    direction: rtl;
}

.print-modal.show .print-modal-content {
    transform: scale(1);
}

.print-modal-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.print-modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.print-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.print-modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.print-modal-body {
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.print-options {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.option-group {
    margin-bottom: 15px;
}

.option-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.option-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    direction: rtl;
}

.option-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.option-group input[type="checkbox"] {
    margin-left: 8px;
    transform: scale(1.1);
}

.option-group label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 10px;
}

.print-preview {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.print-preview h4 {
    background: #f8f9fa;
    margin: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.preview-container {
    height: 400px;
    position: relative;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

#print-preview-frame {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
    border-radius: 4px;
    margin: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.print-modal-footer {
    background: #f8f9fa;
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    gap: 10px;
    justify-content: flex-start;
}

.print-modal-footer .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.print-modal-footer .btn-primary {
    background: #007bff;
    color: white;
}

.print-modal-footer .btn-primary:hover {
    background: #0056b3;
}

.print-modal-footer .btn-secondary {
    background: #6c757d;
    color: white;
}

.print-modal-footer .btn-secondary:hover {
    background: #545b62;
}

.print-modal-footer .btn-default {
    background: #e9ecef;
    color: #333;
    border: 1px solid #ced4da;
}

.print-modal-footer .btn-default:hover {
    background: #dae0e5;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .print-modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .print-modal-body {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .preview-container {
        height: 250px;
    }
    
    .print-modal-footer {
        flex-direction: column;
    }
    
    .print-modal-footer .btn {
        width: 100%;
        justify-content: center;
    }
}

/* أنماط أزرار الطباعة في الصفحات */
.print-btn,
.btn-print {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.print-btn:hover,
.btn-print:hover {
    background: #218838;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.print-btn i,
.btn-print i {
    font-size: 16px;
}

/* أنماط خاصة للطباعة */
@media print {
    .print-modal,
    .print-btn,
    .btn-print {
        display: none !important;
    }
}

/* تحسينات إضافية */
.print-options .option-group:last-child {
    margin-bottom: 0;
}

.print-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 49%, rgba(0,0,0,0.02) 50%, transparent 51%);
    pointer-events: none;
    z-index: 1;
}

/* تأثيرات التحميل */
.preview-container.loading::after {
    content: 'جاري التحميل...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
    z-index: 2;
}

/* تحسينات الوصولية */
.print-modal-content:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.option-group select:focus,
.option-group input:focus {
    outline: 2px solid #007bff;
    outline-offset: 1px;
}

/* أنماط الأخطاء */
.option-group.error select {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

.option-group .error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.option-group.error .error-message {
    display: block;
}
