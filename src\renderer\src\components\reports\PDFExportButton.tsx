import React, { useState } from 'react'
import { Button, message } from 'antd'
import { FilePdfOutlined } from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'

interface PDFExportButtonProps {
  // بيانات التقرير
  reportData: {
    title: string
    subtitle?: string
    data: any[]
    summary?: any
    columns?: Array<{
      key: string
      title: string
      align?: 'left' | 'center' | 'right'
      format?: 'currency' | 'number' | 'date' | 'text'
    }>
  }
  
  // إعدادات المظهر
  filename?: string
  title?: string
  size?: 'small' | 'middle' | 'large'
  type?: 'default' | 'primary' | 'ghost' | 'dashed' | 'link' | 'text'
  disabled?: boolean
  loading?: boolean
  
  // callbacks
  onExportSuccess?: (filePath: string) => void
  onExportError?: (error: string) => void
}

const PDFExportButton: React.FC<PDFExportButtonProps> = ({
  reportData,
  filename,
  title = 'تصدير PDF',
  size = 'middle',
  type = 'default',
  disabled = false,
  loading = false,
  onExportSuccess,
  onExportError
}) => {
  const [exporting, setExporting] = useState(false)

  const handleExport = async () => {
    try {
      setExporting(true)

      // التحقق من وجود البيانات
      if (!reportData.data || reportData.data.length === 0) {
        message.warning('لا توجد بيانات للتصدير')
        return
      }

      // التحقق من وجود electronAPI
      if (!window.electronAPI) {
        message.error('وظيفة التصدير غير متوفرة في وضع المتصفح')
        return
      }

      // إنشاء محتوى HTML للتقرير
      const htmlContent = generateReportHTML()
      
      // تحديد اسم الملف
      const defaultFilename = filename || `${reportData.title}_${new Date().toISOString().split('T')[0]}.pdf`
      
      // طلب حفظ التقرير كـ PDF
      const result = await window.electronAPI.saveReportAsPDF(
        reportData.title,
        defaultFilename,
        {
          content: htmlContent,
          format: 'A4',
          orientation: 'portrait',
          includeHeader: true,
          includeFooter: true,
          includeLogo: true
        }
      )

      if (result.success) {
        message.success('تم تصدير التقرير بصيغة PDF بنجاح')
        onExportSuccess?.(result.data?.filePath || defaultFilename)
      } else {
        message.error(result.message || 'فشل في تصدير التقرير')
        onExportError?.(result.message || 'فشل في تصدير التقرير')
      }

    } catch (error) {
      Logger.error('PDFExportButton', 'خطأ في تصدير PDF:', error)
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
      message.error(`حدث خطأ أثناء تصدير التقرير: ${errorMessage}`)
      onExportError?.(errorMessage)
    } finally {
      setExporting(false)
    }
  }

  // إنشاء محتوى HTML للتقرير
  const generateReportHTML = (): string => {
    const { title: reportTitle, subtitle, data, summary, columns } = reportData
    
    // إنشاء رؤوس الجدول
    const tableHeaders = columns?.map(col => 
      `<th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; text-align: ${col.align || 'center'}">${col.title}</th>`
    ).join('') || ''

    // إنشاء صفوف البيانات
    const tableRows = data.map(row => {
      const cells = columns?.map(col => {
        let value = row[col.key]
        
        // تنسيق القيم حسب النوع
        if (col.format === 'currency' && typeof value === 'number') {
          value = `₪${value.toLocaleString()}`
        } else if (col.format === 'number' && typeof value === 'number') {
          value = value.toLocaleString()
        } else if (col.format === 'date' && value) {
          value = new Date(value).toLocaleDateString('ar-EG')
        }
        
        return `<td style="border: 1px solid #ddd; padding: 8px; text-align: ${col.align || 'center'}">${value || ''}</td>`
      }).join('') || ''
      
      return `<tr>${cells}</tr>`
    }).join('')

    // إنشاء ملخص التقرير
    const summarySection = summary ? `
      <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
        <h3 style="margin: 0 0 10px 0; color: #333;">ملخص التقرير</h3>
        ${Object.entries(summary).map(([key, value]) => 
          `<p style="margin: 5px 0;"><strong>${key}:</strong> ${value}</p>`
        ).join('')}
      </div>
    ` : ''

    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${reportTitle}</title>
        <style>
          body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 15px;
          }
          .header h1 {
            color: #1890ff;
            margin: 0;
            font-size: 24px;
          }
          .header h2 {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 16px;
            font-weight: normal;
          }
          .table-container {
            margin: 20px 0;
            overflow-x: auto;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 12px;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
          }
          th {
            background-color: #f5f5f5;
            font-weight: bold;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${reportTitle}</h1>
          ${subtitle ? `<h2>${subtitle}</h2>` : ''}
          <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
        </div>
        
        <div class="table-container">
          <table>
            <thead>
              <tr>${tableHeaders}</tr>
            </thead>
            <tbody>
              ${tableRows}
            </tbody>
          </table>
        </div>
        
        ${summarySection}
        
        <div class="footer">
          <p>تم إنشاء هذا التقرير بواسطة نظام المحاسبة والإنتاج</p>
          <p>تاريخ الإنشاء: ${new Date().toLocaleString('ar-EG')}</p>
        </div>
      </body>
      </html>
    `
  }

  return (
    <Button
      type={type as any}
      size={size}
      icon={<FilePdfOutlined />}
      onClick={handleExport}
      disabled={disabled || !reportData.data || reportData.data.length === 0}
      loading={loading || exporting}
    >
      {title}
    </Button>
  )
}

export default PDFExportButton
