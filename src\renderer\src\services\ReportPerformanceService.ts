/**
 * خدمة تحسين أداء التقارير
 * تحسين سرعة تحميل وعرض التقارير مع تقنيات التخزين المؤقت والتحميل التدريجي
 */

import { ReportData, ReportFilters, ReportType } from '../types/reports'
import { SafeLogger as Logger } from '../utils/logger'

interface CacheEntry {
  data: ReportData
  timestamp: number
  accessCount: number
  lastAccessed: number
}

interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  dataSize: number
  cacheHitRate: number
  memoryUsage: number
}

export class ReportPerformanceService {
  private static instance: ReportPerformanceService
  private cache: Map<string, CacheEntry> = new Map()
  private metrics: Map<string, PerformanceMetrics> = new Map()
  private maxCacheSize = 50 // حد أقصى 50 تقرير في الكاش
  private cacheTimeout = 10 * 60 * 1000 // 10 دقائق
  private compressionEnabled = true
  private lazyLoadingEnabled = true

  private constructor() {
    this.startCacheCleanup()
  }

  public static getInstance(): ReportPerformanceService {
    if (!ReportPerformanceService.instance) {
      ReportPerformanceService.instance = new ReportPerformanceService()
    }
    return ReportPerformanceService.instance
  }

  /**
   * تحسين بيانات التقرير للعرض السريع
   */
  public optimizeReportData(data: ReportData): ReportData {
    const startTime = performance.now()
    
    try {
      Logger.info('ReportPerformanceService', 'تحسين بيانات التقرير...')
      
      // تحسين البيانات
      const optimizedData = {
        ...data,
        data: this.optimizeDataArray(data.data),
        columns: this.optimizeColumns(data.columns)
      }

      // ضغط البيانات إذا كانت كبيرة
      if (this.compressionEnabled && data.data.length > 1000) {
        optimizedData.data = this.compressLargeDataset(optimizedData.data)
      }

      const processingTime = performance.now() - startTime
      Logger.info('ReportPerformanceService', `تم تحسين البيانات في ${processingTime.toFixed(2)}ms`)
      
      return optimizedData
    } catch (error) {
      Logger.error('ReportPerformanceService', 'خطأ في تحسين البيانات:', error)
      return data
    }
  }

  /**
   * تحسين مصفوفة البيانات
   */
  private optimizeDataArray(data: any[]): any[] {
    if (!Array.isArray(data) || data.length === 0) return data

    return data.map((item, index) => ({
      ...item,
      _index: index, // إضافة فهرس للتحسين
      _optimized: true
    }))
  }

  /**
   * تحسين أعمدة الجدول
   */
  private optimizeColumns(columns: any[]): any[] {
    return columns.map(col => ({
      ...col,
      // تحسين عرض الأعمدة
      ellipsis: col.width && col.width < 150,
      // تفعيل الفرز للأعمدة المهمة
      sorter: col.sortable !== false,
      // تحسين العرض للأعمدة الطويلة
      render: col.render || (col.format ? this.createOptimizedRenderer(col.format) : undefined)
    }))
  }

  /**
   * إنشاء معرض محسن للقيم
   */
  private createOptimizedRenderer(format: string) {
    return (value: any) => {
      if (value === null || value === undefined) return ''
      
      switch (format) {
        case 'currency':
          return typeof value === 'number' ? `₪${value.toLocaleString()}` : value
        case 'number':
          return typeof value === 'number' ? value.toLocaleString() : value
        case 'percentage':
          return typeof value === 'number' ? `${value.toFixed(1)}%` : value
        case 'date':
          return new Date(value).toLocaleDateString('ar-EG')
        default:
          return value.toString()
      }
    }
  }

  /**
   * ضغط مجموعة البيانات الكبيرة
   */
  private compressLargeDataset(data: any[]): any[] {
    Logger.info('ReportPerformanceService', `ضغط مجموعة بيانات كبيرة: ${data.length} سجل`)
    
    // تطبيق تقنيات الضغط
    return data.map(item => {
      const compressed: any = {}
      
      // ضغط القيم المكررة
      Object.keys(item).forEach(key => {
        const value = item[key]
        
        // تحويل القيم الطويلة إلى مراجع
        if (typeof value === 'string' && value.length > 100) {
          compressed[key] = this.compressString(value)
        } else {
          compressed[key] = value
        }
      })
      
      return compressed
    })
  }

  /**
   * ضغط النصوص الطويلة
   */
  private compressString(str: string): string {
    // تطبيق ضغط بسيط للنصوص
    return str.length > 100 ? str.substring(0, 97) + '...' : str
  }

  /**
   * تحميل تدريجي للبيانات
   */
  public async loadDataProgressively(
    reportType: ReportType,
    filters: ReportFilters,
    pageSize: number = 50
  ): Promise<{ data: any[], hasMore: boolean, nextPage: number }> {
    
    const startTime = performance.now()
    
    try {
      const page = filters.page || 1
      const offset = (page - 1) * pageSize
      
      Logger.info('ReportPerformanceService', `تحميل تدريجي: صفحة ${page}, حجم ${pageSize}`)
      
      // محاكاة تحميل البيانات
      const allData = await this.fetchReportData(reportType, filters)
      const paginatedData = allData.slice(offset, offset + pageSize)
      const hasMore = offset + pageSize < allData.length
      
      const loadTime = performance.now() - startTime
      this.recordMetrics(reportType, {
        loadTime,
        renderTime: 0,
        dataSize: paginatedData.length,
        cacheHitRate: 0,
        memoryUsage: this.estimateMemoryUsage(paginatedData)
      })
      
      return {
        data: paginatedData,
        hasMore,
        nextPage: hasMore ? page + 1 : page
      }
    } catch (error) {
      Logger.error('ReportPerformanceService', 'خطأ في التحميل التدريجي:', error)
      throw error
    }
  }

  /**
   * جلب بيانات التقرير مع التخزين المؤقت
   */
  private async fetchReportData(reportType: ReportType, filters: ReportFilters): Promise<any[]> {
    const cacheKey = this.generateCacheKey(reportType, filters)
    
    // فحص الكاش أولاً
    const cached = this.getFromCache(cacheKey)
    if (cached) {
      Logger.info('ReportPerformanceService', 'استخدام البيانات المحفوظة')
      return cached.data.data
    }
    
    // تحميل البيانات من المصدر
    const data = await this.loadFromSource(reportType, filters)
    
    // حفظ في الكاش
    this.saveToCache(cacheKey, { 
      title: `تقرير ${reportType}`,
      data,
      columns: [],
      metadata: {
        generatedAt: new Date().toISOString(),
        reportType,
        filters,
        totalRecords: data.length
      }
    })
    
    return data
  }

  /**
   * تحميل البيانات من المصدر
   */
  private async loadFromSource(reportType: ReportType, filters: ReportFilters): Promise<any[]> {
    try {
      // استدعاء البيانات الحقيقية من النظام حسب نوع التقرير
      switch (reportType) {
        case 'inventory_detailed': {
          const inventoryResponse = await window.electronAPI?.getInventoryReport?.(filters)
          return inventoryResponse?.success ? inventoryResponse.data : []
        }

        case 'sales_by_customer': {
          const salesResponse = await window.electronAPI?.getSalesByCustomerReport?.(filters)
          return salesResponse?.success ? salesResponse.data : []
        }

        case 'purchases_by_supplier': {
          const purchasesResponse = await window.electronAPI?.getPurchasesBySupplierReport?.(filters)
          return purchasesResponse?.success ? purchasesResponse.data : []
        }

        case 'employee_attendance': {
          const attendanceResponse = await window.electronAPI?.getEmployeeAttendanceReport?.(filters)
          return attendanceResponse?.success ? attendanceResponse.data : []
        }

        default: {
          // للتقارير الأخرى، استدعاء API عام
          const genericResponse = await (window.electronAPI as any)?.generateReport?.(reportType, filters)
          return genericResponse?.success ? genericResponse.data : []
        }
      }
    } catch (error) {
      Logger.error('ReportPerformanceService', 'خطأ في تحميل البيانات:', error)
      return []
    }
  }

  /**
   * إدارة الكاش
   */
  private generateCacheKey(reportType: ReportType, filters: ReportFilters): string {
    return `${reportType}_${JSON.stringify(filters)}`
  }

  private getFromCache(key: string): CacheEntry | null {
    const entry = this.cache.get(key)
    
    if (!entry) return null
    
    // فحص انتهاء الصلاحية
    if (Date.now() - entry.timestamp > this.cacheTimeout) {
      this.cache.delete(key)
      return null
    }
    
    // تحديث إحصائيات الوصول
    entry.accessCount++
    entry.lastAccessed = Date.now()
    
    return entry
  }

  private saveToCache(key: string, data: ReportData): void {
    // تنظيف الكاش إذا امتلأ
    if (this.cache.size >= this.maxCacheSize) {
      this.cleanupCache()
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now()
    })
  }

  /**
   * تنظيف الكاش
   */
  private cleanupCache(): void {
    const entries = Array.from(this.cache.entries())
    
    // ترتيب حسب آخر وصول والتكرار
    entries.sort((a, b) => {
      const scoreA = a[1].accessCount * (Date.now() - a[1].lastAccessed)
      const scoreB = b[1].accessCount * (Date.now() - b[1].lastAccessed)
      return scoreA - scoreB
    })
    
    // حذف النصف الأقل استخداماً
    const toDelete = entries.slice(0, Math.floor(entries.length / 2))
    toDelete.forEach(([key]) => this.cache.delete(key))
    
    Logger.info('ReportPerformanceService', `تم تنظيف الكاش: حذف ${toDelete.length} عنصر`)
  }

  /**
   * تنظيف دوري للكاش
   */
  private startCacheCleanup(): void {
    setInterval(() => {
      const now = Date.now()
      let deletedCount = 0
      
      for (const [key, entry] of this.cache.entries()) {
        if (now - entry.timestamp > this.cacheTimeout) {
          this.cache.delete(key)
          deletedCount++
        }
      }
      
      if (deletedCount > 0) {
        Logger.info('ReportPerformanceService', `تنظيف دوري: حذف ${deletedCount} عنصر منتهي الصلاحية`)
      }
    }, 5 * 60 * 1000) // كل 5 دقائق
  }

  /**
   * تسجيل مقاييس الأداء
   */
  private recordMetrics(reportType: ReportType, metrics: PerformanceMetrics): void {
    this.metrics.set(reportType, metrics)
    
    Logger.info('ReportPerformanceService', `مقاييس الأداء لـ ${reportType}:`, {
      loadTime: `${metrics.loadTime.toFixed(2)}ms`,
      dataSize: metrics.dataSize,
      memoryUsage: `${(metrics.memoryUsage / 1024).toFixed(2)}KB`
    })
  }

  /**
   * تقدير استخدام الذاكرة
   */
  private estimateMemoryUsage(data: any[]): number {
    return JSON.stringify(data).length * 2 // تقدير تقريبي
  }

  /**
   * الحصول على مقاييس الأداء
   */
  public getPerformanceMetrics(reportType?: ReportType): Map<string, PerformanceMetrics> | PerformanceMetrics | null {
    if (reportType) {
      return this.metrics.get(reportType) || null
    }
    return this.metrics
  }

  /**
   * تنظيف الذاكرة
   */
  public clearCache(): void {
    this.cache.clear()
    this.metrics.clear()
    Logger.info('ReportPerformanceService', 'تم تنظيف الكاش والمقاييس')
  }

  /**
   * تحديث إعدادات الأداء
   */
  public updateSettings(settings: {
    maxCacheSize?: number
    cacheTimeout?: number
    compressionEnabled?: boolean
    lazyLoadingEnabled?: boolean
  }): void {
    if (settings.maxCacheSize) this.maxCacheSize = settings.maxCacheSize
    if (settings.cacheTimeout) this.cacheTimeout = settings.cacheTimeout
    if (settings.compressionEnabled !== undefined) this.compressionEnabled = settings.compressionEnabled
    if (settings.lazyLoadingEnabled !== undefined) this.lazyLoadingEnabled = settings.lazyLoadingEnabled
    
    Logger.info('ReportPerformanceService', 'تم تحديث إعدادات الأداء')
  }
}
