"""
مسارات الطباعة المحسنة
Enhanced Print Routes
"""

from flask import Blueprint, render_template, request, jsonify, make_response, abort
from flask import current_app, session, redirect, url_for
from datetime import datetime
import json
import os

# إنشاء Blueprint للطباعة
print_bp = Blueprint('print', __name__, url_prefix='/print')

# استيراد النماذج (يجب تعديل هذا حسب بنية مشروعك)
try:
    from models import Invoice, Customer, Product, InvoiceItem
    from database import db
except ImportError:
    # في حالة عدم وجود النماذج، استخدم نماذج وهمية
    class Invoice:
        def __init__(self):
            self.id = 1
            self.date = datetime.now()
            self.due_date = datetime.now()
            self.status = 'pending'
            self.subtotal = 1000.0
            self.tax = 150.0
            self.discount = 50.0
            self.total = 1100.0
            self.notes = 'فاتورة تجريبية'
            self.customer = None
            self.items = []
    
    class Customer:
        def __init__(self):
            self.id = 1
            self.name = 'عميل تجريبي'
            self.phone = '0501234567'
            self.address = 'الرياض، المملكة العربية السعودية'


@print_bp.route('/invoice/<int:invoice_id>')
def print_invoice(invoice_id):
    """
    طباعة فاتورة محددة
    """
    try:
        # محاولة جلب الفاتورة من قاعدة البيانات
        try:
            invoice = Invoice.query.get_or_404(invoice_id)
        except:
            # في حالة عدم وجود قاعدة بيانات، استخدم بيانات تجريبية
            invoice = Invoice()
            invoice.id = invoice_id
            
            # إضافة عميل تجريبي
            customer = Customer()
            invoice.customer = customer
            
            # إضافة أصناف تجريبية
            class Item:
                def __init__(self, name, qty, price):
                    self.description = name
                    self.quantity = qty
                    self.unit_price = price
                    self.discount = 0
                    self.total = qty * price
                    self.product = None
            
            invoice.items = [
                Item('صنف تجريبي 1', 2, 100),
                Item('صنف تجريبي 2', 1, 200),
                Item('صنف تجريبي 3', 3, 150)
            ]
        
        # تحديد نوع الطباعة
        print_type = request.args.get('type', 'full')
        auto_print = request.args.get('auto_print', 'false').lower() == 'true'
        
        # إعداد البيانات للقالب
        template_data = {
            'invoice': invoice,
            'auto_print': auto_print,
            'print_date': datetime.now(),
            'company_info': get_company_info()
        }
        
        if print_type == 'simple':
            return render_template('print/invoice_simple.html', **template_data)
        else:
            return render_template('print/invoice_print.html', **template_data)
            
    except Exception as e:
        current_app.logger.error(f"خطأ في طباعة الفاتورة {invoice_id}: {str(e)}")
        abort(500)


@print_bp.route('/receipt/<int:receipt_id>')
def print_receipt(receipt_id):
    """
    طباعة سند قبض/صرف
    """
    try:
        # هنا يمكن إضافة منطق جلب السند
        receipt_data = {
            'id': receipt_id,
            'type': request.args.get('type', 'receipt'),
            'amount': 1000.0,
            'date': datetime.now(),
            'description': 'سند تجريبي'
        }
        
        return render_template('print/receipt_print.html', 
                             receipt=receipt_data,
                             auto_print=request.args.get('auto_print', 'false').lower() == 'true')
                             
    except Exception as e:
        current_app.logger.error(f"خطأ في طباعة السند {receipt_id}: {str(e)}")
        abort(500)


@print_bp.route('/report/<report_type>')
def print_report(report_type):
    """
    طباعة التقارير المختلفة
    """
    try:
        # تحديد نوع التقرير
        if report_type == 'sales':
            template = 'print/sales_report.html'
            data = generate_sales_report()
        elif report_type == 'customers':
            template = 'print/customers_report.html'
            data = generate_customers_report()
        elif report_type == 'financial':
            template = 'print/financial_report.html'
            data = generate_financial_report()
        else:
            abort(404)
        
        return render_template(template, 
                             report_data=data,
                             report_type=report_type,
                             auto_print=request.args.get('auto_print', 'false').lower() == 'true')
                             
    except Exception as e:
        current_app.logger.error(f"خطأ في طباعة التقرير {report_type}: {str(e)}")
        abort(500)


@print_bp.route('/preview', methods=['POST'])
def print_preview():
    """
    معاينة الطباعة قبل التنفيذ
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'لا توجد بيانات للمعاينة'}), 400
        
        document_type = data.get('type', 'invoice')
        document_id = data.get('id')
        options = data.get('options', {})
        
        # إنشاء HTML للمعاينة
        if document_type == 'invoice':
            html_content = generate_invoice_preview(document_id, options)
        elif document_type == 'receipt':
            html_content = generate_receipt_preview(document_id, options)
        elif document_type == 'report':
            html_content = generate_report_preview(document_id, options)
        else:
            return jsonify({'error': 'نوع مستند غير مدعوم'}), 400
        
        return jsonify({
            'success': True,
            'html': html_content,
            'options': options
        })
        
    except Exception as e:
        current_app.logger.error(f"خطأ في معاينة الطباعة: {str(e)}")
        return jsonify({'error': 'حدث خطأ في المعاينة'}), 500


@print_bp.route('/settings', methods=['GET', 'POST'])
def print_settings():
    """
    إعدادات الطباعة
    """
    if request.method == 'POST':
        try:
            settings = request.get_json()
            
            # حفظ الإعدادات
            save_print_settings(settings)
            
            return jsonify({
                'success': True,
                'message': 'تم حفظ إعدادات الطباعة بنجاح'
            })
            
        except Exception as e:
            current_app.logger.error(f"خطأ في حفظ إعدادات الطباعة: {str(e)}")
            return jsonify({'error': 'فشل في حفظ الإعدادات'}), 500
    
    else:
        # جلب الإعدادات الحالية
        settings = get_print_settings()
        return jsonify(settings)


@print_bp.route('/templates')
def print_templates():
    """
    قائمة قوالب الطباعة المتاحة
    """
    try:
        templates = {
            'invoices': [
                {'id': 'invoice_standard', 'name': 'فاتورة قياسية', 'description': 'قالب فاتورة أساسي'},
                {'id': 'invoice_detailed', 'name': 'فاتورة مفصلة', 'description': 'قالب فاتورة مع تفاصيل إضافية'},
                {'id': 'invoice_simple', 'name': 'فاتورة مبسطة', 'description': 'قالب فاتورة مبسط'}
            ],
            'receipts': [
                {'id': 'receipt_standard', 'name': 'سند قياسي', 'description': 'قالب سند أساسي'},
                {'id': 'receipt_detailed', 'name': 'سند مفصل', 'description': 'قالب سند مع تفاصيل إضافية'}
            ],
            'reports': [
                {'id': 'report_sales', 'name': 'تقرير مبيعات', 'description': 'قالب تقرير المبيعات'},
                {'id': 'report_financial', 'name': 'تقرير مالي', 'description': 'قالب التقرير المالي'}
            ]
        }
        
        return jsonify(templates)
        
    except Exception as e:
        current_app.logger.error(f"خطأ في جلب قوالب الطباعة: {str(e)}")
        return jsonify({'error': 'فشل في جلب القوالب'}), 500


# وظائف مساعدة
def get_company_info():
    """
    جلب معلومات الشركة
    """
    return {
        'name': 'شركة المحاسبة المتقدمة',
        'address': 'شارع الملك فهد، الرياض، المملكة العربية السعودية',
        'phone': '+966 11 123 4567',
        'email': '<EMAIL>',
        'tax_number': '123456789012345',
        'logo': '/static/images/logo.png'
    }


def generate_sales_report():
    """
    إنشاء تقرير المبيعات
    """
    return {
        'title': 'تقرير المبيعات الشهري',
        'period': 'يناير 2024',
        'total_sales': 50000.0,
        'total_invoices': 25,
        'top_customers': [
            {'name': 'عميل 1', 'amount': 10000},
            {'name': 'عميل 2', 'amount': 8000},
            {'name': 'عميل 3', 'amount': 6000}
        ]
    }


def generate_customers_report():
    """
    إنشاء تقرير العملاء
    """
    return {
        'title': 'تقرير العملاء',
        'total_customers': 100,
        'active_customers': 85,
        'new_customers': 15
    }


def generate_financial_report():
    """
    إنشاء التقرير المالي
    """
    return {
        'title': 'التقرير المالي',
        'revenue': 100000.0,
        'expenses': 30000.0,
        'profit': 70000.0
    }


def generate_invoice_preview(invoice_id, options):
    """
    إنشاء معاينة الفاتورة
    """
    try:
        # جلب بيانات الفاتورة من قاعدة البيانات
        # هذا مثال أساسي - يجب تطويره حسب هيكل قاعدة البيانات

        html = f"""
        <div class="invoice-preview" style="direction: rtl; font-family: Arial, sans-serif;">
            <div class="invoice-header" style="text-align: center; margin-bottom: 20px;">
                <h2>فاتورة رقم {invoice_id}</h2>
                <p>تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            </div>

            <div class="invoice-details" style="margin: 20px 0;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background-color: #f0f0f0;">
                            <th style="border: 1px solid #000; padding: 8px;">م</th>
                            <th style="border: 1px solid #000; padding: 8px;">الصنف</th>
                            <th style="border: 1px solid #000; padding: 8px;">الكمية</th>
                            <th style="border: 1px solid #000; padding: 8px;">السعر</th>
                            <th style="border: 1px solid #000; padding: 8px;">المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="border: 1px solid #000; padding: 8px; text-align: center;">1</td>
                            <td style="border: 1px solid #000; padding: 8px;">صنف تجريبي</td>
                            <td style="border: 1px solid #000; padding: 8px; text-align: center;">1</td>
                            <td style="border: 1px solid #000; padding: 8px; text-align: left;">100.00</td>
                            <td style="border: 1px solid #000; padding: 8px; text-align: left;">100.00</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="invoice-total" style="text-align: left; margin-top: 20px;">
                <strong>الإجمالي: 100.00</strong>
            </div>
        </div>
        """

        return html

    except Exception as e:
        return f"<div>خطأ في إنشاء معاينة الفاتورة: {str(e)}</div>"


def generate_receipt_preview(receipt_id, options):
    """
    إنشاء معاينة السند
    """
    try:
        html = f"""
        <div class="receipt-preview" style="direction: rtl; font-family: Arial, sans-serif;">
            <div class="receipt-header" style="text-align: center; margin-bottom: 20px;">
                <h2>سند قبض رقم {receipt_id}</h2>
                <p>تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            </div>

            <div class="receipt-details" style="margin: 20px 0;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="border: 1px solid #000; padding: 10px; font-weight: bold; background-color: #f0f0f0;">رقم السند:</td>
                        <td style="border: 1px solid #000; padding: 10px;">{receipt_id}</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000; padding: 10px; font-weight: bold; background-color: #f0f0f0;">التاريخ:</td>
                        <td style="border: 1px solid #000; padding: 10px;">{datetime.now().strftime('%Y-%m-%d')}</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000; padding: 10px; font-weight: bold; background-color: #f0f0f0;">المبلغ:</td>
                        <td style="border: 1px solid #000; padding: 10px; text-align: left; direction: ltr;">1000.00 ₪</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #000; padding: 10px; font-weight: bold; background-color: #f0f0f0;">البيان:</td>
                        <td style="border: 1px solid #000; padding: 10px;">دفعة على حساب</td>
                    </tr>
                </table>
            </div>

            <div class="receipt-signature" style="margin-top: 40px;">
                <div style="display: flex; justify-content: space-between;">
                    <div style="text-align: center; width: 30%;">
                        <div style="border-top: 1px solid #000; margin-top: 50px; padding-top: 5px;">
                            توقيع المستلم
                        </div>
                    </div>
                    <div style="text-align: center; width: 30%;">
                        <div style="border-top: 1px solid #000; margin-top: 50px; padding-top: 5px;">
                            توقيع المحاسب
                        </div>
                    </div>
                </div>
            </div>
        </div>
        """

        return html

    except Exception as e:
        return f"<div>خطأ في إنشاء معاينة السند: {str(e)}</div>"


def generate_report_preview(report_id, options):
    """
    إنشاء معاينة التقرير
    """
    try:
        html = f"""
        <div class="report-preview" style="direction: rtl; font-family: Arial, sans-serif;">
            <div class="report-header" style="text-align: center; margin-bottom: 20px;">
                <h2>تقرير رقم {report_id}</h2>
                <p>تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            </div>

            <div class="report-content" style="margin: 20px 0;">
                <p>محتوى التقرير سيتم عرضه هنا...</p>
            </div>
        </div>
        """

        return html

    except Exception as e:
        return f"<div>خطأ في إنشاء معاينة التقرير: {str(e)}</div>"


def save_print_settings(settings):
    """
    حفظ إعدادات الطباعة
    """
    # هنا يمكن حفظ الإعدادات في قاعدة البيانات أو ملف
    settings_file = os.path.join(current_app.instance_path, 'print_settings.json')
    
    try:
        os.makedirs(os.path.dirname(settings_file), exist_ok=True)
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)
    except Exception as e:
        current_app.logger.error(f"فشل في حفظ إعدادات الطباعة: {str(e)}")
        raise


def get_print_settings():
    """
    جلب إعدادات الطباعة
    """
    settings_file = os.path.join(current_app.instance_path, 'print_settings.json')
    
    default_settings = {
        'paper_size': 'A4',
        'orientation': 'portrait',
        'margins': {
            'top': '1.5cm',
            'right': '1cm',
            'bottom': '1cm',
            'left': '1.5cm'
        },
        'font_family': 'Amiri',
        'font_size': '12pt',
        'include_background': True,
        'include_headers': True
    }
    
    try:
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                saved_settings = json.load(f)
                default_settings.update(saved_settings)
    except Exception as e:
        current_app.logger.error(f"فشل في جلب إعدادات الطباعة: {str(e)}")
    
    return default_settings


# معالج الأخطاء
@print_bp.errorhandler(404)
def print_not_found(error):
    return jsonify({'error': 'المستند المطلوب غير موجود'}), 404


@print_bp.errorhandler(500)
def print_server_error(error):
    return jsonify({'error': 'حدث خطأ في الخادم'}), 500
