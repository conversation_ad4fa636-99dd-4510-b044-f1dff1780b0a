/**
 * Hook موحد لإدارة الإعدادات والقوالب
 * يوفر واجهة سهلة للتفاعل مع النظام المتكامل
 */

import { useState, useEffect, useCallback } from 'react'
import { message } from 'antd'
import { 
  UnifiedPrintSettings, 
  EnhancedTemplate, 
  SettingsValidation,
  createDefaultUnifiedSettings,
  createEmptyTemplate
} from '../types/enhancedTemplateTypes'
import { UnifiedSettingsService } from '../services/UnifiedSettingsService'
import { SafeLogger as Logger } from '../utils/logger'

interface UseUnifiedSettingsReturn {
  // الإعدادات العامة
  globalSettings: UnifiedPrintSettings
  globalLoading: boolean
  globalError: string | null
  
  // القوالب
  templates: EnhancedTemplate[]
  templatesLoading: boolean
  templatesError: string | null
  selectedTemplate: EnhancedTemplate | null
  
  // حالة التهيئة
  isInitialized: boolean
  
  // دوال إدارة الإعدادات العامة
  updateGlobalSettings: (settings: Partial<UnifiedPrintSettings>) => Promise<boolean>
  resetGlobalSettings: () => Promise<boolean>
  
  // دوال إدارة القوالب
  createTemplate: (template?: Partial<EnhancedTemplate>) => EnhancedTemplate
  saveTemplate: (template: EnhancedTemplate) => Promise<boolean>
  deleteTemplate: (id: string) => Promise<boolean>
  selectTemplate: (id: string) => void
  duplicateTemplate: (id: string) => EnhancedTemplate | null
  
  // دوال الوراثة والحساب
  getEffectiveSettings: (template: EnhancedTemplate) => UnifiedPrintSettings
  getTemplatesByCategory: (category: string) => EnhancedTemplate[]
  getTemplatesByType: (type: string) => EnhancedTemplate[]
  
  // دوال التحقق
  validateSettings: (settings: Partial<UnifiedPrintSettings>) => SettingsValidation
  validateTemplate: (template: EnhancedTemplate) => SettingsValidation
  
  // دوال الاستيراد والتصدير
  exportSettings: () => Promise<string>
  importSettings: (data: string) => Promise<boolean>
  
  // دوال إعادة التحميل
  refreshGlobalSettings: () => Promise<void>
  refreshTemplates: () => Promise<void>
  refreshAll: () => Promise<void>
}

export const useUnifiedSettings = (): UseUnifiedSettingsReturn => {
  // الحالة المحلية
  const [globalSettings, setGlobalSettings] = useState<UnifiedPrintSettings>(createDefaultUnifiedSettings())
  const [globalLoading, setGlobalLoading] = useState(true)
  const [globalError, setGlobalError] = useState<string | null>(null)
  
  const [templates, setTemplates] = useState<EnhancedTemplate[]>([])
  const [templatesLoading, setTemplatesLoading] = useState(true)
  const [templatesError, setTemplatesError] = useState<string | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<EnhancedTemplate | null>(null)
  
  const [isInitialized, setIsInitialized] = useState(false)
  
  // خدمة الإعدادات الموحدة
  const settingsService = UnifiedSettingsService.getInstance()

  // ===== تحميل البيانات الأولي =====

  const loadGlobalSettings = useCallback(async () => {
    try {
      setGlobalLoading(true)
      setGlobalError(null)
      
      const settings = await settingsService.loadGlobalSettings()
      setGlobalSettings(settings)
      
      Logger.info('useUnifiedSettings', 'تم تحميل الإعدادات العامة')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في تحميل الإعدادات العامة'
      setGlobalError(errorMessage)
      Logger.error('useUnifiedSettings', 'خطأ في تحميل الإعدادات العامة:', error)
    } finally {
      setGlobalLoading(false)
    }
  }, [settingsService])

  const loadTemplates = useCallback(async () => {
    try {
      setTemplatesLoading(true)
      setTemplatesError(null)
      
      const loadedTemplates = await settingsService.loadTemplates()
      setTemplates(loadedTemplates)
      
      Logger.info('useUnifiedSettings', `تم تحميل ${loadedTemplates.length} قالب`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في تحميل القوالب'
      setTemplatesError(errorMessage)
      Logger.error('useUnifiedSettings', 'خطأ في تحميل القوالب:', error)
    } finally {
      setTemplatesLoading(false)
    }
  }, [settingsService])

  // تحميل البيانات عند التهيئة
  useEffect(() => {
    const initializeData = async () => {
      await Promise.all([
        loadGlobalSettings(),
        loadTemplates()
      ])
      setIsInitialized(true)
    }

    initializeData()
  }, [loadGlobalSettings, loadTemplates])

  // ===== دوال إدارة الإعدادات العامة =====

  const updateGlobalSettings = useCallback(async (newSettings: Partial<UnifiedPrintSettings>): Promise<boolean> => {
    try {
      // التحقق من صحة الإعدادات
      const validation = settingsService.validateSettings(newSettings)
      if (!validation.isValid) {
        message.error(`إعدادات غير صحيحة: ${validation.errors.join(', ')}`)
        return false
      }

      // عرض التحذيرات إن وجدت
      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => message.warning(warning))
      }

      const success = await settingsService.updateGlobalSettings(newSettings)
      
      if (success) {
        const updatedSettings = settingsService.getGlobalSettings()
        setGlobalSettings(updatedSettings)
        message.success('تم تحديث الإعدادات العامة بنجاح')
        Logger.info('useUnifiedSettings', 'تم تحديث الإعدادات العامة')
      } else {
        message.error('فشل في تحديث الإعدادات العامة')
      }

      return success
    } catch (error) {
      Logger.error('useUnifiedSettings', 'خطأ في تحديث الإعدادات العامة:', error)
      message.error('حدث خطأ في تحديث الإعدادات العامة')
      return false
    }
  }, [settingsService])

  const resetGlobalSettings = useCallback(async (): Promise<boolean> => {
    try {
      const defaultSettings = createDefaultUnifiedSettings()
      const success = await settingsService.saveGlobalSettings(defaultSettings)
      
      if (success) {
        setGlobalSettings(defaultSettings)
        message.success('تم إعادة تعيين الإعدادات العامة')
        Logger.info('useUnifiedSettings', 'تم إعادة تعيين الإعدادات العامة')
      } else {
        message.error('فشل في إعادة تعيين الإعدادات العامة')
      }

      return success
    } catch (error) {
      Logger.error('useUnifiedSettings', 'خطأ في إعادة تعيين الإعدادات العامة:', error)
      message.error('حدث خطأ في إعادة تعيين الإعدادات العامة')
      return false
    }
  }, [settingsService])

  // ===== دوال إدارة القوالب =====

  const createTemplate = useCallback((templateData?: Partial<EnhancedTemplate>): EnhancedTemplate => {
    const newTemplate = createEmptyTemplate()
    
    if (templateData) {
      // دمج البيانات المرسلة
      if (templateData.metadata) {
        newTemplate.metadata = { ...newTemplate.metadata, ...templateData.metadata }
      }
      if (templateData.inheritance) {
        newTemplate.inheritance = { ...newTemplate.inheritance, ...templateData.inheritance }
      }
      if (templateData.columns) {
        newTemplate.columns = [...templateData.columns]
      }
    }

    Logger.info('useUnifiedSettings', 'تم إنشاء قالب جديد')
    return newTemplate
  }, [])

  const saveTemplate = useCallback(async (template: EnhancedTemplate): Promise<boolean> => {
    try {
      // التحقق من صحة القالب
      const validation = settingsService.validateTemplate(template)
      if (!validation.isValid) {
        message.error(`قالب غير صحيح: ${validation.errors.join(', ')}`)
        return false
      }

      // عرض التحذيرات إن وجدت
      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => message.warning(warning))
      }

      const success = await settingsService.saveTemplate(template)
      
      if (success) {
        // تحديث القائمة المحلية
        setTemplates(prev => {
          const index = prev.findIndex(t => t.metadata.id === template.metadata.id)
          if (index >= 0) {
            // تحديث قالب موجود
            const updated = [...prev]
            updated[index] = template
            return updated
          } else {
            // إضافة قالب جديد
            return [...prev, template]
          }
        })
        
        message.success(`تم حفظ القالب: ${template.metadata.name}`)
        Logger.info('useUnifiedSettings', `تم حفظ القالب: ${template.metadata.name}`)
      } else {
        message.error('فشل في حفظ القالب')
      }

      return success
    } catch (error) {
      Logger.error('useUnifiedSettings', 'خطأ في حفظ القالب:', error)
      message.error('حدث خطأ في حفظ القالب')
      return false
    }
  }, [settingsService])

  const deleteTemplate = useCallback(async (id: string): Promise<boolean> => {
    try {
      const template = templates.find(t => t.metadata.id === id)
      if (!template) {
        message.error('القالب غير موجود')
        return false
      }

      if (template.metadata.isDefault) {
        message.error('لا يمكن حذف القالب الافتراضي')
        return false
      }

      const success = await settingsService.deleteTemplate(id)
      
      if (success) {
        setTemplates(prev => prev.filter(t => t.metadata.id !== id))
        
        // إلغاء التحديد إذا كان القالب المحذوف محدد
        if (selectedTemplate?.metadata.id === id) {
          setSelectedTemplate(null)
        }
        
        message.success('تم حذف القالب بنجاح')
        Logger.info('useUnifiedSettings', `تم حذف القالب: ${id}`)
      } else {
        message.error('فشل في حذف القالب')
      }

      return success
    } catch (error) {
      Logger.error('useUnifiedSettings', 'خطأ في حذف القالب:', error)
      message.error('حدث خطأ في حذف القالب')
      return false
    }
  }, [settingsService, templates, selectedTemplate])

  const selectTemplate = useCallback((id: string) => {
    const template = templates.find(t => t.metadata.id === id)
    setSelectedTemplate(template || null)
    
    if (template) {
      Logger.info('useUnifiedSettings', `تم تحديد القالب: ${template.metadata.name}`)
    }
  }, [templates])

  const duplicateTemplate = useCallback((id: string): EnhancedTemplate | null => {
    const originalTemplate = templates.find(t => t.metadata.id === id)
    if (!originalTemplate) {
      message.error('القالب غير موجود')
      return null
    }

    const duplicatedTemplate: EnhancedTemplate = {
      metadata: {
        ...originalTemplate.metadata,
        id: `template-${Date.now()}`,
        name: `${originalTemplate.metadata.name} - نسخة`,
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      inheritance: { ...originalTemplate.inheritance },
      columns: originalTemplate.columns ? [...originalTemplate.columns] : undefined,
      typeSpecificSettings: originalTemplate.typeSpecificSettings ? 
        { ...originalTemplate.typeSpecificSettings } : undefined
    }

    Logger.info('useUnifiedSettings', `تم نسخ القالب: ${originalTemplate.metadata.name}`)
    return duplicatedTemplate
  }, [templates])

  // ===== دوال الوراثة والحساب =====

  const getEffectiveSettings = useCallback((template: EnhancedTemplate): UnifiedPrintSettings => {
    return settingsService.getEffectiveSettings(template)
  }, [settingsService])

  const getTemplatesByCategory = useCallback((category: string): EnhancedTemplate[] => {
    return templates.filter(t => t.metadata.category === category && t.metadata.isActive)
  }, [templates])

  const getTemplatesByType = useCallback((type: string): EnhancedTemplate[] => {
    return templates.filter(t => t.metadata.type === type && t.metadata.isActive)
  }, [templates])

  // ===== دوال التحقق =====

  const validateSettings = useCallback((settings: Partial<UnifiedPrintSettings>): SettingsValidation => {
    return settingsService.validateSettings(settings)
  }, [settingsService])

  const validateTemplate = useCallback((template: EnhancedTemplate): SettingsValidation => {
    return settingsService.validateTemplate(template)
  }, [settingsService])

  // ===== دوال الاستيراد والتصدير =====

  const exportSettings = useCallback(async (): Promise<string> => {
    try {
      const exportData = {
        globalSettings,
        templates,
        exportDate: new Date().toISOString(),
        version: '1.0.0'
      }
      
      const jsonString = JSON.stringify(exportData, null, 2)
      Logger.info('useUnifiedSettings', 'تم تصدير الإعدادات')
      return jsonString
    } catch (error) {
      Logger.error('useUnifiedSettings', 'خطأ في تصدير الإعدادات:', error)
      throw new Error('فشل في تصدير الإعدادات')
    }
  }, [globalSettings, templates])

  const importSettings = useCallback(async (data: string): Promise<boolean> => {
    try {
      const importData = JSON.parse(data)
      
      if (importData.globalSettings) {
        await updateGlobalSettings(importData.globalSettings)
      }
      
      if (importData.templates && Array.isArray(importData.templates)) {
        for (const template of importData.templates) {
          await saveTemplate(template)
        }
      }
      
      message.success('تم استيراد الإعدادات بنجاح')
      Logger.info('useUnifiedSettings', 'تم استيراد الإعدادات')
      return true
    } catch (error) {
      Logger.error('useUnifiedSettings', 'خطأ في استيراد الإعدادات:', error)
      message.error('فشل في استيراد الإعدادات - تأكد من صحة الملف')
      return false
    }
  }, [updateGlobalSettings, saveTemplate])

  // ===== دوال إعادة التحميل =====

  const refreshGlobalSettings = useCallback(async () => {
    await loadGlobalSettings()
  }, [loadGlobalSettings])

  const refreshTemplates = useCallback(async () => {
    await loadTemplates()
  }, [loadTemplates])

  const refreshAll = useCallback(async () => {
    await Promise.all([
      loadGlobalSettings(),
      loadTemplates()
    ])
  }, [loadGlobalSettings, loadTemplates])

  return {
    // الإعدادات العامة
    globalSettings,
    globalLoading,
    globalError,
    
    // القوالب
    templates,
    templatesLoading,
    templatesError,
    selectedTemplate,
    
    // حالة التهيئة
    isInitialized,
    
    // دوال إدارة الإعدادات العامة
    updateGlobalSettings,
    resetGlobalSettings,
    
    // دوال إدارة القوالب
    createTemplate,
    saveTemplate,
    deleteTemplate,
    selectTemplate,
    duplicateTemplate,
    
    // دوال الوراثة والحساب
    getEffectiveSettings,
    getTemplatesByCategory,
    getTemplatesByType,
    
    // دوال التحقق
    validateSettings,
    validateTemplate,
    
    // دوال الاستيراد والتصدير
    exportSettings,
    importSettings,
    
    // دوال إعادة التحميل
    refreshGlobalSettings,
    refreshTemplates,
    refreshAll
  }
}
