import { SafeLogger as Logger } from './logger'
// Early Polyfill - يتم تحميله قبل أي شيء آخر
(function() {
  'use strict'

  // إنشاء Logger مبسط للتهيئة المبكرة
  if (typeof window !== 'undefined' && !(window as any).Logger) {
    (window as any).Logger = {
      error: (module: string, message: string, error?: unknown) => {
        Logger.error('EarlyPolyfill', `[${module}] ${message}`, error || '')
      },
      warn: (module: string, message: string, data?: any) => {
        Logger.warn('EarlyPolyfill', `[${module}] ${message}`, data || '')
      },
      info: (module: string, message: string, data?: any) => {
        console.info(`[${module}] ${message}`, data || '')
      },
      debug: (module: string, message: string, data?: any) => {
        console.debug(`[${module}] ${message}`, data || '')
      },
      success: (module: string, message: string, data?: any) => {
        Logger.info('EarlyPolyfill', `[${module}] ${message}`, data || '')
      }
    }
  }

  // إنشاء Buffer مبسط للتهيئة المبكرة
  if (typeof window !== 'undefined' && !window.Buffer) {
    (window as any).Buffer = {
      concat: (list: Uint8Array[], totalLength?: number) => {
        if (!Array.isArray(list) || list.length === 0) {
          return new Uint8Array(0)
        }
        if (list.length === 1) {
          return list[0]
        }
        if (totalLength === undefined) {
          totalLength = list.reduce((sum, buf) => sum + buf.length, 0)
        }
        const result = new Uint8Array(totalLength)
        let offset = 0
        for (const buf of list) {
          result.set(buf, offset)
          offset += buf.length
        }
        return result
      },
      from: (data: any) => {
        if (data instanceof Uint8Array) return data
        if (typeof data === 'string') {
          return new TextEncoder().encode(data)
        }
        if (Array.isArray(data)) {
          return new Uint8Array(data)
        }
        return new Uint8Array(0)
      },
      alloc: (size: number) => new Uint8Array(size),
      isBuffer: (obj: any) => obj instanceof Uint8Array
    }
  }

  // إنشاء أيقونات بديلة للتهيئة المبكرة
  if (typeof window !== 'undefined') {
    const createFallbackIcon = (name: string) => {
      return function(props: any) {
        return {
          type: 'span',
          props: {
            ...props,
            className: `anticon anticon-${name.toLowerCase()} ${props.className || ''}`,
            children: '📄'
          }
        }
      }
    }

    if (!(window as any).FileTextOutlined) {
      (window as any).FileTextOutlined = createFallbackIcon('file-text')
    }
  }

  console.info('[EarlyPolyfill] ✅ تم تفعيل Early polyfills بنجاح')
})()

export {}
