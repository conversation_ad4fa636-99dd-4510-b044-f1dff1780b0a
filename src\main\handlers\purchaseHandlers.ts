import { ipcMain } from 'electron'
import { PurchaseService, SupplierService, AuthService } from '../services'
import { Logger } from '../utils/logger'

let purchaseService: PurchaseService
let supplierService: SupplierService

export function setPurchaseServices(purchase: PurchaseService, supplier: SupplierService) {
  purchaseService = purchase
  supplierService = supplier
}

function registerPurchaseHandlers(): void {
  // الموردين
  ipcMain.handle('get-suppliers', async () => {
    try {
      const suppliers = await supplierService.getSuppliers()
      return { success: true, data: suppliers }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في جلب الموردين:', error)
      return { success: false, message: 'حدث خطأ في جلب الموردين' }
    }
  })

  ipcMain.handle('create-supplier', async (_event, supplierData: any) => {
    try {
      return await supplierService.createSupplier(supplierData)
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في إنشاء المورد:', error)
      return { success: false, message: 'حدث خطأ في إنشاء المورد' }
    }
  })

  ipcMain.handle('update-supplier', async (_event, supplierData: any) => {
    try {
      return await supplierService.updateSupplier(supplierData)
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تحديث المورد:', error)
      return { success: false, message: 'حدث خطأ في تحديث المورد' }
    }
  })

  ipcMain.handle('delete-supplier', async (_event, supplierId: number) => {
    try {
      return await supplierService.deleteSupplier(supplierId)
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في حذف المورد:', error)
      return { success: false, message: 'حدث خطأ في حذف المورد' }
    }
  })

  ipcMain.handle('generate-supplier-code', async () => {
    try {
      const code = await supplierService.generateSupplierCode()
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في توليد كود المورد:', error)
      return { success: false, message: 'حدث خطأ في توليد كود المورد' }
    }
  })

  // فواتير المشتريات
  ipcMain.handle('get-purchase-invoices', async () => {
    try {
      const invoices = await purchaseService.getPurchaseInvoices()
      return { success: true, data: invoices }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في جلب فواتير المشتريات:', error)
      return { success: false, message: 'حدث خطأ في جلب فواتير المشتريات' }
    }
  })

  ipcMain.handle('create-purchase-invoice', async (_event, invoiceData: any) => {
    try {
      return await purchaseService.createPurchaseInvoice(invoiceData)
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في إنشاء فاتورة المشتريات:', error)
      return { success: false, message: 'حدث خطأ في إنشاء فاتورة المشتريات' }
    }
  })

  ipcMain.handle('update-purchase-invoice', async (_, invoiceId: number, invoiceData: any) => {
    try {
      return await purchaseService.updatePurchaseInvoice(invoiceId, invoiceData)
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تحديث فاتورة المشتريات:', error)
      return { success: false, message: 'حدث خطأ في تحديث فاتورة المشتريات' }
    }
  })

  ipcMain.handle('delete-purchase-invoice', async (_, invoiceId: number) => {
    try {
      // الحصول على دور المستخدم الحالي
      const userRole = AuthService.getCurrentUserRole()

      return await purchaseService.deletePurchaseInvoice(invoiceId, userRole || undefined)
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في حذف فاتورة المشتريات:', error)
      return { success: false, message: 'حدث خطأ في حذف فاتورة المشتريات' }
    }
  })

  ipcMain.handle('get-purchase-invoice-items', async (_event, invoiceId: number) => {
    try {
      const items = await purchaseService.getPurchaseInvoiceItems(invoiceId)
      return { success: true, data: items }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في جلب تفاصيل فاتورة المشتريات:', error)
      return { success: false, message: 'حدث خطأ في جلب تفاصيل فاتورة المشتريات' }
    }
  })

  ipcMain.handle('update-purchase-invoice-status', async (_, invoiceId: number, status: string) => {
    try {
      return await purchaseService.updatePurchaseInvoiceStatus(invoiceId, status)
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تحديث حالة فاتورة المشتريات:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة فاتورة المشتريات' }
    }
  })

  ipcMain.handle('generate-purchase-invoice-number', async () => {
    try {
      const invoiceNumber = await purchaseService.generateInvoiceNumber()
      return { success: true, data: { invoiceNumber } }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في توليد رقم فاتورة المشتريات:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم فاتورة المشتريات' }
    }
  })

  // أوامر الشراء
  ipcMain.handle('get-purchase-orders', async () => {
    try {
      const orders = await purchaseService.getPurchaseOrders()
      return { success: true, data: orders }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في جلب أوامر الشراء:', error)
      return { success: false, message: 'حدث خطأ في جلب أوامر الشراء' }
    }
  })

  ipcMain.handle('create-purchase-order', async (_event, orderData: any) => {
    try {
      return await purchaseService.createPurchaseOrder(orderData)
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في إنشاء أمر الشراء:', error)
      return { success: false, message: 'حدث خطأ في إنشاء أمر الشراء' }
    }
  })

  ipcMain.handle('update-purchase-order', async (_, orderId: number, orderData: any) => {
    try {
      return await purchaseService.updatePurchaseOrder(orderId, orderData)
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تحديث أمر الشراء:', error)
      return { success: false, message: 'حدث خطأ في تحديث أمر الشراء' }
    }
  })

  ipcMain.handle('delete-purchase-order', async (_, orderId: number) => {
    try {
      // الحصول على دور المستخدم الحالي
      const userRole = AuthService.getCurrentUserRole()

      return await purchaseService.deletePurchaseOrder(orderId, userRole || undefined)
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في حذف أمر الشراء:', error)
      return { success: false, message: 'حدث خطأ في حذف أمر الشراء' }
    }
  })

  ipcMain.handle('get-purchase-order-items', async (_, orderId: number) => {
    try {
      const items = await purchaseService.getPurchaseOrderItems(orderId)
      return { success: true, data: items }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في جلب تفاصيل أمر الشراء:', error)
      return { success: false, message: 'حدث خطأ في جلب تفاصيل أمر الشراء' }
    }
  })

  ipcMain.handle('update-purchase-order-status', async (_, orderId: number, status: string) => {
    try {
      return await purchaseService.updatePurchaseOrderStatus(orderId, status)
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تحديث حالة أمر الشراء:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة أمر الشراء' }
    }
  })

  ipcMain.handle('convert-order-to-invoice', async (_, orderId: number) => {
    try {
      return await purchaseService.convertOrderToInvoice(orderId)
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تحويل أمر الشراء إلى فاتورة:', error)
      return { success: false, message: 'حدث خطأ في تحويل أمر الشراء إلى فاتورة' }
    }
  })

  ipcMain.handle('generate-purchase-order-number', async () => {
    try {
      const orderNumber = await purchaseService.generateOrderNumber()
      return { success: true, data: { orderNumber } }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في توليد رقم أمر الشراء:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم أمر الشراء' }
    }
  })



  // تقارير المشتريات
  ipcMain.handle('get-purchases-by-supplier-report', async (_, filters: any) => {
    try {
      const data = await purchaseService.getPurchasesBySupplierReport(filters)
      return { success: true, data }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تقرير المشتريات حسب المورد:', error)
      return { success: false, message: 'حدث خطأ في تقرير المشتريات حسب المورد' }
    }
  })

  ipcMain.handle('get-purchases-by-item-report', async (_, filters: any) => {
    try {
      const data = await purchaseService.getPurchasesByItemReport(filters)
      return { success: true, data }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تقرير المشتريات حسب الصنف:', error)
      return { success: false, message: 'حدث خطأ في تقرير المشتريات حسب الصنف' }
    }
  })

  ipcMain.handle('get-supplier-payables-report', async (_, filters: any) => {
    try {
      const data = await purchaseService.getSupplierPayablesReport(filters)
      return { success: true, data }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تقرير مديونيات الموردين:', error)
      return { success: false, message: 'حدث خطأ في تقرير مديونيات الموردين' }
    }
  })

  ipcMain.handle('get-purchase-analysis-report', async (_, filters: any) => {
    try {
      const data = await purchaseService.getPurchaseAnalysisReport(filters)
      return { success: true, data }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تقرير تحليل أداء المشتريات:', error)
      return { success: false, message: 'حدث خطأ في تقرير تحليل أداء المشتريات' }
    }
  })

  ipcMain.handle('get-cost-analysis-report', async (_, filters: any) => {
    try {
      const data = await purchaseService.getCostAnalysisReport(filters)
      return { success: true, data }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تقرير تحليل التكاليف:', error)
      return { success: false, message: 'حدث خطأ في تقرير تحليل التكاليف' }
    }
  })

  // التقارير المتقدمة للمشتريات
  ipcMain.handle('get-supplier-analysis-report', async (_, filters: any) => {
    try {
      const data = await purchaseService.getSupplierAnalysisReport(filters)
      return { success: true, data }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تقرير تحليل الموردين:', error)
      return { success: false, message: 'حدث خطأ في تحميل تقرير تحليل الموردين' }
    }
  })

  // تم نقل معالج get-supplier-quality-report إلى reportsHandlers.ts لتجنب التكرار

  ipcMain.handle('get-supplier-price-comparison-report', async (_, filters: any) => {
    try {
      const data = await purchaseService.getSupplierPriceComparison(filters)
      return { success: true, data }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في تقرير مقارنة أسعار الموردين:', error)
      return { success: false, message: 'حدث خطأ في تحميل تقرير مقارنة أسعار الموردين' }
    }
  })

  ipcMain.handle('get-purchase-metrics', async (_, _filters: any) => {
    try {
      // بيانات وهمية لمقاييس المشتريات
      const data = {
        totalPurchases: 1250000,
        totalSuppliers: 45,
        avgOrderValue: 15750,
        onTimeDeliveryRate: 87.5,
        qualityScore: 4.2,
        costSavings: 125000,
        pendingOrders: 12,
        overdueOrders: 3
      }
      return { success: true, data }
    } catch (error) {
      Logger.error('PurchaseHandlers', 'خطأ في مقاييس المشتريات:', error)
      return { success: false, message: 'حدث خطأ في تحميل مقاييس المشتريات' }
    }
  })

  // تم إزالة معالجات البيانات التجريبية - يجب استخدام البيانات الحقيقية فقط
}

export { registerPurchaseHandlers }
