import { useContext, useState, useEffect, useCallback } from 'react'
import { CentralizedPrintSettings } from '../types/print'
import { PrintSettingsContext } from '../contexts/PrintSettingsContext'
import { MasterPrintService } from '../services/MasterPrintService'
import { Logger } from '../utils/logger'

// نوع للاستجابة
interface UsePrintSettingsReturn {
  settings: CentralizedPrintSettings | null
  loading: boolean
  error: string | null
  updateSettings: (newSettings: Partial<CentralizedPrintSettings>) => Promise<void>
  resetSettings: () => Promise<void>
  refreshSettings: () => Promise<void>
  isReady: boolean
}

// Hook مركزي لإدارة إعدادات الطباعة - يستخدم Context داخلياً
export const usePrintSettings = (): UsePrintSettingsReturn => {
  const context = useContext(PrintSettingsContext)
  const localSettings = useLocalPrintSettings()

  // إذا كان Context متاحاً، استخدمه، وإلا استخدم التنفيذ المحلي
  return context || localSettings
}

// التنفيذ المحلي كـ fallback
const useLocalPrintSettings = (): UsePrintSettingsReturn => {
  const [settings, setSettings] = useState<CentralizedPrintSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isReady, setIsReady] = useState(false)

  const printService = MasterPrintService.getInstance()

  // تحميل الإعدادات من المصدر المركزي
  const loadSettings = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // الحصول على الإعدادات من قاعدة البيانات
      const savedSettings = await window.electronAPI?.invoke('get-print-settings')

      if (savedSettings) {
        const centralizedSettings: CentralizedPrintSettings = {
          ...printService.getDefaultOptions(),
          ...savedSettings,
          autoSync: true,
          lastUpdated: new Date().toISOString(),
          version: '1.0.0'
        }

        setSettings(centralizedSettings)

        // تطبيق الإعدادات على خدمة الطباعة
        printService.updateSettings(centralizedSettings)

        // تطبيق الألوان على متغيرات CSS
        applyColorsToCSS(centralizedSettings)

        Logger.info('usePrintSettings', 'تم تحميل الإعدادات المركزية:', centralizedSettings)
      } else {
        // استخدام الإعدادات الافتراضية
        const defaultSettings: CentralizedPrintSettings = {
          ...printService.getDefaultOptions(),
          autoSync: true,
          lastUpdated: new Date().toISOString(),
          version: '1.0.0'
        }

        setSettings(defaultSettings)
        await saveSettings(defaultSettings)
      }

      setIsReady(true)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'خطأ في تحميل إعدادات الطباعة'
      setError(errorMessage)
      Logger.error('usePrintSettings', 'خطأ في تحميل الإعدادات:', err)
    } finally {
      setLoading(false)
    }
  }, [printService])

  // حفظ الإعدادات في قاعدة البيانات
  const saveSettings = useCallback(async (newSettings: CentralizedPrintSettings) => {
    try {
      await window.electronAPI?.invoke('save-print-settings', newSettings)
      Logger.info('usePrintSettings', 'تم حفظ الإعدادات:', newSettings)
    } catch (err) {
      Logger.error('usePrintSettings', 'خطأ في حفظ الإعدادات:', err)
      throw err
    }
  }, [])

  // تطبيق الألوان على متغيرات CSS
  const applyColorsToCSS = useCallback((settings: CentralizedPrintSettings) => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement

      if (settings.primaryColor) {
        root.style.setProperty('--print-primary-color', settings.primaryColor)
      }
      if (settings.secondaryColor) {
        root.style.setProperty('--print-secondary-color', settings.secondaryColor)
      }
      if (settings.borderColor) {
        root.style.setProperty('--print-border-color', settings.borderColor)
      }
      if (settings.backgroundColor) {
        root.style.setProperty('--print-background-color', settings.backgroundColor)
      }
      if (settings.textColor) {
        root.style.setProperty('--print-text-color', settings.textColor)
      }

      Logger.info('usePrintSettings', 'تم تطبيق الألوان على CSS')
    }
  }, [])

  // تحديث الإعدادات
  const updateSettings = useCallback(async (newSettings: Partial<CentralizedPrintSettings>) => {
    if (!settings) return

    try {
      setLoading(true)
      setError(null)

      const updatedSettings: CentralizedPrintSettings = {
        ...settings,
        ...newSettings,
        lastUpdated: new Date().toISOString()
      }

      setSettings(updatedSettings)

      // حفظ في قاعدة البيانات
      await saveSettings(updatedSettings)

      // تطبيق على خدمة الطباعة
      printService.updateSettings(updatedSettings)

      // تطبيق الألوان على CSS
      applyColorsToCSS(updatedSettings)

      Logger.info('usePrintSettings', 'تم تحديث الإعدادات:', updatedSettings)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'خطأ في تحديث الإعدادات'
      setError(errorMessage)
      Logger.error('usePrintSettings', 'خطأ في تحديث الإعدادات:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [settings, saveSettings, printService, applyColorsToCSS])

  // إعادة تعيين الإعدادات للافتراضية
  const resetSettings = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const defaultSettings: CentralizedPrintSettings = {
        ...printService.getDefaultOptions(),
        autoSync: true,
        lastUpdated: new Date().toISOString(),
        version: '1.0.0'
      }

      setSettings(defaultSettings)
      await saveSettings(defaultSettings)
      printService.updateSettings(defaultSettings)
      applyColorsToCSS(defaultSettings)

      Logger.info('usePrintSettings', 'تم إعادة تعيين الإعدادات للافتراضية')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'خطأ في إعادة تعيين الإعدادات'
      setError(errorMessage)
      Logger.error('usePrintSettings', 'خطأ في إعادة تعيين الإعدادات:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }, [printService, saveSettings, applyColorsToCSS])

  // تحديث الإعدادات من المصدر
  const refreshSettings = useCallback(async () => {
    await loadSettings()
  }, [loadSettings])

  // تحميل الإعدادات عند بدء التشغيل
  useEffect(() => {
    loadSettings()
  }, [loadSettings])

  return {
    settings,
    loading,
    error,
    updateSettings,
    resetSettings,
    refreshSettings,
    isReady
  }
}

// Hook مبسط للحصول على الألوان فقط
export const usePrintColors = () => {
  const { settings, isReady } = usePrintSettings()
  
  return {
    primaryColor: settings?.primaryColor || '#1890ff',
    secondaryColor: settings?.secondaryColor || '#fff3cd',
    borderColor: settings?.borderColor || '#d9d9d9',
    backgroundColor: settings?.backgroundColor || '#ffffff',
    textColor: settings?.textColor || '#000000',
    isReady
  }
}

// Hook للحصول على إعدادات نوع معين من المستندات
export const useDocumentPrintSettings = (documentType: 'invoice' | 'receipt' | 'report') => {
  const { settings, updateSettings, isReady } = usePrintSettings()
  
  // إعدادات مخصصة حسب نوع المستند
  const getDocumentSpecificSettings = useCallback(() => {
    if (!settings) return null
    
    const baseSettings = { ...settings }
    
    switch (documentType) {
      case 'receipt':
        return {
          ...baseSettings,
          pageSize: 'A5' as const,
          fontSize: 11,
          showTerms: false
        }
      case 'report':
        return {
          ...baseSettings,
          pageSize: 'A4' as const,
          fontSize: 10,
          showSignature: false
        }
      default: // invoice
        return baseSettings
    }
  }, [settings, documentType])
  
  return {
    settings: getDocumentSpecificSettings(),
    updateSettings,
    isReady
  }
}
