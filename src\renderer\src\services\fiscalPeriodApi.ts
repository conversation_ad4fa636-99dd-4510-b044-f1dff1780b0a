import {
  FiscalPeriod,
  CreateFiscalPeriodData,
  UpdateFiscalPeriodData,
  ClosingValidation,
  ClosingReport,
  ClosingStatistics,
  ApiResponse,
  FiscalPeriodFilters,
  PeriodComparison,
  ValidationResult,
  ValidationSummary,
  ClosingProgress,
  ClosingNotification,
  NotificationSettings
} from '../types/fiscalPeriod'

/**
 * خدمة API للتفاعل مع نظام الإقفال المحاسبي
 */
export class FiscalPeriodApi {
  /**
   * الحصول على جميع الفترات المالية
   */
  static async getFiscalPeriods(filters?: FiscalPeriodFilters): Promise<ApiResponse<FiscalPeriod[]>> {
    try {
      return await window.electronAPI.invoke('get-fiscal-periods', filters)
    } catch (error) {
      console.error('خطأ في جلب الفترات المالية:', error)
      return { success: false, message: 'حدث خطأ في جلب الفترات المالية' }
    }
  }

  /**
   * الحصول على الفترة المالية الحالية
   */
  static async getCurrentFiscalPeriod(): Promise<ApiResponse<FiscalPeriod>> {
    try {
      return await window.electronAPI.invoke('get-current-fiscal-period')
    } catch (error) {
      console.error('خطأ في جلب الفترة المالية الحالية:', error)
      return { success: false, message: 'حدث خطأ في جلب الفترة المالية الحالية' }
    }
  }

  /**
   * إنشاء فترة مالية جديدة
   */
  static async createFiscalPeriod(periodData: CreateFiscalPeriodData, userId?: number): Promise<ApiResponse<FiscalPeriod>> {
    try {
      return await window.electronAPI.invoke('create-fiscal-period', periodData, userId)
    } catch (error) {
      console.error('خطأ في إنشاء الفترة المالية:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الفترة المالية' }
    }
  }

  /**
   * تحديث فترة مالية
   */
  static async updateFiscalPeriod(
    periodId: number, 
    updateData: UpdateFiscalPeriodData, 
    userId?: number
  ): Promise<ApiResponse<FiscalPeriod>> {
    try {
      return await window.electronAPI.invoke('update-fiscal-period', periodId, updateData, userId)
    } catch (error) {
      console.error('خطأ في تحديث الفترة المالية:', error)
      return { success: false, message: 'حدث خطأ في تحديث الفترة المالية' }
    }
  }

  /**
   * التحقق من إمكانية إقفال الفترة
   */
  static async validatePeriodClosing(periodId: number): Promise<ApiResponse<ClosingValidation>> {
    try {
      return await window.electronAPI.invoke('validate-period-closing', periodId)
    } catch (error) {
      console.error('خطأ في التحقق من إمكانية الإقفال:', error)
      return { success: false, message: 'حدث خطأ في التحقق من إمكانية الإقفال' }
    }
  }

  /**
   * إقفال الفترة المالية
   */
  static async closeFiscalPeriod(periodId: number, userId: number): Promise<ApiResponse> {
    try {
      return await window.electronAPI.invoke('close-fiscal-period', periodId, userId)
    } catch (error) {
      console.error('خطأ في إقفال الفترة المالية:', error)
      return { success: false, message: 'حدث خطأ في إقفال الفترة المالية' }
    }
  }

  /**
   * إعادة فتح الفترة المالية
   */
  static async reopenFiscalPeriod(periodId: number, userId: number, reason?: string): Promise<ApiResponse> {
    try {
      return await window.electronAPI.invoke('reopen-fiscal-period', periodId, userId, reason)
    } catch (error) {
      console.error('خطأ في إعادة فتح الفترة المالية:', error)
      return { success: false, message: 'حدث خطأ في إعادة فتح الفترة المالية' }
    }
  }

  /**
   * حذف فترة مالية
   */
  static async deleteFiscalPeriod(periodId: number, userId: number): Promise<ApiResponse> {
    try {
      return await window.electronAPI.invoke('delete-fiscal-period', periodId, userId)
    } catch (error) {
      console.error('خطأ في حذف الفترة المالية:', error)
      return { success: false, message: 'حدث خطأ في حذف الفترة المالية' }
    }
  }

  /**
   * الحصول على تقرير الإقفال
   */
  static async getClosingReport(periodId: number): Promise<ApiResponse<ClosingReport>> {
    try {
      return await window.electronAPI.invoke('get-closing-report', periodId)
    } catch (error) {
      console.error('خطأ في جلب تقرير الإقفال:', error)
      return { success: false, message: 'حدث خطأ في جلب تقرير الإقفال' }
    }
  }

  /**
   * التحقق من حماية البيانات
   */
  static async checkDataProtection(periodId: number, tableName: string, recordId: number): Promise<ApiResponse<{ isProtected: boolean }>> {
    try {
      return await window.electronAPI.invoke('check-data-protection', periodId, tableName, recordId)
    } catch (error) {
      console.error('خطأ في التحقق من حماية البيانات:', error)
      return { success: false, message: 'حدث خطأ في التحقق من حماية البيانات' }
    }
  }

  /**
   * إضافة حماية للبيانات
   */
  static async addDataProtection(
    periodId: number, 
    tableName: string, 
    recordId: number, 
    protectionLevel: 'read_only' | 'no_access' | 'admin_only'
  ): Promise<ApiResponse> {
    try {
      return await window.electronAPI.invoke('add-data-protection', periodId, tableName, recordId, protectionLevel)
    } catch (error) {
      console.error('خطأ في إضافة حماية البيانات:', error)
      return { success: false, message: 'حدث خطأ في إضافة حماية البيانات' }
    }
  }

  /**
   * الحصول على إحصائيات الإقفال
   */
  static async getClosingStatistics(): Promise<ApiResponse<ClosingStatistics>> {
    try {
      return await window.electronAPI.invoke('get-closing-statistics')
    } catch (error) {
      console.error('خطأ في جلب إحصائيات الإقفال:', error)
      return { success: false, message: 'حدث خطأ في جلب إحصائيات الإقفال' }
    }
  }

  // خدمات التحقق من صحة البيانات
  /**
   * التحقق من صحة البيانات للإقفال
   */
  static async validateClosingData(startDate: string, endDate: string): Promise<ApiResponse> {
    try {
      return await window.electronAPI.invoke('validate-closing-data', startDate, endDate)
    } catch (error) {
      console.error('خطأ في التحقق من صحة البيانات:', error)
      return { success: false, message: 'حدث خطأ في التحقق من صحة البيانات' }
    }
  }

  /**
   * الحصول على ميزان المراجعة
   */
  static async getTrialBalance(startDate: string, endDate: string): Promise<ApiResponse> {
    try {
      return await window.electronAPI.invoke('get-trial-balance', startDate, endDate)
    } catch (error) {
      console.error('خطأ في جلب ميزان المراجعة:', error)
      return { success: false, message: 'حدث خطأ في جلب ميزان المراجعة' }
    }
  }

  /**
   * إنشاء تقرير التحقق التفصيلي
   */
  static async generateValidationReport(startDate: string, endDate: string): Promise<ApiResponse> {
    try {
      return await window.electronAPI.invoke('generate-validation-report', startDate, endDate)
    } catch (error) {
      console.error('خطأ في إنشاء تقرير التحقق:', error)
      return { success: false, message: 'حدث خطأ في إنشاء تقرير التحقق' }
    }
  }

  // خدمات النسخ الاحتياطية
  /**
   * إنشاء نسخة احتياطية للإقفال
   */
  static async createClosingBackup(periodId: number, closingType: string, description?: string): Promise<ApiResponse> {
    try {
      return await window.electronAPI.invoke('create-closing-backup', periodId, closingType, description)
    } catch (error) {
      console.error('خطأ في إنشاء النسخة الاحتياطية:', error)
      return { success: false, message: 'حدث خطأ في إنشاء النسخة الاحتياطية' }
    }
  }

  /**
   * الحصول على النسخ الاحتياطية للإقفال
   */
  static async getClosingBackups(periodId?: number): Promise<ApiResponse> {
    try {
      return await window.electronAPI.invoke('get-closing-backups', periodId)
    } catch (error) {
      console.error('خطأ في جلب النسخ الاحتياطية:', error)
      return { success: false, message: 'حدث خطأ في جلب النسخ الاحتياطية' }
    }
  }

  /**
   * التحقق من سلامة النسخة الاحتياطية
   */
  static async verifyBackupIntegrity(backupId: number): Promise<ApiResponse> {
    try {
      return await window.electronAPI.invoke('verify-backup-integrity', backupId)
    } catch (error) {
      console.error('خطأ في التحقق من سلامة النسخة الاحتياطية:', error)
      return { success: false, message: 'حدث خطأ في التحقق من سلامة النسخة الاحتياطية' }
    }
  }

  // خدمات التقارير
  /**
   * إنشاء تقرير الإقفال الشامل
   */
  static async generateClosingReport(periodId: number): Promise<ApiResponse> {
    try {
      return await window.electronAPI.invoke('generate-closing-report', periodId)
    } catch (error) {
      console.error('خطأ في إنشاء تقرير الإقفال:', error)
      return { success: false, message: 'حدث خطأ في إنشاء تقرير الإقفال' }
    }
  }

  /**
   * إنشاء تقرير مقارنة الفترات
   */
  static async generatePeriodComparisonReport(currentPeriodId: number, previousPeriodId?: number): Promise<ApiResponse<PeriodComparison>> {
    try {
      return await window.electronAPI.invoke('generate-period-comparison-report', currentPeriodId, previousPeriodId)
    } catch (error) {
      console.error('خطأ في إنشاء تقرير مقارنة الفترات:', error)
      return { success: false, message: 'حدث خطأ في إنشاء تقرير مقارنة الفترات' }
    }
  }

  /**
   * تنظيف النسخ الاحتياطية القديمة
   */
  static async cleanupOldBackups(keepDays: number = 30): Promise<ApiResponse> {
    try {
      return await window.electronAPI.invoke('cleanup-old-backups', keepDays)
    } catch (error) {
      console.error('خطأ في تنظيف النسخ الاحتياطية:', error)
      return { success: false, message: 'حدث خطأ في تنظيف النسخ الاحتياطية' }
    }
  }

  // دوال مساعدة
  /**
   * تنسيق التاريخ للعرض
   */
  static formatDate(dateString: string): string {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch (error) {
      return dateString
    }
  }

  /**
   * تنسيق نوع الفترة للعرض
   */
  static formatPeriodType(periodType: string): string {
    const types = {
      monthly: 'شهري',
      quarterly: 'ربع سنوي',
      semi_annual: 'نصف سنوي',
      annual: 'سنوي'
    }
    return types[periodType as keyof typeof types] || periodType
  }

  /**
   * تنسيق حالة الفترة للعرض
   */
  static formatPeriodStatus(status: string): string {
    const statuses = {
      open: 'مفتوحة',
      closed: 'مقفلة',
      locked: 'مؤمنة'
    }
    return statuses[status as keyof typeof statuses] || status
  }

  /**
   * الحصول على لون حالة الفترة
   */
  static getStatusColor(status: string): string {
    const colors = {
      open: 'green',
      closed: 'orange',
      locked: 'red'
    }
    return colors[status as keyof typeof colors] || 'gray'
  }

  // دوال جديدة للواجهات المطورة

  /**
   * الحصول على جميع الفترات المالية (مبسط للواجهة الجديدة)
   */
  static async getAllPeriods(): Promise<FiscalPeriod[]> {
    try {
      const response = await this.getFiscalPeriods()
      if (response.success && response.data) {
        return response.data
      } else {
        console.error('Failed to get fiscal periods:', response.message)
        return []
      }
    } catch (error) {
      console.error('Error getting all periods:', error)
      throw error
    }
  }

  /**
   * إنشاء فترة مالية جديدة (مبسط للواجهة الجديدة)
   */
  static async createPeriod(data: any): Promise<FiscalPeriod> {
    try {
      const createData: CreateFiscalPeriodData = {
        period_name: data.name,
        period_type: 'annual',
        start_date: data.startDate.toISOString().split('T')[0],
        end_date: data.endDate.toISOString().split('T')[0],
        notes: data.description
      }
      const response = await this.createFiscalPeriod(createData)
      return response.data!
    } catch (error) {
      console.error('Error creating period:', error)
      throw error
    }
  }

  /**
   * تحديث فترة مالية (مبسط للواجهة الجديدة)
   */
  static async updatePeriod(id: string, data: any): Promise<FiscalPeriod> {
    try {
      const updateData: UpdateFiscalPeriodData = {
        period_name: data.name,
        start_date: data.startDate.toISOString().split('T')[0],
        end_date: data.endDate.toISOString().split('T')[0],
        notes: data.description
      }
      const response = await this.updateFiscalPeriod(parseInt(id), updateData)
      return response.data!
    } catch (error) {
      console.error('Error updating period:', error)
      throw error
    }
  }

  /**
   * التحقق من صحة البيانات قبل الإقفال
   */
  static async validatePeriodForClosing(periodId: string): Promise<ValidationResult[]> {
    try {
      const response = await window.electronAPI.invoke('fiscal-period:validate-for-closing', {
        periodId: parseInt(periodId)
      })
      return response.data || []
    } catch (error) {
      console.error('Error validating period for closing:', error)
      throw error
    }
  }

  /**
   * الحصول على ملخص التحقق
   */
  static async getValidationSummary(periodId: string): Promise<ValidationSummary> {
    try {
      const response = await window.electronAPI.invoke('fiscal-period:validation-summary', {
        periodId: parseInt(periodId)
      })
      return response.data!
    } catch (error) {
      console.error('Error getting validation summary:', error)
      throw error
    }
  }

  /**
   * إنشاء نسخة احتياطية للإقفال (مبسط للواجهة الجديدة)
   */
  static async createClosingBackupSimple(periodId: string): Promise<void> {
    try {
      await window.electronAPI.invoke('fiscal-period:create-backup', {
        periodId: parseInt(periodId)
      })
    } catch (error) {
      console.error('Error creating closing backup:', error)
      throw error
    }
  }

  /**
   * إقفال الفترة مع تتبع التقدم
   */
  static async closePeriodWithProgress(
    periodId: string,
    progressCallback: (progress: ClosingProgress) => void
  ): Promise<void> {
    try {
      // محاكاة تتبع التقدم
      const steps = [
        'التحقق من صحة البيانات',
        'إنشاء نسخة احتياطية',
        'إقفال حسابات الإيرادات',
        'إقفال حسابات المصروفات',
        'ترحيل صافي الربح/الخسارة',
        'ترحيل الأرصدة',
        'إنهاء عملية الإقفال'
      ]

      for (let i = 0; i < steps.length; i++) {
        progressCallback({
          currentStep: steps[i],
          percentage: ((i + 1) / steps.length) * 100
        })

        // محاكاة وقت المعالجة
        await new Promise(resolve => setTimeout(resolve, 1000))
      }

      await this.closeFiscalPeriod(parseInt(periodId), 1) // استخدام معرف مستخدم افتراضي
    } catch (error) {
      console.error('Error closing period with progress:', error)
      throw error
    }
  }

  /**
   * إقفال الفترة المالية (مبسط)
   */
  static async closePeriod(periodId: string): Promise<void> {
    try {
      await this.closeFiscalPeriod(parseInt(periodId), 1)
    } catch (error) {
      console.error('Error closing period:', error)
      throw error
    }
  }

  /**
   * تنفيذ إقفال الفترة
   */
  static async performPeriodClosing(periodId: number): Promise<any> {
    try {
      return await this.closeFiscalPeriod(periodId, 1)
    } catch (error) {
      console.error('Error performing period closing:', error)
      throw error
    }
  }

  /**
   * ترحيل الأرصدة
   */
  static async carryForwardBalances(periodId: number): Promise<any> {
    try {
      return await window.electronAPI.invoke('carry-forward-balances', periodId)
    } catch (error) {
      console.error('Error carrying forward balances:', error)
      throw error
    }
  }

  /**
   * إنهاء إقفال الفترة
   */
  static async finalizePeriodClosing(periodId: number): Promise<any> {
    try {
      return await window.electronAPI.invoke('finalize-period-closing', periodId)
    } catch (error) {
      console.error('Error finalizing period closing:', error)
      throw error
    }
  }

  /**
   * إعادة فتح الفترة المالية
   */
  static async reopenPeriod(periodId: string): Promise<void> {
    try {
      await window.electronAPI.invoke('fiscal-period:reopen', {
        periodId: parseInt(periodId)
      })
    } catch (error) {
      console.error('Error reopening period:', error)
      throw error
    }
  }

  /**
   * إنتاج تقارير الإقفال
   */
  static async generateClosingReports(periodId: string): Promise<void> {
    try {
      await window.electronAPI.invoke('fiscal-period:generate-reports', {
        periodId: parseInt(periodId)
      })
    } catch (error) {
      console.error('Error generating closing reports:', error)
      throw error
    }
  }

  /**
   * الحصول على تقرير الإقفال (مبسط للواجهة الجديدة)
   */
  static async getClosingReportSimple(periodId: string): Promise<ClosingReport> {
    try {
      const response = await this.getClosingReport(parseInt(periodId))
      return response.data!
    } catch (error) {
      console.error('Error getting closing report:', error)
      throw error
    }
  }

  /**
   * الحصول على مقارنة الفترات
   */
  static async getPeriodComparison(periodId: string): Promise<PeriodComparison> {
    try {
      const response = await this.generatePeriodComparisonReport(parseInt(periodId))
      return response.data!
    } catch (error) {
      console.error('Error getting period comparison:', error)
      throw error
    }
  }

  /**
   * تحميل تقرير
   */
  static async downloadReport(periodId: string, reportType: string): Promise<void> {
    try {
      await window.electronAPI.invoke('fiscal-period:download-report', {
        periodId: parseInt(periodId),
        reportType
      })
    } catch (error) {
      console.error('Error downloading report:', error)
      throw error
    }
  }

  /**
   * إرسال تقرير بالبريد الإلكتروني
   */
  static async emailReport(periodId: string, reportType: string): Promise<void> {
    try {
      await window.electronAPI.invoke('fiscal-period:email-report', {
        periodId: parseInt(periodId),
        reportType
      })
    } catch (error) {
      console.error('Error emailing report:', error)
      throw error
    }
  }

  /**
   * الحصول على إشعارات الإقفال
   */
  static async getClosingNotifications(periodId: string): Promise<ClosingNotification[]> {
    try {
      const response = await window.electronAPI.invoke('fiscal-period:get-notifications', {
        periodId: parseInt(periodId)
      })
      return response.data || []
    } catch (error) {
      console.error('Error getting closing notifications:', error)
      throw error
    }
  }

  /**
   * الحصول على عدد الإشعارات غير المقروءة
   */
  static async getUnreadNotificationCount(): Promise<number> {
    try {
      const response = await window.electronAPI.invoke('fiscal-period:unread-notification-count')
      return response.data || 0
    } catch (error) {
      console.error('Error getting unread notification count:', error)
      throw error
    }
  }

  /**
   * تمييز الإشعار كمقروء
   */
  static async markNotificationAsRead(notificationId: string): Promise<void> {
    try {
      await window.electronAPI.invoke('fiscal-period:mark-notification-read', {
        notificationId
      })
    } catch (error) {
      console.error('Error marking notification as read:', error)
      throw error
    }
  }

  /**
   * إخفاء الإشعار
   */
  static async dismissNotification(notificationId: string): Promise<void> {
    try {
      await window.electronAPI.invoke('fiscal-period:dismiss-notification', {
        notificationId
      })
    } catch (error) {
      console.error('Error dismissing notification:', error)
      throw error
    }
  }

  /**
   * الحصول على إعدادات الإشعارات
   */
  static async getNotificationSettings(): Promise<NotificationSettings> {
    try {
      const response = await window.electronAPI.invoke('fiscal-period:get-notification-settings')
      return response.data!
    } catch (error) {
      console.error('Error getting notification settings:', error)
      throw error
    }
  }

  /**
   * تحديث إعدادات الإشعارات
   */
  static async updateNotificationSettings(settings: NotificationSettings): Promise<void> {
    try {
      await window.electronAPI.invoke('fiscal-period:update-notification-settings', {
        settings
      })
    } catch (error) {
      console.error('Error updating notification settings:', error)
      throw error
    }
  }
}

// إنشاء مثيل مُصدَّر للاستخدام في الواجهات
export const fiscalPeriodApi = FiscalPeriodApi;
