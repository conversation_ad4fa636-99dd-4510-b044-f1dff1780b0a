import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  message,
  Space,
  Popconfirm,
  Tag,
  Row,
  Col,
  Statistic,
  Upload,
  Image
} from 'antd'
import {
  ArrowLeftOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  BgColorsOutlined,
  DatabaseOutlined,
  BarcodeOutlined,
  CameraOutlined,
  EyeOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../../utils/logger'
import styled from 'styled-components'
import { DateUtils } from '../../../utils/dateUtils'

const { Option } = Select

const StyledCard = styled(Card)`
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .ant-card-head {
    background: linear-gradient(135deg, #ff7875 0%, #ff9c6e 100%);
    border-radius: 8px 8px 0 0;
    
    .ant-card-head-title {
      color: white;
      font-weight: 600;
    }
  }
`

const StatsCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .ant-statistic-title {
    color: #666;
    font-size: 14px;
  }
  
  .ant-statistic-content {
    color: #1890ff;
  }
`

interface PaintType {
  id: number
  code: string
  name: string
  description: string
  price_per_sqm: number
  unit: string
  color: string
  finish_type: string
  drying_time: number
  coverage_per_liter: number
  is_active: number
  created_at: string
  updated_at: string
  images?: string[] // مصفوفة روابط الصور
}

interface PaintTypesManagementProps {
  onBack: () => void
}

const PaintTypesManagement: React.FC<PaintTypesManagementProps> = ({ onBack }) => {
  const [paintTypes, setPaintTypes] = useState<PaintType[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingPaintType, setEditingPaintType] = useState<PaintType | null>(null)
  const [form] = Form.useForm()

  // حالات جديدة للصور
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false)
  const [previewImage, setPreviewImage] = useState('')
  const [fileList, setFileList] = useState<any[]>([])
  const [currentPaintTypeImages, setCurrentPaintTypeImages] = useState<string[]>([])

  useEffect(() => {
    loadPaintTypes()
  }, [])

  const loadPaintTypes = async () => {
    setLoading(true)
    try {
      Logger.info('PaintTypesManagement', '🔄 جاري تحميل أنواع الدهانات...')

      if (!window.electronAPI) {
        Logger.error('PaintTypesManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('PaintTypesManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية لأنواع الدهانات
        const mockPaintTypes = [
          {
            id: 1,
            name: 'دهان أكريليك',
            code: 'PAINT001',
            price_per_sqm: 25.0,
            description: 'دهان أكريليك عالي الجودة مقاوم للماء',
            color_options: 'أبيض، أسود، أزرق، أحمر، أخضر',
            coverage_per_liter: 12.0,
            drying_time: 4,
            is_active: true,
            created_at: '2024-06-20'
          },
          {
            id: 2,
            name: 'دهان زيتي',
            code: 'PAINT002',
            price_per_sqm: 35.0,
            description: 'دهان زيتي فاخر للأثاث الخشبي',
            color_options: 'بني، أسود، أبيض كريمي',
            coverage_per_liter: 10.0,
            drying_time: 8,
            is_active: true,
            created_at: '2024-06-18'
          },
          {
            id: 3,
            name: 'دهان لامع',
            code: 'PAINT003',
            price_per_sqm: 30.0,
            description: 'دهان لامع للمعادن والخشب',
            color_options: 'ذهبي، فضي، أسود لامع، أبيض لامع',
            coverage_per_liter: 8.0,
            drying_time: 6,
            is_active: true,
            created_at: '2024-06-15'
          }
        ]

        setPaintTypes(mockPaintTypes as any)
        Logger.info('PaintTypesManagement', '✅ تم تحميل ' + mockPaintTypes.length + ' نوع دهان وهمي')
      } else {
        const response = await window.electronAPI.getPaintTypes()
        if (response.success) {
          setPaintTypes(response.data)
          Logger.info('PaintTypesManagement', '✅ تم تحميل أنواع الدهانات من قاعدة البيانات')
        } else {
          message.error('فشل في تحميل أنواع الدهانات')
        }
      }
    } catch (error) {
      Logger.error('PaintTypesManagement', 'خطأ في تحميل أنواع الدهانات:', error)
      message.error('حدث خطأ في تحميل أنواع الدهانات')
    } finally {
      setLoading(false)
    }
  }



  // دالة إنشاء الكود التلقائي
  const generateCode = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generatePaintTypeCode()
        if (response.success && response.data) {
          form.setFieldsValue({ code: response.data.code })
          message.success('تم إنشاء الكود تلقائياً')
        } else {
          message.error(response.message || 'فشل في إنشاء الكود')
        }
      }
    } catch (error) {
      Logger.error('PaintTypesManagement', 'خطأ في إنشاء الكود:', error)
      message.error('فشل في إنشاء الكود')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      if (window.electronAPI) {
        let response
        if (editingPaintType) {
          response = await window.electronAPI.updatePaintType(editingPaintType.id, values)
        } else {
          response = await window.electronAPI.createPaintType(values)
        }
        
        if (response.success) {
          message.success(editingPaintType ? 'تم تحديث نوع الدهان بنجاح' : 'تم إضافة نوع الدهان بنجاح')
          setModalVisible(false)
          setEditingPaintType(null)
          form.resetFields()
          loadPaintTypes()
        } else {
          message.error('فشل في حفّ نوع الدهان')
        }
      }
    } catch (error) {
      Logger.error('PaintTypesManagement', 'خطأ في حفّ نوع الدهان:', error)
      message.error('حدث خطأ في حفّ نوع الدهان')
    }
  }

  const handleEdit = (paintType: PaintType) => {
    setEditingPaintType(paintType)
    form.setFieldsValue(paintType)
    setModalVisible(true)
  }

  const handleDelete = async (id: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.deletePaintType(id)
        if (response.success) {
          message.success('تم حذف نوع الدهان بنجاح')
          loadPaintTypes()
        } else {
          message.error('فشل في حذف نوع الدهان')
        }
      }
    } catch (error) {
      Logger.error('PaintTypesManagement', 'خطأ في حذف نوع الدهان:', error)
      message.error('حدث خطأ في حذف نوع الدهان')
    }
  }

  // معالجة رفع الصور
  const handleImageUpload = async (info: any) => {
    const { fileList: newFileList } = info
    setFileList(newFileList)

    // معالجة الصور المرفوعة
    // TODO: استخدام SimpleImageService بدلاً من ImageService القديم
    const uploadedImages: string[] = []
    for (const file of newFileList) {
      if (file.url) {
        uploadedImages.push(file.url)
      }
    }

    setCurrentPaintTypeImages(uploadedImages)
  }

  // معاينة الصورة
  const handlePreview = (file: any) => {
    setPreviewImage(file.url || file.thumbUrl)
    setImagePreviewVisible(true)
  }

  // إزالة الصورة
  const handleRemoveImage = (file: any) => {
    const updatedImages = currentPaintTypeImages.filter(
      img => img !== (file.url || file.thumbUrl)
    )
    setCurrentPaintTypeImages(updatedImages)
  }

  const columns = [
    {
      title: 'الكود',
      dataIndex: 'code',
      key: 'code',
      width: 100,
    },
    {
      title: 'اسم الدهان',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: 'اللون',
      dataIndex: 'color',
      key: 'color',
      width: 100,
      render: (color: string) => (
        <Tag color={color === 'شفاف' ? 'default' : 'blue'}>{color}</Tag>
      ),
    },
    {
      title: 'نوع التشطيب',
      dataIndex: 'finish_type',
      key: 'finish_type',
      width: 120,
      render: (finish: string) => (
        <Tag color={finish === 'لامع' ? 'gold' : finish === 'مطفي' ? 'green' : 'blue'}>
          {finish}
        </Tag>
      ),
    },
    {
      title: 'السعر/متر مربع (₪)',
      dataIndex: 'price_per_sqm',
      key: 'price_per_sqm',
      width: 150,
      render: (price: number) => '₪' + price.toFixed(2),
    },
    {
      title: 'وقت الجفاف (ساعة)',
      dataIndex: 'drying_time',
      key: 'drying_time',
      width: 130,
    },
    {
      title: 'التغطية/لتر (م²)',
      dataIndex: 'coverage_per_liter',
      key: 'coverage_per_liter',
      width: 130,
    },
    {
      title: 'الصور',
      key: 'images',
      width: 120,
      render: (_: any, record: PaintType) => {
        // TODO: استخدام SimpleImageService لجلب الصور
        const images: any[] = []
        return (
          <div style={{ display: 'flex', gap: '4px' }}>
            {images.slice(0, 3).map((img, index) => (
              <Image
                key={index}
                width={30}
                height={30}
                src={img.thumbnailUrl || img.url}
                style={{ borderRadius: '4px', objectFit: 'cover' }}
                preview={{
                  mask: <EyeOutlined style={{ fontSize: '12px' }} />
                }}
              />
            ))}
            {images.length > 3 && (
              <div style={{
                width: '30px',
                height: '30px',
                borderRadius: '4px',
                background: '#f0f0f0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '10px'
              }}>
                +{images.length - 3}
              </div>
            )}
            {images.length === 0 && (
              <div style={{
                width: '30px',
                height: '30px',
                borderRadius: '4px',
                background: '#f9f9f9',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#ccc'
              }}>
                <CameraOutlined style={{ fontSize: '12px' }} />
              </div>
            )}
          </div>
        )
      },
    },
    {
      title: 'تاريخ الإنشاء (ميلادي)',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => DateUtils.formatDate(date),
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 120,
      render: (_: any, record: PaintType) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Popconfirm
            title="هل أنت متأكد من حذف هذا النوع؟"
            onConfirm={() => handleDelete(record.id)}
            okText="نعم"
            cancelText="لا"
          >
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const stats = {
    total: paintTypes.length,
    avgPrice: paintTypes.length > 0 ? paintTypes.reduce((sum, p) => sum + p.price_per_sqm, 0) / paintTypes.length : 0,
    maxPrice: paintTypes.length > 0 ? Math.max(...paintTypes.map(p => p.price_per_sqm)) : 0,
    minPrice: paintTypes.length > 0 ? Math.min(...paintTypes.map(p => p.price_per_sqm)) : 0,
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h1 style={{ margin: 0, color: '#ff7875', fontSize: '28px' }}>
            <BgColorsOutlined style={{ marginLeft: '12px' }} />
            إدارة أنواع الدهانات
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '16px' }}>
            إدارة أنواع الدهانات والأسعار لكل متر مربع
          </p>
        </div>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
          size="large"
          style={{ borderRadius: '8px' }}
        >
          العودة
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={12} sm={6}>
          <StatsCard>
            <Statistic
              title="إجمالي الأنواع"
              value={stats.total}
              prefix={<BgColorsOutlined />}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={6}>
          <StatsCard>
            <Statistic
              title="متوسط السعر"
              value={stats.avgPrice}
              precision={2}
              suffix="₪/م²"
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={6}>
          <StatsCard>
            <Statistic
              title="أعلى سعر"
              value={stats.maxPrice}
              precision={2}
              suffix="₪/م²"
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={6}>
          <StatsCard>
            <Statistic
              title="أقل سعر"
              value={stats.minPrice}
              precision={2}
              suffix="₪/م²"
            />
          </StatsCard>
        </Col>
      </Row>

      <StyledCard
        title="قائمة أنواع الدهانات"
        extra={
          <Space>

            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingPaintType(null)
                form.resetFields()
                generateCode() // إنشاء كود تلقائي عند إضافة نوع دهان جديد
                setModalVisible(true)
              }}
              style={{ borderRadius: '6px' }}
            >
              إضافة نوع دهان
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={paintTypes}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => 'إجمالي ' + total + ' نوع دهان',
          }}
          scroll={{ x: 1200 }}
        />
      </StyledCard>

      <Modal
        title={editingPaintType ? 'تعديل نوع الدهان' : 'إضافة نوع دهان جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setEditingPaintType(null)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{ marginTop: '20px' }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="كود الدهان"
                rules={[{ required: true, message: 'يرجى إدخال كود الدهان' }]}
              >
                <Input
                  placeholder="مثال: PAINT001"
                  addonAfter={
                    !editingPaintType && (
                      <Button
                        size="small"
                        onClick={generateCode}
                        icon={<BarcodeOutlined />}
                      >
                        إنشاء تلقائي
                      </Button>
                    )
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="اسم الدهان"
                rules={[{ required: true, message: 'يرجى إدخال اسم الدهان' }]}
              >
                <Input placeholder="مثال: دهان خشب لامع" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="الوصف"
          >
            <Input.TextArea rows={2} placeholder="وصف مختصر للدهان" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="price_per_sqm"
                label="السعر لكل متر مربع (₪)"
                rules={[{ required: true, message: 'يرجى إدخال السعر' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="45.00"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="الوحدة"
                initialValue="متر مربع"
              >
                <Input disabled />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="color"
                label="اللون"
                rules={[{ required: true, message: 'يرجى اختيار اللون' }]}
              >
                <Select placeholder="اختر اللون">
                  <Option value="شفاف">شفاف</Option>
                  <Option value="بني فاتح">بني فاتح</Option>
                  <Option value="بني داكن">بني داكن</Option>
                  <Option value="أسود">أسود</Option>
                  <Option value="أبيض">أبيض</Option>
                  <Option value="أحمر">أحمر</Option>
                  <Option value="أزرق">أزرق</Option>
                  <Option value="أخضر">أخضر</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="finish_type"
                label="نوع التشطيب"
                rules={[{ required: true, message: 'يرجى اختيار نوع التشطيب' }]}
              >
                <Select placeholder="اختر نوع التشطيب">
                  <Option value="لامع">لامع</Option>
                  <Option value="مطفي">مطفي</Option>
                  <Option value="شبه لامع">شبه لامع</Option>
                  <Option value="ساتان">ساتان</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="drying_time"
                label="وقت الجفاف (ساعة)"
                rules={[{ required: true, message: 'يرجى إدخال وقت الجفاف' }]}
              >
                <InputNumber
                  min={1}
                  max={72}
                  style={{ width: '100%' }}
                  placeholder="24"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="coverage_per_liter"
                label="التغطية لكل لتر (متر مربع)"
                rules={[{ required: true, message: 'يرجى إدخال التغطية' }]}
              >
                <InputNumber
                  min={1}
                  precision={1}
                  style={{ width: '100%' }}
                  placeholder="8.0"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="صور الدهان">
            <Upload
              listType="picture-card"
              fileList={fileList}
              onPreview={handlePreview}
              onChange={handleImageUpload}
              onRemove={handleRemoveImage}
              beforeUpload={() => false} // منع الرفع التلقائي
              accept="image/*"
              multiple
            >
              {fileList.length < 5 && (
                <div>
                  <CameraOutlined />
                  <div style={{ marginTop: 8 }}>رفع صورة</div>
                </div>
              )}
            </Upload>
            <div style={{ marginTop: '8px', color: '#666', fontSize: '12px' }}>
              يمكنك رفع حتى 5 صور للدهان (أنواع مدعومة: JPG, PNG, GIF)
            </div>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => {
                setModalVisible(false)
                setEditingPaintType(null)
                form.resetFields()
                setFileList([])
                setCurrentPaintTypeImages([])
              }}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {editingPaintType ? 'تحديث' : 'إضافة'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* مودال معاينة الصور */}
      <Modal
        open={imagePreviewVisible}
        title="معاينة الصورة"
        footer={null}
        onCancel={() => setImagePreviewVisible(false)}
        width={800}
      >
        <img alt="معاينة" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>
  )
}

export default PaintTypesManagement
