import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  DatePicker, Row,
  Col,
  Typography,
  Statistic,
  Tag,
  Avatar,
  Tooltip,
  Tabs,
  Divider,
  message
} from 'antd'

import { SafeLogger as Logger } from '../../utils/logger'
import * as XLSX from 'xlsx'
// import type { TabsProps } from 'antd'
import {
  UserOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  TeamOutlined,
  IdcardOutlined,
  PhoneOutlined,
  MailOutlined,
  BankOutlined,
  SafetyOutlined,
  DollarOutlined,
  UserDeleteOutlined,
  FileTextOutlined,
  BarcodeOutlined,

} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import EmployeeReportsHub from './EmployeeReportsHub'
import UnifiedPrintButton from '../common/UnifiedPrintButton'
// import SimplePrintButton from '../common/SimplePrintButton'
// import { CertificatePrintButton } from '../common'
// import type { CertificateData } from '../common'

// تعريف محلي لـ CertificateData
interface CertificateData {
  type: 'salary' | 'work'
  employeeName: string
  employeeId: string
  position: string
  department: string
  hireDate: string
  salary?: number
  workPeriod?: string
  issueDate: string
  companyName: string
  companyAddress: string
  currency?: string
  startDate?: string
  endDate?: string
  purpose?: string
}
import type {
  Employee,
  Department,
  FingerprintDevice
} from '../../../../shared/types/employee'

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input

// استخدام الأنواع المستوردة من الملف المشترك

const EmployeeManagement: React.FC = () => {
  const [employees, setEmployees] = useState<Employee[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [fingerprintDevices, setFingerprintDevices] = useState<FingerprintDevice[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [terminationModalVisible, setTerminationModalVisible] = useState(false)
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null)
  const [terminatingEmployee, setTerminatingEmployee] = useState<Employee | null>(null)
  const [showReports, setShowReports] = useState(false)
  const [form] = Form.useForm()
  const [terminationForm] = Form.useForm()

  useEffect(() => {
    fetchEmployees()
    fetchDepartments()
    fetchFingerprintDevices()
  }, [])

  const fetchEmployees = async () => {
    setLoading(true)
    try {
      Logger.info('EmployeeManagement', 'جاري جلب بيانات الموظفين...')

      if (!window.electronAPI) {
        Logger.error('EmployeeManagement', '❌ window.electronAPI غير متوفر - لا يمكن جلب بيانات الموظفين')
        message.error('فشل في الاتصال بقاعدة البيانات')
        setEmployees([])
        return
      }

      const result = await window.electronAPI.getEmployees()
      Logger.info('EmployeeManagement', 'نتيجة جلب الموظفين:', result)

      if (result && result.success) {
        setEmployees(result.data || [])
        Logger.info('EmployeeManagement', `✅ تم جلب ${result.data?.length || 0} موظف بنجاح`)
      } else {
        Logger.error('EmployeeManagement', '❌ فشل في جلب الموظفين:', result?.message || 'فشل في جلب الموظفين')
        message.error('فشل في جلب بيانات الموظفين')
        setEmployees([])
      }
    } catch (_error) {
      Logger.error('EmployeeManagement', 'خطأ في جلب بيانات الموظفين:', _error)
      const errorMessage = _error instanceof Error ? _error.message : 'خطأ غير معروف'
      message.error('فشل في جلب بيانات الموظفين: ' + errorMessage)
      setEmployees([])
    } finally {
      setLoading(false)
    }
  }

  const fetchDepartments = async () => {
    try {
      Logger.info('EmployeeManagement', 'جاري جلب أقسام الموظفين...')

      if (!window.electronAPI) {
        Logger.error('EmployeeManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('EmployeeManagement', 'التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للأقسام
        const mockDepartments = [
          { id: 1, name: 'قسم الموارد البشرية', code: 'HR001' },
          { id: 2, name: 'قسم المالية والمحاسبة', code: 'FIN001' },
          { id: 3, name: 'قسم المبيعات', code: 'SALES001' },
          { id: 4, name: 'قسم الإنتاج', code: 'PROD001' },
          { id: 5, name: 'قسم تقنية المعلومات', code: 'IT001' }
        ]

        setDepartments(mockDepartments as any[])
        Logger.info('EmployeeManagement', `✅ تم تحميل ${mockDepartments.length} قسم وهمي للمتصفح`)
        return
      }

      const result = await window.electronAPI.getEmployeeDepartments()
      Logger.info('EmployeeManagement', 'نتيجة جلب الأقسام:', result)

      if (result && result.success) {
        setDepartments(result.data || [])
        Logger.info('EmployeeManagement', `✅ تم جلب ${result.data?.length || 0} قسم بنجاح`)
      } else {
        Logger.error('EmployeeManagement', '❌ فشل في جلب الأقسام:', result?.message)
        setDepartments([])
      }
    } catch (_error) {
      Logger.error('EmployeeManagement', 'خطأ في جلب الأقسام:', _error)
      const _errorMessage = _error instanceof Error ? _error.message : 'خطأ غير معروف'
      Logger.info('EmployeeManagement', `فشل في جلب الأقسام: ${_errorMessage}`)
      setDepartments([])
    }
  }

  // دالة إنشاء الكود التلقائي
  const generateCode = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generateEmployeeCode()
        if (response.success && response.data) {

        setTimeout(() => {
          try {
            if (form && form.setFieldsValue) {
              form.setFieldsValue({ employee_code: response.data.code })
            }
          } catch (_error) {
            Logger.warn('EmployeeManagement', 'خطأ في تحديث النموذج:', _error)
          }
        }, 100)
        message.success('تم إنشاء كود الموظف تلقائياً')
        } else {
          message.error(response.message || 'فشل في إنشاء كود الموظف')
        }
      }
    } catch (_error) {
      Logger.error('EmployeeManagement', 'خطأ في إنشاء كود الموظف:', _error)
      message.error('فشل في إنشاء كود الموظف')
    }
  }

  const fetchFingerprintDevices = async () => {
    try {
      if (!window.electronAPI) {
        Logger.info('EmployeeManagement', 'استخدام بيانات وهمية لأجهزة البصمة')

        // بيانات وهمية لأجهزة البصمة
        const mockDevices = [
          {
            id: 1,
            name: 'جهاز البصمة الرئيسي',
            model: 'ZKTeco F18',
            location: 'المدخل الرئيسي',
            ip_address: '*************',
            status: 'active',
            last_sync: '2024-06-24 18:00:00'
          },
          {
            id: 2,
            name: 'جهاز البصمة الفرعي',
            model: 'ZKTeco K40',
            location: 'مدخل المصنع',
            ip_address: '*************',
            status: 'active',
            last_sync: '2024-06-24 17:55:00'
          }
        ]

        setFingerprintDevices(mockDevices as any[])
        Logger.info('EmployeeManagement', '✅ تم تحميل ' + mockDevices.length + ' جهاز بصمة وهمي للمتصفح')
        return
      }

      const result = await window.electronAPI.getFingerprintDevices()
      if (result.success) {
        setFingerprintDevices(result.data)
      }
    } catch (_error) {
      Logger.error('EmployeeManagement', 'فشل في جلب أجهزة البصمة:', _error)
      const errorMessage = _error instanceof Error ? _error.message : 'خطأ غير معروف'
      Logger.info('EmployeeManagement', `فشل في جلب أجهزة البصمة: ${errorMessage}`)
    }
  }

  const handleCreateEmployee = () => {
    setEditingEmployee(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEditEmployee = (employee: Employee) => {
    setEditingEmployee(employee)

    setTimeout(() => {
      try {
        if (form && form.setFieldsValue) {
          form.setFieldsValue({
            ...employee,
            // تحويل name إلى full_name للتوافق مع النموذج
            full_name: employee.full_name || (employee as any).name,
            birth_date: employee.birth_date ? dayjs(employee.birth_date) : null,
            hire_date: dayjs(employee.hire_date)
          })
        }
      } catch (_error) {
        Logger.warn('EmployeeManagement', 'خطأ في تحديث النموذج:', _error)
      }
    }, 100)

    setModalVisible(true)
  }

  const handleDeleteEmployee = async (employee: Employee) => {
    if (!window.electronAPI) {
      Logger.error('EmployeeManagement', '❌ window.electronAPI غير متوفر')
      message.error('التطبيق يعمل في وضع المتصفح - لا يمكن حذف الموظف')
      return
    }

    Modal.confirm({
      title: 'تأكيد الحذف',
      content: `هل أنت متأكد من حذف الموظف "${employee.full_name}"؟`,
      okText: 'حذف',
      cancelText: 'إلغاء',
      okType: 'danger',
      onOk: async () => {
        try {
          const result = await window.electronAPI.deleteEmployee(employee.id)
          if (result.success) {
            message.success('تم حذف الموظف بنجاح')
            fetchEmployees()
          } else {
            message.error(result.message)
          }
        } catch (_error) {
          Logger.error('EmployeeManagement', 'خطأ في حذف الموظف:', _error)
          const errorMessage = _error instanceof Error ? _error.message : 'خطأ غير معروف'
          message.error('فشل في حذف الموظف: ' + errorMessage)
        }
      }
    })
  }

  const handleTerminateEmployee = (employee: Employee) => {
    setTerminatingEmployee(employee)
    terminationForm.resetFields()
    setTerminationModalVisible(true)
  }

  const handleSubmit = async (values: any) => {
    try {
      if (!window.electronAPI) {
        Logger.error('EmployeeManagement', '❌ window.electronAPI غير متوفر')
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن حفظ بيانات الموظف')
        return
      }

      // التحقق من صحة البيانات الأساسية
      if (!values.full_name && !values.first_name && !values.last_name) {
        message.error('يجب إدخال اسم الموظف')
        return
      }

      // التحقق من صحة الراتب
      if (values.basic_salary && values.basic_salary < 0) {
        message.error('الراتب الأساسي يجب أن يكون أكبر من أو يساوي الصفر')
        return
      }

      if (values.basic_salary && values.basic_salary > 100000) {
        message.error('الراتب الأساسي كبير جداً، يرجى التحقق من القيمة المدخلة')
        return
      }

      // تحضير بيانات الموظف
      const employeeData = {
        ...values,
        department_id: values.department_id ? parseInt(String(values.department_id)) : null, // تحويل معرف القسم إلى رقم صحيح
        birth_date: values.birth_date ? values.birth_date.format('YYYY-MM-DD') : null,
        hire_date: values.hire_date ? values.hire_date.format('YYYY-MM-DD') : null,
        basic_salary: values.basic_salary || 0,
        is_active: values.status === 'active'
      }

      let result
      if (editingEmployee) {
        result = await window.electronAPI.updateEmployee(editingEmployee.id, employeeData)
      } else {
        // إنشاء كود موظف تلقائي إذا لم يتم إدخاله
        if (!employeeData.employee_code) {
          const codeResult = await window.electronAPI.generateEmployeeCode()
          if (codeResult.success) {
            employeeData.employee_code = codeResult.data.code
          }
        }
        result = await window.electronAPI.createEmployee(employeeData)
      }

      if (result.success) {
        message.success(editingEmployee ? 'تم تحديث الموظف بنجاح' : 'تم إضافة الموظف بنجاح')
        setModalVisible(false)
        form.resetFields()
        fetchEmployees()
      } else {
        message.error(result.message)
      }
    } catch (_error) {
      Logger.error('EmployeeManagement', 'خطأ في حفظ بيانات الموظف:', _error)
      const errorMessage = _error instanceof Error ? _error.message : 'خطأ غير معروف'
      message.error('فشل في حفظ بيانات الموظف: ' + errorMessage)
    }
  }

  const handleTerminationSubmit = async (values: any) => {
    if (!terminatingEmployee) return

    try {
      if (!window.electronAPI) {
        Logger.error('EmployeeManagement', '❌ window.electronAPI غير متوفر')
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن إنهاء خدمة الموظف')
        return
      }

      const terminationData = {
        ...values,
        termination_date: values.termination_date.format('YYYY-MM-DD')
      }

      const result = await window.electronAPI.terminateEmployee(terminatingEmployee.id, terminationData)
      if (result.success) {
        message.success('تم إنهاء خدمة الموظف بنجاح')
        setTerminationModalVisible(false)
        terminationForm.resetFields()
        fetchEmployees()
      } else {
        message.error(result.message)
      }
    } catch (_error) {
      Logger.error('EmployeeManagement', 'خطأ في إنهاء خدمة الموظف:', _error)
      const errorMessage = _error instanceof Error ? _error.message : 'خطأ غير معروف'
      message.error('فشل في إنهاء خدمة الموظف: ' + errorMessage)
    }
  }





  const _getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'inactive': return 'orange'
      case 'terminated': return 'red'
      case 'suspended': return 'volcano'
      default: return 'default'
    }
  }

  const _getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط'
      case 'inactive': return 'غير نشط'
      case 'terminated': return 'منتهي الخدمة'
      case 'suspended': return 'موقوف'
      default: return status
    }
  }

  const _getEmploymentTypeText = (type: string) => {
    switch (type) {
      case 'full_time': return 'دوام كامل'
      case 'part_time': return 'دوام جزئي'
      case 'contract': return 'عقد'
      case 'temporary': return 'مؤقت'
      default: return type
    }
  }

  // ✅ دالة إنشاء شهادة راتب للموظف
  const _createSalaryCertificate = (employee: Employee): CertificateData => {
    return {
      type: 'salary',
      employeeName: employee.full_name || employee.name || '',
      employeeId: String(employee.id),
      position: employee.position || employee.job_title || '',
      department: employee.department_name || '',
      hireDate: employee.hire_date || '',
      salary: employee.salary || 0,
      currency: 'ريال',
      startDate: employee.hire_date || '',
      issueDate: dayjs().format('YYYY-MM-DD'),
      purpose: 'للاستعمال لدى الجهات المختصة',
      companyName: 'ZET.IA',
      companyAddress: 'فلسطين - غزة'
    }
  }

  // ✅ دالة إنشاء شهادة عمل للموظف
  const _createWorkCertificate = (employee: Employee): CertificateData => {
    return {
      type: 'work',
      employeeName: employee.full_name || employee.name || '',
      employeeId: String(employee.id),
      position: employee.position || employee.job_title || '',
      department: employee.department_name || '',
      hireDate: employee.hire_date || '',
      startDate: employee.hire_date || '',
      endDate: employee.termination_date || undefined,
      issueDate: dayjs().format('YYYY-MM-DD'),
      purpose: 'للاستعمال لدى الجهات المختصة',
      companyName: 'ZET.IA',
      companyAddress: 'فلسطين - غزة'
    }
  }

  // دالة مساعدة لحساب فترة الخدمة
  const _calculateServicePeriod = (hireDate?: string, endDate?: string): string => {
    if (!hireDate) return 'غير محدد'

    const start = dayjs(hireDate)
    const end = endDate ? dayjs(endDate) : dayjs()
    const years = end.diff(start, 'year')
    const months = end.diff(start.add(years, 'year'), 'month')

    return `${years} سنة و ${months} شهر`
  }

  // دالة مساعدة لتحويل الأرقام إلى كلمات (مبسطة)
  const _convertNumberToWords = (amount: number): string => {
    // هذه دالة مبسطة - يمكن تحسينها لاحقاً
    const thousands = Math.floor(amount / 1000)
    const remainder = amount % 1000

    if (thousands > 0) {
      return `${thousands} ألف و ${remainder} ريال سعودي`
    }
    return `${amount} ريال سعودي`
  }

  const columns: ColumnsType<Employee> = [
    {
      title: 'كود الموظف',
      dataIndex: 'employee_code',
      key: 'employee_code',
      width: 120,
      fixed: 'left'
    },
    {
      title: 'الاسم الكامل',
      dataIndex: 'full_name',
      key: 'full_name',
      width: 200,
      fixed: 'left',
      render: (text, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <div>
            <div style={{ fontWeight: 'bold' }}>{text || record.name}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.position || record.job_title}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: 'القسم',
      dataIndex: 'department_name',
      key: 'department_name',
      width: 150,
      render: (text) => text || '-'
    },
    {
      title: 'الهاتف',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
      render: (text) => text ? (
        <Space>
          <PhoneOutlined />
          {text}
        </Space>
      ) : '-'
    },
    {
      title: 'البريد الإلكتروني',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      render: (text) => text ? (
        <Space>
          <MailOutlined />
          {text}
        </Space>
      ) : '-'
    },
    {
      title: 'الراتب الأساسي',
      dataIndex: 'basic_salary',
      key: 'basic_salary',
      width: 150,
      render: (salary) => salary ? (
        <Space>
          <DollarOutlined />
          {salary.toLocaleString()} ₪
        </Space>
      ) : '-'
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : status === 'inactive' ? 'orange' : 'red'}>
          {status === 'active' ? 'نشط' : status === 'inactive' ? 'غير نشط' : 'منتهي الخدمة'}
        </Tag>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Tooltip title="تعديل">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditEmployee(record)}
            />
          </Tooltip>
          <Tooltip title="حذف">
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteEmployee(record)}
            />
          </Tooltip>
          <Tooltip title="إنهاء الخدمة">
            <Button
              type="default"
              size="small"
              icon={<UserDeleteOutlined />}
              onClick={() => handleTerminateEmployee(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  // عرض التقارير إذا تم اختيارها
  if (showReports) {
    return <EmployeeReportsHub onBack={() => setShowReports(false)} />
  }

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <TeamOutlined /> إدارة الموظفين
      </Title>

      {/* إحصائيات سريعة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="إجمالي الموظفين"
              value={employees.length}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="الموظفين النشطين"
              value={employees.filter(e => e.status === 'active').length}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="الأقسام"
              value={departments.length}
              prefix={<BankOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="أجهزة البصمة"
              value={fingerprintDevices.filter(d => d.status === 'active').length}
              prefix={<SafetyOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* جدول الموظفين */}
      <Card
        title={
          <Space>
            <TeamOutlined />
            <span>قائمة الموظفين</span>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<FileTextOutlined />}
              onClick={() => setShowReports(true)}
            >
              التقارير
            </Button>
            <UnifiedPrintButton
              type="report"
              subType="work"
              data={{
                title: 'تقرير الموظفين',
                subtitle: `إجمالي ${employees.length} موظف`,
                headers: ['كود الموظف', 'الاسم', 'القسم', 'المسمى الوظيفي', 'الراتب الأساسي', 'الهاتف', 'البريد الإلكتروني'],
                rows: employees.map(emp => [
                  emp.employee_code,
                  emp.full_name || emp.name,
                  emp.department_name || 'غير محدد',
                  emp.job_title || emp.position || 'غير محدد',
                  emp.basic_salary ? `${emp.basic_salary.toLocaleString()} ₪` : 'غير محدد',
                  emp.phone || 'غير محدد',
                  emp.email || 'غير محدد'
                ]),
                summary: {
                  totalEmployees: employees.length,
                  totalSalaries: employees.reduce((sum, emp) => sum + (emp.basic_salary || 0), 0),
                  avgSalary: employees.length > 0 ? employees.reduce((sum, emp) => sum + (emp.basic_salary || 0), 0) / employees.length : 0
                }
              }}
              title="تقرير الموظفين"
              buttonText="طباعة"
              size="middle"
              showDropdown={true}
              showExportOptions={true}
              _documentId="employees-list"
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateEmployee}
            >
              إضافة موظف
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={employees}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} موظف`
          }}
        />
      </Card>

      {/* نافذة إضافة/تعديل الموظف */}
      <Modal
        title={editingEmployee ? 'تعديل الموظف' : 'إضافة موظف جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={1000}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            employment_type: 'full_time',
            salary_type: 'monthly',
            status: 'active',
            access_level: 1,
            working_hours_start: '08:00',
            working_hours_end: '17:00'
          }}
        >
          <Tabs
            defaultActiveKey="basic"
            items={[
              {
                key: 'basic',
                label: 'المعلومات الأساسية',
                children: (
                  <div>
                    <Row gutter={16}>
                      <Col span={8}>
                        <Form.Item
                          name="employee_code"
                          label="كود الموظف"
                          rules={[{ required: true, message: 'يرجى إدخال كود الموظف' }]}
                        >
                          <Input
                            prefix={<IdcardOutlined />}
                            placeholder="سيتم إنشاؤه تلقائياً"
                            addonAfter={
                              <Button
                                size="small"
                                onClick={generateCode}
                                icon={<BarcodeOutlined />}
                              >
                                إنشاء تلقائي
                              </Button>
                            }
                          />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          name="national_id"
                          label="رقم الهوية"
                        >
                          <Input placeholder="رقم الهوية الوطنية" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          name="full_name"
                          label="الاسم الكامل"
                          rules={[{ required: true, message: 'يرجى إدخال الاسم الكامل' }]}
                        >
                          <Input prefix={<UserOutlined />} placeholder="الاسم الكامل" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={8}>
                        <Form.Item
                          name="email"
                          label="البريد الإلكتروني"
                          rules={[
                            { type: 'email', message: 'يرجى إدخال بريد إلكتروني صحيح' }
                          ]}
                        >
                          <Input prefix={<MailOutlined />} placeholder="<EMAIL>" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          name="phone"
                          label="رقم الهاتف"
                        >
                          <Input prefix={<PhoneOutlined />} placeholder="05xxxxxxxx" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          name="birth_date"
                          label="تاريخ الميلاد"
                        >
                          <DatePicker style={{ width: '100%' }} placeholder="اختر تاريخ الميلاد" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="department_id"
                          label="القسم"
                          rules={[{ required: true, message: 'يرجى اختيار القسم' }]}
                        >
                          <Select placeholder="اختر القسم">
                            {departments.map(dept => (
                              <Option key={dept.id} value={dept.id}>
                                {dept.name}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="job_title"
                          label="المسمى الوظيفي"
                        >
                          <Input placeholder="مثال: مطور برمجيات" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={8}>
                        <Form.Item
                          name="hire_date"
                          label="تاريخ التوظيف"
                          rules={[{ required: true, message: 'يرجى إدخال تاريخ التوظيف' }]}
                        >
                          <DatePicker style={{ width: '100%' }} placeholder="اختر تاريخ التوظيف" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          name="employment_type"
                          label="نوع التوظيف"
                        >
                          <Select>
                            <Option value="full_time">دوام كامل</Option>
                            <Option value="part_time">دوام جزئي</Option>
                            <Option value="contract">عقد</Option>
                            <Option value="temporary">مؤقت</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          name="status"
                          label="الحالة"
                        >
                          <Select>
                            <Option value="active">نشط</Option>
                            <Option value="inactive">غير نشط</Option>
                            <Option value="terminated">منتهي الخدمة</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>
                  </div>
                )
              }
            ]}
          />

          <Divider />

          <Row justify="end">
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {editingEmployee ? 'تحديث' : 'إضافة'}
              </Button>
            </Space>
          </Row>
        </Form>
      </Modal>

      {/* نافذة إنهاء الخدمة */}
      <Modal
        title="إنهاء خدمة الموظف"
        open={terminationModalVisible}
        onCancel={() => setTerminationModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form
          form={terminationForm}
          layout="vertical"
          onFinish={handleTerminationSubmit}
        >
          <Form.Item
            name="termination_date"
            label="تاريخ إنهاء الخدمة"
            rules={[{ required: true, message: 'يرجى إدخال تاريخ إنهاء الخدمة' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="termination_reason"
            label="سبب إنهاء الخدمة"
            rules={[{ required: true, message: 'يرجى إدخال سبب إنهاء الخدمة' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Row justify="end">
            <Space>
              <Button onClick={() => setTerminationModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" danger htmlType="submit">
                إنهاء الخدمة
              </Button>
            </Space>
          </Row>
        </Form>
      </Modal>
    </div>
  )
}

export default EmployeeManagement
