import React, { useState, useEffect } from 'react'
import { Table, Button, Modal, Form, Input, Space, message, Select, DatePicker, Card, Statistic, Row, Col, Tag, InputNumber } from 'antd'
import { PlusOutlined, DollarOutlined, BankOutlined, CreditCardOutlined, WalletOutlined } from '@ant-design/icons'
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig'
import { getCurrencySymbol, formatCurrency } from '../../utils/settings'
import dayjs from 'dayjs'
import { SafeLogger as Logger } from '../../utils/logger'

interface SupplierPayment {
  id: number
  supplier_id: number
  supplier_name: string
  invoice_id?: number
  invoice_number?: string
  payment_number: string
  amount: number
  payment_date: string
  payment_method: string
  reference_number?: string
  notes?: string
  created_by: number
  created_by_name: string
  created_at: string
}

interface Supplier {
  id: number
  code: string
  name: string
  is_active: boolean
}

interface PurchaseInvoice {
  id: number
  invoice_number: string
  supplier_id: number
  total_amount: number
  paid_amount: number
  status: string
}

interface SupplierPaymentManagementProps {
  onBack: () => void
}

const SupplierPaymentManagement: React.FC<SupplierPaymentManagementProps> = ({ onBack }) => {
  const [payments, setPayments] = useState<SupplierPayment[]>([])
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [invoices, setInvoices] = useState<PurchaseInvoice[]>([])
  const [filteredInvoices, setFilteredInvoices] = useState<PurchaseInvoice[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    loadPayments()
    loadSuppliers()
    loadInvoices()
  }, [])

  const loadPayments = async () => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getSupplierPayments()
        if (response.success && Array.isArray(response.data)) {
          setPayments(response.data)
        } else {
          Logger.error('SupplierPaymentManagement', 'خطأ في استجابة مدفوعات الموردين:', response)
          setPayments([])
        }
      }
    } catch (error) {
      Logger.error('SupplierPaymentManagement', 'خطأ في تحميل مدفوعات الموردين:', error)
      message.error('فشل في تحميل مدفوعات الموردين')
      setPayments([])
    } finally {
      setLoading(false)
    }
  }

  const loadSuppliers = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getSuppliers()
        if (response.success) {
          setSuppliers(response.data.filter((s: Supplier) => s.is_active))
        } else {
          message.error('فشل في تحميل الموردين')
        }
      }
    } catch (error) {
      message.error('فشل في تحميل الموردين')
    }
  }

  const loadInvoices = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getPurchaseInvoices()
        if (response.success && Array.isArray(response.data)) {
          setInvoices(response.data.filter((i: PurchaseInvoice) => i.status !== 'paid' && i.status !== 'cancelled'))
        } else {
          Logger.error('SupplierPaymentManagement', 'خطأ في استجابة فواتير الشراء:', response)
          setInvoices([])
        }
      }
    } catch (error) {
      Logger.error('SupplierPaymentManagement', 'خطأ في تحميل الفواتير:', error)
      message.error('فشل في تحميل الفواتير')
      setInvoices([])
    }
  }

  const generatePaymentNumber = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generatePaymentNumber()
        if (response.success && response.data) {
          form.setFieldsValue({ payment_number: response.data.paymentNumber })
          message.success('تم إنشاء رقم الدفعة تلقائياً')
        }
      }
    } catch (error) {
      message.error('فشل في إنشاء رقم الدفعة')
    }
  }

  const handleSupplierChange = (supplierId: number) => {
    const supplierInvoices = invoices.filter(invoice => invoice.supplier_id === supplierId)
    setFilteredInvoices(supplierInvoices)
    form.setFieldsValue({ invoice_id: undefined })
  }

  const handleInvoiceChange = (invoiceId: number) => {
    const selectedInvoice = filteredInvoices.find(invoice => invoice.id === invoiceId)
    if (selectedInvoice) {
      const remainingAmount = selectedInvoice.total_amount - selectedInvoice.paid_amount
      form.setFieldsValue({ amount: remainingAmount })
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      if (window.electronAPI) {
        const paymentData = {
          ...values,
          payment_date: values.payment_date ? values.payment_date.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          created_by: 1 // TODO: استخدام معرف المستخدم الحالي
        }

        const response = await window.electronAPI.createSupplierPayment(paymentData)

        if (response.success) {
          message.success('تم إنشاء الدفعة بنجاح')
          setModalVisible(false)
          form.resetFields()
          setFilteredInvoices([])
          loadPayments()
          loadInvoices() // إعادة تحميل الفواتير لتحديث الحالات
        } else {
          message.error(response.message || 'فشل في حفّ الدفعة')
        }
      }
    } catch (error) {
      message.error('حدث خطأ أثناء حفّ الدفعة')
    }
  }

  const handleAdd = () => {
    form.resetFields()
    form.setFieldsValue({
      payment_date: dayjs(),
      payment_method: 'cash'
    })
    setFilteredInvoices([])
    generatePaymentNumber()
    setModalVisible(true)
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash': return <WalletOutlined style={{ color: '#52c41a' }} />
      case 'check': return <BankOutlined style={{ color: '#1890ff' }} />
      case 'transfer': return <BankOutlined style={{ color: '#722ed1' }} />
      case 'credit': return <CreditCardOutlined style={{ color: '#fa8c16' }} />
      default: return <DollarOutlined />
    }
  }

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return 'نقدي'
      case 'check': return 'شيك'
      case 'transfer': return 'تحويل بنكي'
      case 'credit': return 'ائتمان'
      default: return method
    }
  }

  const getPaymentStats = () => {
    const total = payments.length
    const totalAmount = payments.reduce((sum, p) => sum + p.amount, 0)
    const cashPayments = payments.filter(p => p.payment_method === 'cash').length
    const checkPayments = payments.filter(p => p.payment_method === 'check').length
    const transferPayments = payments.filter(p => p.payment_method === 'transfer').length

    return { total, totalAmount, cashPayments, checkPayments, transferPayments }
  }

  const stats = getPaymentStats()

  const columns = [
    {
      title: 'رقم الدفعة',
      dataIndex: 'payment_number',
      key: 'payment_number',
      width: 120,
      render: (paymentNumber: string) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{paymentNumber}</span>
      )
    },
    {
      title: 'المورد',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      render: (name: string) => (
        <Space>
          <DollarOutlined style={{ color: '#1890ff' }} />
          {name}
        </Space>
      )
    },
    {
      title: 'رقم الفاتورة',
      dataIndex: 'invoice_number',
      key: 'invoice_number',
      width: 120,
      render: (invoiceNumber: string) => invoiceNumber || '-'
    },
    {
      title: `المبلغ (${getCurrencySymbol()})`,
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount: number) => (
        <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
          {amount.toLocaleString()}
        </span>
      )
    },
    {
      title: 'تاريخ الدفع (ميلادي)',
      dataIndex: 'payment_date',
      key: 'payment_date',
      width: 140,
      render: (date: string) => DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE)
    },
    {
      title: 'طريقة الدفع',
      dataIndex: 'payment_method',
      key: 'payment_method',
      width: 120,
      render: (method: string) => (
        <Space>
          {getPaymentMethodIcon(method)}
          <Tag color={method === 'cash' ? 'green' : method === 'check' ? 'blue' : method === 'transfer' ? 'purple' : 'orange'}>
            {getPaymentMethodText(method)}
          </Tag>
        </Space>
      )
    },
    {
      title: 'رقم المرجع',
      dataIndex: 'reference_number',
      key: 'reference_number',
      width: 120,
      render: (ref: string) => ref || '-'
    },
    {
      title: 'ملاحّات',
      dataIndex: 'notes',
      key: 'notes',
      render: (notes: string) => notes || '-'
    },
    {
      title: 'المنشئ',
      dataIndex: 'created_by_name',
      key: 'created_by_name',
      width: 120
    },
    {
      title: 'تاريخ الإنشاء (ميلادي)',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 140,
      render: (date: string) => DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE)
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>إدارة مدفوعات الموردين</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>تسجيل ومتابعة مدفوعات الموردين والفواتير</p>
        </div>
        <Space>
          <Button onClick={onBack}>رجوع</Button>

          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            دفعة جديدة
          </Button>
        </Space>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={5}>
          <Card>
            <Statistic
              title="إجمالي الدفعات"
              value={stats.total}
              valueStyle={{ color: '#1890ff' }}
              prefix={<DollarOutlined />}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="إجمالي المبالغ"
              value={stats.totalAmount}
              valueStyle={{ color: '#52c41a' }}
              prefix={getCurrencySymbol()}
              precision={0}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="دفعات نقدية"
              value={stats.cashPayments}
              valueStyle={{ color: '#52c41a' }}
              prefix={<WalletOutlined />}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="دفعات بشيكات"
              value={stats.checkPayments}
              valueStyle={{ color: '#1890ff' }}
              prefix={<BankOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="تحويلات بنكية"
              value={stats.transferPayments}
              valueStyle={{ color: '#722ed1' }}
              prefix={<BankOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Table
        columns={columns}
        dataSource={payments}
        rowKey="id"
        loading={loading}
        pagination={{
          total: payments.length,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} من ${total} دفعة`
        }}
        scroll={{ x: 1400 }}
      />

      <Modal
        title="دفعة جديدة للمورد"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
          setFilteredInvoices([])
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            label="رقم الدفعة"
            name="payment_number"
            rules={[{ required: true, message: 'يرجى إدخال رقم الدفعة' }]}
          >
            <Input 
              placeholder="رقم الدفعة" 
              suffix={
                <Button type="link" size="small" onClick={generatePaymentNumber}>
                  إنشاء تلقائي
                </Button>
              }
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="المورد"
                name="supplier_id"
                rules={[{ required: true, message: 'يرجى اختيار المورد' }]}
              >
                <Select 
                  placeholder="اختر المورد"
                  onChange={handleSupplierChange}
                >
                  {suppliers.map(supplier => (
                    <Select.Option key={supplier.id} value={supplier.id}>
                      {supplier.name} ({supplier.code})
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="الفاتورة (اختياري)"
                name="invoice_id"
              >
                <Select 
                  placeholder="اختر الفاتورة"
                  onChange={handleInvoiceChange}
                  allowClear
                >
                  {filteredInvoices.map(invoice => (
                    <Select.Option key={invoice.id} value={invoice.id}>
                      {invoice.invoice_number} - متبقي: {formatCurrency(invoice.total_amount - invoice.paid_amount)}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={`المبلغ (${getCurrencySymbol()})`}
                name="amount"
                rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
              >
                <InputNumber
                  placeholder="المبلغ"
                  min={0}
                  style={{ width: '100%' }}
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="تاريخ الدفع"
                name="payment_date"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الدفع' }]}
              >
                <DatePicker 
                  style={{ width: '100%' }} 
                  placeholder="اختر التاريخ"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="طريقة الدفع"
                name="payment_method"
                rules={[{ required: true, message: 'يرجى اختيار طريقة الدفع' }]}
              >
                <Select placeholder="اختر طريقة الدفع">
                  <Select.Option value="cash">
                    <Space>
                      <WalletOutlined style={{ color: '#52c41a' }} />
                      نقدي
                    </Space>
                  </Select.Option>
                  <Select.Option value="check">
                    <Space>
                      <BankOutlined style={{ color: '#1890ff' }} />
                      شيك
                    </Space>
                  </Select.Option>
                  <Select.Option value="transfer">
                    <Space>
                      <BankOutlined style={{ color: '#722ed1' }} />
                      تحويل بنكي
                    </Space>
                  </Select.Option>
                  <Select.Option value="credit">
                    <Space>
                      <CreditCardOutlined style={{ color: '#fa8c16' }} />
                      ائتمان
                    </Space>
                  </Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="رقم المرجع"
                name="reference_number"
              >
                <Input placeholder="رقم الشيك أو التحويل" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="ملاحّات" name="notes">
            <Input.TextArea placeholder="ملاحّات إضافية" rows={3} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                حفّ الدفعة
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default SupplierPaymentManagement
