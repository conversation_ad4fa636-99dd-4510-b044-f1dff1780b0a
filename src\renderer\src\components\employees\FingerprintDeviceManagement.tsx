﻿import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Row,
  Col,
  Typography,
  Statistic,
  Tag,
  Tooltip,
  message,
  Progress,
  Badge,
  Divider
} from 'antd'
import {
  SafetyOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SyncOutlined,
  WifiOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  ToolOutlined,
  BookOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import FingerprintSetupGuide from './FingerprintSetupGuide'

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input

interface FingerprintDevice {
  id: number
  device_name: string
  device_model?: string
  ip_address?: string
  port: number
  device_id: number
  location?: string
  status: string
  last_sync?: string
  firmware_version?: string
  capacity: number
  current_users: number
  notes?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

const FingerprintDeviceManagement: React.FC = () => {
  const [devices, setDevices] = useState<FingerprintDevice[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingDevice, setEditingDevice] = useState<FingerprintDevice | null>(null)
  const [syncingDeviceId, setSyncingDeviceId] = useState<number | null>(null)
  const [testingDeviceId, setTestingDeviceId] = useState<number | null>(null)
  const [setupGuideVisible, setSetupGuideVisible] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    fetchDevices()
  }, [])

  const fetchDevices = async () => {
    setLoading(true)
    try {
      const result = await window.electronAPI.getFingerprintDevices()
      if (result.success) {
        setDevices(result.data)
      } else {
        message.error(result.message)
      }
    } catch (_error) {
      message.error('فشل في جلب بيانات أجهزة البصمة')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateDevice = () => {
    setEditingDevice(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEditDevice = (device: FingerprintDevice) => {
    setEditingDevice(device)
    form.setFieldsValue(device)
    setModalVisible(true)
  }

  const handleDeleteDevice = async (device: FingerprintDevice) => {
    Modal.confirm({
      title: 'تأكيد الحذف',
      content: `هل أنت متأكد من حذف الجهاز "${device.device_name}"؟`,
      okText: 'حذف',
      cancelText: 'إلغاء',
      okType: 'danger',
      onOk: async () => {
        try {
          const result = await window.electronAPI.deleteFingerprintDevice(device.id)
          if (result.success) {
            message.success('تم حذف الجهاز بنجاح')
            fetchDevices()
          } else {
            message.error(result.message)
          }
        } catch (_error) {
          message.error('فشل في حذف الجهاز')
        }
      }
    })
  }

  const handleSyncDevice = async (deviceId: number) => {
    setSyncingDeviceId(deviceId)
    try {
      const result = await window.electronAPI.syncFingerprintDevice(deviceId)
      if (result.success) {
        message.success('تم تحديث المزامنة بنجاح')
        fetchDevices()
      } else {
        message.error(result.message)
      }
    } catch (_error) {
      message.error('فشل في مزامنة الجهاز')
    } finally {
      setSyncingDeviceId(null)
    }
  }

  const handleTestConnection = async (device: FingerprintDevice) => {
    setTestingDeviceId(device.id)
    try {
      // محاكاة اختبار الاتصال
      message.loading('جاري اختبار الاتصال...', 2)

      // في التطبيق الحقيقي، سيتم إرسال ping أو طلب اتصال للجهاز
      await new Promise(resolve => setTimeout(resolve, 2000))

      // محاكاة نتيجة الاختبار
      const isConnected = Math.random() > 0.3 // 70% نجاح

      if (isConnected) {
        message.success(`تم الاتصال بنجاح مع ${device.device_name}`)
        Modal.info({
          title: 'نتيجة اختبار الاتصال',
          content: (
            <div>
              <p><strong>الجهاز:</strong> {device.device_name}</p>
              <p><strong>العنوان:</strong> {device.ip_address}:{device.port}</p>
              <p><strong>الحالة:</strong> متصل ويعمل بشكل طبيعي</p>
              <p><strong>وقت الاستجابة:</strong> 45ms</p>
              <p><strong>إصدار البرنامج:</strong> {device.firmware_version || 'غير محدد'}</p>
            </div>
          )
        })
      } else {
        message.error(`فشل في الاتصال مع ${device.device_name}`)
        Modal.error({
          title: 'فشل في اختبار الاتصال',
          content: (
            <div>
              <p><strong>الجهاز:</strong> {device.device_name}</p>
              <p><strong>العنوان:</strong> {device.ip_address}:{device.port}</p>
              <p><strong>المشكلة:</strong> لا يمكن الوصول للجهاز</p>
              <p><strong>الحلول المقترحة:</strong></p>
              <ul>
                <li>تحقق من اتصال الجهاز بالشبكة</li>
                <li>تأكد من صحة عنوان IP</li>
                <li>تحقق من إعدادات الجدار الناري</li>
                <li>تأكد من تشغيل الجهاز</li>
              </ul>
            </div>
          )
        })
      }
    } catch (_error) {
      message.error('حدث خطأ أثناء اختبار الاتصال')
    } finally {
      setTestingDeviceId(null)
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      // التحقق من صحة البيانات الأساسية
      if (!values.device_name || values.device_name.trim().length === 0) {
        message.error('ٍجب إدخال اسم الجهاز')
        return
      }

      if (!values.device_id) {
        message.error('ٍجب إدخال معرف الجهاز')
        return
      }

      if (values.device_id <= 0) {
        message.error('معرف الجهاز ٍجب أْ ٍكوْ أكبر من الصفر')
        return
      }

      if (!values.ip_address || values.ip_address.trim().length === 0) {
        message.error('ٍجب إدخال عْواْ IP')
        return
      }

      // التحقق من صحة عْواْ IP
      const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/
      if (!ipRegex.test(values.ip_address)) {
        message.error('عْواْ IP غير صحٍح')
        return
      }

      // التحقق من صحة المنفذ
      if (!values.port) {
        message.error('ٍجب إدخال رقم المنفذ')
        return
      }

      if (values.port < 1 || values.port > 65535) {
        message.error('رقم المنفذ ٍجب أْ ٍكوْ بٍْ 1 و 65535')
        return
      }

      // التحقق من صحة السعة
      if (!values.capacity) {
        message.error('ٍجب إدخال السعة القصوى')
        return
      }

      if (values.capacity <= 0) {
        message.error('السعة القصوى ٍجب أْ تكوْ أكبر من الصفر')
        return
      }

      if (values.capacity > 100000) {
        message.error('السعة القصوى كبٍرة جداًطŒ ٍرجى التحقق من القيمة المدخلة')
        return
      }

      // التحقق من صحة عدد المستخدمٍْ الحالٍٍْ
      if (values.current_users && values.current_users < 0) {
        message.error('عدد المستخدمٍْ الحالٍٍْ ٍجب أْ ٍكوْ أكبر من أو ٍساوٍ الصفر')
        return
      }

      if (values.current_users && values.current_users > values.capacity) {
        message.error('عدد المستخدمٍْ الحالٍٍْ لا يمكن أْ ٍكوْ أكبر من السعة القصوى')
        return
      }

      let result
      if (editingDevice) {
        result = await window.electronAPI.updateFingerprintDevice(editingDevice.id, values)
      } else {
        result = await window.electronAPI.createFingerprintDevice(values)
      }

      if (result.success) {
        message.success(editingDevice ? 'تم تحديث الجهاز بنجاح' : 'تم إضافة الجهاز بنجاح')
        setModalVisible(false)
        form.resetFields()
        fetchDevices()
      } else {
        message.error(result.message)
      }
    } catch (_error) {
      message.error('فشل في حفظ بيانات الجهاز')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'inactive': return 'red'
      case 'maintenance': return 'orange'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'ْشط'
      case 'inactive': return 'غير ْشط'
      case 'maintenance': return 'صٍاْة'
      default: return status
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircleOutlined />
      case 'inactive': return <CloseCircleOutlined />
      case 'maintenance': return <ToolOutlined />
      default: return <ExclamationCircleOutlined />
    }
  }

  const getCapacityUsage = (current: number, capacity: number) => {
    return capacity > 0 ? (current / capacity) * 100 : 0
  }

  const columns: ColumnsType<FingerprintDevice> = [
    {
      title: 'اسم الجهاز',
      dataIndex: 'device_name',
      key: 'device_name',
      width: 200,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.device_model} - ID: {record.device_id}
          </Text>
        </div>
      )
    },
    {
      title: 'عْواْ IP',
      key: 'network',
      width: 150,
      render: (_, record) => record.ip_address ? (
        <div>
          <Space>
            <WifiOutlined />
            {record.ip_address}:{record.port}
          </Space>
        </div>
      ) : '-'
    },
    {
      title: 'الموقع',
      dataIndex: 'location',
      key: 'location',
      width: 200,
      render: (location) => location || '-'
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'السعة',
      key: 'capacity',
      width: 150,
      render: (_, record) => {
        const usage = getCapacityUsage(record.current_users, record.capacity)
        return (
          <div>
            <div style={{ marginBottom: '4px' }}>
              {record.current_users} / {record.capacity}
            </div>
            <Progress 
              percent={usage} 
              size="small"
              strokeColor={usage > 90 ? '#ff4d4f' : usage > 70 ? '#faad14' : '#52c41a'}
              showInfo={false}
            />
          </div>
        )
      }
    },
    {
      title: 'إصدار البرْامج',
      dataIndex: 'firmware_version',
      key: 'firmware_version',
      width: 120,
      render: (version) => version ? (
        <Tag color="blue">{version}</Tag>
      ) : '-'
    },
    {
      title: 'آخر مزامنة',
      dataIndex: 'last_sync',
      key: 'last_sync',
      width: 150,
      render: (lastSync) => lastSync ? (
        <div>
          <div>{dayjs(lastSync).format('YYYY-MM-DD')}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {dayjs(lastSync).format('HH:mm')}
          </Text>
        </div>
      ) : (
        <Text type="secondary">لم ٍتم المزامنة</Text>
      )
    },
    {
      title: 'الإجراط،ات',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="تعديل">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditDevice(record)}
            />
          </Tooltip>
          <Tooltip title="اختبار الاتصال">
            <Button
              type="default"
              size="small"
              icon={<WifiOutlined />}
              loading={testingDeviceId === record.id}
              onClick={() => handleTestConnection(record)}
            />
          </Tooltip>
          <Tooltip title="مزامنة">
            <Button
              type="default"
              size="small"
              icon={<SyncOutlined />}
              loading={syncingDeviceId === record.id}
              onClick={() => handleSyncDevice(record.id)}
            />
          </Tooltip>
          <Tooltip title="إعدادات">
            <Button
              size="small"
              icon={<SettingOutlined />}
              onClick={() => {
                message.info('سٍتم تطوٍر صفحة الإعدادات قرٍباً')
              }}
            />
          </Tooltip>
          <Tooltip title="حذف">
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteDevice(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const activeDevices = devices.filter(device => device.status === 'active').length
  const totalUsers = devices.reduce((sum, device) => sum + device.current_users, 0)
  const totalCapacity = devices.reduce((sum, device) => sum + device.capacity, 0)
  const maintenanceDevices = devices.filter(device => device.status === 'maintenance').length

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <SafetyOutlined /> إدارة أجهزة البصمة
      </Title>
      
      {/* إحصائيات سرٍعة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="إجمالي الأجهزة"
              value={devices.length}
              prefix={<SafetyOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="الأجهزة الْشطة"
              value={activeDevices}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="المستخدمٍْ المسجلٍْ"
              value={totalUsers}
              suffix={`/ ${totalCapacity}`}
              prefix={<Badge />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="أجهزة في الصٍاْة"
              value={maintenanceDevices}
              prefix={<ToolOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* دليل التوصيل السريع */}
      <Card
        title={
          <Space>
            <ToolOutlined />
            <span>دليل التوصيل السريع</span>
          </Space>
        }
        style={{ marginBottom: '16px' }}
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} md={8}>
            <Card size="small" title="الخطوة 1: تحضير الجهاز">
              <div style={{ fontSize: '12px', lineHeight: '1.6' }}>
                • تأكد من توصيل الجهاز بالكهرباء<br/>
                • اتصل بنفس الشبكة المحلية<br/>
                • تحقق من عنوان IP للجهاز<br/>
                • المنفذ الافتراضي: 4370
              </div>
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card size="small" title="الخطوة 2: إضافة الجهاز">
              <div style={{ fontSize: '12px', lineHeight: '1.6' }}>
                • اضغط &quot;إضافة جهاز&quot;<br/>
                • أدخل اسم الجهاز والموقع<br/>
                • أدخل عنوان IP والمنفذ<br/>
                • حدد السعة القصوى
              </div>
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card size="small" title="الخطوة 3: اختبار الاتصال">
              <div style={{ fontSize: '12px', lineHeight: '1.6' }}>
                • اضغط &quot;مزامنة&quot; لاختبار الاتصال<br/>
                • تحقق من حالة الجهاز<br/>
                • راجع آخر وقت مزامنة<br/>
                • تأكد من عمل الجهاز بشكل صحيح
              </div>
            </Card>
          </Col>
        </Row>
      </Card>

      {/* جدول الأجهزة */}
      <Card
        title={
          <Space>
            <SafetyOutlined />
            <span>قائمة أجهزة البصمة</span>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<BookOutlined />}
              onClick={() => setSetupGuideVisible(true)}
            >
              دليل التوصيل
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateDevice}
            >
              إضافة جهاز
            </Button>
            <Button
              icon={<SyncOutlined />}
              onClick={() => {
                devices.forEach(device => {
                  if (device.status === 'active') {
                    handleSyncDevice(device.id)
                  }
                })
              }}
            >
              مزامنة الكل
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={devices}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} جهاز`
          }}
        />
      </Card>

      {/* نافذة إضافة/تعديل الجهاز */}
      <Modal
        title={
          <Space>
            <SafetyOutlined />
            {editingDevice ? 'تعديل جهاز البصمة' : 'إضافة جهاز بصمة جديد'}
          </Space>
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={900}
        destroyOnHidden
      >
        {/* نصائح سريعة */}
        <div style={{ marginBottom: '16px', padding: '12px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '6px' }}>
          <Text strong style={{ color: '#52c41a' }}>💡 نصائح سريعة:</Text>
          <div style={{ marginTop: '8px', fontSize: '12px', lineHeight: '1.6' }}>
            • تأكد من أن الجهاز متصل بنفس الشبكة المحلية<br/>
            • يمكنك العثور على عنوان IP من إعدادات الجهاز<br/>
            • المنفذ الافتراضي لمعّم الأجهزة هو 4370<br/>
            • استخدم &quot;اختبار الاتصال&quot; للتأكد من صحة الإعدادات
          </div>
        </div>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            port: 4370,
            status: 'active',
            capacity: 1000,
            current_users: 0
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="device_name"
                label="اسم الجهاز"
                rules={[{ required: true, message: 'ٍرجى إدخال اسم الجهاز' }]}
              >
                <Input placeholder="مثال: جهاز البصمة الرئٍسٍ" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="device_model"
                label="مودٍل الجهاز"
              >
                <Input placeholder="مثال: ZKTeco F18" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="device_id"
                label="معرف الجهاز"
                rules={[{ required: true, message: 'ٍرجى إدخال معرف الجهاز' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  placeholder="1"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="ip_address"
                label={
                  <Space>
                    <span>عْواْ IP</span>
                    <Button
                      size="small"
                      type="link"
                      onClick={() => form.setFieldsValue({ ip_address: '*************' })}
                      style={{ padding: 0, height: 'auto' }}
                    >
                      مثال
                    </Button>
                  </Space>
                }
                rules={[
                  { required: true, message: 'ٍرجى إدخال عْواْ IP' },
                  { pattern: /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/, message: 'عْواْ IP غير صحٍح' }
                ]}
              >
                <Input placeholder="*************" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="port"
                label={
                  <Space>
                    <span>المنفذ</span>
                    <Button
                      size="small"
                      type="link"
                      onClick={() => form.setFieldsValue({ port: 4370 })}
                      style={{ padding: 0, height: 'auto' }}
                    >
                      افتراضي
                    </Button>
                  </Space>
                }
                rules={[{ required: true, message: 'ٍرجى إدخال رقم المنفذ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  max={65535}
                  placeholder="4370"
                />
              </Form.Item>
            </Col>
          </Row>

          {/* أزرار الإعداد السريع */}
          <Row gutter={16} style={{ marginBottom: '16px' }}>
            <Col span={24}>
              <Text strong>إعداد سريع للأجهزة الشائعة:</Text>
              <div style={{ marginTop: '8px' }}>
                <Space wrap>
                  <Button
                    size="small"
                    onClick={() => {
                      form.setFieldsValue({
                        device_model: 'ZKTeco F18',
                        port: 4370,
                        capacity: 1000,
                        firmware_version: '6.60'
                      })
                    }}
                  >
                    ZKTeco F18
                  </Button>
                  <Button
                    size="small"
                    onClick={() => {
                      form.setFieldsValue({
                        device_model: 'ZKTeco K40',
                        port: 4370,
                        capacity: 3000,
                        firmware_version: '6.60'
                      })
                    }}
                  >
                    ZKTeco K40
                  </Button>
                  <Button
                    size="small"
                    onClick={() => {
                      form.setFieldsValue({
                        device_model: 'Hikvision DS-K1T201',
                        port: 8000,
                        capacity: 1000,
                        firmware_version: '1.4.41'
                      })
                    }}
                  >
                    Hikvision DS-K1T201
                  </Button>
                </Space>
              </div>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="location"
                label="موقع الجهاز"
              >
                <Input placeholder="مثال: المدخل الرئٍسٍ للمبْى" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="status"
                label="حالة الجهاز"
                rules={[{ required: true, message: 'ٍرجى اختٍار حالة الجهاز' }]}
              >
                <Select>
                  <Option value="active">ْشط</Option>
                  <Option value="inactive">غير ْشط</Option>
                  <Option value="maintenance">صٍاْة</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="capacity"
                label="السعة القصوى"
                rules={[{ required: true, message: 'ٍرجى إدخال السعة القصوى' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  placeholder="1000"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="firmware_version"
                label="إصدار البرْامج"
              >
                <Input placeholder="6.60.1.85" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="notes"
                label="ملاحّات"
              >
                <TextArea rows={3} placeholder="ملاحّات إضافية حول الجهاز" />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <Row justify="end">
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {editingDevice ? 'تحديث' : 'إضافة'}
              </Button>
            </Space>
          </Row>
        </Form>
      </Modal>

      {/* دليل التوصيل */}
      <FingerprintSetupGuide
        visible={setupGuideVisible}
        onClose={() => setSetupGuideVisible(false)}
      />
    </div>
  )
}

export default FingerprintDeviceManagement

