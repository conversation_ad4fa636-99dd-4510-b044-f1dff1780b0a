const fs = require('fs');
const path = require('path');
const initSqlJs = require('sql.js');

async function createTestDataForImages() {
  try {
    console.log('🧪 إنشاء بيانات تجريبية لاختبار نظام الصور...\n');
    
    const dbPath = path.join(process.env.APPDATA, 'accounting-production-app', 'accounting.db');
    
    if (!fs.existsSync(dbPath)) {
      console.log('❌ قاعدة البيانات غير موجودة:', dbPath);
      return;
    }
    
    const SQL = await initSqlJs({
      locateFile: file => path.join(__dirname, 'node_modules', 'sql.js', 'dist', file)
    });
    
    const filebuffer = fs.readFileSync(dbPath);
    const db = new SQL.Database(filebuffer);
    
    console.log('📋 إنشاء أوامر إنتاج تجريبية...');
    
    // إنشاء أوامر إنتاج تجريبية
    const testOrders = [
      { order_number: 'TEST-001', product_id: 1, quantity: 5, status: 'pending' },
      { order_number: 'TEST-002', product_id: 1, quantity: 10, status: 'in_progress' },
      { order_number: 'TEST-003', product_id: 1, quantity: 3, status: 'completed' }
    ];
    
    let createdOrders = 0;
    for (const order of testOrders) {
      try {
        db.run(`
          INSERT INTO production_orders (
            order_number, product_id, quantity, status, 
            start_date, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          order.order_number,
          order.product_id,
          order.quantity,
          order.status,
          new Date().toISOString().split('T')[0],
          new Date().toISOString(),
          new Date().toISOString()
        ]);
        createdOrders++;
        console.log(`   ✅ أمر إنتاج: ${order.order_number}`);
      } catch (error) {
        console.log(`   ⚠️ تخطي أمر ${order.order_number}: ${error.message}`);
      }
    }
    
    console.log(`\n📦 إنشاء أصناف تجريبية...`);
    
    // إنشاء أصناف تجريبية
    const testItems = [
      { code: 'ITEM-001', name: 'كرسي خشبي', category_id: 1, unit: 'قطعة', cost_price: 100, sale_price: 150 },
      { code: 'ITEM-002', name: 'طاولة مكتب', category_id: 1, unit: 'قطعة', cost_price: 200, sale_price: 300 },
      { code: 'ITEM-003', name: 'خزانة ملابس', category_id: 1, unit: 'قطعة', cost_price: 500, sale_price: 750 }
    ];
    
    let createdItems = 0;
    for (const item of testItems) {
      try {
        db.run(`
          INSERT INTO items (
            code, name, category_id, unit, cost_price, sale_price,
            quantity, min_quantity, max_quantity, is_active, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          item.code,
          item.name,
          item.category_id,
          item.unit,
          item.cost_price,
          item.sale_price,
          0, // quantity
          0, // min_quantity
          100, // max_quantity
          1, // is_active
          new Date().toISOString()
        ]);
        createdItems++;
        console.log(`   ✅ صنف: ${item.name}`);
      } catch (error) {
        console.log(`   ⚠️ تخطي صنف ${item.name}: ${error.message}`);
      }
    }
    
    console.log(`\n🖼️ إنشاء صور تجريبية لأوامر الإنتاج...`);
    
    // إنشاء صور لأوامر الإنتاج
    const ordersResult = db.exec('SELECT id, order_number FROM production_orders WHERE order_number LIKE "TEST-%"');
    if (ordersResult[0]?.values && ordersResult[0].values.length > 0) {
      let createdOrderImages = 0;
      for (const orderRow of ordersResult[0].values) {
        const orderId = orderRow[0];
        const orderNumber = orderRow[1];
        
        const svgContent = `<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="150" fill="#4ECDC4"/><text x="100" y="75" text-anchor="middle" fill="white" font-size="16">صورة تجريبية</text><text x="100" y="100" text-anchor="middle" fill="white" font-size="12">${orderNumber}</text></svg>`;
        const dataUrl = `data:image/svg+xml;charset=utf-8;base64,${Buffer.from(svgContent, 'utf8').toString('base64')}`;
        
        try {
          db.run(`
            INSERT INTO production_order_images (
              order_id, image_name, image_path, file_size, file_type,
              description, category, is_primary, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            orderId,
            `صورة تجريبية - ${orderNumber}`,
            dataUrl,
            svgContent.length,
            'image/svg+xml',
            'صورة تجريبية للاختبار',
            'general',
            1,
            new Date().toISOString()
          ]);
          createdOrderImages++;
          console.log(`   ✅ صورة لأمر ${orderNumber}`);
        } catch (error) {
          console.log(`   ⚠️ تخطي صورة أمر ${orderNumber}: ${error.message}`);
        }
      }
    }
    
    console.log(`\n🖼️ إنشاء صور تجريبية للأصناف...`);
    
    // إنشاء صور للأصناف
    const itemsResult = db.exec('SELECT id, name FROM items WHERE code LIKE "ITEM-%"');
    if (itemsResult[0]?.values && itemsResult[0].values.length > 0) {
      let createdItemImages = 0;
      for (const itemRow of itemsResult[0].values) {
        const itemId = itemRow[0];
        const itemName = itemRow[1];
        
        const svgContent = `<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="150" fill="#FF6B6B"/><text x="100" y="75" text-anchor="middle" fill="white" font-size="14">صنف</text><text x="100" y="100" text-anchor="middle" fill="white" font-size="12">${itemName}</text></svg>`;
        const dataUrl = `data:image/svg+xml;charset=utf-8;base64,${Buffer.from(svgContent, 'utf8').toString('base64')}`;
        
        try {
          db.run(`
            INSERT INTO item_images (
              item_id, image_name, image_path, file_size, file_type,
              description, is_primary, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            itemId,
            `صورة ${itemName}`,
            dataUrl,
            svgContent.length,
            'image/svg+xml',
            'صورة تجريبية للصنف',
            1,
            new Date().toISOString()
          ]);
          createdItemImages++;
          console.log(`   ✅ صورة للصنف ${itemName}`);
        } catch (error) {
          console.log(`   ⚠️ تخطي صورة صنف ${itemName}: ${error.message}`);
        }
      }
    }
    
    console.log(`\n🖼️ إنشاء صور في النظام الموحد...`);
    
    // إنشاء صور في النظام الموحد
    const unifiedImages = [
      { id: 'img-001', name: 'صورة موحدة 1', context_type: 'production_order', context_id: 1 },
      { id: 'img-002', name: 'صورة موحدة 2', context_type: 'item', context_id: 1 },
      { id: 'img-003', name: 'صورة موحدة 3', context_type: 'general', context_id: 0 }
    ];
    
    let createdUnifiedImages = 0;
    for (const img of unifiedImages) {
      try {
        const svgContent = `<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="150" fill="#9B59B6"/><text x="100" y="75" text-anchor="middle" fill="white" font-size="14">صورة موحدة</text><text x="100" y="100" text-anchor="middle" fill="white" font-size="12">${img.name}</text></svg>`;
        const dataUrl = `data:image/svg+xml;charset=utf-8;base64,${Buffer.from(svgContent, 'utf8').toString('base64')}`;
        
        db.run(`
          INSERT INTO unified_images (
            id, name, original_name, path, size, type, category,
            context_type, context_id, is_active, is_primary, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          img.id,
          img.name,
          img.name + '.svg',
          dataUrl,
          svgContent.length,
          'image/svg+xml',
          'general',
          img.context_type,
          img.context_id,
          1,
          1,
          new Date().toISOString()
        ]);
        createdUnifiedImages++;
        console.log(`   ✅ ${img.name}`);
      } catch (error) {
        console.log(`   ⚠️ تخطي ${img.name}: ${error.message}`);
      }
    }
    
    console.log(`\n⚙️ تحديث إعدادات الصور...`);
    
    // تحديث إعدادات الصور
    const settings = [
      { key: 'max_file_size', value: '10485760' },
      { key: 'allowed_types', value: 'image/jpeg,image/png,image/gif,image/webp,image/svg+xml' },
      { key: 'thumbnail_width', value: '150' },
      { key: 'thumbnail_height', value: '150' },
      { key: 'image_quality', value: '85' },
      { key: 'storage_path', value: 'images' },
      { key: 'enable_watermark', value: 'false' },
      { key: 'auto_backup', value: 'true' }
    ];
    
    let updatedSettings = 0;
    for (const setting of settings) {
      try {
        db.run(`
          INSERT OR REPLACE INTO image_settings (setting_key, setting_value, created_at, updated_at)
          VALUES (?, ?, ?, ?)
        `, [setting.key, setting.value, new Date().toISOString(), new Date().toISOString()]);
        updatedSettings++;
      } catch (error) {
        console.log(`   ⚠️ تخطي إعداد ${setting.key}: ${error.message}`);
      }
    }
    console.log(`   ✅ تم تحديث ${updatedSettings} إعداد`);
    
    // حفظ قاعدة البيانات
    console.log('\n💾 حفظ التغييرات...');
    const data = db.export();
    fs.writeFileSync(dbPath, data);
    
    // إحصائيات نهائية
    console.log('\n📊 إحصائيات البيانات التجريبية:');
    
    const stats = {};
    const tables = [
      'production_orders',
      'items',
      'production_order_images',
      'item_images',
      'unified_images',
      'image_settings'
    ];
    
    for (const table of tables) {
      try {
        const result = db.exec(`SELECT COUNT(*) FROM ${table}`);
        stats[table] = result[0]?.values[0]?.[0] || 0;
        console.log(`   📋 ${table}: ${stats[table]} سجل`);
      } catch (error) {
        console.log(`   ❌ خطأ في قراءة ${table}: ${error.message}`);
      }
    }
    
    db.close();
    
    console.log('\n' + '='.repeat(80));
    console.log('✅ تم إنشاء البيانات التجريبية بنجاح!');
    console.log('='.repeat(80));
    
    console.log('\n🎯 ما تم إنشاؤه:');
    console.log(`   ✅ ${createdOrders} أوامر إنتاج تجريبية`);
    console.log(`   ✅ ${createdItems} أصناف تجريبية`);
    console.log(`   ✅ صور أوامر الإنتاج`);
    console.log(`   ✅ صور الأصناف`);
    console.log(`   ✅ صور النظام الموحد`);
    console.log(`   ✅ إعدادات النظام`);
    
    console.log('\n📝 الآن يمكنك:');
    console.log('   1. إعادة تشغيل التطبيق');
    console.log('   2. اختبار عرض الصور في أوامر الإنتاج');
    console.log('   3. اختبار عرض الصور في الأصناف');
    console.log('   4. اختبار طباعة الصور');
    console.log('   5. اختبار رفع صور جديدة');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات التجريبية:', error);
  }
}

// تشغيل إنشاء البيانات التجريبية
createTestDataForImages();
