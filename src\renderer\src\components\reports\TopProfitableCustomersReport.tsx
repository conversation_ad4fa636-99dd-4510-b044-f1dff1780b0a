import React from 'react';
import { Tag, Typography, Progress, Statistic, Tooltip } from 'antd';
import {
  UserOutlined,
  DollarOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  StarOutlined,
  CrownOutlined,
  ShoppingCartOutlined,
  CalendarOutlined,
  PercentageOutlined
} from '@ant-design/icons';
import UniversalReport from './UniversalReport';

import type { ReportData, ReportType } from '../../types/reports';
import { createDefaultFilters } from '../../types/reports';
import { SafeLogger as Logger } from '../../utils/logger';

const { Text } = Typography;

const TopProfitableCustomersReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      Logger.info('TopProfitableCustomersReport', '👑 بدء إنشاء تقرير أكثر العملاء ربحية...');
      Logger.info('TopProfitableCustomersReport', '🔍 الفلاتر المطبقة:', filters);

      let customersData: any[] = [];

      if (!window.electronAPI || !window.electronAPI.getTopProfitableCustomers) {
        Logger.info('TopProfitableCustomersReport', '⚠️ التطبيق يعمل في وضع المتصفح أو الدالة غير متوفرة - استخدام بيانات وهمية');

        // بيانات وهمية للعملاء الأكثر ربحية
        customersData = [
          {
            customer_id: 1,
            customer_code: 'CUST001',
            customer_name: 'شركة الأمل التجارية',
            customer_type: 'corporate',
            total_sales: 125000.00,
            total_profit: 35000.00,
            profit_margin: 28.0,
            total_orders: 45,
            avg_order_value: 2777.78,
            last_order_date: '2024-01-20',
            customer_rank: 1,
            growth_rate: 15.5,
            loyalty_score: 95
          },
          {
            customer_id: 2,
            customer_code: 'CUST002',
            customer_name: 'مؤسسة النور للتجارة',
            customer_type: 'corporate',
            total_sales: 98000.00,
            total_profit: 28000.00,
            profit_margin: 28.6,
            total_orders: 38,
            avg_order_value: 2578.95,
            last_order_date: '2024-01-18',
            customer_rank: 2,
            growth_rate: 12.3,
            loyalty_score: 88
          },
          {
            customer_id: 3,
            customer_code: 'CUST003',
            customer_name: 'شركة البركة للاستيراد',
            customer_type: 'corporate',
            total_sales: 87500.00,
            total_profit: 22000.00,
            profit_margin: 25.1,
            total_orders: 32,
            avg_order_value: 2734.38,
            last_order_date: '2024-01-22',
            customer_rank: 3,
            growth_rate: 8.7,
            loyalty_score: 82
          },
          {
            customer_id: 4,
            customer_code: 'CUST004',
            customer_name: 'مجموعة الفجر التجارية',
            customer_type: 'corporate',
            total_sales: 76000.00,
            total_profit: 18500.00,
            profit_margin: 24.3,
            total_orders: 28,
            avg_order_value: 2714.29,
            last_order_date: '2024-01-15',
            customer_rank: 4,
            growth_rate: 5.2,
            loyalty_score: 78
          },
          {
            customer_id: 5,
            customer_code: 'CUST005',
            customer_name: 'شركة الرائد للتوزيع',
            customer_type: 'corporate',
            total_sales: 65000.00,
            total_profit: 16000.00,
            profit_margin: 24.6,
            total_orders: 25,
            avg_order_value: 2600.00,
            last_order_date: '2024-01-19',
            customer_rank: 5,
            growth_rate: 3.8,
            loyalty_score: 75
          }
        ];
      } else {
        // استعلام البيانات من قاعدة البيانات
        const response = await window.electronAPI.getTopProfitableCustomers({
          dateRange: filters.dateRange,
          customerType: filters.customerType,
          limit: filters.limit || 50
        });

        if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
          throw new Error('لا توجد بيانات متاحة');
        }

        customersData = response.data;
      }

      // تعريف الأعمدة
      const columns = [
        {
          title: 'الترتيب',
          key: 'customer_rank',
          format: 'number' as const,
          render: (record: any) => {
            const rank = record.customer_rank;
            let color = '#1890ff';
            let icon = <TrophyOutlined />;
            
            if (rank === 1) {
              color = '#faad14';
              icon = <CrownOutlined />;
            } else if (rank === 2) {
              color = '#52c41a';
              icon = <StarOutlined />;
            } else if (rank === 3) {
              color = '#fa8c16';
              icon = <TrophyOutlined />;
            }
            
            return (
              <div style={{ textAlign: 'center' }}>
                {icon && React.cloneElement(icon, { style: { color, marginLeft: '4px' } })}
                <strong style={{ color, fontSize: '18px' }}>
                  #{rank}
                </strong>
              </div>
            );
          },
          width: 80,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'العميل',
          key: 'customer',
          render: (record: any) => (
            <div>
              <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
                <UserOutlined style={{ marginLeft: '8px' }} />
                {record.customer_name}
              </div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.customer_code} - {record.customer_type === 'corporate' ? 'شركة' : 'فرد'}
              </Text>
            </div>
          ),
          width: 250
        },
        {
          title: 'إجمالي المبيعات',
          key: 'total_sales',
          format: 'currency' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <ShoppingCartOutlined style={{ color: '#1890ff', marginLeft: '4px' }} />
              <strong style={{ color: '#1890ff', fontSize: '16px' }}>
                {record.total_sales.toLocaleString('ar-EG', { minimumFractionDigits: 2 })}
              </strong>
              <div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  ج.م
                </Text>
              </div>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'إجمالي الربح',
          key: 'total_profit',
          format: 'currency' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <DollarOutlined style={{ color: '#52c41a', marginLeft: '4px' }} />
              <strong style={{ color: '#52c41a', fontSize: '16px' }}>
                {record.total_profit.toLocaleString('ar-EG', { minimumFractionDigits: 2 })}
              </strong>
              <div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  ج.م
                </Text>
              </div>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'هامش الربح',
          key: 'profit_margin',
          format: 'percentage' as const,
          render: (record: any) => {
            const margin = record.profit_margin;
            const color = margin >= 30 ? '#52c41a' :
                         margin >= 25 ? '#1890ff' :
                         margin >= 20 ? '#fa8c16' : '#ff4d4f';
            return (
              <div style={{ textAlign: 'center' }}>
                <PercentageOutlined style={{ color, marginLeft: '4px' }} />
                <strong style={{ color, fontSize: '16px' }}>{margin.toFixed(1)}%</strong>
                <Progress 
                  percent={margin} 
                  size="small" 
                  strokeColor={color}
                  showInfo={false}
                  style={{ width: '80px', margin: '4px auto' }}
                />
              </div>
            );
          },
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'عدد الطلبات',
          key: 'total_orders',
          format: 'number' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <CalendarOutlined style={{ color: '#722ed1', marginLeft: '4px' }} />
              <strong style={{ color: '#722ed1', fontSize: '16px' }}>
                {record.total_orders}
              </strong>
              <div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  طلب
                </Text>
              </div>
            </div>
          ),
          width: 100,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'متوسط قيمة الطلب',
          key: 'avg_order_value',
          format: 'currency' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <strong style={{ color: '#fa8c16', fontSize: '16px' }}>
                {record.avg_order_value.toLocaleString('ar-EG', { minimumFractionDigits: 2 })}
              </strong>
              <div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  ج.م
                </Text>
              </div>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'معدل النمو',
          key: 'growth_rate',
          format: 'percentage' as const,
          render: (record: any) => {
            const growth = record.growth_rate;
            const color = growth >= 10 ? '#52c41a' :
                         growth >= 5 ? '#1890ff' :
                         growth >= 0 ? '#fa8c16' : '#ff4d4f';
            const icon = growth >= 0 ? <RiseOutlined /> : <FallOutlined />;
            return (
              <div style={{ textAlign: 'center' }}>
                {React.cloneElement(icon, { style: { color, marginLeft: '4px' } })}
                <strong style={{ color, fontSize: '16px' }}>{growth.toFixed(1)}%</strong>
              </div>
            );
          },
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'نقاط الولاء',
          key: 'loyalty_score',
          render: (record: any) => {
            const score = record.loyalty_score;
            const color = score >= 90 ? '#52c41a' :
                         score >= 80 ? '#1890ff' :
                         score >= 70 ? '#fa8c16' : '#ff4d4f';
            return (
              <div style={{ textAlign: 'center' }}>
                <StarOutlined style={{ color, marginLeft: '4px' }} />
                <strong style={{ color, fontSize: '16px' }}>{score}</strong>
                <Progress 
                  percent={score} 
                  size="small" 
                  strokeColor={color}
                  showInfo={false}
                  style={{ width: '80px', margin: '4px auto' }}
                />
              </div>
            );
          },
          width: 120,
          align: 'center' as const,
          sortable: true
        }
      ];

      // حساب الإحصائيات
      const totalCustomers = customersData.length;
      const totalSales = customersData.reduce((sum, cust) => sum + (cust.total_sales || 0), 0);
      const totalProfit = customersData.reduce((sum, cust) => sum + (cust.total_profit || 0), 0);
      const avgProfitMargin = totalCustomers > 0 ? 
        customersData.reduce((sum, cust) => sum + (cust.profit_margin || 0), 0) / totalCustomers : 0;
      const totalOrders = customersData.reduce((sum, cust) => sum + (cust.total_orders || 0), 0);
      const avgGrowthRate = totalCustomers > 0 ? 
        customersData.reduce((sum, cust) => sum + (cust.growth_rate || 0), 0) / totalCustomers : 0;

      Logger.info('TopProfitableCustomersReport', `✅ تم إنشاء تقرير أكثر العملاء ربحية بنجاح: ${totalCustomers} عميل`);

      return {
        title: 'تقرير أكثر العملاء ربحية',
        data: customersData,
        columns,
        summary: {
          totalCustomers,
          totalSales: Math.round(totalSales * 100) / 100,
          totalProfit: Math.round(totalProfit * 100) / 100,
          avgProfitMargin: Math.round(avgProfitMargin * 10) / 10,
          totalOrders,
          avgGrowthRate: Math.round(avgGrowthRate * 10) / 10,
          topCustomer: customersData.length > 0 ? customersData[0] : null
        },
        metadata: {
          reportType: 'top_profitable_customers' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: customersData.length
        }
      };
    } catch (error) {
      Logger.error('TopProfitableCustomersReport', 'خطأ في إنشاء تقرير أكثر العملاء ربحية:', error);
      throw new Error('فشل في إنشاء تقرير أكثر العملاء ربحية');
    }
  };

  return (
    <UniversalReport
      reportType={'top_profitable_customers' as ReportType}
      title="تقرير أكثر العملاء ربحية"
      description="تقرير مرتب للعملاء الأكثر ربحية مع تحليل الأداء ومعدلات النمو"
      onGenerateReport={generateReport}
      defaultFilters={createDefaultFilters('top_profitable_customers')}
      showDateRange={true}
      showCustomerFilter={false}
      showAdvancedSearch={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default TopProfitableCustomersReport;
