import React, { useState, useEffect } from 'react'
import { <PERSON>, But<PERSON>, DatePicker, Table, Spin, message, Row, Col, Statistic, Typography, Select, Alert, Tag } from 'antd'
import { PrinterOutlined, FileExcelOutlined, BankOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import * as XLSX from 'xlsx'
import { Logger } from '../../utils/logger'

const { Title, Text } = Typography
const { Option } = Select

interface BankTransaction {
  id: number
  transaction_date: string
  description: string
  debit_amount: number
  credit_amount: number
  balance: number
  reference_number?: string
  is_reconciled: boolean
  reconciled_date?: string
  reconciled_by?: string
}

interface BookTransaction {
  id: number
  entry_date: string
  description: string
  debit_amount: number
  credit_amount: number
  reference_number?: string
  voucher_number?: string
  is_reconciled: boolean
  reconciled_date?: string
}

interface ReconciliationData {
  bank_account: {
    id: number
    account_name: string
    account_number: string
    bank_name: string
  }
  period: {
    from_date: string
    to_date: string
  }
  bank_statement: {
    opening_balance: number
    closing_balance: number
    transactions: BankTransaction[]
  }
  book_records: {
    opening_balance: number
    closing_balance: number
    transactions: BookTransaction[]
  }
  reconciliation: {
    reconciled_items: number
    unreconciled_bank_items: BankTransaction[]
    unreconciled_book_items: BookTransaction[]
    outstanding_checks: BankTransaction[]
    deposits_in_transit: BankTransaction[]
    bank_errors: BankTransaction[]
    book_errors: BookTransaction[]
    adjusted_bank_balance: number
    adjusted_book_balance: number
    difference: number
  }
  // خصائص إضافية للتوافق
  bankTransactions: BankTransaction[]
  bookTransactions: BookTransaction[]
  bankBalance: number
  bookBalance: number
  reconciledItemsCount: number
  unreconciledItemsCount: number
}

const BankReconciliationReport: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [reconciliationData, setReconciliationData] = useState<ReconciliationData | null>(null)
  const [bankAccounts, setBankAccounts] = useState<any[]>([])
  const [selectedBankAccount, setSelectedBankAccount] = useState<number | null>(null)
  const [reconciliationDate, setReconciliationDate] = useState(dayjs().endOf('month'))

  useEffect(() => {
    loadBankAccounts()
  }, [])

  const loadBankAccounts = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getBankAccounts()
        if (response.success) {
          setBankAccounts(response.data || [])
          if (response.data && response.data.length > 0) {
            setSelectedBankAccount(response.data[0].id)
          }
        }
      }
    } catch (error) {
      Logger.error('BankReconciliationReport', 'خطأ في تحميل الحسابات البنكية:', error)
    }
  }

  const loadReconciliation = async () => {
    if (!selectedBankAccount) {
      message.warning('يرجى اختيار حساب بنكي')
      return
    }

    setLoading(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generateBankReconciliation({
          bankAccountId: selectedBankAccount,
          asOfDate: reconciliationDate.format('YYYY-MM-DD')
        })

        if (response.success) {
          setReconciliationData(response.data)
        } else {
          message.error(response.message || 'فشل في تحميل مطابقة البنك')
        }
      } else {
        message.error('لا يمكن الوصول إلى قاعدة البيانات')
      }
    } catch (error) {
      Logger.error('BankReconciliationReport', 'خطأ في تحميل مطابقة البنك:', error)
      message.error('حدث خطأ أثناء تحميل مطابقة البنك')
    } finally {
      setLoading(false)
    }
  }

  const handleBankAccountChange = (value: number) => {
    setSelectedBankAccount(value)
  }

  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      setReconciliationDate(date)
    }
  }

  const exportToExcel = () => {
    if (!reconciliationData) {
      message.warning('لا توجد بيانات للتصدير')
      return
    }

    try {
      const workbook = XLSX.utils.book_new()

      // ملخص المطابقة
      const summaryData = [
        ['مطابقة البنك', '', ''],
        [`الحساب: ${reconciliationData.bank_account.account_name}`, '', ''],
        [`البنك: ${reconciliationData.bank_account.bank_name}`, '', ''],
        [`رقم الحساب: ${reconciliationData.bank_account.account_number}`, '', ''],
        [`كما في: ${reconciliationDate.format('YYYY-MM-DD')}`, '', ''],
        ['', '', ''],
        ['البيان', 'المبلغ (₪)', 'ملاحظات'],
        ['رصيد البنك الختامي', reconciliationData.bank_statement.closing_balance.toLocaleString(), ''],
        ['ناقص: الشيكات المعلقة', reconciliationData.reconciliation.outstanding_checks.reduce((sum, item) => sum + item.debit_amount, 0).toLocaleString(), ''],
        ['زائد: الودائع في الطريق', reconciliationData.reconciliation.deposits_in_transit.reduce((sum, item) => sum + item.credit_amount, 0).toLocaleString(), ''],
        ['الرصيد البنكي المعدل', reconciliationData.reconciliation.adjusted_bank_balance.toLocaleString(), ''],
        ['', '', ''],
        ['رصيد الدفاتر الختامي', reconciliationData.book_records.closing_balance.toLocaleString(), ''],
        ['الرصيد الدفتري المعدل', reconciliationData.reconciliation.adjusted_book_balance.toLocaleString(), ''],
        ['', '', ''],
        ['الفرق', reconciliationData.reconciliation.difference.toLocaleString(), reconciliationData.reconciliation.difference === 0 ? 'متطابق' : 'غير متطابق']
      ]

      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData)
      summarySheet['!cols'] = [{ wch: 25 }, { wch: 15 }, { wch: 20 }]
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'ملخص المطابقة')

      // الشيكات المعلقة
      if (reconciliationData.reconciliation.outstanding_checks.length > 0) {
        const checksData = [
          ['الشيكات المعلقة', '', '', ''],
          ['التاريخ', 'الوصف', 'رقم المرجع', 'المبلغ'],
          ...reconciliationData.reconciliation.outstanding_checks.map(check => [
            dayjs(check.transaction_date).format('YYYY-MM-DD'),
            check.description,
            check.reference_number || '',
            check.debit_amount.toLocaleString()
          ])
        ]
        const checksSheet = XLSX.utils.aoa_to_sheet(checksData)
        checksSheet['!cols'] = [{ wch: 12 }, { wch: 30 }, { wch: 15 }, { wch: 12 }]
        XLSX.utils.book_append_sheet(workbook, checksSheet, 'الشيكات المعلقة')
      }

      // الودائع في الطريق
      if (reconciliationData.reconciliation.deposits_in_transit.length > 0) {
        const depositsData = [
          ['الودائع في الطريق', '', '', ''],
          ['التاريخ', 'الوصف', 'رقم المرجع', 'المبلغ'],
          ...reconciliationData.reconciliation.deposits_in_transit.map(deposit => [
            dayjs(deposit.transaction_date).format('YYYY-MM-DD'),
            deposit.description,
            deposit.reference_number || '',
            deposit.credit_amount.toLocaleString()
          ])
        ]
        const depositsSheet = XLSX.utils.aoa_to_sheet(depositsData)
        depositsSheet['!cols'] = [{ wch: 12 }, { wch: 30 }, { wch: 15 }, { wch: 12 }]
        XLSX.utils.book_append_sheet(workbook, depositsSheet, 'الودائع في الطريق')
      }

      const fileName = `مطابقة_البنك_${reconciliationData.bank_account.account_name}_${reconciliationDate.format('YYYY-MM-DD')}.xlsx`
      XLSX.writeFile(workbook, fileName)

      message.success('تم تصدير مطابقة البنك بنجاح')
    } catch (error) {
      Logger.error('BankReconciliationReport', 'خطأ في تصدير Excel:', error)
      message.error('فشل في تصدير البيانات')
    }
  }



  const outstandingChecksColumns = [
    {
      key: 'transaction_date',
      title: 'التاريخ',
      width: '120px',
      align: 'center' as const,
      format: 'date' as const,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      key: 'description',
      title: 'الوصف',
      width: '200px',
      align: 'right' as const
    },
    {
      key: 'reference_number',
      title: 'رقم المرجع',
      width: '120px',
      align: 'center' as const,
      render: (value: string) => value || '-',
    },
    {
      key: 'debit_amount',
      title: 'المبلغ (₪)',
      width: '120px',
      align: 'right' as const,
      format: 'currency' as const,
      render: (value: number) => `₪${value.toLocaleString()}`,
    },
  ]

  const depositsColumns = [
    {
      key: 'transaction_date',
      title: 'التاريخ',
      width: '120px',
      align: 'center' as const,
      format: 'date' as const,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      key: 'description',
      title: 'الوصف',
      width: '200px',
      align: 'right' as const
    },
    {
      key: 'reference_number',
      title: 'رقم المرجع',
      width: '120px',
      align: 'center' as const,
      render: (value: string) => value || '-',
    },
    {
      key: 'credit_amount',
      title: 'المبلغ (₪)',
      width: '120px',
      align: 'right' as const,
      format: 'currency' as const,
      render: (value: number) => `₪${value.toLocaleString()}`,
    },
  ]

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            <BankOutlined style={{ marginRight: '8px' }} />
            مطابقة البنك
          </Title>
          
          <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
            <Select
              placeholder="اختر الحساب البنكي"
              style={{ width: 200 }}
              value={selectedBankAccount}
              onChange={handleBankAccountChange}
            >
              {bankAccounts.map(account => (
                <Option key={account.id} value={account.id}>
                  {account.account_name}
                </Option>
              ))}
            </Select>
            <DatePicker
              value={reconciliationDate}
              onChange={handleDateChange}
              placeholder="تاريخ المطابقة"
              style={{ width: 150 }}
            />
            <Button
              type="primary"
              onClick={loadReconciliation}
              loading={loading}
              disabled={!selectedBankAccount}
            >
              تحديث
            </Button>

            <Button
              icon={<FileExcelOutlined />}
              onClick={exportToExcel}
              disabled={!reconciliationData}
            >
              تصدير Excel
            </Button>
          </div>
        </div>

        {loading ? (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>جاري تحميل مطابقة البنك...</div>
          </div>
        ) : reconciliationData ? (
          <>
            <Alert
              message={
                reconciliationData.reconciliation.difference === 0 
                  ? "المطابقة متوازنة ✓" 
                  : `يوجد فرق في المطابقة: ₪${reconciliationData.reconciliation.difference.toLocaleString()}`
              }
              type={reconciliationData.reconciliation.difference === 0 ? "success" : "warning"}
              showIcon
              style={{ marginBottom: '24px' }}
            />

            <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="رصيد البنك"
                    value={reconciliationData.bank_statement.closing_balance}
                    precision={2}
                    prefix="₪"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="رصيد الدفاتر"
                    value={reconciliationData.book_records.closing_balance}
                    precision={2}
                    prefix="₪"
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="البنود المطابقة"
                    value={reconciliationData.reconciliation.reconciled_items}
                    suffix="بند"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="الفرق"
                    value={reconciliationData.reconciliation.difference}
                    precision={2}
                    prefix="₪"
                    valueStyle={{ 
                      color: reconciliationData.reconciliation.difference === 0 ? '#52c41a' : '#ff4d4f' 
                    }}
                    suffix={
                      reconciliationData.reconciliation.difference === 0 
                        ? <CheckCircleOutlined /> 
                        : <CloseCircleOutlined />
                    }
                  />
                </Card>
              </Col>
            </Row>

            <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
              <Col span={12}>
                <Card title="ملخص المطابقة" size="small">
                  <div style={{ marginBottom: '16px' }}>
                    <Row justify="space-between">
                      <Col><Text>رصيد البنك الختامي:</Text></Col>
                      <Col><Text strong>₪{reconciliationData.bank_statement.closing_balance.toLocaleString()}</Text></Col>
                    </Row>
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <Row justify="space-between">
                      <Col><Text>ناقص: الشيكات المعلقة:</Text></Col>
                      <Col>
                        <Text strong style={{ color: '#ff4d4f' }}>
                          (₪{reconciliationData.reconciliation.outstanding_checks.reduce((sum, item) => sum + item.debit_amount, 0).toLocaleString()})
                        </Text>
                      </Col>
                    </Row>
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <Row justify="space-between">
                      <Col><Text>زائد: الودائع في الطريق:</Text></Col>
                      <Col>
                        <Text strong style={{ color: '#52c41a' }}>
                          ₪{reconciliationData.reconciliation.deposits_in_transit.reduce((sum, item) => sum + item.credit_amount, 0).toLocaleString()}
                        </Text>
                      </Col>
                    </Row>
                  </div>
                  <div style={{ padding: '12px', backgroundColor: '#f0f0f0', borderRadius: '4px', marginBottom: '16px' }}>
                    <Row justify="space-between">
                      <Col><Text strong>الرصيد البنكي المعدل:</Text></Col>
                      <Col><Text strong>₪{reconciliationData.reconciliation.adjusted_bank_balance.toLocaleString()}</Text></Col>
                    </Row>
                  </div>
                  <div style={{ marginBottom: '16px' }}>
                    <Row justify="space-between">
                      <Col><Text>رصيد الدفاتر الختامي:</Text></Col>
                      <Col><Text strong>₪{reconciliationData.book_records.closing_balance.toLocaleString()}</Text></Col>
                    </Row>
                  </div>
                  <div style={{ padding: '12px', backgroundColor: '#f0f0f0', borderRadius: '4px', marginBottom: '16px' }}>
                    <Row justify="space-between">
                      <Col><Text strong>الرصيد الدفتري المعدل:</Text></Col>
                      <Col><Text strong>₪{reconciliationData.reconciliation.adjusted_book_balance.toLocaleString()}</Text></Col>
                    </Row>
                  </div>
                  <div style={{ 
                    padding: '12px', 
                    backgroundColor: reconciliationData.reconciliation.difference === 0 ? '#f6ffed' : '#fff2f0', 
                    borderRadius: '4px',
                    border: `1px solid ${reconciliationData.reconciliation.difference === 0 ? '#b7eb8f' : '#ffccc7'}`
                  }}>
                    <Row justify="space-between">
                      <Col>
                        <Text strong style={{ 
                          color: reconciliationData.reconciliation.difference === 0 ? '#52c41a' : '#ff4d4f' 
                        }}>
                          الفرق:
                        </Text>
                      </Col>
                      <Col>
                        <Text strong style={{ 
                          color: reconciliationData.reconciliation.difference === 0 ? '#52c41a' : '#ff4d4f' 
                        }}>
                          ₪{reconciliationData.reconciliation.difference.toLocaleString()}
                          {reconciliationData.reconciliation.difference === 0 ? ' ✓' : ' ✗'}
                        </Text>
                      </Col>
                    </Row>
                  </div>
                </Card>
              </Col>

              <Col span={12}>
                <Card title="معلومات الحساب" size="small">
                  <div style={{ marginBottom: '12px' }}>
                    <Text strong>اسم الحساب: </Text>
                    <Text>{reconciliationData.bank_account.account_name}</Text>
                  </div>
                  <div style={{ marginBottom: '12px' }}>
                    <Text strong>البنك: </Text>
                    <Text>{reconciliationData.bank_account.bank_name}</Text>
                  </div>
                  <div style={{ marginBottom: '12px' }}>
                    <Text strong>رقم الحساب: </Text>
                    <Text>{reconciliationData.bank_account.account_number}</Text>
                  </div>
                  <div style={{ marginBottom: '12px' }}>
                    <Text strong>تاريخ المطابقة: </Text>
                    <Text>{reconciliationDate.format('YYYY-MM-DD')}</Text>
                  </div>
                  <div style={{ marginTop: '20px' }}>
                    <Title level={5}>حالة المطابقة</Title>
                    <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                      <Tag color={reconciliationData.reconciliation.difference === 0 ? 'green' : 'red'}>
                        {reconciliationData.reconciliation.difference === 0 ? 'متطابقة' : 'غير متطابقة'}
                      </Tag>
                      <Tag color="blue">
                        {reconciliationData.reconciliation.reconciled_items} بند مطابق
                      </Tag>
                      {reconciliationData.reconciliation.outstanding_checks.length > 0 && (
                        <Tag color="orange">
                          {reconciliationData.reconciliation.outstanding_checks.length} شيك معلق
                        </Tag>
                      )}
                      {reconciliationData.reconciliation.deposits_in_transit.length > 0 && (
                        <Tag color="purple">
                          {reconciliationData.reconciliation.deposits_in_transit.length} وديعة في الطريق
                        </Tag>
                      )}
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>

            {reconciliationData.reconciliation.outstanding_checks.length > 0 && (
              <Card title="الشيكات المعلقة" style={{ marginBottom: '16px' }} size="small">
                <Table
                  columns={outstandingChecksColumns}
                  dataSource={reconciliationData.reconciliation.outstanding_checks}
                  pagination={false}
                  size="small"
                  rowKey="id"
                  summary={() => (
                    <Table.Summary.Row style={{ backgroundColor: '#fff2f0' }}>
                      <Table.Summary.Cell index={0} colSpan={3}>
                        <Text strong>إجمالي الشيكات المعلقة</Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={3} align="right">
                        <Text strong style={{ color: '#ff4d4f' }}>
                          ₪{reconciliationData.reconciliation.outstanding_checks.reduce((sum, item) => sum + item.debit_amount, 0).toLocaleString()}
                        </Text>
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  )}
                />
              </Card>
            )}

            {reconciliationData.reconciliation.deposits_in_transit.length > 0 && (
              <Card title="الودائع في الطريق" size="small">
                <Table
                  columns={depositsColumns}
                  dataSource={reconciliationData.reconciliation.deposits_in_transit}
                  pagination={false}
                  size="small"
                  rowKey="id"
                  summary={() => (
                    <Table.Summary.Row style={{ backgroundColor: '#f6ffed' }}>
                      <Table.Summary.Cell index={0} colSpan={3}>
                        <Text strong>إجمالي الودائع في الطريق</Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={3} align="right">
                        <Text strong style={{ color: '#52c41a' }}>
                          ₪{reconciliationData.reconciliation.deposits_in_transit.reduce((sum, item) => sum + item.credit_amount, 0).toLocaleString()}
                        </Text>
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  )}
                />
              </Card>
            )}
          </>
        ) : (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Text type="secondary">اختر حساب بنكي وانقر على &quot;تحديث&quot; لعرض مطابقة البنك</Text>
          </div>
        )}
      </Card>
    </div>
  )
}

export default BankReconciliationReport
