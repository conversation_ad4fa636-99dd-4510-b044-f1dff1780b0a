// تصدير جميع التقارير
export { default as ReportBase } from './ReportBase';
export { default as UniversalReport } from './UniversalReport';
export { default as PrintableReport } from './PrintableReport';
export { default as ExcelExportButton } from './ExcelExportButton';
export { default as PDFExportButton } from './PDFExportButton';

// التقارير المالية والإدارية الجديدة
export { default as ProfitLossReport } from './ProfitLossReport';
export { default as CashFlowAnalysisReport } from './CashFlowAnalysisReport';
export { default as CustomerAnalysisReport } from './CustomerAnalysisReport';
export { default as SupplierAnalysisReport } from './SupplierAnalysisReport';
export { default as AdvancedInventoryReport } from './AdvancedInventoryReport';
export { default as FinancialSummaryReport } from './FinancialSummaryReport';

// مراقب الأداء
export { default as ReportPerformanceMonitor } from './ReportPerformanceMonitor';
export { default as AdvancedFilters } from './AdvancedFilters';
export { default as QuickStats, SalesStats, CustomerStats, ProductStats } from './QuickStats';

// تقارير المخزون
export { default as InventoryDetailedReport } from './InventoryDetailedReport';
export { default as InventoryMovementsReport } from './InventoryMovementsReport';
export { default as LowStockReport } from './LowStockReport';
export { default as MaterialConsumptionReport } from './MaterialConsumptionReport';
export { default as InventoryAuditReport } from './InventoryAuditReport';

// تقارير المشتريات
export { default as PurchasesBySupplierReport } from './PurchasesBySupplierReport';
export { default as PurchasesByItemReport } from './PurchasesByItemReport';
export { default as SupplierPayablesReport } from './SupplierPayablesReport';
export { default as PurchaseAnalysisReport } from './PurchaseAnalysisReport';
export { default as CostAnalysisReport } from './CostAnalysisReport';
export { default as SupplierPriceComparisonReport } from './SupplierPriceComparisonReport';
export { default as SupplierQualityReport } from './SupplierQualityReport';

// تقارير المبيعات
export { default as SalesByCustomerReport } from './SalesByCustomerReport';
export { default as SalesByProductReport } from './SalesByProductReport';
export { default as MonthlySalesReport } from './MonthlySalesReport';
export { default as ProfitabilityReport } from './ProfitabilityReport';
export { default as CustomerAgingReport } from './CustomerAgingReport';

// تقارير الدهان
export { default as PaintByCustomerReport } from './PaintByCustomerReport';
export { default as PaintByTypeReport } from './PaintByTypeReport';
export { default as MonthlyPaintReport } from './MonthlyPaintReport';
export { default as PaintProfitabilityReport } from './PaintProfitabilityReport';
export { default as PaintPerformanceReport } from './PaintPerformanceReport';
export { default as PaintQualityReport } from './PaintQualityReport';

// تقارير الإنتاج
export { default as ProductionOrdersReport } from './ProductionOrdersReport';
export { default as ProductionEfficiencyReport } from './ProductionEfficiencyReport';
export { default as ProductionCostsReport } from './ProductionCostsReport';
export { default as ProductionScheduleReport } from './ProductionScheduleReport';
export { default as ProductionQualityReport } from './ProductionQualityReport';
export { default as ProductionWorkersPerformanceReport } from './ProductionWorkersPerformanceReport';
export { default as ProductionMaterialsConsumptionReport } from './ProductionMaterialsConsumptionReport';
export { default as ProductionProfitabilityReport } from './ProductionProfitabilityReport';

// تقارير الموّفين
export { default as EmployeeAttendanceReport } from './EmployeeAttendanceReport';
export { default as EmployeePayrollReport } from './EmployeePayrollReport';
export { default as EmployeeLeavesReport } from './EmployeeLeavesReport';
export { default as EmployeePerformanceReport } from './EmployeePerformanceReport';
export { default as EmployeeOvertimeReport } from './EmployeeOvertimeReport';

// تصدير الأنواع
export type { ReportData, ReportFilters, ReportProps, ReportType } from './ReportBase';
