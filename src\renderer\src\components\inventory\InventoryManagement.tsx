import React, { useState, useEffect } from 'react'
import {
  Card, Table, Button, Modal, Form, Input, Select, InputNumber, Space,
  Typography, Row, Col, Statistic, Tag, Alert, App
} from 'antd'
import {
  EditOutlined, InboxOutlined, WarningOutlined, CheckCircleOutlined,
  ExclamationCircleOutlined, HomeOutlined, AppstoreOutlined, Bar<PERSON>hartOutlined,
  ArrowLeftOutlined, FileTextOutlined, DashboardOutlined, TruckOutlined
} from '@ant-design/icons'
import { logger as Logger } from './../../utils/logger'
import styled from 'styled-components'
import { Inventory, Warehouse, ApiResponse } from '../../types/global'
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig'
import WarehouseManagement from './WarehouseManagement'
import CategoryManagement from './CategoryManagement'
import ItemManagement from './ItemManagement'
import BarcodeManagement from './BarcodeManagement'
import InventoryMovements from './InventoryMovements'
import OpeningStockManagement from './OpeningStockManagement'
import {
  InventoryDetailedReport,
  InventoryMovementsReport,
  LowStockReport,
  MaterialConsumptionReport,
  InventoryAuditReport
} from '../reports'

const { Title } = Typography
const { Option } = Select

const StyledCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
`

const ActionButton = styled(Button)`
  margin-left: 8px;
`

const QuantityDisplay = styled.div<{ status: 'normal' | 'low' | 'high' | 'out' }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  color: ${props => {
    switch (props.status) {
      case 'low': return '#fa8c16'
      case 'high': return '#52c41a'
      case 'out': return '#ff4d4f'
      default: return '#1890ff'
    }
  }};
  background: ${props => {
    switch (props.status) {
      case 'low': return '#fff7e6'
      case 'high': return '#f6ffed'
      case 'out': return '#fff2f0'
      default: return '#e6f7ff'
    }
  }};
  border: 1px solid ${props => {
    switch (props.status) {
      case 'low': return '#ffd591'
      case 'high': return '#b7eb8f'
      case 'out': return '#ffccc7'
      default: return '#91d5ff'
    }
  }};
`

interface InventoryManagementProps {
  onBack?: () => void
  initialView?: ActiveView
}

type ActiveView = 'main' | 'warehouses' | 'categories' | 'items' | 'barcodes' | 'movements' | 'inventory' | 'reports' |
  'inventory-detailed' | 'inventory-movements' | 'low-stock' | 'material-consumption' | 'inventory-audit' | 'opening-stock'

const InventoryManagement: React.FC<InventoryManagementProps> = ({ onBack, initialView = 'main' }) => {
  Logger.info('InventoryManagement', '🚀 InventoryManagement component loaded with initialView:', initialView)
  const { message } = App.useApp()

  const [activeView, setActiveView] = useState<ActiveView>(initialView as ActiveView)
  const [inventory, setInventory] = useState<Inventory[]>([])
  const [warehouses, setWarehouses] = useState<Warehouse[]>([])
  const [loading, setLoading] = useState(false)

  const [modalVisible, setModalVisible] = useState(false)
  const [, setEditingInventory] = useState<Inventory | null>(null)
  const [selectedWarehouse, setSelectedWarehouse] = useState<number | null>(null)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `${range[0]}-${range[1]} من ${total} عنصر`
  })
  const [form] = Form.useForm()

  // تحديث activeView عندما يتغير initialView من الخارج
  useEffect(() => {
    setActiveView(initialView as ActiveView)
  }, [initialView])

  useEffect(() => {
    loadInventory()
    loadWarehouses()
  }, [])

  const loadInventory = async (page: number = 1, pageSize: number = 20, _warehouseFilter?: number) => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        // const filters = {
        //   page,
        //   pageSize,
        //   warehouse_id: warehouseFilter || selectedWarehouse
        // }

        const response = await window.electronAPI.getInventory()
        if (response && (response as any).success) {
          const inventoryData = (response as any).data || []
          setInventory(inventoryData)
          setPagination(prev => ({
            ...prev,
            current: page,
            pageSize,
            total: (response as any).total || inventoryData.length
          }))
          Logger.info('InventoryManagement', '✅ تم تحميل ${inventoryData.length} عنصر مخزون بنجاح (صفحة ${page})')
        } else if (Array.isArray(response)) {
          // للتوافق مع الإصدارات القديمة
          setInventory(response)
          setPagination(prev => ({
            ...prev,
            current: 1,
            total: response.length
          }))
          Logger.info('InventoryManagement', '✅ تم تحميل ${response.length} عنصر مخزون بنجاح')
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل بيانات المخزون'
          Logger.error('InventoryManagement', '❌ خطأ في تحميل المخزون:', errorMessage)
          message.error(errorMessage)
          setInventory([])
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('InventoryManagement', '❌ window.electronAPI غير متوفر')
        message.error(errorMessage)
        setInventory([])
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل المخزون'
      Logger.error('InventoryManagement', '❌ خطأ في تحميل المخزون:', error instanceof Error ? error : new Error(String(error)))
      message.error(`خطأ في تحميل المخزون: ${errorMessage}`)
      setInventory([])
    } finally {
      setLoading(false)
    }
  }

  const loadWarehouses = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getWarehouses()
        if (response && (response as any).success) {
          const warehousesData = (response as any).data || []
          setWarehouses(warehousesData.filter((w: Warehouse) => w.is_active))
          Logger.info('InventoryManagement', '✅ تم تحميل ${warehousesData.length} مخزن بنجاح')
        } else if (Array.isArray(response)) {
          // للتوافق مع الإصدارات القديمة
          setWarehouses(response.filter((w: Warehouse) => w.is_active))
          Logger.info('InventoryManagement', '✅ تم تحميل ${response.length} مخزن بنجاح')
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل المخازن'
          Logger.error('InventoryManagement', '❌ خطأ في تحميل المخازن:', errorMessage)
          message.error(errorMessage)
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('InventoryManagement', '❌ window.electronAPI غير متوفر')
        message.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل المخازن'
      Logger.error('InventoryManagement', '❌ خطأ في تحميل المخازن:', error instanceof Error ? error : new Error(String(error)))
      message.error(`خطأ في تحميل المخازن: ${errorMessage}`)
    }
  }

  const getQuantityStatus = (quantity: number, minQuantity?: number, maxQuantity?: number) => {
    if (quantity === 0) return 'out'
    if (minQuantity && quantity <= minQuantity) return 'low'
    if (maxQuantity && quantity >= maxQuantity) return 'high'
    return 'normal'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'low': return <WarningOutlined />
      case 'high': return <CheckCircleOutlined />
      case 'out': return <ExclamationCircleOutlined />
      default: return <InboxOutlined />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'low': return 'منخفض'
      case 'high': return 'مرتفع'
      case 'out': return 'نفد'
      default: return 'عادي'
    }
  }

  const handleEdit = (inventoryItem: Inventory) => {
    setEditingInventory(inventoryItem)
    form.setFieldsValue({
      item_id: inventoryItem.item_id,
      warehouse_id: inventoryItem.warehouse_id,
      quantity: inventoryItem.quantity,
      reserved_quantity: inventoryItem.reserved_quantity,
      location: inventoryItem.location,
      notes: inventoryItem.notes
    })
    setModalVisible(true)
  }

  const handleSubmit = async (values: any) => {
    try {
      if (window.electronAPI) {
        // تحويل المعرفات إلى أرقام صحيحة
        const inventoryData = {
          ...values,
          item_id: parseInt(String(values.item_id)),
          warehouse_id: parseInt(String(values.warehouse_id))
        }

        const response: ApiResponse = await window.electronAPI.updateInventory(inventoryData)
        if (response.success) {
          message.success('تم تحديث المخزون بنجاح')
          setModalVisible(false)
          form.resetFields()
          loadInventory()
        } else {
          message.error(response.message || 'فشل في تحديث المخزون')
        }
      }
    } catch (error) {
      Logger.error('InventoryManagement', 'خطأ في تحديث المخزون:', error instanceof Error ? error : new Error(String(error)))
      message.error('فشل في تحديث المخزون')
    }
  }



  const filteredInventory = selectedWarehouse 
    ? inventory.filter(inv => inv.warehouse_id === selectedWarehouse)
    : inventory

  const columns = [
    {
      title: 'كود الصنف',
      dataIndex: 'item_code',
      key: 'item_code',
      width: 120,
      render: (text: string) => (
        <Tag color="blue" style={{ fontFamily: 'monospace' }}>
          {text}
        </Tag>
      )
    },
    {
      title: 'اسم الصنف',
      dataIndex: 'item_name',
      key: 'item_name',
      render: (text: string, record: Inventory) => (
        <Space direction="vertical" size={0}>
          <span style={{ fontWeight: 'bold' }}>{text}</span>
          <span style={{ fontSize: '12px', color: '#666' }}>
            فئة: {record.category_name || 'غير محدد'}
          </span>
        </Space>
      )
    },
    {
      title: 'المخزن',
      dataIndex: 'warehouse_name',
      key: 'warehouse_name',
      render: (text: string) => (
        <Space>
          <HomeOutlined />
          {text}
        </Space>
      )
    },
    {
      title: 'الكمية المتاحة',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number, record: Inventory) => {
        const status = getQuantityStatus(quantity, record.min_quantity, record.max_quantity)
        return (
          <QuantityDisplay status={status}>
            {quantity} {record.unit}
          </QuantityDisplay>
        )
      }
    },
    {
      title: 'الكمية المحجوزة',
      dataIndex: 'reserved_quantity',
      key: 'reserved_quantity',
      render: (quantity: number, record: Inventory) => (
        <span style={{ color: '#fa8c16' }}>
          {quantity || 0} {record.unit}
        </span>
      )
    },
    {
      title: 'الكمية الفعلية المتاحة',
      key: 'available_quantity',
      render: (_: any, record: Inventory) => {
        const available = (record.quantity || 0) - (record.reserved_quantity || 0);
        return (
          <span style={{
            color: available > 0 ? '#52c41a' : available === 0 ? '#faad14' : '#f5222d',
            fontWeight: 'bold'
          }}>
            {available} {record.unit}
          </span>
        );
      }
    },
    {
      title: 'الحالة',
      key: 'status',
      render: (_: any, record: Inventory) => {
        const status = getQuantityStatus(record.quantity, record.min_quantity, record.max_quantity)
        return (
          <Tag 
            color={status === 'normal' ? 'blue' : status === 'low' ? 'orange' : status === 'high' ? 'green' : 'red'}
            icon={getStatusIcon(status)}
          >
            {getStatusText(status)}
          </Tag>
        )
      }
    },
    {
      title: 'قيمة المخزون (₪)',
      key: 'inventory_value',
      render: (_: any, record: Inventory) => {
        const value = (record.quantity || 0) * (record.cost_price || 0);
        return (
          <span style={{ color: '#722ed1', fontWeight: 'bold' }}>
            {value.toFixed(2)}
          </span>
        );
      }
    },
    {
      title: 'الموقع',
      dataIndex: 'location',
      key: 'location',
      render: (text: string) => text || '-'
    },
    {
      title: 'آخر تحديث (ميلادي)',
      dataIndex: 'last_updated',
      key: 'last_updated',
      width: 140,
      render: (date: string) => {
        if (!date) return '-';
        try {
          return DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE_TIME);
        } catch {
          return '-';
        }
      }
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: Inventory) => (
        <ActionButton
          type="primary"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
        >
          تعديل
        </ActionButton>
      )
    }
  ]

  // إحصائيات المخزون
  const totalItems = filteredInventory.length
  const lowStockItems = filteredInventory.filter(inv =>
    getQuantityStatus(inv.quantity, inv.min_quantity, inv.max_quantity) === 'low'
  ).length
  const outOfStockItems = filteredInventory.filter(inv => inv.quantity === 0).length
  // const totalQuantity = filteredInventory.reduce((sum, inv) => sum + (inv.quantity || 0), 0)
  // const totalReservedQuantity = filteredInventory.reduce((sum, inv) => sum + (inv.reserved_quantity || 0), 0)
  // const totalAvailableQuantity = totalQuantity - totalReservedQuantity
  const totalValue = filteredInventory.reduce((sum, inv) => sum + ((inv.quantity || 0) * (inv.cost_price || 0)), 0)

  const renderContent = () => {
    Logger.info('InventoryManagement', '🔄 Rendering content for activeView:', activeView)

    switch (activeView) {
      case 'warehouses':
        return <WarehouseManagement onBack={() => setActiveView('main')} />
      case 'categories':
        return <CategoryManagement onBack={() => setActiveView('main')} />
      case 'items':
        return <ItemManagement onBack={() => setActiveView('main')} />
      case 'barcodes':
        return <BarcodeManagement onBack={() => setActiveView('main')} />
      case 'movements':
        return <InventoryMovements onBack={() => setActiveView('main')} />
      case 'opening-stock':
        return <OpeningStockManagement onBack={() => setActiveView('main')} />
      case 'inventory':
        return renderInventoryView()
      case 'reports':
        return (
          <div style={{ padding: '24px' }}>
            <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <h2 style={{ margin: 0, color: '#1890ff' }}>📊 تقارير المخزون</h2>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => setActiveView('main')}
                size="large"
              >
                رجوع
              </Button>
            </div>
            <Row gutter={[24, 24]}>
              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ height: '180px' }}
                  styles={{ body: { padding: '20px', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' } }}
                  onClick={() => setActiveView('inventory-detailed')}
                >
                  <div>
                    <InboxOutlined style={{ fontSize: '32px', color: '#1890ff', marginBottom: '12px' }} />
                    <h3 style={{ margin: 0, fontSize: '16px' }}>تقرير المخزون التفصيلي</h3>
                    <p style={{ color: '#666', margin: '8px 0 0 0', fontSize: '14px' }}>
                      عرض شامل لجميع الأصناف والكميات في المخازن
                    </p>
                  </div>
                  <Button type="primary" size="small">عرض التقرير</Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ height: '180px' }}
                  styles={{ body: { padding: '20px', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' } }}
                  onClick={() => setActiveView('inventory-movements')}
                >
                  <div>
                    <TruckOutlined style={{ fontSize: '32px', color: '#52c41a', marginBottom: '12px' }} />
                    <h3 style={{ margin: 0, fontSize: '16px' }}>تقرير حركة المخزون</h3>
                    <p style={{ color: '#666', margin: '8px 0 0 0', fontSize: '14px' }}>
                      تتبع جميع حركات الإدخال والإخراج والتحويل
                    </p>
                  </div>
                  <Button type="primary" size="small">عرض التقرير</Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ height: '180px' }}
                  styles={{ body: { padding: '20px', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' } }}
                  onClick={() => setActiveView('low-stock')}
                >
                  <div>
                    <WarningOutlined style={{ fontSize: '32px', color: '#fa8c16', marginBottom: '12px' }} />
                    <h3 style={{ margin: 0, fontSize: '16px' }}>تقرير الأصناف المنخفضة</h3>
                    <p style={{ color: '#666', margin: '8px 0 0 0', fontSize: '14px' }}>
                      الأصناف التي وصلت للحد الأدنى أو نفدت
                    </p>
                  </div>
                  <Button type="primary" size="small">عرض التقرير</Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ height: '180px' }}
                  styles={{ body: { padding: '20px', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' } }}
                  onClick={() => setActiveView('material-consumption')}
                >
                  <div>
                    <DashboardOutlined style={{ fontSize: '32px', color: '#722ed1', marginBottom: '12px' }} />
                    <h3 style={{ margin: 0, fontSize: '16px' }}>تقرير استهلاك المواد</h3>
                    <p style={{ color: '#666', margin: '8px 0 0 0', fontSize: '14px' }}>
                      معدل استهلاك المواد وأنماط الاستخدام
                    </p>
                  </div>
                  <Button type="primary" size="small">عرض التقرير</Button>
                </Card>
              </Col>

              <Col xs={24} sm={12} lg={8}>
                <Card
                  hoverable
                  style={{ height: '180px' }}
                  styles={{ body: { padding: '20px', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' } }}
                  onClick={() => setActiveView('inventory-audit')}
                >
                  <div>
                    <CheckCircleOutlined style={{ fontSize: '32px', color: '#eb2f96', marginBottom: '12px' }} />
                    <h3 style={{ margin: 0, fontSize: '16px' }}>تقرير الجرد والمطابقة</h3>
                    <p style={{ color: '#666', margin: '8px 0 0 0', fontSize: '14px' }}>
                      مطابقة الكميات الفعلية مع النّام
                    </p>
                  </div>
                  <Button type="primary" size="small">عرض التقرير</Button>
                </Card>
              </Col>
            </Row>
          </div>
        )
      case 'inventory-detailed':
        return <InventoryDetailedReport />
      case 'inventory-movements':
        return <InventoryMovementsReport />
      case 'low-stock':
        return <LowStockReport />
      case 'material-consumption':
        return <MaterialConsumptionReport />
      case 'inventory-audit':
        return <InventoryAuditReport />
      default:
        return renderMainView()
    }
  }

  const renderMainView = () => (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h1 style={{ margin: 0, color: '#1890ff', fontSize: '28px' }}>
            <InboxOutlined style={{ marginLeft: '12px' }} />
            إدارة المخزون
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '16px' }}>
            نّام شامل لإدارة المخازن والأصناف والمخزون
          </p>
        </div>
        {onBack && (
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={onBack}
            size="large"
          >
            رجوع للقائمة الرئيسية
          </Button>
        )}
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '32px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="إجمالي الأصناف"
              value={totalItems}
              valueStyle={{ color: '#1890ff' }}
              prefix={<AppstoreOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="أصناف منخفضة"
              value={lowStockItems}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="أصناف نفدت"
              value={outOfStockItems}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="قيمة المخزون"
              value={totalValue}
              precision={2}
              suffix="₪"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* بطاقات الوحدات الفرعية */}
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12} lg={8}>
          <Card
            hoverable
            style={{
              height: '200px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              borderRadius: '12px'
            }}
            styles={{
              body: {
                padding: '24px',
                color: 'white',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between'
              }
            }}
            onClick={() => setActiveView('warehouses')}
          >
            <div>
              <HomeOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>إدارة المخازن</h3>
              <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                إضافة وتعديل المخازن ومواقعها
              </p>
            </div>
            <Button
              type="primary"
              ghost
              style={{ alignSelf: 'flex-start' }}
            >
              إدارة المخازن
            </Button>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <Card
            hoverable
            style={{
              height: '200px',
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              border: 'none',
              borderRadius: '12px'
            }}
            styles={{
              body: {
                padding: '24px',
                color: 'white',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between'
              }
            }}
            onClick={() => setActiveView('categories')}
          >
            <div>
              <AppstoreOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>إدارة الفئات</h3>
              <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                تصنيف الأصناف في فئات منّمة
              </p>
            </div>
            <Button
              type="primary"
              ghost
              style={{ alignSelf: 'flex-start' }}
            >
              إدارة الفئات
            </Button>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <Card
            hoverable
            style={{
              height: '200px',
              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
              border: 'none',
              borderRadius: '12px'
            }}
            styles={{
              body: {
                padding: '24px',
                color: 'white',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between'
              }
            }}
            onClick={() => setActiveView('items')}
          >
            <div>
              <FileTextOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>إدارة الأصناف</h3>
              <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                إضافة وتعديل الأصناف والمنتجات
              </p>
            </div>
            <Button
              type="primary"
              ghost
              style={{ alignSelf: 'flex-start' }}
            >
              إدارة الأصناف
            </Button>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <Card
            hoverable
            style={{
              height: '200px',
              background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
              border: 'none',
              borderRadius: '12px'
            }}
            styles={{
              body: {
                padding: '24px',
                color: 'white',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between'
              }
            }}
            onClick={() => setActiveView('barcodes')}
          >
            <div>
              <EditOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>إدارة الباركود</h3>
              <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                إنشاء وإدارة باركود الأصناف
              </p>
            </div>
            <Button
              type="primary"
              ghost
              style={{ alignSelf: 'flex-start' }}
            >
              إدارة الباركود
            </Button>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <Card
            hoverable
            style={{
              height: '200px',
              background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
              border: 'none',
              borderRadius: '12px'
            }}
            styles={{
              body: {
                padding: '24px',
                color: 'white',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between'
              }
            }}
            onClick={() => setActiveView('movements')}
          >
            <div>
              <TruckOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>حركة المخزون</h3>
              <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                تسجيل حركات الإدخال والإخراج
              </p>
            </div>
            <Button
              type="primary"
              ghost
              style={{ alignSelf: 'flex-start' }}
            >
              حركة المخزون
            </Button>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <Card
            hoverable
            style={{
              height: '200px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              borderRadius: '12px'
            }}
            styles={{
              body: {
                padding: '24px',
                color: 'white',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between'
              }
            }}
            onClick={() => setActiveView('inventory')}
          >
            <div>
              <InboxOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>إدارة المخزون</h3>
              <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                عرض وتعديل كميات المخزون
              </p>
            </div>
            <Button
              type="primary"
              ghost
              style={{ alignSelf: 'flex-start' }}
            >
              إدارة المخزون
            </Button>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <Card
            hoverable
            style={{
              height: '200px',
              background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
              border: 'none',
              borderRadius: '12px'
            }}
            styles={{
              body: {
                padding: '24px',
                color: 'white',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between'
              }
            }}
            onClick={() => setActiveView('opening-stock')}
          >
            <div>
              <FileTextOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>مخزون أول المدة</h3>
              <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                إدارة وتسجيل مخزون أول المدة
              </p>
            </div>
            <Button
              type="primary"
              ghost
              style={{ alignSelf: 'flex-start' }}
            >
              إدارة مخزون أول المدة
            </Button>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <Card
            hoverable
            style={{
              height: '200px',
              background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
              border: 'none',
              borderRadius: '12px'
            }}
            styles={{
              body: {
                padding: '24px',
                color: 'white',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between'
              }
            }}
            onClick={() => setActiveView('reports')}
          >
            <div>
              <BarChartOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <h3 style={{ color: 'white', margin: 0, fontSize: '20px' }}>تقارير المخزون</h3>
              <p style={{ color: 'rgba(255,255,255,0.8)', margin: '8px 0 0 0' }}>
                تقارير شاملة عن المخزون والحركات
              </p>
            </div>
            <Button
              type="primary"
              ghost
              style={{ alignSelf: 'flex-start' }}
            >
              عرض التقارير
            </Button>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <Card
            style={{
              height: '200px',
              background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
              border: 'none',
              borderRadius: '12px'
            }}
            styles={{
              body: {
                padding: '24px',
                color: '#333',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                textAlign: 'center'
              }
            }}
          >
            <h3 style={{ color: '#333', margin: 0, fontSize: '18px' }}>المزيد قريباً</h3>
            <p style={{ color: '#666', margin: '8px 0 0 0' }}>
              المزيد من الميزات والتقارير المتقدمة
            </p>
          </Card>
        </Col>
      </Row>

      {/* نصائح وإرشادات */}
      <Card
        title="نصائح لإدارة المخزون بفعالية"
        style={{ marginTop: '32px' }}
        styles={{ header: { background: '#f0f2f5', color: '#1890ff' } }}
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <h4>🎯 أفضل الممارسات:</h4>
            <ul style={{ paddingRight: '20px' }}>
              <li>تحديث كميات المخزون بانتّام</li>
              <li>مراجعة الحد الأدنى للأصناف</li>
              <li>تسجيل جميع حركات المخزون</li>
              <li>إجراء جرد دوري للمخازن</li>
            </ul>
          </Col>
          <Col xs={24} md={12}>
            <h4>📊 مؤشرات الأداء:</h4>
            <ul style={{ paddingRight: '20px' }}>
              <li>معدل دوران المخزون</li>
              <li>دقة المخزون والجرد</li>
              <li>نسبة الأصناف المنخفضة</li>
              <li>قيمة المخزون الإجمالية</li>
            </ul>
          </Col>
        </Row>
      </Card>
    </div>
  )

  const renderInventoryView = () => (
    <div>
      {/* تنبيهات */}
      {outOfStockItems > 0 && (
        <Alert
          message={`تحذير: ${outOfStockItems} صنف نفد من المخزون`}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}
      
      {lowStockItems > 0 && (
        <Alert
          message={`تنبيه: ${lowStockItems} صنف وصل للحد الأدنى`}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="إجمالي الأصناف"
              value={totalItems}
              prefix={<AppstoreOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </StyledCard>
        </Col>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="أصناف منخفضة"
              value={lowStockItems}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </StyledCard>
        </Col>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="أصناف نفدت"
              value={outOfStockItems}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </StyledCard>
        </Col>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="قيمة المخزون"
              value={totalValue}
              precision={2}
              suffix="₪"
              valueStyle={{ color: '#52c41a' }}
            />
          </StyledCard>
        </Col>
      </Row>

      {/* جدول المخزون */}
      <StyledCard
        title={
          <Space>
            <InboxOutlined />
            <Title level={4} style={{ margin: 0 }}>إدارة المخزون</Title>
          </Space>
        }
        extra={
          <Space>

            <Select
              placeholder="اختر المخزن"
              style={{ width: 200 }}
              allowClear
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
              }
              onChange={(warehouseId) => {
                setSelectedWarehouse(warehouseId)
                loadInventory(1, pagination.pageSize, warehouseId)
              }}
            >
              {warehouses.map(warehouse => (
                <Option key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                </Option>
              ))}
            </Select>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={filteredInventory}
          rowKey={(record) => record.id || `${record.item_id || 'unknown'}-${record.warehouse_id || 'unknown'}`}
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            ...pagination,
            onChange: (page, pageSize) => {
              loadInventory(page, pageSize || pagination.pageSize, selectedWarehouse || undefined)
            },
            onShowSizeChange: (_current: number, size: number) => {
              loadInventory(1, size, selectedWarehouse || undefined)
            }
          }}
        />
      </StyledCard>

      {/* نموذج تعديل المخزون */}
      <Modal
        title="تعديل بيانات المخزون"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item name="item_id" hidden>
            <Input />
          </Form.Item>
          
          <Form.Item name="warehouse_id" hidden>
            <Input />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="quantity"
                label="الكمية المتاحة"
                rules={[{ required: true, message: 'يرجى إدخال الكمية' }]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="الكمية المتاحة"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="reserved_quantity"
                label="الكمية المحجوزة"
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="الكمية المحجوزة"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="location"
            label="الموقع داخل المخزن"
          >
            <Input placeholder="مثال: رف A - مستوى 2" />
          </Form.Item>

          <Form.Item
            name="notes"
            label="ملاحّات"
          >
            <Input.TextArea placeholder="ملاحّات إضافية" rows={3} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                تحديث
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )

  return renderContent()
}

export default InventoryManagement
