import { SafeLogger as Logger } from './logger'
/**
 * نّام تنّيف الأمان
 * يزيل المعلومات الحساسة من console.log ويحسن الأمان
 */

interface SecurityConfig {
  enableConsoleFiltering: boolean
  sensitiveKeywords: string[]
  exemptKeys: string[]
  productionMode: boolean
  logLevel: 'debug' | 'info' | 'warn' | 'error' | 'none'
}

class SecurityCleaner {
  private config: SecurityConfig
  private originalConsole: {
    log: typeof console.log
    warn: typeof console.warn
    error: typeof console.error
    info: typeof console.info
    debug: typeof console.debug
  }

  constructor() {
    this.config = {
      enableConsoleFiltering: true,
      sensitiveKeywords: [
        'password',
        'token',
        'secret',
        'key',
        'auth',
        'credential',
        'session',
        'cookie',
        'كلمة المرور',
        'الرقم السري',
        'المفتاح',
        'التوكن'
      ],
      // قائمة المفاتيح المستثناة من التنظيف (مفاتيح مهمة للتطبيق)
      exemptKeys: [
        'authToken',
        'userData',
        'currentUser',
        'app_settings',
        'app-theme',
        'rememberedUsername',
        'rememberMe',
        'preserved_authToken',
        'preserved_userData'
      ],
      productionMode: process.env.NODE_ENV === 'production',
      logLevel: process.env.NODE_ENV === 'production' ? 'error' : 'debug'
    }

    // حفّ console الأصلي
    this.originalConsole = {
      log: console.log.bind(console),
      warn: console.warn.bind(console),
      error: console.error.bind(console),
      info: console.info.bind(console),
      debug: console.debug.bind(console)
    }

    this.initializeSecureConsole()
  }

  // تهيئة console آمن
  private initializeSecureConsole(): void {
    if (!this.config.enableConsoleFiltering) return

    // تصفية console.log
    console.log = (...args: any[]) => {
      if (this.shouldLog('debug')) {
        const filteredArgs = this.filterSensitiveData(args)
        this.originalConsole.log(...filteredArgs)
      }
    }

    // تصفية console.info
    console.info = (...args: any[]) => {
      if (this.shouldLog('info')) {
        const filteredArgs = this.filterSensitiveData(args)
        this.originalConsole.info(...filteredArgs)
      }
    }

    // تصفية console.warn
    console.warn = (...args: any[]) => {
      if (this.shouldLog('warn')) {
        const filteredArgs = this.filterSensitiveData(args)
        this.originalConsole.warn(...filteredArgs)
      }
    }

    // console.error يبقى كما هو (مهم للتشخيص)
    console.error = (...args: any[]) => {
      if (this.shouldLog('error')) {
        // تصفية خفيفة للأخطاء
        const filteredArgs = this.filterSensitiveData(args, true)
        this.originalConsole.error(...filteredArgs)
      }
    }

    console.debug = (...args: any[]) => {
      if (this.shouldLog('debug')) {
        const filteredArgs = this.filterSensitiveData(args)
        this.originalConsole.debug(...filteredArgs)
      }
    }
  }

  // فحص ما إذا كان يجب تسجيل هذا المستوى
  private shouldLog(level: SecurityConfig['logLevel']): boolean {
    const levels = ['debug', 'info', 'warn', 'error', 'none']
    const currentLevelIndex = levels.indexOf(this.config.logLevel)
    const requestedLevelIndex = levels.indexOf(level)
    
    return requestedLevelIndex >= currentLevelIndex
  }

  // تصفية البيانات الحساسة
  private filterSensitiveData(args: any[], isError = false): any[] {
    return args.map(arg => {
      if (typeof arg === 'string') {
        return this.filterSensitiveString(arg, isError)
      } else if (typeof arg === 'object' && arg !== null) {
        return this.filterSensitiveObject(arg, isError)
      }
      return arg
    })
  }

  // تصفية النصوص الحساسة
  private filterSensitiveString(str: string, isError = false): string {
    let filtered = str
    
    this.config.sensitiveKeywords.forEach(keyword => {
      // البحث عن patterns مثل "password: value" أو "كلمة المرور: value"
      const patterns = [
        new RegExp(`${keyword}\\s*[:=]\\s*[^\\s,}]+`, 'gi'),
        new RegExp(`"${keyword}"\\s*:\\s*"[^"]*"`, 'gi'),
        new RegExp(`'${keyword}'\\s*:\\s*'[^']*'`, 'gi')
      ]
      
      patterns.forEach(pattern => {
        if (isError) {
          // في الأخطاء، نخفي جزئياً فقط
          filtered = filtered.replace(pattern, (match) => {
            const parts = match.split(/[:=]/)
            if (parts.length > 1) {
              return `${parts[0]}:***`
            }
            return '***'
          })
        } else {
          // في الـ logs العادية، نخفي كاملاً
          filtered = filtered.replace(pattern, `${keyword}:***`)
        }
      })
    })
    
    return filtered
  }

  // تصفية الكائنات الحساسة
  private filterSensitiveObject(obj: any, isError = false): any {
    if (obj instanceof Error) {
      // لا نصفي رسائل الأخطاء كثيراً
      return obj
    }

    try {
      const filtered = { ...obj }
      
      Object.keys(filtered).forEach(key => {
        const lowerKey = key.toLowerCase()
        const isSensitive = this.config.sensitiveKeywords.some(keyword => 
          lowerKey.includes(keyword.toLowerCase())
        )
        
        if (isSensitive) {
          if (isError) {
            // في الأخطاء، نّهر نوع البيانات
            filtered[key] = `[${typeof filtered[key]}:***]`
          } else {
            filtered[key] = '***'
          }
        } else if (typeof filtered[key] === 'object' && filtered[key] !== null) {
          // تصفية متداخلة
          filtered[key] = this.filterSensitiveObject(filtered[key], isError)
        }
      })
      
      return filtered
    } catch {
      // في حالة فشل التصفية، نعيد نص آمن
      return '[Object: تعذر تصفيته]'
    }
  }

  // إنشاء logger آمن مخصص
  createSecureLogger(prefix: string) {
    return {
      debug: (...args: any[]) => {
        if (this.shouldLog('debug')) {
          const filteredArgs = this.filterSensitiveData(args)
          this.originalConsole.log(`[${prefix}]`, ...filteredArgs)
        }
      },
      info: (...args: any[]) => {
        if (this.shouldLog('info')) {
          const filteredArgs = this.filterSensitiveData(args)
          this.originalConsole.info(`[${prefix}]`, ...filteredArgs)
        }
      },
      warn: (...args: any[]) => {
        if (this.shouldLog('warn')) {
          const filteredArgs = this.filterSensitiveData(args)
          this.originalConsole.warn(`[${prefix}]`, ...filteredArgs)
        }
      },
      error: (...args: any[]) => {
        if (this.shouldLog('error')) {
          const filteredArgs = this.filterSensitiveData(args, true)
          this.originalConsole.error(`[${prefix}]`, ...filteredArgs)
        }
      }
    }
  }

  // تحديث الإعدادات
  updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    if (newConfig.enableConsoleFiltering !== undefined) {
      if (newConfig.enableConsoleFiltering) {
        this.initializeSecureConsole()
      } else {
        this.restoreOriginalConsole()
      }
    }
  }

  // استعادة console الأصلي
  restoreOriginalConsole(): void {
    console.log = this.originalConsole.log
    console.warn = this.originalConsole.warn
    console.error = this.originalConsole.error
    console.info = this.originalConsole.info
    console.debug = this.originalConsole.debug
  }

  // تنّيف البيانات الحساسة من localStorage
  cleanLocalStorage(): void {
    const sensitiveKeys: string[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        // التحقق من أن المفتاح ليس في قائمة الاستثناءات
        const isExempt = this.config.exemptKeys.includes(key)

        if (!isExempt) {
          const isSensitive = this.config.sensitiveKeywords.some(keyword =>
            key.toLowerCase().includes(keyword.toLowerCase())
          )
          if (isSensitive) {
            sensitiveKeys.push(key)
          }
        }
      }
    }

    sensitiveKeys.forEach(key => {
      Logger.warn('SecurityCleaner', `🧹 إزالة مفتاح حساس من localStorage: ${key}`)
      localStorage.removeItem(key)
    })

    // تسجيل المفاتيح المستثناة للمراجعة
    const exemptFound = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && this.config.exemptKeys.includes(key)) {
        exemptFound.push(key)
      }
    }

    if (exemptFound.length > 0) {
      Logger.info('SecurityCleaner', `🔒 تم الاحتفاظ بالمفاتيح المهمة: ${exemptFound.join(', ')}`)
    }
  }

  // تنّيف البيانات الحساسة من sessionStorage
  cleanSessionStorage(): void {
    const sensitiveKeys: string[] = []

    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i)
      if (key) {
        // التحقق من أن المفتاح ليس في قائمة الاستثناءات
        const isExempt = this.config.exemptKeys.includes(key)

        if (!isExempt) {
          const isSensitive = this.config.sensitiveKeywords.some(keyword =>
            key.toLowerCase().includes(keyword.toLowerCase())
          )
          if (isSensitive) {
            sensitiveKeys.push(key)
          }
        }
      }
    }

    sensitiveKeys.forEach(key => {
      Logger.warn('SecurityCleaner', `🧹 إزالة مفتاح حساس من sessionStorage: ${key}`)
      sessionStorage.removeItem(key)
    })

    // تسجيل المفاتيح المستثناة للمراجعة
    const exemptFound = []
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i)
      if (key && this.config.exemptKeys.includes(key)) {
        exemptFound.push(key)
      }
    }

    if (exemptFound.length > 0) {
      Logger.info('SecurityCleaner', `🔒 تم الاحتفاظ بالمفاتيح المهمة في sessionStorage: ${exemptFound.join(', ')}`)
    }
  }

  // الحصول على الإعدادات الحالية
  getConfig(): SecurityConfig {
    return { ...this.config }
  }
}

// إنشاء مثيل واحد
export const securityCleaner = new SecurityCleaner()

// إتاحة النّام عالمياً
if (typeof window !== 'undefined') {
  ;(window as any).securityCleaner = securityCleaner
}

export default SecurityCleaner
