# 🎨 النظام المركزي لإعدادات الطباعة

## 📋 نظرة عامة

تم إنشاء نظام مركزي شامل لإدارة جميع إعدادات الطباعة والألوان في التطبيق. هذا النظام يضمن:

- **مصدر واحد للحقيقة**: جميع إعدادات الطباعة تأتي من مكان واحد
- **تحديث تلقائي**: تغيير الإعدادات يؤثر على جميع نماذج الطباعة فوراً
- **تناسق الألوان**: لا توجد ألوان مدمجة في الكود
- **سهولة الصيانة**: إضافة أو تعديل الإعدادات من مكان واحد

## 🏗️ هيكل النظام

### 1. المكونات الأساسية

```
src/renderer/src/
├── hooks/
│   └── usePrintSettings.ts          # Hook مركزي للإعدادات
├── contexts/
│   └── PrintSettingsContext.tsx    # Context للإدارة العامة
└── services/
    └── MasterPrintService.ts        # خدمة الطباعة الرئيسية
```

### 2. الـ Hooks المتاحة

#### `usePrintSettings()`
Hook رئيسي لإدارة جميع إعدادات الطباعة:

```typescript
const { 
  settings,           // الإعدادات الحالية
  updateSettings,     // تحديث الإعدادات
  resetSettings,      // إعادة تعيين للافتراضية
  refreshSettings,    // تحديث من قاعدة البيانات
  isReady,           // هل الإعدادات جاهزة؟
  loading,           // حالة التحميل
  error              // رسائل الخطأ
} = usePrintSettings()
```

#### `usePrintColors()`
Hook مبسط للحصول على الألوان فقط:

```typescript
const {
  primaryColor,      // اللون الأساسي
  secondaryColor,    // اللون الثانوي (#fff3cd)
  borderColor,       // لون الحدود
  backgroundColor,   // لون الخلفية
  textColor,         // لون النص
  isReady           // هل الألوان جاهزة؟
} = usePrintColors()
```

#### `useDocumentPrintSettings(type)`
Hook للحصول على إعدادات مخصصة حسب نوع المستند:

```typescript
const { settings, updateSettings, isReady } = useDocumentPrintSettings('receipt')
// الأنواع المتاحة: 'invoice' | 'receipt' | 'report'
```

## 🎯 كيفية الاستخدام

### 1. في مكونات الطباعة

```typescript
import { useDocumentPrintSettings } from '../hooks/usePrintSettings'

const MyPrintButton = () => {
  const { settings, isReady } = useDocumentPrintSettings('invoice')
  
  if (!isReady) return <Loading />
  
  return (
    <UnifiedPrintButton
      data={printData}
      customSettings={settings}
    />
  )
}
```

### 2. تحديث الإعدادات

```typescript
import { usePrintSettings } from '../hooks/usePrintSettings'

const SettingsPanel = () => {
  const { updateSettings } = usePrintSettings()
  
  const handleColorChange = async () => {
    await updateSettings({
      secondaryColor: '#fff3cd',
      primaryColor: '#1890ff'
    })
  }
  
  return <Button onClick={handleColorChange}>تحديث الألوان</Button>
}
```

### 3. استخدام Context Provider

```typescript
import { PrintSettingsProvider } from '../contexts/PrintSettingsContext'

const App = () => (
  <PrintSettingsProvider>
    <YourAppComponents />
  </PrintSettingsProvider>
)
```

## 🔧 الإعدادات المتاحة

### الألوان
- `primaryColor`: اللون الأساسي (افتراضي: #1890ff)
- `secondaryColor`: اللون الثانوي (افتراضي: #fff3cd)
- `borderColor`: لون الحدود (افتراضي: #d9d9d9)
- `backgroundColor`: لون الخلفية (افتراضي: #ffffff)
- `textColor`: لون النص (افتراضي: #000000)

### إعدادات الصفحة
- `pageSize`: حجم الورق (A4, A5, Letter, A3)
- `orientation`: اتجاه الورق (portrait, landscape)
- `margins`: الهوامش {top, right, bottom, left}

### إعدادات النص
- `fontSize`: حجم الخط (افتراضي: 12)
- `fontFamily`: نوع الخط (افتراضي: Arial)
- `lineSpacing`: تباعد الأسطر (افتراضي: 1.5)

### إعدادات التنسيق المتقدمة
- `borderWidth`: سماكة الحدود (افتراضي: 1)
- `headerSize`: حجم العناوين (افتراضي: 18)
- `sectionSpacing`: المسافة بين الأقسام (افتراضي: 15)
- `tableWidth`: عرض الجدول (افتراضي: 100%)

### إعدادات المحتوى
- `showLogo`: إظهار الشعار
- `showHeader`: إظهار الرأس
- `showFooter`: إظهار التذييل
- `showSignature`: إظهار التوقيع
- `showTerms`: إظهار الشروط
- `showQR`: إظهار رمز QR

## 🧪 الاختبار والتحقق

### استخدام النظام
```typescript
import { usePrintSettings } from '../hooks/usePrintSettings'

// استخدام الإعدادات المركزية
const { settings, updateSettings, isReady } = usePrintSettings()
console.log('إعدادات الطباعة:', settings)

// تقرير مفصل
await printSystemReport()
```

## 🔄 التحديث التلقائي

النظام يدعم التحديث التلقائي عبر:

1. **Events**: إرسال أحداث مخصصة عند تغيير الإعدادات
2. **Context**: تحديث جميع المكونات المشتركة تلقائياً
3. **CSS Variables**: تطبيق الألوان على متغيرات CSS فوراً

```typescript
// الاستماع للتحديثات
useEffect(() => {
  const handleUpdate = (event) => {
    console.log('تم تحديث الإعدادات:', event.detail)
  }
  
  window.addEventListener('printSettingsUpdated', handleUpdate)
  return () => window.removeEventListener('printSettingsUpdated', handleUpdate)
}, [])
```

## 🎨 متغيرات CSS

النظام يطبق الألوان على متغيرات CSS تلقائياً:

```css
:root {
  --print-primary-color: #1890ff;
  --print-secondary-color: #fff3cd;
  --print-border-color: #d9d9d9;
  --print-background-color: #ffffff;
  --print-text-color: #000000;
}
```

## 📝 أفضل الممارسات

### 1. استخدم الـ Hooks دائماً
```typescript
// ✅ صحيح
const { settings } = usePrintSettings()

// ❌ خطأ - لا تستخدم ألوان مدمجة
const primaryColor = '#1890ff'
```

### 2. تحقق من الجاهزية
```typescript
const { settings, isReady } = usePrintSettings()

if (!isReady) {
  return <Loading />
}
```

### 3. استخدم الإعدادات المخصصة للمستندات
```typescript
// للإيصالات
const receiptSettings = useDocumentPrintSettings('receipt')

// للفواتير
const invoiceSettings = useDocumentPrintSettings('invoice')
```

## 🚀 المزايا

1. **مصدر واحد للحقيقة**: جميع الإعدادات من مكان واحد
2. **تحديث فوري**: تغيير الإعدادات يؤثر على جميع النماذج
3. **سهولة الصيانة**: إضافة إعدادات جديدة بسهولة
4. **تناسق مضمون**: لا توجد ألوان متضاربة
5. **أداء محسن**: تحميل الإعدادات مرة واحدة فقط
6. **اختبار شامل**: أدوات تحقق وفحص متقدمة

## 🔧 استكشاف الأخطاء

### مشكلة: الألوان لا تظهر
```typescript
// تحقق من الجاهزية
const { isReady } = usePrintSettings()
if (!isReady) {
  // انتظر حتى تصبح الإعدادات جاهزة
}

// تحقق من متغيرات CSS
const secondaryColor = getComputedStyle(document.documentElement)
  .getPropertyValue('--print-secondary-color')
```

### مشكلة: الإعدادات لا تحفظ
```typescript
// تأكد من وجود electronAPI
if (window.electronAPI) {
  await updateSettings(newSettings)
} else {
  console.error('electronAPI غير متاح')
}
```

## 📚 المراجع

- [MasterPrintService Documentation](./MASTER_PRINT_SERVICE.md)
- [Print Color Diagnostics](./PRINT_COLOR_DIAGNOSTICS.md)
- [Testing Guide](./TESTING_GUIDE.md)
