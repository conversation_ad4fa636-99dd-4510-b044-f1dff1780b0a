{"timestamp": "2025-09-23T09:06:41.932Z", "error": "Command failed: npm run build:renderer", "stack": "Error: Command failed: npm run build:renderer\n    at genericNodeError (node:internal/errors:983:15)\n    at wrappedFn (node:internal/errors:537:14)\n    at checkExecSyncError (node:child_process:882:11)\n    at execSync (node:child_process:954:15)\n    at UnifiedBuilder.buildCore (D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.11\\scripts\\unified-build.js:115:5)\n    at UnifiedBuilder.buildAll (D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.11\\scripts\\unified-build.js:36:18)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)", "buildConfig": {"skipNativeRebuild": true, "useSqlJs": true, "optimizeSize": true, "includeDebugInfo": false}, "environment": {"nodeVersion": "v22.16.0", "platform": "win32", "arch": "x64"}}