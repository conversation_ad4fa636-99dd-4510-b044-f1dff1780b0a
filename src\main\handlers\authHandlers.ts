import { ipcMain } from 'electron'
import { AuthService } from '../services'
import { HandlerLogger } from './logger'

let authService: AuthService

export function setAuthService(service: AuthService) {
  authService = service
}

function registerAuthHandlers(): void {
  // تسجيل الدخول
  ipcMain.handle('login', async (_event, username: string, password: string) => {
    try {
      const result = await authService.login(username, password)

      // تعيين المستخدم الحالي عند نجاح تسجيل الدخول
      if (result.success && result.user) {
        AuthService.setCurrentUser(result.user)
      }

      return result
    } catch (error) {
      HandlerLogger.error('AUTH', 'خطأ في تسجيل الدخول:', error)
      return { success: false, message: 'حدث خطأ في تسجيل الدخول' }
    }
  })

  // معالج الحصول على المستخدم الحالي
  ipcMain.handle('get-current-user', async () => {
    try {
      const user = AuthService.getCurrentUser()
      return { success: true, data: user }
    } catch (error) {
      HandlerLogger.error('AUTH', 'خطأ في الحصول على المستخدم الحالي:', error)
      return { success: false, message: 'حدث خطأ في الحصول على المستخدم الحالي' }
    }
  })

  // تسجيل الخروج
  ipcMain.handle('logout', async (_event, token: string) => {
    try {
      return await authService.logout(token)
    } catch (error) {
      HandlerLogger.error('AUTH', 'خطأ في تسجيل الخروج:', error)
      return { success: false, message: 'حدث خطأ في تسجيل الخروج' }
    }
  })

  // التحقق من الجلسة
  ipcMain.handle('verify-session', async (_event, token: string) => {
    try {
      return await authService.verifySession(token)
    } catch (error) {
      HandlerLogger.error('AUTH', 'خطأ في التحقق من الجلسة:', error)
      return { success: false, message: 'حدث خطأ في التحقق من الجلسة' }
    }
  })

  // إعادة تعيين حالة المستخدم وإلغاء الحّر
  ipcMain.handle('reset-user-login-status', async (_event, username: string) => {
    try {
      return await authService.resetUserLoginStatus(username)
    } catch (error) {
      HandlerLogger.error('AUTH', 'خطأ في إعادة تعيين حالة المستخدم:', error)
      return { success: false, message: 'حدث خطأ في إعادة تعيين الحالة' }
    }
  })

  // إعادة تعيين جميع محاولات تسجيل الدخول
  ipcMain.handle('reset-all-login-attempts', async () => {
    try {
      return await authService.resetAllLoginAttempts()
    } catch (error) {
      HandlerLogger.error('AUTH', 'خطأ في إعادة تعيين جميع محاولات تسجيل الدخول:', error)
      return { success: false, message: 'حدث خطأ في إعادة تعيين جميع محاولات تسجيل الدخول' }
    }
  })

  // الحصول على معلومات النّام
  ipcMain.handle('get-system-info', async () => {
    try {
      return await authService.getSystemInfo()
    } catch (error) {
      HandlerLogger.error('AUTH', 'خطأ في جلب معلومات النّام:', error)
      return { success: false, message: 'حدث خطأ في جلب معلومات النّام' }
    }
  })
}

export { registerAuthHandlers }
