const fs = require('fs');
const path = require('path');
const initSqlJs = require('sql.js');

async function deepProductionImageAnalysis() {
  try {
    console.log('🔍 فحص عميق لنظام الصور في قسم أمر الإنتاج...\n');
    
    // فحص جميع قواعد البيانات المحتملة
    const dbPaths = [
      path.join(process.env.APPDATA, 'accounting-production-app', 'accounting.db'),
      path.join(process.env.APPDATA, 'accounting-production-app-final', 'accounting.db'),
      path.join(process.env.APPDATA, 'accounting-app-clean5 -fares99.9.9.12', 'accounting.db')
    ];
    
    console.log('📍 قواعد البيانات المحتملة:');
    dbPaths.forEach((dbPath, index) => {
      const exists = fs.existsSync(dbPath);
      console.log(`   ${index + 1}. ${dbPath} - ${exists ? '✅ موجود' : '❌ غير موجود'}`);
    });
    
    // تهيئة SQL.js
    const SQL = await initSqlJs({
      locateFile: file => path.join(__dirname, 'node_modules', 'sql.js', 'dist', file)
    });
    
    for (const dbPath of dbPaths) {
      if (!fs.existsSync(dbPath)) continue;
      
      console.log(`\n🔍 فحص قاعدة البيانات: ${path.basename(path.dirname(dbPath))}`);
      console.log('='.repeat(80));
      
      const filebuffer = fs.readFileSync(dbPath);
      const db = new SQL.Database(filebuffer);
      
      // 1. فحص جداول الإنتاج
      console.log('\n📋 جداول الإنتاج:');
      const productionTables = db.exec(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name LIKE '%production%'
        ORDER BY name
      `);
      
      if (productionTables[0]?.values) {
        productionTables[0].values.forEach(table => {
          const tableName = table[0];
          try {
            const countResult = db.exec(`SELECT COUNT(*) as count FROM ${tableName}`);
            const count = countResult[0]?.values[0]?.[0] || 0;
            console.log(`   📊 ${tableName}: ${count} سجل`);
            
            // فحص تفصيلي لجدول أوامر الإنتاج
            if (tableName === 'production_orders' && count > 0) {
              console.log('     🔍 تفاصيل أوامر الإنتاج:');
              const ordersResult = db.exec(`
                SELECT id, order_number, status, item_id, quantity, customer_name, order_date
                FROM production_orders 
                ORDER BY created_at DESC 
                LIMIT 5
              `);
              
              if (ordersResult[0]?.values) {
                ordersResult[0].values.forEach((order, index) => {
                  console.log(`       ${index + 1}. أمر ${order[1]} - ${order[2]} - كمية: ${order[4]} - عميل: ${order[5] || 'غير محدد'}`);
                });
              }
            }
          } catch (error) {
            console.log(`   ❌ خطأ في قراءة ${tableName}: ${error.message}`);
          }
        });
      } else {
        console.log('   ❌ لا توجد جداول إنتاج');
      }
      
      // 2. فحص جداول الصور
      console.log('\n🖼️ جداول الصور:');
      const imageTables = db.exec(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND (name LIKE '%image%' OR name LIKE '%unified%')
        ORDER BY name
      `);
      
      if (imageTables[0]?.values) {
        imageTables[0].values.forEach(table => {
          const tableName = table[0];
          try {
            const countResult = db.exec(`SELECT COUNT(*) as count FROM ${tableName}`);
            const count = countResult[0]?.values[0]?.[0] || 0;
            console.log(`   📊 ${tableName}: ${count} صورة`);
            
            // فحص تفصيلي لجدول صور أوامر الإنتاج
            if (tableName === 'production_order_images' && count > 0) {
              console.log('     🔍 تفاصيل صور أوامر الإنتاج:');
              const imagesResult = db.exec(`
                SELECT id, order_id, image_name, category, file_size, created_at
                FROM production_order_images 
                ORDER BY created_at DESC 
                LIMIT 5
              `);
              
              if (imagesResult[0]?.values) {
                imagesResult[0].values.forEach((image, index) => {
                  const sizeKB = (image[4] / 1024).toFixed(2);
                  console.log(`       ${index + 1}. ${image[2]} - أمر: ${image[1]} - فئة: ${image[3]} - حجم: ${sizeKB} KB`);
                });
              }
            }
            
            // فحص جدول الصور الموحد
            if (tableName === 'unified_images' && count > 0) {
              console.log('     🔍 تفاصيل الصور الموحدة:');
              const unifiedResult = db.exec(`
                SELECT id, name, context_type, context_id, category, size, uploaded_at
                FROM unified_images 
                WHERE context_type = 'production_order' OR category = 'production'
                ORDER BY uploaded_at DESC 
                LIMIT 5
              `);
              
              if (unifiedResult[0]?.values) {
                unifiedResult[0].values.forEach((image, index) => {
                  const sizeKB = (image[5] / 1024).toFixed(2);
                  console.log(`       ${index + 1}. ${image[1]} - سياق: ${image[2]}/${image[3]} - فئة: ${image[4]} - حجم: ${sizeKB} KB`);
                });
              }
            }
          } catch (error) {
            console.log(`   ❌ خطأ في قراءة ${tableName}: ${error.message}`);
          }
        });
      } else {
        console.log('   ❌ لا توجد جداول صور');
      }
      
      // 3. فحص العلاقة بين أوامر الإنتاج والصور
      console.log('\n🔗 فحص العلاقة بين أوامر الإنتاج والصور:');
      
      // التحقق من وجود أوامر إنتاج
      const ordersExist = db.exec("SELECT name FROM sqlite_master WHERE type='table' AND name='production_orders'");
      const imagesExist = db.exec("SELECT name FROM sqlite_master WHERE type='table' AND name='production_order_images'");
      
      if (ordersExist[0]?.values && imagesExist[0]?.values) {
        try {
          const relationResult = db.exec(`
            SELECT 
              po.id as order_id,
              po.order_number,
              COUNT(poi.id) as image_count,
              GROUP_CONCAT(poi.category) as categories
            FROM production_orders po
            LEFT JOIN production_order_images poi ON po.id = poi.order_id
            GROUP BY po.id, po.order_number
            ORDER BY image_count DESC
            LIMIT 10
          `);
          
          if (relationResult[0]?.values && relationResult[0].values.length > 0) {
            console.log('   📊 أوامر الإنتاج وصورها:');
            relationResult[0].values.forEach((row, index) => {
              const categories = row[3] ? row[3].split(',').join(', ') : 'لا توجد';
              console.log(`     ${index + 1}. أمر ${row[1]} (ID: ${row[0]}) - ${row[2]} صورة - فئات: ${categories}`);
            });
          } else {
            console.log('   ⚠️ لا توجد علاقات بين أوامر الإنتاج والصور');
          }
        } catch (error) {
          console.log(`   ❌ خطأ في فحص العلاقات: ${error.message}`);
        }
      } else {
        console.log('   ❌ جداول أوامر الإنتاج أو الصور غير موجودة');
      }
      
      // 4. فحص مسارات الصور
      console.log('\n📁 فحص مسارات الصور:');
      
      if (imagesExist[0]?.values) {
        try {
          const pathsResult = db.exec(`
            SELECT 
              image_path,
              COUNT(*) as count,
              AVG(file_size) as avg_size
            FROM production_order_images
            GROUP BY CASE 
              WHEN image_path LIKE 'data:%' THEN 'data_url'
              WHEN image_path LIKE 'file:%' THEN 'file_path'
              WHEN image_path LIKE 'http%' THEN 'http_url'
              ELSE 'other'
            END
          `);
          
          if (pathsResult[0]?.values) {
            console.log('   📊 أنواع مسارات الصور:');
            pathsResult[0].values.forEach((row, index) => {
              const avgSizeKB = (row[2] / 1024).toFixed(2);
              const pathType = row[0].includes('data:') ? 'Data URL (مدمجة)' : 
                             row[0].includes('file:') ? 'مسار ملف' :
                             row[0].includes('http') ? 'رابط ويب' : 'أخرى';
              console.log(`     ${index + 1}. ${pathType} - ${row[1]} صورة - متوسط الحجم: ${avgSizeKB} KB`);
            });
          }
        } catch (error) {
          console.log(`   ❌ خطأ في فحص المسارات: ${error.message}`);
        }
      }
      
      // 5. فحص صحة مسارات الصور الفعلية
      console.log('\n🔍 فحص صحة مسارات الصور:');
      
      if (imagesExist[0]?.values) {
        try {
          const allImagesResult = db.exec(`
            SELECT id, image_name, image_path, file_size
            FROM production_order_images
            LIMIT 10
          `);
          
          if (allImagesResult[0]?.values) {
            console.log('   📊 حالة الصور:');
            allImagesResult[0].values.forEach((image, index) => {
              const imagePath = image[2];
              let status = '';
              
              if (imagePath.startsWith('data:')) {
                status = '✅ Data URL (مدمجة)';
              } else if (imagePath.startsWith('http')) {
                status = '🌐 رابط ويب';
              } else if (fs.existsSync(imagePath)) {
                status = '✅ ملف موجود';
              } else {
                status = '❌ ملف مفقود';
              }
              
              const sizeKB = (image[3] / 1024).toFixed(2);
              console.log(`     ${index + 1}. ${image[1]} - ${status} - ${sizeKB} KB`);
            });
          }
        } catch (error) {
          console.log(`   ❌ خطأ في فحص صحة الصور: ${error.message}`);
        }
      }
      
      db.close();
    }
    
    // 6. فحص ملفات الصور في النظام
    console.log('\n📁 فحص ملفات الصور في النظام:');
    
    const imageFolders = [
      path.join(process.env.APPDATA, 'accounting-production-app'),
      path.join(process.env.APPDATA, 'accounting-production-app-final'),
      path.join(process.env.APPDATA, 'accounting-app-clean5 -fares99.9.9.12')
    ];
    
    imageFolders.forEach((folder, index) => {
      console.log(`\n   ${index + 1}. فحص مجلد: ${folder}`);
      
      if (!fs.existsSync(folder)) {
        console.log('     ❌ المجلد غير موجود');
        return;
      }
      
      try {
        const findImages = (dir, depth = 0) => {
          if (depth > 3) return []; // تجنب العمق الزائد
          
          const items = fs.readdirSync(dir);
          let images = [];
          
          for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
              images = images.concat(findImages(fullPath, depth + 1));
            } else if (/\.(png|jpg|jpeg|gif|bmp|webp|svg)$/i.test(item)) {
              images.push({
                path: fullPath,
                name: item,
                size: stat.size,
                modified: stat.mtime
              });
            }
          }
          
          return images;
        };
        
        const images = findImages(folder);
        
        if (images.length > 0) {
          console.log(`     ✅ تم العثور على ${images.length} صورة:`);
          images.slice(0, 5).forEach((img, idx) => {
            const sizeKB = (img.size / 1024).toFixed(2);
            const relativePath = path.relative(folder, img.path);
            console.log(`       ${idx + 1}. ${relativePath} - ${sizeKB} KB - ${img.modified.toLocaleDateString()}`);
          });
          
          if (images.length > 5) {
            console.log(`       ... و ${images.length - 5} صورة أخرى`);
          }
        } else {
          console.log('     ❌ لا توجد صور');
        }
      } catch (error) {
        console.log(`     ❌ خطأ في فحص المجلد: ${error.message}`);
      }
    });
    
    console.log('\n' + '='.repeat(80));
    console.log('📋 ملخص الفحص العميق لقسم أمر الإنتاج:');
    console.log('='.repeat(80));
    console.log('✅ تم فحص جميع قواعد البيانات المحتملة');
    console.log('✅ تم فحص جداول الإنتاج والصور');
    console.log('✅ تم فحص العلاقات بين أوامر الإنتاج والصور');
    console.log('✅ تم فحص مسارات وصحة الصور');
    console.log('✅ تم فحص ملفات الصور في النظام');
    
    console.log('\n📝 التوصيات للخطوة التالية:');
    console.log('   1. تحليل النتائج لتحديد المشاكل الدقيقة');
    console.log('   2. إنشاء خطة إصلاح شاملة لكل نظام الصور');
    console.log('   3. تطبيق الإصلاحات بشكل تدريجي');
    console.log('   4. اختبار كل جزء بعد الإصلاح');
    
  } catch (error) {
    console.error('❌ خطأ في الفحص العميق:', error);
  }
}

// تشغيل الفحص العميق
deepProductionImageAnalysis();
