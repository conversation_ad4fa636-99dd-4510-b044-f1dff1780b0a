﻿import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Row,
  Col,
  Typography,
  Statistic,
  Tag,
  Tooltip,

  Divider,
  App} from 'antd'

import { SafeLogger as Logger } from '../../utils/logger'
import {
  BankOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  TeamOutlined,
  UserOutlined,
  DollarOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import UnifiedPrintButton from '../common/UnifiedPrintButton'

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input

interface Department {
  id: number
  code: string
  name: string
  description?: string
  manager_id?: number
  manager_name?: string
  location?: string
  budget: number
  working_hours_start: string
  working_hours_end: string
  employee_count: number
  is_active: boolean
  created_at: string
  updated_at: string
}

interface User {
  id: number
  full_name: string
  role: string
}

const DepartmentManagement: React.FC = () => {
  const { message: messageApi } = App.useApp()
  const [departments, setDepartments] = useState<Department[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingDepartment, setEditingDepartment] = useState<Department | null>(null)
  const [form] = Form.useForm()

  useEffect(() => {
    fetchDepartments()
    fetchUsers()
  }, [])

  const fetchDepartments = async () => {
    setLoading(true)
    try {
      Logger.info('DepartmentManagement', 'ًں”„ جارٍ جلب بيانات الأقسام...')

      if (!window.electronAPI) {
        Logger.error('DepartmentManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('DepartmentManagement', 'âڑ ï¸ڈ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للأقسام
        const mockDepartments = [
          {
            id: 1,
            code: 'HR001',
            name: 'قسم الموارد البشرية',
            description: 'إدارة شؤوْ الموظفيْ والتوٍّف والتدرٍب',
            manager_name: 'أحمد محمد علٍ',
            location: 'الطابق الأول - مكتب 101',
            budget: 50000.0,
            working_hours_start: '08:00',
            working_hours_end: '17:00',
            employee_count: 5,
            is_active: true
          },
          {
            id: 2,
            code: 'FIN001',
            name: 'قسم المالية والمحاسبة',
            description: 'إدارة الحسابات والمٍزاٍْات والتقارٍر المالية',
            manager_name: 'فاطمة أحمد السالم',
            location: 'الطابق الأول - مكتب 102',
            budget: 75000.0,
            working_hours_start: '08:30',
            working_hours_end: '17:30',
            employee_count: 8,
            is_active: true
          },
          {
            id: 3,
            code: 'SALES001',
            name: 'قسم المبيعات',
            description: 'إدارة المبيعات وخدمة العملاط، والتسوٍق',
            manager_name: 'خالد عبدالله الزهراٍْ',
            location: 'الطابق الأرضٍ - قاعة المبيعات',
            budget: 100000.0,
            working_hours_start: '09:00',
            working_hours_end: '18:00',
            employee_count: 12,
            is_active: true
          }
        ]

        setDepartments(mockDepartments as any[])
        Logger.info('DepartmentManagement', '✅ تم تحميل ${mockDepartments.length} قسم وهمي للمتصفح')
        return
      }

      const result = await window.electronAPI.getEmployeeDepartments()
      Logger.info('DepartmentManagement', 'ًں“ٹ ْتٍجة جلب الأقسام:', result)

      if (result && result.success) {
        setDepartments(result.data || [])
        Logger.info('DepartmentManagement', '✅ تم جلب ${result.data?.length || 0} قسم بنجاح')
      } else {
        Logger.error('DepartmentManagement', '❌ فشل في جلب الأقسام:', result?.message)
        messageApi.error(result?.message || 'فشل في جلب بيانات الأقسام')
        setDepartments([])
      }
    } catch (_error) {
      Logger.error('DepartmentManagement', 'خطأ في جلب بيانات الأقسام:', _error)
      messageApi.error('فشل في جلب بيانات الأقسام - تحقق من الاتصال')
      setDepartments([])
    } finally {
      setLoading(false)
    }
  }

  const fetchUsers = async () => {
    try {
      if (!window.electronAPI) {
        Logger.info('DepartmentManagement', 'âڑ ï¸ڈ استخدام بيانات وهمية للمستخدمٍْ')

        // بيانات وهمية للمستخدمٍْ
        const mockUsers = [
          { id: 1, full_name: 'أحمد محمد علٍ', username: 'admin' },
          { id: 2, full_name: 'فاطمة أحمد السالم', username: 'fatima' },
          { id: 3, full_name: 'خالد عبدالله الزهراٍْ', username: 'khalid' }
        ]

        setUsers(mockUsers as any[])
        return
      }

      const result = await window.electronAPI.getUsers()
      if (result.success) {
        setUsers(result.data)
      }
    } catch (_error) {
      Logger.error('DepartmentManagement', 'فشل في جلب المستخدمين:', _error)
    }
  }

  const handleCreateDepartment = async () => {
    setEditingDepartment(null)
    form.resetFields()
    // توليد كود تلقائي للقسم الجديد
    await generateDepartmentCode()
    setModalVisible(true)
  }

  // دالة توليد كود القسم التلقائي
  const generateDepartmentCode = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generateDepartmentCode()
        if (response.success && response.data) {
          form.setFieldsValue({ code: response.data.code })
          messageApi.success('تم إنشاء كود القسم تلقائياً')
        } else {
          messageApi.error(response.message || 'فشل في إنشاء كود القسم')
        }
      }
    } catch (_error) {
      Logger.error('DepartmentManagement', 'خطأ في إنشاء كود القسم:', _error)
      messageApi.error('فشل في إنشاء كود القسم')
    }
  }

  const handleEditDepartment = (department: Department) => {
    setEditingDepartment(department)
    form.setFieldsValue(department)
    setModalVisible(true)
  }

  const handleDeleteDepartment = async (department: Department) => {
    Modal.confirm({
      title: 'تأكيد الحذف',
      content: `هل أنت متأكد من حذف القسم "${department.name}"؟`,
      okText: 'حذف',
      cancelText: 'إلغاء',
      okType: 'danger',
      onOk: async () => {
        try {
          const result = await window.electronAPI.deleteEmployeeDepartment(department.id)
          if (result.success) {
            messageApi.success('تم حذف القسم بنجاح')
            fetchDepartments()
          } else {
            messageApi.error(result.message)
          }
        } catch (_error) {
          messageApi.error('فشل في حذف القسم')
        }
      }
    })
  }

  const handleSubmit = async (values: any) => {
    try {
      let result: any
      if (editingDepartment) {
        result = await window.electronAPI.updateEmployeeDepartment(editingDepartment.id, values)
      } else {
        result = await window.electronAPI.createEmployeeDepartment(values)
      }

      if (result.success) {
        messageApi.success(editingDepartment ? 'تم تحديث القسم بنجاح' : 'تم إضافة القسم بنجاح')
        setModalVisible(false)
        form.resetFields()
        fetchDepartments()
      } else {
        messageApi.error(result.message)
      }
    } catch (_error) {
      messageApi.error('فشل في حفظ بيانات القسم')
    }
  }

  const columns: ColumnsType<Department> = [
    {
      title: 'كود القسم',
      dataIndex: 'code',
      key: 'code',
      width: 120
    },
    {
      title: 'اسم القسم',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.description}
          </Text>
        </div>
      )
    },
    {
      title: 'المدير',
      dataIndex: 'manager_name',
      key: 'manager_name',
      width: 150,
      render: (text) => text ? (
        <Space>
          <UserOutlined />
          {text}
        </Space>
      ) : '-'
    },
    {
      title: 'الموقع',
      dataIndex: 'location',
      key: 'location',
      width: 200,
      render: (text) => text ? (
        <Space>
          <EnvironmentOutlined />
          {text}
        </Space>
      ) : '-'
    },
    {
      title: 'عدد الموظفيْ',
      dataIndex: 'employee_count',
      key: 'employee_count',
      width: 120,
      render: (count) => (
        <Tag color="blue" icon={<TeamOutlined />}>
          {count}
        </Tag>
      )
    },
    {
      title: 'المٍزاٍْة',
      dataIndex: 'budget',
      key: 'budget',
      width: 150,
      render: (budget) => (
        <Space>
          <DollarOutlined />
          {budget?.toLocaleString() || '0'} ₪
        </Space>
      )
    },
    {
      title: 'ساعات العمل',
      key: 'working_hours',
      width: 150,
      render: (_, record) => (
        <Space>
          <ClockCircleOutlined />
          {record.working_hours_start} - {record.working_hours_end}
        </Space>
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'ْشط' : 'غير ْشط'}
        </Tag>
      )
    },
    {
      title: 'الإجراط،ات',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Tooltip title="تعديل">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditDepartment(record)}
            />
          </Tooltip>
          <Tooltip title="حذف">
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteDepartment(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const totalEmployees = departments.reduce((sum, dept) => sum + dept.employee_count, 0)
  const totalBudget = departments.reduce((sum, dept) => sum + dept.budget, 0)
  const activeDepartments = departments.filter(dept => dept.is_active).length

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <BankOutlined /> إدارة الأقسام
      </Title>
      
      {/* إحصائيات سرٍعة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="إجمالي الأقسام"
              value={departments.length}
              prefix={<BankOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="الأقسام الْشطة"
              value={activeDepartments}
              prefix={<BankOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="إجمالي الموظفيْ"
              value={totalEmployees}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="إجمالي المٍزاٍْات"
              value={totalBudget}
              prefix={<DollarOutlined />}
              suffix="₪"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* جدول الأقسام */}
      <Card
        title={
          <Space>
            <BankOutlined />
            <span>قائمة الأقسام</span>
          </Space>
        }
        extra={
          <Space>

            <UnifiedPrintButton
              type="report"
              subType="work"
              data={{
                title: 'تقرير الأقسام',
                subtitle: `إجمالي ${departments.length} قسم`,
                headers: ['كود القسم', 'اسم القسم', 'الوصف', 'المدير', 'الموقع', 'الميزانية', 'بداية العمل', 'نهاية العمل', 'عدد الموظفين'],
                rows: departments.map(dept => [
                  dept.code,
                  dept.name,
                  dept.description || 'غير محدد',
                  dept.manager_name || 'غير محدد',
                  dept.location || 'غير محدد',
                  dept.budget ? `${dept.budget.toLocaleString()} ₪` : 'غير محدد',
                  dept.working_hours_start || 'غير محدد',
                  dept.working_hours_end || 'غير محدد',
                  dept.employee_count || 0
                ]),
                summary: {
                  totalDepartments: departments.length,
                  totalBudget: departments.reduce((sum, dept) => sum + (dept.budget || 0), 0),
                  totalEmployees: departments.reduce((sum, dept) => sum + (dept.employee_count || 0), 0)
                }
              }}
              title="تقرير الأقسام"
              buttonText="طباعة"
              size="middle"
              showDropdown={true}
              showExportOptions={true}
              _documentId="departments-list"
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateDepartment}
            >
              إضافة قسم
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={departments}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} قسم`
          }}
        />
      </Card>

      {/* نافذة إضافة/تعديل القسم */}
      <Modal
        title={editingDepartment ? 'تعديل القسم' : 'إضافة قسم جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={800}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            is_active: true,
            budget: 0,
            working_hours_start: '08:00',
            working_hours_end: '17:00'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="كود القسم"
                rules={[{ required: true, message: 'ٍرجى إدخال كود القسم' }]}
              >
                <Input
                  placeholder="مثال: DEPT001"
                  addonAfter={
                    <Button
                      type="link"
                      size="small"
                      onClick={generateDepartmentCode}
                      title="تولٍد كود تلقائٍ"
                    >
                      تولٍد
                    </Button>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="اسم القسم"
                rules={[{ required: true, message: 'ٍرجى إدخال اسم القسم' }]}
              >
                <Input placeholder="مثال: قسم الموارد البشرية" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="description"
                label="وصف القسم"
              >
                <TextArea rows={3} placeholder="وصف مختصر عن القسم ومهامه" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="manager_id"
                label="مدير القسم"
              >
                <Select placeholder="اختر مدير القسم">
                  {users.filter(user => user.role === 'admin' || user.role === 'manager').map(user => (
                    <Option key={user.id} value={user.id}>
                      {user.full_name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="location"
                label="موقع القسم"
              >
                <Input placeholder="مثال: الطابق الثاني - مكتب 201" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="budget"
                label="مٍزاٍْة القسم (₪)"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  formatter={value => (value + '').replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => (value || '').replace(/\$\s?|(,*)/g, '') as any}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="working_hours_start"
                label="بداٍة الدوام"
                rules={[{ required: true, message: 'ٍرجى إدخال بداٍة الدوام' }]}
              >
                <Input type="time" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="working_hours_end"
                label="ْهاٍة الدوام"
                rules={[{ required: true, message: 'ٍرجى إدخال ْهاٍة الدوام' }]}
              >
                <Input type="time" />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <Row justify="end">
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {editingDepartment ? 'تحديث' : 'إضافة'}
              </Button>
            </Space>
          </Row>
        </Form>
      </Modal>
    </div>
  )
}

const DepartmentManagementWithApp: React.FC = () => {
  return (
    <App>
      <DepartmentManagement />
    </App>
  )
}

export default DepartmentManagementWithApp

