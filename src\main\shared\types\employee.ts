/**
 * أنواع البيانات المشتركة للموظفين
 * Shared Employee Types
 */

export interface Employee {
  id: number
  employee_code: string
  name: string
  first_name?: string
  last_name?: string
  full_name?: string
  national_id?: string
  fingerprint_id?: string
  fingerprint_device_id?: number
  position?: string
  job_title?: string
  department_id?: number
  department_name?: string
  direct_manager_id?: number
  manager_name?: string
  employment_type?: string
  salary_type?: string
  basic_salary?: number
  hourly_rate?: number
  overtime_rate?: number
  allowances?: number
  phone?: string
  email?: string
  address?: string
  birth_date?: string
  hire_date: string
  termination_date?: string
  termination_reason?: string
  social_insurance_number?: string
  tax_number?: string
  bank_account?: string
  emergency_contact_name?: string
  emergency_contact_phone?: string
  card_number?: string
  access_level?: number
  working_hours_start?: string
  working_hours_end?: string
  notes?: string
  status?: string
  salary: number
  is_active: boolean
  created_at: string
  updated_at?: string
  created_by?: number
  // خصائص إضافية للعرض
  fingerprint_device_name?: string
  created_by_name?: string
}

export interface CreateEmployeeData {
  employee_code?: string
  name: string
  first_name?: string
  last_name?: string
  full_name?: string
  national_id?: string
  fingerprint_id?: string
  fingerprint_device_id?: number
  position?: string
  job_title?: string
  department_id?: number
  direct_manager_id?: number
  employment_type?: string
  salary_type?: string
  basic_salary?: number
  hourly_rate?: number
  overtime_rate?: number
  allowances?: number
  phone?: string
  email?: string
  address?: string
  birth_date?: string
  hire_date: string
  social_insurance_number?: string
  tax_number?: string
  bank_account?: string
  emergency_contact_name?: string
  emergency_contact_phone?: string
  card_number?: string
  access_level?: number
  working_hours_start?: string
  working_hours_end?: string
  notes?: string
  status?: string
  salary?: number
  is_active?: boolean
  created_by?: number
}

export interface UpdateEmployeeData extends Partial<CreateEmployeeData> {
  id: number
}

export interface Department {
  id: number
  name: string
  code?: string
  description?: string
  manager_id?: number
  manager_name?: string
  location?: string
  budget?: number
  working_hours_start?: string
  working_hours_end?: string
  employee_count?: number
  is_active: boolean
  created_at?: string
  updated_at?: string
}

export interface AttendanceRecord {
  id: number
  employee_id: number
  employee_name?: string
  date: string
  check_in?: string
  check_out?: string
  hours_worked?: number
  status: 'present' | 'absent' | 'late' | 'early_leave'
  notes?: string
  created_at: string
}

export interface FingerprintDevice {
  id: number
  device_name: string
  device_model?: string
  ip_address?: string
  port: number
  device_id: number
  location?: string
  status: string
  capacity: number
  current_users: number
  is_active?: boolean
  created_at?: string
}

// أنواع البيانات للتقارير
export interface EmployeeReportData {
  id: number
  employee_code: string
  full_name: string
  department_name: string
  position?: string
  hire_date: string
  status: string
}

// أنواع البيانات للإحصائيات
export interface EmployeeStats {
  total: number
  active: number
  inactive: number
  byDepartment: { [key: string]: number }
  byEmploymentType: { [key: string]: number }
}

// أنواع البيانات للبحث والفلترة
export interface EmployeeFilters {
  search?: string
  department_id?: number
  status?: string
  employment_type?: string
  hire_date_from?: string
  hire_date_to?: string
}

// أنواع البيانات للاستجابة من API
export interface EmployeeResponse {
  success: boolean
  data?: Employee | Employee[]
  message?: string
  total?: number
  page?: number
  limit?: number
}
