import React from 'react'
import UniversalReport from './UniversalReport'
import type { ReportData, ReportType } from '../../types/reports'

const IncomeStatementReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير قائمة الدخل...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getIncomeStatement(filters);

      if (!response || !response.success || !response.data) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const incomeData = response.data;

      // إعداد الأعمدة
      const columns = [
        {
          key: 'account_code',
          title: 'كود الحساب',
          align: 'center' as const,
          width: '120px'
        },
        {
          key: 'account_name',
          title: 'اسم الحساب',
          align: 'right' as const,
          width: '200px'
        },
        {
          key: 'account_type',
          title: 'نوع الحساب',
          align: 'center' as const,
          width: '120px'
        },
        {
          key: 'amount',
          title: 'المبلغ',
          align: 'left' as const,
          format: 'currency' as const,
          width: '150px'
        },
        {
          key: 'percentage',
          title: 'النسبة %',
          align: 'center' as const,
          format: 'percentage' as const,
          width: '100px'
        }
      ];

      // تحويل البيانات إلى تنسيق مسطح للجدول
      const tableData: any[] = [];
      
      // إضافة الإيرادات
      tableData.push({
        key: 'revenue-header',
        account_code: '',
        account_name: 'الإيرادات',
        account_type: 'عنوان',
        amount: null,
        percentage: null,
        isHeader: true
      });

      // إيرادات المبيعات
      if (incomeData.revenue?.sales_revenue) {
        incomeData.revenue.sales_revenue.forEach((item: any, index: number) => {
          tableData.push({
            key: `sales-revenue-${index}`,
            account_code: item.account_code,
            account_name: item.account_name,
            account_type: 'إيرادات مبيعات',
            amount: item.amount,
            percentage: item.percentage
          });
        });
      }

      // الإيرادات الأخرى
      if (incomeData.revenue?.other_revenue) {
        incomeData.revenue.other_revenue.forEach((item: any, index: number) => {
          tableData.push({
            key: `other-revenue-${index}`,
            account_code: item.account_code,
            account_name: item.account_name,
            account_type: 'إيرادات أخرى',
            amount: item.amount,
            percentage: item.percentage
          });
        });
      }

      // إجمالي الإيرادات
      tableData.push({
        key: 'total-revenue',
        account_code: '',
        account_name: 'إجمالي الإيرادات',
        account_type: 'إجمالي',
        amount: incomeData.revenue?.total_revenue || 0,
        percentage: null,
        isTotal: true
      });

      // تكلفة البضاعة المباعة
      tableData.push({
        key: 'cogs-header',
        account_code: '',
        account_name: 'تكلفة البضاعة المباعة',
        account_type: 'عنوان',
        amount: null,
        percentage: null,
        isHeader: true
      });

      if (incomeData.cost_of_goods_sold?.cogs_items) {
        incomeData.cost_of_goods_sold.cogs_items.forEach((item: any, index: number) => {
          tableData.push({
            key: `cogs-${index}`,
            account_code: item.account_code,
            account_name: item.account_name,
            account_type: 'تكلفة',
            amount: item.amount,
            percentage: item.percentage
          });
        });
      }

      // إجمالي تكلفة البضاعة المباعة
      tableData.push({
        key: 'total-cogs',
        account_code: '',
        account_name: 'إجمالي تكلفة البضاعة المباعة',
        account_type: 'إجمالي',
        amount: incomeData.cost_of_goods_sold?.total_cogs || 0,
        percentage: null,
        isTotal: true
      });

      // إجمالي الربح
      tableData.push({
        key: 'gross-profit',
        account_code: '',
        account_name: 'إجمالي الربح',
        account_type: 'ربح',
        amount: incomeData.gross_profit || 0,
        percentage: null,
        isGrossProfit: true
      });

      // المصروفات التشغيلية
      tableData.push({
        key: 'operating-expenses-header',
        account_code: '',
        account_name: 'المصروفات التشغيلية',
        account_type: 'عنوان',
        amount: null,
        percentage: null,
        isHeader: true
      });

      // مصروفات البيع
      if (incomeData.operating_expenses?.selling_expenses) {
        incomeData.operating_expenses.selling_expenses.forEach((item: any, index: number) => {
          tableData.push({
            key: `selling-expenses-${index}`,
            account_code: item.account_code,
            account_name: item.account_name,
            account_type: 'مصروفات بيع',
            amount: item.amount,
            percentage: item.percentage
          });
        });
      }

      // المصروفات الإدارية
      if (incomeData.operating_expenses?.administrative_expenses) {
        incomeData.operating_expenses.administrative_expenses.forEach((item: any, index: number) => {
          tableData.push({
            key: `admin-expenses-${index}`,
            account_code: item.account_code,
            account_name: item.account_name,
            account_type: 'مصروفات إدارية',
            amount: item.amount,
            percentage: item.percentage
          });
        });
      }

      // إجمالي المصروفات التشغيلية
      tableData.push({
        key: 'total-operating-expenses',
        account_code: '',
        account_name: 'إجمالي المصروفات التشغيلية',
        account_type: 'إجمالي',
        amount: incomeData.operating_expenses?.total_operating_expenses || 0,
        percentage: null,
        isTotal: true
      });

      // الدخل التشغيلي
      tableData.push({
        key: 'operating-income',
        account_code: '',
        account_name: 'الدخل التشغيلي',
        account_type: 'دخل',
        amount: incomeData.operating_income || 0,
        percentage: null,
        isOperatingIncome: true
      });

      // الإيرادات والمصروفات الأخرى
      if (incomeData.other_income_expenses?.other_income?.length > 0 || 
          incomeData.other_income_expenses?.other_expenses?.length > 0) {
        
        tableData.push({
          key: 'other-income-expenses-header',
          account_code: '',
          account_name: 'الإيرادات والمصروفات الأخرى',
          account_type: 'عنوان',
          amount: null,
          percentage: null,
          isHeader: true
        });

        // الإيرادات الأخرى
        if (incomeData.other_income_expenses.other_income) {
          incomeData.other_income_expenses.other_income.forEach((item: any, index: number) => {
            tableData.push({
              key: `other-income-${index}`,
              account_code: item.account_code,
              account_name: item.account_name,
              account_type: 'إيرادات أخرى',
              amount: item.amount,
              percentage: item.percentage
            });
          });
        }

        // المصروفات الأخرى
        if (incomeData.other_income_expenses.other_expenses) {
          incomeData.other_income_expenses.other_expenses.forEach((item: any, index: number) => {
            tableData.push({
              key: `other-expenses-${index}`,
              account_code: item.account_code,
              account_name: item.account_name,
              account_type: 'مصروفات أخرى',
              amount: item.amount,
              percentage: item.percentage
            });
          });
        }

        // صافي الإيرادات والمصروفات الأخرى
        tableData.push({
          key: 'net-other',
          account_code: '',
          account_name: 'صافي الإيرادات والمصروفات الأخرى',
          account_type: 'صافي',
          amount: incomeData.other_income_expenses?.net_other || 0,
          percentage: null,
          isTotal: true
        });
      }

      // صافي الدخل قبل الضريبة
      tableData.push({
        key: 'net-income-before-tax',
        account_code: '',
        account_name: 'صافي الدخل قبل الضريبة',
        account_type: 'دخل',
        amount: incomeData.net_income_before_tax || 0,
        percentage: null,
        isNetIncomeBeforeTax: true
      });

      // مصروف الضريبة
      if (incomeData.tax_expense && incomeData.tax_expense !== 0) {
        tableData.push({
          key: 'tax-expense',
          account_code: '',
          account_name: 'مصروف الضريبة',
          account_type: 'ضريبة',
          amount: incomeData.tax_expense,
          percentage: null,
          isTaxExpense: true
        });
      }

      // صافي الدخل
      tableData.push({
        key: 'net-income',
        account_code: '',
        account_name: 'صافي الدخل',
        account_type: 'صافي الدخل',
        amount: incomeData.net_income || 0,
        percentage: null,
        isNetIncome: true
      });

      // إحصائيات سريعة
      const quickStats = [
        {
          title: 'إجمالي الإيرادات',
          value: incomeData.revenue?.total_revenue || 0,
          format: 'currency' as const,
          color: '#52c41a'
        },
        {
          title: 'إجمالي الربح',
          value: incomeData.gross_profit || 0,
          format: 'currency' as const,
          color: incomeData.gross_profit >= 0 ? '#52c41a' : '#ff4d4f'
        },
        {
          title: 'الدخل التشغيلي',
          value: incomeData.operating_income || 0,
          format: 'currency' as const,
          color: incomeData.operating_income >= 0 ? '#52c41a' : '#ff4d4f'
        },
        {
          title: 'صافي الدخل',
          value: incomeData.net_income || 0,
          format: 'currency' as const,
          color: incomeData.net_income >= 0 ? '#52c41a' : '#ff4d4f'
        }
      ];

      console.log('✅ تم إنشاء تقرير قائمة الدخل بنجاح');

      return {
        title: 'قائمة الدخل',
        data: tableData,
        columns,
        summary: {
          totalRecords: tableData.length,
          quickStats
        }
      };

    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير قائمة الدخل:', error);
      throw error;
    }
  };

  // إعدادات التقرير
  const reportConfig = {
    reportType: 'income-statement' as ReportType,
    title: 'قائمة الدخل',
    generateReport,
    defaultFilters: {
      dateRange: null, // كل المدة افتراضياً
    },
    enabledFilters: {
      showDateFilter: true,
      showAdvancedSearch: true,
    },
    features: {
      enableExport: true,
      enablePrint: true,
      enableRefresh: true,
      showQuickStats: true,
    }
  };

  return <UniversalReport {...reportConfig} />;
};

export default IncomeStatementReport;
