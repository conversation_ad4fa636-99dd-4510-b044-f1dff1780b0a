import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  DatePicker,
  Select,
  Row,
  Col,
  Statistic,
  Progress,
  Tag,
  Space,
  Typography,
  Form,
  message,
  Tooltip
} from 'antd'
import {
  Bar<PERSON>hartOutlined,
  DollarOutlined,
  RiseOutlined,
  FallOutlined,
  ExportOutlined,
  FilterOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../../utils/logger'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

interface CostAnalysisData {
  analysis: ProductionCostRecord[]
  summary: CostSummary
}

interface ProductionCostRecord {
  id: number
  order_number: string
  product_name: string
  product_code: string
  quantity: number
  actual_quantity?: number
  estimated_cost: number
  actual_cost: number
  material_cost: number
  labor_cost: number
  overhead_cost: number
  unit_cost: number
  labor_hours: number
  completion_date: string
  department_name?: string
  cost_variance: number
  cost_variance_percent: number
}

interface CostSummary {
  total_orders: number
  total_estimated_cost: number
  total_actual_cost: number
  total_material_cost: number
  total_labor_cost: number
  total_overhead_cost: number
  total_labor_hours: number
  cost_variance: number
  cost_variance_percent: number
  average_unit_cost: number
}

const ProductionCostAnalysis: React.FC = () => {
  const [data, setData] = useState<CostAnalysisData | null>(null)
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()
  const [products, setProducts] = useState<any[]>([])
  const [departments, setDepartments] = useState<any[]>([])

  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    await Promise.all([
      loadProducts(),
      loadDepartments(),
      loadAnalysis()
    ])
  }

  const loadProducts = async () => {
    try {
      if (!window.electronAPI) return

      const result = await window.electronAPI.getItems()
      // getItems returns Item[] directly, but handlers wrap it in ApiResponse
      let items: any[] = []
      if (Array.isArray(result)) {
        items = result
      } else if (result && typeof result === 'object' && 'success' in result) {
        const apiResult = result as any
        if (apiResult.success) {
          items = apiResult.data || []
        }
      }

      const finishedProducts = items.filter((item: any) =>
        item.category_name === 'منتجات تامة' || item.type === 'finished_product'
      )
      setProducts(finishedProducts)
    } catch (error) {
      Logger.error('ProductionCostAnalysis', 'خطأ في تحميل المنتجات:', error)
    }
  }

  const loadDepartments = async () => {
    try {
      if (!window.electronAPI) return

      const result = await window.electronAPI.getProductionDepartments()
      if (result.success) {
        setDepartments(result.data)
      }
    } catch (error) {
      Logger.error('ProductionCostAnalysis', 'خطأ في تحميل الأقسام:', error)
    }
  }

  const loadAnalysis = async (filters?: any) => {
    setLoading(true)
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionCostAnalysis', 'window.electronAPI غير متوفر')
        return
      }

      const result = await window.electronAPI.getProductionCosts()
      
      if (result.success) {
        setData(result.data)
        Logger.info('ProductionCostAnalysis', 'تم تحميل تحليل التكاليف بنجاح')
      } else {
        message.error('فشل في تحميل تحليل التكاليف')
      }
    } catch (error) {
      Logger.error('ProductionCostAnalysis', 'خطأ في تحميل تحليل التكاليف:', error)
      message.error('حدث خطأ في تحميل تحليل التكاليف')
    }
    setLoading(false)
  }

  const handleFilter = (values: any) => {
    const filters: any = {}
    
    if (values.product_id) {
      filters.product_id = values.product_id
    }
    
    if (values.department_id) {
      filters.department_id = values.department_id
    }
    
    if (values.dateRange && values.dateRange[0]) {
      filters.date_from = values.dateRange[0].format('YYYY-MM-DD')
      filters.date_to = values.dateRange[1].format('YYYY-MM-DD')
    }

    loadAnalysis(filters)
  }

  const getVarianceColor = (variance: number) => {
    if (variance > 0) return '#f5222d' // أحمر للزيادة في التكلفة
    if (variance < 0) return '#52c41a' // أخضر للتوفير
    return '#666' // رمادي للتساوي
  }

  const getVarianceIcon = (variance: number) => {
    if (variance > 0) return <RiseOutlined style={{ color: '#f5222d' }} />
    if (variance < 0) return <FallOutlined style={{ color: '#52c41a' }} />
    return null
  }

  const columns = [
    {
      title: 'رقم الأمر',
      dataIndex: 'order_number',
      key: 'order_number',
      width: 120,
    },
    {
      title: 'المنتج',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 200,
    },
    {
      title: 'الكمية',
      key: 'quantity',
      width: 100,
      render: (record: ProductionCostRecord) => (
        <div>
          <div>{record.actual_quantity || record.quantity}</div>
          {record.actual_quantity && record.actual_quantity !== record.quantity && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              مخطط: {record.quantity}
            </div>
          )}
        </div>
      )
    },
    {
      title: 'التكلفة المقدرة',
      dataIndex: 'estimated_cost',
      key: 'estimated_cost',
      width: 120,
      render: (cost: number) => `₪${cost.toFixed(2)}`
    },
    {
      title: 'التكلفة الفعلية',
      dataIndex: 'actual_cost',
      key: 'actual_cost',
      width: 120,
      render: (cost: number) => `₪${cost.toFixed(2)}`
    },
    {
      title: 'الانحراف',
      key: 'variance',
      width: 120,
      render: (record: ProductionCostRecord) => (
        <div>
          <div style={{ color: getVarianceColor(record.cost_variance) }}>
            {getVarianceIcon(record.cost_variance)}
            ₪{Math.abs(record.cost_variance).toFixed(2)}
          </div>
          <div style={{ fontSize: '12px', color: getVarianceColor(record.cost_variance_percent) }}>
            {record.cost_variance_percent > 0 ? '+' : ''}{record.cost_variance_percent.toFixed(1)}%
          </div>
        </div>
      )
    },
    {
      title: 'تكلفة الوحدة',
      dataIndex: 'unit_cost',
      key: 'unit_cost',
      width: 100,
      render: (cost: number) => `₪${cost.toFixed(2)}`
    },
    {
      title: 'تفصيل التكاليف',
      key: 'cost_breakdown',
      width: 200,
      render: (record: ProductionCostRecord) => (
        <div style={{ fontSize: '12px' }}>
          <div>مواد: ₪{record.material_cost.toFixed(2)}</div>
          <div>عمالة: ₪{record.labor_cost.toFixed(2)}</div>
          <div>عامة: ₪{record.overhead_cost.toFixed(2)}</div>
        </div>
      )
    },
    {
      title: 'ساعات العمل',
      dataIndex: 'labor_hours',
      key: 'labor_hours',
      width: 100,
      render: (hours: number) => `${hours.toFixed(1)}س`
    },
    {
      title: 'تاريخ الإكمال',
      dataIndex: 'completion_date',
      key: 'completion_date',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={3}>
          <BarChartOutlined /> تحليل التكاليف الفعلية مقابل المقدرة
        </Title>
      </div>

      {/* فلاتر البحث */}
      <Card style={{ marginBottom: '24px' }}>
        <Form
          form={form}
          layout="inline"
          onFinish={handleFilter}
        >
          <Form.Item name="product_id" label="المنتج">
            <Select
              placeholder="اختر المنتج"
              style={{ width: 200 }}
              allowClear
              showSearch
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
              }
            >
              {products.map(product => (
                <Option key={product.id} value={product.id}>
                  {product.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="department_id" label="القسم">
            <Select
              placeholder="اختر القسم"
              style={{ width: 150 }}
              allowClear
            >
              {departments.map(dept => (
                <Option key={dept.id} value={dept.id}>
                  {dept.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="dateRange" label="الفترة">
            <RangePicker style={{ width: 250 }} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<FilterOutlined />}>
                تطبيق الفلتر
              </Button>
              <Button onClick={() => {
                form.resetFields()
                loadAnalysis()
              }}>
                إعادة تعيين
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* الإحصائيات الإجمالية */}
      {data && (
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={4}>
            <Card>
              <Statistic
                title="إجمالي الأوامر"
                value={data.summary.total_orders}
                prefix={<BarChartOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="التكلفة المقدرة"
                value={data.summary.total_estimated_cost}
                precision={2}
                prefix="₪"
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="التكلفة الفعلية"
                value={data.summary.total_actual_cost}
                precision={2}
                prefix="₪"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="الانحراف"
                value={Math.abs(data.summary.cost_variance)}
                precision={2}
                prefix={data.summary.cost_variance >= 0 ? '+₪' : '-₪'}
                valueStyle={{ color: getVarianceColor(data.summary.cost_variance) }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="نسبة الانحراف"
                value={Math.abs(data.summary.cost_variance_percent)}
                precision={1}
                suffix="%"
                prefix={data.summary.cost_variance_percent >= 0 ? '+' : '-'}
                valueStyle={{ color: getVarianceColor(data.summary.cost_variance_percent) }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="متوسط تكلفة الوحدة"
                value={data.summary.average_unit_cost}
                precision={2}
                prefix="₪"
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* تفصيل التكاليف */}
      {data && (
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={8}>
            <Card title="تكلفة المواد">
              <Statistic
                value={data.summary.total_material_cost}
                precision={2}
                prefix="₪"
                valueStyle={{ color: '#f5222d' }}
              />
              <Progress
                percent={Math.round((data.summary.total_material_cost / data.summary.total_actual_cost) * 100)}
                strokeColor="#f5222d"
                showInfo={false}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card title="تكلفة العمالة">
              <Statistic
                value={data.summary.total_labor_cost}
                precision={2}
                prefix="₪"
                valueStyle={{ color: '#52c41a' }}
              />
              <Progress
                percent={Math.round((data.summary.total_labor_cost / data.summary.total_actual_cost) * 100)}
                strokeColor="#52c41a"
                showInfo={false}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card title="التكاليف العامة">
              <Statistic
                value={data.summary.total_overhead_cost}
                precision={2}
                prefix="₪"
                valueStyle={{ color: '#fa8c16' }}
              />
              <Progress
                percent={Math.round((data.summary.total_overhead_cost / data.summary.total_actual_cost) * 100)}
                strokeColor="#fa8c16"
                showInfo={false}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* جدول التفاصيل */}
      <Card title="تفاصيل أوامر الإنتاج" extra={
        <Button onClick={() => loadAnalysis()} loading={loading}>
          تحديث البيانات
        </Button>
      }>
        <Table
          columns={columns}
          dataSource={data?.analysis || []}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} أمر`
          }}
        />
      </Card>
    </div>
  )
}

export default ProductionCostAnalysis
