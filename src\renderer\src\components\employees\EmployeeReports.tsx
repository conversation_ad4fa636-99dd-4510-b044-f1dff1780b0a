import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  Row,
  Col,
  Typo<PERSON>,
  Select, Button,
  Space,
  Table,
  Statistic,
  Progress,
  Tag,
  Tabs,
  Form,
  DatePicker
, App} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
// import type { TabsProps } from 'antd'
import {
  FileTextOutlined,
  Bar<PERSON><PERSON>Outlined,
  TeamOutlined,
  Clock<PERSON>ircleOutlined,
  DollarOutlined,
  CalendarOutlined,
  PrinterOutlined,
  FileExcelOutlined,
  UserOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  MedicineBoxOutlined,
  TrophyOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

interface AttendanceReport {
  employee_id: number
  employee_name: string
  employee_code: string
  department_name: string
  total_days: number
  present_days: number
  absent_days: number
  late_days: number
  total_hours: number
  regular_hours: number
  overtime_hours: number
  attendance_rate: number
}

interface PayrollReport {
  employee_id: number
  employee_name: string
  employee_code: string
  department_name: string
  basic_salary: number
  overtime_amount: number
  allowances: number
  gross_salary: number
  total_deductions: number
  net_salary: number
}

interface LeaveReport {
  employee_id: number
  employee_name: string
  employee_code: string
  department_name: string
  annual_leaves: number
  sick_leaves: number
  emergency_leaves: number
  total_leave_days: number
}

interface Employee {
  id: number
  employee_code: string
  full_name: string
  department_name: string
}

interface Department {
  id: number
  name: string
}

interface EmployeeReportsProps {
  initialTab?: string
}

const EmployeeReports: React.FC<EmployeeReportsProps> = ({ initialTab = 'attendance' }) => {
  const { message: messageApi } = App.useApp() // استخدام App context لحل تحذير Antd
  const [employees, setEmployees] = useState<Employee[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [attendanceReport, setAttendanceReport] = useState<AttendanceReport[]>([])
  const [payrollReport, setPayrollReport] = useState<PayrollReport[]>([])
  const [leaveReport, setLeaveReport] = useState<LeaveReport[]>([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState(initialTab)
  const [form] = Form.useForm()

  useEffect(() => {
    fetchEmployees()
    fetchDepartments()
  }, [])

  const fetchEmployees = async () => {
    try {
      Logger.info('EmployeeReports', '🔄 جاري جلب بيانات الموظفين...')

      if (!window.electronAPI) {
        Logger.error('EmployeeReports', '❌ window.electronAPI غير متوفر')
        Logger.info('EmployeeReports', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للموظفين
        const mockEmployees = [
          {
            id: 1,
            employee_code: 'EMP001',
            full_name: 'أحمد محمد علي',
            department_name: 'قسم الموارد البشرية',
            status: 'active'
          },
          {
            id: 2,
            employee_code: 'EMP002',
            full_name: 'فاطمة عبدالله الزهراني',
            department_name: 'قسم المالية والمحاسبة',
            status: 'active'
          }
        ]

        setEmployees(mockEmployees as any[])
        Logger.info('EmployeeReports', '✅ تم تحميل ${mockEmployees.length} موظف وهمي للمتصفح')
        return
      }

      const result = await window.electronAPI.getEmployees()
      Logger.info('EmployeeReports', '📊 نتيجة جلب الموظفين:', result)

      if (result && result.success) {
        // تصحيح اسم الخاصية من status إلى is_active
        const activeEmployees = result.data.filter((emp: any) => emp.is_active === 1)
        setEmployees(activeEmployees)
        Logger.info('EmployeeReports', '✅ تم جلب ${activeEmployees.length} موظف نشط بنجاح')
      } else {
        Logger.error('EmployeeReports', '❌ فشل في جلب الموظفين:', result?.message)
        setEmployees([])
      }
    } catch (error) {
      Logger.error('EmployeeReports', 'فشل في جلب الموظفين:', error)
      setEmployees([])
    }
  }

  const fetchDepartments = async () => {
    try {
      if (!window.electronAPI) {
        Logger.error('EmployeeReports', '❌ window.electronAPI غير متوفر')
        Logger.info('EmployeeReports', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للأقسام
        const mockDepartments = [
          { id: 1, name: 'قسم الموارد البشرية', code: 'HR001' },
          { id: 2, name: 'قسم المالية والمحاسبة', code: 'FIN001' },
          { id: 3, name: 'قسم المبيعات', code: 'SALES001' }
        ]

        setDepartments(mockDepartments as any[])
        Logger.info('EmployeeReports', '✅ تم تحميل ${mockDepartments.length} قسم وهمي للمتصفح')
        return
      }

      const result = await window.electronAPI.getEmployeeDepartments()
      if (result.success) {
        setDepartments(result.data)
      }
    } catch (error) {
      Logger.error('EmployeeReports', 'فشل في جلب الأقسام:', error)
    }
  }

  const generateAttendanceReport = async (filters?: any) => {
    Logger.info('EmployeeReports', '📈 بدء إنشاء تقرير الحضور...')
    setLoading(true)
    try {
      if (!window.electronAPI) {
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      }

      // جلب تقرير الحضور الحقيقي من قاعدة البيانات
      const response = await window.electronAPI.getEmployeeAttendanceReport(filters || {})

      if (!response || !response.success) {
        throw new Error('فشل في جلب تقرير الحضور من قاعدة البيانات')
      }

      const attendanceData = response.data || []
      setAttendanceReport(attendanceData)
      Logger.info('EmployeeReports', `✅ تم جلب تقرير الحضور بنجاح: ${attendanceData.length} موظف`)
      messageApi.success(`تم جلب تقرير الحضور بنجاح - ${attendanceData.length} موظف`)
    } catch (error) {
      Logger.error('EmployeeReports', 'خطأ في تقرير الحضور:', error)
      messageApi.error('فشل في إنشاء تقرير الحضور')
    } finally {
      setLoading(false)
    }
  }

  const generatePayrollReport = async (_filters?: any) => {
    Logger.info('EmployeeReports', '💰 بدء إنشاء تقرير الرواتب...')
    Logger.info('EmployeeReports', '👥 عدد الموظفين المتاحين:', employees.length)
    setLoading(true)
    try {
      // إنشاء بيانات تجريبية احترافية للرواتب
      const mockData: PayrollReport[] = employees.length > 0 ? employees.map(emp => {
        const basicSalary = Math.floor(Math.random() * 3000) + 2500 // 2500-5500
        const overtime = Math.floor(Math.random() * 800) + 200 // 200-1000
        const allowances = Math.floor(Math.random() * 500) + 100 // 100-600
        const gross = basicSalary + overtime + allowances
        const deductions = Math.floor(gross * (0.08 + Math.random() * 0.08)) // 8-16%
        return {
          employee_id: emp.id,
          employee_name: emp.full_name,
          employee_code: emp.employee_code,
          department_name: emp.department_name || 'قسم عام',
          basic_salary: basicSalary,
          overtime_amount: overtime,
          allowances: allowances,
          gross_salary: gross,
          total_deductions: deductions,
          net_salary: gross - deductions
        }
      }) : [
        // بيانات افتراضية إذا لم يوجد موظفين
        {
          employee_id: 1,
          employee_name: 'أحمد محمد علي',
          employee_code: 'EMP001',
          department_name: 'قسم المحاسبة',
          basic_salary: 4500,
          overtime_amount: 650,
          allowances: 300,
          gross_salary: 5450,
          total_deductions: 654,
          net_salary: 4796
        },
        {
          employee_id: 2,
          employee_name: 'فاطمة أحمد حسن',
          employee_code: 'EMP002',
          department_name: 'قسم المبيعات',
          basic_salary: 3800,
          overtime_amount: 420,
          allowances: 250,
          gross_salary: 4470,
          total_deductions: 447,
          net_salary: 4023
        },
        {
          employee_id: 3,
          employee_name: 'محمد عبدالله سالم',
          employee_code: 'EMP003',
          department_name: 'قسم الإنتاج',
          basic_salary: 3200,
          overtime_amount: 380,
          allowances: 200,
          gross_salary: 3780,
          total_deductions: 378,
          net_salary: 3402
        }
      ]

      setPayrollReport(mockData)
      Logger.info('EmployeeReports', `✅ تم إنشاء تقرير الرواتب بنجاح: ${mockData.length} موظف`)
      messageApi.success(`تم إنشاء تقرير الرواتب بنجاح - ${mockData.length} موظف`)
    } catch (error) {
      Logger.error('EmployeeReports', 'خطأ في تقرير الرواتب:', error)
      messageApi.error('فشل في إنشاء تقرير الرواتب')
    } finally {
      setLoading(false)
    }
  }

  const generateLeaveReport = async (_filters?: any) => {
    Logger.info('EmployeeReports', '📅 بدء إنشاء تقرير الإجازات...')
    Logger.info('EmployeeReports', '👥 عدد الموظفين المتاحين:', employees.length)
    setLoading(true)
    try {
      // إنشاء بيانات تجريبية احترافية للإجازات
      const mockData: LeaveReport[] = employees.length > 0 ? employees.map(emp => {
        const annual = Math.floor(Math.random() * 12) + 3 // 3-15 يوم
        const sick = Math.floor(Math.random() * 6) // 0-5 أيام
        const emergency = Math.floor(Math.random() * 4) // 0-3 أيام
        return {
          employee_id: emp.id,
          employee_name: emp.full_name,
          employee_code: emp.employee_code,
          department_name: emp.department_name || 'قسم عام',
          annual_leaves: annual,
          sick_leaves: sick,
          emergency_leaves: emergency,
          total_leave_days: annual + sick + emergency
        }
      }) : [
        // بيانات افتراضية إذا لم يوجد موظفين
        {
          employee_id: 1,
          employee_name: 'أحمد محمد علي',
          employee_code: 'EMP001',
          department_name: 'قسم المحاسبة',
          annual_leaves: 12,
          sick_leaves: 3,
          emergency_leaves: 1,
          total_leave_days: 16
        },
        {
          employee_id: 2,
          employee_name: 'فاطمة أحمد حسن',
          employee_code: 'EMP002',
          department_name: 'قسم المبيعات',
          annual_leaves: 8,
          sick_leaves: 2,
          emergency_leaves: 0,
          total_leave_days: 10
        },
        {
          employee_id: 3,
          employee_name: 'محمد عبدالله سالم',
          employee_code: 'EMP003',
          department_name: 'قسم الإنتاج',
          annual_leaves: 15,
          sick_leaves: 4,
          emergency_leaves: 2,
          total_leave_days: 21
        }
      ]

      setLeaveReport(mockData)
      Logger.info('EmployeeReports', `✅ تم إنشاء تقرير الإجازات بنجاح: ${mockData.length} موظف`)
      messageApi.success(`تم إنشاء تقرير الإجازات بنجاح - ${mockData.length} موظف`)
    } catch (error) {
      Logger.error('EmployeeReports', 'خطأ في تقرير الإجازات:', error)
      messageApi.error('فشل في إنشاء تقرير الإجازات')
    } finally {
      setLoading(false)
    }
  }

  const handleGenerateReport = (values: any) => {
    Logger.info('EmployeeReports', '🔄 تم النقر على زر إنشاء التقرير')
    Logger.info('EmployeeReports', '📊 التبويب النشط:', activeTab)
    Logger.info('EmployeeReports', '📋 القيم المرسلة:', values)

    const filters = {
      ...values,
      date_from: values.date_range ? values.date_range[0].format('YYYY-MM-DD') : null,
      date_to: values.date_range ? values.date_range[1].format('YYYY-MM-DD') : null
    }
    delete filters.date_range

    Logger.info('EmployeeReports', '🔍 الفلاتر المعالجة:', filters)

    switch (activeTab) {
      case 'attendance':
        Logger.info('EmployeeReports', '📈 تشغيل تقرير الحضور')
        generateAttendanceReport(filters)
        break
      case 'payroll':
        Logger.info('EmployeeReports', '💰 تشغيل تقرير الرواتب')
        generatePayrollReport(filters)
        break
      case 'leaves':
        Logger.info('EmployeeReports', '📅 تشغيل تقرير الإجازات')
        generateLeaveReport(filters)
        break
      case 'performance':
        Logger.info('EmployeeReports', '🏆 تشغيل تقرير الأداء')
        // سيتم تطوير تقرير الأداء لاحقاً
        messageApi.info('تقرير الأداء والتقييم قيد التطوير')
        break
      case 'overtime':
        Logger.info('EmployeeReports', '⏰ تشغيل تقرير الساعات الإضافية')
        // استخدام تقرير الحضور لعرض الساعات الإضافية
        generateAttendanceReport(filters)
        generatePayrollReport(filters)
        break
      default:
        Logger.error('EmployeeReports', '❌ تبويب غير معروف:', activeTab)
        messageApi.error('خطأ: تبويب غير معروف')
    }
  }

  const attendanceColumns: ColumnsType<AttendanceReport> = [
    {
      title: 'الموظف',
      key: 'employee',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.employee_name}</div>
          <Text type="secondary">{record.employee_code}</Text>
        </div>
      )
    },
    {
      title: 'القسم',
      dataIndex: 'department_name',
      key: 'department_name'
    },
    {
      title: 'أيام الحضور',
      dataIndex: 'present_days',
      key: 'present_days',
      render: (days, record) => (
        <div>
          <div>{days} / {record.total_days}</div>
          <Progress 
            percent={(days / record.total_days) * 100} 
            size="small" 
            showInfo={false}
            strokeColor="#52c41a"
          />
        </div>
      )
    },
    {
      title: 'أيام الغياب',
      dataIndex: 'absent_days',
      key: 'absent_days',
      render: (days) => days > 0 ? (
        <Tag color="red">{days}</Tag>
      ) : (
        <Tag color="green">0</Tag>
      )
    },
    {
      title: 'أيام التأخير',
      dataIndex: 'late_days',
      key: 'late_days',
      render: (days) => days > 0 ? (
        <Tag color="orange">{days}</Tag>
      ) : (
        <Tag color="green">0</Tag>
      )
    },
    {
      title: 'إجمالي الساعات',
      dataIndex: 'total_hours',
      key: 'total_hours',
      render: (hours) => `${hours} ساعة`
    },
    {
      title: 'ساعات إضافية',
      dataIndex: 'overtime_hours',
      key: 'overtime_hours',
      render: (hours) => hours > 0 ? (
        <Tag color="blue">{hours} ساعة</Tag>
      ) : '-'
    },
    {
      title: 'معدل الحضور',
      dataIndex: 'attendance_rate',
      key: 'attendance_rate',
      render: (rate) => (
        <div>
          <Progress 
            percent={rate} 
            size="small"
            strokeColor={rate >= 90 ? '#52c41a' : rate >= 80 ? '#faad14' : '#ff4d4f'}
          />
        </div>
      )
    }
  ]

  const payrollColumns: ColumnsType<PayrollReport> = [
    {
      title: 'الموظف',
      key: 'employee',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.employee_name}</div>
          <Text type="secondary">{record.employee_code}</Text>
        </div>
      )
    },
    {
      title: 'القسم',
      dataIndex: 'department_name',
      key: 'department_name'
    },
    {
      title: 'الراتب الأساسي',
      dataIndex: 'basic_salary',
      key: 'basic_salary',
      render: (amount) => `${amount.toLocaleString()} ₪`
    },
    {
      title: 'الساعات الإضافية',
      dataIndex: 'overtime_amount',
      key: 'overtime_amount',
      render: (amount) => amount > 0 ? `${amount.toLocaleString()} ₪` : '-'
    },
    {
      title: 'البدلات',
      dataIndex: 'allowances',
      key: 'allowances',
      render: (amount) => amount > 0 ? `${amount.toLocaleString()} ₪` : '-'
    },
    {
      title: 'الإجمالي',
      dataIndex: 'gross_salary',
      key: 'gross_salary',
      render: (amount) => (
        <strong style={{ color: '#1890ff' }}>
          {amount.toLocaleString()} ₪
        </strong>
      )
    },
    {
      title: 'الخصومات',
      dataIndex: 'total_deductions',
      key: 'total_deductions',
      render: (amount) => (
        <span style={{ color: '#ff4d4f' }}>
          -{amount.toLocaleString()} ₪
        </span>
      )
    },
    {
      title: 'الصافي',
      dataIndex: 'net_salary',
      key: 'net_salary',
      render: (amount) => (
        <strong style={{ color: '#52c41a' }}>
          {amount.toLocaleString()} ₪
        </strong>
      )
    }
  ]

  const leaveColumns: ColumnsType<LeaveReport> = [
    {
      title: 'الموظف',
      key: 'employee',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.employee_name}</div>
          <Text type="secondary">{record.employee_code}</Text>
        </div>
      )
    },
    {
      title: 'القسم',
      dataIndex: 'department_name',
      key: 'department_name'
    },
    {
      title: 'إجازات سنوية',
      dataIndex: 'annual_leaves',
      key: 'annual_leaves',
      render: (days) => days > 0 ? (
        <Tag color="blue">{days} يوم</Tag>
      ) : '-'
    },
    {
      title: 'إجازات مرضية',
      dataIndex: 'sick_leaves',
      key: 'sick_leaves',
      render: (days) => days > 0 ? (
        <Tag color="red">{days} يوم</Tag>
      ) : '-'
    },
    {
      title: 'إجازات طارئة',
      dataIndex: 'emergency_leaves',
      key: 'emergency_leaves',
      render: (days) => days > 0 ? (
        <Tag color="orange">{days} يوم</Tag>
      ) : '-'
    },
    {
      title: 'إجمالي الإجازات',
      dataIndex: 'total_leave_days',
      key: 'total_leave_days',
      render: (days) => (
        <strong>{days} يوم</strong>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <FileTextOutlined /> تقارير الموظفين
      </Title>

      {/* فلاتر التقرير */}
      <Card style={{ marginBottom: '16px' }}>
        <Form
          form={form}
          layout="inline"
          onFinish={handleGenerateReport}
          initialValues={{
            date_range: [dayjs().startOf('month'), dayjs().endOf('month')]
          }}
        >
          <Form.Item name="employee_id" label="الموظف">
            <Select placeholder="جميع الموظفين" style={{ width: 200 }} allowClear>
              {employees.map(emp => (
                <Option key={emp.id} value={emp.id}>
                  {emp.full_name} - {emp.employee_code}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item name="department_id" label="القسم">
            <Select placeholder="جميع الأقسام" style={{ width: 150 }} allowClear>
              {departments.map(dept => (
                <Option key={dept.id} value={dept.id}>
                  {dept.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item name="date_range" label="الفترة">
            <RangePicker />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<BarChartOutlined />} loading={loading}>
                إنشاء التقرير
              </Button>
              <Button icon={<PrinterOutlined />}>
                طباعة
              </Button>
              <Button icon={<FileExcelOutlined />}>
                تصدير Excel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* تبويبات التقارير */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'attendance',
              label: (
                <span>
                  <ClockCircleOutlined />
                  تقرير الحضور والانصراف
                </span>
              ),
              children: (
                <div>
                  {attendanceReport.length > 0 ? (
              <>
                {/* إحصائيات سريعة للحضور */}
                <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="متوسط معدل الحضور"
                        value={attendanceReport.reduce((sum, emp) => sum + emp.attendance_rate, 0) / attendanceReport.length}
                        precision={1}
                        suffix="%"
                        valueStyle={{ color: '#52c41a' }}
                        prefix={<TrophyOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="إجمالي أيام الغياب"
                        value={attendanceReport.reduce((sum, emp) => sum + emp.absent_days, 0)}
                        valueStyle={{ color: '#ff4d4f' }}
                        prefix={<CloseCircleOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="إجمالي الساعات الإضافية"
                        value={attendanceReport.reduce((sum, emp) => sum + emp.overtime_hours, 0)}
                        suffix="ساعة"
                        valueStyle={{ color: '#1890ff' }}
                        prefix={<ClockCircleOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="الموظفين المتأخرين"
                        value={attendanceReport.filter(emp => emp.late_days > 0).length}
                        valueStyle={{ color: '#fa8c16' }}
                        prefix={<ExclamationCircleOutlined />}
                      />
                    </Card>
                  </Col>
                </Row>

                <Table
                  columns={attendanceColumns}
                  dataSource={attendanceReport}
                  rowKey="employee_id"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showTotal: (total) => `إجمالي ${total} موظف`
                  }}
                />
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <ClockCircleOutlined style={{ fontSize: 48, color: '#ccc' }} />
                <div style={{ marginTop: 16, fontSize: 16 }}>لا توجد بيانات حضور</div>
                <div style={{ marginTop: 8, color: '#666' }}>انقر على &quot;إنشاء التقرير&quot; لعرض بيانات الحضور والانصراف</div>
              </div>
                  )}
                </div>
              )
            },
            {
              key: 'payroll',
              label: (
                <span>
                  <DollarOutlined />
                  تقرير الرواتب
                </span>
              ),
              children: (
                <div>
                  {payrollReport.length > 0 ? (
              <>
                {/* إحصائيات سريعة للرواتب */}
                <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="إجمالي الرواتب الإجمالية"
                        value={payrollReport.reduce((sum, emp) => sum + emp.gross_salary, 0)}
                        precision={0}
                        suffix="₪"
                        valueStyle={{ color: '#1890ff' }}
                        prefix={<DollarOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="إجمالي الرواتب الصافية"
                        value={payrollReport.reduce((sum, emp) => sum + emp.net_salary, 0)}
                        precision={0}
                        suffix="₪"
                        valueStyle={{ color: '#52c41a' }}
                        prefix={<DollarOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="إجمالي الساعات الإضافية"
                        value={payrollReport.reduce((sum, emp) => sum + emp.overtime_amount, 0)}
                        precision={0}
                        suffix="₪"
                        valueStyle={{ color: '#722ed1' }}
                        prefix={<ClockCircleOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="إجمالي الخصومات"
                        value={payrollReport.reduce((sum, emp) => sum + emp.total_deductions, 0)}
                        precision={0}
                        suffix="₪"
                        valueStyle={{ color: '#ff4d4f' }}
                        prefix={<ExclamationCircleOutlined />}
                      />
                    </Card>
                  </Col>
                </Row>

                <Table
                  columns={payrollColumns}
                  dataSource={payrollReport}
                  rowKey="employee_id"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showTotal: (total) => `إجمالي ${total} موظف`
                  }}
                />
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <DollarOutlined style={{ fontSize: 48, color: '#ccc' }} />
                <div style={{ marginTop: 16, fontSize: 16 }}>لا توجد بيانات رواتب</div>
                <div style={{ marginTop: 8, color: '#666' }}>انقر على &quot;إنشاء التقرير&quot; لعرض بيانات الرواتب والمكافآت</div>
              </div>
                  )}
                </div>
              )
            },
            {
              key: 'leaves',
              label: (
                <span>
                  <CalendarOutlined />
                  تقرير الإجازات
                </span>
              ),
              children: (
                <div>
                  {leaveReport.length > 0 ? (
              <>
                {/* إحصائيات سريعة للإجازات */}
                <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="إجمالي أيام الإجازات"
                        value={leaveReport.reduce((sum, emp) => sum + emp.total_leave_days, 0)}
                        suffix="يوم"
                        valueStyle={{ color: '#1890ff' }}
                        prefix={<CalendarOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="الإجازات السنوية"
                        value={leaveReport.reduce((sum, emp) => sum + emp.annual_leaves, 0)}
                        suffix="يوم"
                        valueStyle={{ color: '#52c41a' }}
                        prefix={<CalendarOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="الإجازات المرضية"
                        value={leaveReport.reduce((sum, emp) => sum + emp.sick_leaves, 0)}
                        suffix="يوم"
                        valueStyle={{ color: '#ff4d4f' }}
                        prefix={<MedicineBoxOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="الإجازات الطارئة"
                        value={leaveReport.reduce((sum, emp) => sum + emp.emergency_leaves, 0)}
                        suffix="يوم"
                        valueStyle={{ color: '#fa8c16' }}
                        prefix={<ExclamationCircleOutlined />}
                      />
                    </Card>
                  </Col>
                </Row>

                <Table
                  columns={leaveColumns}
                  dataSource={leaveReport}
                  rowKey="employee_id"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showTotal: (total) => `إجمالي ${total} موظف`
                  }}
                />
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <CalendarOutlined style={{ fontSize: 48, color: '#ccc' }} />
                <div style={{ marginTop: 16, fontSize: 16 }}>لا توجد بيانات إجازات</div>
                <div style={{ marginTop: 8, color: '#666' }}>انقر على &quot;إنشاء التقرير&quot; لعرض بيانات الإجازات والغياب</div>
              </div>
                  )}
                </div>
              )
            },
            {
              key: 'performance',
              label: (
                <span>
                  <TrophyOutlined />
                  تقرير الأداء والتقييم
                </span>
              ),
              children: (
                <div>
                  {activeTab === 'performance' && (
              <>
                <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="متوسط التقييم"
                        value={4.2}
                        precision={1}
                        suffix="/5"
                        valueStyle={{ color: '#52c41a' }}
                        prefix={<TrophyOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="الموظفين المتميزين"
                        value={8}
                        valueStyle={{ color: '#1890ff' }}
                        prefix={<UserOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="معدل الإنجاز"
                        value={87}
                        suffix="%"
                        valueStyle={{ color: '#722ed1' }}
                        prefix={<BarChartOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="التحسن الشهري"
                        value={12}
                        suffix="%"
                        valueStyle={{ color: '#fa8c16' }}
                        prefix={<TrophyOutlined />}
                      />
                    </Card>
                  </Col>
                </Row>

                <div style={{ textAlign: 'center', padding: '50px' }}>
                  <TrophyOutlined style={{ fontSize: 48, color: '#ccc' }} />
                  <div style={{ marginTop: 16 }}>تقرير الأداء والتقييم قيد التطوير</div>
                  <Text type="secondary">سيتم إضافة تفاصيل شاملة لتقييم أداء الموظفين قريباً</Text>
                </div>
              </>
                  )}
                </div>
              )
            },
            {
              key: 'overtime',
              label: (
                <span>
                  <ClockCircleOutlined />
                  تقرير الساعات الإضافية
                </span>
              ),
              children: (
                <div>
                  {activeTab === 'overtime' && (
                    <>
                <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="إجمالي الساعات الإضافية"
                        value={attendanceReport.reduce((sum, emp) => sum + emp.overtime_hours, 0)}
                        suffix="ساعة"
                        valueStyle={{ color: '#1890ff' }}
                        prefix={<ClockCircleOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="متوسط الساعات الإضافية"
                        value={attendanceReport.length > 0 ?
                          Math.round((attendanceReport.reduce((sum, emp) => sum + emp.overtime_hours, 0) / attendanceReport.length) * 10) / 10 : 0}
                        suffix="ساعة"
                        valueStyle={{ color: '#52c41a' }}
                        prefix={<UserOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="إجمالي مبلغ الساعات الإضافية"
                        value={payrollReport.reduce((sum, emp) => sum + emp.overtime_amount, 0)}
                        suffix="₪"
                        valueStyle={{ color: '#722ed1' }}
                        prefix={<DollarOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={6}>
                    <Card size="small">
                      <Statistic
                        title="الموظفين بساعات إضافية"
                        value={attendanceReport.filter(emp => emp.overtime_hours > 0).length}
                        valueStyle={{ color: '#fa8c16' }}
                        prefix={<TeamOutlined />}
                      />
                    </Card>
                  </Col>
                </Row>

                <Table
                  columns={[
                    {
                      title: 'رقم الموظف',
                      dataIndex: 'employee_code',
                      key: 'employee_code'
                    },
                    {
                      title: 'اسم الموظف',
                      dataIndex: 'employee_name',
                      key: 'employee_name'
                    },
                    {
                      title: 'القسم',
                      dataIndex: 'department_name',
                      key: 'department_name'
                    },
                    {
                      title: 'الساعات الإضافية',
                      dataIndex: 'overtime_hours',
                      key: 'overtime_hours',
                      render: (hours) => hours > 0 ? (
                        <Tag color="blue">{hours} ساعة</Tag>
                      ) : '-',
                      sorter: (a, b) => a.overtime_hours - b.overtime_hours
                    },
                    {
                      title: 'مبلغ الساعات الإضافية',
                      key: 'overtime_amount',
                      render: (_, record) => {
                        const payrollRecord = payrollReport.find(p => p.employee_id === record.employee_id);
                        return payrollRecord?.overtime_amount ?
                          `${payrollRecord.overtime_amount.toLocaleString()} ₪` : '-';
                      },
                      sorter: (a, b) => {
                        const aAmount = payrollReport.find(p => p.employee_id === a.employee_id)?.overtime_amount || 0;
                        const bAmount = payrollReport.find(p => p.employee_id === b.employee_id)?.overtime_amount || 0;
                        return aAmount - bAmount;
                      }
                    }
                  ]}
                  dataSource={attendanceReport.filter(emp => emp.overtime_hours > 0)}
                  rowKey="employee_id"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showTotal: (total) => `إجمالي ${total} موظف`
                  }}
                />
              </>
                  )}
                </div>
              )
            }
          ]}
        />
      </Card>
    </div>
  )
}

export default EmployeeReports
