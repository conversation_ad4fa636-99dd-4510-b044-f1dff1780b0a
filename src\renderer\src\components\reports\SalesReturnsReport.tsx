import React from 'react';
import { Tag, Typography, Progress, Statistic, Tooltip } from 'antd';
import {
  ShoppingCartOutlined,
  UserOutlined,
  DollarOutlined,
  CalendarOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';
import UniversalReport from './UniversalReport';

import type { ReportData, ReportType } from '../../types/reports';
import { createDefaultFilters } from '../../types/reports';
import { SafeLogger as Logger } from '../../utils/logger';

const { Text } = Typography;

const SalesReturnsReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      Logger.info('SalesReturnsReport', '🔄 بدء إنشاء تقرير مرتجعات المبيعات...');
      Logger.info('SalesReturnsReport', '🔍 الفلاتر المطبقة:', filters);

      let returnsData: any[] = [];

      if (!window.electronAPI || !window.electronAPI.getSalesReturnsAndDiscounts) {
        Logger.info('SalesReturnsReport', '⚠️ التطبيق يعمل في وضع المتصفح أو الدالة غير متوفرة - استخدام بيانات وهمية');

        // بيانات وهمية للمرتجعات
        returnsData = [
          {
            return_id: 1,
            return_number: 'RET-2024-001',
            customer_name: 'شركة الأمل التجارية',
            customer_code: 'CUST001',
            return_date: '2024-01-15',
            original_sale_date: '2024-01-10',
            return_reason: 'عيب في المنتج',
            return_status: 'approved',
            total_items: 3,
            total_quantity: 15,
            total_return_amount: 2500.00,
            refund_amount: 2500.00,
            refund_status: 'completed',
            processing_days: 2,
            return_rate: 8.5
          },
          {
            return_id: 2,
            return_number: 'RET-2024-002',
            customer_name: 'مؤسسة النور للتجارة',
            customer_code: 'CUST002',
            return_date: '2024-01-18',
            original_sale_date: '2024-01-12',
            return_reason: 'طلب العميل',
            return_status: 'pending',
            total_items: 2,
            total_quantity: 8,
            total_return_amount: 1800.00,
            refund_amount: 0.00,
            refund_status: 'pending',
            processing_days: 5,
            return_rate: 12.3
          },
          {
            return_id: 3,
            return_number: 'RET-2024-003',
            customer_name: 'شركة البركة للاستيراد',
            customer_code: 'CUST003',
            return_date: '2024-01-20',
            original_sale_date: '2024-01-14',
            return_reason: 'تلف أثناء الشحن',
            return_status: 'approved',
            total_items: 1,
            total_quantity: 5,
            total_return_amount: 950.00,
            refund_amount: 950.00,
            refund_status: 'completed',
            processing_days: 1,
            return_rate: 15.8
          },
          {
            return_id: 4,
            return_number: 'RET-2024-004',
            customer_name: 'مجموعة الفجر التجارية',
            customer_code: 'CUST004',
            return_date: '2024-01-22',
            original_sale_date: '2024-01-16',
            return_reason: 'مواصفات غير مطابقة',
            return_status: 'rejected',
            total_items: 4,
            total_quantity: 20,
            total_return_amount: 3200.00,
            refund_amount: 0.00,
            refund_status: 'rejected',
            processing_days: 3,
            return_rate: 18.2
          }
        ];
      } else {
        // استعلام البيانات من قاعدة البيانات
        const response = await window.electronAPI.getSalesReturnsAndDiscounts({
          customerId: filters.customerId,
          dateRange: filters.dateRange,
          returnStatus: filters.returnStatus
        });

        if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
          throw new Error('لا توجد بيانات متاحة');
        }

        returnsData = response.data;
      }

      // تعريف الأعمدة
      const columns = [
        {
          title: 'رقم المرتجع',
          key: 'return_number',
          format: 'text' as const,
          render: (record: any) => (
            <div>
              <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
                <ShoppingCartOutlined style={{ marginLeft: '8px' }} />
                {record.return_number}
              </div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {new Date(record.return_date).toLocaleDateString('ar-EG')}
              </Text>
            </div>
          ),
          width: 150
        },
        {
          title: 'العميل',
          key: 'customer',
          render: (record: any) => (
            <div>
              <div style={{ fontWeight: 'bold' }}>
                <UserOutlined style={{ marginLeft: '8px' }} />
                {record.customer_name}
              </div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.customer_code}
              </Text>
            </div>
          ),
          width: 200
        },
        {
          title: 'سبب الإرجاع',
          key: 'return_reason',
          format: 'text' as const,
          render: (record: any) => (
            <Tag color="orange">
              {record.return_reason}
            </Tag>
          ),
          width: 150,
          align: 'center' as const
        },
        {
          title: 'حالة المرتجع',
          key: 'return_status',
          render: (record: any) => {
            const status = record.return_status;
            let color = 'blue';
            let icon = <ExclamationCircleOutlined />;
            let text = 'غير محدد';
            
            if (status === 'approved') {
              color = 'green';
              icon = <CheckCircleOutlined />;
              text = 'موافق عليه';
            } else if (status === 'pending') {
              color = 'orange';
              icon = <ExclamationCircleOutlined />;
              text = 'قيد المراجعة';
            } else if (status === 'rejected') {
              color = 'red';
              icon = <CloseCircleOutlined />;
              text = 'مرفوض';
            }
            
            return (
              <Tag color={color} icon={icon}>
                {text}
              </Tag>
            );
          },
          width: 120,
          align: 'center' as const
        },
        {
          title: 'الكمية',
          key: 'total_quantity',
          format: 'number' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <strong style={{ color: '#1890ff', fontSize: '16px' }}>
                {record.total_quantity}
              </strong>
              <div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  قطعة
                </Text>
              </div>
            </div>
          ),
          width: 100,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'قيمة المرتجع',
          key: 'total_return_amount',
          format: 'currency' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <DollarOutlined style={{ color: '#722ed1', marginLeft: '4px' }} />
              <strong style={{ color: '#722ed1', fontSize: '16px' }}>
                {record.total_return_amount.toLocaleString('ar-EG', { minimumFractionDigits: 2 })}
              </strong>
              <div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  ج.م
                </Text>
              </div>
            </div>
          ),
          width: 140,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'حالة الاسترداد',
          key: 'refund_status',
          render: (record: any) => {
            const status = record.refund_status;
            let color = 'blue';
            let icon = <ExclamationCircleOutlined />;
            let text = 'غير محدد';
            
            if (status === 'completed') {
              color = 'green';
              icon = <CheckCircleOutlined />;
              text = 'مكتمل';
            } else if (status === 'pending') {
              color = 'orange';
              icon = <ExclamationCircleOutlined />;
              text = 'معلق';
            } else if (status === 'rejected') {
              color = 'red';
              icon = <CloseCircleOutlined />;
              text = 'مرفوض';
            }
            
            return (
              <Tag color={color} icon={icon}>
                {text}
              </Tag>
            );
          },
          width: 120,
          align: 'center' as const
        },
        {
          title: 'معدل الإرجاع',
          key: 'return_rate',
          format: 'percentage' as const,
          render: (record: any) => {
            const rate = record.return_rate;
            const color = rate >= 20 ? '#ff4d4f' :
                         rate >= 15 ? '#fa8c16' :
                         rate >= 10 ? '#1890ff' : '#52c41a';
            return (
              <div style={{ textAlign: 'center' }}>
                <div>
                  <TrophyOutlined style={{ color, marginLeft: '4px' }} />
                  <strong style={{ color, fontSize: '16px' }}>{rate.toFixed(1)}%</strong>
                </div>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  معدل
                </Text>
              </div>
            );
          },
          width: 120,
          align: 'center' as const,
          sortable: true
        }
      ];

      // حساب الإحصائيات
      const totalReturns = returnsData.length;
      const totalReturnAmount = returnsData.reduce((sum, ret) => sum + (ret.total_return_amount || 0), 0);
      const totalRefundAmount = returnsData.reduce((sum, ret) => sum + (ret.refund_amount || 0), 0);
      const approvedReturns = returnsData.filter(ret => ret.return_status === 'approved').length;
      const pendingReturns = returnsData.filter(ret => ret.return_status === 'pending').length;
      const rejectedReturns = returnsData.filter(ret => ret.return_status === 'rejected').length;
      const avgReturnRate = totalReturns > 0 ? 
        returnsData.reduce((sum, ret) => sum + (ret.return_rate || 0), 0) / totalReturns : 0;

      Logger.info('SalesReturnsReport', `✅ تم إنشاء تقرير مرتجعات المبيعات بنجاح: ${totalReturns} مرتجع`);

      return {
        title: 'تقرير مرتجعات المبيعات',
        data: returnsData,
        columns,
        summary: {
          totalReturns,
          totalReturnAmount: Math.round(totalReturnAmount * 100) / 100,
          totalRefundAmount: Math.round(totalRefundAmount * 100) / 100,
          approvedReturns,
          pendingReturns,
          rejectedReturns,
          avgReturnRate: Math.round(avgReturnRate * 10) / 10,
          approvalRate: totalReturns > 0 ? Math.round((approvedReturns / totalReturns) * 100 * 10) / 10 : 0
        },
        metadata: {
          reportType: 'sales_returns' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: returnsData.length
        }
      };
    } catch (error) {
      Logger.error('SalesReturnsReport', 'خطأ في إنشاء تقرير مرتجعات المبيعات:', error);
      throw new Error('فشل في إنشاء تقرير مرتجعات المبيعات');
    }
  };

  return (
    <UniversalReport
      reportType={'sales_returns' as ReportType}
      title="تقرير مرتجعات المبيعات"
      description="تقرير شامل لمرتجعات المبيعات مع أسباب الإرجاع وحالات الاسترداد"
      onGenerateReport={generateReport}
      defaultFilters={createDefaultFilters('sales_returns')}
      showDateRange={true}
      showCustomerFilter={true}
      showStatusFilter={true}
      showAdvancedSearch={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default SalesReturnsReport;
