import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>, Card, Row, Col, Progress, Button, Typography, Space, Tag, 
  List, Avatar, Divider, Steps, Timeline, Tooltip, Badge, Rate,
  Tabs, Alert, Statistic
} from 'antd'
import { logger as Logger } from './../../utils/logger'
import {
  PlayCircleOutlined, BookOutlined, TrophyOutlined, ClockCircleOutlined,
  CheckCircleOutlined, StarOutlined, RocketOutlined, ToolOutlined,
  InboxOutlined, ShoppingCartOutlined, ShopOutlined, BankOutlined,
  TeamOutlined, FileTextOutlined, SoundOutlined, SettingOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { tutorialSystem, TutorialModule } from '../../utils/tutorialSystem'
import { audioSystem } from '../../utils/audioSystem'

const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs
const { Step } = Steps

const StyledModal = styled(Modal)`
  .ant-modal-content {
    direction: rtl;
  }
  
  .ant-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px 8px 0 0;
    
    .ant-modal-title {
      color: white;
      font-weight: bold;
    }
  }
`

const ModuleCard = styled(Card)`
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
  
  .ant-card-head {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 12px 12px 0 0;
    
    .ant-card-head-title {
      color: white;
      font-weight: bold;
    }
  }
  
  &.completed {
    .ant-card-head {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
  }
  
  &.production {
    .ant-card-head {
      background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
  }
`

const CategoryTag = styled(Tag)`
  border-radius: 20px;
  padding: 4px 12px;
  font-weight: bold;
  
  &.basic { background: #e6f7ff; color: #1890ff; border-color: #91d5ff; }
  &.intermediate { background: #fff7e6; color: #fa8c16; border-color: #ffd591; }
  &.advanced { background: #fff1f0; color: #f5222d; border-color: #ffa39e; }
  &.production { background: #f6ffed; color: #52c41a; border-color: #b7eb8f; }
`

interface TutorialCenterProps {
  visible: boolean
  onClose: () => void
}

const TutorialCenter: React.FC<TutorialCenterProps> = ({ visible, onClose }) => {
  const [modules, setModules] = useState<TutorialModule[]>([])
  const [activeTab, setActiveTab] = useState('overview')
  const [selectedModule, setSelectedModule] = useState<TutorialModule | null>(null)
  const [audioSettings, setAudioSettings] = useState(audioSystem.getSettings())

  useEffect(() => {
    loadModules()
  }, [])

  const loadModules = () => {
    const allModules = tutorialSystem.getModules()
    setModules(allModules)
  }

  const startModule = async (moduleId: string) => {
    try {
      audioSystem.playSound('click')
      const success = tutorialSystem.startModule(moduleId)
      if (success) {
        onClose()
        audioSystem.playSound('success')
      }
    } catch (error: any) {
      Logger.error('TutorialCenter', 'خطأ في بدء الوحدة:', error)
      // يمكن إضافة إشعار هنا
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'basic': return <BookOutlined />
      case 'intermediate': return <RocketOutlined />
      case 'advanced': return <TrophyOutlined />
      case 'production': return <ToolOutlined />
      default: return <BookOutlined />
    }
  }

  const getModuleIcon = (moduleId: string) => {
    switch (moduleId) {
      case 'introduction': return <PlayCircleOutlined />
      case 'inventory': return <InboxOutlined />
      case 'sales': return <ShoppingCartOutlined />
      case 'purchases': return <ShopOutlined />
      case 'production': return <ToolOutlined />
      case 'finance': return <BankOutlined />
      case 'employees': return <TeamOutlined />
      default: return <FileTextOutlined />
    }
  }

  const renderOverview = () => (
    <div>
      <Alert
        message="مرحباً بك في مركز التعليم الشامل!"
        description="هذا المركز يوفر لك دليلاً تفاعلياً شاملاً لتعلم جميع ميزات البرنامج بطريقة مشوقة واحترافية"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />
      
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Statistic
            title="إجمالي الوحدات"
            value={modules.length}
            prefix={<BookOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="الوحدات المكتملة"
            value={modules.filter(m => m.isCompleted).length}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="الوقت المقدر الإجمالي"
            value={modules.reduce((total, m) => total + m.estimatedTime, 0)}
            suffix="دقيقة"
            prefix={<ClockCircleOutlined />}
            valueStyle={{ color: '#fa8c16' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="التقدم العام"
            value={Math.round((modules.filter(m => m.isCompleted).length / modules.length) * 100)}
            suffix="%"
            prefix={<TrophyOutlined />}
            valueStyle={{ color: '#722ed1' }}
          />
        </Col>
      </Row>

      <Title level={4}>مسار التعلم الموصى به</Title>
      <Steps direction="vertical" size="small" current={modules.findIndex(m => !m.isCompleted)}>
        {modules.map((module, index) => (
          <Step
            key={module.id}
            title={module.title}
            description={module.description}
            status={module.isCompleted ? 'finish' : index === modules.findIndex(m => !m.isCompleted) ? 'process' : 'wait'}
            icon={getModuleIcon(module.id)}
          />
        ))}
      </Steps>
    </div>
  )

  const renderModules = () => (
    <div>
      <Row gutter={[16, 16]}>
        {modules.map(module => (
          <Col span={12} key={module.id}>
            <ModuleCard
              className={`${module.isCompleted ? 'completed' : ''} ${module.category === 'production' ? 'production' : ''}`}
              title={
                <Space>
                  {getModuleIcon(module.id)}
                  {module.title}
                  {module.isCompleted && <CheckCircleOutlined style={{ color: '#52c41a' }} />}
                </Space>
              }
              extra={
                <CategoryTag className={module.category}>
                  {getCategoryIcon(module.category)}
                  {module.category === 'basic' && 'أساسي'}
                  {module.category === 'intermediate' && 'متوسط'}
                  {module.category === 'advanced' && 'متقدم'}
                  {module.category === 'production' && 'إنتاج'}
                </CategoryTag>
              }
              actions={[
                <Button
                  key="start"
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={() => startModule(module.id)}
                  disabled={module.prerequisites?.some(prereq => !tutorialSystem.isModuleCompleted(prereq))}
                >
                  {module.isCompleted ? 'إعادة التشغيل' : 'بدء التعلم'}
                </Button>,
                <Button
                  key="details"
                  icon={<FileTextOutlined />}
                  onClick={() => setSelectedModule(module)}
                >
                  التفاصيل
                </Button>
              ]}
            >
              <Paragraph ellipsis={{ rows: 2 }}>{module.description}</Paragraph>
              
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text type="secondary">
                    <ClockCircleOutlined /> {module.estimatedTime} دقيقة
                  </Text>
                  <Text type="secondary">
                    {module.steps.length} خطوة
                  </Text>
                </div>
                
                <Progress
                  percent={tutorialSystem.getProgress(module.id)}
                  size="small"
                  status={module.isCompleted ? 'success' : 'active'}
                />
                
                {module.prerequisites && module.prerequisites.length > 0 && (
                  <div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      المتطلبات المسبقة:
                    </Text>
                    <div style={{ marginTop: 4 }}>
                      {module.prerequisites.map(prereq => {
                        const prereqModule = modules.find(m => m.id === prereq)
                        const isCompleted = tutorialSystem.isModuleCompleted(prereq)
                        return (
                          <Tag
                            key={prereq}
                            color={isCompleted ? 'green' : 'orange'}
                            style={{ marginBottom: 4, fontSize: '12px' }}
                          >
                            {isCompleted ? <CheckCircleOutlined /> : <ClockCircleOutlined />}
                            {prereqModule?.title || prereq}
                          </Tag>
                        )
                      })}
                    </div>
                  </div>
                )}
              </Space>
            </ModuleCard>
          </Col>
        ))}
      </Row>
    </div>
  )

  const renderProductionGuide = () => {
    const productionModule = modules.find(m => m.id === 'production')
    
    return (
      <div>
        <Alert
          message="الدورة المستندية الإنتاجية - دليل شامل"
          description="هذا القسم يوفر شرحاً تفصيلياً للدورة المستندية الإنتاجية وكيفية تطبيقها في البرنامج"
          type="success"
          showIcon
          style={{ marginBottom: 24 }}
        />

        {productionModule && (
          <Card
            title={
              <Space>
                <ToolOutlined />
                {productionModule.title}
                <Badge count={productionModule.steps.length} style={{ backgroundColor: '#52c41a' }} />
              </Space>
            }
            extra={
              <Button
                type="primary"
                size="large"
                icon={<PlayCircleOutlined />}
                onClick={() => startModule('production')}
              >
                بدء التعلم التفاعلي
              </Button>
            }
          >
            <Timeline mode="left">
              {productionModule.steps.map((step, index) => (
                <Timeline.Item
                  key={step.id}
                  color={index === 0 ? 'green' : 'blue'}
                  dot={index === 0 ? <StarOutlined /> : undefined}
                >
                  <div style={{ marginBottom: 16 }}>
                    <Title level={5}>{step.title}</Title>
                    <Paragraph>{step.description}</Paragraph>
                    {step.duration && (
                      <Text type="secondary">
                        <ClockCircleOutlined /> مدة الخطوة: {step.duration} ثانية
                      </Text>
                    )}
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        )}

        <Divider />

        <Title level={4}>نصائح مهمة للدورة المستندية الإنتاجية</Title>
        <List
          dataSource={[
            'ابدأ بتحديد المواد الخام المطلوبة بدقة',
            'أنشئ وصفات إنتاج مفصلة لكل منتج',
            'تتبع مراحل الإنتاج خطوة بخطوة',
            'طبق معايير مراقبة الجودة في كل مرحلة',
            'احسب التكاليف الفعلية مقابل المعيارية',
            'وثق جميع العمليات للمراجعة المستقبلية'
          ]}
          renderItem={(item, index) => (
            <List.Item>
              <Space>
                <Avatar size="small" style={{ backgroundColor: '#1890ff' }}>
                  {index + 1}
                </Avatar>
                <Text>{item}</Text>
              </Space>
            </List.Item>
          )}
        />
      </div>
    )
  }

  const renderAudioSettings = () => (
    <div>
      <Title level={4}>إعدادات النّام الصوتي</Title>
      <Card>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text>تفعيل الأصوات</Text>
            <Button
              type={audioSettings.enabled ? 'primary' : 'default'}
              icon={<SoundOutlined />}
              onClick={() => {
                if (audioSettings.enabled) {
                  audioSystem.disable()
                } else {
                  audioSystem.enable()
                }
                setAudioSettings(audioSystem.getSettings())
              }}
            >
              {audioSettings.enabled ? 'مفعل' : 'معطل'}
            </Button>
          </div>
          
          <Divider />
          
          <div>
            <Text>مستوى الصوت: {Math.round(audioSettings.volume * 100)}%</Text>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={audioSettings.volume}
              onChange={(e) => {
                audioSystem.setVolume(parseFloat(e.target.value))
                setAudioSettings(audioSystem.getSettings())
              }}
              style={{ width: '100%', marginTop: 8 }}
            />
          </div>
          
          <Divider />
          
          <Space direction="vertical">
            <Text strong>اختبار الأصوات:</Text>
            <Space wrap>
              <Button onClick={() => audioSystem.playSound('click')}>صوت النقر</Button>
              <Button onClick={() => audioSystem.playSound('success')}>صوت النجاح</Button>
              <Button onClick={() => audioSystem.playSound('error')}>صوت الخطأ</Button>
              <Button onClick={() => audioSystem.playSound('warning')}>صوت التحذير</Button>
              <Button onClick={() => audioSystem.playSound('notification')}>صوت الإشعار</Button>
            </Space>
          </Space>
        </Space>
      </Card>
    </div>
  )

  return (
    <StyledModal
      title={
        <Space>
          <BookOutlined />
          مركز التعليم الشامل
          <Badge count="جديد" style={{ backgroundColor: '#52c41a' }} />
        </Space>
      }
      visible={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
      destroyOnClose
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        size="large"
        items={[
          {
            key: 'overview',
            label: (
              <Space>
                <TrophyOutlined />
                نظرة عامة
              </Space>
            ),
            children: renderOverview()
          },
          {
            key: 'modules',
            label: (
              <Space>
                <BookOutlined />
                الوحدات التعليمية
              </Space>
            ),
            children: renderModules()
          },
          {
            key: 'production',
            label: (
              <Space>
                <ToolOutlined />
                الدورة المستندية الإنتاجية
              </Space>
            ),
            children: renderProductionGuide()
          },
          {
            key: 'audio',
            label: (
              <Space>
                <SoundOutlined />
                إعدادات الصوت
              </Space>
            ),
            children: renderAudioSettings()
          }
        ]}
      />

      {/* نافذة تفاصيل الوحدة */}
      <Modal
        title={selectedModule?.title}
        open={!!selectedModule}
        onCancel={() => setSelectedModule(null)}
        footer={[
          <Button key="close" onClick={() => setSelectedModule(null)}>
            إغلاق
          </Button>,
          <Button
            key="start"
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={() => {
              if (selectedModule) {
                startModule(selectedModule.id)
                setSelectedModule(null)
              }
            }}
          >
            بدء التعلم
          </Button>
        ]}
      >
        {selectedModule && (
          <div>
            <Paragraph>{selectedModule.description}</Paragraph>
            <Divider />
            <Title level={5}>خطوات التعلم:</Title>
            <List
              dataSource={selectedModule.steps}
              renderItem={(step, index) => (
                <List.Item>
                  <Space>
                    <Avatar size="small" style={{ backgroundColor: '#1890ff' }}>
                      {index + 1}
                    </Avatar>
                    <div>
                      <Text strong>{step.title}</Text>
                      <br />
                      <Text type="secondary">{step.description}</Text>
                    </div>
                  </Space>
                </List.Item>
              )}
            />
          </div>
        )}
      </Modal>
    </StyledModal>
  )
}

export default TutorialCenter
