import { Logger } from './logger'
import { sanitizeFormData, validateRequiredIds, logFormData } from './dataTypeUtils'

/**
 * دالة شاملة للتحقق من صحة بيانات النماذج
 * تستخدم في جميع الخدمات لضمان توحيد المعالجة
 */
export class FormDataValidator {
  
  /**
   * التحقق من صحة بيانات فاتورة الشراء
   */
  static validatePurchaseInvoiceData(data: any): { isValid: boolean; sanitizedData?: any; message?: string } {
    try {
      // تسجيل البيانات الأصلية
      logFormData('FormDataValidator', 'فاتورة الشراء', data)
      
      // تنظيف البيانات
      const sanitizedData = sanitizeFormData(data)
      
      // التحقق من المعرفات المطلوبة
      const idValidation = validateRequiredIds(sanitizedData, ['supplier_id'])
      if (!idValidation.isValid) {
        return { isValid: false, message: idValidation.message }
      }
      
      // التحقق من البيانات الأساسية
      if (!sanitizedData.invoice_date) {
        return { isValid: false, message: 'تاريخ الفاتورة مطلوب' }
      }
      
      if (!sanitizedData.items || sanitizedData.items.length === 0) {
        return { isValid: false, message: 'يجب إضافة صنف واحد على الأقل' }
      }
      
      // التحقق من بيانات الأصناف
      for (let i = 0; i < sanitizedData.items.length; i++) {
        const item = sanitizedData.items[i]
        
        if (!item.item_id || item.item_id <= 0) {
          return { isValid: false, message: `يجب اختيار صنف صحيح في السطر ${i + 1}` }
        }
        
        if (!item.warehouse_id || item.warehouse_id <= 0) {
          return { isValid: false, message: `يجب اختيار مخزن صحيح في السطر ${i + 1}` }
        }
        
        if (!item.quantity || item.quantity <= 0) {
          return { isValid: false, message: `الكمية يجب أن تكون أكبر من صفر في السطر ${i + 1}` }
        }
        
        if (!item.unit_price || item.unit_price <= 0) {
          return { isValid: false, message: `سعر الوحدة يجب أن يكون أكبر من صفر في السطر ${i + 1}` }
        }
      }
      
      return { isValid: true, sanitizedData }
      
    } catch (error) {
      Logger.error('FormDataValidator', 'خطأ في التحقق من بيانات فاتورة الشراء:', error)
      return { isValid: false, message: 'خطأ في التحقق من البيانات' }
    }
  }
  
  /**
   * التحقق من صحة بيانات أمر الشراء
   */
  static validatePurchaseOrderData(data: any): { isValid: boolean; sanitizedData?: any; message?: string } {
    try {
      // تسجيل البيانات الأصلية
      logFormData('FormDataValidator', 'أمر الشراء', data)
      
      // تنظيف البيانات
      const sanitizedData = sanitizeFormData(data)
      
      // التحقق من المعرفات المطلوبة
      const idValidation = validateRequiredIds(sanitizedData, ['supplier_id'])
      if (!idValidation.isValid) {
        return { isValid: false, message: idValidation.message }
      }
      
      // التحقق من البيانات الأساسية
      if (!sanitizedData.order_date) {
        return { isValid: false, message: 'تاريخ الأمر مطلوب' }
      }
      
      if (!sanitizedData.items || sanitizedData.items.length === 0) {
        return { isValid: false, message: 'يجب إضافة صنف واحد على الأقل' }
      }
      
      // التحقق من بيانات الأصناف
      for (let i = 0; i < sanitizedData.items.length; i++) {
        const item = sanitizedData.items[i]
        
        if (!item.item_id || item.item_id <= 0) {
          return { isValid: false, message: `يجب اختيار صنف صحيح في السطر ${i + 1}` }
        }
        
        if (!item.warehouse_id || item.warehouse_id <= 0) {
          return { isValid: false, message: `يجب اختيار مخزن صحيح في السطر ${i + 1}` }
        }
        
        if (!item.quantity || item.quantity <= 0) {
          return { isValid: false, message: `الكمية يجب أن تكون أكبر من صفر في السطر ${i + 1}` }
        }
        
        if (!item.unit_price || item.unit_price <= 0) {
          return { isValid: false, message: `سعر الوحدة يجب أن يكون أكبر من صفر في السطر ${i + 1}` }
        }
      }
      
      return { isValid: true, sanitizedData }
      
    } catch (error) {
      Logger.error('FormDataValidator', 'خطأ في التحقق من بيانات أمر الشراء:', error)
      return { isValid: false, message: 'خطأ في التحقق من البيانات' }
    }
  }
  
  /**
   * التحقق من صحة بيانات فاتورة المبيعات
   */
  static validateSalesInvoiceData(data: any): { isValid: boolean; sanitizedData?: any; message?: string } {
    try {
      // تسجيل البيانات الأصلية
      logFormData('FormDataValidator', 'فاتورة المبيعات', data)
      
      // تنظيف البيانات
      const sanitizedData = sanitizeFormData(data)
      
      // التحقق من المعرفات المطلوبة
      const idValidation = validateRequiredIds(sanitizedData, ['customer_id'])
      if (!idValidation.isValid) {
        return { isValid: false, message: idValidation.message }
      }
      
      // التحقق من البيانات الأساسية
      if (!sanitizedData.invoice_date) {
        return { isValid: false, message: 'تاريخ الفاتورة مطلوب' }
      }
      
      if (!sanitizedData.items || sanitizedData.items.length === 0) {
        return { isValid: false, message: 'يجب إضافة صنف واحد على الأقل' }
      }
      
      // التحقق من بيانات الأصناف
      for (let i = 0; i < sanitizedData.items.length; i++) {
        const item = sanitizedData.items[i]
        
        if (!item.item_id || item.item_id <= 0) {
          return { isValid: false, message: `يجب اختيار صنف صحيح في السطر ${i + 1}` }
        }
        
        if (!item.warehouse_id || item.warehouse_id <= 0) {
          return { isValid: false, message: `يجب اختيار مخزن صحيح في السطر ${i + 1}` }
        }
        
        if (!item.quantity || item.quantity <= 0) {
          return { isValid: false, message: `الكمية يجب أن تكون أكبر من صفر في السطر ${i + 1}` }
        }
        
        if (!item.unit_price || item.unit_price <= 0) {
          return { isValid: false, message: `سعر الوحدة يجب أن يكون أكبر من صفر في السطر ${i + 1}` }
        }
      }
      
      return { isValid: true, sanitizedData }
      
    } catch (error) {
      Logger.error('FormDataValidator', 'خطأ في التحقق من بيانات فاتورة المبيعات:', error)
      return { isValid: false, message: 'خطأ في التحقق من البيانات' }
    }
  }
  
  /**
   * التحقق من صحة بيانات أمر المبيعات
   */
  static validateSalesOrderData(data: any): { isValid: boolean; sanitizedData?: any; message?: string } {
    try {
      // تسجيل البيانات الأصلية
      logFormData('FormDataValidator', 'أمر المبيعات', data)
      
      // تنظيف البيانات
      const sanitizedData = sanitizeFormData(data)
      
      // التحقق من المعرفات المطلوبة
      const idValidation = validateRequiredIds(sanitizedData, ['customer_id'])
      if (!idValidation.isValid) {
        return { isValid: false, message: idValidation.message }
      }
      
      // التحقق من البيانات الأساسية
      if (!sanitizedData.order_date) {
        return { isValid: false, message: 'تاريخ الأمر مطلوب' }
      }
      
      if (!sanitizedData.items || sanitizedData.items.length === 0) {
        return { isValid: false, message: 'يجب إضافة صنف واحد على الأقل' }
      }
      
      // التحقق من بيانات الأصناف
      for (let i = 0; i < sanitizedData.items.length; i++) {
        const item = sanitizedData.items[i]
        
        if (!item.item_id || item.item_id <= 0) {
          return { isValid: false, message: `يجب اختيار صنف صحيح في السطر ${i + 1}` }
        }
        
        if (!item.warehouse_id || item.warehouse_id <= 0) {
          return { isValid: false, message: `يجب اختيار مخزن صحيح في السطر ${i + 1}` }
        }
        
        if (!item.quantity || item.quantity <= 0) {
          return { isValid: false, message: `الكمية يجب أن تكون أكبر من صفر في السطر ${i + 1}` }
        }
        
        if (!item.unit_price || item.unit_price <= 0) {
          return { isValid: false, message: `سعر الوحدة يجب أن يكون أكبر من صفر في السطر ${i + 1}` }
        }
      }
      
      return { isValid: true, sanitizedData }
      
    } catch (error) {
      Logger.error('FormDataValidator', 'خطأ في التحقق من بيانات أمر المبيعات:', error)
      return { isValid: false, message: 'خطأ في التحقق من البيانات' }
    }
  }
}
