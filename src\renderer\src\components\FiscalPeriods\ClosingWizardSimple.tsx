import React, { useState, useEffect } from 'react';
import {
  Modal,
  Steps,
  Button,
  Space,
  Alert,
  Progress,
  Typography,
  Card,
  List,
  notification,
  Result,
  Row,
  Col,
  Statistic,
  Tag,
  Timeline,
  Divider,
  Spin,
  Tooltip,
  Badge
} from 'antd';
import {
  CheckCircleOutlined,
  LoadingOutlined,
  ExclamationCircleOutlined,
  SafetyOutlined,
  DatabaseOutlined,
  LockOutlined,
  SyncOutlined,
  FileTextOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
  BankOutlined,
  AuditOutlined
} from '@ant-design/icons';
import { FiscalPeriod, ValidationResult, ClosingProgress } from '../../types/fiscalPeriod';
import { fiscalPeriodApi } from '../../services/fiscalPeriodApi';

const { Title, Text } = Typography;
const { Step } = Steps;

interface ClosingWizardProps {
  period: FiscalPeriod;
  open: boolean;
  onClose: () => void;
  onComplete?: () => void;
}

const ClosingWizard: React.FC<ClosingWizardProps> = ({
  period,
  open,
  onClose,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [completed, setCompleted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  // حالات متقدمة للمعالج
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [stepStatus, setStepStatus] = useState<('wait' | 'process' | 'finish' | 'error')[]>(
    new Array(5).fill('wait')
  );
  const [stepDetails, setStepDetails] = useState<string[]>([]);
  const [processingTime, setProcessingTime] = useState(0);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [backupInfo, setBackupInfo] = useState<any>(null);
  const [closingEntries, setClosingEntries] = useState<any[]>([]);
  const [carriedForwardAccounts, setCarriedForwardAccounts] = useState<number>(0);

  const steps = [
    {
      title: 'التحقق من البيانات',
      description: 'فحص شامل لصحة البيانات قبل الإقفال',
      icon: <SafetyOutlined />,
      details: [
        'فحص توازن ميزان المراجعة',
        'التحقق من القيود غير المرحلة',
        'فحص المعاملات المصرفية',
        'التحقق من الفواتير المعلقة',
        'فحص أرصدة المخزون'
      ]
    },
    {
      title: 'النسخ الاحتياطي',
      description: 'إنشاء نسخة احتياطية آمنة ومشفرة',
      icon: <DatabaseOutlined />,
      details: [
        'إنشاء نسخة احتياطية كاملة',
        'تشفير البيانات',
        'التحقق من سلامة النسخة',
        'حفظ في مجلد آمن'
      ]
    },
    {
      title: 'إقفال الحسابات',
      description: 'إقفال حسابات الإيرادات والمصروفات',
      icon: <LockOutlined />,
      details: [
        'إقفال حسابات الإيرادات',
        'إقفال حسابات المصروفات',
        'حساب صافي الربح/الخسارة',
        'إنشاء قيود الإقفال'
      ]
    },
    {
      title: 'ترحيل الأرصدة',
      description: 'ترحيل الأرصدة للفترة المالية الجديدة',
      icon: <SyncOutlined />,
      details: [
        'ترحيل أرصدة الأصول',
        'ترحيل أرصدة الخصوم',
        'ترحيل حقوق الملكية',
        'تحديث الأرصدة الافتتاحية'
      ]
    },
    {
      title: 'إنهاء الإقفال',
      description: 'إنهاء عملية الإقفال وإنشاء التقارير',
      icon: <FileTextOutlined />,
      details: [
        'قفل الفترة نهائياً',
        'إنشاء تقارير الإقفال',
        'حفظ سجل المراجعة',
        'إرسال الإشعارات'
      ]
    }
  ];

  // تأثير لحساب الوقت المنقضي
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (loading && startTime) {
      interval = setInterval(() => {
        setProcessingTime(Math.floor((Date.now() - startTime.getTime()) / 1000));
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [loading, startTime]);

  const updateStepStatus = (stepIndex: number, status: 'wait' | 'process' | 'finish' | 'error', detail?: string) => {
    setStepStatus(prev => {
      const newStatus = [...prev];
      newStatus[stepIndex] = status;
      return newStatus;
    });

    if (detail) {
      setStepDetails(prev => {
        const newDetails = [...prev];
        newDetails[stepIndex] = detail;
        return newDetails;
      });
    }
  };

  const handleStart = async () => {
    setLoading(true);
    setError(null);
    setCurrentStep(0);
    setProgress(0);
    setStartTime(new Date());
    setCompleted(false);

    try {
      // خطوة 1: التحقق من البيانات
      setCurrentStep(0);
      updateStepStatus(0, 'process', 'جاري فحص البيانات...');
      setProgress(5);

      const validation = await fiscalPeriodApi.validatePeriodForClosing(period.id.toString());
      setValidationResults(validation || []);

      const hasErrors = validation?.some(v => v.type === 'error');
      if (hasErrors) {
        updateStepStatus(0, 'error', 'تم العثور على أخطاء في البيانات');
        throw new Error('يجب إصلاح الأخطاء قبل المتابعة');
      }

      updateStepStatus(0, 'finish', 'تم التحقق من البيانات بنجاح');
      setProgress(20);

      // خطوة 2: النسخ الاحتياطي
      setCurrentStep(1);
      updateStepStatus(1, 'process', 'جاري إنشاء النسخة الاحتياطية...');

      // محاكاة إنشاء النسخة الاحتياطية
      await new Promise(resolve => setTimeout(resolve, 2000));

      const backupData = {
        fileName: `backup_${period.period_name}_${new Date().toISOString().split('T')[0]}.sql`,
        size: '125.4 MB',
        location: '/backups/fiscal_periods/',
        encrypted: true
      };
      setBackupInfo(backupData);

      updateStepStatus(1, 'finish', 'تم إنشاء النسخة الاحتياطية بنجاح');
      setProgress(40);

      // خطوة 3: إقفال الحسابات
      setCurrentStep(2);
      updateStepStatus(2, 'process', 'جاري إقفال الحسابات...');

      // محاكاة إقفال الحسابات
      await new Promise(resolve => setTimeout(resolve, 1500));

      const entries = [
        { type: 'revenue_closing', amount: 250000, description: 'إقفال حسابات الإيرادات' },
        { type: 'expense_closing', amount: 180000, description: 'إقفال حسابات المصروفات' },
        { type: 'profit_transfer', amount: 70000, description: 'ترحيل صافي الربح' }
      ];
      setClosingEntries(entries);

      updateStepStatus(2, 'finish', 'تم إقفال الحسابات بنجاح');
      setProgress(60);

      // خطوة 4: ترحيل الأرصدة
      setCurrentStep(3);
      updateStepStatus(3, 'process', 'جاري ترحيل الأرصدة...');

      await new Promise(resolve => setTimeout(resolve, 1000));
      setCarriedForwardAccounts(156);

      updateStepStatus(3, 'finish', 'تم ترحيل الأرصدة بنجاح');
      setProgress(80);

      // خطوة 5: إنهاء الإقفال
      setCurrentStep(4);
      updateStepStatus(4, 'process', 'جاري إنهاء الإقفال...');

      await fiscalPeriodApi.closeFiscalPeriod(period.id, 1);

      updateStepStatus(4, 'finish', 'تم إنهاء الإقفال بنجاح');
      setProgress(100);
      setCompleted(true);
      notification.success({
        message: 'تم الإقفال بنجاح',
        description: `تم إقفال الفترة المحاسبية "${period.period_name}" بنجاح`
      });

      if (onComplete) {
        onComplete();
      }

    } catch (error: any) {
      setError(error.message || 'حدث خطأ أثناء عملية الإقفال');
      notification.error({
        message: 'فشل في الإقفال',
        description: error.message || 'حدث خطأ أثناء عملية الإقفال'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (loading) {
      Modal.confirm({
        title: 'تأكيد الإغلاق',
        content: 'عملية الإقفال جارية. هل أنت متأكد من الإغلاق؟',
        onOk: () => {
          setCurrentStep(0);
          setProgress(0);
          setCompleted(false);
          setError(null);
          onClose();
        }
      });
    } else {
      setCurrentStep(0);
      setProgress(0);
      setCompleted(false);
      setError(null);
      onClose();
    }
  };

  // مكونات العرض المتقدمة
  const renderStepContent = () => {
    if (completed) {
      return (
        <Result
          status="success"
          title="تم إقفال السنة المالية بنجاح"
          subTitle={
            <Space direction="vertical">
              <Text>تم إقفال الفترة &quot;{period.period_name}&quot; بنجاح</Text>
              <Text type="secondary">وقت المعالجة: {processingTime} ثانية</Text>
              {backupInfo && (
                <Text type="secondary">
                  تم إنشاء نسخة احتياطية: {backupInfo.fileName}
                </Text>
              )}
            </Space>
          }
          extra={[
            <Button type="primary" key="reports">
              عرض التقارير
            </Button>,
            <Button key="close" onClick={handleClose}>
              إغلاق
            </Button>
          ]}
        />
      );
    }

    const currentStepData = steps[currentStep];

    return (
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* معلومات الخطوة الحالية */}
        <Card>
          <Row gutter={[16, 16]} align="middle">
            <Col span={4}>
              <div style={{ textAlign: 'center', fontSize: '48px', color: '#1890ff' }}>
                {currentStepData?.icon}
              </div>
            </Col>
            <Col span={20}>
              <Space direction="vertical" size="small">
                <Title level={4} style={{ margin: 0 }}>
                  {currentStepData?.title}
                </Title>
                <Text type="secondary">
                  {currentStepData?.description}
                </Text>
                {loading && (
                  <Space>
                    <Spin size="small" />
                    <Text type="secondary">
                      {stepDetails[currentStep] || 'جاري المعالجة...'}
                    </Text>
                  </Space>
                )}
              </Space>
            </Col>
          </Row>
        </Card>

        {/* تفاصيل الخطوة */}
        {currentStepData?.details && (
          <Card title="تفاصيل العملية" size="small">
            <List
              size="small"
              dataSource={currentStepData.details}
              renderItem={(item, index) => (
                <List.Item>
                  <Space>
                    {stepStatus[currentStep] === 'finish' ? (
                      <CheckCircleOutlined style={{ color: '#52c41a' }} />
                    ) : stepStatus[currentStep] === 'process' && index === 0 ? (
                      <LoadingOutlined style={{ color: '#1890ff' }} />
                    ) : (
                      <ClockCircleOutlined style={{ color: '#d9d9d9' }} />
                    )}
                    <Text>{item}</Text>
                  </Space>
                </List.Item>
              )}
            />
          </Card>
        )}

        {/* إحصائيات سريعة */}
        {loading && (
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Card size="small">
                <Statistic
                  title="الوقت المنقضي"
                  value={processingTime}
                  suffix="ثانية"
                  prefix={<ClockCircleOutlined />}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small">
                <Statistic
                  title="الخطوة الحالية"
                  value={currentStep + 1}
                  suffix={`/ ${steps.length}`}
                  prefix={<AuditOutlined />}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small">
                <Statistic
                  title="نسبة الإنجاز"
                  value={progress}
                  suffix="%"
                  prefix={<BankOutlined />}
                />
              </Card>
            </Col>
          </Row>
        )}
      </Space>
    );
  };

  return (
    <Modal
      title="معالج إقفال السنة المالية"
      open={open}
      onCancel={handleClose}
      width={800}
      footer={null}
      closable={!loading}
      maskClosable={false}
    >
      <div style={{ padding: '20px 0' }}>
        {!completed && !error && (
          <>
            <Card style={{ marginBottom: 20 }}>
              <Title level={4}>الفترة المالية: {period.period_name}</Title>
              <Text type="secondary">
                من {period.start_date} إلى {period.end_date}
              </Text>
            </Card>

            <Steps current={currentStep} direction="vertical">
              {steps.map((step, index) => (
                <Step
                  key={index}
                  title={step.title}
                  description={step.description}
                  status={
                    index < currentStep ? 'finish' :
                    index === currentStep && loading ? 'process' :
                    index === currentStep && error ? 'error' : 'wait'
                  }
                  icon={
                    index < currentStep ? <CheckCircleOutlined /> :
                    index === currentStep && loading ? <LoadingOutlined /> :
                    index === currentStep && error ? <ExclamationCircleOutlined /> : undefined
                  }
                />
              ))}
            </Steps>

            {loading && (
              <div style={{ marginTop: 20 }}>
                <Progress 
                  percent={progress} 
                  status="active"
                  format={(percent) => `${percent}% مكتمل`}
                />
                <Text style={{ display: 'block', marginTop: 10, textAlign: 'center' }}>
                  جاري تنفيذ: {steps[currentStep]?.title}
                </Text>
              </div>
            )}

            {error && (
              <Alert
                message="خطأ في عملية الإقفال"
                description={error}
                type="error"
                style={{ marginTop: 20 }}
                showIcon
              />
            )}
          </>
        )}

        {completed && (
          <Result
            status="success"
            title="تم إقفال السنة المالية بنجاح"
            subTitle={`تم إقفال الفترة "${period.period_name}" وإنشاء جميع التقارير المطلوبة`}
            extra={[
              <Button type="primary" key="reports">
                عرض التقارير
              </Button>,
              <Button key="close" onClick={handleClose}>
                إغلاق
              </Button>
            ]}
          />
        )}

        {!completed && !loading && (
          <div style={{ marginTop: 24, textAlign: 'center' }}>
            <Space size="large">
              <Button
                onClick={handleClose}
                size="large"
              >
                إلغاء
              </Button>
              <Button
                type="primary"
                onClick={handleStart}
                size="large"
                icon={<LockOutlined />}
              >
                بدء عملية الإقفال المتقدم
              </Button>
            </Space>
          </div>
        )}

        {/* معلومات إضافية */}
        {(backupInfo || closingEntries.length > 0 || carriedForwardAccounts > 0) && (
          <Card
            title="ملخص العملية"
            style={{ marginTop: 24 }}
            size="small"
          >
            <Row gutter={[16, 16]}>
              {backupInfo && (
                <Col span={8}>
                  <Statistic
                    title="النسخة الاحتياطية"
                    value={backupInfo.size}
                    prefix={<DatabaseOutlined />}
                  />
                </Col>
              )}
              {closingEntries.length > 0 && (
                <Col span={8}>
                  <Statistic
                    title="قيود الإقفال"
                    value={closingEntries.length}
                    prefix={<FileTextOutlined />}
                  />
                </Col>
              )}
              {carriedForwardAccounts > 0 && (
                <Col span={8}>
                  <Statistic
                    title="الحسابات المرحلة"
                    value={carriedForwardAccounts}
                    prefix={<SyncOutlined />}
                  />
                </Col>
              )}
            </Row>
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default ClosingWizard;
