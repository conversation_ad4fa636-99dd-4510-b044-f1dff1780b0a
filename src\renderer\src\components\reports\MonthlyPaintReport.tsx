import React from 'react';
import { Tag, Typography, Progress } from 'antd';
import UniversalReport from './UniversalReport';
import { DateUtils } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger';
import type { ReportData, ReportType } from '../../types/reports';

const { Text } = Typography;

const MonthlyPaintReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getMonthlyPaintReport({
        year: filters.year,
        dateRange: filters.dateRange
      });

      if (!response || !response.success || !response.data || !Array.isArray(response.data.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const paintData = response.data;
      const data = paintData.data;

      // حساب الإحصائيات
      const totalMonths = data.length;
      const totalOrders = data.reduce((sum, item) => sum + item.total_orders, 0);
      const totalInvoices = data.reduce((sum, item) => sum + item.total_invoices, 0);
      const totalCustomers = data.reduce((sum, item) => sum + item.unique_customers, 0);
      const totalArea = data.reduce((sum, item) => sum + item.total_area, 0);
      const totalRevenue = data.reduce((sum, item) => sum + item.total_revenue, 0);
      const totalPaid = data.reduce((sum, item) => sum + item.paid_amount, 0);

      // حساب معدلات النمو
      const dataWithGrowth = data.map((item, index) => {
        const prevItem = index < data.length - 1 ? data[index + 1] : null;
        const growthRate = prevItem && prevItem.total_revenue > 0 
          ? ((item.total_revenue - prevItem.total_revenue) / prevItem.total_revenue * 100)
          : 0;
        
        return {
          ...item,
          growth_rate: growthRate
        };
      });

      // إعداد البيانات للجدول
      const tableData = dataWithGrowth.map((item, index) => {
        const monthNames = [
          'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
          'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        
        const monthName = monthNames[parseInt(item.month_num) - 1];
        
        return {
          key: item.month,
          index: index + 1,
          month: item.month,
          month_name: `${monthName} ${item.year}`,
          total_orders: item.total_orders,
          total_invoices: item.total_invoices,
          unique_customers: item.unique_customers,
          total_area: item.total_area,
          total_revenue: item.total_revenue,
          paid_amount: item.paid_amount,
          outstanding_amount: item.total_revenue - item.paid_amount,
          avg_area_per_order: item.avg_area_per_order,
          avg_revenue_per_invoice: item.avg_revenue_per_invoice,
          payment_percentage: item.total_revenue > 0 ? (item.paid_amount / item.total_revenue * 100) : 0,
          growth_rate: item.growth_rate
        };
      });

      // تعريف الأعمدة
      const columns = [
        {
          title: '#',
          key: 'index',
          width: 50,
          align: 'center' as const
        },
        {
          title: 'الشهر',
          key: 'month_name',
          width: 120,
          align: 'center' as const
        },
        {
          title: 'عدد الأوامر',
          key: 'total_orders',
          width: 100,
          align: 'center' as const,
          sortable: true,
          render: (record: any) => (
            <Tag color="blue">{record.total_orders}</Tag>
          )
        },
        {
          title: 'عدد الفواتير',
          key: 'total_invoices',
          width: 100,
          align: 'center' as const,
          sortable: true,
          render: (record: any) => (
            <Tag color="green">{record.total_invoices}</Tag>
          )
        },
        {
          title: 'عدد العملاء',
          key: 'unique_customers',
          width: 100,
          align: 'center' as const,
          sortable: true,
          render: (record: any) => (
            <Tag color="orange">{record.unique_customers}</Tag>
          )
        },
        {
          title: 'إجمالي المساحة (م²)',
          key: 'total_area',
          width: 130,
          align: 'right' as const,
          sortable: true,
          render: (record: any) => record.total_area.toLocaleString('ar-EG', { minimumFractionDigits: 2 })
        },
        {
          title: 'إجمالي الإيرادات',
          key: 'total_revenue',
          width: 130,
          align: 'right' as const,
          sortable: true,
          render: (record: any) => (
            <Text strong style={{ color: '#1890ff' }}>
              {record.total_revenue.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'المبلغ المدفوع',
          key: 'paid_amount',
          width: 120,
          align: 'right' as const,
          sortable: true,
          render: (record: any) => (
            <Text style={{ color: '#52c41a' }}>
              {record.paid_amount.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'المبلغ المستحق',
          key: 'outstanding_amount',
          width: 120,
          align: 'right' as const,
          sortable: true,
          render: (record: any) => (
            <Text style={{ color: record.outstanding_amount > 0 ? '#ff4d4f' : '#52c41a' }}>
              {record.outstanding_amount.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'نسبة السداد',
          key: 'payment_percentage',
          width: 120,
          align: 'center' as const,
          sortable: true,
          render: (record: any) => (
            <Progress
              percent={Math.round(record.payment_percentage)}
              size="small"
              status={record.payment_percentage === 100 ? 'success' : record.payment_percentage > 50 ? 'active' : 'exception'}
            />
          )
        },
        {
          title: 'متوسط المساحة/أمر',
          key: 'avg_area_per_order',

          width: 130,
          align: 'right' as const,
          sortable: true,
          render: (record: any) => (
            <Text style={{ color: '#722ed1' }}>
              {record.avg_area_per_order.toFixed(2)} م²
            </Text>
          )
        },
        {
          title: 'متوسط الإيرادات/فاتورة',
          key: 'avg_revenue_per_invoice',

          width: 150,
          align: 'right' as const,
          sortable: true,
          render: (record: any) => (
            <Text style={{ color: '#13c2c2' }}>
              {record.avg_revenue_per_invoice.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'معدل النمو',
          key: 'growth_rate',

          width: 100,
          align: 'center' as const,
          sortable: true,
          render: (record: any) => {
            const value = record.growth_rate;
            const _color = value > 0 ? '#52c41a' : value < 0 ? '#ff4d4f' : '#d9d9d9';
            const icon = value > 0 ? '↗️' : value < 0 ? '↘️' : '➡️';
            return (
              <Tag color={value > 0 ? 'green' : value < 0 ? 'red' : 'default'}>
                {icon} {Math.abs(value).toFixed(1)}%
              </Tag>
            );
          }
        }
      ];

      // إعداد الإحصائيات
      const statistics = [
        {
          title: 'إجمالي الشهور',
          value: totalMonths,
          color: '#1890ff',
          icon: '📅'
        },
        {
          title: 'إجمالي الأوامر',
          value: totalOrders,
          color: '#52c41a',
          icon: '📋'
        },
        {
          title: 'إجمالي الفواتير',
          value: totalInvoices,
          color: '#722ed1',
          icon: '🧾'
        },
        {
          title: 'إجمالي العملاء',
          value: totalCustomers,
          color: '#fa8c16',
          icon: '👥'
        },
        {
          title: 'إجمالي المساحة (م²)',
          value: totalArea.toLocaleString('ar-EG', { minimumFractionDigits: 2 }),
          color: '#eb2f96',
          icon: '📐'
        },
        {
          title: 'إجمالي الإيرادات',
          value: `${totalRevenue.toLocaleString('ar-EG')} ج.م`,
          color: '#13c2c2',
          icon: '💰'
        },
        {
          title: 'إجمالي المدفوع',
          value: `${totalPaid.toLocaleString('ar-EG')} ج.م`,
          color: '#52c41a',
          icon: '✅'
        },
        {
          title: 'متوسط الإيرادات/شهر',
          value: totalMonths > 0 ? `${Math.round(totalRevenue / totalMonths).toLocaleString('ar-EG')} ج.م` : '0 ج.م',
          color: '#f759ab',
          icon: '📊'
        }
      ];

      return {
        title: 'التقرير الشهري للدهان',
        columns,
        data: tableData,
        statistics,
        summary: {
          totalRecords: totalMonths,
          totalRevenue: totalRevenue,
          totalPaid: totalPaid,
          totalOutstanding: totalRevenue - totalPaid
        }
      };

    } catch (error) {
      Logger.error('MonthlyPaintReport', 'خطأ في إنشاء التقرير الشهري للدهان:', error);
      throw new Error('فشل في تحميل بيانات التقرير');
    }
  };

  return (
    <UniversalReport
      reportType={'monthly_paint' as ReportType}
      title="التقرير الشهري للدهان"
      description="تقرير شامل للدهان الشهري مع الإحصائيات والتحليلات"
      onGenerateReport={generateReport}
      showDateRange={true}
    />
  );
};

export default MonthlyPaintReport;
