import { logger as Logger }  from './logger'
/**
 * نّام التعليم الشامل للبرنامج
 * يوفر دليل تفاعلي لجميع أقسام البرنامج مع التركيز على الدورة المستندية الإنتاجية
 */

export interface TutorialStep {
  id: string
  title: string
  description: string
  target?: string
  position?: 'top' | 'bottom' | 'left' | 'right'
  action?: () => void
  nextStep?: string
  prevStep?: string
  isOptional?: boolean
  duration?: number
  videoUrl?: string
  imageUrl?: string
}

export interface TutorialModule {
  id: string
  title: string
  description: string
  icon: string
  category: 'basic' | 'intermediate' | 'advanced' | 'production'
  estimatedTime: number
  steps: TutorialStep[]
  prerequisites?: string[]
  isCompleted?: boolean
  progress?: number
}

export interface TutorialProgress {
  moduleId: string
  stepId: string
  completed: boolean
  timestamp: Date
  timeSpent: number
}

export class TutorialSystem {
  private modules: Map<string, TutorialModule> = new Map()
  private currentModule: string | null = null
  private currentStep: string | null = null
  private progress: TutorialProgress[] = []
  private isActive: boolean = false
  private overlay: HTMLElement | null = null
  private tooltip: HTMLElement | null = null

  constructor() {
    this.loadProgress()
    this.initializeModules()
    this.createOverlay()
  }

  private loadProgress(): void {
    try {
      const savedProgress = localStorage.getItem('tutorialProgress')
      if (savedProgress) {
        this.progress = JSON.parse(savedProgress).map((p: any) => ({
          ...p,
          timestamp: new Date(p.timestamp)
        }))
      }
    } catch (error) {
      Logger.warn('TutorialSystem', 'خطأ في تحميل تقدم التعليم:', error)
    }
  }

  private saveProgress(): void {
    try {
      localStorage.setItem('tutorialProgress', JSON.stringify(this.progress))
    } catch (error) {
      Logger.warn('TutorialSystem', 'خطأ في حفّ تقدم التعليم:', error)
    }
  }

  private initializeModules(): void {
    // الوحدة الأساسية - مقدمة البرنامج
    this.addModule({
      id: 'introduction',
      title: 'مقدمة البرنامج',
      description: 'تعرف على واجهة البرنامج والميزات الأساسية',
      icon: 'PlayCircleOutlined',
      category: 'basic',
      estimatedTime: 10,
      steps: [
        {
          id: 'welcome',
          title: 'مرحباً بك في برنامج المحاسبة والإنتاج',
          description: 'هذا البرنامج الشامل يساعدك في إدارة جميع عمليات شركتك من المحاسبة إلى الإنتاج',
          duration: 30
        },
        {
          id: 'dashboard',
          title: 'لوحة التحكم الرئيسية',
          description: 'هذه هي لوحة التحكم الرئيسية حيث يمكنك رؤية ملخص لجميع العمليات',
          target: '.ant-layout-content',
          position: 'bottom'
        },
        {
          id: 'sidebar',
          title: 'القائمة الجانبية',
          description: 'من هنا يمكنك الوصول لجميع أقسام البرنامج',
          target: '.ant-layout-sider',
          position: 'right'
        }
      ]
    })

    // وحدة إدارة المخزون
    this.addModule({
      id: 'inventory',
      title: 'إدارة المخزون',
      description: 'تعلم كيفية إدارة المخزون والأصناف والمستودعات',
      icon: 'InboxOutlined',
      category: 'basic',
      estimatedTime: 20,
      steps: [
        {
          id: 'inventory-intro',
          title: 'مقدمة إدارة المخزون',
          description: 'إدارة المخزون هي أساس أي نّام محاسبي ناجح'
        },
        {
          id: 'categories',
          title: 'إدارة الفئات',
          description: 'ابدأ بإنشاء فئات للأصناف لتنّيم المخزون'
        },
        {
          id: 'items',
          title: 'إدارة الأصناف',
          description: 'أضف الأصناف مع تفاصيلها الكاملة'
        },
        {
          id: 'warehouses',
          title: 'إدارة المستودعات',
          description: 'أنشئ المستودعات وحدد مواقعها'
        }
      ]
    })

    // وحدة المبيعات
    this.addModule({
      id: 'sales',
      title: 'إدارة المبيعات',
      description: 'تعلم كيفية إدارة العملاء والفواتير وأوامر البيع',
      icon: 'ShoppingCartOutlined',
      category: 'intermediate',
      estimatedTime: 25,
      prerequisites: ['inventory'],
      steps: [
        {
          id: 'customers',
          title: 'إدارة العملاء',
          description: 'أضف بيانات العملاء وتصنيفاتهم'
        },
        {
          id: 'sales-orders',
          title: 'أوامر البيع',
          description: 'إنشاء وإدارة أوامر البيع'
        },
        {
          id: 'invoices',
          title: 'فواتير المبيعات',
          description: 'إصدار فواتير المبيعات والتحكم بها'
        }
      ]
    })

    // وحدة المشتريات
    this.addModule({
      id: 'purchases',
      title: 'إدارة المشتريات',
      description: 'تعلم كيفية إدارة الموردين وفواتير الشراء',
      icon: 'ShopOutlined',
      category: 'intermediate',
      estimatedTime: 25,
      prerequisites: ['inventory'],
      steps: [
        {
          id: 'suppliers',
          title: 'إدارة الموردين',
          description: 'أضف بيانات الموردين وشروط التعامل'
        },
        {
          id: 'purchase-orders',
          title: 'أوامر الشراء',
          description: 'إنشاء وإدارة أوامر الشراء'
        },
        {
          id: 'purchase-invoices',
          title: 'فواتير المشتريات',
          description: 'استلام وتسجيل فواتير المشتريات'
        }
      ]
    })

    // وحدة الإنتاج - الأهم والأكثر تفصيلاً
    this.addModule({
      id: 'production',
      title: 'إدارة الإنتاج - الدورة المستندية الكاملة',
      description: 'دليل شامل لإدارة الإنتاج والدورة المستندية الإنتاجية',
      icon: 'ToolOutlined',
      category: 'production',
      estimatedTime: 45,
      prerequisites: ['inventory', 'purchases'],
      steps: [
        {
          id: 'production-intro',
          title: 'مقدمة الدورة المستندية الإنتاجية',
          description: 'فهم مفهوم الدورة المستندية وأهميتها في الإنتاج'
        },
        {
          id: 'production-planning',
          title: 'تخطيط الإنتاج',
          description: 'كيفية وضع خطة إنتاج فعالة وتحديد الاحتياجات'
        },
        {
          id: 'materials-management',
          title: 'إدارة المواد الخام',
          description: 'تسجيل وإدارة المواد الخام المطلوبة للإنتاج'
        },
        {
          id: 'production-recipes',
          title: 'وصفات الإنتاج',
          description: 'إنشاء وصفات الإنتاج وتحديد المكونات والكميات'
        },
        {
          id: 'production-orders',
          title: 'أوامر الإنتاج',
          description: 'إنشاء وإدارة أوامر الإنتاج وتتبع حالتها'
        },
        {
          id: 'production-stages',
          title: 'مراحل الإنتاج',
          description: 'تقسيم الإنتاج إلى مراحل وتتبع التقدم'
        },
        {
          id: 'quality-control',
          title: 'مراقبة الجودة',
          description: 'تطبيق معايير الجودة وفحص المنتجات'
        },
        {
          id: 'production-costing',
          title: 'تكلفة الإنتاج',
          description: 'حساب تكلفة الإنتاج الفعلية والمعيارية'
        },
        {
          id: 'finished-goods',
          title: 'البضائع التامة الصنع',
          description: 'تسجيل البضائع المنتهة ونقلها للمخزون'
        },
        {
          id: 'production-reports',
          title: 'تقارير الإنتاج',
          description: 'إنشاء تقارير شاملة عن الإنتاج والكفاءة'
        }
      ]
    })

    // وحدة الإدارة المالية
    this.addModule({
      id: 'finance',
      title: 'الإدارة المالية',
      description: 'تعلم إدارة الحسابات والقيود والتقارير المالية',
      icon: 'BankOutlined',
      category: 'advanced',
      estimatedTime: 35,
      prerequisites: ['sales', 'purchases'],
      steps: [
        {
          id: 'chart-of-accounts',
          title: 'دليل الحسابات',
          description: 'إعداد دليل الحسابات وتصنيف الحسابات'
        },
        {
          id: 'journal-entries',
          title: 'القيود اليومية',
          description: 'تسجيل القيود المحاسبية اليومية'
        },
        {
          id: 'vouchers',
          title: 'إدارة السندات',
          description: 'إنشاء وإدارة سندات القبض والصرف'
        },
        {
          id: 'financial-reports',
          title: 'التقارير المالية',
          description: 'إنشاء الميزانية وقائمة الدخل'
        }
      ]
    })
  }

  public addModule(module: TutorialModule): void {
    this.modules.set(module.id, module)
  }

  public getModules(): TutorialModule[] {
    return Array.from(this.modules.values())
  }

  public getModule(id: string): TutorialModule | undefined {
    return this.modules.get(id)
  }

  public startModule(moduleId: string): boolean {
    const module = this.modules.get(moduleId)
    if (!module) return false

    // التحقق من المتطلبات المسبقة
    if (module.prerequisites) {
      for (const prereq of module.prerequisites) {
        if (!this.isModuleCompleted(prereq)) {
          throw new Error(`يجب إكمال وحدة "${prereq}" أولاً`)
        }
      }
    }

    this.currentModule = moduleId
    this.currentStep = module.steps[0]?.id || null
    this.isActive = true
    
    this.showStep()
    return true
  }

  public nextStep(): boolean {
    if (!this.currentModule || !this.currentStep) return false

    const module = this.modules.get(this.currentModule)
    if (!module) return false

    const currentStepIndex = module.steps.findIndex(s => s.id === this.currentStep)
    if (currentStepIndex === -1 || currentStepIndex >= module.steps.length - 1) {
      this.completeModule()
      return false
    }

    // تسجيل إكمال الخطوة الحالية
    this.markStepCompleted(this.currentModule, this.currentStep)

    this.currentStep = module.steps[currentStepIndex + 1].id
    this.showStep()
    return true
  }

  public prevStep(): boolean {
    if (!this.currentModule || !this.currentStep) return false

    const module = this.modules.get(this.currentModule)
    if (!module) return false

    const currentStepIndex = module.steps.findIndex(s => s.id === this.currentStep)
    if (currentStepIndex <= 0) return false

    this.currentStep = module.steps[currentStepIndex - 1].id
    this.showStep()
    return true
  }

  private showStep(): void {
    if (!this.currentModule || !this.currentStep) return

    const module = this.modules.get(this.currentModule)
    if (!module) return

    const step = module.steps.find(s => s.id === this.currentStep)
    if (!step) return

    this.createTooltip(step)
    this.highlightTarget(step.target)
  }

  private createOverlay(): void {
    this.overlay = document.createElement('div')
    this.overlay.className = 'tutorial-overlay'
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 9998;
      display: none;
    `
    document.body.appendChild(this.overlay)
  }

  private createTooltip(step: TutorialStep): void {
    if (this.tooltip) {
      this.tooltip.remove()
    }

    this.tooltip = document.createElement('div')
    this.tooltip.className = 'tutorial-tooltip'
    this.tooltip.innerHTML = `
      <div style="
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        max-width: 400px;
        position: relative;
        direction: rtl;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      ">
        <h3 style="margin: 0 0 10px 0; color: #1890ff; font-size: 18px;">${step.title}</h3>
        <p style="margin: 0 0 15px 0; color: #666; line-height: 1.6;">${step.description}</p>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <button onclick="window.tutorialSystem.prevStep()" style="
              background: #f0f0f0;
              border: none;
              padding: 8px 16px;
              border-radius: 4px;
              margin-left: 8px;
              cursor: pointer;
            ">السابق</button>
            <button onclick="window.tutorialSystem.nextStep()" style="
              background: #1890ff;
              color: white;
              border: none;
              padding: 8px 16px;
              border-radius: 4px;
              cursor: pointer;
            ">التالي</button>
          </div>
          <button onclick="window.tutorialSystem.stopTutorial()" style="
            background: #ff4d4f;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
          ">إنهاء</button>
        </div>
      </div>
    `

    this.positionTooltip(step)
    document.body.appendChild(this.tooltip)
    
    if (this.overlay) {
      this.overlay.style.display = 'block'
    }
  }

  private positionTooltip(step: TutorialStep): void {
    if (!this.tooltip) return

    let target: HTMLElement | null = null
    if (step.target) {
      target = document.querySelector(step.target) as HTMLElement
    }

    this.tooltip.style.cssText = `
      position: fixed;
      z-index: 9999;
      ${target ? this.calculatePosition(target, step.position || 'bottom') : 'top: 50%; left: 50%; transform: translate(-50%, -50%);'}
    `
  }

  private calculatePosition(target: HTMLElement, position: string): string {
    const rect = target.getBoundingClientRect()
    const tooltipWidth = 400
    const tooltipHeight = 200

    switch (position) {
      case 'top':
        return `bottom: ${window.innerHeight - rect.top + 10}px; left: ${rect.left + rect.width / 2 - tooltipWidth / 2}px;`
      case 'bottom':
        return `top: ${rect.bottom + 10}px; left: ${rect.left + rect.width / 2 - tooltipWidth / 2}px;`
      case 'left':
        return `top: ${rect.top + rect.height / 2 - tooltipHeight / 2}px; right: ${window.innerWidth - rect.left + 10}px;`
      case 'right':
        return `top: ${rect.top + rect.height / 2 - tooltipHeight / 2}px; left: ${rect.right + 10}px;`
      default:
        return `top: ${rect.bottom + 10}px; left: ${rect.left + rect.width / 2 - tooltipWidth / 2}px;`
    }
  }

  private highlightTarget(target?: string): void {
    // إزالة التمييز السابق
    document.querySelectorAll('.tutorial-highlight').forEach(el => {
      el.classList.remove('tutorial-highlight')
    })

    if (target) {
      const element = document.querySelector(target)
      if (element) {
        element.classList.add('tutorial-highlight')
      }
    }
  }

  public stopTutorial(): void {
    this.isActive = false
    this.currentModule = null
    this.currentStep = null
    
    if (this.tooltip) {
      this.tooltip.remove()
      this.tooltip = null
    }
    
    if (this.overlay) {
      this.overlay.style.display = 'none'
    }

    this.highlightTarget() // إزالة التمييز
  }

  private markStepCompleted(moduleId: string, stepId: string): void {
    const existing = this.progress.find(p => p.moduleId === moduleId && p.stepId === stepId)
    if (!existing) {
      this.progress.push({
        moduleId,
        stepId,
        completed: true,
        timestamp: new Date(),
        timeSpent: 0
      })
      this.saveProgress()
    }
  }

  private completeModule(): void {
    if (!this.currentModule) return

    const module = this.modules.get(this.currentModule)
    if (module) {
      module.isCompleted = true
      module.progress = 100
    }

    this.stopTutorial()
    
    // إشعار بإكمال الوحدة
    if (window.audioSystem) {
      window.audioSystem.playSound('success')
    }
  }

  public isModuleCompleted(moduleId: string): boolean {
    const module = this.modules.get(moduleId)
    return module?.isCompleted || false
  }

  public getProgress(moduleId: string): number {
    const module = this.modules.get(moduleId)
    if (!module) return 0

    const completedSteps = this.progress.filter(p => 
      p.moduleId === moduleId && p.completed
    ).length

    return (completedSteps / module.steps.length) * 100
  }

  public resetProgress(): void {
    this.progress = []
    this.modules.forEach(module => {
      module.isCompleted = false
      module.progress = 0
    })
    this.saveProgress()
  }
}

// إنشاء مثيل واحد للنّام
export const tutorialSystem = new TutorialSystem()

// إتاحة النّام عالمياً
declare global {
  interface Window {
    tutorialSystem: TutorialSystem
  }
}

if (typeof window !== 'undefined') {
  window.tutorialSystem = tutorialSystem
}

export default tutorialSystem
