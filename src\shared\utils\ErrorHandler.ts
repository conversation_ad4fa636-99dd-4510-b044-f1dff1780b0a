// نظام معالجة الأخطاء الشامل للنظام المحاسبي

export interface ErrorDetails {
  code: string
  message: string
  details?: any
  timestamp: Date
  context?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  errors?: string[]
  warnings?: string[]
  code?: string
  details?: string
}

export class ErrorHandler {
  // معالجة أخطاء قاعدة البيانات
  static handleDatabaseError(error: any, context: string = 'Database'): ApiResponse {
    console.error(`[${context}] Database Error:`, error)

    // أخطاء SQLite الشائعة
    if (error.message?.includes('UNIQUE constraint failed')) {
      if (error.message.includes('check_number')) {
        return {
          success: false,
          message: 'رقم الشيك موجود مسبقاً',
          code: 'DUPLICATE_CHECK_NUMBER'
        }
      }
      if (error.message.includes('account_number')) {
        return {
          success: false,
          message: 'رقم الحساب موجود مسبقاً',
          code: 'DUPLICATE_ACCOUNT_NUMBER'
        }
      }
      return {
        success: false,
        message: 'البيانات موجودة مسبقاً',
        code: 'DUPLICATE_ENTRY'
      }
    }

    if (error.message?.includes('FOREIGN KEY constraint failed')) {
      return {
        success: false,
        message: 'البيانات المرجعية غير صحيحة',
        code: 'FOREIGN_KEY_ERROR'
      }
    }

    if (error.message?.includes('NOT NULL constraint failed')) {
      return {
        success: false,
        message: 'بعض البيانات المطلوبة مفقودة',
        code: 'MISSING_REQUIRED_DATA'
      }
    }

    if (error.message?.includes('CHECK constraint failed')) {
      return {
        success: false,
        message: 'البيانات لا تتوافق مع القيود المحددة',
        code: 'CHECK_CONSTRAINT_ERROR'
      }
    }

    // خطأ عام في قاعدة البيانات
    return {
      success: false,
      message: 'حدث خطأ في قاعدة البيانات',
      code: 'DATABASE_ERROR',
      details: error.message
    }
  }

  // معالجة أخطاء التحقق من البيانات
  static handleValidationError(errors: string[], context: string = 'Validation'): ApiResponse {
    console.warn(`[${context}] Validation Error:`, errors)

    return {
      success: false,
      message: 'البيانات المدخلة غير صحيحة',
      errors,
      code: 'VALIDATION_ERROR'
    }
  }

  // معالجة أخطاء الشبكة
  static handleNetworkError(error: any, context: string = 'Network'): ApiResponse {
    console.error(`[${context}] Network Error:`, error)

    if (error.code === 'ENOTFOUND') {
      return {
        success: false,
        message: 'لا يمكن الاتصال بالخادم',
        code: 'CONNECTION_ERROR'
      }
    }

    if (error.code === 'ETIMEDOUT') {
      return {
        success: false,
        message: 'انتهت مهلة الاتصال',
        code: 'TIMEOUT_ERROR'
      }
    }

    return {
      success: false,
      message: 'حدث خطأ في الشبكة',
      code: 'NETWORK_ERROR'
    }
  }

  // معالجة أخطاء الملفات
  static handleFileError(error: any, context: string = 'File'): ApiResponse {
    console.error(`[${context}] File Error:`, error)

    if (error.code === 'ENOENT') {
      return {
        success: false,
        message: 'الملف غير موجود',
        code: 'FILE_NOT_FOUND'
      }
    }

    if (error.code === 'EACCES') {
      return {
        success: false,
        message: 'ليس لديك صلاحية للوصول للملف',
        code: 'FILE_ACCESS_DENIED'
      }
    }

    if (error.code === 'ENOSPC') {
      return {
        success: false,
        message: 'لا توجد مساحة كافية على القرص',
        code: 'DISK_FULL'
      }
    }

    return {
      success: false,
      message: 'حدث خطأ في التعامل مع الملف',
      code: 'FILE_ERROR'
    }
  }

  // معالجة أخطاء الصلاحيات
  static handlePermissionError(action: string, context: string = 'Permission'): ApiResponse {
    console.warn(`[${context}] Permission Error: ${action}`)

    return {
      success: false,
      message: `ليس لديك صلاحية لتنفيذ هذا الإجراء: ${action}`,
      code: 'PERMISSION_DENIED'
    }
  }

  // معالجة الأخطاء العامة
  static handleGenericError(error: any, context: string = 'General'): ApiResponse {
    console.error(`[${context}] Generic Error:`, error)

    // إذا كان الخطأ يحتوي على رسالة مفهومة
    if (error.message && typeof error.message === 'string') {
      return {
        success: false,
        message: error.message,
        code: 'GENERIC_ERROR'
      }
    }

    return {
      success: false,
      message: 'حدث خطأ غير متوقع',
      code: 'UNKNOWN_ERROR'
    }
  }

  // إنشاء استجابة نجاح
  static createSuccessResponse<T>(message: string, data?: T, warnings?: string[]): ApiResponse<T> {
    return {
      success: true,
      message,
      data,
      warnings
    }
  }

  // إنشاء استجابة فشل
  static createErrorResponse(message: string, code?: string, errors?: string[]): ApiResponse {
    return {
      success: false,
      message,
      code,
      errors
    }
  }

  // تسجيل الأخطاء مع التفاصيل
  static logError(context: string, action: string, error: any, additionalInfo?: any): void {
    const errorDetails: ErrorDetails = {
      code: error.code || 'UNKNOWN',
      message: error.message || 'Unknown error',
      details: error,
      timestamp: new Date(),
      context: `${context}:${action}`
    }

    console.error('Error Details:', {
      ...errorDetails,
      additionalInfo
    })

    // يمكن إضافة تسجيل في ملف أو إرسال للخادم هنا
  }
}

export default ErrorHandler
