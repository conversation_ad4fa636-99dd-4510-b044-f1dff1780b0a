import React, { useState, useEffect } from 'react'
import {
  Card,
  Button,
  Upload,
  Avatar,
  Space,
  Typography,
  Row,
  Col,
  Alert,
  Progress,
  Modal,
  Image,
  Tooltip,
  message
} from 'antd'
import {
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  BankOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { logger as Logger } from './../../utils/logger'

const { Title, Text } = Typography

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
  background: linear-gradient(135deg, #f0f8ff, #e6f7ff);
  border-radius: 12px;
  border: 2px solid #1890ff;
  margin-bottom: 24px;
`

const LogoPreview = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
`

const LogoInfo = styled.div`
  flex: 1;
`

const ActionButtons = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`

interface LogoManagerProps {
  onLogoChange?: (logoUrl: string) => void
}

const LogoManager: React.FC<LogoManagerProps> = ({ onLogoChange }) => {
  const [logoUrl, setLogoUrl] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [previewVisible, setPreviewVisible] = useState(false)
  const [logoInfo, setLogoInfo] = useState<{
    size?: number
    type?: string
    lastModified?: string
  }>({})

  const defaultLogo = './default-logo.svg'

  useEffect(() => {
    loadCompanyLogo()
  }, [])

  const loadCompanyLogo = async () => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.getCompanyLogo()
        if (result.success && result.logoPath) {
          setLogoUrl(result.logoPath)
          // جلب معلومات الشعار
          if (result.data) {
            setLogoInfo({
              size: result.data.size,
              type: result.data.type,
              lastModified: result.data.lastModified
            })
          }
        } else {
          setLogoUrl(defaultLogo)
        }
      } else {
        setLogoUrl(defaultLogo)
      }
    } catch (error) {
      Logger.error('LogoManager', 'خطأ في تحميل الشعار:', error as Error)
      setLogoUrl(defaultLogo)
    } finally {
      setLoading(false)
    }
  }

  const handleLogoUpload = async (file: File) => {
    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      message.error('يرجى اختيار ملف صورة صالح (JPG, PNG, SVG, WebP)')
      return false
    }

    // التحقق من حجم الملف (5MB كحد أقصى)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      message.error('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت')
      return false
    }

    setUploading(true)
    setUploadProgress(0)

    try {
      if (window.electronAPI) {
        // محاكاة تقدم الرفع
        const progressInterval = setInterval(() => {
          setUploadProgress(prev => {
            if (prev >= 90) {
              clearInterval(progressInterval)
              return 90
            }
            return prev + 10
          })
        }, 100)

        const result = await window.electronAPI.uploadCompanyLogo()
        clearInterval(progressInterval)
        setUploadProgress(100)

        if (result.success && result.logoPath) {
          setLogoUrl(result.logoPath)
          setLogoInfo({
            size: file.size,
            type: file.type,
            lastModified: new Date().toISOString()
          })
          
          message.success('تم رفع الشعار بنجاح')
          onLogoChange?.(result.logoPath)

          // إعادة تحميل الشعار للتأكد من التحديث
          setTimeout(() => loadCompanyLogo(), 500)

          // تحديث خدمة الطباعة بالشعار الجديد
          try {
            const { MasterPrintService } = await import('../../services/MasterPrintService')
            const printService = MasterPrintService.getInstance()
            await printService.loadCompanyInfo()
            Logger.info('LogoManager', 'تم تحديث خدمة الطباعة بالشعار الجديد')
          } catch (printError) {
            Logger.warn('LogoManager', 'فشل في تحديث خدمة الطباعة:', printError)
          }
        } else {
          message.error(result.message || 'فشل في رفع الشعار')
        }
      } else {
        message.error('وّيفة رفع الشعار غير متوفرة في وضع التطوير')
      }
    } catch (error) {
      Logger.error('LogoManager', 'خطأ في رفع الشعار:', error as Error)
      message.error('حدث خطأ أثناء رفع الشعار')
    } finally {
      setUploading(false)
      setUploadProgress(0)
    }

    return false // منع الرفع التلقائي
  }

  const handleDeleteLogo = async () => {
    Modal.confirm({
      title: 'حذف الشعار',
      content: 'هل أنت متأكد من حذف شعار الشركة؟ سيتم استخدام الشعار الافتراضي.',
      okText: 'حذف',
      cancelText: 'إلغاء',
      okType: 'danger',
      onOk: async () => {
        try {
          if (window.electronAPI) {
            const result = await window.electronAPI.deleteCompanyLogo()
            if (result.success) {
              setLogoUrl(defaultLogo)
              setLogoInfo({})
              message.success('تم حذف الشعار بنجاح')
              onLogoChange?.(defaultLogo)
            } else {
              message.error(result.message || 'فشل في حذف الشعار')
            }
          }
        } catch (error) {
          Logger.error('LogoManager', 'خطأ في حذف الشعار:', error as Error)
          message.error('حدث خطأ أثناء حذف الشعار')
        }
      }
    })
  }

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'غير معروف'
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const getFileTypeDisplay = (type?: string) => {
    if (!type) return 'غير معروف'
    const typeMap: { [key: string]: string } = {
      'image/jpeg': 'JPEG',
      'image/jpg': 'JPG', 
      'image/png': 'PNG',
      'image/svg+xml': 'SVG',
      'image/webp': 'WebP'
    }
    return typeMap[type] || type
  }

  return (
    <Card title="إدارة شعار الشركة" loading={loading}>
      <LogoContainer>
        <LogoPreview>
          <Avatar
            size={100}
            src={logoUrl || defaultLogo}
            icon={<BankOutlined />}
            style={{
              border: '3px solid #1890ff',
              boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)'
            }}
            onError={() => {
              Logger.warn('LogoManager', 'فشل في تحميل شعار الشركة، استخدام الشعار الافتراضي')
              setLogoUrl(defaultLogo)
              return false
            }}
          />
          <Space>
            <Tooltip title="معاينة الشعار">
              <Button
                size="small"
                icon={<EyeOutlined />}
                onClick={() => setPreviewVisible(true)}
              />
            </Tooltip>
            {logoUrl !== defaultLogo && (
              <Tooltip title="حذف الشعار">
                <Button
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleDeleteLogo}
                />
              </Tooltip>
            )}
          </Space>
        </LogoPreview>

        <LogoInfo>
          <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
            شعار الشركة
          </Title>
          <Text type="secondary">
            يُستخدم في جميع المطبوعات والمستندات الرسمية
          </Text>
          
          {logoInfo.size && (
            <div style={{ marginTop: 12 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Text strong>الحجم:</Text>
                  <br />
                  <Text>{formatFileSize(logoInfo.size)}</Text>
                </Col>
                <Col span={8}>
                  <Text strong>النوع:</Text>
                  <br />
                  <Text>{getFileTypeDisplay(logoInfo.type)}</Text>
                </Col>
                <Col span={8}>
                  <Text strong>آخر تحديث:</Text>
                  <br />
                  <Text>
                    {logoInfo.lastModified 
                      ? new Date(logoInfo.lastModified).toLocaleDateString('ar-EG')
                      : 'غير معروف'
                    }
                  </Text>
                </Col>
              </Row>
            </div>
          )}
        </LogoInfo>

        <ActionButtons>
          <Upload
            beforeUpload={handleLogoUpload}
            showUploadList={false}
            accept="image/*"
          >
            <Button
              type="primary"
              icon={<UploadOutlined />}
              loading={uploading}
            >
              {uploading ? 'جاري الرفع...' : 'تغيير الشعار'}
            </Button>
          </Upload>
          
          {uploading && (
            <Progress 
              percent={uploadProgress} 
              size="small" 
              status="active"
              style={{ width: 120 }}
            />
          )}
        </ActionButtons>
      </LogoContainer>

      <Alert
        message="متطلبات الشعار"
        description={
          <ul style={{ margin: 0, paddingRight: 20 }}>
            <li>الأنواع المدعومة: JPG, PNG, SVG, WebP</li>
            <li>الحد الأقصى للحجم: 5 ميجابايت</li>
            <li>الأبعاد المُوصى بها: 200x200 بكسل أو أكبر</li>
            <li>يُفضل استخدام خلفية شفافة (PNG أو SVG)</li>
          </ul>
        }
        type="info"
        showIcon
      />

      {/* نافذة المعاينة */}
      <Modal
        title="معاينة الشعار"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Image
            src={logoUrl || defaultLogo}
            alt="شعار الشركة"
            style={{ maxWidth: '100%', maxHeight: '400px' }}
            fallback={defaultLogo}
          />
        </div>
      </Modal>
    </Card>
  )
}

export default LogoManager
