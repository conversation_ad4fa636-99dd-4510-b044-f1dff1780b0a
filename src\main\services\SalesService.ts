import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { CustomerService } from './CustomerService'
import { InventoryService } from './InventoryService'
import { Logger } from '../utils/logger'
import { sanitizeFormData, validateRequiredIds, logFormData } from '../utils/dataTypeUtils'

export interface SalesInvoice {
  id: number
  invoice_number: string
  customer_id: number
  customer_name?: string
  order_id?: number
  order_number?: string
  invoice_date: string
  due_date?: string
  status: 'pending' | 'paid' | 'partial' | 'overdue' | 'cancelled'
  total_amount: number
  discount: number
  tax: number
  final_amount: number
  paid_amount: number
  remaining_amount: number
  payment_method?: string
  notes?: string
  created_at: string
  updated_at?: string
  created_by?: number
  items?: SalesInvoiceItem[]
}

export interface SalesInvoiceItem {
  id: number
  invoice_id: number
  item_id: number
  item_name?: string
  warehouse_id: number
  warehouse_name?: string
  quantity: number
  unit_price: number
  total_price: number
  notes?: string
}

export interface SalesOrder {
  id: number
  order_number: string
  customer_id: number
  customer_name?: string
  order_date: string
  delivery_date?: string
  status: 'pending' | 'confirmed' | 'delivered' | 'cancelled'
  total_amount: number
  discount: number
  tax: number
  final_amount: number
  notes?: string
  created_at: string
  updated_at?: string
  created_by?: number
}

export interface CreateSalesInvoiceData {
  customer_id: number
  order_id?: number
  invoice_date: string
  due_date?: string
  discount?: number
  tax?: number
  payment_method?: string
  notes?: string
  items: {
    item_id: number
    warehouse_id: number
    quantity: number
    unit_price: number
  }[]
}

export class SalesService {
  private static instance: SalesService
  private db: any
  private customerService: CustomerService
  private inventoryService: InventoryService

  private constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
    this.customerService = CustomerService.getInstance()
    this.inventoryService = InventoryService.getInstance()
  }

  public static getInstance(): SalesService {
    if (!SalesService.instance) {
      SalesService.instance = new SalesService()
    }
    return SalesService.instance
  }

  // إنشاء جداول المبيعات
  public async createSalesTables(): Promise<void> {
    const database = this.db

    // جدول فواتير المبيعات
    database.exec(`
      CREATE TABLE IF NOT EXISTS sales_invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT UNIQUE NOT NULL,
        customer_id INTEGER NOT NULL,
        order_id INTEGER,
        invoice_date DATE NOT NULL,
        due_date DATE,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'partial', 'overdue', 'cancelled')),
        total_amount DECIMAL(10,2) DEFAULT 0,
        discount DECIMAL(10,2) DEFAULT 0,
        tax DECIMAL(10,2) DEFAULT 0,
        final_amount DECIMAL(10,2) DEFAULT 0,
        paid_amount DECIMAL(10,2) DEFAULT 0,
        remaining_amount DECIMAL(10,2) DEFAULT 0,
        payment_method TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (order_id) REFERENCES sales_orders (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // جدول تفاصيل فواتير المبيعات
    database.exec(`
      CREATE TABLE IF NOT EXISTS sales_invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        item_id INTEGER NOT NULL,
        warehouse_id INTEGER NOT NULL,
        quantity DECIMAL(10,2) NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (invoice_id) REFERENCES sales_invoices (id) ON DELETE CASCADE,
        FOREIGN KEY (item_id) REFERENCES items (id),
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
      )
    `)

    // جدول أوامر البيع
    database.exec(`
      CREATE TABLE IF NOT EXISTS sales_orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_number TEXT UNIQUE NOT NULL,
        customer_id INTEGER NOT NULL,
        order_date DATE NOT NULL,
        delivery_date DATE,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'delivered', 'cancelled')),
        total_amount DECIMAL(10,2) DEFAULT 0,
        discount DECIMAL(10,2) DEFAULT 0,
        tax DECIMAL(10,2) DEFAULT 0,
        final_amount DECIMAL(10,2) DEFAULT 0,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // جدول تفاصيل أوامر البيع
    database.exec(`
      CREATE TABLE IF NOT EXISTS sales_order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        item_id INTEGER NOT NULL,
        warehouse_id INTEGER,
        quantity DECIMAL(10,3) NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES sales_orders (id) ON DELETE CASCADE,
        FOREIGN KEY (item_id) REFERENCES items (id),
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
      )
    `)

    // إنشاء الفهارس
    database.exec('CREATE INDEX IF NOT EXISTS idx_sales_invoices_customer ON sales_invoices(customer_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_sales_invoices_date ON sales_invoices(invoice_date)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_sales_invoices_status ON sales_invoices(status)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_sales_invoice_items_invoice ON sales_invoice_items(invoice_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_sales_orders_customer ON sales_orders(customer_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_sales_orders_date ON sales_orders(order_date)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_sales_order_items_order ON sales_order_items(order_id)')
  }

  // الحصول على فواتير المبيعات
  public async getSalesInvoices(): Promise<SalesInvoice[]> {
    try {
      const invoices = this.db.prepare(`
        SELECT si.*, c.name as customer_name, so.order_number
        FROM sales_invoices si
        LEFT JOIN customers c ON si.customer_id = c.id
        LEFT JOIN sales_orders so ON si.order_id = so.id
        ORDER BY si.created_at DESC
      `).all() as SalesInvoice[]

      return invoices
    } catch (error) {
      Logger.error('SalesService', 'خطأ في جلب فواتير المبيعات:', error)
      return []
    }
  }

  // إنشاء فاتورة مبيعات جديدة
  public async createSalesInvoice(invoiceData: CreateSalesInvoiceData, userId?: number): Promise<ApiResponse> {
    try {
      // تسجيل البيانات الأصلية للتشخيص
      logFormData('SalesService', 'إنشاء فاتورة المبيعات', invoiceData)

      // تنّيف وتحويل البيانات
      const sanitizedData = sanitizeFormData(invoiceData)

      // تسجيل البيانات بعد التنّيف
      Logger.info('SalesService', '🧹 البيانات بعد التنّيف:', {
        customer_id: `${sanitizedData.customer_id} (${typeof sanitizedData.customer_id})`,
        items_count: sanitizedData.items?.length || 0,
        total_amount: `${sanitizedData.total_amount} (${typeof sanitizedData.total_amount})`,
        payment_method: `${sanitizedData.payment_method} (${typeof sanitizedData.payment_method})`
      })

      // التحقق من المعرفات المطلوبة أولاً
      const idValidation = validateRequiredIds(sanitizedData, ['customer_id'])
      if (!idValidation.isValid) {
        Logger.error('SalesService', '❌ فشل التحقق من المعرفات:', idValidation.message)
        return { success: false, message: idValidation.message || 'معرفات غير صحيحة' }
      }

      // التحقق من صحة البيانات الأساسية
      if (!sanitizedData.customer_id) {
        Logger.error('SalesService', '❌ معرف العميل مفقود')
        return { success: false, message: 'معرف العميل مطلوب' }
      }

      if (!sanitizedData.invoice_date) {
        Logger.error('SalesService', '❌ تاريخ الفاتورة مفقود')
        return { success: false, message: 'تاريخ الفاتورة مطلوب' }
      }

      if (!sanitizedData.items || sanitizedData.items.length === 0) {
        Logger.error('SalesService', '❌ لا توجد أصناف في الفاتورة')
        return { success: false, message: 'يجب إضافة عنصر واحد على الأقل للفاتورة' }
      }

      const customerId = sanitizedData.customer_id

      Logger.info('SalesService', '🔍 البحث عن العميل:', {
        customer_id: customerId,
        customer_id_type: typeof customerId
      })

      // التحقق من وجود العميل
      const customer = this.db.prepare('SELECT id, is_active FROM customers WHERE id = ?').get(customerId) as any

      Logger.info('SalesService', '🔍 نتيجة البحث عن العميل:', {
        customer_found: !!customer,
        customer_data: customer,
        search_value: customerId,
        search_type: typeof customerId
      })

      if (!customer) {
        Logger.error('SalesService', '❌ العميل غير موجود:', {
          customer_id: customerId,
          customer_id_type: typeof customerId,
          search_query: 'SELECT id, is_active FROM customers WHERE id = ?'
        })
        return { success: false, message: 'العميل غير موجود' }
      }
      if (!customer.is_active) {
        Logger.error('SalesService', '❌ العميل غير نشط:', { customer_id: customerId })
        return { success: false, message: 'العميل غير نشط' }
      }

      // توليد رقم الفاتورة
      const invoiceNumber = await this.generateInvoiceNumber()

      // حساب المجاميع
      let totalAmount = 0
      for (const item of sanitizedData.items) {
        if (!item.item_id || !item.quantity || !item.unit_price) {
          return { success: false, message: 'بيانات الأصناف غير مكتملة' }
        }
        totalAmount += item.quantity * item.unit_price
      }

      const discount = sanitizedData.discount || 0
      const tax = sanitizedData.tax || 0
      const finalAmount = totalAmount - discount + tax

      // حساب المبلغ المدفوع والمتبقي
      const paidAmount = parseFloat(String(sanitizedData.paid_amount || 0))
      const remainingAmount = finalAmount - paidAmount

      // إدراج الفاتورة
      const invoiceResult = this.db.prepare(`
        INSERT INTO sales_invoices (
          invoice_number, customer_id, invoice_date, due_date,
          total_amount, discount, tax, final_amount, paid_amount, remaining_amount,
          payment_method, notes, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        invoiceNumber,
        customerId,
        sanitizedData.invoice_date,
        sanitizedData.due_date || null,
        totalAmount,
        discount,
        tax,
        finalAmount,
        paidAmount,
        remainingAmount,
        sanitizedData.payment_method || null,
        sanitizedData.notes || null,
        userId || null
      )

      const invoiceId = invoiceResult.lastInsertRowid

      // تحديد حالة الفاتورة بناءً على المبلغ المدفوع
      let invoiceStatus = 'pending'
      if (remainingAmount <= 0) {
        invoiceStatus = 'paid'
      } else if (paidAmount > 0) {
        invoiceStatus = 'partial'
      }

      // تحديث حالة الفاتورة
      this.db.prepare(`
        UPDATE sales_invoices
        SET status = ?
        WHERE id = ?
      `).run(invoiceStatus, invoiceId)

      // إدراج تفاصيل الفاتورة وتحديث المخزون
      for (const item of sanitizedData.items) {
        const totalPrice = item.quantity * item.unit_price

        // إدراج تفاصيل الفاتورة
        this.db.prepare(`
          INSERT INTO sales_invoice_items (
            invoice_id, item_id, warehouse_id, quantity, unit_price, total_price
          ) VALUES (?, ?, ?, ?, ?, ?)
        `).run(
          invoiceId,
          item.item_id,
          item.warehouse_id,
          item.quantity,
          item.unit_price,
          totalPrice
        )

        // تحديث المخزون (خصم الكمية)
        this.inventoryService.updateInventoryQuantity(
          item.item_id,
          item.warehouse_id,
          'out',
          item.quantity
        )

        // إنشاء حركة مخزون
        await this.inventoryService.createInventoryMovement({
          item_id: item.item_id,
          warehouse_id: item.warehouse_id,
          movement_type: 'out',
          quantity: item.quantity,
          reference_type: 'sales_invoice',
          reference_id: invoiceId,
          notes: `فاتورة مبيعات رقم ${invoiceNumber}`,
          created_by: userId
        })
      }

      // تحديث رصيد العميل
      await this.customerService.updateCustomerBalance(
        invoiceData.customer_id,
        finalAmount,
        'invoice',
        `فاتورة مبيعات رقم ${invoiceNumber}`
      )

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم إنشاء فاتورة المبيعات بنجاح',
        data: { invoiceId, invoiceNumber }
      }
    } catch (error) {
      Logger.error('SalesService', 'خطأ في إنشاء فاتورة المبيعات:', error)
      return { success: false, message: 'حدث خطأ في إنشاء فاتورة المبيعات' }
    }
  }

  // توليد رقم فاتورة جديد
  public async generateInvoiceNumber(): Promise<string> {
    try {
      const lastInvoice = this.db.prepare(`
        SELECT invoice_number FROM sales_invoices
        WHERE invoice_number LIKE 'SAL%' AND invoice_number IS NOT NULL AND invoice_number != ''
        ORDER BY CAST(SUBSTR(invoice_number, 4) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastInvoice && lastInvoice.invoice_number && lastInvoice.invoice_number.length >= 9) {
        const codeNumber = lastInvoice.invoice_number.substring(3)
        const lastNumber = parseInt(codeNumber)

        if (!isNaN(lastNumber)) {
          const newNumber = lastNumber + 1
          return `SAL${newNumber.toString().padStart(6, '0')}`
        }
      }

      return 'SAL000001'
    } catch (error) {
      Logger.error('SalesService', 'خطأ في توليد رقم الفاتورة:', error)
      return `SAL${Date.now().toString().slice(-6)}`
    }
  }

  // الحصول على تفاصيل فاتورة
  public async getSalesInvoiceItems(invoiceId: number): Promise<SalesInvoiceItem[]> {
    try {
      const items = this.db.prepare(`
        SELECT sii.*, i.name as item_name, w.name as warehouse_name
        FROM sales_invoice_items sii
        LEFT JOIN items i ON sii.item_id = i.id
        LEFT JOIN warehouses w ON sii.warehouse_id = w.id
        WHERE sii.invoice_id = ?
        ORDER BY sii.id
      `).all(invoiceId) as SalesInvoiceItem[]

      return items
    } catch (error) {
      Logger.error('SalesService', 'خطأ في جلب تفاصيل الفاتورة:', error)
      return []
    }
  }

  // الحصول على أوامر البيع
  public async getSalesOrders(): Promise<SalesOrder[]> {
    try {
      const orders = this.db.prepare(`
        SELECT so.*, c.name as customer_name
        FROM sales_orders so
        LEFT JOIN customers c ON so.customer_id = c.id
        ORDER BY so.created_at DESC
      `).all() as SalesOrder[]

      return orders
    } catch (error) {
      Logger.error('SalesService', 'خطأ في جلب أوامر البيع:', error)
      return []
    }
  }

  // الحصول على أوامر البيع المتاحة للربط (غير مرتبطة بفواتير)
  public async getAvailableSalesOrders(customerId?: number): Promise<SalesOrder[]> {
    try {
      let query = `
        SELECT so.*, c.name as customer_name
        FROM sales_orders so
        LEFT JOIN customers c ON so.customer_id = c.id
        LEFT JOIN sales_invoices si ON so.id = si.order_id
        WHERE si.order_id IS NULL AND so.status IN ('confirmed', 'pending')
      `

      if (customerId) {
        query += ` AND so.customer_id = ${customerId}`
      }

      query += ` ORDER BY so.created_at DESC`

      const orders = this.db.prepare(query).all() as SalesOrder[]
      return orders
    } catch (error) {
      Logger.error('SalesService', 'خطأ في جلب أوامر البيع المتاحة:', error)
      return []
    }
  }

  // توليد رقم أمر بيع جديد
  public async generateOrderNumber(): Promise<string> {
    try {
      const lastOrder = this.db.prepare(`
        SELECT order_number FROM sales_orders
        WHERE order_number LIKE 'SO%' AND order_number IS NOT NULL AND order_number != ''
        ORDER BY CAST(SUBSTR(order_number, 3) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastOrder && lastOrder.order_number && lastOrder.order_number.length >= 8) {
        const codeNumber = lastOrder.order_number.substring(2)
        const lastNumber = parseInt(codeNumber)

        if (!isNaN(lastNumber)) {
          const newNumber = lastNumber + 1
          return `SO${newNumber.toString().padStart(6, '0')}`
        }
      }

      return 'SO000001'
    } catch (error) {
      Logger.error('SalesService', 'خطأ في توليد رقم أمر البيع:', error)
      return `SO${Date.now().toString().slice(-6)}`
    }
  }

  // إنشاء أمر بيع جديد
  public async createSalesOrder(orderData: any, userId?: number): Promise<ApiResponse> {
    try {
      const orderNumber = await this.generateOrderNumber()

      // حساب المجاميع
      let totalAmount = 0
      for (const item of orderData.items) {
        totalAmount += item.quantity * item.unit_price
      }

      const discount = orderData.discount || 0
      const tax = orderData.tax || 0
      const finalAmount = totalAmount - discount + tax

      // إدراج الأمر
      const orderResult = this.db.prepare(`
        INSERT INTO sales_orders (
          order_number, customer_id, order_date, delivery_date,
          total_amount, discount, tax, final_amount, notes, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        orderNumber,
        orderData.customer_id,
        orderData.order_date,
        orderData.delivery_date || null,
        totalAmount,
        discount,
        tax,
        finalAmount,
        orderData.notes || null,
        userId || null
      )

      const orderId = orderResult.lastInsertRowid

      // إدراج تفاصيل الأمر
      for (const item of orderData.items) {
        this.db.prepare(`
          INSERT INTO sales_order_items (
            order_id, item_id, warehouse_id, quantity, unit_price, total_price
          ) VALUES (?, ?, ?, ?, ?, ?)
        `).run(
          orderId,
          item.item_id,
          item.warehouse_id,
          item.quantity,
          item.unit_price,
          item.quantity * item.unit_price
        )
      }

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم إنشاء أمر البيع بنجاح',
        data: { id: orderId, order_number: orderNumber }
      }
    } catch (error) {
      Logger.error('SalesService', 'خطأ في إنشاء أمر البيع:', error)
      return { success: false, message: 'حدث خطأ في إنشاء أمر البيع' }
    }
  }

  // تحديث أمر بيع
  public async updateSalesOrder(orderId: number, orderData: any): Promise<ApiResponse> {
    try {
      // حساب المجاميع
      let totalAmount = 0
      for (const item of orderData.items) {
        totalAmount += item.quantity * item.unit_price
      }

      const discount = orderData.discount || 0
      const tax = orderData.tax || 0
      const finalAmount = totalAmount - discount + tax

      // تحديث الأمر
      const result = this.db.prepare(`
        UPDATE sales_orders
        SET customer_id = ?, order_date = ?, delivery_date = ?,
            total_amount = ?, discount = ?, tax = ?, final_amount = ?,
            notes = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run(
        orderData.customer_id,
        orderData.order_date,
        orderData.delivery_date || null,
        totalAmount,
        discount,
        tax,
        finalAmount,
        orderData.notes || null,
        orderId
      )

      if (result.changes > 0) {
        // حذف التفاصيل القديمة
        this.db.prepare('DELETE FROM sales_order_items WHERE order_id = ?').run(orderId)

        // إدراج التفاصيل الجديدة
        for (const item of orderData.items) {
          this.db.prepare(`
            INSERT INTO sales_order_items (
              order_id, item_id, warehouse_id, quantity, unit_price, total_price
            ) VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            orderId,
            item.item_id,
            item.warehouse_id,
            item.quantity,
            item.unit_price,
            item.quantity * item.unit_price
          )
        }

        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث أمر البيع بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث أمر البيع' }
      }
    } catch (error) {
      Logger.error('SalesService', 'خطأ في تحديث أمر البيع:', error)
      return { success: false, message: 'حدث خطأ في تحديث أمر البيع' }
    }
  }

  // حذف أمر بيع
  public async deleteSalesOrder(orderId: number): Promise<ApiResponse> {
    try {
      // حذف التفاصيل أولاً
      this.db.prepare('DELETE FROM sales_order_items WHERE order_id = ?').run(orderId)

      // حذف الأمر
      const result = this.db.prepare('DELETE FROM sales_orders WHERE id = ?').run(orderId)

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم حذف أمر البيع بنجاح' }
      } else {
        return { success: false, message: 'فشل في حذف أمر البيع' }
      }
    } catch (error) {
      Logger.error('SalesService', 'خطأ في حذف أمر البيع:', error)
      return { success: false, message: 'حدث خطأ في حذف أمر البيع' }
    }
  }

  // الحصول على تفاصيل أمر البيع
  public async getSalesOrderItems(orderId: number): Promise<any[]> {
    try {
      const items = this.db.prepare(`
        SELECT soi.*, i.name as item_name, w.name as warehouse_name
        FROM sales_order_items soi
        LEFT JOIN items i ON soi.item_id = i.id
        LEFT JOIN warehouses w ON soi.warehouse_id = w.id
        WHERE soi.order_id = ?
        ORDER BY soi.id
      `).all(orderId)

      return items
    } catch (error) {
      Logger.error('SalesService', 'خطأ في جلب تفاصيل أمر البيع:', error)
      return []
    }
  }

  // تحديث فاتورة بيع
  public async updateSalesInvoice(invoiceId: number, invoiceData: any): Promise<ApiResponse> {
    try {
      // حساب المجاميع
      let totalAmount = 0
      for (const item of invoiceData.items) {
        totalAmount += item.quantity * item.unit_price
      }

      const discount = invoiceData.discount || 0
      const tax = invoiceData.tax || 0
      const finalAmount = totalAmount - discount + tax

      // الحصول على المبلغ المدفوع الحالي من قاعدة البيانات
      const currentInvoice = this.db.prepare('SELECT paid_amount FROM sales_invoices WHERE id = ?').get(invoiceId) as any
      const currentPaidAmount = currentInvoice?.paid_amount || 0
      const remainingAmount = finalAmount - currentPaidAmount

      // تحديث الفاتورة
      const result = this.db.prepare(`
        UPDATE sales_invoices
        SET customer_id = ?, invoice_date = ?, due_date = ?,
            total_amount = ?, discount = ?, tax = ?, final_amount = ?,
            remaining_amount = ?, payment_method = ?, notes = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run(
        invoiceData.customer_id,
        invoiceData.invoice_date,
        invoiceData.due_date || null,
        totalAmount,
        discount,
        tax,
        finalAmount,
        remainingAmount,
        invoiceData.payment_method || null,
        invoiceData.notes || null,
        invoiceId
      )

      if (result.changes > 0) {
        // حذف التفاصيل القديمة
        this.db.prepare('DELETE FROM sales_invoice_items WHERE invoice_id = ?').run(invoiceId)

        // إدراج التفاصيل الجديدة
        for (const item of invoiceData.items) {
          this.db.prepare(`
            INSERT INTO sales_invoice_items (
              invoice_id, item_id, warehouse_id, quantity, unit_price, total_price
            ) VALUES (?, ?, ?, ?, ?, ?)
          `).run(
            invoiceId,
            item.item_id,
            item.warehouse_id,
            item.quantity,
            item.unit_price,
            item.quantity * item.unit_price
          )
        }

        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث فاتورة البيع بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث فاتورة البيع' }
      }
    } catch (error) {
      Logger.error('SalesService', 'خطأ في تحديث فاتورة البيع:', error)
      return { success: false, message: 'حدث خطأ في تحديث فاتورة البيع' }
    }
  }

  // حذف فاتورة بيع
  public async deleteSalesInvoice(invoiceId: number): Promise<ApiResponse> {
    try {
      // حذف التفاصيل أولاً
      this.db.prepare('DELETE FROM sales_invoice_items WHERE invoice_id = ?').run(invoiceId)

      // حذف الفاتورة
      const result = this.db.prepare('DELETE FROM sales_invoices WHERE id = ?').run(invoiceId)

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم حذف فاتورة البيع بنجاح' }
      } else {
        return { success: false, message: 'فشل في حذف فاتورة البيع' }
      }
    } catch (error) {
      Logger.error('SalesService', 'خطأ في حذف فاتورة البيع:', error)
      return { success: false, message: 'حدث خطأ في حذف فاتورة البيع' }
    }
  }

  // تحديث حالة فاتورة البيع
  public async updateSalesInvoiceStatus(invoiceId: number, status: string): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        UPDATE sales_invoices
        SET status = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run(status, invoiceId)

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث حالة فاتورة البيع بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث حالة فاتورة البيع' }
      }
    } catch (error) {
      Logger.error('SalesService', 'خطأ في تحديث حالة فاتورة البيع:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة فاتورة البيع' }
    }
  }

  // تم إزالة جميع دوال البيانات التجريبية - يجب استخدام البيانات الحقيقية فقط

  /**
   * تقرير المبيعات حسب العميل
   */
  public async getSalesByCustomerReport(filters: {
    customerId?: number
    dateRange?: [string, string]
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}): Promise<ApiResponse> {
    try {
      Logger.info('SalesService', 'إنشاء تقرير المبيعات حسب العميل', filters)

      const whereConditions = ['1=1']
      const params: any[] = []

      // فلترة حسب العميل
      if (filters.customerId) {
        whereConditions.push('si.customer_id = ?')
        params.push(filters.customerId)
      }

      // فلترة حسب التاريخ
      if (filters.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
        whereConditions.push('si.invoice_date BETWEEN ? AND ?')
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      // ترتيب النتائج
      const sortBy = filters.sortBy || 'total_amount'
      const sortOrder = filters.sortOrder || 'desc'

      const query = `
        SELECT
          c.id as customer_id,
          c.name as customer_name,
          c.code as customer_code,
          c.phone as customer_phone,
          c.email as customer_email,
          COUNT(DISTINCT si.id) as total_invoices,
          COALESCE(SUM(si.final_amount), 0) as total_amount,
          COALESCE(SUM(si.paid_amount), 0) as paid_amount,
          COALESCE(SUM(si.remaining_amount), 0) as outstanding_amount,
          COALESCE(AVG(si.final_amount), 0) as avg_invoice_amount,
          MAX(si.invoice_date) as last_purchase_date,
          MIN(si.invoice_date) as first_purchase_date,
          COUNT(CASE WHEN si.status = 'paid' THEN 1 END) as paid_invoices,
          COUNT(CASE WHEN si.status = 'pending' THEN 1 END) as pending_invoices,
          COUNT(CASE WHEN si.status = 'overdue' THEN 1 END) as overdue_invoices
        FROM customers c
        LEFT JOIN sales_invoices si ON c.id = si.customer_id
        WHERE ${whereConditions.join(' AND ')}
        GROUP BY c.id, c.name, c.code, c.phone, c.email
        HAVING COUNT(si.id) > 0
        ORDER BY ${sortBy} ${sortOrder}
      `

      const results = this.db.prepare(query).all(...params) as any[]

      // تحسين البيانات للعرض
      const processedData = results.map(row => ({
        ...row,
        total_amount: Math.round(row.total_amount * 100) / 100,
        paid_amount: Math.round(row.paid_amount * 100) / 100,
        outstanding_amount: Math.round(row.outstanding_amount * 100) / 100,
        avg_invoice_amount: Math.round(row.avg_invoice_amount * 100) / 100,
        payment_percentage: row.total_amount > 0 ?
          Math.round((row.paid_amount / row.total_amount) * 100 * 100) / 100 : 0,
        last_purchase_date: row.last_purchase_date || '',
        first_purchase_date: row.first_purchase_date || ''
      }))

      Logger.info('SalesService', `تم إنشاء تقرير المبيعات حسب العميل: ${processedData.length} عميل`)

      return {
        success: true,
        data: processedData,
        message: `تم جلب تقرير المبيعات لـ ${processedData.length} عميل`
      }

    } catch (error: any) {
      Logger.error('SalesService', 'خطأ في تقرير المبيعات حسب العميل:', error)
      return {
        success: false,
        message: error.message || 'حدث خطأ في إنشاء تقرير المبيعات حسب العميل'
      }
    }
  }

  /**
   * تقرير المبيعات حسب المنتج
   */
  public async getSalesByProductReport(filters: {
    itemId?: number
    categoryId?: number
    dateRange?: [string, string]
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}): Promise<ApiResponse> {
    try {
      Logger.info('SalesService', 'إنشاء تقرير المبيعات حسب المنتج', filters)

      const whereConditions = ['1=1']
      const params: any[] = []

      // فلترة حسب المنتج
      if (filters.itemId) {
        whereConditions.push('sii.item_id = ?')
        params.push(filters.itemId)
      }

      // فلترة حسب الفئة
      if (filters.categoryId) {
        whereConditions.push('i.category_id = ?')
        params.push(filters.categoryId)
      }

      // فلترة حسب التاريخ
      if (filters.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
        whereConditions.push('si.invoice_date BETWEEN ? AND ?')
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      // ترتيب النتائج
      const sortBy = filters.sortBy || 'total_value'
      const sortOrder = filters.sortOrder || 'desc'

      const query = `
        SELECT
          i.id as item_id,
          i.code as item_code,
          i.name as item_name,
          ic.name as category_name,
          SUM(sii.quantity) as total_quantity,
          COALESCE(SUM(sii.total_price), 0) as total_value,
          COALESCE(AVG(sii.unit_price), 0) as avg_unit_price,
          COUNT(DISTINCT si.id) as total_invoices,
          COUNT(DISTINCT si.customer_id) as unique_customers,
          MAX(si.invoice_date) as last_sale_date,
          MIN(si.invoice_date) as first_sale_date,
          COALESCE(AVG(sii.unit_price - COALESCE(i.cost_price, 0)), 0) as avg_profit_per_unit,
          CASE
            WHEN COALESCE(i.cost_price, 0) > 0 THEN
              ROUND(((COALESCE(AVG(sii.unit_price), 0) - COALESCE(i.cost_price, 0)) / COALESCE(i.cost_price, 1)) * 100, 2)
            ELSE 0
          END as profit_margin
        FROM items i
        LEFT JOIN item_categories ic ON i.category_id = ic.id
        LEFT JOIN sales_invoice_items sii ON i.id = sii.item_id
        LEFT JOIN sales_invoices si ON sii.invoice_id = si.id
        WHERE ${whereConditions.join(' AND ')}
        GROUP BY i.id, i.code, i.name, ic.name, i.cost_price
        HAVING SUM(sii.quantity) > 0
        ORDER BY ${sortBy} ${sortOrder}
      `

      const results = this.db.prepare(query).all(...params) as any[]

      // تحسين البيانات للعرض
      const processedData = results.map(row => ({
        ...row,
        total_value: Math.round(row.total_value * 100) / 100,
        avg_unit_price: Math.round(row.avg_unit_price * 100) / 100,
        avg_profit_per_unit: Math.round(row.avg_profit_per_unit * 100) / 100,
        profit_margin: Math.round(row.profit_margin * 100) / 100,
        last_sale_date: row.last_sale_date || '',
        first_sale_date: row.first_sale_date || ''
      }))

      Logger.info('SalesService', `تم إنشاء تقرير المبيعات حسب المنتج: ${processedData.length} منتج`)

      return {
        success: true,
        data: processedData,
        message: `تم جلب تقرير المبيعات لـ ${processedData.length} منتج`
      }

    } catch (error: any) {
      Logger.error('SalesService', 'خطأ في تقرير المبيعات حسب المنتج:', error)
      return {
        success: false,
        message: error.message || 'حدث خطأ في إنشاء تقرير المبيعات حسب المنتج'
      }
    }
  }

  /**
   * تقرير المبيعات الشهري
   */
  public async getMonthlySalesReport(filters: {
    year?: number
    month?: number
    dateRange?: [string, string]
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}): Promise<ApiResponse> {
    try {
      Logger.info('SalesService', 'إنشاء تقرير المبيعات الشهري', filters)

      const whereConditions = ['1=1']
      const params: any[] = []

      // فلترة حسب السنة والشهر
      if (filters.year && filters.month) {
        whereConditions.push("strftime('%Y', si.invoice_date) = ? AND strftime('%m', si.invoice_date) = ?")
        params.push(filters.year.toString(), filters.month.toString().padStart(2, '0'))
      } else if (filters.year) {
        whereConditions.push("strftime('%Y', si.invoice_date) = ?")
        params.push(filters.year.toString())
      }

      // فلترة حسب التاريخ
      if (filters.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
        whereConditions.push('si.invoice_date BETWEEN ? AND ?')
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      // ترتيب النتائج
      const sortBy = filters.sortBy || 'month_year'
      const sortOrder = filters.sortOrder || 'desc'

      const query = `
        SELECT
          strftime('%Y-%m', si.invoice_date) as month_year,
          strftime('%Y', si.invoice_date) as year,
          strftime('%m', si.invoice_date) as month,
          COUNT(DISTINCT si.id) as total_invoices,
          COUNT(DISTINCT si.customer_id) as unique_customers,
          COALESCE(SUM(si.final_amount), 0) as total_sales,
          COALESCE(SUM(si.paid_amount), 0) as total_paid,
          COALESCE(SUM(si.remaining_amount), 0) as total_outstanding,
          COALESCE(AVG(si.final_amount), 0) as avg_invoice_amount,
          COUNT(CASE WHEN si.status = 'paid' THEN 1 END) as paid_invoices,
          COUNT(CASE WHEN si.status = 'pending' THEN 1 END) as pending_invoices,
          COUNT(CASE WHEN si.status = 'overdue' THEN 1 END) as overdue_invoices,
          ROUND(
            CASE
              WHEN COUNT(si.id) > 0 THEN
                (COUNT(CASE WHEN si.status = 'paid' THEN 1 END) * 100.0 / COUNT(si.id))
              ELSE 0
            END, 2
          ) as payment_success_rate
        FROM sales_invoices si
        WHERE ${whereConditions.join(' AND ')}
        GROUP BY strftime('%Y-%m', si.invoice_date)
        ORDER BY ${sortBy} ${sortOrder}
      `

      const results = this.db.prepare(query).all(...params) as any[]

      // تحسين البيانات للعرض
      const processedData = results.map(row => ({
        ...row,
        total_sales: Math.round(row.total_sales * 100) / 100,
        total_paid: Math.round(row.total_paid * 100) / 100,
        total_outstanding: Math.round(row.total_outstanding * 100) / 100,
        avg_invoice_amount: Math.round(row.avg_invoice_amount * 100) / 100,
        month_name: this.getMonthName(parseInt(row.month)),
        growth_rate: 0 // سيتم حسابه لاحقاً
      }))

      // حساب معدل النمو الشهري
      for (let i = 1; i < processedData.length; i++) {
        const current = processedData[i].total_sales
        const previous = processedData[i - 1].total_sales
        if (previous > 0) {
          processedData[i].growth_rate = Math.round(((current - previous) / previous) * 100 * 100) / 100
        }
      }

      Logger.info('SalesService', `تم إنشاء تقرير المبيعات الشهري: ${processedData.length} شهر`)

      return {
        success: true,
        data: processedData,
        message: `تم جلب تقرير المبيعات الشهري لـ ${processedData.length} شهر`
      }

    } catch (error: any) {
      Logger.error('SalesService', 'خطأ في تقرير المبيعات الشهري:', error)
      return {
        success: false,
        message: error.message || 'حدث خطأ في إنشاء تقرير المبيعات الشهري'
      }
    }
  }

  /**
   * تقرير الربحية
   */
  public async getProfitabilityReport(filters: {
    customerId?: number
    itemId?: number
    dateRange?: [string, string]
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}): Promise<ApiResponse> {
    try {
      Logger.info('SalesService', 'إنشاء تقرير الربحية', filters)

      const whereConditions = ['1=1']
      const params: any[] = []

      // فلترة حسب العميل
      if (filters.customerId) {
        whereConditions.push('si.customer_id = ?')
        params.push(filters.customerId)
      }

      // فلترة حسب المنتج
      if (filters.itemId) {
        whereConditions.push('sii.item_id = ?')
        params.push(filters.itemId)
      }

      // فلترة حسب التاريخ
      if (filters.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
        whereConditions.push('si.invoice_date BETWEEN ? AND ?')
        params.push(filters.dateRange[0], filters.dateRange[1])
      }

      // ترتيب النتائج
      const sortBy = filters.sortBy || 'total_profit'
      const sortOrder = filters.sortOrder || 'desc'

      const query = `
        SELECT
          i.id as item_id,
          i.code as item_code,
          i.name as item_name,
          ic.name as category_name,
          SUM(sii.quantity) as total_quantity_sold,
          COALESCE(SUM(sii.total_price), 0) as total_revenue,
          COALESCE(SUM(sii.quantity * COALESCE(i.cost_price, 0)), 0) as total_cost,
          COALESCE(SUM(sii.total_price) - SUM(sii.quantity * COALESCE(i.cost_price, 0)), 0) as total_profit,
          COALESCE(AVG(sii.unit_price), 0) as avg_selling_price,
          COALESCE(i.cost_price, 0) as cost_price,
          CASE
            WHEN COALESCE(SUM(sii.total_price), 0) > 0 THEN
              ROUND(((COALESCE(SUM(sii.total_price) - SUM(sii.quantity * COALESCE(i.cost_price, 0)), 0)) / COALESCE(SUM(sii.total_price), 1)) * 100, 2)
            ELSE 0
          END as profit_margin_percentage,
          CASE
            WHEN COALESCE(i.cost_price, 0) > 0 THEN
              ROUND(((COALESCE(AVG(sii.unit_price), 0) - COALESCE(i.cost_price, 0)) / COALESCE(i.cost_price, 1)) * 100, 2)
            ELSE 0
          END as markup_percentage,
          COUNT(DISTINCT si.id) as total_invoices,
          COUNT(DISTINCT si.customer_id) as unique_customers
        FROM items i
        LEFT JOIN item_categories ic ON i.category_id = ic.id
        LEFT JOIN sales_invoice_items sii ON i.id = sii.item_id
        LEFT JOIN sales_invoices si ON sii.invoice_id = si.id
        WHERE ${whereConditions.join(' AND ')}
        GROUP BY i.id, i.code, i.name, ic.name, i.cost_price
        HAVING SUM(sii.quantity) > 0
        ORDER BY ${sortBy} ${sortOrder}
      `

      const results = this.db.prepare(query).all(...params) as any[]

      // تحسين البيانات للعرض
      const processedData = results.map(row => ({
        ...row,
        total_revenue: Math.round(row.total_revenue * 100) / 100,
        total_cost: Math.round(row.total_cost * 100) / 100,
        total_profit: Math.round(row.total_profit * 100) / 100,
        avg_selling_price: Math.round(row.avg_selling_price * 100) / 100,
        cost_price: Math.round(row.cost_price * 100) / 100,
        profit_per_unit: Math.round((row.avg_selling_price - row.cost_price) * 100) / 100,
        roi_percentage: row.cost_price > 0 ?
          Math.round(((row.avg_selling_price - row.cost_price) / row.cost_price) * 100 * 100) / 100 : 0
      }))

      Logger.info('SalesService', `تم إنشاء تقرير الربحية: ${processedData.length} منتج`)

      return {
        success: true,
        data: processedData,
        message: `تم جلب تقرير الربحية لـ ${processedData.length} منتج`
      }

    } catch (error: any) {
      Logger.error('SalesService', 'خطأ في تقرير الربحية:', error)
      return {
        success: false,
        message: error.message || 'حدث خطأ في إنشاء تقرير الربحية'
      }
    }
  }

  /**
   * دالة مساعدة للحصول على اسم الشهر
   */
  private getMonthName(month: number): string {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ]
    return months[month - 1] || ''
  }
}
