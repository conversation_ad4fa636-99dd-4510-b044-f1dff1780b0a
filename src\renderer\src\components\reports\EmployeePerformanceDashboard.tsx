/**
 * لوحة معلومات أداء الموظفين
 * لوحة تحكم تفاعلية لمراقبة أداء الموظفين
 */

import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Statistic, Progress, Table, Tag, Typography, Space, Select, DatePicker, Button } from 'antd'
import {
  UserOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  TrophyOutlined,
  CalendarOutlined,
  RiseOutlined,
  FallOutlined,
  CheckCircleOutlined,
  WarningOutlined
} from '@ant-design/icons'
// import { Line, Column, Pie } from '@ant-design/plots' // مكتبة الرسوم البيانية - يمكن تثبيتها لاحقاً
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

interface EmployeeStats {
  totalEmployees: number
  activeEmployees: number
  avgAttendanceRate: number
  avgSalary: number
  totalOvertimeHours: number
  topPerformers: number
}

interface DepartmentPerformance {
  department: string
  employeeCount: number
  avgAttendance: number
  avgSalary: number
  performanceScore: number
}

const EmployeePerformanceDashboard: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState<EmployeeStats>({
    totalEmployees: 0,
    activeEmployees: 0,
    avgAttendanceRate: 0,
    avgSalary: 0,
    totalOvertimeHours: 0,
    topPerformers: 0
  })
  const [departmentData, setDepartmentData] = useState<DepartmentPerformance[]>([])
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all')
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs()
  ])

  // بيانات وهمية للعرض
  useEffect(() => {
    loadDashboardData()
  }, [selectedDepartment, dateRange])

  const loadDashboardData = async () => {
    setLoading(true)
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setStats({
        totalEmployees: 45,
        activeEmployees: 42,
        avgAttendanceRate: 92,
        avgSalary: 4250,
        totalOvertimeHours: 320,
        topPerformers: 12
      })

      setDepartmentData([
        {
          department: 'قسم المحاسبة',
          employeeCount: 8,
          avgAttendance: 95,
          avgSalary: 4800,
          performanceScore: 88
        },
        {
          department: 'قسم المبيعات',
          employeeCount: 12,
          avgAttendance: 89,
          avgSalary: 4200,
          performanceScore: 85
        },
        {
          department: 'قسم الإنتاج',
          employeeCount: 18,
          avgAttendance: 91,
          avgSalary: 3900,
          performanceScore: 82
        },
        {
          department: 'قسم الموارد البشرية',
          employeeCount: 7,
          avgAttendance: 96,
          avgSalary: 4500,
          performanceScore: 90
        }
      ])
      
      setLoading(false)
    }, 1000)
  }

  // إعداد الرسوم البيانية - معلق مؤقتاً حتى تثبيت @ant-design/plots
  /*
  const attendanceChartConfig = {
    data: departmentData.map(dept => ({
      department: dept.department,
      attendance: dept.avgAttendance
    })),
    xField: 'department',
    yField: 'attendance',
    color: '#1890ff',
    columnWidthRatio: 0.6,
    meta: {
      attendance: { alias: 'معدل الحضور (%)' }
    }
  }

  const salaryChartConfig = {
    data: departmentData.map(dept => ({
      department: dept.department,
      salary: dept.avgSalary
    })),
    angleField: 'salary',
    colorField: 'department',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}'
    }
  }

  const performanceChartConfig = {
    data: departmentData.map(dept => ({
      department: dept.department,
      score: dept.performanceScore
    })),
    xField: 'department',
    yField: 'score',
    point: {
      size: 5,
      shape: 'diamond'
    },
    color: '#52c41a'
  }
  */

  const departmentColumns = [
    {
      title: 'القسم',
      dataIndex: 'department',
      key: 'department',
      render: (text: string) => (
        <Space>
          <TeamOutlined style={{ color: '#1890ff' }} />
          <strong>{text}</strong>
        </Space>
      )
    },
    {
      title: 'عدد الموظفين',
      dataIndex: 'employeeCount',
      key: 'employeeCount',
      render: (count: number) => (
        <Tag color="blue">{count} موظف</Tag>
      )
    },
    {
      title: 'معدل الحضور',
      dataIndex: 'avgAttendance',
      key: 'avgAttendance',
      render: (rate: number) => (
        <Progress
          percent={rate}
          size="small"
          status={rate >= 90 ? 'success' : rate >= 80 ? 'normal' : 'exception'}
        />
      )
    },
    {
      title: 'متوسط الراتب',
      dataIndex: 'avgSalary',
      key: 'avgSalary',
      render: (salary: number) => (
        <Text strong style={{ color: '#52c41a' }}>
          {salary.toLocaleString()} ₪
        </Text>
      )
    },
    {
      title: 'نقاط الأداء',
      dataIndex: 'performanceScore',
      key: 'performanceScore',
      render: (score: number) => (
        <Space>
          <Progress
            type="circle"
            percent={score}
            size={40}
            strokeColor={score >= 85 ? '#52c41a' : score >= 70 ? '#fa8c16' : '#ff4d4f'}
          />
          <Text strong>{score}/100</Text>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      <Title level={2} style={{ marginBottom: '24px' }}>
        <TrophyOutlined style={{ marginLeft: '8px', color: '#1890ff' }} />
        لوحة معلومات أداء الموظفين
      </Title>

      {/* فلاتر التحكم */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Text strong>القسم:</Text>
            <Select
              style={{ width: '100%', marginTop: '8px' }}
              value={selectedDepartment}
              onChange={setSelectedDepartment}
            >
              <Option value="all">جميع الأقسام</Option>
              <Option value="accounting">قسم المحاسبة</Option>
              <Option value="sales">قسم المبيعات</Option>
              <Option value="production">قسم الإنتاج</Option>
              <Option value="hr">قسم الموارد البشرية</Option>
            </Select>
          </Col>
          <Col span={8}>
            <Text strong>الفترة الزمنية:</Text>
            <RangePicker
              style={{ width: '100%', marginTop: '8px' }}
              value={dateRange}
              onChange={(dates) => dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
            />
          </Col>
          <Col span={4}>
            <Button
              type="primary"
              loading={loading}
              onClick={loadDashboardData}
              style={{ marginTop: '24px' }}
            >
              تحديث البيانات
            </Button>
          </Col>
        </Row>
      </Card>

      {/* الإحصائيات الرئيسية */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي الموظفين"
              value={stats.totalEmployees}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="الموظفون النشطون"
              value={stats.activeEmployees}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="معدل الحضور العام"
              value={stats.avgAttendanceRate}
              suffix="%"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="متوسط الراتب"
              value={stats.avgSalary}
              suffix="₪"
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="ساعات إضافية"
              value={stats.totalOvertimeHours}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="المتميزون"
              value={stats.topPerformers}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* الرسوم البيانية - معلقة مؤقتاً حتى تثبيت @ant-design/plots */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={8}>
          <Card title="معدل الحضور بالأقسام" loading={loading}>
            <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
              الرسم البياني متاح بعد تثبيت @ant-design/plots
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="توزيع الرواتب" loading={loading}>
            <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
              الرسم البياني متاح بعد تثبيت @ant-design/plots
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="نقاط الأداء" loading={loading}>
            <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
              الرسم البياني متاح بعد تثبيت @ant-design/plots
            </div>
          </Card>
        </Col>
      </Row>

      {/* جدول الأقسام */}
      <Card title="أداء الأقسام" loading={loading}>
        <Table
          columns={departmentColumns}
          dataSource={departmentData}
          rowKey="department"
          pagination={false}
        />
      </Card>
    </div>
  )
}

export default EmployeePerformanceDashboard
