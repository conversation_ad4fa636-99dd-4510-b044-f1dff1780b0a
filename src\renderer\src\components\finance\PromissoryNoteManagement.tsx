import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  InputNumber, 
  Space, 
  message, 
  Tag,
  Row,
  Col,
  Statistic,
  DatePicker
} from 'antd'
import {
  PlusOutlined,
  ArrowLeftOutlined,
  WalletOutlined,
  CheckOutlined,
  ExclamationOutlined,
  CloseOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import { UnifiedPrintButton } from '../common'

interface PromissoryNoteManagementProps {
  onBack: () => void
}

const PromissoryNoteManagement: React.FC<PromissoryNoteManagementProps> = ({ onBack }) => {
  const [notes, setNotes] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    loadNotes()
  }, [])

  const loadNotes = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.getPromissoryNotes()
      if (response.success) {
        setNotes(response.data)
      } else {
        message.error('فشل في تحميل الكمبيالات')
      }
    } catch (_error) {
      message.error('خطأ في تحميل الكمبيالات')
    }
    setLoading(false)
  }

  const generateNoteNumber = async () => {
    try {
      const response = await window.electronAPI.generatePromissoryNoteNumber()
      if (response.success) {
        form.setFieldsValue({ note_number: response.data.noteNumber })
      }
    } catch (_error) {
      message.error('خطأ في إنشاء رقم الكمبيالة')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      // التحقق من صحة رقم الكمبيالة
      if (!values.note_number || values.note_number.trim().length < 3) {
        message.error('رقم الكمبيالة يجب أن يكون على الأقل 3 أرقام')
        return
      }

      // التحقق من صحة المبلغ
      const amount = parseFloat(values.amount) || 0
      if (amount <= 0) {
        message.error('يجب أن يكون مبلغ الكمبيالة أكبر من الصفر')
        return
      }

      if (amount > 1000000) {
        message.error('مبلغ الكمبيالة كبير جداً، يرجى التحقق من القيمة المدخلة')
        return
      }

      // التحقق من صحة التواريخ
      if (values.due_date && values.issue_date && values.due_date.isBefore(values.issue_date)) {
        message.error('تاريخ الاستحقاق لا يمكن أن يكون قبل تاريخ الإصدار')
        return
      }

      // التحقق من أن تاريخ الاستحقاق ليس في الماضي البعيد
      if (values.due_date && values.due_date.isBefore(dayjs().subtract(1, 'year'))) {
        message.error('تاريخ الاستحقاق لا يمكن أن يكون في الماضي البعيد')
        return
      }

      const noteData = {
        ...values,
        note_number: values.note_number.trim(),
        amount: amount,
        issue_date: values.issue_date.format('YYYY-MM-DD'),
        due_date: values.due_date.format('YYYY-MM-DD'),
        created_by: 1 // يجب الحصول على معرف المستخدم الحالي
      }

      const response = await window.electronAPI.createPromissoryNote(noteData)
      if (response.success) {
        message.success('تم إضافة الكمبيالة بنجاح')
        loadNotes()
        setModalVisible(false)
        form.resetFields()
      } else {
        message.error('فشل في إضافة الكمبيالة')
      }
    } catch (_error) {
      message.error('خطأ في حفّ الكمبيالة')
    }
  }

  const handleStatusChange = async (noteId: number, status: string) => {
    try {
      const response = await window.electronAPI.updatePromissoryNoteStatus(noteId, status)
      if (response.success) {
        message.success('تم تحديث حالة الكمبيالة بنجاح')
        loadNotes()
      } else {
        message.error('فشل في تحديث حالة الكمبيالة')
      }
    } catch (_error) {
      message.error('خطأ في تحديث حالة الكمبيالة')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'issued': return 'blue'
      case 'active': return 'blue' // للتوافق مع الإصدارات القديمة
      case 'paid': return 'green'
      case 'dishonored': return 'red'
      case 'overdue': return 'red' // للتوافق مع الإصدارات القديمة
      case 'cancelled': return 'gray'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'issued': return 'صادرة'
      case 'active': return 'نشطة' // للتوافق مع الإصدارات القديمة
      case 'paid': return 'مدفوعة'
      case 'dishonored': return 'مرفوضة'
      case 'overdue': return 'متأخرة' // للتوافق مع الإصدارات القديمة
      case 'cancelled': return 'ملغية'
      default: return status
    }
  }

  const isOverdue = (dueDate: string, status: string) => {
    return (status === 'issued' || status === 'active') && dayjs(dueDate).isBefore(dayjs(), 'day')
  }

  const columns = [
    {
      title: 'رقم الكمبيالة',
      dataIndex: 'note_number',
      key: 'note_number',
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ₪ {amount?.toLocaleString() || 0}
        </span>
      ),
    },
    {
      title: 'المسحوب عليه',
      dataIndex: 'drawer_name',
      key: 'drawer_name',
    },
    {
      title: 'المستفيد',
      dataIndex: 'payee_name',
      key: 'payee_name',
    },
    {
      title: 'تاريخ الإصدار',
      dataIndex: 'issue_date',
      key: 'issue_date',
    },
    {
      title: 'تاريخ الاستحقاق',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (dueDate: string, record: any) => (
        <span style={{ 
          color: isOverdue(dueDate, record.status) ? '#ff4d4f' : '#000',
          fontWeight: isOverdue(dueDate, record.status) ? 'bold' : 'normal'
        }}>
          {dueDate}
          {isOverdue(dueDate, record.status) && <ExclamationOutlined style={{ marginLeft: '4px', color: '#ff4d4f' }} />}
        </span>
      ),
    },
    {
      title: 'مكان الدفع',
      dataIndex: 'place_of_payment',
      key: 'place_of_payment',
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: any) => {
        const actualStatus = isOverdue(record.due_date, status) ? 'overdue' : status
        return (
          <Tag color={getStatusColor(actualStatus)}>
            {getStatusText(actualStatus)}
          </Tag>
        )
      },
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: any) => (
        <Space>
          {(record.status === 'issued' || record.status === 'active') && !isOverdue(record.due_date, record.status) && (
            <Button
              type="primary"
              size="small"
              icon={<CheckOutlined />}
              onClick={() => handleStatusChange(record.id, 'paid')}
            >
              تسديد
            </Button>
          )}
          {(record.status === 'issued' || record.status === 'active') && (
            <Button
              danger
              size="small"
              icon={<CloseOutlined />}
              onClick={() => handleStatusChange(record.id, 'cancelled')}
            >
              إلغاء
            </Button>
          )}
          {(record.status === 'issued' || record.status === 'active') && (
            <Button
              size="small"
              icon={<CloseOutlined />}
              onClick={() => handleStatusChange(record.id, 'dishonored')}
              style={{ color: '#ff4d4f', borderColor: '#ff4d4f' }}
            >
              رفض
            </Button>
          )}

          {/* زر الطباعة الموحد */}
          <UnifiedPrintButton
            data={{
              id: record.id,
              title: 'سند إذني',
              subtitle: `رقم: ${record.note_number}`,
              date: record.issue_date,
              customer: {
                name: record.payee_name || 'غير محدد',
                phone: record.payee_phone || '',
                address: record.payee_address || ''
              },
              items: [{
                id: 1,
                name: 'مبلغ السند الإذني',
                description: `سند إذني - ${record.drawer_name || 'غير محدد'}`,
                quantity: 1,
                unit: 'مبلغ',
                unitPrice: record.amount || 0,
                total: record.amount || 0
              }],
              total: record.amount || 0,
              notes: `تاريخ الاستحقاق: ${record.due_date}\nالحالة: ${record.status}\nالساحب: ${record.drawer_name || 'غير محدد'}\nالمستفيد: ${record.payee_name || 'غير محدد'}`
            }}
            type="receipt"
            subType="payment"
            buttonText=""
            size="small"
            showDropdown={true}
            _documentId={`promissory_note_${record.id}`}
            onAfterPrint={() => message.success('تم طباعة السند الإذني بنجاح')}
            onError={() => message.error('فشل في طباعة السند الإذني')}
          />
        </Space>
      ),
    },
  ]

  const stats = {
    total: notes.length,
    active: notes.filter((note: any) => note.status === 'active').length,
    paid: notes.filter((note: any) => note.status === 'paid').length,
    overdue: notes.filter((note: any) => note.status === 'active' && isOverdue(note.due_date, note.status)).length,
    totalAmount: notes.reduce((sum: number, note: any) => sum + (note.amount || 0), 0),
    activeAmount: notes.filter((note: any) => note.status === 'active').reduce((sum: number, note: any) => sum + (note.amount || 0), 0)
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>💰 إدارة الكمبيالات</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            إدارة الكمبيالات والأوراق المالية
          </p>
        </div>
        <Button 
          type="default" 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
        >
          العودة
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي الكمبيالات"
              value={stats.total}
              valueStyle={{ color: '#1890ff' }}
              prefix={<WalletOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="كمبيالات نشطة"
              value={stats.active}
              valueStyle={{ color: '#722ed1' }}
              prefix={<WalletOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="كمبيالات مدفوعة"
              value={stats.paid}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="كمبيالات متأخرة"
              value={stats.overdue}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<ExclamationOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي المبالغ"
              value={stats.totalAmount}
              valueStyle={{ color: '#13c2c2' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="المبالغ النشطة"
              value={stats.activeAmount}
              valueStyle={{ color: '#fa8c16' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      <Card 
        title="قائمة الكمبيالات"
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={() => {
              setModalVisible(true)
              generateNoteNumber()
            }}
          >
            إضافة كمبيالة جديدة
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={notes}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* نموذج إضافة كمبيالة جديدة */}
      <Modal
        title="إضافة كمبيالة جديدة"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="note_number"
                label="رقم الكمبيالة"
                rules={[{ required: true, message: 'يرجى إدخال رقم الكمبيالة' }]}
              >
                <Input 
                  placeholder="PN000001" 
                  disabled
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="amount"
                label="المبلغ"
                rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  formatter={value => ('₪ ' + value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value: string | undefined) => value ? parseFloat(value.replace(/₪\s?|(,*)/g, '')) || 0 : 0}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="drawer_name"
                label="المسحوب عليه"
                rules={[{ required: true, message: 'يرجى إدخال اسم المسحوب عليه' }]}
              >
                <Input placeholder="اسم الشخص أو الشركة المسحوب عليها" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="payee_name"
                label="المستفيد"
                rules={[{ required: true, message: 'يرجى إدخال اسم المستفيد' }]}
              >
                <Input placeholder="اسم الشخص أو الشركة المستفيدة" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="issue_date"
                label="تاريخ الإصدار"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الإصدار' }]}
              >
                <DatePicker 
                  style={{ width: '100%' }}
                  placeholder="اختر تاريخ الإصدار"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="due_date"
                label="تاريخ الاستحقاق"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الاستحقاق' }]}
              >
                <DatePicker 
                  style={{ width: '100%' }}
                  placeholder="اختر تاريخ الاستحقاق"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="place_of_payment"
            label="مكان الدفع"
          >
            <Input placeholder="المكان المحدد لدفع الكمبيالة" />
          </Form.Item>

          <Form.Item
            name="notes"
            label="ملاحّات"
          >
            <Input.TextArea 
              placeholder="ملاحّات إضافية حول الكمبيالة"
              rows={3}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                إضافة الكمبيالة
              </Button>
              <Button onClick={() => {
                setModalVisible(false)
                form.resetFields()
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default PromissoryNoteManagement
