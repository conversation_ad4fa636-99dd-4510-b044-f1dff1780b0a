/**
 * إعدادات وتكوين المحرر البصري
 */

// أنواع العناصر المدعومة
export const ELEMENT_TYPES = {
  TEXT: 'text',
  HEADER: 'header', 
  TABLE: 'table',
  IMAGE: 'image',
  LINE: 'line',
  COMPANY_INFO: 'company-info'
} as const

// الأنماط الافتراضية للعناصر
export const DEFAULT_STYLES = {
  [ELEMENT_TYPES.TEXT]: {
    fontSize: 14,
    fontWeight: 'normal' as const,
    fontStyle: 'normal' as const,
    textDecoration: 'none' as const,
    textAlign: 'right' as const,
    color: '#333333',
    backgroundColor: 'transparent',
    padding: 8,
    margin: 4
  },
  [ELEMENT_TYPES.HEADER]: {
    fontSize: 20,
    fontWeight: 'bold' as const,
    fontStyle: 'normal' as const,
    textDecoration: 'none' as const,
    textAlign: 'center' as const,
    color: '#333333',
    backgroundColor: 'transparent',
    padding: 12,
    margin: 8
  },
  [ELEMENT_TYPES.TABLE]: {
    fontSize: 14,
    fontWeight: 'normal' as const,
    fontStyle: 'normal' as const,
    textDecoration: 'none' as const,
    textAlign: 'center' as const,
    color: '#333333',
    backgroundColor: 'transparent',
    padding: 0,
    margin: 8
  },
  [ELEMENT_TYPES.IMAGE]: {
    fontSize: 14,
    fontWeight: 'normal' as const,
    fontStyle: 'normal' as const,
    textDecoration: 'none' as const,
    textAlign: 'center' as const,
    color: '#333333',
    backgroundColor: 'transparent',
    padding: 0,
    margin: 8
  },
  [ELEMENT_TYPES.LINE]: {
    fontSize: 1,
    fontWeight: 'normal' as const,
    fontStyle: 'normal' as const,
    textDecoration: 'none' as const,
    textAlign: 'center' as const,
    color: '#ddd',
    backgroundColor: 'transparent',
    padding: 0,
    margin: 8,
    borderWidth: 1,
    borderColor: '#ddd'
  },
  [ELEMENT_TYPES.COMPANY_INFO]: {
    fontSize: 14,
    fontWeight: 'normal' as const,
    fontStyle: 'normal' as const,
    textDecoration: 'none' as const,
    textAlign: 'center' as const,
    color: '#666666',
    backgroundColor: 'transparent',
    padding: 8,
    margin: 4
  }
}

// المواضع الافتراضية للعناصر
export const DEFAULT_POSITIONS = {
  [ELEMENT_TYPES.TEXT]: { x: 10, y: 50, width: 80, height: 40 },
  [ELEMENT_TYPES.HEADER]: { x: 10, y: 20, width: 80, height: 60 },
  [ELEMENT_TYPES.TABLE]: { x: 10, y: 200, width: 80, height: 200 },
  [ELEMENT_TYPES.IMAGE]: { x: 10, y: 50, width: 30, height: 100 },
  [ELEMENT_TYPES.LINE]: { x: 10, y: 150, width: 80, height: 2 },
  [ELEMENT_TYPES.COMPANY_INFO]: { x: 10, y: 80, width: 80, height: 80 }
}

// المحتوى الافتراضي للعناصر
export const DEFAULT_CONTENT = {
  [ELEMENT_TYPES.TEXT]: 'نص جديد',
  [ELEMENT_TYPES.HEADER]: 'عنوان جديد',
  [ELEMENT_TYPES.TABLE]: 'جدول الأصناف',
  [ELEMENT_TYPES.IMAGE]: 'صورة',
  [ELEMENT_TYPES.LINE]: '',
  [ELEMENT_TYPES.COMPANY_INFO]: 'معلومات الشركة'
}

// إعدادات المحرر
export const EDITOR_CONFIG = {
  // حدود التكبير
  ZOOM_MIN: 50,
  ZOOM_MAX: 200,
  ZOOM_DEFAULT: 100,
  ZOOM_STEP: 10,

  // حدود الخطوط
  FONT_SIZE_MIN: 8,
  FONT_SIZE_MAX: 72,
  FONT_SIZE_DEFAULT: 14,

  // ألوان افتراضية
  DEFAULT_COLORS: [
    '#000000', '#333333', '#666666', '#999999',
    '#1890ff', '#52c41a', '#faad14', '#f5222d',
    '#722ed1', '#eb2f96', '#13c2c2', '#fa8c16'
  ],

  // أحجام الشبكة
  GRID_SIZE: 10,
  SNAP_TO_GRID: true,

  // حدود منطقة التحرير
  CANVAS_MIN_HEIGHT: 500,
  CANVAS_PADDING: 20
}

// دوال مساعدة
export const generateElementId = (type: string): string => {
  return `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

export const validateElementPosition = (position: any) => {
  return {
    x: Math.max(0, Math.min(100, position.x || 0)),
    y: Math.max(0, position.y || 0),
    width: Math.max(10, Math.min(100, position.width || 50)),
    height: Math.max(10, position.height || 30)
  }
}

export const validateElementStyles = (styles: any) => {
  return {
    fontSize: Math.max(EDITOR_CONFIG.FONT_SIZE_MIN, Math.min(EDITOR_CONFIG.FONT_SIZE_MAX, styles.fontSize || EDITOR_CONFIG.FONT_SIZE_DEFAULT)),
    fontWeight: ['normal', 'bold'].includes(styles.fontWeight) ? styles.fontWeight : 'normal',
    fontStyle: ['normal', 'italic'].includes(styles.fontStyle) ? styles.fontStyle : 'normal',
    textDecoration: ['none', 'underline'].includes(styles.textDecoration) ? styles.textDecoration : 'none',
    textAlign: ['left', 'center', 'right'].includes(styles.textAlign) ? styles.textAlign : 'right',
    color: styles.color || '#333333',
    backgroundColor: styles.backgroundColor || 'transparent',
    padding: Math.max(0, styles.padding || 0),
    margin: Math.max(0, styles.margin || 0)
  }
}

// تصدير الأنواع
export type ElementType = typeof ELEMENT_TYPES[keyof typeof ELEMENT_TYPES]
export type ElementStyles = typeof DEFAULT_STYLES[ElementType]
export type ElementPosition = typeof DEFAULT_POSITIONS[ElementType]
