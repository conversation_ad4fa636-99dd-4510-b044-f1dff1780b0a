import * as crypto from 'crypto'
import { Logger } from '../utils/logger'

/**
 * خدمة التشفير للإعدادات الحساسة
 */
export class EncryptionService {
  private static instance: EncryptionService
  private readonly algorithm = 'aes-256-gcm'
  private readonly keyLength = 32
  private readonly ivLength = 16
  private readonly tagLength = 16
  private encryptionKey: Buffer | null = null

  private constructor() {
    this.initializeKey()
  }

  public static getInstance(): EncryptionService {
    if (!EncryptionService.instance) {
      EncryptionService.instance = new EncryptionService()
    }
    return EncryptionService.instance
  }

  /**
   * تهيئة مفتاح التشفير
   */
  private initializeKey(): void {
    try {
      // في بيئة الإنتاج، يجب الحصول على المفتاح من متغير بيئة آمن
      const keySource = process.env.ENCRYPTION_KEY || 'default-key-for-development-only'
      
      // إنشاء مفتاح ثابت من المصدر
      this.encryptionKey = crypto.scryptSync(keySource, 'salt', this.keyLength)
      
      Logger.info('EncryptionService', '✅ تم تهيئة مفتاح التشفير بنجاح')
    } catch (error) {
      Logger.error('EncryptionService', '❌ خطأ في تهيئة مفتاح التشفير:', error)
      throw new Error('فشل في تهيئة خدمة التشفير')
    }
  }

  /**
   * تشفير نص
   */
  public encrypt(text: string): string {
    try {
      if (!this.encryptionKey) {
        throw new Error('مفتاح التشفير غير مهيأ')
      }

      if (!text || text.length === 0) {
        return text
      }

      const iv = crypto.randomBytes(this.ivLength)
      const cipher = crypto.createCipher(this.algorithm, this.encryptionKey)
      cipher.setAAD(Buffer.from('settings-encryption'))

      let encrypted = cipher.update(text, 'utf8', 'hex')
      encrypted += cipher.final('hex')

      const tag = cipher.getAuthTag()

      // دمج IV والـ tag مع النص المشفر
      const result = iv.toString('hex') + ':' + tag.toString('hex') + ':' + encrypted

      return result
    } catch (error) {
      Logger.error('EncryptionService', 'خطأ في التشفير:', error)
      throw new Error('فشل في تشفير البيانات')
    }
  }

  /**
   * فك تشفير نص
   */
  public decrypt(encryptedText: string): string {
    try {
      if (!this.encryptionKey) {
        throw new Error('مفتاح التشفير غير مهيأ')
      }

      if (!encryptedText || encryptedText.length === 0) {
        return encryptedText
      }

      // التحقق من تنسيق النص المشفر
      const parts = encryptedText.split(':')
      if (parts.length !== 3) {
        // إذا لم يكن النص مشفراً، إرجاعه كما هو (للتوافق مع البيانات القديمة)
        return encryptedText
      }

      const _iv = Buffer.from(parts[0], 'hex')
      const tag = Buffer.from(parts[1], 'hex')
      const encrypted = parts[2]

      const decipher = crypto.createDecipher(this.algorithm, this.encryptionKey)
      decipher.setAAD(Buffer.from('settings-encryption'))
      decipher.setAuthTag(tag)

      let decrypted = decipher.update(encrypted, 'hex', 'utf8')
      decrypted += decipher.final('utf8')

      return decrypted
    } catch (error) {
      Logger.error('EncryptionService', 'خطأ في فك التشفير:', error)
      // في حالة فشل فك التشفير، إرجاع النص الأصلي (للتوافق مع البيانات القديمة)
      return encryptedText
    }
  }

  /**
   * التحقق من كون الإعداد حساساً ويحتاج تشفير
   */
  public isSensitiveSetting(key: string): boolean {
    const sensitiveKeys = [
      'database_password',
      'email_password',
      'api_key',
      'secret_key',
      'encryption_key',
      'jwt_secret',
      'oauth_secret',
      'payment_api_key',
      'backup_encryption_key',
      'fingerprint_device_password',
      'sync_encryption_key'
    ]

    return sensitiveKeys.some(sensitiveKey => 
      key.toLowerCase().includes(sensitiveKey.toLowerCase())
    )
  }

  /**
   * تشفير إعداد إذا كان حساساً
   */
  public encryptIfSensitive(key: string, value: string): string {
    if (this.isSensitiveSetting(key)) {
      return this.encrypt(value)
    }
    return value
  }

  /**
   * فك تشفير إعداد إذا كان مشفراً
   */
  public decryptIfEncrypted(key: string, value: string): string {
    if (this.isSensitiveSetting(key)) {
      return this.decrypt(value)
    }
    return value
  }

  /**
   * إنشاء hash آمن لكلمة مرور
   */
  public hashPassword(password: string): string {
    try {
      const salt = crypto.randomBytes(16).toString('hex')
      const hash = crypto.scryptSync(password, salt, 64).toString('hex')
      return salt + ':' + hash
    } catch (error) {
      Logger.error('EncryptionService', 'خطأ في تشفير كلمة المرور:', error)
      throw new Error('فشل في تشفير كلمة المرور')
    }
  }

  /**
   * التحقق من كلمة مرور
   */
  public verifyPassword(password: string, hashedPassword: string): boolean {
    try {
      const parts = hashedPassword.split(':')
      if (parts.length !== 2) {
        return false
      }

      const salt = parts[0]
      const hash = parts[1]
      const hashToVerify = crypto.scryptSync(password, salt, 64).toString('hex')

      return hash === hashToVerify
    } catch (error) {
      Logger.error('EncryptionService', 'خطأ في التحقق من كلمة المرور:', error)
      return false
    }
  }

  /**
   * إنشاء مفتاح عشوائي آمن
   */
  public generateSecureKey(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex')
  }

  /**
   * إنشاء token آمن
   */
  public generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('base64url')
  }
}

export default EncryptionService
