# 📸 أمثلة استخدام نظام الصور المحسن

## 🚀 البدء السريع

### 1. تهيئة النظام

```typescript
import { initializeImageServices } from '../services/images'

// في بداية التطبيق
await initializeImageServices()
```

### 2. استخدام Hook بسيط

```typescript
import { useImageManager } from '../hooks/images'

const MyComponent = () => {
  const {
    images,
    primaryImage,
    loading,
    uploadImage,
    deleteImage,
    setPrimaryImage
  } = useImageManager({
    category: 'inventory',
    contextType: 'item',
    contextId: 123
  })

  const handleFileUpload = async (file: File) => {
    const success = await uploadImage(file)
    if (success) {
      console.log('تم رفع الصورة بنجاح!')
    }
  }

  return (
    <div>
      {loading && <p>جاري التحميل...</p>}
      {images.map(image => (
        <div key={image.id}>
          <img src={`file://${image.thumbnailPath || image.path}`} alt={image.name} />
          <button onClick={() => setPrimaryImage(image.id)}>
            تعيين كرئيسية
          </button>
          <button onClick={() => deleteImage(image.id)}>
            حذف
          </button>
        </div>
      ))}
    </div>
  )
}
```

### 3. استخدام المكون الموحد

```typescript
import { UnifiedImageManager } from '../components/images'

const ItemForm = ({ itemId }: { itemId: number }) => {
  return (
    <div>
      <h2>صور الصنف</h2>
      <UnifiedImageManager
        category="inventory"
        contextType="item"
        contextId={itemId}
        maxImages={5}
        allowMultiple={true}
        showThumbnails={true}
        uploadOptions={{
          maxSize: 5 * 1024 * 1024, // 5MB
          quality: 0.8,
          generateThumbnail: true
        }}
        onImagesChange={(images) => {
          console.log('تم تحديث الصور:', images)
        }}
      />
    </div>
  )
}
```

## 🔧 أمثلة متقدمة

### 1. رفع صور متعددة مع تقدم

```typescript
import { ImageCoreService } from '../services/images'

const AdvancedUpload = () => {
  const [progress, setProgress] = useState<number>(0)
  const imageService = ImageCoreService.getInstance()

  const handleMultipleUpload = async (files: File[]) => {
    try {
      const result = await imageService.uploadMultipleImages(
        files,
        'production',
        'production_order',
        456,
        {
          maxSize: 10 * 1024 * 1024,
          quality: 0.85,
          autoResize: true,
          watermark: true,
          watermarkText: 'شركة الأثاث'
        }
      )

      if (result.success) {
        console.log('تم رفع جميع الصور:', result.data)
      } else {
        console.error('خطأ في الرفع:', result.error)
      }
    } catch (error) {
      console.error('خطأ:', error)
    }
  }

  return (
    <div>
      <input
        type="file"
        multiple
        accept="image/*"
        onChange={(e) => {
          const files = Array.from(e.target.files || [])
          handleMultipleUpload(files)
        }}
      />
    </div>
  )
}
```

### 2. عارض الصور المتقدم

```typescript
import { EnhancedImageViewer } from '../components/images'
import { useImages } from '../hooks/images'

const ProductGallery = ({ productId }: { productId: number }) => {
  const { images, loading } = useImages('inventory', 'item', productId)
  const [viewerOpen, setViewerOpen] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)

  const openViewer = (index: number) => {
    setCurrentIndex(index)
    setViewerOpen(true)
  }

  return (
    <div>
      {/* معرض الصور المصغرة */}
      <div className="grid grid-cols-4 gap-2">
        {images.map((image, index) => (
          <img
            key={image.id}
            src={`file://${image.thumbnailPath || image.path}`}
            alt={image.name}
            className="cursor-pointer hover:opacity-80"
            onClick={() => openViewer(index)}
          />
        ))}
      </div>

      {/* عارض الصور المتقدم */}
      <EnhancedImageViewer
        images={images}
        currentIndex={currentIndex}
        isOpen={viewerOpen}
        onClose={() => setViewerOpen(false)}
        onImageChange={(index, image) => {
          setCurrentIndex(index)
          console.log('تم تغيير الصورة:', image.name)
        }}
        showThumbnails={true}
        showControls={true}
        allowDownload={true}
        allowFullscreen={true}
      />
    </div>
  )
}
```

### 3. إدارة صور الشيكات

```typescript
import { useImageManager } from '../hooks/images'

const CheckImageManager = ({ checkId }: { checkId: number }) => {
  const {
    images,
    primaryImage,
    uploadImage,
    deleteImage,
    updateImage,
    error,
    clearError
  } = useImageManager({
    category: 'checks',
    contextType: 'check',
    contextId: checkId,
    uploadOptions: {
      maxSize: 2 * 1024 * 1024, // 2MB للشيكات
      quality: 0.9, // جودة عالية للشيكات
      generateThumbnail: true
    }
  })

  const handleAddDescription = async (imageId: string, description: string) => {
    await updateImage(imageId, { description })
  }

  const handleAddTags = async (imageId: string, tags: string[]) => {
    await updateImage(imageId, { tags })
  }

  return (
    <div>
      <h3>صور الشيك</h3>
      
      {error && (
        <div className="error">
          {error}
          <button onClick={clearError}>×</button>
        </div>
      )}

      {/* رفع صورة جديدة */}
      <input
        type="file"
        accept="image/*"
        onChange={async (e) => {
          const file = e.target.files?.[0]
          if (file) {
            await uploadImage(file)
          }
        }}
      />

      {/* عرض الصور */}
      {images.map(image => (
        <div key={image.id} className="image-card">
          <img 
            src={`file://${image.thumbnailPath || image.path}`} 
            alt={image.name} 
          />
          
          <div className="image-info">
            <h4>{image.name}</h4>
            <p>{image.description}</p>
            
            {/* إضافة وصف */}
            <input
              type="text"
              placeholder="إضافة وصف..."
              onBlur={(e) => handleAddDescription(image.id, e.target.value)}
            />
            
            {/* إضافة علامات */}
            <input
              type="text"
              placeholder="علامات (مفصولة بفواصل)..."
              onBlur={(e) => {
                const tags = e.target.value.split(',').map(t => t.trim())
                handleAddTags(image.id, tags)
              }}
            />
            
            <button onClick={() => deleteImage(image.id)}>
              حذف
            </button>
          </div>
        </div>
      ))}
    </div>
  )
}
```

### 4. إحصائيات الصور

```typescript
import { ImageCoreService } from '../services/images'

const ImageStatistics = () => {
  const [stats, setStats] = useState(null)
  const imageService = ImageCoreService.getInstance()

  useEffect(() => {
    const loadStats = async () => {
      const result = await imageService.getImageStatistics()
      if (result.success) {
        setStats(result.data)
      }
    }
    loadStats()
  }, [])

  if (!stats) return <div>جاري التحميل...</div>

  return (
    <div className="stats-dashboard">
      <h2>إحصائيات الصور</h2>
      
      <div className="stats-grid">
        <div className="stat-card">
          <h3>إجمالي الصور</h3>
          <p>{stats.totalImages}</p>
        </div>
        
        <div className="stat-card">
          <h3>الحجم الإجمالي</h3>
          <p>{formatFileSize(stats.totalSize)}</p>
        </div>
        
        <div className="stat-card">
          <h3>متوسط الحجم</h3>
          <p>{formatFileSize(stats.averageSize)}</p>
        </div>
      </div>

      <div className="category-breakdown">
        <h3>توزيع الصور حسب الفئة</h3>
        {Object.entries(stats.byCategory).map(([category, count]) => (
          <div key={category}>
            <span>{category}: {count}</span>
          </div>
        ))}
      </div>
    </div>
  )
}
```

### 5. البحث في الصور

```typescript
import { ImageCoreService } from '../services/images'

const ImageSearch = () => {
  const [searchResults, setSearchResults] = useState([])
  const [loading, setLoading] = useState(false)
  const imageService = ImageCoreService.getInstance()

  const searchImages = async (criteria: {
    category?: string
    tags?: string[]
    dateFrom?: Date
    dateTo?: Date
  }) => {
    setLoading(true)
    try {
      const result = await imageService.getImages({
        category: criteria.category as any,
        tags: criteria.tags,
        dateFrom: criteria.dateFrom,
        dateTo: criteria.dateTo,
        sortBy: 'uploadedAt',
        sortOrder: 'desc'
      })

      if (result.success) {
        setSearchResults(result.data)
      }
    } catch (error) {
      console.error('خطأ في البحث:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <h2>البحث في الصور</h2>
      
      {/* نموذج البحث */}
      <form onSubmit={(e) => {
        e.preventDefault()
        const formData = new FormData(e.target as HTMLFormElement)
        searchImages({
          category: formData.get('category') as string,
          tags: (formData.get('tags') as string)?.split(',').map(t => t.trim()),
          dateFrom: formData.get('dateFrom') ? new Date(formData.get('dateFrom') as string) : undefined,
          dateTo: formData.get('dateTo') ? new Date(formData.get('dateTo') as string) : undefined
        })
      }}>
        <select name="category">
          <option value="">جميع الفئات</option>
          <option value="inventory">المخزون</option>
          <option value="production">الإنتاج</option>
          <option value="sales">المبيعات</option>
          <option value="checks">الشيكات</option>
        </select>
        
        <input name="tags" placeholder="العلامات (مفصولة بفواصل)" />
        <input name="dateFrom" type="date" />
        <input name="dateTo" type="date" />
        
        <button type="submit">بحث</button>
      </form>

      {/* نتائج البحث */}
      {loading && <p>جاري البحث...</p>}
      
      <div className="search-results">
        {searchResults.map(image => (
          <div key={image.id} className="search-result">
            <img src={`file://${image.thumbnailPath || image.path}`} alt={image.name} />
            <div>
              <h4>{image.name}</h4>
              <p>الفئة: {image.category}</p>
              <p>التاريخ: {new Date(image.uploadedAt).toLocaleDateString('ar')}</p>
              <p>العلامات: {image.tags.join(', ')}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
```

## 🎯 نصائح للاستخدام الأمثل

### 1. إدارة الذاكرة
```typescript
// تنظيف الموارد عند إلغاء تحميل المكون
useEffect(() => {
  return () => {
    // تنظيف cache إذا لزم الأمر
    imageService.cleanup()
  }
}, [])
```

### 2. معالجة الأخطاء
```typescript
const handleImageOperation = async () => {
  try {
    const result = await imageService.uploadImage(file, category, contextType, contextId)
    if (!result.success) {
      // معالجة الخطأ
      showErrorMessage(result.error)
    }
  } catch (error) {
    // معالجة الاستثناءات
    showErrorMessage('حدث خطأ غير متوقع')
  }
}
```

### 3. تحسين الأداء
```typescript
// استخدام lazy loading للصور
const LazyImage = ({ src, alt }) => {
  const [loaded, setLoaded] = useState(false)
  
  return (
    <img
      src={loaded ? src : 'placeholder.jpg'}
      alt={alt}
      loading="lazy"
      onLoad={() => setLoaded(true)}
    />
  )
}
```

### 4. التكامل مع النظام الحالي
```typescript
// استخدام النظام الجديد مع الكود الموجود
const LegacyComponent = () => {
  const { images } = useImages('inventory', 'item', itemId)
  
  // تحويل إلى التنسيق القديم إذا لزم الأمر
  const legacyImages = images.map(img => ({
    id: img.id,
    url: `file://${img.path}`,
    name: img.name
  }))
  
  return <OldImageComponent images={legacyImages} />
}
```
