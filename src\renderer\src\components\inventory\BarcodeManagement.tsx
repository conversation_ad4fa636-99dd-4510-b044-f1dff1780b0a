import React, { useState, useEffect } from 'react'
import {
  Card, Table, Button, Modal, Form, Input, Space,
  Popconfirm, Typography, Row, Col, Tag, Tooltip, Switch, App} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  PlusOutlined, DeleteOutlined, BarcodeOutlined, SearchOutlined,
  StarOutlined, StarFilled, PrinterOutlined, FileExcelOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { Item, ItemBarcode, ApiResponse } from '../../types/global'
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig'
import * as XLSX from 'xlsx'

const { Title } = Typography

const StyledCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
`

const ActionButton = styled(Button)`
  margin-left: 8px;
`

const BarcodeDisplay = styled.div`
  font-family: 'Courier New', monospace;
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
  background: #f0f8ff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
`

interface BarcodeManagementProps {
  onBack?: () => void
}

const BarcodeManagement: React.FC<BarcodeManagementProps> = ({ onBack: _onBack }) => {
  const { message: messageApi } = App.useApp()
  const [items, setItems] = useState<Item[]>([])
  const [selectedItem, setSelectedItem] = useState<Item | null>(null)
  const [barcodes, setBarcodes] = useState<ItemBarcode[]>([])
  const [, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [searchModalVisible, setSearchModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [searchForm] = Form.useForm()

  useEffect(() => {
    loadItems()
  }, [])

  const loadItems = async () => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getItems()
        if (response && typeof response === 'object' && 'success' in response) {
          // استجابة من نوع ApiResponse
          const apiResponse = response as ApiResponse
          if (apiResponse.success) {
            const itemsData = apiResponse.data || []
            setItems(itemsData.filter((item: Item) => item.is_active))
            Logger.info('BarcodeManagement', '✅ تم تحميل ${itemsData.length} صنف بنجاح')
          } else {
            const errorMessage = apiResponse.message || 'فشل في تحميل الأصناف'
            Logger.error('BarcodeManagement', '❌ خطأ في تحميل الأصناف:', errorMessage)
            messageApi.error(errorMessage)
          }
        } else if (Array.isArray(response)) {
          // للتوافق مع الإصدارات القديمة - استجابة مباشرة
          setItems(response.filter((item: Item) => item.is_active))
          Logger.info('BarcodeManagement', '✅ تم تحميل ${response.length} صنف بنجاح')
        } else {
          Logger.error('BarcodeManagement', '❌ استجابة غير متوقعة من getItems')
          messageApi.error('فشل في تحميل الأصناف')
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('BarcodeManagement', '❌ window.electronAPI غير متوفر')
        messageApi.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل الأصناف'
      Logger.error('BarcodeManagement', '❌ خطأ في تحميل الأصناف:', error)
      messageApi.error(`خطأ في تحميل الأصناف: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  const loadBarcodes = async (itemId: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getItemBarcodes(itemId)
        if (response && typeof response === 'object' && 'success' in response) {
          // استجابة من نوع ApiResponse
          const apiResponse = response as ApiResponse
          if (apiResponse.success && Array.isArray(apiResponse.data)) {
            setBarcodes(apiResponse.data)
          } else {
            Logger.error('BarcodeManagement', 'خطأ في استجابة الباركودات:', response)
            setBarcodes([])
          }
        } else if (Array.isArray(response)) {
          // استجابة مباشرة
          setBarcodes(response)
        } else {
          Logger.error('BarcodeManagement', 'خطأ في استجابة الباركودات:', response)
          setBarcodes([])
        }
      }
    } catch (error) {
      Logger.error('BarcodeManagement', 'خطأ في تحميل الباركودات:', error)
      messageApi.error('فشل في تحميل الباركودات')
      setBarcodes([])
    }
  }

  const handleItemSelect = (item: Item) => {
    setSelectedItem(item)
    loadBarcodes(item.id)
  }

  const handleAddBarcode = () => {
    if (!selectedItem) {
      messageApi.warning('يرجى اختيار صنف أولاً')
      return
    }
    form.resetFields()
    form.setFieldsValue({ item_id: selectedItem.id })
    setModalVisible(true)
  }

  const handleDeleteBarcode = async (barcodeId: number) => {
    try {
      if (window.electronAPI) {
        const response: ApiResponse = await window.electronAPI.deleteItemBarcode(barcodeId)
        if (response.success) {
          messageApi.success('تم حذف الباركود بنجاح')
          if (selectedItem) {
            loadBarcodes(selectedItem.id)
          }
        } else {
          messageApi.error(response.message || 'فشل في حذف الباركود')
        }
      }
    } catch (error) {
      Logger.error('BarcodeManagement', 'خطأ في حذف الباركود:', error)
      messageApi.error('فشل في حذف الباركود')
    }
  }

  const checkBarcodeUniqueness = async (barcode: string): Promise<boolean> => {
    try {
      if (window.electronAPI) {
        // البحث في جميع الباركودات الموجودة
        const allItems = await window.electronAPI.getItems()
        let itemsToCheck: Item[] = []

        if (allItems && typeof allItems === 'object' && 'success' in allItems) {
          const apiResponse = allItems as ApiResponse
          if (apiResponse.success && apiResponse.data) {
            itemsToCheck = apiResponse.data
          }
        } else if (Array.isArray(allItems)) {
          itemsToCheck = allItems
        }

        if (itemsToCheck.length > 0) {
          for (const item of itemsToCheck) {
            const itemBarcodes = await window.electronAPI.getItemBarcodes(item.id)
            if (itemBarcodes && itemBarcodes.length > 0) {
              const existingBarcode = itemBarcodes.find((bc: any) =>
                bc.barcode.toLowerCase() === barcode.toLowerCase()
              )
              if (existingBarcode) {
                return false
              }
            }
          }
        }
      }
      return true
    } catch (error) {
      Logger.error('BarcodeManagement', 'خطأ في التحقق من تفرد الباركود:', error)
      return true // في حالة الخطأ، نسمح بالمتابعة
    }
  }

  const validateBarcodeFormat = (barcode: string): boolean => {
    // التحقق من تنسيق الباركود الأساسي
    if (!barcode || barcode.trim().length < 3) {
      return false
    }

    // التحقق من أن الباركود يحتوي على أرقام وحروف فقط
    const validPattern = /^[A-Za-z0-9]+$/
    return validPattern.test(barcode.trim())
  }

  const handleSubmit = async (values: any) => {
    try {
      if (window.electronAPI) {
        // التحقق من صحة تنسيق الباركود
        if (!validateBarcodeFormat(values.barcode)) {
          messageApi.error('تنسيق الباركود غير صحيح. يجب أن يحتوي على أرقام وحروف فقط (3 أحرف على الأقل)')
          form.setFields([{
            name: 'barcode',
            errors: ['تنسيق الباركود غير صحيح']
          }])
          return
        }

        // التحقق من تفرد الباركود
        const isUnique = await checkBarcodeUniqueness(values.barcode)
        if (!isUnique) {
          messageApi.error('هذا الباركود مستخدم بالفعل لصنف آخر')
          form.setFields([{
            name: 'barcode',
            errors: ['هذا الباركود مستخدم بالفعل']
          }])
          return
        }

        const response: ApiResponse = await window.electronAPI.addItemBarcode(values)
        if (response.success) {
          messageApi.success('تم إضافة الباركود بنجاح')
          setModalVisible(false)
          form.resetFields()
          if (selectedItem) {
            loadBarcodes(selectedItem.id)
          }
        } else {
          messageApi.error(response.message || 'فشل في إضافة الباركود')
        }
      } else {
        messageApi.error('لا يمكن الوصول إلى قاعدة البيانات')
      }
    } catch (error) {
      Logger.error('BarcodeManagement', 'خطأ في إضافة الباركود:', error)
      messageApi.error('فشل في إضافة الباركود')
    }
  }

  const handleSearchByBarcode = async (values: any) => {
    try {
      if (window.electronAPI) {
        const item = await window.electronAPI.findItemByBarcode(values.barcode)
        if (item) {
          setSelectedItem(item)
          loadBarcodes(item.id)
          setSearchModalVisible(false)
          searchForm.resetFields()
          messageApi.success(`تم العثور على الصنف: ${item.name}`)
        } else {
          messageApi.warning('لم يتم العثور على صنف بهذا الباركود')
        }
      }
    } catch (error) {
      Logger.error('BarcodeManagement', 'خطأ في البحث بالباركود:', error)
      messageApi.error('فشل في البحث بالباركود')
    }
  }

  // وّيفة طباعة الباركودات الموحدة
  const handlePrintBarcodes = async () => {
    if (!selectedItem || barcodes.length === 0) {
      messageApi.warning('يرجى اختيار صنف يحتوي على باركودات')
      return
    }

    try {
      const { MasterPrintService } = await import('../../services/MasterPrintService')
      const printService = MasterPrintService.getInstance()

      const printData = {
        title: `باركودات الصنف - ${selectedItem.name}`,
        subtitle: `كود الصنف: ${selectedItem.code}`,
        date: new Date().toLocaleDateString('ar-SA'),
        items: barcodes.map(barcode => ({
          name: barcode.barcode,
          description: `نوع: ${barcode.type}`,
          quantity: 1,
          unit: 'باركود',
          unitPrice: 0,
          total: 0
        })),
        total: 0,
        notes: `باركودات الصنف: ${selectedItem.name}`
      }

      await printService.print(printData, {
        type: 'report',
        subType: 'inventory',
        showLogo: true,
        showHeader: true,
        showFooter: true
      })

      messageApi.success('تم إرسال الباركودات للطباعة')
    } catch (error) {
      Logger.error('BarcodeManagement', 'خطأ في الطباعة:', error)
      messageApi.error('فشل في طباعة الباركودات')
    }
  }

  // وّيفة التصدير إلى Excel
  const handleExportToExcel = () => {
    if (!selectedItem || barcodes.length === 0) {
      messageApi.warning('يرجى اختيار صنف يحتوي على باركودات')
      return
    }

    try {
      const exportData = barcodes.map((barcode, index) => ({
        'الرقم': index + 1,
        'الباركود': barcode.barcode,
        'النوع': barcode.is_primary ? 'أساسي' : 'ثانوي',
        'الصنف': selectedItem.name,
        'كود الصنف': selectedItem.code,
        'الفئة': selectedItem.category_name || '-',
        'تاريخ الإضافة': DateUtils.formatForDisplay(barcode.created_at, DATE_FORMATS.DISPLAY_DATE)
      }))

      const worksheet = XLSX.utils.json_to_sheet(exportData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'الباركودات')

      const fileName = `باركودات_${selectedItem.name}_${DateUtils.formatForDisplay(new Date().toISOString(), DATE_FORMATS.DISPLAY_DATE)}.xlsx`
      XLSX.writeFile(workbook, fileName)

      messageApi.success('تم تصدير البيانات بنجاح')
    } catch (error) {
      Logger.error('BarcodeManagement', 'خطأ في التصدير:', error)
      messageApi.error('حدث خطأ أثناء التصدير')
    }
  }

  // إنشاء محتوى طباعة الباركودات
  const _generateBarcodePrintContent = () => {
    const currentDate = DateUtils.formatForDisplay(new Date().toISOString(), DATE_FORMATS.DISPLAY_DATE_TIME)

    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>باركودات الصنف - ${selectedItem?.name}</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 20px;
          }
          .header h1 {
            color: #1890ff;
            margin: 0;
            font-size: 24px;
          }
          .item-info {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
          }
          .barcode-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
          }
          .barcode-card {
            border: 2px solid #1890ff;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: white;
          }
          .barcode-display {
            font-family: 'Courier New', monospace;
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
            background: #f0f8ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            letter-spacing: 2px;
          }
          .barcode-type {
            font-weight: bold;
            color: #fa8c16;
          }
          .primary-barcode {
            border-color: #faad14;
            background: #fffbe6;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 12px;
          }
          @media print {
            body { margin: 0; }
            .no-print { display: none; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>باركودات الصنف</h1>
          <h2>${selectedItem?.name}</h2>
          <p>تاريخ الطباعة: ${currentDate}</p>
        </div>

        <div class="item-info">
          <strong>معلومات الصنف:</strong><br>
          <strong>الاسم:</strong> ${selectedItem?.name}<br>
          <strong>الكود:</strong> ${selectedItem?.code}<br>
          <strong>الفئة:</strong> ${selectedItem?.category_name || 'غير محدد'}<br>
          <strong>عدد الباركودات:</strong> ${barcodes.length}
        </div>

        <div class="barcode-grid">
          ${barcodes.map(barcode => `
            <div class="barcode-card ${barcode.is_primary ? 'primary-barcode' : ''}">
              <div class="barcode-type">${barcode.is_primary ? '⭐ باركود أساسي' : 'باركود ثانوي'}</div>
              <div class="barcode-display">${barcode.barcode}</div>
              <div>تاريخ الإضافة: ${DateUtils.formatForDisplay(barcode.created_at, DATE_FORMATS.DISPLAY_DATE)}</div>
            </div>
          `).join('')}
        </div>

        <div class="footer">
          <p>تم إنشاء هذا التقرير بواسطة نّام إدارة المخزون - ${currentDate}</p>
        </div>
      </body>
      </html>
    `
  }

  const barcodeColumns = [
    {
      title: 'الباركود',
      dataIndex: 'barcode',
      key: 'barcode',
      render: (barcode: string) => (
        <BarcodeDisplay>{barcode}</BarcodeDisplay>
      )
    },
    {
      title: 'النوع',
      dataIndex: 'is_primary',
      key: 'is_primary',
      render: (isPrimary: boolean) => (
        <Tag 
          color={isPrimary ? 'gold' : 'blue'} 
          icon={isPrimary ? <StarFilled /> : <StarOutlined />}
        >
          {isPrimary ? 'أساسي' : 'ثانوي'}
        </Tag>
      )
    },
    {
      title: 'تاريخ الإضافة (ميلادي)',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 140,
      render: (date: string) => DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE)
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: ItemBarcode) => (
        <Popconfirm
          title="هل أنت متأكد من حذف هذا الباركود؟"
          onConfirm={() => handleDeleteBarcode(record.id)}
          okText="نعم"
          cancelText="لا"
        >
          <Tooltip title="حذف الباركود">
            <ActionButton
              danger
              size="small"
              icon={<DeleteOutlined />}
            />
          </Tooltip>
        </Popconfirm>
      )
    }
  ]

  return (
    <div>
      <Row gutter={16}>
        {/* قائمة الأصناف */}
        <Col span={12}>
          <StyledCard
            title={
              <Space>
                <BarcodeOutlined />
                <Title level={4} style={{ margin: 0 }}>الأصناف</Title>
              </Space>
            }
            extra={
              <Button
                icon={<SearchOutlined />}
                onClick={() => setSearchModalVisible(true)}
              >
                البحث بالباركود
              </Button>
            }
          >
            <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
              {items.map(item => (
                <Card
                  key={item.id}
                  size="small"
                  style={{ 
                    marginBottom: 8, 
                    cursor: 'pointer',
                    border: selectedItem?.id === item.id ? '2px solid #1890ff' : '1px solid #d9d9d9'
                  }}
                  onClick={() => handleItemSelect(item)}
                >
                  <Space direction="vertical" size={0} style={{ width: '100%' }}>
                    <div style={{ fontWeight: 'bold' }}>{item.name}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      كود: {item.code} | فئة: {item.category_name || 'غير محدد'}
                    </div>
                  </Space>
                </Card>
              ))}
            </div>
          </StyledCard>
        </Col>

        {/* باركودات الصنف المحدد */}
        <Col span={12}>
          <StyledCard
            title={
              selectedItem ? (
                <Space>
                  <BarcodeOutlined />
                  <Title level={4} style={{ margin: 0 }}>
                    باركودات: {selectedItem.name}
                  </Title>
                </Space>
              ) : (
                <Space>
                  <BarcodeOutlined />
                  <Title level={4} style={{ margin: 0 }}>الباركودات</Title>
                </Space>
              )
            }
            extra={
              selectedItem && (
                <Space>
                  <Button
                    icon={<PrinterOutlined />}
                    onClick={handlePrintBarcodes}
                    disabled={barcodes.length === 0}
                  >
                    طباعة
                  </Button>
                  <Button
                    icon={<FileExcelOutlined />}
                    onClick={handleExportToExcel}
                    disabled={barcodes.length === 0}
                    style={{ color: '#52c41a', borderColor: '#52c41a' }}
                  >
                    تصدير Excel
                  </Button>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAddBarcode}
                  >
                    إضافة باركود
                  </Button>
                </Space>
              )
            }
          >
            {selectedItem ? (
              <div>
                <div style={{ marginBottom: 16, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>
                  <Space direction="vertical" size={4}>
                    <div><strong>الصنف:</strong> {selectedItem.name}</div>
                    <div><strong>الكود:</strong> {selectedItem.code}</div>
                    <div><strong>الفئة:</strong> {selectedItem.category_name || 'غير محدد'}</div>
                  </Space>
                </div>
                
                <Table
                  columns={barcodeColumns}
                  dataSource={barcodes}
                  rowKey="id"
                  size="small"
                  pagination={false}
                  locale={{ emptyText: 'لا توجد باركودات لهذا الصنف' }}
                />
              </div>
            ) : (
              <div style={{ textAlign: 'center', color: '#999', padding: '60px 0' }}>
                اختر صنفاً من القائمة لعرض الباركودات
              </div>
            )}
          </StyledCard>
        </Col>
      </Row>

      {/* نموذج إضافة باركود */}
      <Modal
        title="إضافة باركود جديد"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="item_id"
            hidden
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="barcode"
            label="الباركود"
            rules={[
              { required: true, message: 'يرجى إدخال الباركود' },
              { min: 8, message: 'الباركود يجب أن يكون 8 أرقام على الأقل' }
            ]}
          >
            <Input 
              placeholder="أدخل الباركود"
              style={{ fontFamily: 'monospace', fontSize: '16px' }}
            />
          </Form.Item>

          <Form.Item
            name="is_primary"
            label="نوع الباركود"
            valuePropName="checked"
          >
            <Switch 
              checkedChildren="أساسي" 
              unCheckedChildren="ثانوي"
              onChange={(checked) => {
                if (checked) {
                  messageApi.info('سيتم تعيين هذا الباركود كأساسي وإلغاء الأساسية من الباركودات الأخرى')
                }
              }}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                إضافة
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نموذج البحث بالباركود */}
      <Modal
        title="البحث بالباركود"
        open={searchModalVisible}
        onCancel={() => setSearchModalVisible(false)}
        footer={null}
        width={400}
      >
        <Form
          form={searchForm}
          layout="vertical"
          onFinish={handleSearchByBarcode}
        >
          <Form.Item
            name="barcode"
            label="الباركود"
            rules={[{ required: true, message: 'يرجى إدخال الباركود للبحث' }]}
          >
            <Input 
              placeholder="امسح أو أدخل الباركود"
              style={{ fontFamily: 'monospace', fontSize: '16px' }}
              autoFocus
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => setSearchModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                بحث
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default BarcodeManagement
