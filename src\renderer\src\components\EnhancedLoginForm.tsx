import React, { useState, useEffect, useCallback } from 'react'
import {
  Form, Input, Button, Card, Typography, Space, App, Alert,
  Checkbox, Modal, Progress, Select, Spin} from 'antd'
import { logger as Logger } from './../utils/logger'
import {
  UserOutlined, LockOutlined, EyeInvisibleOutlined,
  EyeTwoTone, KeyOutlined,
  ClockCircleOutlined, SecurityScanOutlined, TeamOutlined, ReloadOutlined,
  CrownOutlined, SettingOutlined, ShopOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import { LoginResponse } from '../types/global'
import dayjs from 'dayjs'
import {
  AnimatedBackground,
  GlassCard,
  EnhancedButton,
  EnhancedPasswordInput,
  EnhancedForm
} from './common/StyledComponents'
import AnimatedLogo from './common/AnimatedLogo'

const { Title, Text, Link } = Typography

const LogoContainer = styled.div`
  text-align: center;
  margin-bottom: 40px;
`

const SecurityInfo = styled.div`
  background: #f6f8fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 24px;
  border-left: 4px solid #0078D4;
`

const AttemptWarning = styled(Alert)`
  margin-bottom: 16px;
  border-radius: 8px;
`

interface EnhancedLoginFormProps {
  onLogin: (credentials: { username: string; password: string; rememberMe?: boolean }) => Promise<LoginResponse>
}

interface LoginAttempt {
  timestamp: string
  ip_address: string
  success: boolean
}

const EnhancedLoginForm: React.FC<EnhancedLoginFormProps> = ({ onLogin }) => {
  const { message } = App.useApp() // استخدام App context لحل تحذير Antd
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [loginAttempts, setLoginAttempts] = useState(0)
  const [isBlocked, setIsBlocked] = useState(false)
  const [blockTimeRemaining, setBlockTimeRemaining] = useState(0)
  const [showForgotPassword, setShowForgotPassword] = useState(false)
  const [forgotPasswordForm] = Form.useForm()
  const [systemInfo, setSystemInfo] = useState<any>({})
  const [recentAttempts, setRecentAttempts] = useState<LoginAttempt[]>([])
  const [activeUsers, setActiveUsers] = useState<any[]>([])
  const [loadingUsers, setLoadingUsers] = useState(false)

  // تحميل البيانات المحفوّة
  const loadSavedCredentials = useCallback(() => {
    try {
      const savedUsername = localStorage.getItem('rememberedUsername')
      const savedPassword = localStorage.getItem('rememberedPassword')
      const rememberMe = localStorage.getItem('rememberMe') === 'true'

      if (rememberMe && savedUsername) {
        form.setFieldsValue({
          username: savedUsername,
          password: savedPassword || '',
          rememberMe: true
        })
      }
    } catch (error) {
      Logger.error('EnhancedLoginForm', 'Error loading saved credentials:', error as Error)
    }
  }, [form])

  // وضع البيانات الافتراضية
  const setDefaultPassword = useCallback(() => {
    const currentUsername = form.getFieldValue('username')
    const currentPassword = form.getFieldValue('password')

    if (!currentUsername) {
      form.setFieldValue('username', 'admin') // اسم المستخدم الافتراضي
    }
    if (!currentPassword) {
      form.setFieldValue('password', 'admin123') // كلمة المرور الافتراضية
    }
  }, [form])

  const loadSystemInfo = useCallback(async () => {
    try {
      if (window.electronAPI) {
        const info = await window.electronAPI.getSystemInfo()
        setSystemInfo(info)
      } else {
        // معلومات وهمية
        setSystemInfo({
          company_name: 'شركة فارس للأثاث والدهانات',
          version: '1.0.0',
          last_backup: '2024-06-25T02:00:00Z'
        })
      }
    } catch (error) {
      Logger.error('EnhancedLoginForm', 'Error loading system info:', error as Error)
    }
  }, [])

  const loadRecentAttempts = useCallback(async () => {
    try {
      if (window.electronAPI) {
        const attempts = await window.electronAPI.getRecentLoginAttempts()
        if (attempts && Array.isArray(attempts)) {
          setRecentAttempts(attempts)
        }
      } else {
        // محاولات وهمية
        const mockAttempts: LoginAttempt[] = [
          {
            timestamp: '2024-06-25T10:30:00Z',
            ip_address: '*************',
            success: true
          },
          {
            timestamp: '2024-06-24T16:45:00Z',
            ip_address: '*************',
            success: false
          }
        ]
        setRecentAttempts(mockAttempts)
      }
    } catch (error) {
      Logger.error('EnhancedLoginForm', 'Error loading recent attempts:', error as Error)
    }
  }, [])

  // جلب المستخدمين النشطين
  const loadActiveUsers = useCallback(async () => {
    try {
      setLoadingUsers(true)
      if (window.electronAPI) {
        const response = await window.electronAPI.getActiveUsers()
        if (response.success && response.data) {
          setActiveUsers(response.data)
          Logger.info('EnhancedLoginForm', `تم جلب ${response.data.length} مستخدم نشط`)
        } else {
          Logger.error('EnhancedLoginForm', 'فشل في جلب المستخدمين النشطين:', new Error(response.message || 'خطأ غير معروف'))
        }
      }
    } catch (error) {
      Logger.error('EnhancedLoginForm', 'خطأ في جلب المستخدمين النشطين:', error as Error)
    } finally {
      setLoadingUsers(false)
    }
  }, [])

  const checkLoginBlock = useCallback(() => {
    const storedAttempts = localStorage.getItem('loginAttempts')
    const storedBlockTime = localStorage.getItem('loginBlockTime')

    if (storedAttempts) {
      const attempts = parseInt(storedAttempts)
      setLoginAttempts(attempts)

      if (attempts >= 5 && storedBlockTime) {
        const blockTime = parseInt(storedBlockTime)
        const now = Date.now()
        const timeRemaining = Math.max(0, Math.ceil((blockTime - now) / 1000))

        if (timeRemaining > 0) {
          setIsBlocked(true)
          setBlockTimeRemaining(timeRemaining)
        } else {
          // انتهت فترة الحّر
          localStorage.removeItem('loginAttempts')
          localStorage.removeItem('loginBlockTime')
          setLoginAttempts(0)
        }
      }
    }
  }, [])

  useEffect(() => {
    loadSystemInfo()
    loadRecentAttempts()
    checkLoginBlock()
    loadSavedCredentials() // تحميل البيانات المحفوّة
    loadActiveUsers() // تحميل المستخدمين النشطين
    setDefaultPassword() // وضع كلمة المرور الافتراضية
  }, [loadSystemInfo, loadRecentAttempts, checkLoginBlock, loadSavedCredentials, loadActiveUsers, setDefaultPassword])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isBlocked && blockTimeRemaining > 0) {
      interval = setInterval(() => {
        setBlockTimeRemaining(prev => {
          if (prev <= 1) {
            setIsBlocked(false)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isBlocked, blockTimeRemaining])

  // حفّ بيانات تسجيل الدخول
  const saveCredentials = (username: string, password: string, rememberMe: boolean) => {
    try {
      if (rememberMe) {
        localStorage.setItem('rememberedUsername', username)
        localStorage.setItem('rememberedPassword', password)
        localStorage.setItem('rememberMe', 'true')
      } else {
        localStorage.removeItem('rememberedUsername')
        localStorage.removeItem('rememberedPassword')
        localStorage.removeItem('rememberMe')
      }
    } catch (error) {
      Logger.error('EnhancedLoginForm', 'Error saving credentials:', error as Error)
    }
  }



  const handleLoginAttempt = (success: boolean) => {
    if (success) {
      // نجح تسجيل الدخول - إعادة تعيين المحاولات
      localStorage.removeItem('loginAttempts')
      localStorage.removeItem('loginBlockTime')
      setLoginAttempts(0)
      setIsBlocked(false)
    } else {
      // فشل تسجيل الدخول
      const newAttempts = loginAttempts + 1
      setLoginAttempts(newAttempts)
      localStorage.setItem('loginAttempts', newAttempts.toString())
      
      if (newAttempts >= 5) {
        // حّر لمدة 15 دقيقة
        const blockUntil = Date.now() + (15 * 60 * 1000)
        localStorage.setItem('loginBlockTime', blockUntil.toString())
        setIsBlocked(true)
        setBlockTimeRemaining(15 * 60)
        
        message.error('تم حّر تسجيل الدخول لمدة 15 دقيقة بسبب المحاولات المتكررة الفاشلة')
      }
    }
  }

  const handleSubmit = async (values: any) => {
    if (isBlocked) {
      message.warning(`تسجيل الدخول محّور - يرجى الانتّار ${Math.ceil(blockTimeRemaining / 60)} دقيقة قبل المحاولة مرة أخرى`)
      return
    }

    setLoading(true)
    try {
      const response = await onLogin({
        username: values.username,
        password: values.password,
        rememberMe: values.rememberMe
      })

      if (response.success) {
        handleLoginAttempt(true)
        // حفّ البيانات إذا كان "تذكرني" مفعل
        saveCredentials(values.username, values.password, values.rememberMe || false)
        message.success(`تم تسجيل الدخول بنجاح - مرحباً ${response.user?.full_name || values.username}`)
      } else {
        handleLoginAttempt(false)
        message.error(response.message || 'اسم المستخدم أو كلمة المرور غير صحيحة')
      }
    } catch {
      handleLoginAttempt(false)
      message.error('حدث خطأ أثناء محاولة تسجيل الدخول')
    } finally {
      setLoading(false)
    }
  }

  const handleForgotPassword = async (values: any) => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.resetPasswordRequest(values.identifier)
        if (result.success) {
          message.success('تم إرسال رابط إعادة تعيين كلمة المرور - يرجى التحقق من بريدك الإلكتروني')
          setShowForgotPassword(false)
        } else {
          message.error(result.message || 'فشل في إرسال رابط إعادة التعيين')
        }
      } else {
        // محاكاة النجاح
        message.success('تم إرسال رابط إعادة تعيين كلمة المرور - يرجى التحقق من بريدك الإلكتروني')
        setShowForgotPassword(false)
      }
    } catch {
      message.error('فشل في إرسال رابط إعادة تعيين كلمة المرور')
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  /*
  const getPasswordStrength = (password: string) => {
    let strength = 0
    if (password.length >= 8) strength += 25
    if (/[A-Z]/.test(password)) strength += 25
    if (/[0-9]/.test(password)) strength += 25
    if (/[^A-Za-z0-9]/.test(password)) strength += 25
    return strength
  }
  */

  return (
    <AnimatedBackground style={{
      height: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      direction: 'rtl'
    }}>
      <GlassCard $animated={true} style={{ width: '500px', maxWidth: '90vw' }}>
        <LogoContainer>
          <AnimatedLogo
            size="lg"
            showText={false}
            showSubtitle={false}
            variant="minimal"
            style={{ marginBottom: '16px' }}
          />
          <Title level={3} style={{
            margin: 0,
            background: 'linear-gradient(135deg, #FF6B6B, #4ECDC4)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            {systemInfo.company_name || 'نّام إدارة الأعمال'}
          </Title>
          <Text type="secondary">
            الإصدار {systemInfo.version || '1.0.0'}
          </Text>
        </LogoContainer>

        {isBlocked && (
          <AttemptWarning
            message="تم حّر تسجيل الدخول مؤقتاً"
            description={
              <div>
                <div>تم حّر تسجيل الدخول بسبب المحاولات المتكررة الفاشلة</div>
                <div style={{ marginTop: 8 }}>
                  <ClockCircleOutlined /> الوقت المتبقي: {formatTime(blockTimeRemaining)}
                </div>
                <Progress 
                  percent={Math.max(0, 100 - (blockTimeRemaining / (15 * 60)) * 100)} 
                  showInfo={false}
                  strokeColor="#ff4d4f"
                  style={{ marginTop: 8 }}
                />
              </div>
            }
            type="error"
            showIcon
          />
        )}

        {loginAttempts > 0 && loginAttempts < 5 && (
          <AttemptWarning
            message={`تحذير: ${loginAttempts} من 5 محاولات فاشلة`}
            description="سيتم حّر تسجيل الدخول بعد 5 محاولات فاشلة"
            type="warning"
            showIcon
          />
        )}

        <EnhancedForm
          form={form}
          name="login"
          onFinish={handleSubmit}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: 'يرجى اختيار اسم المستخدم' }
            ]}
            extra={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '4px' }}>
                <Button
                  type="link"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={loadActiveUsers}
                  loading={loadingUsers}
                  style={{ padding: 0, height: 'auto', fontSize: '12px' }}
                >
                  تحديث القائمة
                </Button>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {activeUsers.length > 0 ? `${activeUsers.length} مستخدم متاح` : 'لا توجد مستخدمين'}
                </Text>
              </div>
            }
          >
            <Select
              placeholder="اختر اسم المستخدم"
              disabled={isBlocked || loadingUsers}
              loading={loadingUsers}
              showSearch
              filterOption={(input, option) => {
                const searchTerm = input.toLowerCase()
                const username = option?.value?.toString().toLowerCase() || ''

                // البحث في قائمة المستخدمين الأصلية للحصول على الاسم الكامل
                const user = activeUsers.find(u => u.username === option?.value)
                const fullName = user?.full_name?.toLowerCase() || ''
                const role = user?.role?.toLowerCase() || ''

                return username.includes(searchTerm) ||
                       fullName.includes(searchTerm) ||
                       role.includes(searchTerm)
              }}
              suffixIcon={<UserOutlined />}
              size="large"
              style={{ textAlign: 'right' }}
              options={activeUsers.map(user => ({
                value: user.username,
                label: (
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', direction: 'rtl' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      {user.role === 'admin' ? (
                        <CrownOutlined style={{ color: '#ff4d4f' }} />
                      ) : user.role === 'manager' ? (
                        <SettingOutlined style={{ color: '#1890ff' }} />
                      ) : user.role === 'accountant' ? (
                        <TeamOutlined style={{ color: '#52c41a' }} />
                      ) : user.role === 'warehouse' ? (
                        <ShopOutlined style={{ color: '#fa8c16' }} />
                      ) : (
                        <UserOutlined style={{ color: '#666' }} />
                      )}
                      <span style={{ fontWeight: 'bold' }}>{user.full_name}</span>
                      <span style={{ color: '#666', fontSize: '12px' }}>({user.username})</span>
                    </div>
                    <span style={{
                      fontSize: '10px',
                      color: '#999',
                      backgroundColor: user.role === 'admin' ? '#fff2f0' : user.role === 'manager' ? '#f0f8ff' : '#f6ffed',
                      padding: '2px 6px',
                      borderRadius: '4px',
                      border: `1px solid ${user.role === 'admin' ? '#ffccc7' : user.role === 'manager' ? '#bae7ff' : '#d9f7be'}`
                    }}>
                      {user.role === 'admin' ? 'مدير' : user.role === 'manager' ? 'مشرف' : user.role === 'accountant' ? 'محاسب' : user.role === 'warehouse' ? 'مخزن' : 'مستخدم'}
                    </span>
                  </div>
                )
              }))}
              notFoundContent={
                loadingUsers ? (
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Spin size="small" />
                    <div style={{ marginTop: '8px' }}>جاري تحميل المستخدمين...</div>
                  </div>
                ) : (
                  <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                    <UserOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                    <div>لا توجد مستخدمين نشطين</div>
                    <Button
                      type="link"
                      size="small"
                      onClick={loadActiveUsers}
                      style={{ marginTop: '8px' }}
                    >
                      إعادة المحاولة
                    </Button>
                  </div>
                )
              }
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: 'يرجى إدخال كلمة المرور' },
              { min: 6, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' }
            ]}
            extra={
              <Text type="secondary" style={{ fontSize: '12px' }}>
                كلمة المرور الافتراضية: admin123
              </Text>
            }
          >
            <EnhancedPasswordInput
              prefix={<LockOutlined />}
              placeholder="كلمة المرور (افتراضي: admin123)"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              disabled={isBlocked}
              $size="lg"
            />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'space-between' }}>
              <Form.Item name="rememberMe" valuePropName="checked" noStyle>
                <Checkbox disabled={isBlocked}>تذكرني</Checkbox>
              </Form.Item>
              <Link onClick={() => setShowForgotPassword(true)}>
                نسيت كلمة المرور؟
              </Link>
            </Space>
          </Form.Item>

          <Form.Item>
            <EnhancedButton
              $variant="primary"
              htmlType="submit"
              loading={loading}
              disabled={isBlocked}
              block
            >
              {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
            </EnhancedButton>
          </Form.Item>
        </EnhancedForm>

        <SecurityInfo>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <SecurityScanOutlined style={{ color: '#0078D4', marginLeft: 8 }} />
              <Text strong>معلومات الأمان</Text>
            </div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              • يتم حّر تسجيل الدخول بعد 5 محاولات فاشلة
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              • آخر نسخة احتياطية: {systemInfo.last_backup ? 
                dayjs(systemInfo.last_backup).format('DD/MM/YYYY HH:mm') : 
                'غير متوفر'
              }
            </Text>
            {recentAttempts.length > 0 && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                • آخر دخول ناجح: {dayjs(recentAttempts.find(a => a.success)?.timestamp).format('DD/MM/YYYY HH:mm')}
              </Text>
            )}
          </Space>
        </SecurityInfo>
      </GlassCard>

      {/* نافذة نسيان كلمة المرور */}
      <Modal
        title={
          <Space>
            <KeyOutlined />
            إعادة تعيين كلمة المرور
          </Space>
        }
        open={showForgotPassword}
        onCancel={() => setShowForgotPassword(false)}
        footer={null}
        width={400}
      >
        <Form
          form={forgotPasswordForm}
          onFinish={handleForgotPassword}
          layout="vertical"
        >
          <Alert
            message="إعادة تعيين كلمة المرور"
            description="أدخل اسم المستخدم أو البريد الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Form.Item
            name="identifier"
            label="اسم المستخدم أو البريد الإلكتروني"
            rules={[{ required: true, message: 'يرجى إدخال اسم المستخدم أو البريد الإلكتروني' }]}
          >
            <Input placeholder="اسم المستخدم أو البريد الإلكتروني" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                إرسال رابط إعادة التعيين
              </Button>
              <Button onClick={() => setShowForgotPassword(false)}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </AnimatedBackground>
  )
}

export default EnhancedLoginForm
