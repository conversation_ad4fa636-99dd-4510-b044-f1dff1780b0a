/**
 * تقرير إقفال السنة المالية الموحد
 * تقرير شامل لإقفال السنة المالية باستخدام النظام الموحد
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

interface FiscalClosingReportProps {
  periodId?: number
  reportSubType?: 'closing_summary' | 'closing_entries' | 'carried_forward_balances' | 'audit_log' | 'period_comparison' | 'closing_checklist'
  title?: string
  description?: string
}

const FiscalClosingReport: React.FC<FiscalClosingReportProps> = ({
  periodId,
  reportSubType = 'closing_summary',
  title,
  description
}) => {
  
  // تحديد العنوان والوصف حسب نوع التقرير
  const getReportInfo = () => {
    switch (reportSubType) {
      case 'closing_summary':
        return {
          title: 'ملخص إقفال السنة المالية',
          description: 'تقرير شامل لملخص عملية إقفال السنة المالية مع الأرقام الرئيسية'
        }
      case 'closing_entries':
        return {
          title: 'قيود إقفال السنة المالية',
          description: 'تقرير مفصل لجميع قيود الإقفال المحاسبية'
        }
      case 'carried_forward_balances':
        return {
          title: 'الأرصدة المرحلة للسنة القادمة',
          description: 'تقرير الأرصدة المرحلة من السنة المالية المقفلة إلى السنة الجديدة'
        }
      case 'audit_log':
        return {
          title: 'سجل تدقيق إقفال السنة المالية',
          description: 'سجل مفصل لجميع عمليات الإقفال والتعديلات'
        }
      case 'period_comparison':
        return {
          title: 'مقارنة الفترات المالية',
          description: 'مقارنة شاملة بين الفترات المالية المختلفة'
        }
      case 'closing_checklist':
        return {
          title: 'قائمة مراجعة إقفال السنة المالية',
          description: 'قائمة مراجعة شاملة لضمان اكتمال عملية الإقفال'
        }
      default:
        return {
          title: 'تقرير إقفال السنة المالية',
          description: 'تقرير عام لإقفال السنة المالية'
        }
    }
  }

  const reportInfo = getReportInfo()

  // دالة مخصصة لتوليد التقرير
  const generateReport = async (filters: any) => {
    try {
      // استدعاء API مخصص لتقارير إقفال السنة المالية
      const response = await window.electronAPI.getFiscalClosingReport({
        periodId: periodId || filters.periodId,
        reportType: reportSubType,
        dateRange: filters.dateRange,
        includeDetails: filters.includeDetails || true,
        ...filters
      })

      if (!response || !response.success) {
        throw new Error(response?.message || 'فشل في جلب بيانات التقرير')
      }

      return {
        title: reportInfo.title,
        data: response.data.data || [],
        summary: response.data.summary || {},
        metadata: {
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام إقفال السنة المالية',
          reportType: reportSubType as ReportType,
          periodId: periodId,
          totalRecords: response.data.data?.length || 0,
          dateRange: filters.dateRange || 'الفترة المحددة'
        },
        columns: response.data.columns || []
      }
    } catch (error) {
      console.error('خطأ في توليد تقرير إقفال السنة المالية:', error)
      throw error
    }
  }

  return (
    <UniversalReport
      reportType={reportSubType as ReportType}
      title={title || reportInfo.title}
      description={description || reportInfo.description}
      onGenerateReport={generateReport}
      showDateRange={false} // تقارير الإقفال مرتبطة بفترة محددة
      showWarehouseFilter={false}
      showCategoryFilter={false}
      showItemFilter={false}
      showCustomerFilter={false}
      showSupplierFilter={false}
      showDepartmentFilter={false}
      showStatusFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName={`fiscal_closing_${reportSubType}_report`}
      defaultFilters={{
        periodId: periodId,
        sortBy: 'created_at',
        sortOrder: 'desc',
        includeDetails: true
      }}
      customFilters={undefined}
    />
  )
}

export default FiscalClosingReport
