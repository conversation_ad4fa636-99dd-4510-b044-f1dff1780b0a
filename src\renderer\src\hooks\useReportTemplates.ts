/**
 * Hook لإدارة قوالب التقارير
 * يربط التقارير بالقوالب المحفوظة في إعدادات الطباعة
 */

import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { SafeLogger as Logger } from '../utils/logger';
// import { useUnifiedSettings } from './useUnifiedSettings'; // متاح للاستخدام المستقبلي

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: 'invoice' | 'receipt' | 'report' | 'certificate' | 'custom';
  category?: string; // فئة التقرير (financial, inventory, sales, etc.)
  isDefault: boolean;
  isActive: boolean;
  settings: {
    pageSize?: string;
    orientation?: string;
    fontSize?: number;
    fontFamily?: string;
    showHeader?: boolean;
    showFooter?: boolean;
    showLogo?: boolean;
    marginTop?: number;
    marginBottom?: number;
    marginLeft?: number;
    marginRight?: number;
    primaryColor?: string;
    secondaryColor?: string;
    // إعدادات إضافية للتقارير
    headerSize?: number;
    lineSpacing?: number;
    tableWidth?: number;
    sectionSpacing?: number;
  };
  // أنواع التقارير المدعومة
  supportedReportTypes?: string[];
  createdAt: string;
  updatedAt: string;
}

interface UseReportTemplatesReturn {
  templates: ReportTemplate[];
  reportTemplates: ReportTemplate[];
  selectedTemplate: ReportTemplate | null;
  loading: boolean;
  error: string | null;
  selectTemplate: (templateId: string) => void;
  getTemplateSettings: (templateId?: string) => any;
  refreshTemplates: () => Promise<void>;
  // دوال جديدة للقوالب الموحدة
  getTemplateForReport: (reportType: string) => ReportTemplate | null;
  getTemplatesByCategory: (category: string) => ReportTemplate[];
  getGroupedTemplates: () => Record<string, ReportTemplate[]>;
  selectTemplateForReport: (reportType: string) => void;
  getOptimalTemplate: (reportType: string) => Promise<ReportTemplate | null>;
  autoSelectTemplate: (reportType: string) => Promise<ReportTemplate | null>;
  getCategoryForReportType: (reportType: string) => string;
}

export const useReportTemplates = (): UseReportTemplatesReturn => {
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // تحميل القوالب من قاعدة البيانات
  const loadTemplates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // محاولة تحميل القوالب من قاعدة البيانات
      const result = await window.electronAPI?.invoke('get-print-templates');
      
      if (result?.success && result.data) {
        const loadedTemplates = result.data.map((template: any) => ({
          id: template.id,
          name: template.name || template.template_name,
          description: template.description || '',
          type: template.type || template.template_type || 'report',
          category: template.category,
          isDefault: Boolean(template.is_default),
          isActive: Boolean(template.is_active),
          settings: template.settings || JSON.parse(template.template_data || '{}'),
          supportedReportTypes: template.supportedReportTypes || [],
          createdAt: template.created_at || template.createdAt,
          updatedAt: template.updated_at || template.updatedAt
        }));

        setTemplates(loadedTemplates);
        
        // تحديد القالب الافتراضي للتقارير
        const defaultReportTemplate = loadedTemplates.find(
          (t: ReportTemplate) => t.type === 'report' && t.isDefault
        );
        
        if (defaultReportTemplate) {
          setSelectedTemplate(defaultReportTemplate);
        }

        Logger.info('useReportTemplates', 'تم تحميل القوالب:', loadedTemplates);
      } else {
        // استخدام النظام الموحد إذا لم تكن متوفرة في قاعدة البيانات
        Logger.warn('useReportTemplates', 'لم يتم العثور على قوالب في قاعدة البيانات، سيتم استخدام النظام الموحد');
        setTemplates([]);
        setSelectedTemplate(null);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'خطأ في تحميل القوالب';
      setError(errorMessage);
      Logger.error('useReportTemplates', 'خطأ في تحميل القوالب:', err);
      message.error('فشل في تحميل قوالب التقارير');
    } finally {
      setLoading(false);
    }
  }, []);

  // تحديث القالب المحدد
  const selectTemplate = useCallback((templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
      Logger.info('useReportTemplates', 'تم تحديد القالب:', template);
    }
  }, [templates]);

  // الحصول على إعدادات القالب
  const getTemplateSettings = useCallback((templateId?: string) => {
    const template = templateId 
      ? templates.find(t => t.id === templateId)
      : selectedTemplate;

    if (template) {
      return {
        ...template.settings,
        templateName: template.name,
        templateType: template.type
      };
    }

    // إعدادات افتراضية إذا لم يتم العثور على القالب
    return {
      pageSize: 'A4',
      orientation: 'landscape',
      fontSize: 10,
      fontFamily: 'Arial',
      showHeader: true,
      showFooter: true,
      showLogo: true,
      marginTop: 15,
      marginBottom: 15,
      marginLeft: 10,
      marginRight: 10,
      primaryColor: '#1890ff',
      secondaryColor: '#f0f2f5'
    };
  }, [templates, selectedTemplate]);

  // الحصول على القالب المناسب لنوع تقرير معين
  const getTemplateForReport = useCallback((reportType: string): ReportTemplate | null => {
    // البحث في القوالب المحملة من قاعدة البيانات أولاً
    const dbTemplate = templates.find(template =>
      template.supportedReportTypes?.includes(reportType) && template.isActive
    );

    if (dbTemplate) {
      return dbTemplate;
    }

    // إذا لم يتم العثور على قالب مناسب، استخدم القالب الافتراضي للتقارير
    const defaultTemplate = templates.find(template =>
      template.type === 'report' && template.isDefault
    );

    if (defaultTemplate) {
      return defaultTemplate;
    }

    return null;
  }, [templates]);

  // اختيار القالب المناسب لنوع تقرير معين تلقائياً
  const selectTemplateForReport = useCallback((reportType: string) => {
    const template = getTemplateForReport(reportType);
    if (template) {
      setSelectedTemplate(template);
      Logger.info('useReportTemplates', `تم اختيار القالب تلقائياً للتقرير ${reportType}:`, template);
    }
  }, [getTemplateForReport]);

  // الحصول على القوالب حسب الفئة
  const getTemplatesByCategoryFunc = useCallback((category: string): ReportTemplate[] => {
    return templates.filter(t => t.category === category && t.isActive);
  }, [templates]);

  // الحصول على القوالب مجمعة حسب الفئة
  const getGroupedTemplates = useCallback((): Record<string, ReportTemplate[]> => {
    const grouped: Record<string, ReportTemplate[]> = {};

    templates.forEach(template => {
      if (template.type === 'report' && template.isActive) {
        const category = template.category || 'general';
        if (!grouped[category]) {
          grouped[category] = [];
        }
        grouped[category].push(template);
      }
    });

    return grouped;
  }, [templates]);

  // تحديث القوالب
  const refreshTemplates = useCallback(async () => {
    await loadTemplates();
  }, [loadTemplates]);

  // تحميل القوالب عند بدء التشغيل
  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  // فلترة قوالب التقارير فقط
  const reportTemplates = templates.filter(t => t.type === 'report');

  // دالة للحصول على القالب الأمثل تلقائياً
  const getOptimalTemplate = useCallback(async (reportType: string): Promise<ReportTemplate | null> => {
    try {
      const { MasterPrintService } = await import('../services/MasterPrintService');
      const service = MasterPrintService.getInstance();
      const optimalTemplate = await service.getOptimalTemplateForReport(reportType);

      if (optimalTemplate) {
        return {
          id: optimalTemplate.id || `auto-${reportType}`,
          name: optimalTemplate.name || `قالب ${reportType}`,
          category: optimalTemplate.category || 'general',
          supportedReportTypes: optimalTemplate.supportedReportTypes || [reportType],
          ...optimalTemplate
        } as ReportTemplate;
      }

      return null;
    } catch (error) {
      console.warn('خطأ في الحصول على القالب الأمثل:', error);
      return null;
    }
  }, []);

  // دالة لاختيار القالب المناسب تلقائياً
  const autoSelectTemplate = useCallback(async (reportType: string): Promise<ReportTemplate | null> => {
    // أولاً: البحث في القوالب المحملة
    const existingTemplate = getTemplateForReport(reportType);
    if (existingTemplate) {
      return existingTemplate;
    }

    // ثانياً: الحصول على القالب الأمثل من الخدمة
    const optimalTemplate = await getOptimalTemplate(reportType);
    if (optimalTemplate) {
      return optimalTemplate;
    }

    // ثالثاً: استخدام أول قالب متاح من نفس الفئة
    const category = getCategoryForReportType(reportType);
    const categoryTemplates = getTemplatesByCategoryFunc(category);
    if (categoryTemplates.length > 0) {
      return categoryTemplates[0];
    }

    // أخيراً: استخدام أول قالب متاح
    if (templates.length > 0) {
      return templates[0];
    }

    return null;
  }, [templates, getTemplateForReport, getOptimalTemplate, getTemplatesByCategoryFunc]);

  // دالة مساعدة لتحديد فئة التقرير
  const getCategoryForReportType = useCallback((reportType: string): string => {
    const categoryMap: Record<string, string> = {
      // التقارير المالية
      'profit_loss': 'financial', 'cash_flow': 'financial', 'balance_sheet': 'financial',
      'income_statement': 'financial', 'bank_reconciliation': 'financial', 'customer_aging': 'financial',
      'profitability': 'financial', 'customer_analysis': 'financial', 'financial_summary': 'financial',

      // تقارير المخزون
      'inventory_detailed': 'inventory', 'inventory_movements': 'inventory', 'inventory_audit': 'inventory',
      'material_consumption': 'inventory', 'low_stock': 'inventory', 'advanced_inventory': 'inventory',
      'abc_analysis': 'inventory', 'abc-analysis': 'inventory', 'item_warehouse_distribution': 'inventory',
      'item-warehouse-distribution': 'inventory',

      // تقارير المبيعات
      'sales_by_customer': 'sales', 'sales_by_product': 'sales', 'sales_by_region': 'sales',
      'monthly_sales': 'sales', 'sales_returns': 'sales', 'top_profitable_customers': 'sales',

      // تقارير الإنتاج
      'production_orders': 'production', 'production_efficiency': 'production', 'production_costs': 'production',
      'production_schedule': 'production', 'production_quality': 'production', 'production_workers_performance': 'production',
      'production_materials_consumption': 'production', 'production_profitability': 'production',

      // تقارير الموظفين
      'employee_attendance': 'employees', 'employee-attendance': 'employees', 'employee_payroll': 'employees',
      'employee_leaves': 'employees', 'employee_performance': 'employees', 'employee_overtime': 'employees',
      'employee_analysis': 'employees', 'employee-analysis': 'employees', 'salary_comparison': 'employees',
      'efficiency_evaluation': 'employees',

      // تقارير الدهان
      'monthly_paint': 'paint', 'paint_by_customer': 'paint', 'paint_by_type': 'paint',
      'paint_performance': 'paint', 'paint_profitability': 'paint', 'paint_quality': 'paint',

      // تقارير المشتريات
      'purchases_by_supplier': 'purchases', 'purchases_by_item': 'purchases', 'purchase_analysis': 'purchases',
      'supplier_analysis': 'purchases', 'supplier_payables': 'purchases', 'supplier_price_comparison': 'purchases',
      'supplier_quality': 'purchases', 'cost_analysis': 'purchases'
    };

    return categoryMap[reportType] || 'general';
  }, []);

  return {
    templates,
    reportTemplates,
    selectedTemplate,
    loading,
    error,
    selectTemplate,
    getTemplateSettings,
    refreshTemplates,
    // الدوال الجديدة
    getTemplateForReport,
    getTemplatesByCategory: getTemplatesByCategoryFunc,
    getGroupedTemplates,
    selectTemplateForReport,
    getOptimalTemplate,
    autoSelectTemplate,
    getCategoryForReportType
  };
};
