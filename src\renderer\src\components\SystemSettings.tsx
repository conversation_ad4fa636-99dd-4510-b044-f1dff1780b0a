import React, { useState, useEffect } from 'react'
import {
  Card, Tabs, Form, Input, Button, Space, Divider,
  Row, Col, Select, Switch, InputNumber, Alert,
  Spin, App
} from 'antd'
import { SafeLogger as Logger } from '../utils/logger'
import {
  SettingOutlined, SecurityScanOutlined,
  SaveOutlined, ReloadOutlined, BankOutlined,
  GlobalOutlined, LockOutlined, DatabaseOutlined, BgColorsOutlined,
  BellOutlined, SkinOutlined, ThunderboltOutlined, SoundOutlined,
  SyncOutlined, ScanOutlined, FileExcelOutlined, PrinterOutlined,
  SafetyOutlined, KeyOutlined, CalendarOutlined, ClockCircleOutlined,
  ExperimentOutlined, TableOutlined, RocketOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
// import defaultLogo from '../assets/default-logo.svg?url'


import { reloadSettingsFromDatabase } from '../utils/settings'
import LogoManager from './common/LogoManager'
import SimpleSyncSettings from './Settings/SimpleSyncSettings'
// import { PrintSettingsManager } from './common' // تم حذف نظام الطباعة
import * as XLSX from 'xlsx'


const { TextArea } = Input

const StyledCard = styled(Card)`
  .ant-card-head {
    background: linear-gradient(135deg, #0078D4 0%, #106EBE 100%);
    border-bottom: none;
    
    .ant-card-head-title {
      color: white;
      font-weight: bold;
    }
  }
  
  .ant-tabs-tab {
    font-weight: 500;
  }
  
  .ant-tabs-tab-active {
    color: #0078D4 !important;
  }
  
  .ant-tabs-ink-bar {
    background: #0078D4;
  }
`



const SettingsSection = styled.div`
  margin-bottom: 32px;
  
  .section-title {
    color: #0078D4;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
  }
`

interface SystemSettingsProps {
  currentUser?: any
}

interface Settings {
  [key: string]: string
}

const SystemSettings: React.FC<SystemSettingsProps> = ({ currentUser: _currentUser }) => {
  const { message } = App.useApp()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [settings, setSettings] = useState<Settings>({})
  const [companyForm] = Form.useForm()
  const [generalForm] = Form.useForm()
  const [advancedForm] = Form.useForm()
  const [themeForm] = Form.useForm()
  const [notificationForm] = Form.useForm()
  const [fingerprintForm] = Form.useForm()

  // معلومات التفعيل
  const [licenseInfo, setLicenseInfo] = useState<any>({ isActivated: false })
  const [deactivationCode, setDeactivationCode] = useState('')
  const [deactivationLoading, setDeactivationLoading] = useState(false)

  // إعدادات الطباعة - تم حذف نظام الطباعة القديم


  // وّائف التحقق من صحة البيانات
  const validateCompanyData = (values: any): string | null => {
    // التحقق من اسم الشركة
    if (!values.company_name || values.company_name.trim().length === 0) {
      return 'اسم الشركة مطلوب'
    }
    if (values.company_name.length > 100) {
      return 'اسم الشركة يجب أن يكون أقل من 100 حرف'
    }

    // التحقق من البريد الإلكتروني
    if (values.company_email && values.company_email.length > 0) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(values.company_email)) {
        return 'البريد الإلكتروني غير صحيح'
      }
    }

    // التحقق من رقم الهاتف
    if (values.company_phone && values.company_phone.length > 0) {
      const phoneRegex = /^[0-9+\-\s()]+$/
      if (!phoneRegex.test(values.company_phone)) {
        return 'رقم الهاتف يجب أن يحتوي على أرقام فقط'
      }
      if (values.company_phone.length > 20) {
        return 'رقم الهاتف طويل جداً'
      }
    }

    return null
  }

  const validateGeneralSettings = (values: any): string | null => {
    // التحقق من العملة
    if (!values.currency || values.currency.trim().length === 0) {
      return 'العملة مطلوبة'
    }

    // التحقق من معدل الضريبة
    if (values.tax_rate !== undefined && values.tax_rate !== null) {
      if (values.tax_rate < 0 || values.tax_rate > 100) {
        return 'معدل الضريبة يجب أن يكون بين 0 و 100'
      }
    }

    // التحقق من مهلة الجلسة
    if (values.session_timeout) {
      const timeout = parseInt(values.session_timeout)
      if (isNaN(timeout) || timeout < 5 || timeout > 1440) {
        return 'مهلة الجلسة يجب أن تكون بين 5 و 1440 دقيقة'
      }
    }

    // التحقق من الحد الأدنى لطول كلمة المرور
    if (values.password_min_length) {
      const minLength = parseInt(values.password_min_length)
      if (isNaN(minLength) || minLength < 4 || minLength > 20) {
        return 'الحد الأدنى لطول كلمة المرور يجب أن يكون بين 4 و 20 حرف'
      }
    }

    return null
  }

  const validateFingerprintSettings = (values: any): string | null => {
    // التحقق من مهلة الاتصال
    if (values.fingerprint_connection_timeout) {
      const timeout = parseInt(values.fingerprint_connection_timeout)
      if (isNaN(timeout) || timeout < 5 || timeout > 60) {
        return 'مهلة الاتصال يجب أن تكون بين 5 و 60 ثانية'
      }
    }

    // التحقق من مهلة انتّار البصمة
    if (values.fingerprint_timeout) {
      const timeout = parseInt(values.fingerprint_timeout)
      if (isNaN(timeout) || timeout < 10 || timeout > 120) {
        return 'مهلة انتّار البصمة يجب أن تكون بين 10 و 120 ثانية'
      }
    }

    // التحقق من عدد محاولات إعادة المحاولة
    if (values.fingerprint_retry_attempts) {
      const attempts = parseInt(values.fingerprint_retry_attempts)
      if (isNaN(attempts) || attempts < 1 || attempts > 10) {
        return 'عدد محاولات إعادة المحاولة يجب أن يكون بين 1 و 10'
      }
    }

    // التحقق من فترة المزامنة
    if (values.fingerprint_sync_interval) {
      const interval = parseInt(values.fingerprint_sync_interval)
      if (isNaN(interval) || interval < 5 || interval > 1440) {
        return 'فترة المزامنة يجب أن تكون بين 5 و 1440 دقيقة'
      }
    }

    return null
  }

  // وّائف التصدير والطباعة
  const handleExportSettings = () => {
    try {
      // فحص وجود الإعدادات قبل التصدير
      if (!settings || Object.keys(settings).length === 0) {
        message.warning('لا توجد إعدادات للتصدير')
        return
      }

      // تحضير البيانات للتصدير
      const exportData = Object.entries(settings).map(([key, value]) => ({
        'مفتاح الإعداد': key,
        'القيمة': value,
        'النوع': typeof value === 'boolean' ? 'منطقي' :
                typeof value === 'number' ? 'رقمي' : 'نصي',
        'الوصف': getSettingDescription(key)
      }))

      // إضافة ورقة إحصائيات
      const stats = [
        { 'الإحصائية': 'إجمالي الإعدادات', 'القيمة': Object.keys(settings).length },
        { 'الإحصائية': 'إعدادات الشركة', 'القيمة': Object.keys(settings).filter(k => k.startsWith('company_')).length },
        { 'الإحصائية': 'الإعدادات العامة', 'القيمة': Object.keys(settings).filter(k => !k.startsWith('company_') && !k.startsWith('fingerprint_')).length },
        { 'الإحصائية': 'إعدادات أجهزة البصمة', 'القيمة': Object.keys(settings).filter(k => k.startsWith('fingerprint_')).length },
        { 'الإحصائية': 'تاريخ التصدير', 'القيمة': new Date().toLocaleString('ar') }
      ]

      // إنشاء workbook
      const wb = XLSX.utils.book_new()

      // إضافة ورقة الإعدادات
      const ws1 = XLSX.utils.json_to_sheet(exportData)
      XLSX.utils.book_append_sheet(wb, ws1, 'إعدادات النظام')

      // إضافة ورقة الإحصائيات
      const ws2 = XLSX.utils.json_to_sheet(stats)
      XLSX.utils.book_append_sheet(wb, ws2, 'إحصائيات')

      // تصدير الملف
      const fileName = 'إعدادات_النظام_' + new Date().toISOString().split('T')[0] + '.xlsx'
      XLSX.writeFile(wb, fileName)

      message.success(`تم تصدير ${exportData.length} إعداد بنجاح`)
    } catch (error) {
      Logger.error('SystemSettings', 'خطأ في تصدير الإعدادات:', error)
      message.error('حدث خطأ أثناء تصدير الإعدادات')
    }
  }

  const getSettingDescription = (key: string): string => {
    const descriptions: { [key: string]: string } = {
      'company_name': 'اسم الشركة',
      'company_address': 'عنوان الشركة',
      'company_phone': 'هاتف الشركة',
      'company_email': 'بريد الشركة الإلكتروني',
      'currency': 'العملة الافتراضية',
      'currency_symbol': 'رمز العملة',
      'tax_rate': 'معدل الضريبة',
      'session_timeout': 'مهلة انتهاء الجلسة',
      'password_min_length': 'الحد الأدنى لطول كلمة المرور',
      'fingerprint_enabled': 'تفعيل أجهزة البصمة',
      'fingerprint_timeout': 'مهلة انتّار البصمة',
      'fingerprint_auto_sync': 'المزامنة التلقائية للبصمة'
    }
    return descriptions[key] || 'إعداد النظام'
  }

  const handlePrintSettings = () => {
    try {
      const printContent = `
        <html>
          <head>
            <title>إعدادات النظام</title>
            <style>
              body { font-family: Arial, sans-serif; direction: rtl; }
              .header { text-align: center; margin-bottom: 30px; }
              .company-info { background: #f8f9fa; padding: 20px; margin-bottom: 20px; border-radius: 8px; }
              .settings-section { margin-bottom: 30px; }
              .section-title { font-size: 18px; font-weight: bold; color: #0078D4; margin-bottom: 15px; border-bottom: 2px solid #0078D4; padding-bottom: 5px; }
              .setting-item { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee; }
              .setting-key { font-weight: bold; }
              .setting-value { color: #666; }
              .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
              @media print { body { margin: 0; } }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>تقرير إعدادات النظام</h1>
              <p>تاريخ الطباعة: ${new Date().toLocaleString('ar')}</p>
            </div>

            <div class="company-info">
              <h2>معلومات الشركة</h2>
              <div class="setting-item">
                <span class="setting-key">اسم الشركة:</span>
                <span class="setting-value">${settings.company_name || 'غير محدد'}</span>
              </div>
              <div class="setting-item">
                <span class="setting-key">العنوان:</span>
                <span class="setting-value">${settings.company_address || 'غير محدد'}</span>
              </div>
              <div class="setting-item">
                <span class="setting-key">الهاتف:</span>
                <span class="setting-value">${settings.company_phone || 'غير محدد'}</span>
              </div>
              <div class="setting-item">
                <span class="setting-key">البريد الإلكتروني:</span>
                <span class="setting-value">${settings.company_email || 'غير محدد'}</span>
              </div>
            </div>

            <div class="settings-section">
              <div class="section-title">الإعدادات العامة</div>
              ${Object.entries(settings)
                .filter(([key]) => !key.startsWith('company_') && !key.startsWith('fingerprint_'))
                .map(([key, value]) => `
                  <div class="setting-item">
                    <span class="setting-key">${getSettingDescription(key)}:</span>
                    <span class="setting-value">${value}</span>
                  </div>
                `).join('')}
            </div>

            <div class="settings-section">
              <div class="section-title">إعدادات أجهزة البصمة</div>
              ${Object.entries(settings)
                .filter(([key]) => key.startsWith('fingerprint_'))
                .map(([key, value]) => `
                  <div class="setting-item">
                    <span class="setting-key">${getSettingDescription(key)}:</span>
                    <span class="setting-value">${value === 'true' ? 'مفعل' : value === 'false' ? 'معطل' : value}</span>
                  </div>
                `).join('')}
            </div>

            <div class="footer">
              <p>تم إنشاء هذا التقرير بواسطة نّام إدارة الحسابات</p>
              <p>إجمالي الإعدادات: ${Object.keys(settings).length}</p>
            </div>
          </body>
        </html>
      `

      const printWindow = window.open('', '_blank')
      if (printWindow) {
        const doc = (printWindow as any).document

        // كتابة محتوى HTML كامل
        doc.open()
        doc.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <title>طباعة التقرير</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 20px;
                direction: rtl;
                text-align: right;
              }
              @media print {
                body { margin: 0; }
                .no-print { display: none; }
              }
            </style>
          </head>
          <body>
            ${printContent}
          </body>
          </html>
        `)
        doc.close()

        // انتظار تحميل المحتوى قبل الطباعة
        ;(printWindow as any).onload = () => {
          setTimeout(() => {
            printWindow.focus()
            printWindow.print()
            printWindow.close()
          }, 500)
        }

        // في حالة عدم تشغيل onload
        setTimeout(() => {
          if (!printWindow.closed) {
            printWindow.focus()
            printWindow.print()
            printWindow.close()
          }
        }, 1000)
        message.success('تم فتح نافذة الطباعة')
      } else {
        message.error('فشل في فتح نافذة الطباعة')
      }
    } catch (error) {
      Logger.error('SystemSettings', 'خطأ في طباعة الإعدادات:', error)
      message.error('حدث خطأ أثناء طباعة الإعدادات')
    }
  }

  useEffect(() => {
    // تأخير تحميل الإعدادات للتأكد من أن Forms جاهزة
    const timer = setTimeout(() => {
      // التأكد من أن جميع النماذج جاهزة قبل تحميل الإعدادات
      if (companyForm && generalForm && advancedForm && themeForm && notificationForm && fingerprintForm) {
        loadSettings()
        loadLicenseInfo()
      } else {
        // إعادة المحاولة إذا لم تكن النماذج جاهزة
        setTimeout(loadSettings, 200)
      }
    }, 300) // تقليل التأخير الأولي

    return () => clearTimeout(timer)
  }, [companyForm, generalForm, advancedForm, themeForm, notificationForm]) // إضافة النماذج كتبعيات

  // تحميل معلومات التفعيل
  const loadLicenseInfo = async () => {
    try {
      const info = await window.electronAPI?.getLicenseInfo()
      if (info) {
        setLicenseInfo(info)
      }
    } catch (error) {
      console.error('خطأ في تحميل معلومات التفعيل:', error)
    }
  }

  // إلغاء التفعيل
  const handleDeactivation = async () => {
    if (!deactivationCode.trim()) {
      message.error('يرجى إدخال رقم إلغاء التفعيل')
      return
    }

    setDeactivationLoading(true)
    try {
      const result = await window.electronAPI?.activateLicense(deactivationCode.trim())

      if (result?.success) {
        message.success('تم إلغاء تفعيل البرنامج بنجاح')
        setDeactivationCode('')
        await loadLicenseInfo()

        // إعادة تحميل التطبيق بعد 2 ثانية
        setTimeout(() => {
          window.location.reload()
        }, 2000)
      } else {
        message.error('رقم إلغاء التفعيل غير صحيح')
      }
    } catch (error) {
      console.error('خطأ في إلغاء التفعيل:', error)
      message.error('حدث خطأ أثناء إلغاء التفعيل')
    } finally {
      setDeactivationLoading(false)
    }
  }

  const loadSettings = async () => {
    try {
      setLoading(true)

      // التأكد من أن جميع Forms جاهزة وأن لديها الطرق المطلوبة
      const formsReady = companyForm?.setFieldsValue &&
                        generalForm?.setFieldsValue &&
                        advancedForm?.setFieldsValue &&
                        themeForm?.setFieldsValue &&
                        notificationForm?.setFieldsValue

      if (!formsReady) {
        Logger.warn('SystemSettings', 'Forms not ready yet, retrying...')
        // إعادة المحاولة بعد وقت أقصر
        setTimeout(() => loadSettings(), 100)
        return
      }
      if (window.electronAPI) {
        const response = await window.electronAPI.getSettings()
        const settingsMap: Settings = {}

        // التحقق من نجاح الاستجابة والبيانات
        if (response && response.success && response.data && Array.isArray(response.data)) {
          response.data.forEach((setting: any) => {
            settingsMap[setting.key] = setting.value
          })
          Logger.info('SystemSettings', 'تم تحميل الإعدادات بنجاح:', settingsMap)
        } else {
          Logger.warn('SystemSettings', 'فشل في تحميل الإعدادات أو البيانات غير صحيحة:', response)
          // إعدادات افتراضية
          settingsMap.company_name = 'ZET.IA'
          settingsMap.currency = 'ILS'
          settingsMap.currency_code = 'ILS'
          settingsMap.currency_symbol = '₪'
          settingsMap.timezone = 'Asia/Jerusalem'
          settingsMap.language = 'ar'
          settingsMap.date_format = 'DD/MM/YYYY'
          settingsMap.session_timeout = '480'
          settingsMap.password_min_length = '6'
          settingsMap.backup_enabled = 'true'
          settingsMap.backup_frequency = 'daily'
          settingsMap.max_login_attempts = '5'
          settingsMap.auto_logout_enabled = 'true'
        }
        setSettings(settingsMap)
        
        // تحديث النماذج بالبيانات المحملة - بتأخير للتأكد من الربط
        setTimeout(() => {
          try {
            // التأكد من أن النموذج جاهز ومتصل
            if (companyForm?.setFieldsValue && companyForm.getFieldsValue) {
              companyForm.setFieldsValue({
                company_name: settingsMap.company_name,
                company_address: settingsMap.company_address,
                company_phone: settingsMap.company_phone,
                company_email: settingsMap.company_email,
                company_website: settingsMap.company_website,
                company_tax_number: settingsMap.company_tax_number,
                company_registration_number: settingsMap.company_registration_number
              })
            }
          } catch (error) {
            Logger.warn('SystemSettings', 'خطأ في تحديث نموذج معلومات الشركة:', error)
          }

          try {
            if (generalForm?.setFieldsValue && generalForm.getFieldsValue) {
              generalForm.setFieldsValue({
                currency: settingsMap.currency,
                currency_code: settingsMap.currency_code,
                currency_symbol: settingsMap.currency_symbol,
                timezone: settingsMap.timezone,
                language: settingsMap.language,
                date_format: settingsMap.date_format
              })
            }
          } catch (error) {
            Logger.warn('SystemSettings', 'خطأ في تحديث النموذج العام:', error)
          }

          try {
            if (advancedForm?.setFieldsValue && advancedForm.getFieldsValue) {
              advancedForm.setFieldsValue({
                session_timeout: parseInt(settingsMap.session_timeout || '480'),
                password_min_length: parseInt(settingsMap.password_min_length || '6'),
                backup_enabled: settingsMap.backup_enabled === 'true',
                backup_frequency: settingsMap.backup_frequency,
                max_login_attempts: parseInt(settingsMap.max_login_attempts || '5'),
                auto_logout_enabled: settingsMap.auto_logout_enabled === 'true'
              })
            }
          } catch (error) {
            Logger.warn('SystemSettings', 'خطأ في تحديث النموذج المتقدم:', error)
          }

          try {
            if (themeForm?.setFieldsValue && themeForm.getFieldsValue) {
              themeForm.setFieldsValue({
                theme_mode: settingsMap.theme_mode || 'light',
                primary_color: settingsMap.primary_color || '#1890ff',
                font_size: parseInt(settingsMap.font_size || '14'),
                border_radius: parseInt(settingsMap.border_radius || '6'),
                compact_mode: settingsMap.compact_mode === 'true',
                font_family: settingsMap.font_family || 'Segoe UI, Tahoma, Arial, sans-serif',
                sidebar_collapsed: settingsMap.sidebar_collapsed === 'true',
                auto_close_menus: settingsMap.auto_close_menus === 'true',
                custom_success_color: settingsMap.custom_success_color || '#52c41a',
                custom_warning_color: settingsMap.custom_warning_color || '#faad14',
                custom_error_color: settingsMap.custom_error_color || '#ff4d4f',
                custom_info_color: settingsMap.custom_info_color || '#1890ff'
              })
            }
          } catch (error) {
            Logger.warn('SystemSettings', 'خطأ في تحديث نموذج الثيم:', error)
          }

          try {
            if (notificationForm?.setFieldsValue && notificationForm.getFieldsValue) {
              notificationForm.setFieldsValue({
                notifications_enabled: settingsMap.notifications_enabled === 'true',
                notification_sound: settingsMap.notification_sound === 'true',
                voice_guide_enabled: settingsMap.voice_guide_enabled === 'true',
                notification_duration: parseInt(settingsMap.notification_duration || '4'),
                speech_rate: parseFloat(settingsMap.speech_rate || '1.2'),
                low_stock_threshold: parseInt(settingsMap.low_stock_threshold || '10'),
                invoice_due_reminder_days: parseInt(settingsMap.invoice_due_reminder_days || '3')
              })
            }
          } catch (error) {
            Logger.warn('SystemSettings', 'خطأ في تحديث نموذج الإشعارات:', error)
          }
        }, 200) // تأخير أطول للتأكد من ربط النماذج



        // تطبيق الإعدادات على الواجهة
        applySettingsToInterface(settingsMap)
      } else {
        Logger.error('SystemSettings', '❌ window.electronAPI غير متوفر - لا يمكن تحميل الإعدادات')
        message.error('فشل في الاتصال بقاعدة البيانات')
        return
      }
    } catch (error) {
      Logger.error('SystemSettings', 'خطأ في تحميل الإعدادات:', error)
      message.error('فشل في تحميل الإعدادات')
    } finally {
      setLoading(false)
    }
  }

  // تطبيق الإعدادات على الواجهة
  const applySettingsToInterface = (settingsMap: Settings) => {
    try {
      // تطبيق إعدادات الثيم
      if (window.themeManager && settingsMap.theme_mode) {
        const themeConfig = {
          mode: settingsMap.theme_mode as 'light' | 'dark',
          primaryColor: settingsMap.primary_color || '#1890ff',
          fontSize: parseInt(settingsMap.font_size || '14'),
          borderRadius: parseInt(settingsMap.border_radius || '6'),
          compactMode: settingsMap.compact_mode === 'true',
          fontFamily: settingsMap.font_family || 'Segoe UI, Tahoma, Arial, sans-serif',
          customColors: {
            success: settingsMap.custom_success_color || '#fff3cd',
            warning: settingsMap.custom_warning_color || '#faad14',
            error: settingsMap.custom_error_color || '#ff4d4f',
            info: settingsMap.custom_info_color || '#1890ff'
          }
        }
        window.themeManager.applyTheme(themeConfig)
        Logger.info('SystemSettings', 'تم تطبيق إعدادات الثيم:', themeConfig)
      }

      // تطبيق إعدادات الصوت
      if (window.comprehensiveAudioSystem) {
        const audioSettings = {
          enabled: settingsMap.sound_enabled === 'true',
          volume: 70, // مستوى الصوت الافتراضي
          speechRate: parseFloat(settingsMap.speech_rate || '1.2'),
          voiceEnabled: settingsMap.voice_guide_enabled === 'true'
        }
        window.comprehensiveAudioSystem.updateSettings(audioSettings)
        Logger.info('SystemSettings', 'تم تطبيق إعدادات الصوت:', audioSettings)
      }

      Logger.info('SystemSettings', '✅ تم تطبيق جميع الإعدادات على الواجهة')
    } catch (error) {
      Logger.error('SystemSettings', '❌ خطأ في تطبيق الإعدادات على الواجهة:', error)
    }
  }



  const handleCompanySubmit = async (values: any) => {
    // التحقق من صحة البيانات
    const validationError = validateCompanyData(values)
    if (validationError) {
      message.error(validationError)
      return
    }

    setSaving(true)
    try {
      Logger.info('SystemSettings', 'محاولة حفظ معلومات الشركة:', values)

      // تحويل القيم إلى نصوص
      const processedValues: any = {}
      Object.keys(values).forEach(key => {
        if (typeof values[key] === 'boolean') {
          processedValues[key] = values[key].toString()
        } else if (typeof values[key] === 'number') {
          processedValues[key] = values[key].toString()
        } else {
          processedValues[key] = values[key]
        }
      })

      Logger.info('SystemSettings', 'قيم معلومات الشركة المعالجة:', processedValues)

      if (window.electronAPI) {
        const result = await window.electronAPI.updateSettings(processedValues)
        if (result.success) {
          // تحديث الإعدادات المحلية
          setSettings(prev => ({ ...prev, ...processedValues }))

          message.success('تم حفظ معلومات الشركة بنجاح')

          // إعادة تحميل الإعدادات في utils/settings
          await reloadSettingsFromDatabase()

          // إعادة تحميل معلومات الشركة في PrintService
          if ((window as any).PrintService) {
            await (window as any).PrintService.loadCompanyInfo()
          }

          // تشغيل صوت النجاح
          if (window.comprehensiveAudioSystem) {
            window.comprehensiveAudioSystem.playSound('success')
          }
        } else {
          message.error(result.message || 'فشل في حفظ معلومات الشركة')

          // تشغيل صوت الخطأ
          if (window.comprehensiveAudioSystem) {
            window.comprehensiveAudioSystem.playSound('error')
          }
        }
      } else {
        // في وضع التطوير
        setSettings(prev => ({ ...prev, ...processedValues }))
        message.success('تم حفظ معلومات الشركة بنجاح (وضع التطوير)')

        // تشغيل صوت النجاح
        if (window.comprehensiveAudioSystem) {
          window.comprehensiveAudioSystem.playSound('success')
        }
      }
    } catch (error) {
      Logger.error('SystemSettings', 'خطأ في حفظ معلومات الشركة:', error)
      message.error('حدث خطأ أثناء حفظ الإعدادات')

      // تشغيل صوت الخطأ
      if (window.comprehensiveAudioSystem) {
        window.comprehensiveAudioSystem.playSound('error')
      }
    } finally {
      setSaving(false)
    }
  }

  const handleGeneralSubmit = async (values: any) => {
    // التحقق من صحة البيانات
    const validationError = validateGeneralSettings(values)
    if (validationError) {
      message.error(validationError)
      return
    }

    setSaving(true)
    try {
      // تحويل القيم إلى نصوص
      const processedValues: any = {}
      Object.keys(values).forEach(key => {
        if (typeof values[key] === 'boolean') {
          processedValues[key] = values[key].toString()
        } else if (typeof values[key] === 'number') {
          processedValues[key] = values[key].toString()
        } else {
          processedValues[key] = values[key]
        }
      })

      Logger.info('SystemSettings', 'قيم الإعدادات العامة المعالجة:', processedValues)

      if (window.electronAPI) {
        const result = await window.electronAPI.updateSettings(processedValues)
        if (result.success) {
          // تحديث الإعدادات المحلية
          setSettings(prev => ({ ...prev, ...processedValues }))

          message.success('تم حفظ الإعدادات العامة بنجاح')

          // إعادة تحميل الإعدادات في utils/settings
          await reloadSettingsFromDatabase()

          // تشغيل صوت النجاح
          if (window.comprehensiveAudioSystem) {
            window.comprehensiveAudioSystem.playSound('success')
          }
        } else {
          message.error(result.message || 'فشل في حفظ الإعدادات العامة')

          // تشغيل صوت الخطأ
          if (window.comprehensiveAudioSystem) {
            window.comprehensiveAudioSystem.playSound('error')
          }
        }
      } else {
        // في وضع التطوير
        setSettings(prev => ({ ...prev, ...processedValues }))
        message.success('تم حفظ الإعدادات العامة بنجاح (وضع التطوير)')

        // تشغيل صوت النجاح
        if (window.comprehensiveAudioSystem) {
          window.comprehensiveAudioSystem.playSound('success')
        }
      }
    } catch (error) {
      Logger.error('SystemSettings', 'خطأ في حفظ الإعدادات العامة:', error)
      message.error('حدث خطأ أثناء حفظ الإعدادات')

      // تشغيل صوت الخطأ
      if (window.comprehensiveAudioSystem) {
        window.comprehensiveAudioSystem.playSound('error')
      }
    } finally {
      setSaving(false)
    }
  }

  const handleAdvancedSubmit = async (values: any) => {
    setSaving(true)
    try {
      // تحويل القيم المنطقية إلى نصوص
      const processedValues: any = {}
      Object.keys(values).forEach(key => {
        if (typeof values[key] === 'boolean') {
          processedValues[key] = values[key].toString()
        } else if (typeof values[key] === 'number') {
          processedValues[key] = values[key].toString()
        } else {
          processedValues[key] = values[key]
        }
      })

      Logger.info('SystemSettings', 'قيم الإعدادات المتقدمة المعالجة:', processedValues)

      if (window.electronAPI) {
        const result = await window.electronAPI.updateSettings(processedValues)
        if (result.success) {
          // تحديث الإعدادات المحلية
          setSettings(prev => ({ ...prev, ...processedValues }))

          message.success('تم حفظ الإعدادات المتقدمة بنجاح')

          // تشغيل صوت النجاح
          if (window.comprehensiveAudioSystem) {
            window.comprehensiveAudioSystem.playSound('success')
          }
        } else {
          message.error(result.message || 'فشل في حفظ الإعدادات المتقدمة')

          // تشغيل صوت الخطأ
          if (window.comprehensiveAudioSystem) {
            window.comprehensiveAudioSystem.playSound('error')
          }
        }
      } else {
        // في وضع التطوير
        setSettings(prev => ({ ...prev, ...processedValues }))
        message.success('تم حفظ الإعدادات المتقدمة بنجاح (وضع التطوير)')

        // تشغيل صوت النجاح
        if (window.comprehensiveAudioSystem) {
          window.comprehensiveAudioSystem.playSound('success')
        }
      }
    } catch (error) {
      Logger.error('SystemSettings', 'خطأ في حفظ الإعدادات المتقدمة:', error)
      message.error('حدث خطأ أثناء حفظ الإعدادات')

      // تشغيل صوت الخطأ
      if (window.comprehensiveAudioSystem) {
        window.comprehensiveAudioSystem.playSound('error')
      }
    } finally {
      setSaving(false)
    }
  }

  const handleThemeSubmit = async (values: any) => {
    setSaving(true)
    try {
      // معالجة القيم قبل الحفظ
      const processedValues: any = {}

      // تحويل القيم المنطقية إلى نصوص
      Object.keys(values).forEach(key => {
        if (typeof values[key] === 'boolean') {
          processedValues[key] = values[key].toString()
        } else if (typeof values[key] === 'number') {
          processedValues[key] = values[key].toString()
        } else {
          processedValues[key] = values[key]
        }
      })

      Logger.info('SystemSettings', 'قيم الثيم المعالجة:', processedValues)

      if (window.electronAPI) {
        const result = await window.electronAPI.updateSettings(processedValues)
        if (result.success) {
          // تحديث الإعدادات المحلية
          setSettings(prev => ({ ...prev, ...processedValues }))

          message.success('تم حفظ إعدادات الثيم بنجاح')

          // تطبيق الثيم فوراً باستخدام مدير الثيم
          if (window.themeManager) {
            await window.themeManager.loadThemeFromDatabase()

            // إعادة تحميل تكوين Ant Design
            const newThemeConfig = window.themeManager.getAntdThemeConfig()
            Logger.info('SystemSettings', 'تكوين الثيم الجديد:', newThemeConfig)

            // تطبيق الثيم فوراً بدلاً من إعادة تحميل الصفحة
            window.themeManager.applyTheme(window.themeManager.getCurrentTheme())
          }

          // إعادة تحميل الصفحة فقط إذا لزم الأمر (تعليق مؤقت)
          // setTimeout(() => {
          //   window.location.reload()
          // }, 1000)

          // تشغيل صوت النجاح
          if (window.comprehensiveAudioSystem) {
            window.comprehensiveAudioSystem.playSound('success')
          }
        } else {
          message.error(result.message || 'فشل في حفظ إعدادات الثيم')

          // تشغيل صوت الخطأ
          if (window.comprehensiveAudioSystem) {
            window.comprehensiveAudioSystem.playSound('error')
          }
        }
      } else {
        // في وضع التطوير
        setSettings(prev => ({ ...prev, ...processedValues }))
        message.success('تم حفظ إعدادات الثيم بنجاح (وضع التطوير)')

        // تشغيل صوت النجاح
        if (window.comprehensiveAudioSystem) {
          window.comprehensiveAudioSystem.playSound('success')
        }
      }
    } catch (error) {
      Logger.error('SystemSettings', 'خطأ في حفظ إعدادات الثيم:', error)
      message.error('حدث خطأ في حفظ إعدادات الثيم')

      // تشغيل صوت الخطأ
      if (window.comprehensiveAudioSystem) {
        window.comprehensiveAudioSystem.playSound('error')
      }
    } finally {
      setSaving(false)
    }
  }

  const handleNotificationSubmit = async (values: any) => {
    setSaving(true)
    try {
      // معالجة القيم قبل الحفظ
      const processedValues: any = {}

      // تحويل القيم المنطقية إلى نصوص
      Object.keys(values).forEach(key => {
        if (typeof values[key] === 'boolean') {
          processedValues[key] = values[key].toString()
        } else {
          processedValues[key] = values[key]
        }
      })

      if (window.electronAPI) {
        const result = await window.electronAPI.updateSettings(processedValues)
        if (result.success) {
          // تحديث الإعدادات المحلية
          setSettings(prev => ({ ...prev, ...processedValues }))

          message.success('تم حفظ إعدادات الإشعارات بنجاح')

          // تحديث إعدادات نظام الإشعارات الذكية
          if (window.smartNotificationService) {
            window.smartNotificationService.updateSettings(processedValues)
          }

          // تطبيق إعدادات الصوت فوراً
          if (window.comprehensiveAudioSystem) {
            const audioSettings = {
              enabled: values.sound_enabled,
              speechRate: values.speech_rate || 1.2,
              voiceEnabled: values.voice_guide_enabled
            }
            window.comprehensiveAudioSystem.updateSettings(audioSettings)
          }

          // تشغيل صوت النجاح
          if (window.comprehensiveAudioSystem) {
            window.comprehensiveAudioSystem.playSound('success')
          }
        } else {
          message.error(result.message || 'فشل في حفظ إعدادات الإشعارات')

          // تشغيل صوت الخطأ
          if (window.comprehensiveAudioSystem) {
            window.comprehensiveAudioSystem.playSound('error')
          }
        }
      } else {
        message.success('تم حفظ إعدادات الإشعارات بنجاح (وضع التطوير)')

        // تشغيل صوت النجاح
        if (window.comprehensiveAudioSystem) {
          window.comprehensiveAudioSystem.playSound('success')
        }
      }
    } catch (error) {
      Logger.error('SystemSettings', 'خطأ في حفظ إعدادات الإشعارات:', error)
      message.error('حدث خطأ في حفظ إعدادات الإشعارات')

      // تشغيل صوت الخطأ
      if (window.comprehensiveAudioSystem) {
        window.comprehensiveAudioSystem.playSound('error')
      }
    } finally {
      setSaving(false)
    }
  }

  // حفظ إعدادات أجهزة البصمة
  const handleFingerprintSubmit = async (values: any) => {
    // التحقق من صحة البيانات
    const validationError = validateFingerprintSettings(values)
    if (validationError) {
      message.error(validationError)
      return
    }

    setSaving(true)
    try {
      const fingerprintSettings = {
        fingerprint_enabled: values.fingerprint_enabled ? 'true' : 'false',
        fingerprint_timeout: values.fingerprint_timeout || '30',
        fingerprint_retry_attempts: values.fingerprint_retry_attempts || '3',
        fingerprint_auto_sync: values.fingerprint_auto_sync ? 'true' : 'false',
        fingerprint_sync_interval: values.fingerprint_sync_interval || '60',
        fingerprint_backup_enabled: values.fingerprint_backup_enabled ? 'true' : 'false',
        fingerprint_log_level: values.fingerprint_log_level || 'info',
        fingerprint_connection_timeout: values.fingerprint_connection_timeout || '10'
      }

      if (window.electronAPI) {
        const response = await window.electronAPI.updateSettings(fingerprintSettings)
        if (response.success) {
          message.success('تم حفظ إعدادات أجهزة البصمة بنجاح')
          // تحديث الإعدادات المحلية
          setSettings(prev => ({ ...prev, ...fingerprintSettings }))
        } else {
          message.error(response.message || 'فشل في حفظ إعدادات أجهزة البصمة')
        }
      } else {
        // في وضع التطوير
        message.success('تم حفظ إعدادات أجهزة البصمة (وضع التطوير)')
      }
    } catch (error) {
      Logger.error('SystemSettings', 'خطأ في حفظ إعدادات أجهزة البصمة:', error)
      message.error('حدث خطأ أثناء حفظ إعدادات أجهزة البصمة')
    } finally {
      setSaving(false)
    }
  }





  if (loading) {
    return (
      <StyledCard title="إعدادات النظام">
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>جاري تحميل الإعدادات...</div>
        </div>
      </StyledCard>
    )
  }

  return (
    <StyledCard
      title={
        <Space>
          <SettingOutlined />
          إعدادات النظام
        </Space>
      }
      extra={
        <Space>
          <Button
            icon={<FileExcelOutlined />}
            onClick={handleExportSettings}
            type="default"
            size="small"
          >
            تصدير Excel
          </Button>
          <Button
            icon={<PrinterOutlined />}
            onClick={handlePrintSettings}
            type="default"
            size="small"
          >
            طباعة
          </Button>
        </Space>
      }
    >
      <Tabs
        defaultActiveKey="company"
        size="large"
        items={[
          {
            key: 'company',
            label: (
              <Space>
                <BankOutlined />
                معلومات الشركة
              </Space>
            ),
            children: (
          <Form
            form={companyForm}
            layout="vertical"
            onFinish={handleCompanySubmit}
          >
            <LogoManager
              onLogoChange={(newLogoUrl) => {
                // تحديث خدمة الطباعة بالشعار الجديد
                if ((window as any).PrintService) {
                  (window as any).PrintService.updateCompanyInfo({ logo: newLogoUrl })
                }
              }}
            />

            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="اسم الشركة"
                  name="company_name"
                  rules={[{ required: true, message: 'يرجى إدخال اسم الشركة' }]}
                >
                  <Input placeholder="اسم الشركة" />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label="الرقم الضريبي"
                  name="company_tax_number"
                >
                  <Input placeholder="الرقم الضريبي" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="العنوان"
                  name="company_address"
                >
                  <TextArea rows={3} placeholder="عنوان الشركة" />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  label="رقم التسجيل التجاري"
                  name="company_registration_number"
                >
                  <Input placeholder="رقم التسجيل التجاري" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 16]}>
              <Col xs={24} md={8}>
                <Form.Item
                  label="رقم الهاتف"
                  name="company_phone"
                >
                  <Input placeholder="+970-2-123-4567" />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item
                  label="البريد الإلكتروني"
                  name="company_email"
                  rules={[{ type: 'email', message: 'يرجى إدخال بريد إلكتروني صحيح' }]}
                >
                  <Input placeholder="<EMAIL>" />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item
                  label="الموقع الإلكتروني"
                  name="company_website"
                >
                  <Input placeholder="www.company.com" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item>
              <Space>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={saving}
                  icon={<SaveOutlined />}
                >
                  حفظ معلومات الشركة
                </Button>
                <Button 
                  icon={<ReloadOutlined />}
                  onClick={() => companyForm.resetFields()}
                >
                  إعادة تعيين
                </Button>
              </Space>
            </Form.Item>
          </Form>
            )
          },
          {
            key: 'general',
            label: (
              <Space>
                <GlobalOutlined />
                الإعدادات العامة
              </Space>
            ),
            children: (
          <Form
            form={generalForm}
            layout="vertical"
            onFinish={handleGeneralSubmit}
          >
            <SettingsSection>
              <div className="section-title">
                <GlobalOutlined />
                إعدادات العملة والتاريخ
              </div>

              <Row gutter={[16, 16]}>
                <Col xs={24} md={8}>
                  <Form.Item
                    label="العملة الافتراضية"
                    name="currency"
                  >
                    <Select placeholder="اختر العملة">
                      <Select.Option value="الشيكل الإسرائيلي">الشيكل الإسرائيلي</Select.Option>
                      <Select.Option value="الدينار الأردني">الدينار الأردني</Select.Option>
                      <Select.Option value="الدولار الأمريكي">الدولار الأمريكي</Select.Option>
                      <Select.Option value="اليورو">اليورو</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    label="رمز العملة"
                    name="currency_code"
                  >
                    <Select placeholder="رمز العملة">
                      <Select.Option value="ILS">ILS</Select.Option>
                      <Select.Option value="JOD">JOD</Select.Option>
                      <Select.Option value="USD">USD</Select.Option>
                      <Select.Option value="EUR">EUR</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item
                    label="رمز العرض"
                    name="currency_symbol"
                  >
                    <Select placeholder="رمز العرض">
                      <Select.Option value="₪">₪ (شيكل)</Select.Option>
                      <Select.Option value="د.أ">د.أ (دينار)</Select.Option>
                      <Select.Option value="$">$ (دولار)</Select.Option>
                      <Select.Option value="€">€ (يورو)</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="المنطقة الزمنية"
                    name="timezone"
                  >
                    <Select placeholder="اختر المنطقة الزمنية">
                      <Select.Option value="Asia/Jerusalem">آسيا/القدس</Select.Option>
                      <Select.Option value="Asia/Amman">آسيا/عمان</Select.Option>
                      <Select.Option value="Asia/Dubai">آسيا/دبي</Select.Option>
                      <Select.Option value="Europe/London">أوروبا/لندن</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="تنسيق التاريخ"
                    name="date_format"
                  >
                    <Select placeholder="تنسيق التاريخ">
                      <Select.Option value="YYYY-MM-DD">YYYY-MM-DD</Select.Option>
                      <Select.Option value="DD/MM/YYYY">DD/MM/YYYY</Select.Option>
                      <Select.Option value="MM/DD/YYYY">MM/DD/YYYY</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="اللغة الافتراضية"
                    name="language"
                  >
                    <Select placeholder="اختر اللغة">
                      <Select.Option value="ar">العربية</Select.Option>
                      <Select.Option value="en">English</Select.Option>
                      <Select.Option value="he">עברית</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </SettingsSection>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={saving}
                  icon={<SaveOutlined />}
                >
                  حفظ الإعدادات العامة
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => generalForm.resetFields()}
                >
                  إعادة تعيين
                </Button>
              </Space>
            </Form.Item>
          </Form>
            )
          },
          {
            key: 'advanced',
            label: (
              <Space>
                <SecurityScanOutlined />
                الإعدادات المتقدمة
              </Space>
            ),
            children: (
          <Form
            form={advancedForm}
            layout="vertical"
            onFinish={handleAdvancedSubmit}
          >
            <SettingsSection>
              <div className="section-title">
                <LockOutlined />
                إعدادات الأمان
              </div>

              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="مدة انتهاء الجلسة (بالدقائق)"
                    name="session_timeout"
                    rules={[{ required: true, message: 'يرجى إدخال مدة الجلسة' }]}
                  >
                    <InputNumber
                      min={30}
                      max={1440}
                      placeholder="480"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="الحد الأدنى لطول كلمة المرور"
                    name="password_min_length"
                    rules={[{ required: true, message: 'يرجى إدخال الحد الأدنى' }]}
                  >
                    <InputNumber
                      min={4}
                      max={20}
                      placeholder="6"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="الحد الأقصى لمحاولات تسجيل الدخول"
                    name="max_login_attempts"
                  >
                    <InputNumber
                      min={3}
                      max={10}
                      placeholder="5"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="تسجيل الخروج التلقائي"
                    name="auto_logout_enabled"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="مفعل" unCheckedChildren="معطل" />
                  </Form.Item>
                </Col>
              </Row>
            </SettingsSection>

            <Divider />

            <SettingsSection>
              <div className="section-title">
                <DatabaseOutlined />
                إعدادات النسخ الاحتياطي
              </div>

              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="تفعيل النسخ الاحتياطي التلقائي"
                    name="backup_enabled"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="مفعل" unCheckedChildren="معطل" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item
                    label="تكرار النسخ الاحتياطي"
                    name="backup_frequency"
                  >
                    <Select placeholder="اختر التكرار">
                      <Select.Option value="daily">يومي</Select.Option>
                      <Select.Option value="weekly">أسبوعي</Select.Option>
                      <Select.Option value="monthly">شهري</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Alert
                message="تنبيه"
                description="يُنصح بتفعيل النسخ الاحتياطي التلقائي لحماية بياناتك من الفقدان"
                type="info"
                showIcon
                style={{ marginTop: 16 }}
              />
            </SettingsSection>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={saving}
                  icon={<SaveOutlined />}
                >
                  حفظ الإعدادات المتقدمة
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => advancedForm.resetFields()}
                >
                  إعادة تعيين
                </Button>
              </Space>
            </Form.Item>
          </Form>
            )
          },
          {
            key: 'theme',
            label: (
              <Space>
                <BgColorsOutlined />
                إعدادات الثيم
              </Space>
            ),
            children: (
              <Form
                form={themeForm}
                layout="vertical"
                onFinish={handleThemeSubmit}
              >
                <SettingsSection>
                  <div className="section-title">
                    <SkinOutlined />
                    مظهر التطبيق
                  </div>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="وضع الثيم"
                        name="theme_mode"
                      >
                        <Select placeholder="اختر وضع الثيم">
                          <Select.Option value="light">فاتح</Select.Option>
                          <Select.Option value="dark">داكن</Select.Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="اللون الأساسي"
                        name="primary_color"
                      >
                        <Input type="color" style={{ width: '100%', height: 32 }} />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="لون النجاح"
                        name="custom_success_color"
                      >
                        <Input type="color" style={{ width: '100%', height: 32 }} />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="لون التحذير"
                        name="custom_warning_color"
                      >
                        <Input type="color" style={{ width: '100%', height: 32 }} />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="لون الخطأ"
                        name="custom_error_color"
                      >
                        <Input type="color" style={{ width: '100%', height: 32 }} />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="لون المعلومات"
                        name="custom_info_color"
                      >
                        <Input type="color" style={{ width: '100%', height: 32 }} />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={8}>
                      <Form.Item
                        label="حجم الخط"
                        name="font_size"
                      >
                        <InputNumber
                          min={12}
                          max={18}
                          placeholder="14"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={8}>
                      <Form.Item
                        label="نصف قطر الحواف"
                        name="border_radius"
                      >
                        <InputNumber
                          min={0}
                          max={20}
                          placeholder="6"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={8}>
                      <Form.Item
                        label="الوضع المضغوط"
                        name="compact_mode"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="مفعل" unCheckedChildren="معطل" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="عائلة الخط"
                        name="font_family"
                      >
                        <Select placeholder="اختر عائلة الخط">
                          <Select.Option value="Segoe UI, Tahoma, Arial, sans-serif">Segoe UI</Select.Option>
                          <Select.Option value="Arial, sans-serif">Arial</Select.Option>
                          <Select.Option value="Helvetica, sans-serif">Helvetica</Select.Option>
                          <Select.Option value="Times New Roman, serif">Times New Roman</Select.Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="طي الشريط الجانبي"
                        name="sidebar_collapsed"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="مطوي" unCheckedChildren="مفتوح" />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="إغلاق القوائم المنسدلة تلقائياً"
                        name="auto_close_menus"
                        valuePropName="checked"
                        tooltip="عند تفعيل هذا الخيار، ستُغلق القوائم المنسدلة تلقائياً بعد اختيار عنصر فرعي"
                      >
                        <Switch checkedChildren="تلقائي" unCheckedChildren="يدوي" />
                      </Form.Item>
                    </Col>
                  </Row>
                </SettingsSection>

                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={saving}
                      icon={<SaveOutlined />}
                    >
                      حفظ إعدادات الثيم
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={() => themeForm.resetFields()}
                    >
                      إعادة تعيين
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            )
          },
          {
            key: 'notifications',
            label: (
              <Space>
                <BellOutlined />
                إعدادات الإشعارات
              </Space>
            ),
            children: (
              <Form
                form={notificationForm}
                layout="vertical"
                onFinish={handleNotificationSubmit}
              >
                <SettingsSection>
                  <div className="section-title">
                    <BellOutlined />
                    إعدادات الإشعارات العامة
                  </div>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="تفعيل الإشعارات"
                        name="notifications_enabled"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="مفعل" unCheckedChildren="معطل" />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="تفعيل صوت الإشعارات"
                        name="notification_sound"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="مفعل" unCheckedChildren="معطل" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="تفعيل الدليل الصوتي"
                        name="voice_guide_enabled"
                        valuePropName="checked"
                        tooltip="تشغيل شرح صوتي عند التمرير على العناصر"
                      >
                        <Switch
                          checkedChildren="مفعل"
                          unCheckedChildren="معطل"
                          onChange={(checked) => {
                            if (window.comprehensiveAudioSystem) {
                              window.comprehensiveAudioSystem.updateSettings({ voiceEnabled: checked })
                            }
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="مدة عرض الإشعار (ميلي ثانية)"
                        name="notification_duration"
                      >
                        <InputNumber
                          min={1000}
                          max={10000}
                          step={500}
                          placeholder="4500"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="سرعة الصوت المتحدث"
                        name="speech_rate"
                        tooltip="تحكم في سرعة الصوت المتحدث (0.5 = بطيء، 1.0 = عادي، 2.0 = سريع)"
                      >
                        <InputNumber
                          min={0.5}
                          max={2.0}
                          step={0.1}
                          placeholder="1.2"
                          style={{ width: '100%' }}
                          formatter={(value) => `${value}x`}
                          parser={(value) => {
                            const num = parseFloat(value?.replace('x', '') || '0') || 0
                            return Math.max(0.5, Math.min(2, num)) as 0.5 | 2
                          }}
                          onChange={(value) => {
                            if (value && window.comprehensiveAudioSystem) {
                              (window.comprehensiveAudioSystem as any).updateSpeechRate(value)
                            }
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
                    <Col xs={24} md={12}>
                      <Form.Item label="اختبار الدليل الصوتي">
                        <Button
                          type="default"
                          icon={<SoundOutlined />}
                          onClick={() => {
                            if (window.comprehensiveAudioSystem) {
                              window.comprehensiveAudioSystem.speakText('test', 'مرحباً، هذا اختبار للدليل الصوتي')
                            }
                          }}
                        >
                          اختبار الصوت
                        </Button>
                      </Form.Item>
                    </Col>
                  </Row>
                </SettingsSection>

                <Divider />

                <SettingsSection>
                  <div className="section-title">
                    <ThunderboltOutlined />
                    الإشعارات الذكية
                  </div>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="حد التنبيه للمخزون المنخفض"
                        name="low_stock_threshold"
                      >
                        <InputNumber
                          min={1}
                          max={100}
                          placeholder="10"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="أيام التذكير قبل استحقاق الفاتورة"
                        name="invoice_due_reminder_days"
                      >
                        <InputNumber
                          min={1}
                          max={30}
                          placeholder="3"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Alert
                    message="الإشعارات الذكية"
                    description="سيتم إرسال إشعارات تلقائية للمخزون المنخفض والفواتير المستحقة والأحداث المهمة الأخرى"
                    type="info"
                    showIcon
                    style={{ marginTop: 16 }}
                  />
                </SettingsSection>

                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={saving}
                      icon={<SaveOutlined />}
                    >
                      حفظ إعدادات الإشعارات
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={() => notificationForm.resetFields()}
                    >
                      إعادة تعيين
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            )
          },
          {
            key: 'sync',
            label: (
              <Space>
                <SyncOutlined />
                ربط الأجهزة
              </Space>
            ),
            children: <SimpleSyncSettings />
          },


          {
            key: 'fingerprint',
            label: (
              <Space>
                <ScanOutlined />
                أجهزة البصمة
              </Space>
            ),
            children: (
              <Form
                form={fingerprintForm}
                layout="vertical"
                onFinish={handleFingerprintSubmit}
                initialValues={{
                  fingerprint_enabled: settings.fingerprint_enabled === 'true',
                  fingerprint_timeout: parseInt(settings.fingerprint_timeout || '30'),
                  fingerprint_retry_attempts: parseInt(settings.fingerprint_retry_attempts || '3'),
                  fingerprint_auto_sync: settings.fingerprint_auto_sync === 'true',
                  fingerprint_sync_interval: parseInt(settings.fingerprint_sync_interval || '60'),
                  fingerprint_backup_enabled: settings.fingerprint_backup_enabled === 'true',
                  fingerprint_log_level: settings.fingerprint_log_level || 'info',
                  fingerprint_connection_timeout: parseInt(settings.fingerprint_connection_timeout || '10')
                }}
              >
                <SettingsSection>
                  <div className="section-title">
                    <ScanOutlined />
                    إعدادات الاتصال
                  </div>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="fingerprint_enabled"
                        label="تفعيل أجهزة البصمة"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="fingerprint_connection_timeout"
                        label="مهلة الاتصال (ثانية)"
                      >
                        <InputNumber
                          min={5}
                          max={60}
                          placeholder="10"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="fingerprint_timeout"
                        label="مهلة انتّار البصمة (ثانية)"
                      >
                        <InputNumber
                          min={10}
                          max={120}
                          placeholder="30"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="fingerprint_retry_attempts"
                        label="عدد محاولات إعادة المحاولة"
                      >
                        <InputNumber
                          min={1}
                          max={10}
                          placeholder="3"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </SettingsSection>

                <SettingsSection>
                  <div className="section-title">
                    <SyncOutlined />
                    إعدادات المزامنة
                  </div>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="fingerprint_auto_sync"
                        label="المزامنة التلقائية"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="fingerprint_sync_interval"
                        label="فترة المزامنة (دقيقة)"
                      >
                        <InputNumber
                          min={5}
                          max={1440}
                          placeholder="60"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="fingerprint_backup_enabled"
                        label="النسخ الاحتياطي للبيانات"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        name="fingerprint_log_level"
                        label="مستوى السجلات"
                      >
                        <Select
                          placeholder="اختر مستوى السجلات"
                          style={{ width: '100%' }}
                        >
                          <Select.Option value="error">أخطاء فقط</Select.Option>
                          <Select.Option value="warn">تحذيرات وأخطاء</Select.Option>
                          <Select.Option value="info">معلومات عامة</Select.Option>
                          <Select.Option value="debug">تفاصيل كاملة</Select.Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                </SettingsSection>

                <Alert
                  message="إعدادات أجهزة البصمة"
                  description="هذه الإعدادات تتحكم في كيفية اتصال النظام مع أجهزة البصمة ومزامنة البيانات. تأكد من ضبط المهل الزمنية بما يتناسب مع شبكتك."
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={saving}
                      icon={<SaveOutlined />}
                    >
                      حفظ إعدادات أجهزة البصمة
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={() => fingerprintForm.resetFields()}
                    >
                      إعادة تعيين
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            )
          },
          {
            key: 'license',
            label: (
              <Space>
                <SafetyOutlined />
                إدارة التفعيل
              </Space>
            ),
            children: (
              <div>
                <SettingsSection>
                  <div className="section-title">
                    <SafetyOutlined />
                    معلومات التفعيل
                  </div>

                  {licenseInfo.isActivated ? (
                    <Card style={{ marginBottom: 24 }}>
                      <Row gutter={[16, 16]}>
                        <Col xs={24} md={12}>
                          <div style={{ textAlign: 'center', padding: '20px' }}>
                            <SafetyOutlined style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} />
                            <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#52c41a', marginBottom: '8px' }}>
                              البرنامج مفعل
                            </div>
                            <div style={{ color: '#666' }}>
                              {licenseInfo.licenseType === 'LIFETIME' ? 'ترخيص مدى الحياة' : 'ترخيص محدود المدة'}
                            </div>
                          </div>
                        </Col>
                        <Col xs={24} md={12}>
                          <div style={{ padding: '20px' }}>
                            {licenseInfo.activationDate && (
                              <div style={{ marginBottom: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <CalendarOutlined style={{ color: '#1890ff' }} />
                                <span style={{ fontWeight: '500' }}>تاريخ التفعيل:</span>
                                <span>{new Date(licenseInfo.activationDate).toLocaleDateString('ar-EG')}</span>
                              </div>
                            )}

                            {licenseInfo.expiryDate && (
                              <div style={{ marginBottom: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <CalendarOutlined style={{ color: '#faad14' }} />
                                <span style={{ fontWeight: '500' }}>تاريخ الانتهاء:</span>
                                <span>{new Date(licenseInfo.expiryDate).toLocaleDateString('ar-EG')}</span>
                              </div>
                            )}

                            {licenseInfo.daysRemaining !== undefined && licenseInfo.daysRemaining >= 0 && (
                              <div style={{ marginBottom: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <ClockCircleOutlined style={{ color: licenseInfo.daysRemaining < 30 ? '#ff4d4f' : '#52c41a' }} />
                                <span style={{ fontWeight: '500' }}>الأيام المتبقية:</span>
                                <span style={{
                                  color: licenseInfo.daysRemaining < 30 ? '#ff4d4f' : '#52c41a',
                                  fontWeight: 'bold'
                                }}>
                                  {licenseInfo.daysRemaining} يوم
                                </span>
                              </div>
                            )}

                            {licenseInfo.daysRemaining === -1 && (
                              <div style={{ marginBottom: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <ClockCircleOutlined style={{ color: '#52c41a' }} />
                                <span style={{ fontWeight: '500' }}>مدة الترخيص:</span>
                                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>مدى الحياة</span>
                              </div>
                            )}
                          </div>
                        </Col>
                      </Row>
                    </Card>
                  ) : (
                    <Alert
                      message="البرنامج غير مفعل"
                      description="يرجى تفعيل البرنامج للاستمرار في الاستخدام"
                      type="warning"
                      showIcon
                      style={{ marginBottom: 24 }}
                    />
                  )}
                </SettingsSection>

                {licenseInfo.isActivated && (
                  <SettingsSection>
                    <div className="section-title">
                      <KeyOutlined />
                      إلغاء التفعيل
                    </div>

                    <Alert
                      message="تحذير"
                      description="إلغاء التفعيل سيؤدي إلى إغلاق البرنامج وستحتاج إلى رقم تفعيل جديد للاستخدام مرة أخرى"
                      type="warning"
                      showIcon
                      style={{ marginBottom: 20 }}
                    />

                    <Row gutter={[16, 16]}>
                      <Col xs={24} md={16}>
                        <Input
                          size="large"
                          placeholder="أدخل رقم إلغاء التفعيل"
                          value={deactivationCode}
                          onChange={(e) => setDeactivationCode(e.target.value.toUpperCase())}
                          prefix={<KeyOutlined />}
                          style={{
                            fontSize: '16px',
                            direction: 'ltr',
                            textAlign: 'center'
                          }}
                        />
                      </Col>
                      <Col xs={24} md={8}>
                        <Button
                          danger
                          size="large"
                          loading={deactivationLoading}
                          onClick={handleDeactivation}
                          disabled={!deactivationCode.trim()}
                          style={{ width: '100%' }}
                        >
                          إلغاء التفعيل
                        </Button>
                      </Col>
                    </Row>
                  </SettingsSection>
                )}
              </div>
            )
          },
          {
            key: 'enhancements',
            label: (
              <Space>
                <RocketOutlined />
                التحسينات المتقدمة
              </Space>
            ),
            children: (
              <div>
                <SettingsSection>
                  <div className="section-title">
                    <TableOutlined />
                    إعدادات الجداول المحسنة
                  </div>

                  <Alert
                    message="الجداول المحسنة"
                    description="تتيح لك الجداول المحسنة عرض وإدارة البيانات بكفاءة أكبر مع ميزات البحث والتصفية والتصدير المتقدمة."
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />

                  <Row gutter={[16, 16]}>
                    <Col xs={24}>
                      <Card
                        title="عرض الجداول المحسنة"
                        size="small"
                        extra={
                          <Button
                            type="primary"
                            icon={<TableOutlined />}
                            onClick={() => {
                              // الانتقال إلى صفحة العرض التوضيحي
                              if (window.location.hash !== '#/dashboard') {
                                window.location.hash = '#/dashboard'
                              }
                              // تحديد القسم المطلوب
                              setTimeout(() => {
                                const event = new CustomEvent('navigate-to-section', {
                                  detail: { section: 'enhanced-table-demo' }
                                })
                                window.dispatchEvent(event)
                              }, 100)
                            }}
                          >
                            فتح العرض التوضيحي
                          </Button>
                        }
                      >
                        <p>جرب الجداول المحسنة مع بيانات تجريبية تتضمن:</p>
                        <ul>
                          <li>🔍 بحث متقدم في جميع الحقول</li>
                          <li>📊 تصفية ديناميكية للبيانات</li>
                          <li>📤 تصدير إلى Excel و PDF</li>
                          <li>🖨️ طباعة محسنة</li>
                          <li>⚡ تمرير افتراضي للبيانات الكبيرة</li>
                        </ul>
                      </Card>
                    </Col>
                  </Row>
                </SettingsSection>



                <SettingsSection>
                  <div className="section-title">
                    <ExperimentOutlined />
                    مكونات UX محسنة
                  </div>

                  <Alert
                    message="تجربة مستخدم محسنة"
                    description="مكونات متحركة وتأثيرات بصرية لتحسين تجربة المستخدم."
                    type="success"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />

                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <Card title="Loading Skeletons" size="small">
                        <p>هياكل تحميل متنوعة:</p>
                        <ul>
                          <li>📄 هيكل الجداول</li>
                          <li>📊 هيكل البطاقات</li>
                          <li>📝 هيكل النماذج</li>
                          <li>📈 هيكل الرسوم البيانية</li>
                        </ul>
                      </Card>
                    </Col>
                    <Col xs={24} md={12}>
                      <Card title="مكونات متحركة" size="small">
                        <p>تأثيرات حركية سلسة:</p>
                        <ul>
                          <li>✨ FadeIn Animation</li>
                          <li>📱 SlideIn Animation</li>
                          <li>🎯 Bounce Animation</li>
                          <li>🔢 عداد متحرك</li>
                        </ul>
                      </Card>
                    </Col>
                  </Row>
                </SettingsSection>

                <Divider />

                <Alert
                  message="ملاحظة مهمة"
                  description="جميع التحسينات المتقدمة متاحة الآن ويمكن الوصول إليها من خلال واجهة التطبيق. لا تحتاج إلى إعدادات إضافية."
                  type="warning"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              </div>
            )
          },

        ]}
      />

      {/* تم حذف مدير إعدادات الطباعة المتقدم */}
      {/* <PrintSettingsManager
        visible={showPrintSettings}
        onClose={() => setShowPrintSettings(false)}
      /> */}
    </StyledCard>
  )
}

export default SystemSettings
