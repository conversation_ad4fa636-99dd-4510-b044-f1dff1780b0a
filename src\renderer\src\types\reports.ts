/**
 * واجهات موحدة لنظام التقارير
 * تحتوي على جميع الأنواع والواجهات المستخدمة في التقارير
 */

import dayjs from 'dayjs'

// أنواع التقارير المختلفة
export type ReportType = 
  // تقارير المخزون
  | 'inventory_detailed' 
  | 'inventory_movements' 
  | 'inventory_audit' 
  | 'material_consumption' 
  | 'low_stock'
  | 'item_warehouse_distribution'
  | 'abc_analysis'
  
  // تقارير المشتريات
  | 'purchases_by_supplier'
  | 'purchases_by_item'
  | 'supplier_payables'
  | 'purchase_analysis'
  | 'cost_analysis'
  | 'supplier_price_comparison'
  | 'supplier_quality'
  | 'supplier_analysis'
  | 'purchase_performance'
  
  // تقارير المبيعات
  | 'sales_by_customer'
  | 'sales_by_product'
  | 'sales_by_region'
  | 'monthly_sales'
  | 'sales_returns'
  | 'top_profitable_customers'
  | 'profitability'
  
  // تقارير الموظفين
  | 'employee_attendance'
  | 'employee_payroll'
  | 'employee_leaves'
  | 'employee_performance'
  | 'employee_overtime'
  | 'employee_analysis'
  | 'salary_comparison'
  | 'efficiency_evaluation'
  
  // تقارير الإنتاج
  | 'production_orders'
  | 'production_efficiency'
  | 'production_costs'
  | 'production_schedule'
  | 'production_quality'
  | 'production_workers_performance'
  | 'production_materials_consumption'
  | 'production_profitability'
  
  // تقارير الدهان
  | 'paint_by_customer'
  | 'paint_by_type'
  | 'monthly_paint'
  | 'paint_profitability'
  | 'paint_performance'
  | 'paint_quality'

  // تقارير إقفال السنة المالية
  | 'closing_summary'
  | 'closing_entries'
  | 'carried_forward_balances'
  | 'audit_log'
  | 'period_comparison'
  | 'closing_checklist'
  
  // تقارير مالية
  | 'balance_sheet'
  | 'income_statement'
  | 'cash_flow'
  | 'cash_flow_analysis'
  | 'profit_loss'
  | 'bank_reconciliation'
  | 'customer_aging'
  
  // تقارير إدارية
  | 'departments_integration'

// واجهة عمود التقرير
export interface ReportColumn {
  key: string
  title: string
  dataIndex?: string
  width?: number | string
  align?: 'left' | 'center' | 'right'
  format?: 'currency' | 'number' | 'date' | 'text' | 'percentage'
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, record: any, index: number) => React.ReactNode
}

// واجهة بيانات التقرير
export interface ReportData {
  title: string
  subtitle?: string
  description?: string
  columns: ReportColumn[]
  data: any[]
  statistics?: ReportStatistic[]
  summary?: ReportSummary
  metadata?: ReportMetadata
}

// واجهة إحصائيات التقرير
export interface ReportStatistic {
  title: string
  value: string | number
  color?: string
  icon?: string
  trend?: 'up' | 'down' | 'stable'
  percentage?: number
}

// واجهة ملخص التقرير
export interface ReportSummary {
  totalItems?: number
  totalValue?: number
  totalQuantity?: number
  averageValue?: number
  maxValue?: number
  minValue?: number
  [key: string]: any
}

// واجهة بيانات التقرير الوصفية
export interface ReportMetadata {
  generatedAt: string
  generatedBy?: string
  reportType: ReportType
  filters?: ReportFilters
  totalRecords: number
  processingTime?: number
  version?: string
  dateRange?: string
  // خصائص إضافية للتحسين والضغط
  isLazyLoaded?: boolean
  totalEstimated?: number
  currentlyLoaded?: number
  batchSize?: number
  isCompressed?: boolean
  originalSize?: number
  compressedSize?: number
  compressionKey?: string
}

// واجهة فلاتر التقرير
export interface ReportFilters {
  // فلاتر التاريخ
  dateRange?: [any, any] | string[]
  startDate?: string | any
  endDate?: string | any
  year?: number
  month?: number
  quarter?: number
  
  // فلاتر الكيانات
  warehouseId?: number | number[]
  categoryId?: number | number[]
  itemId?: number | number[]
  supplierId?: number | number[]
  customerId?: number | number[]
  employeeId?: number | number[]
  departmentId?: number | number[]
  
  // فلاتر الحالة
  status?: string | string[]
  movementType?: string | string[]
  paymentStatus?: string | string[]
  
  // فلاتر القيم
  minAmount?: number
  maxAmount?: number
  amountFrom?: number
  amountTo?: number
  minQuantity?: number
  maxQuantity?: number
  
  // فلاتر أخرى
  searchTerm?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  page?: number
  pageSize?: number
  
  [key: string]: any
}

// واجهة خصائص مكون التقرير
export interface ReportComponentProps {
  type?: ReportType
  title: string
  description?: string
  generateReport?: (filters: ReportFilters) => Promise<ReportData>
  onGenerateReport?: (filters: ReportFilters) => Promise<ReportData>
  defaultFilters?: Partial<ReportFilters>
  
  // إعدادات الفلاتر
  showDateRange?: boolean
  showWarehouseFilter?: boolean
  showCategoryFilter?: boolean
  showItemFilter?: boolean
  showSupplierFilter?: boolean
  showCustomerFilter?: boolean
  showEmployeeFilter?: boolean
  showDepartmentFilter?: boolean
  showStatusFilter?: boolean
  showMovementTypeFilter?: boolean
  showAmountRangeFilter?: boolean
  showAdvancedSearch?: boolean
  
  // إعدادات العرض
  showStatistics?: boolean
  showSummary?: boolean
  showExportOptions?: boolean
  showPrintOptions?: boolean
  
  // إعدادات التخصيص
  customFilters?: React.ReactNode
  customActions?: React.ReactNode
  exportFileName?: string
  
  // callbacks
  onDataLoad?: (data: ReportData) => void
  onError?: (error: string) => void
  onExport?: (format: 'excel' | 'pdf' | 'csv', data: ReportData) => void
}

// واجهة خيارات التصدير
export interface ExportOptions {
  format: 'excel' | 'pdf' | 'csv'
  fileName?: string
  includeStatistics?: boolean
  includeSummary?: boolean
  includeMetadata?: boolean
  customHeaders?: Record<string, string>
  excludeColumns?: string[]
  pageSize?: 'A4' | 'A3' | 'Letter'
  orientation?: 'portrait' | 'landscape'
}

// واجهة خيارات الطباعة
export interface PrintOptions {
  title?: string
  subtitle?: string
  showLogo?: boolean
  showHeader?: boolean
  showFooter?: boolean
  showSignature?: boolean
  pageSize?: 'A4' | 'A3' | 'A5' | 'Letter'
  orientation?: 'portrait' | 'landscape'
  margins?: {
    top?: number
    bottom?: number
    left?: number
    right?: number
  }
  fontSize?: number
  fontFamily?: string
  primaryColor?: string
  secondaryColor?: string
}

// واجهة نتيجة عملية التقرير
export interface ReportResult {
  success: boolean
  data?: ReportData
  message?: string
  error?: string
  processingTime?: number
  totalRecords?: number
  fromCache?: boolean
  hasMore?: boolean
  isLazyLoaded?: boolean
  compressionRatio?: number
  cacheHit?: boolean
}

// واجهة إعدادات التقرير
export interface ReportSettings {
  autoRefresh?: boolean
  refreshInterval?: number
  cacheResults?: boolean
  cacheDuration?: number
  maxRecords?: number
  defaultPageSize?: number
  defaultSortBy?: string
  defaultSortOrder?: 'asc' | 'desc'
}

// أنواع حالات التقرير
export type ReportStatus = 'idle' | 'loading' | 'success' | 'error'

// واجهة حالة التقرير
export interface ReportState {
  status: ReportStatus
  data: ReportData | null
  filters: ReportFilters
  settings: ReportSettings
  error: string | null
  lastUpdated: string | null
}

// واجهة أكشن التقرير
export interface ReportAction {
  type: 'SET_LOADING' | 'SET_DATA' | 'SET_ERROR' | 'SET_FILTERS' | 'RESET'
  payload?: any
}

// دالة مساعدة لتنسيق القيم
export const formatValue = (value: any, format?: ReportColumn['format']): string => {
  if (value === null || value === undefined) return ''
  
  switch (format) {
    case 'currency':
      return typeof value === 'number' ? `₪${value.toLocaleString()}` : value.toString()
    case 'number':
      return typeof value === 'number' ? value.toLocaleString() : value.toString()
    case 'percentage':
      return typeof value === 'number' ? `${value.toFixed(1)}%` : value.toString()
    case 'date':
      return dayjs(value).isValid() ? dayjs(value).format('YYYY-MM-DD') : value.toString()
    default:
      return value.toString()
  }
}

// دالة مساعدة لإنشاء فلاتر افتراضية
export const createDefaultFilters = (_type: ReportType): ReportFilters => {
  // عدم تحديد تواريخ افتراضية = كل المدة
  return {
    dateRange: null, // null يعني كل المدة
    page: 1,
    pageSize: 50,
    sortOrder: 'desc'
  }
}

// دالة مساعدة لإنشاء أعمدة افتراضية
export const createDefaultColumns = (_type: ReportType): ReportColumn[] => {
  // يمكن توسيع هذه الدالة لإرجاع أعمدة مختلفة حسب نوع التقرير
  return [
    { key: 'id', title: 'المعرف', align: 'center', width: 80 },
    { key: 'name', title: 'الاسم', align: 'right', width: 200 },
    { key: 'date', title: 'التاريخ', align: 'center', format: 'date', width: 120 },
    { key: 'amount', title: 'المبلغ', align: 'center', format: 'currency', width: 120 }
  ]
}
