/**
 * مكون إنشاء قوالب الطباعة المحسن
 * يستخدم المحرر المتكامل الجديد مع دعم الوراثة والأعمدة
 */

import React, { useState } from 'react';
import {
  Button,
  Space
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  CopyOutlined
} from '@ant-design/icons';

// استيراد المحرر المتكامل الجديد
import EnhancedTemplateCreator from './EnhancedTemplateCreator';
import { EnhancedTemplate, TemplateEditMode } from '../../types/enhancedTemplateTypes';

interface PrintTemplateCreatorProps {
  visible?: boolean;
  onClose?: () => void;
  template?: EnhancedTemplate;
  mode?: TemplateEditMode;
  reportContext?: {
    reportType: string;
    reportCategory: string;
    reportTitle: string;
  };
}
/**
 * مكون إنشاء قوالب الطباعة المحسن
 * يستخدم المحرر المتكامل الجديد
 */

const PrintTemplateCreator: React.FC<PrintTemplateCreatorProps> = ({
  visible = false,
  onClose = () => {},
  template,
  mode = 'create',
  reportContext
}) => {
  const [editorVisible, setEditorVisible] = useState(false);

  // فتح المحرر المتكامل
  const openEnhancedEditor = () => {
    setEditorVisible(true);
  };

  // إغلاق المحرر المتكامل
  const closeEnhancedEditor = () => {
    setEditorVisible(false);
    if (onClose) onClose();
  };

  // إذا كان المحرر القديم مطلوب للعرض، نفتح المحرر الجديد مباشرة
  React.useEffect(() => {
    if (visible) {
      openEnhancedEditor();
    }
  }, [visible]);

  return (
    <>
      {/* أزرار سريعة لإنشاء قوالب جديدة */}
      {!visible && (
        <Space wrap style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={openEnhancedEditor}
          >
            إنشاء قالب جديد
          </Button>

          {template && (
            <>
              <Button
                icon={<EditOutlined />}
                onClick={() => {
                  setEditorVisible(true);
                }}
              >
                تعديل القالب
              </Button>

              <Button
                icon={<CopyOutlined />}
                onClick={() => {
                  setEditorVisible(true);
                }}
              >
                نسخ القالب
              </Button>
            </>
          )}
        </Space>
      )}

      {/* المحرر المتكامل الجديد */}
      <EnhancedTemplateCreator
        visible={editorVisible}
        onClose={closeEnhancedEditor}
        template={template}
        mode={mode}
        reportContext={reportContext}
      />
    </>
  );
};

export default PrintTemplateCreator;
