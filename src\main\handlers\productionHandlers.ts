import { ipcMain } from 'electron'
import { ProductionService, AuthService } from '../services'
import { Logger } from '../utils/logger'

let productionService: ProductionService

export function setProductionService(service: ProductionService) {
  productionService = service
}

export function registerProductionHandlers(): void {
  // أقسام الإنتاج
  ipcMain.handle('get-production-departments', async () => {
    try {
      const departments = await productionService.getProductionDepartments()
      return { success: true, data: departments }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب أقسام الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب أقسام الإنتاج' }
    }
  })

  ipcMain.handle('create-production-department', async (_event, departmentData: any) => {
    try {
      // الحصول على دور المستخدم الحالي
      const userRole = AuthService.getCurrentUserRole()

      return await productionService.createProductionDepartment(departmentData, userRole || undefined)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إنشاء قسم الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في إنشاء قسم الإنتاج' }
    }
  })

  // تحديث قسم إنتاج
  ipcMain.handle('update-production-department', async (_event, departmentId: number, departmentData: any) => {
    try {
      if (!productionService) {
        return { success: false, message: 'خدمة الإنتاج غير متاحة' }
      }

      // الحصول على دور المستخدم الحالي
      const userRole = AuthService.getCurrentUserRole()

      return await productionService.updateProductionDepartment(departmentId, departmentData, userRole || undefined)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تحديث قسم الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تحديث قسم الإنتاج' }
    }
  })

  // حذف قسم إنتاج
  ipcMain.handle('delete-production-department', async (_event, departmentId: number) => {
    try {
      if (!productionService) {
        return { success: false, message: 'خدمة الإنتاج غير متاحة' }
      }

      // الحصول على دور المستخدم الحالي
      const userRole = AuthService.getCurrentUserRole()

      return await productionService.deleteProductionDepartment(departmentId, userRole || undefined)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في حذف قسم الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في حذف قسم الإنتاج' }
    }
  })

  ipcMain.handle('generate-production-department-code', async () => {
    try {
      const code = await productionService.generateDepartmentCode()
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في توليد كود قسم الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في توليد كود قسم الإنتاج' }
    }
  })

  // أوامر الإنتاج
  ipcMain.handle('get-production-orders', async () => {
    try {
      const orders = await productionService.getProductionOrders()
      return { success: true, data: orders }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب أوامر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب أوامر الإنتاج' }
    }
  })

  ipcMain.handle('create-production-order', async (_event, orderData: any) => {
    try {
      return await productionService.createProductionOrder(orderData)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إنشاء أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في إنشاء أمر الإنتاج' }
    }
  })

  ipcMain.handle('generate-production-order-number', async () => {
    try {
      const orderNumber = await productionService.generateOrderNumber()
      return { success: true, data: { order_number: orderNumber } }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في توليد رقم أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم أمر الإنتاج' }
    }
  })

  ipcMain.handle('generate-production-order-code', async () => {
    try {
      const orderCode = await productionService.generateOrderCode()
      return { success: true, data: { order_code: orderCode } }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في توليد كود أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في توليد كود أمر الإنتاج' }
    }
  })

  // إنشاء أمر إنتاج مع عدة وصفات
  ipcMain.handle('create-production-order-with-multiple-recipes', async (_event, orderData: any) => {
    try {
      return await productionService.createProductionOrderWithMultipleRecipes(orderData)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إنشاء أمر الإنتاج مع عدة وصفات:', error)
      return { success: false, message: 'حدث خطأ في إنشاء أمر الإنتاج' }
    }
  })

  // التحقق من توفر المواد لعدة وصفات
  ipcMain.handle('check-multiple-recipes-materials-availability', async (_event, recipes: any[]) => {
    try {
      return await productionService.checkMultipleRecipesMaterialsAvailability(recipes)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في التحقق من توفر المواد:', error)
      return { success: false, message: 'حدث خطأ في التحقق من توفر المواد' }
    }
  })

  // الحصول على تفاصيل أمر الإنتاج مع الوصفات
  ipcMain.handle('get-production-order-with-recipes', async (_event, orderId: number) => {
    try {
      return await productionService.getProductionOrderWithRecipes(orderId)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب تفاصيل أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب تفاصيل أمر الإنتاج' }
    }
  })

  // إضافة وصفات لأمر إنتاج موجود
  ipcMain.handle('add-production-order-recipes', async (_event, orderId: number, recipes: any[]) => {
    try {
      return await productionService.addProductionOrderRecipes(orderId, recipes)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إضافة وصفات لأمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في إضافة الوصفات' }
    }
  })

  ipcMain.handle('get-production-order-details', async (_event, orderId: number) => {
    try {
      const details = await productionService.getProductionOrderDetails(orderId)
      return { success: true, data: details }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب تفاصيل أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب تفاصيل أمر الإنتاج' }
    }
  })

  // وصفات الإنتاج
  ipcMain.handle('get-production-recipes', async () => {
    try {
      const recipes = await productionService.getProductionRecipes()
      return { success: true, data: recipes }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب وصفات الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب وصفات الإنتاج' }
    }
  })

  ipcMain.handle('create-production-recipe', async (_event, recipeData: any) => {
    try {
      return await productionService.createProductionRecipe(recipeData)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إنشاء وصفة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في إنشاء وصفة الإنتاج' }
    }
  })

  ipcMain.handle('generate-recipe-code', async () => {
    try {
      const code = await productionService.generateRecipeCode()
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في توليد كود الوصفة:', error)
      return { success: false, message: 'حدث خطأ في توليد كود الوصفة' }
    }
  })

  // معالجات الدهان
  ipcMain.handle('get-paint-types', async () => {
    try {
      const paintTypes = await productionService.getPaintTypes()
      return { success: true, data: paintTypes }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب أنواع الدهانات:', error)
      return { success: false, message: 'حدث خطأ في جلب أنواع الدهانات' }
    }
  })

  ipcMain.handle('create-paint-type', async (_event, paintTypeData) => {
    try {
      const result = await productionService.createPaintType(paintTypeData)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إنشاء نوع الدهان:', error)
      return { success: false, message: 'حدث خطأ في إنشاء نوع الدهان' }
    }
  })

  ipcMain.handle('update-paint-type', async (_event, paintTypeId, paintTypeData) => {
    try {
      const result = await productionService.updatePaintType(paintTypeId, paintTypeData)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تحديث نوع الدهان:', error)
      return { success: false, message: 'حدث خطأ في تحديث نوع الدهان' }
    }
  })

  ipcMain.handle('delete-paint-type', async (_event, paintTypeId) => {
    try {
      const result = await productionService.deletePaintType(paintTypeId)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في حذف نوع الدهان:', error)
      return { success: false, message: 'حدث خطأ في حذف نوع الدهان' }
    }
  })

  ipcMain.handle('generate-paint-type-code', async () => {
    try {
      const code = await productionService.generatePaintTypeCode()
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في توليد كود نوع الدهان:', error)
      return { success: false, message: 'حدث خطأ في توليد كود نوع الدهان' }
    }
  })

  // تم إزالة معالج البيانات التجريبية - يجب استخدام البيانات الحقيقية فقط

  // أوامر الدهان
  ipcMain.handle('get-paint-orders', async () => {
    try {
      const orders = await productionService.getPaintOrders()
      return { success: true, data: orders }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب أوامر الدهان:', error)
      return { success: false, message: 'حدث خطأ في جلب أوامر الدهان' }
    }
  })

  ipcMain.handle('create-paint-order', async (_event, orderData) => {
    try {
      const result = await productionService.createPaintOrder(orderData)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إنشاء أمر الدهان:', error)
      return { success: false, message: 'حدث خطأ في إنشاء أمر الدهان' }
    }
  })

  ipcMain.handle('update-paint-order-status', async (_event, orderId, status) => {
    try {
      const result = await productionService.updatePaintOrderStatus(orderId, status)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تحديث حالة أمر الدهان:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة أمر الدهان' }
    }
  })

  ipcMain.handle('generate-paint-order-number', async () => {
    try {
      const orderNumber = await productionService.generatePaintOrderNumber()
      return { success: true, data: { orderNumber } }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في توليد رقم أمر الدهان:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم أمر الدهان' }
    }
  })

  // مراحل الإنتاج
  ipcMain.handle('get-production-stages', async () => {
    try {
      const stages = await productionService.getProductionStages()
      return { success: true, data: stages }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب مراحل الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب مراحل الإنتاج' }
    }
  })

  ipcMain.handle('create-production-stage', async (_event, stageData: any) => {
    try {
      return await productionService.createProductionStage(stageData)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إنشاء مرحلة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في إنشاء مرحلة الإنتاج' }
    }
  })

  ipcMain.handle('update-production-stage', async (_event, stageId: number, stageData: any) => {
    try {
      return await productionService.updateProductionStage(stageId, stageData)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تحديث مرحلة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تحديث مرحلة الإنتاج' }
    }
  })

  ipcMain.handle('generate-production-stage-code', async () => {
    try {
      const code = await productionService.generateProductionStageCode()
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في توليد كود مرحلة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في توليد كود مرحلة الإنتاج' }
    }
  })

  // بدء أمر الإنتاج - استهلاك المواد الخام
  ipcMain.handle('start-production-order', async (_, orderId: number) => {
    try {
      const result = await productionService.startProductionOrder(orderId)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في بدء أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في بدء أمر الإنتاج' }
    }
  })

  // جلب تفاصيل أوامر الإنتاج للطباعة
  ipcMain.handle('get-production-order-items', async (_, orderId: number) => {
    try {
      const items = await productionService.getProductionOrderItems(orderId)
      return { success: true, data: items }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب أصناف أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب أصناف أمر الإنتاج' }
    }
  })

  ipcMain.handle('get-production-order-materials', async (_, orderId: number) => {
    try {
      const materials = await productionService.getProductionOrderMaterials(orderId)
      return { success: true, data: materials }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب مواد أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب مواد أمر الإنتاج' }
    }
  })

  ipcMain.handle('get-production-order-stages', async (_, orderId: number) => {
    try {
      const stages = await productionService.getProductionOrderStages(orderId)
      return { success: true, data: stages }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب مراحل أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب مراحل أمر الإنتاج' }
    }
  })

  // جلب صور أمر الإنتاج
  ipcMain.handle('get-production-order-images', async (_, orderId: number) => {
    try {
      const images = await productionService.getProductionOrderImages(orderId)
      return { success: true, data: images }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب صور أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب صور أمر الإنتاج' }
    }
  })

  // رفع صورة لأمر الإنتاج
  ipcMain.handle('upload-production-order-image', async (_, imageData: any) => {
    try {
      const imageId = await productionService.addProductionOrderImage(imageData)
      return { success: true, data: { id: imageId } }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في رفع صورة أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في رفع صورة أمر الإنتاج' }
    }
  })

  // إنشاء صور تجريبية لأمر الإنتاج
  ipcMain.handle('create-sample-production-order-images', async (_, orderId: number) => {
    try {
      const success = await productionService.createSampleProductionOrderImages(orderId)
      return { success, message: success ? 'تم إنشاء الصور التجريبية بنجاح' : 'فشل في إنشاء الصور التجريبية' }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إنشاء صور تجريبية لأمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الصور التجريبية' }
    }
  })

  // جلب جميع مراحل أوامر الإنتاج
  ipcMain.handle('get-all-production-order-stages', async () => {
    try {
      const stages = await productionService.getAllProductionOrderStages()
      return { success: true, data: stages }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب جميع مراحل أوامر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب جميع مراحل أوامر الإنتاج' }
    }
  })

  // إكمال أمر الإنتاج - إضافة المنتج للمخزون
  ipcMain.handle('complete-production-order', async (_, orderId: number) => {
    try {
      const result = await productionService.completeProductionOrder(orderId)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إكمال أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في إكمال أمر الإنتاج' }
    }
  })

  // تحديث أمر الإنتاج
  ipcMain.handle('update-production-order', async (_, orderId: number, orderData: any) => {
    try {
      return await productionService.updateProductionOrder(orderId, orderData)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تحديث أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تحديث أمر الإنتاج' }
    }
  })

  // حذف أمر الإنتاج
  ipcMain.handle('delete-production-order', async (_, orderId: number) => {
    try {
      return await productionService.deleteProductionOrder(orderId)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في حذف أمر الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في حذف أمر الإنتاج' }
    }
  })

  // تحديث وصفة الإنتاج
  ipcMain.handle('update-production-recipe', async (_, recipeId: number, recipeData: any) => {
    try {
      return await productionService.updateProductionRecipe(recipeId, recipeData)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تحديث وصفة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تحديث وصفة الإنتاج' }
    }
  })

  // حذف وصفة الإنتاج
  ipcMain.handle('delete-production-recipe', async (_, recipeId: number) => {
    try {
      return await productionService.deleteProductionRecipe(recipeId)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في حذف وصفة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في حذف وصفة الإنتاج' }
    }
  })

  // جلب مواد الوصفة
  ipcMain.handle('get-recipe-materials', async (_, recipeId: number) => {
    try {
      return await productionService.getRecipeMaterials(recipeId)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب مواد الوصفة:', error)
      return { success: false, message: 'حدث خطأ في جلب مواد الوصفة' }
    }
  })

  // التحقق من توفر مواد الوصفة
  ipcMain.handle('check-recipe-materials-availability', async (_, recipeId: number, quantity: number) => {
    try {
      const result = await productionService.checkRecipeMaterialsAvailability(recipeId, quantity)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في التحقق من توفر المواد:', error)
      return { success: false, message: 'حدث خطأ في التحقق من توفر المواد' }
    }
  })

  // تنفيذ الإنتاج وتحويل المواد
  ipcMain.handle('execute-production', async (_, productionData: any) => {
    try {
      const result = await productionService.executeProduction(productionData)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تنفيذ الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تنفيذ الإنتاج' }
    }
  })

  // تقرير كفاءة الإنتاج
  ipcMain.handle('get-production-efficiency-report', async (_, filters: any) => {
    try {
      const result = await productionService.getProductionEfficiencyReport(filters)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تقرير كفاءة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تقرير كفاءة الإنتاج' }
    }
  })

  // تقرير تكاليف الإنتاج
  ipcMain.handle('get-production-costs-report', async (_, filters: any) => {
    try {
      const result = await productionService.getProductionCostsReport(filters)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تقرير تكاليف الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تقرير تكاليف الإنتاج' }
    }
  })

  // تقرير جدولة الإنتاج
  ipcMain.handle('get-production-schedule-report', async (_, filters: any) => {
    try {
      const result = await productionService.getProductionScheduleReport(filters)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تقرير جدولة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تقرير جدولة الإنتاج' }
    }
  })

  // تقرير جودة الإنتاج
  ipcMain.handle('get-production-quality-report', async (_, filters: any) => {
    try {
      const result = await productionService.getProductionQualityReport(filters)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تقرير جودة الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تقرير جودة الإنتاج' }
    }
  })

  // تقرير أداء العمال في الإنتاج
  ipcMain.handle('get-production-workers-performance-report', async (_, filters: any) => {
    try {
      const result = await productionService.getProductionWorkersPerformanceReport(filters)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تقرير أداء العمال:', error)
      return { success: false, message: 'حدث خطأ في تقرير أداء العمال' }
    }
  })

  // تقرير استهلاك المواد في الإنتاج
  ipcMain.handle('get-production-materials-consumption-report', async (_, filters: any) => {
    try {
      const result = await productionService.getProductionMaterialsConsumptionReport(filters)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تقرير استهلاك المواد:', error)
      return { success: false, message: 'حدث خطأ في تقرير استهلاك المواد' }
    }
  })

  // تقرير ربحية الإنتاج
  ipcMain.handle('get-production-profitability-report', async (_, filters: any) => {
    try {
      const result = await productionService.getProductionProfitabilityReport(filters)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تقرير ربحية الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في تقرير ربحية الإنتاج' }
    }
  })

  // تقارير الدهان
  ipcMain.handle('get-paint-by-customer-report', async (_, filters: any) => {
    try {
      Logger.info('ProductionHandlers', 'طلب تقرير الدهان حسب العميل', filters)
      const result = await productionService.getPaintByCustomerReport(filters)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تقرير الدهان حسب العميل:', error)
      return { success: false, message: 'حدث خطأ في تقرير الدهان حسب العميل' }
    }
  })

  ipcMain.handle('get-paint-by-type-report', async (_, filters: any) => {
    try {
      Logger.info('ProductionHandlers', 'طلب تقرير الدهان حسب النوع', filters)
      const result = await productionService.getPaintByTypeReport(filters)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تقرير الدهان حسب النوع:', error)
      return { success: false, message: 'حدث خطأ في تقرير الدهان حسب النوع' }
    }
  })

  ipcMain.handle('get-monthly-paint-report', async (_, filters: any) => {
    try {
      Logger.info('ProductionHandlers', 'طلب تقرير الدهان الشهري', filters)
      const result = await productionService.getMonthlyPaintReport(filters)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تقرير الدهان الشهري:', error)
      return { success: false, message: 'حدث خطأ في تقرير الدهان الشهري' }
    }
  })

  ipcMain.handle('get-paint-profitability-report', async (_, filters: any) => {
    try {
      Logger.info('ProductionHandlers', 'طلب تقرير ربحية الدهان', filters)
      const result = await productionService.getPaintProfitabilityReport(filters)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تقرير ربحية الدهان:', error)
      return { success: false, message: 'حدث خطأ في تقرير ربحية الدهان' }
    }
  })

  // ==================== فواتير الدهان ====================

  // جلب فواتير الدهان
  ipcMain.handle('get-paint-invoices', async () => {
    try {
      const result = await productionService.getPaintInvoices()
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب فواتير الدهان:', error)
      return { success: false, message: 'حدث خطأ في جلب فواتير الدهان' }
    }
  })

  // إنشاء فاتورة دهان جديدة
  ipcMain.handle('create-paint-invoice', async (_, invoiceData: any) => {
    try {
      const result = await productionService.createPaintInvoice(invoiceData)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إنشاء فاتورة الدهان:', error)
      return { success: false, message: 'حدث خطأ في إنشاء فاتورة الدهان' }
    }
  })

  // تحديث حالة فاتورة الدهان
  ipcMain.handle('update-paint-invoice-status', async (_, invoiceId: number, status: string) => {
    try {
      const result = await productionService.updatePaintInvoiceStatus(invoiceId, status)
      return result
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تحديث حالة فاتورة الدهان:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة فاتورة الدهان' }
    }
  })

  // توليد رقم فاتورة دهان جديد
  ipcMain.handle('generate-paint-invoice-number', async () => {
    try {
      const invoiceNumber = await productionService.generatePaintInvoiceNumber()
      return { success: true, data: { invoice_number: invoiceNumber } }
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في توليد رقم فاتورة الدهان:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم فاتورة الدهان' }
    }
  })

  // ===== أوامر صرف المواد =====

  // إنشاء أمر صرف مواد
  ipcMain.handle('create-material-issue-order', async (_event, orderData: any) => {
    try {
      return await productionService.createMaterialIssueOrder(orderData)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إنشاء أمر صرف المواد:', error)
      return { success: false, message: 'حدث خطأ في إنشاء أمر صرف المواد' }
    }
  })

  // جلب أوامر صرف المواد
  ipcMain.handle('get-material-issue-orders', async (_event, filters: any) => {
    try {
      return await productionService.getMaterialIssueOrders(filters)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب أوامر صرف المواد:', error)
      return { success: false, message: 'حدث خطأ في جلب أوامر صرف المواد' }
    }
  })

  // جلب تفاصيل أمر صرف المواد
  ipcMain.handle('get-material-issue-order-details', async (_event, issueOrderId: number) => {
    try {
      return await productionService.getMaterialIssueOrderDetails(issueOrderId)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب تفاصيل أمر صرف المواد:', error)
      return { success: false, message: 'حدث خطأ في جلب تفاصيل أمر صرف المواد' }
    }
  })

  // ===== تتبع ساعات العمل =====

  // بدء تسجيل ساعات العمل
  ipcMain.handle('start-labor-time-tracking', async (_event, trackingData: any) => {
    try {
      return await productionService.startLaborTimeTracking(trackingData)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في بدء تسجيل ساعات العمل:', error)
      return { success: false, message: 'حدث خطأ في بدء تسجيل ساعات العمل' }
    }
  })

  // إنهاء تسجيل ساعات العمل
  ipcMain.handle('end-labor-time-tracking', async (_event, trackingId: number, notes?: string) => {
    try {
      return await productionService.endLaborTimeTracking(trackingId, notes)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إنهاء تسجيل ساعات العمل:', error)
      return { success: false, message: 'حدث خطأ في إنهاء تسجيل ساعات العمل' }
    }
  })

  // جلب تسجيلات ساعات العمل
  ipcMain.handle('get-labor-time-tracking', async (_event, filters: any) => {
    try {
      return await productionService.getLaborTimeTracking(filters)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب تسجيلات ساعات العمل:', error)
      return { success: false, message: 'حدث خطأ في جلب تسجيلات ساعات العمل' }
    }
  })

  // تنفيذ الإنتاج المحسن
  ipcMain.handle('execute-production-enhanced', async (_event, productionData: any) => {
    try {
      return await productionService.executeProductionEnhanced(productionData)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تنفيذ الإنتاج المحسن:', error)
      return { success: false, message: 'حدث خطأ في تنفيذ الإنتاج المحسن' }
    }
  })

  // إكمال الإنتاج
  ipcMain.handle('complete-production', async (_event, productionOrderId: number, completionData?: any) => {
    try {
      return await productionService.completeProduction(productionOrderId, completionData)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في إكمال الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في إكمال الإنتاج' }
    }
  })

  // تحديث تكلفة المنتج من الإنتاج
  ipcMain.handle('update-product-cost-from-production', async (_event, productId: number) => {
    try {
      return await productionService.updateProductCostFromProduction(productId)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تحديث تكلفة المنتج:', error)
      return { success: false, message: 'حدث خطأ في تحديث تكلفة المنتج' }
    }
  })

  // تحديث تكلفة المنتج من الوصفات
  ipcMain.handle('update-product-cost-from-recipes', async (_event, productId: number) => {
    try {
      return await productionService.updateProductCostFromRecipes(productId)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تحديث تكلفة المنتج من الوصفات:', error)
      return { success: false, message: 'حدث خطأ في تحديث تكلفة المنتج' }
    }
  })

  // تحليل التكاليف الفعلية مقابل المقدرة
  ipcMain.handle('get-production-cost-analysis', async (_event, filters?: any) => {
    try {
      return await productionService.getProductionCostAnalysis(filters)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في تحليل التكاليف:', error)
      return { success: false, message: 'حدث خطأ في تحليل التكاليف' }
    }
  })

  // إعدادات الإنتاج
  ipcMain.handle('get-production-settings', async () => {
    try {
      return await productionService.getProductionSettings()
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في جلب إعدادات الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في جلب إعدادات الإنتاج' }
    }
  })

  ipcMain.handle('save-production-settings', async (_event, settings: any) => {
    try {
      return await productionService.saveProductionSettings(settings)
    } catch (error) {
      Logger.error('ProductionHandlers', 'خطأ في حفظ إعدادات الإنتاج:', error)
      return { success: false, message: 'حدث خطأ في حفظ إعدادات الإنتاج' }
    }
  })



  Logger.info('ProductionHandlers', '✅ تم تسجيل جميع معالجات أوامر الإنتاج')
}
