/**
 * أنواع البيانات الموحدة لنظام الطباعة - ZET.IA
 * يحل تضارب التعريفات بين الخدمات المختلفة
 */

// ===== الأنواع الأساسية =====

export type PageSize = 'A4' | 'A5' | 'A3' | 'Letter' | 'Legal'
export type PageOrientation = 'portrait' | 'landscape'
export type PrintQuality = 'draft' | 'normal' | 'high'
export type LogoPosition = 'top-left' | 'top-center' | 'top-right'
export type LogoSize = 'small' | 'medium' | 'large' | 'extra-large'

// ===== واجهات الطباعة الموحدة =====

export interface PrintMargins {
  top: number
  right: number
  bottom: number
  left: number
}

export interface PrintColors {
  primaryColor: string
  secondaryColor: string
  borderColor: string
  backgroundColor: string
  textColor: string
}

export interface PrintLayout {
  logoPosition: LogoPosition
  logoSize: LogoSize | number
  borderWidth: number
  sectionSpacing: number
  tableWidth: number
}

export interface PrintDisplay {
  showHeader: boolean
  showFooter: boolean
  showLogo: boolean
  showSignature: boolean
  showTerms: boolean
  showQR: boolean
}

export interface PrintContent {
  headerText: string
  footerText: string
  watermark: boolean
  watermarkText: string
  watermarkOpacity: number
}

// ===== الواجهة الرئيسية الموحدة =====

export interface UnifiedPrintOptions {
  // إعدادات الصفحة
  pageSize?: PageSize
  orientation?: PageOrientation
  margins?: PrintMargins
  
  // إعدادات النص
  fontSize?: number
  fontFamily?: string
  headerSize?: number
  lineSpacing?: number
  
  // إعدادات الألوان
  colors?: PrintColors
  primaryColor?: string
  secondaryColor?: string
  borderColor?: string
  backgroundColor?: string
  textColor?: string
  
  // إعدادات التخطيط
  layout?: PrintLayout
  logoPosition?: LogoPosition
  logoSize?: LogoSize | number
  borderWidth?: number
  sectionSpacing?: number
  tableWidth?: number
  
  // إعدادات العرض
  display?: PrintDisplay
  showHeader?: boolean
  showFooter?: boolean
  showLogo?: boolean
  showSignature?: boolean
  showTerms?: boolean
  showQR?: boolean
  
  // إعدادات المحتوى
  content?: PrintContent
  headerText?: string
  footerText?: string
  watermark?: boolean
  watermarkText?: string
  watermarkOpacity?: number
  
  // إعدادات الطباعة
  quality?: PrintQuality
  copies?: number
  preview?: boolean
  previewOnly?: boolean // معاينة فقط بدون فتح نافذة طباعة تلقائياً
  autoClose?: boolean
  autoSave?: boolean
  saveAsPDF?: boolean
  
  // نوع المستند
  type?: 'invoice' | 'receipt' | 'order' | 'report' | 'certificate' | 'statement' | 'image' | 'catalog'
  subType?: 'sales' | 'purchase' | 'payment' | 'work' | 'financial' | 'inventory' | 'production' | 'employees' | 'paint' | 'analysis' | 'item' | 'check' | 'product' | 'invoice' | 'production_order' | 'customer' | 'supplier' | 'fiscal_closing' | string
  
  // callbacks
  onSuccess?: () => void
  onError?: (error: string) => void
  onProgress?: (progress: number) => void
}

// ===== بيانات الطباعة =====

export interface PrintCustomer {
  name: string
  address?: string
  phone?: string
  email?: string
  taxNumber?: string
  department?: string
  orderDate?: string
  deliveryDate?: string
  type?: string
}

export interface PrintCompany {
  name: string
  nameEn?: string
  address: string
  phone: string
  email: string
  website?: string
  logo?: string
  taxNumber?: string
}

export interface PrintItem {
  id?: string | number
  name: string
  description?: string
  quantity: number
  unit?: string
  unitPrice: number
  discount?: number
  tax?: number
  total: number
}

export interface PrintImage {
  id?: string | number
  path: string
  name: string
  description?: string
  width?: number
  height?: number
  category?: string
  size?: number
  uploadDate?: string
  notes?: string
  metadata?: Record<string, any>
}

export interface PrintData {
  // نوع الطباعة
  type?: 'invoice' | 'order' | 'report' | 'image' | 'catalog' | 'customer' | 'production'

  // بيانات أساسية
  id?: string | number
  title?: string
  subtitle?: string
  date?: string
  number?: string
  notes?: string

  // بيانات العميل/المورد
  customer?: PrintCustomer

  // بيانات الشركة
  company?: PrintCompany

  // العناصر/البنود
  items?: PrintItem[]

  // الصور
  images?: PrintImage[]

  // إعدادات الصور
  imageSettings?: {
    layout?: 'single' | 'grid' | 'list' | 'catalog' | 'gallery'
    imagesPerPage?: number
    showMetadata?: boolean
    showThumbnails?: boolean
    imageQuality?: 'low' | 'medium' | 'high' | 'ultra'
    fitToPage?: boolean
  }

  // المجاميع المالية
  subtotal?: number
  discount?: number
  tax?: number
  total?: number
  paid?: number
  remaining?: number

  // المجاميع (للتوافق مع النظام القديم)
  totals?: {
    subtotal?: number
    discount?: number
    tax?: number
    total?: number
    paid?: number
    remaining?: number
  }

  // بيانات إضافية للتقارير
  generatedBy?: string
  period?: string

  // معلومات السياق
  context?: string
  contextName?: string

  // شروط وأحكام الفاتورة
  terms?: string

  // بيانات إضافية
  metadata?: Record<string, any>
}

// ===== نتيجة الطباعة =====

export interface PrintResult {
  success: boolean
  message?: string
  error?: string
  printedAt?: string
  duration?: number
}

// ===== إعدادات مركزية =====

export interface CentralizedPrintSettings extends UnifiedPrintOptions {
  // إعدادات إضافية للتحكم المركزي
  autoSync?: boolean
  lastUpdated?: string
  version?: string
  userId?: string
  isDefault?: boolean
}

// ===== دوال مساعدة =====

export const createDefaultPrintOptions = (): UnifiedPrintOptions => ({
  pageSize: 'A4',
  orientation: 'portrait',
  margins: { top: 20, right: 20, bottom: 20, left: 20 },
  fontSize: 12,
  fontFamily: 'Arial',
  headerSize: 18,
  lineSpacing: 1.5,
  primaryColor: '#1890ff',
  secondaryColor: '#fff3cd',
  borderColor: '#d9d9d9',
  backgroundColor: '#ffffff',
  textColor: '#000000',
  logoPosition: 'top-left',
  logoSize: 'medium',
  borderWidth: 1,
  sectionSpacing: 15,
  tableWidth: 100,
  showHeader: true,
  showFooter: true,
  showLogo: true,
  showSignature: false,
  showTerms: true,
  showQR: false,
  headerText: 'شركة المحاسبة المتقدمة',
  footerText: 'شكراً لتعاملكم معنا',
  watermark: false,
  watermarkText: 'ZET.IA',
  watermarkOpacity: 0.1,
  quality: 'normal',
  copies: 1,
  preview: false,
  autoClose: true,
  autoSave: true,
  saveAsPDF: false
})

export const createDefaultPrintData = (): PrintData => ({
  title: '',
  date: new Date().toISOString(),
  company: {
    name: 'ZET.IA',
    nameEn: 'ZET.IA - Accounting & Production System',
    address: 'فلسطين - غزة',
    phone: '**********',
    email: '<EMAIL>',
    website: 'www.zetia.com',
    logo: 'assets/company-logo.png',
    taxNumber: '*********'
  },
  items: [],
  totals: {
    subtotal: 0,
    discount: 0,
    tax: 0,
    total: 0,
    paid: 0,
    remaining: 0
  }
})

// ===== التوافق مع الأنواع القديمة =====

// للتوافق مع PrintService.ts
export interface LegacyPrintOptions {
  format?: PageSize
  orientation?: PageOrientation
  margins?: {
    top?: number
    bottom?: number
    left?: number
    right?: number
  }
  includeHeader?: boolean
  includeFooter?: boolean
  includeLogo?: boolean
  customCSS?: string
}

// للتوافق مع reports.ts
export interface ReportPrintOptions {
  title?: string
  subtitle?: string
  showLogo?: boolean
  showHeader?: boolean
  showFooter?: boolean
  showSignature?: boolean
  pageSize?: PageSize
  orientation?: PageOrientation
  margins?: {
    top?: number
    bottom?: number
    left?: number
    right?: number
  }
  fontSize?: number
  fontFamily?: string
  primaryColor?: string
  secondaryColor?: string
}

// دوال تحويل للتوافق
export const convertLegacyOptions = (legacy: LegacyPrintOptions): UnifiedPrintOptions => ({
  pageSize: legacy.format || 'A4',
  orientation: legacy.orientation || 'portrait',
  margins: legacy.margins ? {
    top: legacy.margins.top || 20,
    right: 20,
    bottom: legacy.margins.bottom || 20,
    left: legacy.margins.left || 20
  } : { top: 20, right: 20, bottom: 20, left: 20 },
  showHeader: legacy.includeHeader ?? true,
  showFooter: legacy.includeFooter ?? true,
  showLogo: legacy.includeLogo ?? true
})

export const convertReportOptions = (report: ReportPrintOptions): UnifiedPrintOptions => ({
  pageSize: report.pageSize || 'A4',
  orientation: report.orientation || 'portrait',
  margins: report.margins ? {
    top: report.margins.top || 20,
    right: report.margins.right || 20,
    bottom: report.margins.bottom || 20,
    left: report.margins.left || 20
  } : { top: 20, right: 20, bottom: 20, left: 20 },
  fontSize: report.fontSize || 12,
  fontFamily: report.fontFamily || 'Arial',
  primaryColor: report.primaryColor || '#1890ff',
  secondaryColor: report.secondaryColor || '#fff3cd',
  showHeader: report.showHeader ?? true,
  showFooter: report.showFooter ?? true,
  showLogo: report.showLogo ?? true,
  showSignature: report.showSignature ?? false,
  headerText: report.title || ''
  // subtitle يتم التعامل معه في البيانات وليس في الخيارات
})
