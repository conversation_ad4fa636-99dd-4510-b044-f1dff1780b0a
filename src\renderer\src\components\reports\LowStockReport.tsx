import React from 'react';
import { Tag, Typography, Progress } from 'antd';
import UniversalReport from './UniversalReport';
import { DateUtils } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger'

const { Text } = Typography;

const LowStockReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<any> => {
    try {
      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getLowStockReport({
        warehouseId: filters.warehouseId,
        categoryId: filters.categoryId
      });

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const lowStockData = response.data;

      // تحضير أعمدة الجدول
      const columns = [
        {
          title: 'كود الصنف',
          dataIndex: 'item_code',
          key: 'item_code',
          width: 120,
          fixed: 'left' as const,
          render: (text: string) => (
            <Text strong style={{ color: '#1890ff' }}>
              {text}
            </Text>
          )
        },
        {
          title: 'اسم الصنف',
          dataIndex: 'item_name',
          key: 'item_name',
          width: 200,
          fixed: 'left' as const,
          render: (text: string) => (
            <Text strong>{text}</Text>
          )
        },
        {
          title: 'الفئة',
          dataIndex: 'category_name',
          key: 'category_name',
          width: 150,
          render: (text: string) => (
            <Tag color="blue">{text}</Tag>
          )
        },
        {
          title: 'المخزن',
          dataIndex: 'warehouse_name',
          key: 'warehouse_name',
          width: 150,
          render: (text: string) => (
            <Tag color="green">{text}</Tag>
          )
        },
        {
          title: 'الوحدة',
          dataIndex: 'unit',
          key: 'unit',
          width: 80,
          align: 'center' as const
        },
        {
          title: 'الكمية الحالية',
          dataIndex: 'quantity',
          key: 'quantity',
          width: 120,
          align: 'center' as const,
          render: (quantity: number) => (
            <Text strong style={{ color: quantity > 0 ? '#faad14' : '#f5222d' }}>
              {quantity.toLocaleString()}
            </Text>
          )
        },
        {
          title: 'الكمية المحجوزة',
          dataIndex: 'reserved_quantity',
          key: 'reserved_quantity',
          width: 120,
          align: 'center' as const,
          render: (quantity: number) => (
            <Text style={{ color: '#faad14' }}>
              {quantity.toLocaleString()}
            </Text>
          )
        },
        {
          title: 'الكمية المتاحة',
          dataIndex: 'available_quantity',
          key: 'available_quantity',
          width: 120,
          align: 'center' as const,
          render: (quantity: number) => (
            <Text strong style={{ 
              color: quantity > 0 ? '#faad14' : '#f5222d' 
            }}>
              {quantity.toLocaleString()}
            </Text>
          )
        },
        {
          title: 'الحد الأدنى',
          dataIndex: 'min_quantity',
          key: 'min_quantity',
          width: 100,
          align: 'center' as const,
          render: (minQty: number) => (
            <Text strong style={{ color: '#f5222d' }}>
              {minQty.toLocaleString()}
            </Text>
          )
        },
        {
          title: 'نسبة المخزون',
          dataIndex: 'stock_percentage',
          key: 'stock_percentage',
          width: 150,
          align: 'center' as const,
          render: (percentage: number, record: any) => {
            const stockPercentage = record.min_quantity > 0 
              ? Math.round((record.quantity / record.min_quantity) * 100) 
              : 0;
            
            let status: 'exception' | 'normal' | 'success' = 'exception';
            let strokeColor = '#f5222d';
            
            if (stockPercentage > 100) {
              status = 'success';
              strokeColor = '#52c41a';
            } else if (stockPercentage > 50) {
              status = 'normal';
              strokeColor = '#faad14';
            }
            
            return (
              <Progress
                percent={Math.min(stockPercentage, 100)}
                size="small"
                status={status}
                strokeColor={strokeColor}
                format={(percent) => `${stockPercentage}%`}
              />
            );
          }
        },
        {
          title: 'الكمية المطلوبة',
          dataIndex: 'required_quantity',
          key: 'required_quantity',
          width: 120,
          align: 'center' as const,
          render: (required: number, record: any) => {
            const requiredQty = Math.max(0, record.min_quantity - record.quantity);
            return (
              <Text strong style={{ color: '#722ed1' }}>
                {requiredQty.toLocaleString()}
              </Text>
            );
          }
        },
        {
          title: 'قيمة الكمية المطلوبة (₪)',
          dataIndex: 'required_value',
          key: 'required_value',
          width: 160,
          align: 'center' as const,
          render: (value: number, record: any) => {
            const requiredQty = Math.max(0, record.min_quantity - record.quantity);
            const requiredValue = requiredQty * record.cost_price;
            return (
              <Text strong style={{ color: '#722ed1' }}>
                {requiredValue.toFixed(2)}
              </Text>
            );
          }
        },
        {
          title: 'مستوى الخطر',
          dataIndex: 'risk_level',
          key: 'risk_level',
          width: 120,
          align: 'center' as const,
          render: (level: string, record: any) => {
            const stockPercentage = record.min_quantity > 0 
              ? (record.quantity / record.min_quantity) * 100 
              : 0;
            
            let riskLevel = 'حرج';
            let color = 'red';
            
            if (stockPercentage <= 0) {
              riskLevel = 'نفد المخزون';
              color = 'red';
            } else if (stockPercentage <= 25) {
              riskLevel = 'حرج جداً';
              color = 'red';
            } else if (stockPercentage <= 50) {
              riskLevel = 'حرج';
              color = 'orange';
            } else if (stockPercentage <= 75) {
              riskLevel = 'تحذير';
              color = 'yellow';
            } else {
              riskLevel = 'منخفض';
              color = 'green';
            }
            
            return <Tag color={color}>{riskLevel}</Tag>;
          }
        },
        {
          title: 'سعر التكلفة (₪)',
          dataIndex: 'cost_price',
          key: 'cost_price',
          width: 120,
          align: 'center' as const,
          render: (price: number) => (
            <Text>{price ? price.toFixed(2) : '0.00'}</Text>
          )
        },
        {
          title: 'سعر البيع (₪)',
          dataIndex: 'sale_price',
          key: 'sale_price',
          width: 120,
          align: 'center' as const,
          render: (price: number) => (
            <Text style={{ color: '#1890ff' }}>
              {price ? price.toFixed(2) : '0.00'}
            </Text>
          )
        }
      ];

      // حساب الملخص
      const totalItems = lowStockData.length;
      const criticalItems = lowStockData.filter((item: any) => item.quantity <= 0).length;
      const warningItems = lowStockData.filter((item: any) => 
        item.quantity > 0 && item.quantity <= item.min_quantity * 0.5
      ).length;
      
      const totalRequiredQuantity = lowStockData.reduce((sum: number, item: any) => 
        sum + Math.max(0, item.min_quantity - item.quantity), 0
      );
      
      const totalRequiredValue = lowStockData.reduce((sum: number, item: any) => {
        const requiredQty = Math.max(0, item.min_quantity - item.quantity);
        return sum + (requiredQty * item.cost_price);
      }, 0);

      // إضافة مفاتيح فريدة للبيانات
      const dataWithKeys = (Array.isArray(lowStockData) ? lowStockData : []).map((item: any, index: number) => ({
        ...item,
        key: `${item.item_id}-${item.warehouse_name}-${index}`
      }));

      // تحضير عنوان فرعي
      let subtitle = 'تقرير الأصناف التي وصلت للحد الأدنى أو أقل';
      if (filters.warehouseId) {
        const warehouse = await window.electronAPI.getWarehouse(filters.warehouseId);
        subtitle += ` - المخزن: ${warehouse?.name}`;
      }

      return {
        title: 'تقرير الأصناف المنخفضة',
        subtitle,
        columns,
        data: dataWithKeys,
        summary: {
          totalItems,
          criticalItems,
          warningItems,
          totalRequiredQuantity: Math.round(totalRequiredQuantity * 100) / 100,
          totalRequiredValue: Math.round(totalRequiredValue * 100) / 100
        }
      };
    } catch (error) {
      Logger.error('LowStockReport', 'خطأ في إنشاء تقرير الأصناف المنخفضة:', error);
      throw new Error('فشل في إنشاء التقرير');
    }
  };

  return (
    <UniversalReport
      reportType="low_stock"
      title="تقرير الأصناف المنخفضة"
      description="تقرير ينبه للأصناف التي وصلت للحد الأدنى أو أقل مع حساب الكميات والقيم المطلوبة"
      onGenerateReport={generateReport}
      showDateRange={false}
      showWarehouseFilter={true}
      showCategoryFilter={true}
      showItemFilter={false}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="low_stock_report"
      defaultFilters={{
        sortBy: 'stock_percentage',
        sortOrder: 'asc'
      }}
    />
  );
};

export default LowStockReport;
