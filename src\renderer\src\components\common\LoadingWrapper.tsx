import React from 'react'
import { <PERSON>, But<PERSON>, Space, Card, Typography } from 'antd'
import { ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons'

const { Text } = Typography

interface LoadingWrapperProps {
  loading: boolean
  error?: string | null
  children: React.ReactNode
  loadingText?: string
  errorTitle?: string
  onRetry?: () => void
  showRetryButton?: boolean
  emptyText?: string
  isEmpty?: boolean
  minHeight?: number | string
}

/**
 * مكون wrapper لمعالجة حالات التحميل والأخطاء
 */
const LoadingWrapper: React.FC<LoadingWrapperProps> = ({
  loading,
  error,
  children,
  loadingText = 'جاري التحميل...',
  errorTitle = 'حدث خطأ',
  onRetry,
  showRetryButton = true,
  emptyText = 'لا توجد بيانات',
  isEmpty = false,
  minHeight = 200
}) => {
  const containerStyle: React.CSSProperties = {
    minHeight,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '20px'
  }

  // حالة التحميل
  if (loading) {
    return (
      <div style={containerStyle}>
        <Spin size="large" tip={loadingText}>
          <div style={{ minHeight: 100 }} />
        </Spin>
      </div>
    )
  }

  // حالة الخطأ
  if (error) {
    return (
      <div style={containerStyle}>
        <Card style={{ textAlign: 'center', maxWidth: 400 }}>
          <Space direction="vertical" size="large">
            <ExclamationCircleOutlined 
              style={{ fontSize: 48, color: '#ff4d4f' }} 
            />
            
            <div>
              <Text strong style={{ fontSize: 16, display: 'block', marginBottom: 8 }}>
                {errorTitle}
              </Text>
              <Text type="secondary">
                {error}
              </Text>
            </div>

            {showRetryButton && onRetry && (
              <Button 
                type="primary" 
                icon={<ReloadOutlined />} 
                onClick={onRetry}
              >
                إعادة المحاولة
              </Button>
            )}
          </Space>
        </Card>
      </div>
    )
  }

  // حالة البيانات الفارغة
  if (isEmpty) {
    return (
      <div style={containerStyle}>
        <Card style={{ textAlign: 'center', maxWidth: 400 }}>
          <Space direction="vertical" size="large">
            <div style={{ fontSize: 48, color: '#d9d9d9' }}>
              📄
            </div>
            
            <div>
              <Text type="secondary" style={{ fontSize: 16 }}>
                {emptyText}
              </Text>
            </div>

            {onRetry && (
              <Button 
                type="default" 
                icon={<ReloadOutlined />} 
                onClick={onRetry}
              >
                تحديث
              </Button>
            )}
          </Space>
        </Card>
      </div>
    )
  }

  // عرض المحتوى العادي
  return <>{children}</>
}

export default LoadingWrapper
