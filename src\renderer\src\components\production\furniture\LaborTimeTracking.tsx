import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Space,
  Tag,
  Card,
  Row,
  Col,
  Statistic,
  DatePicker,
  TimePicker,
  Tooltip
} from 'antd'
import {
  PlusOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  Clock<PERSON>ircleOutlined,
  UserOutlined,
  ToolOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../../utils/logger'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

interface LaborTimeTracking {
  id: number
  production_order_id: number
  production_order_number: string
  worker_id?: number
  worker_name: string
  department_id?: number
  department_name?: string
  start_time: string
  end_time?: string
  actual_hours?: number
  hourly_rate: number
  labor_cost?: number
  task_description?: string
  status: 'active' | 'paused' | 'completed' | 'cancelled'
  notes?: string
  created_at: string
}

interface LaborTimeTrackingProps {
  productionOrderId?: number
}

const LaborTimeTrackingComponent: React.FC<LaborTimeTrackingProps> = ({ productionOrderId }) => {
  const [trackings, setTrackings] = useState<LaborTimeTracking[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [productionOrders, setProductionOrders] = useState<any[]>([])
  const [departments, setDepartments] = useState<any[]>([])
  const [stats, setStats] = useState({
    activeTrackings: 0,
    totalHours: 0,
    totalCost: 0,
    averageHourlyRate: 0
  })

  useEffect(() => {
    loadData()
  }, [productionOrderId])

  const loadData = async () => {
    await Promise.all([
      loadTrackings(),
      loadProductionOrders(),
      loadDepartments()
    ])
  }

  const loadTrackings = async () => {
    setLoading(true)
    try {
      if (!window.electronAPI) {
        Logger.error('LaborTimeTracking', 'window.electronAPI غير متوفر')
        return
      }

      const filters = productionOrderId ? { production_order_id: productionOrderId } : {}
      const result = await window.electronAPI.getLaborTimeTracking(filters)
      
      if (result.success) {
        setTrackings(result.data)
        calculateStats(result.data)
        Logger.info('LaborTimeTracking', 'تم تحميل تسجيلات ساعات العمل بنجاح')
      } else {
        message.error('فشل في تحميل تسجيلات ساعات العمل')
      }
    } catch (error) {
      Logger.error('LaborTimeTracking', 'خطأ في تحميل تسجيلات ساعات العمل:', error)
      message.error('حدث خطأ في تحميل تسجيلات ساعات العمل')
    }
    setLoading(false)
  }

  const loadProductionOrders = async () => {
    try {
      if (!window.electronAPI) return

      const result = await window.electronAPI.getProductionOrders()
      if (result.success) {
        setProductionOrders(result.data)
      }
    } catch (error) {
      Logger.error('LaborTimeTracking', 'خطأ في تحميل أوامر الإنتاج:', error)
    }
  }

  const loadDepartments = async () => {
    try {
      if (!window.electronAPI) return

      const result = await window.electronAPI.getProductionDepartments()
      if (result.success) {
        setDepartments(result.data)
      }
    } catch (error) {
      Logger.error('LaborTimeTracking', 'خطأ في تحميل الأقسام:', error)
    }
  }

  const calculateStats = (data: LaborTimeTracking[]) => {
    const activeTrackings = data.filter(t => t.status === 'active').length
    const completedTrackings = data.filter(t => t.status === 'completed')
    const totalHours = completedTrackings.reduce((sum, t) => sum + (t.actual_hours || 0), 0)
    const totalCost = completedTrackings.reduce((sum, t) => sum + (t.labor_cost || 0), 0)
    const averageHourlyRate = completedTrackings.length > 0 
      ? completedTrackings.reduce((sum, t) => sum + t.hourly_rate, 0) / completedTrackings.length 
      : 0

    setStats({
      activeTrackings,
      totalHours: Math.round(totalHours * 100) / 100,
      totalCost: Math.round(totalCost * 100) / 100,
      averageHourlyRate: Math.round(averageHourlyRate * 100) / 100
    })
  }

  const handleStartTracking = async (values: any) => {
    try {
      if (!window.electronAPI) {
        message.error('النظام غير متوفر')
        return
      }

      const trackingData = {
        production_order_id: values.production_order_id,
        worker_name: values.worker_name,
        department_id: values.department_id,
        hourly_rate: values.hourly_rate,
        task_description: values.task_description
      }

      const result = await window.electronAPI.startLaborTimeTracking(trackingData)
      
      if (result.success) {
        message.success('تم بدء تسجيل ساعات العمل بنجاح')
        setModalVisible(false)
        form.resetFields()
        await loadTrackings()
      } else {
        message.error(result.message || 'فشل في بدء تسجيل ساعات العمل')
      }
    } catch (error) {
      Logger.error('LaborTimeTracking', 'خطأ في بدء تسجيل ساعات العمل:', error)
      message.error('حدث خطأ في بدء تسجيل ساعات العمل')
    }
  }

  const handleEndTracking = async (trackingId: number) => {
    try {
      if (!window.electronAPI) {
        message.error('النظام غير متوفر')
        return
      }

      const result = await window.electronAPI.endLaborTimeTracking(trackingId)
      
      if (result.success) {
        message.success(`تم إنهاء التسجيل بنجاح - الساعات: ${result.data.actual_hours.toFixed(2)} - التكلفة: ₪${result.data.labor_cost.toFixed(2)}`)
        await loadTrackings()
      } else {
        message.error(result.message || 'فشل في إنهاء تسجيل ساعات العمل')
      }
    } catch (error) {
      Logger.error('LaborTimeTracking', 'خطأ في إنهاء تسجيل ساعات العمل:', error)
      message.error('حدث خطأ في إنهاء تسجيل ساعات العمل')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'paused': return 'orange'
      case 'completed': return 'blue'
      case 'cancelled': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط'
      case 'paused': return 'متوقف مؤقتاً'
      case 'completed': return 'مكتمل'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  const formatDuration = (startTime: string, endTime?: string) => {
    const start = dayjs(startTime)
    const end = endTime ? dayjs(endTime) : dayjs()
    const duration = end.diff(start, 'minute')
    const hours = Math.floor(duration / 60)
    const minutes = duration % 60
    return `${hours}:${minutes.toString().padStart(2, '0')}`
  }

  const columns = [
    {
      title: 'أمر الإنتاج',
      dataIndex: 'production_order_number',
      key: 'production_order_number',
      width: 120,
    },
    {
      title: 'العامل',
      dataIndex: 'worker_name',
      key: 'worker_name',
      width: 150,
      render: (text: string) => (
        <Space>
          <UserOutlined />
          {text}
        </Space>
      )
    },
    {
      title: 'القسم',
      dataIndex: 'department_name',
      key: 'department_name',
      width: 120,
    },
    {
      title: 'وقت البداية',
      dataIndex: 'start_time',
      key: 'start_time',
      width: 150,
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm')
    },
    {
      title: 'المدة',
      key: 'duration',
      width: 100,
      render: (record: LaborTimeTracking) => (
        <Space>
          <ClockCircleOutlined />
          {record.actual_hours ? 
            `${record.actual_hours.toFixed(2)} ساعة` : 
            formatDuration(record.start_time, record.end_time)
          }
        </Space>
      )
    },
    {
      title: 'المعدل/ساعة',
      dataIndex: 'hourly_rate',
      key: 'hourly_rate',
      width: 100,
      render: (rate: number) => `₪${rate.toFixed(2)}`
    },
    {
      title: 'التكلفة',
      dataIndex: 'labor_cost',
      key: 'labor_cost',
      width: 100,
      render: (cost?: number) => cost ? `₪${cost.toFixed(2)}` : '-'
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 120,
      render: (record: LaborTimeTracking) => (
        <Space>
          {record.status === 'active' && (
            <Tooltip title="إنهاء التسجيل">
              <Button
                type="primary"
                size="small"
                icon={<StopOutlined />}
                onClick={() => handleEndTracking(record.id)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* الإحصائيات */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="التسجيلات النشطة"
              value={stats.activeTrackings}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الساعات"
              value={stats.totalHours}
              precision={2}
              suffix="ساعة"
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي التكلفة"
              value={stats.totalCost}
              precision={2}
              prefix="₪"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="متوسط المعدل"
              value={stats.averageHourlyRate}
              precision={2}
              suffix="/ساعة"
              prefix="₪"
            />
          </Card>
        </Col>
      </Row>

      {/* شريط الأدوات */}
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            form.resetFields()
            if (productionOrderId) {
              form.setFieldsValue({ production_order_id: productionOrderId })
            }
            setModalVisible(true)
          }}
        >
          بدء تسجيل جديد
        </Button>
        <Button onClick={loadTrackings} loading={loading}>
          تحديث البيانات
        </Button>
      </div>

      {/* جدول التسجيلات */}
      <Table
        columns={columns}
        dataSource={trackings}
        rowKey="id"
        loading={loading}
        scroll={{ x: 1000 }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `إجمالي ${total} تسجيل`
        }}
      />

      {/* نافذة بدء تسجيل جديد */}
      <Modal
        title="بدء تسجيل ساعات العمل"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleStartTracking}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="production_order_id"
                label="أمر الإنتاج"
                rules={[{ required: true, message: 'يرجى اختيار أمر الإنتاج' }]}
              >
                <Select
                  placeholder="اختر أمر الإنتاج"
                  showSearch
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                  }
                  disabled={!!productionOrderId}
                >
                  {productionOrders.map(order => (
                    <Option key={order.id} value={order.id}>
                      {order.order_number} - {order.item_name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="worker_name"
                label="اسم العامل"
                rules={[{ required: true, message: 'يرجى إدخال اسم العامل' }]}
              >
                <Input placeholder="اسم العامل" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="department_id"
                label="القسم"
              >
                <Select placeholder="اختر القسم">
                  {departments.map(dept => (
                    <Option key={dept.id} value={dept.id}>
                      {dept.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="hourly_rate"
                label="المعدل بالساعة (₪)"
                rules={[{ required: true, message: 'يرجى إدخال المعدل بالساعة' }]}
              >
                <InputNumber
                  min={0}
                  step={0.5}
                  style={{ width: '100%' }}
                  placeholder="المعدل بالساعة"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="task_description"
            label="وصف المهمة"
          >
            <TextArea rows={3} placeholder="وصف المهمة أو العمل المطلوب..." />
          </Form.Item>

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                بدء التسجيل
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  )
}

export default LaborTimeTrackingComponent
