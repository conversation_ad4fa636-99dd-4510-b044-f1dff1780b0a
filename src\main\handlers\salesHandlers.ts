import { ipcMain } from 'electron'
import { SalesService, CustomerService } from '../services'
import { Logger } from '../utils/logger'

let salesService: SalesService
let customerService: CustomerService

export function setSalesServices(sales: SalesService, customer: CustomerService) {
  salesService = sales
  customerService = customer
}

function registerSalesHandlers(): void {
  // العملاء
  ipcMain.handle('get-customers', async () => {
    try {
      const customers = await customerService.getCustomers()
      return { success: true, data: customers }
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في جلب العملاء:', error)
      return { success: false, message: 'حدث خطأ في جلب العملاء' }
    }
  })

  ipcMain.handle('create-customer', async (_, customerData: any) => {
    try {
      return await customerService.createCustomer(customerData)
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في إنشاء العميل:', error)
      return { success: false, message: 'حدث خطأ في إنشاء العميل' }
    }
  })

  ipcMain.handle('update-customer', async (_, customerData: any) => {
    try {
      return await customerService.updateCustomer(customerData)
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في تحديث العميل:', error)
      return { success: false, message: 'حدث خطأ في تحديث العميل' }
    }
  })

  ipcMain.handle('delete-customer', async (_, customerId: number) => {
    try {
      return await customerService.deleteCustomer(customerId)
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في حذف العميل:', error)
      return { success: false, message: 'حدث خطأ في حذف العميل' }
    }
  })

  ipcMain.handle('generate-customer-code', async () => {
    try {
      const code = await customerService.generateCustomerCode()
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في توليد كود العميل:', error)
      return { success: false, message: 'حدث خطأ في توليد كود العميل' }
    }
  })

  // فواتير البيع
  ipcMain.handle('get-sales-invoices', async () => {
    try {
      const invoices = await salesService.getSalesInvoices()
      return { success: true, data: invoices }
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في جلب فواتير البيع:', error)
      return { success: false, message: 'حدث خطأ في جلب فواتير البيع' }
    }
  })

  ipcMain.handle('create-sales-invoice', async (_, invoiceData: any) => {
    try {
      return await salesService.createSalesInvoice(invoiceData)
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في إنشاء فاتورة البيع:', error)
      return { success: false, message: 'حدث خطأ في إنشاء فاتورة البيع' }
    }
  })

  ipcMain.handle('get-sales-invoice-items', async (_, invoiceId: number) => {
    try {
      const items = await salesService.getSalesInvoiceItems(invoiceId)
      return { success: true, data: items }
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في جلب تفاصيل فاتورة البيع:', error)
      return { success: false, message: 'حدث خطأ في جلب تفاصيل فاتورة البيع' }
    }
  })

  ipcMain.handle('generate-sales-invoice-number', async () => {
    try {
      const invoiceNumber = await salesService.generateInvoiceNumber()
      return { success: true, data: { invoiceNumber } }
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في توليد رقم فاتورة البيع:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم فاتورة البيع' }
    }
  })

  // أوامر البيع
  ipcMain.handle('get-sales-orders', async () => {
    try {
      const orders = await salesService.getSalesOrders()
      return { success: true, data: orders }
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في جلب أوامر البيع:', error)
      return { success: false, message: 'حدث خطأ في جلب أوامر البيع' }
    }
  })

  ipcMain.handle('get-available-sales-orders', async (_, customerId?: number) => {
    try {
      const orders = await salesService.getAvailableSalesOrders(customerId)
      return { success: true, data: orders }
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في جلب أوامر البيع المتاحة:', error)
      return { success: false, message: 'حدث خطأ في جلب أوامر البيع المتاحة' }
    }
  })

  ipcMain.handle('generate-sales-order-number', async () => {
    try {
      const orderNumber = await salesService.generateOrderNumber()
      return { success: true, data: { orderNumber } }
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في توليد رقم أمر البيع:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم أمر البيع' }
    }
  })

  ipcMain.handle('create-sales-order', async (_, orderData: any) => {
    try {
      return await salesService.createSalesOrder(orderData)
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في إنشاء أمر البيع:', error)
      return { success: false, message: 'حدث خطأ في إنشاء أمر البيع' }
    }
  })

  ipcMain.handle('update-sales-order', async (_, orderId: number, orderData: any) => {
    try {
      return await salesService.updateSalesOrder(orderId, orderData)
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في تحديث أمر البيع:', error)
      return { success: false, message: 'حدث خطأ في تحديث أمر البيع' }
    }
  })

  ipcMain.handle('delete-sales-order', async (_, orderId: number) => {
    try {
      return await salesService.deleteSalesOrder(orderId)
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في حذف أمر البيع:', error)
      return { success: false, message: 'حدث خطأ في حذف أمر البيع' }
    }
  })

  ipcMain.handle('get-sales-order-items', async (_, orderId: number) => {
    try {
      const items = await salesService.getSalesOrderItems(orderId)
      return { success: true, data: items }
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في جلب تفاصيل أمر البيع:', error)
      return { success: false, message: 'حدث خطأ في جلب تفاصيل أمر البيع' }
    }
  })

  // فواتير البيع - المعالجات المفقودة
  ipcMain.handle('update-sales-invoice', async (_, invoiceId: number, invoiceData: any) => {
    try {
      return await salesService.updateSalesInvoice(invoiceId, invoiceData)
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في تحديث فاتورة البيع:', error)
      return { success: false, message: 'حدث خطأ في تحديث فاتورة البيع' }
    }
  })

  ipcMain.handle('delete-sales-invoice', async (_, invoiceId: number) => {
    try {
      return await salesService.deleteSalesInvoice(invoiceId)
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في حذف فاتورة البيع:', error)
      return { success: false, message: 'حدث خطأ في حذف فاتورة البيع' }
    }
  })

  ipcMain.handle('update-sales-invoice-status', async (_, invoiceId: number, status: string) => {
    try {
      return await salesService.updateSalesInvoiceStatus(invoiceId, status)
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في تحديث حالة فاتورة البيع:', error)
      return { success: false, message: 'حدث خطأ في تحديث حالة فاتورة البيع' }
    }
  })

  // تقارير المبيعات
  ipcMain.handle('get-sales-by-customer-report', async (_, filters: any) => {
    try {
      Logger.info('SalesHandlers', 'طلب تقرير المبيعات حسب العميل', filters)
      const result = await salesService.getSalesByCustomerReport(filters)
      return result
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في تقرير المبيعات حسب العميل:', error)
      return { success: false, message: 'حدث خطأ في تقرير المبيعات حسب العميل' }
    }
  })

  ipcMain.handle('get-sales-by-product-report', async (_, filters: any) => {
    try {
      Logger.info('SalesHandlers', 'طلب تقرير المبيعات حسب المنتج', filters)
      const result = await salesService.getSalesByProductReport(filters)
      return result
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في تقرير المبيعات حسب المنتج:', error)
      return { success: false, message: 'حدث خطأ في تقرير المبيعات حسب المنتج' }
    }
  })

  ipcMain.handle('get-monthly-sales-report', async (_, filters: any) => {
    try {
      Logger.info('SalesHandlers', 'طلب تقرير المبيعات الشهري', filters)
      const result = await salesService.getMonthlySalesReport(filters)
      return result
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في تقرير المبيعات الشهري:', error)
      return { success: false, message: 'حدث خطأ في تقرير المبيعات الشهري' }
    }
  })

  ipcMain.handle('get-profitability-report', async (_, filters: any) => {
    try {
      Logger.info('SalesHandlers', 'طلب تقرير الربحية', filters)
      const result = await salesService.getProfitabilityReport(filters)
      return result
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في تقرير الربحية:', error)
      return { success: false, message: 'حدث خطأ في تقرير الربحية' }
    }
  })

  // تصدير العملاء
  ipcMain.handle('export-customers', async (_, _format: 'excel' | 'csv') => {
    try {
      const customers = await customerService.getCustomers()
      return { success: true, data: customers }
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في تصدير العملاء:', error)
      return { success: false, message: 'حدث خطأ في تصدير العملاء' }
    }
  })

  // تصدير فواتير المبيعات
  ipcMain.handle('export-sales-invoices', async (_, _format: 'excel' | 'csv') => {
    try {
      const invoices = await salesService.getSalesInvoices()
      return { success: true, data: invoices }
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في تصدير فواتير المبيعات:', error)
      return { success: false, message: 'حدث خطأ في تصدير فواتير المبيعات' }
    }
  })

  // تصدير أوامر المبيعات
  ipcMain.handle('export-sales-orders', async (_, _format: 'excel' | 'csv') => {
    try {
      const orders = await salesService.getSalesOrders()
      return { success: true, data: orders }
    } catch (error) {
      Logger.error('SalesHandlers', 'خطأ في تصدير أوامر المبيعات:', error)
      return { success: false, message: 'حدث خطأ في تصدير أوامر المبيعات' }
    }
  })

  // تم إزالة معالجات البيانات التجريبية - يجب استخدام البيانات الحقيقية فقط

}

export { registerSalesHandlers }
