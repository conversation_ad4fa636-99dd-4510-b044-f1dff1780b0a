import React, { useState, useEffect, useCallback } from 'react'
import {
  Layout, Menu, Row, Col, Card, Statistic, Typography, Space, Progress, List, Tag,
  Avatar, Dropdown, Button, Badge, notification, Spin, message
} from 'antd'
import { logger as Logger } from './../utils/logger'
import {
  DollarOutlined,
  ShoppingCartOutlined,
  ShopOutlined,
  InboxOutlined,
  RiseOutlined,
  FallOutlined,
  UserOutlined,
  ToolOutlined,
  DashboardOutlined,
  TeamOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BankOutlined,
  FileTextOutlined,
  BugOutlined,
  PictureOutlined,
  PrinterOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { User } from '../types/global'
import AnimatedLogo from './common/AnimatedLogo'
import { designSystem } from '../styles/designSystem'
// import { DateUtils } from '../utils/dateConfig' // غير مستخدم حالياً
import EnhancedUserManagement from './EnhancedUserManagement'
import EnhancedRoleManagement from './EnhancedRoleManagement'
import ErrorBoundary from './common/ErrorBoundary'
import BackupManagement from './BackupManagement'
// import OrganizedPrintSettings from './Settings/OrganizedPrintSettings' // تم حذف نظام الطباعة
// import NewPrintSystemTest from './test/NewPrintSystemTest' // تم حذف نظام الطباعة
import ThemeToggle from './common/ThemeToggle'
import EnhancedTableDemo from './common/EnhancedTableDemo'

// import PrintSystemTest from './common/PrintSystemTest' // تم تعطيل الاستيراد مؤقتاً
import PrintSettings from './Settings/PrintSettings'
import InventoryManagement from './inventory/InventoryManagement'
import PurchasesBySupplierReport from './reports/PurchasesBySupplierReport'
import PurchasesByItemReport from './reports/PurchasesByItemReport'
import SupplierPayablesReport from './reports/SupplierPayablesReport'
import PurchaseAnalysisReport from './reports/PurchaseAnalysisReport'
import CostAnalysisReport from './reports/CostAnalysisReport'
import PurchaseManagement from './purchases/PurchaseManagement'
import SalesManagement from './sales/SalesManagement'
import SalesByCustomerReport from './reports/SalesByCustomerReport'
import SalesByProductReport from './reports/SalesByProductReport'
import MonthlySalesReport from './reports/MonthlySalesReport'
import SalesReturnsReport from './reports/SalesReturnsReport'
import SalesByRegionReport from './reports/SalesByRegionReport'
import TopProfitableCustomersReport from './reports/TopProfitableCustomersReport'
import SupplierPriceComparisonReport from './reports/SupplierPriceComparisonReport'
import SupplierQualityReport from './reports/SupplierQualityReport'
import BalanceSheetReport from './reports/BalanceSheetReport'
import IncomeStatementReport from './reports/IncomeStatementReport'
import CashFlowReport from './reports/CashFlowReport'
import BankReconciliationReport from './reports/BankReconciliationReport'
import ProductionManagement from './production/ProductionManagement'
import FinanceManagement from './finance/FinanceManagement'
import { FiscalPeriodManager } from './FiscalPeriods'
import EmployeeManagement from './employees/EmployeeManagement'
import DepartmentManagement from './employees/DepartmentManagement'
import AttendanceManagement from './employees/AttendanceManagement'
import PayrollManagement from './employees/PayrollManagement'

import LeaveManagement from './employees/LeaveManagement'
import EmployeeReports from './employees/EmployeeReports'
import EmployeeAttendanceReport from './reports/EmployeeAttendanceReport'
import EmployeePayrollReport from './reports/EmployeePayrollReport'
import EmployeeLeavesReport from './reports/EmployeeLeavesReport'
import EmployeePerformanceReport from './reports/EmployeePerformanceReport'
import FingerprintDeviceManagement from './employees/FingerprintDeviceManagement'
import SystemSettings from './SystemSettings'
import NotificationCenter from './NotificationCenter'
import SimpleHelpButton from './tutorial/SimpleHelpButton'
import { comprehensiveAudioSystem } from '../utils/comprehensiveAudioSystem'

// ملاحظة: تم حذف نظام طباعة الصور القديم - استخدم SimpleImageManager بدلاً منه
// import { SimplePrintService as PrintService } from '../services/SimplePrintService' // تم حذف نظام الطباعة
import QuickShortcuts from './common/QuickShortcuts'
import QuickAttendance from './common/QuickAttendance'
import DailySummary from './common/DailySummary'
import QuickReports from './common/QuickReports'
import QuickSyncControl from './common/QuickSyncControl'
import LicenseWarning from './activation/LicenseWarning'
import LicenseStatusDisplay from './common/LicenseStatusDisplay'

// أدوات التشخيص
import QuickShortcutsTest from './test/QuickShortcutsTest'

// import SimplePrintTest from './common/SimplePrintTest' // تم حذف نظام الطباعة
// import MigrationTestButton from './common/MigrationTestButton' // محذوف - تم الانتقال للنظام الجديد


const { Header, Sider, Content } = Layout
const { Title, Text } = Typography

const StyledLayout = styled(Layout)`
  min-height: 100vh;
  direction: rtl;
  background: ${designSystem.colors.gradients.background};
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;

  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
`

const MainContentLayout = styled(Layout)<{ $collapsed: boolean }>`
  margin-right: ${props => props.$collapsed ? '80px' : '200px'};
  transition: margin-right 0.3s ease-in-out;
  min-height: 100vh;
`

const StyledHeader = styled(Header)`
  background: var(--header-bg-color, #ffffff) !important;
  padding: 0 12px;
  height: 32px !important;
  line-height: 32px !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid var(--border-color, #f0f0f0);

  .sidebar-toggle-btn {
    height: 24px !important;
    width: 24px !important;
    min-width: 24px !important;
    padding: 0 !important;
    display: flex;
    align-items: center;
    justify-content: center;

    .anticon {
      font-size: 12px !important;
    }

    &:hover {
      background-color: var(--primary-color, #1890ff) !important;
      color: white !important;
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }
  }
`

const StyledSider = styled(Sider)`
  background: ${designSystem.colors.background.glass} !important;
  backdrop-filter: blur(20px) !important;
  border-left: 2px solid ${designSystem.colors.border.glass} !important;
  box-shadow: ${designSystem.shadows.xl} !important;
  transition: all 0.3s ease-in-out !important;
  height: 100vh !important;
  position: fixed !important;
  right: 0 !important;
  top: 0 !important;
  z-index: 1000 !important;
  overflow: hidden !important;

  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: visible; /* السماح بظهور اللوجو */
  }

  .ant-menu {
    flex: 1;
    border-right: none;
    background: var(--sidebar-bg-color, #001529) !important;
    overflow: hidden !important;
    height: calc(100vh - 160px); /* اطرح ارتفاع اللوجو المحدث (160px) */
    margin-top: 0; /* إزالة أي هامش علوي */

    /* منع التمرير تماماً */
    overflow-y: hidden !important;
    overflow-x: hidden !important;

    /* إخفاء أي محتوى زائد */
    .ant-menu-item-group-list {
      overflow: hidden !important;
    }

    .ant-menu-submenu-popup {
      overflow: hidden !important;
    }
  }

  .ant-menu-dark {
    background: var(--sidebar-bg-color, #001529) !important;
  }

  .ant-menu-dark .ant-menu-item-selected {
    background-color: var(--menu-selected-color, var(--primary-color, #1890ff)) !important;
  }

  .ant-menu-dark .ant-menu-submenu-title:hover,
  .ant-menu-dark .ant-menu-item:hover {
    background-color: var(--menu-hover-color, #112545) !important;
  }

  /* تثبيت الأزرار الرئيسية وعدم إزاحتها */
  .ant-menu-vertical {
    position: relative;
  }

  /* تحسين مظهر القائمة المطوية */
  &.ant-layout-sider-collapsed {
    .ant-menu {
      height: calc(100vh - 140px) !important; /* ارتفاع أقل في الحالة المطوية */
    }

    .ant-menu-item {
      padding: 0 calc(50% - 14px) !important;
      text-align: center;
    }

    .ant-menu-submenu {
      text-align: center;
    }

    .ant-menu-item-icon,
    .ant-menu-submenu-title .ant-menu-item-icon {
      margin-inline-end: 0 !important;
    }
  }

  .ant-menu-vertical .ant-menu-submenu {
    position: relative;
  }

  .ant-menu-vertical .ant-menu-submenu-title {
    position: relative;
    z-index: 1;
  }

  /* ضمان عدم تأثير القوائم المنسدلة على تخطيط القائمة الرئيسية */
  .ant-menu-vertical .ant-menu-submenu-popup {
    position: absolute !important;
    z-index: 1050 !important;
    background: var(--sidebar-bg-color, #001529) !important;
  }

  /* تطبيق الألوان المخصصة على القوائم الفرعية */
  .ant-menu-vertical .ant-menu-submenu-popup .ant-menu-item-selected {
    background-color: var(--menu-selected-color, var(--primary-color, #1890ff)) !important;
  }

  .ant-menu-vertical .ant-menu-submenu-popup .ant-menu-item:hover {
    background-color: var(--menu-hover-color, #112545) !important;
  }
`

const StyledContent = styled(Content)`
  margin: 24px;
  padding: 24px;
  background: var(--content-bg-color, #f0f2f5) !important;
  border-radius: var(--border-radius, 8px);
  min-height: calc(100vh - 112px);
`

const StyledCard = styled(Card)`
  border-radius: ${designSystem.borderRadius.lg};
  box-shadow: ${designSystem.shadows.md};
  border: 1px solid ${designSystem.colors.border.glass};
  backdrop-filter: blur(10px);
  background: ${designSystem.colors.background.glass};
  transition: ${designSystem.transitions.smooth};

  &:hover {
    transform: translateY(-5px);
    box-shadow: ${designSystem.shadows.lg};
  }

  .ant-card-body {
    padding: ${designSystem.spacing.lg};
    background: transparent;
  }
`

const StatCard = styled(StyledCard)`
  background: ${designSystem.colors.background.glass};
  border: 2px solid ${designSystem.colors.border.glass};

  .ant-statistic-title {
    font-size: ${designSystem.fontSize.base};
    margin-bottom: ${designSystem.spacing.sm};
    font-weight: ${designSystem.fontWeight.medium};
    color: ${designSystem.colors.text.secondary};
  }

  .ant-statistic-content {
    font-size: ${designSystem.fontSize['2xl']};
    font-weight: ${designSystem.fontWeight.bold};
    background: ${designSystem.colors.gradients.text};
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  &:hover {
    border-color: ${designSystem.colors.primary};
    box-shadow: ${designSystem.shadows.focus};
  }
`

const WelcomeCard = styled(StyledCard)`
  background: ${designSystem.colors.gradients.primary};
  color: white;
  border-radius: ${designSystem.borderRadius['2xl']};
  box-shadow: ${designSystem.shadows.lg};
  border: 2px solid ${designSystem.colors.border.glass};
  backdrop-filter: blur(20px);
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: ${designSystem.colors.gradients.shimmer};
    background-size: 200% 100%;
    animation: shimmer 2s linear infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  .ant-card-body {
    padding: 20px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 80px;
    background: transparent;
  }

  h2, p {
    color: white !important;
    margin: 0 !important;
  }

  .welcome-content {
    display: flex;
    align-items: center;
    gap: 24px;
    flex: 1;
  }

  .welcome-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .welcome-tags {
    display: flex;
    gap: 8px;
    align-items: center;
  }
`

const DeveloperInfoBar = styled.div`
  background: linear-gradient(90deg, #f0f8ff 0%, #e6f3ff 100%);
  border-bottom: 1px solid #d6e4ff;
  padding: 8px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #0078D4;

  .developer-info {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .app-name {
    font-weight: 600;
    font-size: 14px;
  }
`

const LogoContainer = styled.div`
  height: 160px;
  min-height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  color: white;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 2px solid ${designSystem.colors.border.glass};
  transition: all 0.3s ease-in-out;
  padding: ${designSystem.spacing.xl};
  overflow: visible; /* السماح بظهور اللوجو كاملاً */
  white-space: nowrap;
  flex-shrink: 0; /* منع تقليص اللوجو */
  position: relative;
  z-index: 1001;

  /* تحسين النص في الحالة المطوية */
  &.collapsed {
    height: 140px;
    min-height: 140px;
    font-size: 14px;
    font-weight: 900;
    padding: ${designSystem.spacing.lg};
  }
`

interface DashboardProps {
  user: User
  onLogout: () => void
  authToken: string | null
}

const Dashboard: React.FC<DashboardProps> = ({ user, onLogout }) => {
  const [collapsed, setCollapsed] = useState(false)
  const [selectedKey, setSelectedKey] = useState('dashboard')
  const [settings, setSettings] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [openKeys, setOpenKeys] = useState<string[]>([])
  const [notificationCenterVisible, setNotificationCenterVisible] = useState(false)
  const [unreadNotificationsCount, setUnreadNotificationsCount] = useState(0)

  // حالات المكونات الجديدة
  const [quickCheckinVisible, setQuickCheckinVisible] = useState(false)
  const [quickCheckoutVisible, setQuickCheckoutVisible] = useState(false)
  const [dailySummaryVisible, setDailySummaryVisible] = useState(false)
  const [quickReportsVisible, setQuickReportsVisible] = useState(false)

  // النظام التعليمي البسيط مدمج في زر المساعدة

  // تحميل معلومات الشركة للطباعة
  const loadCompanyInfo = useCallback(async () => {
    try {
      // تم حذف نظام الطباعة
      // await PrintService.loadCompanyInfo()
      Logger.info('Dashboard', '✅ تم تخطي تحميل معلومات الشركة - نظام الطباعة محذوف')
    } catch (error) {
      Logger.error('Dashboard', '❌ خطأ في تحميل معلومات الشركة', error as Error)
    }
  }, [])

  // تحميل الإعدادات
  const loadSettings = useCallback(async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getSettings()
        if (response.success && Array.isArray(response.data)) {
          setSettings(response.data)

          // تطبيق إعداد طي القائمة الجانبية
          const sidebarSetting = response.data.find((setting: any) => setting.key === 'sidebar_collapsed')
          if (sidebarSetting) {
            const isCollapsed = sidebarSetting.value === 'true'
            setCollapsed(isCollapsed)
            Logger.info('Dashboard', 'تم تطبيق إعداد القائمة الجانبية:', isCollapsed)
          }

          // إعادة تحميل معلومات الشركة للطباعة
          await loadCompanyInfo()
        } else {
          Logger.error('Dashboard', 'خطأ في تحميل الإعدادات', new Error(response.message))
          // استخدام إعدادات افتراضية
          setSettings([
            { key: 'company_name', value: 'ZET.IA' },
            { key: 'currency_symbol', value: '₪' }
          ])
        }
      } else {
        // إعدادات افتراضية للتطوير
        setSettings([
          { key: 'company_name', value: 'ZET.IA' },
          { key: 'currency_symbol', value: '₪' }
        ])
      }
    } catch (error) {
      Logger.error('Dashboard', 'خطأ في تحميل الإعدادات', error as Error)
      // استخدام إعدادات افتراضية في حالة الخطأ
      setSettings([
        { key: 'company_name', value: 'ZET.IA' },
        { key: 'currency_symbol', value: '₪' }
      ])
      notification.error({
        message: 'خطأ',
        description: 'فشل في تحميل إعدادات النظام - تم استخدام الإعدادات الافتراضية'
      })
    } finally {
      setLoading(false)
    }
  }, [loadCompanyInfo])

  // تحميل عدد الإشعارات غير المقروءة
  const loadUnreadNotificationsCount = useCallback(async () => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.getNotifications({
          user_id: user.id,
          is_read: false,
          limit: 1000 // للحصول على العدد الكامل
        })
        if (result.success) {
          setUnreadNotificationsCount(result.data?.length || 0)
        }
      } else {
        // بيانات وهمية للتطوير
        setUnreadNotificationsCount(5)
      }
    } catch (error) {
      Logger.error('Dashboard', 'خطأ في تحميل عدد الإشعارات', error as Error)
    }
  }, [user.id])

  // تهيئة النظام الصوتي الشامل
  const initializeAudioSystem = useCallback(() => {
    try {
      // تهيئة النظام الصوتي الشامل
      comprehensiveAudioSystem.attachToElements()

      Logger.info('Dashboard', '✅ تم تهيئة النظام الصوتي الشامل بنجاح')
    } catch (error) {
      Logger.error('Dashboard', '❌ خطأ في تهيئة النظام الصوتي', error as Error)
    }
  }, [])



  useEffect(() => {
    loadSettings()
    loadUnreadNotificationsCount()
    initializeAudioSystem()
    loadCompanyInfo()

    // تحديث عدد الإشعارات كل دقيقة
    const interval = setInterval(loadUnreadNotificationsCount, 60000)
    return () => clearInterval(interval)
  }, [loadSettings, loadUnreadNotificationsCount, initializeAudioSystem, loadCompanyInfo])

  // وظائف بسيطة للنظام الجديد
  const handlePlaySound = (type: 'click' | 'success' | 'error' | 'warning') => {
    comprehensiveAudioSystem.playSound(type)
  }

  // Initialize open keys based on current selected key to ensure proper menu state
  // Only run when selectedKey changes, not when openKeys changes to avoid conflicts
  useEffect(() => {
    // Check if auto close menus is enabled
    const autoCloseMenus = settings.find(s => s.key === 'auto_close_menus')?.value === 'true'

    // If auto close is enabled and sidebar is not collapsed, don't auto-open menus
    if (autoCloseMenus && !collapsed) {
      return
    }

    const menuHierarchy: { [key: string]: string[] } = {
      // Inventory management
      'warehouses': ['inventory-management'],
      'categories': ['inventory-management'],
      'items': ['inventory-management'],
      'barcodes': ['inventory-management'],
      'inventory': ['inventory-management'],
      'inventory-movements': ['inventory-management'],
      'inventory-detailed-report': ['inventory-management', 'inventory-reports'],
      'inventory-movement-report': ['inventory-management', 'inventory-reports'],
      'inventory-audit-report': ['inventory-management', 'inventory-reports'],
      'material-consumption-report': ['inventory-management', 'inventory-reports'],
      'low-stock-report': ['inventory-management', 'inventory-reports'],

      // Sales management
      'customers': ['sales'],
      'sales-orders': ['sales'],
      'sales-invoices': ['sales'],
      'sales-by-customer-report': ['sales', 'sales-reports'],
      'sales-by-product-report': ['sales', 'sales-reports'],
      'sales-performance-report': ['sales', 'sales-reports'],
      'commission-report': ['sales', 'sales-reports'],
      'customer-receivables-report': ['sales', 'sales-reports'],

      // Purchases management
      'suppliers': ['purchases-management'],
      'purchase-orders': ['purchases-management'],
      'purchase-invoices': ['purchases-management'],
      'supplier-payments': ['purchases-management'],
      'purchases-by-supplier-report': ['purchases-management', 'purchases-reports'],
      'purchases-by-item-report': ['purchases-management', 'purchases-reports'],
      'supplier-payables-report': ['purchases-management', 'purchases-reports'],
      'purchase-analysis-report': ['purchases-management', 'purchases-reports'],
      'cost-analysis-report': ['purchases-management', 'purchases-reports'],

      // Production management
      'furniture-production': ['production'],
      'paint-production': ['production'],
      'production-orders': ['production'],
      'furniture-production-report': ['production', 'production-reports'],
      'paint-production-report': ['production', 'production-reports'],
      'quality-control-report': ['production', 'production-reports'],
      'production-efficiency-report': ['production', 'production-reports'],
      'production-cost-report': ['production', 'production-reports'],
      'raw-material-usage-report': ['production', 'production-reports'],

      // Finance management
      'banks': ['finance'],
      'checks': ['finance'],
      'check-transfers': ['finance'],
      'company-checks': ['finance'],
      'vouchers': ['finance'],
      'fiscal-periods': ['finance'],
      'balance-sheet-report': ['finance', 'finance-reports'],
      'income-statement-report': ['finance', 'finance-reports'],
      'cash-flow-report': ['finance', 'finance-reports'],
      'receivables-report': ['finance', 'finance-reports'],
      'detailed-costs-report': ['finance', 'finance-reports'],
      'profitability-analysis-report': ['finance', 'finance-reports'],
      'cost-centers-report': ['finance', 'finance-reports'],

      // Employee management
      'employees-list': ['employees'],
      'departments': ['employees'],
      'attendance': ['employees'],
      'payroll': ['employees'],
      'leaves': ['employees'],
      'fingerprint-devices': ['employees'],
      'attendance-report': ['employees', 'employees-reports'],
      'payroll-report': ['employees', 'employees-reports'],
      'leaves-report': ['employees', 'employees-reports'],
      'performance-report': ['employees', 'employees-reports'],
      'overtime-report': ['employees', 'employees-reports'],

      // Settings
      'roles': ['settings'],
      'system-settings': ['settings'],
      'backup': ['settings'],

      // Debug Tools
      'image-debugger': ['debug-tools'],
      'image-test': ['debug-tools'],
      'image-monitor': ['debug-tools'],
      'print-system-test': ['debug-tools'],
      'new-print-system-test': ['debug-tools'],
      'migration-test': ['debug-tools']
    }

    const requiredOpenKeys = menuHierarchy[selectedKey] || []
    if (requiredOpenKeys.length > 0) {
      setOpenKeys(prev => {
        // تجنب التحديث إذا كانت المفاتيح المطلوبة موجودة بالفعل
        const hasAllKeys = requiredOpenKeys.every(key => prev.includes(key))
        if (hasAllKeys) {
          return prev
        }
        return [...new Set([...prev, ...requiredOpenKeys])]
      })
    }
  }, [selectedKey, collapsed, settings]) // إزالة openKeys من dependency array لتجنب infinite loop

  // Handle menu open/close
  const handleMenuOpenChange = (keys: string[]) => {
    // تشغيل صوت عند فتح أو إغلاق القوائم الفرعية
    if (keys.length !== openKeys.length) {
      handlePlaySound('click')
    }
    setOpenKeys(keys)
  }



  // Handle shortcut click - wrapper for menu click
  const handleShortcutClick = (key: string) => {
    handleMenuClick({ key })
  }

  // Handle menu click - ensure parent menu stays open when clicking child items
  const handleMenuClick = ({ key }: { key: string }) => {
    // Define parent menu items that should not be clickable (only for opening/closing)
    const parentMenuKeys = [
      'inventory-management',
      'sales',
      'purchases-management',
      'production',
      'finance',
      'employees',
      'settings',
      'tutorial-system',
      'inventory-reports',
      'sales-reports',
      'purchases-reports',
      'production-reports',
      'finance-reports',
      'employees-reports',
      'debug-tools'
    ]

    // If clicking on a parent menu item, don't change selectedKey, just toggle open state
    if (parentMenuKeys.includes(key)) {
      // تشغيل صوت النقر للقوائم الرئيسية
      handlePlaySound('click')
      return
    }

    // تشغيل صوت النقر لجميع عناصر القائمة
    handlePlaySound('click')

    // معالجة الاختصارات الجديدة
    if (key === 'quick-checkin') {
      setQuickCheckinVisible(true)
      return
    }
    if (key === 'quick-checkout') {
      setQuickCheckoutVisible(true)
      return
    }
    if (key === 'daily-summary') {
      setDailySummaryVisible(true)
      return
    }
    if (key === 'quick-reports') {
      setQuickReportsVisible(true)
      return
    }

    setSelectedKey(key)

    // إغلاق جميع القوائم المنسدلة بعد اختيار عنصر فرعي (حسب الإعداد)
    // يمكن للمستخدم التحكم في هذا السلوك من قسم الإعدادات
    const autoCloseMenus = settings.find(s => s.key === 'auto_close_menus')?.value

    // إغلاق القوائم المنبثقة بعد الاختيار (افتراضياً مفعل إذا لم يتم تعيين الإعداد)
    if (!collapsed && (autoCloseMenus === 'true' || autoCloseMenus === undefined)) {
      // إغلاق فوري بدون setTimeout لتجنب التداخل مع useEffect
      setOpenKeys([])
    }

    // إذا كانت القائمة مطوية، نحتاج لفتح القائمة المناسبة مؤقتاً لإظهار العنصر المحدد
    if (collapsed) {
      // Define menu hierarchy mapping
      const menuHierarchy: { [key: string]: string[] } = {
        // Inventory management
        'warehouses': ['inventory-management'],
        'categories': ['inventory-management'],
        'items': ['inventory-management'],
        'barcodes': ['inventory-management'],
        'inventory': ['inventory-management'],
        'inventory-movements': ['inventory-management'],
        'inventory-detailed-report': ['inventory-management', 'inventory-reports'],
        'inventory-movement-report': ['inventory-management', 'inventory-reports'],
        'inventory-audit-report': ['inventory-management', 'inventory-reports'],
        'material-consumption-report': ['inventory-management', 'inventory-reports'],
        'low-stock-report': ['inventory-management', 'inventory-reports'],

        // Sales management
        'customers': ['sales'],
        'sales-orders': ['sales'],
        'sales-invoices': ['sales'],
        'sales-by-customer-report': ['sales', 'sales-reports'],
        'sales-by-product-report': ['sales', 'sales-reports'],
        'sales-performance-report': ['sales', 'sales-reports'],
        'commission-report': ['sales', 'sales-reports'],
        'customer-receivables-report': ['sales', 'sales-reports'],

        // Purchases management
        'suppliers': ['purchases-management'],
        'purchase-orders': ['purchases-management'],
        'purchase-invoices': ['purchases-management'],
        'supplier-payments': ['purchases-management'],
        'purchases-by-supplier-report': ['purchases-management', 'purchases-reports'],
        'purchases-by-item-report': ['purchases-management', 'purchases-reports'],
        'supplier-payables-report': ['purchases-management', 'purchases-reports'],
        'purchase-analysis-report': ['purchases-management', 'purchases-reports'],
        'cost-analysis-report': ['purchases-management', 'purchases-reports'],

        // Production management
        'furniture-production': ['production'],
        'paint-production': ['production'],
        'production-orders': ['production'],
        'furniture-production-report': ['production', 'production-reports'],
        'paint-production-report': ['production', 'production-reports'],
        'quality-control-report': ['production', 'production-reports'],
        'production-efficiency-report': ['production', 'production-reports'],
        'production-cost-report': ['production', 'production-reports'],
        'raw-material-usage-report': ['production', 'production-reports'],

        // Finance management
        'banks': ['finance'],
        'checks': ['finance'],
        'check-transfers': ['finance'],
        'company-checks': ['finance'],
        'vouchers': ['finance'],
        'fiscal-periods': ['finance'],
        'balance-sheet-report': ['finance', 'finance-reports'],
        'income-statement-report': ['finance', 'finance-reports'],
        'cash-flow-report': ['finance', 'finance-reports'],
        'receivables-report': ['finance', 'finance-reports'],
        'detailed-costs-report': ['finance', 'finance-reports'],
        'profitability-analysis-report': ['finance', 'finance-reports'],
        'cost-centers-report': ['finance', 'finance-reports'],

        // Employee management
        'employees-list': ['employees'],
        'departments': ['employees'],
        'attendance': ['employees'],
        'payroll': ['employees'],
        'leaves': ['employees'],
        'fingerprint-devices': ['employees'],
        'attendance-report': ['employees', 'employees-reports'],
        'payroll-report': ['employees', 'employees-reports'],
        'leaves-report': ['employees', 'employees-reports'],
        'performance-report': ['employees', 'employees-reports'],
        'overtime-report': ['employees', 'employees-reports'],

        // Settings
        'roles': ['settings'],
        'system-settings': ['settings'],
        'backup': ['settings'],

        // Tutorial System
        'tutorial-center': ['tutorial-system'],
        'smart-assistant': ['tutorial-system'],
        'production-guide': ['tutorial-system'],
        'audio-settings': ['tutorial-system'],

        // Debug Tools
        'image-debugger': ['debug-tools'],
        'image-monitor': ['debug-tools']
      }

      // Keep parent menus open based on hierarchy only when collapsed
      const requiredOpenKeys = menuHierarchy[key] || []
      if (requiredOpenKeys.length > 0) {
        const newOpenKeys = [...new Set([...openKeys, ...requiredOpenKeys])]
        setOpenKeys(newOpenKeys)
      }
    }

    // معالجة النقر على عناصر النظام التعليمي الجديد
    if (key.startsWith('tutorial-')) {
      handlePlaySound('click')
      // سيتم التعامل مع هذا في زر المساعدة البسيط
    }
  }





  const companyName = settings.find(s => s.key === 'company_name')?.value || 'ZET.IA'
  const currencySymbol = String(settings.find(s => s.key === 'currency_symbol')?.value || '₪')

  // قائمة التنقل
  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined data-audio-id="dashboard" />,
      label: 'لوحة التحكم'
    },
    {
      key: 'users',
      icon: <TeamOutlined />,
      label: 'إدارة المستخدمين',
      disabled: false // السماح لجميع المستخدمين بالوصول
    },
    {
      key: 'inventory-management',
      icon: <InboxOutlined data-audio-id="inventory" />,
      label: 'إدارة المخزون',
      children: [
        { key: 'warehouses', label: 'المخازن' },
        { key: 'categories', label: 'فئات الأصناف' },
        { key: 'items', label: 'الأصناف' },
        { key: 'barcodes', label: 'إدارة الباركود' },
        { key: 'inventory', label: 'المخزون الحالي' },
        { key: 'inventory-movements', label: 'حركات المخزون' },
        {
          key: 'inventory-reports',
          label: '📊 تقارير المخزون',
          children: [
            { key: 'inventory-detailed-report', label: 'تقرير المخزون التفصيلي' },
            { key: 'inventory-movement-report', label: 'تقرير حركة المخزون' },
            { key: 'inventory-audit-report', label: 'تقرير الجرد والمطابقة' },
            { key: 'material-consumption-report', label: 'تقرير استهلاك المواد' },
            { key: 'low-stock-report', label: 'تقرير الأصناف المنخفضة' }
          ]
        }
      ]
    },
    {
      key: 'sales',
      icon: <ShopOutlined data-audio-id="sales" />,
      label: 'المبيعات',
      children: [
        { key: 'customers', label: 'العملاء' },
        { key: 'sales-orders', label: 'أوامر البيع' },
        { key: 'sales-invoices', label: 'فواتير البيع' },
        {
          key: 'sales-reports',
          label: '📊 تقارير المبيعات',
          children: [
            { key: 'sales-by-customer-report', label: 'تقرير المبيعات حسب العميل' },
            { key: 'sales-by-product-report', label: 'تقرير المبيعات حسب المنتج' },
            { key: 'sales-performance-report', label: 'تحليل أداء المبيعات' },
            { key: 'commission-report', label: 'تقرير العمولات والحوافز' },
            { key: 'customer-receivables-report', label: 'تقرير مديونيات العملاء' },
            { key: 'sales-returns-discounts', label: 'تقرير المرتجعات والخصومات' },
            { key: 'sales-by-region', label: 'تقرير المبيعات حسب المنطقة' },
            { key: 'top-profitable-customers', label: 'تقرير العملاء الأكثر ربحية' }
          ]
        }
      ]
    },
    {
      key: 'purchases-management',
      icon: <ShoppingCartOutlined data-audio-id="purchases" />,
      label: 'المشتريات',
      children: [
        { key: 'suppliers', label: 'الموردين' },
        { key: 'purchase-orders', label: 'أوامر الشراء' },
        { key: 'purchase-invoices', label: 'فواتير الشراء' },
        { key: 'supplier-payments', label: 'مدفوعات الموردين' },
        {
          key: 'purchases-reports',
          label: '📊 تقارير المشتريات',
          children: [
            { key: 'purchases-by-supplier-report', label: 'تقرير المشتريات حسب المورد' },
            { key: 'purchases-by-item-report', label: 'تقرير المشتريات حسب الصنف' },
            { key: 'supplier-payables-report', label: 'تقرير مديونيات الموردين' },
            { key: 'purchase-analysis-report', label: 'تحليل أداء المشتريات' },
            { key: 'cost-analysis-report', label: 'تحليل التكاليف' },
            { key: 'supplier-price-comparison', label: 'تقرير مقارنة أسعار الموردين' },
            { key: 'supplier-quality-report', label: 'تقرير جودة الموردين' }
          ]
        }
      ]
    },
    {
      key: 'production',
      icon: <ToolOutlined data-audio-id="production" />,
      label: 'الإنتاج',
      children: [
        { key: 'furniture-production', label: 'إنتاج الأثاث' },
        { key: 'paint-production', label: 'دهان الأثاث' },
        { key: 'production-orders', label: 'أوامر الإنتاج' },
        {
          key: 'production-reports',
          label: '📊 تقارير الإنتاج',
          children: [
            { key: 'furniture-production-report', label: 'تقرير إنتاج الأثاث' },
            { key: 'paint-production-report', label: 'تقرير دهان الأثاث' },
            { key: 'quality-control-report', label: 'تقرير الجودة والفحص' },
            { key: 'production-efficiency-report', label: 'تقرير كفاءة الإنتاج' },
            { key: 'production-cost-report', label: 'تقرير تكاليف الإنتاج' },
            { key: 'raw-material-usage-report', label: 'تقرير استهلاك المواد الخام' }
          ]
        }
      ]
    },
    {
      key: 'finance',
      icon: <BankOutlined data-audio-id="finance" />,
      label: 'المالية',
      children: [
        { key: 'banks', label: 'البنوك' },
        { key: 'checks', label: 'الشيكات' },
        { key: 'check-transfers', label: 'تحويل الشيكات' },
        { key: 'company-checks', label: 'شيكات الشركة' },
        { key: 'vouchers', label: 'السندات' },
        { key: 'fiscal-periods', label: '🔒 إقفال السنة المالية' },
        {
          key: 'finance-reports',
          label: '📊 التقارير المالية',
          children: [
            { key: 'balance-sheet-report', label: 'الميزانية العمومية' },
            { key: 'income-statement-report', label: 'قائمة الأرباح والخسائر' },
            { key: 'income-statement', label: 'قائمة الدخل' },
            { key: 'cash-flow-report', label: 'التدفق النقدي' },
            { key: 'bank-reconciliation', label: 'مطابقة البنك' },
            { key: 'receivables-report', label: 'تقرير المديونيات' },
            { key: 'detailed-costs-report', label: 'تقارير التكاليف التفصيلية' },
            { key: 'profitability-analysis-report', label: 'تحليل الربحية والهوامش' },
            { key: 'cost-centers-report', label: 'تقارير مراكز التكلفة' }
          ]
        }
      ]
    },
    {
      key: 'employees',
      icon: <TeamOutlined data-audio-id="employees" />,
      label: 'إدارة الموظفين',
      children: [
        { key: 'employees-list', label: 'الموظفين' },
        { key: 'departments', label: 'الأقسام' },
        { key: 'attendance', label: 'الحضور والانصراف' },
        { key: 'payroll', label: 'الرواتب' },
        { key: 'leaves', label: 'الإجازات' },
        { key: 'fingerprint-devices', label: 'أجهزة البصمة' },
        {
          key: 'employees-reports',
          label: '📊 تقارير الموظفين',
          children: [
            { key: 'attendance-report', label: 'تقرير الحضور والانصراف' },
            { key: 'payroll-report', label: 'تقرير الرواتب والمكافآت' },
            { key: 'leaves-report', label: 'تقرير الإجازات والغياب' },
            { key: 'performance-report', label: 'تقرير الأداء والتقييم' },
            { key: 'overtime-report', label: 'تقرير الساعات الإضافية' }
          ]
        }
      ]
    },
    {
      key: 'settings',
      icon: <SettingOutlined data-audio-id="settings" />,
      label: 'الإعدادات',
      disabled: false, // السماح لجميع المستخدمين بالوصول
      children: [
        { key: 'roles', label: 'الأدوار والصلاحيات' },
        { key: 'system-settings', label: 'إعدادات النظام' },
        { key: 'print-settings', label: 'إعدادات الطباعة والتحسينات' },
        { key: 'backup', label: 'النسخ الاحتياطي' }
      ]
    },

    {
      key: 'reports',
      icon: <FileTextOutlined data-audio-id="reports" />,
      label: 'التقارير',
      children: [
        { key: 'all-reports', label: 'جميع التقارير' },
        { key: 'custom-reports', label: 'تقارير مخصصة' }
      ]
    },
    {
      key: 'debug-tools',
      icon: <BugOutlined data-audio-id="debug-tools" />,
      label: 'أدوات التشخيص',
      children: [
        {
          key: 'image-debugger',
          icon: <PictureOutlined />,
          label: 'مشخص الصور'
        },
        {
          key: 'image-monitor',
          icon: <ToolOutlined />,
          label: 'مراقب الصور'
        },
        {
          key: 'shortcuts-test',
          icon: <ToolOutlined />,
          label: 'اختبار الاختصارات السريعة'
        },
        {
          key: 'enhanced-table-demo',
          icon: <ToolOutlined />,
          label: 'عرض الجداول المحسنة'
        },
        {
          key: 'test-print-templates',
          icon: <PrinterOutlined />,
          label: 'اختبار قوالب الطباعة الجديدة'
        }
      ]
    }
  ]

  // قائمة المستخدم
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'الملف الشخصي'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'تسجيل الخروج',
      onClick: onLogout
    }
  ]

  // بيانات تجريبية
  const stats = [
    {
      title: 'إجمالي المبيعات',
      value: 125000,
      prefix: <DollarOutlined />,
      suffix: currencySymbol,
      color: '#52c41a'
    },
    {
      title: 'إجمالي المشتريات',
      value: 85000,
      prefix: <ShoppingCartOutlined />,
      suffix: currencySymbol,
      color: '#1890ff'
    },
    {
      title: 'عدد الأصناف',
      value: 450,
      prefix: <InboxOutlined />,
      color: '#722ed1'
    },
    {
      title: 'عدد العملاء',
      value: 125,
      prefix: <UserOutlined />,
      color: '#fa8c16'
    }
  ]

  const recentActivities = [
    { id: 1, type: 'sale', description: 'فاتورة بيع رقم 1001', amount: 5500, time: 'منذ ساعة' },
    { id: 2, type: 'purchase', description: 'فاتورة شراء رقم 2001', amount: 3200, time: 'منذ ساعتين' },
    { id: 3, type: 'production', description: 'أمر إنتاج أثاث رقم 3001', amount: 0, time: 'منذ 3 ساعات' },
    { id: 4, type: 'payment', description: 'سند قبض رقم 4001', amount: 2800, time: 'منذ 4 ساعات' }
  ]

  const productionStatus = [
    { department: 'قسم الأثاث', orders: 12, completed: 8, progress: 67 },
    { department: 'قسم الدهانات', orders: 8, completed: 6, progress: 75 }
  ]

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'sale': return <ShopOutlined style={{ color: '#52c41a' }} />
      case 'purchase': return <ShoppingCartOutlined style={{ color: '#1890ff' }} />
      case 'production': return <ToolOutlined style={{ color: '#722ed1' }} />
      case 'payment': return <DollarOutlined style={{ color: '#fa8c16' }} />
      default: return <InboxOutlined />
    }
  }

  const getActivityTag = (type: string) => {
    switch (type) {
      case 'sale': return <Tag color="green">مبيعات</Tag>
      case 'purchase': return <Tag color="blue">مشتريات</Tag>
      case 'production': return <Tag color="purple">إنتاج</Tag>
      case 'payment': return <Tag color="orange">دفع</Tag>
      default: return <Tag>عام</Tag>
    }
  }

  const renderContent = () => {
    if (loading) {
      return (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>جاري تحميل البيانات...</div>
        </div>
      )
    }

    switch (selectedKey) {
      case 'dashboard':
        return (
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            {/* ترحيب */}
            <WelcomeCard>
              <div className="welcome-content">
                <div className="welcome-info">
                  <Title level={4} style={{ color: 'white', margin: 0 }}>
                    مرحباً {user?.full_name || 'المستخدم'} في {companyName}
                  </Title>
                  <Text style={{ fontSize: 14, color: 'white', opacity: 0.9 }}>
                    نظام إدارة شامل للمحاسبة والإنتاج - {new Date().toLocaleDateString('ar-EG')}
                  </Text>
                </div>
                <div className="welcome-tags">
                  <Tag color="blue">الدور: {user?.role || 'غير محدد'}</Tag>
                  <Tag color="green">نشط</Tag>
                </div>
              </div>
            </WelcomeCard>

      {/* الاختصارات السريعة */}
      <QuickShortcuts
        onShortcutClick={handleShortcutClick}
        onPlaySound={handlePlaySound}
      />

      {/* الإحصائيات الرئيسية */}
      <Row gutter={[16, 16]}>
        {stats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <StatCard>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.prefix}
                suffix={stat.suffix}
                valueStyle={{ color: stat.color }}
              />
            </StatCard>
          </Col>
        ))}
      </Row>

      <Row gutter={[16, 16]}>
        {/* الأنشطة الأخيرة */}
        <Col xs={24} lg={12}>
          <StyledCard title="الأنشطة الأخيرة" extra={<a href="#">عرض الكل</a>}>
            <List
              itemLayout="horizontal"
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={getActivityIcon(item.type)}
                    title={
                      <Space>
                        {item.description}
                        {getActivityTag(item.type)}
                      </Space>
                    }
                    description={
                      <Space>
                        {item.amount > 0 && (
                          <Text strong style={{ color: '#52c41a' }}>
                            {Number(item.amount).toLocaleString()} {currencySymbol}
                          </Text>
                        )}
                        <Text type="secondary">{item.time}</Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </StyledCard>
        </Col>

        {/* حالة الإنتاج */}
        <Col xs={24} lg={12}>
          <StyledCard title="حالة الإنتاج" extra={<a href="#">تفاصيل</a>}>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              {productionStatus.map((dept, index) => (
                <div key={index}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                    <Text strong>{dept.department}</Text>
                    <Text type="secondary">
                      {dept.completed}/{dept.orders} أوامر
                    </Text>
                  </div>
                  <Progress 
                    percent={dept.progress} 
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                    size="small"
                  />
                </div>
              ))}
            </Space>
          </StyledCard>
        </Col>
      </Row>

      {/* مؤشرات سريعة */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <StyledCard>
            <Statistic
              title="نمو المبيعات"
              value={11.28}
              precision={2}
              valueStyle={{ color: '#3f8600' }}
              prefix={<RiseOutlined />}
              suffix="%"
            />
          </StyledCard>
        </Col>
        <Col xs={24} sm={8}>
          <StyledCard>
            <Statistic
              title="معدل الإنتاج"
              value={9.3}
              precision={2}
              valueStyle={{ color: '#cf1322' }}
              prefix={<FallOutlined />}
              suffix="%"
            />
          </StyledCard>
        </Col>
        <Col xs={24} sm={8}>
          <StyledCard>
            <Statistic
              title="رضا العملاء"
              value={93.5}
              precision={1}
              valueStyle={{ color: '#3f8600' }}
              prefix={<RiseOutlined />}
              suffix="%"
            />
          </StyledCard>
        </Col>
            </Row>
          </Space>
        )

      case 'users':
        return (
          <ErrorBoundary
            onError={(error, errorInfo) => {
              Logger.error('Dashboard', 'خطأ في مكون إدارة المستخدمين:', error, errorInfo)
              message.error('حدث خطأ في قسم إدارة المستخدمين')
            }}
          >
            <EnhancedUserManagement currentUser={user} />
          </ErrorBoundary>
        )

      case 'roles':
        return <EnhancedRoleManagement currentUser={user} />

      case 'system-settings':
        return <SystemSettings currentUser={user} />

      case 'print-settings':
        return <PrintSettings />

      case 'backup':
        return <BackupManagement />

      // تم حذف حالات طباعة الصور القديمة - استخدم SimpleImageManager بدلاً منها

      case 'warehouses':
        return <InventoryManagement onBack={() => setSelectedKey('dashboard')} initialView="warehouses" />

      case 'categories':
        return <InventoryManagement onBack={() => setSelectedKey('dashboard')} initialView="categories" />

      case 'items':
        return <InventoryManagement onBack={() => setSelectedKey('dashboard')} initialView="items" />

      case 'barcodes':
        return <InventoryManagement onBack={() => setSelectedKey('dashboard')} initialView="barcodes" />

      case 'inventory':
        return <InventoryManagement onBack={() => setSelectedKey('dashboard')} initialView="inventory" />

      case 'inventory-movements':
        return <InventoryManagement onBack={() => setSelectedKey('dashboard')} initialView="movements" />

      // تقارير المخزون
      case 'inventory-detailed-report':
        return <InventoryManagement onBack={() => setSelectedKey('dashboard')} initialView="inventory-detailed" />

      case 'inventory-movement-report':
        return <InventoryManagement onBack={() => setSelectedKey('dashboard')} initialView="inventory-movements" />

      case 'low-stock-report':
        return <InventoryManagement onBack={() => setSelectedKey('dashboard')} initialView="low-stock" />

      case 'inventory-audit-report':
        return <InventoryManagement onBack={() => setSelectedKey('dashboard')} initialView="inventory-audit" />

      case 'material-consumption-report':
        return <InventoryManagement onBack={() => setSelectedKey('dashboard')} initialView="material-consumption" />

      // وحدة المشتريات
      case 'purchases-management':
        return (
          <ErrorBoundary
            onError={(error, errorInfo) => {
              Logger.error('Dashboard', 'خطأ في مكون إدارة المشتريات:', error, errorInfo)
              message.error('حدث خطأ في قسم إدارة المشتريات')
            }}
          >
            <PurchaseManagement onBack={() => setSelectedKey('dashboard')} />
          </ErrorBoundary>
        )

      // الوحدات الفرعية للمشتريات
      case 'suppliers':
        return (
          <ErrorBoundary
            onError={(error, errorInfo) => {
              Logger.error('Dashboard', 'خطأ في مكون الموردين:', error, errorInfo)
              message.error('حدث خطأ في قسم الموردين')
            }}
          >
            <PurchaseManagement onBack={() => setSelectedKey('dashboard')} initialView="suppliers" />
          </ErrorBoundary>
        )

      case 'purchase-orders':
        return (
          <ErrorBoundary
            onError={(error, errorInfo) => {
              Logger.error('Dashboard', 'خطأ في مكون أوامر الشراء:', error, errorInfo)
              message.error('حدث خطأ في قسم أوامر الشراء')
            }}
          >
            <PurchaseManagement onBack={() => setSelectedKey('dashboard')} initialView="orders" />
          </ErrorBoundary>
        )

      case 'purchase-invoices':
        return (
          <ErrorBoundary
            onError={(error, errorInfo) => {
              Logger.error('Dashboard', 'خطأ في مكون فواتير الشراء:', error, errorInfo)
              message.error('حدث خطأ في قسم فواتير الشراء')
            }}
          >
            <PurchaseManagement onBack={() => setSelectedKey('dashboard')} initialView="invoices" />
          </ErrorBoundary>
        )

      case 'supplier-payments':
        return (
          <ErrorBoundary
            onError={(error, errorInfo) => {
              Logger.error('Dashboard', 'خطأ في مكون مدفوعات الموردين:', error, errorInfo)
              message.error('حدث خطأ في قسم مدفوعات الموردين')
            }}
          >
            <PurchaseManagement onBack={() => setSelectedKey('dashboard')} initialView="payments" />
          </ErrorBoundary>
        )

      // تقارير المشتريات
      case 'purchases-by-supplier-report':
        return <PurchasesBySupplierReport />

      case 'purchases-by-item-report':
        return <PurchasesByItemReport />

      case 'supplier-payables-report':
        return <SupplierPayablesReport />

      case 'purchase-analysis-report':
        return <PurchaseAnalysisReport />

      case 'cost-analysis-report':
        return <CostAnalysisReport />

      case 'supplier-price-comparison':
        return <SupplierPriceComparisonReport />

      case 'supplier-quality-report':
        return <SupplierQualityReport />

      // وحدة المبيعات
      case 'sales':
        return <SalesManagement onBack={() => setSelectedKey('dashboard')} />

      // الوحدات الفرعية للمبيعات
      case 'customers':
        return <SalesManagement onBack={() => setSelectedKey('dashboard')} initialView="customers" />

      case 'sales-orders':
        return <SalesManagement onBack={() => setSelectedKey('dashboard')} initialView="orders" />

      case 'sales-invoices':
        return <SalesManagement onBack={() => setSelectedKey('dashboard')} initialView="invoices" />

      // تقارير المبيعات
      case 'sales-by-customer-report':
        return <SalesByCustomerReport />

      case 'sales-by-product-report':
        return <SalesByProductReport />

      case 'sales-performance-report':
        return <MonthlySalesReport />

      case 'commission-report':
        return <SalesByProductReport />

      case 'customer-receivables-report':
        return <SalesByCustomerReport />

      case 'sales-returns-discounts':
        return <SalesReturnsReport />

      case 'sales-by-region':
        return <SalesByRegionReport />

      case 'top-profitable-customers':
        return <TopProfitableCustomersReport />

      // وحدات الإنتاج
      case 'furniture-production':
        return <ProductionManagement onBack={() => setSelectedKey('dashboard')} initialView="furniture" />

      case 'paint-production':
        return <ProductionManagement onBack={() => setSelectedKey('dashboard')} initialView="paint" />

      case 'production-orders':
        return <ProductionManagement onBack={() => setSelectedKey('dashboard')} initialView="orders" />

      // تقارير الإنتاج
      case 'furniture-production-report':
        return <ProductionManagement onBack={() => setSelectedKey('dashboard')} initialView="reports" />

      case 'paint-production-report':
        return <ProductionManagement onBack={() => setSelectedKey('dashboard')} initialView="reports" />

      case 'quality-control-report':
        return <ProductionManagement onBack={() => setSelectedKey('dashboard')} initialView="reports" />

      case 'production-efficiency-report':
        return <ProductionManagement onBack={() => setSelectedKey('dashboard')} initialView="reports" />

      case 'production-cost-report':
        return <ProductionManagement onBack={() => setSelectedKey('dashboard')} initialView="reports" />

      case 'raw-material-usage-report':
        return <ProductionManagement onBack={() => setSelectedKey('dashboard')} initialView="reports" />

      // وحدات المالية
      case 'finance':
        return <FinanceManagement onBack={() => setSelectedKey('dashboard')} />

      case 'banks':
        return <FinanceManagement onBack={() => setSelectedKey('dashboard')} initialView="banks" />

      case 'checks':
        return <FinanceManagement onBack={() => setSelectedKey('dashboard')} initialView="checks" />

      case 'check-transfers':
        return <FinanceManagement onBack={() => setSelectedKey('dashboard')} initialView="check-transfers" />

      case 'company-checks':
        return <FinanceManagement onBack={() => setSelectedKey('dashboard')} initialView="company-checks" />

      case 'vouchers':
        return <FinanceManagement onBack={() => setSelectedKey('dashboard')} initialView="vouchers" />

      case 'fiscal-periods':
        return <FiscalPeriodManager />

      // التقارير المالية
      case 'balance-sheet-report':
        return <BalanceSheetReport />

      case 'income-statement-report':
        return <FinanceManagement onBack={() => setSelectedKey('dashboard')} initialView="reports" reportType="income-statement-report" />

      case 'income-statement':
        return <IncomeStatementReport />

      case 'cash-flow-report':
        return <CashFlowReport />

      case 'bank-reconciliation':
        return <BankReconciliationReport />

      case 'receivables-report':
        return <FinanceManagement onBack={() => setSelectedKey('dashboard')} initialView="reports" reportType="receivables-report" />

      case 'detailed-costs-report':
        return <FinanceManagement onBack={() => setSelectedKey('dashboard')} initialView="reports" reportType="detailed-costs-report" />

      case 'profitability-analysis-report':
        return <FinanceManagement onBack={() => setSelectedKey('dashboard')} initialView="reports" reportType="profitability-analysis-report" />

      case 'cost-centers-report':
        return <FinanceManagement onBack={() => setSelectedKey('dashboard')} initialView="reports" reportType="cost-centers-report" />

      // وحدات الموظفين
      case 'employees-list':
        return <EmployeeManagement />

      case 'departments':
        return <DepartmentManagement />

      case 'attendance':
        return <AttendanceManagement />

      case 'payroll':
        return <PayrollManagement />

      case 'leaves':
        return <LeaveManagement />

      case 'employee-reports':
        return <EmployeeReports />

      // تقارير الموظفين الفردية - استخدام ReportBase
      case 'attendance-report':
        return <EmployeeAttendanceReport />

      case 'payroll-report':
        return <EmployeePayrollReport />

      case 'leaves-report':
        return <EmployeeLeavesReport />

      case 'performance-report':
        return <EmployeePerformanceReport />

      case 'overtime-report':
        return <EmployeeAttendanceReport />

      case 'fingerprint-devices':
        return <FingerprintDeviceManagement />

      // أدوات التشخيص
      case 'shortcuts-test':
        return <QuickShortcutsTest />

      case 'enhanced-table-demo':
        return <EnhancedTableDemo />

      default:
        return (
          <StyledCard title={`الوحدة: ${selectedKey}`}>
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <FileTextOutlined style={{ fontSize: 48, color: '#ccc' }} />
              <div style={{ marginTop: 16 }}>قريباً - هذه الوحدة قيد التطوير</div>
            </div>
          </StyledCard>
        )
    }
  }

  return (
    <StyledLayout>
      <StyledSider trigger={null} collapsible collapsed={collapsed}>
        <LogoContainer className={collapsed ? 'collapsed' : ''}>
          <AnimatedLogo
            size={collapsed ? 'sm' : 'md'}
            showText={!collapsed}
            showSubtitle={false}
            showDeveloper={false}
            variant={collapsed ? 'minimal' : 'default'}
          />
        </LogoContainer>
        <Menu
          theme="dark"
          mode="vertical"
          selectedKeys={[selectedKey]}
          openKeys={openKeys}
          items={menuItems}
          onClick={handleMenuClick}
          onOpenChange={handleMenuOpenChange}
          style={{ border: 'none' }}
          triggerSubMenuAction="click"
          data-audio-id="main-menu"
        />
      </StyledSider>

      <MainContentLayout $collapsed={collapsed}>
        {/* شريط معلومات المطور - يظهر فقط في وضع التطوير */}
        {process.env.NODE_ENV === 'development' && (
          <DeveloperInfoBar>
            <div className="developer-info">
              <span className="app-name">ZET.IA</span>
              <span>|</span>
              <span>المطور: FARESNAWAF</span>
              <span>|</span>
              <span><EMAIL></span>
              <span>|</span>
              <span>**********</span>
            </div>
            <div>
              <span>نظام إدارة شامل للمحاسبة والإنتاج</span>
            </div>
          </DeveloperInfoBar>
        )}

        {/* تحذير انتهاء الترخيص */}
        <LicenseWarning />

        <StyledHeader>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={async () => {
                handlePlaySound('click')
                const newCollapsedState = !collapsed
                setCollapsed(newCollapsedState)

                // حفظ الإعداد في قاعدة البيانات
                try {
                  if (window.electronAPI) {
                    await window.electronAPI.updateSettings({
                      sidebar_collapsed: newCollapsedState.toString()
                    })
                    Logger.info('Dashboard', 'تم حفظ إعداد القائمة الجانبية:', newCollapsedState)
                  }
                } catch (error) {
                  Logger.error('Dashboard', 'خطأ في حفظ إعداد القائمة الجانبية', error as Error)
                }
              }}
              style={{
                fontSize: '12px',
                width: '24px',
                height: '24px',
                borderRadius: '4px',
                transition: 'all 0.3s ease-in-out'
              }}
              className="sidebar-toggle-btn"
              title={collapsed ? 'توسيع القائمة الجانبية' : 'طي القائمة الجانبية'}
              data-audio-id="sidebar-toggle"
            />

            {/* عرض حالة التفعيل */}
            <LicenseStatusDisplay />
            <Title level={5} style={{
              margin: 0,
              marginLeft: 8,
              fontSize: '14px',
              lineHeight: '32px'
            }}>
              {menuItems.find(item => item.key === selectedKey)?.label || 'لوحة التحكم'}
            </Title>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            {/* زر المساعدة البسيط */}
            <SimpleHelpButton />

            {/* التحكم السريع في المزامنة */}
            <QuickSyncControl />

            {/* تبديل الثيم */}
            <ThemeToggle size="small" />

            <Badge count={unreadNotificationsCount} overflowCount={99}>
              <Button
                type="text"
                icon={<BellOutlined />}
                size="small"
                style={{ height: '24px', width: '24px', padding: 0 }}
                onClick={() => setNotificationCenterVisible(true)}
              />
            </Badge>

            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: ({ key }) => {
                  if (key === 'logout') {
                    onLogout()
                  }
                }
              }}
              placement="bottomRight"
            >
              <div style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', padding: '2px 4px' }}>
                <Avatar
                  icon={<UserOutlined />}
                  size={20}
                  style={{ marginLeft: 6 }}
                  src={(user as any)?.avatar_url || undefined}
                  onError={() => {
                    Logger.warn('Dashboard', 'فشل في تحميل صورة المستخدم')
                    return false // استخدام الأيقونة الافتراضية
                  }}
                />
                <div>
                  <div style={{ fontWeight: 500, fontSize: '12px', lineHeight: '14px' }}>{user?.full_name || 'المستخدم'}</div>
                  <div style={{ fontSize: 10, color: '#666', lineHeight: '12px' }}>{user?.role || 'غير محدد'}</div>
                </div>
              </div>
            </Dropdown>
          </div>
        </StyledHeader>

        <StyledContent>
          {renderContent()}
        </StyledContent>
      </MainContentLayout>

      {/* مركز الإشعارات */}
      <NotificationCenter
        visible={notificationCenterVisible}
        onClose={() => {
          setNotificationCenterVisible(false)
          loadUnreadNotificationsCount() // تحديث العدد عند إغلاق المركز
        }}
        currentUser={user}
      />

      {/* النظام التعليمي البسيط مدمج في زر المساعدة */}

      {/* النظام التعليمي البسيط متاح من زر المساعدة في الهيدر */}

      {/* المكونات الجديدة للاختصارات السريعة */}
      <QuickAttendance
        visible={quickCheckinVisible}
        onClose={() => setQuickCheckinVisible(false)}
        type="checkin"
      />

      <QuickAttendance
        visible={quickCheckoutVisible}
        onClose={() => setQuickCheckoutVisible(false)}
        type="checkout"
      />

      <DailySummary
        visible={dailySummaryVisible}
        onClose={() => setDailySummaryVisible(false)}
      />

      <QuickReports
        visible={quickReportsVisible}
        onClose={() => setQuickReportsVisible(false)}
        onNavigateToReport={(reportKey) => setSelectedKey(reportKey)}
      />
    </StyledLayout>
  )
}

export default Dashboard
