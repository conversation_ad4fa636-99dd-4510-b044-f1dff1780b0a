// Enhanced TypeScript declarations
declare module 'sql.js' {
  interface Database {
    run(sql: string, params?: any[]): void;
    exec(sql: string): any[];
    prepare(sql: string): Statement;
    close(): void;
    export(): Uint8Array;
  }
  interface Statement {
    run(params?: any[]): void;
    get(params?: any[]): any;
    all(params?: any[]): any[];
    bind(params?: any[]): void;
    step(): boolean;
    getColumnNames(): string[];
    free(): void;
  }
  interface SqlJsStatic {
    Database: { new (data?: ArrayLike<number> | Buffer | null): Database; };
  }
  function initSqlJs(config?: { locateFile?: (filename: string) => string; }): Promise<SqlJsStatic>;
  export = initSqlJs;
}

declare module 'electron' {
  export * from 'electron/renderer';
}

declare global {
  interface Window {
    electronAPI: {
      getDatabaseStatus: () => Promise<any>;
      checkHealth: () => Promise<any>;
      on: (channel: string, callback: Function) => void;
      removeListener: (channel: string, callback: Function) => void;
      [key: string]: any;
    };
  }
}

export {};