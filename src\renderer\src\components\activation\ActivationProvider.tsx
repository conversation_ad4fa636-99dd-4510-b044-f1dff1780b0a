import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import EnhancedActivationModal from './EnhancedActivationModal'
import { SafeLogger as Logger } from '../../utils/logger'

interface ActivationContextType {
  isActivated: boolean
  isLoading: boolean
  licenseInfo: any
  checkActivation: () => Promise<void>
  showActivationModal: () => void
  hideActivationModal: () => void
}

const ActivationContext = createContext<ActivationContextType | undefined>(undefined)

interface ActivationProviderProps {
  children: ReactNode
}

export const ActivationProvider: React.FC<ActivationProviderProps> = ({ children }) => {
  const [isActivated, setIsActivated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [licenseInfo, setLicenseInfo] = useState(null)
  const [showModal, setShowModal] = useState(false)

  // فحص حالة التفعيل عند بدء التطبيق
  useEffect(() => {
    checkActivation()

    // فحص دوري كل 30 دقيقة للتأكد من عدم انتهاء الترخيص
    const interval = setInterval(() => {
      checkActivation()
    }, 30 * 60 * 1000) // كل 30 دقيقة

    return () => clearInterval(interval)
  }, [])

  const checkActivation = async () => {
    setIsLoading(true)
    try {
      const status = await window.electronAPI?.checkActivationStatus()
      
      if (status) {
        setIsActivated(status.isActivated)
        setLicenseInfo(status)
        
        // إذا لم يكن مفعل، أظهر نافذة التفعيل
        if (!status.isActivated) {
          setShowModal(true)
        } else {
          // بدء الفحص الدوري إذا كان مفعل
          await window.electronAPI?.startActivationCheck()
        }
      } else {
        setIsActivated(false)
        setShowModal(true)
      }
    } catch (error) {
      Logger.error('ActivationProvider', 'خطأ في فحص التفعيل:', error)
      setIsActivated(false)
      setShowModal(true)
    } finally {
      setIsLoading(false)
    }
  }

  const showActivationModal = () => {
    setShowModal(true)
  }

  const hideActivationModal = () => {
    setShowModal(false)
  }

  const handleActivationSuccess = async () => {
    setShowModal(false)
    await checkActivation()
  }

  const contextValue: ActivationContextType = {
    isActivated,
    isLoading,
    licenseInfo,
    checkActivation,
    showActivationModal,
    hideActivationModal
  }

  return (
    <ActivationContext.Provider value={contextValue}>
      {/* نافذة التفعيل - يجب أن تظهر فوق كل شيء */}
      <EnhancedActivationModal
        visible={showModal}
        onActivationSuccess={handleActivationSuccess}
        licenseInfo={licenseInfo}
      />

      {/* المحتوى الرئيسي - يظهر فقط إذا كان البرنامج مفعل */}
      {isActivated ? children : null}
    </ActivationContext.Provider>
  )
}

export const useActivation = (): ActivationContextType => {
  const context = useContext(ActivationContext)
  if (context === undefined) {
    throw new Error('useActivation must be used within an ActivationProvider')
  }
  return context
}

export default ActivationProvider
