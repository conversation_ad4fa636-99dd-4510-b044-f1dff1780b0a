import { ipcMain } from 'electron'
import { NotificationService } from '../services'
import { Logger } from '../utils/logger'

// متغيرات الخدمات
let notificationService: NotificationService

// دالة تعيين خدمة الإشعارات
export function setNotificationService(service: NotificationService) {
  notificationService = service
}

// تسجيل معالجات الإشعارات
export function registerNotificationHandlers() {
  // الإشعارات
  ipcMain.handle('get-notifications', async (_event, filters?: any) => {
    try {
      if (!notificationService) {
        return { success: false, message: 'خدمة الإشعارات غير متاحة' }
      }
      return await notificationService.getNotifications(filters?.userId, filters)
    } catch (error) {
      Logger.error('NotificationHandlers', 'خطأ في جلب الإشعارات:', error)
      return { success: false, message: 'حدث خطأ في جلب الإشعارات' }
    }
  })

  ipcMain.handle('get-unread-notifications-count', async (_event, userId?: number) => {
    try {
      if (!notificationService || !userId) {
        return { success: false, message: 'خدمة الإشعارات غير متاحة أو معرف المستخدم مفقود' }
      }
      return await notificationService.getUnreadNotificationsCount(userId)
    } catch (error) {
      Logger.error('NotificationHandlers', 'خطأ في جلب عدد الإشعارات:', error)
      return { success: false, message: 'حدث خطأ في جلب عدد الإشعارات' }
    }
  })

  ipcMain.handle('mark-notification-as-read', async (_event, notificationId: number) => {
    try {
      if (!notificationService) {
        return { success: false, message: 'خدمة الإشعارات غير متاحة' }
      }
      return await notificationService.markNotificationAsRead(notificationId)
    } catch (error) {
      Logger.error('NotificationHandlers', 'خطأ في تحديد الإشعار كمقروء:', error)
      return { success: false, message: 'حدث خطأ في تحديد الإشعار كمقروء' }
    }
  })

  ipcMain.handle('mark-all-notifications-as-read', async (_event, userId?: number) => {
    try {
      if (!notificationService || !userId) {
        return { success: false, message: 'خدمة الإشعارات غير متاحة أو معرف المستخدم مفقود' }
      }
      return await notificationService.markAllNotificationsAsRead(userId)
    } catch (error) {
      Logger.error('NotificationHandlers', 'خطأ في تحديد جميع الإشعارات كمقروءة:', error)
      return { success: false, message: 'حدث خطأ في تحديد جميع الإشعارات كمقروءة' }
    }
  })

  ipcMain.handle('create-notification', async (_event, notificationData: any) => {
    try {
      if (!notificationService) {
        return { success: false, message: 'خدمة الإشعارات غير متاحة' }
      }
      return await notificationService.createNotification(notificationData)
    } catch (error) {
      Logger.error('NotificationHandlers', 'خطأ في إنشاء الإشعار:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الإشعار' }
    }
  })

  ipcMain.handle('delete-notification', async (_event, notificationId: number) => {
    try {
      if (!notificationService) {
        return { success: false, message: 'خدمة الإشعارات غير متاحة' }
      }
      return await notificationService.deleteNotification(notificationId)
    } catch (error) {
      Logger.error('NotificationHandlers', 'خطأ في حذف الإشعار:', error)
      return { success: false, message: 'حدث خطأ في حذف الإشعار' }
    }
  })

  ipcMain.handle('get-notification-settings', async (_event, userId?: number) => {
    try {
      if (!notificationService || !userId) {
        return { success: false, message: 'خدمة الإشعارات غير متاحة أو معرف المستخدم مفقود' }
      }
      return await notificationService.getNotificationSettings(userId)
    } catch (error) {
      Logger.error('NotificationHandlers', 'خطأ في جلب إعدادات الإشعارات:', error)
      return { success: false, message: 'حدث خطأ في جلب إعدادات الإشعارات' }
    }
  })

  ipcMain.handle('update-notification-settings', async (_event, userId: number, settings: any) => {
    try {
      if (!notificationService) {
        return { success: false, message: 'خدمة الإشعارات غير متاحة' }
      }
      return await notificationService.updateNotificationSettings(userId, settings)
    } catch (error) {
      Logger.error('NotificationHandlers', 'خطأ في تحديث إعدادات الإشعارات:', error)
      return { success: false, message: 'حدث خطأ في تحديث إعدادات الإشعارات' }
    }
  })

  // إشعارات النّام
  ipcMain.handle('get-system-alerts', async () => {
    try {
      if (!notificationService) {
        return { success: false, message: 'خدمة الإشعارات غير متاحة' }
      }
      return await notificationService.getSystemAlerts()
    } catch (error) {
      Logger.error('NotificationHandlers', 'خطأ في جلب تنبيهات النّام:', error)
      return { success: false, message: 'حدث خطأ في جلب تنبيهات النّام' }
    }
  })

  ipcMain.handle('dismiss-system-alert', async (_event, alertId: number) => {
    try {
      if (!notificationService) {
        return { success: false, message: 'خدمة الإشعارات غير متاحة' }
      }
      return await notificationService.dismissSystemAlert(alertId)
    } catch (error) {
      Logger.error('NotificationHandlers', 'خطأ في إخفاء التنبيه:', error)
      return { success: false, message: 'حدث خطأ في إخفاء التنبيه' }
    }
  })

  // الإشعارات الذكية - سيتم تنفيذها لاحقاً
  ipcMain.handle('get-low-stock-items', async (_event, _threshold?: number) => {
    // TODO: تنفيذ جلب الأصناف منخفضة المخزون
    return { success: true, data: [] }
  })

  ipcMain.handle('get-due-invoices', async (_event, _reminderDays?: number) => {
    // TODO: تنفيذ جلب الفواتير المستحقة
    return { success: true, data: [] }
  })

  ipcMain.handle('get-last-backup-info', async () => {
    // TODO: تنفيذ جلب معلومات آخر نسخة احتياطية
    return { success: true, data: { lastBackup: null } }
  })

  ipcMain.handle('get-suspicious-activities', async () => {
    // TODO: تنفيذ جلب الأنشطة المشبوهة
    return { success: true, data: [] }
  })
}
