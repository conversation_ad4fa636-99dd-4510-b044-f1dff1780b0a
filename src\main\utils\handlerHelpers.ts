import { Logger } from './logger'

// واجهة موحدة للاستجابة
export interface ApiResponse {
  success: boolean
  data?: any
  message?: string
  error?: string
}

// دالة مساعدة موحدة لمعالجة استدعاءات API
export const handleApiCall = async (
  operation: () => Promise<any>,
  errorContext: string,
  validateParams?: () => void
): Promise<ApiResponse> => {
  try {
    // تشغيل validation إذا كان موجود
    if (validateParams) {
      validateParams()
    }

    const result = await operation()
    return { success: true, data: result }
  } catch (error) {
    Logger.error(errorContext, 'خطأ في العملية:', error)
    return { 
      success: false, 
      message: `حدث خطأ في ${errorContext}`,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

// دوال التحقق الموحدة
export const validateId = (id: any, fieldName: string): void => {
  if (!id || typeof id !== 'number' || id <= 0) {
    throw new Error(`${fieldName} مطلوب ويجب أن يكون رقم صحيح أكبر من صفر`)
  }
}

export const validateString = (value: any, fieldName: string, required: boolean = true): void => {
  if (required && (!value || typeof value !== 'string' || value.trim() === '')) {
    throw new Error(`${fieldName} مطلوب ويجب أن يكون نص صحيح`)
  }
  if (!required && value && typeof value !== 'string') {
    throw new Error(`${fieldName} يجب أن يكون نص صحيح`)
  }
}

export const validateNumber = (value: any, fieldName: string, required: boolean = true): void => {
  if (required && (value === undefined || value === null || typeof value !== 'number')) {
    throw new Error(`${fieldName} مطلوب ويجب أن يكون رقم صحيح`)
  }
  if (!required && value !== undefined && value !== null && typeof value !== 'number') {
    throw new Error(`${fieldName} يجب أن يكون رقم صحيح`)
  }
}

export const validateObject = (obj: any, fieldName: string): void => {
  if (!obj || typeof obj !== 'object' || Array.isArray(obj)) {
    throw new Error(`${fieldName} مطلوب ويجب أن يكون كائن صحيح`)
  }
}

export const validateArray = (arr: any, fieldName: string, required: boolean = true): void => {
  if (required && (!arr || !Array.isArray(arr))) {
    throw new Error(`${fieldName} مطلوب ويجب أن يكون مصفوفة صحيحة`)
  }
  if (!required && arr && !Array.isArray(arr)) {
    throw new Error(`${fieldName} يجب أن يكون مصفوفة صحيحة`)
  }
}

export const validateFilters = (filters: any): void => {
  if (filters && typeof filters !== 'object') {
    throw new Error('المرشحات يجب أن تكون كائن صحيح')
  }
}

// دالة مساعدة لتسجيل العمليات
export const logOperation = (context: string, operation: string, details?: any): void => {
  Logger.info(context, `🔄 بدء العملية: ${operation}`)
  if (details) {
    Logger.info(context, `📋 تفاصيل العملية:`, details)
  }
}

export const logSuccess = (context: string, operation: string, result?: any): void => {
  if (result && Array.isArray(result)) {
    Logger.info(context, `✅ تم إنجاز العملية بنجاح: ${operation} - ${result.length} عنصر`)
  } else {
    Logger.info(context, `✅ تم إنجاز العملية بنجاح: ${operation}`)
  }
}

export const logError = (context: string, operation: string, error: any): void => {
  Logger.error(context, `❌ فشل في العملية: ${operation}`, error)
}

// دالة مساعدة لتنظيف البيانات من SQL injection
export const sanitizeString = (value: string): string => {
  if (!value || typeof value !== 'string') return ''
  return value.replace(/'/g, "''")
}

// دالة مساعدة لتحويل القيم إلى نص آمن
export const toSafeString = (value: any): string => {
  if (value === null || value === undefined) return ''
  return sanitizeString(String(value))
}

// دالة مساعدة للتحقق من وجود الخدمة
export const validateService = (service: any, serviceName: string): void => {
  if (!service) {
    throw new Error(`خدمة ${serviceName} غير متاحة`)
  }
}

// دالة مساعدة لإنشاء استجابة خطأ موحدة
export const createErrorResponse = (message: string, error?: any): ApiResponse => {
  return {
    success: false,
    message,
    error: error instanceof Error ? error.message : String(error)
  }
}

// دالة مساعدة لإنشاء استجابة نجاح موحدة
export const createSuccessResponse = (data?: any, message?: string): ApiResponse => {
  return {
    success: true,
    data,
    message
  }
}
