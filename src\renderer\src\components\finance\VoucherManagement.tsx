import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Space,
  Row,
  Col,
  Statistic,
  DatePicker,
  Tabs,
  App,
  message
} from 'antd'
import {
  PlusOutlined,
  ArrowLeftOutlined,
  <PERSON>UpOutlined,
  <PERSON>DownOutlined,
  PrinterOutlined,
  FileExcelOutlined
} from '@ant-design/icons'
import { UnifiedPrintButton } from '../common'
import { SafeLogger as Logger } from '../../utils/logger'

import * as XLSX from 'xlsx'
import dayjs from 'dayjs'




interface VoucherManagementProps {
  onBack: () => void
}

const VoucherManagement: React.FC<VoucherManagementProps> = ({ onBack }) => {
  const { message: messageApi } = App.useApp()
  const [paymentVouchers, setPaymentVouchers] = useState([])
  const [receiptVouchers, setReceiptVouchers] = useState([])
  const [bankAccounts, setBankAccounts] = useState([])
  const [customers, setCustomers] = useState([])
  const [suppliers, setSuppliers] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [voucherType, setVoucherType] = useState<'payment' | 'receipt'>('payment')
  const [form] = Form.useForm()

  const loadVouchers = async () => {
    setLoading(true)
    try {
      const [paymentResponse, receiptResponse] = await Promise.all([
        window.electronAPI.invoke('get-payment-vouchers'),
        window.electronAPI.invoke('get-receipt-vouchers')
      ])

      if (paymentResponse.success) {
        const paymentData = paymentResponse.data || []
        setPaymentVouchers(paymentData)

        // تشخيص البيانات
        console.log('🔍 تشخيص سندات الدفع:', {
          totalPaymentVouchers: paymentData.length,
          samplePaymentVoucher: paymentData[0] || null
        })
      }
      if (receiptResponse.success) {
        const receiptData = receiptResponse.data || []
        setReceiptVouchers(receiptData)

        // تشخيص البيانات
        console.log('🔍 تشخيص سندات القبض:', {
          totalReceiptVouchers: receiptData.length,
          sampleReceiptVoucher: receiptData[0] || null
        })
      }
    } catch (_error) {
      messageApi.error('خطأ في تحميل السندات')
    }
    setLoading(false)
  }

  const loadBankAccounts = async () => {
    try {
      const response = await window.electronAPI.invoke('get-bank-accounts')
      Logger.info('VoucherManagement', 'Bank accounts response:', response)
      if (response.success) {
        setBankAccounts(response.data)
        Logger.info('VoucherManagement', 'Bank accounts loaded:', response.data)
      } else {
        Logger.error('VoucherManagement', 'Failed to load bank accounts:', response.message)
        messageApi.error('فشل في تحميل الحسابات المصرفية')
      }
    } catch (error) {
      Logger.error('VoucherManagement', 'خطأ في تحميل الحسابات المصرفية:', error)
      messageApi.error('فشل في تحميل الحسابات المصرفية')
    }
  }

  const loadCustomers = async () => {
    try {
      const response = await window.electronAPI.invoke('get-customers')
      if (response.success) {
        setCustomers(response.data)
        Logger.info('VoucherManagement', 'Customers loaded:', response.data.length)
      } else {
        Logger.error('VoucherManagement', 'Failed to load customers:', response.message)
      }
    } catch (error) {
      Logger.error('VoucherManagement', 'خطأ في تحميل العملاء:', error)
    }
  }

  const loadSuppliers = async () => {
    try {
      const response = await window.electronAPI.invoke('get-suppliers')
      if (response.success) {
        setSuppliers(response.data)
        Logger.info('VoucherManagement', 'Suppliers loaded:', response.data.length)
      } else {
        Logger.error('VoucherManagement', 'Failed to load suppliers:', response.message)
      }
    } catch (error) {
      Logger.error('VoucherManagement', 'خطأ في تحميل الموردين:', error)
    }
  }

  const generateVoucherNumber = async (type: 'payment' | 'receipt') => {
    try {
      const response = type === 'payment'
        ? await window.electronAPI.invoke('generate-payment-voucher-number')
        : await window.electronAPI.invoke('generate-receipt-voucher-number')
      
      if (response.success) {
        form.setFieldsValue({ voucher_number: response.data.voucherNumber })
      }
    } catch (_error) {
      messageApi.error('خطأ في إنشاء رقم السند')
    }
  }

  useEffect(() => {
    loadVouchers()
    loadBankAccounts()
    loadCustomers()
    loadSuppliers()
  }, [])

  // توليد رقم السند عند تغيير نوع السند
  useEffect(() => {
    if (modalVisible) {
      generateVoucherNumber(voucherType)
    }
  }, [voucherType, modalVisible])

  const handleSubmit = async (values: any) => {
    try {
      // التحقق من صحة المبلغ
      const amount = parseFloat(values.amount) || 0
      if (amount <= 0) {
        messageApi.error('يجب أن يكون المبلغ أكبر من الصفر')
        return
      }

      if (amount > 1000000) {
        messageApi.error('المبلغ كبير جداً، يرجى التحقق من القيمة المدخلة')
        return
      }

      // التحقق من صحة التاريخ
      const voucherDate = values[voucherType === 'payment' ? 'payment_date' : 'receipt_date']
      if (voucherDate && voucherDate.isAfter(dayjs())) {
        messageApi.error('لا يمكن أن يكون تاريخ السند في المستقبل')
        return
      }

      const voucherData = {
        ...values,
        amount: amount,
        payment_date: values.payment_date?.format('YYYY-MM-DD'),
        receipt_date: values.receipt_date?.format('YYYY-MM-DD'),
        created_by: 1 // يجب الحصول على معرف المستخدم الحالي
      }

      const response = voucherType === 'payment'
        ? await window.electronAPI.invoke('create-payment-voucher', voucherData)
        : await window.electronAPI.invoke('create-receipt-voucher', voucherData)

      if (response.success) {
        messageApi.success('تم إضافة ' + (voucherType === 'payment' ? 'سند الدفع' : 'سند القبض') + ' بنجاح')
        loadVouchers()
        setModalVisible(false)
        form.resetFields()
      } else {
        messageApi.error('فشل في إضافة ' + (voucherType === 'payment' ? 'سند الدفع' : 'سند القبض'))
      }
    } catch (_error) {
      messageApi.error('خطأ في حفّ السند')
    }
  }

  const _getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'paid': return 'green'
      case 'received': return 'green'
      case 'cancelled': return 'red'
      default: return 'default'
    }
  }

  const _getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتّار'
      case 'paid': return 'مدفوع'
      case 'received': return 'مستلم'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return 'نقداً'
      case 'check': return 'شيك'
      case 'bank_transfer': return 'تحويل بنكي'
      default: return method
    }
  }

  const getReferenceText = (referenceType: string, referenceId: number) => {
    if (!referenceType || !referenceId) return '-'

    switch (referenceType) {
      case 'sales_invoice': return 'فاتورة مبيعات #' + referenceId
      case 'purchase_invoice': return 'فاتورة مشتريات #' + referenceId
      case 'paint_invoice': return 'فاتورة دهان #' + referenceId
      default: return referenceType + ' #' + referenceId
    }
  }

  // دالة طباعة السند التقليدية
  const printVoucher = (voucher: any) => {
    try {
      // إنشاء نافذة طباعة جديدة
      const printWindow = window.open('', '_blank')
      if (!printWindow) {
        messageApi.error('لا يمكن فتح نافذة الطباعة')
        return
      }

      // محتوى HTML للطباعة
      const printContent = `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
          <meta charset="UTF-8">
          <title>سند ${voucher.type === 'payment' ? 'دفع' : 'قبض'} - ${voucher.voucher_number}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
            .header { text-align: center; margin-bottom: 30px; }
            .voucher-info { margin-bottom: 20px; }
            .amount { font-size: 18px; font-weight: bold; text-align: center; margin: 20px 0; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
            th { background-color: #f5f5f5; }
            .signature { margin-top: 50px; display: flex; justify-content: space-between; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>سند ${voucher.type === 'payment' ? 'دفع' : 'قبض'}</h2>
            <p>رقم السند: ${voucher.voucher_number}</p>
            <p>التاريخ: ${dayjs(voucher.date).format('YYYY/MM/DD')}</p>
          </div>

          <div class="voucher-info">
            <table>
              <tr><th>المبلغ</th><td>${voucher.amount} ريال</td></tr>
              <tr><th>طريقة الدفع</th><td>${getPaymentMethodText(voucher.payment_method)}</td></tr>
              <tr><th>البيان</th><td>${voucher.description || '-'}</td></tr>
              <tr><th>المرجع</th><td>${getReferenceText(voucher.reference_type, voucher.reference_id)}</td></tr>
            </table>
          </div>

          <div class="amount">
            المبلغ الإجمالي: ${voucher.amount} ريال سعودي
          </div>

          <div class="signature">
            <div>توقيع المستلم: ________________</div>
            <div>توقيع المحاسب: ________________</div>
          </div>
        </body>
        </html>
      `

      const printDoc = (printWindow as any).document
      if (printDoc) {
        printDoc.write(printContent)
        printDoc.close()
      }
      printWindow.focus()
      printWindow.print()
      printWindow.close()

      messageApi.success('تم إرسال السند للطباعة')
    } catch (error) {
      Logger.error('خطأ في طباعة السند:', error as string)
      messageApi.error('حدث خطأ أثناء طباعة السند')
    }
  }

  // دالة تصدير Excel للسندات
  const handleExportExcel = (type: 'payment' | 'receipt') => {
    try {
      const vouchersToExport = type === 'payment' ? paymentVouchers : receiptVouchers
      const typeText = type === 'payment' ? 'الدفع' : 'القبض'

      // تحضير البيانات للتصدير
      const exportData = vouchersToExport.map(voucher => ({
        'رقم السند': voucher.voucher_number,
        'التاريخ': dayjs(voucher.voucher_date).format('YYYY-MM-DD'),
        'المبلغ': voucher.amount || 0,
        'الوصف': voucher.description || '',
        'طريقة الدفع': voucher.payment_method === 'cash' ? 'نقدي' :
                      voucher.payment_method === 'check' ? 'شيك' :
                      voucher.payment_method === 'transfer' ? 'تحويل' : voucher.payment_method,
        'الحساب البنكي': voucher.bank_account_name || '',
        'المرجع': getReferenceText(voucher.reference_type, voucher.reference_id),
        'تاريخ الإنشاء': dayjs(voucher.created_at).format('YYYY-MM-DD HH:mm')
      }))

      // إنشاء ورقة العمل
      const worksheet = XLSX.utils.json_to_sheet(exportData)

      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 15 }, // رقم السند
        { wch: 12 }, // التاريخ
        { wch: 15 }, // المبلغ
        { wch: 30 }, // الوصف
        { wch: 12 }, // طريقة الدفع
        { wch: 20 }, // الحساب البنكي
        { wch: 25 }, // المرجع
        { wch: 18 }  // تاريخ الإنشاء
      ]
      worksheet['!cols'] = columnWidths

      // إنشاء المصنف
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'سندات ' + typeText)

      // إضافة ورقة إحصائيات
      const totalAmount = vouchersToExport.reduce((sum, v) => sum + (v.amount || 0), 0)
      const stats = [
        { 'البيان': 'إجمالي عدد السندات', 'القيمة': vouchersToExport.length },
        { 'البيان': 'إجمالي المبالغ', 'القيمة': totalAmount },
        { 'البيان': 'متوسط المبلغ', 'القيمة': vouchersToExport.length > 0 ? (totalAmount / vouchersToExport.length).toFixed(2) : 0 },
        { 'البيان': 'السندات النقدية', 'القيمة': vouchersToExport.filter(v => v.payment_method === 'cash').length },
        { 'البيان': 'السندات بالشيك', 'القيمة': vouchersToExport.filter(v => v.payment_method === 'check').length },
        { 'البيان': 'السندات بالتحويل', 'القيمة': vouchersToExport.filter(v => v.payment_method === 'transfer').length }
      ]

      const statsWorksheet = XLSX.utils.json_to_sheet(stats)
      statsWorksheet['!cols'] = [{ wch: 25 }, { wch: 15 }]
      XLSX.utils.book_append_sheet(workbook, statsWorksheet, 'الإحصائيات')

      // حفّ الملف
      const fileName = 'سندات_' + typeText + '_' + dayjs().format('YYYY-MM-DD_HH-mm') + '.xlsx'
      XLSX.writeFile(workbook, fileName)

      messageApi.success('تم تصدير البيانات بنجاح')
    } catch (error) {
      Logger.error('VoucherManagement', 'خطأ في تصدير Excel:', error)
      messageApi.error('فشل في تصدير البيانات')
    }
  }

  const paymentColumns = [
    {
      title: 'رقم السند',
      dataIndex: 'voucher_number',
      key: 'voucher_number',
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
          ₪ {amount?.toLocaleString() || 0}
        </span>
      ),
    },
    {
      title: 'الوصف',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'المرجع',
      key: 'reference',
      render: (record: any) => getReferenceText(record.reference_type, record.reference_id),
    },
    {
      title: 'تاريخ الدفع',
      dataIndex: 'voucher_date',
      key: 'voucher_date',
    },
    {
      title: 'طريقة الدفع',
      dataIndex: 'payment_method',
      key: 'payment_method',
      render: (method: string) => getPaymentMethodText(method),
    },
    {
      title: 'الحساب البنكي',
      dataIndex: 'bank_account_name',
      key: 'bank_account_name',
      render: (name: string) => name || '-',
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (record: any) => (
        <Space>
          <UnifiedPrintButton
            data={{
              id: record.id,
              title: record.voucher_type === 'payment' ? 'سند دفع' : 'سند قبض',
              subtitle: `رقم: ${record.voucher_number}`,
              date: record.voucher_date,
              notes: record.description,
              items: [{
                id: 1,
                name: record.voucher_type === 'payment' ? 'مبلغ مدفوع' : 'مبلغ مقبوض',
                description: record.description,
                quantity: 1,
                unit: 'مبلغ',
                unitPrice: record.amount,
                total: record.amount
              }],
              subtotal: record.amount,
              total: record.amount
            }}
            type="receipt"
            subType={record.voucher_type}
            buttonText="طباعة"
            size="small"
            showDropdown={true}
            _documentId={`voucher_${record.id}`}
            onAfterPrint={() => message.success('تم طباعة السند بنجاح')}
            onError={() => message.error('فشل في طباعة السند')}
          />

          <Button
            type="default"
            icon={<PrinterOutlined />}
            size="small"
            onClick={() => printVoucher(record)}
            title="طباعة تقليدية"
          >
            طباعة قديمة
          </Button>
        </Space>
      ),
    },
  ]

  const receiptColumns = [
    {
      title: 'رقم السند',
      dataIndex: 'voucher_number',
      key: 'voucher_number',
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          ₪ {amount?.toLocaleString() || 0}
        </span>
      ),
    },
    {
      title: 'الوصف',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'المرجع',
      key: 'reference',
      render: (record: any) => getReferenceText(record.reference_type, record.reference_id),
    },
    {
      title: 'تاريخ القبض',
      dataIndex: 'voucher_date',
      key: 'voucher_date',
    },
    {
      title: 'طريقة الدفع',
      dataIndex: 'payment_method',
      key: 'payment_method',
      render: (method: string) => getPaymentMethodText(method),
    },
    {
      title: 'الحساب البنكي',
      dataIndex: 'bank_account_name',
      key: 'bank_account_name',
      render: (name: string) => name || '-',
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (record: any) => (
        <Space>
          <UnifiedPrintButton
            data={{
              id: record.id,
              title: record.voucher_type === 'payment' ? 'سند دفع' : 'سند قبض',
              subtitle: `رقم: ${record.voucher_number}`,
              date: record.voucher_date,
              notes: record.description,
              items: [{
                id: 1,
                name: record.voucher_type === 'payment' ? 'مبلغ مدفوع' : 'مبلغ مقبوض',
                description: record.description,
                quantity: 1,
                unit: 'مبلغ',
                unitPrice: record.amount,
                total: record.amount
              }],
              subtotal: record.amount,
              total: record.amount
            }}
            type="receipt"
            subType={record.voucher_type}
            buttonText="طباعة"
            size="small"
            showDropdown={true}
            _documentId={`voucher_${record.id}`}
            onAfterPrint={() => message.success('تم طباعة السند بنجاح')}
            onError={() => message.error('فشل في طباعة السند')}
          />

          <Button
            type="default"
            icon={<PrinterOutlined />}
            size="small"
            onClick={() => printVoucher(record)}
            title="طباعة تقليدية"
          >
            طباعة قديمة
          </Button>
        </Space>
      ),
    },
  ]

  const paymentStats = {
    total: paymentVouchers.length,
    pending: paymentVouchers.filter((v: any) => v.status === 'pending').length,
    paid: paymentVouchers.filter((v: any) => v.status === 'paid').length,
    totalAmount: paymentVouchers.reduce((sum: number, v: any) => sum + (v.amount || 0), 0)
  }

  const receiptStats = {
    total: receiptVouchers.length,
    pending: receiptVouchers.filter((v: any) => v.status === 'pending').length,
    received: receiptVouchers.filter((v: any) => v.status === 'received').length,
    totalAmount: receiptVouchers.reduce((sum: number, v: any) => sum + (v.amount || 0), 0)
  }

  const showModal = (type: 'payment' | 'receipt') => {
    setVoucherType(type)
    setModalVisible(true)
    generateVoucherNumber(type)
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>📄 إدارة السندات</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            إدارة سندات الدفع والقبض
          </p>
        </div>
        <Button 
          type="default" 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
        >
          العودة
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="سندات الدفع"
              value={paymentStats.total}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<ArrowUpOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="سندات القبض"
              value={receiptStats.total}
              valueStyle={{ color: '#52c41a' }}
              prefix={<ArrowDownOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي المدفوعات"
              value={paymentStats.totalAmount}
              valueStyle={{ color: '#ff4d4f' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي المقبوضات"
              value={receiptStats.totalAmount}
              valueStyle={{ color: '#52c41a' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Tabs
          defaultActiveKey="payment"
          items={[
            {
              key: 'payment',
              label: (
                <span>
                  <ArrowUpOutlined style={{ color: '#ff4d4f' }} />
                  سندات الدفع ({paymentStats.total})
                </span>
              ),
              children: (
                <div>
                  <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => showModal('payment')}
                    >
                      إضافة سند دفع
                    </Button>
                    <Space>
                      <Button
                        type="default"
                        icon={<FileExcelOutlined />}
                        onClick={() => handleExportExcel('payment')}
                        style={{ color: '#52c41a', borderColor: '#52c41a' }}
                      >
                        تصدير Excel
                      </Button>

                    </Space>
                  </div>
                  <Table
                    columns={paymentColumns}
                    dataSource={paymentVouchers}
                    rowKey="id"
                    loading={loading}
                    pagination={{ pageSize: 10 }}
                    locale={{
                      emptyText: paymentVouchers.length === 0 ? 'لا توجد سندات دفع' : 'لا توجد بيانات'
                    }}
                  />
                </div>
              )
            },
            {
              key: 'receipt',
              label: (
                <span>
                  <ArrowDownOutlined style={{ color: '#52c41a' }} />
                  سندات القبض ({receiptStats.total})
                </span>
              ),
              children: (
                <div>
                  <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => showModal('receipt')}
                    >
                      إضافة سند قبض
                    </Button>
                    <Space>
                      <Button
                        type="default"
                        icon={<FileExcelOutlined />}
                        onClick={() => handleExportExcel('receipt')}
                        style={{ color: '#52c41a', borderColor: '#52c41a' }}
                      >
                        تصدير Excel
                      </Button>
                    </Space>
                  </div>
                  <Table
                    columns={receiptColumns}
                    dataSource={receiptVouchers}
                    rowKey="id"
                    loading={loading}
                    pagination={{ pageSize: 10 }}
                    locale={{
                      emptyText: receiptVouchers.length === 0 ? 'لا توجد سندات قبض' : 'لا توجد بيانات'
                    }}
                  />
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* نموذج إضافة سند */}
      <Modal
        title={voucherType === 'payment' ? 'إضافة سند دفع جديد' : 'إضافة سند قبض جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="voucher_number"
                label="رقم السند"
                rules={[{ required: true, message: 'يرجى إدخال رقم السند' }]}
              >
                <Input 
                  placeholder={voucherType === 'payment' ? 'PV000001' : 'RV000001'}
                  disabled
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="amount"
                label="المبلغ"
                rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  formatter={value => ('₪ ' + value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value: string | undefined) => value ? parseFloat(value.replace(/₪\s?|(,*)/g, '')) || 0 : 0}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={voucherType === 'payment' ? 'payee_name' : 'payer_name'}
                label={voucherType === 'payment' ? 'المستفيد (مورد)' : 'الدافع (عميل)'}
                rules={[{ required: true, message: 'يرجى اختيار ' + (voucherType === 'payment' ? 'المستفيد' : 'الدافع') }]}
              >
                <Select
                  placeholder={voucherType === 'payment' ? 'اختر المورد المستفيد' : 'اختر العميل الدافع'}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                  onOpenChange={(open) => {
                    if (open) {
                      Logger.info('VoucherManagement', voucherType + ' dropdown opened')
                    }
                  }}
                >
                  {voucherType === 'payment'
                    ? suppliers.map((supplier: any) => (
                        <Select.Option key={supplier.id} value={supplier.name}>
                          {supplier.name} - {supplier.phone || 'لا يوجد هاتف'}
                        </Select.Option>
                      ))
                    : customers.map((customer: any) => (
                        <Select.Option key={customer.id} value={customer.name}>
                          {customer.name} - {customer.phone || 'لا يوجد هاتف'}
                        </Select.Option>
                      ))
                  }
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={voucherType === 'payment' ? 'payment_date' : 'receipt_date'}
                label={voucherType === 'payment' ? 'تاريخ الدفع' : 'تاريخ القبض'}
                rules={[{ required: true, message: 'يرجى اختيار ' + (voucherType === 'payment' ? 'تاريخ الدفع' : 'تاريخ القبض') }]}
              >
                <DatePicker 
                  style={{ width: '100%' }}
                  placeholder={'اختر ' + (voucherType === 'payment' ? 'تاريخ الدفع' : 'تاريخ القبض')}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="payment_method"
                label="طريقة الدفع"
                rules={[{ required: true, message: 'يرجى اختيار طريقة الدفع' }]}
              >
                <Select placeholder="اختر طريقة الدفع">
                  <Select.Option value="cash">نقداً</Select.Option>
                  <Select.Option value="check">شيك</Select.Option>
                  <Select.Option value="bank_transfer">تحويل بنكي</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="bank_account_id"
                label="الحساب المصرفي (اختياري)"
              >
                <Select
                  placeholder="اختر الحساب المصرفي"
                  allowClear
                  onOpenChange={(open) => {
                    if (open) {
                      Logger.info('VoucherManagement', 'Dropdown opened, bankAccounts:', bankAccounts)
                    }
                  }}
                >
                  {bankAccounts.map((account: any) => (
                    <Select.Option key={account.id} value={account.id}>
                      {account.bank_name} - {account.account_name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="الوصف"
            rules={[{ required: true, message: 'يرجى إدخال وصف السند' }]}
          >
            <Input.TextArea 
              placeholder={'وصف ' + (voucherType === 'payment' ? 'سند الدفع' : 'سند القبض')}
              rows={3}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {voucherType === 'payment' ? 'إضافة سند الدفع' : 'إضافة سند القبض'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false)
                form.resetFields()
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default VoucherManagement
