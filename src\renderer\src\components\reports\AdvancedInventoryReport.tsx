/**
 * تقرير تحليل المخزون المتقدم
 * تقرير شامل لتحليل المخزون مع مؤشرات الأداء
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'
import { SafeLogger as Logger } from '../../utils/logger'

interface AdvancedInventoryData {
  item_id: number
  item_code: string
  item_name: string
  category_name: string
  unit: string
  current_quantity: number
  reserved_quantity: number
  available_quantity: number
  cost_price: number
  sale_price: number
  total_value: number
  min_quantity: number
  max_quantity: number
  reorder_point: number
  turnover_rate: number
  days_of_supply: number
  last_movement_date: string
  movement_frequency: number
  profit_margin: number
  status: 'normal' | 'low' | 'high' | 'out' | 'overstock'
  warehouse_name: string
  supplier_name: string
}

const AdvancedInventoryReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any) => {
    try {
      Logger.info('AdvancedInventoryReport', 'بدء إنشاء تقرير المخزون المتقدم:', filters)

      // استدعاء API للحصول على بيانات المخزون المتقدمة
      const response = await window.electronAPI?.invoke('get-advanced-inventory-analysis', {
        startDate: filters.startDate,
        endDate: filters.endDate,
        warehouseId: filters.warehouseId,
        categoryId: filters.categoryId,
        itemId: filters.itemId,
        sortBy: filters.sortBy || 'total_value',
        sortOrder: filters.sortOrder || 'desc'
      })

      if (!response?.success) {
        throw new Error(response?.message || 'فشل في تحميل بيانات التقرير')
      }

      const data = response.data || []

      // حساب الإحصائيات المتقدمة
      const totalItems = data.length
      const totalValue = data.reduce((sum: number, item: AdvancedInventoryData) => sum + item.total_value, 0)
      const totalQuantity = data.reduce((sum: number, item: AdvancedInventoryData) => sum + item.current_quantity, 0)
      const totalReserved = data.reduce((sum: number, item: AdvancedInventoryData) => sum + item.reserved_quantity, 0)
      const totalAvailable = data.reduce((sum: number, item: AdvancedInventoryData) => sum + item.available_quantity, 0)

      const lowStockItems = data.filter((item: AdvancedInventoryData) => item.status === 'low').length
      const outOfStockItems = data.filter((item: AdvancedInventoryData) => item.status === 'out').length
      const overstockItems = data.filter((item: AdvancedInventoryData) => item.status === 'overstock').length
      const normalStockItems = data.filter((item: AdvancedInventoryData) => item.status === 'normal').length

      const averageTurnover = data.length > 0 ? data.reduce((sum: number, item: AdvancedInventoryData) => sum + item.turnover_rate, 0) / data.length : 0
      const averageDaysSupply = data.length > 0 ? data.reduce((sum: number, item: AdvancedInventoryData) => sum + item.days_of_supply, 0) / data.length : 0
      const averageProfitMargin = data.length > 0 ? data.reduce((sum: number, item: AdvancedInventoryData) => sum + item.profit_margin, 0) / data.length : 0

      // تحديد الأعمدة
      const columns = [
        {
          key: 'item_code',
          title: 'كود الصنف',
          align: 'center' as const,
          format: 'text' as const,
          width: '120px'
        },
        {
          key: 'item_name',
          title: 'اسم الصنف',
          align: 'right' as const,
          format: 'text' as const,
          width: '200px'
        },
        {
          key: 'category_name',
          title: 'الفئة',
          align: 'center' as const,
          format: 'text' as const,
          width: '120px'
        },
        {
          key: 'warehouse_name',
          title: 'المخزن',
          align: 'center' as const,
          format: 'text' as const,
          width: '120px'
        },
        {
          key: 'current_quantity',
          title: 'الكمية الحالية',
          align: 'center' as const,
          format: 'number' as const,
          width: '120px'
        },
        {
          key: 'available_quantity',
          title: 'الكمية المتاحة',
          align: 'center' as const,
          format: 'number' as const,
          width: '120px'
        },
        {
          key: 'total_value',
          title: 'القيمة الإجمالية',
          align: 'center' as const,
          format: 'currency' as const,
          width: '140px'
        },
        {
          key: 'turnover_rate',
          title: 'معدل الدوران',
          align: 'center' as const,
          format: 'number' as const,
          width: '120px'
        },
        {
          key: 'days_of_supply',
          title: 'أيام التوريد',
          align: 'center' as const,
          format: 'number' as const,
          width: '120px'
        },
        {
          key: 'profit_margin',
          title: 'هامش الربح %',
          align: 'center' as const,
          format: 'percentage' as const,
          width: '120px'
        },
        {
          key: 'status',
          title: 'الحالة',
          align: 'center' as const,
          format: 'text' as const,
          width: '100px'
        }
      ]

      return {
        title: 'تقرير تحليل المخزون المتقدم',
        data,
        columns,
        summary: {
          totalItems,
          totalValue,
          totalQuantity,
          totalReserved,
          totalAvailable,
          lowStockItems,
          outOfStockItems,
          overstockItems,
          normalStockItems,
          averageTurnover: Number(averageTurnover.toFixed(2)),
          averageDaysSupply: Number(averageDaysSupply.toFixed(1)),
          averageProfitMargin: Number(averageProfitMargin.toFixed(2)),
          averageValue: totalItems > 0 ? Number((totalValue / totalItems).toFixed(2)) : 0
        },
        metadata: {
          reportType: 'inventory_detailed' as ReportType,
          totalRecords: data.length,
          dateRange: filters.startDate && filters.endDate ? `${filters.startDate} - ${filters.endDate}` : 'جميع الفترات',
          warehouse: filters.warehouseId ? 'مخزن محدد' : 'جميع المخازن',
          category: filters.categoryId ? 'فئة محددة' : 'جميع الفئات',
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام تحليل المخزون المتقدم'
        }
      }
    } catch (error) {
      Logger.error('AdvancedInventoryReport', 'خطأ في إنشاء التقرير:', error)
      throw error
    }
  }

  return (
    <UniversalReport
      reportType={'inventory_detailed' as ReportType}
      title="تقرير تحليل المخزون المتقدم"
      description="تحليل شامل للمخزون مع مؤشرات الأداء ومعدلات الدوران والتنبؤات"
      onGenerateReport={generateReport}
      showDateRange={true}
      showWarehouseFilter={true}
      showCategoryFilter={true}
      showItemFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="advanced_inventory_report"
      defaultFilters={{
        dateRange: null, // كل المدة افتراضياً
        sortBy: 'total_value',
        sortOrder: 'desc'
      }}
    />
  )
}

export default AdvancedInventoryReport
