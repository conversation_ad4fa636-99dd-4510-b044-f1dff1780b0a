import React, { Component, ReactNode } from 'react'
import { <PERSON><PERSON>, But<PERSON>, Card, Space, Typography } from 'antd'
import { ExclamationCircleOutlined, ReloadOutlined } from '@ant-design/icons'
import { logger as Logger } from './../../utils/logger'

const { Title, Text } = Typography

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    Logger.error('ErrorBoundary', '<PERSON>rrorBoundary caught an error:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // استدعاء callback إذا كان موجوداً
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }

  handleReload = () => {
    window.location.reload()
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  override render() {
    if (this.state.hasError) {
      // يمكن عرض fallback مخصص
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Card style={{ margin: '20px', textAlign: 'center' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <ExclamationCircleOutlined style={{ fontSize: '48px', color: '#ff4d4f' }} />
            
            <div>
              <Title level={3} style={{ color: '#ff4d4f' }}>
                حدث خطأ غير متوقع
              </Title>
              <Text type="secondary">
                عذراً، حدث خطأ أثناء تحميل هذا القسم
              </Text>
            </div>

            {this.state.error && (
              <Alert
                type="error"
                message="تفاصيل الخطأ"
                description={
                  <div style={{ textAlign: 'left', direction: 'ltr' }}>
                    <Text code>{this.state.error.message}</Text>
                    {this.state.errorInfo?.componentStack && (
                      <details style={{ marginTop: '8px' }}>
                        <summary>Component Stack</summary>
                        <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </details>
                    )}
                  </div>
                }
                showIcon
                style={{ textAlign: 'left' }}
              />
            )}

            <Space>
              <Button 
                type="primary" 
                icon={<ReloadOutlined />}
                onClick={this.handleReload}
              >
                إعادة تحميل الصفحة
              </Button>
              <Button onClick={this.handleReset}>
                المحاولة مرة أخرى
              </Button>
            </Space>
          </Space>
        </Card>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
