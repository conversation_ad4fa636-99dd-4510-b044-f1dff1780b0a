import { HardwareIdGenerator } from './hardwareId'
import { ActivationCodes, LicenseType, LicenseInfo } from './activationCodes'
import { LicenseStorage, StoredLicenseData } from './licenseStorage'
import { Logger } from '../utils/logger'

/**
 * نتيجة عملية التفعيل
 */
export interface ActivationResult {
  success: boolean
  message: string
  licenseInfo?: LicenseInfo
  expiryDate?: Date | null
  daysRemaining?: number
}

/**
 * حالة التفعيل
 */
export interface ActivationStatus {
  isActivated: boolean
  licenseType?: LicenseType
  activationDate?: Date
  expiryDate?: Date | null
  daysRemaining?: number
  hardwareId?: string
}

/**
 * مدير التفعيل الرئيسي
 */
export class ActivationManager {
  private static instance: ActivationManager
  private hardwareIdGenerator: HardwareIdGenerator
  private licenseStorage: LicenseStorage
  private checkInterval: NodeJS.Timeout | null = null

  private constructor() {
    this.hardwareIdGenerator = HardwareIdGenerator.getInstance()
    this.licenseStorage = LicenseStorage.getInstance()
  }

  public static getInstance(): ActivationManager {
    if (!ActivationManager.instance) {
      ActivationManager.instance = new ActivationManager()
    }
    return ActivationManager.instance
  }

  /**
   * تفعيل البرنامج برقم التفعيل
   */
  public async activateWithCode(activationCode: string): Promise<ActivationResult> {
    try {
      // التحقق من صحة رقم التفعيل
      const licenseInfo = ActivationCodes.validateActivationCode(activationCode)
      if (!licenseInfo) {
        return {
          success: false,
          message: 'رقم التفعيل غير صحيح. تأكد من الرقم المدخل.'
        }
      }

      // التحقق من رقم الحظر
      if (licenseInfo.type === LicenseType.BLOCK) {
        await this.deactivateLicense()
        return {
          success: true,
          message: 'تم إلغاء تفعيل البرنامج بنجاح.',
          licenseInfo
        }
      }

      // الحصول على معرف الجهاز
      const hardwareId = await this.hardwareIdGenerator.getHardwareId()

      // حساب تاريخ انتهاء الصلاحية
      const expiryDate = ActivationCodes.calculateExpiryDate(licenseInfo)

      // إنشاء بيانات الترخيص
      const licenseData: StoredLicenseData = {
        activationCode: activationCode.toUpperCase(),
        licenseType: licenseInfo.type,
        activationDate: new Date().toISOString(),
        expiryDate: expiryDate ? expiryDate.toISOString() : null,
        hardwareId,
        isActive: true
      }

      // حفظ بيانات الترخيص
      const saved = await this.licenseStorage.saveLicenseData(licenseData)
      if (!saved) {
        return {
          success: false,
          message: 'فشل في حفظ بيانات التفعيل. حاول مرة أخرى.'
        }
      }

      // بدء الفحص الدوري
      this.startPeriodicCheck()

      const daysRemaining = expiryDate ? ActivationCodes.getDaysRemaining(expiryDate) : -1

      return {
        success: true,
        message: `تم تفعيل البرنامج بنجاح! ${ActivationCodes.formatLicenseInfo(licenseInfo, expiryDate)}`,
        licenseInfo,
        expiryDate,
        daysRemaining
      }
    } catch (error) {
      Logger.error('ActivationManager', 'خطأ في عملية التفعيل:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء التفعيل. حاول مرة أخرى.'
      }
    }
  }

  /**
   * التحقق من حالة التفعيل
   */
  public async checkActivationStatus(): Promise<ActivationStatus> {
    try {
      const licenseInfo = await this.licenseStorage.getLicenseInfo()
      
      if (!licenseInfo.isActivated) {
        return { isActivated: false }
      }

      // التحقق من معرف الجهاز
      const storedLicense = await this.licenseStorage.loadLicenseData()
      if (storedLicense) {
        const isValidHardware = await this.hardwareIdGenerator.validateHardwareId(storedLicense.hardwareId)
        if (!isValidHardware) {
          Logger.warn('ActivationManager', 'معرف الجهاز غير متطابق - إلغاء التفعيل')
          await this.deactivateLicense()
          return { isActivated: false }
        }
      }

      // التحقق من انتهاء الصلاحية
      if (licenseInfo.expiryDate && ActivationCodes.isLicenseExpired(licenseInfo.expiryDate)) {
        Logger.warn('ActivationManager', 'انتهت صلاحية الترخيص في:', licenseInfo.expiryDate)
        await this.licenseStorage.updateLicenseStatus(false)

        // حساب عدد الأيام منذ انتهاء الترخيص
        const expiredDays = Math.floor((new Date().getTime() - licenseInfo.expiryDate.getTime()) / (1000 * 60 * 60 * 24))

        return {
          isActivated: false,
          licenseType: licenseInfo.licenseType,
          expiryDate: licenseInfo.expiryDate,
          daysRemaining: -expiredDays // رقم سالب يدل على انتهاء الترخيص
        }
      }

      const hardwareId = await this.hardwareIdGenerator.getHardwareId()

      // تحذير المستخدم قبل انتهاء الترخيص
      if (licenseInfo.daysRemaining !== null && licenseInfo.daysRemaining !== undefined && licenseInfo.daysRemaining <= 7 && licenseInfo.daysRemaining > 0) {
        Logger.warn('ActivationManager', `تحذير: سينتهي الترخيص خلال ${licenseInfo.daysRemaining} أيام`)
      }

      return {
        isActivated: true,
        licenseType: licenseInfo.licenseType,
        activationDate: licenseInfo.activationDate,
        expiryDate: licenseInfo.expiryDate,
        daysRemaining: licenseInfo.daysRemaining,
        hardwareId
      }
    } catch (error) {
      Logger.error('ActivationManager', 'خطأ في فحص حالة التفعيل:', error)
      return { isActivated: false }
    }
  }

  /**
   * إلغاء تفعيل البرنامج
   */
  public async deactivateLicense(): Promise<boolean> {
    try {
      const deleted = await this.licenseStorage.deleteLicenseData()
      this.stopPeriodicCheck()
      Logger.info('ActivationManager', 'تم إلغاء تفعيل البرنامج')
      return deleted
    } catch (error) {
      Logger.error('ActivationManager', 'خطأ في إلغاء التفعيل:', error)
      return false
    }
  }

  /**
   * بدء الفحص الدوري للتفعيل
   */
  public startPeriodicCheck(): void {
    // إيقاف الفحص السابق إن وجد
    this.stopPeriodicCheck()

    // فحص كل ساعة
    this.checkInterval = setInterval(async () => {
      const status = await this.checkActivationStatus()
      if (!status.isActivated) {
        Logger.warn('ActivationManager', 'تم اكتشاف مشكلة في التفعيل - إيقاف الفحص الدوري')
        this.stopPeriodicCheck()
      }
    }, 60 * 60 * 1000) // كل ساعة

    Logger.info('ActivationManager', 'تم بدء الفحص الدوري للتفعيل')
  }

  /**
   * إيقاف الفحص الدوري
   */
  public stopPeriodicCheck(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
      Logger.info('ActivationManager', 'تم إيقاف الفحص الدوري للتفعيل')
    }
  }

  /**
   * الحصول على معرف الجهاز الحالي
   */
  public async getCurrentHardwareId(): Promise<string> {
    return await this.hardwareIdGenerator.getHardwareId()
  }

  /**
   * التحقق من صحة رقم التفعيل دون تفعيل
   */
  public validateActivationCode(code: string): LicenseInfo | null {
    return ActivationCodes.validateActivationCode(code)
  }

  /**
   * الحصول على معلومات مفصلة عن الترخيص
   */
  public async getDetailedLicenseInfo(): Promise<{
    isActivated: boolean
    activationCode?: string
    licenseType?: LicenseType
    activationDate?: string
    expiryDate?: string | null
    daysRemaining?: number
    hardwareId?: string
    formattedInfo?: string
  }> {
    try {
      const storedLicense = await this.licenseStorage.loadLicenseData()
      
      if (!storedLicense || !storedLicense.isActive) {
        return { isActivated: false }
      }

      const expiryDate = storedLicense.expiryDate ? new Date(storedLicense.expiryDate) : null
      const daysRemaining = expiryDate ? ActivationCodes.getDaysRemaining(expiryDate) : -1
      
      const licenseInfo = ActivationCodes.validateActivationCode(storedLicense.activationCode)
      const formattedInfo = licenseInfo ? ActivationCodes.formatLicenseInfo(licenseInfo, expiryDate) : ''

      return {
        isActivated: true,
        activationCode: storedLicense.activationCode,
        licenseType: storedLicense.licenseType,
        activationDate: storedLicense.activationDate,
        expiryDate: storedLicense.expiryDate,
        daysRemaining,
        hardwareId: storedLicense.hardwareId,
        formattedInfo
      }
    } catch (error) {
      Logger.error('ActivationManager', 'خطأ في الحصول على معلومات الترخيص المفصلة:', error)
      return { isActivated: false }
    }
  }
}
