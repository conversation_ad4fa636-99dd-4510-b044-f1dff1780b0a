/**
 * تعريفات DOM للاختبارات
 */

declare global {
  // Basic DOM types
  interface HTMLFormElement extends HTMLElement {
    elements: HTMLFormControlsCollection;
    length: number;
    submit(): void;
    reset(): void;
  }

  interface HTMLCanvasElement extends HTMLElement {
    width: number;
    height: number;
    getContext(contextId: '2d'): CanvasRenderingContext2D | null;
    getContext(contextId: string): any;
  }
  
  interface CanvasRenderingContext2D {
    fillRect(x: number, y: number, w: number, h: number): void;
    clearRect(x: number, y: number, w: number, h: number): void;
    getImageData(sx: number, sy: number, sw: number, sh: number): ImageData;
    putImageData(imagedata: ImageData, dx: number, dy: number): void;
    createImageData(sw: number, sh: number): ImageData;
    setTransform(a: number, b: number, c: number, d: number, e: number, f: number): void;
    resetTransform(): void;
  }

  interface ImageData {
    readonly data: Uint8ClampedArray;
    readonly height: number;
    readonly width: number;
  }

  interface DOMRect {
    readonly x: number;
    readonly y: number;
    readonly width: number;
    readonly height: number;
    readonly top: number;
    readonly right: number;
    readonly bottom: number;
    readonly left: number;
  }

  interface DOMRectInit {
    x?: number;
    y?: number;
    width?: number;
    height?: number;
  }

  interface Range {
    readonly collapsed: boolean;
    readonly commonAncestorContainer: Node;
    readonly endContainer: Node;
    readonly endOffset: number;
    readonly startContainer: Node;
    readonly startOffset: number;
  }

  interface Selection {
    readonly anchorNode: Node | null;
    readonly anchorOffset: number;
    readonly focusNode: Node | null;
    readonly focusOffset: number;
    readonly isCollapsed: boolean;
    readonly rangeCount: number;
    readonly type: string;
  }

  interface EventInit {
    bubbles?: boolean;
    cancelable?: boolean;
    composed?: boolean;
  }

  interface CustomEventInit extends EventInit {
    detail?: any;
  }

  interface MediaQueryListEvent extends Event {
    readonly matches: boolean;
    readonly media: string;
  }

  interface IdleDeadline {
    readonly didTimeout: boolean;
    timeRemaining(): number;
  }

  interface IdleRequestCallback {
    (deadline: IdleDeadline): void;
  }

  interface IdleRequestOptions {
    timeout?: number;
  }

  interface WorkerOptions {
    type?: 'classic' | 'module';
    credentials?: 'omit' | 'same-origin' | 'include';
    name?: string;
  }

  interface AbstractWorker {
    onerror: ((this: AbstractWorker, ev: ErrorEvent) => any) | null;
  }

  // Crypto types
  type KeyFormat = 'raw' | 'spki' | 'pkcs8' | 'jwk';
  type KeyUsage = 'encrypt' | 'decrypt' | 'sign' | 'verify' | 'deriveKey' | 'deriveBits' | 'wrapKey' | 'unwrapKey';
  type AlgorithmIdentifier = string | object;
  type BufferSource = ArrayBufferView | ArrayBuffer;

  interface JsonWebKey {
    alg?: string;
    crv?: string;
    d?: string;
    dp?: string;
    dq?: string;
    e?: string;
    ext?: boolean;
    k?: string;
    key_ops?: string[];
    kty?: string;
    n?: string;
    oth?: RsaOtherPrimesInfo[];
    p?: string;
    q?: string;
    qi?: string;
    use?: string;
    x?: string;
    y?: string;
  }

  interface RsaOtherPrimesInfo {
    d?: string;
    r?: string;
    t?: string;
  }

  interface CryptoKey {
    readonly algorithm: object;
    readonly extractable: boolean;
    readonly type: string;
    readonly usages: KeyUsage[];
  }

  interface CryptoKeyPair {
    readonly privateKey: CryptoKey;
    readonly publicKey: CryptoKey;
  }

  interface SubtleCrypto {
    decrypt(algorithm: any, key: CryptoKey, data: BufferSource): Promise<ArrayBuffer>;
    deriveBits(algorithm: any, baseKey: CryptoKey, length: number): Promise<ArrayBuffer>;
    deriveKey(algorithm: any, baseKey: CryptoKey, derivedKeyType: any, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKey>;
    digest(algorithm: AlgorithmIdentifier, data: BufferSource): Promise<ArrayBuffer>;
    encrypt(algorithm: any, key: CryptoKey, data: BufferSource): Promise<ArrayBuffer>;
    exportKey(format: KeyFormat, key: CryptoKey): Promise<JsonWebKey | ArrayBuffer>;
    generateKey(algorithm: any, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKeyPair | CryptoKey>;
    importKey(format: KeyFormat, keyData: JsonWebKey | BufferSource, algorithm: any, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKey>;
    sign(algorithm: any, key: CryptoKey, data: BufferSource): Promise<ArrayBuffer>;
    unwrapKey(format: KeyFormat, wrappedKey: BufferSource, unwrappingKey: CryptoKey, unwrapAlgorithm: any, unwrappedKeyAlgorithm: any, extractable: boolean, keyUsages: KeyUsage[]): Promise<CryptoKey>;
    verify(algorithm: any, key: CryptoKey, signature: BufferSource, data: BufferSource): Promise<boolean>;
    wrapKey(format: KeyFormat, key: CryptoKey, wrappingKey: CryptoKey, wrapAlgorithm: any): Promise<ArrayBuffer>;
  }

  interface Crypto {
    readonly subtle: SubtleCrypto;
    getRandomValues<T extends ArrayBufferView | null>(array: T): T;
  }

  interface PerformanceNavigation {
    readonly type: number;
    readonly redirectCount: number;
  }

  interface PerformanceTiming {
    readonly navigationStart: number;
    readonly unloadEventStart: number;
    readonly unloadEventEnd: number;
    readonly redirectStart: number;
    readonly redirectEnd: number;
    readonly fetchStart: number;
    readonly domainLookupStart: number;
    readonly domainLookupEnd: number;
    readonly connectStart: number;
    readonly connectEnd: number;
    readonly secureConnectionStart: number;
    readonly requestStart: number;
    readonly responseStart: number;
    readonly responseEnd: number;
    readonly domLoading: number;
    readonly domInteractive: number;
    readonly domContentLoadedEventStart: number;
    readonly domContentLoadedEventEnd: number;
    readonly domComplete: number;
    readonly loadEventStart: number;
    readonly loadEventEnd: number;
  }

  interface Performance {
    readonly navigation: PerformanceNavigation;
    readonly timing: PerformanceTiming;
    now(): number;
  }

  interface MutationRecord {
    readonly type: string;
    readonly target: Node;
    readonly addedNodes: NodeList;
    readonly removedNodes: NodeList;
    readonly previousSibling: Node | null;
    readonly nextSibling: Node | null;
    readonly attributeName: string | null;
    readonly attributeNamespace: string | null;
    readonly oldValue: string | null;
  }

  interface MutationCallback {
    (mutations: MutationRecord[], observer: MutationObserver): void;
  }

  type BlobPart = BufferSource | Blob | string;

  interface BlobPropertyBag {
    type?: string;
    endings?: 'transparent' | 'native';
  }

  interface Headers {
    append(name: string, value: string): void;
    delete(name: string): void;
    get(name: string): string | null;
    has(name: string): boolean;
    set(name: string, value: string): void;
    forEach(callbackfn: (value: string, key: string, parent: Headers) => void, thisArg?: any): void;
  }

  interface ReadableStream<R = any> {
    readonly locked: boolean;
    cancel(reason?: any): Promise<void>;
    getReader(): ReadableStreamDefaultReader<R>;
  }

  interface ReadableStreamDefaultReader<R = any> {
    readonly closed: Promise<undefined>;
    cancel(reason?: any): Promise<void>;
    read(): Promise<ReadableStreamDefaultReadResult<R>>;
    releaseLock(): void;
  }

  interface ReadableStreamDefaultReadResult<T> {
    done: boolean;
    value: T;
  }

  interface Response {
    readonly body: ReadableStream<Uint8Array> | null;
    readonly bodyUsed: boolean;
    readonly headers: Headers;
    readonly ok: boolean;
    readonly redirected: boolean;
    readonly status: number;
    readonly statusText: string;
    readonly type: string;
    readonly url: string;
    clone(): Response;
    arrayBuffer(): Promise<ArrayBuffer>;
    blob(): Promise<Blob>;
    formData(): Promise<FormData>;
    json(): Promise<any>;
    text(): Promise<string>;
  }

  // Browser APIs
  interface NotificationOptions {
    body?: string;
    icon?: string;
    image?: string;
    badge?: string;
    sound?: string;
    tag?: string;
    data?: any;
    vibrate?: number | number[];
    renotify?: boolean;
    silent?: boolean;
    requireInteraction?: boolean;
    actions?: NotificationAction[];
    timestamp?: number;
  }

  interface NotificationAction {
    action: string;
    title: string;
    icon?: string;
  }

  interface Notification {
    readonly actions: ReadonlyArray<NotificationAction>;
    readonly badge: string;
    readonly body: string;
    readonly data: any;
    readonly dir: NotificationDirection;
    readonly icon: string;
    readonly image: string;
    readonly lang: string;
    readonly renotify: boolean;
    readonly requireInteraction: boolean;
    readonly silent: boolean;
    readonly tag: string;
    readonly timestamp: number;
    readonly title: string;
    readonly vibrate: ReadonlyArray<number>;
    close(): void;
    onclick: ((this: Notification, ev: Event) => any) | null;
    onclose: ((this: Notification, ev: Event) => any) | null;
    onerror: ((this: Notification, ev: Event) => any) | null;
    onshow: ((this: Notification, ev: Event) => any) | null;
    addEventListener(type: string, listener: EventListener): void;
    removeEventListener(type: string, listener: EventListener): void;
    dispatchEvent(event: Event): boolean;
  }

  type NotificationDirection = 'auto' | 'ltr' | 'rtl';
  type NotificationPermission = 'default' | 'denied' | 'granted';

  interface NotificationConstructor {
    new(title: string, options?: NotificationOptions): Notification;
    readonly permission: NotificationPermission;
    requestPermission(): Promise<NotificationPermission>;
    requestPermission(deprecatedCallback: (permission: NotificationPermission) => void): void;
  }

  var Notification: NotificationConstructor;

  interface HTMLAudioElement extends HTMLMediaElement {
    // Additional audio-specific properties
    volume: number;
    muted: boolean;
  }

  interface HTMLMediaElement extends HTMLElement {
    autoplay: boolean;
    controls: boolean;
    currentSrc: string;
    currentTime: number;
    defaultMuted: boolean;
    defaultPlaybackRate: number;
    duration: number;
    ended: boolean;
    loop: boolean;
    muted: boolean;
    paused: boolean;
    playbackRate: number;
    preload: string;
    src: string;
    volume: number;
    play(): Promise<void>;
    pause(): void;
    load(): void;
    canPlayType(type: string): string;
  }

  interface AudioConstructor {
    new(src?: string): HTMLAudioElement;
  }

  var Audio: AudioConstructor;

  interface HTMLFormControlsCollection {
    readonly length: number;
    item(index: number): Element | null;
    namedItem(name: string): Element | null;
    [index: number]: Element;
    [name: string]: Element;
  }
}

export {};
