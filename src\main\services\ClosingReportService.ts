// import { Database } from 'better-sqlite3' // لم نعد نستخدم better-sqlite3
import { DatabaseService } from './DatabaseService'
import { Logger } from '../utils/logger'

export interface ClosingReportData {
  period: any
  summary: {
    totalRevenue: number
    totalExpenses: number
    netProfit: number
    closingEntries: number
    carriedForwardAccounts: number
  }
  revenueAccounts: Array<{
    account_code: string
    account_name: string
    balance: number
    closing_entry_id?: number
  }>
  expenseAccounts: Array<{
    account_code: string
    account_name: string
    balance: number
    closing_entry_id?: number
  }>
  carriedForwardBalances: Array<{
    account_code: string
    account_name: string
    opening_balance: number
    closing_balance: number
    carried_forward_amount: number
  }>
  auditTrail: Array<{
    action_type: string
    user_name: string
    action_details: string
    created_at: string
    success: boolean
  }>
}

export interface PeriodComparisonReport {
  currentPeriod: any
  previousPeriod?: any
  comparison: {
    revenue: {
      current: number
      previous: number
      change: number
      changePercent: number
    }
    expenses: {
      current: number
      previous: number
      change: number
      changePercent: number
    }
    profit: {
      current: number
      previous: number
      change: number
      changePercent: number
    }
  }
  topRevenueAccounts: Array<{
    account_name: string
    current: number
    previous: number
    change: number
  }>
  topExpenseAccounts: Array<{
    account_name: string
    current: number
    previous: number
    change: number
  }>
}

/**
 * خدمة تقارير الإقفال المحاسبي
 */
export class ClosingReportService {
  private db: any // sql.js Database instance
  private static instance: ClosingReportService

  constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
  }

  public static getInstance(): ClosingReportService {
    if (!ClosingReportService.instance) {
      ClosingReportService.instance = new ClosingReportService()
    }
    return ClosingReportService.instance
  }

  /**
   * إنشاء تقرير الإقفال الشامل
   */
  public async generateClosingReport(periodId: number): Promise<ClosingReportData> {
    try {
      // جلب معلومات الفترة
      const period = this.db.prepare(`
        SELECT fp.*, u.full_name as closed_by_name
        FROM fiscal_periods fp
        LEFT JOIN users u ON fp.closed_by = u.id
        WHERE fp.id = ?
      `).get(periodId)

      if (!period) {
        throw new Error('الفترة المالية غير موجودة')
      }

      // جلب ملخص الإقفال
      const summary = await this.getClosingSummary(periodId, (period as any).start_date, (period as any).end_date)

      // جلب حسابات الإيرادات المقفلة
      const revenueAccounts = await this.getClosedRevenueAccounts((period as any).start_date, (period as any).end_date)

      // جلب حسابات المصروفات المقفلة
      const expenseAccounts = await this.getClosedExpenseAccounts((period as any).start_date, (period as any).end_date)

      // جلب الأرصدة المرحلة
      const carriedForwardBalances = await this.getCarriedForwardBalances(periodId)

      // جلب سجل التدقيق
      const auditTrail = await this.getAuditTrail(periodId)

      return {
        period,
        summary,
        revenueAccounts,
        expenseAccounts,
        carriedForwardBalances,
        auditTrail
      }
    } catch (error) {
      Logger.error('ClosingReportService', 'خطأ في إنشاء تقرير الإقفال:', error)
      throw error
    }
  }

  /**
   * إنشاء تقرير مقارنة الفترات
   */
  public async generatePeriodComparisonReport(currentPeriodId: number, previousPeriodId?: number): Promise<PeriodComparisonReport> {
    try {
      const currentPeriod = this.db.prepare(`
        SELECT * FROM fiscal_periods WHERE id = ?
      `).get(currentPeriodId)

      if (!currentPeriod) {
        throw new Error('الفترة الحالية غير موجودة')
      }

      let previousPeriod = null
      if (previousPeriodId) {
        previousPeriod = this.db.prepare(`
          SELECT * FROM fiscal_periods WHERE id = ?
        `).get(previousPeriodId)
      } else {
        // البحث عن الفترة السابقة تلقائياً
        previousPeriod = this.db.prepare(`
          SELECT * FROM fiscal_periods 
          WHERE end_date < ? AND status = 'closed'
          ORDER BY end_date DESC 
          LIMIT 1
        `).get((currentPeriod as any).start_date)
      }

      // حساب الأرقام للفترة الحالية
      const currentNumbers = await this.getPeriodNumbers((currentPeriod as any).start_date, (currentPeriod as any).end_date)
      
      // حساب الأرقام للفترة السابقة
      let previousNumbers = { revenue: 0, expenses: 0, profit: 0 }
      if (previousPeriod) {
        previousNumbers = await this.getPeriodNumbers(previousPeriod.start_date, previousPeriod.end_date)
      }

      // حساب التغييرات
      const comparison = {
        revenue: {
          current: currentNumbers.revenue,
          previous: previousNumbers.revenue,
          change: currentNumbers.revenue - previousNumbers.revenue,
          changePercent: previousNumbers.revenue > 0 ? 
            ((currentNumbers.revenue - previousNumbers.revenue) / previousNumbers.revenue) * 100 : 0
        },
        expenses: {
          current: currentNumbers.expenses,
          previous: previousNumbers.expenses,
          change: currentNumbers.expenses - previousNumbers.expenses,
          changePercent: previousNumbers.expenses > 0 ? 
            ((currentNumbers.expenses - previousNumbers.expenses) / previousNumbers.expenses) * 100 : 0
        },
        profit: {
          current: currentNumbers.profit,
          previous: previousNumbers.profit,
          change: currentNumbers.profit - previousNumbers.profit,
          changePercent: previousNumbers.profit !== 0 ? 
            ((currentNumbers.profit - previousNumbers.profit) / Math.abs(previousNumbers.profit)) * 100 : 0
        }
      }

      // جلب أهم حسابات الإيرادات والمصروفات
      const topRevenueAccounts = await this.getTopAccountsComparison('revenue', currentPeriod, previousPeriod)
      const topExpenseAccounts = await this.getTopAccountsComparison('expense', currentPeriod, previousPeriod)

      return {
        currentPeriod,
        previousPeriod,
        comparison,
        topRevenueAccounts,
        topExpenseAccounts
      }
    } catch (error) {
      Logger.error('ClosingReportService', 'خطأ في إنشاء تقرير مقارنة الفترات:', error)
      throw error
    }
  }

  /**
   * جلب ملخص الإقفال
   */
  private async getClosingSummary(periodId: number, startDate: string, endDate: string): Promise<any> {
    try {
      // حساب إجمالي الإيرادات
      const totalRevenue = this.db.prepare(`
        SELECT COALESCE(SUM(jed.credit_amount - jed.debit_amount), 0) as total
        FROM journal_entry_details jed
        JOIN journal_entries je ON jed.entry_id = je.id
        JOIN chart_of_accounts coa ON jed.account_id = coa.id
        WHERE coa.account_type = 'revenue'
          AND je.entry_date BETWEEN ? AND ?
          AND je.status = 'posted'
      `).get(startDate, endDate) as { total: number }

      // حساب إجمالي المصروفات
      const totalExpenses = this.db.prepare(`
        SELECT COALESCE(SUM(jed.debit_amount - jed.credit_amount), 0) as total
        FROM journal_entry_details jed
        JOIN journal_entries je ON jed.entry_id = je.id
        JOIN chart_of_accounts coa ON jed.account_id = coa.id
        WHERE coa.account_type = 'expense'
          AND je.entry_date BETWEEN ? AND ?
          AND je.status = 'posted'
      `).get(startDate, endDate) as { total: number }

      // عدد قيود الإقفال
      const closingEntries = this.db.prepare(`
        SELECT COUNT(*) as count FROM closing_entries WHERE fiscal_period_id = ?
      `).get(periodId) as { count: number }

      // عدد الحسابات المرحلة
      const carriedForwardAccounts = this.db.prepare(`
        SELECT COUNT(*) as count FROM carried_forward_balances WHERE fiscal_period_id = ?
      `).get(periodId) as { count: number }

      return {
        totalRevenue: totalRevenue.total,
        totalExpenses: totalExpenses.total,
        netProfit: totalRevenue.total - totalExpenses.total,
        closingEntries: closingEntries.count,
        carriedForwardAccounts: carriedForwardAccounts.count
      }
    } catch (error) {
      Logger.error('ClosingReportService', 'خطأ في جلب ملخص الإقفال:', error)
      return {
        totalRevenue: 0,
        totalExpenses: 0,
        netProfit: 0,
        closingEntries: 0,
        carriedForwardAccounts: 0
      }
    }
  }

  /**
   * جلب حسابات الإيرادات المقفلة
   */
  private async getClosedRevenueAccounts(startDate: string, endDate: string): Promise<any[]> {
    try {
      return this.db.prepare(`
        SELECT 
          coa.account_code,
          coa.account_name,
          COALESCE(SUM(jed.credit_amount - jed.debit_amount), 0) as balance
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type = 'revenue'
          AND (je.entry_date IS NULL OR (je.entry_date BETWEEN ? AND ? AND je.status = 'posted'))
        GROUP BY coa.id, coa.account_code, coa.account_name
        HAVING balance != 0
        ORDER BY balance DESC
      `).all(startDate, endDate)
    } catch (error) {
      Logger.error('ClosingReportService', 'خطأ في جلب حسابات الإيرادات:', error)
      return []
    }
  }

  /**
   * جلب حسابات المصروفات المقفلة
   */
  private async getClosedExpenseAccounts(startDate: string, endDate: string): Promise<any[]> {
    try {
      return this.db.prepare(`
        SELECT 
          coa.account_code,
          coa.account_name,
          COALESCE(SUM(jed.debit_amount - jed.credit_amount), 0) as balance
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type = 'expense'
          AND (je.entry_date IS NULL OR (je.entry_date BETWEEN ? AND ? AND je.status = 'posted'))
        GROUP BY coa.id, coa.account_code, coa.account_name
        HAVING balance != 0
        ORDER BY balance DESC
      `).all(startDate, endDate)
    } catch (error) {
      Logger.error('ClosingReportService', 'خطأ في جلب حسابات المصروفات:', error)
      return []
    }
  }

  /**
   * جلب الأرصدة المرحلة
   */
  private async getCarriedForwardBalances(periodId: number): Promise<any[]> {
    try {
      return this.db.prepare(`
        SELECT 
          cfb.*,
          coa.account_code,
          coa.account_name
        FROM carried_forward_balances cfb
        JOIN chart_of_accounts coa ON cfb.account_id = coa.id
        WHERE cfb.fiscal_period_id = ?
        ORDER BY coa.account_code
      `).all(periodId)
    } catch (error) {
      Logger.error('ClosingReportService', 'خطأ في جلب الأرصدة المرحلة:', error)
      return []
    }
  }

  /**
   * جلب سجل التدقيق
   */
  private async getAuditTrail(periodId: number): Promise<any[]> {
    try {
      return this.db.prepare(`
        SELECT 
          pca.*,
          u.full_name as user_name
        FROM period_closing_audit pca
        LEFT JOIN users u ON pca.user_id = u.id
        WHERE pca.fiscal_period_id = ?
        ORDER BY pca.created_at DESC
      `).all(periodId)
    } catch (error) {
      Logger.error('ClosingReportService', 'خطأ في جلب سجل التدقيق:', error)
      return []
    }
  }

  /**
   * حساب أرقام الفترة
   */
  private async getPeriodNumbers(startDate: string, endDate: string): Promise<{ revenue: number; expenses: number; profit: number }> {
    try {
      const revenue = this.db.prepare(`
        SELECT COALESCE(SUM(jed.credit_amount - jed.debit_amount), 0) as total
        FROM journal_entry_details jed
        JOIN journal_entries je ON jed.entry_id = je.id
        JOIN chart_of_accounts coa ON jed.account_id = coa.id
        WHERE coa.account_type = 'revenue'
          AND je.entry_date BETWEEN ? AND ?
          AND je.status = 'posted'
      `).get(startDate, endDate) as { total: number }

      const expenses = this.db.prepare(`
        SELECT COALESCE(SUM(jed.debit_amount - jed.credit_amount), 0) as total
        FROM journal_entry_details jed
        JOIN journal_entries je ON jed.entry_id = je.id
        JOIN chart_of_accounts coa ON jed.account_id = coa.id
        WHERE coa.account_type = 'expense'
          AND je.entry_date BETWEEN ? AND ?
          AND je.status = 'posted'
      `).get(startDate, endDate) as { total: number }

      return {
        revenue: revenue.total,
        expenses: expenses.total,
        profit: revenue.total - expenses.total
      }
    } catch (error) {
      Logger.error('ClosingReportService', 'خطأ في حساب أرقام الفترة:', error)
      return { revenue: 0, expenses: 0, profit: 0 }
    }
  }

  /**
   * جلب أهم الحسابات للمقارنة
   */
  private async getTopAccountsComparison(accountType: 'revenue' | 'expense', currentPeriod: any, previousPeriod: any): Promise<any[]> {
    try {
      if (!previousPeriod) return []

      const accounts = this.db.prepare(`
        SELECT DISTINCT coa.id, coa.account_code, coa.account_name
        FROM chart_of_accounts coa
        WHERE coa.account_type = ?
        ORDER BY coa.account_code
        LIMIT 10
      `).all(accountType)

      const results = []
      for (const account of accounts) {
        const current = this.db.prepare(`
          SELECT COALESCE(SUM(
            CASE WHEN ? = 'revenue' 
            THEN jed.credit_amount - jed.debit_amount
            ELSE jed.debit_amount - jed.credit_amount END
          ), 0) as total
          FROM journal_entry_details jed
          JOIN journal_entries je ON jed.entry_id = je.id
          WHERE jed.account_id = ?
            AND je.entry_date BETWEEN ? AND ?
            AND je.status = 'posted'
        `).get(accountType, (account as any).id, (currentPeriod as any).start_date, (currentPeriod as any).end_date) as { total: number }

        const previous = this.db.prepare(`
          SELECT COALESCE(SUM(
            CASE WHEN ? = 'revenue'
            THEN jed.credit_amount - jed.debit_amount
            ELSE jed.debit_amount - jed.credit_amount END
          ), 0) as total
          FROM journal_entry_details jed
          JOIN journal_entries je ON jed.entry_id = je.id
          WHERE jed.account_id = ?
            AND je.entry_date BETWEEN ? AND ?
            AND je.status = 'posted'
        `).get(accountType, (account as any).id, (previousPeriod as any).start_date, (previousPeriod as any).end_date) as { total: number }

        if (current.total > 0 || previous.total > 0) {
          results.push({
            account_name: (account as any).account_name,
            current: current.total,
            previous: previous.total,
            change: current.total - previous.total
          })
        }
      }

      return results.sort((a, b) => Math.abs(b.change) - Math.abs(a.change)).slice(0, 5)
    } catch (error) {
      Logger.error('ClosingReportService', 'خطأ في جلب أهم الحسابات:', error)
      return []
    }
  }
}
