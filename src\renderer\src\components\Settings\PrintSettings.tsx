import React, { useState, useEffect } from 'react'
import {
  Card,
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  Button,
  Space,
  Divider,
  Row,
  Col,
  Typography,
  Upload,
  Tabs,
  Alert,
  Tag,
  App,
  Tooltip,
  Badge,
  Statistic,
  Modal,
  Progress,
  Slider,
  Radio,
  Checkbox
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  PrinterOutlined,
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  UploadOutlined,
  EyeOutlined,
  FileTextOutlined,
  BgColorsOutlined,
  TableOutlined,
  ToolOutlined,

  BulbOutlined,
  SunOutlined,
  MoonOutlined,
  DesktopOutlined,
  CheckOutlined,
  DatabaseOutlined,
  CloudUploadOutlined,
  DownloadOutlined,
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import ThemeToggle from '../common/ThemeToggle'
import EnhancedTableDemo from '../common/EnhancedTableDemo'
import LoadingSkeletons from '../common/LoadingSkeletons'
import AnimatedComponents from '../common/AnimatedComponents'
import PrintTemplateCreator from './PrintTemplateCreator'
import { MasterPrintService, PrintData, EnhancedPrintOptions } from '../../services/MasterPrintService'
import { UnifiedPrintButton } from '../common'

import { usePrintSettings } from '../../hooks/usePrintSettings'
import { STANDARD_PRINT_TEMPLATES, StandardPrintTemplate } from '../../utils/standardPrintTemplates'

const { Title, Text } = Typography

interface PrintSettingsConfig {
  pageSize: string
  orientation: string
  margins: {
    top: number
    bottom: number
    left: number
    right: number
  }
  fontSize: number
  fontFamily: string
  showHeader: boolean
  showFooter: boolean
  showLogo: boolean
  showSignature: boolean
  showTerms: boolean
  showQR: boolean
  headerText: string
  footerText: string
  logoPosition: string
  logoSize: 'small' | 'medium' | 'large' | 'extra-large'
  primaryColor: string
  secondaryColor: string
  borderColor: string
  backgroundColor: string
  textColor: string
  quality: 'draft' | 'normal' | 'high'
  copies: number
  autoSave: boolean
  watermark: boolean
  watermarkText: string
  watermarkOpacity: number
  lineSpacing: number
  borderWidth: number
  headerSize: number
  sectionSpacing: number
  tableWidth: number
}

interface CompanySettings {
  name: string
  nameEn: string
  address: string
  phone: string
  email: string
  website: string
  taxNumber: string
  registrationNumber: string
  logo: string
  description: string
  slogan: string
}

interface PrintTemplate {
  id: string
  name: string
  description: string
  type: 'invoice' | 'receipt' | 'report' | 'certificate' | 'custom'
  isDefault: boolean
  isActive: boolean
  settings: Partial<PrintSettingsConfig>
  preview?: string
  createdAt: string
  updatedAt: string
}

const PrintSettings: React.FC = () => {
  const { message } = App.useApp()

  // استخدام النظام المركزي لإدارة الإعدادات
  const {
    settings: centralSettings,
    updateSettings: updateCentralSettings,
    resetSettings: resetCentralSettings,
    isReady: _centralReady,
    loading: _centralLoading
  } = usePrintSettings()

  // إنشاء النماذج مع معالجة أفضل للأخطاء
  const [companyForm] = Form.useForm()
  const [printForm] = Form.useForm()
  const [templateForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [previewLoading, setPreviewLoading] = useState(false)
  const [saveLoading, setSaveLoading] = useState(false)
  const [testPrintLoading, setTestPrintLoading] = useState(false)
  const [templateModalVisible, setTemplateModalVisible] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<PrintTemplate | null>(null)
  const [templateCreatorVisible, setTemplateCreatorVisible] = useState(false)

  const [defaultSettings, setDefaultSettings] = useState<PrintSettingsConfig>({
    pageSize: 'A4',
    orientation: 'portrait',
    margins: { top: 20, bottom: 20, left: 20, right: 20 },
    fontSize: 12,
    fontFamily: 'Arial',
    showHeader: true,
    showFooter: true,
    showLogo: true,
    showSignature: false,
    showTerms: true,
    showQR: false,
    headerText: 'شركة المحاسبة المتقدمة',
    footerText: 'شكراً لتعاملكم معنا',
    logoPosition: 'top-left',
    logoSize: 'medium',
    primaryColor: '#1890ff',
    secondaryColor: '#fff3cd',
    borderColor: '#d9d9d9',
    backgroundColor: '#ffffff',
    textColor: '#000000',
    quality: 'normal',
    copies: 1,
    autoSave: true,
    watermark: false,
    watermarkText: 'ZET.IA',
    watermarkOpacity: 0.1,
    lineSpacing: 1.5,
    borderWidth: 1,
    headerSize: 18,
    sectionSpacing: 15,
    tableWidth: 100
  })

  const [companySettings, setCompanySettings] = useState<CompanySettings>({
    name: 'ZET.IA',
    nameEn: 'ZET.IA - Accounting & Production System',
    address: 'فلسطين - غزة',
    phone: '**********',
    email: '<EMAIL>',
    website: 'www.zetia.com',
    taxNumber: '*********',
    registrationNumber: 'REG-123456',
    logo: '',
    description: 'نظام محاسبة وإنتاج متكامل',
    slogan: 'الحل الأمثل لإدارة أعمالك'
  })

  const [printTemplates, setPrintTemplates] = useState<PrintTemplate[]>(
    STANDARD_PRINT_TEMPLATES.map(template => ({
      ...template,
      settings: template.settings as Partial<PrintSettingsConfig>
    }))
  )

  const printService = MasterPrintService.getInstance()

  useEffect(() => {
    loadSettings()
    loadCompanySettings()
    loadPrintTemplates()
    initializePrintService()
  }, [])

  // تحديث نموذج الطباعة عند تغيير الإعدادات
  useEffect(() => {
    if (defaultSettings && Object.keys(defaultSettings).length > 0) {
      // تأخير لضمان ربط النموذج بـ DOM
      const timer = setTimeout(() => {
        try {
          if (printForm && typeof printForm.setFieldsValue === 'function') {
            printForm.setFieldsValue(defaultSettings)
          }
        } catch (error) {
          Logger.warn('PrintSettings', 'تحذير في تحديث نموذج الطباعة:', error)
        }
      }, 300)

      return () => clearTimeout(timer)
    }
    return undefined
  }, [defaultSettings, printForm])

  // تحديث نموذج الشركة عند تغيير الإعدادات
  useEffect(() => {
    if (companySettings && Object.keys(companySettings).length > 0) {
      // التأكد من أن النموذج متصل بـ DOM قبل المحاولة
      const timer = setTimeout(() => {
        try {
          companyForm.setFieldsValue(companySettings)
        } catch (error) {
          console.warn('PrintSettings: تحذير في تحديث نموذج الشركة:', error)
        }
      }, 50) // تقليل وقت الانتظار

      return () => clearTimeout(timer)
    }
    return undefined
  }, [companySettings, companyForm])





  const initializePrintService = () => {
    // تحديث معلومات الشركة في خدمة الطباعة
    printService.updateCompanyInfo({
      name: companySettings.name,
      nameEn: companySettings.nameEn,
      address: companySettings.address,
      phone: companySettings.phone,
      email: companySettings.email,
      website: companySettings.website,
      logo: companySettings.logo,
      taxNumber: companySettings.taxNumber
    })

    // تحديث إعدادات الطباعة الافتراضية في الخدمة
    if (printService.updateDefaultSettings) {
      printService.updateDefaultSettings(defaultSettings as any)
    }
  }

  // دالة لتحديث الإعدادات فوراً عند تغيير القيم
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    // تحديث الإعدادات المحلية
    const updatedSettings = { ...defaultSettings, ...allValues }
    setDefaultSettings(updatedSettings)

    // تحديث خدمة الطباعة فوراً
    if (printService.updateDefaultSettings) {
      printService.updateDefaultSettings(updatedSettings as any)
    }

    // تطبيق الألوان على متغيرات CSS فوراً للمعاينة
    if (changedValues && typeof document !== 'undefined') {
      const root = document.documentElement
      if (changedValues.primaryColor) {
        root.style.setProperty('--print-primary-color', changedValues.primaryColor)
      }
      if (changedValues.secondaryColor) {
        root.style.setProperty('--print-secondary-color', changedValues.secondaryColor)
      }
      if (changedValues.borderColor) {
        root.style.setProperty('--print-border-color', changedValues.borderColor)
      }
      if (changedValues.backgroundColor) {
        root.style.setProperty('--print-background-color', changedValues.backgroundColor)
      }
      if (changedValues.textColor) {
        root.style.setProperty('--print-text-color', changedValues.textColor)
      }

      // تطبيق إعدادات الشعار على متغيرات CSS فوراً
      if (changedValues.logoSize) {
        // تحديد أبعاد الشعار حسب الحجم
        const getLogoDimensions = (size: 'small' | 'medium' | 'large' | 'extra-large') => {
          switch (size) {
            case 'small': return { width: 60, height: 40 }
            case 'medium': return { width: 90, height: 60 }
            case 'large': return { width: 120, height: 80 }
            case 'extra-large': return { width: 160, height: 120 }
            default: return { width: 90, height: 60 }
          }
        }

        const dimensions = getLogoDimensions(changedValues.logoSize)
        root.style.setProperty('--print-logo-width', `${dimensions.width}px`)
        root.style.setProperty('--print-logo-height', `${dimensions.height}px`)
        root.style.setProperty('--print-logo-size', changedValues.logoSize)
      }

      if (changedValues.logoPosition) {
        root.style.setProperty('--print-logo-position', changedValues.logoPosition)
      }

      if (changedValues.showLogo !== undefined) {
        root.style.setProperty('--print-show-logo', changedValues.showLogo ? 'block' : 'none')
      }
    }

    // طباعة معلومات التحديث للتشخيص
    if (changedValues) {
      Logger.info('PrintSettings', 'تم تحديث إعدادات الطباعة:', changedValues)

      // طباعة معلومات الألوان إذا تم تغييرها
      const colorChanges = Object.keys(changedValues).filter(key =>
        key.includes('Color') || key.includes('color')
      )
      if (colorChanges.length > 0) {
        Logger.info('PrintSettings', 'تم تحديث الألوان:',
          colorChanges.reduce((acc, key) => ({ ...acc, [key]: changedValues[key] }), {})
        )
      }
    }
  }

  const loadSettings = async () => {
    try {
      setLoading(true)
      // تحميل الإعدادات من قاعدة البيانات
      const result = await window.electronAPI?.invoke('get-settings', 'print_')
      if (result?.success && result.data) {
        const settingsMap = result.data
        const updatedSettings = {
          ...defaultSettings,
          pageSize: settingsMap.print_page_size || defaultSettings.pageSize,
          orientation: settingsMap.print_orientation || defaultSettings.orientation,
          fontSize: parseInt(settingsMap.print_font_size) || defaultSettings.fontSize,
          fontFamily: settingsMap.print_font_family || defaultSettings.fontFamily,
          showHeader: settingsMap.print_show_header !== 'false',
          showFooter: settingsMap.print_show_footer !== 'false',
          showLogo: settingsMap.print_show_logo !== 'false',
          showSignature: settingsMap.print_show_signature === 'true',
          showTerms: settingsMap.print_show_terms !== 'false',
          showQR: settingsMap.print_show_qr === 'true',
          headerText: settingsMap.print_header_text || defaultSettings.headerText,
          footerText: settingsMap.print_footer_text || defaultSettings.footerText,
          logoPosition: settingsMap.print_logo_position || defaultSettings.logoPosition,
          logoSize: (settingsMap.print_logo_size as 'small' | 'medium' | 'large' | 'extra-large') || defaultSettings.logoSize,
          primaryColor: settingsMap.print_primary_color || defaultSettings.primaryColor,
          secondaryColor: settingsMap.print_secondary_color || defaultSettings.secondaryColor,
          borderColor: settingsMap.print_border_color || defaultSettings.borderColor,
          backgroundColor: settingsMap.print_background_color || defaultSettings.backgroundColor,
          textColor: settingsMap.print_text_color || defaultSettings.textColor,
          quality: settingsMap.print_quality || defaultSettings.quality,
          copies: parseInt(settingsMap.print_copies) || defaultSettings.copies,
          autoSave: settingsMap.print_auto_save !== 'false',
          watermark: settingsMap.print_watermark === 'true',
          watermarkText: settingsMap.print_watermark_text || defaultSettings.watermarkText,
          watermarkOpacity: parseFloat(settingsMap.print_watermark_opacity) || defaultSettings.watermarkOpacity,
          margins: {
            top: parseInt(settingsMap.print_margin_top) || defaultSettings.margins.top,
            bottom: parseInt(settingsMap.print_margin_bottom) || defaultSettings.margins.bottom,
            left: parseInt(settingsMap.print_margin_left) || defaultSettings.margins.left,
            right: parseInt(settingsMap.print_margin_right) || defaultSettings.margins.right
          },
          lineSpacing: parseFloat(settingsMap.print_line_spacing) || defaultSettings.lineSpacing,
          borderWidth: parseFloat(settingsMap.print_border_width) || defaultSettings.borderWidth,
          headerSize: parseInt(settingsMap.print_header_size) || defaultSettings.headerSize,
          sectionSpacing: parseInt(settingsMap.print_section_spacing) || defaultSettings.sectionSpacing,
          tableWidth: parseInt(settingsMap.print_table_width) || defaultSettings.tableWidth
        }
        setDefaultSettings(updatedSettings)
        // تحديث النموذج فقط إذا كان جاهزاً ومتصل بـ Form element
        if (printForm && typeof printForm.setFieldsValue === 'function' && printForm.getFieldsValue) {
          try {
            const currentValues = printForm.getFieldsValue()
            if (currentValues !== undefined) {
              printForm.setFieldsValue(updatedSettings)
            }
          } catch (error) {
            console.warn('PrintSettings: تحذير في تحديث النموذج أثناء التحميل:', error)
          }
        }
      }
      message.success('تم تحميل إعدادات الطباعة بنجاح')
    } catch (error) {
      Logger.error('PrintSettings', 'خطأ في تحميل إعدادات الطباعة:', error)
      message.error('فشل في تحميل إعدادات الطباعة')
    } finally {
      setLoading(false)
    }
  }

  const loadCompanySettings = async () => {
    try {
      // تحميل معلومات الشركة من قاعدة البيانات
      const result = await window.electronAPI?.invoke('get-settings', 'company_')
      if (result?.success && result.data) {
        const settingsMap = result.data

        // تحميل الشعار بشكل صحيح
        let logoData = settingsMap.company_logo || companySettings.logo
        if (logoData && !logoData.startsWith('data:')) {
          // إذا كان الشعار مسار ملف، نحوله إلى base64
          try {
            const logoResult = await window.electronAPI?.getCompanyLogo()
            if (logoResult?.success && logoResult.logoPath) {
              logoData = logoResult.logoPath
            }
          } catch (logoError) {
            Logger.warn('PrintSettings', 'فشل في تحميل الشعار:', logoError)
          }
        }

        const updatedCompanySettings = {
          ...companySettings,
          name: settingsMap.company_name || companySettings.name,
          nameEn: settingsMap.company_name_en || companySettings.nameEn,
          address: settingsMap.company_address || companySettings.address,
          phone: settingsMap.company_phone || companySettings.phone,
          email: settingsMap.company_email || companySettings.email,
          website: settingsMap.company_website || companySettings.website,
          taxNumber: settingsMap.company_tax_number || companySettings.taxNumber,
          registrationNumber: settingsMap.company_registration_number || companySettings.registrationNumber,
          logo: logoData,
          description: settingsMap.company_description || companySettings.description,
          slogan: settingsMap.company_slogan || companySettings.slogan
        }
        setCompanySettings(updatedCompanySettings)

        // تحديث خدمة الطباعة بمعلومات الشركة المحدثة
        printService.updateCompanyInfo({
          name: updatedCompanySettings.name,
          nameEn: updatedCompanySettings.nameEn,
          address: updatedCompanySettings.address,
          phone: updatedCompanySettings.phone,
          email: updatedCompanySettings.email,
          website: updatedCompanySettings.website,
          logo: logoData, // استخدام البيانات المحولة
          taxNumber: updatedCompanySettings.taxNumber
        })

        Logger.info('PrintSettings', 'تم تحميل معلومات الشركة مع الشعار:', {
          hasLogo: !!logoData,
          logoType: logoData?.startsWith('data:') ? 'base64' : 'path'
        })
      }
    } catch (error) {
      Logger.error('PrintSettings', 'خطأ في تحميل معلومات الشركة:', error)
      message.error('فشل في تحميل معلومات الشركة')
    }
  }

  const loadPrintTemplates = async () => {
    try {
      // تحميل قوالب الطباعة من قاعدة البيانات
      const result = await window.electron?.ipcRenderer?.invoke('get-print-templates')
      if (result?.success && result.data) {
        setPrintTemplates(result.data)
      }
    } catch (error) {
      Logger.error('PrintSettings', 'خطأ في تحميل قوالب الطباعة:', error)
    }
  }

  const handleSavePrintSettings = async (values: PrintSettingsConfig) => {
    try {
      setSaveLoading(true)

      // تحويل الإعدادات إلى تنسيق قاعدة البيانات
      const settingsToSave = [
        { key: 'print_page_size', value: values.pageSize },
        { key: 'print_orientation', value: values.orientation },
        { key: 'print_font_size', value: values.fontSize.toString() },
        { key: 'print_font_family', value: values.fontFamily },
        { key: 'print_show_header', value: values.showHeader.toString() },
        { key: 'print_show_footer', value: values.showFooter.toString() },
        { key: 'print_show_logo', value: values.showLogo.toString() },
        { key: 'print_show_signature', value: values.showSignature.toString() },
        { key: 'print_show_terms', value: values.showTerms.toString() },
        { key: 'print_show_qr', value: values.showQR.toString() },
        { key: 'print_primary_color', value: values.primaryColor },
        { key: 'print_secondary_color', value: values.secondaryColor },
        { key: 'print_border_color', value: values.borderColor },
        { key: 'print_background_color', value: values.backgroundColor },
        { key: 'print_text_color', value: values.textColor },
        { key: 'print_quality', value: values.quality },
        { key: 'print_copies', value: values.copies.toString() },
        { key: 'print_auto_save', value: values.autoSave.toString() },
        { key: 'print_watermark', value: values.watermark.toString() },
        { key: 'print_watermark_text', value: values.watermarkText },
        { key: 'print_watermark_opacity', value: values.watermarkOpacity.toString() },
        { key: 'print_header_text', value: values.headerText },
        { key: 'print_footer_text', value: values.footerText },
        { key: 'print_logo_position', value: values.logoPosition },
        { key: 'print_logo_size', value: values.logoSize },
        { key: 'print_margin_top', value: values.margins.top.toString() },
        { key: 'print_margin_bottom', value: values.margins.bottom.toString() },
        { key: 'print_margin_left', value: values.margins.left.toString() },
        { key: 'print_margin_right', value: values.margins.right.toString() },
        { key: 'print_line_spacing', value: values.lineSpacing.toString() },
        { key: 'print_border_width', value: values.borderWidth.toString() },
        { key: 'print_header_size', value: values.headerSize.toString() },
        { key: 'print_section_spacing', value: values.sectionSpacing.toString() },
        { key: 'print_table_width', value: values.tableWidth.toString() }
      ]

      // حفظ الإعدادات في قاعدة البيانات
      const result = await window.electronAPI?.invoke('save-settings', settingsToSave)
      if (result?.success) {
        setDefaultSettings(values)
        message.success('تم حفظ إعدادات الطباعة بنجاح')

        // تحديث خدمة الطباعة
        initializePrintService()
      } else {
        throw new Error(result?.message || 'فشل في حفظ الإعدادات')
      }
    } catch (error) {
      Logger.error('PrintSettings', 'خطأ في حفظ إعدادات الطباعة:', error)
      message.error('فشل في حفظ إعدادات الطباعة')
    } finally {
      setSaveLoading(false)
    }
  }

  const handleCompanySave = async (values: CompanySettings) => {
    try {
      setSaveLoading(true)

      // تحويل معلومات الشركة إلى تنسيق قاعدة البيانات
      const settingsToSave = [
        { key: 'company_name', value: values.name },
        { key: 'company_name_en', value: values.nameEn },
        { key: 'company_address', value: values.address },
        { key: 'company_phone', value: values.phone },
        { key: 'company_email', value: values.email },
        { key: 'company_website', value: values.website },
        { key: 'company_tax_number', value: values.taxNumber },
        { key: 'company_registration_number', value: values.registrationNumber },
        { key: 'company_logo', value: values.logo },
        { key: 'company_description', value: values.description },
        { key: 'company_slogan', value: values.slogan }
      ]

      // حفظ معلومات الشركة في قاعدة البيانات
      const result = await window.electronAPI?.invoke('save-settings', settingsToSave)
      if (result?.success) {
        setCompanySettings(values)
        message.success('تم حفظ معلومات الشركة بنجاح')

        // تحديث خدمة الطباعة
        printService.updateCompanyInfo({
          name: values.name,
          nameEn: values.nameEn,
          address: values.address,
          phone: values.phone,
          email: values.email,
          website: values.website,
          logo: values.logo,
          taxNumber: values.taxNumber
        })

        // إعادة تحميل معلومات الشركة للتأكد من تحديث الشعار
        await printService.loadCompanyInfo()
      } else {
        throw new Error(result?.message || 'فشل في حفظ معلومات الشركة')
      }
    } catch (error) {
      Logger.error('PrintSettings', 'خطأ في حفظ معلومات الشركة:', error)
      message.error('فشل في حفظ معلومات الشركة')
    } finally {
      setSaveLoading(false)
    }
  }

  // دالة إنشاء بيانات تجريبية حسب نوع القالب
  const generateTestDataForTemplate = (template: PrintTemplate): PrintData => {
    const baseData = {
      id: `TEST-${template.type.toUpperCase()}-001`,
      date: new Date().toLocaleDateString('ar-SA'),
      customer: {
        name: 'عميل تجريبي',
        address: 'عنوان تجريبي، المدينة، الرمز البريدي',
        phone: '*********',
        email: '<EMAIL>'
      },
      company: {
        name: companySettings?.name || 'ZET.IA',
        nameEn: companySettings?.nameEn || 'ZET.IA - Accounting & Production System',
        address: companySettings?.address || 'فلسطين - غزة',
        phone: companySettings?.phone || '**********',
        email: companySettings?.email || '<EMAIL>',
        website: companySettings?.website || 'www.zetia.com',
        logo: companySettings?.logo || '',
        taxNumber: companySettings?.taxNumber || ''
      }
    }



    switch (template.type) {
      case 'invoice':
        return {
          ...baseData,
          title: 'فاتورة تجريبية',
          number: 'INV-TEST-001',
          items: [
            {
              id: 1,
              name: 'منتج تجريبي 1',
              description: 'وصف المنتج التجريبي',
              quantity: 2,
              unit: 'قطعة',
              unitPrice: 100,
              total: 200
            },
            {
              id: 2,
              name: 'منتج تجريبي 2',
              description: 'وصف المنتج التجريبي الثاني',
              quantity: 1,
              unit: 'قطعة',
              unitPrice: 150,
              total: 150
            }
          ],
          subtotal: 350,
          tax: 56,
          total: 406,
          notes: `هذه فاتورة تجريبية لاختبار قالب: ${template.name}`
        }

      case 'receipt':
        return {
          ...baseData,
          title: 'إيصال تجريبي',
          number: 'REC-TEST-001',
          items: [
            {
              id: 1,
              name: 'دفعة على حساب',
              description: 'دفعة نقدية',
              quantity: 1,
              unit: 'دفعة',
              unitPrice: 500,
              total: 500
            }
          ],
          subtotal: 500,
          total: 500,
          notes: `هذا إيصال تجريبي لاختبار قالب: ${template.name}`
        }

      case 'report':
        return {
          ...baseData,
          title: 'تقرير تجريبي',
          number: 'REP-TEST-001',
          items: [
            {
              id: 1,
              name: 'بند التقرير 1',
              description: 'وصف البند الأول',
              quantity: 1,
              unit: 'بند',
              unitPrice: 0,
              total: 0
            }
          ],
          notes: `هذا تقرير تجريبي لاختبار قالب: ${template.name}`
        }

      default:
        return {
          ...baseData,
          title: 'مستند تجريبي',
          number: 'DOC-TEST-001',
          notes: `هذا مستند تجريبي لاختبار قالب: ${template.name}`
        }
    }
  }

  const handleTestPrint = async () => {
    try {
      setTestPrintLoading(true)

      // تطبيق إعدادات الطباعة
      Logger.info('PrintSettings', 'بدء اختبار الطباعة')

      // الحصول على القيم الحالية من النموذج
      const currentFormValues = printForm.getFieldsValue()

      // دمج القيم الحالية مع الإعدادات الافتراضية
      const currentSettings = {
        ...defaultSettings,
        ...currentFormValues
      }

      // التأكد من تطبيق الهوامش والألوان الحالية
      const finalSettings = {
        ...currentSettings,
        margins: currentFormValues.margins || currentSettings.margins,
        primaryColor: currentFormValues.primaryColor || currentSettings.primaryColor,
        secondaryColor: currentFormValues.secondaryColor || currentSettings.secondaryColor,
        borderColor: currentFormValues.borderColor || currentSettings.borderColor,
        backgroundColor: currentFormValues.backgroundColor || currentSettings.backgroundColor,
        textColor: currentFormValues.textColor || currentSettings.textColor
      }

      // طباعة معلومات الألوان للتشخيص
      Logger.info('PrintSettings', 'الألوان المطبقة في الطباعة التجريبية:', {
        primaryColor: finalSettings.primaryColor,
        secondaryColor: finalSettings.secondaryColor,
        borderColor: finalSettings.borderColor,
        backgroundColor: finalSettings.backgroundColor,
        textColor: finalSettings.textColor
      })

      // إنشاء بيانات تجريبية للطباعة
      const testData = generateTestDataForTemplate({
        id: 'test',
        name: 'اختبار الإعدادات',
        description: 'اختبار إعدادات الطباعة الحالية',
        type: 'invoice',
        isDefault: false,
        isActive: true,
        settings: finalSettings,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })

      // تحويل إعدادات الطباعة إلى تنسيق EnhancedPrintOptions
      const printOptions: EnhancedPrintOptions = {
        type: 'invoice',
        pageSize: finalSettings.pageSize as any,
        orientation: finalSettings.orientation as any,
        margins: finalSettings.margins,
        showLogo: finalSettings.showLogo,
        showHeader: finalSettings.showHeader,
        showFooter: finalSettings.showFooter,
        showSignature: finalSettings.showSignature,
        fontSize: finalSettings.fontSize,
        fontFamily: finalSettings.fontFamily,
        primaryColor: finalSettings.primaryColor,
        secondaryColor: finalSettings.secondaryColor,
        borderColor: finalSettings.borderColor,
        backgroundColor: finalSettings.backgroundColor,
        textColor: finalSettings.textColor,
        headerText: finalSettings.headerText,
        footerText: finalSettings.footerText,
        logoPosition: finalSettings.logoPosition,
        quality: finalSettings.quality,
        watermark: finalSettings.watermark,
        watermarkText: finalSettings.watermarkText,
        watermarkOpacity: finalSettings.watermarkOpacity,
        showTerms: finalSettings.showTerms,
        showQR: finalSettings.showQR,
        copies: finalSettings.copies,
        preview: true,
        autoClose: false
      }

      // تحديث خدمة الطباعة بالإعدادات الجديدة قبل الطباعة
      if (printService.updateDefaultSettings) {
        printService.updateDefaultSettings(finalSettings)
      }

      // تحديث خدمة الطباعة بمعلومات الشركة الحالية قبل الطباعة التجريبية
      printService.updateCompanyInfo({
        name: companySettings.name,
        nameEn: companySettings.nameEn,
        address: companySettings.address,
        phone: companySettings.phone,
        email: companySettings.email,
        website: companySettings.website,
        logo: companySettings.logo,
        taxNumber: companySettings.taxNumber
      })

      // إعادة تحميل معلومات الشركة للتأكد من تحديث الشعار
      await printService.loadCompanyInfo()

      // تحديث الإعدادات الافتراضية في خدمة الطباعة
      printService.updateDefaultSettings(finalSettings as any)

      // طباعة تجريبية
      await printService.print(testData, printOptions)
      message.success('تم إنشاء معاينة الطباعة التجريبية بنجاح')
    } catch (error) {
      Logger.error('PrintSettings', 'خطأ في الطباعة التجريبية:', error)
      message.error('فشل في إنشاء معاينة الطباعة')
    } finally {
      setTestPrintLoading(false)
    }
  }

  // دالة معاينة القالب
  const handleTemplatePreview = async (template: PrintTemplate) => {
    try {
      setPreviewLoading(true)

      // إنشاء بيانات تجريبية حسب نوع القالب
      const testData = generateTestDataForTemplate(template)

      // دمج إعدادات القالب مع الإعدادات الافتراضية
      const templateSettings = {
        ...defaultSettings,
        ...template.settings
      }

      // تحويل إعدادات الطباعة إلى تنسيق EnhancedPrintOptions
      const printOptions: EnhancedPrintOptions = {
        type: template.type as any,
        pageSize: templateSettings.pageSize as any,
        orientation: templateSettings.orientation as any,
        margins: templateSettings.margins,
        showLogo: templateSettings.showLogo,
        showHeader: templateSettings.showHeader,
        showFooter: templateSettings.showFooter,
        showSignature: templateSettings.showSignature,
        fontSize: templateSettings.fontSize,
        fontFamily: templateSettings.fontFamily,
        primaryColor: templateSettings.primaryColor,
        secondaryColor: templateSettings.secondaryColor,
        borderColor: templateSettings.borderColor,
        copies: 1,
        preview: true,
        autoClose: false,
        onSuccess: () => {
          message.success(`تم فتح معاينة قالب: ${template.name}`)
        },
        onError: (error) => {
          message.error(`فشل في معاينة القالب: ${error}`)
        }
      }

      // تحديث خدمة الطباعة بمعلومات الشركة الحالية قبل المعاينة
      printService.updateCompanyInfo({
        name: companySettings.name,
        nameEn: companySettings.nameEn,
        address: companySettings.address,
        phone: companySettings.phone,
        email: companySettings.email,
        website: companySettings.website,
        logo: companySettings.logo,
        taxNumber: companySettings.taxNumber
      })

      // فتح معاينة القالب
      await printService.previewOnly(testData, printOptions)

    } catch (error) {
      Logger.error('PrintSettings', 'خطأ في معاينة القالب:', error)
      message.error('حدث خطأ أثناء معاينة القالب')
    } finally {
      setPreviewLoading(false)
    }
  }

  const handleResetPrintSettings = async () => {
    try {
      if (resetCentralSettings) {
        // استخدام النظام المركزي لإعادة التعيين
        await resetCentralSettings()
        message.success('تم إعادة تعيين إعدادات الطباعة في النظام المركزي')

        // إعادة تعيين النموذج المحلي أيضاً
        printForm.resetFields()

        // تحديث الإعدادات المحلية من النظام المركزي
        if (centralSettings) {
          const localSettings: PrintSettingsConfig = {
            pageSize: centralSettings.pageSize || 'A4',
            orientation: centralSettings.orientation || 'portrait',
            margins: centralSettings.margins || { top: 20, bottom: 20, left: 20, right: 20 },
            fontSize: centralSettings.fontSize || 12,
            fontFamily: centralSettings.fontFamily || 'Arial',
            showHeader: centralSettings.showHeader ?? true,
            showFooter: centralSettings.showFooter ?? true,
            showLogo: centralSettings.showLogo ?? true,
            showSignature: centralSettings.showSignature ?? false,
            showTerms: centralSettings.showTerms ?? true,
            showQR: centralSettings.showQR ?? false,
            headerText: centralSettings.headerText || 'شركة المحاسبة المتقدمة',
            footerText: centralSettings.footerText || 'شكراً لتعاملكم معنا',
            logoPosition: centralSettings.logoPosition || 'top-left',
            logoSize: (typeof centralSettings.logoSize === 'string' ? centralSettings.logoSize : 'medium') as 'small' | 'medium' | 'large' | 'extra-large',
            primaryColor: centralSettings.primaryColor || '#1890ff',
            secondaryColor: centralSettings.secondaryColor || '#fff3cd',
            borderColor: centralSettings.borderColor || '#d9d9d9',
            backgroundColor: centralSettings.backgroundColor || '#ffffff',
            textColor: centralSettings.textColor || '#000000',
            quality: centralSettings.quality || 'normal',
            copies: centralSettings.copies || 1,
            autoSave: centralSettings.autoSave ?? true,
            watermark: centralSettings.watermark ?? false,
            watermarkText: centralSettings.watermarkText || 'ZET.IA',
            watermarkOpacity: centralSettings.watermarkOpacity || 0.1,
            lineSpacing: centralSettings.lineSpacing || 1.5,
            borderWidth: centralSettings.borderWidth || 1,
            headerSize: centralSettings.headerSize || 18,
            sectionSpacing: centralSettings.sectionSpacing || 15,
            tableWidth: centralSettings.tableWidth || 100
          }
          setDefaultSettings(localSettings)
          // تحديث النموذج فقط إذا كان متصل بـ Form element
          if (printForm && printForm.getFieldsValue) {
            try {
              const currentValues = printForm.getFieldsValue()
              if (currentValues !== undefined) {
                printForm.setFieldsValue(localSettings)
              }
            } catch (error) {
              console.warn('PrintSettings: تحذير في تحديث النموذج:', error)
            }
          }
        }
      } else {
        // fallback للطريقة القديمة
        const defaultPrintSettings: PrintSettingsConfig = {
          pageSize: 'A4',
          orientation: 'portrait',
          margins: { top: 20, bottom: 20, left: 20, right: 20 },
          fontSize: 12,
          fontFamily: 'Arial',
          showHeader: true,
          showFooter: true,
          showLogo: true,
          showSignature: false,
          showTerms: true,
          showQR: false,
          headerText: 'شركة المحاسبة المتقدمة',
          footerText: 'شكراً لتعاملكم معنا',
          logoPosition: 'top-left',
          logoSize: 'medium',
          primaryColor: '#1890ff',
          secondaryColor: '#fff3cd',
          borderColor: '#d9d9d9',
          backgroundColor: '#ffffff',
          textColor: '#000000',
          quality: 'normal',
          copies: 1,
          autoSave: true,
          watermark: false,
          watermarkText: 'ZET.IA',
          watermarkOpacity: 0.1,
          lineSpacing: 1.5,
          borderWidth: 1,
          headerSize: 18,
          sectionSpacing: 15,
          tableWidth: 100
        }

        // حفظ القيم الجديدة في قاعدة البيانات فوراً
        await handleSavePrintSettings(defaultPrintSettings)

        setDefaultSettings(defaultPrintSettings)
        // تحديث النموذج فقط إذا كان متصل بـ Form element
        if (printForm && printForm.getFieldsValue) {
          try {
            const currentValues = printForm.getFieldsValue()
            if (currentValues !== undefined) {
              printForm.setFieldsValue(defaultPrintSettings)
            }
          } catch (error) {
            console.warn('PrintSettings: تحذير في تحديث النموذج:', error)
          }
        }
        message.success('تم إعادة تعيين إعدادات الطباعة وحفظها في قاعدة البيانات')
      }
    } catch (error) {
      Logger.error('PrintSettings', 'خطأ في إعادة تعيين إعدادات الطباعة:', error)
      message.error('فشل في إعادة تعيين إعدادات الطباعة')
    }
  }

  // دالة لتحديث الألوان فقط باستخدام النظام المركزي
  const handleUpdateColors = async () => {
    try {
      if (updateCentralSettings) {
        await updateCentralSettings({
          secondaryColor: '#fff3cd'
        })
        message.success('تم تحديث الألوان في النظام المركزي بنجاح')

        // تحديث النموذج المحلي أيضاً
        if (printForm && printForm.getFieldsValue) {
          try {
            const currentValues = printForm.getFieldsValue()
            if (currentValues !== undefined) {
              const updatedValues = {
                ...currentValues,
                secondaryColor: '#fff3cd'
              }
              printForm.setFieldsValue(updatedValues)
              setDefaultSettings(updatedValues)
            }
          } catch (error) {
            console.warn('PrintSettings: تحذير في تحديث الألوان:', error)
          }
        }
      } else {
        // fallback للطريقة القديمة
        if (printForm && printForm.getFieldsValue) {
          try {
            const currentValues = printForm.getFieldsValue()
            if (currentValues !== undefined) {
              const updatedValues = {
                ...currentValues,
                secondaryColor: '#fff3cd'
              }

              await handleSavePrintSettings(updatedValues)
              printForm.setFieldsValue(updatedValues)
              setDefaultSettings(updatedValues)
            }
          } catch (error) {
            console.warn('PrintSettings: تحذير في تحديث الألوان (fallback):', error)
          }
        }
        message.success('تم تحديث الألوان بنجاح')
      }
    } catch (error) {
      Logger.error('PrintSettings', 'خطأ في تحديث الألوان:', error)
      message.error('فشل في تحديث الألوان')
    }
  }

  const handleResetCompanySettings = () => {
    companyForm.resetFields()
    message.info('تم إعادة تعيين معلومات الشركة')
  }

  // حذف الإعدادات المكررة
  const handleRemoveDuplicateSettings = async () => {
    try {
      const result = await window.electronAPI?.invoke('remove-duplicate-logo-settings')
      if (result?.success) {
        message.success('تم حذف الإعدادات المكررة بنجاح')
        // إعادة تحميل الإعدادات
        loadSettings()
        loadCompanySettings()
      } else {
        message.error(result?.message || 'فشل في حذف الإعدادات المكررة')
      }
    } catch (error) {
      Logger.error('PrintSettings', 'خطأ في حذف الإعدادات المكررة:', error)
      message.error('حدث خطأ في حذف الإعدادات المكررة')
    }
  }

  const handleExportSettings = async () => {
    try {
      const settingsData = {
        printSettings: defaultSettings,
        companySettings: companySettings,
        templates: printTemplates,
        exportDate: new Date().toISOString(),
        version: '1.0'
      }

      const dataStr = JSON.stringify(settingsData, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)

      const link = document.createElement('a')
      link.href = url
      link.download = `print-settings-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      message.success('تم تصدير إعدادات الطباعة بنجاح')
    } catch (error) {
      Logger.error('PrintSettings', 'خطأ في تصدير الإعدادات:', error)
      message.error('فشل في تصدير إعدادات الطباعة')
    }
  }

  const handleImportSettings = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const importedData = JSON.parse(e.target?.result as string)

        if (importedData.printSettings) {
          setDefaultSettings(importedData.printSettings)
          // تحديث النموذج فقط إذا كان متصل بـ Form element
          if (printForm && printForm.getFieldsValue) {
            try {
              const currentValues = printForm.getFieldsValue()
              if (currentValues !== undefined) {
                printForm.setFieldsValue(importedData.printSettings)
              }
            } catch (error) {
              console.warn('PrintSettings: تحذير في استيراد إعدادات الطباعة:', error)
            }
          }
        }

        if (importedData.companySettings) {
          setCompanySettings(importedData.companySettings)
          // سيتم تعيين قيم النموذج تلقائياً عبر useEffect
        }

        if (importedData.templates) {
          setPrintTemplates(importedData.templates)
        }

        message.success('تم استيراد إعدادات الطباعة بنجاح')
      } catch (error) {
        Logger.error('PrintSettings', 'خطأ في استيراد الإعدادات:', error)
        message.error('فشل في استيراد إعدادات الطباعة - تأكد من صحة الملف')
      }
    }
    reader.readAsText(file)
    return false // منع الرفع التلقائي
  }

  // تبويب إعدادات الطباعة المتقدمة
  const printSettingsTab = (
    <Card title={
      <Space>
        <PrinterOutlined />
        <span>إعدادات الطباعة</span>
      </Space>
    }>
      <Form
        form={printForm}
        layout="vertical"
        onFinish={handleSavePrintSettings}
        onValuesChange={handleFormValuesChange}
        initialValues={defaultSettings}
        preserve={false}
      >
        <Row gutter={[24, 16]}>
          <Col xs={24} lg={12}>
            <Card size="small" title="إعدادات الصفحة" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item label="حجم الورق" name="pageSize">
                    <Select>
                      <Select.Option value="A4">A4</Select.Option>
                      <Select.Option value="A5">A5</Select.Option>
                      <Select.Option value="A3">A3</Select.Option>
                      <Select.Option value="Letter">Letter</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="اتجاه الصفحة" name="orientation">
                    <Radio.Group>
                      <Radio value="portrait">عمودي</Radio>
                      <Radio value="landscape">أفقي</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col xs={12} md={6}>
                  <Form.Item label="الهامش العلوي" name={['margins', 'top']}>
                    <InputNumber min={0} max={50} addonAfter="مم" />
                  </Form.Item>
                </Col>
                <Col xs={12} md={6}>
                  <Form.Item label="الهامش السفلي" name={['margins', 'bottom']}>
                    <InputNumber min={0} max={50} addonAfter="مم" />
                  </Form.Item>
                </Col>
                <Col xs={12} md={6}>
                  <Form.Item label="الهامش الأيسر" name={['margins', 'left']}>
                    <InputNumber min={0} max={50} addonAfter="مم" />
                  </Form.Item>
                </Col>
                <Col xs={12} md={6}>
                  <Form.Item label="الهامش الأيمن" name={['margins', 'right']}>
                    <InputNumber min={0} max={50} addonAfter="مم" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            <Card size="small" title="إعدادات النص" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item label="نوع الخط" name="fontFamily">
                    <Select>
                      <Select.Option value="Arial">Arial</Select.Option>
                      <Select.Option value="Times New Roman">Times New Roman</Select.Option>
                      <Select.Option value="Calibri">Calibri</Select.Option>
                      <Select.Option value="Tahoma">Tahoma</Select.Option>
                      <Select.Option value="Cairo">Cairo</Select.Option>
                      <Select.Option value="Amiri">Amiri</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="حجم الخط" name="fontSize">
                    <Slider min={8} max={24} marks={{ 8: '8', 12: '12', 16: '16', 20: '20', 24: '24' }} />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card size="small" title="إعدادات التنسيق المتقدمة" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col xs={24} md={8}>
                  <Form.Item label="تباعد الأسطر" name="lineSpacing">
                    <Slider min={1} max={3} step={0.1} marks={{ 1: '1x', 1.5: '1.5x', 2: '2x', 3: '3x' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="سماكة الحدود" name="borderWidth">
                    <Slider min={0.5} max={5} step={0.5} marks={{ 0.5: '0.5', 2: '2', 5: '5' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="حجم العناوين" name="headerSize">
                    <Slider min={14} max={28} marks={{ 14: '14', 18: '18', 24: '24', 28: '28' }} />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item label="المسافة بين الأقسام" name="sectionSpacing">
                    <Slider min={5} max={30} marks={{ 5: '5px', 15: '15px', 30: '30px' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="عرض الجدول" name="tableWidth">
                    <Slider min={80} max={100} marks={{ 80: '80%', 90: '90%', 100: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            <Card size="small" title="إعدادات العرض" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col xs={12}>
                  <Form.Item name="showHeader" valuePropName="checked">
                    <Checkbox>إظهار الهيدر</Checkbox>
                  </Form.Item>
                </Col>
                <Col xs={12}>
                  <Form.Item name="showFooter" valuePropName="checked">
                    <Checkbox>إظهار الفوتر</Checkbox>
                  </Form.Item>
                </Col>
                <Col xs={12}>
                  <Form.Item name="showLogo" valuePropName="checked">
                    <Checkbox>إظهار الشعار</Checkbox>
                  </Form.Item>
                </Col>
                <Col xs={12}>
                  <Form.Item name="showSignature" valuePropName="checked">
                    <Checkbox>إظهار التوقيع</Checkbox>
                  </Form.Item>
                </Col>
                <Col xs={12}>
                  <Form.Item name="showTerms" valuePropName="checked">
                    <Checkbox>إظهار الشروط والأحكام</Checkbox>
                  </Form.Item>
                </Col>
                <Col xs={12}>
                  <Form.Item name="showQR" valuePropName="checked">
                    <Checkbox>إظهار رمز QR</Checkbox>
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <Card size="small" title="النصوص المخصصة" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item label="نص الهيدر المخصص" name="headerText">
                    <Input.TextArea rows={2} placeholder="نص يظهر في أعلى الفاتورة" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="نص الفوتر المخصص" name="footerText">
                    <Input.TextArea rows={2} placeholder="نص يظهر في أسفل الفاتورة" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item label="موقع الشعار" name="logoPosition">
                    <Select>
                      <Select.Option value="top-left">أعلى يسار</Select.Option>
                      <Select.Option value="top-center">أعلى وسط</Select.Option>
                      <Select.Option value="top-right">أعلى يمين</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="حجم الشعار" name="logoSize">
                    <Select>
                      <Select.Option value="small">صغير</Select.Option>
                      <Select.Option value="medium">متوسط</Select.Option>
                      <Select.Option value="large">كبير</Select.Option>
                      <Select.Option value="extra-large">كبير جداً</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* قسم التحكم في الألوان */}
        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <Card size="small" title="🎨 التحكم في الألوان" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col xs={24} md={8}>
                  <Form.Item label="اللون الأساسي" name="primaryColor">
                    <Input
                      type="color"
                      style={{ width: '100%', height: 40 }}
                      onChange={(e) => {
                        const newColor = e.target.value
                        if (updateCentralSettings) {
                          updateCentralSettings({ primaryColor: newColor })
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="اللون الثانوي" name="secondaryColor">
                    <Input
                      type="color"
                      style={{ width: '100%', height: 40 }}
                      onChange={(e) => {
                        const newColor = e.target.value
                        if (updateCentralSettings) {
                          updateCentralSettings({ secondaryColor: newColor })
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="لون الحدود" name="borderColor">
                    <Input
                      type="color"
                      style={{ width: '100%', height: 40 }}
                      onChange={(e) => {
                        const newColor = e.target.value
                        if (updateCentralSettings) {
                          updateCentralSettings({ borderColor: newColor })
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={[16, 16]}>
                <Col xs={24} md={8}>
                  <Form.Item label="لون الخلفية" name="backgroundColor">
                    <Input
                      type="color"
                      style={{ width: '100%', height: 40 }}
                      onChange={(e) => {
                        const newColor = e.target.value
                        if (updateCentralSettings) {
                          updateCentralSettings({ backgroundColor: newColor })
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="لون النص" name="textColor">
                    <Input
                      type="color"
                      style={{ width: '100%', height: 40 }}
                      onChange={(e) => {
                        const newColor = e.target.value
                        if (updateCentralSettings) {
                          updateCentralSettings({ textColor: newColor })
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <div style={{ display: 'flex', gap: 8, alignItems: 'end', height: 40 }}>
                    <Button
                      type="primary"
                      icon={<BgColorsOutlined />}
                      onClick={() => {
                        if (updateCentralSettings) {
                          updateCentralSettings({
                            primaryColor: '#1890ff',
                            secondaryColor: '#fff3cd',
                            borderColor: '#d9d9d9',
                            backgroundColor: '#ffffff',
                            textColor: '#000000'
                          })
                          message.success('تم إعادة تعيين الألوان للافتراضية')
                        }
                      }}
                    >
                      إعادة تعيين الألوان
                    </Button>
                  </div>
                </Col>
              </Row>
              <Row>
                <Col span={24}>
                  <Alert
                    message="💡 نصيحة"
                    description="تغيير الألوان هنا سيؤثر على جميع نماذج الطباعة (الفواتير، الإيصالات، التقارير) تلقائياً"
                    type="info"
                    showIcon
                    style={{ marginTop: 16 }}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <Card size="small" title="إعدادات متقدمة">
              <Row gutter={[16, 16]}>
                <Col xs={24} md={8}>
                  <Form.Item label="جودة الطباعة" name="quality">
                    <Select>
                      <Select.Option value="draft">مسودة</Select.Option>
                      <Select.Option value="normal">عادية</Select.Option>
                      <Select.Option value="high">عالية</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="عدد النسخ" name="copies">
                    <InputNumber min={1} max={10} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item name="autoSave" valuePropName="checked">
                    <Checkbox>حفظ تلقائي للإعدادات</Checkbox>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item name="watermark" valuePropName="checked">
                    <Checkbox>إضافة علامة مائية</Checkbox>
                  </Form.Item>
                  <Form.Item label="نص العلامة المائية" name="watermarkText">
                    <Input placeholder="نص العلامة المائية" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="شفافية العلامة المائية" name="watermarkOpacity">
                    <Slider min={0.05} max={0.5} step={0.05} marks={{ 0.1: '10%', 0.3: '30%', 0.5: '50%' }} />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        <Divider />

        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <Space wrap>
              <Button type="primary" htmlType="submit" loading={saveLoading} icon={<SaveOutlined />}>
                حفظ إعدادات الطباعة
              </Button>
              <Button onClick={handleUpdateColors} icon={<ReloadOutlined />} style={{ backgroundColor: '#fff3cd', borderColor: '#fff3cd' }}>
                تحديث الألوان
              </Button>
              <Button onClick={handleTestPrint} loading={testPrintLoading} icon={<EyeOutlined />}>
                طباعة تجريبية
              </Button>

              <Button onClick={handleResetPrintSettings} icon={<ReloadOutlined />}>
                إعادة تعيين
              </Button>
              <Button onClick={handleExportSettings} icon={<DownloadOutlined />}>
                تصدير الإعدادات
              </Button>
              <Upload
                accept=".json"
                beforeUpload={handleImportSettings}
                showUploadList={false}
              >
                <Button icon={<CloudUploadOutlined />}>
                  استيراد الإعدادات
                </Button>
              </Upload>
              <Button
                onClick={handleRemoveDuplicateSettings}
                icon={<DeleteOutlined />}
                type="dashed"
                danger
              >
                حذف الإعدادات المكررة
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    </Card>
  )

  const companyInfoTab = (
    <Card title={
      <Space>
        <SettingOutlined />
        <span>معلومات الشركة</span>
      </Space>
    }>
      <Form
        form={companyForm}
        layout="vertical"
        onFinish={handleCompanySave}
        initialValues={companySettings}
        preserve={false}
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <Form.Item
              label="اسم الشركة (عربي)"
              name="name"
              rules={[{ required: true, message: 'يرجى إدخال اسم الشركة' }]}
            >
              <Input placeholder="اسم الشركة" />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              label="اسم الشركة (إنجليزي)"
              name="nameEn"
            >
              <Input placeholder="Company Name" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <Form.Item
              label="رقم الهاتف"
              name="phone"
              rules={[{ required: true, message: 'يرجى إدخال رقم الهاتف' }]}
            >
              <Input placeholder="رقم الهاتف" />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              label="البريد الإلكتروني"
              name="email"
              rules={[
                { required: true, message: 'يرجى إدخال البريد الإلكتروني' },
                { type: 'email', message: 'يرجى إدخال بريد إلكتروني صحيح' }
              ]}
            >
              <Input placeholder="البريد الإلكتروني" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <Form.Item
              label="الرقم الضريبي"
              name="taxNumber"
            >
              <Input placeholder="الرقم الضريبي" />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              label="رقم التسجيل"
              name="registrationNumber"
            >
              <Input placeholder="رقم التسجيل" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label="العنوان"
          name="address"
          rules={[{ required: true, message: 'يرجى إدخال العنوان' }]}
        >
          <Input.TextArea rows={3} placeholder="العنوان الكامل للشركة" />
        </Form.Item>

        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <Form.Item
              label="الموقع الإلكتروني"
              name="website"
            >
              <Input placeholder="www.company.com" />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              label="وصف الشركة"
              name="description"
            >
              <Input placeholder="وصف مختصر للشركة" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label="شعار الشركة"
          name="slogan"
        >
          <Input placeholder="شعار أو رسالة الشركة" />
        </Form.Item>



        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={saveLoading} icon={<SaveOutlined />}>
              حفظ معلومات الشركة
            </Button>
            <Button onClick={handleResetCompanySettings} icon={<ReloadOutlined />}>
              إعادة تعيين
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  )

  // تبويب قوالب الطباعة
  const printTemplatesTab = (
    <Card title={
      <Space>
        <FileTextOutlined />
        <span>قوالب الطباعة</span>
      </Space>
    }>
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24}>
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => setTemplateCreatorVisible(true)}>
              إضافة قالب جديد
            </Button>
            <Button icon={<DownloadOutlined />}>
              تصدير القوالب
            </Button>
            <Upload accept=".json" showUploadList={false}>
              <Button icon={<CloudUploadOutlined />}>
                استيراد القوالب
              </Button>
            </Upload>
          </Space>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {printTemplates.map((template) => (
          <Col xs={24} md={12} lg={8} key={template.id}>
            <Card
              size="small"
              title={
                <Space>
                  <FileTextOutlined />
                  <span>{template.name}</span>
                  {template.isDefault && <Tag color="blue">افتراضي</Tag>}
                  {!template.isActive && <Tag color="red">غير نشط</Tag>}
                </Space>
              }
              extra={
                <Space>
                  <Tooltip title="تعديل">
                    <Button
                      type="text"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => {
                        setSelectedTemplate(template)
                        setTemplateCreatorVisible(true)
                      }}
                    />
                  </Tooltip>
                  <Tooltip title="نسخ">
                    <Button
                      type="text"
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => {
                        const newTemplate = {
                          ...template,
                          id: `${template.id}-copy-${Date.now()}`,
                          name: `${template.name} - نسخة`,
                          isDefault: false
                        }
                        setPrintTemplates([...printTemplates, newTemplate])
                        message.success('تم نسخ القالب بنجاح')
                      }}
                    />
                  </Tooltip>
                  {!template.isDefault && (
                    <Tooltip title="حذف">
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => {
                          Modal.confirm({
                            title: 'تأكيد الحذف',
                            content: `هل أنت متأكد من حذف القالب "${template.name}"؟`,
                            okText: 'حذف',
                            cancelText: 'إلغاء',
                            okType: 'danger',
                            onOk: () => {
                              setPrintTemplates(printTemplates.filter(t => t.id !== template.id))
                              message.success('تم حذف القالب بنجاح')
                            }
                          })
                        }}
                      />
                    </Tooltip>
                  )}
                </Space>
              }
            >
              <div style={{ marginBottom: 8 }}>
                <Text type="secondary">{template.description}</Text>
              </div>
              <div style={{ marginBottom: 8 }}>
                <Tag color="geekblue">{template.type}</Tag>
              </div>
              <div style={{ marginBottom: 16 }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  آخر تحديث: {new Date(template.updatedAt).toLocaleDateString('ar-SA')}
                </Text>
              </div>
              <Space>
                <Button
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => handleTemplatePreview(template)}
                  loading={previewLoading}
                >
                  معاينة
                </Button>
                <Button
                  size="small"
                  type={template.isDefault ? 'default' : 'primary'}
                  onClick={() => {
                    if (!template.isDefault) {
                      const updatedTemplates = printTemplates.map(t => ({
                        ...t,
                        isDefault: t.id === template.id
                      }))
                      setPrintTemplates(updatedTemplates)
                      message.success('تم تعيين القالب كافتراضي')
                    }
                  }}
                >
                  {template.isDefault ? 'افتراضي' : 'تعيين كافتراضي'}
                </Button>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>

      {/* مودال إضافة/تعديل القالب */}
      <Modal
        title={selectedTemplate ? 'تعديل القالب' : 'إضافة قالب جديد'}
        open={templateModalVisible}
        onCancel={() => {
          setTemplateModalVisible(false)
          setSelectedTemplate(null)
        }}
        footer={null}
        width={600}
      >
        <Form
          form={templateForm}
          layout="vertical"
          onFinish={(values) => {
            if (selectedTemplate) {
              // تعديل القالب
              const updatedTemplates = printTemplates.map(t =>
                t.id === selectedTemplate.id
                  ? { ...t, ...values, updatedAt: new Date().toISOString() }
                  : t
              )
              setPrintTemplates(updatedTemplates)
              message.success('تم تحديث القالب بنجاح')
            } else {
              // إضافة قالب جديد
              const newTemplate: PrintTemplate = {
                id: `template-${Date.now()}`,
                ...values,
                isDefault: false,
                isActive: true,
                settings: {},
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              }
              setPrintTemplates([...printTemplates, newTemplate])
              message.success('تم إضافة القالب بنجاح')
            }
            setTemplateModalVisible(false)
            setSelectedTemplate(null)
          }}
          initialValues={selectedTemplate || {
            name: '',
            description: '',
            type: 'invoice'
          }}
        >
          <Form.Item
            label="اسم القالب"
            name="name"
            rules={[{ required: true, message: 'يرجى إدخال اسم القالب' }]}
          >
            <Input placeholder="اسم القالب" />
          </Form.Item>

          <Form.Item
            label="وصف القالب"
            name="description"
          >
            <Input.TextArea rows={3} placeholder="وصف مختصر للقالب" />
          </Form.Item>

          <Form.Item
            label="نوع القالب"
            name="type"
            rules={[{ required: true, message: 'يرجى اختيار نوع القالب' }]}
          >
            <Select>
              <Select.Option value="invoice">فاتورة</Select.Option>
              <Select.Option value="receipt">إيصال</Select.Option>
              <Select.Option value="report">تقرير</Select.Option>
              <Select.Option value="certificate">شهادة</Select.Option>
              <Select.Option value="custom">مخصص</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {selectedTemplate ? 'تحديث' : 'إضافة'}
              </Button>
              <Button onClick={() => {
                setTemplateModalVisible(false)
                setSelectedTemplate(null)
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  )

  const themeTab = (
    <Card title={
      <Space>
        <BulbOutlined />
        <span>إعدادات الثيم</span>
      </Space>
    }>
      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Card size="small" title="تبديل الثيم">
            <ThemeToggle />
            <Divider />
            <Text type="secondary">
              يمكنك تبديل بين الوضع الليلي والنهاري حسب تفضيلك
            </Text>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card size="small" title="إحصائيات الثيم">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Statistic title="الوضع الحالي" value="النهاري" prefix={<SunOutlined />} />
              <Statistic title="مرات التبديل اليوم" value={5} prefix={<DesktopOutlined />} />
            </Space>
          </Card>
        </Col>
      </Row>
    </Card>
  )

  const tabItems = [
    {
      key: 'print-settings',
      label: (
        <Space>
          <PrinterOutlined />
          إعدادات الطباعة
        </Space>
      ),
      children: printSettingsTab
    },
    {
      key: 'company',
      label: (
        <Space>
          <SettingOutlined />
          معلومات الشركة
        </Space>
      ),
      children: companyInfoTab
    },
    {
      key: 'templates',
      label: (
        <Space>
          <FileTextOutlined />
          قوالب الطباعة
        </Space>
      ),
      children: printTemplatesTab
    },
    {
      key: 'theme',
      label: (
        <Space>
          <BulbOutlined />
          الثيم
        </Space>
      ),
      children: themeTab
    },
    {
      key: 'tables',
      label: (
        <Space>
          <TableOutlined />
          الجداول المحسنة
        </Space>
      ),
      children: (
        <Card title={
          <Space>
            <TableOutlined />
            <span>عرض الجداول المحسنة</span>
          </Space>
        }>
          <EnhancedTableDemo />
        </Card>
      )
    }
  ]

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        {/* إحصائيات سريعة */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="قوالب الطباعة"
                value={printTemplates.length}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: defaultSettings?.primaryColor || '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="القوالب النشطة"
                value={printTemplates.filter(t => t.isActive).length}
                prefix={<CheckOutlined />}
                valueStyle={{ color: defaultSettings?.secondaryColor || '#fff3cd' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="آخر حفظ"
                value="اليوم"
                prefix={<DatabaseOutlined />}
                valueStyle={{ color: defaultSettings?.borderColor || '#d9d9d9' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="حالة النظام"
                value="متصل"
                prefix={<InfoCircleOutlined />}
                valueStyle={{ color: defaultSettings?.primaryColor || '#1890ff' }}
              />
            </Card>
          </Col>
        </Row>

        <Card>
          <div style={{ marginBottom: '24px' }}>
            <Title level={2}>
              <Space>
                <PrinterOutlined />
                إعدادات الطباعة والنظام المتكامل
              </Space>
            </Title>
            <Text type="secondary">
              إدارة شاملة لإعدادات الطباعة ومعلومات الشركة وقوالب الطباعة مع دعم كامل للعربية
            </Text>
          </div>

          {loading ? (
            <LoadingSkeletons.Page />
          ) : (
            <Tabs
              defaultActiveKey="print-settings"
              items={tabItems}
              size="large"
              tabPosition="top"
              animated={{ inkBar: true, tabPane: true }}
            />
          )}
        </Card>

        {/* تنبيهات مهمة */}
        <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
          <Col xs={24}>
            <Alert
              message="نصائح مهمة"
              description={
                <ul style={{ margin: 0, paddingRight: 20 }}>
                  <li>تأكد من حفظ الإعدادات بعد كل تغيير</li>
                  <li>استخدم الطباعة التجريبية لاختبار الإعدادات قبل الطباعة الفعلية</li>
                  <li>يمكنك تصدير واستيراد الإعدادات للنسخ الاحتياطي</li>
                  <li>القوالب الافتراضية لا يمكن حذفها ولكن يمكن تعديلها</li>
                </ul>
              }
              type="info"
              showIcon
              closable
            />
          </Col>
        </Row>

        {/* مكون إنشاء القوالب الجديد */}
        <PrintTemplateCreator
          visible={templateCreatorVisible}
          onClose={() => {
            setTemplateCreatorVisible(false)
            setSelectedTemplate(null)
          }}

        />
      </div>
    </div>
  )
}

export default PrintSettings
