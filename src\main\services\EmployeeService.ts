import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { Logger } from '../utils/logger'
import type {
  Employee,
  CreateEmployeeData,
  Department,
  AttendanceRecord
} from '../shared/types/employee'

// استخدام الأنواع المستوردة من الملف المشترك

export class EmployeeService {
  private static instance: EmployeeService
  private db: any

  private constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
  }

  public static getInstance(): EmployeeService {
    if (!EmployeeService.instance) {
      EmployeeService.instance = new EmployeeService()
    }
    return EmployeeService.instance
  }

  // تحديث مخطط جدول الموّفين
  private async updateEmployeeTableSchema(): Promise<void> {
    try {
      // التحقق من وجود الأعمدة الجديدة
      const tableInfo = this.db.prepare("PRAGMA table_info(employees)").all()
      const existingColumns = tableInfo.map((col: any) => col.name)

      const newColumns = [
        { name: 'first_name', type: 'TEXT' },
        { name: 'last_name', type: 'TEXT' },
        { name: 'full_name', type: 'TEXT' },
        { name: 'national_id', type: 'TEXT' },
        { name: 'fingerprint_id', type: 'TEXT' },
        { name: 'fingerprint_device_id', type: 'INTEGER' },
        { name: 'job_title', type: 'TEXT' },
        { name: 'direct_manager_id', type: 'INTEGER' },
        { name: 'employment_type', type: 'TEXT DEFAULT "full_time"' },
        { name: 'salary_type', type: 'TEXT DEFAULT "monthly"' },
        { name: 'basic_salary', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'hourly_rate', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'overtime_rate', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'allowances', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'birth_date', type: 'DATE' },
        { name: 'termination_date', type: 'DATE' },
        { name: 'termination_reason', type: 'TEXT' },
        { name: 'social_insurance_number', type: 'TEXT' },
        { name: 'tax_number', type: 'TEXT' },
        { name: 'bank_account', type: 'TEXT' },
        { name: 'emergency_contact_name', type: 'TEXT' },
        { name: 'emergency_contact_phone', type: 'TEXT' },
        { name: 'card_number', type: 'TEXT' },
        { name: 'access_level', type: 'INTEGER DEFAULT 1' },
        { name: 'working_hours_start', type: 'TIME DEFAULT "08:00"' },
        { name: 'working_hours_end', type: 'TIME DEFAULT "17:00"' },
        { name: 'notes', type: 'TEXT' },
        { name: 'status', type: 'TEXT DEFAULT "active"' },
        { name: 'created_by', type: 'INTEGER' }
      ]

      // إضافة الأعمدة المفقودة
      for (const column of newColumns) {
        if (!existingColumns.includes(column.name)) {
          try {
            this.db.exec(`ALTER TABLE employees ADD COLUMN ${column.name} ${column.type}`)
            Logger.info('EmployeeService', '✅ تم إضافة العمود: ' + column.name)
          } catch (error) {
            Logger.info('EmployeeService', '⚠️ فشل في إضافة العمود ' + column.name + ':', error)
          }
        }
      }

      // إصلاح البيانات الموجودة
      await this.fixExistingEmployeeData()
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في تحديث مخطط جدول الموّفين:', error)
    }
  }

  // إصلاح البيانات الموجودة للموّفين
  private async fixExistingEmployeeData(): Promise<void> {
    try {
      Logger.info('EmployeeService', '🔧 بدء إصلاح البيانات الموجودة للموّفين...')

      // 1. تحديث is_active للموّفين الذين لا يحتوون على قيمة
      const updateActiveResult = this.db.prepare(`
        UPDATE employees
        SET is_active = 1
        WHERE is_active IS NULL OR is_active = 0
      `).run()

      if (updateActiveResult.changes > 0) {
        Logger.info('EmployeeService', '✅ تم تحديث حالة النشاط لـ ${updateActiveResult.changes} موّف')
      }

      // 2. تحديث full_name للموّفين الذين لا يحتوون على قيمة
      const updateFullNameResult = this.db.prepare(`
        UPDATE employees
        SET full_name = name
        WHERE full_name IS NULL OR full_name = ''
      `).run()

      if (updateFullNameResult.changes > 0) {
        Logger.info('EmployeeService', '✅ تم تحديث الاسم الكامل لـ ${updateFullNameResult.changes} موّف')
      }

      // 3. تحديث status للموّفين الذين لا يحتوون على قيمة
      const updateStatusResult = this.db.prepare(`
        UPDATE employees
        SET status = 'active'
        WHERE status IS NULL OR status = ''
      `).run()

      if (updateStatusResult.changes > 0) {
        Logger.info('EmployeeService', '✅ تم تحديث الحالة لـ ${updateStatusResult.changes} موّف')
      }

      // 4. تحديث مخطط جدول الرواتب
      await this.updatePayrollTableSchema()

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()
      Logger.info('EmployeeService', '✅ تم إصلاح البيانات الموجودة بنجاح')

    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في إصلاح البيانات الموجودة:', error)
    }
  }

  // تحديث مخطط جدول الرواتب
  private async updatePayrollTableSchema(): Promise<void> {
    try {
      // التحقق من وجود الأعمدة الجديدة في جدول الرواتب
      const tableInfo = this.db.prepare("PRAGMA table_info(payroll_records)").all()
      const existingColumns = tableInfo.map((col: any) => col.name)

      const newColumns = [
        { name: 'bonus', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'commission', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'gross_salary', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'updated_at', type: 'DATETIME DEFAULT CURRENT_TIMESTAMP' }
      ]

      // إضافة الأعمدة المفقودة
      for (const column of newColumns) {
        if (!existingColumns.includes(column.name)) {
          try {
            this.db.exec(`ALTER TABLE payroll_records ADD COLUMN ${column.name} ${column.type}`)
            Logger.info('EmployeeService', '✅ تم إضافة العمود في جدول الرواتب: ' + column.name)
          } catch (error) {
            Logger.info('EmployeeService', '⚠️ فشل في إضافة العمود ' + column.name + ' في جدول الرواتب:', error)
          }
        }
      }

    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في تحديث مخطط جدول الرواتب:', error)
    }
  }

  // إنشاء جداول الموّفين
  public async createEmployeeTables(): Promise<void> {
    const database = this.db

    // جدول الأقسام
    database.exec(`
      CREATE TABLE IF NOT EXISTS departments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE,
        name TEXT UNIQUE NOT NULL,
        description TEXT,
        manager_id INTEGER,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (manager_id) REFERENCES employees (id)
      )
    `)

    // إضافة عمود code إذا لم يكن موجوداً (للتوافق مع قواعد البيانات الموجودة)
    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('departments', 'code TEXT')
    } catch {
      // العمود موجود بالفعل أو خطأ آخر - تجاهل
    }

    // تحديث مخطط جدول الموّفين إذا كان موجوداً
    await this.updateEmployeeTableSchema()

    // جدول الموّفين
    database.exec(`
      CREATE TABLE IF NOT EXISTS employees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        first_name TEXT,
        last_name TEXT,
        full_name TEXT,
        national_id TEXT,
        fingerprint_id TEXT,
        fingerprint_device_id INTEGER,
        position TEXT,
        job_title TEXT,
        department_id INTEGER,
        direct_manager_id INTEGER,
        employment_type TEXT DEFAULT 'full_time',
        salary_type TEXT DEFAULT 'monthly',
        basic_salary DECIMAL(10,2) DEFAULT 0,
        hourly_rate DECIMAL(10,2) DEFAULT 0,
        overtime_rate DECIMAL(10,2) DEFAULT 0,
        allowances DECIMAL(10,2) DEFAULT 0,
        phone TEXT,
        email TEXT,
        address TEXT,
        birth_date DATE,
        hire_date DATE NOT NULL,
        termination_date DATE,
        termination_reason TEXT,
        social_insurance_number TEXT,
        tax_number TEXT,
        bank_account TEXT,
        emergency_contact_name TEXT,
        emergency_contact_phone TEXT,
        card_number TEXT,
        access_level INTEGER DEFAULT 1,
        working_hours_start TIME DEFAULT '08:00',
        working_hours_end TIME DEFAULT '17:00',
        notes TEXT,
        status TEXT DEFAULT 'active',
        salary DECIMAL(10,2) DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER,
        FOREIGN KEY (department_id) REFERENCES departments (id),
        FOREIGN KEY (direct_manager_id) REFERENCES employees (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // جدول الحضور والانصراف
    database.exec(`
      CREATE TABLE IF NOT EXISTS attendance_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        date DATE NOT NULL,
        check_in TIME,
        check_out TIME,
        hours_worked DECIMAL(4,2),
        status TEXT DEFAULT 'present' CHECK (status IN ('present', 'absent', 'late', 'early_leave')),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (employee_id) REFERENCES employees (id),
        UNIQUE(employee_id, date)
      )
    `)

    // جدول أجهزة البصمة
    database.exec(`
      CREATE TABLE IF NOT EXISTS fingerprint_devices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        device_name TEXT NOT NULL,
        device_model TEXT,
        ip_address TEXT,
        port INTEGER DEFAULT 4370,
        device_id INTEGER NOT NULL,
        location TEXT,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
        last_sync DATETIME,
        firmware_version TEXT,
        capacity INTEGER DEFAULT 1000,
        current_users INTEGER DEFAULT 0,
        notes TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(device_id),
        UNIQUE(ip_address, port)
      )
    `)

    // جدول الإجازات
    database.exec(`
      CREATE TABLE IF NOT EXISTS employee_leaves (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        leave_type TEXT NOT NULL CHECK (leave_type IN ('annual', 'sick', 'emergency', 'maternity', 'other')),
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        days_count INTEGER NOT NULL,
        reason TEXT,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
        approved_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (employee_id) REFERENCES employees (id),
        FOREIGN KEY (approved_by) REFERENCES users (id)
      )
    `)

    // جدول الرواتب
    database.exec(`
      CREATE TABLE IF NOT EXISTS payroll_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        month INTEGER NOT NULL,
        year INTEGER NOT NULL,
        basic_salary DECIMAL(10,2) NOT NULL,
        allowances DECIMAL(10,2) DEFAULT 0,
        deductions DECIMAL(10,2) DEFAULT 0,
        overtime_hours DECIMAL(4,2) DEFAULT 0,
        overtime_amount DECIMAL(10,2) DEFAULT 0,
        bonus DECIMAL(10,2) DEFAULT 0,
        commission DECIMAL(10,2) DEFAULT 0,
        gross_salary DECIMAL(10,2) DEFAULT 0,
        net_salary DECIMAL(10,2) NOT NULL,
        payment_date DATE,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'cancelled', 'calculated')),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (employee_id) REFERENCES employees (id),
        UNIQUE(employee_id, month, year)
      )
    `)

    // إنشاء الفهارس
    database.exec('CREATE INDEX IF NOT EXISTS idx_employees_code ON employees(employee_code)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_employees_department ON employees(department_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_employees_active ON employees(is_active)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_attendance_employee ON attendance_records(employee_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance_records(date)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_leaves_employee ON employee_leaves(employee_id)')
    database.exec('CREATE INDEX IF NOT EXISTS idx_payroll_employee ON payroll_records(employee_id)')
  }

  // الحصول على جميع الموّفين
  public async getEmployees(): Promise<Employee[]> {
    try {
      const employees = this.db.prepare(`
        SELECT e.*, d.name as department_name, m.name as manager_name
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN employees m ON e.direct_manager_id = m.id
        WHERE e.is_active = 1
        ORDER BY e.name
      `).all() as Employee[]

      return employees
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في جلب الموّفين:', error)
      return []
    }
  }

  // إنشاء موّف جديد
  public async createEmployee(employeeData: CreateEmployeeData): Promise<ApiResponse> {
    try {
      // التحقق من صحة البيانات الأساسية
      if (!employeeData.name || employeeData.name.trim() === '') {
        return { success: false, message: 'اسم الموّف مطلوب' }
      }

      if (!employeeData.hire_date) {
        return { success: false, message: 'تاريخ التوّيف مطلوب' }
      }

      // توليد كود الموّف إذا لم يتم توفيره
      if (!employeeData.employee_code) {
        employeeData.employee_code = await this.generateEmployeeCode()
      }

      // التحقق من تفرد كود الموّف
      const existingEmployee = this.db.prepare('SELECT id FROM employees WHERE employee_code = ?').get(employeeData.employee_code.trim())
      if (existingEmployee) {
        return { success: false, message: 'كود الموّف موجود مسبقاً' }
      }

      // التحقق من تفرد البريد الإلكتروني
      if (employeeData.email && employeeData.email.trim()) {
        const existingEmail = this.db.prepare('SELECT id FROM employees WHERE email = ?').get(employeeData.email.trim())
        if (existingEmail) {
          return { success: false, message: 'البريد الإلكتروني موجود مسبقاً' }
        }
      }

      const result = this.db.prepare(`
        INSERT INTO employees (
          employee_code, name, first_name, last_name, full_name, national_id, fingerprint_id, fingerprint_device_id,
          position, job_title, department_id, direct_manager_id, employment_type, salary_type,
          basic_salary, hourly_rate, overtime_rate, allowances, phone, email, address, birth_date,
          hire_date, social_insurance_number, tax_number, bank_account, emergency_contact_name,
          emergency_contact_phone, card_number, access_level, working_hours_start, working_hours_end,
          notes, status, salary, is_active, created_by, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `).run([
        employeeData.employee_code,
        employeeData.name,
        employeeData.first_name || null,
        employeeData.last_name || null,
        employeeData.full_name || null,
        employeeData.national_id || null,
        employeeData.fingerprint_id || null,
        employeeData.fingerprint_device_id || null,
        employeeData.position || null,
        employeeData.job_title || null,
        employeeData.department_id || null,
        employeeData.direct_manager_id || null,
        employeeData.employment_type || 'full_time',
        employeeData.salary_type || 'monthly',
        employeeData.basic_salary || 0,
        employeeData.hourly_rate || 0,
        employeeData.overtime_rate || 0,
        employeeData.allowances || 0,
        employeeData.phone || null,
        employeeData.email || null,
        employeeData.address || null,
        employeeData.birth_date || null,
        employeeData.hire_date,
        employeeData.social_insurance_number || null,
        employeeData.tax_number || null,
        employeeData.bank_account || null,
        employeeData.emergency_contact_name || null,
        employeeData.emergency_contact_phone || null,
        employeeData.card_number || null,
        employeeData.access_level || 1,
        employeeData.working_hours_start || '08:00',
        employeeData.working_hours_end || '17:00',
        employeeData.notes || null,
        employeeData.status || 'active',
        employeeData.salary || 0,
        employeeData.is_active ? 1 : 0,
        employeeData.created_by || null
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إنشاء الموّف بنجاح', data: { id: result.lastInsertRowid } }
      } else {
        return { success: false, message: 'فشل في إنشاء الموّف' }
      }
    } catch (error: any) {
      Logger.error('EmployeeService', 'خطأ في إنشاء الموّف:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود الموّف موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في إنشاء الموّف' }
    }
  }

  // تحديث موّف
  public async updateEmployee(employeeId: number, employeeData: CreateEmployeeData): Promise<ApiResponse> {
    try {
      // التحقق من صحة البيانات الأساسية
      if (!employeeData.name || employeeData.name.trim() === '') {
        return { success: false, message: 'اسم الموّف مطلوب' }
      }

      if (!employeeData.hire_date) {
        return { success: false, message: 'تاريخ التوّيف مطلوب' }
      }

      // التحقق من وجود الموّف
      const existingEmployee = this.db.prepare('SELECT id FROM employees WHERE id = ?').get(employeeId)
      if (!existingEmployee) {
        return { success: false, message: 'الموّف غير موجود' }
      }

      // التحقق من تفرد كود الموّف (إذا تم تغييره)
      if (employeeData.employee_code) {
        const duplicateEmployee = this.db.prepare('SELECT id FROM employees WHERE employee_code = ? AND id != ?').get(employeeData.employee_code.trim(), employeeId)
        if (duplicateEmployee) {
          return { success: false, message: 'كود الموّف موجود مسبقاً' }
        }
      }

      // التحقق من تفرد البريد الإلكتروني (إذا تم تغييره)
      if (employeeData.email && employeeData.email.trim()) {
        const duplicateEmail = this.db.prepare('SELECT id FROM employees WHERE email = ? AND id != ?').get(employeeData.email.trim(), employeeId)
        if (duplicateEmail) {
          return { success: false, message: 'البريد الإلكتروني موجود مسبقاً' }
        }
      }

      const result = this.db.prepare(`
        UPDATE employees SET
          employee_code = ?, name = ?, first_name = ?, last_name = ?, full_name = ?, national_id = ?,
          fingerprint_id = ?, fingerprint_device_id = ?, position = ?, job_title = ?, department_id = ?,
          direct_manager_id = ?, employment_type = ?, salary_type = ?, basic_salary = ?, hourly_rate = ?,
          overtime_rate = ?, allowances = ?, phone = ?, email = ?, address = ?, birth_date = ?,
          hire_date = ?, social_insurance_number = ?, tax_number = ?, bank_account = ?,
          emergency_contact_name = ?, emergency_contact_phone = ?, card_number = ?, access_level = ?,
          working_hours_start = ?, working_hours_end = ?, notes = ?, status = ?, salary = ?,
          is_active = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run([
        employeeData.employee_code,
        employeeData.name,
        employeeData.first_name || null,
        employeeData.last_name || null,
        employeeData.full_name || null,
        employeeData.national_id || null,
        employeeData.fingerprint_id || null,
        employeeData.fingerprint_device_id || null,
        employeeData.position || null,
        employeeData.job_title || null,
        employeeData.department_id || null,
        employeeData.direct_manager_id || null,
        employeeData.employment_type || 'full_time',
        employeeData.salary_type || 'monthly',
        employeeData.basic_salary || 0,
        employeeData.hourly_rate || 0,
        employeeData.overtime_rate || 0,
        employeeData.allowances || 0,
        employeeData.phone || null,
        employeeData.email || null,
        employeeData.address || null,
        employeeData.birth_date || null,
        employeeData.hire_date,
        employeeData.social_insurance_number || null,
        employeeData.tax_number || null,
        employeeData.bank_account || null,
        employeeData.emergency_contact_name || null,
        employeeData.emergency_contact_phone || null,
        employeeData.card_number || null,
        employeeData.access_level || 1,
        employeeData.working_hours_start || '08:00',
        employeeData.working_hours_end || '17:00',
        employeeData.notes || null,
        employeeData.status || 'active',
        employeeData.salary || 0,
        employeeData.is_active ? 1 : 0,
        employeeId
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث الموّف بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث الموّف' }
      }
    } catch (error: any) {
      Logger.error('EmployeeService', 'خطأ في تحديث الموّف:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود الموّف أو البريد الإلكتروني موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في تحديث الموّف' }
    }
  }

  // حذف موّف
  public async deleteEmployee(employeeId: number): Promise<ApiResponse> {
    try {
      // بدلاً من الحذف الفعلي، نقوم بتعطيل الموّف
      const result = this.db.prepare(`
        UPDATE employees SET is_active = 0, updated_at = datetime('now')
        WHERE id = ?
      `).run(employeeId)

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم حذف الموّف بنجاح' }
      } else {
        return { success: false, message: 'الموّف غير موجود' }
      }
    } catch (error: any) {
      Logger.error('EmployeeService', 'خطأ في حذف الموّف:', error)
      return { success: false, message: 'حدث خطأ في حذف الموّف' }
    }
  }

  // الحصول على الأقسام
  public async getDepartments(): Promise<Department[]> {
    try {
      const departments = this.db.prepare(`
        SELECT d.*, e.name as manager_name
        FROM departments d
        LEFT JOIN employees e ON d.manager_id = e.id
        WHERE d.is_active = 1
        ORDER BY d.name
      `).all() as Department[]

      return departments
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في جلب الأقسام:', error)
      return []
    }
  }

  // إنشاء قسم جديد
  public async createDepartment(departmentData: { code?: string; name: string; description?: string; manager_id?: number }): Promise<ApiResponse> {
    try {
      // توليد كود القسم إذا لم يتم توفيره
      if (!departmentData.code) {
        departmentData.code = await this.generateDepartmentCode()
      }

      const result = this.db.prepare(`
        INSERT INTO departments (code, name, description, manager_id)
        VALUES (?, ?, ?, ?)
      `).run([
        departmentData.code,
        departmentData.name,
        departmentData.description || null,
        departmentData.manager_id || null
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إنشاء القسم بنجاح' }
      } else {
        return { success: false, message: 'فشل في إنشاء القسم' }
      }
    } catch (error: any) {
      Logger.error('EmployeeService', 'خطأ في إنشاء القسم:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'اسم القسم موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في إنشاء القسم' }
    }
  }

  // تسجيل حضور بكائن بيانات شامل
  public async recordAttendanceData(attendanceData: any): Promise<ApiResponse> {
    try {
      const {
        employee_id,
        date,
        check_in_time,
        check_out_time,
        break_start_time,
        break_end_time,
        status = 'present',
        notes,
        attendance_source: _attendance_source = 'manual'
      } = attendanceData

      if (!employee_id) {
        return { success: false, message: 'معرف الموّف مطلوب' }
      }

      if (!date) {
        return { success: false, message: 'التاريخ مطلوب' }
      }

      // حساب ساعات العمل إذا توفر وقت الحضور والانصراف
      let hoursWorked: number | null = null
      if (check_in_time && check_out_time) {
        const checkInDate = new Date(date + 'T' + check_in_time)
        const checkOutDate = new Date(date + 'T' + check_out_time)

        // طرح وقت الاستراحة إذا توفر
        let breakDuration = 0
        if (break_start_time && break_end_time) {
          const breakStart = new Date(date + 'T' + break_start_time)
          const breakEnd = new Date(date + 'T' + break_end_time)
          breakDuration = (breakEnd.getTime() - breakStart.getTime()) / (1000 * 60 * 60)
        }

        hoursWorked = (checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60) - breakDuration
      }

      const result = this.db.prepare(`
        INSERT OR REPLACE INTO attendance_records (
          employee_id, date, check_in, check_out, hours_worked, status, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run([
        employee_id,
        date,
        check_in_time,
        check_out_time,
        hoursWorked,
        status,
        notes
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تسجيل الحضور بنجاح' }
      } else {
        return { success: false, message: 'فشل في تسجيل الحضور' }
      }
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في تسجيل الحضور:', error)
      return { success: false, message: 'حدث خطأ في تسجيل الحضور' }
    }
  }

  // تسجيل حضور (الطريقة القديمة للتوافق)
  public async recordAttendance(employeeId: number, checkIn: string, date?: string): Promise<ApiResponse> {
    try {
      const attendanceDate = date || new Date().toISOString().split('T')[0]

      const result = this.db.prepare(`
        INSERT OR REPLACE INTO attendance_records (employee_id, date, check_in, status)
        VALUES (?, ?, ?, 'present')
      `).run([employeeId, attendanceDate, checkIn])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تسجيل الحضور بنجاح' }
      } else {
        return { success: false, message: 'فشل في تسجيل الحضور' }
      }
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في تسجيل الحضور:', error)
      return { success: false, message: 'حدث خطأ في تسجيل الحضور' }
    }
  }

  // تسجيل انصراف
  public async recordCheckOut(employeeId: number, checkOut: string, date?: string): Promise<ApiResponse> {
    try {
      const attendanceDate = date || new Date().toISOString().split('T')[0]

      // الحصول على وقت الحضور
      const attendance = this.db.prepare(`
        SELECT check_in FROM attendance_records
        WHERE employee_id = ? AND date = ?
      `).get(employeeId, attendanceDate) as any

      if (!attendance) {
        return { success: false, message: 'لم يتم تسجيل الحضور لهذا اليوم' }
      }

      // حساب ساعات العمل
      const checkInTime = new Date(attendanceDate + 'T' + attendance.check_in)
      const checkOutTime = new Date(attendanceDate + 'T' + checkOut)
      const hoursWorked = (checkOutTime.getTime() - checkInTime.getTime()) / (1000 * 60 * 60)

      const result = this.db.prepare(`
        UPDATE attendance_records
        SET check_out = ?, hours_worked = ?, updated_at = datetime('now')
        WHERE employee_id = ? AND date = ?
      `).run([checkOut, hoursWorked, employeeId, attendanceDate])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تسجيل الانصراف بنجاح' }
      } else {
        return { success: false, message: 'فشل في تسجيل الانصراف' }
      }
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في تسجيل الانصراف:', error)
      return { success: false, message: 'حدث خطأ في تسجيل الانصراف' }
    }
  }

  // جلب سجلات الحضور
  public async getEmployeeAttendance(filters?: any): Promise<AttendanceRecord[]> {
    try {
      let query = `
        SELECT
          ar.id,
          ar.employee_id,
          e.full_name as employee_name,
          e.employee_code,
          d.name as department_name,
          ar.date,
          ar.check_in,
          ar.check_out,
          ar.hours_worked,
          ar.status,
          ar.notes,
          ar.created_at,
          ar.updated_at
        FROM attendance_records ar
        LEFT JOIN employees e ON ar.employee_id = e.id
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE 1=1
      `

      const params: any[] = []

      // تطبيق الفلاتر
      if (filters?.employee_id) {
        query += ` AND ar.employee_id = ?`
        params.push(filters.employee_id)
      }

      if (filters?.department_id) {
        query += ` AND e.department_id = ?`
        params.push(filters.department_id)
      }

      if (filters?.date_from) {
        query += ` AND ar.date >= ?`
        params.push(filters.date_from)
      }

      if (filters?.date_to) {
        query += ` AND ar.date <= ?`
        params.push(filters.date_to)
      }

      if (filters?.status) {
        query += ` AND ar.status = ?`
        params.push(filters.status)
      }

      query += ` ORDER BY ar.date DESC, ar.check_in DESC`

      const records = this.db.prepare(query).all(params) as AttendanceRecord[]
      return records
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في جلب سجلات الحضور:', error)
      return []
    }
  }

  // جلب سجلات الحضور (دالة بديلة للتوافق)
  public async getAttendanceRecords(employeeId?: number, startDate?: string, endDate?: string): Promise<AttendanceRecord[]> {
    const filters: any = {}

    if (employeeId) filters.employee_id = employeeId
    if (startDate) filters.date_from = startDate
    if (endDate) filters.date_to = endDate

    return this.getEmployeeAttendance(filters)
  }

  // توليد كود قسم جديد
  public async generateDepartmentCode(): Promise<string> {
    try {
      const lastDepartment = this.db.prepare(`
        SELECT code FROM departments
        WHERE code LIKE 'DEPT%' AND code IS NOT NULL AND code != ''
        ORDER BY CAST(SUBSTR(code, 5) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastDepartment && lastDepartment.code && lastDepartment.code.length >= 8) {
        const codeNumber = lastDepartment.code.substring(4)
        const lastNumber = parseInt(codeNumber)

        if (!isNaN(lastNumber)) {
          const newNumber = lastNumber + 1
          return 'DEPT' + newNumber.toString().padStart(3, '0')
        }
      }

      return 'DEPT001'
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في توليد كود القسم:', error)
      return 'DEPT' + Date.now().toString().slice(-3)
    }
  }

  // توليد كود موّف جديد
  public async generateEmployeeCode(): Promise<string> {
    try {
      // محاولة توليد كود فريد مع إعادة المحاولة في حالة التضارب
      for (let attempt = 0; attempt < 10; attempt++) {
        const lastEmployee = this.db.prepare(`
          SELECT employee_code FROM employees
          WHERE employee_code LIKE 'EMP%' AND employee_code IS NOT NULL AND employee_code != ''
          ORDER BY CAST(SUBSTR(employee_code, 4) AS INTEGER) DESC
          LIMIT 1
        `).get() as any

        let newCode = 'EMP001'

        if (lastEmployee && lastEmployee.employee_code && lastEmployee.employee_code.length >= 6) {
          const codeNumber = lastEmployee.employee_code.substring(3)
          const lastNumber = parseInt(codeNumber)

          if (!isNaN(lastNumber)) {
            const newNumber = lastNumber + 1 + attempt // إضافة attempt لتجنب التضارب
            newCode = 'EMP' + newNumber.toString().padStart(3, '0')
          }
        }

        // التحقق من عدم وجود الكود
        const existingEmployee = this.db.prepare('SELECT id FROM employees WHERE employee_code = ?').get(newCode)
        if (!existingEmployee) {
          return newCode
        }
      }

      // إذا فشلت جميع المحاولات، استخدم timestamp
      return 'EMP' + Date.now().toString().slice(-3)
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في توليد كود الموّف:', error)
      return 'EMP' + Date.now().toString().slice(-3)
    }
  }

  // الحصول على أجهزة البصمة
  public async getFingerprintDevices(): Promise<any[]> {
    try {
      const devices = this.db.prepare(`
        SELECT * FROM fingerprint_devices
        WHERE is_active = 1
        ORDER BY device_name
      `).all()

      return devices
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في جلب أجهزة البصمة:', error)
      return []
    }
  }

  // إنشاء جهاز بصمة جديد
  public async createFingerprintDevice(deviceData: any): Promise<ApiResponse> {
    try {
      const {
        device_name,
        device_model,
        ip_address,
        port = 4370,
        device_id,
        location,
        status = 'active',
        firmware_version,
        capacity = 1000,
        current_users = 0,
        notes
      } = deviceData

      if (!device_name) {
        return { success: false, message: 'اسم الجهاز مطلوب' }
      }

      if (!device_id) {
        return { success: false, message: 'معرف الجهاز مطلوب' }
      }

      if (!ip_address) {
        return { success: false, message: 'عنوان IP مطلوب' }
      }

      // التحقق من عدم تكرار معرف الجهاز
      const existingDevice = this.db.prepare('SELECT id FROM fingerprint_devices WHERE device_id = ?').get(device_id)
      if (existingDevice) {
        return { success: false, message: 'معرف الجهاز موجود مسبقاً' }
      }

      // التحقق من عدم تكرار عنوان IP والمنفذ
      const existingIP = this.db.prepare('SELECT id FROM fingerprint_devices WHERE ip_address = ? AND port = ?').get(ip_address, port)
      if (existingIP) {
        return { success: false, message: 'عنوان IP والمنفذ مستخدمان مسبقاً' }
      }

      const result = this.db.prepare(`
        INSERT INTO fingerprint_devices (
          device_name, device_model, ip_address, port, device_id, location,
          status, firmware_version, capacity, current_users, notes, is_active,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
      `).run([
        device_name,
        device_model,
        ip_address,
        port,
        device_id,
        location,
        status,
        firmware_version,
        capacity,
        current_users,
        notes
      ])

      if (result.changes > 0) {
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إضافة جهاز البصمة بنجاح' }
      } else {
        return { success: false, message: 'فشل في إضافة جهاز البصمة' }
      }
    } catch (error: any) {
      Logger.error('EmployeeService', 'خطأ في إنشاء جهاز البصمة:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'بيانات الجهاز موجودة مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في إضافة جهاز البصمة' }
    }
  }

  // تحديث جهاز البصمة
  public async updateFingerprintDevice(deviceId: number, deviceData: any): Promise<ApiResponse> {
    try {
      const {
        device_name,
        device_model,
        ip_address,
        port,
        device_id,
        location,
        status,
        firmware_version,
        capacity,
        current_users,
        notes
      } = deviceData

      if (!device_name) {
        return { success: false, message: 'اسم الجهاز مطلوب' }
      }

      if (!device_id) {
        return { success: false, message: 'معرف الجهاز مطلوب' }
      }

      if (!ip_address) {
        return { success: false, message: 'عنوان IP مطلوب' }
      }

      // التحقق من عدم تكرار معرف الجهاز (باستثناء الجهاز الحالي)
      const existingDevice = this.db.prepare('SELECT id FROM fingerprint_devices WHERE device_id = ? AND id != ?').get(device_id, deviceId)
      if (existingDevice) {
        return { success: false, message: 'معرف الجهاز موجود مسبقاً' }
      }

      // التحقق من عدم تكرار عنوان IP والمنفذ (باستثناء الجهاز الحالي)
      const existingIP = this.db.prepare('SELECT id FROM fingerprint_devices WHERE ip_address = ? AND port = ? AND id != ?').get(ip_address, port, deviceId)
      if (existingIP) {
        return { success: false, message: 'عنوان IP والمنفذ مستخدمان مسبقاً' }
      }

      const result = this.db.prepare(`
        UPDATE fingerprint_devices SET
          device_name = ?, device_model = ?, ip_address = ?, port = ?, device_id = ?,
          location = ?, status = ?, firmware_version = ?, capacity = ?, current_users = ?,
          notes = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run([
        device_name,
        device_model,
        ip_address,
        port,
        device_id,
        location,
        status,
        firmware_version,
        capacity,
        current_users,
        notes,
        deviceId
      ])

      if (result.changes > 0) {
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث جهاز البصمة بنجاح' }
      } else {
        return { success: false, message: 'لم يتم العثور على الجهاز' }
      }
    } catch (error: any) {
      Logger.error('EmployeeService', 'خطأ في تحديث جهاز البصمة:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'بيانات الجهاز موجودة مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في تحديث جهاز البصمة' }
    }
  }

  // حذف جهاز البصمة
  public async deleteFingerprintDevice(deviceId: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود الجهاز
      const device = this.db.prepare('SELECT * FROM fingerprint_devices WHERE id = ?').get(deviceId)
      if (!device) {
        return { success: false, message: 'لم يتم العثور على الجهاز' }
      }

      // حذف الجهاز (soft delete)
      const result = this.db.prepare(`
        UPDATE fingerprint_devices SET
          is_active = 0,
          updated_at = datetime('now')
        WHERE id = ?
      `).run(deviceId)

      if (result.changes > 0) {
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم حذف جهاز البصمة بنجاح' }
      } else {
        return { success: false, message: 'فشل في حذف جهاز البصمة' }
      }
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في حذف جهاز البصمة:', error)
      return { success: false, message: 'حدث خطأ في حذف جهاز البصمة' }
    }
  }

  // مزامنة جهاز البصمة
  public async syncFingerprintDevice(deviceId: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود الجهاز
      const device = this.db.prepare('SELECT * FROM fingerprint_devices WHERE id = ?').get(deviceId)
      if (!device) {
        return { success: false, message: 'لم يتم العثور على الجهاز' }
      }

      // تحديث وقت آخر مزامنة
      const result = this.db.prepare(`
        UPDATE fingerprint_devices SET
          last_sync = datetime('now'),
          updated_at = datetime('now')
        WHERE id = ?
      `).run(deviceId)

      if (result.changes > 0) {
        DatabaseService.getInstance().saveDatabase()

        // هنا يمكن إضافة منطق المزامنة الفعلي مع الجهاز
        // مثل جلب بيانات الحضور من الجهاز أو تحديث قائمة الموّفين

        return { success: true, message: 'تم تحديث المزامنة بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث المزامنة' }
      }
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في مزامنة جهاز البصمة:', error)
      return { success: false, message: 'حدث خطأ في مزامنة الجهاز' }
    }
  }

  // ===== إدارة الرواتب =====

  // جلب سجلات الرواتب
  public async getEmployeePayroll(filters?: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          pr.id,
          pr.employee_id,
          e.full_name as employee_name,
          e.employee_code,
          d.name as department_name,
          pr.month,
          pr.year,
          pr.basic_salary,
          pr.overtime_hours,
          pr.overtime_amount,
          pr.allowances,
          pr.deductions,
          pr.bonus,
          pr.commission,
          pr.gross_salary,
          pr.net_salary,
          pr.status,
          pr.notes,
          pr.created_at,
          pr.updated_at
        FROM payroll_records pr
        LEFT JOIN employees e ON pr.employee_id = e.id
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE 1=1
      `

      const params: any[] = []

      // تطبيق الفلاتر
      if (filters?.employee_id) {
        query += ` AND pr.employee_id = ?`
        params.push(filters.employee_id)
      }

      if (filters?.department_id) {
        query += ` AND e.department_id = ?`
        params.push(filters.department_id)
      }

      if (filters?.month) {
        query += ` AND pr.month = ?`
        params.push(filters.month)
      }

      if (filters?.year) {
        query += ` AND pr.year = ?`
        params.push(filters.year)
      }

      if (filters?.status) {
        query += ` AND pr.status = ?`
        params.push(filters.status)
      }

      query += ` ORDER BY pr.year DESC, pr.month DESC, e.full_name`

      const records = this.db.prepare(query).all(params)
      return records
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في جلب سجلات الرواتب:', error)
      return []
    }
  }

  // حساب الراتب
  public async calculatePayroll(payrollData: any): Promise<ApiResponse> {
    try {
      const { employee_id, month, year, overtime_hours = 0, allowances = 0, deductions = 0, bonus = 0, commission = 0 } = payrollData

      // الحصول على بيانات الموّف
      const employee = this.db.prepare(`
        SELECT * FROM employees WHERE id = ?
      `).get(employee_id) as any

      if (!employee) {
        return { success: false, message: 'الموّف غير موجود' }
      }

      // حساب الراتب الإجمالي
      const basicSalary = employee.basic_salary || 0
      const overtimeAmount = overtime_hours * (basicSalary / 160) // افتراض 160 ساعة عمل شهرياً
      const grossSalary = basicSalary + overtimeAmount + allowances + bonus + commission
      const netSalary = grossSalary - deductions

      // التحقق من وجود سجل راتب للشهر
      const existingRecord = this.db.prepare(`
        SELECT id FROM payroll_records
        WHERE employee_id = ? AND month = ? AND year = ?
      `).get(employee_id, month, year) as any

      if (existingRecord) {
        // تحديث السجل الموجود
        const result = this.db.prepare(`
          UPDATE payroll_records
          SET basic_salary = ?, overtime_hours = ?, overtime_amount = ?,
              allowances = ?, deductions = ?, bonus = ?, commission = ?,
              gross_salary = ?, net_salary = ?, status = 'calculated',
              updated_at = datetime('now')
          WHERE id = ?
        `).run([
          basicSalary, overtime_hours, overtimeAmount, allowances, deductions,
          bonus, commission, grossSalary, netSalary, existingRecord.id
        ])

        if (result.changes > 0) {
          // حفّ قاعدة البيانات
          DatabaseService.getInstance().saveDatabase()
          return { success: true, message: 'تم تحديث حساب الراتب بنجاح', id: existingRecord.id }
        }
      } else {
        // إنشاء سجل جديد
        const result = this.db.prepare(`
          INSERT INTO payroll_records (
            employee_id, month, year, basic_salary, overtime_hours, overtime_amount,
            allowances, deductions, bonus, commission, gross_salary, net_salary,
            status, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'calculated', datetime('now'))
        `).run([
          employee_id, month, year, basicSalary, overtime_hours, overtimeAmount,
          allowances, deductions, bonus, commission, grossSalary, netSalary
        ])

        if (result.changes > 0) {
          // حفّ قاعدة البيانات
          DatabaseService.getInstance().saveDatabase()
          return { success: true, message: 'تم حساب الراتب بنجاح', id: result.lastInsertRowid }
        }
      }

      return { success: false, message: 'فشل في حساب الراتب' }
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في حساب الراتب:', error)
      return { success: false, message: 'حدث خطأ في حساب الراتب' }
    }
  }

  // تحديث سجل راتب
  public async updatePayroll(payrollId: number, payrollData: any): Promise<ApiResponse> {
    try {
      const { overtime_hours = 0, allowances = 0, deductions = 0, bonus = 0, commission = 0, notes } = payrollData

      // الحصول على السجل الحالي
      const currentRecord = this.db.prepare(`
        SELECT pr.*, e.basic_salary
        FROM payroll_records pr
        LEFT JOIN employees e ON pr.employee_id = e.id
        WHERE pr.id = ?
      `).get(payrollId) as any

      if (!currentRecord) {
        return { success: false, message: 'سجل الراتب غير موجود' }
      }

      // إعادة حساب الراتب
      const basicSalary = currentRecord.basic_salary || 0
      const overtimeAmount = overtime_hours * (basicSalary / 160)
      const grossSalary = basicSalary + overtimeAmount + allowances + bonus + commission
      const netSalary = grossSalary - deductions

      const result = this.db.prepare(`
        UPDATE payroll_records
        SET overtime_hours = ?, overtime_amount = ?, allowances = ?,
            deductions = ?, bonus = ?, commission = ?, gross_salary = ?,
            net_salary = ?, notes = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run([
        overtime_hours, overtimeAmount, allowances, deductions,
        bonus, commission, grossSalary, netSalary, notes, payrollId
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث سجل الراتب بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث سجل الراتب' }
      }
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في تحديث سجل الراتب:', error)
      return { success: false, message: 'حدث خطأ في تحديث سجل الراتب' }
    }
  }

  // حذف سجل راتب
  public async deletePayroll(payrollId: number): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        DELETE FROM payroll_records WHERE id = ?
      `).run(payrollId)

      if (result.changes > 0) {
        return { success: true, message: 'تم حذف سجل الراتب بنجاح' }
      } else {
        return { success: false, message: 'سجل الراتب غير موجود' }
      }
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في حذف سجل الراتب:', error)
      return { success: false, message: 'حدث خطأ في حذف سجل الراتب' }
    }
  }

  // ===== إدارة الإجازات =====

  // جلب سجلات الإجازات
  public async getEmployeeLeaves(filters?: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          el.id,
          el.employee_id,
          e.full_name as employee_name,
          e.employee_code,
          d.name as department_name,
          el.leave_type,
          el.start_date,
          el.end_date,
          el.days_count,
          el.reason,
          el.status,
          el.approved_by,
          el.approved_at,
          el.notes,
          el.created_at,
          el.updated_at,
          approver.full_name as approved_by_name
        FROM employee_leaves el
        LEFT JOIN employees e ON el.employee_id = e.id
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN employees approver ON el.approved_by = approver.id
        WHERE 1=1
      `

      const params: any[] = []

      // تطبيق الفلاتر
      if (filters?.employee_id) {
        query += ` AND el.employee_id = ?`
        params.push(filters.employee_id)
      }

      if (filters?.department_id) {
        query += ` AND e.department_id = ?`
        params.push(filters.department_id)
      }

      if (filters?.leave_type) {
        query += ` AND el.leave_type = ?`
        params.push(filters.leave_type)
      }

      if (filters?.status) {
        query += ` AND el.status = ?`
        params.push(filters.status)
      }

      if (filters?.start_date) {
        query += ` AND el.start_date >= ?`
        params.push(filters.start_date)
      }

      if (filters?.end_date) {
        query += ` AND el.end_date <= ?`
        params.push(filters.end_date)
      }

      query += ` ORDER BY el.created_at DESC`

      const records = this.db.prepare(query).all(params)
      return records
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في جلب سجلات الإجازات:', error)
      return []
    }
  }

  // إنشاء طلب إجازة
  public async createLeaveRequest(leaveData: any): Promise<ApiResponse> {
    try {
      const { employee_id, leave_type, start_date, end_date, reason, notes } = leaveData

      // حساب عدد الأيام
      const startDate = new Date(start_date)
      const endDate = new Date(end_date)
      const timeDiff = endDate.getTime() - startDate.getTime()
      const daysCount = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1

      const result = this.db.prepare(`
        INSERT INTO employee_leaves (
          employee_id, leave_type, start_date, end_date, days_count,
          reason, notes, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', datetime('now'))
      `).run([employee_id, leave_type, start_date, end_date, daysCount, reason, notes])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إنشاء طلب الإجازة بنجاح', id: result.lastInsertRowid }
      } else {
        return { success: false, message: 'فشل في إنشاء طلب الإجازة' }
      }
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في إنشاء طلب الإجازة:', error)
      return { success: false, message: 'حدث خطأ في إنشاء طلب الإجازة' }
    }
  }

  // تحديث طلب إجازة
  public async updateLeaveRequest(leaveId: number, leaveData: any): Promise<ApiResponse> {
    try {
      const { leave_type, start_date, end_date, reason, notes } = leaveData

      // حساب عدد الأيام
      const startDate = new Date(start_date)
      const endDate = new Date(end_date)
      const timeDiff = endDate.getTime() - startDate.getTime()
      const daysCount = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1

      const result = this.db.prepare(`
        UPDATE employee_leaves
        SET leave_type = ?, start_date = ?, end_date = ?, days_count = ?,
            reason = ?, notes = ?, updated_at = datetime('now')
        WHERE id = ? AND status = 'pending'
      `).run([leave_type, start_date, end_date, daysCount, reason, notes, leaveId])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث طلب الإجازة بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث طلب الإجازة أو أن الطلب غير قابل للتعديل' }
      }
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في تحديث طلب الإجازة:', error)
      return { success: false, message: 'حدث خطأ في تحديث طلب الإجازة' }
    }
  }

  // الموافقة على طلب إجازة
  public async approveLeaveRequest(leaveId: number, approvalData: any): Promise<ApiResponse> {
    try {
      const { approved_by, status, notes } = approvalData

      const result = this.db.prepare(`
        UPDATE employee_leaves
        SET status = ?, approved_by = ?, approved_at = datetime('now'),
            notes = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run([status, approved_by, notes, leaveId])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        const statusText = status === 'approved' ? 'الموافقة على' : 'رفض'
        return { success: true, message: `تم ${statusText} طلب الإجازة بنجاح` }
      } else {
        return { success: false, message: 'طلب الإجازة غير موجود' }
      }
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في الموافقة على طلب الإجازة:', error)
      return { success: false, message: 'حدث خطأ في معالجة طلب الإجازة' }
    }
  }

  // حذف طلب إجازة
  public async deleteLeaveRequest(leaveId: number): Promise<ApiResponse> {
    try {
      const result = this.db.prepare(`
        DELETE FROM employee_leaves WHERE id = ? AND status = 'pending'
      `).run(leaveId)

      if (result.changes > 0) {
        return { success: true, message: 'تم حذف طلب الإجازة بنجاح' }
      } else {
        return { success: false, message: 'طلب الإجازة غير موجود أو غير قابل للحذف' }
      }
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في حذف طلب الإجازة:', error)
      return { success: false, message: 'حدث خطأ في حذف طلب الإجازة' }
    }
  }

  // ===== تقارير الموّفين =====

  // تقرير الأداء للموّفين
  public async getEmployeePerformance(filters?: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          e.id,
          e.employee_code,
          e.full_name as employee_name,
          e.position,
          d.name as department_name,
          -- حساب إحصائيات الحضور
          COUNT(ar.id) as total_attendance_days,
          SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) as present_days,
          SUM(CASE WHEN ar.status = 'absent' THEN 1 ELSE 0 END) as absent_days,
          SUM(CASE WHEN ar.status = 'late' THEN 1 ELSE 0 END) as late_days,
          AVG(ar.hours_worked) as avg_hours_worked,
          -- حساب إحصائيات الإجازات
          (SELECT COUNT(*) FROM employee_leaves el WHERE el.employee_id = e.id AND el.status = 'approved') as total_leaves,
          -- حساب إحصائيات الرواتب
          (SELECT AVG(net_salary) FROM payroll_records pr WHERE pr.employee_id = e.id) as avg_salary,
          e.hire_date,
          e.status
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN attendance_records ar ON e.id = ar.employee_id
        WHERE e.is_active = 1
      `

      const params: any[] = []

      // تطبيق الفلاتر
      if (filters?.employee_id) {
        query += ` AND e.id = ?`
        params.push(filters.employee_id)
      }

      if (filters?.department_id) {
        query += ` AND e.department_id = ?`
        params.push(filters.department_id)
      }

      if (filters?.date_from) {
        query += ` AND ar.date >= ?`
        params.push(filters.date_from)
      }

      if (filters?.date_to) {
        query += ` AND ar.date <= ?`
        params.push(filters.date_to)
      }

      query += ` GROUP BY e.id ORDER BY e.full_name`

      const records = this.db.prepare(query).all(params)
      return records
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في تقرير أداء الموّفين:', error)
      return []
    }
  }

  // تقرير الساعات الإضافية للموّفين
  public async getEmployeeOvertime(filters?: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          e.id,
          e.employee_code,
          e.full_name as employee_name,
          d.name as department_name,
          pr.month,
          pr.year,
          pr.overtime_hours,
          pr.overtime_amount,
          e.overtime_rate,
          pr.created_at
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN payroll_records pr ON e.id = pr.employee_id
        WHERE e.is_active = 1 AND pr.overtime_hours > 0
      `

      const params: any[] = []

      // تطبيق الفلاتر
      if (filters?.employee_id) {
        query += ` AND e.id = ?`
        params.push(filters.employee_id)
      }

      if (filters?.department_id) {
        query += ` AND e.department_id = ?`
        params.push(filters.department_id)
      }

      if (filters?.month) {
        query += ` AND pr.month = ?`
        params.push(filters.month)
      }

      if (filters?.year) {
        query += ` AND pr.year = ?`
        params.push(filters.year)
      }

      query += ` ORDER BY pr.year DESC, pr.month DESC, e.full_name`

      const records = this.db.prepare(query).all(params)
      return records
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في تقرير الساعات الإضافية:', error)
      return []
    }
  }

  // تقرير تحليل الموظفين المتقدم
  public async getEmployeeAnalysis(filters?: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          e.id,
          e.employee_code,
          e.full_name as employee_name,
          e.position,
          e.job_title,
          d.name as department_name,
          e.hire_date,
          e.basic_salary,
          e.employment_type,
          e.salary_type,
          -- إحصائيات الحضور
          COALESCE(attendance_stats.total_days, 0) as total_attendance_days,
          COALESCE(attendance_stats.present_days, 0) as present_days,
          COALESCE(attendance_stats.absent_days, 0) as absent_days,
          COALESCE(attendance_stats.late_days, 0) as late_days,
          COALESCE(attendance_stats.attendance_rate, 0) as attendance_rate,
          -- إحصائيات الرواتب
          COALESCE(payroll_stats.avg_salary, 0) as avg_net_salary,
          COALESCE(payroll_stats.total_overtime_hours, 0) as total_overtime_hours,
          COALESCE(payroll_stats.total_overtime_amount, 0) as total_overtime_amount,
          -- إحصائيات الإجازات
          COALESCE(leave_stats.total_leaves, 0) as total_leave_days,
          COALESCE(leave_stats.annual_leaves, 0) as annual_leaves,
          COALESCE(leave_stats.sick_leaves, 0) as sick_leaves,
          -- نقاط الأداء
          CASE
            WHEN attendance_stats.attendance_rate >= 95 THEN 100
            WHEN attendance_stats.attendance_rate >= 90 THEN 85
            WHEN attendance_stats.attendance_rate >= 80 THEN 70
            WHEN attendance_stats.attendance_rate >= 70 THEN 55
            ELSE 40
          END as performance_score
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN (
          SELECT
            employee_id,
            COUNT(*) as total_days,
            SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_days,
            SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_days,
            SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_days,
            ROUND(AVG(CASE WHEN status = 'present' THEN 100.0 ELSE 0 END), 2) as attendance_rate
          FROM attendance_records
          GROUP BY employee_id
        ) attendance_stats ON e.id = attendance_stats.employee_id
        LEFT JOIN (
          SELECT
            employee_id,
            AVG(net_salary) as avg_salary,
            SUM(overtime_hours) as total_overtime_hours,
            SUM(overtime_amount) as total_overtime_amount
          FROM payroll_records
          GROUP BY employee_id
        ) payroll_stats ON e.id = payroll_stats.employee_id
        LEFT JOIN (
          SELECT
            employee_id,
            SUM(days_count) as total_leaves,
            SUM(CASE WHEN leave_type = 'annual' THEN days_count ELSE 0 END) as annual_leaves,
            SUM(CASE WHEN leave_type = 'sick' THEN days_count ELSE 0 END) as sick_leaves
          FROM employee_leaves
          WHERE status = 'approved'
          GROUP BY employee_id
        ) leave_stats ON e.id = leave_stats.employee_id
        WHERE e.is_active = 1
      `

      const params: any[] = []

      // تطبيق الفلاتر
      if (filters?.employee_id) {
        query += ` AND e.id = ?`
        params.push(filters.employee_id)
      }

      if (filters?.department_id) {
        query += ` AND e.department_id = ?`
        params.push(filters.department_id)
      }

      if (filters?.employment_type) {
        query += ` AND e.employment_type = ?`
        params.push(filters.employment_type)
      }

      query += ` ORDER BY performance_score DESC, e.full_name`

      const records = this.db.prepare(query).all(params)
      return records
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في تقرير تحليل الموظفين:', error)
      return []
    }
  }

  // تقرير مقارنة الرواتب
  public async getSalaryComparison(filters?: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          d.id as department_id,
          d.name as department_name,
          COUNT(e.id) as employee_count,
          AVG(e.basic_salary) as avg_basic_salary,
          MIN(e.basic_salary) as min_salary,
          MAX(e.basic_salary) as max_salary,
          AVG(payroll_stats.avg_net_salary) as avg_net_salary,
          SUM(payroll_stats.total_paid) as total_department_cost,
          -- تحليل توزيع الرواتب
          COUNT(CASE WHEN e.basic_salary < 3000 THEN 1 END) as low_salary_count,
          COUNT(CASE WHEN e.basic_salary BETWEEN 3000 AND 5000 THEN 1 END) as mid_salary_count,
          COUNT(CASE WHEN e.basic_salary > 5000 THEN 1 END) as high_salary_count
        FROM departments d
        LEFT JOIN employees e ON d.id = e.department_id AND e.is_active = 1
        LEFT JOIN (
          SELECT
            employee_id,
            AVG(net_salary) as avg_net_salary,
            SUM(net_salary) as total_paid
          FROM payroll_records
          GROUP BY employee_id
        ) payroll_stats ON e.id = payroll_stats.employee_id
        WHERE d.id IS NOT NULL
      `

      const params: any[] = []

      if (filters?.department_id) {
        query += ` AND d.id = ?`
        params.push(filters.department_id)
      }

      query += ` GROUP BY d.id, d.name ORDER BY avg_basic_salary DESC`

      const records = this.db.prepare(query).all(params)
      return records
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في تقرير مقارنة الرواتب:', error)
      return []
    }
  }

  // تقرير تقييم الكفاءة
  public async getEfficiencyEvaluation(filters?: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          e.id,
          e.employee_code,
          e.full_name as employee_name,
          e.position,
          d.name as department_name,
          e.hire_date,
          -- مؤشرات الكفاءة
          COALESCE(attendance_stats.attendance_rate, 0) as attendance_efficiency,
          COALESCE(productivity_stats.productivity_score, 0) as productivity_score,
          COALESCE(overtime_stats.overtime_efficiency, 0) as overtime_efficiency,
          COALESCE(leave_stats.leave_efficiency, 0) as leave_efficiency,
          -- النقاط الإجمالية
          ROUND((
            COALESCE(attendance_stats.attendance_rate, 0) * 0.3 +
            COALESCE(productivity_stats.productivity_score, 0) * 0.4 +
            COALESCE(overtime_stats.overtime_efficiency, 0) * 0.2 +
            COALESCE(leave_stats.leave_efficiency, 0) * 0.1
          ), 2) as efficiency_score,
          -- تصنيف الكفاءة
          CASE
            WHEN ROUND((
              COALESCE(attendance_stats.attendance_rate, 0) * 0.3 +
              COALESCE(productivity_stats.productivity_score, 0) * 0.4 +
              COALESCE(overtime_stats.overtime_efficiency, 0) * 0.2 +
              COALESCE(leave_stats.leave_efficiency, 0) * 0.1
            ), 2) >= 90 THEN 'ممتاز'
            WHEN ROUND((
              COALESCE(attendance_stats.attendance_rate, 0) * 0.3 +
              COALESCE(productivity_stats.productivity_score, 0) * 0.4 +
              COALESCE(overtime_stats.overtime_efficiency, 0) * 0.2 +
              COALESCE(leave_stats.leave_efficiency, 0) * 0.1
            ), 2) >= 80 THEN 'جيد جداً'
            WHEN ROUND((
              COALESCE(attendance_stats.attendance_rate, 0) * 0.3 +
              COALESCE(productivity_stats.productivity_score, 0) * 0.4 +
              COALESCE(overtime_stats.overtime_efficiency, 0) * 0.2 +
              COALESCE(leave_stats.leave_efficiency, 0) * 0.1
            ), 2) >= 70 THEN 'جيد'
            WHEN ROUND((
              COALESCE(attendance_stats.attendance_rate, 0) * 0.3 +
              COALESCE(productivity_stats.productivity_score, 0) * 0.4 +
              COALESCE(overtime_stats.overtime_efficiency, 0) * 0.2 +
              COALESCE(leave_stats.leave_efficiency, 0) * 0.1
            ), 2) >= 60 THEN 'مقبول'
            ELSE 'يحتاج تحسين'
          END as efficiency_rating
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN (
          SELECT
            employee_id,
            ROUND(AVG(CASE WHEN status = 'present' THEN 100.0 ELSE 0 END), 2) as attendance_rate
          FROM attendance_records
          GROUP BY employee_id
        ) attendance_stats ON e.id = attendance_stats.employee_id
        LEFT JOIN (
          SELECT
            employee_id,
            CASE
              WHEN AVG(hours_worked) >= 8 THEN 100
              WHEN AVG(hours_worked) >= 7 THEN 85
              WHEN AVG(hours_worked) >= 6 THEN 70
              ELSE 50
            END as productivity_score
          FROM attendance_records
          WHERE hours_worked > 0
          GROUP BY employee_id
        ) productivity_stats ON e.id = productivity_stats.employee_id
        LEFT JOIN (
          SELECT
            employee_id,
            CASE
              WHEN AVG(overtime_hours) <= 10 THEN 100
              WHEN AVG(overtime_hours) <= 20 THEN 80
              WHEN AVG(overtime_hours) <= 30 THEN 60
              ELSE 40
            END as overtime_efficiency
          FROM payroll_records
          GROUP BY employee_id
        ) overtime_stats ON e.id = overtime_stats.employee_id
        LEFT JOIN (
          SELECT
            employee_id,
            CASE
              WHEN SUM(days_count) <= 15 THEN 100
              WHEN SUM(days_count) <= 25 THEN 80
              WHEN SUM(days_count) <= 35 THEN 60
              ELSE 40
            END as leave_efficiency
          FROM employee_leaves
          WHERE status = 'approved'
          GROUP BY employee_id
        ) leave_stats ON e.id = leave_stats.employee_id
        WHERE e.is_active = 1
      `

      const params: any[] = []

      // تطبيق الفلاتر
      if (filters?.employee_id) {
        query += ` AND e.id = ?`
        params.push(filters.employee_id)
      }

      if (filters?.department_id) {
        query += ` AND e.department_id = ?`
        params.push(filters.department_id)
      }

      query += ` ORDER BY efficiency_score DESC, e.full_name`

      const records = this.db.prepare(query).all(params)
      return records
    } catch (error) {
      Logger.error('EmployeeService', 'خطأ في تقرير تقييم الكفاءة:', error)
      return []
    }
  }
}
