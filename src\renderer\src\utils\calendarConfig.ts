/**
 * تكوين التقويم الميلادي
 * هذا الملف يضمن استخدام التقويم الميلادي فقط في جميع أنحاء التطبيق
 * ولا يدعم أي تقويم آخر مثل الهجري أو الصيني أو العبري
 */

import arEG from 'antd/locale/ar_EG';
import dayjs from 'dayjs';
import { logger as Logger }  from './logger'

// تكوين Ant Design للتقويم الميلادي فقط
export const antdLocaleConfig = {
  ...arEG,
  DatePicker: {
    ...(arEG.DatePicker || {}),
    lang: {
      ...(arEG.DatePicker?.lang || {}),
      // تأكيد استخدام التقويم الميلادي
      calendar: 'gregorian',
      // منع استخدام أي تقويم آخر
      hijri: undefined,
      islamic: undefined,
      hebrew: undefined,
      chinese: undefined,
      persian: undefined,
      // تحديد أسماء الأشهر الميلادية بالعربية
      monthNames: [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ],
      // تحديد أسماء الأيام بالعربية
      dayNames: [
        'الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'
      ],
      // تحديد أسماء الأيام المختصرة
      dayNamesShort: [
        'أحد', 'إثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'
      ]
    }
  },
  Calendar: {
    ...(arEG.Calendar || {}),
    lang: {
      ...(arEG.Calendar?.lang || {}),
      // تأكيد استخدام التقويم الميلادي
      calendar: 'gregorian',
      // منع استخدام أي تقويم آخر
      hijri: undefined,
      islamic: undefined,
      hebrew: undefined,
      chinese: undefined,
      persian: undefined
    }
  }
};

// دالة للتحقق من أن التاريخ ميلادي
export const validateGregorianDate = (date: any): boolean => {
  if (!date) return false;
  
  // التحقق من أن التاريخ صالح
  const dayjsDate = dayjs(date);
  if (!dayjsDate.isValid()) return false;
  
  // التحقق من أن السنة في النطاق الميلادي المعقول
  const year = dayjsDate.year();
  if (year < 1900 || year > 2100) return false;
  
  // التحقق من أن الشهر في النطاق الصحيح (1-12)
  const month = dayjsDate.month() + 1; // dayjs يبدأ من 0
  if (month < 1 || month > 12) return false;
  
  // التحقق من أن اليوم في النطاق الصحيح
  const day = dayjsDate.date();
  if (day < 1 || day > 31) return false;
  
  return true;
};

// دالة لتحويل أي تاريخ إلى التقويم الميلادي
export const ensureGregorianDate = (date: any): dayjs.Dayjs => {
  if (!date) return dayjs();
  
  const dayjsDate = dayjs(date);
  
  // إذا كان التاريخ غير صالح، إرجاع التاريخ الحالي
  if (!dayjsDate.isValid()) {
    Logger.warn('CalendarConfig', 'تاريخ غير صالح، سيتم استخدام التاريخ الحالي:', date);
    return dayjs();
  }
  
  // التأكد من أن التاريخ في النطاق الميلادي المعقول
  if (!validateGregorianDate(dayjsDate)) {
    Logger.warn('CalendarConfig', 'تاريخ خارج النطاق الميلادي المعقول، سيتم استخدام التاريخ الحالي:', date);
    return dayjs();
  }
  
  return dayjsDate;
};

// دالة لمنع استخدام التقويم الهجري أو أي تقويم آخر
export const preventNonGregorianCalendar = () => {
  // تحذير في وحدة التحكم إذا تم محاولة استخدام تقويم غير ميلادي
  const originalConsoleWarn = console.warn;
  console.warn = (...args: any[]) => {
    const message = args.join(' ');
    if (message.includes('hijri') || 
        message.includes('islamic') || 
        message.includes('hebrew') || 
        message.includes('chinese') || 
        message.includes('persian')) {
      originalConsoleWarn('⚠️ تحذير: هذا التطبيق يدعم التقويم الميلادي فقط!');
      return;
    }
    originalConsoleWarn(...args);
  };
};

// تطبيق التكوين
export const applyGregorianCalendarConfig = () => {
  // منع استخدام التقاويم الأخرى
  preventNonGregorianCalendar();
  
  // تعيين اللغة العربية مع التقويم الميلادي
  dayjs.locale('ar');
  
  // تسجيل رسالة تأكيد
  Logger.info('CalendarConfig', '✅ تم تطبيق تكوين التقويم الميلادي بنجاح');
  Logger.info('CalendarConfig', '📅 النّام يدعم التقويم الميلادي فقط');
  Logger.info('CalendarConfig', '🚫 لا يدعم النّام التقويم الهجري أو أي تقويم آخر');
};

// تصدير التكوين الافتراضي
export default {
  antdLocaleConfig,
  validateGregorianDate,
  ensureGregorianDate,
  preventNonGregorianCalendar,
  applyGregorianCalendarConfig
};
