import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react'
import {
  Table,
  Button,
  Space,
  Card,
  Input,
  Select,
  Tooltip,
  Dropdown,
  Checkbox,
  Typography,
  Spin,
  Empty,
  ConfigProvider
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  SettingOutlined,
  SearchOutlined,
  FilterOutlined,
  ColumnWidthOutlined,
  ReloadOutlined,
  DownloadOutlined,
  PrinterOutlined,
  FullscreenOutlined,
  CompressOutlined
} from '@ant-design/icons'
import type { TableProps, ColumnType } from 'antd/es/table'
import type { TableRef } from 'antd/es/table'
import styled from 'styled-components'

const { Search } = Input
const { Option } = Select
const { Text } = Typography

// أنواع البيانات المحسنة
interface EnhancedColumnType<T = any> extends ColumnType<T> {
  searchable?: boolean
  filterable?: boolean
  resizable?: boolean
  sortable?: boolean
  exportable?: boolean
  printable?: boolean
  width?: number | string
  minWidth?: number
  maxWidth?: number
  type?: 'text' | 'number' | 'date' | 'currency'
}

interface EnhancedTableProps<T = any> extends Omit<TableProps<T>, 'columns' | 'title'> {
  columns: EnhancedColumnType<T>[]
  data: T[]
  title?: string
  subtitle?: string
  loading?: boolean
  searchable?: boolean
  _filterable?: boolean
  exportable?: boolean
  printable?: boolean
  resizable?: boolean
  virtualScroll?: boolean
  maxHeight?: number
  onExport?: (data: T[]) => void
  onPrint?: () => void
  onRefresh?: () => void
  customActions?: React.ReactNode[]
  emptyText?: string
  showSummary?: boolean
  summaryData?: Record<string, any>
  // ميزات جديدة للتحكم بالمظهر
  tableTheme?: 'default' | 'compact' | 'comfortable' | 'spacious'
  showTableControls?: boolean
  allowColumnReorder?: boolean
  showRowNumbers?: boolean
  stickyHeader?: boolean
}

// أنماط الجداول المختلفة
const getTableThemeStyles = (theme: string) => {
  const themes = {
    default: {
      cellPadding: '8px 12px',
      fontSize: '13px',
      rowHeight: 'auto'
    },
    compact: {
      cellPadding: '4px 8px',
      fontSize: '12px',
      rowHeight: '32px'
    },
    comfortable: {
      cellPadding: '12px 16px',
      fontSize: '14px',
      rowHeight: 'auto'
    },
    spacious: {
      cellPadding: '16px 20px',
      fontSize: '15px',
      rowHeight: 'auto'
    }
  }
  return themes[theme as keyof typeof themes] || themes.default
}

// الأنماط المحسنة
const StyledCard = styled(Card)<{ $tableTheme?: string }>`
  .ant-card-head {
    border-bottom: 2px solid #f0f0f0;
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  }

  .ant-card-head-title {
    font-weight: 600;
    color: #262626;
  }

  .ant-table-wrapper {
    .ant-table {
      font-size: ${props => getTableThemeStyles(props.$tableTheme || 'default').fontSize};
    }

    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      color: #262626;
      border-bottom: 2px solid #e8e8e8;
      position: relative;
      padding: ${props => getTableThemeStyles(props.$tableTheme || 'default').cellPadding};

      &:hover {
        background: #f0f0f0;
      }
    }

    .ant-table-tbody > tr {
      transition: all 0.2s ease;
      height: ${props => getTableThemeStyles(props.$tableTheme || 'default').rowHeight};

      &:hover {
        background: #f8f9fa;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }

      &:nth-child(even) {
        background: #fafafa;

        &:hover {
          background: #f0f2f5;
        }
      }
    }

    .ant-table-cell {
      padding: ${props => getTableThemeStyles(props.$tableTheme || 'default').cellPadding};
      border-bottom: 1px solid #f0f0f0;
    }

    /* أنماط الرؤوس الثابتة */
    &.sticky-header .ant-table-thead > tr > th {
      position: sticky;
      top: 0;
      z-index: 10;
      background: #fafafa;
    }

    /* أنماط أرقام الصفوف */
    .row-number-column {
      width: 50px;
      text-align: center;
      background: #f8f9fa;
      font-weight: 600;
      color: #666;
    }
  }
  
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
  }
  
  .table-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }
  
  .table-search {
    min-width: 250px;
  }
  
  .table-summary {
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 6px;
    margin-top: 16px;
    border: 1px solid #e8e8e8;
  }
  
  .summary-item {
    display: inline-block;
    margin-left: 24px;
    
    .summary-label {
      color: #666;
      font-size: 12px;
      margin-left: 8px;
    }
    
    .summary-value {
      font-weight: 600;
      color: #1890ff;
      font-size: 14px;
    }
  }
  
  @media (max-width: 768px) {
    .table-toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }
    
    .table-actions {
      justify-content: center;
      flex-wrap: wrap;
    }
    
    .table-search {
      min-width: auto;
    }
  }
`

const ResizeHandle = styled.div`
  position: absolute;
  top: 0;
  left: -2px;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  z-index: 1;
  
  &:hover {
    background: #1890ff;
  }
  
  &:active {
    background: #096dd9;
  }
`

// مكون الجدول المحسن
const EnhancedTable = <T extends Record<string, any>>({
  columns,
  data,
  title,
  subtitle,
  loading = false,
  searchable = true,
  _filterable = true,
  exportable = true,
  printable = true,
  resizable = true,
  virtualScroll = false,
  maxHeight = 600,
  onExport,
  onPrint,
  onRefresh,
  customActions = [],
  emptyText = 'لا توجد بيانات',
  showSummary = false,
  summaryData = {},
  // الخصائص الجديدة
  tableTheme = 'default',
  showTableControls = true,
  allowColumnReorder: _allowColumnReorder = false,
  showRowNumbers = false,
  stickyHeader = false,
  ...tableProps
}: EnhancedTableProps<T>) => {
  const [searchText, setSearchText] = useState('')
  const [filteredData, setFilteredData] = useState<T[]>(data)
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({})
  const [isFullscreen, setIsFullscreen] = useState(false)
  const tableRef = useRef<TableRef>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // تصفية البيانات بناءً على البحث
  const handleSearch = useCallback((value: string) => {
    setSearchText(value)
    if (!value.trim()) {
      setFilteredData(data)
      return
    }

    const filtered = data.filter(item =>
      Object.values(item).some(val =>
        String(val).toLowerCase().includes(value.toLowerCase())
      )
    )
    setFilteredData(filtered)
  }, [data])

  // تحديث البيانات المفلترة عند تغيير البيانات الأصلية
  useEffect(() => {
    if (!searchText.trim()) {
      setFilteredData(data)
    } else {
      handleSearch(searchText)
    }
  }, [data, searchText, handleSearch])

  // إضافة عمود أرقام الصفوف إذا كان مطلوباً
  const enhancedColumns = useMemo(() => {
    let processedColumns = [...columns]

    // إضافة عمود أرقام الصفوف
    if (showRowNumbers) {
      const rowNumberColumn: EnhancedColumnType<T> = {
        key: 'rowNumber',
        title: '#',
        dataIndex: 'rowNumber',
        width: 50,
        align: 'center',
        className: 'row-number-column',
        render: (_: any, __: T, index: number) => index + 1
      }
      processedColumns = [rowNumberColumn, ...processedColumns]
    }

    return processedColumns
  }, [columns, showRowNumbers])

  // إعداد الأعمدة مع الميزات المحسنة
  const processedColumns = useMemo(() => {
    return enhancedColumns.map((col, index) => {
      const enhancedCol: ColumnType<T> = {
        ...col,
        width: columnWidths[col.key as string] || col.width,
        onHeaderCell: resizable ? (_column) => ({
          width: columnWidths[col.key as string] || col.width,
          onResize: (e: any, { size }: any) => {
            setColumnWidths(prev => ({
              ...prev,
              [col.key as string]: size.width
            }))
          }
        } as any) : undefined
      }

      // إضافة البحث للأعمدة القابلة للبحث
      if (col.searchable && col.dataIndex) {
        enhancedCol.filterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
          <div style={{ padding: 8 }}>
            <Input
              placeholder={`البحث في ${col.title}`}
              value={selectedKeys[0]}
              onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
              onPressEnter={() => confirm()}
              style={{ marginBottom: 8, display: 'block' }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => confirm()}
                icon={<SearchOutlined />}
                size="small"
              >
                بحث
              </Button>
              <Button onClick={() => clearFilters?.()} size="small">
                إعادة تعيين
              </Button>
            </Space>
          </div>
        )
        enhancedCol.filterIcon = (filtered: boolean) => (
          <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
        )
      }

      return enhancedCol
    })
  }, [enhancedColumns, columnWidths, resizable])

  // إعداد خصائص الجدول
  const tableConfig: TableProps<T> = {
    ...tableProps,
    columns: processedColumns,
    dataSource: filteredData,
    loading,
    rowKey: tableProps.rowKey || 'id',
    scroll: {
      x: 'max-content',
      y: virtualScroll ? maxHeight : undefined,
      ...tableProps.scroll
    },
    pagination: {
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) =>
        `${range[0]}-${range[1]} من ${total} عنصر`,
      pageSize: 20,
      ...tableProps.pagination
    },
    size: 'small',
    bordered: true,
    locale: {
      emptyText: <Empty description={emptyText} />
    }
  }

  // معالجة التصدير
  const handleExport = useCallback(() => {
    if (onExport) {
      onExport(filteredData)
    }
  }, [filteredData, onExport])

  // معالجة وضع ملء الشاشة
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen)
  }, [isFullscreen])

  return (
    <ConfigProvider direction="rtl">
      <StyledCard
        ref={containerRef}
        $tableTheme={tableTheme}
        title={
          <Space>
            <Text strong style={{ fontSize: 16 }}>
              {title}
            </Text>
            {subtitle && (
              <Text type="secondary" style={{ fontSize: 12 }}>
                {subtitle}
              </Text>
            )}
          </Space>
        }
        extra={
          <Space>
            {customActions}
            {onRefresh && (
              <Tooltip title="تحديث البيانات">
                <Button
                  icon={<ReloadOutlined />}
                  onClick={onRefresh}
                  loading={loading}
                />
              </Tooltip>
            )}
            {exportable && (
              <Tooltip title="تصدير البيانات">
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExport}
                  disabled={filteredData.length === 0}
                />
              </Tooltip>
            )}
            {printable && onPrint && (
              <Tooltip title="طباعة">
                <Button
                  icon={<PrinterOutlined />}
                  onClick={onPrint}
                  disabled={filteredData.length === 0}
                />
              </Tooltip>
            )}
            <Tooltip title={isFullscreen ? "تصغير" : "ملء الشاشة"}>
              <Button
                icon={isFullscreen ? <CompressOutlined /> : <FullscreenOutlined />}
                onClick={toggleFullscreen}
              />
            </Tooltip>
          </Space>
        }
        style={{
          position: isFullscreen ? 'fixed' : 'relative',
          top: isFullscreen ? 0 : 'auto',
          left: isFullscreen ? 0 : 'auto',
          right: isFullscreen ? 0 : 'auto',
          bottom: isFullscreen ? 0 : 'auto',
          zIndex: isFullscreen ? 1000 : 'auto',
          width: isFullscreen ? '100vw' : 'auto',
          height: isFullscreen ? '100vh' : 'auto',
          overflow: isFullscreen ? 'auto' : 'visible'
        }}
      >
        {/* شريط التحكم بالثيم */}
        {showTableControls && (
          <div className="table-controls" style={{
            padding: '12px 16px',
            borderBottom: '1px solid #f0f0f0',
            background: '#fafafa'
          }}>
            <Space>
              <Text strong style={{ fontSize: 12 }}>مظهر الجدول:</Text>
              <Select
                size="small"
                value={tableTheme}
                style={{ width: 120 }}
                onChange={(value) => {
                  // يمكن إضافة callback هنا لتحديث الثيم
                  Logger.info('EnhancedTable', 'تغيير ثيم الجدول:', value)
                }}
              >
                <Option value="default">افتراضي</Option>
                <Option value="compact">مضغوط</Option>
                <Option value="comfortable">مريح</Option>
                <Option value="spacious">واسع</Option>
              </Select>

              <Checkbox
                checked={showRowNumbers}
                onChange={(e) => {
                  // يمكن إضافة callback هنا
                  Logger.info('EnhancedTable', 'إظهار أرقام الصفوف:', e.target.checked)
                }}
              >
                أرقام الصفوف
              </Checkbox>

              <Checkbox
                checked={stickyHeader}
                onChange={(e) => {
                  // يمكن إضافة callback هنا
                  Logger.info('EnhancedTable', 'رأس ثابت:', e.target.checked)
                }}
              >
                رأس ثابت
              </Checkbox>
            </Space>
          </div>
        )}

        {/* شريط الأدوات */}
        <div className="table-toolbar">
          <div className="table-actions">
            {searchable && (
              <Search
                className="table-search"
                placeholder="البحث في جميع الحقول..."
                value={searchText}
                onChange={e => handleSearch(e.target.value)}
                allowClear
                enterButton={<SearchOutlined />}
              />
            )}
          </div>

          <Space>
            <Text type="secondary" style={{ fontSize: 12 }}>
              إجمالي: {filteredData.length} عنصر
            </Text>
          </Space>
        </div>

        {/* الجدول */}
        <div className={stickyHeader ? 'sticky-header' : ''}>
          <Table<T>
            ref={tableRef}
            {...tableConfig}
          />
        </div>

        {/* ملخص البيانات */}
        {showSummary && Object.keys(summaryData).length > 0 && (
          <div className="table-summary">
            <Text strong style={{ marginLeft: 16 }}>ملخص:</Text>
            {Object.entries(summaryData).map(([key, value]) => (
              <span key={key} className="summary-item">
                <span className="summary-value">
                  {typeof value === 'number' ? value.toLocaleString() : value}
                </span>
                <span className="summary-label">{key}</span>
              </span>
            ))}
          </div>
        )}
      </StyledCard>
    </ConfigProvider>
  )
}

export default EnhancedTable
export type { EnhancedTableProps, EnhancedColumnType }
