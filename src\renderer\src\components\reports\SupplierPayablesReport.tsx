import React, { useState } from 'react';
import { Tag, Typography, Progress, Tabs, Card, Space } from 'antd';
import {
  DollarOutlined,
  ClockCircleOutlined,
  BarChartOutlined,
  FileTextOutlined,
  AlertOutlined
} from '@ant-design/icons';
import UniversalReport from './UniversalReport';
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger';
import type { ReportData, ReportType } from '../../types/reports';
import { createDefaultFilters } from '../../types/reports';
import ReportColumnManager from './ReportColumnManager';
import ReportTemplateSelector from './ReportTemplateSelector';
import { ColumnConfig } from '../common/SimpleTableManager';
import { useReportTemplates, ReportTemplate } from '../../hooks/useReportTemplates';

const { Text } = Typography;

const SupplierPayablesReport: React.FC = () => {
  const [activeTab, setActiveTab] = useState('summary');
  const [managedColumns, setManagedColumns] = useState<ColumnConfig[]>([]);
  const [_selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const { getTemplateSettings } = useReportTemplates();

  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير مستحقات الموردين...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getSupplierPayablesReport(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const supplierPayablesData = response.data;

      // معالجة البيانات
      const processedData = supplierPayablesData.map((item: any, index: number) => ({
        ...item,
        key: item.supplier_id || index,
        outstanding_balance: item.total_invoices_amount - item.total_paid_amount,
        credit_usage_percentage: item.credit_limit > 0 ? ((item.total_invoices_amount - item.total_paid_amount) / item.credit_limit) * 100 : 0,
        payment_status: item.overdue_days > 0 ? 'overdue' : 'current'
      }));

      // حساب الإحصائيات
      const totalSuppliers = processedData.length;
      const totalInvoicesAmount = processedData.reduce((sum: number, item: any) => sum + item.total_invoices_amount, 0);
      const totalPaidAmount = processedData.reduce((sum: number, item: any) => sum + item.total_paid_amount, 0);
      const totalOutstanding = totalInvoicesAmount - totalPaidAmount;
      const overdueSuppliers = processedData.filter(item => item.overdue_days > 0).length;

      const summary = {
        totalSuppliers,
        totalInvoicesAmount: Math.round(totalInvoicesAmount * 100) / 100,
        totalPaidAmount: Math.round(totalPaidAmount * 100) / 100,
        totalOutstanding: Math.round(totalOutstanding * 100) / 100,
        overdueSuppliers,
        paymentRate: totalInvoicesAmount > 0 ? Math.round((totalPaidAmount / totalInvoicesAmount) * 100 * 100) / 100 : 0
      };

      console.log('✅ تم إنشاء تقرير مستحقات الموردين بنجاح');

      return {
        title: 'تقرير مستحقات الموردين',
        data: processedData,
        columns,
        summary,
        metadata: {
          reportType: 'supplier_payables' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: processedData.length
        }
      };
    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير مستحقات الموردين:', error);
      throw error;
    }
  };

  // دالة إنشاء تقرير المتأخرات
  const generateOverdueReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير المتأخرات...', filters);

      const response = await window.electronAPI.getSupplierPayablesReport({
        ...filters,
        overdueOnly: true
      });

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const overdueData = response.data.filter((item: any) => item.overdue_days > 0);

      const processedData = overdueData.map((item: any, index: number) => ({
        ...item,
        key: item.supplier_id || index,
        outstanding_balance: item.total_invoices_amount - item.total_paid_amount,
        risk_level: item.overdue_days > 60 ? 'high' : item.overdue_days > 30 ? 'medium' : 'low'
      }));

      const totalOverdue = processedData.reduce((sum: number, item: any) => sum + item.outstanding_balance, 0);

      return {
        title: 'تقرير المتأخرات',
        data: processedData,
        columns: overdueColumns,
        summary: {
          totalSuppliers: processedData.length,
          totalOverdue: Math.round(totalOverdue * 100) / 100,
          highRisk: processedData.filter(item => item.risk_level === 'high').length,
          mediumRisk: processedData.filter(item => item.risk_level === 'medium').length
        }
      };
    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير المتأخرات:', error);
      throw error;
    }
  };

  // إعداد الأعمدة القابلة للإدارة
  const defaultColumns: ColumnConfig[] = [
    {
      title: 'كود المورد',
      dataIndex: 'supplier_code',
      key: 'supplier_code',
      type: 'text',
      visible: true,
      width: 120,
      align: 'right',
      sortable: true,
      filterable: true
    },
    {
      title: 'اسم المورد',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      type: 'text',
      visible: true,
      width: 200,
      align: 'right',
      sortable: true,
      filterable: true
    },
    {
      title: 'إجمالي الفواتير (₪)',
      dataIndex: 'total_invoices_amount',
      key: 'total_invoices_amount',
      type: 'currency',
      visible: true,
      width: 150,
      align: 'right',
      sortable: true,
      filterable: false
    },
    {
      title: 'المبلغ المدفوع (₪)',
      dataIndex: 'total_paid_amount',
      key: 'total_paid_amount',
      type: 'currency',
      visible: true,
      width: 150,
      align: 'right',
      sortable: true,
      filterable: false
    },
    {
      title: 'الرصيد المستحق (₪)',
      dataIndex: 'outstanding_balance',
      key: 'outstanding_balance',
      type: 'currency',
      visible: true,
      width: 150,
      align: 'right',
      sortable: true,
      filterable: false
    },
    {
      title: 'الحد الائتماني (₪)',
      dataIndex: 'credit_limit',
      key: 'credit_limit',
      type: 'currency',
      visible: true,
      width: 150,
      align: 'right',
      sortable: true,
      filterable: false
    },
    {
      title: 'نسبة الاستخدام',
      dataIndex: 'credit_usage_percentage',
      key: 'credit_usage_percentage',
      type: 'number',
      visible: true,
      width: 150,
      align: 'center',
      sortable: true,
      filterable: false
    },
    {
      title: 'أيام التأخير',
      dataIndex: 'overdue_days',
      key: 'overdue_days',
      type: 'number',
      visible: true,
      width: 120,
      align: 'center',
      sortable: true,
      filterable: false
    },
    {
      title: 'شروط الدفع',
      dataIndex: 'payment_terms',
      key: 'payment_terms',
      type: 'number',
      visible: true,
      width: 120,
      align: 'center',
      sortable: true,
      filterable: false
    },
    {
      title: 'آخر دفعة (ميلادي)',
      dataIndex: 'last_payment_date',
      key: 'last_payment_date',
      type: 'date',
      visible: true,
      width: 140,
      align: 'center',
      sortable: true,
      filterable: false
    }
  ];

  // استخدام الأعمدة المُدارة أو الافتراضية
  const activeColumns = managedColumns.length > 0 ? managedColumns : defaultColumns;

  // تحويل الأعمدة المُدارة إلى تنسيق Ant Design
  const columns = activeColumns
    .filter(col => col.visible)
    .map(col => ({
      title: col.title,
      dataIndex: col.dataIndex,
      key: col.key,
      width: col.width,
      align: col.align as 'left' | 'center' | 'right',
      sorter: col.sortable,
      render: (value: any) => {
        switch (col.type) {
          case 'currency':
            return (
              <Text strong style={{
                color: col.key === 'outstanding_balance'
                  ? (value > 0 ? '#ff4d4f' : '#52c41a')
                  : col.key === 'total_paid_amount' ? '#52c41a' : '#1890ff'
              }}>
                {value?.toLocaleString() || '0'}
              </Text>
            );
          case 'number':
            if (col.key === 'credit_usage_percentage') {
              return (
                <Progress
                  percent={Math.round(value || 0)}
                  size="small"
                  strokeColor={value >= 90 ? '#ff4d4f' : value >= 70 ? '#fa8c16' : '#52c41a'}
                />
              );
            }
            if (col.key === 'overdue_days') {
              if (value <= 0) return <Tag color="green">في الموعد</Tag>;
              if (value <= 30) return <Tag color="orange">{value} يوم</Tag>;
              return <Tag color="red">{value} يوم</Tag>;
            }
            if (col.key === 'payment_terms') {
              return <Tag color="blue">{value} يوم</Tag>;
            }
            return <Text>{value}</Text>;
          case 'date':
            return value ? DateUtils.formatForDisplay(value, DATE_FORMATS.DISPLAY_DATE) : '-';
          case 'text':
          default:
            return (
              <Text strong={col.key === 'supplier_code' || col.key === 'supplier_name'}
                    style={{ color: col.key === 'supplier_code' ? '#1890ff' : undefined }}>
                {value}
              </Text>
            );
        }
      }
    }));

  // أعمدة تقرير المتأخرات
  const overdueColumns = [
    {
      title: 'اسم المورد',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      width: 200,
      render: (name: string) => (
        <Text strong>{name}</Text>
      )
    },
    {
      title: 'المبلغ المتأخر (₪)',
      dataIndex: 'outstanding_balance',
      key: 'outstanding_balance',
      width: 150,
      align: 'right' as const,
      render: (balance: number) => (
        <Text strong style={{ color: '#ff4d4f' }}>
          {balance.toLocaleString()}
        </Text>
      )
    },
    {
      title: 'أيام التأخير',
      dataIndex: 'overdue_days',
      key: 'overdue_days',
      width: 120,
      align: 'center' as const,
      render: (days: number) => {
        if (days <= 30) return <Tag color="orange">{days} يوم</Tag>;
        if (days <= 60) return <Tag color="red">{days} يوم</Tag>;
        return <Tag color="red" style={{ backgroundColor: '#ff4d4f', color: 'white' }}>{days} يوم</Tag>;
      }
    },
    {
      title: 'مستوى المخاطر',
      dataIndex: 'risk_level',
      key: 'risk_level',
      width: 120,
      align: 'center' as const,
      render: (risk: string) => {
        const colors = { high: 'red', medium: 'orange', low: 'green' };
        const labels = { high: 'عالي', medium: 'متوسط', low: 'منخفض' };
        return <Tag color={colors[risk as keyof typeof colors]}>{labels[risk as keyof typeof labels]}</Tag>;
      }
    },
    {
      title: 'الهاتف',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
      render: (phone: string) => phone || '-'
    }
  ];

  return (
    <Card>
      {/* شريط أدوات إدارة الأعمدة */}
      <ReportColumnManager
        columns={activeColumns}
        onColumnsChange={setManagedColumns}
        reportTitle="تقرير مستحقات الموردين"
      />

      {/* مكون اختيار قالب الطباعة */}
      <ReportTemplateSelector
        onTemplateSelect={setSelectedTemplate}
        onPrintWithTemplate={(template) => {
          // طباعة التقرير بالقالب المحدد
          const templateSettings = getTemplateSettings(template.id);
          console.log('طباعة بالقالب:', template.name, templateSettings);
          // هنا يمكن إضافة منطق الطباعة
        }}
        showPrintButton={true}
        size="small"
        style={{ marginBottom: 16 }}
      />

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'summary',
            label: (
              <Space>
                <DollarOutlined />
                ملخص المستحقات
              </Space>
            ),
            children: (
              <UniversalReport
                reportType={'supplier_payables' as ReportType}
                title="تقرير مستحقات الموردين"
                description="تقرير شامل لمستحقات الموردين مع تحليل الائتمان والمدفوعات"
                onGenerateReport={generateReport}
                defaultFilters={createDefaultFilters('supplier_payables')}
                showDateRange={true}
                showSupplierFilter={true}
                showStatusFilter={true}
                showAmountRangeFilter={true}
                showAdvancedSearch={true}
                showPrintOptions={true}
                showExportOptions={true}
              />
            )
          },
          {
            key: 'overdue',
            label: (
              <Space>
                <AlertOutlined />
                المتأخرات
              </Space>
            ),
            children: (
              <UniversalReport
                reportType={'supplier_payables_overdue' as ReportType}
                title="تقرير المتأخرات"
                description="تقرير المدفوعات المتأخرة للموردين مع مستويات المخاطر"
                onGenerateReport={generateOverdueReport}
                defaultFilters={createDefaultFilters('supplier_payables')}
                showDateRange={true}
                showSupplierFilter={true}
                showAmountRangeFilter={true}
                showAdvancedSearch={true}
                showPrintOptions={true}
                showExportOptions={true}
              />
            )
          },
          {
            key: 'analysis',
            label: (
              <Space>
                <BarChartOutlined />
                التحليل
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <FileTextOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                    <h3>تحليل مستحقات الموردين</h3>
                    <p>سيتم إضافة الرسوم البيانية والتحليلات المتقدمة هنا</p>
                  </div>
                </Space>
              </Card>
            )
          },
          {
            key: 'aging',
            label: (
              <Space>
                <ClockCircleOutlined />
                تحليل الأعمار
              </Space>
            ),
            children: (
              <Card>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <ClockCircleOutlined style={{ fontSize: '48px', color: '#fa8c16' }} />
                    <h3>تحليل أعمار المديونيات</h3>
                    <p>تحليل المديونيات حسب الفترات الزمنية (30، 60، 90+ يوم)</p>
                  </div>
                </Space>
              </Card>
            )
          }
        ]}
      />
    </Card>
  );
};

export default SupplierPayablesReport;
