import React, { useState, useRef } from 'react';
import { Card, Button, Space, DatePicker, Select, Table, Typography, Divider, Spin, App, message } from 'antd';
import { PrinterOutlined, DownloadOutlined, FileExcelOutlined, FilePdfOutlined, ReloadOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import dayjs, { DateUtils, DATE_FORMATS } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger'
import UnifiedPrintButton from '../common/UnifiedPrintButton'


const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

// أنواع التقارير
export type ReportType = 'inventory_detailed' | 'inventory_movements' | 'inventory_audit' | 'material_consumption' | 'low_stock' | 'purchases_by_supplier' | 'purchases_by_item' | 'supplier_payables' | 'purchase_analysis' | 'cost_analysis' | 'employee_attendance' | 'employee_payroll' | 'employee_leaves' | 'employee_performance' | 'employee_overtime';

// واجهة بيانات التقرير
export interface ReportData {
  title: string;
  subtitle?: string;
  columns: any[];
  data: any[];
  statistics?: any[];
  summary?: {
    totalItems?: number;
    totalValue?: number;
    totalQuantity?: number;
    [key: string]: any;
  };
}

// واجهة خصائص التقرير
export interface ReportProps {
  title: string;
  description?: string;
  onGenerateReport?: (filters: ReportFilters) => Promise<ReportData>;
  generateReport?: (filters: ReportFilters) => Promise<ReportData>;
  defaultFilters?: Partial<ReportFilters>;
  showDateRange?: boolean;
  showWarehouseFilter?: boolean;
  showCategoryFilter?: boolean;
  showItemFilter?: boolean;
  showSupplierFilter?: boolean;
  showMovementTypeFilter?: boolean;
  customFilters?: React.ReactNode;
}

// واجهة فلاتر التقرير
export interface ReportFilters {
  dateRange?: [dayjs.Dayjs, dayjs.Dayjs] | string[];
  warehouseId?: number;
  categoryId?: number;
  itemId?: number;
  supplierId?: number;
  customerId?: number;
  departmentId?: number;
  status?: string;
  year?: number;
  movementType?: string;
  [key: string]: any;
}

const ReportContainer = styled.div`
  .report-header {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
  }

  .report-filters {
    background: white;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
  }

  .report-content {
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }

  .report-summary {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    border: 1px solid #e9ecef;
  }

  .summary-item {
    text-align: center;
    padding: 8px;
  }

  .summary-value {
    font-size: 24px;
    font-weight: bold;
    color: #1890ff;
    display: block;
  }

  .summary-label {
    color: #666;
    font-size: 12px;
  }

  @media print {
    .no-print {
      display: none !important;
    }
    
    .report-container {
      box-shadow: none !important;
      border: none !important;
    }
  }
`;

const ReportBase: React.FC<ReportProps> = ({
  title,
  description,
  onGenerateReport,
  generateReport,
  defaultFilters = {},
  showDateRange = true,
  showWarehouseFilter = true,
  showCategoryFilter = false,
  showItemFilter = false,
  showSupplierFilter = false,
  showMovementTypeFilter = false,
  customFilters
}) => {
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [filters, setFilters] = useState<ReportFilters>(defaultFilters);
  const [warehouses, setWarehouses] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [items, setItems] = useState<any[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const printRef = useRef<HTMLDivElement>(null);

  // تحميل البيانات المساعدة
  React.useEffect(() => {
    loadSupportData();
  }, []);

  const loadSupportData = async () => {
    try {
      Logger.info('ReportBase', '🔄 جاري تحميل البيانات المساعدة للتقارير...')

      if (!window.electronAPI) {
        Logger.error('ReportBase', '❌ window.electronAPI غير متوفر')
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      }

      // تحميل البيانات الحقيقية من قاعدة البيانات
      if (showWarehouseFilter) {
        const warehousesResponse = await window.electronAPI.getWarehouses();
        const warehousesData = Array.isArray(warehousesResponse) ? warehousesResponse : ((warehousesResponse as any)?.success ? (warehousesResponse as any).data : warehousesResponse);
        setWarehouses(warehousesData || []);
        Logger.info('ReportBase', '✅ تم تحميل بيانات المخازن')
      }

      if (showCategoryFilter) {
        const categoriesResponse = await window.electronAPI.getCategories();
        const categoriesData = Array.isArray(categoriesResponse) ? categoriesResponse : ((categoriesResponse as any)?.success ? (categoriesResponse as any).data : categoriesResponse);
        setCategories(categoriesData || []);
        Logger.info('ReportBase', '✅ تم تحميل بيانات الفئات')
      }

      if (showItemFilter) {
        const itemsResponse = await window.electronAPI.getItems();
        const itemsData = Array.isArray(itemsResponse) ? itemsResponse : ((itemsResponse as any)?.success ? (itemsResponse as any).data : itemsResponse);
        setItems(itemsData || []);
        Logger.info('ReportBase', '✅ تم تحميل بيانات الأصناف')
      }

      if (showSupplierFilter) {
        const suppliersResponse = await window.electronAPI.getSuppliers();
        const suppliersData = suppliersResponse?.success ? suppliersResponse.data : suppliersResponse;
        setSuppliers(suppliersData || []);
        Logger.info('ReportBase', '✅ تم تحميل بيانات الموردين')
      }

      Logger.info('ReportBase', '✅ تم تحميل جميع البيانات المساعدة بنجاح')
    } catch (error) {
      Logger.error('ReportBase', 'خطأ في تحميل البيانات المساعدة:', error);
    }
  };

  // توليد التقرير
  const handleGenerateReport = async () => {
    setLoading(true);
    try {
      // تحويل التواريخ إلى strings قبل الإرسال (تنسيق ميلادي)
      const processedFilters = {
        ...filters,
        dateRange: filters.dateRange ? [
          DateUtils.formatForDatabase(filters.dateRange[0]),
          DateUtils.formatForDatabase(filters.dateRange[1])
        ] : undefined
      };

      const reportFunction = onGenerateReport || generateReport;
      if (!reportFunction) {
        throw new Error('لم يتم تحديد دالة إنشاء التقرير');
      }

      const data = await reportFunction(processedFilters);
      setReportData(data);
      // message.success('تم إنشاء التقرير بنجاح');
    } catch (error) {
      Logger.error('ReportBase', 'خطأ في إنشاء التقرير:', error);
      // message.error('حدث خطأ في إنشاء التقرير');
    } finally {
      setLoading(false);
    }
  };





  // تصدير إلى Excel
  const handleExportExcel = () => {
    if (!reportData) {
      message.warning('لا توجد بيانات للتصدير');
      return;
    }

    // فحص البيانات الفارغة
    const dataToExport = Array.isArray(reportData) ? reportData : [reportData];
    if (dataToExport.length === 0) {
      message.warning('لا توجد بيانات للتصدير إلى Excel');
      return;
    }

    try {
      // هنا سيتم تنفيذ تصدير Excel
      message.success(`تم تصدير ${dataToExport.length} عنصر إلى Excel بنجاح`);
    } catch (error) {
      message.error('حدث خطأ في تصدير التقرير إلى Excel');
    }
  };

  // تصدير إلى PDF
  const handleExportPDF = () => {
    if (!reportData) return;
    
    try {
      // هنا سيتم تنفيذ تصدير PDF
      // message.success('تم تصدير التقرير إلى PDF بنجاح');
    } catch (error) {
      // message.error('حدث خطأ في تصدير التقرير');
    }
  };

  return (
    <ReportContainer>
      <div className="report-header">
        <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
          {title}
        </Title>
        {description && (
          <Text type="secondary" style={{ fontSize: '14px' }}>
            {description}
          </Text>
        )}
      </div>

      <Card className="report-filters no-print" title="فلاتر التقرير">
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap', alignItems: 'center' }}>
            {showDateRange && (
              <div>
                <Text strong>الفترة الزمنية:</Text>
                <RangePicker
                  style={{ marginLeft: '8px' }}
                  value={Array.isArray(filters.dateRange) && typeof filters.dateRange[0] === 'string'
                    ? null
                    : filters.dateRange as [dayjs.Dayjs, dayjs.Dayjs]}
                  onChange={(dates) => setFilters({ ...filters, dateRange: dates as [dayjs.Dayjs, dayjs.Dayjs] })}
                  placeholder={['من تاريخ', 'إلى تاريخ']}
                />
              </div>
            )}

            {showWarehouseFilter && (
              <div>
                <Text strong>المخزن:</Text>
                <Select
                  style={{ width: 200, marginLeft: '8px' }}
                  placeholder="اختر المخزن"
                  allowClear
                  value={filters.warehouseId}
                  onChange={(value) => setFilters({ ...filters, warehouseId: value })}
                >
                  {warehouses && Array.isArray(warehouses) && warehouses.map(warehouse => (
                    <Select.Option key={warehouse.id} value={warehouse.id}>
                      {warehouse.name}
                    </Select.Option>
                  ))}
                </Select>
              </div>
            )}

            {showCategoryFilter && (
              <div>
                <Text strong>الفئة:</Text>
                <Select
                  style={{ width: 200, marginLeft: '8px' }}
                  placeholder="اختر الفئة"
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                  }
                  value={filters.categoryId}
                  onChange={(value) => setFilters({ ...filters, categoryId: value })}
                >
                  {categories && Array.isArray(categories) && categories.map(category => (
                    <Select.Option key={category.id} value={category.id}>
                      {category.name}
                    </Select.Option>
                  ))}
                </Select>
              </div>
            )}

            {showItemFilter && (
              <div>
                <Text strong>الصنف:</Text>
                <Select
                  style={{ width: 200, marginLeft: '8px' }}
                  placeholder="اختر الصنف"
                  allowClear
                  showSearch
                  value={filters.itemId}
                  onChange={(value) => setFilters({ ...filters, itemId: value })}
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {items && Array.isArray(items) && items.map(item => (
                    <Select.Option key={item.id} value={item.id}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              </div>
            )}

            {showSupplierFilter && (
              <div>
                <Text strong>المورد:</Text>
                <Select
                  style={{ width: 200, marginLeft: '8px' }}
                  placeholder="اختر المورد"
                  allowClear
                  showSearch
                  value={filters.supplierId}
                  onChange={(value) => setFilters({ ...filters, supplierId: value })}
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {suppliers && Array.isArray(suppliers) && suppliers.map(supplier => (
                    <Select.Option key={supplier.id} value={supplier.id}>
                      {supplier.name}
                    </Select.Option>
                  ))}
                </Select>
              </div>
            )}

            {showMovementTypeFilter && (
              <div>
                <Text strong>نوع الحركة:</Text>
                <Select
                  style={{ width: 150, marginLeft: '8px' }}
                  placeholder="جميع الحركات"
                  allowClear
                  value={filters.movementType}
                  onChange={(value) => setFilters({ ...filters, movementType: value })}
                >
                  <Select.Option value="in">إدخال</Select.Option>
                  <Select.Option value="out">إخراج</Select.Option>
                  <Select.Option value="transfer">تحويل</Select.Option>
                  <Select.Option value="adjustment">تسوية</Select.Option>
                </Select>
              </div>
            )}

            {customFilters}
          </div>

          <div>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={handleGenerateReport}
              loading={loading}
              size="large"
            >
              إنشاء التقرير
            </Button>
          </div>
        </Space>
      </Card>

      {reportData && (
        <div ref={printRef}>
          <Card className="report-content">
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
              <Title level={2} style={{ color: '#1890ff', margin: 0 }}>
                {reportData.title}
              </Title>
              {reportData.subtitle && (
                <Text type="secondary" style={{ fontSize: '16px' }}>
                  {reportData.subtitle}
                </Text>
              )}
              <div style={{ marginTop: '8px' }}>
                <Text type="secondary">
                  تاريخ الإنشاء: {DateUtils.formatForDisplay(DateUtils.getCurrentDate(), DATE_FORMATS.DISPLAY_DATE_TIME)} (ميلادي)
                </Text>
              </div>
            </div>

            {reportData.summary && (
              <div className="report-summary">
                <div style={{ display: 'flex', justifyContent: 'space-around', flexWrap: 'wrap' }}>
                  {Object.entries(reportData.summary).map(([key, value]) => {
                    // تحديد المسميات للمفاتيح المختلفة
                    const getLabel = (key: string): string => {
                      const labels: { [key: string]: string } = {
                        // المسميات العامة
                        'totalItems': 'إجمالي الأصناف',
                        'totalValue': 'إجمالي القيمة (₪)',
                        'totalQuantity': 'إجمالي الكمية',
                        'totalAmount': 'إجمالي المبلغ (₪)',
                        'totalInvoices': 'إجمالي الفواتير',
                        'totalSuppliers': 'إجمالي الموردين',
                        'totalPaid': 'إجمالي المدفوع (₪)',
                        'totalOutstanding': 'إجمالي المستحق (₪)',
                        'avgInvoiceValue': 'متوسط قيمة الفاتورة (₪)',
                        'avgItemValue': 'متوسط قيمة الصنف (₪)',
                        'avgQuantityPerItem': 'متوسط الكمية لكل صنف',
                        'overallPaymentPercentage': 'نسبة السداد الإجمالية (%)',
                        'totalCostCenters': 'إجمالي مراكز التكلفة',
                        'totalDirectCost': 'إجمالي التكلفة المباشرة (₪)',
                        'totalIndirectCost': 'إجمالي التكلفة غير المباشرة (₪)',
                        'grandTotal': 'الإجمالي العام (₪)',
                        'avgCostPerUnit': 'متوسط التكلفة للوحدة (₪)',
                        'directCostPercentage': 'نسبة التكلفة المباشرة (%)',
                        'indirectCostPercentage': 'نسبة التكلفة غير المباشرة (%)',
                        'totalPurchaseValue': 'إجمالي قيمة المشتريات (₪)',
                        'totalPurchaseQuantity': 'إجمالي كمية المشتريات',
                        'avgPurchaseValue': 'متوسط قيمة المشتريات (₪)',
                        'growthRate': 'معدل النمو (%)',
                        'purchaseFrequency': 'تكرار المشتريات',
                        'avgDeliveryTime': 'متوسط وقت التسليم (يوم)',
                        'qualityScore': 'نقاط الجودة',
                        'supplierReliability': 'موثوقية المورد (%)'
                      };
                      return labels[key] || '';
                    };

                    const label = getLabel(key);

                    // عرض العنصر فقط إذا كان له مسمى وكان قيمة رقمية بسيطة
                    if (!label || typeof value === 'object' || Array.isArray(value)) return null;

                    return (
                      <div key={key} className="summary-item">
                        <span className="summary-value">
                          {typeof value === 'number' ? value.toLocaleString() : value}
                        </span>
                        <span className="summary-label">
                          {label}
                        </span>
                      </div>
                    );
                  }).filter(Boolean)}
                </div>
              </div>
            )}

            <Table
              columns={reportData.columns}
              dataSource={reportData.data}
              rowKey={(record) => record.key || record.id || Math.random().toString()}
              pagination={{
                pageSize: 50,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} من ${total} عنصر`,
              }}
              scroll={{ x: 'max-content' }}
              size="small"
              bordered
            />

            <div className="no-print" style={{ marginTop: '20px', textAlign: 'center' }}>
              <Space size="middle">
                <UnifiedPrintButton
                  data={{
                    title: reportData?.title || 'تقرير',
                    subtitle: reportData?.subtitle,
                    date: new Date().toLocaleDateString('ar-SA'),
                    data: reportData?.data || [],
                    columns: reportData?.columns || [],
                    summary: reportData?.summary || {},
                    metadata: {
                      generatedAt: new Date().toISOString(),
                      reportType: 'detailed_report',
                      totalRecords: reportData?.data?.length || 0
                    }
                  }}
                  type="report"
                  subType="financial"
                  buttonText="طباعة"
                  size="large"
                  showDropdown={true}
                  showExportOptions={true}
                  showSettings={true}
                  disabled={!reportData || !reportData.data || reportData.data.length === 0}
                  onAfterPrint={() => message.success('تم طباعة التقرير بنجاح')}
                  onError={() => message.error('فشل في طباعة التقرير')}
                />
                <Button
                  icon={<FileExcelOutlined />}
                  onClick={handleExportExcel}
                  size="large"
                  style={{ backgroundColor: '#52c41a', borderColor: '#52c41a', color: 'white' }}
                >
                  تصدير Excel
                </Button>
                <Button
                  icon={<FilePdfOutlined />}
                  onClick={handleExportPDF}
                  size="large"
                  style={{ backgroundColor: '#f5222d', borderColor: '#f5222d', color: 'white' }}
                >
                  تصدير PDF
                </Button>
              </Space>
            </div>
          </Card>
        </div>
      )}

      {loading && (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>
            <Text>جاري إنشاء التقرير...</Text>
          </div>
        </div>
      )}
    </ReportContainer>
  );
};

export default ReportBase;
