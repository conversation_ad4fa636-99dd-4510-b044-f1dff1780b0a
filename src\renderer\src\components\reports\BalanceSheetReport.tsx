import React from 'react'
import UniversalReport from './UniversalReport'
import type { ReportData, ReportType } from '../../types/reports'

const BalanceSheetReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير الميزانية العمومية...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getBalanceSheet(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const balanceSheetData = response.data;

      // إعداد الأعمدة
      const columns = [
        {
          key: 'account_code',
          title: 'كود الحساب',
          align: 'center' as const,
          width: '120px'
        },
        {
          key: 'account_name',
          title: 'اسم الحساب',
          align: 'right' as const,
          width: '200px'
        },
        {
          key: 'account_type',
          title: 'نوع الحساب',
          align: 'center' as const,
          width: '120px'
        },
        {
          key: 'balance',
          title: 'الرصيد',
          align: 'left' as const,
          format: 'currency' as const,
          width: '150px'
        }
      ];

      // إضافة مفاتيح فريدة للبيانات
      const dataWithKeys = balanceSheetData.map((item: any, index: number) => ({
        ...item,
        key: item.account_code || index
      }));

      // حساب الإحصائيات
      const assets = balanceSheetData.filter((item: any) => item.account_type === 'asset');
      const liabilities = balanceSheetData.filter((item: any) => item.account_type === 'liability');
      const equity = balanceSheetData.filter((item: any) => item.account_type === 'equity');

      const statistics = [
        {
          title: 'إجمالي الأصول',
          value: assets.reduce((sum: number, item: any) => sum + (item.balance || 0), 0),
          format: 'currency' as const,
          color: '#52c41a'
        },
        {
          title: 'إجمالي الخصوم',
          value: liabilities.reduce((sum: number, item: any) => sum + (item.balance || 0), 0),
          format: 'currency' as const,
          color: '#ff4d4f'
        },
        {
          title: 'حقوق الملكية',
          value: equity.reduce((sum: number, item: any) => sum + (item.balance || 0), 0),
          format: 'currency' as const,
          color: '#1890ff'
        },
        {
          title: 'عدد الحسابات',
          value: balanceSheetData.length,
          format: 'number' as const,
          color: '#722ed1'
        }
      ];

      return {
        title: 'تقرير الميزانية العمومية',
        subtitle: `فترة التقرير: ${filters.start_date || 'غير محدد'} - ${filters.end_date || 'غير محدد'}`,
        columns,
        data: dataWithKeys,
        statistics,
        summary: {
          totalRecords: balanceSheetData.length,
          totalAssets: assets.reduce((sum: number, item: any) => sum + (item.balance || 0), 0),
          totalLiabilities: liabilities.reduce((sum: number, item: any) => sum + (item.balance || 0), 0),
          totalEquity: equity.reduce((sum: number, item: any) => sum + (item.balance || 0), 0)
        },
        metadata: {
          dateRange: `${filters.start_date || ''} - ${filters.end_date || ''}`,
          generatedAt: new Date().toISOString(),
          reportType: 'balance_sheet' as ReportType,
          totalRecords: balanceSheetData.length
        }
      };

    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير الميزانية العمومية:', error);
      throw error;
    }
  };

  return (
    <UniversalReport
      reportType={'balance_sheet' as ReportType}
      title="تقرير الميزانية العمومية"
      description="تقرير شامل للميزانية العمومية يعرض الأصول والخصوم وحقوق الملكية"
      onGenerateReport={generateReport}
      showDateRange={true}
      showStatistics={true}
      showSummary={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default BalanceSheetReport;
