import React, { useState } from 'react'
import {
  Button,
  Dropdown,
  Space,
  Typography,
  Switch,
  Slider,
  Card,
  Divider
} from 'antd'
import {
  QuestionCircleOutlined,
  BookOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import type { MenuProps } from 'antd'
import SimpleTutorial from './SimpleTutorial'
import AboutModal from '../activation/AboutModal'
import { comprehensiveAudioSystem } from '../../utils/comprehensiveAudioSystem'

const { Text } = Typography

interface SimpleHelpButtonProps {
  style?: React.CSSProperties
}

const SimpleHelpButton: React.FC<SimpleHelpButtonProps> = ({ style }) => {
  const [tutorialVisible, setTutorialVisible] = useState(false)
  const [audioSettingsVisible, setAudioSettingsVisible] = useState(false)
  const [aboutVisible, setAboutVisible] = useState(false)
  const [audioSettings, setAudioSettings] = useState(comprehensiveAudioSystem.getSettings())

  // قائمة المساعدة البسيطة
  const helpMenuItems: MenuProps['items'] = [
    {
      key: 'quick-help',
      icon: <QuestionCircleOutlined />,
      label: 'مساعدة سريعة',
      onClick: () => showQuickHelp()
    },
    {
      key: 'tutorial',
      icon: <BookOutlined />,
      label: 'دليل التعلم',
      onClick: () => setTutorialVisible(true)
    },
    {
      key: 'audio-guide',
      icon: <PlayCircleOutlined />,
      label: 'تفعيل الدليل الصوتي',
      onClick: () => toggleAudioGuide()
    },
    {
      type: 'divider'
    },
    {
      key: 'audio-settings',
      icon: <SettingOutlined />,
      label: 'إعدادات الصوت',
      onClick: () => setAudioSettingsVisible(!audioSettingsVisible)
    },

    {
      type: 'divider'
    },
    {
      key: 'about',
      icon: <InfoCircleOutlined />,
      label: 'حول البرنامج',
      onClick: () => setAboutVisible(true)
    }
  ]

  const showQuickHelp = () => {
    comprehensiveAudioSystem.speakText('quick-help', 
      'مرحباً بك في برنامج المحاسبة والإنتاج. استخدم القائمة الجانبية للتنقل. مرر الماوس على أي عنصر لسماع شرحه. انقر على دليل التعلم للحصول على شرح مفصل لكل قسم.'
    )
  }

  const toggleAudioGuide = () => {
    const newSettings = { 
      ...audioSettings, 
      voiceEnabled: !audioSettings.voiceEnabled 
    }
    setAudioSettings(newSettings)
    comprehensiveAudioSystem.updateSettings(newSettings)
    
    if (newSettings.voiceEnabled) {
      comprehensiveAudioSystem.speakText('audio-enabled', 'تم تفعيل الدليل الصوتي. مرر الماوس على أي عنصر لسماع شرحه')
    }
  }

  const handleAudioSettingChange = (key: keyof typeof audioSettings, value: any) => {
    const newSettings = { ...audioSettings, [key]: value }
    setAudioSettings(newSettings)
    comprehensiveAudioSystem.updateSettings(newSettings)
  }

  // إعدادات الصوت المدمجة
  const audioSettingsContent = audioSettingsVisible ? (
    <Card 
      size="small" 
      style={{ 
        position: 'absolute', 
        top: '100%', 
        right: 0, 
        width: 300, 
        zIndex: 1000,
        marginTop: 8,
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
      }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Text strong>إعدادات الصوت</Text>
        
        <Space>
          <Text>تفعيل الأصوات:</Text>
          <Switch 
            checked={audioSettings.enabled}
            onChange={(checked) => handleAudioSettingChange('enabled', checked)}
            size="small"
          />
        </Space>

        <Space>
          <Text>الدليل الصوتي:</Text>
          <Switch 
            checked={audioSettings.voiceEnabled}
            onChange={(checked) => handleAudioSettingChange('voiceEnabled', checked)}
            size="small"
          />
        </Space>

        <Space>
          <Text>أصوات النقر:</Text>
          <Switch 
            checked={audioSettings.clickSounds}
            onChange={(checked) => handleAudioSettingChange('clickSounds', checked)}
            size="small"
          />
        </Space>

        <Space>
          <Text>أصوات التمرير:</Text>
          <Switch 
            checked={audioSettings.hoverSounds}
            onChange={(checked) => handleAudioSettingChange('hoverSounds', checked)}
            size="small"
          />
        </Space>

        <Divider style={{ margin: '8px 0' }} />

        <div>
          <Text>مستوى الصوت: {audioSettings.volume}%</Text>
          <Slider
            value={audioSettings.volume}
            onChange={(value: number) => handleAudioSettingChange('volume', value)}
            style={{ marginTop: 8 }}
            min={0}
            max={100}
          />
        </div>


      </Space>
    </Card>
  ) : null

  return (
    <div style={{ position: 'relative', ...style }}>
      <Dropdown
        menu={{ items: helpMenuItems }}
        trigger={['click']}
        placement="bottomRight"
      >
        <Button
          type="primary"
          shape="circle"
          icon={<QuestionCircleOutlined />}
          size="small"
          style={{
            backgroundColor: '#52c41a',
            borderColor: '#52c41a',
            boxShadow: '0 1px 4px rgba(82, 196, 26, 0.3)',
            height: '24px',
            width: '24px',
            minWidth: '24px',
            padding: 0,
            fontSize: '12px'
          }}
          data-audio-id="help-button"
          title="المساعدة والدليل الصوتي"
        />
      </Dropdown>

      {audioSettingsContent}

      <SimpleTutorial
        visible={tutorialVisible}
        onClose={() => setTutorialVisible(false)}
      />

      <AboutModal
        visible={aboutVisible}
        onClose={() => setAboutVisible(false)}
      />
    </div>
  )
}

export default SimpleHelpButton
