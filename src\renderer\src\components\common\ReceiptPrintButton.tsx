import React from 'react'
import UnifiedPrintButton from './UnifiedPrintButton'
import { PrintData } from '../../types/print'
import { useDocumentPrintSettings } from '../../hooks/usePrintSettings'

interface ReceiptPrintButtonProps {
  // بيانات الإيصال
  receiptData: {
    id: string | number
    receiptNumber: string
    receiptDate: string
    type: 'payment' | 'receipt' // قبض أو صرف
    amount: number
    paymentMethod?: 'cash' | 'bank' | 'check' | 'card'
    customerName?: string
    customerPhone?: string
    description: string
    notes?: string
    referenceNumber?: string
    bankName?: string
    checkNumber?: string
  }
  
  // إعدادات المظهر
  buttonText?: string
  size?: 'small' | 'middle' | 'large'
  disabled?: boolean
  showDropdown?: boolean
  
  // callbacks
  onPrintSuccess?: () => void
  onPrintError?: (error: string) => void
}

const ReceiptPrintButton: React.FC<ReceiptPrintButtonProps> = ({
  receiptData,
  buttonText,
  size = 'middle',
  disabled = false,
  showDropdown = true,
  onPrintSuccess,
  onPrintError
}) => {
  // استخدام الإعدادات المركزية للإيصالات
  const { settings: centralSettings, isReady } = useDocumentPrintSettings('receipt')
  // تحويل بيانات الإيصال إلى تنسيق PrintData
  const printData: PrintData = {
    id: receiptData.id,
    title: receiptData.type === 'payment' ? 'سند قبض' : 'سند صرف',
    number: receiptData.receiptNumber,
    date: receiptData.receiptDate,
    customer: receiptData.customerName ? {
      name: receiptData.customerName,
      phone: receiptData.customerPhone
    } : undefined,
    totals: { total: receiptData.amount },
    notes: receiptData.notes,
    // إضافة تفاصيل الدفع كعناصر محسنة
    items: [{
      id: 1,
      name: receiptData.description || 'دفعة',
      description: getPaymentMethodDescription(receiptData),
      quantity: 1,
      unit: 'مبلغ',
      unitPrice: receiptData.amount,
      total: receiptData.amount
    }]
  }

  // وصف طريقة الدفع
  function getPaymentMethodDescription(data: typeof receiptData): string {
    const methods = {
      cash: 'نقداً',
      bank: 'تحويل بنكي',
      check: 'شيك',
      card: 'بطاقة ائتمان'
    }
    
    let description = `طريقة الدفع: ${methods[data.paymentMethod || 'cash']}`
    
    if (data.referenceNumber) {
      description += ` | رقم المرجع: ${data.referenceNumber}`
    }
    
    if (data.bankName) {
      description += ` | البنك: ${data.bankName}`
    }
    
    if (data.checkNumber) {
      description += ` | رقم الشيك: ${data.checkNumber}`
    }
    
    return description
  }

  // إعدادات مخصصة للإيصالات من المصدر المركزي
  const customSettings = {
    ...centralSettings,
    // تخصيصات خاصة بالإيصالات
    showSignature: true,
    showTerms: false,
    // تحديد اللون حسب نوع الإيصال باستخدام الألوان المركزية
    primaryColor: receiptData.type === 'payment'
      ? centralSettings?.secondaryColor || '#fff3cd'  // لون النجاح من الإعدادات المركزية
      : '#ff4d4f'  // لون الخطأ للأنواع الأخرى
  }

  // عدم عرض الزر إذا لم تكن الإعدادات جاهزة
  if (!isReady || !centralSettings) {
    return null
  }

  return (
    <UnifiedPrintButton
      data={printData}
      type="receipt"
      subType="payment"
      title={`طباعة ${printData.title}`}
      buttonText={buttonText || `طباعة ${printData.title}`}
      size={size}
      disabled={disabled}
      showDropdown={showDropdown}
      showExportOptions={true}
      showSettings={true}
      _documentId={`receipt_${receiptData.id}`}
      customSettings={customSettings}
      onAfterPrint={onPrintSuccess}
      onError={onPrintError}
    />
  )
}

export default ReceiptPrintButton
