import React, { useState, useEffect } from 'react'
import * as XLSX from 'xlsx'
import { Card, Table, Button, Space, Typography, message } from 'antd'
import { TeamOutlined, PlusOutlined, PrinterOutlined, ExportOutlined, ReloadOutlined } from '@ant-design/icons'
import { User } from '../types/global.d'
import { SafeLogger as Logger } from '../utils/logger'

const { Title } = Typography

interface SimpleUserManagementProps {
  currentUser: User
}

const SimpleUserManagement: React.FC<SimpleUserManagementProps> = ({ currentUser }) => {
  Logger.info('SimpleUserManagement', '🔄 تحميل مكون SimpleUserManagement', { currentUser })
  
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    Logger.info('SimpleUserManagement', '🔄 useEffect: بدء تحميل البيانات')
    loadUsers()
  }, [])

  const loadUsers = async () => {
    Logger.info('SimpleUserManagement', '🔄 loadUsers: بدء تحميل المستخدمين')
    setLoading(true)
    try {
      if (window.electronAPI) {
        Logger.info('SimpleUserManagement', '✅ window.electronAPI متوفر')
        const response = await window.electronAPI.getUsers()
        Logger.info('SimpleUserManagement', '📊 استجابة المستخدمين:', response)
        if (response.success && Array.isArray(response.data)) {
          setUsers(response.data)
        } else {
          Logger.error('SimpleUserManagement', 'خطأ في استجابة المستخدمين:', response)
          setUsers([])
        }
      } else {
        Logger.error('SimpleUserManagement', '❌ window.electronAPI غير متوفر')
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      }
    } catch (error) {
      Logger.error('SimpleUserManagement', '❌ خطأ في تحميل المستخدمين:', error)
      message.error('فشل في تحميل بيانات المستخدمين')
    } finally {
      setLoading(false)
      Logger.info('SimpleUserManagement', '✅ انتهاء تحميل المستخدمين')
    }
  }

  const columns = [
    {
      title: 'اسم المستخدم',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: 'الاسم الكامل',
      dataIndex: 'full_name',
      key: 'full_name',
    },
    {
      title: 'البريد الإلكتروني',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'الدور',
      dataIndex: 'role',
      key: 'role',
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <span style={{ color: isActive ? 'green' : 'red' }}>
          {isActive ? 'نشط' : 'غير نشط'}
        </span>
      ),
    },
  ]

  // طباعة قائمة المستخدمين
  const handlePrintUsers = async () => {
    try {
      if (users.length === 0) {
        message.warning('لا توجد بيانات للطباعة')
        return
      }

      // استخدام MasterPrintService الموحد
      const { MasterPrintService } = await import('../services/MasterPrintService')

      const printData = users.map(user => ({
        'كود المستخدم': user.user_code || 'غير محدد',
        'اسم المستخدم': user.username,
        'الاسم الكامل': user.full_name,
        'البريد الإلكتروني': user.email || '-',
        'الدور': user.role,
        'الحالة': user.is_active ? 'نشط' : 'معطل'
      }))

      const statistics = [
        { label: 'إجمالي المستخدمين', value: users.length },
        { label: 'المستخدمين النشطين', value: users.filter(u => u.is_active).length },
        { label: 'المستخدمين المعطلين', value: users.filter(u => !u.is_active).length }
      ]

      // طباعة التقرير (تم تعطيل MasterPrintService مؤقتاً)
      console.log('طباعة تقرير المستخدمين:', {
        title: 'قائمة المستخدمين البسيطة',
        data: printData,
        statistics,
        type: 'users'
      })

      message.success('تم إرسال التقرير للطباعة')
    } catch (error) {
      Logger.error('SimpleUserManagement', 'خطأ في طباعة المستخدمين:', error)
      message.error('فشل في طباعة قائمة المستخدمين')
    }
  }

  // تصدير قائمة المستخدمين
  const handleExportUsers = async () => {
    try {
      if (users.length === 0) {
        message.warning('لا توجد بيانات للتصدير')
        return
      }

      // استخدام MasterPrintService للتصدير
      const { MasterPrintService } = await import('../services/MasterPrintService')

      const exportData = users.map(user => ({
        'كود المستخدم': user.user_code || 'غير محدد',
        'اسم المستخدم': user.username,
        'الاسم الكامل': user.full_name,
        'البريد الإلكتروني': user.email || '-',
        'رقم الهاتف': user.phone || '-',
        'الدور': user.role,
        'الحالة': user.is_active ? 'نشط' : 'معطل',
        'تاريخ الإنشاء': user.created_at
      }))

      // تصدير إلى Excel باستخدام XLSX
      const ws = XLSX.utils.json_to_sheet(exportData)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, 'المستخدمين')
      XLSX.writeFile(wb, 'قائمة_المستخدمين_البسيطة.xlsx')
      message.success('تم تصدير البيانات بنجاح')
    } catch (error) {
      Logger.error('SimpleUserManagement', 'خطأ في تصدير المستخدمين:', error)
      message.error('فشل في تصدير البيانات')
    }
  }

  // تحديث قائمة المستخدمين
  const handleRefresh = () => {
    setLoading(true)
    // محاكاة إعادة تحميل البيانات
    setTimeout(() => {
      setLoading(false)
      message.success('تم تحديث البيانات')
    }, 1000)
  }

  Logger.info('SimpleUserManagement', '🎨 render: عرض مكون SimpleUserManagement', {
    usersCount: users.length,
    loading
  })

  return (
    <div>
      <Card
        title={
          <Space>
            <TeamOutlined />
            <Title level={4} style={{ margin: 0 }}>إدارة المستخدمين البسيطة</Title>
          </Space>
        }
        extra={
          <Space wrap>
            <Button
              icon={<ExportOutlined />}
              onClick={handleExportUsers}
              disabled={users.length === 0}
            >
              تصدير Excel
            </Button>
            <Button
              icon={<PrinterOutlined />}
              onClick={handlePrintUsers}
              disabled={users.length === 0}
            >
              طباعة
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              تحديث
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => message.info('سيتم إضافة هذه الميزة قريباً')}
            >
              إضافة مستخدم
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} مستخدم`
          }}
        />
      </Card>
    </div>
  )
}

export default SimpleUserManagement
