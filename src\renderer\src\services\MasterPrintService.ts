/**
 * خدمة الطباعة الرئيسية المتكاملة
 * تدعم جميع أنواع المستندات مع قوالب احترافية
 */

import { settingsManager } from '../utils/settings'
import { SafeLogger as Logger } from '../utils/logger'
import {
  UnifiedPrintOptions,
  PrintData as UnifiedPrintData,
  PrintResult,
  createDefaultPrintOptions
} from '../types/print'

// استخدام النوع الموحد من types/print.ts مع إضافات للتوافق
export interface EnhancedPrintData extends UnifiedPrintData {
  // إضافات خاصة بـ MasterPrintService للتوافق مع الكود الحالي
  subtotal?: number
  discount?: number
  tax?: number
  total?: number
  paid?: number
  remaining?: number
  terms?: string
  signature?: string
  dueDate?: string
  qrCode?: string
  headers?: string[]
  dateRange?: string
  rows?: any[][]
  template?: any

  // بيانات التقارير المخصصة
  data?: Array<{
    [key: string]: any
  }>

  // بيانات الصور المتقدمة
  imageSettings?: {
    layout?: 'single' | 'grid' | 'list' | 'catalog' | 'gallery'
    imagesPerPage?: number
    showMetadata?: boolean
    showThumbnails?: boolean
    imageQuality?: 'low' | 'medium' | 'high' | 'ultra'
    fitToPage?: boolean
  }

  // أعمدة التقرير
  columns?: Array<{
    key: string
    title: string
    align?: 'left' | 'center' | 'right'
    format?: 'currency' | 'number' | 'date' | 'percentage' | 'text'
    width?: string
  }>

  // ملخص التقرير
  summary?: {
    totalRecords?: number
    totalAmount?: number
    averageAmount?: number
    [key: string]: any
  }

  // فترة التقرير
  period?: string
  generatedBy?: string
}

// استخدام النوع الموحد مباشرة
export type EnhancedPrintOptions = UnifiedPrintOptions

// تصدير النوع للاستخدام في الملفات الأخرى
export type PrintData = EnhancedPrintData

export class MasterPrintService {
  private static instance: MasterPrintService
  private companyInfo: EnhancedPrintData['company']
  private defaultPrintOptions: Partial<EnhancedPrintOptions>

  // إدارة الموارد لمنع تسريب الذاكرة
  private activeTimeouts: Set<NodeJS.Timeout> = new Set()
  private activeIntervals: Set<NodeJS.Timeout> = new Set()
  private eventListeners: Map<string, { element: any; listener: EventListener }> = new Map()
  private openWindows: Set<any> = new Set()

  // تتبع نوافذ المعاينة المفتوحة لمنع إغلاقها عند إغلاق نوافذ الطباعة
  private activePreviewModals: Map<string, HTMLElement> = new Map()

  private constructor() {
    this.companyInfo = {
      name: 'ZET.IA',
      nameEn: 'ZET.IA - Accounting & Production System',
      address: 'فلسطين - غزة',
      phone: '**********',
      email: '<EMAIL>',
      website: 'www.zetia.com',
      logo: 'assets/company-logo.png',
      taxNumber: '*********'
    }

    this.defaultPrintOptions = {
      pageSize: 'A4',
      orientation: 'portrait',
      margins: { top: 20, right: 20, bottom: 20, left: 20 },
      fontSize: 12,
      fontFamily: 'Arial',
      primaryColor: '#1890ff',
      secondaryColor: '#fff3cd',
      borderColor: '#d9d9d9',
      backgroundColor: '#ffffff',
      textColor: '#000000',
      showLogo: true,
      showHeader: true,
      showFooter: true,
      showSignature: false,
      showTerms: true,
      showQR: false,
      quality: 'high',
      copies: 1,
      watermark: false,
      watermarkText: 'ZET.IA',
      watermarkOpacity: 0.1,
      // إعدادات التنسيق المتقدمة
      lineSpacing: 1.5,
      borderWidth: 1,
      headerSize: 18,
      sectionSpacing: 15,
      tableWidth: 100
    }
  }
  
  public static getInstance(): MasterPrintService {
    if (!MasterPrintService.instance) {
      MasterPrintService.instance = new MasterPrintService()
      // تحميل معلومات الشركة تلقائياً
      MasterPrintService.instance.loadCompanyInfo().catch(error => {
        Logger.error('MasterPrintService', 'فشل في التحميل التلقائي لمعلومات الشركة:', error)
      })
    }
    return MasterPrintService.instance
  }

  // ===== إدارة الموارد لمنع تسريب الذاكرة =====

  /**
   * إنشاء timeout مع تتبع
   */
  private createTimeout(callback: () => void, delay: number): NodeJS.Timeout {
    const timeout = setTimeout(() => {
      this.activeTimeouts.delete(timeout)
      callback()
    }, delay)
    this.activeTimeouts.add(timeout)
    return timeout
  }

  /**
   * إنشاء interval مع تتبع
   */
  private createInterval(callback: () => void, delay: number): NodeJS.Timeout {
    const interval = setInterval(callback, delay)
    this.activeIntervals.add(interval)
    return interval
  }

  /**
   * إضافة event listener مع تتبع
   */
  private addEventListenerTracked(
    element: any,
    event: string,
    listener: EventListener,
    options?: boolean | any
  ): void {
    const key = `${event}_${Date.now()}_${Math.random()}`
    element.addEventListener(event, listener, options)
    this.eventListeners.set(key, { element, listener })
  }

  /**
   * تنظيف جميع الموارد
   */
  public cleanup(): void {
    // تنظيف timeouts
    this.activeTimeouts.forEach(timeout => clearTimeout(timeout))
    this.activeTimeouts.clear()

    // تنظيف intervals
    this.activeIntervals.forEach(interval => clearInterval(interval))
    this.activeIntervals.clear()

    // تنظيف event listeners
    this.eventListeners.forEach(({ element, listener }, key) => {
      const eventType = key.split('_')[0]
      element.removeEventListener(eventType, listener)
    })
    this.eventListeners.clear()

    // إغلاق النوافذ المفتوحة
    this.openWindows.forEach(window => {
      if (!window.closed) {
        window.close()
      }
    })
    this.openWindows.clear()

    // إغلاق نوافذ المعاينة المفتوحة
    this.activePreviewModals.forEach((modal, id) => {
      try {
        if (modal && modal.parentNode) {
          modal.parentNode.removeChild(modal)
          Logger.info('MasterPrintService', `🗑️ تم إغلاق نافذة معاينة ${id} أثناء التنظيف`)
        }
      } catch (error) {
        Logger.warn('MasterPrintService', `تعذر إغلاق نافذة معاينة ${id}:`, error)
      }
    })
    this.activePreviewModals.clear()

    Logger.info('MasterPrintService', '🧹 تم تنظيف جميع موارد الطباعة')
  }

  /**
   * إغلاق نوافذ الطباعة المرتبطة بمعاينة معينة
   */
  private closePrintWindowsForPreview(previewId: string): void {
    let closedCount = 0

    // إغلاق جميع نوافذ الطباعة المفتوحة بحذر
    this.openWindows.forEach(window => {
      if (!window.closed) {
        try {
          // التحقق من أن النافذة ما زالت موجودة قبل الإغلاق
          if (window && typeof window.close === 'function') {
            window.close()
            closedCount++
            Logger.info('MasterPrintService', `🗑️ تم إغلاق نافذة طباعة مرتبطة بالمعاينة ${previewId}`)
          }
        } catch (error) {
          // تجاهل الأخطاء إذا كانت النافذة مُغلقة بالفعل
          Logger.debug('MasterPrintService', 'نافذة طباعة مُغلقة بالفعل:', error)
        }
      }
    })

    // تنظيف قائمة النوافذ
    this.openWindows.clear()

    if (closedCount > 0) {
      Logger.info('MasterPrintService', `✅ تم إغلاق ${closedCount} نافذة طباعة للمعاينة ${previewId}`)
    }
  }
  
  /**
   * الحصول على الإعدادات الافتراضية
   */
  public getDefaultOptions(): EnhancedPrintOptions {
    return { ...createDefaultPrintOptions(), ...this.defaultPrintOptions }
  }

  /**
   * تحديث الإعدادات (alias لـ updateDefaultSettings)
   */
  public updateSettings(settings: Partial<EnhancedPrintOptions>): void {
    this.updateDefaultSettings(settings)
  }

  /**
   * تحديث معلومات الشركة
   */
  public updateCompanyInfo(info: Partial<EnhancedPrintData['company']>): void {
    this.companyInfo = { ...this.companyInfo, ...info }
  }

  /**
   * تحميل معلومات الشركة والشعار من قاعدة البيانات
   */
  public async loadCompanyInfo(): Promise<void> {
    try {
      Logger.info('MasterPrintService', 'بدء تحميل معلومات الشركة...')
      if (window.electronAPI) {
        // تحميل الشعار
        const logoResult = await window.electronAPI.getCompanyLogo()
        if (logoResult?.success && logoResult.logoPath) {
          this.companyInfo.logo = logoResult.logoPath
          Logger.info('MasterPrintService', `تم تحميل شعار الشركة للطباعة: ${logoResult.logoPath}`)
        } else {
          Logger.warn('MasterPrintService', 'فشل في تحميل شعار الشركة أو الشعار غير موجود')
        }

        // تحميل معلومات الشركة الأخرى
        const settingsResult = await window.electronAPI.getSettings()
        if (settingsResult?.success && settingsResult.data) {
          const settings = settingsResult.data
          this.companyInfo = {
            ...this.companyInfo,
            name: settings.company_name || this.companyInfo.name,
            nameEn: settings.company_name_en || this.companyInfo.nameEn,
            address: settings.company_address || this.companyInfo.address,
            phone: settings.company_phone || this.companyInfo.phone,
            email: settings.company_email || this.companyInfo.email,
            website: settings.company_website || this.companyInfo.website,
            taxNumber: settings.company_tax_number || this.companyInfo.taxNumber
          }
          Logger.info('MasterPrintService', 'تم تحميل معلومات الشركة للطباعة')
        }
      }
    } catch (error) {
      Logger.error('MasterPrintService', 'خطأ في تحميل معلومات الشركة:', error)
    }
  }

  /**
   * تحديث الإعدادات الافتراضية للطباعة
   */
  public updateDefaultSettings(settings: Partial<EnhancedPrintOptions>): void {
    this.defaultPrintOptions = { ...this.defaultPrintOptions, ...settings }

    // تطبيق الألوان على متغيرات CSS العامة للمعاينة
    if (typeof document !== 'undefined') {
      const root = document.documentElement
      if (settings.primaryColor) {
        root.style.setProperty('--print-primary-color', settings.primaryColor)
      }
      if (settings.secondaryColor) {
        root.style.setProperty('--print-secondary-color', settings.secondaryColor)
      }
      if (settings.borderColor) {
        root.style.setProperty('--print-border-color', settings.borderColor)
      }
      if (settings.backgroundColor) {
        root.style.setProperty('--print-background-color', settings.backgroundColor)
      }
      if (settings.textColor) {
        root.style.setProperty('--print-text-color', settings.textColor)
      }

      // تطبيق إعدادات الشعار على متغيرات CSS للتنسيق التلقائي
      if (settings.logoSize) {
        const dimensions = this.getLogoDimensions(settings.logoSize)
        root.style.setProperty('--print-logo-width', `${dimensions.width}px`)
        root.style.setProperty('--print-logo-height', `${dimensions.height}px`)
        root.style.setProperty('--print-logo-size', typeof settings.logoSize === 'number' ? `${settings.logoSize}px` : settings.logoSize)
      }

      if (settings.logoPosition) {
        root.style.setProperty('--print-logo-position', settings.logoPosition)
      }

      if (settings.showLogo !== undefined) {
        root.style.setProperty('--print-show-logo', settings.showLogo ? 'block' : 'none')
      }
    }

    Logger.info('MasterPrintService', 'تم تحديث الإعدادات الافتراضية مع الألوان:', this.defaultPrintOptions)
  }
  
  /**
   * الطباعة الرئيسية
   */
  public async print(data: EnhancedPrintData, options: EnhancedPrintOptions): Promise<void> {
    try {
      // التحقق من صحة البيانات
      const validation = this.validatePrintData(data)
      if (!validation.isValid) {
        const errorMessage = `بيانات غير صحيحة: ${validation.errors.join(', ')}`
        Logger.error('MasterPrintService', errorMessage)
        options.onError?.(errorMessage)
        return
      }

      // تحميل معلومات الشركة المحدثة قبل الطباعة
      await this.loadCompanyInfo()

      // دمج معلومات الشركة
      const printData: EnhancedPrintData = {
        ...data,
        company: { ...this.companyInfo, ...data.company }
      }
      
      // الحصول على إعدادات القالب المخصص
      const templateSettings = options.subType ? await this.getReportTemplateSettings(options.subType) : {}

      // إعدادات افتراضية مدمجة مع الإعدادات المحفوظة
      const defaultOptions: EnhancedPrintOptions = {
        type: 'invoice',
        pageSize: 'A4',
        orientation: 'portrait',
        margins: { top: 20, right: 20, bottom: 20, left: 20 },
        showLogo: true,
        showHeader: true,
        showFooter: true,
        showSignature: false,
        showTerms: true,
        fontSize: 12,
        fontFamily: 'Arial, sans-serif',
        primaryColor: '#1890ff',
        secondaryColor: '#f0f0f0',
        borderColor: '#d9d9d9',
        copies: 1,
        preview: false,
        autoClose: true,
        ...this.defaultPrintOptions, // استخدام الإعدادات المحفوظة
        ...templateSettings, // تطبيق إعدادات القالب المخصص
        ...options // الإعدادات المرسلة مع الطلب لها الأولوية
      }
      
      // إنشاء HTML للطباعة
      const html = await this.generateHTML(printData, defaultOptions)

      if (defaultOptions.preview) {
        // استخدام المعاينة المحسنة
        this.showPrintPreviewModal(html, printData, defaultOptions)
      } else {
        // طباعة مباشرة محسنة
        this.directPrint(html, defaultOptions)
      }
      
      options.onSuccess?.()
      
    } catch (error) {
      Logger.error('MasterPrintService', 'خطأ في الطباعة:', error)
      options.onError?.(error instanceof Error ? error.message : 'خطأ غير معروف')
    }
  }
  
  /**
   * إنشاء HTML للطباعة
   */
  public async generateHTML(data: EnhancedPrintData, options: EnhancedPrintOptions): Promise<string> {
    const css = this.generateCSS(options)
    const body = await this.generateBody(data, options)
    
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${data.title || 'مستند'}</title>
        <style>${css}</style>
      </head>
      <body>
        ${body}
      </body>
      </html>
    `
  }
  
  /**
   * إنشاء CSS للطباعة
   */
  private generateCSS(options: EnhancedPrintOptions): string {
    const margins = options.margins || { top: 20, right: 20, bottom: 20, left: 20 }
    const fontSize = options.fontSize || 12
    const fontFamily = options.fontFamily || 'Arial'
    const primaryColor = options.primaryColor || '#1890ff'
    const secondaryColor = options.secondaryColor || '#52c41a'
    const borderColor = options.borderColor || '#d9d9d9'
    const backgroundColor = options.backgroundColor || '#ffffff'
    const textColor = options.textColor || '#000000'

    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: ${fontFamily}, Arial, sans-serif;
        font-size: ${fontSize}px;
        line-height: 1.6;
        color: ${textColor};
        background: ${backgroundColor};
        direction: rtl;
        text-align: right;
      }

      .print-container {
        max-width: 100%;
        margin: 0 auto;
        padding: ${margins.top}px ${margins.right}px ${margins.bottom}px ${margins.left}px;
        position: relative;
      }

      /* العلامة المائية */
      ${options.watermark ? `
      .watermark {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-45deg);
        font-size: ${fontSize * 4}px;
        color: ${primaryColor};
        opacity: ${options.watermarkOpacity || 0.1};
        z-index: -1;
        pointer-events: none;
        font-weight: bold;
        white-space: nowrap;
      }
      ` : ''}

      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid ${primaryColor};
      }

      .logo {
        max-width: ${options.logoSize === 'small' ? '60px' : options.logoSize === 'large' ? '120px' : options.logoSize === 'extra-large' ? '160px' : '90px'} !important;
        max-height: ${options.logoSize === 'small' ? '40px' : options.logoSize === 'large' ? '80px' : options.logoSize === 'extra-large' ? '120px' : '60px'} !important;
        width: auto !important;
        height: auto !important;
        object-fit: contain !important;
        display: block !important;
        margin: 0 !important;
        border: none !important;
        background: transparent !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
      }

      /* تنسيق تلقائي للشعار حسب الموقع */
      .logo-container {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        min-height: ${options.logoSize === 'small' ? '50px' : options.logoSize === 'large' ? '100px' : options.logoSize === 'extra-large' ? '140px' : '70px'} !important;
        overflow: hidden !important;
      }

      .logo-container.top-left {
        justify-content: flex-start !important;
      }

      .logo-container.top-right {
        justify-content: flex-end !important;
      }

      .logo-container.top-center {
        justify-content: center !important;
      }

      .company-info {
        text-align: center;
        flex: 1;
        margin: 0 20px;
      }

      .company-name {
        font-size: ${fontSize + 6}px;
        font-weight: bold;
        color: ${primaryColor};
        margin-bottom: 5px;
      }

      .company-details {
        font-size: ${fontSize - 1}px;
        color: #666;
      }
      
      .document-info {
        text-align: left;
        min-width: 200px;
      }
      
      .document-title {
        font-size: ${(options.fontSize || 12) + 4}px;
        font-weight: bold;
        color: ${options.primaryColor};
        margin-bottom: 10px;
      }
      
      .document-details {
        background: ${options.secondaryColor};
        padding: 10px;
        border-radius: 5px;
      }
      
      .customer-section {
        margin: 20px 0;
        padding: 15px;
        background: #f9f9f9;
        border-radius: 5px;
      }
      
      .section-title {
        font-weight: bold;
        color: ${options.primaryColor};
        margin-bottom: 10px;
        font-size: ${(options.fontSize || 12) + 1}px;
      }
      
      .items-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
      }
      
      .items-table th,
      .items-table td {
        border: 1px solid ${options.borderColor};
        padding: 8px;
        text-align: center;
      }
      
      .items-table th {
        background: ${options.primaryColor};
        color: white;
        font-weight: bold;
      }
      
      .items-table tr:nth-child(even) {
        background: #f9f9f9;
      }
      
      .totals-section {
        margin-top: 20px;
        text-align: left;
      }
      
      .totals-table {
        margin-right: auto;
        border-collapse: collapse;
        min-width: 300px;
      }
      
      .totals-table td {
        padding: 8px 15px;
        border: 1px solid ${options.borderColor};
      }
      
      .totals-table .label {
        background: ${options.secondaryColor};
        font-weight: bold;
        text-align: right;
      }
      
      .totals-table .total-row {
        background: ${options.primaryColor};
        color: white;
        font-weight: bold;
        font-size: ${(options.fontSize || 12) + 1}px;
      }
      
      .footer {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid ${options.borderColor};
        text-align: center;
        font-size: ${(options.fontSize || 12) - 1}px;
        color: #666;
      }
      
      .signature-section {
        margin-top: 40px;
        display: flex;
        justify-content: space-between;
      }
      
      .signature-box {
        width: 200px;
        height: 80px;
        border: 1px solid ${options.borderColor};
        text-align: center;
        padding-top: 60px;
        font-size: ${(options.fontSize || 12) - 1}px;
      }
      
      /* CSS خاص بطباعة الصور */
      .images-section {
        margin: 20px 0;
      }

      .single-image-page {
        min-height: 80vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .image-container img {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border-radius: 8px;
      }

      .image-metadata {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        max-width: 600px;
        margin: 0 auto;
      }

      .images-grid {
        display: grid;
        gap: 20px;
        margin: 20px 0;
      }

      .grid-image-item {
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .grid-image-item img {
        transition: none;
      }

      .images-list {
        margin: 20px 0;
      }

      .list-image-item {
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 15px;
      }

      .image-thumbnail img {
        border: 1px solid #ddd;
      }

      @media print {
        body {
          margin: 0;
          padding: 0;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
          color-adjust: exact !important;
        }

        .print-container {
          margin: 0;
          padding: ${margins.top}px ${margins.right}px ${margins.bottom}px ${margins.left}px;
        }

        @page {
          size: ${options.pageSize} ${options.orientation};
          margin: 0;
        }

        /* التأكد من طباعة الألوان بقوة */
        *, *::before, *::after {
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
          color-adjust: exact !important;
        }

        /* تطبيق الألوان المخصصة في الطباعة */
        .header {
          border-bottom-color: ${primaryColor} !important;
        }

        .company-name {
          color: ${primaryColor} !important;
        }

        .document-title {
          color: ${primaryColor} !important;
        }

        .section-title {
          color: ${primaryColor} !important;
        }

        .items-table th {
          background-color: ${primaryColor} !important;
          color: white !important;
        }

        .items-table td,
        .items-table th {
          border-color: ${borderColor} !important;
        }

        .totals-table .label {
          background-color: ${secondaryColor} !important;
        }

        .totals-table .total-row {
          background-color: ${primaryColor} !important;
          color: white !important;
        }

        .footer {
          border-top-color: ${borderColor} !important;
        }

        .watermark {
          color: ${primaryColor} !important;
        }

        .single-image-page {
          page-break-after: always;
        }

        .grid-image-item,
        .list-image-item {
          break-inside: avoid;
        }
      }
    `
  }

  /**
   * إنشاء محتوى HTML للطباعة
   */
  private async generateBody(data: EnhancedPrintData, options: EnhancedPrintOptions): Promise<string> {
    let html = '<div class="print-container">'

    // العلامة المائية
    if (options.watermark && options.watermarkText) {
      html += `<div class="watermark">${options.watermarkText}</div>`
    }

    // الهيدر
    if (options.showHeader) {
      html += await this.generateHeader(data, options)
    }

    // معلومات العميل
    if (data.customer) {
      html += this.generateCustomerSection(data.customer)
    }

    // جدول العناصر أو معرض الصور أو جدول التقرير
    if (options.type === 'image' || options.type === 'catalog') {
      if (data.images && data.images.length > 0) {
        html += this.generateImagesSection(data.images, data.imageSettings, options)
      }
    } else if (options.type === 'report' && data.data) {
      // استخدام جدول التقرير المخصص للتقارير
      html += this.generateReportTable(data.data, options, data.columns)
    } else if (data.items && data.items.length > 0) {
      html += this.generateItemsTable(data.items, options)
    }

    // المجاميع أو ملخص التقرير أو الصور (لأوامر الإنتاج)
    if (options.type === 'order' && options.subType === 'work') {
      // لأوامر الإنتاج: عرض الصور المرفقة بدلاً من الملخص
      if (data.images && data.images.length > 0) {
        html += await this.generateProductionOrderImagesSection(data.images, options)
      } else {
        html += this.generateNoImagesMessage()
      }
    } else if (options.type === 'report' && data.summary) {
      html += this.generateReportSummary(data.summary, options)
    } else if (data.subtotal !== undefined || data.total !== undefined) {
      html += this.generateTotalsSection(data, options)
    }

    // الملاحظات
    if (data.notes) {
      html += this.generateNotesSection(data.notes)
    }

    // التوقيع
    if (options.showSignature) {
      html += this.generateSignatureSection()
    }

    // الفوتر
    if (options.showFooter) {
      html += this.generateFooter(data, options)
    }

    html += '</div>'
    return html
  }

  /**
   * إنشاء الهيدر
   */
  private getLogoSize(size: 'small' | 'medium' | 'large' | 'extra-large'): string {
    switch (size) {
      case 'small':
        return '40px'
      case 'medium':
        return '60px'
      case 'large':
        return '80px'
      case 'extra-large':
        return '120px'
      default:
        return '60px'
    }
  }

  private async generateLogoImage(logoPath: string, size: 'small' | 'medium' | 'large' | 'extra-large' | number, position?: string): Promise<string> {
    try {
      Logger.info('MasterPrintService', `محاولة تحميل الشعار: ${logoPath}`)
      let logoSrc = ''

      // التحقق من نوع البيانات
      if (logoPath.startsWith('data:')) {
        // الشعار محول بالفعل إلى base64 من get-company-logo
        logoSrc = logoPath
        Logger.info('MasterPrintService', 'الشعار محول بالفعل إلى base64')
      } else {
        // إذا كان مسار ملف، نحاول تحويله (هذا نادر الحدوث الآن)
        if (window.electronAPI) {
          Logger.info('MasterPrintService', `محاولة تحويل الشعار إلى base64: ${logoPath}`)
          const base64 = await window.electronAPI.readFileAsBase64(logoPath)
          if (base64) {
            Logger.info('MasterPrintService', 'تم تحويل الشعار إلى base64 بنجاح')
            // تحديد نوع الصورة من الامتداد
            const extension = logoPath.toLowerCase().split('.').pop()
            let mimeType = 'image/png'
            switch (extension) {
              case 'jpg':
              case 'jpeg':
                mimeType = 'image/jpeg'
                break
              case 'png':
                mimeType = 'image/png'
                break
              case 'gif':
                mimeType = 'image/gif'
                break
              case 'svg':
                mimeType = 'image/svg+xml'
                break
            }
            logoSrc = `data:${mimeType};base64,${base64}`
          } else {
            Logger.warn('MasterPrintService', '⚠️ فشل في تحويل الشعار إلى base64، سيتم إنشاء شعار افتراضي')
            logoSrc = this.generateDefaultLogo()
          }
        } else {
          Logger.warn('MasterPrintService', '❌ electronAPI غير متوفر لتحويل الشعار، سيتم إنشاء شعار افتراضي')
          logoSrc = this.generateDefaultLogo()
        }
      }

      if (logoSrc) {
        Logger.info('MasterPrintService', 'تم إنشاء HTML للشعار بنجاح')
        // تحديد أبعاد الشعار حسب الحجم المطلوب
        const dimensions = this.getLogoDimensions(size)

        // إنشاء container للشعار مع التنسيق التلقائي
        const containerClass = position ? `logo-container ${position}` : 'logo-container'

        const logoHtml = `
          <div class="${containerClass}">
            <img
              src="${logoSrc}"
              alt="شعار الشركة"
              class="logo"
              style="
                max-width: ${dimensions.width}px !important;
                max-height: ${dimensions.height}px !important;
                width: auto !important;
                height: auto !important;
                object-fit: contain !important;
                display: block !important;
                margin: 0 !important;
                border: none !important;
                background: transparent !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
              "
            >
          </div>
        `
        return logoHtml
      } else {
        Logger.warn('MasterPrintService', 'لم يتم العثور على مصدر صالح للشعار')
      }

      return ''
    } catch (error) {
      Logger.warn('MasterPrintService', 'فشل في تحميل شعار الشركة:', error)
      return ''
    }
  }

  /**
   * الحصول على أبعاد الشعار حسب الحجم
   */
  private getLogoDimensions(size: 'small' | 'medium' | 'large' | 'extra-large' | number): { width: number; height: number } {
    if (typeof size === 'number') {
      return { width: size, height: size * 0.67 } // نسبة عرض إلى ارتفاع 3:2
    }

    switch (size) {
      case 'small':
        return { width: 60, height: 40 }
      case 'medium':
        return { width: 90, height: 60 }
      case 'large':
        return { width: 120, height: 80 }
      case 'extra-large':
        return { width: 160, height: 120 }
      default:
        return { width: 90, height: 60 }
    }
  }

  private async generateHeader(data: EnhancedPrintData, options: EnhancedPrintOptions): Promise<string> {
    const logoPosition = options.logoPosition || 'top-left'
    const primaryColor = options.primaryColor || '#1890ff'
    const borderColor = options.borderColor || '#d9d9d9'

    // نص الهيدر المخصص
    const customHeaderText = options.headerText ? `
      <div style="text-align: center; margin-bottom: 10px; padding: 8px; background-color: #f9f9f9; border-radius: 4px; font-weight: bold; color: ${primaryColor};">
        ${options.headerText}
      </div>
    ` : ''

    // ترتيب الشعار حسب الموقع المحدد مع التنسيق التلقائي
    Logger.info('MasterPrintService', `إعدادات الشعار: showLogo=${options.showLogo}, logoPath=${data.company?.logo}`)
    const logoHtml = options.showLogo && data.company?.logo ?
      await this.generateLogoImage(data.company.logo, options.logoSize || 'medium', logoPosition) : ''
    Logger.info('MasterPrintService', `HTML الشعار المُنشأ: ${logoHtml ? 'تم إنشاؤه بنجاح' : 'فارغ'}`)

    let headerLayout = ''

    if (logoPosition === 'top-center') {
      headerLayout = `
        <div style="text-align: center; margin-bottom: 15px;">
          ${logoHtml}
        </div>
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 15px;">
          <div class="company-info" style="flex: 1;">
            <div class="company-name" style="font-weight: bold; font-size: 16px; color: ${primaryColor}; margin-bottom: 3px;">${data.company?.name || 'اسم الشركة'}</div>
            <div class="company-details" style="font-size: 11px; color: #666; line-height: 1.3;">
              ${data.company?.address || ''}<br>
              ${data.company?.phone ? `هاتف: ${data.company.phone}` : ''} ${data.company?.email ? `| إيميل: ${data.company.email}` : ''}
            </div>
          </div>
          <div class="document-info" style="text-align: left; flex-shrink: 0;">
            <div class="document-title" style="font-weight: bold; font-size: 18px; color: ${primaryColor}; margin-bottom: 5px;">${data.title || this.getDocumentTitle(options.type)}</div>
            <div class="document-details" style="font-size: 12px; line-height: 1.4;">
              <div><strong>رقم الفاتورة:</strong> ${data.number || data.id || '---'}</div>
              <div><strong>تاريخ الإصدار:</strong> ${data.date || new Date().toLocaleDateString('en-GB')}</div>
              ${data.dueDate ? `<div><strong>تاريخ الاستحقاق:</strong> ${data.dueDate}</div>` : ''}
            </div>
          </div>
        </div>
      `
    } else if (logoPosition === 'top-right') {
      headerLayout = `
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 15px;">
          <div class="company-info" style="flex: 1;">
            <div class="company-name" style="font-weight: bold; font-size: 16px; color: ${primaryColor}; margin-bottom: 3px;">${data.company?.name || 'اسم الشركة'}</div>
            <div class="company-details" style="font-size: 11px; color: #666; line-height: 1.3;">
              ${data.company?.address || ''}<br>
              ${data.company?.phone ? `هاتف: ${data.company.phone}` : ''} ${data.company?.email ? `| إيميل: ${data.company.email}` : ''}
            </div>
          </div>
          <div style="display: flex; align-items: center; gap: 10px;">
            ${logoHtml}
            <div class="document-info" style="text-align: left;">
              <div class="document-title" style="font-weight: bold; font-size: 18px; color: ${primaryColor}; margin-bottom: 5px;">${data.title || this.getDocumentTitle(options.type)}</div>
              <div class="document-details" style="font-size: 12px; line-height: 1.4;">
                <div><strong>رقم الفاتورة:</strong> ${data.number || data.id || '---'}</div>
                <div><strong>تاريخ الإصدار:</strong> ${data.date || new Date().toLocaleDateString('en-GB')}</div>
                ${data.dueDate ? `<div><strong>تاريخ الاستحقاق:</strong> ${data.dueDate}</div>` : ''}
              </div>
            </div>
          </div>
        </div>
      `
    } else { // top-left (default)
      headerLayout = `
        <div style="display: flex; justify-content: space-between; align-items: flex-start; gap: 15px;">
          <div style="display: flex; align-items: center; gap: 10px; flex: 1;">
            ${logoHtml}
            <div class="company-info">
              <div class="company-name" style="font-weight: bold; font-size: 16px; color: ${primaryColor}; margin-bottom: 3px;">${data.company?.name || 'اسم الشركة'}</div>
              <div class="company-details" style="font-size: 11px; color: #666; line-height: 1.3;">
                ${data.company?.address || ''}<br>
                ${data.company?.phone ? `هاتف: ${data.company.phone}` : ''} ${data.company?.email ? `| إيميل: ${data.company.email}` : ''}
              </div>
            </div>
          </div>
          <div class="document-info" style="text-align: left; flex-shrink: 0;">
            <div class="document-title" style="font-weight: bold; font-size: 18px; color: ${primaryColor}; margin-bottom: 5px;">${data.title || this.getDocumentTitle(options.type)}</div>
            <div class="document-details" style="font-size: 12px; line-height: 1.4;">
              <div><strong>رقم الفاتورة:</strong> ${data.number || data.id || '---'}</div>
              <div><strong>تاريخ الإصدار:</strong> ${data.date || new Date().toLocaleDateString('en-GB')}</div>
              ${data.dueDate ? `<div><strong>تاريخ الاستحقاق:</strong> ${data.dueDate}</div>` : ''}
            </div>
          </div>
        </div>
      `
    }

    return `
      ${customHeaderText}
      <div class="header" style="margin-bottom: 10px; padding: 8px 0; border-bottom: 1px solid ${borderColor};">
        ${headerLayout}
      </div>
    `
  }

  /**
   * إنشاء قسم العميل - سطر واحد أفقي
   */
  private generateCustomerSection(customer: EnhancedPrintData['customer']): string {
    if (!customer) return ''

    // تجميع المعلومات في سطر واحد
    const customerInfo = []

    if (customer.name) {
      customerInfo.push(`<strong>العميل:</strong> ${customer.name}`)
    }

    if ((customer as any).department) {
      customerInfo.push(`<strong>القسم:</strong> ${(customer as any).department}`)
    }

    if ((customer as any).orderDate) {
      customerInfo.push(`<strong>تاريخ الأمر:</strong> ${(customer as any).orderDate}`)
    }

    if ((customer as any).deliveryDate) {
      customerInfo.push(`<strong>تاريخ التسليم:</strong> ${(customer as any).deliveryDate}`)
    }

    if (customer.phone) {
      customerInfo.push(`<strong>الهاتف:</strong> ${customer.phone}`)
    }

    if (customer.address) {
      customerInfo.push(`<strong>العنوان:</strong> ${customer.address}`)
    }

    return `
      <div class="customer-section" style="
        margin: 8px 0;
        padding: 8px 12px;
        border: 1px solid #1890ff;
        border-radius: 4px;
        background: #f0f8ff;
        font-size: 12px;
        text-align: center;
        font-weight: 500;
      ">
        ${customerInfo.join(' | ')}
      </div>
    `
  }

  /**
   * إنشاء جدول مخصص للتقارير حسب النوع - محسن
   */
  private generateReportTable(data: any[], options: EnhancedPrintOptions, columns?: any[]): string {
    if (!data || data.length === 0) {
      return `
        <div class="no-data-message" style="text-align: center; padding: 40px; border: 2px dashed #ddd; background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 8px; margin: 20px 0;">
          <div style="font-size: 48px; color: #dee2e6; margin-bottom: 16px;">📊</div>
          <h3 style="margin: 0 0 8px 0; color: #6c757d; font-size: 18px;">لا توجد بيانات لعرضها</h3>
          <p style="margin: 0; color: #adb5bd; font-size: 14px;">يرجى التحقق من الفلاتر المطبقة أو إضافة بيانات جديدة</p>
        </div>
      `
    }

    // تحديد الأعمدة حسب نوع التقرير
    const reportColumns = columns || this.getReportColumns(options.subType || '', data)
    const tableId = `report-table-${Date.now()}`

    let html = `
      <div class="report-table-section" style="margin: 20px 0;">
        <table id="${tableId}" class="report-table" style="
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          font-size: ${options.fontSize || 11}px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          border-radius: 8px;
          overflow: hidden;
          background: white;
        ">
          <thead>
            <tr style="
              background: linear-gradient(135deg, ${options.primaryColor || '#1890ff'}, ${this.lightenColor(options.primaryColor || '#1890ff', 20)});
              color: white;
              font-weight: 600;
            ">
    `

    // إنشاء رؤوس الأعمدة المحسنة
    reportColumns.forEach((column, index) => {
      const width = column.width || 'auto'
      const isFirstColumn = index === 0
      const isLastColumn = index === reportColumns.length - 1

      html += `
        <th style="
          border: 1px solid rgba(255,255,255,0.2);
          padding: 12px 8px;
          text-align: center;
          font-weight: 600;
          font-size: ${(options.fontSize || 11) + 1}px;
          width: ${width};
          ${isFirstColumn ? 'border-top-left-radius: 8px;' : ''}
          ${isLastColumn ? 'border-top-right-radius: 8px;' : ''}
          position: relative;
          text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        ">
          ${column.title}
          ${column.sortable ? '<span style="margin-right: 4px; opacity: 0.7;">⇅</span>' : ''}
        </th>
      `
    })

    html += `
            </tr>
          </thead>
          <tbody>
    `

    // إنشاء صفوف البيانات المحسنة
    data.forEach((row, index) => {
      const isEvenRow = index % 2 === 0
      const rowStyle = isEvenRow
        ? 'background-color: #fafbfc;'
        : 'background-color: white;'

      html += `<tr style="${rowStyle}">`

      reportColumns.forEach((column) => {
        const value = this.formatCellValue(row[column.key], column.format)
        const align = column.align || 'center'
        const isNumeric = column.format === 'currency' || column.format === 'number'

        html += `
          <td style="
            border: 1px solid #e8e8e8;
            padding: 10px 8px;
            text-align: ${align};
            font-size: ${options.fontSize || 11}px;
            ${isNumeric ? 'font-weight: 500;' : ''}
            ${column.format === 'currency' ? 'color: #52c41a;' : ''}
            vertical-align: middle;
          ">
            ${value}
          </td>
        `
      })

      html += '</tr>'
    })

    html += `
          </tbody>
        </table>
      </div>
    `

    return html
  }

  /**
   * إنشاء قسم الصور المرفقة لأمر الإنتاج - محسن
   */
  private async generateProductionOrderImagesSection(images: any[], options: EnhancedPrintOptions): Promise<string> {
    if (!images || images.length === 0) {
      return this.generateNoImagesMessage()
    }

    let html = `
      <div class="production-images-section" style="
        margin: 24px 0;
        padding: 20px;
        border: 2px solid ${options.primaryColor || '#1890ff'};
        border-radius: 8px;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      ">
        <h3 style="
          color: ${options.primaryColor || '#1890ff'};
          font-size: ${(options.fontSize || 11) + 4}px;
          font-weight: 600;
          text-align: center;
          margin: 0 0 20px 0;
          border-bottom: 2px solid ${options.primaryColor || '#1890ff'};
          padding-bottom: 12px;
        ">📷 الصور المرفقة لأمر الإنتاج</h3>

        <div class="images-grid" style="
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
          margin-top: 16px;
        ">
    `

    // معالجة الصور بشكل متوازي
    const processedImages = await Promise.all(images.map(async (image, index) => {
      const imageName = image.name || image.image_name || `صورة ${index + 1}`
      const imageDescription = image.description || image.category || ''
      const imageSize = image.size || image.file_size ? this.formatFileSize(image.size || image.file_size) : ''

      // تحويل الصورة إلى base64 إذا لم تكن محولة بالفعل
      let imageSrc = image.path || image.image_path

      if (imageSrc && !imageSrc.startsWith('data:')) {
        try {
          Logger.info('MasterPrintService', `🔄 محاولة تحويل صورة أمر الإنتاج إلى base64: ${imageName}`)
          Logger.debug('MasterPrintService', `مسار الصورة الأصلي: ${imageSrc}`)

          if (window.electronAPI) {
            const base64 = await window.electronAPI.readFileAsBase64(imageSrc)
            if (base64) {
              // تحديد نوع الصورة من الامتداد أو المحتوى
              let mimeType = 'image/png'

              // محاولة تحديد النوع من الامتداد
              const extension = imageSrc.toLowerCase().split('.').pop()
              switch (extension) {
                case 'jpg':
                case 'jpeg':
                  mimeType = 'image/jpeg'
                  break
                case 'png':
                  mimeType = 'image/png'
                  break
                case 'gif':
                  mimeType = 'image/gif'
                  break
                case 'svg':
                  mimeType = 'image/svg+xml'
                  break
                case 'webp':
                  mimeType = 'image/webp'
                  break
                default:
                  // محاولة تحديد النوع من بداية البيانات
                  if (base64.startsWith('/9j/')) mimeType = 'image/jpeg'
                  else if (base64.startsWith('iVBORw0KGgo')) mimeType = 'image/png'
                  else if (base64.startsWith('R0lGODlh')) mimeType = 'image/gif'
                  break
              }

              imageSrc = `data:${mimeType};base64,${base64}`
              Logger.info('MasterPrintService', `✅ تم تحويل صورة أمر الإنتاج بنجاح: ${imageName} (${mimeType})`)
            } else {
              Logger.warn('MasterPrintService', `⚠️ فشل في تحويل صورة أمر الإنتاج: ${imageName}`)
              // إنشاء صورة بديلة
              imageSrc = this.generateFallbackImage(imageName, 200, 150)
              Logger.info('MasterPrintService', `🔄 تم إنشاء صورة بديلة لـ: ${imageName}`)
            }
          } else {
            Logger.error('MasterPrintService', 'electronAPI غير متوفر')
            imageSrc = this.generateFallbackImage(imageName, 200, 150)
          }
        } catch (error) {
          Logger.error('MasterPrintService', `❌ خطأ في تحويل صورة أمر الإنتاج ${imageName}:`, error)
          // إنشاء صورة بديلة عند الخطأ
          imageSrc = this.generateFallbackImage(imageName, 200, 150)
          Logger.info('MasterPrintService', `🔄 تم إنشاء صورة بديلة بسبب الخطأ لـ: ${imageName}`)
        }
      }

      return {
        imageName,
        imageDescription,
        imageSize,
        imageSrc
      }
    }))

    // إنشاء HTML للصور
    processedImages.forEach((processedImage) => {
      const { imageName, imageDescription, imageSize, imageSrc } = processedImage

      html += `
        <div class="image-card" style="
          border: 1px solid #e8e8e8;
          border-radius: 8px;
          padding: 12px;
          background: white;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          text-align: center;
        ">
          <div class="image-container" style="
            width: 100%;
            height: 150px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            margin-bottom: 8px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
          ">
            ${imageSrc ? `
              <img src="${imageSrc}" alt="${imageName}" style="
                max-width: 100%;
                max-height: 100%;
                object-fit: cover;
                border-radius: 4px;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
              " onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
              <div style="
                display: none;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                color: #999;
                font-size: 12px;
              ">
                <span style="font-size: 24px; margin-bottom: 4px;">🖼️</span>
                <span>تعذر تحميل الصورة</span>
              </div>
            ` : `
              <div style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                color: #999;
                font-size: 12px;
              ">
                <span style="font-size: 24px; margin-bottom: 4px;">🖼️</span>
                <span>صورة غير متوفرة</span>
              </div>
            `}
          </div>

          <div class="image-info" style="
            font-size: ${(options.fontSize || 11) - 1}px;
            color: #333;
          ">
            <div style="
              font-weight: 600;
              margin-bottom: 4px;
              color: ${options.primaryColor || '#1890ff'};
            ">${imageName}</div>

            ${imageDescription ? `
              <div style="
                color: #666;
                margin-bottom: 4px;
                font-size: ${(options.fontSize || 11) - 2}px;
              ">${imageDescription}</div>
            ` : ''}

            ${imageSize ? `
              <div style="
                color: #999;
                font-size: ${(options.fontSize || 11) - 2}px;
              ">${imageSize}</div>
            ` : ''}
          </div>
        </div>
      `
    })

    html += `
        </div>

        <div class="images-summary" style="
          margin-top: 16px;
          padding: 12px;
          background: ${options.primaryColor || '#1890ff'}15;
          border-radius: 6px;
          text-align: center;
          font-size: ${(options.fontSize || 11) - 1}px;
          color: #666;
        ">
          📊 إجمالي الصور المرفقة: <strong style="color: ${options.primaryColor || '#1890ff'};">${images.length}</strong>
        </div>
      </div>
    `

    return html
  }

  /**
   * إنشاء رسالة عدم وجود صور
   */
  private generateNoImagesMessage(): string {
    return `
      <div class="no-images-section" style="
        margin: 24px 0;
        padding: 40px 20px;
        border: 2px dashed #d9d9d9;
        border-radius: 8px;
        background: #fafafa;
        text-align: center;
      ">
        <div style="
          font-size: 48px;
          color: #d9d9d9;
          margin-bottom: 16px;
        ">📷</div>

        <h3 style="
          color: #999;
          font-size: 16px;
          font-weight: 500;
          margin: 0 0 8px 0;
        ">لا يوجد صور مرفقة</h3>

        <p style="
          color: #bbb;
          font-size: 14px;
          margin: 0;
        ">لم يتم إرفاق أي صور مع أمر الإنتاج هذا</p>
      </div>
    `
  }

  /**
   * تنسيق حجم الملف
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 بايت'

    const k = 1024
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * إنشاء ملخص محسن للتقرير
   */
  private generateReportSummary(summary: any, options: EnhancedPrintOptions): string {
    if (!summary || Object.keys(summary).length === 0) {
      return ''
    }

    const summaryItems = Object.entries(summary).filter(([key, value]) =>
      value !== null && value !== undefined && key !== 'metadata'
    )

    if (summaryItems.length === 0) {
      return ''
    }

    let html = `
      <div class="report-summary" style="
        background: linear-gradient(135deg, #f0f8ff, #e6f7ff);
        border: 2px solid ${options.primaryColor || '#1890ff'};
        border-radius: 12px;
        padding: 24px;
        margin: 24px 0;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      ">
        <h3 style="
          margin: 0 0 20px 0;
          color: ${options.primaryColor || '#1890ff'};
          font-size: ${(options.fontSize || 11) + 4}px;
          font-weight: 600;
          text-align: center;
          border-bottom: 2px solid ${options.primaryColor || '#1890ff'};
          padding-bottom: 12px;
        ">📊 ملخص التقرير</h3>

        <div class="summary-grid" style="
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
          margin-top: 20px;
        ">
    `

    summaryItems.forEach(([key, value]) => {
      const formattedValue = this.formatSummaryValue(key, value)
      const icon = this.getSummaryIcon(key)

      html += `
        <div class="summary-item" style="
          background: white;
          border: 1px solid #e8e8e8;
          border-radius: 8px;
          padding: 16px;
          text-align: center;
          box-shadow: 0 2px 4px rgba(0,0,0,0.05);
          transition: transform 0.2s ease;
        ">
          <div style="font-size: 24px; margin-bottom: 8px;">${icon}</div>
          <div class="summary-label" style="
            font-size: ${(options.fontSize || 11) - 1}px;
            color: #666;
            margin-bottom: 4px;
            font-weight: 500;
          ">${this.formatSummaryLabel(key)}</div>
          <div class="summary-value" style="
            font-size: ${(options.fontSize || 11) + 3}px;
            font-weight: 700;
            color: ${this.getSummaryValueColor(key, value)};
          ">${formattedValue}</div>
        </div>
      `
    })

    html += `
        </div>
      </div>
    `

    return html
  }

  /**
   * تنسيق قيمة الملخص
   */
  private formatSummaryValue(key: string, value: any): string {
    if (typeof value === 'number') {
      if (key.includes('amount') || key.includes('total') || key.includes('value')) {
        return this.formatCurrency(value)
      }
      if (key.includes('percentage') || key.includes('percent')) {
        return `${value.toFixed(1)}%`
      }
      return value.toLocaleString('en-US')
    }
    return String(value)
  }

  /**
   * الحصول على أيقونة الملخص
   */
  private getSummaryIcon(key: string): string {
    const iconMap: { [key: string]: string } = {
      totalItems: '📦',
      totalRecords: '📋',
      totalAmount: '💰',
      totalValue: '💎',
      totalQuantity: '📊',
      totalCustomers: '👥',
      totalSuppliers: '🏢',
      totalOrders: '📝',
      totalInvoices: '🧾',
      averageValue: '📈',
      percentage: '📊',
      count: '🔢'
    }

    // البحث عن مطابقة جزئية
    for (const [pattern, icon] of Object.entries(iconMap)) {
      if (key.toLowerCase().includes(pattern.toLowerCase())) {
        return icon
      }
    }

    return '📊' // أيقونة افتراضية
  }

  /**
   * تنسيق تسمية الملخص
   */
  private formatSummaryLabel(key: string): string {
    const labelMap: { [key: string]: string } = {
      totalItems: 'إجمالي الأصناف',
      totalRecords: 'إجمالي السجلات',
      totalAmount: 'إجمالي المبلغ',
      totalValue: 'إجمالي القيمة',
      totalQuantity: 'إجمالي الكمية',
      totalCustomers: 'إجمالي العملاء',
      totalSuppliers: 'إجمالي الموردين',
      totalOrders: 'إجمالي الطلبات',
      totalInvoices: 'إجمالي الفواتير',
      averageValue: 'متوسط القيمة',
      percentage: 'النسبة المئوية',
      count: 'العدد'
    }

    return labelMap[key] || key.replace(/([A-Z])/g, ' $1').trim()
  }

  /**
   * الحصول على لون قيمة الملخص
   */
  private getSummaryValueColor(key: string, value: any): string {
    if (typeof value === 'number') {
      if (key.includes('amount') || key.includes('total') || key.includes('value')) {
        return value >= 0 ? '#52c41a' : '#ff4d4f'
      }
      if (key.includes('percentage') && value < 50) {
        return '#faad14'
      }
    }
    return '#1890ff'
  }

  /**
   * إنشاء جدول العناصر
   */
  private generateItemsTable(items: EnhancedPrintData['items'], options: EnhancedPrintOptions): string {
    if (!items || items.length === 0) {
      return `
        <div class="no-items-message" style="text-align: center; padding: 20px; border: 1px solid #ddd; background: #f9f9f9;">
          <p style="margin: 0; color: #666;">لا توجد أصناف لعرضها</p>
        </div>
      `
    }

    // التحقق من وجود خصومات أو ضرائب
    const hasDiscount = items.some(item => item.discount && item.discount > 0)
    const hasTax = items.some(item => item.tax && item.tax > 0)

    let html = `
      <div class="items-section">
        <h3 style="margin-bottom: 15px; color: ${options.primaryColor || '#1890ff'};">تفاصيل الأصناف</h3>
        <table class="items-table" style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <thead>
            <tr style="background-color: ${options.secondaryColor || '#f0f0f0'};">
              <th style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 6px 4px; text-align: center; font-weight: bold; font-size: 12px;">م</th>
              <th style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 6px 4px; text-align: center; font-weight: bold; min-width: 200px; font-size: 12px;">الصنف</th>
              <th style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 6px 4px; text-align: center; font-weight: bold; font-size: 12px;">الكمية</th>
              <th style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 6px 4px; text-align: center; font-weight: bold; font-size: 12px;">الوحدة</th>
              <th style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 6px 4px; text-align: center; font-weight: bold; font-size: 12px;">سعر الوحدة</th>
              ${hasDiscount ? `<th style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 6px 4px; text-align: center; font-weight: bold; font-size: 12px;">الخصم</th>` : ''}
              ${hasTax ? `<th style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 6px 4px; text-align: center; font-weight: bold; font-size: 12px;">الضريبة</th>` : ''}
              <th style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 6px 4px; text-align: center; font-weight: bold; font-size: 12px;">المجموع الفرعي</th>
            </tr>
          </thead>
          <tbody>
    `

    items.forEach((item, index) => {
      const rowStyle = index % 2 === 0 ? 'background-color: #fafafa;' : 'background-color: white;'

      html += `
        <tr style="${rowStyle}">
          <td style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 4px 3px; text-align: center; font-size: 12px;">${index + 1}</td>
          <td style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 4px 6px; text-align: right; direction: rtl;">
            <div style="font-weight: bold; margin-bottom: 2px; font-size: 12px;">${item.name || 'غير محدد'}</div>
            ${item.description ? `<div style="font-size: 10px; color: #666; line-height: 1.2;">${item.description}</div>` : ''}
          </td>
          <td style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 4px 3px; text-align: center; font-weight: bold; font-size: 12px;">${item.quantity || 0}</td>
          <td style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 4px 3px; text-align: center; font-size: 12px;">${item.unit || 'قطعة'}</td>
          <td style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 4px 3px; text-align: left; direction: ltr; font-family: monospace; font-size: 12px;">${this.formatCurrency(item.unitPrice || 0)}</td>
          ${hasDiscount ? `<td style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 4px 3px; text-align: left; direction: ltr; font-family: monospace; font-size: 12px;">${item.discount ? this.formatCurrency(item.discount) : '-'}</td>` : ''}
          ${hasTax ? `<td style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 4px 3px; text-align: left; direction: ltr; font-family: monospace; font-size: 12px;">${item.tax ? this.formatCurrency(item.tax) : '-'}</td>` : ''}
          <td style="border: 1px solid ${options.borderColor || '#d9d9d9'}; padding: 4px 3px; text-align: left; direction: ltr; font-family: monospace; font-weight: bold; background-color: #f0f8ff; font-size: 12px;">${this.formatCurrency(item.total || 0)}</td>
        </tr>
      `
    })

    html += `
          </tbody>
        </table>
      </div>
    `

    return html
  }



  /**
   * إنشاء قسم المجاميع
   */
  private generateTotalsSection(data: EnhancedPrintData, _options: EnhancedPrintOptions): string {
    // حساب المجاميع بشكل صحيح
    const subtotal = data.subtotal || 0
    const discount = data.discount || 0
    const tax = data.tax || 0
    const total = data.total || 0
    const paid = data.paid || 0
    const remaining = data.remaining !== undefined ? data.remaining : (total - paid)

    return `
      <div class="totals-section" style="margin-top: 15px; border-top: 1px solid #ddd; padding-top: 10px;">
        <table class="totals-table" style="width: 100%; max-width: 350px; margin: 0 auto; border-collapse: collapse; font-size: 13px;">

          <!-- المجموع الفرعي -->
          <tr style="border-bottom: 1px solid #eee;">
            <td class="label" style="padding: 4px 10px; text-align: right; font-weight: bold; background: #f8f9fa;">المجموع الفرعي:</td>
            <td style="padding: 4px 10px; text-align: left; direction: ltr; font-family: monospace;">${this.formatCurrency(subtotal)}</td>
          </tr>

          <!-- الخصم -->
          ${discount > 0 ? `
            <tr style="border-bottom: 1px solid #eee;">
              <td class="label" style="padding: 4px 10px; text-align: right; font-weight: bold; background: #f8f9fa;">الخصم:</td>
              <td style="padding: 4px 10px; text-align: left; direction: ltr; font-family: monospace; color: #52c41a;">- ${this.formatCurrency(discount)}</td>
            </tr>
          ` : ''}

          <!-- الضريبة -->
          ${tax > 0 ? `
            <tr style="border-bottom: 1px solid #eee;">
              <td class="label" style="padding: 4px 10px; text-align: right; font-weight: bold; background: #f8f9fa;">الضريبة:</td>
              <td style="padding: 4px 10px; text-align: left; direction: ltr; font-family: monospace; color: #fa8c16;">+ ${this.formatCurrency(tax)}</td>
            </tr>
          ` : ''}

          <!-- المجموع الكلي -->
          <tr class="total-row" style="border-top: 2px solid #1890ff; border-bottom: 1px solid #1890ff; background: #f0f8ff;">
            <td class="label" style="padding: 6px 10px; text-align: right; font-weight: bold; font-size: 14px; color: #1890ff;">المجموع الكلي:</td>
            <td style="padding: 6px 10px; text-align: left; direction: ltr; font-family: monospace; font-weight: bold; font-size: 14px; color: #1890ff;">${this.formatCurrency(total)}</td>
          </tr>

          <!-- المدفوع -->
          <tr style="border-bottom: 1px solid #eee; background: #f6ffed;">
            <td class="label" style="padding: 4px 10px; text-align: right; font-weight: bold; color: #52c41a;">المدفوع:</td>
            <td style="padding: 4px 10px; text-align: left; direction: ltr; font-family: monospace; font-weight: bold; color: #52c41a;">${this.formatCurrency(paid)}</td>
          </tr>

          <!-- المتبقي -->
          <tr style="background: ${remaining > 0 ? '#fff2f0' : '#f6ffed'};">
            <td class="label" style="padding: 4px 10px; text-align: right; font-weight: bold; color: ${remaining > 0 ? '#ff4d4f' : '#52c41a'};">المتبقي:</td>
            <td style="padding: 4px 10px; text-align: left; direction: ltr; font-family: monospace; font-weight: bold; color: ${remaining > 0 ? '#ff4d4f' : '#52c41a'};">${this.formatCurrency(remaining)}</td>
          </tr>

        </table>
      </div>
    `
  }

  /**
   * إنشاء قسم الملاحظات
   */
  private generateNotesSection(notes: string): string {
    return `
      <div class="notes-section" style="margin: 20px 0; padding: 15px; background: #f9f9f9; border-radius: 5px;">
        <div class="section-title">ملاحظات:</div>
        <div style="white-space: pre-wrap;">${notes}</div>
      </div>
    `
  }

  /**
   * إنشاء قسم الصور (محسن للنظام الجديد)
   */
  private generateImagesSection(
    images: NonNullable<EnhancedPrintData['images']>,
    settings: EnhancedPrintData['imageSettings'],
    options: EnhancedPrintOptions
  ): string {
    const layout = settings?.layout || 'grid'
    const imagesPerPage = settings?.imagesPerPage || 6
    const showMetadata = settings?.showMetadata !== false
    const imageQuality = settings?.imageQuality || 'high'
    const fitToPage = settings?.fitToPage !== false

    let html = '<div class="images-section enhanced-print">'

    // إضافة عنوان القسم مع معلومات محسنة
    if (images.length > 0) {
      html += `<div class="images-header enhanced">
        <h3>الصور المرفقة (${images.length})</h3>
        <div class="images-info">
          <span class="layout-info">تخطيط: ${this.getLayoutText(layout)}</span>
          <span class="quality-info">جودة: ${this.getQualityText(imageQuality)}</span>
          <span class="print-info">محسن للطباعة</span>
        </div>
      </div>`
    }

    // إضافة CSS محسن للطباعة
    html += this.generateEnhancedImageCSS(layout, imageQuality)

    if (layout === 'single') {
      // طباعة صورة واحدة في الصفحة
      html += this.generateSingleImageLayout(images, showMetadata, options, fitToPage)
    } else if (layout === 'grid') {
      // طباعة شبكة من الصور
      html += this.generateGridImageLayout(images, imagesPerPage, showMetadata, options)
    } else if (layout === 'list') {
      // طباعة قائمة الصور مع التفاصيل
      html += this.generateListImageLayout(images, showMetadata, options)
    } else if (layout === 'gallery') {
      // تخطيط معرض محسن
      html += this.generateGalleryLayout(images, imagesPerPage, showMetadata, options)
    } else if (layout === 'catalog') {
      // تخطيط كتالوج خاص
      html += this.generateCatalogLayout(images, imagesPerPage, showMetadata, options)
    }

    html += '</div>'
    return html
  }

  /**
   * الحصول على نص التخطيط
   */
  private getLayoutText(layout: string): string {
    const layouts = {
      single: 'صورة واحدة',
      grid: 'شبكة',
      list: 'قائمة',
      catalog: 'كتالوج'
    }
    return layouts[layout as keyof typeof layouts] || layout
  }

  /**
   * الحصول على نص الجودة (محدثة لدعم ultra)
   */
  private getQualityText(quality: string): string {
    const qualities = {
      low: 'منخفضة',
      medium: 'متوسطة',
      high: 'عالية',
      ultra: 'فائقة'
    }
    return qualities[quality as keyof typeof qualities] || quality
  }

  /**
   * تخطيط صورة واحدة
   */
  private generateSingleImageLayout(
    images: NonNullable<EnhancedPrintData['images']>,
    showMetadata: boolean,
    options: EnhancedPrintOptions,
    fitToPage: boolean = true
  ): string {
    return images.map((image, index) => {
      const imageStyle = fitToPage
        ? "max-width: 90%; max-height: 70vh; object-fit: contain;"
        : "max-width: 100%; height: auto; object-fit: cover;"

      // استخدام الدالة الجديدة لإنشاء img tag مع معالجة الأخطاء
      const imageTag = this.generateImageTag(
        image,
        `${imageStyle} border: 2px solid #ddd; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);`
      )

      return `
        <div class="single-image-page" style="page-break-after: always; text-align: center; min-height: 80vh; display: flex; flex-direction: column; justify-content: center; align-items: center;">
          <div class="image-container" style="margin: 20px 0; flex: 1; display: flex; align-items: center; justify-content: center;">
            ${imageTag}
          </div>
          ${showMetadata ? `
            <div class="image-metadata" style="margin-top: 20px; text-align: right; background: #f8f9fa; padding: 20px; border-radius: 8px; max-width: 600px; width: 100%;">
              <h3 style="color: #1890ff; margin-bottom: 15px; border-bottom: 2px solid #1890ff; padding-bottom: 8px;">${image.name}</h3>
              ${image.description ? `<p style="margin: 8px 0;"><strong>الوصف:</strong> ${image.description}</p>` : ''}
              ${image.category ? `<p style="margin: 8px 0;"><strong>الفئة:</strong> ${image.category}</p>` : ''}
              ${image.uploadDate ? `<p style="margin: 8px 0;"><strong>تاريخ الرفع:</strong> ${new Date(image.uploadDate).toLocaleDateString('en-GB')}</p>` : ''}
              ${image.size ? `<p style="margin: 8px 0;"><strong>حجم الملف:</strong> ${this.formatFileSize(image.size)}</p>` : ''}
              ${image.notes ? `<p style="margin: 8px 0;"><strong>ملاحظات:</strong> ${image.notes}</p>` : ''}
              ${image.metadata ? Object.entries(image.metadata).map(([key, value]) =>
                `<p style="margin: 8px 0;"><strong>${key}:</strong> ${value}</p>`
              ).join('') : ''}
              <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #ddd; font-size: 12px; color: #666;">
                صورة ${index + 1} من ${images.length}
              </div>
            </div>
          ` : ''}
        </div>
      `
    }).join('')
  }

  /**
   * تخطيط شبكة الصور
   */
  private generateGridImageLayout(
    images: NonNullable<EnhancedPrintData['images']>,
    imagesPerPage: number,
    showMetadata: boolean,
    _options: EnhancedPrintOptions
  ): string {
    const imagesPerRow = Math.ceil(Math.sqrt(imagesPerPage))
    let html = '<div class="images-grid" style="display: grid; grid-template-columns: repeat(' + imagesPerRow + ', 1fr); gap: 20px; margin: 20px 0;">'

    images.forEach((image, index) => {
      if (index > 0 && index % imagesPerPage === 0) {
        html += '</div><div style="page-break-before: always;"></div><div class="images-grid" style="display: grid; grid-template-columns: repeat(' + imagesPerRow + ', 1fr); gap: 20px; margin: 20px 0;">'
      }

      // استخدام الدالة الجديدة لإنشاء img tag مع معالجة الأخطاء
      const imageTag = this.generateImageTag(
        image,
        "width: 100%; height: 200px; object-fit: cover; border: 1px solid #ddd;"
      )

      html += `
        <div class="grid-image-item" style="text-align: center; border: 2px solid #f0f0f0; padding: 15px; border-radius: 8px; background: white; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: none;">
          <div class="grid-image-container" style="margin-bottom: 12px; overflow: hidden; border-radius: 6px;">
            ${imageTag}
          </div>
          ${showMetadata ? `
            <div class="image-info" style="margin-top: 10px; font-size: 12px; text-align: right;">
              <div style="font-weight: bold; margin-bottom: 8px; color: #1890ff; font-size: 13px;">${image.name}</div>
              ${image.category ? `<div style="color: #666; background: #f8f9fa; padding: 4px 8px; border-radius: 4px; margin-bottom: 6px; font-size: 11px;">${image.category}</div>` : ''}
              ${image.description ? `<div style="color: #888; font-size: 10px; line-height: 1.3; margin-top: 6px;">${image.description.substring(0, 60)}${image.description.length > 60 ? '...' : ''}</div>` : ''}

              ${image.metadata ? this.formatUniversalImageMetadata(image.metadata) : ''}

              ${image.uploadDate ? `<div style="color: #999; font-size: 10px; margin-top: 4px;">تاريخ الرفع: ${new Date(image.uploadDate).toLocaleDateString('en-GB')}</div>` : ''}
              ${image.size ? `<div style="color: #999; font-size: 10px; margin-top: 2px;">الحجم: ${this.formatFileSize(image.size)}</div>` : ''}
            </div>
          ` : ''}
        </div>
      `
    })

    html += '</div>'
    return html
  }

  /**
   * تخطيط قائمة الصور
   */
  private generateListImageLayout(
    images: NonNullable<EnhancedPrintData['images']>,
    showMetadata: boolean,
    _options: EnhancedPrintOptions
  ): string {
    return `
      <div class="images-list" style="margin: 20px 0;">
        ${images.map((image, index) => {
          // استخدام الدالة الجديدة لإنشاء img tag مع معالجة الأخطاء
          const imageTag = this.generateImageTag(
            image,
            "width: 120px; height: 120px; object-fit: cover; border-radius: 6px; border: 1px solid #ddd;"
          )

          return `
            <div class="list-image-item" style="display: flex; margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <div class="image-thumbnail" style="flex-shrink: 0; margin-left: 15px;">
                ${imageTag}
              </div>
              <div class="image-details" style="flex: 1;">
                <h4 style="margin: 0 0 10px 0; color: #1890ff; font-size: 16px;">${image.name}</h4>
                ${showMetadata ? `
                  ${image.description ? `<p style="margin: 5px 0; line-height: 1.5;"><strong>الوصف:</strong> ${image.description}</p>` : ''}
                  ${image.category ? `<p style="margin: 5px 0;"><strong>الفئة:</strong> <span style="background: #f0f0f0; padding: 2px 8px; border-radius: 4px;">${image.category}</span></p>` : ''}
                  ${image.uploadDate ? `<p style="margin: 5px 0;"><strong>تاريخ الرفع:</strong> ${new Date(image.uploadDate).toLocaleDateString('en-GB')}</p>` : ''}
                  ${image.size ? `<p style="margin: 5px 0;"><strong>حجم الملف:</strong> ${this.formatFileSize(image.size)}</p>` : ''}
                  ${image.notes ? `<p style="margin: 5px 0;"><strong>ملاحظات:</strong> ${image.notes}</p>` : ''}
                  ${image.metadata ? Object.entries(image.metadata).map(([key, value]) =>
                    `<p style="margin: 5px 0; font-size: 13px;"><strong>${key}:</strong> ${value}</p>`
                  ).join('') : ''}
                ` : ''}
                <div style="margin-top: 10px; padding-top: 8px; border-top: 1px solid #eee; font-size: 12px; color: #999;">
                  عنصر ${index + 1} من ${images.length}
                </div>
              </div>
            </div>
          `
        }).join('')}
      </div>
    `
  }

  /**
   * تخطيط كتالوج المنتجات
   */
  private generateCatalogLayout(
    images: NonNullable<EnhancedPrintData['images']>,
    imagesPerPage: number,
    showMetadata: boolean,
    _options: EnhancedPrintOptions
  ): string {
    const itemsPerRow = Math.min(3, Math.ceil(Math.sqrt(imagesPerPage)))
    let html = '<div class="catalog-layout" style="margin: 20px 0;">'

    images.forEach((image, index) => {
      if (index > 0 && index % imagesPerPage === 0) {
        html += '</div><div style="page-break-before: always;"></div><div class="catalog-layout" style="margin: 20px 0;">'
      }

      if (index % itemsPerRow === 0) {
        html += '<div class="catalog-row" style="display: flex; margin-bottom: 30px; gap: 20px;">'
      }

      // استخدام الدالة الجديدة لإنشاء img tag مع معالجة الأخطاء
      const imageTag = this.generateImageTag(
        image,
        "width: 100%; height: 180px; object-fit: cover; border-radius: 8px; border: 1px solid #ddd;"
      )

      html += `
        <div class="catalog-item" style="flex: 1; text-align: center; border: 2px solid #f0f0f0; border-radius: 12px; padding: 15px; background: white; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
          <div class="catalog-image" style="margin-bottom: 15px;">
            ${imageTag}
          </div>
          <div class="catalog-info">
            <h4 style="margin: 0 0 8px 0; color: #1890ff; font-size: 14px; font-weight: bold;">${image.name}</h4>
            ${showMetadata && image.metadata ? `
              ${image.metadata.code ? `<p style="margin: 4px 0; font-size: 12px; color: #666;"><strong>كود:</strong> ${image.metadata.code}</p>` : ''}
              ${image.metadata.price ? `<p style="margin: 4px 0; font-size: 14px; color: #52c41a; font-weight: bold;">${image.metadata.price} ₪</p>` : ''}
              ${image.category ? `<p style="margin: 4px 0; font-size: 11px; background: #f0f0f0; padding: 2px 6px; border-radius: 4px; display: inline-block;">${image.category}</p>` : ''}
            ` : ''}
            ${showMetadata && image.description ? `<p style="margin: 8px 0 0 0; font-size: 11px; color: #666; line-height: 1.4;">${image.description.substring(0, 80)}${image.description.length > 80 ? '...' : ''}</p>` : ''}
          </div>
        </div>
      `

      if ((index + 1) % itemsPerRow === 0 || index === images.length - 1) {
        // إضافة عناصر فارغة لملء الصف
        const remaining = itemsPerRow - ((index % itemsPerRow) + 1)
        for (let i = 0; i < remaining; i++) {
          html += '<div style="flex: 1;"></div>'
        }
        html += '</div>'
      }
    })

    html += '</div>'
    return html
  }



  /**
   * تنسيق معلومات الصور الشاملة
   */
  private formatUniversalImageMetadata(metadata: any): string {
    if (!metadata) return ''

    let html = ''

    // معلومات الفاتورة
    if (metadata.invoiceNumber) {
      html += `<div style="color: #1890ff; font-weight: bold; margin-top: 6px; font-size: 11px;">فاتورة: ${metadata.invoiceNumber}</div>`
      if (metadata.customerName) {
        html += `<div style="color: #666; font-size: 10px;">العميل: ${metadata.customerName}</div>`
      }
      if (metadata.invoiceTotal) {
        html += `<div style="color: #52c41a; font-weight: bold; font-size: 10px;">المبلغ: ${metadata.invoiceTotal} ₪</div>`
      }
    }

    // معلومات أمر الإنتاج
    if (metadata.orderNumber) {
      html += `<div style="color: #722ed1; font-weight: bold; margin-top: 6px; font-size: 11px;">أمر إنتاج: ${metadata.orderNumber}</div>`
      if (metadata.itemName) {
        html += `<div style="color: #666; font-size: 10px;">الصنف: ${metadata.itemName}</div>`
      }
      if (metadata.quantity) {
        html += `<div style="color: #fa8c16; font-size: 10px;">الكمية: ${metadata.quantity}</div>`
      }
    }

    // معلومات العميل/المورد
    if (metadata.contactName && metadata.contactType) {
      const contactLabel = metadata.contactType === 'customer' ? 'العميل' : 'المورد'
      html += `<div style="color: #13c2c2; font-weight: bold; margin-top: 6px; font-size: 11px;">${contactLabel}: ${metadata.contactName}</div>`
      if (metadata.phone) {
        html += `<div style="color: #666; font-size: 10px;">الهاتف: ${metadata.phone}</div>`
      }
    }

    // معلومات الصنف
    if (metadata.itemCode) {
      html += `<div style="color: #fa541c; font-weight: bold; margin-top: 6px; font-size: 11px;">كود الصنف: ${metadata.itemCode}</div>`
      if (metadata.itemPrice) {
        html += `<div style="color: #52c41a; font-weight: bold; font-size: 10px;">السعر: ${metadata.itemPrice} ₪</div>`
      }
    }

    // معلومات الشيك
    if (metadata.checkNumber) {
      html += `<div style="color: #eb2f96; font-weight: bold; margin-top: 6px; font-size: 11px;">شيك: ${metadata.checkNumber}</div>`
      if (metadata.checkAmount) {
        html += `<div style="color: #52c41a; font-weight: bold; font-size: 10px;">المبلغ: ${metadata.checkAmount} ₪</div>`
      }
      if (metadata.bankName) {
        html += `<div style="color: #666; font-size: 10px;">البنك: ${metadata.bankName}</div>`
      }
    }

    return html
  }

  /**
   * إنشاء قسم التوقيع
   */
  private generateSignatureSection(): string {
    return `
      <div class="signature-section">
        <div class="signature-box">توقيع المستلم</div>
        <div class="signature-box">توقيع المسؤول</div>
      </div>
    `
  }

  /**
   * إنشاء الفوتر
   */
  private generateFooter(data: EnhancedPrintData, options: EnhancedPrintOptions): string {
    const primaryColor = options.primaryColor || '#1890ff'
    const borderColor = options.borderColor || '#d9d9d9'

    // نص الفوتر المخصص
    const customFooterText = options.footerText ? `
      <div style="text-align: center; margin-bottom: 10px; padding: 8px; background-color: #f9f9f9; border-radius: 4px; font-weight: bold; color: ${primaryColor};">
        ${options.footerText}
      </div>
    ` : ''

    return `
      <div class="footer" style="margin-top: 20px; padding-top: 15px; border-top: 1px solid ${borderColor}; text-align: center; font-size: 11px; color: #666;">
        ${customFooterText}
        <div>تم إنشاء هذا المستند بواسطة نظام ${data.company?.name || 'ZET.IA'}</div>
        <div>تاريخ الطباعة: ${new Date().toLocaleString('en-GB')}</div>
        ${options.showTerms && data.terms ? `<div style="margin-top: 10px; font-size: 10px; text-align: right; padding: 8px; background-color: #f9f9f9; border-radius: 4px;">${data.terms}</div>` : ''}
        ${options.showQR && data.qrCode ? `<div style="margin-top: 10px;"><img src="${data.qrCode}" alt="QR Code" style="width: 80px; height: 80px;"></div>` : ''}
      </div>
    `
  }

  /**
   * تنسيق العملة
   */
  private formatCurrency(amount: number): string {
    // التحقق من صحة المبلغ
    if (typeof amount !== 'number' || isNaN(amount)) {
      amount = 0
    }

    // استخدام العملة من الإعدادات
    const settings = settingsManager.getSettings()
    const currencySymbol = settings?.currencySymbol || '₪'

    try {
      // تنسيق الرقم مع فواصل الآلاف
      const formattedNumber = amount.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })

      return `${formattedNumber} ${currencySymbol}`
    } catch (error) {
      // في حالة فشل التنسيق، استخدم تنسيق بسيط
      const formattedNumber = (amount || 0).toFixed(2)
      return `${formattedNumber} ${currencySymbol}`
    }
  }

  /**
   * الحصول على عنوان المستند حسب النوع
   */
  private getDocumentTitle(type: string): string {
    const titles = {
      invoice: 'فاتورة',
      receipt: 'إيصال',
      order: 'أمر عمل',
      report: 'تقرير',
      certificate: 'شهادة',
      statement: 'كشف حساب',
      image: 'صور',
      catalog: 'كتالوج المنتجات'
    }
    return titles[type as keyof typeof titles] || 'مستند'
  }

  /**
   * الحصول على إعدادات القالب المخصص حسب نوع التقرير
   * يحاول أولاً جلب القالب من قاعدة البيانات، ثم يستخدم القوالب المبرمجة
   */
  private async getReportTemplateSettings(subType: string): Promise<Partial<EnhancedPrintOptions>> {
    try {
      // محاولة جلب القالب من قاعدة البيانات
      const result = await window.electronAPI?.invoke('get-print-templates');

      if (result?.success && result.data) {
        // البحث عن قالب يدعم نوع التقرير المحدد (النظام الموحد الجديد)
        const matchingTemplate = result.data.find((template: any) => {
          // البحث في supportedReportTypes أولاً
          if (template.supportedReportTypes && Array.isArray(template.supportedReportTypes)) {
            return template.supportedReportTypes.includes(subType) && template.is_active;
          }

          // البحث في الاسم والوصف كبديل
          return template.type === 'report' &&
                 template.is_active &&
                 (template.name?.toLowerCase().includes(subType.toLowerCase()) ||
                  template.template_type === subType ||
                  template.category === this.getCategoryForReportType(subType));
        });

        if (matchingTemplate) {
          const settings = matchingTemplate.settings || JSON.parse(matchingTemplate.template_data || '{}');
          return {
            pageSize: settings.pageSize || 'A4',
            orientation: settings.orientation || 'landscape',
            fontSize: settings.fontSize || 10,
            fontFamily: settings.fontFamily || 'Arial',
            showHeader: settings.showHeader !== false,
            showFooter: settings.showFooter !== false,
            showLogo: settings.showLogo !== false,
            margins: {
              top: settings.marginTop || 15,
              bottom: settings.marginBottom || 15,
              left: settings.marginLeft || 10,
              right: settings.marginRight || 10
            },
            primaryColor: settings.primaryColor || '#1890ff',
            secondaryColor: settings.secondaryColor || '#f0f2f5',
            borderColor: settings.borderColor || '#d9d9d9',
            // إعدادات إضافية للتقارير
            headerSize: settings.headerSize || 16,
            lineSpacing: settings.lineSpacing || 1.4,
            tableWidth: settings.tableWidth || 100,
            sectionSpacing: settings.sectionSpacing || 12
          };
        }
      }
    } catch (error) {
      Logger.warn('MasterPrintService', 'فشل في جلب القوالب من قاعدة البيانات، استخدام القوالب الافتراضية:', error);
    }

    // استخدام القوالب المبرمجة المحسنة كبديل
    return this.getBuiltInTemplateSettings(subType);
  }

  /**
   * اختيار القالب الأمثل تلقائياً حسب نوع التقرير
   */
  public async getOptimalTemplateForReport(reportType: string): Promise<any> {
    try {
      // محاولة الحصول على قالب مخصص من قاعدة البيانات
      const customTemplate = await this.getCustomTemplateForReportType(reportType);
      if (customTemplate) {
        return customTemplate;
      }

      // الحصول على القالب الافتراضي المناسب
      const category = this.getCategoryForReportType(reportType);
      const defaultTemplate = this.getDefaultTemplateForCategory(category);
      return defaultTemplate;
    } catch (error) {
      // في حالة الخطأ، استخدام القالب العام
      return this.getDefaultTemplateForCategory('general');
    }
  }

  /**
   * الحصول على قالب مخصص من قاعدة البيانات حسب نوع التقرير
   */
  private async getCustomTemplateForReportType(reportType: string): Promise<any | null> {
    try {
      if (!window.electronAPI?.getTemplates) {
        return null;
      }

      const templates = await window.electronAPI.getTemplates();
      if (!templates?.success || !templates?.data) {
        return null;
      }

      // البحث عن قالب يدعم نوع التقرير المطلوب
      const matchingTemplate = templates.data.find((template: any) => {
        const supportedTypes = template.supportedReportTypes || [];
        return supportedTypes.includes(reportType);
      });

      return matchingTemplate || null;
    } catch (error) {
      console.warn('خطأ في جلب القوالب المخصصة:', error);
      return null;
    }
  }

  /**
   * الحصول على القالب الافتراضي حسب الفئة
   */
  private getDefaultTemplateForCategory(category: string): any {
    const defaultTemplates = {
      financial: {
        name: 'قالب التقارير المالية',
        primaryColor: '#1890ff',
        secondaryColor: '#f0f5ff',
        borderColor: '#91d5ff',
        orientation: 'portrait',
        fontSize: 11,
        headerText: 'التقارير المالية',
        footerText: 'نظام المحاسبة المتكامل - ZET.IA'
      },
      inventory: {
        name: 'قالب تقارير المخزون',
        primaryColor: '#52c41a',
        secondaryColor: '#f6ffed',
        borderColor: '#b7eb8f',
        orientation: 'landscape',
        fontSize: 10,
        headerText: 'تقارير إدارة المخزون',
        footerText: 'نظام إدارة المخزون - ZET.IA'
      },
      sales: {
        name: 'قالب تقارير المبيعات',
        primaryColor: '#fa8c16',
        secondaryColor: '#fff7e6',
        borderColor: '#ffd591',
        orientation: 'landscape',
        fontSize: 10,
        headerText: 'تقارير المبيعات',
        footerText: 'نظام إدارة المبيعات - ZET.IA'
      },
      production: {
        name: 'قالب تقارير الإنتاج',
        primaryColor: '#722ed1',
        secondaryColor: '#f9f0ff',
        borderColor: '#d3adf7',
        orientation: 'landscape',
        fontSize: 10,
        headerText: 'تقارير الإنتاج',
        footerText: 'نظام إدارة الإنتاج - ZET.IA'
      },
      employees: {
        name: 'قالب تقارير الموظفين',
        primaryColor: '#13c2c2',
        secondaryColor: '#e6fffb',
        borderColor: '#87e8de',
        orientation: 'portrait',
        fontSize: 11,
        headerText: 'تقارير الموارد البشرية',
        footerText: 'نظام إدارة الموارد البشرية - ZET.IA'
      },
      paint: {
        name: 'قالب تقارير الدهان',
        primaryColor: '#eb2f96',
        secondaryColor: '#fff0f6',
        borderColor: '#ffadd2',
        orientation: 'landscape',
        fontSize: 10,
        headerText: 'تقارير الدهان',
        footerText: 'نظام إدارة الدهان - ZET.IA'
      },
      fiscal_closing: {
        name: 'قالب تقارير إقفال السنة المالية',
        primaryColor: '#722ed1',
        secondaryColor: '#f9f0ff',
        borderColor: '#d3adf7',
        orientation: 'portrait',
        fontSize: 11,
        headerText: 'تقارير إقفال السنة المالية',
        footerText: 'نظام إقفال السنة المالية - ZET.IA'
      },
      purchases: {
        name: 'قالب تقارير المشتريات',
        primaryColor: '#fa541c',
        secondaryColor: '#fff2e8',
        borderColor: '#ffbb96',
        orientation: 'landscape',
        fontSize: 10,
        headerText: 'تقارير المشتريات',
        footerText: 'نظام إدارة المشتريات - ZET.IA'
      },
      general: {
        name: 'القالب العام',
        primaryColor: '#595959',
        secondaryColor: '#f5f5f5',
        borderColor: '#d9d9d9',
        orientation: 'portrait',
        fontSize: 11,
        headerText: 'تقرير عام',
        footerText: 'النظام المتكامل - ZET.IA'
      }
    };

    return defaultTemplates[category] || defaultTemplates.general;
  }

  /**
   * تحديد فئة التقرير حسب نوعه
   */
  private getCategoryForReportType(reportType: string): string {
    const categoryMap: Record<string, string> = {
      // التقارير المالية
      'profit_loss': 'financial',
      'cash_flow': 'financial',
      'cash_flow_analysis': 'financial',
      'balance_sheet': 'financial',
      'income_statement': 'financial',
      'bank_reconciliation': 'financial',
      'customer_aging': 'financial',
      'profitability': 'financial',
      'customer_analysis': 'financial',
      'financial_summary': 'financial',

      // تقارير المخزون
      'inventory_detailed': 'inventory',
      'inventory_movements': 'inventory',
      'inventory_audit': 'inventory',
      'material_consumption': 'inventory',
      'low_stock': 'inventory',
      'advanced_inventory': 'inventory',
      'abc_analysis': 'inventory',
      'abc-analysis': 'inventory',
      'item_warehouse_distribution': 'inventory',
      'item-warehouse-distribution': 'inventory',

      // تقارير المبيعات
      'sales_by_customer': 'sales',
      'sales_by_product': 'sales',
      'sales_by_region': 'sales',
      'monthly_sales': 'sales',
      'sales_returns': 'sales',
      'top_profitable_customers': 'sales',

      // تقارير المشتريات
      'purchases_by_supplier': 'purchases',
      'purchases_by_item': 'purchases',
      'supplier_payables': 'purchases',
      'purchase_analysis': 'purchases',
      'cost_analysis': 'purchases',
      'supplier_price_comparison': 'purchases',
      'supplier_quality': 'purchases',
      'supplier_analysis': 'purchases',

      // تقارير الإنتاج
      'production_orders': 'production',
      'production_efficiency': 'production',
      'production_costs': 'production',
      'production_schedule': 'production',
      'production_quality': 'production',
      'production_workers_performance': 'production',
      'production_materials_consumption': 'production',
      'production_profitability': 'production',

      // تقارير الموظفين
      'employee_attendance': 'employees',
      'employee-attendance': 'employees',
      'employee_payroll': 'employees',
      'employee_leaves': 'employees',
      'employee_performance': 'employees',
      'employee_overtime': 'employees',
      'employee_analysis': 'employees',
      'employee-analysis': 'employees',
      'salary_comparison': 'employees',
      'efficiency_evaluation': 'employees',

      // تقارير الدهان
      'paint_by_customer': 'paint',
      'paint_by_type': 'paint',
      'monthly_paint': 'paint',
      'paint_profitability': 'paint',
      'paint_performance': 'paint',
      'paint_quality': 'paint'
    };

    return categoryMap[reportType] || 'general';
  }

  /**
   * الحصول على إعدادات القوالب المبرمجة المحسنة
   */
  private getBuiltInTemplateSettings(subType: string): Partial<EnhancedPrintOptions> {
    const templates = {
      // === القوالب المالية ===
      financial: {
        primaryColor: '#1890ff',
        secondaryColor: '#f0f8ff',
        accentColor: '#52c41a',
        borderColor: '#91d5ff',
        orientation: 'portrait' as const,
        fontSize: 11,
        headerFontSize: 14,
        logoPosition: 'top-left' as const,
        logoSize: 'medium' as const,
        showWatermark: false,
        headerText: 'التقرير المالي',
        footerText: 'تم إنشاؤه بواسطة النظام المالي - ZET.IA'
      },
      'financial-classic': {
        primaryColor: '#000000',
        secondaryColor: '#f5f5f5',
        accentColor: '#666666',
        borderColor: '#d9d9d9',
        fontFamily: 'Times New Roman, serif',
        orientation: 'portrait' as const,
        fontSize: 12,
        headerFontSize: 14,
        logoPosition: 'top-center' as const,
        logoSize: 'large' as const,
        showWatermark: true,
        watermarkText: 'سري',
        watermarkOpacity: 0.1,
        headerText: 'التقرير المالي الكلاسيكي',
        footerText: 'النظام المالي المتكامل - ZET.IA'
      },
      'financial-executive': {
        primaryColor: '#2f54eb',
        secondaryColor: '#f0f5ff',
        accentColor: '#faad14',
        borderColor: '#adc6ff',
        orientation: 'portrait' as const,
        fontSize: 12,
        headerFontSize: 16,
        logoPosition: 'top-center' as const,
        logoSize: 'large' as const,
        showWatermark: true,
        watermarkText: 'تنفيذي',
        watermarkOpacity: 0.05,
        headerText: 'التقرير المالي التنفيذي',
        footerText: 'الإدارة التنفيذية - ZET.IA'
      },

      // === قوالب إقفال السنة المالية ===
      fiscal_closing: {
        primaryColor: '#722ed1',
        secondaryColor: '#f9f0ff',
        accentColor: '#fa8c16',
        borderColor: '#d3adf7',
        orientation: 'portrait' as const,
        fontSize: 11,
        headerFontSize: 15,
        logoPosition: 'top-center' as const,
        logoSize: 'large' as const,
        showWatermark: true,
        watermarkText: 'إقفال السنة المالية',
        watermarkOpacity: 0.08,
        headerText: 'تقرير إقفال السنة المالية',
        footerText: 'نظام إقفال السنة المالية - ZET.IA'
      },
      'closing_summary': {
        primaryColor: '#531dab',
        secondaryColor: '#f9f0ff',
        accentColor: '#52c41a',
        borderColor: '#b37feb',
        orientation: 'portrait' as const,
        fontSize: 12,
        headerFontSize: 16,
        logoPosition: 'top-left' as const,
        logoSize: 'medium' as const,
        showWatermark: false,
        headerText: 'ملخص إقفال السنة المالية',
        footerText: 'الملخص التنفيذي - ZET.IA'
      },

      // === قوالب الإنتاج ===
      production: {
        primaryColor: '#722ed1',
        secondaryColor: '#f9f0ff',
        accentColor: '#fa8c16',
        borderColor: '#d3adf7',
        orientation: 'landscape' as const,
        fontSize: 10,
        headerFontSize: 12,
        logoPosition: 'top-right' as const,
        logoSize: 'small' as const,
        showWatermark: false,
        headerText: 'تقرير إدارة الإنتاج',
        footerText: 'نظام إدارة الإنتاج - ZET.IA'
      },
      'production-detailed': {
        primaryColor: '#531dab',
        secondaryColor: '#f9f0ff',
        accentColor: '#13c2c2',
        borderColor: '#b37feb',
        orientation: 'landscape' as const,
        pageSize: 'A3' as const,
        fontSize: 9,
        headerFontSize: 11,
        logoPosition: 'top-left' as const,
        logoSize: 'small' as const,
        showWatermark: false,
        headerText: 'تقرير الإنتاج المفصل',
        footerText: 'التقرير التفصيلي للإنتاج - ZET.IA'
      },
      'production-summary': {
        primaryColor: '#722ed1',
        secondaryColor: '#fff2e8',
        accentColor: '#fa541c',
        borderColor: '#d3adf7',
        orientation: 'portrait' as const,
        fontSize: 11,
        headerFontSize: 13,
        logoPosition: 'top-center' as const,
        logoSize: 'medium' as const,
        showWatermark: false,
        headerText: 'ملخص الإنتاج',
        footerText: 'الملخص التنفيذي للإنتاج - ZET.IA'
      },

      // === قوالب المخزون ===
      inventory: {
        primaryColor: '#13c2c2',
        secondaryColor: '#e6fffb',
        accentColor: '#52c41a',
        borderColor: '#87e8de',
        orientation: 'landscape' as const,
        fontSize: 10,
        headerFontSize: 12,
        logoPosition: 'top-left' as const,
        logoSize: 'medium' as const,
        showWatermark: false,
        headerText: 'تقرير إدارة المخزون',
        footerText: 'نظام إدارة المخزون - ZET.IA'
      },
      'inventory-detailed': {
        primaryColor: '#006d75',
        secondaryColor: '#e6fffb',
        accentColor: '#52c41a',
        borderColor: '#5cdbd3',
        orientation: 'landscape' as const,
        pageSize: 'A3' as const,
        fontSize: 8,
        headerFontSize: 10,
        logoPosition: 'top-left' as const,
        logoSize: 'small' as const,
        showWatermark: false,
        headerText: 'تقرير المخزون المفصل',
        footerText: 'التقرير التفصيلي للمخزون - ZET.IA'
      },
      'inventory-summary': {
        primaryColor: '#13c2c2',
        secondaryColor: '#f6ffed',
        accentColor: '#389e0d',
        borderColor: '#87e8de',
        orientation: 'portrait' as const,
        fontSize: 11,
        headerFontSize: 13,
        logoPosition: 'top-center' as const,
        logoSize: 'medium' as const,
        showWatermark: false,
        headerText: 'ملخص المخزون',
        footerText: 'الملخص التنفيذي للمخزون - ZET.IA'
      },

      // === قوالب المبيعات ===
      sales: {
        primaryColor: '#52c41a',
        secondaryColor: '#f6ffed',
        accentColor: '#fa8c16',
        borderColor: '#b7eb8f',
        orientation: 'landscape' as const,
        fontSize: 10,
        headerFontSize: 12,
        logoPosition: 'top-left' as const,
        logoSize: 'medium' as const,
        showWatermark: false,
        headerText: 'تقرير إدارة المبيعات',
        footerText: 'نظام إدارة المبيعات - ZET.IA'
      },
      'sales-performance': {
        primaryColor: '#389e0d',
        secondaryColor: '#f6ffed',
        accentColor: '#faad14',
        borderColor: '#95de64',
        orientation: 'landscape' as const,
        fontSize: 11,
        headerFontSize: 13,
        logoPosition: 'top-right' as const,
        logoSize: 'small' as const,
        showWatermark: false,
        headerText: 'تقرير أداء المبيعات',
        footerText: 'تقرير الأداء والإنجاز - ZET.IA'
      },

      // === قوالب الموظفين ===
      employees: {
        primaryColor: '#fa541c',
        secondaryColor: '#fff2e8',
        accentColor: '#1890ff',
        borderColor: '#ffbb96',
        orientation: 'portrait' as const,
        fontSize: 10,
        headerFontSize: 12,
        logoPosition: 'top-left' as const,
        logoSize: 'medium' as const,
        showWatermark: false,
        headerText: 'تقرير إدارة الموارد البشرية',
        footerText: 'نظام إدارة الموارد البشرية - ZET.IA'
      },
      'employees-payroll': {
        primaryColor: '#d4380d',
        secondaryColor: '#fff2e8',
        accentColor: '#52c41a',
        borderColor: '#ff7a45',
        orientation: 'landscape' as const,
        fontSize: 11,
        headerFontSize: 13,
        logoPosition: 'top-center' as const,
        logoSize: 'medium' as const,
        showWatermark: true,
        watermarkText: 'سري',
        watermarkOpacity: 0.08,
        headerText: 'تقرير الرواتب والأجور',
        footerText: 'تقرير سري - نظام الرواتب - ZET.IA'
      },

      // === قوالب المشتريات ===
      purchase: {
        primaryColor: '#13c2c2',
        secondaryColor: '#e6fffb',
        accentColor: '#fa8c16',
        borderColor: '#87e8de',
        orientation: 'landscape' as const,
        fontSize: 10,
        headerFontSize: 12,
        logoPosition: 'top-left' as const,
        logoSize: 'medium' as const,
        showWatermark: false,
        headerText: 'تقرير إدارة المشتريات',
        footerText: 'نظام إدارة المشتريات - ZET.IA'
      },

      // === قوالب الدهان ===
      paint: {
        primaryColor: '#eb2f96',
        secondaryColor: '#fff0f6',
        accentColor: '#722ed1',
        borderColor: '#ffadd2',
        orientation: 'landscape' as const,
        fontSize: 10,
        headerFontSize: 12,
        logoPosition: 'top-right' as const,
        logoSize: 'small' as const,
        showWatermark: false,
        headerText: 'تقرير إدارة الدهان والألوان',
        footerText: 'نظام إدارة الدهان - ZET.IA'
      },

      // === قوالب التحليل ===
      analysis: {
        primaryColor: '#2f54eb',
        secondaryColor: '#f0f5ff',
        accentColor: '#faad14',
        borderColor: '#adc6ff',
        orientation: 'landscape' as const,
        fontSize: 9,
        headerFontSize: 11,
        logoPosition: 'top-center' as const,
        logoSize: 'medium' as const,
        showWatermark: false,
        headerText: 'تقرير التحليل والإحصائيات',
        footerText: 'نظام التحليل والذكاء التجاري - ZET.IA'
      },

      // === قوالب عامة ===
      general: {
        primaryColor: '#1890ff',
        secondaryColor: '#f0f5ff',
        accentColor: '#52c41a',
        borderColor: '#91d5ff',
        orientation: 'portrait' as const,
        fontSize: 11,
        headerFontSize: 13,
        logoPosition: 'top-center' as const,
        logoSize: 'medium' as const,
        showWatermark: false,
        headerText: 'التقرير العام',
        footerText: 'النظام المتكامل - ZET.IA'
      },
      'general-minimal': {
        primaryColor: '#595959',
        secondaryColor: '#fafafa',
        accentColor: '#1890ff',
        borderColor: '#d9d9d9',
        orientation: 'portrait' as const,
        fontSize: 12,
        headerFontSize: 14,
        logoPosition: 'top-left' as const,
        logoSize: 'small' as const,
        showWatermark: false,
        headerText: 'التقرير المبسط',
        footerText: 'النظام المتكامل - ZET.IA'
      }
    }

    return templates[subType as keyof typeof templates] || templates.general || {}
  }

  /**
   * الحصول على أعمدة التقرير حسب النوع
   */
  private getReportColumns(subType: string, data: any[]): any[] {
    // إذا لم توجد بيانات، إرجاع أعمدة افتراضية
    if (!data || data.length === 0) {
      return [
        { key: 'id', title: 'الرقم', align: 'center' },
        { key: 'name', title: 'الاسم', align: 'right' },
        { key: 'value', title: 'القيمة', align: 'center' }
      ]
    }

    // استخراج الأعمدة من البيانات الفعلية
    const firstRow = data[0]
    const autoColumns = Object.keys(firstRow).map(key => ({
      key,
      title: this.getColumnTitle(key, subType),
      align: this.getColumnAlignment(key),
      format: this.getColumnFormat(key)
    }))

    // قوالب أعمدة مخصصة حسب نوع التقرير
    const customColumns = {
      inventory: [
        { key: 'item_code', title: 'كود الصنف', align: 'center' },
        { key: 'item_name', title: 'اسم الصنف', align: 'right' },
        { key: 'category', title: 'الفئة', align: 'center' },
        { key: 'quantity', title: 'الكمية', align: 'center', format: 'number' },
        { key: 'unit', title: 'الوحدة', align: 'center' },
        { key: 'unit_price', title: 'سعر الوحدة', align: 'left', format: 'currency' },
        { key: 'total_value', title: 'القيمة الإجمالية', align: 'left', format: 'currency' },
        { key: 'min_quantity', title: 'الحد الأدنى', align: 'center', format: 'number' },
        { key: 'status', title: 'الحالة', align: 'center' }
      ],

      production: [
        { key: 'order_number', title: 'رقم الأمر', align: 'center' },
        { key: 'product_name', title: 'اسم المنتج', align: 'right' },
        { key: 'customer_name', title: 'العميل', align: 'right' },
        { key: 'quantity', title: 'الكمية', align: 'center', format: 'number' },
        { key: 'start_date', title: 'تاريخ البدء', align: 'center', format: 'date' },
        { key: 'end_date', title: 'تاريخ الانتهاء', align: 'center', format: 'date' },
        { key: 'status', title: 'الحالة', align: 'center' },
        { key: 'progress', title: 'نسبة الإنجاز', align: 'center', format: 'percentage' },
        { key: 'cost', title: 'التكلفة', align: 'left', format: 'currency' }
      ],

      employees: [
        { key: 'employee_id', title: 'رقم الموظف', align: 'center' },
        { key: 'employee_name', title: 'اسم الموظف', align: 'right' },
        { key: 'department', title: 'القسم', align: 'center' },
        { key: 'position', title: 'المنصب', align: 'center' },
        { key: 'attendance_days', title: 'أيام الحضور', align: 'center', format: 'number' },
        { key: 'absence_days', title: 'أيام الغياب', align: 'center', format: 'number' },
        { key: 'overtime_hours', title: 'ساعات إضافية', align: 'center', format: 'number' },
        { key: 'salary', title: 'الراتب', align: 'left', format: 'currency' },
        { key: 'performance_score', title: 'تقييم الأداء', align: 'center', format: 'percentage' }
      ],

      purchase: [
        { key: 'purchase_number', title: 'رقم المشتريات', align: 'center' },
        { key: 'supplier_name', title: 'اسم المورد', align: 'right' },
        { key: 'item_name', title: 'اسم الصنف', align: 'right' },
        { key: 'quantity', title: 'الكمية', align: 'center', format: 'number' },
        { key: 'unit_price', title: 'سعر الوحدة', align: 'left', format: 'currency' },
        { key: 'total_amount', title: 'المبلغ الإجمالي', align: 'left', format: 'currency' },
        { key: 'purchase_date', title: 'تاريخ الشراء', align: 'center', format: 'date' },
        { key: 'payment_status', title: 'حالة الدفع', align: 'center' }
      ],

      paint: [
        { key: 'paint_code', title: 'كود الدهان', align: 'center' },
        { key: 'paint_name', title: 'اسم الدهان', align: 'right' },
        { key: 'color', title: 'اللون', align: 'center' },
        { key: 'type', title: 'النوع', align: 'center' },
        { key: 'quantity_used', title: 'الكمية المستخدمة', align: 'center', format: 'number' },
        { key: 'unit', title: 'الوحدة', align: 'center' },
        { key: 'cost_per_unit', title: 'تكلفة الوحدة', align: 'left', format: 'currency' },
        { key: 'total_cost', title: 'التكلفة الإجمالية', align: 'left', format: 'currency' },
        { key: 'quality_grade', title: 'درجة الجودة', align: 'center' }
      ]
    }

    return customColumns[subType as keyof typeof customColumns] || autoColumns
  }

  /**
   * الحصول على عنوان العمود
   */
  private getColumnTitle(key: string, _subType: string): string {
    const commonTitles: { [key: string]: string } = {
      id: 'الرقم',
      name: 'الاسم',
      code: 'الكود',
      date: 'التاريخ',
      amount: 'المبلغ',
      quantity: 'الكمية',
      price: 'السعر',
      total: 'المجموع',
      status: 'الحالة',
      type: 'النوع',
      category: 'الفئة',
      description: 'الوصف',
      notes: 'ملاحظات'
    }

    return commonTitles[key] || key.replace(/_/g, ' ')
  }

  /**
   * الحصول على محاذاة العمود
   */
  private getColumnAlignment(key: string): 'left' | 'center' | 'right' {
    if (key.includes('amount') || key.includes('price') || key.includes('cost') || key.includes('salary')) {
      return 'left' // للأرقام المالية
    }
    if (key.includes('name') || key.includes('description') || key.includes('notes')) {
      return 'right' // للنصوص
    }
    return 'center' // افتراضي
  }

  /**
   * الحصول على تنسيق العمود
   */
  private getColumnFormat(key: string): 'currency' | 'number' | 'date' | 'percentage' | 'text' {
    if (key.includes('amount') || key.includes('price') || key.includes('cost') || key.includes('salary')) {
      return 'currency'
    }
    if (key.includes('quantity') || key.includes('count') || key.includes('days') || key.includes('hours')) {
      return 'number'
    }
    if (key.includes('date') || key.includes('time')) {
      return 'date'
    }
    if (key.includes('percentage') || key.includes('rate') || key.includes('score')) {
      return 'percentage'
    }
    return 'text'
  }

  /**
   * تفتيح لون بنسبة معينة
   */
  private lightenColor(color: string, percent: number): string {
    // إزالة # إذا كانت موجودة
    const hex = color.replace('#', '')

    // تحويل إلى RGB
    const r = parseInt(hex.substring(0, 2), 16)
    const g = parseInt(hex.substring(2, 4), 16)
    const b = parseInt(hex.substring(4, 6), 16)

    // تطبيق التفتيح
    const newR = Math.min(255, Math.floor(r + (255 - r) * percent / 100))
    const newG = Math.min(255, Math.floor(g + (255 - g) * percent / 100))
    const newB = Math.min(255, Math.floor(b + (255 - b) * percent / 100))

    // تحويل إلى hex
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`
  }

  /**
   * تنسيق قيمة الخلية
   */
  private formatCellValue(value: any, format?: string): string {
    if (value === null || value === undefined) {
      return '-'
    }

    switch (format) {
      case 'currency':
        return this.formatCurrency(Number(value) || 0)

      case 'number':
        return Number(value).toLocaleString('en-US')

      case 'date':
        if (typeof value === 'string' && value.includes('-')) {
          return new Date(value).toLocaleDateString('en-GB')
        }
        return String(value)

      case 'percentage':
        return `${Number(value) || 0}%`

      default:
        return String(value)
    }
  }

  /**
   * طباعة سريعة بدون خيارات متقدمة
   */
  public async quickPrint(data: EnhancedPrintData, type: EnhancedPrintOptions['type'] = 'invoice'): Promise<void> {
    return this.print(data, { type })
  }

  /**
   * معاينة قبل الطباعة - نسخة محسنة
   */
  public async preview(data: EnhancedPrintData, options: EnhancedPrintOptions): Promise<void> {
    try {
      // تحميل معلومات الشركة المحدثة قبل المعاينة
      await this.loadCompanyInfo()

      // دمج معلومات الشركة
      const printData: EnhancedPrintData = {
        ...data,
        company: { ...this.companyInfo, ...data.company }
      }

      // إعدادات افتراضية للمعاينة مدمجة مع الإعدادات المحفوظة
      const defaultOptions: EnhancedPrintOptions = {
        type: 'invoice',
        pageSize: 'A4',
        orientation: 'portrait',
        margins: { top: 20, right: 20, bottom: 20, left: 20 },
        showLogo: true,
        showHeader: true,
        showFooter: true,
        showSignature: false,
        showTerms: true,
        fontSize: 12,
        fontFamily: 'Arial, sans-serif',
        primaryColor: '#1890ff',
        secondaryColor: '#f0f0f0',
        borderColor: '#d9d9d9',
        copies: 1,
        preview: true,
        autoClose: false,
        ...this.defaultPrintOptions, // استخدام الإعدادات المحفوظة
        ...options // الإعدادات المرسلة مع الطلب لها الأولوية
      }

      // إنشاء HTML للمعاينة
      const html = await this.generateHTML(printData, defaultOptions)

      // إنشاء معاينة احترافية في modal
      this.showPrintPreviewModal(html, printData, defaultOptions)

      options.onSuccess?.()

    } catch (error) {
      Logger.error('MasterPrintService', 'خطأ في المعاينة:', error)
      options.onError?.(error instanceof Error ? error.message : 'خطأ غير معروف')
    }
  }

  /**
   * معاينة فقط بدون فتح نافذة طباعة تلقائياً - جديد
   */
  public async previewOnly(data: EnhancedPrintData, options: EnhancedPrintOptions): Promise<void> {
    try {
      // تحميل معلومات الشركة المحدثة قبل المعاينة
      await this.loadCompanyInfo()

      // دمج معلومات الشركة
      const printData: EnhancedPrintData = {
        ...data,
        company: { ...this.companyInfo, ...data.company }
      }

      // إعدادات افتراضية للمعاينة مدمجة مع الإعدادات المحفوظة
      const defaultOptions: EnhancedPrintOptions = {
        type: 'invoice',
        pageSize: 'A4',
        orientation: 'portrait',
        margins: { top: 20, right: 20, bottom: 20, left: 20 },
        showLogo: true,
        showHeader: true,
        showFooter: true,
        showSignature: false,
        showTerms: true,
        fontSize: 12,
        fontFamily: 'Arial, sans-serif',
        primaryColor: '#1890ff',
        secondaryColor: '#f0f0f0',
        borderColor: '#d9d9d9',
        copies: 1,
        preview: true,
        autoClose: false,
        previewOnly: true, // علامة خاصة للمعاينة فقط
        ...this.defaultPrintOptions, // استخدام الإعدادات المحفوظة
        ...options // الإعدادات المرسلة مع الطلب لها الأولوية
      }

      // إنشاء HTML للمعاينة
      const html = await this.generateHTML(printData, defaultOptions)

      // إنشاء معاينة احترافية في modal بدون طباعة تلقائية
      this.showPreviewOnlyModal(html, printData, defaultOptions)

      options.onSuccess?.()

    } catch (error) {
      Logger.error('MasterPrintService', 'خطأ في المعاينة فقط:', error)
      options.onError?.(error instanceof Error ? error.message : 'خطأ غير معروف')
    }
  }

  /**
   * عرض معاينة الطباعة في modal احترافي
   */
  private showPrintPreviewModal(html: string, data: EnhancedPrintData, options: EnhancedPrintOptions): void {
    // إنشاء modal للمعاينة
    const modalOverlay = document.createElement('div')
    modalOverlay.id = `print-preview-modal-${Date.now()}`
    modalOverlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(4px);
    `

    const modalContent = document.createElement('div')
    modalContent.style.cssText = `
      background: white;
      border-radius: 12px;
      width: 90%;
      height: 90%;
      max-width: 1200px;
      max-height: 800px;
      display: flex;
      flex-direction: column;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      overflow: hidden;
    `

    // شريط الأدوات
    const toolbar = document.createElement('div')
    toolbar.style.cssText = `
      padding: 16px 24px;
      border-bottom: 1px solid #e8e8e8;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #fafafa;
    `

    const title = document.createElement('h3')
    title.textContent = `معاينة ${data.title || 'المستند'}`
    title.style.cssText = `
      margin: 0;
      color: #262626;
      font-size: 16px;
      font-weight: 600;
    `

    const buttonGroup = document.createElement('div')
    buttonGroup.style.cssText = `
      display: flex;
      gap: 12px;
      align-items: center;
    `

    // زر الطباعة
    const printBtn = document.createElement('button')
    printBtn.textContent = '🖨️ طباعة'
    printBtn.style.cssText = `
      padding: 8px 16px;
      background: ${options.primaryColor || '#1890ff'};
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s;
    `
    printBtn.onmouseover = () => printBtn.style.background = '#096dd9'
    printBtn.onmouseout = () => printBtn.style.background = options.primaryColor || '#1890ff'
    printBtn.onclick = () => {
      // طباعة في نافذة منفصلة دون إغلاق المعاينة
      this.printFromPreviewSeparate(html, modalOverlay.id)
      // لا نغلق نافذة المعاينة تلقائياً
    }

    // زر الطباعة والإغلاق
    const printCloseBtn = document.createElement('button')
    printCloseBtn.textContent = '🖨️ طباعة وإغلاق'
    printCloseBtn.style.cssText = `
      padding: 8px 16px;
      background: #52c41a;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s;
    `
    printCloseBtn.onmouseover = () => printCloseBtn.style.background = '#389e0d'
    printCloseBtn.onmouseout = () => printCloseBtn.style.background = '#52c41a'
    printCloseBtn.onclick = () => {
      this.printFromPreviewSeparate(html, modalOverlay.id)
      // إغلاق نافذة المعاينة فوراً - نافذة الطباعة ستبقى مستقلة
      this.createTimeout(() => {
        try {
          this.activePreviewModals.delete(modalOverlay.id)
          document.body.removeChild(modalOverlay)
          Logger.info('MasterPrintService', `🖨️ تم إرسال للطباعة وإغلاق المعاينة ${modalOverlay.id}`)
        } catch (error) {
          Logger.warn('MasterPrintService', 'خطأ في إغلاق نافذة المعاينة بعد الطباعة:', error)
        }
      }, 300)
    }

    // زر الإغلاق
    const closeBtn = document.createElement('button')
    closeBtn.textContent = '✕ إغلاق'
    closeBtn.style.cssText = `
      padding: 8px 16px;
      background: #f5f5f5;
      color: #666;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s;
    `
    closeBtn.onmouseover = () => closeBtn.style.background = '#e8e8e8'
    closeBtn.onmouseout = () => closeBtn.style.background = '#f5f5f5'
    closeBtn.onclick = () => {
      // إغلاق نافذة المعاينة فقط - نوافذ الطباعة تبقى مستقلة
      try {
        this.activePreviewModals.delete(modalOverlay.id)
        document.body.removeChild(modalOverlay)
        Logger.info('MasterPrintService', `🔒 تم إغلاق نافذة المعاينة ${modalOverlay.id} - نوافذ الطباعة تبقى مفتوحة`)
      } catch (error) {
        Logger.warn('MasterPrintService', 'خطأ في إغلاق نافذة المعاينة:', error)
      }
    }

    buttonGroup.appendChild(printBtn)
    buttonGroup.appendChild(printCloseBtn)
    buttonGroup.appendChild(closeBtn)
    toolbar.appendChild(title)
    toolbar.appendChild(buttonGroup)

    // منطقة المعاينة
    const previewArea = document.createElement('div')
    previewArea.style.cssText = `
      flex: 1;
      padding: 24px;
      overflow: auto;
      background: #f8f9fa;
    `

    // إنشاء iframe للمعاينة مع CSS مخصص
    const previewFrame = document.createElement('iframe')
    previewFrame.style.cssText = `
      width: 100%;
      height: 600px;
      border: none;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      margin: 0 auto;
      max-width: 800px;
      transform: scale(0.85);
      transform-origin: top center;
      background: white;
    `

    // إنشاء محتوى HTML مع CSS محسن للمعاينة
    const previewHTML = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>معاينة الطباعة</title>
        <style>
          /* إعدادات أساسية للمعاينة */
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
          }

          body {
            margin: 0;
            padding: 20px;
            font-family: ${options.fontFamily || 'Arial'}, sans-serif;
            font-size: ${options.fontSize || 12}px;
            line-height: 1.6;
            color: ${options.textColor || '#000000'};
            background: ${options.backgroundColor || '#ffffff'};
            direction: rtl;
            text-align: right;
          }

          /* تطبيق الألوان المخصصة */
          .header {
            border-bottom: 2px solid ${options.primaryColor || '#1890ff'};
          }

          .company-name {
            color: ${options.primaryColor || '#1890ff'};
          }

          .document-title {
            color: ${options.primaryColor || '#1890ff'};
          }

          .section-title {
            color: ${options.primaryColor || '#1890ff'};
          }

          .items-table th {
            background: ${options.primaryColor || '#1890ff'};
            color: white;
          }

          .items-table td,
          .items-table th {
            border: 1px solid ${options.borderColor || '#d9d9d9'};
          }

          .totals-table .label {
            background: ${options.secondaryColor || '#52c41a'};
          }

          .totals-table .total-row {
            background: ${options.primaryColor || '#1890ff'};
            color: white;
          }

          .footer {
            border-top: 1px solid ${options.borderColor || '#d9d9d9'};
          }

          .watermark {
            color: ${options.primaryColor || '#1890ff'};
            opacity: ${options.watermarkOpacity || 0.1};
          }
        </style>
      </head>
      <body>
        ${html.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')}
      </body>
      </html>
    `

    // تحميل المحتوى في iframe
    previewFrame.onload = () => {
      const doc = previewFrame.contentDocument
      if (doc) {
        doc.open()
        doc.write(previewHTML)
        doc.close()
      }
    }

    previewArea.appendChild(previewFrame)
    modalContent.appendChild(toolbar)
    modalContent.appendChild(previewArea)
    modalOverlay.appendChild(modalContent)

    // تسجيل نافذة المعاينة
    this.activePreviewModals.set(modalOverlay.id, modalOverlay)

    // إضافة للصفحة
    document.body.appendChild(modalOverlay)

    // تحميل المحتوى
    this.createTimeout(() => {
      const doc = previewFrame.contentDocument
      if (doc) {
        doc.open()
        doc.write(previewHTML)
        doc.close()
      }
    }, 100)

    // إغلاق عند النقر خارج المحتوى
    modalOverlay.onclick = (e) => {
      if (e.target === modalOverlay) {
        try {
          this.activePreviewModals.delete(modalOverlay.id)
          document.body.removeChild(modalOverlay)
          Logger.info('MasterPrintService', `🔒 تم إغلاق نافذة المعاينة ${modalOverlay.id} بالنقر خارجها - نوافذ الطباعة تبقى مفتوحة`)
        } catch (error) {
          Logger.warn('MasterPrintService', 'خطأ في إغلاق نافذة المعاينة:', error)
        }
      }
    }

    // إغلاق بمفتاح Escape
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        try {
          this.activePreviewModals.delete(modalOverlay.id)
          document.body.removeChild(modalOverlay)
          document.removeEventListener('keydown', handleEscape)
          Logger.info('MasterPrintService', `🔒 تم إغلاق نافذة المعاينة ${modalOverlay.id} بمفتاح Escape - نوافذ الطباعة تبقى مفتوحة`)
        } catch (error) {
          Logger.warn('MasterPrintService', 'خطأ في إغلاق نافذة المعاينة:', error)
        }
      }
    }
    this.addEventListenerTracked(document, 'keydown', handleEscape)
  }

  /**
   * عرض معاينة فقط بدون فتح نافذة طباعة تلقائياً
   */
  private showPreviewOnlyModal(html: string, data: EnhancedPrintData, options: EnhancedPrintOptions): void {
    // إنشاء modal للمعاينة
    const modalOverlay = document.createElement('div')
    modalOverlay.id = `preview-only-modal-${Date.now()}`
    modalOverlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(4px);
    `

    const modalContent = document.createElement('div')
    modalContent.style.cssText = `
      background: white;
      border-radius: 12px;
      width: 98%;
      height: 98%;
      max-width: none;
      max-height: none;
      display: flex;
      flex-direction: column;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      overflow: hidden;
      resize: both;
      min-width: 900px;
      min-height: 700px;
      position: relative;
    `

    // إضافة مؤشر التوسيع في الزاوية
    const resizeHandle = document.createElement('div')
    resizeHandle.style.cssText = `
      position: absolute;
      bottom: 0;
      right: 0;
      width: 20px;
      height: 20px;
      background: linear-gradient(-45deg, transparent 30%, #ccc 30%, #ccc 40%, transparent 40%, transparent 60%, #ccc 60%, #ccc 70%, transparent 70%);
      cursor: nw-resize;
      z-index: 1000;
    `
    modalContent.appendChild(resizeHandle)

    // شريط الأدوات العلوي
    const toolbar = document.createElement('div')
    toolbar.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background: linear-gradient(135deg, ${options.primaryColor || '#1890ff'}, ${options.secondaryColor || '#40a9ff'});
      color: white;
      border-bottom: 1px solid #e8e8e8;
    `

    const title = document.createElement('h3')
    title.textContent = `📋 معاينة: ${data.title || 'مستند'}`
    title.style.cssText = `
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: white;
    `

    const buttonGroup = document.createElement('div')
    buttonGroup.style.cssText = `
      display: flex;
      gap: 12px;
      align-items: center;
    `

    // زر الطباعة (يفتح نافذة طباعة عند الحاجة)
    const printBtn = document.createElement('button')
    printBtn.textContent = '🖨️ طباعة الآن'
    printBtn.style.cssText = `
      padding: 8px 16px;
      background: #52c41a;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s;
    `
    printBtn.onmouseover = () => printBtn.style.background = '#389e0d'
    printBtn.onmouseout = () => printBtn.style.background = '#52c41a'
    printBtn.onclick = () => {
      // طباعة في نافذة منفصلة عند الطلب فقط
      this.printFromPreviewSeparate(html, modalOverlay.id)
    }

    // زر الإغلاق
    const closeBtn = document.createElement('button')
    closeBtn.textContent = '✕ إغلاق'
    closeBtn.style.cssText = `
      padding: 8px 16px;
      background: #f5f5f5;
      color: #666;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s;
    `
    closeBtn.onmouseover = () => closeBtn.style.background = '#e8e8e8'
    closeBtn.onmouseout = () => closeBtn.style.background = '#f5f5f5'
    closeBtn.onclick = () => {
      try {
        this.activePreviewModals.delete(modalOverlay.id)
        document.body.removeChild(modalOverlay)
        Logger.info('MasterPrintService', `🔒 تم إغلاق نافذة المعاينة ${modalOverlay.id}`)
      } catch (error) {
        Logger.warn('MasterPrintService', 'خطأ في إغلاق نافذة المعاينة:', error)
      }
    }

    buttonGroup.appendChild(printBtn)
    buttonGroup.appendChild(closeBtn)
    toolbar.appendChild(title)
    toolbar.appendChild(buttonGroup)

    // منطقة المعاينة
    const previewArea = document.createElement('div')
    previewArea.style.cssText = `
      flex: 1;
      padding: 16px;
      overflow: auto;
      background: #f8f9fa;
      position: relative;
    `

    // شريط أدوات المعاينة
    const previewToolbar = document.createElement('div')
    previewToolbar.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: white;
      border-radius: 6px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    `

    const zoomControls = document.createElement('div')
    zoomControls.style.cssText = `
      display: flex;
      gap: 8px;
      align-items: center;
    `

    // أزرار التحكم في التكبير
    const zoomOutBtn = document.createElement('button')
    zoomOutBtn.textContent = '🔍-'
    zoomOutBtn.style.cssText = `
      padding: 4px 8px;
      border: 1px solid #d9d9d9;
      background: white;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    `

    const zoomInBtn = document.createElement('button')
    zoomInBtn.textContent = '🔍+'
    zoomInBtn.style.cssText = `
      padding: 4px 8px;
      border: 1px solid #d9d9d9;
      background: white;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    `

    const fitBtn = document.createElement('button')
    fitBtn.textContent = '📐 ملائم'
    fitBtn.style.cssText = `
      padding: 4px 8px;
      border: 1px solid #d9d9d9;
      background: white;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    `

    zoomControls.appendChild(zoomOutBtn)
    zoomControls.appendChild(zoomInBtn)
    zoomControls.appendChild(fitBtn)

    const previewInfo = document.createElement('span')
    previewInfo.textContent = '📄 معاينة تفاعلية - قابلة للتوسيع والتكبير'
    previewInfo.style.cssText = `
      font-size: 12px;
      color: #666;
    `

    previewToolbar.appendChild(previewInfo)
    previewToolbar.appendChild(zoomControls)

    // إنشاء iframe للمعاينة مع CSS محسن
    const previewFrame = document.createElement('iframe')
    previewFrame.style.cssText = `
      width: 100%;
      height: calc(100% - 60px);
      min-height: 500px;
      border: none;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      background: white;
      transform: scale(1);
      transform-origin: top center;
      transition: transform 0.3s ease;
    `

    // إضافة وظائف التكبير
    let currentZoom = 1
    zoomInBtn.onclick = () => {
      currentZoom = Math.min(currentZoom + 0.1, 2)
      previewFrame.style.transform = `scale(${currentZoom})`
    }
    zoomOutBtn.onclick = () => {
      currentZoom = Math.max(currentZoom - 0.1, 0.5)
      previewFrame.style.transform = `scale(${currentZoom})`
    }
    fitBtn.onclick = () => {
      currentZoom = 1
      previewFrame.style.transform = 'scale(1)'
    }

    // إنشاء محتوى HTML مع CSS محسن للمعاينة
    const previewHTML = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>معاينة - ${data.title || 'مستند'}</title>
        <style>
          body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            line-height: 1.6;
          }
          .preview-notice {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 20px;
            text-align: center;
            color: #0050b3;
            font-weight: 500;
          }
        </style>
      </head>
      <body>
        <div class="preview-notice">
          📋 هذه معاينة للمستند - استخدم زر "طباعة الآن" لفتح نافذة الطباعة
        </div>
        ${html.replace(/<html[^>]*>|<\/html>|<head[^>]*>.*?<\/head>|<body[^>]*>|<\/body>/gis, '')}
      </body>
      </html>
    `

    // تحميل المحتوى في iframe
    previewFrame.onload = () => {
      const doc = previewFrame.contentDocument
      if (doc) {
        doc.open()
        doc.write(previewHTML)
        doc.close()
      }
    }

    previewArea.appendChild(previewToolbar)
    previewArea.appendChild(previewFrame)
    modalContent.appendChild(toolbar)
    modalContent.appendChild(previewArea)
    modalOverlay.appendChild(modalContent)

    // تسجيل نافذة المعاينة
    this.activePreviewModals.set(modalOverlay.id, modalOverlay)

    // إضافة للصفحة
    document.body.appendChild(modalOverlay)

    // تحميل المحتوى
    this.createTimeout(() => {
      const doc = previewFrame.contentDocument
      if (doc) {
        doc.open()
        doc.write(previewHTML)
        doc.close()
      }
    }, 100)

    // إغلاق عند النقر خارج المحتوى
    modalOverlay.onclick = (e) => {
      if (e.target === modalOverlay) {
        try {
          this.activePreviewModals.delete(modalOverlay.id)
          document.body.removeChild(modalOverlay)
          Logger.info('MasterPrintService', `🔒 تم إغلاق نافذة المعاينة ${modalOverlay.id} بالنقر خارجها`)
        } catch (error) {
          Logger.warn('MasterPrintService', 'خطأ في إغلاق نافذة المعاينة:', error)
        }
      }
    }

    // إغلاق بمفتاح Escape
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        try {
          this.activePreviewModals.delete(modalOverlay.id)
          document.body.removeChild(modalOverlay)
          document.removeEventListener('keydown', handleEscape)
          Logger.info('MasterPrintService', `🔒 تم إغلاق نافذة المعاينة ${modalOverlay.id} بمفتاح Escape`)
        } catch (error) {
          Logger.warn('MasterPrintService', 'خطأ في إغلاق نافذة المعاينة:', error)
        }
      }
    }
    this.addEventListenerTracked(document, 'keydown', handleEscape)

    Logger.info('MasterPrintService', `👁️ تم فتح نافذة معاينة فقط: ${modalOverlay.id}`)
  }

  /**
   * طباعة مباشرة محسنة
   */
  private directPrint(html: string, options: Partial<EnhancedPrintOptions> = {}): void {
    const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes')
    if (!printWindow) {
      throw new Error('فشل في فتح نافذة الطباعة')
    }

    // تحسين عنوان النافذة
    const printDoc = (printWindow as any).document
    if (printDoc) {
      printDoc.title = `طباعة - ${options.type || 'مستند'}`
      printDoc.write(html)
      printDoc.close()
    }

    const printWin = printWindow as any
    if (printWin.onload !== undefined) {
      printWin.onload = () => {
        // إضافة تأخير أطول للتأكد من تحميل الخطوط والصور
        this.createTimeout(() => {
          printWindow.print()
          if (options.autoClose) {
            // إغلاق النافذة بعد الطباعة
            this.createTimeout(() => {
              printWindow.close()
              this.openWindows.delete(printWindow)
            }, 1000)
          }
        }, 1000)
      }
    }

    // تتبع النافذة المفتوحة
    this.openWindows.add(printWindow)

    // معالجة أخطاء التحميل
    const printWinError = printWindow as any
    if (printWinError.onerror !== undefined) {
      printWinError.onerror = (error: any) => {
        Logger.error('MasterPrintService', 'خطأ في تحميل نافذة الطباعة:', error)
      }
    }
  }

  /**
   * طباعة من المعاينة
   */
  private printFromPreview(html: string): void {
    this.directPrint(html, { autoClose: true })
  }

  /**
   * طباعة من المعاينة في نافذة منفصلة (لا تؤثر على نافذة المعاينة)
   */
  private printFromPreviewSeparate(html: string, previewId?: string): void {
    try {
      // إنشاء نافذة طباعة منفصلة ومعزولة تماماً
      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no')
      if (!printWindow) {
        throw new Error('فشل في فتح نافذة الطباعة')
      }

      // عزل النافذة تماماً عن النافذة الرئيسية
      try {
        // منع الوصول للنافذة الرئيسية
        Object.defineProperty(printWindow, 'opener', {
          value: null,
          writable: false,
          configurable: false
        })

        // منع الوصول للنافذة الأب
        Object.defineProperty(printWindow, 'parent', {
          value: printWindow,
          writable: false,
          configurable: false
        })
      } catch (error) {
        // تجاهل أخطاء الحماية
        Logger.warn('MasterPrintService', 'تحذير: لا يمكن عزل النافذة بالكامل:', error)
      }

      // كتابة المحتوى مع عزل كامل للنافذة
      const printDoc = (printWindow as any).document
      printDoc.title = 'طباعة أمر الإنتاج'

      // إضافة script لعزل النافذة تماماً عن النافذة الرئيسية
      const isolatedHTML = html.replace(
        '</head>',
        `<script>
          // منع تأثير إغلاق هذه النافذة على النافذة الرئيسية
          (function() {
            // إزالة جميع معالجات beforeunload الموروثة
            window.onbeforeunload = null;

            // منع انتشار أحداث الإغلاق للنافذة الرئيسية
            const originalAddEventListener = window.addEventListener;
            window.addEventListener = function(type, listener, options) {
              if (type === 'beforeunload' || type === 'unload') {
                // لا نضيف معالجات الإغلاق التي قد تؤثر على النافذة الرئيسية
                return;
              }
              return originalAddEventListener.call(this, type, listener, options);
            };

            // منع استدعاء cleanup من النافذة الرئيسية
            if (window.parent && window.parent !== window) {
              try {
                delete window.parent.systemInitializer;
                delete window.parent.resourceManager;
              } catch(e) {
                // تجاهل الأخطاء
              }
            }
          })();
        </script>
        </head>`
      );

      printDoc.write(isolatedHTML)
      printDoc.close()

      // متغير لتتبع حالة الطباعة
      let isPrintCompleted = false;

      // انتظار تحميل المحتوى ثم الطباعة
      (printWindow as any).onload = () => {
        this.createTimeout(() => {
          try {
            // تتبع حالة الطباعة
            const originalPrint = (printWindow as any).print;
            (printWindow as any).print = () => {
              isPrintCompleted = true;
              return originalPrint.call(printWindow);
            };

            (printWindow as any).print();

            // مراقبة إغلاق نافذة الطباعة بطريقة آمنة ومعزولة
            const checkWindowStatus = () => {
              try {
                // فحص حالة النافذة بطريقة آمنة
                if ((printWindow as any).closed) {
                  // إذا أُغلقت النافذة دون طباعة، فهذا يعني إلغاء الطباعة
                  if (!isPrintCompleted) {
                    Logger.info('MasterPrintService', '❌ تم إلغاء الطباعة من قبل المستخدم')
                  } else {
                    Logger.info('MasterPrintService', '✅ تم إغلاق نافذة الطباعة بعد إتمام الطباعة')
                  }
                  this.openWindows.delete(printWindow)
                  return
                }
                // استمرار المراقبة
                this.createTimeout(checkWindowStatus, 500)
              } catch (error) {
                // النافذة مُغلقة أو غير متاحة
                Logger.info('MasterPrintService', '🔄 تم إغلاق نافذة الطباعة')
                this.openWindows.delete(printWindow)
              }
            }

            // بدء مراقبة حالة النافذة
            this.createTimeout(checkWindowStatus, 1000)

          } catch (error) {
            Logger.error('MasterPrintService', 'خطأ في الطباعة المنفصلة:', error)
          }
        }, 1000)
      }

      // لا نضيف معالجات beforeunload لنافذة الطباعة لمنع التداخل مع النافذة الرئيسية
      // بدلاً من ذلك، نعتمد على مراقبة حالة النافذة في checkWindowStatus

      // تتبع النافذة
      this.openWindows.add(printWindow)

      Logger.info('MasterPrintService', `🖨️ تم فتح نافذة طباعة منفصلة${previewId ? ` للمعاينة ${previewId}` : ''}`)

    } catch (error) {
      Logger.error('MasterPrintService', 'خطأ في الطباعة المنفصلة:', error)
      throw error
    }
  }

  /**
   * حفظ كـ PDF
   */
  public async saveAsPDF(data: EnhancedPrintData, options: EnhancedPrintOptions): Promise<void> {
    // هذه الوظيفة تحتاج إلى تطبيق خاص في Electron
    Logger.info('MasterPrintService', 'حفظ PDF - يحتاج تطبيق في Electron')
    return this.print(data, { ...options, saveAsPDF: true })
  }

  /**
   * التحقق من صحة بيانات الطباعة
   */
  public validatePrintData(data: EnhancedPrintData): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!data.title) {
      errors.push('عنوان المستند مطلوب')
    }

    // التحقق من صحة الصور
    if (data.images && data.images.length > 0) {
      data.images.forEach((image, index) => {
        if (!image.name) {
          errors.push(`اسم الصورة مطلوب للصورة رقم ${index + 1}`)
        }
        if (!image.path) {
          errors.push(`مسار الصورة مطلوب للصورة رقم ${index + 1}`)
        } else {
          // التحقق من صحة مسار الصورة
          const pathValidation = this.validateImagePath(image.path)
          if (!pathValidation.isValid) {
            errors.push(`مسار الصورة رقم ${index + 1} غير صحيح: ${pathValidation.error}`)
          }
        }

        // التحقق من حجم الصورة إذا كان متوفراً
        if (image.size && image.size > 50 * 1024 * 1024) { // 50MB
          errors.push(`حجم الصورة رقم ${index + 1} كبير جداً (${this.formatFileSize(image.size)}). قد يؤثر على أداء الطباعة`)
        }
      })
    }

    if (data.items && data.items.length > 0) {
      data.items.forEach((item, index) => {
        if (!item.name) {
          errors.push(`اسم الصنف مطلوب للعنصر رقم ${index + 1}`)
        }
        if (typeof item.quantity !== 'number' || item.quantity <= 0) {
          errors.push(`كمية صحيحة مطلوبة للعنصر رقم ${index + 1}`)
        }
        if (typeof item.unitPrice !== 'number' || item.unitPrice < 0) {
          errors.push(`سعر صحيح مطلوب للعنصر رقم ${index + 1}`)
        }
      })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * التحقق من صحة مسار الصورة
   */
  private validateImagePath(imagePath: string): { isValid: boolean; error?: string } {
    if (!imagePath || imagePath.trim() === '') {
      return { isValid: false, error: 'مسار الصورة فارغ' }
    }

    // التحقق من base64
    if (imagePath.startsWith('data:image/')) {
      const parts = imagePath.split(',')
      if (parts.length !== 2) {
        return { isValid: false, error: 'تنسيق base64 غير صحيح' }
      }

      const base64Data = parts[1]
      if (!base64Data || base64Data.length < 100) {
        return { isValid: false, error: 'بيانات base64 قصيرة أو فارغة' }
      }

      // التحقق من صحة header base64
      const header = parts[0]
      if (!header.includes('data:image/')) {
        return { isValid: false, error: 'header base64 غير صحيح' }
      }

      return { isValid: true }
    }

    // التحقق من URL
    try {
      new URL(imagePath)
      return { isValid: true }
    } catch {
      // التحقق من المسار المحلي
      if (imagePath.includes('\\') || imagePath.includes('/')) {
        // مسار ملف محلي - نفترض أنه صحيح
        return { isValid: true }
      }
      return { isValid: false, error: 'مسار الصورة غير صحيح' }
    }
  }

  /**
   * إنشاء صورة بديلة عند فشل تحميل الصورة الأصلية
   */
  private generateFallbackImage(imageName: string, width: number = 200, height: number = 200): string {
    // إنشاء SVG كصورة بديلة
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f5f5f5" stroke="#ddd" stroke-width="2"/>
        <text x="50%" y="40%" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#999">
          صورة غير متوفرة
        </text>
        <text x="50%" y="60%" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">
          ${imageName.substring(0, 20)}${imageName.length > 20 ? '...' : ''}
        </text>
        <circle cx="50%" cy="25%" r="15" fill="#ccc"/>
        <path d="M ${width/2 - 8} ${height/4 - 5} L ${width/2 + 8} ${height/4 - 5} L ${width/2} ${height/4 + 5} Z" fill="#999"/>
      </svg>
    `

    // تحويل SVG إلى base64
    const base64 = btoa(encodeURIComponent(svg).replace(/%([0-9A-F]{2})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16))))
    return `data:image/svg+xml;base64,${base64}`
  }

  /**
   * إنشاء شعار افتراضي عند فشل تحميل الشعار الأصلي
   */
  private generateDefaultLogo(): string {
    // إنشاء SVG كشعار افتراضي
    const svg = `
      <svg width="120" height="80" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#2563eb" rx="8"/>
        <text x="50%" y="35%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
          ZET.IA
        </text>
        <text x="50%" y="65%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="10" fill="#e2e8f0">
          نظام المحاسبة
        </text>
      </svg>
    `

    // تحويل SVG إلى base64
    const base64 = btoa(encodeURIComponent(svg).replace(/%([0-9A-F]{2})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16))))
    return `data:image/svg+xml;base64,${base64}`
  }

  /**
   * إنشاء img tag مع معالجة الأخطاء
   */
  private generateImageTag(image: NonNullable<EnhancedPrintData['images']>[0], style: string, className?: string): string {
    // تسجيل معلومات الصورة للتشخيص
    Logger.info('MasterPrintService', `🖼️ إنشاء img tag للصورة: ${image.name}, مسار: ${image.path}`)

    const pathValidation = this.validateImagePath(image.path)
    const fallbackImage = this.generateFallbackImage(image.name)

    if (!pathValidation.isValid) {
      Logger.warn('MasterPrintService', `استخدام صورة بديلة للصورة: ${image.name} - ${pathValidation.error}`)
      return `<img src="${fallbackImage}" alt="${image.name}" style="${style}" ${className ? `class="${className}"` : ''}>`
    }

    // إنشاء img tag مع onerror fallback
    const imgTag = `
      <img src="${image.path}"
           alt="${image.name}"
           style="${style}"
           ${className ? `class="${className}"` : ''}
           onerror="this.onerror=null; this.src='${fallbackImage}'; console.warn('فشل تحميل الصورة: ${image.name}');">
    `

    Logger.info('MasterPrintService', `✅ تم إنشاء img tag بنجاح للصورة: ${image.name}`)
    return imgTag
  }

  /**
   * إنشاء CSS محسن للطباعة
   */
  private generateEnhancedImageCSS(layout: string, imageQuality: string): string {
    const qualitySettings = {
      low: { resolution: '150dpi', quality: '0.6' },
      medium: { resolution: '300dpi', quality: '0.8' },
      high: { resolution: '600dpi', quality: '0.9' },
      ultra: { resolution: '1200dpi', quality: '0.95' }
    }

    const settings = qualitySettings[imageQuality as keyof typeof qualitySettings] || qualitySettings.high

    return `
      <style>
        .images-section.enhanced-print {
          page-break-inside: avoid;
          print-color-adjust: exact;
          -webkit-print-color-adjust: exact;
        }

        .images-header.enhanced {
          margin-bottom: 20px;
          border-bottom: 2px solid #333;
          padding-bottom: 10px;
        }

        .images-info {
          display: flex;
          gap: 15px;
          font-size: 12px;
          color: #666;
          margin-top: 5px;
        }

        .images-info span {
          padding: 2px 8px;
          background: #f5f5f5;
          border-radius: 4px;
        }

        .enhanced-image {
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
          print-color-adjust: exact;
          -webkit-print-color-adjust: exact;
        }

        @media print {
          .images-section.enhanced-print {
            resolution: ${settings.resolution};
          }

          .enhanced-image {
            max-width: 100% !important;
            height: auto !important;
            page-break-inside: avoid;
          }
        }
      </style>
    `
  }

  /**
   * تخطيط معرض محسن
   */
  private generateGalleryLayout(
    images: NonNullable<EnhancedPrintData['images']>,
    imagesPerPage: number,
    showMetadata: boolean,
    options: EnhancedPrintOptions
  ): string {
    let html = '<div class="gallery-layout">'

    // تقسيم الصور إلى صفحات
    for (let i = 0; i < images.length; i += imagesPerPage) {
      const pageImages = images.slice(i, i + imagesPerPage)

      if (i > 0) {
        html += '<div class="page-break"></div>'
      }

      html += '<div class="gallery-page">'

      // ترتيب الصور في شبكة مرنة
      const cols = Math.ceil(Math.sqrt(pageImages.length))
      const rows = Math.ceil(pageImages.length / cols)

      html += `<div class="gallery-grid" style="display: grid; grid-template-columns: repeat(${cols}, 1fr); gap: 15px; margin: 20px 0;">`

      pageImages.forEach((image, index) => {
        const imageTag = this.generateImageTag(
          image,
          'width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); enhanced-image'
        )

        html += `
          <div class="gallery-item" style="text-align: center;">
            ${imageTag}
            ${showMetadata ? `
              <div class="image-caption" style="margin-top: 8px; font-size: 11px; color: #555;">
                <strong>${image.name}</strong>
                ${image.description ? `<br><span style="color: #777;">${image.description}</span>` : ''}
              </div>
            ` : ''}
          </div>
        `
      })

      html += '</div></div>'
    }

    html += '</div>'
    return html
  }

}
