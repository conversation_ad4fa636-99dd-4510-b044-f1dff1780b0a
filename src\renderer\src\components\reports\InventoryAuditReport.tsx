import React, { useState } from 'react';
import { Tag, Typography, Input, Button, Space, Modal, Form, InputNumber, message } from 'antd';
import { EditOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';
import UniversalReport from './UniversalReport';
import { DateUtils } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger'

const { Text } = Typography;

const InventoryAuditReport: React.FC = () => {
  const [editingKey, setEditingKey] = useState<string>('');
  const [form] = Form.useForm();

  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<any> => {
    try {
      Logger.info('InventoryAuditReport', '🔄 جاري إنشاء تقرير الجرد والمطابقة...')

      let auditData: any[]

      if (!window.electronAPI) {
        Logger.error('InventoryAuditReport', '❌ window.electronAPI غير متوفر')
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      } else {
        // استعلام البيانات من قاعدة البيانات
        const response = await window.electronAPI.getInventoryAuditReport({
          warehouseId: filters.warehouseId,
          categoryId: filters.categoryId
        });

        if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
          throw new Error('لا توجد بيانات متاحة');
        }

        auditData = response.data;
        Logger.info('InventoryAuditReport', '✅ تم جلب بيانات الجرد من قاعدة البيانات')
      }

      // تحضير أعمدة الجدول
      const columns = [
        {
          title: 'كود الصنف',
          dataIndex: 'item_code',
          key: 'item_code',
          width: 120,
          fixed: 'left' as const,
          render: (text: string) => (
            <Text strong style={{ color: '#1890ff' }}>
              {text}
            </Text>
          )
        },
        {
          title: 'اسم الصنف',
          dataIndex: 'item_name',
          key: 'item_name',
          width: 200,
          fixed: 'left' as const,
          render: (text: string) => (
            <Text strong>{text}</Text>
          )
        },
        {
          title: 'الفئة',
          dataIndex: 'category_name',
          key: 'category_name',
          width: 150,
          render: (text: string) => (
            <Tag color="blue">{text}</Tag>
          )
        },
        {
          title: 'المخزن',
          dataIndex: 'warehouse_name',
          key: 'warehouse_name',
          width: 150,
          render: (text: string) => (
            <Tag color="green">{text}</Tag>
          )
        },
        {
          title: 'الوحدة',
          dataIndex: 'unit',
          key: 'unit',
          width: 80,
          align: 'center' as const
        },
        {
          title: 'الكمية في النّام',
          dataIndex: 'system_quantity',
          key: 'system_quantity',
          width: 140,
          align: 'center' as const,
          render: (quantity: number) => (
            <Text strong style={{ color: '#1890ff' }}>
              {quantity ? quantity.toLocaleString() : '0'}
            </Text>
          )
        },
        {
          title: 'الكمية المحجوزة',
          dataIndex: 'reserved_quantity',
          key: 'reserved_quantity',
          width: 120,
          align: 'center' as const,
          render: (quantity: number) => (
            <Text style={{ color: '#faad14' }}>
              {quantity ? quantity.toLocaleString() : '0'}
            </Text>
          )
        },
        {
          title: 'الكمية الفعلية',
          dataIndex: 'physical_quantity',
          key: 'physical_quantity',
          width: 140,
          align: 'center' as const,
          render: (quantity: number, record: any) => {
            const isEditing = editingKey === record.key;
            
            if (isEditing) {
              return (
                <Form.Item
                  name="physical_quantity"
                  style={{ margin: 0 }}
                  rules={[
                    { required: true, message: 'الرجاء إدخال الكمية الفعلية' },
                    { type: 'number', min: 0, message: 'الكمية يجب أن تكون أكبر من أو تساوي صفر' }
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="الكمية الفعلية"
                    precision={2}
                  />
                </Form.Item>
              );
            }
            
            return (
              <Text strong style={{ 
                color: quantity === record.system_quantity ? '#52c41a' : '#f5222d' 
              }}>
                {quantity ? quantity.toLocaleString() : '0'}
              </Text>
            );
          }
        },
        {
          title: 'الفرق',
          dataIndex: 'variance',
          key: 'variance',
          width: 100,
          align: 'center' as const,
          render: (variance: number, record: any) => {
            const actualVariance = (record.physical_quantity || 0) - (record.system_quantity || 0);
            const isPositive = actualVariance > 0;
            const isZero = actualVariance === 0;
            
            return (
              <Text strong style={{ 
                color: isZero ? '#52c41a' : isPositive ? '#1890ff' : '#f5222d' 
              }}>
                {isPositive && !isZero ? '+' : ''}{actualVariance.toLocaleString()}
              </Text>
            );
          }
        },
        {
          title: 'نسبة الفرق %',
          dataIndex: 'variance_percentage',
          key: 'variance_percentage',
          width: 120,
          align: 'center' as const,
          render: (percentage: number, record: any) => {
            const systemQty = record.system_quantity || 0;
            const physicalQty = record.physical_quantity || 0;
            const variancePercentage = systemQty > 0 ? ((physicalQty - systemQty) / systemQty) * 100 : 0;
            
            const isPositive = variancePercentage > 0;
            const isZero = Math.abs(variancePercentage) < 0.01;
            
            return (
              <Text strong style={{ 
                color: isZero ? '#52c41a' : isPositive ? '#1890ff' : '#f5222d' 
              }}>
                {isPositive && !isZero ? '+' : ''}{variancePercentage.toFixed(1)}%
              </Text>
            );
          }
        },
        {
          title: 'حالة الجرد',
          dataIndex: 'audit_status',
          key: 'audit_status',
          width: 120,
          align: 'center' as const,
          render: (status: string, record: any) => {
            const systemQty = record.system_quantity || 0;
            const physicalQty = record.physical_quantity || 0;
            const variance = physicalQty - systemQty;
            
            let statusText = 'مطابق';
            let color = 'green';
            
            if (Math.abs(variance) < 0.01) {
              statusText = 'مطابق';
              color = 'green';
            } else if (variance > 0) {
              statusText = 'زيادة';
              color = 'blue';
            } else {
              statusText = 'نقص';
              color = 'red';
            }
            
            return <Tag color={color}>{statusText}</Tag>;
          }
        },
        {
          title: 'الموقع',
          dataIndex: 'location',
          key: 'location',
          width: 120,
          render: (location: string) => (
            <Text type="secondary">{location || '-'}</Text>
          )
        },
        {
          title: 'آخر تحديث',
          dataIndex: 'last_updated',
          key: 'last_updated',
          width: 140,
          render: (date: string) => (
            <Text type="secondary">
              {date ? new Date(date).toLocaleDateString('ar-SA') : '-'}
            </Text>
          )
        },
        {
          title: 'إجراءات',
          key: 'actions',
          width: 120,
          align: 'center' as const,
          render: (text: any, record: any) => {
            const isEditing = editingKey === record.key;
            
            if (isEditing) {
              return (
                <Space>
                  <Button
                    type="primary"
                    size="small"
                    icon={<CheckOutlined />}
                    onClick={() => handleSave(record.key)}
                  />
                  <Button
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={() => setEditingKey('')}
                  />
                </Space>
              );
            }
            
            return (
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              >
                تعديل
              </Button>
            );
          }
        }
      ];

      // حساب الملخص
      const totalItems = auditData.length;
      const matchingItems = auditData.filter((item: any) => 
        Math.abs((item.physical_quantity || 0) - (item.system_quantity || 0)) < 0.01
      ).length;
      const surplusItems = auditData.filter((item: any) => 
        (item.physical_quantity || 0) > (item.system_quantity || 0)
      ).length;
      const shortageItems = auditData.filter((item: any) => 
        (item.physical_quantity || 0) < (item.system_quantity || 0)
      ).length;
      
      const totalSystemQuantity = auditData.reduce((sum: number, item: any) => 
        sum + (item.system_quantity || 0), 0
      );
      const totalPhysicalQuantity = auditData.reduce((sum: number, item: any) => 
        sum + (item.physical_quantity || 0), 0
      );
      const totalVariance = totalPhysicalQuantity - totalSystemQuantity;

      // إضافة مفاتيح فريدة للبيانات
      const dataWithKeys = auditData.map((item: any, index: number) => ({
        ...item,
        key: `${item.item_id}-${item.warehouse_id}-${index}`,
        variance: (item.physical_quantity || 0) - (item.system_quantity || 0)
      }));

      // تحضير عنوان فرعي
      let subtitle = 'تقرير مطابقة الكميات الفعلية مع الكميات المسجلة في النّام';
      if (filters.warehouseId && window.electronAPI) {
        try {
          const warehouse = await window.electronAPI.getWarehouse(filters.warehouseId);
          subtitle += ` - المخزن: ${warehouse?.name}`;
        } catch (error) {
          Logger.error('InventoryAuditReport', 'خطأ في جلب بيانات المخزن:', error);
        }
      }

      return {
        title: 'تقرير الجرد والمطابقة',
        subtitle,
        columns,
        data: dataWithKeys,
        summary: {
          totalItems,
          matchingItems,
          surplusItems,
          shortageItems,
          totalSystemQuantity: Math.round(totalSystemQuantity * 100) / 100,
          totalPhysicalQuantity: Math.round(totalPhysicalQuantity * 100) / 100,
          totalVariance: Math.round(totalVariance * 100) / 100,
          accuracyPercentage: totalItems > 0 ? Math.round((matchingItems / totalItems) * 100) : 0
        }
      };
    } catch (error) {
      Logger.error('InventoryAuditReport', 'خطأ في إنشاء تقرير الجرد والمطابقة:', error);
      throw new Error('فشل في إنشاء التقرير');
    }
  };

  // تعديل الكمية الفعلية
  const handleEdit = (record: any) => {
    form.setFieldsValue({
      physical_quantity: record.physical_quantity || 0
    });
    setEditingKey(record.key);
  };

  // حفّ التعديل
  const handleSave = async (key: string) => {
    try {
      const _values = await form.validateFields();
      // هنا يمكن إضافة منطق حفّ البيانات في قاعدة البيانات
      message.success('تم تحديث الكمية الفعلية بنجاح');
      setEditingKey('');
    } catch (error) {
      Logger.error('InventoryAuditReport', 'خطأ في حفّ البيانات:', error);
      message.error('فشل في حفّ البيانات');
    }
  };

  return (
    <Form form={form} component={false}>
      <UniversalReport
        reportType="inventory_audit"
        title="تقرير الجرد والمطابقة"
        description="تقرير لمطابقة الكميات الفعلية مع الكميات المسجلة في النظام مع إمكانية التعديل"
        onGenerateReport={generateReport}
        showDateRange={false}
        showWarehouseFilter={true}
        showCategoryFilter={true}
        showItemFilter={false}
        showStatistics={true}
        showSummary={true}
        showExportOptions={true}
        showPrintOptions={true}
        exportFileName="inventory_audit_report"
        defaultFilters={{
          sortBy: 'variance_percentage',
          sortOrder: 'desc'
        }}
      />
    </Form>
  );
};

export default InventoryAuditReport;
