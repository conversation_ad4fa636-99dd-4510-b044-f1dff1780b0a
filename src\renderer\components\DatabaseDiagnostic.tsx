import React, { useState, useEffect } from 'react'
import { <PERSON>, But<PERSON>, Badge, Al<PERSON>, Typo<PERSON>, Space, Divider } from 'antd'
import {
  DatabaseOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  GlobalOutlined,
  HddOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons'

const { Title, Text } = Typography

interface DatabaseStatus {
  path: string
  isNetworkPath: boolean
  exists: boolean
  size: number
  lastModified: string
  tableCount: number
  recordCount: number
  isReadable: boolean
  isWritable: boolean
  error?: string
  details?: string
}

export const DatabaseDiagnostic: React.FC = () => {
  const [status, setStatus] = useState<DatabaseStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchDatabaseStatus = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // محاكاة بيانات حالة قاعدة البيانات
      const mockStatus: DatabaseStatus = {
        path: './database/accounting.db',
        isNetworkPath: false,
        exists: true,
        size: 1024 * 1024 * 5, // 5MB
        lastModified: new Date().toISOString(),
        tableCount: 15,
        recordCount: 1250,
        isReadable: true,
        isWritable: true
      }

      setStatus(mockStatus)
    } catch (err) {
      setError('فشل في الحصول على حالة قاعدة البيانات')
      console.error('Database status error:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDatabaseStatus()
  }, [])

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusIcon = (condition: boolean) => {
    return condition ? (
      <CheckCircleOutlined style={{ color: '#52c41a' }} />
    ) : (
      <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
    )
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const _getStatusBadge = (condition: boolean, trueText: string, falseText: string) => {
    return (
      <Badge color={condition ? "green" : "red"} text={condition ? trueText : falseText} />
    )
  }

  if (loading) {
    return (
      <Card style={{ width: '100%', maxWidth: '1000px', margin: '0 auto' }}>
        <div style={{ padding: '16px' }}>
          <Title level={4} style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <DatabaseOutlined />
            تشخيص قاعدة البيانات
          </Title>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '32px 0' }}>
            <ReloadOutlined spin style={{ marginRight: '8px' }} />
            جاري فحص قاعدة البيانات...
          </div>
        </div>
      </Card>
    )
  }

  if (error) {
    return (
      <Card style={{ width: '100%', maxWidth: '1000px', margin: '0 auto' }}>
        <div style={{ padding: '16px' }}>
          <Title level={4} style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
            <DatabaseOutlined />
            تشخيص قاعدة البيانات
          </Title>
          <Alert
            message="خطأ في فحص قاعدة البيانات"
            description={error}
            type="error"
            icon={<ExclamationCircleOutlined />}
            style={{ marginBottom: '16px' }}
          />
          <Button
            onClick={fetchDatabaseStatus}
            icon={<ReloadOutlined />}
            type="primary"
          >
            إعادة المحاولة
          </Button>
        </div>
      </Card>
    )
  }

  if (!status) {
    return null
  }

  const hasIssues = !status.exists || !status.isReadable || !status.isWritable || status.tableCount === 0

  return (
    <Card style={{ width: '100%', maxWidth: '1000px', margin: '0 auto' }}>
      <div style={{ padding: '16px' }}>
        <Title level={4} style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
          <DatabaseOutlined />
          تشخيص قاعدة البيانات
          {hasIssues && <ExclamationCircleOutlined style={{ color: '#faad14' }} />}
        </Title>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* معلومات المسار */}
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
            <Card size="small">
              <Title level={5} style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                <HddOutlined />
                معلومات المسار
              </Title>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Text strong>المسار:</Text>
                  <Text code style={{ fontSize: '12px' }}>
                    {status.path}
                  </Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Text strong>نوع المسار:</Text>
                  {status.isNetworkPath ? (
                    <Badge color="blue" text="مسار شبكة" />
                  ) : (
                    <Badge color="green" text="مسار محلي" />
                  )}
                </div>
              </Space>
            </Card>

            <Card size="small">
              <Title level={5} style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                <ClockCircleOutlined />
                معلومات الملف
              </Title>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Text strong>الحجم:</Text>
                  <Text>{formatFileSize(status.size)}</Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Text strong>آخر تعديل:</Text>
                  <Text>{new Date(status.lastModified).toLocaleString('ar-SA')}</Text>
                </div>
              </Space>
            </Card>
          </div>

          {/* حالة الوصول */}
          <Card size="small">
            <Title level={5} style={{ marginBottom: '12px' }}>حالة الوصول</Title>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '12px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {getStatusIcon(status.exists)}
                <Text>الملف موجود</Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {getStatusIcon(status.isReadable)}
                <Text>قابل للقراءة</Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {getStatusIcon(status.isWritable)}
                <Text>قابل للكتابة</Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {getStatusIcon(status.tableCount > 0)}
                <Text>يحتوي على جداول</Text>
              </div>
            </div>
          </Card>

          {/* إحصائيات قاعدة البيانات */}
          <Card size="small">
            <Title level={5} style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
              <UserOutlined />
              إحصائيات قاعدة البيانات
            </Title>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '16px' }}>
              <div style={{ backgroundColor: '#e6f7ff', padding: '12px', borderRadius: '8px', textAlign: 'center' }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>{status.tableCount}</div>
                <div style={{ fontSize: '12px', color: '#1890ff' }}>عدد الجداول</div>
              </div>
              <div style={{ backgroundColor: '#f6ffed', padding: '12px', borderRadius: '8px', textAlign: 'center' }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>{status.recordCount.toLocaleString()}</div>
                <div style={{ fontSize: '12px', color: '#52c41a' }}>إجمالي السجلات</div>
              </div>
            </div>
          </Card>

          {/* تحذيرات ومشاكل */}
          {hasIssues && (
            <Alert
              message="تم اكتشاف مشاكل في قاعدة البيانات"
              description={
                <ul style={{ margin: 0, paddingLeft: '20px' }}>
                  {!status.exists && <li>ملف قاعدة البيانات غير موجود</li>}
                  {!status.isReadable && <li>لا يمكن قراءة قاعدة البيانات</li>}
                  {!status.isWritable && <li>لا يمكن الكتابة في قاعدة البيانات</li>}
                  {status.tableCount === 0 && <li>قاعدة البيانات فارغة أو تالفة</li>}
                </ul>
              }
              type="warning"
              icon={<ExclamationCircleOutlined />}
            />
          )}

          {/* أزرار الإجراءات */}
          <div style={{ display: 'flex', gap: '8px', paddingTop: '16px' }}>
            <Button
              onClick={fetchDatabaseStatus}
              icon={<ReloadOutlined />}
              size="small"
            >
              تحديث الحالة
            </Button>
          </div>
        </Space>
      </div>
    </Card>
  )
}

export default DatabaseDiagnostic
