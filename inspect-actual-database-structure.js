const fs = require('fs');
const path = require('path');
const initSqlJs = require('sql.js');

async function inspectActualDatabaseStructure() {
  try {
    console.log('🔍 فحص الهيكل الفعلي لقاعدة البيانات...\n');
    
    const dbPath = path.join(process.env.APPDATA, 'accounting-production-app', 'accounting.db');
    
    if (!fs.existsSync(dbPath)) {
      console.log('❌ قاعدة البيانات غير موجودة:', dbPath);
      return;
    }
    
    const SQL = await initSqlJs({
      locateFile: file => path.join(__dirname, 'node_modules', 'sql.js', 'dist', file)
    });
    
    const filebuffer = fs.readFileSync(dbPath);
    const db = new SQL.Database(filebuffer);
    
    console.log('📋 جميع الجداول في قاعدة البيانات:');
    const tablesResult = db.exec("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name");
    const tables = tablesResult[0]?.values?.map(row => row[0]) || [];
    
    for (const table of tables) {
      console.log(`\n🗂️ جدول: ${table}`);
      
      try {
        const columnsResult = db.exec(`PRAGMA table_info(${table})`);
        if (columnsResult[0]?.values) {
          console.log('   الأعمدة:');
          for (const column of columnsResult[0].values) {
            const [cid, name, type, notnull, dflt_value, pk] = column;
            const nullable = notnull ? 'NOT NULL' : 'NULL';
            const primary = pk ? ' (PRIMARY KEY)' : '';
            const defaultVal = dflt_value ? ` DEFAULT ${dflt_value}` : '';
            console.log(`     - ${name}: ${type} ${nullable}${defaultVal}${primary}`);
          }
        }
        
        // عدد السجلات
        const countResult = db.exec(`SELECT COUNT(*) FROM ${table}`);
        const count = countResult[0]?.values[0]?.[0] || 0;
        console.log(`   📊 عدد السجلات: ${count}`);
        
      } catch (error) {
        console.log(`   ❌ خطأ في فحص الجدول: ${error.message}`);
      }
    }
    
    // فحص خاص لجداول الصور
    console.log('\n' + '='.repeat(80));
    console.log('🖼️ تحليل خاص لجداول الصور:');
    console.log('='.repeat(80));
    
    const imageTables = [
      'production_order_images',
      'unified_images',
      'item_images',
      'customer_images',
      'supplier_images',
      'image_settings'
    ];
    
    for (const table of imageTables) {
      if (tables.includes(table)) {
        console.log(`\n✅ ${table} موجود:`);
        try {
          const columnsResult = db.exec(`PRAGMA table_info(${table})`);
          const columns = columnsResult[0]?.values?.map(row => row[1]) || [];
          console.log(`   📋 الأعمدة: ${columns.join(', ')}`);
          
          const countResult = db.exec(`SELECT COUNT(*) FROM ${table}`);
          const count = countResult[0]?.values[0]?.[0] || 0;
          console.log(`   📊 السجلات: ${count}`);
          
        } catch (error) {
          console.log(`   ❌ خطأ: ${error.message}`);
        }
      } else {
        console.log(`\n❌ ${table} غير موجود`);
      }
    }
    
    // فحص الجداول المرجعية
    console.log('\n' + '='.repeat(80));
    console.log('🔗 تحليل الجداول المرجعية:');
    console.log('='.repeat(80));
    
    const referenceTables = ['products', 'categories', 'users', 'departments', 'warehouses'];
    
    for (const table of referenceTables) {
      if (tables.includes(table)) {
        console.log(`\n✅ ${table} موجود:`);
        try {
          const columnsResult = db.exec(`PRAGMA table_info(${table})`);
          const columns = columnsResult[0]?.values?.map(row => row[1]) || [];
          console.log(`   📋 الأعمدة: ${columns.join(', ')}`);
          
          const countResult = db.exec(`SELECT COUNT(*) FROM ${table}`);
          const count = countResult[0]?.values[0]?.[0] || 0;
          console.log(`   📊 السجلات: ${count}`);
          
          if (count > 0) {
            const sampleResult = db.exec(`SELECT * FROM ${table} LIMIT 3`);
            if (sampleResult[0]?.values) {
              console.log('   📝 عينة من البيانات:');
              for (let i = 0; i < Math.min(3, sampleResult[0].values.length); i++) {
                const row = sampleResult[0].values[i];
                console.log(`     ${i + 1}. ${row.join(' | ')}`);
              }
            }
          }
          
        } catch (error) {
          console.log(`   ❌ خطأ: ${error.message}`);
        }
      } else {
        console.log(`\n❌ ${table} غير موجود`);
      }
    }
    
    // تحليل العلاقات
    console.log('\n' + '='.repeat(80));
    console.log('🔗 تحليل العلاقات والمراجع:');
    console.log('='.repeat(80));
    
    // فحص production_orders
    if (tables.includes('production_orders')) {
      console.log('\n📋 تحليل production_orders:');
      try {
        const columnsResult = db.exec("PRAGMA table_info(production_orders)");
        const columns = columnsResult[0]?.values?.map(row => row[1]) || [];
        
        console.log('   🔍 الأعمدة المرجعية:');
        if (columns.includes('product_id')) {
          console.log('     - product_id: يحتاج جدول products');
        }
        if (columns.includes('department_id')) {
          console.log('     - department_id: يحتاج جدول departments');
        }
        if (columns.includes('created_by')) {
          console.log('     - created_by: يحتاج جدول users');
        }
        
        const countResult = db.exec('SELECT COUNT(*) FROM production_orders');
        const count = countResult[0]?.values[0]?.[0] || 0;
        console.log(`   📊 عدد أوامر الإنتاج: ${count}`);
        
      } catch (error) {
        console.log(`   ❌ خطأ: ${error.message}`);
      }
    }
    
    // فحص items
    if (tables.includes('items')) {
      console.log('\n📦 تحليل items:');
      try {
        const columnsResult = db.exec("PRAGMA table_info(items)");
        const columns = columnsResult[0]?.values?.map(row => row[1]) || [];
        
        console.log('   🔍 الأعمدة المرجعية:');
        if (columns.includes('category_id')) {
          console.log('     - category_id: يحتاج جدول categories');
        }
        if (columns.includes('warehouse_id')) {
          console.log('     - warehouse_id: يحتاج جدول warehouses');
        }
        
        const countResult = db.exec('SELECT COUNT(*) FROM items');
        const count = countResult[0]?.values[0]?.[0] || 0;
        console.log(`   📊 عدد الأصناف: ${count}`);
        
        if (count > 0) {
          const sampleResult = db.exec('SELECT id, code, name FROM items LIMIT 3');
          if (sampleResult[0]?.values) {
            console.log('   📝 عينة من الأصناف:');
            for (const row of sampleResult[0].values) {
              console.log(`     ID: ${row[0]}, Code: ${row[1]}, Name: ${row[2]}`);
            }
          }
        }
        
      } catch (error) {
        console.log(`   ❌ خطأ: ${error.message}`);
      }
    }
    
    db.close();
    
    console.log('\n' + '='.repeat(80));
    console.log('📋 ملخص التحليل:');
    console.log('='.repeat(80));
    console.log(`📊 إجمالي الجداول: ${tables.length}`);
    console.log(`🖼️ جداول الصور الموجودة: ${imageTables.filter(t => tables.includes(t)).length}/${imageTables.length}`);
    console.log(`🔗 الجداول المرجعية الموجودة: ${referenceTables.filter(t => tables.includes(t)).length}/${referenceTables.length}`);
    
    console.log('\n📝 التوصيات:');
    console.log('   1. إنشاء جدول products إذا كان مطلوباً');
    console.log('   2. تعديل هياكل جداول الصور لتتوافق مع الموجود');
    console.log('   3. إنشاء بيانات تجريبية متوافقة مع الهيكل الفعلي');
    
  } catch (error) {
    console.error('❌ خطأ في فحص قاعدة البيانات:', error);
  }
}

// تشغيل فحص الهيكل
inspectActualDatabaseStructure();
