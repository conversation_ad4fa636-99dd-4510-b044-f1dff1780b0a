import { logger as Logger }  from './logger'
/**
 * نّام الصوت للبرنامج
 * يوفر أصوات تفاعلية للأزرار والعمليات المختلفة
 */

export interface AudioSettings {
  enabled: boolean
  volume: number
  clickSound: boolean
  successSound: boolean
  errorSound: boolean
  warningSound: boolean
  notificationSound: boolean
}

export class AudioSystem {
  private settings: AudioSettings
  private audioContext: AudioContext | null = null
  private sounds: Map<string, AudioBuffer> = new Map()

  constructor() {
    this.settings = {
      enabled: true,
      volume: 0.5,
      clickSound: true,
      successSound: true,
      errorSound: true,
      warningSound: true,
      notificationSound: true
    }

    this.loadSettings()
    this.initializeAudioContext()
    this.generateSounds()
  }

  private loadSettings(): void {
    try {
      const savedSettings = localStorage.getItem('audioSettings')
      if (savedSettings) {
        this.settings = { ...this.settings, ...JSON.parse(savedSettings) }
      }
    } catch (error) {
      Logger.warn('AudioSystem', 'خطأ في تحميل إعدادات الصوت:', error)
    }
  }

  private saveSettings(): void {
    try {
      localStorage.setItem('audioSettings', JSON.stringify(this.settings))
    } catch (error) {
      Logger.warn('AudioSystem', 'خطأ في حفّ إعدادات الصوت:', error)
    }
  }

  private initializeAudioContext(): void {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    } catch (error) {
      Logger.warn('AudioSystem', 'لا يمكن تهيئة AudioContext:', error)
    }
  }

  private generateSounds(): void {
    if (!this.audioContext) return

    // صوت النقر
    this.generateClickSound()

    // صوت النجاح
    this.generateSuccessSound()

    // صوت الخطأ
    this.generateErrorSound()

    // صوت التحذير
    this.generateWarningSound()

    // صوت الإشعار
    this.generateNotificationSound()
  }

  private generateClickSound(): void {
    if (!this.audioContext) return

    const buffer = this.audioContext.createBuffer(1, 1000, this.audioContext.sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < 1000; i++) {
      data[i] = Math.sin(2 * Math.PI * 800 * i / this.audioContext.sampleRate) *
                Math.exp(-i / 200) * 0.1
    }

    this.sounds.set('click', buffer)
  }

  private generateSuccessSound(): void {
    if (!this.audioContext) return

    const buffer = this.audioContext.createBuffer(1, 4000, this.audioContext.sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < 4000; i++) {
      const freq1 = 523.25 // C5
      const freq2 = 659.25 // E5
      const freq3 = 783.99 // G5

      data[i] = (
        Math.sin(2 * Math.PI * freq1 * i / this.audioContext.sampleRate) +
        Math.sin(2 * Math.PI * freq2 * i / this.audioContext.sampleRate) +
        Math.sin(2 * Math.PI * freq3 * i / this.audioContext.sampleRate)
      ) * Math.exp(-i / 1000) * 0.05
    }

    this.sounds.set('success', buffer)
  }

  private generateErrorSound(): void {
    if (!this.audioContext) return

    const buffer = this.audioContext.createBuffer(1, 3000, this.audioContext.sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < 3000; i++) {
      const freq = 200 + Math.sin(i / 100) * 50
      data[i] = Math.sin(2 * Math.PI * freq * i / this.audioContext.sampleRate) *
                Math.exp(-i / 800) * 0.1
    }

    this.sounds.set('error', buffer)
  }

  private generateWarningSound(): void {
    if (!this.audioContext) return

    const buffer = this.audioContext.createBuffer(1, 2000, this.audioContext.sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < 2000; i++) {
      const freq = 440 + Math.sin(i / 50) * 100
      data[i] = Math.sin(2 * Math.PI * freq * i / this.audioContext.sampleRate) *
                Math.exp(-i / 600) * 0.08
    }

    this.sounds.set('warning', buffer)
  }

  private generateNotificationSound(): void {
    if (!this.audioContext) return

    const buffer = this.audioContext.createBuffer(1, 2500, this.audioContext.sampleRate)
    const data = buffer.getChannelData(0)

    for (let i = 0; i < 2500; i++) {
      const freq = 880 // A5
      data[i] = Math.sin(2 * Math.PI * freq * i / this.audioContext.sampleRate) *
                Math.exp(-i / 800) * 0.06
    }

    this.sounds.set('notification', buffer)
  }

  public playSound(soundType: 'click' | 'success' | 'error' | 'warning' | 'notification'): void {
    if (!this.settings.enabled || !this.audioContext || !this.sounds.has(soundType)) {
      return
    }

    // التحقق من إعدادات الصوت المحددة
    const soundEnabled = this.getSoundEnabled(soundType)
    if (!soundEnabled) return

    try {
      const source = this.audioContext.createBufferSource()
      const gainNode = this.audioContext.createGain()

      const soundBuffer = this.sounds.get(soundType)
      if (!soundBuffer) return
      source.buffer = soundBuffer
      gainNode.gain.value = this.settings.volume

      source.connect(gainNode)
      gainNode.connect(this.audioContext.destination)

      source.start()
    } catch (error) {
      Logger.warn('AudioSystem', 'خطأ في تشغيل الصوت:', error)
    }
  }

  private getSoundEnabled(soundType: string): boolean {
    switch (soundType) {
      case 'click': return this.settings.clickSound
      case 'success': return this.settings.successSound
      case 'error': return this.settings.errorSound
      case 'warning': return this.settings.warningSound
      case 'notification': return this.settings.notificationSound
      default: return false
    }
  }

  public updateSettings(newSettings: Partial<AudioSettings>): void {
    this.settings = { ...this.settings, ...newSettings }
    this.saveSettings()
  }

  public getSettings(): AudioSettings {
    return { ...this.settings }
  }

  public enable(): void {
    this.settings.enabled = true
    this.saveSettings()
  }

  public disable(): void {
    this.settings.enabled = false
    this.saveSettings()
  }

  public setVolume(volume: number): void {
    this.settings.volume = Math.max(0, Math.min(1, volume))
    this.saveSettings()
  }

  // إضافة مستمعات للأزرار تلقائياً
  public attachToButtons(): void {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement

      if (target.tagName === 'BUTTON' ||
          target.closest('button') ||
          target.classList.contains('ant-btn')) {
        this.playSound('click')
      }
    })
  }

  // تشغيل أصوات للعمليات المختلفة
  public playOperationSound(operation: 'save' | 'delete' | 'edit' | 'add' | 'cancel'): void {
    switch (operation) {
      case 'save':
      case 'add':
        this.playSound('success')
        break
      case 'delete':
        this.playSound('warning')
        break
      case 'edit':
        this.playSound('click')
        break
      case 'cancel':
        this.playSound('click')
        break
    }
  }
}

// إنشاء مثيل واحد للنّام
export const audioSystem = new AudioSystem()

// إتاحة النّام عالمياً
declare global {
  interface Window {
    audioSystem: AudioSystem
  }
}

if (typeof window !== 'undefined') {
  window.audioSystem = audioSystem
}

export default audioSystem