import React, { useState, useEffect } from 'react'
import { Table, Button, Modal, Form, Input, Space, Select, DatePicker, Card, Statistic, Row, Col, Tag, InputNumber, Popconfirm, message } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, ShoppingCartOutlined, FileTextOutlined, EyeOutlined } from '@ant-design/icons'
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig'
import { useCurrentUser } from '../../hooks/useCurrentUser'
import dayjs from 'dayjs'
import { SafeLogger as Logger } from '../../utils/logger'
import UnifiedPrintButton from '../common/UnifiedPrintButton'
import * as XLSX from 'xlsx'

interface PurchaseOrder {
  id: number
  order_number: string
  supplier_id: number
  supplier_name: string
  order_date: string
  expected_date?: string
  status: string
  subtotal: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  notes?: string
  created_by: number
  created_by_name: string
  created_at: string
}

interface Supplier {
  id: number
  code: string
  name: string
  is_active: boolean
}

interface Item {
  id: number
  code: string
  name: string
  unit: string
  cost_price: number
}

interface OrderItem {
  item_id: number
  item_name?: string
  item_code?: string
  warehouse_id: number
  warehouse_name?: string
  quantity: number
  unit_price: number
  total_price: number
  notes?: string
}

interface PurchaseOrderManagementProps {
  onBack: () => void
}

const PurchaseOrderManagement: React.FC<PurchaseOrderManagementProps> = ({ onBack }) => {
  const [orders, setOrders] = useState<PurchaseOrder[]>([])
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [items, setItems] = useState<Item[]>([])
  const [warehouses, setWarehouses] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingOrder, setEditingOrder] = useState<PurchaseOrder | null>(null)
  const [orderItems, setOrderItems] = useState<OrderItem[]>([])
  const [form] = Form.useForm()
  const { userId } = useCurrentUser()

  useEffect(() => {
    loadOrders()
    loadSuppliers()
    loadItems()
    loadWarehouses()
  }, [])

  const loadOrders = async () => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getPurchaseOrders()
        if (response.success && Array.isArray(response.data)) {
          const ordersData = response.data
          setOrders(ordersData)

          // تشخيص البيانات
          console.log('🔍 تشخيص أوامر الشراء:', {
            totalOrders: ordersData.length,
            sampleOrder: ordersData[0] || null,
            loading: false
          })
        } else {
          Logger.error('PurchaseOrderManagement', 'خطأ في استجابة أوامر الشراء:', response as any)
          setOrders([])
        }
      }
    } catch (error) {
      Logger.error('PurchaseOrderManagement', 'خطأ في تحميل أوامر الشراء:', error as Error)
      message.error('فشل في تحميل أوامر الشراء')
      setOrders([])
    } finally {
      setLoading(false)
    }
  }

  const loadSuppliers = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getSuppliers()
        if (response.success) {
          setSuppliers(response.data.filter((s: Supplier) => s.is_active))
        } else {
          message.error('فشل في تحميل الموردين')
        }
      }
    } catch (error) {
      message.error('فشل في تحميل الموردين')
    }
  }

  const loadItems = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getItems()
        if (response && typeof response === 'object' && 'success' in response && (response as any).success) {
          // الاستجابة الجديدة: { success: true, data: items }
          const itemsData = (response as any).data || []
          setItems(itemsData.filter((i: any) => i.is_active !== false))
          Logger.info('PurchaseOrderManagement', '✅ تم تحميل ' + itemsData.length + ' صنف بنجاح')
        } else if (Array.isArray(response)) {
          // للتوافق مع الإصدارات القديمة
          setItems(response.filter((i: any) => i.is_active !== false))
          Logger.info('PurchaseOrderManagement', '✅ تم تحميل ' + response.length + ' صنف بنجاح')
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل الأصناف'
          Logger.error('PurchaseOrderManagement', '❌ خطأ في استجابة الأصناف:', errorMessage)
          message.error(errorMessage)
          setItems([])
        }
      } else {
        Logger.warn('PurchaseOrderManagement', '⚠️ window.electronAPI غير متوفر')
        message.warning('لا يمكن تحميل الأصناف في وضع التطوير')
        setItems([])
      }
    } catch (error) {
      Logger.error('PurchaseOrderManagement', 'خطأ في تحميل الأصناف:', error as Error)
      message.error('فشل في تحميل الأصناف')
      setItems([])
    }
  }

  const loadWarehouses = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getWarehouses()
        if (response && typeof response === 'object' && 'success' in response && (response as any).success) {
          const warehousesData = (response as any).data || []
          setWarehouses(warehousesData.filter((w: any) => w.is_active !== false))
          Logger.info('PurchaseOrderManagement', '✅ تم تحميل ' + warehousesData.length + ' مخزن بنجاح')
        } else if (Array.isArray(response)) {
          setWarehouses(response.filter((w: any) => w.is_active !== false))
          Logger.info('PurchaseOrderManagement', '✅ تم تحميل ' + response.length + ' مخزن بنجاح')
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل المخازن'
          Logger.error('PurchaseOrderManagement', '❌ خطأ في استجابة المخازن:', errorMessage)
          message.error(errorMessage)
          setWarehouses([])
        }
      } else {
        Logger.warn('PurchaseOrderManagement', '⚠️ window.electronAPI غير متوفر')
        setWarehouses([])
      }
    } catch (error) {
      Logger.error('PurchaseOrderManagement', 'خطأ في تحميل المخازن:', error as Error)
      message.error('فشل في تحميل المخازن')
      setWarehouses([])
    }
  }

  const generateOrderNumber = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generatePurchaseOrderNumber()
        if (response.success && response.data) {
          form.setFieldsValue({ order_number: response.data.orderNumber })
          message.success('تم إنشاء رقم الأمر تلقائياً')
        }
      }
    } catch (error) {
      message.error('فشل في إنشاء رقم الأمر')
    }
  }

  const calculateTotals = () => {
    const subtotal = orderItems.reduce((sum, item) => sum + item.total_price, 0)
    const taxAmount = subtotal * 0.16 // ضريبة 16%
    const discountAmount = form.getFieldValue('discount_amount') || 0
    const totalAmount = subtotal + taxAmount - discountAmount

    form.setFieldsValue({
      subtotal,
      tax_amount: taxAmount,
      total_amount: totalAmount
    })
  }

  const addOrderItem = () => {
    const newItem: OrderItem = {
      item_id: 0,
      warehouse_id: 0,
      quantity: 1,
      unit_price: 0,
      total_price: 0
    }
    setOrderItems([...orderItems, newItem])
  }

  const updateOrderItem = (index: number, field: keyof OrderItem, value: any) => {
    const updatedItems = [...orderItems]
    updatedItems[index] = { ...updatedItems[index], [field]: value }

    if (field === 'item_id') {
      const selectedItem = items.find(item => item.id === value)
      if (selectedItem) {
        updatedItems[index].item_name = selectedItem.name
        updatedItems[index].item_code = selectedItem.code
        updatedItems[index].unit_price = selectedItem.cost_price || 0
      }
    }

    if (field === 'quantity' || field === 'unit_price') {
      updatedItems[index].total_price = updatedItems[index].quantity * updatedItems[index].unit_price
    }

    setOrderItems(updatedItems)
    setTimeout(calculateTotals, 100)
  }

  const removeOrderItem = (index: number) => {
    const updatedItems = orderItems.filter((_, i) => i !== index)
    setOrderItems(updatedItems)
    setTimeout(calculateTotals, 100)
  }

  const handleSubmit = async (values: any) => {
    if (orderItems.length === 0) {
      message.error('يجب إضافة صنف واحد على الأقل')
      return
    }

    // التحقق من صحة التواريخ
    if (values.expected_date && values.order_date) {
      const orderDate = dayjs(values.order_date)
      const expectedDate = dayjs(values.expected_date)

      if (expectedDate.isBefore(orderDate)) {
        message.error('تاريخ التسليم المتوقع لا يمكن أن يكون قبل تاريخ الأمر')
        return
      }

      if (expectedDate.isBefore(dayjs(), 'day')) {
        message.warning('تاريخ التسليم المتوقع في الماضي. هل تريد المتابعة؟')
      }
    }

    // التحقق من صحة الكميات والأسعار
    for (let i = 0; i < orderItems.length; i++) {
      const item = orderItems[i]
      if (item.quantity <= 0) {
        message.error('الكمية يجب أن تكون أكبر من صفر في السطر ' + (i + 1))
        return
      }
      if (item.unit_price <= 0) {
        message.error('سعر الوحدة يجب أن يكون أكبر من صفر في السطر ' + (i + 1))
        return
      }
      // التحقق من الكميات العشرية المنطقية
      if (item.quantity % 0.001 !== 0) {
        message.error('الكمية في السطر ' + (i + 1) + ' تحتوي على دقة عشرية غير مناسبة')
        return
      }
    }

    // التحقق من الخصم
    const discountAmount = form.getFieldValue('discount_amount') || 0
    const subtotal = orderItems.reduce((sum, item) => sum + item.total_price, 0)

    if (discountAmount > subtotal) {
      message.error('مبلغ الخصم لا يمكن أن يكون أكبر من المجموع الفرعي')
      return
    }

    if (discountAmount < 0) {
      message.error('مبلغ الخصم لا يمكن أن يكون سالباً')
      return
    }

    try {
      if (window.electronAPI) {
        const orderData = {
          ...values,
          supplier_id: parseInt(String(values.supplier_id)), // تحويل معرف المورد إلى رقم صحيح
          order_date: values.order_date ? values.order_date.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          expected_date: values.expected_date ? values.expected_date.format('YYYY-MM-DD') : null,
          items: orderItems,
          created_by: userId
        }

        let response
        if (editingOrder) {
          response = await window.electronAPI.updatePurchaseOrder(editingOrder.id, orderData)
        } else {
          response = await window.electronAPI.createPurchaseOrder(orderData)
        }

        if (response.success) {
          message.success(editingOrder ? 'تم تحديث أمر الشراء بنجاح' : 'تم إنشاء أمر الشراء بنجاح')
          setModalVisible(false)
          form.resetFields()
          setEditingOrder(null)
          setOrderItems([])
          loadOrders()
        } else {
          message.error(response.message || 'فشل في حفّ أمر الشراء')
        }
      }
    } catch (error) {
      message.error('حدث خطأ أثناء حفّ أمر الشراء')
    }
  }

  const loadOrderItems = async (orderId: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getPurchaseOrderItems(orderId)
        if (response.success && Array.isArray(response.data)) {
          const items = response.data.map((item: any) => ({
            item_id: item.item_id,
            item_name: item.item_name,
            item_code: item.item_code,
            warehouse_id: item.warehouse_id || 0,
            warehouse_name: item.warehouse_name,
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.total_price,
            notes: item.notes
          }))
          setOrderItems(items)

          // تحديث المجاميع
          setTimeout(calculateTotals, 100)
        } else {
          Logger.error('PurchaseOrderManagement', 'خطأ في استجابة أصناف الأمر:', response as any)
          setOrderItems([])
        }
      }
    } catch (error) {
      Logger.error('PurchaseOrderManagement', 'خطأ في تحميل أصناف الأمر:', error as Error)
      message.error('فشل في تحميل أصناف الأمر')
      setOrderItems([])
    }
  }

  const handleEdit = async (order: PurchaseOrder) => {
    setEditingOrder(order)
    form.setFieldsValue({
      ...order,
      order_date: order.order_date ? dayjs(order.order_date) : dayjs(),
      expected_date: order.expected_date ? dayjs(order.expected_date) : null
    })

    // تحميل أصناف الأمر
    await loadOrderItems(order.id)
    setModalVisible(true)
  }

  const handleDelete = async (orderId: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.deletePurchaseOrder(orderId)
        if (response.success) {
          message.success('تم حذف أمر الشراء بنجاح')
          loadOrders()
        } else {
          message.error(response.message || 'فشل في حذف أمر الشراء')
        }
      }
    } catch (error) {
      message.error('حدث خطأ أثناء حذف أمر الشراء')
    }
  }

  const handleAdd = () => {
    setEditingOrder(null)
    form.resetFields()
    form.setFieldsValue({
      order_date: dayjs(),
      status: 'pending',
      subtotal: 0,
      tax_amount: 0,
      discount_amount: 0,
      total_amount: 0
    })
    setOrderItems([])
    generateOrderNumber()
    setModalVisible(true)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'confirmed': return 'blue'
      case 'received': return 'green'
      case 'cancelled': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'معلق'
      case 'confirmed': return 'مؤكد'
      case 'received': return 'مستلم'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  const getOrderStats = () => {
    const total = orders.length
    const pending = orders.filter(o => o.status === 'pending').length
    const confirmed = orders.filter(o => o.status === 'confirmed').length
    const received = orders.filter(o => o.status === 'received').length
    const totalAmount = orders.reduce((sum, o) => sum + o.total_amount, 0)

    return { total, pending, confirmed, received, totalAmount }
  }

  const stats = getOrderStats()

  // دالة تصدير Excel محسنة
  const handleExportExcel = () => {
    try {
      if (orders.length === 0) {
        message.warning('لا توجد أوامر شراء للتصدير')
        return
      }

      // تحضير البيانات للتصدير
      const exportData = orders.map(order => ({
        'رقم الأمر': order.order_number,
        'المورد': order.supplier_name,
        'تاريخ الأمر': new Date(order.order_date).toLocaleDateString('ar-EG'),
        'التاريخ المتوقع': order.expected_date ? new Date(order.expected_date).toLocaleDateString('ar-EG') : '',
        'الحالة': getStatusText(order.status),
        'المجموع الفرعي': order.subtotal,
        'الضريبة': order.tax_amount,
        'الخصم': order.discount_amount,
        'الإجمالي': order.total_amount,
        'الملاحّات': order.notes || '',
        'المنشئ': order.created_by_name,
        'تاريخ الإنشاء': new Date(order.created_at).toLocaleDateString('ar-EG')
      }))

      // إنشاء ورقة العمل
      const worksheet = XLSX.utils.json_to_sheet(exportData)

      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 15 }, // رقم الأمر
        { wch: 25 }, // المورد
        { wch: 15 }, // تاريخ الأمر
        { wch: 15 }, // التاريخ المتوقع
        { wch: 10 }, // الحالة
        { wch: 15 }, // المجموع الفرعي
        { wch: 10 }, // الضريبة
        { wch: 10 }, // الخصم
        { wch: 15 }, // الإجمالي
        { wch: 30 }, // الملاحّات
        { wch: 20 }, // المنشئ
        { wch: 15 }  // تاريخ الإنشاء
      ]
      worksheet['!cols'] = columnWidths

      // إنشاء المصنف
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'أوامر الشراء')

      // إضافة ورقة معلومات التقرير
      const reportInfo = [
        ['تقرير أوامر الشراء'],
        [''],
        ['إجمالي الأوامر:', orders.length],
        ['الأوامر المعلقة:', stats.pending],
        ['الأوامر المؤكدة:', stats.confirmed],
        ['الأوامر المستلمة:', stats.received],
        ['إجمالي القيمة:', stats.totalAmount.toLocaleString()],
        [''],
        ['تاريخ التصدير:', new Date().toLocaleDateString('ar-EG')],
        ['وقت التصدير:', new Date().toLocaleTimeString('ar-EG')]
      ]

      const infoWorksheet = XLSX.utils.aoa_to_sheet(reportInfo)
      XLSX.utils.book_append_sheet(workbook, infoWorksheet, 'معلومات التقرير')

      // تحديد اسم الملف
      const timestamp = new Date().toISOString().split('T')[0]
      const fileName = 'تقرير_أوامر_الشراء_' + timestamp + '.xlsx'

      // تحميل الملف
      XLSX.writeFile(workbook, fileName)

      message.success('تم تصدير ' + orders.length + ' أمر شراء بنجاح إلى ملف Excel')
    } catch (error) {
      Logger.error('PurchaseOrderManagement', 'خطأ في تصدير Excel:', error)
      message.error('حدث خطأ أثناء تصدير البيانات')
    }
  }

  const columns = [
    {
      title: 'رقم الأمر',
      dataIndex: 'order_number',
      key: 'order_number',
      width: 120,
      render: (orderNumber: string) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{orderNumber}</span>
      )
    },
    {
      title: 'المورد',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      render: (name: string) => (
        <Space>
          <ShoppingCartOutlined style={{ color: '#1890ff' }} />
          {name}
        </Space>
      )
    },
    {
      title: 'تاريخ الأمر (ميلادي)',
      dataIndex: 'order_date',
      key: 'order_date',
      width: 140,
      render: (date: string) => DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE)
    },
    {
      title: 'التاريخ المتوقع (ميلادي)',
      dataIndex: 'expected_date',
      key: 'expected_date',
      width: 140,
      render: (date: string) => date ? DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE) : '-'
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'المبلغ الإجمالي (₪)',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 140,
      render: (amount: number) => (
        <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
          {amount.toLocaleString()}
        </span>
      )
    },
    {
      title: 'المنشئ',
      dataIndex: 'created_by_name',
      key: 'created_by_name',
      width: 120
    },
    {
      title: 'تاريخ الإنشاء (ميلادي)',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 140,
      render: (date: string) => DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE)
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 200,
      render: (_, record: PurchaseOrder) => (
        <Space>
          <UnifiedPrintButton
            data={{
              title: `أمر شراء رقم ${record.order_number}`,
              subtitle: `المورد: ${record.supplier_name}`,
              data: [record],
              summary: {
                totalItems: 0, // سيتم تحديثه عند تحميل تفاصيل الأمر
                totalAmount: record.total_amount || 0,
                status: record.status
              },
              metadata: {
                generatedAt: new Date().toISOString(),
                generatedBy: 'نظام المشتريات',
                orderNumber: record.order_number
              }
            }}
            type="order"
            subType="purchase"
            title="طباعة أمر الشراء"
            buttonText=""
            size="small"
            buttonType="default"
            showDropdown={true}
            icon={<FileTextOutlined />}
          />
          <Button
            type="link"
            icon={<EyeOutlined />}
            size="small"
            title="عرض"
          />
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
            title="تعديل"
          />
          <Popconfirm
            title="هل أنت متأكد من حذف هذا الأمر؟"
            onConfirm={() => handleDelete(record.id)}
            okText="نعم"
            cancelText="لا"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
              title="حذف"
            />
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>إدارة أوامر الشراء</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>إنشاء ومتابعة أوامر الشراء من الموردين</p>
        </div>
        <Space>
          <Button onClick={onBack}>رجوع</Button>

          <UnifiedPrintButton
            data={{
              title: 'تقرير أوامر الشراء',
              subtitle: `إجمالي الأوامر: ${orders.length}`,
              data: orders,
              summary: {
                totalOrders: orders.length,
                pendingOrders: orders.filter(o => o.status === 'pending').length,
                approvedOrders: orders.filter(o => o.status === 'approved').length,
                receivedOrders: orders.filter(o => o.status === 'received').length,
                totalAmount: orders.reduce((sum, o) => sum + (o.total_amount || 0), 0)
              },
              metadata: {
                generatedAt: new Date().toISOString(),
                generatedBy: 'نظام المشتريات',
                reportType: 'أوامر الشراء'
              }
            }}
            type="report"
            subType="purchase"
            title="طباعة تقرير أوامر الشراء"
            buttonText="طباعة التقرير"
            size="middle"
            showDropdown={true}
            showExportOptions={true}
            showSettings={true}
          />
          <Button
            icon={<FileTextOutlined />}
            onClick={handleExportExcel}
          >
            تصدير Excel
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            أمر شراء جديد
          </Button>
        </Space>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={5}>
          <Card>
            <Statistic
              title="إجمالي الأوامر"
              value={stats.total}
              valueStyle={{ color: '#1890ff' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="أوامر معلقة"
              value={stats.pending}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="أوامر مؤكدة"
              value={stats.confirmed}
              valueStyle={{ color: '#1890ff' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="أوامر مستلمة"
              value={stats.received}
              valueStyle={{ color: '#52c41a' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="إجمالي القيمة"
              value={stats.totalAmount}
              valueStyle={{ color: '#52c41a' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      <Table
        columns={columns}
        dataSource={orders}
        rowKey="id"
        loading={loading}
        pagination={{
          total: orders.length,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => range[0] + '-' + range[1] + ' من ' + total + ' أمر'
        }}
        scroll={{ x: 1400 }}
        locale={{
          emptyText: orders.length === 0 ? 'لا توجد أوامر شراء' : 'لا توجد بيانات'
        }}
      />

      <Modal
        title={editingOrder ? 'تعديل أمر الشراء' : 'أمر شراء جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
          setEditingOrder(null)
          setOrderItems([])
        }}
        footer={null}
        width={1000}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                label="رقم الأمر"
                name="order_number"
                rules={[{ required: true, message: 'يرجى إدخال رقم الأمر' }]}
              >
                <Input
                  placeholder="رقم الأمر"
                  suffix={
                    !editingOrder && (
                      <Button type="link" size="small" onClick={generateOrderNumber}>
                        إنشاء تلقائي
                      </Button>
                    )
                  }
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="المورد"
                name="supplier_id"
                rules={[{ required: true, message: 'يرجى اختيار المورد' }]}
              >
                <Select placeholder="اختر المورد">
                  {suppliers.map(supplier => (
                    <Select.Option key={supplier.id} value={supplier.id}>
                      {supplier.name} ({supplier.code})
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="تاريخ الأمر"
                name="order_date"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الأمر' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="اختر التاريخ"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="التاريخ المتوقع للاستلام"
                name="expected_date"
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="اختر التاريخ"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="ملاحّات" name="notes">
            <Input.TextArea placeholder="ملاحّات إضافية" rows={2} />
          </Form.Item>

          {/* جدول الأصناف */}
          <div style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
              <h4>أصناف الأمر</h4>
              <Button type="dashed" onClick={addOrderItem} icon={<PlusOutlined />}>
                إضافة صنف
              </Button>
            </div>
            
            <Table
              dataSource={orderItems}
              pagination={false}
              size="small"
              columns={[
                {
                  title: 'الصنف',
                  key: 'item',
                  render: (_, record, index) => (
                    <Select
                      placeholder="اختر الصنف"
                      style={{ width: '100%' }}
                      value={record.item_id || undefined}
                      onChange={(value) => updateOrderItem(index, 'item_id', value)}
                    >
                      {items.map(item => (
                        <Select.Option key={item.id} value={item.id}>
                          {item.name} ({item.code})
                        </Select.Option>
                      ))}
                    </Select>
                  )
                },
                {
                  title: 'المخزن',
                  key: 'warehouse',
                  width: 150,
                  render: (_, record, index) => (
                    <Select
                      placeholder="اختر المخزن"
                      style={{ width: '100%' }}
                      value={record.warehouse_id || undefined}
                      onChange={(value) => updateOrderItem(index, 'warehouse_id', value)}
                    >
                      {warehouses.map(warehouse => (
                        <Select.Option key={warehouse.id} value={warehouse.id}>
                          {warehouse.name} ({warehouse.code})
                        </Select.Option>
                      ))}
                    </Select>
                  )
                },
                {
                  title: 'الكمية',
                  key: 'quantity',
                  width: 100,
                  render: (_, record, index) => (
                    <InputNumber
                      min={0}
                      value={record.quantity}
                      onChange={(value) => updateOrderItem(index, 'quantity', value || 0)}
                      style={{ width: '100%' }}
                    />
                  )
                },
                {
                  title: 'سعر الوحدة (₪)',
                  key: 'unit_price',
                  width: 120,
                  render: (_, record, index) => (
                    <InputNumber
                      min={0}
                      value={record.unit_price}
                      onChange={(value) => updateOrderItem(index, 'unit_price', value || 0)}
                      style={{ width: '100%' }}
                    />
                  )
                },
                {
                  title: 'الإجمالي (₪)',
                  key: 'total_price',
                  width: 120,
                  render: (_, record) => record.total_price.toLocaleString()
                },
                {
                  title: 'ملاحّات',
                  key: 'notes',
                  render: (_, record, index) => (
                    <Input
                      placeholder="ملاحّات"
                      value={record.notes}
                      onChange={(e) => updateOrderItem(index, 'notes', e.target.value)}
                    />
                  )
                },
                {
                  title: 'إجراءات',
                  key: 'actions',
                  width: 80,
                  render: (_, record, index) => (
                    <Button
                      type="link"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removeOrderItem(index)}
                      size="small"
                    />
                  )
                }
              ]}
            />
          </div>

          {/* ملخص المبالغ */}
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label="المجموع الفرعي (₪)" name="subtotal">
                <InputNumber style={{ width: '100%' }} disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="الضريبة (₪)" name="tax_amount">
                <InputNumber style={{ width: '100%' }} disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="الخصم (₪)" name="discount_amount">
                <InputNumber 
                  style={{ width: '100%' }} 
                  min={0}
                  onChange={calculateTotals}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="الإجمالي النهائي (₪)" name="total_amount">
                <InputNumber style={{ width: '100%' }} disabled />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {editingOrder ? 'تحديث' : 'إنشاء'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default PurchaseOrderManagement
