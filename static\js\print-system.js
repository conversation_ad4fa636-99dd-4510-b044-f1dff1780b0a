/**
 * ===================================
 * نظام طباعة احترافي متكامل
 * Professional Print System
 * ===================================
 */

class PrintSystem {
    constructor() {
        this.printOptions = {
            orientation: 'portrait',
            paperSize: 'A4',
            margins: {
                top: '1.5cm',
                right: '1cm',
                bottom: '1cm',
                left: '1.5cm'
            },
            includeBackground: true,
            scale: 1
        };
        
        this.init();
    }

    /**
     * تهيئة نظام الطباعة
     */
    init() {
        this.loadPrintCSS();
        this.setupPrintButtons();
        this.setupKeyboardShortcuts();
    }

    /**
     * تحميل CSS الطباعة
     */
    loadPrintCSS() {
        if (!document.querySelector('link[href*="print.css"]')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.type = 'text/css';
            link.href = '/static/css/print.css';
            link.media = 'print';
            document.head.appendChild(link);
        }
    }

    /**
     * إعداد أزرار الطباعة
     */
    setupPrintButtons() {
        // إزالة أزرار الطباعة القديمة
        document.querySelectorAll('[onclick*="window.print"]').forEach(btn => {
            btn.removeAttribute('onclick');
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showPrintDialog();
            });
        });

        // إضافة أزرار طباعة جديدة
        document.querySelectorAll('.print-btn, .btn-print').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showPrintDialog();
            });
        });
    }

    /**
     * إعداد اختصارات لوحة المفاتيح
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                this.showPrintDialog();
            }
        });
    }

    /**
     * عرض نافذة خيارات الطباعة
     */
    showPrintDialog() {
        const modal = this.createPrintModal();
        document.body.appendChild(modal);
        
        // إظهار النافذة
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }

    /**
     * إنشاء نافذة خيارات الطباعة
     */
    createPrintModal() {
        const modal = document.createElement('div');
        modal.className = 'print-modal';
        modal.innerHTML = `
            <div class="print-modal-content">
                <div class="print-modal-header">
                    <h3>خيارات الطباعة</h3>
                    <button class="print-modal-close">&times;</button>
                </div>
                <div class="print-modal-body">
                    <div class="print-options">
                        <div class="option-group">
                            <label>اتجاه الصفحة:</label>
                            <select id="print-orientation">
                                <option value="portrait">عمودي</option>
                                <option value="landscape">أفقي</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <label>حجم الورق:</label>
                            <select id="print-paper-size">
                                <option value="A4">A4</option>
                                <option value="A3">A3</option>
                                <option value="Letter">Letter</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <label>نطاق الطباعة:</label>
                            <select id="print-range">
                                <option value="all">جميع الصفحات</option>
                                <option value="current">الصفحة الحالية</option>
                                <option value="selection">المحدد</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="print-background" checked>
                                طباعة الخلفيات والألوان
                            </label>
                        </div>
                        
                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="print-headers" checked>
                                طباعة الرؤوس والتذييلات
                            </label>
                        </div>
                    </div>
                    
                    <div class="print-preview">
                        <h4>معاينة الطباعة</h4>
                        <div class="preview-container">
                            <iframe id="print-preview-frame"></iframe>
                        </div>
                    </div>
                </div>
                <div class="print-modal-footer">
                    <button class="btn btn-primary" onclick="printSystem.executePrint()">
                        <i class="fa fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-secondary" onclick="printSystem.generatePDF()">
                        <i class="fa fa-file-pdf"></i> حفظ كـ PDF
                    </button>
                    <button class="btn btn-default" onclick="printSystem.closePrintModal()">
                        إلغاء
                    </button>
                </div>
            </div>
        `;

        // إضافة أحداث النافذة
        modal.querySelector('.print-modal-close').addEventListener('click', () => {
            this.closePrintModal();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closePrintModal();
            }
        });

        // تحديث المعاينة عند تغيير الخيارات
        modal.querySelectorAll('select, input').forEach(input => {
            input.addEventListener('change', () => {
                this.updatePreview();
            });
        });

        // تحديث المعاينة الأولية
        setTimeout(() => {
            this.updatePreview();
        }, 100);

        return modal;
    }

    /**
     * تحديث معاينة الطباعة
     */
    updatePreview() {
        const previewFrame = document.getElementById('print-preview-frame');
        if (!previewFrame) return;

        // إنشاء محتوى المعاينة
        const printContent = this.preparePrintContent();
        
        // إنشاء مستند HTML للمعاينة
        const previewDoc = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>معاينة الطباعة</title>
                <link rel="stylesheet" href="/static/css/print.css">
                <style>
                    body { 
                        transform: scale(0.7); 
                        transform-origin: top right;
                        margin: 0;
                        padding: 10px;
                    }
                </style>
            </head>
            <body>
                ${printContent}
            </body>
            </html>
        `;

        // تحديث إطار المعاينة
        previewFrame.srcdoc = previewDoc;
    }

    /**
     * تحضير محتوى الطباعة
     */
    preparePrintContent() {
        // البحث عن المحتوى المحدد للطباعة أولاً
        let printableContent = document.querySelector('.printable-content, .invoice-content, .receipt-content, .report-content');

        if (!printableContent) {
            // إذا لم يوجد محتوى محدد، استخدم الجسم الكامل
            printableContent = document.body;
        }

        // نسخ المحتوى
        const content = printableContent.cloneNode(true);

        // إزالة العناصر غير المرغوبة بشكل أكثر شمولية
        const elementsToRemove = [
            '.no-print', '.print-modal', '.btn', '.navbar', '.sidebar',
            '.ant-btn', '.ant-dropdown', '.ant-pagination', '.ant-table-pagination',
            '.ant-table-filter-trigger', '.ant-table-column-sorter', '.actions-column',
            'button', 'input[type="button"]', 'input[type="submit"]', '.form-control',
            '.modal', '.dropdown', '.breadcrumb', '.alert', '.tooltip', '.popover'
        ];

        elementsToRemove.forEach(selector => {
            content.querySelectorAll(selector).forEach(el => {
                el.remove();
            });
        });

        // تحسين عرض الجداول
        content.querySelectorAll('table').forEach(table => {
            // إضافة فئات CSS للطباعة
            table.classList.add('print-table');

            // التأكد من عرض جميع الأعمدة
            table.style.width = '100%';
            table.style.tableLayout = 'auto';

            // تحسين عرض الخلايا
            table.querySelectorAll('th, td').forEach(cell => {
                cell.style.border = '1px solid #000';
                cell.style.padding = '8px';
                cell.style.fontSize = '12px';
                cell.style.lineHeight = '1.4';

                // إزالة أي تنسيق يمكن أن يخفي المحتوى
                cell.style.overflow = 'visible';
                cell.style.textOverflow = 'unset';
                cell.style.whiteSpace = 'normal';
                cell.style.maxWidth = 'none';
            });

            // تحسين رؤوس الجداول
            table.querySelectorAll('thead th').forEach(th => {
                th.style.backgroundColor = '#f0f0f0';
                th.style.fontWeight = 'bold';
                th.style.textAlign = 'center';
            });
        });

        // إضافة فئات الطباعة للأرقام والتواريخ
        content.querySelectorAll('.amount, .price, .total, .currency').forEach(el => {
            el.classList.add('number');
            el.style.textAlign = 'left';
            el.style.direction = 'ltr';
            el.style.fontFamily = 'monospace';
        });

        content.querySelectorAll('.date').forEach(el => {
            el.classList.add('date');
            el.style.direction = 'ltr';
            el.style.textAlign = 'left';
        });

        // تحسين عرض النصوص العربية
        content.querySelectorAll('*').forEach(el => {
            if (el.textContent && /[\u0600-\u06FF]/.test(el.textContent)) {
                el.style.direction = 'rtl';
                el.style.textAlign = 'right';
            }
        });

        // إضافة معلومات إضافية للطباعة
        const printInfo = document.createElement('div');
        printInfo.className = 'print-info';
        printInfo.style.cssText = `
            position: fixed;
            bottom: 10px;
            right: 10px;
            font-size: 10px;
            color: #666;
            direction: rtl;
        `;
        printInfo.innerHTML = `تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}`;
        content.appendChild(printInfo);

        return content.innerHTML;
    }

    /**
     * تنفيذ الطباعة
     */
    executePrint() {
        // تطبيق خيارات الطباعة
        this.applyPrintOptions();
        
        // إخفاء النافذة
        this.closePrintModal();
        
        // تنفيذ الطباعة
        setTimeout(() => {
            window.print();
        }, 100);
    }

    /**
     * تطبيق خيارات الطباعة
     */
    applyPrintOptions() {
        const orientation = document.getElementById('print-orientation')?.value || 'portrait';
        const paperSize = document.getElementById('print-paper-size')?.value || 'A4';
        const includeBackground = document.getElementById('print-background')?.checked || false;

        // إنشاء CSS مخصص للطباعة
        let customCSS = `
            @media print {
                @page {
                    size: ${paperSize} ${orientation};
                    margin: ${this.printOptions.margins.top} ${this.printOptions.margins.right} 
                           ${this.printOptions.margins.bottom} ${this.printOptions.margins.left};
                }
        `;

        if (!includeBackground) {
            customCSS += `
                * {
                    background: white !important;
                    color: black !important;
                }
            `;
        }

        customCSS += `}`;

        // إضافة CSS مخصص
        const styleElement = document.createElement('style');
        styleElement.textContent = customCSS;
        styleElement.id = 'custom-print-styles';
        
        // إزالة الأنماط السابقة
        const existingStyles = document.getElementById('custom-print-styles');
        if (existingStyles) {
            existingStyles.remove();
        }
        
        document.head.appendChild(styleElement);
    }

    /**
     * إنشاء PDF
     */
    generatePDF() {
        // هذه الوظيفة تتطلب مكتبة خارجية مثل jsPDF أو html2pdf
        alert('وظيفة إنشاء PDF ستكون متاحة قريباً');
    }

    /**
     * إغلاق نافذة الطباعة
     */
    closePrintModal() {
        const modal = document.querySelector('.print-modal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    /**
     * طباعة عنصر محدد
     */
    printElement(elementId) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.error('العنصر غير موجود:', elementId);
            return;
        }

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>طباعة</title>
                <link rel="stylesheet" href="/static/css/print.css">
            </head>
            <body>
                ${element.outerHTML}
                <script>
                    window.onload = function() {
                        window.print();
                        window.close();
                    }
                </script>
            </body>
            </html>
        `);
        printWindow.document.close();
    }

    /**
     * طباعة سريعة بدون خيارات
     */
    quickPrint() {
        this.applyPrintOptions();
        window.print();
    }
}

// إنشاء مثيل عام لنظام الطباعة
const printSystem = new PrintSystem();

// وظائف مساعدة عامة
window.printPage = () => printSystem.quickPrint();
window.printElement = (id) => printSystem.printElement(id);
window.showPrintDialog = () => printSystem.showPrintDialog();
