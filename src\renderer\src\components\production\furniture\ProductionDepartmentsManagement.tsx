import React, { useState, useEffect } from 'react'
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  message, 
  Space, 
  Tooltip, 
  Card, 
  Row, 
  Col, 
  Statistic,
  Tag,
  Select,
  Popconfirm
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  BuildOutlined,
  UserOutlined,
  BarcodeOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../../utils/logger'

const { TextArea } = Input
const { Option } = Select

interface ProductionDepartment {
  id: number
  code: string
  name: string
  description?: string
  manager_id?: number
  manager_name?: string
  location?: string
  capacity?: number
  is_active: boolean
  created_at: string
}

interface ProductionDepartmentsManagementProps {
  onBack?: () => void
}

const ProductionDepartmentsManagement: React.FC<ProductionDepartmentsManagementProps> = ({ onBack: _onBack }) => {
  const [departments, setDepartments] = useState<ProductionDepartment[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingDepartment, setEditingDepartment] = useState<ProductionDepartment | null>(null)
  const [form] = Form.useForm()
  const [users, setUsers] = useState<any[]>([])

  // إحصائيات سريعة
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    with_manager: 0
  })

  useEffect(() => {
    loadDepartments()
    loadUsers()
  }, [])

  const loadDepartments = async () => {
    setLoading(true)
    try {
      Logger.info('ProductionDepartmentsManagement', '🔄 جاري تحميل أقسام الإنتاج...')

      if (!window.electronAPI) {
        Logger.error('ProductionDepartmentsManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('ProductionDepartmentsManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية لأقسام الإنتاج
        const mockDepartments = [
          {
            id: 1,
            code: 'DEPT001',
            name: 'قسم النجارة',
            description: 'قسم متخصص في أعمال النجارة والخشب',
            manager_id: 1,
            manager_name: 'أحمد النجار',
            location: 'الطابق الأول - الجناح الشرقي',
            capacity: 15,
            is_active: true,
            created_at: '2024-06-20'
          },
          {
            id: 2,
            code: 'DEPT002',
            name: 'قسم التجميع',
            description: 'قسم تجميع القطع والمنتجات النهائية',
            manager_id: 2,
            manager_name: 'محمد المجمع',
            location: 'الطابق الأول - الجناح الغربي',
            capacity: 10,
            is_active: true,
            created_at: '2024-06-18'
          },
          {
            id: 3,
            code: 'DEPT003',
            name: 'قسم التشطيب',
            description: 'قسم التشطيب النهائي والدهان',
            manager_id: 3,
            manager_name: 'علي المشطب',
            location: 'الطابق الثاني',
            capacity: 8,
            is_active: true,
            created_at: '2024-06-15'
          }
        ]

        setDepartments(mockDepartments)
        calculateStats(mockDepartments)
        Logger.info('ProductionDepartmentsManagement', '✅ تم تحميل ' + mockDepartments.length + ' قسم إنتاج وهمي')
      } else {
        const result = await window.electronAPI.getProductionDepartments()
        if (result.success) {
          setDepartments(result.data)
          calculateStats(result.data)
          Logger.info('ProductionDepartmentsManagement', '✅ تم تحميل أقسام الإنتاج من قاعدة البيانات')
        } else {
          message.error('فشل في تحميل أقسام الإنتاج')
        }
      }
    } catch (error) {
      message.error('خطأ في تحميل أقسام الإنتاج')
    }
    setLoading(false)
  }

  const loadUsers = async () => {
    try {
      Logger.info('ProductionDepartmentsManagement', '🔄 جاري تحميل المستخدمين...')

      if (!window.electronAPI) {
        Logger.error('ProductionDepartmentsManagement', '❌ window.electronAPI غير متوفر')
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      }

      // جلب المستخدمين الحقيقيين من قاعدة البيانات
      const response = await window.electronAPI.getUsers()

      if (!response || !response.success) {
        throw new Error('فشل في جلب المستخدمين من قاعدة البيانات')
      }

      const users = response.data || []
      setUsers(users)
      Logger.info('ProductionDepartmentsManagement', '✅ تم تحميل المستخدمين من قاعدة البيانات')
    } catch (error) {
      Logger.error('ProductionDepartmentsManagement', 'خطأ في تحميل المستخدمين:', error)
    }
  }

  const calculateStats = (data: ProductionDepartment[]) => {
    const stats = {
      total: data.length,
      active: data.filter(d => d.is_active).length,
      inactive: data.filter(d => !d.is_active).length,
      with_manager: data.filter(d => d.manager_id).length
    }
    setStats(stats)
  }

  const generateCode = async () => {
    try {
      const response = await window.electronAPI.generateProductionDepartmentCode()
      if (response.success && response.data) {
        form.setFieldsValue({ code: response.data.code })
        message.success('تم إنشاء الكود تلقائياً')
      }
    } catch (error) {
      message.error('فشل في إنشاء الكود')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionDepartmentsManagement', '❌ window.electronAPI غير متوفر')
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن حفّ أقسام الإنتاج')
        return
      }

      let result
      if (editingDepartment) {
        result = await window.electronAPI.updateProductionDepartment(editingDepartment.id, values)
      } else {
        result = await window.electronAPI.createProductionDepartment(values)
      }

      if (result.success) {
        message.success(editingDepartment ? 'تم تحديث القسم بنجاح' : 'تم إنشاء القسم بنجاح')
        setModalVisible(false)
        setEditingDepartment(null)
        form.resetFields()
        loadDepartments()
      } else {
        message.error(result.message || 'فشل في حفّ القسم')
      }
    } catch (error) {
      message.error('خطأ في حفّ القسم')
    }
  }

  const handleEdit = (department: ProductionDepartment) => {
    setEditingDepartment(department)
    form.setFieldsValue(department)
    setModalVisible(true)
  }

  const handleDelete = async (departmentId: number) => {
    try {
      const result = await window.electronAPI.deleteProductionDepartment(departmentId)
      if (result.success) {
        message.success('تم حذف القسم بنجاح')
        loadDepartments()
      } else {
        message.error(result.message || 'فشل في حذف القسم')
      }
    } catch (error) {
      message.error('خطأ في حذف القسم')
    }
  }

  const columns = [
    {
      title: 'الكود',
      dataIndex: 'code',
      key: 'code',
      width: 100,
      render: (code: string) => <Tag color="blue">{code}</Tag>
    },
    {
      title: 'اسم القسم',
      dataIndex: 'name',
      key: 'name',
      width: 200
    },
    {
      title: 'الوصف',
      dataIndex: 'description',
      key: 'description',
      width: 250,
      ellipsis: true
    },
    {
      title: 'المدير',
      dataIndex: 'manager_name',
      key: 'manager_name',
      width: 150,
      render: (name: string) => name ? (
        <Tag color="green" icon={<UserOutlined />}>{name}</Tag>
      ) : (
        <Tag color="default">غير محدد</Tag>
      )
    },
    {
      title: 'الموقع',
      dataIndex: 'location',
      key: 'location',
      width: 120
    },
    {
      title: 'السعة',
      dataIndex: 'capacity',
      key: 'capacity',
      width: 100,
      render: (capacity: number) => capacity ? capacity + ' عامل' : '-'
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'نشط' : 'غير نشط'}
        </Tag>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 120,
      render: (record: ProductionDepartment) => (
        <Space>
          <Tooltip title="تعديل">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="حذف">
            <Popconfirm
              title="هل أنت متأكد من حذف هذا القسم؟"
              onConfirm={() => handleDelete(record.id)}
              okText="نعم"
              cancelText="لا"
            >
              <Button
                danger
                size="small"
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* العنوان والإحصائيات */}
      <div style={{ marginBottom: '24px' }}>
        <h2 style={{ margin: 0, color: '#1890ff', display: 'flex', alignItems: 'center' }}>
          <BuildOutlined style={{ marginRight: '8px' }} />
          إدارة أقسام الإنتاج
        </h2>
        <p style={{ margin: '8px 0 0 0', color: '#666' }}>إدارة أقسام الإنتاج والمديرين المسؤولين</p>
      </div>

      {/* الإحصائيات السريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الأقسام"
              value={stats.total}
              prefix={<BuildOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="الأقسام النشطة"
              value={stats.active}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="الأقسام غير النشطة"
              value={stats.inactive}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="أقسام لها مدير"
              value={stats.with_manager}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* شريط الأدوات */}
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingDepartment(null)
            form.resetFields()
            generateCode() // إنشاء كود تلقائي عند إضافة قسم جديد
            setModalVisible(true)
          }}
        >
          قسم جديد
        </Button>
        <Button onClick={loadDepartments}>
          تحديث
        </Button>
      </div>

      {/* جدول الأقسام */}
      <Table
        columns={columns}
        dataSource={departments}
        rowKey="id"
        loading={loading}
        scroll={{ x: 1200 }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => 'إجمالي ' + total + ' قسم'
        }}
      />

      {/* نموذج إضافة/تعديل القسم */}
      <Modal
        title={editingDepartment ? 'تعديل القسم' : 'إضافة قسم جديد'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ is_active: true }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="كود القسم"
                rules={[{ required: true, message: 'يرجى إدخال كود القسم' }]}
              >
                <Input
                  placeholder="أدخل كود القسم"
                  addonAfter={
                    !editingDepartment && (
                      <Button
                        size="small"
                        onClick={generateCode}
                        icon={<BarcodeOutlined />}
                      >
                        إنشاء تلقائي
                      </Button>
                    )
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="اسم القسم"
                rules={[{ required: true, message: 'يرجى إدخال اسم القسم' }]}
              >
                <Input placeholder="اسم القسم" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="manager_id"
                label="مدير القسم"
              >
                <Select
                  placeholder="اختر مدير القسم"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label as string)?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {users.map(user => (
                    <Option key={user.id} value={user.id}>
                      {user.full_name || user.username}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="location"
                label="الموقع"
              >
                <Input placeholder="موقع القسم" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="capacity"
                label="السعة (عدد العمال)"
              >
                <Input type="number" placeholder="السعة القصوى للقسم" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="is_active"
                label="الحالة"
                valuePropName="checked"
              >
                <Select>
                  <Option value={true}>نشط</Option>
                  <Option value={false}>غير نشط</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="الوصف"
          >
            <TextArea
              rows={3}
              placeholder="وصف القسم ومهامه"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {editingDepartment ? 'تحديث' : 'إضافة'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ProductionDepartmentsManagement
