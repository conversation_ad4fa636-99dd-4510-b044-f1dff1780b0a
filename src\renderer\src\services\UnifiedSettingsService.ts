/**
 * خدمة إدارة الإعدادات الموحدة
 * تدير الإعدادات العامة والقوالب المخصصة مع نظام الوراثة
 */

import { 
  UnifiedPrintSettings, 
  EnhancedTemplate, 
  TemplateMetadata,
  SettingsValidation,
  createDefaultUnifiedSettings,
  createEmptyTemplate
} from '../types/enhancedTemplateTypes'
import { SafeLogger as Logger } from '../utils/logger'

export class UnifiedSettingsService {
  private static instance: UnifiedSettingsService
  private globalSettings: UnifiedPrintSettings
  private templates: Map<string, EnhancedTemplate> = new Map()
  private isInitialized = false

  private constructor() {
    this.globalSettings = createDefaultUnifiedSettings()
  }

  public static getInstance(): UnifiedSettingsService {
    if (!UnifiedSettingsService.instance) {
      UnifiedSettingsService.instance = new UnifiedSettingsService()
    }
    return UnifiedSettingsService.instance
  }

  // ===== إدارة الإعدادات العامة =====

  /**
   * تحميل الإعدادات العامة من قاعدة البيانات
   */
  public async loadGlobalSettings(): Promise<UnifiedPrintSettings> {
    try {
      Logger.info('UnifiedSettingsService', 'تحميل الإعدادات العامة...')
      
      // محاولة تحميل من قاعدة البيانات
      const savedSettings = await window.electronAPI?.invoke('get-print-settings')
      
      if (savedSettings) {
        // تحويل الإعدادات المحفوظة إلى التنسيق الموحد
        this.globalSettings = this.convertLegacySettings(savedSettings)
        Logger.info('UnifiedSettingsService', 'تم تحميل الإعدادات العامة من قاعدة البيانات')
      } else {
        // استخدام الإعدادات الافتراضية
        this.globalSettings = createDefaultUnifiedSettings()
        await this.saveGlobalSettings(this.globalSettings)
        Logger.info('UnifiedSettingsService', 'تم إنشاء إعدادات افتراضية جديدة')
      }

      this.isInitialized = true
      return this.globalSettings
    } catch (error) {
      Logger.error('UnifiedSettingsService', 'خطأ في تحميل الإعدادات العامة:', error)
      this.globalSettings = createDefaultUnifiedSettings()
      this.isInitialized = true
      return this.globalSettings
    }
  }

  /**
   * حفظ الإعدادات العامة
   */
  public async saveGlobalSettings(settings: UnifiedPrintSettings): Promise<boolean> {
    try {
      Logger.info('UnifiedSettingsService', 'حفظ الإعدادات العامة...')
      
      // تحديث الطابع الزمني
      settings.lastUpdated = new Date().toISOString()
      
      // تحويل إلى التنسيق القديم للحفظ في قاعدة البيانات
      const legacySettings = this.convertToLegacyFormat(settings)
      
      // حفظ في قاعدة البيانات
      const result = await window.electronAPI?.invoke('save-print-settings', legacySettings)
      
      if (result?.success) {
        this.globalSettings = settings
        Logger.success('UnifiedSettingsService', 'تم حفظ الإعدادات العامة بنجاح')
        return true
      } else {
        Logger.error('UnifiedSettingsService', 'فشل في حفظ الإعدادات العامة')
        return false
      }
    } catch (error) {
      Logger.error('UnifiedSettingsService', 'خطأ في حفظ الإعدادات العامة:', error)
      return false
    }
  }

  /**
   * الحصول على الإعدادات العامة الحالية
   */
  public getGlobalSettings(): UnifiedPrintSettings {
    if (!this.isInitialized) {
      Logger.warn('UnifiedSettingsService', 'الخدمة غير مهيأة، استخدام الإعدادات الافتراضية')
      return createDefaultUnifiedSettings()
    }
    return { ...this.globalSettings }
  }

  /**
   * تحديث جزء من الإعدادات العامة
   */
  public async updateGlobalSettings(partialSettings: Partial<UnifiedPrintSettings>): Promise<boolean> {
    const updatedSettings = {
      ...this.globalSettings,
      ...partialSettings,
      lastUpdated: new Date().toISOString()
    }
    return await this.saveGlobalSettings(updatedSettings)
  }

  // ===== إدارة القوالب =====

  /**
   * تحميل جميع القوالب
   */
  public async loadTemplates(): Promise<EnhancedTemplate[]> {
    try {
      Logger.info('UnifiedSettingsService', 'تحميل القوالب...')
      
      const savedTemplates = await window.electronAPI?.invoke('get-print-templates')
      
      if (savedTemplates && Array.isArray(savedTemplates)) {
        // تحويل القوالب المحفوظة إلى التنسيق الجديد
        for (const template of savedTemplates) {
          const enhancedTemplate = this.convertLegacyTemplate(template)
          this.templates.set(enhancedTemplate.metadata.id, enhancedTemplate)
        }
        Logger.info('UnifiedSettingsService', `تم تحميل ${savedTemplates.length} قالب`)
      }

      return Array.from(this.templates.values())
    } catch (error) {
      Logger.error('UnifiedSettingsService', 'خطأ في تحميل القوالب:', error)
      return []
    }
  }

  /**
   * حفظ قالب
   */
  public async saveTemplate(template: EnhancedTemplate): Promise<boolean> {
    try {
      Logger.info('UnifiedSettingsService', `حفظ القالب: ${template.metadata.name}`)
      
      // تحديث الطابع الزمني
      template.metadata.updatedAt = new Date().toISOString()
      
      // تحويل إلى التنسيق القديم للحفظ
      const legacyTemplate = this.convertToLegacyTemplate(template)
      
      // حفظ في قاعدة البيانات
      const result = await window.electronAPI?.invoke('save-template', legacyTemplate)
      
      if (result?.success) {
        this.templates.set(template.metadata.id, template)
        Logger.success('UnifiedSettingsService', `تم حفظ القالب: ${template.metadata.name}`)
        return true
      } else {
        Logger.error('UnifiedSettingsService', `فشل في حفظ القالب: ${template.metadata.name}`)
        return false
      }
    } catch (error) {
      Logger.error('UnifiedSettingsService', 'خطأ في حفظ القالب:', error)
      return false
    }
  }

  /**
   * الحصول على قالب بالمعرف
   */
  public getTemplate(id: string): EnhancedTemplate | null {
    return this.templates.get(id) || null
  }

  /**
   * الحصول على جميع القوالب
   */
  public getAllTemplates(): EnhancedTemplate[] {
    return Array.from(this.templates.values())
  }

  /**
   * حذف قالب
   */
  public async deleteTemplate(id: string): Promise<boolean> {
    try {
      const result = await window.electronAPI?.invoke('delete-template', id)
      
      if (result?.success) {
        this.templates.delete(id)
        Logger.success('UnifiedSettingsService', `تم حذف القالب: ${id}`)
        return true
      } else {
        Logger.error('UnifiedSettingsService', `فشل في حذف القالب: ${id}`)
        return false
      }
    } catch (error) {
      Logger.error('UnifiedSettingsService', 'خطأ في حذف القالب:', error)
      return false
    }
  }

  // ===== نظام الوراثة =====

  /**
   * حساب الإعدادات الفعلية للقالب (مع الوراثة)
   */
  public getEffectiveSettings(template: EnhancedTemplate): UnifiedPrintSettings {
    if (!template.inheritance.inheritsFromGlobal) {
      // القالب لا يرث - استخدام إعداداته المخصصة فقط
      return {
        ...createDefaultUnifiedSettings(),
        ...template.inheritance.customSettings
      }
    }

    // دمج الإعدادات العامة مع التخصيصات
    return this.mergeSettings(this.globalSettings, template.inheritance.customSettings)
  }

  /**
   * دمج الإعدادات مع التخصيصات
   */
  private mergeSettings(
    baseSettings: UnifiedPrintSettings, 
    customSettings: Partial<UnifiedPrintSettings>
  ): UnifiedPrintSettings {
    return {
      page: { ...baseSettings.page, ...customSettings.page },
      font: { ...baseSettings.font, ...customSettings.font },
      colors: { ...baseSettings.colors, ...customSettings.colors },
      display: { ...baseSettings.display, ...customSettings.display },
      layout: { ...baseSettings.layout, ...customSettings.layout },
      content: { ...baseSettings.content, ...customSettings.content },
      quality: { ...baseSettings.quality, ...customSettings.quality },
      version: customSettings.version || baseSettings.version,
      lastUpdated: customSettings.lastUpdated || baseSettings.lastUpdated,
      autoSync: customSettings.autoSync ?? baseSettings.autoSync
    }
  }

  // ===== دوال التحويل =====

  /**
   * تحويل الإعدادات القديمة إلى التنسيق الموحد
   */
  private convertLegacySettings(legacySettings: any): UnifiedPrintSettings {
    return {
      page: {
        pageSize: legacySettings.pageSize || 'A4',
        orientation: legacySettings.orientation || 'portrait',
        margins: legacySettings.margins || { top: 20, bottom: 20, left: 20, right: 20 }
      },
      font: {
        fontSize: legacySettings.fontSize || 12,
        fontFamily: legacySettings.fontFamily || 'Arial',
        headerSize: legacySettings.headerSize || 18,
        lineSpacing: legacySettings.lineSpacing || 1.5
      },
      colors: {
        primaryColor: legacySettings.primaryColor || '#1890ff',
        secondaryColor: legacySettings.secondaryColor || '#fff3cd',
        borderColor: legacySettings.borderColor || '#d9d9d9',
        backgroundColor: legacySettings.backgroundColor || '#ffffff',
        textColor: legacySettings.textColor || '#000000'
      },
      display: {
        showHeader: legacySettings.showHeader ?? true,
        showFooter: legacySettings.showFooter ?? true,
        showLogo: legacySettings.showLogo ?? true,
        showSignature: legacySettings.showSignature ?? false,
        showTerms: legacySettings.showTerms ?? true,
        showQR: legacySettings.showQR ?? false
      },
      layout: {
        logoPosition: legacySettings.logoPosition || 'top-left',
        logoSize: legacySettings.logoSize || 'medium',
        borderWidth: legacySettings.borderWidth || 1,
        sectionSpacing: legacySettings.sectionSpacing || 15,
        tableWidth: legacySettings.tableWidth || 100
      },
      content: {
        headerText: legacySettings.headerText || 'شركة المحاسبة المتقدمة',
        footerText: legacySettings.footerText || 'شكراً لتعاملكم معنا',
        watermark: legacySettings.watermark ?? false,
        watermarkText: legacySettings.watermarkText || 'ZET.IA',
        watermarkOpacity: legacySettings.watermarkOpacity || 0.1
      },
      quality: {
        quality: legacySettings.quality || 'normal',
        copies: legacySettings.copies || 1,
        autoSave: legacySettings.autoSave ?? true
      },
      version: '1.0.0',
      lastUpdated: new Date().toISOString(),
      autoSync: true
    }
  }

  /**
   * تحويل الإعدادات الموحدة إلى التنسيق القديم
   */
  private convertToLegacyFormat(settings: UnifiedPrintSettings): any {
    return {
      pageSize: settings.page.pageSize,
      orientation: settings.page.orientation,
      margins: settings.page.margins,
      fontSize: settings.font.fontSize,
      fontFamily: settings.font.fontFamily,
      headerSize: settings.font.headerSize,
      lineSpacing: settings.font.lineSpacing,
      primaryColor: settings.colors.primaryColor,
      secondaryColor: settings.colors.secondaryColor,
      borderColor: settings.colors.borderColor,
      backgroundColor: settings.colors.backgroundColor,
      textColor: settings.colors.textColor,
      showHeader: settings.display.showHeader,
      showFooter: settings.display.showFooter,
      showLogo: settings.display.showLogo,
      showSignature: settings.display.showSignature,
      showTerms: settings.display.showTerms,
      showQR: settings.display.showQR,
      logoPosition: settings.layout.logoPosition,
      logoSize: settings.layout.logoSize,
      borderWidth: settings.layout.borderWidth,
      sectionSpacing: settings.layout.sectionSpacing,
      tableWidth: settings.layout.tableWidth,
      headerText: settings.content.headerText,
      footerText: settings.content.footerText,
      watermark: settings.content.watermark,
      watermarkText: settings.content.watermarkText,
      watermarkOpacity: settings.content.watermarkOpacity,
      quality: settings.quality.quality,
      copies: settings.quality.copies,
      autoSave: settings.quality.autoSave
    }
  }

  /**
   * تحويل القالب القديم إلى التنسيق الجديد
   */
  private convertLegacyTemplate(legacyTemplate: any): EnhancedTemplate {
    const template = createEmptyTemplate()
    
    // معلومات القالب
    template.metadata = {
      id: legacyTemplate.id,
      name: legacyTemplate.name,
      description: legacyTemplate.description || '',
      type: legacyTemplate.type || 'report',
      category: legacyTemplate.category,
      isDefault: legacyTemplate.isDefault || false,
      isActive: legacyTemplate.isActive ?? true,
      createdAt: legacyTemplate.createdAt || new Date().toISOString(),
      updatedAt: legacyTemplate.updatedAt || new Date().toISOString(),
      supportedReportTypes: legacyTemplate.supportedReportTypes
    }

    // إعدادات الوراثة
    template.inheritance = {
      inheritsFromGlobal: true, // افتراضياً يرث من الإعدادات العامة
      customSettings: legacyTemplate.settings ? this.convertLegacySettings(legacyTemplate.settings) : {},
      overrides: Object.keys(legacyTemplate.settings || {})
    }

    return template
  }

  /**
   * تحويل القالب الجديد إلى التنسيق القديم
   */
  private convertToLegacyTemplate(template: EnhancedTemplate): any {
    return {
      id: template.metadata.id,
      name: template.metadata.name,
      description: template.metadata.description,
      type: template.metadata.type,
      category: template.metadata.category,
      isDefault: template.metadata.isDefault,
      isActive: template.metadata.isActive,
      createdAt: template.metadata.createdAt,
      updatedAt: template.metadata.updatedAt,
      supportedReportTypes: template.metadata.supportedReportTypes,
      settings: this.convertToLegacyFormat(template.inheritance.customSettings as UnifiedPrintSettings)
    }
  }

  // ===== التحقق من صحة البيانات =====

  /**
   * التحقق من صحة الإعدادات
   */
  public validateSettings(settings: Partial<UnifiedPrintSettings>): SettingsValidation {
    const errors: string[] = []
    const warnings: string[] = []

    // التحقق من إعدادات الصفحة
    if (settings.page) {
      if (settings.page.margins) {
        const { top, bottom, left, right } = settings.page.margins
        if (top < 0 || bottom < 0 || left < 0 || right < 0) {
          errors.push('الهوامش يجب أن تكون أرقام موجبة')
        }
        if (top > 50 || bottom > 50 || left > 50 || right > 50) {
          warnings.push('الهوامش كبيرة جداً قد تؤثر على التخطيط')
        }
      }
    }

    // التحقق من إعدادات الخط
    if (settings.font) {
      if (settings.font.fontSize && (settings.font.fontSize < 8 || settings.font.fontSize > 24)) {
        warnings.push('حجم الخط خارج النطاق المنصوح به (8-24)')
      }
      if (settings.font.headerSize && (settings.font.headerSize < 14 || settings.font.headerSize > 28)) {
        warnings.push('حجم خط العنوان خارج النطاق المنصوح به (14-28)')
      }
    }

    // التحقق من الألوان
    if (settings.colors) {
      const colorRegex = /^#[0-9A-F]{6}$/i
      Object.entries(settings.colors).forEach(([key, value]) => {
        if (value && !colorRegex.test(value)) {
          errors.push(`لون ${key} غير صحيح`)
        }
      })
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * التحقق من صحة القالب
   */
  public validateTemplate(template: EnhancedTemplate): SettingsValidation {
    const errors: string[] = []
    const warnings: string[] = []

    // التحقق من معلومات القالب
    if (!template.metadata.name.trim()) {
      errors.push('اسم القالب مطلوب')
    }

    if (!template.metadata.type) {
      errors.push('نوع القالب مطلوب')
    }

    // التحقق من الإعدادات المخصصة
    const settingsValidation = this.validateSettings(template.inheritance.customSettings)
    errors.push(...settingsValidation.errors)
    warnings.push(...settingsValidation.warnings)

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}
