import React, { useState } from 'react'
import {
  Modal,
  Button,
  message,
  Select,
  Space,
  Alert
} from 'antd'
import {
  FileImageOutlined,
  ScanOutlined
} from '@ant-design/icons'
import { SimpleImageManager } from './SimpleImageManager'
import type { CheckImage } from '../../types/global'
import EnhancedCheckImagePrint from '../financial/EnhancedCheckImagePrint'
import type { UnifiedImage } from '../../services/SimpleImageService'

interface CheckImageManagerProps {
  visible: boolean
  onClose: () => void
  checkId?: number
  receiptId?: number
  checkNumber?: string
  checkInfo?: {
    id: number
    check_number: string
    amount: number
    bank_name?: string
    account_holder?: string
    issue_date?: string
    due_date?: string
    status?: string
    notes?: string
  }
}

const CheckImageManager: React.FC<CheckImageManagerProps> = ({
  visible,
  onClose,
  checkId,
  receiptId,
  checkNumber,
  checkInfo
}) => {
  const [images, setImages] = useState<UnifiedImage[]>([])
  const [selectedSide, setSelectedSide] = useState<'front' | 'back' | 'deposit' | 'receipt'>('front')

  // خيارات جهة الشيك
  const sideOptions = [
    { value: 'front', label: 'وجه الشيك', color: 'blue' },
    { value: 'back', label: 'ظهر الشيك', color: 'green' },
    { value: 'deposit', label: 'إيصال الإيداع', color: 'orange' },
    { value: 'receipt', label: 'إقرار البنك', color: 'purple' }
  ]

  // تحويل الصور إلى تنسيق CheckImage للطباعة
  const convertToCheckImages = (unifiedImages: UnifiedImage[]): CheckImage[] => {
    return unifiedImages.map(img => ({
      id: parseInt(img.id),
      check_id: checkId || 0,
      receipt_id: receiptId,
      image_name: img.name,
      image_path: img.path,
      image_side: (img.metadata?.side as 'front' | 'back' | 'deposit' | 'receipt') || 'front',
      scan_quality: img.metadata?.quality as 'high' | 'medium' | 'low' | undefined,
      file_size: img.size,
      file_type: img.type,
      notes: img.description,
      uploaded_at: img.created_at,
      uploaded_by: 1
    }))
  }

  // معالج نجاح الطباعة
  const handlePrintSuccess = () => {
    message.success('تم إرسال صور الشيك للطباعة بنجاح')
  }

  // معالج خطأ الطباعة
  const handlePrintError = (error: string) => {
    message.error(`فشل في الطباعة: ${error}`)
  }

  // إعداد معلومات الشيك الافتراضية
  const getCheckInfo = () => {
    return checkInfo || {
      id: checkId || 0,
      check_number: checkNumber || 'غير محدد',
      amount: 0,
      bank_name: '',
      account_holder: '',
      status: 'غير محدد'
    }
  }

  // معالج تغيير الصور
  const handleImagesChange = (unifiedImages: UnifiedImage[]) => {
    setImages(unifiedImages)
  }

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <FileImageOutlined />
          <span>إدارة صور الشيك: {checkNumber || 'غير محدد'}</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={[
        <EnhancedCheckImagePrint
          key="print"
          images={convertToCheckImages(images)}
          checkInfo={getCheckInfo()}
          onPrintSuccess={handlePrintSuccess}
          onPrintError={handlePrintError}
        />,
        <Button key="close" onClick={onClose}>
          إغلاق
        </Button>
      ]}
    >
      {/* تعليمات استخدام النظام الجديد */}
      <Alert
        message="نظام إدارة الصور الموحد"
        description={
          <div>
            <p>• اختر جهة الشيك قبل رفع الصورة (وجه، ظهر، إيصال إيداع، إقرار البنك)</p>
            <p>• يمكنك رفع عدة صور في نفس الوقت</p>
            <p>• استخدم حقل الوصف لإضافة ملاحظات على كل صورة</p>
          </div>
        }
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* اختيار جهة الشيك */}
      <div style={{ marginBottom: 16 }}>
        <Space>
          <span>جهة الشيك:</span>
          <Select
            value={selectedSide}
            onChange={setSelectedSide}
            style={{ width: 180 }}
            placeholder="اختر جهة الشيك"
          >
            {sideOptions.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>

          <Button
            icon={<ScanOutlined />}
            onClick={() => message.info('ميزة المسح الضوئي قيد التطوير')}
          >
            مسح ضوئي
          </Button>
        </Space>
      </div>

      {/* مكون إدارة الصور الموحد */}
      {checkId && (
        <SimpleImageManager
          category="check"
          contextId={checkId}
          maxImages={10}
          allowMultiple={true}
          showDescription={true}
          showPrimaryButton={false}
          metadata={{
            side: selectedSide,
            quality: 'high',
            checkNumber: checkNumber,
            receiptId: receiptId
          }}
          onChange={handleImagesChange}
        />
      )}
    </Modal>
  )
}

export default CheckImageManager
