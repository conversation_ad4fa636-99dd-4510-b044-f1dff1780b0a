# دليل نظام الطباعة الشامل - ZET.IA

## نظرة عامة

تم تطوير نظام طباعة متكامل وشامل لتطبيق ZET.IA يدعم جميع أنواع المستندات مع إعدادات متقدمة وقوالب قابلة للتخصيص.

## المكونات الرئيسية

### 1. إعدادات الطباعة (`PrintSettings.tsx`)

**الموقع:** `src/renderer/src/components/Settings/PrintSettings.tsx`

**الميزات:**
- إعدادات طباعة شاملة (الهوامش، الخطوط، الألوان)
- إدارة معلومات الشركة
- نظام قوالب الطباعة المتقدم
- إعدادات الثيم والألوان
- تصدير/استيراد الإعدادات
- طباعة تجريبية

**الواجهات المدعومة:**
```typescript
interface PrintSettings {
  pageSize: string
  orientation: string
  margins: { top: number, bottom: number, left: number, right: number }
  fontSize: number
  fontFamily: string
  showHeader: boolean
  showFooter: boolean
  showLogo: boolean
  showSignature: boolean
  // ... 15+ إعداد إضافي
}

interface CompanySettings {
  name: string
  address: string
  phone: string
  email: string
  website: string
  logo: string
  // ... معلومات إضافية
}

interface PrintTemplate {
  id: string
  name: string
  type: 'invoice' | 'receipt' | 'report' | 'certificate'
  settings: any
  isDefault: boolean
  isActive: boolean
}
```

### 2. خدمة الطباعة الرئيسية (`MasterPrintService.ts`)

**الموقع:** `src/renderer/src/services/MasterPrintService.ts`

**الميزات:**
- Singleton pattern للإدارة المركزية
- دعم أنواع مختلفة من المستندات
- إنشاء HTML/CSS ديناميكي
- دعم كامل للعربية (RTL)
- تخصيص الألوان والخطوط
- معاينة قبل الطباعة

### 3. مكونات الطباعة المتخصصة

#### أ. مكون الطباعة الموحد (`UnifiedPrintButton.tsx`)
- مكون عام لجميع أنواع الطباعة
- واجهة موحدة وسهلة الاستخدام

#### ب. مكون طباعة الفواتير (`InvoicePrintButton.tsx`)
- متخصص في طباعة الفواتير
- دعم فواتير المبيعات والمشتريات
- حسابات الضرائب والخصومات

#### ج. مكون طباعة الإيصالات (`ReceiptPrintButton.tsx`)
- طباعة سندات القبض والصرف
- تنسيق مناسب لحجم A5
- معلومات الدفع والمراجع

### 4. نظام قاعدة البيانات

**الجداول المدعومة:**
- `settings`: إعدادات النظام العامة
- `print_templates`: قوالب الطباعة المخصصة
- `company_info`: معلومات الشركة

**IPC Handlers الجديدة:**
- `get-settings`: جلب الإعدادات
- `save-settings`: حفظ الإعدادات
- `get-print-templates`: جلب قوالب الطباعة
- `save-print-template`: حفظ قالب طباعة
- `delete-print-template`: حذف قالب طباعة

### 5. نظام الاختبار (`PrintSystemTest.tsx`)

**الموقع:** `src/renderer/src/components/common/PrintSystemTest.tsx`

**الميزات:**
- اختبار شامل لجميع مكونات النظام
- اختبارات أداء وسرعة
- بيانات تجريبية آمنة
- تقارير مفصلة للنتائج
- أمثلة تفاعلية للطباعة

## كيفية الاستخدام

### 1. الوصول لإعدادات الطباعة

```typescript
// في أي مكون React
import { useNavigate } from 'react-router-dom'

const navigate = useNavigate()
navigate('/settings') // ثم اختر تبويب "إعدادات الطباعة"
```

### 2. استخدام مكونات الطباعة

```typescript
// طباعة فاتورة
import { InvoicePrintButton } from '../components/common'

<InvoicePrintButton
  invoiceData={invoiceData}
  invoiceType="sales"
  buttonText="طباعة الفاتورة"
  onPrintSuccess={() => console.log('تم الطباعة بنجاح')}
/>

// طباعة إيصال
import { ReceiptPrintButton } from '../components/common'

<ReceiptPrintButton
  receiptData={receiptData}
  buttonText="طباعة الإيصال"
/>

// طباعة موحدة
import { UnifiedPrintButton } from '../components/common'

<UnifiedPrintButton
  data={documentData}
  type="report"
  buttonText="طباعة التقرير"
/>
```

### 3. تخصيص إعدادات الطباعة

```typescript
// تحديث معلومات الشركة
const printService = MasterPrintService.getInstance()
printService.updateCompanyInfo({
  name: 'اسم الشركة',
  address: 'عنوان الشركة',
  phone: '+966501234567'
})

// تحديث إعدادات الطباعة
printService.updatePrintOptions({
  pageSize: 'A4',
  orientation: 'portrait',
  margins: { top: 20, bottom: 20, left: 15, right: 15 }
})
```

## الميزات المتقدمة

### 1. نظام القوالب

- **قوالب افتراضية**: قوالب جاهزة لجميع أنواع المستندات
- **قوالب مخصصة**: إمكانية إنشاء وتعديل قوالب جديدة
- **معاينة القوالب**: رؤية القالب قبل التطبيق
- **تصدير/استيراد**: مشاركة القوالب بين الأنظمة

### 2. إدارة الألوان والثيم

- **ألوان مخصصة**: اختيار ألوان الشركة
- **ثيمات جاهزة**: ثيمات محددة مسبقاً
- **معاينة مباشرة**: رؤية التغييرات فوراً

### 3. إعدادات متقدمة

- **جودة الطباعة**: تحكم في دقة الطباعة
- **العلامات المائية**: إضافة علامات مائية للمستندات
- **التوقيعات الرقمية**: دعم التوقيعات
- **الباركود**: إضافة باركود للمستندات

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **عدم ظهور المعاينة**
   - تأكد من تفعيل JavaScript
   - تحقق من إعدادات المتصفح

2. **مشاكل في التنسيق**
   - تحقق من إعدادات الهوامش
   - تأكد من حجم الصفحة المناسب

3. **عدم حفظ الإعدادات**
   - تحقق من اتصال قاعدة البيانات
   - تأكد من صلاحيات الكتابة

### أدوات التشخيص

```typescript
// تشغيل اختبار شامل للنظام
import PrintSystemTest from '../components/common/PrintSystemTest'

// استخدم المكون في أي صفحة للاختبار
<PrintSystemTest />
```

## التطوير والتحسين

### إضافة نوع مستند جديد

1. إضافة النوع في `MasterPrintService`
2. إنشاء قالب HTML مخصص
3. تحديث واجهات TypeScript
4. إضافة اختبارات

### تخصيص التصميم

1. تعديل ملفات CSS في `MasterPrintService`
2. إضافة متغيرات جديدة للألوان
3. تحديث نظام القوالب

## الأمان والخصوصية

- جميع البيانات محفوظة محلياً
- لا يتم إرسال أي معلومات خارجياً
- تشفير إعدادات الشركة الحساسة
- نسخ احتياطية آمنة للإعدادات

## الدعم الفني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. استخدم أداة الاختبار المدمجة
3. تحقق من سجلات النظام
4. اتصل بفريق الدعم الفني

---

**تم التطوير بواسطة:** فريق ZET.IA  
**آخر تحديث:** يناير 2025  
**الإصدار:** 1.0.0
