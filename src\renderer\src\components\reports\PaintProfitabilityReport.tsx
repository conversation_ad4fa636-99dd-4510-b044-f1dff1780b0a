import React from 'react';
import { Tag, Typography, Progress } from 'antd';
import UniversalReport from './UniversalReport';
import type { ReportData, ReportType } from '../../types/reports';
import dayjs, { DateUtils, DATE_FORMATS } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger'

const { Text } = Typography;

const PaintProfitabilityReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getPaintProfitabilityReport({
        customerId: filters.customerId,
        dateRange: filters.dateRange
      });

      if (!response || !response.success || !response.data || !Array.isArray(response.data.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const paintData = response.data;

      const data = paintData.data;

      // حساب الإحصائيات
      const totalOrders = data.length;
      const totalArea = data.reduce((sum, item) => sum + item.total_area, 0);
      const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0);
      const totalPaid = data.reduce((sum, item) => sum + item.paid_amount, 0);
      const totalOutstanding = data.reduce((sum, item) => sum + item.outstanding, 0);
      const totalEstimatedCost = data.reduce((sum, item) => sum + item.estimated_cost, 0);
      const totalEstimatedProfit = data.reduce((sum, item) => sum + item.estimated_profit, 0);

      // إعداد البيانات للجدول
      const tableData = data.map((item, index) => ({
        key: item.order_id,
        index: index + 1,
        order_number: item.order_number,
        customer_name: item.customer_name,
        total_area: item.total_area,
        revenue: item.revenue,
        paid_amount: item.paid_amount,
        outstanding: item.outstanding,
        estimated_cost: item.estimated_cost,
        estimated_profit: item.estimated_profit,
        profit_margin: item.profit_margin,
        order_status: item.order_status,
        payment_status: item.payment_status,
        order_date: item.order_date ? dayjs(item.order_date).format(DATE_FORMATS.DISPLAY_DATE) : '-',
        invoice_date: item.invoice_date ? dayjs(item.invoice_date).format(DATE_FORMATS.DISPLAY_DATE) : '-',
        payment_percentage: item.revenue > 0 ? (item.paid_amount / item.revenue * 100) : 0
      }));

      // تعريف الأعمدة
      const columns = [
        {
          title: '#',

          key: 'index',
          width: 50,
          align: 'center' as const
        },
        {
          title: 'رقم الأمر',

          filterable: true,
          key: 'order_number',
          width: 120
        },
        {
          title: 'اسم العميل',

          filterable: true,
          key: 'customer_name',
          width: 150
        },
        {
          title: 'المساحة (م²)',

          sortable: true,
          key: 'total_area',
          width: 100,
          align: 'right' as const,
          render: (record: any) => record.total_area.toLocaleString('ar-EG', { minimumFractionDigits: 2 })
        },
        {
          title: 'الإيرادات',

          sortable: true,
          key: 'revenue',
          width: 120,
          align: 'right' as const,
          render: (record: any) => (
            <Text strong style={{ color: '#1890ff' }}>
              {record.revenue.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'المبلغ المدفوع',

          sortable: true,
          key: 'paid_amount',
          width: 120,
          align: 'right' as const,
          render: (record: any) => (
            <Text style={{ color: '#52c41a' }}>
              {record.paid_amount.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'المبلغ المستحق',

          sortable: true,
          key: 'outstanding',
          width: 120,
          align: 'right' as const,
          render: (record: any) => (
            <Text style={{ color: record.outstanding > 0 ? '#ff4d4f' : '#52c41a' }}>
              {record.outstanding.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'التكلفة المقدرة',

          sortable: true,
          key: 'estimated_cost',
          width: 120,
          align: 'right' as const,
          render: (record: any) => (
            <Text style={{ color: '#fa8c16' }}>
              {record.estimated_cost.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'الربح المقدر',

          sortable: true,
          key: 'estimated_profit',
          width: 120,
          align: 'right' as const,
          render: (record: any) => (
            <Text strong style={{ color: record.estimated_profit > 0 ? '#52c41a' : '#ff4d4f' }}>
              {record.estimated_profit.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'هامش الربح',

          sortable: true,
          key: 'profit_margin',
          width: 100,
          align: 'center' as const,
          render: (record: any) => {
            const value = record.profit_margin;
            const color = value >= 30 ? 'green' : value >= 20 ? 'orange' : value >= 10 ? 'yellow' : 'red';
            return (
              <Tag color={color}>
                {value.toFixed(1)}%
              </Tag>
            );
          }
        },
        {
          title: 'نسبة السداد',

          sortable: true,
          key: 'payment_percentage',
          width: 120,
          align: 'center' as const,
          render: (record: any) => (
            <Progress
              percent={Math.round(record.payment_percentage)}
              size="small"
              status={record.payment_percentage === 100 ? 'success' : record.payment_percentage > 50 ? 'active' : 'exception'}
            />
          )
        },
        {
          title: 'حالة الأمر',

          filterable: true,
          key: 'order_status',
          width: 100,
          align: 'center' as const,
          render: (record: any) => {
            const value = record.order_status;
            const statusColors: { [key: string]: string } = {
              'pending': 'orange',
              'in_progress': 'blue',
              'completed': 'green',
              'cancelled': 'red',
              'invoiced': 'purple'
            };
            const statusLabels: { [key: string]: string } = {
              'pending': 'معلق',
              'in_progress': 'قيد التنفيذ',
              'completed': 'مكتمل',
              'cancelled': 'ملغي',
              'invoiced': 'مفوتر'
            };
            return (
              <Tag color={statusColors[value] || 'default'}>
                {statusLabels[value] || value}
              </Tag>
            );
          }
        },
        {
          title: 'حالة الدفع',

          filterable: true,
          key: 'payment_status',
          width: 100,
          align: 'center' as const,
          render: (record: any) => {
            const value = record.payment_status;
            const statusColors: { [key: string]: string } = {
              'pending': 'orange',
              'partial': 'blue',
              'paid': 'green',
              'overdue': 'red'
            };
            const statusLabels: { [key: string]: string } = {
              'pending': 'معلق',
              'partial': 'جزئي',
              'paid': 'مدفوع',
              'overdue': 'متأخر'
            };
            return (
              <Tag color={statusColors[value] || 'default'}>
                {statusLabels[value] || value}
              </Tag>
            );
          }
        },
        {
          title: 'تاريخ الأمر',

          sortable: true,
          key: 'order_date',
          width: 120,
          align: 'center' as const
        },
        {
          title: 'تاريخ الفاتورة',

          sortable: true,
          key: 'invoice_date',
          width: 120,
          align: 'center' as const
        }
      ];

      // إعداد الإحصائيات
      const avgProfitMargin = totalRevenue > 0 ? (totalEstimatedProfit / totalRevenue * 100) : 0;
      const paymentRate = totalRevenue > 0 ? (totalPaid / totalRevenue * 100) : 0;

      const statistics = [
        {
          title: 'إجمالي الأوامر',
          value: totalOrders,
          color: '#1890ff',
          icon: '📋'
        },
        {
          title: 'إجمالي المساحة (م²)',
          value: totalArea.toLocaleString('ar-EG', { minimumFractionDigits: 2 }),
          color: '#fa8c16',
          icon: '📐'
        },
        {
          title: 'إجمالي الإيرادات',
          value: `${totalRevenue.toLocaleString('ar-EG')} ج.م`,
          color: '#13c2c2',
          icon: '💰'
        },
        {
          title: 'إجمالي التكلفة المقدرة',
          value: `${totalEstimatedCost.toLocaleString('ar-EG')} ج.م`,
          color: '#fa8c16',
          icon: '💸'
        },
        {
          title: 'إجمالي الربح المقدر',
          value: `${totalEstimatedProfit.toLocaleString('ar-EG')} ج.م`,
          color: '#52c41a',
          icon: '💵'
        },
        {
          title: 'متوسط هامش الربح',
          value: `${avgProfitMargin.toFixed(1)}%`,
          color: '#722ed1',
          icon: '📊'
        },
        {
          title: 'إجمالي المدفوع',
          value: `${totalPaid.toLocaleString('ar-EG')} ج.م`,
          color: '#52c41a',
          icon: '✅'
        },
        {
          title: 'معدل التحصيل',
          value: `${paymentRate.toFixed(1)}%`,
          color: '#eb2f96',
          icon: '📈'
        }
      ];

      return {
        title: 'تقرير ربحية الدهان',
        columns,
        data: tableData,
        statistics,
        summary: {
          totalRecords: totalOrders,
          totalRevenue: totalRevenue,
          totalPaid: totalPaid,
          totalOutstanding: totalOutstanding,
          totalProfit: totalEstimatedProfit
        }
      };

    } catch (error) {
      Logger.error('PaintProfitabilityReport', 'خطأ في إنشاء تقرير ربحية الدهان:', error);
      throw new Error('فشل في تحميل بيانات التقرير');
    }
  };

  return (
    <UniversalReport
      reportType={'paint_profitability' as ReportType}
      title="تقرير ربحية الدهان"
      description="تقرير تحليلي لربحية الدهان مع مؤشرات الأداء المالي"
      onGenerateReport={generateReport}
      showDateRange={true}
    />
  );
};

export default PaintProfitabilityReport;
