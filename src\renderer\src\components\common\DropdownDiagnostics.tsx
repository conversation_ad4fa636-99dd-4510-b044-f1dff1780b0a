import React, { useState, useEffect } from 'react'
import { Card, Button, Space, Alert, Typography, Tag, Spin, List } from 'antd'
import { CheckCircleOutlined, ExclamationCircleOutlined, CloseCircleOutlined, ReloadOutlined } from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'

const { Text, Paragraph } = Typography

interface DropdownTest {
  name: string
  description: string
  status: 'loading' | 'success' | 'warning' | 'error'
  message: string
  data?: any[]
  error?: string
}

interface DropdownDiagnosticsProps {
  visible?: boolean
  onClose?: () => void
}

/**
 * مكون تشخيص وفحص القوائم المنسدلة
 */
const DropdownDiagnostics: React.FC<DropdownDiagnosticsProps> = ({
  visible = false,
  onClose
}) => {
  const [tests, setTests] = useState<DropdownTest[]>([])
  const [loading, setLoading] = useState(false)

  // قائمة الاختبارات
  const dropdownTests = [
    {
      name: 'customers',
      description: 'قائمة العملاء',
      apiCall: () => window.electronAPI?.getCustomers()
    },
    {
      name: 'suppliers',
      description: 'قائمة الموردين',
      apiCall: () => window.electronAPI?.getSuppliers()
    },
    {
      name: 'items',
      description: 'قائمة الأصناف',
      apiCall: () => window.electronAPI?.getItems()
    },
    {
      name: 'warehouses',
      description: 'قائمة المخازن',
      apiCall: () => window.electronAPI?.getWarehouses()
    },
    {
      name: 'categories',
      description: 'قائمة الفئات',
      apiCall: () => window.electronAPI?.getCategories()
    },
    {
      name: 'employees',
      description: 'قائمة الموّفين',
      apiCall: () => window.electronAPI?.getEmployees()
    },
    {
      name: 'departments',
      description: 'قائمة الأقسام',
      apiCall: () => window.electronAPI?.getDepartments()
    },
    {
      name: 'accounts',
      description: 'قائمة الحسابات',
      apiCall: () => (window.electronAPI as any)?.getAccounts()
    },
    {
      name: 'users',
      description: 'قائمة المستخدمين',
      apiCall: () => window.electronAPI?.getUsers()
    }
  ]

  // تشغيل جميع الاختبارات
  const runAllTests = async () => {
    setLoading(true)
    const results: DropdownTest[] = []

    for (const test of dropdownTests) {
      try {
        const result: DropdownTest = {
          name: test.name,
          description: test.description,
          status: 'loading',
          message: 'جاري الاختبار...'
        }
        
        results.push(result)
        setTests([...results])

        if (!test.apiCall) {
          result.status = 'error'
          result.message = 'API غير متوفر'
          result.error = 'لا يوجد استدعاء API محدد'
          continue
        }

        const response = await test.apiCall()
        
        if (!response) {
          result.status = 'error'
          result.message = 'لا توجد استجابة من الخادم'
          result.error = 'الاستجابة فارغة أو null'
        } else if (response.success === false) {
          result.status = 'error'
          result.message = response.message || 'فشل في تحميل البيانات'
          result.error = response.error || 'خطأ غير محدد'
        } else if (response.success === true) {
          const data = response.data || []
          result.status = Array.isArray(data) && data.length > 0 ? 'success' : 'warning'
          result.message = Array.isArray(data) ? 
            `تم تحميل ${data.length} عنصر بنجاح` : 
            'تم التحميل لكن البيانات ليست مصفوفة'
          result.data = Array.isArray(data) ? data : []
          
          if (data.length === 0) {
            result.status = 'warning'
            result.message = 'تم التحميل بنجاح لكن لا توجد بيانات'
          }
        } else if (Array.isArray(response)) {
          // للتوافق مع الإصدارات القديمة
          result.status = response.length > 0 ? 'success' : 'warning'
          result.message = `تم تحميل ${response.length} عنصر بنجاح`
          result.data = response
          
          if (response.length === 0) {
            result.status = 'warning'
            result.message = 'تم التحميل بنجاح لكن لا توجد بيانات'
          }
        } else {
          result.status = 'warning'
          result.message = 'تنسيق استجابة غير متوقع'
          result.error = 'البيانات المستلمة ليست في التنسيق المتوقع'
        }

        Logger.info('DropdownDiagnostics', `✅ اختبار ${test.name}: ${result.status}`)
        
      } catch (error) {
        const result = results.find(r => r.name === test.name)
        if (result) {
          result.status = 'error'
          result.message = 'حدث خطأ أثناء الاختبار'
          result.error = error instanceof Error ? error.message : 'خطأ غير معروف'
        }
        Logger.error('DropdownDiagnostics', `❌ خطأ في اختبار ${test.name}:`, error)
      }
      
      setTests([...results])
    }

    setLoading(false)
  }

  // تشغيل الاختبارات عند فتح المكون
  useEffect(() => {
    if (visible) {
      runAllTests()
    }
  }, [visible])

  // إحصائيات النتائج
  const successCount = tests.filter(t => t.status === 'success').length
  const warningCount = tests.filter(t => t.status === 'warning').length
  const errorCount = tests.filter(t => t.status === 'error').length

  // أيقونة الحالة
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
      case 'loading':
        return <Spin size="small" />
      default:
        return null
    }
  }

  // لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'success'
      case 'warning': return 'warning'
      case 'error': return 'error'
      case 'loading': return 'processing'
      default: return 'default'
    }
  }

  if (!visible) return null

  return (
    <Card
      title="تشخيص القوائم المنسدلة"
      extra={
        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={runAllTests}
            loading={loading}
          >
            إعادة الاختبار
          </Button>
          {onClose && (
            <Button onClick={onClose}>
              إغلاق
            </Button>
          )}
        </Space>
      }
      style={{ margin: '16px 0' }}
    >
      {/* ملخص النتائج */}
      <Space style={{ marginBottom: 16 }}>
        <Tag color="success">نجح: {successCount}</Tag>
        <Tag color="warning">تحذير: {warningCount}</Tag>
        <Tag color="error">فشل: {errorCount}</Tag>
        <Tag color="default">المجموع: {tests.length}</Tag>
      </Space>

      {/* قائمة النتائج */}
      <List
        dataSource={tests}
        renderItem={(test) => (
          <List.Item>
            <List.Item.Meta
              avatar={getStatusIcon(test.status)}
              title={
                <Space>
                  <Text strong>{test.description}</Text>
                  <Tag color={getStatusColor(test.status)}>{test.status}</Tag>
                </Space>
              }
              description={
                <div>
                  <Paragraph style={{ margin: 0 }}>{test.message}</Paragraph>
                  {test.error && (
                    <Alert
                      message="تفاصيل الخطأ"
                      description={test.error}
                      type="error"
                      style={{ marginTop: 8 }}
                    />
                  )}
                  {test.data && test.data.length > 0 && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      عدد العناصر: {test.data.length}
                    </Text>
                  )}
                </div>
              }
            />
          </List.Item>
        )}
      />

      {/* توصيات الإصلاح */}
      {(errorCount > 0 || warningCount > 0) && (
        <Alert
          message="توصيات الإصلاح"
          description={
            <ul>
              {errorCount > 0 && <li>تحقق من اتصال قاعدة البيانات</li>}
              {warningCount > 0 && <li>تأكد من وجود بيانات في الجداول</li>}
              <li>راجع سجلات الأخطاء للحصول على تفاصيل أكثر</li>
              <li>تأكد من تشغيل الخدمات المطلوبة</li>
            </ul>
          }
          type="info"
          style={{ marginTop: 16 }}
        />
      )}
    </Card>
  )
}

export default DropdownDiagnostics
