import { ipcMain } from 'electron'
import { Logger } from '../utils/logger'

// متغيرات الخدمات
let employeeService: any = null

// دالة تعيين خدمة الموّفين
export function setEmployeeService(service: any) {
  employeeService = service
}

// تسجيل معالجات الموّفين
export function registerEmployeeHandlers() {
  // الموّفين
  ipcMain.handle('get-employees', async () => {
    try {
      const employees = await employeeService.getEmployees()
      return { success: true, data: employees }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في جلب الموّفين:', error)
      return { success: false, message: 'حدث خطأ في جلب الموّفين' }
    }
  })

  ipcMain.handle('create-employee', async (_event, employeeData: any) => {
    try {
      return await employeeService.createEmployee(employeeData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في إنشاء الموّف:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الموّف' }
    }
  })

  ipcMain.handle('update-employee', async (_event, employeeId: number, employeeData: any) => {
    try {
      return await employeeService.updateEmployee(employeeId, employeeData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تحديث الموّف:', error)
      return { success: false, message: 'حدث خطأ في تحديث الموّف' }
    }
  })

  ipcMain.handle('delete-employee', async (_event, employeeId: number) => {
    try {
      return await employeeService.deleteEmployee(employeeId)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في حذف الموّف:', error)
      return { success: false, message: 'حدث خطأ في حذف الموّف' }
    }
  })

  ipcMain.handle('generate-employee-code', async () => {
    try {
      const code = await employeeService.generateEmployeeCode()
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في توليد كود الموّف:', error)
      return { success: false, message: 'حدث خطأ في توليد كود الموّف' }
    }
  })

  ipcMain.handle('generate-department-code', async () => {
    try {
      const code = await employeeService.generateDepartmentCode()
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في توليد كود القسم:', error)
      return { success: false, message: 'حدث خطأ في توليد كود القسم' }
    }
  })

  // الأقسام
  ipcMain.handle('get-departments', async () => {
    try {
      const departments = await employeeService.getDepartments()
      return { success: true, data: departments }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في جلب الأقسام:', error)
      return { success: false, message: 'حدث خطأ في جلب الأقسام' }
    }
  })

  ipcMain.handle('get-employee-departments', async () => {
    try {
      const departments = await employeeService.getDepartments()
      return { success: true, data: departments }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في جلب أقسام الموّفين:', error)
      return { success: false, message: 'حدث خطأ في جلب أقسام الموّفين' }
    }
  })

  ipcMain.handle('create-department', async (_event, departmentData: any) => {
    try {
      return await employeeService.createDepartment(departmentData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في إنشاء القسم:', error)
      return { success: false, message: 'حدث خطأ في إنشاء القسم' }
    }
  })

  ipcMain.handle('create-employee-department', async (_event, departmentData: any) => {
    try {
      return await employeeService.createDepartment(departmentData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في إنشاء قسم الموّفين:', error)
      return { success: false, message: 'حدث خطأ في إنشاء قسم الموّفين' }
    }
  })

  ipcMain.handle('update-employee-department', async (_event, departmentId: number, departmentData: any) => {
    try {
      return await employeeService.updateDepartment(departmentId, departmentData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تحديث قسم الموّفين:', error)
      return { success: false, message: 'حدث خطأ في تحديث قسم الموّفين' }
    }
  })

  ipcMain.handle('delete-employee-department', async (_event, departmentId: number) => {
    try {
      return await employeeService.deleteDepartment(departmentId)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في حذف قسم الموّفين:', error)
      return { success: false, message: 'حدث خطأ في حذف قسم الموّفين' }
    }
  })

  // أجهزة البصمة
  ipcMain.handle('get-fingerprint-devices', async () => {
    try {
      const devices = await employeeService.getFingerprintDevices()
      return { success: true, data: devices }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في جلب أجهزة البصمة:', error)
      return { success: false, message: 'حدث خطأ في جلب أجهزة البصمة' }
    }
  })

  ipcMain.handle('create-fingerprint-device', async (_event, deviceData: any) => {
    try {
      return await employeeService.createFingerprintDevice(deviceData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في إنشاء جهاز البصمة:', error)
      return { success: false, message: 'حدث خطأ في إنشاء جهاز البصمة' }
    }
  })

  ipcMain.handle('update-fingerprint-device', async (_event, deviceId: number, deviceData: any) => {
    try {
      return await employeeService.updateFingerprintDevice(deviceId, deviceData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تحديث جهاز البصمة:', error)
      return { success: false, message: 'حدث خطأ في تحديث جهاز البصمة' }
    }
  })

  ipcMain.handle('delete-fingerprint-device', async (_event, deviceId: number) => {
    try {
      return await employeeService.deleteFingerprintDevice(deviceId)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في حذف جهاز البصمة:', error)
      return { success: false, message: 'حدث خطأ في حذف جهاز البصمة' }
    }
  })

  ipcMain.handle('sync-fingerprint-device', async (_event, deviceId: number) => {
    try {
      return await employeeService.syncFingerprintDevice(deviceId)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في مزامنة جهاز البصمة:', error)
      return { success: false, message: 'حدث خطأ في مزامنة جهاز البصمة' }
    }
  })

  // الحضور والانصراف
  ipcMain.handle('record-attendance', async (_event, attendanceData: any) => {
    try {
      // دعم كلا الطريقتين: كائن أو معاملات منفصلة
      if (typeof attendanceData === 'object' && attendanceData.employee_id) {
        // إذا تم تمرير كائن
        return await employeeService.recordAttendanceData(attendanceData)
      } else {
        // إذا تم تمرير معاملات منفصلة (للتوافق مع النسخة القديمة)
        const employeeId = attendanceData
        const checkIn = arguments[2]
        const date = arguments[3]
        return await employeeService.recordAttendance(employeeId, checkIn, date)
      }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تسجيل الحضور:', error)
      return { success: false, message: 'حدث خطأ في تسجيل الحضور' }
    }
  })

  ipcMain.handle('record-checkout', async (_event, employeeId: number, checkOut: string, date?: string) => {
    try {
      return await employeeService.recordCheckOut(employeeId, checkOut, date)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تسجيل الانصراف:', error)
      return { success: false, message: 'حدث خطأ في تسجيل الانصراف' }
    }
  })

  ipcMain.handle('get-attendance-records', async (_event, employeeId?: number, startDate?: string, endDate?: string) => {
    try {
      const records = await employeeService.getAttendanceRecords(employeeId, startDate, endDate)
      return { success: true, data: records }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في جلب سجلات الحضور:', error)
      return { success: false, message: 'حدث خطأ في جلب سجلات الحضور' }
    }
  })

  ipcMain.handle('get-employee-attendance', async (_event, filters?: any) => {
    try {
      const records = await employeeService.getEmployeeAttendance(filters)
      return { success: true, data: records }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في جلب بيانات الحضور:', error)
      return { success: false, message: 'فشل في جلب بيانات الحضور' }
    }
  })

  // الرواتب
  ipcMain.handle('get-employee-payroll', async (_event, filters?: any) => {
    try {
      const records = await employeeService.getEmployeePayroll(filters)
      return { success: true, data: records }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في جلب سجلات الرواتب:', error)
      return { success: false, message: 'فشل في جلب بيانات الرواتب' }
    }
  })

  ipcMain.handle('calculate-payroll', async (_event, payrollData: any) => {
    try {
      return await employeeService.calculatePayroll(payrollData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في حساب الراتب:', error)
      return { success: false, message: 'حدث خطأ في حساب الراتب' }
    }
  })

  ipcMain.handle('update-payroll', async (_event, payrollId: number, payrollData: any) => {
    try {
      return await employeeService.updatePayroll(payrollId, payrollData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تحديث سجل الراتب:', error)
      return { success: false, message: 'حدث خطأ في تحديث سجل الراتب' }
    }
  })

  ipcMain.handle('delete-payroll', async (_event, payrollId: number) => {
    try {
      return await employeeService.deletePayroll(payrollId)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في حذف سجل الراتب:', error)
      return { success: false, message: 'حدث خطأ في حذف سجل الراتب' }
    }
  })

  // الإجازات
  ipcMain.handle('get-employee-leaves', async (_event, filters?: any) => {
    try {
      const records = await employeeService.getEmployeeLeaves(filters)
      return { success: true, data: records }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في جلب سجلات الإجازات:', error)
      return { success: false, message: 'فشل في جلب بيانات الإجازات' }
    }
  })

  ipcMain.handle('create-leave-request', async (_event, leaveData: any) => {
    try {
      return await employeeService.createLeaveRequest(leaveData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في إنشاء طلب الإجازة:', error)
      return { success: false, message: 'حدث خطأ في إنشاء طلب الإجازة' }
    }
  })

  ipcMain.handle('update-leave-request', async (_event, leaveId: number, leaveData: any) => {
    try {
      return await employeeService.updateLeaveRequest(leaveId, leaveData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تحديث طلب الإجازة:', error)
      return { success: false, message: 'حدث خطأ في تحديث طلب الإجازة' }
    }
  })

  ipcMain.handle('approve-leave-request', async (_event, leaveId: number, approvalData: any) => {
    try {
      return await employeeService.approveLeaveRequest(leaveId, approvalData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في الموافقة على طلب الإجازة:', error)
      return { success: false, message: 'حدث خطأ في معالجة طلب الإجازة' }
    }
  })

  ipcMain.handle('delete-leave-request', async (_event, leaveId: number) => {
    try {
      return await employeeService.deleteLeaveRequest(leaveId)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في حذف طلب الإجازة:', error)
      return { success: false, message: 'حدث خطأ في حذف طلب الإجازة' }
    }
  })

  // تقارير الموّفين
  ipcMain.handle('get-employee-report', async (_event, filters?: any) => {
    try {
      const report = await employeeService.getEmployeeReport(filters)
      return { success: true, data: report }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تقرير الموّفين:', error)
      return { success: false, message: 'حدث خطأ في تقرير الموّفين' }
    }
  })

  ipcMain.handle('get-attendance-report', async (_event, filters?: any) => {
    try {
      const report = await employeeService.getAttendanceReport(filters)
      return { success: true, data: report }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تقرير الحضور:', error)
      return { success: false, message: 'حدث خطأ في تقرير الحضور' }
    }
  })

  // الرواتب
  ipcMain.handle('get-employee-salaries', async (_event, employeeId?: number) => {
    try {
      const salaries = await employeeService.getEmployeeSalaries(employeeId)
      return { success: true, data: salaries }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في جلب رواتب الموّفين:', error)
      return { success: false, message: 'حدث خطأ في جلب رواتب الموّفين' }
    }
  })

  ipcMain.handle('create-salary-record', async (_event, salaryData: any) => {
    try {
      return await employeeService.createSalaryRecord(salaryData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في إنشاء سجل راتب:', error)
      return { success: false, message: 'حدث خطأ في إنشاء سجل راتب' }
    }
  })

  // تقارير الموّفين المفقودة
  ipcMain.handle('get-employee-payroll-report', async (_event, filters?: any) => {
    try {
      const report = await employeeService.getEmployeePayroll(filters)
      return { success: true, data: report }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تقرير رواتب الموّفين:', error)
      return { success: false, message: 'حدث خطأ في تقرير رواتب الموّفين' }
    }
  })

  ipcMain.handle('get-employee-attendance-report', async (_event, filters?: any) => {
    try {
      const report = await employeeService.getEmployeeAttendance(filters)
      return { success: true, data: report }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تقرير حضور الموّفين:', error)
      return { success: false, message: 'حدث خطأ في تقرير حضور الموّفين' }
    }
  })

  ipcMain.handle('get-employee-leaves-report', async (_event, filters?: any) => {
    try {
      const report = await employeeService.getEmployeeLeaves(filters)
      return { success: true, data: report }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تقرير إجازات الموّفين:', error)
      return { success: false, message: 'حدث خطأ في تقرير إجازات الموّفين' }
    }
  })

  ipcMain.handle('get-employee-performance-report', async (_event, filters?: any) => {
    try {
      const report = await employeeService.getEmployeePerformance(filters)
      return { success: true, data: report }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تقرير أداء الموّفين:', error)
      return { success: false, message: 'حدث خطأ في تقرير أداء الموّفين' }
    }
  })

  ipcMain.handle('get-employee-overtime-report', async (_event, filters?: any) => {
    try {
      const report = await employeeService.getEmployeeOvertime(filters)
      return { success: true, data: report }
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في تقرير العمل الإضافي:', error)
      return { success: false, message: 'حدث خطأ في تقرير العمل الإضافي' }
    }
  })

  // معالجات إضافية للرواتب
  ipcMain.handle('calculate-employee-payroll', async (_event, payrollData: any) => {
    try {
      return await employeeService.calculatePayroll(payrollData)
    } catch (error) {
      Logger.error('EmployeeHandlers', 'خطأ في حساب راتب الموّف:', error)
      return { success: false, message: 'حدث خطأ في حساب راتب الموّف' }
    }
  })
}
