import React, { useState, useEffect } from 'react'
import { Mo<PERSON>, Select, Button, message, Space, Card, Typography, Row, Col, Tag, Divider } from 'antd'
import { LoginOutlined, LogoutOutlined, ClockCircleOutlined, UserOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import { SafeLogger as Logger } from '../../utils/logger'

const { Title, Text } = Typography
const { Option } = Select

interface Employee {
  id: number
  full_name: string
  employee_number: string
  department: string
  position: string
}

interface QuickAttendanceProps {
  visible: boolean
  onClose: () => void
  type: 'checkin' | 'checkout'
}

const QuickAttendance: React.FC<QuickAttendanceProps> = ({ visible, onClose, type }) => {
  const [employees, setEmployees] = useState<Employee[]>([])
  const [selectedEmployee, setSelectedEmployee] = useState<number | null>(null)
  const [loading, setLoading] = useState(false)
  const [loadingEmployees, setLoadingEmployees] = useState(false)

  useEffect(() => {
    if (visible) {
      fetchEmployees()
    }
  }, [visible])

  const fetchEmployees = async () => {
    setLoadingEmployees(true)
    try {
      const result = await window.electronAPI.getEmployees()
      if (result.success && result.data) {
        setEmployees(result.data)
        if (result.data.length === 0) {
          message.warning('لا يوجد موظفين مسجلين في النظام')
        }
      } else {
        message.error(result.message || 'فشل في جلب قائمة الموظفين')
        setEmployees([])
      }
    } catch (error) {
      Logger.error('QuickAttendance', 'خطأ في جلب الموظفين:', error)
      message.error('حدث خطأ في الاتصال بقاعدة البيانات')
      setEmployees([])
    } finally {
      setLoadingEmployees(false)
    }
  }

  const handleRecord = async () => {
    if (!selectedEmployee) {
      message.warning('يرجى اختيار موظف')
      return
    }

    setLoading(true)
    try {
      const currentTime = dayjs().format('HH:mm:ss')
      const currentDate = dayjs().format('YYYY-MM-DD')

      let result
      if (type === 'checkin') {
        // استخدام الطريقة الصحيحة لتسجيل الحضور
        const attendanceData = {
          employee_id: selectedEmployee,
          date: currentDate,
          check_in_time: currentTime,
          status: 'present',
          attendance_source: 'manual'
        }
        result = await window.electronAPI.recordAttendance(attendanceData)
      } else {
        // استخدام الطريقة الصحيحة لتسجيل الانصراف
        result = await window.electronAPI.recordCheckout(selectedEmployee, currentTime, currentDate)
      }

      if (result.success) {
        message.success(result.message)
        setSelectedEmployee(null)
        onClose()
      } else {
        message.error(result.message)
      }
    } catch (error) {
      Logger.error('QuickAttendance', `خطأ في تسجيل ${type}:`, error)
      message.error(`حدث خطأ في تسجيل ${type === 'checkin' ? 'الحضور' : 'الانصراف'}`)
    } finally {
      setLoading(false)
    }
  }

  const selectedEmployeeData = employees.find(emp => emp.id === selectedEmployee)

  return (
    <Modal
      title={
        <Space>
          {type === 'checkin' ? <LoginOutlined /> : <LogoutOutlined />}
          {type === 'checkin' ? 'تسجيل حضور سريع' : 'تسجيل انصراف سريع'}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          إلغاء
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleRecord}
          disabled={!selectedEmployee}
          icon={type === 'checkin' ? <LoginOutlined /> : <LogoutOutlined />}
        >
          {type === 'checkin' ? 'تسجيل الحضور' : 'تسجيل الانصراف'}
        </Button>
      ]}
      width={600}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* معلومات الوقت الحالي */}
        <Card size="small" style={{ background: '#f6ffed', border: '1px solid #b7eb8f' }}>
          <Row gutter={16} align="middle">
            <Col span={12}>
              <Space>
                <ClockCircleOutlined style={{ color: '#52c41a' }} />
                <Text strong>الوقت الحالي:</Text>
                <Tag color="green">{dayjs().format('HH:mm:ss')}</Tag>
              </Space>
            </Col>
            <Col span={12}>
              <Space>
                <Text strong>التاريخ:</Text>
                <Tag color="blue">{dayjs().format('YYYY-MM-DD')}</Tag>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* اختيار الموظف */}
        <div>
          <Title level={5}>
            <UserOutlined /> اختر الموظف:
          </Title>
          <Select
            style={{ width: '100%' }}
            placeholder="ابحث عن موظف..."
            showSearch
            loading={loadingEmployees}
            value={selectedEmployee}
            onChange={setSelectedEmployee}
            filterOption={(input, option) =>
              option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ||
              option?.value?.toString().includes(input)
            }
            size="large"
          >
            {employees.map(employee => (
              <Option key={employee.id} value={employee.id}>
                <Space>
                  <Text strong>{employee.full_name}</Text>
                  <Text type="secondary">({employee.employee_number})</Text>
                  <Tag>{employee.department}</Tag>
                </Space>
              </Option>
            ))}
          </Select>
        </div>

        {/* معلومات الموظف المختار */}
        {selectedEmployeeData && (
          <>
            <Divider />
            <Card size="small" title="معلومات الموظف المختار">
              <Row gutter={16}>
                <Col span={12}>
                  <Text strong>الاسم:</Text> {selectedEmployeeData.full_name}
                </Col>
                <Col span={12}>
                  <Text strong>رقم الموظف:</Text> {selectedEmployeeData.employee_number}
                </Col>
                <Col span={12}>
                  <Text strong>القسم:</Text> {selectedEmployeeData.department}
                </Col>
                <Col span={12}>
                  <Text strong>المنصب:</Text> {selectedEmployeeData.position}
                </Col>
              </Row>
            </Card>
          </>
        )}

        {/* تنبيه */}
        <Card size="small" style={{ background: '#fff7e6', border: '1px solid #ffd591' }}>
          <Text type="warning">
            💡 سيتم تسجيل {type === 'checkin' ? 'الحضور' : 'الانصراف'} بالوقت الحالي تلقائياً
          </Text>
          {employees.length === 0 && !loadingEmployees && (
            <div style={{ marginTop: 8 }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                ⚠️ لا يوجد موّفين متاحين للتسجيل
              </Text>
            </div>
          )}
        </Card>
      </Space>
    </Modal>
  )
}

export default QuickAttendance
