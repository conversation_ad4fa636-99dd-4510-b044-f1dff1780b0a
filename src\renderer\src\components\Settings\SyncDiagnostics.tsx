import React, { useState, useEffect } from 'react'
import { <PERSON>, Alert, Button, Space, Typography, Steps, List, Tag, Divider, message } from 'antd'
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  DatabaseOutlined,
  GlobalOutlined,
  FolderOutlined,
  ToolOutlined
} from '@ant-design/icons'

const { Title, Text, Paragraph } = Typography
const { Step } = Steps

interface DiagnosticResult {
  step: string
  status: 'success' | 'warning' | 'error'
  message: string
  details?: string
}

const SyncDiagnostics: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([])
  const [overallStatus, setOverallStatus] = useState<'success' | 'warning' | 'error'>('success')

  // تشغيل التشخيص الشامل
  const runDiagnostics = async () => {
    setLoading(true)
    setDiagnostics([])
    
    const results: DiagnosticResult[] = []

    try {
      // 1. فحص حالة قاعدة البيانات
      results.push(await checkDatabaseStatus())
      
      // 2. فحص إعدادات المزامنة
      results.push(await checkSyncSettings())
      
      // 3. فحص المجلد المشترك
      results.push(await checkSharedFolder())
      
      // 4. فحص الاتصال بالشبكة
      results.push(await checkNetworkConnection())
      
      // 5. فحص الأجهزة المتصلة
      results.push(await checkConnectedDevices())

      setDiagnostics(results)
      
      // تحديد الحالة العامة
      const hasError = results.some(r => r.status === 'error')
      const hasWarning = results.some(r => r.status === 'warning')
      
      if (hasError) {
        setOverallStatus('error')
      } else if (hasWarning) {
        setOverallStatus('warning')
      } else {
        setOverallStatus('success')
      }

    } catch (error) {
      message.error('خطأ في تشغيل التشخيص')
    } finally {
      setLoading(false)
    }
  }

  // فحص حالة قاعدة البيانات
  const checkDatabaseStatus = async (): Promise<DiagnosticResult> => {
    try {
      const response = await window.electronAPI.invoke('get-database-type')
      if (response.success) {
        return {
          step: 'حالة قاعدة البيانات',
          status: 'success',
          message: `قاعدة البيانات تعمل بشكل صحيح (${response.type === 'local' ? 'محلية' : 'مشتركة'})`,
          details: response.path
        }
      } else {
        return {
          step: 'حالة قاعدة البيانات',
          status: 'error',
          message: 'خطأ في قاعدة البيانات',
          details: response.message
        }
      }
    } catch (error) {
      return {
        step: 'حالة قاعدة البيانات',
        status: 'error',
        message: 'فشل في فحص قاعدة البيانات',
        details: 'خطأ في الاتصال بالخدمة'
      }
    }
  }

  // فحص إعدادات المزامنة
  const checkSyncSettings = async (): Promise<DiagnosticResult> => {
    try {
      const response = await window.electronAPI.invoke('get-sync-settings')
      if (response.success) {
        const settings = response.data
        if (settings.enabled) {
          if (settings.sharedFolder) {
            return {
              step: 'إعدادات المزامنة',
              status: 'success',
              message: 'المزامنة مفعلة ومكونة بشكل صحيح',
              details: `المجلد المشترك: ${settings.sharedFolder}`
            }
          } else {
            return {
              step: 'إعدادات المزامنة',
              status: 'warning',
              message: 'المزامنة مفعلة لكن لم يتم تحديد مجلد مشترك',
              details: 'يرجى تحديد مجلد مشترك في إعدادات المزامنة'
            }
          }
        } else {
          return {
            step: 'إعدادات المزامنة',
            status: 'warning',
            message: 'المزامنة غير مفعلة',
            details: 'يمكنك تفعيل المزامنة من إعدادات المزامنة'
          }
        }
      } else {
        return {
          step: 'إعدادات المزامنة',
          status: 'error',
          message: 'خطأ في جلب إعدادات المزامنة',
          details: response.message
        }
      }
    } catch (error) {
      return {
        step: 'إعدادات المزامنة',
        status: 'error',
        message: 'فشل في فحص إعدادات المزامنة',
        details: 'خطأ في الاتصال بالخدمة'
      }
    }
  }

  // فحص المجلد المشترك
  const checkSharedFolder = async (): Promise<DiagnosticResult> => {
    try {
      const settingsResponse = await window.electronAPI.invoke('get-sync-settings')
      if (!settingsResponse.success || !settingsResponse.data.sharedFolder) {
        return {
          step: 'المجلد المشترك',
          status: 'warning',
          message: 'لم يتم تحديد مجلد مشترك',
          details: 'يرجى تحديد مجلد مشترك في إعدادات المزامنة'
        }
      }

      const testResponse = await window.electronAPI.invoke('test-sync-connection', settingsResponse.data.sharedFolder)
      if (testResponse.success) {
        return {
          step: 'المجلد المشترك',
          status: 'success',
          message: 'المجلد المشترك يعمل بشكل صحيح',
          details: settingsResponse.data.sharedFolder
        }
      } else {
        return {
          step: 'المجلد المشترك',
          status: 'error',
          message: 'مشكلة في المجلد المشترك',
          details: testResponse.message
        }
      }
    } catch (error) {
      return {
        step: 'المجلد المشترك',
        status: 'error',
        message: 'فشل في فحص المجلد المشترك',
        details: 'خطأ في الاتصال بالخدمة'
      }
    }
  }

  // فحص الاتصال بالشبكة
  const checkNetworkConnection = async (): Promise<DiagnosticResult> => {
    try {
      const response = await window.electronAPI.invoke('get-network-info')
      if (response.success) {
        return {
          step: 'الاتصال بالشبكة',
          status: 'success',
          message: 'الاتصال بالشبكة يعمل بشكل صحيح',
          details: `IP: ${response.ip}, اسم الجهاز: ${response.hostname}`
        }
      } else {
        return {
          step: 'الاتصال بالشبكة',
          status: 'warning',
          message: 'مشكلة في معلومات الشبكة',
          details: response.message
        }
      }
    } catch (error) {
      return {
        step: 'الاتصال بالشبكة',
        status: 'error',
        message: 'فشل في فحص الاتصال بالشبكة',
        details: 'خطأ في الاتصال بالخدمة'
      }
    }
  }

  // فحص الأجهزة المتصلة
  const checkConnectedDevices = async (): Promise<DiagnosticResult> => {
    try {
      const response = await window.electronAPI.invoke('discover-connected-devices')
      if (response.success) {
        const deviceCount = response.devices.length
        return {
          step: 'الأجهزة المتصلة',
          status: deviceCount > 0 ? 'success' : 'warning',
          message: `تم العثور على ${deviceCount} جهاز في الشبكة`,
          details: deviceCount > 0 ? `الأجهزة: ${response.devices.map((d: any) => d.hostname || d.ip).join(', ')}` : 'لا توجد أجهزة أخرى مكتشفة'
        }
      } else {
        return {
          step: 'الأجهزة المتصلة',
          status: 'warning',
          message: 'لم يتم العثور على أجهزة أخرى',
          details: response.message
        }
      }
    } catch (error) {
      return {
        step: 'الأجهزة المتصلة',
        status: 'error',
        message: 'فشل في البحث عن الأجهزة',
        details: 'خطأ في الاتصال بالخدمة'
      }
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'warning': return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
      case 'error': return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
      default: return <SyncOutlined />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'success'
      case 'warning': return 'warning'
      case 'error': return 'error'
      default: return 'default'
    }
  }

  useEffect(() => {
    runDiagnostics()
  }, [])

  return (
    <Card
      title={
        <Space>
          <ToolOutlined />
          تشخيص مشاكل المزامنة
        </Space>
      }
      extra={
        <Button 
          type="primary"
          icon={<SyncOutlined />} 
          onClick={runDiagnostics}
          loading={loading}
        >
          إعادة التشخيص
        </Button>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* الحالة العامة */}
        {diagnostics.length > 0 && (
          <Alert
            message={
              overallStatus === 'success' ? 'جميع الفحوصات نجحت' :
              overallStatus === 'warning' ? 'توجد تحذيرات' :
              'توجد مشاكل تحتاج إلى حل'
            }
            type={getStatusColor(overallStatus) as any}
            showIcon
            icon={getStatusIcon(overallStatus)}
          />
        )}

        {/* نتائج التشخيص */}
        {diagnostics.length > 0 && (
          <List
            dataSource={diagnostics}
            renderItem={(item) => (
              <List.Item>
                <List.Item.Meta
                  avatar={getStatusIcon(item.status)}
                  title={
                    <Space>
                      <Text strong>{item.step}</Text>
                      <Tag color={getStatusColor(item.status)}>
                        {item.status === 'success' ? 'نجح' : 
                         item.status === 'warning' ? 'تحذير' : 'خطأ'}
                      </Tag>
                    </Space>
                  }
                  description={
                    <div>
                      <Text>{item.message}</Text>
                      {item.details && (
                        <div style={{ marginTop: 4 }}>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {item.details}
                          </Text>
                        </div>
                      )}
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}

        {/* نصائح لحل المشاكل */}
        {overallStatus !== 'success' && (
          <>
            <Divider />
            <div>
              <Title level={5}>نصائح لحل المشاكل:</Title>
              <List size="small">
                <List.Item>• تأكد من تفعيل مشاركة الملفات في Windows</List.Item>
                <List.Item>• تحقق من أن جميع الأجهزة متصلة بنفس الشبكة</List.Item>
                <List.Item>• تأكد من وجود صلاحيات كافية للوصول للمجلد المشترك</List.Item>
                <List.Item>• جرب إعادة تشغيل التطبيق أو الجهاز</List.Item>
                <List.Item>• تحقق من إعدادات Firewall وأنها لا تحجب التطبيق</List.Item>
              </List>
            </div>
          </>
        )}
      </Space>
    </Card>
  )
}

export default SyncDiagnostics
