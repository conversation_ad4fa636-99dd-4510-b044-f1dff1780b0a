{"name": "zetia-accounting-app", "version": "1.8.0", "description": "ZET.IA - Complete Accounting and Production Management System with Advanced Visual Template Editor by FARESNAWAF", "author": "FARESNAWAF <<EMAIL>>", "main": "dist/main/main/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:renderer": "vite --config vite.config.ts", "dev:main": "tsc -p tsconfig.main.json --watch", "electron": "electron dist/main/main/main.js", "electron:dev": "npm run build && cross-env NODE_ENV=production electron dist/main/main/main.js", "build": "npm run build:renderer && npm run build:main && npm run copy-assets", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "copy-assets": "powershell -Command \"Copy-Item 'src/renderer/public/default-logo.svg' 'dist/renderer/default-logo.svg' -Force\"", "start": "npm run build && electron dist/main/main/main.js", "pack": "electron dist/main/main/main.js", "postinstall": "echo 'تم تثبيت التبعيات بنجاح'", "rebuild": "electron-rebuild", "test": "node scripts/test-simple-image-system.js", "test:db": "node scripts/test-unified-images-db.js", "test:all": "npm run test:db && npm run test", "build-windows": "npm run build && npx electron-builder --win --publish=never", "build-windows-dir": "npm run build && npx electron-builder --win --dir --publish=never", "build-portable": "node scripts/build-portable-final.js", "build-installer": "npm run build && npx electron-builder --win --publish=never", "build-enhanced": "node scripts/enhanced-build.js", "build-all": "npm run build-enhanced", "cleanup-releases": "node scripts/cleanup-old-releases.js", "build-mac": "npm run build && npx electron-builder --mac --publish=never", "build-linux": "npm run build && npx electron-builder --linux --publish=never"}, "keywords": ["accounting", "production", "inventory", "electron", "react", "zetia"], "license": "MIT", "devDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.46.2", "@types/bcryptjs": "^2.4.6", "@types/electron": "^1.4.38", "@types/estree": "^1.0.8", "@types/file-saver": "^2.0.7", "@types/json-schema": "^7.0.15", "@types/node": "^20.10.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/sql.js": "^1.4.9", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.2.1", "chokidar": "^4.0.3", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^28.0.0", "electron-builder": "^24.9.1", "electron-rebuild": "^3.2.9", "eslint": "^9.32.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "terser": "^5.43.1", "typescript": "^5.3.3", "vite": "^5.0.8"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@types/node-cron": "^3.0.11", "@types/react-window": "^1.8.8", "antd": "^5.12.8", "bcryptjs": "^3.0.2", "buffer": "^6.0.3", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.544.0", "node-cron": "^4.2.1", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-to-print": "^3.1.0", "react-window": "^2.0.2", "react-window-infinite-loader": "^1.0.10", "sql.js": "^1.13.0", "styled-components": "^6.1.6", "xlsx": "^0.18.5"}, "build": {"appId": "com.faresnawaf.zetia", "productName": "ZET.IA", "directories": {"output": "release-new", "buildResources": "build"}, "files": ["dist/**/*", "node_modules/**/*", "!node_modules/.cache", "!node_modules/.vite", "!node_modules/**/test/**", "!node_modules/**/tests/**", "!node_modules/**/*.md", "!node_modules/**/.git*", "!node_modules/**/docs/**", "!node_modules/**/examples/**", "!node_modules/**/*.map", "!node_modules/**/LICENSE*", "!node_modules/**/CHANGELOG*", "!node_modules/**/README*", "!node_modules/**/.bin/**", "!node_modules/**/man/**", "!node_modules/**/*.d.ts"], "asar": false, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "npmRebuild": false, "win": {"target": "nsis", "requestedExecutionLevel": "asInvoker", "signAndEditExecutable": false, "verifyUpdateCodeSignature": false, "artifactName": "${productName}-Setup-${version}-${arch}.${ext}", "extraResources": [{"from": "build/", "to": "build/", "filter": ["**/*"]}, {"from": "resources/sql-backup/", "to": "sql-backup/", "filter": ["**/*"]}, {"from": "node_modules/sql.js/dist/", "to": "sql.js/dist/", "filter": ["**/*"]}]}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icon.png", "hardenedRuntime": false, "gatekeeperAssess": false, "artifactName": "${productName}-${version}-${arch}.${ext}"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "assets/icon.png", "artifactName": "${productName}-${version}-${arch}.${ext}", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "ZET.IA", "perMachine": false, "differentialPackage": false, "warningsAsErrors": false}, "forceCodeSigning": false, "electronDownload": {"cache": "./cache"}, "compression": "maximum", "includeSubNodeModules": true, "extraMetadata": {"main": "dist/main/main/main.js"}, "afterPack": "./scripts/after-pack.js", "publish": null}}