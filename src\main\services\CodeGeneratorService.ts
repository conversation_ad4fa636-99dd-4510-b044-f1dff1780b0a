import { Logger } from '../utils/logger'
// import Database from 'better-sqlite3' // تم تعطيله مؤقتاً

export interface CodeConfig {
  prefix: string
  table: string
  column: string
  length: number
  startNumber?: number
}

export class CodeGeneratorService {
  private db: any // تم تعطيل النوع مؤقتاً

  constructor(database: any) {
    this.db = database
  }

  // توليد كود جديد مع ضمان التفرد
  public async generateUniqueCode(config: CodeConfig): Promise<string> {
    const maxAttempts = 100
    let attempts = 0

    while (attempts < maxAttempts) {
      try {
        const code = await this.generateNextCode(config)
        
        // التحقق من التفرد
        const exists = this.db.prepare(
          `SELECT 1 FROM ${config.table} WHERE ${config.column} = ? LIMIT 1`
        ).get(code)

        if (!exists) {
          return code
        }

        attempts++
      } catch (error) {
        Logger.error('CodeGeneratorService', 'خطأ في توليد الكود (المحاولة ${attempts + 1}):', error)
        attempts++
      }
    }

    // كود احتياطي مع timestamp
    return `${config.prefix}${Date.now().toString().slice(-config.length)}`
  }

  // توليد الكود التالي في التسلسل
  private async generateNextCode(config: CodeConfig): Promise<string> {
    try {
      // البحث عن آخر كود بنفس البادئة مع تحسين الاستعلام
      const query = `
        SELECT ${config.column} FROM ${config.table}
        WHERE ${config.column} LIKE '${config.prefix}%'
        AND LENGTH(${config.column}) = ${config.prefix.length + config.length}
        AND SUBSTR(${config.column}, ${config.prefix.length + 1}) GLOB '[0-9]*'
        ORDER BY CAST(SUBSTR(${config.column}, ${config.prefix.length + 1}) AS INTEGER) DESC
        LIMIT 1
      `

      const lastRecord = this.db.prepare(query).get() as any

      let nextNumber = config.startNumber || 1

      if (lastRecord && lastRecord[config.column]) {
        const numberPart = lastRecord[config.column].substring(config.prefix.length)
        const lastNumber = parseInt(numberPart, 10)
        
        if (!isNaN(lastNumber) && lastNumber >= 0) {
          nextNumber = lastNumber + 1
        }
      }

      return `${config.prefix}${nextNumber.toString().padStart(config.length, '0')}`
    } catch (error) {
      Logger.error('CodeGeneratorService', 'خطأ في توليد الكود التالي:', error)
      throw error
    }
  }

  // التحقق من تفرد الكود
  public checkCodeUniqueness(table: string, column: string, code: string, excludeId?: number): boolean {
    try {
      let query = `SELECT 1 FROM ${table} WHERE ${column} = ?`
      const params: any[] = [code]

      if (excludeId) {
        query += ' AND id != ?'
        params.push(excludeId)
      }

      const exists = this.db.prepare(query).get(params)
      return !exists
    } catch (error) {
      Logger.error('CodeGeneratorService', 'خطأ في التحقق من تفرد الكود:', error)
      return false
    }
  }

  // توليد كود متزامن للاستخدام في المعاملات
  public generateCodeSync(config: CodeConfig): string {
    try {
      const query = `
        SELECT ${config.column} FROM ${config.table}
        WHERE ${config.column} LIKE '${config.prefix}%'
        AND LENGTH(${config.column}) = ${config.prefix.length + config.length}
        AND SUBSTR(${config.column}, ${config.prefix.length + 1}) GLOB '[0-9]*'
        ORDER BY CAST(SUBSTR(${config.column}, ${config.prefix.length + 1}) AS INTEGER) DESC
        LIMIT 1
      `

      const lastRecord = this.db.prepare(query).get() as any
      let nextNumber = config.startNumber || 1

      if (lastRecord && lastRecord[config.column]) {
        const numberPart = lastRecord[config.column].substring(config.prefix.length)
        const lastNumber = parseInt(numberPart, 10)
        
        if (!isNaN(lastNumber) && lastNumber >= 0) {
          nextNumber = lastNumber + 1
        }
      }

      return `${config.prefix}${nextNumber.toString().padStart(config.length, '0')}`
    } catch (error) {
      Logger.error('CodeGeneratorService', 'خطأ في توليد الكود المتزامن:', error)
      return `${config.prefix}${Date.now().toString().slice(-config.length)}`
    }
  }
}
