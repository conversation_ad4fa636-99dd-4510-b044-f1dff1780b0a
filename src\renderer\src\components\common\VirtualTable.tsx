import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react'
import { Table, Spin, Empty, ConfigProvider } from 'antd'
import type { TableProps, ColumnType } from 'antd/es/table'
import styled from 'styled-components'
import { List } from 'react-window'

// أنواع البيانات للجدول الافتراضي
interface VirtualTableProps<T = any> extends Omit<TableProps<T>, 'pagination' | 'onScroll'> {
  data: T[]
  height?: number
  itemHeight?: number
  _overscan?: number
  loading?: boolean
  emptyText?: string
  onScroll?: (scrollTop: number) => void
  enableVirtualization?: boolean
  threshold?: number // عدد العناصر التي تفعل الافتراضية
}

// الأنماط المحسنة
const VirtualTableContainer = styled.div`
  .virtual-table {
    .ant-table {
      .ant-table-container {
        .ant-table-body,
        .ant-table-content {
          overflow: hidden !important;
        }
      }
    }
  }
  
  .virtual-list {
    .ant-table-tbody {
      .ant-table-row {
        display: flex;
        align-items: center;
        
        .ant-table-cell {
          flex: 1;
          border-bottom: 1px solid #f0f0f0;
          padding: 8px 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
  
  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    flex-direction: column;
  }
`

// مكون الصف الافتراضي
const VirtualRow: React.FC<{
  index: number
  style: React.CSSProperties
  data: {
    items: any[]
    columns: ColumnType<any>[]
    rowKey: string | ((record: any) => string)
    onRow?: (record: any, index?: number) => any
  }
}> = ({ index, style, data }) => {
  const { items, columns, rowKey, onRow } = data
  const item = items[index]

  if (!item) return null

  const key = typeof rowKey === 'function' ? rowKey(item) : item[rowKey]
  const rowProps = onRow ? onRow(item, index) : {}

  return (
    <div
      style={style}
      className="ant-table-row"
      key={key}
      {...rowProps}
    >
      {columns.map((column, colIndex) => {
        const { dataIndex, key: colKey, render, width } = column
        let cellContent = item[dataIndex as string]

        if (render) {
          cellContent = render(cellContent, item, index)
        }

        return (
          <div
            key={colKey || colIndex}
            className="ant-table-cell"
            style={{
              width: width || 'auto',
              minWidth: width || 100,
              maxWidth: width || 'none'
            }}
          >
            {cellContent}
          </div>
        )
      })}
    </div>
  )
}

// مكون الجدول الافتراضي
const VirtualTable = <T extends Record<string, any>>({
  data,
  columns = [],
  height = 400,
  itemHeight = 54,
  _overscan = 5,
  loading = false,
  emptyText = 'لا توجد بيانات',
  onScroll,
  enableVirtualization = true,
  threshold = 100,
  rowKey = 'id',
  onRow,
  ...tableProps
}: VirtualTableProps<T>) => {
  const [_scrollTop, setScrollTop] = useState(0)
  const _tableRef = useRef<HTMLDivElement>(null)
  const _listRef = useRef(null)
  
  // تحديد ما إذا كان يجب استخدام الافتراضية
  const shouldUseVirtualization = useMemo(() => {
    return enableVirtualization && data.length > threshold
  }, [enableVirtualization, data.length, threshold])
  
  // معالجة التمرير
  const _handleScroll = useCallback((props: any) => {
    const newScrollTop = props.scrollTop || 0
    setScrollTop(newScrollTop)
    if (onScroll) {
      onScroll(newScrollTop)
    }
  }, [onScroll])
  
  // إعداد بيانات الصفوف
  const _itemData = useMemo(() => ({
    items: data,
    columns,
    rowKey: String(rowKey),
    onRow
  }), [data, columns, rowKey, onRow])
  
  // عرض حالة التحميل
  if (loading) {
    return (
      <VirtualTableContainer>
        <div className="loading-container">
          <Spin size="large" tip="جاري تحميل البيانات..." />
        </div>
      </VirtualTableContainer>
    )
  }
  
  // عرض حالة عدم وجود بيانات
  if (!data || data.length === 0) {
    return (
      <VirtualTableContainer>
        <div className="empty-container">
          <Empty description={emptyText} />
        </div>
      </VirtualTableContainer>
    )
  }
  
  // إذا كانت البيانات قليلة، استخدم الجدول العادي
  if (!shouldUseVirtualization) {
    return (
      <ConfigProvider direction="rtl">
        <Table<T>
          {...tableProps}
          columns={columns}
          dataSource={data}
          rowKey={rowKey}
          onRow={onRow}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} من ${total} عنصر`,
            pageSize: 20
          }}
        />
      </ConfigProvider>
    )
  }
  
  // استخدام الجدول الافتراضي للبيانات الكبيرة
  return (
    <ConfigProvider direction="rtl">
      <VirtualTableContainer>
        <div className="virtual-table">
          <Table<T>
            {...tableProps}
            columns={columns}
            dataSource={[]}
            rowKey={rowKey}
            pagination={false}
            scroll={{ y: height }}
            components={{
              body: () => (
                <div className="virtual-list" style={{ height, overflow: 'auto' }}>
                  {data.map((item, index) => (
                    <VirtualRow
                      key={rowKey ? (typeof rowKey === 'function' ? rowKey(item) : item[rowKey]) : index}
                      index={index}
                      style={{ height: itemHeight }}
                      data={_itemData}
                    />
                  ))}
                </div>
              )
            }}
          />
        </div>
        
        {/* معلومات إضافية */}
        <div style={{ 
          padding: '8px 16px', 
          background: '#fafafa', 
          borderTop: '1px solid #f0f0f0',
          fontSize: '12px',
          color: '#666',
          textAlign: 'center'
        }}>
          إجمالي {data.length.toLocaleString()} عنصر • 
          الافتراضية مفعلة لتحسين الأداء
        </div>
      </VirtualTableContainer>
    </ConfigProvider>
  )
}

// Hook لاستخدام الجدول الافتراضي مع البحث والتصفية
export const useVirtualTable = <T extends Record<string, any>>(
  originalData: T[],
  options: {
    searchFields?: (keyof T)[]
    defaultPageSize?: number
    enableVirtualization?: boolean
    threshold?: number
  } = {}
) => {
  const {
    searchFields = [],
    defaultPageSize = 20,
    enableVirtualization = true,
    threshold = 100
  } = options
  
  const [searchText, setSearchText] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(defaultPageSize)
  
  // تصفية البيانات بناءً على البحث
  const filteredData = useMemo(() => {
    if (!searchText.trim()) return originalData
    
    return originalData.filter(item => {
      if (searchFields.length > 0) {
        return searchFields.some(field => 
          String(item[field]).toLowerCase().includes(searchText.toLowerCase())
        )
      } else {
        return Object.values(item).some(value =>
          String(value).toLowerCase().includes(searchText.toLowerCase())
        )
      }
    })
  }, [originalData, searchText, searchFields])
  
  // تحديد البيانات المعروضة
  const displayData = useMemo(() => {
    if (enableVirtualization && filteredData.length > threshold) {
      return filteredData // عرض كل البيانات مع الافتراضية
    } else {
      // استخدام pagination للبيانات القليلة
      const startIndex = (currentPage - 1) * pageSize
      const endIndex = startIndex + pageSize
      return filteredData.slice(startIndex, endIndex)
    }
  }, [filteredData, currentPage, pageSize, enableVirtualization, threshold])
  
  // إعادة تعيين الصفحة عند تغيير البحث
  useEffect(() => {
    setCurrentPage(1)
  }, [searchText])
  
  return {
    data: displayData,
    totalCount: filteredData.length,
    searchText,
    setSearchText,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    shouldUseVirtualization: enableVirtualization && filteredData.length > threshold
  }
}

export default VirtualTable
export type { VirtualTableProps }
