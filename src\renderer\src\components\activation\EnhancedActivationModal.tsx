import React, { useState, useEffect } from 'react'
import { Input, Button, Typography, Space, Alert, Card } from 'antd'
import { KeyOutlined, SafetyOutlined, PhoneOutlined, MailOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import { SafeLogger as Logger } from '../../utils/logger'

const { Title, Text, Paragraph } = Typography

interface ActivationModalProps {
  visible: boolean
  onActivationSuccess: () => void
  licenseInfo?: any
}

interface ActivationAttempt {
  count: number
  lastAttempt: Date
}

const ActivationOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
  z-index: 999999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.8s ease-out;
  direction: rtl;

  /* إضافة نمط متحرك للخلفية */
  background-size: 400% 400%;
  animation: gradientShift 4s ease infinite, fadeIn 0.8s ease-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
`

const ActivationContainer = styled(Card)`
  width: 500px;
  max-width: 90vw;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 1;
  animation: slideUp 1s cubic-bezier(0.16, 1, 0.3, 1);
  overflow: hidden;

  /* إضافة تأثير الضوء */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #FF6B6B, #4ECDC4, #45B7D1, #FF6B6B);
    background-size: 200% 100%;
    animation: shimmer 2s linear infinite;
  }

  .ant-card-body {
    padding: 48px;
    background: transparent;
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(80px) scale(0.8) rotateX(10deg);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1) rotateX(0deg);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`

const LogoContainer = styled.div`
  text-align: center;
  margin-bottom: 40px;

  .company-logo {
    width: 100px;
    height: 100px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36px;
    color: white;
    box-shadow:
      0 15px 35px rgba(255, 107, 107, 0.4),
      0 5px 15px rgba(0, 0, 0, 0.1);
    font-weight: 900;
    position: relative;
    animation: logoFloat 3s ease-in-out infinite;

    /* تأثير الهالة */
    &::before {
      content: '';
      position: absolute;
      top: -5px;
      left: -5px;
      right: -5px;
      bottom: -5px;
      background: linear-gradient(135deg, #FF6B6B, #4ECDC4, #45B7D1);
      border-radius: 50%;
      z-index: -1;
      opacity: 0.3;
      animation: pulse 2s ease-in-out infinite;
    }
  }

  @keyframes logoFloat {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes pulse {
    0%, 100% {
      transform: scale(1);
      opacity: 0.3;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.6;
    }
  }
`

const StyledForm = styled.div`
  .ant-form-item {
    margin-bottom: 20px;
  }

  .ant-input-affix-wrapper {
    height: 60px;
    border-radius: 16px;
    font-size: 18px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }

  .ant-btn {
    height: 60px;
    border-radius: 16px;
    font-weight: 700;
    font-size: 18px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover:not(:disabled) {
      transform: translateY(-3px);
      box-shadow: 0 12px 35px rgba(255, 107, 107, 0.5);
    }

    &:active:not(:disabled) {
      transform: translateY(-1px);
    }

    /* تأثير الموجة عند النقر */
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transition: width 0.6s, height 0.6s;
      transform: translate(-50%, -50%);
      z-index: 0;
    }

    &:active::before {
      width: 300px;
      height: 300px;
    }

    span {
      position: relative;
      z-index: 1;
    }
  }

  /* انيميشن النبض للزر */
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
    }
  }
`

const DeveloperInfo = styled.div`
  margin-top: 24px;
  padding: 16px;
  background: rgba(0, 120, 212, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 120, 212, 0.1);
  
  .developer-title {
    color: #0078D4;
    font-weight: 600;
    font-size: 12px;
    margin-bottom: 4px;
  }
  
  .developer-contact {
    color: #666;
    font-size: 11px;
    line-height: 1.4;
  }
`

const ErrorAlert = styled(Alert)`
  margin: 20px 0;
  border-radius: 8px;
  
  &.warning-level-1 {
    border-color: #faad14;
    background-color: #fffbe6;
  }
  
  &.warning-level-2 {
    border-color: #fa8c16;
    background-color: #fff2e8;
  }
  
  &.warning-level-3 {
    border-color: #f5222d;
    background-color: #fff1f0;
  }
`

const EnhancedActivationModal: React.FC<ActivationModalProps> = ({
  visible,
  onActivationSuccess,
  licenseInfo: _licenseInfo
}) => {
  const [activationCode, setActivationCode] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showCode, setShowCode] = useState(false)
  const [attempts, setAttempts] = useState<ActivationAttempt>({ count: 0, lastAttempt: new Date() })

  // تحميل محاولات التفعيل السابقة
  useEffect(() => {
    const savedAttempts = localStorage.getItem('activation_attempts')
    if (savedAttempts) {
      try {
        const parsed = JSON.parse(savedAttempts)
        setAttempts({
          count: parsed.count || 0,
          lastAttempt: new Date(parsed.lastAttempt || new Date())
        })
      } catch (error) {
        Logger.error('EnhancedActivationModal', 'خطأ في تحليل محاولات التفعيل:', error)
      }
    }
  }, [])

  const saveAttempts = (newAttempts: ActivationAttempt) => {
    localStorage.setItem('activation_attempts', JSON.stringify(newAttempts))
    setAttempts(newAttempts)
  }

  const getErrorMessage = (attemptCount: number) => {
    if (attemptCount >= 5) {
      return {
        message: 'تم تجاوز الحد الأقصى للمحاولات. يرجى التواصل مع الدعم الفني.',
        level: 3
      }
    } else if (attemptCount >= 3) {
      return {
        message: `محاولة فاشلة ${attemptCount}/5. تبقى ${5 - attemptCount} محاولات.`,
        level: 2
      }
    } else {
      return {
        message: `رقم التفعيل غير صحيح. المحاولة ${attemptCount}/5.`,
        level: 1
      }
    }
  }

  const handleActivation = async () => {
    if (!activationCode.trim()) {
      setError('يرجى إدخال رقم التفعيل')
      return
    }

    if (attempts.count >= 5) {
      setError('تم تجاوز الحد الأقصى للمحاولات')
      return
    }

    setLoading(true)
    setError('')

    try {
      // استدعاء API التفعيل
      const result = await window.electronAPI?.activateLicense(activationCode.trim())
      
      if (result?.success) {
        // نجح التفعيل - إعادة تعيين المحاولات
        localStorage.removeItem('activation_attempts')
        onActivationSuccess()
      } else {
        // فشل التفعيل - زيادة عدد المحاولات
        const newAttemptCount = attempts.count + 1
        const newAttempts: ActivationAttempt = {
          count: newAttemptCount,
          lastAttempt: new Date()
        }
        saveAttempts(newAttempts)

        const errorInfo = getErrorMessage(newAttemptCount)
        setError(errorInfo.message)
      }
    } catch (error) {
      Logger.error('EnhancedActivationModal', 'خطأ في التفعيل:', error)
      setError('حدث خطأ أثناء التفعيل. حاول مرة أخرى.')
    } finally {
      setLoading(false)
    }
  }

  if (!visible) return null

  const errorInfo = attempts.count > 0 ? getErrorMessage(attempts.count) : null

  return (
    <ActivationOverlay>
      <ActivationContainer>
        <LogoContainer>
          <div className="company-logo">ZET.IA</div>
          <Title level={2} style={{
            background: 'linear-gradient(135deg, #FF6B6B, #4ECDC4, #45B7D1)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            marginBottom: 8,
            fontSize: '32px',
            fontWeight: 'bold'
          }}>
            ZET.IA
          </Title>
          <Text style={{
            marginBottom: 8,
            fontSize: '16px',
            color: '#666',
            fontWeight: '500'
          }}>
            نظام إدارة شامل للمحاسبة والإنتاج
          </Text>
          <DeveloperInfo>
            <div className="developer-title">المطور: FARESNAWAF</div>
            <div className="developer-contact">
              <EMAIL> | 0569329925
            </div>
          </DeveloperInfo>
        </LogoContainer>

        <Title level={3} style={{
          background: 'linear-gradient(135deg, #FF6B6B, #4ECDC4)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          marginBottom: 25,
          fontSize: '28px',
          fontWeight: 'bold',
          textAlign: 'center'
        }}>
          <SafetyOutlined style={{ marginLeft: 10, color: '#FF6B6B' }} /> تفعيل البرنامج
        </Title>

        {attempts.count === 0 && (
          <Paragraph style={{
            textAlign: 'center',
            color: '#666',
            fontSize: 16,
            marginBottom: 30,
            lineHeight: 1.6
          }}>
            يرجى إدخال رقم التفعيل الخاص بك للمتابعة واستخدام البرنامج
          </Paragraph>
        )}

        <StyledForm>
          <div style={{ marginBottom: '24px' }}>
            <Input.Password
              size="large"
              placeholder="أدخل رقم التفعيل هنا"
              value={activationCode}
              onChange={(e) => {
                const value = e.target.value.toUpperCase().trim()
                setActivationCode(value)
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleActivation()
                }
              }}
              prefix={<KeyOutlined style={{ color: '#0078D4' }} />}
              maxLength={50}
              autoComplete="off"
              spellCheck={false}
              autoFocus
              visibilityToggle={{
                visible: showCode,
                onVisibleChange: setShowCode,
              }}
              style={{
                height: '60px',
                fontSize: '18px',
                textAlign: 'center',
                borderRadius: '16px',
                border: '2px solid #e1e5e9',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.3s ease'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = '#FF6B6B'
                e.target.style.boxShadow = '0 0 0 3px rgba(255, 107, 107, 0.1)'
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#e1e5e9'
                e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)'
              }}
            />
          </div>

          <Button
            type="primary"
            size="large"
            loading={loading}
            onClick={handleActivation}
            disabled={!activationCode.trim() || attempts.count >= 5}
            block
            style={{
              height: '60px',
              fontSize: '18px',
              fontWeight: 'bold',
              borderRadius: '16px',
              background: activationCode.trim() && attempts.count < 5
                ? 'linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 50%, #45B7D1 100%)'
                : '#f5f5f5',
              border: 'none',
              boxShadow: activationCode.trim() && attempts.count < 5
                ? '0 8px 25px rgba(255, 107, 107, 0.4)'
                : 'none',
              transition: 'all 0.3s ease',
              transform: loading ? 'scale(0.98)' : 'scale(1)'
            }}
          >
            {loading ? (
              <Space>
                <span>جاري التفعيل</span>
                <span style={{
                  animation: 'pulse 1.5s infinite',
                  display: 'inline-block',
                  fontSize: '20px'
                }}>⚡</span>
              </Space>
            ) : (
              <Space>
                <SafetyOutlined style={{ fontSize: '20px' }} />
                <span>تفعيل البرنامج</span>
              </Space>
            )}
          </Button>
        </StyledForm>

        {error && (
          <ErrorAlert
            message={error}
            type={errorInfo?.level === 3 ? 'error' : errorInfo?.level === 2 ? 'warning' : 'info'}
            showIcon
            className={`warning-level-${errorInfo?.level || 1}`}
          />
        )}

        {attempts.count >= 3 && (
          <div style={{
            marginTop: '20px',
            padding: '16px',
            background: '#fff7e6',
            border: '1px solid #ffd591',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <Text style={{ color: '#d46b08', fontSize: '14px' }}>
              <PhoneOutlined style={{ marginLeft: '8px' }} />
              للحصول على المساعدة: 0569329925
            </Text>
            <br />
            <Text style={{ color: '#d46b08', fontSize: '14px' }}>
              <MailOutlined style={{ marginLeft: '8px' }} />
              <EMAIL>
            </Text>
          </div>
        )}
      </ActivationContainer>
    </ActivationOverlay>
  )
}

export default EnhancedActivationModal
