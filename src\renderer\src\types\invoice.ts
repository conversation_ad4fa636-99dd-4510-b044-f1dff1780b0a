// واجهات موحدة لنظام الفواتير

// حالات الفواتير الموحدة
export type InvoiceStatus = 'draft' | 'pending' | 'sent' | 'paid' | 'partial' | 'overdue' | 'cancelled'

// أنواع الفواتير
export type InvoiceType = 'sales' | 'purchase' | 'paint' | 'service'

// أنواع الكيانات
export type EntityType = 'customer' | 'supplier'

// طرق الدفع
export type PaymentMethod = 'cash' | 'bank_transfer' | 'check' | 'credit_card'

// واجهة المبالغ الموحدة
export interface InvoiceAmounts {
  subtotal: number
  discount_amount: number
  tax_amount: number
  total_amount: number
  paid_amount: number
  remaining_amount: number
}

// واجهة صنف الفاتورة الموحدة
export interface InvoiceItem {
  id?: number
  item_id: number
  item_name: string
  item_code?: string
  warehouse_id: number
  warehouse_name?: string
  quantity: number
  unit_price: number
  total_price: number
  discount?: number
  tax?: number
  notes?: string
}

// واجهة الفاتورة الأساسية الموحدة
export interface BaseInvoice {
  id: number
  invoice_number: string
  invoice_type: InvoiceType
  entity_id: number
  entity_name: string
  entity_type: EntityType
  entity_code?: string
  entity_address?: string
  entity_phone?: string
  entity_email?: string
  order_id?: number
  order_number?: string
  invoice_date: string
  due_date?: string
  status: InvoiceStatus
  amounts: InvoiceAmounts
  items: InvoiceItem[]
  payment_method?: PaymentMethod
  notes?: string
  terms?: string
  created_by: number
  created_by_name?: string
  created_at: string
  updated_at?: string
  metadata?: Record<string, any> // بيانات إضافية حسب نوع الفاتورة
}

// فاتورة المبيعات
export interface SalesInvoice extends BaseInvoice {
  invoice_type: 'sales'
  entity_type: 'customer'
  customer_id: number
  customer_name: string
  customer_address?: string
  customer_phone?: string
  customer_email?: string
}

// فاتورة المشتريات
export interface PurchaseInvoice extends BaseInvoice {
  invoice_type: 'purchase'
  entity_type: 'supplier'
  supplier_id: number
  supplier_name: string
  supplier_address?: string
  supplier_phone?: string
  supplier_email?: string
}

// فاتورة الدهان
export interface PaintInvoice extends BaseInvoice {
  invoice_type: 'paint'
  entity_type: 'customer'
  customer_id: number
  customer_name: string
  total_area?: number // المساحة الإجمالية
  paint_type?: string
  color_code?: string
  surface_type?: string
}

// فاتورة الخدمات
export interface ServiceInvoice extends BaseInvoice {
  invoice_type: 'service'
  service_type: 'consultation' | 'maintenance' | 'installation' | 'repair'
  service_duration?: number // بالساعات
  hourly_rate?: number
  service_description?: string
  technician_id?: number
  technician_name?: string
  completion_date?: string
  location?: string
}

// بيانات إنشاء فاتورة
export interface CreateInvoiceData {
  invoice_type: InvoiceType
  entity_id: number
  order_id?: number
  invoice_date: string
  due_date?: string
  discount_amount?: number
  tax_amount?: number
  payment_method?: PaymentMethod
  notes?: string
  terms?: string
  items: Omit<InvoiceItem, 'id' | 'item_name' | 'warehouse_name' | 'total_price'>[]
  metadata?: Record<string, any>
}

// بيانات تحديث فاتورة
export interface UpdateInvoiceData extends Partial<CreateInvoiceData> {
  status?: InvoiceStatus
  paid_amount?: number
}

// فلاتر البحث
export interface InvoiceFilters {
  invoice_type?: InvoiceType
  entity_id?: number
  entity_type?: EntityType
  status?: InvoiceStatus[]
  date_from?: string
  date_to?: string
  amount_from?: number
  amount_to?: number
  search_text?: string
  order_by?: 'invoice_date' | 'total_amount' | 'created_at'
  order_direction?: 'ASC' | 'DESC'
  page?: number
  page_size?: number
}

// إحصائيات الفواتير
export interface InvoiceStatistics {
  total_invoices: number
  total_amount: number
  paid_amount: number
  remaining_amount: number
  by_status: Record<InvoiceStatus, {
    count: number
    amount: number
  }>
  by_type: Record<InvoiceType, {
    count: number
    amount: number
  }>
  monthly_trends: Array<{
    month: string
    count: number
    amount: number
  }>
  top_entities: Array<{
    entity_id: number
    entity_name: string
    entity_type: EntityType
    total_amount: number
    invoice_count: number
  }>
}

// نتيجة البحث مع الصفحات
export interface InvoiceSearchResult {
  invoices: BaseInvoice[]
  total_count: number
  page: number
  page_size: number
  total_pages: number
  statistics?: InvoiceStatistics
}

// حالات الدفع
export interface PaymentStatus {
  status: 'unpaid' | 'partial' | 'paid' | 'overpaid'
  percentage: number
  amount_paid: number
  amount_remaining: number
  last_payment_date?: string
}

// معلومات الدفعة
export interface PaymentInfo {
  id: number
  payment_number: string
  invoice_id: number
  invoice_number: string
  amount: number
  payment_method: PaymentMethod
  payment_date: string
  reference_number?: string
  notes?: string
  created_by: number
  created_at: string
}

// تحليلات الفواتير
export interface InvoiceAnalytics {
  revenue_trends: Array<{
    period: string
    sales_amount: number
    purchase_amount: number
    profit_margin: number
  }>
  payment_patterns: Array<{
    payment_method: PaymentMethod
    count: number
    amount: number
    percentage: number
  }>
  overdue_analysis: {
    total_overdue: number
    overdue_amount: number
    by_age: Array<{
      age_range: string
      count: number
      amount: number
    }>
  }
  profitability_analysis: {
    gross_profit: number
    profit_margin: number
    top_profitable_items: Array<{
      item_id: number
      item_name: string
      profit_amount: number
      profit_margin: number
    }>
  }
}

// خيارات التصدير
export interface ExportOptions {
  format: 'excel' | 'pdf' | 'csv'
  include_items: boolean
  include_payments: boolean
  date_range?: {
    from: string
    to: string
  }
  filters?: InvoiceFilters
}

// نتيجة العمليات
export interface InvoiceOperationResult {
  success: boolean
  message: string
  data?: any
  errors?: string[]
  warnings?: string[]
}

// إعدادات الفواتير
export interface InvoiceSettings {
  auto_generate_number: boolean
  number_prefix: Record<InvoiceType, string>
  default_terms: string
  default_due_days: number
  auto_calculate_tax: boolean
  default_tax_rate: number
  require_approval: boolean
  allow_negative_inventory: boolean
  auto_send_email: boolean
  email_template: string
}

// قالب فاتورة متكررة
export interface RecurringInvoiceTemplate {
  id: number
  template_name: string
  invoice_type: InvoiceType
  entity_id: number
  entity_name: string
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  interval: number // كل كم من الفترة
  start_date: string
  end_date?: string
  next_generation_date: string
  last_generated_date?: string
  is_active: boolean
  auto_send: boolean
  template_data: CreateInvoiceData
  created_by: number
  created_at: string
}

// سجل الفواتير المتكررة
export interface RecurringInvoiceLog {
  id: number
  template_id: number
  generated_invoice_id: number
  generation_date: string
  status: 'success' | 'failed'
  error_message?: string
}
