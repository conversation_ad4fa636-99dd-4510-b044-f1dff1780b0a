import React, { useState } from 'react'
import { Tabs, Button, Typography } from 'antd'
// import type { TabsProps } from 'antd'
import {
  ArrowLeftOutlined,
  FileTextOutlined,
  ExperimentOutlined,
  InboxOutlined,
  ClockCircleOutlined,
  Bar<PERSON><PERSON>Outlined,
  B<PERSON>Outlined,
  DollarOutlined,
  StarOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../../utils/logger'
import ProductionOrdersManagement from './ProductionOrdersManagement'
import ProductionRecipesManagement from './ProductionRecipesManagement'
import MaterialsManagement from './MaterialsManagement'
import ProductionStagesManagement from './ProductionStagesManagement'
import ProductionFinanceManagement from './ProductionFinanceManagement'
import ProductionReports from './ProductionReports'
import ProductionDepartmentsManagement from './ProductionDepartmentsManagement'
import FurnitureEnhancedFeatures from './FurnitureEnhancedFeatures'

const { Title } = Typography

interface FurnitureProductionProps {
  onBack?: () => void
}

const FurnitureProduction: React.FC<FurnitureProductionProps> = ({ onBack }) => {
  const [activeKey, setActiveKey] = useState('1')

  // مكون آمن لعرض المحتوى مع معالجة الأخطاء
  const SafeComponent: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    try {
      return <>{children}</>
    } catch (error) {
      Logger.error('FurnitureProduction', 'خطأ في تحميل المكون:', error)
      return <div style={{ padding: '20px', textAlign: 'center' }}>حدث خطأ في تحميل هذا القسم</div>
    }
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* شريط العنوان والعودة */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {onBack && (
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={onBack}
            >
              العودة
            </Button>
          )}
          <Title level={2} style={{ margin: 0 }}>
            إنتاج الأثاث
          </Title>
        </div>
      </div>

      {/* التبويبات */}
      <Tabs
        activeKey={activeKey}
        onChange={setActiveKey}
        type="card"
        items={[
          {
            key: '1',
            label: (
              <span>
                <FileTextOutlined />
                أوامر الإنتاج
              </span>
            ),
            children: (
              <SafeComponent>
                <ProductionOrdersManagement />
              </SafeComponent>
            )
          },
          {
            key: '2',
            label: (
              <span>
                <ExperimentOutlined />
                وصفات الإنتاج
              </span>
            ),
            children: (
              <SafeComponent>
                <ProductionRecipesManagement />
              </SafeComponent>
            )
          },
          {
            key: '3',
            label: (
              <span>
                <InboxOutlined />
                إدارة المواد
              </span>
            ),
            children: (
              <SafeComponent>
                <MaterialsManagement />
              </SafeComponent>
            )
          },
          {
            key: '4',
            label: (
              <span>
                <ClockCircleOutlined />
                مراحل الإنتاج
              </span>
            ),
            children: (
              <SafeComponent>
                <ProductionStagesManagement />
              </SafeComponent>
            )
          },
          {
            key: '5',
            label: (
              <span>
                <BuildOutlined />
                أقسام الإنتاج
              </span>
            ),
            children: (
              <SafeComponent>
                <ProductionDepartmentsManagement />
              </SafeComponent>
            )
          },
          {
            key: '6',
            label: (
              <span>
                <DollarOutlined />
                المالية
              </span>
            ),
            children: (
              <SafeComponent>
                <ProductionFinanceManagement onBack={() => setActiveKey('1')} />
              </SafeComponent>
            )
          },
          {
            key: '7',
            label: (
              <span>
                <BarChartOutlined />
                التقارير
              </span>
            ),
            children: (
              <SafeComponent>
                <ProductionReports />
              </SafeComponent>
            )
          },
          {
            key: '8',
            label: (
              <span>
                <StarOutlined />
                المميزات المحسنة
              </span>
            ),
            children: (
              <SafeComponent>
                <FurnitureEnhancedFeatures onBack={() => setActiveKey('1')} />
              </SafeComponent>
            )
          }
        ]}
      />
    </div>
  )
}

export default FurnitureProduction
