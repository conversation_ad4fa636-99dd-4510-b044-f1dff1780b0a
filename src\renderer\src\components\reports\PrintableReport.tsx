import React, { forwardRef, ReactNode } from 'react'
import { Card, Row, Col, Divider, Typography } from 'antd'
import dayjs from 'dayjs'

const { Title, Text } = Typography

interface PrintableReportProps {
  title: string
  subtitle?: string
  children: ReactNode
  showHeader?: boolean
  showFooter?: boolean
  reportDate?: string
  generatedBy?: string
  className?: string
}

const PrintableReport = forwardRef<HTMLDivElement, PrintableReportProps>(
  ({ 
    title, 
    subtitle, 
    children, 
    showHeader = true, 
    showFooter = true, 
    reportDate, 
    generatedBy = 'نظام المحاسبة والإنتاج',
    className = ''
  }, ref) => {
    const currentDate = dayjs().format('YYYY-MM-DD HH:mm:ss')
    const displayDate = reportDate || currentDate

    return (
      <div 
        ref={ref}
        className={`printable-report ${className}`}
        style={{
          backgroundColor: '#fff',
          padding: '20px',
          fontFamily: 'Arial, sans-serif',
          fontSize: '12px',
          lineHeight: '1.4',
          color: '#000'
        }}
      >
        {/* Print Styles */}
        <style dangerouslySetInnerHTML={{
          __html: `
            @media print {
              .printable-report {
                padding: 0 !important;
                margin: 0 !important;
                box-shadow: none !important;
                border: none !important;
              }

              .no-print {
                display: none !important;
              }

              .print-break {
                page-break-before: always;
              }

              .print-avoid-break {
                page-break-inside: avoid;
              }

              table {
                border-collapse: collapse !important;
              }

              table, th, td {
                border: 1px solid #000 !important;
              }

              th {
                background-color: #f5f5f5 !important;
                font-weight: bold !important;
              }

              .ant-card {
                border: none !important;
                box-shadow: none !important;
              }

              .ant-card-body {
                padding: 0 !important;
              }

              .ant-table-thead > tr > th {
                background-color: #f5f5f5 !important;
                border: 1px solid #000 !important;
              }

              .ant-table-tbody > tr > td {
                border: 1px solid #000 !important;
              }

              .ant-statistic-content {
                font-size: 14px !important;
              }
            }

            @page {
              margin: 1cm;
              size: A4;
            }
          `
        }} />

        {/* Header */}
        {showHeader && (
          <div className="print-avoid-break" style={{ marginBottom: '30px' }}>
            <Row align="middle" style={{ marginBottom: '20px' }}>
              <Col span={4}>
                <div 
                  style={{
                    width: '80px',
                    height: '80px',
                    backgroundColor: '#1890ff',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#fff',
                    fontSize: '24px',
                    fontWeight: 'bold'
                  }}
                >
                  ش.م
                </div>
              </Col>
              <Col span={16}>
                <div style={{ textAlign: 'center' }}>
                  <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
                    شركة المحاسبة والإنتاج
                  </Title>
                  <Text style={{ fontSize: '16px', color: '#666' }}>
                    نّام إدارة الأعمال المتكامل
                  </Text>
                </div>
              </Col>
              <Col span={4}>
                <div style={{ textAlign: 'left', fontSize: '10px', color: '#666' }}>
                  <div>تاريخ الطباعة:</div>
                  <div>{currentDate}</div>
                </div>
              </Col>
            </Row>
            
            <Divider style={{ margin: '20px 0', borderColor: '#1890ff' }} />
            
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
              <Title level={3} style={{ margin: 0, color: '#333' }}>
                {title}
              </Title>
              {subtitle && (
                <Text style={{ fontSize: '14px', color: '#666', display: 'block', marginTop: '8px' }}>
                  {subtitle}
                </Text>
              )}
              <Text style={{ fontSize: '12px', color: '#999', display: 'block', marginTop: '8px' }}>
                تاريخ التقرير: {displayDate}
              </Text>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="print-content">
          {children}
        </div>

        {/* Footer */}
        {showFooter && (
          <div className="print-avoid-break" style={{ marginTop: '40px', borderTop: '1px solid #ddd', paddingTop: '20px' }}>
            <Row justify="space-between" align="middle">
              <Col>
                <Text style={{ fontSize: '10px', color: '#666' }}>
                  تم إنشاء هذا التقرير بواسطة: {generatedBy}
                </Text>
              </Col>
              <Col>
                <Text style={{ fontSize: '10px', color: '#666' }}>
                  تاريخ الإنشاء: {currentDate}
                </Text>
              </Col>
            </Row>
            <div style={{ textAlign: 'center', marginTop: '10px' }}>
              <Text style={{ fontSize: '10px', color: '#999' }}>
                هذا التقرير سري ومخصص للاستخدام الداخلي فقط
              </Text>
            </div>
          </div>
        )}
      </div>
    )
  }
)

PrintableReport.displayName = 'PrintableReport'

export default PrintableReport
