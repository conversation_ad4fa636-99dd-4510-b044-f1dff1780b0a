import { ipcMain } from 'electron'
import { ActivationManager } from '../activation/activationManager'

/**
 * تسجيل معالجات IPC للتفعيل
 */
export function registerActivationHandlers(): void {
  const activationManager = ActivationManager.getInstance()

  /**
   * تفعيل البرنامج برقم التفعيل
   */
  ipcMain.handle('activate-license', async (event, activationCode: string) => {
    try {
      console.log('طلب تفعيل البرنامج:', activationCode)
      const result = await activationManager.activateWithCode(activationCode)
      console.log('نتيجة التفعيل:', result)
      return result
    } catch (error) {
      console.error('خطأ في معالج التفعيل:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء التفعيل'
      }
    }
  })

  /**
   * فحص حالة التفعيل
   */
  ipcMain.handle('check-activation-status', async (_event) => {
    try {
      const status = await activationManager.checkActivationStatus()
      console.log('حالة التفعيل:', status)
      return status
    } catch (error) {
      console.error('خطأ في فحص حالة التفعيل:', error)
      return { isActivated: false }
    }
  })

  /**
   * الحصول على معلومات الترخيص المفصلة
   */
  ipcMain.handle('get-license-info', async (_event) => {
    try {
      const info = await activationManager.getDetailedLicenseInfo()
      console.log('معلومات الترخيص:', info)
      return info
    } catch (error) {
      console.error('خطأ في الحصول على معلومات الترخيص:', error)
      return { isActivated: false }
    }
  })

  /**
   * إلغاء تفعيل البرنامج
   */
  ipcMain.handle('deactivate-license', async (_event) => {
    try {
      const result = await activationManager.deactivateLicense()
      console.log('نتيجة إلغاء التفعيل:', result)
      return { success: result }
    } catch (error) {
      console.error('خطأ في إلغاء التفعيل:', error)
      return { success: false }
    }
  })

  /**
   * الحصول على معرف الجهاز الحالي
   */
  ipcMain.handle('get-hardware-id', async (_event) => {
    try {
      const hardwareId = await activationManager.getCurrentHardwareId()
      console.log('معرف الجهاز:', hardwareId)
      return { success: true, hardwareId }
    } catch (error) {
      console.error('خطأ في الحصول على معرف الجهاز:', error)
      return { success: false, hardwareId: null }
    }
  })

  /**
   * التحقق من صحة رقم التفعيل دون تفعيل
   */
  ipcMain.handle('validate-activation-code', async (_event, activationCode: string) => {
    try {
      const licenseInfo = activationManager.validateActivationCode(activationCode)
      return {
        success: true,
        isValid: licenseInfo !== null,
        licenseInfo
      }
    } catch (error) {
      console.error('خطأ في التحقق من رقم التفعيل:', error)
      return {
        success: false,
        isValid: false,
        licenseInfo: null
      }
    }
  })

  /**
   * بدء الفحص الدوري للتفعيل
   */
  ipcMain.handle('start-activation-check', async (_event) => {
    try {
      activationManager.startPeriodicCheck()
      return { success: true }
    } catch (error) {
      console.error('خطأ في بدء الفحص الدوري:', error)
      return { success: false }
    }
  })

  /**
   * إيقاف الفحص الدوري للتفعيل
   */
  ipcMain.handle('stop-activation-check', async (_event) => {
    try {
      activationManager.stopPeriodicCheck()
      return { success: true }
    } catch (error) {
      console.error('خطأ في إيقاف الفحص الدوري:', error)
      return { success: false }
    }
  })

  console.log('تم تسجيل معالجات IPC للتفعيل')
}
