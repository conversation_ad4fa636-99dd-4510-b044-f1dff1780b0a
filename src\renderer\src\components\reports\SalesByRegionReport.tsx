import React from 'react';
import { Tag, Typography, Progress } from 'antd';
import { EnvironmentOutlined } from '@ant-design/icons';
import UniversalReport from './UniversalReport';
import { SafeLogger as Logger } from '../../utils/logger';
import type { ReportData, ReportType } from '../../types/reports';
import { createDefaultFilters } from '../../types/reports';

const { Text } = Typography;

const SalesByRegionReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      Logger.info('SalesByRegionReport', '🌍 بدء إنشاء تقرير المبيعات حسب المنطقة...');
      Logger.info('SalesByRegionReport', '🔍 الفلاتر المطبقة:', filters);

      let salesData: any[] = [];

      if (!window.electronAPI || !window.electronAPI.getSalesByRegion) {
        Logger.info('SalesByRegionReport', '⚠️ التطبيق يعمل في وضع المتصفح أو الدالة غير متوفرة - استخدام بيانات وهمية');

        // بيانات وهمية للمبيعات حسب المنطقة
        salesData = [
          {
            region_id: 1,
            region_name: 'القاهرة الكبرى',
            total_sales: 125000.00,
            total_orders: 45,
            avg_order_value: 2777.78,
            total_customers: 25,
            growth_rate: 15.5,
            market_share: 35.2
          },
          {
            region_id: 2,
            region_name: 'الإسكندرية',
            total_sales: 98000.00,
            total_orders: 38,
            avg_order_value: 2578.95,
            total_customers: 20,
            growth_rate: 12.3,
            market_share: 27.6
          },
          {
            region_id: 3,
            region_name: 'الدلتا',
            total_sales: 67500.00,
            total_orders: 32,
            avg_order_value: 2109.38,
            total_customers: 18,
            growth_rate: 8.7,
            market_share: 19.0
          },
          {
            region_id: 4,
            region_name: 'الصعيد',
            total_sales: 45000.00,
            total_orders: 28,
            avg_order_value: 1607.14,
            total_customers: 15,
            growth_rate: 5.2,
            market_share: 12.7
          },
          {
            region_id: 5,
            region_name: 'سيناء والبحر الأحمر',
            total_sales: 20000.00,
            total_orders: 15,
            avg_order_value: 1333.33,
            total_customers: 8,
            growth_rate: 3.8,
            market_share: 5.6
          }
        ];
      } else {
        // استعلام البيانات من قاعدة البيانات
        const response = await window.electronAPI.getSalesByRegion({
          dateRange: filters.dateRange,
          regionId: filters.regionId
        });

        if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
          throw new Error('لا توجد بيانات متاحة');
        }

        salesData = response.data;
      }

      // تعريف الأعمدة
      const columns = [
        {
          title: 'المنطقة',
          key: 'region_name',
          format: 'text' as const,
          render: (record: any) => (
            <div>
              <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
                <EnvironmentOutlined style={{ marginLeft: '8px' }} />
                {record.region_name}
              </div>
            </div>
          ),
          width: 200
        },
        {
          title: 'إجمالي المبيعات',
          key: 'total_sales',
          format: 'currency' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <strong style={{ color: '#1890ff', fontSize: '16px' }}>
                {record.total_sales.toLocaleString('ar-EG', { minimumFractionDigits: 2 })} ج.م
              </strong>
            </div>
          ),
          width: 150,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'عدد الطلبات',
          key: 'total_orders',
          format: 'number' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <strong style={{ color: '#52c41a', fontSize: '16px' }}>
                {record.total_orders}
              </strong>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'متوسط قيمة الطلب',
          key: 'avg_order_value',
          format: 'currency' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <strong style={{ color: '#fa8c16', fontSize: '16px' }}>
                {record.avg_order_value.toLocaleString('ar-EG', { minimumFractionDigits: 2 })} ج.م
              </strong>
            </div>
          ),
          width: 150,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'عدد العملاء',
          key: 'total_customers',
          format: 'number' as const,
          render: (record: any) => (
            <div style={{ textAlign: 'center' }}>
              <strong style={{ color: '#722ed1', fontSize: '16px' }}>
                {record.total_customers}
              </strong>
            </div>
          ),
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'معدل النمو',
          key: 'growth_rate',
          format: 'percentage' as const,
          render: (record: any) => {
            const growth = record.growth_rate;
            const color = growth >= 10 ? '#52c41a' : growth >= 5 ? '#1890ff' : '#fa8c16';
            return (
              <div style={{ textAlign: 'center' }}>
                <strong style={{ color, fontSize: '16px' }}>{growth.toFixed(1)}%</strong>
              </div>
            );
          },
          width: 120,
          align: 'center' as const,
          sortable: true
        },
        {
          title: 'حصة السوق',
          key: 'market_share',
          format: 'percentage' as const,
          render: (record: any) => {
            const share = record.market_share;
            return (
              <div style={{ textAlign: 'center' }}>
                <strong style={{ color: '#13c2c2', fontSize: '16px' }}>{share.toFixed(1)}%</strong>
                <Progress 
                  percent={share} 
                  size="small" 
                  strokeColor="#13c2c2"
                  showInfo={false}
                  style={{ width: '80px', margin: '4px auto' }}
                />
              </div>
            );
          },
          width: 120,
          align: 'center' as const,
          sortable: true
        }
      ];

      // حساب الإحصائيات
      const totalRegions = salesData.length;
      const totalSales = salesData.reduce((sum, region) => sum + (region.total_sales || 0), 0);
      const totalOrders = salesData.reduce((sum, region) => sum + (region.total_orders || 0), 0);
      const totalCustomers = salesData.reduce((sum, region) => sum + (region.total_customers || 0), 0);
      const avgOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;
      const avgGrowthRate = totalRegions > 0 ? 
        salesData.reduce((sum, region) => sum + (region.growth_rate || 0), 0) / totalRegions : 0;

      Logger.info('SalesByRegionReport', `✅ تم إنشاء تقرير المبيعات حسب المنطقة بنجاح: ${totalRegions} منطقة`);

      return {
        title: 'تقرير المبيعات حسب المنطقة',
        data: salesData,
        columns,
        summary: {
          totalRegions,
          totalSales: Math.round(totalSales * 100) / 100,
          totalOrders,
          totalCustomers,
          avgOrderValue: Math.round(avgOrderValue * 100) / 100,
          avgGrowthRate: Math.round(avgGrowthRate * 10) / 10,
          topRegion: salesData.length > 0 ? salesData[0] : null
        },
        metadata: {
          reportType: 'sales_by_region' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: salesData.length
        }
      };
    } catch (error) {
      Logger.error('SalesByRegionReport', 'خطأ في إنشاء تقرير المبيعات حسب المنطقة:', error);
      throw new Error('فشل في إنشاء تقرير المبيعات حسب المنطقة');
    }
  };

  return (
    <UniversalReport
      reportType={'sales_by_region' as ReportType}
      title="تقرير المبيعات حسب المنطقة"
      description="تقرير شامل للمبيعات موزعة حسب المناطق الجغرافية مع تحليل الأداء"
      onGenerateReport={generateReport}
      defaultFilters={createDefaultFilters('sales_by_region')}
      showDateRange={true}
      showAdvancedSearch={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default SalesByRegionReport;
