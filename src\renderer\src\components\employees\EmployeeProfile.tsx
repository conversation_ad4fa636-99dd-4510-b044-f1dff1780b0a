﻿import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Avatar,
  Descriptions,
  Tabs,
  Table,
  Tag,
  Statistic,
  Progress, Button,
  Space,
  message} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
// import type { TabsProps } from 'antd'
import {
  UserOutlined,
  DollarOutlined,
  Clock<PERSON>ircleOutlined,
  CalendarOutlined,
  Bar<PERSON><PERSON>Outlined,
  ToolOutlined, TrophyOutlined,
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { Title, Text } = Typography

interface Employee {
  id: number
  employee_code: string
  full_name: string
  email?: string
  phone?: string
  address?: string
  department_name?: string
  position?: string
  job_title?: string
  hire_date: string
  basic_salary: number
  status: string
  photo_path?: string
}

interface SalesPerformance {
  total_sales: number
  total_amount: number
  avg_sale_amount: number
  sale_date: string
}

interface ProductionAssignment {
  id: number
  product_name: string
  stage_name: string
  status: string
  created_at: string
}

interface FinancialTransaction {
  transaction_type: string
  transaction_id: number
  amount: number
  description: string
  transaction_date: string
  status: string
}

interface AttendanceRecord {
  date: string
  check_in_time?: string
  check_out_time?: string
  total_hours: number
  status: string
}

interface EmployeeProfileProps {
  employeeId: number
  onClose: () => void
}

const EmployeeProfile: React.FC<EmployeeProfileProps> = ({ employeeId, onClose }) => {
  const [employee, setEmployee] = useState<Employee | null>(null)
  const [salesPerformance, setSalesPerformance] = useState<SalesPerformance[]>([])
  const [productionAssignments, setProductionAssignments] = useState<ProductionAssignment[]>([])
  const [financialTransactions, setFinancialTransactions] = useState<FinancialTransaction[]>([])
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (employeeId) {
      fetchEmployeeData()
    }
  }, [employeeId])

  const fetchEmployeeData = async () => {
    setLoading(true)
    try {
      Logger.info('EmployeeProfile', 'ًں”„ جارٍ جلب بيانات الموظف...')

      if (!window.electronAPI) {
        Logger.error('EmployeeProfile', '❌ window.electronAPI غير متوفر')
        Logger.info('EmployeeProfile', 'âڑ ï¸ڈ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للموظف
        const mockEmployee = {
          id: employeeId,
          employee_code: 'EMP001',
          full_name: 'أحمد محمد علٍ',
          department_name: 'قسم الموارد البشرية',
          basic_salary: 6000,
          is_active: 1
        }

        setEmployee(mockEmployee as any)
        Logger.info('EmployeeProfile', '✅ تم تحميل بيانات موظف وهمية للمتصفح')
        return
      }

      // جلب بيانات الموظف الأساسية
      const employeesResult = await window.electronAPI.getEmployees()
      Logger.info('EmployeeProfile', 'ًں“ٹ ْتٍجة جلب الموظفيْ:', employeesResult)

      if (employeesResult && employeesResult.success) {
        const emp = employeesResult.data.find((e: Employee) => e.id === employeeId)
        setEmployee(emp)
        Logger.info('EmployeeProfile', '✅ تم جلب بيانات الموظف بنجاح')
      } else {
        Logger.error('EmployeeProfile', '❌ فشل في جلب بيانات الموظف:', employeesResult?.message)
      }

      // جلب أداط، المبيعات
      const salesResult = await window.electronAPI.getEmployeeSalesPerformance(employeeId, {
        date_from: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
        date_to: dayjs().format('YYYY-MM-DD')
      })
      if (salesResult.success) {
        setSalesPerformance(salesResult.data)
      }

      // جلب مهام الإنتاج
      const productionResult = await window.electronAPI.getEmployeeProductionAssignments(employeeId)
      if (productionResult.success) {
        setProductionAssignments(productionResult.data)
      }

      // جلب المعاملات المالية
      const financialResult = await window.electronAPI.getEmployeeFinancialTransactions(employeeId)
      if (financialResult.success) {
        setFinancialTransactions(financialResult.data)
      }

      // جلب سجلات الحضور
      const attendanceResult = await window.electronAPI.getEmployeeAttendance({
        employee_id: employeeId,
        date_from: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
        date_to: dayjs().format('YYYY-MM-DD')
      })
      if (attendanceResult.success) {
        setAttendanceRecords(attendanceResult.data)
      }

    } catch (_error) {
      message.error('فشل في جلب بيانات الموظف')
    } finally {
      setLoading(false)
    }
  }

  const salesColumns: ColumnsType<SalesPerformance> = [
    {
      title: 'التارٍخ',
      dataIndex: 'sale_date',
      key: 'sale_date',
      render: (date) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'عدد المبيعات',
      dataIndex: 'total_sales',
      key: 'total_sales'
    },
    {
      title: 'إجمالي المبلغ',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (amount) => `${amount?.toLocaleString() || 0} ₪`
    },
    {
      title: 'متوسط البٍع',
      dataIndex: 'avg_sale_amount',
      key: 'avg_sale_amount',
      render: (amount) => `${amount?.toLocaleString() || 0} ₪`
    }
  ]

  const productionColumns: ColumnsType<ProductionAssignment> = [
    {
      title: 'المنتج',
      dataIndex: 'product_name',
      key: 'product_name'
    },
    {
      title: 'المرحلة',
      dataIndex: 'stage_name',
      key: 'stage_name'
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'completed' ? 'green' : status === 'in_progress' ? 'blue' : 'orange'}>
          {status === 'completed' ? 'مكتمل' : status === 'in_progress' ? 'قٍد التْفيذ' : 'معلق'}
        </Tag>
      )
    },
    {
      title: 'تارٍخ التعٍٍْ',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => dayjs(date).format('YYYY-MM-DD')
    }
  ]

  const financialColumns: ColumnsType<FinancialTransaction> = [
    {
      title: 'النوع',
      dataIndex: 'transaction_type',
      key: 'transaction_type',
      render: (type) => (
        <Tag color="blue">
          {type === 'payroll' ? 'راتب' : type}
        </Tag>
      )
    },
    {
      title: 'الوصف',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          {amount?.toLocaleString()} ₪
        </span>
      )
    },
    {
      title: 'التارٍخ',
      dataIndex: 'transaction_date',
      key: 'transaction_date',
      render: (date) => date ? dayjs(date).format('YYYY-MM-DD') : '-'
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'paid' ? 'green' : 'orange'}>
          {status === 'paid' ? 'مدفوع' : 'معلق'}
        </Tag>
      )
    }
  ]

  const attendanceColumns: ColumnsType<AttendanceRecord> = [
    {
      title: 'التارٍخ',
      dataIndex: 'date',
      key: 'date',
      render: (date) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'الحضور',
      dataIndex: 'check_in_time',
      key: 'check_in_time',
      render: (time) => time || '-'
    },
    {
      title: 'الاْصراف',
      dataIndex: 'check_out_time',
      key: 'check_out_time',
      render: (time) => time || '-'
    },
    {
      title: 'إجمالي الساعات',
      dataIndex: 'total_hours',
      key: 'total_hours',
      render: (hours) => `${hours?.toFixed(2) || 0} ساعة`
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'present' ? 'green' : status === 'absent' ? 'red' : 'orange'}>
          {status === 'present' ? 'حاضر' : status === 'absent' ? 'غائب' : 'متأخر'}
        </Tag>
      )
    }
  ]

  if (!employee) {
    return <div>جارٍ التحميل...</div>
  }

  const totalSales = salesPerformance.reduce((sum, sale) => sum + sale.total_sales, 0)
  const totalSalesAmount = salesPerformance.reduce((sum, sale) => sum + sale.total_amount, 0)
  const completedProductions = productionAssignments.filter(p => p.status === 'completed').length
  const presentDays = attendanceRecords.filter(a => a.status === 'present').length
  const attendanceRate = attendanceRecords.length > 0 ? (presentDays / attendanceRecords.length) * 100 : 0

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[24, 24]}>
        {/* معلومات الموظف الأساسية */}
        <Col span={24}>
          <Card>
            <Row gutter={24}>
              <Col span={6}>
                <div style={{ textAlign: 'center' }}>
                  <Avatar
                    size={120}
                    icon={<UserOutlined />}
                    src={employee.photo_path || undefined}
                    onError={() => {
                      Logger.warn('EmployeeProfile', 'فشل في تحميل صورة الموظف:', employee.photo_path)
                      return false // استخدام الأيقونة الافتراضية
                    }}
                  />
                  <Title level={4} style={{ marginTop: '16px' }}>
                    {employee.full_name}
                  </Title>
                  <Text type="secondary">{employee.employee_code}</Text>
                </div>
              </Col>
              <Col span={18}>
                <Descriptions title="المعلومات الشخصٍة" column={2}>
                  <Descriptions.Item label="القسم" span={1}>
                    {employee.department_name || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="المنصب" span={1}>
                    {employee.position || employee.job_title || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="تارٍخ التوٍّف" span={1}>
                    {dayjs(employee.hire_date).format('YYYY-MM-DD')}
                  </Descriptions.Item>
                  <Descriptions.Item label="الراتب الأساسي" span={1}>
                    {employee.basic_salary?.toLocaleString()} ₪
                  </Descriptions.Item>
                  <Descriptions.Item label="الهاتف" span={1}>
                    <Space>
                      <PhoneOutlined />
                      {employee.phone || '-'}
                    </Space>
                  </Descriptions.Item>
                  <Descriptions.Item label="البرٍد الإلكتروٍْ" span={1}>
                    <Space>
                      <MailOutlined />
                      {employee.email || '-'}
                    </Space>
                  </Descriptions.Item>
                  <Descriptions.Item label="العْواْ" span={2}>
                    <Space>
                      <EnvironmentOutlined />
                      {employee.address || '-'}
                    </Space>
                  </Descriptions.Item>
                </Descriptions>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* إحصائيات سرٍعة */}
        <Col span={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="إجمالي المبيعات"
                  value={totalSales}
                  prefix={<BarChartOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="قٍمة المبيعات"
                  value={totalSalesAmount}
                  suffix="₪"
                  prefix={<DollarOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="مهام الإنتاج المكتملة"
                  value={completedProductions}
                  prefix={<ToolOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="معدل الحضور"
                  value={attendanceRate}
                  precision={1}
                  suffix="%"
                  prefix={<ClockCircleOutlined />}
                  valueStyle={{ color: attendanceRate >= 90 ? '#52c41a' : '#fa8c16' }}
                />
              </Card>
            </Col>
          </Row>
        </Col>

        {/* تبوٍبات التفاصٍل */}
        <Col span={24}>
          <Card>
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={[
                {
                  key: 'sales',
                  label: (
                    <span>
                      <BarChartOutlined />
                      أداط، المبيعات
                    </span>
                  ),
                  children: (
                    <Table
                      columns={salesColumns}
                      dataSource={salesPerformance}
                      rowKey="sale_date"
                      pagination={{ pageSize: 10 }}
                      loading={loading}
                    />
                  )
                },
                {
                  key: 'production',
                  label: (
                    <span>
                      <ToolOutlined />
                      مهام الإنتاج
                    </span>
                  ),
                  children: (
                    <Table
                      columns={productionColumns}
                      dataSource={productionAssignments}
                      rowKey="id"
                      pagination={{ pageSize: 10 }}
                      loading={loading}
                    />
                  )
                },
                {
                  key: 'financial',
                  label: (
                    <span>
                      <DollarOutlined />
                      المعاملات المالية
                    </span>
                  ),
                  children: (
                    <Table
                      columns={financialColumns}
                      dataSource={financialTransactions}
                      rowKey="transaction_id"
                      pagination={{ pageSize: 10 }}
                      loading={loading}
                    />
                  )
                },
                {
                  key: 'attendance',
                  label: (
                    <span>
                      <ClockCircleOutlined />
                      سجل الحضور
                    </span>
                  ),
                  children: (
                    <div>
                      <div style={{ marginBottom: '16px' }}>
                        <Progress
                          percent={attendanceRate}
                          strokeColor={attendanceRate >= 90 ? '#52c41a' : '#fa8c16'}
                          format={percent => `معدل الحضور: ${percent?.toFixed(1)}%`}
                        />
                      </div>
                      <Table
                        columns={attendanceColumns}
                        dataSource={attendanceRecords}
                        rowKey="date"
                        pagination={{ pageSize: 10 }}
                        loading={loading}
                      />
                    </div>
                  )
                },
                {
                  key: 'leaves',
                  label: (
                    <span>
                      <CalendarOutlined />
                      الإجازات
                    </span>
                  ),
                  children: (
                    <div style={{ textAlign: 'center', padding: '50px' }}>
                      <CalendarOutlined style={{ fontSize: 48, color: '#ccc' }} />
                      <div style={{ marginTop: 16 }}>سٍتم إضافة سجل الإجازات قرٍباً</div>
                    </div>
                  )
                },
                {
                  key: 'evaluations',
                  label: (
                    <span>
                      <TrophyOutlined />
                      التقٍٍمات
                    </span>
                  ),
                  children: (
                    <div style={{ textAlign: 'center', padding: '50px' }}>
                      <TrophyOutlined style={{ fontSize: 48, color: '#ccc' }} />
                      <div style={{ marginTop: 16 }}>سٍتم إضافة ّْام التقٍٍمات قرٍباً</div>
                    </div>
                  )
                }
              ]}
            />
          </Card>
        </Col>
      </Row>

      {/* أزرار الإجراط،ات */}
      <div style={{ position: 'fixed', bottom: '24px', right: '24px' }}>
        <Space>
          <Button onClick={onClose}>
            إغلاق
          </Button>
          <Button type="primary" onClick={() => message.info('سٍتم تطوٍر مٍزة التعديل قرٍباً')}>
            تعديل البيانات
          </Button>
        </Space>
      </div>
    </div>
  )
}

export default EmployeeProfile

