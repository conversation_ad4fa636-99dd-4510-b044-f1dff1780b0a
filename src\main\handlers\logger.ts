/**
 * نّام تسجيل موحد للمعالجات
 */
class HandlerLogger {
  private static isDevelopment = process.env.NODE_ENV === 'development'
  
  private static formatMessage(level: string, handler: string, message: string): string {
    const timestamp = new Date().toLocaleString('ar-SA', {
      year: 'numeric',
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    return `[${timestamp}] ${level} [${handler}] ${message}`
  }
  
  static info(handler: string, message: string, data?: any) {
    if (this.isDevelopment) {
      console.log(this.formatMessage('ℹ️', handler, message), data || '')
    }
  }
  
  static error(handler: string, message: string, error?: any) {
    console.error(this.formatMessage('❌', handler, message), error || '')
  }
  
  static warn(handler: string, message: string, data?: any) {
    if (this.isDevelopment) {
      console.warn(this.formatMessage('⚠️', handler, message), data || '')
    }
  }
  
  static success(handler: string, message: string, data?: any) {
    if (this.isDevelopment) {
      console.log(this.formatMessage('✅', handler, message), data || '')
    }
  }
  
  static debug(handler: string, message: string, data?: any) {
    if (this.isDevelopment) {
      console.debug(this.formatMessage('🔍', handler, message), data || '')
    }
  }
}

export { HandlerLogger }
