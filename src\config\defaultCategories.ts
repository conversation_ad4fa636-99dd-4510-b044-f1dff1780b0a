// ملف تكوين الفئات الافتراضية
// يمكنك تعديل هذا الملف لتخصيص الفئات حسب احتياجاتك

export interface CategoryConfig {
  name: string
  description: string
  parent_name?: string
  level?: number
}

// الفئات الرئيسية - يمكنك تعديل الأسماء والأوصاف حسب احتياجاتك
export const mainCategories: CategoryConfig[] = [
  {
    name: 'المواد الخام',
    description: 'جميع المواد الخام والأساسية المستخدمة في الإنتاج'
  },
  {
    name: 'المنتجات النهائية',
    description: 'المنتجات الجاهزة للبيع والتوزيع'
  },
  {
    name: 'قطع الغيار',
    description: 'قطع الغيار والمكونات الاحتياطية'
  },
  {
    name: 'الأدوات والمعدات',
    description: 'الأدوات والمعدات المساعدة والتشغيلية'
  },
  {
    name: 'المستلزمات المكتبية',
    description: 'الأدوات والمستلزمات المكتبية والإدارية'
  },
  {
    name: 'مواد التنظيف',
    description: 'مواد التنظيف والصحة العامة'
  },
  {
    name: 'المواد الاستهلاكية',
    description: 'المواد القابلة للاستهلاك والتجديد'
  }
]

// الفئات الفرعية - يمكنك إضافة أو تعديل أو حذف حسب الحاجة
export const subCategories: CategoryConfig[] = [
  // فئات فرعية للمواد الخام
  {
    name: 'معادن وسبائك',
    description: 'المعادن والسبائك المختلفة',
    parent_name: 'المواد الخام'
  },
  {
    name: 'مواد بلاستيكية',
    description: 'البلاستيك والمواد البوليمرية',
    parent_name: 'المواد الخام'
  },
  {
    name: 'أخشاب ومواد خشبية',
    description: 'الأخشاب والمواد الخشبية المختلفة',
    parent_name: 'المواد الخام'
  },
  {
    name: 'مواد كيميائية',
    description: 'المواد الكيميائية والمذيبات',
    parent_name: 'المواد الخام'
  },

  // فئات فرعية للمنتجات النهائية
  {
    name: 'إلكترونيات',
    description: 'الأجهزة والمكونات الإلكترونية',
    parent_name: 'المنتجات النهائية'
  },
  {
    name: 'أثاث وديكور',
    description: 'الأثاث وعناصر الديكور',
    parent_name: 'المنتجات النهائية'
  },
  {
    name: 'ملابس ومنسوجات',
    description: 'الملابس والمنسوجات والأقمشة',
    parent_name: 'المنتجات النهائية'
  },
  {
    name: 'أجهزة منزلية',
    description: 'الأجهزة المنزلية والكهربائية',
    parent_name: 'المنتجات النهائية'
  },

  // فئات فرعية للأدوات والمعدات
  {
    name: 'أدوات يدوية',
    description: 'الأدوات اليدوية والمطارق والمفاتيح',
    parent_name: 'الأدوات والمعدات'
  },
  {
    name: 'معدات كهربائية',
    description: 'المعدات والأدوات الكهربائية',
    parent_name: 'الأدوات والمعدات'
  },
  {
    name: 'أجهزة قياس',
    description: 'أجهزة القياس والمعايرة',
    parent_name: 'الأدوات والمعدات'
  },

  // فئات فرعية لقطع الغيار
  {
    name: 'قطع غيار إلكترونية',
    description: 'قطع الغيار للأجهزة الإلكترونية',
    parent_name: 'قطع الغيار'
  },
  {
    name: 'قطع غيار ميكانيكية',
    description: 'قطع الغيار الميكانيكية والمحركات',
    parent_name: 'قطع الغيار'
  },

  // فئات فرعية للمستلزمات المكتبية
  {
    name: 'أدوات الكتابة',
    description: 'الأقلام والأوراق وأدوات الكتابة',
    parent_name: 'المستلزمات المكتبية'
  },
  {
    name: 'أجهزة مكتبية',
    description: 'الطابعات والماسحات والأجهزة المكتبية',
    parent_name: 'المستلزمات المكتبية'
  },

  // فئات فرعية لمواد التنظيف
  {
    name: 'منظفات عامة',
    description: 'المنظفات العامة والمطهرات',
    parent_name: 'مواد التنظيف'
  },
  {
    name: 'أدوات التنظيف',
    description: 'الفرش والممسحات وأدوات التنظيف',
    parent_name: 'مواد التنظيف'
  }
]

// دالة للحصول على جميع الفئات مرتبة
export function getAllCategories(): CategoryConfig[] {
  return [...mainCategories, ...subCategories]
}

// دالة للحصول على الفئات الرئيسية فقط
export function getMainCategories(): CategoryConfig[] {
  return mainCategories
}

// دالة للحصول على الفئات الفرعية لفئة معينة
export function getSubCategories(parentName: string): CategoryConfig[] {
  return subCategories.filter(cat => cat.parent_name === parentName)
}
