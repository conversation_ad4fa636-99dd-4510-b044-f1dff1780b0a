import * as crypto from 'crypto'
import * as os from 'os'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

/**
 * فئة لتوليد معرف فريد للجهاز
 */
export class HardwareIdGenerator {
  private static instance: HardwareIdGenerator
  private cachedHardwareId: string | null = null

  private constructor() {}

  public static getInstance(): HardwareIdGenerator {
    if (!HardwareIdGenerator.instance) {
      HardwareIdGenerator.instance = new HardwareIdGenerator()
    }
    return HardwareIdGenerator.instance
  }

  /**
   * الحصول على معرف الجهاز الفريد
   */
  public async getHardwareId(): Promise<string> {
    if (this.cachedHardwareId) {
      return this.cachedHardwareId
    }

    try {
      const components = await this.gatherHardwareComponents()
      const combinedString = components.join('|')
      const hash = crypto.createHash('sha256').update(combinedString).digest('hex')
      
      // أخذ أول 16 حرف من الهاش لتكوين معرف قصير
      this.cachedHardwareId = hash.substring(0, 16).toUpperCase()
      return this.cachedHardwareId
    } catch (error) {
      console.error('خطأ في توليد معرف الجهاز:', error)
      // في حالة الخطأ، استخدم معرف احتياطي
      return this.generateFallbackId()
    }
  }

  /**
   * جمع مكونات الجهاز المختلفة
   */
  private async gatherHardwareComponents(): Promise<string[]> {
    const components: string[] = []

    try {
      // معرف اللوحة الأم
      const motherboardId = await this.getMotherboardId()
      if (motherboardId) components.push(`MB:${motherboardId}`)

      // معرف المعالج
      const cpuId = await this.getCpuId()
      if (cpuId) components.push(`CPU:${cpuId}`)

      // عنوان MAC للشبكة
      const macAddress = await this.getMacAddress()
      if (macAddress) components.push(`MAC:${macAddress}`)

      // معرف القرص الصلب
      const diskId = await this.getDiskId()
      if (diskId) components.push(`DISK:${diskId}`)

      // معلومات النظام
      const systemInfo = this.getSystemInfo()
      components.push(`SYS:${systemInfo}`)

    } catch (error) {
      console.error('خطأ في جمع مكونات الجهاز:', error)
    }

    return components
  }

  /**
   * الحصول على معرف اللوحة الأم
   */
  private async getMotherboardId(): Promise<string | null> {
    try {
      const platform = os.platform()
      
      if (platform === 'win32') {
        const { stdout } = await execAsync('wmic baseboard get serialnumber /value')
        const match = stdout.match(/SerialNumber=(.+)/)
        return match ? match[1].trim() : null
      } else if (platform === 'linux') {
        const { stdout } = await execAsync('sudo dmidecode -s baseboard-serial-number')
        return stdout.trim() || null
      } else if (platform === 'darwin') {
        const { stdout } = await execAsync('system_profiler SPHardwareDataType | grep "Serial Number"')
        const match = stdout.match(/Serial Number \(system\): (.+)/)
        return match ? match[1].trim() : null
      }
    } catch (error) {
      console.error('خطأ في الحصول على معرف اللوحة الأم:', error)
    }
    return null
  }

  /**
   * الحصول على معرف المعالج
   */
  private async getCpuId(): Promise<string | null> {
    try {
      const platform = os.platform()
      
      if (platform === 'win32') {
        const { stdout } = await execAsync('wmic cpu get processorid /value')
        const match = stdout.match(/ProcessorId=(.+)/)
        return match ? match[1].trim() : null
      } else if (platform === 'linux') {
        const { stdout } = await execAsync('cat /proc/cpuinfo | grep "processor" | head -1')
        return stdout.trim() || null
      } else if (platform === 'darwin') {
        const { stdout } = await execAsync('sysctl -n machdep.cpu.brand_string')
        return stdout.trim() || null
      }
    } catch (error) {
      console.error('خطأ في الحصول على معرف المعالج:', error)
    }
    return null
  }

  /**
   * الحصول على عنوان MAC
   */
  private async getMacAddress(): Promise<string | null> {
    try {
      const networkInterfaces = os.networkInterfaces()
      
      for (const interfaceName in networkInterfaces) {
        const interfaces = networkInterfaces[interfaceName]
        if (interfaces) {
          for (const iface of interfaces) {
            if (!iface.internal && iface.mac && iface.mac !== '00:00:00:00:00:00') {
              return iface.mac
            }
          }
        }
      }
    } catch (error) {
      console.error('خطأ في الحصول على عنوان MAC:', error)
    }
    return null
  }

  /**
   * الحصول على معرف القرص الصلب
   */
  private async getDiskId(): Promise<string | null> {
    try {
      const platform = os.platform()
      
      if (platform === 'win32') {
        const { stdout } = await execAsync('wmic diskdrive get serialnumber /value')
        const match = stdout.match(/SerialNumber=(.+)/)
        return match ? match[1].trim() : null
      } else if (platform === 'linux') {
        const { stdout } = await execAsync('lsblk -o NAME,SERIAL | head -2 | tail -1')
        const parts = stdout.trim().split(/\s+/)
        return parts.length > 1 ? parts[1] : null
      } else if (platform === 'darwin') {
        const { stdout } = await execAsync('system_profiler SPSerialATADataType | grep "Serial Number"')
        const match = stdout.match(/Serial Number: (.+)/)
        return match ? match[1].trim() : null
      }
    } catch (error) {
      console.error('خطأ في الحصول على معرف القرص:', error)
    }
    return null
  }

  /**
   * الحصول على معلومات النظام
   */
  private getSystemInfo(): string {
    const info = [
      os.platform(),
      os.arch(),
      os.hostname(),
      os.release()
    ]
    return info.join('-')
  }

  /**
   * توليد معرف احتياطي في حالة فشل الطرق الأخرى
   */
  private generateFallbackId(): string {
    const fallbackData = [
      os.hostname(),
      os.platform(),
      os.arch(),
      os.release(),
      Date.now().toString()
    ].join('|')
    
    const hash = crypto.createHash('sha256').update(fallbackData).digest('hex')
    return hash.substring(0, 16).toUpperCase()
  }

  /**
   * التحقق من صحة معرف الجهاز
   */
  public async validateHardwareId(storedId: string): Promise<boolean> {
    try {
      const currentId = await this.getHardwareId()
      return currentId === storedId
    } catch (error) {
      console.error('خطأ في التحقق من معرف الجهاز:', error)
      return false
    }
  }

  /**
   * إعادة تعيين الكاش
   */
  public resetCache(): void {
    this.cachedHardwareId = null
  }
}
