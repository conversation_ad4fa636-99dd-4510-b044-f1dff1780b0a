# 📋 دليل السكريپتات المحسنة - ZET.IA

## 🎯 السكريپتات الأساسية

### **1. السكريپت الرئيسي المحسن**
```bash
npm run build-enhanced
# أو
node scripts/enhanced-build.js
```
**الوصف:** السكريپت الرئيسي الشامل الذي يقوم بـ:
- ✅ فحص ما قبل البناء
- ✅ إصلاح مشاكل TypeScript
- ✅ بناء واجهة المستخدم (Renderer)
- ✅ بناء العملية الرئيسية (Main Process)
- ✅ نسخ الأصول
- ✅ فحص شامل للبناء
- ✅ بناء النسخة المحمولة
- ✅ بناء المثبت
- ✅ تقرير نهائي مفصل

**هذا هو السكريپت الموصى به للاستخدام دائماً!**

### **2. السكريپتات المساعدة**

#### **بناء النسخة المحمولة فقط:**
```bash
npm run build-portable
# أو
node scripts/build-portable-final.js
```

#### **بناء المثبت فقط:**
```bash
npm run build-installer
```

#### **تنظيف الإصدارات القديمة:**
```bash
npm run cleanup-releases
```

## 🔧 السكريپتات الأساسية للتطوير

### **التطوير:**
```bash
npm run dev          # تشغيل وضع التطوير
npm run dev:renderer # تشغيل Renderer فقط
npm run dev:main     # تشغيل Main Process فقط
```

### **البناء الأساسي:**
```bash
npm run build         # بناء أساسي
npm run build:renderer # بناء Renderer فقط
npm run build:main    # بناء Main Process فقط
```

### **التشغيل:**
```bash
npm run electron     # تشغيل التطبيق
npm run start        # بناء وتشغيل
```

## 📁 الملفات المتبقية

### **السكريپتات الموجودة:**
- `enhanced-build.js` - السكريپت الرئيسي المحسن ⭐
- `build-portable-final.js` - بناء النسخة المحمولة
- `after-pack.js` - معالجة ما بعد البناء (مطلوب لـ electron-builder)
- `cleanup-old-releases.js` - تنظيف الإصدارات القديمة

## 🎯 التوصيات

### **للاستخدام اليومي:**
```bash
npm run build-enhanced
```

### **للتطوير:**
```bash
npm run dev
```

### **لتنظيف المشروع:**
```bash
npm run cleanup-releases
```

## ⚠️ ملاحظات مهمة

1. **السكريپت المحسن** (`enhanced-build.js`) هو الأفضل والأكثر شمولية
2. **تم حذف جميع السكريپتات المكررة** لمنع الالتباس
3. **جميع السكريپتات المتبقية مختبرة وتعمل بشكل صحيح**
4. **استخدم دائماً `npm run build-enhanced` للبناء الكامل**

## 🔄 تحديث تلقائي

السكريپت المحسن يقوم بـ:
- إصلاح مشاكل TypeScript تلقائياً
- استخدام sql.js بدلاً من better-sqlite3
- معالجة جميع الأخطاء الشائعة
- إنشاء تقارير مفصلة

**لا حاجة لتدخل يدوي في معظم الحالات!**
