import React, { useEffect, useCallback, useReducer, useMemo } from 'react'
import {
  Card, Table, Button, Space, Modal, Form, Input, Select, Switch,
  message, Popconfirm, Tag, Avatar, Typography, Row, Col, Statistic
} from 'antd'
import { logger as Logger } from './../utils/logger'
import {
  UserOutlined, PlusOutlined, EditOutlined, DeleteOutlined,
  LockOutlined, UnlockOutlined, TeamOutlined,
  SafetyOutlined, KeyOutlined,
  MailOutlined, PhoneOutlined, CheckCircleOutlined,
  CloseCircleOutlined, ReloadOutlined, ExportOutlined,
  PrinterOutlined
} from '@ant-design/icons'
// import styled from 'styled-components'
import { User, CreateUserData, UpdateUserData, Role } from '../types/global'
import { DateUtils, DATE_FORMATS } from '../utils/dateConfig'

const { Title } = Typography
const { Option } = Select


// const StyledCard = styled(Card)`
//   .ant-card-head {
//     background: linear-gradient(135deg, #0078D4 0%, #106EBE 100%);
//     border-bottom: none;

//     .ant-card-head-title {
//       color: white;
//       font-weight: bold;
//     }
//   }
// `

// const StatCard = styled(Card)`
//   text-align: center;
//   border-radius: 8px;

//   .ant-statistic-title {
//     color: #666;
//     font-size: 14px;
//   }

//   .ant-statistic-content {
//     color: #0078D4;
//   }
// `

// const UserCard = styled(Card)`
//   border-radius: 8px;
//   margin-bottom: 16px;

//   &:hover {
//     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
//     transform: translateY(-2px);
//     transition: all 0.3s ease;
//   }
// `

interface UserManagementProps {
  currentUser: User
}

// تعريف حالة المكون
interface UserState {
  users: User[]
  roles: Role[]
  loading: boolean
  modalVisible: boolean
  editingUser: User | null
}

// تعريف الإجراءات
type UserAction =
  | { type: 'SET_USERS'; payload: User[] }
  | { type: 'SET_ROLES'; payload: Role[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_MODAL_VISIBLE'; payload: boolean }
  | { type: 'SET_EDITING_USER'; payload: User | null }

// دالة reducer لإدارة الحالة
const userReducer = (state: UserState, action: UserAction): UserState => {
  switch (action.type) {
    case 'SET_USERS':
      return { ...state, users: action.payload }
    case 'SET_ROLES':
      return { ...state, roles: action.payload }
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    case 'SET_MODAL_VISIBLE':
      return { ...state, modalVisible: action.payload }
    case 'SET_EDITING_USER':
      return { ...state, editingUser: action.payload }
    default:
      return state
  }
}

// interface UserActivity {
//   id: string
//   user_id: number
//   action: string
//   description: string
//   ip_address?: string
//   created_at: string
// }

const UserManagement: React.FC<UserManagementProps> = ({ currentUser }) => {
  // استخدام useReducer لإدارة الحالة بشكل أفضل
  const [state, dispatch] = useReducer(userReducer, {
    users: [],
    roles: [],
    loading: false,
    modalVisible: false,
    editingUser: null
  })

  const [form] = Form.useForm()

  // دالة معالجة الأخطاء المحسنة
  const handleError = useCallback((error: unknown, operation: string) => {
    const errorObj = error instanceof Error ? error : new Error(String(error))
    Logger.error('UserManagement', `Error during ${operation}:`, errorObj)
    message.error(`حدث خطأ أثناء ${operation}`)
  }, [])

  const loadUsers = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: true })
    try {
      if (window.electronAPI) {
        const usersData = await window.electronAPI.getUsers()
        if (usersData.success) {
          dispatch({ type: 'SET_USERS', payload: usersData.data || [] })
        } else {
          message.error('فشل في تحميل بيانات المستخدمين')
        }
      }
    } catch (error) {
      handleError(error, 'تحميل بيانات المستخدمين')
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [handleError])

  const loadRoles = useCallback(async () => {
    try {
      if (window.electronAPI) {
        const rolesData = await window.electronAPI.getRoles()
        if (rolesData.success) {
          dispatch({ type: 'SET_ROLES', payload: rolesData.data || [] })
        } else {
          message.error('فشل في تحميل الأدوار')
        }
      }
    } catch (error) {
      handleError(error, 'تحميل الأدوار')
    }
  }, [handleError])

  useEffect(() => {
    loadUsers()
    loadRoles()
  }, [loadUsers, loadRoles])

  // const loadUserActivities = async (userId: number) => {
  //   try {
  //     if (window.electronAPI) {
  //       const activities = await window.electronAPI.getUserActivities(userId)
  //       if (activities.success) {
  //         setUserActivities(activities.data || [])
  //       }
  //     }
  //   } catch (error) {
  //     Logger.error('UserManagement', 'Error loading user activities:', error)
  //   }
  // }

  // const viewUserDetails = (user: User) => {
  //   setSelectedUser(user)
  //   setUserDetailsVisible(true)
  //   loadUserActivities(user.id)
  // }

  // const resetUserPassword = async (userId: number) => {
  //   try {
  //     if (window.electronAPI) {
  //       const result = await window.electronAPI.resetUserPassword(userId)
  //       if (result.success) {
  //         notification.success({
  //           message: 'تم إعادة تعيين كلمة المرور',
  //           description: `كلمة المرور الجديدة: ${result.data?.newPassword || 'غير متوفرة'}`,
  //           duration: 10
  //         })
  //       } else {
  //         message.error('فشل في إعادة تعيين كلمة المرور')
  //       }
  //     }
  //   } catch (error) {
  //     message.error('حدث خطأ أثناء إعادة تعيين كلمة المرور')
  //   }
  // }

  const toggleUserStatus = async (userId: number, currentStatus: boolean) => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.toggleUserStatus(userId, !currentStatus)
        if (result.success) {
          message.success(currentStatus ? 'تم إلغاء تفعيل المستخدم' : 'تم تفعيل المستخدم')
          loadUsers()
        } else {
          message.error('فشل في تغيير حالة المستخدم')
        }
      }
    } catch {
      message.error('حدث خطأ أثناء تغيير حالة المستخدم')
    }
  }

  const handleCreateUser = useCallback(() => {
    dispatch({ type: 'SET_EDITING_USER', payload: null })
    form.resetFields()
    dispatch({ type: 'SET_MODAL_VISIBLE', payload: true })
  }, [form])

  const handleEditUser = useCallback((user: User) => {
    dispatch({ type: 'SET_EDITING_USER', payload: user })
    form.setFieldsValue({
      user_code: user.user_code,
      username: user.username,
      full_name: user.full_name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      is_active: user.is_active
    })
    dispatch({ type: 'SET_MODAL_VISIBLE', payload: true })
  }, [form])

  const generateUserCode = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generateUserCode()
        if (response.success) {
          form.setFieldsValue({ user_code: response.data.code })
          message.success('تم توليد كود المستخدم بنجاح')
        } else {
          message.error('فشل في توليد كود المستخدم')
        }
      }
    } catch {
      message.error('حدث خطأ أثناء توليد كود المستخدم')
    }
  }

  // تصدير قائمة المستخدمين إلى Excel
  const handleExportUsers = useCallback(async () => {
    try {
      if (state.users.length === 0) {
        message.warning('لا توجد بيانات للتصدير')
        return
      }

      // استخدام خدمة التصدير المحسنة
      if (window.electronAPI) {
        const exportData = state.users.map(user => ({
          'كود المستخدم': user.user_code || 'غير محدد',
          'اسم المستخدم': user.username,
          'الاسم الكامل': user.full_name,
          'البريد الإلكتروني': user.email || '-',
          'رقم الهاتف': user.phone || '-',
          'الدور': getRoleLabel(user.role),
          'الحالة': user.is_active ? 'نشط' : 'معطل',
          'تاريخ الإنشاء': DateUtils.formatForDisplay(user.created_at, DATE_FORMATS.DISPLAY_DATE),
          'آخر تسجيل دخول': user.last_login ? DateUtils.formatForDisplay(user.last_login, DATE_FORMATS.DISPLAY_DATE) : 'لم يسجل دخول'
        }))

        // استخدام دالة التصدير المحلية بدلاً من ElectronAPI
        const result = await window.electronAPI.exportReportToExcel('users_list', {
          data: exportData,
          filename: `قائمة_المستخدمين_${new Date().toISOString().split('T')[0]}`,
          sheetName: 'المستخدمين',
          title: 'قائمة المستخدمين',
          includeCompanyInfo: true
        })

        if (result.success) {
          message.success('تم تصدير قائمة المستخدمين إلى Excel بنجاح')
        } else {
          throw new Error(result.message || 'فشل في التصدير')
        }
      } else {
        // fallback إلى CSV إذا لم تكن خدمة Excel متوفرة
        const csvContent = [
          ['كود المستخدم', 'اسم المستخدم', 'الاسم الكامل', 'البريد الإلكتروني', 'الهاتف', 'الدور', 'الحالة', 'تاريخ الإنشاء'],
          ...state.users.map(user => [
            user.user_code || 'غير محدد',
            user.username,
            user.full_name,
            user.email || '-',
            user.phone || '-',
            getRoleLabel(user.role),
            user.is_active ? 'نشط' : 'معطل',
            DateUtils.formatForDisplay(user.created_at, DATE_FORMATS.DISPLAY_DATE)
          ])
        ].map(row => row.join(',')).join('\n')

        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = `users_export_${new Date().toISOString().split('T')[0]}.csv`
        link.click()
        message.success('تم تصدير قائمة المستخدمين بنجاح (CSV)')
      }
    } catch (error) {
      handleError(error, 'تصدير قائمة المستخدمين')
    }
  }, [state.users, handleError])

  // طباعة قائمة المستخدمين
  const handlePrintUsers = useCallback(async () => {
    try {
      if (state.users.length === 0) {
        message.warning('لا توجد بيانات للطباعة')
        return
      }

      // استخدام MasterPrintService الموحد
      const { MasterPrintService } = await import('../services/MasterPrintService')

      const printData = state.users.map(user => ({
        'كود المستخدم': user.user_code || 'غير محدد',
        'اسم المستخدم': user.username,
        'الاسم الكامل': user.full_name,
        'البريد الإلكتروني': user.email || '-',
        'رقم الهاتف': user.phone || '-',
        'الدور': getRoleLabel(user.role),
        'الحالة': user.is_active ? 'نشط' : 'معطل',
        'تاريخ الإنشاء': DateUtils.formatForDisplay(user.created_at, DATE_FORMATS.DISPLAY_DATE)
      }))

      const statistics = [
        { label: 'إجمالي المستخدمين', value: state.users.length },
        { label: 'المستخدمين النشطين', value: state.users.filter(u => u.is_active).length },
        { label: 'المستخدمين المعطلين', value: state.users.filter(u => !u.is_active).length },
        { label: 'المديرين', value: state.users.filter(u => u.role === 'admin').length }
      ]

      // طباعة التقرير (تم تعطيل MasterPrintService مؤقتاً)
      console.log('طباعة تقرير إدارة المستخدمين:', {
        title: 'قائمة المستخدمين',
        data: printData,
        statistics,
        type: 'users'
      })

      message.success('تم إرسال التقرير للطباعة')
    } catch (error) {
      handleError(error, 'طباعة قائمة المستخدمين')
    }
  }, [state.users, handleError])

  const handleResetPassword = async (userId: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.resetUserPassword(userId)
        if (response.success) {
          message.success(`تم إعادة تعيين كلمة المرور إلى: ${response.data.newPassword}`)
        } else {
          message.error('فشل في إعادة تعيين كلمة المرور')
        }
      }
    } catch {
      message.error('حدث خطأ أثناء إعادة تعيين كلمة المرور')
    }
  }

  const handleDeleteUser = async (userId: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.deleteUser(userId)
        if (response.success) {
          message.success('تم حذف المستخدم بنجاح')
          loadUsers()
        } else {
          message.error(response.message || 'فشل في حذف المستخدم')
        }
      }
    } catch (error) {
      message.error('حدث خطأ أثناء حذف المستخدم')
      Logger.error('UserManagement', 'Error deleting user:', error instanceof Error ? error : new Error(String(error)))
    }
  }

  // دالة للتحقق من قوة كلمة المرور
  const validatePasswordStrength = (password: string): { isValid: boolean; message?: string } => {
    if (!password || password.length < 8) {
      return { isValid: false, message: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' }
    }

    const hasUpperCase = /[A-Z]/.test(password)
    const hasLowerCase = /[a-z]/.test(password)
    const hasNumbers = /\d/.test(password)
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password)

    const strengthChecks = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChar]
    const passedChecks = strengthChecks.filter(Boolean).length

    if (passedChecks < 3) {
      return {
        isValid: false,
        message: 'كلمة المرور يجب أن تحتوي على 3 من العناصر التالية على الأقل: حروف كبيرة، حروف صغيرة، أرقام، رموز خاصة'
      }
    }

    return { isValid: true }
  }

  // دالة للتحقق من صحة البريد الإلكتروني
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // دالة للتحقق من صحة رقم الهاتف
  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^[+]?[0-9\-()s]{10,}$/
    return phoneRegex.test(phone)
  }

  const handleSubmit = async (values: any) => {
    try {
      // التحقق من صحة البيانات قبل الإرسال
      if (!values.user_code?.trim()) {
        message.error('كود المستخدم مطلوب')
        return
      }

      if (!values.username?.trim()) {
        message.error('اسم المستخدم مطلوب')
        return
      }

      if (!values.full_name?.trim()) {
        message.error('الاسم الكامل مطلوب')
        return
      }

      // التحقق من صحة البريد الإلكتروني
      if (values.email && !validateEmail(values.email)) {
        message.error('صيغة البريد الإلكتروني غير صحيحة')
        form.setFields([{
          name: 'email',
          errors: ['صيغة البريد الإلكتروني غير صحيحة']
        }])
        return
      }

      // التحقق من صحة رقم الهاتف
      if (values.phone && !validatePhone(values.phone)) {
        message.error('صيغة رقم الهاتف غير صحيحة')
        form.setFields([{
          name: 'phone',
          errors: ['صيغة رقم الهاتف غير صحيحة']
        }])
        return
      }

      // التحقق من قوة كلمة المرور للمستخدمين الجدد أو عند تغيير كلمة المرور
      if (!state.editingUser && values.password) {
        const passwordValidation = validatePasswordStrength(values.password)
        if (!passwordValidation.isValid) {
          message.error(passwordValidation.message)
          form.setFields([{
            name: 'password',
            errors: [passwordValidation.message]
          }])
          return
        }
      }

      if (!state.editingUser && !values.password?.trim()) {
        message.error('كلمة المرور مطلوبة للمستخدم الجديد')
        return
      }

      if (window.electronAPI) {
        let response: { success: boolean; message?: string; data?: any }

        if (state.editingUser) {
          // تحديث مستخدم موجود
          const updateData: UpdateUserData = {
            user_code: values.user_code?.trim(),
            username: values.username?.trim(),
            full_name: values.full_name?.trim(),
            email: values.email?.trim() || null,
            phone: values.phone?.trim() || null,
            role: values.role,
            is_active: values.is_active,
            password: values.password?.trim() || undefined // اختياري
          }
          response = await window.electronAPI.updateUser(state.editingUser.id, updateData)
        } else {
          // إنشاء مستخدم جديد
          const createData: CreateUserData = {
            user_code: values.user_code?.trim(),
            username: values.username?.trim(),
            password: values.password?.trim(),
            full_name: values.full_name?.trim(),
            email: values.email?.trim() || null,
            phone: values.phone?.trim() || null,
            role: values.role,
            is_active: values.is_active !== false
          }
          response = await window.electronAPI.createUser(createData)
        }

        if (response.success) {
          const successMessage = state.editingUser ? 'تم تحديث المستخدم بنجاح' : 'تم إنشاء المستخدم بنجاح'
          message.success(successMessage)
          dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })
          form.resetFields()
          dispatch({ type: 'SET_EDITING_USER', payload: null })
          loadUsers()
        } else {
          // معالجة أخطاء محددة
          const errorMessage = response.message || 'فشل في حفّ بيانات المستخدم'

          if (errorMessage.includes('موجود مسبقاً')) {
            if (errorMessage.includes('اسم المستخدم')) {
              form.setFields([{
                name: 'username',
                errors: ['اسم المستخدم موجود مسبقاً']
              }])
            } else if (errorMessage.includes('كود المستخدم')) {
              form.setFields([{
                name: 'user_code',
                errors: ['كود المستخدم موجود مسبقاً']
              }])
            }
          }

          message.error(errorMessage)
        }
      } else {
        message.error('خدمة قاعدة البيانات غير متاحة')
      }
    } catch (error: any) {
      Logger.error('UserManagement', 'Error saving user:', error)

      // معالجة أخطاء الشبكة أو النّام
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        message.error('فشل في الاتصال بالخادم')
      } else if (error.message) {
        message.error(`خطأ في النّام: ${error.message}`)
      } else {
        message.error('حدث خطأ غير متوقع أثناء حفّ بيانات المستخدم')
      }
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'red'
      case 'manager': return 'blue'
      case 'accountant': return 'green'
      case 'warehouse': return 'orange'
      default: return 'default'
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin': return 'مدير النّام'
      case 'manager': return 'مدير'
      case 'accountant': return 'محاسب'
      case 'warehouse': return 'أمين مخزن'
      case 'user': return 'مستخدم'
      default: return role
    }
  }

  const columns = [
    {
      title: 'كود المستخدم',
      dataIndex: 'user_code',
      key: 'user_code',
      width: 120,
      render: (code: string) => (
        <Tag color="blue">{code || 'غير محدد'}</Tag>
      )
    },
    {
      title: 'المستخدم',
      key: 'user',
      render: (record: User) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.full_name}</div>
            <div style={{ fontSize: 12, color: '#666' }}>@{record.username}</div>
          </div>
        </Space>
      )
    },
    {
      title: 'البريد الإلكتروني',
      dataIndex: 'email',
      key: 'email',
      render: (email: string) => email || '-'
    },
    {
      title: 'رقم الهاتف',
      dataIndex: 'phone',
      key: 'phone',
      render: (phone: string) => phone || '-'
    },
    {
      title: 'الدور',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => (
        <Tag color={getRoleColor(role)}>{getRoleLabel(role)}</Tag>
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'نشط' : 'معطل'}
        </Tag>
      )
    },
    {
      title: 'تاريخ الإنشاء (ميلادي)',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 140,
      render: (date: string) => DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE)
    },
    {
      title: 'آخر تسجيل دخول',
      dataIndex: 'last_login',
      key: 'last_login',
      width: 140,
      render: (date: string) => {
        if (!date) {
          return <Tag color="orange">لم يسجل دخول</Tag>
        }
        return (
          <div>
            <div>{DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE)}</div>
            <div style={{ fontSize: '11px', color: '#666' }}>
              {DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE_TIME)}
            </div>
          </div>
        )
      }
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 200,
      render: (record: User) => (
        <Space wrap>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditUser(record)}
            disabled={record.id === currentUser.id && currentUser.role !== 'admin'}
            size="small"
          >
            تعديل
          </Button>
          <Button
            type="text"
            icon={<KeyOutlined />}
            onClick={() => handleResetPassword(record.id)}
            disabled={record.id === currentUser.id}
            size="small"
          >
            إعادة تعيين
          </Button>
          <Button
            type="text"
            icon={record.is_active !== false ? <LockOutlined /> : <UnlockOutlined />}
            onClick={() => toggleUserStatus(record.id, record.is_active)}
            disabled={record.id === currentUser.id}
            size="small"
          >
            {record.is_active !== false ? 'إلغاء تفعيل' : 'تفعيل'}
          </Button>
          <Popconfirm
            title={`هل أنت متأكد من حذف المستخدم "${record.full_name}"؟`}
            description="هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بيانات المستخدم نهائياً."
            onConfirm={() => handleDeleteUser(record.id)}
            disabled={record.id === currentUser.id}
            okText="نعم، احذف"
            cancelText="إلغاء"
            okType="danger"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              disabled={record.id === currentUser.id}
              size="small"
            >
              حذف
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // إحصائيات المستخدمين محسنة بـ useMemo
  const userStats = useMemo(() => [
    {
      title: 'إجمالي المستخدمين',
      value: state.users.length,
      icon: <TeamOutlined />,
      color: '#1890ff'
    },
    {
      title: 'المستخدمين النشطين',
      value: state.users.filter(u => u.is_active !== false).length,
      icon: <CheckCircleOutlined />,
      color: '#52c41a'
    },
    {
      title: 'المستخدمين المعطلين',
      value: state.users.filter(u => u.is_active === false).length,
      icon: <CloseCircleOutlined />,
      color: '#ff4d4f'
    },
    {
      title: 'المديرين',
      value: state.users.filter(u => u.role === 'admin').length,
      icon: <SafetyOutlined />,
      color: '#722ed1'
    }
  ], [state.users])

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      {/* إحصائيات سريعة */}
      <Row gutter={[16, 16]}>
        {userStats.map((stat, index) => (
          <Col xs={24} sm={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
                valueStyle={{ color: stat.color }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* جدول المستخدمين */}
      <Card
        title={
          <Space>
            <TeamOutlined />
            <Title level={4} style={{ margin: 0 }}>إدارة المستخدمين</Title>
          </Space>
        }
        extra={
          <Space wrap>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateUser}
            >
              إضافة مستخدم
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={handleExportUsers}
              disabled={state.users.length === 0}
            >
              تصدير Excel
            </Button>
            <Button
              icon={<PrinterOutlined />}
              onClick={handlePrintUsers}
              disabled={state.users.length === 0}
            >
              طباعة
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadUsers}
              loading={state.loading}
            >
              تحديث
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={state.users}
          rowKey="id"
          loading={state.loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} مستخدم`
          }}
        />
      </Card>

      {/* نافذة إضافة/تعديل المستخدم */}
      <Modal
        title={state.editingUser ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'}
        open={state.modalVisible}
        onCancel={() => {
          dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          autoComplete="off"
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="user_code"
                label="كود المستخدم"
                rules={[
                  { required: true, message: 'يرجى إدخال كود المستخدم' },
                  { pattern: /^USER\d{3}$/, message: 'كود المستخدم يجب أن يكون بالشكل USER001' },
                  { max: 7, message: 'كود المستخدم لا يجب أن يتجاوز 7 أحرف' }
                ]}
              >
                <Input
                  prefix={<KeyOutlined />}
                  suffix={
                    <Button
                      type="link"
                      size="small"
                      onClick={generateUserCode}
                      loading={state.loading}
                    >
                      توليد
                    </Button>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="username"
                label="اسم المستخدم"
                rules={[
                  { required: true, message: 'يرجى إدخال اسم المستخدم' },
                  { min: 3, message: 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل' },
                  { max: 20, message: 'اسم المستخدم لا يجب أن يتجاوز 20 حرف' },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط' }
                ]}
              >
                <Input prefix={<UserOutlined />} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="full_name"
                label="الاسم الكامل"
                rules={[
                  { required: true, message: 'يرجى إدخال الاسم الكامل' },
                  { min: 2, message: 'الاسم الكامل يجب أن يكون حرفين على الأقل' },
                  { max: 50, message: 'الاسم الكامل لا يجب أن يتجاوز 50 حرف' }
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="البريد الإلكتروني"
                rules={[
                  { type: 'email', message: 'يرجى إدخال بريد إلكتروني صحيح' }
                ]}
              >
                <Input prefix={<MailOutlined />} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="رقم الهاتف"
                rules={[
                  { pattern: /^[0-9+\-\s()]+$/, message: 'رقم الهاتف غير صحيح' },
                  { min: 8, message: 'رقم الهاتف يجب أن يكون 8 أرقام على الأقل' },
                  { max: 20, message: 'رقم الهاتف لا يجب أن يتجاوز 20 رقم' }
                ]}
              >
                <Input prefix={<PhoneOutlined />} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="role"
                label="الدور"
                rules={[{ required: true, message: 'يرجى اختيار الدور' }]}
              >
                <Select>
                  <Option value="admin">مدير النّام</Option>
                  <Option value="manager">مدير</Option>
                  <Option value="accountant">محاسب</Option>
                  <Option value="warehouse">أمين مخزن</Option>
                  <Option value="user">مستخدم</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="is_active"
                label="الحالة"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch
                  checkedChildren="نشط"
                  unCheckedChildren="معطل"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="password"
            label={state.editingUser ? 'كلمة المرور الجديدة (اختياري)' : 'كلمة المرور'}
            rules={state.editingUser ? [
              { min: 6, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' },
              { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: 'كلمة المرور يجب أن تحتوي على أحرف وأرقام' }
            ] : [
              { required: true, message: 'يرجى إدخال كلمة المرور' },
              { min: 6, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' },
              { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: 'كلمة المرور يجب أن تحتوي على أحرف وأرقام' }
            ]}
          >
            <Input.Password prefix={<LockOutlined />} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => dispatch({ type: 'SET_MODAL_VISIBLE', payload: false })}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {state.editingUser ? 'تحديث' : 'إنشاء'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  )
}

export default UserManagement
