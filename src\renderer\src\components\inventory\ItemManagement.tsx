import React, { useState, useEffect } from 'react'
import {
  Card, Table, Button, Modal, Form, Input, Select, InputNumber, Switch,
  Space, Popconfirm, Typography, Row, Col, Statistic, Tag, Tooltip, App
} from 'antd'
import { logger as Logger } from './../../utils/logger'
import {
  PlusOutlined, EditOutlined, DeleteOutlined, AppstoreOutlined,
  BarcodeOutlined, DollarOutlined, CheckCircleOutlined, StopOutlined,
  PrinterOutlined, FileExcelOutlined, PictureOutlined, EyeOutlined,
  ImportOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { Item, Category, ApiResponse, ItemType, ITEM_TYPES, ITEM_TYPE_OPTIONS } from '../../types/global.d'
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig'
import * as XLSX from 'xlsx'
import { SimpleImageManager } from '../common/SimpleImageManager'
import { Modal } from 'antd'
import ImageGallery from '../common/ImageGallery'
import ItemImportExport from './ItemImportExport'

const { Title } = Typography
const { Option } = Select

const StyledCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
`

const ActionButton = styled(Button)`
  margin-left: 8px;
`

interface ItemManagementProps {
  onBack?: () => void
}

interface Warehouse {
  id: number
  code: string
  name: string
  is_active: boolean
}

const ItemManagement: React.FC<ItemManagementProps> = () => {
  const { message } = App.useApp()
  const [items, setItems] = useState<Item[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [warehouses, setWarehouses] = useState<Warehouse[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingItem, setEditingItem] = useState<Item | null>(null)
  const [form] = Form.useForm()

  // فلترة الأصناف
  const [selectedItemType, setSelectedItemType] = useState<ItemType | 'all'>('all')
  const [filteredItems, setFilteredItems] = useState<Item[]>([])

  // حالات إدارة الصور
  const [imageManagerVisible, setImageManagerVisible] = useState(false)
  const [galleryVisible, setGalleryVisible] = useState(false)
  const [selectedItemForImages, setSelectedItemForImages] = useState<Item | null>(null)
  const [itemImages, setItemImages] = useState<any[]>([])

  // حالة الاستيراد والتصدير
  const [importExportVisible, setImportExportVisible] = useState(false)

  useEffect(() => {
    loadItems()
    loadCategories()
    loadWarehouses()
  }, [])

  // فلترة الأصناف عند تغيير النوع المحدد
  useEffect(() => {
    if (selectedItemType === 'all') {
      setFilteredItems(items)
    } else {
      setFilteredItems(items.filter(item => item.type === selectedItemType))
    }
  }, [items, selectedItemType])

  const loadItems = async () => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getItems()
        if (Array.isArray(response)) {
          const itemsData = response
          setItems(itemsData) // عرض جميع الأصناف (النشطة والمعطلة)
          Logger.info('ItemManagement', '✅ تم تحميل ${itemsData.length} صنف بنجاح')
        } else if (response && typeof response === 'object' && 'success' in response && (response as any).success) {
          const itemsData = (response as any).data || []
          setItems(itemsData) // عرض جميع الأصناف (النشطة والمعطلة)
          Logger.info('ItemManagement', '✅ تم تحميل ${itemsData.length} صنف بنجاح')
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل الأصناف'
          Logger.error('ItemManagement', '❌ خطأ في تحميل الأصناف:', errorMessage)
          message.error(errorMessage)
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('ItemManagement', '❌ window.electronAPI غير متوفر')
        message.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل الأصناف'
      Logger.error('ItemManagement', '❌ خطأ في تحميل الأصناف:', error instanceof Error ? error : new Error(String(error)))
      message.error(`خطأ في تحميل الأصناف: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  const loadCategories = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getCategories()
        if (Array.isArray(response)) {
          const categoriesData = response
          setCategories(categoriesData.filter((cat: Category) => cat.is_active))
          Logger.info('ItemManagement', '✅ تم تحميل ${categoriesData.length} فئة بنجاح')
        } else if (response && typeof response === 'object' && 'success' in response && (response as any).success) {
          const categoriesData = (response as any).data || []
          setCategories(categoriesData.filter((cat: Category) => cat.is_active))
          Logger.info('ItemManagement', '✅ تم تحميل ${categoriesData.length} فئة بنجاح')
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل الفئات'
          Logger.error('ItemManagement', '❌ خطأ في تحميل الفئات:', errorMessage)
          message.error(errorMessage)
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('ItemManagement', '❌ window.electronAPI غير متوفر')
        message.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل الفئات'
      Logger.error('ItemManagement', '❌ خطأ في تحميل الفئات:', error instanceof Error ? error : new Error(String(error)))
      message.error(`خطأ في تحميل الفئات: ${errorMessage}`)
    }
  }

  const loadWarehouses = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getWarehouses()
        if (Array.isArray(response)) {
          const warehousesData = response as any[]
          setWarehouses(warehousesData.filter((warehouse: any) => warehouse.is_active))
          Logger.info('ItemManagement', '✅ تم تحميل ${warehousesData.length} مخزن بنجاح')
        } else if (response && typeof response === 'object' && 'success' in response && (response as any).success) {
          const warehousesData = (response as any).data || []
          setWarehouses(warehousesData.filter((warehouse: any) => warehouse.is_active))
          Logger.info('ItemManagement', '✅ تم تحميل ${warehousesData.length} مخزن بنجاح')
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل المخازن'
          Logger.error('ItemManagement', '❌ خطأ في تحميل المخازن:', errorMessage)
          message.error(errorMessage)
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('ItemManagement', '❌ window.electronAPI غير متوفر')
        message.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل المخازن'
      Logger.error('ItemManagement', '❌ خطأ في تحميل المخازن:', error instanceof Error ? error : new Error(String(error)))
      message.error(`خطأ في تحميل المخازن: ${errorMessage}`)
    }
  }

  const handleAdd = () => {
    setEditingItem(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = (item: Item) => {
    setEditingItem(item)

    setTimeout(() => {
      try {
        if (form && form.setFieldsValue) {
          form.setFieldsValue({
            code: item.code,
            name: item.name,
            description: item.description,
            category_id: item.category_id,
            warehouse_id: item.warehouse_id,
            type: item.type || 'raw_material',
            unit: item.unit,
            cost_price: item.cost_price,
            sale_price: item.sale_price,
            min_quantity: item.min_quantity,
            max_quantity: item.max_quantity,
            is_active: item.is_active
          })
        }
      } catch (error) {
        Logger.warn('ItemManagement', 'خطأ في تحديث النموذج:', error)
      }
    }, 100)

    setModalVisible(true)
  }

  const handleDelete = async (itemId: number) => {
    try {
      if (window.electronAPI) {
        const response: ApiResponse = await window.electronAPI.deleteItem(itemId)
        if (response.success) {
          message.success('تم حذف الصنف بنجاح')
          loadItems()
        } else {
          message.error(response.message || 'فشل في حذف الصنف')
        }
      }
    } catch (error) {
      Logger.error('ItemManagement', 'خطأ في حذف الصنف:', error instanceof Error ? error : new Error(String(error)))
      message.error('فشل في حذف الصنف')
    }
  }

  // دوال إدارة الصور
  const handleManageImages = (item: Item) => {
    setSelectedItemForImages(item)
    setImageManagerVisible(true)
  }

  const handleViewGallery = async (item: Item) => {
    try {
      setSelectedItemForImages(item)
      // استخدام النظام الجديد البسيط لجلب الصور
      const { simpleImageService } = await import('../../services/SimpleImageService')

      const result = await simpleImageService.getImages('item', item.id)

      if (result.success && result.data) {
        // تحويل الصور إلى التنسيق المطلوب للمعرض
        const galleryImages = result.data.map((img: any) => ({
          id: img.id,
          image_path: img.path,
          image_name: img.name,
          is_primary: img.is_primary
        }))

        setItemImages(galleryImages)
        setGalleryVisible(true)
      } else {
        throw new Error(result.error || 'فشل في تحميل الصور')
      }
    } catch (error) {
      Logger.error('ItemManagement', 'خطأ في تحميل صور الصنف:', error instanceof Error ? error : new Error(String(error)))
      message.error('حدث خطأ أثناء تحميل صور الصنف')
    }
  }

  const closeImageManager = () => {
    setImageManagerVisible(false)
    setSelectedItemForImages(null)
  }

  const closeGallery = () => {
    setGalleryVisible(false)
    setSelectedItemForImages(null)
    setItemImages([])
  }

  const generateCode = async (categoryId?: number) => {
    try {
      if (!window.electronAPI) {
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن إنشاء الكود')
        return
      }

      const response: ApiResponse = await window.electronAPI.generateItemCode(categoryId)
      if (response.success && response.data) {
        setTimeout(() => {
          try {
            if (form && form.setFieldsValue) {
              form.setFieldsValue({ code: response.data.code })
            }
          } catch (error) {
            Logger.warn('ItemManagement', 'خطأ في تحديث النموذج:', error)
          }
        }, 100)
        message.success('تم إنشاء الكود تلقائياً')
      } else {
        message.error(response.message || 'فشل في إنشاء الكود')
      }
    } catch (error) {
      Logger.error('ItemManagement', 'خطأ في إنشاء الكود:', error instanceof Error ? error : new Error(String(error)))
      message.error('فشل في إنشاء الكود')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      if (window.electronAPI) {
        // التحقق من تفرد الكود قبل الحفّ
        const codeCheckResponse = await window.electronAPI.checkCodeUniqueness('items', 'code', values.code, editingItem?.id)
        if (codeCheckResponse.success && !codeCheckResponse.data.isUnique) {
          message.error('هذا الكود مستخدم بالفعل، يرجى اختيار كود آخر')
          return
        }

        let response: ApiResponse

        if (editingItem) {
          response = await window.electronAPI.updateItem(editingItem.id, values)
        } else {
          response = await window.electronAPI.createItem(values)
        }

        if (response.success) {
          message.success(editingItem ? 'تم تحديث الصنف بنجاح' : 'تم إضافة الصنف بنجاح')
          setModalVisible(false)
          setEditingItem(null)
          form.resetFields()
          loadItems()
        } else {
          message.error(response.message || 'فشل في حفّ الصنف')
        }
      }
    } catch (error) {
      Logger.error('ItemManagement', 'خطأ في حفّ الصنف:', error instanceof Error ? error : new Error(String(error)))
      message.error('فشل في حفّ الصنف')
    }
  }

  const columns = [
    {
      title: 'الكود',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      render: (text: string) => (
        <Tag color="blue" style={{ fontFamily: 'monospace' }}>
          {text}
        </Tag>
      )
    },
    {
      title: 'اسم الصنف',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Item) => (
        <Space direction="vertical" size={0}>
          <span style={{ fontWeight: 'bold' }}>{text}</span>
          {record.description && (
            <span style={{ fontSize: '12px', color: '#666' }}>
              {record.description}
            </span>
          )}
        </Space>
      )
    },
    {
      title: 'الفئة',
      dataIndex: 'category_name',
      key: 'category_name',
      render: (text: string) => text || 'غير محدد'
    },
    {
      title: 'نوع الصنف',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: ItemType) => {
        const typeInfo = ITEM_TYPES[type] || 'غير محدد'
        const colors: Record<ItemType, string> = {
          raw_material: 'orange',
          finished_product: 'green',
          component: 'blue',
          tool: 'purple',
          consumable: 'red'
        }
        return (
          <Tag color={colors[type] || 'default'}>
            {typeInfo}
          </Tag>
        )
      }
    },
    {
      title: 'المخزن',
      dataIndex: 'warehouse_name',
      key: 'warehouse_name',
      render: (text: string) => (
        <Tag color="purple">{text || 'غير محدد'}</Tag>
      )
    },
    {
      title: 'الوحدة',
      dataIndex: 'unit',
      key: 'unit',
      width: 80,
      render: (text: string) => (
        <Tag color="green">{text}</Tag>
      )
    },
    {
      title: 'سعر التكلفة',
      dataIndex: 'cost_price',
      key: 'cost_price',
      width: 120,
      render: (price: number) => (
        <span style={{ color: '#fa8c16' }}>
          {price?.toFixed(2)} ₪
        </span>
      )
    },
    {
      title: 'سعر البيع',
      dataIndex: 'sale_price',
      key: 'sale_price',
      width: 120,
      render: (price: number) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          {price?.toFixed(2)} ₪
        </span>
      )
    },
    {
      title: 'الحد الأدنى',
      dataIndex: 'min_quantity',
      key: 'min_quantity',
      width: 100,
      render: (qty: number) => qty || '-'
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'} icon={isActive ? <CheckCircleOutlined /> : <StopOutlined />}>
          {isActive ? 'نشط' : 'معطل'}
        </Tag>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 200,
      render: (_: any, record: Item) => (
        <Space>
          <Tooltip title="تعديل">
            <ActionButton
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="إدارة الصور">
            <ActionButton
              size="small"
              icon={<PictureOutlined />}
              onClick={() => handleManageImages(record)}
            />
          </Tooltip>
          <Tooltip title="معرض الصور">
            <ActionButton
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewGallery(record)}
            />
          </Tooltip>
          <Popconfirm
            title="هل أنت متأكد من حذف هذا الصنف؟"
            onConfirm={() => handleDelete(record.id)}
            okText="نعم"
            cancelText="لا"
          >
            <Tooltip title="حذف">
              <ActionButton
                danger
                size="small"
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // وّيفة الطباعة الموحدة
  const handlePrint = async () => {
    try {
      const { MasterPrintService } = await import('../../services/MasterPrintService')
      const printService = MasterPrintService.getInstance()

      const printData = {
        title: 'قائمة الأصناف',
        subtitle: `إجمالي الأصناف: ${items.length}`,
        date: new Date().toLocaleDateString('ar-SA'),
        items: items.map(item => ({
          name: item.name,
          description: item.code,
          quantity: 1,
          unit: item.unit || 'قطعة',
          unitPrice: item.sale_price || 0,
          total: item.sale_price || 0
        })),
        total: items.reduce((sum, item) => sum + (item.sale_price || 0), 0)
      }

      await printService.print(printData, {
        type: 'report',
        subType: 'inventory',
        showLogo: true,
        showHeader: true,
        showFooter: true
      })

      message.success('تم إرسال التقرير للطباعة')
    } catch (error) {
      Logger.error('ItemManagement', 'خطأ في الطباعة:', error as Error)
      message.error('فشل في طباعة التقرير')
    }
  }

  // وّيفة التصدير إلى Excel
  const handleExportToExcel = () => {
    try {
      const exportData = items.map((item, index) => ({
        'الرقم': index + 1,
        'كود الصنف': item.code,
        'اسم الصنف': item.name,
        'الوصف': item.description || '-',
        'الفئة': item.category_name || '-',
        'الوحدة': item.unit,
        'سعر التكلفة': item.cost_price || 0,
        'سعر البيع': item.sale_price || 0,
        'الحد الأدنى': item.min_quantity || 0,
        'الحد الأقصى': item.max_quantity || 0,
        'الحالة': item.is_active !== false ? 'نشط' : 'معطل',
        'هامش الربح': item.sale_price && item.cost_price ?
          ((item.sale_price - item.cost_price) / item.cost_price * 100).toFixed(2) + '%' : '-'
      }))

      const worksheet = XLSX.utils.json_to_sheet(exportData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'الأصناف')

      const fileName = `قائمة_الأصناف_${DateUtils.formatForDisplay(new Date().toISOString(), DATE_FORMATS.DISPLAY_DATE)}.xlsx`
      XLSX.writeFile(workbook, fileName)

      message.success('تم تصدير البيانات بنجاح')
    } catch (error) {
      Logger.error('ItemManagement', 'خطأ في التصدير:', error instanceof Error ? error : new Error(String(error)))
      message.error('حدث خطأ أثناء التصدير')
    }
  }

  // تم حذف generatePrintContent - نستخدم الآن MasterPrintService الموحد

  // إحصائيات الأصناف
  const totalItems = items.length
  const activeItems = items.filter(i => i.is_active).length
  const avgCostPrice = items.length > 0 ? items.reduce((sum, item) => sum + (item.cost_price || 0), 0) / items.length : 0
  const avgSalePrice = items.length > 0 ? items.reduce((sum, item) => sum + (item.sale_price || 0), 0) / items.length : 0

  // إحصائيات حسب نوع الصنف
  const _itemsByType = items.reduce((acc, item) => {
    const type = item.type || 'raw_material'
    acc[type] = (acc[type] || 0) + 1
    return acc
  }, {} as Record<ItemType, number>)

  return (
    <div>
      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="إجمالي الأصناف"
              value={totalItems}
              prefix={<AppstoreOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </StyledCard>
        </Col>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="الأصناف النشطة"
              value={activeItems}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </StyledCard>
        </Col>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="متوسط سعر التكلفة"
              value={avgCostPrice}
              precision={2}
              prefix={<DollarOutlined />}
              suffix="₪"
              valueStyle={{ color: '#fa8c16' }}
            />
          </StyledCard>
        </Col>
        <Col span={6}>
          <StyledCard>
            <Statistic
              title="متوسط سعر البيع"
              value={avgSalePrice}
              precision={2}
              prefix={<DollarOutlined />}
              suffix="₪"
              valueStyle={{ color: '#52c41a' }}
            />
          </StyledCard>
        </Col>
      </Row>

      {/* جدول الأصناف */}
      <StyledCard
        title={
          <Space>
            <AppstoreOutlined />
            <Title level={4} style={{ margin: 0 }}>إدارة الأصناف</Title>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<PrinterOutlined />}
              onClick={handlePrint}
              disabled={items.length === 0}
            >
              طباعة
            </Button>
            <Button
              icon={<ImportOutlined />}
              onClick={() => setImportExportVisible(true)}
              style={{ color: '#1890ff', borderColor: '#1890ff' }}
            >
              استيراد/تصدير
            </Button>
            <Button
              icon={<FileExcelOutlined />}
              onClick={handleExportToExcel}
              disabled={items.length === 0}
              style={{ color: '#52c41a', borderColor: '#52c41a' }}
            >
              تصدير Excel
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              إضافة صنف جديد
            </Button>
          </Space>
        }
      >
        {/* فلتر نوع الصنف */}
        <div style={{ marginBottom: 16 }}>
          <Space>
            <span>فلترة حسب نوع الصنف:</span>
            <Select
              value={selectedItemType}
              onChange={setSelectedItemType}
              style={{ width: 200 }}
              placeholder="اختر نوع الصنف"
            >
              <Option value="all">جميع الأنواع</Option>
              {ITEM_TYPE_OPTIONS.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredItems}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} صنف`
          }}
        />
      </StyledCard>

      {/* نموذج إضافة/تعديل الصنف */}
      <Modal
        title={editingItem ? 'تعديل الصنف' : 'إضافة صنف جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setEditingItem(null)
          form.resetFields()
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ is_active: true }}
        >
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name="code"
                label="كود الصنف"
                rules={[{ required: true, message: 'يرجى إدخال كود الصنف' }]}
              >
                <Input
                  placeholder="أدخل كود الصنف"
                  addonAfter={
                    <Button
                      size="small"
                      onClick={() => generateCode(form.getFieldValue('category_id'))}
                      icon={<BarcodeOutlined />}
                    >
                      إنشاء تلقائي
                    </Button>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="barcode"
                label="الباركود"
              >
                <Input
                  placeholder="أدخل الباركود (اختياري)"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="category_id"
                label="الفئة"
                rules={[{ required: true, message: 'يرجى اختيار الفئة' }]}
              >
                <Select
                  placeholder="اختر الفئة"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                  }
                >
                  {categories.map(cat => (
                    <Option key={cat.id} value={cat.id}>
                      {cat.path}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name="type"
                label="نوع الصنف"
                rules={[{ required: true, message: 'يرجى اختيار نوع الصنف' }]}
              >
                <Select
                  placeholder="اختر نوع الصنف"
                  showSearch
                  optionFilterProp="children"
                >
                  {ITEM_TYPE_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="warehouse_id"
                label="المخزن"
                rules={[{ required: true, message: 'يرجى اختيار المخزن' }]}
              >
                <Select
                  placeholder="اختر المخزن"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                  }
                >
                  {warehouses.map(warehouse => (
                    <Option key={warehouse.id} value={warehouse.id}>
                      {warehouse.name} ({warehouse.code})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="name"
            label="اسم الصنف"
            rules={[{ required: true, message: 'يرجى إدخال اسم الصنف' }]}
          >
            <Input placeholder="أدخل اسم الصنف" />
          </Form.Item>

          <Form.Item
            name="description"
            label="الوصف"
          >
            <Input.TextArea placeholder="أدخل وصف الصنف (اختياري)" rows={2} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="unit"
                label="الوحدة"
                rules={[{ required: true, message: 'يرجى إدخال الوحدة' }]}
              >
                <Select placeholder="اختر الوحدة">
                  <Option value="قطعة">قطعة</Option>
                  <Option value="متر">متر</Option>
                  <Option value="كيلو">كيلو</Option>
                  <Option value="لتر">لتر</Option>
                  <Option value="علبة">علبة</Option>
                  <Option value="كرتون">كرتون</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="cost_price"
                label="سعر التكلفة"
                rules={[{ required: true, message: 'يرجى إدخال سعر التكلفة' }]}
              >
                <InputNumber
                  placeholder="0.00"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  addonAfter="₪"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="sale_price"
                label="سعر البيع"
                rules={[{ required: true, message: 'يرجى إدخال سعر البيع' }]}
              >
                <InputNumber
                  placeholder="0.00"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  addonAfter="₪"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="min_quantity"
                label="الحد الأدنى للكمية"
              >
                <InputNumber
                  placeholder="0"
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="max_quantity"
                label="الحد الأقصى للكمية"
              >
                <InputNumber
                  placeholder="0"
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="is_active"
            label="الحالة"
            valuePropName="checked"
          >
            <Switch checkedChildren="نشط" unCheckedChildren="معطل" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => {
                setModalVisible(false)
                setEditingItem(null)
                form.resetFields()
              }}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {editingItem ? 'تحديث' : 'إضافة'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* مكون إدارة صور الصنف */}
      <Modal
        title={`إدارة صور الصنف: ${selectedItemForImages?.name || ''}`}
        open={imageManagerVisible}
        onCancel={closeImageManager}
        footer={null}
        width={1200}
        style={{ top: 20 }}
      >
        {selectedItemForImages && (
          <SimpleImageManager
            category="item"
            contextId={selectedItemForImages.id}
            maxImages={10}
            allowMultiple={true}
            showDescription={true}
            showPrimaryButton={true}
            onChange={(images) => {
              // يمكن إضافة معالجة إضافية هنا إذا لزم الأمر
              console.log('تم تحديث الصور:', images)
            }}
          />
        )}
      </Modal>

      {/* مكون معرض الصور */}
      {selectedItemForImages && (
        <ImageGallery
          visible={galleryVisible}
          onClose={closeGallery}
          images={itemImages}
          title={`معرض صور الصنف: ${selectedItemForImages.name}`}
        />
      )}

      {/* مكون الاستيراد والتصدير */}
      <ItemImportExport
        visible={importExportVisible}
        onClose={() => setImportExportVisible(false)}
        onImportComplete={() => {
          loadItems() // إعادة تحميل الأصناف بعد الاستيراد
          setImportExportVisible(false)
        }}
      />
    </div>
  )
}

const ItemManagementWithApp: React.FC<ItemManagementProps> = (props) => {
  return (
    <App>
      <ItemManagement {...props} />
    </App>
  )
}

export default ItemManagementWithApp
