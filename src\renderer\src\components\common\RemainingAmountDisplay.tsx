import React from 'react'
import { Tag, Progress, Space, Typography, Card, Row, Col, Statistic } from 'antd'
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  WarningOutlined
} from '@ant-design/icons'

const { Text } = Typography

// أنواع حالات الدفع
export const PAYMENT_STATUS = {
  PAID: 'paid',
  PENDING: 'pending',
  PARTIAL: 'partial',
  OVERDUE: 'overdue',
  CANCELLED: 'cancelled'
} as const

export type PaymentStatusType = typeof PAYMENT_STATUS[keyof typeof PAYMENT_STATUS]

// حساب حالة الدفع بناءً على المبالغ
export const calculatePaymentStatus = (
  totalAmount: number,
  paidAmount: number,
  dueDate?: string
): PaymentStatusType => {
  // التأكد من أن القيم رقمية وليست null أو undefined
  const safeTotalAmount = Number(totalAmount) || 0
  const safePaidAmount = Number(paidAmount) || 0

  if (safePaidAmount >= safeTotalAmount) {
    return PAYMENT_STATUS.PAID
  }

  if (safePaidAmount > 0) {
    return PAYMENT_STATUS.PARTIAL
  }

  if (dueDate && new Date(dueDate) < new Date()) {
    return PAYMENT_STATUS.OVERDUE
  }

  return PAYMENT_STATUS.PENDING
}

// الحصول على لون الحالة
export const getStatusColor = (status: PaymentStatusType) => {
  switch (status) {
    case PAYMENT_STATUS.PAID:
      return 'success'
    case PAYMENT_STATUS.PENDING:
      return 'default'
    case PAYMENT_STATUS.PARTIAL:
      return 'warning'
    case PAYMENT_STATUS.OVERDUE:
      return 'error'
    case PAYMENT_STATUS.CANCELLED:
      return 'error'
    default:
      return 'default'
  }
}

// الحصول على أيقونة الحالة
export const getStatusIcon = (status: PaymentStatusType) => {
  switch (status) {
    case PAYMENT_STATUS.PAID:
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />
    case PAYMENT_STATUS.PENDING:
      return <ClockCircleOutlined style={{ color: '#1890ff' }} />
    case PAYMENT_STATUS.PARTIAL:
      return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
    case PAYMENT_STATUS.OVERDUE:
      return <WarningOutlined style={{ color: '#ff4d4f' }} />
    case PAYMENT_STATUS.CANCELLED:
      return <WarningOutlined style={{ color: '#ff4d4f' }} />
    default:
      return <DollarOutlined />
  }
}

// الحصول على نص الحالة
export const getStatusText = (status: PaymentStatusType) => {
  switch (status) {
    case PAYMENT_STATUS.PAID:
      return 'مدفوع بالكامل'
    case PAYMENT_STATUS.PENDING:
      return 'في الانتّار'
    case PAYMENT_STATUS.PARTIAL:
      return 'مدفوع جزئياً'
    case PAYMENT_STATUS.OVERDUE:
      return 'متأخر'
    case PAYMENT_STATUS.CANCELLED:
      return 'ملغي'
    default:
      return status
  }
}

// خصائص مكون عرض المبلغ المتبقي
interface RemainingAmountDisplayProps {
  totalAmount: number
  paidAmount: number
  currency?: string
  dueDate?: string
  showProgress?: boolean
  showDetails?: boolean
  size?: 'small' | 'default' | 'large'
  layout?: 'horizontal' | 'vertical'
}

// مكون عرض المبلغ المتبقي
export const RemainingAmountDisplay: React.FC<RemainingAmountDisplayProps> = ({
  totalAmount,
  paidAmount,
  currency = '₪',
  dueDate,
  showProgress = true,
  showDetails = false,
  size = 'default',
  layout = 'horizontal'
}) => {
  // التأكد من أن القيم رقمية وليست null أو undefined
  const safeTotalAmount = Number(totalAmount) || 0
  const safePaidAmount = Number(paidAmount) || 0

  const remainingAmount = safeTotalAmount - safePaidAmount
  const paymentPercentage = safeTotalAmount > 0 ? (safePaidAmount / safeTotalAmount) * 100 : 0
  const status = calculatePaymentStatus(safeTotalAmount, safePaidAmount, dueDate)

  const formatAmount = (amount: number) => {
    return `${currency} ${amount.toLocaleString()}`
  }

  if (size === 'small') {
    return (
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        <Space>
          {getStatusIcon(status)}
          <Tag color={getStatusColor(status)}>
            {getStatusText(status)}
          </Tag>
        </Space>
        <Text strong>
          المتبقي: {formatAmount(remainingAmount)}
        </Text>
        {showProgress && (
          <Progress
            percent={paymentPercentage}
            size="small"
            status={status === PAYMENT_STATUS.PAID ? 'success' : 'active'}
            format={() => `${paymentPercentage.toFixed(1)}%`}
          />
        )}
      </Space>
    )
  }

  if (!showDetails) {
    return (
      <Space direction={layout === 'vertical' ? 'vertical' : 'horizontal'} size="middle">
        <Space>
          {getStatusIcon(status)}
          <Tag color={getStatusColor(status)} style={{ fontSize: '14px', padding: '4px 8px' }}>
            {getStatusText(status)}
          </Tag>
        </Space>
        <Text strong style={{ fontSize: '16px' }}>
          المتبقي: {formatAmount(remainingAmount)}
        </Text>
        {showProgress && (
          <Progress
            percent={paymentPercentage}
            status={status === PAYMENT_STATUS.PAID ? 'success' : 'active'}
            format={() => `${paymentPercentage.toFixed(1)}%`}
          />
        )}
      </Space>
    )
  }

  return (
    <Card size="small" style={{ width: '100%' }}>
      <Row gutter={16}>
        <Col span={layout === 'vertical' ? 24 : 8}>
          <Statistic
            title="إجمالي المبلغ"
            value={totalAmount}
            prefix={currency}
            valueStyle={{ color: '#1890ff' }}
          />
        </Col>
        <Col span={layout === 'vertical' ? 24 : 8}>
          <Statistic
            title="المبلغ المدفوع"
            value={paidAmount}
            prefix={currency}
            valueStyle={{ color: '#52c41a' }}
          />
        </Col>
        <Col span={layout === 'vertical' ? 24 : 8}>
          <Statistic
            title="المبلغ المتبقي"
            value={remainingAmount}
            prefix={currency}
            valueStyle={{ 
              color: remainingAmount > 0 ? '#fa8c16' : '#52c41a' 
            }}
          />
        </Col>
      </Row>
      
      <div style={{ marginTop: 16 }}>
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <Space>
            {getStatusIcon(status)}
            <Tag color={getStatusColor(status)} style={{ fontSize: '14px', padding: '4px 8px' }}>
              {getStatusText(status)}
            </Tag>
            {dueDate && (
              <Text type="secondary">
                تاريخ الاستحقاق: {new Date(dueDate).toLocaleDateString('ar-EG')}
              </Text>
            )}
          </Space>
          
          {showProgress && (
            <Progress
              percent={paymentPercentage}
              status={status === PAYMENT_STATUS.PAID ? 'success' : 'active'}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
              format={() => `${paymentPercentage.toFixed(1)}% مدفوع`}
            />
          )}
        </Space>
      </div>
    </Card>
  )
}

// مكون مبسط لعرض الحالة فقط
interface PaymentStatusTagProps {
  totalAmount: number
  paidAmount: number
  dueDate?: string
  size?: 'small' | 'default'
}

export const PaymentStatusTag: React.FC<PaymentStatusTagProps> = ({
  totalAmount,
  paidAmount,
  dueDate,
  size = 'default'
}) => {
  // التأكد من أن القيم رقمية وليست null أو undefined
  const safeTotalAmount = Number(totalAmount) || 0
  const safePaidAmount = Number(paidAmount) || 0

  const status = calculatePaymentStatus(safeTotalAmount, safePaidAmount, dueDate)
  
  return (
    <Space>
      {getStatusIcon(status)}
      <Tag 
        color={getStatusColor(status)} 
        style={{ 
          fontSize: size === 'small' ? '12px' : '14px',
          padding: size === 'small' ? '2px 6px' : '4px 8px'
        }}
      >
        {getStatusText(status)}
      </Tag>
    </Space>
  )
}

// مكون عرض ملخص المدفوعات
interface PaymentSummaryProps {
  invoices: Array<{
    totalAmount: number
    paidAmount: number
    dueDate?: string
  }>
  title?: string
}

export const PaymentSummary: React.FC<PaymentSummaryProps> = ({
  invoices,
  title = "ملخص المدفوعات"
}) => {
  const totals = invoices.reduce(
    (acc, invoice) => ({
      total: acc.total + invoice.totalAmount,
      paid: acc.paid + invoice.paidAmount,
      remaining: acc.remaining + (invoice.totalAmount - invoice.paidAmount)
    }),
    { total: 0, paid: 0, remaining: 0 }
  )

  const paidInvoices = invoices.filter(inv => inv.paidAmount >= inv.totalAmount).length
  const partialInvoices = invoices.filter(inv => inv.paidAmount > 0 && inv.paidAmount < inv.totalAmount).length
  const pendingInvoices = invoices.filter(inv => inv.paidAmount === 0).length

  return (
    <Card title={title} size="small">
      <Row gutter={16}>
        <Col span={6}>
          <Statistic
            title="إجمالي الفواتير"
            value={invoices.length}
            valueStyle={{ color: '#1890ff' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="مدفوعة بالكامل"
            value={paidInvoices}
            valueStyle={{ color: '#52c41a' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="مدفوعة جزئياً"
            value={partialInvoices}
            valueStyle={{ color: '#faad14' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="في الانتّار"
            value={pendingInvoices}
            valueStyle={{ color: '#ff4d4f' }}
          />
        </Col>
      </Row>
      
      <div style={{ marginTop: 16 }}>
        <RemainingAmountDisplay
          totalAmount={totals.total}
          paidAmount={totals.paid}
          showDetails={true}
          layout="horizontal"
        />
      </div>
    </Card>
  )
}
