import * as fs from 'fs'
import * as path from 'path'
import { app } from 'electron'
import { Logger } from './logger'

/**
 * نظام ذكي لتحديد مسار ملفات sql.js في جميع بيئات التشغيل
 */
export class SqlJsPathResolver {
  private static instance: SqlJsPathResolver
  private cachedPaths: Map<string, string> = new Map()

  private constructor() {}

  public static getInstance(): SqlJsPathResolver {
    if (!SqlJsPathResolver.instance) {
      SqlJsPathResolver.instance = new SqlJsPathResolver()
    }
    return SqlJsPathResolver.instance
  }

  /**
   * تحديد مسار ملف sql.js بذكاء
   */
  public locateFile(filename: string): string {
    // التحقق من الكاش أولاً
    const cacheKey = filename
    if (this.cachedPaths.has(cacheKey)) {
      const cachedPath = this.cachedPaths.get(cacheKey)!
      if (fs.existsSync(cachedPath)) {
        Logger.info('SqlJsPathResolver', `📋 استخدام المسار المحفوظ: ${cachedPath}`)
        return cachedPath
      } else {
        // إزالة المسار من الكاش إذا لم يعد موجوداً
        this.cachedPaths.delete(cacheKey)
      }
    }

    Logger.info('SqlJsPathResolver', `🔍 البحث عن ملف: ${filename}`)

    // قائمة المسارات المحتملة مرتبة حسب الأولوية
    const possiblePaths = this.generatePossiblePaths(filename)

    // البحث في المسارات
    for (const filePath of possiblePaths) {
      Logger.debug('SqlJsPathResolver', `🔍 فحص المسار: ${filePath}`)
      
      if (fs.existsSync(filePath)) {
        Logger.success('SqlJsPathResolver', `✅ تم العثور على الملف في: ${filePath}`)
        
        // حفظ المسار في الكاش
        this.cachedPaths.set(cacheKey, filePath)
        
        return filePath
      }
    }

    // إذا لم نجد الملف، نحاول إنشاء نسخة احتياطية
    const backupPath = this.createBackupFile(filename)
    if (backupPath && fs.existsSync(backupPath)) {
      Logger.warn('SqlJsPathResolver', `⚠️ استخدام النسخة الاحتياطية: ${backupPath}`)
      this.cachedPaths.set(cacheKey, backupPath)
      return backupPath
    }

    // المسار الافتراضي كحل أخير
    const defaultPath = path.join(process.cwd(), 'node_modules/sql.js/dist/', filename)
    Logger.error('SqlJsPathResolver', `❌ لم يتم العثور على الملف، استخدام المسار الافتراضي: ${defaultPath}`)
    
    return defaultPath
  }

  /**
   * توليد قائمة المسارات المحتملة
   */
  private generatePossiblePaths(filename: string): string[] {
    const paths: string[] = []

    try {
      // 1. مسارات التطوير
      paths.push(
        path.join(process.cwd(), 'node_modules/sql.js/dist/', filename),
        path.join(__dirname, '../../../node_modules/sql.js/dist/', filename),
        path.join(__dirname, '../../node_modules/sql.js/dist/', filename),
        path.join(__dirname, '../node_modules/sql.js/dist/', filename)
      )

      // 2. مسارات النسخة المحمولة والمثبت
      if (process.resourcesPath) {
        paths.push(
          path.join(process.resourcesPath, 'app/node_modules/sql.js/dist/', filename),
          path.join(process.resourcesPath, '../app/node_modules/sql.js/dist/', filename),
          path.join(process.resourcesPath, 'sql.js/dist/', filename),
          path.join(process.resourcesPath, 'sql.js/', filename)
        )
      }

      // 3. مسارات بديلة للنسخة المحمولة
      if (process.execPath) {
        const execDir = path.dirname(process.execPath)
        paths.push(
          path.join(execDir, 'resources/app/node_modules/sql.js/dist/', filename),
          path.join(execDir, 'resources/sql.js/dist/', filename),
          path.join(execDir, 'resources/sql.js/', filename),
          path.join(execDir, 'node_modules/sql.js/dist/', filename),
          path.join(execDir, 'sql.js/dist/', filename),
          path.join(execDir, 'sql.js/', filename)
        )
      }

      // 4. مسارات app.getAppPath()
      try {
        const appPath = app.getAppPath()
        paths.push(
          path.join(appPath, 'node_modules/sql.js/dist/', filename),
          path.join(appPath, '../node_modules/sql.js/dist/', filename),
          path.join(appPath, 'sql.js/dist/', filename),
          path.join(appPath, 'sql.js/', filename)
        )
      } catch (error) {
        Logger.debug('SqlJsPathResolver', 'لا يمكن الحصول على app.getAppPath()')
      }

      // 5. مسارات النسخة الاحتياطية في resources
      if (process.resourcesPath) {
        paths.push(
          path.join(process.resourcesPath, 'sql-backup/', filename),
          path.join(process.resourcesPath, 'backup/', filename)
        )
      }

      // 6. مسارات إضافية للنسخة المحمولة
      const portablePaths = [
        'dist-portable-final/ZET.IA-Final/resources/app/node_modules/sql.js/dist/',
        'release-new/win-unpacked/resources/app/node_modules/sql.js/dist/',
        'dist/main/node_modules/sql.js/dist/'
      ]

      for (const portablePath of portablePaths) {
        paths.push(path.join(process.cwd(), portablePath, filename))
      }

    } catch (error) {
      Logger.warn('SqlJsPathResolver', `تحذير في توليد المسارات: ${error}`)
    }

    // إزالة المسارات المكررة
    return [...new Set(paths)]
  }

  /**
   * إنشاء نسخة احتياطية من الملف
   */
  private createBackupFile(filename: string): string | null {
    try {
      // البحث عن الملف الأصلي
      const originalPath = path.join(process.cwd(), 'node_modules/sql.js/dist/', filename)
      
      if (!fs.existsSync(originalPath)) {
        Logger.warn('SqlJsPathResolver', `الملف الأصلي غير موجود: ${originalPath}`)
        return null
      }

      // تحديد مجلد النسخة الاحتياطية
      let backupDir: string
      
      if (process.resourcesPath) {
        backupDir = path.join(process.resourcesPath, 'sql-backup')
      } else {
        backupDir = path.join(process.cwd(), 'resources', 'sql-backup')
      }

      // إنشاء المجلد إذا لم يكن موجوداً
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true })
      }

      const backupPath = path.join(backupDir, filename)

      // نسخ الملف إذا لم يكن موجوداً
      if (!fs.existsSync(backupPath)) {
        fs.copyFileSync(originalPath, backupPath)
        Logger.success('SqlJsPathResolver', `✅ تم إنشاء نسخة احتياطية: ${backupPath}`)
      }

      return backupPath

    } catch (error) {
      Logger.error('SqlJsPathResolver', `خطأ في إنشاء النسخة الاحتياطية: ${error}`)
      return null
    }
  }

  /**
   * تنظيف الكاش
   */
  public clearCache(): void {
    this.cachedPaths.clear()
    Logger.info('SqlJsPathResolver', '🧹 تم تنظيف كاش المسارات')
  }

  /**
   * الحصول على معلومات الكاش
   */
  public getCacheInfo(): { [key: string]: string } {
    const info: { [key: string]: string } = {}
    this.cachedPaths.forEach((value, key) => {
      info[key] = value
    })
    return info
  }
}
