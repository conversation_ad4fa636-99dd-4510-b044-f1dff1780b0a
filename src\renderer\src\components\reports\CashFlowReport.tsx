import React from 'react'
import UniversalReport from './UniversalReport'
import type { ReportData, ReportType } from '../../types/reports'

const CashFlowReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير التدفق النقدي...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getCashFlow(filters);

      if (!response || !response.success || !response.data) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const cashFlowData = response.data;

      // إعداد الأعمدة
      const columns = [
        {
          key: 'account_code',
          title: 'كود الحساب',
          align: 'center' as const,
          width: '120px'
        },
        {
          key: 'account_name',
          title: 'اسم الحساب',
          align: 'right' as const,
          width: '200px'
        },
        {
          key: 'category',
          title: 'الفئة',
          align: 'center' as const,
          width: '150px'
        },
        {
          key: 'amount',
          title: 'المبلغ',
          align: 'left' as const,
          format: 'currency' as const,
          width: '150px'
        },
        {
          key: 'percentage',
          title: 'النسبة %',
          align: 'center' as const,
          format: 'percentage' as const,
          width: '100px'
        }
      ];

      // تحويل البيانات إلى تنسيق مسطح للجدول
      const tableData: any[] = [];
      
      // الأنشطة التشغيلية
      tableData.push({
        key: 'operating-activities-header',
        account_code: '',
        account_name: 'الأنشطة التشغيلية',
        category: 'عنوان',
        amount: null,
        percentage: null,
        isHeader: true
      });

      // المقبوضات النقدية التشغيلية
      if (cashFlowData.operating_activities?.cash_receipts) {
        const receipts = cashFlowData.operating_activities.cash_receipts;
        
        // مقبوضات المبيعات
        if (receipts.sales_receipts) {
          receipts.sales_receipts.forEach((item: any, index: number) => {
            tableData.push({
              key: `sales-receipts-${index}`,
              account_code: item.account_code,
              account_name: item.account_name,
              category: 'مقبوضات مبيعات',
              amount: item.amount,
              percentage: item.percentage
            });
          });
        }

        // إجمالي المقبوضات
        tableData.push({
          key: 'total-receipts',
          account_code: '',
          account_name: 'إجمالي المقبوضات النقدية',
          category: 'إجمالي',
          amount: receipts.total_receipts || 0,
          percentage: null,
          isTotal: true
        });
      }

      // المدفوعات النقدية التشغيلية
      if (cashFlowData.operating_activities?.cash_payments) {
        const payments = cashFlowData.operating_activities.cash_payments;
        
        // مدفوعات الموردين
        if (payments.supplier_payments) {
          payments.supplier_payments.forEach((item: any, index: number) => {
            tableData.push({
              key: `supplier-payments-${index}`,
              account_code: item.account_code,
              account_name: item.account_name,
              category: 'مدفوعات موردين',
              amount: item.amount,
              percentage: item.percentage
            });
          });
        }

        // إجمالي المدفوعات
        tableData.push({
          key: 'total-payments',
          account_code: '',
          account_name: 'إجمالي المدفوعات النقدية',
          category: 'إجمالي',
          amount: payments.total_payments || 0,
          percentage: null,
          isTotal: true
        });
      }

      // صافي التدفق النقدي من الأنشطة التشغيلية
      tableData.push({
        key: 'net-operating-cash-flow',
        account_code: '',
        account_name: 'صافي التدفق النقدي من الأنشطة التشغيلية',
        category: 'صافي',
        amount: cashFlowData.operating_activities?.net_operating_cash_flow || 0,
        percentage: null,
        isNetOperating: true
      });

      // الأنشطة الاستثمارية
      if (cashFlowData.investing_activities) {
        tableData.push({
          key: 'investing-activities-header',
          account_code: '',
          account_name: 'الأنشطة الاستثمارية',
          category: 'عنوان',
          amount: null,
          percentage: null,
          isHeader: true
        });

        // صافي التدفق النقدي من الأنشطة الاستثمارية
        tableData.push({
          key: 'net-investing-cash-flow',
          account_code: '',
          account_name: 'صافي التدفق النقدي من الأنشطة الاستثمارية',
          category: 'صافي',
          amount: cashFlowData.investing_activities.net_investing_cash_flow || 0,
          percentage: null,
          isNetInvesting: true
        });
      }

      // الأنشطة التمويلية
      if (cashFlowData.financing_activities) {
        tableData.push({
          key: 'financing-activities-header',
          account_code: '',
          account_name: 'الأنشطة التمويلية',
          category: 'عنوان',
          amount: null,
          percentage: null,
          isHeader: true
        });

        // صافي التدفق النقدي من الأنشطة التمويلية
        tableData.push({
          key: 'net-financing-cash-flow',
          account_code: '',
          account_name: 'صافي التدفق النقدي من الأنشطة التمويلية',
          category: 'صافي',
          amount: cashFlowData.financing_activities.net_financing_cash_flow || 0,
          percentage: null,
          isNetFinancing: true
        });
      }

      // صافي التغير في النقدية
      tableData.push({
        key: 'net-change-in-cash',
        account_code: '',
        account_name: 'صافي التغير في النقدية',
        category: 'صافي التغير',
        amount: cashFlowData.net_change_in_cash || 0,
        percentage: null,
        isNetChange: true
      });

      // النقدية في نهاية الفترة
      tableData.push({
        key: 'ending-cash',
        account_code: '',
        account_name: 'النقدية في نهاية الفترة',
        category: 'رصيد نهاية',
        amount: cashFlowData.ending_cash_balance || 0,
        percentage: null,
        isEnding: true
      });

      // إحصائيات سريعة
      const quickStats = [
        {
          title: 'التدفق النقدي التشغيلي',
          value: cashFlowData.operating_activities?.net_operating_cash_flow || 0,
          format: 'currency' as const,
          color: (cashFlowData.operating_activities?.net_operating_cash_flow || 0) >= 0 ? '#52c41a' : '#ff4d4f'
        },
        {
          title: 'التدفق النقدي الاستثماري',
          value: cashFlowData.investing_activities?.net_investing_cash_flow || 0,
          format: 'currency' as const,
          color: (cashFlowData.investing_activities?.net_investing_cash_flow || 0) >= 0 ? '#52c41a' : '#ff4d4f'
        },
        {
          title: 'التدفق النقدي التمويلي',
          value: cashFlowData.financing_activities?.net_financing_cash_flow || 0,
          format: 'currency' as const,
          color: (cashFlowData.financing_activities?.net_financing_cash_flow || 0) >= 0 ? '#52c41a' : '#ff4d4f'
        },
        {
          title: 'صافي التغير في النقدية',
          value: cashFlowData.net_change_in_cash || 0,
          format: 'currency' as const,
          color: (cashFlowData.net_change_in_cash || 0) >= 0 ? '#52c41a' : '#ff4d4f'
        }
      ];

      console.log('✅ تم إنشاء تقرير التدفق النقدي بنجاح');

      return {
        title: 'تقرير التدفق النقدي',
        data: tableData,
        columns,
        summary: {
          totalRecords: tableData.length,
          quickStats
        }
      };

    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير التدفق النقدي:', error);
      throw error;
    }
  };

  // إعدادات التقرير
  const reportConfig = {
    reportType: 'cash-flow' as ReportType,
    title: 'تقرير التدفق النقدي',
    generateReport,
    defaultFilters: {
      dateRange: null, // كل المدة افتراضياً
    },
    enabledFilters: {
      showDateFilter: true,
      showAdvancedSearch: true,
    },
    features: {
      enableExport: true,
      enablePrint: true,
      enableRefresh: true,
      showQuickStats: true,
    }
  };

  return <UniversalReport {...reportConfig} />;
};

export default CashFlowReport;
