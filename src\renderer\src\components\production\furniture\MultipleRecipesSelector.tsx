import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Select,
  InputNumber,
  Input,
  Space,
  message,
  Tooltip,
  Tag,
  Alert,
  Divider,
  Row,
  Col,
  Statistic,
  Typography,
  Popconfirm
} from 'antd'
import {
  PlusOutlined,
  DeleteOutlined,
  Info<PERSON>ircleOutlined,
  Check<PERSON>ircleOutlined,
  Exclamation<PERSON>ircleOutlined,
  CalculatorOutlined
} from '@ant-design/icons'
import { Logger } from '../../../utils/logger'

const { Option } = Select
const { TextArea } = Input
const { Text } = Typography

interface Recipe {
  id: number
  code: string
  name: string
  item_id: number
  item_name: string
  department_id: number
  department_name: string
  estimated_time: number
  estimated_cost: number
  difficulty_level: string
}

interface SelectedRecipe {
  id: string
  recipe_id: number
  recipe_name: string
  item_name: string
  quantity: number
  estimated_cost: number
  estimated_time: number
  notes: string
}

interface MaterialAvailability {
  available: boolean
  total_materials: number
  insufficient_materials: any[]
  materials_summary: any[]
}

interface MultipleRecipesSelectorProps {
  onRecipesChange: (recipes: SelectedRecipe[]) => void
  onMaterialsAvailabilityChange: (availability: MaterialAvailability | null) => void
  departmentId?: number
}

const MultipleRecipesSelector: React.FC<MultipleRecipesSelectorProps> = ({
  onRecipesChange,
  onMaterialsAvailabilityChange,
  departmentId
}) => {
  const [recipes, setRecipes] = useState<Recipe[]>([])
  const [selectedRecipes, setSelectedRecipes] = useState<SelectedRecipe[]>([])
  const [materialsAvailability, setMaterialsAvailability] = useState<MaterialAvailability | null>(null)
  const [loading, setLoading] = useState(false)
  const [checkingMaterials, setCheckingMaterials] = useState(false)

  // تحميل الوصفات
  useEffect(() => {
    loadRecipes()
  }, [departmentId])

  // التحقق من توفر المواد عند تغيير الوصفات
  useEffect(() => {
    if (selectedRecipes.length > 0) {
      checkMaterialsAvailability()
    } else {
      setMaterialsAvailability(null)
      onMaterialsAvailabilityChange(null)
    }
  }, [selectedRecipes])

  // إرسال التغييرات للمكون الأب
  useEffect(() => {
    onRecipesChange(selectedRecipes)
  }, [selectedRecipes])

  const loadRecipes = async () => {
    try {
      setLoading(true)
      if (!window.electronAPI) return

      const result = await window.electronAPI.getProductionRecipes()
      if (result.success) {
        let filteredRecipes = result.data
        
        // تصفية حسب القسم إذا تم تحديده
        if (departmentId) {
          filteredRecipes = result.data.filter((recipe: Recipe) => recipe.department_id === departmentId)
        }
        
        setRecipes(filteredRecipes)
      }
    } catch (error) {
      Logger.error('MultipleRecipesSelector', 'خطأ في تحميل الوصفات:', error)
      message.error('حدث خطأ في تحميل الوصفات')
    } finally {
      setLoading(false)
    }
  }

  const addRecipe = () => {
    const newRecipe: SelectedRecipe = {
      id: Date.now().toString(),
      recipe_id: 0,
      recipe_name: '',
      item_name: '',
      quantity: 1,
      estimated_cost: 0,
      estimated_time: 0,
      notes: ''
    }
    setSelectedRecipes([...selectedRecipes, newRecipe])
  }

  const removeRecipe = (id: string) => {
    setSelectedRecipes(selectedRecipes.filter(recipe => recipe.id !== id))
  }

  const updateRecipe = (id: string, field: keyof SelectedRecipe, value: any) => {
    setSelectedRecipes(selectedRecipes.map(recipe => {
      if (recipe.id === id) {
        const updatedRecipe = { ...recipe, [field]: value }
        
        // إذا تم تغيير الوصفة، نحديث المعلومات
        if (field === 'recipe_id') {
          const selectedRecipeData = recipes.find(r => r.id === value)
          if (selectedRecipeData) {
            updatedRecipe.recipe_name = selectedRecipeData.name
            updatedRecipe.item_name = selectedRecipeData.item_name
            updatedRecipe.estimated_cost = selectedRecipeData.estimated_cost || 0
            updatedRecipe.estimated_time = selectedRecipeData.estimated_time || 0
          }
        }
        
        return updatedRecipe
      }
      return recipe
    }))
  }

  const checkMaterialsAvailability = async () => {
    try {
      setCheckingMaterials(true)
      if (!window.electronAPI) return

      const recipesToCheck = selectedRecipes
        .filter(recipe => recipe.recipe_id > 0 && recipe.quantity > 0)
        .map(recipe => ({
          recipe_id: recipe.recipe_id,
          quantity: recipe.quantity
        }))

      if (recipesToCheck.length === 0) {
        setMaterialsAvailability(null)
        onMaterialsAvailabilityChange(null)
        return
      }

      const result = await window.electronAPI.checkMultipleRecipesMaterialsAvailability(recipesToCheck)
      if (result.success) {
        setMaterialsAvailability(result.data)
        onMaterialsAvailabilityChange(result.data)
      }
    } catch (error) {
      Logger.error('MultipleRecipesSelector', 'خطأ في التحقق من توفر المواد:', error)
      message.error('حدث خطأ في التحقق من توفر المواد')
    } finally {
      setCheckingMaterials(false)
    }
  }

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'easy': return 'green'
      case 'medium': return 'blue'
      case 'hard': return 'orange'
      case 'expert': return 'red'
      default: return 'default'
    }
  }

  const getDifficultyText = (level: string) => {
    switch (level) {
      case 'easy': return 'سهل'
      case 'medium': return 'متوسط'
      case 'hard': return 'صعب'
      case 'expert': return 'خبير'
      default: return level
    }
  }

  // حساب الإحصائيات
  const totalQuantity = selectedRecipes.reduce((sum, recipe) => sum + recipe.quantity, 0)
  const totalCost = selectedRecipes.reduce((sum, recipe) => sum + (recipe.estimated_cost * recipe.quantity), 0)
  const totalTime = selectedRecipes.reduce((sum, recipe) => sum + (recipe.estimated_time * recipe.quantity), 0)

  const columns = [
    {
      title: 'الوصفة',
      key: 'recipe',
      width: 300,
      render: (_: any, record: SelectedRecipe, index: number) => (
        <Select
          placeholder="اختر الوصفة"
          style={{ width: '100%' }}
          value={record.recipe_id || undefined}
          onChange={(value) => updateRecipe(record.id, 'recipe_id', value)}
          showSearch
          optionFilterProp="children"
          loading={loading}
        >
          {recipes
            .filter(recipe => !selectedRecipes.some(sr => sr.recipe_id === recipe.id && sr.id !== record.id))
            .map(recipe => (
              <Option key={recipe.id} value={recipe.id}>
                <div>
                  <Text strong>{recipe.name}</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {recipe.item_name} - {recipe.code}
                  </Text>
                  <br />
                  <Space size="small">
                    <Tag color={getDifficultyColor(recipe.difficulty_level)}>
                      {getDifficultyText(recipe.difficulty_level)}
                    </Tag>
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {recipe.estimated_time}س - ₪{recipe.estimated_cost}
                    </Text>
                  </Space>
                </div>
              </Option>
            ))}
        </Select>
      )
    },
    {
      title: 'الكمية',
      key: 'quantity',
      width: 120,
      render: (_: any, record: SelectedRecipe) => (
        <InputNumber
          min={1}
          max={1000}
          value={record.quantity}
          onChange={(value) => updateRecipe(record.id, 'quantity', value || 1)}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'التكلفة المقدرة',
      key: 'cost',
      width: 120,
      render: (_: any, record: SelectedRecipe) => (
        <Text>₪{(record.estimated_cost * record.quantity).toFixed(2)}</Text>
      )
    },
    {
      title: 'الوقت المقدر',
      key: 'time',
      width: 100,
      render: (_: any, record: SelectedRecipe) => (
        <Text>{(record.estimated_time * record.quantity).toFixed(1)}س</Text>
      )
    },
    {
      title: 'ملاحظات',
      key: 'notes',
      width: 200,
      render: (_: any, record: SelectedRecipe) => (
        <TextArea
          rows={2}
          value={record.notes}
          onChange={(e) => updateRecipe(record.id, 'notes', e.target.value)}
          placeholder="ملاحظات اختيارية..."
          maxLength={200}
        />
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 80,
      render: (_: any, record: SelectedRecipe) => (
        <Popconfirm
          title="هل أنت متأكد من حذف هذه الوصفة؟"
          onConfirm={() => removeRecipe(record.id)}
          okText="نعم"
          cancelText="لا"
        >
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            size="small"
          />
        </Popconfirm>
      )
    }
  ]

  return (
    <Card
      title={
        <Space>
          <CalculatorOutlined />
          وصفات الإنتاج المطلوبة
          <Tag color="blue">{selectedRecipes.length} وصفة</Tag>
        </Space>
      }
      extra={
        <Button
          type="dashed"
          icon={<PlusOutlined />}
          onClick={addRecipe}
          size="small"
        >
          إضافة وصفة
        </Button>
      }
    >
      {/* الإحصائيات */}
      {selectedRecipes.length > 0 && (
        <>
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={6}>
              <Statistic
                title="إجمالي الكمية"
                value={totalQuantity}
                suffix="قطعة"
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="التكلفة المقدرة"
                value={totalCost}
                precision={2}
                prefix="₪"
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="الوقت المقدر"
                value={totalTime}
                precision={1}
                suffix="ساعة"
                valueStyle={{ color: '#fa8c16' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="عدد الوصفات"
                value={selectedRecipes.length}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
          </Row>
          <Divider />
        </>
      )}

      {/* تحذير توفر المواد */}
      {materialsAvailability && !materialsAvailability.available && (
        <Alert
          message="تحذير: نقص في المواد"
          description={
            <div>
              <p>هناك نقص في {materialsAvailability.insufficient_materials.length} مادة من أصل {materialsAvailability.total_materials} مادة مطلوبة:</p>
              <ul style={{ marginBottom: 0 }}>
                {materialsAvailability.insufficient_materials.slice(0, 3).map((material, index) => (
                  <li key={index}>
                    <strong>{material.material_name}</strong>: نقص {material.shortage} {material.unit}
                  </li>
                ))}
                {materialsAvailability.insufficient_materials.length > 3 && (
                  <li>... و {materialsAvailability.insufficient_materials.length - 3} مواد أخرى</li>
                )}
              </ul>
            </div>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* رسالة نجاح توفر المواد */}
      {materialsAvailability && materialsAvailability.available && (
        <Alert
          message="جميع المواد متوفرة"
          description={`تم التحقق من توفر جميع المواد المطلوبة (${materialsAvailability.total_materials} مادة)`}
          type="success"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* جدول الوصفات */}
      <Table
        columns={columns}
        dataSource={selectedRecipes}
        rowKey="id"
        pagination={false}
        size="small"
        locale={{
          emptyText: (
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <InfoCircleOutlined style={{ fontSize: '24px', color: '#d9d9d9', marginBottom: '8px' }} />
              <p style={{ color: '#999' }}>لم يتم إضافة أي وصفات بعد</p>
              <Button type="dashed" icon={<PlusOutlined />} onClick={addRecipe}>
                إضافة وصفة الآن
              </Button>
            </div>
          )
        }}
        loading={checkingMaterials}
      />

      {/* معلومات إضافية */}
      {selectedRecipes.length > 0 && (
        <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6ffed', borderRadius: 6, border: '1px solid #b7eb8f' }}>
          <Space>
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
            <Text style={{ color: '#52c41a' }}>
              تم إضافة {selectedRecipes.length} وصفة بإجمالي {totalQuantity} قطعة
            </Text>
          </Space>
        </div>
      )}
    </Card>
  )
}

export default MultipleRecipesSelector
