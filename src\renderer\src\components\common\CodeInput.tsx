import React from 'react'
import { Input, Button, message } from 'antd'
import { BarcodeOutlined } from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'

interface CodeInputProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  disabled?: boolean
  generateCodeFunction?: () => Promise<{ success: boolean; data?: { code: string }; message?: string }>
  showGenerateButton?: boolean
  prefix?: string
  style?: React.CSSProperties
}

const CodeInput: React.FC<CodeInputProps> = ({
  value,
  onChange,
  placeholder = 'أدخل الكود',
  disabled = false,
  generateCodeFunction,
  showGenerateButton = true,
  prefix,
  style
}) => {
  const handleGenerateCode = async () => {
    if (!generateCodeFunction) {
      message.error('دالة إنشاء الكود غير متوفرة')
      return
    }

    try {
      const response = await generateCodeFunction()
      if (response.success && response.data) {
        onChange?.(response.data.code)
        message.success('تم إنشاء الكود تلقائياً')
      } else {
        message.error(response.message || 'فشل في إنشاء الكود')
      }
    } catch (error) {
      Logger.error('CodeInput', 'خطأ في إنشاء الكود:', error)
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
      message.error(`حدث خطأ أثناء إنشاء الكود: ${errorMessage}`)
    }
  }

  return (
    <Input
      value={value}
      onChange={(e) => onChange?.(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      prefix={prefix}
      style={style}
      addonAfter={
        showGenerateButton && !disabled && generateCodeFunction && (
          <Button
            size="small"
            onClick={handleGenerateCode}
            icon={<BarcodeOutlined />}
            type="link"
          >
            إنشاء تلقائي
          </Button>
        )
      }
    />
  )
}

export default CodeInput
