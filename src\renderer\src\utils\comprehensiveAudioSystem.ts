/**
 * نظام صوتي شامل وفعال
 * يغطي جميع أجزاء البرنامج بطريقة واضحة ومفيدة
 */

import { resourceManager } from './resourceManager'
import { performanceOptimizer } from './performanceOptimizer'
import { SafeLogger as Logger } from './logger'

interface AudioSettings {
  enabled: boolean
  volume: number
  voiceEnabled: boolean
  hoverSounds: boolean
  clickSounds: boolean
  notificationSounds: boolean
  speechRate: number // سرعة الصوت المتحدث
}

interface VoiceContent {
  [key: string]: string
}

class ComprehensiveAudioSystem {
  private audioContext: AudioContext | null = null
  private settings: AudioSettings
  private voiceContent: VoiceContent
  private isInitialized = false
  private eventListeners: Map<string, EventListener> = new Map()
  private mutationObserver: MutationObserver | null = null
  private activeOscillators: Set<OscillatorNode> = new Set()
  private resourceId: string | null = null
  private periodicUpdateInterval: NodeJS.Timeout | null = null

  constructor() {
    this.settings = {
      enabled: true,
      volume: 70,
      voiceEnabled: true,
      hoverSounds: true,
      clickSounds: true,
      notificationSounds: true,
      speechRate: 1.2 // سرعة الصوت الافتراضية
    }

    // تحميل الإعدادات المحفوّة
    this.loadSettings()

    // تسجيل النظام الصوتي في مدير الموارد
    this.resourceId = resourceManager.register({
      name: 'النظام الصوتي الشامل',
      type: 'audio',
      priority: 10,
      cleanup: () => this.cleanup()
    })

    // محتوى الشرح الصوتي لكل عنصر في البرنامج
    this.voiceContent = {
      // القائمة الرئيسية
      'dashboard': 'لوحة التحكم الرئيسية - عرض ملخص شامل لحالة النشاط',
      'inventory': 'إدارة المخزون - إدارة الأصناف والمستودعات والجرد',
      'sales': 'إدارة المبيعات - العملاء وأوامر البيع والفواتير',
      'purchases': 'إدارة المشتريات - الموردين وأوامر الشراء والاستلام',
      'production': 'إدارة الإنتاج - الدورة المستندية الإنتاجية الكاملة',
      'finance': 'الإدارة المالية - الحسابات والقيود والتقارير المالية',
      'employees': 'إدارة الموظفين - بيانات الموظفين والحضور والرواتب',
      'reports': 'التقارير - تقارير شاملة لجميع أقسام البرنامج',
      'settings': 'الإعدادات - إعدادات النظام والمستخدمين والصلاحيات',

      // أزرار العمليات
      'add-button': 'إضافة عنصر جديد',
      'edit-button': 'تعديل العنصر المحدد',
      'delete-button': 'حذف العنصر المحدد - تأكد من صحة العملية',
      'save-button': 'حفظ التغييرات',
      'cancel-button': 'إلغاء العملية والعودة',
      'search-button': 'البحث في البيانات',
      'filter-button': 'تصفية النتائج',
      'export-button': 'تصدير البيانات',
      'print-button': 'طباعة التقرير',
      'refresh-button': 'تحديث البيانات',

      // المخزون
      'categories': 'فئات الأصناف - تصنيف الأصناف حسب النوع',
      'items': 'الأصناف - قائمة جميع الأصناف والمنتجات',
      'warehouses': 'المستودعات - إدارة أماكن التخزين',
      'inventory-movements': 'حركات المخزون - تتبع دخول وخروج الأصناف',
      'stock-adjustment': 'تسوية المخزون - تصحيح الكميات',
      'barcode-scanner': 'قارئ الباركود - مسح الأصناف بسرعة',

      // المبيعات
      'customers': 'العملاء - إدارة بيانات العملاء',
      'sales-orders': 'أوامر البيع - طلبات العملاء',
      'sales-invoices': 'فواتير البيع - الفواتير المحررة',
      'quotations': 'عروض الأسعار - عروض للعملاء',
      'delivery-notes': 'إذن التسليم - تسليم البضائع',

      // المشتريات
      'suppliers': 'الموردين - إدارة بيانات الموردين',
      'purchase-orders': 'أوامر الشراء - طلبات الشراء من الموردين',
      'purchase-invoices': 'فواتير الشراء - فواتير الموردين',
      'receiving-notes': 'إذن الاستلام - استلام البضائع',

      // الإنتاج
      'production-orders': 'أوامر الإنتاج - تخطيط وتنفيذ الإنتاج',
      'bom': 'قائمة المواد - مكونات المنتج النهائي',
      'work-centers': 'مراكز العمل - محطات الإنتاج',
      'quality-control': 'مراقبة الجودة - فحص المنتجات',
      'production-reports': 'تقارير الإنتاج - تحليل الأداء الإنتاجي',

      // المالية
      'chart-of-accounts': 'شجرة الحسابات - هيكل الحسابات المحاسبية',
      'journal-entries': 'القيود اليومية - تسجيل العمليات المحاسبية',
      'trial-balance': 'ميزان المراجعة - التحقق من توازن الحسابات',
      'income-statement': 'قائمة الدخل - الإيرادات والمصروفات',
      'balance-sheet': 'الميزانية العمومية - الأصول والخصوم',

      // الموظفين
      'employee-list': 'قائمة الموظفين - بيانات جميع الموظفين',
      'attendance': 'الحضور والانصراف - تسجيل أوقات العمل',
      'payroll': 'الرواتب - حساب وصرف الرواتب',
      'leaves': 'الإجازات - إدارة إجازات الموظفين',
      'performance': 'تقييم الأداء - تقييم أداء الموظفين',

      // الإعدادات
      'system-settings': 'إعدادات النظام - تخصيص البرنامج حسب احتياجاتك',
      'theme-settings': 'إعدادات الثيم - تغيير مظهر البرنامج والألوان',
      'company-settings': 'إعدادات الشركة - معلومات الشركة الأساسية',
      'user-settings': 'إعدادات المستخدم - تخصيص تجربة المستخدم',
      'backup-settings': 'إعدادات النسخ الاحتياطي - حماية البيانات',
      'notification-settings': 'إعدادات الإشعارات - تخصيص التنبيهات',
      'audio-settings': 'إعدادات الصوت - تحكم في الأصوات والشرح الصوتي',

      // أزرار الإعدادات
      'save-settings': 'حفظ الإعدادات - تطبيق التغييرات',
      'reset-settings': 'إعادة تعيين الإعدادات - العودة للقيم الافتراضية',
      'export-settings': 'تصدير الإعدادات - حفظ الإعدادات في ملف',
      'import-settings': 'استيراد الإعدادات - تحميل إعدادات من ملف',

      // عناصر التحكم
      'sidebar-toggle': 'طي أو فتح القائمة الجانبية',
      'main-menu': 'القائمة الرئيسية - التنقل بين أقسام البرنامج',
      'user-menu': 'قائمة المستخدم - الملف الشخصي والخروج',
      'notifications-menu': 'قائمة الإشعارات - التنبيهات والرسائل',

      // أزرار النماذج
      'form-submit': 'إرسال النموذج - حفظ البيانات المدخلة',
      'form-reset': 'إعادة تعيين النموذج - مسح جميع الحقول',
      'form-cancel': 'إلغاء النموذج - العودة بدون حفظ'
    }

    this.loadSettings()
    this.initialize()
  }

  private async initialize() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      this.isInitialized = true
      Logger.info('ComprehensiveAudioSystem', '✅ تم تهيئة النظام الصوتي الشامل بنجاح')
    } catch (error) {
      Logger.error('ComprehensiveAudioSystem', '❌ خطأ في تهيئة النظام الصوتي:', error as Error)
    }
  }

  // تنظيف شامل للموارد
  async cleanup() {
    Logger.info('ComprehensiveAudioSystem', '🧹 بدء تنظيف موارد النظام الصوتي...')

    try {
      // إيقاف جميع oscillators النشطة
      this.activeOscillators.forEach(oscillator => {
        try {
          oscillator.stop()
          oscillator.disconnect()
        } catch (error) {
          // تجاهل الأخطاء إذا كان oscillator متوقف بالفعل
        }
      })
      this.activeOscillators.clear()

      // إزالة جميع مستمعات الأحداث
      this.eventListeners.forEach((listener, eventType) => {
        document.removeEventListener(eventType.split(':')[0], listener, true)
      })
      this.eventListeners.clear()

      // إيقاف مراقب DOM
      if (this.mutationObserver) {
        this.mutationObserver.disconnect()
        this.mutationObserver = null
      }

      // إغلاق AudioContext
      if (this.audioContext && this.audioContext.state !== 'closed') {
        await this.audioContext.close()
        this.audioContext = null
      }

      this.isInitialized = false

      // إلغاء تسجيل المورد من مدير الموارد
      if (this.resourceId) {
        resourceManager.unregister(this.resourceId)
        this.resourceId = null
      }

      Logger.info('ComprehensiveAudioSystem', '✅ تم تنظيف موارد النظام الصوتي بنجاح')
    } catch (error) {
      Logger.error('ComprehensiveAudioSystem', '❌ خطأ في تنظيف موارد النظام الصوتي:', error as Error)
    }
  }

  // تشغيل صوت بسيط
  playSound(type: 'click' | 'hover' | 'success' | 'error' | 'warning' | 'notification') {
    if (!this.settings.enabled || !this.isInitialized || !this.audioContext) return

    try {
      const frequencies = {
        click: 800,
        hover: 600,
        success: 523.25, // C5
        error: 220,      // A3
        warning: 440,    // A4
        notification: 659.25 // E5
      }

      const frequency = frequencies[type]
      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(this.audioContext.destination)

      oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime)
      oscillator.type = 'sine'

      const volume = this.settings.volume / 100
      gainNode.gain.setValueAtTime(0, this.audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(volume * 0.1, this.audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.3)

      // إضافة oscillator إلى المجموعة النشطة
      this.activeOscillators.add(oscillator)

      // إزالة oscillator من المجموعة عند انتهائه
      oscillator.onended = () => {
        this.activeOscillators.delete(oscillator)
        oscillator.disconnect()
        gainNode.disconnect()
      }

      oscillator.start(this.audioContext.currentTime)
      oscillator.stop(this.audioContext.currentTime + 0.3)
    } catch (error) {
      Logger.warn('ComprehensiveAudioSystem', 'خطأ في تشغيل الصوت ${type}:', error)
    }
  }

  // تشغيل شرح صوتي
  speakText(elementId: string, customText?: string) {
    if (!this.settings.enabled || !this.settings.voiceEnabled) return

    try {
      const text = customText || this.voiceContent[elementId] || 'عنصر غير محدد'

      if ('speechSynthesis' in window) {
        // إيقاف أي كلام سابق
        speechSynthesis.cancel()

        const utterance = new SpeechSynthesisUtterance(text)
        utterance.lang = 'ar-SA'
        utterance.rate = this.settings.speechRate // استخدام سرعة الصوت من الإعدادات
        utterance.pitch = 1
        utterance.volume = this.settings.volume / 100

        speechSynthesis.speak(utterance)
      }
    } catch (error) {
      Logger.warn('ComprehensiveAudioSystem', 'خطأ في الشرح الصوتي لـ ${elementId}:', error)
    }
  }

  // ربط الأصوات بالعناصر تلقائياً
  attachToElements() {
    if (!this.isInitialized) return

    try {
      // إضافة معرفات صوتية للعناصر أولاً
      this.addAudioIdsToElements()
      // ربط أصوات النقر العامة مع debouncing
      if (this.settings.clickSounds) {
        const clickHandler = performanceOptimizer.debounce((event: Event) => {
          try {
            const target = event.target as HTMLElement
            if (!target) return

            // التحقق من أنواع العناصر المختلفة
            if (target.tagName === 'BUTTON' ||
                target.closest('button') ||
                target.classList.contains('ant-btn') ||
                target.classList.contains('ant-menu-item') ||
                target.classList.contains('ant-tabs-tab') ||
                target.classList.contains('ant-select-selector') ||
                target.classList.contains('ant-switch') ||
                target.classList.contains('ant-checkbox') ||
                target.classList.contains('ant-radio') ||
                target.closest('.ant-btn') ||
                target.closest('.ant-menu-item') ||
                target.closest('.ant-tabs-tab') ||
                target.closest('.ant-select') ||
                target.closest('.ant-switch') ||
                target.closest('.ant-checkbox') ||
                target.closest('.ant-radio')) {
              this.playSound('click')
            }
          } catch (error) {
            Logger.warn('ComprehensiveAudioSystem', 'خطأ في معالجة صوت النقر:', error)
          }
        }, { delay: 50, immediate: true }, 'audio-click-handler')

        document.addEventListener('click', clickHandler, true)
        this.eventListeners.set('click:global', clickHandler)
      }

      // إضافة أصوات للعناصر الموجودة
      this.attachToNewElements()

      // بدء مراقبة DOM للعناصر الجديدة
      this.observeDOM()

      // ربط أصوات التمرير مع الشرح الصوتي مع throttling
      if (this.settings.hoverSounds) {
        const hoverHandler = performanceOptimizer.throttle((event: Event) => {
          try {
            const target = event.target as HTMLElement
            if (!target) return

            const elementId = target.id || (target.dataset && target.dataset.audioId)

            if (elementId && this.voiceContent[elementId]) {
              // تشغيل صوت التمرير
              this.playSound('hover')

              // تأخير بسيط ثم تشغيل الشرح الصوتي
              setTimeout(() => {
                this.speakText(elementId)
              }, 200)
            }
          } catch (error) {
            Logger.warn('ComprehensiveAudioSystem', 'خطأ في معالجة صوت التمرير:', error)
          }
        }, { delay: 200, leading: true, trailing: false }, 'audio-hover-handler')

        document.addEventListener('mouseenter', hoverHandler, true)
        this.eventListeners.set('mouseenter:global', hoverHandler)
      }
    } catch (error) {
      Logger.error('ComprehensiveAudioSystem', 'خطأ في ربط الأصوات بالعناصر:', error as Error)
    }
  }

  // تحديث الإعدادات
  updateSettings(newSettings: Partial<AudioSettings>) {
    this.settings = { ...this.settings, ...newSettings }
    this.saveSettings()

    if (newSettings.enabled === false) {
      speechSynthesis.cancel()
    }

    Logger.info('ComprehensiveAudioSystem', '✅ تم تحديث إعدادات النظام الصوتي:', this.settings)
  }

  // تحديث سرعة الصوت المتحدث
  updateSpeechRate(rate: number) {
    // التأكد من أن السرعة ضمن النطاق المسموح (0.1 - 2.0)
    const clampedRate = Math.max(0.1, Math.min(2.0, rate))
    this.updateSettings({ speechRate: clampedRate })
    Logger.info('ComprehensiveAudioSystem', '🔊 تم تحديث سرعة الصوت إلى: ${clampedRate}')
  }

  // الحصول على الإعدادات
  getSettings(): AudioSettings {
    return { ...this.settings }
  }

  // حفظ الإعدادات
  private saveSettings() {
    try {
      localStorage.setItem('comprehensiveAudioSettings', JSON.stringify(this.settings))
    } catch (error) {
      Logger.error('ComprehensiveAudioSystem', 'خطأ في حفظ إعدادات الصوت:', error as Error)
    }
  }

  // تحميل الإعدادات
  private loadSettings() {
    try {
      const saved = localStorage.getItem('comprehensiveAudioSettings')
      if (saved) {
        this.settings = { ...this.settings, ...JSON.parse(saved) }
      }
    } catch (error) {
      Logger.error('ComprehensiveAudioSystem', 'خطأ في تحميل إعدادات الصوت:', error as Error)
    }
  }

  // إضافة محتوى صوتي جديد
  addVoiceContent(elementId: string, text: string) {
    this.voiceContent[elementId] = text
  }

  // إضافة معرفات صوتية للعناصر الموجودة
  addAudioIdsToElements() {
    try {
      // إضافة معرفات للقوائم الرئيسية
      const menuItems = document.querySelectorAll('.ant-menu-item')
      menuItems.forEach((item) => {
        if (!item.id && !item.getAttribute('data-audio-id')) {
          const text = item.textContent?.trim()
          if (text) {
            const audioId = text.toLowerCase().replace(/\s+/g, '_')
            item.setAttribute('data-audio-id', audioId)

            // إضافة محتوى صوتي إذا لم يكن موجود
            if (!this.voiceContent[audioId]) {
              this.voiceContent[audioId] = `قائمة ${text}`
            }
          }
        }
      })

      // إضافة معرفات للأزرار
      const buttons = document.querySelectorAll('button, .ant-btn')
      buttons.forEach((button) => {
        if (!button.id && !button.getAttribute('data-audio-id')) {
          const text = button.textContent?.trim()
          if (text && text.length > 0 && text.length < 50) {
            const audioId = `btn_${text.toLowerCase().replace(/\s+/g, '_')}`
            button.setAttribute('data-audio-id', audioId)

            if (!this.voiceContent[audioId]) {
              this.voiceContent[audioId] = `زر ${text}`
            }
          }
        }
      })

      // إضافة معرفات للبطاقات
      const cards = document.querySelectorAll('.ant-card')
      cards.forEach((card) => {
        if (!card.id && !card.getAttribute('data-audio-id')) {
          const title = card.querySelector('.ant-card-head-title')?.textContent?.trim()
          if (title) {
            const audioId = `card_${title.toLowerCase().replace(/\s+/g, '_')}`
            card.setAttribute('data-audio-id', audioId)

            if (!this.voiceContent[audioId]) {
              this.voiceContent[audioId] = `بطاقة ${title}`
            }
          }
        }
      })

      Logger.info('ComprehensiveAudioSystem', '✅ تم إضافة معرفات صوتية للعناصر')
    } catch (error) {
      Logger.error('ComprehensiveAudioSystem', '❌ خطأ في إضافة معرفات صوتية:', error)
    }
  }

  // إضافة أصوات للعناصر الجديدة مع تحسين الأداء
  attachToNewElements(container?: HTMLElement) {
    const targetContainer = container || document

    // تجميع جميع العمليات في batch واحد
    const updates: (() => void)[] = []

    // البحث عن الأزرار الجديدة باستخدام DOM queries محسنة
    const buttonSelectors = ['button:not([data-audio-attached])', '.ant-btn:not([data-audio-attached])']
    buttonSelectors.forEach(selector => {
      const elements = container ?
        Array.from(targetContainer.querySelectorAll(selector)) :
        performanceOptimizer.optimizedQuerySelectorAll(selector, false)

      elements.forEach(button => {
        updates.push(() => {
          button.setAttribute('data-audio-attached', 'true')
          const clickHandler = performanceOptimizer.debounce(() => {
            if (this.settings.clickSounds) {
              this.playSound('click')
            }
          }, { delay: 50, immediate: true }, `button-${button.id || 'anonymous'}`)

          button.addEventListener('click', clickHandler)
        })
      })
    })

    // البحث عن عناصر القائمة الجديدة
    const menuSelectors = ['.ant-menu-item:not([data-audio-attached])', '.ant-tabs-tab:not([data-audio-attached])']
    menuSelectors.forEach(selector => {
      const elements = container ?
        Array.from(targetContainer.querySelectorAll(selector)) :
        performanceOptimizer.optimizedQuerySelectorAll(selector, false)

      elements.forEach(item => {
        updates.push(() => {
          item.setAttribute('data-audio-attached', 'true')
          const clickHandler = performanceOptimizer.debounce(() => {
            if (this.settings.clickSounds) {
              this.playSound('click')
            }
          }, { delay: 50, immediate: true }, `menu-${item.id || 'anonymous'}`)

          item.addEventListener('click', clickHandler)
        })
      })
    })

    // البحث عن عناصر النماذج الجديدة
    const formSelectors = [
      '.ant-select:not([data-audio-attached])',
      '.ant-switch:not([data-audio-attached])',
      '.ant-checkbox:not([data-audio-attached])',
      '.ant-radio:not([data-audio-attached])'
    ]
    formSelectors.forEach(selector => {
      const elements = container ?
        Array.from(targetContainer.querySelectorAll(selector)) :
        performanceOptimizer.optimizedQuerySelectorAll(selector, false)

      elements.forEach(element => {
        updates.push(() => {
          element.setAttribute('data-audio-attached', 'true')
          const clickHandler = performanceOptimizer.debounce(() => {
            if (this.settings.clickSounds) {
              this.playSound('click')
            }
          }, { delay: 50, immediate: true }, `form-${element.id || 'anonymous'}`)

          element.addEventListener('click', clickHandler)
        })
      })
    })

    // تنفيذ جميع التحديثات في batch واحد
    if (updates.length > 0) {
      performanceOptimizer.batchDOMUpdates(updates)
    }
  }

  // مراقبة التغييرات في DOM وإضافة الأصوات للعناصر الجديدة
  observeDOM() {
    if (typeof MutationObserver !== 'undefined' && !this.mutationObserver) {
      this.mutationObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                // تأخير بسيط للسماح للعناصر بالتحميل الكامل
                setTimeout(() => {
                  this.attachToNewElements(node as HTMLElement)
                  this.addAudioIdsToElements()
                }, 100)
              }
            })
          }
        })
      })

      this.mutationObserver.observe(document.body, {
        childList: true,
        subtree: true
      })

      Logger.info('ComprehensiveAudioSystem', '✅ تم تفعيل مراقبة DOM للنظام الصوتي')
    }

    // إضافة تحديث دوري للتأكد من ربط جميع العناصر
    this.startPeriodicUpdate()
  }

  // تحديث دوري للنظام الصوتي
  private startPeriodicUpdate() {
    if (this.periodicUpdateInterval) {
      clearInterval(this.periodicUpdateInterval)
    }

    this.periodicUpdateInterval = setInterval(() => {
      try {
        // إضافة معرفات صوتية للعناصر الجديدة
        this.addAudioIdsToElements()

        // ربط الأصوات للعناصر الجديدة
        this.attachToNewElements()

        Logger.debug('ComprehensiveAudioSystem', '🔄 تم تحديث النظام الصوتي دورياً')
      } catch (error) {
        Logger.warn('ComprehensiveAudioSystem', 'خطأ في التحديث الدوري:', error)
      }
    }, 3000) // كل 3 ثوانٍ
  }

  // اختبار النظام الصوتي
  testSystem() {
    Logger.info('ComprehensiveAudioSystem', '🔊 اختبار النظام الصوتي الشامل...')
    
    // اختبار الأصوات
    const sounds = ['click', 'hover', 'success', 'error', 'warning', 'notification'] as const
    sounds.forEach((sound, index) => {
      setTimeout(() => {
        this.playSound(sound)
        Logger.info('ComprehensiveAudioSystem', '✅ تم اختبار صوت: ${sound}')
      }, index * 500)
    })

    // اختبار الشرح الصوتي
    setTimeout(() => {
      this.speakText('dashboard')
      Logger.info('ComprehensiveAudioSystem', '✅ تم اختبار الشرح الصوتي')
    }, sounds.length * 500 + 1000)
  }
}

// إنشاء مثيل واحد للنظام
export const comprehensiveAudioSystem = new ComprehensiveAudioSystem()

// إتاحة النظام عالمياً - التعريف موجود في global.d.ts

if (typeof window !== 'undefined') {
  window.comprehensiveAudioSystem = comprehensiveAudioSystem

  // تنظيف تلقائي عند إغلاق النافذة
  window.addEventListener('beforeunload', () => {
    comprehensiveAudioSystem.cleanup()
  })

  window.addEventListener('pagehide', () => {
    comprehensiveAudioSystem.cleanup()
  })
}

export default ComprehensiveAudioSystem
