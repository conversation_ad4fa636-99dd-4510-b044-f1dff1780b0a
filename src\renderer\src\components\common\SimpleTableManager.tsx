import React, { useState } from 'react'
import { 
  Button, 
  Tooltip
} from 'antd'
import {
  SettingOutlined
} from '@ant-design/icons'
import ImprovedColumnEditor, { ColumnConfig } from './ImprovedColumnEditor'

interface SimpleTableManagerProps {
  columns: ColumnConfig[]
  data: any[]
  onColumnsChange: (columns: ColumnConfig[]) => void
  title?: string
  buttonText?: string
  buttonSize?: 'small' | 'middle' | 'large'
  buttonType?: 'default' | 'primary' | 'dashed' | 'link' | 'text'
}

const SimpleTableManager: React.FC<SimpleTableManagerProps> = ({
  columns,
  data: _data,
  onColumnsChange,
  title = 'إدارة أعمدة الجدول',
  buttonText = 'إدارة الأعمدة',
  buttonSize = 'middle',
  buttonType = 'default'
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false)

  return (
    <>
      <Tooltip title="إدارة وتخصيص أعمدة الجدول - واجهة محسنة وسهلة الاستخدام">
        <Button
          icon={<SettingOutlined />}
          onClick={() => setIsModalVisible(true)}
          size={buttonSize}
          type={buttonType}
        >
          {buttonText}
        </Button>
      </Tooltip>

      <ImprovedColumnEditor
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        columns={columns}
        onColumnsChange={onColumnsChange}
        title={title}
      />
    </>
  )
}

export default SimpleTableManager
export type { ColumnConfig }
