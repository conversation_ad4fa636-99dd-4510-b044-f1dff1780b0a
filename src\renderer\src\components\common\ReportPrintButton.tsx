import React from 'react'
import UnifiedPrintButton from './UnifiedPrintButton'
import { PrintData } from '../../types/print'

interface ReportPrintButtonProps {
  // بيانات التقرير
  reportData: {
    id?: string | number
    title: string
    subtitle?: string
    reportDate: string
    period?: string
    generatedBy?: string
    data: Array<{
      [key: string]: any
    }>
    summary?: {
      totalRecords?: number
      totalAmount?: number
      averageAmount?: number
      [key: string]: any
    }
    notes?: string
  }
  
  // نوع التقرير
  reportType?: 'sales' | 'financial' | 'inventory' | 'customers' | 'general'
  
  // إعدادات العرض
  columns?: Array<{
    key: string
    title: string
    width?: string
    align?: 'left' | 'center' | 'right'
    format?: 'currency' | 'number' | 'date' | 'text'
  }>
  
  // إعدادات المظهر
  buttonText?: string
  size?: 'small' | 'middle' | 'large'
  disabled?: boolean
  showDropdown?: boolean
  
  // callbacks
  onPrintSuccess?: () => void
  onPrintError?: (error: string) => void
}

const ReportPrintButton: React.FC<ReportPrintButtonProps> = ({
  reportData,
  reportType = 'general',
  columns,
  buttonText,
  size = 'middle',
  disabled = false,
  showDropdown = true,
  onPrintSuccess,
  onPrintError
}) => {
  // تحويل بيانات التقرير إلى تنسيق PrintData
  const printData: PrintData = {
    id: reportData.id || `report_${Date.now()}`,
    title: reportData.title,
    subtitle: reportData.subtitle,
    date: reportData.reportDate,
    notes: reportData.notes,
    // تحويل بيانات التقرير إلى عناصر للطباعة
    items: reportData.data.map((row, index) => ({
      id: index + 1,
      name: getRowDisplayName(row, columns),
      description: getRowDescription(row, columns),
      quantity: 1,
      unit: 'سجل',
      unitPrice: getRowAmount(row),
      total: getRowAmount(row)
    })),
    // إضافة الملخص
    totals: { total: reportData.summary?.totalAmount },
    // معلومات إضافية
    generatedBy: reportData.generatedBy,
    period: reportData.period
  }

  // الحصول على اسم العرض للصف
  function getRowDisplayName(row: any, cols?: typeof columns): string {
    if (cols && cols.length > 0) {
      const firstCol = cols[0]
      return row[firstCol.key] || 'غير محدد'
    }
    
    // البحث عن حقل مناسب للاسم
    const nameFields = ['name', 'title', 'description', 'customer_name', 'product_name']
    for (const field of nameFields) {
      if (row[field]) return row[field]
    }
    
    return Object.values(row)[0] as string || 'غير محدد'
  }

  // الحصول على وصف الصف
  function getRowDescription(row: any, cols?: typeof columns): string {
    if (!cols) return ''
    
    const descriptions: string[] = []
    cols.slice(1, 4).forEach(col => { // أخذ أول 3 أعمدة إضافية
      if (row[col.key] !== undefined) {
        descriptions.push(`${col.title}: ${formatValue(row[col.key], col.format)}`)
      }
    })
    
    return descriptions.join(' | ')
  }

  // الحصول على المبلغ من الصف
  function getRowAmount(row: any): number {
    const amountFields = ['amount', 'total', 'value', 'price', 'cost', 'balance']
    for (const field of amountFields) {
      if (typeof row[field] === 'number') return row[field]
    }
    return 0
  }

  // تنسيق القيم حسب النوع
  function formatValue(value: any, format?: string): string {
    if (value === null || value === undefined) return '-'
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('ar-SA', {
          style: 'currency',
          currency: 'SAR'
        }).format(Number(value))
      
      case 'number':
        return new Intl.NumberFormat('ar-SA').format(Number(value))
      
      case 'date':
        return new Date(value).toLocaleDateString('ar-SA')
      
      default:
        return String(value)
    }
  }

  // ألوان التقارير حسب النوع
  const reportColors = {
    sales: '#52c41a',
    financial: '#1890ff',
    inventory: '#fa8c16',
    customers: '#722ed1',
    general: '#13c2c2'
  }

  // إعدادات مخصصة للتقارير
  const customSettings = {
    showLogo: true,
    showHeader: true,
    showFooter: true,
    showSignature: false,
    showTerms: false,
    pageSize: 'A4' as const,
    orientation: 'landscape' as const, // أفقي للتقارير عادة
    fontSize: 10, // خط أصغر للتقارير
    primaryColor: reportColors[reportType]
  }

  return (
    <UnifiedPrintButton
      data={printData}
      type="report"
      subType={reportType === 'general' ? 'financial' : reportType as any}
      title={`طباعة ${reportData.title}`}
      buttonText={buttonText || `طباعة التقرير`}
      size={size}
      disabled={disabled}
      showDropdown={showDropdown}
      showExportOptions={true}
      showSettings={true}
      _documentId={`report_${reportData.id || Date.now()}`}
      customSettings={customSettings}
      onAfterPrint={onPrintSuccess}
      onError={onPrintError}
    />
  )
}

export default ReportPrintButton
