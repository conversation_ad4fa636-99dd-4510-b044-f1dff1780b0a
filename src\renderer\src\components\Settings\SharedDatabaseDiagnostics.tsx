import React, { useState, useEffect } from 'react'
import {
  Card,
  Button,
  Space,
  Alert,
  Descriptions,
  Tag,
  Progress,
  message,
  Modal,
  Typography,
  Divider,
  Steps,
  List
} from 'antd'
import {
  DatabaseOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  ToolOutlined,
  InfoCircleOutlined,
  SyncOutlined
} from '@ant-design/icons'

const { Title, Text, Paragraph } = Typography
const { Step } = Steps

interface DatabaseStatus {
  currentPath: string
  isShared: boolean
  type: 'shared' | 'local'
  exists: boolean
  size: number
  sizeKB: number
  lastModified: string | null
  isConnected: boolean
  tableCount: number
  recordCount: number
  isEmpty: boolean
  isNetworkPath: boolean
}

interface DiagnosticResult {
  step: string
  status: 'success' | 'warning' | 'error'
  message: string
  details?: string
}

const SharedDatabaseDiagnostics: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [databaseStatus, setDatabaseStatus] = useState<DatabaseStatus | null>(null)
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([])
  const [showDetails, setShowDetails] = useState(false)

  // تحميل حالة قاعدة البيانات
  const loadDatabaseStatus = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.invoke('get-database-status')
      if (response.success) {
        setDatabaseStatus(response.data)
        await runDiagnostics(response.data)
      } else {
        message.error(response.message || 'فشل في تحميل حالة قاعدة البيانات')
      }
    } catch (error) {
      message.error('خطأ في تحميل حالة قاعدة البيانات')
    } finally {
      setLoading(false)
    }
  }

  // تشغيل التشخيص الشامل
  const runDiagnostics = async (status: DatabaseStatus) => {
    const results: DiagnosticResult[] = []

    // 1. فحص وجود قاعدة البيانات
    results.push({
      step: 'فحص وجود قاعدة البيانات',
      status: status.exists ? 'success' : 'error',
      message: status.exists ? 'قاعدة البيانات موجودة' : 'قاعدة البيانات غير موجودة',
      details: `المسار: ${status.currentPath}`
    })

    // 2. فحص نوع قاعدة البيانات
    results.push({
      step: 'فحص نوع قاعدة البيانات',
      status: status.isShared ? 'success' : 'warning',
      message: status.isShared ? 'قاعدة بيانات مشتركة' : 'قاعدة بيانات محلية',
      details: status.isNetworkPath ? 'مسار شبكة' : 'مسار محلي'
    })

    // 3. فحص الاتصال
    results.push({
      step: 'فحص الاتصال بقاعدة البيانات',
      status: status.isConnected ? 'success' : 'error',
      message: status.isConnected ? 'متصل بنجاح' : 'غير متصل',
      details: status.isConnected ? 'الاتصال نشط' : 'فشل في الاتصال'
    })

    // 4. فحص حجم قاعدة البيانات
    if (status.exists) {
      results.push({
        step: 'فحص حجم قاعدة البيانات',
        status: status.size > 0 ? 'success' : 'error',
        message: status.size > 0 ? `${status.sizeKB} KB` : 'قاعدة البيانات فارغة',
        details: `الحجم بالبايت: ${status.size}`
      })
    }

    // 5. فحص الجداول
    results.push({
      step: 'فحص الجداول',
      status: status.tableCount > 0 ? 'success' : 'error',
      message: `${status.tableCount} جدول`,
      details: status.tableCount > 0 ? 'الجداول موجودة' : 'لا توجد جداول'
    })

    // 6. فحص البيانات
    results.push({
      step: 'فحص البيانات',
      status: status.recordCount > 0 ? 'success' : 'warning',
      message: `${status.recordCount} سجل`,
      details: status.isEmpty ? 'قاعدة البيانات فارغة من البيانات' : 'تحتوي على بيانات'
    })

    setDiagnostics(results)
  }

  // إصلاح تلقائي للمشاكل
  const autoFix = async () => {
    setLoading(true)
    try {
      // محاولة إعادة تحميل قاعدة البيانات
      message.info('جاري إعادة تحميل قاعدة البيانات...')
      
      // إعادة تحميل الصفحة لإعادة تهيئة قاعدة البيانات
      setTimeout(() => {
        window.location.reload()
      }, 2000)
      
    } catch (error) {
      message.error('فشل في الإصلاح التلقائي')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadDatabaseStatus()
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'warning': return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
      case 'error': return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
      default: return <InfoCircleOutlined />
    }
  }

  const getOverallStatus = () => {
    if (!diagnostics.length) return 'info'
    
    const hasErrors = diagnostics.some(d => d.status === 'error')
    const hasWarnings = diagnostics.some(d => d.status === 'warning')
    
    if (hasErrors) return 'error'
    if (hasWarnings) return 'warning'
    return 'success'
  }

  const getOverallMessage = () => {
    const status = getOverallStatus()
    switch (status) {
      case 'success': return 'قاعدة البيانات تعمل بشكل صحيح'
      case 'warning': return 'توجد تحذيرات في قاعدة البيانات'
      case 'error': return 'توجد مشاكل في قاعدة البيانات'
      default: return 'جاري فحص قاعدة البيانات...'
    }
  }

  return (
    <Card
      title={
        <Space>
          <DatabaseOutlined />
          تشخيص قاعدة البيانات المشتركة
        </Space>
      }
      extra={
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadDatabaseStatus}
            loading={loading}
          >
            إعادة فحص
          </Button>
          <Button
            type="primary"
            icon={<ToolOutlined />}
            onClick={autoFix}
            loading={loading}
            disabled={getOverallStatus() === 'success'}
          >
            إصلاح تلقائي
          </Button>
        </Space>
      }
    >
      {/* حالة عامة */}
      <Alert
        message={getOverallMessage()}
        type={getOverallStatus()}
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* معلومات قاعدة البيانات */}
      {databaseStatus && (
        <Descriptions
          title="معلومات قاعدة البيانات"
          bordered
          size="small"
          style={{ marginBottom: 16 }}
        >
          <Descriptions.Item label="المسار" span={3}>
            <Text code>{databaseStatus.currentPath}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="النوع">
            <Tag color={databaseStatus.isShared ? 'blue' : 'orange'}>
              {databaseStatus.isShared ? 'مشتركة' : 'محلية'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="الحجم">
            {databaseStatus.sizeKB} KB
          </Descriptions.Item>
          <Descriptions.Item label="الحالة">
            <Tag color={databaseStatus.isConnected ? 'green' : 'red'}>
              {databaseStatus.isConnected ? 'متصل' : 'غير متصل'}
            </Tag>
          </Descriptions.Item>
        </Descriptions>
      )}

      {/* نتائج التشخيص */}
      <Title level={5}>نتائج التشخيص</Title>
      <List
        dataSource={diagnostics}
        renderItem={(item, index) => (
          <List.Item>
            <List.Item.Meta
              avatar={getStatusIcon(item.status)}
              title={item.step}
              description={
                <Space direction="vertical" size="small">
                  <Text>{item.message}</Text>
                  {item.details && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {item.details}
                    </Text>
                  )}
                </Space>
              }
            />
          </List.Item>
        )}
      />

      {/* تفاصيل إضافية */}
      <Divider />
      <Space>
        <Button
          type="link"
          onClick={() => setShowDetails(true)}
          icon={<InfoCircleOutlined />}
        >
          عرض التفاصيل التقنية
        </Button>
      </Space>

      {/* نافذة التفاصيل */}
      <Modal
        title="التفاصيل التقنية"
        open={showDetails}
        onCancel={() => setShowDetails(false)}
        footer={null}
        width={600}
      >
        {databaseStatus && (
          <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
            {JSON.stringify(databaseStatus, null, 2)}
          </pre>
        )}
      </Modal>
    </Card>
  )
}

export default SharedDatabaseDiagnostics
