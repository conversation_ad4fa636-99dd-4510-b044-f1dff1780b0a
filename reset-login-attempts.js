/**
 * سكريبت لإعادة تعيين محاولات تسجيل الدخول
 * يستخدم هذا السكريبت لإلغاء حظر المستخدمين الذين تم حظرهم بسبب المحاولات المتكررة
 */

const { app } = require('electron')
const path = require('path')
const fs = require('fs')

// تهيئة sql.js
const initSqlJs = require('sql.js')

async function resetLoginAttempts() {
  try {
    console.log('🔄 بدء إعادة تعيين محاولات تسجيل الدخول...')

    // تهيئة sql.js
    const SQL = await initSqlJs({
      locateFile: (file) => {
        const possiblePaths = [
          path.join(process.cwd(), 'node_modules/sql.js/dist/', file),
          path.join(__dirname, 'node_modules/sql.js/dist/', file),
          path.join(__dirname, '../node_modules/sql.js/dist/', file)
        ]

        for (const filePath of possiblePaths) {
          if (fs.existsSync(filePath)) {
            return filePath
          }
        }

        return path.join(process.cwd(), 'node_modules/sql.js/dist/', file)
      }
    })

    // مسار قاعدة البيانات
    const dbPath = path.join(process.cwd(), 'database.db')
    
    let db
    if (fs.existsSync(dbPath)) {
      // قراءة قاعدة البيانات الموجودة
      const data = fs.readFileSync(dbPath)
      db = new SQL.Database(data)
      console.log('✅ تم تحميل قاعدة البيانات الموجودة')
    } else {
      console.log('❌ لم يتم العثور على قاعدة البيانات في:', dbPath)
      return
    }

    // إعادة تعيين محاولات تسجيل الدخول
    console.log('🔧 إعادة تعيين محاولات تسجيل الدخول...')
    
    // إلغاء الحظر لجميع المستخدمين
    db.exec(`
      UPDATE users 
      SET locked_until = NULL, login_attempts = 0
      WHERE locked_until IS NOT NULL OR login_attempts > 0
    `)

    // حذف جميع محاولات تسجيل الدخول الفاشلة
    db.exec(`
      DELETE FROM login_attempts 
      WHERE success = 0
    `)

    // حفظ قاعدة البيانات
    const data = db.export()
    fs.writeFileSync(dbPath, data)
    
    console.log('✅ تم إعادة تعيين محاولات تسجيل الدخول بنجاح!')
    console.log('✅ تم إلغاء حظر جميع المستخدمين')
    console.log('✅ يمكنك الآن تسجيل الدخول بشكل طبيعي')

    // إغلاق قاعدة البيانات
    db.close()

  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين محاولات تسجيل الدخول:', error)
  }
}

// تشغيل السكريبت
resetLoginAttempts().then(() => {
  console.log('🎉 تم الانتهاء من السكريبت')
  process.exit(0)
}).catch((error) => {
  console.error('❌ خطأ في تشغيل السكريبت:', error)
  process.exit(1)
})
