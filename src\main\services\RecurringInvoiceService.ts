import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { Logger } from '../utils/logger'
import { UniversalInvoiceService, CreateUniversalInvoiceData, InvoiceType } from './UniversalInvoiceService'
import * as cron from 'node-cron'

// واجهة قالب الفاتورة المتكررة
export interface RecurringInvoiceTemplate {
  id: number
  template_name: string
  invoice_type: InvoiceType
  entity_id: number
  entity_name: string
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  interval: number // كل كم من الفترة (مثلاً كل 2 أسبوع)
  start_date: string
  end_date?: string
  next_generation_date: string
  last_generated_date?: string
  is_active: boolean
  auto_send: boolean
  template_data: CreateUniversalInvoiceData
  created_by: number
  created_at: string
  updated_at?: string
}

// واجهة إنشاء قالب متكرر
export interface CreateRecurringTemplateData {
  template_name: string
  invoice_type: InvoiceType
  entity_id: number
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  interval: number
  start_date: string
  end_date?: string
  auto_send: boolean
  template_data: CreateUniversalInvoiceData
}

// سجل توليد الفواتير المتكررة
export interface RecurringInvoiceLog {
  id: number
  template_id: number
  generated_invoice_id?: number
  generation_date: string
  status: 'success' | 'failed' | 'skipped'
  error_message?: string
  created_at: string
}

export class RecurringInvoiceService {
  private static instance: RecurringInvoiceService
  private db: any
  private universalInvoiceService: UniversalInvoiceService
  private cronJobs: Map<number, cron.ScheduledTask> = new Map()

  private constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
    this.universalInvoiceService = UniversalInvoiceService.getInstance()
    this.initializeTables()
    this.startScheduler()
  }

  public static getInstance(): RecurringInvoiceService {
    if (!RecurringInvoiceService.instance) {
      RecurringInvoiceService.instance = new RecurringInvoiceService()
    }
    return RecurringInvoiceService.instance
  }

  // إنشاء الجداول المطلوبة
  private initializeTables(): void {
    try {
      // جدول قوالب الفواتير المتكررة
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS recurring_invoice_templates (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          template_name TEXT NOT NULL,
          invoice_type TEXT NOT NULL,
          entity_id INTEGER NOT NULL,
          entity_name TEXT NOT NULL,
          frequency TEXT NOT NULL,
          interval INTEGER DEFAULT 1,
          start_date TEXT NOT NULL,
          end_date TEXT,
          next_generation_date TEXT NOT NULL,
          last_generated_date TEXT,
          is_active BOOLEAN DEFAULT 1,
          auto_send BOOLEAN DEFAULT 0,
          template_data TEXT NOT NULL, -- JSON
          created_by INTEGER NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME
        )
      `)

      // جدول سجل توليد الفواتير المتكررة
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS recurring_invoice_logs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          template_id INTEGER NOT NULL,
          generated_invoice_id INTEGER,
          generation_date TEXT NOT NULL,
          status TEXT NOT NULL,
          error_message TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (template_id) REFERENCES recurring_invoice_templates(id)
        )
      `)

      Logger.info('RecurringInvoiceService', '✅ تم إنشاء جداول الفواتير المتكررة')
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في إنشاء الجداول:', error)
    }
  }

  // بدء جدولة المهام
  private startScheduler(): void {
    try {
      // تشغيل كل ساعة للتحقق من الفواتير المستحقة
      cron.schedule('0 * * * *', () => {
        this.processRecurringInvoices()
      })

      Logger.info('RecurringInvoiceService', '✅ تم بدء جدولة الفواتير المتكررة')
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في بدء الجدولة:', error)
    }
  }

  // إنشاء قالب فاتورة متكررة
  public async createTemplate(data: CreateRecurringTemplateData, userId: number): Promise<ApiResponse> {
    try {
      Logger.info('RecurringInvoiceService', `إنشاء قالب فاتورة متكررة: ${data.template_name}`)

      // حساب تاريخ التوليد التالي
      const nextGenerationDate = this.calculateNextDate(data.start_date, data.frequency, data.interval)

      const stmt = this.db.prepare(`
        INSERT INTO recurring_invoice_templates (
          template_name, invoice_type, entity_id, entity_name, frequency, interval,
          start_date, end_date, next_generation_date, auto_send, template_data, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      // جلب اسم الكيان
      const entityName = await this.getEntityName(data.entity_id, data.invoice_type)

      const result = stmt.run(
        data.template_name,
        data.invoice_type,
        data.entity_id,
        entityName,
        data.frequency,
        data.interval,
        data.start_date,
        data.end_date || null,
        nextGenerationDate,
        data.auto_send ? 1 : 0,
        JSON.stringify(data.template_data),
        userId
      )

      Logger.info('RecurringInvoiceService', `تم إنشاء القالب بنجاح، ID: ${result.lastInsertRowid}`)

      return {
        success: true,
        message: 'تم إنشاء قالب الفاتورة المتكررة بنجاح',
        data: { id: result.lastInsertRowid }
      }
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في إنشاء القالب:', error)
      return {
        success: false,
        message: 'حدث خطأ في إنشاء قالب الفاتورة المتكررة'
      }
    }
  }

  // تحديث قالب فاتورة متكررة
  public async updateTemplate(templateId: number, data: Partial<CreateRecurringTemplateData>): Promise<ApiResponse> {
    try {
      Logger.info('RecurringInvoiceService', `تحديث قالب الفاتورة المتكررة: ${templateId}`)

      const updates: string[] = []
      const values: any[] = []

      if (data.template_name) {
        updates.push('template_name = ?')
        values.push(data.template_name)
      }

      if (data.frequency || data.interval) {
        // إعادة حساب تاريخ التوليد التالي
        const template = await this.getTemplate(templateId)
        if (template) {
          const nextDate = this.calculateNextDate(
            template.start_date,
            data.frequency || template.frequency,
            data.interval || template.interval
          )
          updates.push('next_generation_date = ?')
          values.push(nextDate)
        }
      }

      if (data.frequency) {
        updates.push('frequency = ?')
        values.push(data.frequency)
      }

      if (data.interval) {
        updates.push('interval = ?')
        values.push(data.interval)
      }

      if (data.end_date !== undefined) {
        updates.push('end_date = ?')
        values.push(data.end_date)
      }

      if (data.auto_send !== undefined) {
        updates.push('auto_send = ?')
        values.push(data.auto_send ? 1 : 0)
      }

      if (data.template_data) {
        updates.push('template_data = ?')
        values.push(JSON.stringify(data.template_data))
      }

      if (updates.length === 0) {
        return { success: false, message: 'لا توجد بيانات للتحديث' }
      }

      updates.push('updated_at = CURRENT_TIMESTAMP')
      values.push(templateId)

      const stmt = this.db.prepare(`
        UPDATE recurring_invoice_templates 
        SET ${updates.join(', ')} 
        WHERE id = ?
      `)

      stmt.run(...values)

      return {
        success: true,
        message: 'تم تحديث قالب الفاتورة المتكررة بنجاح'
      }
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في تحديث القالب:', error)
      return {
        success: false,
        message: 'حدث خطأ في تحديث قالب الفاتورة المتكررة'
      }
    }
  }

  // حذف قالب فاتورة متكررة
  public async deleteTemplate(templateId: number): Promise<ApiResponse> {
    try {
      Logger.info('RecurringInvoiceService', `حذف قالب الفاتورة المتكررة: ${templateId}`)

      // إيقاف المهمة المجدولة إن وجدت
      const cronJob = this.cronJobs.get(templateId)
      if (cronJob) {
        cronJob.stop()
        this.cronJobs.delete(templateId)
      }

      const stmt = this.db.prepare('DELETE FROM recurring_invoice_templates WHERE id = ?')
      const result = stmt.run(templateId)

      if (result.changes === 0) {
        return { success: false, message: 'القالب غير موجود' }
      }

      return {
        success: true,
        message: 'تم حذف قالب الفاتورة المتكررة بنجاح'
      }
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في حذف القالب:', error)
      return {
        success: false,
        message: 'حدث خطأ في حذف قالب الفاتورة المتكررة'
      }
    }
  }

  // جلب جميع القوالب
  public async getTemplates(): Promise<ApiResponse> {
    try {
      const stmt = this.db.prepare(`
        SELECT * FROM recurring_invoice_templates 
        ORDER BY created_at DESC
      `)
      
      const rows = stmt.all()
      const templates = rows.map((row: any) => ({
        ...row,
        is_active: Boolean(row.is_active),
        auto_send: Boolean(row.auto_send),
        template_data: JSON.parse(row.template_data)
      }))

      return {
        success: true,
        data: templates
      }
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في جلب القوالب:', error)
      return {
        success: false,
        message: 'حدث خطأ في جلب قوالب الفواتير المتكررة'
      }
    }
  }

  // جلب قالب واحد
  public async getTemplate(templateId: number): Promise<RecurringInvoiceTemplate | null> {
    try {
      const stmt = this.db.prepare('SELECT * FROM recurring_invoice_templates WHERE id = ?')
      const row = stmt.get(templateId)
      
      if (!row) return null

      return {
        ...row,
        is_active: Boolean(row.is_active),
        auto_send: Boolean(row.auto_send),
        template_data: JSON.parse(row.template_data)
      }
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في جلب القالب:', error)
      return null
    }
  }

  // تفعيل/إلغاء تفعيل قالب
  public async toggleTemplate(templateId: number, isActive: boolean): Promise<ApiResponse> {
    try {
      const stmt = this.db.prepare(`
        UPDATE recurring_invoice_templates 
        SET is_active = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `)
      
      stmt.run(isActive ? 1 : 0, templateId)

      return {
        success: true,
        message: isActive ? 'تم تفعيل القالب' : 'تم إلغاء تفعيل القالب'
      }
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في تغيير حالة القالب:', error)
      return {
        success: false,
        message: 'حدث خطأ في تغيير حالة القالب'
      }
    }
  }

  // معالجة الفواتير المتكررة المستحقة
  public async processRecurringInvoices(): Promise<void> {
    try {
      Logger.info('RecurringInvoiceService', 'بدء معالجة الفواتير المتكررة المستحقة')

      const today = new Date().toISOString().split('T')[0]
      
      const stmt = this.db.prepare(`
        SELECT * FROM recurring_invoice_templates 
        WHERE is_active = 1 
        AND next_generation_date <= ? 
        AND (end_date IS NULL OR end_date >= ?)
      `)
      
      const templates = stmt.all(today, today)

      for (const template of templates) {
        await this.generateInvoiceFromTemplate(template)
      }

      Logger.info('RecurringInvoiceService', `تم معالجة ${templates.length} قالب`)
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في معالجة الفواتير المتكررة:', error)
    }
  }

  // توليد فاتورة من قالب
  private async generateInvoiceFromTemplate(template: any): Promise<void> {
    try {
      Logger.info('RecurringInvoiceService', `توليد فاتورة من القالب: ${template.template_name}`)

      const templateData = JSON.parse(template.template_data)
      
      // إنشاء الفاتورة
      const result = await this.universalInvoiceService.createInvoice({
        ...templateData,
        invoice_date: new Date().toISOString().split('T')[0]
      })

      // تسجيل النتيجة
      await this.logGeneration(template.id, result.success, result.data?.id, result.message)

      if (result.success) {
        // تحديث تاريخ التوليد التالي
        const nextDate = this.calculateNextDate(
          template.next_generation_date,
          template.frequency,
          template.interval
        )

        const updateStmt = this.db.prepare(`
          UPDATE recurring_invoice_templates 
          SET next_generation_date = ?, last_generated_date = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `)
        
        updateStmt.run(nextDate, new Date().toISOString().split('T')[0], template.id)

        Logger.info('RecurringInvoiceService', `تم توليد الفاتورة بنجاح، ID: ${result.data?.id}`)
      } else {
        Logger.error('RecurringInvoiceService', `فشل في توليد الفاتورة: ${result.message}`)
      }
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في توليد الفاتورة من القالب:', error)
      await this.logGeneration(template.id, false, undefined, error instanceof Error ? error.message : 'خطأ غير معروف')
    }
  }

  // تسجيل عملية التوليد
  private async logGeneration(templateId: number, success: boolean, invoiceId?: number, errorMessage?: string): Promise<void> {
    try {
      const stmt = this.db.prepare(`
        INSERT INTO recurring_invoice_logs (template_id, generated_invoice_id, generation_date, status, error_message)
        VALUES (?, ?, ?, ?, ?)
      `)
      
      stmt.run(
        templateId,
        invoiceId || null,
        new Date().toISOString().split('T')[0],
        success ? 'success' : 'failed',
        errorMessage || null
      )
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في تسجيل عملية التوليد:', error)
    }
  }

  // حساب التاريخ التالي
  private calculateNextDate(currentDate: string, frequency: string, interval: number): string {
    const date = new Date(currentDate)
    
    switch (frequency) {
      case 'daily':
        date.setDate(date.getDate() + interval)
        break
      case 'weekly':
        date.setDate(date.getDate() + (interval * 7))
        break
      case 'monthly':
        date.setMonth(date.getMonth() + interval)
        break
      case 'quarterly':
        date.setMonth(date.getMonth() + (interval * 3))
        break
      case 'yearly':
        date.setFullYear(date.getFullYear() + interval)
        break
    }
    
    return date.toISOString().split('T')[0]
  }

  // جلب اسم الكيان
  private async getEntityName(entityId: number, invoiceType: InvoiceType): Promise<string> {
    try {
      let tableName = ''
      let nameField = ''
      
      if (invoiceType === 'sales' || invoiceType === 'paint' || invoiceType === 'service') {
        tableName = 'customers'
        nameField = 'customer_name'
      } else {
        tableName = 'suppliers'
        nameField = 'supplier_name'
      }
      
      const stmt = this.db.prepare(`SELECT ${nameField} FROM ${tableName} WHERE id = ?`)
      const result = stmt.get(entityId)
      
      return result ? result[nameField] : 'غير معروف'
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في جلب اسم الكيان:', error)
      return 'غير معروف'
    }
  }

  // جلب سجل التوليد
  public async getGenerationLogs(templateId?: number): Promise<ApiResponse> {
    try {
      let query = `
        SELECT l.*, t.template_name 
        FROM recurring_invoice_logs l
        JOIN recurring_invoice_templates t ON l.template_id = t.id
      `
      const params: any[] = []
      
      if (templateId) {
        query += ' WHERE l.template_id = ?'
        params.push(templateId)
      }
      
      query += ' ORDER BY l.created_at DESC'
      
      const stmt = this.db.prepare(query)
      const logs = stmt.all(...params)

      return {
        success: true,
        data: logs
      }
    } catch (error) {
      Logger.error('RecurringInvoiceService', 'خطأ في جلب سجل التوليد:', error)
      return {
        success: false,
        message: 'حدث خطأ في جلب سجل التوليد'
      }
    }
  }
}
