import { logger as Logger } from './logger'

// نّام النسخ الاحتياطي للبيانات المحلية
export class DataBackupManager {
  private static instance: DataBackupManager
  private backupKey = 'app_data_backup'
  private lastBackupKey = 'last_backup_timestamp'

  private constructor() {}

  public static getInstance(): DataBackupManager {
    if (!DataBackupManager.instance) {
      DataBackupManager.instance = new DataBackupManager()
    }
    return DataBackupManager.instance
  }

  // إنشاء نسخة احتياطية من البيانات المحلية
  public createBackup(): void {
    try {
      const backupData: any = {}
      
      // جمع جميع البيانات المهمة من localStorage
      const importantKeys = [
        'app_settings',
        'currentUser',
        'authToken',
        'app-theme',
        'rememberedUsername',
        'rememberMe'
      ]

      // إضافة البيانات السريعة
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && (key.startsWith('quick_input_') || key.startsWith('user_preferences_'))) {
          importantKeys.push(key)
        }
      }

      // نسخ البيانات
      importantKeys.forEach(key => {
        const value = localStorage.getItem(key)
        if (value) {
          backupData[key] = value
        }
      })

      // حفّ النسخة الاحتياطية
      localStorage.setItem(this.backupKey, JSON.stringify(backupData))
      localStorage.setItem(this.lastBackupKey, new Date().toISOString())

      Logger.info('DataBackup', '✅ تم إنشاء نسخة احتياطية من البيانات المحلية')
    } catch (error) {
      Logger.error('DataBackup', 'خطأ في إنشاء النسخة الاحتياطية:', error as Error)
    }
  }

  // استعادة البيانات من النسخة الاحتياطية
  public restoreBackup(): boolean {
    try {
      const backupData = localStorage.getItem(this.backupKey)
      if (!backupData) {
        Logger.warn('DataBackup', 'لا توجد نسخة احتياطية للاستعادة')
        return false
      }

      const parsedData = JSON.parse(backupData)
      let restoredCount = 0

      // استعادة البيانات
      Object.keys(parsedData).forEach(key => {
        try {
          localStorage.setItem(key, parsedData[key])
          restoredCount++
        } catch (error) {
          Logger.error('DataBackup', `خطأ في استعادة المفتاح ${key}:`, error as Error)
        }
      })

      Logger.info('DataBackup', `✅ تم استعادة ${restoredCount} عنصر من النسخة الاحتياطية`)
      return true
    } catch (error) {
      Logger.error('DataBackup', 'خطأ في استعادة النسخة الاحتياطية:', error as Error)
      return false
    }
  }

  // فحص ما إذا كانت هناك حاجة لنسخة احتياطية
  public shouldCreateBackup(): boolean {
    try {
      const lastBackup = localStorage.getItem(this.lastBackupKey)
      if (!lastBackup) return true

      const lastBackupTime = new Date(lastBackup).getTime()
      const now = new Date().getTime()
      const hoursSinceBackup = (now - lastBackupTime) / (1000 * 60 * 60)

      // إنشاء نسخة احتياطية كل 6 ساعات
      return hoursSinceBackup >= 6
    } catch (error) {
      Logger.error('DataBackup', 'خطأ في فحص الحاجة للنسخة الاحتياطية:', error as Error)
      return true
    }
  }

  // تنّيف النسخ الاحتياطية القديمة
  public cleanOldBackups(): void {
    try {
      // الاحتفاّ بآخر 3 نسخ احتياطية فقط
      const backupKeys = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith('app_data_backup_')) {
          backupKeys.push(key)
        }
      }

      if (backupKeys.length > 3) {
        // ترتيب حسب التاريخ وحذف الأقدم
        backupKeys.sort().slice(0, -3).forEach(key => {
          localStorage.removeItem(key)
        })
        Logger.info('DataBackup', `تم تنّيف ${backupKeys.length - 3} نسخة احتياطية قديمة`)
      }
    } catch (error) {
      Logger.error('DataBackup', 'خطأ في تنّيف النسخ الاحتياطية:', error as Error)
    }
  }

  // بدء النسخ الاحتياطي التلقائي
  public startAutoBackup(): void {
    // إنشاء نسخة احتياطية فورية إذا لزم الأمر
    if (this.shouldCreateBackup()) {
      this.createBackup()
    }

    // جدولة النسخ الاحتياطي التلقائي كل ساعة
    setInterval(() => {
      if (this.shouldCreateBackup()) {
        this.createBackup()
        this.cleanOldBackups()
      }
    }, 60 * 60 * 1000) // كل ساعة

    Logger.info('DataBackup', '🔄 تم بدء النسخ الاحتياطي التلقائي')
  }

  // فحص سلامة البيانات
  public checkDataIntegrity(): boolean {
    try {
      const criticalKeys = ['app_settings', 'currentUser']
      const missingKeys = criticalKeys.filter(key => !localStorage.getItem(key))
      
      if (missingKeys.length > 0) {
        Logger.warn('DataBackup', `بيانات مفقودة: ${missingKeys.join(', ')}`)
        return false
      }
      
      return true
    } catch (error) {
      Logger.error('DataBackup', 'خطأ في فحص سلامة البيانات:', error as Error)
      return false
    }
  }
}

// إنشاء مثيل واحد للاستخدام العام
export const dataBackupManager = DataBackupManager.getInstance()

// دوال مساعدة للاستخدام السريع
export const createDataBackup = () => dataBackupManager.createBackup()
export const restoreDataBackup = () => dataBackupManager.restoreBackup()
export const startAutoBackup = () => dataBackupManager.startAutoBackup()
export const checkDataIntegrity = () => dataBackupManager.checkDataIntegrity()
