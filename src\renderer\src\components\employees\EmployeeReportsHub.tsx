import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Typo<PERSON>,
  But<PERSON>,
  Space,
  Menu,
  Layout,
  Breadcrumb,
  Divider,
  Statistic} from 'antd';
import {
  FileTextOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  CalendarOutlined,
  TeamOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  UserOutlined,
  ArrowLeftOutlined,
  TrophyOutlined,
  RiseOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

// استيراد تقارير الموظفين
import EmployeeAttendanceReport from '../reports/EmployeeAttendanceReport';
import EmployeePayrollReport from '../reports/EmployeePayrollReport';
import EmployeeLeavesReport from '../reports/EmployeeLeavesReport';
import EmployeePerformanceReport from '../reports/EmployeePerformanceReport';
import EmployeeOvertimeReport from '../reports/EmployeeOvertimeReport';

const { Title, Text, Paragraph } = Typography;
const { Sider, Content } = Layout;

const StyledLayout = styled(Layout)`
  min-height: 100vh;
  background: #f0f2f5;
`;

const StyledSider = styled(Sider)`
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
`;

const StyledContent = styled(Content)`
  padding: 24px;
  background: #f0f2f5;
`;

const ReportCard = styled(Card)`
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
`;

const OverviewCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
`;

interface EmployeeReportsHubProps {
  onBack?: () => void;
}

const EmployeeReportsHub: React.FC<EmployeeReportsHubProps> = ({ onBack }) => {
  const [selectedReport, setSelectedReport] = useState<string>('overview');

  // قائمة التقارير المتاحة
  const reportMenuItems = [
    {
      key: 'overview',
      icon: <BarChartOutlined />,
      label: 'نّرة عامة',
      description: 'ملخص سريع لجميع تقارير الموظفين'
    },
    {
      key: 'attendance',
      icon: <ClockCircleOutlined />,
      label: 'تقرير الحضور والانصراف',
      description: 'تقرير شامل لحضور وانصراف الموظفين'
    },
    {
      key: 'payroll',
      icon: <DollarOutlined />,
      label: 'تقرير الرواتب والمكافآت',
      description: 'تقرير مفصل للرواتب والمكافآت'
    },
    {
      key: 'leaves',
      icon: <CalendarOutlined />,
      label: 'تقرير الإجازات والغياب',
      description: 'تقرير شامل لإجازات الموظفين'
    },
    {
      key: 'performance',
      icon: <TeamOutlined />,
      label: 'تقرير أداء الأقسام',
      description: 'تقرير أداء الموظفين حسب القسم'
    },
    {
      key: 'overtime',
      icon: <ClockCircleOutlined />,
      label: 'تقرير الساعات الإضافية',
      description: 'تقرير شامل للساعات الإضافية والتكاليف'
    }
  ];

  // عرض التقرير المحدد
  const renderSelectedReport = () => {
    switch (selectedReport) {
      case 'attendance':
        return <EmployeeAttendanceReport />;
      case 'payroll':
        return <EmployeePayrollReport />;
      case 'leaves':
        return <EmployeeLeavesReport />;
      case 'performance':
        return <EmployeePerformanceReport />;
      case 'overtime':
        return <EmployeeOvertimeReport />;
      default:
        return renderOverview();
    }
  };

  // عرض النّرة العامة
  const renderOverview = () => (
    <div>
      <OverviewCard>
        <Title level={3} style={{ color: '#1890ff', marginBottom: '16px' }}>
          <BarChartOutlined style={{ marginLeft: '12px' }} />
          نّرة عامة على تقارير الموظفين
        </Title>
        <Paragraph style={{ fontSize: '16px', color: '#666' }}>
          مرحباً بك في مركز تقارير الموظفين. يمكنك من هنا الوصول إلى جميع التقارير المتعلقة بالموظفين
          والحضور والرواتب والإجازات وأداء الأقسام.
        </Paragraph>
        
        {/* إحصائيات سريعة */}
        <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
          <Col xs={24} sm={6}>
            <Card size="small" style={{ textAlign: 'center' }}>
              <Statistic
                title="إجمالي التقارير"
                value={4}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card size="small" style={{ textAlign: 'center' }}>
              <Statistic
                title="تقارير الحضور"
                value={1}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card size="small" style={{ textAlign: 'center' }}>
              <Statistic
                title="تقارير الرواتب"
                value={1}
                prefix={<DollarOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card size="small" style={{ textAlign: 'center' }}>
              <Statistic
                title="تقارير الأداء"
                value={2}
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>
      </OverviewCard>

      {/* بطاقات التقارير */}
      <Row gutter={[16, 16]}>
        {reportMenuItems.slice(1).map((report) => (
          <Col xs={24} sm={12} lg={6} key={report.key}>
            <ReportCard
              hoverable
              onClick={() => setSelectedReport(report.key)}
              style={{ cursor: 'pointer', height: '200px' }}
            >
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <div style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }}>
                  {report.icon}
                </div>
                <Title level={5} style={{ marginBottom: '8px' }}>
                  {report.label}
                </Title>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {report.description}
                </Text>
              </div>
            </ReportCard>
          </Col>
        ))}
      </Row>

      {/* معلومات إضافية */}
      <Card style={{ marginTop: '24px' }}>
        <Title level={4} style={{ color: '#1890ff' }}>
          <RiseOutlined style={{ marginLeft: '8px' }} />
          مميزات التقارير
        </Title>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <div style={{ textAlign: 'center', padding: '16px' }}>
              <CheckCircleOutlined style={{ fontSize: '32px', color: '#52c41a', marginBottom: '8px' }} />
              <Title level={5}>فلاتر متقدمة</Title>
              <Text type="secondary">فلترة حسب التاريخ والقسم والموظف</Text>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div style={{ textAlign: 'center', padding: '16px' }}>
              <FileTextOutlined style={{ fontSize: '32px', color: '#1890ff', marginBottom: '8px' }} />
              <Title level={5}>تصدير متعدد</Title>
              <Text type="secondary">تصدير إلى PDF وExcel والطباعة</Text>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div style={{ textAlign: 'center', padding: '16px' }}>
              <BarChartOutlined style={{ fontSize: '32px', color: '#722ed1', marginBottom: '8px' }} />
              <Title level={5}>إحصائيات تفاعلية</Title>
              <Text type="secondary">رسوم بيانية وإحصائيات مفصلة</Text>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  );

  return (
    <StyledLayout>
      <StyledSider width={280} theme="light">
        <div style={{ padding: '16px' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={onBack}
              style={{ marginBottom: '16px' }}
            >
              العودة لإدارة الموظفين
            </Button>
            
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
              <FileTextOutlined style={{ marginLeft: '8px' }} />
              تقارير الموظفين
            </Title>
            
            <Divider style={{ margin: '16px 0' }} />
          </Space>
        </div>

        <Menu
          mode="inline"
          selectedKeys={[selectedReport]}
          onClick={({ key }) => setSelectedReport(key)}
          style={{ border: 'none' }}
          items={reportMenuItems.map((item) => ({
            key: item.key,
            icon: item.icon,
            label: (
              <div>
                <div style={{ fontWeight: 'bold' }}>{item.label}</div>
                <div style={{ fontSize: '11px', color: '#999', marginTop: '2px' }}>
                  {item.description}
                </div>
              </div>
            )
          }))}
        />
      </StyledSider>

      <StyledContent>
        <Breadcrumb
          style={{ marginBottom: '16px' }}
          items={[
            {
              title: (
                <span>
                  <UserOutlined />
                  <span style={{ marginLeft: '8px' }}>إدارة الموظفين</span>
                </span>
              )
            },
            {
              title: (
                <span>
                  <FileTextOutlined />
                  <span style={{ marginLeft: '8px' }}>التقارير</span>
                </span>
              )
            },
            {
              title: reportMenuItems.find(item => item.key === selectedReport)?.label || 'نّرة عامة'
            }
          ]}
        />

        {renderSelectedReport()}
      </StyledContent>
    </StyledLayout>
  );
};

export default EmployeeReportsHub;
