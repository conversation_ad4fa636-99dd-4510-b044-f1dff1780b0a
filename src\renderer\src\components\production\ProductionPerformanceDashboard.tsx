/**
 * لوحة مراقبة أداء الإنتاج
 * مكون شامل لمراقبة أداء قسم الإنتاج في الوقت الفعلي
 */

import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Tooltip,
  Alert,
  Divider,
  Select,
  DatePicker
} from 'antd'
import {
  DashboardOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  AlertOutlined,
  ReloadOutlined,
  SettingOutlined,
  LineChartOutlined,
  BarChartOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import { SafeLogger as Logger } from '../../utils/logger'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select

interface PerformanceMetrics {
  totalOrders: number
  completedOrders: number
  inProgressOrders: number
  delayedOrders: number
  averageCompletionTime: number
  efficiencyScore: number
  qualityScore: number
  productivityScore: number
  onTimeDeliveryRate: number
  defectRate: number
  resourceUtilization: number
  costEfficiency: number
}

interface DepartmentPerformance {
  id: number
  name: string
  efficiency: number
  quality: number
  productivity: number
  orders: number
  status: 'excellent' | 'good' | 'average' | 'poor'
}

const ProductionPerformanceDashboard: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [departments, setDepartments] = useState<DepartmentPerformance[]>([])
  const [selectedPeriod, setSelectedPeriod] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(7, 'days'),
    dayjs()
  ])
  const [refreshInterval, setRefreshInterval] = useState<number>(30000) // 30 ثانية

  // تحميل بيانات الأداء
  const loadPerformanceData = async () => {
    try {
      setLoading(true)
      Logger.info('ProductionPerformanceDashboard', 'تحميل بيانات الأداء')

      // محاكاة بيانات الأداء
      const mockMetrics: PerformanceMetrics = {
        totalOrders: 150,
        completedOrders: 120,
        inProgressOrders: 25,
        delayedOrders: 5,
        averageCompletionTime: 3.2,
        efficiencyScore: 87.5,
        qualityScore: 92.3,
        productivityScore: 85.7,
        onTimeDeliveryRate: 96.7,
        defectRate: 2.1,
        resourceUtilization: 78.9,
        costEfficiency: 89.4
      }

      const mockDepartments: DepartmentPerformance[] = [
        { id: 1, name: 'النجارة', efficiency: 92, quality: 95, productivity: 88, orders: 45, status: 'excellent' },
        { id: 2, name: 'التجميع', efficiency: 85, quality: 90, productivity: 82, orders: 38, status: 'good' },
        { id: 3, name: 'التشطيب', efficiency: 78, quality: 88, productivity: 75, orders: 32, status: 'average' },
        { id: 4, name: 'التعبئة', efficiency: 95, quality: 97, productivity: 93, orders: 35, status: 'excellent' }
      ]

      setMetrics(mockMetrics)
      setDepartments(mockDepartments)

    } catch (error) {
      Logger.error('ProductionPerformanceDashboard', 'خطأ في تحميل بيانات الأداء:', error)
    } finally {
      setLoading(false)
    }
  }

  // تحديث تلقائي للبيانات
  useEffect(() => {
    loadPerformanceData()
    
    const interval = setInterval(() => {
      loadPerformanceData()
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [selectedPeriod, refreshInterval])

  // أعمدة جدول الأقسام
  const departmentColumns = [
    {
      title: 'القسم',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: DepartmentPerformance) => (
        <Space>
          <Text strong>{text}</Text>
          <Tag color={
            record.status === 'excellent' ? 'green' :
            record.status === 'good' ? 'blue' :
            record.status === 'average' ? 'orange' : 'red'
          }>
            {record.status === 'excellent' ? 'ممتاز' :
             record.status === 'good' ? 'جيد' :
             record.status === 'average' ? 'متوسط' : 'ضعيف'}
          </Tag>
        </Space>
      )
    },
    {
      title: 'الكفاءة',
      dataIndex: 'efficiency',
      key: 'efficiency',
      render: (value: number) => (
        <Progress 
          percent={value} 
          size="small" 
          status={value >= 90 ? 'success' : value >= 70 ? 'active' : 'exception'}
        />
      )
    },
    {
      title: 'الجودة',
      dataIndex: 'quality',
      key: 'quality',
      render: (value: number) => (
        <Progress 
          percent={value} 
          size="small" 
          status={value >= 95 ? 'success' : value >= 85 ? 'active' : 'exception'}
        />
      )
    },
    {
      title: 'الإنتاجية',
      dataIndex: 'productivity',
      key: 'productivity',
      render: (value: number) => (
        <Progress 
          percent={value} 
          size="small" 
          status={value >= 90 ? 'success' : value >= 70 ? 'active' : 'exception'}
        />
      )
    },
    {
      title: 'عدد الأوامر',
      dataIndex: 'orders',
      key: 'orders',
      align: 'center' as const,
      render: (value: number) => (
        <Tag color="blue">{value}</Tag>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* العنوان والتحكم */}
      <Card style={{ marginBottom: '16px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              <DashboardOutlined /> لوحة مراقبة أداء الإنتاج
            </Title>
            <Text type="secondary">مراقبة الأداء في الوقت الفعلي</Text>
          </Col>
          <Col>
            <Space>
              <RangePicker
                value={selectedPeriod}
                onChange={(dates) => setSelectedPeriod(dates as [dayjs.Dayjs, dayjs.Dayjs])}
                format="YYYY-MM-DD"
              />
              <Select
                value={refreshInterval}
                onChange={setRefreshInterval}
                style={{ width: 120 }}
              >
                <Option value={10000}>10 ثانية</Option>
                <Option value={30000}>30 ثانية</Option>
                <Option value={60000}>دقيقة</Option>
                <Option value={300000}>5 دقائق</Option>
              </Select>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadPerformanceData}
                loading={loading}
              >
                تحديث
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* المقاييس الرئيسية */}
      <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="إجمالي الأوامر"
              value={metrics?.totalOrders || 0}
              prefix={<DashboardOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="أوامر مكتملة"
              value={metrics?.completedOrders || 0}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="قيد التنفيذ"
              value={metrics?.inProgressOrders || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="أوامر متأخرة"
              value={metrics?.delayedOrders || 0}
              prefix={<AlertOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* مؤشرات الأداء */}
      <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
        <Col xs={24} md={12}>
          <Card title="مؤشرات الكفاءة" extra={<LineChartOutlined />}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="نقاط الكفاءة"
                  value={metrics?.efficiencyScore || 0}
                  precision={1}
                  suffix="%"
                  valueStyle={{ 
                    color: (metrics?.efficiencyScore || 0) >= 85 ? '#52c41a' : '#fa8c16' 
                  }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="نقاط الجودة"
                  value={metrics?.qualityScore || 0}
                  precision={1}
                  suffix="%"
                  valueStyle={{ 
                    color: (metrics?.qualityScore || 0) >= 90 ? '#52c41a' : '#fa8c16' 
                  }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="الإنتاجية"
                  value={metrics?.productivityScore || 0}
                  precision={1}
                  suffix="%"
                  valueStyle={{ 
                    color: (metrics?.productivityScore || 0) >= 80 ? '#52c41a' : '#fa8c16' 
                  }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="التسليم في الوقت"
                  value={metrics?.onTimeDeliveryRate || 0}
                  precision={1}
                  suffix="%"
                  valueStyle={{ 
                    color: (metrics?.onTimeDeliveryRate || 0) >= 95 ? '#52c41a' : '#fa8c16' 
                  }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title="مؤشرات التشغيل" extra={<BarChartOutlined />}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="متوسط وقت الإنجاز"
                  value={metrics?.averageCompletionTime || 0}
                  precision={1}
                  suffix="أيام"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="معدل العيوب"
                  value={metrics?.defectRate || 0}
                  precision={1}
                  suffix="%"
                  valueStyle={{ 
                    color: (metrics?.defectRate || 0) <= 3 ? '#52c41a' : '#ff4d4f' 
                  }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="استغلال الموارد"
                  value={metrics?.resourceUtilization || 0}
                  precision={1}
                  suffix="%"
                  valueStyle={{ 
                    color: (metrics?.resourceUtilization || 0) >= 75 ? '#52c41a' : '#fa8c16' 
                  }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="كفاءة التكلفة"
                  value={metrics?.costEfficiency || 0}
                  precision={1}
                  suffix="%"
                  valueStyle={{ 
                    color: (metrics?.costEfficiency || 0) >= 85 ? '#52c41a' : '#fa8c16' 
                  }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* أداء الأقسام */}
      <Card title="أداء الأقسام" loading={loading}>
        <Table
          columns={departmentColumns}
          dataSource={departments}
          rowKey="id"
          pagination={false}
          size="middle"
        />
      </Card>
    </div>
  )
}

export default ProductionPerformanceDashboard
