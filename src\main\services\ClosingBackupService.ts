// import { Database } from 'better-sqlite3' // لم نعد نستخدم better-sqlite3
import { DatabaseService } from './DatabaseService'
import { Logger } from '../utils/logger'
import * as fs from 'fs'
import * as path from 'path'

export interface BackupResult {
  success: boolean
  message: string
  data?: {
    backupId: number
    fileName: string
    filePath: string
    fileSize: number
    checksum?: string
  }
}

export interface ClosingBackupInfo {
  id: number
  fiscal_period_id: number
  file_name: string
  file_path: string
  file_size: number
  backup_type: string
  status: string
  is_period_closing_backup: boolean
  closing_type: string
  created_at: string
  checksum?: string
}

/**
 * خدمة النسخ الاحتياطية المتخصصة للإقفال المحاسبي
 */
export class ClosingBackupService {
  private db: any // sql.js Database instance
  private static instance: ClosingBackupService

  constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
  }

  public static getInstance(): ClosingBackupService {
    if (!ClosingBackupService.instance) {
      ClosingBackupService.instance = new ClosingBackupService()
    }
    return ClosingBackupService.instance
  }

  /**
   * إنشاء نسخة احتياطية خاصة بالإقفال
   */
  public async createClosingBackup(
    periodId: number,
    closingType: 'monthly' | 'quarterly' | 'semi_annual' | 'annual',
    description?: string
  ): Promise<BackupResult> {
    try {
      // إنشاء اسم الملف
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
      const timeStamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[1].split('.')[0]
      const fileName = `closing_backup_${closingType}_${timestamp}_${timeStamp}.db`

      // إنشاء مجلد النسخ الاحتياطية للإقفال
      const backupDir = path.join(path.dirname(DatabaseService.getInstance().getDatabasePath()), 'closing_backups')
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true })
      }

      const backupPath = path.join(backupDir, fileName)
      const relativeFilePath = `./closing_backups/${fileName}`

      // تسجيل النسخة الاحتياطية في قاعدة البيانات
      const backupRecord = this.db.prepare(`
        INSERT INTO backups (
          file_name, file_path, file_size, backup_type, status, description,
          fiscal_period_id, is_period_closing_backup, closing_type
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        fileName,
        relativeFilePath,
        0, // سيتم تحديثه بعد إنشاء الملف
        'manual',
        'in_progress',
        description || `نسخة احتياطية للإقفال ${this.getClosingTypeArabic(closingType)}`,
        periodId,
        1, // is_period_closing_backup
        closingType
      )

      const backupId = backupRecord.lastInsertRowid as number

      try {
        // إنشاء النسخة الاحتياطية الفعلية
        await this.createPhysicalBackup(backupPath)

        // التحقق من نجاح إنشاء الملف
        if (!fs.existsSync(backupPath)) {
          throw new Error('فشل في إنشاء ملف النسخة الاحتياطية')
        }

        const stats = fs.statSync(backupPath)
        if (stats.size === 0) {
          throw new Error('النسخة الاحتياطية فارغة')
        }

        // حساب checksum للتحقق من سلامة الملف
        const checksum = await this.calculateChecksum(backupPath)

        // تحديث سجل النسخة الاحتياطية
        this.db.prepare(`
          UPDATE backups 
          SET status = 'completed', file_size = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).run(stats.size, backupId)

        // حفظ checksum في جدول منفصل للأمان
        this.db.prepare(`
          INSERT OR REPLACE INTO backup_checksums (backup_id, checksum, algorithm)
          VALUES (?, ?, 'sha256')
        `).run(backupId, checksum)

        // حماية النسخة الاحتياطية من الحذف التلقائي
        await this.protectClosingBackup(backupId)

        Logger.info('ClosingBackupService', `✅ تم إنشاء نسخة احتياطية للإقفال: ${fileName}`)

        return {
          success: true,
          message: 'تم إنشاء النسخة الاحتياطية بنجاح',
          data: {
            backupId,
            fileName,
            filePath: backupPath,
            fileSize: stats.size,
            checksum
          }
        }
      } catch (error) {
        // تحديث حالة النسخة الاحتياطية إلى فاشلة
        this.db.prepare(`
          UPDATE backups 
          SET status = 'failed', error_message = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).run(error instanceof Error ? error.message : 'خطأ غير معروف', backupId)

        throw error
      }
    } catch (error) {
      Logger.error('ClosingBackupService', 'خطأ في إنشاء النسخة الاحتياطية للإقفال:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : 'حدث خطأ في إنشاء النسخة الاحتياطية'
      }
    }
  }

  /**
   * إنشاء النسخة الاحتياطية الفيزيائية
   */
  private async createPhysicalBackup(backupPath: string): Promise<void> {
    try {
      const databaseService = DatabaseService.getInstance()

      // إنشاء نسخة من قاعدة البيانات
      const data = (this.db as any).export()
      fs.writeFileSync(backupPath, data)

      // التحقق من سلامة النسخة
      const backupStats = fs.statSync(backupPath)
      if (backupStats.size === 0) {
        throw new Error('النسخة الاحتياطية فارغة - فشل في التصدير')
      }

      Logger.info('ClosingBackupService', `✅ تم إنشاء النسخة الاحتياطية الفيزيائية: ${backupPath}`)
    } catch (error) {
      Logger.error('ClosingBackupService', '❌ خطأ في إنشاء النسخة الاحتياطية الفيزيائية:', error)
      throw error
    }
  }

  /**
   * حساب checksum للملف
   */
  private async calculateChecksum(filePath: string): Promise<string> {
    try {
      const crypto = require('crypto')
      const fileBuffer = fs.readFileSync(filePath)
      const hashSum = crypto.createHash('sha256')
      hashSum.update(fileBuffer)
      return hashSum.digest('hex')
    } catch (error) {
      Logger.error('ClosingBackupService', 'خطأ في حساب checksum:', error)
      return ''
    }
  }

  /**
   * حماية النسخة الاحتياطية من الحذف التلقائي
   */
  private async protectClosingBackup(backupId: number): Promise<void> {
    try {
      // إنشاء جدول حماية النسخ الاحتياطية إذا لم يكن موجوداً
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS backup_checksums (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          backup_id INTEGER NOT NULL,
          checksum TEXT NOT NULL,
          algorithm TEXT DEFAULT 'sha256',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (backup_id) REFERENCES backups (id)
        )
      `)

      this.db.exec(`
        CREATE TABLE IF NOT EXISTS protected_backups (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          backup_id INTEGER NOT NULL,
          protection_reason TEXT NOT NULL,
          protected_until DATE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (backup_id) REFERENCES backups (id)
        )
      `)

      // حماية النسخة الاحتياطية لمدة 7 سنوات (متطلبات قانونية)
      const protectionDate = new Date()
      protectionDate.setFullYear(protectionDate.getFullYear() + 7)

      this.db.prepare(`
        INSERT INTO protected_backups (backup_id, protection_reason, protected_until)
        VALUES (?, ?, ?)
      `).run(
        backupId,
        'نسخة احتياطية للإقفال المحاسبي - محمية قانونياً',
        protectionDate.toISOString().split('T')[0]
      )

      Logger.info('ClosingBackupService', `✅ تم حماية النسخة الاحتياطية رقم ${backupId}`)
    } catch (error) {
      Logger.error('ClosingBackupService', 'خطأ في حماية النسخة الاحتياطية:', error)
    }
  }

  /**
   * الحصول على النسخ الاحتياطية للإقفال
   */
  public async getClosingBackups(periodId?: number): Promise<ClosingBackupInfo[]> {
    try {
      let query = `
        SELECT b.*, pb.protection_reason, pb.protected_until
        FROM backups b
        LEFT JOIN protected_backups pb ON b.id = pb.backup_id
        WHERE b.is_period_closing_backup = 1
      `
      const params: any[] = []

      if (periodId) {
        query += ' AND b.fiscal_period_id = ?'
        params.push(periodId)
      }

      query += ' ORDER BY b.created_at DESC'

      const backups = this.db.prepare(query).all(...params) as ClosingBackupInfo[]
      return backups
    } catch (error) {
      Logger.error('ClosingBackupService', 'خطأ في جلب النسخ الاحتياطية للإقفال:', error)
      return []
    }
  }

  /**
   * التحقق من سلامة النسخة الاحتياطية
   */
  public async verifyBackupIntegrity(backupId: number): Promise<{ isValid: boolean; message: string }> {
    try {
      // جلب معلومات النسخة الاحتياطية
      const backup = this.db.prepare(`
        SELECT b.*, bc.checksum
        FROM backups b
        LEFT JOIN backup_checksums bc ON b.id = bc.backup_id
        WHERE b.id = ?
      `).get(backupId) as any

      if (!backup) {
        return { isValid: false, message: 'النسخة الاحتياطية غير موجودة' }
      }

      // التحقق من وجود الملف
      const fullPath = path.resolve(backup.file_path)
      if (!fs.existsSync(fullPath)) {
        return { isValid: false, message: 'ملف النسخة الاحتياطية غير موجود' }
      }

      // التحقق من حجم الملف
      const stats = fs.statSync(fullPath)
      if (stats.size !== backup.file_size) {
        return { isValid: false, message: 'حجم الملف لا يطابق السجل المحفوظ' }
      }

      // التحقق من checksum إذا كان متوفراً
      if (backup.checksum) {
        const currentChecksum = await this.calculateChecksum(fullPath)
        if (currentChecksum !== backup.checksum) {
          return { isValid: false, message: 'checksum الملف لا يطابق القيمة المحفوظة' }
        }
      }

      return { isValid: true, message: 'النسخة الاحتياطية سليمة' }
    } catch (error) {
      Logger.error('ClosingBackupService', 'خطأ في التحقق من سلامة النسخة الاحتياطية:', error)
      return { isValid: false, message: 'حدث خطأ في التحقق من سلامة النسخة الاحتياطية' }
    }
  }

  /**
   * تنظيف النسخ الاحتياطية القديمة (مع الحفاظ على نسخ الإقفال)
   */
  public async cleanupOldBackups(keepDays: number = 30): Promise<{ deleted: number; protected: number }> {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - keepDays)

      // جلب النسخ القديمة غير المحمية
      const oldBackups = this.db.prepare(`
        SELECT b.id, b.file_path
        FROM backups b
        LEFT JOIN protected_backups pb ON b.id = pb.backup_id
        WHERE b.created_at < ? 
          AND b.is_period_closing_backup = 0
          AND pb.id IS NULL
      `).all(cutoffDate.toISOString())

      let deletedCount = 0
      let protectedCount = 0

      for (const backup of oldBackups) {
        try {
          // حذف الملف الفيزيائي
          if (fs.existsSync((backup as any).file_path)) {
            fs.unlinkSync((backup as any).file_path)
          }

          // حذف السجل من قاعدة البيانات
          this.db.prepare('DELETE FROM backups WHERE id = ?').run((backup as any).id)
          deletedCount++
        } catch (error) {
          Logger.error('ClosingBackupService', `خطأ في حذف النسخة الاحتياطية ${(backup as any).id}:`, error)
        }
      }

      // عد النسخ المحمية
      const protectedBackups = this.db.prepare(`
        SELECT COUNT(*) as count
        FROM backups b
        JOIN protected_backups pb ON b.id = pb.backup_id
        WHERE b.created_at < ?
      `).get(cutoffDate.toISOString()) as { count: number }

      protectedCount = protectedBackups.count

      Logger.info('ClosingBackupService', `تنظيف النسخ الاحتياطية: حذف ${deletedCount}، محمي ${protectedCount}`)

      return { deleted: deletedCount, protected: protectedCount }
    } catch (error) {
      Logger.error('ClosingBackupService', 'خطأ في تنظيف النسخ الاحتياطية:', error)
      return { deleted: 0, protected: 0 }
    }
  }

  /**
   * ترجمة نوع الإقفال إلى العربية
   */
  private getClosingTypeArabic(closingType: string): string {
    const types = {
      monthly: 'شهري',
      quarterly: 'ربع سنوي',
      semi_annual: 'نصف سنوي',
      annual: 'سنوي'
    }
    return types[closingType as keyof typeof types] || closingType
  }
}
