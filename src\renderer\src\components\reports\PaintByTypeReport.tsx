import React from 'react';
import { Tag, Typography } from 'antd';
import UniversalReport from './UniversalReport';
import { DateUtils } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger';
import type { ReportData, ReportType } from '../../types/reports';

const { Text } = Typography;

const PaintByTypeReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getPaintByTypeReport({
        paintTypeId: filters.paintTypeId,
        dateRange: filters.dateRange
      });

      if (!response || !response.success || !response.data || !Array.isArray(response.data.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const paintData = response.data;
      const data = paintData.data;

      // حساب الإحصائيات
      const totalTypes = data.length;
      const totalOrders = data.reduce((sum, item) => sum + item.total_orders, 0);
      const totalArea = data.reduce((sum, item) => sum + item.total_area, 0);
      const totalQuantity = data.reduce((sum, item) => sum + item.total_quantity, 0);
      const totalRevenue = data.reduce((sum, item) => sum + item.total_revenue, 0);
      const totalCustomers = data.reduce((sum, item) => sum + item.unique_customers, 0);

      // إعداد البيانات للجدول
      const tableData = data.map((item, index) => ({
        key: item.paint_type_id,
        index: index + 1,
        paint_type_code: item.paint_type_code,
        paint_type_name: item.paint_type_name,
        color: item.color || '-',
        finish_type: item.finish_type || '-',
        price_per_sqm: item.price_per_sqm,
        total_orders: item.total_orders,
        total_area: item.total_area,
        total_quantity: item.total_quantity,
        total_revenue: item.total_revenue,
        avg_price: item.avg_price,
        unique_customers: item.unique_customers,
        revenue_percentage: totalRevenue > 0 ? (item.total_revenue / totalRevenue * 100) : 0,
        area_percentage: totalArea > 0 ? (item.total_area / totalArea * 100) : 0
      }));

      // تعريف الأعمدة
      const columns = [
        {
          title: '#',

          key: 'index',
          width: 50,
          align: 'center' as const
        },
        {
          title: 'كود النوع',

          key: 'paint_type_code',
          width: 100
        },
        {
          title: 'اسم نوع الدهان',

          key: 'paint_type_name',
          width: 150
        },
        {
          title: 'اللون',

          key: 'color',
          width: 100,
          render: (record: any) => (
            <Tag color={record.color !== '-' ? 'blue' : 'default'}>{record.color}</Tag>
          )
        },
        {
          title: 'نوع التشطيب',

          key: 'finish_type',
          width: 120,
          render: (record: any) => (
            <Tag color={record.finish_type !== '-' ? 'green' : 'default'}>{record.finish_type}</Tag>
          )
        },
        {
          title: 'السعر/م²',

          sortable: true,
          key: 'price_per_sqm',
          width: 100,
          align: 'right' as const,
          render: (record: any) => (
            <Text strong style={{ color: '#1890ff' }}>
              {record.price_per_sqm.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'عدد الأوامر',

          sortable: true,
          key: 'total_orders',
          width: 100,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="blue">{record.total_orders}</Tag>
          )
        },
        {
          title: 'إجمالي المساحة (م²)',

          sortable: true,
          key: 'total_area',
          width: 130,
          align: 'right' as const,
          render: (record: any) => record.total_area.toLocaleString('ar-EG', { minimumFractionDigits: 2 })
        },
        {
          title: 'إجمالي الكمية',

          sortable: true,
          key: 'total_quantity',
          width: 120,
          align: 'right' as const,
          render: (record: any) => record.total_quantity.toLocaleString('ar-EG')
        },
        {
          title: 'إجمالي الإيرادات',

          sortable: true,
          key: 'total_revenue',
          width: 130,
          align: 'right' as const,
          render: (record: any) => (
            <Text strong style={{ color: '#52c41a' }}>
              {record.total_revenue.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'متوسط السعر',

          sortable: true,
          key: 'avg_price',
          width: 120,
          align: 'right' as const,
          render: (record: any) => (
            <Text style={{ color: '#722ed1' }}>
              {record.avg_price.toLocaleString('ar-EG', { minimumFractionDigits: 2 })} ج.م
            </Text>
          )
        },
        {
          title: 'عدد العملاء',

          sortable: true,
          key: 'unique_customers',
          width: 100,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="orange">{record.unique_customers}</Tag>
          )
        },
        {
          title: 'نسبة الإيرادات',

          sortable: true,
          key: 'revenue_percentage',
          width: 120,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="cyan">{record.revenue_percentage.toFixed(1)}%</Tag>
          )
        },
        {
          title: 'نسبة المساحة',

          sortable: true,
          key: 'area_percentage',
          width: 120,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="purple">{record.area_percentage.toFixed(1)}%</Tag>
          )
        }
      ];

      // إعداد الإحصائيات
      const statistics = [
        {
          title: 'إجمالي أنواع الدهان',
          value: totalTypes,
          color: '#1890ff',
          icon: '🎨'
        },
        {
          title: 'إجمالي الأوامر',
          value: totalOrders,
          color: '#52c41a',
          icon: '📋'
        },
        {
          title: 'إجمالي المساحة (م²)',
          value: totalArea.toLocaleString('ar-EG', { minimumFractionDigits: 2 }),
          color: '#fa8c16',
          icon: '📐'
        },
        {
          title: 'إجمالي الكمية',
          value: totalQuantity.toLocaleString('ar-EG'),
          color: '#722ed1',
          icon: '📦'
        },
        {
          title: 'إجمالي الإيرادات',
          value: `${totalRevenue.toLocaleString('ar-EG')} ج.م`,
          color: '#13c2c2',
          icon: '💰'
        },
        {
          title: 'إجمالي العملاء',
          value: totalCustomers,
          color: '#eb2f96',
          icon: '👥'
        },
        {
          title: 'متوسط الإيرادات/نوع',
          value: totalTypes > 0 ? `${Math.round(totalRevenue / totalTypes).toLocaleString('ar-EG')} ج.م` : '0 ج.م',
          color: '#f759ab',
          icon: '📊'
        },
        {
          title: 'متوسط المساحة/أمر',
          value: totalOrders > 0 ? `${(totalArea / totalOrders).toFixed(2)} م²` : '0 م²',
          color: '#40a9ff',
          icon: '📏'
        }
      ];

      return {
        title: 'تقرير الدهان حسب النوع',
        columns,
        data: tableData,
        statistics,
        summary: {
          totalRecords: totalTypes,
          totalRevenue: totalRevenue,
          totalArea: totalArea,
          totalQuantity: totalQuantity
        }
      };

    } catch (error) {
      Logger.error('PaintByTypeReport', 'خطأ في إنشاء تقرير الدهان حسب النوع:', error);
      throw new Error('فشل في تحميل بيانات التقرير');
    }
  };

  return (
    <UniversalReport
      reportType={'paint_by_type' as ReportType}
      title="تقرير الدهان حسب النوع"
      description="تقرير تفصيلي للدهان مجمع حسب النوع مع الإحصائيات"
      onGenerateReport={generateReport}
      showDateRange={true}
    />
  );
};

export default PaintByTypeReport;
