/**
 * مراقب أداء التقارير
 * مكون لعرض مقاييس الأداء والإحصائيات
 */

import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Tooltip,
  Alert
} from 'antd'
import {
  DashboardOutlined,
  ClockCircleOutlined,
  DatabaseOutlined,
  HddOutlined,
  ReloadOutlined,
  ClearOutlined,
  SettingOutlined
} from '@ant-design/icons'
import { UniversalReportService } from '../../services/UniversalReportService'
import { ReportType } from '../../types/reports'
import { SafeLogger as Logger } from '../../utils/logger'

const { Title, Text } = Typography

interface PerformanceData {
  reportType: string
  loadTime: number
  renderTime: number
  dataSize: number
  cacheHitRate: number
  memoryUsage: number
}

const ReportPerformanceMonitor: React.FC = () => {
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([])
  const [loading, setLoading] = useState(false)
  const [totalCacheSize, setTotalCacheSize] = useState(0)
  const [averageLoadTime, setAverageLoadTime] = useState(0)
  const [totalMemoryUsage, setTotalMemoryUsage] = useState(0)

  const reportService = UniversalReportService.getInstance()

  // تحميل بيانات الأداء
  const loadPerformanceData = async () => {
    try {
      setLoading(true)
      Logger.info('ReportPerformanceMonitor', 'تحميل بيانات الأداء...')

      const metrics = reportService.getPerformanceMetrics()
      
      if (metrics instanceof Map) {
        const data: PerformanceData[] = []
        let totalLoad = 0
        let totalMemory = 0
        
        metrics.forEach((metric, reportType) => {
          data.push({
            reportType,
            loadTime: metric.loadTime,
            renderTime: metric.renderTime,
            dataSize: metric.dataSize,
            cacheHitRate: metric.cacheHitRate,
            memoryUsage: metric.memoryUsage
          })
          
          totalLoad += metric.loadTime
          totalMemory += metric.memoryUsage
        })
        
        setPerformanceData(data)
        setAverageLoadTime(data.length > 0 ? totalLoad / data.length : 0)
        setTotalMemoryUsage(totalMemory)
        setTotalCacheSize(data.length)
      }
    } catch (error) {
      Logger.error('ReportPerformanceMonitor', 'خطأ في تحميل بيانات الأداء:', error)
    } finally {
      setLoading(false)
    }
  }

  // تنظيف الكاش
  const clearCache = () => {
    reportService.clearCache()
    setPerformanceData([])
    setTotalCacheSize(0)
    setAverageLoadTime(0)
    setTotalMemoryUsage(0)
    Logger.info('ReportPerformanceMonitor', 'تم تنظيف الكاش')
  }

  // تحديث إعدادات الأداء
  const updatePerformanceSettings = () => {
    reportService.updatePerformanceSettings({
      maxCacheSize: 100,
      cacheTimeout: 15 * 60 * 1000, // 15 دقيقة
      compressionEnabled: true,
      lazyLoadingEnabled: true
    })
    Logger.info('ReportPerformanceMonitor', 'تم تحديث إعدادات الأداء')
  }

  useEffect(() => {
    loadPerformanceData()
    
    // تحديث دوري كل 30 ثانية
    const interval = setInterval(loadPerformanceData, 30000)
    
    return () => clearInterval(interval)
  }, [])

  // أعمدة جدول الأداء
  const columns = [
    {
      title: 'نوع التقرير',
      dataIndex: 'reportType',
      key: 'reportType',
      render: (type: string) => (
        <Tag color="blue">{getReportTitle(type as ReportType)}</Tag>
      )
    },
    {
      title: 'وقت التحميل',
      dataIndex: 'loadTime',
      key: 'loadTime',
      render: (time: number) => (
        <span>
          <ClockCircleOutlined style={{ marginLeft: '4px' }} />
          {time.toFixed(2)}ms
        </span>
      ),
      sorter: (a: PerformanceData, b: PerformanceData) => a.loadTime - b.loadTime
    },
    {
      title: 'حجم البيانات',
      dataIndex: 'dataSize',
      key: 'dataSize',
      render: (size: number) => (
        <span>
          <DatabaseOutlined style={{ marginLeft: '4px' }} />
          {size.toLocaleString()} سجل
        </span>
      ),
      sorter: (a: PerformanceData, b: PerformanceData) => a.dataSize - b.dataSize
    },
    {
      title: 'استخدام الذاكرة',
      dataIndex: 'memoryUsage',
      key: 'memoryUsage',
      render: (usage: number) => (
        <span>
          <HddOutlined style={{ marginLeft: '4px' }} />
          {(usage / 1024).toFixed(2)} KB
        </span>
      ),
      sorter: (a: PerformanceData, b: PerformanceData) => a.memoryUsage - b.memoryUsage
    },
    {
      title: 'معدل إصابة الكاش',
      dataIndex: 'cacheHitRate',
      key: 'cacheHitRate',
      render: (rate: number) => (
        <Progress
          percent={Math.round(rate)}
          size="small"
          status={rate > 80 ? 'success' : rate > 50 ? 'normal' : 'exception'}
        />
      ),
      sorter: (a: PerformanceData, b: PerformanceData) => a.cacheHitRate - b.cacheHitRate
    }
  ]

  // الحصول على عنوان التقرير
  const getReportTitle = (type: ReportType): string => {
    const titles: Record<ReportType, string> = {
      inventory_detailed: 'المخزون التفصيلي',
      inventory_movements: 'حركات المخزون',
      inventory_audit: 'جرد المخزون',
      material_consumption: 'استهلاك المواد',
      closing_summary: 'ملخص الإقفال',
      closing_entries: 'قيود الإقفال',
      carried_forward_balances: 'الأرصدة المرحلة',
      audit_log: 'سجل المراجعة',
      period_comparison: 'مقارنة الفترات',
      closing_checklist: 'قائمة مراجعة الإقفال',
      low_stock: 'الأصناف المنخفضة',
      item_warehouse_distribution: 'توزيع الأصناف',
      abc_analysis: 'تحليل ABC',
      purchases_by_supplier: 'المشتريات حسب المورد',
      purchases_by_item: 'المشتريات حسب الصنف',
      supplier_payables: 'مستحقات الموردين',
      purchase_analysis: 'تحليل المشتريات',
      cost_analysis: 'تحليل التكاليف',
      supplier_price_comparison: 'مقارنة أسعار الموردين',
      supplier_quality: 'جودة الموردين',
      supplier_analysis: 'تحليل الموردين',
      purchase_performance: 'أداء المشتريات',
      sales_by_customer: 'المبيعات حسب العميل',
      sales_by_product: 'المبيعات حسب المنتج',
      sales_by_region: 'المبيعات حسب المنطقة',
      monthly_sales: 'المبيعات الشهرية',
      sales_returns: 'مرتجعات المبيعات',
      top_profitable_customers: 'أفضل العملاء ربحية',
      profitability: 'الربحية',
      employee_attendance: 'حضور الموظفين',
      employee_payroll: 'رواتب الموظفين',
      employee_leaves: 'إجازات الموظفين',
      employee_performance: 'أداء الموظفين',
      employee_overtime: 'ساعات إضافية',
      employee_analysis: 'تحليل الموظفين',
      salary_comparison: 'مقارنة الرواتب',
      efficiency_evaluation: 'تقييم الكفاءة',
      production_orders: 'أوامر الإنتاج',
      production_efficiency: 'كفاءة الإنتاج',
      production_costs: 'تكاليف الإنتاج',
      production_schedule: 'جدولة الإنتاج',
      production_quality: 'جودة الإنتاج',
      production_workers_performance: 'أداء عمال الإنتاج',
      production_materials_consumption: 'استهلاك مواد الإنتاج',
      production_profitability: 'ربحية الإنتاج',
      paint_by_customer: 'الدهان حسب العميل',
      paint_by_type: 'الدهان حسب النوع',
      monthly_paint: 'الدهان الشهري',
      paint_profitability: 'ربحية الدهان',
      paint_performance: 'أداء الدهان',
      paint_quality: 'جودة الدهان',
      balance_sheet: 'الميزانية العمومية',
      income_statement: 'قائمة الدخل',
      cash_flow: 'التدفقات النقدية',
      cash_flow_analysis: 'تحليل التدفق النقدي',
      profit_loss: 'الأرباح والخسائر',
      bank_reconciliation: 'تسوية البنك',
      customer_aging: 'أعمار الذمم',
      departments_integration: 'تكامل الأقسام'
    }
    return titles[type] || type
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* رأس المراقب */}
      <Card style={{ marginBottom: '16px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              <DashboardOutlined style={{ marginLeft: '8px' }} />
              مراقب أداء التقارير
            </Title>
            <Text type="secondary">
              مراقبة وتحليل أداء نظام التقارير في الوقت الفعلي
            </Text>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadPerformanceData}
                loading={loading}
              >
                تحديث
              </Button>
              <Button
                icon={<ClearOutlined />}
                onClick={clearCache}
                danger
              >
                تنظيف الكاش
              </Button>
              <Button
                icon={<SettingOutlined />}
                onClick={updatePerformanceSettings}
              >
                تحسين الإعدادات
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* إحصائيات عامة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="إجمالي التقارير المحفوظة"
              value={totalCacheSize}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="متوسط وقت التحميل"
              value={averageLoadTime.toFixed(2)}
              suffix="ms"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="إجمالي استخدام الذاكرة"
              value={(totalMemoryUsage / 1024).toFixed(2)}
              suffix="KB"
              prefix={<HddOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="حالة النظام"
              value={averageLoadTime < 1000 ? 'ممتاز' : averageLoadTime < 3000 ? 'جيد' : 'يحتاج تحسين'}
              prefix={<DashboardOutlined />}
              valueStyle={{ 
                color: averageLoadTime < 1000 ? '#52c41a' : 
                       averageLoadTime < 3000 ? '#1890ff' : '#ff4d4f' 
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* تنبيهات الأداء */}
      {averageLoadTime > 3000 && (
        <Alert
          message="تحذير: أداء بطيء"
          description="متوسط وقت تحميل التقارير أعلى من المعدل الطبيعي. يُنصح بتنظيف الكاش أو تحسين الإعدادات."
          type="warning"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      {totalMemoryUsage > 50 * 1024 * 1024 && (
        <Alert
          message="تحذير: استخدام ذاكرة عالي"
          description="استخدام الذاكرة مرتفع. يُنصح بتنظيف الكاش لتحرير الذاكرة."
          type="error"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* جدول تفاصيل الأداء */}
      <Card title="تفاصيل أداء التقارير">
        <Table
          columns={columns}
          dataSource={performanceData}
          rowKey="reportType"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => (
              `عرض ${range[0]}-${range[1]} من أصل ${total} تقرير`
            )
          }}
          scroll={{ x: 'max-content' }}
          size="middle"
        />
      </Card>
    </div>
  )
}

export default ReportPerformanceMonitor
