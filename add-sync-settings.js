const fs = require('fs');
const path = require('path');
const initSqlJs = require('sql.js');

async function addSyncSettings() {
  try {
    console.log('🔧 إضافة إعدادات المزامنة إلى قاعدة البيانات...\n');

    // مسار قاعدة البيانات
    const dbPath = path.join(process.env.APPDATA, 'ZET.IA', 'database.db');
    console.log(`📍 مسار قاعدة البيانات: ${dbPath}`);

    // التحقق من وجود الملف
    if (!fs.existsSync(dbPath)) {
      console.log('❌ ملف قاعدة البيانات غير موجود!');
      return;
    }

    // التحقق من حجم الملف
    const stats = fs.statSync(dbPath);
    console.log(`📊 حجم قاعدة البيانات: ${(stats.size / 1024).toFixed(2)} KB`);

    if (stats.size === 0) {
      console.log('❌ ملف قاعدة البيانات فارغ!');
      return;
    }
    
    // تهيئة SQL.js
    const SQL = await initSqlJs({
      locateFile: file => path.join(__dirname, 'node_modules', 'sql.js', 'dist', file)
    });
    
    // قراءة قاعدة البيانات
    const filebuffer = fs.readFileSync(dbPath);
    const db = new SQL.Database(filebuffer);
    
    console.log('✅ تم تحميل قاعدة البيانات بنجاح\n');
    
    // إعدادات المزامنة المطلوب إضافتها
    const syncSettings = [
      { key: 'sync_enabled', value: 'false', description: 'تفعيل المزامنة بين الأجهزة' },
      { key: 'sync_device_role', value: 'branch', description: 'دور الجهاز في المزامنة (main/branch)' },
      { key: 'sync_shared_folder', value: '', description: 'مسار المجلد المشترك للمزامنة' },
      { key: 'sync_interval', value: '5', description: 'فترة المزامنة التلقائية (دقائق)' },
      { key: 'sync_auto_discover', value: 'true', description: 'البحث التلقائي عن الأجهزة' },
      { key: 'sync_device_name', value: '', description: 'اسم الجهاز في الشبكة' },
      { key: 'sync_last_sync', value: '', description: 'تاريخ آخر مزامنة' }
    ];
    
    console.log('📝 إضافة إعدادات المزامنة:');
    let addedCount = 0;
    
    for (const setting of syncSettings) {
      try {
        // التحقق من وجود الإعداد
        const existing = db.exec(`SELECT id FROM settings WHERE key = '${setting.key}'`);
        
        if (!existing.length || !existing[0].values || existing[0].values.length === 0) {
          // إضافة الإعداد الجديد
          const stmt = db.prepare(`
            INSERT INTO settings (key, value, description, created_at, updated_at) 
            VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          `);
          stmt.bind([setting.key, setting.value, setting.description]);
          stmt.step();
          stmt.free();
          
          addedCount++;
          console.log(`   ✅ تم إضافة: ${setting.key} = ${setting.value}`);
        } else {
          console.log(`   ⚠️  موجود بالفعل: ${setting.key}`);
        }
      } catch (error) {
        console.log(`   ❌ خطأ في إضافة ${setting.key}: ${error.message}`);
      }
    }
    
    console.log(`\n📊 تم إضافة ${addedCount} إعداد جديد من أصل ${syncSettings.length}`);
    
    // حفظ التغييرات فقط إذا تم إضافة إعدادات جديدة
    if (addedCount > 0) {
      console.log('\n💾 حفظ التغييرات في قاعدة البيانات...');
      const data = db.export();

      // إنشاء نسخة احتياطية إضافية
      const backupPath = dbPath + '.backup-' + Date.now();
      fs.copyFileSync(dbPath, backupPath);
      console.log(`📋 تم إنشاء نسخة احتياطية: ${path.basename(backupPath)}`);

      // حفظ قاعدة البيانات المحدثة
      fs.writeFileSync(dbPath, data);
      console.log('✅ تم حفظ التغييرات بنجاح');
    } else {
      console.log('\n⚠️  لم يتم إجراء أي تغييرات - جميع الإعدادات موجودة بالفعل');
    }
    
    // التحقق من النتيجة
    console.log('\n🔍 التحقق من الإعدادات المضافة:');
    const syncSettingsCheck = db.exec("SELECT key, value FROM settings WHERE key LIKE 'sync_%'");
    
    if (syncSettingsCheck.length && syncSettingsCheck[0].values) {
      syncSettingsCheck[0].values.forEach(setting => {
        console.log(`   ${setting[0]}: ${setting[1]}`);
      });
    } else {
      console.log('   ❌ لم يتم العثور على إعدادات المزامنة');
    }
    
    db.close();
    console.log('\n✅ تم إنهاء العملية بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في إضافة إعدادات المزامنة:', error);
  }
}

// تشغيل العملية
addSyncSettings();
