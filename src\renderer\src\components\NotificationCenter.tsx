import React, { useState, useEffect, useCallback } from 'react'
import {
  Drawer, List, Avatar, Typography, Space, Button, Badge, Empty,
  Spin, Select, DatePicker, Input,
  Tag, Tooltip, Popconfirm, Row, Col, Statistic, App
} from 'antd'
import { SafeLogger as Logger } from '../utils/logger'
import {
  BellOutlined, CheckOutlined, EyeOutlined,
  FilterOutlined, ReloadOutlined, SettingOutlined,
  InfoCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined,
  CloseCircleOutlined, ClearOutlined, FileTextOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/ar'
import NotificationSettings from './NotificationSettings'

dayjs.extend(relativeTime)
dayjs.locale('ar')

const { Text, Paragraph } = Typography
const { RangePicker } = DatePicker
const { Search } = Input

const StyledDrawer = styled(Drawer)`
  .ant-drawer-header {
    background: linear-gradient(135deg, #0078D4 0%, #106EBE 100%);
    border-bottom: none;
    
    .ant-drawer-title {
      color: white;
      font-weight: bold;
    }
    
    .ant-drawer-close {
      color: white;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
  
  .ant-drawer-body {
    padding: 0;
  }
`

const NotificationItem = styled(List.Item)`
  padding: 16px 20px !important;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: #f8f9fa;
  }
  
  &.unread {
    background: #f6ffed;
    border-left: 4px solid #52c41a;
  }
  
  &.read {
    opacity: 0.7;
  }
  
  .notification-content {
    flex: 1;
  }
  
  .notification-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover .notification-actions {
    opacity: 1;
  }
`

const FilterSection = styled.div`
  padding: 16px 20px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  
  .filter-row {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .filter-label {
    min-width: 60px;
    font-weight: 500;
    color: #666;
  }
`

const StatsSection = styled.div`
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
`

interface Notification {
  id: number
  user_id?: number
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  category: string
  data?: any
  is_read: boolean
  is_system: boolean
  expires_at?: string
  created_at: string
  user_name?: string
}

interface NotificationCenterProps {
  visible: boolean
  onClose: () => void
  currentUser: any
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  visible,
  onClose,
  currentUser
}) => {
  const { message: messageApi } = App.useApp()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(false)
  const [filters, setFilters] = useState({
    type: '',
    category: '',
    is_read: '',
    search: '',
    dateRange: null as any
  })
  const [showFilters, setShowFilters] = useState(false)
  const [stats, setStats] = useState({
    total: 0,
    unread: 0,
    today: 0
  })
  const [settingsVisible, setSettingsVisible] = useState(false)

  const loadNotifications = useCallback(async () => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        const filterParams: any = {
          user_id: currentUser.id,
          limit: 100
        }

        if (filters.type) filterParams.type = filters.type
        if (filters.category) filterParams.category = filters.category
        if (filters.is_read !== '') filterParams.is_read = filters.is_read === 'true'

        const result = await window.electronAPI.getNotifications(filterParams)
        if (result.success) {
          let filteredNotifications = result.data || []

          // تطبيق فلتر البحث النصي
          if (filters.search) {
            const searchTerm = filters.search.toLowerCase()
            filteredNotifications = filteredNotifications.filter((notif: Notification) =>
              notif.title.toLowerCase().includes(searchTerm) ||
              notif.message.toLowerCase().includes(searchTerm)
            )
          }

          // تطبيق فلتر التاريخ
          if (filters.dateRange && filters.dateRange.length === 2) {
            const [startDate, endDate] = filters.dateRange
            filteredNotifications = filteredNotifications.filter((notif: Notification) =>
              dayjs(notif.created_at).isAfter(startDate.startOf('day')) &&
              dayjs(notif.created_at).isBefore(endDate.endOf('day'))
            )
          }

          setNotifications(filteredNotifications)
          
          // حساب الإحصائيات
          const unreadCount = filteredNotifications.filter((n: Notification) => !n.is_read).length
          const todayCount = filteredNotifications.filter((n: Notification) =>
            dayjs(n.created_at).isAfter(dayjs().startOf('day'))
          ).length

          setStats({
            total: filteredNotifications.length,
            unread: unreadCount,
            today: todayCount
          })
        } else {
          messageApi.error(result.message || 'فشل في تحميل الإشعارات')
        }
      } else {
        // بيانات وهمية للتطوير
        const mockNotifications: Notification[] = [
          {
            id: 1,
            title: 'مخزون منخفض',
            message: 'الصنف "كرسي مكتب" وصل إلى الحد الأدنى للمخزون (5 قطع)',
            type: 'warning',
            category: 'inventory',
            is_read: false,
            is_system: true,
            created_at: dayjs().subtract(1, 'hour').toISOString()
          },
          {
            id: 2,
            title: 'فاتورة مستحقة',
            message: 'فاتورة رقم #2024-001 مستحقة خلال 3 أيام',
            type: 'info',
            category: 'finance',
            is_read: true,
            is_system: true,
            created_at: dayjs().subtract(2, 'hours').toISOString()
          }
        ]
        setNotifications(mockNotifications)
        setStats({
          total: mockNotifications.length,
          unread: mockNotifications.filter(n => !n.is_read).length,
          today: mockNotifications.length
        })
      }
    } catch (error) {
      Logger.error('NotificationCenter', 'خطأ في تحميل الإشعارات:', error)
      messageApi.error('حدث خطأ في تحميل الإشعارات')
    } finally {
      setLoading(false)
    }
  }, [currentUser.id, filters.category, filters.dateRange, filters.is_read, filters.search, filters.type, messageApi])

  useEffect(() => {
    if (visible) {
      loadNotifications()
    }
  }, [visible, loadNotifications])

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'warning': return <ExclamationCircleOutlined style={{ color: '#faad14' }} />
      case 'error': return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
      default: return <InfoCircleOutlined style={{ color: '#1890ff' }} />
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success': return '#52c41a'
      case 'warning': return '#faad14'
      case 'error': return '#ff4d4f'
      default: return '#1890ff'
    }
  }

  const getCategoryName = (category: string) => {
    const categories: { [key: string]: string } = {
      general: 'عام',
      inventory: 'المخزون',
      sales: 'المبيعات',
      purchases: 'المشتريات',
      finance: 'المالية',
      production: 'الإنتاج',
      employees: 'الموّفين'
    }
    return categories[category] || category
  }

  const handleMarkAsRead = async (notificationId: number, isRead: boolean) => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.markNotificationRead(notificationId, isRead)
        if (result.success) {
          setNotifications(prev =>
            prev.map(notif =>
              notif.id === notificationId ? { ...notif, is_read: isRead } : notif
            )
          )
          messageApi.success(isRead ? 'تم تحديد الإشعار كمقروء' : 'تم تحديد الإشعار كغير مقروء')
        } else {
          messageApi.error(result.message || 'فشل في تحديث الإشعار')
        }
      }
    } catch (error) {
      Logger.error('NotificationCenter', 'خطأ في تحديث الإشعار:', error)
      messageApi.error('حدث خطأ في تحديث الإشعار')
    }
  }

  const handleMarkAllAsRead = async () => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.markAllNotificationsRead(currentUser.id)
        if (result.success) {
          setNotifications(prev =>
            prev.map(notif => ({ ...notif, is_read: true }))
          )
          setStats(prev => ({ ...prev, unread: 0 }))
          messageApi.success('تم تحديد جميع الإشعارات كمقروءة')
        } else {
          messageApi.error(result.message || 'فشل في تحديث الإشعارات')
        }
      }
    } catch (error) {
      Logger.error('NotificationCenter', 'خطأ في تحديث الإشعارات:', error)
      messageApi.error('حدث خطأ في تحديث الإشعارات')
    }
  }

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      type: '',
      category: '',
      is_read: '',
      search: '',
      dateRange: null
    })
  }

  return (
    <StyledDrawer
      title={
        <Space>
          <BellOutlined />
          مركز الإشعارات
          {stats.unread > 0 && (
            <Badge count={stats.unread} style={{ backgroundColor: '#52c41a' }} />
          )}
        </Space>
      }
      placement="left"
      width={450}
      open={visible}
      onClose={onClose}
      extra={
        <Space>
          <Tooltip title="إعدادات الإشعارات">
            <Button
              type="text"
              icon={<SettingOutlined />}
              style={{ color: 'white' }}
              onClick={() => setSettingsVisible(true)}
            />
          </Tooltip>
          <Tooltip title="تحديث">
            <Button 
              type="text" 
              icon={<ReloadOutlined />} 
              loading={loading}
              onClick={loadNotifications}
              style={{ color: 'white' }}
            />
          </Tooltip>
        </Space>
      }
    >
      {/* قسم الإحصائيات */}
      <StatsSection>
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="الإجمالي"
              value={stats.total}
              prefix={<BellOutlined />}
              valueStyle={{ fontSize: 16, color: '#1890ff' }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="غير مقروءة"
              value={stats.unread}
              prefix={<EyeOutlined />}
              valueStyle={{ fontSize: 16, color: '#faad14' }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="اليوم"
              value={stats.today}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ fontSize: 16, color: '#52c41a' }}
            />
          </Col>
        </Row>
        
        {stats.unread > 0 && (
          <div style={{ marginTop: 12, textAlign: 'center' }}>
            <Popconfirm
              title="هل تريد تحديد جميع الإشعارات كمقروءة؟"
              onConfirm={handleMarkAllAsRead}
              okText="نعم"
              cancelText="لا"
            >
              <Button type="primary" size="small" icon={<CheckOutlined />}>
                تحديد الكل كمقروء
              </Button>
            </Popconfirm>
          </div>
        )}
      </StatsSection>

      {/* قسم الفلاتر */}
      <FilterSection>
        <div className="filter-row">
          <Button
            type={showFilters ? 'primary' : 'default'}
            icon={<FilterOutlined />}
            onClick={() => setShowFilters(!showFilters)}
            size="small"
          >
            فلترة
          </Button>
          
          <Search
            placeholder="البحث في الإشعارات..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            style={{ flex: 1 }}
            size="small"
          />
          
          {(filters.type || filters.category || filters.is_read || filters.search || filters.dateRange) && (
            <Button
              type="text"
              icon={<ClearOutlined />}
              onClick={clearFilters}
              size="small"
            >
              مسح
            </Button>
          )}
        </div>

        {showFilters && (
          <>
            <div className="filter-row">
              <span className="filter-label">النوع:</span>
              <Select
                value={filters.type}
                onChange={(value) => handleFilterChange('type', value)}
                placeholder="جميع الأنواع"
                style={{ width: 120 }}
                size="small"
                allowClear
              >
                <Select.Option value="info">معلومات</Select.Option>
                <Select.Option value="success">نجاح</Select.Option>
                <Select.Option value="warning">تحذير</Select.Option>
                <Select.Option value="error">خطأ</Select.Option>
              </Select>

              <span className="filter-label">الفئة:</span>
              <Select
                value={filters.category}
                onChange={(value) => handleFilterChange('category', value)}
                placeholder="جميع الفئات"
                style={{ width: 120 }}
                size="small"
                allowClear
              >
                <Select.Option value="general">عام</Select.Option>
                <Select.Option value="inventory">المخزون</Select.Option>
                <Select.Option value="sales">المبيعات</Select.Option>
                <Select.Option value="purchases">المشتريات</Select.Option>
                <Select.Option value="finance">المالية</Select.Option>
                <Select.Option value="production">الإنتاج</Select.Option>
              </Select>
            </div>

            <div className="filter-row">
              <span className="filter-label">الحالة:</span>
              <Select
                value={filters.is_read}
                onChange={(value) => handleFilterChange('is_read', value)}
                placeholder="جميع الحالات"
                style={{ width: 120 }}
                size="small"
                allowClear
              >
                <Select.Option value="false">غير مقروءة</Select.Option>
                <Select.Option value="true">مقروءة</Select.Option>
              </Select>

              <span className="filter-label">التاريخ:</span>
              <RangePicker
                value={filters.dateRange}
                onChange={(dates) => handleFilterChange('dateRange', dates)}
                style={{ width: 200 }}
                size="small"
                placeholder={['من', 'إلى']}
              />
            </div>
          </>
        )}
      </FilterSection>

      {/* قائمة الإشعارات */}
      <div style={{ flex: 1, overflow: 'auto' }}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>جاري تحميل الإشعارات...</div>
          </div>
        ) : notifications.length === 0 ? (
          <Empty
            description="لا توجد إشعارات"
            style={{ marginTop: 50 }}
          />
        ) : (
          <List
            dataSource={notifications}
            renderItem={(notification) => (
              <NotificationItem
                className={notification.is_read ? 'read' : 'unread'}
                onClick={() => handleMarkAsRead(notification.id, !notification.is_read)}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      icon={getNotificationIcon(notification.type)}
                      style={{ backgroundColor: getNotificationColor(notification.type) }}
                    />
                  }
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Text strong={!notification.is_read}>
                        {notification.title}
                      </Text>
                      <Space>
                        <Tag color={getNotificationColor(notification.type)}>
                          {getCategoryName(notification.category)}
                        </Tag>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {dayjs(notification.created_at).fromNow()}
                        </Text>
                      </Space>
                    </div>
                  }
                  description={
                    <div className="notification-content">
                      <Paragraph
                        ellipsis={{ rows: 2, expandable: true, symbol: 'المزيد' }}
                        style={{ margin: 0, color: notification.is_read ? '#999' : '#666' }}
                      >
                        {notification.message}
                      </Paragraph>
                      
                      {notification.data && (
                        <div style={{ marginTop: 8 }}>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            معلومات إضافية متاحة
                          </Text>
                        </div>
                      )}
                    </div>
                  }
                />
                
                <div className="notification-actions">
                  <Tooltip title={notification.is_read ? 'تحديد كغير مقروء' : 'تحديد كمقروء'}>
                    <Button
                      type="text"
                      size="small"
                      icon={notification.is_read ? <FileTextOutlined /> : <CheckOutlined />}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleMarkAsRead(notification.id, !notification.is_read)
                      }}
                    />
                  </Tooltip>
                </div>
              </NotificationItem>
            )}
          />
        )}
      </div>

      {/* إعدادات الإشعارات */}
      <NotificationSettings
        visible={settingsVisible}
        onClose={() => setSettingsVisible(false)}
        currentUser={currentUser}
      />
    </StyledDrawer>
  )
}

export default NotificationCenter
