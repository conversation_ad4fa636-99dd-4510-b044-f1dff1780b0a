const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// سكريبت after-pack محسن لنسخ sql.js والمكتبات المطلوبة
module.exports = async function(context) {
  console.log('🔧 After pack: إصلاح المكتبات Native...');

  const appOutDir = context.appOutDir;
  const platform = context.electronPlatformName;
  const arch = context.arch;

  console.log(`📋 المنصة: ${platform}, المعمارية: ${arch}`);

  try {
    // التأكد من وجود مجلد resources
    const resourcesPath = path.join(appOutDir, 'resources');
    if (!fs.existsSync(resourcesPath)) {
      fs.mkdirSync(resourcesPath, { recursive: true });
    }

    // التأكد من وجود مجلد app
    const appPath = path.join(resourcesPath, 'app');
    if (!fs.existsSync(appPath)) {
      fs.mkdirSync(appPath, { recursive: true });
    }

    // نسخ sql.js بطريقة محسنة
    await copySqlJsEnhanced(appOutDir, platform, arch);

    // إصلاح المكتبات Native الأخرى
    await fixNativeModulesEnhanced(appOutDir);

    // إنشاء ملف معلومات النظام
    await createSystemInfoEnhanced(appOutDir, platform, arch);

    // إضافة ملفات الدعم
    await addSupportFilesEnhanced(appOutDir);

    console.log('✅ تم إصلاح جميع المكتبات Native بنجاح');
  } catch (error) {
    console.error('❌ خطأ في إصلاح المكتبات Native:', error.message);
    console.log('⚠️ متابعة البناء مع تسجيل الخطأ...');

    // تسجيل الخطأ في ملف
    try {
      const errorLogPath = path.join(appOutDir, 'native-modules-error.log');
      fs.writeFileSync(errorLogPath, `خطأ في إصلاح المكتبات Native:\n${error.message}\n${error.stack}\nالوقت: ${new Date().toISOString()}`);
    } catch (logError) {
      console.error('فشل في تسجيل الخطأ:', logError.message);
    }

    // لا نرمي الخطأ لتجنب فشل البناء
  }
};

// نسخ sql.js المحسن
async function copySqlJsEnhanced(appOutDir, platform, arch) {
  console.log('🔧 نسخ sql.js المحسن...');

  try {
    // نسخ sql.js مع جميع الملفات المطلوبة
    console.log('📦 نسخ sql.js مع جميع التبعيات...');

    // نسخ sql.js إلى مجلد node_modules
    const sqlJsSource = path.join(__dirname, '..', 'node_modules', 'sql.js');
    const sqlJsTarget = path.join(appOutDir, 'resources', 'app', 'node_modules', 'sql.js');

    if (fs.existsSync(sqlJsSource)) {
      // التأكد من وجود المجلد الهدف
      const targetDir = path.dirname(sqlJsTarget);
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }

      // نسخ sql.js
      if (fs.existsSync(sqlJsTarget)) {
        fs.rmSync(sqlJsTarget, { recursive: true, force: true });
      }
      fs.cpSync(sqlJsSource, sqlJsTarget, { recursive: true });
      console.log('✅ تم نسخ sql.js إلى node_modules بنجاح');
    }

    // نسخ sql.js إلى مجلد resources كنسخة احتياطية
    const sqlJsBackupSource = path.join(__dirname, '..', 'resources', 'sql-backup');
    const sqlJsBackupTarget = path.join(appOutDir, 'resources', 'sql-backup');

    if (fs.existsSync(sqlJsBackupSource)) {
      if (fs.existsSync(sqlJsBackupTarget)) {
        fs.rmSync(sqlJsBackupTarget, { recursive: true, force: true });
      }
      fs.mkdirSync(sqlJsBackupTarget, { recursive: true });
      fs.cpSync(sqlJsBackupSource, sqlJsBackupTarget, { recursive: true });
      console.log('✅ تم نسخ النسخة الاحتياطية من sql.js بنجاح');
    }

    // نسخ sql.js مباشرة إلى resources
    const sqlJsDirectTarget = path.join(appOutDir, 'resources', 'sql.js');
    if (fs.existsSync(sqlJsSource)) {
      if (fs.existsSync(sqlJsDirectTarget)) {
        fs.rmSync(sqlJsDirectTarget, { recursive: true, force: true });
      }
      fs.cpSync(sqlJsSource, sqlJsDirectTarget, { recursive: true });
      console.log('✅ تم نسخ sql.js مباشرة إلى resources بنجاح');
    }

    // إنشاء ملف تكوين لقاعدة البيانات
    await createDatabaseConfig(appOutDir);

  } catch (error) {
    console.warn('⚠️ تحذير في نسخ sql.js:', error.message);
  }
}

// دالة محذوفة - لم تعد مطلوبة لأننا نستخدم sql.js فقط
// async function fixBetterSqlite3(appOutDir) { ... }

// فحص وجود ملفات .node
async function verifyNodeFiles(buildDir) {
  const nodeFiles = [];

  function findNodeFiles(dir) {
    try {
      const files = fs.readdirSync(dir, { withFileTypes: true });
      for (const file of files) {
        const fullPath = path.join(dir, file.name);
        if (file.isDirectory()) {
          findNodeFiles(fullPath);
        } else if (file.name.endsWith('.node')) {
          nodeFiles.push(fullPath);
        }
      }
    } catch (error) {
      // تجاهل أخطاء القراءة
    }
  }

  findNodeFiles(buildDir);

  if (nodeFiles.length > 0) {
    console.log(`✅ تم العثور على ${nodeFiles.length} ملف .node:`);
    nodeFiles.forEach(file => {
      const stats = fs.statSync(file);
      console.log(`   📄 ${path.basename(file)} (${(stats.size / 1024).toFixed(2)} KB)`);
    });
  } else {
    console.warn('⚠️ لم يتم العثور على أي ملفات .node');
  }
}

// نسخ جميع ملفات .node
async function copyAllNodeFiles(sourceDir, targetDir) {
  const nodeFiles = [];

  function findNodeFiles(dir) {
    try {
      const files = fs.readdirSync(dir, { withFileTypes: true });
      for (const file of files) {
        const fullPath = path.join(dir, file.name);
        if (file.isDirectory()) {
          findNodeFiles(fullPath);
        } else if (file.name.endsWith('.node')) {
          nodeFiles.push(fullPath);
        }
      }
    } catch (error) {
      // تجاهل أخطاء القراءة
    }
  }

  findNodeFiles(sourceDir);

  for (const nodeFile of nodeFiles) {
    const relativePath = path.relative(sourceDir, nodeFile);
    const targetFile = path.join(targetDir, relativePath);
    const targetFileDir = path.dirname(targetFile);

    if (!fs.existsSync(targetFileDir)) {
      fs.mkdirSync(targetFileDir, { recursive: true });
    }

    fs.copyFileSync(nodeFile, targetFile);
    console.log(`📄 نسخ ملف .node: ${relativePath}`);
  }
}

// إضافة Visual C++ Redistributables
async function addVCRedist(appOutDir) {
  console.log('🔧 إضافة Visual C++ Redistributables...');

  try {
    // إنشاء مجلد للمكتبات المطلوبة
    const vcRedistDir = path.join(appOutDir, 'resources', 'vcredist');
    if (!fs.existsSync(vcRedistDir)) {
      fs.mkdirSync(vcRedistDir, { recursive: true });
    }

    // إنشاء ملف معلومات المتطلبات
    const requirementsInfo = {
      vcRedist: {
        required: true,
        downloadUrl: 'https://aka.ms/vs/17/release/vc_redist.x64.exe',
        note: 'مطلوب لتشغيل better-sqlite3'
      },
      nodeVersion: process.version,
      electronVersion: getPackageVersion('electron'),
      buildDate: new Date().toISOString()
    };

    fs.writeFileSync(
      path.join(vcRedistDir, 'requirements.json'),
      JSON.stringify(requirementsInfo, null, 2)
    );

    console.log('✅ تم إضافة معلومات المتطلبات');
  } catch (error) {
    console.warn('⚠️ تحذير في إضافة Visual C++ Redistributables:', error.message);
  }
}

// إصلاح المكتبات Native الأخرى
async function fixNativeModules(appOutDir) {
  console.log('🔧 إصلاح المكتبات Native الأخرى...');

  const nativeModules = ['bcryptjs', 'node-cron'];
  const nodeModulesPath = path.join(appOutDir, 'resources', 'app', 'node_modules');

  for (const moduleName of nativeModules) {
    const modulePath = path.join(nodeModulesPath, moduleName);
    if (fs.existsSync(modulePath)) {
      console.log(`✅ ${moduleName} موجود`);
    }
  }
}

// إنشاء ملف معلومات النظام
async function createSystemInfo(appOutDir, platform, arch) {
  try {
    const systemInfo = {
      buildTime: new Date().toISOString(),
      platform: platform,
      arch: arch,
      electronVersion: getPackageVersion('electron'),
      nodeVersion: process.version,
      buildMachine: require('os').hostname(),
      nativeModules: {
        'better-sqlite3': getPackageVersion('better-sqlite3'),
        'bcryptjs': getPackageVersion('bcryptjs')
      }
    };

    const infoPath = path.join(appOutDir, 'resources', 'build-info.json');
    fs.writeFileSync(infoPath, JSON.stringify(systemInfo, null, 2));
    console.log('✅ تم إنشاء ملف معلومات النظام');
  } catch (error) {
    console.warn('⚠️ تحذير في إنشاء ملف معلومات النظام:', error.message);
  }
}

// إنشاء ملف تكوين قاعدة البيانات
async function createDatabaseConfig(appOutDir) {
  try {
    const configPath = path.join(appOutDir, 'resources', 'database-config.json');
    const config = {
      type: 'sql.js',
      wasmPath: './node_modules/sql.js/dist/sql-wasm.wasm',
      fallback: true,
      memory: true,
      note: 'نظام قاعدة البيانات الموحد باستخدام sql.js - محسن للأداء والاستقرار'
    };

    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    console.log('✅ تم إنشاء ملف تكوين قاعدة البيانات');
  } catch (error) {
    console.warn('⚠️ تحذير في إنشاء ملف تكوين قاعدة البيانات:', error.message);
  }
}

// إصلاح المكتبات Native المحسن
async function fixNativeModulesEnhanced(appOutDir) {
  console.log('🔧 إصلاح المكتبات Native المحسن...');

  const essentialModules = ['bcryptjs', 'node-cron', 'dayjs', 'antd'];
  const nodeModulesPath = path.join(appOutDir, 'resources', 'app', 'node_modules');

  for (const moduleName of essentialModules) {
    try {
      const sourceModule = path.join(__dirname, '..', 'node_modules', moduleName);
      const targetModule = path.join(nodeModulesPath, moduleName);

      if (fs.existsSync(sourceModule)) {
        if (!fs.existsSync(path.dirname(targetModule))) {
          fs.mkdirSync(path.dirname(targetModule), { recursive: true });
        }

        if (fs.existsSync(targetModule)) {
          fs.rmSync(targetModule, { recursive: true, force: true });
        }

        fs.cpSync(sourceModule, targetModule, { recursive: true });
        console.log(`✅ ${moduleName} تم نسخه بنجاح`);
      } else {
        console.warn(`⚠️ ${moduleName} غير موجود في المصدر`);
      }
    } catch (error) {
      console.warn(`⚠️ تحذير في نسخ ${moduleName}:`, error.message);
    }
  }
}

// إنشاء ملف معلومات النظام المحسن
async function createSystemInfoEnhanced(appOutDir, platform, arch) {
  try {
    const systemInfo = {
      buildTime: new Date().toISOString(),
      platform: platform,
      arch: arch,
      electronVersion: getPackageVersion('electron'),
      nodeVersion: process.version,
      buildMachine: require('os').hostname(),
      buildType: 'enhanced',
      databaseType: 'sql.js',
      nativeModules: {
        'sql.js': getPackageVersion('sql.js'),
        'bcryptjs': getPackageVersion('bcryptjs'),
        'node-cron': getPackageVersion('node-cron'),
        'antd': getPackageVersion('antd')
      },
      features: {
        nativeModulesFixed: true,
        sqlJsEnabled: true,
        unifiedDatabaseSystem: true
      }
    };

    const infoPath = path.join(appOutDir, 'resources', 'build-info.json');
    fs.writeFileSync(infoPath, JSON.stringify(systemInfo, null, 2));
    console.log('✅ تم إنشاء ملف معلومات النظام المحسن');
  } catch (error) {
    console.warn('⚠️ تحذير في إنشاء ملف معلومات النظام:', error.message);
  }
}

// إضافة ملفات الدعم المحسنة
async function addSupportFilesEnhanced(appOutDir) {
  try {
    console.log('📄 إضافة ملفات الدعم المحسنة...');

    const supportDir = path.join(appOutDir, 'resources', 'support');
    if (!fs.existsSync(supportDir)) {
      fs.mkdirSync(supportDir, { recursive: true });
    }

    // إنشاء ملف README
    const readmeContent = `# ZET.IA - نظام المحاسبة والإنتاج

## معلومات البناء
- تاريخ البناء: ${new Date().toISOString()}
- نوع قاعدة البيانات: sql.js (نظام موحد محسن)
- المكتبات Native: محسنة ومُصلحة

## الميزات
- ✅ نظام قاعدة بيانات محسن
- ✅ مكتبات Native مُصلحة
- ✅ أداء محسن
- ✅ استقرار عالي

## الدعم الفني
للدعم الفني، يرجى التواصل مع فريق التطوير.
`;

    fs.writeFileSync(path.join(supportDir, 'README.txt'), readmeContent);

    // إنشاء ملف معلومات التشغيل
    const runInfo = {
      requiredNodeVersion: '>=16.0.0',
      recommendedRAM: '4GB',
      supportedOS: ['Windows 10', 'Windows 11'],
      databaseEngine: 'sql.js',
      lastUpdated: new Date().toISOString()
    };

    fs.writeFileSync(path.join(supportDir, 'run-info.json'), JSON.stringify(runInfo, null, 2));

    console.log('✅ تم إضافة ملفات الدعم المحسنة');
  } catch (error) {
    console.warn('⚠️ تحذير في إضافة ملفات الدعم:', error.message);
  }
}

// دالة مساعدة للحصول على إصدار الحزمة
function getPackageVersion(packageName) {
  try {
    const packagePath = path.join(__dirname, '..', 'node_modules', packageName, 'package.json');
    if (fs.existsSync(packagePath)) {
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      return packageJson.version;
    }
    return 'unknown';
  } catch (error) {
    return 'unknown';
  }
}
