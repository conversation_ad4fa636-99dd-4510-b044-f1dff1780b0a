import React, { useState } from 'react'
import {
  Card, Steps, Timeline, Collapse, Row, Col, Typo<PERSON>, Space, Button,
  Tag, Alert, Divider, List, Avatar, Progress, Tooltip, Badge, Image
} from 'antd'
import {
  ToolOutlined, FileTextOutlined, InboxOutlined, SettingOutlined,
  CheckCircleOutlined, <PERSON><PERSON><PERSON>cleOutlined, <PERSON>Outlined, TeamOutlined,
  <PERSON><PERSON><PERSON>Outlined, SafetyOutlined, RocketOutlined, StarOutlined,
  PlayCircleOutlined, BookOutlined, BulbOutlined, WarningOutlined
} from '@ant-design/icons'
import styled from 'styled-components'

const { Title, Text, Paragraph } = Typography
const { Step } = Steps
const { Panel } = Collapse

const GuideContainer = styled.div`
  direction: rtl;
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
`

const StyledCard = styled(Card)`
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .ant-card-head {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px 12px 0 0;
    
    .ant-card-head-title {
      color: white;
      font-weight: bold;
    }
  }
`

const PhaseCard = styled(Card)`
  margin-bottom: 16px;
  border-left: 4px solid #1890ff;
  
  &.planning { border-left-color: #722ed1; }
  &.execution { border-left-color: #13c2c2; }
  &.control { border-left-color: #fa8c16; }
  &.completion { border-left-color: #52c41a; }
`

const ProcessFlow = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: white;
  border-radius: 8px;
  margin: 16px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .process-step {
    text-align: center;
    flex: 1;
    
    .step-icon {
      font-size: 32px;
      color: #1890ff;
      margin-bottom: 8px;
    }
    
    .step-title {
      font-weight: bold;
      margin-bottom: 4px;
    }
    
    .step-desc {
      color: #666;
      font-size: 12px;
    }
  }
  
  .arrow {
    font-size: 24px;
    color: #1890ff;
    margin: 0 16px;
  }
`

interface ProductionCycleGuideProps {
  onStartInteractiveTutorial?: () => void
}

const ProductionCycleGuide: React.FC<ProductionCycleGuideProps> = ({ 
  onStartInteractiveTutorial 
}) => {
  const [activePhase, setActivePhase] = useState<number>(0)

  const productionPhases = [
    {
      id: 'planning',
      title: 'مرحلة التخطيط',
      description: 'وضع خطة الإنتاج وتحديد الاحتياجات',
      icon: <SettingOutlined />,
      color: '#722ed1',
      steps: [
        'تحليل الطلب على المنتجات',
        'تحديد كميات الإنتاج المطلوبة',
        'حساب احتياجات المواد الخام',
        'جدولة عمليات الإنتاج',
        'تخصيص الموارد والعمالة'
      ]
    },
    {
      id: 'execution',
      title: 'مرحلة التنفيذ',
      description: 'تنفيذ عمليات الإنتاج الفعلية',
      icon: <ToolOutlined />,
      color: '#13c2c2',
      steps: [
        'إصدار أوامر الإنتاج',
        'سحب المواد الخام من المخزون',
        'تنفيذ عمليات التصنيع',
        'تسجيل تقدم الإنتاج',
        'مراقبة الجودة أثناء التصنيع'
      ]
    },
    {
      id: 'control',
      title: 'مرحلة المراقبة',
      description: 'مراقبة ومتابعة عمليات الإنتاج',
      icon: <BarChartOutlined />,
      color: '#fa8c16',
      steps: [
        'مراقبة معدلات الإنتاج',
        'تتبع استهلاك المواد',
        'مراجعة معايير الجودة',
        'حساب التكاليف الفعلية',
        'تحليل الانحرافات'
      ]
    },
    {
      id: 'completion',
      title: 'مرحلة الإنجاز',
      description: 'إنهاء الإنتاج وتسليم المنتجات',
      icon: <CheckCircleOutlined />,
      color: '#52c41a',
      steps: [
        'فحص المنتجات النهائية',
        'تسجيل البضائع التامة',
        'نقل المنتجات للمخزون',
        'إعداد تقارير الإنتاج',
        'تحليل الأداء والكفاءة'
      ]
    }
  ]

  const documentarySteps = [
    {
      title: 'طلب الإنتاج',
      description: 'إنشاء طلب إنتاج جديد بناءً على الطلب أو التوقعات',
      documents: ['نموذج طلب الإنتاج', 'تحليل الطلب', 'خطة الإنتاج'],
      icon: <FileTextOutlined />
    },
    {
      title: 'وصفة الإنتاج',
      description: 'تحديد المكونات والكميات المطلوبة لكل منتج',
      documents: ['وصفة الإنتاج', 'قائمة المواد', 'مواصفات المنتج'],
      icon: <BookOutlined />
    },
    {
      title: 'أمر الإنتاج',
      description: 'إصدار أمر رسمي لبدء عملية الإنتاج',
      documents: ['أمر الإنتاج', 'جدولة الإنتاج', 'تخصيص الموارد'],
      icon: <RocketOutlined />
    },
    {
      title: 'سحب المواد',
      description: 'سحب المواد الخام المطلوبة من المخزون',
      documents: ['إذن صرف مواد', 'كشف المواد المسحوبة', 'تحديث المخزون'],
      icon: <InboxOutlined />
    },
    {
      title: 'تتبع الإنتاج',
      description: 'متابعة تقدم الإنتاج في كل مرحلة',
      documents: ['تقرير تقدم الإنتاج', 'سجل العمليات', 'مراقبة الجودة'],
      icon: <ClockCircleOutlined />
    },
    {
      title: 'استلام المنتج',
      description: 'استلام المنتجات النهائية وفحصها',
      documents: ['إذن استلام منتجات', 'تقرير الجودة', 'شهادة المطابقة'],
      icon: <SafetyOutlined />
    },
    {
      title: 'حساب التكلفة',
      description: 'حساب التكلفة الفعلية للإنتاج',
      documents: ['تقرير التكلفة', 'تحليل الانحرافات', 'قائمة التكاليف'],
      icon: <DollarOutlined />
    }
  ]

  const renderProcessFlow = () => (
    <ProcessFlow>
      <div className="process-step">
        <div className="step-icon">
          <FileTextOutlined />
        </div>
        <div className="step-title">التخطيط</div>
        <div className="step-desc">وضع خطة الإنتاج</div>
      </div>
      
      <div className="arrow">→</div>
      
      <div className="process-step">
        <div className="step-icon">
          <ToolOutlined />
        </div>
        <div className="step-title">التنفيذ</div>
        <div className="step-desc">بدء الإنتاج</div>
      </div>
      
      <div className="arrow">→</div>
      
      <div className="process-step">
        <div className="step-icon">
          <BarChartOutlined />
        </div>
        <div className="step-title">المراقبة</div>
        <div className="step-desc">متابعة التقدم</div>
      </div>
      
      <div className="arrow">→</div>
      
      <div className="process-step">
        <div className="step-icon">
          <CheckCircleOutlined />
        </div>
        <div className="step-title">الإنجاز</div>
        <div className="step-desc">تسليم المنتج</div>
      </div>
    </ProcessFlow>
  )

  return (
    <GuideContainer>
      <StyledCard
        title={
          <Space>
            <ToolOutlined />
            دليل الدورة المستندية الإنتاجية الشامل
            <Badge count="تفصيلي" style={{ backgroundColor: '#52c41a' }} />
          </Space>
        }
        extra={
          <Button
            type="primary"
            size="large"
            icon={<PlayCircleOutlined />}
            onClick={onStartInteractiveTutorial}
          >
            بدء التعلم التفاعلي
          </Button>
        }
      >
        <Alert
          message="مرحباً بك في دليل الدورة المستندية الإنتاجية"
          description="هذا الدليل يوضح لك خطوة بخطوة كيفية إدارة العمليات الإنتاجية بطريقة احترافية ومنّمة"
          type="success"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Title level={3}>
          <BulbOutlined /> مفهوم الدورة المستندية الإنتاجية
        </Title>
        <Paragraph>
          الدورة المستندية الإنتاجية هي مجموعة من الخطوات والإجراءات المنّمة التي تحكم عملية الإنتاج
          من بدايتها حتى نهايتها، وتشمل جميع المستندات والتقارير المطلوبة لضمان سير العملية بكفاءة وشفافية.
        </Paragraph>

        <Title level={4}>تدفق العملية الإنتاجية</Title>
        {renderProcessFlow()}
      </StyledCard>

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card
            title={
              <Space>
                <StarOutlined />
                مراحل الإنتاج الأساسية
              </Space>
            }
          >
            <Steps
              direction="vertical"
              current={activePhase}
              onChange={setActivePhase}
            >
              {productionPhases.map((phase, index) => (
                <Step
                  key={phase.id}
                  title={phase.title}
                  description={phase.description}
                  icon={phase.icon}
                />
              ))}
            </Steps>
          </Card>
        </Col>

        <Col span={12}>
          <PhaseCard
            className={productionPhases[activePhase]?.id}
            title={
              <Space>
                {productionPhases[activePhase]?.icon}
                {productionPhases[activePhase]?.title}
              </Space>
            }
          >
            <List
              dataSource={productionPhases[activePhase]?.steps || []}
              renderItem={(step, index) => (
                <List.Item>
                  <Space>
                    <Avatar
                      size="small"
                      style={{
                        backgroundColor: productionPhases[activePhase]?.color
                      }}
                    >
                      {index + 1}
                    </Avatar>
                    <Text>{step}</Text>
                  </Space>
                </List.Item>
              )}
            />
          </PhaseCard>
        </Col>
      </Row>

      <StyledCard
        title={
          <Space>
            <FileTextOutlined />
            الدورة المستندية التفصيلية
          </Space>
        }
      >
        <Timeline mode="left">
          {documentarySteps.map((step, index) => (
            <Timeline.Item
              key={index}
              color={index % 2 === 0 ? 'blue' : 'green'}
              dot={step.icon}
            >
              <Card size="small" style={{ marginBottom: 16 }}>
                <Title level={5}>{step.title}</Title>
                <Paragraph>{step.description}</Paragraph>
                <div>
                  <Text strong>المستندات المطلوبة:</Text>
                  <div style={{ marginTop: 8 }}>
                    {step.documents.map((doc, docIndex) => (
                      <Tag key={docIndex} color="blue" style={{ marginBottom: 4 }}>
                        {doc}
                      </Tag>
                    ))}
                  </div>
                </div>
              </Card>
            </Timeline.Item>
          ))}
        </Timeline>
      </StyledCard>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Card
            title={
              <Space>
                <BulbOutlined />
                نصائح مهمة
              </Space>
            }
          >
            <List
              size="small"
              dataSource={[
                'احرص على دقة البيانات في جميع المراحل',
                'راجع الوصفات قبل بدء الإنتاج',
                'تابع التكاليف باستمرار',
                'طبق معايير الجودة بصرامة',
                'وثق جميع العمليات'
              ]}
              renderItem={(item, index) => (
                <List.Item>
                  <Space>
                    <Avatar size="small" style={{ backgroundColor: '#52c41a' }}>
                      {index + 1}
                    </Avatar>
                    <Text>{item}</Text>
                  </Space>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        <Col span={8}>
          <Card
            title={
              <Space>
                <WarningOutlined />
                تحديات شائعة
              </Space>
            }
          >
            <List
              size="small"
              dataSource={[
                'نقص في المواد الخام',
                'تأخير في الجدولة',
                'مشاكل في الجودة',
                'زيادة في التكاليف',
                'نقص في العمالة المدربة'
              ]}
              renderItem={(item, index) => (
                <List.Item>
                  <Space>
                    <Avatar size="small" style={{ backgroundColor: '#fa8c16' }}>
                      !
                    </Avatar>
                    <Text>{item}</Text>
                  </Space>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        <Col span={8}>
          <Card
            title={
              <Space>
                <CheckCircleOutlined />
                مؤشرات النجاح
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text>كفاءة الإنتاج</Text>
                <Progress percent={85} size="small" status="active" />
              </div>
              <div>
                <Text>جودة المنتجات</Text>
                <Progress percent={92} size="small" status="active" />
              </div>
              <div>
                <Text>الالتزام بالجدولة</Text>
                <Progress percent={78} size="small" status="active" />
              </div>
              <div>
                <Text>التحكم في التكاليف</Text>
                <Progress percent={88} size="small" status="active" />
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      <Alert
        message="هل تريد تطبيق ما تعلمته؟"
        description="ابدأ الآن بالتعلم التفاعلي لتطبيق الدورة المستندية الإنتاجية خطوة بخطوة في البرنامج"
        type="info"
        showIcon
        action={
          <Button
            type="primary"
            size="large"
            icon={<PlayCircleOutlined />}
            onClick={onStartInteractiveTutorial}
          >
            بدء التطبيق العملي
          </Button>
        }
        style={{ marginTop: 24 }}
      />
    </GuideContainer>
  )
}

export default ProductionCycleGuide
