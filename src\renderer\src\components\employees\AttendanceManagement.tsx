﻿import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  TimePicker,
  Row,
  Col,
  Typography,
  Statistic,
  Tag,
  Tooltip,
  Badge,
  Avatar,
  Divider,
  App} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import * as XLSX from 'xlsx'
import {
  ClockCircleOutlined,
  PlusOutlined,
  EditOutlined,
  UserOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  SafetyOutlined,
  SearchOutlined,
  FileExcelOutlined,
  PrinterOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input
const { RangePicker } = DatePicker

interface AttendanceRecord {
  id: number
  employee_id: number
  employee_code: string
  employee_name: string
  department_name: string
  date: string
  check_in_time?: string
  check_out_time?: string
  break_start_time?: string
  break_end_time?: string
  total_hours: number
  regular_hours: number
  overtime_hours: number
  break_hours: number
  late_minutes: number
  early_leave_minutes: number
  status: string
  attendance_source: string
  device_name?: string
  notes?: string
  approved_by_name?: string
  created_at: string
  updated_at: string
}

interface Employee {
  id: number
  employee_code: string
  full_name: string
  department_name: string
  status: string
}

interface Department {
  id: number
  name: string
}

interface FingerprintDevice {
  id: number
  device_name: string
  location: string
  status: string
}

const AttendanceManagement: React.FC = () => {
  // استخدام App context للرسائل
  const { message: messageApi } = App.useApp()
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([])
  const [employees, setEmployees] = useState<Employee[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [fingerprintDevices, setFingerprintDevices] = useState<FingerprintDevice[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingRecord, setEditingRecord] = useState<AttendanceRecord | null>(null)
  const [filters, setFilters] = useState<any>({})
  const [form] = Form.useForm()

  useEffect(() => {
    fetchAttendanceRecords()
    fetchEmployees()
    fetchDepartments()
    fetchFingerprintDevices()
  }, [])

  const fetchAttendanceRecords = async (filterParams?: any) => {
    setLoading(true)
    try {
      if (!window.electronAPI) {
        Logger.error('AttendanceManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('AttendanceManagement', 'âڑ ï¸ڈ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للحضور
        const mockAttendanceRecords = [
          {
            id: 1,
            employee_id: 1,
            employee_name: 'أحمد محمد علٍ',
            employee_code: 'EMP001',
            department_name: 'قسم الموارد البشرية',
            date: new Date().toISOString().split('T')[0],
            check_in: '08:00',
            check_out: '17:00',
            status: 'present'
          }
        ]

        setAttendanceRecords(mockAttendanceRecords as any[])
        Logger.info('AttendanceManagement', '✅ تم تحميل ' + mockAttendanceRecords.length + ' سجل حضور وهمي للمتصفح')
        return
      }

      const result = await window.electronAPI.getEmployeeAttendance(filterParams || filters)
      if (result.success) {
        setAttendanceRecords(result.data)
      } else {
        messageApi.error(result.message)
      }
    } catch (_error) {
      messageApi.error('فشل في جلب بيانات الحضور')
    } finally {
      setLoading(false)
    }
  }

  const fetchEmployees = async () => {
    try {
      Logger.info('AttendanceManagement', 'ًں”„ جارٍ جلب بيانات الموظفيْ...')

      if (!window.electronAPI) {
        Logger.error('AttendanceManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('AttendanceManagement', 'âڑ ï¸ڈ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للموظفيْ
        const mockEmployees = [
          {
            id: 1,
            employee_code: 'EMP001',
            full_name: 'أحمد محمد علٍ',
            department_name: 'قسم الموارد البشرية',
            status: 'active'
          },
          {
            id: 2,
            employee_code: 'EMP002',
            full_name: 'فاطمة عبدالله الزهراٍْ',
            department_name: 'قسم المالية والمحاسبة',
            status: 'active'
          }
        ]

        setEmployees(mockEmployees as any[])
        Logger.info('AttendanceManagement', '✅ تم تحميل ' + mockEmployees.length + ' موظف وهمي للمتصفح')
        return
      }

      const result = await window.electronAPI.getEmployees()
      Logger.info('AttendanceManagement', 'ًں“ٹ ْتٍجة جلب الموظفيْ:', result)

      if (result && result.success) {
        // تصحٍح اسم الخاصٍة من status إلى is_active
        const activeEmployees = result.data.filter((emp: any) => emp.is_active === 1)
        setEmployees(activeEmployees)
        Logger.info('AttendanceManagement', '✅ تم جلب ' + activeEmployees.length + ' موظف ْشط بنجاح')
      } else {
        Logger.error('AttendanceManagement', '❌ فشل في جلب الموظفيْ:', result?.message)
        setEmployees([])
      }
    } catch (_error) {
      Logger.error('AttendanceManagement', 'فشل في جلب الموظفين:', _error)
      setEmployees([])
    }
  }

  const fetchDepartments = async () => {
    try {
      if (!window.electronAPI) {
        Logger.error('AttendanceManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('AttendanceManagement', 'âڑ ï¸ڈ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للأقسام
        const mockDepartments = [
          { id: 1, name: 'قسم الموارد البشرية', code: 'HR001' },
          { id: 2, name: 'قسم المالية والمحاسبة', code: 'FIN001' },
          { id: 3, name: 'قسم المبيعات', code: 'SALES001' }
        ]

        setDepartments(mockDepartments as any[])
        Logger.info('AttendanceManagement', '✅ تم تحميل ' + mockDepartments.length + ' قسم وهمي للمتصفح')
        return
      }

      const result = await window.electronAPI.getEmployeeDepartments()
      if (result.success) {
        setDepartments(result.data)
      }
    } catch (_error) {
      Logger.error('AttendanceManagement', 'فشل في جلب الأقسام:', _error)
    }
  }

  const fetchFingerprintDevices = async () => {
    try {
      const result = await window.electronAPI.getFingerprintDevices()
      if (result.success) {
        setFingerprintDevices(result.data)
      }
    } catch (_error) {
      Logger.error('AttendanceManagement', 'فشل في جلب أجهزة البصمة:', _error)
    }
  }

  const handleCreateRecord = () => {
    setEditingRecord(null)
    form.resetFields()
    form.setFieldsValue({
      date: dayjs(),
      status: 'present',
      attendance_source: 'manual'
    })
    setModalVisible(true)
  }

  const handleEditRecord = (record: AttendanceRecord) => {
    setEditingRecord(record)
    form.setFieldsValue({
      ...record,
      date: dayjs(record.date),
      check_in_time: record.check_in_time ? dayjs(record.check_in_time, 'HH:mm') : null,
      check_out_time: record.check_out_time ? dayjs(record.check_out_time, 'HH:mm') : null,
      break_start_time: record.break_start_time ? dayjs(record.break_start_time, 'HH:mm') : null,
      break_end_time: record.break_end_time ? dayjs(record.break_end_time, 'HH:mm') : null
    })
    setModalVisible(true)
  }

  const handleSubmit = async (values: any) => {
    try {
      // التحقق من صحة البيانات الأساسية
      if (!values.employee_id) {
        messageApi.error('ٍجب اختٍار الموظف')
        return
      }

      if (!values.date) {
        messageApi.error('ٍجب اختٍار التارٍخ')
        return
      }

      // التحقق من صحة الأوقات
      if (values.check_in_time && values.check_out_time) {
        const checkInTime = values.check_in_time.format('HH:mm')
        const checkOutTime = values.check_out_time.format('HH:mm')

        if (checkInTime >= checkOutTime) {
          messageApi.error('وقت الحضور ٍجب أْ ٍكوْ قبل وقت الاْصراف')
          return
        }
      }

      // التحقق من صحة أوقات الاستراحة
      if (values.break_start_time && values.break_end_time) {
        const breakStartTime = values.break_start_time.format('HH:mm')
        const breakEndTime = values.break_end_time.format('HH:mm')

        if (breakStartTime >= breakEndTime) {
          messageApi.error('وقت بداٍة الاستراحة ٍجب أْ ٍكوْ قبل وقت ْهاٍة الاستراحة')
          return
        }

        // التحقق من أْ الاستراحة ضمن ساعات العمل
        if (values.check_in_time && values.check_out_time) {
          const checkInTime = values.check_in_time.format('HH:mm')
          const checkOutTime = values.check_out_time.format('HH:mm')

          if (breakStartTime < checkInTime || breakEndTime > checkOutTime) {
            messageApi.error('أوقات الاستراحة ٍجب أْ تكوْ ضمن ساعات العمل')
            return
          }
        }
      }

      // التحقق من صحة التارٍخ (لا يمكن أْ ٍكوْ في المستقبل)
      const selectedDate = values.date.format('YYYY-MM-DD')
      const today = new Date().toISOString().split('T')[0]

      if (selectedDate > today) {
        messageApi.error('لا يمكن تسجٍل الحضور لتارٍخ في المستقبل')
        return
      }

      const attendanceData = {
        ...values,
        date: values.date.format('YYYY-MM-DD'),
        check_in_time: values.check_in_time ? values.check_in_time.format('HH:mm') : null,
        check_out_time: values.check_out_time ? values.check_out_time.format('HH:mm') : null,
        break_start_time: values.break_start_time ? values.break_start_time.format('HH:mm') : null,
        break_end_time: values.break_end_time ? values.break_end_time.format('HH:mm') : null,
        approved_by: 1 // ٍجب الحصول على معرف المستخدم الحالٍ
      }

      const result = await window.electronAPI.recordAttendance(attendanceData)
      if (result.success) {
        messageApi.success('تم حفظ سجل الحضور بنجاح')
        setModalVisible(false)
        form.resetFields()
        fetchAttendanceRecords()
      } else {
        messageApi.error(result.message)
      }
    } catch (_error) {
      messageApi.error('فشل في حفظ سجل الحضور')
    }
  }

  const handleFilter = (values: any) => {
    const filterParams = {
      ...values,
      date_from: values.date_range ? values.date_range[0].format('YYYY-MM-DD') : null,
      date_to: values.date_range ? values.date_range[1].format('YYYY-MM-DD') : null
    }
    delete filterParams.date_range
    setFilters(filterParams)
    fetchAttendanceRecords(filterParams)
  }

  // دالة تصدٍر Excel للحضور والاْصراف
  const handleExportExcel = () => {
    try {
      // فحص البيانات قبل التصدٍر
      if (!attendanceRecords || attendanceRecords.length === 0) {
        messageApi.warning('لا توجد سجلات حضور للتصدٍر')
        return
      }

      // تحضٍر البيانات للتصدٍر
      const exportData = attendanceRecords.map(record => ({
        'كود الموظف': record.employee_code,
        'اسم الموظف': record.employee_name,
        'القسم': record.department_name,
        'التارٍخ': record.date,
        'وقت الحضور': record.check_in_time || '',
        'وقت الاْصراف': record.check_out_time || '',
        'بداٍة الاستراحة': record.break_start_time || '',
        'ْهاٍة الاستراحة': record.break_end_time || '',
        'إجمالي الساعات': record.total_hours || 0,
        'الساعات العادٍة': record.regular_hours || 0,
        'الساعات الإضافية': record.overtime_hours || 0,
        'ساعات الاستراحة': record.break_hours || 0,
        'دقائق التأخٍر': record.late_minutes || 0,
        'دقائق الخروج المبكر': record.early_leave_minutes || 0,
        'الحالة': getStatusText(record.status),
        'مصدر التسجٍل': record.attendance_source === 'fingerprint' ? 'بصمة' :
                        record.attendance_source === 'card' ? 'كارت' :
                        record.attendance_source === 'mobile' ? 'موباٍل' : 'ٍدوٍ',
        'اسم الجهاز': record.device_name || '',
        'ملاحّات': record.notes || '',
        'معتمد من': record.approved_by_name || '',
        'تارٍخ الإْشاط،': record.created_at,
        'تارٍخ التحديث': record.updated_at
      }))

      // إْشاط، ورقة العمل
      const worksheet = XLSX.utils.json_to_sheet(exportData)

      // تحسٍْ عرض الأعمدة
      const columnWidths = [
        { wch: 12 }, { wch: 20 }, { wch: 15 }, { wch: 12 }, { wch: 12 },
        { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 12 }, { wch: 12 },
        { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 15 }, { wch: 10 },
        { wch: 12 }, { wch: 15 }, { wch: 30 }, { wch: 15 }, { wch: 20 }, { wch: 20 }
      ]
      worksheet['!cols'] = columnWidths

      // إْشاط، المصْف
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'سجلات الحضور')

      // إضافة ورقة إحصائيات
      const stats = [
        { 'الإحصائية': 'إجمالي السجلات', 'القيمة': attendanceRecords.length },
        { 'الإحصائية': 'الحاضرٍْ الٍوم', 'القيمة': attendanceRecords.filter(r => r.date === dayjs().format('YYYY-MM-DD') && r.status === 'present').length },
        { 'الإحصائية': 'الغائبٍْ الٍوم', 'القيمة': attendanceRecords.filter(r => r.date === dayjs().format('YYYY-MM-DD') && r.status === 'absent').length },
        { 'الإحصائية': 'المتأخرٍْ الٍوم', 'القيمة': attendanceRecords.filter(r => r.date === dayjs().format('YYYY-MM-DD') && r.late_minutes > 0).length },
        { 'الإحصائية': 'إجمالي الساعات المسجلة', 'القيمة': attendanceRecords.reduce((sum, r) => sum + (r.total_hours || 0), 0).toFixed(2) + ' ساعة' },
        { 'الإحصائية': 'إجمالي الساعات الإضافية', 'القيمة': attendanceRecords.reduce((sum, r) => sum + (r.overtime_hours || 0), 0).toFixed(2) + ' ساعة' }
      ]

      const statsWorksheet = XLSX.utils.json_to_sheet(stats)
      statsWorksheet['!cols'] = [{ wch: 25 }, { wch: 20 }]
      XLSX.utils.book_append_sheet(workbook, statsWorksheet, 'الإحصائيات')

      // تصدٍر الملف
      const fileName = 'تقرٍر_الحضور_والاْصراف_' + new Date().toISOString().split('T')[0] + '.xlsx'
      XLSX.writeFile(workbook, fileName)

      messageApi.success(`تم تصدٍر ${exportData.length} سجل حضور بنجاح`)
    } catch (_error) {
      Logger.error('AttendanceManagement', 'خطأ في تصدير Excel:', _error)
      messageApi.error('فشل في تصدير التقرير')
    }
  }

  // دالة الطباعة المحسنة باستخدام MasterPrintService
  const handlePrint = async () => {
    try {
      if (!attendanceRecords || attendanceRecords.length === 0) {
        messageApi.warning('لا توجد سجلات حضور للطباعة')
        return
      }

      // استخدام طباعة مبسطة مع تصميم احترافي
      const printContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <title>تقرير الحضور والانصراف</title>
          <style>
            @page { size: A4; margin: 1cm; }
            body {
              font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
              direction: rtl;
              margin: 0;
              padding: 20px;
              color: #333;
              line-height: 1.6;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 3px solid #1890ff;
              padding-bottom: 20px;
            }
            .header h1 {
              color: #1890ff;
              margin: 0;
              font-size: 28px;
              font-weight: bold;
            }
            .header .subtitle {
              color: #666;
              margin-top: 10px;
              font-size: 16px;
            }
            .statistics {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
              margin-bottom: 30px;
              padding: 20px;
              background: #f8f9fa;
              border-radius: 8px;
            }
            .stat-item {
              text-align: center;
              padding: 10px;
              background: white;
              border-radius: 6px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .stat-value {
              font-size: 24px;
              font-weight: bold;
              color: #1890ff;
            }
            .stat-label {
              font-size: 12px;
              color: #666;
              margin-top: 5px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 20px;
              font-size: 12px;
              background: white;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: center;
            }
            th {
              background: #1890ff;
              color: white;
              font-weight: bold;
            }
            tr:nth-child(even) {
              background: #f9f9f9;
            }
            .status-present { color: #52c41a; font-weight: bold; }
            .status-late { color: #faad14; font-weight: bold; }
            .status-absent { color: #ff4d4f; font-weight: bold; }
            .status-early_leave { color: #fa8c16; font-weight: bold; }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 10px;
              color: #666;
              border-top: 1px solid #ddd;
              padding-top: 10px;
            }
            @media print {
              body { -webkit-print-color-adjust: exact; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>تقرير الحضور والانصراف</h1>
            <div class="subtitle">
              تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')} |
              إجمالي السجلات: ${attendanceRecords.length}
            </div>
          </div>

          <div class="statistics">
            <div class="stat-item">
              <div class="stat-value">${attendanceRecords.filter(r => r.status === 'present').length}</div>
              <div class="stat-label">الحضور في الوقت</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">${attendanceRecords.filter(r => r.status === 'late').length}</div>
              <div class="stat-label">التأخير</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">${attendanceRecords.filter(r => r.status === 'absent').length}</div>
              <div class="stat-label">الغياب</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">${attendanceRecords.reduce((sum, r) => sum + (r.total_hours || 0), 0).toFixed(1)}</div>
              <div class="stat-label">إجمالي الساعات</div>
            </div>
          </div>

          <table>
            <thead>
              <tr>
                <th>كود الموظف</th>
                <th>اسم الموظف</th>
                <th>القسم</th>
                <th>التاريخ</th>
                <th>وقت الحضور</th>
                <th>وقت الانصراف</th>
                <th>ساعات العمل</th>
                <th>الحالة</th>
                <th>مصدر التسجيل</th>
              </tr>
            </thead>
            <tbody>
              ${attendanceRecords.map(record => `
                <tr>
                  <td>${record.employee_code}</td>
                  <td>${record.employee_name}</td>
                  <td>${record.department_name}</td>
                  <td>${record.date}</td>
                  <td>${record.check_in_time || '-'}</td>
                  <td>${record.check_out_time || '-'}</td>
                  <td>${(record.total_hours || 0).toFixed(2)}</td>
                  <td class="status-${record.status}">${getStatusText(record.status)}</td>
                  <td>${record.attendance_source === 'fingerprint' ? 'بصمة' :
                        record.attendance_source === 'card' ? 'كارت' :
                        record.attendance_source === 'mobile' ? 'موبايل' : 'يدوي'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="footer">
            تم إنشاء هذا التقرير بواسطة نظام ZET.IA للمحاسبة والإنتاج
          </div>
        </body>
        </html>
      `

      const printWindow = window.open('', '_blank')
      if (printWindow) {
        const printDoc = (printWindow as any).document
        if (printDoc) {
          printDoc.write(printContent)
          printDoc.close()
        }
        printWindow.print()
      }

      messageApi.success('تم إرسال التقرير للطباعة')
    } catch (_error) {
      Logger.error('AttendanceManagement', 'خطأ في الطباعة:', _error)
      messageApi.error('فشل في طباعة التقرير')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present': return 'green'
      case 'absent': return 'red'
      case 'late': return 'orange'
      case 'half_day': return 'blue'
      case 'sick_leave': return 'purple'
      case 'vacation': return 'cyan'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'present': return 'حاضر'
      case 'absent': return 'غائب'
      case 'late': return 'متأخر'
      case 'half_day': return 'ْصف ٍوم'
      case 'sick_leave': return 'إجازة مرضٍة'
      case 'vacation': return 'إجازة'
      default: return status
    }
  }

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'fingerprint': return <SafetyOutlined />
      case 'card': return <Badge />
      case 'mobile': return <Badge />
      default: return <EditOutlined />
    }
  }

  const columns: ColumnsType<AttendanceRecord> = [
    {
      title: 'الموظف',
      key: 'employee',
      width: 200,
      fixed: 'left',
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.employee_name}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.employee_code} - {record.department_name}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: 'التارٍخ',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date) => (
        <Space>
          <CalendarOutlined />
          {dayjs(date).format('YYYY-MM-DD')}
        </Space>
      )
    },
    {
      title: 'الحضور',
      dataIndex: 'check_in_time',
      key: 'check_in_time',
      width: 100,
      render: (time) => time ? (
        <Tag color="green" icon={<CheckCircleOutlined />}>
          {time}
        </Tag>
      ) : '-'
    },
    {
      title: 'الاْصراف',
      dataIndex: 'check_out_time',
      key: 'check_out_time',
      width: 100,
      render: (time) => time ? (
        <Tag color="red" icon={<CloseCircleOutlined />}>
          {time}
        </Tag>
      ) : '-'
    },
    {
      title: 'إجمالي الساعات',
      dataIndex: 'total_hours',
      key: 'total_hours',
      width: 120,
      render: (hours) => (
        <Space>
          <ClockCircleOutlined />
          {hours?.toFixed(2) || '0'} ساعة
        </Space>
      )
    },
    {
      title: 'ساعات إضافية',
      dataIndex: 'overtime_hours',
      key: 'overtime_hours',
      width: 120,
      render: (hours) => hours > 0 ? (
        <Tag color="orange">
          {hours.toFixed(2)} ساعة
        </Tag>
      ) : '-'
    },
    {
      title: 'التأخٍر',
      dataIndex: 'late_minutes',
      key: 'late_minutes',
      width: 100,
      render: (minutes) => minutes > 0 ? (
        <Tag color="red" icon={<ExclamationCircleOutlined />}>
          {minutes} دقٍقة
        </Tag>
      ) : '-'
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'المصدر',
      dataIndex: 'attendance_source',
      key: 'attendance_source',
      width: 100,
      render: (source, record) => (
        <Tooltip title={record.device_name || 'تسجٍل ٍدوٍ'}>
          <Tag icon={getSourceIcon(source)}>
            {source === 'fingerprint' ? 'بصمة' : 
             source === 'card' ? 'كارت' : 
             source === 'mobile' ? 'موباٍل' : 'ٍدوٍ'}
          </Tag>
        </Tooltip>
      )
    },
    {
      title: 'الإجراط،ات',
      key: 'actions',
      width: 100,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Tooltip title="تعديل">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditRecord(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <ClockCircleOutlined /> إدارة الحضور والاْصراف
      </Title>
      
      {/* إحصائيات سرٍعة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="إجمالي السجلات"
              value={attendanceRecords.length}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="الحاضرٍْ الٍوم"
              value={attendanceRecords.filter(r => 
                r.date === dayjs().format('YYYY-MM-DD') && r.status === 'present'
              ).length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="الغائبٍْ الٍوم"
              value={attendanceRecords.filter(r => 
                r.date === dayjs().format('YYYY-MM-DD') && r.status === 'absent'
              ).length}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="المتأخرٍْ الٍوم"
              value={attendanceRecords.filter(r => 
                r.date === dayjs().format('YYYY-MM-DD') && r.late_minutes > 0
              ).length}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* فلاتر البحث */}
      <Card style={{ marginBottom: '16px' }}>
        <Form layout="inline" onFinish={handleFilter}>
          <Form.Item name="employee_id" label="الموظف">
            <Select placeholder="اختر الموظف" style={{ width: 200 }} allowClear>
              {employees.map(emp => (
                <Option key={emp.id} value={emp.id}>
                  {emp.full_name} - {emp.employee_code}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="department_id" label="القسم">
            <Select placeholder="اختر القسم" style={{ width: 150 }} allowClear>
              {departments.map(dept => (
                <Option key={dept.id} value={dept.id}>
                  {dept.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="status" label="الحالة">
            <Select placeholder="اختر الحالة" style={{ width: 120 }} allowClear>
              <Option value="present">حاضر</Option>
              <Option value="absent">غائب</Option>
              <Option value="late">متأخر</Option>
              <Option value="half_day">ْصف ٍوم</Option>
              <Option value="sick_leave">إجازة مرضٍة</Option>
              <Option value="vacation">إجازة</Option>
            </Select>
          </Form.Item>

          <Form.Item name="date_range" label="الفترة">
            <RangePicker />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              بحث
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* جدول الحضور */}
      <Card
        title={
          <Space>
            <ClockCircleOutlined />
            <span>سجلات الحضور والاْصراف</span>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<FileExcelOutlined />}
              onClick={handleExportExcel}
            >
              تصدٍر Excel
            </Button>
            <Button
              icon={<PrinterOutlined />}
              onClick={handlePrint}
            >
              طباعة
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateRecord}
            >
              تسجٍل حضور
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={attendanceRecords}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} سجل`
          }}
        />
      </Card>

      {/* نافذة تسجٍل/تعديل الحضور */}
      <Modal
        title={editingRecord ? 'تعديل سجل الحضور' : 'تسجٍل حضور جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={800}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="employee_id"
                label="الموظف"
                rules={[{ required: true, message: 'ٍرجى اختٍار الموظف' }]}
              >
                <Select placeholder="اختر الموظف" showSearch>
                  {employees.map(emp => (
                    <Option key={emp.id} value={emp.id}>
                      {emp.full_name} - {emp.employee_code}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="date"
                label="التارٍخ"
                rules={[{ required: true, message: 'ٍرجى اختٍار التارٍخ' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name="check_in_time"
                label="وقت الحضور"
              >
                <TimePicker style={{ width: '100%' }} format="HH:mm" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="check_out_time"
                label="وقت الاْصراف"
              >
                <TimePicker style={{ width: '100%' }} format="HH:mm" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="break_start_time"
                label="بداٍة الاستراحة"
              >
                <TimePicker style={{ width: '100%' }} format="HH:mm" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="break_end_time"
                label="ْهاٍة الاستراحة"
              >
                <TimePicker style={{ width: '100%' }} format="HH:mm" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="status"
                label="الحالة"
                rules={[{ required: true, message: 'ٍرجى اختٍار الحالة' }]}
              >
                <Select>
                  <Option value="present">حاضر</Option>
                  <Option value="absent">غائب</Option>
                  <Option value="late">متأخر</Option>
                  <Option value="half_day">ْصف ٍوم</Option>
                  <Option value="sick_leave">إجازة مرضٍة</Option>
                  <Option value="vacation">إجازة</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="attendance_source"
                label="مصدر التسجٍل"
                rules={[{ required: true, message: 'ٍرجى اختٍار مصدر التسجٍل' }]}
              >
                <Select>
                  <Option value="manual">ٍدوٍ</Option>
                  <Option value="fingerprint">بصمة</Option>
                  <Option value="card">كارت</Option>
                  <Option value="mobile">موباٍل</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="device_id"
                label="جهاز البصمة"
              >
                <Select placeholder="اختر الجهاز" allowClear>
                  {fingerprintDevices.map(device => (
                    <Option key={device.id} value={device.id}>
                      {device.device_name} - {device.location}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="notes"
                label="ملاحّات"
              >
                <TextArea rows={3} />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <Row justify="end">
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {editingRecord ? 'تحديث' : 'حفظ'}
              </Button>
            </Space>
          </Row>
        </Form>
      </Modal>
    </div>
  )
}

export default AttendanceManagement

