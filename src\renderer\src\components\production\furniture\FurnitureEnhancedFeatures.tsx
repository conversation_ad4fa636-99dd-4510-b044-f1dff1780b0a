import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  Typography,
  Calendar,
  Badge,
  List,
  Avatar,
  Tag,
  Progress,
  Divider,
  Image,
  Empty,
  Tooltip
} from 'antd'
import {
  BellOutlined,
  CalendarOutlined,
  PictureOutlined,
  NotificationOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined as _ExclamationCircleOutlined,
  CheckCircleOutlined,
  CameraOutlined as _CameraOutlined,
  FileImageOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons'
import { furnitureNotificationService } from './FurnitureNotificationService'
import { furnitureCalendarService } from './FurnitureCalendarService'
import dayjs from 'dayjs'

const { Title, Text } = Typography

interface FurnitureEnhancedFeaturesProps {
  onBack?: () => void
}

const FurnitureEnhancedFeatures: React.FC<FurnitureEnhancedFeaturesProps> = ({ onBack }) => {
  const [notificationStats, setNotificationStats] = useState({
    total: 0,
    unread: 0,
    pending: 0
  })
  
  const [calendarStats, setCalendarStats] = useState({
    todayEvents: 0,
    weekEvents: 0,
    monthEvents: 0,
    overdueEvents: 0
  })
  
  const [imageStats, setImageStats] = useState({
    totalImages: 0,
    totalSize: 0,
    recentImages: [] as any[]
  })

  const [selectedDate, setSelectedDate] = useState(dayjs())

  useEffect(() => {
    loadStats()
  }, [])

  const loadStats = () => {
    // إحصائيات التنبيهات
    const allNotifications = furnitureNotificationService.getAllNotifications()
    const unreadNotifications = furnitureNotificationService.getUnreadNotifications()
    const pendingNotifications = furnitureNotificationService.getPendingNotifications()

    setNotificationStats({
      total: allNotifications.length,
      unread: unreadNotifications.length,
      pending: pendingNotifications.length
    })

    // إحصائيات التقويم
    const calendarStatsData = furnitureCalendarService.getCalendarStats()
    setCalendarStats({
      todayEvents: calendarStatsData.todayEvents,
      weekEvents: calendarStatsData.thisWeekEvents,
      monthEvents: calendarStatsData.thisMonthEvents,
      overdueEvents: calendarStatsData.overdueEvents
    })

    // إحصائيات الصور - تم تعطيلها مؤقتاً (تم حذف FurnitureImageService)
    // TODO: استخدام SimpleImageService للحصول على الإحصائيات
    setImageStats({
      totalImages: 0,
      totalSize: 0,
      recentImages: 0
    })
  }

  const getDateCellRender = (value: dayjs.Dayjs) => {
    const events = furnitureCalendarService.getEventsByDate(value.toDate())
    if (events.length === 0) return null

    return (
      <div>
        {events.slice(0, 2).map(event => (
          <Badge
            key={event.id}
            status={
              event.type === 'deadline' ? 'error' :
              event.type === 'production_order' ? 'processing' : 'success'
            }
            text={event.title.substring(0, 10) + '...'}
            style={{ fontSize: '10px', display: 'block' }}
          />
        ))}
        {events.length > 2 && (
          <Badge
            count={`+${events.length - 2}`}
            style={{ fontSize: '10px' }}
          />
        )}
      </div>
    )
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* شريط العنوان والعودة */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {onBack && (
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={onBack}
            >
              العودة
            </Button>
          )}
          <Title level={2} style={{ margin: 0 }}>
            🏠 المميزات المحسنة - قسم الأثاث
          </Title>
        </div>
        <Button type="primary" onClick={loadStats}>
          تحديث الإحصائيات
        </Button>
      </div>

      {/* الإحصائيات العامة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="إجمالي التنبيهات"
              value={notificationStats.total}
              prefix={<BellOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="danger">غير مقروءة: {notificationStats.unread}</Text>
              <br />
              <Text type="warning">معلقة: {notificationStats.pending}</Text>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="أحداث التقويم"
              value={calendarStats.todayEvents}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#52c41a' }}
              suffix="اليوم"
            />
            <div style={{ marginTop: 8 }}>
              <Text>هذا الأسبوع: {calendarStats.weekEvents}</Text>
              <br />
              <Text type="danger">متأخرة: {calendarStats.overdueEvents}</Text>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="الصور المرفوعة"
              value={imageStats.totalImages}
              prefix={<PictureOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text>الحجم: {formatFileSize(imageStats.totalSize)}</Text>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="معدل الكفاءة"
              value={85}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
              suffix="%"
            />
            <Progress percent={85} size="small" style={{ marginTop: 8 }} />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* التقويم التفاعلي */}
        <Col xs={24} lg={14}>
          <Card title={
            <Space>
              <CalendarOutlined />
              التقويم التفاعلي
            </Space>
          }>
            <Calendar
              fullscreen={false}
              value={selectedDate}
              onSelect={setSelectedDate}
              dateCellRender={getDateCellRender}
            />
          </Card>
        </Col>

        {/* التنبيهات الأخيرة */}
        <Col xs={24} lg={10}>
          <Card 
            title={
              <Space>
                <NotificationOutlined />
                التنبيهات الأخيرة
              </Space>
            }
            extra={
              <Badge count={notificationStats.unread} size="small">
                <BellOutlined style={{ fontSize: '16px' }} />
              </Badge>
            }
          >
            <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
              {furnitureNotificationService.getAllNotifications().slice(0, 5).map(notification => (
                <div key={notification.id} style={{ 
                  padding: '8px', 
                  marginBottom: '8px',
                  backgroundColor: notification.isRead ? '#f5f5f5' : '#fff2e8',
                  borderRadius: '6px',
                  border: '1px solid #d9d9d9'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <div style={{ flex: 1 }}>
                      <Text strong>{notification.title}</Text>
                      <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                        {notification.message}
                      </div>
                      <div style={{ fontSize: '11px', color: '#999', marginTop: '4px' }}>
                        {notification.scheduledTime.toLocaleString('ar-SA')}
                      </div>
                    </div>
                    <Tag color={
                      notification.priority === 'urgent' ? 'red' :
                      notification.priority === 'high' ? 'orange' :
                      notification.priority === 'medium' ? 'blue' : 'green'
                    }>
                      {notification.priority}
                    </Tag>
                  </div>
                </div>
              ))}
              {notificationStats.total === 0 && (
                <Empty description="لا توجد تنبيهات" image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </div>
          </Card>
        </Col>
      </Row>

      <Divider />

      <Row gutter={[16, 16]}>
        {/* أحداث اليوم المحدد */}
        <Col xs={24} lg={12}>
          <Card title={
            <Space>
              <ClockCircleOutlined />
              أحداث {selectedDate.format('YYYY-MM-DD')}
            </Space>
          }>
            <div style={{ maxHeight: '250px', overflowY: 'auto' }}>
              {furnitureCalendarService.getEventsByDate(selectedDate.toDate()).map(event => (
                <div key={event.id} style={{ 
                  padding: '8px', 
                  marginBottom: '8px',
                  backgroundColor: '#f0f8ff',
                  borderRadius: '6px',
                  border: '1px solid #91d5ff'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <div style={{ flex: 1 }}>
                      <Text strong>{event.title}</Text>
                      <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                        {event.description}
                      </div>
                      <div style={{ fontSize: '11px', color: '#999', marginTop: '4px' }}>
                        {event.start.toLocaleTimeString('ar-SA')} - {event.end.toLocaleTimeString('ar-SA')}
                      </div>
                    </div>
                    <Space>
                      <Tag color={
                        event.type === 'deadline' ? 'red' :
                        event.type === 'production_order' ? 'blue' : 'green'
                      }>
                        {event.type}
                      </Tag>
                      <Tag color={
                        event.status === 'completed' ? 'green' :
                        event.status === 'in_progress' ? 'blue' :
                        event.status === 'cancelled' ? 'red' : 'orange'
                      }>
                        {event.status}
                      </Tag>
                    </Space>
                  </div>
                </div>
              ))}
              {furnitureCalendarService.getEventsByDate(selectedDate.toDate()).length === 0 && (
                <Empty description="لا توجد أحداث في هذا اليوم" image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </div>
          </Card>
        </Col>

        {/* معرض الصور الأخيرة */}
        <Col xs={24} lg={12}>
          <Card title={
            <Space>
              <FileImageOutlined />
              معرض الصور الأخيرة
            </Space>
          }>
            <div style={{ maxHeight: '250px', overflowY: 'auto' }}>
              {imageStats.recentImages.length > 0 ? (
                <Row gutter={[8, 8]}>
                  {imageStats.recentImages.slice(0, 6).map(image => (
                    <Col span={8} key={image.id}>
                      <div style={{ textAlign: 'center' }}>
                        <Image
                          width={60}
                          height={60}
                          src={image.thumbnailUrl}
                          alt={image.originalName}
                          style={{ objectFit: 'cover', borderRadius: '6px' }}
                        />
                        <div style={{ fontSize: '10px', color: '#666', marginTop: '4px' }}>
                          {image.originalName.substring(0, 10)}...
                        </div>
                        <div style={{ fontSize: '9px', color: '#999' }}>
                          {image.uploadedAt.toLocaleDateString('ar-SA')}
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              ) : (
                <Empty description="لا توجد صور" image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default FurnitureEnhancedFeatures
