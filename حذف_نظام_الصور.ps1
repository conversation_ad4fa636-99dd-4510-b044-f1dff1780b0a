# ═══════════════════════════════════════════════════════════════
# سكريبت PowerShell لحذف نظام الصور بالكامل
# ═══════════════════════════════════════════════════════════════
# 
# تحذير: هذا السكريبت سيحذف جميع ملفات ومجلدات نظام الصور
# تأكد من عمل نسخة احتياطية قبل التشغيل!
#
# ═══════════════════════════════════════════════════════════════

Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "  بدء عملية حذف نظام الصور" -ForegroundColor Yellow
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""

# التأكد من أن المستخدم يريد المتابعة
$confirmation = Read-Host "هل أنت متأكد من حذف نظام الصور بالكامل؟ (اكتب 'نعم' للمتابعة)"
if ($confirmation -ne "نعم") {
    Write-Host "تم إلغاء العملية" -ForegroundColor Red
    exit
}

Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "  المرحلة 1: حذف المجلدات الكاملة" -ForegroundColor Green
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""

# حذف مجلد components/images
$path1 = "src/renderer/src/components/images"
if (Test-Path $path1) {
    Remove-Item -Path $path1 -Recurse -Force
    Write-Host "✅ تم حذف: $path1" -ForegroundColor Green
} else {
    Write-Host "⚠️  غير موجود: $path1" -ForegroundColor Yellow
}

# حذف مجلد services/images
$path2 = "src/renderer/src/services/images"
if (Test-Path $path2) {
    Remove-Item -Path $path2 -Recurse -Force
    Write-Host "✅ تم حذف: $path2" -ForegroundColor Green
} else {
    Write-Host "⚠️  غير موجود: $path2" -ForegroundColor Yellow
}

# حذف مجلد hooks/images
$path3 = "src/renderer/src/hooks/images"
if (Test-Path $path3) {
    Remove-Item -Path $path3 -Recurse -Force
    Write-Host "✅ تم حذف: $path3" -ForegroundColor Green
} else {
    Write-Host "⚠️  غير موجود: $path3" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "  المرحلة 2: حذف ملفات Common" -ForegroundColor Green
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""

$commonFiles = @(
    "src/renderer/src/components/common/ImageGallery.tsx",
    "src/renderer/src/components/common/ImageService.ts",
    "src/renderer/src/components/common/ImageSettings.tsx",
    "src/renderer/src/components/common/ImageWithFallback.tsx",
    "src/renderer/src/components/common/CheckImageManager.tsx",
    "src/renderer/src/components/common/ImagePrintTest.tsx",
    "src/renderer/src/components/common/UniversalImagePrint.tsx",
    "src/renderer/src/components/common/UniversalImagePrintDemo.tsx",
    "src/renderer/src/components/common/InvoiceImagesPrintButton.tsx",
    "src/renderer/src/components/common/ProductionOrderImagesPrintButton.tsx",
    "src/renderer/src/components/common/CustomerImagesPrintButton.tsx",
    "src/renderer/src/components/common/SmartImagePrintButton.tsx",
    "src/renderer/src/components/common/README_IMAGE_PRINT.md"
)

foreach ($file in $commonFiles) {
    if (Test-Path $file) {
        Remove-Item -Path $file -Force
        Write-Host "✅ تم حذف: $file" -ForegroundColor Green
    } else {
        Write-Host "⚠️  غير موجود: $file" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "  المرحلة 3: حذف ملفات أخرى" -ForegroundColor Green
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""

$otherFiles = @(
    "src/renderer/src/components/examples/ImageSystemExample.tsx",
    "src/renderer/src/components/debug/ImageDebugger.tsx",
    "src/renderer/src/components/inventory/EnhancedItemImagePrint.tsx",
    "src/renderer/src/components/production/furniture/FurnitureImageService.ts",
    "src/renderer/src/services/UniversalImageService.ts",
    "src/renderer/src/utils/imageUtils.ts",
    "src/main/handlers/imageHandlers.ts"
)

foreach ($file in $otherFiles) {
    if (Test-Path $file) {
        Remove-Item -Path $file -Force
        Write-Host "✅ تم حذف: $file" -ForegroundColor Green
    } else {
        Write-Host "⚠️  غير موجود: $file" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "  المرحلة 4: حذف ملفات الاختبار" -ForegroundColor Green
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""

$testFiles = @(
    "src/test/image-print-system-test.ts",
    "src/test/run-image-print-test.ts"
)

foreach ($file in $testFiles) {
    if (Test-Path $file) {
        Remove-Item -Path $file -Force
        Write-Host "✅ تم حذف: $file" -ForegroundColor Green
    } else {
        Write-Host "⚠️  غير موجود: $file" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "  المرحلة 5: حذف ملفات التوثيق" -ForegroundColor Green
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""

$docFile = "docs/IMAGE_SYSTEM_DATABASE_INTEGRATION_COMPLETE.md"
if (Test-Path $docFile) {
    Remove-Item -Path $docFile -Force
    Write-Host "✅ تم حذف: $docFile" -ForegroundColor Green
} else {
    Write-Host "⚠️  غير موجود: $docFile" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "  ملخص العملية" -ForegroundColor Yellow
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""
Write-Host "✅ تم حذف جميع ملفات ومجلدات نظام الصور" -ForegroundColor Green
Write-Host ""
Write-Host "⚠️  الخطوات المتبقية يدوياً:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. حذف جداول قاعدة البيانات:" -ForegroundColor White
Write-Host "   - unified_images" -ForegroundColor Gray
Write-Host "   - item_images" -ForegroundColor Gray
Write-Host "   - production_order_images" -ForegroundColor Gray
Write-Host "   - customer_images" -ForegroundColor Gray
Write-Host "   - check_images" -ForegroundColor Gray
Write-Host ""
Write-Host "2. تعديل الملفات التي تستورد مكونات الصور:" -ForegroundColor White
Write-Host "   - ItemManagement.tsx" -ForegroundColor Gray
Write-Host "   - Dashboard.tsx" -ForegroundColor Gray
Write-Host "   - main.ts" -ForegroundColor Gray
Write-Host "   - SimpleDatabaseService.ts" -ForegroundColor Gray
Write-Host "   - ProductionService.ts" -ForegroundColor Gray
Write-Host "   - global.d.ts" -ForegroundColor Gray
Write-Host "   - ملفات index.ts" -ForegroundColor Gray
Write-Host ""
Write-Host "3. حذف مجلد الصور من userData (إذا كان موجوداً)" -ForegroundColor White
Write-Host ""
Write-Host "4. اختبار التطبيق والتأكد من عدم وجود أخطاء" -ForegroundColor White
Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "  انتهت عملية الحذف" -ForegroundColor Green
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""

# إيقاف مؤقت للسماح بقراءة النتائج
Read-Host "اضغط Enter للخروج"

