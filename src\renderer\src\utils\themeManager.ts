// مدير الثيم المتقدم
import { theme } from 'antd'
import { SafeLogger as Logger } from './logger'

// أنواع الثيمات المتاحة
export type ThemeMode = 'light' | 'dark' | 'auto'
export type ColorScheme = 'blue' | 'green' | 'purple' | 'orange' | 'red'

export interface ThemeConfig {
  mode: ThemeMode
  colorScheme: ColorScheme
  primaryColor: string
  borderRadius: number
  fontSize: number
  fontFamily: string
  compactMode: boolean
  sidebarCollapsed: boolean
  customColors?: {
    success?: string
    warning?: string
    error?: string
    info?: string
  }
}

export interface ThemePreset {
  name: string
  displayName: string
  config: ThemeConfig
}

// ألوان النظام المحسنة
export const colorPalettes = {
  blue: {
    primary: '#1890ff',
    secondary: '#096dd9',
    success: '#fff3cd',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1890ff'
  },
  green: {
    primary: '#fff3cd',
    secondary: '#389e0d',
    success: '#fff3cd',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1890ff'
  },
  purple: {
    primary: '#722ed1',
    secondary: '#531dab',
    success: '#fff3cd',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1890ff'
  },
  orange: {
    primary: '#fa8c16',
    secondary: '#d46b08',
    success: '#fff3cd',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1890ff'
  },
  red: {
    primary: '#f5222d',
    secondary: '#cf1322',
    success: '#fff3cd',
    warning: '#faad14',
    error: '#ff4d4f',
    info: '#1890ff'
  }
}

// الثيمات المحددة مسبقاً
export const themePresets: ThemePreset[] = [
  {
    name: 'default-light',
    displayName: 'الافتراضي الفاتح',
    config: {
      mode: 'light',
      colorScheme: 'blue',
      primaryColor: '#1890ff',
      borderRadius: 6,
      fontSize: 14,
      fontFamily: 'Segoe UI, Tahoma, Arial, sans-serif',
      compactMode: false,
      sidebarCollapsed: false
    }
  },
  {
    name: 'default-dark',
    displayName: 'الافتراضي الداكن',
    config: {
      mode: 'dark',
      colorScheme: 'blue',
      primaryColor: '#1890ff',
      borderRadius: 6,
      fontSize: 14,
      fontFamily: 'Segoe UI, Tahoma, Arial, sans-serif',
      compactMode: false,
      sidebarCollapsed: false
    }
  },
  {
    name: 'blue-theme',
    displayName: 'الثيم الأزرق',
    config: {
      mode: 'light',
      colorScheme: 'blue',
      primaryColor: '#0078D4',
      borderRadius: 8,
      fontSize: 14,
      fontFamily: 'Segoe UI, Tahoma, Arial, sans-serif',
      compactMode: false,
      sidebarCollapsed: false,
      customColors: {
        success: '#107C10',
        warning: '#FF8C00',
        error: '#D13438',
        info: '#0078D4'
      }
    }
  },
  {
    name: 'green-theme',
    displayName: 'الثيم الأخضر',
    config: {
      mode: 'light',
      colorScheme: 'green',
      primaryColor: '#fff3cd',
      borderRadius: 6,
      fontSize: 14,
      fontFamily: 'Segoe UI, Tahoma, Arial, sans-serif',
      compactMode: false,
      sidebarCollapsed: false,
      customColors: {
        success: '#fff3cd',
        warning: '#faad14',
        error: '#ff4d4f',
        info: '#1890ff'
      }
    }
  },
  {
    name: 'purple-theme',
    displayName: 'الثيم البنفسجي',
    config: {
      mode: 'light',
      colorScheme: 'purple',
      primaryColor: '#722ed1',
      borderRadius: 8,
      fontSize: 14,
      fontFamily: 'Segoe UI, Tahoma, Arial, sans-serif',
      compactMode: false,
      sidebarCollapsed: false,
      customColors: {
        success: '#fff3cd',
        warning: '#faad14',
        error: '#ff4d4f',
        info: '#722ed1'
      }
    }
  },
  {
    name: 'compact-light',
    displayName: 'مضغوط فاتح',
    config: {
      mode: 'light',
      colorScheme: 'blue',
      primaryColor: '#1890ff',
      borderRadius: 4,
      fontSize: 13,
      fontFamily: 'Segoe UI, Tahoma, Arial, sans-serif',
      compactMode: true,
      sidebarCollapsed: true,
      customColors: {
        success: '#fff3cd',
        warning: '#faad14',
        error: '#ff4d4f',
        info: '#1890ff'
      }
    }
  }
]

export class ThemeManager {
  private static instance: ThemeManager
  private currentTheme: ThemeConfig
  private listeners: ((theme: ThemeConfig) => void)[] = []

  private constructor() {
    this.currentTheme = this.getDefaultTheme()
    this.loadThemeFromStorage()

    // بدء مراقبة DOM عند تهيئة مدير الثيم
    if (typeof window !== 'undefined') {
      setTimeout(() => this.observeDOM(), 1000) // تأخير بسيط للتأكد من تحميل DOM
    }
  }

  public static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager()
    }
    return ThemeManager.instance
  }

  private getDefaultTheme(): ThemeConfig {
    return themePresets[0].config
  }

  // تحميل الثيم من التخزين المحلي
  private loadThemeFromStorage(): void {
    try {
      const savedTheme = localStorage.getItem('app-theme')
      if (savedTheme) {
        const parsedTheme = JSON.parse(savedTheme)
        this.currentTheme = { ...this.getDefaultTheme(), ...parsedTheme }
      }
    } catch (error) {
      Logger.error('ThemeManager', 'خطأ في تحميل الثيم من التخزين:', error)
    }
  }

  // حفّ الثيم في التخزين المحلي
  private saveThemeToStorage(): void {
    try {
      localStorage.setItem('app-theme', JSON.stringify(this.currentTheme))
    } catch (error) {
      Logger.error('ThemeManager', 'خطأ في حفّ الثيم:', error)
    }
  }

  // حفّ الثيم في قاعدة البيانات
  private async saveThemeToDatabase(): Promise<void> {
    try {
      if (window.electronAPI) {
        const themeSettings: any = {
          theme_mode: this.currentTheme.mode,
          primary_color: this.currentTheme.primaryColor,
          border_radius: this.currentTheme.borderRadius.toString(),
          font_size: this.currentTheme.fontSize.toString(),
          font_family: this.currentTheme.fontFamily,
          compact_mode: this.currentTheme.compactMode.toString(),
          sidebar_collapsed: this.currentTheme.sidebarCollapsed.toString()
        }

        // حفّ الألوان المخصصة
        if (this.currentTheme.customColors) {
          if (this.currentTheme.customColors.success) {
            themeSettings.custom_success_color = this.currentTheme.customColors.success
          }
          if (this.currentTheme.customColors.warning) {
            themeSettings.custom_warning_color = this.currentTheme.customColors.warning
          }
          if (this.currentTheme.customColors.error) {
            themeSettings.custom_error_color = this.currentTheme.customColors.error
          }
          if (this.currentTheme.customColors.info) {
            themeSettings.custom_info_color = this.currentTheme.customColors.info
          }
        }

        const result = await window.electronAPI.updateSettings(themeSettings)
        if (result.success) {
          Logger.info('ThemeManager', '✅ تم حفّ الثيم في قاعدة البيانات بنجاح')
        } else {
          Logger.error('ThemeManager', '❌ فشل في حفّ الثيم:', result.message)
          // لا نرمي خطأ هنا لأن الثيم يمكن أن يعمل بدون قاعدة البيانات
        }
      }
    } catch (error) {
      Logger.error('ThemeManager', 'خطأ في حفّ الثيم في قاعدة البيانات:', error)
    }
  }

  // الحصول على الثيم الحالي
  public getCurrentTheme(): ThemeConfig {
    return { ...this.currentTheme }
  }

  // تطبيق ثيم جديد
  public async applyTheme(newTheme: Partial<ThemeConfig>): Promise<void> {
    try {
      this.currentTheme = { ...this.currentTheme, ...newTheme }

      // حفّ في التخزين المحلي
      this.saveThemeToStorage()

      // حفّ في قاعدة البيانات (لا نتوقف إذا فشل)
      try {
        await this.saveThemeToDatabase()
      } catch (dbError) {
        Logger.warn('ThemeManager', 'تحذير: فشل في حفّ الثيم في قاعدة البيانات، لكن الثيم سيعمل محلياً:', dbError)
      }

      // تطبيق الثيم على DOM
      this.applyThemeToDOM()

      // إشعار المستمعين
      this.notifyListeners()
    } catch (error) {
      Logger.error('ThemeManager', 'خطأ في تطبيق الثيم:', error)
      throw error
    }
  }

  // تطبيق ثيم محدد مسبقاً
  public async applyPreset(presetName: string): Promise<void> {
    const preset = themePresets.find(p => p.name === presetName)
    if (preset) {
      await this.applyTheme(preset.config)
    }
  }

  // تبديل بين الوضع الفاتح والداكن
  public async toggleMode(): Promise<void> {
    const newMode = this.currentTheme.mode === 'light' ? 'dark' : 'light'
    await this.applyTheme({ mode: newMode })
  }

  // تطبيق الثيم على DOM
  private applyThemeToDOM(): void {
    const root = document.documentElement

    // تطبيق متغيرات CSS الأساسية
    root.style.setProperty('--primary-color', this.currentTheme.primaryColor)
    root.style.setProperty('--border-radius', `${this.currentTheme.borderRadius}px`)
    root.style.setProperty('--font-size', `${this.currentTheme.fontSize}px`)
    root.style.setProperty('--font-family', this.currentTheme.fontFamily)

    // تطبيق نصف قطر الحواف على جميع العناصر
    this.applyBorderRadiusToElements()

    // تطبيق الألوان المخصصة
    if (this.currentTheme.customColors) {
      const colors = this.currentTheme.customColors
      if (colors.success) root.style.setProperty('--success-color', colors.success)
      if (colors.warning) root.style.setProperty('--warning-color', colors.warning)
      if (colors.error) root.style.setProperty('--error-color', colors.error)
      if (colors.info) root.style.setProperty('--info-color', colors.info)
    }

    // تطبيق ألوان القائمة الجانبية
    const isDark = this.currentTheme.mode === 'dark'
    root.style.setProperty('--sidebar-bg-color', isDark ? '#000c17' : '#001529')
    root.style.setProperty('--menu-selected-color', this.currentTheme.primaryColor)
    root.style.setProperty('--menu-hover-color', isDark ? '#112545' : '#112545')
    root.style.setProperty('--header-bg-color', isDark ? '#141414' : '#ffffff')
    root.style.setProperty('--content-bg-color', isDark ? '#000000' : '#f0f2f5')

    // تطبيق الوضع الداكن/الفاتح
    root.setAttribute('data-theme', this.currentTheme.mode)

    // تطبيق الوضع المضغوط
    if (this.currentTheme.compactMode) {
      root.classList.add('compact-mode')
    } else {
      root.classList.remove('compact-mode')
    }

    Logger.info('ThemeManager', '🎨 تم تطبيق الثيم على DOM:', {
      mode: this.currentTheme.mode,
      primaryColor: this.currentTheme.primaryColor,
      borderRadius: this.currentTheme.borderRadius,
      customColors: this.currentTheme.customColors
    })
  }

  // تطبيق نصف قطر الحواف على جميع العناصر
  private applyBorderRadiusToElements(): void {
    const borderRadius = this.currentTheme.borderRadius

    // إنشاء أو تحديث style element للـ border radius
    let styleElement = document.getElementById('dynamic-border-radius-styles')
    if (!styleElement) {
      styleElement = document.createElement('style')
      styleElement.id = 'dynamic-border-radius-styles'
      document.head.appendChild(styleElement)
    }

    // CSS rules لتطبيق نصف قطر الحواف على جميع العناصر
    const css = `
      /* تطبيق نصف قطر الحواف على عناصر Ant Design */
      .ant-card,
      .ant-modal-content,
      .ant-drawer-content,
      .ant-popover-content,
      .ant-tooltip-content,
      .ant-dropdown-menu,
      .ant-select-dropdown,
      .ant-picker-dropdown,
      .ant-menu,
      .ant-table-container,
      .ant-pagination,
      .ant-alert,
      .ant-message,
      .ant-notification-notice,
      .ant-progress,
      .ant-tag,
      .ant-badge,
      .ant-avatar,
      .ant-image,
      .ant-upload,
      .ant-steps-item,
      .ant-collapse,
      .ant-tabs-content,
      .ant-form-item,
      .ant-statistic,
      .ant-result,
      .ant-empty,
      .ant-spin-container,
      .ant-affix,
      .ant-anchor,
      .ant-back-top,
      .ant-divider {
        border-radius: ${borderRadius}px !important;
      }

      /* تطبيق على عناصر الإدخال */
      .ant-input,
      .ant-input-number,
      .ant-select-selector,
      .ant-picker,
      .ant-cascader-picker,
      .ant-tree-select-selector,
      .ant-mentions,
      .ant-textarea,
      .ant-btn,
      .ant-switch,
      .ant-slider-handle,
      .ant-rate-star,
      .ant-checkbox-wrapper,
      .ant-radio-wrapper {
        border-radius: ${borderRadius}px !important;
      }

      /* تطبيق على العناصر المخصصة */
      .settings-section,
      .dashboard-card,
      .stat-card,
      .welcome-card,
      .tutorial-card,
      .invoice-card,
      .product-card,
      .customer-card,
      .report-card {
        border-radius: ${borderRadius}px !important;
      }

      /* تطبيق على المحتوى الرئيسي */
      .ant-layout-content,
      .main-content,
      .content-wrapper {
        border-radius: ${borderRadius}px !important;
      }

      /* تطبيق على الجداول */
      .ant-table,
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        border-radius: ${borderRadius}px !important;
      }

      /* تطبيق على النوافذ المنبثقة */
      .ant-modal-content,
      .ant-drawer-content-wrapper,
      .ant-popconfirm-inner-content {
        border-radius: ${borderRadius}px !important;
      }
    `

    styleElement.textContent = css
    Logger.info('ThemeManager', '✅ تم تطبيق نصف قطر الحواف ${borderRadius}px على جميع العناصر')
  }

  // الحصول على تكوين Ant Design
  public getAntdThemeConfig(): any {
    const isDark = this.currentTheme.mode === 'dark'
    
    return {
      algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
      token: {
        colorPrimary: this.currentTheme.primaryColor,
        colorSuccess: this.currentTheme.customColors?.success || '#fff3cd',
        colorWarning: this.currentTheme.customColors?.warning || '#faad14',
        colorError: this.currentTheme.customColors?.error || '#ff4d4f',
        colorInfo: this.currentTheme.customColors?.info || this.currentTheme.primaryColor,
        borderRadius: this.currentTheme.borderRadius,
        fontSize: this.currentTheme.fontSize,
        fontFamily: this.currentTheme.fontFamily,
        controlHeight: this.currentTheme.compactMode ? 28 : 32,
        controlHeightLG: this.currentTheme.compactMode ? 36 : 40,
        controlHeightSM: this.currentTheme.compactMode ? 20 : 24,
        // ألوان الخلفية للوضع الداكن
        ...(isDark && {
          colorBgContainer: '#141414',
          colorBgElevated: '#1f1f1f',
          colorBgLayout: '#000000',
          colorText: 'rgba(255, 255, 255, 0.85)',
          colorTextSecondary: 'rgba(255, 255, 255, 0.65)',
          colorBorder: '#434343',
          colorSplit: '#434343'
        })
      },
      components: {
        Layout: {
          bodyBg: isDark ? '#000000' : '#f0f2f5',
          headerBg: isDark ? '#141414' : '#ffffff',
          siderBg: isDark ? '#000c17' : '#001529',
          triggerBg: isDark ? '#002140' : '#002140'
        },
        Menu: {
          darkItemBg: isDark ? '#000c17' : '#001529',
          darkSubMenuItemBg: isDark ? '#000c17' : '#000c17',
          darkItemSelectedBg: this.currentTheme.primaryColor,
          darkItemHoverBg: isDark ? '#112545' : '#112545',
          darkItemColor: 'rgba(255, 255, 255, 0.85)',
          darkItemSelectedColor: '#ffffff',
          darkItemDisabledColor: 'rgba(255, 255, 255, 0.35)',
          itemMarginBlock: 4,
          itemMarginInline: 4,
          itemPaddingInline: 12,
          subMenuItemBorderRadius: 6,
          itemBorderRadius: 6
        },
        Card: {
          colorBgContainer: isDark ? '#141414' : '#ffffff',
          borderRadius: this.currentTheme.borderRadius
        },
        Button: {
          borderRadius: this.currentTheme.borderRadius,
          controlHeight: this.currentTheme.compactMode ? 28 : 32
        },
        Input: {
          borderRadius: this.currentTheme.borderRadius,
          controlHeight: this.currentTheme.compactMode ? 28 : 32
        },
        Table: {
          colorBgContainer: isDark ? '#141414' : '#ffffff',
          headerBg: isDark ? '#1f1f1f' : '#fafafa'
        }
      }
    }
  }

  // إضافة مستمع للتغييرات
  public addListener(listener: (theme: ThemeConfig) => void): void {
    this.listeners.push(listener)
  }

  // إزالة مستمع
  public removeListener(listener: (theme: ThemeConfig) => void): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  // إشعار جميع المستمعين
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.currentTheme)
      } catch (error) {
        Logger.error('ThemeManager', 'خطأ في إشعار مستمع الثيم:', error)
      }
    })
  }

  // تحميل الثيم من قاعدة البيانات
  public async loadThemeFromDatabase(): Promise<void> {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.getSettings()
        if (result.success) {
          const settings: any = {}
          result.data.forEach((setting: any) => {
            settings[setting.key] = setting.value
          })

          const themeConfig: Partial<ThemeConfig> = {}

          // تحميل الإعدادات الأساسية
          if (settings.theme_mode) themeConfig.mode = settings.theme_mode as 'light' | 'dark'
          if (settings.primary_color) themeConfig.primaryColor = settings.primary_color
          if (settings.border_radius) themeConfig.borderRadius = parseInt(settings.border_radius)
          if (settings.font_size) themeConfig.fontSize = parseInt(settings.font_size)
          if (settings.font_family) themeConfig.fontFamily = settings.font_family
          if (settings.compact_mode) themeConfig.compactMode = settings.compact_mode === 'true'
          if (settings.sidebar_collapsed) themeConfig.sidebarCollapsed = settings.sidebar_collapsed === 'true'

          // تحميل الألوان المخصصة
          const customColors: any = {}
          if (settings.custom_success_color) customColors.success = settings.custom_success_color
          if (settings.custom_warning_color) customColors.warning = settings.custom_warning_color
          if (settings.custom_error_color) customColors.error = settings.custom_error_color
          if (settings.custom_info_color) customColors.info = settings.custom_info_color

          if (Object.keys(customColors).length > 0) {
            themeConfig.customColors = customColors
          }

          if (Object.keys(themeConfig).length > 0) {
            this.currentTheme = { ...this.currentTheme, ...themeConfig }
            this.applyThemeToDOM()
            this.notifyListeners()
            Logger.info('ThemeManager', '✅ تم تحميل الثيم من قاعدة البيانات:', this.currentTheme)
          }
        }
      }
    } catch (error) {
      Logger.error('ThemeManager', 'خطأ في تحميل الثيم من قاعدة البيانات:', error)
    }
  }

  // إعادة تعيين الثيم للافتراضي
  public async resetToDefault(): Promise<void> {
    await this.applyTheme(this.getDefaultTheme())
  }

  // تصدير الثيم الحالي
  public exportTheme(): string {
    return JSON.stringify(this.currentTheme, null, 2)
  }

  // استيراد ثيم من JSON
  public async importTheme(themeJson: string): Promise<void> {
    try {
      const importedTheme = JSON.parse(themeJson)
      await this.applyTheme(importedTheme)
    } catch {
      throw new Error('تنسيق الثيم غير صحيح')
    }
  }

  // الوظائف المحسنة الجديدة

  // تغيير نظام الألوان
  public async setColorScheme(colorScheme: ColorScheme): Promise<void> {
    const palette = colorPalettes[colorScheme]
    await this.applyTheme({
      colorScheme,
      primaryColor: palette.primary,
      customColors: {
        success: palette.success,
        warning: palette.warning,
        error: palette.error,
        info: palette.info
      }
    })
  }

  // الحصول على نظام الألوان الحالي
  public getColorScheme(): ColorScheme {
    return this.currentTheme.colorScheme || 'blue'
  }

  // تغيير وضع الثيم مع دعم التلقائي
  public async setMode(mode: ThemeMode): Promise<void> {
    await this.applyTheme({ mode })
  }

  // الحصول على الوضع الحالي
  public getMode(): ThemeMode {
    return this.currentTheme.mode || 'light'
  }

  // الحصول على الوضع الفعلي (حل التلقائي)
  public getEffectiveThemeMode(): 'light' | 'dark' {
    if (this.currentTheme.mode === 'auto') {
      // التحقق من تفضيل النظام
      if (typeof window !== 'undefined' && window.matchMedia) {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      }
      return 'light'
    }
    return this.currentTheme.mode as 'light' | 'dark'
  }

  // الحصول على الألوان المتاحة
  public getAvailableColorSchemes(): ColorScheme[] {
    return Object.keys(colorPalettes) as ColorScheme[]
  }

  // الحصول على معلومات نظام الألوان
  public getColorSchemeInfo(scheme: ColorScheme) {
    return colorPalettes[scheme]
  }

  // مراقبة تغييرات DOM لتطبيق نصف قطر الحواف على العناصر الجديدة
  private observeDOM(): void {
    if (typeof MutationObserver !== 'undefined') {
      const observer = new MutationObserver((mutations) => {
        let shouldReapplyBorderRadius = false

        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element
                // التحقق من وجود عناصر Ant Design جديدة
                if (element.classList.contains('ant-') ||
                    element.querySelector && element.querySelector('[class*="ant-"]')) {
                  shouldReapplyBorderRadius = true
                }
              }
            })
          }
        })

        // إعادة تطبيق نصف قطر الحواف عند إضافة عناصر جديدة
        if (shouldReapplyBorderRadius) {
          this.applyBorderRadiusToElements()
        }
      })

      observer.observe(document.body, {
        childList: true,
        subtree: true
      })

      Logger.info('ThemeManager', '✅ تم تفعيل مراقبة DOM لنصف قطر الحواف')
    }
  }
}

// تصدير المثيل الوحيد
export const themeManager = ThemeManager.getInstance()

// دوال مساعدة
export const getCurrentTheme = () => themeManager.getCurrentTheme()
export const applyTheme = (theme: Partial<ThemeConfig>) => themeManager.applyTheme(theme)
export const toggleThemeMode = () => themeManager.toggleMode()
export const applyThemePreset = (presetName: string) => themeManager.applyPreset(presetName)
