import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Alert,
  List,
  Button,
  Tag,
  Progress,
  Collapse,
  Tooltip,
  Divider,
  Space,
  Statistic,
  message,
  Spin
} from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  Bar<PERSON>hartOutlined,
  BankOutlined,
  FileTextOutlined,
  ShoppingOutlined,
  CloudUploadOutlined
} from '@ant-design/icons';
import { FiscalPeriod, ValidationResult, ValidationSummary } from '../../src/types/fiscalPeriod';
import { fiscalPeriodApi } from '../../src/services/fiscalPeriodApi';

const { Panel } = Collapse;
const { Title, Text } = Typography;

interface ValidationDashboardProps {
  period: FiscalPeriod;
  onValidationComplete?: (canClose: boolean) => void;
}

const ValidationDashboard: React.FC<ValidationDashboardProps> = ({
  period,
  onValidationComplete
}) => {
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [validationSummary, setValidationSummary] = useState<ValidationSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    performValidation();
  }, [period.id]);

  const performValidation = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [results, summary] = await Promise.all([
        fiscalPeriodApi.validatePeriodForClosing(period.id.toString()),
        fiscalPeriodApi.getValidationSummary(period.id.toString())
      ]);
      
      setValidationResults(results);
      setValidationSummary(summary);
      
      const canClose = !results.some(r => r.type === 'error');
      onValidationComplete?.(canClose);
      
      message.success('تم فحص صحة البيانات بنجاح');
    } catch (err) {
      setError('فشل في تنفيذ عملية التحقق');
      message.error('فشل في تنفيذ عملية التحقق');
      console.error('Validation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getValidationIcon = (type: string) => {
    switch (type) {
      case 'error': return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'warning': return <WarningOutlined style={{ color: '#faad14' }} />;
      case 'info': return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      default: return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    }
  };

  const getValidationColor = (type: string) => {
    switch (type) {
      case 'error': return 'red';
      case 'warning': return 'orange';
      case 'info': return 'blue';
      default: return 'green';
    }
  };

  const getSectionIcon = (section: string) => {
    switch (section) {
      case 'trial_balance': return <BankOutlined />;
      case 'unposted_entries': return <FileTextOutlined />;
      case 'bank_reconciliation': return <BankOutlined />;
      case 'pending_invoices': return <FileTextOutlined />;
      case 'inventory_status': return <ShoppingOutlined />;
      case 'backup_status': return <CloudUploadOutlined />;
      default: return <BarChartOutlined />;
    }
  };

  const getSectionTitle = (section: string) => {
    switch (section) {
      case 'trial_balance': return 'ميزان المراجعة';
      case 'unposted_entries': return 'القيود غير المرحلة';
      case 'bank_reconciliation': return 'مطابقة البنوك';
      case 'pending_invoices': return 'الفواتير المعلقة';
      case 'inventory_status': return 'حالة المخزون';
      case 'backup_status': return 'النسخ الاحتياطية';
      default: return section;
    }
  };

  const groupResultsBySection = () => {
    const grouped: Record<string, ValidationResult[]> = {};
    validationResults.forEach(result => {
      if (!grouped[result.section]) {
        grouped[result.section] = [];
      }
      grouped[result.section].push(result);
    });
    return grouped;
  };

  const getSectionSummary = (section: string, results: ValidationResult[]) => {
    const errors = results.filter(r => r.type === 'error').length;
    const warnings = results.filter(r => r.type === 'warning').length;
    const info = results.filter(r => r.type === 'info').length;
    const success = results.filter(r => r.type === 'success').length;

    return { errors, warnings, info, success };
  };

  const renderSummaryCards = () => {
    if (!validationSummary) return null;

    return (
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6} md={6} lg={6}>
          <Card>
            <Statistic
              title="أخطاء"
              value={validationSummary.totalErrors}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        
        <Col xs={12} sm={6} md={6} lg={6}>
          <Card>
            <Statistic
              title="تحذيرات"
              value={validationSummary.totalWarnings}
              valueStyle={{ color: '#faad14' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        
        <Col xs={12} sm={6} md={6} lg={6}>
          <Card>
            <Statistic
              title="معلومات"
              value={validationSummary.totalInfo}
              valueStyle={{ color: '#1890ff' }}
              prefix={<InfoCircleOutlined />}
            />
          </Card>
        </Col>
        
        <Col xs={12} sm={6} md={6} lg={6}>
          <Card>
            <Statistic
              title="نجح"
              value={validationSummary.totalSuccess}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  const renderValidationSection = (section: string, results: ValidationResult[]) => {
    const summary = getSectionSummary(section, results);
    const hasErrors = summary.errors > 0;

    return (
      <Panel
        header={
          <Space>
            {getSectionIcon(section)}
            <Text strong>{getSectionTitle(section)}</Text>
            <Space>
              {summary.errors > 0 && <Tag color="red">{summary.errors} خطأ</Tag>}
              {summary.warnings > 0 && <Tag color="orange">{summary.warnings} تحذير</Tag>}
              {summary.info > 0 && <Tag color="blue">{summary.info} معلومة</Tag>}
              {summary.success > 0 && <Tag color="green">{summary.success} نجح</Tag>}
            </Space>
          </Space>
        }
        key={section}
        style={{
          borderColor: hasErrors ? '#ff4d4f' : undefined,
          borderWidth: hasErrors ? 2 : 1
        }}
      >
        <List
          dataSource={results}
          renderItem={(result) => (
            <List.Item>
              <List.Item.Meta
                avatar={getValidationIcon(result.type)}
                title={
                  <Space>
                    <Text>{result.message}</Text>
                    <Tag color={getValidationColor(result.type)}>
                      {result.type === 'error' ? 'خطأ' : 
                       result.type === 'warning' ? 'تحذير' : 
                       result.type === 'info' ? 'معلومة' : 'نجح'}
                    </Tag>
                  </Space>
                }
                description={result.details}
              />
            </List.Item>
          )}
        />
      </Panel>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>جاري فحص صحة البيانات...</Text>
        </div>
      </div>
    );
  }

  const groupedResults = groupResultsBySection();
  const canClose = validationSummary?.canClose || false;

  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={3}>
          فحص صحة البيانات - {period.period_name}
        </Title>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={performValidation}
          loading={loading}
        >
          إعادة الفحص
        </Button>
      </div>

      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      {validationSummary && (
        <>
          <Alert
            message={
              canClose 
                ? 'يمكن إقفال الفترة المالية - جميع الفحوصات نجحت'
                : 'لا يمكن إقفال الفترة المالية - يوجد أخطاء يجب حلها'
            }
            type={canClose ? 'success' : 'error'}
            showIcon
            style={{ marginBottom: 24 }}
          />

          {renderSummaryCards()}

          <Card title="تفاصيل الفحص">
            <Collapse>
              {Object.entries(groupedResults).map(([section, results]) =>
                renderValidationSection(section, results)
              )}
            </Collapse>
          </Card>
        </>
      )}
    </div>
  );
};

export default ValidationDashboard;
