import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  Space,
  Popconfirm,
  Tag,
  Row,
  Col,
  Statistic,
  DatePicker
, App} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ArrowLeftOutlined,
  BankOutlined,
  TransactionOutlined,
  FileExcelOutlined,
  PrinterOutlined
} from '@ant-design/icons'
import * as XLSX from 'xlsx'
import dayjs from 'dayjs'
const { Option } = Select

interface BankAccountManagementProps {
  onBack: () => void
}

const BankAccountManagement: React.FC<BankAccountManagementProps> = ({ onBack }) => {
  const { message: messageApi } = App.useApp()
  const [accounts, setAccounts] = useState([])
  const [transactions, setTransactions] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [transactionModalVisible, setTransactionModalVisible] = useState(false)
  const [editingAccount, setEditingAccount] = useState<any>(null)
  const [selectedAccount, setSelectedAccount] = useState<any>(null)
  const [form] = Form.useForm()
  const [transactionForm] = Form.useForm()
  const [addTransactionModalVisible, setAddTransactionModalVisible] = useState(false)

  useEffect(() => {
    loadAccounts()
  }, [])

  const loadAccounts = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.getBankAccounts()
      if (response.success) {
        setAccounts(response.data)
      } else {
        messageApi.error('فشل في تحميل الحسابات المصرفية')
      }
    } catch (_error) {
      messageApi.error('خطأ في تحميل الحسابات المصرفية')
    }
    setLoading(false)
  }

  const loadTransactions = async (_accountId: number) => {
    try {
      const response = await window.electronAPI.getBankTransactions()
      if (response.success) {
        setTransactions(response.data)
      }
    } catch (_error) {
      messageApi.error('خطأ في تحميل المعاملات')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      // التحقق من صحة رقم الحساب
      if (!values.account_number || values.account_number.trim().length < 5) {
        messageApi.error('رقم الحساب يجب أن يكون على الأقل 5 أرقام')
        return
      }

      // التحقق من صحة الرصيد الافتتاحي
      const initialBalance = parseFloat(values.initial_balance) || 0
      if (initialBalance < 0) {
        messageApi.error('الرصيد الافتتاحي لا يمكن أن يكون سالباً')
        return
      }

      // التحقق من صحة الحد الائتماني
      const creditLimit = parseFloat(values.credit_limit) || 0
      if (creditLimit < 0) {
        messageApi.error('الحد الائتماني لا يمكن أن يكون سالباً')
        return
      }

      const accountData = {
        ...values,
        account_number: values.account_number.trim(),
        initial_balance: initialBalance,
        credit_limit: creditLimit
      }

      if (editingAccount) {
        const response = await window.electronAPI.updateBankAccount(editingAccount.id, accountData)
        if (response.success) {
          messageApi.success('تم تحديث الحساب بنجاح')
          loadAccounts()
          setModalVisible(false)
          setEditingAccount(null)
          form.resetFields()
        } else {
          messageApi.error('فشل في تحديث الحساب')
        }
      } else {
        const response = await window.electronAPI.createBankAccount(accountData)
        Logger.info('BankAccountManagement', 'Bank account creation response:', response)
        if (response.success) {
          messageApi.success('تم إضافة الحساب بنجاح')
          loadAccounts()
          setModalVisible(false)
          form.resetFields()
        } else {
          const errorMessage = response.message || 'فشل في إضافة الحساب'
          const errors = (response as any).errors ? (response as any).errors.join(', ') : ''
          messageApi.error(errorMessage + (errors ? ': ' + errors : ''))
          Logger.error('BankAccountManagement', 'Bank account creation failed:', response)
        }
      }
    } catch (error) {
      Logger.error('BankAccountManagement', 'Bank account creation error:', error)
      messageApi.error('خطأ في حفّ الحساب')
    }
  }

  const handleTransactionSubmit = async (values: any) => {
    try {
      const transactionData = {
        ...values,
        account_id: selectedAccount?.id || accounts[0]?.id,
        transaction_date: values.transaction_date?.format('YYYY-MM-DD'),
        created_by: 1 // يجب الحصول على معرف المستخدم الحالي
      }

      const response = await window.electronAPI.createBankTransaction(transactionData)
      if (response.success) {
        messageApi.success('تم إضافة المعاملة بنجاح')
        loadAccounts()
        if (selectedAccount) {
          loadTransactions(selectedAccount.id)
        }
        setTransactionModalVisible(false)
        setAddTransactionModalVisible(false)
        transactionForm.resetFields()
      } else {
        messageApi.error('فشل في إضافة المعاملة')
      }
    } catch (_error) {
      messageApi.error('خطأ في إضافة المعاملة')
    }
  }

  const handleEdit = (account: any) => {
    setEditingAccount(account)
    form.setFieldsValue(account)
    setModalVisible(true)
  }

  const handleDelete = async (accountId: number) => {
    try {
      const response = await window.electronAPI.deleteBankAccount(accountId)
      if (response.success) {
        messageApi.success('تم حذف الحساب بنجاح')
        loadAccounts()
      } else {
        messageApi.error(response.message || 'فشل في حذف الحساب')
      }
    } catch (_error) {
      messageApi.error('خطأ في حذف الحساب')
    }
  }

  const showTransactions = (account: any) => {
    setSelectedAccount(account)
    loadTransactions(account.id)
    setTransactionModalVisible(true)
  }

  // دالة تصدير Excel للحسابات البنكية
  const handleExportExcel = () => {
    try {
      // تحضير البيانات للتصدير
      const exportData = accounts.map(account => ({
        'رقم الحساب': account.account_number,
        'اسم الحساب': account.account_name,
        'اسم البنك': account.bank_name,
        'نوع الحساب': getAccountTypeText(account.account_type),
        'الرصيد الحالي': account.balance || 0,
        'الرصيد الافتتاحي': account.initial_balance || 0,
        'الحد الائتماني': account.credit_limit || 0,
        'الحالة': account.is_active ? 'نشط' : 'غير نشط',
        'تاريخ الإنشاء': dayjs(account.created_at).format('YYYY-MM-DD'),
        'ملاحّات': account.notes || ''
      }))

      // إنشاء ورقة العمل
      const worksheet = XLSX.utils.json_to_sheet(exportData)

      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 15 }, // رقم الحساب
        { wch: 20 }, // اسم الحساب
        { wch: 20 }, // اسم البنك
        { wch: 12 }, // نوع الحساب
        { wch: 15 }, // الرصيد الحالي
        { wch: 15 }, // الرصيد الافتتاحي
        { wch: 15 }, // الحد الائتماني
        { wch: 10 }, // الحالة
        { wch: 12 }, // تاريخ الإنشاء
        { wch: 25 }  // ملاحّات
      ]
      worksheet['!cols'] = columnWidths

      // إنشاء المصنف
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'الحسابات البنكية')

      // إضافة ورقة إحصائيات
      const stats = [
        { 'البيان': 'إجمالي عدد الحسابات', 'القيمة': accounts.length },
        { 'البيان': 'الحسابات النشطة', 'القيمة': accounts.filter(a => a.is_active).length },
        { 'البيان': 'الحسابات غير النشطة', 'القيمة': accounts.filter(a => !a.is_active).length },
        { 'البيان': 'إجمالي الأرصدة', 'القيمة': accounts.reduce((sum, a) => sum + (a.balance || 0), 0) },
        { 'البيان': 'إجمالي الحدود الائتمانية', 'القيمة': accounts.reduce((sum, a) => sum + (a.credit_limit || 0), 0) }
      ]

      const statsWorksheet = XLSX.utils.json_to_sheet(stats)
      statsWorksheet['!cols'] = [{ wch: 25 }, { wch: 15 }]
      XLSX.utils.book_append_sheet(workbook, statsWorksheet, 'الإحصائيات')

      // حفّ الملف
      const fileName = 'الحسابات_البنكية_' + dayjs().format('YYYY-MM-DD_HH-mm') + '.xlsx'
      XLSX.writeFile(workbook, fileName)

      messageApi.success('تم تصدير البيانات بنجاح')
    } catch (error) {
      Logger.error('BankAccountManagement', 'خطأ في تصدير Excel:', error)
      messageApi.error('فشل في تصدير البيانات')
    }
  }

  // دالة الطباعة المحسنة
  const handlePrint = () => {
    try {
      const printWindow = window.open('', '_blank')
      if (!printWindow) {
        messageApi.error('فشل في فتح نافذة الطباعة')
        return
      }

      const totalBalance = accounts.reduce((sum, account) => sum + (account.balance || 0), 0)
      const activeAccounts = accounts.filter(account => account.is_active).length

      const printContent = `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
          <meta charset="UTF-8">
          <title>تقرير الحسابات البنكية</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
            .report-title { font-size: 20px; color: #666; }
            .report-date { font-size: 14px; color: #888; margin-top: 10px; }
            .summary { margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-radius: 5px; }
            .summary-item { display: inline-block; margin: 0 20px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .status-active { color: green; font-weight: bold; }
            .status-inactive { color: red; }
            .amount { font-weight: bold; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="company-name">نّام إدارة الحسابات</div>
            <div class="report-title">تقرير الحسابات البنكية</div>
            <div class="report-date">تاريخ التقرير: ${dayjs().format('YYYY-MM-DD HH:mm')}</div>
          </div>

          <div class="summary">
            <div class="summary-item"><strong>إجمالي الحسابات:</strong> ${accounts.length}</div>
            <div class="summary-item"><strong>الحسابات النشطة:</strong> ${activeAccounts}</div>
            <div class="summary-item"><strong>إجمالي الأرصدة:</strong> ₪${totalBalance.toLocaleString()}</div>
          </div>

          <table>
            <thead>
              <tr>
                <th>رقم الحساب</th>
                <th>اسم الحساب</th>
                <th>البنك</th>
                <th>النوع</th>
                <th>الرصيد الحالي</th>
                <th>الحالة</th>
              </tr>
            </thead>
            <tbody>
              ${accounts.map(account => `
                <tr>
                  <td>${account.account_number}</td>
                  <td>${account.account_name}</td>
                  <td>${account.bank_name}</td>
                  <td>${getAccountTypeText(account.account_type)}</td>
                  <td class="amount">₪${(account.balance || 0).toLocaleString()}</td>
                  <td class="${account.is_active ? 'status-active' : 'status-inactive'}">
                    ${account.is_active ? 'نشط' : 'غير نشط'}
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="footer">
            تم إنشاء هذا التقرير بواسطة نّام إدارة الحسابات - ${dayjs().format('YYYY-MM-DD HH:mm')}
          </div>
        </body>
        </html>
      `

      const printDoc = (printWindow as any).document
      if (printDoc) {
        printDoc.write(printContent)
        printDoc.close()
      }
      printWindow.focus()
      printWindow.print()
    } catch (error) {
      Logger.error('BankAccountManagement', 'خطأ في الطباعة:', error)
      messageApi.error('فشل في طباعة التقرير')
    }
  }

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'current': return 'blue'
      case 'savings': return 'green'
      case 'business': return 'purple'
      default: return 'default'
    }
  }

  const getAccountTypeText = (type: string) => {
    switch (type) {
      case 'current': return 'جاري'
      case 'savings': return 'توفير'
      case 'business': return 'أعمال'
      default: return type
    }
  }

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'deposit': return 'green'
      case 'withdrawal': return 'red'
      case 'transfer': return 'blue'
      default: return 'default'
    }
  }

  const getTransactionTypeText = (type: string) => {
    switch (type) {
      case 'deposit': return 'إيداع'
      case 'withdrawal': return 'سحب'
      case 'transfer': return 'تحويل'
      default: return type
    }
  }

  const columns = [
    {
      title: 'اسم البنك',
      dataIndex: 'bank_name',
      key: 'bank_name' },
    {
      title: 'اسم الحساب',
      dataIndex: 'account_name',
      key: 'account_name' },
    {
      title: 'رقم الحساب',
      dataIndex: 'account_number',
      key: 'account_number' },
    {
      title: 'نوع الحساب',
      dataIndex: 'account_type',
      key: 'account_type',
      render: (type: string) => (
        <Tag color={getAccountTypeColor(type)}>
          {getAccountTypeText(type)}
        </Tag>
      ) },
    {
      title: 'الرصيد',
      dataIndex: 'balance',
      key: 'balance',
      render: (balance: number) => (
        <span style={{ color: balance >= 0 ? '#52c41a' : '#ff4d4f' }}>
          ₪ {balance?.toLocaleString() || 0}
        </span>
      ) },
    {
      title: 'الفرع',
      dataIndex: 'branch',
      key: 'branch' },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'نشط' : 'غير نشط'}
        </Tag>
      ) },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_: any, record: any) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<TransactionOutlined />}
            onClick={() => showTransactions(record)}
          >
            المعاملات
          </Button>
          <Button
            type="default"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            تعديل
          </Button>
          <Popconfirm
            title="هل أنت متأكد من حذف هذا الحساب؟"
            onConfirm={() => handleDelete(record.id)}
            okText="نعم"
            cancelText="لا"
          >
            <Button
              type="default"
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              حذف
            </Button>
          </Popconfirm>
        </Space>
      ) },
  ]

  const transactionColumns = [
    {
      title: 'التاريخ',
      dataIndex: 'transaction_date',
      key: 'transaction_date' },
    {
      title: 'النوع',
      dataIndex: 'transaction_type',
      key: 'transaction_type',
      render: (type: string) => (
        <Tag color={getTransactionTypeColor(type)}>
          {getTransactionTypeText(type)}
        </Tag>
      ) },
    {
      title: 'المبلغ',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number, record: any) => (
        <span style={{ 
          color: record.transaction_type === 'deposit' ? '#52c41a' : '#ff4d4f' 
        }}>
          {record.transaction_type === 'deposit' ? '+' : '-'}₪ {amount?.toLocaleString() || 0}
        </span>
      ) },
    {
      title: 'الوصف',
      dataIndex: 'description',
      key: 'description' },
    {
      title: 'رقم المرجع',
      dataIndex: 'reference_number',
      key: 'reference_number' },
  ]

  const totalBalance = accounts.reduce((sum: number, account: any) => sum + (account.balance || 0), 0)

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>🏦 إدارة البنوك والحسابات المصرفية</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            إدارة الحسابات المصرفية والمعاملات المالية
          </p>
        </div>
        <Button 
          type="default" 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
        >
          العودة
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الحسابات"
              value={accounts.length}
              valueStyle={{ color: '#1890ff' }}
              prefix={<BankOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الأرصدة"
              value={totalBalance}
              valueStyle={{ color: totalBalance >= 0 ? '#52c41a' : '#ff4d4f' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="الحسابات النشطة"
              value={accounts.filter((acc: any) => acc.is_active).length}
              valueStyle={{ color: '#52c41a' }}
              prefix={<BankOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={() => setModalVisible(true)}
              size="large"
              block
            >
              إضافة حساب جديد
            </Button>
          </Card>
        </Col>
      </Row>

      <Card
        title="قائمة الحسابات المصرفية"
        extra={
          <Space>
            <Button
              type="default"
              icon={<FileExcelOutlined />}
              onClick={handleExportExcel}
              style={{ color: '#52c41a', borderColor: '#52c41a' }}
            >
              تصدير Excel
            </Button>
            <Button
              type="default"
              icon={<PrinterOutlined />}
              onClick={handlePrint}
            >
              طباعة
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={accounts}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* نموذج إضافة/تعديل الحساب */}
      <Modal
        title={editingAccount ? 'تعديل الحساب المصرفي' : 'إضافة حساب مصرفي جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setEditingAccount(null)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="bank_name"
                label="اسم البنك"
                rules={[{ required: true, message: 'يرجى إدخال اسم البنك' }]}
              >
                <Input placeholder="مثال: بنك فلسطين" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="account_number"
                label="رقم الحساب"
                rules={[{ required: true, message: 'يرجى إدخال رقم الحساب' }]}
              >
                <Input placeholder="*********" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="account_type"
                label="نوع الحساب"
                rules={[{ required: true, message: 'يرجى اختيار نوع الحساب' }]}
              >
                <Select placeholder="اختر نوع الحساب">
                  <Option value="current">جاري</Option>
                  <Option value="savings">توفير</Option>
                  <Option value="business">أعمال</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="account_name"
                label="اسم الحساب"
                rules={[{ required: true, message: 'يرجى إدخال اسم الحساب' }]}
              >
                <Input placeholder="الحساب الجاري الرئيسي" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="balance"
                label="الرصيد الحالي"
                rules={[{ required: true, message: 'يرجى إدخال الرصيد' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  formatter={value => ('₪ ' + value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value: string | undefined) => {
                    if (!value) return 0;
                    const parsed = parseFloat(value.replace(/₪\s?|(,*)/g, ''));
                    return isNaN(parsed) ? 0 : parsed;
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="branch"
                label="الفرع"
              >
                <Input placeholder="فرع رام الله" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="contact_person"
                label="الشخص المسؤول"
              >
                <Input placeholder="أحمد محمد" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="رقم الهاتف"
              >
                <Input placeholder="**********" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="is_active"
            label="الحالة"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="نشط" unCheckedChildren="غير نشط" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingAccount ? 'تحديث' : 'إضافة'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false)
                setEditingAccount(null)
                form.resetFields()
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نموذج المعاملات المصرفية */}
      <Modal
        title={'معاملات الحساب: ' + (selectedAccount?.account_name || '')}
        open={transactionModalVisible}
        onCancel={() => {
          setTransactionModalVisible(false)
          setSelectedAccount(null)
          transactionForm.resetFields()
        }}
        footer={null}
        width={800}
      >
        <div style={{ marginBottom: '16px' }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setAddTransactionModalVisible(true)}
          >
            إضافة معاملة جديدة
          </Button>
        </div>

        <Table
          columns={transactionColumns}
          dataSource={transactions}
          rowKey="id"
          pagination={{ pageSize: 5 }}
          size="small"
        />

        <Form
          form={transactionForm}
          layout="vertical"
          onFinish={handleTransactionSubmit}
          style={{ marginTop: '16px', padding: '16px', background: '#f5f5f5', borderRadius: '8px' }}
        >
          <h4>إضافة معاملة جديدة</h4>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="transaction_type"
                label="نوع المعاملة"
                rules={[{ required: true, message: 'يرجى اختيار نوع المعاملة' }]}
              >
                <Select placeholder="اختر نوع المعاملة">
                  <Option value="deposit">إيداع</Option>
                  <Option value="withdrawal">سحب</Option>
                  <Option value="transfer">تحويل</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="amount"
                label="المبلغ"
                rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  formatter={value => ('₪ ' + value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value: string | undefined) => {
                    if (!value) return 0;
                    const parsed = parseFloat(value.replace(/₪\s?|(,*)/g, ''));
                    return isNaN(parsed) ? 0 : parsed;
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="transaction_date"
                label="تاريخ المعاملة"
                rules={[{ required: true, message: 'يرجى إدخال تاريخ المعاملة' }]}
              >
                <Input type="date" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="description"
                label="الوصف"
              >
                <Input placeholder="وصف المعاملة" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="reference_number"
                label="رقم المرجع"
              >
                <Input placeholder="رقم المرجع أو الشيك" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                إضافة المعاملة
              </Button>
              <Button onClick={() => transactionForm.resetFields()}>
                مسح
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نموذج إضافة معاملة جديدة */}
      <Modal
        title="إضافة معاملة مصرفية جديدة"
        open={addTransactionModalVisible}
        onCancel={() => {
          setAddTransactionModalVisible(false)
          transactionForm.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={transactionForm}
          layout="vertical"
          onFinish={handleTransactionSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="transaction_type"
                label="نوع المعاملة"
                rules={[{ required: true, message: 'يرجى اختيار نوع المعاملة' }]}
              >
                <Select placeholder="اختر نوع المعاملة">
                  <Select.Option value="deposit">إيداع</Select.Option>
                  <Select.Option value="withdrawal">سحب</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="amount"
                label="المبلغ"
                rules={[{ required: true, message: 'يرجى إدخال المبلغ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  formatter={value => ('₪ ' + value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value: string | undefined) => value ? parseFloat(value.replace(/₪\s?|(,*)/g, '')) || 0 : 0}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="الوصف"
            rules={[{ required: true, message: 'يرجى إدخال وصف المعاملة' }]}
          >
            <Input.TextArea rows={3} placeholder="وصف المعاملة..." />
          </Form.Item>

          <Form.Item
            name="transaction_date"
            label="تاريخ المعاملة"
            rules={[{ required: true, message: 'يرجى اختيار تاريخ المعاملة' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              placeholder="اختر التاريخ"
              format="YYYY-MM-DD"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => {
                setAddTransactionModalVisible(false)
                transactionForm.resetFields()
              }}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                حفّ المعاملة
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default BankAccountManagement
