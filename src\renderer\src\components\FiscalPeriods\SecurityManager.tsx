import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Table,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Row,
  Col,
  Typography,
  Alert,
  Tag,
  Switch,
  Divider,
  Tooltip,
  message,
  Badge,
  Timeline,
  Avatar,
  List,
  Tabs,
  Descriptions,
  Progress,
  Statistic
} from 'antd';
import {
  SafetyOutlined,
  UserOutlined,
  LockOutlined,
  UnlockOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  AuditOutlined,
  SettingOutlined,
  TeamOutlined,
  KeyOutlined,
  WarningOutlined,
  BankOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { FiscalPeriod } from '../../types/fiscalPeriod';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

interface SecurityManagerProps {
  period: FiscalPeriod;
}

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  level: 'read' | 'write' | 'admin';
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  permissions: string[];
  lastLogin: string;
  status: 'active' | 'inactive' | 'locked';
}

interface ApprovalRequest {
  id: string;
  type: string;
  description: string;
  requestedBy: string;
  requestDate: string;
  status: 'pending' | 'approved' | 'rejected';
  approvers: string[];
  currentLevel: number;
  totalLevels: number;
}

const SecurityManager: React.FC<SecurityManagerProps> = ({ period }) => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('permissions');
  const [users, setUsers] = useState<User[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [approvalRequests, setApprovalRequests] = useState<ApprovalRequest[]>([]);
  const [userModalVisible, setUserModalVisible] = useState(false);
  const [permissionModalVisible, setPermissionModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    setLoading(true);
    try {
      // محاكاة تحميل بيانات الأمان
      const mockUsers: User[] = [
        {
          id: '1',
          name: 'أحمد محمد',
          email: '<EMAIL>',
          role: 'مدير مالي',
          permissions: ['close_period', 'view_reports', 'manage_users'],
          lastLogin: '2024-01-15 10:30',
          status: 'active'
        },
        {
          id: '2',
          name: 'فاطمة علي',
          email: '<EMAIL>',
          role: 'محاسب أول',
          permissions: ['view_reports', 'create_entries'],
          lastLogin: '2024-01-15 09:15',
          status: 'active'
        },
        {
          id: '3',
          name: 'محمد سالم',
          email: '<EMAIL>',
          role: 'محاسب',
          permissions: ['view_reports'],
          lastLogin: '2024-01-14 16:45',
          status: 'inactive'
        }
      ];

      const mockPermissions: Permission[] = [
        {
          id: 'close_period',
          name: 'إقفال الفترة',
          description: 'صلاحية إقفال الفترات المحاسبية',
          category: 'fiscal_periods',
          level: 'admin'
        },
        {
          id: 'view_reports',
          name: 'عرض التقارير',
          description: 'صلاحية عرض التقارير المالية',
          category: 'reports',
          level: 'read'
        },
        {
          id: 'manage_users',
          name: 'إدارة المستخدمين',
          description: 'صلاحية إدارة المستخدمين والصلاحيات',
          category: 'security',
          level: 'admin'
        },
        {
          id: 'create_entries',
          name: 'إنشاء القيود',
          description: 'صلاحية إنشاء وتعديل القيود المحاسبية',
          category: 'accounting',
          level: 'write'
        }
      ];

      const mockApprovalRequests: ApprovalRequest[] = [
        {
          id: '1',
          type: 'period_closing',
          description: 'طلب إقفال الفترة المحاسبية ديسمبر 2023',
          requestedBy: 'أحمد محمد',
          requestDate: '2024-01-15 14:30',
          status: 'pending',
          approvers: ['مدير عام', 'مدير مالي'],
          currentLevel: 1,
          totalLevels: 2
        },
        {
          id: '2',
          type: 'user_permission',
          description: 'طلب منح صلاحية إقفال الفترة لفاطمة علي',
          requestedBy: 'أحمد محمد',
          requestDate: '2024-01-15 11:20',
          status: 'approved',
          approvers: ['مدير عام'],
          currentLevel: 1,
          totalLevels: 1
        }
      ];

      setUsers(mockUsers);
      setPermissions(mockPermissions);
      setApprovalRequests(mockApprovalRequests);
    } catch (error) {
      message.error('فشل في تحميل بيانات الأمان');
    } finally {
      setLoading(false);
    }
  };

  const handleUserSave = async (values: any) => {
    try {
      if (selectedUser) {
        // تحديث مستخدم موجود
        setUsers(prev => prev.map(user => 
          user.id === selectedUser.id ? { ...user, ...values } : user
        ));
        message.success('تم تحديث المستخدم بنجاح');
      } else {
        // إضافة مستخدم جديد
        const newUser: User = {
          id: Date.now().toString(),
          ...values,
          lastLogin: 'لم يسجل دخول بعد',
          status: 'active'
        };
        setUsers(prev => [...prev, newUser]);
        message.success('تم إضافة المستخدم بنجاح');
      }
      setUserModalVisible(false);
      setSelectedUser(null);
      form.resetFields();
    } catch (error) {
      message.error('فشل في حفظ المستخدم');
    }
  };

  const handleApprovalAction = async (requestId: string, action: 'approve' | 'reject') => {
    try {
      setApprovalRequests(prev => prev.map(request => 
        request.id === requestId 
          ? { ...request, status: action === 'approve' ? 'approved' : 'rejected' }
          : request
      ));
      message.success(`تم ${action === 'approve' ? 'الموافقة على' : 'رفض'} الطلب`);
    } catch (error) {
      message.error('فشل في معالجة الطلب');
    }
  };

  const getPermissionLevel = (level: string) => {
    const levels = {
      read: { color: 'blue', text: 'قراءة' },
      write: { color: 'orange', text: 'كتابة' },
      admin: { color: 'red', text: 'إدارة' }
    };
    return levels[level] || { color: 'default', text: level };
  };

  const getUserStatusColor = (status: string) => {
    const colors = {
      active: 'green',
      inactive: 'orange',
      locked: 'red'
    };
    return colors[status] || 'default';
  };

  const getUserStatusText = (status: string) => {
    const texts = {
      active: 'نشط',
      inactive: 'غير نشط',
      locked: 'مقفل'
    };
    return texts[status] || status;
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card>
        <div style={{ marginBottom: 20 }}>
          <Title level={3}>
            <SafetyOutlined style={{ marginRight: 8 }} />
            نظام الأمان والصلاحيات - {period.period_name}
          </Title>
          <Text type="secondary">
            إدارة متقدمة للمستخدمين والصلاحيات مع نظام موافقات متعدد المستويات
          </Text>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={
            <span>
              <UserOutlined />
              المستخدمون
              <Badge count={users.length} style={{ marginLeft: 8 }} />
            </span>
          } key="users">
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              {/* شريط الأدوات */}
              <Row justify="space-between" align="middle">
                <Col>
                  <Title level={4}>إدارة المستخدمين</Title>
                </Col>
                <Col>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setSelectedUser(null);
                      setUserModalVisible(true);
                    }}
                  >
                    إضافة مستخدم
                  </Button>
                </Col>
              </Row>

              {/* إحصائيات سريعة */}
              <Row gutter={[16, 16]}>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="إجمالي المستخدمين"
                      value={users.length}
                      prefix={<TeamOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="المستخدمون النشطون"
                      value={users.filter(u => u.status === 'active').length}
                      prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="المستخدمون المقفلون"
                      value={users.filter(u => u.status === 'locked').length}
                      prefix={<LockOutlined style={{ color: '#ff4d4f' }} />}
                      valueStyle={{ color: '#ff4d4f' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="المستخدمون غير النشطون"
                      value={users.filter(u => u.status === 'inactive').length}
                      prefix={<ExclamationCircleOutlined style={{ color: '#faad14' }} />}
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Card>
                </Col>
              </Row>

              {/* جدول المستخدمين */}
              <Card title="قائمة المستخدمين">
                <Table
                  dataSource={users}
                  rowKey="id"
                  loading={loading}
                  columns={[
                    {
                      title: 'المستخدم',
                      key: 'user',
                      render: (_, record) => (
                        <Space>
                          <Avatar icon={<UserOutlined />} />
                          <div>
                            <div>{record.name}</div>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {record.email}
                            </Text>
                          </div>
                        </Space>
                      )
                    },
                    {
                      title: 'الدور',
                      dataIndex: 'role',
                      key: 'role',
                      render: (role) => <Tag color="blue">{role}</Tag>
                    },
                    {
                      title: 'الصلاحيات',
                      dataIndex: 'permissions',
                      key: 'permissions',
                      render: (permissions) => (
                        <Space wrap>
                          {permissions.slice(0, 2).map(permission => (
                            <Tag key={permission}>
                              {permissions.find(p => p.id === permission)?.name || permission}
                            </Tag>
                          ))}
                          {permissions.length > 2 && (
                            <Tag>+{permissions.length - 2} أخرى</Tag>
                          )}
                        </Space>
                      )
                    },
                    {
                      title: 'آخر تسجيل دخول',
                      dataIndex: 'lastLogin',
                      key: 'lastLogin'
                    },
                    {
                      title: 'الحالة',
                      dataIndex: 'status',
                      key: 'status',
                      render: (status) => (
                        <Tag color={getUserStatusColor(status)}>
                          {getUserStatusText(status)}
                        </Tag>
                      )
                    },
                    {
                      title: 'الإجراءات',
                      key: 'actions',
                      render: (_, record) => (
                        <Space>
                          <Tooltip title="عرض">
                            <Button
                              type="text"
                              icon={<EyeOutlined />}
                              size="small"
                            />
                          </Tooltip>
                          <Tooltip title="تعديل">
                            <Button
                              type="text"
                              icon={<EditOutlined />}
                              size="small"
                              onClick={() => {
                                setSelectedUser(record);
                                setUserModalVisible(true);
                              }}
                            />
                          </Tooltip>
                          <Tooltip title="قفل/إلغاء قفل">
                            <Button
                              type="text"
                              icon={record.status === 'locked' ? <UnlockOutlined /> : <LockOutlined />}
                              size="small"
                              onClick={() => {
                                const newStatus = record.status === 'locked' ? 'active' : 'locked';
                                setUsers(prev => prev.map(user =>
                                  user.id === record.id ? { ...user, status: newStatus } : user
                                ));
                                message.success(`تم ${newStatus === 'locked' ? 'قفل' : 'إلغاء قفل'} المستخدم`);
                              }}
                            />
                          </Tooltip>
                        </Space>
                      )
                    }
                  ]}
                />
              </Card>
            </Space>
          </TabPane>

          <TabPane tab={
            <span>
              <KeyOutlined />
              الصلاحيات
              <Badge count={permissions.length} style={{ marginLeft: 8 }} />
            </span>
          } key="permissions">
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              {/* شريط الأدوات */}
              <Row justify="space-between" align="middle">
                <Col>
                  <Title level={4}>إدارة الصلاحيات</Title>
                </Col>
                <Col>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setPermissionModalVisible(true)}
                  >
                    إضافة صلاحية
                  </Button>
                </Col>
              </Row>

              {/* إحصائيات الصلاحيات */}
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Card>
                    <Statistic
                      title="صلاحيات القراءة"
                      value={permissions.filter(p => p.level === 'read').length}
                      prefix={<EyeOutlined style={{ color: '#1890ff' }} />}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card>
                    <Statistic
                      title="صلاحيات الكتابة"
                      value={permissions.filter(p => p.level === 'write').length}
                      prefix={<EditOutlined style={{ color: '#faad14' }} />}
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card>
                    <Statistic
                      title="صلاحيات الإدارة"
                      value={permissions.filter(p => p.level === 'admin').length}
                      prefix={<SettingOutlined style={{ color: '#ff4d4f' }} />}
                      valueStyle={{ color: '#ff4d4f' }}
                    />
                  </Card>
                </Col>
              </Row>

              {/* جدول الصلاحيات */}
              <Card title="قائمة الصلاحيات">
                <Table
                  dataSource={permissions}
                  rowKey="id"
                  loading={loading}
                  columns={[
                    {
                      title: 'اسم الصلاحية',
                      dataIndex: 'name',
                      key: 'name',
                      render: (name, record) => (
                        <Space>
                          <KeyOutlined />
                          <div>
                            <div>{name}</div>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {record.description}
                            </Text>
                          </div>
                        </Space>
                      )
                    },
                    {
                      title: 'الفئة',
                      dataIndex: 'category',
                      key: 'category',
                      render: (category) => {
                        const categoryNames = {
                          fiscal_periods: 'الفترات المحاسبية',
                          reports: 'التقارير',
                          security: 'الأمان',
                          accounting: 'المحاسبة'
                        };
                        return <Tag color="purple">{categoryNames[category] || category}</Tag>;
                      }
                    },
                    {
                      title: 'مستوى الصلاحية',
                      dataIndex: 'level',
                      key: 'level',
                      render: (level) => {
                        const levelInfo = getPermissionLevel(level);
                        return <Tag color={levelInfo.color}>{levelInfo.text}</Tag>;
                      }
                    },
                    {
                      title: 'عدد المستخدمين',
                      key: 'userCount',
                      render: (_, record) => {
                        const count = users.filter(user =>
                          user.permissions.includes(record.id)
                        ).length;
                        return <Badge count={count} showZero />;
                      }
                    },
                    {
                      title: 'الإجراءات',
                      key: 'actions',
                      render: (_, record) => (
                        <Space>
                          <Tooltip title="عرض المستخدمين">
                            <Button
                              type="text"
                              icon={<TeamOutlined />}
                              size="small"
                            />
                          </Tooltip>
                          <Tooltip title="تعديل">
                            <Button
                              type="text"
                              icon={<EditOutlined />}
                              size="small"
                            />
                          </Tooltip>
                          <Tooltip title="حذف">
                            <Button
                              type="text"
                              icon={<DeleteOutlined />}
                              size="small"
                              danger
                            />
                          </Tooltip>
                        </Space>
                      )
                    }
                  ]}
                />
              </Card>
            </Space>
          </TabPane>

          <TabPane tab={
            <span>
              <AuditOutlined />
              طلبات الموافقة
              <Badge count={approvalRequests.filter(r => r.status === 'pending').length} style={{ marginLeft: 8 }} />
            </span>
          } key="approvals">
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              {/* إحصائيات طلبات الموافقة */}
              <Row gutter={[16, 16]}>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="الطلبات المعلقة"
                      value={approvalRequests.filter(r => r.status === 'pending').length}
                      prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="الطلبات المعتمدة"
                      value={approvalRequests.filter(r => r.status === 'approved').length}
                      prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="الطلبات المرفوضة"
                      value={approvalRequests.filter(r => r.status === 'rejected').length}
                      prefix={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
                      valueStyle={{ color: '#ff4d4f' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="إجمالي الطلبات"
                      value={approvalRequests.length}
                      prefix={<AuditOutlined />}
                    />
                  </Card>
                </Col>
              </Row>

              {/* قائمة طلبات الموافقة */}
              <Card title="طلبات الموافقة">
                <List
                  dataSource={approvalRequests}
                  renderItem={(request) => (
                    <List.Item
                      actions={request.status === 'pending' ? [
                        <Button
                          key="approve"
                          type="primary"
                          size="small"
                          icon={<CheckCircleOutlined />}
                          onClick={() => handleApprovalAction(request.id, 'approve')}
                        >
                          موافقة
                        </Button>,
                        <Button
                          key="reject"
                          danger
                          size="small"
                          icon={<ExclamationCircleOutlined />}
                          onClick={() => handleApprovalAction(request.id, 'reject')}
                        >
                          رفض
                        </Button>
                      ] : []}
                    >
                      <List.Item.Meta
                        avatar={
                          <Avatar
                            icon={
                              request.type === 'period_closing' ? <BankOutlined /> :
                              request.type === 'user_permission' ? <UserOutlined /> :
                              <FileTextOutlined />
                            }
                            style={{
                              backgroundColor:
                                request.status === 'pending' ? '#faad14' :
                                request.status === 'approved' ? '#52c41a' : '#ff4d4f'
                            }}
                          />
                        }
                        title={
                          <Space>
                            <span>{request.description}</span>
                            <Tag color={
                              request.status === 'pending' ? 'orange' :
                              request.status === 'approved' ? 'green' : 'red'
                            }>
                              {request.status === 'pending' ? 'معلق' :
                               request.status === 'approved' ? 'معتمد' : 'مرفوض'}
                            </Tag>
                          </Space>
                        }
                        description={
                          <Space direction="vertical" size="small">
                            <Text type="secondary">
                              طلب من: {request.requestedBy} في {request.requestDate}
                            </Text>
                            <div>
                              <Text type="secondary">مستوى الموافقة: </Text>
                              <Progress
                                percent={(request.currentLevel / request.totalLevels) * 100}
                                size="small"
                                format={() => `${request.currentLevel}/${request.totalLevels}`}
                              />
                            </div>
                            <Space wrap>
                              {request.approvers.map((approver, index) => (
                                <Tag
                                  key={index}
                                  color={index < request.currentLevel ? 'green' : 'default'}
                                >
                                  {approver}
                                </Tag>
                              ))}
                            </Space>
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Space>
          </TabPane>

          <TabPane tab={
            <span>
              <SettingOutlined />
              إعدادات الأمان
            </span>
          } key="settings">
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <Card title="إعدادات كلمة المرور">
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Descriptions column={1} bordered>
                      <Descriptions.Item label="الحد الأدنى لطول كلمة المرور">
                        <Space>
                          <Text>8 أحرف</Text>
                          <Switch defaultChecked />
                        </Space>
                      </Descriptions.Item>
                      <Descriptions.Item label="يجب أن تحتوي على أرقام">
                        <Switch defaultChecked />
                      </Descriptions.Item>
                      <Descriptions.Item label="يجب أن تحتوي على رموز خاصة">
                        <Switch defaultChecked />
                      </Descriptions.Item>
                      <Descriptions.Item label="يجب أن تحتوي على أحرف كبيرة وصغيرة">
                        <Switch defaultChecked />
                      </Descriptions.Item>
                    </Descriptions>
                  </Col>
                  <Col span={12}>
                    <Descriptions column={1} bordered>
                      <Descriptions.Item label="انتهاء صلاحية كلمة المرور">
                        <Space>
                          <Text>90 يوم</Text>
                          <Switch defaultChecked />
                        </Space>
                      </Descriptions.Item>
                      <Descriptions.Item label="عدد محاولات تسجيل الدخول الخاطئة">
                        <Text>5 محاولات</Text>
                      </Descriptions.Item>
                      <Descriptions.Item label="مدة قفل الحساب">
                        <Text>30 دقيقة</Text>
                      </Descriptions.Item>
                      <Descriptions.Item label="تسجيل الخروج التلقائي">
                        <Space>
                          <Text>60 دقيقة</Text>
                          <Switch defaultChecked />
                        </Space>
                      </Descriptions.Item>
                    </Descriptions>
                  </Col>
                </Row>
              </Card>

              <Card title="إعدادات الموافقات">
                <Descriptions column={2} bordered>
                  <Descriptions.Item label="موافقة إقفال الفترة">
                    <Tag color="red">مطلوبة - مستويين</Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="موافقة تعديل الصلاحيات">
                    <Tag color="orange">مطلوبة - مستوى واحد</Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="موافقة إضافة مستخدم">
                    <Tag color="blue">غير مطلوبة</Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="موافقة حذف البيانات">
                    <Tag color="red">مطلوبة - ثلاثة مستويات</Tag>
                  </Descriptions.Item>
                </Descriptions>
              </Card>

              <Card title="سجل الأمان">
                <Timeline>
                  <Timeline.Item
                    dot={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                    color="green"
                  >
                    <div>
                      <Text strong>تسجيل دخول ناجح</Text>
                      <br />
                      <Text type="secondary">أحمد محمد - 15 يناير 2024 10:30</Text>
                    </div>
                  </Timeline.Item>
                  <Timeline.Item
                    dot={<WarningOutlined style={{ color: '#faad14' }} />}
                    color="orange"
                  >
                    <div>
                      <Text strong>محاولة تسجيل دخول فاشلة</Text>
                      <br />
                      <Text type="secondary">محمد سالم - 15 يناير 2024 09:45</Text>
                    </div>
                  </Timeline.Item>
                  <Timeline.Item
                    dot={<LockOutlined style={{ color: '#ff4d4f' }} />}
                    color="red"
                  >
                    <div>
                      <Text strong>قفل حساب مستخدم</Text>
                      <br />
                      <Text type="secondary">محمد سالم - 15 يناير 2024 09:50</Text>
                    </div>
                  </Timeline.Item>
                </Timeline>
              </Card>
            </Space>
          </TabPane>
        </Tabs>

        {/* مودال إضافة/تعديل مستخدم */}
        <Modal
          title={selectedUser ? 'تعديل مستخدم' : 'إضافة مستخدم جديد'}
          open={userModalVisible}
          onCancel={() => {
            setUserModalVisible(false);
            setSelectedUser(null);
            form.resetFields();
          }}
          footer={null}
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleUserSave}
            initialValues={selectedUser || {}}
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Form.Item
                  label="اسم المستخدم"
                  name="name"
                  rules={[{ required: true, message: 'يرجى إدخال اسم المستخدم' }]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="البريد الإلكتروني"
                  name="email"
                  rules={[
                    { required: true, message: 'يرجى إدخال البريد الإلكتروني' },
                    { type: 'email', message: 'يرجى إدخال بريد إلكتروني صحيح' }
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              label="الدور"
              name="role"
              rules={[{ required: true, message: 'يرجى اختيار الدور' }]}
            >
              <Select>
                <Option value="مدير مالي">مدير مالي</Option>
                <Option value="محاسب أول">محاسب أول</Option>
                <Option value="محاسب">محاسب</Option>
                <Option value="مراجع">مراجع</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="الصلاحيات"
              name="permissions"
              rules={[{ required: true, message: 'يرجى اختيار الصلاحيات' }]}
            >
              <Select mode="multiple" placeholder="اختر الصلاحيات">
                {permissions.map(permission => (
                  <Option key={permission.id} value={permission.id}>
                    {permission.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  {selectedUser ? 'تحديث' : 'إضافة'}
                </Button>
                <Button onClick={() => {
                  setUserModalVisible(false);
                  setSelectedUser(null);
                  form.resetFields();
                }}>
                  إلغاء
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    </div>
  );
};

export default SecurityManager;
