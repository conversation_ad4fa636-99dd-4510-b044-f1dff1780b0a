const fs = require('fs');
const path = require('path');
const initSqlJs = require('sql.js');

async function inspectDatabase() {
  try {
    console.log('🔍 بدء فحص قاعدة البيانات...\n');
    
    // مسار قاعدة البيانات
    const dbPath = path.join(process.env.APPDATA, 'ZET.IA', 'database.db');
    console.log(`📍 مسار قاعدة البيانات: ${dbPath}`);
    
    // التحقق من وجود الملف
    if (!fs.existsSync(dbPath)) {
      console.log('❌ ملف قاعدة البيانات غير موجود!');
      return;
    }
    
    // قراءة حجم الملف
    const stats = fs.statSync(dbPath);
    console.log(`📊 حجم قاعدة البيانات: ${(stats.size / 1024).toFixed(2)} KB`);
    
    // تهيئة SQL.js
    const SQL = await initSqlJs({
      locateFile: file => path.join(__dirname, 'node_modules', 'sql.js', 'dist', file)
    });
    
    // قراءة قاعدة البيانات
    const filebuffer = fs.readFileSync(dbPath);
    const db = new SQL.Database(filebuffer);
    
    console.log('\n✅ تم تحميل قاعدة البيانات بنجاح\n');
    
    // فحص الجداول الموجودة
    console.log('📋 الجداول الموجودة:');
    const tables = db.exec("SELECT name FROM sqlite_master WHERE type='table'");
    
    if (tables.length === 0 || !tables[0].values) {
      console.log('❌ لا توجد جداول في قاعدة البيانات!');
      return;
    }
    
    const tableNames = tables[0].values.map(row => row[0]);
    console.log(`   العدد الإجمالي: ${tableNames.length}`);
    tableNames.forEach((table, index) => {
      console.log(`   ${index + 1}. ${table}`);
    });
    
    console.log('\n📊 إحصائيات الجداول:');
    
    // فحص كل جدول
    for (const tableName of tableNames) {
      try {
        const countResult = db.exec(`SELECT COUNT(*) as count FROM ${tableName}`);
        const count = countResult[0]?.values[0]?.[0] || 0;
        console.log(`   ${tableName}: ${count} سجل`);
        
        // فحص خاص للجداول المهمة
        if (tableName === 'users' && count > 0) {
          console.log('     👥 المستخدمون:');
          const users = db.exec("SELECT username, full_name, role, is_active FROM users");
          if (users[0]?.values) {
            users[0].values.forEach(user => {
              console.log(`       - ${user[1]} (${user[0]}) - ${user[2]} - ${user[3] ? 'نشط' : 'غير نشط'}`);
            });
          }
        }
        
        if (tableName === 'settings' && count > 0) {
          console.log('     ⚙️ الإعدادات:');
          const settings = db.exec("SELECT key, value FROM settings LIMIT 10");
          if (settings[0]?.values) {
            settings[0].values.forEach(setting => {
              console.log(`       - ${setting[0]}: ${setting[1]}`);
            });
          }
        }
        
      } catch (error) {
        console.log(`   ${tableName}: خطأ في القراءة - ${error.message}`);
      }
    }
    
    // فحص المزامنة
    console.log('\n🔄 حالة المزامنة:');
    try {
      const syncSettings = db.exec("SELECT * FROM settings WHERE key LIKE '%sync%'");
      if (syncSettings[0]?.values && syncSettings[0].values.length > 0) {
        syncSettings[0].values.forEach(setting => {
          console.log(`   ${setting[1]}: ${setting[2]}`);
        });
      } else {
        console.log('   ❌ لا توجد إعدادات مزامنة');
      }
    } catch (error) {
      console.log(`   ❌ خطأ في فحص المزامنة: ${error.message}`);
    }
    
    // فحص آخر نشاط
    console.log('\n📅 آخر نشاط:');
    try {
      const lastActivity = db.exec(`
        SELECT 'users' as table_name, MAX(updated_at) as last_update FROM users
        UNION ALL
        SELECT 'settings' as table_name, MAX(updated_at) as last_update FROM settings
        ORDER BY last_update DESC
        LIMIT 5
      `);
      
      if (lastActivity[0]?.values) {
        lastActivity[0].values.forEach(activity => {
          console.log(`   ${activity[0]}: ${activity[1] || 'غير محدد'}`);
        });
      }
    } catch (error) {
      console.log(`   ❌ خطأ في فحص آخر نشاط: ${error.message}`);
    }
    
    db.close();
    console.log('\n✅ تم إنهاء فحص قاعدة البيانات');
    
  } catch (error) {
    console.error('❌ خطأ في فحص قاعدة البيانات:', error);
  }
}

// تشغيل الفحص
inspectDatabase();
