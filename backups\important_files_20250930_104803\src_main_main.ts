import { app, BrowserWindow, ipcMain } from 'electron'
import * as path from 'path'
import { Logger } from './utils/logger'

// فحص بيئة التطوير
const isDevelopment = process.env.NODE_ENV === 'development'

// استيراد الخدمات الجديدة
import {
  DatabaseService,
  AuthService,
  UserService,
  InventoryService,
  SalesService,
  PurchaseService,
  CustomerService,
  SupplierService,
  EmployeeService,
  FinancialService,
  ProductionService,
  NotificationService,
  CodeGeneratorService,
  UniversalInvoiceService
} from './services'
import { registerAllHandlers, setAllServices } from './handlers'
import { registerEmergencyHandlers, setEmergencyFinancialService } from './handlers/emergencyHandlers'
import { registerSyncHandlers, setSyncDatabaseService, initializeSyncService } from './handlers/syncHandlers'
import { registerImageHandlers } from './handlers/imageHandlers'
import { SimpleDatabaseService } from './services/SimpleDatabaseService'
import { registerActivationHandlers } from './ipc/activationHandlers'
import { registerReportsHandlers } from './ipc/reportsHandlers'




// متغيرات عامة
// متتبع intervals النشطة
let activeIntervals: NodeJS.Timeout[] = []
let activeTimeouts: NodeJS.Timeout[] = []

// متتبع مراقب الذاكرة
let memoryMonitorInterval: NodeJS.Timeout | null = null

// ===== دوال مساعدة لإدارة الموارد =====

/**
 * إنشاء interval مع تتبع تلقائي
 */
function createInterval(callback: () => void, delay: number): NodeJS.Timeout {
  const interval = setInterval(callback, delay)
  activeIntervals.push(interval)
  Logger.info('MemoryManager', `📅 تم إنشاء interval جديد (${activeIntervals.length} نشط)`)
  return interval
}

/**
 * إنشاء timeout مع تتبع تلقائي
 */
function _createTimeout(callback: () => void, delay: number): NodeJS.Timeout {
  const timeout = setTimeout(() => {
    // إزالة timeout من المتتبع عند انتهائه
    const index = activeTimeouts.indexOf(timeout)
    if (index > -1) {
      activeTimeouts.splice(index, 1)
    }
    callback()
  }, delay)
  activeTimeouts.push(timeout)
  Logger.info('MemoryManager', `⏰ تم إنشاء timeout جديد (${activeTimeouts.length} نشط)`)
  return timeout
}

/**
 * إزالة interval من المتتبع
 */
function _removeInterval(interval: NodeJS.Timeout): void {
  clearInterval(interval)
  const index = activeIntervals.indexOf(interval)
  if (index > -1) {
    activeIntervals.splice(index, 1)
    Logger.info('MemoryManager', `📅 تم إزالة interval (${activeIntervals.length} متبقي)`)
  }
}

/**
 * إزالة timeout من المتتبع
 */
function _removeTimeout(timeout: NodeJS.Timeout): void {
  clearTimeout(timeout)
  const index = activeTimeouts.indexOf(timeout)
  if (index > -1) {
    activeTimeouts.splice(index, 1)
    Logger.info('MemoryManager', `⏰ تم إزالة timeout (${activeTimeouts.length} متبقي)`)
  }
}

// ===== مراقبة الأداء =====

/**
 * مراقبة استهلاك الذاكرة
 */
function startMemoryMonitoring(): void {
  if (!isDevelopment || memoryMonitorInterval) return

  memoryMonitorInterval = createInterval(() => {
    const memUsage = process.memoryUsage()
    const formatBytes = (bytes: number) => {
      const mb = bytes / 1024 / 1024
      return `${mb.toFixed(2)} MB`
    }

    Logger.debug('MemoryMonitor', '📊 استهلاك الذاكرة', {
      rss: formatBytes(memUsage.rss),
      heapUsed: formatBytes(memUsage.heapUsed),
      heapTotal: formatBytes(memUsage.heapTotal),
      external: formatBytes(memUsage.external),
      activeIntervals: activeIntervals.length,
      activeTimeouts: activeTimeouts.length
    })

    // تحذير إذا تجاوز الاستهلاك 400 MB (تم تقليل الحد)
    if (memUsage.heapUsed > 400 * 1024 * 1024) {
      Logger.warn('MemoryMonitor', '⚠️ استهلاك الذاكرة مرتفع', formatBytes(memUsage.heapUsed))

      // تشغيل تنظيف الذاكرة
      performMemoryCleanup()
    }

    // تنظيف دوري كل 10 دقائق
    if (Date.now() % (10 * 60 * 1000) < 30000) {
      performPeriodicCleanup()
    }
  }, 30000) // كل 30 ثانية

  Logger.info('MemoryMonitor', '📊 تم بدء مراقبة الذاكرة المحسنة')
}

/**
 * تنظيف الذاكرة الفوري
 */
function performMemoryCleanup(): void {
  try {
    // تشغيل garbage collection إذا كان متاحاً
    if (global.gc) {
      global.gc()
      Logger.info('MemoryManager', '🧹 تم تشغيل garbage collection')
    }

    // تنظيف cache قاعدة البيانات
    if (databaseService) {
      databaseService.saveDatabase()
    }

    Logger.info('MemoryManager', '🧹 تم تنظيف الذاكرة')
  } catch (error) {
    Logger.error('MemoryManager', 'خطأ في تنظيف الذاكرة:', error)
  }
}

/**
 * تنظيف دوري شامل
 */
function performPeriodicCleanup(): void {
  try {
    // تنظيف intervals المنتهية
    activeIntervals = activeIntervals.filter(interval => interval !== null)
    activeTimeouts = activeTimeouts.filter(timeout => timeout !== null)

    // تشغيل garbage collection
    if (global.gc) {
      global.gc()
    }

    Logger.info('MemoryManager', '🔄 تم تنفيذ التنظيف الدوري')
  } catch (error) {
    Logger.error('MemoryManager', 'خطأ في التنظيف الدوري:', error)
  }
}

/**
 * مراقبة أداء العمليات
 */
function _measurePerformance<T>(operation: () => T, operationName: string): T {
  const startTime = Date.now()
  const result = operation()
  Logger.performance('Performance', `${operationName}`, startTime)
  return result
}

/**
 * مراقبة أداء العمليات غير المتزامنة
 */
async function measureAsyncPerformance<T>(
  operation: () => Promise<T>,
  operationName: string
): Promise<T> {
  const startTime = Date.now()
  try {
    const result = await operation()
    Logger.performance('Performance', `${operationName}`, startTime)
    return result
  } catch (error) {
    Logger.error('Performance', `خطأ في ${operationName}`, error)
    throw error
  }
}

let mainWindow: BrowserWindow | null = null
let databaseService: DatabaseService
let authService: AuthService
let userService: UserService
let inventoryService: InventoryService
let salesService: SalesService
let purchaseService: PurchaseService
let customerService: CustomerService
let supplierService: SupplierService
let employeeService: EmployeeService
let financialService: FinancialService
let productionService: ProductionService
let notificationService: NotificationService
let codeGeneratorService: CodeGeneratorService
let universalInvoiceService: UniversalInvoiceService

// ===== دوال التهيئة =====

/**
 * تهيئة الخدمات
 */

// دالة تنظيف الموارد المحسنة
function cleanupResources(): void {
  Logger.info('ResourceManager', '🧹 بدء تنظيف الموارد...')

  const startTime = Date.now()

  // تنظيف intervals
  if (activeIntervals.length > 0) {
    Logger.debug('ResourceManager', `📅 تنظيف ${activeIntervals.length} intervals`)
    activeIntervals.forEach(interval => {
      clearInterval(interval)
    })
    activeIntervals = []
  }

  // تنظيف timeouts
  if (activeTimeouts.length > 0) {
    Logger.debug('ResourceManager', `⏰ تنظيف ${activeTimeouts.length} timeouts`)
    activeTimeouts.forEach(timeout => {
      clearTimeout(timeout)
    })
    activeTimeouts = []
  }

  // تنظيف مراقب الذاكرة
  if (memoryMonitorInterval) {
    Logger.debug('ResourceManager', '📊 إيقاف مراقب الذاكرة')
    clearInterval(memoryMonitorInterval)
    memoryMonitorInterval = null
  }

  // تنظيف معالجات IPC
  const ipcHandlers = [
    'login', 'logout', 'verify-session', 'get-current-user',
    'get-users', 'create-user', 'update-user', 'delete-user',
    'get-items', 'create-item', 'get-inventory', 'create-inventory-movement'
    // يمكن إضافة المزيد حسب الحاجة
  ]

  ipcHandlers.forEach(handler => {
    try {
      ipcMain.removeHandler(handler)
    } catch {
      // تجاهل الأخطاء إذا كان المعالج غير موجود
    }
  })

  // إغلاق قاعدة البيانات
  if (databaseService) {
    try {
      databaseService.close()
    } catch (error) {
      Logger.error('DatabaseService', 'خطأ في إغلاق قاعدة البيانات', error)
    }
  }

  Logger.performance('ResourceManager', '🧹 تم تنظيف جميع الموارد', startTime)
}

async function initializeServices(): Promise<void> {
  try {
    Logger.info('ServiceManager', ' تهيئة الخدمات...')

    // تهيئة خدمة قاعدة البيانات المحسنة مع قياس الأداء
    Logger.info('ServiceManager', '🔄 استخدام قاعدة البيانات المحسنة (sql.js)...')
    const simpleDatabaseService = SimpleDatabaseService.getInstance()
    await measureAsyncPerformance(
      () => simpleDatabaseService.initialize(),
      'تهيئة قاعدة البيانات المحسنة (sql.js)'
    )

    // استخدام الخدمة المحسنة كخدمة قاعدة البيانات الرئيسية
    databaseService = simpleDatabaseService as any

    // تهيئة باقي الخدمات
    authService = AuthService.getInstance()
    userService = UserService.getInstance()
    inventoryService = InventoryService.getInstance()
    salesService = SalesService.getInstance()
    purchaseService = PurchaseService.getInstance()
    customerService = CustomerService.getInstance()
    supplierService = SupplierService.getInstance()
    employeeService = EmployeeService.getInstance()
    financialService = FinancialService.getInstance()
    productionService = ProductionService.getInstance()
    notificationService = new NotificationService(databaseService)
    codeGeneratorService = new CodeGeneratorService(databaseService.getDatabase())

    // تهيئة خدمة الفواتير الموحدة
    universalInvoiceService = UniversalInvoiceService.getInstance()

    // إنشاء جداول المخزون
    await inventoryService.createInventoryTables()

    // إنشاء جداول الخدمات الجديدة
    await customerService.createCustomerTables()
    await supplierService.createSupplierTables()
    await salesService.createSalesTables()
    await purchaseService.createPurchaseTables()
    await employeeService.createEmployeeTables()
    await financialService.createFinancialTables()
    await productionService.createProductionTables()
    await notificationService.createNotificationTables()

    // التأكد من إنشاء جدول الصور الموحد
    Logger.info('ServiceManager', '🖼️ التأكد من إنشاء جدول الصور الموحد...')
    try {
      const db = databaseService.getDatabase()
      if (db) {
        const createImageTableQuery = `
          CREATE TABLE IF NOT EXISTS unified_images (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            original_name TEXT NOT NULL,
            path TEXT NOT NULL,
            thumbnail_path TEXT,
            size INTEGER NOT NULL,
            width INTEGER,
            height INTEGER,
            type TEXT NOT NULL,
            category TEXT NOT NULL,
            context_type TEXT NOT NULL,
            context_id INTEGER NOT NULL,
            description TEXT DEFAULT '',
            tags TEXT DEFAULT '[]',
            is_active INTEGER DEFAULT 1,
            is_primary INTEGER DEFAULT 0,
            sort_order INTEGER DEFAULT 0,
            uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            uploaded_by INTEGER,
            metadata TEXT DEFAULT '{}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            deleted_at DATETIME
          )
        `
        // استخدام SimpleDatabaseService للتنفيذ
        const simpleDatabaseService = SimpleDatabaseService.getInstance()
        const dbWrapper = simpleDatabaseService.getDatabase()
        dbWrapper.exec(createImageTableQuery)
        Logger.info('ServiceManager', '✅ تم التأكد من إنشاء جدول الصور الموحد')
      }
    } catch (error) {
      Logger.warn('ServiceManager', '⚠️ تحذير في إنشاء جدول الصور:', error)
    }

    Logger.success('ServiceManager', ' تم تهيئة جميع الخدمات بنجاح')
  } catch (error) {
    Logger.error('ServiceManager', ' خطأ في تهيئة الخدمات', error)
    throw error
  }
}

/**
 * إنشاء النافذة الرئيسية
 */
function createMainWindow(): void {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true // تفعيل وضع المطور للتشخيص
    },
    icon: path.join(__dirname, '../../assets/icon.png'),
    show: false,
    titleBarStyle: 'default',
    autoHideMenuBar: true
  })

  // تحميل التطبيق
  if (process.env.NODE_ENV === 'development') {
    // في وضع التطوير، اتصل بخادم Vite
    const devServerUrl = 'http://localhost:3000'
    Logger.info('MainWindow', `تحميل من خادم التطوير: ${devServerUrl}`)
    mainWindow.loadURL(devServerUrl)
  } else {
    // في وضع الإنتاج، حمل الملفات المحلية
    // المسار الصحيح: من dist/main/main/ إلى dist/renderer/
    // __dirname = dist/main/main/ لذلك نحتاج ../../../ للوصول إلى dist/renderer/
    // تم إصلاح هذا المسار نهائياً - لا تغيره!
    const indexPath = path.resolve(__dirname, '../../renderer/index.html')
    console.log('Loading file from:', indexPath)
    Logger.info('MainWindow', `محاولة تحميل الملف من: ${indexPath}`)

    // التحقق من وجود الملف
    const fs = require('fs')
    if (fs.existsSync(indexPath)) {
      Logger.info('MainWindow', 'ملف index.html موجود')
      mainWindow.loadFile(indexPath)
    } else {
      Logger.error('MainWindow', `ملف index.html غير موجود في: ${indexPath}`)
      // محاولة مسارات بديلة
      const alternativePaths = [
        path.resolve(__dirname, '../../renderer/index.html'),
        path.resolve(__dirname, '../renderer/index.html'),
        path.resolve(process.cwd(), 'dist/renderer/index.html')
      ]

      let found = false
      for (const altPath of alternativePaths) {
        Logger.info('MainWindow', `محاولة المسار البديل: ${altPath}`)
        if (fs.existsSync(altPath)) {
          Logger.info('MainWindow', 'تم العثور على الملف في المسار البديل')
          mainWindow.loadFile(altPath)
          found = true
          break
        }
      }

      if (!found) {
        Logger.error('MainWindow', 'لم يتم العثور على ملف index.html في أي مسار')
        Logger.error('MainWindow', `المجلد الحالي: ${__dirname}`)
        Logger.error('MainWindow', `مجلد العمل: ${process.cwd()}`)
      }
    }
  }

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show()

    // فتح أدوات المطور تلقائياً في وضع التطوير
    if (process.env.NODE_ENV === 'development') {
      mainWindow?.webContents.openDevTools()
    }
  })

  // إضافة اختصار F12 لفتح أدوات المطور
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (input.key === 'F12') {
      if (mainWindow?.webContents.isDevToolsOpened()) {
        mainWindow.webContents.closeDevTools()
      } else {
        mainWindow?.webContents.openDevTools()
      }
    }
  })

  // تنظيف المرجع عند إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null
  })
}

// ===== أحداث التطبيق =====

app.whenReady().then(async () => {
  try {
    Logger.info('Application', '🚀 بدء تشغيل التطبيق...')

    // تهيئة الخدمات
    await initializeServices()

    // إنشاء النافذة الرئيسية
    createMainWindow()

    // تسجيل معالجات IPC
    // تعيين جميع الخدمات للمعالجات
    setAllServices({
      databaseService,
      authService,
      userService,
      inventoryService,
      salesService,
      customerService,
      purchaseService,
      supplierService,
      productionService,
      financialService,
      employeeService,
      notificationService,
      codeGeneratorService,
      universalInvoiceService
    })

    // تسجيل جميع المعالجات
    registerAllHandlers()

    // تسجيل المعالجات الطارئة
    setEmergencyFinancialService(financialService)
    registerEmergencyHandlers()

    // تسجيل معالجات المزامنة (استخدام الخدمة القديمة للتوافق)
    setSyncDatabaseService(databaseService)
    registerSyncHandlers()

    // تسجيل معالجات التفعيل
    registerActivationHandlers()

    // تسجيل معالجات التقارير المتقدمة
    registerReportsHandlers()

    // تسجيل معالجات نظام الصور المحسن
    registerImageHandlers()

    // تشخيص المعالجات المسجلة
    setTimeout(() => {
      const requiredHandlers = [
        'create-customer-payment',
        'get-bank-accounts',
        'create-receipt-voucher',
        'link-invoice-to-payment'
      ]

      Logger.info('HandlerDiagnostic', '🔍 فحص المعالجات المسجلة:')
      requiredHandlers.forEach(handler => {
        const count = ipcMain.listenerCount(handler)
        Logger.info('HandlerDiagnostic', `${handler}: ${count > 0 ? '✅ مسجل' : '❌ غير مسجل'} (${count} معالج)`)
      })
    }, 2000)

    // تم إزالة المعالج المكرر - المعالج الأساسي موجود في financialHandlers.ts

    // معالجات معلومات النظام
    ipcMain.handle('get-platform', () => {
      return process.platform
    })

    ipcMain.handle('get-electron-version', () => {
      return process.versions.electron
    })

    ipcMain.handle('get-resource-path', (event, relativePath: string) => {
      return path.join(process.resourcesPath, relativePath)
    })

    // معالجات فتح نوافذ الإدارة
    ipcMain.handle('open-items-management', async () => {
      try {
        // إرسال رسالة للنافذة الرئيسية للانتقال إلى صفحة إدارة الأصناف
        if (mainWindow) {
          mainWindow.webContents.send('navigate-to', '/items')
          mainWindow.focus()
        }
        return { success: true }
      } catch (error) {
        console.error('خطأ في فتح إدارة الأصناف:', error)
        return { success: false, error: 'فشل في فتح إدارة الأصناف' }
      }
    })

    ipcMain.handle('open-materials-management', async () => {
      try {
        // إرسال رسالة للنافذة الرئيسية للانتقال إلى صفحة إدارة المواد
        if (mainWindow) {
          mainWindow.webContents.send('navigate-to', '/materials')
          mainWindow.focus()
        }
        return { success: true }
      } catch (error) {
        console.error('خطأ في فتح إدارة المواد:', error)
        return { success: false, error: 'فشل في فتح إدارة المواد' }
      }
    })

    ipcMain.handle('open-recipes-management', async () => {
      try {
        // إرسال رسالة للنافذة الرئيسية للانتقال إلى صفحة وصفات الإنتاج
        if (mainWindow) {
          mainWindow.webContents.send('navigate-to', '/recipes')
          mainWindow.focus()
        }
        return { success: true }
      } catch (error) {
        console.error('خطأ في فتح وصفات الإنتاج:', error)
        return { success: false, message: 'خطأ في فتح وصفات الإنتاج' }
      }
    })

    // معالج حفظ ملفات Excel
    ipcMain.handle('save-excel-file', async (event, buffer: ArrayBuffer, fileName: string) => {
      try {
        const { dialog } = await import('electron')
        const fs = await import('fs')

        const result = await dialog.showSaveDialog({
          title: 'حفظ ملف Excel',
          defaultPath: fileName,
          filters: [
            { name: 'Excel Files', extensions: ['xlsx'] },
            { name: 'All Files', extensions: ['*'] }
          ]
        })

        if ((result as any).canceled || !(result as any).filePath) {
          return {
            success: false,
            message: 'تم إلغاء عملية الحفظ'
          }
        }

        // كتابة الملف
        fs.writeFileSync((result as any).filePath, Buffer.from(buffer))

        return {
          success: true,
          message: 'تم حفظ الملف بنجاح',
          filePath: (result as any).filePath
        }
      } catch (error) {
        Logger.error('Main', 'خطأ في حفظ ملف Excel:', error)
        return {
          success: false,
          message: 'حدث خطأ أثناء حفظ الملف'
        }
      }
    })







    // معالج حالة قاعدة البيانات موجود في syncHandlers.ts

    // بدء مراقبة الذاكرة في بيئة التطوير
    startMemoryMonitoring()

    // تهيئة خدمة المزامنة بعد تسجيل المعالجات
    await initializeSyncService()

    Logger.success('Application', ' تم تشغيل التطبيق بنجاح')
  } catch (error) {
    Logger.error('Application', ' خطأ في تشغيل التطبيق', error)

    // تنظيف الموارد قبل الإغلاق
    try {
      cleanupResources()
    } catch (cleanupError) {
      Logger.error('Application', ' خطأ في تنظيف الموارد', cleanupError)
    }

    // إغلاق التطبيق مع رمز خطأ
    app.exit(1)
  }
}).catch((error) => {
  Logger.error('Application', ' خطأ في تهيئة التطبيق', error)

  // تنظيف الموارد قبل الإغلاق
  try {
    cleanupResources()
  } catch (cleanupError) {
    Logger.error('Application', ' خطأ في تنظيف الموارد', cleanupError)
  }

  app.exit(1)
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // تنظيف الموارد قبل الإغلاق
    cleanupResources()
    app.quit()
  }
})


// تنظيف الموارد قبل إغلاق التطبيق
app.on('before-quit', () => {
  cleanupResources()
})

app.on('will-quit', () => {
  cleanupResources()
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow()
  }
})

// ===== معالجات IPC =====
// تم نقل جميع المعالجات إلى ملفات منفصلة في مجلد handlers





















// ===== التحسينات المطبقة =====
/*
✅ إصلاح مشكلة DevTools في الإنتاج
✅ إضافة دوال مساعدة لإدارة Intervals و Timeouts
✅ تحسين معالجة الأخطاء مع تنظيف الموارد
✅ تحسين نظام التسجيل مع timestamps ومستويات متعددة
✅ إضافة مراقبة استهلاك الذاكرة والأداء
✅ تحسين دالة تنظيف الموارد

📝 ملاحظات للاستخدام:
- استخدم createInterval() بدلاً من setInterval() لتتبع تلقائي
- استخدم createTimeout() بدلاً من setTimeout() لتتبع تلقائي
- استخدم measurePerformance() لقياس أداء العمليات المتزامنة
- استخدم measureAsyncPerformance() لقياس أداء العمليات غير المتزامنة
- مراقبة الذاكرة تعمل فقط في بيئة التطوير
*/

// ===== نهاية الملف =====
