/**
 * تقرير جودة الإنتاج
 * تقرير شامل لجودة الإنتاج ومعدلات العيوب والمراجعات
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const ProductionQualityReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'production_quality' as ReportType}
      title="تقرير جودة الإنتاج"
      description="تقرير مفصل لجودة الإنتاج مع تحليل معدلات العيوب والمراجعات والتحسينات"
      showDateRange={true}
      showDepartmentFilter={true}
      showStatusFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="production_quality_report"
      defaultFilters={{
        sortBy: 'quality_score',
        sortOrder: 'desc'
      }}
    />
  )
}

export default ProductionQualityReport
