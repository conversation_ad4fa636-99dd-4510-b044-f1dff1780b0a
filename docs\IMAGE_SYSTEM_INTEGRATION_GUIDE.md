# 🔗 دليل التكامل مع نظام إدارة الصور المحسن

## 📋 نظرة عامة

هذا الدليل يوضح كيفية دمج نظام إدارة الصور المحسن مع النظام الحالي للمحاسبة، مع الحفاظ على التوافق مع المكونات الموجودة.

## 🚀 خطوات التكامل

### 1. تهيئة النظام

```typescript
// في App.tsx أو المكون الرئيسي
import { initializeImageServices } from './services/images'

useEffect(() => {
  const initApp = async () => {
    try {
      // تهيئة نظام الصور
      await initializeImageServices()
      console.log('تم تهيئة نظام الصور بنجاح')
    } catch (error) {
      console.error('خطأ في تهيئة نظام الصور:', error)
    }
  }
  
  initApp()
}, [])
```

### 2. إضافة معالجات IPC في Main Process

```typescript
// في main.ts
import { registerImageHandlers } from './handlers/imageHandlers'

// بعد تهيئة الخدمات
registerImageHandlers()
```

### 3. إنشاء جدول قاعدة البيانات

```typescript
// تشغيل هذا الكود مرة واحدة لإنشاء الجدول
await window.electronAPI.invoke('create-unified-images-table')
await window.electronAPI.invoke('create-image-indexes')
```

## 🔄 ترحيل البيانات الموجودة

### من localStorage إلى قاعدة البيانات

```typescript
import { imageService } from './services/images'

const migrateFromLocalStorage = async () => {
  try {
    // جلب البيانات من localStorage
    const existingImages = JSON.parse(localStorage.getItem('item_images') || '{}')
    
    for (const [itemId, images] of Object.entries(existingImages)) {
      for (const image of images as any[]) {
        // تحويل إلى التنسيق الجديد
        const imageData = {
          id: image.id || generateId(),
          name: image.name,
          originalName: image.originalName || image.name,
          path: image.path,
          size: image.size || 0,
          type: image.type || 'image/jpeg',
          category: 'inventory' as const,
          contextType: 'item' as const,
          contextId: parseInt(itemId),
          description: image.description,
          tags: image.tags || [],
          isActive: true,
          isPrimary: image.isPrimary || false,
          sortOrder: image.sortOrder || 0,
          uploadedAt: new Date(image.uploadedAt || Date.now()),
          metadata: image.metadata || {}
        }
        
        // حفظ في النظام الجديد
        await imageService.saveImageData(imageData)
      }
    }
    
    console.log('تم ترحيل البيانات بنجاح')
  } catch (error) {
    console.error('خطأ في ترحيل البيانات:', error)
  }
}
```

### من الجداول القديمة

```typescript
const migrateFromOldTables = async () => {
  try {
    // ترحيل من جدول item_images
    const itemImagesQuery = `
      SELECT * FROM item_images WHERE is_active = 1
    `
    const itemImages = await window.electronAPI.invoke('database-query', itemImagesQuery)
    
    if (itemImages.success) {
      for (const oldImage of itemImages.data) {
        const newImageData = {
          id: generateId(),
          name: oldImage.image_name,
          originalName: oldImage.image_name,
          path: oldImage.image_path,
          size: oldImage.image_size || 0,
          width: oldImage.image_width,
          height: oldImage.image_height,
          type: oldImage.image_type || 'image/jpeg',
          category: 'inventory' as const,
          contextType: 'item' as const,
          contextId: oldImage.item_id,
          description: oldImage.description,
          tags: [],
          isActive: true,
          isPrimary: oldImage.is_primary === 1,
          sortOrder: oldImage.sort_order || 0,
          uploadedAt: new Date(oldImage.created_at),
          uploadedBy: oldImage.created_by,
          metadata: {}
        }
        
        await imageService.saveImageData(newImageData)
      }
    }
    
    // ترحيل من جدول check_images
    const checkImagesQuery = `
      SELECT * FROM check_images
    `
    const checkImages = await window.electronAPI.invoke('database-query', checkImagesQuery)
    
    if (checkImages.success) {
      for (const oldImage of checkImages.data) {
        const newImageData = {
          id: generateId(),
          name: oldImage.image_name,
          originalName: oldImage.image_name,
          path: oldImage.image_path,
          size: oldImage.image_size || 0,
          type: oldImage.image_type || 'image/jpeg',
          category: 'checks' as const,
          contextType: 'check' as const,
          contextId: oldImage.check_id,
          description: oldImage.notes,
          tags: [oldImage.image_side],
          isActive: true,
          isPrimary: false,
          sortOrder: 0,
          uploadedAt: new Date(oldImage.created_at),
          uploadedBy: oldImage.created_by,
          metadata: {
            imageSide: oldImage.image_side,
            scanQuality: oldImage.scan_quality
          }
        }
        
        await imageService.saveImageData(newImageData)
      }
    }
    
    console.log('تم ترحيل البيانات من الجداول القديمة')
  } catch (error) {
    console.error('خطأ في ترحيل البيانات:', error)
  }
}

const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}
```

## 🔧 تحديث المكونات الموجودة

### تحديث مكون إدارة الأصناف

```typescript
// في ItemForm.tsx
import { useImageManager } from '../hooks/images'

const ItemForm = ({ itemId }: { itemId: number }) => {
  const {
    images,
    uploadImage,
    deleteImage,
    setPrimaryImage
  } = useImageManager({
    category: 'inventory',
    contextType: 'item',
    contextId: itemId
  })

  return (
    <div>
      {/* باقي نموذج الصنف */}
      
      <div className="item-images-section">
        <h3>صور الصنف</h3>
        
        {/* استخدام المكون الجديد */}
        <UnifiedImageManager
          category="inventory"
          contextType="item"
          contextId={itemId}
          maxImages={5}
          allowMultiple={true}
          onImagesChange={(images) => {
            console.log('تم تحديث صور الصنف:', images)
          }}
        />
      </div>
    </div>
  )
}
```

### تحديث مكون إدارة الشيكات

```typescript
// في CheckForm.tsx
import { useImageManager } from '../hooks/images'

const CheckForm = ({ checkId }: { checkId: number }) => {
  const {
    images,
    uploadImage,
    deleteImage
  } = useImageManager({
    category: 'checks',
    contextType: 'check',
    contextId: checkId,
    uploadOptions: {
      maxSize: 2 * 1024 * 1024, // 2MB للشيكات
      quality: 0.9, // جودة عالية
      generateThumbnail: true
    }
  })

  const handleUploadCheckSide = async (file: File, side: 'front' | 'back') => {
    const success = await uploadImage(file, {
      tags: [side],
      description: `صورة ${side === 'front' ? 'وجه' : 'ظهر'} الشيك`
    })
    
    if (success) {
      message.success(`تم رفع صورة ${side === 'front' ? 'الوجه' : 'الظهر'} بنجاح`)
    }
  }

  return (
    <div>
      {/* باقي نموذج الشيك */}
      
      <div className="check-images-section">
        <h3>صور الشيك</h3>
        
        <Space direction="vertical">
          <div>
            <h4>وجه الشيك</h4>
            <input
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0]
                if (file) handleUploadCheckSide(file, 'front')
              }}
            />
          </div>
          
          <div>
            <h4>ظهر الشيك</h4>
            <input
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0]
                if (file) handleUploadCheckSide(file, 'back')
              }}
            />
          </div>
        </Space>
        
        {/* عرض الصور الموجودة */}
        <div className="existing-images">
          {images.map(image => (
            <div key={image.id} className="image-item">
              <img 
                src={`file://${image.thumbnailPath || image.path}`} 
                alt={image.name}
                style={{ width: 100, height: 100, objectFit: 'cover' }}
              />
              <p>{image.description}</p>
              <Button 
                size="small" 
                danger 
                onClick={() => deleteImage(image.id)}
              >
                حذف
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
```

### تحديث مكون أوامر الإنتاج

```typescript
// في ProductionOrderForm.tsx
import { UnifiedImageManager } from '../components/images'

const ProductionOrderForm = ({ orderId }: { orderId: number }) => {
  return (
    <div>
      {/* باقي نموذج أمر الإنتاج */}
      
      <div className="production-images-section">
        <h3>صور أمر الإنتاج</h3>
        
        <UnifiedImageManager
          category="production"
          contextType="production_order"
          contextId={orderId}
          maxImages={10}
          allowMultiple={true}
          uploadOptions={{
            watermark: true,
            watermarkText: 'أمر إنتاج #' + orderId
          }}
          onImagesChange={(images) => {
            // تحديث حالة أمر الإنتاج إذا لزم الأمر
            console.log('تم تحديث صور أمر الإنتاج:', images)
          }}
        />
      </div>
    </div>
  )
}
```

## 🔄 التوافق مع النظام القديم

### استخدام Adapter Pattern

```typescript
// إنشاء محول للتوافق مع النظام القديم
class LegacyImageAdapter {
  private imageService = ImageCoreService.getInstance()

  // تحويل من التنسيق القديم إلى الجديد
  async convertLegacyToNew(legacyImage: any): Promise<UnifiedImageData> {
    return {
      id: legacyImage.id || this.generateId(),
      name: legacyImage.name || legacyImage.image_name,
      originalName: legacyImage.originalName || legacyImage.image_name,
      path: legacyImage.path || legacyImage.image_path,
      thumbnailPath: legacyImage.thumbnailPath,
      size: legacyImage.size || legacyImage.image_size || 0,
      width: legacyImage.width || legacyImage.image_width,
      height: legacyImage.height || legacyImage.image_height,
      type: legacyImage.type || legacyImage.image_type || 'image/jpeg',
      category: this.mapCategory(legacyImage.category),
      contextType: this.mapContextType(legacyImage.contextType),
      contextId: legacyImage.contextId || legacyImage.item_id || legacyImage.check_id,
      description: legacyImage.description || legacyImage.notes,
      tags: legacyImage.tags || [],
      isActive: legacyImage.isActive !== false,
      isPrimary: legacyImage.isPrimary || legacyImage.is_primary === 1,
      sortOrder: legacyImage.sortOrder || legacyImage.sort_order || 0,
      uploadedAt: new Date(legacyImage.uploadedAt || legacyImage.created_at || Date.now()),
      uploadedBy: legacyImage.uploadedBy || legacyImage.created_by,
      metadata: legacyImage.metadata || {}
    }
  }

  // تحويل من التنسيق الجديد إلى القديم
  convertNewToLegacy(newImage: UnifiedImageData): any {
    return {
      id: newImage.id,
      name: newImage.name,
      path: newImage.path,
      size: newImage.size,
      type: newImage.type,
      isPrimary: newImage.isPrimary,
      description: newImage.description,
      uploadedAt: newImage.uploadedAt.toISOString()
    }
  }

  private mapCategory(category: any): ImageCategory {
    const mapping: Record<string, ImageCategory> = {
      'items': 'inventory',
      'inventory': 'inventory',
      'production': 'production',
      'sales': 'sales',
      'checks': 'checks'
    }
    return mapping[category] || 'general'
  }

  private mapContextType(contextType: any): ImageContextType {
    const mapping: Record<string, ImageContextType> = {
      'item': 'item',
      'production_order': 'production_order',
      'invoice': 'invoice',
      'check': 'check'
    }
    return mapping[contextType] || 'item'
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }
}

// استخدام المحول
const adapter = new LegacyImageAdapter()

// في المكونات القديمة
const legacyComponent = {
  async loadImages(itemId: number) {
    const result = await imageService.getImages({
      category: 'inventory',
      contextType: 'item',
      contextId: itemId
    })
    
    if (result.success) {
      // تحويل إلى التنسيق القديم للتوافق
      return result.data.map(img => adapter.convertNewToLegacy(img))
    }
    
    return []
  }
}
```

## 📊 مراقبة الأداء

```typescript
// إضافة مراقبة الأداء
const performanceMonitor = {
  async measureImageOperation(operation: string, fn: () => Promise<any>) {
    const startTime = performance.now()
    
    try {
      const result = await fn()
      const endTime = performance.now()
      const duration = endTime - startTime
      
      console.log(`${operation}: ${duration.toFixed(2)}ms`)
      
      // إرسال إلى نظام المراقبة إذا لزم الأمر
      if (duration > 1000) {
        console.warn(`عملية بطيئة: ${operation} استغرقت ${duration.toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      console.error(`فشل في ${operation} بعد ${duration.toFixed(2)}ms:`, error)
      throw error
    }
  }
}

// استخدام المراقب
const uploadWithMonitoring = async (file: File) => {
  return performanceMonitor.measureImageOperation(
    `رفع صورة ${file.name}`,
    () => uploadImage(file)
  )
}
```

## 🧪 اختبار التكامل

```typescript
// اختبارات التكامل
const integrationTests = {
  async testImageUpload() {
    console.log('🧪 اختبار رفع الصور...')
    
    // إنشاء ملف تجريبي
    const testFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
    
    const result = await imageService.uploadImage(
      testFile,
      'inventory',
      'item',
      999, // معرف تجريبي
      { quality: 0.8 }
    )
    
    if (result.success) {
      console.log('✅ نجح اختبار رفع الصور')
      
      // اختبار الحذف
      await imageService.deleteImage(result.data!.id)
      console.log('✅ نجح اختبار حذف الصور')
    } else {
      console.error('❌ فشل اختبار رفع الصور:', result.error)
    }
  },

  async testDatabaseIntegration() {
    console.log('🧪 اختبار تكامل قاعدة البيانات...')
    
    try {
      const stats = await imageService.getImageStatistics()
      if (stats.success) {
        console.log('✅ نجح اختبار قاعدة البيانات')
        console.log('📊 الإحصائيات:', stats.data)
      }
    } catch (error) {
      console.error('❌ فشل اختبار قاعدة البيانات:', error)
    }
  },

  async runAllTests() {
    await this.testImageUpload()
    await this.testDatabaseIntegration()
    console.log('🎉 انتهت جميع الاختبارات')
  }
}

// تشغيل الاختبارات في وضع التطوير
if (process.env.NODE_ENV === 'development') {
  integrationTests.runAllTests()
}
```

## 📝 نصائح مهمة

### 1. التدرج في التطبيق
- ابدأ بقسم واحد (مثل الأصناف)
- اختبر بدقة قبل الانتقال للقسم التالي
- احتفظ بنسخة احتياطية من البيانات

### 2. إدارة الأخطاء
- استخدم try-catch في جميع العمليات
- اعرض رسائل خطأ واضحة للمستخدم
- سجل الأخطاء للمراجعة اللاحقة

### 3. الأداء
- راقب أداء العمليات
- استخدم lazy loading للصور الكبيرة
- نظف cache بانتظام

### 4. الأمان
- تحقق من صحة الملفات قبل الرفع
- استخدم التشفير للبيانات الحساسة
- قيد الوصول حسب صلاحيات المستخدم

---

**هذا الدليل يضمن التكامل السلس مع النظام الحالي مع الاستفادة من جميع ميزات النظام المحسن.**
