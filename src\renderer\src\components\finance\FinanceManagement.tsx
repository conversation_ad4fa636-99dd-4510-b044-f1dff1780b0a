import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Button, Statistic } from 'antd'
import {
  BankOutlined,
  CreditCardOutlined,
  FileTextOutlined,
  BarChartOutlined,
  ArrowLeftOutlined,
  WalletOutlined,
  TransactionOutlined
} from '@ant-design/icons'
import BankAccountManagement from './BankAccountManagement'
import CheckManagement from './CheckManagement'
import CheckTransferManagement from './CheckTransferManagement'
import CompanyCheckManagement from './CompanyCheckManagement'
import VoucherManagement from './VoucherManagement'
import AdvancedVoucherManagement from './AdvancedVoucherManagement'
import PromissoryNoteManagement from './PromissoryNoteManagement'
import ChartOfAccountsManagement from './ChartOfAccountsManagement'
import FinanceReports from './FinanceReports'
import ComprehensiveFinanceReports from './ComprehensiveFinanceReports'

import JournalEntryManagement from './JournalEntryManagement'

interface FinanceManagementProps {
  onBack: () => void
  initialView?: ActiveView
  reportType?: string
}

type ActiveView = 'main' | 'banks' | 'checks' | 'check-transfers' | 'company-checks' | 'vouchers' | 'advanced-vouchers' | 'notes' | 'reports' | 'comprehensive-reports' | 'journal-entries' | 'chart-of-accounts'

const FinanceManagement: React.FC<FinanceManagementProps> = ({ onBack, initialView = 'main', reportType }) => {
  const [activeView, setActiveView] = useState<ActiveView>(initialView as ActiveView)
  const [stats, setStats] = useState({
    totalBankAccounts: 0,
    totalBalance: 0,
    totalChecks: 0,
    pendingChecks: 0,
    totalVouchers: 0,
    pendingVouchers: 0
  })

  // تحديث activeView عندما يتغير initialView من الخارج
  useEffect(() => {
    setActiveView(initialView as ActiveView)
  }, [initialView])

  useEffect(() => {
    loadStats()
  }, [])

  const loadStats = async () => {
    try {
      // إحصائيات الحسابات المصرفية
      const bankAccountsResponse = await window.electronAPI.getBankAccounts()
      if (bankAccountsResponse.success) {
        const accounts = bankAccountsResponse.data
        const totalBalance = accounts.reduce((sum: number, account: any) => sum + (account.balance || 0), 0)
        setStats(prev => ({
          ...prev,
          totalBankAccounts: accounts.length,
          totalBalance
        }))
      }

      // إحصائيات الشيكات
      const checksResponse = await window.electronAPI.getChecks()
      if (checksResponse.success) {
        const checks = checksResponse.data
        const pendingChecks = checks.filter((check: any) => check.status === 'issued').length
        setStats(prev => ({
          ...prev,
          totalChecks: checks.length,
          pendingChecks
        }))
      }

      // إحصائيات السندات
      const paymentVouchersResponse = await window.electronAPI.getPaymentVouchers()
      const receiptVouchersResponse = await window.electronAPI.getReceiptVouchers()
      if (paymentVouchersResponse.success && receiptVouchersResponse.success) {
        const totalVouchers = paymentVouchersResponse.data.length + receiptVouchersResponse.data.length
        const pendingVouchers = [
          ...paymentVouchersResponse.data.filter((v: any) => v.status === 'pending'),
          ...receiptVouchersResponse.data.filter((v: any) => v.status === 'pending')
        ].length
        setStats(prev => ({
          ...prev,
          totalVouchers,
          pendingVouchers
        }))
      }
    } catch (error) {
      Logger.error('FinanceManagement', 'خطأ في تحميل الإحصائيات:', error)
    }
  }

  const renderMainView = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>💰 إدارة المالية</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            إدارة شاملة للحسابات المصرفية، الشيكات، السندات، والمعاملات المالية
          </p>
        </div>
        <Button 
          type="default" 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
          size="large"
        >
          العودة للوحة الرئيسية
        </Button>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="الحسابات المصرفية"
              value={stats.totalBankAccounts}
              valueStyle={{ color: '#1890ff' }}
              prefix={<BankOutlined />}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="إجمالي الأرصدة"
              value={stats.totalBalance}
              valueStyle={{ color: '#52c41a' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي الشيكات"
              value={stats.totalChecks}
              valueStyle={{ color: '#722ed1' }}
              prefix={<CreditCardOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="شيكات معلقة"
              value={stats.pendingChecks}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<CreditCardOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي السندات"
              value={stats.totalVouchers}
              valueStyle={{ color: '#13c2c2' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={3}>
          <Card>
            <Statistic
              title="سندات معلقة"
              value={stats.pendingVouchers}
              valueStyle={{ color: '#eb2f96' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* قوائم الإدارة */}
      <Row gutter={16}>
        <Col span={6}>
          <Card 
            hoverable 
            onClick={() => setActiveView('banks')}
            style={{ textAlign: 'center', cursor: 'pointer' }}
          >
            <BankOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
            <h3>إدارة البنوك</h3>
            <p>الحسابات المصرفية والمعاملات</p>
          </Card>
        </Col>
        <Col span={6}>
          <Card
            hoverable
            onClick={() => setActiveView('checks')}
            style={{ textAlign: 'center', cursor: 'pointer' }}
          >
            <CreditCardOutlined style={{ fontSize: '48px', color: '#722ed1', marginBottom: '16px' }} />
            <h3>إدارة الشيكات</h3>
            <p>إصدار ومتابعة الشيكات</p>
          </Card>
        </Col>
        <Col span={6}>
          <Card
            hoverable
            onClick={() => setActiveView('check-transfers')}
            style={{ textAlign: 'center', cursor: 'pointer' }}
          >
            <TransactionOutlined style={{ fontSize: '48px', color: '#fa8c16', marginBottom: '16px' }} />
            <h3>تحويل الشيكات</h3>
            <p>تحويل الشيكات بين الأطراف</p>
          </Card>
        </Col>
        <Col span={6}>
          <Card
            hoverable
            onClick={() => setActiveView('journal-entries')}
            style={{ textAlign: 'center', cursor: 'pointer' }}
          >
            <FileTextOutlined style={{ fontSize: '48px', color: '#13c2c2', marginBottom: '16px' }} />
            <h3>القيود اليومية</h3>
            <p>إدخال وإدارة القيود المحاسبية اليومية</p>
          </Card>
        </Col>
        <Col span={6}>
          <Card 
            hoverable 
            onClick={() => setActiveView('notes')}
            style={{ textAlign: 'center', cursor: 'pointer' }}
          >
            <WalletOutlined style={{ fontSize: '48px', color: '#fa8c16', marginBottom: '16px' }} />
            <h3>الكمبيالات</h3>
            <p>إدارة الكمبيالات والأوراق المالية</p>
          </Card>
        </Col>
      </Row>

      <Row gutter={16} style={{ marginTop: '16px' }}>
        <Col span={8}>
          <Card
            hoverable
            onClick={() => setActiveView('company-checks')}
            style={{ textAlign: 'center', cursor: 'pointer' }}
          >
            <BankOutlined style={{ fontSize: '48px', color: '#eb2f96', marginBottom: '16px' }} />
            <h3>شيكات الشركة</h3>
            <p>إصدار وإدارة شيكات الشركة</p>
          </Card>
        </Col>
        <Col span={6}>
          <Card
            hoverable
            onClick={() => setActiveView('advanced-vouchers')}
            style={{ textAlign: 'center', cursor: 'pointer' }}
          >
            <WalletOutlined style={{ fontSize: '48px', color: '#fa8c16', marginBottom: '16px' }} />
            <h3>نّام السندات المتقدم</h3>
            <p>سندات الدفع والقبض مع الربط التلقائي</p>
          </Card>
        </Col>
        <Col span={6}>
          <Card
            hoverable
            onClick={() => setActiveView('chart-of-accounts')}
            style={{ textAlign: 'center', cursor: 'pointer' }}
          >
            <BankOutlined style={{ fontSize: '48px', color: '#13c2c2', marginBottom: '16px' }} />
            <h3>دليل الحسابات</h3>
            <p>إدارة الهيكل المحاسبي والحسابات</p>
          </Card>
        </Col>
        <Col span={6}>
          <Card
            hoverable
            onClick={() => setActiveView('comprehensive-reports')}
            style={{ textAlign: 'center', cursor: 'pointer' }}
          >
            <BarChartOutlined style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} />
            <h3>التقارير المالية الشاملة</h3>
            <p>تقارير متقدمة تشمل جميع الأقسام</p>
          </Card>
        </Col>

      </Row>
    </div>
  )

  const getReportInitialView = () => {
    if (!reportType) return 'main'

    const reportTypeMap: { [key: string]: string } = {
      'balance-sheet-report': 'balance-sheet',
      'income-statement-report': 'income-statement',
      'cash-flow-report': 'cash-flow',
      'receivables-report': 'receivables',
      'detailed-costs-report': 'detailed-costs',
      'profitability-analysis-report': 'profitability-analysis',
      'cost-centers-report': 'cost-centers'
    }

    return reportTypeMap[reportType] || 'main'
  }

  const renderContent = () => {
    switch (activeView) {
      case 'banks':
        return <BankAccountManagement onBack={() => setActiveView('main')} />
      case 'checks':
        return <CheckManagement onBack={() => setActiveView('main')} />
      case 'check-transfers':
        return <CheckTransferManagement onBack={() => setActiveView('main')} />
      case 'company-checks':
        return <CompanyCheckManagement onBack={() => setActiveView('main')} />
      case 'vouchers':
        return <VoucherManagement onBack={() => setActiveView('main')} />
      case 'advanced-vouchers':
        return <AdvancedVoucherManagement onBack={() => setActiveView('main')} />
      case 'notes':
        return <PromissoryNoteManagement onBack={() => setActiveView('main')} />
      case 'reports':
        return <FinanceReports onBack={() => setActiveView('main')} initialView={getReportInitialView() as any} />
      case 'comprehensive-reports':
        return <ComprehensiveFinanceReports onBack={() => setActiveView('main')} />

      case 'journal-entries':
        return <JournalEntryManagement onBack={() => setActiveView('main')} />
      case 'chart-of-accounts':
        return <ChartOfAccountsManagement onBack={() => setActiveView('main')} />
      default:
        return renderMainView()
    }
  }

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      {renderContent()}
    </div>
  )
}

export default FinanceManagement

import { SafeLogger as Logger } from '../../utils/logger'