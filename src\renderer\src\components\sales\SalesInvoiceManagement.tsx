import React, { useState, useEffect } from 'react'
import { SafeLogger as Logger } from '../../utils/logger'
import * as XLSX from 'xlsx'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Space,
  Popconfirm,
  Card,
  Row,
  Col,
  Statistic,
  Tag,
  Select,
  DatePicker,
  InputNumber,
  Divider,
  Typography,
  // message,
  App
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FileTextOutlined,
  UserOutlined,
  CalendarOutlined,
  DollarOutlined,
  ArrowLeftOutlined,
  SearchOutlined,
  CreditCardOutlined,
  DownloadOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import { useCurrentUser } from '../../utils/permissions'
import { calculateInvoiceTotals, getCurrencySymbol, formatCurrency } from '../../utils/settings'

import {
  PaymentMethodSelector,
  PaymentForm,
  PAYMENT_METHODS,
  PaymentMethodType
} from '../common/PaymentComponents'
import {
  RemainingAmountDisplay,
  PaymentStatusTag,
  PaymentSummary
} from '../common/RemainingAmountDisplay'
import { InvoicePrintButton } from '../common'

const { Option } = Select
const { TextArea } = Input

interface SalesInvoice {
  id: number
  invoice_number: string
  customer_id: number
  customer_name: string
  customer_address?: string
  customer_phone?: string
  invoice_date: string
  due_date?: string
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
  total_amount: number
  paid_amount: number
  remaining_amount: number
  subtotal?: number
  discount?: number
  tax?: number
  discount_amount?: number
  tax_amount?: number
  items?: SalesInvoiceItem[]
  notes?: string
  created_by: number
  created_at: string
  updated_at: string
}

interface SalesInvoiceItem {
  id?: number
  item_id: number
  item_name: string
  quantity: number
  unit_price: number
  total_price: number
  warehouse_id: number
  notes?: string
}

interface Customer {
  id: number
  name: string
  code: string
}

interface Item {
  id: number
  name: string
  code: string
  sale_price: number
  is_active: boolean
  available_quantity?: number
}

interface Warehouse {
  id: number
  name: string
  location: string
  is_active: boolean
}

interface SalesInvoiceManagementProps {
  onBack: () => void
}

const SalesInvoiceManagement: React.FC<SalesInvoiceManagementProps> = ({ onBack }) => {
  const { id: userId } = useCurrentUser()
  const [invoices, setInvoices] = useState<SalesInvoice[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [_items, setItems] = useState<Item[]>([])
  const [itemsByWarehouse, setItemsByWarehouse] = useState<{[key: number]: Item[]}>({})
  const [warehouses, setWarehouses] = useState<Warehouse[]>([])
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState({
    totalInvoices: 0,
    draftInvoices: 0,
    paidInvoices: 0,
    totalAmount: 0,
    paidAmount: 0,
    remainingAmount: 0
  })
  const [modalVisible, setModalVisible] = useState(false)
  const [editingInvoice, setEditingInvoice] = useState<SalesInvoice | null>(null)
  const [invoiceItems, setInvoiceItems] = useState<SalesInvoiceItem[]>([])
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState('')

  // حالات جديدة لربط أوامر البيع
  const [availableOrders, setAvailableOrders] = useState<any[]>([])
  const [selectedCustomerId, setSelectedCustomerId] = useState<number | null>(null)

  // حالات جديدة للدفع والطباعة
  const [paymentModalVisible, setPaymentModalVisible] = useState(false)
  const [selectedInvoiceForPayment, setSelectedInvoiceForPayment] = useState<SalesInvoice | null>(null)
  const [paymentForm] = Form.useForm()
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethodType>(PAYMENT_METHODS.CASH)
  const [invoicePayments, setInvoicePayments] = useState<any[]>([])
  const [loadingPayments, setLoadingPayments] = useState(false)

  // استخدام App context للرسائل
  const { message: messageApi } = App.useApp()

  useEffect(() => {
    Logger.info('SalesInvoiceManagement', '🚀 تحميل مكون فاتورة المبيعات')
    loadInvoices()
    loadCustomers()
    loadItems()
    loadWarehouses()
  }, [])

  // مراقبة تغييرات itemsByWarehouse
  useEffect(() => {
    Logger.info('SalesInvoiceManagement', '📦 تم تحديث itemsByWarehouse:', itemsByWarehouse)
  }, [itemsByWarehouse])



  const loadInvoices = async () => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getSalesInvoices()
        if (response.success) {
          const invoicesData = response.data || []
          setInvoices(invoicesData)

          // حساب الإحصائيات
          const totalInvoices = invoicesData.length
          const draftInvoices = invoicesData.filter((inv: SalesInvoice) => inv.status === 'draft').length
          const paidInvoices = invoicesData.filter((inv: SalesInvoice) => inv.status === 'paid').length
          const totalAmount = invoicesData.reduce((sum: number, inv: SalesInvoice) => sum + (inv.total_amount || 0), 0)
          const paidAmount = invoicesData.reduce((sum: number, inv: SalesInvoice) => sum + (inv.paid_amount || 0), 0)
          const remainingAmount = totalAmount - paidAmount

          setStats({
            totalInvoices,
            draftInvoices,
            paidInvoices,
            totalAmount,
            paidAmount,
            remainingAmount
          })

          Logger.info('SalesInvoiceManagement', `✅ تم تحميل ${totalInvoices} فاتورة بيع بنجاح`)
        } else {
          const errorMessage = response.message || 'فشل في تحميل فواتير البيع'
          Logger.error('SalesInvoiceManagement', '❌ خطأ في تحميل فواتير البيع:', new Error(errorMessage))
          messageApi.error(errorMessage)
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('SalesInvoiceManagement', '❌ window.electronAPI غير متوفر', new Error(errorMessage))
        messageApi.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل فواتير البيع'
      Logger.error('SalesInvoiceManagement', '❌ خطأ في تحميل فواتير البيع:', error instanceof Error ? error : new Error(String(error)))
      messageApi.error(`خطأ في تحميل الفواتير: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  const loadCustomers = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getCustomers()
        if (response.success) {
          setCustomers(response.data || [])
        }
      }
    } catch (error) {
      Logger.error('SalesInvoiceManagement', 'خطأ في تحميل العملاء:', error instanceof Error ? error : new Error(String(error)))
    }
  }

  const loadItems = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getItems()
        if (response && (response as any).success) {
          const itemsData = (response as any).data || []
          setItems(itemsData.filter((item: any) => item.is_active !== false) as any)
          Logger.info('SalesInvoiceManagement', `✅ تم تحميل ${itemsData.length} صنف بنجاح`)
        } else if (Array.isArray(response)) {
          // للتوافق مع الإصدارات القديمة
          setItems(response.filter((item: any) => item.is_active) as any)
          Logger.info('SalesInvoiceManagement', `✅ تم تحميل ${response.length} صنف بنجاح`)
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل الأصناف'
          Logger.error('SalesInvoiceManagement', '❌ خطأ في تحميل الأصناف:', new Error(errorMessage))
          messageApi.error(errorMessage)
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('SalesInvoiceManagement', '❌ window.electronAPI غير متوفر', new Error(errorMessage))
        messageApi.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل الأصناف'
      Logger.error('SalesInvoiceManagement', '❌ خطأ في تحميل الأصناف:', error instanceof Error ? error : new Error(String(error)))
      messageApi.error(`خطأ في تحميل الأصناف: ${errorMessage}`)
    }
  }

  // تحميل الأصناف المتوفرة في مخزن محدد
  const loadItemsByWarehouse = async (warehouseId: number) => {
    Logger.info('SalesInvoiceManagement', `🔄 بدء تحميل الأصناف للمخزن ${warehouseId}`)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getItemsByWarehouse(warehouseId)
        if (response.success) {
          const itemsData = response.data || []
          setItemsByWarehouse(prev => {
            const updated = {
              ...prev,
              [warehouseId]: itemsData
            }
            Logger.info('SalesInvoiceManagement', '✅ تم تحديث itemsByWarehouse:', updated)
            return updated
          })
          Logger.info('SalesInvoiceManagement', '✅ تم تحميل ${itemsData.length} صنف للمخزن ${warehouseId}:', itemsData)
        } else {
          const errorMessage = response.message || 'فشل في تحميل الأصناف للمخزن'
          Logger.error('SalesInvoiceManagement', '❌ خطأ في تحميل الأصناف للمخزن:', new Error(errorMessage))
          messageApi.error(errorMessage)
          setItemsByWarehouse(prev => ({
            ...prev,
            [warehouseId]: []
          }))
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات'
        Logger.error('SalesInvoiceManagement', '❌ window.electronAPI غير متوفر', new Error(errorMessage))
        messageApi.error(errorMessage)
        setItemsByWarehouse(prev => ({
          ...prev,
          [warehouseId]: []
        }))
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل الأصناف للمخزن'
      Logger.error('SalesInvoiceManagement', '❌ خطأ في تحميل الأصناف للمخزن:', error instanceof Error ? error : new Error(String(error)))
      messageApi.error(`خطأ في تحميل الأصناف للمخزن: ${errorMessage}`)
      setItemsByWarehouse(prev => ({
        ...prev,
        [warehouseId]: []
      }))
    }
  }

  const loadWarehouses = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getWarehouses() as any
        if (response.success) {
          const warehousesData = response.data || []
          setWarehouses(warehousesData.filter((warehouse: Warehouse) => warehouse.is_active))
          Logger.info('SalesInvoiceManagement', '✅ تم تحميل ${warehousesData.length} مخزن بنجاح')
        } else {
          const errorMessage = response.message || 'فشل في تحميل المخازن'
          Logger.error('SalesInvoiceManagement', '❌ خطأ في تحميل المخازن:', new Error(errorMessage))
          messageApi.error(errorMessage)
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('SalesInvoiceManagement', '❌ window.electronAPI غير متوفر', new Error(errorMessage))
        messageApi.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل المخازن'
      Logger.error('SalesInvoiceManagement', '❌ خطأ في تحميل المخازن:', error instanceof Error ? error : new Error(String(error)))
      messageApi.error(`خطأ في تحميل المخازن: ${errorMessage}`)
    }
  }

  // تحميل أوامر البيع المتاحة للربط
  const loadAvailableOrders = async (customerId?: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getAvailableSalesOrders(customerId)
        if (response.success) {
          setAvailableOrders(response.data || [])
          Logger.info('SalesInvoiceManagement', '✅ تم تحميل ${response.data?.length || 0} أمر بيع متاح')
        } else {
          Logger.error('SalesInvoiceManagement', '❌ خطأ في تحميل أوامر البيع المتاحة:', new Error(response.message || 'خطأ غير معروف'))
        }
      }
    } catch (error) {
      Logger.error('SalesInvoiceManagement', '❌ خطأ في تحميل أوامر البيع المتاحة:', error instanceof Error ? error : new Error(String(error)))
    }
  }



  // دالة للتحقق من تفرد رقم الفاتورة
  const checkInvoiceNumberUniqueness = async (invoiceNumber: string): Promise<boolean> => {
    try {
      if (window.electronAPI && window.electronAPI.checkInvoiceNumberUniqueness) {
        const response = await window.electronAPI.checkInvoiceNumberUniqueness(
          'sales_invoices',
          invoiceNumber,
          editingInvoice?.id
        )
        if (response.success) {
          return response.data.isUnique
        }
      }
      return true
    } catch (error) {
      Logger.error('SalesInvoiceManagement', 'خطأ في التحقق من تفرد رقم الفاتورة:', error instanceof Error ? error : new Error(String(error)))
      return true // في حالة الخطأ، نسمح بالمتابعة
    }
  }

  // دالة للتحقق من توفر الكميات في المخزن
  const checkInventoryAvailability = async (items: SalesInvoiceItem[]): Promise<{success: boolean, message?: string}> => {
    try {
      if (window.electronAPI) {
        for (let i = 0; i < items.length; i++) {
          const item = items[i]

          // التحقق من توفر الكمية في المخزن المحدد
          const inventoryResponse = await window.electronAPI.checkItemAvailability(item.item_id, item.warehouse_id, item.quantity)

          if (inventoryResponse.success) {
            if (!inventoryResponse.available) {
              const itemName = item.item_name || `الصنف ${item.item_id}`
              const warehouseName = warehouses.find(w => w.id === item.warehouse_id)?.name || `المخزن ${item.warehouse_id}`

              return {
                success: false,
                message: `الكمية المطلوبة (${item.quantity}) من "${itemName}" غير متوفرة في "${warehouseName}". الكمية المتوفرة: ${inventoryResponse.availableQuantity || 0}`
              }
            }
          } else {
            // إذا لم نتمكن من الحصول على بيانات المخزون، نحذر المستخدم
            const itemName = item.item_name || `الصنف ${item.item_id}`
            Logger.warn('SalesInvoiceManagement', `تعذر التحقق من مخزون "${itemName}": ${inventoryResponse.message}`)
          }
        }

        return { success: true }
      }

      return { success: true } // في حالة عدم توفر API، نسمح بالمتابعة
    } catch (error) {
      Logger.error('SalesInvoiceManagement', 'خطأ في التحقق من المخزون:', error instanceof Error ? error : new Error(String(error)))
      return {
        success: false,
        message: 'حدث خطأ أثناء التحقق من المخزون. يرجى المحاولة مرة أخرى.'
      }
    }
  }

  const generateInvoiceNumber = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generateSalesInvoiceNumber()
        if (response.success && response.data?.invoiceNumber) {
          // التحقق من تفرد الرقم المولد
          const isUnique = await checkInvoiceNumberUniqueness(response.data.invoiceNumber)
          if (!isUnique) {
            // إذا كان الرقم موجود، أعد المحاولة مرة أخرى
            const retryResponse = await window.electronAPI.generateSalesInvoiceNumber()
            if (retryResponse.success && retryResponse.data?.invoiceNumber) {
              form.setFieldsValue({ invoice_number: retryResponse.data.invoiceNumber })
              messageApi.success('تم إنشاء رقم فاتورة جديد')
            } else {
              throw new Error('فشل في إعادة إنشاء رقم الفاتورة')
            }
          } else {
            form.setFieldsValue({ invoice_number: response.data.invoiceNumber })
            messageApi.success('تم إنشاء رقم الفاتورة')
          }
        } else {
          throw new Error('فشل في الحصول على رقم فاتورة من الخادم')
        }
      }
    } catch (error) {
      Logger.error('SalesInvoiceManagement', 'خطأ في إنشاء رقم الفاتورة:', error instanceof Error ? error : new Error(String(error)))
      // fallback إلى timestamp مع معرف عشوائي
      const timestamp = Date.now().toString().slice(-6)
      const randomId = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      const invoiceNumber = `SI${timestamp}${randomId}`
      form.setFieldsValue({ invoice_number: invoiceNumber })
      messageApi.warning('تم إنشاء رقم فاتورة مؤقت. يرجى التحقق من الرقم قبل الحفّ.')
    }
  }

  const handleSubmit = async (values: any) => {
    // التحقق من صحة البيانات الأساسية
    if (!values.invoice_number || values.invoice_number.trim() === '') {
      messageApi.error('رقم الفاتورة مطلوب')
      return { success: false, message: 'رقم الفاتورة مطلوب' }
    }

    // التحقق من عدم وجود رقم الفاتورة مسبقاً باستخدام API محسن
    const isUnique = await checkInvoiceNumberUniqueness(values.invoice_number.trim())
    if (!isUnique) {
      messageApi.error('رقم الفاتورة موجود مسبقاً. يرجى استخدام رقم آخر أو الضغط على زر "توليد"')
      return { success: false, message: 'رقم الفاتورة موجود مسبقاً' }
    }

    if (!values.customer_id) {
      messageApi.error('يجب اختيار العميل')
      return { success: false, message: 'يجب اختيار العميل' }
    }

    if (invoiceItems.length === 0) {
      messageApi.error('يجب إضافة صنف واحد على الأقل')
      return { success: false, message: 'يجب إضافة صنف واحد على الأقل' }
    }

    // التحقق من صحة بيانات الأصناف
    for (let i = 0; i < invoiceItems.length; i++) {
      const item = invoiceItems[i]
      if (!item.item_id || item.item_id === 0) {
        messageApi.error(`يجب اختيار الصنف في السطر ${i + 1}`)
        return { success: false, message: `يجب اختيار الصنف في السطر ${i + 1}` }
      }
      if (!item.warehouse_id || item.warehouse_id === 0) {
        messageApi.error(`يجب اختيار المخزن في السطر ${i + 1}`)
        return { success: false, message: `يجب اختيار المخزن في السطر ${i + 1}` }
      }
      if (!item.quantity || item.quantity <= 0) {
        messageApi.error(`الكمية يجب أن تكون أكبر من صفر في السطر ${i + 1}`)
        return { success: false, message: `الكمية يجب أن تكون أكبر من صفر في السطر ${i + 1}` }
      }
      if (!item.unit_price || item.unit_price <= 0) {
        messageApi.error(`سعر الوحدة يجب أن يكون أكبر من صفر في السطر ${i + 1}`)
        return { success: false, message: `سعر الوحدة يجب أن يكون أكبر من صفر في السطر ${i + 1}` }
      }

    }

    // التحقق من توفر الكميات في المخزون
    const inventoryCheck = await checkInventoryAvailability(invoiceItems)
    if (!inventoryCheck.success) {
      messageApi.error(inventoryCheck.message || 'خطأ في التحقق من المخزون')
      return { success: false, message: inventoryCheck.message || 'خطأ في التحقق من المخزون' }
    }

    // حساب المجموع الفرعي من العناصر المضافة (وليس من القيمة المدخلة)
    const calculatedSubtotal = invoiceItems.reduce((sum, item) => sum + (item.total_price || 0), 0)
    const totalAmount = values.total_amount || 0
    const taxAmount = values.tax_amount || 0
    const discountAmount = values.discount_amount || 0

    // التحقق من وجود عناصر في الفاتورة
    if (invoiceItems.length === 0) {
      messageApi.error('يجب إضافة صنف واحد على الأقل إلى الفاتورة')
      return { success: false, message: 'يجب إضافة صنف واحد على الأقل إلى الفاتورة' }
    }

    // التحقق من صحة الخصم
    if (discountAmount < 0) {
      messageApi.error('مبلغ الخصم لا يمكن أن يكون سالباً')
      return { success: false, message: 'مبلغ الخصم لا يمكن أن يكون سالباً' }
    }

    if (discountAmount > calculatedSubtotal) {
      messageApi.error('مبلغ الخصم لا يمكن أن يكون أكبر من المجموع الفرعي')
      return { success: false, message: 'مبلغ الخصم لا يمكن أن يكون أكبر من المجموع الفرعي' }
    }

    // التحقق من صحة الضريبة
    if (taxAmount < 0) {
      messageApi.error('مبلغ الضريبة لا يمكن أن يكون سالباً')
      return { success: false, message: 'مبلغ الضريبة لا يمكن أن يكون سالباً' }
    }

    // التحقق من أن المجموع الفرعي المحسوب أكبر من صفر
    if (calculatedSubtotal <= 0) {
      messageApi.error('المجموع الفرعي يجب أن يكون أكبر من صفر. تأكد من إضافة أصناف بكميات وأسعار صحيحة.')
      return { success: false, message: 'المجموع الفرعي يجب أن يكون أكبر من صفر' }
    }

    if (totalAmount <= 0) {
      messageApi.error('إجمالي الفاتورة يجب أن يكون أكبر من صفر')
      return { success: false, message: 'إجمالي الفاتورة يجب أن يكون أكبر من صفر' }
    }

    // تحديث المجموع الفرعي في البيانات المرسلة
    values.subtotal = calculatedSubtotal

    // التحقق من منطقية المبالغ
    const expectedTotal = calculatedSubtotal + taxAmount - discountAmount
    if (Math.abs(totalAmount - expectedTotal) > 0.01) {
      messageApi.error('هناك خطأ في حساب المجاميع. يرجى إعادة حساب المبالغ')
      return { success: false, message: 'خطأ في حساب المجاميع' }
    }

    try {
      if (window.electronAPI) {
        const invoiceData = {
          ...values,
          customer_id: parseInt(String(values.customer_id)), // تحويل معرف العميل إلى رقم صحيح
          invoice_date: values.invoice_date ? values.invoice_date.format('YYYY-MM-DD') : null,
          due_date: values.due_date ? values.due_date.format('YYYY-MM-DD') : null,
          total_amount: parseFloat(values.total_amount) || 0,
          discount_amount: parseFloat(values.discount_amount) || 0,
          tax_amount: parseFloat(values.tax_amount) || 0,
          final_amount: parseFloat(values.total_amount) || 0,
          paid_amount: parseFloat(values.paid_amount) || 0,
          items: invoiceItems.map(item => ({
            ...item,
            item_id: parseInt(String(item.item_id)),
            warehouse_id: parseInt(String(item.warehouse_id)),
            quantity: parseFloat(String(item.quantity)),
            unit_price: parseFloat(String(item.unit_price)),
            total_price: parseFloat(String(item.total_price))
          })),
          created_by: userId
        }

        let response
        if (editingInvoice) {
          response = await window.electronAPI.updateSalesInvoice(editingInvoice.id, invoiceData)
        } else {
          response = await window.electronAPI.createSalesInvoice(invoiceData)
        }

        if (response.success) {
          const successMessage = editingInvoice ? 'تم تحديث فاتورة البيع بنجاح' : 'تم إنشاء فاتورة البيع بنجاح'
          messageApi.success(successMessage)
          Logger.info('SalesInvoiceManagement', `✅ ${successMessage}`)
          setModalVisible(false)
          form.resetFields()
          setEditingInvoice(null)
          setInvoiceItems([])
          setItemsByWarehouse({}) // إعادة تعيين الأصناف حسب المخزن
          setSelectedCustomerId(null) // تنّيف معرف العميل المختار
          setAvailableOrders([]) // تنّيف أوامر البيع المتاحة
          loadInvoices()
          return { success: true, data: response.data }
        } else {
          let errorMessage = response.message || 'فشل في حفّ فاتورة البيع'

          // معالجة خاصة لخطأ رقم الفاتورة المكرر
          if (errorMessage.includes('UNIQUE constraint failed') || errorMessage.includes('invoice_number')) {
            errorMessage = 'رقم الفاتورة موجود مسبقاً. يرجى الضغط على زر "توليد" لإنشاء رقم جديد.'
            // إعادة تعيين رقم الفاتورة
            form.setFieldsValue({ invoice_number: '' })
          }

          Logger.error('SalesInvoiceManagement', '❌ خطأ من الخادم:', response)
          messageApi.error(errorMessage)
          return { success: false, message: errorMessage }
        }
      }
      const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى التأكد من تشغيل التطبيق بشكل صحيح.'
      Logger.error('SalesInvoiceManagement', '❌ window.electronAPI غير متوفر')
      messageApi.error(errorMessage)
      return { success: false, message: errorMessage }
    } catch (error) {
      Logger.error('SalesInvoiceManagement', '❌ خطأ في حفّ فاتورة البيع:', error instanceof Error ? error : new Error(String(error)))
      let errorMessage = 'حدث خطأ غير متوقع أثناء حفّ فاتورة البيع'

      if (error instanceof Error) {
        if (error.message.includes('UNIQUE constraint failed')) {
          if (error.message.includes('invoice_number')) {
            errorMessage = 'رقم الفاتورة موجود مسبقاً. يرجى الضغط على زر "توليد" لإنشاء رقم جديد.'
            form.setFieldsValue({ invoice_number: '' })
          } else {
            errorMessage = 'يوجد بيانات مكررة. يرجى التحقق من البيانات المدخلة.'
          }
        } else if (error.message.includes('NOT NULL constraint failed')) {
          if (error.message.includes('warehouse_id')) {
            errorMessage = 'يجب اختيار المخزن لجميع الأصناف.'
          } else if (error.message.includes('customer_id')) {
            errorMessage = 'يجب اختيار العميل.'
          } else if (error.message.includes('item_id')) {
            errorMessage = 'يجب اختيار الأصناف لجميع الأسطر.'
          } else {
            errorMessage = 'يوجد بيانات مطلوبة مفقودة. يرجى التحقق من جميع الحقول.'
          }
        } else if (error.message.includes('FOREIGN KEY constraint failed')) {
          errorMessage = 'خطأ في ربط البيانات. يرجى التأكد من صحة بيانات العميل والأصناف والمخازن.'
        } else if (error.message.includes('كمية غير كافية')) {
          errorMessage = error.message
        } else {
          errorMessage = error.message
        }
      }

      messageApi.error(errorMessage)
      return { success: false, message: errorMessage }
    }
  }

  const handleEdit = async (invoice: SalesInvoice) => {
    setEditingInvoice(invoice)
    setModalVisible(true)

    // تأخير تعيين قيم النموذج حتى بعد فتح المودال لتجنب تحذير useForm
    setTimeout(() => {
      form.setFieldsValue({
        ...invoice,
        invoice_date: dayjs(invoice.invoice_date),
        due_date: invoice.due_date ? dayjs(invoice.due_date) : null
      })
    }, 100)

    // تحميل عناصر الفاتورة
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getSalesInvoiceItems(invoice.id)
        if (response.success) {
          const items = response.data.map((item: any) => ({
            id: item.id,
            item_id: item.item_id,
            item_name: item.item_name,
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.total_price,
            warehouse_id: item.warehouse_id, // إضافة warehouse_id المفقود
            notes: item.notes || ''
          }))
          setInvoiceItems(items)

          // تحميل الأصناف لكل مخزن مستخدم في الفاتورة
          const uniqueWarehouseIds = [...new Set(items.map(item => item.warehouse_id).filter(id => id))]
          for (const warehouseId of uniqueWarehouseIds) {
            await loadItemsByWarehouse(Number(warehouseId))
          }
        }
      }
    } catch (error) {
      Logger.error('SalesInvoiceManagement', 'خطأ في تحميل عناصر الفاتورة:', error instanceof Error ? error : new Error(String(error)))
    }
  }

  const handleDelete = async (invoiceId: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.deleteSalesInvoice(invoiceId)
        if (response.success) {
          messageApi.success('تم حذف فاتورة البيع بنجاح')
          loadInvoices()
        } else {
          messageApi.error(response.message || 'فشل في حذف فاتورة البيع')
        }
      }
    } catch (error) {
      messageApi.error('حدث خطأ أثناء حذف فاتورة البيع')
    }
  }

  const updateInvoiceStatus = async (invoiceId: number, status: string) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.updateSalesInvoiceStatus(invoiceId, status)
        if (response.success) {
          messageApi.success('تم تحديث حالة الفاتورة بنجاح')
          loadInvoices()
        } else {
          messageApi.error(response.message || 'فشل في تحديث حالة الفاتورة')
        }
      }
    } catch (error) {
      Logger.error('SalesInvoiceManagement', 'خطأ في تحديث حالة الفاتورة:', error instanceof Error ? error : new Error(String(error)))
      messageApi.error('حدث خطأ أثناء تحديث حالة الفاتورة')
    }
  }

  const calculateTotals = () => {
    // تحديث total_price لكل صنف أولاً بدون إعادة إنشاء العناصر
    const updatedItems = invoiceItems.map(item => {
      const newItem = { ...item }
      newItem.total_price = (item.quantity || 0) * (item.unit_price || 0)
      return newItem
    })

    // تحديث قائمة الأصناف فقط إذا كان هناك تغيير في المجاميع
    const hasChanges = updatedItems.some((item, index) =>
      item.total_price !== invoiceItems[index].total_price
    )

    if (hasChanges) {
      setInvoiceItems(updatedItems)
    }

    const subtotal = updatedItems.reduce((sum, item) => {
      return sum + item.total_price
    }, 0)

    const discountAmount = form.getFieldValue('discount_amount') || 0
    const totals = calculateInvoiceTotals(subtotal, discountAmount)
    const paidAmount = form.getFieldValue('paid_amount') || 0
    const remainingAmount = totals.totalAmount - paidAmount

    form.setFieldsValue({
      subtotal: totals.subtotal,
      tax_amount: totals.taxAmount,
      total_amount: totals.totalAmount,
      remaining_amount: Math.round(remainingAmount * 100) / 100
    })
  }

  const handleAdd = () => {
    Logger.info('SalesInvoiceManagement', '🆕 إنشاء فاتورة جديدة - مسح العناصر')
    setEditingInvoice(null)
    form.resetFields()
    form.setFieldsValue({
      invoice_date: dayjs(),
      status: 'draft',
      subtotal: 0,
      tax_amount: 0,
      discount_amount: 0,
      total_amount: 0,
      payment_type: 'cash',
      paid_amount: 0,
      remaining_amount: 0
    })
    // لا نولد رقم فاتورة تلقائياً، سيقوم المستخدم بالضغط على زر "توليد"
    setInvoiceItems([])
    setItemsByWarehouse({}) // إعادة تعيين الأصناف حسب المخزن
    setSelectedCustomerId(null)
    setAvailableOrders([])
    setModalVisible(true)
  }

  const addInvoiceItem = () => {
    Logger.info('SalesInvoiceManagement', '➕ إضافة عنصر جديد للفاتورة')
    const newItem: SalesInvoiceItem = {
      item_id: 0,
      item_name: '',
      quantity: 1,
      unit_price: 0,
      total_price: 0,
      warehouse_id: 0, // يجب اختيار المخزن أولاً
      notes: ''
    }
    const newItems = [...invoiceItems, newItem]
    Logger.info('SalesInvoiceManagement', '➕ العناصر الجديدة:', newItems)
    setInvoiceItems(newItems)
    // لا نحتاج لإعادة تعيين قائمة الأصناف المفلترة هنا
    // setFilteredItems([])
  }

  // التحقق من توفر الكمية في المخزن
  const checkItemAvailability = async (itemId: number, warehouseId: number, quantity: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.checkItemAvailability(itemId, warehouseId, quantity)
        return response
      }
    } catch (error) {
      Logger.error('SalesInvoiceManagement', 'خطأ في التحقق من توفر الصنف:', error instanceof Error ? error : new Error(String(error)))
      return { success: false, available: false, message: 'خطأ في التحقق من توفر الصنف' }
    }
    return { success: false, available: false, message: 'خطأ في الاتصال' }
  }

  const updateInvoiceItem = async (index: number, field: keyof SalesInvoiceItem, value: any) => {
    Logger.info('SalesInvoiceManagement', '🔄 تحديث عنصر الفاتورة - الفهرس: ${index}, الحقل: ${field}, القيمة:', value)
    Logger.info('SalesInvoiceManagement', '📋 العناصر قبل التحديث:', invoiceItems)

    const updatedItems = [...invoiceItems]
    updatedItems[index] = { ...updatedItems[index], [field]: value }

    Logger.info('SalesInvoiceManagement', '📝 العنصر بعد التحديث [${index}]:', updatedItems[index])

    if (field === 'warehouse_id') {
      Logger.info('SalesInvoiceManagement', `🏪 تغيير المخزن للعنصر ${index} إلى المخزن ${value}`)

      // الحفاّ على المخزن المختار وإعادة تعيين الصنف فقط
      const previousWarehouseId = updatedItems[index].warehouse_id
      updatedItems[index].warehouse_id = value || 0
      updatedItems[index].item_id = 0
      updatedItems[index].item_name = ''
      updatedItems[index].unit_price = 0
      updatedItems[index].total_price = 0

      // تحديث الحالة فوراً
      setInvoiceItems(updatedItems)
      Logger.info('SalesInvoiceManagement', '📝 تم تحديث العناصر بعد تغيير المخزن:', updatedItems)

      // تحميل الأصناف للمخزن الجديد إذا كان مختلفاً
      if (value && value > 0 && value !== previousWarehouseId) {
        Logger.info('SalesInvoiceManagement', `🔄 بدء تحميل الأصناف للمخزن ${value}`)
        await loadItemsByWarehouse(value)
        Logger.info('SalesInvoiceManagement', `✅ انتهى تحميل الأصناف للمخزن ${value}`)
      } else if (!value || value === 0) {
        // إذا تم إلغاء اختيار المخزن، لا نحتاج لتحميل أصناف
        Logger.info('SalesInvoiceManagement', `🚫 تم إلغاء اختيار المخزن للعنصر ${index}`)
      }

      // إنهاء الدالة هنا لتجنب التحديث المزدوج
      return
    }

    if (field === 'item_id') {
      const warehouseId = updatedItems[index].warehouse_id
      Logger.info('SalesInvoiceManagement', `📦 تغيير الصنف للعنصر ${index} إلى الصنف ${value} في المخزن ${warehouseId}`)
      Logger.info('SalesInvoiceManagement', `📋 الأصناف المتاحة في المخزن ${warehouseId}:`, itemsByWarehouse[warehouseId])

      if (!warehouseId || warehouseId === 0) {
        Logger.info('SalesInvoiceManagement', `❌ يجب اختيار المخزن أولاً للعنصر ${index}`)
        messageApi.warning('يرجى اختيار المخزن أولاً')
        return
      }

      const warehouseItems = itemsByWarehouse[warehouseId] || []
      const selectedItem = warehouseItems.find(item => item.id === value)
      Logger.info('SalesInvoiceManagement', '🔍 الصنف المختار:', selectedItem)

      if (selectedItem) {
        updatedItems[index].item_id = value
        updatedItems[index].item_name = selectedItem.name
        updatedItems[index].unit_price = selectedItem.sale_price || 0
        // إعادة حساب المجموع عند اختيار الصنف مع الكمية الحالية
        const currentQuantity = updatedItems[index].quantity || 1
        updatedItems[index].total_price = currentQuantity * (selectedItem.sale_price || 0)
        Logger.info('SalesInvoiceManagement', '✅ تم تحديث بيانات الصنف:', updatedItems[index])
      } else {
        Logger.info('SalesInvoiceManagement', `❌ لم يتم العثور على الصنف ${value} في المخزن ${warehouseId}`)
        // في حالة عدم العثور على الصنف، إعادة تعيين البيانات
        updatedItems[index].item_id = 0
        updatedItems[index].item_name = ''
        updatedItems[index].unit_price = 0
        updatedItems[index].total_price = 0
        messageApi.error('الصنف المختار غير متوفر في هذا المخزن')
      }
    }

    if (field === 'quantity' || field === 'unit_price') {
      updatedItems[index].total_price = updatedItems[index].quantity * updatedItems[index].unit_price

      // التحقق من توفر الكمية عند تغيير الكمية
      if (field === 'quantity' && updatedItems[index].item_id && updatedItems[index].warehouse_id && value > 0) {
        const availability = await checkItemAvailability(updatedItems[index].item_id, updatedItems[index].warehouse_id, value)
        if (availability.success && 'available' in availability && !availability.available) {
          messageApi.warning(availability.message || 'الكمية المطلوبة غير متوفرة')
        }
      }
    }

    // تحديث الحالة للحقول الأخرى
    setInvoiceItems(updatedItems)
    Logger.info('SalesInvoiceManagement', '✅ تم حفّ العناصر المحدثة:', updatedItems)

    // حساب المجاميع مباشرة باستخدام البيانات المحدثة
    setTimeout(() => {
      const validItems = updatedItems.filter(item => item.item_id && item.quantity > 0 && item.unit_price > 0)
      const subtotal = validItems.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0)
      const discountAmount = form.getFieldValue('discount_amount') || 0
      const totals = calculateInvoiceTotals(subtotal, discountAmount)

      form.setFieldsValue({
        subtotal: totals.subtotal,
        tax_amount: totals.taxAmount,
        total_amount: totals.totalAmount
      })
    }, 50)
  }

  const removeInvoiceItem = (index: number) => {
    const updatedItems = invoiceItems.filter((_, i) => i !== index)
    setInvoiceItems(updatedItems)

    // تحديث المجاميع الإجمالية تلقائياً
    setTimeout(calculateTotals, 100)
  }

  // تحميل مدفوعات الفاتورة
  const loadInvoicePayments = async (invoiceId: number) => {
    setLoadingPayments(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getInvoicePayments(invoiceId, 'sales')
        if (response.success) {
          setInvoicePayments(response.data || [])
        }
      }
    } catch (error) {
      Logger.error('SalesInvoiceManagement', 'خطأ في تحميل المدفوعات:', error instanceof Error ? error : new Error(String(error)))
    } finally {
      setLoadingPayments(false)
    }
  }

  // فتح نافذة الدفع
  const showPaymentModal = (invoice: SalesInvoice) => {
    setSelectedInvoiceForPayment(invoice)
    setPaymentModalVisible(true)
    paymentForm.resetFields()
    paymentForm.setFieldsValue({
      payment_date: dayjs(),
      payment_method: PAYMENT_METHODS.CASH
    })
    setSelectedPaymentMethod(PAYMENT_METHODS.CASH)
    loadInvoicePayments(invoice.id)
  }

  // معالجة الدفع
  const handlePayment = async (values: any) => {
    if (!selectedInvoiceForPayment) {
      messageApi.error('لم يتم اختيار فاتورة للدفع')
      return
    }

    // التحقق من صحة بيانات الدفع
    if (!values.amount || values.amount <= 0) {
      messageApi.error('مبلغ الدفعة يجب أن يكون أكبر من صفر')
      return
    }

    if (values.amount > selectedInvoiceForPayment.remaining_amount) {
      messageApi.error(`مبلغ الدفعة لا يمكن أن يكون أكبر من المبلغ المتبقي (${formatCurrency(selectedInvoiceForPayment.remaining_amount)})`)
      return
    }

    if (!values.payment_date) {
      messageApi.error('تاريخ الدفع مطلوب')
      return
    }

    // التحقق من البيانات الإضافية حسب طريقة الدفع
    if (selectedPaymentMethod === PAYMENT_METHODS.CHECK) {
      if (!values.check_number || values.check_number.trim() === '') {
        messageApi.error('رقم الشيك مطلوب')
        return
      }
      if (!values.due_date) {
        messageApi.error('تاريخ استحقاق الشيك مطلوب')
        return
      }
    }

    if (selectedPaymentMethod === PAYMENT_METHODS.BANK_TRANSFER) {
      if (!values.reference_number || values.reference_number.trim() === '') {
        messageApi.error('رقم مرجع التحويل مطلوب')
        return
      }
    }

    try {
      const paymentData = {
        invoice_id: selectedInvoiceForPayment.id,
        invoice_type: 'sales_invoice',
        customer_id: selectedInvoiceForPayment.customer_id,
        amount: values.amount,
        payment_method: selectedPaymentMethod,
        payment_date: values.payment_date.format('YYYY-MM-DD'),
        reference_number: values.reference_number || values.check_number || values.voucher_number,
        notes: values.notes,
        // بيانات إضافية حسب طريقة الدفع
        check_number: values.check_number,
        bank_name: values.bank_name,
        due_date: values.due_date ? values.due_date.format('YYYY-MM-DD') : null,
        bank_account_id: values.bank_account_id,
        created_by: userId
      }

      if (window.electronAPI) {
        // محاولة استخدام المعالج الأساسي أولاً
        let response = await window.electronAPI.createCustomerPayment(paymentData)

        // إذا فشل المعالج الأساسي، استخدم المعالج الطارئ
        if (!response.success && response.message?.includes('No handler registered')) {
          Logger.warn('SalesInvoiceManagement', 'المعالج الأساسي غير متاح، استخدام المعالج الطارئ')
          response = await (window.electronAPI as any).emergencyCreateCustomerPayment(paymentData)
        }

        if (response.success) {
          messageApi.success('تم تسجيل الدفعة بنجاح')
          setPaymentModalVisible(false)
          paymentForm.resetFields()
          loadInvoices() // إعادة تحميل الفواتير لتحديث المبالغ
          loadInvoicePayments(selectedInvoiceForPayment.id) // تحديث المدفوعات
        } else {
          const errorMessage = response.message || 'فشل في تسجيل الدفعة'
          messageApi.error(errorMessage)
          Logger.error('SalesInvoiceManagement', 'خطأ من الخادم:', new Error(JSON.stringify(response)))
        }
      } else {
        messageApi.error('لا يمكن الوصول إلى قاعدة البيانات')
      }
    } catch (error) {
      Logger.error('SalesInvoiceManagement', 'خطأ في تسجيل الدفعة:', error instanceof Error ? error : new Error(String(error)))
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تسجيل الدفعة'
      messageApi.error(errorMessage)
    }
  }

  const filteredInvoices = invoices.filter(invoice =>
    invoice.invoice_number.toLowerCase().includes(searchText.toLowerCase()) ||
    invoice.customer_name.toLowerCase().includes(searchText.toLowerCase())
  )

  // تشخيص البيانات
  console.log('🔍 تشخيص فواتير المبيعات:', {
    totalInvoices: invoices.length,
    filteredInvoices: filteredInvoices.length,
    loading,
    searchText
  })

  // دالة تصدير Excel محسنة ومصححة لحل مشاكل فتح الملف
  const handleExportExcel = async () => {
    try {
      if (invoices.length === 0) {
        messageApi.warning('لا توجد فواتير مبيعات للتصدير')
        return
      }

      // تحضير البيانات للتصدير مع تنظيف وتنسيق صحيح
      const exportData = invoices.map(invoice => ({
        'Invoice_Number': invoice.invoice_number || '',
        'Customer_Name': invoice.customer_name || '',
        'Invoice_Date': invoice.invoice_date ? new Date(invoice.invoice_date).toISOString().split('T')[0] : '',
        'Due_Date': invoice.due_date ? new Date(invoice.due_date).toISOString().split('T')[0] : '',
        'Status': invoice.status || '',
        'Total_Amount': invoice.total_amount || 0,
        'Paid_Amount': invoice.paid_amount || 0,
        'Remaining_Amount': (invoice.total_amount || 0) - (invoice.paid_amount || 0),
        'Notes': invoice.notes || '',
        'Created_By': (invoice as any).created_by_name || invoice.created_by || '',
        'Creation_Date': invoice.created_at ? new Date(invoice.created_at).toISOString().split('T')[0] : ''
      }))

      // إنشاء workbook جديد مع إعدادات محسنة
      const workbook = XLSX.utils.book_new()

      // تعيين خصائص الـ workbook
      workbook.Props = {
        Title: 'Sales Invoices Report',
        Subject: 'Sales Invoice Management System Export',
        Author: 'ZET.IA Accounting System',
        CreatedDate: new Date()
      }

      // إنشاء ورقة العمل مع تنسيق محسن
      const worksheet = XLSX.utils.json_to_sheet(exportData, {
        header: [
          'Invoice_Number',
          'Customer_Name',
          'Invoice_Date',
          'Due_Date',
          'Status',
          'Total_Amount',
          'Paid_Amount',
          'Remaining_Amount',
          'Notes',
          'Created_By',
          'Creation_Date'
        ]
      })

      // تعيين عرض الأعمدة
      worksheet['!cols'] = [
        { wch: 15 }, // Invoice_Number
        { wch: 25 }, // Customer_Name
        { wch: 12 }, // Invoice_Date
        { wch: 12 }, // Due_Date
        { wch: 12 }, // Status
        { wch: 15 }, // Total_Amount
        { wch: 15 }, // Paid_Amount
        { wch: 15 }, // Remaining_Amount
        { wch: 30 }, // Notes
        { wch: 20 }, // Created_By
        { wch: 15 }  // Creation_Date
      ]

      // إضافة الورقة إلى workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sales_Invoices')

      // إضافة ورقة معلومات التقرير بتنسيق محسن
      const reportData = [
        ['Sales Invoices Report Summary'],
        [''],
        ['Report Date', new Date().toISOString().split('T')[0]],
        ['Total Invoices', invoices.length],
        ['Draft Invoices', invoices.filter(i => i.status === 'draft').length],
        ['Paid Invoices', invoices.filter(i => i.status === 'paid').length],
        ['Total Sales', invoices.reduce((sum, i) => sum + (i.total_amount || 0), 0)],
        ['Total Paid', invoices.reduce((sum, i) => sum + (i.paid_amount || 0), 0)],
        ['Total Remaining', invoices.reduce((sum, i) => sum + ((i.total_amount || 0) - (i.paid_amount || 0)), 0)]
      ]

      const summaryWorksheet = XLSX.utils.aoa_to_sheet(reportData)
      summaryWorksheet['!cols'] = [{ wch: 25 }, { wch: 20 }]
      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'Summary')

      // تحديد اسم الملف بتنسيق آمن
      const timestamp = new Date().toISOString().split('T')[0]
      const fileName = `Sales_Invoices_Report_${timestamp}.xlsx`

      // استخدام Electron API لحفظ الملف
      if (window.electronAPI && window.electronAPI.saveExcelFile) {
        // تحويل workbook إلى buffer مع إعدادات محسنة
        const buffer = XLSX.write(workbook, {
          type: 'buffer',
          bookType: 'xlsx',
          compression: true,
          Props: {
            Title: 'Sales Invoices Report',
            Subject: 'Export from ZET.IA System'
          }
        })

        const result = await window.electronAPI.saveExcelFile(buffer, fileName)

        if (result.success) {
          messageApi.success(`تم تصدير ${invoices.length} فاتورة مبيعات بنجاح إلى ملف Excel`)
        } else {
          messageApi.error(result.message || 'فشل في حفظ الملف')
        }
      } else {
        // fallback للمتصفح العادي
        XLSX.writeFile(workbook, fileName, {
          compression: true,
          Props: {
            Title: 'Sales Invoices Report',
            Subject: 'Export from ZET.IA System'
          }
        })
        messageApi.success(`تم تصدير ${invoices.length} فاتورة مبيعات بنجاح إلى ملف Excel`)
      }
    } catch (error) {
      Logger.error('SalesInvoiceManagement', 'خطأ في تصدير Excel:', error)
      messageApi.error('حدث خطأ أثناء تصدير البيانات: ' + (error as Error).message)
    }
  }

  // دالة للحصول على نص الحالة
  const _getStatusText = (status: string) => {
    switch (status) {
      case 'draft': return 'مسودة'
      case 'confirmed': return 'مؤكدة'
      case 'paid': return 'مدفوعة'
      case 'partially_paid': return 'مدفوعة جزئياً'
      case 'cancelled': return 'ملغية'
      default: return status
    }
  }

  // تحديث الإحصائيات عند تغيير الفواتير
  React.useEffect(() => {
    setStats({
      totalInvoices: invoices.length,
      draftInvoices: invoices.filter(i => i.status === 'draft').length,
      paidInvoices: invoices.filter(i => i.status === 'paid').length,
      totalAmount: invoices.reduce((sum, i) => sum + i.total_amount, 0),
      paidAmount: invoices.reduce((sum, i) => sum + i.paid_amount, 0),
      remainingAmount: invoices.reduce((sum, i) => sum + i.remaining_amount, 0)
    })
  }, [invoices])





  const columns = [
    {
      title: 'رقم الفاتورة',
      dataIndex: 'invoice_number',
      key: 'invoice_number',
      width: 120,
      render: (invoiceNumber: string) => <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{invoiceNumber}</span>
    },
    {
      title: 'العميل',
      dataIndex: 'customer_name',
      key: 'customer_name',
      render: (name: string) => (
        <Space>
          <UserOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontWeight: 'bold' }}>{name}</span>
        </Space>
      )
    },
    {
      title: 'تاريخ الفاتورة',
      dataIndex: 'invoice_date',
      key: 'invoice_date',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'تاريخ الاستحقاق',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : '-'
    },
    {
      title: 'حالة الدفع',
      key: 'payment_status',
      width: 150,
      render: (_, record: SalesInvoice) => (
        <PaymentStatusTag
          totalAmount={record.total_amount || 0}
          paidAmount={record.paid_amount || 0}
          dueDate={record.due_date}
          size="small"
        />
      )
    },
    {
      title: 'المبالغ',
      key: 'amounts',
      width: 200,
      render: (_, record: SalesInvoice) => (
        <RemainingAmountDisplay
          totalAmount={record.total_amount || 0}
          paidAmount={record.paid_amount || 0}
          dueDate={record.due_date}
          size="small"
          showProgress={false}
        />
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 200,
      render: (_, record: SalesInvoice) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            title="تعديل"
          />
          {record.remaining_amount > 0 && (
            <Button
              type="default"
              size="small"
              icon={<CreditCardOutlined />}
              onClick={() => showPaymentModal(record)}
              title="تسجيل دفعة"
              style={{ color: '#52c41a', borderColor: '#52c41a' }}
            />
          )}
          <Select
            size="small"
            value={record.status}
            onChange={(status) => updateInvoiceStatus(record.id, status)}
            style={{ width: 100 }}
          >
            <Select.Option value="pending">معلقة</Select.Option>
            <Select.Option value="paid">مدفوعة</Select.Option>
            <Select.Option value="partial">جزئية</Select.Option>
            <Select.Option value="overdue">متأخرة</Select.Option>
            <Select.Option value="cancelled">ملغية</Select.Option>
          </Select>
          <InvoicePrintButton
            invoiceData={{
              id: record.id,
              invoiceNumber: record.invoice_number,
              invoiceDate: record.invoice_date,
              dueDate: record.due_date,
              customerName: record.customer_name || '',
              customerAddress: record.customer_address || '',
              customerPhone: record.customer_phone || '',
              items: [], // سيتم تحميل الأصناف عند الطباعة
              subtotal: record.subtotal || 0,
              discount: record.discount_amount || 0,
              tax: record.tax_amount || 0,
              total: record.total_amount || 0,
              paid: record.paid_amount || 0,
              remaining: (record.total_amount || 0) - (record.paid_amount || 0),
              notes: record.notes || ''
            }}
            invoiceType="sales"
            size="small"
            buttonText="طباعة"
            loadInvoiceItems={async () => {
              // تحميل عناصر الفاتورة عند الطباعة
              try {
                if (window.electronAPI) {
                  const response = await window.electronAPI.getSalesInvoiceItems(record.id)
                  if (response.success) {
                    return response.data.map((item: any) => {
                      const quantity = item.quantity || 0
                      const unitPrice = item.unit_price || 0
                      const calculatedTotal = quantity * unitPrice

                      return {
                        id: item.id,
                        name: item.item_name || 'غير محدد',
                        description: item.notes || '',
                        quantity: quantity,
                        unit: 'قطعة',
                        unitPrice: unitPrice,
                        discount: 0,
                        tax: 0,
                        // التأكد من أن المجموع الفرعي = الكمية × سعر الوحدة
                        total: item.total_price || calculatedTotal
                      }
                    })
                  }
                }
                return []
              } catch (error) {
                Logger.error('SalesInvoiceManagement', 'خطأ في تحميل عناصر الفاتورة للطباعة:', error)
                return []
              }
            }}
            onPrintSuccess={() => messageApi.success('تم طباعة الفاتورة بنجاح')}
            onPrintError={() => messageApi.error('فشل في طباعة الفاتورة')}
          />
          <Popconfirm
            title="هل أنت متأكد من حذف هذه الفاتورة؟"
            description={`سيتم حذف الفاتورة رقم ${record.invoice_number} نهائياً ولا يمكن التراجع عن هذا الإجراء.`}
            onConfirm={() => handleDelete(record.id)}
            okText="نعم، احذف"
            cancelText="إلغاء"
            okType="danger"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
              title="حذف"
            />
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h1 style={{ margin: 0, color: '#1890ff', fontSize: '28px' }}>
            <FileTextOutlined style={{ marginLeft: '12px' }} />
            إدارة فواتير البيع
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '16px' }}>
            إدارة شاملة لفواتير البيع ومتابعة المدفوعات
          </p>
        </div>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
          size="large"
        >
          رجوع
        </Button>
      </div>

      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي الفواتير"
              value={stats.totalInvoices}
              valueStyle={{ color: '#1890ff' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="مسودات"
              value={stats.draftInvoices}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="مدفوعة"
              value={stats.paidInvoices}
              valueStyle={{ color: '#52c41a' }}
              prefix={<DollarOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي المبيعات"
              value={stats.totalAmount}
              valueStyle={{ color: '#722ed1' }}
              prefix={getCurrencySymbol()}
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="المبلغ المدفوع"
              value={stats.paidAmount}
              valueStyle={{ color: '#52c41a' }}
              prefix={getCurrencySymbol()}
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="المتبقي للتحصيل"
              value={stats.remainingAmount}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={getCurrencySymbol()}
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      {/* ملخص المدفوعات */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <PaymentSummary
            invoices={invoices.map(invoice => ({
              totalAmount: invoice.total_amount,
              paidAmount: invoice.paid_amount,
              dueDate: invoice.due_date
            }))}
            title="ملخص فواتير المبيعات"
          />
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              size="large"
            >
              فاتورة بيع جديدة
            </Button>
            <Button
              type="default"
              icon={<DownloadOutlined />}
              onClick={handleExportExcel}
              size="large"
            >
              تصدير Excel
            </Button>

            <Input
              placeholder="البحث في الفواتير..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 300 }}
            />
          </Space>
        </div>



        <Table
          columns={columns}
          dataSource={filteredInvoices}
          rowKey="id"
          loading={loading}
          pagination={{
            total: filteredInvoices.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} من ${total} فاتورة`
          }}
          scroll={{ x: 1600 }}
          locale={{
            emptyText: filteredInvoices.length === 0 ? 'لا توجد فواتير مبيعات' : 'لا توجد بيانات'
          }}
        />
      </Card>

      <Modal
        title={editingInvoice ? 'تعديل فاتورة البيع' : 'فاتورة بيع جديدة'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
          setEditingInvoice(null)
          setInvoiceItems([])
          setItemsByWarehouse({}) // إعادة تعيين الأصناف حسب المخزن
          setSelectedCustomerId(null) // تنّيف معرف العميل المختار
          setAvailableOrders([]) // تنّيف أوامر البيع المتاحة
        }}
        footer={null}
        width={1200}

      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            invoice_date: dayjs(),
            status: 'draft',
            subtotal: 0,
            tax_amount: 0,
            discount_amount: 0,
            total_amount: 0
          }}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="invoice_number"
                label="رقم الفاتورة"
                rules={[{ required: true, message: 'يرجى إدخال رقم الفاتورة' }]}
              >
                <Input
                  placeholder="رقم الفاتورة"
                  addonAfter={
                    <Button
                      type="link"
                      size="small"
                      onClick={generateInvoiceNumber}
                      style={{ padding: 0 }}
                    >
                      توليد
                    </Button>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="customer_id"
                label="العميل"
                rules={[{ required: true, message: 'يرجى اختيار العميل' }]}
              >
                <Select
                  placeholder="اختر العميل"
                  showSearch
                  optionFilterProp="children"
                  onChange={(customerId) => {
                    setSelectedCustomerId(customerId)
                    loadAvailableOrders(customerId)
                    // مسح أمر البيع المختار عند تغيير العميل
                    form.setFieldsValue({ order_id: undefined })
                  }}
                >
                  {customers.map(customer => (
                    <Option key={customer.id} value={customer.id}>
                      {customer.name} ({customer.code})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="status"
                label="الحالة"
                rules={[{ required: true, message: 'يرجى اختيار الحالة' }]}
              >
                <Select placeholder="اختر الحالة">
                  <Option value="draft">مسودة</Option>
                  <Option value="sent">مرسلة</Option>
                  <Option value="paid">مدفوعة</Option>
                  <Option value="overdue">متأخرة</Option>
                  <Option value="cancelled">ملغية</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="invoice_date"
                label="تاريخ الفاتورة"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الفاتورة' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="due_date"
                label="تاريخ الاستحقاق"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="order_id"
                label="أمر البيع (اختياري)"
              >
                <Select
                  placeholder="اختر أمر البيع"
                  allowClear
                  disabled={!selectedCustomerId}
                  onChange={(orderId) => {
                    if (orderId) {
                      // تحميل عناصر أمر البيع المختار
                      const selectedOrder = availableOrders.find(order => order.id === orderId)
                      if (selectedOrder) {
                        messageApi.info(`تم اختيار أمر البيع: ${selectedOrder.order_number}`)
                      }
                    }
                  }}
                >
                  {availableOrders.map(order => (
                    <Option key={order.id} value={order.id}>
                      {order.order_number} - {order.customer_name} ({formatCurrency(order.total_amount || 0)})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Divider>أصناف الفاتورة</Divider>

          <div style={{ marginBottom: '16px' }}>
            <Button
              type="dashed"
              icon={<PlusOutlined />}
              onClick={addInvoiceItem}
              style={{ width: '100%' }}
            >
              إضافة صنف
            </Button>
          </div>

          {/* عناوين الأعمدة */}
          {invoiceItems.length > 0 && (
            <Row gutter={8} style={{ marginBottom: '8px', fontWeight: 'bold', color: '#666' }}>
              <Col span={5}>الصنف</Col>
              <Col span={3}>المخزن</Col>
              <Col span={2}>الكمية</Col>
              <Col span={3}>سعر الوحدة</Col>
              <Col span={3}>المجموع</Col>
              <Col span={3}>ملاحّات</Col>
              <Col span={2}>إجراءات</Col>
            </Row>
          )}

          {invoiceItems.map((item, index) => (
            <Card key={`invoice-item-${index}-${item.warehouse_id}-${item.item_id}`} size="small" style={{ marginBottom: '8px' }}>
              <Row gutter={8} align="middle">
                <Col span={3}>
                  <Select
                    placeholder="اختر المخزن أولاً"
                    value={item.warehouse_id > 0 ? item.warehouse_id : undefined}
                    onChange={(value) => {
                      Logger.info('SalesInvoiceManagement', `🏪 تم اختيار المخزن ${value} للعنصر ${index}`)
                      updateInvoiceItem(index, 'warehouse_id', value)
                    }}
                    showSearch
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                    }
                    style={{ width: '100%' }}
                  >
                    {warehouses.map(warehouse => (
                      <Option key={warehouse.id} value={warehouse.id}>
                        {warehouse.name}
                      </Option>
                    ))}
                  </Select>
                </Col>
                <Col span={5}>
                  <Select
                    placeholder={item.warehouse_id > 0 ? "اختر الصنف" : "اختر المخزن أولاً"}
                    value={item.item_id || undefined}
                    onChange={(value) => {
                      Logger.info('SalesInvoiceManagement', `🎯 تم اختيار الصنف ${value} للعنصر ${index}`)
                      updateInvoiceItem(index, 'item_id', value)
                    }}
                    style={{ width: '100%' }}
                    showSearch
                    optionFilterProp="children"
                    notFoundContent="لا توجد أصناف متاحة"
                    onOpenChange={async (open) => {
                      if (open && item.warehouse_id > 0) {
                        Logger.info('SalesInvoiceManagement', `📋 فتح قائمة الأصناف للعنصر ${index}, المخزن: ${item.warehouse_id}`)
                        Logger.info('SalesInvoiceManagement', '📦 الأصناف المتاحة:', itemsByWarehouse[item.warehouse_id])

                        // تحميل الأصناف إذا لم تكن محملة مسبقاً
                        if (!itemsByWarehouse[item.warehouse_id] || itemsByWarehouse[item.warehouse_id].length === 0) {
                          Logger.info('SalesInvoiceManagement', '🔄 تحميل الأصناف للمخزن ${item.warehouse_id} عند فتح القائمة')
                          await loadItemsByWarehouse(item.warehouse_id)
                        }
                      }
                    }}
                  >
                    {(() => {
                      const warehouseItems = item.warehouse_id > 0 && itemsByWarehouse[item.warehouse_id] ? itemsByWarehouse[item.warehouse_id] : []
                      Logger.info('SalesInvoiceManagement', '🔍 عرض الأصناف للعنصر ${index}, المخزن ${item.warehouse_id}:', warehouseItems)
                      return warehouseItems.map(product => (
                        <Option key={product.id} value={product.id}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <span>{product.name} ({product.code})</span>
                            <span style={{ color: '#52c41a', fontWeight: 'bold', fontSize: '12px' }}>
                              {formatCurrency(product.sale_price || 0)}
                            </span>
                            {product.available_quantity !== undefined && (
                              <span style={{ color: '#1890ff', fontSize: '10px', marginLeft: '4px' }}>
                                متوفر: {product.available_quantity}
                              </span>
                            )}
                          </div>
                        </Option>
                      ))
                    })()}
                  </Select>
                </Col>
                <Col span={2}>
                  <InputNumber
                    placeholder="الكمية"
                    value={item.quantity}
                    onChange={(value) => updateInvoiceItem(index, 'quantity', value || 0)}
                    min={0}
                    style={{ width: '100%' }}
                  />
                </Col>
                <Col span={3}>
                  <InputNumber
                    placeholder="سعر الوحدة"
                    value={item.unit_price}
                    onChange={(value) => updateInvoiceItem(index, 'unit_price', value || 0)}
                    min={0}
                    style={{ width: '100%' }}
                    formatter={value => `${getCurrencySymbol()} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={value => parseFloat((value || '').replace(new RegExp(`${getCurrencySymbol()}\\s?|(,*)`,'g'), '')) || 0}
                  />
                </Col>
                <Col span={3}>
                  <InputNumber
                    placeholder="المجموع"
                    value={item.total_price}
                    disabled
                    style={{ width: '100%' }}
                    formatter={value => `${getCurrencySymbol()} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  />
                </Col>
                <Col span={3}>
                  <Input
                    placeholder="ملاحّات"
                    value={item.notes || ''}
                    onChange={(e) => updateInvoiceItem(index, 'notes', e.target.value)}
                  />
                </Col>
                <Col span={2}>
                  <Button
                    type="primary"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => removeInvoiceItem(index)}
                    size="small"
                  />
                </Col>
              </Row>
            </Card>
          ))}

          <Row gutter={16} style={{ marginTop: '24px' }}>
            <Col span={6}>
              <Form.Item name="subtotal" label="المجموع الفرعي">
                <InputNumber
                  style={{ width: '100%' }}
                  value={invoiceItems.reduce((sum: number, item) => sum + item.total_price, 0)}
                  disabled
                  formatter={value => `${getCurrencySymbol()} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="tax_amount" label="الضريبة">
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  onChange={() => setTimeout(calculateTotals, 100)}
                  formatter={value => `${getCurrencySymbol()} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => (value || '').replace(new RegExp(`${getCurrencySymbol()}\\s?|(,*)`,'g'), '') as any}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="discount_amount" label="الخصم">
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  onChange={() => setTimeout(calculateTotals, 100)}
                  formatter={value => `${getCurrencySymbol()} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => (value || '').replace(new RegExp(`${getCurrencySymbol()}\\s?|(,*)`,'g'), '') as any}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="total_amount" label="المجموع الإجمالي">
                <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
                  prevValues.tax_amount !== currentValues.tax_amount ||
                  prevValues.discount_amount !== currentValues.discount_amount
                }>
                  {({ getFieldValue }) => {
                    const subtotal = invoiceItems.reduce((sum, item) => sum + item.total_price, 0)
                    const taxAmount = getFieldValue('tax_amount') || 0
                    const discountAmount = getFieldValue('discount_amount') || 0
                    const total = subtotal + taxAmount - discountAmount

                    return (
                      <InputNumber
                        style={{ width: '100%' }}
                        value={total}
                        disabled
                        formatter={value => `${getCurrencySymbol()} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      />
                    )
                  }}
                </Form.Item>
              </Form.Item>
            </Col>
          </Row>

          <Divider>معلومات الدفع</Divider>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="payment_type"
                label="نوع الدفع"
                rules={[{ required: true, message: 'يرجى اختيار نوع الدفع' }]}
                initialValue="cash"
              >
                <Select
                  placeholder="اختر نوع الدفع"
                  onChange={(value) => {
                    const total = form.getFieldValue('total_amount') || 0
                    let paidAmount = 0

                    if (value === 'cash') {
                      paidAmount = total
                    } else if (value === 'credit') {
                      paidAmount = 0
                    }
                    // للدفع الجزئي، نترك المبلغ كما هو

                    const remaining = total - paidAmount
                    form.setFieldsValue({
                      paid_amount: paidAmount,
                      remaining_amount: remaining
                    })
                  }}
                >
                  <Option value="cash">نقدي</Option>
                  <Option value="credit">آجل</Option>
                  <Option value="partial">جزئي</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="paid_amount"
                label="المبلغ المدفوع"
                dependencies={['payment_type', 'total_amount']}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  formatter={value => `${getCurrencySymbol()} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => (value || '').replace(new RegExp(`${getCurrencySymbol()}\\s?|(,*)`,'g'), '') as any}
                  placeholder="المبلغ المدفوع"
                  onChange={(value) => {
                    const total = form.getFieldValue('total_amount') || 0
                    const remaining = total - (value || 0)
                    form.setFieldsValue({ remaining_amount: remaining })
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="remaining_amount"
                label="المبلغ المتبقي"
                dependencies={['paid_amount', 'total_amount']}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  disabled
                  formatter={value => `${getCurrencySymbol()} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  placeholder="المبلغ المتبقي"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="payment_method" label="طريقة الدفع">
                <Select placeholder="اختر طريقة الدفع" allowClear>
                  <Option value="cash">نقدي</Option>
                  <Option value="check">شيك</Option>
                  <Option value="bank_transfer">تحويل بنكي</Option>
                  <Option value="credit_card">بطاقة ائتمان</Option>
                  <Option value="receipt_voucher">سند قبض</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="payment_reference" label="مرجع الدفع">
                <Input placeholder="رقم الشيك، رقم التحويل، إلخ..." />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="notes" label="ملاحّات">
            <TextArea rows={3} placeholder="ملاحّات إضافية..." />
          </Form.Item>

          <div style={{ textAlign: 'left', marginTop: '24px' }}>
            <Space>
              <Button onClick={() => {
                setModalVisible(false)
                form.resetFields()
                setEditingInvoice(null)
                setInvoiceItems([])
                setItemsByWarehouse({}) // إعادة تعيين الأصناف حسب المخزن
                setSelectedCustomerId(null) // تنّيف معرف العميل المختار
                setAvailableOrders([]) // تنّيف أوامر البيع المتاحة
              }}>
                إلغاء
              </Button>
              {editingInvoice && (
                <InvoicePrintButton
                  invoiceData={{
                    id: editingInvoice.id,
                    invoiceNumber: editingInvoice.invoice_number,
                    invoiceDate: editingInvoice.invoice_date,
                    dueDate: editingInvoice.due_date,
                    customerName: editingInvoice.customer_name || '',
                    customerAddress: editingInvoice.customer_address || '',
                    customerPhone: editingInvoice.customer_phone || '',
                    items: invoiceItems.map(item => {
                      const quantity = item.quantity || 0
                      const unitPrice = item.unit_price || 0
                      const calculatedTotal = quantity * unitPrice

                      return {
                        id: item.id,
                        name: item.item_name,
                        description: item.notes || '',
                        quantity: quantity,
                        unit: 'قطعة',
                        unitPrice: unitPrice,
                        discount: 0,
                        tax: 0,
                        // التأكد من أن المجموع الفرعي = الكمية × سعر الوحدة
                        total: item.total_price || calculatedTotal
                      }
                    }),
                    subtotal: editingInvoice.subtotal || 0,
                    discount: editingInvoice.discount_amount || 0,
                    tax: editingInvoice.tax_amount || 0,
                    total: editingInvoice.total_amount || 0,
                    paid: editingInvoice.paid_amount || 0,
                    remaining: (editingInvoice.total_amount || 0) - (editingInvoice.paid_amount || 0),
                    notes: editingInvoice.notes || ''
                  }}
                  invoiceType="sales"
                  size="middle"
                  buttonText="طباعة الفاتورة"
                  onPrintSuccess={() => messageApi.success('تم طباعة الفاتورة بنجاح')}
                  onPrintError={() => messageApi.error('فشل في طباعة الفاتورة')}
                />
              )}
              <Button type="primary" htmlType="submit">
                {editingInvoice ? 'تحديث' : 'حفّ'}
              </Button>
              {!editingInvoice && (
                <Button
                  type="default"
                  htmlType="submit"
                  onClick={() => {
                    // حفّ وطباعة باستخدام النظام المتقدم
                    form.validateFields().then(values => {
                      handleSubmit(values).then(async (result) => {
                        if (result?.success) {
                          messageApi.success('تم حفّ الفاتورة، جاري الطباعة...')

                          try {
                            // استخدام نظام الطباعة المتقدم
                            const { MasterPrintService } = await import('../../services/MasterPrintService')

                            // تحضير بيانات الطباعة
                            const customer = customers.find(c => c.id === values.customer_id)
                            const invoiceData = {
                              id: result.data?.invoiceId || values.invoice_number,
                              title: 'فاتورة مبيعات',
                              number: values.invoice_number,
                              date: values.invoice_date?.format('YYYY-MM-DD') || new Date().toISOString().split('T')[0],
                              dueDate: values.due_date?.format('YYYY-MM-DD'),
                              customer: {
                                name: customer?.name || 'غير محدد',
                                address: '', // سيتم تحديثه لاحقاً من قاعدة البيانات
                                phone: '', // سيتم تحديثه لاحقاً من قاعدة البيانات
                                email: '' // سيتم تحديثه لاحقاً من قاعدة البيانات
                              },
                              items: invoiceItems.map(item => ({
                                id: item.item_id,
                                name: item.item_name || 'غير محدد',
                                description: item.notes || '',
                                quantity: item.quantity || 0,
                                unit: 'قطعة',
                                unitPrice: item.unit_price || 0,
                                total: item.total_price || 0
                              })),
                              subtotal: values.subtotal || 0,
                              discount: values.discount_amount || 0,
                              tax: values.tax_amount || 0,
                              total: values.total_amount || 0,
                              paid: values.paid_amount || 0,
                              remaining: values.remaining_amount || 0,
                              notes: values.notes,
                              terms: 'شكراً لتعاملكم معنا. يرجى الدفع خلال 30 يوماً من تاريخ الفاتورة.'
                            }

                            // تحميل إعدادات الطباعة
                            const loadPrintSettings = async () => {
                              try {
                                const result = await window.electronAPI?.invoke('get-settings', 'print_')
                                if (result?.success && result.data) {
                                  const settingsMap = result.data
                                  return {
                                    pageSize: settingsMap.print_page_size || 'A4',
                                    orientation: settingsMap.print_orientation || 'portrait',
                                    fontSize: parseInt(settingsMap.print_font_size) || 12,
                                    fontFamily: settingsMap.print_font_family || 'Arial',
                                    showHeader: settingsMap.print_show_header !== 'false',
                                    showFooter: settingsMap.print_show_footer !== 'false',
                                    showLogo: settingsMap.print_show_logo !== 'false',
                                    primaryColor: settingsMap.print_primary_color || '#52c41a',
                                    margins: {
                                      top: parseInt(settingsMap.print_margin_top) || 20,
                                      bottom: parseInt(settingsMap.print_margin_bottom) || 20,
                                      left: parseInt(settingsMap.print_margin_left) || 20,
                                      right: parseInt(settingsMap.print_margin_right) || 20
                                    }
                                  }
                                }
                              } catch (error) {
                                console.warn('فشل في تحميل إعدادات الطباعة:', error)
                              }

                              // الإعدادات الافتراضية
                              return {
                                pageSize: 'A4',
                                orientation: 'portrait',
                                fontSize: 12,
                                fontFamily: 'Arial',
                                showHeader: true,
                                showFooter: true,
                                showLogo: true,
                                primaryColor: '#52c41a',
                                margins: { top: 20, right: 20, bottom: 20, left: 20 }
                              }
                            }

                            const printSettings = await loadPrintSettings()
                            const printService = MasterPrintService.getInstance()

                            await printService.print(invoiceData, {
                              type: 'invoice',
                              subType: 'sales',
                              ...printSettings,
                              onSuccess: () => {
                                messageApi.success('تم طباعة الفاتورة بنجاح')
                              },
                              onError: (error) => {
                                messageApi.error(`فشل في الطباعة: ${error}`)
                              }
                            })

                          } catch (error) {
                            console.error('خطأ في الطباعة:', error)
                            messageApi.error('فشل في طباعة الفاتورة')
                          }
                        }
                      }).catch(error => {
                        messageApi.error('فشل في حفّ الفاتورة')
                      })
                    })
                  }}
                >
                  حفّ وطباعة
                </Button>
              )}
            </Space>
          </div>
        </Form>
      </Modal>

      {/* نافذة تسجيل الدفعة */}
      <Modal
        title={`تسجيل دفعة - ${selectedInvoiceForPayment?.invoice_number || ''}`}
        open={paymentModalVisible}
        onCancel={() => {
          setPaymentModalVisible(false)
          setSelectedInvoiceForPayment(null)
          paymentForm.resetFields()
        }}
        footer={null}
        width={700}
      >
        {selectedInvoiceForPayment && (
          <div>
            {/* معلومات الفاتورة */}
            <Card size="small" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Space direction="vertical" size="small">
                    <Typography.Text><strong>العميل:</strong> {selectedInvoiceForPayment.customer_name}</Typography.Text>
                    <Typography.Text><strong>تاريخ الفاتورة:</strong> {dayjs(selectedInvoiceForPayment.invoice_date).format('YYYY-MM-DD')}</Typography.Text>
                  </Space>
                </Col>
                <Col span={12}>
                  <RemainingAmountDisplay
                    totalAmount={selectedInvoiceForPayment.total_amount}
                    paidAmount={selectedInvoiceForPayment.paid_amount}
                    dueDate={selectedInvoiceForPayment.due_date}
                    showDetails={true}
                    size="small"
                  />
                </Col>
              </Row>
            </Card>

            {/* نموذج الدفع */}
            <Form
              form={paymentForm}
              layout="vertical"
              onFinish={handlePayment}
            >
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    name="payment_method"
                    label="طريقة الدفع"
                    rules={[{ required: true, message: 'يرجى اختيار طريقة الدفع' }]}
                  >
                    <PaymentMethodSelector
                      value={selectedPaymentMethod}
                      onChange={setSelectedPaymentMethod}
                      allowedMethods={[
                        PAYMENT_METHODS.CASH,
                        PAYMENT_METHODS.CHECK,
                        PAYMENT_METHODS.BANK_TRANSFER,
                        PAYMENT_METHODS.RECEIPT_VOUCHER
                      ]}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <PaymentForm
                paymentMethod={selectedPaymentMethod}
                maxAmount={selectedInvoiceForPayment.remaining_amount}
              />

              <div style={{ textAlign: 'left', marginTop: 24 }}>
                <Space>
                  <Button onClick={() => setPaymentModalVisible(false)}>
                    إلغاء
                  </Button>
                  <Button type="primary" htmlType="submit">
                    تسجيل الدفعة
                  </Button>
                </Space>
              </div>
            </Form>

            {/* سجل المدفوعات السابقة */}
            {invoicePayments.length > 0 && (
              <div style={{ marginTop: 24 }}>
                <Divider>المدفوعات السابقة</Divider>
                <Table
                  dataSource={invoicePayments}
                  size="small"
                  pagination={false}
                  loading={loadingPayments}
                  columns={[
                    {
                      title: 'التاريخ',
                      dataIndex: 'payment_date',
                      key: 'payment_date',
                      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
                    },
                    {
                      title: 'المبلغ',
                      dataIndex: 'amount',
                      key: 'amount',
                      render: (amount: number) => formatCurrency(amount)
                    },
                    {
                      title: 'طريقة الدفع',
                      dataIndex: 'payment_method',
                      key: 'payment_method'
                    },
                    {
                      title: 'المرجع',
                      dataIndex: 'reference_number',
                      key: 'reference_number',
                      render: (ref: string) => ref || '-'
                    }
                  ]}
                />
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default SalesInvoiceManagement
