import React from 'react';
import { Tag, Typography, Progress } from 'antd';
import UniversalReport from './UniversalReport';
import { DateUtils } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger'

const { Text } = Typography;

const MaterialConsumptionReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<any> => {
    try {
      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getMaterialConsumptionReport({
        dateRange: filters.dateRange,
        itemId: filters.itemId,
        categoryId: filters.categoryId
      });

      if (!response || !response.success || !response.data || !Array.isArray(response.data.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const materialData = response.data;

      const data = materialData.data;

      // حساب الإحصائيات
      const totalItems = data.length;
      const totalConsumed = data.reduce((sum, item) => sum + item.total_consumed, 0);
      const totalValue = data.reduce((sum, item) => sum + item.total_value, 0);
      const totalCurrentStock = data.reduce((sum, item) => sum + item.current_stock, 0);
      const lowStockItems = data.filter(item => item.current_stock <= item.min_stock).length;

      // إعداد البيانات للجدول
      const tableData = data.map((item, index) => ({
        key: item.item_id || index,
        index: index + 1,
        item_code: item.item_code,
        item_name: item.item_name,
        category_name: item.category_name || '-',
        unit: item.unit || 'قطعة',
        total_consumed: item.total_consumed,
        total_value: item.total_value,
        avg_unit_cost: item.avg_unit_cost,
        current_stock: item.current_stock,
        min_stock: item.min_stock,
        max_stock: item.max_stock,
        consumption_rate: item.consumption_rate,
        stock_status: item.current_stock <= item.min_stock ? 'منخفض' : 
                     item.current_stock >= item.max_stock ? 'مرتفع' : 'طبيعي',
        stock_percentage: item.max_stock > 0 ? (item.current_stock / item.max_stock * 100) : 0,
        consumption_percentage: totalConsumed > 0 ? (item.total_consumed / totalConsumed * 100) : 0,
        value_percentage: totalValue > 0 ? (item.total_value / totalValue * 100) : 0
      }));

      // تعريف الأعمدة
      const columns = [
        {
          title: '#',
          dataIndex: 'index',
          key: 'index',
          width: 50,
          align: 'center' as const
        },
        {
          title: 'كود الصنف',
          dataIndex: 'item_code',
          key: 'item_code',
          width: 100
        },
        {
          title: 'اسم الصنف',
          dataIndex: 'item_name',
          key: 'item_name',
          width: 150
        },
        {
          title: 'الفئة',
          dataIndex: 'category_name',
          key: 'category_name',
          width: 120
        },
        {
          title: 'الوحدة',
          dataIndex: 'unit',
          key: 'unit',
          width: 80,
          align: 'center' as const
        },
        {
          title: 'إجمالي الاستهلاك',
          dataIndex: 'total_consumed',
          key: 'total_consumed',
          width: 120,
          align: 'right' as const,
          render: (value: number, record: any) => (
            <Text strong style={{ color: '#1890ff' }}>
              {value.toLocaleString('ar-EG')} {record.unit}
            </Text>
          )
        },
        {
          title: 'قيمة الاستهلاك',
          dataIndex: 'total_value',
          key: 'total_value',
          width: 120,
          align: 'right' as const,
          render: (value: number) => (
            <Text strong style={{ color: '#52c41a' }}>
              {value.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'متوسط التكلفة',
          dataIndex: 'avg_unit_cost',
          key: 'avg_unit_cost',
          width: 100,
          align: 'right' as const,
          render: (value: number) => (
            <Text style={{ color: '#722ed1' }}>
              {value.toLocaleString('ar-EG')} ج.م
            </Text>
          )
        },
        {
          title: 'المخزون الحالي',
          dataIndex: 'current_stock',
          key: 'current_stock',
          width: 100,
          align: 'right' as const,
          render: (value: number, record: any) => {
            const color = value <= record.min_stock ? '#ff4d4f' : 
                         value >= record.max_stock ? '#fa8c16' : '#52c41a';
            return (
              <Text style={{ color }}>
                {value.toLocaleString('ar-EG')} {record.unit}
              </Text>
            );
          }
        },
        {
          title: 'الحد الأدنى',
          dataIndex: 'min_stock',
          key: 'min_stock',
          width: 100,
          align: 'right' as const,
          render: (value: number, record: any) => (
            <Text style={{ color: '#fa8c16' }}>
              {value.toLocaleString('ar-EG')} {record.unit}
            </Text>
          )
        },
        {
          title: 'حالة المخزون',
          dataIndex: 'stock_status',
          key: 'stock_status',
          width: 100,
          align: 'center' as const,
          render: (value: string) => {
            const statusColors: { [key: string]: string } = {
              'منخفض': 'red',
              'طبيعي': 'green',
              'مرتفع': 'orange'
            };
            return (
              <Tag color={statusColors[value] || 'default'}>
                {value}
              </Tag>
            );
          }
        },
        {
          title: 'نسبة المخزون',
          dataIndex: 'stock_percentage',
          key: 'stock_percentage',
          width: 120,
          align: 'center' as const,
          render: (value: number) => (
            <Progress
              percent={Math.round(value)}
              size="small"
              status={value <= 20 ? 'exception' : value >= 80 ? 'success' : 'active'}
            />
          )
        },
        {
          title: 'معدل الاستهلاك',
          dataIndex: 'consumption_rate',
          key: 'consumption_rate',
          width: 120,
          align: 'center' as const,
          render: (value: number) => (
            <Tag color="blue">
              {value.toFixed(2)}/يوم
            </Tag>
          )
        },
        {
          title: 'نسبة الاستهلاك',
          dataIndex: 'consumption_percentage',
          key: 'consumption_percentage',
          width: 120,
          align: 'center' as const,
          render: (value: number) => (
            <Tag color="purple">
              {value.toFixed(1)}%
            </Tag>
          )
        },
        {
          title: 'نسبة القيمة',
          dataIndex: 'value_percentage',
          key: 'value_percentage',
          width: 100,
          align: 'center' as const,
          render: (value: number) => (
            <Tag color="cyan">
              {value.toFixed(1)}%
            </Tag>
          )
        }
      ];

      // إعداد الإحصائيات
      const avgConsumptionRate = data.length > 0 ? data.reduce((sum, item) => sum + item.consumption_rate, 0) / data.length : 0;
      const avgUnitCost = totalConsumed > 0 ? (totalValue / totalConsumed) : 0;
      const stockTurnover = totalCurrentStock > 0 ? (totalConsumed / totalCurrentStock) : 0;

      const statistics = [
        {
          title: 'إجمالي الأصناف',
          value: totalItems,
          color: '#1890ff',
          icon: '📦'
        },
        {
          title: 'إجمالي الاستهلاك',
          value: totalConsumed.toLocaleString('ar-EG'),
          color: '#52c41a',
          icon: '📊'
        },
        {
          title: 'قيمة الاستهلاك',
          value: `${totalValue.toLocaleString('ar-EG')} ج.م`,
          color: '#13c2c2',
          icon: '💰'
        },
        {
          title: 'متوسط التكلفة/وحدة',
          value: `${avgUnitCost.toFixed(2)} ج.م`,
          color: '#722ed1',
          icon: '💵'
        },
        {
          title: 'إجمالي المخزون',
          value: totalCurrentStock.toLocaleString('ar-EG'),
          color: '#fa8c16',
          icon: '🏪'
        },
        {
          title: 'أصناف منخفضة المخزون',
          value: lowStockItems,
          color: '#ff4d4f',
          icon: '⚠️'
        },
        {
          title: 'معدل الاستهلاك المتوسط',
          value: `${avgConsumptionRate.toFixed(2)}/يوم`,
          color: '#eb2f96',
          icon: '📈'
        },
        {
          title: 'معدل دوران المخزون',
          value: stockTurnover.toFixed(2),
          color: '#f759ab',
          icon: '🔄'
        }
      ];

      return {
        title: 'تقرير استهلاك المواد',
        columns,
        data: tableData,
        statistics,
        summary: {
          totalRecords: totalItems,
          totalConsumed: totalConsumed,
          totalValue: totalValue,
          lowStockItems: lowStockItems
        }
      };

    } catch (error) {
      Logger.error('MaterialConsumptionReport', 'خطأ في إنشاء تقرير استهلاك المواد الخام:', error);
      throw new Error('فشل في تحميل بيانات التقرير');
    }
  };

  return (
    <UniversalReport
      reportType="material_consumption"
      title="تقرير استهلاك المواد الخام"
      description="تقرير يعرض استهلاك المواد الخام خلال فترة محددة مع تحليل الكميات والتكاليف"
      onGenerateReport={generateReport}
      showDateRange={true}
      showWarehouseFilter={false}
      showCategoryFilter={true}
      showItemFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="material_consumption_report"
      defaultFilters={{
        sortBy: 'total_consumed',
        sortOrder: 'desc'
      }}
    />
  );
};

export default MaterialConsumptionReport;
