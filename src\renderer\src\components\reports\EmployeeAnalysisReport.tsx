import React from 'react'
import UniversalReport from './UniversalReport'
import type { ReportData, ReportType } from '../../types/reports'

const EmployeeAnalysisReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير تحليل الموظفين...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getEmployeeAnalysis(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const employeeData = response.data;

      // إعداد الأعمدة
      const columns = [
        {
          key: 'employee_code',
          title: 'كود الموظف',
          align: 'center' as const,
          width: '120px'
        },
        {
          key: 'employee_name',
          title: 'اسم الموظف',
          align: 'right' as const,
          width: '200px'
        },
        {
          key: 'department',
          title: 'القسم',
          align: 'center' as const,
          width: '150px'
        },
        {
          key: 'position',
          title: 'المنصب',
          align: 'center' as const,
          width: '150px'
        },
        {
          key: 'attendance_rate',
          title: 'معدل الحضور %',
          align: 'center' as const,
          format: 'percentage' as const,
          width: '120px'
        },
        {
          key: 'performance_score',
          title: 'تقييم الأداء',
          align: 'center' as const,
          format: 'number' as const,
          width: '120px'
        },
        {
          key: 'monthly_salary',
          title: 'الراتب الشهري',
          align: 'left' as const,
          format: 'currency' as const,
          width: '150px'
        },
        {
          key: 'overtime_hours',
          title: 'ساعات إضافية',
          align: 'center' as const,
          format: 'number' as const,
          width: '120px'
        }
      ];

      // إضافة مفاتيح فريدة للبيانات
      const dataWithKeys = employeeData.map((item: any, index: number) => ({
        key: `employee-${index}`,
        ...item
      }));

      // حساب الإحصائيات السريعة
      const totalEmployees = employeeData.length;
      const averageAttendance = employeeData.length > 0
        ? employeeData.reduce((sum: number, emp: any) => sum + (emp.attendance_rate || 0), 0) / employeeData.length
        : 0;
      const averagePerformance = employeeData.length > 0
        ? employeeData.reduce((sum: number, emp: any) => sum + (emp.performance_score || 0), 0) / employeeData.length
        : 0;
      const totalSalaries = employeeData.reduce((sum: number, emp: any) => sum + (emp.monthly_salary || 0), 0);

      const quickStats = [
        {
          title: 'إجمالي الموظفين',
          value: totalEmployees,
          format: 'number' as const,
          color: '#1890ff'
        },
        {
          title: 'متوسط الحضور',
          value: averageAttendance,
          format: 'percentage' as const,
          color: averageAttendance >= 90 ? '#52c41a' : averageAttendance >= 80 ? '#faad14' : '#ff4d4f'
        },
        {
          title: 'متوسط الأداء',
          value: averagePerformance,
          format: 'number' as const,
          color: averagePerformance >= 8 ? '#52c41a' : averagePerformance >= 6 ? '#faad14' : '#ff4d4f'
        },
        {
          title: 'إجمالي الرواتب',
          value: totalSalaries,
          format: 'currency' as const,
          color: '#722ed1'
        }
      ];

      console.log('✅ تم إنشاء تقرير تحليل الموظفين بنجاح');

      return {
        title: 'تقرير تحليل الموظفين',
        data: dataWithKeys,
        columns,
        summary: {
          totalRecords: dataWithKeys.length,
          quickStats
        }
      };

    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير تحليل الموظفين:', error);
      throw error;
    }
  };

  // إعدادات التقرير
  const reportConfig = {
    reportType: 'employee-analysis' as ReportType,
    title: 'تقرير تحليل الموظفين',
    generateReport,
    defaultFilters: {
      dateRange: null, // كل المدة افتراضياً
    },
    enabledFilters: {
      showDateFilter: true,
      showAdvancedSearch: true,
    },
    features: {
      enableExport: true,
      enablePrint: true,
      enableRefresh: true,
      showQuickStats: true,
    }
  };

  return <UniversalReport {...reportConfig} />;
};

export default EmployeeAnalysisReport;
