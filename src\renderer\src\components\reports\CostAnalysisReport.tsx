import React from 'react';
import { Tag, Typography, Progress } from 'antd';
import UniversalReport from './UniversalReport';

import { SafeLogger as Logger } from '../../utils/logger';
import type { ReportData, ReportType } from '../../types/reports';

const { Text } = Typography;

const CostAnalysisReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير تحليل التكاليف...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getCostAnalysisReport(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const costAnalysisData = response.data;

      // إعداد الأعمدة للنظام المركزي
      const columns = [
        {
          key: 'cost_center',
          title: 'مركز التكلفة',
          width: '150px',
          align: 'center' as const,
          render: (center: string) => (
            <Text strong style={{ color: '#1890ff' }}>{center}</Text>
          )
        },
        {
          key: 'category_name',
          title: 'الفئة',
          width: '120px',
          align: 'center' as const,
          render: (category: string) => (
            <Tag color="purple">{category}</Tag>
          )
        },
        {
          key: 'direct_cost',
          title: 'التكلفة المباشرة (₪)',
          width: '150px',
          align: 'right' as const,
          format: 'currency' as const,
          render: (cost: number) => (
            <Text strong style={{ color: '#52c41a' }}>
              {cost.toLocaleString()}
            </Text>
          )
        },
        {
          key: 'indirect_cost',
          title: 'التكلفة غير المباشرة (₪)',
          width: '170px',
          align: 'right' as const,
          format: 'currency' as const,
          render: (cost: number) => (
            <Text style={{ color: '#fa8c16' }}>
              {cost.toLocaleString()}
            </Text>
          )
        },
        {
          key: 'total_cost',
          title: 'إجمالي التكلفة (₪)',
          width: '150px',
          align: 'right' as const,
          format: 'currency' as const,
          render: (cost: number) => (
            <Text strong style={{ color: '#1890ff' }}>
              {cost.toLocaleString()}
            </Text>
          )
        },
        {
          key: 'percentage_of_total',
          title: 'نسبة من الإجمالي',
          width: '150px',
          align: 'center' as const,
          format: 'percentage' as const,
          render: (percentage: number) => (
            <Progress
              percent={Math.round(percentage)}
              size="small"
              strokeColor="#1890ff"
            />
          )
        },
        {
          key: 'cost_per_unit',
          title: 'التكلفة لكل وحدة (₪)',
          width: '150px',
          align: 'right' as const,
          format: 'currency' as const,
          render: (cost: number) => (
            <Text>{cost.toLocaleString()}</Text>
          )
        },
        {
          key: 'quantity',
          title: 'الكمية',
          width: '100px',
          align: 'right' as const,
          format: 'number' as const,
          render: (quantity: number) => (
            <Text>{quantity.toLocaleString()}</Text>
          )
        },
        {
          key: 'cost_variance',
          title: 'تغير التكلفة',
          width: '130px',
          align: 'center' as const,
          render: (variance: number) => {
            const color = variance <= 0 ? '#52c41a' : '#ff4d4f';
            const icon = variance <= 0 ? '↓' : '↑';
            return (
              <Text style={{ color }}>
                {icon} {Math.abs(variance).toFixed(1)}%
              </Text>
            );
          }
        },
        {
          key: 'status',
          title: 'الحالة',
          width: '100px',
          align: 'center' as const,
          render: (_: string, record: any) => {
            if (record.cost_variance > 10) return <Tag color="red">مرتفع</Tag>;
            if (record.cost_variance > 5) return <Tag color="orange">متوسط</Tag>;
            return <Tag color="green">طبيعي</Tag>;
          }
        }
      ];

      // إضافة مفاتيح فريدة للبيانات
      const dataWithKeys = costAnalysisData.map((item: any, index: number) => ({
        ...item,
        key: item.cost_center_id || index,
        total_cost: item.direct_cost + item.indirect_cost,
        cost_per_unit: item.quantity > 0 ? (item.direct_cost + item.indirect_cost) / item.quantity : 0
      }));

      // حساب النسب المئوية
      const grandTotal = dataWithKeys.reduce((sum: number, item: any) => sum + item.total_cost, 0);
      dataWithKeys.forEach((item: any) => {
        item.percentage_of_total = grandTotal > 0 ? (item.total_cost / grandTotal) * 100 : 0;
      });

      // حساب الإحصائيات
      const totalCostCenters = dataWithKeys.length;
      const totalDirectCost = dataWithKeys.reduce((sum: number, item: any) => sum + item.direct_cost, 0);
      const totalIndirectCost = dataWithKeys.reduce((sum: number, item: any) => sum + item.indirect_cost, 0);
      const totalQuantity = dataWithKeys.reduce((sum: number, item: any) => sum + item.quantity, 0);
      const avgCostPerUnit = totalQuantity > 0 ? grandTotal / totalQuantity : 0;
      const directCostPercentage = grandTotal > 0 ? (totalDirectCost / grandTotal) * 100 : 0;
      const indirectCostPercentage = grandTotal > 0 ? (totalIndirectCost / grandTotal) * 100 : 0;

      // أعلى مراكز التكلفة
      const highestCostCenters = [...dataWithKeys]
        .sort((a: any, b: any) => b.total_cost - a.total_cost)
        .slice(0, 5)
        .map((item: any) => item.cost_center);

      // مراكز التكلفة مع أعلى تباين
      const highestVarianceCenters = [...dataWithKeys]
        .filter((item: any) => item.cost_variance > 0)
        .sort((a: any, b: any) => b.cost_variance - a.cost_variance)
        .slice(0, 5)
        .map((item: any) => item.cost_center);

      // أكثر الفئات تكلفة
      const categoryTotals = dataWithKeys.reduce((acc: any, item: any) => {
        if (!acc[item.category_name]) {
          acc[item.category_name] = 0;
        }
        acc[item.category_name] += item.total_cost;
        return acc;
      }, {});

      const topCategories = Object.entries(categoryTotals)
        .sort(([,a]: any, [,b]: any) => b - a)
        .slice(0, 5)
        .map(([category]: any) => category);

      // مراكز التكلفة الأكثر كفاءة (أقل تكلفة لكل وحدة)
      const mostEfficientCenters = [...dataWithKeys]
        .filter((item: any) => item.cost_per_unit > 0)
        .sort((a: any, b: any) => a.cost_per_unit - b.cost_per_unit)
        .slice(0, 5)
        .map((item: any) => item.cost_center);

      // إعداد العنوان الفرعي
      let subtitle = 'تحليل شامل للتكاليف';
      if (filters.categoryId) {
        const selectedCategory = dataWithKeys.find((item: any) => item.category_id === filters.categoryId);
        if (selectedCategory) {
          subtitle = `الفئة: ${selectedCategory.category_name}`;
        }
      }
      if (filters.supplierId) {
        subtitle += ' - مورد محدد';
      }
      if (filters.dateRange && filters.dateRange.length === 2) {
        // التعامل مع التواريخ كـ strings أو dayjs objects
        const startDate = typeof filters.dateRange[0] === 'string' ? filters.dateRange[0] : filters.dateRange[0].format('YYYY-MM-DD');
        const endDate = typeof filters.dateRange[1] === 'string' ? filters.dateRange[1] : filters.dateRange[1].format('YYYY-MM-DD');
        subtitle += ` | الفترة: ${startDate} - ${endDate}`;
      }

      return {
        title: 'تحليل التكاليف',
        subtitle,
        columns,
        data: dataWithKeys,
        summary: {
          totalCostCenters,
          totalDirectCost: Math.round(totalDirectCost * 100) / 100,
          totalIndirectCost: Math.round(totalIndirectCost * 100) / 100,
          grandTotal: Math.round(grandTotal * 100) / 100,
          totalQuantity: Math.round(totalQuantity * 100) / 100,
          avgCostPerUnit: Math.round(avgCostPerUnit * 100) / 100,
          directCostPercentage: Math.round(directCostPercentage * 100) / 100,
          indirectCostPercentage: Math.round(indirectCostPercentage * 100) / 100,
          highestCostCenters,
          highestVarianceCenters,
          topCategories,
          mostEfficientCenters
        }
      };
    } catch (error) {
      Logger.error('CostAnalysisReport', 'خطأ في إنشاء تحليل التكاليف:', error);
      throw new Error('فشل في إنشاء التقرير');
    }
  };

  return (
    <UniversalReport
      reportType={'cost_analysis' as ReportType}
      title="تقرير تحليل التكاليف"
      description="تقرير تحليلي شامل للتكاليف المباشرة وغير المباشرة مع مؤشرات الكفاءة والتباين"
      onGenerateReport={generateReport}
      showDateRange={true}
      showSupplierFilter={true}
      showCategoryFilter={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default CostAnalysisReport;
