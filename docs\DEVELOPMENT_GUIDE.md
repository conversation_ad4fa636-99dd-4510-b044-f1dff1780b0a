# 🛠️ دليل التطوير - ZET.IA

**المطور:** FARESNAWAF
**البريد الإلكتروني:** <EMAIL>
**الهاتف:** **********

## 📋 نظرة عامة

هذا الدليل يوضح كيفية إعداد بيئة التطوير والعمل على مشروع ZET.IA.

## 🏗️ بنية المشروع

```
accounting-app/
├── src/                    # الكود المصدري
│   ├── main/              # Electron main process
│   │   ├── main.ts        # الملف الرئيسي
│   │   ├── preload.ts     # ملف التحميل المسبق
│   │   ├── handlers/      # معالجات IPC
│   │   └── services/      # خدمات قاعدة البيانات
│   ├── renderer/          # React app
│   │   ├── src/
│   │   │   ├── components/    # مكونات React
│   │   │   ├── types/         # تعريفات TypeScript
│   │   │   ├── utils/         # أدوات مساعدة
│   │   │   ├── App.tsx        # التطبيق الرئيسي
│   │   │   └── main.tsx       # نقطة الدخول
│   │   └── index.html
│   ├── config/            # ملفات الإعداد
│   ├── assets/            # الأصول (صور، أيقونات)
│   └── test/              # ملفات الاختبار
├── tools/                 # أدوات التطوير والبناء
│   ├── scripts/           # سكريبتات الصيانة
│   ├── build-production.js   # بناء الإنتاج
│   └── dev-setup.js       # إعداد التطوير
├── docs/                  # التوثيق
├── dist/                  # ملفات البناء
├── release-updated/       # التطبيق النهائي
├── node_modules/          # التبعيات
├── package.json           # إعدادات المشروع
├── tsconfig.json          # إعدادات TypeScript
├── vite.config.ts         # إعدادات Vite
└── README.md              # الدليل الرئيسي
```

## 🚀 البدء السريع

### 1. المتطلبات
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn
- Windows 10 أو أحدث

### 2. إعداد المشروع
```bash
# استنساخ المشروع
git clone [repository-url]
cd accounting-app

# إعداد بيئة التطوير (يتضمن تثبيت التبعيات وفحص المتطلبات)
npm run dev-setup
```

### 3. تشغيل وضع التطوير
```bash
npm run dev
```

## 🔧 أوامر التطوير

### أوامر أساسية
```bash
npm run dev              # تشغيل وضع التطوير
npm run build            # بناء المشروع
npm run pack             # إنشاء حزمة Electron
npm run dist             # بناء وإنشاء ملف التثبيت
```

### أوامر الفحص والجودة
```bash
npm run type-check       # فحص TypeScript
npm run lint             # فحص ESLint
npm run lint:fix         # إصلاح أخطاء ESLint
npm run quality-check    # فحص شامل للجودة
```

### أوامر البناء المتقدمة
```bash
npm run clean            # تنظيف ملفات البناء
npm run pre-build        # فحص الجودة + التنظيف
npm run build-safe       # بناء آمن مع فحص الجودة
npm run build-production # بناء شامل للإنتاج
npm run full-build       # إعداد + بناء كامل
```

### أوامر التشغيل
```bash
npm run electron         # تشغيل Electron مباشرة
npm run run-app          # تشغيل التطبيق المبني
```

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 18** - مكتبة واجهة المستخدم
- **TypeScript** - لغة البرمجة مع الأنواع
- **Ant Design v5** - مكتبة المكونات
- **Styled Components** - تنسيق CSS-in-JS
- **React Router** - التنقل بين الصفحات

### Backend
- **Electron** - إطار التطبيقات المحمولة
- **Node.js** - بيئة تشغيل JavaScript
- **SQLite + sql.js** - قاعدة البيانات
- **bcryptjs** - تشفير كلمات المرور

### أدوات التطوير
- **Vite** - أداة البناء السريعة
- **ESLint** - فحص جودة الكود
- **Electron Builder** - بناء التطبيق النهائي

## 📁 ملفات الإعداد المهمة

### package.json
يحتوي على:
- معلومات المشروع
- التبعيات والحزم
- سكريبتات البناء والتطوير
- إعدادات Electron Builder

### tsconfig.json
إعدادات TypeScript للمشروع الرئيسي

### tsconfig.main.json
إعدادات TypeScript لـ Electron main process

### vite.config.ts
إعدادات Vite للبناء والتطوير:
- تحسينات الأداء
- تقسيم الحزم
- إعدادات Terser للضغط

## 🔍 نصائح التطوير

### 1. فحص الأخطاء
```bash
# فحص شامل قبل البناء
npm run quality-check

# فحص TypeScript فقط
npm run type-check
```

### 2. تحسين الأداء
- استخدم `React.memo` للمكونات الثقيلة
- استخدم `useMemo` و `useCallback` للحسابات المعقدة
- تجنب re-renders غير الضرورية

### 3. إدارة الحالة
- استخدم `useState` للحالة المحلية
- استخدم `useContext` للحالة المشتركة
- تجنب prop drilling

### 4. أفضل الممارسات
- اكتب كود TypeScript صحيح
- استخدم ESLint لفحص الجودة
- اكتب تعليقات واضحة
- اتبع نمط تسمية ثابت

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في تثبيت التبعيات**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **أخطاء TypeScript**
   ```bash
   npm run type-check
   ```

3. **مشاكل البناء**
   ```bash
   npm run clean
   npm run build-safe
   ```

4. **مشاكل Electron**
   ```bash
   npm run rebuild
   ```

## 📞 الدعم

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملف README.md
3. راجع ملفات التوثيق في مجلد docs/
4. تواصل مع فريق التطوير
