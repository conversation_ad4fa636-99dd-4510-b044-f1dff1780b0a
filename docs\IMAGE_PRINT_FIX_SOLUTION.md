# 🔧 حل مشكلة عدم ظهور الصور في الطباعة والمعاينة

## 🎯 **المشاكل المكتشفة**

### 1. **مسارات الصور غير الصحيحة**
- النظام يحفظ مسارات مطلقة في قاعدة البيانات
- عند الطباعة، لا يتم العثور على الملفات في المسارات المحفوظة
- عدم وجود آلية للبحث في مسارات بديلة

### 2. **فشل تحويل الصور إلى base64**
- دالة `readFileAsBase64` تفشل عند عدم وجود الملف
- لا توجد معالجة للأخطاء أو صور بديلة
- عدم التحقق من صحة المسارات قبل المحاولة

### 3. **عدم وجود مجلد الصور**
- مجلد `images` غير موجود في userData
- الصور لا يتم حفظها في المكان المتوقع
- عدم إنشاء المجلدات تلقائياً

## 🛠️ **الحلول المطبقة**

### 1. **تحسين دالة readFileAsBase64**

**الملف**: `src/main/handlers/systemHandlers.ts`

```typescript
// قراءة ملف كـ base64 للشعارات والصور مع معالجة محسنة
ipcMain.handle('read-file-as-base64', async (event, filePath: string) => {
  try {
    Logger.debug('SystemHandlers', `🔍 محاولة قراءة الملف: ${filePath}`)

    // إذا كان المسار يحتوي على base64 بالفعل، أرجعه كما هو
    if (filePath.startsWith('data:')) {
      Logger.info('SystemHandlers', 'الملف محول بالفعل إلى base64')
      return filePath.split(',')[1] // إرجاع الجزء base64 فقط
    }

    // محاولة المسارات المختلفة
    const possiblePaths = [
      filePath, // المسار الأصلي
      path.resolve(filePath), // المسار المطلق
      path.join(app.getPath('userData'), filePath), // مسار userData
      path.join(app.getPath('userData'), 'images', path.basename(filePath)), // مجلد الصور
      path.join(process.cwd(), filePath), // مجلد المشروع
      path.join(process.cwd(), 'images', path.basename(filePath)) // مجلد صور المشروع
    ]

    let actualPath = null
    for (const testPath of possiblePaths) {
      if (fs.existsSync(testPath)) {
        actualPath = testPath
        Logger.info('SystemHandlers', `✅ تم العثور على الملف في: ${testPath}`)
        break
      }
    }

    if (!actualPath) {
      Logger.warn('SystemHandlers', `❌ الملف غير موجود في أي من المسارات المحتملة: ${filePath}`)
      return null
    }

    // قراءة الملف وتحويله إلى base64
    const fileBuffer = fs.readFileSync(actualPath)
    const base64String = fileBuffer.toString('base64')

    Logger.info('SystemHandlers', `✅ تم تحميل الملف بنجاح: ${path.basename(actualPath)} (${fileBuffer.length} بايت)`)
    return base64String
  } catch (error) {
    Logger.error('SystemHandlers', `❌ خطأ في قراءة الملف ${filePath}:`, error)
    return null
  }
})
```

### 2. **تحسين معالجة الصور في MasterPrintService**

**الملف**: `src/renderer/src/services/MasterPrintService.ts`

#### أ. تحسين تحويل صور أوامر الإنتاج:

```typescript
// تحويل الصورة إلى base64 إذا لم تكن محولة بالفعل
let imageSrc = image.path || image.image_path

if (imageSrc && !imageSrc.startsWith('data:')) {
  try {
    Logger.info('MasterPrintService', `🔄 محاولة تحويل صورة أمر الإنتاج إلى base64: ${imageName}`)
    
    if (window.electronAPI) {
      const base64 = await window.electronAPI.readFileAsBase64(imageSrc)
      if (base64) {
        // تحديد نوع الصورة من الامتداد أو المحتوى
        let mimeType = 'image/png'
        
        const extension = imageSrc.toLowerCase().split('.').pop()
        switch (extension) {
          case 'jpg':
          case 'jpeg':
            mimeType = 'image/jpeg'
            break
          case 'png':
            mimeType = 'image/png'
            break
          case 'gif':
            mimeType = 'image/gif'
            break
          case 'webp':
            mimeType = 'image/webp'
            break
          default:
            // محاولة تحديد النوع من بداية البيانات
            if (base64.startsWith('/9j/')) mimeType = 'image/jpeg'
            else if (base64.startsWith('iVBORw0KGgo')) mimeType = 'image/png'
            else if (base64.startsWith('R0lGODlh')) mimeType = 'image/gif'
            break
        }
        
        imageSrc = `data:${mimeType};base64,${base64}`
        Logger.info('MasterPrintService', `✅ تم تحويل صورة أمر الإنتاج بنجاح: ${imageName} (${mimeType})`)
      } else {
        Logger.warn('MasterPrintService', `⚠️ فشل في تحويل صورة أمر الإنتاج: ${imageName}`)
        // إنشاء صورة بديلة
        imageSrc = this.generateFallbackImage(imageName, 200, 150)
        Logger.info('MasterPrintService', `🔄 تم إنشاء صورة بديلة لـ: ${imageName}`)
      }
    } else {
      Logger.error('MasterPrintService', 'electronAPI غير متوفر')
      imageSrc = this.generateFallbackImage(imageName, 200, 150)
    }
  } catch (error) {
    Logger.error('MasterPrintService', `❌ خطأ في تحويل صورة أمر الإنتاج ${imageName}:`, error)
    // إنشاء صورة بديلة عند الخطأ
    imageSrc = this.generateFallbackImage(imageName, 200, 150)
    Logger.info('MasterPrintService', `🔄 تم إنشاء صورة بديلة بسبب الخطأ لـ: ${imageName}`)
  }
}
```

#### ب. تحسين معالجة الشعار:

```typescript
} else {
  Logger.warn('MasterPrintService', '⚠️ فشل في تحويل الشعار إلى base64، سيتم إنشاء شعار افتراضي')
  logoSrc = this.generateDefaultLogo()
}
```

#### ج. إضافة دالة إنشاء شعار افتراضي:

```typescript
/**
 * إنشاء شعار افتراضي عند فشل تحميل الشعار الأصلي
 */
private generateDefaultLogo(): string {
  // إنشاء SVG كشعار افتراضي
  const svg = `
    <svg width="120" height="80" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#2563eb" rx="8"/>
      <text x="50%" y="35%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
        ZET.IA
      </text>
      <text x="50%" y="65%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="10" fill="#e2e8f0">
        نظام المحاسبة
      </text>
    </svg>
  `
  
  // تحويل SVG إلى base64
  const base64 = btoa(encodeURIComponent(svg).replace(/%([0-9A-F]{2})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16))))
  return `data:image/svg+xml;base64,${base64}`
}
```

### 3. **تحسين ImagePrintService**

**الملف**: `src/renderer/src/services/images/ImagePrintService.ts`

#### إضافة دالة التحقق من صحة الصور:

```typescript
/**
 * التحقق من صحة الصور ومعالجتها
 */
private async validateAndProcessImages(images: UniversalImage[]): Promise<UniversalImage[]> {
  const validImages: UniversalImage[] = []

  for (const image of images) {
    try {
      // التحقق من وجود المسار
      if (!image.path) {
        Logger.warn('ImagePrintService', `⚠️ صورة بدون مسار: ${image.name}`)
        continue
      }

      // إذا كانت الصورة base64 بالفعل، أضفها مباشرة
      if (image.path.startsWith('data:')) {
        validImages.push(image)
        continue
      }

      // محاولة تحويل الصورة إلى base64
      if (window.electronAPI) {
        const base64 = await window.electronAPI.readFileAsBase64(image.path)
        if (base64) {
          // تحديد نوع الصورة
          let mimeType = 'image/png'
          const extension = image.path.toLowerCase().split('.').pop()
          switch (extension) {
            case 'jpg':
            case 'jpeg':
              mimeType = 'image/jpeg'
              break
            case 'png':
              mimeType = 'image/png'
              break
            case 'gif':
              mimeType = 'image/gif'
              break
            case 'webp':
              mimeType = 'image/webp'
              break
          }

          // إنشاء نسخة محدثة من الصورة
          const updatedImage: UniversalImage = {
            ...image,
            path: `data:${mimeType};base64,${base64}`
          }
          
          validImages.push(updatedImage)
          Logger.info('ImagePrintService', `✅ تم تحويل الصورة: ${image.name}`)
        } else {
          Logger.warn('ImagePrintService', `⚠️ فشل في تحويل الصورة: ${image.name}`)
        }
      }
    } catch (error) {
      Logger.error('ImagePrintService', `❌ خطأ في معالجة الصورة ${image.name}:`, error)
    }
  }

  return validImages
}
```

### 4. **أداة اختبار التشخيص**

**الملف**: `src/renderer/src/components/common/ImagePrintTest.tsx`

تم إنشاء مكون React لاختبار وتشخيص مشاكل طباعة الصور:

- اختبار تحميل الصور من قاعدة البيانات
- اختبار تحويل الصور إلى base64
- اختبار طباعة صورة تجريبية
- عرض نتائج مفصلة مع التفاصيل

## 🚀 **كيفية الاستخدام**

### 1. **للمطورين - اختبار النظام:**

```typescript
import { ImagePrintTest } from '../components/common/ImagePrintTest'

// في أي صفحة للاختبار
<ImagePrintTest />
```

### 2. **للمستخدمين - التحقق من الصور:**

1. تأكد من وجود الصور في قاعدة البيانات
2. تحقق من صحة مسارات الصور
3. استخدم أداة التشخيص لفحص المشاكل

### 3. **للإدارة - مراقبة النظام:**

- راجع سجلات النظام (logs) للتحقق من أخطاء تحميل الصور
- استخدم أداة التشخيص بانتظام
- تأكد من وجود مساحة كافية لحفظ الصور

## 📊 **النتائج المتوقعة**

### ✅ **بعد تطبيق الحلول:**

1. **الصور تظهر في الطباعة والمعاينة**
2. **معالجة أفضل للأخطاء مع صور بديلة**
3. **تسجيل مفصل للمشاكل والحلول**
4. **دعم مسارات متعددة للبحث عن الصور**
5. **شعار افتراضي عند فشل تحميل شعار الشركة**

### 🔧 **أدوات التشخيص:**

- مكون اختبار شامل للتحقق من النظام
- سجلات مفصلة لتتبع المشاكل
- رسائل واضحة للمستخدم عند حدوث أخطاء

## 🎯 **التوصيات للمستقبل**

1. **إنشاء مجلد صور افتراضي** عند بدء التطبيق
2. **تحويل جميع الصور إلى base64** عند الحفظ
3. **إضافة نظام نسخ احتياطي للصور**
4. **تحسين ضغط الصور** لتوفير المساحة
5. **إضافة واجهة إدارة الصور** للمستخدمين

---

**تاريخ الإنشاء**: 2025-01-28  
**الحالة**: مطبق ✅  
**المطور**: Augment Agent
