import React from 'react'
import { Button, message } from 'antd'
import { DownloadOutlined } from '@ant-design/icons'
import * as XLSX from 'xlsx'
import dayjs from 'dayjs'
import { SafeLogger as Logger } from '../../utils/logger'
import { exportToExcel, canExport } from '../../utils/excelExportUtils'

interface ExcelExportButtonProps {
  data: any[]
  filename?: string
  sheetName?: string
  title?: string
  size?: 'small' | 'middle' | 'large'
  type?: 'default' | 'primary' | 'ghost' | 'dashed' | 'link' | 'text'
  disabled?: boolean
  loading?: boolean
  headers?: { [key: string]: string }
  excludeColumns?: string[]
  includeTimestamp?: boolean
}

const ExcelExportButton: React.FC<ExcelExportButtonProps> = ({
  data,
  filename,
  sheetName = 'التقرير',
  title = 'تصدير Excel',
  size = 'middle',
  type = 'primary',
  disabled = false,
  loading = false,
  headers = {},
  excludeColumns = [],
  includeTimestamp = true
}) => {
  const handleExport = async () => {
    try {
      // فحص إمكانية التصدير
      if (!canExport(data)) {
        message.warning('لا توجد بيانات للتصدير')
        return
      }

      // تحضير البيانات للتصدير
      const exportData = data.map(item => {
        const filteredItem: any = {}

        Object.keys(item).forEach(key => {
          if (!excludeColumns.includes(key)) {
            const headerName = headers[key] || key
            let value = item[key]

            // تنسيق التواريخ
            if (typeof value === 'string' && dayjs(value).isValid()) {
              value = dayjs(value).format('YYYY-MM-DD')
            }

            // تنسيق الأرقام
            if (typeof value === 'number') {
              value = value.toLocaleString()
            }

            filteredItem[headerName] = value
          }
        })

        return filteredItem
      })

      // استخدام دالة التصدير المحسنة
      const result = await exportToExcel(exportData, {
        fileName: filename || 'تقرير',
        sheetName: sheetName,
        includeTimestamp: includeTimestamp,
        headers: headers,
        excludeColumns: excludeColumns,
        customFormatting: true
      })

      if (!result.success) {
        message.error(result.message || 'فشل في تصدير التقرير')
      }
      
    } catch (error) {
      Logger.error('ExcelExportButton', 'خطأ في تصدير Excel:', error)
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
      message.error(`حدث خطأ أثناء تصدير التقرير: ${errorMessage}`)
    }
  }

  return (
    <Button
      type={(type as any) || 'default'}
      size={size}
      icon={<DownloadOutlined />}
      onClick={handleExport}
      disabled={disabled || !data || data.length === 0}
      loading={loading}
    >
      {title}
    </Button>
  )
}

export default ExcelExportButton
