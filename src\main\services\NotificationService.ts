import { DatabaseService } from './DatabaseService'
import { Logger } from '../utils/logger'

export interface Notification {
  id: number
  userId: number
  title: string
  message: string
  type: 'info' | 'warning' | 'error' | 'success'
  isRead: boolean
  createdAt: string
  updatedAt: string
}

export interface NotificationSettings {
  userId: number
  emailNotifications: boolean
  pushNotifications: boolean
  soundNotifications: boolean
  inventoryAlerts: boolean
  salesAlerts: boolean
  purchaseAlerts: boolean
  financialAlerts: boolean
}

export interface SystemAlert {
  id: number
  type: 'system' | 'security' | 'backup' | 'update'
  title: string
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  isActive: boolean
  createdAt: string
}

export class NotificationService {
  private db: any

  constructor(private databaseService: DatabaseService) {
    this.db = databaseService.getDatabase()
  }

  // إنشاء جداول الإشعارات
  public async createNotificationTables(): Promise<void> {
    if (!this.db) throw new Error('قاعدة البيانات غير مهيأة')

    // جدول الإشعارات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL DEFAULT 'info',
        is_read BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `)

    // جدول إعدادات الإشعارات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS notification_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER UNIQUE NOT NULL,
        email_notifications BOOLEAN DEFAULT 1,
        push_notifications BOOLEAN DEFAULT 1,
        sound_notifications BOOLEAN DEFAULT 0,
        inventory_alerts BOOLEAN DEFAULT 1,
        sales_alerts BOOLEAN DEFAULT 1,
        purchase_alerts BOOLEAN DEFAULT 1,
        financial_alerts BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `)

    // جدول تنبيهات النّام
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS system_alerts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        severity TEXT NOT NULL DEFAULT 'medium',
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    Logger.info('NotificationService', '✅ تم إنشاء جداول الإشعارات بنجاح')
  }

  // جلب الإشعارات
  public async getNotifications(userId?: number, filters?: any): Promise<{ success: boolean; data?: Notification[]; message?: string }> {
    try {
      let query = 'SELECT * FROM notifications'
      const params: any[] = []

      if (userId) {
        query += ' WHERE user_id = ?'
        params.push(userId)
      }

      if (filters?.isRead !== undefined) {
        query += userId ? ' AND' : ' WHERE'
        query += ' is_read = ?'
        params.push(filters.isRead ? 1 : 0)
      }

      query += ' ORDER BY created_at DESC'

      // إضافة LIMIT بدون معامل لتجنب مشكلة datatype mismatch
      const limit = filters?.limit || 100
      query += ` LIMIT ${limit}`

      const stmt = this.db.prepare(query)
      const notifications = stmt.all(params)

      return { success: true, data: notifications }
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في جلب الإشعارات:', error)
      return { success: false, message: 'حدث خطأ في جلب الإشعارات' }
    }
  }

  // جلب عدد الإشعارات غير المقروءة
  public async getUnreadNotificationsCount(userId: number): Promise<{ success: boolean; data?: number; message?: string }> {
    try {
      const stmt = this.db.prepare('SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0')
      const result = stmt.get([userId])

      return { success: true, data: result.count }
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في جلب عدد الإشعارات غير المقروءة:', error)
      return { success: false, message: 'حدث خطأ في جلب عدد الإشعارات' }
    }
  }

  // إنشاء إشعار جديد
  public async createNotification(notificationData: Partial<Notification>): Promise<{ success: boolean; data?: Notification; message?: string }> {
    try {
      const stmt = this.db.prepare(`
        INSERT INTO notifications (user_id, title, message, type)
        VALUES (?, ?, ?, ?)
      `)

      const result = stmt.run([
        notificationData.userId,
        notificationData.title,
        notificationData.message,
        notificationData.type || 'info'
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        const newNotification = this.db.prepare('SELECT * FROM notifications WHERE id = ?').get([result.lastInsertRowid])
        return { success: true, data: newNotification, message: 'تم إنشاء الإشعار بنجاح' }
      } else {
        return { success: false, message: 'فشل في إنشاء الإشعار' }
      }
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في إنشاء الإشعار:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الإشعار' }
    }
  }

  // تحديد الإشعار كمقروء
  public async markNotificationAsRead(notificationId: number): Promise<{ success: boolean; message?: string }> {
    try {
      const stmt = this.db.prepare(`
        UPDATE notifications 
        SET is_read = 1, updated_at = datetime('now')
        WHERE id = ?
      `)

      const result = stmt.run([notificationId])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديد الإشعار كمقروء' }
      } else {
        return { success: false, message: 'لم يتم العثور على الإشعار' }
      }
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في تحديد الإشعار كمقروء:', error)
      return { success: false, message: 'حدث خطأ في تحديد الإشعار كمقروء' }
    }
  }

  // تحديد جميع الإشعارات كمقروءة
  public async markAllNotificationsAsRead(userId: number): Promise<{ success: boolean; message?: string }> {
    try {
      const stmt = this.db.prepare(`
        UPDATE notifications 
        SET is_read = 1, updated_at = datetime('now')
        WHERE user_id = ? AND is_read = 0
      `)

      const result = stmt.run([userId])

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()
      return { success: true, message: `تم تحديد ${result.changes} إشعار كمقروء` }
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في تحديد جميع الإشعارات كمقروءة:', error)
      return { success: false, message: 'حدث خطأ في تحديد جميع الإشعارات كمقروءة' }
    }
  }

  // حذف إشعار
  public async deleteNotification(notificationId: number): Promise<{ success: boolean; message?: string }> {
    try {
      const stmt = this.db.prepare('DELETE FROM notifications WHERE id = ?')
      const result = stmt.run([notificationId])

      if (result.changes > 0) {
        return { success: true, message: 'تم حذف الإشعار بنجاح' }
      } else {
        return { success: false, message: 'لم يتم العثور على الإشعار' }
      }
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في حذف الإشعار:', error)
      return { success: false, message: 'حدث خطأ في حذف الإشعار' }
    }
  }

  // جلب إعدادات الإشعارات
  public async getNotificationSettings(userId: number): Promise<{ success: boolean; data?: NotificationSettings; message?: string }> {
    try {
      const stmt = this.db.prepare('SELECT * FROM notification_settings WHERE user_id = ?')
      let settings = stmt.get([userId])

      // إنشاء إعدادات افتراضية إذا لم تكن موجودة
      if (!settings) {
        const insertStmt = this.db.prepare(`
          INSERT INTO notification_settings (user_id, email_notifications, push_notifications, sound_notifications, inventory_alerts, sales_alerts, purchase_alerts, financial_alerts)
          VALUES (?, 1, 1, 0, 1, 1, 1, 1)
        `)
        insertStmt.run([userId])
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()

        settings = stmt.get([userId])
      }

      return { success: true, data: settings }
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في جلب إعدادات الإشعارات:', error)
      return { success: false, message: 'حدث خطأ في جلب إعدادات الإشعارات' }
    }
  }

  // تحديث إعدادات الإشعارات
  public async updateNotificationSettings(userId: number, settings: Partial<NotificationSettings>): Promise<{ success: boolean; message?: string }> {
    try {
      const stmt = this.db.prepare(`
        UPDATE notification_settings 
        SET email_notifications = ?, push_notifications = ?, sound_notifications = ?, 
            inventory_alerts = ?, sales_alerts = ?, purchase_alerts = ?, financial_alerts = ?,
            updated_at = datetime('now')
        WHERE user_id = ?
      `)

      const result = stmt.run([
        settings.emailNotifications ? 1 : 0,
        settings.pushNotifications ? 1 : 0,
        settings.soundNotifications ? 1 : 0,
        settings.inventoryAlerts ? 1 : 0,
        settings.salesAlerts ? 1 : 0,
        settings.purchaseAlerts ? 1 : 0,
        settings.financialAlerts ? 1 : 0,
        userId
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث إعدادات الإشعارات بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث إعدادات الإشعارات' }
      }
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في تحديث إعدادات الإشعارات:', error)
      return { success: false, message: 'حدث خطأ في تحديث إعدادات الإشعارات' }
    }
  }

  // جلب تنبيهات النّام
  public async getSystemAlerts(): Promise<{ success: boolean; data?: SystemAlert[]; message?: string }> {
    try {
      const stmt = this.db.prepare('SELECT * FROM system_alerts WHERE is_active = 1 ORDER BY created_at DESC')
      const alerts = stmt.all()

      return { success: true, data: alerts }
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في جلب تنبيهات النّام:', error)
      return { success: false, message: 'حدث خطأ في جلب تنبيهات النّام' }
    }
  }

  // إخفاء تنبيه النّام
  public async dismissSystemAlert(alertId: number): Promise<{ success: boolean; message?: string }> {
    try {
      const stmt = this.db.prepare('UPDATE system_alerts SET is_active = 0 WHERE id = ?')
      const result = stmt.run([alertId])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم إخفاء التنبيه بنجاح' }
      } else {
        return { success: false, message: 'لم يتم العثور على التنبيه' }
      }
    } catch (error) {
      Logger.error('NotificationService', 'خطأ في إخفاء التنبيه:', error)
      return { success: false, message: 'حدث خطأ في إخفاء التنبيه' }
    }
  }
}
