const fs = require('fs');
const path = require('path');

/**
 * سكريبت تنظيف الإصدارات القديمة
 * يحذف الملفات القديمة ويحتفظ بالنسخة الجديدة فقط
 */

class ReleaseCleanup {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.releaseDir = path.join(this.projectRoot, 'release-new');
    this.deletedFiles = [];
    this.keptFiles = [];
  }

  async cleanup() {
    console.log('🧹 بدء تنظيف الإصدارات القديمة...');
    
    try {
      if (!fs.existsSync(this.releaseDir)) {
        console.log('❌ مجلد release-new غير موجود');
        return;
      }

      // فحص الملفات الموجودة
      await this.analyzeFiles();
      
      // حذف الملفات القديمة
      await this.deleteOldFiles();
      
      // عرض النتائج
      this.displayResults();
      
    } catch (error) {
      console.error('❌ خطأ في تنظيف الإصدارات:', error.message);
      throw error;
    }
  }

  async analyzeFiles() {
    console.log('🔍 فحص الملفات الموجودة...');
    
    const files = fs.readdirSync(this.releaseDir);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (const file of files) {
      const filePath = path.join(this.releaseDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isFile()) {
        const fileDate = new Date(stats.mtime);
        fileDate.setHours(0, 0, 0, 0);
        
        const isToday = fileDate.getTime() === today.getTime();
        const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        
        console.log(`📄 ${file}`);
        console.log(`   📅 تاريخ: ${stats.mtime.toLocaleString('ar-EG')}`);
        console.log(`   📊 حجم: ${sizeInMB} MB`);
        console.log(`   ${isToday ? '✅ جديد (سيتم الاحتفاظ به)' : '❌ قديم (سيتم حذفه)'}`);
        console.log('');
      }
    }
  }

  async deleteOldFiles() {
    console.log('🗑️ حذف الملفات القديمة...');
    
    const files = fs.readdirSync(this.releaseDir);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // الملفات التي يجب الاحتفاظ بها دائماً
    const keepAlways = [
      'builder-effective-config.yaml',
      'builder-debug.yml'
    ];

    for (const file of files) {
      const filePath = path.join(this.releaseDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isFile()) {
        const fileDate = new Date(stats.mtime);
        fileDate.setHours(0, 0, 0, 0);
        
        const isToday = fileDate.getTime() === today.getTime();
        const shouldKeep = isToday || keepAlways.includes(file);
        
        if (shouldKeep) {
          this.keptFiles.push({
            name: file,
            size: (stats.size / (1024 * 1024)).toFixed(2),
            reason: isToday ? 'جديد' : 'ملف تكوين'
          });
        } else {
          try {
            fs.unlinkSync(filePath);
            this.deletedFiles.push({
              name: file,
              size: (stats.size / (1024 * 1024)).toFixed(2),
              date: stats.mtime.toLocaleString('ar-EG')
            });
            console.log(`🗑️ تم حذف: ${file}`);
          } catch (error) {
            console.error(`❌ فشل حذف ${file}:`, error.message);
          }
        }
      } else if (stats.isDirectory()) {
        // فحص المجلدات القديمة
        const dirDate = new Date(stats.mtime);
        dirDate.setHours(0, 0, 0, 0);
        
        const isToday = dirDate.getTime() === today.getTime();
        
        if (!isToday && file === 'win-unpacked') {
          // حذف مجلد win-unpacked القديم
          try {
            fs.rmSync(filePath, { recursive: true, force: true });
            this.deletedFiles.push({
              name: file + '/ (مجلد)',
              size: 'متغير',
              date: stats.mtime.toLocaleString('ar-EG')
            });
            console.log(`🗑️ تم حذف المجلد: ${file}`);
          } catch (error) {
            console.error(`❌ فشل حذف المجلد ${file}:`, error.message);
          }
        } else {
          this.keptFiles.push({
            name: file + '/ (مجلد)',
            size: 'متغير',
            reason: isToday ? 'جديد' : 'مطلوب'
          });
        }
      }
    }
  }

  displayResults() {
    console.log('\n📊 نتائج التنظيف:\n');
    
    // الملفات المحذوفة
    if (this.deletedFiles.length > 0) {
      console.log('🗑️ الملفات المحذوفة:');
      this.deletedFiles.forEach(file => {
        console.log(`  ❌ ${file.name} (${file.size} MB) - ${file.date}`);
      });
      console.log('');
    }
    
    // الملفات المحتفظ بها
    if (this.keptFiles.length > 0) {
      console.log('✅ الملفات المحتفظ بها:');
      this.keptFiles.forEach(file => {
        console.log(`  ✅ ${file.name} (${file.size} MB) - ${file.reason}`);
      });
      console.log('');
    }
    
    // الملخص
    const totalDeleted = this.deletedFiles.length;
    const totalKept = this.keptFiles.length;
    
    console.log(`📈 الملخص:`);
    console.log(`  🗑️ تم حذف: ${totalDeleted} عنصر`);
    console.log(`  ✅ تم الاحتفاظ بـ: ${totalKept} عنصر`);
    
    if (totalDeleted > 0) {
      console.log('\n✅ تم تنظيف مجلد release-new بنجاح!');
      console.log('💡 الآن يحتوي المجلد على النسخة الجديدة فقط');
    } else {
      console.log('\n💡 لا توجد ملفات قديمة للحذف');
    }
  }
}

// تشغيل التنظيف
if (require.main === module) {
  const cleanup = new ReleaseCleanup();
  cleanup.cleanup().catch(error => {
    console.error('❌ فشل التنظيف:', error);
    process.exit(1);
  });
}

module.exports = ReleaseCleanup;
