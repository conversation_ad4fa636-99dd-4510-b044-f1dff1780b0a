import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { Logger } from '../utils/logger'
// import { sanitizeFormData, validateRequiredIds } from '../utils/dataTypeUtils' // غير مستخدم حالياً
import { SalesService } from './SalesService'
import { PurchaseService } from './PurchaseService'
import { ProductionService } from './ProductionService'
import { InventoryService } from './InventoryService'
import { FinancialService } from './FinancialService'

// أنواع الفواتير
export type InvoiceType = 'sales' | 'purchase' | 'paint' | 'service'
export type InvoiceStatus = 'draft' | 'pending' | 'sent' | 'paid' | 'partial' | 'overdue' | 'cancelled'
export type EntityType = 'customer' | 'supplier'

// واجهة الفاتورة الموحدة
export interface UniversalInvoice {
  id: number
  invoice_number: string
  invoice_type: InvoiceType
  entity_id: number
  entity_name: string
  entity_type: EntityType
  invoice_date: string
  due_date?: string
  status: InvoiceStatus
  subtotal: number
  discount_amount: number
  tax_amount: number
  total_amount: number
  paid_amount: number
  remaining_amount: number
  payment_method?: string
  notes?: string
  created_by: number
  created_at: string
  updated_at?: string
  items?: UniversalInvoiceItem[]
  metadata?: Record<string, any>
}

// واجهة صنف الفاتورة الموحدة
export interface UniversalInvoiceItem {
  id: number
  invoice_id: number
  item_id: number
  item_name: string
  warehouse_id: number
  warehouse_name?: string
  quantity: number
  unit_price: number
  total_price: number
  notes?: string
}

// بيانات إنشاء فاتورة موحدة
export interface CreateUniversalInvoiceData {
  invoice_type: InvoiceType
  entity_id: number
  order_id?: number
  invoice_date: string
  due_date?: string
  discount_amount?: number
  tax_amount?: number
  total_amount?: number
  final_amount?: number
  payment_method?: string
  notes?: string
  items: Array<{
    item_id: number
    warehouse_id: number
    quantity: number
    unit_price: number
  }>
  metadata?: Record<string, any>
}

// فلاتر البحث الموحدة
export interface UniversalInvoiceFilters {
  invoice_type?: InvoiceType
  entity_id?: number
  entity_type?: EntityType
  status?: InvoiceStatus[]
  date_from?: string
  date_to?: string
  amount_from?: number
  amount_to?: string
  search_text?: string
  page?: number
  page_size?: number
}

// إحصائيات موحدة
export interface UniversalInvoiceStatistics {
  total_invoices: number
  total_amount: number
  paid_amount: number
  remaining_amount: number
  by_status: Record<InvoiceStatus, { count: number; amount: number }>
  by_type: Record<InvoiceType, { count: number; amount: number }>
}

export class UniversalInvoiceService {
  private static instance: UniversalInvoiceService
  private db: any
  private salesService: SalesService
  private purchaseService: PurchaseService
  private productionService: ProductionService
  private inventoryService: InventoryService
  private financialService: FinancialService

  private constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
    this.salesService = SalesService.getInstance()
    this.purchaseService = PurchaseService.getInstance()
    this.productionService = ProductionService.getInstance()
    this.inventoryService = InventoryService.getInstance()
    this.financialService = FinancialService.getInstance()
  }

  public static getInstance(): UniversalInvoiceService {
    if (!UniversalInvoiceService.instance) {
      UniversalInvoiceService.instance = new UniversalInvoiceService()
    }
    return UniversalInvoiceService.instance
  }

  // إنشاء فاتورة موحدة
  public async createInvoice(data: CreateUniversalInvoiceData): Promise<ApiResponse> {
    try {
      Logger.info('UniversalInvoiceService', `إنشاء فاتورة ${data.invoice_type}`)

      // التحقق من صحة البيانات
      const validation = this.validateInvoiceData(data)
      if (!validation.success) {
        return validation
      }

      // توجيه الطلب للخدمة المناسبة
      switch (data.invoice_type) {
        case 'sales': {
          const salesData = {
            ...data,
            customer_id: data.entity_id
          }
          return await this.salesService.createSalesInvoice(salesData as any)
        }
        case 'purchase': {
          const purchaseData = {
            ...data,
            supplier_id: data.entity_id
          }
          return await this.purchaseService.createPurchaseInvoice(purchaseData as any)
        }
        case 'paint': {
          const paintData = {
            ...data,
            customer_id: data.entity_id,
            total_amount: data.total_amount || 0,
            final_amount: data.final_amount || data.total_amount || 0
          }
          return await this.productionService.createPaintInvoice(paintData as any)
        }
        case 'service':
          return await this.createServiceInvoice(data)
        default:
          return { success: false, message: 'نوع فاتورة غير مدعوم' }
      }
    } catch (error) {
      Logger.error('UniversalInvoiceService', 'خطأ في إنشاء الفاتورة:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الفاتورة' }
    }
  }

  // تحديث فاتورة موحدة
  public async updateInvoice(
    invoiceType: InvoiceType,
    invoiceId: number,
    data: Partial<CreateUniversalInvoiceData>
  ): Promise<ApiResponse> {
    try {
      Logger.info('UniversalInvoiceService', `تحديث فاتورة ${invoiceType} رقم ${invoiceId}`)

      switch (invoiceType) {
        case 'sales':
          return await this.salesService.updateSalesInvoice(invoiceId, data as any)
        case 'purchase': {
          const purchaseUpdateData = {
            ...data,
            supplier_id: data.entity_id || 0
          }
          return await this.purchaseService.updatePurchaseInvoice(invoiceId, purchaseUpdateData as any)
        }
        case 'paint':
          // ProductionService لا يحتوي على updatePaintInvoice، سنستخدم createPaintInvoice مؤقتاً
          return { success: false, message: 'تحديث فواتير الدهان غير مدعوم حالياً' }
        case 'service':
          return await this.updateServiceInvoice(invoiceId, data)
        default:
          return { success: false, message: 'نوع فاتورة غير مدعوم' }
      }
    } catch (error) {
      Logger.error('UniversalInvoiceService', 'خطأ في تحديث الفاتورة:', error)
      return { success: false, message: 'حدث خطأ في تحديث الفاتورة' }
    }
  }

  // حذف فاتورة موحدة
  public async deleteInvoice(invoiceType: InvoiceType, invoiceId: number): Promise<ApiResponse> {
    try {
      Logger.info('UniversalInvoiceService', `حذف فاتورة ${invoiceType} رقم ${invoiceId}`)

      switch (invoiceType) {
        case 'sales':
          return await this.salesService.deleteSalesInvoice(invoiceId)
        case 'purchase':
          return await this.purchaseService.deletePurchaseInvoice(invoiceId)
        case 'paint':
          // ProductionService لا يحتوي على deletePaintInvoice
          return { success: false, message: 'حذف فواتير الدهان غير مدعوم حالياً' }
        case 'service':
          return await this.deleteServiceInvoice(invoiceId)
        default:
          return { success: false, message: 'نوع فاتورة غير مدعوم' }
      }
    } catch (error) {
      Logger.error('UniversalInvoiceService', 'خطأ في حذف الفاتورة:', error)
      return { success: false, message: 'حدث خطأ في حذف الفاتورة' }
    }
  }

  // جلب الفواتير مع الفلترة
  public async getInvoices(filters: UniversalInvoiceFilters = {}): Promise<ApiResponse> {
    try {
      Logger.info('UniversalInvoiceService', 'جلب الفواتير مع الفلترة')

      const invoices: UniversalInvoice[] = []

      // جلب فواتير المبيعات
      if (!filters.invoice_type || filters.invoice_type === 'sales') {
        try {
          const salesResult = await this.salesService.getSalesInvoices()
          if (Array.isArray(salesResult)) {
            const salesInvoices = await Promise.all(
              salesResult.map(async (invoice) => {
                const mappedInvoice = this.mapToUniversalInvoice(invoice)
                // تحميل تفاصيل الأصناف للفاتورة
                try {
                  const items = await this.salesService.getSalesInvoiceItems(invoice.id)
                  mappedInvoice.items = items.map(item => ({
                    id: item.id,
                    invoice_id: item.invoice_id,
                    item_id: item.item_id,
                    item_name: item.item_name || 'غير محدد',
                    warehouse_id: item.warehouse_id,
                    warehouse_name: item.warehouse_name,
                    quantity: item.quantity,
                    unit_price: item.unit_price,
                    total_price: item.total_price,
                    notes: item.notes
                  }))
                } catch (itemError) {
                  Logger.error('UniversalInvoiceService', `خطأ في تحميل أصناف فاتورة المبيعات ${invoice.id}:`, itemError)
                  mappedInvoice.items = []
                }
                return mappedInvoice
              })
            )
            invoices.push(...salesInvoices)
          }
        } catch (error) {
          Logger.error('UniversalInvoiceService', 'خطأ في جلب فواتير المبيعات:', error)
        }
      }

      // جلب فواتير المشتريات
      if (!filters.invoice_type || filters.invoice_type === 'purchase') {
        try {
          const purchaseResult = await this.purchaseService.getPurchaseInvoices()
          if (Array.isArray(purchaseResult)) {
            const purchaseInvoices = await Promise.all(
              purchaseResult.map(async (invoice) => {
                const mappedInvoice = this.mapToUniversalInvoice(invoice)
                // تحميل تفاصيل الأصناف للفاتورة
                try {
                  const items = await this.purchaseService.getPurchaseInvoiceItems(invoice.id)
                  mappedInvoice.items = items.map(item => ({
                    id: item.id,
                    invoice_id: item.invoice_id,
                    item_id: item.item_id,
                    item_name: item.item_name || 'غير محدد',
                    warehouse_id: item.warehouse_id,
                    warehouse_name: item.warehouse_name,
                    quantity: item.quantity,
                    unit_price: item.unit_price,
                    total_price: item.total_price,
                    notes: item.notes
                  }))
                } catch (itemError) {
                  Logger.error('UniversalInvoiceService', `خطأ في تحميل أصناف فاتورة المشتريات ${invoice.id}:`, itemError)
                  mappedInvoice.items = []
                }
                return mappedInvoice
              })
            )
            invoices.push(...purchaseInvoices)
          }
        } catch (error) {
          Logger.error('UniversalInvoiceService', 'خطأ في جلب فواتير المشتريات:', error)
        }
      }

      // جلب فواتير الدهان
      if (!filters.invoice_type || filters.invoice_type === 'paint') {
        const paintResult = await this.productionService.getPaintInvoices()
        if (paintResult.success && paintResult.data) {
          const paintInvoices = paintResult.data.map(this.mapToUniversalInvoice)
          invoices.push(...paintInvoices)
        }
      }

      // تطبيق الفلاتر
      const filteredInvoices = this.applyFilters(invoices, filters)

      // تطبيق الصفحات
      const paginatedResult = this.applyPagination(filteredInvoices, filters)

      return {
        success: true,
        data: paginatedResult
      }
    } catch (error) {
      Logger.error('UniversalInvoiceService', 'خطأ في جلب الفواتير:', error)
      return { success: false, message: 'حدث خطأ في جلب الفواتير' }
    }
  }

  // جلب إحصائيات موحدة
  public async getStatistics(filters: UniversalInvoiceFilters = {}): Promise<ApiResponse> {
    try {
      Logger.info('UniversalInvoiceService', 'جلب إحصائيات الفواتير')

      const invoicesResult = await this.getInvoices(filters)
      if (!invoicesResult.success) {
        return invoicesResult
      }

      const invoices = invoicesResult.data.invoices || []
      const statistics = this.calculateStatistics(invoices)

      return {
        success: true,
        data: statistics
      }
    } catch (error) {
      Logger.error('UniversalInvoiceService', 'خطأ في جلب الإحصائيات:', error)
      return { success: false, message: 'حدث خطأ في جلب الإحصائيات' }
    }
  }

  // توليد رقم فاتورة موحد
  public async generateInvoiceNumber(invoiceType: InvoiceType): Promise<string> {
    try {
      const prefix = this.getInvoicePrefix(invoiceType)
      const year = new Date().getFullYear()
      const month = String(new Date().getMonth() + 1).padStart(2, '0')

      // جلب آخر رقم فاتورة لهذا النوع
      const lastNumber = this.getLastInvoiceNumber(invoiceType, year, parseInt(month))
      const nextNumber = lastNumber + 1

      return `${prefix}-${year}${month}-${String(nextNumber).padStart(4, '0')}`
    } catch (error) {
      Logger.error('UniversalInvoiceService', 'خطأ في توليد رقم الفاتورة:', error)
      throw error
    }
  }

  // دوال مساعدة خاصة
  private validateInvoiceData(data: CreateUniversalInvoiceData): ApiResponse {
    if (!data.invoice_type) {
      return { success: false, message: 'نوع الفاتورة مطلوب' }
    }

    if (!data.entity_id) {
      return { success: false, message: 'العميل أو المورد مطلوب' }
    }

    if (!data.invoice_date) {
      return { success: false, message: 'تاريخ الفاتورة مطلوب' }
    }

    if (!data.items || data.items.length === 0) {
      return { success: false, message: 'يجب إضافة صنف واحد على الأقل' }
    }

    return { success: true }
  }

  private mapToUniversalInvoice(invoice: any): UniversalInvoice {
    return {
      id: invoice.id,
      invoice_number: invoice.invoice_number,
      invoice_type: this.detectInvoiceType(invoice),
      entity_id: invoice.customer_id || invoice.supplier_id,
      entity_name: invoice.customer_name || invoice.supplier_name,
      entity_type: invoice.customer_id ? 'customer' : 'supplier',
      invoice_date: invoice.invoice_date,
      due_date: invoice.due_date,
      status: invoice.status,
      subtotal: invoice.subtotal || 0,
      discount_amount: invoice.discount_amount || 0,
      tax_amount: invoice.tax_amount || 0,
      total_amount: invoice.total_amount || 0,
      paid_amount: invoice.paid_amount || 0,
      remaining_amount: invoice.remaining_amount || 0,
      payment_method: invoice.payment_method,
      notes: invoice.notes,
      created_by: invoice.created_by,
      created_at: invoice.created_at,
      updated_at: invoice.updated_at,
      items: invoice.items,
      metadata: invoice.metadata
    }
  }

  private detectInvoiceType(invoice: any): InvoiceType {
    if (invoice.customer_id && invoice.total_area) return 'paint'
    if (invoice.customer_id) return 'sales'
    if (invoice.supplier_id) return 'purchase'
    return 'service'
  }

  private applyFilters(invoices: UniversalInvoice[], filters: UniversalInvoiceFilters): UniversalInvoice[] {
    let filtered = [...invoices]

    if (filters.entity_id) {
      filtered = filtered.filter(inv => inv.entity_id === filters.entity_id)
    }

    if (filters.status && filters.status.length > 0) {
      filtered = filtered.filter(inv => filters.status?.includes(inv.status))
    }

    if (filters.date_from) {
      filtered = filtered.filter(inv => inv.invoice_date >= (filters.date_from || ''))
    }

    if (filters.date_to) {
      filtered = filtered.filter(inv => inv.invoice_date <= (filters.date_to || ''))
    }

    if (filters.search_text) {
      const searchLower = filters.search_text.toLowerCase()
      filtered = filtered.filter(inv =>
        inv.invoice_number.toLowerCase().includes(searchLower) ||
        inv.entity_name.toLowerCase().includes(searchLower)
      )
    }

    return filtered
  }

  private applyPagination(invoices: UniversalInvoice[], filters: UniversalInvoiceFilters) {
    const page = filters.page || 1
    const pageSize = filters.page_size || 50
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize

    return {
      invoices: invoices.slice(startIndex, endIndex),
      total_count: invoices.length,
      page,
      page_size: pageSize,
      total_pages: Math.ceil(invoices.length / pageSize)
    }
  }

  private calculateStatistics(invoices: UniversalInvoice[]): UniversalInvoiceStatistics {
    const stats: UniversalInvoiceStatistics = {
      total_invoices: invoices.length,
      total_amount: 0,
      paid_amount: 0,
      remaining_amount: 0,
      by_status: {} as any,
      by_type: {} as any
    }

    invoices.forEach(invoice => {
      stats.total_amount += invoice.total_amount
      stats.paid_amount += invoice.paid_amount
      stats.remaining_amount += invoice.remaining_amount

      // إحصائيات حسب الحالة
      if (!stats.by_status[invoice.status]) {
        stats.by_status[invoice.status] = { count: 0, amount: 0 }
      }
      stats.by_status[invoice.status].count++
      stats.by_status[invoice.status].amount += invoice.total_amount

      // إحصائيات حسب النوع
      if (!stats.by_type[invoice.invoice_type]) {
        stats.by_type[invoice.invoice_type] = { count: 0, amount: 0 }
      }
      stats.by_type[invoice.invoice_type].count++
      stats.by_type[invoice.invoice_type].amount += invoice.total_amount
    })

    return stats
  }

  private getInvoicePrefix(invoiceType: InvoiceType): string {
    const prefixes = {
      sales: 'SAL',
      purchase: 'PUR',
      paint: 'PAI',
      service: 'SRV'
    }
    return prefixes[invoiceType]
  }

  private getLastInvoiceNumber(_invoiceType: InvoiceType, _year: number, _month: number): number {
    // تنفيذ جلب آخر رقم فاتورة من قاعدة البيانات
    // هذا مثال مبسط
    return 0
  }

  // دوال فواتير الخدمات (مؤقتة)
  private async createServiceInvoice(_data: CreateUniversalInvoiceData): Promise<ApiResponse> {
    // تنفيذ إنشاء فاتورة خدمة
    return { success: false, message: 'فواتير الخدمات قيد التطوير' }
  }

  private async updateServiceInvoice(_invoiceId: number, _data: any): Promise<ApiResponse> {
    // تنفيذ تحديث فاتورة خدمة
    return { success: false, message: 'فواتير الخدمات قيد التطوير' }
  }

  private async deleteServiceInvoice(_invoiceId: number): Promise<ApiResponse> {
    // تنفيذ حذف فاتورة خدمة
    return { success: false, message: 'فواتير الخدمات قيد التطوير' }
  }
}
