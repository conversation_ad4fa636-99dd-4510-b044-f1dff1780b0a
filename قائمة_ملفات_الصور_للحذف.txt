═══════════════════════════════════════════════════════════════
  قائمة سريعة بجميع ملفات ومجلدات نظام الصور المطلوب حذفها
═══════════════════════════════════════════════════════════════

📁 المجلدات الكاملة (احذف المجلد بالكامل):
═══════════════════════════════════════════════════════════════

src/renderer/src/components/images/
src/renderer/src/services/images/
src/renderer/src/hooks/images/


📄 الملفات الفردية في Common:
═══════════════════════════════════════════════════════════════

src/renderer/src/components/common/ImageGallery.tsx
src/renderer/src/components/common/ImageService.ts
src/renderer/src/components/common/ImageSettings.tsx
src/renderer/src/components/common/ImageWithFallback.tsx
src/renderer/src/components/common/CheckImageManager.tsx
src/renderer/src/components/common/ImagePrintTest.tsx
src/renderer/src/components/common/UniversalImagePrint.tsx
src/renderer/src/components/common/UniversalImagePrintDemo.tsx
src/renderer/src/components/common/InvoiceImagesPrintButton.tsx
src/renderer/src/components/common/ProductionOrderImagesPrintButton.tsx
src/renderer/src/components/common/CustomerImagesPrintButton.tsx
src/renderer/src/components/common/SmartImagePrintButton.tsx
src/renderer/src/components/common/README_IMAGE_PRINT.md


📄 ملفات أخرى:
═══════════════════════════════════════════════════════════════

src/renderer/src/components/examples/ImageSystemExample.tsx
src/renderer/src/components/debug/ImageDebugger.tsx
src/renderer/src/components/inventory/EnhancedItemImagePrint.tsx
src/renderer/src/components/production/furniture/FurnitureImageService.ts
src/renderer/src/services/UniversalImageService.ts
src/renderer/src/utils/imageUtils.ts
src/main/handlers/imageHandlers.ts


📄 ملفات الاختبار:
═══════════════════════════════════════════════════════════════

src/test/image-print-system-test.ts
src/test/run-image-print-test.ts


📄 ملفات التوثيق:
═══════════════════════════════════════════════════════════════

docs/IMAGE_SYSTEM_DATABASE_INTEGRATION_COMPLETE.md


🗄️ جداول قاعدة البيانات (SQL):
═══════════════════════════════════════════════════════════════

DROP TABLE IF EXISTS unified_images;
DROP TABLE IF EXISTS item_images;
DROP TABLE IF EXISTS production_order_images;
DROP TABLE IF EXISTS customer_images;
DROP TABLE IF EXISTS check_images;


📂 مجلدات نظام الملفات:
═══════════════════════════════════════════════════════════════

userData/images/


═══════════════════════════════════════════════════════════════
  إجمالي العناصر المطلوب حذفها
═══════════════════════════════════════════════════════════════

✅ 3 مجلدات كاملة
✅ 13 ملف في common
✅ 7 ملفات أخرى
✅ 2 ملف اختبار
✅ 1 ملف توثيق
✅ 5 جداول قاعدة بيانات
✅ 1 مجلد نظام ملفات

═══════════════════════════════════════════════════════════════
  المجموع: ~32 ملف + 4 مجلدات + 5 جداول
═══════════════════════════════════════════════════════════════


⚠️ ملاحظة مهمة:
═══════════════════════════════════════════════════════════════

لا تحذف الملفات التالية (متعلقة بالشعار):
❌ AdvancedLogoManager.tsx
❌ AnimatedLogo.tsx
❌ LogoManager.tsx
❌ logo.svg
❌ default-logo.svg


✅ خطوات الحذف الموصى بها:
═══════════════════════════════════════════════════════════════

1. عمل نسخة احتياطية (Backup)
2. إنشاء فرع جديد (Branch)
3. حذف المجلدات الثلاثة الكاملة
4. حذف الملفات الفردية
5. حذف جداول قاعدة البيانات
6. حذف مجلد الصور من userData
7. تعديل الملفات التي تستورد هذه المكونات
8. اختبار التطبيق
9. التأكد من عدم وجود أخطاء


═══════════════════════════════════════════════════════════════
  نهاية القائمة
═══════════════════════════════════════════════════════════════

