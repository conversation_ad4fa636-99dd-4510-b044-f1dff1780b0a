// تم نقل تعريفات Window إلى global.d.ts لتجنب التضارب

// تعريف أنواع IPC للطباعة والإعدادات
export interface IPCResponse<T = any> {
  success: boolean
  data?: T
  message?: string
}

export interface SettingItem {
  key: string
  value: string
  description?: string
}

export interface PrintTemplate {
  id: string
  name: string
  description?: string
  type: 'invoice' | 'receipt' | 'report' | 'certificate' | 'custom'
  category?: string
  is_default: boolean
  is_custom: boolean
  is_active: boolean
  template_data: string
  settings?: any
  created_at: string
  updated_at: string
}

// تعريف قنوات IPC
export type IPCChannels =
  | 'get-settings'
  | 'save-settings'
  | 'get-print-templates'
  | 'save-print-template'
  | 'delete-print-template'
  | 'find-database-files'
  | 'read-file-as-base64'

export {}
