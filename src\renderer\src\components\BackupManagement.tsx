import React, { useState, useEffect, useCallback } from 'react'
import {
  Card, Button, Space, message, Modal, Form, Input, Select,
  Upload, Progress, Alert, Divider, Row, Col, Statistic, Tag,
  Typography, Tooltip, Popconfirm, Switch, notification
} from 'antd'
import { SafeLogger as Logger } from '../utils/logger'
import {
  DatabaseOutlined, DownloadOutlined, DeleteOutlined,
  ClockCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined,
  ReloadOutlined, SettingOutlined, HistoryOutlined, CloudDownloadOutlined,
  SafetyOutlined, CompressOutlined, PictureOutlined,
  ImportOutlined, ExportOutlined} from '@ant-design/icons'
import styled from 'styled-components'
import dayjs from 'dayjs'

const { Title, Text } = Typography

const StyledCard = styled(Card)`
  .ant-card-head {
    background: linear-gradient(135deg, #0078D4 0%, #106EBE 100%);
    border-bottom: none;
    
    .ant-card-head-title {
      color: white;
      font-weight: bold;
    }
  }
`

const BackupCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }
`

const StatCard = styled(Card)`
  text-align: center;
  border-radius: 8px;
  
  .ant-statistic-title {
    color: #666;
    font-size: 14px;
  }
  
  .ant-statistic-content {
    color: #0078D4;
  }
`

interface BackupManagementProps {
  // Component props interface
  [key: string]: any
}

interface BackupFile {
  id: string
  filename: string
  size: number
  created_at: string
  type: 'manual' | 'automatic'
  status: 'completed' | 'failed' | 'in_progress'
  description?: string
}

const BackupManagement: React.FC<BackupManagementProps> = () => {
  const [loading, setLoading] = useState(false)
  const [backups, setBackups] = useState<BackupFile[]>([])
  const [backupProgress, setBackupProgress] = useState(0)
  const [isBackingUp, setIsBackingUp] = useState(false)
  const [isRestoring, setIsRestoring] = useState(false)
  const [restoreProgress, setRestoreProgress] = useState(0)
  const [settings, setSettings] = useState<any>({})
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [form] = Form.useForm()

  // حالات النسخ الاحتياطي المحسن
  const [backupOptions, setBackupOptions] = useState({
    includeImages: true,
    compress: true,
    description: ''
  })
  const [verifyingBackup, setVerifyingBackup] = useState<string | null>(null)
  const [exportingBackup, setExportingBackup] = useState<string | null>(null)
  const [importingBackup, setImportingBackup] = useState(false)
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false)

  const loadBackups = useCallback(async () => {
    try {
      setLoading(true)
      if (window.electronAPI) {
        const result = await window.electronAPI.getBackups()
        if (result.success) {
          setBackups(result.data)
        }
      } else {
        // بيانات وهمية للتطوير
        const mockBackups: BackupFile[] = [
          {
            id: '1',
            filename: 'backup_2024-06-25_14-30.db',
            size: 2048576,
            created_at: '2024-06-25T14:30:00Z',
            type: 'automatic',
            status: 'completed',
            description: 'نسخة احتياطية تلقائية يومية'
          },
          {
            id: '2',
            filename: 'backup_2024-06-24_09-15.db',
            size: 1987654,
            created_at: '2024-06-24T09:15:00Z',
            type: 'manual',
            status: 'completed',
            description: 'نسخة احتياطية يدوية قبل التحديث'
          },
          {
            id: '3',
            filename: 'backup_2024-06-23_18-45.db',
            size: 1876543,
            created_at: '2024-06-23T18:45:00Z',
            type: 'automatic',
            status: 'completed'
          }
        ]
        setBackups(mockBackups)
      }
    } catch (error) {
      Logger.error('BackupManagement', 'خطأ في تحميل النسخ الاحتياطية:', error)
      message.error('فشل في تحميل النسخ الاحتياطية')
    } finally {
      setLoading(false)
    }
  }, [])

  const loadBackupSettings = useCallback(async () => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.getBackupSettings()
        if (result.success) {
          setSettings(result.data)
          form.setFieldsValue(result.data)
        }
      } else {
        // إعدادات وهمية
        const mockSettings = {
          auto_backup_enabled: true,
          backup_frequency: 'daily',
          backup_time: '02:00',
          max_backups: 30,
          backup_location: 'default'
        }
        setSettings(mockSettings)
        form.setFieldsValue(mockSettings)
      }
    } catch (error) {
      Logger.error('BackupManagement', 'خطأ في تحميل إعدادات النسخ الاحتياطي:', error)
    }
  }, [form])

  useEffect(() => {
    loadBackups()
    loadBackupSettings()
  }, [loadBackups, loadBackupSettings])

  const createBackup = async (options = backupOptions) => {
    try {
      setIsBackingUp(true)
      setBackupProgress(0)

      // محاكاة تقدم النسخ الاحتياطي
      const progressInterval = setInterval(() => {
        setBackupProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + Math.random() * 15
        })
      }, 300)

      if (window.electronAPI) {
        const result = await window.electronAPI.createBackup(options)
        clearInterval(progressInterval)
        setBackupProgress(100)

        if (result.success) {
          message.success('تم إنشاء النسخة الاحتياطية بنجاح')
          loadBackups()

          const backupInfo = result.data
          notification.success({
            message: 'نسخة احتياطية جديدة',
            description: (
              <div>
                <div>الملف: {backupInfo?.fileName || 'غير محدد'}</div>
                <div>الحجم: {formatFileSize(backupInfo?.fileSize || 0)}</div>
                <div>المسار: {backupInfo?.filePath || 'غير محدد'}</div>
                {options.includeImages && <div>✓ تشمل الصور</div>}
                {options.compress && <div>✓ مضغوطة</div>}
              </div>
            ),
            placement: 'topRight',
            duration: 8
          })
        } else {
          message.error(result.message || 'فشل في إنشاء النسخة الاحتياطية')
        }
      } else {
        // محاكاة النجاح
        setTimeout(() => {
          clearInterval(progressInterval)
          setBackupProgress(100)
          message.success('تم إنشاء النسخة الاحتياطية بنجاح')

          // إضافة نسخة احتياطية جديدة للقائمة
          const newBackup: BackupFile = {
            id: Date.now().toString(),
            filename: 'backup_' + dayjs().format('YYYY-MM-DD_HH-mm') + '.' + (options.compress ? 'zip' : 'db'),
            size: Math.floor(Math.random() * 3000000) + 1000000,
            created_at: new Date().toISOString(),
            type: 'manual',
            status: 'completed',
            description: options.description || 'نسخة احتياطية يدوية'
          }
          setBackups(prev => [newBackup, ...prev])
        }, 2000)
      }
    } catch (error) {
      Logger.error('BackupManagement', 'خطأ في إنشاء النسخة الاحتياطية:', error)
      message.error('حدث خطأ أثناء إنشاء النسخة الاحتياطية')
    } finally {
      setTimeout(() => {
        setIsBackingUp(false)
        setBackupProgress(0)
      }, 1000)
    }
  }

  const restoreBackup = async (backupId: string) => {
    try {
      setIsRestoring(true)
      setRestoreProgress(0)

      // محاكاة تقدم الاستعادة
      const progressInterval = setInterval(() => {
        setRestoreProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + Math.random() * 10
        })
      }, 300)

      if (window.electronAPI) {
        const result = await window.electronAPI.restoreBackup(backupId)
        clearInterval(progressInterval)
        setRestoreProgress(100)
        
        if (result.success) {
          notification.success({
            message: 'تم استعادة النسخة الاحتياطية بنجاح',
            description: 'سيتم إعادة تشغيل التطبيق لتطبيق التغييرات'
          })
          
          // إعادة تشغيل التطبيق بعد 3 ثوان
          setTimeout(() => {
            window.location.reload()
          }, 3000)
        } else {
          message.error(result.message || 'فشل في استعادة النسخة الاحتياطية')
        }
      } else {
        // محاكاة النجاح
        setTimeout(() => {
          clearInterval(progressInterval)
          setRestoreProgress(100)
          notification.success({
            message: 'تم استعادة النسخة الاحتياطية بنجاح',
            description: 'سيتم إعادة تشغيل التطبيق لتطبيق التغييرات'
          })
        }, 3000)
      }
    } catch (error) {
      Logger.error('BackupManagement', 'خطأ في استعادة النسخة الاحتياطية:', error)
      message.error('حدث خطأ أثناء استعادة النسخة الاحتياطية')
    } finally {
      setTimeout(() => {
        setIsRestoring(false)
        setRestoreProgress(0)
      }, 1000)
    }
  }

  const deleteBackup = async (backupId: string) => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.deleteBackup(backupId)
        if (result.success) {
          message.success('تم حذف النسخة الاحتياطية بنجاح')
          loadBackups()
        } else {
          message.error(result.message || 'فشل في حذف النسخة الاحتياطية')
        }
      } else {
        // محاكاة الحذف
        setBackups(prev => prev.filter(backup => backup.id !== backupId))
        message.success('تم حذف النسخة الاحتياطية بنجاح')
      }
    } catch (error) {
      Logger.error('BackupManagement', 'خطأ في حذف النسخة الاحتياطية:', error)
      message.error('حدث خطأ أثناء حذف النسخة الاحتياطية')
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'failed':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
      case 'in_progress':
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />
      default:
        return <ClockCircleOutlined />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'مكتملة'
      case 'failed':
        return 'فاشلة'
      case 'in_progress':
        return 'قيد التنفيذ'
      default:
        return 'غير معروف'
    }
  }

  const getTypeColor = (type: string) => {
    return type === 'automatic' ? 'blue' : 'green'
  }

  const getTypeText = (type: string) => {
    return type === 'automatic' ? 'تلقائية' : 'يدوية'
  }

  // دوال النسخ الاحتياطي المحسنة
  const verifyBackup = async (backupId: string) => {
    try {
      setVerifyingBackup(backupId)

      if (window.electronAPI && window.electronAPI.verifyBackup) {
        const result = await window.electronAPI.verifyBackup(backupId)
        if (result.success) {
          notification.success({
            message: 'تم التحقق من النسخة الاحتياطية',
            description: 'النسخة الاحتياطية سليمة ويمكن استعادتها بأمان'
          })
        } else {
          notification.error({
            message: 'خطأ في النسخة الاحتياطية',
            description: result.message || 'النسخة الاحتياطية تالفة أو غير مكتملة'
          })
        }
      } else {
        // محاكاة التحقق
        setTimeout(() => {
          notification.success({
            message: 'تم التحقق من النسخة الاحتياطية',
            description: 'النسخة الاحتياطية سليمة ويمكن استعادتها بأمان'
          })
        }, 2000)
      }
    } catch (error) {
      Logger.error('BackupManagement', 'خطأ في التحقق من النسخة الاحتياطية:', error)
      message.error('فشل في التحقق من النسخة الاحتياطية')
    } finally {
      setVerifyingBackup(null)
    }
  }

  const exportBackup = async (backupId: string) => {
    try {
      setExportingBackup(backupId)

      if (window.electronAPI && window.electronAPI.exportBackup) {
        const result = await window.electronAPI.exportBackup(backupId)
        if (result.success) {
          message.success('تم تصدير النسخة الاحتياطية بنجاح')
        } else {
          message.error(result.message || 'فشل في تصدير النسخة الاحتياطية')
        }
      } else {
        // محاكاة التصدير
        setTimeout(() => {
          message.success('تم تصدير النسخة الاحتياطية بنجاح')
        }, 1500)
      }
    } catch (error) {
      Logger.error('BackupManagement', 'خطأ في تصدير النسخة الاحتياطية:', error)
      message.error('فشل في تصدير النسخة الاحتياطية')
    } finally {
      setExportingBackup(null)
    }
  }

  const importBackup = async (file: File) => {
    try {
      setImportingBackup(true)

      if (window.electronAPI && window.electronAPI.importBackup) {
        const result = await window.electronAPI.importBackup((file as any).path || file.name)
        if (result.success) {
          message.success('تم استيراد النسخة الاحتياطية بنجاح')
          loadBackups()
        } else {
          message.error(result.message || 'فشل في استيراد النسخة الاحتياطية')
        }
      } else {
        // محاكاة الاستيراد
        setTimeout(() => {
          message.success('تم استيراد النسخة الاحتياطية بنجاح')
          loadBackups()
        }, 2000)
      }
    } catch (error) {
      Logger.error('BackupManagement', 'خطأ في استيراد النسخة الاحتياطية:', error)
      message.error('فشل في استيراد النسخة الاحتياطية')
    } finally {
      setImportingBackup(false)
    }
  }

  return (
    <div>
      <StyledCard 
        title={
          <Space>
            <DatabaseOutlined />
            إدارة النسخ الاحتياطي
          </Space>
        }
        extra={
          <Space>
            <Button 
              icon={<SettingOutlined />}
              onClick={() => setShowSettingsModal(true)}
            >
              الإعدادات
            </Button>
            <Button 
              icon={<ReloadOutlined />}
              onClick={loadBackups}
              loading={loading}
            >
              تحديث
            </Button>
          </Space>
        }
      >
        {/* إحصائيات سريعة */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6}>
            <StatCard>
              <Statistic
                title="إجمالي النسخ"
                value={backups.length}
                prefix={<DatabaseOutlined />}
              />
            </StatCard>
          </Col>
          <Col xs={24} sm={6}>
            <StatCard>
              <Statistic
                title="الحجم الإجمالي"
                value={formatFileSize(backups.reduce((sum, backup) => sum + backup.size, 0))}
                prefix={<CloudDownloadOutlined />}
              />
            </StatCard>
          </Col>
          <Col xs={24} sm={6}>
            <StatCard>
              <Statistic
                title="النسخ التلقائية"
                value={backups.filter(b => b.type === 'automatic').length}
                prefix={<ClockCircleOutlined />}
              />
            </StatCard>
          </Col>
          <Col xs={24} sm={6}>
            <StatCard>
              <Statistic
                title="آخر نسخة"
                value={backups.length > 0 ? dayjs(backups[0].created_at).format('DD/MM') : '--'}
                prefix={<HistoryOutlined />}
              />
            </StatCard>
          </Col>
        </Row>

        {/* تقدم العمليات */}
        {(isBackingUp || isRestoring) && (
          <Alert
            message={isBackingUp ? "جاري إنشاء النسخة الاحتياطية..." : "جاري استعادة النسخة الاحتياطية..."}
            description={
              <Progress
                percent={Math.round(isBackingUp ? backupProgress : restoreProgress)}
                status="active"
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068'}}
              />
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {/* أزرار العمليات الرئيسية */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={8}>
            <Button
              type="primary"
              size="large"
              icon={<DatabaseOutlined />}
              onClick={() => setShowAdvancedOptions(true)}
              loading={isBackingUp}
              block
            >
              إنشاء نسخة احتياطية
            </Button>
          </Col>
          <Col xs={24} sm={8}>
            <Upload
              accept=".db,.sql,.backup,.zip"
              showUploadList={false}
              beforeUpload={(file) => {
                Modal.confirm({
                  title: 'استيراد نسخة احتياطية',
                  content: 'هل تريد استيراد النسخة الاحتياطية من الملف: ' + file.name + '؟',
                  onOk: () => {
                    importBackup(file)
                  },
                  okText: 'استيراد',
                  cancelText: 'إلغاء'
                })
                return false
              }}
            >
              <Button
                size="large"
                icon={<ImportOutlined />}
                loading={importingBackup}
                block
              >
                استيراد نسخة احتياطية
              </Button>
            </Upload>
          </Col>
          <Col xs={24} sm={8}>
            <Button
              size="large"
              icon={<DownloadOutlined />}
              onClick={() => {
                if (backups.length === 0) {
                  message.warning('لا توجد نسخ احتياطية للتصدير')
                  return
                }
                // هنا يمكن إضافة منطق تصدير جميع النسخ
                message.info('جاري تطوير هذه الميزة...')
              }}
              block
            >
              تصدير جميع النسخ
            </Button>
          </Col>
        </Row>

        <Divider />

        {/* قائمة النسخ الاحتياطية */}
        <div>
          <Title level={4}>
            <HistoryOutlined /> النسخ الاحتياطية المتاحة
          </Title>

          {backups.length === 0 ? (
            <Alert
              message="لا توجد نسخ احتياطية"
              description="لم يتم إنشاء أي نسخ احتياطية بعد. انقر على 'إنشاء نسخة احتياطية' لإنشاء أول نسخة."
              type="info"
              showIcon
              style={{ marginTop: 16 }}
            />
          ) : (
            <Row gutter={[16, 16]}>
              {backups.map((backup) => (
                <Col xs={24} lg={12} key={backup.id}>
                  <BackupCard
                    size="small"
                    title={
                      <Space>
                        {getStatusIcon(backup.status)}
                        <Text strong>{backup.filename}</Text>
                        <Tag color={getTypeColor(backup.type)}>
                          {getTypeText(backup.type)}
                        </Tag>
                      </Space>
                    }
                    extra={
                      <Space>
                        <Tooltip title="التحقق من سلامة النسخة">
                          <Button
                            size="small"
                            icon={<SafetyOutlined />}
                            onClick={() => verifyBackup(backup.id)}
                            loading={verifyingBackup === backup.id}
                            disabled={backup.status !== 'completed'}
                          />
                        </Tooltip>
                        <Tooltip title="تصدير">
                          <Button
                            size="small"
                            icon={<ExportOutlined />}
                            onClick={() => exportBackup(backup.id)}
                            loading={exportingBackup === backup.id}
                            disabled={backup.status !== 'completed'}
                          />
                        </Tooltip>
                        <Tooltip title="استعادة">
                          <Button
                            type="primary"
                            size="small"
                            icon={<DownloadOutlined />}
                            onClick={() => {
                              Modal.confirm({
                                title: 'تأكيد الاستعادة',
                                content: (
                                  <div>
                                    <Alert
                                      message="تحذير مهم"
                                      description="ستؤدي عملية الاستعادة إلى استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية. هذه العملية لا يمكن التراجع عنها."
                                      type="warning"
                                      showIcon
                                      style={{ marginBottom: 16 }}
                                    />
                                    <p>هل تريد المتابعة مع استعادة النسخة الاحتياطية؟</p>
                                  </div>
                                ),
                                onOk: () => restoreBackup(backup.id),
                                okText: 'استعادة',
                                cancelText: 'إلغاء',
                                okType: 'danger'
                              })
                            }}
                            disabled={backup.status !== 'completed' || isRestoring}
                          />
                        </Tooltip>
                        <Tooltip title="حذف">
                          <Popconfirm
                            title="هل تريد حذف هذه النسخة الاحتياطية؟"
                            onConfirm={() => deleteBackup(backup.id)}
                            okText="حذف"
                            cancelText="إلغاء"
                            okType="danger"
                          >
                            <Button
                              danger
                              size="small"
                              icon={<DeleteOutlined />}
                            />
                          </Popconfirm>
                        </Tooltip>
                      </Space>
                    }
                  >
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text type="secondary">الحجم: </Text>
                        <Text>{formatFileSize(backup.size)}</Text>
                      </div>
                      <div>
                        <Text type="secondary">التاريخ: </Text>
                        <Text>{dayjs(backup.created_at).format('DD/MM/YYYY HH:mm')}</Text>
                      </div>
                      <div>
                        <Text type="secondary">الحالة: </Text>
                        <Text>{getStatusText(backup.status)}</Text>
                      </div>
                      {backup.description && (
                        <div>
                          <Text type="secondary">الوصف: </Text>
                          <Text>{backup.description}</Text>
                        </div>
                      )}
                    </Space>
                  </BackupCard>
                </Col>
              ))}
            </Row>
          )}
        </div>
      </StyledCard>

      {/* مودال إعدادات النسخ الاحتياطي */}
      <Modal
        title={
          <Space>
            <SettingOutlined />
            إعدادات النسخ الاحتياطي
          </Space>
        }
        open={showSettingsModal}
        onCancel={() => setShowSettingsModal(false)}
        onOk={() => {
          form.validateFields().then(values => {
            // حفّ الإعدادات
            if (window.electronAPI) {
              window.electronAPI.updateBackupSettings(values).then(result => {
                if (result.success) {
                  message.success('تم حفّ الإعدادات بنجاح')
                  setSettings(values)
                  setShowSettingsModal(false)
                } else {
                  message.error('فشل في حفّ الإعدادات')
                }
              })
            } else {
              setSettings(values)
              message.success('تم حفّ الإعدادات بنجاح')
              setShowSettingsModal(false)
            }
          })
        }}
        okText="حفّ"
        cancelText="إلغاء"
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={settings}
        >
          <Form.Item
            label="تفعيل النسخ الاحتياطي التلقائي"
            name="auto_backup_enabled"
            valuePropName="checked"
          >
            <Switch checkedChildren="مفعل" unCheckedChildren="معطل" />
          </Form.Item>

          <Form.Item
            label="تكرار النسخ الاحتياطي"
            name="backup_frequency"
          >
            <Select>
              <Select.Option value="daily">يومي</Select.Option>
              <Select.Option value="weekly">أسبوعي</Select.Option>
              <Select.Option value="monthly">شهري</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="وقت النسخ الاحتياطي"
            name="backup_time"
          >
            <Input placeholder="02:00" />
          </Form.Item>

          <Form.Item
            label="الحد الأقصى لعدد النسخ المحفوّة"
            name="max_backups"
          >
            <Select>
              <Select.Option value={10}>10 نسخ</Select.Option>
              <Select.Option value={20}>20 نسخة</Select.Option>
              <Select.Option value={30}>30 نسخة</Select.Option>
              <Select.Option value={50}>50 نسخة</Select.Option>
            </Select>
          </Form.Item>

          <Alert
            message="ملاحّة"
            description="سيتم حذف النسخ الاحتياطية القديمة تلقائياً عند تجاوز الحد الأقصى المحدد"
            type="info"
            showIcon
            style={{ marginTop: 16 }}
          />
        </Form>
      </Modal>

      {/* مودال خيارات النسخ الاحتياطي المتقدمة */}
      <Modal
        title={
          <Space>
            <DatabaseOutlined />
            خيارات النسخ الاحتياطي المتقدمة
          </Space>
        }
        open={showAdvancedOptions}
        onCancel={() => setShowAdvancedOptions(false)}
        onOk={() => {
          createBackup(backupOptions)
          setShowAdvancedOptions(false)
        }}
        okText="إنشاء النسخة الاحتياطية"
        cancelText="إلغاء"
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <Alert
            message="خيارات النسخ الاحتياطي"
            description="يمكنك تخصيص النسخة الاحتياطية حسب احتياجاتك"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        </div>

        <Form layout="vertical">
          <Form.Item label="وصف النسخة الاحتياطية">
            <Input.TextArea
              placeholder="أدخل وصفاً للنسخة الاحتياطية (اختياري)"
              rows={3}
              value={backupOptions.description}
              onChange={(e) => setBackupOptions(prev => ({ ...prev, description: e.target.value }))}
            />
          </Form.Item>

          <Form.Item label="خيارات المحتوى">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Space>
                  <PictureOutlined />
                  <span>تضمين الصور</span>
                </Space>
                <Switch
                  checked={backupOptions.includeImages}
                  onChange={(checked) => setBackupOptions(prev => ({ ...prev, includeImages: checked }))}
                  checkedChildren="نعم"
                  unCheckedChildren="لا"
                />
              </div>
              <Text type="secondary" style={{ fontSize: '12px', marginLeft: 24 }}>
                سيتم تضمين جميع صور الأصناف والشيكات في النسخة الاحتياطية
              </Text>
            </Space>
          </Form.Item>

          <Form.Item label="خيارات الضغط">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Space>
                  <CompressOutlined />
                  <span>ضغط النسخة الاحتياطية</span>
                </Space>
                <Switch
                  checked={backupOptions.compress}
                  onChange={(checked) => setBackupOptions(prev => ({ ...prev, compress: checked }))}
                  checkedChildren="نعم"
                  unCheckedChildren="لا"
                />
              </div>
              <Text type="secondary" style={{ fontSize: '12px', marginLeft: 24 }}>
                سيتم ضغط النسخة الاحتياطية لتوفير مساحة التخزين
              </Text>
            </Space>
          </Form.Item>

          <Divider />

          <Alert
            message="معلومات مهمة"
            description={
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                <li>النسخة الاحتياطية ستشمل جميع البيانات الحالية</li>
                <li>قد تستغرق العملية وقتاً أطول عند تضمين الصور</li>
                <li>الضغط يقلل حجم الملف ولكن قد يزيد وقت الإنشاء</li>
              </ul>
            }
            type="warning"
            showIcon
          />
        </Form>
      </Modal>
    </div>
  )
}

export default BackupManagement
