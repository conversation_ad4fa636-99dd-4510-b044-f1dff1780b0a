/**
 * محرر القوالب المتكامل المحسن
 * يجمع بين تعديل القوالب وإدارة الأعمدة مع نظام الوراثة الذكي
 */

import React, { useState, useEffect, useCallback } from 'react'
import {
  Modal,
  Tabs,
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  Button,
  Space,
  Card,
  Row,
  Col,
  Typography,
  Divider,
  Alert,
  Tag,
  Tooltip,
  message,
  Slider,
  Radio,
  Checkbox,
  Badge
} from 'antd'
import {
  FileTextOutlined,
  SaveOutlined,
  EyeOutlined,
  ReloadOutlined,
  SettingOutlined,
  TableOutlined,
  BgColorsOutlined,
  FontSizeOutlined,
  LayoutOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  EditOutlined,
  BranchesOutlined
} from '@ant-design/icons'

import {
  EnhancedTemplate,
  UnifiedPrintSettings,
  EnhancedTemplateEditorProps,
  SettingsSection,
  TemplateEditMode,
  createEmptyTemplate,
  EnhancedColumnConfig
} from '../../types/enhancedTemplateTypes'
import { useUnifiedSettings } from '../../hooks/useUnifiedSettings'
import { SafeLogger as Logger } from '../../utils/logger'
import IntegratedColumnEditor from './IntegratedColumnEditor'

const { Title, Text } = Typography
const { Option } = Select
const { TabPane } = Tabs
const { TextArea } = Input

interface EnhancedTemplateCreatorProps {
  visible: boolean
  onClose: () => void
  template?: EnhancedTemplate
  mode?: TemplateEditMode
  reportContext?: {
    reportType: string
    reportCategory: string
    reportTitle: string
  }
}

const EnhancedTemplateCreator: React.FC<EnhancedTemplateCreatorProps> = ({
  visible,
  onClose,
  template,
  mode = 'create',
  reportContext
}) => {
  // الحالة المحلية
  const [form] = Form.useForm()
  const [activeTab, setActiveTab] = useState<SettingsSection>('page')
  const [currentTemplate, setCurrentTemplate] = useState<EnhancedTemplate>(createEmptyTemplate())
  const [loading, setLoading] = useState(false)
  const [_previewVisible, setPreviewVisible] = useState(false)

  // حالة الأعمدة
  const [templateColumns, setTemplateColumns] = useState<EnhancedColumnConfig[]>([])
  const [columnsChanged, setColumnsChanged] = useState(false)

  // Hook الإعدادات الموحدة
  const {
    globalSettings,
    saveTemplate,
    getEffectiveSettings,
    validateTemplate,
    isInitialized
  } = useUnifiedSettings()

  // إنشاء أعمدة افتراضية حسب نوع التقرير
  const createDefaultColumns = useCallback((reportType: string): EnhancedColumnConfig[] => {
    const baseColumns: EnhancedColumnConfig[] = [
      {
        key: 'id',
        title: 'الرقم',
        dataIndex: 'id',
        type: 'number',
        visible: true,
        width: 80,
        align: 'center',
        printVisible: true,
        printWidth: 60,
        printAlign: 'center',
        printOrder: 1,
        printFormat: { bold: true }
      },
      {
        key: 'name',
        title: 'الاسم',
        dataIndex: 'name',
        type: 'text',
        visible: true,
        width: 200,
        align: 'right',
        printVisible: true,
        printWidth: 150,
        printAlign: 'right',
        printOrder: 2
      }
    ]

    // إضافة أعمدة خاصة حسب نوع التقرير
    switch (reportType) {
      case 'financial':
        return [
          ...baseColumns,
          {
            key: 'amount',
            title: 'المبلغ',
            dataIndex: 'amount',
            type: 'currency',
            visible: true,
            width: 120,
            align: 'left',
            printVisible: true,
            printWidth: 100,
            printAlign: 'left',
            printOrder: 3,
            printFormat: { bold: true, color: '#1890ff' },
            numberFormat: { decimals: 2, thousandsSeparator: true, currency: 'ر.س' }
          }
        ]

      case 'inventory':
        return [
          ...baseColumns,
          {
            key: 'quantity',
            title: 'الكمية',
            dataIndex: 'quantity',
            type: 'number',
            visible: true,
            width: 100,
            align: 'center',
            printVisible: true,
            printWidth: 80,
            printAlign: 'center',
            printOrder: 3,
            numberFormat: { decimals: 0, thousandsSeparator: true }
          },
          {
            key: 'unit_price',
            title: 'سعر الوحدة',
            dataIndex: 'unit_price',
            type: 'currency',
            visible: true,
            width: 120,
            align: 'left',
            printVisible: true,
            printWidth: 100,
            printAlign: 'left',
            printOrder: 4,
            numberFormat: { decimals: 2, thousandsSeparator: true, currency: 'ر.س' }
          }
        ]

      default:
        return baseColumns
    }
  }, [])

  // تهيئة القالب عند فتح المحرر
  useEffect(() => {
    if (visible && isInitialized) {
      let initialTemplate: EnhancedTemplate

      if (template && mode === 'edit') {
        // تعديل قالب موجود
        initialTemplate = { ...template }
      } else if (template && mode === 'duplicate') {
        // نسخ قالب موجود
        initialTemplate = {
          ...template,
          metadata: {
            ...template.metadata,
            id: `template-${Date.now()}`,
            name: `${template.metadata.name} - نسخة`,
            isDefault: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        }
      } else {
        // إنشاء قالب جديد
        initialTemplate = createEmptyTemplate()
        
        // تطبيق سياق التقرير إذا كان متاحاً
        if (reportContext) {
          initialTemplate.metadata.name = `قالب ${reportContext.reportTitle}`
          initialTemplate.metadata.category = reportContext.reportCategory
          initialTemplate.metadata.type = 'report'
        }
      }

      setCurrentTemplate(initialTemplate)

      // تهيئة الأعمدة
      if (initialTemplate.columns && initialTemplate.columns.length > 0) {
        setTemplateColumns(initialTemplate.columns)
      } else {
        // إنشاء أعمدة افتراضية حسب نوع التقرير
        const defaultColumns = createDefaultColumns(reportContext?.reportType || 'general')
        setTemplateColumns(defaultColumns)
      }

      // تحديث النموذج
      updateFormFromTemplate(initialTemplate)

      Logger.info('EnhancedTemplateCreator', `تم تهيئة المحرر في وضع: ${mode}`)
    }
  }, [visible, template, mode, reportContext, isInitialized])

  // تحديث النموذج من القالب
  const updateFormFromTemplate = useCallback((templateData: EnhancedTemplate) => {
    const effectiveSettings = getEffectiveSettings(templateData)
    
    form.setFieldsValue({
      // معلومات القالب
      name: templateData.metadata.name,
      description: templateData.metadata.description,
      type: templateData.metadata.type,
      category: templateData.metadata.category,
      
      // نظام الوراثة
      inheritsFromGlobal: templateData.inheritance.inheritsFromGlobal,
      
      // إعدادات الصفحة
      pageSize: effectiveSettings.page.pageSize,
      orientation: effectiveSettings.page.orientation,
      marginTop: effectiveSettings.page.margins.top,
      marginBottom: effectiveSettings.page.margins.bottom,
      marginLeft: effectiveSettings.page.margins.left,
      marginRight: effectiveSettings.page.margins.right,
      
      // إعدادات الخط
      fontSize: effectiveSettings.font.fontSize,
      fontFamily: effectiveSettings.font.fontFamily,
      headerSize: effectiveSettings.font.headerSize,
      lineSpacing: effectiveSettings.font.lineSpacing,
      
      // إعدادات الألوان
      primaryColor: effectiveSettings.colors.primaryColor,
      secondaryColor: effectiveSettings.colors.secondaryColor,
      borderColor: effectiveSettings.colors.borderColor,
      backgroundColor: effectiveSettings.colors.backgroundColor,
      textColor: effectiveSettings.colors.textColor,
      
      // إعدادات العرض
      showHeader: effectiveSettings.display.showHeader,
      showFooter: effectiveSettings.display.showFooter,
      showLogo: effectiveSettings.display.showLogo,
      showSignature: effectiveSettings.display.showSignature,
      showTerms: effectiveSettings.display.showTerms,
      showQR: effectiveSettings.display.showQR,
      
      // إعدادات التخطيط
      logoPosition: effectiveSettings.layout.logoPosition,
      logoSize: effectiveSettings.layout.logoSize,
      borderWidth: effectiveSettings.layout.borderWidth,
      sectionSpacing: effectiveSettings.layout.sectionSpacing,
      tableWidth: effectiveSettings.layout.tableWidth,
      
      // إعدادات المحتوى
      headerText: effectiveSettings.content.headerText,
      footerText: effectiveSettings.content.footerText,
      watermark: effectiveSettings.content.watermark,
      watermarkText: effectiveSettings.content.watermarkText,
      watermarkOpacity: effectiveSettings.content.watermarkOpacity,
      
      // إعدادات الجودة
      quality: effectiveSettings.quality.quality,
      copies: effectiveSettings.quality.copies,
      autoSave: effectiveSettings.quality.autoSave
    })
  }, [form, getEffectiveSettings])

  // حفظ القالب
  const handleSave = useCallback(async (values: any) => {
    try {
      setLoading(true)
      
      // تحديث معلومات القالب
      const updatedTemplate: EnhancedTemplate = {
        ...currentTemplate,
        metadata: {
          ...currentTemplate.metadata,
          name: values.name,
          description: values.description || '',
          type: values.type,
          category: values.category,
          updatedAt: new Date().toISOString()
        },
        inheritance: {
          ...currentTemplate.inheritance,
          inheritsFromGlobal: values.inheritsFromGlobal,
          customSettings: values.inheritsFromGlobal ?
            extractCustomSettings(values) :
            extractAllSettings(values)
        },
        columns: templateColumns
      }

      // التحقق من صحة القالب
      const validation = validateTemplate(updatedTemplate)
      if (!validation.isValid) {
        message.error(`قالب غير صحيح: ${validation.errors.join(', ')}`)
        return
      }

      // عرض التحذيرات إن وجدت
      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => message.warning(warning))
      }

      // حفظ القالب
      const success = await saveTemplate(updatedTemplate)
      
      if (success) {
        message.success(`تم حفظ القالب: ${updatedTemplate.metadata.name}`)
        onClose()
        Logger.info('EnhancedTemplateCreator', `تم حفظ القالب: ${updatedTemplate.metadata.name}`)
      }
    } catch (error) {
      Logger.error('EnhancedTemplateCreator', 'خطأ في حفظ القالب:', error)
      message.error('حدث خطأ في حفظ القالب')
    } finally {
      setLoading(false)
    }
  }, [currentTemplate, saveTemplate, validateTemplate, onClose])

  // استخراج الإعدادات المخصصة فقط (للوراثة)
  const extractCustomSettings = useCallback((values: any) => {
    // هنا نقارن مع الإعدادات العامة ونحفظ الاختلافات فقط
    const customSettings: any = {}
    
    // مقارنة الألوان
    if (values.primaryColor !== globalSettings.colors.primaryColor) {
      customSettings.colors = customSettings.colors || {}
      customSettings.colors.primaryColor = values.primaryColor
    }
    
    // مقارنة الخطوط
    if (values.fontSize !== globalSettings.font.fontSize) {
      customSettings.font = customSettings.font || {}
      customSettings.font.fontSize = values.fontSize
    }
    
    // يمكن إضافة المزيد من المقارنات هنا
    
    return customSettings
  }, [globalSettings])

  // استخراج جميع الإعدادات (بدون وراثة)
  const extractAllSettings = useCallback((values: any) => {
    return {
      page: {
        pageSize: values.pageSize,
        orientation: values.orientation,
        margins: {
          top: values.marginTop,
          bottom: values.marginBottom,
          left: values.marginLeft,
          right: values.marginRight
        }
      },
      font: {
        fontSize: values.fontSize,
        fontFamily: values.fontFamily,
        headerSize: values.headerSize,
        lineSpacing: values.lineSpacing
      },
      colors: {
        primaryColor: values.primaryColor,
        secondaryColor: values.secondaryColor,
        borderColor: values.borderColor,
        backgroundColor: values.backgroundColor,
        textColor: values.textColor
      },
      display: {
        showHeader: values.showHeader,
        showFooter: values.showFooter,
        showLogo: values.showLogo,
        showSignature: values.showSignature,
        showTerms: values.showTerms,
        showQR: values.showQR
      },
      layout: {
        logoPosition: values.logoPosition,
        logoSize: values.logoSize,
        borderWidth: values.borderWidth,
        sectionSpacing: values.sectionSpacing,
        tableWidth: values.tableWidth
      },
      content: {
        headerText: values.headerText,
        footerText: values.footerText,
        watermark: values.watermark,
        watermarkText: values.watermarkText,
        watermarkOpacity: values.watermarkOpacity
      },
      quality: {
        quality: values.quality,
        copies: values.copies,
        autoSave: values.autoSave
      }
    }
  }, [])

  // معاينة القالب
  const handlePreview = useCallback(() => {
    setPreviewVisible(true)
    // هنا يمكن إضافة منطق المعاينة
    message.info('ستتم إضافة معاينة القالب قريباً')
  }, [])

  // إعادة تعيين النموذج
  const handleReset = useCallback(() => {
    updateFormFromTemplate(currentTemplate)
    message.info('تم إعادة تعيين النموذج')
  }, [currentTemplate, updateFormFromTemplate])

  // تبديل نظام الوراثة
  const handleInheritanceToggle = useCallback((inherit: boolean) => {
    const updatedTemplate = {
      ...currentTemplate,
      inheritance: {
        ...currentTemplate.inheritance,
        inheritsFromGlobal: inherit
      }
    }
    setCurrentTemplate(updatedTemplate)
    updateFormFromTemplate(updatedTemplate)

    message.info(inherit ? 'تم تفعيل الوراثة من الإعدادات العامة' : 'تم إلغاء الوراثة - استخدام إعدادات مخصصة')
  }, [currentTemplate, updateFormFromTemplate])

  // إدارة الأعمدة
  const handleColumnsChange = useCallback((newColumns: EnhancedColumnConfig[]) => {
    setTemplateColumns(newColumns)
    setColumnsChanged(true)

    // تحديث القالب الحالي
    const updatedTemplate = {
      ...currentTemplate,
      columns: newColumns
    }
    setCurrentTemplate(updatedTemplate)

    Logger.info('EnhancedTemplateCreator', `تم تحديث الأعمدة: ${newColumns.length} عمود`)
  }, [currentTemplate])

  // حفظ تغييرات الأعمدة
  const saveColumnsChanges = useCallback(() => {
    if (columnsChanged) {
      const updatedTemplate = {
        ...currentTemplate,
        columns: templateColumns,
        metadata: {
          ...currentTemplate.metadata,
          updatedAt: new Date().toISOString()
        }
      }
      setCurrentTemplate(updatedTemplate)
      setColumnsChanged(false)
      message.success('تم حفظ تغييرات الأعمدة')
    }
  }, [currentTemplate, templateColumns, columnsChanged])

  // إعادة تعيين الأعمدة
  const resetColumns = useCallback(() => {
    const defaultColumns = createDefaultColumns(reportContext?.reportType || 'general')
    setTemplateColumns(defaultColumns)
    setColumnsChanged(true)
    message.info('تم إعادة تعيين الأعمدة للقيم الافتراضية')
  }, [createDefaultColumns, reportContext])

  // تحديد عنوان المودال
  const getModalTitle = () => {
    switch (mode) {
      case 'create': return 'إنشاء قالب جديد'
      case 'edit': return `تعديل القالب: ${template?.metadata.name}`
      case 'duplicate': return `نسخ القالب: ${template?.metadata.name}`
      default: return 'محرر القوالب'
    }
  }

  return (
    <Modal
      title={
        <Space>
          <FileTextOutlined />
          <span>{getModalTitle()}</span>
          {reportContext && (
            <Tag color="blue">{reportContext.reportTitle}</Tag>
          )}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      style={{ top: 20 }}
      footer={
        <Space>
          <Button onClick={onClose}>إلغاء</Button>
          <Button icon={<ReloadOutlined />} onClick={handleReset}>
            إعادة تعيين
          </Button>
          <Button icon={<EyeOutlined />} onClick={handlePreview}>
            معاينة
          </Button>
          <Button 
            type="primary" 
            icon={<SaveOutlined />} 
            loading={loading}
            onClick={() => form.submit()}
          >
            {mode === 'create' ? 'إنشاء' : 'حفظ'} القالب
          </Button>
        </Space>
      }
    >
      {/* تنبيه نظام الوراثة */}
      <Alert
        message="نظام الوراثة الذكي"
        description="يمكن للقالب وراثة الإعدادات من الإعدادات العامة وتخصيص ما يحتاجه فقط، أو استخدام إعدادات مخصصة بالكامل."
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
        action={
          <Switch
            checkedChildren="وراثة"
            unCheckedChildren="مخصص"
            checked={currentTemplate.inheritance.inheritsFromGlobal}
            onChange={handleInheritanceToggle}
          />
        }
      />

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        preserve={false}
      >
        <Tabs 
          activeKey={activeTab} 
          onChange={(key) => setActiveTab(key as SettingsSection)}
          type="card"
        >
          {/* تبويب المعلومات العامة */}
          <TabPane
            tab={
              <Space>
                <InfoCircleOutlined />
                <span>معلومات عامة</span>
              </Space>
            }
            key="page"
          >
            <Row gutter={[24, 16]}>
              <Col xs={24} lg={12}>
                <Card size="small" title="معلومات القالب" style={{ marginBottom: 16 }}>
                  <Form.Item
                    label="اسم القالب"
                    name="name"
                    rules={[{ required: true, message: 'اسم القالب مطلوب' }]}
                  >
                    <Input placeholder="أدخل اسم القالب" />
                  </Form.Item>

                  <Form.Item label="وصف القالب" name="description">
                    <TextArea rows={3} placeholder="وصف مختصر للقالب" />
                  </Form.Item>

                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <Form.Item
                        label="نوع القالب"
                        name="type"
                        rules={[{ required: true, message: 'نوع القالب مطلوب' }]}
                      >
                        <Select placeholder="اختر نوع القالب">
                          <Option value="invoice">فاتورة</Option>
                          <Option value="receipt">إيصال</Option>
                          <Option value="report">تقرير</Option>
                          <Option value="certificate">شهادة</Option>
                          <Option value="custom">مخصص</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item label="فئة القالب" name="category">
                        <Select placeholder="اختر فئة القالب" allowClear>
                          <Option value="financial">مالية</Option>
                          <Option value="inventory">مخزون</Option>
                          <Option value="sales">مبيعات</Option>
                          <Option value="production">إنتاج</Option>
                          <Option value="employees">موظفين</Option>
                          <Option value="paint">دهان</Option>
                          <Option value="purchases">مشتريات</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>

                <Card size="small" title="إعدادات الصفحة">
                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={12}>
                      <Form.Item label="حجم الورق" name="pageSize">
                        <Select>
                          <Option value="A4">A4</Option>
                          <Option value="A5">A5</Option>
                          <Option value="A3">A3</Option>
                          <Option value="Letter">Letter</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col xs={24} md={12}>
                      <Form.Item label="اتجاه الصفحة" name="orientation">
                        <Radio.Group>
                          <Radio value="portrait">عمودي</Radio>
                          <Radio value="landscape">أفقي</Radio>
                        </Radio.Group>
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]}>
                    <Col xs={12} md={6}>
                      <Form.Item label="الهامش العلوي" name="marginTop">
                        <InputNumber min={0} max={50} addonAfter="مم" style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col xs={12} md={6}>
                      <Form.Item label="الهامش السفلي" name="marginBottom">
                        <InputNumber min={0} max={50} addonAfter="مم" style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col xs={12} md={6}>
                      <Form.Item label="الهامش الأيسر" name="marginLeft">
                        <InputNumber min={0} max={50} addonAfter="مم" style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                    <Col xs={12} md={6}>
                      <Form.Item label="الهامش الأيمن" name="marginRight">
                        <InputNumber min={0} max={50} addonAfter="مم" style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card size="small" title="نظام الوراثة" style={{ marginBottom: 16 }}>
                  <Form.Item name="inheritsFromGlobal" valuePropName="checked">
                    <Checkbox>
                      <Space>
                        <BranchesOutlined />
                        <span>وراثة من الإعدادات العامة</span>
                      </Space>
                    </Checkbox>
                  </Form.Item>

                  <Alert
                    message={
                      currentTemplate.inheritance.inheritsFromGlobal
                        ? "🟢 الوراثة مفعلة"
                        : "🔵 إعدادات مخصصة"
                    }
                    description={
                      currentTemplate.inheritance.inheritsFromGlobal
                        ? "القالب يرث الإعدادات من الإعدادات العامة ويمكن تخصيص ما يحتاجه فقط"
                        : "القالب يستخدم إعدادات مخصصة بالكامل ولا يرث من الإعدادات العامة"
                    }
                    type={currentTemplate.inheritance.inheritsFromGlobal ? "success" : "info"}
                    showIcon
                    style={{ marginTop: 8 }}
                  />
                </Card>

                <Card size="small" title="إعدادات العرض">
                  <Row gutter={[16, 16]}>
                    <Col xs={12}>
                      <Form.Item name="showHeader" valuePropName="checked">
                        <Checkbox>إظهار الهيدر</Checkbox>
                      </Form.Item>
                    </Col>
                    <Col xs={12}>
                      <Form.Item name="showFooter" valuePropName="checked">
                        <Checkbox>إظهار الفوتر</Checkbox>
                      </Form.Item>
                    </Col>
                    <Col xs={12}>
                      <Form.Item name="showLogo" valuePropName="checked">
                        <Checkbox>إظهار الشعار</Checkbox>
                      </Form.Item>
                    </Col>
                    <Col xs={12}>
                      <Form.Item name="showSignature" valuePropName="checked">
                        <Checkbox>إظهار التوقيع</Checkbox>
                      </Form.Item>
                    </Col>
                    <Col xs={12}>
                      <Form.Item name="showTerms" valuePropName="checked">
                        <Checkbox>إظهار الشروط والأحكام</Checkbox>
                      </Form.Item>
                    </Col>
                    <Col xs={12}>
                      <Form.Item name="showQR" valuePropName="checked">
                        <Checkbox>إظهار رمز QR</Checkbox>
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>
          </TabPane>

          {/* تبويب الألوان */}
          <TabPane
            tab={
              <Space>
                <BgColorsOutlined />
                <span>الألوان</span>
              </Space>
            }
            key="colors"
          >
            <Card title="🎨 إعدادات الألوان">
              <Row gutter={[24, 16]}>
                <Col xs={24} md={8}>
                  <Form.Item label="اللون الأساسي" name="primaryColor">
                    <Input
                      type="color"
                      style={{ width: '100%', height: 40 }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="اللون الثانوي" name="secondaryColor">
                    <Input
                      type="color"
                      style={{ width: '100%', height: 40 }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="لون الحدود" name="borderColor">
                    <Input
                      type="color"
                      style={{ width: '100%', height: 40 }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={[24, 16]}>
                <Col xs={24} md={8}>
                  <Form.Item label="لون الخلفية" name="backgroundColor">
                    <Input
                      type="color"
                      style={{ width: '100%', height: 40 }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="لون النص" name="textColor">
                    <Input
                      type="color"
                      style={{ width: '100%', height: 40 }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <div style={{ display: 'flex', gap: 8, alignItems: 'end', height: 40 }}>
                    <Button
                      type="primary"
                      icon={<BgColorsOutlined />}
                      onClick={() => {
                        // إعادة تعيين الألوان للافتراضية
                        form.setFieldsValue({
                          primaryColor: globalSettings.colors.primaryColor,
                          secondaryColor: globalSettings.colors.secondaryColor,
                          borderColor: globalSettings.colors.borderColor,
                          backgroundColor: globalSettings.colors.backgroundColor,
                          textColor: globalSettings.colors.textColor
                        })
                        message.success('تم إعادة تعيين الألوان')
                      }}
                    >
                      إعادة تعيين
                    </Button>
                  </div>
                </Col>
              </Row>
            </Card>
          </TabPane>

          {/* تبويب الخطوط */}
          <TabPane
            tab={
              <Space>
                <FontSizeOutlined />
                <span>الخطوط</span>
              </Space>
            }
            key="fonts"
          >
            <Card title="📝 إعدادات الخطوط والنصوص">
              <Row gutter={[24, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item label="نوع الخط" name="fontFamily">
                    <Select>
                      <Option value="Arial">Arial</Option>
                      <Option value="Times New Roman">Times New Roman</Option>
                      <Option value="Calibri">Calibri</Option>
                      <Option value="Tahoma">Tahoma</Option>
                      <Option value="Verdana">Verdana</Option>
                      <Option value="Georgia">Georgia</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="حجم الخط الأساسي" name="fontSize">
                    <Slider
                      min={8}
                      max={24}
                      marks={{
                        8: '8',
                        12: '12',
                        16: '16',
                        20: '20',
                        24: '24'
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={[24, 16]}>
                <Col xs={24} md={12}>
                  <Form.Item label="حجم خط العناوين" name="headerSize">
                    <Slider
                      min={12}
                      max={32}
                      marks={{
                        12: '12',
                        16: '16',
                        20: '20',
                        24: '24',
                        28: '28',
                        32: '32'
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="تباعد الأسطر" name="lineSpacing">
                    <Select>
                      <Option value={1}>عادي (1.0)</Option>
                      <Option value={1.15}>متوسط (1.15)</Option>
                      <Option value={1.5}>واسع (1.5)</Option>
                      <Option value={2}>مضاعف (2.0)</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </TabPane>

          {/* تبويب التخطيط */}
          <TabPane
            tab={
              <Space>
                <LayoutOutlined />
                <span>التخطيط</span>
              </Space>
            }
            key="layout"
          >
            <Row gutter={[24, 16]}>
              <Col xs={24} lg={12}>
                <Card size="small" title="إعدادات الشعار">
                  <Form.Item label="موضع الشعار" name="logoPosition">
                    <Radio.Group>
                      <Radio value="top-left">أعلى يسار</Radio>
                      <Radio value="top-center">أعلى وسط</Radio>
                      <Radio value="top-right">أعلى يمين</Radio>
                    </Radio.Group>
                  </Form.Item>
                  <Form.Item label="حجم الشعار" name="logoSize">
                    <Slider
                      min={50}
                      max={200}
                      marks={{
                        50: 'صغير',
                        100: 'متوسط',
                        150: 'كبير',
                        200: 'كبير جداً'
                      }}
                    />
                  </Form.Item>
                </Card>

                <Card size="small" title="إعدادات الحدود والمسافات">
                  <Form.Item label="عرض الحدود" name="borderWidth">
                    <InputNumber min={0} max={5} addonAfter="px" style={{ width: '100%' }} />
                  </Form.Item>
                  <Form.Item label="تباعد الأقسام" name="sectionSpacing">
                    <InputNumber min={5} max={50} addonAfter="px" style={{ width: '100%' }} />
                  </Form.Item>
                  <Form.Item label="عرض الجدول" name="tableWidth">
                    <Slider
                      min={80}
                      max={100}
                      marks={{
                        80: '80%',
                        90: '90%',
                        95: '95%',
                        100: '100%'
                      }}
                    />
                  </Form.Item>
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card size="small" title="إعدادات المحتوى">
                  <Form.Item label="نص الهيدر" name="headerText">
                    <TextArea rows={2} placeholder="نص يظهر في أعلى الصفحة" />
                  </Form.Item>
                  <Form.Item label="نص الفوتر" name="footerText">
                    <TextArea rows={2} placeholder="نص يظهر في أسفل الصفحة" />
                  </Form.Item>

                  <Divider />

                  <Form.Item name="watermark" valuePropName="checked">
                    <Checkbox>إضافة علامة مائية</Checkbox>
                  </Form.Item>
                  <Form.Item label="نص العلامة المائية" name="watermarkText">
                    <Input placeholder="نص العلامة المائية" />
                  </Form.Item>
                  <Form.Item label="شفافية العلامة المائية" name="watermarkOpacity">
                    <Slider
                      min={10}
                      max={50}
                      marks={{
                        10: '10%',
                        20: '20%',
                        30: '30%',
                        40: '40%',
                        50: '50%'
                      }}
                    />
                  </Form.Item>
                </Card>
              </Col>
            </Row>
          </TabPane>

          {/* تبويب الأعمدة */}
          <TabPane
            tab={
              <Space>
                <TableOutlined />
                <span>الأعمدة</span>
                {columnsChanged && <Badge dot />}
              </Space>
            }
            key="columns"
          >
            <Card
              title="📊 إدارة أعمدة التقرير"
              extra={
                <Space>
                  {columnsChanged && (
                    <Button
                      type="primary"
                      size="small"
                      icon={<SaveOutlined />}
                      onClick={saveColumnsChanges}
                    >
                      حفظ التغييرات
                    </Button>
                  )}
                  <Button
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={resetColumns}
                  >
                    إعادة تعيين
                  </Button>
                </Space>
              }
            >
              <IntegratedColumnEditor
                columns={templateColumns}
                onColumnsChange={handleColumnsChange}
                globalSettings={globalSettings}
                reportContext={reportContext}
              />
            </Card>
          </TabPane>

          {/* تبويب الجودة والإعدادات المتقدمة */}
          <TabPane
            tab={
              <Space>
                <SettingOutlined />
                <span>متقدم</span>
              </Space>
            }
            key="advanced"
          >
            <Card title="⚙️ إعدادات الجودة والطباعة">
              <Row gutter={[24, 16]}>
                <Col xs={24} md={8}>
                  <Form.Item label="جودة الطباعة" name="quality">
                    <Select>
                      <Option value="draft">مسودة (سريع)</Option>
                      <Option value="normal">عادي</Option>
                      <Option value="high">عالي الجودة</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="عدد النسخ" name="copies">
                    <InputNumber min={1} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item name="autoSave" valuePropName="checked">
                    <Checkbox>حفظ تلقائي للتغييرات</Checkbox>
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </TabPane>

          {/* تبويب المعاينة */}
          <TabPane
            tab={
              <Space>
                <EyeOutlined />
                <span>معاينة</span>
              </Space>
            }
            key="preview"
          >
            <Card title="👀 معاينة القالب">
              <Alert
                message="معاينة مباشرة"
                description="سيتم إضافة معاينة مباشرة للقالب هنا"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
              <div style={{ minHeight: 400, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Space direction="vertical" align="center">
                  <EyeOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                  <Title level={4}>معاينة القالب</Title>
                  <Text type="secondary">سيتم إضافة المعاينة المباشرة قريباً</Text>
                  <Button type="primary" icon={<EyeOutlined />} onClick={handlePreview}>
                    فتح المعاينة
                  </Button>
                </Space>
              </div>
            </Card>
          </TabPane>
        </Tabs>
      </Form>
    </Modal>
  )
}

export default EnhancedTemplateCreator
