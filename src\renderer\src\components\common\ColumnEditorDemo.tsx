import React, { useState } from 'react'
import { Card, Table, Space, Typography, Alert } from 'antd'
import SimpleTableManager, { ColumnConfig } from './SimpleTableManager'

const { Title, Paragraph } = Typography

const ColumnEditorDemo: React.FC = () => {
  // بيانات تجريبية للجدول
  const sampleData = [
    {
      id: 1,
      name: 'أحمد محمد',
      email: '<EMAIL>',
      phone: '123456789',
      department: 'المبيعات',
      salary: 5000,
      joinDate: '2023-01-15',
      active: true
    },
    {
      id: 2,
      name: 'فاطمة علي',
      email: '<EMAIL>',
      phone: '987654321',
      department: 'المحاسبة',
      salary: 6000,
      joinDate: '2022-08-20',
      active: true
    },
    {
      id: 3,
      name: 'محمد حسن',
      email: '<EMAIL>',
      phone: '555666777',
      department: 'التقنية',
      salary: 7500,
      joinDate: '2021-12-10',
      active: false
    }
  ]

  // إعداد الأعمدة الافتراضية
  const [columns, setColumns] = useState<ColumnConfig[]>([
    {
      key: 'id',
      title: 'الرقم',
      dataIndex: 'id',
      type: 'number',
      visible: true,
      width: 80,
      align: 'center',
      sortable: true
    },
    {
      key: 'name',
      title: 'الاسم',
      dataIndex: 'name',
      type: 'text',
      visible: true,
      width: 150,
      align: 'right',
      sortable: true,
      filterable: true
    },
    {
      key: 'email',
      title: 'البريد الإلكتروني',
      dataIndex: 'email',
      type: 'text',
      visible: true,
      width: 200,
      align: 'left',
      filterable: true
    },
    {
      key: 'phone',
      title: 'الهاتف',
      dataIndex: 'phone',
      type: 'text',
      visible: true,
      width: 120,
      align: 'center'
    },
    {
      key: 'department',
      title: 'القسم',
      dataIndex: 'department',
      type: 'tag',
      visible: true,
      width: 100,
      align: 'center',
      filterable: true
    },
    {
      key: 'salary',
      title: 'الراتب',
      dataIndex: 'salary',
      type: 'currency',
      visible: true,
      width: 120,
      align: 'right',
      sortable: true
    },
    {
      key: 'joinDate',
      title: 'تاريخ الانضمام',
      dataIndex: 'joinDate',
      type: 'date',
      visible: true,
      width: 130,
      align: 'center',
      sortable: true
    },
    {
      key: 'active',
      title: 'نشط',
      dataIndex: 'active',
      type: 'boolean',
      visible: true,
      width: 80,
      align: 'center',
      filterable: true
    }
  ])

  // تحويل الأعمدة إلى تنسيق Ant Design Table
  const tableColumns = columns
    .filter(col => col.visible)
    .map(col => ({
      key: col.key,
      title: col.title,
      dataIndex: col.dataIndex,
      width: col.width,
      align: col.align,
      sorter: col.sortable ? (a: any, b: any) => {
        const aVal = a[col.dataIndex]
        const bVal = b[col.dataIndex]
        if (col.type === 'number' || col.type === 'currency') {
          return aVal - bVal
        }
        return String(aVal).localeCompare(String(bVal))
      } : undefined,
      render: (value: any) => {
        switch (col.type) {
          case 'currency':
            return `${value.toLocaleString()} ريال`
          case 'boolean':
            return value ? '✅ نعم' : '❌ لا'
          case 'tag':
            return <span style={{ 
              padding: '2px 8px', 
              borderRadius: '4px', 
              backgroundColor: '#f0f0f0',
              fontSize: '12px'
            }}>{value}</span>
          default:
            return value
        }
      }
    }))

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>🎯 نظام تعديل الأعمدة المحسن</Title>
      
      <Alert
        message="نظام محسن وسهل الاستخدام"
        description="هذا النظام الجديد يوفر واجهة بديهية وواضحة لإدارة أعمدة الجداول مع ميزات متقدمة مثل السحب والإفلات، والتعديل المرئي، والإحصائيات السريعة."
        type="success"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      <Card 
        title="جدول الموظفين - مثال تطبيقي"
        extra={
          <Space>
            <SimpleTableManager
              columns={columns}
              data={sampleData}
              onColumnsChange={setColumns}
              title="إدارة أعمدة جدول الموظفين"
              buttonText="تخصيص الأعمدة"
              buttonType="primary"
            />
          </Space>
        }
      >
        <Table
          columns={tableColumns}
          dataSource={sampleData}
          rowKey="id"
          pagination={false}
          scroll={{ x: 'max-content' }}
          size="middle"
        />
      </Card>

      <Card title="الميزات الجديدة" style={{ marginTop: '24px' }}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <Title level={4}>✨ واجهة محسنة</Title>
            <Paragraph>
              • تبويبات منظمة للإدارة والإضافة والتعديل<br/>
              • إحصائيات سريعة للأعمدة المرئية والمخفية<br/>
              • تصميم بديهي وسهل الفهم
            </Paragraph>
          </div>

          <div>
            <Title level={4}>🎯 ميزات متقدمة</Title>
            <Paragraph>
              • سحب وإفلات لإعادة ترتيب الأعمدة<br/>
              • نسخ إعدادات الأعمدة<br/>
              • تحكم كامل في العرض والمحاذاة والنوع<br/>
              • إعدادات الترتيب والتصفية
            </Paragraph>
          </div>

          <div>
            <Title level={4}>🔧 سهولة الاستخدام</Title>
            <Paragraph>
              • أزرار واضحة مع أيقونات مفهومة<br/>
              • نصائح مساعدة (tooltips) شاملة<br/>
              • تأكيدات للعمليات المهمة<br/>
              • رسائل نجاح وخطأ واضحة
            </Paragraph>
          </div>
        </Space>
      </Card>
    </div>
  )
}

export default ColumnEditorDemo
