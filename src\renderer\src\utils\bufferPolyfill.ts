import { SafeLogger as Logger } from './logger'
// Buffer Polyfill للمتصفح
(function() {
  'use strict'

  // فحص ما إذا كان Buffer موجود بالفعل
  if (typeof window !== 'undefined' && window.Buffer && typeof window.Buffer.concat === 'function') {
    return // Buffer موجود بالفعل
  }

  // إنشاء Buffer polyfill
  const createBufferPolyfill = () => {
    // تنفيذ مبسط لـ Buffer.concat
    const concat = (list: Uint8Array[], totalLength?: number) => {
      if (!Array.isArray(list)) {
        throw new TypeError('list argument must be an Array of Buffers')
      }

      if (list.length === 0) {
        return new Uint8Array(0)
      }

      if (list.length === 1) {
        return list[0]
      }

      // حساب الطول الإجمالي إذا لم يتم توفيره
      if (totalLength === undefined) {
        totalLength = 0
        for (let i = 0; i < list.length; i++) {
          totalLength += list[i].length
        }
      }

      // إنشاء buffer جديد
      const result = new Uint8Array(totalLength)
      let offset = 0

      // نسخ البيانات
      for (let i = 0; i < list.length; i++) {
        const buf = list[i]
        result.set(buf, offset)
        offset += buf.length
      }

      return result
    }

    // تنفيذ مبسط لـ Buffer.from
    const from = (data: any, encoding?: string) => {
      if (data instanceof Uint8Array) {
        return data
      }
      
      if (typeof data === 'string') {
        const encoder = new TextEncoder()
        return encoder.encode(data)
      }
      
      if (Array.isArray(data)) {
        return new Uint8Array(data)
      }
      
      throw new TypeError('Invalid data type for Buffer.from')
    }

    // تنفيذ مبسط لـ Buffer.alloc
    const alloc = (size: number, fill?: any) => {
      const buffer = new Uint8Array(size)
      if (fill !== undefined) {
        buffer.fill(fill)
      }
      return buffer
    }

    // تنفيذ مبسط لـ Buffer.isBuffer
    const isBuffer = (obj: any) => {
      return obj instanceof Uint8Array
    }

    return {
      concat,
      from,
      alloc,
      isBuffer
    }
  }

  // إنشاء Buffer polyfill
  const BufferPolyfill = createBufferPolyfill()

  // تعيين Buffer في window
  if (typeof window !== 'undefined') {
    if (!window.Buffer) {
      (window as any).Buffer = BufferPolyfill
    } else {
      // إضافة الوّائف المفقودة فقط
      if (!window.Buffer.concat) {
        (window.Buffer as any).concat = BufferPolyfill.concat
      }
      if (!window.Buffer.from) {
        (window.Buffer as any).from = BufferPolyfill.from
      }
      if (!window.Buffer.alloc) {
        (window.Buffer as any).alloc = BufferPolyfill.alloc
      }
      if (!window.Buffer.isBuffer) {
        (window.Buffer as any).isBuffer = BufferPolyfill.isBuffer
      }
    }
    
    // إضافة Buffer للـ global scope أيضاً
    ;(window as any).globalThis = window.globalThis || window
    if (!(window as any).globalThis.Buffer) {
      ;(window as any).globalThis.Buffer = window.Buffer
    }
  }

  // تعيين Buffer في global للـ Node.js
  if (typeof global !== 'undefined' && !global.Buffer) {
    (global as any).Buffer = BufferPolyfill
  }

  // تعيين Buffer في globalThis
  if (typeof globalThis !== 'undefined' && !globalThis.Buffer) {
    (globalThis as any).Buffer = BufferPolyfill
  }

  console.info('[BufferPolyfill] ✅ تم تفعيل Buffer polyfill بنجاح')
})()

// تصدير Buffer للاستخدام في TypeScript
declare global {
  interface Window {
    Buffer: {
      concat: (list: Uint8Array[], totalLength?: number) => Uint8Array
      from: (data: any, encoding?: string) => Uint8Array
      alloc: (size: number, fill?: any) => Uint8Array
      isBuffer: (obj: any) => boolean
    }
  }
  
  // تم حذف تعريف Buffer لتجنب التعارض مع @types/node
}

export {}
