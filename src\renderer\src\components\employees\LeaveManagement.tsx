﻿import React, { useState, useEffect } from 'react'
import * as XLSX from 'xlsx'
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form, Select, Row,
  Col,
  Typography,
  Statistic,
  Tag,
  Tooltip,
  Badge,
  Avatar,
  Divider,
  Input,
  DatePicker
, App} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  CalendarOutlined,
  PlusOutlined,
  EditOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  MedicineBoxOutlined,
  HeartOutlined,
  HomeOutlined,
  PrinterOutlined,
  FileExcelOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input
const { RangePicker } = DatePicker

interface LeaveRecord {
  id: number
  employee_id: number
  employee_code: string
  employee_name: string
  department_name: string
  leave_type: string
  start_date: string
  end_date: string
  total_days: number
  reason?: string
  status: string
  applied_date: string
  approved_by?: number
  approved_by_name?: string
  approved_date?: string
  rejection_reason?: string
  notes?: string
  created_at: string
  updated_at: string
}

interface Employee {
  id: number
  employee_code: string
  full_name: string
  department_name: string
  status: string
}

interface Department {
  id: number
  name: string
}

const LeaveManagement: React.FC = () => {
  const { message: messageApi } = App.useApp() // استخدام App context لحل تحذٍر Antd
  const [leaveRecords, setLeaveRecords] = useState<LeaveRecord[]>([])
  const [employees, setEmployees] = useState<Employee[]>([])
  const [, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [approvalModalVisible, setApprovalModalVisible] = useState(false)
  const [editingRecord, setEditingRecord] = useState<LeaveRecord | null>(null)
  const [approvingRecord, setApprovingRecord] = useState<LeaveRecord | null>(null)
  const [filters, setFilters] = useState<any>({})
  const [form] = Form.useForm()
  const [approvalForm] = Form.useForm()

  useEffect(() => {
    fetchLeaveRecords()
    fetchEmployees()
    fetchDepartments()
  }, [])

  const fetchLeaveRecords = async (filterParams?: any) => {
    setLoading(true)
    try {
      if (!window.electronAPI) {
        Logger.error('LeaveManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('LeaveManagement', 'âڑ ï¸ڈ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للإجازات
        const mockLeaveRecords = [
          {
            id: 1,
            employee_id: 1,
            employee_name: 'أحمد محمد علٍ',
            employee_code: 'EMP001',
            department_name: 'قسم الموارد البشرية',
            leave_type: 'annual',
            start_date: new Date().toISOString().split('T')[0],
            end_date: new Date(Date.now() + 86400000).toISOString().split('T')[0],
            days_count: 1,
            status: 'approved'
          }
        ]

        setLeaveRecords(mockLeaveRecords as any[])
        Logger.info('LeaveManagement', '✅ تم تحميل ${mockLeaveRecords.length} سجل إجازة وهمي للمتصفح')
        return
      }

      const result = await window.electronAPI.getEmployeeLeaves(filterParams || filters)
      if (result.success) {
        setLeaveRecords(result.data)
      } else {
        messageApi.error(result.message)
      }
    } catch (_error) {
      messageApi.error('فشل في جلب بيانات الإجازات')
    } finally {
      setLoading(false)
    }
  }

  const fetchEmployees = async () => {
    try {
      Logger.info('LeaveManagement', 'ًں”„ جارٍ جلب بيانات الموظفيْ...')

      if (!window.electronAPI) {
        Logger.error('LeaveManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('LeaveManagement', 'âڑ ï¸ڈ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للموظفيْ
        const mockEmployees = [
          {
            id: 1,
            employee_code: 'EMP001',
            full_name: 'أحمد محمد علٍ',
            department_name: 'قسم الموارد البشرية',
            status: 'active'
          },
          {
            id: 2,
            employee_code: 'EMP002',
            full_name: 'فاطمة عبدالله الزهراٍْ',
            department_name: 'قسم المالية والمحاسبة',
            status: 'active'
          }
        ]

        setEmployees(mockEmployees as any[])
        Logger.info('LeaveManagement', '✅ تم تحميل ${mockEmployees.length} موظف وهمي للمتصفح')
        return
      }

      const result = await window.electronAPI.getEmployees()
      Logger.info('LeaveManagement', 'ًں“ٹ ْتٍجة جلب الموظفيْ:', result)

      if (result && result.success) {
        // تصحٍح اسم الخاصٍة من status إلى is_active
        const activeEmployees = result.data.filter((emp: any) => emp.is_active === 1)
        setEmployees(activeEmployees)
        Logger.info('LeaveManagement', '✅ تم جلب ${activeEmployees.length} موظف ْشط بنجاح')
      } else {
        Logger.error('LeaveManagement', '❌ فشل في جلب الموظفيْ:', result?.message)
        setEmployees([])
      }
    } catch (_error) {
      Logger.error('LeaveManagement', 'فشل في جلب الموظفين:', _error)
      setEmployees([])
    }
  }

  const fetchDepartments = async () => {
    try {
      if (!window.electronAPI) {
        Logger.error('LeaveManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('LeaveManagement', 'âڑ ï¸ڈ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للأقسام
        const mockDepartments = [
          { id: 1, name: 'قسم الموارد البشرية', code: 'HR001' },
          { id: 2, name: 'قسم المالية والمحاسبة', code: 'FIN001' },
          { id: 3, name: 'قسم المبيعات', code: 'SALES001' }
        ]

        setDepartments(mockDepartments as any[])
        Logger.info('LeaveManagement', '✅ تم تحميل ${mockDepartments.length} قسم وهمي للمتصفح')
        return
      }

      const result = await window.electronAPI.getEmployeeDepartments()
      if (result.success) {
        setDepartments(result.data)
      }
    } catch (_error) {
      Logger.error('LeaveManagement', 'فشل في جلب الأقسام:', _error)
    }
  }

  const handleCreateLeave = () => {
    setEditingRecord(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleApproveLeave = (record: LeaveRecord) => {
    setApprovingRecord(record)
    approvalForm.resetFields()
    setApprovalModalVisible(true)
  }

  const handleSubmit = async (values: any) => {
    try {
      const leaveData = {
        ...values,
        start_date: values.date_range[0].format('YYYY-MM-DD'),
        end_date: values.date_range[1].format('YYYY-MM-DD'),
        total_days: values.date_range[1].diff(values.date_range[0], 'day') + 1
      }
      delete leaveData.date_range

      const result = await window.electronAPI.applyEmployeeLeave(leaveData)
      if (result.success) {
        messageApi.success('تم تقدٍم طلب الإجازة بنجاح')
        setModalVisible(false)
        form.resetFields()
        fetchLeaveRecords()
      } else {
        messageApi.error(result.message)
      }
    } catch (_error) {
      messageApi.error('فشل في تقديم طلب الإجازة')
    }
  }

  const handleApprovalSubmit = async (values: any) => {
    if (!approvingRecord) return

    try {
      const approvalData = {
        ...values,
        approved_by: 1 // ٍجب الحصول على معرف المستخدم الحالٍ
      }

      const result = await window.electronAPI.approveEmployeeLeave(approvingRecord.id, approvalData)
      if (result.success) {
        messageApi.success('تم تحديث حالة الإجازة بنجاح')
        setApprovalModalVisible(false)
        approvalForm.resetFields()
        fetchLeaveRecords()
      } else {
        messageApi.error(result.message)
      }
    } catch (_error) {
      messageApi.error('فشل في تحديث حالة الإجازة')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'processing'
      case 'approved': return 'success'
      case 'rejected': return 'error'
      case 'cancelled': return 'default'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'قٍد المراجعة'
      case 'approved': return 'معتمد'
      case 'rejected': return 'مرفوض'
      case 'cancelled': return 'ملغٍ'
      default: return status
    }
  }

  const getLeaveTypeIcon = (type: string) => {
    switch (type) {
      case 'annual': return <CalendarOutlined />
      case 'sick': return <MedicineBoxOutlined />
      case 'maternity': return <HeartOutlined />
      case 'emergency': return <ExclamationCircleOutlined />
      case 'unpaid': return <HomeOutlined />
      default: return <CalendarOutlined />
    }
  }

  const getLeaveTypeText = (type: string) => {
    switch (type) {
      case 'annual': return 'إجازة سْوٍة'
      case 'sick': return 'إجازة مرضٍة'
      case 'maternity': return 'إجازة أمومة'
      case 'emergency': return 'إجازة طارئة'
      case 'unpaid': return 'إجازة بدوْ راتب'
      default: return type
    }
  }

  const getLeaveTypeColor = (type: string) => {
    switch (type) {
      case 'annual': return 'blue'
      case 'sick': return 'red'
      case 'maternity': return 'pink'
      case 'emergency': return 'orange'
      case 'unpaid': return 'default'
      default: return 'default'
    }
  }

  // دالة طباعة تقرير الإجازات
  const handlePrintLeaves = async () => {
    try {
      if (!leaveRecords || leaveRecords.length === 0) {
        messageApi.warning('لا توجد طلبات إجازات للطباعة')
        return
      }

      // استخدام طباعة مبسطة مع تصميم احترافي
      const printContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <title>تقرير طلبات الإجازات</title>
          <style>
            @page { size: A4; margin: 1cm; }
            body {
              font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
              direction: rtl;
              margin: 0;
              padding: 20px;
              color: #333;
              line-height: 1.6;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 3px solid #1890ff;
              padding-bottom: 20px;
            }
            .header h1 {
              color: #1890ff;
              margin: 0;
              font-size: 28px;
              font-weight: bold;
            }
            .header .subtitle {
              color: #666;
              margin-top: 10px;
              font-size: 16px;
            }
            .statistics {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
              margin-bottom: 30px;
              padding: 20px;
              background: #f8f9fa;
              border-radius: 8px;
            }
            .stat-item {
              text-align: center;
              padding: 10px;
              background: white;
              border-radius: 6px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .stat-value {
              font-size: 24px;
              font-weight: bold;
              color: #1890ff;
            }
            .stat-label {
              font-size: 12px;
              color: #666;
              margin-top: 5px;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 20px;
              font-size: 12px;
              background: white;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: center;
            }
            th {
              background: #1890ff;
              color: white;
              font-weight: bold;
            }
            tr:nth-child(even) {
              background: #f9f9f9;
            }
            .status-pending { color: #faad14; font-weight: bold; }
            .status-approved { color: #52c41a; font-weight: bold; }
            .status-rejected { color: #ff4d4f; font-weight: bold; }
            .status-cancelled { color: #666; font-weight: bold; }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 10px;
              color: #666;
              border-top: 1px solid #ddd;
              padding-top: 10px;
            }
            @media print {
              body { -webkit-print-color-adjust: exact; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>تقرير طلبات الإجازات</h1>
            <div class="subtitle">
              تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')} |
              إجمالي الطلبات: ${leaveRecords.length}
            </div>
          </div>

          <div class="statistics">
            <div class="stat-item">
              <div class="stat-value">${leaveRecords.filter(r => r.status === 'pending').length}</div>
              <div class="stat-label">قيد المراجعة</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">${leaveRecords.filter(r => r.status === 'approved').length}</div>
              <div class="stat-label">معتمد</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">${leaveRecords.filter(r => r.status === 'rejected').length}</div>
              <div class="stat-label">مرفوض</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">${leaveRecords.reduce((sum, r) => sum + (r.total_days || 0), 0)}</div>
              <div class="stat-label">إجمالي الأيام</div>
            </div>
          </div>

          <table>
            <thead>
              <tr>
                <th>كود الموظف</th>
                <th>اسم الموظف</th>
                <th>نوع الإجازة</th>
                <th>تاريخ البداية</th>
                <th>تاريخ النهاية</th>
                <th>عدد الأيام</th>
                <th>الحالة</th>
                <th>السبب</th>
              </tr>
            </thead>
            <tbody>
              ${leaveRecords.map(record => `
                <tr>
                  <td>${record.employee_code}</td>
                  <td>${record.employee_name}</td>
                  <td>${getLeaveTypeText(record.leave_type)}</td>
                  <td>${dayjs(record.start_date).format('YYYY-MM-DD')}</td>
                  <td>${dayjs(record.end_date).format('YYYY-MM-DD')}</td>
                  <td>${record.total_days}</td>
                  <td class="status-${record.status}">${getStatusText(record.status)}</td>
                  <td>${record.reason || '-'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="footer">
            تم إنشاء هذا التقرير بواسطة نظام ZET.IA للمحاسبة والإنتاج
          </div>
        </body>
        </html>
      `

      const printWindow = window.open('', '_blank')
      if (printWindow) {
        const printDoc = (printWindow as any).document
        if (printDoc) {
          printDoc.write(printContent)
          printDoc.close()
        }
        printWindow.print()
        messageApi.success('تم إرسال التقرير للطباعة')
      }
    } catch (_error) {
      Logger.error('LeaveManagement', 'خطأ في الطباعة:', _error)
      messageApi.error('فشل في طباعة التقرير')
    }
  }

  // دالة تصدير Excel للإجازات
  const handleExportLeaves = async () => {
    try {
      if (!leaveRecords || leaveRecords.length === 0) {
        messageApi.warning('لا توجد طلبات إجازات للتصدير')
        return
      }

      // استخدام MasterPrintService للتصدير
      const { MasterPrintService } = await import('../../services/MasterPrintService')

      const exportData = leaveRecords.map(record => ({
        'كود الموظف': record.employee_code,
        'اسم الموظف': record.employee_name,
        'القسم': record.department_name || '-',
        'نوع الإجازة': getLeaveTypeText(record.leave_type),
        'تاريخ البداية': dayjs(record.start_date).format('YYYY-MM-DD'),
        'تاريخ النهاية': dayjs(record.end_date).format('YYYY-MM-DD'),
        'عدد الأيام': record.total_days,
        'الحالة': getStatusText(record.status),
        'السبب': record.reason || '-',
        'ملاحظات': record.notes || '-',
        'تاريخ التقديم': dayjs(record.created_at).format('YYYY-MM-DD'),
        'معتمد من': record.approved_by_name || '-',
        'تاريخ الاعتماد': record.approved_date ? dayjs(record.approved_date).format('YYYY-MM-DD') : '-'
      }))

      // تصدير إلى Excel باستخدام XLSX
      const ws = XLSX.utils.json_to_sheet(exportData)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, 'طلبات الإجازات')
      XLSX.writeFile(wb, 'تقرير_طلبات_الإجازات.xlsx')
      messageApi.success(`تم تصدير ${exportData.length} طلب إجازة بنجاح`)
    } catch (_error) {
      Logger.error('LeaveManagement', 'خطأ في تصدير الإجازات:', _error)
      messageApi.error('فشل في تصدير البيانات')
    }
  }

  const columns: ColumnsType<LeaveRecord> = [
    {
      title: 'الموظف',
      key: 'employee',
      width: 200,
      fixed: 'left',
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.employee_name}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.employee_code} - {record.department_name}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: 'نوع الإجازة',
      dataIndex: 'leave_type',
      key: 'leave_type',
      width: 150,
      render: (type) => (
        <Tag color={getLeaveTypeColor(type)} icon={getLeaveTypeIcon(type)}>
          {getLeaveTypeText(type)}
        </Tag>
      )
    },
    {
      title: 'تارٍخ البداٍة',
      dataIndex: 'start_date',
      key: 'start_date',
      width: 120,
      render: (date) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'تارٍخ الْهاٍة',
      dataIndex: 'end_date',
      key: 'end_date',
      width: 120,
      render: (date) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'عدد الأٍام',
      dataIndex: 'total_days',
      key: 'total_days',
      width: 100,
      render: (days) => (
        <Badge count={days} style={{ backgroundColor: '#52c41a' }} />
      )
    },
    {
      title: 'السبب',
      dataIndex: 'reason',
      key: 'reason',
      width: 200,
      render: (reason) => reason ? (
        <Tooltip title={reason}>
          <Text ellipsis style={{ maxWidth: 180 }}>
            {reason}
          </Text>
        </Tooltip>
      ) : '-'
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'تارٍخ التقدٍم',
      dataIndex: 'applied_date',
      key: 'applied_date',
      width: 120,
      render: (date) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'معتمد من',
      dataIndex: 'approved_by_name',
      key: 'approved_by_name',
      width: 150,
      render: (name, record) => record.status === 'approved' && name ? (
        <Space>
          <UserOutlined />
          {name}
        </Space>
      ) : '-'
    },
    {
      title: 'الإجراط،ات',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Tooltip title="عرض التفاصٍل">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingRecord(record)
                setModalVisible(true)
              }}
            />
          </Tooltip>
          {record.status === 'pending' && (
            <Tooltip title="الموافقة/الرفض">
              <Button
                type="default"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => handleApproveLeave(record)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  const totalLeaves = leaveRecords.length
  const pendingLeaves = leaveRecords.filter(record => record.status === 'pending').length
  const approvedLeaves = leaveRecords.filter(record => record.status === 'approved').length
  const rejectedLeaves = leaveRecords.filter(record => record.status === 'rejected').length

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <CalendarOutlined /> إدارة الإجازات
      </Title>
      
      {/* إحصائيات سرٍعة */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="إجمالي الطلبات"
              value={totalLeaves}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="قٍد المراجعة"
              value={pendingLeaves}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="معتمد"
              value={approvedLeaves}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="مرفوض"
              value={rejectedLeaves}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* فلاتر البحث */}
      <Card style={{ marginBottom: '16px' }}>
        <Form layout="inline" onFinish={(values) => {
          setFilters(values)
          fetchLeaveRecords(values)
        }}>
          <Form.Item name="employee_id" label="الموظف">
            <Select placeholder="اختر الموظف" style={{ width: 200 }} allowClear>
              {employees.map(emp => (
                <Option key={emp.id} value={emp.id}>
                  {emp.full_name} - {emp.employee_code}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="leave_type" label="نوع الإجازة">
            <Select placeholder="اختر النوع" style={{ width: 150 }} allowClear>
              <Option value="annual">إجازة سْوٍة</Option>
              <Option value="sick">إجازة مرضٍة</Option>
              <Option value="maternity">إجازة أمومة</Option>
              <Option value="emergency">إجازة طارئة</Option>
              <Option value="unpaid">إجازة بدوْ راتب</Option>
            </Select>
          </Form.Item>

          <Form.Item name="status" label="الحالة">
            <Select placeholder="اختر الحالة" style={{ width: 120 }} allowClear>
              <Option value="pending">قٍد المراجعة</Option>
              <Option value="approved">معتمد</Option>
              <Option value="rejected">مرفوض</Option>
              <Option value="cancelled">ملغٍ</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit">
              بحث
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* جدول الإجازات */}
      <Card
        title={
          <Space>
            <CalendarOutlined />
            <span>طلبات الإجازات</span>
          </Space>
        }
        extra={
          <Space wrap>
            <Button
              icon={<FileExcelOutlined />}
              onClick={handleExportLeaves}
              disabled={leaveRecords.length === 0}
            >
              تصدير Excel
            </Button>
            <Button
              icon={<PrinterOutlined />}
              onClick={handlePrintLeaves}
              disabled={leaveRecords.length === 0}
            >
              طباعة
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateLeave}
            >
              طلب إجازة
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={leaveRecords}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `إجمالي ${total} طلب`
          }}
        />
      </Card>

      {/* نافذة تقدٍم/عرض طلب الإجازة */}
      <Modal
        title={editingRecord ? 'تفاصٍل طلب الإجازة' : 'تقدٍم طلب إجازة جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
          setEditingRecord(null)
        }}
        footer={editingRecord ? null : undefined}
        width={700}
        destroyOnHidden
      >
        {editingRecord ? (
          // عرض تفاصٍل الطلب
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <Card title="معلومات الموظف" size="small">
                  <p><strong>الاسم:</strong> {editingRecord.employee_name}</p>
                  <p><strong>الكود:</strong> {editingRecord.employee_code}</p>
                  <p><strong>القسم:</strong> {editingRecord.department_name}</p>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="معلومات الطلب" size="small">
                  <p><strong>نوع الإجازة:</strong> <Tag color={getLeaveTypeColor(editingRecord.leave_type)}>{getLeaveTypeText(editingRecord.leave_type)}</Tag></p>
                  <p><strong>الحالة:</strong> <Tag color={getStatusColor(editingRecord.status)}>{getStatusText(editingRecord.status)}</Tag></p>
                  <p><strong>تارٍخ التقدٍم:</strong> {dayjs(editingRecord.applied_date).format('YYYY-MM-DD')}</p>
                </Card>
              </Col>
            </Row>

            <Divider />

            <Row gutter={16}>
              <Col span={8}>
                <p><strong>تارٍخ البداٍة:</strong></p>
                <p>{dayjs(editingRecord.start_date).format('YYYY-MM-DD')}</p>
              </Col>
              <Col span={8}>
                <p><strong>تارٍخ الْهاٍة:</strong></p>
                <p>{dayjs(editingRecord.end_date).format('YYYY-MM-DD')}</p>
              </Col>
              <Col span={8}>
                <p><strong>عدد الأٍام:</strong></p>
                <p><Badge count={editingRecord.total_days} style={{ backgroundColor: '#52c41a' }} /></p>
              </Col>
            </Row>

            {editingRecord.reason && (
              <>
                <Divider />
                <p><strong>السبب:</strong></p>
                <p>{editingRecord.reason}</p>
              </>
            )}

            {editingRecord.status === 'approved' && editingRecord.approved_by_name && (
              <>
                <Divider />
                <p><strong>معتمد من:</strong> {editingRecord.approved_by_name}</p>
                <p><strong>تارٍخ الاعتماد:</strong> {editingRecord.approved_date ? dayjs(editingRecord.approved_date).format('YYYY-MM-DD') : '-'}</p>
              </>
            )}

            {editingRecord.status === 'rejected' && editingRecord.rejection_reason && (
              <>
                <Divider />
                <p><strong>سبب الرفض:</strong></p>
                <p style={{ color: '#ff4d4f' }}>{editingRecord.rejection_reason}</p>
              </>
            )}

            {editingRecord.notes && (
              <>
                <Divider />
                <p><strong>ملاحّات:</strong></p>
                <p>{editingRecord.notes}</p>
              </>
            )}
          </div>
        ) : (
          // ْموذج تقدٍم طلب جديد
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  name="employee_id"
                  label="الموظف"
                  rules={[{ required: true, message: 'ٍرجى اختٍار الموظف' }]}
                >
                  <Select placeholder="اختر الموظف" showSearch>
                    {employees.map(emp => (
                      <Option key={emp.id} value={emp.id}>
                        {emp.full_name} - {emp.employee_code} ({emp.department_name})
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="leave_type"
                  label="نوع الإجازة"
                  rules={[{ required: true, message: 'ٍرجى اختٍار نوع الإجازة' }]}
                >
                  <Select>
                    <Option value="annual">إجازة سْوٍة</Option>
                    <Option value="sick">إجازة مرضٍة</Option>
                    <Option value="maternity">إجازة أمومة</Option>
                    <Option value="emergency">إجازة طارئة</Option>
                    <Option value="unpaid">إجازة بدوْ راتب</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="date_range"
                  label="فترة الإجازة"
                  rules={[{ required: true, message: 'ٍرجى اختٍار فترة الإجازة' }]}
                >
                  <RangePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  name="reason"
                  label="سبب الإجازة"
                  rules={[{ required: true, message: 'ٍرجى إدخال سبب الإجازة' }]}
                >
                  <TextArea rows={3} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  name="notes"
                  label="ملاحّات إضافية"
                >
                  <TextArea rows={2} />
                </Form.Item>
              </Col>
            </Row>

            <Divider />

            <Row justify="end">
              <Space>
                <Button onClick={() => setModalVisible(false)}>
                  إلغاء
                </Button>
                <Button type="primary" htmlType="submit">
                  تقدٍم الطلب
                </Button>
              </Space>
            </Row>
          </Form>
        )}
      </Modal>

      {/* نافذة الموافقة/الرفض */}
      <Modal
        title="الموافقة على طلب الإجازة"
        open={approvalModalVisible}
        onCancel={() => {
          setApprovalModalVisible(false)
          approvalForm.resetFields()
          setApprovingRecord(null)
        }}
        footer={null}
        width={500}
      >
        {approvingRecord && (
          <div>
            <Card size="small" style={{ marginBottom: '16px' }}>
              <p><strong>الموظف:</strong> {approvingRecord.employee_name}</p>
              <p><strong>نوع الإجازة:</strong> {getLeaveTypeText(approvingRecord.leave_type)}</p>
              <p><strong>الفترة:</strong> {dayjs(approvingRecord.start_date).format('YYYY-MM-DD')} إلى {dayjs(approvingRecord.end_date).format('YYYY-MM-DD')}</p>
              <p><strong>عدد الأٍام:</strong> {approvingRecord.total_days}</p>
              <p><strong>السبب:</strong> {approvingRecord.reason}</p>
            </Card>

            <Form
              form={approvalForm}
              layout="vertical"
              onFinish={handleApprovalSubmit}
            >
              <Form.Item
                name="status"
                label="القرار"
                rules={[{ required: true, message: 'ٍرجى اختٍار القرار' }]}
              >
                <Select>
                  <Option value="approved">موافقة</Option>
                  <Option value="rejected">رفض</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="rejection_reason"
                label="سبب الرفض (في حالة الرفض)"
              >
                <TextArea rows={3} />
              </Form.Item>

              <Row justify="end">
                <Space>
                  <Button onClick={() => setApprovalModalVisible(false)}>
                    إلغاء
                  </Button>
                  <Button type="primary" htmlType="submit">
                    حفظ القرار
                  </Button>
                </Space>
              </Row>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default LeaveManagement

