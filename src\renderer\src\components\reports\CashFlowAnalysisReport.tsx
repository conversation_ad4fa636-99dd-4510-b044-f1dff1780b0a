/**
 * تقرير تحليل التدفق النقدي
 * تقرير مالي يعرض حركة النقد الداخل والخارج
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const CashFlowAnalysisReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'cash_flow' as ReportType}
      title="تقرير تحليل التدفق النقدي"
      description="تحليل شامل لحركة النقد الداخل والخارج مع التنبؤات المستقبلية"
      showDateRange={true}
      showDepartmentFilter={true}
      showStatusFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="cash_flow_analysis_report"
      defaultFilters={{
        sortBy: 'date',
        sortOrder: 'desc'
      }}
    />
  )
}

export default CashFlowAnalysisReport
