import { ipcMain } from 'electron'
import { CodeGeneratorService } from '../services'
import { Logger } from '../utils/logger'

// متغيرات الخدمات
let customerService: any = null
let supplierService: any = null
let codeGeneratorService: CodeGeneratorService | null = null

// دالة تعيين الخدمات
export function setCommonServices(customer: any, supplier: any, codeGenerator?: CodeGeneratorService) {
  customerService = customer
  supplierService = supplier
  codeGeneratorService = codeGenerator || null
}

// تسجيل المعالجات المشتركة
export function registerCommonHandlers() {
  // الكيانات للتحويل (العملاء والموردين)
  ipcMain.handle('get-entities-for-transfer', async () => {
    try {
      const customersResult = await customerService.getCustomers()
      const suppliersResult = await supplierService.getSuppliers()
      
      const customers = customersResult.success ? customersResult.data : []
      const suppliers = suppliersResult.success ? suppliersResult.data : []
      
      const entities = [
        ...customers.map((customer: any) => ({
          id: customer.id,
          name: customer.name,
          type: 'customer',
          code: customer.code
        })),
        ...suppliers.map((supplier: any) => ({
          id: supplier.id,
          name: supplier.name,
          type: 'supplier',
          code: supplier.code
        }))
      ]
      
      return { success: true, data: entities }
    } catch (error) {
      Logger.error('CommonHandlers', 'خطأ في جلب الكيانات للتحويل:', error)
      return { success: false, message: 'حدث خطأ في جلب الكيانات للتحويل' }
    }
  })

  // الحصول على جميع الكيانات (عملاء وموردين)
  ipcMain.handle('get-all-entities', async () => {
    try {
      const customersResult = await customerService.getCustomers()
      const suppliersResult = await supplierService.getSuppliers()
      
      const customers = customersResult.success ? customersResult.data : []
      const suppliers = suppliersResult.success ? suppliersResult.data : []
      
      return { 
        success: true, 
        data: {
          customers,
          suppliers,
          all: [
            ...customers.map((customer: any) => ({ ...customer, type: 'customer' })),
            ...suppliers.map((supplier: any) => ({ ...supplier, type: 'supplier' }))
          ]
        }
      }
    } catch (error) {
      Logger.error('CommonHandlers', 'خطأ في جلب جميع الكيانات:', error)
      return { success: false, message: 'حدث خطأ في جلب جميع الكيانات' }
    }
  })

  // البحث في الكيانات
  ipcMain.handle('search-entities', async (_event, searchTerm: string, entityType?: 'customer' | 'supplier') => {
    try {
      const entities: any[] = []
      
      if (!entityType || entityType === 'customer') {
        const customersResult = await customerService.getCustomers()
        const customers = customersResult.success ? customersResult.data : []
        entities.push(...customers.map((customer: any) => ({ ...customer, type: 'customer' })))
      }
      
      if (!entityType || entityType === 'supplier') {
        const suppliersResult = await supplierService.getSuppliers()
        const suppliers = suppliersResult.success ? suppliersResult.data : []
        entities.push(...suppliers.map((supplier: any) => ({ ...supplier, type: 'supplier' })))
      }
      
      // تصفية النتائج بناءً على مصطلح البحث
      const filteredEntities = entities.filter(entity => 
        entity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entity.code.toLowerCase().includes(searchTerm.toLowerCase())
      )
      
      return { success: true, data: filteredEntities }
    } catch (error) {
      Logger.error('CommonHandlers', 'خطأ في البحث في الكيانات:', error)
      return { success: false, message: 'حدث خطأ في البحث في الكيانات' }
    }
  })

  // التحقق من تفرد القيم - دالة موحدة
  ipcMain.handle('check-uniqueness', async (_event, params: { tableName: string, columnName: string, value: string, excludeId?: number }) => {
    try {
      if (!codeGeneratorService) {
        return { success: false, message: 'خدمة توليد الأكواد غير متاحة' }
      }

      const { tableName, columnName, value, excludeId } = params
      const isUnique = codeGeneratorService.checkCodeUniqueness(tableName, columnName, value, excludeId)
      return { success: true, data: { isUnique } }
    } catch (error) {
      Logger.error('CommonHandlers', 'خطأ في التحقق من تفرد القيمة:', error)
      return { success: false, message: 'حدث خطأ في التحقق من تفرد القيمة' }
    }
  })

  // معالجات للتوافق مع الكود الموجود - تستخدم الدالة الموحدة
  ipcMain.handle('check-code-uniqueness', async (_event, tableName: string, codeColumn: string, code: string, excludeId?: number) => {
    return await ipcMain.emit('check-uniqueness', _event, { tableName, columnName: codeColumn, value: code, excludeId })
  })

  ipcMain.handle('check-invoice-number-uniqueness', async (_event, tableName: string, invoiceNumber: string, excludeId?: number) => {
    return await ipcMain.emit('check-uniqueness', _event, { tableName, columnName: 'invoice_number', value: invoiceNumber, excludeId })
  })

  ipcMain.handle('check-order-number-uniqueness', async (_event, tableName: string, orderNumber: string, excludeId?: number) => {
    return await ipcMain.emit('check-uniqueness', _event, { tableName, columnName: 'order_number', value: orderNumber, excludeId })
  })
}
