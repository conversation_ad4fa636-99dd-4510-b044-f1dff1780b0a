import React from 'react';
import { Tag, Typography, Progress, Rate } from 'antd';
import UniversalReport from './UniversalReport';
import type { ReportData, ReportType } from '../../types/reports';

const { Text } = Typography;

const SupplierQualityReport: React.FC = () => {
  // دالة إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      console.log('🔄 إنشاء تقرير جودة الموردين...', filters);

      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getSupplierQualityReport(filters);

      if (!response || !response.success || !response.data || !Array.isArray(response.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const supplierQualityData = response.data;

      // معالجة البيانات
      const processedData = supplierQualityData.map((item: any, index: number) => ({
        ...item,
        key: item.supplier_id || index,
        quality_percentage: (item.quality_score / 5) * 100,
        return_rate: item.total_orders > 0 ? (item.returns / item.total_orders) * 100 : 0
      }));

      // حساب الإحصائيات
      const totalSuppliers = processedData.length;
      const avgDeliveryRate = processedData.reduce((sum: number, item: any) => sum + item.on_time_delivery_rate, 0) / totalSuppliers;
      const avgQualityScore = processedData.reduce((sum: number, item: any) => sum + item.quality_score, 0) / totalSuppliers;
      const avgDefectRate = processedData.reduce((sum: number, item: any) => sum + item.defect_rate, 0) / totalSuppliers;
      
      const excellentSuppliers = processedData.filter(item => item.overall_rating >= 4.5).length;
      const lowRiskSuppliers = processedData.filter(item => item.risk_level === 'low').length;

      const summary = {
        totalSuppliers,
        avgDeliveryRate: Math.round(avgDeliveryRate * 100) / 100,
        avgQualityScore: Math.round(avgQualityScore * 100) / 100,
        avgDefectRate: Math.round(avgDefectRate * 100) / 100,
        excellentSuppliers,
        lowRiskSuppliers
      };

      console.log('✅ تم إنشاء تقرير جودة الموردين بنجاح');
      
      return {
        title: 'تقرير جودة الموردين',
        data: processedData,
        columns,
        summary,
        metadata: {
          reportType: 'supplier_quality' as ReportType,
          generatedAt: new Date().toISOString(),
          filters,
          totalRecords: processedData.length
        }
      };
    } catch (error) {
      console.error('❌ خطأ في إنشاء تقرير جودة الموردين:', error);
      throw error;
    }
  };

  // إعداد الأعمدة
  const columns = [
    {
      title: 'كود المورد',
      dataIndex: 'supplier_code',
      key: 'supplier_code',
      width: 120,
      render: (code: string) => (
        <Text strong style={{ color: '#1890ff' }}>{code}</Text>
      )
    },
    {
      title: 'اسم المورد',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      width: 200,
      render: (name: string) => (
        <Text strong>{name}</Text>
      )
    },
    {
      title: 'عدد الطلبات',
      dataIndex: 'total_orders',
      key: 'total_orders',
      width: 120,
      align: 'center' as const,
      render: (count: number) => (
        <Tag color="blue">{count}</Tag>
      )
    },
    {
      title: 'معدل التسليم في الوقت',
      dataIndex: 'on_time_delivery_rate',
      key: 'on_time_delivery_rate',
      width: 150,
      align: 'center' as const,
      render: (rate: number) => (
        <Progress 
          percent={Math.round(rate)} 
          size="small"
          strokeColor={rate >= 90 ? '#52c41a' : rate >= 75 ? '#fa8c16' : '#ff4d4f'}
        />
      )
    },
    {
      title: 'تقييم الجودة',
      dataIndex: 'quality_score',
      key: 'quality_score',
      width: 120,
      align: 'center' as const,
      render: (score: number) => (
        <Rate disabled value={score} allowHalf style={{ fontSize: '14px' }} />
      )
    },
    {
      title: 'معدل العيوب (%)',
      dataIndex: 'defect_rate',
      key: 'defect_rate',
      width: 120,
      align: 'center' as const,
      render: (rate: number) => (
        <Text style={{ color: rate <= 2 ? '#52c41a' : rate <= 5 ? '#fa8c16' : '#ff4d4f' }}>
          {rate.toFixed(1)}%
        </Text>
      )
    },
    {
      title: 'المرتجعات',
      dataIndex: 'returns',
      key: 'returns',
      width: 100,
      align: 'center' as const,
      render: (returns: number) => (
        <Tag color={returns === 0 ? 'green' : returns <= 2 ? 'orange' : 'red'}>
          {returns}
        </Tag>
      )
    },
    {
      title: 'الشهادات',
      dataIndex: 'certification_status',
      key: 'certification_status',
      width: 150,
      render: (status: string) => (
        <Tag color="purple">{status}</Tag>
      )
    },
    {
      title: 'التقييم العام',
      dataIndex: 'overall_rating',
      key: 'overall_rating',
      width: 120,
      align: 'center' as const,
      render: (rating: number) => (
        <Rate disabled value={rating} allowHalf style={{ fontSize: '14px' }} />
      )
    },
    {
      title: 'مستوى المخاطر',
      dataIndex: 'risk_level',
      key: 'risk_level',
      width: 120,
      align: 'center' as const,
      render: (level: string) => {
        const riskConfig = {
          low: { color: 'green', text: 'منخفض' },
          medium: { color: 'orange', text: 'متوسط' },
          high: { color: 'red', text: 'عالي' }
        };
        const config = riskConfig[level as keyof typeof riskConfig] || { color: 'default', text: level };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: 'اتجاه التحسن',
      dataIndex: 'improvement_trend',
      key: 'improvement_trend',
      width: 120,
      align: 'center' as const,
      render: (trend: string) => {
        const trendConfig = {
          improving: { color: 'green', text: 'متحسن' },
          stable: { color: 'blue', text: 'مستقر' },
          declining: { color: 'red', text: 'متراجع' }
        };
        const config = trendConfig[trend as keyof typeof trendConfig] || { color: 'default', text: trend };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    }
  ];

  return (
    <UniversalReport
      reportType={'supplier_quality' as ReportType}
      title="تقرير جودة الموردين"
      description="تقرير شامل لجودة الموردين مع تحليل الأداء والتقييمات والمخاطر"
      onGenerateReport={generateReport}
      showDateRange={true}
      showSupplierFilter={true}
      showPrintOptions={true}
      showExportOptions={true}
    />
  );
};

export default SupplierQualityReport;
