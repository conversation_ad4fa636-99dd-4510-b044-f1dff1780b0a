import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal, Card, Typography, Space, Button, List, Avatar,
  Input, Tag, Divider, Alert, Tooltip, Badge, Rate, Progress
} from 'antd'
import {
  QuestionCircleOutlined, RobotOutlined, BulbOutlined, BookOutlined,
  PlayCircleOutlined, SearchOutlined, StarOutlined, CloseOutlined,
  SoundOutlined, SettingOutlined, MessageOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { tutorialSystem } from '../../utils/tutorialSystem'
import { audioSystem } from '../../utils/audioSystem'

const { Title, Text, Paragraph } = Typography
const { Search } = Input

const AssistantModal = styled(Modal)`
  .ant-modal-content {
    direction: rtl;
    border-radius: 16px;
  }
  
  .ant-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px 16px 0 0;
    
    .ant-modal-title {
      color: white;
      font-weight: bold;
    }
  }
`

const ChatContainer = styled.div`
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 16px 0;
`

const MessageBubble = styled.div<{ isUser?: boolean }>`
  display: flex;
  margin-bottom: 12px;
  justify-content: ${props => props.isUser ? 'flex-start' : 'flex-end'};
  
  .message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    background: ${props => props.isUser ? '#1890ff' : '#f0f0f0'};
    color: ${props => props.isUser ? 'white' : '#333'};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .message-avatar {
    margin: ${props => props.isUser ? '0 8px 0 0' : '0 0 0 8px'};
  }
`

const QuickActions = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 16px 0;
  
  .action-button {
    border-radius: 20px;
    font-size: 12px;
  }
`

interface Message {
  id: string
  content: string
  isUser: boolean
  timestamp: Date
  type?: 'text' | 'suggestion' | 'tutorial'
}

interface SmartAssistantProps {
  visible: boolean
  onClose: () => void
  onStartTutorial?: (moduleId: string) => void
}

const SmartAssistant: React.FC<SmartAssistantProps> = ({
  visible,
  onClose,
  onStartTutorial
}) => {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)

  useEffect(() => {
    if (visible && messages.length === 0) {
      // رسالة ترحيب
      addMessage({
        id: 'welcome',
        content: 'مرحباً! أنا مساعدك الذكي في برنامج المحاسبة والإنتاج. كيف يمكنني مساعدتك اليوم؟',
        isUser: false,
        timestamp: new Date(),
        type: 'text'
      })
    }
  }, [visible])

  const addMessage = (message: Message) => {
    setMessages(prev => [...prev, message])
    audioSystem.playSound('notification')
  }

  const handleUserMessage = (content: string) => {
    if (!content.trim()) return

    // إضافة رسالة المستخدم
    addMessage({
      id: Date.now().toString(),
      content,
      isUser: true,
      timestamp: new Date(),
      type: 'text'
    })

    setInputValue('')
    setIsTyping(true)

    // محاكاة الرد التلقائي
    setTimeout(() => {
      const response = generateResponse(content)
      addMessage(response)
      setIsTyping(false)
    }, 1500)
  }

  const generateResponse = (userInput: string): Message => {
    const input = userInput.toLowerCase()
    
    // ردود ذكية بناءً على الكلمات المفتاحية
    if (input.includes('إنتاج') || input.includes('تصنيع')) {
      return {
        id: Date.now().toString(),
        content: 'الإنتاج هو قلب النّام! أنصحك بالبدء بوحدة "إدارة الإنتاج - الدورة المستندية الكاملة" التي تشرح كل شيء بالتفصيل.',
        isUser: false,
        timestamp: new Date(),
        type: 'suggestion'
      }
    }
    
    if (input.includes('مخزون') || input.includes('أصناف')) {
      return {
        id: Date.now().toString(),
        content: 'إدارة المخزون أساسية لأي نّام. ابدأ بوحدة "إدارة المخزون" لتتعلم كيفية إنشاء الفئات والأصناف والمستودعات.',
        isUser: false,
        timestamp: new Date(),
        type: 'suggestion'
      }
    }
    
    if (input.includes('مبيعات') || input.includes('فواتير')) {
      return {
        id: Date.now().toString(),
        content: 'وحدة المبيعات تغطي إدارة العملاء وأوامر البيع والفواتير. هل تريد البدء بالتعلم التفاعلي؟',
        isUser: false,
        timestamp: new Date(),
        type: 'suggestion'
      }
    }
    
    if (input.includes('مشتريات') || input.includes('موردين')) {
      return {
        id: Date.now().toString(),
        content: 'وحدة المشتريات تشمل إدارة الموردين وأوامر الشراء. أنصح بإكمال وحدة المخزون أولاً.',
        isUser: false,
        timestamp: new Date(),
        type: 'suggestion'
      }
    }
    
    if (input.includes('محاسبة') || input.includes('مالية')) {
      return {
        id: Date.now().toString(),
        content: 'الإدارة المالية تتطلب فهم الوحدات الأساسية أولاً. ابدأ بالمبيعات والمشتريات ثم انتقل للمحاسبة.',
        isUser: false,
        timestamp: new Date(),
        type: 'suggestion'
      }
    }
    
    if (input.includes('بداية') || input.includes('أبدأ') || input.includes('جديد')) {
      return {
        id: Date.now().toString(),
        content: 'مرحباً بك! أنصحك بالبدء بوحدة "مقدمة البرنامج" ثم "إدارة المخزون". هذا سيعطيك أساساً قوياً.',
        isUser: false,
        timestamp: new Date(),
        type: 'suggestion'
      }
    }
    
    if (input.includes('مساعدة') || input.includes('help')) {
      return {
        id: Date.now().toString(),
        content: 'يمكنني مساعدتك في:\n• اختيار الوحدة التعليمية المناسبة\n• شرح ميزات البرنامج\n• إرشادك للدورة المستندية\n• حل المشاكل الشائعة',
        isUser: false,
        timestamp: new Date(),
        type: 'text'
      }
    }
    
    // رد افتراضي
    return {
      id: Date.now().toString(),
      content: 'فهمت سؤالك. دعني أقترح عليك البدء بالوحدات التعليمية المناسبة لاحتياجاتك. ما هو مجال اهتمامك الأساسي؟',
      isUser: false,
      timestamp: new Date(),
      type: 'text'
    }
  }

  const quickActions = [
    {
      label: 'بداية التعلم',
      action: () => handleUserMessage('أريد البدء بتعلم البرنامج من الصفر')
    },
    {
      label: 'الدورة المستندية',
      action: () => handleUserMessage('أريد تعلم الدورة المستندية الإنتاجية')
    },
    {
      label: 'إدارة المخزون',
      action: () => handleUserMessage('كيف أدير المخزون والأصناف؟')
    },
    {
      label: 'المبيعات والفواتير',
      action: () => handleUserMessage('أريد تعلم إدارة المبيعات والفواتير')
    },
    {
      label: 'المشتريات',
      action: () => handleUserMessage('كيف أدير المشتريات والموردين؟')
    },
    {
      label: 'الإنتاج',
      action: () => handleUserMessage('أريد تعلم إدارة الإنتاج بالتفصيل')
    }
  ]

  const tutorialModules = tutorialSystem.getModules()

  return (
    <AssistantModal
      title={
        <Space>
          <RobotOutlined />
          المساعد الذكي
          <Badge count="AI" style={{ backgroundColor: '#52c41a' }} />
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={600}
      footer={null}
      destroyOnHidden
    >
      <Alert
        message="مساعد ذكي متطور"
        description="أنا هنا لمساعدتك في تعلم البرنامج بأفضل طريقة ممكنة"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* الإجراءات السريعة */}
      <Title level={5}>
        <BulbOutlined /> إجراءات سريعة:
      </Title>
      <QuickActions>
        {quickActions.map((action, index) => (
          <Button
            key={index}
            size="small"
            className="action-button"
            onClick={action.action}
          >
            {action.label}
          </Button>
        ))}
      </QuickActions>

      <Divider />

      {/* منطقة المحادثة */}
      <ChatContainer>
        {messages.map(message => (
          <MessageBubble key={message.id} isUser={message.isUser}>
            {!message.isUser && (
              <Avatar
                className="message-avatar"
                icon={<RobotOutlined />}
                style={{ backgroundColor: '#1890ff' }}
              />
            )}
            <div className="message-content">
              {message.content}
            </div>
            {message.isUser && (
              <Avatar
                className="message-avatar"
                icon={<MessageOutlined />}
                style={{ backgroundColor: '#52c41a' }}
              />
            )}
          </MessageBubble>
        ))}
        
        {isTyping && (
          <MessageBubble isUser={false}>
            <Avatar
              className="message-avatar"
              icon={<RobotOutlined />}
              style={{ backgroundColor: '#1890ff' }}
            />
            <div className="message-content">
              <Text type="secondary">يكتب...</Text>
            </div>
          </MessageBubble>
        )}
      </ChatContainer>

      {/* مربع الإدخال */}
      <Search
        placeholder="اكتب سؤالك هنا..."
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onSearch={handleUserMessage}
        enterButton="إرسال"
        size="large"
      />

      <Divider />

      {/* الوحدات المقترحة */}
      <Title level={5}>
        <BookOutlined /> الوحدات التعليمية المتاحة:
      </Title>
      <List
        size="small"
        dataSource={tutorialModules.slice(0, 4)}
        renderItem={module => (
          <List.Item
            actions={[
              <Button
                key="start"
                type="link"
                icon={<PlayCircleOutlined />}
                onClick={() => {
                  if (onStartTutorial) {
                    onStartTutorial(module.id)
                    onClose()
                  }
                }}
              >
                بدء
              </Button>
            ]}
          >
            <List.Item.Meta
              avatar={
                <Avatar
                  style={{ 
                    backgroundColor: module.category === 'production' ? '#fa8c16' : '#1890ff' 
                  }}
                >
                  {module.category === 'production' ? '🏭' : '📚'}
                </Avatar>
              }
              title={module.title}
              description={
                <Space>
                  <Text type="secondary">{module.estimatedTime} دقيقة</Text>
                  <Tag color={module.category === 'production' ? 'orange' : 'blue'}>
                    {module.category === 'production' ? 'إنتاج' : 'عام'}
                  </Tag>
                  {module.isCompleted && (
                    <Tag color="green">مكتمل</Tag>
                  )}
                </Space>
              }
            />
          </List.Item>
        )}
      />

      {/* إحصائيات التقدم */}
      <Card size="small" style={{ marginTop: 16 }}>
        <Title level={5}>تقدمك في التعلم:</Title>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text>الوحدات المكتملة: {tutorialModules.filter(m => m.isCompleted).length} / {tutorialModules.length}</Text>
            <Progress
              percent={Math.round((tutorialModules.filter(m => m.isCompleted).length / tutorialModules.length) * 100)}
              size="small"
              status="active"
            />
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Text type="secondary">استمر في التعلم!</Text>
            <Rate disabled defaultValue={4} style={{ fontSize: '14px' }} />
          </div>
        </Space>
      </Card>
    </AssistantModal>
  )
}

export default SmartAssistant
