import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  Card,
  Button,
  Typography,
  List,
  Space,
  Divider,
  Switch,
  Slider,
  message,
  Collapse,
  Tag
} from 'antd'
import {
  BookOutlined,
  PlayCircleOutlined,
  SoundOutlined,
  QuestionCircleOutlined,
  InboxOutlined,
  ShoppingCartOutlined,
  ShopOutlined,
  ToolOutlined,
  BankOutlined
} from '@ant-design/icons'
import { audioSystem } from '../../utils/audioSystem'

const { Title, Text } = Typography
const { Panel } = Collapse

interface SimpleTutorialProps {
  visible: boolean
  onClose: () => void
}

// بيانات الأقسام البسيطة
const tutorialSections = [
  {
    id: 'quick-start',
    title: 'دليل البدء السريع',
    icon: <QuestionCircleOutlined />,
    duration: '5 دقائق',
    description: 'تعلم أساسيات البرنامج والبدء السريع',
    color: '#52c41a'
  },
  {
    id: 'inventory',
    title: 'إدارة المخزون',
    icon: <InboxOutlined />,
    duration: '10 دقائق',
    description: 'إدارة الأصناف والمستودعات والجرد',
    color: '#1890ff'
  },
  {
    id: 'sales',
    title: 'إدارة المبيعات',
    icon: <ShoppingCartOutlined />,
    duration: '10 دقائق',
    description: 'العملاء وأوامر البيع والفواتير',
    color: '#722ed1'
  },
  {
    id: 'purchases',
    title: 'إدارة المشتريات',
    icon: <ShopOutlined />,
    duration: '10 دقائق',
    description: 'الموردين وأوامر الشراء والاستلام',
    color: '#fa8c16'
  },
  {
    id: 'production',
    title: 'إدارة الإنتاج',
    icon: <ToolOutlined />,
    duration: '15 دقيقة',
    description: 'الدورة المستندية الإنتاجية الكاملة',
    color: '#eb2f96'
  },
  {
    id: 'finance',
    title: 'الإدارة المالية',
    icon: <BankOutlined />,
    duration: '10 دقيقة',
    description: 'الحسابات والتقارير المالية',
    color: '#13c2c2'
  }
]

const SimpleTutorial: React.FC<SimpleTutorialProps> = ({ visible, onClose }) => {
  const [audioEnabled, setAudioEnabled] = useState(true)
  const [audioVolume, setAudioVolume] = useState(50)

  useEffect(() => {
    if (visible) {
      // تحميل إعدادات الصوت
      const settings = audioSystem.getSettings()
      setAudioEnabled(settings.enabled)
      setAudioVolume(settings.volume)
    }
  }, [visible])

  const handleSectionStart = (sectionId: string) => {
    audioSystem.playSound('click')
    message.success(`بدء تعلم: ${tutorialSections.find(s => s.id === sectionId)?.title}`)
    
    // هنا يمكن إضافة منطق بدء التعلم لكل قسم
    switch (sectionId) {
      case 'quick-start':
        showQuickStartGuide()
        break
      case 'inventory':
        showInventoryGuide()
        break
      case 'sales':
        showSalesGuide()
        break
      case 'purchases':
        showPurchasesGuide()
        break
      case 'production':
        showProductionGuide()
        break
      case 'finance':
        showFinanceGuide()
        break
    }
  }

  const showQuickStartGuide = () => {
    Modal.info({
      title: '🚀 دليل البدء السريع',
      width: 600,
      content: (
        <div style={{ direction: 'rtl' }}>
          <h4>مرحباً بك في برنامج المحاسبة والإنتاج!</h4>
          <p><strong>الخطوة 1:</strong> استخدم القائمة الجانبية للتنقل بين الأقسام</p>
          <p><strong>الخطوة 2:</strong> ابدأ بإعداد الأصناف في قسم المخزون</p>
          <p><strong>الخطوة 3:</strong> أضف العملاء والموردين</p>
          <p><strong>الخطوة 4:</strong> ابدأ بإدخال العمليات (مبيعات، مشتريات، إنتاج)</p>
          <p><strong>نصيحة:</strong> استخدم زر المساعدة 🔊 لسماع شرح أي عنصر</p>
        </div>
      )
    })
  }

  const showInventoryGuide = () => {
    Modal.info({
      title: '📦 دليل إدارة المخزون',
      width: 600,
      content: (
        <div style={{ direction: 'rtl' }}>
          <h4>إدارة المخزون خطوة بخطوة:</h4>
          <p><strong>1. الفئات:</strong> أنشئ فئات للأصناف (خشب، دهانات، إكسسوارات)</p>
          <p><strong>2. الأصناف:</strong> أضف الأصناف مع تحديد الفئة والوحدة</p>
          <p><strong>3. المستودعات:</strong> أنشئ المستودعات المختلفة</p>
          <p><strong>4. الجرد:</strong> راقب الكميات والحركات</p>
          <p><strong>نصيحة:</strong> استخدم الباركود لتسريع العمليات</p>
        </div>
      )
    })
  }

  const showSalesGuide = () => {
    Modal.info({
      title: '🛒 دليل إدارة المبيعات',
      width: 600,
      content: (
        <div style={{ direction: 'rtl' }}>
          <h4>إدارة المبيعات خطوة بخطوة:</h4>
          <p><strong>1. العملاء:</strong> أضف بيانات العملاء الكاملة</p>
          <p><strong>2. أوامر البيع:</strong> أنشئ أوامر البيع مع تحديد الأصناف</p>
          <p><strong>3. الفواتير:</strong> حول أوامر البيع إلى فواتير</p>
          <p><strong>4. المتابعة:</strong> تابع حالة الطلبات والمدفوعات</p>
          <p><strong>نصيحة:</strong> استخدم التقارير لمتابعة الأداء</p>
        </div>
      )
    })
  }

  const showPurchasesGuide = () => {
    Modal.info({
      title: '🏪 دليل إدارة المشتريات',
      width: 600,
      content: (
        <div style={{ direction: 'rtl' }}>
          <h4>إدارة المشتريات خطوة بخطوة:</h4>
          <p><strong>1. الموردين:</strong> أضف بيانات الموردين</p>
          <p><strong>2. أوامر الشراء:</strong> أنشئ أوامر الشراء</p>
          <p><strong>3. الاستلام:</strong> سجل استلام البضائع</p>
          <p><strong>4. الفواتير:</strong> أدخل فواتير الموردين</p>
          <p><strong>نصيحة:</strong> راقب مستويات المخزون لتجنب النفاد</p>
        </div>
      )
    })
  }

  const showProductionGuide = () => {
    Modal.info({
      title: '🏭 دليل إدارة الإنتاج',
      width: 700,
      content: (
        <div style={{ direction: 'rtl' }}>
          <h4>الدورة المستندية الإنتاجية:</h4>
          <p><strong>1. أمر الإنتاج:</strong> ابدأ بإنشاء أمر إنتاج جديد</p>
          <p><strong>2. تخصيص المواد:</strong> حدد المواد الخام المطلوبة</p>
          <p><strong>3. بدء الإنتاج:</strong> ابدأ عملية الإنتاج وسجل التقدم</p>
          <p><strong>4. مراقبة الجودة:</strong> فحص المنتجات أثناء الإنتاج</p>
          <p><strong>5. الإنجاز:</strong> أنهي أمر الإنتاج وأضف المنتجات للمخزون</p>
          <p><strong>6. التكاليف:</strong> احسب تكاليف الإنتاج الفعلية</p>
          <p><strong>نصيحة:</strong> استخدم تقارير الإنتاج لتحليل الأداء</p>
        </div>
      )
    })
  }

  const showFinanceGuide = () => {
    Modal.info({
      title: '💰 دليل الإدارة المالية',
      width: 600,
      content: (
        <div style={{ direction: 'rtl' }}>
          <h4>الإدارة المالية خطوة بخطوة:</h4>
          <p><strong>1. الحسابات:</strong> أعد شجرة الحسابات</p>
          <p><strong>2. القيود:</strong> سجل القيود المحاسبية</p>
          <p><strong>3. التقارير:</strong> استخرج التقارير المالية</p>
          <p><strong>4. المراجعة:</strong> راجع الأرصدة والحركات</p>
          <p><strong>نصيحة:</strong> راجع التقارير دورياً لضمان الدقة</p>
        </div>
      )
    })
  }

  const handleAudioToggle = (enabled: boolean) => {
    setAudioEnabled(enabled)
    audioSystem.updateSettings({ enabled })
    if (enabled) {
      audioSystem.playSound('success')
      message.success('تم تفعيل الصوت')
    } else {
      message.info('تم إلغاء الصوت')
    }
  }

  const handleVolumeChange = (volume: number) => {
    setAudioVolume(volume)
    audioSystem.updateSettings({ volume })
  }

  return (
    <Modal
      title={
        <Space>
          <BookOutlined />
          <span>دليل التعلم البسيط</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      style={{ direction: 'rtl' }}
    >
      <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
        {/* إعدادات الصوت */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Space>
              <SoundOutlined />
              <Text strong>إعدادات الصوت:</Text>
              <Switch 
                checked={audioEnabled} 
                onChange={handleAudioToggle}
                checkedChildren="مفعل"
                unCheckedChildren="معطل"
              />
            </Space>
            {audioEnabled && (
              <div>
                <Text>مستوى الصوت: {audioVolume}%</Text>
                <Slider
                  value={audioVolume}
                  onChange={handleVolumeChange}
                  style={{ width: 200, marginRight: 16 }}
                />
              </div>
            )}
          </Space>
        </Card>

        <Divider>الأقسام التعليمية</Divider>

        {/* قائمة الأقسام */}
        <List
          dataSource={tutorialSections}
          renderItem={section => (
            <List.Item>
              <Card 
                hoverable
                style={{ width: '100%' }}
                actions={[
                  <Button
                    key="start"
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={() => handleSectionStart(section.id)}
                    style={{ backgroundColor: section.color, borderColor: section.color }}
                  >
                    ابدأ التعلم
                  </Button>
                ]}
              >
                <Card.Meta
                  avatar={<div style={{ fontSize: 24, color: section.color }}>{section.icon}</div>}
                  title={
                    <Space>
                      <span>{section.title}</span>
                      <Tag color={section.color}>{section.duration}</Tag>
                    </Space>
                  }
                  description={section.description}
                />
              </Card>
            </List.Item>
          )}
        />
      </div>
    </Modal>
  )
}

export default SimpleTutorial
