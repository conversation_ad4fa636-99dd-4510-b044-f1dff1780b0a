import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Space,
  Tag,
  Tooltip,
  Card,
  Row,
  Col,
  Statistic,
  Popover,
  Popconfirm,
  message,
  Image,
  Badge as _Badge,
  Typography,
  Dropdown as _Dropdown,
  Menu as _Menu,
  Progress as _Progress,
  Divider,
  Switch as _Switch,
  Tabs as _Tabs,
  Alert} from 'antd'

import { SafeLogger as Logger } from '../../../utils/logger'
import { DateUtils } from '../../../utils/dateConfig'


import {
  PlusOutlined,
  EditOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PrinterOutlined,
  QuestionCircleOutlined,
  EyeOutlined,
  DeleteOutlined,
  StopOutlined,
  PauseCircleOutlined,
  BarChartOutlined,
  FilterOutlined,
  CalendarOutlined,
  FileExcelOutlined,
  BellOutlined,
  CameraOutlined,
  PictureOutlined,
  NotificationOutlined,
  InfoCircleOutlined,
  NumberOutlined,
  ReloadOutlined,
  FilePdfOutlined,
  BarcodeOutlined
} from '@ant-design/icons'
import * as XLSX from 'xlsx'
import UnifiedPrintButton from '../../common/UnifiedPrintButton'
import { QuickInputService, QuickInputHelper, RecentData } from '../../common/QuickInputService'
import ProductionOrderDetailsModal from './ProductionOrderDetailsModal'
import ProductionAdvancedReports from './ProductionAdvancedReports'

import ProductionAdvancedFilters, { ProductionFilters } from './ProductionAdvancedFilters'
import ProductionGanttChart from './ProductionGanttChart'
import { furnitureNotificationService } from './FurnitureNotificationService'
import { furnitureCalendarService } from './FurnitureCalendarService'
import { SimpleImageManager } from '../../common/SimpleImageManager'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

interface ProductionOrder {
  id: number
  order_number: string
  order_code: string
  recipe_id: number  // الوصفة هي الأساس
  recipe_name: string
  recipe_code: string
  product_id: number  // المنتج النهائي من الوصفة
  product_name: string
  product_code: string
  item_name?: string  // اسم الصنف من قاعدة البيانات
  item_code?: string  // كود الصنف من قاعدة البيانات
  department_id: number
  department_name: string
  customer_id?: number
  customer_name?: string
  order_date: string
  start_date?: string
  expected_completion_date?: string
  actual_completion_date?: string
  quantity: number
  unit: string
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold'
  priority: 'low' | 'normal' | 'high' | 'urgent'
  estimated_cost: number
  actual_cost: number
  estimated_hours: number
  actual_hours: number
  notes?: string
  created_by_name?: string
  created_at: string
}

interface ProductionOrdersManagementProps {
  onBack?: () => void
}

// بيانات الشروحات التفاعلية
const helpSteps = [
  {
    title: "مرحباً بك في إدارة أوامر الإنتاج! 👋",
    content: "هذا القسم يساعدك على إنشاء وإدارة أوامر الإنتاج بسهولة. دعنا نتعرف على الميزات الأساسية.",
    icon: "🎯",
    tips: ["يمكنك إنشاء أوامر إنتاج جديدة", "تتبع حالة الأوامر الحالية", "طباعة التقارير والفواتير"]
  },
  {
    title: "إنشاء أمر إنتاج جديد 📝",
    content: "لإنشاء أمر إنتاج جديد، اضغط على زر 'أمر إنتاج جديد' واختر الوصفة المطلوبة. سيتم حساب التكلفة والوقت تلقائياً.",
    icon: "➕",
    tips: [
      "🔍 ابدأ باختيار الوصفة المطلوبة - ستحدد المنتج والمواد تلقائياً",
      "📊 حدد الكمية المطلوبة - ستؤثر على حساب المواد والتكلفة الإجمالية",
      "🏭 اختر القسم المسؤول عن الإنتاج حسب نوع المنتج",
      "📅 حدد تاريخ البدء والانتهاء المتوقع لتنظيم جدولة الإنتاج",
      "📷 يمكنك رفع الصور التوضيحية بعد إنشاء الأمر مباشرة"
    ]
  },
  {
    title: "الأزرار المساعدة ⚡",
    content: "يمكنك إضافة أصناف أو مخازن أو أقسام جديدة مباشرة من نافذة إنشاء الأمر.",
    icon: "🔧",
    tips: [
      "اضغط على زر '+' بجانب قائمة الأصناف لإضافة صنف جديد",
      "اضغط على زر '+' بجانب قائمة المخازن لإضافة مخزن جديد",
      "اضغط على زر '+' بجانب قائمة الأقسام لإضافة قسم جديد",
      "استخدم زر 'تحديث شامل' لتحديث جميع القوائم"
    ]
  },
  {
    title: "حالات أوامر الإنتاج 📊",
    content: "كل أمر إنتاج يمر بمراحل مختلفة من البداية حتى الإنتهاء.",
    icon: "🔄",
    tips: [
      "معلق (Pending): أمر جديد لم يبدأ بعد",
      "قيد التنفيذ (In Progress): بدأ الإنتاج واستهلكت المواد",
      "مكتمل (Completed): انتهى الإنتاج وأضيف المنتج للمخزن",
      "ملغي (Cancelled): تم إلغاء الأمر"
    ]
  },
  {
    title: "التقارير والطباعة 📄",
    content: "يمكنك طباعة التقارير وتصدير البيانات بصيغ مختلفة.",
    icon: "🖨️",
    tips: [
      "اضغط على 'طباعة التقرير' لطباعة تقرير شامل",
      "استخدم 'تصدير Excel' لحفظ البيانات",
      "يمكنك طباعة تفاصيل أمر واحد من قائمة الإجراءات"
    ]
  },
  {
    title: "نصائح مهمة للمبتدئين 💡",
    content: "بعض النصائح المهمة لاستخدام النظام بكفاءة.",
    icon: "⭐",
    tips: [
      "تأكد من وجود وصفة إنتاج للصنف قبل إنشاء الأمر",
      "تحقق من توفر المواد الخام قبل بدء الإنتاج",
      "استخدم الفلاتر للبحث عن أوامر محددة",
      "راجع التقارير بانتظام لمتابعة الأداء"
    ]
  }
]

const ProductionOrdersManagement: React.FC<ProductionOrdersManagementProps> = ({ onBack }) => {
  const [orders, setOrders] = useState<ProductionOrder[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingOrder, setEditingOrder] = useState<ProductionOrder | null>(null)
  const [form] = Form.useForm()
  const [recentInputs, setRecentInputs] = useState<RecentData[]>([])
  const [showQuickInput, setShowQuickInput] = useState(false)
  const [items, setItems] = useState<any[]>([])
  const [availableItems, setAvailableItems] = useState<any[]>([])
  const [departments, setDepartments] = useState<any[]>([])
  const [customers, setCustomers] = useState<any[]>([])
  const [warehouses, setWarehouses] = useState<any[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [recipes, setRecipes] = useState<any[]>([])

  // حالات النوافذ المساعدة
  const [itemModalVisible, setItemModalVisible] = useState(false)
  const [newProductModalVisible, setNewProductModalVisible] = useState(false)
  const [newProductForm] = Form.useForm()
  // تم إزالة warehouseModalVisible لأن المخازن ستؤخذ من الوصفة
  const [departmentModalVisible, setDepartmentModalVisible] = useState(false)

  // حالة الشروحات التفاعلية
  const [helpVisible, setHelpVisible] = useState(false)
  const [currentHelpStep, setCurrentHelpStep] = useState(0)

  // حالة modal عرض التفاصيل
  const [detailsModalVisible, setDetailsModalVisible] = useState(false)
  const [selectedOrderId, setSelectedOrderId] = useState<number | null>(null)

  // حالة التقارير المتقدمة
  const [showAdvancedReports, setShowAdvancedReports] = useState(false)

  // حالة الفلاتر المتقدمة
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [_activeFilters, setActiveFilters] = useState<ProductionFilters>({})

  // حالة التنبيهات والتقويم والصور
  const [notificationsModalVisible, setNotificationsModalVisible] = useState(false)
  const [calendarModalVisible, setCalendarModalVisible] = useState(false)
  const [customNotificationModalVisible, setCustomNotificationModalVisible] = useState(false)
  const [selectedOrderForNotification, setSelectedOrderForNotification] = useState<ProductionOrder | null>(null)
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false)
  const [previewImageUrl, setPreviewImageUrl] = useState('')
  const [uploadedImages, setUploadedImages] = useState<string[]>([])
  const [imageUploadModalVisible, setImageUploadModalVisible] = useState(false)
  const [_filteredOrders, setFilteredOrders] = useState<ProductionOrder[]>([])
  const [allOrders, setAllOrders] = useState<ProductionOrder[]>([])
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([])

  // حالة عرض الجانت
  const [showGanttChart, setShowGanttChart] = useState(false)



  // إحصائيات سريعة
  const [_stats, _setStats] = useState({
    total: 0,
    pending: 0,
    in_progress: 0,
    completed: 0,
    total_estimated_cost: 0
  })

  // دوال التنبيهات والتقويم والصور
  const handleShowNotifications = () => {
    setNotificationsModalVisible(true)
  }

  const handleShowCalendar = () => {
    setCalendarModalVisible(true)
  }

  // دالة تحضير بيانات طباعة أمر الإنتاج المفصل
  const prepareDetailedOrderPrintData = async (order: ProductionOrder) => {
    try {
      // تحميل مواد الوصفة إذا كانت متوفرة
      let materials: any[] = []

      if (order.recipe_id) {
        const materialsResult = await window.electronAPI?.getRecipeMaterials(order.recipe_id)
        materials = materialsResult?.success ? materialsResult.data : []
      }

      // جلب الصور المرفقة مع أمر الإنتاج
      const orderImages = await getOrderImages(order.id.toString())
      Logger.info('ProductionOrdersManagement', `🖼️ تم العثور على ${orderImages.length} صورة مرفقة مع أمر الإنتاج ${order.order_number}`)

      // تحويل الصور إلى تنسيق الطباعة
      const printImages = orderImages.map(image => {
        Logger.info('ProductionOrdersManagement', `🖼️ معالجة صورة مفصلة: ${image.originalName}, URL: ${image.url}`)
        return {
          id: image.id,
          name: image.originalName || image.filename || 'صورة غير مسماة',
          path: image.url || image.thumbnailUrl, // استخدام URL أو thumbnailUrl
          description: image.description || `صورة أمر الإنتاج ${order.order_number}`,
          category: 'صورة أمر الإنتاج',
          size: image.size,
          uploadDate: image.uploadedAt.toLocaleDateString('ar-SA'),
          notes: image.tags?.join(', ') || '',
          metadata: {
            orderNumber: order.order_number,
            orderDate: order.order_date,
            imageType: image.category,
            uploadedBy: image.uploadedBy || 'غير محدد'
          }
        }
      })

      // تحضير بيانات أمر الإنتاج
      const orderInfo = {
        id: 1,
        order_number: order.order_number,
        product_name: order.item_name || order.product_name || '',
        quantity: order.quantity || 0,
        unit: order.unit || 'قطعة',
        department_name: order.department_name,
        customer_name: order.customer_name || 'إنتاج داخلي',
        status: getStatusText(order.status),
        priority: getPriorityText(order.priority),
        estimated_cost: order.estimated_cost || 0,
        order_date: order.order_date ? DateUtils.formatForDisplay(order.order_date, 'DD/MM/YYYY') : '',
        start_date: order.start_date ? DateUtils.formatForDisplay(order.start_date, 'DD/MM/YYYY') : '',
        expected_completion_date: order.expected_completion_date ? DateUtils.formatForDisplay(order.expected_completion_date, 'DD/MM/YYYY') : '',
        notes: order.notes || 'لا توجد ملاحظات'
      }

      return {
        title: `أمر إنتاج مفصل رقم: ${order.order_number}`,
        subtitle: `المنتج: ${order.item_name || order.product_name || ''} | الكمية: ${order.quantity} ${order.unit} | القسم: ${order.department_name}`,

        // إضافة بيانات العميل مع معلومات إضافية
        customer: {
          name: order.customer_name || 'إنتاج داخلي',
          department: order.department_name,
          orderDate: order.order_date ? DateUtils.formatForDisplay(order.order_date, 'DD/MM/YYYY') : '',
          deliveryDate: order.expected_completion_date ? DateUtils.formatForDisplay(order.expected_completion_date, 'DD/MM/YYYY') : '',
          type: 'عميل'
        },

        data: [orderInfo],

        columns: [
          { key: 'order_number', title: 'رقم الأمر', align: 'center' as const, width: '15%' },
          { key: 'product_name', title: 'المنتج', align: 'right' as const, width: '25%' },
          { key: 'quantity', title: 'الكمية', align: 'center' as const, format: 'number' as const, width: '10%' },
          { key: 'unit', title: 'الوحدة', align: 'center' as const, width: '10%' },
          { key: 'status', title: 'الحالة', align: 'center' as const, width: '12%' },
          { key: 'priority', title: 'الأولوية', align: 'center' as const, width: '10%' },
          { key: 'estimated_cost', title: 'التكلفة المقدرة', align: 'center' as const, format: 'currency' as const, width: '18%' }
        ],

        // معلومات المواد إذا كانت متوفرة
        materialsData: materials.length > 0 ? materials.map((material: any, index: number) => ({
          id: index + 1,
          material_code: material.material_code || '',
          material_name: material.material_name || '',
          warehouse_name: material.warehouse_name || '',
          quantity: material.quantity || 0,
          unit: material.unit || 'قطعة',
          cost_per_unit: material.cost_per_unit || 0,
          total_cost: material.total_cost || 0,
          is_optional: material.is_optional ? 'اختياري' : 'إجباري'
        })) : [],

        summary: {
          compact: true,
          showTotals: false,
          data: [
            { label: 'رقم الأمر', value: order.order_number },
            { label: 'المنتج', value: order.item_name || order.product_name || '' },
            { label: 'الكمية', value: `${order.quantity} ${order.unit}` },
            { label: 'الحالة', value: getStatusText(order.status) },
            { label: 'التكلفة المقدرة', value: order.estimated_cost || 0, format: 'currency' },
            { label: 'تاريخ الأمر', value: order.order_date ? DateUtils.formatForDisplay(order.order_date, 'DD/MM/YYYY') : '' }
          ]
        },

        // إضافة الصور المرفقة إلى بيانات الطباعة
        images: printImages,
        imageSettings: {
          layout: 'grid' as const,
          imagesPerPage: 4,
          showMetadata: true,
          imageQuality: 'high' as const,
          fitToPage: true
        },

        additionalInfo: {
          instructions: [
            'تأكد من توفر جميع المواد المطلوبة قبل بدء الإنتاج',
            'راجع المواصفات الفنية بعناية',
            'اتبع إجراءات السلامة والجودة المعتمدة',
            'سجل أي ملاحظات أو تغييرات أثناء الإنتاج'
          ],
          notes: `أمر إنتاج ${order.order_number} - تم إنشاؤه في ${DateUtils.formatForDisplay(new Date(), 'DD/MM/YYYY')}`,
          warnings: [
            order.priority === 'urgent' ? 'أمر عاجل - يتطلب معالجة فورية' : null,
            materials.length === 0 ? 'تحذير: لا توجد مواد محددة لهذا الأمر' : null,
            printImages.length === 0 ? 'ملاحظة: لا توجد صور مرفقة مع هذا الأمر' : `تم إرفاق ${printImages.length} صورة مع هذا الأمر`
          ].filter(Boolean)
        },

        metadata: {
          generatedAt: DateUtils.formatForDisplay(new Date(), 'DD/MM/YYYY HH:mm'),
          generatedBy: 'نظام إدارة الإنتاج - ZET.IA',
          orderNumber: order.order_number,
          itemName: order.item_name || order.product_name || '',
          quantity: order.quantity,
          department: order.department_name,
          contactName: order.customer_name,
          contactType: 'customer',
          documentType: 'أمر إنتاج مفصل',
          documentVersion: '2.0',
          totalMaterials: materials.length,
          totalImages: printImages.length,
          printSettings: {
            orientation: 'portrait',
            pageSize: 'A4',
            showLogo: true,
            showHeader: true,
            showFooter: true,
            primaryColor: '#1890ff',
            secondaryColor: '#f0f2f5'
          }
        }
      }
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في تحضير بيانات الطباعة المفصلة:', error)
      throw new Error(`فشل في تحضير بيانات الطباعة المفصلة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    }
  }

  // دالة تحضير تقرير شامل لجميع أوامر الإنتاج
  const prepareComprehensiveOrdersReport = async () => {
    try {
      if (orders.length === 0) {
        throw new Error('لا توجد أوامر إنتاج لإنشاء التقرير')
      }

      // حساب الإحصائيات
      const totalOrders = orders.length
      const pendingOrders = orders.filter(o => o.status === 'pending').length
      const inProgressOrders = orders.filter(o => o.status === 'in_progress').length
      const completedOrders = orders.filter(o => o.status === 'completed').length
      const cancelledOrders = orders.filter(o => o.status === 'cancelled').length
      const onHoldOrders = orders.filter(o => o.status === 'on_hold').length

      // إحصائيات الأولوية
      const urgentOrders = orders.filter(o => o.priority === 'urgent').length
      const highOrders = orders.filter(o => o.priority === 'high').length
      const normalOrders = orders.filter(o => o.priority === 'normal').length
      const lowOrders = orders.filter(o => o.priority === 'low').length

      // إحصائيات التكلفة
      const totalEstimatedCost = orders.reduce((sum, o) => sum + (o.estimated_cost || 0), 0)
      const totalActualCost = orders.reduce((sum, o) => sum + (o.actual_cost || 0), 0)
      const averageOrderCost = totalEstimatedCost / totalOrders

      // إحصائيات الوقت
      const totalEstimatedHours = orders.reduce((sum, o) => sum + (o.estimated_hours || 0), 0)
      const totalActualHours = orders.reduce((sum, o) => sum + (o.actual_hours || 0), 0)
      const averageOrderHours = totalEstimatedHours / totalOrders

      // تحضير بيانات التقرير
      const reportData = orders.map((order, index) => ({
        id: index + 1,
        order_number: order.order_number || 'غير محدد',
        order_code: order.order_code || 'غير محدد',
        product_name: order.item_name || order.product_name || '',
        recipe_name: order.recipe_name || 'غير محدد',
        quantity: order.quantity || 0,
        unit: order.unit || 'قطعة',
        department_name: order.department_name || 'غير محدد',
        customer_name: order.customer_name || 'غير محدد',
        status: getStatusText(order.status),
        priority: getPriorityText(order.priority),
        estimated_cost: order.estimated_cost || 0,
        actual_cost: order.actual_cost || 0,
        estimated_hours: order.estimated_hours || 0,
        actual_hours: order.actual_hours || 0,
        order_date: order.order_date || 'غير محدد',
        expected_completion_date: order.expected_completion_date || 'غير محدد',
        efficiency: (order.estimated_hours && order.actual_hours) ?
          ((order.estimated_hours / order.actual_hours) * 100).toFixed(1) + '%' : 'غير محسوب'
      }))

      return {
        title: 'تقرير شامل لأوامر الإنتاج',
        subtitle: `إجمالي الأوامر: ${totalOrders} | المكتملة: ${completedOrders} | قيد التنفيذ: ${inProgressOrders} | في الانتظار: ${pendingOrders}`,

        data: reportData,

        columns: [
          { key: 'id', title: '#', align: 'center' as const, width: '4%' },
          { key: 'order_number', title: 'رقم الأمر', align: 'center' as const, width: '8%' },
          { key: 'product_name', title: 'المنتج', align: 'right' as const, width: '15%' },
          { key: 'quantity', title: 'الكمية', align: 'center' as const, format: 'number' as const, width: '6%' },
          { key: 'department_name', title: 'القسم', align: 'center' as const, width: '10%' },
          { key: 'customer_name', title: 'العميل', align: 'center' as const, width: '12%' },
          { key: 'status', title: 'الحالة', align: 'center' as const, width: '8%' },
          { key: 'priority', title: 'الأولوية', align: 'center' as const, width: '8%' },
          { key: 'estimated_cost', title: 'التكلفة المقدرة', align: 'center' as const, format: 'currency' as const, width: '10%' },
          { key: 'estimated_hours', title: 'الساعات المقدرة', align: 'center' as const, format: 'number' as const, width: '8%' },
          { key: 'order_date', title: 'تاريخ الأمر', align: 'center' as const, width: '8%' },
          { key: 'efficiency', title: 'الكفاءة', align: 'center' as const, width: '7%' }
        ],

        summary: {
          compact: false,
          showTotals: true,
          data: [
            { label: 'إجمالي أوامر الإنتاج', value: totalOrders, format: 'number', icon: '📋', color: '#1890ff' },
            { label: 'أوامر مكتملة', value: completedOrders, format: 'number', icon: '✅', color: '#52c41a' },
            { label: 'أوامر قيد التنفيذ', value: inProgressOrders, format: 'number', icon: '🔄', color: '#faad14' },
            { label: 'أوامر في الانتظار', value: pendingOrders, format: 'number', icon: '⏳', color: '#13c2c2' },
            { label: 'أوامر ملغاة', value: cancelledOrders, format: 'number', icon: '❌', color: '#f5222d' },
            { label: 'أوامر معلقة', value: onHoldOrders, format: 'number', icon: '⏸️', color: '#fa8c16' },
            { label: 'أوامر عاجلة', value: urgentOrders, format: 'number', icon: '🚨', color: '#ff4d4f' },
            { label: 'أوامر عالية الأولوية', value: highOrders, format: 'number', icon: '🔺', color: '#ff7a45' },
            { label: 'إجمالي التكلفة المقدرة', value: totalEstimatedCost, format: 'currency', icon: '💰', color: '#722ed1' },
            { label: 'إجمالي التكلفة الفعلية', value: totalActualCost, format: 'currency', icon: '💸', color: '#eb2f96' },
            { label: 'متوسط تكلفة الأمر', value: averageOrderCost, format: 'currency', icon: '📊', color: '#2f54eb' },
            { label: 'إجمالي الساعات المقدرة', value: totalEstimatedHours, format: 'number', unit: 'ساعة', icon: '⏱️', color: '#389e0d' },
            { label: 'متوسط ساعات الأمر', value: averageOrderHours, format: 'number', unit: 'ساعة', icon: '📈', color: '#d46b08' }
          ]
        },

        additionalInfo: {
          recommendations: [
            pendingOrders > totalOrders * 0.4 ? 'نسبة عالية من الأوامر في الانتظار - يُنصح بمراجعة سير العمل' : null,
            urgentOrders > totalOrders * 0.2 ? 'نسبة عالية من الأوامر العاجلة - قد تحتاج لإعادة تنظيم الأولويات' : null,
            totalActualCost > totalEstimatedCost * 1.2 ? 'التكلفة الفعلية تتجاوز المقدرة بشكل كبير - مراجعة التقديرات مطلوبة' : null,
            completedOrders < totalOrders * 0.3 ? 'نسبة إنجاز منخفضة - قد تحتاج لتحسين العمليات' : null
          ].filter(Boolean),
          insights: [
            `معدل الإنجاز: ${((completedOrders / totalOrders) * 100).toFixed(1)}%`,
            `كفاءة التكلفة: ${totalEstimatedCost > 0 ? ((totalActualCost / totalEstimatedCost) * 100).toFixed(1) : '0'}%`,
            `متوسط وقت الأمر: ${averageOrderHours.toFixed(1)} ساعة`,
            `الأولوية الأكثر شيوعاً: ${normalOrders > highOrders && normalOrders > urgentOrders && normalOrders > lowOrders ? 'عادية' : 'متنوعة'}`
          ],
          notes: `تقرير شامل لأوامر الإنتاج - تم إنشاؤه في ${new Date().toLocaleDateString('ar-SA')}`
        },

        metadata: {
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام إدارة الإنتاج - ZET.IA',
          documentType: 'تقرير شامل لأوامر الإنتاج',
          documentVersion: '2.0',
          totalOrders: totalOrders,
          reportPeriod: 'جميع الفترات',
          printSettings: {
            orientation: 'landscape',
            pageSize: 'A4',
            showLogo: true,
            showHeader: true,
            showFooter: true,
            primaryColor: '#1890ff',
            secondaryColor: '#f0f2f5'
          }
        }
      }
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في تحضير التقرير الشامل:', error)
      throw new Error(`فشل في تحضير التقرير الشامل: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    }
  }

  // دالة تحضير طباعة أمر الإنتاج مع QR Code
  const prepareOrderWithQRCode = async (order: ProductionOrder) => {
    try {
      // جلب الصور المرفقة مع أمر الإنتاج
      const orderImages = await getOrderImages(order.id.toString())
      Logger.info('ProductionOrdersManagement', `🖼️ تم العثور على ${orderImages.length} صورة مرفقة مع أمر الإنتاج ${order.order_number} للطباعة مع QR`)

      // تحويل الصور إلى تنسيق الطباعة
      const printImages = orderImages.map(image => {
        Logger.info('ProductionOrdersManagement', `🖼️ معالجة صورة QR: ${image.originalName}, URL: ${image.url}`)
        return {
          id: image.id,
          name: image.originalName || image.filename || 'صورة غير مسماة',
          path: image.url || image.thumbnailUrl, // استخدام URL أو thumbnailUrl
          description: image.description || `صورة أمر الإنتاج ${order.order_number}`,
          category: 'صورة أمر الإنتاج',
          size: image.size,
          uploadDate: DateUtils.formatForDisplay(image.uploadedAt, 'DD/MM/YYYY'),
          notes: image.tags?.join(', ') || '',
          metadata: {
            orderNumber: order.order_number,
            orderDate: order.order_date,
            imageType: image.category,
            uploadedBy: image.uploadedBy || 'غير محدد'
          }
        }
      })

      // إنشاء بيانات QR Code
      const qrData = JSON.stringify({
        type: 'production_order',
        order_id: order.id,
        order_number: order.order_number,
        product_name: order.item_name || order.product_name || '',
        quantity: order.quantity,
        department: order.department_name,
        status: order.status,
        priority: order.priority,
        created_at: order.created_at,
        version: '2.0',
        timestamp: new Date().toISOString()
      })

      // تحضير بيانات أمر الإنتاج
      const orderInfo = {
        id: 1,
        order_number: order.order_number,
        product_name: order.item_name || order.product_name || '',
        quantity: order.quantity || 0,
        unit: order.unit || 'قطعة',
        department_name: order.department_name,
        customer_name: order.customer_name || 'إنتاج داخلي',
        status: getStatusText(order.status),
        priority: getPriorityText(order.priority),
        estimated_cost: order.estimated_cost || 0,
        order_date: order.order_date ? DateUtils.formatForDisplay(order.order_date, 'DD/MM/YYYY') : '',
        expected_completion_date: order.expected_completion_date ? DateUtils.formatForDisplay(order.expected_completion_date, 'DD/MM/YYYY') : '',
        qr_data: qrData
      }

      return {
        title: `أمر إنتاج مع باركود QR: ${order.order_number}`,
        subtitle: `المنتج: ${order.item_name || order.product_name || ''} | الكمية: ${order.quantity} ${order.unit} | QR للوصول السريع`,

        // إضافة بيانات العميل مع معلومات إضافية
        customer: {
          name: order.customer_name || 'إنتاج داخلي',
          department: order.department_name,
          orderDate: order.order_date ? DateUtils.formatForDisplay(order.order_date, 'DD/MM/YYYY') : '',
          deliveryDate: order.expected_completion_date ? DateUtils.formatForDisplay(order.expected_completion_date, 'DD/MM/YYYY') : '',
          type: 'عميل'
        },

        data: [orderInfo],

        columns: [
          { key: 'order_number', title: 'رقم الأمر', align: 'center' as const, width: '12%' },
          { key: 'product_name', title: 'المنتج', align: 'right' as const, width: '20%' },
          { key: 'quantity', title: 'الكمية', align: 'center' as const, format: 'number' as const, width: '10%' },
          { key: 'unit', title: 'الوحدة', align: 'center' as const, width: '8%' },
          { key: 'department_name', title: 'القسم', align: 'center' as const, width: '15%' },
          { key: 'status', title: 'الحالة', align: 'center' as const, width: '10%' },
          { key: 'priority', title: 'الأولوية', align: 'center' as const, width: '10%' },
          { key: 'estimated_cost', title: 'التكلفة المقدرة', align: 'center' as const, format: 'currency' as const, width: '15%' }
        ],

        // إعدادات QR Code
        qrSettings: {
          showQRCode: true,
          qrPosition: 'top-right',
          qrSize: 'large',
          includeInstructions: true
        },

        summary: {
          compact: true,
          data: [
            { label: 'رقم أمر الإنتاج', value: order.order_number, icon: '📋' },
            { label: 'المنتج المطلوب', value: order.product_name, icon: '🏭' },
            { label: 'الكمية', value: order.quantity, format: 'number', unit: order.unit, icon: '📦' },
            { label: 'حالة الأمر', value: getStatusText(order.status), icon: '📊' },
            { label: 'الأولوية', value: getPriorityText(order.priority), icon: '⚡' },
            { label: 'القسم المسؤول', value: order.department_name || 'غير محدد', icon: '🏢' },
            { label: 'تاريخ الأمر', value: order.order_date || 'غير محدد', icon: '📅' }
          ]
        },

        // إضافة الصور المرفقة إلى بيانات الطباعة
        images: printImages,
        imageSettings: {
          layout: 'grid' as const,
          imagesPerPage: 2,
          showMetadata: true,
          imageQuality: 'high' as const,
          fitToPage: true
        },

        additionalInfo: {
          instructions: [
            'استخدم قارئ الباركود لمسح QR Code للوصول السريع لتفاصيل الأمر',
            'احتفظ بهذا المستند في منطقة الإنتاج للمرجعية',
            'تأكد من تحديث حالة الأمر عند التقدم في الإنتاج',
            'راجع المواصفات والوصفة قبل بدء العمل'
          ],
          qrInstructions: [
            'QR Code يحتوي على معلومات الأمر الأساسية',
            'يمكن مسحه بأي تطبيق قارئ باركود',
            'يوفر وصولاً سريعاً لتفاصيل الأمر في النظام',
            'مفيد للتتبع والمراجعة السريعة'
          ],
          notes: `أمر إنتاج ${order.order_number} مع QR Code - تم إنشاؤه في ${new Date().toLocaleDateString('ar-SA')}`,
          imageNotes: printImages.length > 0 ? `تم إرفاق ${printImages.length} صورة مع هذا الأمر` : 'لا توجد صور مرفقة مع هذا الأمر'
        },

        metadata: {
          generatedAt: new Date().toISOString(),
          generatedBy: 'نظام إدارة الإنتاج - ZET.IA',
          orderNumber: order.order_number,
          orderCode: order.order_code,
          itemName: order.item_name || order.product_name || '',
          quantity: order.quantity,
          department: order.department_name,
          contactName: order.customer_name,
          contactType: 'customer',
          documentType: 'أمر إنتاج مع QR Code',
          documentVersion: '2.0',
          qrCodeData: qrData,
          printSettings: {
            orientation: 'portrait',
            pageSize: 'A4',
            showLogo: true,
            showHeader: true,
            showFooter: true,
            showQRCode: true,
            primaryColor: '#52c41a',
            secondaryColor: '#f6ffed'
          }
        }
      }
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في تحضير بيانات الطباعة مع QR:', error)
      throw new Error(`فشل في تحضير بيانات الطباعة مع QR: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    }
  }

  const handleAddCustomNotification = (order: ProductionOrder) => {
    setSelectedOrderForNotification(order)
    setCustomNotificationModalVisible(true)
  }

  const handleImagePreview = (imageUrl: string) => {
    setPreviewImageUrl(imageUrl)
    setImagePreviewVisible(true)
  }

  // جلب صور أمر الإنتاج باستخدام النظام الجديد
  const getOrderImages = async (orderId: string) => {
    try {
      const { simpleImageService } = await import('../../../services/SimpleImageService')
      const result = await simpleImageService.getImages('production', parseInt(orderId))

      if (result.success && result.data) {
        // تحويل إلى التنسيق القديم للتوافق
        return result.data.map((img: any) => ({
          id: img.id,
          originalName: img.name,
          thumbnailUrl: img.path,
          url: img.path
        }))
      }
      return []
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في جلب الصور:', error)
      return []
    }
  }

  // ملاحظة: تم إزالة useEffect لتحميل الصور - SimpleImageManager يتولى ذلك تلقائياً

  useEffect(() => {
    loadOrders()
    loadItems()
    loadDepartments()
    loadCustomers()
    loadCategories()
    loadWarehouses()
    loadRecipes()
    loadRecentInputs()
  }, [])

  const loadRecentInputs = () => {
    const recent = QuickInputService.getRecentData('production_order')
    setRecentInputs(recent)
  }

  const loadOrders = async () => {
    setLoading(true)
    try {
      Logger.info('ProductionOrdersManagement', '🔄 جاري تحميل أوامر الإنتاج...')

      if (!window.electronAPI) {
        Logger.error('ProductionOrdersManagement', '❌ window.electronAPI غير متوفر - لا يمكن تحميل أوامر الإنتاج')
        message.error('فشل في الاتصال بقاعدة البيانات')
        setOrders([])
        setAllOrders([])
        setFilteredOrders([])
        return
      } else {
        const result = await window.electronAPI.getProductionOrders()
        if (result.success) {
          setOrders(result.data)
          setAllOrders(result.data)
          setFilteredOrders(result.data)
          calculateStats(result.data)
          Logger.info('ProductionOrdersManagement', '✅ تم تحميل أوامر الإنتاج من قاعدة البيانات')
        } else {
          message.error('فشل في تحميل أوامر الإنتاج')
        }
      }
    } catch (error) {
      message.error('خطأ في تحميل أوامر الإنتاج')
    }
    setLoading(false)
  }

  const loadItems = async () => {
    try {
      Logger.info('ProductionOrdersManagement', '🔄 جاري تحميل الأصناف...')

      if (!window.electronAPI) {
        Logger.error('ProductionOrdersManagement', '❌ window.electronAPI غير متوفر - لا يمكن تحميل الأصناف')
        message.error('فشل في الاتصال بقاعدة البيانات')
        setItems([])
        setAvailableItems([])
        return
      } else {
        const result: any = await window.electronAPI.getItems()
        if (result && result.success) {
          setItems(result.data)

          // تحميل الوصفات لتحديد الأصناف المتاحة للإنتاج
          const recipesResult = await window.electronAPI.getProductionRecipes()
          if (recipesResult.success) {
            // إضافة معلومات الوصفات للأصناف
            const itemsWithRecipes = result.data.map((item: any) => {
              const itemRecipes = recipesResult.data.filter((recipe: any) =>
                recipe.product_id === item.id || recipe.item_id === item.id
              )
              return {
                ...item,
                hasRecipe: itemRecipes.length > 0,
                recipes: itemRecipes // إضافة قائمة الوصفات المرتبطة بالصنف
              }
            })

            // عرض جميع الأصناف مع تمييز التي لها وصفات
            setAvailableItems(itemsWithRecipes)

            // تسجيل إحصائيات للمساعدة في التشخيص
            const itemsWithRecipesCount = itemsWithRecipes.filter((item: any) => item.hasRecipe).length
            const totalItemsCount = itemsWithRecipes.length
            Logger.info('ProductionOrdersManagement', `📊 إحصائيات الأصناف: ${totalItemsCount} إجمالي، ${itemsWithRecipesCount} لها وصفات`)

            if (itemsWithRecipesCount === 0) {
              Logger.warn('ProductionOrdersManagement', '⚠️ لا توجد أصناف لها وصفات إنتاج - يجب إنشاء وصفات أولاً')
            }
          } else {
            // في حالة فشل تحميل الوصفات، اعرض جميع الأصناف
            const itemsWithoutRecipeInfo = result.data.map((item: any) => ({
              ...item,
              hasRecipe: false,
              recipes: []
            }))
            setAvailableItems(itemsWithoutRecipeInfo)
            Logger.warn('ProductionOrdersManagement', '⚠️ فشل في تحميل الوصفات - عرض جميع الأصناف بدون معلومات الوصفات')
          }

          Logger.info('ProductionOrdersManagement', '✅ تم تحميل الأصناف من قاعدة البيانات')
        }
      }
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في تحميل الأصناف:', error)
    }
  }

  // دالة إنشاء منتج جديد
  const handleCreateNewProduct = async (values: any) => {
    try {
      const productData = {
        ...values,
        type: 'finished_product', // تحديد نوع الصنف كمنتج نهائي
        category_id: parseInt(String(values.category_id)), // تحويل معرف الفئة إلى رقم صحيح
        warehouse_id: parseInt(String(values.warehouse_id)), // تحويل معرف المخزن إلى رقم صحيح
        cost_price: 0, // سيتم حسابها تلقائياً من الوصفات
        sale_price: 0  // سيتم حسابها تلقائياً بناءً على التكلفة + هامش الربح
      }

      if (window.electronAPI) {
        const response: any = await window.electronAPI.createItem(productData)
        if (response.success) {
          message.success('تم إنشاء المنتج بنجاح')
          setNewProductModalVisible(false)
          newProductForm.resetFields()

          // إعادة تحميل قائمة المنتجات
          await loadItems()

          // تحديد المنتج الجديد في النموذج
          form.setFieldsValue({ item_id: response.data.id })

          return response.data
        } else {
          message.error(response.message || 'فشل في إنشاء المنتج')
        }
      }
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في إنشاء المنتج:', error)
      message.error('فشل في إنشاء المنتج')
    }
    return null
  }

  const loadCategories = async () => {
    try {
      if (window.electronAPI) {
        const result: any = await window.electronAPI.getCategories()
        if (Array.isArray(result)) {
          setCategories(result)
        } else if (result.success) {
          setCategories(result.data)
        }
      }
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في تحميل الفئات:', error)
    }
  }

  const loadWarehouses = async () => {
    try {
      if (window.electronAPI) {
        const result: any = await window.electronAPI.getWarehouses()
        if (Array.isArray(result)) {
          setWarehouses(result)
        } else if (result.success) {
          setWarehouses(result.data)
        }
      }
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في تحميل المخازن:', error)
    }
  }

  const loadDepartments = async () => {
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionOrdersManagement', '❌ window.electronAPI غير متوفر - لا يمكن تحميل الأقسام')
        message.error('فشل في الاتصال بقاعدة البيانات')
        setDepartments([])
        return
      }

      const result = await window.electronAPI.getProductionDepartments()
      if (result.success) {
        setDepartments(result.data)
        Logger.info('ProductionOrdersManagement', '✅ تم تحميل أقسام الإنتاج من قاعدة البيانات')
      } else {
        message.warning('فشل في تحميل أقسام الإنتاج')
      }
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في تحميل الأقسام:', error)
      message.error('خطأ في تحميل أقسام الإنتاج')
    }
  }

  const loadCustomers = async () => {
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionOrdersManagement', '❌ window.electronAPI غير متوفر')
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      }

      const result = await window.electronAPI.getCustomers()
      if (result.success) {
        setCustomers(result.data)
        Logger.info('ProductionOrdersManagement', '✅ تم تحميل العملاء من قاعدة البيانات')
      } else {
        message.warning('فشل في تحميل العملاء')
      }
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في تحميل العملاء:', error)
      message.error('خطأ في تحميل العملاء')
    }
  }

  // تم إزالة loadWarehouses لأن المخازن ستؤخذ من الوصفة

  const loadRecipes = async () => {
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionOrdersManagement', '❌ window.electronAPI غير متوفر')
        message.error('فشل في الاتصال بقاعدة البيانات')
        setRecipes([])
        return
      }

      const result = await window.electronAPI.getProductionRecipes()
      if (result.success) {
        setRecipes(result.data)
        Logger.info('ProductionOrdersManagement', `✅ تم تحميل ${result.data.length} وصفة إنتاج`)

        if (result.data.length === 0) {
          Logger.warn('ProductionOrdersManagement', '⚠️ لا توجد وصفات إنتاج متاحة')
        }
      } else {
        message.error('فشل في تحميل الوصفات')
        setRecipes([])
      }
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في تحميل الوصفات:', error)
      message.error('خطأ في تحميل الوصفات')
      setRecipes([])
    }
  }



  // دالة لإعادة حساب التكلفة والوقت المقدر عند تغيير الكمية
  const handleQuantityChange = async (quantity: number | null) => {
    if (!quantity || quantity <= 0) return

    try {
      const productId = form.getFieldValue('product_id')
      if (!productId) return

      // البحث عن الوصفة المرتبطة بالصنف
      if (window.electronAPI) {
        const recipesResult = await window.electronAPI.getProductionRecipes()
        if (recipesResult.success) {
          const recipe = recipesResult.data.find((r: any) =>
            r.product_id === productId
          )

          if (recipe) {
            // حساب التكلفة والوقت المقدر بناءً على الكمية الجديدة
            const estimatedCost = (recipe.estimated_cost || 0) * quantity
            const estimatedHours = (recipe.estimated_time || 0) * quantity

            // تحديث القيم في النموذج
            form.setFieldsValue({
              estimated_cost: estimatedCost,
              estimated_hours: estimatedHours
            })
          }
        }
      }
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في إعادة حساب التكلفة والوقت:', error)
    }
  }

  const calculateStats = (ordersData: ProductionOrder[]) => {
    const stats = {
      total: ordersData.length,
      pending: ordersData.filter(o => o.status === 'pending').length,
      in_progress: ordersData.filter(o => o.status === 'in_progress').length,
      completed: ordersData.filter(o => o.status === 'completed').length,
      total_estimated_cost: ordersData.reduce((sum, o) => sum + o.estimated_cost, 0)
    }
    _setStats(stats)
  }

  const handleSubmit = async (values: any) => {
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionOrdersManagement', '❌ window.electronAPI غير متوفر')
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن حفّ أوامر الإنتاج')
        return
      }

      // طباعة قيم النموذج للتشخيص
      Logger.info('ProductionOrdersManagement', '🔍 قيم النموذج المرسلة:', values)

      // التحقق من الوصفة المختارة
      if (!values.recipe_id) {
        Logger.warn('ProductionOrdersManagement', '⚠️ لم يتم اختيار وصفة إنتاج')
        message.error('يجب اختيار وصفة الإنتاج أولاً. يرجى اختيار وصفة من القائمة المنسدلة.')
        return
      }

      if (!values.quantity || values.quantity <= 0) {
        message.error('يجب أن تكون الكمية أكبر من الصفر')
        return
      }

      // التحقق من صحة التواريخ
      if (values.start_date && values.end_date) {
        if (values.start_date.isAfter(values.end_date)) {
          message.error('تاريخ البدء يجب أن يكون قبل تاريخ الانتهاء')
          return
        }
      }

      if (values.start_date && values.start_date.isBefore(dayjs().subtract(1, 'year'))) {
        message.error('تاريخ البدء لا يمكن أن يكون في الماضي البعيد')
        return
      }

      // التحقق من صحة التكلفة والساعات المقدرة
      if (values.estimated_cost && values.estimated_cost < 0) {
        message.error('التكلفة المقدرة لا يمكن أن تكون سالبة')
        return
      }

      if (values.estimated_cost && values.estimated_cost > 1000000) {
        message.error('التكلفة المقدرة كبيرة جداً، يرجى التحقق من القيمة')
        return
      }

      if (values.estimated_hours && values.estimated_hours < 0) {
        message.error('الساعات المقدرة لا يمكن أن تكون سالبة')
        return
      }

      if (values.estimated_hours && values.estimated_hours > 10000) {
        message.error('الساعات المقدرة كبيرة جداً، يرجى التحقق من القيمة')
        return
      }

      // الحصول على معرف الوصفة من النموذج أو البحث التلقائي
      let recipeId = values.recipe_id || null
      if (!recipeId && values.item_id) {
        // البحث التلقائي عن الوصفة إذا لم يتم اختيارها يدوياً
        try {
          const recipe = recipes.find((r: any) => r.item_id === values.item_id || r.product_id === values.item_id)
          if (recipe) {
            recipeId = recipe.id
            Logger.info('ProductionOrdersManagement', `✅ تم العثور على وصفة تلقائياً: ${recipe.name} للصنف ID: ${values.item_id}`)
          } else {
            Logger.warn('ProductionOrdersManagement', `⚠️ لم يتم العثور على وصفة للصنف ID: ${values.item_id}`)
          }
        } catch (error) {
          Logger.error('ProductionOrdersManagement', 'خطأ في البحث عن الوصفة:', error)
        }
      }

      // التحقق من وجود الوصفة (مطلوبة لإنشاء أمر الإنتاج)
      if (!recipeId) {
        const selectedItem = availableItems.find(item => item.id === values.item_id)
        const itemName = selectedItem?.name || `الصنف ID: ${values.item_id}`
        message.error(`لا يمكن إنشاء أمر إنتاج للصنف "${itemName}" - لا توجد وصفة إنتاج مرتبطة. يجب إنشاء وصفة إنتاج أولاً.`)
        return
      }

      // الحصول على معلومات الوصفة المختارة
      const selectedRecipe = recipes.find(r => r.id === values.recipe_id)
      if (!selectedRecipe) {
        message.error('لم يتم العثور على الوصفة المختارة')
        return
      }

      const orderData: any = {
        product_id: selectedRecipe.product_id, // المنتج من الوصفة المختارة
        recipe_id: values.recipe_id, // معرف الوصفة المختارة
        quantity: values.quantity,
        department_id: values.department_id,
        customer_id: values.customer_id ? parseInt(String(values.customer_id)) : null, // تحويل معرف العميل إلى رقم صحيح
        // تم إزالة warehouse_id - سيتم تحديد المخازن من الوصفة
        start_date: values.start_date?.format('YYYY-MM-DD'),
        end_date: values.expected_completion_date?.format('YYYY-MM-DD'),
        priority: values.priority || 'normal',
        estimated_cost: values.estimated_cost || 0,
        estimated_hours: values.estimated_hours || 0,
        notes: values.notes,
        created_by: 1 // سيتم تحديثه لاحقاً مع نّام المستخدمين
      }

      let result
      if (editingOrder) {
        result = await window.electronAPI.updateProductionOrder(editingOrder.id, orderData)
      } else {
        // إنشاء أمر إنتاج جديد
        // إنشاء رقم أمر تلقائي
        const orderNumberResponse = await window.electronAPI.generateProductionOrderNumber()
        if (orderNumberResponse.success) {
          orderData.order_number = orderNumberResponse.data.order_number
        } else {
          orderData.order_number = 'PROD' + Date.now().toString().slice(-6)
        }

        // إنشاء كود أمر تلقائي
        const orderCodeResponse = await window.electronAPI.generateProductionOrderCode()
        if (orderCodeResponse.success) {
          orderData.order_code = orderCodeResponse.data.order_code
        } else {
          orderData.order_code = 'PO-' + Date.now().toString().slice(-6)
        }

        result = await window.electronAPI.createProductionOrder(orderData)
      }

      if (result.success) {
        let successMessage = ''
        if (editingOrder) {
          successMessage = 'تم تحديث أمر الإنتاج بنجاح'
        } else {
          successMessage = `تم إنشاء أمر الإنتاج بنجاح - رقم الأمر: ${orderData.order_number}`
        }

        message.success(successMessage)

        // حفّ البيانات للإدخال السريع
        if (!editingOrder) {
          const quickInputData = {
            ...values,
            customer_name: customers.find(c => c.id === values.customer_id)?.name,
            item_name: items.find(i => i.id === values.item_id)?.name,
            department_name: departments.find(d => d.id === values.department_id)?.name
          }

          const label = quickInputData.item_name + ' - ' + (quickInputData.customer_name || 'إنتاج داخلي')
          QuickInputService.saveRecentInput('production_order', quickInputData, label)
          loadRecentInputs()

          // عرض خيار رفع الصور للأوامر الجديدة
          setTimeout(() => {
            Modal.confirm({
              title: '📸 رفع صور أمر الإنتاج',
              content: (
                <div>
                  <p>تم إنشاء أمر الإنتاج بنجاح!</p>
                  <p><strong>رقم الأمر:</strong> {result.data?.order_number || orderData.order_number}</p>
                  <p>هل تريد رفع صور توضيحية للأمر الآن؟</p>
                </div>
              ),
              okText: '📷 رفع الصور',
              cancelText: 'لاحقاً',
              onOk: () => {
                // البحث عن الأمر المنشأ حديثاً وفتحه للتعديل
                const newOrderId = result.data?.id
                if (newOrderId) {
                  // إعادة تحميل الأوامر أولاً
                  loadOrders().then(() => {
                    // البحث عن الأمر الجديد
                    const newOrder = orders.find(order => order.id === newOrderId) || result.data
                    if (newOrder) {
                      handleEdit(newOrder)
                    }
                  })
                }
              }
            })
          }, 1000)
        }

        setModalVisible(false)
        setEditingOrder(null)
        form.resetFields()
        // إعادة تعيين النموذج
        loadOrders()
      } else {
        message.error(result.message || 'فشل في حفّ أمر الإنتاج')
      }
    } catch (error) {
      message.error('خطأ في حفّ أمر الإنتاج')
    }
  }

  const handleEdit = async (order: ProductionOrder) => {
    try {
      setEditingOrder(order)

      // تحميل معلومات الوصفة المرتبطة بأمر الإنتاج
      let recipeInfo = null
      if (order.recipe_id && window.electronAPI) {
        const recipesResult = await window.electronAPI.getProductionRecipes()
        if (recipesResult.success) {
          recipeInfo = recipesResult.data.find((r: any) => r.id === order.recipe_id)
        }
      }

      // إعداد قيم النموذج مع معلومات الوصفة إذا كانت متوفرة
      const formValues = {
        ...order,
        order_number: order.order_number,
        order_date: dayjs(order.order_date),
        expected_completion_date: order.expected_completion_date ? dayjs(order.expected_completion_date) : null
      }

      // إذا كانت هناك وصفة مرتبطة، عرض معلوماتها
      if (recipeInfo) {
        message.info(`أمر الإنتاج مرتبط بالوصفة: ${recipeInfo.name} - التكلفة المقدرة: ${order.estimated_cost?.toFixed(2) || 0}`)
      }

      form.setFieldsValue(formValues)
      setModalVisible(true)
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في تحميل معلومات أمر الإنتاج:', error)
      // في حالة الخطأ، استمر بالتحميل العادي
      setEditingOrder(order)
      form.setFieldsValue({
        ...order,
        order_number: order.order_number,
        order_date: dayjs(order.order_date),
        expected_completion_date: order.expected_completion_date ? dayjs(order.expected_completion_date) : null
      })
      setModalVisible(true)
    }
  }



  // دالة معالجة تغيير الوصفة المختارة - النظام الجديد
  const handleRecipeChange = (recipeId: number) => {
    Logger.info('ProductionOrdersManagement', `🔄 تم اختيار وصفة بمعرف: ${recipeId}`)

    if (!recipeId) {
      // إذا تم مسح الوصفة، قم بمسح التكلفة والوقت المقدر
      form.setFieldsValue({
        recipe_id: undefined,
        product_id: undefined,
        department_id: undefined,
        estimated_cost: 0,
        estimated_hours: 0
      })
      Logger.warn('ProductionOrdersManagement', '⚠️ تم مسح اختيار الوصفة')
      return
    }

    // البحث عن الوصفة المختارة
    const selectedRecipe = recipes.find(recipe => recipe.id === recipeId)
    if (selectedRecipe) {
      // حساب التكلفة والوقت المقدر من الوصفة
      const quantity = form.getFieldValue('quantity') || 1
      const estimatedCost = (selectedRecipe.total_cost || selectedRecipe.estimated_cost || 0) * quantity
      const estimatedHours = (selectedRecipe.estimated_time || selectedRecipe.estimated_hours || 0) * quantity

      // تحديث النموذج بمعلومات الوصفة والمنتج والقسم
      form.setFieldsValue({
        recipe_id: recipeId, // التأكد من حفظ معرف الوصفة
        product_id: selectedRecipe.product_id,
        department_id: selectedRecipe.department_id,
        estimated_cost: estimatedCost,
        estimated_hours: estimatedHours
      })

      Logger.info('ProductionOrdersManagement', `✅ تم اختيار الوصفة: ${selectedRecipe.name} - المنتج: ${selectedRecipe.product_name}`)
      message.success(`تم اختيار الوصفة: ${selectedRecipe.name} - المنتج: ${selectedRecipe.product_name} - القسم: ${selectedRecipe.department_name || 'غير محدد'} - التكلفة المقدرة: ${estimatedCost.toFixed(2)}`)
    } else {
      Logger.error('ProductionOrdersManagement', `❌ لم يتم العثور على الوصفة بمعرف: ${recipeId}`)
      message.error('لم يتم العثور على الوصفة المختارة')
    }
  }



  const handleStartOrder = async (orderId: number) => {
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionOrdersManagement', '❌ window.electronAPI غير متوفر')
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن بدء أوامر الإنتاج')
        return
      }

      // عرض تأكيد مع تفاصيل المواد المطلوبة
      Modal.confirm({
        title: 'تأكيد بدء أمر الإنتاج',
        content: (
          <div>
            <p>هل أنت متأكد من بدء أمر الإنتاج؟</p>
            <p style={{ color: '#fa8c16', fontSize: '12px' }}>
              ⚠️ سيتم استهلاك المواد الخام المطلوبة من المخزون تلقائياً
            </p>
          </div>
        ),
        okText: 'بدء الإنتاج',
        cancelText: 'إلغاء',
        onOk: async () => {
          const result = await window.electronAPI.startProductionOrder(orderId)
          if (result.success) {
            message.success({
              content: 'تم بدء أمر الإنتاج بنجاح وتم استهلاك المواد من المخزون',
              duration: 4
            })
            loadOrders()
          } else {
            message.error(result.message || 'فشل في بدء أمر الإنتاج')
          }
        }
      })
    } catch (error) {
      message.error('خطأ في بدء أمر الإنتاج')
    }
  }

  const handleCompleteOrder = async (orderId: number) => {
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionOrdersManagement', '❌ window.electronAPI غير متوفر')
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن إكمال أوامر الإنتاج')
        return
      }

      // عرض تأكيد مع تفاصيل إضافة المنتج للمخزون
      Modal.confirm({
        title: 'تأكيد إكمال أمر الإنتاج',
        content: (
          <div>
            <p>هل أنت متأكد من إكمال أمر الإنتاج؟</p>
            <p style={{ color: '#52c41a', fontSize: '12px' }}>
              ✅ سيتم إضافة المنتج النهائي إلى المخزون تلقائياً
            </p>
          </div>
        ),
        okText: 'إكمال الإنتاج',
        cancelText: 'إلغاء',
        onOk: async () => {
          const result = await window.electronAPI.completeProductionStage(orderId)
          if (result.success) {
            message.success({
              content: `تم إكمال أمر الإنتاج بنجاح!
                الكمية المنتجة: ${result.data.actual_quantity}
                التكلفة الإجمالية: ₪${result.data.actual_total_cost.toFixed(2)}
                تكلفة الوحدة: ₪${result.data.unit_cost.toFixed(2)}`,
              duration: 4
            })
            loadOrders()
          } else {
            message.error(result.message || 'فشل في إكمال أمر الإنتاج')
          }
        }
      })
    } catch (error) {
      message.error('خطأ في إكمال أمر الإنتاج')
    }
  }

  const handleDeleteOrder = async (orderId: number) => {
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionOrdersManagement', '❌ window.electronAPI غير متوفر')
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن حذف أوامر الإنتاج')
        return
      }

      const result = await window.electronAPI.deleteProductionOrder(orderId)
      if (result.success) {
        message.success('تم حذف أمر الإنتاج بنجاح')
        loadOrders()
      } else {
        message.error(result.message || 'فشل في حذف أمر الإنتاج')
      }
    } catch (error) {
      message.error('خطأ في حذف أمر الإنتاج')
    }
  }

  const handleCancelOrder = async (orderId: number) => {
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionOrdersManagement', '❌ window.electronAPI غير متوفر')
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن إلغاء أوامر الإنتاج')
        return
      }

      const result = await window.electronAPI.updateProductionOrder(orderId, { status: 'cancelled' })
      if (result.success) {
        message.success('تم إلغاء أمر الإنتاج بنجاح')
        loadOrders()
      } else {
        message.error(result.message || 'فشل في إلغاء أمر الإنتاج')
      }
    } catch (error) {
      message.error('خطأ في إلغاء أمر الإنتاج')
    }
  }

  const handlePauseOrder = async (orderId: number) => {
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionOrdersManagement', '❌ window.electronAPI غير متوفر')
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن تعليق أوامر الإنتاج')
        return
      }

      const result = await window.electronAPI.updateProductionOrder(orderId, { status: 'on_hold' })
      if (result.success) {
        message.success('تم تعليق أمر الإنتاج بنجاح')
        loadOrders()
      } else {
        message.error(result.message || 'فشل في تعليق أمر الإنتاج')
      }
    } catch (error) {
      message.error('خطأ في تعليق أمر الإنتاج')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'in_progress': return 'blue'
      case 'completed': return 'green'
      case 'cancelled': return 'red'
      case 'on_hold': return 'purple'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار'
      case 'in_progress': return 'قيد الإنتاج'
      case 'completed': return 'مكتمل'
      case 'cancelled': return 'ملغي'
      case 'on_hold': return 'معلق'
      default: return status
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'green'
      case 'normal': return 'blue'
      case 'high': return 'orange'
      case 'urgent': return 'red'
      default: return 'default'
    }
  }

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'low': return 'منخفضة'
      case 'normal': return 'عادية'
      case 'high': return 'عالية'
      case 'urgent': return 'عاجلة'
      default: return priority
    }
  }



  const handleViewDetails = (order: ProductionOrder) => {
    setSelectedOrderId(order.id)
    setDetailsModalVisible(true)
  }

  const handleQuickInput = (recentData: RecentData) => {
    QuickInputHelper.applyDataToForm(form, recentData.data)

    // تحديث التواريخ للوقت الحالي
    form.setFieldsValue({
      order_date: dayjs(),
      expected_completion_date: recentData.data.expected_completion_date ?
        dayjs().add(7, 'days') : undefined
    })

    message.success('تم تطبيق البيانات: ' + recentData.label)
  }

  const handleSaveAsPreference = () => {
    const currentValues = form.getFieldsValue()
    const preferences = {
      department_id: currentValues.department_id,
      priority: currentValues.priority,
      unit: currentValues.unit,
      estimated_hours: currentValues.estimated_hours
    }



  const _addOrderToCalendar = (order: ProductionOrder) => {
    if (order.start_date && order.expected_completion_date) {
      furnitureCalendarService.addProductionOrderEvent(
        order.id.toString(),
        order.order_number,
        order.product_name,
        new Date(order.start_date),
        new Date(order.expected_completion_date),
        order.department_id.toString(),
        order.department_name
      )
    }

    if (order.expected_completion_date) {
      furnitureCalendarService.addDeadlineEvent(
        order.id.toString(),
        order.order_number,
        new Date(order.expected_completion_date),
        order.priority as any
      )
    }
  }

  const _addOrderNotifications = (order: ProductionOrder) => {
    if (order.expected_completion_date) {
      furnitureNotificationService.addDeadlineNotification(
        order.id.toString(),
        order.order_number,
        new Date(order.expected_completion_date)
      )
    }
  }

    QuickInputService.saveUserPreferences('production_order', preferences)
    message.success('تم حفّ الإعدادات المفضلة')
  }

  const applyUserPreferences = () => {
    const userPreferences = QuickInputService.getUserPreferences('production_order')
    const defaultValues = {
      order_date: dayjs(),
      priority: userPreferences.priority || 'normal',
      department_id: userPreferences.department_id,
      unit: userPreferences.unit || 'قطعة',
      ...userPreferences
    }

    form.setFieldsValue(defaultValues)
  }

  // دالة تصدير Excel
  const handleExportExcel = () => {
    try {
      // فحص البيانات قبل التصدير
      if (!orders || orders.length === 0) {
        message.warning('لا توجد أوامر إنتاج للتصدير')
        return
      }

      // تحضير البيانات للتصدير
      const exportData = orders.map(order => ({
        'رقم الأمر': order.order_number,
        'المنتج': order.item_name || order.product_name || '',
        'العميل': order.customer_name || 'إنتاج داخلي',
        'القسم': order.department_name,
        'الكمية': order.quantity,
        'الحالة': getStatusText(order.status),
        'الأولوية': getPriorityText(order.priority),
        'التكلفة المقدرة': order.estimated_cost || 0,
        'التكلفة الفعلية': order.actual_cost || 0,
        'الساعات المقدرة': order.estimated_hours || 0,
        'الساعات الفعلية': order.actual_hours || 0,
        'تاريخ الأمر': order.order_date,
        'تاريخ البدء': order.start_date || '-',
        'تاريخ الإنجاز المتوقع': order.expected_completion_date || '-',
        'تاريخ الإنجاز الفعلي': order.actual_completion_date || '-',
        'الملاحّات': order.notes || '-',
        'تاريخ الإنشاء': order.created_at
      }))

      // إنشاء ورقة العمل
      const worksheet = XLSX.utils.json_to_sheet(exportData)

      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 15 }, // رقم الأمر
        { wch: 20 }, // المنتج
        { wch: 20 }, // العميل
        { wch: 15 }, // القسم
        { wch: 10 }, // الكمية
        { wch: 12 }, // الحالة
        { wch: 10 }, // الأولوية
        { wch: 15 }, // التكلفة المقدرة
        { wch: 15 }, // التكلفة الفعلية
        { wch: 15 }, // الساعات المقدرة
        { wch: 15 }, // الساعات الفعلية
        { wch: 15 }, // تاريخ الأمر
        { wch: 15 }, // تاريخ البدء
        { wch: 20 }, // تاريخ الإنجاز المتوقع
        { wch: 20 }, // تاريخ الإنجاز الفعلي
        { wch: 30 }, // الملاحّات
        { wch: 20 }  // تاريخ الإنشاء
      ]
      worksheet['!cols'] = columnWidths

      // إنشاء المصنف
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'أوامر الإنتاج')

      // إضافة ورقة إحصائيات
      const stats = [
        { 'المؤشر': 'إجمالي الأوامر', 'القيمة': orders.length },
        { 'المؤشر': 'أوامر قيد التنفيذ', 'القيمة': orders.filter(o => o.status === 'in_progress').length },
        { 'المؤشر': 'أوامر مكتملة', 'القيمة': orders.filter(o => o.status === 'completed').length },
        { 'المؤشر': 'أوامر معلقة', 'القيمة': orders.filter(o => o.status === 'on_hold').length },
        { 'المؤشر': 'إجمالي التكلفة المقدرة', 'القيمة': orders.reduce((sum, o) => sum + (o.estimated_cost || 0), 0) },
        { 'المؤشر': 'إجمالي التكلفة الفعلية', 'القيمة': orders.reduce((sum, o) => sum + (o.actual_cost || 0), 0) },
        { 'المؤشر': 'إجمالي الساعات المقدرة', 'القيمة': orders.reduce((sum, o) => sum + (o.estimated_hours || 0), 0) },
        { 'المؤشر': 'إجمالي الساعات الفعلية', 'القيمة': orders.reduce((sum, o) => sum + (o.actual_hours || 0), 0) }
      ]

      const statsWorksheet = XLSX.utils.json_to_sheet(stats)
      statsWorksheet['!cols'] = [{ wch: 25 }, { wch: 20 }]
      XLSX.utils.book_append_sheet(workbook, statsWorksheet, 'الإحصائيات')

      // حفّ الملف
      const fileName = 'أوامر_الإنتاج_' + dayjs().format('YYYY-MM-DD_HH-mm') + '.xlsx'
      XLSX.writeFile(workbook, fileName)

      message.success(`تم تصدير ${exportData.length} أمر إنتاج بنجاح`)
    } catch (error) {
      Logger.error('ProductionOrdersManagement', 'خطأ في تصدير البيانات:', error)
      message.error('حدث خطأ أثناء تصدير البيانات')
    }
  }



  // دوال الفلترة المتقدمة
  const applyFilters = (filters: ProductionFilters) => {
    setActiveFilters(filters)
    let filtered = [...allOrders]

    // فلتر التاريخ
    if (filters.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
      const startDate = filters.dateRange[0].format('YYYY-MM-DD')
      const endDate = filters.dateRange[1].format('YYYY-MM-DD')
      filtered = filtered.filter(order => {
        const orderDate = dayjs(order.order_date).format('YYYY-MM-DD')
        return orderDate >= startDate && orderDate <= endDate
      })
    }

    // فلتر الحالة
    if (filters.status && filters.status.length > 0) {
      filtered = filtered.filter(order => filters.status?.includes(order.status))
    }

    // فلتر الأولوية
    if (filters.priority && filters.priority.length > 0) {
      filtered = filtered.filter(order => filters.priority?.includes(order.priority))
    }

    // فلتر القسم
    if (filters.department_id && filters.department_id.length > 0) {
      filtered = filtered.filter(order => filters.department_id?.includes(order.department_id))
    }

    // فلتر العميل
    if (filters.customer_id && filters.customer_id.length > 0) {
      filtered = filtered.filter(order =>
        order.customer_id && filters.customer_id?.includes(order.customer_id)
      )
    }

    // فلتر المنتج
    if (filters.item_id && filters.item_id.length > 0) {
      filtered = filtered.filter(order => filters.item_id?.includes(order.product_id))
    }

    // فلتر نطاق التكلفة
    if (filters.estimated_cost_range && filters.estimated_cost_range[0] !== filters.estimated_cost_range[1]) {
      const [min, max] = filters.estimated_cost_range
      filtered = filtered.filter(order =>
        order.estimated_cost >= min && order.estimated_cost <= max
      )
    }

    // فلتر نطاق الساعات
    if (filters.estimated_hours_range && filters.estimated_hours_range[0] !== filters.estimated_hours_range[1]) {
      const [min, max] = filters.estimated_hours_range
      filtered = filtered.filter(order =>
        order.estimated_hours >= min && order.estimated_hours <= max
      )
    }

    // فلتر عرض المكتملة
    if (!filters.show_completed) {
      filtered = filtered.filter(order => order.status !== 'completed')
    }

    // فلتر عرض الملغية
    if (!filters.show_cancelled) {
      filtered = filtered.filter(order => order.status !== 'cancelled')
    }

    setFilteredOrders(filtered)
    setOrders(filtered)
    calculateStats(filtered)
  }

  const resetFilters = () => {
    setActiveFilters({})
    setFilteredOrders(allOrders)
    setOrders(allOrders)
    calculateStats(allOrders)
  }

  const columns = [
    {
      title: 'رقم الأمر',
      dataIndex: 'order_number',
      key: 'order_number',
      width: 120,
      fixed: 'left' as const,
      render: (text: string) => (
        <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
          {text || 'غير محدد'}
        </div>
      )
    },
    {
      title: 'كود الصنف',
      dataIndex: 'item_code',
      key: 'item_code',
      width: 120,
      render: (text: string) => (
        <div style={{
          fontWeight: 'bold',
          color: '#722ed1',
          backgroundColor: '#f9f0ff',
          padding: '4px 8px',
          borderRadius: '4px',
          textAlign: 'center'
        }}>
          {text || 'غير محدد'}
        </div>
      )
    },
    {
      title: 'اسم الصنف',
      dataIndex: 'item_name',
      key: 'item_name',
      width: 200,
      render: (text: string, record: ProductionOrder) => (
        <div>
          <div style={{ fontWeight: 'bold', color: '#1890ff' }}>{text}</div>
          <div style={{ fontSize: '11px', color: '#999', marginTop: '2px' }}>
            الكمية: <strong>{record.quantity} {record.unit}</strong>
          </div>
        </div>
      )
    },
    {
      title: 'القسم',
      dataIndex: 'department_name',
      key: 'department_name',
      width: 150,
    },
    {
      title: 'العميل',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 150,
      render: (text: string) => text || 'غير محدد'
    },
    {
      title: 'التواريخ',
      key: 'dates',
      width: 180,
      render: (record: ProductionOrder) => (
        <div>
          <div style={{ fontSize: '12px', marginBottom: '4px' }}>
            <span style={{ color: '#666' }}>البدء: </span>
            <span style={{ fontWeight: 'bold' }}>
              {record.order_date ? dayjs(record.order_date).format('YYYY-MM-DD') : 'غير محدد'}
            </span>
          </div>
          <div style={{ fontSize: '12px' }}>
            <span style={{ color: '#666' }}>الانتهاء: </span>
            <span style={{ fontWeight: 'bold', color: record.expected_completion_date ? '#1890ff' : '#999' }}>
              {record.expected_completion_date ? dayjs(record.expected_completion_date).format('YYYY-MM-DD') : 'غير محدد'}
            </span>
          </div>
        </div>
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'الأولوية',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>
          {getPriorityText(priority)}
        </Tag>
      )
    },
    {
      title: 'التكلفة',
      key: 'cost',
      width: 140,
      render: (record: ProductionOrder) => (
        <div>
          <div style={{ fontSize: '12px', marginBottom: '4px' }}>
            <span style={{ color: '#666' }}>المقدرة: </span>
            <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
              ₪{record.estimated_cost ? record.estimated_cost.toFixed(2) : '0.00'}
            </span>
          </div>
          {record.actual_cost > 0 && (
            <div style={{ fontSize: '12px' }}>
              <span style={{ color: '#666' }}>الفعلية: </span>
              <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
                ₪{record.actual_cost.toFixed(2)}
              </span>
            </div>
          )}
        </div>
      )
    },

    {
      title: 'الإجراءات',
      key: 'actions',
      width: 450,
      render: (record: ProductionOrder) => (
        <Space wrap size="small">
          <Tooltip title="عرض تفاصيل أمر الإنتاج كاملة مع جميع الأصناف والمواد">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
              style={{
                backgroundColor: '#1890ff',
                borderColor: '#1890ff',
                color: 'white'
              }}
            >
              عرض
            </Button>
          </Tooltip>

          {/* زر الطباعة السريعة */}
          <Tooltip title="طباعة سريعة لأمر الإنتاج">
            <UnifiedPrintButton
              data={async () => {
                try {
                  // جلب الصور المرفقة مع أمر الإنتاج
                  const orderImages = await getOrderImages(record.id.toString())
                  Logger.info('ProductionOrdersManagement', `🖼️ تم العثور على ${orderImages.length} صورة مرفقة مع أمر الإنتاج ${record.order_number} للطباعة السريعة`)

                  // تحويل الصور إلى تنسيق الطباعة
                  const printImages = orderImages.map(image => {
                    Logger.info('ProductionOrdersManagement', `🖼️ معالجة صورة: ${image.originalName}, URL: ${image.url}`)
                    return {
                      id: image.id,
                      name: image.originalName || image.filename || 'صورة غير مسماة',
                      path: image.url || image.thumbnailUrl, // استخدام URL أو thumbnailUrl
                      description: image.description || `صورة أمر الإنتاج ${record.order_number}`,
                      category: 'صورة أمر الإنتاج',
                      size: image.size,
                      uploadDate: DateUtils.formatForDisplay(image.uploadedAt, 'DD/MM/YYYY'),
                      notes: image.tags?.join(', ') || '',
                      metadata: {
                        orderNumber: record.order_number,
                        orderDate: record.order_date,
                        imageType: image.category,
                        uploadedBy: image.uploadedBy || 'غير محدد'
                      }
                    }
                  })

                  // إنشاء بيانات محدثة مع النصوص العربية
                  const updatedRecord = {
                    ...record,
                    status: getStatusText(record.status), // تحويل إلى النص العربي
                    priority: getPriorityText(record.priority), // تحويل إلى النص العربي
                    product_name: record.item_name || record.product_name || '',
                    customer_name: record.customer_name || 'إنتاج داخلي'
                  }

                  return {
                    title: `أمر إنتاج رقم ${record.order_number}`,
                    subtitle: `العميل: ${record.customer_name || 'إنتاج داخلي'} | القسم: ${record.department_name} | تاريخ الأمر: ${record.order_date ? DateUtils.formatForDisplay(record.order_date, 'DD/MM/YYYY') : 'غير محدد'} | تاريخ التسليم: ${record.expected_completion_date ? DateUtils.formatForDisplay(record.expected_completion_date, 'DD/MM/YYYY') : 'غير محدد'}`,

                    // إضافة بيانات العميل مع معلومات إضافية
                    customer: {
                      name: record.customer_name || 'إنتاج داخلي',
                      department: record.department_name,
                      orderDate: record.order_date ? DateUtils.formatForDisplay(record.order_date, 'DD/MM/YYYY') : '',
                      deliveryDate: record.expected_completion_date ? DateUtils.formatForDisplay(record.expected_completion_date, 'DD/MM/YYYY') : '',
                      type: 'عميل'
                    },

                    data: [updatedRecord], // استخدام البيانات المحدثة
                    columns: [
                      { key: 'order_number', title: 'رقم الأمر', align: 'center' },
                      { key: 'product_name', title: 'المنتج', align: 'right' },
                      { key: 'quantity', title: 'الكمية', align: 'center', format: 'number' },
                      { key: 'status', title: 'الحالة', align: 'center' },
                      { key: 'priority', title: 'الأولوية', align: 'center' },
                      { key: 'estimated_cost', title: 'التكلفة المقدرة', align: 'center', format: 'currency' }
                    ],
                    summary: {
                      compact: true,
                      data: [
                        { label: 'الكمية', value: record.quantity, format: 'number' },
                        { label: 'التكلفة', value: record.estimated_cost || 0, format: 'currency' },
                        { label: 'الحالة', value: getStatusText(record.status) },
                        { label: 'الأولوية', value: getPriorityText(record.priority) }
                      ]
                    },
                    // إضافة الصور المرفقة إلى بيانات الطباعة
                    images: printImages,
                    imageSettings: {
                      layout: 'grid' as const,
                      imagesPerPage: 4,
                      showMetadata: true,
                      imageQuality: 'medium' as const,
                      fitToPage: true
                    },
                    metadata: {
                      generatedAt: DateUtils.formatForDisplay(new Date(), 'DD/MM/YYYY HH:mm'),
                      generatedBy: 'نظام الإنتاج',
                      orderNumber: record.order_number,
                      documentType: 'أمر إنتاج سريع',
                      totalImages: printImages.length
                    }
                  }
                } catch (error) {
                  Logger.error('ProductionOrdersManagement', 'خطأ في تحضير الطباعة السريعة:', error)
                  throw error
                }
              }}
              type="report"
              subType="production"
              title="طباعة سريعة لأمر الإنتاج"
              buttonText=""
              size="small"
              buttonType="default"
              showDropdown={true}
              showExportOptions={true}
              showSettings={true}
              icon={<PrinterOutlined style={{ color: '#1890ff' }} />}
              customSettings={{
                showLogo: true,
                showHeader: true,
                showFooter: false,
                primaryColor: '#1890ff',
                orientation: 'portrait',
                pageSize: 'A4'
              }}
              onBeforePrint={() => {
                Logger.info('ProductionOrdersManagement', `بدء طباعة سريعة لأمر الإنتاج: ${record.order_number}`)
              }}
              onAfterPrint={() => {
                Logger.info('ProductionOrdersManagement', `تم إكمال الطباعة السريعة لأمر الإنتاج: ${record.order_number}`)
              }}
              onError={(error) => {
                Logger.error('ProductionOrdersManagement', `خطأ في الطباعة السريعة لأمر الإنتاج ${record.order_number}:`, error)
              }}
            />
          </Tooltip>

          {/* زر الطباعة المفصلة */}
          <Tooltip title="طباعة مفصلة لأمر الإنتاج مع المواد">
            <UnifiedPrintButton
              data={async () => {
                try {
                  const printData = await prepareDetailedOrderPrintData(record)
                  if (!printData) {
                    throw new Error('فشل في تحضير بيانات الطباعة المفصلة')
                  }
                  return printData
                } catch (error) {
                  Logger.error('ProductionOrdersManagement', 'خطأ في تحضير الطباعة المفصلة:', error)
                  throw error
                }
              }}
              type="report"
              subType="production"
              title="طباعة مفصلة لأمر الإنتاج"
              buttonText=""
              size="small"
              buttonType="default"
              showDropdown={true}
              showExportOptions={true}
              showSettings={true}
              icon={<FilePdfOutlined style={{ color: '#722ed1' }} />}
              customSettings={{
                showLogo: true,
                showHeader: true,
                showFooter: true,
                primaryColor: '#722ed1',
                orientation: 'portrait',
                pageSize: 'A4'
              }}
              onBeforePrint={() => {
                Logger.info('ProductionOrdersManagement', `بدء طباعة مفصلة لأمر الإنتاج: ${record.order_number}`)
              }}
              onAfterPrint={() => {
                Logger.info('ProductionOrdersManagement', `تم إكمال الطباعة المفصلة لأمر الإنتاج: ${record.order_number}`)
              }}
              onError={(error) => {
                Logger.error('ProductionOrdersManagement', `خطأ في الطباعة المفصلة لأمر الإنتاج ${record.order_number}:`, error)
              }}
            />
          </Tooltip>

          {/* زر طباعة مع QR Code */}
          <Tooltip title="طباعة أمر الإنتاج مع باركود QR">
            <UnifiedPrintButton
              data={async () => {
                try {
                  const printData = await prepareOrderWithQRCode(record)
                  if (!printData) {
                    throw new Error('فشل في تحضير بيانات الطباعة مع QR')
                  }
                  return printData
                } catch (error) {
                  Logger.error('ProductionOrdersManagement', 'خطأ في تحضير بيانات الطباعة مع QR:', error)
                  throw error
                }
              }}
              type="report"
              subType="production"
              title="طباعة أمر الإنتاج مع باركود QR"
              buttonText=""
              size="small"
              buttonType="default"
              showDropdown={true}
              showExportOptions={true}
              showSettings={true}
              icon={<BarcodeOutlined style={{ color: '#52c41a' }} />}
              customSettings={{
                showLogo: true,
                showHeader: true,
                showFooter: true,
                primaryColor: '#52c41a',
                orientation: 'portrait',
                pageSize: 'A4'
              }}
              onBeforePrint={() => {
                Logger.info('ProductionOrdersManagement', `بدء طباعة أمر الإنتاج مع QR: ${record.order_number}`)
              }}
              onAfterPrint={() => {
                Logger.info('ProductionOrdersManagement', `تم إكمال طباعة أمر الإنتاج مع QR: ${record.order_number}`)
              }}
              onError={(error) => {
                Logger.error('ProductionOrdersManagement', `خطأ في طباعة أمر الإنتاج مع QR ${record.order_number}:`, error)
              }}
            />
          </Tooltip>
          <Tooltip title="تعديل أمر الإنتاج">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              style={{
                backgroundColor: '#fa8c16',
                borderColor: '#fa8c16'
              }}
            />
          </Tooltip>
          {record.status === 'pending' && (
            <Tooltip title="بدء تنفيذ أمر الإنتاج">
              <Button
                type="primary"
                size="small"
                icon={<PlayCircleOutlined />}
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                onClick={() => handleStartOrder(record.id)}
              />
            </Tooltip>
          )}
          {record.status === 'in_progress' && (
            <Tooltip title="إكمال أمر الإنتاج">
              <Button
                type="primary"
                size="small"
                icon={<CheckCircleOutlined />}
                style={{ backgroundColor: '#13c2c2', borderColor: '#13c2c2' }}
                onClick={() => handleCompleteOrder(record.id)}
              />
            </Tooltip>
          )}
          {record.status === 'in_progress' && (
            <Tooltip title="تعليق أمر الإنتاج">
              <Button
                size="small"
                icon={<PauseCircleOutlined />}
                onClick={() => handlePauseOrder(record.id)}
                style={{
                  backgroundColor: '#faad14',
                  borderColor: '#faad14',
                  color: 'white'
                }}
              />
            </Tooltip>
          )}
          {(record.status === 'pending' || record.status === 'on_hold') && (
            <Tooltip title="إلغاء أمر الإنتاج">
              <Button
                danger
                size="small"
                icon={<StopOutlined />}
                onClick={() => handleCancelOrder(record.id)}
              />
            </Tooltip>
          )}
          <Tooltip title="إضافة تنبيه مخصص">
            <Button
              size="small"
              icon={<NotificationOutlined />}
              onClick={() => handleAddCustomNotification(record)}
              style={{
                backgroundColor: '#f5222d',
                borderColor: '#f5222d',
                color: 'white'
              }}
            />
          </Tooltip>
          <Tooltip title="عرض/إضافة صور">
            <Button
              size="small"
              icon={<CameraOutlined />}
              onClick={() => {
                setSelectedOrderForNotification(record)
                setImageUploadModalVisible(true)
              }}
              style={{
                backgroundColor: '#722ed1',
                borderColor: '#722ed1',
                color: 'white'
              }}
            />
          </Tooltip>
          {record.status === 'pending' && (
            <Popconfirm
              title="هل أنت متأكد من حذف أمر الإنتاج؟"
              description="سيتم حذف أمر الإنتاج نهائياً ولا يمكن التراجع عن هذا الإجراء"
              onConfirm={() => handleDeleteOrder(record.id)}
              okText="حذف"
              cancelText="إلغاء"
              okButtonProps={{ danger: true }}
            >
              <Tooltip title="حذف أمر الإنتاج">
                <Button
                  danger
                  size="small"
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* رسالة ترحيب للمستخدمين الجدد */}
      {orders.length === 0 && !loading && (
        <div style={{
          textAlign: 'center',
          padding: '40px 20px',
          backgroundColor: '#f6ffed',
          border: '2px dashed #b7eb8f',
          borderRadius: '8px',
          marginBottom: '24px'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>🎯</div>
          <Typography.Title level={3} style={{ color: '#52c41a', marginBottom: '12px' }}>
            مرحباً بك في إدارة أوامر الإنتاج!
          </Typography.Title>
          <Typography.Paragraph style={{ fontSize: '16px', color: '#52c41a', marginBottom: '20px' }}>
            لا توجد أوامر إنتاج حالياً. ابدأ بإنشاء أول أمر إنتاج لك!
          </Typography.Paragraph>
          <Space size="large">
            <Button
              type="primary"
              size="large"
              icon={<PlusOutlined />}
              onClick={async () => {
                setEditingOrder(null)
                form.resetFields()
                applyUserPreferences()

                // إعادة تحميل الوصفات للتأكد من وجود أحدث البيانات
                await loadRecipes()

                setModalVisible(true)

                // عرض تحذير إذا لم توجد وصفات
                if (recipes.length === 0) {
                  setTimeout(() => {
                    message.warning({
                      content: 'لا توجد وصفات إنتاج متاحة. يجب إنشاء وصفة إنتاج أولاً قبل إنشاء أمر إنتاج.',
                      duration: 5
                    })
                  }, 500)
                }
              }}
            >
              إنشاء أول أمر إنتاج
            </Button>
            <Button
              size="large"
              icon={<QuestionCircleOutlined />}
              onClick={() => setHelpVisible(true)}
            >
              دليل المساعدة
            </Button>
          </Space>
        </div>
      )}

      {/* الإحصائيات السريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الأوامر"
              value={_stats.total}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="في الانتّار"
              value={_stats.pending}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="قيد التنفيذ"
              value={_stats.in_progress}
              valueStyle={{ color: '#1890ff' }}
              prefix={<PlayCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="مكتملة"
              value={_stats.completed}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* شريط الأدوات */}
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex', gap: '8px' }}>
          <Tooltip title="إنشاء أمر إنتاج جديد - تأكد من وجود وصفة إنتاج للصنف المطلوب">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={async () => {
                setEditingOrder(null)
                form.resetFields()
                applyUserPreferences()

                // إعادة تحميل الوصفات للتأكد من وجود أحدث البيانات
                await loadRecipes()

                setModalVisible(true)

                // عرض تحذير إذا لم توجد وصفات
                if (recipes.length === 0) {
                  setTimeout(() => {
                    message.warning({
                      content: 'لا توجد وصفات إنتاج متاحة. يجب إنشاء وصفة إنتاج أولاً قبل إنشاء أمر إنتاج.',
                      duration: 5
                    })
                  }, 500)
                }
              }}
            >
              أمر إنتاج جديد
            </Button>
          </Tooltip>
          <Tooltip title="تحديث قائمة أوامر الإنتاج فقط">
            <Button onClick={loadOrders}>
              تحديث الأوامر
            </Button>
          </Tooltip>
          <Tooltip title="تحديث جميع البيانات: الأوامر، الأصناف، الأقسام، العملاء، والمخازن">
            <Button
              onClick={async () => {
                await Promise.all([
                  loadOrders(),
                  loadItems(),
                  loadDepartments(),
                  loadCustomers(),
                  // تم إزالة loadWarehouses لأن المخازن ستؤخذ من الوصفة
                  loadRecipes()
                ])
                message.success('تم تحديث جميع البيانات بنجاح')
              }}
              icon={<ReloadOutlined />}
            >
              تحديث شامل
            </Button>
          </Tooltip>
          <Tooltip title="دليل تفاعلي للمبتدئين - تعلم كيفية استخدام النظام خطوة بخطوة">
            <Button
              onClick={() => setHelpVisible(true)}
              icon={<QuestionCircleOutlined />}
              type="dashed"
              style={{ borderColor: '#52c41a', color: '#52c41a' }}
            >
              دليل المساعدة
            </Button>
          </Tooltip>
          <Tooltip title="تصدير جميع أوامر الإنتاج إلى ملف Excel للمراجعة أو الأرشفة">
            <Button
              icon={<FileExcelOutlined />}
              onClick={handleExportExcel}
              style={{
                backgroundColor: '#52c41a',
                borderColor: '#52c41a',
                color: 'white'
              }}
            >
              تصدير Excel
            </Button>
          </Tooltip>
          {/* زر التقرير الشامل المحسن */}
          <Tooltip title="تقرير شامل مع إحصائيات وتحليلات متقدمة">
            <UnifiedPrintButton
              data={async () => {
                try {
                  const printData = await prepareComprehensiveOrdersReport()
                  if (!printData) {
                    throw new Error('فشل في تحضير التقرير الشامل')
                  }
                  return printData
                } catch (error) {
                  Logger.error('ProductionOrdersManagement', 'خطأ في تحضير التقرير الشامل:', error)
                  throw error
                }
              }}
              type="report"
              subType="production"
              title="تقرير شامل لأوامر الإنتاج مع التحليلات"
              buttonText="التقرير الشامل"
              size="middle"
              buttonType="default"
              showDropdown={true}
              showExportOptions={true}
              showSettings={true}
              icon={<BarChartOutlined style={{ color: '#722ed1' }} />}
              customSettings={{
                showLogo: true,
                showHeader: true,
                showFooter: true,
                primaryColor: '#722ed1',
                orientation: 'landscape',
                pageSize: 'A4'
              }}
              onBeforePrint={() => {
                Logger.info('ProductionOrdersManagement', 'بدء طباعة التقرير الشامل لأوامر الإنتاج')
              }}
              onAfterPrint={() => {
                Logger.info('ProductionOrdersManagement', 'تم إكمال طباعة التقرير الشامل لأوامر الإنتاج')
              }}
              onError={(error) => {
                Logger.error('ProductionOrdersManagement', 'خطأ في طباعة التقرير الشامل:', error)
              }}
            />
          </Tooltip>

          {/* زر القائمة السريعة */}
          <Tooltip title="قائمة سريعة لأوامر الإنتاج للطباعة السريعة">
            <UnifiedPrintButton
              data={{
                title: 'قائمة سريعة - أوامر الإنتاج',
                subtitle: `إجمالي الأوامر: ${orders.length} | تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}`,

                // إضافة معلومات عامة للتقرير
                customer: {
                  name: 'تقرير شامل',
                  department: 'جميع الأقسام',
                  orderDate: new Date().toLocaleDateString('ar-SA'),
                  deliveryDate: '',
                  type: 'تقرير'
                },

                data: orders.map((order, index) => ({
                  id: index + 1,
                  order_number: order.order_number,
                  product_name: order.item_name || order.product_name || '',
                  quantity: order.quantity,
                  unit: order.unit,
                  status: getStatusText(order.status),
                  priority: getPriorityText(order.priority),
                  department_name: order.department_name,
                  customer_name: order.customer_name || 'إنتاج داخلي'
                })),
                columns: [
                  { key: 'id', title: '#', align: 'center', width: '5%' },
                  { key: 'order_number', title: 'رقم الأمر', align: 'center', width: '12%' },
                  { key: 'product_name', title: 'المنتج', align: 'right', width: '25%' },
                  { key: 'quantity', title: 'الكمية', align: 'center', format: 'number', width: '10%' },
                  { key: 'unit', title: 'الوحدة', align: 'center', width: '8%' },
                  { key: 'status', title: 'الحالة', align: 'center', width: '12%' },
                  { key: 'priority', title: 'الأولوية', align: 'center', width: '10%' },
                  { key: 'department_name', title: 'القسم', align: 'center', width: '10%' },
                  { key: 'customer_name', title: 'العميل', align: 'center', width: '8%' }
                ],
                summary: {
                  compact: true,
                  data: [
                    { label: 'إجمالي الأوامر', value: orders.length, format: 'number', icon: '📋' },
                    { label: 'أوامر مكتملة', value: orders.filter(o => o.status === 'completed').length, format: 'number', icon: '✅' },
                    { label: 'أوامر قيد التنفيذ', value: orders.filter(o => o.status === 'in_progress').length, format: 'number', icon: '🔄' },
                    { label: 'أوامر في الانتظار', value: orders.filter(o => o.status === 'pending').length, format: 'number', icon: '⏳' }
                  ]
                },
                metadata: {
                  generatedAt: new Date().toISOString(),
                  generatedBy: 'نظام الإنتاج',
                  reportType: 'قائمة سريعة - أوامر الإنتاج',
                  dateRange: 'جميع الفترات',
                  totalRecords: orders.length,
                  printSettings: {
                    orientation: 'portrait',
                    pageSize: 'A4',
                    showLogo: true,
                    showHeader: true,
                    showFooter: false,
                    primaryColor: '#13c2c2'
                  }
                }
              }}
              type="report"
              subType="production"
              title="قائمة سريعة لأوامر الإنتاج"
              buttonText="القائمة السريعة"
              size="middle"
              buttonType="default"
              showDropdown={true}
              showExportOptions={true}
              showSettings={true}
              icon={<PrinterOutlined style={{ color: '#13c2c2' }} />}
              customSettings={{
                showLogo: true,
                showHeader: true,
                showFooter: false,
                primaryColor: '#13c2c2',
                orientation: 'portrait',
                pageSize: 'A4'
              }}
              onBeforePrint={() => {
                Logger.info('ProductionOrdersManagement', 'بدء طباعة القائمة السريعة لأوامر الإنتاج')
              }}
              onAfterPrint={() => {
                Logger.info('ProductionOrdersManagement', 'تم إكمال طباعة القائمة السريعة لأوامر الإنتاج')
              }}
              onError={(error) => {
                Logger.error('ProductionOrdersManagement', 'خطأ في طباعة القائمة السريعة:', error)
              }}
            />
          </Tooltip>
          <Tooltip title="تقارير تفصيلية مع رسوم بيانية وإحصائيات متقدمة">
            <Button
              icon={<BarChartOutlined />}
              onClick={() => setShowAdvancedReports(true)}
              style={{
                backgroundColor: '#722ed1',
                borderColor: '#722ed1',
                color: 'white'
              }}
            >
              التقارير المتقدمة
            </Button>
          </Tooltip>
          <Tooltip title="فلترة الأوامر حسب التاريخ، الحالة، القسم، والعميل">
            <Button
              icon={<FilterOutlined />}
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              style={{
                backgroundColor: showAdvancedFilters ? '#52c41a' : '#1890ff',
                borderColor: showAdvancedFilters ? '#52c41a' : '#1890ff',
                color: 'white'
              }}
            >
              الفلاتر المتقدمة
            </Button>
          </Tooltip>
          <Tooltip title="عرض الأوامر في مخطط جانت الزمني لمتابعة الجدولة">
            <Button
              icon={<CalendarOutlined />}
              onClick={() => setShowGanttChart(true)}
              style={{
                backgroundColor: '#fa8c16',
                borderColor: '#fa8c16',
                color: 'white'
              }}
            >
              عرض الجانت
            </Button>
          </Tooltip>
          <Button
            icon={<BellOutlined />}
            onClick={handleShowNotifications}
            style={{
              backgroundColor: '#f5222d',
              borderColor: '#f5222d',
              color: 'white'
            }}
          >
            التنبيهات
          </Button>
          <Button
            icon={<CalendarOutlined />}
            onClick={handleShowCalendar}
            style={{
              backgroundColor: '#13c2c2',
              borderColor: '#13c2c2',
              color: 'white'
            }}
          >
            التقويم
          </Button>
        </div>
        {onBack && (
          <Button
            onClick={onBack}
            style={{ marginLeft: '8px' }}
          >
            العودة
          </Button>
        )}
      </div>

      {/* الفلاتر المتقدمة */}
      {showAdvancedFilters && (
        <ProductionAdvancedFilters
          onFiltersChange={applyFilters}
          onReset={resetFilters}
          departments={departments}
          customers={customers}
          items={items}
        />
      )}

      {/* جدول الأوامر */}
      <div style={{ position: 'relative' }}>
        {/* شرح سريع للجدول */}
        <div style={{
          marginBottom: '12px',
          padding: '8px 12px',
          backgroundColor: '#e6f7ff',
          border: '1px solid #91d5ff',
          borderRadius: '6px',
          fontSize: '13px'
        }}>
          <InfoCircleOutlined style={{ color: '#1890ff', marginRight: '6px' }} />
          <Typography.Text style={{ color: '#1890ff' }}>
            💡 <strong>نصيحة:</strong> يمكنك النقر على أي أمر لعرض التفاصيل، أو استخدام الإجراءات لبدء/إكمال الإنتاج.
            الألوان تدل على الحالة: أزرق (معلق)، برتقالي (قيد التنفيذ)، أخضر (مكتمل).
          </Typography.Text>
        </div>

        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          rowSelection={{
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys as number[]),
            type: 'checkbox'
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => 'إجمالي ' + total + ' أمر إنتاج'
          }}
        />
      </div>

      {/* نافذة إضافة/تعديل أمر الإنتاج */}
      <Modal
        title={
          <div>
            <span>{editingOrder ? 'تعديل أمر الإنتاج' : 'أمر إنتاج جديد'}</span>
            <div style={{ fontSize: '12px', color: '#666', fontWeight: 'normal', marginTop: '4px' }}>
              {!editingOrder && (
                <>
                  📝 املأ البيانات المطلوبة لإنشاء أمر إنتاج جديد
                  <span style={{ marginLeft: '10px', color: recipes.length > 0 ? '#52c41a' : '#ff4d4f' }}>
                    • الوصفات المتاحة: {recipes.length}
                  </span>
                </>
              )}
            </div>
          </div>
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setEditingOrder(null)
          form.resetFields()
          // إعادة تعيين النموذج
        }}
        footer={null}
        width={800}
      >
        {/* شرح سريع للنموذج */}
        {!editingOrder && (
          <div style={{
            marginBottom: '16px',
            padding: '12px',
            backgroundColor: '#fff7e6',
            border: '1px solid #ffd591',
            borderRadius: '6px'
          }}>
            <Typography.Text style={{ color: '#d46b08' }}>
              <InfoCircleOutlined style={{ marginRight: '6px' }} />
              <strong>خطوات إنشاء أمر الإنتاج:</strong>
            </Typography.Text>
            <ol style={{ margin: '8px 0 0 20px', color: '#d46b08' }}>
              <li>اختر الصنف المراد إنتاجه (يجب أن يكون له وصفة)</li>
              <li>حدد قسم الإنتاج المسؤول</li>
              <li>اختر المخزن الذي سيستقبل المنتج النهائي</li>
              <li>أدخل الكمية والتفاصيل الأخرى</li>
            </ol>
          </div>
        )}
        {/* قسم الإدخال السريع */}
        {!editingOrder && recentInputs.length > 0 && (
          <Card
            size="small"
            title="⚡ إدخال سريع"
            style={{ marginBottom: 16, backgroundColor: '#f0f8ff' }}
            extra={
              <Button
                size="small"
                type="link"
                onClick={() => setShowQuickInput(!showQuickInput)}
              >
                {showQuickInput ? 'إخفاء' : 'عرض'}
              </Button>
            }
          >
            {showQuickInput && (
              <Space wrap>
                {recentInputs.slice(0, 3).map((recent) => (
                  <Button
                    key={recent.id}
                    size="small"
                    onClick={() => handleQuickInput(recent)}
                    style={{
                      backgroundColor: '#e6f7ff',
                      borderColor: '#91d5ff',
                      color: '#1890ff'
                    }}
                  >
                    📋 {recent.label}
                  </Button>
                ))}
                <Button
                  size="small"
                  type="dashed"
                  onClick={handleSaveAsPreference}
                  style={{ color: '#52c41a' }}
                >
                  💾 حفّ كإعداد مفضل
                </Button>
              </Space>
            )}
          </Card>
        )}



        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          {/* معلومات رقم الأمر والكود */}
          <Row gutter={16}>
            <Col span={12}>
              {editingOrder ? (
                <Form.Item
                  name="order_number"
                  label={
                    <Space>
                      رقم الأمر
                      <Tooltip title="رقم أمر الإنتاج التلقائي">
                        <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                      </Tooltip>
                    </Space>
                  }
                >
                  <Input
                    disabled
                    style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                    prefix={<NumberOutlined />}
                  />
                </Form.Item>
              ) : (
                <div style={{
                  padding: '12px 16px',
                  backgroundColor: '#f6ffed',
                  border: '1px solid #b7eb8f',
                  borderRadius: '6px',
                  marginBottom: '16px'
                }}>
                  <Typography.Text style={{ fontSize: '14px', color: '#52c41a' }}>
                    ✅ سيتم توليد رقم الأمر تلقائياً عند الحفظ
                  </Typography.Text>
                </div>
              )}
            </Col>
            <Col span={12}>
              <Form.Item
                name="order_code"
                label={
                  <Space>
                    كود الأمر
                    <Tooltip title="كود أمر الإنتاج المميز - سيتم توليده تلقائياً">
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
              >
                {editingOrder ? (
                  <Input
                    disabled
                    style={{ backgroundColor: '#f5f5f5', color: '#666' }}
                    prefix={<NumberOutlined />}
                  />
                ) : (
                  <Input
                    placeholder="سيتم توليد الكود تلقائياً"
                    disabled
                    style={{ backgroundColor: '#f0f8ff', color: '#1890ff' }}
                    prefix={<NumberOutlined />}
                  />
                )}
              </Form.Item>
            </Col>
          </Row>

          {/* حقول النظام المحسن - الاعتماد على الوصفة */}
          {recipes.length === 0 && (
            <Alert
              message="لا توجد وصفات إنتاج متاحة"
              description="يجب إنشاء وصفة إنتاج أولاً قبل إنشاء أمر إنتاج. اضغط على زر '+' بجانب قائمة الوصفات لإنشاء وصفة جديدة."
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
              action={
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    if (window.electronAPI) {
                      window.electronAPI.openRecipesManagement()
                    } else {
                      message.info('يرجى الانتقال إلى قسم وصفات الإنتاج لإنشاء وصفة جديدة')
                    }
                  }}
                >
                  إنشاء وصفة
                </Button>
              }
            />
          )}
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="recipe_id"
                label={
                  <Space>
                    وصفة الإنتاج
                    <Tooltip title="اختر وصفة الإنتاج المطلوبة. ستحدد المنتج والمواد والقسم تلقائياً">
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: 'يرجى اختيار وصفة الإنتاج' }]}
              >
              <Space.Compact style={{ width: '100%' }}>
                <Select
                  placeholder={recipes.length > 0 ? "اختر وصفة الإنتاج المطلوبة" : "لا توجد وصفات متاحة - يرجى إنشاء وصفة أولاً"}
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false)
                  }
                  onChange={handleRecipeChange}
                  style={{ width: 'calc(100% - 40px)' }}
                  disabled={recipes.length === 0}
                  notFoundContent={
                    <div style={{ textAlign: 'center', padding: '20px' }}>
                      <div style={{ marginBottom: '10px' }}>📝 لا توجد وصفات إنتاج</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        يرجى إنشاء وصفة إنتاج أولاً من خلال الضغط على زر &quot;+&quot; أو الانتقال إلى قسم وصفات الإنتاج
                      </div>
                    </div>
                  }
                >
                  {recipes.map(recipe => (
                    <Option key={recipe.id} value={recipe.id}>
                      <div>
                        <strong>{recipe.name}</strong> ({recipe.code})
                        <br />
                        <small style={{ color: '#666' }}>
                          📦 {recipe.product_name} | 🏭 {recipe.department_name} | 💰 {recipe.estimated_cost?.toFixed(2)} ج.م
                        </small>
                      </div>
                    </Option>
                  ))}
                </Select>
                <Tooltip title="إضافة وصفة جديدة">
                  <Button
                    icon={<PlusOutlined />}
                    onClick={() => {
                      if (window.electronAPI) {
                        window.electronAPI.openRecipesManagement()
                      } else {
                        message.info('يرجى الانتقال إلى قسم وصفات الإنتاج لإنشاء وصفة جديدة')
                      }
                    }}
                    style={{ width: '40px' }}
                  />
                </Tooltip>
              </Space.Compact>
            </Form.Item>
          </Col>
            <Col span={12}>
              <Form.Item
                name="department_id"
                label={
                  <Space>
                    قسم الإنتاج
                    <Tooltip title="اختر القسم المسؤول عن إنتاج هذا الصنف">
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: 'يرجى اختيار قسم الإنتاج' }]}
              >
                <Space.Compact style={{ width: '100%' }}>
                  <Select
                    placeholder="اختر قسم الإنتاج"
                    showSearch
                    optionFilterProp="children"
                    style={{ width: 'calc(100% - 40px)' }}
                  >
                    {departments.map(dept => (
                      <Option key={dept.id} value={dept.id}>
                        {dept.name}
                      </Option>
                    ))}
                  </Select>
                  <Tooltip title="إضافة قسم جديد">
                    <Button
                      icon={<PlusOutlined />}
                      onClick={() => setDepartmentModalVisible(true)}
                      style={{ width: '40px' }}
                    />
                  </Tooltip>
                </Space.Compact>
              </Form.Item>
            </Col>
          </Row>



          {/* رسالة توضيحية حول المخازن */}
          <Row gutter={16}>
            <Col span={24}>
              <Alert
                message="معلومات المخازن"
                description="سيتم تحديد مخزن المواد الخام ومخزن المنتج التام تلقائياً من الوصفة المختارة. تأكد من أن الوصفة تحتوي على معلومات المخازن الصحيحة."
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="customer_id"
                label={
                  <Space>
                    العميل (اختياري)
                    <Tooltip title="اختر العميل إذا كان الإنتاج لعميل محدد، أو اتركه فارغاً للإنتاج الداخلي">
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
              >
                <Select
                  placeholder="اختر العميل أو اتركه فارغاً للإنتاج الداخلي"
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false)
                  }
                >
                  {customers.map(customer => (
                    <Option key={customer.id} value={customer.id}>
                      {customer.name} ({customer.code || ('CUS' + customer.id.toString().padStart(3, '0'))})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="order_date"
                label={
                  <Space>
                    تاريخ الأمر
                    <Tooltip title="تاريخ إنشاء أمر الإنتاج">
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: 'يرجى تحديد تاريخ الأمر' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="اختر تاريخ الأمر"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="quantity"
                label={
                  <Space>
                    الكمية
                    <Tooltip title="أدخل الكمية المطلوب إنتاجها">
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: 'يرجى إدخال الكمية' }]}
              >
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                  placeholder="مثال: 10"
                  onChange={handleQuantityChange}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="unit"
                label={
                  <Space>
                    الوحدة
                    <Tooltip title="حدد وحدة القياس (قطعة، متر، كيلو، إلخ)">
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: 'يرجى إدخال الوحدة' }]}
              >
                <Select placeholder="اختر الوحدة">
                  <Option value="قطعة">قطعة</Option>
                  <Option value="متر">متر</Option>
                  <Option value="متر مربع">متر مربع</Option>
                  <Option value="متر مكعب">متر مكعب</Option>
                  <Option value="كيلو">كيلو</Option>
                  <Option value="طن">طن</Option>
                  <Option value="لتر">لتر</Option>
                  <Option value="علبة">علبة</Option>
                  <Option value="كرتون">كرتون</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <div style={{
                padding: '12px 16px',
                backgroundColor: '#f0f9ff',
                border: '1px solid #91d5ff',
                borderRadius: '6px',
                marginBottom: '16px'
              }}>
                <Typography.Text style={{ fontSize: '14px', color: '#1890ff' }}>
                  ℹ️ سيتم تحديد المخازن تلقائياً من الوصفة المختارة
                </Typography.Text>
              </div>
            </Col>
          </Row>



          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="priority"
                label={
                  <Space>
                    الأولوية
                    <Popover
                      content={
                        <div style={{ maxWidth: '250px' }}>
                          <p><strong>مستويات الأولوية:</strong></p>
                          <p>🟢 <strong>منخفضة:</strong> إنتاج عادي بدون استعجال</p>
                          <p>🔵 <strong>عادية:</strong> إنتاج ضمن الجدولة العادية</p>
                          <p>🟠 <strong>عالية:</strong> إنتاج مستعجل</p>
                          <p>🔴 <strong>عاجلة:</strong> إنتاج فوري - أولوية قصوى</p>
                        </div>
                      }
                      title="مستويات الأولوية"
                      trigger="hover"
                    >
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Popover>
                  </Space>
                }
                initialValue="normal"
              >
                <Select>
                  <Option value="low">🟢 منخفضة</Option>
                  <Option value="normal">🔵 عادية</Option>
                  <Option value="high">🟠 عالية</Option>
                  <Option value="urgent">🔴 عاجلة</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="estimated_cost"
                label={
                  <Space>
                    التكلفة المقدرة
                    <Tooltip title="التكلفة المتوقعة لإنتاج هذا الأمر (شاملة المواد والعمالة)">
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  style={{ width: '100%' }}
                  addonAfter="₪"
                  placeholder="0.00"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="estimated_hours"
                label={
                  <Space>
                    الساعات المقدرة
                    <Tooltip title="عدد ساعات العمل المتوقعة لإنجاز هذا الأمر">
                      <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
              >
                <InputNumber
                  min={0}
                  step={0.5}
                  style={{ width: '100%' }}
                  addonAfter="ساعة"
                  placeholder="0.0"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="expected_completion_date"
            label={
              <Space>
                تاريخ الإنجاز المتوقع
                <Tooltip title="التاريخ المتوقع لإنجاز أمر الإنتاج">
                  <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
          >
            <DatePicker
              style={{ width: '100%' }}
              placeholder="اختر تاريخ الإنجاز المتوقع"
              disabledDate={(current) => current && current < dayjs().startOf('day')}
            />
          </Form.Item>

          <Form.Item
            name="notes"
            label={
              <Space>
                ملاحّات
                <Tooltip title="أضف أي ملاحّات أو تعليمات خاصة لفريق الإنتاج">
                  <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                </Tooltip>
              </Space>
            }
          >
            <TextArea
              rows={3}
              placeholder="ملاحّات إضافية أو تعليمات خاصة لفريق الإنتاج..."
              maxLength={1000}
              showCount
            />
          </Form.Item>

          {/* مكون رفع الصور - يظهر فقط عند التعديل */}
          {editingOrder && (
            <Form.Item
              label={
                <Space>
                  <PictureOutlined />
                  صور أمر الإنتاج
                  <Tooltip title="يمكنك رفع حتى 5 صور لتوثيق أمر الإنتاج">
                    <QuestionCircleOutlined style={{ color: '#1890ff' }} />
                  </Tooltip>
                </Space>
              }
            >
              <SimpleImageManager
                category="production"
                contextId={editingOrder.id}
                maxImages={5}
                allowMultiple={true}
                showDescription={true}
                showPrimaryButton={false}
                metadata={{
                  orderNumber: editingOrder.order_number,
                  orderCode: editingOrder.order_code
                }}
              />
            </Form.Item>
          )}

          {/* رسالة توضيحية للصور عند الإنشاء */}
          {!editingOrder && (
            <Alert
              message="رفع الصور"
              description="يمكنك رفع الصور بعد إنشاء أمر الإنتاج من خلال تعديل الأمر أو من خلال نافذة التفاصيل."
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

          {/* رسالة تحذيرية عندما لا توجد وصفات */}
          {!editingOrder && recipes.length === 0 && (
            <Alert
              message="تحذير: لا يمكن إنشاء أمر إنتاج"
              description="لا توجد وصفات إنتاج متاحة. يجب إنشاء وصفة إنتاج أولاً قبل إنشاء أمر إنتاج. الوصفة تحدد المنتج النهائي والمواد المطلوبة والقسم المسؤول."
              type="error"
              showIcon
              style={{ marginBottom: 16 }}
              action={
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    if (window.electronAPI) {
                      window.electronAPI.openRecipesManagement()
                    } else {
                      message.info('يرجى الانتقال إلى قسم وصفات الإنتاج لإنشاء وصفة جديدة')
                    }
                  }}
                >
                  إنشاء وصفة الآن
                </Button>
              }
            />
          )}

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                disabled={!editingOrder && recipes.length === 0}
              >
                {editingOrder ? 'تحديث' : 'إنشاء'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false)
                setEditingOrder(null)
                form.resetFields()
              }}>
                إلغاء
              </Button>
              {!editingOrder && recipes.length === 0 && (
                <Button
                  type="dashed"
                  onClick={() => {
                    if (window.electronAPI) {
                      window.electronAPI.openRecipesManagement()
                    } else {
                      message.info('يرجى الانتقال إلى قسم وصفات الإنتاج لإنشاء وصفة جديدة')
                    }
                  }}
                >
                  إنشاء وصفة أولاً
                </Button>
              )}
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Modal عرض تفاصيل أمر الإنتاج */}
      <ProductionOrderDetailsModal
        visible={detailsModalVisible}
        onClose={() => {
          setDetailsModalVisible(false)
          setSelectedOrderId(null)
        }}
        orderId={selectedOrderId}
      />



      {/* التقارير المتقدمة */}
      {showAdvancedReports && (
        <Modal
          title="التقارير المتقدمة لأوامر الإنتاج"
          open={showAdvancedReports}
          onCancel={() => setShowAdvancedReports(false)}
          footer={null}
          width="95%"
          style={{ top: 20 }}
        >
          <ProductionAdvancedReports onBack={() => setShowAdvancedReports(false)} />
        </Modal>
      )}

      {/* عرض الجانت */}
      {showGanttChart && (
        <Modal
          title="الجدول الزمني لأوامر الإنتاج (Gantt Chart)"
          open={showGanttChart}
          onCancel={() => setShowGanttChart(false)}
          footer={null}
          width="98%"
          style={{ top: 10 }}
        >
          <ProductionGanttChart
            orders={orders}
            onBack={() => setShowGanttChart(false)}
          />
        </Modal>
      )}

      {/* مودال التنبيهات */}
      <Modal
        title="إدارة التنبيهات"
        open={notificationsModalVisible}
        onCancel={() => setNotificationsModalVisible(false)}
        footer={null}
        width={800}
      >
        <div>
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={8}>
              <Card size="small">
                <Statistic
                  title="التنبيهات غير المقروءة"
                  value={furnitureNotificationService.getUnreadNotifications().length}
                  valueStyle={{ color: '#f5222d' }}
                  prefix={<BellOutlined />}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small">
                <Statistic
                  title="التنبيهات المعلقة"
                  value={furnitureNotificationService.getPendingNotifications().length}
                  valueStyle={{ color: '#fa8c16' }}
                  prefix={<ClockCircleOutlined />}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small">
                <Statistic
                  title="إجمالي التنبيهات"
                  value={furnitureNotificationService.getAllNotifications().length}
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<NotificationOutlined />}
                />
              </Card>
            </Col>
          </Row>

          <div style={{ marginBottom: 16 }}>
            <Space>
              <Button
                type="primary"
                onClick={() => furnitureNotificationService.markAllAsRead()}
              >
                تحديد الكل كمقروء
              </Button>
              <Button
                onClick={() => setCustomNotificationModalVisible(true)}
              >
                إضافة تنبيه مخصص
              </Button>
            </Space>
          </div>

          <div style={{ maxHeight: 400, overflowY: 'auto' }}>
            {furnitureNotificationService.getAllNotifications().map(notification => (
              <Card
                key={notification.id}
                size="small"
                style={{
                  marginBottom: 8,
                  backgroundColor: notification.isRead ? '#f5f5f5' : '#fff2e8'
                }}
                extra={
                  <Space>
                    <Tag color={
                      notification.priority === 'urgent' ? 'red' :
                      notification.priority === 'high' ? 'orange' :
                      notification.priority === 'medium' ? 'blue' : 'green'
                    }>
                      {notification.priority}
                    </Tag>
                    {!notification.isRead && (
                      <Button
                        size="small"
                        onClick={() => furnitureNotificationService.markAsRead(notification.id)}
                      >
                        تحديد كمقروء
                      </Button>
                    )}
                  </Space>
                }
              >
                <div>
                  <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                    {notification.title}
                  </div>
                  <div style={{ marginBottom: 4 }}>
                    {notification.message}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {notification.scheduledTime.toLocaleString('ar-SA')}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </Modal>

      {/* مودال التقويم */}
      <Modal
        title="تقويم أوامر الإنتاج"
        open={calendarModalVisible}
        onCancel={() => setCalendarModalVisible(false)}
        footer={null}
        width={1000}
      >
        <div>
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="أحداث اليوم"
                  value={furnitureCalendarService.getEventsByDate(new Date()).length}
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<CalendarOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="أحداث هذا الأسبوع"
                  value={furnitureCalendarService.getThisWeekEvents().length}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<CalendarOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="المواعيد القادمة"
                  value={furnitureCalendarService.getUpcomingEvents().length}
                  valueStyle={{ color: '#fa8c16' }}
                  prefix={<ClockCircleOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="المواعيد المتأخرة"
                  value={furnitureCalendarService.getOverdueEvents().length}
                  valueStyle={{ color: '#f5222d' }}
                  prefix={<ExclamationCircleOutlined />}
                />
              </Card>
            </Col>
          </Row>

          <div style={{ maxHeight: 500, overflowY: 'auto' }}>
            {furnitureCalendarService.getAllEvents().map(event => (
              <Card
                key={event.id}
                size="small"
                style={{ marginBottom: 8 }}
                extra={
                  <Space>
                    <Tag color={
                      event.type === 'deadline' ? 'red' :
                      event.type === 'production_order' ? 'blue' : 'green'
                    }>
                      {event.type}
                    </Tag>
                    <Tag color={
                      event.status === 'completed' ? 'green' :
                      event.status === 'in_progress' ? 'blue' :
                      event.status === 'cancelled' ? 'red' : 'orange'
                    }>
                      {event.status}
                    </Tag>
                  </Space>
                }
              >
                <div>
                  <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                    {event.title}
                  </div>
                  <div style={{ marginBottom: 4 }}>
                    {event.description}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    من: {event.start.toLocaleString('ar-SA')} إلى: {event.end.toLocaleString('ar-SA')}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </Modal>

      {/* مودال إضافة تنبيه مخصص */}
      <Modal
        title="إضافة تنبيه مخصص"
        open={customNotificationModalVisible}
        onCancel={() => {
          setCustomNotificationModalVisible(false)
          setSelectedOrderForNotification(null)
        }}
        onOk={() => {
          const form = document.getElementById('custom-notification-form') as HTMLFormElement
          if (form) {
            const formData = new FormData(form)
            const title = formData.get('title') as string
            const notificationMessage = formData.get('message') as string
            const scheduledTime = new Date(formData.get('scheduledTime') as string)
            const priority = formData.get('priority') as 'low' | 'medium' | 'high' | 'urgent'

            furnitureNotificationService.addCustomNotification(
              title,
              notificationMessage,
              scheduledTime,
              priority,
              selectedOrderForNotification?.id.toString(),
              selectedOrderForNotification?.order_number
            )

            message.success('تم إضافة التنبيه بنجاح')
            setCustomNotificationModalVisible(false)
            setSelectedOrderForNotification(null)
          }
        }}
      >
        <form id="custom-notification-form">
          <div style={{ marginBottom: 16 }}>
            <label>عنوان التنبيه:</label>
            <Input name="title" placeholder="أدخل عنوان التنبيه" style={{ marginTop: 4 }} />
          </div>
          <div style={{ marginBottom: 16 }}>
            <label>رسالة التنبيه:</label>
            <TextArea name="message" rows={3} placeholder="أدخل رسالة التنبيه" style={{ marginTop: 4 }} />
          </div>
          <div style={{ marginBottom: 16 }}>
            <label>وقت التنبيه:</label>
            <Input
              name="scheduledTime"
              type="datetime-local"
              style={{ marginTop: 4 }}
              defaultValue={dayjs().add(1, 'hour').format('YYYY-MM-DDTHH:mm')}
            />
          </div>
          <div style={{ marginBottom: 16 }}>
            <label>الأولوية:</label>
            <Select defaultValue="medium" style={{ width: '100%', marginTop: 4 }}>
              <Option value="low">منخفضة</Option>
              <Option value="medium">متوسطة</Option>
              <Option value="high">عالية</Option>
              <Option value="urgent">عاجلة</Option>
            </Select>
          </div>
          {selectedOrderForNotification ? (
            <div style={{ backgroundColor: '#f0f8ff', padding: 16, borderRadius: 8, marginTop: 16 }}>
              <Typography.Title level={5} style={{ margin: 0, marginBottom: 12 }}>
                <InfoCircleOutlined style={{ marginRight: 8 }} />
                معلومات أمر الإنتاج
              </Typography.Title>
              <Divider style={{ margin: '12px 0' }} />
              <Row gutter={[16, 8]}>
                <Col span={12}>
                  <Typography.Text type="secondary">رقم الأمر:</Typography.Text>
                  <br />
                  <Typography.Text strong copyable>{selectedOrderForNotification.order_number}</Typography.Text>
                </Col>
                <Col span={12}>
                  <Typography.Text type="secondary">المنتج:</Typography.Text>
                  <br />
                  <Typography.Text strong>{selectedOrderForNotification.product_name}</Typography.Text>
                </Col>
                <Col span={12}>
                  <Typography.Text type="secondary">الكمية:</Typography.Text>
                  <br />
                  <Typography.Text strong>
                    {selectedOrderForNotification.quantity} {selectedOrderForNotification.unit}
                  </Typography.Text>
                </Col>
                <Col span={12}>
                  <Typography.Text type="secondary">القسم:</Typography.Text>
                  <br />
                  <Typography.Text strong>{selectedOrderForNotification.department_name}</Typography.Text>
                </Col>
              </Row>
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: 40 }}>
              <Typography.Text type="secondary">
                <ExclamationCircleOutlined style={{ marginRight: 8 }} />
                لم يتم تحديد أمر إنتاج
              </Typography.Text>
            </div>
          )}
        </form>
      </Modal>

      {/* مودال معاينة الصور */}
      <Modal
        open={imagePreviewVisible}
        title="معاينة الصورة"
        footer={null}
        onCancel={() => setImagePreviewVisible(false)}
        width={800}
      >
        <Image
          width="100%"
          src={previewImageUrl}
          alt="معاينة الصورة"
        />
      </Modal>

      {/* مودال إدارة الصور */}
      <Modal
        title={`إدارة صور أمر الإنتاج ${selectedOrderForNotification?.order_number || ''}`}
        open={imageUploadModalVisible}
        onCancel={() => {
          setImageUploadModalVisible(false)
          setSelectedOrderForNotification(null)
        }}
        footer={null}
        width={900}
      >
        {selectedOrderForNotification && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <SimpleImageManager
                category="production"
                contextId={selectedOrderForNotification.id}
                maxImages={5}
                allowMultiple={true}
                showDescription={true}
                showPrimaryButton={false}
                metadata={{
                  orderNumber: selectedOrderForNotification.order_number,
                  orderCode: selectedOrderForNotification.order_code
                }}
              />
            </div>

            <div style={{ backgroundColor: '#f0f8ff', padding: 12, borderRadius: 6 }}>
              <Typography.Text strong>معلومات أمر الإنتاج:</Typography.Text>
              <div style={{ marginTop: 8 }}>
                <div>رقم الأمر: {selectedOrderForNotification.order_number}</div>
                <div>المنتج: {selectedOrderForNotification.product_name}</div>
                <div>الكمية: {selectedOrderForNotification.quantity} {selectedOrderForNotification.unit}</div>
                <div>القسم: {selectedOrderForNotification.department_name}</div>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* نافذة الشروحات التفاعلية */}
      <Modal
        title={
          <div style={{ textAlign: 'center' }}>
            <span style={{ fontSize: '24px', marginRight: '8px' }}>
              {helpSteps[currentHelpStep]?.icon}
            </span>
            دليل المساعدة التفاعلي
          </div>
        }
        open={helpVisible}
        onCancel={() => {
          setHelpVisible(false)
          setCurrentHelpStep(0)
        }}
        footer={[
          <Button
            key="prev"
            onClick={() => setCurrentHelpStep(Math.max(0, currentHelpStep - 1))}
            disabled={currentHelpStep === 0}
          >
            السابق
          </Button>,
          <Button
            key="next"
            type="primary"
            onClick={() => {
              if (currentHelpStep < helpSteps.length - 1) {
                setCurrentHelpStep(currentHelpStep + 1)
              } else {
                setHelpVisible(false)
                setCurrentHelpStep(0)
              }
            }}
          >
            {currentHelpStep < helpSteps.length - 1 ? 'التالي' : 'إنهاء'}
          </Button>
        ]}
        width={700}
        centered
      >
        <div style={{ padding: '20px 0' }}>
          {/* شريط التقدم */}
          <div style={{ marginBottom: '24px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
              <Typography.Text strong>الخطوة {currentHelpStep + 1} من {helpSteps.length}</Typography.Text>
              <Typography.Text type="secondary">
                {Math.round(((currentHelpStep + 1) / helpSteps.length) * 100)}%
              </Typography.Text>
            </div>
            <div style={{
              width: '100%',
              height: '6px',
              backgroundColor: '#f0f0f0',
              borderRadius: '3px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${((currentHelpStep + 1) / helpSteps.length) * 100}%`,
                height: '100%',
                backgroundColor: '#1890ff',
                transition: 'width 0.3s ease'
              }} />
            </div>
          </div>

          {/* محتوى الخطوة الحالية */}
          <div style={{ textAlign: 'center', marginBottom: '24px' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>
              {helpSteps[currentHelpStep]?.icon}
            </div>
            <Typography.Title level={3} style={{ marginBottom: '12px' }}>
              {helpSteps[currentHelpStep]?.title}
            </Typography.Title>
            <Typography.Paragraph style={{ fontSize: '16px', lineHeight: '1.6' }}>
              {helpSteps[currentHelpStep]?.content}
            </Typography.Paragraph>
          </div>

          {/* النصائح */}
          <div style={{
            backgroundColor: '#f6ffed',
            border: '1px solid #b7eb8f',
            borderRadius: '8px',
            padding: '16px'
          }}>
            <Typography.Text strong style={{ color: '#52c41a', marginBottom: '12px', display: 'block' }}>
              💡 نصائح مفيدة:
            </Typography.Text>
            <ul style={{ margin: 0, paddingLeft: '20px' }}>
              {helpSteps[currentHelpStep]?.tips.map((tip, index) => (
                <li key={index} style={{ marginBottom: '8px', color: '#52c41a' }}>
                  {tip}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </Modal>

      {/* نوافذ الوصول السريع */}
      {/* نافذة إضافة منتج جديد */}
      <Modal
        title="إنشاء منتج نهائي جديد"
        open={newProductModalVisible}
        onCancel={() => {
          setNewProductModalVisible(false)
          newProductForm.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={newProductForm}
          layout="vertical"
          onFinish={handleCreateNewProduct}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="كود المنتج"
                rules={[{ required: true, message: 'يرجى إدخال كود المنتج' }]}
              >
                <Input placeholder="أدخل كود المنتج" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="اسم المنتج"
                rules={[{ required: true, message: 'يرجى إدخال اسم المنتج' }]}
              >
                <Input placeholder="أدخل اسم المنتج" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category_id"
                label="الفئة"
                rules={[{ required: true, message: 'يرجى اختيار الفئة' }]}
              >
                <Select placeholder="اختر الفئة">
                  {categories.map(category => (
                    <Option key={category.id} value={category.id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="warehouse_id"
                label="المخزن الافتراضي"
                rules={[{ required: true, message: 'يرجى اختيار المخزن' }]}
              >
                <Select placeholder="اختر المخزن">
                  {warehouses.map(warehouse => (
                    <Option key={warehouse.id} value={warehouse.id}>
                      {warehouse.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Alert
                message="ملاحظة مهمة"
                description="سعر التكلفة وسعر البيع للمنتجات النهائية سيتم حسابهما تلقائياً بناءً على تكلفة المواد والوصفات. لا تحتاج لإدخالهما يدوياً."
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="الوصف"
          >
            <TextArea rows={3} placeholder="وصف المنتج..." />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                إنشاء المنتج
              </Button>
              <Button onClick={() => {
                setNewProductModalVisible(false)
                newProductForm.resetFields()
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نافذة إضافة صنف جديد (النافذة القديمة للمرجع) */}
      <Modal
        title="إضافة صنف جديد"
        open={itemModalVisible}
        onCancel={() => setItemModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setItemModalVisible(false)}>
            إلغاء
          </Button>,
          <Button key="open" type="primary" onClick={() => {
            setItemModalVisible(false)
            // فتح صفحة إدارة الأصناف في نافذة جديدة
            window.open('#/inventory/items', '_blank')
          }}>
            فتح إدارة الأصناف
          </Button>
        ]}
        width={500}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <InfoCircleOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
          <Typography.Title level={4}>إضافة صنف جديد</Typography.Title>
          <Typography.Text>
            سيتم فتح صفحة إدارة الأصناف في نافذة جديدة حيث يمكنك إضافة الصنف الجديد.
            <br />
            بعد الإضافة، ارجع إلى هذه النافذة وقم بتحديث القائمة.
          </Typography.Text>
          <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#f6ffed', borderRadius: '6px' }}>
            <Typography.Text type="success">
              💡 نصيحة: لا تنس إنشاء وصفة إنتاج للصنف الجديد حتى يظهر في قائمة أوامر الإنتاج
            </Typography.Text>
          </div>
        </div>
      </Modal>

      {/* تم إزالة نافذة إضافة مخزن لأن المخازن ستؤخذ من الوصفة */}

      {/* نافذة إضافة قسم جديد */}
      <Modal
        title="إضافة قسم إنتاج جديد"
        open={departmentModalVisible}
        onCancel={() => setDepartmentModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setDepartmentModalVisible(false)}>
            إلغاء
          </Button>,
          <Button key="open" type="primary" onClick={() => {
            setDepartmentModalVisible(false)
            // فتح صفحة إدارة أقسام الإنتاج في نافذة جديدة
            window.open('#/production/departments', '_blank')
          }}>
            فتح إدارة الأقسام
          </Button>
        ]}
        width={500}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <InfoCircleOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
          <Typography.Title level={4}>إضافة قسم إنتاج جديد</Typography.Title>
          <Typography.Text>
            سيتم فتح صفحة إدارة أقسام الإنتاج في نافذة جديدة حيث يمكنك إضافة القسم الجديد.
            <br />
            بعد الإضافة، ارجع إلى هذه النافذة وقم بتحديث القائمة.
          </Typography.Text>
        </div>
      </Modal>
    </div>
  )
}

export default ProductionOrdersManagement
