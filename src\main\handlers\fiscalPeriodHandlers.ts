import { ipcMain } from 'electron'
import { FiscalPeriodService } from '../services/FiscalPeriodService'
import { ClosingValidationService } from '../services/ClosingValidationService'
import { ClosingBackupService } from '../services/ClosingBackupService'
import { ClosingReportService } from '../services/ClosingReportService'
import { Logger } from '../utils/logger'

// تأخير تهيئة الخدمات حتى يتم استدعاؤها لتجنب مشاكل التهيئة
let fiscalPeriodService: FiscalPeriodService | null = null
let validationService: ClosingValidationService | null = null
let backupService: ClosingBackupService | null = null
let reportService: ClosingReportService | null = null

// دالة للحصول على الخدمات مع التهيئة الآمنة
function getServices() {
  if (!fiscalPeriodService) {
    fiscalPeriodService = FiscalPeriodService.getInstance()
  }
  if (!validationService) {
    validationService = ClosingValidationService.getInstance()
  }
  if (!backupService) {
    backupService = ClosingBackupService.getInstance()
  }
  if (!reportService) {
    reportService = ClosingReportService.getInstance()
  }

  return {
    fiscalPeriodService,
    validationService,
    backupService,
    reportService
  }
}

/**
 * معالجات IPC لنظام الإقفال المحاسبي
 */

// دالة تسجيل معالجات الفترات المالية
export function registerFiscalPeriodHandlers() {
  Logger.info('FiscalPeriodHandlers', '🔧 بدء تسجيل معالجات الفترات المالية...')

  // جميع المعالجات مسجلة بالفعل في هذا الملف عند تحميله
  // لا حاجة لتسجيل إضافي

  Logger.info('FiscalPeriodHandlers', '✅ تم تسجيل جميع معالجات الفترات المالية بنجاح')
}

// الحصول على جميع الفترات المالية
ipcMain.handle('get-fiscal-periods', async () => {
  try {
    const { fiscalPeriodService } = getServices()
    return await fiscalPeriodService.getFiscalPeriods()
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في جلب الفترات المالية:', error)
    return { success: false, message: 'حدث خطأ في جلب الفترات المالية' }
  }
})

// الحصول على الفترة المالية الحالية
ipcMain.handle('get-current-fiscal-period', async () => {
  try {
    const { fiscalPeriodService } = getServices()
    return await fiscalPeriodService.getCurrentFiscalPeriod()
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في جلب الفترة المالية الحالية:', error)
    return { success: false, message: 'حدث خطأ في جلب الفترة المالية الحالية' }
  }
})

// إنشاء فترة مالية جديدة
ipcMain.handle('create-fiscal-period', async (_, periodData: any, userId?: number) => {
  try {
    const { fiscalPeriodService } = getServices()
    return await fiscalPeriodService.createFiscalPeriod(periodData, userId)
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في إنشاء الفترة المالية:', error)
    return { success: false, message: 'حدث خطأ في إنشاء الفترة المالية' }
  }
})

// تحديث فترة مالية
ipcMain.handle('update-fiscal-period', async (_, periodId: number, updateData: any, userId?: number) => {
  try {
    const { fiscalPeriodService } = getServices()
    return await fiscalPeriodService.updateFiscalPeriod(periodId, updateData, userId)
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في تحديث الفترة المالية:', error)
    return { success: false, message: 'حدث خطأ في تحديث الفترة المالية' }
  }
})

// التحقق من إمكانية إقفال الفترة
ipcMain.handle('validate-period-closing', async (_, periodId: number) => {
  try {
    const { fiscalPeriodService } = getServices()
    return await fiscalPeriodService.validatePeriodClosing(periodId)
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في التحقق من إمكانية الإقفال:', error)
    return { success: false, message: 'حدث خطأ في التحقق من إمكانية الإقفال' }
  }
})

// التحقق من إمكانية إقفال الفترة (handler بديل)
ipcMain.handle('fiscal-period:validate-for-closing', async (_, periodId: number) => {
  try {
    const { fiscalPeriodService } = getServices()
    return await fiscalPeriodService.validatePeriodClosing(periodId)
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في التحقق من إمكانية الإقفال:', error)
    return { success: false, message: 'حدث خطأ في التحقق من إمكانية الإقفال' }
  }
})

// إقفال الفترة المالية
ipcMain.handle('close-fiscal-period', async (_, periodId: number, userId: number) => {
  try {
    const { fiscalPeriodService } = getServices()
    return await fiscalPeriodService.closeFiscalPeriod(periodId, userId)
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في إقفال الفترة المالية:', error)
    return { success: false, message: 'حدث خطأ في إقفال الفترة المالية' }
  }
})

// إعادة فتح الفترة المالية
ipcMain.handle('reopen-fiscal-period', async (_, periodId: number, userId: number, reason?: string) => {
  try {
    const { fiscalPeriodService } = getServices()
    return await fiscalPeriodService.reopenFiscalPeriod(periodId, userId, reason)
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في إعادة فتح الفترة المالية:', error)
    return { success: false, message: 'حدث خطأ في إعادة فتح الفترة المالية' }
  }
})

// الحصول على تقرير الإقفال
ipcMain.handle('get-closing-report', async (_, periodId: number) => {
  try {
    const { fiscalPeriodService } = getServices()
    return await fiscalPeriodService.getClosingReport(periodId)
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في جلب تقرير الإقفال:', error)
    return { success: false, message: 'حدث خطأ في جلب تقرير الإقفال' }
  }
})

// التحقق من حماية البيانات
ipcMain.handle('check-data-protection', async (_, periodId: number, tableName: string, recordId: number) => {
  try {
    const { fiscalPeriodService } = getServices()
    const isProtected = await fiscalPeriodService.checkDataProtection(periodId, tableName, recordId)
    return { success: true, data: { isProtected } }
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في التحقق من حماية البيانات:', error)
    return { success: false, message: 'حدث خطأ في التحقق من حماية البيانات' }
  }
})

// إضافة حماية للبيانات
ipcMain.handle('add-data-protection', async (_, periodId: number, tableName: string, recordId: number, protectionLevel: string) => {
  try {
    const { fiscalPeriodService } = getServices()
    return await fiscalPeriodService.addDataProtection(periodId, tableName, recordId, protectionLevel as any)
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في إضافة حماية البيانات:', error)
    return { success: false, message: 'حدث خطأ في إضافة حماية البيانات' }
  }
})

// حذف فترة مالية
ipcMain.handle('delete-fiscal-period', async (_, periodId: number, userId: number) => {
  try {
    const { fiscalPeriodService } = getServices()
    return await fiscalPeriodService.deleteFiscalPeriod(periodId, userId)
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في حذف الفترة المالية:', error)
    return { success: false, message: 'حدث خطأ في حذف الفترة المالية' }
  }
})

// معالجات إضافية لإحصائيات الإقفال
ipcMain.handle('get-closing-statistics', async () => {
  try {
    // إحصائيات عامة عن الفترات المالية
    const stats = {
      totalPeriods: 0,
      openPeriods: 0,
      closedPeriods: 0,
      lockedPeriods: 0,
      currentPeriod: null
    }

    // يمكن إضافة المزيد من الإحصائيات هنا
    return { success: true, data: stats }
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في جلب إحصائيات الإقفال:', error)
    return { success: false, message: 'حدث خطأ في جلب إحصائيات الإقفال' }
  }
})

// معالج لجلب الفترات المالية حسب النوع
ipcMain.handle('get-fiscal-periods-by-type', async (_, periodType: string) => {
  try {
    // يمكن إضافة هذه الوظيفة لاحقاً في الخدمة
    return { success: true, data: [] }
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في جلب الفترات حسب النوع:', error)
    return { success: false, message: 'حدث خطأ في جلب الفترات حسب النوع' }
  }
})

// معالج لجلب الفترات المالية في نطاق زمني
ipcMain.handle('get-fiscal-periods-by-date-range', async (_, startDate: string, endDate: string) => {
  try {
    // يمكن إضافة هذه الوظيفة لاحقاً في الخدمة
    return { success: true, data: [] }
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في جلب الفترات حسب النطاق الزمني:', error)
    return { success: false, message: 'حدث خطأ في جلب الفترات حسب النطاق الزمني' }
  }
})

// معالجات خدمة التحقق من صحة البيانات
ipcMain.handle('validate-closing-data', async (_, startDate: string, endDate: string) => {
  try {
    const { validationService } = getServices()
    const validation = await validationService.validateForClosing(startDate, endDate)
    return { success: true, data: validation }
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في التحقق من صحة البيانات:', error)
    return { success: false, message: 'حدث خطأ في التحقق من صحة البيانات' }
  }
})

// ملاحظة: معالج ميزان المراجعة موجود في financialHandlers.ts

// معالج لإنشاء تقرير التحقق التفصيلي
ipcMain.handle('generate-validation-report', async (_, startDate: string, endDate: string) => {
  try {
    const { validationService } = getServices()
    const report = await validationService.generateValidationReport(startDate, endDate)
    return { success: true, data: report }
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في إنشاء تقرير التحقق:', error)
    return { success: false, message: 'حدث خطأ في إنشاء تقرير التحقق' }
  }
})

// معالجات خدمة النسخ الاحتياطية
ipcMain.handle('create-closing-backup', async (_, periodId: number, closingType: string, description?: string) => {
  try {
    const { backupService } = getServices()
    const backup = await backupService.createClosingBackup(periodId, closingType as any, description)
    return backup
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في إنشاء النسخة الاحتياطية:', error)
    return { success: false, message: 'حدث خطأ في إنشاء النسخة الاحتياطية' }
  }
})

// معالج للحصول على النسخ الاحتياطية للإقفال
ipcMain.handle('get-closing-backups', async (_, periodId?: number) => {
  try {
    const { backupService } = getServices()
    const backups = await backupService.getClosingBackups(periodId)
    return { success: true, data: backups }
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في جلب النسخ الاحتياطية:', error)
    return { success: false, message: 'حدث خطأ في جلب النسخ الاحتياطية' }
  }
})

// معالج للتحقق من سلامة النسخة الاحتياطية
ipcMain.handle('verify-backup-integrity', async (_, backupId: number) => {
  try {
    const { backupService } = getServices()
    const verification = await backupService.verifyBackupIntegrity(backupId)
    return { success: true, data: verification }
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في التحقق من سلامة النسخة الاحتياطية:', error)
    return { success: false, message: 'حدث خطأ في التحقق من سلامة النسخة الاحتياطية' }
  }
})

// معالجات خدمة التقارير
ipcMain.handle('generate-closing-report', async (_, periodId: number) => {
  try {
    const { reportService } = getServices()
    const report = await reportService.generateClosingReport(periodId)
    return { success: true, data: report }
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في إنشاء تقرير الإقفال:', error)
    return { success: false, message: 'حدث خطأ في إنشاء تقرير الإقفال' }
  }
})

// معالج لتقرير مقارنة الفترات
ipcMain.handle('generate-period-comparison-report', async (_, currentPeriodId: number, previousPeriodId?: number) => {
  try {
    const { reportService } = getServices()
    const report = await reportService.generatePeriodComparisonReport(currentPeriodId, previousPeriodId)
    return { success: true, data: report }
  } catch (error) {
    Logger.error('FiscalPeriodHandlers', 'خطأ في إنشاء تقرير مقارنة الفترات:', error)
    return { success: false, message: 'حدث خطأ في إنشاء تقرير مقارنة الفترات' }
  }
})

// ملاحظة: معالج تنظيف النسخ الاحتياطية القديمة موجود في systemHandlers.ts

Logger.info('FiscalPeriodHandlers', '✅ تم تحميل معالجات نظام الإقفال المحاسبي')

export default {
  // تصدير المعالجات للاستخدام في أماكن أخرى إذا لزم الأمر
}
