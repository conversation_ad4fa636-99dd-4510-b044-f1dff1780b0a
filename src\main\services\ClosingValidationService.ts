// import { Database } from 'better-sqlite3' // لم نعد نستخدم better-sqlite3
import { DatabaseService } from './DatabaseService'
import { Logger } from '../utils/logger'

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  details: any
}

export interface TrialBalanceResult {
  balanced: boolean
  totalDebits: number
  totalCredits: number
  difference: number
  accounts: Array<{
    account_code: string
    account_name: string
    debit_balance: number
    credit_balance: number
  }>
}

export interface ReconciliationStatus {
  bankAccounts: Array<{
    account_id: number
    account_name: string
    unreconciled_count: number
    unreconciled_amount: number
  }>
  totalUnreconciled: number
}

/**
 * خدمة التحقق من صحة البيانات قبل الإقفال المحاسبي
 */
export class ClosingValidationService {
  private db: any // sql.js Database instance
  private static instance: ClosingValidationService

  constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
  }

  public static getInstance(): ClosingValidationService {
    if (!ClosingValidationService.instance) {
      ClosingValidationService.instance = new ClosingValidationService()
    }
    return ClosingValidationService.instance
  }

  /**
   * التحقق الشامل من صحة البيانات قبل الإقفال
   */
  public async validateForClosing(startDate: string, endDate: string): Promise<ValidationResult> {
    const errors: string[] = []
    const warnings: string[] = []
    const details: any = {}

    try {
      // 1. التحقق من ميزان المراجعة
      const trialBalance = await this.validateTrialBalance(startDate, endDate)
      details.trialBalance = trialBalance
      if (!trialBalance.balanced) {
        errors.push(`ميزان المراجعة غير متوازن - الفرق: ${trialBalance.difference.toFixed(2)}`)
      }

      // 2. التحقق من القيود غير المرحلة
      const unpostedEntries = await this.checkUnpostedEntries(startDate, endDate)
      details.unpostedEntries = unpostedEntries
      if (unpostedEntries.count > 0) {
        errors.push(`يوجد ${unpostedEntries.count} قيد غير مرحل`)
      }

      // 3. التحقق من المعاملات المصرفية غير المسواة
      const reconciliation = await this.checkBankReconciliation(startDate, endDate)
      details.bankReconciliation = reconciliation
      if (reconciliation.totalUnreconciled > 0) {
        warnings.push(`يوجد ${reconciliation.totalUnreconciled} معاملة مصرفية غير مسواة`)
      }

      // 4. التحقق من الفواتير المعلقة
      const pendingInvoices = await this.checkPendingInvoices(startDate, endDate)
      details.pendingInvoices = pendingInvoices
      if (pendingInvoices.count > 0) {
        warnings.push(`يوجد ${pendingInvoices.count} فاتورة معلقة`)
      }

      // 5. التحقق من المخزون
      const inventoryStatus = await this.checkInventoryStatus(endDate)
      details.inventoryStatus = inventoryStatus
      if (inventoryStatus.hasDiscrepancies) {
        warnings.push('يوجد تضارب في أرصدة المخزون')
      }

      // 6. التحقق من النسخ الاحتياطية
      const backupStatus = await this.checkBackupStatus()
      details.backupStatus = backupStatus
      if (!backupStatus.hasRecentBackup) {
        errors.push('لا توجد نسخة احتياطية حديثة')
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        details
      }
    } catch (error) {
      Logger.error('ClosingValidationService', 'خطأ في التحقق من صحة البيانات:', error)
      return {
        isValid: false,
        errors: ['حدث خطأ في التحقق من صحة البيانات'],
        warnings: [],
        details: {}
      }
    }
  }

  /**
   * التحقق من توازن ميزان المراجعة
   */
  public async validateTrialBalance(startDate: string, endDate: string): Promise<TrialBalanceResult> {
    try {
      const accounts = this.db.prepare(`
        SELECT 
          coa.account_code,
          coa.account_name,
          coa.account_type,
          COALESCE(SUM(CASE 
            WHEN coa.account_type IN ('asset', 'expense') 
            THEN jed.debit_amount - jed.credit_amount
            ELSE jed.credit_amount - jed.debit_amount 
          END), 0) as balance
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE (je.entry_date IS NULL OR je.entry_date BETWEEN ? AND ?)
          AND (je.status IS NULL OR je.status = 'posted')
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        ORDER BY coa.account_code
      `).all(startDate, endDate) as any[]

      let totalDebits = 0
      let totalCredits = 0

      const processedAccounts = accounts.map(account => {
        const balance = account.balance || 0
        const debit_balance = balance > 0 ? balance : 0
        const credit_balance = balance < 0 ? Math.abs(balance) : 0

        totalDebits += debit_balance
        totalCredits += credit_balance

        return {
          account_code: account.account_code,
          account_name: account.account_name,
          debit_balance,
          credit_balance
        }
      })

      const difference = Math.abs(totalDebits - totalCredits)
      const balanced = difference < 0.01 // تسامح بسيط للأخطاء العشرية

      return {
        balanced,
        totalDebits,
        totalCredits,
        difference,
        accounts: processedAccounts
      }
    } catch (error) {
      Logger.error('ClosingValidationService', 'خطأ في التحقق من ميزان المراجعة:', error)
      return {
        balanced: false,
        totalDebits: 0,
        totalCredits: 0,
        difference: 0,
        accounts: []
      }
    }
  }

  /**
   * التحقق من القيود غير المرحلة
   */
  private async checkUnpostedEntries(startDate: string, endDate: string): Promise<{ count: number; entries: any[] }> {
    try {
      const entries = this.db.prepare(`
        SELECT id, entry_number, entry_date, description, total_debit, total_credit
        FROM journal_entries 
        WHERE entry_date BETWEEN ? AND ? AND status = 'draft'
        ORDER BY entry_date DESC
      `).all(startDate, endDate)

      return {
        count: entries.length,
        entries
      }
    } catch (error) {
      Logger.error('ClosingValidationService', 'خطأ في فحص القيود غير المرحلة:', error)
      return { count: 0, entries: [] }
    }
  }

  /**
   * التحقق من المعاملات المصرفية غير المسواة
   */
  private async checkBankReconciliation(startDate: string, endDate: string): Promise<ReconciliationStatus> {
    try {
      const bankAccounts = this.db.prepare(`
        SELECT 
          ba.id as account_id,
          ba.account_name,
          COUNT(bt.id) as unreconciled_count,
          COALESCE(SUM(ABS(bt.amount)), 0) as unreconciled_amount
        FROM bank_accounts ba
        LEFT JOIN bank_transactions bt ON ba.id = bt.account_id
        LEFT JOIN journal_entries je ON je.reference_type = 'bank_transaction' AND je.reference_id = bt.id
        WHERE bt.transaction_date BETWEEN ? AND ? AND je.id IS NULL
        GROUP BY ba.id, ba.account_name
        HAVING unreconciled_count > 0
      `).all(startDate, endDate) as any[]

      const totalUnreconciled = bankAccounts.reduce((sum, account) => sum + account.unreconciled_count, 0)

      return {
        bankAccounts,
        totalUnreconciled
      }
    } catch (error) {
      Logger.error('ClosingValidationService', 'خطأ في فحص المعاملات المصرفية:', error)
      return { bankAccounts: [], totalUnreconciled: 0 }
    }
  }

  /**
   * التحقق من الفواتير المعلقة
   */
  private async checkPendingInvoices(startDate: string, endDate: string): Promise<{ count: number; amount: number }> {
    try {
      // فحص فواتير المبيعات المعلقة
      const salesInvoices = this.db.prepare(`
        SELECT COUNT(*) as count, COALESCE(SUM(remaining_amount), 0) as amount
        FROM sales_invoices 
        WHERE invoice_date BETWEEN ? AND ? 
          AND status IN ('pending', 'partial', 'overdue')
          AND remaining_amount > 0
      `).get(startDate, endDate) as { count: number; amount: number }

      // فحص فواتير المشتريات المعلقة
      const purchaseInvoices = this.db.prepare(`
        SELECT COUNT(*) as count, COALESCE(SUM(remaining_amount), 0) as amount
        FROM purchase_invoices 
        WHERE invoice_date BETWEEN ? AND ? 
          AND status IN ('pending', 'partial', 'overdue')
          AND remaining_amount > 0
      `).get(startDate, endDate) as { count: number; amount: number }

      return {
        count: salesInvoices.count + purchaseInvoices.count,
        amount: salesInvoices.amount + purchaseInvoices.amount
      }
    } catch (error) {
      Logger.error('ClosingValidationService', 'خطأ في فحص الفواتير المعلقة:', error)
      return { count: 0, amount: 0 }
    }
  }

  /**
   * التحقق من حالة المخزون
   */
  private async checkInventoryStatus(endDate: string): Promise<{ hasDiscrepancies: boolean; details: any[] }> {
    try {
      // فحص الأصناف ذات الكميات السالبة
      const negativeItems = this.db.prepare(`
        SELECT i.code, i.name, SUM(im.quantity) as total_quantity
        FROM items i
        LEFT JOIN inventory_movements im ON i.id = im.item_id
        WHERE im.created_at <= ?
        GROUP BY i.id, i.code, i.name
        HAVING total_quantity < 0
      `).all(endDate)

      return {
        hasDiscrepancies: negativeItems.length > 0,
        details: negativeItems
      }
    } catch (error) {
      Logger.error('ClosingValidationService', 'خطأ في فحص حالة المخزون:', error)
      return { hasDiscrepancies: false, details: [] }
    }
  }

  /**
   * التحقق من حالة النسخ الاحتياطية
   */
  private async checkBackupStatus(): Promise<{ hasRecentBackup: boolean; lastBackup?: any }> {
    try {
      const lastBackup = this.db.prepare(`
        SELECT * FROM backups 
        WHERE status = 'completed' 
        ORDER BY created_at DESC 
        LIMIT 1
      `).get()

      if (!lastBackup) {
        return { hasRecentBackup: false }
      }

      // التحقق من أن النسخة الاحتياطية خلال آخر 24 ساعة
      const backupDate = new Date((lastBackup as any).created_at)
      const now = new Date()
      const hoursDiff = (now.getTime() - backupDate.getTime()) / (1000 * 60 * 60)

      return {
        hasRecentBackup: hoursDiff <= 24,
        lastBackup
      }
    } catch (error) {
      Logger.error('ClosingValidationService', 'خطأ في فحص النسخ الاحتياطية:', error)
      return { hasRecentBackup: false }
    }
  }

  /**
   * إنشاء تقرير التحقق التفصيلي
   */
  public async generateValidationReport(startDate: string, endDate: string): Promise<any> {
    try {
      const validation = await this.validateForClosing(startDate, endDate)
      
      return {
        summary: {
          isValid: validation.isValid,
          errorCount: validation.errors.length,
          warningCount: validation.warnings.length
        },
        errors: validation.errors,
        warnings: validation.warnings,
        details: validation.details,
        generatedAt: new Date().toISOString(),
        period: { startDate, endDate }
      }
    } catch (error) {
      Logger.error('ClosingValidationService', 'خطأ في إنشاء تقرير التحقق:', error)
      throw error
    }
  }
}
