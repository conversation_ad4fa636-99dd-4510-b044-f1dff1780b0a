import React, { useState } from 'react'
import { <PERSON>, Row, <PERSON>, <PERSON>po<PERSON>, Button } from 'antd'
import {
  ArrowLeftOutlined,
  BarChartOutlined,
  UserOutlined,
  BgColorsOutlined,
  CalendarOutlined,
  DollarOutlined,
  FileTextOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import {
  PaintByCustomerReport,
  PaintByTypeReport,
  MonthlyPaintReport,
  PaintProfitabilityReport,
  PaintPerformanceReport,
  PaintQualityReport
} from '../../reports'

const { Title, Text } = Typography

const ReportCard = styled(Card)`
  height: 200px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }
  
  .ant-card-body {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  }
  
  .report-icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: #ff7875;
  }
  
  .report-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #262626;
  }
  
  .report-description {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
  }
`

const CustomerReportCard = styled(ReportCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }
  
  .report-icon {
    color: #52c41a;
  }
`

const PaintTypeReportCard = styled(ReportCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  }
  
  .report-icon {
    color: #1890ff;
  }
`

const MonthlyReportCard = styled(ReportCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
  }
  
  .report-icon {
    color: #722ed1;
  }
`

const ProfitabilityReportCard = styled(ReportCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
  }
  
  .report-icon {
    color: #faad14;
  }
`

const PerformanceReportCard = styled(ReportCard)`
  .ant-card-body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .report-icon {
    color: #eb2f96;
  }
`

interface PaintReportsProps {
  onBack: () => void
}

type ReportType = 'customer' | 'type' | 'monthly' | 'profitability' | 'performance' | 'quality' | null

const PaintReports: React.FC<PaintReportsProps> = ({ onBack }) => {
  const [selectedReport, setSelectedReport] = useState<ReportType>(null)

  const handleReportClick = (reportType: ReportType) => {
    setSelectedReport(reportType)
  }

  const renderReport = () => {
    switch (selectedReport) {
      case 'customer':
        return <PaintByCustomerReport />
      case 'type':
        return <PaintByTypeReport />
      case 'monthly':
        return <MonthlyPaintReport />
      case 'profitability':
        return <PaintProfitabilityReport />
      case 'performance':
        return <PaintPerformanceReport />
      case 'quality':
        return <PaintQualityReport />
      default:
        return null
    }
  }

  if (selectedReport) {
    return (
      <div>
        <div style={{ marginBottom: '16px', display: 'flex', gap: '8px' }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => setSelectedReport(null)}
          >
            العودة للتقارير
          </Button>
          <Button
            onClick={onBack}
          >
            العودة للدهان
          </Button>
        </div>
        {renderReport()}
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h1 style={{ margin: 0, color: '#ff7875', fontSize: '28px' }}>
            <BarChartOutlined style={{ marginLeft: '12px' }} />
            تقارير الدهان
          </h1>
          <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '16px' }}>
            تقارير شاملة لعمليات الدهان مشابهة لتقارير المبيعات
          </p>
        </div>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
          size="large"
          style={{ borderRadius: '8px' }}
        >
          العودة
        </Button>
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12} lg={8}>
          <CustomerReportCard onClick={() => handleReportClick('customer')}>
            <div className="ant-card-body">
              <UserOutlined className="report-icon" />
              <div className="report-title">تقرير الدهان حسب العميل</div>
              <div className="report-description">
                عرض إجمالي أعمال الدهان والمدفوعات لكل عميل
                <br />
                مع تحليل الأداء والمستحقات
              </div>
            </div>
          </CustomerReportCard>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <PaintTypeReportCard onClick={() => handleReportClick('type')}>
            <div className="ant-card-body">
              <BgColorsOutlined className="report-icon" />
              <div className="report-title">تقرير الدهان حسب النوع</div>
              <div className="report-description">
                تحليل أعمال الدهان بالمساحات والأسعار وأنواع الدهان
                <br />
                مع إحصائيات شاملة
              </div>
            </div>
          </PaintTypeReportCard>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <MonthlyReportCard onClick={() => handleReportClick('monthly')}>
            <div className="ant-card-body">
              <CalendarOutlined className="report-icon" />
              <div className="report-title">التقرير الشهري للدهان</div>
              <div className="report-description">
                تقرير شهري شامل لأعمال الدهان
                <br />
                مع معدلات النمو والمقارنات
              </div>
            </div>
          </MonthlyReportCard>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <ProfitabilityReportCard onClick={() => handleReportClick('profitability')}>
            <div className="ant-card-body">
              <DollarOutlined className="report-icon" />
              <div className="report-title">تقرير ربحية الدهان</div>
              <div className="report-description">
                تحليل شامل للأرباح والهوامش
                <br />
                والعائد على الاستثمار
              </div>
            </div>
          </ProfitabilityReportCard>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <PerformanceReportCard onClick={() => handleReportClick('performance')}>
            <div className="ant-card-body">
              <BarChartOutlined className="report-icon" />
              <div className="report-title">تحليل أداء الدهان</div>
              <div className="report-description">
                تحليل الأداء عبر الفترات الزمنية
                <br />
                مع مؤشرات النمو ومعدل الإنجاز
              </div>
            </div>
          </PerformanceReportCard>
        </Col>

        <Col xs={24} sm={12} lg={8}>
          <ReportCard onClick={() => handleReportClick('quality')}>
            <div className="ant-card-body">
              <FileTextOutlined className="report-icon" />
              <div className="report-title">تقرير جودة الدهان</div>
              <div className="report-description">
                تقرير مراقبة جودة أعمال الدهان
                <br />
                ومعدلات الرضا والإعادة
              </div>
            </div>
          </ReportCard>
        </Col>
      </Row>

      <div style={{ marginTop: '32px', padding: '24px', background: '#f5f5f5', borderRadius: '12px' }}>
        <Title level={4} style={{ color: '#ff7875', marginBottom: '16px' }}>
          <BarChartOutlined style={{ marginLeft: '8px' }} />
          مميزات تقارير الدهان
        </Title>
        
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <div style={{ padding: '16px', background: 'white', borderRadius: '8px', marginBottom: '12px' }}>
              <Text strong style={{ color: '#52c41a' }}>✓ تقارير مشابهة للمبيعات:</Text>
              <br />
              <Text type="secondary">
                نفس تصميم وهيكل تقارير المبيعات للتناسق
              </Text>
            </div>
          </Col>
          
          <Col xs={24} md={12}>
            <div style={{ padding: '16px', background: 'white', borderRadius: '8px', marginBottom: '12px' }}>
              <Text strong style={{ color: '#1890ff' }}>✓ تحليل المساحات:</Text>
              <br />
              <Text type="secondary">
                تحليل مفصل للمساحات المدهونة وأنواع الدهان
              </Text>
            </div>
          </Col>
          
          <Col xs={24} md={12}>
            <div style={{ padding: '16px', background: 'white', borderRadius: '8px', marginBottom: '12px' }}>
              <Text strong style={{ color: '#722ed1' }}>✓ تحليل الربحية:</Text>
              <br />
              <Text type="secondary">
                حساب الأرباح والهوامش لكل نوع دهان وعميل
              </Text>
            </div>
          </Col>
          
          <Col xs={24} md={12}>
            <div style={{ padding: '16px', background: 'white', borderRadius: '8px', marginBottom: '12px' }}>
              <Text strong style={{ color: '#faad14' }}>✓ مؤشرات الأداء:</Text>
              <br />
              <Text type="secondary">
                معدلات الإنجاز وأوقات التسليم ومؤشرات الجودة
              </Text>
            </div>
          </Col>
        </Row>
      </div>

      <div style={{ marginTop: '24px', padding: '20px', background: '#fff7e6', border: '1px solid #ffd591', borderRadius: '8px' }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
          <FileTextOutlined style={{ fontSize: '20px', color: '#faad14', marginLeft: '8px' }} />
          <Text strong style={{ color: '#d48806' }}>ملاحّة هامة</Text>
        </div>
        <Text style={{ color: '#d48806' }}>
          تقارير الدهان مصممة لتكون مشابهة تماماً لتقارير المبيعات من ناحية التصميم والوّائف،
          مع التركيز على المساحات المدهونة وأنواع الدهان بدلاً من الكميات والأصناف المخزنية.
          جميع التقارير تدعم التصدير للـ PDF و Excel والطباعة المباشرة.
        </Text>
      </div>
    </div>
  )
}

export default PaintReports
