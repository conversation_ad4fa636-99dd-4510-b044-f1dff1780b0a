﻿[
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\financialHandlers.ts",
        "LineNumber":  371,
        "Line":  "      const images = await financialService.getCheckImages(checkId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\financialHandlers.ts",
        "LineNumber":  381,
        "Line":  "      const imageId = await financialService.addCheckImage(imageData)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  11,
        "Line":  "import { ImageDataMigrator } from \u0027../scripts/migrateImageData\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  12,
        "Line":  "import { OldImageSystemCleanup } from \u0027../scripts/cleanupOldImageSystem\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  17,
        "Line":  "export function registerImageHandlers(): void {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  142,
        "Line":  "async function handleSaveImageFile("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  161,
        "Line":  "    const base64Data = dataUrl.replace(/^data:image\\/[a-z]+;base64,/, \u0027\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  186,
        "Line":  "async function handleDeleteImageFile("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  222,
        "Line":  "async function handleCreateUnifiedImagesTable("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  277,
        "Line":  "async function handleCreateImageIndexes("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  315,
        "Line":  "async function handleCreateImageDirectories("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  322,
        "Line":  "    const imagesBasePath = path.join(userDataPath, \u0027images\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  358,
        "Line":  "async function handleMigrateImageData("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  364,
        "Line":  "    const migrator = new ImageDataMigrator()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  382,
        "Line":  "async function handleCleanupOldImageSystem("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  388,
        "Line":  "    const cleanup = new OldImageSystemCleanup()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  412,
        "Line":  "    const cleanup = new OldImageSystemCleanup()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\imageHandlers.ts",
        "LineNumber":  430,
        "Line":  "export function unregisterImageHandlers(): void {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\productionHandlers.ts",
        "LineNumber":  3,
        "Line":  "import { ImageMigrationService } from \u0027../services/ImageMigrationService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\productionHandlers.ts",
        "LineNumber":  378,
        "Line":  "      const images = await productionService.getProductionOrderImages(orderId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\productionHandlers.ts",
        "LineNumber":  389,
        "Line":  "      const imageId = await productionService.addProductionOrderImage(imageData)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\productionHandlers.ts",
        "LineNumber":  400,
        "Line":  "      const success = await productionService.createSampleProductionOrderImages(orderId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\productionHandlers.ts",
        "LineNumber":  815,
        "Line":  "      const migrationService = new ImageMigrationService(productionService)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\productionHandlers.ts",
        "LineNumber":  816,
        "Line":  "      const result = await migrationService.migrateAllImages(localStorageData)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\productionHandlers.ts",
        "LineNumber":  844,
        "Line":  "      const migrationService = new ImageMigrationService(productionService)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  420,
        "Line":  "            const imageBuffer = fs.readFileSync(logoPath)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  424,
        "Line":  "            let mimeType = \u0027image/png\u0027 // افتراضي"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  428,
        "Line":  "                mimeType = \u0027image/jpeg\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  431,
        "Line":  "                mimeType = \u0027image/png\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  434,
        "Line":  "                mimeType = \u0027image/gif\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  437,
        "Line":  "                mimeType = \u0027image/bmp\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  440,
        "Line":  "                mimeType = \u0027image/svg+xml\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  445,
        "Line":  "            const base64Data = imageBuffer.toString(\u0027base64\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  455,
        "Line":  "                type: \u0027image\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  537,
        "Line":  "        const imageBuffer = fs.readFileSync(newFilePath)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  538,
        "Line":  "        let mimeType = \u0027image/png\u0027 // افتراضي"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  542,
        "Line":  "            mimeType = \u0027image/jpeg\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  545,
        "Line":  "            mimeType = \u0027image/png\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  548,
        "Line":  "            mimeType = \u0027image/gif\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  551,
        "Line":  "            mimeType = \u0027image/bmp\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  554,
        "Line":  "            mimeType = \u0027image/svg+xml\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\handlers\\systemHandlers.ts",
        "LineNumber":  558,
        "Line":  "        const base64Data = imageBuffer.toString(\u0027base64\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  12,
        "Line":  "export class OldImageSystemCleanup {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  72,
        "Line":  "        SELECT COUNT(*) as count FROM item_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  77,
        "Line":  "        SELECT COUNT(*) as count FROM unified_images "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  107,
        "Line":  "    const backupTableName = `item_images_final_backup_${Date.now()}`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  111,
        "Line":  "      SELECT * FROM item_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  140,
        "Line":  "      const oldImagesPath = path.join(app.getPath(\u0027userData\u0027), \u0027images\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  144,
        "Line":  "        const files = this.findOldImageFiles(oldImagesPath)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  150,
        "Line":  "              SELECT COUNT(*) as count FROM unified_images "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  199,
        "Line":  "          const imageExtensions = [\u0027.jpg\u0027, \u0027.jpeg\u0027, \u0027.png\u0027, \u0027.gif\u0027, \u0027.bmp\u0027, \u0027.webp\u0027]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  221,
        "Line":  "        WHERE type=\u0027table\u0027 AND name=\u0027unified_images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  233,
        "Line":  "        SELECT COUNT(*) as count FROM unified_images "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  262,
        "Line":  "  const cleanup = new OldImageSystemCleanup()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\cleanupOldImageSystem.ts",
        "LineNumber":  270,
        "Line":  "  const cleanup = new OldImageSystemCleanup()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  12,
        "Line":  "interface OldImageRecord {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  23,
        "Line":  "interface NewImageRecord {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  40,
        "Line":  "export class ImageDataMigrator {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  63,
        "Line":  "      const oldImages = await this.getOldImages()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  76,
        "Line":  "      for (const oldImage of oldImages) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  82,
        "Line":  "          const errorMsg = `خطأ في ترحيل الصورة ${oldImage.id}: ${error}`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  126,
        "Line":  "      WHERE type=\u0027table\u0027 AND name=\u0027item_images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  136,
        "Line":  "      WHERE type=\u0027table\u0027 AND name=\u0027unified_images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  150,
        "Line":  "    const images = this.db.prepare(`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  151,
        "Line":  "      SELECT * FROM item_images "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  170,
        "Line":  "    const newId = `item_${oldImage.item_id}_${oldImage.id}_${Date.now()}`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  173,
        "Line":  "    const mimeType = this.getMimeType(path.extname(oldImage.image_name))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  176,
        "Line":  "    const newImageDir = path.join(app.getPath(\u0027userData\u0027), \u0027images\u0027, \u0027inventory\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  181,
        "Line":  "    const newFileName = `${newId}${path.extname(oldImage.image_name)}`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  182,
        "Line":  "    const newFilePath = path.join(newImageDir, newFileName)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  190,
        "Line":  "      migratedFrom: \u0027item_images\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  196,
        "Line":  "    const newRecord: Omit\u003cNewImageRecord, \u0027id\u0027\u003e = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  240,
        "Line":  "    const backupTableName = `item_images_backup_${Date.now()}`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  244,
        "Line":  "      SELECT * FROM item_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  267,
        "Line":  "    return mimeTypes[extension.toLowerCase()] || \u0027image/jpeg\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  274,
        "Line":  "export async function runImageMigration(): Promise\u003cvoid\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\scripts\\migrateImageData.ts",
        "LineNumber":  275,
        "Line":  "  const migrator = new ImageDataMigrator()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\FinancialService.ts",
        "LineNumber":  2414,
        "Line":  "    return mimeTypes[extension] || \u0027image/jpeg\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\FinancialService.ts",
        "LineNumber":  2422,
        "Line":  "      const images = this.db.prepare(`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\FinancialService.ts",
        "LineNumber":  2423,
        "Line":  "        SELECT * FROM check_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\FinancialService.ts",
        "LineNumber":  2429,
        "Line":  "      const processedImages = images.map(image =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\FinancialService.ts",
        "LineNumber":  2432,
        "Line":  "            const imageBuffer = fs.readFileSync(image.image_path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\FinancialService.ts",
        "LineNumber":  2433,
        "Line":  "            const mimeType = this.getMimeType(image.image_path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\FinancialService.ts",
        "LineNumber":  2434,
        "Line":  "            const base64Data = imageBuffer.toString(\u0027base64\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\FinancialService.ts",
        "LineNumber":  2495,
        "Line":  "        const base64Data = imageData.fileData.split(\u0027,\u0027)[1] // إزالة data:image/...;base64,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\FinancialService.ts",
        "LineNumber":  2550,
        "Line":  "      const imagesDir = path.join(app.getPath(\u0027userData\u0027), \u0027images\u0027, \u0027checks\u0027, checkId.toString())"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\FinancialService.ts",
        "LineNumber":  2558,
        "Line":  "      const newFilePath = path.join(imagesDir, newFileName)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\FinancialService.ts",
        "LineNumber":  2576,
        "Line":  "        imageData.fileType || imageData.image_type || null,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\FinancialService.ts",
        "LineNumber":  2597,
        "Line":  "      this.db.prepare(\u0027DELETE FROM check_images WHERE id = ?\u0027).run([imageId])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  28,
        "Line":  "export interface FurnitureImageData {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  48,
        "Line":  "export class ImageMigrationService {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  75,
        "Line":  "      const imageKeys = this.findImageKeys(localStorageData)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  81,
        "Line":  "      for (const key of imageKeys) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  112,
        "Line":  "    const imageKeys: string[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  115,
        "Line":  "    const knownImageKeys = ["
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  164,
        "Line":  "      const images: FurnitureImageData[] = JSON.parse(data)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  170,
        "Line":  "      for (const image of images) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  171,
        "Line":  "        const migrationResult = await this.migrateImage(image)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  240,
        "Line":  "      const orderIdNum = parseInt(image.orderId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  249,
        "Line":  "      const existingImages = await this.productionService.getProductionOrderImages(orderIdNum)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  250,
        "Line":  "      const imageExists = existingImages.some(existing =\u003e "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  264,
        "Line":  "      const imageData = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  269,
        "Line":  "        file_type: image.type,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ImageMigrationService.ts",
        "LineNumber":  279,
        "Line":  "      const newImageId = await this.productionService.addProductionOrderImage(imageData)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ProductionService.ts",
        "LineNumber":  1607,
        "Line":  "        SELECT * FROM production_order_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ProductionService.ts",
        "LineNumber":  1629,
        "Line":  "      const processedImages = images.map((image: any) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ProductionService.ts",
        "LineNumber":  1641,
        "Line":  "            const imageBuffer = fs.readFileSync(image.image_path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ProductionService.ts",
        "LineNumber":  1642,
        "Line":  "            const mimeType = this.getMimeType(require(\u0027path\u0027).extname(image.image_path))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ProductionService.ts",
        "LineNumber":  1654,
        "Line":  "            const base64Data = imageBuffer.toString(\u0027base64\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ProductionService.ts",
        "LineNumber":  1773,
        "Line":  "        imageData.file_type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ProductionService.ts",
        "LineNumber":  1821,
        "Line":  "      const existingImages = this.db.prepare(\u0027SELECT COUNT(*) as count FROM production_order_images WHERE order_id = ?\u0027).get(orderId) as any"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ProductionService.ts",
        "LineNumber":  1828,
        "Line":  "      const sampleImages = ["
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ProductionService.ts",
        "LineNumber":  1856,
        "Line":  "        const imageInfo = sampleImages[i]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ProductionService.ts",
        "LineNumber":  1869,
        "Line":  "        const dataUrl = `data:image/svg+xml;base64,${Buffer.from(svgContent).toString(\u0027base64\u0027)}`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\ProductionService.ts",
        "LineNumber":  1877,
        "Line":  "          file_type: \u0027image/svg+xml\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\SimpleDatabaseService.ts",
        "LineNumber":  242,
        "Line":  "        type TEXT NOT NULL CHECK (type IN (\u0027invoice\u0027, \u0027receipt\u0027, \u0027report\u0027, \u0027certificate\u0027, \u0027custom\u0027, \u0027image\u0027)),"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\SimpleDatabaseService.ts",
        "LineNumber":  325,
        "Line":  "      const unifiedImagesTable = `"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\SimpleDatabaseService.ts",
        "LineNumber":  357,
        "Line":  "      const imageIndexes = ["
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\SimpleDatabaseService.ts",
        "LineNumber":  366,
        "Line":  "      for (const indexSQL of imageIndexes) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\SimpleDatabaseService.ts",
        "LineNumber":  528,
        "Line":  "            type: \u0027image\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\TemplateStorageService.ts",
        "LineNumber":  83,
        "Line":  "            type TEXT NOT NULL CHECK (type IN (\u0027invoice\u0027, \u0027receipt\u0027, \u0027report\u0027, \u0027certificate\u0027, \u0027custom\u0027, \u0027image\u0027)),"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\services\\TemplateStorageService.ts",
        "LineNumber":  161,
        "Line":  "          (id, name, description, type, category, is_default, is_custom, is_active, template_data, preview_image, created_by)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\main.ts",
        "LineNumber":  28,
        "Line":  "import { registerImageHandlers } from \u0027./handlers/imageHandlers\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\main\\main.ts",
        "LineNumber":  339,
        "Line":  "        const createImageTableQuery = `"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\admin\\ImageMigrationTool.tsx",
        "LineNumber":  45,
        "Line":  "export const ImageMigrationTool: React.FC = () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\admin\\ImageMigrationTool.tsx",
        "LineNumber":  63,
        "Line":  "      const migrationResult = await window.electronAPI.invoke(\u0027migrate-image-data\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\admin\\ImageMigrationTool.tsx",
        "LineNumber":  266,
        "Line":  "export default ImageMigrationTool"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\AdvancedLogoManager.tsx",
        "LineNumber":  189,
        "Line":  "  const [originalImageSize, setOriginalImageSize] = useState({ width: 0, height: 0 })"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\AdvancedLogoManager.tsx",
        "LineNumber":  354,
        "Line":  "      const aspectRatio = originalImageSize.width / originalImageSize.height"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  30,
        "Line":  "import type { CheckImage } from \u0027../../types/global\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  31,
        "Line":  "import EnhancedCheckImagePrint from \u0027../financial/EnhancedCheckImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  33,
        "Line":  "interface CheckImageManagerProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  52,
        "Line":  "const CheckImageManager: React.FC\u003cCheckImageManagerProps\u003e = ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  60,
        "Line":  "  const [images, setImages] = useState\u003cCheckImage[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  67,
        "Line":  "  const [previewImage, setPreviewImage] = useState(\u0027\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  79,
        "Line":  "  const loadImages = async () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  84,
        "Line":  "      const response = await window.electronAPI?.getCheckImages(checkId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  137,
        "Line":  "      const response = await window.electronAPI?.uploadCheckImage(uploadData)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  156,
        "Line":  "  const handleDeleteImage = async (imageId: number) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  158,
        "Line":  "      const response = await window.electronAPI?.deleteCheckImage(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  172,
        "Line":  "  const handleUpdateNotes = async (imageId: number, notes: string) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  174,
        "Line":  "      const response = await window.electronAPI?.updateCheckImageNotes({ imageId, notes })"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  189,
        "Line":  "  const handlePreview = (image: CheckImage) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  208,
        "Line":  "  const startEditNotes = (image: CheckImage) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  220,
        "Line":  "  const saveNotes = (imageId: number) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckImageManager.tsx",
        "LineNumber":  443,
        "Line":  "export default CheckImageManager"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckScanner.tsx",
        "LineNumber":  24,
        "Line":  "import type { CheckImage } from \u0027../../types/global\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckScanner.tsx",
        "LineNumber":  44,
        "Line":  "  const [scannedImages, setScannedImages] = useState\u003cany[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckScanner.tsx",
        "LineNumber":  79,
        "Line":  "      const scannedImage = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckScanner.tsx",
        "LineNumber":  116,
        "Line":  "        const uploadedImage = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckScanner.tsx",
        "LineNumber":  141,
        "Line":  "  const removeScannedImage = (imageId: number | string) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckScanner.tsx",
        "LineNumber":  166,
        "Line":  "      const savedImages: CheckImage[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckScanner.tsx",
        "LineNumber":  168,
        "Line":  "      for (const image of scannedImages) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckScanner.tsx",
        "LineNumber":  174,
        "Line":  "          fileType: image.file?.type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CheckScanner.tsx",
        "LineNumber":  183,
        "Line":  "        const response = await window.electronAPI?.uploadCheckImage(uploadData)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CustomerImagesPrintButton.tsx",
        "LineNumber":  8,
        "Line":  "import { FileImageOutlined, LoadingOutlined, UserOutlined } from \u0027@ant-design/icons\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CustomerImagesPrintButton.tsx",
        "LineNumber":  9,
        "Line":  "import UniversalImagePrint from \u0027./UniversalImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CustomerImagesPrintButton.tsx",
        "LineNumber":  10,
        "Line":  "import { imageService, type UnifiedImageData } from \u0027../../services/images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CustomerImagesPrintButton.tsx",
        "LineNumber":  11,
        "Line":  "import type { UniversalImage } from \u0027./UniversalImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CustomerImagesPrintButton.tsx",
        "LineNumber":  14,
        "Line":  "interface CustomerImagesPrintButtonProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CustomerImagesPrintButton.tsx",
        "LineNumber":  25,
        "Line":  "const CustomerImagesPrintButton: React.FC\u003cCustomerImagesPrintButtonProps\u003e = ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CustomerImagesPrintButton.tsx",
        "LineNumber":  38,
        "Line":  "  const [images, setImages] = useState\u003cUniversalImage[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CustomerImagesPrintButton.tsx",
        "LineNumber":  41,
        "Line":  "  const loadImages = async () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CustomerImagesPrintButton.tsx",
        "LineNumber":  53,
        "Line":  "      const result = await imageService.getImages({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CustomerImagesPrintButton.tsx",
        "LineNumber":  65,
        "Line":  "      const convertedImages: UniversalImage[] = result.data.map((img: UnifiedImageData) =\u003e ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\CustomerImagesPrintButton.tsx",
        "LineNumber":  143,
        "Line":  "export default CustomerImagesPrintButton"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\EnhancedProductCatalogPrint.tsx",
        "LineNumber":  30,
        "Line":  "import type { Item, ItemImage } from \u0027../../types/global\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\EnhancedProductCatalogPrint.tsx",
        "LineNumber":  86,
        "Line":  "  const getPrimaryImage = (itemId: number): ItemImage | undefined =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\EnhancedProductCatalogPrint.tsx",
        "LineNumber":  92,
        "Line":  "  const convertItemsToPrintFormat = (): PrintData[\u0027images\u0027] =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\EnhancedProductCatalogPrint.tsx",
        "LineNumber":  94,
        "Line":  "      const primaryImage = getPrimaryImage(item.id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\EnhancedProductCatalogPrint.tsx",
        "LineNumber":  139,
        "Line":  "    const printImages = convertItemsToPrintFormat()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageGallery.tsx",
        "LineNumber":  26,
        "Line":  "import type { ItemImage } from \u0027../../types/global\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageGallery.tsx",
        "LineNumber":  28,
        "Line":  "interface ImageGalleryProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageGallery.tsx",
        "LineNumber":  36,
        "Line":  "const ImageGallery: React.FC\u003cImageGalleryProps\u003e = ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageGallery.tsx",
        "LineNumber":  50,
        "Line":  "  const filteredAndSortedImages = React.useMemo(() =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageGallery.tsx",
        "LineNumber":  51,
        "Line":  "    const filtered = images.filter(image =\u003e "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageGallery.tsx",
        "LineNumber":  75,
        "Line":  "  const currentImage = filteredAndSortedImages[currentIndex]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageGallery.tsx",
        "LineNumber":  84,
        "Line":  "  const nextImage = () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageGallery.tsx",
        "LineNumber":  91,
        "Line":  "  const prevImage = () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageGallery.tsx",
        "LineNumber":  98,
        "Line":  "  const goToImage = (index: number) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageGallery.tsx",
        "LineNumber":  103,
        "Line":  "  const downloadImage = (image: ItemImage) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageGallery.tsx",
        "LineNumber":  330,
        "Line":  "export default ImageGallery"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImagePrintTest.tsx",
        "LineNumber":  5,
        "Line":  "import { ImagePrintService } from \u0027../../services/images/ImagePrintService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImagePrintTest.tsx",
        "LineNumber":  17,
        "Line":  "export const ImagePrintTest: React.FC = () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImagePrintTest.tsx",
        "LineNumber":  22,
        "Line":  "  const imageService = ImagePrintService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImagePrintTest.tsx",
        "LineNumber":  25,
        "Line":  "  const testImageLoading = async (): Promise\u003cTestResult\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImagePrintTest.tsx",
        "LineNumber":  30,
        "Line":  "      const itemImages = await window.electronAPI?.getItemImages(1)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImagePrintTest.tsx",
        "LineNumber":  33,
        "Line":  "        const firstImage = itemImages.data[0]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImagePrintTest.tsx",
        "LineNumber":  63,
        "Line":  "  const testImageConversion = async (): Promise\u003cTestResult\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImagePrintTest.tsx",
        "LineNumber":  108,
        "Line":  "  const testImagePrint = async (): Promise\u003cTestResult\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImagePrintTest.tsx",
        "LineNumber":  113,
        "Line":  "      const testImage = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImagePrintTest.tsx",
        "LineNumber":  136,
        "Line":  "        type: \u0027images\u0027 as const,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImagePrintTest.tsx",
        "LineNumber":  284,
        "Line":  "export default ImagePrintTest"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  2,
        "Line":  "export interface ImageData {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  18,
        "Line":  "export interface ImageUploadOptions {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  28,
        "Line":  "export class ImageService {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  32,
        "Line":  "    allowedTypes: [\u0027image/jpeg\u0027, \u0027image/png\u0027, \u0027image/gif\u0027, \u0027image/webp\u0027],"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  62,
        "Line":  "      const processedImage = await this.processImage(file, opts)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  71,
        "Line":  "      const imageData: ImageData = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  99,
        "Line":  "      const img = new Image()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  137,
        "Line":  "      const img = new Image()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  159,
        "Line":  "        const thumbnailUrl = canvas.toDataURL(\u0027image/jpeg\u0027, 0.7)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  170,
        "Line":  "    const images = this.getAllImages()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  192,
        "Line":  "  static getImagesByReference(reference_type: string, reference_id: number): ImageData[] {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  205,
        "Line":  "    const images = this.getAllImages()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  206,
        "Line":  "    const index = images.findIndex(img =\u003e img.id === id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  219,
        "Line":  "    const images = this.getAllImages().filter(img =\u003e img.id !== id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  220,
        "Line":  "    const originalLength = this.getAllImages().length"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  243,
        "Line":  "    const image = this.getImageById(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  245,
        "Line":  "      const tags = image.tags || []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  256,
        "Line":  "    const image = this.getImageById(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  258,
        "Line":  "      const tags = image.tags.filter(t =\u003e t !== tag)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  266,
        "Line":  "    const allTags = this.getAllImages()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  275,
        "Line":  "    const images = this.getAllImages()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  276,
        "Line":  "    const totalSize = images.reduce((sum, img) =\u003e sum + img.size, 0)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  288,
        "Line":  "      byType: images.reduce((acc, img) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  311,
        "Line":  "    const currentImages = this.getAllImages()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  312,
        "Line":  "    const filteredImages = currentImages.filter(img =\u003e "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  316,
        "Line":  "    const removedCount = currentImages.length - filteredImages.length"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  324,
        "Line":  "  static exportImageMetadata(): string {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageService.ts",
        "LineNumber":  325,
        "Line":  "    const images = this.getAllImages().map(img =\u003e ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageSettings.tsx",
        "LineNumber":  27,
        "Line":  "import type { ImageSetting } from \u0027../../types/global\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageSettings.tsx",
        "LineNumber":  29,
        "Line":  "interface ImageSettingsProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageSettings.tsx",
        "LineNumber":  34,
        "Line":  "const ImageSettings: React.FC\u003cImageSettingsProps\u003e = ({ visible, onClose }) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageSettings.tsx",
        "LineNumber":  38,
        "Line":  "  const [, setSettings] = useState\u003cImageSetting[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageSettings.tsx",
        "LineNumber":  44,
        "Line":  "      const response = await window.electronAPI?.getImageSettings()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageSettings.tsx",
        "LineNumber":  93,
        "Line":  "        const response = await window.electronAPI?.updateImageSetting({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageSettings.tsx",
        "LineNumber":  385,
        "Line":  "export default ImageSettings"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageWithFallback.tsx",
        "LineNumber":  2,
        "Line":  "import { Image, Typography } from \u0027antd\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageWithFallback.tsx",
        "LineNumber":  8,
        "Line":  "interface ImageWithFallbackProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageWithFallback.tsx",
        "LineNumber":  18,
        "Line":  "const ImageWithFallback: React.FC\u003cImageWithFallbackProps\u003e = ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageWithFallback.tsx",
        "LineNumber":  27,
        "Line":  "  const [imageError, setImageError] = useState(false)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageWithFallback.tsx",
        "LineNumber":  28,
        "Line":  "  const [imageLoading, setImageLoading] = useState(true)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageWithFallback.tsx",
        "LineNumber":  58,
        "Line":  "  const isValidImageSrc = (src: string | null | undefined): boolean =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ImageWithFallback.tsx",
        "LineNumber":  164,
        "Line":  "export default ImageWithFallback"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\index.ts",
        "LineNumber":  21,
        "Line":  "export { default as UniversalImagePrint } from \u0027./UniversalImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\index.ts",
        "LineNumber":  22,
        "Line":  "export type { UniversalImage, ImageContext } from \u0027./UniversalImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\index.ts",
        "LineNumber":  25,
        "Line":  "export { default as InvoiceImagesPrintButton } from \u0027./InvoiceImagesPrintButton\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\index.ts",
        "LineNumber":  26,
        "Line":  "export { default as ProductionOrderImagesPrintButton } from \u0027./ProductionOrderImagesPrintButton\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\index.ts",
        "LineNumber":  27,
        "Line":  "export { default as CustomerImagesPrintButton } from \u0027./CustomerImagesPrintButton\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\index.ts",
        "LineNumber":  30,
        "Line":  "export { default as SmartImagePrintButton } from \u0027./SmartImagePrintButton\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\index.ts",
        "LineNumber":  33,
        "Line":  "export { default as UniversalImagePrintDemo } from \u0027./UniversalImagePrintDemo\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\index.ts",
        "LineNumber":  36,
        "Line":  "export { universalImageService, UniversalImageService } from \u0027../../services/UniversalImageService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\index.ts",
        "LineNumber":  37,
        "Line":  "export type { ImageQuery } from \u0027../../services/UniversalImageService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\index.ts",
        "LineNumber":  40,
        "Line":  "export { default as CheckImageManager } from \u0027./CheckImageManager\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\InvoiceImagesPrintButton.tsx",
        "LineNumber":  8,
        "Line":  "import { FileImageOutlined, LoadingOutlined } from \u0027@ant-design/icons\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\InvoiceImagesPrintButton.tsx",
        "LineNumber":  9,
        "Line":  "import UniversalImagePrint from \u0027./UniversalImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\InvoiceImagesPrintButton.tsx",
        "LineNumber":  10,
        "Line":  "import { imageService, type UnifiedImageData } from \u0027../../services/images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\InvoiceImagesPrintButton.tsx",
        "LineNumber":  11,
        "Line":  "import type { UniversalImage } from \u0027./UniversalImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\InvoiceImagesPrintButton.tsx",
        "LineNumber":  14,
        "Line":  "interface InvoiceImagesPrintButtonProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\InvoiceImagesPrintButton.tsx",
        "LineNumber":  25,
        "Line":  "const InvoiceImagesPrintButton: React.FC\u003cInvoiceImagesPrintButtonProps\u003e = ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\InvoiceImagesPrintButton.tsx",
        "LineNumber":  38,
        "Line":  "  const [images, setImages] = useState\u003cUniversalImage[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\InvoiceImagesPrintButton.tsx",
        "LineNumber":  41,
        "Line":  "  const loadImages = async () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\InvoiceImagesPrintButton.tsx",
        "LineNumber":  53,
        "Line":  "      const result = await imageService.getImages({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\InvoiceImagesPrintButton.tsx",
        "LineNumber":  65,
        "Line":  "      const convertedImages: UniversalImage[] = result.data.map((img: UnifiedImageData) =\u003e ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\InvoiceImagesPrintButton.tsx",
        "LineNumber":  143,
        "Line":  "export default InvoiceImagesPrintButton"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\LogoManager.tsx",
        "LineNumber":  110,
        "Line":  "    const allowedTypes = [\u0027image/jpeg\u0027, \u0027image/jpg\u0027, \u0027image/png\u0027, \u0027image/svg+xml\u0027, \u0027image/webp\u0027]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductCatalog.tsx",
        "LineNumber":  22,
        "Line":  "import type { Item, ItemImage } from \u0027../../types/global\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductCatalog.tsx",
        "LineNumber":  50,
        "Line":  "  const [itemImages, setItemImages] = useState\u003c{ [key: number]: ItemImage[] }\u003e({})"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductCatalog.tsx",
        "LineNumber":  64,
        "Line":  "        const imagesMap: { [key: number]: ItemImage[] } = {}"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductCatalog.tsx",
        "LineNumber":  67,
        "Line":  "            const imagesResponse = await window.electronAPI?.getItemImages(item.id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductCatalog.tsx",
        "LineNumber":  82,
        "Line":  "        const imagesMap: { [key: number]: ItemImage[] } = {}"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductCatalog.tsx",
        "LineNumber":  85,
        "Line":  "            const imagesResponse = await window.electronAPI?.getItemImages(item.id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductCatalog.tsx",
        "LineNumber":  142,
        "Line":  "  const getPrimaryImage = (itemId: number): ItemImage | null =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductCatalog.tsx",
        "LineNumber":  143,
        "Line":  "    const images = itemImages[itemId] || []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductCatalog.tsx",
        "LineNumber":  148,
        "Line":  "  const getImageCount = (itemId: number): number =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductCatalog.tsx",
        "LineNumber":  174,
        "Line":  "        const primaryImage = getPrimaryImage(item.id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductCatalog.tsx",
        "LineNumber":  175,
        "Line":  "        const imageCount = getImageCount(item.id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductionOrderImagesPrintButton.tsx",
        "LineNumber":  8,
        "Line":  "import { FileImageOutlined, LoadingOutlined } from \u0027@ant-design/icons\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductionOrderImagesPrintButton.tsx",
        "LineNumber":  9,
        "Line":  "import UniversalImagePrint from \u0027./UniversalImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductionOrderImagesPrintButton.tsx",
        "LineNumber":  10,
        "Line":  "import { imageService, type UnifiedImageData } from \u0027../../services/images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductionOrderImagesPrintButton.tsx",
        "LineNumber":  11,
        "Line":  "import type { UniversalImage } from \u0027./UniversalImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductionOrderImagesPrintButton.tsx",
        "LineNumber":  14,
        "Line":  "interface ProductionOrderImagesPrintButtonProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductionOrderImagesPrintButton.tsx",
        "LineNumber":  25,
        "Line":  "const ProductionOrderImagesPrintButton: React.FC\u003cProductionOrderImagesPrintButtonProps\u003e = ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductionOrderImagesPrintButton.tsx",
        "LineNumber":  38,
        "Line":  "  const [images, setImages] = useState\u003cUniversalImage[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductionOrderImagesPrintButton.tsx",
        "LineNumber":  41,
        "Line":  "  const loadImages = async () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductionOrderImagesPrintButton.tsx",
        "LineNumber":  53,
        "Line":  "      const result = await imageService.getImages({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductionOrderImagesPrintButton.tsx",
        "LineNumber":  65,
        "Line":  "      const convertedImages: UniversalImage[] = result.data.map((img: UnifiedImageData) =\u003e ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\ProductionOrderImagesPrintButton.tsx",
        "LineNumber":  143,
        "Line":  "export default ProductionOrderImagesPrintButton"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  15,
        "Line":  "import UniversalImagePrint from \u0027./UniversalImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  16,
        "Line":  "import { imageService, type UnifiedImageData, type ImageQueryOptions } from \u0027../../services/images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  17,
        "Line":  "import type { UniversalImage, ImageContext } from \u0027./UniversalImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  18,
        "Line":  "import type { ImageQuery } from \u0027../../services/UniversalImageService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  24,
        "Line":  "interface SmartImagePrintButtonProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  45,
        "Line":  "const SmartImagePrintButton: React.FC\u003cSmartImagePrintButtonProps\u003e = ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  63,
        "Line":  "  const [images, setImages] = useState\u003cUniversalImage[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  64,
        "Line":  "  const [currentQuery, setCurrentQuery] = useState\u003cImageQuery\u003e({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  81,
        "Line":  "  const loadImages = async (query: any) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  93,
        "Line":  "      const queryOptions: ImageQueryOptions = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  102,
        "Line":  "      const result = await imageService.getImages(queryOptions)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  110,
        "Line":  "      const convertedImages: UniversalImage[] = result.data.map((img: UnifiedImageData) =\u003e ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\SmartImagePrintButton.tsx",
        "LineNumber":  339,
        "Line":  "export default SmartImagePrintButton"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  44,
        "Line":  "  type UnifiedImageData,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  45,
        "Line":  "  type ImagePrintOptions,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  46,
        "Line":  "  type ImagePrintResult,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  47,
        "Line":  "  type ImageContextType"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  48,
        "Line":  "} from \u0027../../services/images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  49,
        "Line":  "import { useImageManager } from \u0027../../hooks/images/useImageManager\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  57,
        "Line":  "export type ImageContext = ImageContextType"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  60,
        "Line":  "export interface UniversalImage extends Omit\u003cUnifiedImageData, \u0027id\u0027 | \u0027uploadedAt\u0027\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  64,
        "Line":  "  contextType: ImageContext"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  103,
        "Line":  "interface UniversalImagePrintProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  114,
        "Line":  "const UniversalImagePrint: React.FC\u003cUniversalImagePrintProps\u003e = ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  127,
        "Line":  "  const [selectedImages, setSelectedImages] = useState\u003c(number | string)[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  129,
        "Line":  "  const [printResult, setPrintResult] = useState\u003cImagePrintResult | null\u003e(null)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  135,
        "Line":  "  const getImageTemplate = () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  136,
        "Line":  "    const imageTemplate = STANDARD_PRINT_TEMPLATES.find(t =\u003e t.type === \u0027custom\u0027 \u0026\u0026 t.id.includes(\u0027image\u0027))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  141,
        "Line":  "  const [imagePrintOptions, setImagePrintOptions] = useState\u003cPartial\u003cImagePrintOptions\u003e\u003e(() =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  142,
        "Line":  "    const template = getImageTemplate()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  187,
        "Line":  "    type: \u0027image\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  206,
        "Line":  "  const getContextTitle = (contextType: ImageContext): string =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  219,
        "Line":  "  const validateImagePath = (imagePath: string): { isValid: boolean; error?: string } =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  226,
        "Line":  "      const parts = imagePath.split(\u0027,\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  253,
        "Line":  "  const convertToPrintFormat = (): PrintData[\u0027images\u0027] =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  254,
        "Line":  "    const selected = images.filter(img =\u003e selectedImages.includes(img.id))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  257,
        "Line":  "      const pathValidation = validateImagePath(image.path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  274,
        "Line":  "          contextType: getContextTitle(image.contextType),"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  286,
        "Line":  "    const printImages = convertToPrintFormat()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  314,
        "Line":  "        printType: \u0027universal-images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  320,
        "Line":  "  const validateImagesForPrint = (): { isValid: boolean; warnings: string[]; errors: string[] } =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  321,
        "Line":  "    const selected = images.filter(img =\u003e selectedImages.includes(img.id))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  326,
        "Line":  "      const pathValidation = validateImagePath(image.path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  358,
        "Line":  "    const validation = validateImagesForPrint()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  376,
        "Line":  "      const imageIds = selectedImages.map(id =\u003e id.toString())"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  379,
        "Line":  "      const result = await imagePrintService.printSpecificImages(imageIds, imagePrintOptions)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  422,
        "Line":  "      const selectedImageIds = selectedImages.map(id =\u003e id.toString())"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  423,
        "Line":  "      const selectedImagesData = images.filter(img =\u003e selectedImageIds.includes(img.id.toString()))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  432,
        "Line":  "      const result = await imagePrintService.previewPrint(queryOptions, imagePrintOptions)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  458,
        "Line":  "  const updatePrintOptions = async (newOptions: Partial\u003cImagePrintOptions\u003e) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  463,
        "Line":  "      const imageSettings = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  489,
        "Line":  "  const loadSavedImageSettings = async () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  491,
        "Line":  "      const response = await window.electronAPI.getSetting(\u0027image_print_settings\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  493,
        "Line":  "        const parsedSettings = JSON.parse(response.data) as Partial\u003cImagePrintOptions\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  519,
        "Line":  "        type: \u0027image\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  544,
        "Line":  "  const handleSelectImage = (imageId: string | number, checked: boolean) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  545,
        "Line":  "    const id = typeof imageId === \u0027string\u0027 ? parseInt(imageId) : imageId"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  663,
        "Line":  "                \u003cText type=\"secondary\"\u003eمحدد: {selectedImages.length}\u003c/Text\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrint.tsx",
        "LineNumber":  878,
        "Line":  "export default UniversalImagePrint"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrintDemo.tsx",
        "LineNumber":  27,
        "Line":  "import InvoiceImagesPrintButton from \u0027./InvoiceImagesPrintButton\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrintDemo.tsx",
        "LineNumber":  28,
        "Line":  "import ProductionOrderImagesPrintButton from \u0027./ProductionOrderImagesPrintButton\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrintDemo.tsx",
        "LineNumber":  29,
        "Line":  "import CustomerImagesPrintButton from \u0027./CustomerImagesPrintButton\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrintDemo.tsx",
        "LineNumber":  30,
        "Line":  "import SmartImagePrintButton from \u0027./SmartImagePrintButton\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrintDemo.tsx",
        "LineNumber":  35,
        "Line":  "const UniversalImagePrintDemo: React.FC = () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\common\\UniversalImagePrintDemo.tsx",
        "LineNumber":  416,
        "Line":  "export default UniversalImagePrintDemo"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageDebugger.tsx",
        "LineNumber":  4,
        "Line":  "import { diagnoseImageIssues, testImageLoad } from \u0027../../utils/imageUtils\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageDebugger.tsx",
        "LineNumber":  5,
        "Line":  "import ImageWithFallback from \u0027../common/ImageWithFallback\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageDebugger.tsx",
        "LineNumber":  11,
        "Line":  "const ImageDebugger: React.FC = () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageDebugger.tsx",
        "LineNumber":  12,
        "Line":  "  const [imageSrc, setImageSrc] = useState(\u0027\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageDebugger.tsx",
        "LineNumber":  22,
        "Line":  "      const result = diagnoseImageIssues(imageSrc)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageDebugger.tsx",
        "LineNumber":  50,
        "Line":  "      const result = await testImageLoad(imageSrc, 5000)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageDebugger.tsx",
        "LineNumber":  213,
        "Line":  "                        const target = e.target as HTMLImageElement"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageDebugger.tsx",
        "LineNumber":  247,
        "Line":  "export default ImageDebugger"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageStatusMonitor.tsx",
        "LineNumber":  15,
        "Line":  "interface ImageStatus {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageStatusMonitor.tsx",
        "LineNumber":  27,
        "Line":  "interface ImageStats {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageStatusMonitor.tsx",
        "LineNumber":  34,
        "Line":  "const ImageStatusMonitor: React.FC = () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageStatusMonitor.tsx",
        "LineNumber":  35,
        "Line":  "  const [images, setImages] = useState\u003cImageStatus[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageStatusMonitor.tsx",
        "LineNumber":  37,
        "Line":  "  const [stats, setStats] = useState\u003cImageStats\u003e({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageStatusMonitor.tsx",
        "LineNumber":  44,
        "Line":  "  const loadImageStatus = async () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageStatusMonitor.tsx",
        "LineNumber":  52,
        "Line":  "      const response = await (window.electronAPI as any).getImageStatus?.()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageStatusMonitor.tsx",
        "LineNumber":  58,
        "Line":  "      const imageData = response.data || []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\debug\\ImageStatusMonitor.tsx",
        "LineNumber":  261,
        "Line":  "export default ImageStatusMonitor"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  11,
        "Line":  "} from \u0027../images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  15,
        "Line":  "} from \u0027../../services/images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  16,
        "Line":  "import { useImageManager } from \u0027../../hooks/images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  17,
        "Line":  "import type { UnifiedImageData } from \u0027../../services/images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  25,
        "Line":  "export const ImageSystemExample: React.FC = () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  29,
        "Line":  "  const [currentImageIndex, setCurrentImageIndex] = useState(0)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  85,
        "Line":  "  const openImageViewer = (index: number = 0) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  95,
        "Line":  "      const success = await uploadImage(file)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  130,
        "Line":  "  const handleDeleteWithConfirm = async (imageId: string, imageName: string) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  131,
        "Line":  "    const confirmed = window.confirm(`هل تريد حذف الصورة \"${imageName}\"؟`)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  133,
        "Line":  "      const success = await deleteImage(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  143,
        "Line":  "  const handleSetPrimary = async (imageId: string) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  144,
        "Line":  "    const success = await setPrimaryImage(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  153,
        "Line":  "  const handleUpdateDescription = async (imageId: string, description: string) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  154,
        "Line":  "    const success = await updateImage(imageId, { description })"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  187,
        "Line":  "      const result = await imageService.getImageStatistics()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  233,
        "Line":  "              \u003cText type={hasImages ? \u0027success\u0027 : \u0027secondary\u0027}\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  356,
        "Line":  "const { images, uploadImage } = useImageManager({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  371,
        "Line":  "const { images } = useImageManager({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\examples\\ImageSystemExample.tsx",
        "LineNumber":  383,
        "Line":  "const { images, uploadImage } = useImageManager({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\finance\\CheckManagement.tsx",
        "LineNumber":  38,
        "Line":  "import CheckImageManager from \u0027../common/CheckImageManager\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\finance\\CheckManagement.tsx",
        "LineNumber":  60,
        "Line":  "  const [imageManagerVisible, setImageManagerVisible] = useState(false)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\finance\\CheckManagement.tsx",
        "LineNumber":  62,
        "Line":  "  const [selectedCheckForImages, setSelectedCheckForImages] = useState\u003cany\u003e(null)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\finance\\CheckManagement.tsx",
        "LineNumber":  252,
        "Line":  "  const handleManageImages = (check: any) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\finance\\CheckManagement.tsx",
        "LineNumber":  262,
        "Line":  "  const closeImageManager = () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\finance\\CheckManagement.tsx",
        "LineNumber":  272,
        "Line":  "  const handleScanComplete = (images: any[]) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  28,
        "Line":  "interface CheckImage {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  51,
        "Line":  "interface EnhancedCheckImagePrintProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  61,
        "Line":  "const EnhancedCheckImagePrint: React.FC\u003cEnhancedCheckImagePrintProps\u003e = ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  77,
        "Line":  "    type: \u0027image\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  88,
        "Line":  "  const [imageSettings, setImageSettings] = useState({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  96,
        "Line":  "  const validateImagePath = (imagePath: string): { isValid: boolean; error?: string } =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  103,
        "Line":  "      const parts = imagePath.split(\u0027,\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  130,
        "Line":  "  const convertImagesToPrintFormat = (): PrintData[\u0027images\u0027] =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  132,
        "Line":  "      const pathValidation = validateImagePath(image.image_path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  191,
        "Line":  "    const printImages = convertImagesToPrintFormat()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  219,
        "Line":  "        printType: \u0027check-images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  225,
        "Line":  "  const validateImagesForPrint = (): { isValid: boolean; warnings: string[]; errors: string[] } =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  230,
        "Line":  "      const pathValidation = validateImagePath(image.image_path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  267,
        "Line":  "    const validation = validateImagesForPrint()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\financial\\EnhancedCheckImagePrint.tsx",
        "LineNumber":  445,
        "Line":  "export default EnhancedCheckImagePrint"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  7,
        "Line":  "import type { UnifiedImageData } from \u0027../../services/images/ImageCoreService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  13,
        "Line":  "export interface EnhancedImageViewerProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  38,
        "Line":  "export const EnhancedImageViewer: React.FC\u003cEnhancedImageViewerProps\u003e = ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  62,
        "Line":  "  const [imageDataUrls, setImageDataUrls] = useState\u003cRecord\u003cstring, string\u003e\u003e({})"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  66,
        "Line":  "  const imageRef = useRef\u003cHTMLImageElement\u003e(null)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  72,
        "Line":  "    const newIndex = Math.max(0, Math.min(currentIndex, images.length - 1))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  93,
        "Line":  "  const loadCurrentImage = useCallback(async () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  94,
        "Line":  "    const currentImage = images[viewerState.currentIndex]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  99,
        "Line":  "      const result = await window.electronAPI.invoke(\u0027read-image-as-base64\u0027, currentImage.path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  177,
        "Line":  "    const newIndex = viewerState.currentIndex \u003c images.length - 1 "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  188,
        "Line":  "  const goToImage = useCallback((index: number) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  256,
        "Line":  "  const downloadImage = useCallback(async () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  260,
        "Line":  "      const image = images[viewerState.currentIndex]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  261,
        "Line":  "      const response = await fetch(`file://${image.path}`)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  333,
        "Line":  "  const handleImageLoad = useCallback(() =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  341,
        "Line":  "  const handleImageError = useCallback(() =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  349,
        "Line":  "  const currentImage = images[viewerState.currentIndex]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\EnhancedImageViewer.tsx",
        "LineNumber":  360,
        "Line":  "          \u003ch3 className=\"text-lg font-medium\"\u003e{currentImage?.name}\u003c/h3\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\index.ts",
        "LineNumber":  7,
        "Line":  "export { UnifiedImageManager } from \u0027./UnifiedImageManager\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\index.ts",
        "LineNumber":  8,
        "Line":  "export { EnhancedImageViewer } from \u0027./EnhancedImageViewer\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\index.ts",
        "LineNumber":  11,
        "Line":  "export { default as ImageGallery } from \u0027../common/ImageGallery\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\index.ts",
        "LineNumber":  12,
        "Line":  "export { default as ImageWithFallback } from \u0027../common/ImageWithFallback\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\index.ts",
        "LineNumber":  13,
        "Line":  "export { default as ImageSettings } from \u0027../common/ImageSettings\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\index.ts",
        "LineNumber":  16,
        "Line":  "export type { UnifiedImageManagerProps } from \u0027./UnifiedImageManager\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\index.ts",
        "LineNumber":  17,
        "Line":  "export type { EnhancedImageViewerProps } from \u0027./EnhancedImageViewer\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  9,
        "Line":  "  type UnifiedImageData, "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  10,
        "Line":  "  type ImageCategory, "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  11,
        "Line":  "  type ImageContextType,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  12,
        "Line":  "  type ImageUploadOptions "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  13,
        "Line":  "} from \u0027../../services/images/ImageCoreService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  15,
        "Line":  "import ImageWithFallback from \u0027../common/ImageWithFallback\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  17,
        "Line":  "export interface UnifiedImageManagerProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  19,
        "Line":  "  contextType: ImageContextType"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  43,
        "Line":  "export const UnifiedImageManager: React.FC\u003cUnifiedImageManagerProps\u003e = ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  59,
        "Line":  "  const [images, setImages] = useState\u003cUnifiedImageData[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  60,
        "Line":  "  const [primaryImage, setPrimaryImage] = useState\u003cUnifiedImageData | null\u003e(null)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  68,
        "Line":  "  const imageService = useRef(ImageCoreService.getInstance())"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  93,
        "Line":  "  const loadImages = useCallback(async () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  98,
        "Line":  "      const result = await imageService.current.getImages({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  107,
        "Line":  "        const imageList = result.data as UnifiedImageData[]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  111,
        "Line":  "        const primary = imageList.find(img =\u003e img.isPrimary) || null"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  126,
        "Line":  "  }, [category, contextType, contextId, onImagesChange, onPrimaryImageChange])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  171,
        "Line":  "      const result = await imageService.current.uploadMultipleImages("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  204,
        "Line":  "  }, [category, contextType, contextId, uploadOptions, loadImages])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  209,
        "Line":  "  const handleDeleteImage = useCallback(async (imageId: string) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  215,
        "Line":  "      const result = await imageService.current.deleteImage(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  233,
        "Line":  "  const handleSetPrimary = useCallback(async (imageId: string) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  237,
        "Line":  "      const result = await imageService.current.setPrimaryImage(imageId, contextType, contextId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  250,
        "Line":  "  }, [contextType, contextId, loadImages])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  265,
        "Line":  "      file.type.startsWith(\u0027image/\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  281,
        "Line":  "    \u003cdiv className={`unified-image-manager ${className}`}\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\images\\UnifiedImageManager.tsx",
        "LineNumber":  356,
        "Line":  "      \u003cdiv className=\"images-grid\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  27,
        "Line":  "import type { ItemImage } from \u0027../../types/global\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  29,
        "Line":  "interface EnhancedItemImagePrintProps {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  41,
        "Line":  "const EnhancedItemImagePrint: React.FC\u003cEnhancedItemImagePrintProps\u003e = ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  59,
        "Line":  "    type: \u0027image\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  70,
        "Line":  "  const [imageSettings, setImageSettings] = useState({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  78,
        "Line":  "  const validateImagePath = (imagePath: string): { isValid: boolean; error?: string } =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  85,
        "Line":  "      const parts = imagePath.split(\u0027,\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  112,
        "Line":  "  const convertImagesToPrintFormat = (): PrintData[\u0027images\u0027] =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  114,
        "Line":  "      const pathValidation = validateImagePath(image.image_path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  143,
        "Line":  "    const printImages = convertImagesToPrintFormat()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  156,
        "Line":  "        printType: \u0027item-images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  162,
        "Line":  "  const validateImagesForPrint = (): { isValid: boolean; warnings: string[]; errors: string[] } =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  167,
        "Line":  "      const pathValidation = validateImagePath(image.image_path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  199,
        "Line":  "    const validation = validateImagesForPrint()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\EnhancedItemImagePrint.tsx",
        "LineNumber":  394,
        "Line":  "export default EnhancedItemImagePrint"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\ItemManagement.tsx",
        "LineNumber":  17,
        "Line":  "import { UnifiedImageManager } from \u0027../images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\ItemManagement.tsx",
        "LineNumber":  19,
        "Line":  "import ImageGallery from \u0027../common/ImageGallery\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\ItemManagement.tsx",
        "LineNumber":  61,
        "Line":  "  const [imageManagerVisible, setImageManagerVisible] = useState(false)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\ItemManagement.tsx",
        "LineNumber":  63,
        "Line":  "  const [selectedItemForImages, setSelectedItemForImages] = useState\u003cItem | null\u003e(null)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\ItemManagement.tsx",
        "LineNumber":  64,
        "Line":  "  const [itemImages, setItemImages] = useState\u003cany[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\ItemManagement.tsx",
        "LineNumber":  227,
        "Line":  "  const handleManageImages = (item: Item) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\ItemManagement.tsx",
        "LineNumber":  236,
        "Line":  "      const imageService = (await import(\u0027../images\u0027)).ImageCoreService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\ItemManagement.tsx",
        "LineNumber":  239,
        "Line":  "      const images = await imageService.getImages({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\ItemManagement.tsx",
        "LineNumber":  246,
        "Line":  "      const galleryImages = images.map(img =\u003e ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\inventory\\ItemManagement.tsx",
        "LineNumber":  261,
        "Line":  "  const closeImageManager = () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureEnhancedFeatures.tsx",
        "LineNumber":  35,
        "Line":  "import { furnitureImageService } from \u0027./FurnitureImageService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureEnhancedFeatures.tsx",
        "LineNumber":  58,
        "Line":  "  const [imageStats, setImageStats] = useState({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureEnhancedFeatures.tsx",
        "LineNumber":  92,
        "Line":  "    const imageStatsData = furnitureImageService.getImageStats()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  3,
        "Line":  "export interface FurnitureImage {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  23,
        "Line":  "export interface FurnitureImageStats {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  32,
        "Line":  "class FurnitureImageService {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  35,
        "Line":  "  private allowedTypes = [\u0027image/jpeg\u0027, \u0027image/png\u0027, \u0027image/gif\u0027, \u0027image/webp\u0027]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  66,
        "Line":  "      const stored = localStorage.getItem(\u0027furniture_images\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  124,
        "Line":  "      const img = new Image()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  149,
        "Line":  "    const compressedBlob = await this.compressImage(file, 200, 0.7)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  222,
        "Line":  "        const existingImages = await this.getImagesByItemFromDatabase(options.orderId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  229,
        "Line":  "      const compressedBlob = await this.compressImage(file)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  230,
        "Line":  "      const imageUrl = compressedBlob ? await this.fileToBase64(new File([compressedBlob], file.name, { type: file.type })) : \u0027\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  233,
        "Line":  "      const imageData = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  248,
        "Line":  "      const result = await window.electronAPI.uploadProductionOrderImage(imageData)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  279,
        "Line":  "      const existingImages = await this.getImagesByItem(options.orderId, options.materialId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  285,
        "Line":  "      const compressedBlob = await this.compressImage(file)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  287,
        "Line":  "      const imageUrl = compressedBlob ? await this.fileToBase64(new File([compressedBlob], file.name, { type: file.type })) : \u0027\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  290,
        "Line":  "      const imageId = `furniture_img_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  293,
        "Line":  "      const newImage: FurnitureImage = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  351,
        "Line":  "    const uploadedImages: string[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  355,
        "Line":  "      const result = await this.uploadImage(file, category, options)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  396,
        "Line":  "  private async getImagesByItemFromDatabase(orderId: string): Promise\u003cFurnitureImage[]\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  398,
        "Line":  "      const result = await window.electronAPI.getProductionOrderImages(parseInt(orderId))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  407,
        "Line":  "          type: dbImage.file_type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  414,
        "Line":  "          category: this.mapCategoryFromDatabase(dbImage.category) as \u0027order\u0027 | \u0027material\u0027 | \u0027process\u0027 | \u0027quality\u0027 | \u0027other\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  461,
        "Line":  "    const image = this.images.find(img =\u003e img.id === imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  472,
        "Line":  "    const image = this.images.find(img =\u003e img.id === imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  501,
        "Line":  "    const activeImages = this.images.filter(img =\u003e img.isActive)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  504,
        "Line":  "    const imagesByCategory: Record\u003cstring, number\u003e = {}"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  510,
        "Line":  "    const imagesByType: Record\u003cstring, number\u003e = {}"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  512,
        "Line":  "      imagesByType[img.type] = (imagesByType[img.type] || 0) + 1"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  516,
        "Line":  "    const recentImages = activeImages"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  521,
        "Line":  "    const largestImages = activeImages"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  526,
        "Line":  "    const totalSize = activeImages.reduce((sum, img) =\u003e sum + img.size, 0)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  548,
        "Line":  "  exportImageList(): string {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  549,
        "Line":  "    const activeImages = this.images.filter(img =\u003e img.isActive)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  550,
        "Line":  "    const exportData = activeImages.map(img =\u003e ({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  565,
        "Line":  "export const furnitureImageService = new FurnitureImageService()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\FurnitureImageService.ts",
        "LineNumber":  566,
        "Line":  "export { FurnitureImageService }"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ImageMigrationComponent.tsx",
        "LineNumber":  48,
        "Line":  "const ImageMigrationComponent: React.FC = () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ImageMigrationComponent.tsx",
        "LineNumber":  82,
        "Line":  "      const result = await window.electronAPI.migrateImagesToDatabase(localStorageData)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ImageMigrationComponent.tsx",
        "LineNumber":  323,
        "Line":  "export default ImageMigrationComponent"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrderDetailsModal.tsx",
        "LineNumber":  181,
        "Line":  "          const imagesResult = await window.electronAPI.getProductionOrderImages?.(orderDetails.id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrderDetailsModal.tsx",
        "LineNumber":  194,
        "Line":  "          const itemImagesResult = await window.electronAPI.getItemImages?.(orderDetails.item_id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrderDetailsModal.tsx",
        "LineNumber":  204,
        "Line":  "      const allImages = ["
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  75,
        "Line":  "import { furnitureImageService } from \u0027./FurnitureImageService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  76,
        "Line":  "import ImageMigrationComponent from \u0027./ImageMigrationComponent\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  227,
        "Line":  "  const [imagePreviewVisible, setImagePreviewVisible] = useState(false)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  228,
        "Line":  "  const [previewImageUrl, setPreviewImageUrl] = useState(\u0027\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  229,
        "Line":  "  const [uploadedImages, setUploadedImages] = useState\u003cstring[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  230,
        "Line":  "  const [imageUploadModalVisible, setImageUploadModalVisible] = useState(false)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  234,
        "Line":  "  const [currentOrderImages, setCurrentOrderImages] = useState\u003cany[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  235,
        "Line":  "  const [loadingImages, setLoadingImages] = useState(false)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  273,
        "Line":  "      const orderImages = await getOrderImages(order.id.toString())"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  277,
        "Line":  "      const printImages = orderImages.map(image =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  291,
        "Line":  "            imageType: image.category,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  560,
        "Line":  "      const orderImages = await getOrderImages(order.id.toString())"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  564,
        "Line":  "      const printImages = orderImages.map(image =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  578,
        "Line":  "            imageType: image.category,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  726,
        "Line":  "  const validateImageFile = (file: File): boolean =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  734,
        "Line":  "    const allowedTypes = [\u0027image/jpeg\u0027, \u0027image/png\u0027, \u0027image/gif\u0027, \u0027image/webp\u0027]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  743,
        "Line":  "  const handleImageUpload = async (file: File, orderId: string, orderNumber: string) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  752,
        "Line":  "      const result = await furnitureImageService.uploadImage(file, \u0027order\u0027, {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  782,
        "Line":  "  const handleImagePreview = (imageUrl: string) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  787,
        "Line":  "  const handleDeleteImage = async (imageId: string) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  791,
        "Line":  "      const success = await furnitureImageService.deleteImage(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  810,
        "Line":  "  const getOrderImages = async (orderId: string) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  815,
        "Line":  "  const loadOrderImages = async (orderId: string) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  818,
        "Line":  "      const images = await getOrderImages(orderId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  1997,
        "Line":  "                  const orderImages = await getOrderImages(record.id.toString())"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  2001,
        "Line":  "                  const printImages = orderImages.map(image =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  2015,
        "Line":  "                        imageType: image.category,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\furniture\\ProductionOrdersManagement.tsx",
        "LineNumber":  3304,
        "Line":  "                  const image = furnitureImageService.getImageById(id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintEnhancedFeatures.tsx",
        "LineNumber":  31,
        "Line":  "import { ImageService, ImageData } from \u0027../../common/ImageService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintEnhancedFeatures.tsx",
        "LineNumber":  43,
        "Line":  "  const [images, setImages] = useState\u003cImageData[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintEnhancedFeatures.tsx",
        "LineNumber":  63,
        "Line":  "    const allImages = ImageService.getAllImages()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintEnhancedFeatures.tsx",
        "LineNumber":  64,
        "Line":  "    const paintImages = allImages.filter(img =\u003e img.category === \u0027paint\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintEnhancedFeatures.tsx",
        "LineNumber":  85,
        "Line":  "  const imageStats = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintEnhancedFeatures.tsx",
        "LineNumber":  89,
        "Line":  "    byType: images.reduce((acc, img) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintEnhancedFeatures.tsx",
        "LineNumber":  137,
        "Line":  "  const recentImages = images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintOrdersManagement.tsx",
        "LineNumber":  182,
        "Line":  "  const [imagePreviewVisible, setImagePreviewVisible] = useState(false)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintOrdersManagement.tsx",
        "LineNumber":  183,
        "Line":  "  const [previewImage, setPreviewImage] = useState(\u0027\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintOrdersManagement.tsx",
        "LineNumber":  340,
        "Line":  "  const handleImageUpload = (info: any, itemIndex: number) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintOrdersManagement.tsx",
        "LineNumber":  363,
        "Line":  "  const handleRemoveImage = (file: any, itemIndex: number) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  35,
        "Line":  "import { ImageService } from \u0027../../common/ImageService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  98,
        "Line":  "  const [imagePreviewVisible, setImagePreviewVisible] = useState(false)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  99,
        "Line":  "  const [previewImage, setPreviewImage] = useState(\u0027\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  101,
        "Line":  "  const [currentPaintTypeImages, setCurrentPaintTypeImages] = useState\u003cstring[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  245,
        "Line":  "  const handleImageUpload = async (info: any) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  250,
        "Line":  "    const uploadedImages: string[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  254,
        "Line":  "          const imageData = await ImageService.uploadImage("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  269,
        "Line":  "    setCurrentPaintTypeImages(uploadedImages)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  279,
        "Line":  "  const handleRemoveImage = (file: any) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  280,
        "Line":  "    const updatedImages = currentPaintTypeImages.filter("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  283,
        "Line":  "    setCurrentPaintTypeImages(updatedImages)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  343,
        "Line":  "        const images = ImageService.getImagesByReference(\u0027paint_type\u0027, record.id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\production\\paint\\PaintTypesManagement.tsx",
        "LineNumber":  714,
        "Line":  "                setCurrentPaintTypeImages([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\Dashboard.tsx",
        "LineNumber":  103,
        "Line":  "import ImageDebugger from \u0027./debug/ImageDebugger\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\Dashboard.tsx",
        "LineNumber":  104,
        "Line":  "import ImageStatusMonitor from \u0027./debug/ImageStatusMonitor\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\Dashboard.tsx",
        "LineNumber":  108,
        "Line":  "import { ImageSystemExample } from \u0027./examples/ImageSystemExample\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\components\\Dashboard.tsx",
        "LineNumber":  109,
        "Line":  "import ImageMigrationTool from \u0027./admin/ImageMigrationTool\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\index.ts",
        "LineNumber":  7,
        "Line":  "export { useImageManager, useImages } from \u0027./useImageManager\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\index.ts",
        "LineNumber":  13,
        "Line":  "} from \u0027./useImageManager\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  9,
        "Line":  "  type UnifiedImageData, "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  10,
        "Line":  "  type ImageCategory, "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  11,
        "Line":  "  type ImageContextType,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  12,
        "Line":  "  type ImageUploadOptions,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  13,
        "Line":  "  type ImageQueryOptions "
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  14,
        "Line":  "} from \u0027../../services/images/ImageCoreService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  17,
        "Line":  "export interface UseImageManagerOptions {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  19,
        "Line":  "  contextType: ImageContextType"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  25,
        "Line":  "export interface UseImageManagerReturn {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  53,
        "Line":  "export const useImageManager = (options: UseImageManagerOptions): UseImageManagerReturn =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  63,
        "Line":  "  const [images, setImages] = useState\u003cUnifiedImageData[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  64,
        "Line":  "  const [primaryImage, setPrimaryImage] = useState\u003cUnifiedImageData | null\u003e(null)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  70,
        "Line":  "  const imageService = useRef(ImageCoreService.getInstance())"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  104,
        "Line":  "  const loadImages = useCallback(async () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  109,
        "Line":  "      const queryOptions: ImageQueryOptions = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  117,
        "Line":  "      const result = await imageService.current.getImages(queryOptions)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  120,
        "Line":  "        const imageList = result.data as UnifiedImageData[]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  124,
        "Line":  "        const primary = imageList.find(img =\u003e img.isPrimary) || null"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  142,
        "Line":  "  const uploadImage = useCallback(async (file: File): Promise\u003cboolean\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  147,
        "Line":  "      const result = await imageService.current.uploadImage("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  169,
        "Line":  "  }, [category, contextType, contextId, uploadOptions, loadImages])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  174,
        "Line":  "  const uploadMultipleImages = useCallback(async (files: File[]): Promise\u003cboolean\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  179,
        "Line":  "      const result = await imageService.current.uploadMultipleImages("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  201,
        "Line":  "  }, [category, contextType, contextId, uploadOptions, loadImages])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  206,
        "Line":  "  const deleteImage = useCallback(async (imageId: string): Promise\u003cboolean\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  211,
        "Line":  "      const result = await imageService.current.deleteImage(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  232,
        "Line":  "  const setPrimaryImageHandler = useCallback(async (imageId: string): Promise\u003cboolean\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  237,
        "Line":  "      const result = await imageService.current.setPrimaryImage(imageId, contextType, contextId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  253,
        "Line":  "  }, [contextType, contextId, loadImages])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  258,
        "Line":  "  const updateImage = useCallback(async ("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  266,
        "Line":  "      const result = await imageService.current.updateImage(imageId, updates)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  301,
        "Line":  "  const canUploadMore = useCallback((maxImages: number = 10): boolean =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  306,
        "Line":  "  const totalImages = images.length"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  307,
        "Line":  "  const totalSize = images.reduce((sum, img) =\u003e sum + img.size, 0)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  308,
        "Line":  "  const hasImages = images.length \u003e 0"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  339,
        "Line":  "export const useImages = ("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  341,
        "Line":  "  contextType: ImageContextType,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  345,
        "Line":  "  const [images, setImages] = useState\u003cUnifiedImageData[]\u003e([])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  349,
        "Line":  "  const imageService = useRef(ImageCoreService.getInstance())"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  351,
        "Line":  "  const loadImages = useCallback(async () =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\hooks\\images\\useImageManager.ts",
        "LineNumber":  358,
        "Line":  "      const result = await imageService.current.getImages({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\EnhancedImageValidator.ts",
        "LineNumber":  6,
        "Line":  "import { imageErrorHandler, ImageErrorType } from \u0027./ImageErrorHandler\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\EnhancedImageValidator.ts",
        "LineNumber":  40,
        "Line":  "export class EnhancedImageValidator {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\EnhancedImageValidator.ts",
        "LineNumber":  123,
        "Line":  "      const imageError = imageErrorHandler.handleError("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\EnhancedImageValidator.ts",
        "LineNumber":  225,
        "Line":  "      const img = new Image()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\EnhancedImageValidator.ts",
        "LineNumber":  277,
        "Line":  "    const aspectRatio = imageInfo.width / imageInfo.height"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\EnhancedImageValidator.ts",
        "LineNumber":  287,
        "Line":  "    return mimeType === \u0027image/png\u0027 || mimeType === \u0027image/gif\u0027 || mimeType === \u0027image/webp\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\EnhancedImageValidator.ts",
        "LineNumber":  294,
        "Line":  "    return mimeType === \u0027image/gif\u0027 || mimeType === \u0027image/webp\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\EnhancedImageValidator.ts",
        "LineNumber":  319,
        "Line":  "export const enhancedImageValidator = EnhancedImageValidator.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCacheService.ts",
        "LineNumber":  7,
        "Line":  "import type { UnifiedImageData } from \u0027./ImageCoreService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCacheService.ts",
        "LineNumber":  28,
        "Line":  "export class ImageCacheService {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCacheService.ts",
        "LineNumber":  367,
        "Line":  "      const settings = localStorage.getItem(\u0027image_cache_settings\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  7,
        "Line":  "import { ImageProcessingService } from \u0027./ImageProcessingService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  8,
        "Line":  "import { ImageCacheService } from \u0027./ImageCacheService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  9,
        "Line":  "import { ImageValidationService } from \u0027./ImageValidationService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  10,
        "Line":  "import { ImageStorageService } from \u0027./ImageStorageService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  13,
        "Line":  "export interface UnifiedImageData {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  24,
        "Line":  "  contextType: ImageContextType"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  36,
        "Line":  "export type ImageCategory = \u0027inventory\u0027 | \u0027production\u0027 | \u0027sales\u0027 | \u0027checks\u0027 | \u0027general\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  37,
        "Line":  "export type ImageContextType = \u0027item\u0027 | \u0027production_order\u0027 | \u0027invoice\u0027 | \u0027check\u0027 | \u0027customer\u0027 | \u0027supplier\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  39,
        "Line":  "export interface ImageUploadOptions {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  52,
        "Line":  "export interface ImageQueryOptions {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  54,
        "Line":  "  contextType?: ImageContextType"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  68,
        "Line":  "export interface ImageOperationResult {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  78,
        "Line":  "export class ImageCoreService {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  129,
        "Line":  "    contextType: ImageContextType,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  143,
        "Line":  "      const processedImage = await this.processingService.processImage(file, options)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  149,
        "Line":  "      const savedImage = await this.storageService.saveImage({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  180,
        "Line":  "    contextType: ImageContextType,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  243,
        "Line":  "      const result = await this.storageService.getImages(options)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  264,
        "Line":  "      const cachedImage = await this.cacheService.getFromCache(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  270,
        "Line":  "      const result = await this.storageService.getImageById(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  295,
        "Line":  "      const result = await this.storageService.updateImage(imageId, updates)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  319,
        "Line":  "      const result = await this.storageService.deleteImage(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  323,
        "Line":  "        await this.cacheService.removeFromCache(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  341,
        "Line":  "    contextType: ImageContextType,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  347,
        "Line":  "      const result = await this.storageService.setPrimaryImage(imageId, contextType, contextId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageCoreService.ts",
        "LineNumber":  400,
        "Line":  "      const result = await this.storageService.getImageStatistics()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageErrorHandler.ts",
        "LineNumber":  8,
        "Line":  "export enum ImageErrorType {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageErrorHandler.ts",
        "LineNumber":  21,
        "Line":  "export interface ImageError {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageErrorHandler.ts",
        "LineNumber":  22,
        "Line":  "  type: ImageErrorType"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageErrorHandler.ts",
        "LineNumber":  39,
        "Line":  "export class ImageErrorHandler {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageErrorHandler.ts",
        "LineNumber":  58,
        "Line":  "    type: ImageErrorType = ImageErrorType.UNKNOWN_ERROR,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageErrorHandler.ts",
        "LineNumber":  62,
        "Line":  "    const imageError: ImageError = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageErrorHandler.ts",
        "LineNumber":  278,
        "Line":  "    const stats = {} as Record\u003cImageErrorType, number\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageErrorHandler.ts",
        "LineNumber":  308,
        "Line":  "export const imageErrorHandler = ImageErrorHandler.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePerformanceOptimizer.ts",
        "LineNumber":  36,
        "Line":  "export class ImagePerformanceOptimizer {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePerformanceOptimizer.ts",
        "LineNumber":  330,
        "Line":  "      const img = new Image()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePerformanceOptimizer.ts",
        "LineNumber":  346,
        "Line":  "        const compressedData = canvas.toDataURL(\u0027image/jpeg\u0027, quality)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePerformanceOptimizer.ts",
        "LineNumber":  416,
        "Line":  "export const imagePerformanceOptimizer = ImagePerformanceOptimizer.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  7,
        "Line":  "import { ImageCoreService } from \u0027./ImageCoreService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  8,
        "Line":  "import { ImageProcessingService } from \u0027./ImageProcessingService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  9,
        "Line":  "import { ImageCacheService } from \u0027./ImageCacheService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  17,
        "Line":  "} from \u0027./ImageCoreService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  18,
        "Line":  "import type { UniversalImage } from \u0027../../components/common/UniversalImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  22,
        "Line":  "export interface PrintableImageData extends UnifiedImageData {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  35,
        "Line":  "export interface ImagePrintOptions {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  64,
        "Line":  "export interface ImagePrintResult {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  77,
        "Line":  "export class ImagePrintService {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  161,
        "Line":  "      const imagesResult = await this.imageService.getImages(queryOptions)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  173,
        "Line":  "      const images = imagesResult.data as UnifiedImageData[]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  218,
        "Line":  "      const images: UnifiedImageData[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  221,
        "Line":  "      for (const imageId of imageIds) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  222,
        "Line":  "        const imageResult = await this.imageService.getImageById(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  242,
        "Line":  "      const result = await this.processAndPrintImages(images, options, startTime)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  277,
        "Line":  "      const processedImages: PrintableImageData[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  280,
        "Line":  "        for (const image of images) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  282,
        "Line":  "            const optimizedImage = await this.optimizeImageForPrint(image, options)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  311,
        "Line":  "      const printData = await this.createPrintData(processedImages, options)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  315,
        "Line":  "        type: \u0027image\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  316,
        "Line":  "        subType: this.getContextFromImages(processedImages),"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  363,
        "Line":  "      const cacheKey = `print_optimized_${image.id}_${options.quality}_${options.dpi}`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  367,
        "Line":  "        const cachedImage = await this.cacheService.getFromCache(cacheKey)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  375,
        "Line":  "      const optimizedImage: PrintableImageData = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  408,
        "Line":  "    const contextInfo = this.getContextInfo(images)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  411,
        "Line":  "      type: \u0027image\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  461,
        "Line":  "    const firstImage = images[0]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  462,
        "Line":  "    const contextType = firstImage.contextType"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  491,
        "Line":  "  private getContextDisplayName(contextType: ImageContextType): string {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  492,
        "Line":  "    const contextNames: Record\u003cImageContextType, string\u003e = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  506,
        "Line":  "  private getContextFromImages(images: PrintableImageData[]): string {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  525,
        "Line":  "      const imagesResult = await this.imageService.getImages(queryOptions)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  530,
        "Line":  "      const images = imagesResult.data as UnifiedImageData[]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  544,
        "Line":  "        type: \u0027image\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  545,
        "Line":  "        subType: this.getContextFromImages(images as PrintableImageData[]),"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  570,
        "Line":  "    const validImages: UniversalImage[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  572,
        "Line":  "    for (const image of images) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  588,
        "Line":  "          const base64 = await window.electronAPI.readFileAsBase64(image.path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  591,
        "Line":  "            let mimeType = \u0027image/png\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  592,
        "Line":  "            const extension = image.path.toLowerCase().split(\u0027.\u0027).pop()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  596,
        "Line":  "                mimeType = \u0027image/jpeg\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  599,
        "Line":  "                mimeType = \u0027image/png\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  602,
        "Line":  "                mimeType = \u0027image/gif\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  605,
        "Line":  "                mimeType = \u0027image/webp\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  610,
        "Line":  "            const updatedImage: UniversalImage = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImagePrintService.ts",
        "LineNumber":  637,
        "Line":  "      const testResult = await this.imageService.getImages({"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  9,
        "Line":  "import type { ImageUploadOptions } from \u0027./ImageCoreService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  14,
        "Line":  "export interface ProcessedImageData {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  37,
        "Line":  "export class ImageProcessingService {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  104,
        "Line":  "      const img = await this.loadImage(file)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  115,
        "Line":  "      const processedDataUrl = await this.compressImage("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  143,
        "Line":  "      const result: ProcessedImageData = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  176,
        "Line":  "      const img = new Image()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  293,
        "Line":  "      const img = new Image()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  365,
        "Line":  "        const batchPromises = batch.map(file =\u003e this.processImage(file, options))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  445,
        "Line":  "      const img = await this.loadImage(file)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  467,
        "Line":  "      const result: ProcessedImageData = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  562,
        "Line":  "    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  563,
        "Line":  "    const data = imageData.data"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  593,
        "Line":  "    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageProcessingService.ts",
        "LineNumber":  594,
        "Line":  "    const data = imageData.data"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  13,
        "Line":  "} from \u0027./ImageCoreService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  14,
        "Line":  "import type { ProcessedImageData } from \u0027./ImageProcessingService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  16,
        "Line":  "export interface SaveImageData {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  20,
        "Line":  "  contextType: ImageContextType"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  25,
        "Line":  "export interface ImageStatistics {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  29,
        "Line":  "  byContextType: Record\u003cImageContextType, number\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  39,
        "Line":  "export class ImageStorageService {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  86,
        "Line":  "      const imageId = this.generateImageId()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  89,
        "Line":  "      const imagePath = await this.generateImagePath(imageId, data.file.name, data.category)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  102,
        "Line":  "      const imageData: UnifiedImageData = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  157,
        "Line":  "      const query = this.buildImageQuery(options)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  167,
        "Line":  "      const images = result.data.map((row: any) =\u003e this.mapRowToImageData(row))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  188,
        "Line":  "        SELECT * FROM unified_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  192,
        "Line":  "      const result = await window.electronAPI.invoke(\u0027database-query\u0027, query, [imageId])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  202,
        "Line":  "      const imageData = this.mapRowToImageData(result.data[0])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  294,
        "Line":  "      const imageResult = await this.getImageById(imageId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  299,
        "Line":  "      const imageData = imageResult.data as UnifiedImageData"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  318,
        "Line":  "      const result = await window.electronAPI.invoke(\u0027database-query\u0027, query, [imageId])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  342,
        "Line":  "    contextType: ImageContextType,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  364,
        "Line":  "      const result = await window.electronAPI.invoke(\u0027database-query\u0027, setPrimaryQuery, [imageId])"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  399,
        "Line":  "        FROM unified_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  413,
        "Line":  "        FROM unified_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  419,
        "Line":  "      const byCategory: Record\u003cImageCategory, number\u003e = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  436,
        "Line":  "        FROM unified_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  442,
        "Line":  "      const byContextType: Record\u003cImageContextType, number\u003e = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  453,
        "Line":  "          byContextType[row.context_type as ImageContextType] = row.count"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  458,
        "Line":  "      const largestImageQuery = `"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  460,
        "Line":  "        FROM unified_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  462,
        "Line":  "          SELECT MAX(size) FROM unified_images WHERE is_active = 1"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  467,
        "Line":  "      const largestResult = await window.electronAPI.invoke(\u0027database-query\u0027, largestImageQuery)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  468,
        "Line":  "      const largestImage = largestResult.success \u0026\u0026 largestResult.data.length \u003e 0"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  473,
        "Line":  "      const oldestImageQuery = `"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  475,
        "Line":  "        FROM unified_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  481,
        "Line":  "      const newestImageQuery = `"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  483,
        "Line":  "        FROM unified_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  494,
        "Line":  "      const oldestImage = oldestResult.success \u0026\u0026 oldestResult.data.length \u003e 0"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  498,
        "Line":  "      const newestImage = newestResult.success \u0026\u0026 newestResult.data.length \u003e 0"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  502,
        "Line":  "      const statistics: ImageStatistics = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  535,
        "Line":  "      const result = await window.electronAPI.invoke(\u0027create-image-directories\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  625,
        "Line":  "    const fileName = `${imageId}${extension}`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  638,
        "Line":  "    const fileName = `${imageId}_thumb${extension}`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  646,
        "Line":  "    const result = await window.electronAPI.invoke(\u0027save-image-file\u0027, path, dataUrl)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  656,
        "Line":  "    const result = await window.electronAPI.invoke(\u0027delete-image-file\u0027, path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  699,
        "Line":  "      SELECT * FROM unified_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  798,
        "Line":  "      contextType: row.context_type as ImageContextType,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  860,
        "Line":  "        FROM unified_images"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageStorageService.ts",
        "LineNumber":  871,
        "Line":  "        await this.setPrimaryImage(imageData.id, imageData.contextType, imageData.contextId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageValidationService.ts",
        "LineNumber":  7,
        "Line":  "import type { ImageUploadOptions } from \u0027./ImageCoreService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageValidationService.ts",
        "LineNumber":  36,
        "Line":  "export class ImageValidationService {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageValidationService.ts",
        "LineNumber":  136,
        "Line":  "      const dimensionsValidation = await this.validateImageDimensions(file)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageValidationService.ts",
        "LineNumber":  205,
        "Line":  "  private validateFileType(file: File, options: ImageUploadOptions): ValidationResult {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageValidationService.ts",
        "LineNumber":  207,
        "Line":  "    if (!file.type || !file.type.startsWith(\u0027image/\u0027)) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageValidationService.ts",
        "LineNumber":  270,
        "Line":  "      const dimensions = await this.getImageDimensions(file)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageValidationService.ts",
        "LineNumber":  333,
        "Line":  "      if (file.type === \u0027image/svg+xml\u0027) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageValidationService.ts",
        "LineNumber":  405,
        "Line":  "      const img = new Image()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\ImageValidationService.ts",
        "LineNumber":  426,
        "Line":  "    const dimensions = await this.getImageDimensions(file)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  7,
        "Line":  "export { ImageCoreService } from \u0027./ImageCoreService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  8,
        "Line":  "export { ImageProcessingService } from \u0027./ImageProcessingService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  9,
        "Line":  "export { ImageCacheService } from \u0027./ImageCacheService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  10,
        "Line":  "export { ImageValidationService } from \u0027./ImageValidationService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  11,
        "Line":  "export { ImageStorageService } from \u0027./ImageStorageService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  12,
        "Line":  "export { ImagePrintService } from \u0027./ImagePrintService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  15,
        "Line":  "export { ImageErrorHandler, imageErrorHandler } from \u0027./ImageErrorHandler\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  16,
        "Line":  "export { EnhancedImageValidator, enhancedImageValidator } from \u0027./EnhancedImageValidator\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  17,
        "Line":  "export { ImagePerformanceOptimizer, imagePerformanceOptimizer } from \u0027./ImagePerformanceOptimizer\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  20,
        "Line":  "import { ImageCoreService } from \u0027./ImageCoreService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  21,
        "Line":  "import { ImagePrintService } from \u0027./ImagePrintService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  31,
        "Line":  "} from \u0027./ImageCoreService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  37,
        "Line":  "} from \u0027./ImageProcessingService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  44,
        "Line":  "} from \u0027./ImageValidationService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  50,
        "Line":  "} from \u0027./ImageStorageService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  55,
        "Line":  "} from \u0027./ImageCacheService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  62,
        "Line":  "} from \u0027./ImagePrintService\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  69,
        "Line":  "} from \u0027./ImageErrorHandler\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  74,
        "Line":  "} from \u0027./EnhancedImageValidator\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  79,
        "Line":  "} from \u0027./ImagePerformanceOptimizer\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  87,
        "Line":  "export const imageService = ImageCoreService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  93,
        "Line":  "export const imagePrintService = ImagePrintService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  99,
        "Line":  "export const initializeImageServices = async (): Promise\u003cvoid\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\index.ts",
        "LineNumber":  107,
        "Line":  "export const cleanupImageServices = async (): Promise\u003cvoid\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  27,
        "Line":  "    const coreService = ImageCoreService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  28,
        "Line":  "    const cacheService = ImageCacheService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  29,
        "Line":  "    const validationService = ImageValidationService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  30,
        "Line":  "    const processingService = ImageProcessingService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  31,
        "Line":  "    const storageService = ImageStorageService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  54,
        "Line":  "    const createTableResult = await window.electronAPI.invoke(\u0027create-unified-images-table\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  60,
        "Line":  "    const createIndexesResult = await window.electronAPI.invoke(\u0027create-image-indexes\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  66,
        "Line":  "    const testQuery = \u0027SELECT COUNT(*) as count FROM unified_images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  86,
        "Line":  "export const testImageProcessing = async (): Promise\u003cboolean\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  90,
        "Line":  "    const processingService = ImageProcessingService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  115,
        "Line":  "    const testFile = new File([blob], \u0027test.jpg\u0027, { type: \u0027image/jpeg\u0027 })"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  116,
        "Line":  "    const processedResult = await processingService.processImage(testFile, {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  145,
        "Line":  "    const validationService = ImageValidationService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  148,
        "Line":  "    const testFile = new File([\u0027test content\u0027], \u0027test.jpg\u0027, { type: \u0027image/jpeg\u0027 })"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  153,
        "Line":  "      allowedTypes: [\u0027image/jpeg\u0027, \u0027image/png\u0027]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  183,
        "Line":  "    const cacheService = ImageCacheService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\images\\test-integration.ts",
        "LineNumber":  186,
        "Line":  "    const testData = { id: \u0027test-1\u0027, name: \u0027test-image.jpg\u0027 }"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  836,
        "Line":  "    if (options.type === \u0027image\u0027 || options.type === \u0027catalog\u0027) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  917,
        "Line":  "            let mimeType = \u0027image/png\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  921,
        "Line":  "                mimeType = \u0027image/jpeg\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  924,
        "Line":  "                mimeType = \u0027image/png\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  927,
        "Line":  "                mimeType = \u0027image/gif\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  930,
        "Line":  "                mimeType = \u0027image/svg+xml\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1276,
        "Line":  "      \u003cdiv class=\"production-images-section\" style=\""
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1293,
        "Line":  "        \u003cdiv class=\"images-grid\" style=\""
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1302,
        "Line":  "    const processedImages = await Promise.all(images.map(async (image, index) =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1303,
        "Line":  "      const imageName = image.name || image.image_name || `صورة ${index + 1}`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1304,
        "Line":  "      const imageDescription = image.description || image.category || \u0027\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1305,
        "Line":  "      const imageSize = image.size || image.file_size ? this.formatFileSize(image.size || image.file_size) : \u0027\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1316,
        "Line":  "            const base64 = await window.electronAPI.readFileAsBase64(imageSrc)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1319,
        "Line":  "              let mimeType = \u0027image/png\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1322,
        "Line":  "              const extension = imageSrc.toLowerCase().split(\u0027.\u0027).pop()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1326,
        "Line":  "                  mimeType = \u0027image/jpeg\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1329,
        "Line":  "                  mimeType = \u0027image/png\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1332,
        "Line":  "                  mimeType = \u0027image/gif\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1335,
        "Line":  "                  mimeType = \u0027image/svg+xml\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1338,
        "Line":  "                  mimeType = \u0027image/webp\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1342,
        "Line":  "                  if (base64.startsWith(\u0027/9j/\u0027)) mimeType = \u0027image/jpeg\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1343,
        "Line":  "                  else if (base64.startsWith(\u0027iVBORw0KGgo\u0027)) mimeType = \u0027image/png\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1344,
        "Line":  "                  else if (base64.startsWith(\u0027R0lGODlh\u0027)) mimeType = \u0027image/gif\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1378,
        "Line":  "      const { imageName, imageDescription, imageSize, imageSrc } = processedImage"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1381,
        "Line":  "        \u003cdiv class=\"image-card\" style=\""
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1389,
        "Line":  "          \u003cdiv class=\"image-container\" style=\""
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1437,
        "Line":  "          \u003cdiv class=\"image-info\" style=\""
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1469,
        "Line":  "        \u003cdiv class=\"images-summary\" style=\""
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1491,
        "Line":  "      \u003cdiv class=\"no-images-section\" style=\""
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1845,
        "Line":  "    const imagesPerPage = settings?.imagesPerPage || 6"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1847,
        "Line":  "    const imageQuality = settings?.imageQuality || \u0027high\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1850,
        "Line":  "    let html = \u0027\u003cdiv class=\"images-section enhanced-print\"\u003e\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1854,
        "Line":  "      html += `\u003cdiv class=\"images-header enhanced\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1856,
        "Line":  "        \u003cdiv class=\"images-info\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1858,
        "Line":  "          \u003cspan class=\"quality-info\"\u003eجودة: ${this.getQualityText(imageQuality)}\u003c/span\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1924,
        "Line":  "      const imageStyle = fitToPage"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1929,
        "Line":  "      const imageTag = this.generateImageTag("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1935,
        "Line":  "        \u003cdiv class=\"single-image-page\" style=\"page-break-after: always; text-align: center; min-height: 80vh; display: flex; flex-direction: column; justify-content: center; align-items: center;\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1936,
        "Line":  "          \u003cdiv class=\"image-container\" style=\"margin: 20px 0; flex: 1; display: flex; align-items: center; justify-content: center;\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1940,
        "Line":  "            \u003cdiv class=\"image-metadata\" style=\"margin-top: 20px; text-align: right; background: #f8f9fa; padding: 20px; border-radius: 8px; max-width: 600px; width: 100%;\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1969,
        "Line":  "    const imagesPerRow = Math.ceil(Math.sqrt(imagesPerPage))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1970,
        "Line":  "    let html = \u0027\u003cdiv class=\"images-grid\" style=\"display: grid; grid-template-columns: repeat(\u0027 + imagesPerRow + \u0027, 1fr); gap: 20px; margin: 20px 0;\"\u003e\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1974,
        "Line":  "        html += \u0027\u003c/div\u003e\u003cdiv style=\"page-break-before: always;\"\u003e\u003c/div\u003e\u003cdiv class=\"images-grid\" style=\"display: grid; grid-template-columns: repeat(\u0027 + imagesPerRow + \u0027, 1fr); gap: 20px; margin: 20px 0;\"\u003e\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1978,
        "Line":  "      const imageTag = this.generateImageTag("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1984,
        "Line":  "        \u003cdiv class=\"grid-image-item\" style=\"text-align: center; border: 2px solid #f0f0f0; padding: 15px; border-radius: 8px; background: white; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: none;\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1985,
        "Line":  "          \u003cdiv class=\"grid-image-container\" style=\"margin-bottom: 12px; overflow: hidden; border-radius: 6px;\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  1989,
        "Line":  "            \u003cdiv class=\"image-info\" style=\"margin-top: 10px; font-size: 12px; text-align: right;\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  2017,
        "Line":  "      \u003cdiv class=\"images-list\" style=\"margin: 20px 0;\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  2020,
        "Line":  "          const imageTag = this.generateImageTag("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  2026,
        "Line":  "            \u003cdiv class=\"list-image-item\" style=\"display: flex; margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  2027,
        "Line":  "              \u003cdiv class=\"image-thumbnail\" style=\"flex-shrink: 0; margin-left: 15px;\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  2030,
        "Line":  "              \u003cdiv class=\"image-details\" style=\"flex: 1;\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  2062,
        "Line":  "    const itemsPerRow = Math.min(3, Math.ceil(Math.sqrt(imagesPerPage)))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  2075,
        "Line":  "      const imageTag = this.generateImageTag("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  2082,
        "Line":  "          \u003cdiv class=\"catalog-image\" style=\"margin-bottom: 15px;\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  4035,
        "Line":  "          const pathValidation = this.validateImagePath(image.path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  4078,
        "Line":  "      const parts = imagePath.split(\u0027,\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  4164,
        "Line":  "    const pathValidation = this.validateImagePath(image.path)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  4165,
        "Line":  "    const fallbackImage = this.generateFallbackImage(image.name)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  4196,
        "Line":  "    const settings = qualitySettings[imageQuality as keyof typeof qualitySettings] || qualitySettings.high"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  4261,
        "Line":  "      const pageImages = images.slice(i, i + imagesPerPage)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  4270,
        "Line":  "      const cols = Math.ceil(Math.sqrt(pageImages.length))"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  4271,
        "Line":  "      const rows = Math.ceil(pageImages.length / cols)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  4276,
        "Line":  "        const imageTag = this.generateImageTag("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\MasterPrintService.ts",
        "LineNumber":  4285,
        "Line":  "              \u003cdiv class=\"image-caption\" style=\"margin-top: 8px; font-size: 11px; color: #555;\"\u003e"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  7,
        "Line":  "import type { UniversalImage, ImageContext } from \u0027../components/common/UniversalImagePrint\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  9,
        "Line":  "export interface ImageQuery {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  18,
        "Line":  "export class UniversalImageService {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  31,
        "Line":  "  private createUniversalImage(baseData: any, contextType: any, contextId: number, contextName: string, category: any): UniversalImage {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  37,
        "Line":  "      type: baseData.image_type || baseData.type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  60,
        "Line":  "      type: imageObj.type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  73,
        "Line":  "      const images: UniversalImage[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  83,
        "Line":  "        const imagesResult = await window.electron.ipcRenderer.invoke(\u0027get-invoice-images\u0027, invoiceId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  90,
        "Line":  "            const itemImagesResult = await window.electron.ipcRenderer.invoke(\u0027get-item-images\u0027, item.item_id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  92,
        "Line":  "              for (const itemImage of itemImagesResult.data) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  93,
        "Line":  "                const imageData = this.createUniversalImage("
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  124,
        "Line":  "        for (const invoiceImage of imagesResult.data) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  130,
        "Line":  "            type: invoiceImage.image_type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  166,
        "Line":  "      const images: UniversalImage[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  169,
        "Line":  "      const furnitureImageModule = await import(\u0027../components/production/furniture/FurnitureImageService\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  170,
        "Line":  "      const furnitureImageService = furnitureImageModule.furnitureImageService"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  180,
        "Line":  "        const furnitureImages = await furnitureImageService.getImagesByItem(orderId.toString())"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  184,
        "Line":  "          for (const furnitureImage of furnitureImages) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  190,
        "Line":  "              type: furnitureImage.type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  218,
        "Line":  "          const imagesResult = await window.electron.ipcRenderer.invoke(\u0027get-production-order-images\u0027, orderId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  220,
        "Line":  "            for (const orderImage of imagesResult.data) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  226,
        "Line":  "                type: orderImage.image_type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  258,
        "Line":  "          const itemImagesResult = await window.electron.ipcRenderer.invoke(\u0027get-item-images\u0027, order.item_id)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  260,
        "Line":  "            for (const itemImage of itemImagesResult.data) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  266,
        "Line":  "                type: itemImage.image_type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  306,
        "Line":  "      const images: UniversalImage[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  316,
        "Line":  "        const imagesResult = await window.electron.ipcRenderer.invoke(\u0027get-customer-images\u0027, customerId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  319,
        "Line":  "        for (const customerImage of imagesResult.data) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  325,
        "Line":  "            type: customerImage.image_type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  364,
        "Line":  "      const images: UniversalImage[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  374,
        "Line":  "        const imagesResult = await window.electron.ipcRenderer.invoke(\u0027get-supplier-images\u0027, supplierId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  377,
        "Line":  "        for (const supplierImage of imagesResult.data) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  383,
        "Line":  "            type: supplierImage.image_type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  422,
        "Line":  "      const images: UniversalImage[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  432,
        "Line":  "        const imagesResult = await window.electron.ipcRenderer.invoke(\u0027get-item-images\u0027, itemId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  435,
        "Line":  "        for (const itemImage of imagesResult.data) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  441,
        "Line":  "            type: itemImage.image_type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  480,
        "Line":  "      const images: UniversalImage[] = []"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  490,
        "Line":  "        const imagesResult = await window.electron.ipcRenderer.invoke(\u0027get-check-images\u0027, checkId)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  493,
        "Line":  "        for (const checkImage of imagesResult.data) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  499,
        "Line":  "            type: checkImage.image_type || \u0027image/jpeg\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  577,
        "Line":  "          const imageDate = new Date(image.uploadDate)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  581,
        "Line":  "          if (fromDate \u0026\u0026 imageDate \u003c fromDate) return false"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\services\\UniversalImageService.ts",
        "LineNumber":  596,
        "Line":  "export const universalImageService = UniversalImageService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  1023,
        "Line":  "  type ImageSmoothingQuality = \"high\" | \"low\" | \"medium\""
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  1062,
        "Line":  "  interface ImageDataSettings {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  1341,
        "Line":  "  interface HTMLImageElement extends HTMLElement {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  1492,
        "Line":  "  interface SVGImageElement extends SVGElement {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  1541,
        "Line":  "  class Image {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  3689,
        "Line":  "  type RequestDestination = \"\" | \"audio\" | \"audioworklet\" | \"document\" | \"embed\" | \"font\" | \"frame\" | \"iframe\" | \"image\" | \"manifest\" | \"object\" | \"paintworklet\" | \"report\" | \"script\" | \"serviceworker\" | \"sharedworker\" | \"style\" | \"track\" | \"video\" | \"worker\" | \"xslt\""
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  4016,
        "Line":  "  interface HTMLImageElement extends HTMLElement {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  5211,
        "Line":  "  type ImageBitmapSource = CanvasImageSource | Blob | ImageData"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  5212,
        "Line":  "  type CanvasImageSource = HTMLOrSVGImageElement | HTMLVideoElement | HTMLCanvasElement | ImageBitmap | OffscreenCanvas"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  5213,
        "Line":  "  type HTMLOrSVGImageElement = HTMLImageElement | SVGImageElement"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  5217,
        "Line":  "  interface ImageBitmapOptions {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  5227,
        "Line":  "  type ImageOrientation = \"flipY\" | \"from-image\" | \"none\""
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  5231,
        "Line":  "  interface ImageBitmap {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  5237,
        "Line":  "  interface ImageData {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  5289,
        "Line":  "  type RequestDestination = \"\" | \"audio\" | \"audioworklet\" | \"document\" | \"embed\" | \"font\" | \"frame\" | \"iframe\" | \"image\" | \"manifest\" | \"object\" | \"paintworklet\" | \"report\" | \"script\" | \"serviceworker\" | \"sharedworker\" | \"style\" | \"track\" | \"video\" | \"worker\" | \"xslt\""
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\browser-globals.d.ts",
        "LineNumber":  5502,
        "Line":  "  interface ImageEncodeOptions {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\dom-test-types.d.ts",
        "LineNumber":  31,
        "Line":  "  interface ImageData {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\global.d.ts",
        "LineNumber":  277,
        "Line":  "export interface ItemImage {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\global.d.ts",
        "LineNumber":  294,
        "Line":  "export interface CheckImage {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\global.d.ts",
        "LineNumber":  309,
        "Line":  "export interface ImageSetting {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\global.d.ts",
        "LineNumber":  318,
        "Line":  "export interface ImageUploadData {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\global.d.ts",
        "LineNumber":  335,
        "Line":  "export interface ImageData {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\print.ts",
        "LineNumber":  113,
        "Line":  "  type?: \u0027invoice\u0027 | \u0027receipt\u0027 | \u0027order\u0027 | \u0027report\u0027 | \u0027certificate\u0027 | \u0027statement\u0027 | \u0027image\u0027 | \u0027catalog\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\print.ts",
        "LineNumber":  159,
        "Line":  "export interface PrintImage {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\types\\print.ts",
        "LineNumber":  175,
        "Line":  "  type?: \u0027invoice\u0027 | \u0027order\u0027 | \u0027report\u0027 | \u0027image\u0027 | \u0027catalog\u0027 | \u0027customer\u0027 | \u0027production\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  8,
        "Line":  "export const SUPPORTED_IMAGE_FORMATS = ["
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  22,
        "Line":  "export const MAX_IMAGE_SIZE = 5 * 1024 * 1024"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  25,
        "Line":  "export const MAX_IMAGE_DIMENSIONS = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  33,
        "Line":  "export interface ImageValidationResult {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  42,
        "Line":  "export const diagnoseImageIssues = (imageSrc: string | null | undefined): {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  58,
        "Line":  "    const parts = imageSrc.split(\u0027,\u0027)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  89,
        "Line":  "export const testImageLoad = (imageSrc: string, timeoutMs: number = 10000): Promise\u003cboolean\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  91,
        "Line":  "    const img = new Image()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  138,
        "Line":  "export const validateImageFile = async (file: File): Promise\u003cImageValidationResult\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  139,
        "Line":  "  const result: ImageValidationResult = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  145,
        "Line":  "  if (!file.type.startsWith(\u0027image/\u0027)) {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  156,
        "Line":  "      error: `صيغة الصورة غير مدعومة: ${file.type}. الصيغ المدعومة: ${SUPPORTED_IMAGE_FORMATS.join(\u0027, \u0027)}`"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  170,
        "Line":  "    const dimensions = await getImageDimensions(file)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  187,
        "Line":  "export const getImageDimensions = (file: File): Promise\u003c{ width: number; height: number }\u003e =\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\imageUtils.ts",
        "LineNumber":  293,
        "Line":  "      const thumbnail = canvas.toDataURL(\u0027image/jpeg\u0027, quality)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\visualEditorConfig.ts",
        "LineNumber":  50,
        "Line":  "  [ELEMENT_TYPES.IMAGE]: {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\visualEditorConfig.ts",
        "LineNumber":  92,
        "Line":  "  [ELEMENT_TYPES.IMAGE]: { x: 10, y: 50, width: 30, height: 100 },"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\renderer\\src\\utils\\visualEditorConfig.ts",
        "LineNumber":  102,
        "Line":  "  [ELEMENT_TYPES.IMAGE]: \u0027صورة\u0027,"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  8,
        "Line":  "import { imageService, imagePrintService, type ImagePrintOptions } from \u0027../renderer/src/services/images\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  16,
        "Line":  "export class ImagePrintSystemTest {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  61,
        "Line":  "      const isInitialized = imageService.isInitialized()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  87,
        "Line":  "      const instance = imagePrintService.getInstance()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  114,
        "Line":  "      const validOptions: ImagePrintOptions = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  162,
        "Line":  "      const mockImageFile = this.createMockImageFile()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  165,
        "Line":  "      const optimizationResult = await this.testImageOptimization(mockImageFile)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  197,
        "Line":  "      const mockPrintOptions: ImagePrintOptions = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  230,
        "Line":  "      const mockImageIds = [\u00271\u0027, \u00272\u0027, \u00273\u0027]"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  231,
        "Line":  "      const mockPrintOptions: ImagePrintOptions = {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  238,
        "Line":  "      const batchResult = await this.testBatchPrint(mockImageIds, mockPrintOptions)"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  341,
        "Line":  "    return new File([canvas.toDataURL()], \u0027test-image.png\u0027, { type: \u0027image/png\u0027 })"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\image-print-system-test.ts",
        "LineNumber":  423,
        "Line":  "export const imagePrintSystemTest = new ImagePrintSystemTest()"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\run-image-print-tests.ts",
        "LineNumber":  5,
        "Line":  "import { imagePrintSystemTest } from \u0027./image-print-system-test\u0027"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\run-image-print-tests.ts",
        "LineNumber":  11,
        "Line":  "async function runImagePrintTests(): Promise\u003cvoid\u003e {"
    },
    {
        "Path":  "D:\\‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏‏accounting-app-clean5 -fares99.9.9.12\\src\\test\\run-image-print-tests.ts",
        "LineNumber":  33,
        "Line":  "export { runImagePrintTests }"
    }
]
