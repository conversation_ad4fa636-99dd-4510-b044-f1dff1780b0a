import React, { useState, useEffect } from 'react'
import { Table, Button, Modal, Form, Input, Space, Select, DatePicker, Card, Statistic, Row, Col, InputNumber, Popconfirm, Divider,  App} from 'antd'
// Removed empty import
import { PlusOutlined, EditOutlined, DeleteOutlined, FileTextOutlined, CreditCardOutlined } from '@ant-design/icons'
import { DateUtils, DATE_FORMATS } from '../../utils/dateConfig'
import { useCurrentUser } from '../../hooks/useCurrentUser'
import { getCurrencySymbol, formatCurrency } from '../../utils/settings'
import dayjs from 'dayjs'
import {
  PaymentMethodSelector,
  PaymentForm,
  PAYMENT_METHODS,
  PaymentMethodType
} from '../common/PaymentComponents'
import {
  RemainingAmountDisplay,
  PaymentStatusTag,
  PaymentSummary
} from '../common/RemainingAmountDisplay'
import { InvoicePrintButton } from '../common'
import UnifiedPrintButton from '../common/UnifiedPrintButton'
import { Item as GlobalItem, Supplier as GlobalSupplier, Warehouse as GlobalWarehouse } from '../../types/global'
import * as XLSX from 'xlsx'
import { SafeLogger as Logger } from '../../utils/logger'
// import InventorySelector from '../common/InventorySelector'

const { Option } = Select

interface PurchaseInvoice {
  id: number
  invoice_number: string
  supplier_id: number
  supplier_name: string
  order_id?: number
  order_number?: string
  invoice_date: string
  due_date?: string
  status: string
  subtotal: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  paid_amount: number
  notes?: string
  created_by: number
  created_by_name: string
  created_at: string
}

// Use the global types with aliases to avoid conflicts
type Supplier = GlobalSupplier
type Item = GlobalItem
type Warehouse = GlobalWarehouse

interface InvoiceItem {
  item_id: number
  item_name?: string
  item_code?: string
  quantity: number
  unit_price: number
  total_price: number
  warehouse_id: number
  notes?: string
}

interface PurchaseInvoiceManagementProps {
  onBack: () => void
}

const PurchaseInvoiceManagement: React.FC<PurchaseInvoiceManagementProps> = ({ onBack }) => {
  const [invoices, setInvoices] = useState<PurchaseInvoice[]>([])
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [_items, setItems] = useState<Item[]>([])
  const [filteredItems, setFilteredItems] = useState<Item[]>([])
  const [warehouses, setWarehouses] = useState<Warehouse[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingInvoice, setEditingInvoice] = useState<PurchaseInvoice | null>(null)
  const [invoiceItems, setInvoiceItems] = useState<InvoiceItem[]>([])
  const [form] = Form.useForm()

  // حالات جديدة للدفع والطباعة
  const [paymentModalVisible, setPaymentModalVisible] = useState(false)
  const [selectedInvoiceForPayment, setSelectedInvoiceForPayment] = useState<PurchaseInvoice | null>(null)
  const [paymentForm] = Form.useForm()
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethodType>(PAYMENT_METHODS.CASH)
  const [invoicePayments, setInvoicePayments] = useState<any[]>([])
  const [loadingPayments, setLoadingPayments] = useState(false)

  // استخدام App context للرسائل
  const { message: messageApi } = App.useApp()
  const { userId } = useCurrentUser()

  useEffect(() => {
    loadInvoices()
    loadSuppliers()
    loadItems()
    loadWarehouses()
  }, [])  
  const loadInvoices = async () => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getPurchaseInvoices()
        if (response.success) {
          const invoicesData = response.data || []
          setInvoices(invoicesData)
          Logger.info('PurchaseInvoiceManagement', '✅ تم تحميل ' + invoicesData.length + ' فاتورة شراء بنجاح')

          // تشخيص البيانات
          console.log('🔍 تشخيص فواتير المشتريات:', {
            totalInvoices: invoicesData.length,
            sampleInvoice: invoicesData[0] || null,
            loading: false
          })
        } else {
          const errorMessage = response.message || 'فشل في تحميل فواتير الشراء'
          Logger.error('PurchaseInvoiceManagement', '❌ خطأ في تحميل فواتير الشراء:', new Error(errorMessage))
          messageApi.error(errorMessage)
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('PurchaseInvoiceManagement', '❌ window.electronAPI غير متوفر')
        messageApi.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل فواتير الشراء'
      Logger.error('PurchaseInvoiceManagement', '❌ خطأ في تحميل فواتير الشراء:', error instanceof Error ? error : new Error(String(error)))
      messageApi.error('خطأ في تحميل الفواتير: ' + errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const loadSuppliers = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getSuppliers()
        if (response.success) {
          setSuppliers(response.data.filter((s: Supplier) => s.is_active))
        } else {
          messageApi.error('فشل في تحميل الموردين')
        }
      }
    } catch (error) {
      Logger.error('PurchaseInvoiceManagement', 'خطأ في تحميل الموردين:', error instanceof Error ? error : new Error(String(error)))
      messageApi.error('فشل في تحميل الموردين')
    }
  }

  const loadItems = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getItems()
        if ((response as any).success) {
          const itemsData = (response as any).data || []
          setItems(itemsData.filter((item: Item) => item.is_active !== false))
          Logger.info('PurchaseInvoiceManagement', '✅ تم تحميل ' + itemsData.length + ' صنف بنجاح')
        } else {
          const errorMessage = (response as any).message || 'فشل في تحميل الأصناف'
          Logger.error('PurchaseInvoiceManagement', '❌ خطأ في تحميل الأصناف:', errorMessage)
          messageApi.error(errorMessage)
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('PurchaseInvoiceManagement', '❌ window.electronAPI غير متوفر')
        messageApi.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل الأصناف'
      Logger.error('PurchaseInvoiceManagement', '❌ خطأ في تحميل الأصناف:', error instanceof Error ? error : new Error(String(error)))
      messageApi.error('خطأ في تحميل الأصناف: ' + errorMessage)
    }
  }

  // تحميل الأصناف المتوفرة في مخزن محدد
  const loadItemsByWarehouse = async (warehouseId: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getItemsByWarehouse(warehouseId)
        if (response.success) {
          const itemsData = response.data || []
          setFilteredItems(itemsData)
          Logger.info('PurchaseInvoiceManagement', '✅ تم تحميل ' + itemsData.length + ' صنف للمخزن ' + warehouseId)
        } else {
          const errorMessage = response.message || 'فشل في تحميل الأصناف للمخزن'
          Logger.error('PurchaseInvoiceManagement', '❌ خطأ في تحميل الأصناف للمخزن:', new Error(errorMessage))
          messageApi.error(errorMessage)
          setFilteredItems([])
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات'
        Logger.error('PurchaseInvoiceManagement', '❌ window.electronAPI غير متوفر')
        messageApi.error(errorMessage)
        setFilteredItems([])
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل الأصناف للمخزن'
      Logger.error('PurchaseInvoiceManagement', '❌ خطأ في تحميل الأصناف للمخزن:', error instanceof Error ? error : new Error(String(error)))
      messageApi.error('خطأ في تحميل الأصناف للمخزن: ' + errorMessage)
      setFilteredItems([])
    }
  }

  const loadWarehouses = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getWarehouses()
        if (response && (response as any).success) {
          const warehousesData = (response as any).data || []
          setWarehouses(warehousesData.filter((warehouse: Warehouse) => warehouse.is_active))
          Logger.info('PurchaseInvoiceManagement', '✅ تم تحميل ' + warehousesData.length + ' مخزن بنجاح')
        } else if (Array.isArray(response)) {
          // للتوافق مع الإصدارات القديمة
          const warehousesData = response || []
          setWarehouses(warehousesData.filter((warehouse: Warehouse) => warehouse.is_active))
          Logger.info('PurchaseInvoiceManagement', '✅ تم تحميل ' + warehousesData.length + ' مخزن بنجاح')
        } else {
          const errorMessage = (response as any)?.message || 'فشل في تحميل المخازن'
          Logger.error('PurchaseInvoiceManagement', '❌ خطأ في تحميل المخازن:', errorMessage)
          messageApi.error(errorMessage)
        }
      } else {
        const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات. يرجى إعادة تشغيل التطبيق.'
        Logger.error('PurchaseInvoiceManagement', '❌ window.electronAPI غير متوفر')
        messageApi.error(errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تحميل المخازن'
      Logger.error('PurchaseInvoiceManagement', '❌ خطأ في تحميل المخازن:', error instanceof Error ? error : new Error(String(error)))
      messageApi.error('خطأ في تحميل المخازن: ' + errorMessage)
    }
  }



  const generateInvoiceNumber = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generatePurchaseInvoiceNumber()
        if (response.success && response.data?.invoiceNumber) {
          // التحقق من أن الرقم المولد غير موجود مسبقاً
          const existingInvoices = await window.electronAPI.getPurchaseInvoices()
          if (existingInvoices.success && existingInvoices.data) {
            const duplicateInvoice = existingInvoices.data.find((inv: any) =>
              inv.invoice_number === response.data.invoiceNumber
            )
            if (duplicateInvoice) {
              // إذا كان الرقم موجود، أعد المحاولة مرة أخرى
              const retryResponse = await window.electronAPI.generatePurchaseInvoiceNumber()
              if (retryResponse.success && retryResponse.data?.invoiceNumber) {
                form.setFieldsValue({ invoice_number: retryResponse.data.invoiceNumber })
                messageApi.success('تم إنشاء رقم فاتورة جديد')
              } else {
                throw new Error('فشل في إعادة إنشاء رقم الفاتورة')
              }
            } else {
              form.setFieldsValue({ invoice_number: response.data.invoiceNumber })
              messageApi.success('تم إنشاء رقم الفاتورة')
            }
          } else {
            form.setFieldsValue({ invoice_number: response.data.invoiceNumber })
            messageApi.success('تم إنشاء رقم الفاتورة')
          }
        } else {
          throw new Error('فشل في الحصول على رقم فاتورة من الخادم')
        }
      }
    } catch (error) {
      Logger.error('PurchaseInvoiceManagement', 'خطأ في إنشاء رقم الفاتورة:', error instanceof Error ? error : new Error(String(error)))
      // fallback إلى timestamp مع معرف عشوائي
      const timestamp = Date.now().toString().slice(-6)
      const randomId = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      const invoiceNumber = 'PI' + timestamp + randomId
      form.setFieldsValue({ invoice_number: invoiceNumber })
      messageApi.warning('تم إنشاء رقم فاتورة مؤقت. يرجى التحقق من الرقم قبل الحفظ.')
    }
  }

  const calculateTotals = () => {
    // التأكد من أن جميع الأصناف لها total_price محسوب
    const validItems = invoiceItems.filter(item => item.item_id && item.quantity > 0 && item.unit_price > 0)
    const subtotal = validItems.reduce((sum, item) => {
      const itemTotal = item.quantity * item.unit_price
      return sum + itemTotal
    }, 0)

    const taxAmount = subtotal * 0.16 // ضريبة 16%
    const discountAmount = form.getFieldValue('discount_amount') || 0
    const totalAmount = subtotal + taxAmount - discountAmount

    Logger.info('PurchaseInvoiceManagement', '🧮 حساب المجاميع:', {
      validItemsCount: validItems.length,
      invoiceItems: validItems.map(item => ({
        item_name: item.item_name,
        quantity: item.quantity,
        unit_price: item.unit_price,
        calculated_total: item.quantity * item.unit_price,
        stored_total: item.total_price
      })),
      subtotal,
      taxAmount,
      discountAmount,
      totalAmount
    })

    const paidAmount = form.getFieldValue('paid_amount') || 0
    const roundedTotalAmount = Math.round(totalAmount * 100) / 100
    const remainingAmount = roundedTotalAmount - paidAmount

    form.setFieldsValue({
      subtotal: Math.round(subtotal * 100) / 100,
      tax_amount: Math.round(taxAmount * 100) / 100,
      total_amount: roundedTotalAmount,
      remaining_amount: Math.round(remainingAmount * 100) / 100
    })
  }

  const addInvoiceItem = () => {
    const newItem: InvoiceItem = {
      item_id: 0,
      quantity: 1,
      unit_price: 0,
      total_price: 0,
      warehouse_id: 0 // يجب اختيار المخزن أولاً
    }
    setInvoiceItems([...invoiceItems, newItem])
    // إعادة تعيين قائمة الأصناف المفلترة
    setFilteredItems([])
  }

  const updateInvoiceItem = async (index: number, field: keyof InvoiceItem, value: any) => {
    const updatedItems = [...invoiceItems]
    updatedItems[index /* TODO: Define index */] = { ...updatedItems[index /* TODO: Define index */], [field]: value }

    if (field === 'warehouse_id') {
      // عند تغيير المخزن، إعادة تعيين الصنف وتحميل الأصناف المتوفرة في المخزن الجديد
      updatedItems[index /* TODO: Define index */].item_id = 0
      updatedItems[index /* TODO: Define index */].item_name = ''
      updatedItems[index /* TODO: Define index */].item_code = ''
      updatedItems[index /* TODO: Define index */].unit_price = 0
      updatedItems[index /* TODO: Define index */].total_price = 0

      if (value) {
        await loadItemsByWarehouse(value)
      } else {
        setFilteredItems([])
      }
    }

    if (field === 'item_id') {
      const selectedItem = filteredItems.find(item => item.id === value)
      if (selectedItem) {
        updatedItems[index /* TODO: Define index */].item_name = selectedItem.name
        updatedItems[index /* TODO: Define index */].item_code = selectedItem.code
        updatedItems[index /* TODO: Define index */].unit_price = selectedItem.cost_price || 0
        // إعادة حساب المجموع عند اختيار الصنف
        updatedItems[index /* TODO: Define index */].total_price = updatedItems[index /* TODO: Define index */].quantity * (selectedItem.cost_price || 0)
      }
    }

    if (field === 'quantity' || field === 'unit_price') {
      updatedItems[index /* TODO: Define index */].total_price = updatedItems[index /* TODO: Define index */].quantity * updatedItems[index /* TODO: Define index */].unit_price
    }

    setInvoiceItems(updatedItems)
    // حساب المجاميع مباشرة باستخدام البيانات المحدثة
    setTimeout(() => {
      const validItems = updatedItems.filter(item => item.item_id && item.quantity > 0 && item.unit_price > 0)
      const subtotal = validItems.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0)
      const taxAmount = subtotal * 0.16
      const discountAmount = form.getFieldValue('discount_amount') || 0
      const totalAmount = subtotal + taxAmount - discountAmount

      form.setFieldsValue({
        subtotal: Math.round(subtotal * 100) / 100,
        tax_amount: Math.round(taxAmount * 100) / 100,
        total_amount: Math.round(totalAmount * 100) / 100
      })
    }, 50)
  }

  const removeInvoiceItem = (index: number) => {
    const updatedItems = invoiceItems.filter((_, i) => i !== index /* TODO: Define index */)
    setInvoiceItems(updatedItems)
    setTimeout(calculateTotals, 100)
  }

  const handleSubmit = async (values: any) => {
    Logger.info('PurchaseInvoiceManagement', '🔍 بدء التحقق من البيانات:', values)

    // التحقق من صحة البيانات الأساسية
    if (!values.invoice_number || values.invoice_number.trim() === '') {
      messageApi.error('رقم الفاتورة مطلوب')
      return { success: false, message: 'رقم الفاتورة مطلوب' }
    }

    if (!values.invoice_date) {
      messageApi.error('تاريخ الفاتورة مطلوب')
      return { success: false, message: 'تاريخ الفاتورة مطلوب' }
    }

    if (!values.supplier_id) {
      messageApi.error('يجب اختيار المورد')
      return { success: false, message: 'يجب اختيار المورد' }
    }

    // التحقق من صحة التواريخ
    if (values.due_date && values.invoice_date) {
      const invoiceDate = dayjs(values.invoice_date)
      const dueDate = dayjs(values.due_date)

      if (dueDate.isBefore(invoiceDate)) {
        messageApi.error('تاريخ الاستحقاق لا يمكن أن يكون قبل تاريخ الفاتورة')
        return { success: false, message: 'تاريخ الاستحقاق لا يمكن أن يكون قبل تاريخ الفاتورة' }
      }

      // تحذير إذا كان تاريخ الاستحقاق في الماضي
      if (dueDate.isBefore(dayjs(), 'day')) {
        messageApi.warning('تاريخ الاستحقاق في الماضي')
      }
    }

    // التحقق من عدم وجود رقم الفاتورة مسبقاً
    try {
      if (window.electronAPI && window.electronAPI.checkInvoiceNumberUniqueness) {
        const response = await window.electronAPI.checkInvoiceNumberUniqueness(
          'purchase_invoices',
          values.invoice_number.trim(),
          editingInvoice?.id
        )
        if (response.success && !response.data.isUnique) {
          messageApi.error('رقم الفاتورة موجود مسبقاً. يرجى استخدام رقم آخر أو الضغط على زر "توليد"')
          return { success: false, message: 'رقم الفاتورة موجود مسبقاً' }
        }
      }
    } catch (error) {
      Logger.warn('PurchaseInvoiceManagement', 'تعذر التحقق من رقم الفاتورة:', error)
    }

    if (invoiceItems.length === 0) {
      messageApi.error('يجب إضافة صنف واحد على الأقل')
      return { success: false, message: 'يجب إضافة صنف واحد على الأقل' }
    }

    Logger.info('PurchaseInvoiceManagement', '🔍 فحص الأصناف:', invoiceItems.map(item => ({
      warehouse_id: item.warehouse_id,
      item_id: item.item_id,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.total_price
    })))

    // التحقق من صحة بيانات الأصناف
    for (let i = 0; i < invoiceItems.length; i++) {
      const item = invoiceItems[i]
      if (!item.warehouse_id || item.warehouse_id === 0) {
        messageApi.error('يجب اختيار المخزن في السطر ' + (i + 1))
        return { success: false, message: 'يجب اختيار المخزن في السطر ' + (i + 1) }
      }
      if (!item.item_id || item.item_id === 0) {
        messageApi.error('يجب اختيار الصنف في السطر ' + (i + 1))
        return { success: false, message: 'يجب اختيار الصنف في السطر ' + (i + 1) }
      }
      if (!item.quantity || item.quantity <= 0) {
        messageApi.error('الكمية يجب أن تكون أكبر من صفر في السطر ' + (i + 1))
        return { success: false, message: 'الكمية يجب أن تكون أكبر من صفر في السطر ' + (i + 1) }
      }
      if (!item.unit_price || item.unit_price <= 0) {
        messageApi.error('سعر الوحدة يجب أن يكون أكبر من صفر في السطر ' + (i + 1))
        return { success: false, message: 'سعر الوحدة يجب أن يكون أكبر من صفر في السطر ' + (i + 1) }
      }
    }

    // حساب المجموع الفرعي من العناصر المضافة (وليس من القيمة المدخلة)
    const calculatedSubtotal = invoiceItems.reduce((sum, item) => sum + (item.total_price || 0), 0)
    const totalAmount = values.total_amount || 0

    // التحقق من وجود عناصر في الفاتورة
    if (invoiceItems.length === 0) {
      messageApi.error('يجب إضافة صنف واحد على الأقل إلى الفاتورة')
      return { success: false, message: 'يجب إضافة صنف واحد على الأقل إلى الفاتورة' }
    }

    // التحقق من أن المجموع الفرعي المحسوب أكبر من صفر
    if (calculatedSubtotal <= 0) {
      messageApi.error('المجموع الفرعي يجب أن يكون أكبر من صفر. تأكد من إضافة أصناف بكميات وأسعار صحيحة.')
      return { success: false, message: 'المجموع الفرعي يجب أن يكون أكبر من صفر' }
    }

    if (totalAmount <= 0) {
      messageApi.error('إجمالي الفاتورة يجب أن يكون أكبر من صفر')
      return { success: false, message: 'إجمالي الفاتورة يجب أن يكون أكبر من صفر' }
    }

    // التحقق من الخصم
    const discountAmount = values.discount_amount || 0
    if (discountAmount > calculatedSubtotal) {
      messageApi.error('مبلغ الخصم لا يمكن أن يكون أكبر من المجموع الفرعي')
      return { success: false, message: 'مبلغ الخصم لا يمكن أن يكون أكبر من المجموع الفرعي' }
    }

    if (discountAmount < 0) {
      messageApi.error('مبلغ الخصم لا يمكن أن يكون سالباً')
      return { success: false, message: 'مبلغ الخصم لا يمكن أن يكون سالباً' }
    }

    // التحقق من صحة الضريبة
    const taxAmount = values.tax_amount || 0
    if (taxAmount < 0) {
      messageApi.error('مبلغ الضريبة لا يمكن أن يكون سالباً')
      return { success: false, message: 'مبلغ الضريبة لا يمكن أن يكون سالباً' }
    }

    // تحديث المجموع الفرعي في البيانات المرسلة
    values.subtotal = calculatedSubtotal

    Logger.info('PurchaseInvoiceManagement', '✅ تم التحقق من جميع البيانات بنجاح')

    try {
      if (window.electronAPI) {
        // التأكد من وجود تاريخ الفاتورة
        const invoiceDate = values.invoice_date ? values.invoice_date.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD')

        const invoiceData = {
          ...values,
          supplier_id: parseInt(String(values.supplier_id)), // تحويل معرف المورد إلى رقم صحيح
          invoice_date: invoiceDate,
          due_date: values.due_date ? values.due_date.format('YYYY-MM-DD') : null,
          items: invoiceItems,
          created_by: userId
        }

        Logger.info('PurchaseInvoiceManagement', '📤 بيانات الفاتورة قبل الإرسال:', {
          invoice_number: invoiceData.invoice_number,
          supplier_id: invoiceData.supplier_id,
          invoice_date: invoiceData.invoice_date,
          due_date: invoiceData.due_date,
          items_count: invoiceData.items.length,
          subtotal: invoiceData.subtotal,
          total_amount: invoiceData.total_amount
        })

        let response
        if (editingInvoice) {
          response = await window.electronAPI.updatePurchaseInvoice(editingInvoice.id, invoiceData)
        } else {
          response = await window.electronAPI.createPurchaseInvoice(invoiceData)
        }

        if (response.success) {
          messageApi.success(editingInvoice ? 'تم تحديث فاتورة الشراء بنجاح' : 'تم إنشاء فاتورة الشراء بنجاح')
          setModalVisible(false)
          form.resetFields()
          setEditingInvoice(null)
          setInvoiceItems([])
          loadInvoices()
          return { success: true, data: response.data }
        } else {
          const errorMessage = response.message || 'فشل في حفظ فاتورة الشراء'
          messageApi.error(errorMessage)
          Logger.error('PurchaseInvoiceManagement', 'خطأ من الخادم:', response)
          return { success: false, message: errorMessage }
        }
      }
      const errorMessage = 'لا يمكن الوصول إلى قاعدة البيانات'
      messageApi.error(errorMessage)
      return { success: false, message: errorMessage }
    } catch (error) {
      Logger.error('PurchaseInvoiceManagement', 'خطأ في حفظ فاتورة الشراء:', error instanceof Error ? error : new Error(String(error)))
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء حفظ فاتورة الشراء'
      messageApi.error(errorMessage)
      return { success: false, message: errorMessage }
    }
  }

  const handleAdd = () => {
    setEditingInvoice(null)
    form.resetFields()
    form.setFieldsValue({
      invoice_date: dayjs(),
      status: 'pending',
      subtotal: 0,
      tax_amount: 0,
      discount_amount: 0,
      total_amount: 0,
      payment_type: 'credit',
      paid_amount: 0,
      remaining_amount: 0
    })
    setInvoiceItems([])
    generateInvoiceNumber()
    setModalVisible(true)
  }

  const handleEdit = async (invoice: PurchaseInvoice) => {
    try {
      setEditingInvoice(invoice)
      setModalVisible(true)

      // تحميل عناصر الفاتورة
      if (window.electronAPI) {
        const response = await window.electronAPI.getPurchaseInvoiceItems(invoice.id)
        if (response.success) {
          setInvoiceItems(response.data || [])
        }
      }

      // تأخير ملء النموذج ببيانات الفاتورة حتى بعد فتح المودال لتجنب تحذير useForm
      setTimeout(() => {
        form.setFieldsValue({
          invoice_number: invoice.invoice_number,
          supplier_id: invoice.supplier_id,
          invoice_date: dayjs(invoice.invoice_date),
          due_date: invoice.due_date ? dayjs(invoice.due_date) : null,
          status: invoice.status,
          notes: invoice.notes,
          subtotal: invoice.subtotal,
          tax_amount: invoice.tax_amount,
          discount_amount: invoice.discount_amount,
          total_amount: invoice.total_amount
        })
      }, 100)

    } catch (error) {
      messageApi.error('حدث خطأ أثناء تحميل بيانات الفاتورة')
      Logger.error('PurchaseInvoiceManagement', 'خطأ في تحميل الفاتورة:', error instanceof Error ? error : new Error(String(error)))
    }
  }

  const updateInvoiceStatus = async (invoiceId: number, status: string) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.updatePurchaseInvoiceStatus(invoiceId, status)
        if (response.success) {
          messageApi.success('تم تحديث حالة الفاتورة بنجاح')
          loadInvoices()
        } else {
          messageApi.error(response.message || 'فشل في تحديث حالة الفاتورة')
        }
      }
    } catch (error) {
      messageApi.error('حدث خطأ أثناء تحديث حالة الفاتورة')
    }
  }

  // تحميل مدفوعات الفاتورة
  const loadInvoicePayments = async (invoiceId: number) => {
    setLoadingPayments(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getInvoicePayments(invoiceId, 'purchase')
        if (response.success) {
          setInvoicePayments(response.data || [])
        }
      }
    } catch (error) {
      Logger.error('PurchaseInvoiceManagement', 'خطأ في تحميل المدفوعات:', error instanceof Error ? error : new Error(String(error)))
    } finally {
      setLoadingPayments(false)
    }
  }

  // فتح نافذة الدفع
  const showPaymentModal = (invoice: PurchaseInvoice) => {
    setSelectedInvoiceForPayment(invoice)
    setPaymentModalVisible(true)
    paymentForm.resetFields()
    paymentForm.setFieldsValue({
      payment_date: dayjs(),
      payment_method: PAYMENT_METHODS.CASH
    })
    setSelectedPaymentMethod(PAYMENT_METHODS.CASH)
    loadInvoicePayments(invoice.id)
  }

  // معالجة الدفع
  const handlePayment = async (values: any) => {
    if (!selectedInvoiceForPayment) {
      messageApi.error('لم يتم اختيار فاتورة للدفع')
      return
    }

    // التحقق من صحة بيانات الدفع
    if (!values.amount || values.amount <= 0) {
      messageApi.error('مبلغ الدفعة يجب أن يكون أكبر من صفر')
      return
    }

    const remainingAmount = selectedInvoiceForPayment.total_amount - selectedInvoiceForPayment.paid_amount
    if (values.amount > remainingAmount) {
      messageApi.error('مبلغ الدفعة لا يمكن أن يكون أكبر من المبلغ المتبقي (' + formatCurrency(remainingAmount) + ')')
      return
    }

    if (!values.payment_date) {
      messageApi.error('تاريخ الدفع مطلوب')
      return
    }

    // التحقق من البيانات الإضافية حسب طريقة الدفع
    if (selectedPaymentMethod === PAYMENT_METHODS.CHECK) {
      if (!values.check_number || values.check_number.trim() === '') {
        messageApi.error('رقم الشيك مطلوب')
        return
      }
      if (!values.due_date) {
        messageApi.error('تاريخ استحقاق الشيك مطلوب')
        return
      }
    }

    if (selectedPaymentMethod === PAYMENT_METHODS.BANK_TRANSFER) {
      if (!values.reference_number || values.reference_number.trim() === '') {
        messageApi.error('رقم مرجع التحويل مطلوب')
        return
      }
    }

    try {
      const paymentData = {
        invoice_id: selectedInvoiceForPayment.id,
        invoice_type: 'purchase_invoice',
        supplier_id: selectedInvoiceForPayment.supplier_id,
        amount: values.amount,
        payment_method: selectedPaymentMethod,
        payment_date: values.payment_date.format('YYYY-MM-DD'),
        reference_number: values.reference_number || values.check_number || values.voucher_number,
        notes: values.notes,
        // بيانات إضافية حسب طريقة الدفع
        check_number: values.check_number,
        bank_name: values.bank_name,
        due_date: values.due_date ? values.due_date.format('YYYY-MM-DD') : null,
        bank_account_id: values.bank_account_id,
        created_by: userId
      }

      if (window.electronAPI) {
        const response = await window.electronAPI.createSupplierPayment(paymentData)
        if (response.success) {
          messageApi.success('تم تسجيل الدفعة بنجاح')
          setPaymentModalVisible(false)
          paymentForm.resetFields()
          loadInvoices() // إعادة تحميل الفواتير لتحديث المبالغ
          loadInvoicePayments(selectedInvoiceForPayment.id) // تحديث المدفوعات
        } else {
          const errorMessage = response.message || 'فشل في تسجيل الدفعة'
          messageApi.error(errorMessage)
          Logger.error('PurchaseInvoiceManagement', 'خطأ من الخادم:', new Error(response.message || 'خطأ غير محدد'))
        }
      } else {
        messageApi.error('لا يمكن الوصول إلى قاعدة البيانات')
      }
    } catch (error) {
      Logger.error('PurchaseInvoiceManagement', 'خطأ في تسجيل الدفعة:', error instanceof Error ? error : new Error(String(error)))
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير متوقع أثناء تسجيل الدفعة'
      messageApi.error(errorMessage)
    }
  }

  const handleDeleteInvoice = async (invoiceId: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.deletePurchaseInvoice(invoiceId)
        if (response.success) {
          messageApi.success('تم حذف فاتورة الشراء بنجاح')
          loadInvoices()
        } else {
          messageApi.error(response.message || 'فشل في حذف فاتورة الشراء')
        }
      }
    } catch (error) {
      Logger.error('PurchaseInvoiceManagement', 'خطأ في حذف فاتورة الشراء:', error instanceof Error ? error : new Error(String(error)))
      messageApi.error('حدث خطأ أثناء حذف فاتورة الشراء')
    }
  }

  const _getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'paid': return 'green'
      case 'overdue': return 'red'
      case 'cancelled': return 'default'
      default: return 'default'
    }
  }

  const _getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'معلقة'
      case 'paid': return 'مدفوعة'
      case 'partial': return 'جزئية'
      case 'overdue': return 'متأخرة'
      case 'cancelled': return 'ملغية'
      default: return status
    }
  }

  const getInvoiceStats = () => {
    const total = invoices.length
    const pending = invoices.filter(i => i.status === 'pending').length
    const paid = invoices.filter(i => i.status === 'paid').length
    const partial = invoices.filter(i => i.status === 'partial').length
    const overdue = invoices.filter(i => i.status === 'overdue').length
    const totalAmount = invoices.reduce((sum, i) => sum + i.total_amount, 0)
    const paidAmount = invoices.reduce((sum, i) => sum + i.paid_amount, 0)
    const remainingAmount = totalAmount - paidAmount

    return {
      total,
      pending,
      paid,
      partial,
      overdue,
      totalAmount,
      paidAmount,
      remainingAmount,
      totalPaid: paidAmount,
      totalRemaining: remainingAmount
    }
  }

  const stats = getInvoiceStats()

  // دالة تصدير Excel محسنة
  const handleExportExcel = () => {
    try {
      if (invoices.length === 0) {
        messageApi.warning('لا توجد فواتير شراء للتصدير')
        return
      }

      // تحضير البيانات للتصدير
      const exportData = invoices.map(invoice => ({
        'رقم الفاتورة': invoice.invoice_number,
        'المورد': invoice.supplier_name,
        'تاريخ الفاتورة': new Date(invoice.invoice_date).toLocaleDateString('ar-EG'),
        'تاريخ الاستحقاق': invoice.due_date ? new Date(invoice.due_date).toLocaleDateString('ar-EG') : '',
        'الحالة': _getStatusText(invoice.status),
        'المجموع الفرعي': invoice.subtotal,
        'الضريبة': invoice.tax_amount,
        'الخصم': invoice.discount_amount,
        'الإجمالي': invoice.total_amount,
        'المدفوع': invoice.paid_amount,
        'المتبقي': invoice.total_amount - invoice.paid_amount,
        'الملاحظات': invoice.notes || '',
        'المنشئ': invoice.created_by_name,
        'تاريخ الإنشاء': new Date(invoice.created_at).toLocaleDateString('ar-EG')
      }))

      // إنشاء ورقة العمل
      const worksheet = XLSX.utils.json_to_sheet(exportData)

      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 15 }, // رقم الفاتورة
        { wch: 25 }, // المورد
        { wch: 15 }, // تاريخ الفاتورة
        { wch: 15 }, // تاريخ الاستحقاق
        { wch: 10 }, // الحالة
        { wch: 15 }, // المجموع الفرعي
        { wch: 10 }, // الضريبة
        { wch: 10 }, // الخصم
        { wch: 15 }, // الإجمالي
        { wch: 15 }, // المدفوع
        { wch: 15 }, // المتبقي
        { wch: 30 }, // الملاحظات
        { wch: 20 }, // المنشئ
        { wch: 15 }  // تاريخ الإنشاء
      ]
      worksheet['!cols'] = columnWidths

      // إنشاء المصنف
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'فواتير الشراء')

      // إضافة ورقة معلومات التقرير
      const reportInfo = [
        ['تقرير فواتير الشراء'],
        [''],
        ['إجمالي الفواتير:', invoices.length],
        ['الفواتير المعلقة:', stats.pending],
        ['الفواتير المدفوعة:', stats.paid],
        ['الفواتير الجزئية:', stats.partial],
        ['الفواتير المتأخرة:', stats.overdue],
        ['إجمالي المبلغ:', stats.totalAmount.toLocaleString()],
        ['إجمالي المدفوع:', stats.totalPaid.toLocaleString()],
        ['إجمالي المتبقي:', stats.totalRemaining.toLocaleString()],
        [''],
        ['تاريخ التصدير:', new Date().toLocaleDateString('ar-EG')],
        ['وقت التصدير:', new Date().toLocaleTimeString('ar-EG')]
      ]

      const infoWorksheet = XLSX.utils.aoa_to_sheet(reportInfo)
      XLSX.utils.book_append_sheet(workbook, infoWorksheet, 'معلومات التقرير')

      // تحديد اسم الملف
      const timestamp = new Date().toISOString().split('T')[0]
      const fileName = 'تقرير_فواتير_الشراء_' + timestamp + '.xlsx'

      // تحميل الملف
      XLSX.writeFile(workbook, fileName)

      messageApi.success('تم تصدير ' + invoices.length + ' فاتورة شراء بنجاح إلى ملف Excel')
    } catch (error) {
      Logger.error('PurchaseInvoiceManagement', 'خطأ في تصدير Excel:', error)
      messageApi.error('حدث خطأ أثناء تصدير البيانات')
    }
  }

  const columns = [
    {
      title: 'رقم الفاتورة',
      dataIndex: 'invoice_number',
      key: 'invoice_number',
      width: 120,
      render: (invoiceNumber: string) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{invoiceNumber}</span>
      )
    },
    {
      title: 'المورد',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      render: (name: string) => (
        <Space>
          <FileTextOutlined style={{ color: '#1890ff' }} />
          {name}
        </Space>
      )
    },
    {
      title: 'تاريخ الفاتورة (ميلادي)',
      dataIndex: 'invoice_date',
      key: 'invoice_date',
      width: 140,
      render: (date: string) => DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE)
    },
    {
      title: 'تاريخ الاستحقاق (ميلادي)',
      dataIndex: 'due_date',
      key: 'due_date',
      width: 140,
      render: (date: string) => date ? DateUtils.formatForDisplay(date, DATE_FORMATS.DISPLAY_DATE) : '-'
    },
    {
      title: 'حالة الدفع',
      key: 'payment_status',
      width: 150,
      render: (_, record: PurchaseInvoice) => (
        <PaymentStatusTag
          totalAmount={record.total_amount || 0}
          paidAmount={record.paid_amount || 0}
          dueDate={record.due_date}
          size="small"
        />
      )
    },
    {
      title: 'المبالغ',
      key: 'amounts',
      width: 200,
      render: (_, record: PurchaseInvoice) => (
        <RemainingAmountDisplay
          totalAmount={record.total_amount || 0}
          paidAmount={record.paid_amount || 0}
          dueDate={record.due_date}
          size="small"
          showProgress={false}
        />
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 200,
      render: (_, record: PurchaseInvoice) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            title="تعديل"
          />
          {(record.total_amount - record.paid_amount) > 0 && (
            <Button
              type="default"
              size="small"
              icon={<CreditCardOutlined />}
              onClick={() => showPaymentModal(record)}
              title="تسجيل دفعة"
              style={{ color: '#52c41a', borderColor: '#52c41a' }}
            />
          )}
          <Select
            size="small"
            value={record.status}
            onChange={(status) => updateInvoiceStatus(record.id, status)}
            style={{ width: 100 }}
          >
            <Select.Option value="pending">معلقة</Select.Option>
            <Select.Option value="paid">مدفوعة</Select.Option>
            <Select.Option value="partial">جزئية</Select.Option>
            <Select.Option value="overdue">متأخرة</Select.Option>
            <Select.Option value="cancelled">ملغية</Select.Option>
          </Select>
          <InvoicePrintButton
            invoiceData={{
              id: record.id,
              invoiceNumber: record.invoice_number,
              invoiceDate: record.invoice_date,
              customerName: record.supplier_name || '',
              items: [], // سيتم تحميل الأصناف عند الطباعة
              subtotal: record.subtotal || 0,
              discount: record.discount_amount || 0,
              tax: record.tax_amount || 0,
              total: record.total_amount || 0,
              paid: record.paid_amount || 0,
              remaining: (record.total_amount || 0) - (record.paid_amount || 0)
            }}
            invoiceType="purchase"
            size="small"
            buttonText="طباعة"
            loadInvoiceItems={async () => {
              // تحميل أصناف فاتورة المشتريات
              try {
                if (window.electronAPI) {
                  const response = await window.electronAPI.getPurchaseInvoiceItems(record.id)
                  if (response.success && Array.isArray(response.data)) {
                    return response.data.map((item: any) => ({
                      id: item.id,
                      name: item.item_name || 'غير محدد',
                      description: item.notes || '',
                      quantity: item.quantity || 0,
                      unit: 'قطعة',
                      unitPrice: item.unit_price || 0,
                      total: item.total_price || 0
                    }))
                  }
                }
                return []
              } catch (error) {
                console.error('خطأ في تحميل أصناف فاتورة المشتريات:', error)
                return []
              }
            }}
            loadSupplierData={async () => {
              // تحميل بيانات المورد الكاملة
              try {
                if (window.electronAPI && record.supplier_id) {
                  const response = await window.electronAPI.getSuppliers()
                  if (response.success && Array.isArray(response.data)) {
                    const supplier = response.data.find((s: any) => s.id === record.supplier_id)
                    if (supplier) {
                      return {
                        name: supplier.name || record.supplier_name || '',
                        address: supplier.address || '',
                        phone: supplier.phone || '',
                        email: supplier.email || ''
                      }
                    }
                  }
                }
                return {
                  name: record.supplier_name || '',
                  address: '',
                  phone: '',
                  email: ''
                }
              } catch (error) {
                console.error('خطأ في تحميل بيانات المورد:', error)
                return {
                  name: record.supplier_name || '',
                  address: '',
                  phone: '',
                  email: ''
                }
              }
            }}
            onPrintSuccess={() => messageApi.success('تم طباعة الفاتورة بنجاح')}
            onPrintError={() => messageApi.error('فشل في طباعة الفاتورة')}
          />
          <Popconfirm
            title="هل أنت متأكد من حذف هذه الفاتورة؟"
            description="سيتم حذف الفاتورة نهائياً ولا يمكن التراجع عن هذا الإجراء"
            onConfirm={() => handleDeleteInvoice(record.id)}
            okText="نعم، احذف"
            cancelText="إلغاء"
            okType="danger"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
              title="حذف"
            />
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>إدارة فواتير الشراء</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>إنشاء ومتابعة فواتير الشراء والمدفوعات</p>
        </div>
        <Space>
          <Button onClick={onBack}>رجوع</Button>

          <UnifiedPrintButton
            data={{
              title: "فواتير الشراء",
              data: invoices,
              items: invoices.map(invoice => ({
                id: invoice.id,
                name: `فاتورة رقم ${invoice.invoice_number}`,
                quantity: 1,
                unitPrice: invoice.total_amount,
                price: invoice.total_amount,
                total: invoice.total_amount
              }))
            }}
            title="فواتير الشراء"
            type="invoice"
          />
          <Button
            icon={<FileTextOutlined />}
            onClick={handleExportExcel}
          >
            تصدير Excel
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            فاتورة شراء جديدة
          </Button>
        </Space>
      </div>

      {/* إحصائيات سريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي الفواتير"
              value={stats.total}
              valueStyle={{ color: '#1890ff' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="فواتير معلقة"
              value={stats.pending}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="فواتير مدفوعة"
              value={stats.paid}
              valueStyle={{ color: '#52c41a' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="فواتير متأخرة"
              value={stats.overdue}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="إجمالي المبالغ"
              value={stats.totalAmount}
              valueStyle={{ color: '#52c41a' }}
              prefix={getCurrencySymbol()}
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="المتبقي للدفع"
              value={stats.remainingAmount}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={getCurrencySymbol()}
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      {/* ملخص المدفوعات */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <PaymentSummary
            invoices={invoices.map(invoice => ({
              totalAmount: invoice.total_amount,
              paidAmount: invoice.paid_amount,
              dueDate: invoice.due_date
            }))}
            title="ملخص فواتير المشتريات"
          />
        </Col>
      </Row>



      <Table
        columns={columns}
        dataSource={invoices}
        rowKey="id"
        loading={loading}
        pagination={{
          total: invoices.length,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => range[0] + '-' + range[1] + ' من ' + total + ' فاتورة'
        }}
        scroll={{ x: 1400 }}
        locale={{
          emptyText: invoices.length === 0 ? 'لا توجد فواتير مشتريات' : 'لا توجد بيانات'
        }}
      />

      <Modal
        title={editingInvoice ? 'تعديل فاتورة الشراء' : 'فاتورة شراء جديدة'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
          setEditingInvoice(null)
          setInvoiceItems([])
        }}
        footer={null}
        width={1000}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="رقم الفاتورة"
                name="invoice_number"
                rules={[{ required: true, message: 'يرجى إدخال رقم الفاتورة' }]}
              >
                <Input
                  placeholder="رقم الفاتورة"
                  suffix={
                    <Button type="link" size="small" onClick={generateInvoiceNumber}>
                      إنشاء تلقائي
                    </Button>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="المورد"
                name="supplier_id"
                rules={[{ required: true, message: 'يرجى اختيار المورد' }]}
              >
                <Select
                  placeholder="اختر المورد"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                  }
                >
                  {suppliers.map(supplier => (
                    <Select.Option key={supplier.id} value={supplier.id}>
                      {supplier.name} ({supplier.code})
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="تاريخ الفاتورة"
                name="invoice_date"
                rules={[{ required: true, message: 'يرجى اختيار تاريخ الفاتورة' }]}
                initialValue={dayjs()}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="اختر التاريخ"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="تاريخ الاستحقاق"
                name="due_date"
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="اختر التاريخ"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="حالة الفاتورة"
                name="status"
                initialValue="pending"
              >
                <Select placeholder="اختر الحالة">
                  <Select.Option value="pending">معلقة</Select.Option>
                  <Select.Option value="paid">مدفوعة</Select.Option>
                  <Select.Option value="partial">مدفوعة جزئياً</Select.Option>
                  <Select.Option value="cancelled">ملغية</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="ملاحظات" name="notes">
            <Input.TextArea placeholder="ملاحظات إضافية" rows={2} />
          </Form.Item>

          {/* جدول الأصناف */}
          <div style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
              <h4>أصناف الفاتورة</h4>
              <Button type="dashed" onClick={addInvoiceItem} icon={<PlusOutlined />}>
                إضافة صنف
              </Button>
            </div>
            
            <Table
              dataSource={invoiceItems}
              pagination={false}
              size="small"
              rowKey={(record) => 'invoice-item /* TODO: Define item */-' + record.item_id + '-' + record.warehouse_id}
              columns={[
                {
                  title: 'المخزن',
                  key: 'warehouse',
                  width: 120,
                  render: (_, record, index) => (
                    <Select
                      placeholder="اختر المخزن أولاً"
                      style={{ width: '100%' }}
                      value={record.warehouse_id || undefined}
                      onChange={(value) => updateInvoiceItem(index, 'warehouse_id', value)}
                      showSearch
                      optionFilterProp="children"
                      filterOption={(input, option) =>
                        option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                      }
                    >
                      {warehouses.map(warehouse => (
                        <Select.Option key={warehouse.id} value={warehouse.id}>
                          {warehouse.name}
                        </Select.Option>
                      ))}
                    </Select>
                  )
                },
                {
                  title: 'الصنف',
                  key: 'item /* TODO: Define item */',
                  render: (_, record, index) => (
                    <Select
                      placeholder={record.warehouse_id ? "اختر الصنف" : "اختر المخزن أولاً"}
                      style={{ width: '100%' }}
                      value={record.item_id || undefined}
                      onChange={(value) => updateInvoiceItem(index /* TODO: Define index */, 'item_id', value)}
                      disabled={!record.warehouse_id}
                      showSearch
                      optionFilterProp="children"
                      filterOption={(input, option) =>
                        option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                      }
                    >
                      {filteredItems.map(item => (
                        <Select.Option key={item.id} value={item.id}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <span>{item.name} ({item.code})</span>
                            {(item /* TODO: Define item */ as any).available_quantity !== undefined && (
                              <span style={{ color: '#1890ff', fontSize: '12px' }}>
                                متوفر: {(item /* TODO: Define item */ as any).available_quantity}
                              </span>
                            )}
                          </div>
                        </Select.Option>
                      ))}
                    </Select>
                  )
                },
                {
                  title: 'الكمية',
                  key: 'quantity',
                  width: 100,
                  render: (_, record, index) => (
                    <InputNumber
                      min={0}
                      value={record.quantity}
                      onChange={(value) => updateInvoiceItem(index /* TODO: Define index */, 'quantity', value || 0)}
                      style={{ width: '100%' }}
                    />
                  )
                },
                {
                  title: 'سعر الوحدة (' + getCurrencySymbol() + ')',
                  key: 'unit_price',
                  width: 120,
                  render: (_, record, index) => (
                    <InputNumber
                      min={0}
                      value={record.unit_price}
                      onChange={(value) => updateInvoiceItem(index /* TODO: Define index */, 'unit_price', value || 0)}
                      style={{ width: '100%' }}
                    />
                  )
                },
                {
                  title: 'الإجمالي (' + getCurrencySymbol() + ')',
                  key: 'total_price',
                  width: 120,
                  render: (_, record) => record.total_price.toLocaleString()
                },
                {
                  title: 'إجراءات',
                  key: 'actions',
                  width: 80,
                  render: (_, record, index) => (
                    <Button
                      type="link"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removeInvoiceItem(index /* TODO: Define index */)}
                      size="small"
                    />
                  )
                }
              ]}
            />
          </div>

          {/* ملخص المبالغ */}
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label={'المجموع الفرعي (' + getCurrencySymbol() + ')'} name="subtotal">
                <InputNumber style={{ width: '100%' }} disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label={'الضريبة (' + getCurrencySymbol() + ')'} name="tax_amount">
                <InputNumber style={{ width: '100%' }} disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label={'الخصم (' + getCurrencySymbol() + ')'} name="discount_amount">
                <InputNumber 
                  style={{ width: '100%' }} 
                  min={0}
                  onChange={calculateTotals}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label={'الإجمالي النهائي (' + getCurrencySymbol() + ')'} name="total_amount">
                <InputNumber style={{ width: '100%' }} disabled />
              </Form.Item>
            </Col>
          </Row>

          <Divider>معلومات الدفع</Divider>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="payment_type"
                label="نوع الدفع"
                rules={[{ required: true, message: 'يرجى اختيار نوع الدفع' }]}
                initialValue="credit"
              >
                <Select
                  placeholder="اختر نوع الدفع"
                  onChange={(value) => {
                    const total = form.getFieldValue('total_amount') || 0
                    let paidAmount = 0

                    if (value === 'cash') {
                      paidAmount = total
                    } else if (value === 'credit') {
                      paidAmount = 0
                    }
                    // للدفع الجزئي، نترك المبلغ كما هو

                    const remaining = total - paidAmount
                    form.setFieldsValue({
                      paid_amount: paidAmount,
                      remaining_amount: remaining
                    })
                  }}
                >
                  <Option value="cash">نقدي</Option>
                  <Option value="credit">آجل</Option>
                  <Option value="partial">جزئي</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="paid_amount"
                label="المبلغ المدفوع"
                dependencies={['payment_type', 'total_amount']}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  formatter={value => (getCurrencySymbol() + ' ' + value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => (parseFloat((value || '').replace(new RegExp(getCurrencySymbol() + '\\s?|(,*)','g'), '')) || 0) as any}
                  placeholder="المبلغ المدفوع"
                  onChange={(value) => {
                    const total = form.getFieldValue('total_amount') || 0
                    const remaining = total - (value || 0)
                    form.setFieldsValue({ remaining_amount: remaining })
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="remaining_amount"
                label="المبلغ المتبقي"
                dependencies={['paid_amount', 'total_amount']}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  disabled
                  formatter={value => (getCurrencySymbol() + ' ' + value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  placeholder="المبلغ المتبقي"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="payment_method" label="طريقة الدفع">
                <Select placeholder="اختر طريقة الدفع" allowClear>
                  <Option value="cash">نقدي</Option>
                  <Option value="check">شيك</Option>
                  <Option value="bank_transfer">تحويل بنكي</Option>
                  <Option value="credit_card">بطاقة ائتمان</Option>
                  <Option value="payment_voucher">سند دفع</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="payment_reference" label="مرجع الدفع">
                <Input placeholder="رقم الشيك، رقم التحويل، إلخ..." />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'left' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              {editingInvoice && (
                <InvoicePrintButton
                  invoiceData={{
                    id: editingInvoice.id,
                    invoiceNumber: editingInvoice.invoice_number,
                    invoiceDate: editingInvoice.invoice_date,
                    customerName: editingInvoice.supplier_name || '',
                    items: invoiceItems.map(item => ({
                      name: item.item_name,
                      quantity: item.quantity,
                      unitPrice: item.unit_price,
                      total: item.total_price
                    })),
                    subtotal: editingInvoice.subtotal || 0,
                    discount: editingInvoice.discount_amount || 0,
                    tax: editingInvoice.tax_amount || 0,
                    total: editingInvoice.total_amount || 0,
                    paid: editingInvoice.paid_amount || 0,
                    remaining: (editingInvoice.total_amount || 0) - (editingInvoice.paid_amount || 0)
                  }}
                  invoiceType="purchase"
                  size="middle"
                  buttonText="طباعة الفاتورة"
                  onPrintSuccess={() => messageApi.success('تم طباعة الفاتورة بنجاح')}
                  onPrintError={() => messageApi.error('فشل في طباعة الفاتورة')}
                />
              )}
              <Button type="primary" htmlType="submit">
                {editingInvoice ? 'تحديث الفاتورة' : 'إنشاء الفاتورة'}
              </Button>
              <Button
                type="default"
                htmlType="submit"
                onClick={() => {
                  // حفظ وطباعة باستخدام النظام المتقدم
                  form.validateFields().then(values => {
                    handleSubmit(values).then(async (result) => {
                      if (result?.success) {
                        messageApi.success('تم حفظ الفاتورة، جاري الطباعة...')

                        try {
                          // استخدام نظام الطباعة المتقدم
                          const { MasterPrintService } = await import('../../services/MasterPrintService')

                          // تحضير بيانات الطباعة
                          const supplier = suppliers.find(s => s.id === values.supplier_id)
                          const invoiceData = {
                            id: result.data?.invoiceId || values.invoice_number,
                            title: 'فاتورة مشتريات',
                            number: values.invoice_number,
                            date: values.invoice_date?.format('YYYY-MM-DD') || new Date().toISOString().split('T')[0],
                            dueDate: values.due_date?.format('YYYY-MM-DD'),
                            customer: {
                              name: supplier?.name || 'غير محدد',
                              address: supplier?.address,
                              phone: supplier?.phone,
                              email: supplier?.email
                            },
                            items: invoiceItems.map(item => ({
                              id: item.item_id,
                              name: item.item_name || 'غير محدد',
                              description: item.notes || '',
                              quantity: item.quantity || 0,
                              unit: 'قطعة',
                              unitPrice: item.unit_price || 0,
                              total: item.total_price || 0
                            })),
                            subtotal: values.subtotal || 0,
                            discount: values.discount_amount || 0,
                            tax: values.tax_amount || 0,
                            total: values.total_amount || 0,
                            paid: values.paid_amount || 0,
                            remaining: values.remaining_amount || 0,
                            notes: values.notes,
                            terms: 'شكراً لتعاملكم معنا. يرجى الدفع خلال 30 يوماً من تاريخ الفاتورة.'
                          }

                          // تحميل إعدادات الطباعة
                          const loadPrintSettings = async () => {
                            try {
                              const result = await window.electronAPI?.invoke('get-settings', 'print_')
                              if (result?.success && result.data) {
                                const settingsMap = result.data
                                return {
                                  pageSize: settingsMap.print_page_size || 'A4',
                                  orientation: settingsMap.print_orientation || 'portrait',
                                  fontSize: parseInt(settingsMap.print_font_size) || 12,
                                  fontFamily: settingsMap.print_font_family || 'Arial',
                                  showHeader: settingsMap.print_show_header !== 'false',
                                  showFooter: settingsMap.print_show_footer !== 'false',
                                  showLogo: settingsMap.print_show_logo !== 'false',
                                  primaryColor: settingsMap.print_primary_color || '#1890ff',
                                  margins: {
                                    top: parseInt(settingsMap.print_margin_top) || 20,
                                    bottom: parseInt(settingsMap.print_margin_bottom) || 20,
                                    left: parseInt(settingsMap.print_margin_left) || 20,
                                    right: parseInt(settingsMap.print_margin_right) || 20
                                  }
                                }
                              }
                            } catch (error) {
                              console.warn('فشل في تحميل إعدادات الطباعة:', error)
                            }

                            // الإعدادات الافتراضية
                            return {
                              pageSize: 'A4',
                              orientation: 'portrait',
                              fontSize: 12,
                              fontFamily: 'Arial',
                              showHeader: true,
                              showFooter: true,
                              showLogo: true,
                              primaryColor: '#1890ff',
                              margins: { top: 20, right: 20, bottom: 20, left: 20 }
                            }
                          }

                          const printSettings = await loadPrintSettings()
                          const printService = MasterPrintService.getInstance()

                          await printService.print(invoiceData, {
                            type: 'invoice',
                            subType: 'purchase',
                            ...printSettings,
                            onSuccess: () => {
                              messageApi.success('تم طباعة الفاتورة بنجاح')
                            },
                            onError: (error) => {
                              messageApi.error(`فشل في الطباعة: ${error}`)
                            }
                          })

                        } catch (error) {
                          console.error('خطأ في الطباعة:', error)
                          messageApi.error('فشل في طباعة الفاتورة')
                        }
                      }
                    }).catch(error => {
                      messageApi.error('فشل في حفظ الفاتورة')
                    })
                  })
                }}
              >
                حفظ وطباعة
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* نافذة تسجيل الدفعة */}
      <Modal
        title={'تسجيل دفعة - ' + (selectedInvoiceForPayment?.invoice_number || '')}
        open={paymentModalVisible}
        onCancel={() => setPaymentModalVisible(false)}
        footer={null}
        width={700}
      >
        {selectedInvoiceForPayment && (
          <div>
            {/* معلومات الفاتورة */}
            <Card size="small" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Space direction="vertical" size="small">
                    <span><strong>المورد:</strong> {selectedInvoiceForPayment.supplier_name}</span>
                    <span><strong>تاريخ الفاتورة:</strong> {dayjs(selectedInvoiceForPayment.invoice_date).format('YYYY-MM-DD')}</span>
                  </Space>
                </Col>
                <Col span={12}>
                  <RemainingAmountDisplay
                    totalAmount={selectedInvoiceForPayment.total_amount}
                    paidAmount={selectedInvoiceForPayment.paid_amount}
                    dueDate={selectedInvoiceForPayment.due_date}
                    showDetails={true}
                    size="small"
                  />
                </Col>
              </Row>
            </Card>

            {/* نموذج الدفع */}
            <Form
              form={paymentForm}
              layout="vertical"
              onFinish={handlePayment}
            >
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    name="payment_method"
                    label="طريقة الدفع"
                    rules={[{ required: true, message: 'يرجى اختيار طريقة الدفع' }]}
                  >
                    <PaymentMethodSelector
                      value={selectedPaymentMethod}
                      onChange={setSelectedPaymentMethod}
                      allowedMethods={[
                        PAYMENT_METHODS.CASH,
                        PAYMENT_METHODS.CHECK,
                        PAYMENT_METHODS.BANK_TRANSFER,
                        PAYMENT_METHODS.PAYMENT_VOUCHER
                      ]}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <PaymentForm
                paymentMethod={selectedPaymentMethod}
                maxAmount={selectedInvoiceForPayment.total_amount - selectedInvoiceForPayment.paid_amount}
              />

              <div style={{ textAlign: 'left', marginTop: 24 }}>
                <Space>
                  <Button onClick={() => setPaymentModalVisible(false)}>
                    إلغاء
                  </Button>
                  <Button type="primary" htmlType="submit">
                    تسجيل الدفعة
                  </Button>
                </Space>
              </div>
            </Form>

            {/* سجل المدفوعات السابقة */}
            {invoicePayments.length > 0 && (
              <div style={{ marginTop: 24 }}>
                <Divider>المدفوعات السابقة</Divider>
                <Table
                  dataSource={invoicePayments}
                  size="small"
                  pagination={false}
                  loading={loadingPayments}
                  columns={[
                    {
                      title: 'التاريخ',
                      dataIndex: 'payment_date',
                      key: 'payment_date',
                      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
                    },
                    {
                      title: 'المبلغ',
                      dataIndex: 'amount',
                      key: 'amount',
                      render: (amount: number) => formatCurrency(amount)
                    },
                    {
                      title: 'طريقة الدفع',
                      dataIndex: 'payment_method',
                      key: 'payment_method'
                    },
                    {
                      title: 'المرجع',
                      dataIndex: 'reference_number',
                      key: 'reference_number',
                      render: (ref: string) => ref || '-'
                    }
                  ]}
                />
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default PurchaseInvoiceManagement
