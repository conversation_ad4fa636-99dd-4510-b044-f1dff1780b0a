import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  DatePicker,
  Select,
  Input,
  Button,
  Space,
  Typography,
  Divider,
  Tag,
  Tooltip,
  Collapse
} from 'antd'
import {
  FilterOutlined,
  ClearOutlined,
  SearchOutlined,
  CalendarOutlined,
  UserOutlined,
  ShopOutlined,
  TagOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker
const { Option } = Select
const { Search } = Input
const { Text } = Typography

export interface FilterValues {
  dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null
  customers: string[]
  products: string[]
  categories: string[]
  status: string
  amountRange: [number, number] | null
  searchText: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

interface AdvancedFiltersProps {
  onFiltersChange: (filters: FilterValues) => void
  showCustomers?: boolean
  showProducts?: boolean
  showCategories?: boolean
  showStatus?: boolean
  showAmountRange?: boolean
  customFilters?: React.ReactNode
  loading?: boolean
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  onFiltersChange,
  showCustomers = true,
  showProducts = true,
  showCategories = true,
  showStatus = true,
  showAmountRange = true,
  customFilters,
  loading = false
}) => {
  const [filters, setFilters] = useState<FilterValues>({
    dateRange: [dayjs().subtract(30, 'days'), dayjs()],
    customers: [],
    products: [],
    categories: [],
    status: 'all',
    amountRange: null,
    searchText: '',
    sortBy: 'date',
    sortOrder: 'desc'
  })

  const [activeFiltersCount, setActiveFiltersCount] = useState(0)

  // بيانات تجريبية للخيارات
  const customerOptions = [
    { value: 'CUS001', label: 'شركة الأثاث الفاخر' },
    { value: 'CUS002', label: 'مؤسسة البيت الحديث' },
    { value: 'CUS003', label: 'تجارة الديكور المتميز' },
    { value: 'CUS004', label: 'معرض النجمة للأثاث' },
    { value: 'CUS005', label: 'شركة الإبداع للديكور' }
  ]

  const productOptions = [
    { value: 'FUR001', label: 'كرسي خشبي كلاسيكي' },
    { value: 'FUR002', label: 'طاولة طعام خشبية' },
    { value: 'FUR003', label: 'خزانة ملابس حديثة' },
    { value: 'FUR004', label: 'مكتب خشبي مودرن' },
    { value: 'PAINT001', label: 'دهان أكريليك أبيض' },
    { value: 'PAINT002', label: 'دهان خشبي بني' }
  ]

  const categoryOptions = [
    { value: 'furniture', label: 'الأثاث' },
    { value: 'paints', label: 'الدهانات' },
    { value: 'accessories', label: 'الإكسسوارات' },
    { value: 'tools', label: 'الأدوات' }
  ]

  const statusOptions = [
    { value: 'all', label: 'جميع الحالات' },
    { value: 'paid', label: 'مدفوع' },
    { value: 'pending', label: 'معلق' },
    { value: 'overdue', label: 'متأخر' },
    { value: 'cancelled', label: 'ملغي' }
  ]

  const sortOptions = [
    { value: 'date', label: 'التاريخ' },
    { value: 'amount', label: 'المبلغ' },
    { value: 'customer', label: 'العميل' },
    { value: 'product', label: 'المنتج' },
    { value: 'status', label: 'الحالة' }
  ]

  useEffect(() => {
    // حساب عدد الفلاتر النشطة
    let count = 0
    if (filters.customers.length > 0) count++
    if (filters.products.length > 0) count++
    if (filters.categories.length > 0) count++
    if (filters.status !== 'all') count++
    if (filters.amountRange) count++
    if (filters.searchText.trim()) count++
    
    setActiveFiltersCount(count)
    onFiltersChange(filters)
  }, [filters, onFiltersChange])

  const updateFilter = (key: keyof FilterValues, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearAllFilters = () => {
    setFilters({
      dateRange: [dayjs().subtract(30, 'days'), dayjs()],
      customers: [],
      products: [],
      categories: [],
      status: 'all',
      amountRange: null,
      searchText: '',
      sortBy: 'date',
      sortOrder: 'desc'
    })
  }

  const clearSpecificFilter = (filterKey: keyof FilterValues) => {
    switch (filterKey) {
      case 'customers':
      case 'products':
      case 'categories':
        updateFilter(filterKey, [])
        break
      case 'status':
        updateFilter(filterKey, 'all')
        break
      case 'amountRange':
        updateFilter(filterKey, null)
        break
      case 'searchText':
        updateFilter(filterKey, '')
        break
    }
  }

  const renderActiveFilters = () => {
    const activeTags: React.ReactNode[] = []

    if (filters.customers.length > 0) {
      activeTags.push(
        <Tag
          key="customers"
          closable
          onClose={() => clearSpecificFilter('customers')}
          color="blue"
        >
          <UserOutlined /> العملاء ({filters.customers.length})
        </Tag>
      )
    }

    if (filters.products.length > 0) {
      activeTags.push(
        <Tag
          key="products"
          closable
          onClose={() => clearSpecificFilter('products')}
          color="green"
        >
          <ShopOutlined /> المنتجات ({filters.products.length})
        </Tag>
      )
    }

    if (filters.categories.length > 0) {
      activeTags.push(
        <Tag
          key="categories"
          closable
          onClose={() => clearSpecificFilter('categories')}
          color="orange"
        >
          <TagOutlined /> الفئات ({filters.categories.length})
        </Tag>
      )
    }

    if (filters.status !== 'all') {
      const statusLabel = statusOptions.find(opt => opt.value === filters.status)?.label
      activeTags.push(
        <Tag
          key="status"
          closable
          onClose={() => clearSpecificFilter('status')}
          color="purple"
        >
          الحالة: {statusLabel}
        </Tag>
      )
    }

    if (filters.searchText.trim()) {
      activeTags.push(
        <Tag
          key="search"
          closable
          onClose={() => clearSpecificFilter('searchText')}
          color="cyan"
        >
          <SearchOutlined /> البحث: {filters.searchText}
        </Tag>
      )
    }

    return activeTags
  }

  return (
    <Card 
      style={{ marginBottom: '16px' }}
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space>
            <FilterOutlined style={{ color: '#1890ff' }} />
            <Text strong>فلاتر متقدمة</Text>
            {activeFiltersCount > 0 && (
              <Tag color="red" style={{ margin: 0 }}>
                {activeFiltersCount} فلتر نشط
              </Tag>
            )}
          </Space>
          <Space>
            <Tooltip title="مسح جميع الفلاتر">
              <Button
                type="text"
                icon={<ClearOutlined />}
                onClick={clearAllFilters}
                disabled={activeFiltersCount === 0}
              >
                مسح الكل
              </Button>
            </Tooltip>
            <Tooltip title="تحديث">
              <Button
                type="text"
                icon={<ReloadOutlined />}
                loading={loading}
              >
                تحديث
              </Button>
            </Tooltip>
          </Space>
        </div>
      }
    >
      <Collapse
        defaultActiveKey={['basic']}
        ghost
        items={[
          {
            key: 'basic',
            label: 'الفلاتر الأساسية',
            children: (
          <Row gutter={[16, 16]}>
            {/* فترة التاريخ */}
            <Col span={8}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text strong>
                  <CalendarOutlined style={{ marginLeft: '8px' }} />
                  فترة التاريخ:
                </Text>
                <RangePicker
                  value={filters.dateRange}
                  onChange={(dates) => updateFilter('dateRange', dates)}
                  format="YYYY-MM-DD"
                  style={{ width: '100%' }}
                  placeholder={['من تاريخ', 'إلى تاريخ']}
                />
              </Space>
            </Col>

            {/* البحث النصي */}
            <Col span={8}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text strong>
                  <SearchOutlined style={{ marginLeft: '8px' }} />
                  البحث:
                </Text>
                <Search
                  placeholder="البحث في جميع الحقول..."
                  value={filters.searchText}
                  onChange={(e) => updateFilter('searchText', e.target.value)}
                  style={{ width: '100%' }}
                />
              </Space>
            </Col>

            {/* الترتيب */}
            <Col span={8}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text strong>الترتيب:</Text>
                <Space.Compact style={{ width: '100%' }}>
                  <Select
                    value={filters.sortBy}
                    onChange={(value) => updateFilter('sortBy', value)}
                    style={{ width: '70%' }}
                  >
                    {sortOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                  <Select
                    value={filters.sortOrder}
                    onChange={(value) => updateFilter('sortOrder', value)}
                    style={{ width: '30%' }}
                  >
                    <Option value="asc">تصاعدي</Option>
                    <Option value="desc">تنازلي</Option>
                  </Select>
                </Space.Compact>
              </Space>
            </Col>
          </Row>
            )
          },
          {
            key: 'advanced',
            label: 'فلاتر متخصصة',
            children: (
          <Row gutter={[16, 16]}>
            {/* العملاء */}
            {showCustomers && (
              <Col span={8}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Text strong>
                    <UserOutlined style={{ marginLeft: '8px' }} />
                    العملاء:
                  </Text>
                  <Select
                    mode="multiple"
                    placeholder="اختر العملاء..."
                    value={filters.customers}
                    onChange={(value) => updateFilter('customers', value)}
                    style={{ width: '100%' }}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    options={customerOptions}
                  />
                </Space>
              </Col>
            )}

            {/* المنتجات */}
            {showProducts && (
              <Col span={8}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Text strong>
                    <ShopOutlined style={{ marginLeft: '8px' }} />
                    المنتجات:
                  </Text>
                  <Select
                    mode="multiple"
                    placeholder="اختر المنتجات..."
                    value={filters.products}
                    onChange={(value) => updateFilter('products', value)}
                    style={{ width: '100%' }}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    options={productOptions}
                  />
                </Space>
              </Col>
            )}

            {/* الفئات */}
            {showCategories && (
              <Col span={8}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Text strong>
                    <TagOutlined style={{ marginLeft: '8px' }} />
                    الفئات:
                  </Text>
                  <Select
                    mode="multiple"
                    placeholder="اختر الفئات..."
                    value={filters.categories}
                    onChange={(value) => updateFilter('categories', value)}
                    style={{ width: '100%' }}
                    options={categoryOptions}
                  />
                </Space>
              </Col>
            )}

            {/* الحالة */}
            {showStatus && (
              <Col span={8}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Text strong>الحالة:</Text>
                  <Select
                    value={filters.status}
                    onChange={(value) => updateFilter('status', value)}
                    style={{ width: '100%' }}
                    options={statusOptions}
                  />
                </Space>
              </Col>
            )}

            {/* نطاق المبلغ */}
            {showAmountRange && (
              <Col span={8}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Text strong>نطاق المبلغ (₪):</Text>
                  <Space.Compact style={{ width: '100%' }}>
                    <Input
                      placeholder="من"
                      type="number"
                      value={filters.amountRange?.[0] || ''}
                      onChange={(e) => {
                        const value = e.target.value ? Number(e.target.value) : null
                        updateFilter('amountRange', value ? [value, filters.amountRange?.[1] || 0] : null)
                      }}
                      style={{ width: '50%' }}
                    />
                    <Input
                      placeholder="إلى"
                      type="number"
                      value={filters.amountRange?.[1] || ''}
                      onChange={(e) => {
                        const value = e.target.value ? Number(e.target.value) : null
                        updateFilter('amountRange', value ? [filters.amountRange?.[0] || 0, value] : null)
                      }}
                      style={{ width: '50%' }}
                    />
                  </Space.Compact>
                </Space>
              </Col>
            )}

            {/* فلاتر مخصصة */}
            {customFilters && (
              <Col span={24}>
                <Divider orientation="right">فلاتر إضافية</Divider>
                {customFilters}
              </Col>
            )}
          </Row>
            )
          }
        ]}
      />

      {/* عرض الفلاتر النشطة */}
      {activeFiltersCount > 0 && (
        <>
          <Divider style={{ margin: '16px 0 12px 0' }} />
          <div>
            <Text strong style={{ marginLeft: '12px' }}>الفلاتر النشطة:</Text>
            <Space wrap>
              {renderActiveFilters()}
            </Space>
          </div>
        </>
      )}
    </Card>
  )
}

export default AdvancedFilters
