// إعدادات نظام التسجيل
import * as fs from 'fs'
import * as path from 'path'
import { Logger } from './logger'

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

export interface LoggerConfig {
  level: LogLevel
  enableFileLogging: boolean
  enableConsoleLogging: boolean
  maxLogFileSize: number // بالميجابايت
  maxLogFiles: number
  logDirectory: string
  enablePerformanceLogging: boolean
  enableSensitiveDataSanitization: boolean
  enableErrorMonitoring: boolean
}

export class LoggerConfigManager {
  private static configPath = 'config/logger.json'
  private static defaultConfig: LoggerConfig = {
    level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.WARN,
    enableFileLogging: true,
    enableConsoleLogging: true,
    maxLogFileSize: 10, // 10 MB
    maxLogFiles: 5,
    logDirectory: 'logs',
    enablePerformanceLogging: process.env.NODE_ENV === 'development',
    enableSensitiveDataSanitization: true,
    enableErrorMonitoring: process.env.NODE_ENV === 'production'
  }

  // تحميل الإعدادات
  static loadConfig(): LoggerConfig {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8')
        const config = JSON.parse(configData)
        
        // دمج مع الإعدادات الافتراضية
        return { ...this.defaultConfig, ...config }
      }
    } catch (error) {
      Logger.error('LoggerConfig', 'خطأ في العملية', error)
      
      // يمكن إضافة معالجة إضافية هنا حسب الحاجة
      // مثل: عرض رسالة للمستخدم، إعادة المحاولة، إلخ
      
      throw error // إعادة رمي الخطأ للمعالجة في مستوى أعلى
    }
    
    return this.defaultConfig
  }

  // حفظ الإعدادات
  static saveConfig(config: LoggerConfig): boolean {
    try {
      // إنشاء مجلد config إذا لم يكن موجوداً
      const configDir = path.dirname(this.configPath)
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true })
      }

      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2))
      return true
    } catch (error) {
      // استخدام console مقبول في ملف إعدادات Logger
      console.error('فشل في حفظ إعدادات Logger:', error)
      return false
    }
  }

  // تحديث مستوى التسجيل
  static updateLogLevel(level: LogLevel): boolean {
    const config = this.loadConfig()
    config.level = level
    return this.saveConfig(config)
  }

  // تفعيل/إلغاء تفعيل تسجيل الملفات
  static toggleFileLogging(enabled: boolean): boolean {
    const config = this.loadConfig()
    config.enableFileLogging = enabled
    return this.saveConfig(config)
  }

  // تفعيل/إلغاء تفعيل تسجيل الكونسول
  static toggleConsoleLogging(enabled: boolean): boolean {
    const config = this.loadConfig()
    config.enableConsoleLogging = enabled
    return this.saveConfig(config)
  }

  // تفعيل/إلغاء تفعيل تسجيل الأداء
  static togglePerformanceLogging(enabled: boolean): boolean {
    const config = this.loadConfig()
    config.enablePerformanceLogging = enabled
    return this.saveConfig(config)
  }

  // الحصول على اسم مستوى التسجيل
  static getLogLevelName(level: LogLevel): string {
    switch (level) {
      case LogLevel.DEBUG: return 'DEBUG'
      case LogLevel.INFO: return 'INFO'
      case LogLevel.WARN: return 'WARN'
      case LogLevel.ERROR: return 'ERROR'
      case LogLevel.NONE: return 'NONE'
      default: return 'UNKNOWN'
    }
  }

  // الحصول على جميع مستويات التسجيل المتاحة
  static getAvailableLogLevels(): Array<{ value: LogLevel; name: string; description: string }> {
    return [
      {
        value: LogLevel.DEBUG,
        name: 'DEBUG',
        description: 'جميع الرسائل (للتطوير)'
      },
      {
        value: LogLevel.INFO,
        name: 'INFO',
        description: 'معلومات عامة وأخطاء وتحذيرات'
      },
      {
        value: LogLevel.WARN,
        name: 'WARN',
        description: 'تحذيرات وأخطاء فقط'
      },
      {
        value: LogLevel.ERROR,
        name: 'ERROR',
        description: 'أخطاء فقط'
      },
      {
        value: LogLevel.NONE,
        name: 'NONE',
        description: 'إيقاف التسجيل'
      }
    ]
  }

  // إنشاء إعدادات للبيئات المختلفة
  static createEnvironmentConfig(environment: 'development' | 'production' | 'testing'): LoggerConfig {
    const baseConfig = { ...this.defaultConfig }

    switch (environment) {
      case 'development':
        return {
          ...baseConfig,
          level: LogLevel.DEBUG,
          enableFileLogging: true,
          enableConsoleLogging: true,
          enablePerformanceLogging: true,
          enableErrorMonitoring: false
        }

      case 'production':
        return {
          ...baseConfig,
          level: LogLevel.WARN,
          enableFileLogging: true,
          enableConsoleLogging: false,
          enablePerformanceLogging: false,
          enableErrorMonitoring: true,
          maxLogFileSize: 50, // 50 MB للإنتاج
          maxLogFiles: 10
        }

      case 'testing':
        return {
          ...baseConfig,
          level: LogLevel.ERROR,
          enableFileLogging: false,
          enableConsoleLogging: true,
          enablePerformanceLogging: false,
          enableErrorMonitoring: false
        }

      default:
        return baseConfig
    }
  }

  // تصدير الإعدادات
  static exportConfig(): string {
    const config = this.loadConfig()
    return JSON.stringify(config, null, 2)
  }

  // استيراد الإعدادات
  static importConfig(configJson: string): boolean {
    try {
      const config = JSON.parse(configJson)
      
      // التحقق من صحة الإعدادات
      if (this.validateConfig(config)) {
        return this.saveConfig(config)
      } else {
        // استخدام console مقبول في ملف إعدادات Logger
      console.error('إعدادات Logger غير صحيحة')
        return false
      }
    } catch (error) {
      // استخدام console مقبول في ملف إعدادات Logger
      console.error('فشل في استيراد إعدادات Logger:', error)
      return false
    }
  }

  // التحقق من صحة الإعدادات
  private static validateConfig(config: any): boolean {
    return (
      typeof config === 'object' &&
      typeof config.level === 'number' &&
      config.level >= 0 && config.level <= 4 &&
      typeof config.enableFileLogging === 'boolean' &&
      typeof config.enableConsoleLogging === 'boolean' &&
      typeof config.maxLogFileSize === 'number' &&
      typeof config.maxLogFiles === 'number' &&
      typeof config.logDirectory === 'string'
    )
  }

  // إعادة تعيين الإعدادات للافتراضية
  static resetToDefaults(): boolean {
    return this.saveConfig(this.defaultConfig)
  }

  // الحصول على معلومات الإعدادات الحالية
  static getConfigInfo(): {
    current: LoggerConfig
    isDefault: boolean
    configExists: boolean
  } {
    const current = this.loadConfig()
    const configExists = fs.existsSync(this.configPath)
    const isDefault = JSON.stringify(current) === JSON.stringify(this.defaultConfig)

    return {
      current,
      isDefault,
      configExists
    }
  }
}
