import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Space,
  Tag,
  Tooltip,
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Alert,
  Popconfirm
} from 'antd'
import { SafeLogger as Logger } from '../../../utils/logger'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  WarningOutlined,
  InboxOutlined,
  ShoppingCartOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  BarcodeOutlined,
  FileExcelOutlined,
  PrinterOutlined
} from '@ant-design/icons'
import * as XLSX from 'xlsx'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

interface Material {
  id: number
  code: string
  name: string
  description?: string
  category: string
  unit: string
  cost: number
  current_stock: number
  min_stock: number
  max_stock: number
  supplier_id?: number
  supplier_name?: string
  location?: string
  is_active: boolean
  created_at: string
}

interface MaterialsManagementProps {
  onBack?: () => void
}

const MaterialsManagement: React.FC<MaterialsManagementProps> = ({ onBack: _onBack }) => {
  const [materials, setMaterials] = useState<Material[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingMaterial, setEditingMaterial] = useState<Material | null>(null)
  const [form] = Form.useForm()
  const [suppliers, setSuppliers] = useState<any[]>([])

  // إحصائيات سريعة
  const [stats, setStats] = useState({
    total: 0,
    low_stock: 0,
    out_of_stock: 0,
    total_value: 0,
    avg_stock_level: 0
  })

  useEffect(() => {
    loadMaterials()
    loadSuppliers()
  }, [])

  const loadMaterials = async () => {
    setLoading(true)
    try {
      Logger.info('MaterialsManagement', '🔄 جاري تحميل المواد الخام...')

      if (!window.electronAPI) {
        Logger.error('MaterialsManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('MaterialsManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للمواد الخام
        const mockMaterials = [
          {
            id: 1,
            name: 'خشب زان',
            code: 'WOOD001',
            type: 'raw_material',
            category: 'مواد خام',
            unit: 'متر مكعب',
            cost_price: 500,
            sale_price: 600,
            min_quantity: 10,
            max_quantity: 100,
            current_stock: 50,
            is_active: true
          },
          {
            id: 2,
            name: 'مسامير خشب',
            code: 'SCREW001',
            type: 'raw_material',
            category: 'خامات',
            unit: 'كيلو',
            cost_price: 15,
            sale_price: 20,
            min_quantity: 5,
            max_quantity: 50,
            current_stock: 25,
            is_active: true
          },
          {
            id: 3,
            name: 'غراء خشب',
            code: 'GLUE001',
            type: 'raw_material',
            category: 'مواد خام',
            unit: 'لتر',
            cost_price: 25,
            sale_price: 35,
            min_quantity: 2,
            max_quantity: 20,
            current_stock: 8,
            is_active: true
          }
        ]

        setMaterials(mockMaterials as any[])
        calculateStats(mockMaterials as any[])
        Logger.info('MaterialsManagement', '✅ تم تحميل ' + mockMaterials.length + ' مادة خام وهمية')
      } else {
        // تحميل المواد الخام مع بيانات المخزون
        const [itemsResult, inventoryResult] = await Promise.all([
          window.electronAPI.getItemsByType('raw_material'),
          window.electronAPI.getInventory()
        ])

        if (itemsResult && (itemsResult as any).success && inventoryResult) {
          // استخدام المواد الخام المفلترة حسب النوع
          const rawMaterials = (itemsResult as any).data

          // ربط المواد بالمخزون
          const materialsData = rawMaterials.map((item: any) => {
            // البحث عن بيانات المخزون لهذا الصنف
            const inventoryData = inventoryResult.find((inv: any) => inv.item_id === item.id)

            return {
              ...item,
              current_stock: inventoryData?.quantity || 0,
              reserved_stock: inventoryData?.reserved_quantity || 0,
              available_stock: (inventoryData?.quantity || 0) - (inventoryData?.reserved_quantity || 0),
              warehouse_name: inventoryData?.warehouse_name || 'المخزن الرئيسي',
              location: inventoryData?.location || item.location,
              last_updated: inventoryData?.last_updated
            }
          })

          setMaterials(materialsData)
          calculateStats(materialsData)
          Logger.info('MaterialsManagement', '✅ تم تحميل ' + materialsData.length + ' مادة خام مع بيانات المخزون')
        } else {
          message.error('فشل في تحميل المواد أو بيانات المخزون')
        }
      }
    } catch (error) {
      message.error('خطأ في تحميل المواد')
    }
    setLoading(false)
  }

  const loadSuppliers = async () => {
    try {
      Logger.info('MaterialsManagement', '🔄 جاري تحميل الموردين...')

      if (!window.electronAPI) {
        Logger.error('MaterialsManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('MaterialsManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية للموردين
        const mockSuppliers = [
          { id: 1, name: 'مورد الأخشاب الرئيسي', code: 'SUP001', is_active: true },
          { id: 2, name: 'مورد المسامير والأدوات', code: 'SUP002', is_active: true },
          { id: 3, name: 'مورد المواد الكيميائية', code: 'SUP003', is_active: true }
        ]

        setSuppliers(mockSuppliers)
        Logger.info('MaterialsManagement', '✅ تم تحميل ' + mockSuppliers.length + ' مورد وهمي')
      } else {
        const result = await window.electronAPI.getSuppliers()
        if (result.success) {
          setSuppliers(result.data)
          Logger.info('MaterialsManagement', '✅ تم تحميل الموردين من قاعدة البيانات')
        }
      }
    } catch (error) {
      Logger.error('MaterialsManagement', 'خطأ في تحميل الموردين:', error)
    }
  }

  const calculateStats = (materialsData: Material[]) => {
    const stats = {
      total: materialsData.length,
      low_stock: materialsData.filter(m => m.current_stock <= m.min_stock && m.current_stock > 0).length,
      out_of_stock: materialsData.filter(m => m.current_stock <= 0).length,
      total_value: materialsData.reduce((sum, m) => sum + (m.current_stock * m.cost), 0),
      avg_stock_level: materialsData.length > 0 ? 
        materialsData.reduce((sum, m) => {
          const stockLevel = m.max_stock > 0 ? (m.current_stock / m.max_stock) * 100 : 0
          return sum + stockLevel
        }, 0) / materialsData.length : 0
    }
    setStats(stats)
  }

  // دالة إنشاء الكود التلقائي
  const generateCode = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generateItemCode()
        if (response.success && response.data) {
          form.setFieldsValue({ code: response.data.code })
          message.success('تم إنشاء الكود تلقائياً')
        } else {
          message.error(response.message || 'فشل في إنشاء الكود')
        }
      }
    } catch (error) {
      Logger.error('MaterialsManagement', 'خطأ في إنشاء الكود:', error)
      message.error('فشل في إنشاء الكود')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      // التحقق من صحة البيانات الأساسية
      if (!values.name || values.name.trim().length === 0) {
        message.error('اسم المادة مطلوب')
        return
      }

      if (values.name.length > 100) {
        message.error('اسم المادة طويل جداً')
        return
      }

      if (!values.unit || values.unit.trim().length === 0) {
        message.error('وحدة القياس مطلوبة')
        return
      }

      // التحقق من صحة التكلفة
      if (!values.cost || values.cost <= 0) {
        message.error('يجب أن تكون التكلفة أكبر من الصفر')
        return
      }

      if (values.cost > 100000) {
        message.error('التكلفة كبيرة جداً، يرجى التحقق من القيمة')
        return
      }

      // التحقق من صحة كميات المخزون
      if (values.current_stock && values.current_stock < 0) {
        message.error('الكمية الحالية لا يمكن أن تكون سالبة')
        return
      }

      if (values.min_stock && values.min_stock < 0) {
        message.error('الحد الأدنى لا يمكن أن يكون سالباً')
        return
      }

      if (values.max_stock && values.max_stock < 0) {
        message.error('الحد الأقصى لا يمكن أن يكون سالباً')
        return
      }

      if (values.min_stock && values.max_stock && values.min_stock >= values.max_stock) {
        message.error('الحد الأدنى يجب أن يكون أقل من الحد الأقصى')
        return
      }

      if (values.current_stock && values.max_stock && values.current_stock > values.max_stock) {
        message.error('الكمية الحالية لا يمكن أن تكون أكبر من الحد الأقصى')
        return
      }

      const materialData = {
        ...values,
        type: 'raw_material',
        category_id: values.category_id ? parseInt(String(values.category_id)) : null, // تحويل معرف الفئة إلى رقم صحيح
        supplier_id: values.supplier_id ? parseInt(String(values.supplier_id)) : null, // تحويل معرف المورد إلى رقم صحيح
        cost_price: values.cost || 0, // تحويل cost إلى cost_price
        sale_price: values.cost ? values.cost * 1.2 : 0 // سعر البيع = التكلفة + 20%
      }

      let result
      if (editingMaterial) {
        result = await window.electronAPI.updateItem(editingMaterial.id, materialData)
      } else {
        result = await window.electronAPI.createItem(materialData)
      }

      if (result.success) {
        message.success(editingMaterial ? 'تم تحديث المادة بنجاح' : 'تم إنشاء المادة بنجاح')
        setModalVisible(false)
        setEditingMaterial(null)
        form.resetFields()
        loadMaterials()
      } else {
        message.error(result.message || 'فشل في حفّ المادة')
      }
    } catch (error) {
      message.error('خطأ في حفّ المادة')
    }
  }

  const handleEdit = (material: Material) => {
    setEditingMaterial(material)
    form.setFieldsValue(material)
    setModalVisible(true)
  }

  const handleDelete = async (materialId: number) => {
    try {
      if (!window.electronAPI) {
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن حذف المواد')
        return
      }

      const result = await window.electronAPI.deleteItem(materialId)
      if (result.success) {
        message.success('تم حذف المادة بنجاح')
        loadMaterials()
      } else {
        message.error(result.message || 'فشل في حذف المادة')
      }
    } catch (error) {
      message.error('خطأ في حذف المادة')
    }
  }

  const getStockStatus = (material: Material) => {
    if (material.current_stock <= 0) {
      return { status: 'نفدت', color: 'red', icon: <ExclamationCircleOutlined /> }
    } else if (material.current_stock <= material.min_stock) {
      return { status: 'منخفض', color: 'orange', icon: <WarningOutlined /> }
    } else {
      return { status: 'متوفر', color: 'green', icon: <CheckCircleOutlined /> }
    }
  }

  const getStockLevel = (material: Material) => {
    if (material.max_stock <= 0) return 0
    return Math.min((material.current_stock / material.max_stock) * 100, 100)
  }

  // دالة تصدير Excel
  const handleExportExcel = () => {
    try {
      // تحضير البيانات للتصدير
      const exportData = materials.map(material => ({
        'الكود': material.code,
        'اسم المادة': material.name,
        'الوصف': material.description || '-',
        'الفئة': material.category,
        'وحدة القياس': material.unit,
        'التكلفة': material.cost,
        'الكمية الحالية': material.current_stock,
        'الحد الأدنى': material.min_stock,
        'الحد الأقصى': material.max_stock,
        'قيمة المخزون': (material.current_stock * material.cost).toFixed(2),
        'المورد': material.supplier_name || '-',
        'الموقع': material.location || '-',
        'الحالة': material.is_active ? 'نشط' : 'غير نشط',
        'تاريخ الإنشاء': material.created_at
      }))

      // إنشاء ورقة العمل
      const worksheet = XLSX.utils.json_to_sheet(exportData)

      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 12 }, // الكود
        { wch: 25 }, // اسم المادة
        { wch: 30 }, // الوصف
        { wch: 15 }, // الفئة
        { wch: 12 }, // وحدة القياس
        { wch: 12 }, // التكلفة
        { wch: 15 }, // الكمية الحالية
        { wch: 12 }, // الحد الأدنى
        { wch: 12 }, // الحد الأقصى
        { wch: 15 }, // قيمة المخزون
        { wch: 20 }, // المورد
        { wch: 15 }, // الموقع
        { wch: 10 }, // الحالة
        { wch: 20 }  // تاريخ الإنشاء
      ]
      worksheet['!cols'] = columnWidths

      // إنشاء المصنف
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'المواد الخام')

      // إضافة ورقة إحصائيات
      const stats = [
        { 'المؤشر': 'إجمالي المواد', 'القيمة': materials.length },
        { 'المؤشر': 'مواد نفدت من المخزون', 'القيمة': outOfStockMaterials.length },
        { 'المؤشر': 'مواد منخفضة المخزون', 'القيمة': lowStockMaterials.length },
        { 'المؤشر': 'إجمالي قيمة المخزون', 'القيمة': materials.reduce((sum, m) => sum + (m.current_stock * m.cost), 0).toFixed(2) },
        { 'المؤشر': 'متوسط التكلفة', 'القيمة': materials.length > 0 ? (materials.reduce((sum, m) => sum + m.cost, 0) / materials.length).toFixed(2) : 0 },
        { 'المؤشر': 'إجمالي الكمية', 'القيمة': materials.reduce((sum, m) => sum + m.current_stock, 0) }
      ]

      const statsWorksheet = XLSX.utils.json_to_sheet(stats)
      statsWorksheet['!cols'] = [{ wch: 25 }, { wch: 20 }]
      XLSX.utils.book_append_sheet(workbook, statsWorksheet, 'الإحصائيات')

      // حفّ الملف
      const fileName = 'المواد_الخام_' + dayjs().format('YYYY-MM-DD_HH-mm') + '.xlsx'
      XLSX.writeFile(workbook, fileName)

      message.success('تم تصدير البيانات بنجاح')
    } catch (error) {
      Logger.error('MaterialsManagement', 'خطأ في تصدير البيانات:', error)
      message.error('حدث خطأ أثناء تصدير البيانات')
    }
  }

  // دالة الطباعة
  const handlePrint = () => {
    try {
      const printContent = `
        <div style="padding: 20px; font-family: Arial, sans-serif;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #1890ff; margin-bottom: 10px;">تقرير المواد الخام</h1>
            <p style="color: #666; font-size: 14px;">تاريخ التقرير: ${dayjs().format('YYYY/MM/DD HH:mm')}</p>
          </div>

          <div style="margin-bottom: 30px;">
            <h3 style="color: #1890ff; border-bottom: 2px solid #1890ff; padding-bottom: 5px;">الإحصائيات العامة</h3>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin: 20px 0;">
              <div style="text-align: center; padding: 15px; border: 1px solid #d9d9d9; border-radius: 6px;">
                <div style="font-size: 24px; font-weight: bold; color: #1890ff;">${materials.length}</div>
                <div style="color: #666;">إجمالي المواد</div>
              </div>
              <div style="text-align: center; padding: 15px; border: 1px solid #d9d9d9; border-radius: 6px;">
                <div style="font-size: 24px; font-weight: bold; color: #ff4d4f;">${outOfStockMaterials.length}</div>
                <div style="color: #666;">نفدت من المخزون</div>
              </div>
              <div style="text-align: center; padding: 15px; border: 1px solid #d9d9d9; border-radius: 6px;">
                <div style="font-size: 24px; font-weight: bold; color: #faad14;">${lowStockMaterials.length}</div>
                <div style="color: #666;">منخفضة المخزون</div>
              </div>
              <div style="text-align: center; padding: 15px; border: 1px solid #d9d9d9; border-radius: 6px;">
                <div style="font-size: 24px; font-weight: bold; color: #52c41a;">₪${materials.reduce((sum, m) => sum + (m.current_stock * m.cost), 0).toFixed(2)}</div>
                <div style="color: #666;">قيمة المخزون</div>
              </div>
            </div>
          </div>

          <div>
            <h3 style="color: #1890ff; border-bottom: 2px solid #1890ff; padding-bottom: 5px;">تفاصيل المواد</h3>
            <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
              <thead>
                <tr style="background-color: #f5f5f5;">
                  <th style="border: 1px solid #d9d9d9; padding: 12px; text-align: right;">الكود</th>
                  <th style="border: 1px solid #d9d9d9; padding: 12px; text-align: right;">اسم المادة</th>
                  <th style="border: 1px solid #d9d9d9; padding: 12px; text-align: right;">الفئة</th>
                  <th style="border: 1px solid #d9d9d9; padding: 12px; text-align: right;">الكمية الحالية</th>
                  <th style="border: 1px solid #d9d9d9; padding: 12px; text-align: right;">التكلفة</th>
                  <th style="border: 1px solid #d9d9d9; padding: 12px; text-align: right;">قيمة المخزون</th>
                  <th style="border: 1px solid #d9d9d9; padding: 12px; text-align: right;">المورد</th>
                </tr>
              </thead>
              <tbody>
                ${materials.map(material => `
                  <tr>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">${material.code}</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">${material.name}</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">${material.category}</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">${material.current_stock} ${material.unit}</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">₪${material.cost.toFixed(2)}</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">₪${(material.current_stock * material.cost).toFixed(2)}</td>
                    <td style="border: 1px solid #d9d9d9; padding: 8px;">${material.supplier_name || '-'}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>

          <div style="margin-top: 40px; text-align: center; color: #666; font-size: 12px;">
            <p>تم إنشاء هذا التقرير بواسطة نّام إدارة الإنتاج</p>
          </div>
        </div>
      `

      const printWindow = window.open('', '_blank')
      if (printWindow) {
        const printDoc = (printWindow as any).document
        if (printDoc) {
          printDoc.write(`
          <html>
            <head>
              <title>تقرير المواد الخام</title>
              <meta charset="utf-8">
              <style>
                body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
                @media print {
                  body { margin: 0; }
                  .no-print { display: none; }
                }
              </style>
            </head>
            <body>
              ${printContent}
            </body>
          </html>
        `)
          printDoc.close()
        }
        printWindow.focus()
        setTimeout(() => {
          printWindow.print()
          printWindow.close()
        }, 250)
      }
    } catch (error) {
      Logger.error('MaterialsManagement', 'خطأ في الطباعة:', error)
      message.error('حدث خطأ أثناء الطباعة')
    }
  }

  const columns = [
    {
      title: 'كود المادة',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: 'اسم المادة',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: 'الفئة',
      dataIndex: 'category',
      key: 'category',
      width: 120,
    },
    {
      title: 'حالة المخزون',
      key: 'inventory_status',
      width: 200,
      render: (record: Material) => {
        const stockStatus = getStockStatus(record)
        const availableStock = (record as any).available_stock || (record.current_stock - ((record as any).reserved_stock || 0))

        return (
          <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
              <span style={{ color: stockStatus.color }}>
                {stockStatus.icon}
              </span>
              <span style={{ fontWeight: 'bold' }}>
                {record.current_stock} {record.unit}
              </span>
            </div>

            {(record as any).reserved_stock > 0 && (
              <div style={{ fontSize: '11px', color: '#fa8c16', marginBottom: '2px' }}>
                محجوز: {(record as any).reserved_stock} {record.unit}
              </div>
            )}

            <div style={{ fontSize: '11px', color: '#52c41a', marginBottom: '4px' }}>
              متاح: {availableStock} {record.unit}
            </div>

            <Progress
              percent={getStockLevel(record)}
              size="small"
              status={record.current_stock <= record.min_stock ? 'exception' : 'normal'}
              showInfo={false}
            />

            {(record as any).warehouse_name && (
              <div style={{ fontSize: '10px', color: '#999', marginTop: '2px' }}>
                {(record as any).warehouse_name}
              </div>
            )}
          </div>
        )
      }
    },
    {
      title: 'الحد الأدنى',
      dataIndex: 'min_stock',
      key: 'min_stock',
      width: 100,
      render: (stock: number, record: Material) => stock + ' ' + record.unit
    },
    {
      title: 'الحد الأقصى',
      dataIndex: 'max_stock',
      key: 'max_stock',
      width: 100,
      render: (stock: number, record: Material) => stock + ' ' + record.unit
    },
    {
      title: 'التكلفة',
      dataIndex: 'cost',
      key: 'cost',
      width: 100,
      render: (cost: number) => '₪' + cost.toFixed(2)
    },
    {
      title: 'قيمة المخزون',
      key: 'stock_value',
      width: 120,
      render: (record: Material) => '₪' + (record.current_stock * record.cost).toFixed(2)
    },
    {
      title: 'المورد',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      width: 150,
      render: (text: string) => text || 'غير محدد'
    },
    {
      title: 'الحالة',
      key: 'status',
      width: 100,
      render: (record: Material) => {
        const stockStatus = getStockStatus(record)
        return (
          <Tag color={stockStatus.color} icon={stockStatus.icon}>
            {stockStatus.status}
          </Tag>
        )
      }
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 120,
      render: (record: Material) => (
        <Space>
          <Tooltip title="تعديل">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="حذف">
            <Popconfirm
              title="هل أنت متأكد من حذف هذه المادة؟"
              description="سيتم حذف المادة نهائياً"
              onConfirm={() => handleDelete(record.id)}
              okText="نعم"
              cancelText="لا"
            >
              <Button
                danger
                size="small"
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ]

  // فلترة المواد حسب حالة المخزون
  const lowStockMaterials = materials.filter(m => m.current_stock <= m.min_stock && m.current_stock > 0)
  const outOfStockMaterials = materials.filter(m => m.current_stock <= 0)

  return (
    <div style={{ padding: '24px' }}>
      {/* تنبيهات المخزون */}
      {outOfStockMaterials.length > 0 && (
        <Alert
          message="تنبيه: مواد نفدت من المخزون"
          description={'يوجد ' + outOfStockMaterials.length + ' مادة نفدت من المخزون'}
          type="error"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}
      
      {lowStockMaterials.length > 0 && (
        <Alert
          message="تحذير: مواد منخفضة المخزون"
          description={'يوجد ' + lowStockMaterials.length + ' مادة تحتاج إلى إعادة تموين'}
          type="warning"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* الإحصائيات السريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي المواد"
              value={stats.total}
              prefix={<InboxOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="مخزون منخفض"
              value={stats.low_stock}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="نفد المخزون"
              value={stats.out_of_stock}
              valueStyle={{ color: '#f5222d' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="قيمة المخزون الإجمالية"
              value={stats.total_value}
              precision={2}
              prefix="₪"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* شريط الأدوات */}
      <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingMaterial(null)
            form.resetFields()
            generateCode() // إنشاء كود تلقائي عند إضافة مادة جديدة
            setModalVisible(true)
          }}
        >
          مادة خام جديدة
        </Button>
        <Space>
          <Button onClick={loadMaterials}>
            تحديث
          </Button>
          <Button
            icon={<FileExcelOutlined />}
            onClick={handleExportExcel}
            style={{
              backgroundColor: '#52c41a',
              borderColor: '#52c41a',
              color: 'white'
            }}
          >
            تصدير Excel
          </Button>
          <Button
            icon={<PrinterOutlined />}
            onClick={handlePrint}
            style={{
              backgroundColor: '#1890ff',
              borderColor: '#1890ff',
              color: 'white'
            }}
          >
            طباعة
          </Button>
          <Button
            icon={<ShoppingCartOutlined />}
            onClick={() => message.info('ميزة طلبات الشراء قيد التطوير')}
          >
            طلب شراء
          </Button>
        </Space>
      </div>

      {/* جدول المواد */}
      <Table
        columns={columns}
        dataSource={materials}
        rowKey="id"
        loading={loading}
        scroll={{ x: 1400 }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => 'إجمالي ' + total + ' مادة'
        }}
        rowClassName={(record) => {
          if (record.current_stock <= 0) return 'row-out-of-stock'
          if (record.current_stock <= record.min_stock) return 'row-low-stock'
          return ''
        }}
      />

      {/* نافذة إضافة/تعديل مادة */}
      <Modal
        title={editingMaterial ? 'تعديل المادة' : 'مادة خام جديدة'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setEditingMaterial(null)
          form.resetFields()
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="كود المادة"
                rules={[{ required: true, message: 'يرجى إدخال كود المادة' }]}
              >
                <Input
                  placeholder="كود المادة"
                  addonAfter={
                    !editingMaterial && (
                      <Button
                        size="small"
                        onClick={generateCode}
                        icon={<BarcodeOutlined />}
                      >
                        إنشاء تلقائي
                      </Button>
                    )
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="اسم المادة"
                rules={[{ required: true, message: 'يرجى إدخال اسم المادة' }]}
              >
                <Input placeholder="اسم المادة" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category"
                label="الفئة"
                initialValue="مواد خام"
              >
                <Select>
                  <Option value="مواد خام">مواد خام</Option>
                  <Option value="خشب">خشب</Option>
                  <Option value="معادن">معادن</Option>
                  <Option value="دهانات">دهانات</Option>
                  <Option value="مواد لاصقة">مواد لاصقة</Option>
                  <Option value="أدوات">أدوات</Option>
                  <Option value="أخرى">أخرى</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="الوحدة"
                rules={[{ required: true, message: 'يرجى إدخال الوحدة' }]}
              >
                <Select>
                  <Option value="قطعة">قطعة</Option>
                  <Option value="متر">متر</Option>
                  <Option value="متر مربع">متر مربع</Option>
                  <Option value="متر مكعب">متر مكعب</Option>
                  <Option value="كيلو">كيلو</Option>
                  <Option value="لتر">لتر</Option>
                  <Option value="طن">طن</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="cost"
                label="التكلفة لكل وحدة"
                rules={[{ required: true, message: 'يرجى إدخال التكلفة' }]}
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  style={{ width: '100%' }}
                  addonAfter="₪"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="current_stock"
                label="المخزون الحالي"
                initialValue={0}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="supplier_id"
                label="المورد"
              >
                <Select
                  placeholder="اختر المورد"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false) as boolean
                  }
                >
                  {suppliers.map(supplier => (
                    <Option key={supplier.id} value={supplier.id}>
                      {supplier.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="min_stock"
                label="الحد الأدنى للمخزون"
                initialValue={10}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_stock"
                label="الحد الأقصى للمخزون"
                initialValue={100}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="location"
                label="الموقع في المخزن"
              >
                <Input placeholder="رقم الرف أو الموقع" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="الوصف"
          >
            <TextArea rows={3} placeholder="وصف المادة..." />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingMaterial ? 'تحديث' : 'إنشاء'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false)
                setEditingMaterial(null)
                form.resetFields()
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      <style>{`
        .row-out-of-stock {
          background-color: #fff2f0 !important;
        }
        .row-low-stock {
          background-color: #fff7e6 !important;
        }
      `}</style>
    </div>
  )
}

export default MaterialsManagement
