import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Space,
  Tag,
  Tooltip,
  Card,
  Row,
  Col,
  Statistic,
  Tabs
} from 'antd'
import { SafeLogger as Logger } from '../../../utils/logger'
// import type { TabsProps } from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  OrderedListOutlined,
  BarcodeOutlined
} from '@ant-design/icons'

const { Option } = Select
const { TextArea } = Input

interface ProductionStage {
  id: number
  code: string
  name: string
  description?: string
  department_id: number
  department_name: string
  sequence: number
  estimated_hours: number
  labor_cost_per_hour: number
  required_skills?: string
  equipment_needed?: string
  is_active: boolean
  created_at: string
}

interface OrderStage {
  id: number
  order_id: number
  order_number: string
  stage_id: number
  stage_name: string
  status: 'pending' | 'in_progress' | 'completed' | 'skipped'
  start_date?: string
  end_date?: string
  estimated_hours: number
  actual_hours: number
  labor_cost: number
  assigned_to?: number
  assigned_to_name?: string
  notes?: string
}

interface ProductionStagesManagementProps {
  onBack?: () => void
}

const ProductionStagesManagement: React.FC<ProductionStagesManagementProps> = ({ onBack: _onBack }) => {
  const [stages, setStages] = useState<ProductionStage[]>([])
  const [orderStages, setOrderStages] = useState<OrderStage[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingStage, setEditingStage] = useState<ProductionStage | null>(null)
  const [form] = Form.useForm()
  const [departments, setDepartments] = useState<any[]>([])
  const [_users, setUsers] = useState<any[]>([])
  const [activeTab, setActiveTab] = useState('1')

  // إحصائيات سريعة
  const [stats, setStats] = useState({
    total_stages: 0,
    active_orders: 0,
    completed_stages: 0,
    avg_completion_time: 0
  })

  useEffect(() => {
    loadStages()
    loadOrderStages()
    loadDepartments()
    loadUsers()
  }, [])

  const loadStages = async () => {
    setLoading(true)
    try {
      Logger.info('ProductionStagesManagement', '🔄 جاري تحميل مراحل الإنتاج...')

      if (!window.electronAPI) {
        Logger.error('ProductionStagesManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('ProductionStagesManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية لمراحل الإنتاج
        const mockStages = [
          {
            id: 1,
            code: 'STAGE001',
            name: 'التحضير والقطع',
            description: 'تحضير المواد الخام وقطع الأخشاب',
            department_id: 1,
            department_name: 'قسم النجارة',
            estimated_duration: 120, // دقيقة
            sequence_order: 1,
            is_active: true,
            created_at: '2024-06-20'
          },
          {
            id: 2,
            code: 'STAGE002',
            name: 'التجميع',
            description: 'تجميع القطع المقطوعة',
            department_id: 2,
            department_name: 'قسم التجميع',
            estimated_duration: 180,
            sequence_order: 2,
            is_active: true,
            created_at: '2024-06-18'
          },
          {
            id: 3,
            code: 'STAGE003',
            name: 'التشطيب والدهان',
            description: 'تشطيب المنتج ودهانه',
            department_id: 3,
            department_name: 'قسم التشطيب',
            estimated_duration: 240,
            sequence_order: 3,
            is_active: true,
            created_at: '2024-06-15'
          }
        ]

        setStages(mockStages as any)
        calculateStats(mockStages as any)
        Logger.info('ProductionStagesManagement', '✅ تم تحميل ' + mockStages.length + ' مرحلة إنتاج وهمية')
      } else {
        const result = await window.electronAPI.getProductionStages()
        if (result.success) {
          setStages(result.data)
          calculateStats(result.data)
          Logger.info('ProductionStagesManagement', '✅ تم تحميل مراحل الإنتاج من قاعدة البيانات')
        } else {
          message.error('فشل في تحميل مراحل الإنتاج')
        }
      }
    } catch (error) {
      message.error('خطأ في تحميل مراحل الإنتاج')
    }
    setLoading(false)
  }

  const loadOrderStages = async () => {
    try {
      Logger.info('ProductionStagesManagement', '🔄 جاري تحميل مراحل الأوامر...')

      if (!window.electronAPI) {
        Logger.error('ProductionStagesManagement', '❌ window.electronAPI غير متوفر')
        Logger.info('ProductionStagesManagement', '⚠️ التطبيق يعمل في وضع المتصفح - استخدام بيانات وهمية')

        // بيانات وهمية لمراحل الأوامر
        const mockOrderStages = [
          {
            id: 1,
            order_id: 1,
            order_number: 'PO001',
            stage_id: 1,
            stage_name: 'التحضير والقطع',
            status: 'completed',
            start_date: '2024-06-20',
            end_date: '2024-06-20',
            assigned_to: 1,
            assigned_to_name: 'أحمد النجار'
          },
          {
            id: 2,
            order_id: 1,
            order_number: 'PO001',
            stage_id: 2,
            stage_name: 'التجميع',
            status: 'in_progress',
            start_date: '2024-06-21',
            end_date: null,
            assigned_to: 2,
            assigned_to_name: 'محمد المجمع'
          }
        ]

        setOrderStages(mockOrderStages as any)
        Logger.info('ProductionStagesManagement', '✅ تم تحميل ' + mockOrderStages.length + ' مرحلة أمر وهمية')
      } else {
        const result = await window.electronAPI.getAllProductionOrderStages()
        if (result.success) {
          setOrderStages(result.data)
          Logger.info('ProductionStagesManagement', '✅ تم تحميل مراحل الأوامر من قاعدة البيانات')
        }
      }
    } catch (error) {
      Logger.error('ProductionStagesManagement', 'خطأ في تحميل مراحل الأوامر:', error)
    }
  }

  const loadDepartments = async () => {
    try {
      const result = await window.electronAPI.getProductionDepartments()
      if (result.success) {
        setDepartments(result.data)
      }
    } catch (error) {
      Logger.error('ProductionStagesManagement', 'خطأ في تحميل الأقسام:', error)
    }
  }

  const loadUsers = async () => {
    try {
      const result = await window.electronAPI.getUsers()
      if (result.success) {
        setUsers(result.data)
      }
    } catch (error) {
      Logger.error('ProductionStagesManagement', 'خطأ في تحميل المستخدمين:', error)
    }
  }

  const calculateStats = (stagesData: ProductionStage[]) => {
    const stats = {
      total_stages: stagesData.length,
      active_orders: orderStages.filter(os => os.status === 'in_progress').length,
      completed_stages: orderStages.filter(os => os.status === 'completed').length,
      avg_completion_time: orderStages.length > 0 ? 
        orderStages.filter(os => os.actual_hours > 0).reduce((sum, os) => sum + os.actual_hours, 0) / 
        orderStages.filter(os => os.actual_hours > 0).length : 0
    }
    setStats(stats)
  }

  const generateCode = async () => {
    try {
      const response = await window.electronAPI.generateProductionStageCode()
      if (response.success && response.data) {
        form.setFieldsValue({ code: response.data.code })
        message.success('تم إنشاء الكود تلقائياً')
      }
    } catch (error) {
      message.error('فشل في إنشاء الكود')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionStagesManagement', '❌ window.electronAPI غير متوفر')
        message.error('التطبيق يعمل في وضع المتصفح - لا يمكن حفّ مراحل الإنتاج')
        return
      }

      // تحويل معرف القسم إلى رقم صحيح
      const stageData = {
        ...values,
        department_id: parseInt(String(values.department_id))
      }

      let result
      if (editingStage) {
        result = await window.electronAPI.updateProductionStage(editingStage.id, stageData)
      } else {
        result = await window.electronAPI.createProductionStage(stageData)
      }

      if (result.success) {
        message.success(editingStage ? 'تم تحديث المرحلة بنجاح' : 'تم إنشاء المرحلة بنجاح')
        setModalVisible(false)
        setEditingStage(null)
        form.resetFields()
        loadStages()
      } else {
        message.error(result.message || 'فشل في حفّ المرحلة')
      }
    } catch (error) {
      message.error('خطأ في حفّ المرحلة')
    }
  }

  const handleEdit = (stage: ProductionStage) => {
    setEditingStage(stage)
    form.setFieldsValue(stage)
    setModalVisible(true)
  }

  const handleStartStage = async (orderStageId: number) => {
    try {
      const result = await window.electronAPI.startProductionStage(orderStageId)
      if (result.success) {
        message.success('تم بدء المرحلة بنجاح')
        loadOrderStages()
      } else {
        message.error(result.message || 'فشل في بدء المرحلة')
      }
    } catch (error) {
      message.error('خطأ في بدء المرحلة')
    }
  }

  const handleCompleteStage = async (orderStageId: number, actualHours: number) => {
    try {
      const result = await window.electronAPI.completeProductionStage({
        id: orderStageId,
        actual_hours: actualHours
      })
      if (result.success) {
        message.success('تم إكمال المرحلة بنجاح')
        loadOrderStages()
      } else {
        message.error(result.message || 'فشل في إكمال المرحلة')
      }
    } catch (error) {
      message.error('خطأ في إكمال المرحلة')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'in_progress': return 'blue'
      case 'completed': return 'green'
      case 'skipped': return 'gray'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتّار'
      case 'in_progress': return 'قيد التنفيذ'
      case 'completed': return 'مكتملة'
      case 'skipped': return 'متجاوزة'
      default: return status
    }
  }

  const stagesColumns = [
    {
      title: 'كود المرحلة',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: 'اسم المرحلة',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: 'القسم',
      dataIndex: 'department_name',
      key: 'department_name',
      width: 150,
    },
    {
      title: 'التسلسل',
      dataIndex: 'sequence',
      key: 'sequence',
      width: 80,
      sorter: (a: ProductionStage, b: ProductionStage) => a.sequence - b.sequence,
    },
    {
      title: 'الساعات المقدرة',
      dataIndex: 'estimated_hours',
      key: 'estimated_hours',
      width: 120,
      render: (hours: number) => hours + ' ساعة'
    },
    {
      title: 'تكلفة العمالة/ساعة',
      dataIndex: 'labor_cost_per_hour',
      key: 'labor_cost_per_hour',
      width: 150,
      render: (cost: number) => '₪' + cost.toFixed(2)
    },
    {
      title: 'المهارات المطلوبة',
      dataIndex: 'required_skills',
      key: 'required_skills',
      width: 200,
      render: (skills: string) => skills || 'غير محدد'
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 120,
      render: (record: ProductionStage) => (
        <Space>
          <Tooltip title="تعديل">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const orderStagesColumns = [
    {
      title: 'رقم الأمر',
      dataIndex: 'order_number',
      key: 'order_number',
      width: 120,
    },
    {
      title: 'المرحلة',
      dataIndex: 'stage_name',
      key: 'stage_name',
      width: 200,
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'المكلف',
      dataIndex: 'assigned_to_name',
      key: 'assigned_to_name',
      width: 150,
      render: (name: string) => name || 'غير محدد'
    },
    {
      title: 'الساعات المقدرة',
      dataIndex: 'estimated_hours',
      key: 'estimated_hours',
      width: 120,
      render: (hours: number) => hours + ' ساعة'
    },
    {
      title: 'الساعات الفعلية',
      dataIndex: 'actual_hours',
      key: 'actual_hours',
      width: 120,
      render: (hours: number) => hours > 0 ? hours + ' ساعة' : '-'
    },
    {
      title: 'التكلفة',
      dataIndex: 'labor_cost',
      key: 'labor_cost',
      width: 100,
      render: (cost: number) => cost > 0 ? '₪' + cost.toFixed(2) : '-'
    },
    {
      title: 'تاريخ البدء',
      dataIndex: 'start_date',
      key: 'start_date',
      width: 120,
      render: (date: string) => date ? new Date(date).toLocaleDateString('ar') : '-'
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 150,
      render: (record: OrderStage) => (
        <Space>
          {record.status === 'pending' && (
            <Tooltip title="بدء المرحلة">
              <Button
                type="primary"
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={() => handleStartStage(record.id)}
              />
            </Tooltip>
          )}
          {record.status === 'in_progress' && (
            <Tooltip title="إكمال المرحلة">
              <Button
                type="primary"
                size="small"
                icon={<CheckCircleOutlined />}
                style={{ backgroundColor: '#52c41a' }}
                onClick={() => {
                  Modal.confirm({
                    title: 'إكمال المرحلة',
                    content: (
                      <div>
                        <p>يرجى إدخال الساعات الفعلية المستغرقة:</p>
                        <InputNumber
                          id="actual-hours"
                          min={0}
                          step={0.5}
                          defaultValue={record.estimated_hours}
                          addonAfter="ساعة"
                          style={{ width: '100%' }}
                        />
                      </div>
                    ),
                    onOk: () => {
                      const actualHours = (document.getElementById('actual-hours') as HTMLInputElement)?.value
                      handleCompleteStage(record.id, parseFloat(actualHours) || record.estimated_hours)
                    }
                  })
                }}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* الإحصائيات السريعة */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي المراحل"
              value={stats.total_stages}
              prefix={<OrderedListOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="أوامر نشطة"
              value={stats.active_orders}
              valueStyle={{ color: '#1890ff' }}
              prefix={<PlayCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="مراحل مكتملة"
              value={stats.completed_stages}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="متوسط وقت الإنجاز"
              value={stats.avg_completion_time}
              precision={1}
              suffix="ساعة"
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: '1',
            label: 'مراحل الإنتاج',
            children: (
              <div>
                {/* شريط الأدوات */}
                <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setEditingStage(null)
                      form.resetFields()
                      generateCode() // إنشاء كود تلقائي عند إضافة مرحلة جديدة
                      setModalVisible(true)
                    }}
                  >
                    مرحلة جديدة
                  </Button>
                  <Button onClick={loadStages}>
                    تحديث
                  </Button>
                </div>

                {/* جدول المراحل */}
                <Table
                  columns={stagesColumns}
                  dataSource={stages}
                  rowKey="id"
                  loading={loading}
                  scroll={{ x: 1200 }}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => 'إجمالي ' + total + ' مرحلة'
                  }}
                />
              </div>
            )
          },
          {
            key: '2',
            label: 'تتبع الأوامر',
            children: (
              <div>
                <div style={{ marginBottom: '16px' }}>
                  <Button onClick={loadOrderStages}>
                    تحديث
                  </Button>
                </div>

                {/* جدول مراحل الأوامر */}
                <Table
                  columns={orderStagesColumns}
                  dataSource={orderStages}
                  rowKey="id"
                  scroll={{ x: 1400 }}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => 'إجمالي ' + total + ' مرحلة أمر'
                  }}
                />
              </div>
            )
          }
        ]}
      />

      {/* نافذة إضافة/تعديل مرحلة */}
      <Modal
        title={editingStage ? 'تعديل مرحلة الإنتاج' : 'مرحلة إنتاج جديدة'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          setEditingStage(null)
          form.resetFields()
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="كود المرحلة"
                rules={[{ required: true, message: 'يرجى إدخال كود المرحلة' }]}
              >
                <Input
                  placeholder="كود المرحلة"
                  addonAfter={
                    !editingStage && (
                      <Button
                        size="small"
                        onClick={generateCode}
                        icon={<BarcodeOutlined />}
                      >
                        إنشاء تلقائي
                      </Button>
                    )
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="اسم المرحلة"
                rules={[{ required: true, message: 'يرجى إدخال اسم المرحلة' }]}
              >
                <Input placeholder="اسم المرحلة" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="department_id"
                label="القسم"
                rules={[{ required: true, message: 'يرجى اختيار القسم' }]}
              >
                <Select placeholder="اختر القسم">
                  {departments.map(dept => (
                    <Option key={dept.id} value={dept.id}>
                      {dept.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="sequence"
                label="رقم التسلسل"
                rules={[{ required: true, message: 'يرجى إدخال رقم التسلسل' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="estimated_hours"
                label="الساعات المقدرة"
              >
                <InputNumber
                  min={0}
                  step={0.5}
                  style={{ width: '100%' }}
                  addonAfter="ساعة"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="labor_cost_per_hour"
                label="تكلفة العمالة/ساعة"
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  style={{ width: '100%' }}
                  addonAfter="₪"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="وصف المرحلة"
          >
            <TextArea rows={3} placeholder="وصف تفصيلي للمرحلة..." />
          </Form.Item>

          <Form.Item
            name="required_skills"
            label="المهارات المطلوبة"
          >
            <Input placeholder="المهارات المطلوبة لهذه المرحلة" />
          </Form.Item>

          <Form.Item
            name="equipment_needed"
            label="المعدات المطلوبة"
          >
            <Input placeholder="المعدات والأدوات المطلوبة" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingStage ? 'تحديث' : 'إنشاء'}
              </Button>
              <Button onClick={() => {
                setModalVisible(false)
                setEditingStage(null)
                form.resetFields()
              }}>
                إلغاء
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ProductionStagesManagement
