/**
 * مكون طباعة صور الأصناف المحسن
 * يستخدم النظام الموحد للطباعة مع خيارات متقدمة
 */

import React, { useState } from 'react'
import { 
  Button, 
  Modal, 
  Form, 
  Select, 
  Switch, 
  InputNumber, 
  Space, 
  Divider,
  App,
  Tooltip
} from 'antd'
import { 
  PrinterOutlined, 
  SettingOutlined, 
  EyeOutlined,
  FileImageOutlined 
} from '@ant-design/icons'
import { MasterPrintService, type PrintData, type EnhancedPrintOptions } from '../../services/MasterPrintService'
import { SafeLogger as Logger } from '../../utils/logger'
import type { ItemImage } from '../../types/global'

interface EnhancedItemImagePrintProps {
  images: ItemImage[]
  itemName: string
  itemCode?: string
  itemId?: number
  buttonText?: string
  size?: 'small' | 'middle' | 'large'
  disabled?: boolean
  onPrintSuccess?: () => void
  onPrintError?: (error: string) => void
}

const EnhancedItemImagePrint: React.FC<EnhancedItemImagePrintProps> = ({
  images,
  itemName,
  itemCode,
  itemId,
  buttonText = 'طباعة الصور',
  size = 'middle',
  disabled = false,
  onPrintSuccess,
  onPrintError
}) => {
  const { message } = App.useApp()
  const [form] = Form.useForm()
  const [settingsVisible, setSettingsVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  
  // إعدادات افتراضية
  const [printOptions, setPrintOptions] = useState<EnhancedPrintOptions>({
    type: 'image',
    subType: 'item',
    pageSize: 'A4',
    orientation: 'portrait',
    showLogo: true,
    showHeader: true,
    showFooter: true,
    fontSize: 12,
    primaryColor: '#1890ff'
  })

  const [imageSettings, setImageSettings] = useState({
    layout: 'grid' as 'single' | 'grid' | 'list',
    imagesPerPage: 4,
    showMetadata: true,
    imageQuality: 'medium' as 'low' | 'medium' | 'high'
  })

  // التحقق من صحة مسار الصورة
  const validateImagePath = (imagePath: string): { isValid: boolean; error?: string } => {
    if (!imagePath || imagePath.trim() === '') {
      return { isValid: false, error: 'مسار الصورة فارغ' }
    }

    // التحقق من base64
    if (imagePath.startsWith('data:image/')) {
      const parts = imagePath.split(',')
      if (parts.length !== 2) {
        return { isValid: false, error: 'تنسيق base64 غير صحيح' }
      }

      const base64Data = parts[1]
      if (!base64Data || base64Data.length < 100) {
        return { isValid: false, error: 'بيانات base64 قصيرة أو فارغة' }
      }

      return { isValid: true }
    }

    // التحقق من URL أو مسار محلي
    try {
      new URL(imagePath)
      return { isValid: true }
    } catch {
      // مسار محلي - نفترض أنه صحيح
      if (imagePath.includes('\\') || imagePath.includes('/')) {
        return { isValid: true }
      }
      return { isValid: false, error: 'مسار الصورة غير صحيح' }
    }
  }

  // تحويل صور الأصناف إلى تنسيق النظام الموحد مع التحقق من صحتها
  const convertImagesToPrintFormat = (): PrintData['images'] => {
    return images.map(image => {
      const pathValidation = validateImagePath(image.image_path)

      if (!pathValidation.isValid) {
        Logger.warn('EnhancedItemImagePrint', `صورة بمسار غير صحيح: ${image.image_name} - ${pathValidation.error}`)
      }

      return {
        id: image.id,
        name: image.image_name,
        path: image.image_path,
        description: image.description || '',
        category: 'صورة صنف',
        size: image.image_size,
        uploadDate: image.created_at,
        notes: image.description || '',
        metadata: {
          itemId: itemId,
          itemName: itemName,
          itemCode: itemCode,
          isPrimary: image.is_primary,
          pathValid: pathValidation.isValid,
          pathError: pathValidation.error
        }
      }
    })
  }

  // إعداد بيانات الطباعة
  const preparePrintData = (): PrintData => {
    const printImages = convertImagesToPrintFormat()
    
    return {
      title: `صور الصنف: ${itemName}`,
      subtitle: itemCode ? `كود الصنف: ${itemCode}` : undefined,
      date: new Date().toLocaleDateString('ar-SA'),
      images: printImages,
      imageSettings: imageSettings,
      notes: `إجمالي الصور: ${images.length}`,
      metadata: {
        itemId: itemId,
        itemName: itemName,
        itemCode: itemCode,
        printType: 'item-images'
      }
    }
  }

  // فحص الصور قبل الطباعة
  const validateImagesForPrint = (): { isValid: boolean; warnings: string[]; errors: string[] } => {
    const warnings: string[] = []
    const errors: string[] = []

    images.forEach((image, index) => {
      const pathValidation = validateImagePath(image.image_path)

      if (!pathValidation.isValid) {
        errors.push(`الصورة ${index + 1} (${image.image_name}): ${pathValidation.error}`)
      }

      // تحذير للصور كبيرة الحجم
      if (image.image_size && image.image_size > 10 * 1024 * 1024) { // 10MB
        warnings.push(`الصورة ${index + 1} (${image.image_name}) كبيرة الحجم (${(image.image_size / 1024 / 1024).toFixed(1)} ميجابايت) - قد تبطئ الطباعة`)
      }

      // تحذير للصور بدون اسم
      if (!image.image_name || image.image_name.trim() === '') {
        warnings.push(`الصورة ${index + 1} بدون اسم`)
      }
    })

    return {
      isValid: errors.length === 0,
      warnings,
      errors
    }
  }

  // طباعة مباشرة
  const handleDirectPrint = async () => {
    if (images.length === 0) {
      message.warning('لا توجد صور للطباعة')
      return
    }

    // فحص الصور قبل الطباعة
    const validation = validateImagesForPrint()

    if (!validation.isValid) {
      message.error(`لا يمكن الطباعة بسبب الأخطاء التالية:\n${validation.errors.join('\n')}`)
      return
    }

    // عرض التحذيرات إن وجدت
    if (validation.warnings.length > 0) {
      message.warning(`تحذيرات:\n${validation.warnings.join('\n')}`)
    }

    setLoading(true)
    try {
      const printService = MasterPrintService.getInstance()
      const printData = preparePrintData()

      await printService.print(printData, {
        ...printOptions,
        onSuccess: () => {
          message.success('تم إرسال الصور للطباعة بنجاح')
          onPrintSuccess?.()
        },
        onError: (error) => {
          message.error(`فشل في الطباعة: ${error}`)
          onPrintError?.(error)
        }
      })

    } catch (error) {
      Logger.error('EnhancedItemImagePrint', 'خطأ في الطباعة:', error)
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
      message.error(`حدث خطأ أثناء الطباعة: ${errorMessage}`)
      onPrintError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // معاينة قبل الطباعة
  const handlePreview = async () => {
    if (images.length === 0) {
      message.warning('لا توجد صور للمعاينة')
      return
    }

    setLoading(true)
    try {
      const printService = MasterPrintService.getInstance()
      const printData = preparePrintData()

      await printService.previewOnly(printData, {
        ...printOptions,
        onSuccess: () => {
          message.success('تم فتح معاينة الطباعة')
        },
        onError: (error) => {
          message.error(`فشل في المعاينة: ${error}`)
        }
      })

    } catch (error) {
      Logger.error('EnhancedItemImagePrint', 'خطأ في المعاينة:', error)
      message.error('حدث خطأ أثناء المعاينة')
    } finally {
      setLoading(false)
    }
  }

  // حفظ الإعدادات
  const handleSaveSettings = () => {
    form.validateFields().then(values => {
      setPrintOptions(prev => ({ ...prev, ...values.printOptions }))
      setImageSettings(prev => ({ ...prev, ...values.imageSettings }))
      setSettingsVisible(false)
      message.success('تم حفظ الإعدادات')
    })
  }

  return (
    <>
      <Space.Compact>
        <Button
          type="primary"
          icon={<PrinterOutlined />}
          size={size}
          loading={loading}
          disabled={disabled || images.length === 0}
          onClick={handleDirectPrint}
        >
          {buttonText}
        </Button>
        
        <Tooltip title="معاينة">
          <Button
            icon={<EyeOutlined />}
            size={size}
            disabled={disabled || images.length === 0}
            onClick={handlePreview}
          />
        </Tooltip>
        
        <Tooltip title="إعدادات الطباعة">
          <Button
            icon={<SettingOutlined />}
            size={size}
            onClick={() => setSettingsVisible(true)}
          />
        </Tooltip>
      </Space.Compact>

      {/* مودال إعدادات الطباعة */}
      <Modal
        title={
          <Space>
            <FileImageOutlined />
            إعدادات طباعة الصور
          </Space>
        }
        open={settingsVisible}
        onCancel={() => setSettingsVisible(false)}
        onOk={handleSaveSettings}
        width={600}
        okText="حفظ الإعدادات"
        cancelText="إلغاء"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            printOptions: printOptions,
            imageSettings: imageSettings
          }}
        >
          <Divider>إعدادات الطباعة العامة</Divider>
          
          <Form.Item name={['printOptions', 'pageSize']} label="حجم الورقة">
            <Select>
              <Select.Option value="A4">A4</Select.Option>
              <Select.Option value="A3">A3</Select.Option>
              <Select.Option value="A5">A5</Select.Option>
              <Select.Option value="Letter">Letter</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name={['printOptions', 'orientation']} label="اتجاه الطباعة">
            <Select>
              <Select.Option value="portrait">عمودي</Select.Option>
              <Select.Option value="landscape">أفقي</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name={['printOptions', 'showLogo']} label="إظهار الشعار" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item name={['printOptions', 'showHeader']} label="إظهار الرأس" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item name={['printOptions', 'showFooter']} label="إظهار التذييل" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Divider>إعدادات الصور</Divider>

          <Form.Item name={['imageSettings', 'layout']} label="تخطيط الصور">
            <Select>
              <Select.Option value="single">صورة واحدة في الصفحة</Select.Option>
              <Select.Option value="grid">شبكة الصور</Select.Option>
              <Select.Option value="list">قائمة مع التفاصيل</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name={['imageSettings', 'imagesPerPage']} label="عدد الصور في الصفحة">
            <InputNumber min={1} max={12} />
          </Form.Item>

          <Form.Item name={['imageSettings', 'showMetadata']} label="إظهار معلومات الصور" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item name={['imageSettings', 'imageQuality']} label="جودة الصور">
            <Select>
              <Select.Option value="low">منخفضة</Select.Option>
              <Select.Option value="medium">متوسطة</Select.Option>
              <Select.Option value="high">عالية</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default EnhancedItemImagePrint
