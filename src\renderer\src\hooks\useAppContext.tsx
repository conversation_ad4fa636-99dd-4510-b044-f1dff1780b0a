import React, { createContext, useContext, ReactNode } from 'react'

// Define the shape of our app context
export interface AppContextType {
  // User management
  currentUser: any | null
  setCurrentUser: (user: any | null) => void
  
  // Theme and UI
  theme: 'light' | 'dark'
  setTheme: (theme: 'light' | 'dark') => void
  
  // Language
  language: 'ar' | 'en'
  setLanguage: (lang: 'ar' | 'en') => void
  
  // Loading states
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  
  // Error handling
  error: string | null
  setError: (error: string | null) => void
  
  // Data refresh
  refreshData: () => void
  
  // Permissions
  permissions: string[]
  hasPermission: (permission: string) => boolean
  
  // Settings
  settings: Record<string, any>
  updateSetting: (key: string, value: any) => void
}

// Create the context with default values
export const AppContext = createContext<AppContextType>({
  currentUser: null,
  setCurrentUser: () => {},
  theme: 'light',
  setTheme: () => {},
  language: 'ar',
  setLanguage: () => {},
  isLoading: false,
  setIsLoading: () => {},
  error: null,
  setError: () => {},
  refreshData: () => {},
  permissions: [],
  hasPermission: () => false,
  settings: {},
  updateSetting: () => {}
})

// Custom hook to use the app context
export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext)
  
  if (!context) {
    throw new Error('useAppContext must be used within an AppContextProvider')
  }
  
  return context
}



// Export the context for provider usage
export default AppContext
