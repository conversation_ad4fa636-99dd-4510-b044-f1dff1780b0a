import React, { useState, useEffect } from 'react'
import { Select, Spin, Alert, Empty } from 'antd'
import { SelectProps } from 'antd/es/select'
import { SafeLogger as Logger } from '../../utils/logger'

const { Option } = Select

interface EnhancedSelectProps extends Omit<SelectProps, 'loading' | 'notFoundContent'> {
  // خصائص البيانات
  dataSource?: any[]
  loadData?: () => Promise<any[]>
  
  // خصائص العرض
  labelKey?: string
  valueKey?: string
  displayFormat?: (item: any) => React.ReactNode
  
  // خصائص التحكم
  autoLoad?: boolean
  refreshOnOpen?: boolean
  
  // خصائص الرسائل
  loadingText?: string
  errorText?: string
  emptyText?: string
  
  // معالجة الأخطاء
  onError?: (error: any) => void
}

/**
 * مكون Select محسن مع معالجة أفضل للتحميل والأخطاء
 */
const EnhancedSelect: React.FC<EnhancedSelectProps> = ({
  dataSource = [],
  loadData,
  labelKey = 'name',
  valueKey = 'id',
  displayFormat,
  autoLoad = true,
  refreshOnOpen = false,
  errorText = 'حدث خطأ في تحميل البيانات',
  emptyText = 'لا توجد بيانات',
  onError,
  children,
  ...selectProps
}) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [data, setData] = useState<any[]>(dataSource)

  // تحميل البيانات
  const handleLoadData = async () => {
    if (!loadData) return

    setLoading(true)
    setError(null)
    
    try {
      const result = await loadData()
      setData(Array.isArray(result) ? result : [])
      Logger.info('EnhancedSelect', '✅ تم تحميل البيانات بنجاح:', result?.length || 0)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ غير متوقع'
      setError(errorMessage)
      Logger.error('EnhancedSelect', '❌ خطأ في تحميل البيانات:', err)
      onError?.(err)
    } finally {
      setLoading(false)
    }
  }

  // تحميل تلقائي عند التحميل
  useEffect(() => {
    if (autoLoad && loadData) {
      handleLoadData()
    }
  }, [autoLoad, loadData])

  // تحديث البيانات عند تغيير dataSource
  useEffect(() => {
    if (dataSource && dataSource.length > 0) {
      setData(dataSource)
    }
  }, [dataSource])

  // معالجة فتح القائمة
  const handleOpenChange = (open: boolean) => {
    if (open && refreshOnOpen && loadData) {
      handleLoadData()
    }
    selectProps.onOpenChange?.(open)
  }

  // تنسيق العنصر للعرض
  const formatItem = (item: any) => {
    if (displayFormat) {
      return displayFormat(item)
    }
    return item[labelKey] || item.toString()
  }

  // محتوى القائمة
  const getNotFoundContent = () => {
    if (loading) {
      return <Spin size="small" />
    }
    if (error) {
      return (
        <Alert
          message={errorText}
          description={error}
          type="error"
          showIcon
        />
      )
    }
    if (data.length === 0) {
      return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={emptyText} />
    }
    return null
  }

  return (
    <Select
      {...selectProps}
      loading={loading}
      notFoundContent={getNotFoundContent()}
      onOpenChange={handleOpenChange}
    >
      {children || data.map((item) => (
        <Option key={item[valueKey]} value={item[valueKey]}>
          {formatItem(item)}
        </Option>
      ))}
    </Select>
  )
}

export default EnhancedSelect
