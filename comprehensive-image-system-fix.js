const fs = require('fs');
const path = require('path');
const initSqlJs = require('sql.js');

async function comprehensiveImageSystemFix() {
  try {
    console.log('🔧 بدء الإصلاح الشامل لنظام الصور...\n');
    
    // مسارات قواعد البيانات
    const dbPaths = [
      {
        name: 'accounting-production-app',
        path: path.join(process.env.APPDATA, 'accounting-production-app', 'accounting.db'),
        primary: true
      },
      {
        name: 'accounting-production-app-final',
        path: path.join(process.env.APPDATA, 'accounting-production-app-final', 'accounting.db'),
        primary: false
      }
    ];
    
    // تهيئة SQL.js
    const SQL = await initSqlJs({
      locateFile: file => path.join(__dirname, 'node_modules', 'sql.js', 'dist', file)
    });
    
    for (const dbInfo of dbPaths) {
      if (!fs.existsSync(dbInfo.path)) {
        console.log(`⚠️ تخطي قاعدة البيانات غير الموجودة: ${dbInfo.name}`);
        continue;
      }
      
      console.log(`\n🔧 إصلاح قاعدة البيانات: ${dbInfo.name}`);
      console.log('='.repeat(60));
      
      const filebuffer = fs.readFileSync(dbInfo.path);
      const db = new SQL.Database(filebuffer);
      
      // 1. إنشاء جداول الصور المفقودة
      console.log('📋 إنشاء جداول الصور...');
      
      // جدول صور أوامر الإنتاج
      try {
        db.run(`
          CREATE TABLE IF NOT EXISTS production_order_images (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            image_name TEXT NOT NULL,
            image_path TEXT NOT NULL,
            file_size INTEGER DEFAULT 0,
            file_type TEXT,
            description TEXT,
            category TEXT DEFAULT 'general' CHECK (category IN ('general', 'material', 'process', 'quality', 'final')),
            is_primary BOOLEAN DEFAULT 0,
            tags TEXT,
            notes TEXT,
            uploaded_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES production_orders (id) ON DELETE CASCADE
          )
        `);
        console.log('   ✅ جدول production_order_images');
      } catch (error) {
        console.log('   ⚠️ جدول production_order_images موجود مسبقاً');
      }
      
      // جدول الصور الموحد
      try {
        db.run(`
          CREATE TABLE IF NOT EXISTS unified_images (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            original_name TEXT NOT NULL,
            path TEXT NOT NULL,
            thumbnail_path TEXT,
            size INTEGER NOT NULL,
            width INTEGER,
            height INTEGER,
            type TEXT NOT NULL,
            category TEXT NOT NULL,
            context_type TEXT NOT NULL,
            context_id INTEGER NOT NULL,
            description TEXT DEFAULT '',
            tags TEXT DEFAULT '[]',
            is_active INTEGER DEFAULT 1,
            is_primary INTEGER DEFAULT 0,
            sort_order INTEGER DEFAULT 0,
            uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            uploaded_by INTEGER,
            metadata TEXT DEFAULT '{}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `);
        console.log('   ✅ جدول unified_images');
      } catch (error) {
        console.log('   ⚠️ جدول unified_images موجود مسبقاً');
      }
      
      // 2. إنشاء الفهارس
      console.log('🔍 إنشاء الفهارس...');
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_production_order_images_order_id ON production_order_images(order_id)',
        'CREATE INDEX IF NOT EXISTS idx_production_order_images_category ON production_order_images(category)',
        'CREATE INDEX IF NOT EXISTS idx_unified_images_context ON unified_images(context_type, context_id)',
        'CREATE INDEX IF NOT EXISTS idx_unified_images_category ON unified_images(category)',
        'CREATE INDEX IF NOT EXISTS idx_unified_images_active ON unified_images(is_active)'
      ];
      
      indexes.forEach(indexSql => {
        try {
          db.run(indexSql);
        } catch (error) {
          // تجاهل أخطاء الفهارس الموجودة
        }
      });
      console.log('   ✅ تم إنشاء الفهارس');
      
      // 3. إنشاء أوامر إنتاج تجريبية (فقط للقاعدة الأساسية)
      if (dbInfo.primary) {
        console.log('📋 إنشاء أوامر إنتاج تجريبية...');
        
        const ordersResult = db.exec('SELECT COUNT(*) as count FROM production_orders');
        const ordersCount = ordersResult[0]?.values[0]?.[0] || 0;
        
        if (ordersCount === 0) {
          const sampleOrders = [
            {
              order_number: 'PROD-2025-001',
              item_id: 1,
              quantity: 5,
              status: 'pending',
              priority: 'high',
              customer_name: 'شركة الأثاث الحديث',
              notes: 'أمر إنتاج تجريبي - طاولات مكتب'
            },
            {
              order_number: 'PROD-2025-002',
              item_id: 2,
              quantity: 10,
              status: 'in_progress',
              priority: 'normal',
              customer_name: 'معرض الأناقة',
              notes: 'أمر إنتاج تجريبي - كراسي مكتب'
            },
            {
              order_number: 'PROD-2025-003',
              item_id: 3,
              quantity: 3,
              status: 'completed',
              priority: 'low',
              customer_name: 'مكتب الاستشارات',
              notes: 'أمر إنتاج تجريبي - خزائن ملفات'
            }
          ];
          
          for (const order of sampleOrders) {
            try {
              db.run(`
                INSERT INTO production_orders (
                  order_number, item_id, quantity, status, priority,
                  order_date, due_date, customer_name, notes,
                  created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                order.order_number,
                order.item_id,
                order.quantity,
                order.status,
                order.priority,
                new Date().toISOString(),
                new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // أسبوع من الآن
                order.customer_name,
                order.notes,
                new Date().toISOString(),
                new Date().toISOString()
              ]);
              console.log(`   ✅ أمر إنتاج: ${order.order_number}`);
            } catch (error) {
              console.log(`   ❌ خطأ في إنشاء أمر ${order.order_number}:`, error.message);
            }
          }
        } else {
          console.log(`   ℹ️ يوجد ${ordersCount} أمر إنتاج مسبقاً`);
        }
        
        // 4. إنشاء صور تجريبية
        console.log('🖼️ إنشاء صور تجريبية...');
        
        const allOrdersResult = db.exec('SELECT id, order_number FROM production_orders LIMIT 5');
        
        if (allOrdersResult[0]?.values) {
          for (const orderRow of allOrdersResult[0].values) {
            const orderId = orderRow[0];
            const orderNumber = orderRow[1];
            
            console.log(`   📸 إنشاء صور لأمر ${orderNumber}...`);
            
            const imageCategories = [
              { name: 'المواد الخام', category: 'material', color: '#FF6B6B', description: 'صورة توضح المواد الخام المستخدمة' },
              { name: 'العملية الإنتاجية', category: 'process', color: '#4ECDC4', description: 'صورة توضح مراحل الإنتاج' },
              { name: 'فحص الجودة', category: 'quality', color: '#96CEB4', description: 'صورة توضح عملية فحص الجودة' },
              { name: 'المنتج النهائي', category: 'final', color: '#FECA57', description: 'صورة توضح المنتج النهائي' }
            ];
            
            for (let i = 0; i < imageCategories.length; i++) {
              const imageInfo = imageCategories[i];
              
              // إنشاء صورة SVG
              const svgContent = `
                <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
                  <defs>
                    <linearGradient id="grad${i}" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:${imageInfo.color};stop-opacity:1" />
                      <stop offset="100%" style="stop-color:${imageInfo.color}80;stop-opacity:1" />
                    </linearGradient>
                  </defs>
                  <rect width="800" height="600" fill="url(#grad${i})"/>
                  <rect x="50" y="50" width="700" height="500" fill="none" stroke="white" stroke-width="3" stroke-dasharray="10,5"/>
                  <text x="400" y="200" text-anchor="middle" fill="white" font-size="48" font-family="Arial, sans-serif" font-weight="bold">${imageInfo.name}</text>
                  <text x="400" y="280" text-anchor="middle" fill="white" font-size="32" font-family="Arial, sans-serif">أمر إنتاج: ${orderNumber}</text>
                  <text x="400" y="350" text-anchor="middle" fill="white" font-size="24" font-family="Arial, sans-serif">${imageInfo.description}</text>
                  <text x="400" y="420" text-anchor="middle" fill="white" font-size="18" font-family="Arial, sans-serif">تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA')}</text>
                  <circle cx="400" cy="480" r="30" fill="white" opacity="0.3"/>
                  <text x="400" y="490" text-anchor="middle" fill="white" font-size="16" font-family="Arial, sans-serif">صورة تجريبية</text>
                </svg>
              `;
              
              const dataUrl = `data:image/svg+xml;base64,${Buffer.from(svgContent).toString('base64')}`;
              
              try {
                // إدراج في جدول production_order_images
                db.run(`
                  INSERT INTO production_order_images (
                    order_id, image_name, image_path, file_size, file_type,
                    description, category, is_primary, tags, notes, uploaded_by
                  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                  orderId,
                  imageInfo.name,
                  dataUrl,
                  svgContent.length,
                  'image/svg+xml',
                  imageInfo.description,
                  imageInfo.category,
                  i === 0 ? 1 : 0, // الصورة الأولى رئيسية
                  `تجريبي,${imageInfo.category},${orderNumber}`,
                  `صورة تجريبية تم إنشاؤها تلقائياً في ${new Date().toISOString()}`,
                  1
                ]);
                
                // إدراج في جدول unified_images
                const unifiedId = `img_${orderId}_${i}_${Date.now()}`;
                db.run(`
                  INSERT INTO unified_images (
                    id, name, original_name, path, size, type, category,
                    context_type, context_id, description, tags, is_primary, uploaded_by
                  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                  unifiedId,
                  imageInfo.name,
                  `${imageInfo.name}_${orderNumber}.svg`,
                  dataUrl,
                  svgContent.length,
                  'image/svg+xml',
                  imageInfo.category,
                  'production_order',
                  orderId,
                  imageInfo.description,
                  JSON.stringify([imageInfo.category, 'تجريبي', orderNumber]),
                  i === 0 ? 1 : 0,
                  1
                ]);
                
                console.log(`     ✅ ${imageInfo.name}`);
              } catch (error) {
                console.log(`     ❌ خطأ في إنشاء صورة ${imageInfo.name}:`, error.message);
              }
            }
          }
        }
      }
      
      // 5. حفظ قاعدة البيانات
      console.log('💾 حفظ التغييرات...');
      const data = db.export();
      fs.writeFileSync(dbInfo.path, data);
      
      // 6. إحصائيات النتائج
      console.log('📊 إحصائيات النتائج:');
      
      try {
        const ordersCount = db.exec('SELECT COUNT(*) FROM production_orders')[0]?.values[0]?.[0] || 0;
        const imagesCount = db.exec('SELECT COUNT(*) FROM production_order_images')[0]?.values[0]?.[0] || 0;
        const unifiedCount = db.exec('SELECT COUNT(*) FROM unified_images')[0]?.values[0]?.[0] || 0;
        
        console.log(`   📋 أوامر الإنتاج: ${ordersCount}`);
        console.log(`   🖼️ صور أوامر الإنتاج: ${imagesCount}`);
        console.log(`   🎯 الصور الموحدة: ${unifiedCount}`);
      } catch (error) {
        console.log('   ❌ خطأ في قراءة الإحصائيات:', error.message);
      }
      
      db.close();
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('✅ تم الإصلاح الشامل لنظام الصور بنجاح!');
    console.log('='.repeat(80));
    
    console.log('\n📝 الخطوات التالية:');
    console.log('   1. إعادة تشغيل التطبيق');
    console.log('   2. فتح قسم أوامر الإنتاج');
    console.log('   3. اختبار عرض الصور في أوامر الإنتاج');
    console.log('   4. اختبار طباعة أمر إنتاج مع الصور');
    console.log('   5. اختبار رفع صور جديدة');
    console.log('   6. اختبار ربط الأجهزة مع الصور');
    
    console.log('\n🧪 اختبارات مقترحة:');
    console.log('   • فتح أمر إنتاج PROD-2025-001 ومشاهدة الصور');
    console.log('   • طباعة أمر إنتاج والتأكد من ظهور الصور');
    console.log('   • رفع صورة جديدة لأمر إنتاج');
    console.log('   • اختبار حذف وتعديل الصور');
    
  } catch (error) {
    console.error('❌ خطأ في الإصلاح الشامل:', error);
  }
}

// تشغيل الإصلاح الشامل
comprehensiveImageSystemFix();
