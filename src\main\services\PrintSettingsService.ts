/**
 * خدمة إدارة إعدادات الطباعة المركزية - ZET.IA
 * إعادة كتابة جذرية مع نظام migration محكم
 */

import { Logger } from '../utils/logger'
import { DatabaseService } from './DatabaseService'

export interface PrintSettingsData {
  [key: string]: any
}

export interface PrintSettingsResult {
  success: boolean
  data?: PrintSettingsData
  message?: string
  error?: string
}

export class PrintSettingsService {
  private static instance: PrintSettingsService
  private db: any
  private isInitialized = false
  private cache: Map<string, any> = new Map()
  private cacheExpiry = 5 * 60 * 1000 // 5 دقائق
  private readonly CURRENT_VERSION = 2 // إصدار قاعدة البيانات الحالي

  private constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
    this.initializeService()
  }

  public static getInstance(): PrintSettingsService {
    if (!PrintSettingsService.instance) {
      PrintSettingsService.instance = new PrintSettingsService()
    }
    return PrintSettingsService.instance
  }

  /**
   * تهيئة الخدمة مع نظام migration محكم
   */
  private initializeService(): void {
    if (this.isInitialized) return

    try {
      // التحقق من إصدار قاعدة البيانات
      const currentVersion = this.getDatabaseVersion()

      if (currentVersion < this.CURRENT_VERSION) {
        Logger.info('PrintSettingsService', `🔄 بدء migration من الإصدار ${currentVersion} إلى ${this.CURRENT_VERSION}`)
        this.performMigration(currentVersion)
      } else {
        // إنشاء الجدول إذا لم يكن موجوداً
        this.createTableIfNotExists()
      }

      // التحقق من سلامة الجدول
      this.validateTableStructure()

      this.isInitialized = true
      Logger.info('PrintSettingsService', '✅ تم تهيئة خدمة إعدادات الطباعة بنجاح')

    } catch (error) {
      Logger.error('PrintSettingsService', '❌ خطأ في تهيئة خدمة إعدادات الطباعة:', error)
      throw error
    }
  }

  /**
   * الحصول على إصدار قاعدة البيانات الحالي
   */
  private getDatabaseVersion(): number {
    try {
      // التحقق من وجود جدول الإصدارات
      const versionTableExists = this.db.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='db_version'
      `).get()

      if (!versionTableExists) {
        // إنشاء جدول الإصدارات
        this.db.exec(`
          CREATE TABLE db_version (
            component TEXT PRIMARY KEY,
            version INTEGER NOT NULL,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `)
        return 0 // إصدار جديد
      }

      // الحصول على إصدار print_settings
      const versionRow = this.db.prepare(`
        SELECT version FROM db_version WHERE component = 'print_settings'
      `).get()

      return versionRow ? versionRow.version : 0
    } catch (error) {
      Logger.warn('PrintSettingsService', 'تحذير في قراءة إصدار قاعدة البيانات:', error)
      return 0
    }
  }

  /**
   * تنفيذ migration حسب الإصدار
   */
  private performMigration(fromVersion: number): void {
    try {
      if (fromVersion === 0) {
        // Migration من الإصدار 0 إلى 1: إنشاء الجدول الأساسي
        this.createTableIfNotExists()
        this.updateDatabaseVersion(1)
      }

      if (fromVersion <= 1) {
        // Migration من الإصدار 1 إلى 2: إضافة الأعمدة المتقدمة
        this.addAdvancedColumns()
        this.updateDatabaseVersion(2)
      }

      Logger.info('PrintSettingsService', `✅ تم إكمال migration إلى الإصدار ${this.CURRENT_VERSION}`)
    } catch (error) {
      Logger.error('PrintSettingsService', '❌ خطأ في migration:', error)
      throw error
    }
  }

  /**
   * إنشاء الجدول الأساسي
   */
  private createTableIfNotExists(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS print_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // إنشاء فهرس أساسي
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_print_settings_key ON print_settings(setting_key)
    `)
  }

  /**
   * إضافة الأعمدة المتقدمة
   */
  private addAdvancedColumns(): void {
    const columns = [
      { name: 'setting_type', sql: 'ALTER TABLE print_settings ADD COLUMN setting_type TEXT DEFAULT \'global\'' },
      { name: 'user_id', sql: 'ALTER TABLE print_settings ADD COLUMN user_id INTEGER' },
      { name: 'document_type', sql: 'ALTER TABLE print_settings ADD COLUMN document_type TEXT' },
      { name: 'is_default', sql: 'ALTER TABLE print_settings ADD COLUMN is_default BOOLEAN DEFAULT 0' }
    ]

    for (const column of columns) {
      try {
        this.db.exec(column.sql)
        Logger.info('PrintSettingsService', `✅ تم إضافة عمود ${column.name}`)
      } catch (e) {
        // العمود موجود بالفعل
      }
    }

    // إنشاء فهارس للأعمدة الجديدة
    try {
      this.db.exec(`
        CREATE INDEX IF NOT EXISTS idx_print_settings_type ON print_settings(setting_type);
        CREATE INDEX IF NOT EXISTS idx_print_settings_user ON print_settings(user_id);
        CREATE INDEX IF NOT EXISTS idx_print_settings_document ON print_settings(document_type);
      `)
    } catch (e) {
      Logger.warn('PrintSettingsService', 'تحذير في إنشاء فهارس متقدمة:', e)
    }
  }

  /**
   * تحديث إصدار قاعدة البيانات
   */
  private updateDatabaseVersion(version: number): void {
    this.db.prepare(`
      INSERT OR REPLACE INTO db_version (component, version, updated_at)
      VALUES ('print_settings', ?, CURRENT_TIMESTAMP)
    `).run(version)
  }

  /**
   * التحقق من سلامة هيكل الجدول
   */
  private validateTableStructure(): void {
    try {
      // التحقق من وجود الجدول
      const tableExists = this.db.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='print_settings'
      `).get()

      if (!tableExists) {
        throw new Error('جدول print_settings غير موجود')
      }

      // التحقق من الأعمدة الأساسية
      const columns = this.db.prepare(`PRAGMA table_info(print_settings)`).all()
      const columnNames = columns.map((col: any) => col.name)

      const requiredColumns = ['id', 'setting_key', 'setting_value']
      for (const required of requiredColumns) {
        if (!columnNames.includes(required)) {
          throw new Error(`العمود المطلوب ${required} غير موجود`)
        }
      }

      Logger.info('PrintSettingsService', '✅ تم التحقق من سلامة هيكل الجدول')
    } catch (error) {
      Logger.error('PrintSettingsService', '❌ خطأ في التحقق من هيكل الجدول:', error)
      throw error
    }
  }

  /**
   * التحقق من وجود الأعمدة المتقدمة
   */
  private checkAdvancedColumns(): boolean {
    try {
      const columns = this.db.prepare(`PRAGMA table_info(print_settings)`).all()
      const columnNames = columns.map((col: any) => col.name)

      return columnNames.includes('setting_type') &&
             columnNames.includes('user_id') &&
             columnNames.includes('document_type')
    } catch (error) {
      Logger.warn('PrintSettingsService', 'تحذير في فحص الأعمدة المتقدمة:', error)
      return false
    }
  }

  /**
   * الحصول على الإعدادات الافتراضية
   */
  private getDefaultSettings(): PrintSettingsData {
    return {
      primaryColor: '#1890ff',
      secondaryColor: '#fff3cd',
      borderColor: '#d9d9d9',
      backgroundColor: '#ffffff',
      textColor: '#000000',
      pageSize: 'A4',
      orientation: 'portrait',
      fontSize: 12,
      fontFamily: 'Arial',
      headerSize: 18,
      lineSpacing: 1.5,
      showHeader: true,
      showFooter: true,
      showLogo: true,
      showSignature: false,
      showTerms: true,
      showQR: false,
      headerText: 'شركة المحاسبة المتقدمة',
      footerText: 'شكراً لتعاملكم معنا',
      logoPosition: 'top-left',
      logoSize: 'medium',
      borderWidth: 1,
      sectionSpacing: 15,
      tableWidth: 100,
      quality: 'normal',
      copies: 1,
      autoSave: true,
      watermark: false,
      watermarkText: 'ZET.IA',
      watermarkOpacity: 0.1,
      margins: {
        top: 20,
        right: 20,
        bottom: 20,
        left: 20
      }
    }
  }

  /**
   * جلب إعدادات الطباعة مع cache ذكي
   */
  public async getSettings(userId?: number, documentType?: string): Promise<PrintSettingsResult> {
    try {
      const cacheKey = `settings_${userId || 'global'}_${documentType || 'all'}`
      
      // التحقق من الـ cache
      if (this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey)
        if (Date.now() - cached.timestamp < this.cacheExpiry) {
          return {
            success: true,
            data: cached.data
          }
        }
      }

      // جلب الإعدادات من قاعدة البيانات
      const stmt = this.db.prepare(`
        SELECT setting_key, setting_value 
        FROM print_settings 
        WHERE (user_id = ? OR user_id IS NULL)
        AND (document_type = ? OR document_type IS NULL)
        ORDER BY user_id DESC, document_type DESC
      `)

      const rows = stmt.all(userId || null, documentType || null) as Array<{
        setting_key: string
        setting_value: string
      }>

      let settings: PrintSettingsData = {}

      if (rows.length === 0) {
        // استخدام الإعدادات الافتراضية
        settings = this.getDefaultSettings()
        Logger.info('PrintSettingsService', 'استخدام الإعدادات الافتراضية')
      } else {
        // تحويل النتائج إلى كائن
        settings = rows.reduce((acc, row) => {
          try {
            // محاولة تحليل JSON أولاً
            acc[row.setting_key] = JSON.parse(row.setting_value)
          } catch {
            // إذا فشل، استخدم القيمة كما هي
            acc[row.setting_key] = row.setting_value
          }
          return acc
        }, {} as PrintSettingsData)

        // دمج مع الإعدادات الافتراضية للقيم المفقودة
        const defaultSettings = this.getDefaultSettings()
        settings = { ...defaultSettings, ...settings }
      }

      // حفظ في الـ cache
      this.cache.set(cacheKey, {
        data: settings,
        timestamp: Date.now()
      })

      return {
        success: true,
        data: settings
      }

    } catch (error) {
      Logger.error('PrintSettingsService', 'خطأ في جلب إعدادات الطباعة:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف',
        data: this.getDefaultSettings() // إرجاع الإعدادات الافتراضية في حالة الخطأ
      }
    }
  }

  /**
   * حفظ إعدادات الطباعة مع تحسينات
   */
  public async saveSettings(
    settings: PrintSettingsData, 
    userId?: number, 
    documentType?: string
  ): Promise<PrintSettingsResult> {
    try {
      // استخدام transaction للأمان
      const transaction = this.db.transaction(() => {
        // التحقق من وجود الأعمدة المتقدمة
        const hasAdvancedColumns = this.checkAdvancedColumns()

        if (hasAdvancedColumns) {
          // استخدام الاستعلام المتقدم
          const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO print_settings
            (setting_key, setting_value, setting_type, user_id, document_type, updated_at)
            VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
          `)

          const settingType = userId ? 'user' : (documentType ? 'document' : 'global')

          for (const [key, value] of Object.entries(settings)) {
            const valueStr = typeof value === 'object' ? JSON.stringify(value) : String(value)
            stmt.run(key, valueStr, settingType, userId || null, documentType || null)
          }
        } else {
          // استخدام الاستعلام الأساسي
          const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO print_settings
            (setting_key, setting_value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
          `)

          for (const [key, value] of Object.entries(settings)) {
            const valueStr = typeof value === 'object' ? JSON.stringify(value) : String(value)
            stmt.run(key, valueStr)
          }
        }
      })

      transaction()

      // مسح الـ cache المتأثر
      this.clearCache(userId, documentType)

      Logger.info('PrintSettingsService', '✅ تم حفظ إعدادات الطباعة بنجاح')
      return {
        success: true,
        message: 'تم حفظ إعدادات الطباعة بنجاح'
      }

    } catch (error) {
      Logger.error('PrintSettingsService', '❌ خطأ في حفظ إعدادات الطباعة:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ في حفظ الإعدادات'
      }
    }
  }

  /**
   * إعادة تعيين الإعدادات للافتراضية
   */
  public async resetSettings(userId?: number, documentType?: string): Promise<PrintSettingsResult> {
    try {
      const stmt = this.db.prepare(`
        DELETE FROM print_settings 
        WHERE (user_id = ? OR (user_id IS NULL AND ? IS NULL))
        AND (document_type = ? OR (document_type IS NULL AND ? IS NULL))
      `)

      stmt.run(userId || null, userId, documentType || null, documentType)

      // مسح الـ cache
      this.clearCache(userId, documentType)

      Logger.info('PrintSettingsService', '✅ تم إعادة تعيين إعدادات الطباعة')
      return {
        success: true,
        message: 'تم إعادة تعيين إعدادات الطباعة للافتراضية'
      }

    } catch (error) {
      Logger.error('PrintSettingsService', '❌ خطأ في إعادة تعيين الإعدادات:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ في إعادة التعيين'
      }
    }
  }

  /**
   * مسح الـ cache
   */
  private clearCache(userId?: number, documentType?: string): void {
    if (userId || documentType) {
      // مسح cache محدد
      const cacheKey = `settings_${userId || 'global'}_${documentType || 'all'}`
      this.cache.delete(cacheKey)
    } else {
      // مسح جميع الـ cache
      this.cache.clear()
    }
  }

  /**
   * الحصول على إحصائيات الإعدادات
   */
  public getStats(): { totalSettings: number, cacheSize: number, isInitialized: boolean } {
    try {
      const stmt = this.db.prepare('SELECT COUNT(*) as count FROM print_settings')
      const result = stmt.get() as { count: number }

      return {
        totalSettings: result.count,
        cacheSize: this.cache.size,
        isInitialized: this.isInitialized
      }
    } catch (error) {
      Logger.error('PrintSettingsService', 'خطأ في جلب الإحصائيات:', error)
      return {
        totalSettings: 0,
        cacheSize: this.cache.size,
        isInitialized: this.isInitialized
      }
    }
  }
}
