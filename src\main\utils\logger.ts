// نظام تسجيل موحد لـ main process
import * as fs from 'fs'
import * as path from 'path'
import { LogLevel, LoggerConfig, LoggerConfigManager } from './loggerConfig'

interface LogEntry {
  timestamp: Date
  level: LogLevel
  module: string
  message: string
  data?: any
  error?: Error
}

class MainLogger {
  private static isDevelopment = process.env.NODE_ENV === 'development'
  private static isProduction = process.env.NODE_ENV === 'production'
  private static config: LoggerConfig = LoggerConfigManager.loadConfig()
  private static logHistory: LogEntry[] = []
  private static maxHistorySize = 1000
  private static currentLogFile: string | null = null

  // تحديد مستوى التسجيل
  static setLogLevel(level: LogLevel) {
    this.config.level = level
    LoggerConfigManager.updateLogLevel(level)
  }

  // إعادة تحميل الإعدادات
  static reloadConfig() {
    this.config = LoggerConfigManager.loadConfig()
  }

  // الحصول على الإعدادات الحالية
  static getConfig(): LoggerConfig {
    return { ...this.config }
  }

  // تنسيق الرسالة مع معلومات إضافية
  private static formatMessage(level: string, module: string, message: string): string {
    const timestamp = new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
    return `[${timestamp}] ${level} [${module}] ${message}`
  }

  // حفظ السجل في التاريخ
  private static addToHistory(entry: LogEntry) {
    this.logHistory.push(entry)
    if (this.logHistory.length > this.maxHistorySize) {
      this.logHistory.shift()
    }
  }

  // تسجيل آمن للبيانات الحساسة
  private static sanitizeData(data: any): any {
    if (!data) return data
    
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth', 'credential']
    
    if (typeof data === 'object') {
      const sanitized = { ...data }
      for (const key in sanitized) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
          sanitized[key] = '[REDACTED]'
        }
      }
      return sanitized
    }
    
    return data
  }

  // تسجيل موحد
  private static log(level: LogLevel, module: string, message: string, data?: any, error?: any) {
    if (this.config.level <= level) {
      const sanitizedData = this.config.enableSensitiveDataSanitization ? this.sanitizeData(data) : data
      const entry: LogEntry = {
        timestamp: new Date(),
        level,
        module,
        message,
        data: sanitizedData,
        error: error instanceof Error ? error : undefined
      }

      // تسجيل في الكونسول
      if (this.config.enableConsoleLogging) {
        this.logToConsole(entry, error)
      }

      // تسجيل في الملف
      if (this.config.enableFileLogging) {
        this.logToFile(entry)
      }

      // إضافة للتاريخ
      this.addToHistory(entry)

      // إرسال الأخطاء للمراقبة
      if (level === LogLevel.ERROR && this.config.enableErrorMonitoring) {
        this.sendErrorToMonitoring(entry)
      }
    }
  }

  // تسجيل في الكونسول
  private static logToConsole(entry: LogEntry, error?: any) {
    const levelIcons: Record<LogLevel, string> = {
      [LogLevel.DEBUG]: '🔍 DEBUG',
      [LogLevel.INFO]: 'ℹ️ INFO',
      [LogLevel.WARN]: '⚠️ WARN',
      [LogLevel.ERROR]: '❌ ERROR',
      [LogLevel.NONE]: '🚫 NONE'
    }

    const formattedMessage = this.formatMessage(levelIcons[entry.level], entry.module, entry.message)

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(formattedMessage, entry.data || '')
        break
      case LogLevel.INFO:
        console.log(formattedMessage, entry.data || '')
        break
      case LogLevel.WARN:
        console.warn(formattedMessage, entry.data || '')
        break
      case LogLevel.ERROR:
        console.error(formattedMessage, error || '', entry.data || '')
        break
    }
  }

  // تسجيل في الملف
  private static logToFile(entry: LogEntry) {
    try {
      // إنشاء مجلد logs إذا لم يكن موجوداً
      if (!fs.existsSync(this.config.logDirectory)) {
        fs.mkdirSync(this.config.logDirectory, { recursive: true })
      }

      // تحديد اسم الملف
      const today = new Date().toISOString().split('T')[0]
      const logFileName = `main-${today}.log`
      const logFilePath = path.join(this.config.logDirectory, logFileName)

      // تنسيق السجل للملف
      const logLine = JSON.stringify({
        timestamp: entry.timestamp.toISOString(),
        level: LoggerConfigManager.getLogLevelName(entry.level),
        module: entry.module,
        message: entry.message,
        data: entry.data,
        error: entry.error ? {
          message: entry.error.message,
          stack: entry.error.stack
        } : undefined
      }) + '\n'

      // كتابة في الملف
      fs.appendFileSync(logFilePath, logLine)

      // فحص حجم الملف وتدوير السجلات إذا لزم الأمر
      this.rotateLogsIfNeeded(logFilePath)

    } catch (error) {
      // في حالة فشل كتابة الملف، نسجل في الكونسول فقط
      console.error('فشل في كتابة السجل في الملف:', error)
    }
  }

  static debug(module: string, message: string, data?: any) {
    this.log(LogLevel.DEBUG, module, message, data)
  }

  static info(module: string, message: string, data?: any) {
    this.log(LogLevel.INFO, module, message, data)
  }

  static warn(module: string, message: string, data?: any) {
    this.log(LogLevel.WARN, module, message, data)
  }

  static error(module: string, message: string, error?: any, data?: any) {
    this.log(LogLevel.ERROR, module, message, data, error)
  }

  static success(module: string, message: string, data?: any) {
    this.log(LogLevel.INFO, module, `✅ ${message}`, data)
  }

  static performance(module: string, message: string, startTime?: number) {
    if (this.config.enablePerformanceLogging) {
      let perfMessage = message
      if (startTime) {
        const duration = Date.now() - startTime
        perfMessage = `${message} (${duration}ms)`
      }

      this.log(LogLevel.DEBUG, module, `⚡ ${perfMessage}`)
    }
  }

  // تدوير السجلات
  private static rotateLogsIfNeeded(logFilePath: string) {
    try {
      const stats = fs.statSync(logFilePath)
      const fileSizeMB = stats.size / (1024 * 1024)

      if (fileSizeMB > this.config.maxLogFileSize) {
        // إنشاء نسخة مؤرشفة
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
        const archivePath = logFilePath.replace('.log', `-${timestamp}.log`)

        fs.renameSync(logFilePath, archivePath)

        // حذف الملفات القديمة إذا تجاوزت الحد الأقصى
        this.cleanupOldLogFiles()
      }
    } catch {
      // تجاهل أخطاء تدوير السجلات
    }
  }

  // تنظيف الملفات القديمة
  private static cleanupOldLogFiles() {
    try {
      const files = fs.readdirSync(this.config.logDirectory)
      const logFiles = files
        .filter(file => file.startsWith('main-') && file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(this.config.logDirectory, file),
          stats: fs.statSync(path.join(this.config.logDirectory, file))
        }))
        .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime())

      // حذف الملفات الزائدة
      if (logFiles.length > this.config.maxLogFiles) {
        const filesToDelete = logFiles.slice(this.config.maxLogFiles)
        filesToDelete.forEach(file => {
          fs.unlinkSync(file.path)
        })
      }
    } catch {
      // تجاهل أخطاء التنظيف
    }
  }

  // الحصول على سجل الأخطاء
  static getErrorHistory(): LogEntry[] {
    return this.logHistory.filter(entry => entry.level === LogLevel.ERROR)
  }

  // الحصول على السجل الكامل
  static getFullHistory(): LogEntry[] {
    return [...this.logHistory]
  }

  // مسح السجل
  static clearHistory() {
    this.logHistory = []
  }

  // إرسال الأخطاء لخدمة المراقبة (للإنتاج)
  private static sendErrorToMonitoring(_entry: LogEntry) {
    // TODO: تطبيق إرسال الأخطاء لخدمة مراقبة خارجية
    // مثل Sentry أو LogRocket
  }

  // تصدير السجل لملف
  static exportLogs(filePath?: string): string {
    const exportPath = filePath || `logs/main-logs-${new Date().toISOString().split('T')[0]}.json`
    
    try {
      const logsData = {
        exportDate: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        totalEntries: this.logHistory.length,
        logs: this.logHistory
      }
      
      // إنشاء مجلد logs إذا لم يكن موجوداً
      const logsDir = path.dirname(exportPath)
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true })
      }
      
      fs.writeFileSync(exportPath, JSON.stringify(logsData, null, 2))
      this.info('MainLogger', `تم تصدير السجل إلى: ${exportPath}`)
      return exportPath
    } catch (error) {
      this.error('MainLogger', 'فشل في تصدير السجل', error)
      return ''
    }
  }
}

// تصدير Logger كـ default export
export default MainLogger

// تصدير أيضاً كـ named export للتوافق
export { MainLogger as Logger }

// تصدير دوال مساعدة للاستخدام السريع
export const logDebug = (module: string, message: string, data?: any) => 
  MainLogger.debug(module, message, data)

export const logInfo = (module: string, message: string, data?: any) => 
  MainLogger.info(module, message, data)

export const logWarn = (module: string, message: string, data?: any) => 
  MainLogger.warn(module, message, data)

export const logError = (module: string, message: string, error?: Error, data?: any) => 
  MainLogger.error(module, message, error, data)

export const logSuccess = (module: string, message: string, data?: any) => 
  MainLogger.success(module, message, data)

export const logPerformance = (module: string, message: string, startTime?: number) => 
  MainLogger.performance(module, message, startTime)
