import React, { useState } from 'react'
import {
  Mo<PERSON>,
  Steps,
  Card,
  Row,
  Col,
  Typo<PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Al<PERSON>,
  Collapse,
  Table,
  Tag,
  Divider
} from 'antd'
import {
  SafetyOutlined,
  WifiOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ToolOutlined,
  QuestionCircleOutlined,
  BookOutlined
} from '@ant-design/icons'

const { Title, Text, Paragraph } = Typography
const { Step } = Steps

interface FingerprintSetupGuideProps {
  visible: boolean
  onClose: () => void
}

const FingerprintSetupGuide: React.FC<FingerprintSetupGuideProps> = ({
  visible,
  onClose
}) => {
  const [currentStep, setCurrentStep] = useState(0)

  const supportedDevices = [
    {
      brand: 'ZKTeco',
      model: 'F18',
      capacity: '1,000',
      port: '4370',
      notes: 'مناسب للمكاتب الصغيرة'
    },
    {
      brand: 'ZKTeco',
      model: 'K40',
      capacity: '3,000',
      port: '4370',
      notes: 'مناسب للشركات المتوسطة'
    },
    {
      brand: 'ZKTeco',
      model: 'K50',
      capacity: '5,000',
      port: '4370',
      notes: 'مناسب للشركات الكبيرة'
    },
    {
      brand: 'Hikvision',
      model: 'DS-K1T201',
      capacity: '1,000',
      port: '8000',
      notes: 'دعم RFID'
    },
    {
      brand: 'Hikvision',
      model: 'DS-K1T321',
      capacity: '3,000',
      port: '8000',
      notes: 'شاشة LCD'
    }
  ]

  const deviceColumns = [
    {
      title: 'الماركة',
      dataIndex: 'brand',
      key: 'brand',
      render: (text: string) => <Tag color="blue">{text}</Tag>
    },
    {
      title: 'الموديل',
      dataIndex: 'model',
      key: 'model',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: 'السعة',
      dataIndex: 'capacity',
      key: 'capacity',
      render: (text: string) => `${text} مستخدم`
    },
    {
      title: 'المنفذ الافتراضي',
      dataIndex: 'port',
      key: 'port'
    },
    {
      title: 'ملاحّات',
      dataIndex: 'notes',
      key: 'notes'
    }
  ]

  const troubleshootingData = [
    {
      problem: 'لا يمكن الوصول للجهاز',
      symptoms: 'فشل في اختبار الاتصال',
      solutions: [
        'تحقق من اتصال الكابل',
        'تأكد من وصول الكهرباء',
        'فحص عنوان IP',
        'إعادة تشغيل الجهاز'
      ]
    },
    {
      problem: 'الاتصال بطيء أو متقطع',
      symptoms: 'استجابة بطيئة، انقطاع متكرر',
      solutions: [
        'استخدم كابل Cat5e أو أفضل',
        'قرب الجهاز من الراوتر',
        'تحديث firmware الجهاز',
        'تغيير منفذ الراوتر'
      ]
    },
    {
      problem: 'فشل في المزامنة',
      symptoms: 'الاتصال يعمل لكن البيانات لا تنتقل',
      solutions: [
        'تحقق من صلاحيات الوصول',
        'راجع إعدادات الجهاز',
        'مسح البيانات القديمة',
        'إعادة تكوين الجهاز'
      ]
    }
  ]

  const steps = [
    {
      title: 'التحضير',
      icon: <ToolOutlined />,
      content: (
        <div>
          <Title level={4}>الخطوة 1: التحضير والمتطلبات</Title>
          
          <Alert
            message="متطلبات أساسية"
            description="تأكد من توفر هذه المتطلبات قبل البدء"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Card title="متطلبات الشبكة" size="small">
                <ul style={{ margin: 0, paddingLeft: 20 }}>
                  <li>الجهاز والحاسوب على نفس الشبكة</li>
                  <li>عنوان IP ثابت للجهاز (مفضل)</li>
                  <li>المنفذ 4370 مفتوح (أو حسب الجهاز)</li>
                  <li>اتصال إنترنت مستقر</li>
                </ul>
              </Card>
            </Col>
            <Col xs={24} md={12}>
              <Card title="متطلبات الطاقة" size="small">
                <ul style={{ margin: 0, paddingLeft: 20 }}>
                  <li>تيار مستمر 12V</li>
                  <li>استهلاك أقل من 5 واط</li>
                  <li>UPS للحماية من انقطاع الكهرباء</li>
                  <li>مأخذ كهرباء قريب من الجهاز</li>
                </ul>
              </Card>
            </Col>
          </Row>

          <Divider />

          <Title level={5}>الأجهزة المدعومة</Title>
          <Table
            dataSource={supportedDevices}
            columns={deviceColumns}
            pagination={false}
            size="small"
            rowKey={(record) => `${record.brand}-${record.model}`}
          />
        </div>
      )
    },
    {
      title: 'إعداد الشبكة',
      icon: <WifiOutlined />,
      content: (
        <div>
          <Title level={4}>الخطوة 2: إعداد الشبكة</Title>
          
          <Collapse
            defaultActiveKey={['1']}
            items={[
              {
                key: '1',
                label: 'تحديد عنوان IP للجهاز',
                children: (
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text strong>الطريقة الأولى: من إعدادات الجهاز</Text>
                <ol>
                  <li>اضغط على <Text code>Menu</Text> في الجهاز</li>
                  <li>اختر <Text code>System</Text> أو <Text code>النّام</Text></li>
                  <li>اختر <Text code>Network</Text> أو <Text code>الشبكة</Text></li>
                  <li>سجل عنوان IP المعروض</li>
                </ol>

                <Text strong>الطريقة الثانية: استخدام برنامج البحث</Text>
                <ol>
                  <li>حمل برنامج <Text code>Device Network Search</Text> من الشركة المصنعة</li>
                  <li>شغل البرنامج على نفس الشبكة</li>
                  <li>سيّهر جميع الأجهزة المتصلة مع عناوين IP</li>
                </ol>

                <Text strong>الطريقة الثالثة: فحص الشبكة</Text>
                <Text code style={{ display: 'block', padding: 8, backgroundColor: '#f5f5f5' }}>
                  ping *************<br/>
                  ping *************<br/>
                  # جرب عناوين مختلفة في نطاق شبكتك
                </Text>
              </Space>
            )
          },
          {
            key: '2',
            label: 'تعيين IP ثابت (مفضل)',
            children: (
                  <ol>
                    <li>ادخل إعدادات الجهاز</li>
                    <li>اختر <Text code>Network Settings</Text></li>
                    <li>غير من <Text code>DHCP</Text> إلى <Text code>Static IP</Text></li>
                    <li>أدخل البيانات:
                      <ul>
                        <li><Text strong>IP Address:</Text> مثل *************</li>
                        <li><Text strong>Subnet Mask:</Text> عادة *************</li>
                        <li><Text strong>Gateway:</Text> عنوان الراوتر (مثل ***********)</li>
                        <li><Text strong>DNS:</Text> ******* أو عنوان DNS المحلي</li>
                      </ul>
                    </li>
                  </ol>
                )
              }
            ]}
          />
        </div>
      )
    },
    {
      title: 'إضافة الجهاز',
      icon: <SettingOutlined />,
      content: (
        <div>
          <Title level={4}>الخطوة 3: إضافة الجهاز في النّام</Title>
          
          <Alert
            message="نصيحة مهمة"
            description="تأكد من أن الجهاز يعمل ومتصل بالشبكة قبل إضافته في النّام"
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>خطوات الإضافة:</Text>
            <ol>
              <li>افتح <Text code>إدارة الموظفين</Text> ← <Text code>أجهزة البصمة</Text></li>
              <li>اضغط <Button type="primary" size="small">إضافة جهاز</Button></li>
              <li>املأ البيانات المطلوبة</li>
            </ol>

            <Divider />

            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Card title="البيانات الأساسية" size="small">
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    <li><Text strong>اسم الجهاز:</Text> مثل &quot;جهاز البصمة الرئيسي&quot;</li>
                    <li><Text strong>موديل الجهاز:</Text> مثل &quot;ZKTeco F18&quot;</li>
                    <li><Text strong>معرف الجهاز:</Text> رقم فريد (1, 2, 3...)</li>
                    <li><Text strong>الموقع:</Text> مثل &quot;المدخل الرئيسي&quot;</li>
                  </ul>
                </Card>
              </Col>
              <Col xs={24} md={12}>
                <Card title="إعدادات الشبكة" size="small">
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    <li><Text strong>عنوان IP:</Text> العنوان الذي حصلت عليه</li>
                    <li><Text strong>المنفذ:</Text> 4370 (أو حسب الجهاز)</li>
                    <li><Text strong>السعة القصوى:</Text> حسب موديل الجهاز</li>
                    <li><Text strong>الحالة:</Text> نشط</li>
                  </ul>
                </Card>
              </Col>
            </Row>

            <Alert
              message="استخدم الإعداد السريع"
              description="يمكنك استخدام أزرار الإعداد السريع لملء البيانات تلقائياً حسب موديل الجهاز"
              type="info"
              showIcon
            />
          </Space>
        </div>
      )
    },
    {
      title: 'اختبار الاتصال',
      icon: <CheckCircleOutlined />,
      content: (
        <div>
          <Title level={4}>الخطوة 4: اختبار الاتصال والتأكد من العمل</Title>
          
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>خطوات الاختبار:</Text>
            <ol>
              <li>بعد حفظ بيانات الجهاز، اضغط <Button size="small" icon={<WifiOutlined />}>اختبار الاتصال</Button></li>
              <li>أنتّر النتيجة:
                <ul>
                  <li>✅ <Text type="success">نجح:</Text> الجهاز متصل ويعمل</li>
                  <li>❌ <Text type="danger">فشل:</Text> راجع خطوات حل المشاكل</li>
                </ul>
              </li>
              <li>إذا نجح الاختبار، اضغط <Button size="small" icon={<SafetyOutlined />}>مزامنة</Button> لتحديث البيانات</li>
            </ol>

            <Divider />

            <Title level={5}>اختبار الوّائف</Title>
            <Collapse
              items={[
                {
                  key: '1',
                  label: 'اختبار تسجيل البصمة',
                  children: (
                    <ol>
                      <li>اذهب إلى الجهاز مباشرة</li>
                      <li>اختر <Text code>تسجيل مستخدم جديد</Text></li>
                      <li>أدخل رقم الموظف</li>
                      <li>ضع الإصبع على الحساس عدة مرات</li>
                      <li>تأكد من حفظ البصمة بنجاح</li>
                    </ol>
                  )
                },
                {
                  key: '2',
                  label: 'اختبار قراءة البصمة',
                  children: (
                    <ol>
                      <li>ضع إصبع موظف مسجل على الحساس</li>
                      <li>تأكد من ّهور اسم الموظف أو رقمه</li>
                      <li>تحقق من تسجيل الوقت بشكل صحيح</li>
                    </ol>
                  )
                },
                {
                  key: '3',
                  label: 'اختبار المزامنة',
                  children: (
                    <ol>
                      <li>سجل حضور أو انصراف على الجهاز</li>
                      <li>ارجع للنّام واضغط <Text code>مزامنة</Text></li>
                      <li>تحقق من ّهور السجل في قائمة الحضور</li>
                    </ol>
                  )
                }
              ]}
            />
          </Space>
        </div>
      )
    }
  ]

  return (
    <Modal
      title={
        <Space>
          <BookOutlined />
          <span>دليل ربط أجهزة البصمة</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={
        <Space>
          <Button onClick={onClose}>إغلاق</Button>
          {currentStep > 0 && (
            <Button onClick={() => setCurrentStep(currentStep - 1)}>
              السابق
            </Button>
          )}
          {currentStep < steps.length - 1 && (
            <Button type="primary" onClick={() => setCurrentStep(currentStep + 1)}>
              التالي
            </Button>
          )}
        </Space>
      }
    >
      <Steps current={currentStep} style={{ marginBottom: 24 }}>
        {steps.map((step, index) => (
          <Step
            key={index}
            title={step.title}
            icon={step.icon}
            onClick={() => setCurrentStep(index)}
            style={{ cursor: 'pointer' }}
          />
        ))}
      </Steps>

      <div style={{ minHeight: 400 }}>
        {steps[currentStep].content}
      </div>

      {currentStep === steps.length - 1 && (
        <div style={{ marginTop: 24 }}>
          <Title level={5}>حل المشاكل الشائعة</Title>
          <Collapse
            items={troubleshootingData.map((item, index) => ({
              key: index,
              label: (
                <Space>
                  <QuestionCircleOutlined />
                  <Text strong>{item.problem}</Text>
                </Space>
              ),
              children: (
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text><Text strong>الأعراض:</Text> {item.symptoms}</Text>
                  <Text strong>الحلول المقترحة:</Text>
                  <ul>
                    {item.solutions.map((solution, idx) => (
                      <li key={idx}>{solution}</li>
                    ))}
                  </ul>
                </Space>
              )
            }))}
          />
        </div>
      )}
    </Modal>
  )
}

export default FingerprintSetupGuide
