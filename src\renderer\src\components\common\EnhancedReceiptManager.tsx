import React, { useState } from 'react'
import {
  Card, Form, Input, InputNumber, DatePicker, Button, Space, message,
  Row, Col, Select, Typography, Divider, Modal
} from 'antd'
import {
  FileTextOutlined, SaveOutlined, PrinterOutlined, UserOutlined,
  DollarOutlined, CalendarOutlined
} from '@ant-design/icons'
import ReceiptPrintButton from './ReceiptPrintButton'
import { SafeLogger as Logger } from '../../utils/logger'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input

interface ReceiptData {
  id?: string | number
  receiptNumber: string
  date: string
  paidBy: string
  amount: number
  currency: string
  description: string
  paymentMethod: 'cash' | 'bank' | 'check' | 'card'
  reference?: string
  notes?: string
  type?: 'receipt' | 'payment'
  customerName?: string
  customerPhone?: string
  referenceNumber?: string
  bankName?: string
  checkNumber?: string
}

interface EnhancedReceiptManagerProps {
  visible: boolean
  onClose: () => void
  onSave?: (receipt: ReceiptData) => void
  initialData?: Partial<ReceiptData>
}

const EnhancedReceiptManager: React.FC<EnhancedReceiptManagerProps> = ({
  visible,
  onClose,
  onSave,
  initialData
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [receiptData, setReceiptData] = useState<ReceiptData | null>(null)

  // إنشاء رقم إيصال تلقائي
  const generateReceiptNumber = (): string => {
    const date = dayjs().format('YYYYMMDD')
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `REC-${date}-${random}`
  }

  // حفظ الإيصال
  const handleSave = async (values: any) => {
    try {
      setLoading(true)

      const receipt: ReceiptData = {
        receiptNumber: values.receiptNumber || generateReceiptNumber(),
        date: values.date ? values.date.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        paidBy: values.paidBy,
        amount: values.amount,
        currency: values.currency || 'ش.ج',
        description: values.description,
        paymentMethod: values.paymentMethod,
        reference: values.reference,
        notes: values.notes
      }

      setReceiptData(receipt)

      if (onSave) {
        await onSave(receipt)
      }

      message.success('تم حفظ الإيصال بنجاح')
      Logger.info('EnhancedReceiptManager', 'تم حفظ إيصال جديد:', receipt)

    } catch (error) {
      Logger.error('EnhancedReceiptManager', 'خطأ في حفظ الإيصال:', error)
      message.error('حدث خطأ أثناء حفظ الإيصال')
    } finally {
      setLoading(false)
    }
  }

  // حفظ وطباعة
  const handleSaveAndPrint = async (values: any) => {
    await handleSave(values)
    // الطباعة ستتم تلقائياً عبر زر الطباعة
  }

  // إعادة تعيين النموذج
  const handleReset = () => {
    form.resetFields()
    setReceiptData(null)
  }

  return (
    <Modal
      title={
        <Space>
          <FileTextOutlined />
          إدارة الإيصالات المحسنة
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={null}
    >
      <Card>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            receiptNumber: generateReceiptNumber(),
            date: dayjs(),
            currency: 'ش.ج',
            paymentMethod: 'cash',
            ...initialData
          }}
          onFinish={handleSave}
        >
          <Row gutter={[16, 16]}>
            {/* معلومات الإيصال الأساسية */}
            <Col xs={24}>
              <Title level={4}>
                <FileTextOutlined /> معلومات الإيصال
              </Title>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                label="رقم الإيصال"
                name="receiptNumber"
                rules={[{ required: true, message: 'يرجى إدخال رقم الإيصال' }]}
              >
                <Input
                  prefix={<FileTextOutlined />}
                  placeholder="رقم الإيصال"
                />
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                label="التاريخ"
                name="date"
                rules={[{ required: true, message: 'يرجى اختيار التاريخ' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="اختر التاريخ"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>

            {/* معلومات الدافع */}
            <Col xs={24}>
              <Divider />
              <Title level={4}>
                <UserOutlined /> معلومات الدافع
              </Title>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                label="المستلم من"
                name="paidBy"
                rules={[{ required: true, message: 'يرجى إدخال اسم الدافع' }]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="اسم الشخص أو الجهة"
                />
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                label="طريقة الدفع"
                name="paymentMethod"
                rules={[{ required: true, message: 'يرجى اختيار طريقة الدفع' }]}
              >
                <Select placeholder="اختر طريقة الدفع">
                  <Select.Option value="cash">نقداً</Select.Option>
                  <Select.Option value="check">شيك</Select.Option>
                  <Select.Option value="transfer">تحويل بنكي</Select.Option>
                  <Select.Option value="card">بطاقة ائتمان</Select.Option>
                  <Select.Option value="other">أخرى</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            {/* المبلغ والعملة */}
            <Col xs={24}>
              <Divider />
              <Title level={4}>
                <DollarOutlined /> المبلغ
              </Title>
            </Col>

            <Col xs={24} md={16}>
              <Form.Item
                label="المبلغ"
                name="amount"
                rules={[
                  { required: true, message: 'يرجى إدخال المبلغ' },
                  { type: 'number', min: 0.01, message: 'المبلغ يجب أن يكون أكبر من صفر' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  precision={2}
                  min={0}
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => (value || '').replace(/\$\s?|(,*)/g, '') as any}
                />
              </Form.Item>
            </Col>

            <Col xs={24} md={8}>
              <Form.Item
                label="العملة"
                name="currency"
              >
                <Select>
                  <Select.Option value="ش.ج">شيكل (ش.ج)</Select.Option>
                  <Select.Option value="$">دولار ($)</Select.Option>
                  <Select.Option value="€">يورو (€)</Select.Option>
                  <Select.Option value="د.أ">دينار أردني (د.أ)</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            {/* البيان والملاحظات */}
            <Col xs={24}>
              <Divider />
              <Title level={4}>
                <FileTextOutlined /> التفاصيل
              </Title>
            </Col>

            <Col xs={24}>
              <Form.Item
                label="البيان"
                name="description"
                rules={[{ required: true, message: 'يرجى إدخال بيان الإيصال' }]}
              >
                <TextArea
                  rows={3}
                  placeholder="وصف تفصيلي لسبب الدفع..."
                />
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                label="المرجع (اختياري)"
                name="reference"
              >
                <Input placeholder="رقم الفاتورة أو المرجع" />
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                label="ملاحظات إضافية"
                name="notes"
              >
                <Input placeholder="ملاحظات أخرى..." />
              </Form.Item>
            </Col>

            {/* الأزرار */}
            <Col xs={24}>
              <Divider />
              <Space size="middle" style={{ width: '100%', justifyContent: 'center' }}>
                <Button
                  type="default"
                  icon={<SaveOutlined />}
                  loading={loading}
                  htmlType="submit"
                >
                  حفظ الإيصال
                </Button>

                {receiptData && (
                  <ReceiptPrintButton
                    receiptData={{
                      id: receiptData.id || receiptData.receiptNumber,
                      receiptNumber: receiptData.receiptNumber,
                      receiptDate: receiptData.date || new Date().toISOString().split('T')[0],
                      type: receiptData.type || 'payment',
                      amount: receiptData.amount || 0,
                      paymentMethod: receiptData.paymentMethod || 'cash',
                      customerName: receiptData.customerName,
                      customerPhone: receiptData.customerPhone,
                      description: receiptData.description || 'إيصال دفع',
                      notes: receiptData.notes,
                      referenceNumber: receiptData.referenceNumber,
                      bankName: receiptData.bankName,
                      checkNumber: receiptData.checkNumber
                    }}
                    buttonText="طباعة الإيصال"
                    size="middle"
                    showDropdown={true}
                    onPrintSuccess={() => {
                      message.success('تم طباعة الإيصال بنجاح')
                    }}
                    onPrintError={(error) => {
                      message.error(`خطأ في طباعة الإيصال: ${error}`)
                    }}
                  />
                )}

                <Button
                  type="primary"
                  icon={<PrinterOutlined />}
                  loading={loading}
                  onClick={() => {
                    form.validateFields().then(values => {
                      handleSaveAndPrint(values)
                    })
                  }}
                >
                  حفظ وطباعة
                </Button>

                <Button onClick={handleReset}>
                  إعادة تعيين
                </Button>

                <Button onClick={onClose}>
                  إغلاق
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>

        {/* معاينة الإيصال */}
        {receiptData && (
          <Card
            title="معاينة الإيصال"
            style={{ marginTop: 24 }}
            size="small"
          >
            <div style={{
              background: '#f5f5f5',
              padding: '20px',
              borderRadius: '8px',
              textAlign: 'center',
              fontFamily: 'Arial, sans-serif'
            }}>
              <Title level={3} style={{ color: '#1890ff', margin: '0 0 20px 0' }}>
                إيصال استلام
              </Title>
              
              <Row gutter={[16, 8]} style={{ textAlign: 'right' }}>
                <Col span={12}>
                  <Text strong>رقم الإيصال:</Text>
                </Col>
                <Col span={12}>
                  <Text>{receiptData.receiptNumber}</Text>
                </Col>
                
                <Col span={12}>
                  <Text strong>التاريخ:</Text>
                </Col>
                <Col span={12}>
                  <Text>{receiptData.date}</Text>
                </Col>
                
                <Col span={12}>
                  <Text strong>المستلم من:</Text>
                </Col>
                <Col span={12}>
                  <Text>{receiptData.paidBy}</Text>
                </Col>
                
                <Col span={12}>
                  <Text strong>المبلغ:</Text>
                </Col>
                <Col span={12}>
                  <Text style={{ fontSize: '18px', fontWeight: 'bold', color: '#1890ff' }}>
                    {receiptData.amount.toFixed(2)} {receiptData.currency}
                  </Text>
                </Col>
                
                <Col span={12}>
                  <Text strong>البيان:</Text>
                </Col>
                <Col span={12}>
                  <Text>{receiptData.description}</Text>
                </Col>
                
                <Col span={12}>
                  <Text strong>طريقة الدفع:</Text>
                </Col>
                <Col span={12}>
                  <Text>
                    {receiptData.paymentMethod === 'cash' ? 'نقداً' :
                     receiptData.paymentMethod === 'check' ? 'شيك' :
                     receiptData.paymentMethod === 'bank' ? 'تحويل بنكي' :
                     receiptData.paymentMethod === 'card' ? 'بطاقة ائتمان' : 'أخرى'}
                  </Text>
                </Col>
              </Row>
              
              <div style={{
                marginTop: '30px',
                paddingTop: '20px',
                borderTop: '1px solid #d9d9d9',
                fontSize: '12px',
                color: '#666'
              }}>
                تم إنشاء هذا الإيصال بواسطة نظام ZET.IA
              </div>
            </div>
          </Card>
        )}
      </Card>
    </Modal>
  )
}

export default EnhancedReceiptManager
