import React from 'react';
import { Tag, Typography, Progress } from 'antd';
import UniversalReport from './UniversalReport';
import type { ReportData, ReportType } from '../../types/reports';
import { DateUtils } from '../../utils/dateConfig';
import { SafeLogger as Logger } from '../../utils/logger'

const { Text } = Typography;

const PaintQualityReport: React.FC = () => {
  // إنشاء التقرير
  const generateReport = async (filters: any): Promise<ReportData> => {
    try {
      // استعلام البيانات من قاعدة البيانات
      const response = await window.electronAPI.getPaintQualityReport({
        paintTypeId: filters.paintTypeId,
        dateRange: filters.dateRange
      });

      if (!response || !response.success || !response.data || !Array.isArray(response.data.data)) {
        throw new Error('لا توجد بيانات متاحة');
      }

      const paintData = response.data;

      const data = paintData.data;

      // حساب الإحصائيات
      const totalTypes = data.length;
      const totalOrders = data.reduce((sum, item) => sum + item.total_orders, 0);
      const totalCompleted = data.reduce((sum, item) => sum + item.completed_orders, 0);
      const totalCancelled = data.reduce((sum, item) => sum + item.cancelled_orders, 0);
      const totalArea = data.reduce((sum, item) => sum + item.total_area, 0);
      const totalCustomers = data.reduce((sum, item) => sum + item.unique_customers, 0);

      // إعداد البيانات للجدول
      const tableData = data.map((item, index) => ({
        key: item.paint_type_id,
        index: index + 1,
        paint_type_name: item.paint_type_name,
        color: item.color || '-',
        finish_type: item.finish_type || '-',
        total_orders: item.total_orders,
        completed_orders: item.completed_orders,
        cancelled_orders: item.cancelled_orders,
        success_rate: item.success_rate,
        total_area: item.total_area,
        avg_area_per_order: item.avg_area_per_order,
        unique_customers: item.unique_customers,
        customer_retention_score: item.customer_retention_score,
        quality_score: item.success_rate, // استخدام معدل النجاح كمؤشر جودة
        reliability_score: item.total_orders > 0 ? ((item.total_orders - item.cancelled_orders) / item.total_orders * 100) : 0
      }));

      // تعريف الأعمدة
      const columns = [
        {
          title: '#',
          key: 'index',

          width: 50,
          align: 'center' as const
        },
        {
          title: 'نوع الدهان',
          key: 'paint_type_name',

          filterable: true,
          width: 150
        },
        {
          title: 'اللون',
          key: 'color',

          width: 100,
          render: (record: any) => (
            <Tag color={record.color !== '-' ? 'blue' : 'default'}>{record.color}</Tag>
          )
        },
        {
          title: 'نوع التشطيب',
          key: 'finish_type',

          width: 120,
          render: (record: any) => (
            <Tag color={record.finish_type !== '-' ? 'green' : 'default'}>{record.finish_type}</Tag>
          )
        },
        {
          title: 'إجمالي الأوامر',

          sortable: true,
          key: 'total_orders',
          width: 100,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="blue">{record.total_orders}</Tag>
          )
        },
        {
          title: 'الأوامر المكتملة',
          sortable: true,
          key: 'completed_orders',
          width: 120,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="green">{record.completed_orders}</Tag>
          )
        },
        {
          title: 'الأوامر الملغية',
          sortable: true,
          key: 'cancelled_orders',
          width: 100,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="red">{record.cancelled_orders}</Tag>
          )
        },
        {
          title: 'معدل النجاح',
          sortable: true,
          key: 'success_rate',
          width: 120,
          align: 'center' as const,
          render: (record: any) => (
            <Progress
              percent={Math.round(record.success_rate)}
              size="small"
              status={record.success_rate >= 90 ? 'success' : record.success_rate >= 70 ? 'active' : 'exception'}
            />
          )
        },
        {
          title: 'إجمالي المساحة (م²)',

          sortable: true,
          key: 'total_area',
          width: 130,
          align: 'right' as const,
          render: (record: any) => record.total_area.toLocaleString('ar-EG', { minimumFractionDigits: 2 })
        },
        {
          title: 'متوسط المساحة/أمر',

          sortable: true,
          key: 'avg_area_per_order',
          width: 130,
          align: 'right' as const,
          render: (record: any) => (
            <Text style={{ color: '#722ed1' }}>
              {record.avg_area_per_order.toFixed(2)} م²
            </Text>
          )
        },
        {
          title: 'عدد العملاء',

          sortable: true,
          key: 'unique_customers',
          width: 100,
          align: 'center' as const,
          render: (record: any) => (
            <Tag color="purple">{record.unique_customers}</Tag>
          )
        },
        {
          title: 'مؤشر ولاء العملاء',

          sortable: true,
          key: 'customer_retention_score',
          width: 130,
          align: 'center' as const,
          render: (record: any) => {
            const value = record.customer_retention_score;
            const color = value >= 2 ? 'green' : value >= 1.5 ? 'orange' : 'red';
            const label = value >= 2 ? 'ممتاز' : value >= 1.5 ? 'جيد' : 'ضعيف';
            return (
              <Tag color={color}>
                {label} ({value.toFixed(1)})
              </Tag>
            );
          }
        },
        {
          title: 'مؤشر الجودة',

          sortable: true,
          key: 'quality_score',
          width: 120,
          align: 'center' as const,
          render: (record: any) => {
            const value = record.quality_score;
            const color = value >= 90 ? 'green' : value >= 70 ? 'orange' : 'red';
            const label = value >= 90 ? 'ممتاز' : value >= 70 ? 'جيد' : 'يحتاج تحسين';
            return (
              <Tag color={color}>
                {label} ({value.toFixed(1)}%)
              </Tag>
            );
          }
        },
        {
          title: 'مؤشر الموثوقية',

          sortable: true,
          key: 'reliability_score',
          width: 120,
          align: 'center' as const,
          render: (record: any) => {
            const value = record.reliability_score;
            return (
              <Progress
                percent={Math.round(value)}
                size="small"
                status={value >= 95 ? 'success' : value >= 85 ? 'active' : 'exception'}
              />
            );
          }
        }
      ];

      // إعداد الإحصائيات
      const avgSuccessRate = totalOrders > 0 ? (totalCompleted / totalOrders * 100) : 0;
      const avgAreaPerOrder = totalOrders > 0 ? (totalArea / totalOrders) : 0;
      const _avgCustomersPerType = totalTypes > 0 ? (totalCustomers / totalTypes) : 0;
      const overallReliability = totalOrders > 0 ? ((totalOrders - totalCancelled) / totalOrders * 100) : 0;

      const statistics = [
        {
          title: 'إجمالي أنواع الدهان',
          value: totalTypes,
          color: '#1890ff',
          icon: '🎨'
        },
        {
          title: 'إجمالي الأوامر',
          value: totalOrders,
          color: '#52c41a',
          icon: '📋'
        },
        {
          title: 'الأوامر المكتملة',
          value: totalCompleted,
          color: '#52c41a',
          icon: '✅'
        },
        {
          title: 'متوسط معدل النجاح',
          value: `${avgSuccessRate.toFixed(1)}%`,
          color: '#722ed1',
          icon: '🎯'
        },
        {
          title: 'إجمالي المساحة (م²)',
          value: totalArea.toLocaleString('ar-EG', { minimumFractionDigits: 2 }),
          color: '#fa8c16',
          icon: '📐'
        },
        {
          title: 'متوسط المساحة/أمر',
          value: `${avgAreaPerOrder.toFixed(2)} م²`,
          color: '#eb2f96',
          icon: '📏'
        },
        {
          title: 'إجمالي العملاء',
          value: totalCustomers,
          color: '#13c2c2',
          icon: '👥'
        },
        {
          title: 'مؤشر الموثوقية العام',
          value: `${overallReliability.toFixed(1)}%`,
          color: '#f759ab',
          icon: '🛡️'
        }
      ];

      return {
        title: 'تقرير جودة الدهان',
        columns,
        data: tableData,
        statistics,
        summary: {
          totalRecords: totalTypes,
          totalOrders: totalOrders,
          totalCompleted: totalCompleted,
          avgSuccessRate: avgSuccessRate,
          overallReliability: overallReliability
        }
      };

    } catch (error) {
      Logger.error('PaintQualityReport', 'خطأ في إنشاء تقرير جودة الدهان:', error);
      throw new Error('فشل في تحميل بيانات التقرير');
    }
  };

  return (
    <UniversalReport
      reportType={'paint_quality' as ReportType}
      title="تقرير جودة الدهان"
      description="تقرير تحليلي لجودة الدهان مع مؤشرات الأداء"
      onGenerateReport={generateReport}
      showDateRange={true}
    />
  );
};

export default PaintQualityReport;
