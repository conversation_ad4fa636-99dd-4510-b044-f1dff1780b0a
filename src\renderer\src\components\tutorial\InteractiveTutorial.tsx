import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>, <PERSON>ton, Typography, Space, Progress, Card, Steps, Alert,
  Row, Col, Avatar, Tag, Tooltip, Divider, List, Image
} from 'antd'
import {
  PlayCircleOutlined, PauseCircleOutlined, StepForwardOutlined,
  StepBackwardOutlined, CloseOutlined, SoundOutlined, BookOutlined,
  CheckCircleOutlined, BulbOutlined, WarningOutlined, StarOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { tutorialSystem, TutorialStep, TutorialModule } from '../../utils/tutorialSystem'
import { audioSystem } from '../../utils/audioSystem'

const { Title, Text, Paragraph } = Typography
const { Step } = Steps

const TutorialOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9998;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: rtl;
`

const TutorialCard = styled(Card)`
  max-width: 600px;
  width: 90%;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  
  .ant-card-head {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px 16px 0 0;
    
    .ant-card-head-title {
      color: white;
      font-weight: bold;
    }
  }
`

const HighlightBox = styled.div`
  position: absolute;
  border: 3px solid #1890ff;
  border-radius: 8px;
  background: rgba(24, 144, 255, 0.1);
  pointer-events: none;
  z-index: 9999;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(24, 144, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(24, 144, 255, 0); }
  }
`

const StepContent = styled.div`
  padding: 20px 0;
  
  .step-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    .step-number {
      background: #1890ff;
      color: white;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-left: 12px;
    }
    
    .step-title {
      font-size: 18px;
      font-weight: bold;
      color: #1890ff;
    }
  }
  
  .step-description {
    margin-bottom: 16px;
    line-height: 1.6;
    color: #666;
  }
  
  .step-tips {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 8px;
    padding: 12px;
    margin-top: 16px;
    
    .tip-icon {
      color: #52c41a;
      margin-left: 8px;
    }
  }
`

const ControlPanel = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  
  .progress-info {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .control-buttons {
    display: flex;
    gap: 8px;
  }
`

interface InteractiveTutorialProps {
  visible: boolean
  moduleId?: string
  onClose: () => void
}

const InteractiveTutorial: React.FC<InteractiveTutorialProps> = ({
  visible,
  moduleId,
  onClose
}) => {
  const [currentModule, setCurrentModule] = useState<TutorialModule | null>(null)
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [highlightElement, setHighlightElement] = useState<HTMLElement | null>(null)
  const [highlightBox, setHighlightBox] = useState<{ top: number; left: number; width: number; height: number } | null>(null)

  useEffect(() => {
    if (visible && moduleId) {
      const module = tutorialSystem.getModule(moduleId)
      if (module) {
        setCurrentModule(module)
        setCurrentStepIndex(0)
        setIsPlaying(true)
        audioSystem.playSound('success')
      }
    }
  }, [visible, moduleId])

  useEffect(() => {
    if (currentModule && currentStepIndex < currentModule.steps.length) {
      const step = currentModule.steps[currentStepIndex]
      highlightTarget(step.target)

      if (step.duration && isPlaying) {
        const timer = setTimeout(() => {
          nextStep()
        }, step.duration * 1000)

        return () => clearTimeout(timer)
      }
    }

    return () => {
      // cleanup function for all cases
    }
  }, [currentStepIndex, currentModule, isPlaying])

  const highlightTarget = (target?: string) => {
    // إزالة التمييز السابق
    if (highlightElement) {
      highlightElement.classList.remove('tutorial-highlight')
    }
    setHighlightBox(null)

    if (target) {
      const element = document.querySelector(target) as HTMLElement
      if (element) {
        setHighlightElement(element)
        element.classList.add('tutorial-highlight')
        
        // إنشاء صندوق التمييز
        const rect = element.getBoundingClientRect()
        setHighlightBox({
          top: rect.top - 5,
          left: rect.left - 5,
          width: rect.width + 10,
          height: rect.height + 10
        })
        
        // التمرير إلى العنصر
        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    }
  }

  const nextStep = () => {
    if (!currentModule) return
    
    audioSystem.playSound('click')
    
    if (currentStepIndex < currentModule.steps.length - 1) {
      setCurrentStepIndex(prev => prev + 1)
    } else {
      // انتهاء الوحدة
      completeModule()
    }
  }

  const prevStep = () => {
    if (currentStepIndex > 0) {
      audioSystem.playSound('click')
      setCurrentStepIndex(prev => prev - 1)
    }
  }

  const completeModule = () => {
    audioSystem.playSound('success')
    setIsPlaying(false)
    
    // يمكن إضافة منطق إضافي هنا لحفّ التقدم
    setTimeout(() => {
      onClose()
    }, 2000)
  }

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying)
    audioSystem.playSound('click')
  }

  const handleClose = () => {
    setIsPlaying(false)
    if (highlightElement) {
      highlightElement.classList.remove('tutorial-highlight')
    }
    setHighlightBox(null)
    audioSystem.playSound('click')
    onClose()
  }

  if (!visible || !currentModule) return null

  const currentStep = currentModule.steps[currentStepIndex]
  const progress = ((currentStepIndex + 1) / currentModule.steps.length) * 100

  return (
    <>
      {/* صندوق التمييز */}
      {highlightBox && (
        <HighlightBox
          style={{
            top: highlightBox.top,
            left: highlightBox.left,
            width: highlightBox.width,
            height: highlightBox.height
          }}
        />
      )}

      <TutorialOverlay>
        <TutorialCard
          title={
            <Space>
              <BookOutlined />
              {currentModule.title}
              <Tag color="blue">تفاعلي</Tag>
            </Space>
          }
          extra={
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={handleClose}
              style={{ color: 'white' }}
            />
          }
        >
          {/* شريط التقدم */}
          <div style={{ marginBottom: 20 }}>
            <Progress
              percent={progress}
              status="active"
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              الخطوة {currentStepIndex + 1} من {currentModule.steps.length}
            </Text>
          </div>

          {/* محتوى الخطوة */}
          <StepContent>
            <div className="step-header">
              <div className="step-number">{currentStepIndex + 1}</div>
              <div className="step-title">{currentStep?.title}</div>
            </div>
            
            <div className="step-description">
              {currentStep?.description}
            </div>

            {/* نصائح إضافية */}
            {currentStepIndex === 0 && (
              <div className="step-tips">
                <BulbOutlined className="tip-icon" />
                <Text>
                  نصيحة: يمكنك إيقاف التشغيل التلقائي والتنقل يدوياً بين الخطوات
                </Text>
              </div>
            )}

            {currentModule.id === 'production' && currentStepIndex > 0 && (
              <div className="step-tips">
                <StarOutlined className="tip-icon" />
                <Text>
                  هذه الخطوة مهمة جداً في الدورة المستندية الإنتاجية
                </Text>
              </div>
            )}

            {/* معلومات إضافية للخطوة */}
            {currentStep?.videoUrl && (
              <Alert
                message="فيديو تعليمي متاح"
                description="يمكنك مشاهدة فيديو تعليمي لهذه الخطوة"
                type="info"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
          </StepContent>

          {/* لوحة التحكم */}
          <ControlPanel>
            <div className="progress-info">
              <Text type="secondary">
                الوقت المقدر: {currentModule.estimatedTime} دقيقة
              </Text>
              <Divider type="vertical" />
              <Text type="secondary">
                الفئة: {currentModule.category === 'production' ? 'إنتاج' : 'عام'}
              </Text>
            </div>

            <div className="control-buttons">
              <Tooltip title="الخطوة السابقة">
                <Button
                  icon={<StepBackwardOutlined />}
                  onClick={prevStep}
                  disabled={currentStepIndex === 0}
                />
              </Tooltip>
              
              <Tooltip title={isPlaying ? "إيقاف مؤقت" : "تشغيل"}>
                <Button
                  type="primary"
                  icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                  onClick={togglePlayPause}
                />
              </Tooltip>
              
              <Tooltip title="الخطوة التالية">
                <Button
                  icon={<StepForwardOutlined />}
                  onClick={nextStep}
                  disabled={currentStepIndex === currentModule.steps.length - 1}
                />
              </Tooltip>
              
              <Tooltip title="إعدادات الصوت">
                <Button
                  icon={<SoundOutlined />}
                  onClick={() => {
                    audioSystem.playSound('notification')
                  }}
                />
              </Tooltip>
            </div>
          </ControlPanel>

          {/* خطوات الوحدة */}
          <Divider />
          <Title level={5}>خطوات الوحدة:</Title>
          <Steps
            size="small"
            current={currentStepIndex}
            direction="horizontal"
            style={{ marginTop: 16 }}
          >
            {currentModule.steps.map((step, index) => (
              <Step
                key={step.id}
                title={`${index + 1}`}
                description={step.title}
                onClick={() => {
                  setCurrentStepIndex(index)
                  audioSystem.playSound('click')
                }}
                style={{ cursor: 'pointer' }}
              />
            ))}
          </Steps>

          {/* رسالة الإنجاز */}
          {currentStepIndex === currentModule.steps.length - 1 && (
            <Alert
              message="تهانينا! أوشكت على إنهاء هذه الوحدة"
              description="لقد تعلمت الأساسيات المهمة. انقر على 'التالي' لإنهاء الوحدة"
              type="success"
              showIcon
              style={{ marginTop: 16 }}
            />
          )}
        </TutorialCard>
      </TutorialOverlay>
    </>
  )
}

export default InteractiveTutorial

// إضافة الأنماط العامة للتمييز
const tutorialStyles = `
  .tutorial-highlight {
    position: relative;
    z-index: 9997;
    box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.6) !important;
    border-radius: 4px !important;
    background-color: rgba(24, 144, 255, 0.1) !important;
  }

  .tutorial-highlight::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border: 2px solid #1890ff;
    border-radius: 8px;
    animation: tutorialPulse 2s infinite;
    pointer-events: none;
  }

  @keyframes tutorialPulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
  }
`

// إضافة الأنماط إلى الصفحة
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = tutorialStyles
  document.head.appendChild(styleElement)
}
