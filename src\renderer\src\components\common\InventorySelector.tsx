import React, { useState, useEffect } from 'react'
import { Select, InputNumber, Alert, Tag, Space, Typography } from 'antd'
import { InfoCircleOutlined, WarningOutlined, CheckCircleOutlined } from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'

const { Option } = Select
const { Text } = Typography

interface InventoryInfo {
  quantity: number
  reserved_quantity: number
  available_quantity: number
  item_name: string
  warehouse_name: string
  unit: string
}

interface InventorySelectorProps {
  items: Array<{
    id: number
    name: string
    code: string
    unit: string
    sale_price?: number
  }>
  warehouses: Array<{
    id: number
    name: string
  }>
  selectedItemId?: number
  selectedWarehouseId?: number
  quantity?: number
  onItemChange: (itemId: number) => void
  onWarehouseChange: (warehouseId: number) => void
  onQuantityChange: (quantity: number) => void
  disabled?: boolean
}

const InventorySelector: React.FC<InventorySelectorProps> = ({
  items,
  warehouses,
  selectedItemId,
  selectedWarehouseId,
  quantity = 0,
  onItemChange,
  onWarehouseChange,
  onQuantityChange,
  disabled = false
}) => {
  const [inventoryInfo, setInventoryInfo] = useState<InventoryInfo | null>(null)
  const [loading, setLoading] = useState(false)

  // تحديث معلومات المخزون عند تغيير الصنف أو المخزن
  useEffect(() => {
    if (selectedItemId && selectedWarehouseId) {
      loadInventoryInfo()
    } else {
      setInventoryInfo(null)
    }
  }, [selectedItemId, selectedWarehouseId])

  const loadInventoryInfo = async () => {
    if (!selectedItemId || !selectedWarehouseId) return

    setLoading(true)
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.invoke('get-item-warehouse-quantity', selectedItemId, selectedWarehouseId)
        if (response.success) {
          setInventoryInfo(response.data)
        }
      }
    } catch (error) {
      Logger.error('InventorySelector', 'خطأ في تحميل معلومات المخزون:', error)
    } finally {
      setLoading(false)
    }
  }

  const getQuantityStatus = () => {
    if (!inventoryInfo || !quantity) return 'normal'
    
    if (quantity > inventoryInfo.available_quantity) return 'error'
    if (quantity === inventoryInfo.available_quantity) return 'warning'
    return 'success'
  }

  const getStatusIcon = () => {
    const status = getQuantityStatus()
    switch (status) {
      case 'error': return <WarningOutlined style={{ color: '#ff4d4f' }} />
      case 'warning': return <InfoCircleOutlined style={{ color: '#faad14' }} />
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      default: return null
    }
  }

  const getStatusMessage = () => {
    if (!inventoryInfo) return null
    
    const status = getQuantityStatus()
    switch (status) {
      case 'error':
        return `الكمية المطلوبة (${quantity}) أكبر من المتوفر (${inventoryInfo.available_quantity})`
      case 'warning':
        return `ستستنفد كامل الكمية المتوفرة (${inventoryInfo.available_quantity})`
      case 'success':
        return `الكمية متوفرة (${inventoryInfo.available_quantity} ${inventoryInfo.unit})`
      default:
        return null
    }
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      {/* اختيار المخزن أولاً */}
      <Select
        placeholder="اختر المخزن أولاً"
        value={selectedWarehouseId || undefined}
        onChange={onWarehouseChange}
        showSearch
        optionFilterProp="children"
        filterOption={(input, option) =>
          option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
        }
        style={{ width: '100%' }}
        disabled={disabled}
        loading={loading}
        notFoundContent={loading ? 'جاري التحميل...' : 'لا توجد مخازن متاحة'}
      >
        {warehouses.map(warehouse => (
          <Option key={warehouse.id} value={warehouse.id}>
            {warehouse.name}
          </Option>
        ))}
      </Select>

      {/* اختيار الصنف */}
      <Select
        placeholder={selectedWarehouseId ? "اختر الصنف" : "اختر المخزن أولاً"}
        value={selectedItemId || undefined}
        onChange={onItemChange}
        style={{ width: '100%' }}
        showSearch
        optionFilterProp="children"
        disabled={disabled || !selectedWarehouseId}
        loading={loading}
        notFoundContent={
          loading ? 'جاري التحميل...' :
          !selectedWarehouseId ? 'يرجى اختيار المخزن أولاً' :
          'لا توجد أصناف متاحة في هذا المخزن'
        }
        filterOption={(input, option) =>
          option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
        }
      >
        {items.map(item => (
          <Option key={item.id} value={item.id}>
            <Space>
              <Text strong>{item.name}</Text>
              <Text type="secondary">({item.code})</Text>
              {item.sale_price && (
                <Tag color="blue">₪{item.sale_price}</Tag>
              )}
            </Space>
          </Option>
        ))}
      </Select>

      {/* عرض معلومات المخزون */}
      {inventoryInfo && (
        <div style={{ 
          padding: '8px 12px', 
          backgroundColor: '#f6f6f6', 
          borderRadius: '6px',
          border: '1px solid #d9d9d9'
        }}>
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <Space>
              <Text strong>المخزون:</Text>
              <Tag color="blue">{inventoryInfo.quantity} {inventoryInfo.unit}</Tag>
              <Text type="secondary">محجوز: {inventoryInfo.reserved_quantity}</Text>
            </Space>
            
            <Space>
              <Text strong>المتاح:</Text>
              <Tag color="green">{inventoryInfo.available_quantity} {inventoryInfo.unit}</Tag>
            </Space>
          </Space>
        </div>
      )}

      {/* إدخال الكمية */}
      <Space style={{ width: '100%' }}>
        <InputNumber
          placeholder="الكمية"
          value={quantity}
          onChange={(value) => onQuantityChange(value || 0)}
          min={0}
          style={{ flex: 1 }}
          disabled={disabled || !selectedItemId || !selectedWarehouseId}
          addonAfter={inventoryInfo?.unit || 'وحدة'}
        />
        {getStatusIcon()}
      </Space>

      {/* رسالة حالة الكمية */}
      {inventoryInfo && quantity > 0 && (
        <Alert
          message={getStatusMessage()}
          type={getQuantityStatus() === 'error' ? 'error' : getQuantityStatus() === 'warning' ? 'warning' : 'success'}
          showIcon

        />
      )}
    </Space>
  )
}

export default InventorySelector
