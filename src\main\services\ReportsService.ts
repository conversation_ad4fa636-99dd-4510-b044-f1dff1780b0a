import { DatabaseService } from './DatabaseService'

export class ReportsService {
  private db: any

  constructor() {
    this.db = DatabaseService.getInstance().getDatabase()
  }

  // تقرير المرتجعات والخصومات
  public getSalesReturnsAndDiscounts(params: {
    fromDate: string
    toDate: string
    reportType: string
  }): any {
    try {
      // المرتجعات
      const returnsQuery = this.db.exec(`
        SELECT
          sr.id,
          sr.return_number,
          sr.original_invoice_number,
          c.customer_name,
          sr.return_date,
          sr.return_type,
          sr.return_reason,
          sr.total_amount,
          sr.discount_amount,
          sr.net_amount,
          sr.status,
          sr.created_by,
          sr.created_at
        FROM sales_returns sr
        LEFT JOIN customers c ON sr.customer_id = c.id
        WHERE sr.return_date BETWEEN '${params.fromDate}' AND '${params.toDate}'
        ORDER BY sr.return_date DESC
      `)

      const returns: any[] = []
      if (returnsQuery.length > 0 && returnsQuery[0].values.length > 0) {
        const columns = returnsQuery[0].columns
        returnsQuery[0].values.forEach((values: any[]) => {
          const row: any = {}
          columns.forEach((col: string, index: number) => {
            row[col] = values[index]
          })
          returns.push(row)
        })
      }

      // الخصومات
      const discountsQuery = this.db.exec(`
        SELECT
          sd.id,
          sd.invoice_number,
          c.customer_name,
          sd.discount_date,
          sd.discount_type,
          sd.discount_value,
          sd.discount_amount,
          sd.discount_reason,
          sd.approved_by,
          sd.created_at
        FROM sales_discounts sd
        LEFT JOIN invoices i ON sd.invoice_id = i.id
        LEFT JOIN customers c ON i.customer_id = c.id
        WHERE sd.discount_date BETWEEN '${params.fromDate}' AND '${params.toDate}'
        ORDER BY sd.discount_date DESC
      `)

      const discounts: any[] = []
      if (discountsQuery.length > 0 && discountsQuery[0].values.length > 0) {
        const columns = discountsQuery[0].columns
        discountsQuery[0].values.forEach((values: any[]) => {
          const row: any = {}
          columns.forEach((col: string, index: number) => {
            row[col] = values[index]
          })
          discounts.push(row)
        })
      }

      // حساب الإحصائيات
      const totalReturns = returns.length
      const totalReturnAmount = returns.reduce((sum: number, item: any) => sum + (item.net_amount || 0), 0)

      const totalDiscounts = discounts.length
      const totalDiscountAmount = discounts.reduce((sum: number, item: any) => sum + (item.discount_amount || 0), 0)

      // تجميع حسب السبب
      const returnsByReason = returns.reduce((acc: any, item: any) => {
        const reason = item.return_reason || 'غير محدد'
        if (!acc[reason]) {
          acc[reason] = { reason, count: 0, amount: 0 }
        }
        acc[reason].count++
        acc[reason].amount += item.net_amount || 0
        return acc
      }, {})

      // تجميع حسب العميل
      const returnsByCustomer = returns.reduce((acc: any, item: any) => {
        const customer = item.customer_name || 'غير محدد'
        if (!acc[customer]) {
          acc[customer] = { customer, count: 0, amount: 0 }
        }
        acc[customer].count++
        acc[customer].amount += item.net_amount || 0
        return acc
      }, {})

      const discountsByType = discounts.reduce((acc: any, item: any) => {
        const type = item.discount_type || 'غير محدد'
        if (!acc[type]) {
          acc[type] = { type, count: 0, amount: 0 }
        }
        acc[type].count++
        acc[type].amount += item.discount_amount || 0
        return acc
      }, {})

      const discountsByCustomer = discounts.reduce((acc: any, item: any) => {
        const customer = item.customer_name || 'غير محدد'
        if (!acc[customer]) {
          acc[customer] = { customer, count: 0, amount: 0 }
        }
        acc[customer].count++
        acc[customer].amount += item.discount_amount || 0
        return acc
      }, {})

      // حساب إجمالي المبيعات للفترة لحساب النسب
      const totalSales = this.db.prepare(`
        SELECT COALESCE(SUM(total_amount), 0) as total
        FROM invoices
        WHERE invoice_date BETWEEN ? AND ?
        AND status = 'paid'
      `).get(params.fromDate, params.toDate) as any

      const salesAmount = totalSales?.total || 1
      const returnsPercentage = (totalReturnAmount / salesAmount) * 100
      const discountsPercentage = (totalDiscountAmount / salesAmount) * 100

      return {
        success: true,
        data: {
          period: {
            from_date: params.fromDate,
            to_date: params.toDate
          },
          returns: {
            items: params.reportType === 'discounts' ? [] : returns,
            total_returns: totalReturns,
            total_amount: totalReturnAmount,
            by_reason: Object.values(returnsByReason),
            by_customer: Object.values(returnsByCustomer)
          },
          discounts: {
            items: params.reportType === 'returns' ? [] : discounts,
            total_discounts: totalDiscounts,
            total_amount: totalDiscountAmount,
            by_type: Object.values(discountsByType),
            by_customer: Object.values(discountsByCustomer)
          },
          summary: {
            total_sales_adjustments: totalReturnAmount + totalDiscountAmount,
            returns_percentage: returnsPercentage,
            discounts_percentage: discountsPercentage,
            impact_on_revenue: totalReturnAmount + totalDiscountAmount
          }
        }
      }
    } catch (error) {
      console.error('خطأ في تقرير المرتجعات والخصومات:', error)
      return {
        success: false,
        message: 'فشل في تحميل تقرير المرتجعات والخصومات'
      }
    }
  }

  // تقرير المبيعات حسب المنطقة
  public getSalesByRegion(params: {
    fromDate: string
    toDate: string
    sortBy: string
  }): any {
    try {
      const regions = this.db.prepare(`
        SELECT 
          r.id as region_id,
          r.region_name,
          r.region_code,
          COUNT(DISTINCT i.id) as total_invoices,
          COUNT(DISTINCT c.id) as total_customers,
          COALESCE(SUM(i.total_amount), 0) as total_sales,
          COALESCE(AVG(i.total_amount), 0) as average_invoice_value
        FROM regions r
        LEFT JOIN customers c ON r.id = c.region_id
        LEFT JOIN invoices i ON c.id = i.customer_id 
          AND i.invoice_date BETWEEN ? AND ?
          AND i.status = 'paid'
        GROUP BY r.id, r.region_name, r.region_code
        ORDER BY total_sales DESC
      `).all(params.fromDate, params.toDate)

      // حساب إجمالي المبيعات
      const totalSales = regions.reduce((sum: number, region: any) => sum + region.total_sales, 0)

      // إضافة النسب المئوية ومعدل النمو
      const regionsWithMetrics = regions.map((region: any) => {
        const salesPercentage = totalSales > 0 ? (region.total_sales / totalSales) * 100 : 0
        
        // حساب معدل النمو (مقارنة بالفترة السابقة)
        const previousPeriodSales = this.db.prepare(`
          SELECT COALESCE(SUM(i.total_amount), 0) as total
          FROM invoices i
          JOIN customers c ON i.customer_id = c.id
          WHERE c.region_id = ?
          AND i.invoice_date BETWEEN DATE(?, '-1 year') AND DATE(?, '-1 year')
          AND i.status = 'paid'
        `).get(region.region_id, params.fromDate, params.toDate) as any

        const previousSales = previousPeriodSales?.total || 0
        const growthRate = previousSales > 0 ? ((region.total_sales - previousSales) / previousSales) * 100 : 0

        return {
          ...region,
          sales_percentage: salesPercentage,
          growth_rate: growthRate,
          top_products: [], // يمكن إضافة استعلام للمنتجات الأكثر مبيعاً
          monthly_breakdown: [] // يمكن إضافة تفصيل شهري
        }
      })

      // ترتيب حسب المعيار المطلوب
      let sortedRegions = regionsWithMetrics
      switch (params.sortBy) {
        case 'invoices':
          sortedRegions = regionsWithMetrics.sort((a: any, b: any) => b.total_invoices - a.total_invoices)
          break
        case 'customers':
          sortedRegions = regionsWithMetrics.sort((a: any, b: any) => b.total_customers - a.total_customers)
          break
        case 'growth':
          sortedRegions = regionsWithMetrics.sort((a: any, b: any) => b.growth_rate - a.growth_rate)
          break
        default: // sales
          sortedRegions = regionsWithMetrics.sort((a: any, b: any) => b.total_sales - a.total_sales)
      }

      const bestPerformingRegion = sortedRegions[0]?.region_name || 'غير محدد'
      const worstPerformingRegion = sortedRegions[sortedRegions.length - 1]?.region_name || 'غير محدد'

      // حساب معدل النمو الإجمالي
      const previousTotalSales = this.db.prepare(`
        SELECT COALESCE(SUM(total_amount), 0) as total
        FROM invoices
        WHERE invoice_date BETWEEN DATE(?, '-1 year') AND DATE(?, '-1 year')
        AND status = 'paid'
      `).get(params.fromDate, params.toDate) as any

      const previousTotal = previousTotalSales?.total || 0
      const overallGrowth = previousTotal > 0 ? ((totalSales - previousTotal) / previousTotal) * 100 : 0

      return {
        success: true,
        data: {
          period: {
            from_date: params.fromDate,
            to_date: params.toDate
          },
          regions: sortedRegions,
          summary: {
            total_sales: totalSales,
            total_invoices: regions.reduce((sum: number, region: any) => sum + region.total_invoices, 0),
            total_regions: regions.length,
            best_performing_region: bestPerformingRegion,
            worst_performing_region: worstPerformingRegion,
            average_sales_per_region: totalSales / regions.length
          },
          comparison: {
            previous_period_sales: previousTotal,
            growth_percentage: overallGrowth,
            regions_growth: sortedRegions.map((region: any) => ({
              region: region.region_name,
              growth: region.growth_rate
            }))
          }
        }
      }
    } catch (error) {
      console.error('خطأ في تقرير المبيعات حسب المنطقة:', error)
      return {
        success: false,
        message: 'فشل في تحميل تقرير المبيعات حسب المنطقة'
      }
    }
  }

  // تقرير العملاء الأكثر ربحية
  public getTopProfitableCustomers(params: {
    fromDate: string
    toDate: string
    analysisType: string
    topCount: number
  }): any {
    try {
      const customers = this.db.prepare(`
        SELECT
          c.id as customer_id,
          c.customer_name,
          c.customer_code,
          c.customer_type,
          r.region_name as region,
          COUNT(DISTINCT i.id) as total_invoices,
          COALESCE(SUM(i.total_amount), 0) as total_sales,
          COALESCE(SUM(i.cost_amount), 0) as total_cost,
          COALESCE(SUM(i.total_amount - i.cost_amount), 0) as gross_profit,
          COALESCE(AVG(i.total_amount), 0) as average_invoice_value,
          MAX(i.invoice_date) as last_purchase_date,
          c.payment_terms,
          c.credit_limit,
          COALESCE(c.outstanding_balance, 0) as outstanding_balance
        FROM customers c
        LEFT JOIN regions r ON c.region_id = r.id
        LEFT JOIN invoices i ON c.id = i.customer_id
          AND i.invoice_date BETWEEN ? AND ?
          AND i.status = 'paid'
        GROUP BY c.id, c.customer_name, c.customer_code, c.customer_type, r.region_name
        HAVING total_sales > 0
        ORDER BY gross_profit DESC
        LIMIT ?
      `).all(params.fromDate, params.toDate, params.topCount)

      // حساب هامش الربح وإضافة معلومات إضافية
      const customersWithMetrics = customers.map((customer: any, index: number) => {
        const profitMargin = customer.total_sales > 0 ? (customer.gross_profit / customer.total_sales) * 100 : 0

        // حساب قيمة العميل مدى الحياة (تقدير بسيط)
        const customerLifetimeValue = customer.gross_profit * 2 // تقدير بسيط

        // حساب معدل النمو
        const previousPeriodSales = this.db.prepare(`
          SELECT COALESCE(SUM(total_amount), 0) as total
          FROM invoices
          WHERE customer_id = ?
          AND invoice_date BETWEEN DATE(?, '-1 year') AND DATE(?, '-1 year')
          AND status = 'paid'
        `).get(customer.customer_id, params.fromDate, params.toDate) as any

        const previousSales = previousPeriodSales?.total || 0
        const growthRate = previousSales > 0 ? ((customer.total_sales - previousSales) / previousSales) * 100 : 0

        // تحليل RFM مبسط
        const daysSinceLastPurchase = customer.last_purchase_date ?
          Math.floor((new Date().getTime() - new Date(customer.last_purchase_date).getTime()) / (1000 * 60 * 60 * 24)) : 999

        const recencyScore = daysSinceLastPurchase <= 30 ? 5 : daysSinceLastPurchase <= 90 ? 4 : daysSinceLastPurchase <= 180 ? 3 : daysSinceLastPurchase <= 365 ? 2 : 1
        const frequencyScore = customer.total_invoices >= 20 ? 5 : customer.total_invoices >= 10 ? 4 : customer.total_invoices >= 5 ? 3 : customer.total_invoices >= 2 ? 2 : 1
        const monetaryScore = customer.total_sales >= 50000 ? 5 : customer.total_sales >= 20000 ? 4 : customer.total_sales >= 10000 ? 3 : customer.total_sales >= 5000 ? 2 : 1

        let rfmSegment = 'New Customers'
        if (recencyScore >= 4 && frequencyScore >= 4 && monetaryScore >= 4) rfmSegment = 'Champions'
        else if (recencyScore >= 3 && frequencyScore >= 3 && monetaryScore >= 3) rfmSegment = 'Loyal Customers'
        else if (recencyScore >= 3 && frequencyScore <= 2) rfmSegment = 'Potential Loyalists'
        else if (recencyScore <= 2 && frequencyScore >= 3) rfmSegment = 'At Risk'
        else if (recencyScore <= 2 && frequencyScore <= 2) rfmSegment = 'Hibernating'

        return {
          ...customer,
          profit_margin: profitMargin,
          rank: index + 1,
          growth_rate: growthRate,
          customer_lifetime_value: customerLifetimeValue,
          frequency_score: frequencyScore,
          recency_score: recencyScore,
          monetary_score: monetaryScore,
          rfm_segment: rfmSegment
        }
      })

      // ترتيب حسب نوع التحليل
      let sortedCustomers = customersWithMetrics
      switch (params.analysisType) {
        case 'sales':
          sortedCustomers = customersWithMetrics.sort((a: any, b: any) => b.total_sales - a.total_sales)
          break
        case 'margin':
          sortedCustomers = customersWithMetrics.sort((a: any, b: any) => b.profit_margin - a.profit_margin)
          break
        case 'rfm':
          sortedCustomers = customersWithMetrics.sort((a: any, b: any) => {
            const aScore = a.recency_score + a.frequency_score + a.monetary_score
            const bScore = b.recency_score + b.frequency_score + b.monetary_score
            return bScore - aScore
          })
          break
        default: // profit
          sortedCustomers = customersWithMetrics.sort((a: any, b: any) => b.gross_profit - a.gross_profit)
      }

      // إعادة ترقيم الترتيب
      sortedCustomers = sortedCustomers.map((customer: any, index: number) => ({
        ...customer,
        rank: index + 1
      }))

      // حساب الإحصائيات
      const totalCustomers = this.db.prepare(`
        SELECT COUNT(DISTINCT customer_id) as count
        FROM invoices
        WHERE invoice_date BETWEEN ? AND ?
        AND status = 'paid'
      `).get(params.fromDate, params.toDate) as any

      const totalSales = sortedCustomers.reduce((sum: number, customer: any) => sum + customer.total_sales, 0)
      const totalProfit = sortedCustomers.reduce((sum: number, customer: any) => sum + customer.gross_profit, 0)
      const averageProfitMargin = totalSales > 0 ? (totalProfit / totalSales) * 100 : 0

      const top10Contribution = sortedCustomers.slice(0, 10).reduce((sum: number, customer: any) => sum + customer.total_sales, 0)
      const top20Contribution = sortedCustomers.slice(0, 20).reduce((sum: number, customer: any) => sum + customer.total_sales, 0)

      const top10Percentage = totalSales > 0 ? (top10Contribution / totalSales) * 100 : 0
      const top20Percentage = totalSales > 0 ? (top20Contribution / totalSales) * 100 : 0

      // تجميع حسب تصنيف RFM
      const segments = sortedCustomers.reduce((acc: any, customer: any) => {
        const segment = customer.rfm_segment
        if (!acc[segment]) acc[segment] = []
        acc[segment].push(customer)
        return acc
      }, {})

      // تحليل مبدأ باريتو
      const top20PercentCount = Math.ceil(totalCustomers.count * 0.2)
      const paretoContribution = totalSales > 0 ? (top20Contribution / totalSales) * 100 : 0

      // توزيع الربحية
      const highProfitCustomers = sortedCustomers.filter((c: any) => c.profit_margin >= 30).length
      const mediumProfitCustomers = sortedCustomers.filter((c: any) => c.profit_margin >= 15 && c.profit_margin < 30).length
      const lowProfitCustomers = sortedCustomers.filter((c: any) => c.profit_margin < 15).length

      return {
        success: true,
        data: {
          period: {
            from_date: params.fromDate,
            to_date: params.toDate
          },
          customers: sortedCustomers,
          summary: {
            total_customers: totalCustomers.count,
            total_sales: totalSales,
            total_profit: totalProfit,
            average_profit_margin: averageProfitMargin,
            top_10_contribution: top10Percentage,
            top_20_contribution: top20Percentage
          },
          segments: {
            champions: segments['Champions'] || [],
            loyal_customers: segments['Loyal Customers'] || [],
            potential_loyalists: segments['Potential Loyalists'] || [],
            at_risk: segments['At Risk'] || [],
            hibernating: segments['Hibernating'] || []
          },
          analysis: {
            pareto_principle: {
              top_20_percent_customers: top20PercentCount,
              their_sales_contribution: paretoContribution
            },
            profit_distribution: {
              high_profit: highProfitCustomers,
              medium_profit: mediumProfitCustomers,
              low_profit: lowProfitCustomers
            }
          }
        }
      }
    } catch (error) {
      console.error('خطأ في تقرير العملاء الأكثر ربحية:', error)
      return {
        success: false,
        message: 'فشل في تحميل تقرير العملاء الأكثر ربحية'
      }
    }
  }

  // تقرير مقارنة أسعار الموردين
  public getSupplierPriceComparison(params: {
    fromDate: string
    toDate: string
    category: string
  }): any {
    try {
      let categoryFilter = ''
      if (params.category && params.category !== 'all') {
        categoryFilter = 'AND p.category = ?'
      }

      const query = `
        SELECT
          s.id as supplier_id,
          s.supplier_name,
          p.id as product_id,
          p.product_name,
          p.product_code,
          p.category,
          po.unit_price,
          po.purchase_date as last_purchase_date,
          SUM(pod.quantity) as quantity_purchased,
          SUM(pod.quantity * po.unit_price) as total_amount,
          po.currency,
          s.payment_terms,
          s.delivery_time,
          po.minimum_order_quantity,
          po.discount_percentage,
          (po.unit_price * (1 - po.discount_percentage / 100)) as final_price
        FROM suppliers s
        JOIN purchase_orders po ON s.id = po.supplier_id
        JOIN purchase_order_details pod ON po.id = pod.purchase_order_id
        JOIN products p ON pod.product_id = p.id
        WHERE po.purchase_date BETWEEN ? AND ?
        ${categoryFilter}
        GROUP BY s.id, p.id, po.unit_price, po.purchase_date
        ORDER BY p.product_name, po.unit_price
      `

      const args = params.category && params.category !== 'all'
        ? [params.fromDate, params.toDate, params.category]
        : [params.fromDate, params.toDate]

      const supplierPrices = this.db.prepare(query).all(...args)

      // تجميع البيانات حسب المنتج
      const productComparisons = supplierPrices.reduce((acc: any, item: any) => {
        const productKey = item.product_id
        if (!acc[productKey]) {
          acc[productKey] = {
            product_id: item.product_id,
            product_name: item.product_name,
            product_code: item.product_code,
            category: item.category,
            suppliers: []
          }
        }
        acc[productKey].suppliers.push(item)
        return acc
      }, {})

      // حساب الإحصائيات لكل منتج
      const products = Object.values(productComparisons).map((product: any) => {
        const prices = product.suppliers.map((s: any) => s.final_price)
        const lowestPrice = Math.min(...prices)
        const highestPrice = Math.max(...prices)
        const averagePrice = prices.reduce((sum: number, price: number) => sum + price, 0) / prices.length
        const priceVariance = ((highestPrice - lowestPrice) / lowestPrice) * 100

        // ترتيب الموردين حسب السعر
        const sortedSuppliers = product.suppliers
          .map((supplier: any, index: number) => ({
            ...supplier,
            price_rank: 0,
            price_difference_from_lowest: supplier.final_price - lowestPrice,
            price_difference_percentage: lowestPrice > 0 ? ((supplier.final_price - lowestPrice) / lowestPrice) * 100 : 0
          }))
          .sort((a: any, b: any) => a.final_price - b.final_price)
          .map((supplier: any, index: number) => ({
            ...supplier,
            price_rank: index + 1
          }))

        const bestSupplier = sortedSuppliers[0]?.supplier_name || 'غير محدد'
        const recommendedSupplier = bestSupplier // يمكن تحسين هذا بناءً على معايير أخرى
        const potentialSavings = sortedSuppliers.length > 1 ?
          (sortedSuppliers[sortedSuppliers.length - 1].final_price - lowestPrice) : 0

        return {
          ...product,
          suppliers: sortedSuppliers,
          lowest_price: lowestPrice,
          highest_price: highestPrice,
          average_price: averagePrice,
          price_variance: priceVariance,
          best_supplier: bestSupplier,
          recommended_supplier: recommendedSupplier,
          potential_savings: potentialSavings
        }
      })

      // حساب الإحصائيات العامة
      const totalProducts = products.length
      const uniqueSuppliers = new Set(supplierPrices.map((item: any) => item.supplier_id))
      const totalSuppliers = uniqueSuppliers.size

      const averagePriceVariance = products.length > 0 ?
        products.reduce((sum: number, product: any) => sum + product.price_variance, 0) / products.length : 0

      const totalPotentialSavings = products.reduce((sum: number, product: any) => sum + product.potential_savings, 0)

      // أفضل وأسوأ الموردين
      const supplierPerformance = Array.from(uniqueSuppliers).map((supplierId: any) => {
        const supplierItems = supplierPrices.filter((item: any) => item.supplier_id === supplierId)
        const supplierName = supplierItems[0]?.supplier_name || 'غير محدد'

        let bestPriceCount = 0
        let worstPriceCount = 0

        products.forEach((product: any) => {
          const supplierInProduct = product.suppliers.find((s: any) => s.supplier_id === supplierId)
          if (supplierInProduct) {
            if (supplierInProduct.price_rank === 1) bestPriceCount++
            if (supplierInProduct.price_rank === product.suppliers.length) worstPriceCount++
          }
        })

        return {
          supplier_id: supplierId,
          supplier_name: supplierName,
          best_price_count: bestPriceCount,
          worst_price_count: worstPriceCount,
          competitiveness_score: totalProducts > 0 ? (bestPriceCount / totalProducts) * 100 : 0
        }
      })

      const bestValueSuppliers = supplierPerformance
        .filter((s: any) => s.best_price_count > 0)
        .sort((a: any, b: any) => b.best_price_count - a.best_price_count)
        .slice(0, 3)
        .map((s: any) => s.supplier_name)

      const mostExpensiveSuppliers = supplierPerformance
        .filter((s: any) => s.worst_price_count > 0)
        .sort((a: any, b: any) => b.worst_price_count - a.worst_price_count)
        .slice(0, 3)
        .map((s: any) => s.supplier_name)

      return {
        success: true,
        data: {
          period: {
            from_date: params.fromDate,
            to_date: params.toDate
          },
          products: products,
          summary: {
            total_products: totalProducts,
            total_suppliers: totalSuppliers,
            average_price_variance: averagePriceVariance,
            total_potential_savings: totalPotentialSavings,
            best_value_suppliers: bestValueSuppliers,
            most_expensive_suppliers: mostExpensiveSuppliers
          },
          analysis: {
            price_trends: [], // يمكن إضافة تحليل الاتجاهات
            supplier_performance: supplierPerformance.map((s: any) => ({
              supplier: s.supplier_name,
              competitiveness_score: s.competitiveness_score,
              reliability_score: 85, // يمكن حسابه من بيانات التسليم
              overall_rating: (s.competitiveness_score + 85) / 2
            }))
          }
        }
      }
    } catch (error) {
      console.error('خطأ في تقرير مقارنة أسعار الموردين:', error)
      return {
        success: false,
        message: 'فشل في تحميل تقرير مقارنة أسعار الموردين'
      }
    }
  }

  // تقرير جودة الموردين
  public getSupplierQualityReport(params: {
    fromDate: string
    toDate: string
    sortBy: string
    riskFilter: string
  }): any {
    try {
      const suppliers = this.db.prepare(`
        SELECT
          s.id as supplier_id,
          s.supplier_name,
          s.supplier_code,
          s.contact_person,
          s.phone,
          s.email,
          s.payment_terms,
          s.delivery_time as promised_delivery_time,
          s.certification_status,
          s.last_audit_date,
          s.next_audit_date,
          COUNT(DISTINCT po.id) as total_orders,
          COALESCE(SUM(po.total_amount), 0) as total_amount,
          COUNT(CASE WHEN po.delivery_status = 'on_time' THEN 1 END) as on_time_deliveries,
          COUNT(CASE WHEN po.delivery_status = 'late' THEN 1 END) as late_deliveries,
          COUNT(CASE WHEN qr.issue_type = 'quality' THEN 1 END) as quality_issues,
          COUNT(CASE WHEN qr.issue_type = 'return' THEN 1 END) as returns,
          COUNT(CASE WHEN qr.issue_type = 'complaint' THEN 1 END) as customer_complaints,
          COUNT(CASE WHEN ca.status = 'completed' THEN 1 END) as corrective_actions,
          AVG(po.actual_delivery_time) as average_delivery_time
        FROM suppliers s
        LEFT JOIN purchase_orders po ON s.id = po.supplier_id
          AND po.order_date BETWEEN ? AND ?
        LEFT JOIN quality_reports qr ON s.id = qr.supplier_id
          AND qr.report_date BETWEEN ? AND ?
        LEFT JOIN corrective_actions ca ON s.id = ca.supplier_id
          AND ca.created_date BETWEEN ? AND ?
        GROUP BY s.id, s.supplier_name, s.supplier_code, s.contact_person,
                 s.phone, s.email, s.payment_terms, s.delivery_time,
                 s.certification_status, s.last_audit_date, s.next_audit_date
        HAVING total_orders > 0
      `).all(params.fromDate, params.toDate, params.fromDate, params.toDate, params.fromDate, params.toDate)

      // حساب المقاييس لكل مورد
      const suppliersWithMetrics = suppliers.map((supplier: any) => {
        // معدل التسليم في الوقت المحدد
        const onTimeDeliveryRate = supplier.total_orders > 0 ?
          (supplier.on_time_deliveries / supplier.total_orders) * 100 : 0

        // معدل العيوب
        const defectRate = supplier.total_orders > 0 ?
          (supplier.quality_issues / supplier.total_orders) * 100 : 0

        // نقاط الجودة (من 100)
        let qualityScore = 100
        qualityScore -= defectRate * 2 // خصم نقطتين لكل 1% عيوب
        qualityScore -= Math.max(0, (100 - onTimeDeliveryRate)) * 0.5 // خصم للتأخير
        qualityScore = Math.max(0, Math.min(100, qualityScore))

        // تقييم المنافسة في الأسعار (تقدير)
        const priceCompetitiveness = 75 // يمكن حسابه من مقارنة الأسعار

        // تقييم التواصل (تقدير)
        const communicationScore = 80 // يمكن حسابه من تقييمات المستخدمين

        // التقييم الإجمالي
        const overallRating = (qualityScore * 0.4 + onTimeDeliveryRate * 0.3 + priceCompetitiveness * 0.2 + communicationScore * 0.1) / 100 * 5

        // تباين وقت التسليم
        const deliveryVariance = supplier.average_delivery_time && supplier.promised_delivery_time ?
          Math.abs(supplier.average_delivery_time - supplier.promised_delivery_time) : 0

        // امتثال شروط الدفع (تقدير)
        const paymentTermsCompliance = 90 // يمكن حسابه من سجلات الدفع

        // اتجاه التحسن (تقدير بناءً على البيانات التاريخية)
        let improvementTrend: 'improving' | 'declining' | 'stable' = 'stable'
        if (qualityScore > 80 && onTimeDeliveryRate > 90) improvementTrend = 'improving'
        else if (qualityScore < 60 || onTimeDeliveryRate < 70) improvementTrend = 'declining'

        // مستوى المخاطر
        let riskLevel: 'low' | 'medium' | 'high' = 'low'
        if (defectRate > 5 || onTimeDeliveryRate < 70 || supplier.quality_issues > 5) riskLevel = 'high'
        else if (defectRate > 2 || onTimeDeliveryRate < 85 || supplier.quality_issues > 2) riskLevel = 'medium'

        // التوصية
        let recommendation = 'مورد موثوق - استمرار التعامل'
        if (riskLevel === 'high') recommendation = 'يتطلب تحسينات فورية أو إعادة تقييم'
        else if (riskLevel === 'medium') recommendation = 'مراقبة الأداء وتطبيق إجراءات تحسين'

        return {
          ...supplier,
          on_time_delivery_rate: onTimeDeliveryRate,
          defect_rate: defectRate,
          quality_score: qualityScore,
          price_competitiveness: priceCompetitiveness,
          communication_score: communicationScore,
          overall_rating: overallRating,
          delivery_variance: deliveryVariance,
          payment_terms_compliance: paymentTermsCompliance,
          improvement_trend: improvementTrend,
          risk_level: riskLevel,
          recommendation: recommendation
        }
      })

      // تطبيق فلتر المخاطر
      let filteredSuppliers = suppliersWithMetrics
      if (params.riskFilter !== 'all') {
        filteredSuppliers = suppliersWithMetrics.filter((s: any) => s.risk_level === params.riskFilter)
      }

      // ترتيب حسب المعيار المطلوب
      switch (params.sortBy) {
        case 'quality_score':
          filteredSuppliers.sort((a: any, b: any) => b.quality_score - a.quality_score)
          break
        case 'on_time_delivery_rate':
          filteredSuppliers.sort((a: any, b: any) => b.on_time_delivery_rate - a.on_time_delivery_rate)
          break
        case 'defect_rate':
          filteredSuppliers.sort((a: any, b: any) => a.defect_rate - b.defect_rate)
          break
        default: // overall_rating
          filteredSuppliers.sort((a: any, b: any) => b.overall_rating - a.overall_rating)
      }

      // حساب الإحصائيات العامة
      const totalSuppliers = suppliersWithMetrics.length
      const averageQualityScore = totalSuppliers > 0 ?
        suppliersWithMetrics.reduce((sum: number, s: any) => sum + s.quality_score, 0) / totalSuppliers : 0

      const overallOnTimeRate = suppliersWithMetrics.length > 0 ?
        suppliersWithMetrics.reduce((sum: number, s: any) => sum + s.on_time_delivery_rate, 0) / suppliersWithMetrics.length : 0

      const totalQualityIssues = suppliersWithMetrics.reduce((sum: number, s: any) => sum + s.quality_issues, 0)

      const certificationCompliantSuppliers = suppliersWithMetrics.filter((s: any) =>
        s.certification_status === 'certified' || s.certification_status === 'valid').length
      const certificationComplianceRate = totalSuppliers > 0 ?
        (certificationCompliantSuppliers / totalSuppliers) * 100 : 0

      // أفضل وأسوأ الموردين
      const topPerformers = suppliersWithMetrics
        .filter((s: any) => s.overall_rating >= 4.0)
        .slice(0, 5)
        .map((s: any) => s.supplier_name)

      const poorPerformers = suppliersWithMetrics
        .filter((s: any) => s.overall_rating < 2.5)
        .slice(0, 5)
        .map((s: any) => s.supplier_name)

      // تحليل الاتجاهات
      const improvingSuppliers = suppliersWithMetrics.filter((s: any) => s.improvement_trend === 'improving').length
      const decliningSuppliers = suppliersWithMetrics.filter((s: any) => s.improvement_trend === 'declining').length
      const stableSuppliers = suppliersWithMetrics.filter((s: any) => s.improvement_trend === 'stable').length

      // توزيع المخاطر
      const lowRiskSuppliers = suppliersWithMetrics.filter((s: any) => s.risk_level === 'low').length
      const mediumRiskSuppliers = suppliersWithMetrics.filter((s: any) => s.risk_level === 'medium').length
      const highRiskSuppliers = suppliersWithMetrics.filter((s: any) => s.risk_level === 'high').length

      // فئات الأداء
      const excellentPerformers = suppliersWithMetrics.filter((s: any) => s.overall_rating >= 4.5).length
      const goodPerformers = suppliersWithMetrics.filter((s: any) => s.overall_rating >= 3.5 && s.overall_rating < 4.5).length
      const averagePerformers = suppliersWithMetrics.filter((s: any) => s.overall_rating >= 2.5 && s.overall_rating < 3.5).length
      const poorPerformersCount = suppliersWithMetrics.filter((s: any) => s.overall_rating < 2.5).length

      return {
        success: true,
        data: {
          period: {
            from_date: params.fromDate,
            to_date: params.toDate
          },
          suppliers: filteredSuppliers,
          summary: {
            total_suppliers: totalSuppliers,
            average_quality_score: averageQualityScore,
            top_performers: topPerformers,
            poor_performers: poorPerformers,
            total_quality_issues: totalQualityIssues,
            overall_on_time_rate: overallOnTimeRate,
            certification_compliance_rate: certificationComplianceRate
          },
          analysis: {
            quality_trends: {
              improving_suppliers: improvingSuppliers,
              declining_suppliers: decliningSuppliers,
              stable_suppliers: stableSuppliers
            },
            risk_distribution: {
              low_risk: lowRiskSuppliers,
              medium_risk: mediumRiskSuppliers,
              high_risk: highRiskSuppliers
            },
            performance_categories: {
              excellent: excellentPerformers,
              good: goodPerformers,
              average: averagePerformers,
              poor: poorPerformersCount
            }
          }
        }
      }
    } catch (error) {
      console.error('خطأ في تقرير جودة الموردين:', error)
      return {
        success: false,
        message: 'فشل في تحميل تقرير جودة الموردين'
      }
    }
  }

}
