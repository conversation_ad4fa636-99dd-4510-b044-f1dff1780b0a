import { SimpleDatabaseService } from './SimpleDatabaseService'
import { ClosingValidationService } from './ClosingValidationService'
import { ClosingBackupService } from './ClosingBackupService'
import { Logger } from '../utils/logger'

export interface ApiResponse {
  success: boolean
  message?: string
  data?: any
}

export interface FiscalPeriod {
  id: number
  period_name: string
  period_type: 'monthly' | 'quarterly' | 'semi_annual' | 'annual'
  start_date: string
  end_date: string
  status: 'open' | 'closed' | 'locked'
  closing_date?: string
  closed_by?: number
  reopening_date?: string
  reopened_by?: number
  is_current: boolean
  notes?: string
  created_at: string
  updated_at?: string
}

export interface ClosingEntry {
  id: number
  fiscal_period_id: number
  entry_type: 'revenue_closing' | 'expense_closing' | 'profit_transfer'
  journal_entry_id: number
  amount: number
  created_at: string
}

export interface CarriedForwardBalance {
  id: number
  fiscal_period_id: number
  account_id: number
  opening_balance: number
  closing_balance: number
  carried_forward_amount: number
  balance_type: 'debit' | 'credit'
  created_at: string
}

export interface PeriodClosingAudit {
  id: number
  fiscal_period_id: number
  action_type: 'close_attempt' | 'close_success' | 'reopen' | 'modify_attempt'
  user_id: number
  action_details?: string
  ip_address?: string
  user_agent?: string
  success: boolean
  error_message?: string
  created_at: string
}

export class FiscalPeriodService {
  private db!: any
  private static instance: FiscalPeriodService

  constructor() {
    // لا نقوم بتهيئة قاعدة البيانات هنا لتجنب مشاكل التهيئة
  }

  /**
   * الحصول على قاعدة البيانات مع التأكد من التهيئة
   */
  private getDatabase(): any {
    if (!this.db) {
      try {
        const databaseService = SimpleDatabaseService.getInstance()
        this.db = databaseService.getDatabase()
        this.initializeTables(this.db)
      } catch (error) {
        Logger.error('FiscalPeriodService', 'فشل في تهيئة قاعدة البيانات:', error)
        throw new Error('قاعدة البيانات غير متاحة')
      }
    }
    return this.db
  }

  public static getInstance(): FiscalPeriodService {
    if (!FiscalPeriodService.instance) {
      FiscalPeriodService.instance = new FiscalPeriodService()
    }
    return FiscalPeriodService.instance
  }

  /**
   * إنشاء جداول نظام الإقفال المحاسبي
   */
  private initializeTables(db: any): void {
    try {
      // جدول الفترات المالية
      db.exec(`
        CREATE TABLE IF NOT EXISTS fiscal_periods (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          period_name TEXT NOT NULL,
          period_type TEXT NOT NULL CHECK (period_type IN ('monthly', 'quarterly', 'semi_annual', 'annual')),
          start_date DATE NOT NULL,
          end_date DATE NOT NULL,
          status TEXT DEFAULT 'open' CHECK (status IN ('open', 'closed', 'locked')),
          closing_date DATETIME,
          closed_by INTEGER,
          reopening_date DATETIME,
          reopened_by INTEGER,
          is_current BOOLEAN DEFAULT 0,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (closed_by) REFERENCES users (id),
          FOREIGN KEY (reopened_by) REFERENCES users (id)
        )
      `)

      // جدول قيود الإقفال
      db.exec(`
        CREATE TABLE IF NOT EXISTS closing_entries (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          fiscal_period_id INTEGER NOT NULL,
          entry_type TEXT NOT NULL CHECK (entry_type IN ('revenue_closing', 'expense_closing', 'profit_transfer')),
          journal_entry_id INTEGER NOT NULL,
          amount DECIMAL(15,2) NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (fiscal_period_id) REFERENCES fiscal_periods (id),
          FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id)
        )
      `)

      // جدول الحسابات العالقة المرحلة
      db.exec(`
        CREATE TABLE IF NOT EXISTS carried_forward_balances (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          fiscal_period_id INTEGER NOT NULL,
          account_id INTEGER NOT NULL,
          opening_balance DECIMAL(15,2) NOT NULL,
          closing_balance DECIMAL(15,2) NOT NULL,
          carried_forward_amount DECIMAL(15,2) NOT NULL,
          balance_type TEXT NOT NULL CHECK (balance_type IN ('debit', 'credit')),
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (fiscal_period_id) REFERENCES fiscal_periods (id),
          FOREIGN KEY (account_id) REFERENCES chart_of_accounts (id)
        )
      `)

      // جدول سجل عمليات الإقفال
      db.exec(`
        CREATE TABLE IF NOT EXISTS period_closing_audit (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          fiscal_period_id INTEGER NOT NULL,
          action_type TEXT NOT NULL CHECK (action_type IN ('close_attempt', 'close_success', 'reopen', 'modify_attempt')),
          user_id INTEGER NOT NULL,
          action_details TEXT,
          ip_address TEXT,
          user_agent TEXT,
          success BOOLEAN NOT NULL,
          error_message TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (fiscal_period_id) REFERENCES fiscal_periods (id),
          FOREIGN KEY (user_id) REFERENCES users (id)
        )
      `)

      // جدول حماية البيانات المقفلة
      db.exec(`
        CREATE TABLE IF NOT EXISTS closed_period_protection (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          fiscal_period_id INTEGER NOT NULL,
          table_name TEXT NOT NULL,
          record_id INTEGER NOT NULL,
          protection_level TEXT NOT NULL CHECK (protection_level IN ('read_only', 'no_access', 'admin_only')),
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (fiscal_period_id) REFERENCES fiscal_periods (id)
        )
      `)

      // تحديث جدول النسخ الاحتياطية لدعم الإقفال (مع التحقق من وجود الأعمدة)
      this.addColumnIfNotExists(db, 'backups', 'fiscal_period_id', 'INTEGER')
      this.addColumnIfNotExists(db, 'backups', 'is_period_closing_backup', 'BOOLEAN DEFAULT 0')
      this.addColumnIfNotExists(db, 'backups', 'closing_type', 'TEXT')

      // إنشاء الفهارس
      this.createIndexes(db)

      // إضافة حسابات الإقفال لدليل الحسابات
      this.addClosingAccounts(db)

      Logger.info('FiscalPeriodService', '✅ تم إنشاء جداول نظام الإقفال المحاسبي بنجاح')

    } catch (error) {
      Logger.error('FiscalPeriodService', '❌ خطأ في إنشاء جداول نظام الإقفال:', error)
    }
  }

  /**
   * إضافة عمود إلى جدول إذا لم يكن موجوداً
   */
  private addColumnIfNotExists(db: any, tableName: string, columnName: string, columnType: string): void {
    try {
      // محاولة إضافة العمود
      db.exec(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnType}`)
      Logger.info('FiscalPeriodService', `✅ تم إضافة العمود ${columnName} إلى جدول ${tableName}`)
    } catch (error) {
      // العمود موجود بالفعل أو خطأ آخر
      const errorMessage = error instanceof Error ? error.message : String(error)
      if (errorMessage.includes('duplicate column name')) {
        Logger.info('FiscalPeriodService', `⚠️ العمود ${columnName} موجود بالفعل في جدول ${tableName}`)
      } else {
        Logger.error('FiscalPeriodService', `❌ خطأ في إضافة العمود ${columnName}:`, error)
      }
    }
  }

  /**
   * إنشاء الفهارس المطلوبة
   */
  private createIndexes(db: any): void {
    try {
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_fiscal_periods_dates ON fiscal_periods (start_date, end_date)',
        'CREATE INDEX IF NOT EXISTS idx_fiscal_periods_status ON fiscal_periods (status)',
        'CREATE INDEX IF NOT EXISTS idx_fiscal_periods_current ON fiscal_periods (is_current)',
        'CREATE INDEX IF NOT EXISTS idx_closing_entries_period ON closing_entries (fiscal_period_id)',
        'CREATE INDEX IF NOT EXISTS idx_carried_forward_period ON carried_forward_balances (fiscal_period_id)',
        'CREATE INDEX IF NOT EXISTS idx_period_audit_period ON period_closing_audit (fiscal_period_id)',
        'CREATE INDEX IF NOT EXISTS idx_period_audit_user ON period_closing_audit (user_id)',
        'CREATE INDEX IF NOT EXISTS idx_protection_period ON closed_period_protection (fiscal_period_id)'
      ]

      indexes.forEach(indexSQL => {
        db.exec(indexSQL)
      })

      Logger.info('FiscalPeriodService', '✅ تم إنشاء فهارس نظام الإقفال بنجاح')
    } catch (error) {
      Logger.error('FiscalPeriodService', '❌ خطأ في إنشاء الفهارس:', error)
    }
  }

  /**
   * إضافة حسابات الإقفال لدليل الحسابات
   */
  private addClosingAccounts(db: any): void {
    try {
      const closingAccounts = [
        {
          account_code: '3001',
          account_name: 'الأرباح المحتجزة',
          account_type: 'equity',
          level: 1
        },
        {
          account_code: '3002',
          account_name: 'ملخص الدخل',
          account_type: 'equity',
          level: 1
        },
        {
          account_code: '9001',
          account_name: 'حساب الإقفال المؤقت',
          account_type: 'equity',
          level: 1
        }
      ]

      const insertStmt = db.prepare(`
        INSERT OR IGNORE INTO chart_of_accounts (account_code, account_name, account_type, level, is_active)
        VALUES (?, ?, ?, ?, 1)
      `)

      closingAccounts.forEach(account => {
        insertStmt.run(
          account.account_code,
          account.account_name,
          account.account_type,
          account.level
        )
      })

      Logger.info('FiscalPeriodService', '✅ تم إضافة حسابات الإقفال لدليل الحسابات')
    } catch (error) {
      Logger.error('FiscalPeriodService', '❌ خطأ في إضافة حسابات الإقفال:', error)
    }
  }

  /**
   * الحصول على جميع الفترات المالية
   */
  public async getFiscalPeriods(): Promise<ApiResponse> {
    try {
      const db = this.getDatabase()

      // استعلام مبسط أولاً للتأكد من وجود البيانات
      const periods = db.prepare(`
        SELECT * FROM fiscal_periods
        ORDER BY start_date DESC
      `).all()

      Logger.info('FiscalPeriodService', `تم جلب ${periods.length} فترة مالية`)
      return { success: true, data: periods }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في جلب الفترات المالية:', error)
      return { success: false, message: 'حدث خطأ في جلب الفترات المالية' }
    }
  }

  /**
   * الحصول على الفترة المالية الحالية
   */
  public async getCurrentFiscalPeriod(): Promise<ApiResponse> {
    try {
      const db = this.getDatabase()
      const currentPeriod = db.prepare(`
        SELECT * FROM fiscal_periods
        WHERE is_current = 1 AND status = 'open'
        LIMIT 1
      `).get()

      if (!currentPeriod) {
        return { success: false, message: 'لا توجد فترة مالية مفتوحة حالياً' }
      }

      return { success: true, data: currentPeriod }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في جلب الفترة المالية الحالية:', error)
      return { success: false, message: 'حدث خطأ في جلب الفترة المالية الحالية' }
    }
  }

  /**
   * إنشاء فترة مالية جديدة
   */
  public async createFiscalPeriod(periodData: {
    period_name: string
    period_type: 'monthly' | 'quarterly' | 'semi_annual' | 'annual'
    start_date: string
    end_date: string
    is_current?: boolean
    notes?: string
  }, userId?: number): Promise<ApiResponse> {
    try {
      Logger.info('FiscalPeriodService', 'بدء إنشاء فترة مالية جديدة:', periodData)

      // الحصول على قاعدة البيانات مع التأكد من التهيئة
      const db = this.getDatabase()

      // التحقق من عدم تداخل الفترات
      const overlappingPeriod = db.prepare(`
        SELECT id FROM fiscal_periods
        WHERE (start_date <= ? AND end_date >= ?)
           OR (start_date <= ? AND end_date >= ?)
           OR (start_date >= ? AND end_date <= ?)
      `).get(
        periodData.start_date, periodData.start_date,
        periodData.end_date, periodData.end_date,
        periodData.start_date, periodData.end_date
      )

      if (overlappingPeriod) {
        return { success: false, message: 'توجد فترة مالية متداخلة مع التواريخ المحددة' }
      }

      // إذا كانت الفترة الجديدة حالية، إلغاء الفترة الحالية السابقة
      if (periodData.is_current) {
        db.prepare(`
          UPDATE fiscal_periods SET is_current = 0, updated_at = CURRENT_TIMESTAMP
          WHERE is_current = 1
        `).run()
      }

      // إدراج الفترة الجديدة
      Logger.info('FiscalPeriodService', 'البيانات المرسلة للإدراج:', {
        period_name: periodData.period_name,
        period_type: periodData.period_type,
        start_date: periodData.start_date,
        end_date: periodData.end_date,
        is_current: periodData.is_current ? 1 : 0,
        notes: periodData.notes || null
      })

      const result = db.prepare(`
        INSERT INTO fiscal_periods (
          period_name, period_type, start_date, end_date, is_current, notes
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        periodData.period_name,
        periodData.period_type,
        periodData.start_date,
        periodData.end_date,
        periodData.is_current ? 1 : 0,
        periodData.notes || null
      )

      if (result.changes === 0) {
        Logger.error('FiscalPeriodService', 'فشل في إدراج الفترة المالية - لم يتم تغيير أي صف')
        return { success: false, message: 'فشل في إنشاء الفترة المالية' }
      }

      const newPeriodId = result.lastInsertRowid as number
      Logger.info('FiscalPeriodService', `تم إنشاء فترة مالية جديدة بمعرف: ${newPeriodId}`)

      // تسجيل العملية في سجل التدقيق
      await this.logAuditAction(
        newPeriodId,
        'create_period',
        userId || 0,
        { action: 'create_period', period_data: periodData },
        true
      )

      // حفظ قاعدة البيانات
      SimpleDatabaseService.getInstance().saveDatabase()
      Logger.info('FiscalPeriodService', 'تم حفظ قاعدة البيانات بنجاح')

      return {
        success: true,
        message: 'تم إنشاء الفترة المالية بنجاح',
        data: { id: newPeriodId }
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في إنشاء الفترة المالية:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الفترة المالية' }
    }
  }

  /**
   * تحديث فترة مالية
   */
  public async updateFiscalPeriod(
    periodId: number,
    updateData: Partial<FiscalPeriod>,
    userId?: number
  ): Promise<ApiResponse> {
    try {
      // الحصول على قاعدة البيانات مع التأكد من التهيئة
      const db = this.getDatabase()

      // التحقق من وجود الفترة
      const existingPeriod = db.prepare(`
        SELECT * FROM fiscal_periods WHERE id = ?
      `).get(periodId) as FiscalPeriod

      if (!existingPeriod) {
        return { success: false, message: 'الفترة المالية غير موجودة' }
      }

      // منع تعديل الفترات المقفلة
      if (existingPeriod.status === 'closed' || existingPeriod.status === 'locked') {
        return { success: false, message: 'لا يمكن تعديل فترة مالية مقفلة' }
      }

      // بناء استعلام التحديث
      const updateFields = []
      const updateValues = []

      Object.entries(updateData).forEach(([key, value]) => {
        if (key !== 'id' && value !== undefined) {
          updateFields.push(`${key} = ?`)
          updateValues.push(value)
        }
      })

      if (updateFields.length === 0) {
        return { success: false, message: 'لا توجد بيانات للتحديث' }
      }

      updateFields.push('updated_at = CURRENT_TIMESTAMP')
      updateValues.push(periodId)

      const result = db.prepare(`
        UPDATE fiscal_periods
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `).run(...updateValues)

      if (result.changes === 0) {
        return { success: false, message: 'فشل في تحديث الفترة المالية' }
      }

      // تسجيل العملية في سجل التدقيق
      await this.logAuditAction(
        periodId,
        'modify_attempt',
        userId || 0,
        { action: 'update_period', update_data: updateData },
        true
      )

      // حفظ قاعدة البيانات
      SimpleDatabaseService.getInstance().saveDatabase()

      return { success: true, message: 'تم تحديث الفترة المالية بنجاح' }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في تحديث الفترة المالية:', error)
      return { success: false, message: 'حدث خطأ في تحديث الفترة المالية' }
    }
  }

  /**
   * تسجيل عملية في سجل التدقيق
   */
  private async logAuditAction(
    fiscalPeriodId: number,
    actionType: string,
    userId: number,
    actionDetails: any,
    success: boolean,
    errorMessage?: string
  ): Promise<void> {
    try {
      const db = this.getDatabase()
      db.prepare(`
        INSERT INTO period_closing_audit (
          fiscal_period_id, action_type, user_id, action_details, success, error_message
        ) VALUES (?, ?, ?, ?, ?, ?)
      `).run(
        fiscalPeriodId,
        actionType,
        userId,
        JSON.stringify(actionDetails),
        success ? 1 : 0,
        errorMessage || null
      )
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في تسجيل عملية التدقيق:', error)
    }
  }

  /**
   * التحقق من إمكانية إقفال الفترة المالية
   */
  public async validatePeriodClosing(periodId: number): Promise<ApiResponse> {
    try {
      const db = this.getDatabase()
      const period = db.prepare(`
        SELECT * FROM fiscal_periods WHERE id = ?
      `).get(periodId) as FiscalPeriod

      if (!period) {
        return { success: false, message: 'الفترة المالية غير موجودة' }
      }

      if (period.status !== 'open') {
        return { success: false, message: 'الفترة المالية مقفلة بالفعل' }
      }

      // استخدام خدمة التحقق المتخصصة
      const validationService = ClosingValidationService.getInstance()
      const validation = await validationService.validateForClosing(period.start_date, period.end_date)

      return {
        success: validation.isValid,
        message: validation.isValid ? 'يمكن إقفال الفترة المالية' : 'يوجد مشاكل تمنع الإقفال',
        data: {
          errors: validation.errors,
          warnings: validation.warnings,
          details: validation.details,
          canClose: validation.isValid
        }
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في التحقق من إمكانية الإقفال:', error)
      return { success: false, message: 'حدث خطأ في التحقق من إمكانية الإقفال' }
    }
  }

  /**
   * التحقق من توازن ميزان المراجعة
   */
  private async checkTrialBalance(startDate: string, endDate: string): Promise<{
    balanced: boolean
    totalDebits: number
    totalCredits: number
    difference: number
  }> {
    try {
      const db = this.getDatabase()
      const balances = db.prepare(`
        SELECT
          SUM(jed.debit_amount) as total_debits,
          SUM(jed.credit_amount) as total_credits
        FROM journal_entry_details jed
        JOIN journal_entries je ON jed.entry_id = je.id
        WHERE je.entry_date BETWEEN ? AND ? AND je.status = 'posted'
      `).get(startDate, endDate) as { total_debits: number | null, total_credits: number | null }

      const totalDebits = balances.total_debits || 0
      const totalCredits = balances.total_credits || 0
      const difference = Math.abs(totalDebits - totalCredits)

      return {
        balanced: difference < 0.01, // تسامح بسيط للأخطاء العشرية
        totalDebits,
        totalCredits,
        difference
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في فحص ميزان المراجعة:', error)
      return { balanced: false, totalDebits: 0, totalCredits: 0, difference: 0 }
    }
  }

  /**
   * إقفال الفترة المالية
   */
  public async closeFiscalPeriod(periodId: number, userId: number): Promise<ApiResponse> {
    try {
      // تسجيل محاولة الإقفال
      await this.logAuditAction(periodId, 'close_attempt', userId, { action: 'start_closing' }, true)

      // التحقق من صحة البيانات
      const validation = await this.validatePeriodClosing(periodId)
      if (!validation.success) {
        await this.logAuditAction(periodId, 'close_attempt', userId, validation.data, false, validation.message)
        return validation
      }

      const db = this.getDatabase()
      const period = db.prepare(`
        SELECT * FROM fiscal_periods WHERE id = ?
      `).get(periodId) as FiscalPeriod

      if (!period) {
        return { success: false, message: 'الفترة المالية غير موجودة' }
      }

      // إنشاء نسخة احتياطية إجبارية
      const backupResult = await this.createClosingBackup(periodId, period.period_type)
      if (!backupResult.success) {
        await this.logAuditAction(periodId, 'close_attempt', userId, { error: 'backup_failed' }, false, backupResult.message)
        return backupResult
      }

      // تنفيذ عملية الإقفال في معاملة واحدة
      const closingResult = await this.executeClosingProcess(period, userId)
      if (!closingResult.success) {
        await this.logAuditAction(periodId, 'close_attempt', userId, closingResult.data, false, closingResult.message)
        return closingResult
      }

      // تحديث حالة الفترة إلى مقفلة
      db.prepare(`
        UPDATE fiscal_periods
        SET status = 'closed', closing_date = CURRENT_TIMESTAMP, closed_by = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).run(userId, periodId)

      // تسجيل نجاح الإقفال
      await this.logAuditAction(periodId, 'close_success', userId, closingResult.data, true)

      // حفظ قاعدة البيانات
      SimpleDatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم إقفال الفترة المالية بنجاح',
        data: closingResult.data
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في إقفال الفترة المالية:', error)
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
      await this.logAuditAction(periodId, 'close_attempt', userId, { error: errorMessage }, false, errorMessage)
      return { success: false, message: 'حدث خطأ في إقفال الفترة المالية' }
    }
  }

  /**
   * إنشاء نسخة احتياطية خاصة بالإقفال
   */
  private async createClosingBackup(periodId: number, closingType: string): Promise<ApiResponse> {
    try {
      // استخدام خدمة النسخ الاحتياطية المتخصصة
      const backupService = ClosingBackupService.getInstance()
      const backupResult = await backupService.createClosingBackup(
        periodId,
        closingType as any,
        `نسخة احتياطية للإقفال ${this.getClosingTypeArabic(closingType)}`
      )

      if (!backupResult.success) {
        return {
          success: false,
          message: backupResult.message || 'فشل في إنشاء النسخة الاحتياطية'
        }
      }

      return {
        success: true,
        message: 'تم إنشاء النسخة الاحتياطية بنجاح',
        data: backupResult.data
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في إنشاء النسخة الاحتياطية:', error)
      return { success: false, message: 'فشل في إنشاء النسخة الاحتياطية' }
    }
  }

  /**
   * ترجمة نوع الإقفال إلى العربية
   */
  private getClosingTypeArabic(closingType: string): string {
    const types = {
      monthly: 'شهري',
      quarterly: 'ربع سنوي',
      semi_annual: 'نصف سنوي',
      annual: 'سنوي'
    }
    return types[closingType as keyof typeof types] || closingType
  }

  /**
   * تنفيذ عملية الإقفال المحاسبي
   */
  private async executeClosingProcess(period: FiscalPeriod, userId: number): Promise<ApiResponse> {
    try {
      const closingData = {
        revenueClosing: 0,
        expenseClosing: 0,
        profitTransfer: 0,
        entriesCreated: [] as number[]
      }

      // 1. إقفال حسابات الإيرادات
      const revenueResult = await this.closeRevenueAccounts(period, userId)
      if (!revenueResult.success) {
        return revenueResult
      }
      closingData.revenueClosing = revenueResult.data.amount
      closingData.entriesCreated.push(revenueResult.data.entryId)

      // 2. إقفال حسابات المصروفات
      const expenseResult = await this.closeExpenseAccounts(period, userId)
      if (!expenseResult.success) {
        return expenseResult
      }
      closingData.expenseClosing = expenseResult.data.amount
      closingData.entriesCreated.push(expenseResult.data.entryId)

      // 3. ترحيل صافي الربح/الخسارة
      const profitLoss = closingData.revenueClosing - closingData.expenseClosing
      const profitResult = await this.transferProfitLoss(period, profitLoss, userId)
      if (!profitResult.success) {
        return profitResult
      }
      closingData.profitTransfer = profitLoss
      closingData.entriesCreated.push(profitResult.data.entryId)

      // 4. ترحيل الحسابات العالقة
      const carryForwardResult = await this.carryForwardBalances(period)
      if (!carryForwardResult.success) {
        return carryForwardResult
      }

      return {
        success: true,
        message: 'تم تنفيذ عملية الإقفال بنجاح',
        data: closingData
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في تنفيذ عملية الإقفال:', error)
      return { success: false, message: 'فشل في تنفيذ عملية الإقفال' }
    }
  }

  /**
   * إقفال حسابات الإيرادات
   */
  private async closeRevenueAccounts(period: FiscalPeriod, userId: number): Promise<ApiResponse> {
    try {
      const db = this.getDatabase()
      // حساب إجمالي الإيرادات
      const revenueTotal = db.prepare(`
        SELECT SUM(jed.credit_amount - jed.debit_amount) as total
        FROM journal_entry_details jed
        JOIN journal_entries je ON jed.entry_id = je.id
        JOIN chart_of_accounts coa ON jed.account_id = coa.id
        WHERE je.entry_date BETWEEN ? AND ?
          AND je.status = 'posted'
          AND coa.account_type = 'revenue'
      `).get(period.start_date, period.end_date) as { total: number | null }

      const totalRevenue = revenueTotal.total || 0

      if (totalRevenue === 0) {
        return { success: true, data: { amount: 0, entryId: null } }
      }

      // إنشاء قيد إقفال الإيرادات
      const entryResult = await this.createClosingEntry(
        period.id,
        'revenue_closing',
        'إقفال حسابات الإيرادات',
        [
          { account_code: '4001', debit: totalRevenue, credit: 0 }, // حساب الإيرادات
          { account_code: '3002', debit: 0, credit: totalRevenue }  // ملخص الدخل
        ],
        userId
      )

      if (!entryResult.success) {
        return entryResult
      }

      return {
        success: true,
        data: { amount: totalRevenue, entryId: entryResult.data.entryId }
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في إقفال حسابات الإيرادات:', error)
      return { success: false, message: 'فشل في إقفال حسابات الإيرادات' }
    }
  }

  /**
   * إقفال حسابات المصروفات
   */
  private async closeExpenseAccounts(period: FiscalPeriod, userId: number): Promise<ApiResponse> {
    try {
      const db = this.getDatabase()
      // حساب إجمالي المصروفات
      const expenseTotal = db.prepare(`
        SELECT SUM(jed.debit_amount - jed.credit_amount) as total
        FROM journal_entry_details jed
        JOIN journal_entries je ON jed.entry_id = je.id
        JOIN chart_of_accounts coa ON jed.account_id = coa.id
        WHERE je.entry_date BETWEEN ? AND ?
          AND je.status = 'posted'
          AND coa.account_type = 'expense'
      `).get(period.start_date, period.end_date) as { total: number | null }

      const totalExpense = expenseTotal.total || 0

      if (totalExpense === 0) {
        return { success: true, data: { amount: 0, entryId: null } }
      }

      // إنشاء قيد إقفال المصروفات
      const entryResult = await this.createClosingEntry(
        period.id,
        'expense_closing',
        'إقفال حسابات المصروفات',
        [
          { account_code: '3002', debit: totalExpense, credit: 0 }, // ملخص الدخل
          { account_code: '5001', debit: 0, credit: totalExpense }  // حساب المصروفات
        ],
        userId
      )

      if (!entryResult.success) {
        return entryResult
      }

      return {
        success: true,
        data: { amount: totalExpense, entryId: entryResult.data.entryId }
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في إقفال حسابات المصروفات:', error)
      return { success: false, message: 'فشل في إقفال حسابات المصروفات' }
    }
  }

  /**
   * ترحيل صافي الربح أو الخسارة
   */
  private async transferProfitLoss(period: FiscalPeriod, profitLoss: number, userId: number): Promise<ApiResponse> {
    try {
      if (profitLoss === 0) {
        return { success: true, data: { amount: 0, entryId: null } }
      }

      const isProfit = profitLoss > 0
      const amount = Math.abs(profitLoss)

      // إنشاء قيد ترحيل النتيجة
      const entryResult = await this.createClosingEntry(
        period.id,
        'profit_transfer',
        isProfit ? 'ترحيل صافي الربح' : 'ترحيل صافي الخسارة',
        isProfit ? [
          { account_code: '3002', debit: amount, credit: 0 },   // ملخص الدخل
          { account_code: '3001', debit: 0, credit: amount }    // الأرباح المحتجزة
        ] : [
          { account_code: '3001', debit: amount, credit: 0 },   // الأرباح المحتجزة
          { account_code: '3002', debit: 0, credit: amount }    // ملخص الدخل
        ],
        userId
      )

      if (!entryResult.success) {
        return entryResult
      }

      return {
        success: true,
        data: { amount: profitLoss, entryId: entryResult.data.entryId }
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في ترحيل صافي الربح/الخسارة:', error)
      return { success: false, message: 'فشل في ترحيل صافي الربح/الخسارة' }
    }
  }

  /**
   * ترحيل الحسابات العالقة للفترة الجديدة
   */
  private async carryForwardBalances(period: FiscalPeriod): Promise<ApiResponse> {
    try {
      const db = this.getDatabase()
      // جلب أرصدة الحسابات الدائمة (الأصول، الخصوم، حقوق الملكية)
      const permanentAccounts = db.prepare(`
        SELECT
          coa.id,
          coa.account_code,
          coa.account_name,
          coa.account_type,
          SUM(CASE WHEN coa.account_type IN ('asset', 'expense')
                   THEN jed.debit_amount - jed.credit_amount
                   ELSE jed.credit_amount - jed.debit_amount END) as balance
        FROM chart_of_accounts coa
        LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
        LEFT JOIN journal_entries je ON jed.entry_id = je.id
        WHERE coa.account_type IN ('asset', 'liability', 'equity')
          AND (je.entry_date IS NULL OR je.entry_date <= ?)
          AND (je.status IS NULL OR je.status = 'posted')
        GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
        HAVING balance != 0
      `).all(period.end_date)

      // حفظ الأرصدة المرحلة
      const insertStmt = db.prepare(`
        INSERT INTO carried_forward_balances (
          fiscal_period_id, account_id, opening_balance, closing_balance,
          carried_forward_amount, balance_type
        ) VALUES (?, ?, ?, ?, ?, ?)
      `)

      permanentAccounts.forEach((account: any) => {
        const balanceType = account.balance > 0 ? 'debit' : 'credit'
        const amount = Math.abs(account.balance)

        insertStmt.run(
          period.id,
          account.id,
          0, // سيتم تحديثه لاحقاً
          account.balance,
          amount,
          balanceType
        )
      })

      return {
        success: true,
        data: { accountsCarriedForward: permanentAccounts.length }
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في ترحيل الحسابات العالقة:', error)
      return { success: false, message: 'فشل في ترحيل الحسابات العالقة' }
    }
  }

  /**
   * إنشاء قيد إقفال
   */
  private async createClosingEntry(
    periodId: number,
    entryType: string,
    description: string,
    entries: Array<{ account_code: string; debit: number; credit: number }>,
    userId: number
  ): Promise<ApiResponse> {
    try {
      const db = this.getDatabase()
      // حساب إجمالي المدين والدائن
      const totalDebit = entries.reduce((sum, entry) => sum + entry.debit, 0)
      const totalCredit = entries.reduce((sum, entry) => sum + entry.credit, 0)

      if (Math.abs(totalDebit - totalCredit) > 0.01) {
        return { success: false, message: 'القيد غير متوازن' }
      }

      // توليد رقم القيد
      const entryNumber = await this.generateClosingEntryNumber(entryType)

      // إنشاء القيد الرئيسي
      const entryResult = db.prepare(`
        INSERT INTO journal_entries (
          entry_number, entry_date, description, reference_type, reference_id,
          total_debit, total_credit, status, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'posted', ?)
      `).run(
        entryNumber,
        new Date().toISOString().split('T')[0],
        description,
        'fiscal_period_closing',
        periodId,
        totalDebit,
        totalCredit,
        userId
      )

      const entryId = entryResult.lastInsertRowid as number

      // إنشاء تفاصيل القيد
      const detailStmt = db.prepare(`
        INSERT INTO journal_entry_details (
          entry_id, account_id, debit_amount, credit_amount, description
        ) VALUES (?, ?, ?, ?, ?)
      `)

      for (const entry of entries) {
        // البحث عن الحساب
        const account = db.prepare(`
          SELECT id FROM chart_of_accounts WHERE account_code = ?
        `).get(entry.account_code) as { id: number } | undefined

        if (!account) {
          throw new Error(`الحساب ${entry.account_code} غير موجود`)
        }

        detailStmt.run(
          entryId,
          account.id,
          entry.debit,
          entry.credit,
          description
        )
      }

      // تسجيل قيد الإقفال
      db.prepare(`
        INSERT INTO closing_entries (fiscal_period_id, entry_type, journal_entry_id, amount)
        VALUES (?, ?, ?, ?)
      `).run(periodId, entryType, entryId, totalDebit)

      return {
        success: true,
        data: { entryId, entryNumber, totalDebit, totalCredit }
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في إنشاء قيد الإقفال:', error)
      return { success: false, message: 'فشل في إنشاء قيد الإقفال' }
    }
  }

  /**
   * توليد رقم قيد الإقفال
   */
  private async generateClosingEntryNumber(entryType: string): Promise<string> {
    try {
      const db = this.getDatabase()
      const year = new Date().getFullYear()
      const prefix = entryType === 'revenue_closing' ? 'REV' :
                    entryType === 'expense_closing' ? 'EXP' : 'PFT'

      const lastEntry = db.prepare(`
        SELECT entry_number FROM journal_entries
        WHERE entry_number LIKE ?
        ORDER BY id DESC LIMIT 1
      `).get(`${prefix}-${year}-%`) as { entry_number: string } | undefined

      let nextNumber = 1
      if (lastEntry) {
        const match = lastEntry.entry_number.match(/(\d+)$/)
        if (match) {
          nextNumber = parseInt(match[1]) + 1
        }
      }

      return `${prefix}-${year}-${nextNumber.toString().padStart(4, '0')}`
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في توليد رقم قيد الإقفال:', error)
      return `CLO-${Date.now()}`
    }
  }

  /**
   * إعادة فتح فترة مالية مقفلة
   */
  public async reopenFiscalPeriod(periodId: number, userId: number, reason?: string): Promise<ApiResponse> {
    try {
      const db = this.getDatabase()
      const period = db.prepare(`
        SELECT * FROM fiscal_periods WHERE id = ?
      `).get(periodId) as FiscalPeriod

      if (!period) {
        return { success: false, message: 'الفترة المالية غير موجودة' }
      }

      if (period.status !== 'closed') {
        return { success: false, message: 'الفترة المالية ليست مقفلة' }
      }

      // حذف قيود الإقفال
      const closingEntries = db.prepare(`
        SELECT journal_entry_id FROM closing_entries WHERE fiscal_period_id = ?
      `).all(periodId) as Array<{ journal_entry_id: number }>

      for (const entry of closingEntries) {
        // حذف تفاصيل القيد
        db.prepare(`
          DELETE FROM journal_entry_details WHERE entry_id = ?
        `).run(entry.journal_entry_id)

        // حذف القيد الرئيسي
        db.prepare(`
          DELETE FROM journal_entries WHERE id = ?
        `).run(entry.journal_entry_id)
      }

      // حذف قيود الإقفال من الجدول المخصص
      db.prepare(`
        DELETE FROM closing_entries WHERE fiscal_period_id = ?
      `).run(periodId)

      // حذف الأرصدة المرحلة
      db.prepare(`
        DELETE FROM carried_forward_balances WHERE fiscal_period_id = ?
      `).run(periodId)

      // تحديث حالة الفترة
      db.prepare(`
        UPDATE fiscal_periods
        SET status = 'open', reopening_date = CURRENT_TIMESTAMP, reopened_by = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).run(userId, periodId)

      // تسجيل العملية في سجل التدقيق
      await this.logAuditAction(periodId, 'reopen', userId, { reason }, true)

      // حفظ قاعدة البيانات
      SimpleDatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: 'تم إعادة فتح الفترة المالية بنجاح'
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في إعادة فتح الفترة المالية:', error)
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
      await this.logAuditAction(periodId, 'reopen', userId, { error: errorMessage }, false, errorMessage)
      return { success: false, message: 'حدث خطأ في إعادة فتح الفترة المالية' }
    }
  }

  /**
   * الحصول على تقرير الإقفال للفترة المالية
   */
  public async getClosingReport(periodId: number): Promise<ApiResponse> {
    try {
      const db = this.getDatabase()
      const period = db.prepare(`
        SELECT * FROM fiscal_periods WHERE id = ?
      `).get(periodId) as FiscalPeriod

      if (!period) {
        return { success: false, message: 'الفترة المالية غير موجودة' }
      }

      // جلب قيود الإقفال
      const closingEntries = db.prepare(`
        SELECT ce.*, je.entry_number, je.description, je.entry_date
        FROM closing_entries ce
        JOIN journal_entries je ON ce.journal_entry_id = je.id
        WHERE ce.fiscal_period_id = ?
        ORDER BY ce.entry_type
      `).all(periodId)

      // جلب الأرصدة المرحلة
      const carriedForwardBalances = db.prepare(`
        SELECT cfb.*, coa.account_code, coa.account_name
        FROM carried_forward_balances cfb
        JOIN chart_of_accounts coa ON cfb.account_id = coa.id
        WHERE cfb.fiscal_period_id = ?
        ORDER BY coa.account_code
      `).all(periodId)

      // جلب سجل التدقيق
      const auditLog = db.prepare(`
        SELECT pca.*, u.full_name as user_name
        FROM period_closing_audit pca
        JOIN users u ON pca.user_id = u.id
        WHERE pca.fiscal_period_id = ?
        ORDER BY pca.created_at DESC
      `).all(periodId)

      return {
        success: true,
        data: {
          period,
          closingEntries,
          carriedForwardBalances,
          auditLog
        }
      }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في جلب تقرير الإقفال:', error)
      return { success: false, message: 'حدث خطأ في جلب تقرير الإقفال' }
    }
  }

  /**
   * التحقق من حماية البيانات للفترة المقفلة
   */
  public async checkDataProtection(periodId: number, tableName: string, recordId: number): Promise<boolean> {
    try {
      const db = this.getDatabase()
      const period = db.prepare(`
        SELECT status FROM fiscal_periods WHERE id = ?
      `).get(periodId) as { status: string } | undefined

      if (!period || period.status !== 'closed') {
        return false // الفترة غير مقفلة، لا حماية
      }

      // التحقق من وجود حماية خاصة
      const protection = db.prepare(`
        SELECT protection_level FROM closed_period_protection
        WHERE fiscal_period_id = ? AND table_name = ? AND record_id = ?
      `).get(periodId, tableName, recordId) as { protection_level: string } | undefined

      return protection !== undefined
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في التحقق من حماية البيانات:', error)
      return true // في حالة الخطأ، نفترض وجود حماية
    }
  }

  /**
   * إضافة حماية لبيانات معينة
   */
  public async addDataProtection(
    periodId: number,
    tableName: string,
    recordId: number,
    protectionLevel: 'read_only' | 'no_access' | 'admin_only'
  ): Promise<ApiResponse> {
    try {
      const db = this.getDatabase()
      db.prepare(`
        INSERT OR REPLACE INTO closed_period_protection (
          fiscal_period_id, table_name, record_id, protection_level
        ) VALUES (?, ?, ?, ?)
      `).run(periodId, tableName, recordId, protectionLevel)

      return { success: true, message: 'تم إضافة الحماية بنجاح' }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في إضافة حماية البيانات:', error)
      return { success: false, message: 'حدث خطأ في إضافة حماية البيانات' }
    }
  }

  /**
   * حذف فترة مالية (فقط إذا كانت فارغة ومفتوحة)
   */
  public async deleteFiscalPeriod(periodId: number, userId: number): Promise<ApiResponse> {
    try {
      const db = this.getDatabase()
      const period = db.prepare(`
        SELECT * FROM fiscal_periods WHERE id = ?
      `).get(periodId) as FiscalPeriod

      if (!period) {
        return { success: false, message: 'الفترة المالية غير موجودة' }
      }

      if (period.status !== 'open') {
        return { success: false, message: 'لا يمكن حذف فترة مالية مقفلة' }
      }

      // التحقق من وجود معاملات في الفترة
      const hasTransactions = db.prepare(`
        SELECT COUNT(*) as count FROM journal_entries
        WHERE entry_date BETWEEN ? AND ?
      `).get(period.start_date, period.end_date) as { count: number }

      if (hasTransactions.count > 0) {
        return { success: false, message: 'لا يمكن حذف فترة تحتوي على معاملات' }
      }

      // حذف الفترة
      db.prepare(`
        DELETE FROM fiscal_periods WHERE id = ?
      `).run(periodId)

      // تسجيل العملية
      await this.logAuditAction(periodId, 'modify_attempt', userId, { action: 'delete_period' }, true)

      // حفظ قاعدة البيانات
      SimpleDatabaseService.getInstance().saveDatabase()

      return { success: true, message: 'تم حذف الفترة المالية بنجاح' }
    } catch (error) {
      Logger.error('FiscalPeriodService', 'خطأ في حذف الفترة المالية:', error)
      return { success: false, message: 'حدث خطأ في حذف الفترة المالية' }
    }
  }
}
