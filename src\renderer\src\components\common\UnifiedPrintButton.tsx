import React, { useState, useEffect } from 'react'
import { Button, Dropdown, Modal, Form, Select, InputNumber, Switch, Space, App } from 'antd'
import { PrinterOutlined, SettingOutlined, EyeOutlined, FileTextOutlined } from '@ant-design/icons'
import { MasterPrintService, PrintData, EnhancedPrintOptions } from '../../services/MasterPrintService'
import { UnifiedPrintOptions } from '../../types/print'
import { usePrintSettings } from '../../hooks/usePrintSettings'

interface UnifiedPrintButtonProps {
  // البيانات المطلوبة للطباعة
  data: PrintData | (() => Promise<PrintData>) | (() => PrintData)
  
  // نوع المستند
  type: EnhancedPrintOptions['type']
  subType?: EnhancedPrintOptions['subType']

  // إعدادات المظهر
  title?: string
  buttonText?: string
  size?: 'small' | 'middle' | 'large'
  buttonType?: 'primary' | 'default' | 'dashed' | 'link' | 'text'
  icon?: React.ReactNode

  // إعدادات الوظائف
  showDropdown?: boolean
  showExportOptions?: boolean
  showSettings?: boolean
  disabled?: boolean

  // معرف المستند للتتبع
  _documentId?: string

  // إعدادات مخصصة
  customSettings?: Partial<UnifiedPrintOptions>
  
  // callbacks
  onBeforePrint?: () => void
  onAfterPrint?: () => void
  onError?: (error: string) => void
}

const UnifiedPrintButton: React.FC<UnifiedPrintButtonProps> = ({
  data,
  type,
  subType,
  title,
  buttonText = 'طباعة',
  size = 'middle',
  buttonType = 'primary',
  icon = <PrinterOutlined />,
  showDropdown = true,
  showExportOptions = true,
  showSettings = true,
  disabled = false,
  _documentId,
  customSettings = {},
  onBeforePrint,
  onAfterPrint,
  onError
}) => {
  const { message } = App.useApp()
  const [loading, setLoading] = useState(false)
  const [settingsVisible, setSettingsVisible] = useState(false)

  // استخدام الإعدادات المركزية
  const { settings: centralSettings, isReady, updateSettings } = usePrintSettings()

  const [printOptions, setPrintOptions] = useState<UnifiedPrintOptions>({
    type,
    subType,
    ...centralSettings,
    ...customSettings
  })

  // تحديث الخيارات عند تغيير الإعدادات المركزية
  useEffect(() => {
    if (centralSettings && isReady) {
      setPrintOptions(prev => ({
        ...prev,
        type,
        subType,
        ...centralSettings,
        ...customSettings
      }))
    }
  }, [centralSettings, isReady, type, subType])

  const printService = MasterPrintService.getInstance()

  // حل البيانات (سواء كانت مباشرة أو دالة)
  const resolveData = async (): Promise<PrintData> => {
    if (typeof data === 'function') {
      return await data()
    }
    return data
  }

  // الطباعة المباشرة
  const handleDirectPrint = async () => {
    try {
      setLoading(true)
      onBeforePrint?.()

      const resolvedData = await resolveData()
      if (!resolvedData) {
        throw new Error('لا توجد بيانات للطباعة')
      }

      await printService.print(resolvedData, printOptions)

      message.success('تم إرسال المستند للطباعة بنجاح')
      onAfterPrint?.()
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في الطباعة'
      message.error(errorMessage)
      onError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // المعاينة قبل الطباعة (بدون فتح نافذة طباعة)
  const handlePreview = async () => {
    try {
      setLoading(true)

      const resolvedData = await resolveData()
      if (!resolvedData) {
        throw new Error('لا توجد بيانات للمعاينة')
      }

      // استخدام المعاينة الجديدة التي لا تفتح نافذة طباعة تلقائياً
      await printService.previewOnly(resolvedData, printOptions)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في المعاينة'
      message.error(errorMessage)
      onError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // حفظ كـ PDF
  const handleSavePDF = async () => {
    try {
      setLoading(true)

      const resolvedData = await resolveData()
      if (!resolvedData) {
        throw new Error('لا توجد بيانات للحفظ')
      }

      await printService.saveAsPDF(resolvedData, printOptions)
      message.success('تم حفظ الملف بصيغة PDF')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في حفظ PDF'
      message.error(errorMessage)
      onError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // حفظ التغييرات في الإعدادات المركزية
  const handleSaveSettings = async () => {
    try {
      if (updateSettings) {
        await updateSettings(printOptions)
        message.success('تم حفظ الإعدادات بنجاح')
      }
      setSettingsVisible(false)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في حفظ الإعدادات'
      message.error(errorMessage)
    }
  }

  // قائمة الخيارات المنسدلة
  const dropdownItems = [
    {
      key: 'preview',
      label: 'معاينة فقط (بدون طباعة)',
      icon: <EyeOutlined />,
      onClick: handlePreview
    },
    {
      key: 'print',
      label: 'طباعة مباشرة',
      icon: <PrinterOutlined />,
      onClick: handleDirectPrint
    }
  ]

  if (showExportOptions) {
    dropdownItems.push(
      {
        key: 'pdf',
        label: 'حفظ كـ PDF',
        icon: <FileTextOutlined />,
        onClick: handleSavePDF
      }
    )
  }

  if (showSettings) {
    dropdownItems.push(
      {
        key: 'settings',
        label: 'إعدادات الطباعة',
        icon: <SettingOutlined />,
        onClick: async () => setSettingsVisible(true)
      }
    )
  }

  // مكون إعدادات الطباعة
  const PrintSettingsModal = () => (
    <Modal
      title="إعدادات الطباعة"
      open={settingsVisible}
      onCancel={() => setSettingsVisible(false)}
      onOk={handleSaveSettings}
      width={600}
      okText="حفظ في الإعدادات المركزية"
      cancelText="إلغاء"
    >
      <Form layout="vertical">
        <Form.Item label="حجم الورق">
          <Select
            value={printOptions.pageSize}
            onChange={(value) => setPrintOptions(prev => ({ ...prev, pageSize: value }))}
          >
            <Select.Option value="A4">A4</Select.Option>
            <Select.Option value="A5">A5</Select.Option>
            <Select.Option value="Letter">Letter</Select.Option>
            <Select.Option value="A3">A3</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item label="اتجاه الورق">
          <Select
            value={printOptions.orientation}
            onChange={(value) => setPrintOptions(prev => ({ ...prev, orientation: value }))}
          >
            <Select.Option value="portrait">عمودي</Select.Option>
            <Select.Option value="landscape">أفقي</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item label="حجم الخط">
          <InputNumber
            min={8}
            max={20}
            value={printOptions.fontSize}
            onChange={(value) => setPrintOptions(prev => ({ ...prev, fontSize: value || 12 }))}
          />
        </Form.Item>

        <Form.Item label="عدد النسخ">
          <InputNumber
            min={1}
            max={10}
            value={printOptions.copies}
            onChange={(value) => setPrintOptions(prev => ({ ...prev, copies: value || 1 }))}
          />
        </Form.Item>

        <Space direction="vertical" style={{ width: '100%' }}>
          <Form.Item>
            <Switch
              checked={printOptions.showLogo}
              onChange={(checked) => setPrintOptions(prev => ({ ...prev, showLogo: checked }))}
            />
            <span style={{ marginRight: 8 }}>إظهار الشعار</span>
          </Form.Item>

          <Form.Item>
            <Switch
              checked={printOptions.showHeader}
              onChange={(checked) => setPrintOptions(prev => ({ ...prev, showHeader: checked }))}
            />
            <span style={{ marginRight: 8 }}>إظهار الهيدر</span>
          </Form.Item>

          <Form.Item>
            <Switch
              checked={printOptions.showFooter}
              onChange={(checked) => setPrintOptions(prev => ({ ...prev, showFooter: checked }))}
            />
            <span style={{ marginRight: 8 }}>إظهار الفوتر</span>
          </Form.Item>

          <Form.Item>
            <Switch
              checked={printOptions.showSignature}
              onChange={(checked) => setPrintOptions(prev => ({ ...prev, showSignature: checked }))}
            />
            <span style={{ marginRight: 8 }}>إظهار التوقيع</span>
          </Form.Item>

          <Form.Item>
            <Switch
              checked={printOptions.showTerms}
              onChange={(checked) => setPrintOptions(prev => ({ ...prev, showTerms: checked }))}
            />
            <span style={{ marginRight: 8 }}>إظهار الشروط</span>
          </Form.Item>
        </Space>
      </Form>
    </Modal>
  )

  // إذا لم تكن هناك قائمة منسدلة، اعرض زر بسيط
  if (!showDropdown) {
    return (
      <>
        <Button
          type={buttonType}
          size={size}
          icon={icon}
          loading={loading}
          disabled={disabled}
          onClick={handleDirectPrint}
          title={title}
        >
          {buttonText}
        </Button>
        {showSettings && <PrintSettingsModal />}
      </>
    )
  }

  // عرض الزر مع القائمة المنسدلة
  return (
    <>
      <Dropdown
        menu={{ items: dropdownItems }}
        trigger={['click']}
        disabled={disabled}
      >
        <Button
          type={buttonType}
          size={size}
          icon={icon}
          loading={loading}
          disabled={disabled}
          title={title}
        >
          {buttonText}
        </Button>
      </Dropdown>
      {showSettings && <PrintSettingsModal />}
    </>
  )
}

export default UnifiedPrintButton
