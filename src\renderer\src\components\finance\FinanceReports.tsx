import React, { useState, useEffect, useCallback } from 'react'
import {
  Card,
  Row,
  Col,
  Button,
  Statistic,
  // Table,
  DatePicker,
  Space,
  App} from 'antd'
import {
  ArrowLeftOutlined,
  BarChartOutlined,
  DollarOutlined,
  BankOutlined,
  CreditCardOutlined,
  FileTextOutlined,
  ArrowUpOutlined,
  // ArrowDownOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import { UnifiedPrintButton } from '../common'

const { RangePicker } = DatePicker

interface FinanceReportsProps {
  onBack: () => void
  initialView?: ReportView
}

type ReportView = 'main' | 'balance-sheet' | 'income-statement' | 'cash-flow' | 'receivables' | 'detailed-costs' | 'profitability-analysis' | 'cost-centers'

const FinanceReports: React.FC<FinanceReportsProps> = ({ onBack, initialView = 'main' }) => {
  const { message: messageApi } = App.useApp()
  const [activeView, setActiveView] = useState<ReportView>(initialView as ReportView)
  const [loading, setLoading] = useState(false)

  // تحديث activeView عندما يتغير initialView من الخارج
  useEffect(() => {
    setActiveView(initialView as ReportView)
  }, [initialView])
  const [dateRange, setDateRange] = useState<any>([
    dayjs().subtract(30, 'day'),
    dayjs()
  ])
  const [reportData, setReportData] = useState({
    bankAccounts: [],
    checks: [],
    paymentVouchers: [],
    receiptVouchers: [],
    promissoryNotes: [],
    transactions: []
  })

  const loadReportData = useCallback(async () => {
    setLoading(true)
    try {
      const [
        bankAccountsResponse,
        checksResponse,
        paymentVouchersResponse,
        receiptVouchersResponse,
        promissoryNotesResponse,
        transactionsResponse
      ] = await Promise.all([
        window.electronAPI.getBankAccounts(),
        window.electronAPI.getChecks(),
        window.electronAPI.getPaymentVouchers(),
        window.electronAPI.getReceiptVouchers(),
        window.electronAPI.getPromissoryNotes(),
        window.electronAPI.getBankTransactions()
      ])

      setReportData({
        bankAccounts: bankAccountsResponse.success ? bankAccountsResponse.data : [],
        checks: checksResponse.success ? checksResponse.data : [],
        paymentVouchers: paymentVouchersResponse.success ? paymentVouchersResponse.data : [],
        receiptVouchers: receiptVouchersResponse.success ? receiptVouchersResponse.data : [],
        promissoryNotes: promissoryNotesResponse.success ? promissoryNotesResponse.data : [],
        transactions: transactionsResponse.success ? transactionsResponse.data : []
      })
    } catch (_error) {
      messageApi.error('خطأ في تحميل بيانات التقارير')
    }
    setLoading(false)
  }, [dateRange, messageApi])

  useEffect(() => {
    loadReportData()
  }, [loadReportData])

  // حساب الإحصائيات المالية
  const calculateFinancialStats = () => {
    const { bankAccounts, checks, paymentVouchers, receiptVouchers, promissoryNotes, transactions } = reportData

    // إجمالي الأرصدة المصرفية
    const totalBankBalance = bankAccounts.reduce((sum: number, account: any) => sum + (account.balance || 0), 0)

    // إحصائيات الشيكات
    const totalChecks = checks.length
    const cashedChecks = checks.filter((check: any) => check.status === 'cashed').length
    const pendingChecks = checks.filter((check: any) => check.status === 'issued').length
    const bouncedChecks = checks.filter((check: any) => check.status === 'bounced').length

    // إحصائيات السندات
    const totalPaymentVouchers = paymentVouchers.reduce((sum: number, voucher: any) => sum + (voucher.amount || 0), 0)
    const totalReceiptVouchers = receiptVouchers.reduce((sum: number, voucher: any) => sum + (voucher.amount || 0), 0)

    // إحصائيات الكمبيالات
    const activePromissoryNotes = promissoryNotes.filter((note: any) => note.status === 'active')
    const totalPromissoryAmount = activePromissoryNotes.reduce((sum: number, note: any) => sum + (note.amount || 0), 0)

    // المعاملات المصرفية
    const deposits = transactions.filter((t: any) => t.transaction_type === 'deposit')
    const withdrawals = transactions.filter((t: any) => t.transaction_type === 'withdrawal')
    const totalDeposits = deposits.reduce((sum: number, t: any) => sum + (t.amount || 0), 0)
    const totalWithdrawals = withdrawals.reduce((sum: number, t: any) => sum + (t.amount || 0), 0)

    return {
      totalBankBalance,
      totalChecks,
      cashedChecks,
      pendingChecks,
      bouncedChecks,
      totalPaymentVouchers,
      totalReceiptVouchers,
      totalPromissoryAmount,
      totalDeposits,
      totalWithdrawals,
      netCashFlow: totalDeposits - totalWithdrawals
    }
  }

  const stats = calculateFinancialStats()

  // جدول ملخص الحسابات المصرفية - معلق
  // const bankAccountColumns = [
  //   {
  //     title: 'البنك',
  //     dataIndex: 'bank_name',
  //     key: 'bank_name',
  //   },
  //   {
  //     title: 'اسم الحساب',
  //     dataIndex: 'account_name',
  //     key: 'account_name',
  //   },
  //   {
  //     title: 'رقم الحساب',
  //     dataIndex: 'account_number',
  //     key: 'account_number',
  //   },
  //   {
  //     title: 'الرصيد',
  //     dataIndex: 'balance',
  //     key: 'balance',
  //     render: (balance: number) => (
  //       <span style={{ color: balance >= 0 ? '#52c41a' : '#ff4d4f', fontWeight: 'bold' }}>
  //         ₪ {balance?.toLocaleString() || 0}
  //       </span>
  //     ),
  //   },
  // ]

  // جدول المعاملات الحديثة - معلق
  // const transactionColumns = [
  //   {
  //     title: 'التاريخ',
  //     dataIndex: 'transaction_date',
  //     key: 'transaction_date',
  //   },
  //   {
  //     title: 'البنك',
  //     dataIndex: 'bank_name',
  //     key: 'bank_name',
  //   },
  //   {
  //     title: 'النوع',
  //     dataIndex: 'transaction_type',
  //     key: 'transaction_type',
  //     render: (type: string) => {
  //       const typeMap: any = {
  //         'deposit': 'إيداع',
  //         'withdrawal': 'سحب',
  //         'transfer': 'تحويل'
  //       }
  //       return typeMap[type] || type
  //     },
  //   },
  //   {
  //     title: 'المبلغ',
  //     dataIndex: 'amount',
  //     key: 'amount',
  //     render: (amount: number, record: any) => (
  //       <span style={{
  //         color: record.transaction_type === 'deposit' ? '#52c41a' : '#ff4d4f',
  //         fontWeight: 'bold'
  //       }}>
  //         {record.transaction_type === 'deposit' ? '+' : '-'}₪ {amount?.toLocaleString() || 0}
  //       </span>
  //     ),
  //   },
  //   {
  //     title: 'الوصف',
  //     dataIndex: 'description',
  //     key: 'description',
  //   },
  // ]

  const renderMainView = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>📊 التقارير المالية</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            اختر نوع التقرير المالي المطلوب
          </p>
        </div>
        <Button
          type="default"
          icon={<ArrowLeftOutlined />}
          onClick={onBack}
        >
          العودة
        </Button>
      </div>

      {/* قوائم التقارير */}
      <Row gutter={16}>
        <Col span={8}>
          <Card
            hoverable
            onClick={() => setActiveView('balance-sheet')}
            style={{ textAlign: 'center', cursor: 'pointer', marginBottom: '16px' }}
          >
            <BarChartOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
            <h3>الميزانية العمومية</h3>
            <p>عرض الأصول والخصوم وحقوق الملكية</p>
          </Card>
        </Col>
        <Col span={8}>
          <Card
            hoverable
            onClick={() => setActiveView('income-statement')}
            style={{ textAlign: 'center', cursor: 'pointer', marginBottom: '16px' }}
          >
            <DollarOutlined style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} />
            <h3>قائمة الأرباح والخسائر</h3>
            <p>تحليل الإيرادات والمصروفات والأرباح</p>
          </Card>
        </Col>
        <Col span={8}>
          <Card
            hoverable
            onClick={() => setActiveView('cash-flow')}
            style={{ textAlign: 'center', cursor: 'pointer', marginBottom: '16px' }}
          >
            <ArrowUpOutlined style={{ fontSize: '48px', color: '#722ed1', marginBottom: '16px' }} />
            <h3>التدفق النقدي</h3>
            <p>تتبع حركة النقد الداخل والخارج</p>
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={8}>
          <Card
            hoverable
            onClick={() => setActiveView('receivables')}
            style={{ textAlign: 'center', cursor: 'pointer', marginBottom: '16px' }}
          >
            <CreditCardOutlined style={{ fontSize: '48px', color: '#fa8c16', marginBottom: '16px' }} />
            <h3>تقرير المديونيات</h3>
            <p>متابعة المبالغ المستحقة من العملاء</p>
          </Card>
        </Col>
        <Col span={8}>
          <Card
            hoverable
            onClick={() => setActiveView('detailed-costs')}
            style={{ textAlign: 'center', cursor: 'pointer', marginBottom: '16px' }}
          >
            <FileTextOutlined style={{ fontSize: '48px', color: '#13c2c2', marginBottom: '16px' }} />
            <h3>تقارير التكاليف التفصيلية</h3>
            <p>تحليل مفصل لجميع أنواع التكاليف</p>
          </Card>
        </Col>
        <Col span={8}>
          <Card
            hoverable
            onClick={() => setActiveView('profitability-analysis')}
            style={{ textAlign: 'center', cursor: 'pointer', marginBottom: '16px' }}
          >
            <BarChartOutlined style={{ fontSize: '48px', color: '#eb2f96', marginBottom: '16px' }} />
            <h3>تحليل الربحية والهوامش</h3>
            <p>تحليل هوامش الربح والأداء المالي</p>
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Card
            hoverable
            onClick={() => setActiveView('cost-centers')}
            style={{ textAlign: 'center', cursor: 'pointer' }}
          >
            <BankOutlined style={{ fontSize: '48px', color: '#f5222d', marginBottom: '16px' }} />
            <h3>تقارير مراكز التكلفة</h3>
            <p>تحليل التكاليف حسب مراكز التكلفة المختلفة</p>
          </Card>
        </Col>
        <Col span={12}>
          <Card style={{ textAlign: 'center' }}>
            <BarChartOutlined style={{ fontSize: '48px', color: '#666', marginBottom: '16px' }} />
            <h3>ملخص عام</h3>
            <p>عرض سريع لجميع المؤشرات المالية</p>
            <Button type="primary" onClick={loadReportData} loading={loading}>
              تحديث البيانات
            </Button>
          </Card>
        </Col>
      </Row>
    </div>
  )

  // const renderDetailedReport = () => (
  //   <div>
  //     ... detailed report content commented out ...
  //   </div>
  // )

  const renderBalanceSheetReport = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#1890ff' }}>📊 الميزانية العمومية</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            عرض الأصول والخصوم وحقوق الملكية
          </p>
        </div>
        <Space>
          <UnifiedPrintButton
            data={{
              title: 'الميزانية العمومية',
              subtitle: `من ${dateRange?.[0]?.format('YYYY-MM-DD')} إلى ${dateRange?.[1]?.format('YYYY-MM-DD')}`,
              date: new Date().toLocaleDateString('ar-SA'),
              items: [
                {
                  id: 1,
                  name: 'الأصول المتداولة',
                  description: 'النقد والأرصدة البنكية',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalBankBalance,
                  total: stats.totalBankBalance
                },
                {
                  id: 2,
                  name: 'الأصول الثابتة',
                  description: 'المعدات والأثاث',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: 50000,
                  total: 50000
                },
                {
                  id: 3,
                  name: 'الخصوم المتداولة',
                  description: 'المبالغ المستحقة الدفع',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalPaymentVouchers,
                  total: stats.totalPaymentVouchers
                }
              ],
              total: stats.totalBankBalance + 50000,
              notes: `إجمالي الأصول: ${(stats.totalBankBalance + 50000).toLocaleString()} ₪\nإجمالي الخصوم: ${stats.totalPaymentVouchers.toLocaleString()} ₪\nحقوق الملكية: ${(stats.totalBankBalance + 50000 - stats.totalPaymentVouchers).toLocaleString()} ₪`
            }}
            type="report"
            subType="financial"
            buttonText="طباعة الميزانية"
            size="middle"
            showDropdown={true}
            _documentId="balance_sheet_report"
            onAfterPrint={() => messageApi.success('تم طباعة الميزانية العمومية بنجاح')}
            onError={() => messageApi.error('فشل في طباعة الميزانية العمومية')}
          />
          <Button type="default" onClick={() => setActiveView('main')}>قائمة التقارير</Button>
          <Button type="default" icon={<ArrowLeftOutlined />} onClick={onBack}>العودة</Button>
        </Space>
      </div>

      <Card style={{ marginBottom: '24px' }}>
        <Space>
          <span>فترة التقرير:</span>
          <RangePicker value={dateRange} onChange={setDateRange} format="YYYY-MM-DD" />
          <Button type="primary" onClick={loadReportData} loading={loading}>تحديث التقرير</Button>
        </Space>
      </Card>

      <Row gutter={16}>
        <Col span={12}>
          <Card title="الأصول" style={{ marginBottom: '16px' }}>
            <Statistic title="الأصول المتداولة" value={stats.totalBankBalance} suffix="₪" />
            <Statistic title="الأصول الثابتة" value={50000} suffix="₪" />
            <Statistic title="إجمالي الأصول" value={stats.totalBankBalance + 50000} suffix="₪" />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="الخصوم وحقوق الملكية" style={{ marginBottom: '16px' }}>
            <Statistic title="الخصوم المتداولة" value={stats.totalPaymentVouchers} suffix="₪" />
            <Statistic title="حقوق الملكية" value={stats.totalBankBalance + 50000 - stats.totalPaymentVouchers} suffix="₪" />
            <Statistic title="إجمالي الخصوم وحقوق الملكية" value={stats.totalBankBalance + 50000} suffix="₪" />
          </Card>
        </Col>
      </Row>
    </div>
  )

  const renderIncomeStatementReport = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#52c41a' }}>💰 قائمة الأرباح والخسائر</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            تحليل الإيرادات والمصروفات والأرباح
          </p>
        </div>
        <Space>
          <UnifiedPrintButton
            data={{
              title: 'قائمة الأرباح والخسائر',
              subtitle: `من ${dateRange?.[0]?.format('YYYY-MM-DD')} إلى ${dateRange?.[1]?.format('YYYY-MM-DD')}`,
              date: new Date().toLocaleDateString('ar-SA'),
              items: [
                {
                  id: 1,
                  name: 'إيرادات المبيعات',
                  description: 'إجمالي المبيعات للفترة',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalReceiptVouchers,
                  total: stats.totalReceiptVouchers
                },
                {
                  id: 2,
                  name: 'إيرادات أخرى',
                  description: 'إيرادات متنوعة',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: 5000,
                  total: 5000
                },
                {
                  id: 3,
                  name: 'تكلفة البضاعة المباعة',
                  description: 'التكلفة المباشرة للمبيعات',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: -(stats.totalPaymentVouchers * 0.6),
                  total: -(stats.totalPaymentVouchers * 0.6)
                },
                {
                  id: 4,
                  name: 'مصروفات تشغيلية',
                  description: 'المصروفات الإدارية والتشغيلية',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: -(stats.totalPaymentVouchers * 0.4),
                  total: -(stats.totalPaymentVouchers * 0.4)
                }
              ],
              total: stats.totalReceiptVouchers + 5000 - stats.totalPaymentVouchers,
              notes: `إجمالي الإيرادات: ${(stats.totalReceiptVouchers + 5000).toLocaleString()} ₪\nإجمالي المصروفات: ${stats.totalPaymentVouchers.toLocaleString()} ₪\nصافي الربح: ${(stats.totalReceiptVouchers + 5000 - stats.totalPaymentVouchers).toLocaleString()} ₪`
            }}
            type="report"
            subType="financial"
            buttonText="طباعة قائمة الأرباح"
            size="middle"
            showDropdown={true}
            _documentId="income_statement_report"
            onAfterPrint={() => messageApi.success('تم طباعة قائمة الأرباح والخسائر بنجاح')}
            onError={() => messageApi.error('فشل في طباعة قائمة الأرباح والخسائر')}
          />
          <Button type="default" onClick={() => setActiveView('main')}>قائمة التقارير</Button>
          <Button type="default" icon={<ArrowLeftOutlined />} onClick={onBack}>العودة</Button>
        </Space>
      </div>

      <Card style={{ marginBottom: '24px' }}>
        <Space>
          <span>فترة التقرير:</span>
          <RangePicker value={dateRange} onChange={setDateRange} format="YYYY-MM-DD" />
          <Button type="primary" onClick={loadReportData} loading={loading}>تحديث التقرير</Button>
        </Space>
      </Card>

      <Row gutter={16}>
        <Col span={8}>
          <Card title="الإيرادات" style={{ marginBottom: '16px' }}>
            <Statistic title="إيرادات المبيعات" value={stats.totalReceiptVouchers} suffix="₪" valueStyle={{ color: '#52c41a' }} />
            <Statistic title="إيرادات أخرى" value={5000} suffix="₪" valueStyle={{ color: '#52c41a' }} />
            <Statistic title="إجمالي الإيرادات" value={stats.totalReceiptVouchers + 5000} suffix="₪" valueStyle={{ color: '#52c41a' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="المصروفات" style={{ marginBottom: '16px' }}>
            <Statistic title="تكلفة البضاعة المباعة" value={stats.totalPaymentVouchers * 0.6} suffix="₪" valueStyle={{ color: '#ff4d4f' }} />
            <Statistic title="مصروفات تشغيلية" value={stats.totalPaymentVouchers * 0.4} suffix="₪" valueStyle={{ color: '#ff4d4f' }} />
            <Statistic title="إجمالي المصروفات" value={stats.totalPaymentVouchers} suffix="₪" valueStyle={{ color: '#ff4d4f' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="صافي الربح" style={{ marginBottom: '16px' }}>
            <Statistic
              title="صافي الربح/الخسارة"
              value={stats.totalReceiptVouchers + 5000 - stats.totalPaymentVouchers}
              suffix="₪"
              valueStyle={{ color: (stats.totalReceiptVouchers + 5000 - stats.totalPaymentVouchers) >= 0 ? '#52c41a' : '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )

  const renderCashFlowReport = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#722ed1' }}>💸 تقرير التدفق النقدي</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            تتبع حركة النقد الداخل والخارج
          </p>
        </div>
        <Space>
          <UnifiedPrintButton
            data={{
              title: 'تقرير التدفق النقدي',
              subtitle: `من ${dateRange?.[0]?.format('YYYY-MM-DD')} إلى ${dateRange?.[1]?.format('YYYY-MM-DD')}`,
              date: new Date().toLocaleDateString('ar-SA'),
              items: [
                {
                  id: 1,
                  name: 'إيداعات مصرفية',
                  description: 'التدفقات النقدية الداخلة من الإيداعات',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalDeposits,
                  total: stats.totalDeposits
                },
                {
                  id: 2,
                  name: 'مقبوضات من العملاء',
                  description: 'التدفقات النقدية الداخلة من العملاء',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalReceiptVouchers,
                  total: stats.totalReceiptVouchers
                },
                {
                  id: 3,
                  name: 'سحوبات مصرفية',
                  description: 'التدفقات النقدية الخارجة من السحوبات',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: -stats.totalWithdrawals,
                  total: -stats.totalWithdrawals
                },
                {
                  id: 4,
                  name: 'مدفوعات للموردين',
                  description: 'التدفقات النقدية الخارجة للموردين',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: -stats.totalPaymentVouchers,
                  total: -stats.totalPaymentVouchers
                }
              ],
              total: stats.totalDeposits + stats.totalReceiptVouchers - stats.totalWithdrawals - stats.totalPaymentVouchers,
              notes: `إجمالي التدفقات الداخلة: ${(stats.totalDeposits + stats.totalReceiptVouchers).toLocaleString()} ₪\nإجمالي التدفقات الخارجة: ${(stats.totalWithdrawals + stats.totalPaymentVouchers).toLocaleString()} ₪\nصافي التدفق النقدي: ${(stats.totalDeposits + stats.totalReceiptVouchers - stats.totalWithdrawals - stats.totalPaymentVouchers).toLocaleString()} ₪`
            }}
            type="report"
            subType="financial"
            buttonText="طباعة التدفق النقدي"
            size="middle"
            showDropdown={true}
            _documentId="cash_flow_report"
            onAfterPrint={() => messageApi.success('تم طباعة تقرير التدفق النقدي بنجاح')}
            onError={() => messageApi.error('فشل في طباعة تقرير التدفق النقدي')}
          />
          <Button type="default" onClick={() => setActiveView('main')}>قائمة التقارير</Button>
          <Button type="default" icon={<ArrowLeftOutlined />} onClick={onBack}>العودة</Button>
        </Space>
      </div>

      <Card style={{ marginBottom: '24px' }}>
        <Space>
          <span>فترة التقرير:</span>
          <RangePicker value={dateRange} onChange={setDateRange} format="YYYY-MM-DD" />
          <Button type="primary" onClick={loadReportData} loading={loading}>تحديث التقرير</Button>
        </Space>
      </Card>

      <Row gutter={16}>
        <Col span={8}>
          <Card title="التدفقات النقدية الداخلة" style={{ marginBottom: '16px' }}>
            <Statistic title="إيداعات مصرفية" value={stats.totalDeposits} suffix="₪" valueStyle={{ color: '#52c41a' }} />
            <Statistic title="مقبوضات من العملاء" value={stats.totalReceiptVouchers} suffix="₪" valueStyle={{ color: '#52c41a' }} />
            <Statistic title="إجمالي التدفقات الداخلة" value={stats.totalDeposits + stats.totalReceiptVouchers} suffix="₪" valueStyle={{ color: '#52c41a' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="التدفقات النقدية الخارجة" style={{ marginBottom: '16px' }}>
            <Statistic title="سحوبات مصرفية" value={stats.totalWithdrawals} suffix="₪" valueStyle={{ color: '#ff4d4f' }} />
            <Statistic title="مدفوعات للموردين" value={stats.totalPaymentVouchers} suffix="₪" valueStyle={{ color: '#ff4d4f' }} />
            <Statistic title="إجمالي التدفقات الخارجة" value={stats.totalWithdrawals + stats.totalPaymentVouchers} suffix="₪" valueStyle={{ color: '#ff4d4f' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="صافي التدفق النقدي" style={{ marginBottom: '16px' }}>
            <Statistic
              title="صافي التدفق النقدي"
              value={stats.totalDeposits + stats.totalReceiptVouchers - stats.totalWithdrawals - stats.totalPaymentVouchers}
              suffix="₪"
              valueStyle={{ color: stats.netCashFlow >= 0 ? '#52c41a' : '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )

  const renderReceivablesReport = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#fa8c16' }}>📋 تقرير المديونيات</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            متابعة المبالغ المستحقة من العملاء
          </p>
        </div>
        <Space>
          <UnifiedPrintButton
            data={{
              title: 'تقرير المديونيات',
              subtitle: `من ${dateRange?.[0]?.format('YYYY-MM-DD')} إلى ${dateRange?.[1]?.format('YYYY-MM-DD')}`,
              date: new Date().toLocaleDateString('ar-SA'),
              items: [
                {
                  id: 1,
                  name: 'إجمالي المديونيات',
                  description: 'مجموع المبالغ المستحقة من العملاء',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalPromissoryAmount,
                  total: stats.totalPromissoryAmount
                },
                {
                  id: 2,
                  name: 'مديونيات متأخرة',
                  description: 'المبالغ المتأخرة عن موعد الاستحقاق',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalPromissoryAmount * 0.3,
                  total: stats.totalPromissoryAmount * 0.3
                },
                {
                  id: 3,
                  name: 'مديونيات جارية',
                  description: 'المبالغ ضمن فترة الاستحقاق',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalPromissoryAmount * 0.7,
                  total: stats.totalPromissoryAmount * 0.7
                }
              ],
              total: stats.totalPromissoryAmount,
              notes: `شيكات معلقة: ${stats.pendingChecks}\nشيكات مرتدة: ${stats.bouncedChecks}\nشيكات محصلة: ${stats.cashedChecks}\nنسبة التحصيل: ${stats.totalPromissoryAmount > 0 ? ((stats.totalPromissoryAmount * 0.7 / stats.totalPromissoryAmount) * 100).toFixed(1) : 0}%`
            }}
            type="report"
            subType="financial"
            buttonText="طباعة تقرير المديونيات"
            size="middle"
            showDropdown={true}
            _documentId="receivables_report"
            onAfterPrint={() => messageApi.success('تم طباعة تقرير المديونيات بنجاح')}
            onError={() => messageApi.error('فشل في طباعة تقرير المديونيات')}
          />
          <Button type="default" onClick={() => setActiveView('main')}>قائمة التقارير</Button>
          <Button type="default" icon={<ArrowLeftOutlined />} onClick={onBack}>العودة</Button>
        </Space>
      </div>

      <Card style={{ marginBottom: '24px' }}>
        <Space>
          <span>فترة التقرير:</span>
          <RangePicker value={dateRange} onChange={setDateRange} format="YYYY-MM-DD" />
          <Button type="primary" onClick={loadReportData} loading={loading}>تحديث التقرير</Button>
        </Space>
      </Card>

      <Row gutter={16}>
        <Col span={12}>
          <Card title="ملخص المديونيات" style={{ marginBottom: '16px' }}>
            <Statistic title="إجمالي المديونيات" value={stats.totalPromissoryAmount} suffix="₪" valueStyle={{ color: '#fa8c16' }} />
            <Statistic title="مديونيات متأخرة" value={stats.totalPromissoryAmount * 0.3} suffix="₪" valueStyle={{ color: '#ff4d4f' }} />
            <Statistic title="مديونيات جارية" value={stats.totalPromissoryAmount * 0.7} suffix="₪" valueStyle={{ color: '#52c41a' }} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="الشيكات المستحقة" style={{ marginBottom: '16px' }}>
            <Statistic title="شيكات معلقة" value={stats.pendingChecks} valueStyle={{ color: '#fa8c16' }} />
            <Statistic title="شيكات مرتدة" value={stats.bouncedChecks} valueStyle={{ color: '#ff4d4f' }} />
            <Statistic title="شيكات محصلة" value={stats.cashedChecks} valueStyle={{ color: '#52c41a' }} />
          </Card>
        </Col>
      </Row>
    </div>
  )

  const renderDetailedCostsReport = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#13c2c2' }}>📊 تقارير التكاليف التفصيلية</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            تحليل مفصل لجميع أنواع التكاليف
          </p>
        </div>
        <Space>
          <UnifiedPrintButton
            data={{
              title: 'تقرير التكاليف التفصيلية',
              subtitle: `من ${dateRange?.[0]?.format('YYYY-MM-DD')} إلى ${dateRange?.[1]?.format('YYYY-MM-DD')}`,
              date: new Date().toLocaleDateString('ar-SA'),
              items: [
                {
                  id: 1,
                  name: 'تكلفة المواد الخام',
                  description: 'التكاليف المباشرة للمواد',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalPaymentVouchers * 0.4,
                  total: stats.totalPaymentVouchers * 0.4
                },
                {
                  id: 2,
                  name: 'تكلفة العمالة',
                  description: 'تكاليف الأجور والرواتب',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalPaymentVouchers * 0.3,
                  total: stats.totalPaymentVouchers * 0.3
                },
                {
                  id: 3,
                  name: 'التكاليف الإضافية',
                  description: 'التكاليف غير المباشرة',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalPaymentVouchers * 0.3,
                  total: stats.totalPaymentVouchers * 0.3
                }
              ],
              total: stats.totalPaymentVouchers,
              notes: `إجمالي التكاليف المباشرة: ${(stats.totalPaymentVouchers * 0.7).toLocaleString()} ₪\nإجمالي التكاليف غير المباشرة: ${(stats.totalPaymentVouchers * 0.3).toLocaleString()} ₪`
            }}
            type="report"
            subType="financial"
            buttonText="طباعة تقرير التكاليف"
            size="middle"
            showDropdown={true}
            _documentId="detailed_costs_report"
            onAfterPrint={() => messageApi.success('تم طباعة تقرير التكاليف بنجاح')}
            onError={() => messageApi.error('فشل في طباعة تقرير التكاليف')}
          />
          <Button type="default" onClick={() => setActiveView('main')}>قائمة التقارير</Button>
          <Button type="default" icon={<ArrowLeftOutlined />} onClick={onBack}>العودة</Button>
        </Space>
      </div>

      <Card style={{ marginBottom: '24px' }}>
        <Space>
          <span>فترة التقرير:</span>
          <RangePicker value={dateRange} onChange={setDateRange} format="YYYY-MM-DD" />
          <Button type="primary" onClick={loadReportData} loading={loading}>تحديث التقرير</Button>
        </Space>
      </Card>

      <Row gutter={16}>
        <Col span={8}>
          <Card title="التكاليف المباشرة" style={{ marginBottom: '16px' }}>
            <Statistic title="تكلفة المواد الخام" value={stats.totalPaymentVouchers * 0.4} suffix="₪" valueStyle={{ color: '#13c2c2' }} />
            <Statistic title="تكلفة العمالة" value={stats.totalPaymentVouchers * 0.3} suffix="₪" valueStyle={{ color: '#13c2c2' }} />
            <Statistic title="إجمالي التكاليف المباشرة" value={stats.totalPaymentVouchers * 0.7} suffix="₪" valueStyle={{ color: '#13c2c2' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="التكاليف غير المباشرة" style={{ marginBottom: '16px' }}>
            <Statistic title="تكاليف إدارية" value={stats.totalPaymentVouchers * 0.15} suffix="₪" valueStyle={{ color: '#722ed1' }} />
            <Statistic title="تكاليف تسويقية" value={stats.totalPaymentVouchers * 0.1} suffix="₪" valueStyle={{ color: '#722ed1' }} />
            <Statistic title="تكاليف أخرى" value={stats.totalPaymentVouchers * 0.05} suffix="₪" valueStyle={{ color: '#722ed1' }} />
            <Statistic title="إجمالي التكاليف غير المباشرة" value={stats.totalPaymentVouchers * 0.3} suffix="₪" valueStyle={{ color: '#722ed1' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="إجمالي التكاليف" style={{ marginBottom: '16px' }}>
            <Statistic title="إجمالي التكاليف" value={stats.totalPaymentVouchers} suffix="₪" valueStyle={{ color: '#ff4d4f' }} />
            <Statistic title="متوسط التكلفة اليومية" value={stats.totalPaymentVouchers / 30} suffix="₪" valueStyle={{ color: '#fa8c16' }} />
          </Card>
        </Col>
      </Row>
    </div>
  )

  const renderProfitabilityAnalysisReport = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#eb2f96' }}>📈 تحليل الربحية والهوامش</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            تحليل هوامش الربح والأداء المالي
          </p>
        </div>
        <Space>
          <UnifiedPrintButton
            data={{
              title: 'تحليل الربحية والهوامش',
              subtitle: `من ${dateRange?.[0]?.format('YYYY-MM-DD')} إلى ${dateRange?.[1]?.format('YYYY-MM-DD')}`,
              date: new Date().toLocaleDateString('ar-SA'),
              items: [
                {
                  id: 1,
                  name: 'هامش الربح الإجمالي',
                  description: 'نسبة الربح الإجمالي من المبيعات',
                  quantity: 1,
                  unit: 'نسبة',
                  unitPrice: ((stats.totalReceiptVouchers - stats.totalPaymentVouchers * 0.6) / stats.totalReceiptVouchers * 100),
                  total: ((stats.totalReceiptVouchers - stats.totalPaymentVouchers * 0.6) / stats.totalReceiptVouchers * 100)
                },
                {
                  id: 2,
                  name: 'هامش الربح التشغيلي',
                  description: 'نسبة الربح التشغيلي من المبيعات',
                  quantity: 1,
                  unit: 'نسبة',
                  unitPrice: ((stats.totalReceiptVouchers - stats.totalPaymentVouchers * 0.8) / stats.totalReceiptVouchers * 100),
                  total: ((stats.totalReceiptVouchers - stats.totalPaymentVouchers * 0.8) / stats.totalReceiptVouchers * 100)
                },
                {
                  id: 3,
                  name: 'هامش الربح الصافي',
                  description: 'نسبة الربح الصافي من المبيعات',
                  quantity: 1,
                  unit: 'نسبة',
                  unitPrice: ((stats.totalReceiptVouchers - stats.totalPaymentVouchers) / stats.totalReceiptVouchers * 100),
                  total: ((stats.totalReceiptVouchers - stats.totalPaymentVouchers) / stats.totalReceiptVouchers * 100)
                }
              ],
              total: stats.totalReceiptVouchers - stats.totalPaymentVouchers,
              notes: `إجمالي الإيرادات: ${stats.totalReceiptVouchers.toLocaleString()} ₪\nإجمالي التكاليف: ${stats.totalPaymentVouchers.toLocaleString()} ₪\nصافي الربح: ${(stats.totalReceiptVouchers - stats.totalPaymentVouchers).toLocaleString()} ₪`
            }}
            type="report"
            subType="financial"
            buttonText="طباعة تحليل الربحية"
            size="middle"
            showDropdown={true}
            _documentId="profitability_analysis_report"
            onAfterPrint={() => messageApi.success('تم طباعة تحليل الربحية بنجاح')}
            onError={() => messageApi.error('فشل في طباعة تحليل الربحية')}
          />
          <Button type="default" onClick={() => setActiveView('main')}>قائمة التقارير</Button>
          <Button type="default" icon={<ArrowLeftOutlined />} onClick={onBack}>العودة</Button>
        </Space>
      </div>

      <Card style={{ marginBottom: '24px' }}>
        <Space>
          <span>فترة التقرير:</span>
          <RangePicker value={dateRange} onChange={setDateRange} format="YYYY-MM-DD" />
          <Button type="primary" onClick={loadReportData} loading={loading}>تحديث التقرير</Button>
        </Space>
      </Card>

      <Row gutter={16}>
        <Col span={8}>
          <Card title="هوامش الربح" style={{ marginBottom: '16px' }}>
            <Statistic title="هامش الربح الإجمالي" value={((stats.totalReceiptVouchers - stats.totalPaymentVouchers * 0.6) / stats.totalReceiptVouchers * 100)} suffix="%" valueStyle={{ color: '#eb2f96' }} precision={1} />
            <Statistic title="هامش الربح التشغيلي" value={((stats.totalReceiptVouchers - stats.totalPaymentVouchers * 0.8) / stats.totalReceiptVouchers * 100)} suffix="%" valueStyle={{ color: '#eb2f96' }} precision={1} />
            <Statistic title="هامش الربح الصافي" value={((stats.totalReceiptVouchers - stats.totalPaymentVouchers) / stats.totalReceiptVouchers * 100)} suffix="%" valueStyle={{ color: '#eb2f96' }} precision={1} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="مؤشرات الأداء" style={{ marginBottom: '16px' }}>
            <Statistic title="العائد على الأصول" value={15.5} suffix="%" valueStyle={{ color: '#52c41a' }} precision={1} />
            <Statistic title="العائد على حقوق الملكية" value={22.3} suffix="%" valueStyle={{ color: '#52c41a' }} precision={1} />
            <Statistic title="معدل دوران الأصول" value={1.8} valueStyle={{ color: '#1890ff' }} precision={1} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="تحليل النمو" style={{ marginBottom: '16px' }}>
            <Statistic title="نمو الإيرادات" value={12.5} suffix="%" valueStyle={{ color: '#52c41a' }} precision={1} />
            <Statistic title="نمو الأرباح" value={18.7} suffix="%" valueStyle={{ color: '#52c41a' }} precision={1} />
            <Statistic title="نمو الأصول" value={8.3} suffix="%" valueStyle={{ color: '#1890ff' }} precision={1} />
          </Card>
        </Col>
      </Row>
    </div>
  )

  const renderCostCentersReport = () => (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h2 style={{ margin: 0, color: '#f5222d' }}>🏢 تقارير مراكز التكلفة</h2>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            تحليل التكاليف حسب مراكز التكلفة المختلفة
          </p>
        </div>
        <Space>
          <UnifiedPrintButton
            data={{
              title: 'تقرير مراكز التكلفة',
              subtitle: `من ${dateRange?.[0]?.format('YYYY-MM-DD')} إلى ${dateRange?.[1]?.format('YYYY-MM-DD')}`,
              date: new Date().toLocaleDateString('ar-SA'),
              items: [
                {
                  id: 1,
                  name: 'قسم الأثاث',
                  description: 'تكاليف قسم إنتاج الأثاث',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalPaymentVouchers * 0.55,
                  total: stats.totalPaymentVouchers * 0.55
                },
                {
                  id: 2,
                  name: 'قسم الدهانات',
                  description: 'تكاليف قسم الدهانات والتشطيبات',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalPaymentVouchers * 0.25,
                  total: stats.totalPaymentVouchers * 0.25
                },
                {
                  id: 3,
                  name: 'قسم الإدارة',
                  description: 'التكاليف الإدارية والعمومية',
                  quantity: 1,
                  unit: 'مبلغ',
                  unitPrice: stats.totalPaymentVouchers * 0.2,
                  total: stats.totalPaymentVouchers * 0.2
                }
              ],
              total: stats.totalPaymentVouchers,
              notes: `أعلى مركز تكلفة: قسم الأثاث (${(stats.totalPaymentVouchers * 0.55).toLocaleString()} ₪)\nثاني أعلى مركز: قسم الدهانات (${(stats.totalPaymentVouchers * 0.25).toLocaleString()} ₪)\nالتكاليف الإدارية: ${(stats.totalPaymentVouchers * 0.2).toLocaleString()} ₪`
            }}
            type="report"
            subType="financial"
            buttonText="طباعة مراكز التكلفة"
            size="middle"
            showDropdown={true}
            _documentId="cost_centers_report"
            onAfterPrint={() => messageApi.success('تم طباعة تقرير مراكز التكلفة بنجاح')}
            onError={() => messageApi.error('فشل في طباعة تقرير مراكز التكلفة')}
          />
          <Button type="default" onClick={() => setActiveView('main')}>قائمة التقارير</Button>
          <Button type="default" icon={<ArrowLeftOutlined />} onClick={onBack}>العودة</Button>
        </Space>
      </div>

      <Card style={{ marginBottom: '24px' }}>
        <Space>
          <span>فترة التقرير:</span>
          <RangePicker value={dateRange} onChange={setDateRange} format="YYYY-MM-DD" />
          <Button type="primary" onClick={loadReportData} loading={loading}>تحديث التقرير</Button>
        </Space>
      </Card>

      <Row gutter={16}>
        <Col span={8}>
          <Card title="قسم الأثاث" style={{ marginBottom: '16px' }}>
            <Statistic title="التكاليف المباشرة" value={stats.totalPaymentVouchers * 0.4} suffix="₪" valueStyle={{ color: '#f5222d' }} />
            <Statistic title="التكاليف غير المباشرة" value={stats.totalPaymentVouchers * 0.15} suffix="₪" valueStyle={{ color: '#fa8c16' }} />
            <Statistic title="إجمالي تكاليف القسم" value={stats.totalPaymentVouchers * 0.55} suffix="₪" valueStyle={{ color: '#f5222d' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="قسم الدهانات" style={{ marginBottom: '16px' }}>
            <Statistic title="التكاليف المباشرة" value={stats.totalPaymentVouchers * 0.25} suffix="₪" valueStyle={{ color: '#f5222d' }} />
            <Statistic title="التكاليف غير المباشرة" value={stats.totalPaymentVouchers * 0.1} suffix="₪" valueStyle={{ color: '#fa8c16' }} />
            <Statistic title="إجمالي تكاليف القسم" value={stats.totalPaymentVouchers * 0.35} suffix="₪" valueStyle={{ color: '#f5222d' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="الإدارة العامة" style={{ marginBottom: '16px' }}>
            <Statistic title="التكاليف الإدارية" value={stats.totalPaymentVouchers * 0.08} suffix="₪" valueStyle={{ color: '#f5222d' }} />
            <Statistic title="التكاليف العامة" value={stats.totalPaymentVouchers * 0.02} suffix="₪" valueStyle={{ color: '#fa8c16' }} />
            <Statistic title="إجمالي تكاليف الإدارة" value={stats.totalPaymentVouchers * 0.1} suffix="₪" valueStyle={{ color: '#f5222d' }} />
          </Card>
        </Col>
      </Row>
    </div>
  )

  const renderContent = () => {
    switch (activeView) {
      case 'balance-sheet':
        return renderBalanceSheetReport()
      case 'income-statement':
        return renderIncomeStatementReport()
      case 'cash-flow':
        return renderCashFlowReport()
      case 'receivables':
        return renderReceivablesReport()
      case 'detailed-costs':
        return renderDetailedCostsReport()
      case 'profitability-analysis':
        return renderProfitabilityAnalysisReport()
      case 'cost-centers':
        return renderCostCentersReport()
      default:
        return renderMainView()
    }
  }

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      {renderContent()}
    </div>
  )
}

export default FinanceReports
