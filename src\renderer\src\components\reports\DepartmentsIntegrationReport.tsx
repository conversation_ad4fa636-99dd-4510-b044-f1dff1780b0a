import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Alert,
  Button,
  Space,
  Tag,
  Typography,
  Divider,
  Spin
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  ArrowLeftOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  InboxOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'

const { Title, Text } = Typography

interface DepartmentsIntegrationReportProps {
  onBack: () => void
}

const DepartmentsIntegrationReport: React.FC<DepartmentsIntegrationReportProps> = ({ onBack }) => {
  const [loading, setLoading] = useState(false)
  const [reportData, setReportData] = useState<any>(null)
  const [integrityData, setIntegrityData] = useState<any>(null)

  useEffect(() => {
    loadReportData()
    validateIntegrity()
  }, [])

  const loadReportData = async () => {
    setLoading(true)
    try {
      Logger.info('DepartmentsIntegrationReport', '🔄 جاري تحميل تقرير تكامل الأقسام...')

      if (!window.electronAPI) {
        Logger.error('DepartmentsIntegrationReport', '❌ window.electronAPI غير متوفر')
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      }

      const response = await window.electronAPI.getDepartmentsIntegrationReport()
      if (response.success) {
        setReportData(response.data)
        Logger.info('DepartmentsIntegrationReport', '✅ تم تحميل تقرير التكامل بنجاح')
      } else {
        throw new Error(response.message || 'فشل في تحميل تقرير التكامل')
      }
    } catch (error) {
      Logger.error('DepartmentsIntegrationReport', 'خطأ في تحميل تقرير التكامل:', error)
    } finally {
      setLoading(false)
    }
  }

  const validateIntegrity = async () => {
    try {
      Logger.info('DepartmentsIntegrationReport', '🔄 جاري التحقق من صحة البيانات...')

      if (!window.electronAPI) {
        Logger.error('DepartmentsIntegrationReport', '❌ window.electronAPI غير متوفر')
        throw new Error('النظام غير متوفر - يرجى التأكد من تشغيل التطبيق بشكل صحيح')
      }

      const response = await window.electronAPI.validateDepartmentsIntegrity()
      if (response.success) {
        setIntegrityData(response.data)
        Logger.info('DepartmentsIntegrationReport', '✅ تم التحقق من صحة البيانات بنجاح')
      } else {
        throw new Error(response.message || 'فشل في التحقق من صحة البيانات')
      }
    } catch (error) {
      Logger.error('DepartmentsIntegrationReport', 'خطأ في التحقق من صحة البيانات:', error)
    }
  }

  const getIssueIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
      case 'warning':
        return <WarningOutlined style={{ color: '#faad14' }} />
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />
      default:
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />
    }
  }

  const getIssueColor = (type: string) => {
    switch (type) {
      case 'error':
        return 'error'
      case 'warning':
        return 'warning'
      case 'info':
        return 'info'
      default:
        return 'success'
    }
  }

  const activitiesColumns = [
    {
      title: 'النوع',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap = {
          'sales_order': { text: 'أمر مبيعات', color: 'blue' },
          'paint_order': { text: 'أمر دهان', color: 'purple' },
          'purchase_order': { text: 'أمر شراء', color: 'green' }
        }
        const typeInfo = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' }
        return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>
      }
    },
    {
      title: 'المرجع',
      dataIndex: 'reference',
      key: 'reference'
    },
    {
      title: 'الكيان المرتبط',
      dataIndex: 'related_entity',
      key: 'related_entity'
    },
    {
      title: 'التاريخ',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleDateString('ar-EG')
    }
  ]

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>جاري تحميل تقرير التكامل...</div>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
            <CheckCircleOutlined style={{ marginLeft: '12px' }} />
            تقرير تكامل الأقسام
          </Title>
          <Text type="secondary">
            تقرير شامل عن ربط وتكامل جميع أقسام النّام
          </Text>
        </div>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={onBack}
          size="large"
        >
          رجوع
        </Button>
      </div>

      {/* تحليل صحة البيانات */}
      {integrityData && (
        <Card style={{ marginBottom: '24px' }}>
          <Title level={4}>تحليل صحة البيانات</Title>
          {integrityData.totalIssues === 0 ? (
            <Alert
              message="ممتاز! لا توجد مشاكل في تكامل البيانات"
              type="success"
              icon={<CheckCircleOutlined />}
              showIcon
            />
          ) : (
            <div>
              <Alert
                message={`تم العثور على ${integrityData.totalIssues} مشكلة تحتاج إلى مراجعة`}
                type="warning"
                icon={<WarningOutlined />}
                showIcon
                style={{ marginBottom: '16px' }}
              />
              <Space direction="vertical" style={{ width: '100%' }}>
                {integrityData.issues.map((issue: any, index: number) => (
                  <Alert
                    key={index}
                    message={issue.message}
                    type={getIssueColor(issue.type)}
                    icon={getIssueIcon(issue.type)}
                    showIcon
                  />
                ))}
              </Space>
            </div>
          )}
        </Card>
      )}

      {reportData && (
        <>
          {/* إحصائيات عامة */}
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <Col span={8}>
              <Card>
                <Statistic
                  title="إجمالي العملاء"
                  value={reportData.customerStats.total_customers}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
                <Divider style={{ margin: '12px 0' }} />
                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="أوامر مبيعات"
                      value={reportData.customerStats.total_sales_orders}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="أوامر دهان"
                      value={reportData.customerStats.total_paint_orders}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
            
            <Col span={8}>
              <Card>
                <Statistic
                  title="إجمالي الأصناف"
                  value={reportData.inventoryStats.total_items}
                  prefix={<InboxOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
                <Divider style={{ margin: '12px 0' }} />
                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="المخازن"
                      value={reportData.inventoryStats.total_warehouses}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="وصفات الإنتاج"
                      value={reportData.inventoryStats.total_recipes}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
            
            <Col span={8}>
              <Card>
                <Statistic
                  title="إجمالي الموردين"
                  value={reportData.supplierStats.total_suppliers}
                  prefix={<ShoppingCartOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
                <Divider style={{ margin: '12px 0' }} />
                <Statistic
                  title="أوامر الشراء"
                  value={reportData.supplierStats.total_purchase_orders}
                  valueStyle={{ fontSize: '14px' }}
                />
              </Card>
            </Col>
          </Row>

          {/* الإيرادات */}
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <Col span={12}>
              <Card>
                <Statistic
                  title="إيرادات المبيعات"
                  value={reportData.customerStats.total_sales_revenue}
                  prefix="₪"
                  precision={2}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card>
                <Statistic
                  title="إيرادات الدهان"
                  value={reportData.customerStats.total_paint_revenue}
                  prefix="₪"
                  precision={2}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>

          {/* الأنشطة الحديثة */}
          <Card>
            <Title level={4}>الأنشطة الحديثة المترابطة</Title>
            <Table
              columns={activitiesColumns}
              dataSource={reportData.recentActivities}
              rowKey={(record) => `${(record as any).type || 'unknown'}-${(record as any).id || Math.random()}`}
              pagination={{ pageSize: 10 }}
              size="small"
            />
          </Card>
        </>
      )}
    </div>
  )
}

export default DepartmentsIntegrationReport
