import React, { useState, useEffect, useCallback } from 'react'
import { ConfigProvider, App as AntdApp } from 'antd'
import EnhancedLoginForm from './components/EnhancedLoginForm'
import Dashboard from './components/Dashboard'
import { User } from './types/global'
import calendarConfig from './utils/calendarConfig'
import './utils/dateConfig'
import './assets/tutorial-styles.css'
import { themeManager } from './utils/themeManager'
import smartNotifications from './utils/smartNotifications'
import { systemInitializer } from './utils/systemInitializer'
import { SimpleLogger as Logger } from './utils/logger'
import { dataBackupManager, checkDataIntegrity, restoreDataBackup } from './utils/dataBackup'
import { ActivationProvider } from './components/activation/ActivationProvider'

const AppContent: React.FC = () => {
  const { message, notification } = AntdApp.useApp()
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [authToken, setAuthToken] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  // تعريف دالة تهيئة النظام باستخدام useCallback لتجنب مشكلة Temporal Dead Zone
  const initializeSystem = useCallback(async () => {
    try {
      // تمرير notification API إلى smartNotifications
      smartNotifications.setNotificationApi(notification)

      // إتاحة خدمة الإشعارات عالمياً
      ;(window as any).smartNotifications = smartNotifications

      // تهيئة النظام الشامل
      const status = await systemInitializer.initializeAll()

      if (status.initialized) {
        Logger.info('App', '✅ تم تهيئة النظام الشامل بنجاح')
        Logger.info('App', '📊 حالة الأنظمة:', status.systems)

        if (status.errors.length > 0) {
          Logger.warn('App', '⚠️ تحذيرات التهيئة:', status.errors)
        }
      } else {
        Logger.error('App', '❌ فشل في تهيئة النظام الشامل')
        Logger.error('App', '🔍 الأخطاء: ' + JSON.stringify(status.errors))
      }
    } catch (error) {
      Logger.error('App', '❌ خطأ في تهيئة النظام الشامل', error as Error)
    }
  }, [notification])

  useEffect(() => {
    const initializeApp = async () => {
      // استعادة الجلسة المحفوظة إذا كانت موجودة
      const preservedAuthToken = sessionStorage.getItem('preserved_authToken')
      const preservedUserData = sessionStorage.getItem('preserved_userData')

      if (preservedAuthToken) {
        Logger.info('App', '🔄 استعادة الجلسة المحفوظة بعد تبديل قاعدة البيانات...')

        // استعادة الجلسة في localStorage
        localStorage.setItem('authToken', preservedAuthToken)
        if (preservedUserData) {
          localStorage.setItem('userData', preservedUserData)
        }

        // استعادة الجلسة في الحالة (state) مباشرة
        setAuthToken(preservedAuthToken)

        // محاولة استعادة بيانات المستخدم
        if (preservedUserData) {
          try {
            const userData = JSON.parse(preservedUserData)
            setCurrentUser(userData)
            Logger.success('App', '✅ تم استعادة بيانات المستخدم من الجلسة المحفوظة')
          } catch (parseError) {
            Logger.error('App', 'خطأ في تحليل بيانات المستخدم المحفوظة:', parseError)
          }
        }

        // حذف البيانات المؤقتة
        sessionStorage.removeItem('preserved_authToken')
        sessionStorage.removeItem('preserved_userData')

        Logger.success('App', '✅ تم استعادة الجلسة بنجاح بعد تبديل قاعدة البيانات')
      }

      // تطبيق تكوين التقويم الميلادي
      calendarConfig.applyGregorianCalendarConfig()

      // فحص سلامة البيانات واستعادة النسخة الاحتياطية إذا لزم الأمر
      initializeDataBackup()

      // تهيئة نظام الثيم
      initializeTheme()

      // تهيئة النظام الشامل
      await initializeSystem()

      // فحص الجلسة الموجودة عند تحميل التطبيق (فقط إذا لم تكن هناك جلسة محفوظة)
      if (!preservedAuthToken) {
        checkExistingSession()
      } else {
        // إنهاء التحميل إذا تم استعادة الجلسة
        setLoading(false)
      }
    }

    initializeApp()
  }, [initializeSystem])

  // معالج أحداث تبديل قاعدة البيانات
  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _handleDatabaseSwitch = (event: any, data: any) => {
      Logger.info('App', '🔄 تم تبديل قاعدة البيانات:', data)

      // إظهار رسالة للمستخدم
      if (data.message) {
        const description = data.preserveSession
          ? `${data.message} - تم الحفاظ على جلستك الحالية`
          : data.message

        notification.success({
          message: 'تبديل قاعدة البيانات',
          description: description,
          duration: 4
        })
      }

      // إعادة تحميل الصفحة مع تأخير أطول للسماح بإكمال العمليات
      setTimeout(() => {
        Logger.info('App', '🔄 إعادة تحميل التطبيق لتطبيق تبديل قاعدة البيانات...')

        if (data.preserveSession) {
          // الحفاظ على الجلسة - حفظ authToken وبيانات المستخدم قبل إعادة التحميل
          const authToken = localStorage.getItem('authToken')
          let userData = localStorage.getItem('userData')

          // إذا لم تكن بيانات المستخدم محفوظة في localStorage، احفظها من الحالة الحالية
          if (!userData && currentUser) {
            userData = JSON.stringify(currentUser)
            localStorage.setItem('userData', userData)
          }

          Logger.info('App', '💾 حفظ بيانات الجلسة قبل إعادة التحميل...')
          Logger.info('App', `Token: ${authToken ? 'موجود' : 'غير موجود'}`)
          Logger.info('App', `User Data: ${userData ? 'موجود' : 'غير موجود'}`)

          // حفظ البيانات في sessionStorage (يبقى أثناء إعادة التحميل)
          if (authToken) {
            sessionStorage.setItem('preserved_authToken', authToken)
          }
          if (userData) {
            sessionStorage.setItem('preserved_userData', userData)
          }

          // إعادة التحميل
          window.location.reload()
        } else {
          // حذف الجلسة وإعادة التحميل
          localStorage.removeItem('authToken')
          localStorage.removeItem('userData')
          window.location.reload()
        }
      }, 3000) // زيادة التأخير إلى 3 ثوان
    }

    // معالج التنقل
    const _handleNavigateTo = (event: any, path: string) => {
      Logger.info('App', '🔄 طلب التنقل إلى:', path)

      // تحديد المسار المناسب حسب الطلب
      let targetPath = path
      if (path === '/items') {
        targetPath = '#/items'
      } else if (path === '/materials') {
        targetPath = '#/materials'
      } else if (path === '/recipes') {
        targetPath = '#/production/recipes'
      }

      // التنقل إلى المسار المطلوب
      if (targetPath.startsWith('#/')) {
        window.location.hash = targetPath
      } else {
        window.location.hash = `#${targetPath}`
      }

      Logger.info('App', '✅ تم التنقل إلى:', window.location.hash)
    }

    // تسجيل معالجات الأحداث
    if (window.electronAPI?.on) {
      window.electronAPI.on('navigate-to', _handleNavigateTo)
      // window.electronAPI.on('database-switched', _handleDatabaseSwitch) // معطل مؤقتاً
    }

    // تنظيف معالجات الأحداث عند إلغاء التحميل
    return () => {
      if (window.electronAPI?.removeListener) {
        window.electronAPI.removeListener('navigate-to', _handleNavigateTo)
        // window.electronAPI.removeListener('database-switched', _handleDatabaseSwitch)
      }
    }
  }, [])



  const initializeDataBackup = () => {
    try {
      // فحص سلامة البيانات
      if (!checkDataIntegrity()) {
        Logger.warn('App', 'تم اكتشاف بيانات مفقودة، محاولة الاستعادة من النسخة الاحتياطية...')
        if (restoreDataBackup()) {
          Logger.info('App', '✅ تم استعادة البيانات من النسخة الاحتياطية بنجاح')
        }
      }

      // بدء النسخ الاحتياطي التلقائي
      dataBackupManager.startAutoBackup()
    } catch (error) {
      Logger.error('App', 'خطأ في تهيئة النسخ الاحتياطي:', error as Error)
    }
  }

  const initializeTheme = async () => {
    try {
      // تحميل الثيم من قاعدة البيانات
      await themeManager.loadThemeFromDatabase()

      // إضافة مستمع لتغييرات الثيم
      themeManager.addListener(() => {
        // سيتم تحديث الثيم تلقائياً في مكون App الرئيسي
      })



      // تطبيق الثيم على DOM
      themeManager.getCurrentTheme()

      // إتاحة مدير الثيم عالمياً للمكونات الأخرى
      ;(window as any).themeManager = themeManager

      Logger.info('App', '✅ تم تهيئة نظام الثيم بنجاح')
    } catch (error) {
      Logger.error('App', '❌ خطأ في تهيئة نظام الثيم', error as Error)
    }
  }



  const checkExistingSession = async () => {
    try {
      const token = localStorage.getItem('authToken')
      if (token && window.electronAPI) {
        // محاولة التحقق من الجلسة مع إعادة المحاولة
        let attempts = 0
        const maxAttempts = 3
        let response = null

        while (attempts < maxAttempts && !response?.success) {
          try {
            response = await window.electronAPI.verifySession(token)
            if (response.success && response.data?.user) {
              setCurrentUser(response.data.user)
              setAuthToken(token)
              localStorage.setItem('userData', JSON.stringify(response.data.user))
              Logger.info('App', '✅ تم التحقق من الجلسة بنجاح')
              return
            }
          } catch (attemptError) {
            Logger.warn('App', `محاولة ${attempts + 1} فشلت في التحقق من الجلسة:`, attemptError)
          }

          attempts++
          if (attempts < maxAttempts) {
            // انتظار قصير قبل إعادة المحاولة
            await new Promise(resolve => setTimeout(resolve, 1000))
          }
        }

        // إذا فشلت جميع المحاولات
        Logger.warn('App', 'فشل في التحقق من الجلسة بعد عدة محاولات')
        localStorage.removeItem('authToken')
      }
    } catch (error) {
      Logger.error('App', 'خطأ في فحص الجلسة', error as Error)
      localStorage.removeItem('authToken')
    } finally {
      setLoading(false)
    }
  }

  const handleLogin = async (credentials: { username: string; password: string; rememberMe?: boolean }) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.login(credentials.username, credentials.password)
        if (response.success && response.user && response.token) {
          setCurrentUser(response.user)
          setAuthToken(response.token)
          localStorage.setItem('authToken', response.token)
          localStorage.setItem('userData', JSON.stringify(response.user))

          // بدء مراقبة الإشعارات الذكية
          smartNotifications.start()

          return response
        } else {
          return { success: false, message: response.message || 'فشل في تسجيل الدخول' }
        }
      } else {
        // محاكاة تسجيل الدخول للتطوير
        if (credentials.username === 'admin' && credentials.password === '123456') {
          const mockUser: User = {
            id: 1,
            username: 'admin',
            full_name: 'مدير النظام',
            email: '<EMAIL>',
            role: 'admin',
            is_active: true,
            created_at: new Date().toISOString()
          }
          const mockToken = 'mock-token-' + Date.now()
          setCurrentUser(mockUser)
          setAuthToken(mockToken)
          localStorage.setItem('authToken', mockToken)
          localStorage.setItem('userData', JSON.stringify(mockUser))
          return { success: true, user: mockUser, token: mockToken }
        } else {
          return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' }
        }
      }
    } catch (error) {
      Logger.error('App', 'خطأ في تسجيل الدخول', error as Error)
      return { success: false, message: 'حدث خطأ أثناء تسجيل الدخول' }
    }
  }

  const handleLogout = async () => {
    try {
      if (authToken && window.electronAPI) {
        await window.electronAPI.logout(authToken)
      }

      setCurrentUser(null)
      setAuthToken(null)
      localStorage.removeItem('authToken')
      localStorage.removeItem('userData')
      message.success('تم تسجيل الخروج بنجاح')
    } catch (error) {
      Logger.error('App', 'خطأ في تسجيل الخروج', error as Error)
      message.error('حدث خطأ أثناء تسجيل الخروج')
    }
  }

  if (loading) {
    return (
      <div style={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #0078D4 0%, #106EBE 100%)',
        color: 'white',
        fontSize: '18px'
      }}>
        جاري التحميل...
      </div>
    )
  }

  return (
    <>
      {currentUser ? (
        <Dashboard
          user={currentUser}
          onLogout={handleLogout}
          authToken={authToken}
        />
      ) : (
        <EnhancedLoginForm onLogin={handleLogin} />
      )}
    </>
  )
}

const MainApp: React.FC = () => {
  const [appThemeConfig, setAppThemeConfig] = useState(themeManager.getAntdThemeConfig())

  useEffect(() => {
    // إضافة مستمع لتغييرات الثيم
    const handleThemeChange = () => {
      setAppThemeConfig(themeManager.getAntdThemeConfig())
    }

    themeManager.addListener(handleThemeChange)

    return () => {
      themeManager.removeListener(handleThemeChange)
    }
  }, [])

  return (
    <ConfigProvider
      locale={calendarConfig.antdLocaleConfig as any}
      direction="rtl"
      theme={appThemeConfig}
    >
      <AntdApp>
        <ActivationProvider>
          <AppContent />
        </ActivationProvider>
      </AntdApp>
    </ConfigProvider>
  )
}

export default MainApp
