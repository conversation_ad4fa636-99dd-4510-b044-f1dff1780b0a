import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Space,
  Popconfirm,
  // message, // سيتم استخدامها لاحقاً
  Card,
  Row,
  Col,
  Statistic,
  Tag,
  Select,
  Switch,
  App} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import * as XLSX from 'xlsx'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  HomeOutlined,
  ArrowLeftOutlined,
  SearchOutlined,
  BarcodeOutlined,
  PrinterOutlined,
  DownloadOutlined
} from '@ant-design/icons'
// تم إزالة imports errorHandler لتجنب static function warnings
import PermissionGuard, { PermissionButton } from '../common/PermissionGuard'
import { Permission, initializeDefaultUser } from '../../utils/permissions'


const { Option } = Select

interface Customer {
  id: number
  code: string
  name: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  credit_limit: number
  payment_terms: number
  is_active: boolean
  created_at: string
  updated_at: string
}

interface CustomerManagementProps {
  onBack: () => void
}

const CustomerManagement: React.FC<CustomerManagementProps> = ({ onBack }) => {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)
  const [form] = Form.useForm()
  const [searchText, setSearchText] = useState('')

  // حالات التحميل التدريجي
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `${range[0]}-${range[1]} من ${total} عميل`
  })
  const [_filteredCustomers, setFilteredCustomers] = useState<Customer[]>([])
  const [allCustomers, setAllCustomers] = useState<Customer[]>([])

  // استخدام App context للرسائل
  const { message: messageApi } = App.useApp()

  useEffect(() => {
    // تهيئة المستخدم الافتراضي
    initializeDefaultUser()
    loadCustomers(1, pagination.pageSize, searchText)
  }, [])

  // معالج البحث مع debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadCustomers(1, pagination.pageSize, searchText)
    }, 500) // انتّار 500ms بعد توقف الكتابة

    return () => clearTimeout(timeoutId)
  }, [searchText])

  // معالج تغيير الصفحة
  const handleTableChange = (paginationConfig: any) => {
    const { current, pageSize } = paginationConfig
    loadCustomers(current, pageSize, searchText)
  }

  // معالج البحث
  const handleSearch = (value: string) => {
    setSearchText(value)
    // سيتم تشغيل useEffect تلقائياً
  }

  const loadCustomers = async (page: number = 1, pageSize: number = 20, search: string = '') => {
    setLoading(true)
    try {
      if (window.electronAPI) {
        // محاولة استخدام API مع pagination إذا كان متوفراً
        let response
        if (window.electronAPI.getCustomersPaginated) {
          response = await window.electronAPI.getCustomersPaginated({
            page,
            pageSize,
            search: search.trim()
          })
        } else {
          // fallback للطريقة القديمة
          response = await window.electronAPI.getCustomers()
        }

        if (response.success) {
          const customersData = response.data || []

          if (response.pagination) {
            // إذا كان الخادم يدعم pagination
            setCustomers(customersData)
            setFilteredCustomers(customersData)
            setPagination(prev => ({
              ...prev,
              current: response.pagination.page,
              total: response.pagination.total,
              pageSize: response.pagination.pageSize
            }))
          } else {
            // معالجة محلية للبيانات
            setAllCustomers(customersData)
            applyLocalFiltering(customersData, search, page, pageSize)
          }

          Logger.info('CustomerManagement', '✅ تم تحميل ${customersData.length} عميل بنجاح')
        } else {
          messageApi.error(response.message || 'فشل في تحميل العملاء')
        }
      } else {
        messageApi.error('لا يمكن الوصول إلى قاعدة البيانات')
      }
    } catch (error) {
      messageApi.error('حدث خطأ أثناء تحميل العملاء')
    } finally {
      setLoading(false)
    }
  }

  // دالة للفلترة المحلية عندما لا يدعم الخادم pagination
  const applyLocalFiltering = (data: Customer[], search: string, page: number, pageSize: number) => {
    let filtered = data

    if (search.trim()) {
      filtered = data.filter(customer =>
        customer.name.toLowerCase().includes(search.toLowerCase()) ||
        customer.code.toLowerCase().includes(search.toLowerCase()) ||
        (customer.contact_person && customer.contact_person.toLowerCase().includes(search.toLowerCase())) ||
        (customer.phone && customer.phone.includes(search)) ||
        (customer.email && customer.email.toLowerCase().includes(search.toLowerCase()))
      )
    }

    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedData = filtered.slice(startIndex, endIndex)

    setFilteredCustomers(paginatedData)
    setCustomers(paginatedData)
    setPagination(prev => ({
      ...prev,
      current: page,
      total: filtered.length,
      pageSize
    }))
  }

  const generateCode = async () => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.generateCustomerCode()
        if (response.success && response.data) {
          form.setFieldsValue({ code: response.data.code })
          messageApi.success('تم إنشاء الكود تلقائياً')
        }
      }
    } catch (error) {
      messageApi.error('فشل في إنشاء الكود')
    }
  }

  const checkCodeUniqueness = async (code: string): Promise<boolean> => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.getCustomers()
        if (response.success && response.data) {
          const existingCustomer = response.data.find((customer: Customer) =>
            customer.code.toLowerCase() === code.toLowerCase() &&
            (!editingCustomer || customer.id !== editingCustomer.id)
          )
          return !existingCustomer
        }
      }
      return true
    } catch (error) {
      Logger.error('CustomerManagement', 'خطأ في التحقق من تفرد الكود:', error)
      return true // في حالة الخطأ، نسمح بالمتابعة
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      // التحقق من تفرد الكود أولاً
      const isCodeUnique = await checkCodeUniqueness(values.code)
      if (!isCodeUnique) {
        const errorMessage = `كود العميل "${values.code}" موجود مسبقاً`
        messageApi.error(errorMessage)
        form.setFields([{
          name: 'code',
          errors: [errorMessage]
        }])
        return
      }

      if (window.electronAPI) {
        let response
        if (editingCustomer) {
          response = await window.electronAPI.updateCustomer(editingCustomer.id, values)
        } else {
          response = await window.electronAPI.createCustomer(values)
        }

        if (response.success) {
          const successMessage = editingCustomer ? 'تم تحديث العميل بنجاح' : 'تم إضافة العميل بنجاح'
          messageApi.success(successMessage)
          setModalVisible(false)
          form.resetFields()
          setEditingCustomer(null)
          loadCustomers()
        } else {
          messageApi.error(response.message || 'فشل في حفّ العميل')
        }
      } else {
        messageApi.error('لا يمكن الوصول إلى قاعدة البيانات')
      }
    } catch (error) {
      messageApi.error('حدث خطأ أثناء حفّ العميل')
    }
  }

  const handleEdit = (customer: Customer) => {
    setEditingCustomer(customer)
    form.setFieldsValue(customer)
    setModalVisible(true)
  }

  const handleDelete = async (customerId: number) => {
    try {
      if (window.electronAPI) {
        const response = await window.electronAPI.deleteCustomer(customerId)
        if (response.success) {
          messageApi.success('تم حذف العميل بنجاح')
          loadCustomers()
        } else {
          messageApi.error(response.message || 'فشل في حذف العميل')
        }
      }
    } catch (error) {
      messageApi.error('حدث خطأ أثناء حذف العميل')
    }
  }

  const handleAdd = () => {
    setEditingCustomer(null)
    setModalVisible(true)

    // تأخير إعداد النموذج حتى يتم عرض الـ Modal
    setTimeout(() => {
      if (form) {
        form.resetFields()
        form.setFieldsValue({
          payment_terms: 30,
          credit_limit: 0,
          is_active: true
        })
        generateCode()
      }
    }, 100)
  }

  // دالة تصدير Excel محسنة ومصححة لحل مشاكل فتح الملف
  const handleExportExcel = async () => {
    try {
      if (customers.length === 0) {
        messageApi.warning('لا توجد بيانات عملاء للتصدير')
        return
      }

      // تحضير البيانات للتصدير مع تنظيف وتنسيق صحيح
      const exportData = customers.map(customer => ({
        'Customer_Code': customer.code || '',
        'Customer_Name': customer.name || '',
        'Contact_Person': customer.contact_person || '',
        'Phone_Number': customer.phone || '',
        'Email_Address': customer.email || '',
        'Address': customer.address || '',
        'Payment_Terms_Days': customer.payment_terms || 0,
        'Credit_Limit': customer.credit_limit || 0,
        'Status': customer.is_active ? 'Active' : 'Inactive',
        'Creation_Date': customer.created_at ? new Date(customer.created_at).toISOString().split('T')[0] : ''
      }))

      // إنشاء workbook جديد مع إعدادات محسنة
      const workbook = XLSX.utils.book_new()

      // تعيين خصائص الـ workbook
      workbook.Props = {
        Title: 'Customer Report',
        Subject: 'Customer Management System Export',
        Author: 'ZET.IA Accounting System',
        CreatedDate: new Date()
      }

      // إنشاء ورقة العمل مع تنسيق محسن
      const worksheet = XLSX.utils.json_to_sheet(exportData, {
        header: [
          'Customer_Code',
          'Customer_Name',
          'Contact_Person',
          'Phone_Number',
          'Email_Address',
          'Address',
          'Payment_Terms_Days',
          'Credit_Limit',
          'Status',
          'Creation_Date'
        ]
      })

      // تطبيق تنسيق على الأعمدة
      const _range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')

      // تعيين عرض الأعمدة
      worksheet['!cols'] = [
        { wch: 15 }, // Customer_Code
        { wch: 25 }, // Customer_Name
        { wch: 20 }, // Contact_Person
        { wch: 15 }, // Phone_Number
        { wch: 25 }, // Email_Address
        { wch: 30 }, // Address
        { wch: 18 }, // Payment_Terms_Days
        { wch: 15 }, // Credit_Limit
        { wch: 12 }, // Status
        { wch: 15 }  // Creation_Date
      ]

      // إضافة الورقة إلى workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Customers')

      // إضافة ورقة معلومات التقرير بتنسيق محسن
      const reportData = [
        ['Customer Report Summary'],
        [''],
        ['Report Date', new Date().toISOString().split('T')[0]],
        ['Total Customers', customers.length],
        ['Active Customers', customers.filter(c => c.is_active).length],
        ['Inactive Customers', customers.filter(c => !c.is_active).length],
        ['Total Credit Limits', customers.reduce((sum, c) => sum + (c.credit_limit || 0), 0)]
      ]

      const summaryWorksheet = XLSX.utils.aoa_to_sheet(reportData)
      summaryWorksheet['!cols'] = [{ wch: 20 }, { wch: 20 }]
      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'Summary')

      // تحديد اسم الملف بتنسيق آمن
      const timestamp = new Date().toISOString().split('T')[0]
      const fileName = `Customer_Report_${timestamp}.xlsx`

      // استخدام Electron API لحفظ الملف
      if (window.electronAPI && window.electronAPI.saveExcelFile) {
        // تحويل workbook إلى buffer مع إعدادات محسنة
        const buffer = XLSX.write(workbook, {
          type: 'buffer',
          bookType: 'xlsx',
          compression: true,
          Props: {
            Title: 'Customer Report',
            Subject: 'Export from ZET.IA System'
          }
        })

        const result = await window.electronAPI.saveExcelFile(buffer, fileName)

        if (result.success) {
          messageApi.success(`تم تصدير ${customers.length} عميل بنجاح إلى ملف Excel`)
        } else {
          messageApi.error(result.message || 'فشل في حفظ الملف')
        }
      } else {
        // fallback للمتصفح العادي
        XLSX.writeFile(workbook, fileName, {
          compression: true,
          Props: {
            Title: 'Customer Report',
            Subject: 'Export from ZET.IA System'
          }
        })
        messageApi.success(`تم تصدير ${customers.length} عميل بنجاح إلى ملف Excel`)
      }
    } catch (error) {
      Logger.error('CustomerManagement', 'خطأ في تصدير Excel:', error)
      messageApi.error('حدث خطأ أثناء تصدير البيانات: ' + (error as Error).message)
    }
  }

  // حساب الإحصائيات من جميع البيانات (ليس فقط الصفحة الحالية)
  const stats = {
    totalCustomers: pagination.total || customers.length,
    activeCustomers: allCustomers.length > 0 ? allCustomers.filter(c => c.is_active).length : customers.filter(c => c.is_active).length,
    totalCreditLimit: allCustomers.length > 0 ? allCustomers.reduce((sum, c) => sum + c.credit_limit, 0) : customers.reduce((sum, c) => sum + c.credit_limit, 0),
    avgPaymentTerms: (() => {
      const dataSource = allCustomers.length > 0 ? allCustomers : customers
      return dataSource.length > 0 ? Math.round(dataSource.reduce((sum, c) => sum + c.payment_terms, 0) / dataSource.length) : 0
    })()
  }

  const columns = [
    {
      title: 'الكود',
      dataIndex: 'code',
      key: 'code',
      width: 100,
      render: (code: string) => <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{code}</span>
    },
    {
      title: 'اسم العميل',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Customer) => (
        <Space>
          <UserOutlined style={{ color: record.is_active ? '#52c41a' : '#ff4d4f' }} />
          <span style={{ fontWeight: 'bold' }}>{name}</span>
        </Space>
      )
    },
    {
      title: 'الشخص المسؤول',
      dataIndex: 'contact_person',
      key: 'contact_person',
      render: (contact: string) => contact || '-'
    },
    {
      title: 'الهاتف',
      dataIndex: 'phone',
      key: 'phone',
      render: (phone: string) => phone ? (
        <Space>
          <PhoneOutlined style={{ color: '#1890ff' }} />
          {phone}
        </Space>
      ) : '-'
    },
    {
      title: 'البريد الإلكتروني',
      dataIndex: 'email',
      key: 'email',
      render: (email: string) => email ? (
        <Space>
          <MailOutlined style={{ color: '#1890ff' }} />
          {email}
        </Space>
      ) : '-'
    },
    {
      title: 'الحد الائتماني',
      dataIndex: 'credit_limit',
      key: 'credit_limit',
      render: (limit: number) => (
        <span style={{ color: limit > 0 ? '#52c41a' : '#666' }}>
          ₪{limit.toLocaleString()}
        </span>
      )
    },
    {
      title: 'شروط الدفع',
      dataIndex: 'payment_terms',
      key: 'payment_terms',
      render: (terms: number) => `${terms} يوم`
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'نشط' : 'غير نشط'}
        </Tag>
      )
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 120,
      render: (_, record: Customer) => (
        <Space>
          <PermissionButton
            permission={Permission.CUSTOMERS_EDIT}
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            title="تعديل العميل"
          >
            تعديل
          </PermissionButton>
          <PermissionGuard permission={Permission.CUSTOMERS_DELETE} showMessage={false}>
            <Popconfirm
              title="هل أنت متأكد من حذف هذا العميل؟"
              description="سيتم حذف العميل وجميع البيانات المرتبطة به نهائياً"
              onConfirm={() => handleDelete(record.id)}
              okText="نعم، احذف"
              cancelText="إلغاء"
              okType="danger"
            >
              <Button
                type="primary"
                danger
                size="small"
                icon={<DeleteOutlined />}
                title="حذف العميل"
              />
            </Popconfirm>
          </PermissionGuard>
        </Space>
      )
    }
  ]

  return (
    <PermissionGuard permission={Permission.CUSTOMERS_VIEW}>
      <div style={{ padding: '24px' }}>
        <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ margin: 0, color: '#1890ff', fontSize: '28px' }}>
              <UserOutlined style={{ marginLeft: '12px' }} />
              إدارة العملاء
            </h1>
            <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '16px' }}>
              إدارة شاملة لبيانات العملاء والحدود الائتمانية
            </p>
          </div>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={onBack}
            size="large"
          >
            رجوع
          </Button>
        </div>

      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي العملاء"
              value={stats.totalCustomers}
              valueStyle={{ color: '#1890ff' }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="العملاء النشطين"
              value={stats.activeCustomers}
              valueStyle={{ color: '#52c41a' }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="إجمالي الحدود الائتمانية"
              value={stats.totalCreditLimit}
              valueStyle={{ color: '#722ed1' }}
              prefix="₪"
              precision={0}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="متوسط شروط الدفع"
              value={stats.avgPaymentTerms}
              valueStyle={{ color: '#fa8c16' }}
              suffix="يوم"
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <PermissionButton
              permission={Permission.CUSTOMERS_CREATE}
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              size="large"
            >
              إضافة عميل جديد
            </PermissionButton>

            <Button
              type="default"
              icon={<DownloadOutlined />}
              onClick={handleExportExcel}
              size="large"
            >
              تصدير Excel
            </Button>
            <Input
              placeholder="البحث في العملاء (الاسم، الكود، الهاتف، البريد)..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ width: 400 }}
              allowClear
            />
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={customers}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
            pageSizeOptions: ['10', '20', '50', '100'],
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} من ${total} عميل`
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      <Modal
        title={editingCustomer ? 'تعديل العميل' : 'عميل جديد'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false)
          form.resetFields()
          setEditingCustomer(null)
        }}
        footer={null}
        width={800}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ payment_terms: 30, credit_limit: 0, is_active: true }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="code"
                label="كود العميل"
                rules={[{ required: true, message: 'يرجى إدخال كود العميل' }]}
              >
                <Input
                  placeholder="أدخل كود العميل"
                  addonAfter={
                    !editingCustomer && (
                      <Button
                        size="small"
                        onClick={generateCode}
                        icon={<BarcodeOutlined />}
                      >
                        إنشاء تلقائي
                      </Button>
                    )
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="اسم العميل"
                rules={[{ required: true, message: 'يرجى إدخال اسم العميل' }]}
              >
                <Input placeholder="اسم العميل" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="contact_person"
                label="الشخص المسؤول"
              >
                <Input placeholder="الشخص المسؤول" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="رقم الهاتف"
              >
                <Input placeholder="رقم الهاتف" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="البريد الإلكتروني"
                rules={[{ type: 'email', message: 'يرجى إدخال بريد إلكتروني صحيح' }]}
              >
                <Input placeholder="البريد الإلكتروني" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="address"
                label="العنوان"
              >
                <Input placeholder="العنوان" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="credit_limit"
                label="الحد الائتماني (₪)"
                rules={[{ required: true, message: 'يرجى إدخال الحد الائتماني' }]}
              >
                <Input type="number" placeholder="0" min="0" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="payment_terms"
                label="شروط الدفع (بالأيام)"
                rules={[{ required: true, message: 'يرجى إدخال شروط الدفع' }]}
              >
                <Input type="number" placeholder="30" min="1" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="is_active"
                label="الحالة"
                valuePropName="checked"
              >
                <Switch checkedChildren="نشط" unCheckedChildren="غير نشط" />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'left', marginTop: '24px' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                إلغاء
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCustomer ? 'تحديث' : 'إضافة'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
      </div>
    </PermissionGuard>
  )
}

const CustomerManagementWithApp: React.FC<CustomerManagementProps> = (props) => {
  return (
    <App>
      <CustomerManagement {...props} />
    </App>
  )
}

export default CustomerManagementWithApp
