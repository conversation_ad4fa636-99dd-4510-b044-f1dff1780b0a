/**
 * محرر الأعمدة المدمج للقوالب
 * يدمج ميزات ImprovedColumnEditor مع إعدادات الطباعة
 */

import React, { useState, useCallback } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Input,
  Select,
  Switch,
  InputNumber,
  Tooltip,
  Alert,
  Tag,
  List,
  Popconfirm,
  message,
  Form,
  Divider,
  Checkbox,
  Slider
} from 'antd';
import {
  SettingOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  DragOutlined,
  InfoCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  CopyOutlined,
  ReloadOutlined,
  PrinterOutlined,
  TableOutlined
} from '@ant-design/icons';

import { EnhancedColumnConfig } from '../../types/enhancedTemplateTypes';

const { Title, Text } = Typography;
const { Option } = Select;

interface IntegratedColumnEditorProps {
  columns: EnhancedColumnConfig[];
  onColumnsChange: (columns: EnhancedColumnConfig[]) => void;
  globalSettings?: any; // الإعدادات العامة للطباعة
  reportContext?: {
    reportType: string;
    reportCategory: string;
    reportTitle: string;
  };
}

const IntegratedColumnEditor: React.FC<IntegratedColumnEditorProps> = ({
  columns,
  onColumnsChange,
  globalSettings,
  reportContext
}) => {
  const [editingColumn, setEditingColumn] = useState<EnhancedColumnConfig | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [addForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // إحصائيات الأعمدة
  const columnStats = {
    total: columns.length,
    visible: columns.filter(col => col.visible).length,
    hidden: columns.filter(col => !col.visible).length,
    printVisible: columns.filter(col => col.printVisible !== false).length,
    sortable: columns.filter(col => col.sortable).length,
    filterable: columns.filter(col => col.filterable).length
  };

  // إضافة عمود جديد
  const handleAddColumn = useCallback((values: any) => {
    const newColumn: EnhancedColumnConfig = {
      key: values.key,
      title: values.title,
      dataIndex: values.dataIndex || values.key,
      type: values.type || 'text',
      visible: true,
      width: values.width || 'auto',
      align: values.align || 'right',
      sortable: values.sortable || false,
      filterable: values.filterable || false,
      fixed: values.fixed || false,
      
      // إعدادات الطباعة المحسنة
      printVisible: values.printVisible !== false,
      printWidth: values.printWidth || values.width || 'auto',
      printAlign: values.printAlign || values.align || 'right',
      printOrder: columns.length + 1,
      
      // تنسيق الطباعة
      printFormat: {
        bold: values.bold || false,
        italic: values.italic || false,
        underline: values.underline || false,
        color: values.color || globalSettings?.colors?.textColor || '#000000',
        backgroundColor: values.backgroundColor || 'transparent'
      },
      
      // تنسيق الأرقام
      numberFormat: values.type === 'number' || values.type === 'currency' ? {
        decimals: values.decimals || 2,
        thousandsSeparator: values.thousandsSeparator !== false,
        currency: values.currency || 'ر.س'
      } : undefined,
      
      // تنسيق التاريخ
      dateFormat: values.type === 'date' ? (values.dateFormat || 'YYYY-MM-DD') : undefined
    };

    // التحقق من عدم تكرار المفتاح
    if (columns.some(col => col.key === newColumn.key)) {
      message.error('مفتاح العمود موجود بالفعل');
      return;
    }

    const updatedColumns = [...columns, newColumn];
    onColumnsChange(updatedColumns);
    addForm.resetFields();
    setShowAddForm(false);
    message.success('تم إضافة العمود بنجاح');
  }, [columns, onColumnsChange, addForm, globalSettings]);

  // تحديث عمود موجود
  const handleUpdateColumn = useCallback((values: any) => {
    if (!editingColumn) return;

    const updatedColumns = columns.map(col => 
      col.key === editingColumn.key 
        ? { 
            ...col, 
            ...values,
            printFormat: {
              ...col.printFormat,
              ...values.printFormat
            },
            numberFormat: values.type === 'number' || values.type === 'currency' ? {
              decimals: values.decimals || 2,
              thousandsSeparator: values.thousandsSeparator !== false,
              currency: values.currency || 'ر.س'
            } : undefined
          }
        : col
    );
    
    onColumnsChange(updatedColumns);
    setEditingColumn(null);
    message.success('تم تحديث العمود بنجاح');
  }, [editingColumn, columns, onColumnsChange]);

  // حذف عمود
  const handleDeleteColumn = useCallback((key: string) => {
    const updatedColumns = columns.filter(col => col.key !== key);
    onColumnsChange(updatedColumns);
    message.success('تم حذف العمود بنجاح');
  }, [columns, onColumnsChange]);

  // تبديل رؤية العمود
  const handleToggleVisibility = useCallback((key: string) => {
    const updatedColumns = columns.map(col => 
      col.key === key ? { ...col, visible: !col.visible } : col
    );
    onColumnsChange(updatedColumns);
  }, [columns, onColumnsChange]);

  // تبديل رؤية الطباعة
  const handleTogglePrintVisibility = useCallback((key: string) => {
    const updatedColumns = columns.map(col => 
      col.key === key ? { ...col, printVisible: !col.printVisible } : col
    );
    onColumnsChange(updatedColumns);
  }, [columns, onColumnsChange]);

  // نسخ عمود
  const handleCopyColumn = useCallback((column: EnhancedColumnConfig) => {
    const newColumn = {
      ...column,
      key: `${column.key}_copy`,
      title: `${column.title} (نسخة)`,
      printOrder: columns.length + 1
    };
    
    const updatedColumns = [...columns, newColumn];
    onColumnsChange(updatedColumns);
    message.success('تم نسخ العمود بنجاح');
  }, [columns, onColumnsChange]);

  // إعادة ترتيب الأعمدة (محاكاة السحب والإفلات)
  const moveColumn = useCallback((fromIndex: number, toIndex: number) => {
    const updatedColumns = [...columns];
    const [movedColumn] = updatedColumns.splice(fromIndex, 1);
    updatedColumns.splice(toIndex, 0, movedColumn);
    
    // تحديث ترتيب الطباعة
    updatedColumns.forEach((col, index) => {
      col.printOrder = index + 1;
    });
    
    onColumnsChange(updatedColumns);
    message.success('تم إعادة ترتيب الأعمدة');
  }, [columns, onColumnsChange]);

  // إعادة تعيين الأعمدة
  const handleResetColumns = useCallback(() => {
    const resetColumns = columns.map(col => ({
      ...col,
      visible: true,
      printVisible: true,
      width: 'auto',
      printWidth: 'auto',
      align: 'right' as const,
      printAlign: 'right' as const,
      printFormat: {
        bold: false,
        italic: false,
        underline: false,
        color: globalSettings?.colors?.textColor || '#000000',
        backgroundColor: 'transparent'
      }
    }));
    
    onColumnsChange(resetColumns);
    message.success('تم إعادة تعيين إعدادات الأعمدة');
  }, [columns, onColumnsChange, globalSettings]);

  // بدء تعديل عمود
  const startEditColumn = useCallback((column: EnhancedColumnConfig) => {
    setEditingColumn(column);
    editForm.setFieldsValue({
      ...column,
      ...column.printFormat,
      ...column.numberFormat,
      dateFormat: column.dateFormat
    });
  }, [editForm]);

  // إلغاء التعديل
  const cancelEdit = useCallback(() => {
    setEditingColumn(null);
    editForm.resetFields();
  }, [editForm]);

  return (
    <div>
      {/* معلومات السياق */}
      {reportContext && (
        <Alert
          message={`إدارة أعمدة ${reportContext.reportTitle}`}
          description={`فئة: ${reportContext.reportCategory} | نوع: ${reportContext.reportType}`}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* إحصائيات سريعة */}
      <Row gutter={[8, 8]} style={{ marginBottom: 16 }}>
        <Col span={4}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Text strong style={{ color: '#1890ff' }}>{columnStats.visible}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>مرئية</Text>
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Text strong style={{ color: '#ff4d4f' }}>{columnStats.hidden}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>مخفية</Text>
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Text strong style={{ color: '#52c41a' }}>{columnStats.printVisible}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>للطباعة</Text>
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Text strong style={{ color: '#faad14' }}>{columnStats.sortable}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>قابلة للترتيب</Text>
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Text strong style={{ color: '#722ed1' }}>{columnStats.filterable}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>قابلة للتصفية</Text>
          </Card>
        </Col>
        <Col span={4}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              size="small"
              icon={<PlusOutlined />}
              onClick={() => setShowAddForm(true)}
              style={{ width: '100%' }}
            >
              إضافة عمود
            </Button>
          </Card>
        </Col>
      </Row>

      {/* أزرار التحكم */}
      <Space style={{ marginBottom: 16 }}>
        <Popconfirm
          title="هل أنت متأكد من إعادة تعيين جميع الإعدادات؟"
          onConfirm={handleResetColumns}
          okText="نعم"
          cancelText="لا"
        >
          <Button icon={<ReloadOutlined />}>
            إعادة تعيين الكل
          </Button>
        </Popconfirm>
        
        <Button 
          icon={<TableOutlined />}
          onClick={() => {
            // تطبيق إعدادات طباعة موحدة
            const updatedColumns = columns.map(col => ({
              ...col,
              printVisible: col.visible,
              printWidth: col.width,
              printAlign: col.align
            }));
            onColumnsChange(updatedColumns);
            message.success('تم تطبيق إعدادات العرض على الطباعة');
          }}
        >
          تطبيق إعدادات العرض على الطباعة
        </Button>
      </Space>

      {/* قائمة الأعمدة */}
      <div style={{ maxHeight: 400, overflowY: 'auto' }}>
        {columns.map((column, index) => (
          <Card
            key={column.key}
            size="small"
            style={{
              marginBottom: 8,
              border: column.visible ? '1px solid #d9d9d9' : '1px dashed #d9d9d9',
              opacity: column.visible ? 1 : 0.6
            }}
          >
            <Row align="middle" gutter={[8, 0]}>
              <Col flex="none">
                <Space direction="vertical" size={0}>
                  <Tooltip title="تحريك لأعلى">
                    <Button
                      size="small"
                      type="text"
                      icon={<DragOutlined style={{ transform: 'rotate(-90deg)' }} />}
                      onClick={() => index > 0 && moveColumn(index, index - 1)}
                      disabled={index === 0}
                    />
                  </Tooltip>
                  <Tooltip title="تحريك لأسفل">
                    <Button
                      size="small"
                      type="text"
                      icon={<DragOutlined style={{ transform: 'rotate(90deg)' }} />}
                      onClick={() => index < columns.length - 1 && moveColumn(index, index + 1)}
                      disabled={index === columns.length - 1}
                    />
                  </Tooltip>
                </Space>
              </Col>
              
              <Col flex="auto">
                <Space direction="vertical" size={0}>
                  <Space>
                    <Text strong>{column.title}</Text>
                    {column.printVisible !== false && (
                      <Tag color="green">
                        <PrinterOutlined /> طباعة
                      </Tag>
                    )}
                    {column.printFormat?.bold && <Tag>B</Tag>}
                    {column.printFormat?.italic && <Tag>I</Tag>}
                    {column.printFormat?.underline && <Tag>U</Tag>}
                  </Space>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {column.key} • {column.type || 'text'}
                    {column.width && ` • عرض: ${column.width}`}
                    {column.printWidth && column.printWidth !== column.width && ` • طباعة: ${column.printWidth}`}
                  </Text>
                </Space>
              </Col>
              
              <Col flex="none">
                <Space>
                  <Tooltip title={column.visible ? 'إخفاء من العرض' : 'إظهار في العرض'}>
                    <Button
                      size="small"
                      type={column.visible ? 'default' : 'dashed'}
                      icon={column.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                      onClick={() => handleToggleVisibility(column.key)}
                    />
                  </Tooltip>
                  
                  <Tooltip title={column.printVisible !== false ? 'إخفاء من الطباعة' : 'إظهار في الطباعة'}>
                    <Button
                      size="small"
                      type={column.printVisible !== false ? 'default' : 'dashed'}
                      icon={<PrinterOutlined />}
                      onClick={() => handleTogglePrintVisibility(column.key)}
                      style={{ 
                        color: column.printVisible !== false ? '#52c41a' : '#ff4d4f',
                        borderColor: column.printVisible !== false ? '#52c41a' : '#ff4d4f'
                      }}
                    />
                  </Tooltip>
                  
                  <Tooltip title="تعديل العمود">
                    <Button
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => startEditColumn(column)}
                    />
                  </Tooltip>
                  
                  <Tooltip title="نسخ العمود">
                    <Button
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => handleCopyColumn(column)}
                    />
                  </Tooltip>
                  
                  <Tooltip title="حذف العمود">
                    <Popconfirm
                      title="هل أنت متأكد من حذف هذا العمود؟"
                      onConfirm={() => handleDeleteColumn(column.key)}
                      okText="نعم"
                      cancelText="لا"
                    >
                      <Button
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                      />
                    </Popconfirm>
                  </Tooltip>
                </Space>
              </Col>
            </Row>
          </Card>
        ))}
      </div>

      {/* نموذج إضافة عمود جديد */}
      {showAddForm && (
        <Card
          title="إضافة عمود جديد"
          size="small"
          style={{ marginTop: 16 }}
          extra={
            <Button
              size="small"
              icon={<CloseOutlined />}
              onClick={() => {
                setShowAddForm(false);
                addForm.resetFields();
              }}
            />
          }
        >
          <Form
            form={addForm}
            layout="vertical"
            onFinish={handleAddColumn}
            initialValues={{
              type: 'text',
              align: 'right',
              printAlign: 'right',
              visible: true,
              printVisible: true,
              sortable: false,
              filterable: false,
              bold: false,
              italic: false,
              underline: false,
              color: globalSettings?.colors?.textColor || '#000000',
              backgroundColor: 'transparent',
              decimals: 2,
              thousandsSeparator: true,
              currency: 'ر.س',
              dateFormat: 'YYYY-MM-DD'
            }}
          >
            <Row gutter={[16, 0]}>
              <Col xs={24} md={8}>
                <Form.Item
                  label="مفتاح العمود"
                  name="key"
                  rules={[{ required: true, message: 'مطلوب' }]}
                >
                  <Input placeholder="column_key" />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item
                  label="عنوان العمود"
                  name="title"
                  rules={[{ required: true, message: 'مطلوب' }]}
                >
                  <Input placeholder="عنوان العمود" />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="نوع البيانات" name="type">
                  <Select>
                    <Option value="text">نص</Option>
                    <Option value="number">رقم</Option>
                    <Option value="currency">عملة</Option>
                    <Option value="date">تاريخ</Option>
                    <Option value="boolean">منطقي</Option>
                    <Option value="tag">علامة</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 0]}>
              <Col xs={24} md={6}>
                <Form.Item label="عرض العرض" name="width">
                  <Input placeholder="auto أو 120px" />
                </Form.Item>
              </Col>
              <Col xs={24} md={6}>
                <Form.Item label="عرض الطباعة" name="printWidth">
                  <Input placeholder="auto أو 120px" />
                </Form.Item>
              </Col>
              <Col xs={24} md={6}>
                <Form.Item label="محاذاة العرض" name="align">
                  <Select>
                    <Option value="left">يسار</Option>
                    <Option value="center">وسط</Option>
                    <Option value="right">يمين</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={6}>
                <Form.Item label="محاذاة الطباعة" name="printAlign">
                  <Select>
                    <Option value="left">يسار</Option>
                    <Option value="center">وسط</Option>
                    <Option value="right">يمين</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 0]}>
              <Col xs={12} md={6}>
                <Form.Item name="visible" valuePropName="checked">
                  <Checkbox>مرئي في العرض</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={12} md={6}>
                <Form.Item name="printVisible" valuePropName="checked">
                  <Checkbox>مرئي في الطباعة</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={12} md={6}>
                <Form.Item name="sortable" valuePropName="checked">
                  <Checkbox>قابل للترتيب</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={12} md={6}>
                <Form.Item name="filterable" valuePropName="checked">
                  <Checkbox>قابل للتصفية</Checkbox>
                </Form.Item>
              </Col>
            </Row>

            <Divider>تنسيق الطباعة</Divider>

            <Row gutter={[16, 0]}>
              <Col xs={8} md={4}>
                <Form.Item name="bold" valuePropName="checked">
                  <Checkbox>عريض</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={8} md={4}>
                <Form.Item name="italic" valuePropName="checked">
                  <Checkbox>مائل</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={8} md={4}>
                <Form.Item name="underline" valuePropName="checked">
                  <Checkbox>تحته خط</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={12} md={6}>
                <Form.Item label="لون النص" name="color">
                  <Input type="color" style={{ width: '100%', height: 32 }} />
                </Form.Item>
              </Col>
              <Col xs={12} md={6}>
                <Form.Item label="لون الخلفية" name="backgroundColor">
                  <Input type="color" style={{ width: '100%', height: 32 }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}>
              {({ getFieldValue }) => {
                const type = getFieldValue('type');

                if (type === 'number' || type === 'currency') {
                  return (
                    <>
                      <Divider>تنسيق الأرقام</Divider>
                      <Row gutter={[16, 0]}>
                        <Col xs={24} md={8}>
                          <Form.Item label="عدد الخانات العشرية" name="decimals">
                            <InputNumber min={0} max={6} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col xs={24} md={8}>
                          <Form.Item name="thousandsSeparator" valuePropName="checked">
                            <Checkbox>فاصل الآلاف</Checkbox>
                          </Form.Item>
                        </Col>
                        {type === 'currency' && (
                          <Col xs={24} md={8}>
                            <Form.Item label="رمز العملة" name="currency">
                              <Input placeholder="ر.س" />
                            </Form.Item>
                          </Col>
                        )}
                      </Row>
                    </>
                  );
                }

                if (type === 'date') {
                  return (
                    <>
                      <Divider>تنسيق التاريخ</Divider>
                      <Row gutter={[16, 0]}>
                        <Col xs={24} md={12}>
                          <Form.Item label="تنسيق التاريخ" name="dateFormat">
                            <Select>
                              <Option value="YYYY-MM-DD">2024-01-15</Option>
                              <Option value="DD/MM/YYYY">15/01/2024</Option>
                              <Option value="DD-MM-YYYY">15-01-2024</Option>
                              <Option value="YYYY/MM/DD">2024/01/15</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  );
                }

                return null;
              }}
            </Form.Item>

            <Row justify="end" style={{ marginTop: 16 }}>
              <Space>
                <Button onClick={() => {
                  setShowAddForm(false);
                  addForm.resetFields();
                }}>
                  إلغاء
                </Button>
                <Button type="primary" htmlType="submit" icon={<PlusOutlined />}>
                  إضافة العمود
                </Button>
              </Space>
            </Row>
          </Form>
        </Card>
      )}

      {/* نموذج تعديل العمود */}
      {editingColumn && (
        <Card
          title={`تعديل العمود: ${editingColumn.title}`}
          size="small"
          style={{ marginTop: 16 }}
          extra={
            <Space>
              <Button size="small" onClick={cancelEdit}>إلغاء</Button>
              <Button
                size="small"
                type="primary"
                icon={<CheckOutlined />}
                onClick={() => editForm.submit()}
              >
                حفظ
              </Button>
            </Space>
          }
        >
          <Form
            form={editForm}
            layout="vertical"
            onFinish={handleUpdateColumn}
          >
            <Row gutter={[16, 0]}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="عنوان العمود"
                  name="title"
                  rules={[{ required: true, message: 'مطلوب' }]}
                >
                  <Input placeholder="عنوان العمود" />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="نوع البيانات" name="type">
                  <Select>
                    <Option value="text">نص</Option>
                    <Option value="number">رقم</Option>
                    <Option value="currency">عملة</Option>
                    <Option value="date">تاريخ</Option>
                    <Option value="boolean">منطقي</Option>
                    <Option value="tag">علامة</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 0]}>
              <Col xs={24} md={6}>
                <Form.Item label="عرض العرض" name="width">
                  <Input placeholder="auto أو 120px" />
                </Form.Item>
              </Col>
              <Col xs={24} md={6}>
                <Form.Item label="عرض الطباعة" name="printWidth">
                  <Input placeholder="auto أو 120px" />
                </Form.Item>
              </Col>
              <Col xs={24} md={6}>
                <Form.Item label="محاذاة العرض" name="align">
                  <Select>
                    <Option value="left">يسار</Option>
                    <Option value="center">وسط</Option>
                    <Option value="right">يمين</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={6}>
                <Form.Item label="محاذاة الطباعة" name="printAlign">
                  <Select>
                    <Option value="left">يسار</Option>
                    <Option value="center">وسط</Option>
                    <Option value="right">يمين</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 0]}>
              <Col xs={12} md={6}>
                <Form.Item name="visible" valuePropName="checked">
                  <Checkbox>مرئي في العرض</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={12} md={6}>
                <Form.Item name="printVisible" valuePropName="checked">
                  <Checkbox>مرئي في الطباعة</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={12} md={6}>
                <Form.Item name="sortable" valuePropName="checked">
                  <Checkbox>قابل للترتيب</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={12} md={6}>
                <Form.Item name="filterable" valuePropName="checked">
                  <Checkbox>قابل للتصفية</Checkbox>
                </Form.Item>
              </Col>
            </Row>

            <Divider>تنسيق الطباعة</Divider>

            <Row gutter={[16, 0]}>
              <Col xs={8} md={4}>
                <Form.Item name="bold" valuePropName="checked">
                  <Checkbox>عريض</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={8} md={4}>
                <Form.Item name="italic" valuePropName="checked">
                  <Checkbox>مائل</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={8} md={4}>
                <Form.Item name="underline" valuePropName="checked">
                  <Checkbox>تحته خط</Checkbox>
                </Form.Item>
              </Col>
              <Col xs={12} md={6}>
                <Form.Item label="لون النص" name="color">
                  <Input type="color" style={{ width: '100%', height: 32 }} />
                </Form.Item>
              </Col>
              <Col xs={12} md={6}>
                <Form.Item label="لون الخلفية" name="backgroundColor">
                  <Input type="color" style={{ width: '100%', height: 32 }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}>
              {({ getFieldValue }) => {
                const type = getFieldValue('type');

                if (type === 'number' || type === 'currency') {
                  return (
                    <>
                      <Divider>تنسيق الأرقام</Divider>
                      <Row gutter={[16, 0]}>
                        <Col xs={24} md={8}>
                          <Form.Item label="عدد الخانات العشرية" name="decimals">
                            <InputNumber min={0} max={6} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col xs={24} md={8}>
                          <Form.Item name="thousandsSeparator" valuePropName="checked">
                            <Checkbox>فاصل الآلاف</Checkbox>
                          </Form.Item>
                        </Col>
                        {type === 'currency' && (
                          <Col xs={24} md={8}>
                            <Form.Item label="رمز العملة" name="currency">
                              <Input placeholder="ر.س" />
                            </Form.Item>
                          </Col>
                        )}
                      </Row>
                    </>
                  );
                }

                if (type === 'date') {
                  return (
                    <>
                      <Divider>تنسيق التاريخ</Divider>
                      <Row gutter={[16, 0]}>
                        <Col xs={24} md={12}>
                          <Form.Item label="تنسيق التاريخ" name="dateFormat">
                            <Select>
                              <Option value="YYYY-MM-DD">2024-01-15</Option>
                              <Option value="DD/MM/YYYY">15/01/2024</Option>
                              <Option value="DD-MM-YYYY">15-01-2024</Option>
                              <Option value="YYYY/MM/DD">2024/01/15</Option>
                            </Select>
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  );
                }

                return null;
              }}
            </Form.Item>
          </Form>
        </Card>
      )}
    </div>
  );
};

export default IntegratedColumnEditor;
