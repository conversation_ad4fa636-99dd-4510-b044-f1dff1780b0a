import { DatabaseService } from './DatabaseService'
import { ApiResponse } from './AuthService'
import { Logger } from '../utils/logger'

export interface Warehouse {
  id: number
  name: string
  location?: string
  manager_id?: number
  manager_name?: string
  is_active: boolean
  created_at: string
}

export interface Category {
  id: number
  code?: string
  name: string
  description?: string
  parent_id?: number
  level: number
  path: string
  is_active: boolean
  created_at: string
}

export interface Item {
  id: number
  code: string
  name: string
  description?: string
  category_id?: number
  category_name?: string
  category_path?: string
  warehouse_id?: number
  warehouse_name?: string
  unit: string
  cost_price: number
  sale_price: number
  min_quantity: number
  max_quantity: number
  is_active?: boolean
  available_quantity?: number
  created_at: string
}

export interface Inventory {
  id: number
  item_id: number
  item_code: string
  item_name: string
  warehouse_id: number
  warehouse_name: string
  category_name?: string
  unit: string
  quantity: number
  reserved_quantity: number
  cost_price: number
  sale_price: number
  min_quantity: number
  max_quantity: number
  location?: string
  notes?: string
  last_updated: string
}

export interface InventoryMovement {
  id: number
  movement_number: string
  item_id: number
  item_code: string
  item_name: string
  warehouse_id: number
  warehouse_name: string
  movement_type: 'in' | 'out' | 'transfer' | 'adjustment'
  quantity: number
  reference_type?: string
  reference_id?: number
  notes?: string
  created_by?: number
  created_by_name?: string
  created_at: string
}

export class InventoryService {
  private static instance: InventoryService
  private databaseService: DatabaseService

  private constructor() {
    this.databaseService = DatabaseService.getInstance()
  }

  private get db() {
    return this.databaseService.getDatabase()
  }

  public static getInstance(): InventoryService {
    if (!InventoryService.instance) {
      InventoryService.instance = new InventoryService()
    }
    return InventoryService.instance
  }

  // إنشاء جداول المخزون
  public async createInventoryTables(): Promise<void> {
    const database = this.db

    // جدول المخازن
    database.exec(`
      CREATE TABLE IF NOT EXISTS warehouses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        location TEXT,
        manager_id INTEGER,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (manager_id) REFERENCES users (id)
      )
    `)

    // جدول الفئات
    database.exec(`
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        parent_id INTEGER,
        level INTEGER DEFAULT 1,
        path TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES categories (id)
      )
    `)

    // إضافة عمود code إذا لم يكن موجوداً (للتوافق مع قواعد البيانات الموجودة)
    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('categories', 'code TEXT UNIQUE')
    } catch {
      // العمود موجود بالفعل أو خطأ آخر - تجاهل
    }

    // تحديث الفئات الموجودة التي لها مستوى 0 إلى مستوى 1
    try {
      database.exec(`UPDATE categories SET level = 1 WHERE level = 0 AND parent_id IS NULL`)
    } catch {
      // تجاهل الأخطاء
    }

    // جدول الأصناف
    database.exec(`
      CREATE TABLE IF NOT EXISTS items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        category_id INTEGER,
        warehouse_id INTEGER,
        type TEXT DEFAULT 'raw_material' CHECK (type IN ('raw_material', 'finished_product', 'component', 'tool', 'consumable')),
        unit TEXT NOT NULL DEFAULT 'قطعة',
        cost_price DECIMAL(10,2) DEFAULT 0,
        sale_price DECIMAL(10,2) DEFAULT 0,
        min_quantity DECIMAL(10,2) DEFAULT 0,
        max_quantity DECIMAL(10,2) DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories (id),
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
      )
    `)

    // إضافة حقل type للجداول الموجودة إذا لم يكن موجوداً
    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('items', "type TEXT DEFAULT 'raw_material' CHECK (type IN ('raw_material', 'finished_product', 'component', 'tool', 'consumable'))")
    } catch {
      // الحقل موجود بالفعل أو خطأ آخر - تجاهل
    }

    // جدول المخزون
    database.exec(`
      CREATE TABLE IF NOT EXISTS inventory (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        item_id INTEGER NOT NULL,
        warehouse_id INTEGER NOT NULL,
        quantity DECIMAL(10,2) DEFAULT 0,
        reserved_quantity DECIMAL(10,2) DEFAULT 0,
        cost_price DECIMAL(10,2) DEFAULT 0,
        location TEXT,
        notes TEXT,
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (item_id) REFERENCES items (id) ON DELETE CASCADE,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id),
        UNIQUE(item_id, warehouse_id)
      )
    `)

    // جدول حركات المخزون
    database.exec(`
      CREATE TABLE IF NOT EXISTS inventory_movements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        movement_number TEXT UNIQUE NOT NULL,
        item_id INTEGER NOT NULL,
        warehouse_id INTEGER NOT NULL,
        movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'transfer', 'adjustment')),
        quantity DECIMAL(10,2) NOT NULL,
        reference_type TEXT,
        reference_id INTEGER,
        notes TEXT,
        created_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (item_id) REFERENCES items (id),
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id),
        FOREIGN KEY (created_by) REFERENCES users (id)
      )
    `)

    // إضافة عمود movement_number إذا لم يكن موجوداً (للقواعد القديمة)
    try {
      const dbService = DatabaseService.getInstance()
      dbService.safeAddColumn('inventory_movements', 'movement_number TEXT UNIQUE')
    } catch {
      // العمود موجود بالفعل أو خطأ آخر
    }

    // جدول الباركود
    database.exec(`
      CREATE TABLE IF NOT EXISTS item_barcodes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        item_id INTEGER NOT NULL,
        barcode TEXT UNIQUE NOT NULL,
        is_primary BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (item_id) REFERENCES items (id) ON DELETE CASCADE
      )
    `)

    // إنشاء الفهارس
    await this.createInventoryIndexes()

    // ملاحظة: تم إزالة جدول صور الأصناف القديم (item_images)
    // يتم الآن استخدام جدول unified_images من النظام الجديد

    // إنشاء المخازن الافتراضية الأساسية
    await this.createDefaultWarehouses()

    // تحديث أسعار التكلفة في المخزون للبيانات الموجودة
    this.updateInventoryCostPrices()
  }

  private async createInventoryIndexes(): Promise<void> {
    const database = this.db

    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_warehouses_active ON warehouses(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_categories_parent ON categories(parent_id)',
      'CREATE INDEX IF NOT EXISTS idx_categories_active ON categories(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_items_code ON items(code)',
      'CREATE INDEX IF NOT EXISTS idx_items_category ON items(category_id)',
      'CREATE INDEX IF NOT EXISTS idx_items_warehouse ON items(warehouse_id)',
      'CREATE INDEX IF NOT EXISTS idx_items_active ON items(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_inventory_item ON inventory(item_id)',
      'CREATE INDEX IF NOT EXISTS idx_inventory_warehouse ON inventory(warehouse_id)',
      'CREATE INDEX IF NOT EXISTS idx_inventory_item_warehouse ON inventory(item_id, warehouse_id)',
      'CREATE INDEX IF NOT EXISTS idx_movements_item ON inventory_movements(item_id)',
      'CREATE INDEX IF NOT EXISTS idx_movements_warehouse ON inventory_movements(warehouse_id)',
      'CREATE INDEX IF NOT EXISTS idx_movements_type ON inventory_movements(movement_type)',
      'CREATE INDEX IF NOT EXISTS idx_movements_date ON inventory_movements(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_barcodes_item ON item_barcodes(item_id)',
      'CREATE INDEX IF NOT EXISTS idx_barcodes_code ON item_barcodes(barcode)'
    ]

    indexes.forEach(indexSql => {
      try {
        database.exec(indexSql)
      } catch (error) {
        Logger.warn('InventoryService', 'تحذير: فشل في إنشاء فهرس المخزون:', error)
      }
    })

    // تشغيل Migration التلقائي
    await this.runInventoryMigrations()
  }

  // تشغيل Migration التلقائي للمخزون
  private async runInventoryMigrations(): Promise<void> {
    try {
      Logger.info('InventoryService', '🔄 بدء Migration التلقائي للمخزون...')

      // تحديث مخطط جداول المخزون
      await this.updateInventoryTableSchema()

      // إصلاح البيانات الموجودة
      await this.fixExistingInventoryData()

      Logger.info('InventoryService', '✅ تم إكمال Migration المخزون بنجاح')
    } catch (error) {
      Logger.error('InventoryService', '❌ خطأ في Migration المخزون:', error)
    }
  }

  // تحديث مخطط جداول المخزون
  private async updateInventoryTableSchema(): Promise<void> {
    try {
      Logger.info('InventoryService', '🔧 فحص وتحديث مخطط جداول المخزون...')

      // تحديث جدول المخازن
      await this.updateWarehousesTable()

      // تحديث جدول الفئات
      await this.updateCategoriesTable()

      // تحديث جدول الأصناف
      await this.updateItemsTable()

    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تحديث مخطط جداول المخزون:', error)
    }
  }

  // تحديث جدول المخازن
  private async updateWarehousesTable(): Promise<void> {
    try {
      const tableInfo = this.db.prepare("PRAGMA table_info(warehouses)").all()
      const existingColumns = tableInfo.map((col: any) => col.name)

      const newColumns = [
        { name: 'code', type: 'TEXT UNIQUE' },
        { name: 'capacity', type: 'DECIMAL(10,2)' },
        { name: 'current_usage', type: 'DECIMAL(10,2) DEFAULT 0' },
        { name: 'warehouse_type', type: 'TEXT DEFAULT "general"' },
        { name: 'notes', type: 'TEXT' },
        { name: 'created_by', type: 'INTEGER' }
      ]

      for (const column of newColumns) {
        if (!existingColumns.includes(column.name)) {
          try {
            this.db.exec(`ALTER TABLE warehouses ADD COLUMN ${column.name} ${column.type}`)
            Logger.info('InventoryService', '✅ تم إضافة العمود للمخازن: ${column.name}')
          } catch (error) {
            Logger.info('InventoryService', '⚠️ فشل في إضافة العمود ${column.name} للمخازن:', error)
          }
        }
      }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تحديث جدول المخازن:', error)
    }
  }

  // تحديث جدول الفئات
  private async updateCategoriesTable(): Promise<void> {
    try {
      const tableInfo = this.db.prepare("PRAGMA table_info(categories)").all()
      const existingColumns = tableInfo.map((col: any) => col.name)

      const newColumns = [
        { name: 'image_url', type: 'TEXT' },
        { name: 'sort_order', type: 'INTEGER DEFAULT 0' },
        { name: 'meta_title', type: 'TEXT' },
        { name: 'meta_description', type: 'TEXT' },
        { name: 'created_by', type: 'INTEGER' }
      ]

      for (const column of newColumns) {
        if (!existingColumns.includes(column.name)) {
          try {
            this.db.exec(`ALTER TABLE categories ADD COLUMN ${column.name} ${column.type}`)
            Logger.info('InventoryService', '✅ تم إضافة العمود للفئات: ${column.name}')
          } catch (error) {
            Logger.info('InventoryService', '⚠️ فشل في إضافة العمود ${column.name} للفئات:', error)
          }
        }
      }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تحديث جدول الفئات:', error)
    }
  }

  // تحديث جدول الأصناف
  private async updateItemsTable(): Promise<void> {
    try {
      const tableInfo = this.db.prepare("PRAGMA table_info(items)").all()
      const existingColumns = tableInfo.map((col: any) => col.name)

      const newColumns = [
        { name: 'brand', type: 'TEXT' },
        { name: 'model', type: 'TEXT' },
        { name: 'weight', type: 'DECIMAL(10,3)' },
        { name: 'dimensions', type: 'TEXT' },
        { name: 'color', type: 'TEXT' },
        { name: 'material', type: 'TEXT' },
        { name: 'warranty_period', type: 'INTEGER' },
        { name: 'supplier_id', type: 'INTEGER' },
        { name: 'last_purchase_price', type: 'DECIMAL(10,2)' },
        { name: 'last_purchase_date', type: 'DATETIME' },
        { name: 'reorder_level', type: 'INTEGER DEFAULT 0' },
        { name: 'max_stock_level', type: 'INTEGER DEFAULT 0' },
        { name: 'location_in_warehouse', type: 'TEXT' },
        { name: 'expiry_date', type: 'DATE' },
        { name: 'batch_number', type: 'TEXT' },
        { name: 'created_by', type: 'INTEGER' }
      ]

      for (const column of newColumns) {
        if (!existingColumns.includes(column.name)) {
          try {
            this.db.exec(`ALTER TABLE items ADD COLUMN ${column.name} ${column.type}`)
            Logger.info('InventoryService', '✅ تم إضافة العمود للأصناف: ${column.name}')
          } catch (error) {
            Logger.info('InventoryService', '⚠️ فشل في إضافة العمود ${column.name} للأصناف:', error)
          }
        }
      }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تحديث جدول الأصناف:', error)
    }
  }

  // إنشاء المخازن الافتراضية الأساسية
  private async createDefaultWarehouses(): Promise<void> {
    try {
      Logger.info('InventoryService', '🏭 إنشاء المخازن الافتراضية الأساسية...')

      const defaultWarehouses = [
        {
          code: 'MAIN',
          name: 'المخزن الرئيسي',
          location: 'المبنى الرئيسي',
          warehouse_type: 'main',
          is_protected: true, // لا يمكن حذفه
          notes: 'المخزن الرئيسي للشركة - لا يمكن حذفه'
        },
        {
          code: 'WOOD',
          name: 'مخزن الأخشاب',
          location: 'قسم المواد الخام',
          warehouse_type: 'raw_materials',
          is_protected: true,
          notes: 'مخزن الأخشاب والمواد الخشبية - لا يمكن حذفه'
        },
        {
          code: 'PAINT',
          name: 'مخزن الدهانات',
          location: 'قسم المواد الكيميائية',
          warehouse_type: 'chemicals',
          is_protected: true,
          notes: 'مخزن الدهانات والمواد الكيميائية - لا يمكن حذفه'
        },
        {
          code: 'ACCESS',
          name: 'مخزن الاكسسوارات',
          location: 'قسم القطع الصغيرة',
          warehouse_type: 'accessories',
          is_protected: true,
          notes: 'مخزن الاكسسوارات والقطع الصغيرة - لا يمكن حذفه'
        },
        {
          code: 'FINISHED',
          name: 'مخزن المنتج التام',
          location: 'قسم المنتجات النهائية',
          warehouse_type: 'finished_goods',
          is_protected: true,
          notes: 'مخزن المنتجات التامة والجاهزة للبيع - لا يمكن حذفه'
        }
      ]

      // إضافة عمود is_protected إذا لم يكن موجوداً
      try {
        const dbService = DatabaseService.getInstance()
        dbService.safeAddColumn('warehouses', 'is_protected BOOLEAN DEFAULT 0')
      } catch (error) {
        // العمود موجود بالفعل
      }

      for (const warehouse of defaultWarehouses) {
        // التحقق من وجود المخزن
        const existing = this.db.prepare('SELECT id FROM warehouses WHERE code = ?').get(warehouse.code)

        if (!existing) {
          try {
            const result = this.db.prepare(`
              INSERT INTO warehouses (
                code, name, location, warehouse_type, is_protected, notes, is_active, created_at
              ) VALUES (?, ?, ?, ?, ?, ?, 1, datetime('now'))
            `).run(
              warehouse.code,
              warehouse.name,
              warehouse.location,
              warehouse.warehouse_type,
              warehouse.is_protected ? 1 : 0,
              warehouse.notes
            )

            if (result.changes > 0) {
              Logger.info('InventoryService', `✅ تم إنشاء المخزن الافتراضي: ${warehouse.name}`)
            }
          } catch (error) {
            Logger.warn('InventoryService', `⚠️ فشل في إنشاء المخزن ${warehouse.name}:`, error)
          }
        } else {
          // تحديث المخزن الموجود ليكون محمياً
          try {
            this.db.prepare(`
              UPDATE warehouses
              SET is_protected = 1, warehouse_type = ?, notes = ?
              WHERE code = ?
            `).run(warehouse.warehouse_type, warehouse.notes, warehouse.code)
          } catch (error) {
            Logger.warn('InventoryService', `⚠️ فشل في تحديث المخزن ${warehouse.name}:`, error)
          }
        }
      }

      // حفظ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()
      Logger.info('InventoryService', '✅ تم إنشاء/تحديث المخازن الافتراضية بنجاح')

    } catch (error) {
      Logger.error('InventoryService', '❌ خطأ في إنشاء المخازن الافتراضية:', error)
    }
  }

  // إصلاح البيانات الموجودة للمخزون
  private async fixExistingInventoryData(): Promise<void> {
    try {
      Logger.info('InventoryService', '🔧 بدء إصلاح البيانات الموجودة للمخزون...')

      // 1. تحديث is_active للمخازن الذين لا يحتوون على قيمة
      const updateWarehousesActiveResult = this.db.prepare(`
        UPDATE warehouses
        SET is_active = 1
        WHERE is_active IS NULL OR is_active = 0
      `).run()

      if (updateWarehousesActiveResult.changes > 0) {
        Logger.info('InventoryService', '✅ تم تحديث حالة النشاط لـ ${updateWarehousesActiveResult.changes} مخزن')
      }

      // 2. تحديث is_active للفئات الذين لا يحتوون على قيمة
      const updateCategoriesActiveResult = this.db.prepare(`
        UPDATE categories
        SET is_active = 1
        WHERE is_active IS NULL OR is_active = 0
      `).run()

      if (updateCategoriesActiveResult.changes > 0) {
        Logger.info('InventoryService', '✅ تم تحديث حالة النشاط لـ ${updateCategoriesActiveResult.changes} فئة')
      }

      // 3. تحديث is_active للأصناف الذين لا يحتوون على قيمة
      const updateItemsActiveResult = this.db.prepare(`
        UPDATE items
        SET is_active = 1
        WHERE is_active IS NULL OR is_active = 0
      `).run()

      if (updateItemsActiveResult.changes > 0) {
        Logger.info('InventoryService', '✅ تم تحديث حالة النشاط لـ ${updateItemsActiveResult.changes} صنف')
      }

      // 4. تحديث warehouse_type للمخازن الذين لا يحتوون على قيمة
      const updateWarehouseTypeResult = this.db.prepare(`
        UPDATE warehouses
        SET warehouse_type = 'general'
        WHERE warehouse_type IS NULL OR warehouse_type = ''
      `).run()

      if (updateWarehouseTypeResult.changes > 0) {
        Logger.info('InventoryService', '✅ تم تحديث نوع المخزن لـ ${updateWarehouseTypeResult.changes} مخزن')
      }

      // 5. تحديث reorder_level للأصناف الذين لا يحتوون على قيمة
      const updateReorderLevelResult = this.db.prepare(`
        UPDATE items
        SET reorder_level = min_quantity
        WHERE reorder_level IS NULL AND min_quantity IS NOT NULL
      `).run()

      if (updateReorderLevelResult.changes > 0) {
        Logger.info('InventoryService', '✅ تم تحديث مستوى إعادة الطلب لـ ${updateReorderLevelResult.changes} صنف')
      }

      // 6. تحديث max_stock_level للأصناف الذين لا يحتوون على قيمة
      const updateMaxStockResult = this.db.prepare(`
        UPDATE items
        SET max_stock_level = max_quantity
        WHERE max_stock_level IS NULL AND max_quantity IS NOT NULL
      `).run()

      if (updateMaxStockResult.changes > 0) {
        Logger.info('InventoryService', '✅ تم تحديث الحد الأقصى للمخزون لـ ${updateMaxStockResult.changes} صنف')
      }

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()
      Logger.info('InventoryService', '✅ تم إصلاح البيانات الموجودة بنجاح')

    } catch (error) {
      Logger.error('InventoryService', 'خطأ في إصلاح البيانات الموجودة:', error)
    }
  }

  // ===== إدارة المخازن =====

  public async getWarehouses(): Promise<Warehouse[]> {
    try {
      const warehouses = this.db.prepare(`
        SELECT w.*, u.full_name as manager_name
        FROM warehouses w
        LEFT JOIN users u ON w.manager_id = u.id
        WHERE w.is_active = 1
        ORDER BY w.name
      `).all([]) as Warehouse[]

      return warehouses
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب المخازن:', error)
      return []
    }
  }

  public async getWarehouse(warehouseId: number): Promise<Warehouse | null> {
    try {
      // التحقق من صحة معرف المخزن
      if (!warehouseId || typeof warehouseId !== 'number' || warehouseId <= 0) {
        Logger.error('InventoryService', 'معرف المخزن غير صحيح:', warehouseId)
        return null
      }

      const warehouse = this.db.prepare(`
        SELECT w.*, u.full_name as manager_name,
               COUNT(DISTINCT i.id) as items_count,
               COALESCE(SUM(inv.quantity), 0) as total_quantity,
               COALESCE(SUM(inv.quantity * COALESCE(inv.cost_price, i.cost_price, 0)), 0) as total_value
        FROM warehouses w
        LEFT JOIN users u ON w.manager_id = u.id
        LEFT JOIN inventory inv ON w.id = inv.warehouse_id
        LEFT JOIN items i ON inv.item_id = i.id AND i.is_active = 1
        WHERE w.id = ? AND w.is_active = 1
        GROUP BY w.id
      `).get([warehouseId]) as Warehouse | undefined

      if (!warehouse) {
        Logger.warn('InventoryService', `المخزن غير موجود: ${warehouseId}`)
        return null
      }

      Logger.info('InventoryService', `✅ تم جلب المخزن ${warehouse.name} بنجاح`)
      return warehouse
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب المخزن:', error)
      return null
    }
  }

  public async createWarehouse(warehouseData: any): Promise<ApiResponse> {
    try {
      // التحقق من صحة البيانات الأساسية
      if (!warehouseData.name || warehouseData.name.trim() === '') {
        return { success: false, message: 'اسم المخزن مطلوب' }
      }

      // توليد كود تلقائي إذا لم يتم توفيره
      if (!warehouseData.code || warehouseData.code.trim() === '') {
        warehouseData.code = await this.generateWarehouseCode()
      }

      // التحقق من عدم تكرار الكود
      const existingWarehouse = this.db.prepare('SELECT id FROM warehouses WHERE code = ?').get(warehouseData.code.trim())
      if (existingWarehouse) {
        return { success: false, message: 'كود المخزن موجود بالفعل' }
      }

      const result = this.db.prepare(`
        INSERT INTO warehouses (code, name, location, manager_id, is_active)
        VALUES (?, ?, ?, ?, ?)
      `).run([
        warehouseData.code.trim(),
        warehouseData.name.trim(),
        warehouseData.location?.trim() || null,
        warehouseData.manager_id || null,
        warehouseData.is_active ? 1 : 0
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return {
          success: true,
          message: 'تم إنشاء المخزن بنجاح',
          data: { id: result.lastInsertRowid, code: warehouseData.code }
        }
      } else {
        return { success: false, message: 'فشل في إنشاء المخزن' }
      }
    } catch (error: any) {
      Logger.error('InventoryService', 'خطأ في إنشاء المخزن:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود المخزن موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في إنشاء المخزن' }
    }
  }

  // تحديث مخزن
  public async updateWarehouse(warehouseId: number, warehouseData: any): Promise<ApiResponse> {
    try {
      // التحقق من وجود المخزن
      const existingWarehouse = this.db.prepare('SELECT * FROM warehouses WHERE id = ?').get([warehouseId])
      if (!existingWarehouse) {
        return { success: false, message: 'المخزن غير موجود' }
      }

      // التحقق من صحة البيانات الأساسية
      if (!warehouseData.name || warehouseData.name.trim() === '') {
        return { success: false, message: 'اسم المخزن مطلوب' }
      }

      // التحقق من عدم تكرار الكود إذا تم تغييره
      if (warehouseData.code && warehouseData.code.trim() !== '') {
        const codeExists = this.db.prepare('SELECT id FROM warehouses WHERE code = ? AND id != ?').get([warehouseData.code.trim(), warehouseId])
        if (codeExists) {
          return { success: false, message: 'كود المخزن موجود بالفعل' }
        }
      }

      // التحقق من وجود المدير إذا تم تحديده
      if (warehouseData.manager_id) {
        const managerExists = this.db.prepare('SELECT id FROM users WHERE id = ? AND is_active = 1').get([warehouseData.manager_id])
        if (!managerExists) {
          return { success: false, message: 'المدير المحدد غير موجود أو غير نشط' }
        }
      }

      // تحديث المخزن
      const result = this.db.prepare(`
        UPDATE warehouses SET
          code = ?,
          name = ?,
          location = ?,
          manager_id = ?,
          capacity = ?,
          warehouse_type = ?,
          notes = ?,
          is_active = ?,
          updated_at = datetime('now')
        WHERE id = ?
      `).run([
        warehouseData.code?.trim() || (existingWarehouse as any).code,
        warehouseData.name.trim(),
        warehouseData.location?.trim() || (existingWarehouse as any).location,
        warehouseData.manager_id || (existingWarehouse as any).manager_id,
        warehouseData.capacity || (existingWarehouse as any).capacity,
        warehouseData.warehouse_type || (existingWarehouse as any).warehouse_type || 'general',
        warehouseData.notes?.trim() || (existingWarehouse as any).notes,
        warehouseData.is_active !== undefined ? (warehouseData.is_active ? 1 : 0) : (existingWarehouse as any).is_active,
        warehouseId
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث المخزن بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث المخزن' }
      }
    } catch (error: any) {
      Logger.error('InventoryService', 'خطأ في تحديث المخزن:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود المخزن موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في تحديث المخزن' }
    }
  }

  // حذف مخزن (حذف ناعم)
  public async deleteWarehouse(warehouseId: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود المخزن
      const existingWarehouse = this.db.prepare('SELECT * FROM warehouses WHERE id = ?').get([warehouseId]) as any
      if (!existingWarehouse) {
        return { success: false, message: 'المخزن غير موجود' }
      }

      // التحقق من أن المخزن ليس محمياً
      if (existingWarehouse.is_protected) {
        return {
          success: false,
          message: 'لا يمكن حذف هذا المخزن لأنه من المخازن الأساسية المحمية. يمكنك تعديله فقط.'
        }
      }

      // التحقق من وجود مخزون في المخزن
      const inventoryCount = this.db.prepare('SELECT COUNT(*) as count FROM inventory WHERE warehouse_id = ? AND quantity > 0').get([warehouseId]) as any
      if (inventoryCount && inventoryCount.count > 0) {
        // حذف ناعم - تعطيل المخزن بدلاً من حذفه
        const result = this.db.prepare(`
          UPDATE warehouses SET is_active = 0, updated_at = datetime('now')
          WHERE id = ?
        `).run([warehouseId])

        if (result.changes > 0) {
          DatabaseService.getInstance().saveDatabase()
          return { success: true, message: 'تم إلغاء تفعيل المخزن بنجاح (يحتوي على مخزون)' }
        }
      } else {
        // التحقق من وجود حركات مخزون للمخزن
        const movementsCount = this.db.prepare('SELECT COUNT(*) as count FROM inventory_movements WHERE warehouse_id = ?').get([warehouseId]) as any
        if (movementsCount && movementsCount.count > 0) {
          // حذف ناعم - تعطيل المخزن بدلاً من حذفه
          const result = this.db.prepare(`
            UPDATE warehouses SET is_active = 0, updated_at = datetime('now')
            WHERE id = ?
          `).run([warehouseId])

          if (result.changes > 0) {
            DatabaseService.getInstance().saveDatabase()
            return { success: true, message: 'تم إلغاء تفعيل المخزن بنجاح (يحتوي على حركات مخزون)' }
          }
        } else {
          // حذف فعلي إذا لم يكن هناك مخزون أو حركات
          const result = this.db.prepare('DELETE FROM warehouses WHERE id = ?').run([warehouseId])

          if (result.changes > 0) {
            DatabaseService.getInstance().saveDatabase()
            return { success: true, message: 'تم حذف المخزن بنجاح' }
          }
        }
      }

      return { success: false, message: 'فشل في حذف المخزن' }
    } catch (error: any) {
      Logger.error('InventoryService', 'خطأ في حذف المخزن:', error)
      return { success: false, message: 'حدث خطأ في حذف المخزن' }
    }
  }

  // ===== إدارة الفئات =====

  public async getCategories(): Promise<Category[]> {
    try {
      const categories = this.db.prepare(`
        SELECT * FROM categories 
        WHERE is_active = 1
        ORDER BY path, name
      `).all() as Category[]

      return categories
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب الفئات:', error)
      return []
    }
  }

  public async createCategory(categoryData: any): Promise<ApiResponse> {
    try {
      // التحقق من صحة البيانات الأساسية
      if (!categoryData.name || categoryData.name.trim() === '') {
        return { success: false, message: 'اسم الفئة مطلوب' }
      }

      // التحقق من وجود الفئة الأب إذا تم تحديدها
      if (categoryData.parent_id) {
        const parentExists = this.db.prepare('SELECT id FROM categories WHERE id = ? AND is_active = 1').get(categoryData.parent_id)
        if (!parentExists) {
          return { success: false, message: 'الفئة الأب غير موجودة أو غير نشطة' }
        }
      }

      // حساب المستوى والمسار
      let level = 1  // الفئات الرئيسية تبدأ من المستوى 1
      let path = categoryData.name.trim()

      if (categoryData.parent_id) {
        const parent = this.db.prepare('SELECT level, path FROM categories WHERE id = ?').get([categoryData.parent_id]) as any
        if (parent) {
          level = parent.level + 1
          path = `${parent.path} > ${categoryData.name.trim()}`
        }
      }

      const result = this.db.prepare(`
        INSERT INTO categories (code, name, description, parent_id, level, path, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run([
        categoryData.code?.trim() || null,
        categoryData.name.trim(),
        categoryData.description?.trim() || null,
        categoryData.parent_id || null,
        level,
        path,
        categoryData.is_active ? 1 : 0
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return {
          success: true,
          message: 'تم إنشاء الفئة بنجاح',
          data: { id: result.lastInsertRowid }
        }
      } else {
        return { success: false, message: 'فشل في إنشاء الفئة' }
      }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في إنشاء الفئة:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الفئة' }
    }
  }

  public async updateCategory(categoryId: number, categoryData: any): Promise<ApiResponse> {
    try {
      // حساب المستوى والمسار الجديد
      let level = 1  // الفئات الرئيسية تبدأ من المستوى 1
      let path = categoryData.name

      if (categoryData.parent_id) {
        const parent = this.db.prepare('SELECT level, path FROM categories WHERE id = ?').get([categoryData.parent_id]) as any
        if (parent) {
          level = parent.level + 1
          path = `${parent.path} > ${categoryData.name}`
        }
      }

      const result = this.db.prepare(`
        UPDATE categories
        SET code = ?, name = ?, description = ?, parent_id = ?, level = ?, path = ?, is_active = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run([
        categoryData.code || null,
        categoryData.name,
        categoryData.description || null,
        categoryData.parent_id || null,
        level,
        path,
        categoryData.is_active ? 1 : 0,
        categoryId
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث الفئة بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث الفئة' }
      }
    } catch (error: any) {
      Logger.error('InventoryService', 'خطأ في تحديث الفئة:', error)
      if (error.message && error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود الفئة موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في تحديث الفئة' }
    }
  }

  public async deleteCategory(categoryId: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود أصناف مرتبطة بهذه الفئة
      const itemsCount = this.db.prepare('SELECT COUNT(*) as count FROM items WHERE category_id = ?').get([categoryId]) as any
      if (itemsCount && itemsCount.count > 0) {
        return { success: false, message: 'لا يمكن حذف الفئة لوجود أصناف مرتبطة بها' }
      }

      // التحقق من وجود فئات فرعية
      const subCategoriesCount = this.db.prepare('SELECT COUNT(*) as count FROM categories WHERE parent_id = ?').get([categoryId]) as any
      if (subCategoriesCount && subCategoriesCount.count > 0) {
        return { success: false, message: 'لا يمكن حذف الفئة لوجود فئات فرعية تابعة لها' }
      }

      const result = this.db.prepare('UPDATE categories SET is_active = 0, updated_at = datetime(\'now\') WHERE id = ?').run([categoryId])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم حذف الفئة بنجاح' }
      } else {
        return { success: false, message: 'فشل في حذف الفئة' }
      }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في حذف الفئة:', error)
      return { success: false, message: 'حدث خطأ في حذف الفئة' }
    }
  }

  public async renameCategory(categoryId: number, newName: string): Promise<ApiResponse> {
    try {
      // الحصول على بيانات الفئة الحالية
      const category = this.db.prepare('SELECT * FROM categories WHERE id = ?').get([categoryId]) as any
      if (!category) {
        return { success: false, message: 'الفئة غير موجودة' }
      }

      // حساب المسار الجديد
      let newPath = newName
      if (category.parent_id) {
        const parent = this.db.prepare('SELECT path FROM categories WHERE id = ?').get([category.parent_id]) as any
        if (parent) {
          newPath = `${parent.path} > ${newName}`
        }
      }

      const result = this.db.prepare(`
        UPDATE categories
        SET name = ?, path = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run([newName, newPath, categoryId])

      if (result.changes > 0) {
        // تحديث مسارات الفئات الفرعية
        await this.updateSubCategoriesPaths(categoryId, newPath)

        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تغيير اسم الفئة بنجاح' }
      } else {
        return { success: false, message: 'فشل في تغيير اسم الفئة' }
      }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تغيير اسم الفئة:', error)
      return { success: false, message: 'حدث خطأ في تغيير اسم الفئة' }
    }
  }

  private async updateSubCategoriesPaths(parentId: number, parentPath: string): Promise<void> {
    try {
      const subCategories = this.db.prepare('SELECT id, name FROM categories WHERE parent_id = ?').all([parentId]) as any[]

      for (const subCategory of subCategories) {
        const newPath = `${parentPath} > ${subCategory.name}`
        this.db.prepare('UPDATE categories SET path = ? WHERE id = ?').run([newPath, subCategory.id])

        // تحديث الفئات الفرعية للفئة الفرعية (تكرار)
        await this.updateSubCategoriesPaths(subCategory.id, newPath)
      }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تحديث مسارات الفئات الفرعية:', error)
    }
  }

  // ===== إدارة الأصناف =====

  public async getItems(): Promise<Item[]> {
    try {
      const items = this.db.prepare(`
        SELECT i.*, c.name as category_name, c.path as category_path,
               w.name as warehouse_name,
               COALESCE(inv.quantity, 0) as available_quantity
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        LEFT JOIN warehouses w ON i.warehouse_id = w.id
        LEFT JOIN inventory inv ON i.id = inv.item_id AND i.warehouse_id = inv.warehouse_id
        WHERE i.is_active = 1
        ORDER BY i.name
      `).all() as Item[]

      return items
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب الأصناف:', error)
      return []
    }
  }

  // الحصول على الأصناف حسب النوع
  public async getItemsByType(type: string): Promise<Item[]> {
    try {
      const items = this.db.prepare(`
        SELECT i.*, c.name as category_name, c.path as category_path,
               w.name as warehouse_name,
               COALESCE(inv.quantity, 0) as available_quantity
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        LEFT JOIN warehouses w ON i.warehouse_id = w.id
        LEFT JOIN inventory inv ON i.id = inv.item_id AND i.warehouse_id = inv.warehouse_id
        WHERE i.is_active = 1 AND i.type = ?
        ORDER BY i.name
      `).all(type) as Item[]

      return items
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب الأصناف حسب النوع:', error)
      return []
    }
  }

  // الحصول على الأصناف مع معلومات المخزون من جميع المخازن
  public async getItemsWithInventory(): Promise<any[]> {
    try {
      // جلب جميع الأصناف النشطة
      const items = this.db.prepare(`
        SELECT i.*, c.name as category_name, c.path as category_path
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        WHERE i.is_active = 1
        ORDER BY i.name
      `).all() as any[]

      // جلب معلومات المخزون لكل صنف من جميع المخازن
      const inventory = this.db.prepare(`
        SELECT inv.*, w.name as warehouse_name, w.code as warehouse_code
        FROM inventory inv
        JOIN warehouses w ON inv.warehouse_id = w.id
        WHERE w.is_active = 1
        ORDER BY inv.item_id, w.name
      `).all() as any[]

      // ربط معلومات المخزون بالأصناف
      const itemsWithInventory = items.map(item => {
        const itemInventory = inventory.filter(inv => inv.item_id === item.id)
        return {
          ...item,
          inventory: itemInventory,
          total_quantity: itemInventory.reduce((sum, inv) => sum + (inv.quantity || 0), 0),
          total_available: itemInventory.reduce((sum, inv) => sum + ((inv.quantity || 0) - (inv.reserved_quantity || 0)), 0)
        }
      })

      Logger.info('InventoryService', `تم جلب ${itemsWithInventory.length} صنف مع معلومات المخزون`)
      return itemsWithInventory
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب الأصناف مع معلومات المخزون:', error)
      return []
    }
  }

  // الحصول على أنواع الأصناف المتاحة
  public async getItemTypes(): Promise<string[]> {
    return ['raw_material', 'finished_product', 'component', 'tool', 'consumable']
  }

  public async getItemsByWarehouse(warehouseId: number): Promise<Item[]> {
    try {
      // التحقق من صحة معرف المخزن
      if (!warehouseId || typeof warehouseId !== 'number' || warehouseId <= 0) {
        Logger.error('InventoryService', 'معرف المخزن غير صحيح:', warehouseId)
        return []
      }

      const items = this.db.prepare(`
        SELECT i.*, c.name as category_name, c.path as category_path,
               COALESCE(inv.quantity, 0) as available_quantity,
               COALESCE(inv.reserved_quantity, 0) as reserved_quantity,
               COALESCE(inv.quantity, 0) - COALESCE(inv.reserved_quantity, 0) as free_quantity,
               i.sale_price, i.cost_price, i.min_quantity, i.max_quantity,
               inv.location, inv.notes as inventory_notes, inv.last_updated
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        LEFT JOIN inventory inv ON i.id = inv.item_id AND inv.warehouse_id = ?
        WHERE i.is_active = 1
        ORDER BY i.name
      `).all([warehouseId]) as Item[]

      Logger.info('InventoryService', `✅ تم جلب ${items.length} صنف للمخزن ${warehouseId}`)
      return items
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب أصناف المخزن:', error)
      return []
    }
  }

  public async createItem(itemData: any): Promise<ApiResponse> {
    try {
      // التحقق من صحة البيانات الأساسية
      if (!itemData.code || itemData.code.trim() === '') {
        return { success: false, message: 'كود الصنف مطلوب' }
      }

      if (!itemData.name || itemData.name.trim() === '') {
        return { success: false, message: 'اسم الصنف مطلوب' }
      }

      // التحقق من عدم تكرار الكود
      const existingItem = this.db.prepare('SELECT id FROM items WHERE code = ?').get(itemData.code.trim())
      if (existingItem) {
        return { success: false, message: 'كود الصنف موجود بالفعل' }
      }

      const result = this.db.prepare(`
        INSERT INTO items (
          code, name, description, category_id, warehouse_id, type, unit,
          cost_price, sale_price, min_quantity, max_quantity, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run([
        itemData.code.trim(),
        itemData.name.trim(),
        itemData.description?.trim() || null,
        itemData.category_id || null,
        itemData.warehouse_id || null,
        itemData.type || 'raw_material',
        itemData.unit?.trim() || 'قطعة',
        itemData.cost_price || 0,
        itemData.sale_price || 0,
        itemData.min_quantity || 0,
        itemData.max_quantity || 0,
        itemData.is_active ? 1 : 0
      ])

      if (result.changes > 0) {
        // إنشاء سجل مخزون أولي إذا تم تحديد مخزن
        if (itemData.warehouse_id) {
          this.db.prepare(`
            INSERT OR IGNORE INTO inventory (item_id, warehouse_id, quantity, cost_price)
            VALUES (?, ?, 0, ?)
          `).run([result.lastInsertRowid, itemData.warehouse_id, itemData.cost_price || 0])
        }

        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return {
          success: true,
          message: 'تم إنشاء الصنف بنجاح',
          data: { id: result.lastInsertRowid }
        }
      } else {
        return { success: false, message: 'فشل في إنشاء الصنف' }
      }
    } catch (error: any) {
      Logger.error('InventoryService', 'خطأ في إنشاء الصنف:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود الصنف موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في إنشاء الصنف' }
    }
  }

  // تحديث صنف
  public async updateItem(itemId: number, itemData: any): Promise<ApiResponse> {
    try {
      // التحقق من وجود الصنف
      const existingItem = this.db.prepare('SELECT * FROM items WHERE id = ?').get([itemId])
      if (!existingItem) {
        return { success: false, message: 'الصنف غير موجود' }
      }

      // التحقق من تفرد الكود (إذا تم تغييره)
      if (itemData.code && itemData.code !== (existingItem as any).code) {
        const codeExists = this.db.prepare('SELECT id FROM items WHERE code = ? AND id != ?').get([itemData.code, itemId])
        if (codeExists) {
          return { success: false, message: 'كود الصنف موجود مسبقاً' }
        }
      }

      const result = this.db.prepare(`
        UPDATE items SET
          code = ?, name = ?, description = ?, category_id = ?, warehouse_id = ?, type = ?,
          unit = ?, cost_price = ?, sale_price = ?, min_quantity = ?, max_quantity = ?,
          is_active = ?, updated_at = datetime('now')
        WHERE id = ?
      `).run([
        itemData.code?.trim() || (existingItem as any).code,
        itemData.name?.trim() || (existingItem as any).name,
        itemData.description?.trim() || (existingItem as any).description,
        itemData.category_id || (existingItem as any).category_id,
        itemData.warehouse_id || (existingItem as any).warehouse_id,
        itemData.type || (existingItem as any).type || 'raw_material',
        itemData.unit?.trim() || (existingItem as any).unit,
        itemData.cost_price !== undefined ? itemData.cost_price : (existingItem as any).cost_price,
        itemData.sale_price !== undefined ? itemData.sale_price : (existingItem as any).sale_price,
        itemData.min_quantity !== undefined ? itemData.min_quantity : (existingItem as any).min_quantity,
        itemData.max_quantity !== undefined ? itemData.max_quantity : (existingItem as any).max_quantity,
        itemData.is_active !== undefined ? (itemData.is_active ? 1 : 0) : (existingItem as any).is_active,
        itemId
      ])

      if (result.changes > 0) {
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return { success: true, message: 'تم تحديث الصنف بنجاح' }
      } else {
        return { success: false, message: 'فشل في تحديث الصنف' }
      }
    } catch (error: any) {
      Logger.error('InventoryService', 'خطأ في تحديث الصنف:', error)
      if (error.message.includes('UNIQUE constraint failed')) {
        return { success: false, message: 'كود الصنف موجود مسبقاً' }
      }
      return { success: false, message: 'حدث خطأ في تحديث الصنف' }
    }
  }

  // حذف صنف (حذف ناعم)
  public async deleteItem(itemId: number): Promise<ApiResponse> {
    try {
      // التحقق من وجود الصنف
      const existingItem = this.db.prepare('SELECT * FROM items WHERE id = ?').get([itemId])
      if (!existingItem) {
        return { success: false, message: 'الصنف غير موجود' }
      }

      // التحقق من وجود حركات مخزون للصنف
      const movementsCount = this.db.prepare('SELECT COUNT(*) as count FROM inventory_movements WHERE item_id = ?').get([itemId]) as any
      if (movementsCount && movementsCount.count > 0) {
        // حذف ناعم - تعطيل الصنف بدلاً من حذفه
        const result = this.db.prepare(`
          UPDATE items SET is_active = 0, updated_at = datetime('now')
          WHERE id = ?
        `).run([itemId])

        if (result.changes > 0) {
          DatabaseService.getInstance().saveDatabase()
          return { success: true, message: 'تم إلغاء تفعيل الصنف بنجاح' }
        }
      } else {
        // حذف فعلي إذا لم توجد حركات مخزون
        const result = this.db.prepare('DELETE FROM items WHERE id = ?').run([itemId])

        if (result.changes > 0) {
          DatabaseService.getInstance().saveDatabase()
          return { success: true, message: 'تم حذف الصنف بنجاح' }
        }
      }

      return { success: false, message: 'فشل في حذف الصنف' }
    } catch (error: any) {
      Logger.error('InventoryService', 'خطأ في حذف الصنف:', error)
      return { success: false, message: 'حدث خطأ في حذف الصنف' }
    }
  }

  // ===== إدارة المخزون =====

  public async getInventory(): Promise<Inventory[]> {
    try {
      const inventory = this.db.prepare(`
        SELECT inv.*, i.code as item_code, i.name as item_name, i.unit,
               i.min_quantity, i.max_quantity, i.sale_price,
               COALESCE(inv.cost_price, i.cost_price, 0) as cost_price,
               w.name as warehouse_name, c.name as category_name
        FROM inventory inv
        JOIN items i ON inv.item_id = i.id
        JOIN warehouses w ON inv.warehouse_id = w.id
        LEFT JOIN categories c ON i.category_id = c.id
        WHERE i.is_active = 1 AND w.is_active = 1
        ORDER BY i.name, w.name
      `).all([]) as Inventory[]

      return inventory
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب المخزون:', error)
      return []
    }
  }

  public async createInventoryMovement(movementData: any): Promise<ApiResponse> {
    try {
      // التحقق من صحة البيانات الأساسية
      if (!movementData.item_id) {
        return { success: false, message: 'معرف الصنف مطلوب' }
      }

      if (!movementData.warehouse_id) {
        return { success: false, message: 'معرف المخزن مطلوب' }
      }

      if (!movementData.movement_type || !['in', 'out', 'transfer', 'adjustment'].includes(movementData.movement_type)) {
        return { success: false, message: 'نوع الحركة غير صحيح' }
      }

      if (!movementData.quantity || movementData.quantity <= 0) {
        return { success: false, message: 'الكمية يجب أن تكون أكبر من صفر' }
      }

      // التحقق من وجود الصنف والمخزن
      const itemExists = this.db.prepare('SELECT id FROM items WHERE id = ? AND is_active = 1').get(movementData.item_id)
      if (!itemExists) {
        return { success: false, message: 'الصنف غير موجود أو غير نشط' }
      }

      const warehouseExists = this.db.prepare('SELECT id FROM warehouses WHERE id = ? AND is_active = 1').get(movementData.warehouse_id)
      if (!warehouseExists) {
        return { success: false, message: 'المخزن غير موجود أو غير نشط' }
      }

      // توليد رقم الحركة
      const movementNumber = await this.generateMovementNumber(movementData.movement_type)

      // إدراج حركة المخزون
      const movementResult = this.db.prepare(`
        INSERT INTO inventory_movements (
          movement_number, item_id, warehouse_id, movement_type, quantity,
          reference_type, reference_id, notes, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run([
        movementNumber,
        movementData.item_id,
        movementData.warehouse_id,
        movementData.movement_type,
        movementData.quantity,
        movementData.reference_type || null,
        movementData.reference_id || null,
        movementData.notes || null,
        movementData.created_by || null
      ])

      // تحديث المخزون
      this.updateInventoryQuantity(
        movementData.item_id,
        movementData.warehouse_id,
        movementData.movement_type,
        movementData.quantity
      )

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()
      return {
        success: true,
        message: 'تم تسجيل حركة المخزون بنجاح',
        data: {
          id: movementResult.lastInsertRowid,
          movementNumber: movementNumber
        }
      }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في إنشاء حركة المخزون:', error)
      return { success: false, message: 'حدث خطأ في تسجيل حركة المخزون' }
    }
  }

  public updateInventoryQuantity(itemId: number, warehouseId: number, movementType: string, quantity: number, costPrice?: number): void {
    // التأكد من وجود سجل المخزون
    this.db.prepare(`
      INSERT OR IGNORE INTO inventory (item_id, warehouse_id, quantity)
      VALUES (?, ?, 0)
    `).run([itemId, warehouseId])

    // تحديث الكمية حسب نوع الحركة
    let quantityChange = 0
    switch (movementType) {
      case 'in':
        quantityChange = quantity
        break
      case 'out':
        quantityChange = -quantity
        break
      case 'adjustment': {
        // في حالة التسوية، نحدد الكمية الجديدة مباشرة
        let adjustmentQuery = `
          UPDATE inventory
          SET quantity = ?, last_updated = datetime('now')
        `
        const adjustmentParams = [quantity]

        if (costPrice !== undefined) {
          adjustmentQuery += `, cost_price = ?`
          adjustmentParams.push(costPrice)
        }

        adjustmentQuery += ` WHERE item_id = ? AND warehouse_id = ?`
        adjustmentParams.push(itemId, warehouseId)

        this.db.prepare(adjustmentQuery).run(adjustmentParams)
        // حفّ قاعدة البيانات
        DatabaseService.getInstance().saveDatabase()
        return
      }
    }

    if (quantityChange !== 0) {
      let updateQuery = `
        UPDATE inventory
        SET quantity = quantity + ?, last_updated = datetime('now')
      `
      const updateParams = [quantityChange]

      if (costPrice !== undefined) {
        updateQuery += `, cost_price = ?`
        updateParams.push(costPrice)
      }

      updateQuery += ` WHERE item_id = ? AND warehouse_id = ?`
      updateParams.push(itemId, warehouseId)

      this.db.prepare(updateQuery).run(updateParams)
      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()
    }
  }

  // تحديث أسعار التكلفة في المخزون من جدول الأصناف
  public updateInventoryCostPrices(): void {
    try {
      Logger.info('InventoryService', '🔄 بدء تحديث أسعار التكلفة في المخزون...')

      const result = this.db.prepare(`
        UPDATE inventory
        SET cost_price = (
          SELECT i.cost_price
          FROM items i
          WHERE i.id = inventory.item_id
        )
        WHERE cost_price = 0 OR cost_price IS NULL
      `).run()

      Logger.info('InventoryService', `✅ تم تحديث أسعار التكلفة لـ ${result.changes} سجل مخزون`)

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تحديث أسعار التكلفة في المخزون:', error)
    }
  }

  // توليد رقم حركة مخزون جديد
  public async generateMovementNumber(movementType: string): Promise<string> {
    try {
      // تحديد البادئة حسب نوع الحركة
      let prefix = 'MOV'
      switch (movementType) {
        case 'in':
          prefix = 'IN'
          break
        case 'out':
          prefix = 'OUT'
          break
        case 'transfer':
          prefix = 'TRF'
          break
        case 'adjustment':
          prefix = 'ADJ'
          break
        default:
          prefix = 'MOV'
      }

      // الحصول على آخر رقم حركة
      const lastMovement = this.db.prepare(`
        SELECT movement_number FROM inventory_movements
        WHERE movement_number LIKE '${prefix}%'
        ORDER BY CAST(SUBSTR(movement_number, ${prefix.length + 1}) AS INTEGER) DESC
        LIMIT 1
      `).get([]) as any

      let nextNumber = 1
      if (lastMovement && lastMovement.movement_number) {
        const lastNumber = parseInt(lastMovement.movement_number.substring(prefix.length))
        nextNumber = lastNumber + 1
      }

      return `${prefix}${nextNumber.toString().padStart(4, '0')}`
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في توليد رقم الحركة:', error)
      return `MOV${Date.now().toString().slice(-4)}`
    }
  }

  // توليد كود فئة جديد
  public async generateCategoryCode(): Promise<string> {
    try {
      const lastCategory = this.db.prepare(`
        SELECT code FROM categories
        WHERE code LIKE 'CAT%' AND code IS NOT NULL AND code != ''
        ORDER BY CAST(SUBSTR(code, 4) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastCategory && lastCategory.code && lastCategory.code.length >= 6) {
        const codeNumber = lastCategory.code.substring(3)
        const lastNumber = parseInt(codeNumber)

        if (!isNaN(lastNumber)) {
          const newNumber = lastNumber + 1
          return `CAT${newNumber.toString().padStart(3, '0')}`
        }
      }

      return 'CAT001'
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في توليد كود الفئة:', error)
      return `CAT${Date.now().toString().slice(-3)}`
    }
  }

  // توليد كود مخزن جديد
  public async generateWarehouseCode(): Promise<string> {
    try {
      const lastWarehouse = this.db.prepare(`
        SELECT code FROM warehouses
        WHERE code LIKE 'WH%' AND code IS NOT NULL AND code != ''
        ORDER BY CAST(SUBSTR(code, 3) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      if (lastWarehouse && lastWarehouse.code && lastWarehouse.code.length >= 5) {
        const codeNumber = lastWarehouse.code.substring(2)
        const lastNumber = parseInt(codeNumber)

        if (!isNaN(lastNumber)) {
          const newNumber = lastNumber + 1
          return `WH${newNumber.toString().padStart(3, '0')}`
        }
      }

      return 'WH001'
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في توليد كود المخزن:', error)
      return `WH${Date.now().toString().slice(-3)}`
    }
  }

  // توليد كود صنف جديد
  public async generateItemCode(categoryId?: number): Promise<string> {
    try {
      let prefix = 'ITM'

      if (categoryId) {
        const category = this.db.prepare('SELECT name FROM categories WHERE id = ?').get([categoryId]) as any
        if (category && category.name) {
          // استخدام أول 3 أحرف من اسم الفئة
          const categoryPrefix = category.name.substring(0, 3).toUpperCase().replace(/[^A-Z]/g, '')
          if (categoryPrefix.length >= 2) {
            prefix = categoryPrefix
          }
        }
      }

      // الحصول على آخر رقم
      const lastItem = this.db.prepare(`
        SELECT code FROM items
        WHERE code LIKE '${prefix}%' AND code IS NOT NULL AND code != ''
        ORDER BY CAST(SUBSTR(code, ${prefix.length + 1}) AS INTEGER) DESC
        LIMIT 1
      `).get() as any

      let nextNumber = 1
      if (lastItem && lastItem.code && lastItem.code.length > prefix.length) {
        const codeNumber = lastItem.code.substring(prefix.length)
        const lastNumber = parseInt(codeNumber)
        if (!isNaN(lastNumber)) {
          nextNumber = lastNumber + 1
        }
      }

      return `${prefix}${nextNumber.toString().padStart(3, '0')}`
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في توليد كود الصنف:', error)
      return `ITM${Date.now().toString().slice(-3)}`
    }
  }

  // ===== إدارة حركات المخزون =====

  public async getInventoryMovements(filters?: any): Promise<InventoryMovement[]> {
    try {
      let query = `
        SELECT
          im.*,
          i.code as item_code,
          i.name as item_name,
          i.unit,
          w.name as warehouse_name,
          c.name as category_name,
          u.full_name as created_by_name
        FROM inventory_movements im
        JOIN items i ON im.item_id = i.id
        JOIN warehouses w ON im.warehouse_id = w.id
        LEFT JOIN categories c ON i.category_id = c.id
        LEFT JOIN users u ON im.created_by = u.id
        WHERE 1=1
      `

      const params: any[] = []

      // تطبيق الفلاتر
      if (filters) {
        if (filters.item_id) {
          query += ` AND im.item_id = ?`
          params.push(filters.item_id)
        }
        if (filters.warehouse_id) {
          query += ` AND im.warehouse_id = ?`
          params.push(filters.warehouse_id)
        }
        if (filters.movement_type) {
          query += ` AND im.movement_type = ?`
          params.push(filters.movement_type)
        }
        if (filters.reference_type) {
          query += ` AND im.reference_type = ?`
          params.push(filters.reference_type)
        }
        if (filters.date_from) {
          query += ` AND DATE(im.created_at) >= ?`
          params.push(filters.date_from)
        }
        if (filters.date_to) {
          query += ` AND DATE(im.created_at) <= ?`
          params.push(filters.date_to)
        }
      }

      query += ` ORDER BY im.created_at DESC`

      const movements = this.db.prepare(query).all(params) as InventoryMovement[]
      return movements
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب حركات المخزون:', error)
      return []
    }
  }

  // ===== التقارير =====

  public async getInventoryReport(filters?: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          i.id as item_id,
          i.code as item_code,
          i.name as item_name,
          i.unit,
          i.cost_price,
          i.sale_price,
          i.min_quantity,
          i.max_quantity,
          c.name as category_name,
          w.id as warehouse_id,
          w.name as warehouse_name,
          COALESCE(inv.quantity, 0) as quantity,
          COALESCE(inv.reserved_quantity, 0) as reserved_quantity,
          COALESCE(inv.quantity, 0) - COALESCE(inv.reserved_quantity, 0) as available_quantity,
          COALESCE(inv.quantity, 0) * COALESCE(inv.cost_price, i.cost_price, 0) as total_value,
          inv.location,
          inv.last_updated
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        LEFT JOIN warehouses w ON i.warehouse_id = w.id
        LEFT JOIN inventory inv ON i.id = inv.item_id AND i.warehouse_id = inv.warehouse_id
        WHERE i.is_active = 1
      `

      const params: any[] = []

      if (filters) {
        if (filters.warehouseId) {
          query += ` AND w.id = ?`
          params.push(filters.warehouseId)
        }
        if (filters.categoryId) {
          query += ` AND c.id = ?`
          params.push(filters.categoryId)
        }
        if (filters.itemId) {
          query += ` AND i.id = ?`
          params.push(filters.itemId)
        }
      }

      query += ` ORDER BY i.name, w.name`

      const report = this.db.prepare(query).all(params)
      return report
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تقرير المخزون:', error)
      return []
    }
  }

  public async getInventoryMovementsReport(filters?: any): Promise<any[]> {
    try {
      return await this.getInventoryMovements(filters)
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تقرير حركات المخزون:', error)
      return []
    }
  }

  public async getLowStockReport(filters?: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          i.id as item_id,
          i.code as item_code,
          i.name as item_name,
          i.unit,
          i.min_quantity,
          i.max_quantity,
          c.name as category_name,
          w.name as warehouse_name,
          COALESCE(inv.quantity, 0) as current_quantity,
          COALESCE(inv.reserved_quantity, 0) as reserved_quantity,
          COALESCE(inv.quantity, 0) - COALESCE(inv.reserved_quantity, 0) as available_quantity,
          i.min_quantity - COALESCE(inv.quantity, 0) as shortage_quantity
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        LEFT JOIN warehouses w ON i.warehouse_id = w.id
        LEFT JOIN inventory inv ON i.id = inv.item_id AND i.warehouse_id = inv.warehouse_id
        WHERE i.is_active = 1
          AND i.min_quantity > 0
          AND COALESCE(inv.quantity, 0) <= i.min_quantity
      `

      const params: any[] = []

      if (filters) {
        if (filters.warehouseId) {
          query += ` AND w.id = ?`
          params.push(filters.warehouseId)
        }
        if (filters.categoryId) {
          query += ` AND c.id = ?`
          params.push(filters.categoryId)
        }
      }

      query += ` ORDER BY shortage_quantity DESC, i.name`

      const report = this.db.prepare(query).all(params)
      return report
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تقرير المخزون المنخفض:', error)
      return []
    }
  }

  public async getItemWarehouseQuantity(itemId: number, warehouseId: number): Promise<any> {
    try {
      const result = this.db.prepare(`
        SELECT
          inv.quantity,
          inv.reserved_quantity,
          inv.quantity - COALESCE(inv.reserved_quantity, 0) as available_quantity,
          inv.location,
          inv.last_updated,
          i.name as item_name,
          i.unit,
          w.name as warehouse_name
        FROM inventory inv
        JOIN items i ON inv.item_id = i.id
        JOIN warehouses w ON inv.warehouse_id = w.id
        WHERE inv.item_id = ? AND inv.warehouse_id = ?
      `).get([itemId, warehouseId])

      return result || {
        quantity: 0,
        reserved_quantity: 0,
        available_quantity: 0,
        location: null,
        last_updated: null
      }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب كمية الصنف:', error)
      return {
        quantity: 0,
        reserved_quantity: 0,
        available_quantity: 0,
        location: null,
        last_updated: null
      }
    }
  }

  // جلب توزيع الأصناف على المخازن
  public async getItemWarehouseDistribution(params?: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          i.id as item_id,
          i.code as item_code,
          i.name as item_name,
          c.name as category_name,
          i.unit,
          w.id as warehouse_id,
          w.name as warehouse_name,
          COALESCE(inv.quantity, 0) as quantity,
          COALESCE(inv.reserved_quantity, 0) as reserved_quantity,
          COALESCE(inv.quantity, 0) - COALESCE(inv.reserved_quantity, 0) as available_quantity,
          i.cost_price,
          i.sale_price,
          COALESCE(inv.quantity, 0) * i.cost_price as inventory_value,
          i.min_quantity,
          i.max_quantity,
          inv.last_updated as last_movement_date,
          CASE
            WHEN COALESCE(inv.quantity, 0) = 0 THEN 'out'
            WHEN COALESCE(inv.quantity, 0) < i.min_quantity THEN 'low'
            WHEN COALESCE(inv.quantity, 0) > i.max_quantity THEN 'high'
            ELSE 'normal'
          END as status
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        CROSS JOIN warehouses w
        LEFT JOIN inventory inv ON i.id = inv.item_id AND w.id = inv.warehouse_id
        WHERE i.is_active = 1 AND w.is_active = 1
      `

      const queryParams: any[] = []

      // تطبيق الفلاتر
      if (params?.item_id) {
        query += ` AND i.id = ?`
        queryParams.push(params.item_id)
      }

      if (params?.warehouse_id) {
        query += ` AND w.id = ?`
        queryParams.push(params.warehouse_id)
      }

      if (params?.category_id) {
        query += ` AND i.category_id = ?`
        queryParams.push(params.category_id)
      }

      if (params?.status) {
        if (params.status === 'out') {
          query += ` AND COALESCE(inv.quantity, 0) = 0`
        } else if (params.status === 'low') {
          query += ` AND COALESCE(inv.quantity, 0) < i.min_quantity AND COALESCE(inv.quantity, 0) > 0`
        } else if (params.status === 'high') {
          query += ` AND COALESCE(inv.quantity, 0) > i.max_quantity`
        } else if (params.status === 'normal') {
          query += ` AND COALESCE(inv.quantity, 0) >= i.min_quantity AND COALESCE(inv.quantity, 0) <= i.max_quantity`
        }
      }

      query += ` ORDER BY i.name, w.name`

      const results = this.db.prepare(query).all(queryParams)
      return results
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب توزيع الأصناف على المخازن:', error)
      return []
    }
  }

  // ===== نّام التنبيهات =====

  public async getInventoryAlerts(): Promise<any[]> {
    try {
      const alerts: any[] = []

      // تنبيهات المخزون المنخفض
      const lowStockItems = this.db.prepare(`
        SELECT
          i.id,
          i.code,
          i.name,
          i.unit,
          i.min_quantity,
          w.name as warehouse_name,
          COALESCE(inv.quantity, 0) as current_quantity,
          'low_stock' as alert_type,
          'تحذير: مخزون منخفض' as alert_message,
          'warning' as severity
        FROM items i
        LEFT JOIN warehouses w ON i.warehouse_id = w.id
        LEFT JOIN inventory inv ON i.id = inv.item_id AND i.warehouse_id = inv.warehouse_id
        WHERE i.is_active = 1
          AND i.min_quantity > 0
          AND COALESCE(inv.quantity, 0) <= i.min_quantity
      `).all()

      alerts.push(...lowStockItems)

      // تنبيهات المخزون الزائد
      const overStockItems = this.db.prepare(`
        SELECT
          i.id,
          i.code,
          i.name,
          i.unit,
          i.max_quantity,
          w.name as warehouse_name,
          COALESCE(inv.quantity, 0) as current_quantity,
          'over_stock' as alert_type,
          'تحذير: مخزون زائد' as alert_message,
          'info' as severity
        FROM items i
        LEFT JOIN warehouses w ON i.warehouse_id = w.id
        LEFT JOIN inventory inv ON i.id = inv.item_id AND i.warehouse_id = inv.warehouse_id
        WHERE i.is_active = 1
          AND i.max_quantity > 0
          AND COALESCE(inv.quantity, 0) >= i.max_quantity
      `).all()

      alerts.push(...overStockItems)

      // تنبيهات الأصناف بدون حركة لفترة طويلة
      const staleItems = this.db.prepare(`
        SELECT
          i.id,
          i.code,
          i.name,
          i.unit,
          w.name as warehouse_name,
          COALESCE(inv.quantity, 0) as current_quantity,
          'stale_stock' as alert_type,
          'تحذير: لا توجد حركة لأكثر من 90 يوم' as alert_message,
          'warning' as severity
        FROM items i
        LEFT JOIN warehouses w ON i.warehouse_id = w.id
        LEFT JOIN inventory inv ON i.id = inv.item_id AND i.warehouse_id = inv.warehouse_id
        WHERE i.is_active = 1
          AND COALESCE(inv.quantity, 0) > 0
          AND (
            inv.last_updated IS NULL
            OR DATE(inv.last_updated) < DATE('now', '-90 days')
          )
      `).all()

      alerts.push(...staleItems)

      return alerts
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب تنبيهات المخزون:', error)
      return []
    }
  }

  public async getInventoryStatistics(): Promise<any> {
    try {
      const stats = {
        totalItems: 0,
        totalWarehouses: 0,
        totalCategories: 0,
        totalValue: 0,
        lowStockItems: 0,
        overStockItems: 0,
        outOfStockItems: 0,
        recentMovements: 0
      }

      // إجمالي الأصناف النشطة
      const totalItems = this.db.prepare(`
        SELECT COUNT(*) as count FROM items WHERE is_active = 1
      `).get() as any
      stats.totalItems = totalItems?.count || 0

      // إجمالي المخازن النشطة
      const totalWarehouses = this.db.prepare(`
        SELECT COUNT(*) as count FROM warehouses WHERE is_active = 1
      `).get() as any
      stats.totalWarehouses = totalWarehouses?.count || 0

      // إجمالي الفئات النشطة
      const totalCategories = this.db.prepare(`
        SELECT COUNT(*) as count FROM categories WHERE is_active = 1
      `).get() as any
      stats.totalCategories = totalCategories?.count || 0

      // إجمالي قيمة المخزون
      const totalValue = this.db.prepare(`
        SELECT SUM(COALESCE(inv.quantity, 0) * COALESCE(inv.cost_price, i.cost_price, 0)) as total
        FROM items i
        LEFT JOIN inventory inv ON i.id = inv.item_id
        WHERE i.is_active = 1 AND COALESCE(inv.quantity, 0) > 0
      `).get() as any
      stats.totalValue = totalValue?.total || 0

      // عدد الأصناف منخفضة المخزون
      const lowStockItems = this.db.prepare(`
        SELECT COUNT(*) as count
        FROM items i
        LEFT JOIN inventory inv ON i.id = inv.item_id
        WHERE i.is_active = 1
          AND i.min_quantity > 0
          AND COALESCE(inv.quantity, 0) <= i.min_quantity
      `).get() as any
      stats.lowStockItems = lowStockItems?.count || 0

      // عدد الأصناف زائدة المخزون
      const overStockItems = this.db.prepare(`
        SELECT COUNT(*) as count
        FROM items i
        LEFT JOIN inventory inv ON i.id = inv.item_id
        WHERE i.is_active = 1
          AND i.max_quantity > 0
          AND COALESCE(inv.quantity, 0) >= i.max_quantity
      `).get() as any
      stats.overStockItems = overStockItems?.count || 0

      // عدد الأصناف نافدة المخزون
      const outOfStockItems = this.db.prepare(`
        SELECT COUNT(*) as count
        FROM items i
        LEFT JOIN inventory inv ON i.id = inv.item_id
        WHERE i.is_active = 1
          AND COALESCE(inv.quantity, 0) = 0
      `).get() as any
      stats.outOfStockItems = outOfStockItems?.count || 0

      // عدد الحركات الحديثة (آخر 7 أيام)
      const recentMovements = this.db.prepare(`
        SELECT COUNT(*) as count
        FROM inventory_movements
        WHERE DATE(created_at) >= DATE('now', '-7 days')
      `).get() as any
      stats.recentMovements = recentMovements?.count || 0

      return stats
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في جلب إحصائيات المخزون:', error)
      return {
        totalItems: 0,
        totalWarehouses: 0,
        totalCategories: 0,
        totalValue: 0,
        lowStockItems: 0,
        overStockItems: 0,
        outOfStockItems: 0,
        recentMovements: 0
      }
    }
  }

  // ===== إضافة بيانات تجريبية للمخزون =====

  public async addSampleInventoryData(): Promise<ApiResponse> {
    try {
      // التأكد من وجود أصناف
      const items = this.db.prepare('SELECT id, code, name, cost_price FROM items WHERE is_active = 1 LIMIT 10').all()
      if (items.length === 0) {
        return { success: false, message: 'يجب إضافة أصناف أولاً' }
      }

      // التأكد من وجود مخازن
      const warehouses = this.db.prepare('SELECT id, name FROM warehouses WHERE is_active = 1 LIMIT 3').all()
      if (warehouses.length === 0) {
        return { success: false, message: 'يجب إضافة مخازن أولاً' }
      }

      let addedCount = 0

      // إنشاء بيانات مخزون تجريبية (محسن للأداء)
      const inventoryData: any[] = []

      // تحضير البيانات بطريقة محسنة (بدون حلقات متداخلة)
      const checkExistingStmt = this.db.prepare('SELECT id FROM inventory WHERE item_id = ? AND warehouse_id = ?')

      // إنشاء جميع التركيبات الممكنة
      const itemWarehousePairs = items.flatMap(item =>
        warehouses.map(warehouse => ({ item, warehouse }))
      )

      // فلترة التركيبات غير الموجودة وإنشاء البيانات
      itemWarehousePairs.forEach(({ item, warehouse }) => {
        const existingInventory = checkExistingStmt.get([item.id, warehouse.id])

        if (!existingInventory) {
          // إنشاء كمية عشوائية بين 10 و 100
          const quantity = Math.floor(Math.random() * 90) + 10
          const reservedQuantity = Math.floor(quantity * 0.1) // 10% محجوز
          const costPrice = item.cost_price || (Math.floor(Math.random() * 100) + 20)

          inventoryData.push([
            item.id,
            warehouse.id,
            quantity,
            reservedQuantity,
            costPrice,
            `رف ${Math.floor(Math.random() * 10) + 1}`,
            `مخزون تجريبي للصنف ${item.name}`
          ])
        }
      })

      // إدراج البيانات دفعة واحدة (أسرع)
      if (inventoryData.length > 0) {
        const insertStmt = this.db.prepare(`
          INSERT INTO inventory (
            item_id, warehouse_id, quantity, reserved_quantity, cost_price,
            location, notes, last_updated
          ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
        `)

        // إدراج البيانات واحدة تلو الأخرى
        for (const row of inventoryData) {
          insertStmt.run(row)
          addedCount++
        }
      }

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return {
        success: true,
        message: `تم إضافة ${addedCount} سجل مخزون تجريبي بنجاح`
      }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في إضافة بيانات المخزون التجريبية:', error)
      return { success: false, message: 'فشل في إضافة بيانات المخزون التجريبية' }
    }
  }

  // ===== إدارة الكمية المحجوزة =====

  // حجز كمية من صنف
  public async reserveQuantity(itemId: number, warehouseId: number, quantity: number, referenceType?: string, referenceId?: number): Promise<ApiResponse> {
    try {
      // التحقق من توفر الكمية
      const currentInventory = await this.getItemWarehouseQuantity(itemId, warehouseId)
      if (!currentInventory) {
        return { success: false, message: 'الصنف غير موجود في هذا المخزن' }
      }

      const availableQuantity = (currentInventory.quantity || 0) - (currentInventory.reserved_quantity || 0)
      if (availableQuantity < quantity) {
        return {
          success: false,
          message: `الكمية المتاحة (${availableQuantity}) أقل من الكمية المطلوب حجزها (${quantity})`
        }
      }

      // تحديث الكمية المحجوزة
      const result = this.db.prepare(`
        UPDATE inventory
        SET reserved_quantity = COALESCE(reserved_quantity, 0) + ?,
            last_updated = datetime('now')
        WHERE item_id = ? AND warehouse_id = ?
      `).run(quantity, itemId, warehouseId)

      if (result.changes === 0) {
        return { success: false, message: 'فشل في حجز الكمية' }
      }

      // تسجيل حركة الحجز
      await this.createInventoryMovement({
        item_id: itemId,
        warehouse_id: warehouseId,
        movement_type: 'reserve',
        quantity: quantity,
        reference_type: referenceType || 'manual',
        reference_id: referenceId,
        notes: `حجز كمية: ${quantity}`,
        created_by: 1
      })

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return { success: true, message: 'تم حجز الكمية بنجاح' }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في حجز الكمية:', error)
      return { success: false, message: 'حدث خطأ في حجز الكمية' }
    }
  }

  // إلغاء حجز كمية
  public async unreserveQuantity(itemId: number, warehouseId: number, quantity: number, referenceType?: string, referenceId?: number): Promise<ApiResponse> {
    try {
      // التحقق من الكمية المحجوزة
      const currentInventory = await this.getItemWarehouseQuantity(itemId, warehouseId)
      if (!currentInventory) {
        return { success: false, message: 'الصنف غير موجود في هذا المخزن' }
      }

      const reservedQuantity = currentInventory.reserved_quantity || 0
      if (reservedQuantity < quantity) {
        return {
          success: false,
          message: `الكمية المحجوزة (${reservedQuantity}) أقل من الكمية المطلوب إلغاء حجزها (${quantity})`
        }
      }

      // تحديث الكمية المحجوزة
      const result = this.db.prepare(`
        UPDATE inventory
        SET reserved_quantity = COALESCE(reserved_quantity, 0) - ?,
            last_updated = datetime('now')
        WHERE item_id = ? AND warehouse_id = ?
      `).run(quantity, itemId, warehouseId)

      if (result.changes === 0) {
        return { success: false, message: 'فشل في إلغاء حجز الكمية' }
      }

      // تسجيل حركة إلغاء الحجز
      await this.createInventoryMovement({
        item_id: itemId,
        warehouse_id: warehouseId,
        movement_type: 'unreserve',
        quantity: quantity,
        reference_type: referenceType || 'manual',
        reference_id: referenceId,
        notes: `إلغاء حجز كمية: ${quantity}`,
        created_by: 1
      })

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return { success: true, message: 'تم إلغاء حجز الكمية بنجاح' }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في إلغاء حجز الكمية:', error)
      return { success: false, message: 'حدث خطأ في إلغاء حجز الكمية' }
    }
  }

  // تحويل الكمية المحجوزة إلى مباعة (عند تأكيد البيع)
  public async confirmReservedSale(itemId: number, warehouseId: number, quantity: number, referenceType?: string, referenceId?: number): Promise<ApiResponse> {
    try {
      // إلغاء الحجز أولاً
      const unreserveResult = await this.unreserveQuantity(itemId, warehouseId, quantity, referenceType, referenceId)
      if (!unreserveResult.success) {
        return unreserveResult
      }

      // خصم الكمية من المخزون
      const result = this.db.prepare(`
        UPDATE inventory
        SET quantity = quantity - ?,
            last_updated = datetime('now')
        WHERE item_id = ? AND warehouse_id = ? AND quantity >= ?
      `).run(quantity, itemId, warehouseId, quantity)

      if (result.changes === 0) {
        // إعادة حجز الكمية في حالة الفشل
        await this.reserveQuantity(itemId, warehouseId, quantity, referenceType, referenceId)
        return { success: false, message: 'الكمية المتاحة غير كافية لتأكيد البيع' }
      }

      // تسجيل حركة البيع
      await this.createInventoryMovement({
        item_id: itemId,
        warehouse_id: warehouseId,
        movement_type: 'out',
        quantity: quantity,
        reference_type: referenceType || 'sales',
        reference_id: referenceId,
        notes: `بيع مؤكد: ${quantity}`,
        created_by: 1
      })

      // حفّ قاعدة البيانات
      DatabaseService.getInstance().saveDatabase()

      return { success: true, message: 'تم تأكيد البيع وخصم الكمية بنجاح' }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تأكيد البيع:', error)
      return { success: false, message: 'حدث خطأ في تأكيد البيع' }
    }
  }

  // ===== إدارة صور الأصناف =====
  // ملاحظة: تم نقل إدارة الصور إلى النظام الجديد UnifiedImageManager
  // يستخدم الآن جدول unified_images بدلاً من item_images

  // تم إزالة getItemImages - يستخدم الآن النظام الجديد

  // تم إزالة addItemImage - يستخدم الآن النظام الجديد

  // تم إزالة جميع دوال إدارة الصور القديمة:
  // - setItemPrimaryImage
  // - deleteItemImage
  // - getItemImageFile
  // - getMimeType

  // ===== تحليل ABC =====

  public async getABCAnalysisReport(filters?: any): Promise<any[]> {
    try {
      let query = `
        SELECT
          i.id as item_id,
          i.code as item_code,
          i.name as item_name,
          c.name as category_name,
          w.name as warehouse_name,
          COALESCE(SUM(sii.quantity * sii.unit_price), 0) as total_sales,
          COALESCE(SUM(sii.quantity), 0) as total_quantity
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        LEFT JOIN warehouses w ON i.warehouse_id = w.id
        LEFT JOIN sales_invoice_items sii ON i.id = sii.item_id
        LEFT JOIN sales_invoices si ON sii.sales_invoice_id = si.id
        WHERE i.is_active = 1
      `

      const params: any[] = []

      if (filters) {
        if (filters.warehouseId) {
          query += ` AND w.id = ?`
          params.push(filters.warehouseId)
        }
        if (filters.dateFrom) {
          query += ` AND DATE(si.invoice_date) >= ?`
          params.push(filters.dateFrom)
        }
        if (filters.dateTo) {
          query += ` AND DATE(si.invoice_date) <= ?`
          params.push(filters.dateTo)
        }
      }

      query += `
        GROUP BY i.id, i.code, i.name, c.name, w.name
        HAVING total_sales > 0
        ORDER BY total_sales DESC
      `

      const rawData = this.db.prepare(query).all(params)

      // حساب النسب المئوية والتصنيف
      const totalSales = rawData.reduce((sum: number, item: any) => sum + item.total_sales, 0)

      let cumulativePercentage = 0
      const analysisData = rawData.map((item: any, index: number) => {
        const salesPercentage = totalSales > 0 ? (item.total_sales / totalSales) * 100 : 0
        cumulativePercentage += salesPercentage

        // تحديد التصنيف ABC
        let abcCategory = 'C'
        if (cumulativePercentage <= 80) {
          abcCategory = 'A'
        } else if (cumulativePercentage <= 95) {
          abcCategory = 'B'
        }

        return {
          ...item,
          sales_percentage: salesPercentage,
          cumulative_percentage: cumulativePercentage,
          abc_category: abcCategory,
          rank: index + 1
        }
      })

      return analysisData
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تحليل ABC:', error)
      return []
    }
  }

  // تصدير الأصناف
  public async exportItems(_format: 'excel' | 'csv'): Promise<ApiResponse> {
    try {
      const items = this.db.prepare(`
        SELECT
          i.code,
          i.name,
          i.description,
          c.name as category_name,
          i.unit,
          i.cost_price,
          i.sale_price,
          i.min_quantity,
          i.max_quantity,
          i.reorder_level,
          i.barcode,
          i.is_active,
          i.created_at
        FROM items i
        LEFT JOIN categories c ON i.category_id = c.id
        WHERE i.is_active = 1
        ORDER BY i.name
      `).all()

      return { success: true, data: items }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في تصدير الأصناف:', error)
      return { success: false, message: 'حدث خطأ في تصدير الأصناف' }
    }
  }

  // استيراد الأصناف
  public async importItems(itemsData: any[]): Promise<ApiResponse> {
    try {
      let successCount = 0
      let errorCount = 0
      const errors: string[] = []

      for (const itemData of itemsData) {
        try {
          // التحقق من البيانات الأساسية
          if (!itemData.code || !itemData.name) {
            errors.push(`الصنف "${itemData.name || itemData.code || 'غير محدد'}" - الكود والاسم مطلوبان`)
            errorCount++
            continue
          }

          // التحقق من عدم تكرار الكود
          const existingItem = this.db.prepare('SELECT id FROM items WHERE code = ?').get([itemData.code])
          if (existingItem) {
            errors.push(`الصنف "${itemData.name}" - الكود "${itemData.code}" موجود مسبقاً`)
            errorCount++
            continue
          }

          // البحث عن الفئة إذا تم تحديدها
          let categoryId = null
          if (itemData.category_name) {
            const category = this.db.prepare('SELECT id FROM categories WHERE name = ? AND is_active = 1').get([itemData.category_name])
            if (category) {
              categoryId = (category as any).id
            }
          }

          // إنشاء الصنف
          const result = this.db.prepare(`
            INSERT INTO items (
              code, name, description, category_id, type, unit,
              cost_price, sale_price, min_quantity, max_quantity,
              reorder_level, barcode, is_active, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
          `).run([
            itemData.code,
            itemData.name,
            itemData.description || '',
            categoryId,
            itemData.type || 'raw_material',
            itemData.unit || 'قطعة',
            itemData.cost_price || 0,
            itemData.sale_price || 0,
            itemData.min_quantity || 0,
            itemData.max_quantity || 0,
            itemData.reorder_level || 0,
            itemData.barcode || '',
            itemData.is_active !== undefined ? (itemData.is_active ? 1 : 0) : 1
          ])

          if (result.changes > 0) {
            successCount++
          } else {
            errors.push(`فشل في إنشاء الصنف "${itemData.name}"`)
            errorCount++
          }
        } catch (error: any) {
          errors.push(`خطأ في الصنف "${itemData.name}": ${error.message}`)
          errorCount++
        }
      }

      // حفّ قاعدة البيانات
      if (successCount > 0) {
        DatabaseService.getInstance().saveDatabase()
      }

      return {
        success: successCount > 0,
        message: `تم استيراد ${successCount} صنف بنجاح${errorCount > 0 ? ` مع ${errorCount} خطأ` : ''}`,
        data: {
          successCount,
          errorCount,
          errors: errors.slice(0, 10) // أول 10 أخطاء فقط
        }
      }
    } catch (error) {
      Logger.error('InventoryService', 'خطأ في استيراد الأصناف:', error)
      return { success: false, message: 'حدث خطأ في استيراد الأصناف' }
    }
  }
}
