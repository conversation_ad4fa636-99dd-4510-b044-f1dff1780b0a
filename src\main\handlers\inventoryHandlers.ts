import { ipcMain } from 'electron'
import { InventoryService } from '../services'
import { Logger } from '../utils/logger'

let inventoryService: InventoryService

export function setInventoryService(service: InventoryService) {
  inventoryService = service
}

function registerInventoryHandlers(): void {
  // المخازن
  ipcMain.handle('get-warehouses', async () => {
    try {
      const warehouses = await inventoryService.getWarehouses()
      return { success: true, data: warehouses }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب المخازن:', error)
      return { success: false, message: 'حدث خطأ في جلب المخازن' }
    }
  })

  ipcMain.handle('create-warehouse', async (_event, warehouseData: any) => {
    try {
      return await inventoryService.createWarehouse(warehouseData)
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في إنشاء المخزن:', error)
      return { success: false, message: 'حدث خطأ في إنشاء المخزن' }
    }
  })

  // تحديث مخزن
  ipcMain.handle('update-warehouse', async (_event, warehouseId: number, warehouseData: any) => {
    try {
      return await inventoryService.updateWarehouse(warehouseId, warehouseData)
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في تحديث المخزن:', error)
      return { success: false, message: 'حدث خطأ في تحديث المخزن' }
    }
  })

  // حذف مخزن
  ipcMain.handle('delete-warehouse', async (_event, warehouseId: number) => {
    try {
      return await inventoryService.deleteWarehouse(warehouseId)
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في حذف المخزن:', error)
      return { success: false, message: 'حدث خطأ في حذف المخزن' }
    }
  })

  // الفئات
  ipcMain.handle('get-categories', async () => {
    try {
      const categories = await inventoryService.getCategories()
      return { success: true, data: categories }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب الفئات:', error)
      return { success: false, message: 'حدث خطأ في جلب الفئات' }
    }
  })

  ipcMain.handle('create-category', async (_event, categoryData: any) => {
    try {
      return await inventoryService.createCategory(categoryData)
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في إنشاء الفئة:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الفئة' }
    }
  })

  ipcMain.handle('update-category', async (_event, categoryId: number, categoryData: any) => {
    try {
      return await inventoryService.updateCategory(categoryId, categoryData)
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في تحديث الفئة:', error)
      return { success: false, message: 'حدث خطأ في تحديث الفئة' }
    }
  })

  ipcMain.handle('delete-category', async (_event, categoryId: number) => {
    try {
      return await inventoryService.deleteCategory(categoryId)
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في حذف الفئة:', error)
      return { success: false, message: 'حدث خطأ في حذف الفئة' }
    }
  })

  ipcMain.handle('rename-category', async (_event, categoryId: number, newName: string) => {
    try {
      return await inventoryService.renameCategory(categoryId, newName)
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في إعادة تسمية الفئة:', error)
      return { success: false, message: 'حدث خطأ في إعادة تسمية الفئة' }
    }
  })

  // الأصناف
  ipcMain.handle('get-items', async () => {
    try {
      const items = await inventoryService.getItems()
      return { success: true, data: items }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب الأصناف:', error)
      return { success: false, message: 'حدث خطأ في جلب الأصناف' }
    }
  })

  // جلب الأصناف مع معلومات المخزون من جميع المخازن
  ipcMain.handle('get-items-with-inventory', async () => {
    try {
      const items = await inventoryService.getItemsWithInventory()
      return { success: true, data: items }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب الأصناف مع معلومات المخزون:', error)
      return { success: false, message: 'حدث خطأ في جلب الأصناف مع معلومات المخزون' }
    }
  })

  // جلب الأصناف حسب المخزن
  ipcMain.handle('get-items-by-warehouse', async (_, warehouseId: number) => {
    try {
      // التحقق من صحة المعاملات
      if (!warehouseId || typeof warehouseId !== 'number' || warehouseId <= 0) {
        return {
          success: false,
          message: 'معرف المخزن مطلوب ويجب أن يكون رقم صحيح أكبر من صفر'
        }
      }

      const items = await inventoryService.getItemsByWarehouse(warehouseId)
      return { success: true, data: items }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب أصناف المخزن:', error)
      return { success: false, message: 'حدث خطأ في جلب أصناف المخزن' }
    }
  })

  // جلب الأصناف حسب النوع
  ipcMain.handle('get-items-by-type', async (_, type: string) => {
    try {
      const items = await inventoryService.getItemsByType(type)
      return { success: true, data: items }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب الأصناف حسب النوع:', error)
      return { success: false, message: 'حدث خطأ في جلب الأصناف حسب النوع' }
    }
  })

  // جلب أنواع الأصناف المتاحة
  ipcMain.handle('get-item-types', async () => {
    try {
      const types = await inventoryService.getItemTypes()
      return { success: true, data: types }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب أنواع الأصناف:', error)
      return { success: false, message: 'حدث خطأ في جلب أنواع الأصناف' }
    }
  })

  // جلب مخزن محدد
  ipcMain.handle('get-warehouse', async (_, warehouseId: number) => {
    try {
      // التحقق من صحة المعاملات
      if (!warehouseId || typeof warehouseId !== 'number' || warehouseId <= 0) {
        return {
          success: false,
          message: 'معرف المخزن مطلوب ويجب أن يكون رقم صحيح أكبر من صفر'
        }
      }

      const warehouse = await inventoryService.getWarehouse(warehouseId)
      return { success: true, data: warehouse }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب المخزن:', error)
      return { success: false, message: 'حدث خطأ في جلب المخزن' }
    }
  })

  ipcMain.handle('create-item', async (_event, itemData: any) => {
    try {
      return await inventoryService.createItem(itemData)
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في إنشاء الصنف:', error)
      return { success: false, message: 'حدث خطأ في إنشاء الصنف' }
    }
  })

  ipcMain.handle('update-item', async (_event, itemId: number, itemData: any) => {
    try {
      return await inventoryService.updateItem(itemId, itemData)
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في تحديث الصنف:', error)
      return { success: false, message: 'حدث خطأ في تحديث الصنف' }
    }
  })

  ipcMain.handle('delete-item', async (_event, itemId: number) => {
    try {
      return await inventoryService.deleteItem(itemId)
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في حذف الصنف:', error)
      return { success: false, message: 'حدث خطأ في حذف الصنف' }
    }
  })

  // المخزون
  ipcMain.handle('get-inventory', async () => {
    try {
      const inventory = await inventoryService.getInventory()
      return { success: true, data: inventory }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب المخزون:', error)
      return { success: false, message: 'حدث خطأ في جلب المخزون' }
    }
  })

  // حركات المخزون
  ipcMain.handle('create-inventory-movement', async (_event, movementData: any) => {
    try {
      return await inventoryService.createInventoryMovement(movementData)
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في إنشاء حركة المخزون:', error)
      return { success: false, message: 'حدث خطأ في إنشاء حركة المخزون' }
    }
  })

  ipcMain.handle('get-inventory-movements', async (_, filters?: any) => {
    try {
      const movements = await inventoryService.getInventoryMovements(filters)
      return { success: true, data: movements }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب حركات المخزون:', error)
      return { success: false, message: 'حدث خطأ في جلب حركات المخزون' }
    }
  })

  // معالج جلب مدفوعات الموردين
  ipcMain.handle('get-supplier-payments', async () => {
    try {
      // سيتم تنفيذ هذا عند إضافة نّام المدفوعات
      const payments: any[] = []
      return { success: true, data: payments }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب مدفوعات الموردين:', error)
      return { success: false, message: 'حدث خطأ في جلب مدفوعات الموردين' }
    }
  })

  // توليد أكواد
  ipcMain.handle('generate-category-code', async () => {
    try {
      Logger.info('InventoryHandlers', '🔢 بدء توليد كود فئة جديد...')
      const code = await inventoryService.generateCategoryCode()

      if (!code || code.length < 6) {
        throw new Error('فشل في توليد كود صحيح')
      }

      Logger.info('InventoryHandlers', '✅ تم توليد كود الفئة: ${code}')
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('InventoryHandlers', '❌ خطأ في توليد كود الفئة:', error)
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في توليد كود الفئة'
      return { success: false, message: errorMessage }
    }
  })

  ipcMain.handle('generate-item-code', async (_event, categoryId?: number) => {
    try {
      Logger.info('InventoryHandlers', '🔢 بدء توليد كود صنف جديد...', { categoryId })
      const code = await inventoryService.generateItemCode(categoryId)

      if (!code || code.length < 6) {
        throw new Error('فشل في توليد كود صحيح')
      }

      Logger.info('InventoryHandlers', '✅ تم توليد كود الصنف: ${code}')
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('InventoryHandlers', '❌ خطأ في توليد كود الصنف:', error)
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في توليد كود الصنف'
      return { success: false, message: errorMessage }
    }
  })

  ipcMain.handle('generate-warehouse-code', async () => {
    try {
      Logger.info('InventoryHandlers', '🔢 بدء توليد كود مخزن جديد...')
      const code = await inventoryService.generateWarehouseCode()

      if (!code || code.length < 5) {
        throw new Error('فشل في توليد كود صحيح')
      }

      Logger.info('InventoryHandlers', '✅ تم توليد كود المخزن: ${code}')
      return { success: true, data: { code } }
    } catch (error) {
      Logger.error('InventoryHandlers', '❌ خطأ في توليد كود المخزن:', error)
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في توليد كود المخزن'
      return { success: false, message: errorMessage }
    }
  })

  // توليد رقم حركة المخزون
  ipcMain.handle('generate-movement-number', async (_event, movementType: string) => {
    try {
      const movementNumber = await inventoryService.generateMovementNumber(movementType)
      return { success: true, data: movementNumber }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في توليد رقم الحركة:', error)
      return { success: false, message: 'حدث خطأ في توليد رقم الحركة' }
    }
  })

  // تقارير المخزون
  ipcMain.handle('get-inventory-report', async (_, filters?: any) => {
    try {
      const report = await inventoryService.getInventoryReport(filters)
      return { success: true, data: report }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في تقرير المخزون:', error)
      return { success: false, message: 'حدث خطأ في تقرير المخزون' }
    }
  })

  ipcMain.handle('get-inventory-movements-report', async (_, filters?: any) => {
    try {
      const report = await inventoryService.getInventoryMovementsReport(filters)
      return { success: true, data: report }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في تقرير حركات المخزون:', error)
      return { success: false, message: 'حدث خطأ في تقرير حركات المخزون' }
    }
  })

  ipcMain.handle('get-low-stock-report', async (_, filters?: any) => {
    try {
      const report = await inventoryService.getLowStockReport(filters)
      return { success: true, data: report }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في تقرير المخزون المنخفض:', error)
      return { success: false, message: 'حدث خطأ في تقرير المخزون المنخفض' }
    }
  })

  ipcMain.handle('get-item-warehouse-quantity', async (_, itemId: number, warehouseId: number) => {
    try {
      const result = await inventoryService.getItemWarehouseQuantity(itemId, warehouseId)
      return { success: true, data: result }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب كمية الصنف:', error)
      return { success: false, message: 'حدث خطأ في جلب كمية الصنف' }
    }
  })

  // التحقق من توفر الصنف في المخزن
  ipcMain.handle('check-item-availability', async (_, itemId: number, warehouseId: number, requestedQuantity: number) => {
    try {
      // التحقق من صحة المعاملات
      if (!itemId || typeof itemId !== 'number' || itemId <= 0) {
        return {
          success: false,
          available: false,
          message: 'معرف الصنف مطلوب ويجب أن يكون رقم صحيح أكبر من صفر'
        }
      }

      if (!warehouseId || typeof warehouseId !== 'number' || warehouseId <= 0) {
        return {
          success: false,
          available: false,
          message: 'معرف المخزن مطلوب ويجب أن يكون رقم صحيح أكبر من صفر'
        }
      }

      if (!requestedQuantity || typeof requestedQuantity !== 'number' || requestedQuantity <= 0) {
        return {
          success: false,
          available: false,
          message: 'الكمية المطلوبة يجب أن تكون رقم صحيح أكبر من صفر'
        }
      }

      // جلب الكمية المتوفرة في المخزن
      const quantityResult = await inventoryService.getItemWarehouseQuantity(itemId, warehouseId)

      if (!quantityResult) {
        return {
          success: true,
          available: false,
          availableQuantity: 0,
          totalQuantity: 0,
          reservedQuantity: 0,
          message: 'الصنف غير متوفر في هذا المخزن'
        }
      }

      // استخدام الكمية المتاحة (الكمية الإجمالية - الكمية المحجوزة)
      const totalQuantity = quantityResult.quantity || 0
      const reservedQuantity = quantityResult.reserved_quantity || 0
      const availableQuantity = totalQuantity - reservedQuantity
      const isAvailable = availableQuantity >= requestedQuantity

      return {
        success: true,
        available: isAvailable,
        availableQuantity: availableQuantity,
        totalQuantity: totalQuantity,
        reservedQuantity: reservedQuantity,
        requestedQuantity: requestedQuantity,
        message: isAvailable
          ? 'الكمية متوفرة'
          : `الكمية المطلوبة (${requestedQuantity}) غير متوفرة. الكمية المتاحة: ${availableQuantity} (الإجمالي: ${totalQuantity}, المحجوز: ${reservedQuantity})`
      }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في التحقق من توفر الصنف:', error)
      return {
        success: false,
        available: false,
        message: 'حدث خطأ في التحقق من توفر الصنف'
      }
    }
  })

  // جلب معلومات مخزون صنف محدد
  ipcMain.handle('get-item-inventory', async (_, itemId: number) => {
    try {
      // التحقق من صحة المعاملات
      if (!itemId || typeof itemId !== 'number' || itemId <= 0) {
        return {
          success: false,
          message: 'معرف الصنف مطلوب ويجب أن يكون رقم صحيح أكبر من صفر'
        }
      }

      // جلب معلومات المخزون للصنف من جميع المخازن
      const inventory = await inventoryService.getInventory()
      const itemInventory = inventory.filter((inv: any) => inv.item_id === itemId)

      // حساب إجمالي الكمية المتوفرة
      const totalQuantity = itemInventory.reduce((total: number, inv: any) => total + (inv.quantity || 0), 0)

      return {
        success: true,
        data: {
          item_id: itemId,
          total_quantity: totalQuantity,
          warehouses: itemInventory
        }
      }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب معلومات مخزون الصنف:', error)
      return { success: false, message: 'حدث خطأ في جلب معلومات مخزون الصنف' }
    }
  })

  ipcMain.handle('get-item-warehouse-distribution', async (_, params?: any) => {
    try {
      const result = await inventoryService.getItemWarehouseDistribution(params)
      return { success: true, data: result }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب توزيع الأصناف على المخازن:', error)
      return { success: false, message: 'حدث خطأ في جلب توزيع الأصناف على المخازن' }
    }
  })

  // التنبيهات والإحصائيات
  ipcMain.handle('get-inventory-alerts', async () => {
    try {
      const alerts = await inventoryService.getInventoryAlerts()
      return { success: true, data: alerts }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب تنبيهات المخزون:', error)
      return { success: false, message: 'حدث خطأ في جلب تنبيهات المخزون' }
    }
  })

  ipcMain.handle('get-inventory-statistics', async () => {
    try {
      const stats = await inventoryService.getInventoryStatistics()
      return { success: true, data: stats }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في جلب إحصائيات المخزون:', error)
      return { success: false, message: 'حدث خطأ في جلب إحصائيات المخزون' }
    }
  })

  // ملاحظة: تم إزالة معالجات الصور القديمة (get-item-images, upload-item-image)
  // يتم الآن استخدام النظام الجديد UnifiedImageManager مع معالجات imageHandlers.ts

  // ملاحظة: تم إزالة معالجات الصور القديمة (set-item-primary-image, delete-item-image, get-item-image)
  // يتم الآن استخدام النظام الجديد UnifiedImageManager مع معالجات imageHandlers.ts

  // تصدير واستيراد الأصناف
  ipcMain.handle('export-items', async (_, format: 'excel' | 'csv') => {
    try {
      const result = await inventoryService.exportItems(format)
      return result
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في تصدير الأصناف:', error)
      return { success: false, message: 'حدث خطأ في تصدير الأصناف' }
    }
  })

  ipcMain.handle('import-items', async (_, itemsData: any[]) => {
    try {
      const result = await inventoryService.importItems(itemsData)
      return result
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في استيراد الأصناف:', error)
      return { success: false, message: 'حدث خطأ في استيراد الأصناف' }
    }
  })

  // تحليل ABC
  ipcMain.handle('get-abc-analysis-report', async (_, filters?: any) => {
    try {
      const report = await inventoryService.getABCAnalysisReport(filters)
      return { success: true, data: report }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في تحليل ABC:', error)
      return { success: false, message: 'حدث خطأ في تحليل ABC' }
    }
  })

  // ==================== إدارة الكمية المحجوزة ====================

  // حجز كمية من صنف
  ipcMain.handle('reserve-quantity', async (_, data: {
    itemId: number
    warehouseId: number
    quantity: number
    referenceType?: string
    referenceId?: number
  }) => {
    try {
      const result = await inventoryService.reserveQuantity(
        data.itemId,
        data.warehouseId,
        data.quantity,
        data.referenceType,
        data.referenceId
      )
      return result
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في حجز الكمية:', error)
      return { success: false, message: 'حدث خطأ في حجز الكمية' }
    }
  })

  // إلغاء حجز كمية
  ipcMain.handle('unreserve-quantity', async (_, data: {
    itemId: number
    warehouseId: number
    quantity: number
    referenceType?: string
    referenceId?: number
  }) => {
    try {
      const result = await inventoryService.unreserveQuantity(
        data.itemId,
        data.warehouseId,
        data.quantity,
        data.referenceType,
        data.referenceId
      )
      return result
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في إلغاء حجز الكمية:', error)
      return { success: false, message: 'حدث خطأ في إلغاء حجز الكمية' }
    }
  })

  // تأكيد البيع المحجوز
  ipcMain.handle('confirm-reserved-sale', async (_, data: {
    itemId: number
    warehouseId: number
    quantity: number
    referenceType?: string
    referenceId?: number
  }) => {
    try {
      const result = await inventoryService.confirmReservedSale(
        data.itemId,
        data.warehouseId,
        data.quantity,
        data.referenceType,
        data.referenceId
      )
      return result
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في تأكيد البيع المحجوز:', error)
      return { success: false, message: 'حدث خطأ في تأكيد البيع المحجوز' }
    }
  })

  // تم إزالة معالجات البيانات التجريبية - يجب استخدام البيانات الحقيقية فقط

  // تحديث أسعار التكلفة في المخزون
  ipcMain.handle('update-inventory-cost-prices', async () => {
    try {
      inventoryService.updateInventoryCostPrices()
      return { success: true, message: 'تم تحديث أسعار التكلفة في المخزون بنجاح' }
    } catch (error) {
      Logger.error('InventoryHandlers', 'خطأ في تحديث أسعار التكلفة في المخزون:', error)
      return { success: false, message: 'حدث خطأ في تحديث أسعار التكلفة في المخزون' }
    }
  })
}

export { registerInventoryHandlers }
