import React, { useState, useEffect } from 'react'
import { Card, Alert, Button, Space, Typography, Tag, Divider, message } from 'antd'
import { 
  DatabaseOutlined, 
  CloudServerOutlined, 
  DesktopOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SwapOutlined
} from '@ant-design/icons'

const { Title, Text, Paragraph } = Typography

interface DatabaseStatusInfo {
  type: 'local' | 'shared'
  path: string
  message: string
}

const DatabaseStatus: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [statusInfo, setStatusInfo] = useState<DatabaseStatusInfo | null>(null)
  const [switchLoading, setSwitchLoading] = useState(false)

  // جلب حالة قاعدة البيانات
  const loadDatabaseStatus = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI.invoke('get-database-type')
      if (response.success) {
        setStatusInfo({
          type: response.type,
          path: response.path,
          message: response.message
        })
      } else {
        message.error(response.message || 'خطأ في جلب حالة قاعدة البيانات')
      }
    } catch (error) {
      message.error('خطأ في الاتصال بالخدمة')
    } finally {
      setLoading(false)
    }
  }

  // التبديل إلى قاعدة البيانات المحلية
  const switchToLocal = async () => {
    setSwitchLoading(true)
    try {
      const response = await window.electronAPI.invoke('force-switch-to-local-database')
      if (response.success) {
        message.success(response.message)
        await loadDatabaseStatus()
      } else {
        message.error(response.message || 'فشل في التبديل إلى قاعدة البيانات المحلية')
      }
    } catch (error) {
      message.error('خطأ في التبديل إلى قاعدة البيانات المحلية')
    } finally {
      setSwitchLoading(false)
    }
  }

  // التبديل إلى قاعدة البيانات المشتركة
  const switchToShared = async () => {
    // هذا يتطلب مسار قاعدة البيانات المشتركة
    // يمكن تحسينه لاحقاً لجلب المسار من إعدادات المزامنة
    message.info('لتفعيل قاعدة البيانات المشتركة، يرجى استخدام إعدادات المزامنة')
  }

  useEffect(() => {
    loadDatabaseStatus()
  }, [])

  const getDatabaseIcon = () => {
    if (!statusInfo) return <DatabaseOutlined />
    return statusInfo.type === 'local' ? <DesktopOutlined /> : <CloudServerOutlined />
  }

  const getDatabaseColor = () => {
    if (!statusInfo) return 'default'
    return statusInfo.type === 'local' ? 'blue' : 'green'
  }

  const getDatabaseText = () => {
    if (!statusInfo) return 'غير معروف'
    return statusInfo.type === 'local' ? 'قاعدة بيانات محلية' : 'قاعدة بيانات مشتركة'
  }

  return (
    <Card
      title={
        <Space>
          <DatabaseOutlined />
          حالة قاعدة البيانات
        </Space>
      }
      loading={loading}
      extra={
        <Button 
          icon={<SyncOutlined />} 
          onClick={loadDatabaseStatus}
          size="small"
        >
          تحديث
        </Button>
      }
    >
      {statusInfo && (
        <>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            {/* حالة قاعدة البيانات الحالية */}
            <Alert
              message={
                <Space>
                  {getDatabaseIcon()}
                  <Text strong>{getDatabaseText()}</Text>
                </Space>
              }
              description={statusInfo.message}
              type={statusInfo.type === 'shared' ? 'success' : 'info'}
              showIcon
              icon={statusInfo.type === 'shared' ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
            />

            {/* معلومات المسار */}
            <div>
              <Text type="secondary">مسار قاعدة البيانات:</Text>
              <br />
              <Text code copyable style={{ fontSize: '12px' }}>
                {statusInfo.path}
              </Text>
            </div>

            {/* نوع قاعدة البيانات */}
            <div>
              <Text type="secondary">النوع: </Text>
              <Tag color={getDatabaseColor()} icon={getDatabaseIcon()}>
                {getDatabaseText()}
              </Tag>
            </div>

            <Divider />

            {/* شرح الأنواع */}
            <div>
              <Title level={5}>شرح أنواع قواعد البيانات:</Title>
              
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Space>
                    <DesktopOutlined style={{ color: '#1890ff' }} />
                    <Text strong>قاعدة البيانات المحلية:</Text>
                  </Space>
                  <Paragraph style={{ marginLeft: 24, marginBottom: 8 }}>
                    تُحفظ على الجهاز الحالي فقط. كل جهاز له قاعدة بيانات منفصلة.
                    مناسبة للاستخدام الفردي أو عند عدم الحاجة للمزامنة.
                  </Paragraph>
                </div>

                <div>
                  <Space>
                    <CloudServerOutlined style={{ color: '#52c41a' }} />
                    <Text strong>قاعدة البيانات المشتركة:</Text>
                  </Space>
                  <Paragraph style={{ marginLeft: 24, marginBottom: 8 }}>
                    تُحفظ في مجلد مشترك على الشبكة. جميع الأجهزة تستخدم نفس قاعدة البيانات.
                    مناسبة للاستخدام المتعدد الأجهزة مع المزامنة الفورية.
                  </Paragraph>
                </div>
              </Space>
            </div>

            <Divider />

            {/* أزرار التحكم */}
            <Space>
              <Button
                type={statusInfo.type === 'local' ? 'default' : 'primary'}
                icon={<DesktopOutlined />}
                onClick={switchToLocal}
                loading={switchLoading}
                disabled={statusInfo.type === 'local'}
              >
                التبديل للمحلية
              </Button>

              <Button
                type={statusInfo.type === 'shared' ? 'default' : 'primary'}
                icon={<CloudServerOutlined />}
                onClick={switchToShared}
                disabled={statusInfo.type === 'shared'}
              >
                التبديل للمشتركة
              </Button>
            </Space>

            {/* تحذير مهم */}
            <Alert
              message="تحذير مهم"
              description="التبديل بين أنواع قواعد البيانات قد يؤثر على البيانات المحفوظة. تأكد من إنشاء نسخة احتياطية قبل التبديل."
              type="warning"
              showIcon
              style={{ marginTop: 16 }}
            />
          </Space>
        </>
      )}
    </Card>
  )
}

export default DatabaseStatus
