import { DatabaseService } from './DatabaseService'
import * as bcrypt from 'bcryptjs'
import { Logger } from '../utils/logger'

export interface User {
  id: number
  user_code?: string
  username: string
  full_name: string
  email?: string
  phone?: string
  role: string
  is_active: boolean
  last_login?: string | null
  login_attempts?: number
  locked_until?: string | null
  created_at: string
  updated_at?: string
  roles?: string
}

export interface LoginResponse {
  success: boolean
  message?: string
  user?: User
  token?: string
  expiresAt?: string
}

export interface ApiResponse {
  success: boolean
  message?: string
  data?: any
  user?: User
  id?: number | bigint
  details?: any
}

export class AuthService {
  private static instance: AuthService
  private databaseService: DatabaseService

  private constructor() {
    this.databaseService = DatabaseService.getInstance()
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService()
    }
    return AuthService.instance
  }

  // تسجيل الدخول
  public async login(username: string, password: string, ipAddress?: string, userAgent?: string): Promise<LoginResponse> {
    try {
      Logger.info('AuthService', '🔐 محاولة تسجيل دخول للمستخدم:', username)
      Logger.info('AuthService', '📊 معاملات الدخول:', {
        username: username || 'undefined',
        password: password ? '***' : 'undefined',
        ipAddress: ipAddress || 'غير محدد',
        userAgent: userAgent || 'غير محدد'
      })

      // التحقق من صحة المعاملات
      if (!username || !password) {
        Logger.info('AuthService', '❌ معاملات غير صحيحة')
        return { success: false, message: 'يرجى إدخال اسم المستخدم وكلمة المرور' }
      }

      // التحقق من قاعدة البيانات
      const db = this.databaseService.getDatabase()
      if (!db) {
        Logger.info('AuthService', '❌ قاعدة البيانات غير متاحة')
        return { success: false, message: 'خطأ في الاتصال بقاعدة البيانات' }
      }

      // البحث عن المستخدم
      const userQuery = db.exec(`
        SELECT * FROM users
        WHERE username = '${username}' AND is_active = 1
      `)

      let user: any = null
      if (userQuery.length > 0 && userQuery[0].values.length > 0) {
        const columns = userQuery[0].columns
        const values = userQuery[0].values[0]
        user = {}
        columns.forEach((col: string, index: number) => {
          user[col] = values[index]
        })
      }

      Logger.info('AuthService', '👤 بيانات المستخدم:', {
        found: !!user,
        id: user?.id,
        username: user?.username,
        login_attempts: user?.login_attempts,
        locked_until: user?.locked_until,
        is_active: user?.is_active,
        has_password: !!(user?.password || user?.password_hash),
        password_field: user?.password ? 'password' : (user?.password_hash ? 'password_hash' : 'none')
      })

      if (!user) {
        Logger.info('AuthService', '❌ المستخدم غير موجود أو غير نشط')
        await this.logLoginAttempt(username, false, 'مستخدم غير موجود', ipAddress, userAgent)
        return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' }
      }

      // التحقق من وجود كلمة مرور (دعم كلا من password و password_hash)
      const userPassword = user.password || user.password_hash
      if (!userPassword) {
        Logger.info('AuthService', '❌ كلمة المرور مفقودة للمستخدم')
        return { success: false, message: 'خطأ في إعداد الحساب، يرجى الاتصال بالمدير' }
      }

      // فحص الحّر (مؤقتاً معطل للاختبار)
      if (user.locked_until && new Date(user.locked_until) > new Date()) {
        Logger.info('AuthService', '🚫 المستخدم محّور حتى:', user.locked_until)
        Logger.info('AuthService', '🔧 إلغاء الحظر تلقائياً للاختبار...')

        // إلغاء الحظر تلقائياً
        db.exec(`
          UPDATE users
          SET locked_until = NULL, login_attempts = 0
          WHERE id = ${user.id}
        `)
        this.databaseService.saveDatabase()

        Logger.info('AuthService', '✅ تم إلغاء الحظر تلقائياً')
        // المتابعة مع تسجيل الدخول
      }

      Logger.info('AuthService', '🔑 التحقق من كلمة المرور...')
      // التحقق من كلمة المرور (دعم كلا من النص العادي والمشفر)
      let isPasswordValid = false

      if (user.password_hash) {
        // إذا كانت كلمة المرور مشفرة، استخدم bcrypt للمقارنة
        try {
          isPasswordValid = bcrypt.compareSync(password, user.password_hash)
          Logger.info('AuthService', '🔍 التحقق من كلمة المرور المشفرة:', isPasswordValid)
        } catch (error) {
          Logger.error('AuthService', 'خطأ في التحقق من كلمة المرور المشفرة:', error)
          isPasswordValid = false
        }
      } else if (user.password) {
        // إذا كانت كلمة المرور غير مشفرة، قارن مباشرة
        isPasswordValid = password === user.password
        Logger.info('AuthService', '🔍 التحقق من كلمة المرور العادية:', isPasswordValid)
      }

      Logger.info('AuthService', '🔍 نتيجة التحقق النهائية من كلمة المرور:', isPasswordValid)

      if (!isPasswordValid) {
        Logger.info('AuthService', '❌ كلمة المرور غير صحيحة')
        await this.handleFailedLogin(user.id, username, ipAddress, userAgent)
        return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' }
      }

      // تسجيل دخول ناجح
      await this.handleSuccessfulLogin(user.id, username, ipAddress, userAgent)

      // إنشاء رمز JWT
      const token = this.generateToken(user)
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 ساعة

      // حفّ الجلسة
      await this.saveSession(user.id, token, expiresAt)

      // إعداد بيانات المستخدم للإرجاع
      const userData: User = {
        id: user.id,
        user_code: user.user_code,
        username: user.username,
        full_name: user.full_name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        is_active: user.is_active,
        last_login: user.last_login,
        created_at: user.created_at,
        roles: user.roles
      }

      Logger.info('AuthService', '✅ تسجيل دخول ناجح للمستخدم:', user.full_name)

      return {
        success: true,
        message: `مرحباً ${user.full_name}`,
        user: userData,
        token,
        expiresAt
      }

    } catch (error) {
      Logger.error('AuthService', '❌ خطأ في تسجيل الدخول:', error)
      Logger.error('AuthService', '📊 تفاصيل الخطأ:', {
        name: (error as Error).name,
        message: (error as Error).message,
        stack: (error as Error).stack
      })
      return { success: false, message: 'حدث خطأ أثناء تسجيل الدخول' }
    }
  }

  // تسجيل الخروج
  public async logout(token: string): Promise<ApiResponse> {
    try {
      const db = this.databaseService.getDatabase()
      if (db) {
        const deleteStmt = db.prepare(`DELETE FROM user_sessions WHERE token = ?`)
        deleteStmt.run(token)
      }

      return { success: true, message: 'تم تسجيل الخروج بنجاح' }
    } catch (error) {
      Logger.error('AuthService', 'خطأ في تسجيل الخروج:', error)
      return { success: false, message: 'حدث خطأ أثناء تسجيل الخروج' }
    }
  }

  // التحقق من صحة الجلسة
  public async verifySession(token: string): Promise<ApiResponse> {
    try {
      const db = this.databaseService.getDatabase()
      if (!db) {
        return { success: false, message: 'خطأ في الاتصال بقاعدة البيانات' }
      }

      // فحص الجلسة في قاعدة البيانات
      const sessionQuery = db.exec(`
        SELECT s.*, u.* FROM user_sessions s
        JOIN users u ON s.user_id = u.id
        WHERE s.token = '${token}' AND s.expires_at > datetime('now') AND u.is_active = 1
      `)

      let session: any = null
      if (sessionQuery.length > 0 && sessionQuery[0].values.length > 0) {
        const columns = sessionQuery[0].columns
        const values = sessionQuery[0].values[0]
        session = {}
        columns.forEach((col: string, index: number) => {
          session[col] = values[index]
        })
      }

      if (!session) {
        return { success: false, message: 'جلسة غير صالحة أو منتهية الصلاحية' }
      }

      // التحقق من صحة الرمز المميز (بسيط)
      if (!token.startsWith('token_') || token.split('_').length !== 4) {
        // حذف الجلسة غير الصالحة
        const db = this.databaseService.getDatabase()
        if (db) {
          const deleteStmt = db.prepare(`DELETE FROM user_sessions WHERE token = ?`)
          deleteStmt.run(token)
        }
        return { success: false, message: 'رمز الجلسة غير صالح' }
      }

      const userData: User = {
        id: session.user_id,
        user_code: session.user_code,
        username: session.username,
        full_name: session.full_name,
        email: session.email,
        phone: session.phone,
        role: session.role,
        is_active: session.is_active,
        last_login: session.last_login,
        created_at: session.created_at,
        roles: session.roles
      }

      return { success: true, user: userData }

    } catch (error) {
      Logger.error('AuthService', 'خطأ في التحقق من الجلسة:', error)
      return { success: false, message: 'حدث خطأ في التحقق من الجلسة' }
    }
  }

  // إنشاء رمز مميز بسيط
  private generateToken(user: any): string {
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2)
    return `token_${user.id}_${timestamp}_${randomString}`
  }

  // حفّ الجلسة
  private async saveSession(userId: number, token: string, expiresAt: string): Promise<void> {
    const db = this.databaseService.getDatabase()
    if (!db) return

    // حذف الجلسات القديمة للمستخدم
    const deleteStmt = db.prepare(`DELETE FROM user_sessions WHERE user_id = ?`)
    deleteStmt.run(userId)

    // إدراج الجلسة الجديدة
    const insertStmt = db.prepare(`
      INSERT INTO user_sessions (user_id, token, expires_at, created_at)
      VALUES (?, ?, ?, datetime('now'))
    `)
    insertStmt.run(userId, token, expiresAt)

    // حفّ التغييرات
    this.databaseService.saveDatabase()
  }

  // تسجيل محاولة تسجيل الدخول
  private async logLoginAttempt(
    username: string,
    success: boolean,
    failureReason?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      const db = this.databaseService.getDatabase()
      if (db) {
        db.exec(`
          INSERT INTO login_attempts (username, ip_address, user_agent, success, failure_reason, attempt_time)
          VALUES ('${username}', '${ipAddress || ''}', '${userAgent || ''}', ${success ? 1 : 0}, '${failureReason || ''}', datetime('now'))
        `)
        // حفّ قاعدة البيانات
        this.databaseService.saveDatabase()
      }
    } catch (error) {
      Logger.error('AuthService', 'خطأ في تسجيل محاولة الدخول:', error)
    }
  }

  // معالجة تسجيل الدخول الناجح
  private async handleSuccessfulLogin(userId: number, username: string, ipAddress?: string, userAgent?: string): Promise<void> {
    // تحديث آخر تسجيل دخول وإعادة تعيين المحاولات
    const db = this.databaseService.getDatabase()
    if (db) {
      db.exec(`
        UPDATE users
        SET last_login = datetime('now')
        WHERE id = ${userId}
      `)
    }

    // تسجيل المحاولة الناجحة
    await this.logLoginAttempt(username, true, undefined, ipAddress, userAgent)

    // حفّ التغييرات
    this.databaseService.saveDatabase()
  }

  // معالجة تسجيل الدخول الفاشل
  private async handleFailedLogin(userId: number, username: string, ipAddress?: string, userAgent?: string): Promise<void> {
    Logger.info('AuthService', `⚠️ معالجة تسجيل دخول فاشل للمستخدم: ${username}`, { userId })

    const db = this.databaseService.getDatabase()
    if (!db) return

    Logger.info('AuthService', '✅ تم تسجيل المحاولة الفاشلة')

    // تسجيل المحاولة الفاشلة
    await this.logLoginAttempt(username, false, 'كلمة مرور خاطئة', ipAddress, userAgent)

    // حفّ التغييرات
    this.databaseService.saveDatabase()
  }

  // تنّيف الجلسات المنتهية الصلاحية
  public async cleanupExpiredSessions(): Promise<void> {
    try {
      const db = this.databaseService.getDatabase()
      if (db) {
        db.exec(`DELETE FROM user_sessions WHERE expires_at < datetime('now')`)
        Logger.info('AuthService', '🧹 تم تنّيف الجلسات المنتهية الصلاحية')
      }
    } catch (error) {
      Logger.error('AuthService', 'خطأ في تنّيف الجلسات:', error)
    }
  }

  // تنّيف محاولات تسجيل الدخول القديمة
  public async cleanupOldLoginAttempts(daysToKeep: number = 30): Promise<void> {
    try {
      const db = this.databaseService.getDatabase()
      if (db) {
        const cleanupStmt = db.prepare(`DELETE FROM login_attempts WHERE attempt_time < datetime('now', '-' || ? || ' days')`)
        cleanupStmt.run(daysToKeep)
        Logger.info('AuthService', '🧹 تم تنّيف محاولات تسجيل الدخول القديمة')
      }
    } catch (error) {
      Logger.error('AuthService', 'خطأ في تنّيف محاولات تسجيل الدخول:', error)
    }
  }

  // إعادة تعيين حالة المستخدم وإلغاء الحّر
  public async resetUserLoginStatus(username: string): Promise<{ success: boolean; message: string }> {
    try {
      const db = this.databaseService.getDatabase()
      if (db) {
        // إلغاء الحظر وإعادة تعيين محاولات تسجيل الدخول
        db.exec(`
          UPDATE users
          SET locked_until = NULL, login_attempts = 0
          WHERE username = '${username}'
        `)

        // حذف محاولات تسجيل الدخول الفاشلة للمستخدم
        db.exec(`
          DELETE FROM login_attempts
          WHERE username = '${username}' AND success = 0
        `)

        // حفظ التغييرات
        this.databaseService.saveDatabase()

        Logger.info('AuthService', `✅ تم إلغاء الحظر للمستخدم: ${username}`)
        Logger.info('AuthService', `✅ تم إعادة تعيين حالة تسجيل الدخول للمستخدم: ${username}`)
        return { success: true, message: 'تم إلغاء الحظر بنجاح' }
      } else {
        return { success: false, message: 'خطأ في الاتصال بقاعدة البيانات' }
      }
    } catch (error) {
      Logger.error('AuthService', 'خطأ في إعادة تعيين حالة المستخدم:', error)
      return { success: false, message: 'حدث خطأ في إعادة تعيين الحالة' }
    }
  }

  // إعادة تعيين جميع محاولات تسجيل الدخول
  public async resetAllLoginAttempts(): Promise<{ success: boolean; message: string }> {
    try {
      const db = this.databaseService.getDatabase()
      if (db) {
        // إلغاء الحظر لجميع المستخدمين
        db.exec(`
          UPDATE users
          SET locked_until = NULL, login_attempts = 0
        `)

        // حذف جميع محاولات تسجيل الدخول الفاشلة
        db.exec(`
          DELETE FROM login_attempts
          WHERE success = 0
        `)

        // حفظ التغييرات
        this.databaseService.saveDatabase()

        Logger.info('AuthService', '✅ تم إلغاء الحظر لجميع المستخدمين')
        return { success: true, message: 'تم إلغاء الحظر لجميع المستخدمين بنجاح' }
      } else {
        return { success: false, message: 'خطأ في الاتصال بقاعدة البيانات' }
      }
    } catch (error) {
      Logger.error('AuthService', 'خطأ في إعادة تعيين محاولات تسجيل الدخول:', error)
      return { success: false, message: 'حدث خطأ في إعادة تعيين محاولات تسجيل الدخول' }
    }
  }

  // الحصول على المستخدم الحالي من الرمز المميز
  public async getCurrentUser(token: string): Promise<User | null> {
    try {
      const db = this.databaseService.getDatabase()
      if (!db) return null

      const sessionQuery = db.exec(`
        SELECT u.* FROM user_sessions s
        JOIN users u ON s.user_id = u.id
        WHERE s.token = '${token}' AND s.expires_at > datetime('now') AND u.is_active = 1
      `)

      if (sessionQuery.length === 0 || sessionQuery[0].values.length === 0) return null

      const columns = sessionQuery[0].columns
      const values = sessionQuery[0].values[0]
      const session: any = {}
      columns.forEach((col: string, index: number) => {
        session[col] = values[index]
      })

      return {
        id: session.id,
        user_code: session.code,
        username: session.username,
        full_name: session.full_name,
        email: session.email,
        phone: session.phone,
        role: session.role,
        is_active: session.is_active,
        last_login: session.last_login,
        created_at: session.created_at,
        roles: session.roles
      }
    } catch (error) {
      Logger.error('AuthService', 'خطأ في الحصول على المستخدم الحالي:', error)
      return null
    }
  }

  // المستخدم الحالي
  private static currentUser: any = null

  // تعيين المستخدم الحالي
  public static setCurrentUser(user: any): void {
    AuthService.currentUser = user
  }

  // الحصول على المستخدم الحالي
  public static getCurrentUser(): any {
    return AuthService.currentUser
  }

  // الحصول على معرف المستخدم الحالي
  public static getCurrentUserId(): number {
    return AuthService.currentUser?.id || 1 // افتراضي 1 إذا لم يكن هناك مستخدم
  }

  // الحصول على دور المستخدم الحالي
  public static getCurrentUserRole(): string | null {
    return AuthService.currentUser?.role || null
  }

  // الحصول على معلومات النّام
  public async getSystemInfo(): Promise<ApiResponse> {
    try {
      const db = this.databaseService.getDatabase()
      if (!db) {
        return { success: false, message: 'خطأ في الاتصال بقاعدة البيانات' }
      }

      // معلومات قاعدة البيانات
      const dbInfo = {
        connected: true,
        version: '1.0.0',
        lastBackup: null
      }

      // معلومات النّام
      const systemInfo = {
        platform: process.platform,
        nodeVersion: process.version,
        appVersion: '1.7.0',
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage()
      }

      return {
        success: true,
        data: {
          database: dbInfo,
          system: systemInfo,
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      Logger.error('AuthService', 'خطأ في جلب معلومات النّام:', error)
      return { success: false, message: 'حدث خطأ في جلب معلومات النّام' }
    }
  }

  // الحصول على المستخدمين النشطين للقائمة المنسدلة
  public async getActiveUsers(): Promise<ApiResponse> {
    try {
      const db = this.databaseService.getDatabase()
      if (!db) {
        return { success: false, message: 'خطأ في الاتصال بقاعدة البيانات' }
      }

      const usersQuery = db.exec(`
        SELECT id, username, full_name, role, email, phone, last_login
        FROM users
        WHERE is_active = 1
        ORDER BY full_name ASC
      `)

      const users: any[] = []
      if (usersQuery.length > 0 && usersQuery[0].values.length > 0) {
        const columns = usersQuery[0].columns
        usersQuery[0].values.forEach((values: any[]) => {
          const user: any = {}
          columns.forEach((col: string, index: number) => {
            user[col] = values[index]
          })
          users.push(user)
        })
      }

      Logger.info('AuthService', `✅ تم جلب ${users.length} مستخدم نشط`)
      return {
        success: true,
        data: users
      }
    } catch (error) {
      Logger.error('AuthService', 'خطأ في جلب المستخدمين النشطين:', error)
      return { success: false, message: 'حدث خطأ في جلب المستخدمين النشطين' }
    }
  }
}
