/**
 * أدوات تصدير Excel محسنة ومحمية من الأخطاء
 *
 * هذا الملف يحتوي على دوال مساعدة لتصدير Excel بطريقة آمنة ومحسنة
 * مع دعم كامل لـ TypeScript وأفضل الممارسات
 */

import * as XLSX from 'xlsx'
import { message } from 'antd'
import { SafeLogger as Logger } from './logger'

// نوع البيانات القابلة للتصدير
export type ExportableData = Record<string, unknown>

// واجهة خيارات التصدير
export interface ExcelExportOptions {
  fileName?: string
  sheetName?: string
  includeTimestamp?: boolean
  maxRows?: number
  headers?: Record<string, string>
  excludeColumns?: string[]
  customFormatting?: boolean
  batchSize?: number // حجم الدفعة لمعالجة البيانات الكبيرة
}

// واجهة نتيجة التصدير
export interface ExcelExportResult {
  success: boolean
  message: string
  fileName?: string
  rowsExported?: number
  error?: string
}

/**
 * التحقق من صحة البيانات قبل التصدير
 * @param data - البيانات المراد تصديرها
 * @throws {Error} إذا كانت البيانات غير صالحة
 */
export const validateExportData = (data: ExportableData[]): void => {
  if (!data) {
    throw new Error('البيانات غير محددة')
  }

  if (!Array.isArray(data)) {
    throw new Error('البيانات يجب أن تكون مصفوفة')
  }

  if (data.length === 0) {
    throw new Error('لا توجد بيانات للتصدير')
  }

  // التحقق من وجود خصائص في العناصر
  if (data.length > 0 && (typeof data[0] !== 'object' || data[0] === null)) {
    throw new Error('عناصر البيانات يجب أن تكون كائنات')
  }
}

/**
 * تنّيف اسم الملف من الأحرف غير المسموحة
 * @param fileName - اسم الملف المراد تنّيفه
 * @returns اسم الملف المنّف مع امتداد .xlsx
 */
export const sanitizeFileName = (fileName: string | null | undefined): string => {
  // استخدام قيمة افتراضية إذا كان الاسم فارغاً أو غير صحيح
  const baseName = fileName && typeof fileName === 'string' ? fileName : 'تقرير'

  // إزالة الأحرف غير المسموحة والمسافات الزائدة
  let cleanName = baseName
    .replace(/[/\\:*?"<>|]/g, '_')
    .trim()
    .replace(/\s+/g, '_')

  // التأكد من أن الاسم ليس فارغاً
  if (!cleanName || cleanName === '_') {
    cleanName = `تقرير_${new Date().toISOString().split('T')[0]}`
  }

  // إضافة امتداد .xlsx إذا لم يكن موجوداً
  if (!cleanName.toLowerCase().endsWith('.xlsx')) {
    cleanName += '.xlsx'
  }

  return cleanName
}

/**
 * معالجة البيانات وتطبيق التنسيق
 * @param data - البيانات المراد معالجتها
 * @param options - خيارات المعالجة
 * @returns البيانات المعالجة
 */
export const processExportData = (
  data: ExportableData[],
  options: ExcelExportOptions = {}
): ExportableData[] => {
  // استخدام معالجة الدفعات للبيانات الكبيرة
  let processedData = processBatchData([...data], options.batchSize)

  // تطبيق حد أقصى للصفوف إذا تم تحديده
  if (options.maxRows && processedData.length > options.maxRows) {
    Logger.warn('ExcelExport', `تم تقليل البيانات من ${processedData.length} إلى ${options.maxRows} صف`)
    processedData = processedData.slice(0, options.maxRows)
  }

  // استبعاد الأعمدة المحددة
  const excludeColumns = options.excludeColumns ?? []
  if (excludeColumns.length > 0) {
    processedData = processedData.map(row => {
      const newRow = { ...row }
      excludeColumns.forEach(col => {
        delete newRow[col]
      })
      return newRow
    })
  }

  // تطبيق رؤوس مخصصة
  const headers = options.headers ?? {}
  if (Object.keys(headers).length > 0) {
    processedData = processedData.map(row => {
      const newRow: ExportableData = {}
      Object.keys(row).forEach(key => {
        const newKey = headers[key] ?? key
        newRow[newKey] = row[key]
      })
      return newRow
    })
  }
  
  return processedData
}

/**
 * تطبيق تنسيق محسن على ورقة العمل
 * @param worksheet - ورقة العمل
 * @param data - البيانات المراد تنسيقها
 */
export const applyWorksheetFormatting = (
  worksheet: XLSX.WorkSheet,
  data: ExportableData[]
): void => {
  if (!worksheet || !data || data.length === 0) return

  try {
    // تحسين عرض الأعمدة مع دعم النصوص العربية
    const firstRow = data[0]
    if (!firstRow) return

    const columnWidths = Object.keys(firstRow).map(key => {
      // حساب العرض بناءً على طول النص العربي
      const maxLength = Math.max(
        key.length,
        ...data.map(row => String(row[key] || '').length)
      )
      return { wch: Math.min(Math.max(maxLength * 1.2, 15), 50) }
    })

    worksheet['!cols'] = columnWidths

    // إضافة تنسيق للرؤوس إذا أمكن
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col })
      if (worksheet[cellAddress]) {
        worksheet[cellAddress].s = {
          font: { bold: true },
          fill: { fgColor: { rgb: 'CCCCCC' } }
        }
      }
    }
  } catch (error) {
    Logger.warn('ExcelExport', 'فشل في تطبيق التنسيق:', error)
    // لا نعرض رسالة للمستخدم هنا لأن التنسيق اختياري
    // والتصدير سيستمر حتى لو فشل التنسيق
  }
}

/**
 * دالة مساعدة لمعالجة الأخطاء وتحويلها إلى رسائل مفهومة
 * @param error - الخطأ المراد معالجته
 * @returns رسالة الخطأ
 */
const handleExportError = (error: unknown): string => {
  if (error instanceof Error) {
    // معالجة أخطاء محددة
    if (error.name === 'QuotaExceededError') {
      return 'مساحة التخزين ممتلئة. يرجى تحرير مساحة وإعادة المحاولة'
    }
    if (error.name === 'SecurityError') {
      return 'خطأ في الأمان. تأكد من صلاحيات الوصول للملفات'
    }
    if (error.message.includes('Permission denied')) {
      return 'تم رفض الإذن. تأكد من صلاحيات الكتابة في المجلد المحدد'
    }
    if (error.message.includes('File not found')) {
      return 'لم يتم العثور على الملف أو المجلد المحدد'
    }
    if (error.message.includes('Network')) {
      return 'خطأ في الشبكة. تحقق من الاتصال وأعد المحاولة'
    }
    return error.message
  }
  if (typeof error === 'string') {
    return error
  }
  if (typeof error === 'object' && error !== null) {
    try {
      const errorObj = error as Record<string, unknown>
      if (errorObj.code === 'EACCES') {
        return 'تم رفض الوصول. تحقق من صلاحيات الملف'
      }
      if (errorObj.code === 'ENOENT') {
        return 'الملف أو المجلد غير موجود'
      }
      if (errorObj.code === 'ENOSPC') {
        return 'لا توجد مساحة كافية على القرص'
      }
      return JSON.stringify(error)
    } catch {
      return 'خطأ في تحويل كائن الخطأ إلى نص'
    }
  }
  return 'خطأ غير معروف في عملية التصدير'
}

/**
 * دالة مساعدة لتحسين أداء معالجة البيانات الكبيرة
 * @param data - البيانات المراد معالجتها
 * @param batchSize - حجم الدفعة للمعالجة (افتراضي: 1000)
 * @returns البيانات المعالجة
 */
const processBatchData = <T>(data: T[], batchSize: number = 1000): T[] => {
  const actualBatchSize = batchSize || 1000

  if (data.length <= actualBatchSize) {
    return data
  }

  Logger.info('ExcelExport', `معالجة ${data.length} صف في دفعات بحجم ${actualBatchSize}`)
  return data // في المستقبل يمكن إضافة معالجة متقدمة هنا
}

/**
 * دالة مساعدة للتحقق من صحة البيانات وتحويلها
 * @param data - البيانات المراد فحصها
 * @returns البيانات المحولة أو خطأ
 * @throws {Error} إذا كانت البيانات غير صالحة للتصدير
 */
const ensureValidData = (data: unknown): ExportableData[] => {
  if (data === null || data === undefined) {
    throw new Error('البيانات غير محددة - يرجى تمرير مصفوفة من الكائنات')
  }

  if (!Array.isArray(data)) {
    throw new Error(`نوع البيانات غير صحيح: متوقع مصفوفة، تم استلام ${typeof data}`)
  }

  if (data.length === 0) {
    throw new Error('المصفوفة فارغة - لا توجد بيانات للتصدير')
  }

  // التحقق من وجود خصائص في العناصر
  const firstItem = data[0]
  if (typeof firstItem !== 'object' || firstItem === null) {
    throw new Error(`عناصر المصفوفة يجب أن تكون كائنات، العنصر الأول من نوع: ${typeof firstItem}`)
  }

  // التحقق من أن العنصر الأول يحتوي على خصائص
  if (Object.keys(firstItem).length === 0) {
    throw new Error('العنصر الأول في المصفوفة فارغ - يجب أن تحتوي الكائنات على خصائص للتصدير')
  }

  return data as ExportableData[]
}

/**
 * دالة تصدير Excel محسنة وآمنة
 * @param data - البيانات المراد تصديرها
 * @param options - خيارات التصدير
 * @returns نتيجة عملية التصدير
 */
export const exportToExcel = async (
  data: unknown,
  options: ExcelExportOptions = {}
): Promise<ExcelExportResult> => {
  let workbook: XLSX.WorkBook | null = null
  let worksheet: XLSX.WorkSheet | null = null

  try {
    // فحوصات أمان أولية
    if (typeof window === 'undefined') {
      throw new Error('التصدير غير متاح في بيئة الخادم')
    }

    // التحقق من دعم المتصفح لـ Blob
    if (typeof Blob === 'undefined') {
      throw new Error('المتصفح لا يدعم تحميل الملفات')
    }

    // التحقق من الذاكرة المتاحة (تقديري)
    if (performance && (performance as any).memory) {
      const memoryInfo = (performance as any).memory
      const usedMemory = memoryInfo.usedJSHeapSize
      const totalMemory = memoryInfo.totalJSHeapSize
      const memoryUsagePercent = (usedMemory / totalMemory) * 100

      if (memoryUsagePercent > 90) {
        Logger.warn('ExcelExport', `استخدام الذاكرة مرتفع: ${memoryUsagePercent.toFixed(1)}%`)
        message.warning('استخدام الذاكرة مرتفع. قد يكون التصدير بطيئاً')
      }
    }

    // التحقق من صحة البيانات وتحويلها
    const validData = ensureValidData(data)

    // فحص حجم البيانات
    if (validData.length > 100000) {
      Logger.warn('ExcelExport', `حجم البيانات كبير: ${validData.length} صف`)
      message.info('جاري معالجة كمية كبيرة من البيانات، قد يستغرق وقتاً أطول...')
    }

    // معالجة البيانات
    const processedData = processExportData(validData, options)

    // إنشاء workbook
    workbook = XLSX.utils.book_new()

    // تحضير البيانات مع تنظيف أفضل للنصوص العربية
    const cleanedData = processedData.map(row => {
      const cleanedRow: ExportableData = {}
      Object.keys(row).forEach(key => {
        const value = row[key]
        // تنظيف القيم وتحويلها لتنسيق آمن
        if (typeof value === 'string') {
          cleanedRow[key] = value.trim()
        } else if (typeof value === 'number') {
          cleanedRow[key] = value
        } else if (value instanceof Date) {
          cleanedRow[key] = value.toISOString().split('T')[0]
        } else {
          cleanedRow[key] = value || ''
        }
      })
      return cleanedRow
    })

    // إنشاء worksheet مع إعدادات محسنة
    worksheet = XLSX.utils.json_to_sheet(cleanedData)

    // تطبيق التنسيق
    if (options.customFormatting !== false) {
      applyWorksheetFormatting(worksheet, cleanedData)
    }

    // تعيين عرض الأعمدة تلقائياً
    if (cleanedData.length > 0) {
      const headers = Object.keys(cleanedData[0])
      worksheet['!cols'] = headers.map(header => ({
        wch: Math.max(header.length, 15) // عرض أدنى 15 حرف
      }))
    }

    // إضافة الورقة إلى workbook
    const sheetName = options.sheetName || 'Data'
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)

    // تحضير اسم الملف
    let fileName = options.fileName || 'تقرير'
    if (options.includeTimestamp !== false) {
      const timestamp = new Date().toISOString().split('T')[0]
      fileName = `${fileName}_${timestamp}`
    }
    fileName = sanitizeFileName(fileName) + '.xlsx'

    // كتابة الملف باستخدام Electron API أو fallback
    let result: ExcelExportResult

    if (window.electronAPI && window.electronAPI.saveExcelFile) {
      // استخدام Electron API لحفظ الملف مع إعدادات محسنة
      const buffer = XLSX.write(workbook, {
        type: 'buffer',
        bookType: 'xlsx',
        compression: true,
        Props: {
          Title: 'Data Export',
          Subject: 'Export from ZET.IA System',
          Author: 'ZET.IA Accounting System'
        }
      })
      const saveResult = await window.electronAPI.saveExcelFile(buffer, fileName)

      if (saveResult.success) {
        result = {
          success: true,
          message: `تم تصدير ${processedData.length} صف بنجاح`,
          fileName: saveResult.filePath || fileName,
          rowsExported: processedData.length
        }
      } else {
        result = {
          success: false,
          message: saveResult.message || 'فشل في حفظ الملف',
          error: saveResult.message
        }
      }
    } else {
      // fallback للمتصفح العادي مع إعدادات محسنة
      XLSX.writeFile(workbook, fileName, {
        compression: true,
        Props: {
          Title: 'Data Export',
          Subject: 'Export from ZET.IA System',
          Author: 'ZET.IA Accounting System'
        }
      })
      result = {
        success: true,
        message: `تم تصدير ${processedData.length} صف بنجاح`,
        fileName,
        rowsExported: processedData.length
      }
    }

    if (result.success) {
      message.success(result.message)
      Logger.info('ExcelExport', 'تم التصدير بنجاح:', { fileName, rows: processedData.length })
    } else {
      message.error(result.message)
      Logger.error('ExcelExport', 'فشل في التصدير:', result.error)
    }

    return result
    
  } catch (error) {
    const errorMessage = handleExportError(error)
    Logger.error('ExcelExport', 'خطأ في تصدير Excel:', error)

    const result: ExcelExportResult = {
      success: false,
      message: `فشل في تصدير البيانات: ${errorMessage}`,
      error: errorMessage
    }

    message.error(result.message)
    return result
    
  } finally {
    // تنّيف الذاكرة
    try {
      if (worksheet) worksheet = null
      if (workbook) workbook = null
      
      // تشغيل garbage collection إذا كان متاحاً
      if (typeof global !== 'undefined' && global.gc) {
        global.gc()
      }
    } catch (cleanupError) {
      Logger.warn('ExcelExport', 'تحذير في تنّيف الذاكرة:', cleanupError)
    }
  }
}

/**
 * دالة تصدير سريعة للاستخدام البسيط
 * @param data - البيانات المراد تصديرها
 * @param fileName - اسم الملف (اختياري)
 * @returns true إذا نجح التصدير، false إذا فشل
 */
export const quickExportToExcel = async (
  data: ExportableData[] | unknown,
  fileName?: string
): Promise<boolean> => {
  try {
    // التحقق من صحة البيانات قبل التصدير
    const validData = ensureValidData(data)
    const result = await exportToExcel(validData, { fileName })
    return result.success
  } catch (error) {
    const errorMessage = handleExportError(error)
    Logger.error('ExcelExport', 'خطأ في التصدير السريع:', error)
    message.error(`فشل في تصدير البيانات: ${errorMessage}`)
    return false
  }
}

/**
 * دالة فحص إمكانية التصدير
 * @param data - البيانات المراد فحصها
 * @returns true إذا كانت البيانات قابلة للتصدير، false إذا لم تكن كذلك
 */
export const canExport = (data: ExportableData[] | unknown): boolean => {
  try {
    ensureValidData(data)
    return true
  } catch {
    return false
  }
}

/**
 * تصدير جميع الدوال كوحدة واحدة
 * يمكن استخدامها عبر import excelUtils from './excelExportUtils'
 */
export default {
  exportToExcel,
  quickExportToExcel,
  validateExportData,
  sanitizeFileName,
  processExportData,
  applyWorksheetFormatting,
  canExport
} as const

/**
 * أمثلة على الاستخدام:
 *
 * // استخدام بسيط
 * await quickExportToExcel(data, 'تقرير_المبيعات')
 *
 * // استخدام متقدم
 * const result = await exportToExcel(data, {
 *   fileName: 'تقرير_مفصل',
 *   sheetName: 'المبيعات',
 *   headers: { id: 'الرقم', name: 'الاسم' },
 *   excludeColumns: ['password', 'internal_id'],
 *   maxRows: 1000,
 *   customFormatting: true,
 *   batchSize: 500 // حجم دفعة مخصص للبيانات الكبيرة
 * })
 *
 * // فحص إمكانية التصدير
 * if (canExport(data)) {
 *   await exportToExcel(data)
 * }
 *
 * // استخدام مع خيارات متقدمة لمعالجة البيانات الكبيرة
 * const largeDataResult = await exportToExcel(largeDataSet, {
 *   fileName: 'تقرير_كبير',
 *   batchSize: 2000,
 *   maxRows: 10000,
 *   customFormatting: false // تعطيل التنسيق لتحسين الأداء
 * })
 */
