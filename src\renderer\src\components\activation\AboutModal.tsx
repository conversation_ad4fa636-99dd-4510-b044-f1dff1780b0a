import React, { useState, useEffect } from 'react'
import { Modal, Button, Typography, Space, Divider, Card, Input, Alert, Descriptions } from 'antd'
import {
  InfoCircleOutlined,
  SafetyOutlined,
  KeyOutlined,
  PhoneOutlined,
  MailOutlined,
  UserOutlined,
  CalendarOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import { SafeLogger as Logger } from '../../utils/logger'
import styled from 'styled-components'

const { Title, Text, Paragraph } = Typography

interface AboutModalProps {
  visible: boolean
  onClose: () => void
}

interface LicenseInfo {
  isActivated: boolean
  activationCode?: string
  licenseType?: string
  activationDate?: string
  expiryDate?: string | null
  daysRemaining?: number
  formattedInfo?: string
}

const AboutContainer = styled.div`
  direction: rtl
  text-align: center
`

const LogoSection = styled.div`
  margin-bottom: 30px
  
  .app-logo {
    font-size: 36px
    font-weight: bold
    color: #1890ff
    margin-bottom: 10px
  }
  
  .app-version {
    color: #666
    font-size: 14px
  }
`

const LicenseSection = styled.div`
  margin: 20px 0
  text-align: right
`

const LicenseManagement = styled.div`
  background: #f8f9fa
  border-radius: 8px
  padding: 20px
  margin: 20px 0
  text-align: right
  
  .management-title {
    color: #ff4d4f
    font-weight: bold
    margin-bottom: 15px
    text-align: center
  }
`

const ContactSection = styled.div`
  background: #f0f8ff
  border-radius: 8px
  padding: 20px
  margin: 20px 0
  
  .contact-title {
    color: #1890ff
    font-weight: bold
    margin-bottom: 15px
    text-align: center
  }
  
  .contact-item {
    margin: 8px 0
    display: flex
    align-items: center
    justify-content: center
    gap: 8px
  }
`

const AboutModal: React.FC<AboutModalProps> = ({ visible, onClose }) => {
  const [licenseInfo, setLicenseInfo] = useState<LicenseInfo>({ isActivated: false })
  const [showLicenseManagement, setShowLicenseManagement] = useState(false)
  const [deactivationCode, setDeactivationCode] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')

  // تحميل معلومات الترخيص
  useEffect(() => {
    if (visible) {
      loadLicenseInfo()
    }
  }, [visible])

  const loadLicenseInfo = async () => {
    try {
      const info = await window.electronAPI?.getLicenseInfo()
      if (info) {
        setLicenseInfo(info)
      }
    } catch (error) {
      Logger.error('AboutModal', 'خطأ في تحميل معلومات الترخيص:', error)
    }
  }

  // معالجة إلغاء التفعيل
  const handleDeactivation = async () => {
    if (!deactivationCode.trim()) {
      setMessage('يرجى إدخال رقم إلغاء التفعيل')
      return
    }

    setLoading(true)
    setMessage('')

    try {
      const result = await window.electronAPI?.activateLicense(deactivationCode.trim())
      
      if (result?.success) {
        setMessage('تم إلغاء تفعيل البرنامج بنجاح')
        setDeactivationCode('')
        setShowLicenseManagement(false)
        await loadLicenseInfo()
        
        // إعادة تحميل التطبيق بعد 2 ثانية
        setTimeout(() => {
          window.location.reload()
        }, 2000)
      } else {
        setMessage('رقم إلغاء التفعيل غير صحيح')
      }
    } catch (error) {
      Logger.error('AboutModal', 'خطأ في إلغاء التفعيل:', error)
      setMessage('حدث خطأ أثناء إلغاء التفعيل')
    } finally {
      setLoading(false)
    }
  }

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch {
      return dateString
    }
  }

  // تنسيق نوع الترخيص
  const formatLicenseType = (type: string) => {
    const types: Record<string, string> = {
      'MONTHLY': 'ترخيص شهري',
      'LIFETIME': 'مدى الحياة',
      'BLOCK': 'محظور'
    }
    return types[type] || type
  }

  return (
    <Modal
      title={
        <Space>
          <InfoCircleOutlined />
          حول البرنامج
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={600}
      footer={[
        <Button key="close" onClick={onClose}>
          إغلاق
        </Button>
      ]}
      destroyOnHidden
    >
      <AboutContainer>
        <LogoSection>
          <div className="app-logo">ZET.IA</div>
          <div className="app-version">الإصدار 1.7.0</div>
          <Paragraph style={{ margin: '10px 0', color: '#666' }}>
            نظام إدارة شامل للمحاسبة والإنتاج
          </Paragraph>
        </LogoSection>

        <Divider />

        <LicenseSection>
          <Title level={4} style={{ color: '#1890ff' }}>
            <SafetyOutlined /> معلومات الترخيص
          </Title>

          {licenseInfo.isActivated ? (
            <Card>
              <Descriptions column={1} bordered size="small">
                <Descriptions.Item label="حالة التفعيل">
                  <Text type="success" strong>مفعل</Text>
                </Descriptions.Item>
                
                {licenseInfo.licenseType && (
                  <Descriptions.Item label="نوع الترخيص">
                    {formatLicenseType(licenseInfo.licenseType)}
                  </Descriptions.Item>
                )}
                
                {licenseInfo.activationDate && (
                  <Descriptions.Item label="تاريخ التفعيل">
                    <Space>
                      <CalendarOutlined />
                      {formatDate(licenseInfo.activationDate)}
                    </Space>
                  </Descriptions.Item>
                )}
                
                {licenseInfo.expiryDate && (
                  <Descriptions.Item label="تاريخ انتهاء الصلاحية">
                    <Space>
                      <CalendarOutlined />
                      {formatDate(licenseInfo.expiryDate)}
                    </Space>
                  </Descriptions.Item>
                )}
                
                {licenseInfo.daysRemaining !== undefined && licenseInfo.daysRemaining >= 0 && (
                  <Descriptions.Item label="الأيام المتبقية">
                    <Space>
                      <ClockCircleOutlined />
                      <Text strong style={{ color: licenseInfo.daysRemaining < 30 ? '#ff4d4f' : '#52c41a' }}>
                        {licenseInfo.daysRemaining} يوم
                      </Text>
                    </Space>
                  </Descriptions.Item>
                )}
                
                {licenseInfo.daysRemaining === -1 && (
                  <Descriptions.Item label="مدة الترخيص">
                    <Text type="success" strong>مدى الحياة</Text>
                  </Descriptions.Item>
                )}
              </Descriptions>

              {licenseInfo.isActivated && (
                <div style={{ marginTop: 20, textAlign: 'center' }}>
                  <Button
                    type="link"
                    danger
                    onClick={() => setShowLicenseManagement(!showLicenseManagement)}
                  >
                    إدارة الترخيص
                  </Button>
                </div>
              )}
            </Card>
          ) : (
            <Alert
              message="البرنامج غير مفعل"
              description="يجب تفعيل البرنامج للاستخدام"
              type="warning"
              showIcon
            />
          )}

          {showLicenseManagement && (
            <LicenseManagement>
              <div className="management-title">إدارة الترخيص</div>
              <Paragraph style={{ fontSize: 14, color: '#666', marginBottom: 15 }}>
                لإلغاء تفعيل البرنامج، أدخل رقم إلغاء التفعيل المخصص
              </Paragraph>
              
              <Space direction="vertical" style={{ width: '100%' }}>
                <Input
                  placeholder="أدخل رقم إلغاء التفعيل"
                  value={deactivationCode}
                  onChange={(e) => setDeactivationCode(e.target.value.toUpperCase())}
                  onKeyDown={(e) => {
                    // السماح بـ Ctrl+V للصق
                    if (e.ctrlKey && e.key === 'v') {
                      return
                    }
                  }}
                  onInput={(e: any) => {
                    const value = e.target.value.toUpperCase().trim()
                    setDeactivationCode(value)
                  }}
                  prefix={<KeyOutlined />}
                  style={{
                    textAlign: 'center',
                    direction: 'ltr',
                    fontSize: '16px',
                    fontWeight: 'bold',
                    letterSpacing: '2px'
                  }}
                  autoComplete="off"
                  spellCheck={false}
                  allowClear
                />
                
                <Button
                  type="primary"
                  danger
                  loading={loading}
                  onClick={handleDeactivation}
                  style={{ width: '100%' }}
                >
                  إلغاء التفعيل
                </Button>
              </Space>

              {message && (
                <Alert
                  message={message}
                  type={message.includes('بنجاح') ? 'success' : 'error'}
                  style={{ marginTop: 10 }}
                  showIcon
                />
              )}
            </LicenseManagement>
          )}
        </LicenseSection>

        <Divider />

        <ContactSection>
          <div className="contact-title">معلومات المطور</div>
          
          <Space direction="vertical" size="small">
            <div className="contact-item">
              <UserOutlined style={{ color: '#1890ff' }} />
              <Text strong>المطور: FARESNAWAF</Text>
            </div>
            
            <div className="contact-item">
              <PhoneOutlined style={{ color: '#1890ff' }} />
              <Text strong>**********</Text>
            </div>
            
            <div className="contact-item">
              <MailOutlined style={{ color: '#1890ff' }} />
              <Text strong><EMAIL></Text>
            </div>
          </Space>
        </ContactSection>

        <Divider />

        <div style={{ textAlign: 'center', color: '#999', fontSize: 12 }}>
          © 2024 ZET.IA - جميع الحقوق محفوظة
        </div>
      </AboutContainer>
    </Modal>
  )
}

export default AboutModal
