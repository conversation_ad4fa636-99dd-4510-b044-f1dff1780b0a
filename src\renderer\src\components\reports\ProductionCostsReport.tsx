/**
 * تقرير تكاليف الإنتاج المحسن
 * تقرير شامل لتكاليف الإنتاج باستخدام النظام الموحد
 */

import React from 'react'
import UniversalReport from './UniversalReport'
import { ReportType } from '../../types/reports'

const ProductionCostsReport: React.FC = () => {
  return (
    <UniversalReport
      reportType={'production_costs' as ReportType}
      title="تقرير تكاليف الإنتاج"
      description="تقرير مفصل لتكاليف الإنتاج مع تحليل المواد والعمالة والتكاليف الإضافية"
      showDateRange={true}
      showDepartmentFilter={true}
      showCustomerFilter={true}
      showStatistics={true}
      showSummary={true}
      showExportOptions={true}
      showPrintOptions={true}
      exportFileName="production_costs_report"
      defaultFilters={{
        sortBy: 'total_costs',
        sortOrder: 'desc'
      }}
    />
  )
}

export default ProductionCostsReport
