import React, { useState, useEffect } from 'react'
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Card,
  Row,
  Col,
  message,
  Divider,
  Space,
  Tag,
  Tooltip,
  Select
} from 'antd'
import { SafeLogger as Logger } from '../../utils/logger'
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  PictureOutlined
} from '@ant-design/icons'
import type { ImageSetting } from '../../types/global'

interface ImageSettingsProps {
  visible: boolean
  onClose: () => void
}

const ImageSettings: React.FC<ImageSettingsProps> = ({ visible, onClose }) => {
  const [form] = Form.useForm()
  const [, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [, setSettings] = useState<ImageSetting[]>([])

  // تحميل الإعدادات
  const loadSettings = async () => {
    setLoading(true)
    try {
      const response = await window.electronAPI?.getImageSettings()
      if (response?.success) {
        const settingsData = response.data || []
        setSettings(settingsData)
        
        // تحويل الإعدادات إلى كائن للنموذج
        const formValues: { [key: string]: string | number | boolean } = {}
        settingsData.forEach((setting: ImageSetting) => {
          let value: string | number | boolean = setting.setting_value

          // تحويل القيم حسب النوع
          if (setting.setting_key.includes('size') || setting.setting_key.includes('width') ||
              setting.setting_key.includes('height') || setting.setting_key.includes('quality')) {
            value = parseInt(setting.setting_value) || 0
          } else if (setting.setting_key.includes('enabled') || setting.setting_key.includes('auto')) {
            value = setting.setting_value === 'true'
          }

          formValues[setting.setting_key] = value
        })
        
        form.setFieldsValue(formValues)
      } else {
        message.error('فشل في تحميل إعدادات الصور')
      }
    } catch (error) {
      Logger.error('ImageSettings', 'خطأ في تحميل إعدادات الصور:', error)
      message.error('حدث خطأ أثناء تحميل إعدادات الصور')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (visible) {
      loadSettings()
    }
  }, [visible])

  // حفّ الإعدادات
  const handleSave = async () => {
    try {
      const values = await form.validateFields()
      setSaving(true)

      // تحديث كل إعداد
      for (const [key, value] of Object.entries(values)) {
        const stringValue = typeof value === 'boolean' ? value.toString() : String(value)
        
        const response = await window.electronAPI?.updateImageSetting({
          settingKey: key,
          settingValue: stringValue
        })
        
        if (!response?.success) {
          throw new Error(`فشل في تحديث إعداد ${key}`)
        }
      }

      message.success('تم حفّ إعدادات الصور بنجاح')
      loadSettings() // إعادة تحميل الإعدادات
    } catch (error) {
      Logger.error('ImageSettings', 'خطأ في حفّ إعدادات الصور:', error)
      message.error('حدث خطأ أثناء حفّ إعدادات الصور')
    } finally {
      setSaving(false)
    }
  }

  // إعادة تعيين الإعدادات
  const handleReset = () => {
    form.resetFields()
    loadSettings()
  }

  // تحويل حجم الملف إلى نص مقروء
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <SettingOutlined />
          <span>إعدادات الصور</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="reset" onClick={handleReset} icon={<ReloadOutlined />}>
          إعادة تعيين
        </Button>,
        <Button key="cancel" onClick={onClose}>
          إلغاء
        </Button>,
        <Button 
          key="save" 
          type="primary" 
          onClick={handleSave}
          loading={saving}
          icon={<SaveOutlined />}
        >
          حفّ الإعدادات
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Row gutter={[16, 16]}>
          {/* إعدادات الملفات */}
          <Col span={24}>
            <Card 
              title={
                <Space>
                  <PictureOutlined />
                  <span>إعدادات الملفات</span>
                </Space>
              }
              size="small"
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="max_file_size"
                    label={
                      <Space>
                        <span>الحد الأقصى لحجم الملف (بايت)</span>
                        <Tooltip title="الحد الأقصى المسموح لحجم الصورة الواحدة">
                          <InfoCircleOutlined />
                        </Tooltip>
                      </Space>
                    }
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={1024}
                      max={50 * 1024 * 1024}
                      formatter={(value) => formatFileSize(Number(value) || 0)}
                      parser={(value) => {
                        const match = value?.match(/[\d.]+/)
                        return match ? Number(match[0]) : 0
                      }}
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="allowed_types"
                    label={
                      <Space>
                        <span>أنواع الملفات المسموحة</span>
                        <Tooltip title="أنواع الملفات المسموح رفعها (مفصولة بفاصلة)">
                          <InfoCircleOutlined />
                        </Tooltip>
                      </Space>
                    }
                  >
                    <Select
                      mode="tags"
                      style={{ width: '100%' }}
                      placeholder="jpg,png,gif,webp"
                      options={[
                        { value: 'jpg', label: 'JPG' },
                        { value: 'jpeg', label: 'JPEG' },
                        { value: 'png', label: 'PNG' },
                        { value: 'gif', label: 'GIF' },
                        { value: 'webp', label: 'WebP' }
                      ]}
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="max_images_per_item"
                    label={
                      <Space>
                        <span>الحد الأقصى للصور لكل صنف</span>
                        <Tooltip title="العدد الأقصى من الصور المسموح لكل صنف">
                          <InfoCircleOutlined />
                        </Tooltip>
                      </Space>
                    }
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={1}
                      max={50}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* إعدادات الجودة والحجم */}
          <Col span={24}>
            <Card 
              title="إعدادات الجودة والحجم"
              size="small"
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={8}>
                  <Form.Item
                    name="image_quality"
                    label={
                      <Space>
                        <span>جودة الضغط (1-100)</span>
                        <Tooltip title="جودة ضغط الصور عند الحفّ">
                          <InfoCircleOutlined />
                        </Tooltip>
                      </Space>
                    }
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={1}
                      max={100}
                      formatter={(value) => `${value}%`}
                      parser={(value) => value?.replace('%', '') as any}
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={8}>
                  <Form.Item
                    name="max_width"
                    label="العرض الأقصى (بكسل)"
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={100}
                      max={4000}
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={8}>
                  <Form.Item
                    name="max_height"
                    label="الارتفاع الأقصى (بكسل)"
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={100}
                      max={4000}
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="thumbnail_size"
                    label={
                      <Space>
                        <span>حجم الصورة المصغرة (بكسل)</span>
                        <Tooltip title="حجم الصور المصغرة المعروضة في القوائم">
                          <InfoCircleOutlined />
                        </Tooltip>
                      </Space>
                    }
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={50}
                      max={500}
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="auto_resize"
                    label="تغيير الحجم تلقائياً"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* إعدادات العلامة المائية */}
          <Col span={24}>
            <Card 
              title="إعدادات العلامة المائية"
              size="small"
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="watermark_enabled"
                    label="تفعيل العلامة المائية"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="watermark_text"
                    label="نص العلامة المائية"
                  >
                    <Input placeholder="نّام المحاسبة" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        <Divider />

        <div style={{ textAlign: 'center', color: '#666', fontSize: 12 }}>
          <Space direction="vertical">
            <div>
              <Tag color="blue">نصيحة</Tag>
              تأكد من ضبط الإعدادات بما يتناسب مع مساحة التخزين المتاحة وسرعة الشبكة
            </div>
            <div>
              يُنصح بضبط جودة الضغط على 85% للحصول على توازن جيد بين الجودة والحجم
            </div>
          </Space>
        </div>
      </Form>
    </Modal>
  )
}

export default ImageSettings
