import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Progress,
  Tag,
  DatePicker,
  Select,
  Button,
  Space,
  Typography,
  Divider,
  Tabs,
  <PERSON>ert,
  Spin
} from 'antd'
import { SafeLogger as Logger } from '../../../utils/logger'
import {
  Bar<PERSON>hartOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  TrophyOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  SyncOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import type { Dayjs } from 'dayjs'

const { Title, Text } = Typography
const { RangePicker } = DatePicker
const { Option } = Select
const { TabPane } = Tabs

interface ProductionAdvancedReportsProps {
  onBack?: () => void
}

interface EfficiencyData {
  order_id: number
  order_number: string
  product_name: string
  estimated_hours: number
  actual_hours: number
  estimated_cost: number
  actual_cost: number
  efficiency_percentage: number
  cost_variance: number
  time_variance: number
  status: string
  completion_date: string
}

interface ReportFilters {
  dateRange: [Dayjs, Dayjs] | null
  department_id?: number
  status?: string
}

const ProductionAdvancedReports: React.FC<ProductionAdvancedReportsProps> = ({ onBack }) => {
  const [loading, setLoading] = useState(false)
  const [efficiencyData, setEfficiencyData] = useState<EfficiencyData[]>([])
  const [departments, setDepartments] = useState<any[]>([])
  const [filters, setFilters] = useState<ReportFilters>({
    dateRange: [dayjs().subtract(30, 'day'), dayjs()]
  })

  useEffect(() => {
    loadDepartments()
    loadEfficiencyReport()
  }, [])

  const loadDepartments = async () => {
    try {
      const result = await window.electronAPI.getProductionDepartments()
      if (result.success) {
        setDepartments(result.data)
      }
    } catch (error) {
      Logger.error('ProductionAdvancedReports', 'خطأ في تحميل الأقسام:', error)
    }
  }

  const loadEfficiencyReport = async () => {
    setLoading(true)
    try {
      if (!window.electronAPI) {
        Logger.error('ProductionAdvancedReports', '❌ window.electronAPI غير متوفر')
        return
      }

      const params = {
        startDate: filters.dateRange?.[0]?.format('YYYY-MM-DD'),
        endDate: filters.dateRange?.[1]?.format('YYYY-MM-DD'),
        department_id: filters.department_id,
        status: filters.status
      }

      const result = await window.electronAPI.getProductionEfficiencyReport(params)
      if (result.success) {
        setEfficiencyData(result.data || [])
      }
    } catch (error) {
      Logger.error('ProductionAdvancedReports', 'خطأ في تحميل تقرير الكفاءة:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFilterChange = () => {
    loadEfficiencyReport()
  }

  // حساب الإحصائيات العامة
  const calculateStatistics = () => {
    if (efficiencyData.length === 0) {
      return {
        totalOrders: 0,
        avgEfficiency: 0,
        onTimeOrders: 0,
        delayedOrders: 0,
        totalCostVariance: 0,
        totalTimeVariance: 0
      }
    }

    const totalOrders = efficiencyData.length
    const avgEfficiency = efficiencyData.reduce((sum, item) => sum + item.efficiency_percentage, 0) / totalOrders
    const onTimeOrders = efficiencyData.filter(item => item.time_variance <= 0).length
    const delayedOrders = efficiencyData.filter(item => item.time_variance > 0).length
    const totalCostVariance = efficiencyData.reduce((sum, item) => sum + item.cost_variance, 0)
    const totalTimeVariance = efficiencyData.reduce((sum, item) => sum + item.time_variance, 0)

    return {
      totalOrders,
      avgEfficiency,
      onTimeOrders,
      delayedOrders,
      totalCostVariance,
      totalTimeVariance
    }
  }

  const stats = calculateStatistics()

  // أعمدة جدول الكفاءة
  const efficiencyColumns = [
    {
      title: 'رقم الأمر',
      dataIndex: 'order_number',
      key: 'order_number',
      width: 120,
      fixed: 'left' as const
    },
    {
      title: 'المنتج',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 150
    },
    {
      title: 'الساعات المقدرة',
      dataIndex: 'estimated_hours',
      key: 'estimated_hours',
      width: 120,
      align: 'center' as const,
      render: (hours: number) => `${hours} ساعة`
    },
    {
      title: 'الساعات الفعلية',
      dataIndex: 'actual_hours',
      key: 'actual_hours',
      width: 120,
      align: 'center' as const,
      render: (hours: number) => `${hours} ساعة`
    },
    {
      title: 'انحراف الوقت',
      dataIndex: 'time_variance',
      key: 'time_variance',
      width: 120,
      align: 'center' as const,
      render: (variance: number) => (
        <Tag color={variance <= 0 ? 'green' : 'red'}>
          {variance > 0 ? '+' : ''}{variance} ساعة
        </Tag>
      )
    },
    {
      title: 'التكلفة المقدرة',
      dataIndex: 'estimated_cost',
      key: 'estimated_cost',
      width: 130,
      align: 'center' as const,
      render: (cost: number) => `₪${cost.toFixed(2)}`
    },
    {
      title: 'التكلفة الفعلية',
      dataIndex: 'actual_cost',
      key: 'actual_cost',
      width: 130,
      align: 'center' as const,
      render: (cost: number) => `₪${cost.toFixed(2)}`
    },
    {
      title: 'انحراف التكلفة',
      dataIndex: 'cost_variance',
      key: 'cost_variance',
      width: 130,
      align: 'center' as const,
      render: (variance: number) => (
        <Tag color={variance <= 0 ? 'green' : 'red'}>
          {variance > 0 ? '+' : ''}₪{variance.toFixed(2)}
        </Tag>
      )
    },
    {
      title: 'كفاءة الإنتاج',
      dataIndex: 'efficiency_percentage',
      key: 'efficiency_percentage',
      width: 150,
      align: 'center' as const,
      render: (efficiency: number) => (
        <Progress
          percent={efficiency}
          size="small"
          status={efficiency >= 80 ? 'success' : efficiency >= 60 ? 'normal' : 'exception'}
          format={(percent) => `${percent?.toFixed(1)}%`}
        />
      )
    },
    {
      title: 'الحالة',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center' as const,
      render: (status: string) => {
        const statusConfig = {
          completed: { color: 'green', text: 'مكتمل' },
          in_progress: { color: 'blue', text: 'قيد التنفيذ' },
          pending: { color: 'orange', text: 'في الانتّار' },
          cancelled: { color: 'red', text: 'ملغي' }
        }
        const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Title level={2}>
            <BarChartOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
            التقارير المتقدمة لأوامر الإنتاج
          </Title>
        </Col>
        {onBack && (
          <Col>
            <Button onClick={onBack}>العودة</Button>
          </Col>
        )}
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Text strong>فترة التقرير:</Text>
            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
              style={{ width: '100%', marginTop: '8px' }}
              placeholder={['تاريخ البداية', 'تاريخ النهاية']}
            />
          </Col>
          <Col span={6}>
            <Text strong>القسم:</Text>
            <Select
              value={filters.department_id}
              onChange={(value) => setFilters({ ...filters, department_id: value })}
              style={{ width: '100%', marginTop: '8px' }}
              placeholder="جميع الأقسام"
              allowClear
            >
              {departments.map(dept => (
                <Option key={dept.id} value={dept.id}>{dept.name}</Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <Text strong>الحالة:</Text>
            <Select
              value={filters.status}
              onChange={(value) => setFilters({ ...filters, status: value })}
              style={{ width: '100%', marginTop: '8px' }}
              placeholder="جميع الحالات"
              allowClear
            >
              <Option value="completed">مكتمل</Option>
              <Option value="in_progress">قيد التنفيذ</Option>
              <Option value="pending">في الانتّار</Option>
              <Option value="cancelled">ملغي</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Button 
              type="primary" 
              onClick={handleFilterChange}
              style={{ marginTop: '24px', width: '100%' }}
              loading={loading}
            >
              تحديث التقرير
            </Button>
          </Col>
        </Row>
      </Card>

      <Spin spinning={loading}>
        <Tabs defaultActiveKey="efficiency">
          <TabPane tab="تقرير الكفاءة" key="efficiency">
            {/* Statistics Cards */}
            <Row gutter={16} style={{ marginBottom: '24px' }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="إجمالي الأوامر"
                    value={stats.totalOrders}
                    prefix={<BarChartOutlined />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="متوسط الكفاءة"
                    value={stats.avgEfficiency}
                    precision={1}
                    suffix="%"
                    prefix={<TrophyOutlined />}
                    valueStyle={{ color: stats.avgEfficiency >= 80 ? '#3f8600' : '#cf1322' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="أوامر في الوقت المحدد"
                    value={stats.onTimeOrders}
                    prefix={<CheckCircleOutlined />}
                    valueStyle={{ color: '#3f8600' }}
                    suffix={`/ ${stats.totalOrders}`}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="أوامر متأخرة"
                    value={stats.delayedOrders}
                    prefix={<WarningOutlined />}
                    valueStyle={{ color: '#cf1322' }}
                    suffix={`/ ${stats.totalOrders}`}
                  />
                </Card>
              </Col>
            </Row>

            {/* Cost and Time Variance */}
            <Row gutter={16} style={{ marginBottom: '24px' }}>
              <Col span={12}>
                <Card>
                  <Statistic
                    title="إجمالي انحراف التكلفة"
                    value={Math.abs(stats.totalCostVariance)}
                    precision={2}
                    prefix={stats.totalCostVariance >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                    suffix="₪"
                    valueStyle={{ color: stats.totalCostVariance >= 0 ? '#cf1322' : '#3f8600' }}
                  />
                  <Text type="secondary">
                    {stats.totalCostVariance >= 0 ? 'زيادة في التكلفة' : 'توفير في التكلفة'}
                  </Text>
                </Card>
              </Col>
              <Col span={12}>
                <Card>
                  <Statistic
                    title="إجمالي انحراف الوقت"
                    value={Math.abs(stats.totalTimeVariance)}
                    precision={1}
                    prefix={stats.totalTimeVariance >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                    suffix="ساعة"
                    valueStyle={{ color: stats.totalTimeVariance >= 0 ? '#cf1322' : '#3f8600' }}
                  />
                  <Text type="secondary">
                    {stats.totalTimeVariance >= 0 ? 'تأخير في الوقت' : 'توفير في الوقت'}
                  </Text>
                </Card>
              </Col>
            </Row>

            {/* Efficiency Table */}
            <Card title="تفاصيل كفاءة أوامر الإنتاج">
              <Table
                columns={efficiencyColumns}
                dataSource={efficiencyData}
                rowKey="order_id"
                scroll={{ x: 1200 }}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => 
                    `${range[0]}-${range[1]} من ${total} أمر إنتاج`
                }}
              />
            </Card>
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  )
}

export default ProductionAdvancedReports
