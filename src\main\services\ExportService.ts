import * as XLSX from 'xlsx'
import * as fs from 'fs'
// import * as path from 'path' // غير مستخدم حالياً
import { dialog } from 'electron'
import { Logger } from '../utils/logger'

export interface ExportOptions {
  format: 'excel' | 'csv' | 'json'
  fileName?: string
  sheetName?: string
  includeHeaders?: boolean
  dateFormat?: string
  numberFormat?: string
  customHeaders?: { [key: string]: string }
}

export interface ExportData {
  title: string
  subtitle?: string
  data: any[]
  metadata?: {
    generatedAt: string
    generatedBy: string
    companyInfo?: any
    filters?: any
  }
}

export class ExportService {
  private static instance: ExportService

  private constructor() {}

  public static getInstance(): ExportService {
    if (!ExportService.instance) {
      ExportService.instance = new ExportService()
    }
    return ExportService.instance
  }

  // تصدير إلى Excel
  public async exportToExcel(
    exportData: ExportData,
    options: ExportOptions = { format: 'excel' }
  ): Promise<{ success: boolean; message: string; filePath?: string }> {
    try {
      // إنشاء workbook جديد
      const workbook = XLSX.utils.book_new()
      
      // إعداد اسم الورقة
      const sheetName = options.sheetName || 'التقرير'
      
      // تحضير البيانات
      const worksheetData = this.prepareDataForExcel(exportData, options)
      
      // إنشاء worksheet
      const worksheet = XLSX.utils.aoa_to_sheet(worksheetData)
      
      // تطبيق التنسيق
      this.applyExcelFormatting(worksheet, exportData)
      
      // إضافة الورقة إلى الـ workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)
      
      // تحديد مسار الحفّ
      const fileName = options.fileName || `${exportData.title}_${new Date().toISOString().split('T')[0]}.xlsx`
      const result: any = await dialog.showSaveDialog({
        title: 'حفّ التقرير',
        defaultPath: fileName,
        filters: [
          { name: 'Excel Files', extensions: ['xlsx'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      })

      if (result.canceled || !result.filePath) {
        return {
          success: false,
          message: 'تم إلغاء عملية الحفّ'
        }
      }

      // كتابة الملف
      XLSX.writeFile(workbook, result.filePath)

      return {
        success: true,
        message: 'تم تصدير التقرير إلى Excel بنجاح',
        filePath: result.filePath
      }
    } catch (error) {
      Logger.error('ExportService', 'خطأ في تصدير Excel:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء تصدير التقرير إلى Excel'
      }
    }
  }

  // تصدير إلى CSV
  public async exportToCSV(
    exportData: ExportData,
    options: ExportOptions = { format: 'csv' }
  ): Promise<{ success: boolean; message: string; filePath?: string }> {
    try {
      // تحضير البيانات
      const csvData = this.prepareDataForCSV(exportData, options)
      
      // تحديد مسار الحفّ
      const fileName = options.fileName || `${exportData.title}_${new Date().toISOString().split('T')[0]}.csv`
      const result: any = await dialog.showSaveDialog({
        title: 'حفّ التقرير',
        defaultPath: fileName,
        filters: [
          { name: 'CSV Files', extensions: ['csv'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      })

      if (result.canceled || !result.filePath) {
        return {
          success: false,
          message: 'تم إلغاء عملية الحفّ'
        }
      }

      // كتابة الملف
      fs.writeFileSync(result.filePath, csvData, 'utf8')

      return {
        success: true,
        message: 'تم تصدير التقرير إلى CSV بنجاح',
        filePath: result.filePath
      }
    } catch (error) {
      Logger.error('ExportService', 'خطأ في تصدير CSV:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء تصدير التقرير إلى CSV'
      }
    }
  }

  // تصدير إلى JSON
  public async exportToJSON(
    exportData: ExportData,
    options: ExportOptions = { format: 'json' }
  ): Promise<{ success: boolean; message: string; filePath?: string }> {
    try {
      // تحضير البيانات
      const jsonData = {
        title: exportData.title,
        subtitle: exportData.subtitle,
        metadata: exportData.metadata,
        data: exportData.data,
        exportedAt: new Date().toISOString(),
        format: 'json'
      }
      
      // تحديد مسار الحفّ
      const fileName = options.fileName || `${exportData.title}_${new Date().toISOString().split('T')[0]}.json`
      const result: any = await dialog.showSaveDialog({
        title: 'حفّ التقرير',
        defaultPath: fileName,
        filters: [
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      })

      if (result.canceled || !result.filePath) {
        return {
          success: false,
          message: 'تم إلغاء عملية الحفّ'
        }
      }

      // كتابة الملف
      fs.writeFileSync(result.filePath, JSON.stringify(jsonData, null, 2), 'utf8')

      return {
        success: true,
        message: 'تم تصدير التقرير إلى JSON بنجاح',
        filePath: result.filePath
      }
    } catch (error) {
      Logger.error('ExportService', 'خطأ في تصدير JSON:', error)
      return {
        success: false,
        message: 'حدث خطأ أثناء تصدير التقرير إلى JSON'
      }
    }
  }

  // تحضير البيانات للـ Excel
  private prepareDataForExcel(exportData: ExportData, options: ExportOptions): any[][] {
    const result: any[][] = []
    
    // إضافة معلومات الشركة والتقرير
    if (exportData.metadata?.companyInfo) {
      const company = exportData.metadata.companyInfo
      result.push([company.name])
      result.push([company.nameEn])
      result.push([company.address])
      result.push([`${company.phone} | ${company.email}`])
      result.push([]) // سطر فارغ
    }
    
    // إضافة عنوان التقرير
    result.push([exportData.title])
    if (exportData.subtitle) {
      result.push([exportData.subtitle])
    }
    result.push([`تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}`])
    result.push([]) // سطر فارغ
    
    // إضافة البيانات
    if (exportData.data && exportData.data.length > 0) {
      // إضافة العناوين
      const headers = Object.keys(exportData.data[0])
      const translatedHeaders = headers.map(header =>
        options.customHeaders?.[header] || this.translateHeader(header)
      )
      result.push(translatedHeaders)

      // إضافة البيانات
      exportData.data.forEach(row => {
        const rowData = headers.map(header => {
          const value = row[header]
          return this.formatCellValue(value, options)
        })
        result.push(rowData)
      })
    } else {
      // إضافة رسالة عدم وجود بيانات
      result.push(['لا توجد بيانات للعرض'])
    }
    
    return result
  }

  // تحضير البيانات للـ CSV
  private prepareDataForCSV(exportData: ExportData, options: ExportOptions): string {
    const lines: string[] = []
    
    // إضافة معلومات الشركة والتقرير
    if (exportData.metadata?.companyInfo) {
      const company = exportData.metadata.companyInfo
      lines.push(`"${company.name}"`)
      lines.push(`"${company.nameEn}"`)
      lines.push(`"${company.address}"`)
      lines.push(`"${company.phone} | ${company.email}"`)
      lines.push('') // سطر فارغ
    }
    
    // إضافة عنوان التقرير
    lines.push(`"${exportData.title}"`)
    if (exportData.subtitle) {
      lines.push(`"${exportData.subtitle}"`)
    }
    lines.push(`"تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}"`)
    lines.push('') // سطر فارغ
    
    // إضافة البيانات
    if (exportData.data && exportData.data.length > 0) {
      // إضافة العناوين
      const headers = Object.keys(exportData.data[0])
      const translatedHeaders = headers.map(header => 
        `"${options.customHeaders?.[header] || this.translateHeader(header)}"`
      )
      lines.push(translatedHeaders.join(','))
      
      // إضافة البيانات
      exportData.data.forEach(row => {
        const rowData = headers.map(header => {
          const value = this.formatCellValue(row[header], options)
          return `"${value}"`
        })
        lines.push(rowData.join(','))
      })
    }
    
    return lines.join('\n')
  }

  // تطبيق التنسيق على Excel
  private applyExcelFormatting(worksheet: XLSX.WorkSheet, _exportData: ExportData): void {
    // تطبيق عرض الأعمدة
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
    const colWidths: any[] = []
    
    for (let col = range.s.c; col <= range.e.c; col++) {
      colWidths.push({ wch: 15 }) // عرض افتراضي
    }
    
    worksheet['!cols'] = colWidths
  }

  // ترجمة العناوين
  private translateHeader(header: string): string {
    const translations: { [key: string]: string } = {
      'id': 'المعرف',
      'name': 'الاسم',
      'amount': 'المبلغ',
      'date': 'التاريخ',
      'status': 'الحالة',
      'description': 'الوصف',
      'account_name': 'اسم الحساب',
      'account_number': 'رقم الحساب',
      'balance': 'الرصيد',
      'check_number': 'رقم الشيك',
      'payee_name': 'اسم المستفيد',
      'drawer_name': 'اسم الساحب',
      'note_number': 'رقم السند',
      'voucher_number': 'رقم القسيمة',
      'created_at': 'تاريخ الإنشاء',
      'updated_at': 'تاريخ التحديث'
    }
    
    return translations[header] || header
  }

  // تنسيق قيم الخلايا
  private formatCellValue(value: any, _options: ExportOptions): string {
    if (value === null || value === undefined) {
      return ''
    }
    
    if (typeof value === 'number') {
      return value.toLocaleString('ar-EG')
    }
    
    if (value instanceof Date) {
      return value.toLocaleDateString('ar-EG')
    }
    
    return String(value)
  }
}
