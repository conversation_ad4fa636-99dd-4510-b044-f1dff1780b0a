import React, { useState, useEffect } from 'react'
import { Select, Row, Col, Space, Typography } from 'antd'

import EnhancedSelect from './EnhancedSelect'
import { SafeLogger as Logger } from '../../utils/logger'

const { Option } = Select
const { Text } = Typography

interface CascadingSelectProps {
  // خصائص القائمة الأولى (المخزن)
  primaryLabel?: string
  primaryPlaceholder?: string
  primaryData?: any[]
  primaryLoadData?: () => Promise<any[]>
  primaryValue?: any
  onPrimaryChange?: (value: any) => void
  primaryLabelKey?: string
  primaryValueKey?: string
  
  // خصائص القائمة الثانية (الأصناف)
  secondaryLabel?: string
  secondaryPlaceholder?: string
  secondaryDisabledPlaceholder?: string
  secondaryLoadData?: (primaryValue: any) => Promise<any[]>
  secondaryValue?: any
  onSecondaryChange?: (value: any, item?: any) => void
  secondaryLabelKey?: string
  secondaryValueKey?: string
  secondaryDisplayFormat?: (item: any) => React.ReactNode
  
  // خصائص التخطيط
  layout?: 'horizontal' | 'vertical'
  primarySpan?: number
  secondarySpan?: number
  gutter?: number
  
  // خصائص إضافية
  disabled?: boolean
  size?: 'small' | 'middle' | 'large'
  showSearch?: boolean
}

/**
 * مكون للقوائم المنسدلة المترابطة (مثل المخزن والأصناف)
 */
const CascadingSelect: React.FC<CascadingSelectProps> = ({
  // Primary (المخزن)
  primaryLabel = 'المخزن',
  primaryPlaceholder = 'اختر المخزن',
  primaryData = [],
  primaryLoadData,
  primaryValue,
  onPrimaryChange,
  primaryLabelKey = 'name',
  primaryValueKey = 'id',
  
  // Secondary (الأصناف)
  secondaryLabel = 'الصنف',
  secondaryPlaceholder = 'اختر الصنف',
  secondaryDisabledPlaceholder = 'اختر المخزن أولاً',
  secondaryLoadData,
  secondaryValue,
  onSecondaryChange,
  secondaryLabelKey = 'name',
  secondaryValueKey = 'id',
  secondaryDisplayFormat,
  
  // Layout
  layout = 'horizontal',
  primarySpan = 12,
  secondarySpan = 12,
  gutter = 16,
  
  // Other props
  disabled = false,
  size = 'middle',
  showSearch = true
}) => {
  const [secondaryData, setSecondaryData] = useState<any[]>([])
  const [secondaryLoading, setSecondaryLoading] = useState(false)

  // تحميل البيانات الثانوية عند تغيير القيمة الأولى
  useEffect(() => {
    if (primaryValue && secondaryLoadData) {
      loadSecondaryData(primaryValue)
    } else {
      setSecondaryData([])
    }
  }, [primaryValue, secondaryLoadData])

  // تحميل البيانات الثانوية
  const loadSecondaryData = async (primaryVal: any) => {
    if (!secondaryLoadData) return

    setSecondaryLoading(true)
    try {
      const result = await secondaryLoadData(primaryVal)
      setSecondaryData(Array.isArray(result) ? result : [])
      Logger.info('CascadingSelect', `✅ تم تحميل ${result?.length || 0} عنصر ثانوي للقيمة ${primaryVal}`)
    } catch (error) {
      Logger.error('CascadingSelect', '❌ خطأ في تحميل البيانات الثانوية:', error)
      setSecondaryData([])
    } finally {
      setSecondaryLoading(false)
    }
  }

  // معالجة تغيير القيمة الأولى
  const handlePrimaryChange = (value: any) => {
    onPrimaryChange?.(value)
    // مسح القيمة الثانوية عند تغيير الأولى
    onSecondaryChange?.(undefined)
  }

  // معالجة تغيير القيمة الثانوية
  const handleSecondaryChange = (value: any) => {
    const selectedItem = secondaryData.find(item => item[secondaryValueKey] === value)
    onSecondaryChange?.(value, selectedItem)
  }

  // تنسيق عرض العنصر الثانوي
  const formatSecondaryItem = (item: any) => {
    if (secondaryDisplayFormat) {
      return secondaryDisplayFormat(item)
    }
    return item[secondaryLabelKey] || item.toString()
  }

  const primarySelect = (
    <div>
      {primaryLabel && <Text strong style={{ marginBottom: 8, display: 'block' }}>{primaryLabel}</Text>}
      <EnhancedSelect
        placeholder={primaryPlaceholder}
        dataSource={primaryData}
        loadData={primaryLoadData}
        value={primaryValue}
        onChange={handlePrimaryChange}
        labelKey={primaryLabelKey}
        valueKey={primaryValueKey}
        disabled={disabled}
        size={size}
        showSearch={showSearch}
        style={{ width: '100%' }}
      />
    </div>
  )

  const secondarySelect = (
    <div>
      {secondaryLabel && <Text strong style={{ marginBottom: 8, display: 'block' }}>{secondaryLabel}</Text>}
      <Select
        placeholder={primaryValue ? secondaryPlaceholder : secondaryDisabledPlaceholder}
        value={secondaryValue}
        onChange={handleSecondaryChange}
        disabled={disabled || !primaryValue}
        loading={secondaryLoading}
        size={size as 'small' | 'middle' | 'large'}
        showSearch={showSearch}
        optionFilterProp="children"
        style={{ width: '100%' }}
        notFoundContent={secondaryLoading ? 'جاري التحميل...' : 'لا توجد أصناف متاحة'}
      >
        {secondaryData.map((item) => (
          <Option key={item[secondaryValueKey]} value={item[secondaryValueKey]}>
            {formatSecondaryItem(item)}
          </Option>
        ))}
      </Select>
    </div>
  )

  if (layout === 'vertical') {
    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        {primarySelect}
        {secondarySelect}
      </Space>
    )
  }

  return (
    <Row gutter={gutter}>
      <Col span={primarySpan}>
        {primarySelect}
      </Col>
      <Col span={secondarySpan}>
        {secondarySelect}
      </Col>
    </Row>
  )
}

export default CascadingSelect
