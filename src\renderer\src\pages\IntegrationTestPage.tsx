/**
 * صفحة اختبار التكامل الشامل
 * صفحة مخصصة لعرض وتشغيل اختبارات التكامل الشامل للنظام
 */

import React from 'react'
import { Layout, Typography, Breadcrumb } from 'antd'
import { HomeOutlined, ThunderboltOutlined } from '@ant-design/icons'
import IntegrationTestComponent from '../components/Settings/IntegrationTestComponent'

const { Content } = Layout
const { Title } = Typography

const IntegrationTestPage: React.FC = () => {
  return (
    <Layout style={{ minHeight: '100vh', backgroundColor: '#f0f2f5' }}>
      <Content style={{ padding: '24px' }}>
        {/* شريط التنقل */}
        <Breadcrumb style={{ marginBottom: '24px' }}>
          <Breadcrumb.Item>
            <HomeOutlined />
            <span>الرئيسية</span>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <ThunderboltOutlined />
            <span>اختبار التكامل الشامل</span>
          </Breadcrumb.Item>
        </Breadcrumb>

        {/* العنوان الرئيسي */}
        <div style={{ marginBottom: '24px', textAlign: 'center' }}>
          <Title level={1} style={{ marginBottom: '8px' }}>
            🚀 اختبار التكامل الشامل
          </Title>
          <Typography.Paragraph style={{ fontSize: '16px', color: '#666' }}>
            اختبار شامل لجميع مكونات النظام المتكامل لتعديل القوالب والأعمدة
          </Typography.Paragraph>
        </div>

        {/* مكون اختبار التكامل */}
        <IntegrationTestComponent />
      </Content>
    </Layout>
  )
}

export default IntegrationTestPage
